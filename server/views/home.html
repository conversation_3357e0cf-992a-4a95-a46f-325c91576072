<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-Control" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <link href="https://files.zkh360.com/assets/favicon/boss.png" type="image/png" rel="shortcut icon">
  <title>BOSS-UNION</title>
  <link rel="stylesheet" href="https://files.zkh360.com/assets/vxe-table.min.css">
  {% if not isHotDev and manifest['vendor.css']%}
    <link rel="stylesheet" href="{{ entrance('vendor.css') }}" />
  {% endif %}
  {% if manifest['app.css'] %}
    <link rel="stylesheet" href="{{ entrance('app.css') }}"/>
  {% endif %}
  {% if manifest['common.css'] %}
    <link rel="stylesheet" href="{{ entrance('common.css') }}"/>
  {% endif %}
  <script>
    this.CUR_DATA = {
      user: {
        name: '{{ user.name }}'
      },
      env: '{{ env }}'
    }
    this.RES_PUBLIC_PATH = '{{ resUrl }}'
  </script>
  <!-- sensor data tracking  -->
  <script charset="UTF-8" src="https://files.zkh360.com/assets/sensor/sa-sdk-javascript-1.26.3/sensorsdata.min.js"></script>
  <script charset="UTF-8">
  let userName = this.CUR_DATA && this.CUR_DATA.user && this.CUR_DATA.user.name ?
    this.CUR_DATA.user.name : '';
  function getBizRoleCode(){
    var splitCookies = document.cookie.split(';')
    var bizRoleCode = splitCookies.find(c => c.includes('bizRoleCode'))
    bizRoleCode = bizRoleCode.split('=')[1] || ''
    return bizRoleCode
  }
  (() => {
    var sensors = window['sensorsDataAnalytic201505'];
    if (!sensors) return
    sensors.init({
      server_url: this.CUR_DATA.env === 'pro' ? 'https://tracking.zkh.com/sa?project=production' : 'https://tracking.zkh.com/sa?project=abroad',
      show_log: this.CUR_DATA.env !== 'pro',
      distinct_id: userName,
      cross_subdomain: false,
      heatmap:{
        clickmap: 'default',
        scroll_notice_map:'default'
      },
      is_track_single_page:true,
      use_client_time:true,
      send_type:'beacon'    
    });
    sensors.registerPage({
      business_role: function () {
        var bizRoleCode = getBizRoleCode()
        console.log('sensors business_role in boss', bizRoleCode);
        return bizRoleCode;
      },
    });
    sensors.quick('autoTrack');
    sensors.loginWithKey('$identity_login_id', userName);
  })()
  </script>
  <!-- sensor data tracking  -->
</head>
<body>
  <script type='text/javascript'>
    const pid = "frvd3vupws@ed23e5ba4a8fe22"
    const username = this.CUR_DATA && this.CUR_DATA.user && this.CUR_DATA.user.name ?
      this.CUR_DATA.user.name : '';
      this.CUR_DATA.env !== 'local' ? !(function(c,b,d,a){
      c[a]||(c[a]={});
      c[a].config={
        pid,
        appType:"web",
        imgUrl:"https://arms-retcode.aliyuncs.com/r.png?",
        sendResource:true,
        environment: this.CUR_DATA.env === 'pro' ? 'prod' : 'pre',
        enableLinkTrace:true,
        behavior:true,
        setUsername: function () {
          return username;
        },
        urlHelper: [
          {
            rule: /\/([a-zA-Z\-_]+)?\d{2,30}/g, target: '/$1**',
          },
        ],
        ignore: {
          ignoreApis: [],
          ignoreUrls: [],
          ignoreErrors: [
            /^invalid_token\.?$/,
            /^Script error\.?$/,
            'Request failed with status code 401',
            'ResizeObserver loop limit exceeded',
            'ResizeObserver loop completed with undelivered notifications.',
          ]
        }
      };
      with(b)with(body)with(insertBefore(createElement("script"),firstChild))setAttribute("crossorigin","",src=d)
    })(window,document,"https://retcode.alicdn.com/retcode/bl.js","__bl"):null;
    // (function(a,h,c,b,f,g){a["UdeskApiObject"]=f;a[f]=a[f]||function(){(a[f].d=a[f].d||[]).push(arguments)};g=h.createElement(c);g.async=1;g.charset="utf-8";g.src=b;c=h.getElementsByTagName(c)[0];c.parentNode.insertBefore(g,c)})(window,document,"script","https://assets-cli.udesk.cn/im_client/js/udeskApi.js","ud");
    // ud({
    //     "code": "5b141kjb",
    //     "link": "https://zkh360.udesk.cn/im_client/?web_plugin_id=1520251",
    //     "pos_flag": 'crm',
    //     "customer": {
    //       "nonce": "{{ nonce }}",
    //       "signature": "{{ signature }}",
    //       "timestamp": "{{ timestamp }}",
    //       "web_token": "{{ web_token }}",
    //       "c_name": "{{ user.name }}@zkh",
    //     }
    // });
  </script>
   <style>
     #dify-chatbot-bubble-button {
       background-color: #1C64F2 !important;
     }
     #dify-chatbot-bubble-window {
       width: 40rem !important;
       height: 60rem !important;
     }
     #chatbot-bubble-iframe {
       width: 44rem;
       min-width: 414px;
     }
   </style>
  <div id="app" style="display: flex; justify-content: center; align-items: center; height: 100vh;">
    <p style="color: #ddd; font-size: 48px;">Boss Admin</p>
  </div>
  {% if manifest['common.js'] %}
    <script src="{{ entrance('common.js') }}"></script>
  {% endif %}
  {% if manifest['vendor.js'] %}
    <script src="{{ entrance('vendor.js') }}"></script>
  {% endif %}
  <!-- large-size script use cdn for speed up -->
  <script defer src="https://static.zkh360.com/file/resource/external/echarts.v4.3.0.min.js"></script>
  <script src="https://files.zkh360.com/assets/xe-utils.umd.min.js"></script>
  <script src="https://files.zkh360.com/assets/vxe-table.umd.min.js"></script>
  <!-- <script src="https://files.zkh360.com/assets/udesk.js"></script> -->
  <script src="{{ entrance('app.js') }}"></script>
  <script defer src="https://files.zkh360.com/watermark/index.js"></script>
  <script src="https://files.zkh360.com/watermark/chat.js"></script>
</body>
</html>
