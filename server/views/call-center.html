<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Cache-Control" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>BOSS-UNION</title>
  {% if not isHotDev and  manifest['callCenter.css'] %}
    <link rel="stylesheet" href="{{ entrance('callCenter.css') }}"/>
  {% endif %}
  {% if manifest['vendor.css'] %}
    <link rel="stylesheet" href="{{ entrance('vendor.css') }}"/>
  {% endif %}
  {% if manifest['common.css'] %}
    <link rel="stylesheet" href="{{ entrance('common.css') }}"/>
  {% endif %}
  <script>
    this.CUR_DATA = {
      user: {
        name: '{{ user.name }}'
      },
      app: ' {{ manifest[app] }} ',
      env: '{{ env }}'
    }
    this.RES_PUBLIC_PATH = '{{ resUrl }}'
    console.log(this.CUR_DATA)
  </script>
  <script charset="UTF-8" src="https://files.zkh360.com/assets/sensor/sa-sdk-javascript-1.26.3/sensorsdata.min.js"></script>
  <script charset="UTF-8">
  let userName = this.CUR_DATA && this.CUR_DATA.user && this.CUR_DATA.user.name ?
    this.CUR_DATA.user.name : '';
  this.CUR_DATA.env === 'pro' ? (() => {
    var sensors = window['sensorsDataAnalytic201505'];
    if (!sensors) return
    sensors.init({
      server_url: 'https://tracking.zkh.com/sa?project=production',
      show_log: this.CUR_DATA.env !== 'pro',
      distinct_id: userName,
      cross_subdomain: false,
      heatmap:{
        clickmap: 'default',
        scroll_notice_map:'default'
      },
      is_track_single_page:true,
      use_client_time:true,
      send_type:'beacon'    
    });
    sensors.quick('autoTrack');
    sensors.loginWithKey('$identity_login_id', userName);
  })() : null
  </script>
</head>
<body>
  <script type='text/javascript'>
    const pid = this.CUR_DATA.env !== 'pro' ? "frvd3vupws@fa050d1af338770" : "frvd3vupws@ed23e5ba4a8fe22"
    !(function(c,b,d,a){c[a]||(c[a]={});c[a].config={pid,appType:"web",imgUrl:"https://arms-retcode.aliyuncs.com/r.png?",sendResource:true,enableLinkTrace:true,behavior:true};
    with(b)with(body)with(insertBefore(createElement("script"),firstChild))setAttribute("crossorigin","",src=d)
    })(window,document,"https://retcode.alicdn.com/retcode/bl.js","__bl");
  </script>
  <div id="app" style="display: flex; justify-content: center; align-items: center; height: 100vh;">
    <p style="color: #ddd; font-size: 48px;">Boss Admin</p>
  </div>
  {% if not isHotDev and manifest['common.js'] %}
    <script src="{{ entrance('common.js') }}"></script>
  {% endif %}
  {% if manifest['vendor.js'] %}
    <script src="{{ entrance('vendor.js') }}"></script>
  {% endif %}
  <!-- large-size script use cdn for speed up -->
  <script defer src="https://static.zkh360.com/file/resource/external/echarts.v4.3.0.min.js"></script>
  <script src="{{ entrance('callCenter.js') }}"></script>
</body>
</html>
