import { Context } from 'koa'
import { safeRun } from '../util/index'
import { DmsUtils } from '../util/initDms'
import * as env from '@server/env'
import API from '../config/api'
const mseEnv = env.RELEASE || env.PRERELEASE ? 'pro' : 'dev'
const acmInstance = require('@boss/mse').MSE.getInstance(mseEnv);
const SqlString = require('sqlstring')

const type = env.LOCAL || env.DEV ? 'dev' : env.QA ? 'qa' : 'pro'
const dbUrl = API.BOSS_DMS

console.log(`type: ${type}, connect url: ${dbUrl}`)

const config = {
  connectionLimit: 10,
  user: '',
  host: dbUrl,
  token: '',
  key: '',
  database: '',
}
const dmsConfigId = `boss-dms-${type}.config`
const invTypeMap = {
  '20': 'fe_boss_inv_mgt_20',
  '21': 'fe_boss_inv_mgt_20',
  '22': 'fe_boss_inv_mgt_20',
  '18': 'fe_boss_inv_mgt_20',
  '19': 'fe_boss_inv_mgt_20',
  '28': 'fe_boss_inv_mgt_20',
  '30': 'fe_boss_inv_mgt_20',
  '31': 'fe_boss_inv_mgt_20',
  '13': 'fe_boss_inv_mgt_20',
  '29': 'fe_boss_inv_mgt_20',
  '27': 'fe_boss_inv_mgt_20',
  '23': 'fe_boss_inv_mgt_20',
  '32': 'fe_boss_inv_mgt_20',
  'list': 'fe_boss_inv_mgt_list'
}
const poTypeMap = {
  'Z001': 'fe_boss_po_z001',
  'Z002': 'fe_boss_po_z002',
  'Z003': 'fe_boss_po_z003',
  'Z004': 'fe_boss_po_z004',
  'Z005': 'fe_boss_po_z005',
  'Z006': 'fe_boss_po_z006',
  'Z007': 'fe_boss_po_z007',
  'Z008': 'fe_boss_po_z008',
  'Z009': 'fe_boss_po_z009',
  'Z010': 'fe_boss_po_z010',
  'Z011': 'fe_boss_po_z011',
  'Z012': 'fe_boss_po_z012',
  'Z013': 'fe_boss_po_z013',
  'Z014': 'fe_boss_po_z014',
  'Z015': 'fe_boss_po_z015'
}
const poDetailTypeMap = {
  'Z001': 'fe_boss_po_z001_detail',
  'Z002': 'fe_boss_po_z002_detail',
  'Z003': 'fe_boss_po_z003_detail',
  'Z004': 'fe_boss_po_z004_detail',
  'Z005': 'fe_boss_po_z005_detail',
  'Z006': 'fe_boss_po_z006_detail',
  'Z007': 'fe_boss_po_z007_detail',
  'Z008': 'fe_boss_po_z008_detail',
  'Z009': 'fe_boss_po_z009_detail',
  'Z010': 'fe_boss_po_z010_detail',
  'Z011': 'fe_boss_po_z011_detail',
  'Z012': 'fe_boss_po_z012_detail',
  'Z013': 'fe_boss_po_z013_detail',
  'Z014': 'fe_boss_po_z014_detail',
  'Z015': 'fe_boss_po_z015_detail'
}

export const getInvFields = async (ctx: Context) => {
  const { type } = ctx.query
  let data = null
  let message = null
  let code = 0
  let body = { code, data, message }
  if (!type) {
    body.code = 400
    body.message = 'param type needed!'
    ctx.body = body
    return
  }
  if (!invTypeMap[type as string]) {
    body.code = 400
    body.message = 'not suppoer this type'
    ctx.body = body
    return
  }
  try {
    let response = await acmInstance.getConfig(dmsConfigId)
    safeRun(() => {
      response = JSON.parse(response)
    })
    const { token, key, database, user } = response
    config.token = token
    config.key = key
    config.database = database
    config.user = user
    const dmsInstance = DmsUtils.getInstance('boss-admin', config)
    const res = await dmsInstance.query(`SELECT * FROM \`${database}\`.\`${invTypeMap[type as string]}\` ORDER BY \`id\``)
    body.data = res
  } catch (err) {
    console.log('getInvFields DmsUtils.getInstance and dmsInstance.query:', err)
    body.message = err.message || 'internal server error'
    body.code = 500
    if (err.code === 'ER_NO_SUCH_TABLE') {
      body.message = '不支持这种订单类型！'
    }
  }
  ctx.body = body
}
export const getPoFields = async (ctx: Context) => {
  const { type, routerType } = ctx.query
  let orderMap = poTypeMap
  if (routerType === 'detail') {
    orderMap = poDetailTypeMap
  }
  let data = null
  let message = null
  let code = 0
  let body = { code, data, message }
  if (!type) {
    body.code = 400
    body.message = 'param type needed!'
    ctx.body = body
    return
  }
  if (!orderMap[type as string]) {
    body.code = 400
    body.message = 'not suppoer this type'
    ctx.body = body
    return
  }
  try {
    let response = await acmInstance.getConfig(dmsConfigId)
    safeRun(() => {
      response = JSON.parse(response)
    })
    const { token, key, database, user } = response
    config.token = token
    config.key = key
    config.database = database
    config.user = user
    const dmsInstance = DmsUtils.getInstance('boss-admin', config)
    const res = await dmsInstance.query(`SELECT * FROM \`${database}\`.\`${orderMap[type as string]}\` ORDER BY \`id\``)
    body.data = res
  } catch (err) {
    console.log('getPoFields DmsUtils.getInstance and dmsInstance.query:', err)
    body.message = err.message || 'internal server error'
    body.code = 500
    if (err.code === 'ER_NO_SUCH_TABLE') {
      body.message = '不支持这种订单类型！'
    }
  }
  ctx.body = body
}

export const getStockAgreementFields = async (ctx: Context) => {
  let data = null
  let message = null
  let code = 0
  let body = { code, data, message }
  try {
    let response = await acmInstance.getConfig(dmsConfigId)
    safeRun(() => {
      response = JSON.parse(response)
    })
    const { token, key, database, user } = response
    config.token = token
    config.key = key
    config.database = database
    config.user = user
    const dmsInstance = DmsUtils.getInstance('boss-admin', config)
    const res = await dmsInstance.query(`SELECT * FROM \`${database}\`.\`fe_boss_stock_agreement\` ORDER BY \`id\``)
    body.data = res
  } catch (err) {
    console.log('getPoFields DmsUtils.getInstance and dmsInstance.query:', err)
    body.message = err.message || 'internal server error'
    body.code = 500
    if (err.code === 'ER_NO_SUCH_TABLE') {
      body.message = '不支持这种订单类型！'
    }
  }
  ctx.body = body
}

// function insertObject (tableName, data) {
//   let query = 'INSERT INTO' + tableName;
//   const props = []
//   for (let prop in data) { props.push(prop) }
//   query = query +  `(${props.join(',')}) VALUES`
//   const values = []
//   for (let prop in data) { values.push(data[prop]) }
//   query = query +  `(${values.slice().fill('?').join(',')})`
//   return {
//     query,
//     data: values
//   }
// }
// function updateObject (tableName, data) {
//   let query = 'UPDATE ' + tableName + ' SET';
//   const props = []
//   for (let prop in data) { props.push(prop) }
//   query = query +  `${props.join('=?,')}`
//   const values = []
//   for (let prop in data) { values.push(data[prop]) }
//   query = query +  `(${values.slice().fill('?').join(',')})`
//   return {
//     query,
//     data: values
//   }
// }
// function deleteById (tableName, id) {
//   return {
//     query: `DELETE FROM \`${tableName}\` WHERE ID = ?`,
//     data: [id]
//   }
// }
// function buildQuery (queryObj) {
//   const { query, data } = queryObj
//   // let index = 0;
//   // const queryString = query.replace(/\?/g, (a,b,c) => data[index++]);
//   const queryString = SqlString.format(query, data)
//   return queryString
// }
// // INSERT INTO `fe_boss_po_z013_detail` VALUES (1, '采购单号', 'orderNo', 'input', 120, 6, 'baseInfo', 0, NULL, 0, 2, 'detail');
// const sqlManager = {
//   add: (tableName, data) => buildQuery(insertObject(tableName, data)),
//   delete : (tableName, id) => buildQuery(deleteById(tableName, id)),
//   update: (tableName, data) => buildQuery(updateObject(tableName, data)),
// };

// export const updateInvFields = async (ctx: Context) => {
//   const { type } = ctx.query
//   const { body } = ctx.request.body
//   const { crudType, data: submitData } = body || {}
//   let data, message, code = 0
//   let resBody = { code, data, message }
//   if (!type) {
//     return ctx.body = { code: 400, message: 'param type needed!' }
//   }
//   if (!invTypeMap[type as string]) {
//     return ctx.body = { code: 400, message: 'not suppoer this type' }
//   }
//   if (!sqlManager[crudType as string]) {
//     return ctx.body = { code: 400, message: 'crudType needed!' }
//   }
//   try {
//     let response = await acmInstance.getConfig(dmsConfigId)
//     safeRun(() => {
//       response = JSON.parse(response)
//     })
//     const { token, key, database, user } = response
//     config.token = token
//     config.key = key
//     config.database = database
//     config.user = user
//     const dmsInstance = DmsUtils.getInstance('boss-admin', config)
//     const queryString = sqlManager[crudType as string]
//     const res = await dmsInstance.query(queryString)
//     resBody.data = res
//   } catch (err) {
//     console.log('updateInvFields DmsUtils.getInstance and dmsInstance.query:', err)
//     resBody.message = err.message || 'internal server error'
//     resBody.code = 500
//     if (err.code === 'ER_NO_SUCH_TABLE') {
//       resBody.message = '不支持这种订单类型！'
//     }
//   }
//   ctx.body = resBody
// }
// export const updatePoFields = async (ctx: Context) => {
//   const { type, routerType, crudType } = ctx.query
//   let orderMap = poTypeMap
//   if (routerType === 'detail') {
//     orderMap = poDetailTypeMap
//   }
//   let data, message, code = 0
//   let resBody = { code, data, message }
//   if (!type) {
//     return ctx.body = { code: 400, message: 'param type needed!' }
//   }
//   if (!orderMap[type as string]) {
//     return ctx.body = { code: 400, message: 'not suppoer this type' }
//   }
//   if (!sqlManager[crudType as string]) {
//     return ctx.body = { code: 400, message: 'crudType needed!' }
//   }
//   try {
//     let response = await acmInstance.getConfig(dmsConfigId)
//     safeRun(() => {
//       response = JSON.parse(response)
//     })
//     const { token, key, database, user } = response
//     config.token = token
//     config.key = key
//     config.database = database
//     config.user = user
//     const dmsInstance = DmsUtils.getInstance('boss-admin', config)
//     const queryString = sqlManager[crudType as string]
//     const res = await dmsInstance.query(queryString)
//     resBody.data = res
//   } catch (err) {
//     console.log('updatePoFields DmsUtils.getInstance and dmsInstance.query:', err)
//     resBody.message = err.message || 'internal server error'
//     resBody.code = 500
//     if (err.code === 'ER_NO_SUCH_TABLE') {
//       resBody.message = '不支持这种订单类型！'
//     }
//   }
//   ctx.body = resBody
// }
