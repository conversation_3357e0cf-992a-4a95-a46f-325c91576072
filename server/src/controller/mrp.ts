import fetch from 'node-fetch'
import { readSheet } from '@boss/excel'
import { Type } from '@poseidon/token/dist/defination'
import api from '../config/api'

const fs = require('fs')
export const excelMap = {
  '供应商编号': 'providerNo',
  'SKU编号': 'skuNo',
};
// OEM紧固件批导处理(新增/删除)
export const uploadEomExcel = async (ctx: any) => {
  const files = ctx.request.files
  const { type = 1 } = ctx.request.body
  const configLine = 1
  if (files && files.file && files.file.path) {
    try {
      const json = readSheet(fs.readFileSync(files.file.path), {})
      if (json && json.length > configLine) {
        const attrs: string[] = json[0] as string[]
        const itemList = []
        for(let i = 1; i < json.length; i++){
          itemList.push({
            type: Number(type),
          })
          json[i].forEach((item,j) => {
            itemList[itemList.length - 1][excelMap[attrs[j]]] = item;
          })
        }
        if (itemList.length > 0) {
          const options = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json;charset=utf-8',
              Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
            },
            body: JSON.stringify(itemList)
          }
          try {
            const url = '/dictionary/batchInsert';
            const response = await fetch(`${api.MRP}${url}`, options)
            const res = await response.json()
            ctx.body = {
              ...res,
              rawData: itemList
            }
          } catch (ex) {
            ctx.body = {
              code: 500,
              rawData: json,
              msg: ex
            }
          }
        } else {
          const msg = '导入数据为空！'
          ctx.body = {
            code: 500,
            rawData: json,
            msg
          }
        }
      }
    } catch (error) {
      ctx.body = {}
      if (error && error.message) {
        ctx.body.msg = error.message
      }
      ctx.body.code = 500
    }
  }
  ctx.status = 200
}
