import { Context } from 'koa'
import API from '../config/api'
import { defination, helper } from '@poseidon/token'

const { Type } = defination

export const roles = async (ctx: Context) => {
  
  ctx.type = 'json'
  try {
    const res = await helper.request({
      url: `${API.SECURITY}/accounts/info/username`,
      method: 'get',
      headers: {
        Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
      },
      query: {
        username: ctx.state.token[Type.security].user.name
      }
    })
    ctx.body = {
      code: 200,
      message: '',
      data: res ? {
        email: res.email,
        id: res.id,
        roleInfoList: res.roleInfoList,
        owerWorkCode: res.owerWorkCode,
        username: res.username
      } : {}
    }
  } catch (err) {
    // ctx.status = 401
    ctx.body = {
      code: 401,
      message: 'Unauthorized'
    }
  }
}