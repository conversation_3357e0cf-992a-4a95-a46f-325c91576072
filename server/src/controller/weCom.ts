import fetch from 'node-fetch'
import { send as weComSend } from '@boss/wecom';
import { weCom } from '../config'
import api from '../config/api'

const { agentid } = weCom

export const send = async (ctx: any) => {
  const { orderNo, user } = ctx.request.query
  const optons = {
    method: 'GET'
  }
  const modelResponse = await fetch(
    `${api.OMS}/bossmodel/get/model?modelType=1&orderNo=${orderNo}&pdf=1`,
    optons
  )
  const modelRes = await modelResponse.json()
  console.log(modelRes)
  let body
  if (modelRes && modelRes.matchResultUrl) {
    const data = {
      touser: user,
      msgtype: 'text',
      agentid,
      text: {
        content: `【你在交付小助手中导出了一个送货单文件，请查收】<br>文件地址：${modelRes.matchResultUrl}`
      }
    }
    const responseData = await weComSend(data)
    body = responseData
  } else {
    body = {
      msg: '抱歉，该送货单暂时不支持导出'
    }
  }
  ctx.body = body || 'error'
}

export const sendMsg = async (ctx: any) => {
  const { content, user } = ctx.request.body
  let body
  const _user = user?.trim() || ''
  const data = {
    touser: _user,
    msgtype: 'text',
    agentid,
    text: {
      content
    }
  }
  const responseData = await weComSend(data)
  body = responseData
  ctx.body = body || 'error'
}