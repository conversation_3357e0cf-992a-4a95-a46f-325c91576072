import * as env from '@server/env'
import {createHash} from 'crypto'
import * as shortid from 'shortid'
import api from '../config/api'
import { SaSingleton } from '../util/sensors';

import { defination, helper } from '@poseidon/token'
import { log } from 'console';

const { Type } = defination

const encrypt = (algorithm: string, content: string) => {
  let hash = createHash(algorithm)
  hash.update(content)
  return hash.digest('hex')
}

const track = (user: string, eventName: string, props: any) => {
  try {
    const instance = SaSingleton.getInstance();
    instance.track(
      user,
      eventName,
      props,
    );
  } catch (ex) {
    console.log(ex);
  }
}

const checkWorkbenchAuth = async (accessToken: string) => {
  try {
    const res = await helper.request({
      url: `${api.OPC_CSC}/api/user/infoByDomain`,
      method: 'get',
      headers: {
        Authorization: `bearer ${accessToken}`
      },
    })
    if(res?.data){
      return res.data
    }
    // if ((res?.data?.bizRoleList || []).some((item) => item.workbenchAvailable === true)) {
    //   return true;
    // }
  } catch (err) {
    console.log(err && err.message)
  }
  return false;
}

export const index = async ctx => {
  ctx.set({
    'cache-control': 'no-cache',
    expires: 0
  })
  const user = ctx.state.token[Type.security].user;
  const accessToken = ctx.state.token[Type.security].accessToken
  const originalUrl = ctx.request.originalUrl;
  
  if (!env.LOCAL && originalUrl.trim() === '/' && accessToken) {
    const data = await checkWorkbenchAuth(accessToken);
    const needRedirect = (data?.bizRoleList || []).some((item) => item.workbenchAvailable === true)
    if (needRedirect) {
      console.log(`user ${user.name} workbenchAvailable is true, redirect to new home page`);
      
      // 埋点上报
      track(user.name, 'WorkbenchView', {
        current_security_role_name: [data.bizRoleList[0]?.bizRoleName || ''],
        first_menu_name: '我的工作台',
        label_name: '首页',
        platform_type: 'BOSS-工作台',
        current_page_type: data.bizRoleList[0]?.workbenchRoleName || '',
        operation_type: '菜单'
      })
      ctx.redirect('/wb/');
      return;
    }
  }
  // get browser resource info, which in /static
  const info = ctx.helper.getStaticInfo('boss')
  const nonce = shortid.generate();
  const timestamp = Number(new Date());
  const web_token = `${user.name}@zkh`;
  const im_user_key = '776325d6286bfc142d9dbdae27ff9c72';
  const sign_str = `nonce=${nonce}&timestamp=${timestamp}&web_token=${web_token}&${im_user_key}`;
  const signature = encrypt('sha1', sign_str).toUpperCase();

  // get rendered string
  const html = await ctx.render(
    'home',
    {
      ...info,
      nonce,
      timestamp,
      web_token,
      signature,
      user,
      env: env.LOCAL ? 'local' : env.DEV ? 'fat' : env.QA ? 'uat' : 'pro'
    },
    false
  )

  ctx.body = html
}

export const loginCallback = async ctx =>{
  const { query } = ctx;
  if(query.stateUrl){
    ctx.redirect(query.stateUrl);
  }else {
    ctx.redirect('/')
  }
}