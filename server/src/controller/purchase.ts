import fetch from 'node-fetch'
import * as path from 'path'
import * as env from '@server/env'
import { readSheet } from '@boss/excel'
import { createPurchaseOrderContract } from '@boss/pdf'
import { Type } from '@poseidon/token/dist/defination'
import api from '../config/api'
import { safeRun } from '../util/index'
import { Console } from 'console'

const mseEnv = env.RELEASE || env.PRERELEASE ? 'pro' : 'dev'
const instance = require('@boss/mse').MSE.getInstance(mseEnv)
const fs = require('fs')
const padStart = require('lodash/padStart')
const XLSX = require('xlsx')
const jschardet = require('jschardet')

const pdfPath = path.join(__dirname, '../../assets', 'pdf', 'output.pdf')


function getBatchUrl (orderType) {
  let url = '/po/stdBatchInput'
  switch (orderType) {
    case 'Z003':
      url = '/po/transferPOBatchInput'
      break
    case 'Z007':
    case 'Z008':
      url = '/po/adminPOBatchInput'
      break
    case 'Z004':
      url = '/po/returnPOBatchInput'
      break
    case 'Z010':
      url = '/po/servicePOBatchInput'
      break
    default:
      break
  }
  return url
}

// 批量创建订单
export const uploadExcel = async (ctx: any) => {
  const { orderType } = ctx.request.query
  const files = ctx.request.files
  const configLine = 4
  const dictListOptions = {
    method: 'GET',
    headers: {
      Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
    }
  }
  const dictListRes = await fetch(`${api.MM}/config/dictionary/list`, dictListOptions)
  const dictList = await dictListRes.json()
  if (files && files.file && files.file.path) {
    try {
      const json = readSheet(fs.readFileSync(files.file.path), {})
      if (json && json.length > configLine) {
        const attrs: string[] = json[0] as string[]
        const itemList = []
        const createUser = (ctx.state.token[Type.security] && ctx.state.token[Type.security].user) ?
          ctx.state.token[Type.security].user.name : ''
        for (let i = configLine; i < json.length; i++) {
          if (json[i] && json[i].length > 0) {
            const data = {
              orderType: '',
              createUser
            }
            if (['Z003', 'Z004'].indexOf(orderType) > -1) {
              data.orderType = orderType
            }
            for (let j = 1; j < json[i].length; j++) {
              if (json[i][j] != null) {
                if (attrs[j] === 'orderType') {
                  data[attrs[j]] = (json[i][j] || '').trim().substring(0, 4)
                } else if (attrs[j] === 'taxedPrice') {
                  data[attrs[j]] = (new Number(json[i][j])).toFixed(2)
                } else if (attrs[j] === 'returnReason') {
                  const returnReason = (json[i][j]).split(' ').filter(item => !!item)
                  data[attrs[j]] = (returnReason && returnReason.length > 1) ? returnReason[0]: ''
                } else if (attrs[j] === 'inputTax') {
                  const inputTax = (json[i][j]).split(' ').filter(item => !!item)
                  data[attrs[j]] = (inputTax && inputTax.length > 1) ? inputTax[0]: ''
                } else if (['returnWay', 'drawbackWay', 'receiptReturnWay'].includes(attrs[j])) {
                  const dic = dictList.data.filter(item => item.type === attrs[j]).filter(inner => inner.name === json[i][j])
                  data[attrs[j]] = dic && dic.length > 0 ? dic[0].value : ''
                } else if (attrs[j] === 'taglibIdList') {
                  if (json[i][j] === '战略备货')
                  data[attrs[j]] =[101]
                } else if (attrs[j] === 'ownerTransfer') {
                  if (json[i][j] === '货主转移')
                  {
                    if (data['taglibIdList'] && data['taglibIdList'].length > 0) {
                      data['taglibIdList'] = [data['taglibIdList'][0], 102]
                    } else {
                      data['taglibIdList'] = [102]
                    }
                  }
                } else if(attrs[j] === 'isOverchargeFree') {
                  data[attrs[j]] = (json[i][j] === '是')? 1 : 0
                } else {
                  data[attrs[j]] = json[i][j]
                }
              }
              if (attrs[j] === 'group' && !json[i][j]) {
                throw new Error('识别号码必须必填！')
              }
            }
            itemList.push(data)
          }
        }
        if (itemList.length > 0) {
          const formatedItemList = {}
          const oaProps = ['oaNo', 'oaItemNo']
          itemList.forEach((item, idx) => {
            const { soNo } = item
            let { group } = item
            // 针对服务采购订单
            if (!group && soNo) {
              group = soNo
            }
            oaProps.forEach(prop => {
              if (item[prop]) item[prop] = String(item[prop])
            })
            if (!formatedItemList[group]) {
              formatedItemList[group] = []
            }
            formatedItemList[group].push({
              ...item,
              lineNumber: idx + configLine + 1
            })
          })
          const data = Object.keys(formatedItemList).map(key => ({
            identifier: key,
            itemList: formatedItemList[key]
          }))
          const options = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json;charset=utf-8',
              Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
            },
            body: JSON.stringify(data)
          }
          try {
            const url = getBatchUrl(orderType)
            const response = await fetch(`${api.MM}${url}`, options)
            const res = await response.json()
            ctx.body = {
              ...res,
              rawData: data
            }
          } catch (ex) {
            ctx.body = {
              code: 500,
              rawData: json,
              msg: ex
            }
          }
        } else {
          const msg = '导入数据为空！'
          ctx.body = {
            code: 500,
            rawData: json,
            msg
          }
        }
      }
    } catch (error) {
      ctx.body = {}
      if (error && error.message) {
        ctx.body.msg = error.message
      }
      ctx.body.code = 500
    }
  }
  ctx.status = 200
}

function parseExcel(path, confLen, maxLen) {
  let status = false
  let result = null
  try {
    const json = readSheet(fs.readFileSync(path), {})
    if (json && json.length > confLen) {
      if (json.length > maxLen + confLen) {
        result = `当前模板仅支持${maxLen}行，请重新调整后再上传`
      } else {
        status = true
        result = json.filter(item => item && item.length > 0)
      }
    } else {
      result = '没有数据！'
    }
  } catch (ex) {
    result = ex
  }
  return {
    status, result
  }
}

export const batchEditByExcel = async (ctx: any) => {
  const { type } = ctx.request.query
  const files = ctx.request.files
  const createUser = (ctx.state.token[Type.security] && ctx.state.token[Type.security].user) ?
    ctx.state.token[Type.security].user.name : ''
  if (files && files.file && files.file.path) {
    const line = 3
    const { status, result } = parseExcel(files.file.path, line, 3000)
    if (status && (type === 'body' || type === 'header')) {
      const options = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json;charset=utf-8',
          Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
        },
        body: ''
      }
      const getOptions = {
        method: 'GET',
        headers: {
          Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
        }
      }
      const header = result[0]
      const dictListRes = await fetch(`${api.MM}/config/dictionary/list?typeSplit=paymentTermCode`, getOptions)
      const dictList = await dictListRes.json()
      let data
      if (type === 'header') {
        data = result.slice(3).map((item, idx) => {
          if (item && Array.isArray(item)) {
            const formatedData = {
              updateUser: createUser,
              lineNumber: line + idx + 1,
              orderNo: ''
            }
            item.forEach((val, idx) => {
              if (header[idx] === 'isUrgent') {
                formatedData[header[idx]] = val === 'Y' ? 1 : 0
              } else if (header[idx] === 'paymentTermCode') {
                const dic = dictList.data.filter(item => item.type === header[idx]).filter(inner => inner.name === val)
                formatedData[header[idx]] = dic && dic.length > 0 ? dic[0].value : ''
              } else {
                formatedData[header[idx]] = val
              }
            })
            return formatedData
            // paymentTermCode
          }
          return null
        }).filter(item => !!item)
      } else {
        const formatedResult = {}
        result.slice(line).forEach((item, idx) => {
          if (item && Array.isArray(item)) {
            const rowItem:any = {
              orderNo: '',
              lineNumber: idx + 1
            }
            item.forEach((val, idx) => {
              if (['isConfirmed', 'isDeleted', 'isDeliveryDone', 'isLastInvoice'].includes(header[idx])) {
                rowItem[header[idx]] = val === 'Y' ? 1 : 0
              } else if(header[idx] === 'itemNo'){
                rowItem[header[idx]] = padStart(val,5,'0')
              } else if(header[idx] === 'taxedPrice'){
                rowItem[header[idx]] = Number(Number(val).toFixed(2))
              } else {
                rowItem[header[idx]] = val
              }
            })
            const { orderNo } = rowItem
            if (orderNo) {
              if (orderNo && !formatedResult[orderNo]) {
                formatedResult[orderNo] = []
              }
              formatedResult[orderNo].push(rowItem)
            }
          }
        })
        data = []
        Object.keys(formatedResult).forEach(key => {
          if (formatedResult[key] && formatedResult[key].length > 0) {
            const orderNo = key
            const firstOrder = formatedResult[key][0]
            const orderRow = {
              orderNo,
              updateUser: createUser,
              lineNumber: firstOrder.lineNumber,
              itemList: formatedResult[key]
            }
            data.push(orderRow)
          }
        })
      }

      options.body = JSON.stringify(data)
      let url = ''
      if (type === 'body') {
        url = '/po/itemBatchUpdate'
      }
      if (type === 'header') {
        url = '/po/headBatchUpdate'
      }
      const response = await fetch(
        `${api.MM}${url}`,
        options
      )
      const res = await response.json()
      ctx.body = {
        ...res,
        rawData: data
      }
    } else {
      ctx.body = {
        msg: result,
        code: 413,
      }
    }
  }
  ctx.status = 200
}
export function toTree (data) {
  data.forEach(item => {
    delete item.children
  })
  let val = []
  data.forEach(item => {
    let parent = data.find(innerItem => innerItem.value === item.parentCode)
    if (parent) {
      (parent.children || (parent.children = [])).push(parent.parentCode ? toTree(item) : item)
    } else {
      val.push(item)
    }
  })
  return val
}

export const uploadPendingReasonExcel = async (ctx: any) => {
  const files = ctx.request.files
  const createUser = (ctx.state.token[Type.security] && ctx.state.token[Type.security].user) ?
    ctx.state.token[Type.security].user.name : ''
  const dictListOptions = {
    method: 'GET',
    headers: {
      Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
    }
  }
  const reasonListRes = await fetch(`${api.OPC}/v1/dict/values/values/all?includeKey=pendingReason`, dictListOptions)
  const reasonListJson = await reasonListRes.json()
  let reasonList = []
  if (reasonListJson.code === 200) {
    reasonList = toTree(reasonListJson.data.map(item => { return { value: Number(item.code), label: item.name, parentCode: Number(item.parentCode) } }))
  }
  if (files && files.file && files.file.path) {
    const line = 2
    const { status, result } = parseExcel(files.file.path, line, 3000)
    if (status) {
      try {
        const options = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
            Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
          },
          body: ''
        }
        const attrs = result[0]
        let data = []
        for (let i = 2; i < result.length; i++) {
          if (result[i] && result[i].length > 0) {
            let item = {}
            for (let j = 0; j < result[i].length; j++) {
              let reason = []
              if (result[i][0] && attrs[j]) {
                if (attrs[j] === 'prIds') {
                  item[attrs[j]] = [Number(result[i][j])]
                } else if (attrs[j] === 'pendingReason') {
                  reason = reasonList.filter(inner => inner.label === result[i][j])
                  item[attrs[j]] = reason ? reason[0].value : null
                } else if (attrs[j] === 'detailPendingReason') {
                  let detailReason
                  for (let inner = 0 ; inner < reasonList.length; inner ++) {
                    detailReason = reasonList[inner]?.children.filter(
                      detail => {
                        return detail.label === result[i][j]
                      }
                    )
                    if (detailReason && detailReason.length > 0) {
                      item[attrs[j]] = detailReason && detailReason.length > 0 ? detailReason[0].value : null
                      break;
                    }
                  }
                } else if(result[i][j]){
                  item[attrs[j]] = result[i][j]
                }
              }
              if (attrs[j] === 'pendingReason' && !result[i][j]) {
                throw new Error('汇总原因必填！')
              }
              if (attrs[j] === 'detailPendingReason' && !result[i][j]) {
                throw new Error('汇总原因明细必填！')
              }
            }
            data.push(item)
          }
        }
        options.body = JSON.stringify(data)
        let url = '/purchasePendingOrder/update/beatch/reason'
        const response = await fetch(
          `${api.MRP}${url}`,
          options
        )
        const res = await response.json()
        ctx.body = {
          ...res,
          rawData: data
        }
      } catch (error) {
        ctx.body = {}
        if (error && error.message) {
          ctx.body.msg = error.message
        }
        ctx.body.code = 500
      }
    } else {
      ctx.body = {
        msg: result,
        code: 413,
      }
    }
  }
  ctx.status = 200
}

export const exportPdf = async (ctx: any) => {
  const { no, status, type } = ctx.request.query
  if (no) {
    const options = {
      method: 'GET',
      headers: {
        Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
      }
    }
    const [response, dictResponse, purchaseGroupRes] = await Promise.all([
      fetch(
        `${api.MM}/po/get?orderNo=${no}`,
        options
      ),
      fetch(
        `${api.MM}/config/dictionary/list?typeSplit=paymentTermCode,inputTax,returnReason`,
        options
      ),
      fetch(
        `${api.MM}/config/purchaseGroup/all`,
        options
      )
    ])
    let companyList = await instance.getConfig('mm-company.config')
    let backCompanyList = await instance.getConfig('mm-back-company.config')
    const res = await response.json()
    const res1 = await dictResponse.json()
    const res2 = await purchaseGroupRes.json()
    safeRun(() => {
      companyList = JSON.parse(companyList)
      backCompanyList = JSON.parse(backCompanyList)
    })
    if (res?.code === 0 && res1?.code === 0 && res2?.code === 0 && companyList && backCompanyList ) {
      let { data: orderData } = res
      const { data: dictData } = res1
      const { data: purchaseGroupData } = res2
      // 打印合同、送货单不要已删除的行
      // if (type === 'a' || type === 'b' || type === 'c') {
      //   orderData.itemList = orderData.itemList.filter(item => !item.isDeleted)
      // }
      if (orderData && dictData) {
        // 内部使用NKG表示千克，对外展示的时候需要转化成KG
        const {itemList = []} = orderData
        itemList.forEach((item) => {
          if(item.unit === 'NKG'){
            item.unit = 'KG'
          }
        })
        const { path, name }: any = await createPurchaseOrderContract(pdfPath, orderData, dictData, purchaseGroupData, status, type, companyList, backCompanyList)
        if (path && name) {
          const stream = fs.createReadStream(path)
          ctx.body = stream
          ctx.set('Content-type', 'application/pdf')
          ctx.response.attachment(`${name}.pdf`)
        }
      }
    }
  }
  ctx.status = 200
}
export const exportPaPdf = async (ctx: any) => {
  const { no, status, type } = ctx.request.query
  if (no) {
    const options = {
      method: 'GET',
      headers: {
        Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
      }
    }
    const [response, dictResponse] = await Promise.all([
      fetch(
        `${api.MM}/pa/get?orderNo=${no}`,
        options
      ),
      fetch(
        `${api.MM}/config/dictionary/list?typeSplit=stockMode,inputTax,returnReason`,
        options
      )
    ])
    const res = await response.json()
    const res1 = await dictResponse.json()
    if (res?.code === 0 && res1?.code === 0 ) {
      let { data: paData } = res
      const { data: dictData } = res1
      if (paData && dictData) {
        const { path, name }: any = await createPurchaseOrderContract(pdfPath, paData, dictData, [], status, type, {}, {})
        if (path && name) {
          const stream = fs.createReadStream(path)
          ctx.body = stream
          ctx.set('Content-type', 'application/pdf')
          ctx.response.attachment(`${name}.pdf`)
        }
      }
    }
  }
  ctx.status = 200
}

function getInventoryType(name) {
  if (name.indexOf('自营') > -1) return ''
  if (name.indexOf('供应') > -1) return 'K'
  if (name.indexOf('寄售') > -1) return 'W'
  if (name.indexOf('委外') > -1) return 'O'
  if (name.indexOf('直发') > -1) return 'E'
}

export const updateStockReason = async (ctx: any) => {
  const files = ctx.request.files
  if (files && files.file && files.file.path) {
    try {
      const fileData = fs.readFileSync(files.file.path);
      const codepageString = jschardet.detect(fileData);
      let wb
      if (codepageString) {
        const { encoding } = codepageString
        if (encoding === 'UTF-8') {
          wb = XLSX.readFile(files.file.path, {})
        } else {
          wb = XLSX.readFile(files.file.path, { codepage: 936 })
        }
      }
      let json
      if (wb && wb.Sheets && wb.SheetNames && wb.SheetNames[0]) {
        const ws = wb.Sheets[wb.SheetNames[0]]
        json = XLSX.utils.sheet_to_json(ws,{range:1})
      }
      console.log(json);
      const dataList = []
      const creator = (ctx.state.token[Type.security] && ctx.state.token[Type.security].user) ?
        ctx.state.token[Type.security].user.name : ''
      if (json && json.length > 0) {
        json.forEach(jsonRow => {
          if (jsonRow) {
            const data = {
              creator
            }
            Object.keys(jsonRow).forEach(k => {
              if (k) {
                const keys = k.split('_')
                if(keys && keys.length > 0) {
                  const prop = keys[keys.length - 1]
                  const item = jsonRow[k]
                  if(prop==='slowMovingReason') {
                    data['reason'] = item
                  } else if(prop==='supplierName') {
                    data['supplierCode'] = item
                  }else if (prop) {
                    data[prop] = item
                  }
                }
              }
            })
            dataList.push(data)
          }
        })
      }
      if (dataList.length > 0) {
        const options = {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=utf-8',
            Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
          },
          body: JSON.stringify(dataList)
        }
        try {
          const response = await fetch(`${api.SIM}/sluggishReason/import`, options)
          const res = await response.json()
          console.log(1, res)
          ctx.body = {
            ...res,
            data: dataList,
            rawData: json,
          }
        } catch (err) {
        }
      }
    } catch (ex) {
      ctx.body = ex
      ctx.body.code = 500
    }
  }
  ctx.status = 200
}