import fetch from 'node-fetch'
import * as path from 'path'
import { readActiveSheet } from '@boss/excel'
import { Type } from '@poseidon/token/dist/defination'
const fs = require('fs')

import api from '../config/api'
import {
  getBatchUrl,
  orderTypeMap,
  propsMap
} from '../config/inventory'

const optionsMap = {
  responsibilityOwner: [
    { value: '0', label: '非坤合责任' },
    { value: '1', label: '坤合责任' }
  ]
}
const oaProps = ['oaNo', 'oaItemNo']
function mapValue2Data(data, title, value) {
  const prop = propsMap[title]
  if (optionsMap[prop]) {
    const find = optionsMap[prop].find(item => item.label == value)
    if (find) {
      value = find.value
    }
  }
  oaProps.forEach(oProp => {
    if (oProp === prop) value = String(value)
  })
  if (prop) { data[prop] = tryTrim(value) }
}

function tryTrim(str) {
  return str && str.trim && str.trim() || str
}
// 如果一列里边没有一个有效单元格，那过滤这一列
function filterEmpty(json) {
  return json.filter(item => Array.isArray(item) && item.some(data => {
    // 过滤空格串
    if (typeof data === 'string' && data.trim) {
      return !!data.trim()
    }
    return !!data
  }))
}

// 批量创建订单
export const uploadExcel = async (ctx: any) => {
  const { orderType } = ctx.request.query
  const warehouseList = [ '13', '29', '30', '31' ]
  const files = ctx.request.files
  const configLine = 4
  if (files && files.file && files.file.path) {
    try {
      const sheets = readActiveSheet(fs.readFileSync(files.file.path), {})
      let json = sheets[orderTypeMap[orderType]];
      if (!json) {
        throw new Error(`请使用正确模板上传！请确保当前编辑EXCEL模板活动sheet为: ${orderTypeMap[orderType]}！`)
      }
      json = filterEmpty(json)
      if (json && json.length > configLine) {
        const titles: string[] = json[0] as string[]
        const itemList = []
        const createUser = (ctx.state.token[Type.security] && ctx.state.token[Type.security].user) ?
          ctx.state.token[Type.security].user.name : ''

        for (let i = configLine; i < json.length; i++) {
          if (json[i] && json[i].length > 0) {
            const data = {}
            for (let j = 1; j < json[i].length; j++) {
              if (json[i][j] != null) {
                if (warehouseList.includes(orderType) && titles[j] === '发货仓库地点') {
                  titles[j] = '仓库地点'
                }
                mapValue2Data(data, titles[j], json[i][j])
              }
              if (titles[j] === '识别号码' && !json[i][j]) {
                console.log(json)
                throw new Error(`第${i + 1}行识别号码必填！`)
              }
              if (titles[j] === '采购订单' && !json[i][j]) {
                console.log(json)
                throw new Error(`第${i + 1}行采购订单必填！`)
              }
            }
            itemList.push(data)
          }
        }
        if (itemList.length > 0) {
          const idList = {}
          itemList.forEach((item, idx) => {
            const { identifier } = item
            if (!idList[identifier]) { idList[identifier] = [] }
            item.lineNumber = Number(idx + configLine + 1)
            item.transportDirection = Number(item.transportDirection)
            item.quantity = Number(item.quantity)
            idList[identifier].push({...item})
          })
          const data = Object.keys(idList).map(identifier => ({
            identifier,
            createUser,
            orderType,
            itemList: idList[identifier]
          }))
          const optons = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json;charset=utf-8',
              Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
            },
            body: JSON.stringify(data)
          }
          try {
            const url = getBatchUrl(orderType)
            console.log(`post url: ${url}, data: ${JSON.stringify(data)}`)
            const response = await fetch(`${api.MM}${url}`, optons)
            const res = await response.json()
            ctx.body = {
              ...res,
              rawData: data
            }
          } catch (err) {
            ctx.body = {
              code: 500,
              rawData: json,
              msg: err
            }
          }
        } else {
          const msg = '导入数据为空！'
          ctx.body = {
            code: 500,
            rawData: json,
            msg
          }
        }
      }
      if (json.length === configLine) {
        console.log(json)
        throw new Error(`没有数据，请确保EXCEL模板【${orderTypeMap[orderType]}】sheet填写了数据！`)
      }
      if (json.length < configLine) {
        console.log(json)
        throw new Error('不能更改EXCEL表头！')
      }
    } catch (error) {
      ctx.body = {}
      if (error && error.message) {
        ctx.body.msg = error.message
      }
      ctx.body.code = 500
    }
  }
  ctx.status = 200
}
