import fetch from 'node-fetch'
import * as path from 'path'
import { Type } from '@poseidon/token/dist/defination'
import { readSheet } from "@boss/excel"
import { createSalesOrderContract, createGbbOrderContract } from '@boss/pdf'
import api from '../config/api'
import * as shortid from 'shortid'
const { execFileSync } = require('child_process')
const FormData = require('form-data')

const fs = require('fs')
const id = require('@boss/id')
const moment = require('moment');

const pdfPath = path.join(__dirname, '../../assets', 'pdf', 'output.pdf')

enum ContractType {
  draft = 0,
  formal,
  sketch
}

interface PDFResultType {
  path: string;
  name: string;
}

function isNull(val: any) {
  if (val === '' || val === undefined || val === null) {
    return true;
  }
  return false;
}

async function deliveryDateApi(ctx: any, orderData) {
  const { salesOrganization, receiverProvinceCode, receiverCityCode, receiverDistrictCode, customerNo, serviceCenterSelfTransport, orderContact, receiverContact, receiptTimeCategory, specifiedReceiptDayOfWeek, autoBatching, orderType,otherLabelReq, fastenerLabelReq, labelPasteWay, hideLogo, fastenerDetect, packagingReq, fastenerSpecialPackageReq, deliveryOtherNote, bidCustomer, dnIncidentalWay, acceptSupplierDelivery } = orderData
  let skuDemandQtyList = orderData.items.map(item => ({ qty: item.quantity, sku: item.materiel, uuid: item.uuid }))
  const data = {
    customer: customerNo,
    salesOrganization,
    skuDemandQtyList: skuDemandQtyList.map(item => ({ qty: item.qty, sku: item.sku })),
    demandProvinceCode: receiverProvinceCode,
    demandCityCode: receiverCityCode,
    demandDistrictCode: receiverDistrictCode,
    serviceCenterSelfTransport,
    orderContactId: orderContact,
    receiptContactId: receiverContact,
    receiptTimeCategory,
    specifiedReceiptDayOfWeek,
    otherLabelReq: Array.isArray(otherLabelReq) ? otherLabelReq.join(',') : otherLabelReq,
    fastenerLabelReq: Array.isArray(fastenerLabelReq) ? fastenerLabelReq.join(',') : fastenerLabelReq,
    labelPasteWay: Array.isArray(labelPasteWay) ? labelPasteWay.join(',') : labelPasteWay,
    hideLogo,
    fastenerDetect,
    packagingReq: Array.isArray(packagingReq) ? packagingReq.join(',') : packagingReq,
    fastenerSpecialPackageReq,
    deliveryOtherNote,
    bidCustomer,
    dnIncidentalWay,
    acceptSupplierDelivery,
    orderType
  }
  const options = {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
      Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
    },
    body: JSON.stringify(data)
  }
  try {
    const response = await fetch(`${api.OPC}/v4/oms/order/calculateWaveDeliveryDate?autoBatching=${autoBatching}`, options)
    const res = await response.json()
    if (res && res.code === 200 && res.data) {
      const { data: resData } = res;
      resData.forEach((resItem) => {
        const sourceItem = skuDemandQtyList.find(source => source.sku === resItem.material && source.qty === resItem.quantity)
        skuDemandQtyList = skuDemandQtyList.filter((item) => item !== sourceItem);
        const index = orderData.items.findIndex(item => item.uuid === sourceItem.uuid)
        orderData.items[index].deliveryDate = resItem.skuArrivalDate
      })
    }
    return orderData
  } catch (err) {
    console.log(err)
  }
}

async function createPdf(ctx: any, type: ContractType) {
  const { no, status = '2' } = ctx.request.query
  let url;
  let method;
  switch (type) {
    // 老草稿查外围单详情
    case ContractType.draft:
      url = `${api.OPC}/v4/oms/sketchOrder/detail?orderNo=${no}`;
      method = 'POST';
      break;
    // 新草稿查草稿单详情
    case ContractType.sketch:
      url = `${api.OPC}/v2/sketch/detail?sketchOrderNo=${no}`;
      method = 'POST';
      break;
    case ContractType.formal:
      url = `${api.OPC}/v1/so/template/so/detail?omsNo=${no}`;
      method = 'GET';
  }
  try {
    if (no && url && method) {
      const optons = {
        headers: {
          Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
        }
      }
      const response = await fetch(url, {
          ...optons,
          method,
        }
      )
      const res = await response.json()
      if (res && res.code === 200) {
        let { data: orderData } = res
        if (type === ContractType.draft) {
          const items = (orderData?.items || []).map(item => ({ ...item, sapMaterialName: item.materielDescribe || item.sapMaterialName || '', uuid: shortid.generate()}));
          orderData = {
            ...orderData, 
            items,
            sapOrderNo: '',
            orderCreateTime: '',
          }
          orderData = await deliveryDateApi(ctx, orderData)
        }
        if (orderData) {
          const {
            customerNo, customerName, salesOrganization, productGroup,
            distributionChannel, orderSource, isTax, orderBasis, items
          } = orderData
          const queryPriceData = {
            isTax,
            orderBasis,
            items: items.map((sku) => ({
              discountAmount: sku.discountAmount || 0,
              itemNo: sku.soItemNo || sku.orderItemNo,
              promotionalDiscountRate: isNull(sku.promotionalDiscountRate)
                ? 100
                : sku.promotionalDiscountRate,
              quantity: sku.quantity || 0,
              sku: sku.skuNo,
              taxRate: sku.taxRate || 0,
              unitPrice: isTax === '1' ? sku.taxPrice : sku.freeTaxPrice,
            }))
          }
          const [tycResponse, cusResponse, priceResponse] = await Promise.all([
            fetch(`${api.THIRD_PARTY_SERVICE}/tianyancha/baseInfoV4?name=${customerName}&source=GBB`, { ...optons }),
            fetch(
              `${api.OPC}/v1/so/template/customer?customerNumber=${customerNo}&salesOrganization=${salesOrganization}&productGroup=${productGroup}&distributionChannel=${distributionChannel}`,
              {
                ...optons,
                method: 'GET'
              }
            ),
            fetch(`${api.OPC}/v1/so/calculatePrice`, {
              ...optons,
              headers: {
                ...optons.headers,
                'Content-Type': 'application/json',
              },
              method: 'POST',
              body: JSON.stringify(queryPriceData)
            })
          ])
          const [tycRes, customerRes, priceRes] = await Promise.all([tycResponse.json(), cusResponse.json(), priceResponse.json()]);
          if (priceRes && priceRes.code === 200 && priceRes.data) {
            orderData.priceData = priceRes.data
            const priceItems = priceRes.data?.priceItems || [];
            orderData.items = orderData.items.map(item => {
              let foundItem = priceItems.find(sku => String(sku.itemNo) === String(item.soItemNo) || String(sku.itemNo) === String(item.orderItemNo)) || {};
              return {
                ...item,
                ...foundItem,
              }
            })
          } else {
            orderData.priceData = {}
          }
          let tycData = null;
          if (tycRes?.code === '0000') {
            tycData = tycRes?.result;
          }
          if (customerRes && customerRes.code === 200) {
            const { data: customerData } = customerRes
            if (customerData) {
              let pdfData;
              if (orderSource === 'GBB') {
                pdfData = await createGbbOrderContract(pdfPath, orderData, customerData, tycData, status) as PDFResultType;
              } else {
                pdfData = await createSalesOrderContract(pdfPath, orderData, customerData, status) as PDFResultType;
              }
              return pdfData;
            }
          }
        }
      }
    }
  } catch (ex) {
    console.log(ex);
  }
}
async function exportPDF(ctx: any, type: ContractType) {
  try {
    const pdfData = await createPdf(ctx, type)
    if (pdfData) {
      const { path, name }: PDFResultType = pdfData
      const { status = '2' } = ctx.request.query
      if (path && name) {
        if (status === '3') {
          const newPath = path.replace(/.pdf$/, '.docx')
          const formatCommands = `convert ${path} ${newPath}`;
          execFileSync('pdf2docx', formatCommands.split(' '));
          const stream = fs.createReadStream(newPath)
          ctx.body = stream
          ctx.set('Content-type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
          ctx.response.attachment(`${name}.docx`)
        } else {
          const stream = fs.createReadStream(path)
          ctx.body = stream
          ctx.set('Content-type', 'application/pdf')
          ctx.response.attachment(`${name}.pdf`)
        }
      }
    }
  } catch (ex) {
    console.log(ex);
  }
  ctx.status = 200
}

export const exportDraftPdf = async (ctx: any) => {
  await exportPDF(ctx, ContractType.draft)
}
export const exportSketchPdf = async (ctx: any) => {
  await exportPDF(ctx, ContractType.sketch)
}

export const exportFormalPdf = async (ctx: any) => {
  await exportPDF(ctx, ContractType.formal)
}
  // customerMaterialName:客户物料名称
  // customerMaterialNo  客户物料号
  // customerSpecificationModel  客户规格型号
  // customerOrderNo 客户行号
  // customerReferenceNo 客户订单号

/** 导出合同并上传附件
 * /internal-api/so/uploadPdf?status=2&no=Z0012411180818086328&uploadNo=1500015611
 * @param {*} params {  }
 */
export const uploadPdf = async (ctx: any) => {
  try {
    const pdfData = await createPdf(ctx, ContractType.formal)
    const { uploadNo } = ctx.request.query
    if (pdfData) {
      const { path, name }: PDFResultType = pdfData
      if (path && name) {
        const file = fs.createReadStream(path)
        const formData = new FormData();
        formData.append('appName', 'ecorp-uat');
        formData.append('file', file, `${name}.pdf`);
        const options = {
          method: 'POST',
          headers: {
            ...formData.getHeaders(),
            Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
          },
          body: formData
        }
        const res = await fetch(`${api.ALI_FILE}/ali/upload/`, options)
        const resData = await res.json()
        if (resData) {
          const docMetaDataList = [{
            bucketName: resData[0].bucketName,
            fileName: name,
            ossKey: resData[0].objectKey,
            // upUserName: window.CUR_DATA.user && window.CUR_DATA.user.name,
            attachmentType: 'general',
            printDirection: 'order',
            uploadTime: moment(new Date()).format('yyyy-MM-DD HH:mm:ss')
          }]
          const data =  {
            source: 'BOSS',
            dimension: 'order',
            docUploadScene: 'existOrder',
            businessId: uploadNo,
            docMetaDataList
          }
          const uploadOptions = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json;charset=utf-8',
              Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
            },
            body: JSON.stringify(data)
            
          }
          const uploadRes = await fetch(`${api.OPC}/v1/doc/saveDocumentMetaData`, uploadOptions);
          const uploadData = await uploadRes.json();
          console.log('----res', uploadData)
          if (uploadData.code !== 200) {
            ctx.body = {
              code: 500,
              msg: uploadData.msg || '请求失败'
            } 
          }
        }
      }
    } else {
      ctx.body = {
        code: 500,
        msg: '导出合同失败'
      } 
    }
  } catch (ex) {
    console.log(ex);
  }
  ctx.status = 200
}
export const uploadExcel = async (ctx: any) => {
  const { orderType, company, refuseSDD, ignoreDate, hasCreateList } = ctx.request.query
  const files = ctx.request.files
  if (files && files.file && files.file.path) {
    try {
      const json = readSheet(fs.readFileSync(files.file.path), {})
      if (json && json.length > 2) {
        if (json.length > 502) {
          ctx.status = 200
          const msg = '当前模板仅支持500行，请重新调整后再上传'
          ctx.body = {
            code: 500,
            msg
          }
        } else {
          const attrs: string[] = json[0] as string[]
          let itemList = []
          const dateReg = /^\d{4}\/\d{1,2}\/\d{1,2}/ /* 2022/02/01 */
          const errorList = []
          for (let i = 2; i < json.length; i++) {
            if (json[i] && json[i].length > 0) {
              const data = {
                company,
                orderType,
                refuseSDD,
                orderNo: id()
              }
              if (ignoreDate) {
                data['ignoreDate'] = true
              }
              for (let j = 0; j < json[i].length; j++) {
                if (json[i][j] != null) {
                  if (attrs[j] === 'directDeliverySupplier') {
                    const directDeliverySupplier = (json[i][j]).toString()
                    data[attrs[j]] = directDeliverySupplier
                  } else if (attrs[j] === 'customerDate' || attrs[j] === 'customerReferenceDate') {
                    if (typeof(json[i][j]) === 'number' && (json[i][j] < 1e5)) {
                      const stamp = parseInt(json[i][j], 10)
                      data[attrs[j]] = moment('1900-01-01').add(stamp - 2, 'days').format('YYYY-MM-DD')
                      continue
                    }
                    if (!dateReg.test(json[i][j])) {
                      errorList.push(`第${i + 1}行 ${ attrs[j] === 'customerDate' ? '客户期望日期' : '客户参考日期' } 格式错误！`)
                    }
                    data[attrs[j]] = moment(json[i][j]).format('YYYY-MM-DD')
                    console.log(data[attrs[j]], json[i][j])
                  } else if(attrs[j] === 'customerMaterialName' ||
                            attrs[j] === 'customerMaterialNo' ||
                            attrs[j] === 'customerSpecificationModel' ||
                            attrs[j] === 'customerOrderNo' ||
                            attrs[j] === 'customerReferenceNo') {
                    const str = json[i][j]
                    data[attrs[j]] = str ? str.toString().replace(/(?:\r\n|\r|\n)/g, '') : ''
                  }
                  else {
                    data[attrs[j]] = json[i][j]
                  }
                }
              }
              itemList.push(data)
            }
          }
          if (errorList.length > 0) {
            ctx.body = {
              code: 500,
              msg: errorList.join(';\n')
            }
            return
          }
          if (itemList.length > 0) {
            if (ignoreDate && hasCreateList) {
              const list = hasCreateList.split(',')
              console.log(ignoreDate, list)
              itemList.forEach(item => {
                if (!list.some(i => i == item.orderIndex)) {
                  item.hasCreate = true
                }
              })
            }
            const optons = {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json;charset=utf-8',
                Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
              },
              body: JSON.stringify(itemList)
            }
            const response = await fetch(`${api.OPC}/v4/oms/order/batchCreate`, optons)
            const res = await response.json()
            ctx.body = {
              ...res,
              rawData: itemList
            }
          } else {
            const msg = '导入数据为空！'
            ctx.body = {
              code: 500,
              msg
            }
          }
        }
      }
    } catch (ex) {
      console.log(ex)
      ctx.body = {
        code: 500,
        msg: (ex && ex.message) ? ex.message : '解析excel出错'
      }
    }
  }
  ctx.status = 200
}
