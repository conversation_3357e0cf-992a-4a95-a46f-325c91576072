/**
 * 跟单报表controller
 * 跟单报表
 */
import fetch from 'node-fetch'
import { readSheet } from '@boss/excel'
import { Type } from '@poseidon/token/dist/defination'
const fs = require('fs')
import { safeRun } from '../util/index'
import api from '../config/api'

const propsMap = {
  '累计跟单文本': 'accumulateRemark',
  '备注文本': 'remark',
  '交货完成标识': 'isPlanDeliveryDone',
  '审批状态': 'approveStep',
  '品牌名称': 'brandName',
  '合作模式': 'coopMode',
  '订单创建日期': 'createDate',
  '创建用户': 'createUser',
  '客服': 'customerService',
  '发货日期': 'deliveryDate',
  // '发货日期': 'deliveryDateList',
  // '送货单已发数量': 'deliveryOrderQtyList',
  '送货单已发数量': 'deliveryOrderQuantity',
  '发货状态': 'deliveryOrderStatus',
  // '发货状态': 'deliveryOrderStatusList',
  '发货状态描述': 'deliveryOrderStatusText',
  '送货车牌号': 'deliveryVehicleNumber',
  // '送货车牌号': 'deliveryVehicleNumberList',
  '发货方式': 'deliveryWay',
  // '发货方式': 'deliveryWayList',
  '交期天数差异': 'diffLeadDays',
  '商品行折扣': 'discountAmount',
  '外向交货单号': 'dnNo',
  '司机姓名': 'driverName',
  // '司机姓名': 'driverNameList',
  '司机联系方式': 'driverPhone',
  // '司机联系方式': 'driverPhoneList',
  '预计到货天数': 'exceptedLeadDays',
  '工厂': 'factoryCode',
  '首次承诺日期': 'firstDeliveryDate',
  '税率': 'inputTax',
  '是否删除': 'isDeleted',
  '已开票金额': 'invoiceAmount',
  '已开票数量': 'invoiceQuantity',
  '订单交期确认标志': 'isConfirmed',
  '客户接受供应商交期': 'isCustomerAccept',
  '是否已签收': 'isDeliveryOrderSigned',
  // '是否已签收': 'isDeliveryOrderSignedList',
  '是否代管代发': 'isEscrow',
  '是否开通VC': 'isOpenVc',
  'VC标识': 'openVcStatus',
  '急单标识': 'isUrgent',
  '采购订单项目行': 'itemNo',
  '商品行数量': 'itemQty',
  '最新承诺日期': 'lastDeliveryDate',
  '交货进度，百分制': 'leadProcessRate',
  '剩余交货天数': 'leftLeadDays',
  '红绿灯状态': 'lightStatus',
  '物流单号': 'logisticsCode',
  // '物流单号': 'logisticsCodeList',
  '物流公司名称': 'logisticsName',
  // '物流公司名称': 'logisticsNameList',
  '物料描述': 'materialDescription',
  '物料组名称': 'materialGroupName',
  '物料组编号': 'materialGroupNum',
  '订单备注': 'orderRemark',
  '订单类型': 'orderType',
  '付款条件代码': 'paymentTermCode',
  '计划交货日期': 'planDeliveryDate',
  '计划行数量': 'planDeliveryQty',
  '采购订单计划行': 'planNo',
  '采购订单号': 'poNo',
  '打印备注': 'printRemark',
  '商品经理': 'productManager',
  '产品定位': 'productPosition',
  '承诺变更次数': 'promiseUpdateFrequency',
  '采购组': 'purchaseGroup',
  '请求交货日期': 'requestDeliveryDate',
  '退货原因': 'returnReason',
  '商品行运费': 'shippingAmount',
  'SKU编码': 'skuNo',
  'SO需求交期': 'soDeliveryDate',
  '交付主管': 'soDeliveryManager',
  'SO发货状态': 'soDeliveryStatus',
  '关联的销售单行号': 'soItemNo',
  '销售订单数量': 'soItemQty',
  '关联销售单号': 'soNo',
  '销售姓名': 'soSellerName',
  '签单返回': 'soSigningBack',
  '标准交货日期': 'standardDeliveryDate',
  '行标准交期 （下单时）': 'standardLeadDays',
  '转储已发货数量': 'stoDnClearedQty',
  '转储在途数量': 'stoDnOnWayQty',
  '外向交货单过账日期': 'stoDnPostDate',
  '转储已制单数量': 'stoDnQty',
  '转储已制单未发货': 'stoDnUnclearedQty',
  '转储未制单数量': 'stoNotDnQty',
  '供应商物料号': 'supplierMaterialNo',
  '供应商名称': 'supplierName',
  '供应商代码': 'supplierNo',
  '供应商订单号': 'supplierOrderNo',
  // '标签': 'tag',
  '税额': 'taxTotalAmount',
  '含税单价': 'taxedPrice',
  '行含税总额': 'taxedTotalAmount',
  '跟踪单号': 'trackNo',
  '订单单位': 'unit',
  '未税单价': 'untaxedPrice',
  '行未税总额': 'untaxedTotalAmount',
  '预警状态': 'warningType',
  '供应商接受签单返回': 'isSigningBackAccepted',
  '客户已收货': 'isCustomerReceived',
  '直发已送达': 'isCustomerReceived',
  '直发签单能否回传': 'isSigningBackAccepted',
  '申请下单原因': 'orderReason',
  '跟单分类': 'overdueReasonFirst',
  '跟单分类明细': 'overdueReasonSecond',
  '指定渠道打标': 'designatedChannel',
  '无法确认订单原因': 'poUnconfirmedReason',
  '交期变更原因': 'deliveryChangeReason',
  '是否现货': 'isSpot'
}

function getFactoryList(companyFactoryList) {
  const factoryList = []
  if (companyFactoryList && companyFactoryList.length > 0) {
    companyFactoryList.forEach(item => {
      if (item.factoryList && item.factoryList.length > 0) {
        item.factoryList.forEach(fac => {
          factoryList.push({
            value: fac.factoryCode,
            name: fac.factoryName
          })
        })
      }
    })
  }
  return factoryList
}

const fetchList = async (prop: string, ctx) => {
  let token = ''
  safeRun(() => {
    token = ctx.state.token[Type.security].accessToken
  })
  if (!token) return Promise.resolve([])
  let apiUrl = `${api.MM}`
  switch(prop) {
    case 'dictList':
      apiUrl += '/config/dictionary/list' ;break;
    case 'warehouseList':
      apiUrl += '/config/factoryAndWarehouse/list' ;break;
    case 'companyFactoryList':
      apiUrl += '/config/companyAndFactory/list/' ;break;
    case 'purchaseList':
      apiUrl += '/config/purchaseGroup/all' ;break;
  }
  const options = {
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
      Authorization: `bearer ${token}`
    },
  }
  const response = await fetch(apiUrl, options)
  const res = await response.json()
  if (res.code === 0) {
    return res.data
  } else {
    console.log('res:', res)
  }
  return []
}
const cache = {
  dictList: [],
  warehouseList: [],
  companyFactoryList: [],
  purchaseList: [],
}
const getAllDictList = async (ctx: any) => {
  const tasks = [];
  if (!cache.dictList.length) {
    tasks.push(fetchList('dictList', ctx).then(res => {
      if (res && Array.isArray(res) && res.length) cache.dictList = res
    }))
  }
  if (!cache.warehouseList.length) {
    tasks.push(fetchList('warehouseList', ctx).then(res => {
      if (res && Array.isArray(res) && res.length) cache.warehouseList = res
    }))
  }
  if (!cache.companyFactoryList.length) {
    tasks.push(fetchList('companyFactoryList', ctx).then(res => {
      if (res && Array.isArray(res) && res.length) cache.companyFactoryList = res
    }))
  }
  if (!cache.purchaseList.length) {
    tasks.push(fetchList('purchaseList', ctx).then(res => {
      if (res && Array.isArray(res) && res.length) cache.purchaseList = res
    }))
  }

  return Promise.all(tasks)
}

function parseTag(name) {
  let _name = name
  try {
    _name = parseInt(name)
    if (isNaN(_name)) throw new Error(_name)
  } catch (err) {
    if (/(\d+)\s/.test(name) && RegExp.$1) {
      _name = parseInt(RegExp.$1)
    }
  }
  return _name
}
function mapNameToValue (title, name) {
  // '采购订单号', '采购订单项目行', '采购订单计划行', '标签', '急单标识', '客户接受供应商交期', '订单交期确认标志', '计划交货日期', '交货完成标识', '备注文本', '是否删除'
  let ret = name
  const boolList = [
    '急单标识', '客户接受供应商交期', '订单交期确认标志', '是否删除', '交货完成标识', '供应商接受签单返回', '客户已收货', '直发签单能否回传', '是否现货'
  ]
  if (boolList.includes(title)) {
    ret = null
    if (/Y/.test(name)) { ret = 1 }
    if (/N/.test(name)) { ret = 0 }
  }

  if (title === '标签') {
    ret = parseTag(name)
    // let options = buildOptions('poFollowReportTag')
    // if (Array.isArray(options)) {
    //   // eslint-disable-next-line eqeqeq
    //   const item = options.find(item => item.name == name)
    //   if (item) {
    //     ret = item.value
    //   }
    // }
  }
  return ret
}

function findOptions (dicList, prop) {
  return dicList.filter(item => item.type === prop) || []
}
function buildOptions (prop) {
  let dictList = []
  let warehouseList = []
  let companyFactoryList = []
  let purchaseList = []
  
  safeRun(() => {
    dictList = cache['dictList']
    warehouseList = cache['warehouseList']
    companyFactoryList = cache['companyFactoryList']
    purchaseList = cache['purchaseList']
  })

  let mapList = []
  safeRun(() => {
    mapList = findOptions(dictList, prop) || []
    if (/warehouseLocation/gi.test(prop)) {
      mapList = warehouseList.map(item => ({
        value: item.warehouseLocationCode,
        name: item.warehouseLocationName
      }))
    }
    if (/factoryCode/gi.test(prop)) {
      mapList = getFactoryList(companyFactoryList)
    }
    if (/factoryCode2/gi.test(prop)) {
      mapList = companyFactoryList.reduce((prev, next) => {
        if (Array.isArray(next.factoryList)) {
          const tmpList = next.factoryList.map(item => ({ value: item.factoryCode, name: item.factoryName }))
          prev.push(...tmpList)
        }
        return prev
      }, [])
    }
    if (/purchaseGroup/gi.test(prop)) {
      mapList = purchaseList.map(item => ({
        name: item.userName,
        value: item.groupCode
      }))
    }
    mapList = delDuplicateProp(mapList, 'value')
  })
  return mapList
}

function delDuplicateProp(arr, prop) {
  let tmp = {}
  let ret = []
  for (let item of arr) {
    if (!tmp[item[prop]]) {
      ret.push(item)
      tmp[item[prop]] = true
    }
  }
  return ret
}

function parseExcelDate(data, prop, title) {
  if (title === '计划交货日期' && !data[prop].length && Number(data[prop]) === data[prop]) {
    try {
      const tmpDate = new Date(1900, 0, data[prop] - 1).getTime()
      data[prop] = new Date(tmpDate + 8 * 3600 * 1000).toISOString().split('T')[0]
    } catch (err) {
      console.log(err)
    }
  }
}

function mapValue2Data(data, title, value) {
  const titleList = [
    '采购订单号', '采购订单项目行', '采购订单计划行', '标签', '计划交货日期', '订单交期确认标志', '急单标识', '客户接受供应商交期',
    '供应商接受签单返回', '直发已送达', '备注文本', '交货完成标识', '是否删除',  '客户已收货', '直发签单能否回传',
    '申请下单原因', '无法确认订单原因', '交期变更原因', '指定渠道打标', '是否现货'
  ]
  if (!titleList.includes(title)) return
  const prop = propsMap[title]
  if (prop != null) {
    data[prop] = mapNameToValue(title, value)
    parseExcelDate(data, prop, title)
  }
}

function tryTrim(str) {
  return str && str.trim && str.trim() || str
}

export const batchEditTrackingOrder = async (ctx: any) => {
  const { type= 0 } = ctx.request.query
  await getAllDictList(ctx)
  const files = ctx.request.files
  const configLine = 2
  const indexStart = 0
  if (files && files.file && files.file.path) {
    try {
      const json = readSheet(fs.readFileSync(files.file.path), {})
      if (!json) {
        throw new Error('没有数据！')
      }
      if (json && json.length > configLine) {
        const titles: string[] = json[0] as string[]
        const itemList = []
        const updateUser = (ctx.state.token[Type.security] && ctx.state.token[Type.security].user) ?
          ctx.state.token[Type.security].user.name : ''

        for (let i = configLine; i < json.length; i++) {
          if (json[i] && json[i].length > 0) {
            const data = {}
            for (let j = indexStart; j < json[i].length; j++) {
              if (json[i][j] != null) {
                mapValue2Data(data, titles[j], json[i][j])
              }
              if (titles[j] === '采购订单号' && !json[i][j]) {
                console.log(json)
                throw new Error(`第${i + 1}行采购订单号必填！`)
              }
              if (titles[j] === '采购订单项目行' && !json[i][j]) {
                console.log(json)
                throw new Error(`第${i + 1}行采购订单项目行必填！`)
              }
              if (titles[j] === '采购订单计划行' && !json[i][j]) {
                console.log(json)
                throw new Error(`第${i + 1}行采购订单计划行必填！`)
              }
            }
            itemList.push(data)
          }
        }
        if (itemList.length > 0) {
          const data = {
            updateUser,
            rows: itemList,
            updateByExcel: true,
            type
          }
          data.rows = data.rows.filter(item => {
            if (item.isDeleted) return false
            if (item.isPlanDeliveryDone) return false
            return item
          })
          // 没有数据的字段填充null
          const submitProps = [
            'poNo', 'itemNo', 'planNo', 'planDeliveryDate', 'isConfirmed', 'isUrgent', 'isCustomerAccept', 'remark', 'isSigningBackAccepted', 'isCustomerReceived', 'orderReason','poUnconfirmedReason', 'deliveryChangeReason', 'designatedChannel', 'isSpot'
          ]
          data.rows.forEach(item => {
            delete item.isDeleted
            delete item.isPlanDeliveryDone
            if (item.remark != null) {
              item.remark = item.remark + ''
            }
            submitProps.forEach(prop => {
              if (item[prop] === undefined) {
                item[prop] = null
              } else if (prop === 'poUnconfirmedReason' || prop === 'deliveryChangeReason') {
                let option = buildOptions(prop).filter(reason => reason.name === item[prop])
                item[prop] = option && option.length > 0 ? option[0].value : null
              } else if(prop === 'designatedChannel') {
                if(item[prop] === '清空') item[prop] = 0
                else {
                  let option = buildOptions('orderChannel').filter(reason => reason.name === tryTrim(item[prop]))
                  item[prop] = option && option.length > 0 ? Number(option[0].value) : null
                }
              }
            })
          })
          const options = {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json;charset=utf-8',
              Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`
            },
            body: JSON.stringify(data)
          }
          try {
            const url = '/api/v1/report/batchFollowPO'
            const response = await fetch(`${api.MM}${url}`, options)
            const res = await response.json()
            console.log('batchFollowPO:', {
              ...res,
              rawData: data
            })
            ctx.body = {
              ...res,
              rawData: data
            }
          } catch (err) {
            ctx.body = {
              code: 500,
              rawData: json,
              msg: err
            }
          }
        } else {
          const msg = '导入数据为空！'
          ctx.body = {
            code: 500,
            rawData: json,
            msg
          }
        }
      }
      if (json.length === configLine) {
        console.log(json)
        throw new Error('没有数据！')
      }
      if (json.length < configLine) {
        console.log(json)
        throw new Error('不能更改EXCEL表头！')
      }
    } catch (error) {
      ctx.body = {}
      if (error && error.message) {
        ctx.body.msg = error.message
      }
      ctx.body.code = 500
    }
  }
  ctx.status = 200
}
