import * as env from '@server/env'
import { Type } from '@poseidon/token/dist/defination'

export const index = async ctx => {
  ctx.set({
    'cache-control': 'no-cache',
    expires: 0
  })
  // get browser resource info, which in /static
  const info = ctx.helper.getStaticInfo('boss')
  // get rendered string
  const html = await ctx.render(
    'iframe-bridge',
    {
      ...info,
      user: ctx.state.token[Type.security].user,
      env: env.LOCAL ? 'local' : env.DEV ? 'fat' : env.QA ? 'uat' : 'pro'
    },
    false
  )
  ctx.body = html
}
