import fetch from "node-fetch";
import * as path from "path";
import * as env from "@server/env";
import { readSheet } from "@boss/excel";
import { Type } from "@poseidon/token/dist/defination";
import api from "../config/api";

const mseEnv = env.RELEASE || env.PRERELEASE ? "pro" : "dev";
const instance = require("@boss/mse").MSE.getInstance(mseEnv);
const fs = require("fs");
const padStart = require("lodash/padStart");
const XLSX = require("xlsx");
let dictList = []
function getValueFromName(name: string | number, prop: string) {
  let options = [];
  switch (prop) {
    case "isFixPrice":
      options = [
        { name: "是", value: 1 },
        { name: "否", value: 0 },
      ];
      break;
    case "stockMode":
      options = dictList.filter(dict=> dict.type === 'stockMode') || [];
      break;
  }
  const find = options.find((option) => option.name == name);
  if (find) return find.value;
  return name;
}
function formatDate(date: number | string): string {
  let newDate = ''
  try {
    const tmpDate = new Date(1900, 0, parseInt(date + '') - 1).getTime()
    newDate = new Date(tmpDate + 8 * 3600 * 1000).toISOString().split('T')[0]
  } catch (err) {
    console.log(err)
  }
  return newDate;
}
// 批量创建订单
export const uploadExcel = async (ctx: any) => {
  const { createUser } = ctx.request.query;
  const files = ctx.request.files;
  const configLine = 4;
  if (!dictList.length) {
    try {
      const dictListOptions = {
        method: "GET",
        headers: {
          Authorization: `bearer ${ctx.state.token[Type.security].accessToken}`,
        },
      };
      let response = await fetch(
        `${api.MM}/config/dictionary/list`,
        dictListOptions
      ).then(res => res.json())
      dictList = response.data;
    } catch (err) {
      ctx.body = {};
      if (err && err.message) {
        ctx.body.msg = err.message;
      }
      ctx.body.code = 500;
      return
    }
  }
  if (files && files.file && files.file.path) {
    try {
      const json = readSheet(fs.readFileSync(files.file.path), {});
      if (json && json.length > configLine) {
        const attrs: string[] = json[0] as string[];
        const createUser =
          ctx.state.token[Type.security] && ctx.state.token[Type.security].user
            ? ctx.state.token[Type.security].user.name
            : "";
        const itemList = [];
        for (let i = configLine; i < json.length; i++) {
          if (json[i] && json[i].length > 0) {
            const data = {};
            for (let j = 1; j < json[i].length; j++) {
              if (json[i][j] != null) {
                if (attrs[j] === "isFixPrice") {
                  data[attrs[j]] = getValueFromName(json[i][j], "isFixPrice");
                }
                else if (/date/gmi.test(attrs[j])) {
                  data[attrs[j]] = formatDate(json[i][j]);
                }
                else if (attrs[j] === "stockMode") {
                  data[attrs[j]] = getValueFromName(json[i][j], "stockMode");
                } 
                else if (attrs[j] === "inputTax") {
                  data[attrs[j]] = json[i][j].split(/\s+/)[0];
                } else {
                  data[attrs[j]] = json[i][j];
                }
              }
              if (attrs[j] === "group" && !json[i][j]) {
                throw new Error("识别号码必须必填！");
              }
            }
            itemList.push(data);
          }
        }
        const groupData = {}
        itemList.forEach((item, idx) => {
          const group = item.group
          if (!groupData[group]) {
            groupData[group] = []
          }
          groupData[group].push(item)
          item.lineNumber = idx + configLine + 1
          delete item.group
        })
        const body = []
        for (let group in groupData) {
          const tmp = {
            createUser,
            identifier: group,
            itemList: groupData[group]
          }
          body.push(tmp)
        }
        const options = {
          method: "POST",
          headers: {
            "Content-Type": "application/json;charset=utf-8",
            Authorization: `bearer ${
              ctx.state.token[Type.security].accessToken
            }`,
          },
          body: JSON.stringify(body),
        };
        try {
          const url = "/pa/batchInput";
          const response = await fetch(`${api.MM}${url}`, options);
          const res = await response.json();
          ctx.body = {
            ...res,
            rawData: body,
          };
        } catch (ex) {
          ctx.body = {
            code: 500,
            rawData: json,
            msg: ex,
          };
        }
      } else {
        const msg = "导入数据为空！";
        ctx.body = {
          code: 500,
          rawData: json,
          msg,
        };
      }
    } catch (error) {
      ctx.body = {};
      if (error && error.message) {
        ctx.body.msg = error.message;
      }
      ctx.body.code = 500;
    }
  }
  ctx.status = 200;
};
