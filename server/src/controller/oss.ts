import * as env from '@server/env';
const mseEnv = env.RELEASE || env.PRERELEASE ? 'pro' : 'dev'
const acmInstance = require('@boss/mse').MSE.getInstance(mseEnv);
const { STS } = require("ali-oss");

const roleArn = 'acs:ram::1602027139684780:role/sts-zkh-boss'

export interface STSCredentialsType {
  credentials: STSResultType;
}
export interface STSResultType {
  SecurityToken: string;
  AccessKeyId: string;
  AccessKeySecret: string;
  Expiration: string;
}

export const sts = async (res: any) => {
  const response = await acmInstance.getConfig('sts-oss.config')
  const { accessKeyId, accessKeySecret } = response
  const sts = new STS({ accessKeyId, accessKeySecret });
  console.log('env', (process.env.ENV || '').toLocaleLowerCase(), env)
  await sts
    .assumeRole(roleArn, "", 3600, "poster")
    .then((result: STSCredentialsType) => {
      res.response.body = {
        AccessKeyId: result.credentials.AccessKeyId,
        AccessKeySecret: result.credentials.AccessKeySecret,
        SecurityToken: result.credentials.SecurityToken,
        Expiration: result.credentials.Expiration,
      }
      res.response.status = 200
    })
    .catch((err: unknown) => {
      console.log(err);
      res.status = 400
    });
}