import fetch from 'node-fetch'
import * as env from '@server/env'
import api from '../config/api'

export const getShortUrl = async (ctx) => {
  const { params } = ctx;
  const md5 = params.id ? params.id.trim() : '';
  if (md5) {
    try {
      const response = await fetch(`${api.API_FE_SERVER}/api-short-url/${md5}`)
      const res = await response.json()
      if (res.data) {
        ctx.redirect(res.data);
        return;
      }
    } catch (ex) {
      console.log(ex);
    }
  }
  const msg = '数据为空！'
  ctx.body = {
    code: 500,
    msg
  }
};

export const setShortUrl = async (ctx) => {
  const { url } = ctx.request.body
  const _url = url ? url.trim() : '';
  if (_url) {
    try {
      const options = {
        method: 'POST',
        body: JSON.stringify({ url: _url })
      }
      const response = await fetch(`${api.API_FE_SERVER}/api-short-url/`, options)
      const res = await response.json()
      if (res.data) {
        const host = `https://boss${env.RELEASE || env.PRERELEASE ? '' : '-uat'}.zkh360.com`
        ctx.body = {
          url: `${host}/s/${res.data}`,
          code: 200,
          msg: 'success'
        }
        return;
      }
    } catch (ex) {
      console.log(ex);
    }
  }
  const msg = '数据为空！'
  ctx.body = {
    code: 500,
    msg
  }
};