const trans = require('@boss/translate');
const mail = require('@boss/email');
import { client } from '@boss/redis'

function sendMail(emailList, url, keyID) {
  mail.sendMailWithUrl(emailList, url).then(res => {
    console.log(`发送邮件${emailList}成功`)
    client.then(redis => {
      if (redis) {
        redis.del(keyID, function(err, obj) {
          console.log(`delete ${keyID}`)
        });
      }
    })
  }).catch(console.error);
}

function sendFailMail(emailList, content, keyID) {
  mail.sendMailWithText(emailList, content).then(res => {
    console.log(`发送错误邮件${emailList}成功`)
    client.then(redis => {
      if (redis) {
        redis.del(keyID, function(err, obj) {
          console.log(`delete ${keyID}`)
        });
      }
    })
  }).catch(console.error);
}

function getErrMsg(code, msg) {
  return `
    <div>${msg}</div>
    <div>${trans.error[code]}</div>
  `
}

export const countFile = async (ctx: any) => {
  const files = ctx.request.files
  const { from, to, email } = ctx.request.query
  if (!from || !to) {
    ctx.body = {
      code: 500,
      msg: '翻译语言必须填写！'
    }
  } else if (!email) {
    ctx.body = {
      code: 500,
      msg: '邮箱必须填写！'
    }
  } else if (files && files.file && files.file.path && from && to) {
    try {
      const res = await trans.countFileAsync(files.file, from, to)
      if (res && res.data) {
        const { error_code } = res.data
        if (error_code === '52000') {
          const { data } = res
          ctx.body = {
            ...data
          }
        } else {
          const { error_code = 500, error_msg = 'error' } = res.data
          ctx.body = {
            code: error_code,
            msg: error_msg
          }
        }
      }
    } catch (ex) {
      ctx.body = ex
      ctx.body.code = 500
    }
  }
}

export const translateFile = async (ctx: any) => {
  const files = ctx.request.files
  const { from, to, email } = ctx.request.query
  if (!from || !to) {
    ctx.body = {
      code: 500,
      msg: '翻译语言必须填写！'
    }
  } else if (!email) {
    ctx.body = {
      code: 500,
      msg: '邮箱必须填写！'
    }
  } else if (files && files.file && files.file.path && from && to) {
    try {
      const res = await trans.translateFileAsync(files.file, from, to)
      if (res && res.data) {
        const { data } = res.data
        console.log('translateFileAsync', data)
        if (data && data.requestId) {
          const keyID = `fe_requestId_${data.requestId}`
          client.then(redis => {
            if (redis) {
              redis.hmget(keyID, ['fileSrcUrl', 'error_msg'], function(err, url) {
                console.log(err, data.requestId, keyID, url)
                if (email) {
                  const emailList = email.split('-')
                  if (url && url[1]) {
                    sendFailMail(emailList, url[1], keyID)
                  } else if (url && url[0]) {
                    sendMail(emailList, url[0], keyID)
                  } else {
                    redis.hmset(keyID, { email }, function(err, res) {
                      console.log(err, res)
                    })
                  }
                } else {
                  console.log(`${data.requestId}:邮箱地址为空！`)
                }
              });
            }
          })
          ctx.body = {
            ...data
          }
        } else {
          const { error_code = 500, error_msg = 'error' } = res.data
          ctx.body = {
            code: error_code,
            msg: error_msg
          }
        }
      } else {
        ctx.body = {
          code: 500,
          msg: '翻译出错！'
        }
      }
    } catch (ex) {
      ctx.body = ex
      ctx.body.code = 500
    }
  }
  ctx.status = 200
}

export const translateCallback = async (ctx: any) => {
  const { error_code, error_msg, requestId, fileSrcUrl } = ctx.request.query
  console.log(new Date(), 'translateCallback in', error_code, error_msg, requestId, fileSrcUrl)
  const keyID = `fe_requestId_${requestId}`
  client.then(redis => {
    if (redis) {
      redis.hmget(keyID, ['email'], function(err, obj) {
        const email = obj ? obj[0] : ''
        if (email) {
          const emailList = email.split('-')
          if (error_code === '52000') {
            sendMail(emailList, fileSrcUrl, keyID);
          } else {
            const translateErrMsg = getErrMsg(error_code, error_msg)
            sendFailMail(emailList, translateErrMsg, keyID)
          }
        } else {
          if (error_code === '52000') {
            redis.hmset(keyID, { fileSrcUrl })
          } else {
            redis.hmset(keyID, { error_msg: getErrMsg(error_code, error_msg) })
          }
        }
      })
    }
  })
  ctx.status = 200
}