import { readSheet } from '@boss/excel'
const fs = require('fs')

// 标签系统手动打标
export const uploadTagManualExcel = async (ctx: any) => {
  const files = ctx.request.files
  if (files && files.file && files.file.path) {
    const line = 1
    const { status, result } = parseExcel(files.file.path, line, 100000)
    if (status) {
      try {
        ctx.body = {
          res: status,
          data: result.length - 1
        }
      } catch (ex) {
        ctx.body = ex
        ctx.body.code = 500
      }
    } else {
      ctx.body = {
        msg: result,
        code: 413,
      }
    }
  }
  ctx.status = 200
}

function parseExcel(path, confLen, maxLen) {
  let status = false
  let result = null
  try {
    const json = readSheet(fs.readFileSync(path), {})
    if (json && json.length > confLen) {
      if (json.length > maxLen + confLen) {
        result = `数据量过大，上传失败`
      } else {
        status = true
        result = json.filter(item => item && item.length > 0)
      }
    } else {
      result = '没有数据！'
    }
  } catch (ex) {
    result = ex
  }
  return {
    status, result
  }
}