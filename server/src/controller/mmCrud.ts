import { Context } from 'koa'
import { safeRun } from '../util/index'
import { DmsUtils } from '../util/initDms'
import * as env from '@server/env'
import API from '../config/api'
const mseEnv = env.RELEASE || env.PRERELEASE ? 'pro' : 'dev'
const acmInstance = require('@boss/mse').MSE.getInstance(mseEnv)
const SqlString = require('sqlstring')

const type = env.LOCAL || env.DEV ? 'dev' : env.QA ? 'qa' : 'pro'
const dbUrl = API.BOSS_DMS
const dbName = type === 'pro' ? 'boss_fe' : 'boss-service'
console.log(`type: ${type}, connect url: ${dbUrl}`)

const config = {
  connectionLimit: 10,
  user: '',
  host: dbUrl,
  token: '',
  key: '',
  database: '',
}
const dmsConfigId = `boss-dms-${type}.config`
const invTableMap = {
  '20': 'fe_boss_inv_mgt_20',
  '21': 'fe_boss_inv_mgt_20',
  '22': 'fe_boss_inv_mgt_20',
  '18': 'fe_boss_inv_mgt_20',
  '19': 'fe_boss_inv_mgt_20',
  '28': 'fe_boss_inv_mgt_20',
  '30': 'fe_boss_inv_mgt_20',
  '31': 'fe_boss_inv_mgt_20',
  '13': 'fe_boss_inv_mgt_20',
  '29': 'fe_boss_inv_mgt_20',
  '27': 'fe_boss_inv_mgt_20',
  '23': 'fe_boss_inv_mgt_20',
  'list': 'fe_boss_inv_mgt_list'
}
const poTableMap = {
  'Z001': 'fe_boss_po_z001',
  'Z002': 'fe_boss_po_z002',
  'Z003': 'fe_boss_po_z003',
  'Z004': 'fe_boss_po_z004',
  'Z005': 'fe_boss_po_z005',
  'Z006': 'fe_boss_po_z006',
  'Z007': 'fe_boss_po_z007',
  'Z008': 'fe_boss_po_z008',
  'Z009': 'fe_boss_po_z009',
  'Z010': 'fe_boss_po_z010',
  'Z011': 'fe_boss_po_z011',
  'Z012': 'fe_boss_po_z012',
  'Z013': 'fe_boss_po_z013',
  'Z014': 'fe_boss_po_z014',
  'Z015': 'fe_boss_po_z015'
}
const poDetailTableMap = {
  'Z001': 'fe_boss_po_z001_detail',
  'Z002': 'fe_boss_po_z002_detail',
  'Z003': 'fe_boss_po_z003_detail',
  'Z004': 'fe_boss_po_z004_detail',
  'Z005': 'fe_boss_po_z005_detail',
  'Z006': 'fe_boss_po_z006_detail',
  'Z007': 'fe_boss_po_z007_detail',
  'Z008': 'fe_boss_po_z008_detail',
  'Z009': 'fe_boss_po_z009_detail',
  'Z010': 'fe_boss_po_z010_detail',
  'Z011': 'fe_boss_po_z011_detail',
  'Z012': 'fe_boss_po_z012_detail',
  'Z013': 'fe_boss_po_z013_detail',
  'Z014': 'fe_boss_po_z014_detail',
  'Z015': 'fe_boss_po_z015_detail'
}
const paTable = 'fe_boss_stock_agreement'

function insertObject (tableName, data) {
  let query = `INSERT INTO \`${dbName}\`.\`${tableName}\``;
  const values = []
  if(Array.isArray(data)){
    data.forEach((item,index) => {
      delete item.id
      const props = []
      const currentValues = []
      if(index === 0){
        for (let prop in item) if (prop !== 'id') props.push(prop)
        query = query +  `(${props.join(',')}) VALUES`
      }
      for (let prop in item) { values.push(item[prop]);currentValues.push(item[prop]) }
      query = query +  `(${currentValues.slice().fill('?').join(',')}),`
    })
    query = query.substring(0, query.length - 1)
  }else{
    delete data.id
    const props = []
    for (let prop in data) if (prop !== 'id') props.push(prop)
    query = query +  `(${props.join(',')}) VALUES`
    for (let prop in data) { values.push(data[prop]) }
    query = query +  `(${values.slice().fill('?').join(',')})`
  }

  return {
    query,
    data: values
  }
}
function updateObject (tableName, data) {
  const { id } = data
  delete data.id
  let query = `UPDATE \`${dbName}\`.\`${tableName}\` ` + ' SET ';
  const props = []
  for (let prop in data) {
    props.push(prop)
    query = query +  `\`${prop}\`=?,`
  }
  const values = []
  for (let prop in data) { values.push(data[prop]) }
  if (query[query.length - 1] === ',') {
    query = query.slice(0, -1)
  }
  query = query + ` WHERE \`id\` = ${id};`
  return {
    query,
    data: values
  }
}
function deleteById (tableName, data) {
  return {
    query: `DELETE FROM \`${dbName}\`.\`${tableName}\` WHERE ID = ?`,
    data: [data.id]
  }
}
function buildQuery (queryObj) {
  const { query, data } = queryObj
  // let index = 0;
  // const queryString = query.replace(/\?/g, (a,b,c) => data[index++]);
  const queryString = SqlString.format(query, data)
  return queryString
}
// INSERT INTO `fe_boss_po_z013_detail` VALUES (1, '采购单号', 'orderNo', 'input', 120, 6, 'baseInfo', 0, NULL, 0, 2, 'detail');
const sqlManager = {
  add: (tableName, data) => buildQuery(insertObject(tableName, data)),
  delete : (tableName, data) => buildQuery(deleteById(tableName, data)),
  update: (tableName, data) => buildQuery(updateObject(tableName, data)),
};

export const updateInvFields = async (ctx: Context) => {
  let body = (ctx.request as any).body
  safeRun(() => {
    body = JSON.parse(body)
  })
  const { orderType, type: crudType, data: submitData } = body || {}
  // orderType: 18-23-list, type: add|update|delete, data: {}
  let data, message, code = 0
  let resBody = { code, data, message }
  if (!orderType) {
    return ctx.body = { code: 400, message: 'param orderType needed!' }
  }
  if (!invTableMap[orderType as string]) {
    return ctx.body = { code: 400, message: 'not suppoer this orderType' }
  }
  if (!sqlManager[crudType as string]) {
    return ctx.body = { code: 400, message: 'crudType needed!' }
  }
  if ((crudType === 'update' || crudType === 'delete') && !submitData.id) {
    return ctx.body = { code: 400, message: 'id needed!' }
  }
  try {
    let response = await acmInstance.getConfig(dmsConfigId)
    safeRun(() => {
      response = JSON.parse(response)
    })
    const { token, key, database, user } = response
    config.token = token
    config.key = key
    config.database = database
    config.user = user
    const dmsInstance = DmsUtils.getInstance('boss-admin', config)
    const tableName = invTableMap[orderType]
    const queryString = sqlManager[crudType as string](tableName, submitData)
    console.log(queryString)
    const res = await dmsInstance.query(queryString)
    resBody.data = res
  } catch (err) {
    console.log('updateInvFields DmsUtils.getInstance and dmsInstance.query:', err)
    resBody.message = err.message || 'internal server error'
    resBody.code = 500
    if (err.code === 'ER_NO_SUCH_TABLE') {
      resBody.message = '不支持这种订单类型！'
    }
  }
  ctx.body = resBody
}
export const updatePoFields = async (ctx: Context) => {
  let body = (ctx.request as any).body
  safeRun(() => {
    body = JSON.parse(body)
  })
  const { routerType, orderType, type: crudType, data: submitData } = body || {}
  // routerType: create|detail, orderType: Z001-13, type: add|update|delete, data: {}
  // console.log(routerType, orderType, crudType, submitData)
  let tableMap = poTableMap
  if (routerType === 'detail') {
    tableMap = poDetailTableMap
  }
  let data, message, code = 0
  let resBody = { code, data, message }
  if (!orderType) {
    return ctx.body = { code: 400, message: 'orderType needed!' }
  }
  if (!tableMap[orderType as string]) {
    return ctx.body = { code: 400, message: 'not suppoer this orderType' }
  }
  if (!sqlManager[crudType as string]) {
    return ctx.body = { code: 400, message: 'crudType needed!' }
  }
  if ((crudType === 'update' || crudType === 'delete') && !submitData.id) {
    return ctx.body = { code: 400, message: 'id needed!' }
  }
  try {
    let response = await acmInstance.getConfig(dmsConfigId)
    safeRun(() => {
      response = JSON.parse(response)
    })
    const { token, key, database, user } = response
    config.token = token
    config.key = key
    config.database = database
    config.user = user
    const dmsInstance = DmsUtils.getInstance('boss-admin', config)
    const tableName = tableMap[orderType]
    const queryString = sqlManager[crudType as string](tableName, submitData)
    console.log(queryString)
    const res = await dmsInstance.query(queryString)
    resBody.data = res
  } catch (err) {
    console.log('updatePoFields DmsUtils.getInstance and dmsInstance.query:', err)
    resBody.message = err.message || 'internal server error'
    resBody.code = 500
    if (err.code === 'ER_NO_SUCH_TABLE') {
      resBody.message = '不支持这种订单类型！'
    }
  }
  ctx.body = resBody
}
export const updateStockAgreementFields = async (ctx: Context) => {
  console.log('body', ctx)
  let body = (ctx.request as any).body
  safeRun(() => {
    body = JSON.parse(body)
  })
  const { type: crudType, data: submitData } = body || {}
  // orderType: 18-23-list, type: add|update|delete, data: {}
  let data, message, code = 0
  let resBody = { code, data, message }
  if (!sqlManager[crudType as string]) {
    return ctx.body = { code: 400, message: 'crudType needed!' }
  }
  if ((crudType === 'update' || crudType === 'delete') && !submitData.id) {
    return ctx.body = { code: 400, message: 'id needed!' }
  }
  try {
    let response = await acmInstance.getConfig(dmsConfigId)
    safeRun(() => {
      response = JSON.parse(response)
    })
    const { token, key, database, user } = response
    config.token = token
    config.key = key
    config.database = database
    config.user = user
    const dmsInstance = DmsUtils.getInstance('boss-admin', config)
    const tableName = paTable
    const queryString = sqlManager[crudType as string](tableName, submitData)
    console.log(queryString)
    const res = await dmsInstance.query(queryString)
    resBody.data = res
  } catch (err) {
    console.log('updatePaFields DmsUtils.getInstance and dmsInstance.query:', err)
    resBody.message = err.message || 'internal server error'
    resBody.code = 500
    if (err.code === 'ER_NO_SUCH_TABLE') {
      resBody.message = '不支持这种订单类型！'
    }
  }
  ctx.body = resBody
}
