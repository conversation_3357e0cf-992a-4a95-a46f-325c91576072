import Core from '@server/core-common'
import * as token from '@poseidon/token'
import { proxy } from '@server/proxy'
import * as body from 'koa-body'
import * as Router from 'koa-router'
import API from './config/api'
import { Context } from 'koa'
import * as env from '@server/env'
import { loadRouter } from './util'
const { middleware } = require('@boss/mse')

const { Type } = token.defination
const { auth, authorize, resources } = token.middleware
const mseEnv = env.RELEASE || env.PRERELEASE ? 'pro' : 'dev'
const showProxyDebug = env.RELEASE ? false : true

const MAX_BODY = '20mb'
/**
 * override route
 * @param router Koa Router instance
 * @param app  Koa instance
 */
export = (router, app: Core) => {
  // home
  router.get('/', auth(Type.security), authorize(Type.security), app.controllers.home.index)
  // 短链
  router.get('/s/:id', app.controllers.shortUrl.getShortUrl)
  router.post('/s/', body({ multipart: true }), app.controllers.shortUrl.setShortUrl)
  // 子应用登录回调
  router.get('/login/callback', auth(Type.security), authorize(Type.security), app.controllers.home.loginCallback)
  router.get('/internal-api/so/exportFormalPdf', auth(Type.security), authorize(Type.security), app.controllers.sales.exportFormalPdf)
  router.get('/internal-api/so/exportDraftPdf', auth(Type.security), authorize(Type.security), app.controllers.sales.exportDraftPdf)
  router.get('/internal-api/so/exportSketchPdf', auth(Type.security), authorize(Type.security), app.controllers.sales.exportSketchPdf)
  router.get('/internal-api/so/uploadPdf', auth(Type.security), authorize(Type.security), app.controllers.sales.uploadPdf)
  router.post('/bossApi/uploadExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.sales.uploadExcel)
  router.get('/bossApi/wecom/send', app.controllers.weCom.send)
  router.post('/internal-api/wecom/send', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.weCom.sendMsg)
  router.get('/internal-api/mm/getInvFields', auth(Type.security), authorize(Type.security), app.controllers.mm.getInvFields)
  router.get('/internal-api/mm/getPoFields', auth(Type.security), authorize(Type.security), app.controllers.mm.getPoFields)
  router.get('/internal-api/mm/getSaFields', auth(Type.security), authorize(Type.security), app.controllers.mm.getStockAgreementFields)
  router.post('/internal-api/mm/updateInvFields', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.mmCrud.updateInvFields)
  router.post('/internal-api/mm/updateSaFields', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.mmCrud.updateStockAgreementFields)
  router.post('/internal-api/mm/updatePoFields', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.mmCrud.updatePoFields)
  router.post('/internal-api/mm/uploadExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.purchase.uploadExcel)
  router.post('/internal-api/mm-pa/uploadExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.pa.uploadExcel)
  router.post('/internal-api/mm/batchEditByExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.purchase.batchEditByExcel)
  router.post('/internal-api/mm/updateStockReason', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.purchase.updateStockReason)
  router.post('/internal-api/mm/uploadPendingReasonExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.purchase.uploadPendingReasonExcel)

  router.post('/internal-api/mm/uploadInvExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.inventory.uploadExcel)
  router.post('/internal-api/mm/batchEditTrackingOrder', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.trackingOrder.batchEditTrackingOrder)

  router.get('/internal-api/mm/exportPaPdf', auth(Type.security), authorize(Type.security), app.controllers.purchase.exportPaPdf)
  router.get('/internal-api/mm/exportPdf', auth(Type.security), authorize(Type.security), app.controllers.purchase.exportPdf)
  router.post('/internal-api/tools/countFile', auth(Type.security), body({ multipart: true }), app.controllers.tools.countFile)
  router.post('/internal-api/tools/translateFile', auth(Type.security), body({ multipart: true }), app.controllers.tools.translateFile)
  router.get('/internal-api/tools/translateCallback', app.controllers.tools.translateCallback)
  router.post('/internal-api/tg/uploadTagManualExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.tg.uploadTagManualExcel)
  router.post('/internal-api/mrp/uploadOemExcel', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.mrp.uploadEomExcel)
  router.get('/internal-api/pool/sts', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.pool.sts)

  router.get('/api-oss/sts', auth(Type.security), authorize(Type.security), body({ multipart: true }), app.controllers.oss.sts)

  router.get('/logout', token.controller.logout(Type.security))

  const proxies = [
    {
      path: '/price-approval',
      host: API.PRICE_APPROVAL,
      rewrite: ''
    },
    {
      path: '/api',
      host: API.ATP,
      rewrite: '/v1/atp'
    },
    {
      path: '/documentApi',
      host: API.ECORP,
      rewrite: ''
    },
    {
      path: '/honeycomb',
      host: API.HONEYCOMB,
      rewrite: ''
    },
    {
      path: '/token-base',
      host: API.ECORP,
      rewrite: ''
    },
    {
      path: '/token-new',
      host: API.OMS_SEARCH_FRONT,
      rewrite: ''
    },
    {
      path: '/oms-base',
      host: API.OMS,
      rewrite: ''
    },
    {
      path: '/api-oms-opc',
      host: API.OMS_OPC,
      rewrite: ''
    },
    {
      path: '/oms-new',
      host: API.OMS_SEARCH_FRONT,
      rewrite: ''
    },
    {
      path: '/oms-sd',
      host: API.OMS_SD,
      rewrite: ''
    },
    {
      path: '/oms-opc-front',
      host: API.OMS_SEARCH_OPC_FRONT,
      rewrite: ''
    },
    {
      path: '/api-opc',
      host: API.OPC,
      rewrite: ''
    },
    {
      path: '/api-opc-gray',
      host: API.OPC_GRAY,
      rewrite: ''
    },
    {
      path: '/api-opc-csc',
      host: API.OPC_CSC,
      rewrite: ''
    },
    {
      path: '/api-ddc',
      host: API.OPC_DDC,
      rewrite: ''
    },
    {
      path: '/oms-config',
      host: API.OMS_CONFIG,
      rewrite: ''
    },
    {
      path: '/oms-so-query',
      host: API.OMS_SO_QUERY,
      rewrite: ''
    },
    {
      path: '/api-dnc',
      host: API.DNC,
      rewrite: ''
    },
    {
      path: '/reconciliation',
      host: API.RECONCILIATION,
      rewrite: ''
    },
    {
      path: '/data-center',
      host: API.DATA_CENTER,
      rewrite: ''
    },
    {
      path: '/data-center-front',
      host: API.DATA_CENTER_FRONT,
      rewrite: ''
    },
    {
      path: '/api-boss',
      host: API.API_BOSS,
      rewrite: ''
    },
    {
      path: '/api-boss-opc',
      host: API.API_BOSS_OPC,
      rewrite: ''
    },
    {
      path: '/api-opc-atp',
      host: API.API_OPC_ATP,
      rewrite: ''
    },
    {
      path: '/api-boss-supply-channel',
      host: API.API_BOSS_SUPPLY_CHANNEL,
      rewrite: ''
    },
    {
      path: '/api-boss-kunhe',
      host: API.API_BOSS_KUNHE,
      rewrite: ''
    },
    {
      path: '/api-boss-product',
      host: API.API_BOSS_PRODUCT,
      rewrite: ''
    },
    {
      path: '/api-build',
      host: API.API_BUILD,
      rewrite: ''
    },
    {
      path: '/data-control',
      host: API.DATA_CENTER_CONTROL,
      rewrite: ''
    },
    {
      path: '/api-mdm',
      host: API.MDM,
      rewrite: ''
    },
    {
      path: '/api-platform',
      host: API.PLATFORM,
      rewrite: ''
    },
    {
      path: '/dmp-api',
      host: API.DMP_API,
      rewrite: ''
    },
    {
      path: '/dmp-price',
      host: API.DMP_PRICE,
      rewrite: ''
    },
    {
      path: '/dmp-daemon',
      host: API.DMP_DAEMON,
      rewrite: ''
    },
    {
      path: '/api-workflow',
      host: API.HERMES,
      rewrite: ''
    },
    {
      path: '/api-vc',
      host: API.VC,
      rewrite: ''
    },
    {
      path: '/api-vc-boss',
      host: API.VC_BOSS,
      rewrite: ''
    },
    {
      path: '/api-vc-supplier',
      host: API.VC_SUPPLIER,
      rewrite: ''
    },
    {
      path: '/api-atp-realtime',
      host: API.ATP_REALTIME,
      rewrite: ''
    },
    {
      path: '/api-sales',
      host: API.SALES,
      rewrite: ''
    },
    {
      path: '/api-kunhe-mdm',
      host: API.API_KUNHE_MDM,
      rewrite: ''
    },
    {
      path: '/fe-upload',
      host: API.FE_UPLOAD,
      rewrite: ''
    },
    {
      path: '/api-fe-server',
      host: API.API_FE_SERVER,
      rewrite: ''
    },
    {
      path: '/api-mis',
      host: API.API_MIS,
      rewrite: ''
    },
    {
      path: '/api-financial',
      host: API.FINANCIAL,
      rewrite: ''
    },
    {
      path: '/gbb-fn',
      host: API.GBB_FINANCIAL,
      rewrite: '',
      headers: {
        origin: 'https://admin.gongbangbang.com'
      }
    },
    {
      path: '/api-mm',
      host: API.MM,
      rewrite: ''
    },
    {
      path: '/api-logistics',
      host: API.LOGISTICS,
      rewrite: ''
    },
    {
      path: '/api-rulecenter',
      host: API.RULE_CENTER,
      rewrite: ''
    },
    {
      path: '/api-weapp',
      host: API.WEAPP,
      rewrite: ''
    },
    {
      path: '/api-mrp',
      host: API.MRP,
      rewrite: ''
    },
    {
      path: '/api-ab',
      host: API.AB,
      rewrite: ''
    },
    {
      path: '/api-pms-system',
      host: API.PMS_SYSTEM,
      rewrite: ''
    },
    {
      path: '/api-kunhe-dms',
      host: API.KH_DMS,
      rewrite: ''
    },
    {
      path: '/api-kunhe-oms',
      host: API.KH_OMS_WEB,
      rewrite: ''
    },
    {
      path: '/api-kunhe-worker',
      host: API.KH_DMS_WORKER,
      rewrite: ''
    },
    {
      path: '/api-pms',
      host: API.PMS,
      rewrite: ''
    },
    {
      path: '/api-sim',
      host: API.SIM,
      rewrite: ''
    },
    {
      path: '/api-pms-report',
      host: API.PMS_REPORT,
      rewrite: ''
    },
    {
      path: '/api-pms-order',
      host: API.PMS_ORDER,
      rewrite: ''
    },
    {
      path: '/api-msg',
      host: API.API_MSG,
      rewrite: ''
    },
    {
      path: '/api-kunhe-assistant',
      host: API.API_KUNHE_ASSISTANT,
      rewrite: ''
    },
    {
      path: '/api-ifc',
      host: API.API_IFC,
      rewrite: ''
    },
    {
      path: '/api-ifc-pre',
      host: API.API_IFC_PRE,
      rewrite: ''
    },
    {
      path: '/api-area',
      host: API.API_AREA,
      rewrite: ''
    },
    {
      path: '/api-securtity',
      host: API.SECURITY,
      rewrite: ''
    },
    {
      path: '/api-customerCenter',
      host: API.API_CUSTOMERCENTER,
      rewrite: ''
    },
    {
      path: '/api-customerManage',
      host: API.API_CUSTOMERMANGE,
      rewrite: ''
    },
    {
      path: '/api-blackHole',
      host: API.API_BLACKHOLE,
      rewrite: ''
    },
    {
      path: '/api-customerDelivery',
      host: API.API_CUSTOMERDELIVERY,
      rewrite: ''
    },
    {
      path: '/tag-center-data',
      host: API.API_TAGCENTER_DATA,
      rewrite: ''
    },
    {
      path: '/tag-center-marking',
      host: API.API_TAGCENTER_MARKING,
      rewrite: ''
    },
    {
      path: '/tag-center-query',
      host: API.API_TAGCENTER_QUERY,
      rewrite: ''
    },
    {
      path: '/api-cc-gateway',
      host: API.API_CC_GATEWAY,
      rewrite: ''
    },
    {
      path: '/api-cc-item',
      host: API.API_CC_ITEM,
      rewrite: ''
    },
    {
      path: '/api-cc-standard',
      host: API.API_CC_STANDARD,
      rewrite: ''
    },
    {
      path: '/api-cc-query',
      host: API.API_CC_QUERY,
      rewrite: ''
    },
    {
      path: '/api-cc-migrate',
      host: API.API_CC_MIGRATE,
      rewrite: ''
    },
    {
      path: '/api-tag-data',
      host: API.API_TAG_DATA,
      rewrite: ''
    },
    {
      path: '/api-tag-query',
      host: API.API_TAG_QUERY,
      rewrite: ''
    },
    {
      path: '/api-tag-marking',
      host: API.API_TAG_MARKING,
      rewrite: ''
    },
    {
      path: '/api-tag-rule',
      host: API.API_TAG_RULE,
      rewrite: ''
    },
    {
      path: '/api-oms-supply-channel',
      host: API.API_OMS_SUPPLY_CHANNEL,
      rewrite: ''
    },
    {
      path: '/api-sdi',
      host: API.API_SDI,
      rewrite: ''
    },
    {
      path: '/api-gbb-admin',
      host: API.GBB_ADMIN,
      rewrite: ''
    },
    {
      path: '/api-invoice',
      host: API.API_INVOICE,
      rewrite: ''
    },
    {
      path: '/api-chatbot',
      host: API.CHATBOT,
      rewrite: ''
    },
    {
      path: '/api-customer-product',
      host: API.API_CUSTOMER_PRODUCT,
      rewrite: ''
    },
    {
      path: '/api-chatbot',
      host: API.CHATBOT,
      rewrite: ''
    },
    {
      path: '/api-process-center',
      host: API.PROCESS_CENTER,
      rewrite: ''
    },
    {
      path: '/api-event-center',
      host: API.EVENT_CENTER,
      rewrite: ''
    },
    {
      path: '/api-form-center',
      host: API.FORM_CENTER,
      rewrite: '',
    },
    {
      path: '/api-vc-order',
      host: API.API_VC_ORDER,
      rewrite: '',
    },
    {
      path: '/api-qts',
      host: API.API_QTS,
      rewrite: ''
    },
    {
      path: '/api-rqf',
      host: API.API_RQF,
      rewrite: ''
    },
    {
      path: '/api-debug',
      host: API.API_DBG,
      rewrite: ''
    },
    {
      path: '/api-vc-download',
      host: API.API_VC_DOWNLOAD,
      rewrite: '',
    },
    {
      path: '/api-sap',
      host: API.API_SAP,
      rewrite: '',
    },
    {
      path: '/oms-apijson',
      host: API.OMS_API_JSON,
      rewrite: '',
    },
    {
      path: '/api-dmp-price',
      host: API.PRICE_TOOL,
      rewrite: ''
    },
    {
      path: '/api-security-service',
      host: API.SECURITY,
      rewrite: ''
    },
  ]
  router.all(
    '/api-boss/udesk/*',
    body(),
    proxy({
      debug: showProxyDebug,
      host: API.API_BOSS,
      rewrite: {
        '^/api-boss': ''
      },
      extend: (option, ctx: Context) => {
        // option: request option
      }
    })
  )
  router.all(
    '/api-acm-config',
    app.middlewares.routeKors.index,
    auth(Type.security),
    authorize(Type.security),
    body(),
    middleware('koa', mseEnv)
  )
  router.all(
    '/api-mse-config',
    app.middlewares.routeKors.index,
    auth(Type.security),
    authorize(Type.security),
    body(),
    middleware('koa', mseEnv)
  )
  proxies.forEach(item => {
    let routepath = `${item.path}(/.*)?`
    router.all(
      routepath,
      body({
        multipart: true,
        formLimit: MAX_BODY,
        textLimit: MAX_BODY,
        jsonLimit: MAX_BODY
      }),
      auth(Type.security),
      proxy({
        debug: showProxyDebug,
        host: item.host,
        rewrite: {
          [`^${item.path}`]: item.rewrite
        },
        timeout: 1000 * 60 * 10,
        extend: (option, ctx: Context) => {
          // option: request option
          const token = ctx.state.token[Type.security].accessToken ? `bearer ${ctx.state.token[Type.security].accessToken}` : ctx.request.header?.authorization

          option.headers.Authorization = token || ''
          if (item.headers && Object.keys(item.headers).length) {
            for (let header in item.headers) {
              option.headers[header] = item.headers[header]
            }
          }
        }
      })
    )
  })

  router.all(
    '/ali-upload',
    body({ multipart: true }),
    auth(Type.security),
    proxy({
      debug: showProxyDebug,
      host: API.ALI_FILE,
      type: 'stream',
      rewrite: {
        [`^/ali-upload`]: '/ali/upload/'
      },
      extend: (option, ctx: Context) => {
        // option: request option
        option.headers.Authorization = `bearer ${ctx.state.token[Type.security].accessToken}`
      }
    })
  )
  router.all(
    '/api-agent/*',
    body({ multipart: true }),
    // auth(Type.security),
    proxy({
      debug: showProxyDebug,
      host: API.API_AGENT,
      rewrite: {
        '^/api-agent': ''
      },
    })
  )
  router.all(
    '/security-api/*',
    body(),
    auth(Type.security),
    proxy({
      debug: showProxyDebug,
      host: API.SECURITY,
      rewrite: {
        '^/security-api': ''
      },
      extend: (option, ctx: Context) => {
        // option: request option
        option.headers.Authorization = `bearer ${ctx.state.token[Type.security].accessToken}`
      }
    })
  )
  router.all(
    '/api-security/*',
    auth(Type.security),
    body(), // post 提交的时候使用
    proxy({
      debug: showProxyDebug,
      host: API.TECH_SHARE,
      rewrite: {
        '^/api-security': ''
      },
      extend: (option, ctx: Context) => {
        // option: request option
        option.headers.Authorization = `bearer ${ctx.state.token[Type.security].accessToken}`
      }
    })
  )

  router.get('/internal-api/user', auth(Type.security), app.controllers.api.roles)
  router.get('/health_check', ctx => (ctx.body = 'ok'))
  router.get('/unauthorized', async ctx => ctx.render('unauthorized'))
  router.get('/call-center', app.controllers.callCenter.index)
  router.get('/iframe-bridge', app.controllers.iframeBridge.index)
  router.get('/data-center-control', app.controllers.dataCenterControl.index)
  const prefixRouter = new Router({ prefix: '/api' })
  loadRouter(prefixRouter, app)
  app.use(prefixRouter.routes())
  app.use(prefixRouter.allowedMethods())
}

