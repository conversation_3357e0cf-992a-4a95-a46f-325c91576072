export const index = async (ctx, next) => {
  const { method } = ctx
  const origin = ctx.get('Origin')
  ctx.set('Access-Control-Allow-Origin', origin)
  ctx.set('Access-Control-Allow-Methods', 'GET,HEAD,POST')
  if (method === 'OPTIONS') {
    ctx.status = 204
    ctx.body = ''
  } else if(method === 'GET' || method === 'POST' || method === 'HEAD'){
    await next()
  } else {
    ctx.status = 405
    ctx.body = 'Method Not Allowed'
  }
}