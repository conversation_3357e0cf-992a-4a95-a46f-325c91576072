import Core from '@server/core-common'
import * as proxy from 'koa-proxies'
import { initWebProxies } from './util'
import { IncomingMessage, ServerResponse } from 'http';

const env = (process.env.ENV || '').toLocaleLowerCase();

export const setProxies = (app: Core) => {

  initWebProxies(app)  
  app.use(proxy('/ui', {
    target: 'https://kun-ui.zkh360.com',
    changeOrigin: true,
    rewrite: path => path.replace('/ui', ''),
    logs: true
  }))

  let soTarget = '', workflowTarget = '', mmTarget ='', asTarget = '', stockTarget = '', techTarget = '',
    fnTarget = '', fnReactTarget = '', arTarget = '', ikTarget = '';  let tgTarget = '';
  let ixTarget = '';
  let peTarget = '';
  let prTarget = '';
  let workOrderTarget = '';
  let soGrayTarget = '';
  let aiHelperTarget = '';
  let zhsTarget = '';
  const env = (process.env.ENV || '').toLocaleLowerCase();

  if (/pro|pre|release|prelease/gim.test(env)) {
    fnTarget = 'http://boss-fn-zkh360-com.zkh-boss:8080'
    fnReactTarget = 'http://boss-fn-react-zkh360-com.zkh-boss:8080'
    soTarget = 'http://boss-so-zkh360-com.zkh-boss:8080'
    soGrayTarget = 'http://boss-so-gray-zkh360-com.zkh-boss:8080'
    workflowTarget = 'http://boss-wf-zkh360-com.zkh-boss:8080'
    workOrderTarget = 'http://boss-wo-zkh360-com.zkh-boss:8080'
    mmTarget = 'http://boss-mm-zkh360-com.zkh-boss:8080'
    asTarget = 'http://boss-as-zkh360-com.zkh-boss:8080'
    stockTarget = 'http://boss-stock-zkh360-com.zkh-boss:8080'
    techTarget = 'http://boss-tech-zkh360-com.zkh-boss:8080'
    tgTarget = 'http://boss-tg-zkh360-com.zkh-boss:8080'
    ixTarget = 'http://db-index-zkh360-com.zkh-fe:8080'
    peTarget = 'http://boss-pe-zkh360-com.zkh-boss:8080'
    prTarget = 'http://boss-pr-zkh360-com.zkh-boss:8080'
    arTarget = 'http://boss-ar-zkh360-com.zkh-boss:8080'
    ikTarget = 'http://boss-ik-zkh360-com.zkh-boss:8080'
    aiHelperTarget = 'http://fe-ai-helper-zkh-com.zkh-boss:8080'
    zhsTarget = 'http://zhs-fe-admin-zkh360-com.zkh-boss:8080'
  } else if (env === 'uat') {
    fnTarget = 'http://boss-fn-uat-zkh360-com.zkh-boss:8080'
    fnReactTarget = 'http://boss-fn-react-uat-zkh360-com.zkh-boss:8080'
    soTarget = 'http://boss-so-uat-zkh360-com.zkh-boss:8080'
    soGrayTarget = 'http://boss-so-gray-uat-zkh360-com.zkh-boss:8080'
    workflowTarget = 'http://boss-wf-uat-zkh360-com.zkh-boss:8080'
    workOrderTarget = 'http://boss-wo-uat-zkh360-com.zkh-boss:8080'
    mmTarget = 'http://boss-mm-uat-zkh360-com.zkh-boss:8080'
    asTarget = 'http://boss-as-uat-zkh360-com.zkh-boss:8080'
    stockTarget = 'http://boss-stock-uat-zkh360-com.zkh-boss:8080'
    techTarget = 'http://boss-tech-uat-zkh360-com.zkh-boss:8080'
    tgTarget = 'http://boss-tg-uat-zkh360-com.zkh-boss:8080'
    ixTarget = 'http://db-index-uat-zkh360-com.zkh-fe:8080'
    peTarget = 'http://boss-pe-uat-zkh360-com.zkh-boss:8080'
    prTarget = 'http://boss-pr-uat-zkh360-com.zkh-boss:8080'
    arTarget = 'http://boss-ar-uat-zkh360-com.zkh-boss:8080' 
    ikTarget = 'http://boss-ik-uat-zkh360-com.zkh-boss:8080'
    aiHelperTarget = 'http://fe-ai-helper-uat-zkh-com.zkh-boss:8080'
    zhsTarget = 'http://zhs-fe-admin-uat-zkh360-com.zkh-boss:8080'
  } else {
    fnTarget = 'https://boss-fn-uat.zkh360.com'
    fnReactTarget = 'https://boss-fn-react-uat.zkh360.com'
    soTarget = 'https://so-uat.zkh360.com'
    soGrayTarget = 'https://so-gray-uat.zkh360.com'
    workflowTarget = 'https://wf-uat.zkh360.com'
    workOrderTarget = 'https://wo-uat.zkh360.com'
    mmTarget = 'https://boss-mm-uat.zkh360.com'
    asTarget = 'https://boss-as-uat.zkh360.com'
    stockTarget = 'https://stock-uat.zkh360.com'
    techTarget = 'https://boss-tech-uat.zkh360.com'
    tgTarget = 'https://boss-tg-uat.zkh360.com'
    ixTarget = 'https://db-index-uat.zkh360.com'
    peTarget = 'https://boss-pe-uat.zkh360.com'
    prTarget = 'https://boss-pr-uat.zkh360.com'
    arTarget = 'https://ar-uat.zkh360.com'
    ikTarget = 'https://boss-ik-uat.zkh360.com'
    aiHelperTarget = 'https://fe-ai-helper-uat.zkh360.com'
    zhsTarget = 'https://zhs-fe-admin-uat.zkh360.com'
  }
  console.log(env)
  app.use(proxy('/fn', {
    target: fnTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/fn', ''),
    logs: true
  }))
  app.use(proxy('/fr', {
    target: fnReactTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/fn-react', ''),
    logs: true
  }))
  app.use(proxy('/so/', {
    target: soTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/so/', ''),
    logs: true
  }))
  app.use(proxy('/so-gray', {
    target: soGrayTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/so-gray', ''),
    logs: true
  }))
  app.use(proxy('/wf', {
    target: workflowTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/wf', ''),
    logs: true
  }))
  app.use(proxy('/wo', {
    target: workOrderTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/wo', ''),
    logs: true
  }))
  app.use(proxy('/mm', {
    target: mmTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/mm', ''),
    logs: true
  }))
  // app.use(proxy('/as', {
  //   target: asTarget,
  //   changeOrigin: true,
  //   rewrite: path => path.replace('/as', ''),
  //   logs: true
  // }))
  
  app.use(proxy('/stock', {
    target: stockTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/stock', ''),
    logs: true,
    // 注：全局同一个HttpProxy实例，改一个地方全局通用
    events: {
      proxyRes(proxyRes: IncomingMessage,  req: IncomingMessage, res: ServerResponse){
        proxyRes.headers['cache-control'] = 'no-cache'
        proxyRes.headers['expires'] = '0'
      }
    }
  }))
  app.use(proxy('/tech', {
    target: techTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/tech', ''),
    logs: true
  }))
  app.use(proxy('/tg', {
    target: tgTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/tg', ''),
    logs: true
  }))
  app.use(proxy('/ix', {
    target: ixTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/ix', ''),
    logs: true
  }))
  app.use(proxy('/pr', {
    target: prTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/pr', ''),
    logs: true
  }))
  app.use(proxy('/pe', {
    target: peTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/pe', ''),
    logs: true
  }))
  app.use(proxy('/ar', {
    target: arTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/ar', ''),
    logs: true
  }))
  app.use(proxy('/ik', {
    target: ikTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/ik', ''),
    logs: true
  }))
  app.use(proxy('/ai-helper', {
    target: aiHelperTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/ai-helper', ''),
  }))
  app.use(proxy('/zhs', {
    target: zhsTarget,
    changeOrigin: true,
    rewrite: path => path.replace('/zhs', ''),
    logs: true
  }))
}
