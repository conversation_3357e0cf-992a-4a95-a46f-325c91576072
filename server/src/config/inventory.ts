const propsMap = {
  'OA流程编号': 'oaNo',
  'OA行号': 'oaItemNo',
  '识别号码': 'identifier',
  '采购订单': 'referNo',
  '采购订单行': 'referItemNo',
  '抬头备注': 'orderRemark',
  '运输方向': 'transportDirection',
  '运输地址联系人': 'transportContactName',
  '运输地址联系人电话': 'transportContactNumber',
  '运输地址省份': 'transportProvince',
  '运输地址城市': 'transportCity',
  '运输地址区县': 'transportRegion',
  '运输地址街道': 'transportStreet',
  '运输地址详细地址': 'transportAddressDetail',
  '震坤行联系人': 'zkhContactName',
  '震坤行联系人电话': 'zkhContactNumber',
  '震坤行省份': 'zkhProvince',
  '震坤行城市': 'zkhCity',
  '震坤行区县': 'zkhRegion',
  '震坤行街道': 'zkhStreet',
  '震坤行详细地址': 'zkhAddressDetail',
  'SKU编码': 'skuNo',
  '接收SKU编码': 'receiveSkuNo',
  '组件SKU': 'skuNo',
  '数量': 'quantity',
  '仓库地点': 'warehouseLocation',
  '总账科目': 'generalLedgerAccount',
  '成本中心': 'costCenter',
  '接收仓库地点': 'receiveWarehouseLocation',
  '收货仓库地点': 'receiveWarehouseLocation',
  '批次': 'batchNo',
  '项目文本': 'projectText',
  '供应商': 'supplierNo',
  '工厂': 'factoryCode',
  '发货工厂': 'factoryCode',
  '接收工厂': 'receiveFactoryCode',
  '责任归属': 'responsibilityOwner',
  '客户编码': 'customerCode'
}

const orderTypeMap = {
  20: '委外发料单',
  21: '委外退料单',
  18: '费用性领料单',
  19: '费用性退料单',
  28: '采购退货单',
  30: '寄售调拨单',
  31: '库内预调拨单',
  13: '库内调拨单',
  29: '工厂间调拨单',
  27: '库存调整单',
  23: '库存报废单',
  32: '非EVM寄售盘亏单',
  '四级地址': '',
  '总账科目': ''
}

const submitFields = {
  20: [
    'identifier', /* 识别号码 */
    'referNo', /* 采购订单 */
    'referItemNo', /* 采购订单行 */
    'orderRemark', /* 抬头备注 */
    'transportDirection', /* 运输方向 */
    'transportContactName', /* 运输地址联系人 */
    'transportContactNumber', /* 运输地址联系人电话 */
    'transportProvince', /* 运输地址省份 */
    'transportCity', /* 运输地址城市 */
    'transportRegion', /* 运输地址区县 */
    'transportStreet', /* 运输地址街道 */
    'transportAddressDetail', /* 运输地址详细地址 */
    'zkhContactName', /* 震坤行联系人 */
    'zkhContactNumber', /* 震坤行联系人电话 */
    'zkhProvince', /* 震坤行省份 */
    'zkhCity', /* 震坤行城市 */
    'zkhRegion', /* 震坤行区县 */
    'zkhStreet', /* 震坤行街道 */
    'zkhAddressDetail', /* 震坤行详细地址 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'warehouseLocation', /* 仓库地点 */
    'batchNo', /* 批次 */
    'projectText' /* 项目文本 */
  ],
  18: [
    'identifier', /* 识别号码 */
    'orderRemark', /* 抬头备注 */
    'transportDirection', /* 运输方向 */
    'transportContactName', /* 运输地址联系人 */
    'transportContactNumber', /* 运输地址联系人电话 */
    'transportProvince', /* 运输地址省份 */
    'transportCity', /* 运输地址城市 */
    'transportRegion', /* 运输地址区县 */
    'transportStreet', /* 运输地址街道 */
    'transportAddressDetail', /* 运输地址详细地址 */
    'zkhContactName', /* 震坤行联系人 */
    'zkhContactNumber', /* 震坤行联系人电话 */
    'zkhProvince', /* 震坤行省份 */
    'zkhCity', /* 震坤行城市 */
    'zkhRegion', /* 震坤行区县 */
    'zkhStreet', /* 震坤行街道 */
    'zkhAddressDetail', /* 震坤行详细地址 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'factoryCode', /* 工厂 */
    'warehouseLocation', /* 仓库地点 */
    'generalLedgerAccount', /* 总账科目 */
    'costCenter', /* 成本中心 */
    'batchNo', /* 批次 */
    'projectText' /* 项目文本 */
  ],
  28: [
    'identifier', /* 识别号码 */
    'referNo', /* 采购订单 */
    'referItemNo', /* 采购订单行 */
    'orderRemark', /* 抬头备注 */
    'transportDirection', /* 运输方向 */
    'transportContactName', /* 运输地址联系人 */
    'transportContactNumber', /* 运输地址联系人电话 */
    'transportProvince', /* 运输地址省份 */
    'transportCity', /* 运输地址城市 */
    'transportRegion', /* 运输地址区县 */
    'transportStreet', /* 运输地址街道 */
    'transportAddressDetail', /* 运输地址详细地址 */
    'zkhContactName', /* 震坤行联系人 */
    'zkhContactNumber', /* 震坤行联系人电话 */
    'zkhProvince', /* 震坤行省份 */
    'zkhCity', /* 震坤行城市 */
    'zkhRegion', /* 震坤行区县 */
    'zkhStreet', /* 震坤行街道 */
    'zkhAddressDetail', /* 震坤行详细地址 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'batchNo', /* 批次 */
    'supplierNo', /* 供应商 */
    'projectText' /* 项目文本 */
  ],
  30: [
    'identifier', /* 识别号码 */
    'orderRemark', /* 抬头备注 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'factoryCode', /* 工厂 */
    'warehouseLocation', /* 发货仓库地点 */
    'receiveWarehouseLocation', /* 收货仓库地点 */
    'supplierNo', /* 供应商 */
    'projectText' /* 项目文本 */
  ],
  31: [
    'identifier', /* 识别号码 */
    'orderRemark', /* 抬头备注 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'factoryCode', /* 工厂 */
    'warehouseLocation', /* 发货仓库地点 */
    'receiveWarehouseLocation', /* 收货仓库地点 */
    'projectText' /* 项目文本 */
  ],
  13: [
    'identifier', /* 识别号码 */
    'orderRemark', /* 抬头备注 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'factoryCode', /* 工厂 */
    'warehouseLocation', /* 发货仓库地点 */
    'receiveWarehouseLocation', /* 收货仓库地点 */
    'batchNo', /* 批次 */
    'projectText' /* 项目文本 */
  ],
  29: [
    'identifier', /* 识别号码 */
    'orderRemark', /* 抬头备注 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'factoryCode', /* 发货工厂 */
    'receiveFactoryCode', /* 接收工厂 */
    'warehouseLocation', /* 发货仓库地点 */
    'receiveWarehouseLocation', /* 收货仓库地点 */
    'projectText' /* 项目文本 */
  ],
  27: [
    'identifier', /* 识别号码 */
    'orderRemark', /* 抬头备注 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'factoryCode', /* 工厂 */
    'warehouseLocation', /* 仓库地点 */
    'batchNo', /* 批次 */
    'projectText' /* 项目文本 */
  ],
  23: [
    'identifier', /* 识别号码 */
    'orderRemark', /* 抬头备注 */
    'skuNo', /* 组件SKU */
    'quantity', /* 数量 */
    'factoryCode', /* 工厂 */
    'warehouseLocation', /* 仓库地点 */
    'costCenter', /* 成本中心 */
    'batchNo', /* 批次 */
    'projectText' /* 项目文本 */
  ]
}
const getBatchUrl = (orderType) =>{
  let url = ''
  switch (orderType) {
    case '20':
    case '21':
      url = '/iao/iaoOutsourcingBatchInput'
      break
    case '18':
    case '19':
      url = '/iao/iaoCostBatchInput'
      break
    case '28':
      url = '/iao/iaoReturnBatchInput'
      break
    case '30':
    case '31':
    case '13':
    case '29':
      url = '/iao/iaoTransferBatchInput'
      break
    case '27':
      url = '/iao/iaoAdjustBatchInput'
      break
    case '23':
      url = '/iao/iaoDumpBatchInput'
      break
    case '32':
      url = '/iao/iaoLossBatchInput'
      break
    default:
      break
  }
  return url
}

submitFields[21] = submitFields[20]
submitFields[19] = submitFields[18]

export {
  submitFields,
  orderTypeMap,
  propsMap,
  getBatchUrl
}
