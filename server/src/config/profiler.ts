import * as env from '@server/env'

const profilerConfig = {
  local: {
    server: 'ws://xtransit.zkh360.com', // 填写前一节中部署的 xtransit-server 地址
    appId: 4, // 创建应用得到的应用 ID
    appSecret: 'fba66f82336451e7cd1c775b0a27c36b'
  },
  dev: {},
  qa: {
    docker: true,
    libMode: true,
    server: 'ws://xtransit.zkh360.com', // 填写前一节中部署的 xtransit-server 地址
    appId: 4, // 创建应用得到的应用 ID
    appSecret: 'fba66f82336451e7cd1c775b0a27c36b' // 创建应用得到的应用 Secret
  },
  prerelease: {
    docker: true,
    libMode: true,
    server: 'ws://xtransit.zkh360.com',
    appId: 6,
    appSecret: 'ce8e17f77824bffeb887848ba2f7049d'
  },
  release: {
    docker: true,
    libMode: true,
    server: 'ws://xtransit.zkh360.com',
    appId: 5,
    appSecret: '4b460b0a158b6a309031687cab6e5204'
  }
}

export const type = env.LOCAL ? 'local' : env.DEV ? 'dev' : env.QA ? 'qa' : env.PRERELEASE ? 'prerelease' : 'release'
export default profilerConfig[type]
