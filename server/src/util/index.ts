import Core from "@server/core-common";
import * as token from "@poseidon/token";
import { proxy } from "@server/proxy";
import * as koaWebProxy from 'koa-proxies'
import * as body from "koa-body";
import { type } from "../config/api";
import { Context } from "koa";
import * as env from "@server/env";
import { client } from "@boss/redis";
import Debug from "debug";
export const debug = Debug("boss-admin");
Debug.enable('boss-admin');

const { Type } = token.defination;
const { auth } = token.middleware;
const mseEnv = env.RELEASE || env.PRERELEASE ? "pro" : "dev";
const acmInstance = require("@boss/mse").MSE.getInstance(mseEnv);
const showProxyDebug = env.RELEASE ? false : true;
const MAX_BODY = "5mb";
const mseApiConfig = env.RELEASE || env.PRERELEASE
  ? "boss-api.pro.config"
  : "boss-api.uat.config";
const mseWebConfig = env.RELEASE || env.PRERELEASE
  ? "boss-web.pro.config"
  : "boss-web.uat.config";
let apiProxies = [];
export let webProxies = {};

/**
 * try catch wrapper
 * @param callback
 * @param context
 * @param notShowError
 * @returns
 */
export function safeRun(
  callback: () => void,
  context?: any,
  notShowError?: boolean
): any {
  let ret = undefined;
  try {
    ret = callback.call(context);
  } catch (err) {
    if (!notShowError) {
      console.log(err);
    }
  }
  return ret;
}

export function responseTime(ctx, next: any): any {
  const start = process.hrtime();
  return next().then(() => {
    safeRun(() => {
      const end = process.hrtime(start);
      const timeCost = end[0] * 1e3 + end[1] / 1e6;
      const threshold = 30 * 1e3;
      const healthCheck = 1 * 1e3;
      const { url } = ctx.req;
      if (timeCost > threshold) {
        ctx.elk &&
          ctx.elk.error({
            message: `${url} api takes too long[${timeCost}ms][responseTime]`,
            type: "app",
          });
      }
      if (/health_check/gim.test(url) && timeCost > healthCheck) {
        ctx.elk &&
          ctx.elk.error({
            message: `${url} api takes too long[${timeCost}ms][responseTime]`,
            type: "app",
          });
      }
    });
  });
}

function deleteDuplicate(router, path) {
  const find = router.stack.find((item) => item.path === "/api" + path);
  if (!!find) {
    debug(`deleteDuplicate ${path}: ${!!find}`);
  }
  router.stack = router.stack.filter((item) => item !== find);
}

/**
 * dynamic reload routers
 * @params {router, app}
 * @returns
 */
export function loadRouter(router, app) {
  deleteDuplicate(router, "/updateRouter");
  router.get("/updateRouter", (ctx) => {
    loadRouter(router, app);
    const memory = process.memoryUsage();
    for (const key in memory) {
      memory[key] = `${(memory[key] / 1024 / 1024).toFixed(2)}MB`;
    }
    ctx.body = { stack: router.stack, memory };
  });
  apiProxies.forEach((item) => {
    let matchPath = `${item.path}(/.*)?`;
    deleteDuplicate(router, matchPath);
    router.all(
      matchPath,
      body({ multipart: true, formLimit: MAX_BODY, textLimit: MAX_BODY, jsonLimit: MAX_BODY }),
      auth(Type.security),
      proxy({
        debug: showProxyDebug,
        host: item.host,
        rewrite: {
          [`^/api${item.path}`]: item.rewrite,
        },
        timeout: 1000 * 60 * 10,
        extend: (option, ctx: Context) => {
          const token = ctx.state.token[Type.security].accessToken ? `bearer ${ctx.state.token[Type.security].accessToken}` : ctx.request.header?.authorization

          option.headers.Authorization = token || '';
        },
      })
    );
  });
  debug(`loadRouter succeed! ${router.stack.length}`);
}

function buildApiProxies(config) {
  if (!Object.keys(config).length) {
    debug("no proxies found!");
    return
  }
  console.log(config, Object.keys(config))
  for (let prop in config) {
    let path = prop.toLowerCase();
    const proxy = { path: `/${path}`, host: config[prop], rewrite: "" };
    apiProxies.push(proxy);
    if (/_/.test(path)) {
      path = path.replace(/_/gim, "-");
      const dashProxy = { path: `/${path}`, host: config[prop], rewrite: "" };
      apiProxies.push(dashProxy);
    }
  }
  debug("build api proxies finished!");
  debug(apiProxies);
}
export async function initRoutes() {
  debug(`get config[${type}] from mse...`);
  try {
    const config = await acmInstance.getConfig(mseApiConfig);
    buildApiProxies(config[type] || {});
  } catch (err) {
    console.error(err);
  }
  try {
    const config = await acmInstance.getConfig(mseWebConfig);
    webProxies = config[type];
    debug("build web proxies finished!");
    debug(webProxies);
  } catch (err) {
    console.error(err);
  }
}
export async function initRedis() {
  await client
    .then((redis) => {
      if (redis) {
        redis.on("error", function (err) {
          debug("could not establish a connection with redis. " + err);
        });
        redis.on("connect", function (err) {
          debug("connected to redis successfully");
        });
      }
    })
    .catch((err) => {
      console.error(err);
    });
}

export function initWebProxies(app) {
  for (let prop in webProxies) {
    debug(`set web proxy[${prop}]: ${webProxies[prop]}`);
    app.use(
      koaWebProxy(`/${prop}`, {
        target: webProxies[prop],
        changeOrigin: true,
        rewrite: path => path.replace(`/${prop}`, ''),
        logs: true,
      })
    );
  }
}
