import { EventEmitter } from "events";
const getConnection = require('@boss/dms')
interface Iconfig {
  connectionLimit: number;
  user: string;
  host: string;
  token: string;
  key: string;
  database: string;
}
type Iinstance = {
  [propName:string]: DmsUtils;
}
export class DmsUtils extends EventEmitter{
  private config: Iconfig;
  private connection: any;
  public static instance: Iinstance = {};
  static getInstance(label:string,config: Iconfig): DmsUtils{
    if (!this.instance[label]) {
      this.instance[label] = new DmsUtils(config)
    }
    return this.instance[label]
  }
  constructor(config: Iconfig) {
    super();
    this.config = config;
  }
  release () {
    this.connection.release()
  }
  async query(queryString: string) {
    await this.connect()
    return new Promise((resolve, reject) => {
      this.connection.query(queryString, (error, results, fields) => {
        // release connection to pool after query
        this.connection.release()
        if (error) return reject(error)
        resolve(results)
      })
    })
  }
  async retryConnect(callback) {
    await this.connect()
    callback()
  }
  async connect(){
    const connection = await getConnection(this.config)
    this.connection = connection
  }
}