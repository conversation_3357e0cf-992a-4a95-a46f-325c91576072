/**
 * @Author: luozhikai
 * @Date: 2024-03-29 14:25:33
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-03-29 18:10:39
 * @Description: file content
 */
import * as env from '@server/env'

// eslint-disable-next-line @typescript-eslint/no-var-requires
const SensorsAnalytics = require('sa-sdk-node');

const url = 'https://tracking.zkh.com/sa?project=production';
const uatUrl = 'https://tracking.zkh.com/sa?project=abroad';
const cachePath = '/zkh/sa';

export class SaSingleton {
  private static instance: SaSingleton;
  private sa: any;

  private constructor() {
    const sa = new SensorsAnalytics;
    sa.disableReNameOption();

    sa.initNWConsumer({
      url: env.LOCAL ||env.DEV || env.QA ? uatUrl : url,
      cachePath,
      timeout: 30 * 1000,
    });
    this.sa = sa;
  }

  static getInstance(): SaSingleton {
    if (!SaSingleton.instance) {
      SaSingleton.instance = new SaSingleton();
    }
    return SaSingleton.instance;
  }

  public track(
    username: string,
    eventName: string,
    properties: Record<string, unknown> = {},
  ) {
    if (this.sa) {
      this.sa.track(username, eventName, {
        '$is_login_id': true,
        ...properties,
      });
    }
  }
}
