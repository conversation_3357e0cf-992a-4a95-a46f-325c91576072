import Core from '@server/core-common';
import * as token from '@poseidon/token';
import * as env from '@server/env';
import historyApiFallback from 'koa2-connect-history-api-fallback';
import config from './config';
import * as path from 'path';
import elk from '@server/elk';
import { setProxies } from './proxy';
import { responseTime, initRoutes, initRedis } from './util';
import { startMonitor } from './util/initMonitor'
import monitorConfig from './config/profiler'

const { Type } = token.defination;
const project = 'boss-union';
let app;
const startApp = () => {
  app = new Core({
    baseDir: __dirname,
    port: process.env.PORT || 3000,
    keys: 'boss-admin'
  });
  setProxies(app);
  app
    .use(responseTime)
    .use(app.middlewares.cdn.index)
    .use(
      token.middleware.connect({
        env: process.env.ENV || 'local',
        type: Type.security,
        clientId: config.clientId,
        clientSecret: config.clientSecret
      })
    )
    .use(
      historyApiFallback({
        index: '/',
        // SPA router whitelist
        whiteList: [
          '/api',
          '/s/',
          '/api-acm-config',
          '/api-mse-config',
          '/api-sales',
          '/api-kunhe-mdm',
          '/api-logistics',
          '/documentApi',
          '/token-base',
          '/token-new',
          '/oms-base',
          '/fe-upload',
          '/api-financial',
          '/oms-new',
          '/api-opc',
          '/api-opc-csc',
          '/data-center',
          '/call-center',
          '/iframe-bridge',
          '/data-center-front',
          '/gbb-fn',
          '/ali-upload',
          '/security-api',
          '/reconciliation',
          '/internal-api',
          '/logout',
          '/unauthorized',
          '/health_check',
          '/bossApi',
          '/api-oss',
          '/tag-center-data',
          '/tag-center-marking',
          '/login/callback',
          '/tag-center-query',
          '/dmp-price'
        ]
      })
    )
    .use(
      elk({
        project,
        filename: env.LOCAL
          ? path.resolve(__dirname, '../log/logstash.log')
          : `/zkh/log/${project}/logstash.log`
      })
    );
  app.start();
  if (!env.LOCAL) {
    startMonitor(monitorConfig);
  }
};

async function runMain() {
  await initRedis();
  await initRoutes();
  startApp();
}

runMain().catch((err) => {
  console.log(err);
  app &&
    app.elk &&
    app.elk.error({
      type: 'app',
      message: err.message
    });
  process.exit(1);
});
