FROM newhub.zkh360.com/fe/nodejs:16.17.0-alpine3.16-pdf2docx as builder
WORKDIR /app
COPY . ./
RUN npm config set phantomjs_cdnurl "https://files.zkh360.com/download" \
 && npm config set sharp_binary_host "https://npmmirror.com/mirrors/sharp" \
 && npm config set sharp_libvips_binary_host "https://npmmirror.com/mirrors/sharp-libvips" \
 && npm config set xprofiler_binary_host_mirror "https://npmmirror.com/mirrors/xprofiler" \
 && npm i --registry=http://npm.zkh360.com \
 && npm run build

FROM newhub.zkh360.com/fe/ossutil-boss:1.7.19 AS uploader
WORKDIR /usr/local/bin
ARG ENV
ENV ENV=${ENV}
COPY --from=builder /app ./app
ARG DIR
ENV DIR=${DIR}
RUN ./ossutil cp -r app/static/bundle/boss/release/ "oss://zkh360-boss/business/boss/bundle/boss/$DIR" -e oss-cn-beijing.aliyuncs.com

FROM newhub.zkh360.com/fe/nodejs:16.17.0-alpine3.16-pdf2docx
ARG ENV
ARG DIR
ENV ENV=${ENV}
ENV DIR=${DIR}
ENV PORT=3000
COPY --from=uploader /usr/local/bin/app/static/bundle/boss/release "/app/static/bundle/boss/$DIR"
# COPY --from=uploader "/usr/local/bin/app/static/bundle/boss/release/manifest.json" "/app/static/bundle/boss/release/manifest.json"
COPY --from=uploader /usr/local/bin/app/node_modules /app/node_modules
COPY --from=uploader /usr/local/bin/app/package.json /app/package.json
COPY --from=uploader /usr/local/bin/app/server/dist /app/server/dist
COPY --from=uploader /usr/local/bin/app/server/assets /app/server/assets
COPY --from=uploader /usr/local/bin/app/server/views /app/server/views
COPY --from=uploader /usr/local/bin/app/stop.sh /app
RUN ls -la /app
EXPOSE 3000
CMD ["node", "/app/server/dist/app.js"]
