module.exports = {
    "env": {
        "browser": true,
        "es6": true,
        "node": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:@typescript-eslint/eslint-recommended"
    ],
    "parserOptions": {
        "ecmaVersion": 2018,
        "parser": "@typescript-eslint/parser",
        "sourceType": "module"
    },
    "plugins": [],
    "rules": {
        "quotes": [1, "single"],
        "space-before-function-paren": 0,
        'semi': 0,
        "comma-dangle": ["error", "never"],
        "template-curly-spacing" : "off",
        "indent": "off"
    }
}