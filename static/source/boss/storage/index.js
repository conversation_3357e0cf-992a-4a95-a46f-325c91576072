export const setOrderTransfer = (key, value) => {
  if (typeof value === 'object') {
    value = JSON.stringify(value)
  }
  localStorage.setItem(key, value)
}

export const getOrderTransfer = key => {
  const data = localStorage.getItem(key)
  try {
    return JSON.parse(data)
  } catch (err) {
    return null
  }
}

export const removeOrderTransfer = key => {
  localStorage.removeItem(key)
}

export const setIds = (key, value) => {
  value = JSON.stringify(value)
  localStorage.setItem(key, value)
}

export const getIds = key => {
  const data = localStorage.getItem(key)
  try {
    return JSON.parse(data)
  } catch (err) {
    return null
  }
}

export const removeIds = key => {
  localStorage.removeItem(key)
}
