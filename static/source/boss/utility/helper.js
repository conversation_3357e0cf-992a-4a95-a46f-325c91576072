const getType = obj => {
  return Object.prototype.toString.call(obj).slice(8, -1).toLowerCase()
}

const clone = obj => {
  const type = getType(obj)
  let res = type === 'array' ? [] : {}

  if (type === 'array') {
    res = obj.slice(0)
  }
  for (const key in obj) {
    const type = getType(obj[key])
    res[key] = type === 'object' || type === 'array' ? clone(obj[key]) : obj[key]
  }
  return res
}

const formatTime = (time) => {
  if (time) {
    const date = new Date(time)
    const dateString = date.toISOString().replace(/T.+/, '')
    const timeString = date.toTimeString().replace(/\s.+/, '')
    return `${dateString} ${timeString}`
  }
  return time
}
const execOnce = (fn) => {
  if (!fn.executed) {
    fn()
    fn.executed = true
  }
}

const findRoot = (vm) => {
  try {
    let parent = vm.$parent
    while (parent.$parent) {
      parent = parent.$parent
    }
    return parent
  } catch (err) {
    console.log(err)
  }
}
const findInChildren = (root, name) => {
  if (root.$options.name === name) {
    return root
  }
  if (root.$children.some(child => child.$options.name === name)) {
    let tmp = root.$children.filter(child => child.$options.name === name)
    return tmp[0]
  } else {
    for (let ele of root.$children) {
      let tmp = findInChildren(ele, name)
      if (tmp) return tmp
    }
  }
}
const findAnyVue = (vm, name) => {
  let root = findRoot(vm)
  if (name) {
    return findInChildren(root, name)
  } else {
    return root
  }
}

export {
  formatTime,
  getType,
  clone,
  execOnce,
  findRoot,
  findAnyVue
}
