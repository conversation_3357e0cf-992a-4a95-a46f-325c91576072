// TODO: 需整理
import { Vue } from '@client/boss'
import { MessageBox } from 'element-ui'
import { Blob } from 'blob-polyfill';
import axios from 'axios'
import qs from 'qs'

const api = axios.create({
  baseURL: '/',
  withCredentials: true
  // timeout: 500   axios无默认超时时间，即不会超时
})

api.interceptors.request.use(config => {
  if (config.method === 'get') {
    config.paramsSerializer = function (params) {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  }
  return config
})
// 注意interceptor设置, response是否满足结构 {data, status, message}
api.interceptors.response.use(
  response => {
    if (response.status === 200) {
      // success: status === 0, else fail
      // if (response.data && response.data.code === 200) {
      //   return Promise.resolve(response.data.data)
      // } else {
      return Promise.resolve(response)
      // }
    } else {
      return Promise.reject(response)
    }
  },
  error => {
    if (error) {
      const { response } = error
      if (response && response.status === 401) {
        MessageBox.alert('登录超时，请刷新重试！', '提示', {
          type: 'error'
        })
      }
    }
    return Promise.reject(error)
  }
)
// code conflict ???
export const s = api

const serviceOnlyData = function () {
  // eslint-disable-next-line no-useless-call
  return api.call(null, ...arguments).then(res => {
    // success: status === 0, else fail
    // if (res.data && res.data.code) {
    //   return res.data
    // } else if (res.data instanceof Array && res.status === 200) {
    //   return res.data
    // } else {
    //   return res
    // }
    if (res.status === 200 && res.data !== null && res.data !== undefined) {
      return res.data
    } else {
      return res
    }
  }).catch(err => {
    return err && err.response && err.response.data
  })
}

export default serviceOnlyData

export const downloadFile = function (url, data, param, method = 'get') {
  const _param = Object.assign(
    {
      method: method,
      responseType: 'arraybuffer',
      url: url,
      data
    },
    param
  )
  return api(_param).then(res => {
    const blob = new Blob([res.data], {
      type: res.headers['content-type']
    })
    blob.text().then(val => {
      try {
        const response = JSON.parse(val)
        if (url === '/oms-opc-front/backlog/export') {
          if (response.code === 200) {
            Vue.prototype.$message.success(response.message || response.msg || '导出成功！')
          } else if (response.code === 300) {
            Vue.prototype.$message.info(response.message || response.msg || '导出提示！')
          } else {
            Vue.prototype.$message.error(response.message || response.msg || '导出失败！')
          }
          return
        }
        if (response.status !== 200 || response.code === 601) {
          Vue.prototype.$message.error(response.message || response.msg || '下载失败！')
          return
        }
      } catch (err) {
        console.log(err)
      }
      let fileName = 'unknown'
      const contentDisposition = res.headers['content-disposition']
      const contentLength = res.headers['content-length']
      if (contentDisposition) {
        fileName = window.decodeURIComponent(contentDisposition.split('=')[1])
      }
      // eslint-disable-next-line
      if (param && param.ignoreEmpty && contentLength == '0') {
        return MessageBox.alert('没有数据！', '下载提示', {
          type: 'error'
        })
      }
      downFile(blob, fileName)
    })
  })
}
export const downFile = function (blob, fileName) {
  if ('download' in document.createElement('a')) {
    // 非IE下载
    const elink = document.createElement('a')
    elink.download = fileName
    elink.style.display = 'none'
    elink.href = URL.createObjectURL(blob)
    document.body.appendChild(elink)
    elink.click()
    URL.revokeObjectURL(elink.href) // 释放URL 对象
    document.body.removeChild(elink)
  } else {
    // IE10+下载
    navigator.msSaveBlob(blob, fileName)
  }
}
export const downloadByLink = function (link, fileName) {
  try {
    const a = document.createElement('a')
    a.href = link
    a.target = '_blank'
    fileName && (a.download = fileName)
    a.style.display = 'none'
    a.click()
  } catch (e) {
    window.open(link)
  }
}
export const isValidLink = function (link) {
  const reg = /http(s?):\/\//
  return reg.test(link)
}
export function downloadByIframe(url) {
  const iframe = document.createElement('iframe')
  iframe.style.display = 'none'
  function iframeLoad() {
    console.log('iframe onload')
    const win = iframe.contentWindow
    const doc = win.document
    if (win.location.href === url) {
      if (doc.body.childNodes.length > 0) {
        // response is error
      }
      iframe.parentNode.removeChild(iframe)
    }
  }
  if ('onload' in iframe) {
    iframe.onload = iframeLoad
  } else if (iframe.attachEvent) {
    iframe.attachEvent('onload', iframeLoad)
  } else {
    iframe.onreadystatechange = function onreadystatechange() {
      if (iframe.readyState === 'complete') {
        iframeLoad()
      }
    }
  }
  iframe.src = ''
  document.body.appendChild(iframe)

  setTimeout(function loadUrl() {
    iframe.contentWindow.location.href = url
  }, 50)
}
