import _ from 'lodash';

export const orderServiceFields = [
  { field: 'dnPaperReq', label: '送货单纸张要求', multiple: false },
  { field: 'dnIncidentalWay', label: '送货单附带方式', multiple: false },
  { field: 'dnSignatureReq', label: '送货单签章要求', multiple: true, forceReset: true },
  {
    field: 'otherLabelReq',
    label: '其他标签要求',
    multiple: true,
    forceReset: true
  },
  {
    field: 'fastenerLabelReq',
    label: '紧固件标签要求',
    multiple: true,
    forceReset: true
  },
  {
    field: 'labelPasteWay',
    label: '标签张贴方式',
    multiple: true,
    forceReset: true
  },
  { field: 'fastenerDetect', label: '紧固件检测', multiple: false },
  {
    field: 'fastenerSpecialPackageReq',
    label: '紧固件特殊包装要求',
    multiple: false
  },
  {
    field: 'specifiedReceiptDayOfWeek',
    label: '固定送货周期',
    multiple: true,
    forceReset: true
  },
  {
    field: 'specifiedReceiptTime',
    label: '指定收货时间',
    multiple: true,
    forceReset: true
  },
  {
    field: 'disableShipping',
    label: '禁用货运',
    multiple: true,
    forceReset: true
  },
  {
    field: 'designatedShipping',
    label: '指定货运',
    multiple: true,
    forceReset: true
  },
  { field: 'vehicleReq', label: '车辆要求', multiple: true, forceReset: true },
  { field: 'signingReq', label: '签收要求', multiple: false },
  { field: 'forkliftRelated', label: '叉车相关', multiple: false }
];

export const baseOption = {
  name: '无要求',
  code: '0'
};
export const patchOrderServiceOptions = (dict) => {
  const newDict = _.cloneDeep(dict);
  for (const code in newDict) {
    newDict[code] = {
      ..._.find(orderServiceFields, ({ field }) => field === code),
      options: code === 'dnIncidentalWay' || code === 'dnSignatureReq' ? [
        ...newDict[code]
        .filter((opt) => opt.status !== '0')
        .map((opt) => ({
          ...opt,
          persistDisabled: opt.status === '2'
        }))
      ] : [
        baseOption,
        ...newDict[code]
          .filter((opt) => opt.status !== '0')
          .map((opt) => ({
            ...opt,
            persistDisabled: opt.status === '2'
          }))
      ]
    };
  }
  return newDict;
};

/**
 * @description ['1','2','3'] --> '1,2,3'
 */
export const foldFields = (formData, fields) => {
  const tmp = { ...formData };
  fields.forEach((field) => {
    if (_.isArray(tmp[field])) {
      tmp[field] = tmp[field].join(',');
    }
  });
  return tmp;
};

// '1,2,3' --> ['1','2','3']
export const unfoldFields = (formData, fields) => {
  const tmp = { ...formData };
  fields.forEach((field) => {
    if (typeof tmp[field] === 'string') {
      tmp[field] = tmp[field] ? tmp[field].split(',').filter(Boolean) : [];
    }
  });
  return tmp;
};

/**
 * @description '1,2,3' --> ['1','2','3']
 */
export const unfoldField = (data) => {
  if (typeof data === 'string') {
    return data ? data.split(',').filter(Boolean) : [];
  }
  return data;
};
