import { Vue } from '@client/boss'
import Clipboard from 'clipboard'

function clipboardSuccess () {
  Vue.prototype.$message({
    message: '复制成功',
    type: 'success',
    duration: 1500
  })
}

function clipboardError () {
  Vue.prototype.$message({
    message: '复制失败',
    type: 'error'
  })
}

export default function handleClipboard (text, event, cb) {
  const clipboard = new Clipboard(event.target, {
    text: () => text
  })
  clipboard.on('success', () => {
    if (cb) {
      const flag = true
      cb(flag)
    } else {
      clipboardSuccess()
    }
    clipboard.off('error')
    clipboard.off('success')
    clipboard.destroy()
  })
  clipboard.on('error', () => {
    if (cb) {
      const flag = false
      cb(flag)
    } else {
      clipboardError()
    }
    clipboard.off('error')
    clipboard.off('success')
    clipboard.destroy()
  })
  clipboard.onClick(event)
}

/**
 * @param {*} text 复制的内容
 * @description 兼容性更好的复制到剪切板
 */
export function handleClipboardV2(text) {
  if (navigator.clipboard) {
    // clipboard api 复制
    navigator.clipboard.writeText(text);
  } else {
    const textarea = document.createElement('textarea');
    document.body.appendChild(textarea);
    // 隐藏此输入框
    textarea.style.position = 'fixed';
    textarea.style.clip = 'rect(0 0 0 0)';
    textarea.style.top = '10px';
    // 赋值
    textarea.value = text;
    // 选中
    textarea.select();
    try {
      // 复制
      document.execCommand('copy', true);
    } catch (ex) {
      console.log('execCommand', ex);
      clipboardError();
      return;
    } finally {
      // 移除输入框
      document.body.removeChild(textarea);
    }
  }
  clipboardSuccess();
}
