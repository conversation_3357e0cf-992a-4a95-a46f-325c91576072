import axios from 'axios';
import store from '@/store';
import { Message } from 'element-ui';

const OSS = require('ali-oss');

const env = window.CUR_DATA.env.toLowerCase() || 'pro'; // isPro ? 'pro' : isFat ? 'fat' : 'uat'
let region = 'oss-cn-beijing';
let bucket = 'zkh360-boss';
let fileDomain = 'https://files.zkh360.com';
if (env !== 'pro') {
  bucket = 'zkh360-boss-uat';
  fileDomain = 'https://files-uat.zkh360.com';
}
// 阿里云oss client
let client = null;
export async function getClient(specialBucket, specialRegion) {
  try {
    if (client) {
      return client;
    }
    if (specialBucket && env !== 'pro') {
      specialBucket += '-uat';
    }
    const ossBucket = specialBucket || bucket;
    const ossRegion = specialRegion || region;
    const result = await axios.get('/api-oss/sts');
    if (result && result.data) {
      const data = result.data;
      client = new OSS({
        region: ossRegion,
        accessKeyId: data.AccessKeyId,
        accessKeySecret: data.AccessKeySecret,
        stsToken: data.SecurityToken,
        bucket: ossBucket,
        refreshSTSToken: async () => {
          const refreshSTSResponse = await axios.get('/api-oss/sts');
          if (refreshSTSResponse && refreshSTSResponse.data) {
            return {
              accessKeyId: refreshSTSResponse.data.AccessKeyId,
              accessKeySecret: refreshSTSResponse.data.AccessKeySecret,
              stsToken: refreshSTSResponse.data.SecurityToken
            };
          }
        }
      });
      return client;
    }
  } catch (e) {
    console.log('getClient error', e);
  }
  return null;
}
/**
 * @param {*} path 上传文件路径
 * @param {*} file 上传文件
 * @returns 上传成功后可预览路径
 */
export async function upload(path, file, specialBucket, specialRegion) {
  try {
    const fileNameSplit = file.name.split('.');
    const length = fileNameSplit.length;
    if (length >= 2) {
      const suffix = fileNameSplit[length - 1];
      // if (!validateFileType(suffix)) {
      //   Message.error('上传文件格式不正确');
      //   return '';
      // }
      fileNameSplit.pop();
      const fileName = encodeURIComponent(`${fileNameSplit.join('')}_${file.uid}.${suffix}`);
      const client = await getClient(specialBucket, specialRegion);
      const response = await client?.put(`${path}/${fileName}`, file);
      if (response?.url) {
        const urlPath = new URL(response.url);
        const pathname = urlPath.pathname;
        console.log('pathname', urlPath);
        if (specialBucket === 'zkh360-tagcenter') {
          return {
            bucketName: client.options?.bucket,
            name: pathname.substring(1, pathname.length)
          };
        } else {
          return {
            url: fileDomain + pathname
          };
        }
      }
    }
    Message.error('上传失败');
    return '';
  } catch (error) {
    console.log(error);
    Message.error('上传失败');
    return '';
  }
}

/**
 * 验证文件类型是否符合给定的类型列表，并不会严格根据每个upload组件设置的accept校验
 * 核心是为了防止用户上传可能会导致安全漏洞的文件类型，
 * 所以这里主要是校验上传附件是否为工作中常见的文本、excel、pdf、图片、视频等格式
 *
 * @param {string | File} current 当前需要验证的文件类型或文件对象
 * @param {string | string[]} types 允许的文件类型列表，可以是逗号分隔的字符串或字符串数组，默认为空字符串
 * @returns {boolean} 返回验证结果，如果文件类型符合则为true，否则为false
 */
export function validateFileType(current, types = '') {
  try {
    // 将types转换为数组，如果它是一个字符串，则将其分割成数组
    types = typeof types === 'string' ? types.split(',') : types;

    // 如果current不是字符串，说明它是一个文件对象，需要提取其文件类型
    if (typeof current !== 'string') {
      const fileNameSplit = current.name.split('.');
      const length = fileNameSplit.length;
      current = `.${fileNameSplit[length - 1]}`;
    }
    current = current.toLowerCase();

    // 获取默认的文件类型列表，如果不存在，则默认为空数组
    const defaultFileType = store.state.orderCommon?.acceptFileType?.commonType?.split(',') || [];

    // 合并给定的文件类型列表和默认文件类型列表
    const acceptType = [...types, ...defaultFileType].filter(Boolean);

    // 检查当前文件类型是否在合并后的文件类型列表中
    const res = acceptType.includes(current)
    // 如果当前文件类型不在允许的类型列表中，抛出错误提示
    if (!res) {
      Message.error('文件校验错误，请检查上传文件格式');
    }
    // 返回验证结果
    return res;
  } catch (error) {
    // 捕获到错误时，打印错误信息并提示用户文件校验错误
    console.log(error);
    Message.error('文件校验错误，请重试');
    // 返回false表示文件校验失败
    return false
  }
}
