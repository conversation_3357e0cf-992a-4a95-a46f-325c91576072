/* eslint-disable no-mixed-operators */
/**
 * @Author: luozhikai
 * @Date: 2024-02-21 15:01:27
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-04-07 14:17:36
 * @Description: file content
 */
import { BigNumber } from 'bignumber.js';
import { cloneDeep } from 'lodash'

const chargeProps = ['tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment', 'premium', 'other']
// const commonChargeProps = ['shareShipping', 'shareDiscount', 'shareInitialShipping', ...chargeProps]

const chargeAttrs = {
  'shareShipping': '运费',
  'shareDiscount': '折扣',
  'tariff': '关税',
  'customsFee': '报关杂费',
  'intlShipping': '国际运费',
  'saleTax': '消费税',
  'latePayment': '滞报金',
  'premium': '保险费',
  'other': '其他',
  'sundry': '杂费'
}

function isZ002PO(type) {
  return type === 'Z002'
}

export function isAssigned(item) {
  // 判断若该行运费或折扣或返利>0时，不允许将项目行数量改大
  return ['shipping', 'discount', 'rebate', 'sundry'].some(prop => {
    return item[`${prop}Amount`] > 0 || item?.initialItemShippingAmount > 0
  })
}

export function isValid(value, prop) {
  if (prop.indexOf('Type') > -1) {
    // 类型值包含0
    if (value === null || value === undefined || value === '') {
      return false
    }
    return true
  }
  if (value === null || value === undefined || value === '' || value === 0) {
    return false
  }
  return true
}

/**
 * @description 判断po数据中多个字段是否同时有效
 * @param {*} data po数据
 * @param  {...any} props 字段名
 * @returns
 */
export function sameTimeValid(data, ...props) {
  let validCount = 0
  const len = props.length
  for (let prop of props) {
    if (isValid(data[prop], prop)) {
      validCount++
    } else {
      validCount--
    }
  }
  let valid = Math.abs(validCount) === len
  return valid
}
/**
 * @param {number} amount
 * @param {number} n
 * @returns number
 * @description 保留n位小数
 */
export function formatAmount(amount, n) {
  const bg = new BigNumber(amount).dp(n).toNumber()
  return bg
}

function filterFun(item) {
  return item.itemQuantity &&
    (item.skuNo || item.materialDescription) &&
    (item.isDeleted == null || item.isDeleted === 0) &&
    (!item.deliveryStatus || item.deliveryStatus === 0) &&
    (item.isFree == null || item.isFree === 0)
}

function taxedTotalFilterFun(item) {
  return item.itemQuantity &&
    (item.isDeleted == null || item.isDeleted === 0) &&
    (item.isFree == null || item.isFree === 0) &&
    (!item.isEmptyLine)
}
function taxedTotalFilterAddFreeFun(item) {
  return item.itemQuantity &&
    (item.isDeleted == null || item.isDeleted === 0) &&
    (!item.isEmptyLine)
}

export function getTotalQuantity(data) {
  const { itemList } = data
  let allItemQuantity = 0
  itemList.filter(taxedTotalFilterAddFreeFun).forEach((sku) => {
    console.log('getTotalQuantity', sku, sku.skuNo, sku.itemQuantity)
    if (![null, '', undefined, NaN].includes(sku?.poItemExtend?.untaxedSalePrice || sku.untaxedSalePrice)) {
      allItemQuantity = allItemQuantity + sku.itemQuantity
    }
  })
  return allItemQuantity
}

// 【非直发】订单毛利率 = ∑订单行sku毛利率*采购数量/ 总采购数量）

// 未税采购价（转换为销售单位） = 未税采购价（untaxedPrice）* 分母（InventoryUnitDeno）/  价格倍数（PriceTimes，保留6位小数） /  分子（ InventoryUnitMole，保留6位小数）   ；
// 未税销售价（untaxedSalePrice）；
// 【直发】 毛利率 = ∑（未税销售价 - 未税采购价）* 采购数量(itemQuantity)    / ∑（未税销售价 * 采购数量(itemQuantity)）
/**
 * @description 计算po未税总额、含税总额、税费总额、毛利率
 * @param {*} data po数据
 * @returns {*} {unTaxedTotal,taxedTotal,taxTotal,grossMarginTotal}
 */
export function calculateTotal(data) {
  const { itemList } = data
  const allItemQuantity = getTotalQuantity(data)
  const editChange = itemList.filter(item => item.editChange)
  const result = itemList.filter(taxedTotalFilterFun).reduce((acc, sku) => {
    const { taxedTotalAmount, untaxedTotalAmount } = sku
    const taxedTotal = Number(taxedTotalAmount) + Number(acc.taxedTotal)
    const unTaxedTotal = Number(untaxedTotalAmount) + Number(acc.unTaxedTotal)
    const newValue = {
      taxedTotal: taxedTotal,
      unTaxedTotal: unTaxedTotal
    }
    return newValue
  }, { taxedTotal: 0, unTaxedTotal: 0, taxTotal: 0 })
  let result1
  let grossMarginTotal = 0
  let untaxedSaleTotal = 0
  let grossMargin = 0
  if (data.isDirect) { // 直发订单毛利率
    result1 = itemList.filter(taxedTotalFilterAddFreeFun).reduce((acc, sku) => {
      const { untaxedSalePrice, itemQuantity, inventoryUnitDeno = 1, inventoryUnitMole = 1, priceTimes = 1 } = sku
      // 未税采购价（转换为销售单位）
      const grossMarginUntaxedPrice = new BigNumber(sku.untaxedPrice * inventoryUnitDeno / (inventoryUnitMole * priceTimes)).dp(6).toNumber();
      if (untaxedSalePrice || sku.poItemExtend && ![null, '', undefined, NaN].includes(sku.poItemExtend.untaxedSalePrice) && editChange.length > 0) {
        const untaxedSalePrice1 = Number(untaxedSalePrice || sku.poItemExtend.untaxedSalePrice || 0)
        const amount = untaxedSalePrice1 - grossMarginUntaxedPrice || 0;
        grossMarginTotal = Number(acc.grossMarginTotal) + amount * itemQuantity
        untaxedSaleTotal = Number(acc.untaxedSaleTotal) + untaxedSalePrice1 * itemQuantity
      }
      return {
        grossMarginTotal,
        untaxedSaleTotal
      }
    }, { grossMarginTotal: 0, untaxedSaleTotal: 0 })

    grossMargin = new BigNumber(result1.grossMarginTotal).div(result1.untaxedSaleTotal || 1).times(100).dp(2).toNumber()
  } else { // 非直发订单毛利率
    result1 = itemList.filter(taxedTotalFilterAddFreeFun).reduce((acc, sku) => {
      const { untaxedSalePrice, itemQuantity, inventoryUnitDeno = 1, inventoryUnitMole = 1, priceTimes = 1 } = sku
      // 未税采购价（转换为销售单位）
      const grossMarginUntaxedPrice = new BigNumber(sku.untaxedPrice * inventoryUnitDeno / (inventoryUnitMole * priceTimes)).dp(6).toNumber();
      if (untaxedSalePrice || sku.poItemExtend && ![null, '', undefined, NaN].includes(sku.poItemExtend.untaxedSalePrice) && editChange.length > 0) {
        const untaxedSalePrice1 = Number(untaxedSalePrice || sku.poItemExtend.untaxedSalePrice || 0)
        const amount = untaxedSalePrice1 - grossMarginUntaxedPrice || 0;
        grossMarginTotal = Number(acc.grossMarginTotal) + amount / untaxedSalePrice1 * itemQuantity
      }
      const newValue = {
        grossMarginTotal: grossMarginTotal
      }
      return newValue
    }, { grossMarginTotal: 0 })

    grossMargin = new BigNumber(result1.grossMarginTotal).div(allItemQuantity).times(100).dp(2).toNumber()
  }

  result.taxTotal = new BigNumber(result.taxedTotal).minus(result.unTaxedTotal).dp(2).toNumber()
  result.taxedTotal = new BigNumber(result.taxedTotal).dp(2).toNumber()
  result.unTaxedTotal = new BigNumber(result.unTaxedTotal).dp(2).toNumber()
  console.log('grossMarginTotal1', 'grossMarginTotal', grossMargin, 'allItemQuantity', allItemQuantity, editChange.length === 0 && data.poExtend && data.poExtend.lastGrossMargin && allItemQuantity)
  result.grossMarginTotal = (editChange.length === 0 && data.poExtend && data.poExtend.lastGrossMargin && allItemQuantity) ? data.poExtend.lastGrossMargin : grossMargin
  console.log('grossMarginTotal2', ' grossMarginTotal', grossMargin, 'poExtend', data.poExtend, 'editChangeeditChange', editChange)
  return result
}

/**
 * @description 删除行前二次确认提示信息
 * @param {*} data po数据
 * @param {*} orderType po类型
 * @returns string
 */
export function getAssignInfo(data, orderType) {
  const allMsgArr = []
  data.filter(filterFun).forEach(item => {
    const msgArr = []
    const { itemNo } = item
    chargeProps.forEach(prop => {
      if (prop && item[`${prop}Amount`] && item[`${prop}Currency`] && item[`${prop}SupplierNo`]) {
        const propName = chargeAttrs[prop]
        msgArr.push(`${propName}${item[`${prop}Amount`]} ${item[`${prop}Currency`]} ，供应商编码为 ${item[`${prop}SupplierNo`]}`)
      }
    })
    // 返利
    if (item.shareRebatePOItemList) {
      msgArr.push('返利')
    }
    // 杂费
    if (item.sundryAmount) {
      msgArr.push(`杂费${item.sundryAmount}`)
    }
    if (msgArr.length > 0) {
      msgArr.unshift(`项目行${itemNo}已分摊：`)
      allMsgArr.push(...msgArr)
    }
  })
  if (allMsgArr.length > 0) {
    const msg = isZ002PO(orderType)
      ? '删除项目行后，抬头关税、国际运费、报关杂费、消费税、滞报金、保险费、其他费将更新为未删除行之和，您可手工将相关金额在抬头重新分摊或直接维护到其他项目行。'
      : '删除项目行后，抬头关税、国际运费、报关杂费、消费税、滞报金将更新为未删除行之和，您可手工将相关金额在抬头重新分摊或直接维护到其他项目行。'
    allMsgArr.push(msg);
    allMsgArr.push('请确认是否删除？')
  }
  return allMsgArr.length > 0 ? allMsgArr.join('<br>') : ''
}

/**
 * @description 把po价费相关字段的默认值由null改为0， 头上新增价费字段时需要补充在函数中
 * @param {*} data - po
 */
export function formatPurchaseData(data) {
  const poPriceAttrs = [
    'shareShippingAmount', 'shareDiscountAmount', 'shareShipping', 'shareDiscount', 'shareInitialShippingAmount', 'shareInitialShipping',
    'otherAmount', 'premiumAmount', 'latePaymentAmount', 'saleTaxAmount', 'intlShippingAmount', 'customsFeeAmount', 'tariffAmount', 'sundryAmount',
    'other', 'premium', 'latePayment', 'saleTax', 'intlShipping', 'customsFee', 'tariff', 'sundry'
  ]

  const res = cloneDeep(data)
  poPriceAttrs.forEach(key => {
    if (res[key] === null) res[key] = 0
  })

  if (res.itemList && res.itemList.length) {
    res.itemList = res.itemList.map(item => {
      return formatPurchaseItemData(item)
    })
  }

  return res
}

/**
 * @description 把po行上价费相关字段默认值由null改为0， 行上新增价费字段时需要补充在函数中
 * @param {*} data - poItem
 */
export function formatPurchaseItemData(data) {
  const poItemPriceAttrs = [
    'receiveQuantity', 'taxedPrice', 'untaxedPrice', 'taxedTotalAmount', 'untaxedTotalAmount',
    'shippingAmount', 'discountAmount', 'thisShippingAmount', 'thisDiscountAmount', 'initialItemShippingAmount', 'thisInitialItemShippingAmount',
    'otherAmount', 'premiumAmount', 'latePaymentAmount', 'saleTaxAmount', 'intlShippingAmount', 'customsFeeAmount', 'tariffAmount', 'sundryAmount', 'rebateAmount'
  ]
  const res = cloneDeep(data)
  poItemPriceAttrs.forEach(key => {
    if (res[key] === null) res[key] = 0
  })
  return res
}

export function rowEditChange(data) {
  console.log(data);
  try {
    const res = {
      ...data,
      editChange: true
    }
    return res
  } catch (error) {
    console.log(error);
  }
}
