/* eslint-disable */
const XLSX = require('xlsx')
const saveAs = require('file-saver').saveAs

export function toExcel(data, name = new Date().toISOString().split('T')[0]) {
  const keys = Object.keys(data[0])
  const firstRow = {}
  keys.forEach(key => firstRow[key] = key)

  const content = {}
  const t = Date.now()
  const sheetsData = data.map((item, rowIndex) => {
    return keys.map((key, columnIndex) => {
      return {
        value: item[key],
        position: (columnIndex > 25 ? getCharCol(columnIndex) : String.fromCharCode(65 + columnIndex)) + (rowIndex + 1)
      }
    })
  }).reduce((prev, next) => {
    return prev.concat(next)
  })
  console.log(`time cost: ${Date.now() - t}ms`)
  sheetsData.forEach((item, index) => {
    content[item.position] = { v: item.value }
  })
  console.log(content)
  //设置区域,比如表格从A1到D10,SheetNames:标题，
  var coordinate = Object.keys(content);
  var workBook = {
    SheetNames: ['sheet1'],
    Sheets: {
      'sheet1': Object.assign({}, content, { '!ref': coordinate[0] + ':' + coordinate[coordinate.length - 1] }),
    }
  };
  //这里的数据是用来定义导出的格式类型
  var excelData = XLSX.write(workBook, { bookType: 'xlsx', bookSST: false, type: 'binary' });
  var blob = new Blob([string2ArrayBuffer(excelData)], {type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,"});
  if (!/\.xlsx/.test(name)) {
    name = name + '.xlsx'
  }
  console.log(name)
  saveAs(blob, name);
}
function string2ArrayBuffer(s) {
  var buf = new ArrayBuffer(s.length);
  var view = new Uint8Array(buf);
  for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
  return buf;
}
// 将指定的自然数转换为26进制表示。映射关系：[0-25] -> [A-Z]。
function getCharCol(n) {
  let temCol = '',
    s = '',
    m = 0
  while (n > 0) {
    m = n % 26 + 1
    s = String.fromCharCode(m + 64) + s
    n = (n - m) / 26
  }
  return s
}
