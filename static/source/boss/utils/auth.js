/* eslint-disable no-unused-vars */
import store from '@/store'
import { safeRun } from '@/utils/index'

export function findTopRole(allRoleList) {
  if (!Array.isArray(allRoleList)) allRoleList = []
  if (allRoleList.find(item => item === 'PMS管理员')) return 'PMS管理员'
  if (allRoleList.find(item => item === 'PMS采购总监')) return 'PMS采购总监'
  if (allRoleList.find(item => item === 'PMS采购经理')) return 'PMS采购经理'
  if (allRoleList.find(item => item === 'PMS采购员')) return 'PMS采购员'
  if (allRoleList.find(item => item === 'PMS票务')) return 'PMS票务'
  if (allRoleList.find(item => item === 'PMS运营')) return 'PMS运营'
  if (allRoleList.find(item => item === 'PMS仓管')) return 'PMS仓管'
  let item = allRoleList.find(item => /PMS/.test(item))
  return item
}

const authCacheMap = new Map()

export function getButtonAuth(menuName, buttonName) {
  let menu = []
  let authConfig = {}
  safeRun(() => {
    menu = store.state.menu
    authConfig = store.state.orderPurchase.authConfig
  })
  // 关闭权限控制直接返回true
  if (authConfig.enable === false) return true
  const cacheKey = `${menuName}_${buttonName}`
  if (authCacheMap.has(cacheKey)) return authCacheMap.get(cacheKey)
  if (!menuName || !menu || !menu.length) return false

  // 菜单
  const menuFilter = filterFun({
    type: 'MENU',
    name: menuName
  })
  const menuFound = findResource(menu, menuFilter)
  // safeRun(() => {
  //   window._console.red(menuName, menuFound)
  // })
  if (!menuFound || !menuFound.children || !menuFound.children.length) return
  const buttonFilter = filterFun({
    type: 'BUTTON',
    name: buttonName
  })
  const buttonFound = findResource(menuFound.children, buttonFilter)
  // safeRun(() => {
  //   window._console.blue(buttonName, buttonFound)
  // })
  authCacheMap.set(cacheKey, !!buttonFound)
  if (buttonFound) return true
}
function filterFun(condition) {
  return (item) => item.type === condition.type && item.name === condition.name
}
function findResource(menu, filter) {
  let ret = menu.find(filter)
  if (!ret) {
    menu.forEach(subMenu => {
      if (subMenu.children) {
        if (!ret) {
          ret = findResource(subMenu.children, filter)
        }
      }
    })
  }
  return ret
}
