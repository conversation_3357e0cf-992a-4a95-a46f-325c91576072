/**
 * maxFixed Price
 * @param {number} number
 * @param {number} radix
 * @param {boolean} keepRadix
 * @return {boolean | string} formated number
 */
export function toFixedByRadix (number, radix = 6, keepRadix = false) {
  if (typeof (number) === 'string') { number = Number(number) }
  const base = Math.pow(10, radix)
  const accNumber = Math.round(number * base) / base
  let ret = accNumber
  if (keepRadix) { ret = accNumber.toFixed(String(radix)) }
  return ret
}

export function formatCurrencySymbol(currency) {
  if (!currency) return ''
  const symbolMap = {
    USD: '$',
    CNY: '￥',
    EUR: '€',
    MYR: 'RM',
    THB: '฿',
    VND: '₫',
    SGD: 'S$',
    TWD: 'NT$',
    HKD: 'HK$'
  }
  return symbolMap[currency] || ''
}
