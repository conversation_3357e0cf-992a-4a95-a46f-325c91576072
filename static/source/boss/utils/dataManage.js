import { getType as getObjType } from '@/utility/helper'
// 清除对象内值为空的属性
export function removeProperty(obj) {
  let temp = Object.keys(obj).reduce((pre, cur) => {
    let val = obj[cur]
    let jsonVal = JSON.stringify(val)
    let type = getObjType(val)
    if (type === 'string') val = val.trim()
    if ((val !== '' && type !== 'undefined' && type !== 'null' && jsonVal !== '[]' && jsonVal !== '{}')) pre[cur] = val
    return pre
  }, {})
  return temp
}

// 同步下载
export function asyncDownload(url, callback = null) {
  if (!url) console.warn('下载链接不能为空')
  let aLink = document.createElement('a')
  aLink.setAttribute('href', url)
  aLink.setAttribute('id', 'camnpr')
  document.body.appendChild(aLink)
  aLink.click()
  document.body.removeChild(aLink)
  callback && callback()
}

// 获取其他系统跳转链接
export function getOtherSystemUrl(type) {
  const fetch = {
    // 供应商
    provider: {
      pro: 'https://biw.zkh360.com/supplierDetail/',
      uat: 'https://vcadmin-uat.zkh360.com/supplierDetail/',
      local: 'https://vcadmin-uat.zkh360.com/supplierDetail/'
    },
    // 官网
    web: {
      pro: 'https://www.zkh.com/item/',
      uat: 'https://webuat.zkh.com/item/',
      local: 'https://webuat.zkh.com/item/'
    },
    // 商品中心
    cc: {
      pro: 'https://pc.zkh360.com/',
      uat: 'https://pc-uat.zkh360.com/',
      local: 'https://pc-uat.zkh360.com/'
    }
  }
  const { env = '' } = window.CUR_DATA
  let url = (fetch[type] && fetch[type][env]) || ''
  if (!url) console.warn(`${type}服务地址不存在`)
  return url
}

/**
 * 删除对象中指定属性
 * pro为需要删除的属性，类型可以为数组或字符
 */
export function delProperty(obj = {}, pro) {
  let proArr = getObjType(pro) === 'array' ? pro : [pro]
  return Object.keys(obj).reduce((pre, cur) => {
    if (Object.prototype.hasOwnProperty.call(obj, cur) && !proArr.includes(cur)) {
      pre[cur] = obj[cur]
    }
    return pre
  }, {})
}
