/* eslint-disable no-unused-vars */
import store from '@/store'
import { safeRun, throttle } from '@/utils/index'

const menuMap = {
  standarAndDdirect: '采购管理',
  trackingOrder: '采购管理',
  inboundDeliveryDetail: '采购管理',
  inboundDeliveryCreate: '采购管理',
  POList: '采购管理',
  PODetail: '采购管理',
  POCreate: '创建采购单',
  POAudit: '采购管理',
  OutSourcingClose: '采购管理',
  InventoryManagementList: '库存管理',
  InventoryManagementDetail: '库存管理',
  InventoryManagementReport: '库存管理',
  CargoRightsList: '库存管理'
}

export function findTopRole(allRoleList) {
  if (!Array.isArray(allRoleList)) allRoleList = []
  if (allRoleList.find(item => item === 'PMS管理员')) return 'PMS管理员'
  if (allRoleList.find(item => item === 'PMS采购总监')) return 'PMS采购总监'
  if (allRoleList.find(item => item === 'PMS采购经理')) return 'PMS采购经理'
  if (allRoleList.find(item => item === 'PMS采购员')) return 'PMS采购员'
  if (allRoleList.find(item => item === 'PMS票务')) return 'PMS票务'
  if (allRoleList.find(item => item === 'PMS运营')) return 'PMS运营'
  if (allRoleList.find(item => item === 'PMS仓管')) return 'PMS仓管'
  let item = allRoleList.find(item => /PMS/.test(item))
  return item
}

function userTopAuth(authMap, button, userRole) {
  const topRole = findTopRole(userRole)
  if (authMap[topRole]) {
    // console.log(authMap[topRole][button])
    return authMap[topRole][button]
  }
  return false
}
// 采购订单审批权限
function getPOAudit(button, userRole) {
  const gateList = ['PMS管理员', 'PMS采购经理', 'PMS采购总监']
  const superList = ['PMS管理员']
  if (userRole.every(role => !gateList.includes(role))) return false
  // 没有'PMS管理员', 'PMS采购经理', 'PMS采购总监' 角色，直接隐藏所有按钮
  if (userRole.some(role => superList.includes(role))) return true
  // 管理员显示所有按钮
  const authMap = {
    'PMS采购经理': { '一审待审批': true, '二审待审批': false },
    'PMS采购总监': { '一审待审批': false, '二审待审批': true }
  }
  if (userRole.some(role => role === 'PMS采购经理') && userRole.some(role => role === 'PMS采购总监')) {
    return { '一审待审批': true, '二审待审批': true }
  }
  return userTopAuth(authMap, button, userRole)
}
// 一键转标准，一键转直发权限
function getStandarAndDdirect(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员', 'PMS值班账号']
  const authMap = {
    'PMS运营': { '一键转标准': false, '一键转直发': false }
  }
  if (userRole.some(role => superList.includes(role))) return true

  return userTopAuth(authMap, button, userRole)
}
const throttleLog = throttle((...args) => console.log(...args), 800)
// 跟单报表权限
function getTrackingOrder(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员', 'PMS值班账号']
  const authMap = {
    'PMS票务': { '导出明细': true, '保存修改': false, '批量更新跟单': false },
    'PMS运营': { '导出明细': false, '保存修改': false, '批量更新跟单': false },
    'PMS仓管': { '导出明细': false, '保存修改': false, '批量更新跟单': false }
  }
  if (userRole.some(role => superList.includes(role))) {
    throttleLog(button, findTopRole(userRole))
    return true
  }
  return userTopAuth(authMap, button, userRole)
}
// 内向交货单详情权限
function getInboundDeliveryDetail(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员', 'PMS值班账号']
  const authMap = {
    'PMS票务': { '收货入库': false, '修改交货单': false, '取消过账': false, '取消交货单': false },
    'PMS运营': { '收货入库': false, '修改交货单': false, '取消过账': false, '取消交货单': false },
    'PMS仓管': { '收货入库': false, '修改交货单': false, '取消过账': false, '取消交货单': false }
  }
  if (userRole.some(role => superList.includes(role))) return true
  return userTopAuth(authMap, button, userRole)
}
// 内向交货单详情权限
function getInboundDeliveryCreate(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员', 'PMS值班账号']
  const authMap = {}
  if (userRole.some(role => superList.includes(role))) return true
  return userTopAuth(authMap, button, userRole)
}
// 采购订单列表权限
function getPOList(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员']
  const authMap = {
    'PMS值班账号': { '创建内向交货单': false, '批量修改订单': false, '批量创建订单': false },
    'PMS票务': { '创建内向交货单': false, '批量修改订单': false, '批量创建订单': false },
    'PMS运营': { '创建内向交货单': false, '批量修改订单': false, '批量创建订单': false },
    'PMS仓管': { '创建内向交货单': false, '批量修改订单': false, '批量创建订单': false }
  }
  if (userRole.some(role => superList.includes(role))) return true
  return userTopAuth(authMap, button, userRole)
}
// 采购订单详情权限
function getPODetail(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员']
  const authMap = {
    'PMS票务': { '创建内向交货单': false, '修改': false, '打印': false, '删除': false, '恢复': false, '审批金额': false },
    'PMS运营': { '创建内向交货单': false, '修改': false, '打印': false, '删除': false, '恢复': false, '审批金额': false },
    'PMS仓管': { '创建内向交货单': false, '修改': false, '打印': false, '删除': false, '恢复': false, '审批金额': false, '收货与发票': false, '价费信息': false }
  }
  if (userRole.some(role => superList.includes(role))) return true
  return userTopAuth(authMap, button, userRole)
}
// 采购订单创建/编辑权限
function getPOCreate(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员', 'PMS值班账号']
  const authMap = {}
  if (userRole.some(role => superList.includes(role))) return true
  return userTopAuth(authMap, button, userRole)
}
// 委外关单权限
function getOutSourcingClose(button, userRole) {
  return simpleAuth(button, userRole)
}
// 库存申请单列表权限
function getInventoryManagementList(button, userRole) {
  return simpleAuth(button, userRole)
}
// 库存申请单详情权限
function getInventoryManagementDetail(button, userRole) {
  return simpleAuth(button, userRole)
}
// 库存申请单明细权限
function getInventoryManagementReport(button, userRole) {
  return simpleAuth(button, userRole)
}
// 寄售货权转移权限
function getCargoRightsList(button, userRole) {
  return simpleAuth(button, userRole)
}

// 简单判断是否有采购权限显示按钮
function simpleAuth(button, userRole) {
  const superList = ['PMS采购员', 'PMS采购经理', 'PMS采购总监', 'PMS管理员']
  if (userRole.some(role => superList.includes(role))) return true
  return false
}

export function setButtonRole(page, button) {
  let menu = []
  let userRole = []
  let authConfig = {}
  safeRun(() => {
    menu = store.state.menu
    userRole = store.state.userRole
    authConfig = store.state.orderPurchase.authConfig
  })
  // 关闭权限控制直接返回true
  if (authConfig.enable === false) return true
  const firstMenuField = menuMap[page]
  if (firstMenuField || !menu || !menu.length) return false
  const router = menu.find(item => item.name === firstMenuField)
  if (!router || !router.children) return false
  // 先确定一级菜单以降低button名称碰撞概率
  const filter = filterFun({
    type: 'MENU',
    name: '采购订单列表'
  })
  return findAuthByButtonName(router.children, filter)
  // switch (page) {
  //   case 'standarAndDdirect': return getStandarAndDdirect(button, userRole);
  //   case 'trackingOrder': return getTrackingOrder(button, userRole);
  //   case 'inboundDeliveryDetail': return getInboundDeliveryDetail(button, userRole);
  //   case 'inboundDeliveryCreate': return getInboundDeliveryCreate(button, userRole);
  //   case 'POList': return getPOList(button, userRole);
  //   case 'PODetail': return getPODetail(button, userRole);
  //   case 'POCreate': return getPOCreate(button, userRole);
  //   case 'POAudit': return getPOAudit(button, userRole);
  //   case 'OutSourcingClose': return getOutSourcingClose(button, userRole);
  //   case 'InventoryManagementList': return getInventoryManagementList(button, userRole);
  //   case 'InventoryManagementDetail': return getInventoryManagementDetail(button, userRole);
  //   case 'InventoryManagementReport': return getInventoryManagementReport(button, userRole);
  //   case 'CargoRightsList': return getCargoRightsList(button, userRole);
  // }
}
function filterFun(condition) {
  return (item) => item.type === condition.type && item.name === condition.name
}
function findAuthByButtonName(menu, filter) {
  // 递归查找一级目录下类型为button, 指定名称的配置
  const getButton = menu.find(filter)
  if (getButton) return true
  if (getButton && getButton.children) return findAuthByButtonName(getButton.children, filter)
  return false
}
