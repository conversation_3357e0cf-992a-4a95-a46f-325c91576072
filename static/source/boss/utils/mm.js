import { safeRun } from '@/utils/index'
import { MessageBox } from 'element-ui';
import store from '@/store'
import { getUserInfo, getCompanyInfo, needSplitOrder } from '@/api/mm'
import { isArray } from 'lodash';

export function getFactoryList(companyFactoryList) {
  const factoryList = []
  if (companyFactoryList && companyFactoryList.length > 0) {
    companyFactoryList.forEach(item => {
      if (item.factoryList && item.factoryList.length > 0) {
        item.factoryList.forEach(fac => {
          factoryList.push({
            value: fac.factoryCode,
            name: fac.factoryName
          })
        })
      }
    })
  }
  return factoryList
}

export function delDuplicateProp(arr, prop) {
  let tmp = {}
  let ret = []
  for (let item of arr) {
    if (!tmp[item[prop]]) {
      ret.push(item)
      tmp[item[prop]] = true
    }
  }
  return ret
}
export function buildOptions (prop) {
  let dictList = []
  let warehouseList = []
  let companyFactoryList = []
  let purchaseList = []
  safeRun(() => {
    dictList = store.state.orderPurchase.dictList
    warehouseList = store.state.orderPurchase.warehouseList
    companyFactoryList = store.state.orderPurchase.companyFactoryList
    purchaseList = store.state.orderPurchase.purchaseList
  })
  let mapList = []
  safeRun(() => {
    mapList = dictList[prop]
    if (/warehouseLocation/gi.test(prop)) {
      mapList = warehouseList.map(item => ({
        value: item.warehouseLocationCode,
        name: item.warehouseLocationName
      }))
    }
    if (/factoryCode/gi.test(prop)) {
      mapList = getFactoryList(companyFactoryList)
    }
    if (/factoryCode2/gi.test(prop)) {
      mapList = companyFactoryList.reduce((prev, next) => {
        if (Array.isArray(next.factoryList)) {
          const tmpList = next.factoryList.map(item => ({ value: item.factoryCode, name: item.factoryName }))
          prev.push(...tmpList)
        }
        return prev
      }, [])
    }
    if (/purchaseGroup/gi.test(prop)) {
      mapList = purchaseList.map(item => ({
        name: item.userName,
        value: item.groupCode,
        securityUsername: item.securityUsername
      }))
    }
    mapList = delDuplicateProp(mapList, 'value')
  })
  // if (prop === 'iaoGeneralLedger') {
  //   console.log(mapList, dictList)
  // }
  return mapList || []
}
export function readNameFromDic (prop, data) {
  let dictList = []
  let warehouseList = []
  let companyFactoryList = []
  let purchaseList = []
  safeRun(() => {
    dictList = store.state.orderPurchase.dictList
    warehouseList = store.state.orderPurchase.warehouseList
    companyFactoryList = store.state.orderPurchase.companyFactoryList
    purchaseList = store.state.orderPurchase.purchaseList
  })
  let item = { name: '' }
  safeRun(() => {
    let mapList = []
    if (/warehouseLocation/gi.test(prop)) {
      mapList = warehouseList.map(item => ({
        value: item.warehouseLocationCode,
        name: item.warehouseLocationName
      }))
    }
    if (/factoryCode/gi.test(prop)) {
      mapList = getFactoryList(companyFactoryList)
    }
    if (/purchaseGroup/gi.test(prop)) {
      mapList = purchaseList.map(item => ({
        name: item.userName,
        value: item.groupCode
      }))
    }
    // eslint-disable-next-line
    let tmpOther = mapList.find(x => x.value == data)
    if (tmpOther) item = tmpOther
    if (!dictList[prop]) return
    // eslint-disable-next-line
    let tmp = dictList[prop].find(x => x.value == data)
    if (tmp) item = tmp
  })
  return item.name
}
export function mapKeyToValue (prop, value, dictList) {
  if (isArray(value)) return mapListToValue(prop, value, dictList)
  let ret = ''
  safeRun(() => {
    // eslint-disable-next-line
    const item = dictList[prop].find(item => item.value == value)
    ret = item.name
  })
  return ret
}

export function mapListToValue(prop, valueList, dictList) {
  let ret = []
  safeRun(() => {
    (valueList || []).forEach(value => {
      // eslint-disable-next-line
      const item = dictList[prop].find(item => item.value == value)
      ret.push(item.name)
    })
  })
  return ret.join('、')
}

export function labelColon (label, colon = '：') {
  let ret = label
  safeRun(() => {
    label = label.replace(/：|:/, '')
    ret = label + colon
  })
  return ret
}
function notNully(val) {
  return val !== undefined && val !== null
}
function isBoolean (val) {
  return val === true || val === false
}
export function formatString (type, ...args) {
  let ret = args[0]
  if (type === 1) {
    ret = notNully(ret) && ('  ' + ret)
  }
  if (type === 2) {
    ret = notNully(ret) && (ret + '  ')
  }
  if (type === 3) {
    if (notNully(args[0]) && notNully(args[1])) {
      ret = args[0] + '  ' + args[1]
    } else {
      ret = args[0] || args[1]
    }
  }
  ret = isBoolean(ret) ? '' : ret
  return ret
}

export function detailStatus () {
  return 'readonly'
}

export function filterInProp (array, prop) {
  return function(item) {
    return array.some(arr => arr[prop] === item[prop])
  }
}

export function mapToProps (source, sourceProp, destProp, propList) {
  let ret = ''
  safeRun(() => {
    let item = propList.find(prop => prop[sourceProp] === source)
    if (item) {
      ret = item[destProp]
    }
  })
  return ret
}

export function sortColumn (res, columnSetting, props = {}) {
  // 复写顺序，以及列宽等属性
  let tmp = []
  for (let item of columnSetting) {
    if (typeof item === 'string') {
      let fTmp = res.find(field => field.name === item)
      if (fTmp) tmp.push({ ...fTmp, ...props })
    } else if (typeof item === 'object') {
      const { name } = item
      let fTmp = res.find(field => field.name === name)
      if (fTmp) tmp.push({ ...fTmp, ...item, ...props })
    }
  }
  return tmp
}

export async function getAllDictList (vm) {
  const {
    dictList, companyFactoryList, warehouseList, purchaseList, authConfig
  } = vm.$store.state.orderPurchase
  const pList = []
  if (!dictList || Object.keys(dictList).length === 0) {
    pList.push(vm.$store.dispatch('orderPurchase/queryDictList'))
  }
  if (!companyFactoryList || companyFactoryList.length === 0) {
    pList.push(vm.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
  }
  // if (!disableCompanyCodeList || disableCompanyCodeList.length === 0) {
  //   pList.push(vm.$store.dispatch('orderPurchase/queryDisableCompanyCodeList'))
  // }
  if (!purchaseList || purchaseList.length === 0) {
    pList.push(vm.$store.dispatch('orderPurchase/queryPurchaseGroup'))
  }
  if (!warehouseList || warehouseList.length === 0) {
    pList.push(vm.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
  }
  if (!authConfig || Object.keys(authConfig).length === 0) {
    pList.push(vm.$store.dispatch('orderPurchase/getMMAuthConfig'))
  }
  return Promise.all(pList)
}

export function registerReCalcStyle(listener) {
  safeRun(() => {
    document.querySelector('button.el-button.collapse-icon')
      .addEventListener('click', listener, false)
  })
}
export function unRegisterReCalcStyle(listener) {
  safeRun(() => {
    document.querySelector('button.el-button.collapse-icon')
      .removeEventListener('click', listener)
  })
}

export const submitErrorHandler = (errMsg = '', data, msg) => {
  if (data && data.failReasonList) {
    const { failReasonList = [] } = data
    if (failReasonList.length > 0) {
      const title = reason => reason.index === -1 ? '头部:' : `第${(reason.index || 0) + 1}行:`
      errMsg += failReasonList.map(reason => title(reason) + `${reason.reason}`).join('<br>')
    }
  } else if (msg) {
    errMsg += msg
  }
  const scrollWrapper = data => `<div style="max-height: 300px;max-width: 400px;overflow:auto;">${data}</div>`
  MessageBox.alert(scrollWrapper(errMsg), '操作失败', {
    type: 'error',
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定'
  })
}
export function moveInArray(array, value, prop, newIndex) {
  const oldIndex = array.findIndex(item => item[prop] === value)
  if (!~oldIndex) return
  const oldItem = array[oldIndex]
  const newItem = array[newIndex]
  array[newIndex] = oldItem
  array[oldIndex] = newItem
}

// 根据用户获取默认公司
export async function getUserCompany () {
  const res = await getUserInfo({
    username: window.CUR_DATA.user && window.CUR_DATA.user.name
  })
  const companyInfoList = await getCompanyInfo()
  const defaultCompany = (companyInfoList.find(item => item.subCompanyId === res.subCompany) || {}).code
  return defaultCompany
}

// 获取是否需要拆单的提示
export async function getSplitOrderTips(self, data) {
  const loading = self.$loading({
    background: 'rgba(0, 0, 0, 0.5)',
    lock: true
  })
  const res = await needSplitOrder(data)
  loading.close()
  if (res && res.code === 0 && res.data) {
    if (res.data.needSplit) {
      try {
        await self.$confirm(`<div class="btb-wrapper">${res.data.msg || ''}</div>`, '自动拆单确认', {
          confirmButtonText: '继续',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
        return Promise.resolve(res.data.needSplit)
      } catch (err) {
        console.log(err)
        return Promise.reject(err)
      }
    }
  } else {
    self.$notify.error('获取自动拆单信息失败！')
  }
}
