/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string}
 */
import qs from 'qs'
import lodash from 'lodash'
import clip from './clipboard'
import Cookies from 'js-cookie'

export function parseTime (time, cFormat) {
  if (arguments.length === 0) {
    return null
  }
  if (!time) {
    return ''
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
      time = parseInt(time)
    }
    if (typeof time === 'number' && time.toString().length === 10) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const timeStr = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return timeStr
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime (time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject (url) {
  const index = url.lastIndexOf('?')
  if (index < 0) {
    return {}
  }
  url = url == null ? window.location.href : url
  const search = url.substring(index + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getHashObject (url) {
  const index = url.lastIndexOf('#')
  if (index < 0) {
    return {}
  }
  url = url == null ? window.location.href : url
  const hash = url.substring(index + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  hash.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength (str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xdc00 && code <= 0xdfff) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray (actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param (json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj (url) {
  const search = url.split('?')[1]
  if (!search) {
    return {}
  }
  return JSON.parse(
    '{"' +
      decodeURIComponent(search)
        .replace(/"/g, '\\"')
        .replace(/&/g, '","')
        .replace(/=/g, '":"')
        .replace(/\+/g, ' ') +
      '"}'
  )
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text (val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge (target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass (element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime (type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce (func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function (...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone (source) {
  return lodash.cloneDeep(source)
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr (arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString () {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass (ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass (ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass (ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}
export function toTree (data) {
  data.forEach(item => {
    delete item.children
  })
  let val = []
  data.forEach(item => {
    let parent = data.find(innerItem => innerItem.value === item.parentCode)
    if (parent) {
      (parent.children || (parent.children = [])).push(parent.parentCode ? toTree(item) : item)
    } else {
      val.push(item)
    }
  })
  return val
}
/**
 * 树形结构遍历
 * @param {根节点} Node
 * @param {子节点名称} childName
 * @param {回调函数} cb
 */
export function EachTree (Node, childName, cb) {
  if (Node) {
    const newNode = cb(Node)
    if (newNode[childName]) {
      newNode[childName].forEach(item => {
        item = EachTree(item, childName, cb)
      })
    }
    return newNode
  } else {
    return null
  }
}

/**
 * 通过hash切换的方式，切tab时更改hash参数名
 * @param {query参数名} key
 * @param {query参数值} value
 */
export function changeQueryByTab (key, value) {
  // const hash = window.location.hash
  const hasQuery = window.location.href.indexOf('?') !== -1
  const hasQueryTab = window.location.href.indexOf('?' + key + '=') !== -1
  let newkey = '?' + key + '='
  if (!hasQuery || hasQueryTab) {
    newkey = '?' + key + '='
  } else {
    newkey = '&' + key + '='
  }
  if (window.location.href.indexOf(newkey) !== -1) {
    window.location.href =
      window.location.href.split(newkey)[0] + newkey + value
  } else {
    window.location.href = window.location.href + newkey + value
  }
}

export function formatPrice (value = 0, fractionDigits = 6) {
  const num = Number(value)
  const roundNum =
    Math.round((num + Number.EPSILON) * Math.pow(10, fractionDigits + 1)) /
    Math.pow(10, fractionDigits + 1)
  return roundNum.toFixed(fractionDigits)
}

export function buildSoDetailLink ({ query, hash, tagName }) {
  const queryParam = {
    soNo: query.soNo,
    soVoucherId: query.sapOrderNo,
    id: query.id,
    refresh: query.refresh,
    orderNo: query.orderNo,
    tagName
  }
  const key = (query.soNo || '') + (query.sapOrderNo || '')
  // soNo=${soNo}&soVoucherId=${sapOrderNo}&id=${id}&refresh=${refresh}
  let url = `/orderSale/formal/detail/${key}?` + qs.stringify(queryParam)
  if (hash) {
    url += '#' + qs.stringify(hash)
  }
  return url
}

// 数组元素相等
export function ArrayElementEqual (left, right) {
  if (!left || !right) {
    return false
  }

  // compare lengths - can save a lot of time
  if (left.length !== right.length) {
    return false
  }

  for (var i = 0, l = left.length; i < l; i++) {
    // Check if we have nested arrays
    if (left[i] instanceof Array && right[i] instanceof Array) {
      // recurse into the nested arrays
      if (!ArrayElementEqual(left[i], right[i])) {
        return false
      }
    } else if (left[i] !== right[i]) {
      // Warning - two different object instances will never be equal: {x:20} != {x:20}
      return false
    }
  }
  return true
}

// 从获取枚举类型的文案
export function getLabelByValue (value = '', arr) {
  const items = value ? value.split(',') : []
  if (items.length === 1) {
    const labelItem = arr && arr.length ? arr.find(item => {
      return item.value === value
    }) : []
    return labelItem ? labelItem.label || '' : ''
  } else {
    const labelItem = arr && arr.length ? arr.filter(item => items.indexOf(item.value) >= 0) : [];
    return labelItem.map(item => item.label).join(',')
  }
}

export function isNumber (val) {
  return /^([-+]?\d+)(\.\d+)?$/.test(val)
}

export function copyToClipboard (text, event) {
  clip(text, event, () => {
    const content = '复制成功'
    this.$message({
      message: content,
      type: 'success'
    })
  })
}
export function trimParams (obj) {
  for (let key in obj) {
    if (obj[key] && typeof obj[key] === 'string') {
      obj[key] = obj[key].trim()
    }
  }
}

export function throttle (func, delay = 800) {
  let timer
  return function () {
    if (timer) return
    timer = setTimeout(() => {
      func.apply(this, arguments)
      timer = null
    }, delay)
  }
}
export function spDebounce (func, delay = 800, context = null) {
  let timer
  return function () {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(context, arguments)
      timer = null
    }, delay)
  }
}

/**
 * @name debugConsoleManager
 * @desc default silent: true will not show _console.log log record
 * open with localStorage.debugConsole = true
 * @param {*} config
 */
function DebugLogger (config) {
  this.config = config || {}
}
Object.keys(console)
  .forEach(handle => {
    DebugLogger.prototype[handle] = function (...args) {
      if (this.config.silent) {
        let debug = false
        try { debug = localStorage.debugConsole } catch (err) {}
        if (debug) { console[handle](...args) }
      } else {
        console[handle](...args)
      }
    }
  })

DebugLogger.getInstance = function (config) {
  if (!DebugLogger.instance) {
    DebugLogger.instance = new DebugLogger(config)
  }
  return DebugLogger.instance
}
const defaultConfig = {
  silent: true
}
window._console = DebugLogger.getInstance(defaultConfig)
export function getCookie (cname) {
  var name = cname + '='
  var ca = document.cookie.split(';')
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i].trim()
    if (c.indexOf(name) === 0) return c.substring(name.length, c.length)
  }
  return ''
}

const whiteList = [
  'orderSale/formal/create',
  'orderSale/formal/detail',
  'orderSale/formal/edit',
  'orderSale/performanceProcessTool'
]

export const isInWhiteList = location => {
  console.log(location)
  const name = location.path || location.pathname
  if (location && name) {
    const founded = whiteList.find(item => name.indexOf(item) > -1)
    return !!founded
  }
  return false
}

export const toggleSideMenu = toggle => {
  const dom = document.querySelector('.main-side')
  const main = document.querySelector('.main-frame')
  const routerTag = document.querySelector('.header-tags')

  if (~['show', true, 'true', 1].indexOf(toggle)) {
    dom.style.display = 'block'
    routerTag.style.display = 'block'
    main.classList.remove('main-side-hidden')
  }
  if (~['hide', false, 'false', 0].indexOf(toggle)) {
    dom.style.display = 'none'
    routerTag.style.display = 'none'
    main.classList.add('main-side-hidden')
  }
  if (toggle === undefined) {
    dom.style.display = dom.style.display === 'none' ? 'block' : 'none'
  }
}
export const openNewTab = () => localStorage.getItem('open-in-new-tab') !== 'boss'
export const safeRun = (callback, context, hideError = true) => {
  let ret = null
  try {
    ret = callback.apply(context)
  } catch (err) {
    if (!hideError) {
      console.log(err)
    }
  }
  return ret
}
export const deepFlatten = (array) => [].concat(...array.map(v => Array.isArray(v) ? deepFlatten(v) : v))

export function trimString(...args) {
  args = args.map(item => {
    if (item === null || item === undefined) return ''
    return item
  })
  return args.reduce((x, y) => x + ' ' + y, '')
}

export function IsPC() {
  var userAgentInfo = navigator.userAgent;
  var Agents = ['Android', 'iPhone',
    'SymbianOS', 'Windows Phone',
    'iPad', 'iPod'];
  var flag = true;
  for (var v = 0; v < Agents.length; v++) {
    if (userAgentInfo.indexOf(Agents[v]) > 0) {
      flag = false;
      break;
    }
  }
  return flag;
}

export function sensors(name, data) {
  window['sensorsDataAnalytic201505'] && window['sensorsDataAnalytic201505'].track(name, data)
}

/**
 * @description 获取当前右上角角色详细信息
 * @returns
 */
export function getCurBizRole() {
  try {
    // localStorage.setItem是在layout组件中
    const userInfo = JSON.parse(localStorage.getItem('currentUserInfo') || '{}');
    const curBizRoleCode = Cookies.get('bizRoleCode');
    return userInfo?.bizRoleList?.find((role) => role.bizRoleCode === curBizRoleCode) || '';
  } catch (error) {
    console.log(error);
  }
}

export const Storage = (function Storage () {
  return {
    clear () {
      localStorage.clear()
    },
    removeItem: (key) => {
      localStorage.removeItem(key)
    },
    setItem: (key, value) => {
      if (typeof value === 'object') {
        try {
          value = JSON.stringify(value)
        } catch (err) {
          console.log(err)
        }
      }
      localStorage.setItem(key, value)
    },
    getItem: (key) => {
      let ret = localStorage.getItem(key)
      try {
        let tmp = JSON.parse(ret)
        ret = tmp
      } catch (err) {}
      return ret
    }
  }
})()

export function initVersion(config) {
  const { newVersion, versionKey, columKey } = config
  let version = Storage.getItem(versionKey) + ''
  if (newVersion !== version) {
    Storage.removeItem(versionKey)
    Storage.removeItem(columKey)
    Storage.removeItem('VXE_TABLE_CUSTOM_COLUMN_VISIBLE')
    Storage.setItem(versionKey, newVersion)
  }
}

// 是否为空
export const isNullOrUndefinedOrEmpty = function (val) {
  return val === '' || val === null || val === undefined
}

// 将字符串中的手机号遮盖中间4位
// 如输入：'ABC13812345678d'，则输出'ABC138****5678d'
export const coverMobileInText = text => {
  if (isNullOrUndefinedOrEmpty(text)) {
    return ''
  }
  const str = text.toString()
  return str.replace(/(^|\D+)(1\d{2})(\d{4})(\d{4})($|\D+)/, '$1$2****$4$5')
}

// 已知是手机号或座机号，区分针对一下
export const coverMobileAndLandline = phone => {
  if (isNullOrUndefinedOrEmpty(phone)) {
    return ''
  }
  const str = phone.toString()
  if (/1\d{10}/.test(str)) {
    return coverMobileInText(str)
  } else {
    // 座机号（非手机号），遮挡倒数6个数字的前4位
    return str.replace(/(\d{4})(\d{2})($|\D+)/, '****$2$3')
  }
}

// 数字添加千分位分隔符
export const numFormatThousand = (num) => {
  var res = num?.toString().replace(/\d+/, function(n) {
    return n.replace(/(\d)(?=(\d{3})+$)/g, function($1) {
      return $1 + ','
    })
  })
  return res
}

export const getSearchDataFromUrlQuery = (query) => {
  if (query instanceof Object) {
    let res = {}
    Object.entries(query).forEach(([key, value]) => {
      if (value.indexOf(',') > -1) {
        res[key] = value.split(',')
      } else {
        res[key] = value
      }
    })
    return res
  }
  return {}
}

export function formatDepartmentTree(departments) {
  if (departments && departments.sons && departments.sons.length > 0) {
    const ls = []
    departments.sons.forEach(item => {
      const children = formatDepartmentTree(item) || []
      if (children && children.length > 0) {
        ls.push({
          label: item.name,
          value: item.id,
          children
        })
      } else {
        ls.push({
          label: item.name,
          value: item.id,
          leaf: true
        })
      }
    })
    return ls
  }
}

export function routeToWorkflow(path, query = {}) {
  try {
    const origin = window.location.origin
    const queryStr = qs.stringify(query) ? '?' + qs.stringify(query, { arrayFormat: 'comma' }) : ''
    const url = path + queryStr

    if (origin.includes('local')) {
      window.open(origin.replace('3000', '9005') + url, '_blank')
    } else {
      window.open(origin + url, '_blank')
    }
  } catch (error) {
    console.log(error);
  }
}
