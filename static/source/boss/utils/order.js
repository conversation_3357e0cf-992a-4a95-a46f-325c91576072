// 订单相关业务工具方法
import { isNumber } from './'
import { deliveryNoteTips, getBackToBackRemindInfos } from '@/api/orderSale'

export function getQuantityUnitName(value, dictList) {
  if (value && isNumber(value)) {
    const unit = dictList['quantityUnit'].find(item => ('' + item.code) === value)
    if (unit && unit.name) {
      return unit.name
    }
  }
  return value
}

// 集货交货前的提示
export async function getDeliveryNoteTips (self, data) {
  var res;
  try {
    res = await deliveryNoteTips(data);
  } catch (err) {
    self.$message.error(err)
    return Promise.reject(err)
  }
    if (res?.code === 200 && res?.data?.msg) {
        try {
          await self.$confirm(res?.data?.msg, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          return Promise.resolve(res?.data)
        } catch (err) {
          return Promise.reject(err)
        }
      }
}

// 合并交货的错误提示
export async function getGroupErrorTips(self, itemDetailData) {
  if (itemDetailData?.groupErrMsg) {
    try {
      await self.$confirm(itemDetailData?.groupErrMsg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      return Promise.resolve()
    } catch (err) {
      return Promise.reject(err)
    }
  }
}

export async function checkBTBOrder(self, list, hasEdit) {
  let data = list?.filter(item => item.soNo && item.soItemNo)?.map(item => {
    let rItem = {
      soNo: item.soNo,
      soItemNo: item.soItemNo
    }
    // 除编辑外只传单号
    if (hasEdit) {
      rItem.quantity = item.quantity
      rItem.factory = item.factory
      rItem.position = item.position
      rItem.directDeliverySupplier = item.directDeliverySupplier
    }
    return rItem
  })
  const loading = self.$loading({
    background: 'rgba(0, 0, 0, 0.5)',
    lock: true
  })
  const res = await getBackToBackRemindInfos(data)
  loading.close()
  if (res && res.code === 200 && res.data) {
    let unclearBTBInfo = ''
    let deliveryBTBInfo = ''
    res.data.forEach((item) => {
      if (item?.unclearBTBInfo) {
        unclearBTBInfo += `${item.unclearBTBInfo}</br>`
      }
      if (item?.deliveryBTBInfo) {
        deliveryBTBInfo += `${item.deliveryBTBInfo}</br>`
      }
    })
    if (unclearBTBInfo || deliveryBTBInfo) {
      try {
        await self.$confirm(`<div class="btb-wrapper">${unclearBTBInfo}${deliveryBTBInfo}</div>`, '订单背靠背信息', {
          confirmButtonText: '继续',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
        return Promise.resolve()
      } catch (err) {
        return Promise.reject(err)
      }
    } else {
      return Promise.resolve()
    }
  } else {
    self.$notify.error('获取背靠背订单信息失败！')
  }
}
