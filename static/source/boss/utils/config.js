// const env = (process.env.NODE_ENV || process.env.ENV || 'release').toLowerCase()
// console.log('process.env.NODE_ENV:' + process.env.NODE_ENV)

// console.log('process.env.ENV:' + process.env.ENV)

// const isPro = /^(release|production|pro)/i.test(env)
// const isFat = false
// const isUat = false
const env = window.CUR_DATA.env.toLowerCase() || 'pro' // isPro ? 'pro' : isFat ? 'fat' : 'uat'

let url = 'https://ticket.zkh360.com/?worklist_creator-add'
if (env !== 'pro') {
  url = 'https://ticket-uat.zkh360.com/?worklist_creator-add'
}
export const gongdanUrl = url

export const udeskUrl =
  'https://zkh.oneinto.udesk.cn/backend/authz/oauth/request/4/3'

export const updateAppName = env === 'pro' ? 'ecorp-pro' : 'ecorp-uat'

window.omsAppName = env === 'pro' ? 'ecorp-pro' : 'ecorp-uat'

let appList = {
  pro: '138',
  uat: '277', // 主uat '277', 备份 '264'
  fat: '333'
}
appList.local = appList.uat

export const appId = appList[env]

// 自定义 icon

export const favIconMap = {
  boss: 'https://files.zkh360.com/assets/favicon/boss.png', // BOSS
  ep: 'https://files.zkh360.com/assets/favicon/ep.png', // 外部账号管理
  idx: 'https://files.zkh360.com/assets/favicon/idx.png', // 指标平台
  pm: 'https://files.zkh360.com/assets/favicon/pm.png', // 价格管控
  cm: 'https://files.zkh360.com/assets/favicon/cm.png', // 客户中心
  pc: 'https://files.zkh360.com/assets/favicon/pc.png', // 新商品中心/标品库管理
  workflow: 'https://files.zkh360.com/assets/favicon/workflow.png', // 工单管理
  pr: 'https://files.zkh360.com/assets/favicon/pr.png', // 价格中心（商品定价）
  finance: 'https://files.zkh360.com/assets/favicon/finance.png' // 对账中心
}
