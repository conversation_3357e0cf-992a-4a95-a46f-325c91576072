import axios from 'axios'
import { safeRun } from './index'
let env = ''
safeRun(() => {
  env = window.CUR_DATA.env !== 'pro' ? '.uat' : ''
})
const dashPrefix = `/api-acm-config?methodType=getConfig&id=boss-dashboard${env}.config`
export async function getDashboardRoutes () {
  let routes = []
  try {
    // boss-admin提供配置中心接口
    let response = await axios(dashPrefix)
    if (response.status === 200 && Array.isArray(response.data)) {
      routes = response.data
    } else {
      console.log(response)
    }
  } catch (err) {
    console.log(err)
  }
  const dRoutes = buildRoutes(routes)
  return dRoutes
}
export async function saveLocalRoutes (type, data) {
  try {
    if (typeof data === 'object') { data = JSON.stringify(data) }
    localStorage.setItem(type, data)
  } catch (err) { console.log(err) }
}
export async function getLocalRoutes (type) {
  let ret = localStorage.getItem(type)
  try { ret = JSON.parse(ret) } catch (err) { console.log(err) }
  return ret
}
export async function addDynamicRoutes(type, router) {
  const dRoutes = await getDashboardRoutes()
  router.$addRoutes(dRoutes)
  window.dashboardRoutesLoaded = true
  window.vmRouter = router
  window.vmRoutes = dRoutes
  return dRoutes
}

function buildRoutes (list) {
  let root = {
    path: '/iframe-dashboard/',
    component: () =>
      import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
    meta: {
      breadcrumb: ['数据大盘'],
      keepAlive: true,
      tagName: '数据大盘'
    },
    children: []
  }
  if (list && Array.isArray(list) && list.length) {
    try {
      list.forEach((item, index) => {
        let path = item.path
        if (path && path[0] === '/') {
          path = path.slice(1)
        }
        root.children.push({
          path,
          component: () => import ('@/pages/iframeDashboard/dynamicDashboard.vue'),
          name: item.name,
          meta: {
            breadcrumb: ['数据大盘', item.name],
            keepAlive: false,
            tagName: item.name,
            url: item.url
          }
        })
      })
    } catch (err) {
      console.log(err)
    }
  } else {
  }
  const routeTemp = [root, {
    path: '*',
    component: () =>
      import(/* webpackChunkName: "not-found" */ '@/components/NotFound.vue')
  }]
  return routeTemp
}
