import storage from 'good-storage'

const Cache = {
  TokenKey: 'auth_token',
  soVoucherId: 'soVoucherId',

  setToken: function (token) {
    storage.set(this.Token<PERSON>ey, token)
    return token
  },
  getToken: function () {
    return storage.get(this.TokenKey, '')
  },
  removeToken: function () {
    storage.remove(this.TokenKey)
    return ''
  },
  setSoVoucherId: function (button) {
    storage.set(this.soVoucherId, button)
    return button
  },
  getSoVoucherId: function () {
    return storage.get(this.soVoucherId, '')
  },
  removeSoVoucherId: function () {
    storage.remove(this.soVoucherId)
    return ''
  }
}

export default Cache
