import { getCookie } from '@/utils/index'
let lockReconnect = false
let tt
export const heartCheck = {
  timeout: 2000,
  timeoutObj: null,
  serverTimeoutObj: null,
  reset: function () {
    this.timeoutObj && clearInterval(this.timeoutObj)
    this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj)
    return this
  },
  start: function (config) {
    const { vue } = config
    const self = this
    this.timeoutObj = setInterval(() => {
      if (vue.ws.readyState === vue.ws.OPEN) {
        vue.ws.send(new Date().getTime())
      } else if (vue.ws.readyState === vue.ws.CONNECTING) {
        // 若是 正在开启状态，则等待300毫秒
        setTimeout(() => {
          vue.ws.send(new Date().getTime())
        }, 300);
      }
      self.serverTimeoutObj = setTimeout(() => {
        try {
          if (vue.ws.readyState !== vue.ws.OPEN) {
            lockReconnect = false
            vue.ws.close()
          } else {
            self.timeoutObj && clearInterval(self.timeoutObj)
            self.serverTimeoutObj && clearTimeout(self.serverTimeoutObj)
          }
        } catch (err) {
          createWebsocket(config)
        }
      }, self.timeout * 3)
    }, this.timeout)
  }
}
export async function reconnect (config) {
  if (lockReconnect) return
  lockReconnect = true
  tt && clearTimeout(tt);
  tt = setTimeout(() => {
    console.log('reconnect', new Date())
    createWebsocket(config)
    lockReconnect = false
  }, 2000)
}
export function createWebsocket (config) {
  const { vue } = config
  const { prefix, url } = getAccessToken()
  const token = getCookie(prefix)
  console.log('prefix--', prefix)
  if ((/fetest|local/.test(location.href)) || token === '') {
    return
  }
  vue.ws = new WebSocket(url, token === '' ? null : token)
  lockReconnect = false
  vue.global.setWs(vue.ws)
  vue.ws.onmessage = e => {
    if (e.data !== 'success') {
      const data = JSON.parse(e.data)
      if (data.noticeType === 1 || data.noticeType === 0) {
        const index = vue.msgTipsList.findIndex((item) => {
          return item.msgRuleNo === data.data.msgRuleNo
        })
        if (data.data.msgRead === 0) {
          index > -1 ? (vue.msgTipsList[index].count = vue.msgTipsList[index].count + 1) : vue.msgTipsList.push({ msgRuleNo: data.data.msgRuleNo, count: 1, msgRuleTypeName: data.data.msgRuleName })
        } else if (data.data.msgRead === 1 && index > -1) {
          vue.msgTipsList[index].count < 2 ? vue.msgTipsList.splice(index, 1) : vue.msgTipsList[index].count = vue.msgTipsList[index].count - 1
        }
      } else if (data.noticeType === 2 && data.data === 'refresh') {
        vue.getMsgTipsList()
      }
    }
    heartCheck.reset().start(config)
  }
  vue.ws.onopen = e => {
    console.log('open websocket', new Date())
    vue.getMsgTipsList()
    lockReconnect = true
    heartCheck.reset().start(config)
  }
  vue.ws.onclose = e => {
    // console.log('close', new Date())
    reconnect(config)
  }
  vue.ws.onerror = e => {
    // console.log('error', new Date())
    reconnect(config)
  }
}
export function getAccessToken () {
  let proToken = 'zkh_access_token'
  let prefix = proToken
  const url = (/fetest|local|uat/.test(location.href)) ? 'wss://api-uat-boss-msg-hub.zkh360.com/websocket' : 'wss://api-boss-msg-hub.zkh360.com/websocket'
  try {
    prefix = document.cookie.match(/([a-z_]*?zkh_access_token)/g)
    if (prefix && /fetest|local|uat/.test(location.href)) {
      const uatToken = prefix.filter(r => ~r.indexOf('uat'))[0]
      const localToken = prefix.filter(r => ~r.indexOf('local'))[0]
      prefix = uatToken || localToken
    } else {
      prefix = proToken
    }
  } catch (err) { console.log(err) }
  return { prefix, url }
}
