/**
 *
 * @borrows https://github.com/ElemeFE/element/blob/dev/packages/backtop/src/main.vue
 */

const cubic = value => Math.pow(value, 3);
const easeInOutCubic = value => value < 0.5
  ? cubic(value * 2) / 2
  : 1 - cubic((1 - value) * 2) / 2;

/**
 *
 * @param { el: Element, to: number, duration: number }
 */
export function scrollTo({ el, to, duration }) {
  const beginTime = Date.now();
  const beginValue = el.scrollTop;
  const rAF = window.requestAnimationFrame || (func => setTimeout(func, 16));
  const frameFunc = () => {
    const progress = (Date.now() - beginTime) / duration;
    if (progress < 1) {
      const scrollTop = beginValue * (1 - easeInOutCubic(progress)) + to;
      el.scrollTop = scrollTop;
      rAF(frameFunc);
    } else {
      el.scrollTop = to;
    }
  };
  rAF(frameFunc);
}
