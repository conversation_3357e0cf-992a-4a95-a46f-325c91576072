export const setSaleOrderFilter = Vue => {
  Vue.filter('dict', function (code, data, category) {
    if (!code || !category || !data || !data[category]) {
      return code
    }
    if (data[category] && data[category].length > 0) {
      return (data[category].find(f => f.code === code) || { name: code }).name
    }
    return code
  })

  Vue.filter('dictParent', function (code, data, category) {
    if (!code || !category || !data || !data[category]) {
      return code
    }
    if (data[category] && data[category].length > 0) {
      return (data[category].find(f => f.parentCode === code) || { name: code }).name
    }
    return code
  })

  Vue.filter('orderStatus', function (code) {
    const _status = [{ code: 'success', text: '已创建' }, { code: 'sketch', text: '草稿' }, { code: 'new', text: '新建' }, { code: 'confirm', text: '已确认' }, { code: 'cancel', text: '已取消' }]
    return (_status.find(s => s.code === code) || { text: code }).text
  })

  Vue.filter('productStatus', function (code) {
    const _status = [{ code: 'sketch', text: '草稿' }, { code: 'new', text: '新建' }, { code: 'clear', text: '已清' }, { code: 'cancel', text: '已取消' }]
    return (_status.find(s => s.code === code) || { text: code }).text
  })
}
