// import parseTime, formatTime and set to filter
import { buildSoDetailLink, isNumber } from '@/utils/index.js'
export { parseTime, formatTime } from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize (time, label) {
  if (time === 1) {
    return time + label
  }
  return time + label + 's'
}

/**
 * @param {number} time
 */
export function timeAgo (time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter (num, digits) {
  const si = [
    {
      value: 1e18,
      symbol: 'E'
    },
    {
      value: 1e15,
      symbol: 'P'
    },
    {
      value: 1e12,
      symbol: 'T'
    },
    {
      value: 1e9,
      symbol: 'G'
    },
    {
      value: 1e6,
      symbol: 'M'
    },
    {
      value: 1e3,
      symbol: 'k'
    }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (
        (num / si[i].value + 0.1)
          .toFixed(digits)
          .replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
      )
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter (num) {
  return (+num || 0)
    .toString()
    .replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * 10000 => "10000.00"
 * 2.3456 => "2.35"
 * abc => "abc"
 * 用作filter时先用bind绑定小数位参数
 * @param { positive integer } decimalCount
 * @param {number, string} num
 */
export function fixedDecimalPlace (decimalCount, num) {
  // toFixed 四舍六入五取偶数
  // Math.round 最接近整数
  // 因此先用Math.round，乘除10的n次方做四舍五入，然后用toFixed固定小数位
  // 非数值类型返回原值
  if (!/^[1-9]\d*$/.test(decimalCount)) {
    return num
  }
  if (isNumber(num)) {
    const rate = Math.pow(10, decimalCount)
    let newData = Math.round(num * rate) / rate
    newData = newData.toFixed(decimalCount)
    return newData
  } else {
    return num
  }
  // return (+num || 0)
  //   .toString()
  //   .replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst (string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

export function getSupplyStatusClass (supplyStatusText) {
  const text = supplyStatusText + ''
  if (text === '缺货未采购') {
    return 'danger-color'
  } else {
    return ''
  }
}

export function getStatusNameClass (statusText) {
  const text = statusText + ''
  if (text.indexOf('已逾期') >= 0 || text.indexOf('逾期') === 0) {
    // 逾期x天
    return 'danger-color'
  } else if (text.indexOf('即将') === 0) {
    // 即将逾期
    return 'warning-color'
  } else if (text.indexOf('正常') >= 0) {
    // 正常交期
    return 'success-color'
  } else {
    return ''
  }
}

export function listFilter (list) {
  let str = ''
  if (list && list.length > 0) {
    if (list.length > 2) {
      list = list.slice(0, 2)
    }
    str = list.join(',')
  }

  return str
}

export function dateFormat (fmt, date) {
  var o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return fmt
}

// 采购异常未下单原因文本处理
export function getReasonTypeText (reasonOptions, reasonOne, reasonTwo) {
  if (reasonOptions && reasonOptions.length > 0) {
    const reasonOneType = reasonOptions.find(item => {
      return item.value === reasonOne
    })
    if (
      reasonOneType &&
      reasonOneType.children &&
      reasonOneType.children.length > 0
    ) {
      const reasonTwoType = reasonOneType.children.find(item => {
        return item.value === reasonTwo
      })
      return reasonOneType.label + ' / ' + (reasonTwoType.label || '')
    } else {
      return ''
    }
  } else {
    return ''
  }
}

export function getReasonTypeOneList (reasonOptions) {
  const result = reasonOptions.map(item => {
    return {
      value: item.value,
      label: item.label
    }
  })
  return result || []
}

export function getReasonTypeOneText (reasonTypeOneList, reasonOne) {
  if (reasonTypeOneList) {
    const reasonTypeOne = reasonTypeOneList.find(item => {
      return Number(item.value) === Number(reasonOne)
    })
    return reasonTypeOne ? reasonTypeOne.label : ''
  } else {
    return ''
  }
}
export function getReasonTypeTwoText (getReasonTypeTwoList, reasonTwo) {
  if (getReasonTypeTwoList) {
    const reasonTypeOne = getReasonTypeTwoList.find(item => {
      return Number(item.value) === Number(reasonTwo)
    })
    return reasonTypeOne ? reasonTypeOne.label : ''
  } else {
    return ''
  }
}

export function getReasonTypeTwoList (reasonOptions, reasonOne) {
  if (reasonOptions && reasonOptions.length > 0) {
    const reasonOneType = reasonOptions.find(item => {
      return Number(item.value) === Number(reasonOne)
    })
    if (
      reasonOneType &&
      reasonOneType.children &&
      reasonOneType.children.length > 0
    ) {
      const reasonTwoTypeList = reasonOneType.children.map(item => {
        return {
          value: item.value,
          label: item.label
        }
      })
      return reasonTwoTypeList
    }
  } else {
    return []
  }
}

// 相同host返回相对路径，不同host返回全路径
// 如果入参就是相对路径，不处理
export function urlTransitionByHost (target) {
  target = target || ''
  const index = target.indexOf(location.host)
  if (index >= 0 && !/\?open=true/.test(target)) {
    return target.substring(index + location.host.length) || '/'
  } else {
    return target
  }
}

// 整单到货状态 0:未到齐 1:已到齐
export function orderArriStatusFilter (val) {
  const v = val - 0
  let result = ''
  if (v === -1) {
    result = '————'
  } else if (v === 0) {
    result = '未到齐'
  } else if (v === 1) {
    result = '已到齐'
  } else {
    result = val
  }
  return result
}
// 逾期状态 0:正常交期 1:未逾期 2:计划逾期 3:已逾期 ,
// 逾期状态 0:正常交期  1:可能逾期 2:已逾期
export function pastStatusFilter (val) {
  const v = val - 0
  let result = ''
  if (v === -1) {
    result = '————'
  } else if (v === 0) {
    result = '正常交期'
  } else if (v === 1) {
    result = '可能逾期'
  } else if (v === 2) {
    result = '已逾期'
  } else {
    result = val
  }
  return result
}

// 供货状态 -1:- 0:采购异常 1:未采购 2:未到齐 3:已到齐 4:长交期
export function supplyStatusFilter (val) {
  const v = val - 0
  let result = ''
  if (v === -1) {
    result = '————'
  } else if (v === 0) {
    result = '采购异常'
  } else if (v === 1) {
    result = '未采购'
  } else if (v === 2) {
    result = '未到齐'
  } else if (v === 3) {
    result = '已到齐'
  } else if (v === 4) {
    result = '长交期'
  } else {
    result = val
  }
  return result
}

// // 采购订单单据类型 0:正常采购订单 1:转储采购订单 ,
// export function purchaseOrderTypeFilter (val) {
//   const v = val - 0
//   let result = ''
//   if (v === -1) {
//     result = '————'
//   } else if (v === 0) {
//     result = '正常采购订单'
//   } else if (v === 1) {
//     result = '转储采购订单'
//   } else {
//     result = ''
//   }
//   return result
// }

// 采购订单单据类型 0:正常采购订单 1:转储采购订单 ,
export function SOlinkFilter (val, row) {
  let result = buildSoDetailLink({
    query: {
      sapOrderNo: val,
      soNo: row.soNo || ''
    }
  })
  return result
}

export function YNFilter (val) {
  if (val === null || val === undefined) {
    return ''
  } else if (val.toUpperCase() === 'Y') {
    return '是'
  } else if (val.toUpperCase() === 'N') {
    return '否'
  } else {
    return ''
  }
}

export function creditFreezeFilter (val) {
  if (val === null || val === undefined) {
    return ''
  } else if (val === 1) {
    return '是'
  } else if (val === 0) {
    return '否'
  } else {
    return ''
  }
}

// null，undefined， 0 ，0.0 等输出为空字符
export function hideZero (val) {
  if (val === null || val === undefined) {
    return ''
  } else if (parseFloat(val) === 0) {
    return ''
  } else {
    return val
  }
}

export function filterNone (val) {
  if (val === '' || val === undefined || val === null) {
    return ''
  }
  return val
}

export function orderLabelFilter (val) {
  let ret = ''
  if (val === '001') ret = '自主下单'
  if (val === '002') ret = '代理下单'
  return ret || val
}

export function deliveryStatusFilter (val) {
  let ret = ''
  if (val === '1') ret = '已清'
  if (val === '0') ret = '未清'
  return ret
}
export function conditionTypeFilter (val) {
  let ret = ''
  if (val === 'Z010') ret = '未税'
  if (val === 'Z005') ret = '含税'
  return ret
}
export function ifNeedDeliveryFilter (val) {
  let ret = ''
  if (val === 'Y') ret = '是'
  if (val === 'N') ret = '否'
  return ret
}
