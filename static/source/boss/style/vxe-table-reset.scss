.vxe-header--column {
  background: #f4f4f4 !important;
  color: #666;
  font-weight: bold;
  border-right: 0 none;
  font-size: 12px;
}
.el-container {
  .vxe-table--render-default {
    font-size: 12px;
    color: #333;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  }
    .vxe-table--render-default.size--small {
        font-size:12px;
        color: #333;
    }
    .vxe-table--render-default.border--full .vxe-body--column, .vxe-table--render-default.border--full .vxe-footer--column, .vxe-table--render-default.border--full .vxe-header--column {
      background-image: -webkit-gradient(linear,left top, left bottom,from(#EBEEF5),to(#EBEEF5)),-webkit-gradient(linear,left top, left bottom,from(#EBEEF5),to(#EBEEF5));
      background-image: linear-gradient(#EBEEF5,#EBEEF5),linear-gradient(#EBEEF5,#EBEEF5);
      background-repeat: no-repeat;
      background-size: 1px 100%,100% 1px;
      background-position: 100% 0,100% 100%
    }
    .vxe-table--render-default .vxe-body--column:not(.col--ellipsis), .vxe-table--render-default .vxe-footer--column:not(.col--ellipsis), .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
      padding: 8px 0
    }
    .vxe-table--render-default .vxe-body--expanded-cell {
      padding: 0;
    }
    .vxe-body--expanded-row {
      th,td{
        padding-top: 0;
      }
    }

}
