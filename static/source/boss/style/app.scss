@import "./colors.scss";
@import "./vxe-table-reset.scss";

html {
  font-size: 62.5%;
  -webkit-text-size-adjust: none;
  -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
}
body {
  // font-family: 'Helvetica Neue', Helvetica, STHeiTi, 'Microsoft Yahei', sans-serif;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  font-size: 1.4rem;
  overflow-x: hidden;
}
// 框架样式覆盖
.el-main {
  padding: 10px;
  .el-scrollbar__wrap {
    overflow: auto;
  }
}
.header-tags {
  margin-bottom: 0 !important;
  background-color: $color-grey-100 !important;
  .el-tabs--card > .el-tabs__header .el-tabs__item {
    height: 40px !important;
    line-height: 40px !important;
    color: $color-dark-400 !important;
    &.is-active {
      color: $color-dark-400 !important;
      border-bottom: 3px solid $color-blue-200 !important;
    }
    .el-icon-close {
      font-size: 14px !important;
    }
  }
  .el-tabs__nav-prev, .el-tabs__nav-next {
    z-index: 10;
    line-height: 40px;
  }
  .header-tags--menu {
    .el-button {
      color: $color-dark-200;
      font-size: 14px;
    }
  }
}

//main-container全局样式
.app-container {
  padding: 10px 20px 0 5px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 0px;
}

.mgb20{
  margin-bottom: 20px;
}

.text-center {
  text-align: center;
}

.el-loading-spinner {
  font-size: 30px;
  .el-loading-text {
    font-size: 22px;
  }
}
.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
// 为空时内容会叠到上面5px
.el-step__title {
  min-height: 5px;
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

/* 自定义共通样式 */
.el-table table thead th {
  background: #f4f4f4 !important;
  color: #666;
  font-weight: bold;
  border-right: 0 none;
}
.el-table {
  font-size: 12px;
  font-weight: normal;
  color: #333333;
  width:99.9%!important;
}
ul,
li {
  list-style: none;
}

// 自定义字体颜色
.success-color {
  color: #67c23a;
}
.warning-color {
  color: #e6a23c;
}
.danger-color {
  color: #f56c6c;
}
.bold-font {
  font-weight: bold;
}
.title-highlight {
  color: #409eff;
  font-size: 16px;
}
// 指定最大行数
.multiText {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

.is-align-center {
  align-items: center;
}

.btb-wrapper {
  overflow-y: auto;
  max-height: 400px;
}