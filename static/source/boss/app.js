import Boss from '@client/boss';
import '@client/boss/dist/boss.css';
import '@/style/app.scss';
import '@/style/scrollbar.css';
import '@/style/colors.scss';
import store from '@/store';
import router from '@/router';
import App from '@/pages/App.vue';
import ZkhLink from '@/components/ZkhLink.vue';
import ZkhTable from '@/components/ZkhTable/ZkhTable.vue';
import { urlTransitionByHost } from '@/filters/index.js';
import { setSaleOrderFilter } from '@/filters/filterOrderSale';
// import { isInWhiteList } from '@/utils/index.js'
import '@/utils/polyfill.js';
import initLogger from '@boss/logger';
import { validateFileType } from '@/utils/upload'

import global from './utils/global.js';

window._console = initLogger({
  silent: true, // open debug mode
  timestamp: true, // log with timestamp
  prefix: true, // log with boss-logger prefix
  muteGlobalConsole: false // when silent is true and not debug status, mute global console.log
});

// Do not delete: dynamic publicPath for lazy import modules
__webpack_public_path__ = window.RES_PUBLIC_PATH; // eslint-disable-line

const CUR_DATA = window.CUR_DATA;
const { Vue } = Boss;
Vue.prototype.global = global;
Vue.prototype.$validateFileType = validateFileType;

let tag = {
  // router tag信息
  maxPage: 10,
  tagWel: {
    // 默认首页配置
    label: '首页',
    value: '/',
    params: {},
    query: {},
    close: false
  }
};
// setTimeout(() => {
//   if (localStorage.getItem('open-in-new-tab') !== 'boss' && isInWhiteList(location)) {
//     Vue.prototype.$closeTag = () => {}
//   }
// }, 1000)
// 系统一站式中会有多个系统聚合在一起，根据目标链接是否是当前系统来处理链接
Vue.filter('urlTransitionByHost', function (target) {
  return urlTransitionByHost(target);
});
setSaleOrderFilter(Vue);
Vue.component(ZkhLink.name, ZkhLink);
Vue.component(ZkhTable.name, ZkhTable);
Vue.config.ignoredElements = ['safe-phone-num', 'dropdown-task'];
console.log(window.VXETable, '======');
window.VXETable.setup({
  zIndex: 9999
})
Vue.use(window.VXETable);

Boss.init({
  debug: true,
  info: {
    title: '震坤行BOSS平台',
    el: '#app',
    showBread: false
  },
  app: App,
  user: {
    name: CUR_DATA.user && CUR_DATA.user.name,
    menu: [],
    access: {},
    custom: false
  },
  element: {
    size: 'small'
  },
  tag,
  router,
  vue: {
    store
  }
});
