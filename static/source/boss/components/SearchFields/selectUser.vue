<template>
  <el-select
    :style="{width:width}"
    :value="value"
    ref="selectUser"
    filterable
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :loading="loading"
  >
    <el-option
      v-for="item in userOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value"></el-option>
  </el-select>
</template>
<script>
import {
  getUser
} from './api.js'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'SelectUser',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    value: [ String, Number ],
    pageNo: {
      type: Number,
      default: 1
    },
    rowCount: {
      type: Number,
      default: 10
    },
    getLabel: {
      type: Boolean,
      default: false
    },
    username: {
      type: String,
      default: ''
    }
  },
  created () {
    this.remoteMethod = spDebounce(this.remoteMethod)
    // if (this.props.username) {
    //   this.remoteMethod(this.props.userName)
    // }
  },
  mounted() {

  },
  watch: {
    username(val) {
      if (this.value && val) { // 初始有值时无法回显，手动push初始选项到options里
        this.userOptions.push({
          value: this.value,
          label: val
        })
      }
    }
  },
  data () {
    return {
      userOptions: [],
      loading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      let value
      try {
        value = this.userOptions.filter(item => item.value === val)[0]
      } catch (err) { console.log(err) }
      console.log(val)
      this.$emit('change', val, value)
    },
    // 远程查找用户
    remoteMethod (query) {
      console.log(query)
      const key = query.trim()
      const params = {
        pageNo: this.pageNo,
        rowCount: this.rowCount,
        userName: key
      }
      if (key !== '') {
        this.loading = true
        getUser(params).then(res => {
          if (res.code === '0000' && res.result && res.result.rows && res.result.rows.length > 0) {
            this.userOptions = res.result.rows.map(item => ({
              ...item,
              value: item.id,
              label: item.userName
            }))
          } else {
            this.userOptions = []
          }
        }).finally(() => {
          this.loading = false
        })
      } else {
        this.userOptions = []
      }
    }
  }
}
</script>
