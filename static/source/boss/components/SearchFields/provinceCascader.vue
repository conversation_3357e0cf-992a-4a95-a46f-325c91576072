<template>
<el-cascader
  :value="value"
  :options="options"
  :disabled="disabled"
  clearable
  @change="handleChange">
</el-cascader>
</template>
<script>
import { getProvinceCascader } from './api'
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Array,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      options: [],
      deepthMap: {
        1: {
          children: 'serviceAreaCityResults',
          label: 'province'
        },
        2: {
          children: 'serviceAreaRegionResultList',
          label: 'city'
        },
        3: {
          label: 'region'
        }
      }
    }
  },
  methods: {
    handleChange (value) {
      console.log(value)
      this.$emit('change', value)
      this.$emit('provinceChange', this.mapName(value))
    },
    mapName (array) {
      let tmp = []
      try {
        let level1 = this.options.filter(option => option.value === array[0])[0]
        tmp.push(level1)
        let level2 = level1.children.filter(option => option.value === array[1])[0]
        tmp.push(level2)
        let level3 = level2.children.filter(option => option.value === array[2])[0]
        tmp.push(level3)
      } catch (err) {}
      return tmp
    },
    mapData (array, deepth = 1) {
      let tmp = array.filter(e => e).map(item => {
        let d = {
          value: item.id,
          label: item[this.deepthMap[deepth].label]
        }
        if (this.deepthMap[deepth].children) {
          d.children = this.mapData(item[this.deepthMap[deepth].children], deepth + 1)
        }
        return d
      })
      return tmp
    },
    formatData (data) {
      this.options = this.mapData(data, 1)
    }
  },
  created () {
    getProvinceCascader()
      .then(data => {
        data && this.formatData(data)
      })
      .catch(err => {
        this.$message.error(err.msg || err.message || '获取省市区信息失败！')
      })
  }
}
</script>
