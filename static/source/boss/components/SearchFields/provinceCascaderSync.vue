<template>
  <el-cascader
    ref="cascader"
    :value="value"
    :props="props"
    :options="options"
    clearable
    @change="handleChange">
  </el-cascader>
  </template>
  <script>
  import { getAreaAsync } from './api'
  export default {
    model: {
      prop: 'value',
      event: 'change'
    },
    props: {
      value: Array,
      checkStrictly: <PERSON><PERSON><PERSON>,
      multiple: <PERSON><PERSON><PERSON>,
      maxDepth: {
        type: Number,
        default: 3
      }
    },
    data () {
      return {
        options: [],
        props: {
          checkStrictly: this.checkStrictly,
          multiple: this.multiple,
          maxDepth: this.maxDepth
        }
      }
    },
    methods: {
      handleChange (value) {
        let nodes = []
        try {
          nodes = this.$refs.cascader.getCheckedNodes()[0].pathNodes
        } catch (err) {}
        this.$emit('change', value)
        this.$emit('provinceChange', nodes)
      },
      async getLevelArea () {
        const options = localStorage.getItem('provinceArea')
        if (options) {
          this.options = JSON.parse(options)
          return
        }
        const { data: level1List } = await getAreaAsync({ level: 1 })
        if (level1List) {
          const { data: level2List } = await getAreaAsync({ level: 2 })
          const tmp = level1List.map(item => ({
            ...item,
            value: String(item.code),
            label: item.name,
            leaf: false,
            children: level2List.filter(item1 => item1.parentCode === item.code).map(item1 => ({
              ...item1,
              value: String(item1.code),
              label: item1.name,
              leaf: true
            }))
          }))
          localStorage.setItem('provinceArea', JSON.stringify(tmp))
          this.options = tmp
        }
      }
    },
    created () {
      this.getLevelArea()
    }
  }
  </script>
