<template>
  <el-cascader
    ref="cascader"
    :value="value"
    :props="props"
    :options="options"
    :collapse-tags="collapseTags"
    clearable
    filterable
    @change="handleChange"
    :disabled="disabled"
  >
  </el-cascader>
</template>
<script>
import { getCatalogueApi } from './api';
export default {
  name: 'AllDirectoryCascader',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Array,
    checkStrictly: Boolean,
    multiple: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: [],
      props: {
        dirChanged: false,
        multiple: this.multiple,
        value: 'id',
        label: 'name',
        lazy: true,
        lazyLoad: this.lazyLoad,
        checkStrictly: this.checkStrictly
      }
    };
  },
  watch: {
    value: {
      handler: function (newVal) {
        if (newVal && !this.dirChanged) {
          // 回写状态
          const firstDirIds = new Set();
          const secondDirIds = new Set();
          const thirdDirIds = new Set();
          for (let i = 0; i < newVal.length; i++) {
            firstDirIds.add(newVal[i][0]);
            secondDirIds.add(newVal[i][1]);
            thirdDirIds.add(newVal[i][2]);
          }
          Promise.all([
            getCatalogueApi({
              rowCount: 50,
              level: 1,
              parentId: ''
            }),
            ...[...firstDirIds].map((id) =>
              getCatalogueApi({
                rowCount: 50,
                level: 2,
                parentId: id
              })
            ),
            ...[...secondDirIds].map((id) =>
              getCatalogueApi({
                rowCount: 50,
                level: 3,
                parentId: id
              })
            ),
            ...[...thirdDirIds].map((id) =>
              getCatalogueApi({
                rowCount: 50,
                level: 4,
                parentId: id
              })
            )
          ]).then((datas) => {
            if (datas) {
              const remoteIds = [
                -1,
                ...firstDirIds,
                ...secondDirIds,
                ...thirdDirIds
              ];
              newVal.forEach((linked) => {
                for (let i = linked.length - 2; i >= 0; i--) {
                  const remoteIndex = remoteIds.indexOf(linked[i]);
                  const data =
                    datas[remoteIndex] &&
                    (datas[remoteIndex].data || []).map((item) => ({
                      ...item,
                      leaf: i === 2
                    }));
                  const remoteParentIndex = remoteIds.indexOf(
                    i > 0 ? linked[i - 1] : -1
                  );
                  if (datas[remoteParentIndex]) {
                    const parentData = (
                      datas[remoteParentIndex].data || []
                    ).find((row) => row.id === linked[i]);
                    parentData && (parentData.children = data);
                  }
                }
              });

              this.options = datas[0].data;
            }
          });
        }
      },
      immediate: true
    },
    checkStrictly(newVal, oldVal) {
      if (newVal === oldVal) {
        this.props.checkStrictly = newVal
      }
    }
  },
  methods: {
    handleChange(value) {
      this.dirChanged = true;
      let nodes = [];
      let labels = [];
      try {
        nodes = this.$refs.cascader.getCheckedNodes()[0].pathNodes;
      } catch (err) {}
      labels = value.map(item => {
        for (var i of this.$refs.cascader.getCheckedNodes()) {
          if (item[item.length - 1] === i.value) {
            return i.pathLabels
          }
        }
        return null
      })
      this.$emit('change', value, labels);
      this.$emit('directoryChange', nodes);
    },
    lazyLoad(node, resolve) {
      let { level, data } = node;
      if (level > 3) {
        resolve([]);
        return;
      }
      level++;
      const params = { rowCount: 50, level: level };
      if (data && data.id) {
        params.parentId = data.id;
      } else {
        params.parentId = ''
      }
      getCatalogueApi(params)
        .then((res) => {
          if (res.data) {
            const tmp = res.data.map((item) => ({
              ...item,
              leaf: level > 3
            }));
            resolve(tmp);
          } else {
            this.$message.error(res.msg || res.message || '查询失败！');
            resolve([]);
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || err.message || '查询失败！');
          resolve([]);
        });
    }
  },
  created() {}
};
</script>
