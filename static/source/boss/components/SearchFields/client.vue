<template>
  <el-select
    :disabled="disabled"
    :multiple="multiple"
    :multiple-limit="multipleLimit"
    :collapse-tags="collapseTags"
    :style="{width:width}" :value="value" :loading="customerNoLoading"
    filterable remote clearable @change="handleSelectChange"
    :placeholder="placeholder" :remote-method="remoteCustomerMethod"
  >
    <el-option
      v-for="(item,index) in customerNoOptions"
      :key="item.customerNumber"
      :label="getValue ? item.customerNumber : item.customerName"
      :value="getLabel? item.customerName: item.customerNumber"
      :disabled="index===0"
      >
      <div
        class="ba-row-start select-client-item"
        :style="{fontWeight:index===0?'bold':'normal'}">
        <div>{{ item.customerNumber }}</div>
        <div>{{ item.cityName }}</div>
        <div>{{ item.customerName }}</div>
      </div>
    </el-option>
  </el-select>
</template>
<script>
import {
  getClientList
} from './api.js'
export default {
  name: 'remoteMaterialGroupComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入客户编号/名称'
    },
    width: {
      type: String,
      default: 'auto'
    },
    value: [ String, Number ],
    getLabel: Boolean,
    getValue: {
      type: Boolean,
      default: false
    },
    disabled: Boolean,
    multiple: Boolean,
    multipleLimit: {
      type: Number,
      default: 0
    },
    collapseTags: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      customerNoOptions: [],
      customerNoLoading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      const customer = this.customerNoOptions.filter(customer => customer.customerNumber === val) || []
      this.$emit('change', val, customer[0])
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getClientList(key).then(res => {
          if (res.code === 200 && res.data && res.data.length > 0) {
            this.customerNoOptions = [
              {
                customerNumber: '客户编码',
                customerName: '客户名称',
                cityName: '城市'
              },
              ...res.data
            ]
          } else {
            this.customerNoOptions = []
          }
        }).finally(() => {
          this.customerNoLoading = false
        })
      } else {
        // this.customerNoOptions = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.select-client-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
  }
}

</style>
