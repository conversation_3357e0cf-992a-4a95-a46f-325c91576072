import request from '@/utility/request'
// const apiMdm = '/api-mdm'
const apiPlatform = '/api-platform'
const opcFrontGoods = '/oms-opc-front/goods'
const sellOrderHost = '/api-opc'
const afterSale = '/api-sales'
const apiKunheAssistant = '/api-kunhe-assistant'
const gateway = '/api-cc-gateway'
// const apiPms = '/api-pms'
const apiCustomerCenter = '/api-customerCenter'
const security = '/security-api'
// 品牌下拉列表
export function getBrandListApi (data) {
  return request({
    url: `${opcFrontGoods}/get/brand/list`,
    method: 'get',
    params: data
  })
}
// 四级目录下拉列表
// export function getCatalogueApi (params) {
//   return request({
//     url: `${apiMdm}/manageCatalogues`,
//     method: 'get',
//     params
//   })
// }
export function getCatalogueApi (data) {
  return request({
    url: `${gateway}/api/scc/v1/catalog/page`,
    method: 'post',
    data: {
      ...data,
      pageSize: 100,
      pageNum: 1
    }
  })
}
// 客户名称下拉列表
export function getCustomerList (data) {
  return request({
    url: `${opcFrontGoods}/get/customer/list`,
    method: 'get',
    params: data
  })
}
// 物料组下拉列表
export function getMaterialGroupList (data) {
  return request({
    url: `${opcFrontGoods}/get/group/list`,
    method: 'get',
    params: data
  })
}
// 客户名称下拉列表
export function getClientList (query) {
  return request({
    url: sellOrderHost + '/v1/so/template/customer/like',
    params: { value: query }
  })
}
// sku名称下拉列表
export function getSkuList (query, needMaterialGroup = false) {
  return request({
    url: sellOrderHost + '/v1/so/template/sku/like',
    params: { vague: query, needMaterialGroup }
  })
}
// 客户中心根据单个用户名模糊搜索
export function getUser (params) {
  return request({
    url: apiCustomerCenter + '/v1/users/basic',
    params
  })
}
// 安全中心根据单个用户名模糊搜索
export function getAccountByName(name) {
  return request({
    url: `${security}/account/name`,
    params: {
      name
    }
  })
}
// 售后名称下拉列表
export function getAfterSaleCustomerList (params) {
  return request({
    url: afterSale + '/ticket/user/detail',
    params
  })
}
// 省市区信息
let areaList = []
export function getProvinceCascader () {
  if (areaList && areaList.length) return new Promise((resolve, reject) => resolve(areaList))
  return request({
    url: apiKunheAssistant + '/area/init'
  }).then(res => {
    if (res.code === 200 && res.data) {
      areaList = res.data
      return areaList
    }
    return null
  })
}

// 按需查询省市区信息
// export function getAreaAsync (params) {
//   return request({
//     url: apiMdm + '/area',
//     params
//   })
// }
export function getAreaAsync (params) {
  return request({
    url: apiPlatform + '/v2/area',
    params
  })
}

// export function getSupplierList (name) {
//   return request({
//     url: apiMdm + '/supplier/list',
//     params: {
//       providerName: name
//     }
//   })
// }
// export function getSupplierList (name, pageNo = 1, pageSize = 100) {
//   return request({
//     url: apiPms + '/api/v1/supplier/page',
//     method: 'post',
//     data: {
//       supplierNameLike: name,
//       pageNo,
//       pageSize
//     }
//   })
// }

export function getSupplierList (val) {
  return request({
    url: '/api-mm/supplier/options',
    method: 'get',
    params: {
      supplierNoOrName: val
    }
  })
}
