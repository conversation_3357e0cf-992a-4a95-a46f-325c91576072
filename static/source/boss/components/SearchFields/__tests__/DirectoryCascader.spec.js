import { mount } from '@vue/test-utils'
import { Cascader } from 'element-ui';
import flushPromises from 'flush-promises'
import DirectoryCascader from '../directoryCascader.vue';
jest.mock('../api');

describe('DirectoryCascader', () => {
  test('is exist', () => {
    const wrapper = mount(DirectoryCascader, {
      stubs: {
        'el-cascader': Cascader
      }
    })

    wrapper.setProps({ primaryDirectoryNo: 259426 })
    const directoryCascader = wrapper.findComponent(DirectoryCascader)
    expect(directoryCascader.exists()).toBe(true)
  })

  test('expand and check', async () => {
    const wrapper = mount(DirectoryCascader, {
      stubs: {
        'el-cascader': Cascader
      }
    })

    wrapper.setProps({ primaryDirectoryNo: 259426 })
    wrapper.find('.el-cascader').trigger('click');
    await flushPromises();
    let menus = wrapper.findAll('.el-cascader-menu');
    expect(menus.at(0).vm.$el.childNodes.length).toBe(3);
    const secondOptions = menus.at(0).findAll('.el-cascader-node');
    expect(secondOptions.length).toBe(12);
    const secondLabel = secondOptions.at(0).find('.el-cascader-node__label');
    expect(secondLabel.text()).toBe('粘接密封胶');
    secondOptions.at(0).trigger('click');
    await flushPromises();
    menus = wrapper.findAll('.el-cascader-menu');
    const thirdOptions = menus.at(1).findAll('.el-cascader-node');
    expect(thirdOptions.length).toBe(5);
    const thirdLabel = thirdOptions.at(0).find('.el-cascader-node__label');
    expect(thirdLabel.text()).toBe('聚氨酯密封胶');
    thirdOptions.at(0).trigger('click');
    await flushPromises();
    menus = wrapper.findAll('.el-cascader-menu');
    const forthOptions = menus.at(2).findAll('.el-cascader-node');
    expect(forthOptions.length).toBe(10);
    const forthLabel = forthOptions.at(0).find('.el-cascader-node__label');
    expect(forthLabel.text()).toBe('聚氨酯密封胶-高强度型');
    const forthCheckbox = forthOptions.at(0).find('.el-checkbox');
    forthCheckbox.trigger('click');
    await flushPromises();
    expect(wrapper.find('.el-cascader__tags').text()).toBe('粘接密封胶 / 聚氨酯密封胶 / 聚氨酯密封胶-高强度型');
    expect(wrapper.emitted().change).toEqual([[[]], [[[259454, 260158, 261417]]]]);
  })

  test('load selected catalogs', async () => {
    const wrapper = mount(DirectoryCascader, {
      stubs: {
        'el-cascader': Cascader
      }
    })

    wrapper.setProps({ primaryDirectoryNo: 259426 })
    wrapper.setProps({
      value: [[259454, 260158, 261417], [259454, 260158, 261963], [259454, 260158, 262146]]
    })
    await flushPromises();
    expect(wrapper.find('.el-cascader__tags').text()).toBe('粘接密封胶 / 聚氨酯密封胶 / 聚氨酯密封胶-高强度型粘接密封胶 / 聚氨酯密封胶 / 聚氨酯密封胶粘接密封胶 / 聚氨酯密封胶 / 聚氨酯密封胶-主剂');
  })
})
