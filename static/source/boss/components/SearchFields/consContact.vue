<template>
  <el-select
    :style="{width:width}"
    :value="value"
    filterable
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :loading="searchLoading"
   :disabled="disabledU"
  >
    <el-option
      v-for="(item, index) in options"
      :key="item[props.value]"
      :label="item[props.label]"
      :disabled="index===0"
      :value="getLabel? item[props.label]: item[props.value]">
        <div
          class="ba-row-start selectClientItem"
          :style="{fontWeight:index===0?'bold':'normal'}"
        >
          <div>{{ item.contactName }}</div>
          <div>{{ item.contactId }}</div>
          <div>{{ item.contactPhone || '--' }}</div>
          <div>{{ item.address || '--' }}</div>
        </div>
    </el-option>
  </el-select>
</template>
<script>
import {
  searchContactListByGroup as searchApi
} from '@/api/orderSale'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'remoteMaterialGroupComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    disabledU: {
      type: Boolean,
      default: false

    },
    value: [ String, Number ],
    getLabel: Boolean,
    customerNumber: String,
    distributionChannel: String,
    productGroup: String,
    salesOrganization: String
  },
  created () {
    this.remoteMethod = spDebounce(this.remoteMethod)
  },
  data () {
    return {
      options: [],
      searchLoading: false,
      props: {
        label: 'contactName',
        value: 'contactId'
      }
    }
  },
  computed: {
    disabled () {
      console.log('this.customerNumber', this.customerNumber)
      return !this.customerNumber
    }
  },
  methods: {
    handleSelectChange (val) {
      if (!val) this.search('')
      let value
      try {
        value = this.options.filter(option => option[this.getLabel ? this.props.label : this.props.value] === val)[0]
      } catch (err) { console.log(err) }
      this.$emit('change', val, value)
    },
    search (contactName) {
      const params = {
        customerCode: this.customerNumber,
        contactName,
        distributionChannel: this.distributionChannel,
        productGroup: this.productGroup,
        salesOrganization: this.salesOrganization
      }
      return searchApi(params).then(res => {
        if (res.code === 200 && res.data && res.data.records && res.data.records.length > 0) {
          this.options = [
            {
              contactName: '联系人',
              contactId: '联系人编号',
              contactPhone: '联系人电话',
              address: '联系人地址'
            },
            ...res.data.records
          ]
        } else {
          this.options = []
        }
      })
    },
    remoteMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.searchLoading = true
        this.search(query)
          .finally(() => {
            this.searchLoading = false
          })
      } else {
        this.options = []
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
    overflow: auto;
  }
  div:nth-child(4) {
    width: 300px;
    overflow: auto;
  }
}
.ba-row-start{
  display: flex;
  color: #606266;
}
</style>
