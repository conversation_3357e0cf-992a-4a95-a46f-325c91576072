<template>
  <el-select
    :value="value"
    filterable
    default-first-option
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteprimaryLevelMethod"
    :loading="primaryLevelLoading"
  >
    <el-option
      v-for="item in primaryLevelOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel ? item.label : item.value"
    >
    </el-option>
  </el-select>
</template>
<script>
import { getCatalogueApi } from './api.js';
export default {
  name: 'primaryLevelDirectory',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: [String, Number],
    getLabel: Boolean
  },
  data() {
    return {
      primaryLevelOptions: [],
      primaryLevelLoading: false
    };
  },
  methods: {
    handleSelectChange(val) {
      this.$emit('change', val);
    },
    // 远程查找一级目录
    remoteprimaryLevelMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.primaryLevelLoading = true;
        getCatalogueApi({
          // rowCount: 50,
          level: 1,
          name: key
        })
          .then((res) => {
            this.primaryLevelLoading = false;
            if (res && res.data) {
              this.primaryLevelOptions = res.data.map((row) => ({
                value: row.id,
                label: row.name
              }));
            } else {
              this.primaryLevelOptions = [];
            }
          })
          .catch((err) => {
            this.$message.error(err.msg || err.message || '获取一级目录失败！');
            this.primaryLevelOptions = [];
          });
      } else {
        this.primaryLevelOptions = [];
      }
    }
  }
};
</script>
