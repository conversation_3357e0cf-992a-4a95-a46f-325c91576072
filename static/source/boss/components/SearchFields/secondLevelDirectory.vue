<template>
  <el-select
    :value="value"
    filterable
    default-first-option
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remotesecondLevelMethod"
    :loading="secondLevelLoading">
    <el-option
      v-for="item in secondLevelOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value">
    </el-option>
  </el-select>
</template>
<script>
import {
  getCatalogueApi
} from './api.js'
export default {
  name: 'secondLevelDirectory',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: [ String, Number ],
    getLabel: Boolean,
    defaultLabel: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      secondLevelOptions: [],
      secondLevelLoading: false
    }
  },
  created () {
    if (this.defaultLabel) {
      this.remotesecondLevelMethod(this.defaultLabel)
    }
  },
  methods: {
    handleSelectChange (val) {
      this.$emit('change', val)
      const curName = this.secondLevelOptions.find(it => it.value === val)?.label || ''
      this.$emit('getlabel', curName)
    },
    // 远程查找二级目录
    remotesecondLevelMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.secondLevelLoading = true
        getCatalogueApi({
          // rowCount: 50,
          level: 2,
          name: key
        }).then(res => {
          this.secondLevelLoading = false
          if (res && res.data) {
            this.secondLevelOptions = res.data.map(row => ({
              value: row.id,
              label: row.name
            }))
          } else {
            this.secondLevelOptions = []
          }
        }).catch(err => {
          this.$message.error(err.msg || err.message || '获取二级目录失败！')
          this.secondLevelOptions = []
        })
      } else {
        // this.secondLevelOptions = []
      }
    }
  }
}
</script>
