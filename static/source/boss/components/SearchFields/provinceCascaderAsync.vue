<template>
<el-cascader
  ref="cascader"
  :value="value"
  :props="props"
  clearable
  @change="handleChange">
</el-cascader>
</template>
<script>
import { getAreaAsync } from './api'
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Array,
    checkStrictly: <PERSON><PERSON><PERSON>,
    multiple: <PERSON><PERSON><PERSON>,
    maxDepth: {
      type: Number,
      default: 3
    }
  },
  data () {
    return {
      options: [],
      props: {
        lazy: true,
        lazyLoad: this.lazyLoad,
        checkStrictly: this.checkStrictly,
        multiple: this.multiple
      }
    }
  },
  methods: {
    handleChange (value) {
      let nodes = []
      try {
        nodes = this.$refs.cascader.getCheckedNodes()[0].pathNodes
      } catch (err) {}
      this.$emit('change', value)
      this.$emit('provinceChange', nodes)
    },
    lazyLoad (node, resolve) {
      let { level, value } = node
      if (level >= this.maxDepth) return resolve([])
      level++
      getAreaAsync({ level, parentCode: value })
        .then(res => {
          if (res.code === 200 && res.data) {
            const tmp = res.data.map(item => ({
              ...item,
              value: String(item.code),
              label: item.name,
              leaf: level >= this.maxDepth
            }))
            resolve(tmp)
          } else {
            this.$message.error(res.msg || res.message || '查询失败！')
            resolve([])
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || err.message || '查询失败！')
          resolve([])
        })
    }
  },
  created () {
  }
}
</script>
