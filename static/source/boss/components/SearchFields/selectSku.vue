<template>
  <el-select
    :style="{ width: width }"
    :value="value"
    ref="selectSku"
    filterable
    remote
    clearable
    :disabled="disabled"
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :loading="loading"
  >
    <el-option
      v-for="item in skuOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"></el-option>
  </el-select>
</template>
<script>
import {
  getSkuList
} from './api.js'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'SelectSku',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    value: [ String, Number ],
    skuName: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    needMaterialGroup: {
      type: Boolean,
      default: false
    }
  },
  created () {
    this.remoteMethod = spDebounce(this.remoteMethod)
  },
  mounted() {

  },
  watch: {
    skuName(val) {
      if (this.value && val) { // 初始有值时无法回显，手动push初始选项到options里
        this.skuOptions.push({
          value: this.value,
          label: val
        })
      }
    }
  },
  data () {
    return {
      skuOptions: [],
      loading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      let value
      try {
        value = this.skuOptions.find(item => item.value === val)
      } catch (err) { console.log(err) }
      console.log(val)
      this.$emit('change', val)
      this.$emit('selectChange', value)
    },
    // 远程查找用户
    remoteMethod (query) {
      console.log(query)
      const key = query.trim()
      if (key !== '') {
        this.loading = true
        getSkuList(key, this.needMaterialGroup).then(res => {
          if (res.data && res.data.length > 0) {
            this.skuOptions = res.data.map(item => ({
              ...item,
              value: item.skuNo,
              label: item.skuNo + ' ' + item.materialDescribe
            }))
          } else {
            this.skuOptions = []
          }
        }).finally(() => {
          this.loading = false
        })
      } else {
        this.skuOptions = []
      }
    }
  }
}
</script>
