<template>
  <el-select
    :value="value"
    filterable
    remote
    clearable
    :placeholder="placeholder"
      :multiple="multiple"
    :collapseTags="collapseTags"
    @change="handleSelectChange"
    :remote-method="remoteBrandIdMethod"
    :disabled="disabled"
    :loading="brandIdLoading">
    <el-option
      v-for="item in brandIdOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value">
    </el-option>
  </el-select>
</template>
<script>
import {
  getBrandListApi
} from './api.js'
import _ from 'lodash'
export default {
  name: 'remoteBrandComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: [ String, Number, Array ],
    getLabel: Boolean,
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    defaultLabel: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      brandIdOptions: [],
      brandIdLoading: false
    }
  },
  watch: {
    defaultLabel (val) {
      if (val && this.value) {
        const brandIdOptions = [...this.brandIdOptions, {
          label: val,
          value: this.value
        }]
        this.brandIdOptions = _.uniqBy(brandIdOptions, 'value')
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      this.$emit('change', val)
      const curName = this.brandIdOptions.find(it => it.value === val)?.label || ''
      this.$emit('getlabel', curName)
    },
    // 远程查找品牌
    remoteBrandIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.brandIdLoading = true
        getBrandListApi({
          brand_name: key,
          pageSize: 50
        }).then(res => {
          this.brandIdLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.brandIdOptions = res.data.contents.map(item => {
                return {
                  value: item.brandId,
                  label: item.brandName
                }
              })
            } else {
              this.brandIdOptions = []
            }
          } else {
            this.brandIdOptions = []
          }
        })
      } else {
        // this.brandIdOptions = []
      }
    }
  }
}
</script>
