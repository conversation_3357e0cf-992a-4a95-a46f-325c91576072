<template>
  <el-select
    :value="value"
    filterable
    default-first-option
    remote
    clearable
    :multiple="multiple"
    :collapseTags="collapseTags"
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteProductGroupIdMethod"
    :disabled="disabled"
    :loading="productGroupIdLoading"
  >
    <el-option
      v-for="item in productGroupIdOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value"></el-option>
  </el-select>
</template>
<script>

import {
  getMaterialGroupList
} from './api.js'
export default {
  name: 'remoteMaterialGroupComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    value: [ String, Number, Array ],
    getLabel: Boolean,
    disabled: {
      type: Boolean,
      default: false
    },
    defaultLabel: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      productGroupIdOptions: [],
      productGroupIdLoading: false
    }
  },
  created() {
    if (this.defaultLabel) {
      this.remoteProductGroupIdMethod(this.defaultLabel)
    }
  },
  methods: {
    handleSelectChange (val) {
      this.$emit('change', val)
      const curName = this.productGroupIdOptions.find(it => it.value === val)?.label || ''
      this.$emit('getlabel', curName)
    },
    // 远程查找物料组
    remoteProductGroupIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.productGroupIdLoading = true
        getMaterialGroupList({
          group_name: key
        }).then(res => {
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.productGroupIdOptions = res.data.contents.map(item => {
                return {
                  value: item.productGroupId,
                  label: item.productGroupName
                }
              })
            } else {
              this.productGroupIdOptions = []
            }
          } else {
            this.productGroupIdOptions = []
          }
        }).finally(() => {
          this.productGroupIdLoading = false
        })
      } else {
        // this.productGroupIdOptions = []
      }
    }
  },
  watch: {
    defaultLabel(newVal) {
      if (newVal === '') {
        return
      }
      this.remoteProductGroupIdMethod(newVal)
    }
  }
}
</script>
