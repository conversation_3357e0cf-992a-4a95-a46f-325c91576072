<template>
  <el-select
    :style="{width:width}"
    :value="value"
    filterable
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :disabled="disabled"
    :remote-method="remoteCustomerMethod"
    :loading="customerNoLoading"
  >
    <el-option
      v-for="(item, index) in customerNoOptions"
      :key="item.customerNumber"
      :label="item.customerName"
      :disabled="index===0"
      :value="getLabel? item.customerName: item.customerNumber">
        <div
          class="ba-row-start selectClientItem"
          :style="{fontWeight:index===0?'bold':'normal'}"
        >
          <div>{{ item.customerNumber }}</div>
          <div>{{ item.cityName }}</div>
          <div>{{ item.customerName }}</div>
        </div>
    </el-option>
  </el-select>
</template>
<script>
import {
  searchClients as getCustomerList
} from '@/api/orderSale'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'remoteMaterialGroupComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    disabled: {
      type: Boolean,
      default: false

    },
    value: [ String, Number ],
    headName: {
      type: String,
      default: '供应商'
    },
    getLabel: Boolean
  },
  created () {
    console.log(this.value)
    this.remoteCustomerMethod = spDebounce(this.remoteCustomerMethod)
    if (this.value) {
      this.remoteCustomerMethod(this.value)
    }
  },
  data () {
    return {
      customerNoOptions: [],
      customerNoLoading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      let value
      try {
        value = this.customerNoOptions.filter(customer => (this.getLabel ? customer.customerName : customer.customerNumber) === val)[0]
        console.log(val, value)
      } catch (err) { console.log(err) }
      this.$emit('change', val, value)
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      console.log(query)
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerList(key).then(res => {
          if (res.code === 200 && res.data && res.data.length > 0) {
            this.customerNoOptions = [
              {
                customerNumber: `${this.headName}编码`,
                customerName: `${this.headName}名称`,
                cityName: '城市'
              },
              ...res.data
            ]
          } else {
            this.customerNoOptions = []
          }
        }).finally(() => {
          this.customerNoLoading = false
        })
      } else {
        this.customerNoOptions = []
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
    overflow: auto;
  }
}
.ba-row-start{
  display: flex;
  color: #606266;
}
</style>
