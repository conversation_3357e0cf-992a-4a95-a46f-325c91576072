<template>
  <el-cascader
    ref="cascader"
    :value="value"
    :props="props"
    :options="options"
    :collapse-tags="collapseTags"
    clearable
    filterable
    @change="handleChange"
    :disabled="disabled"
  >
  </el-cascader>
</template>
<script>
import { getCatalogTree } from '@/api/purchasePlan';
export default {
  name: 'CattagoryCascader',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Array,
    checkStrictly: Boolean,
    multiple: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: [],
      props: {
        multiple: this.multiple,
        checkStrictly: this.checkStrictly,
        label: 'name',
        value: 'id'
      }
    };
  },
  methods: {
    handleChange(value) {
      let nodes = [];
      try {
        nodes = this.$refs.cascader.getCheckedNodes()[0].pathNodes;
      } catch (err) {}
      this.$emit('change', value, nodes);
    },
    formatTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.formatTreeData(data[i].children);
        }
      }
      return data;
    }
  },
  created() {
    getCatalogTree({ type: 0 }).then((res) => {
      if (res.success === true && res.data && res.data.length) {
        this.options = this.formatTreeData(res.data);
      }
    })
  }
};
</script>
