<template>
  <el-select
    :style="{width:width}"
    :value="value"
    filterable
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteCustomerMethod"
    :loading="customerNoLoading"
  >
    <el-option
      v-for="item in customerNoOptions"
      :key="item.value"
      :label="item.depName?item.label + '-' + item.depName:item.label"
      :value="getLabel? item.label: item.value"></el-option>
  </el-select>
</template>
<script>
import {
  getAfterSaleCustomerList as getCustomerList
} from './api.js'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'remoteMaterialGroupComponent',
  // model: {
  //   prop: 'value',
  //   event: 'change'
  // },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    value: [ String, Number ],
    getLabel: Boolean
  },
  created () {
    this.remoteCustomerMethod = spDebounce(this.remoteCustomerMethod)
  },
  data () {
    return {
      customerNoOptions: [],
      customerNoLoading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      let value
      try {
        value = this.customerNoOptions.filter(customer => customer.value === val)[0]
      } catch (err) { console.log(err) }
      console.log(value)
      this.$emit('change', val, value)
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      console.log(query)
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerList({ username: key }).then(res => {
          if (res.code === 200 && res.data && res.data.length > 0) {
            this.customerNoOptions = res.data.map(item => ({
              ...item,
              value: item.userId,
              label: item.nickName,
              depName: item.depName
            }))
          } else {
            this.customerNoOptions = []
          }
        }).finally(() => {
          this.customerNoLoading = false
        })
      } else {
        this.customerNoOptions = []
      }
    }
  }
}
</script>
