<template>
  <el-select
    :value="value"
    filterable
    default-first-option
    :multiple="multiple"
    remote
    clearable
    :disabled="disabled"
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remotefourLevelMethod"
    :loading="fourLevelLoading">
    <el-option
      v-for="item in fourLevelOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value">
    </el-option>
  </el-select>
</template>
<script>
import {
  getCatalogueApi
} from './api.js'
export default {
  name: 'fourthLevelDirectory',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: [ String, Number, Array ],
    getLabel: Boolean,
    defaultLabel: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      fourLevelOptions: [],
      fourLevelLoading: false
    }
  },
  watch: {
    defaultLabel (val) {
      if (val && this.value) {
        this.fourLevelOptions.push({
          label: val,
          value: this.value
        })
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      this.$emit('change', val)
      const curName = this.fourLevelOptions.find(it => it.value === val)?.label || ''
      this.$emit('getlabel', curName)
    },
    // 远程查找四级目录
    remotefourLevelMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.fourLevelLoading = true
        getCatalogueApi({
          // rowCount: 50,
          level: 4,
          name: key
        }).then(res => {
          this.fourLevelLoading = false
          if (res && res.data) {
            this.fourLevelOptions = res.data.map(row => ({
              value: row.id,
              label: row.name
            }))
          } else {
            this.fourLevelOptions = []
          }
        }).catch(err => {
          this.$message.error(err.msg || err.message || '获取四级目录失败！')
          this.fourLevelOptions = []
        })
      } else {
        this.fourLevelOptions = []
      }
    }
  }
}
</script>
