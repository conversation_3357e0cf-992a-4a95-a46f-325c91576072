<template>
  <el-select
    :style="{width:width}"
    :value="value"
    filterable
    remote
    clearable
    @change="handleSelectChange"
    :multiple="multiple"
    :collapseTags="collapseTags"
    default-first-option
    :placeholder="placeholder"
    :remote-method="remoteCustomerMethod"
    :loading="customerNoLoading"
  >
    <el-option
      v-for="item in customerNoOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value"></el-option>
  </el-select>
</template>
<script>
import { isEmpty } from 'lodash'
import {
  getCustomerList
} from './api.js'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'remoteMaterialGroupComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    value: [ String, Number, Array ],
    initCustomer: {
      type: Object,
      default: () => ({})
    },
    getLabel: Boolean,
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    customerName: {
      type: String,
      default: ''
    }

  },
  created () {
    this.remoteCustomerMethod = spDebounce(this.remoteCustomerMethod)
  },
  watch: {
    value: {
      handler: function (newVal, oldVal) {
        if (newVal && !this.customerNoOptions.find(item => item.value === newVal)) {
          if (!isEmpty(this.initCustomer)) {
            this.customerNoOptions = [this.initCustomer]
          } else {
            this.remoteCustomerMethod(newVal)
          }
        }
      },
      immediate: true
    }
  },
  data () {
    return {
      customerNoOptions: [],
      customerNoLoading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      let value
      try {
        value = this.customerNoOptions.find(customer => customer.value === val)
        this.$emit('change', val, value)
        this.$emit('selectChange', val, value)
      } catch (err) { console.log(err) }
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      console.log(query)
      if (!query) return;
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerList({ customer_name: key }).then(res => {
          if (res.code === 200 && res.data.contents && res.data.contents.length > 0) {
            this.customerNoOptions = res.data.contents.map(item => ({
              value: item.customerNo,
              label: item.customerName
            }))
          } else {
            this.customerNoOptions = []
          }
        }).finally(() => {
          this.customerNoLoading = false
          if (this.value) {
            this.handleSelectChange(this.value)
          }
        })
      } else {
        this.customerNoOptions = []
      }
    }
  }
}
</script>
