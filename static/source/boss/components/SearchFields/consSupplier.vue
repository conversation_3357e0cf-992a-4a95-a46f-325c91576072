<template>
  <el-select
    :style="{width:width}"
    :value="value"
    filterable
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :disabled="disabled"
    :loading="loading"
  >
    <el-option
      v-for="(item, index) in options"
      :key="item[props.value]+index"
      :label="item[props.label]"
      :disabled="index===0"
      :value="getLabel? item[props.label]: item[props.value]">
        <div
          class="ba-row-start selectClientItem"
          :style="{fontWeight:index===0?'bold':'normal'}"
        >
          <div>{{ item[props.value] }}</div>
          <div>{{ item[props.label] }}</div>
        </div>
    </el-option>
  </el-select>
</template>
<script>
import { getSupplierList as getList } from './api'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'supplier',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    value: [ String, Number ],
    supplierName: {
      type: String,
      default: ''
    },
    getLabel: Boolean,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  created () {
    console.log(this.disabled)
    console.log(this.value)
    this.remoteMethod = spDebounce(this.remoteMethod)
  },
  data () {
    return {
      options: this.value && this.supplierName ? [
        {
          providerNo: this.value,
          providerName: this.supplierName
        }
      ] : [],
      loading: false,
      props: {
        value: 'providerNo',
        label: 'providerName'
      }
    }
  },
  watch: {
    supplierName() {
      if (this.supplierName) {
        this.options = [
          {
            providerNo: this.value,
            providerName: this.supplierName
          }
        ]
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      let value
      try {
        value = this.options.filter(item => (this.getLabel ? item[this.props.label] : item[this.props.value]) === val)[0]
      } catch (err) { console.log(err) }
      this.$emit('change', val, value)
      this.$emit('getlabel', value.providerName)
    },
    remoteMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading = true
        getList(key).then(res => {
          if (res && res.data?.length > 0) {
            this.options = [
              {
                [this.props.value]: '供应商编码',
                [this.props.label]: '供应商名称'
              },
              ...res.data.map(item => ({
                providerNo: item.supplierNo,
                providerName: item.supplierName,
                providerId: item.providerId
              }))
            ]
          } else {
            this.options = []
          }
        }).finally(() => {
          this.loading = false
        })
      } else {
        // this.options = []
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 300px;
  }
}
.ba-row-start{
  display: flex;
  color: #606266;
}
</style>
