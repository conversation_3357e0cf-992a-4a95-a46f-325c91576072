<template>
  <el-cascader
    ref="cascader"
    :value="value"
    :props="props"
    :options="options"
    clearable
    @change="handleChange"
  >
  </el-cascader>
</template>
<script>
import { getCatalogueApi } from './api';
export default {
  name: 'DirectoryCascader',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: Array,
    primaryDirectoryNo: [String, Number],
    checkStrictly: Boolean
  },
  data() {
    return {
      options: [],
      props: {
        dirChanged: false,
        multiple: true,
        value: 'id',
        label: 'name',
        lazy: true,
        lazyLoad: this.lazyLoad,
        checkStrictly: this.checkStrictly
      }
    };
  },
  watch: {
    value: {
      handler: function (newVal) {
        if (newVal && !this.dirChanged) {
          // 回写状态
          const secondDirIds = new Set();
          const thirdDirIds = new Set();
          for (let i = 0; i < newVal.length; i++) {
            secondDirIds.add(newVal[i][0]);
            thirdDirIds.add(newVal[i][1]);
          }
          Promise.all([
            getCatalogueApi({
              rowCount: 50,
              level: 2,
              parentId: this.primaryDirectoryNo || ''
            }),
            ...[...secondDirIds].map((id) =>
              getCatalogueApi({
                rowCount: 50,
                level: 3,
                parentId: id
              })
            ),
            ...[...thirdDirIds].map((id) =>
              getCatalogueApi({
                rowCount: 50,
                level: 4,
                parentId: id
              })
            )
          ]).then((datas) => {
            if (datas) {
              const remoteIds = [
                this.primaryDirectoryNo || '',
                ...secondDirIds,
                ...thirdDirIds
              ];
              newVal.forEach((linked) => {
                for (let i = linked.length - 2; i >= 0; i--) {
                  const remoteIndex = remoteIds.indexOf(linked[i]);
                  const data =
                    datas[remoteIndex] &&
                    (datas[remoteIndex].data || []).map((item) => ({
                      ...item,
                      leaf: i === 1
                    }));
                  const remoteParentIndex = remoteIds.indexOf(
                    i > 0 ? linked[i - 1] : this.primaryDirectoryNo
                  );
                  if (datas[remoteParentIndex]) {
                    const parentData = (
                      datas[remoteParentIndex].data || []
                    ).find((row) => row.id === linked[i]);
                    parentData && (parentData.children = data);
                  }
                }
              });

              this.options = datas[0].data;
            }
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    handleChange(value) {
      this.dirChanged = true;
      let nodes = [];
      try {
        nodes = this.$refs.cascader.getCheckedNodes()[0].pathNodes;
      } catch (err) {}
      this.$emit('change', value);
      this.$emit('directoryChange', nodes);
    },
    lazyLoad(node, resolve) {
      let { level, data } = node;
      if (level >= 3) {
        resolve([]);
        return;
      }
      level++;
      const params = { rowCount: 50, level: 1 + level };
      if (data && data.id) {
        params.parentId = data.id;
      }
      if (this.primaryDirectoryNo && level === 1) {
        params.parentId = this.primaryDirectoryNo;
      }
      getCatalogueApi(params)
        .then((res) => {
          if (res.data) {
            const tmp = res.data.map((item) => ({
              ...item,
              leaf: level >= 3
            }));
            resolve(tmp);
          } else {
            this.$message.error(res.msg || res.message || '查询失败！');
            resolve([]);
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || err.message || '查询失败！');
          resolve([]);
        });
    }
  },
  created() {}
};
</script>
