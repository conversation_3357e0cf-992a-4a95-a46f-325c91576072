<template>
  <div class="dn-plan">
    <el-form label-width="100px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="工厂">
            <el-select v-model="mdl.factory" filterable placeholder="请选择工厂" style="width:100%">
              <el-option v-for="item in factoryList"
                :key="'dn-factory'+item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库位">
            <el-select v-model="mdl.position" filterable placeholder="请选择库位" style="width:100%">
              <el-option
                v-for="item in positionList"
                :key="'dn-position'+item.code"
                :label="item.code+item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="交货日期">
            <el-date-picker
              v-model="mdl.deliveryDate"
              type="date"
              unlink-panels
              placeholder="请选择交货日期"
              value-format="yyyy-MM-dd"
              style="width:100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="商品编码">
            <el-input v-model="mdl.materiel" placeholder="请填写商品编码" />
          </el-form-item>
        </el-col>
        <el-col :span="9" class="omsOrderNo" v-if="showAddSoNo">
          <el-form-item label="OMS订单号">
            <el-input v-model="omsNo" placeholder="请填写OMS订单号" style="width: 220px" />
            <el-button style="marginLeft: 20px" @click="addOmsNo">添加</el-button>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label-width="0">
            <el-checkbox v-model="mdl.dnEnableFlag" label="仅显示可生成DN的计划行" />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-button type="primary" @click="handleQuery">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div class="dnPlan-row">
      <p>
        <span style="marginRight:8px">当前已选择订单:</span>
        <el-tag v-if="!omsNoList||omsNoList.length===0">空</el-tag>
        <el-tag v-for="o in omsNoList" :key="o.soNo" :closable="o.closable!==false" @close="closeOmsNoTag(o)">{{ o.soNo }}</el-tag>
      </p>
    </div>
    <p class="dnPlan-tips">
      <i class="el-icon-info" />
      当满足以下情况时，计划行将无法勾选生成DN：1.确认数量无法满足订单数量；2.已存在交货数量，无法重复做DN；3.当前时间小于交货日期，无法做DN
    </p>
    <el-table border :data="itemList" max-height="500" style="width: 100%" v-loading="isLoading" @selection-change="handleSelectionChange">
      <el-table-column align="center" type="selection" width="50" :selectable='checkboxT' />
      <el-table-column align="center" prop="referOrderNo" label="OMS订单号" width="180" />
      <el-table-column align="center" prop="referOrderItemNo" label="行项目编码" width="100" />
      <el-table-column align="center" prop="materiel" label="商品编号" width="100" />
      <el-table-column align="center" prop="materialName" label="商品名称" width="200"/>
      <el-table-column align="center" prop="wmeng" label="订单数量" width="80" />
      <el-table-column align="center" prop="bmeng" label="确认数量" width="80" />
      <el-table-column align="center" prop="clearedQty" label="已交货数量" width="80" />
      <el-table-column align="center" prop="quantityUnit" label="销售单位" width="120">
        <template slot-scope="{row}">
          {{ formatUnit(row.quantityUnit) }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="ettyp" label="计划行类型" width="90" />
      <el-table-column align="center" prop="factory" label="工厂" width="260" />
      <el-table-column align="center" prop="position" label="库位" width="120" />
        <!-- <template slot-scope="{row}">
          {{ row.position | dict(dictList,'position') }}
        </template>
      </el-table-column> -->
      <el-table-column align="center" prop="demandUser" label="领用人" width="80" />
      <el-table-column align="center" prop="edatu" label="交货日期" width="100" />
    </el-table>
    <div class="btn-group">
      <el-button type="primary" @click="handleSubmit">确认{{showAddSoNo ? "添加" :  "创建"}}</el-button>
      <el-button @click="$emit('update:showDialog', false)">取消</el-button>
    </div>
  </div>
</template>

<script>
import { sapOrderDetailQuery, orderDetail } from '@/api/orderSale'
import { getQuantityUnitName } from '@/utils/order'

export default {
  props: {
    showDialog: {
      type: Boolean,
      required: true
    },
    showAddSoNo: {
      type: Boolean,
      required: true
    },
    soNoInfo: {
      required: false
    },
    exclude: {
      required: false
    }
  },
  data () {
    return {
      isLoading: false,
      mdl: {},
      itemList: [],
      omsNo: '', // 对应输入框的model
      omsNoList: [...this.soNoInfo],
      selectedItemList: []
    }
  },
  created () {
    if (this.soNoInfo && this.soNoInfo.length > 0) {
      const data = [...this.soNoInfo]
      this.doQuery({
        omsNos: data.map(item => {
          const { soNo, soItemNo } = item
          return {
            soNo,
            soItemNo
          }
        })
      })
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    factoryList () {
      const factories = []
      if (this.dictList['factory']) {
        this.dictList['factory'].forEach(item => {
          const foundItem = factories.find(f => f.code === item.code)
          if (!foundItem) {
            factories.push(item)
          }
        })
      }
      return factories
    },
    positionList () {
      const positions = this.dictList['position'] || []
      if (this.mdl.factory) {
        return positions.filter(
          item => item.parentCode === this.mdl.factory && item.code !== -1
        )
      }
      return []
    }
  },
  methods: {
    async checkOmsNo (omsNo) {
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const res = await orderDetail({
        omsNo
      })
      loading.close()
      if (res && res.code === 200 && res.data && res.data.orderStatus === 'cancel') {
        this.$message.warning({
          message: '该订单已取消，请重新输入'
        })
        return false
      }
      if (res && res.code === 200 && JSON.stringify(res.data) === '{}') {
        this.$message.warning({
          message: '该订单不存在或者未下发成功，请重新输入'
        })
        return false
      }
      if (res && res.code === 200 && res.data && (res.data.sapOrderNo || res.data.soNo)) {
        return true
      }
      return false
    },
    async addOmsNo () {
      if (this.omsNo) {
        const isValid = await this.checkOmsNo(this.omsNo)
        if (isValid) {
          this.omsNoList.push({
            soNo: this.omsNo
          })
          this.omsNo = ''
        }
      }
    },
    closeOmsNoTag (omsNo) {
      const index = this.omsNoList.indexOf(omsNo)
      if (index > -1) {
        this.splice(index, 1)
      }
    },
    checkboxT (row, index) {
      const { referOrderNo, referOrderItemNo } = row
      let isDuplicated = false
      if (this.exclude && this.exclude.length > 0) {
        isDuplicated = this.exclude.some(item => {
          const { referOrderItemNo: roin, referOrderNo: ron } = item
          return referOrderNo === ron && roin === referOrderItemNo
        })
      }
      if (row.dnEnableFlag && !isDuplicated) {
        return 1
      } else {
        return 0
      }
    },
    handleSubmit () {
      if (this.selectedItemList.length === 0) {
        this.$message.warning({
          message: '请选择商品计划行'
        })
      } else {
        this.$emit('submit', this.selectedItemList)
        this.$emit('update:showDialog', false)
      }
    },
    handleSelectionChange (val) {
      // this.selectedItemList = val.map(item => {
      //   return `${item.referOrderNo}-${item.referOrderItemNo}-${item.referOrderItemDetailNo}`
      // })
      this.selectedItemList = val
    },
    clean (obj) {
      for (var propName in obj) {
        if (!obj[propName]) {
          delete obj[propName]
        }
      }
    },
    doQuery (params) {
      this.isLoading = true
      sapOrderDetailQuery(params).then(res => {
        this.isLoading = false
        if (res && res.code === 200 && res.data && res.data.infos) {
          this.itemList = res.data.infos
        }
      }).catch(error => {
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误')
          }
        }
        this.isLoading = false
      })
    },
    handleReset () {
      this.mdl = {
        dnEnableFlag: false
      }
      this.omsNo = ''
      if (this.soNoInfo) {
        const data = [...this.soNoInfo]
        const params = {
          ...this.mdl,
          omsNos: data.map(item => {
            const { soNo, soItemNo } = item
            return {
              soNo,
              soItemNo
            }
          })
        }
        this.clean(params)
        this.doQuery(params)
      } else {
        this.itemList = []
      }
    },
    handleQuery () {
      if (!this.omsNoList || this.omsNoList.length === 0) {
        this.$message.error('请添加订单后再进行查询')
        return
      }
      const data = [...this.omsNoList]
      const params = {
        ...this.mdl,
        omsNos: data.map(item => {
          const { soNo, soItemNo } = item
          return {
            soNo,
            soItemNo
          }
        })
      }
      this.clean(params)
      this.doQuery(params)
    },
    formatUnit (unit) {
      return getQuantityUnitName(unit, this.dictList)
    }
  }
}
</script>

<style lang="scss">
.dnPlan-row {
  margin-bottom: 10px;
}
.dnPlan-tips {
  font-size: 12px;
  margin-left: 30px;
  margin-bottom: 13px;
  color: #999;
}
.btn-group {
  margin-top: 10px;
  text-align: center;
}
</style>
