<template>
  <el-dialog
    title="请勾选需要生成DN的商品计划行"
    :show-close="false"
    :visible.sync="showDlg"
    :destroy-on-close="true"
    top="10px"
    width="1250px"
    custom-class="SelectDNPlan"
  >
    <DNPlan
      :showDialog.sync="showDlg"
      :soNoInfo="soNoInfo"
      :showAddSoNo="showAddSoNo"
      :exclude="exclude"
      @submit="handleSubmit"
    />
  </el-dialog>
</template>

<script>
// import { orderDetail } from '@/api/orderSale'
import DNPlan from './Plan'

export default {
  components: { DNPlan },
  props: {
    showDialog: {
      type: Boolean,
      required: true
    },
    showAddSoNo: {
      type: Boolean,
      default: true
    },
    soNoInfo: {
      required: false
    },
    exclude: {
      required: false
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    handleSubmit (val) {
      this.$emit('submit', val)
    }
  }
}
</script>

<style lang="scss">
</style>
