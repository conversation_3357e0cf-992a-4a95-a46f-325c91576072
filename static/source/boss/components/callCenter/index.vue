<template>
  <div class="call-center" v-loading="pageLoading">
    <div class="base-info">
      <h2>客户基础信息</h2>
      <div class="flex-item">
        <el-form style="width:100%" label-width="160px" label-position="right" >
          <el-row>
            <el-col :span="12" v-for="(item, index) in baseInfoColumns" :key="index">
              <el-form-item :label="item.label">
                <span class="value">{{baseInfo[item.prop]}}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="seller-config">
      <h2>销售配置信息</h2>
      <el-table border
        class="table"
        :data="listData">
        <el-table-column align="center" v-for="(column,index) in tableListColumns" :key="index"
          :prop="column.prop" :label="column.label" />
      </el-table>
    </div>
  </div>
</template>
<script>
import { getQueryObject } from '@/utils/index'
import { udeskCustomerAndSellerApi } from '@/api/udesk'
export default {
  name: 'CallCenter',
  data () {
    return {
      pageLoading: false,
      baseInfo: {},
      baseInfoColumns: [
        { label: '客户名称：', prop: 'customerName' },
        { label: 'ZKH客户编码：', prop: 'customerNumber' },
        { label: '客户类型：', prop: 'businessPartnerGroupName' },
        { label: '来电人姓名：', prop: 'fullName' }
      ],
      listData: [],
      tableListColumns: [
        { label: '销售配置编号', prop: 'csfId' },
        { label: '客服', prop: 'partner5Name' },
        { label: '销售', prop: 'partner6Name' },
        { label: '产品组', prop: 'spartName' }
      ]
    }
  },
  mounted () {
    this.initData()
  },
  methods: {
    initData () {
      const href = location.href.toLowerCase()
      const query = getQueryObject(href)
      console.log(query)
      const mobile = query.mobile
      const params = {
        sign: query.sign,
        timestamp: query.timestamp
      }
      if (!mobile) return this.$message.error('mobile参数缺失！')
      this.pageLoading = true
      udeskCustomerAndSellerApi(mobile, params)
        .then(res => {
          if (res && res.code === 200 && res.data) {
            this.baseInfo = res.data
            this.listData = res.data.customerSaleOrgForceList
          } else {
            this.$message.error(res.msg || res.message || '获取信息失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '获取信息失败！')
        })
        .finally(() => {
          this.pageLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.call-center {
  max-width: 960px;
  min-height: 90vh;
  margin: auto;
  margin-top: 10px;
  border: solid 1px lightgrey;
  border-radius: 5px;
  padding: 5px;
  h2{
    background: #4682b45c;
    display: inline-block;
    padding: 5px;
    margin: 10px;
    margin-left: 1%;
  }
  .flex-item{
    .label{
      text-align: right;
    }
  }
  .seller-config{
    .table{
      width: 98%;
      margin: auto;
    }
  }
}
</style>
