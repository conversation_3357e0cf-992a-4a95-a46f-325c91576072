<template>
  <el-select
    popper-class="module-select-customer"
    filterable clearable remote :placeholder="placeholder"
    :value="name" :remote-method="getCustomerList" :loading="loading.customer"
    @change="afterChangeCustomer" @clear="afterClear">
    <el-option v-for="(item, index) in option.customer"
      :key="item.customerCode" :label="item.customerName" :value="item.customerCode"
      :disabled="index === 0">
      <p class="option" :class="{ bold: index === 0 }">
        <span >{{ item.customerCode }}</span>
        <span >{{ item.city }}</span>
        <span >{{ item.customerName }}</span>
      </p>
    </el-option>
  </el-select>
</template>

<script>

import api from '@/api/kunheAssistant'

export default {
  data () {
    return {
      name: this.value,
      loading: {
        customer: false
      },
      option: {
        customer: []
      }
    }
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: {
      type: String
    }
  },
  watch: {
    value (val) {
      this.name = val
      this.$emit('inpput', val)
    }
  },
  methods: {
    afterChangeCustomer (val) {
      const target = this.option.customer.find(item => item.customerCode === val)
      this.$emit('input', target ? target.customerName : '')
      this.$emit('select', target)
    },
    afterClear () {
      this.$emit('input', '')
    },
    getCustomerList (query) {
      if (!query) return
      this.loading.customer = true
      api({
        url: '/staff/customer',
        query: {
          customerName: query,
          page: 0,
          pageSize: 100
        }
      }).then(res => {
        if (res.code === 200 && res.data) {
          this.option.customer = [
            {
              customerCode: '客户编码',
              city: '城市',
              customerName: '客户名称'
            },
            ...res.data
          ]
        }
        this.loading.customer = false
      }).catch(err => {
        console.log(err)
        this.loading.customer = false
      })
    }
  }
}
</script>

<style lang="scss">
.module-select-customer{
  width: 560px;
  .option{
    display: flex;
    span{
      font-weight: normal;
      width: 20%;
      color: #666;
      &:last-child{
        width: auto;
      }
    }
  }
  .bold {
    span {
      font-weight: bold;
      color: #333;
    }
  }
}
</style>
