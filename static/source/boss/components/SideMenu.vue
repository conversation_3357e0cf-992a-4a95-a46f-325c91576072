<template>
  <div class="module module-side-menu">
    <el-input
      v-model="filterInput"
      class="menu-filter"
      clearable
      filterable
      placeholder="请输入菜单名称进行搜索"
    />
    <div class="module-menu">
      <ul class="module-menu-ul">
        <li v-for="(item, index) in menudata" :key="item.name + index" @click="handleClick(item.name)">
          <p
            class="title"
            @click="toggleMenu(item)"
            :class="getFirstLevelClass(item)"
            v-if="hasChilren(item)"
          >
            <template>
              <span :class="{ 'menu-text': true, filtering: item.filtering }">{{
                item.name
              }}</span>
              <i class="el-icon-arrow-up toggle-menu-icon"></i>
            </template>
          </p>
          <template v-else>
            <zkh-link
              :commentType="lintType"
              v-if="linkFilter(item.link).local"
              :targetUrl="linkFilter(item.link).link"
              :title="item.name"
              >
              <span :class="getFirstLevelClass(item)">
                <span class="menu-text">{{ item.name }}</span>
              </span>
            </zkh-link>
            <zkh-link
              v-else
              :commentType="lintType"
              :targetUrl="linkFilter(item.link).link"
              :title="item.name"

              >
              <span :class="getFirstLevelClass(item)">
                <span class="menu-text">{{ item.name }}</span>
              </span>
            </zkh-link>
          </template>
          <el-collapse-transition>
            <ul v-show="item.show">
              <li
                v-for="(child, idx) in item.children"
                :key="child.name + item.name + idx"
              >
                <zkh-link
                  :commentType="lintType"
                  v-if="linkFilter(child.link).local"
                  :targetUrl="linkFilter(child.link).link"
                  :title="child.name"
                  :class="{ 'second-menu': true, filtering: child.filtering }"
                  >{{ child.name }}</zkh-link
                >
                <zkh-link
                  v-else
                  :commentType="lintType"
                  :targetUrl="linkFilter(child.link).link"
                  :title="child.name"
                  :class="{ 'second-menu': true, filtering: child.filtering }"
                  >{{ child.name }}</zkh-link
                >
              </li>
            </ul>
          </el-collapse-transition>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { sensors, getCurBizRole } from '@/utils';
import request from '@/utility/request';

export default {
  data () {
    return {
      isToggle: false,
      menudata: this.filterMenuType(this.menu),
      lintType: 'a',
      filterInput: ''
    }
  },
  props: {
    menu: {
      type: Array,
      default: () => []
    },
    reg: {
      type: RegExp
    }
  },
  watch: {
    filterInput (val) {
      let menu = JSON.parse(JSON.stringify(this.menu))
      menu.forEach(item => {
        item.show = false
      })
      try {
        if (val) {
          menu = menu.filter(m => {
            const firstMenu = m.name.indexOf(val) !== -1
            const secondMenu =
              m.children &&
              m.children.find(
                child => child.type === 'MENU' && child.name.indexOf(val) !== -1
              )
            secondMenu && (secondMenu.filtering = true)
            // m.filtering = false
            // if (firstMenu) {
            //   m.filtering = true
            // }
            if (secondMenu) {
              m.children = m.children.filter(
                child => child.type === 'MENU' && child.name.indexOf(val) !== -1
              )
              m.show = true
            }
            return firstMenu || secondMenu
          })
        }
      } catch (err) {
        console.log(err)
      }
      console.log(menu, val)
      this.menudata = this.filterMenuType(menu)
    },
    menu (val) {
      let menu = JSON.parse(JSON.stringify(val))
      menu.forEach(item => {
        item.show = false
      })
      this.menudata = this.filterMenuType(menu)
    }
  },

  methods: {
    trackOrigin(role, type) {
      console.log(role, type);
      sensors('WorkbenchView', {
          current_security_role_name: role || [],
          first_menu_name: '我的工作台',
          label_name: '我的工作台',
          platform_type: 'BOSS-工作台',
          current_page_type: type || '',
          operation_type: '菜单'
        })
    },
    handleClick(name) {
      if (name === '我的工作台') {
        try {
          let curBizRole = getCurBizRole();
          if (curBizRole) {
            this.trackOrigin([curBizRole.bizRoleName], curBizRole.workbenchRoleName)
          } else {
            request({
              url: '/api-opc-csc/api/workbench/item/list',
              method: 'POST'
            }).then((res) => {
              const roleList = res?.data?.personParam?.bizRoleList
              const newWorkbenchAuth = roleList && roleList?.length > 0 && roleList.some(role => role.workbenchAvailable)
              const oldWorkbenchAuth = res && res.total
              if (newWorkbenchAuth || oldWorkbenchAuth) {
                this.trackOrigin([roleList[0].bizRoleName], roleList[0].workbenchRoleName)
              }
            })
          }
        } catch (error) {
          console.log(error);
        }
      }
    },
    filterMenuType (menu) {
      if (menu && menu.length > 0) {
        return menu.filter(item => {
          return item.type === 'MENU'
        })
      } else {
        return []
      }
    },
    getFirstLevelClass (item) {
      let result = {}
      if (!item.show && item.children && item.children.length > 0) {
        result.isCollapsed = true
      }
      if (item.icon) {
        result['el-icon-' + item.icon] = true
      }
      return result
    },
    hasChilren (data) {
      return data.children && data.children.length
    },
    linkFilter (link) {
      let reg = this.reg
      let local = false
      if (reg.test(link) && !/open=true/.test(link)) {
        link = link.replace(reg, '')
        local = true
      }
      return {
        local,
        link
      }
    },
    toggleMenu (item) {
      item.show = !item.show
    },
    toggle () {
      this.isToggle = !this.isToggle
      this.$emit('toggle', this.isToggle)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../style/colors.scss';
$width: 100%;
$h: 48px;

.module-side-menu {
  background: $color-dark-300;
  position: fixed;
  left: 0;
  top: $h;
  width: 220px;
  bottom: 0;
  transition: width 0.2s ease;

  :deep(.menu-filter) {
    input {
      border: #1e2240;
      background: transparent;
    }
  }
}

.module-menu {
  height: calc(100% - 25px);
  overflow: auto;

  &::-webkit-scrollbar-track-piece {
    //滚动条凹槽的颜色，还可以设置边框属性
    background-color: $color-dark-300;
  }

  &::-webkit-scrollbar {
    //滚动条的宽度
    width: 3px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    //滚动条的设置
    background-color: $color-grey-400;
    background-clip: padding-box;
    min-height: 28px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: #bbb;
  }
}

.module-menu-ul {
  color: $color-white;
  font-size: 16px;

  .title {
    line-height: 48px;
    margin: 0;
    padding: 0 28px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    white-space: nowrap;

    &:hover {
      background-color: $color-dark-400;
      color: $color-white;
    }

    .filtering {
      color: gold;
    }
  }

  .menu-text {
    flex: 1;
    padding-left: 14px;
  }

  .isCollapsed {
    color: $color-grey-300;

    .toggle-menu-icon {
      transform: rotate(180deg);
    }
  }

  .toggle-menu-icon {
    width: 16px;
    height: 16px;
    transition: transform 0.2s ease;
    transform: rotate(0deg);
  }

  li {
    line-height: 48px;
    cursor: pointer;
    user-select: none;

    > ul {
      // padding: 0 20px;
      background-color: $color-dark-200;

      a {
        padding: 0 30px 0 58px;
        font-size: 14px;
      }
    }
    > a {
      font-size: 16px;
      padding: 0 28px;
      color: #a8abc9;
      &:hover {
        color: $color-white;
        background-color: $color-dark-400;
      }
    }

    a {
      display: block;
      // color: $color-white;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      text-decoration: none;

      &.second-menu:hover {
        background-color: $color-dark-400;
        color: $color-white;
      }

      &.router-link-exact-active {
        color: $color-white;
        background-color: $color-blue-200;
      }

      &.router-link-exact-active:hover {
        color: $color-white;
        background-color: $color-blue-200;
      }
    }
  }
}
</style>
