<template>
  <el-select
    :value="value"
    filterable
    remote
    default-first-option
    clearable
    :placeholder="placeholder"
    :remote-method="remoteCustomerMethod"
    :loading="customerNoLoading"
    @change="handleSelectChange"
  >
    <el-option
      v-for="item in customerNoOptions"
      :key="item.value"
      :label="showValue ? `${item.value} ${item.label}` : item.label"
      :value="getLabel? item.label: item.value"
    />
  </el-select>
</template>
<script>
import { getCustomerList } from '@/components/SearchFields/api.js'
export default {
  name: 'RemoteCustomer',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    // value: [ String, Number ],
    value: {
      type: String,
      default: ''
    },
    getLabel: Boolean,
    showValue: Boolean
  },
  data () {
    return {
      customerNoOptions: [],
      customerNoLoading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      const foundItem = this.customerNoOptions.find(item => item.value === val)
      this.$emit('change', val, foundItem)
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerList({ customer_name: key }).then(res => {
          if (res.code === 200 && res.data.contents && res.data.contents.length > 0) {
            this.customerNoOptions = res.data.contents.map(item => ({
              ...item,
              value: item.customerNo,
              label: item.customerName
            }))
            console.log(this.customerNoOptions)
          } else {
            this.customerNoOptions = []
          }
        })
          .catch(err => {
            // eslint-disable-next-line no-console
            console.log(err)
          })
          .finally(() => {
            this.customerNoLoading = false
          })
      } else {
        this.customerNoOptions = []
      }
    }
  }
}
</script>
