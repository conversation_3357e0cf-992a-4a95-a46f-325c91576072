<template>
  <el-tooltip
    placement="top"
    effect="light"
  >
    <span>
      {{ title || '--' }}
      <i class="el-icon-info" />
    </span>
    <ul
      slot="content"
      class="tooltip-content"
    >
      <li
        v-for="item in lineList"
        :key="item.title"
      >
        <div class="title">
          {{ item.title }}
        </div>
        <div
          v-for="(n, idx) in item.children"
          :key="`${n}_${idx}`"
          class="row-no"
        >
          <el-button
            v-if="item.type"
            type="text"
            @click="click(item.type, n, item.row)"
          >
            {{ n }}
          </el-button>
          <span v-else>{{ n }}</span>
          <el-button
            type="text"
            @click="copy(n, $event)"
          >
            复制
          </el-button>
        </div>
      </li>
    </ul>
  </el-tooltip>
</template>

<script>
export default {
  name: 'MultiLineTooltip',
  props: {
    title: {
      type: String,
      required: true
    },
    lineList: {
      type: Array,
      default() {
        return []
      }
    },
    click: {
      type: Function,
      default: function() {}
    },
    copy: {
      type: Function,
      default: function() {}
    }
  }
}
</script>

<style scoped>
.el-icon-info {
  color: #999;
}

.tooltip-content {
  width: 300px;
  padding: 0;
  margin: 0;
  list-style: none;
}

.tooltip-content .title {
  font-weight: bold;
}

.tooltip-content .row-no {
  display: flex;
  justify-content: space-between;
  line-height: 40px;
  color: #409eff;
}

.tooltip-content .copy {
  cursor: pointer;
}
</style>
