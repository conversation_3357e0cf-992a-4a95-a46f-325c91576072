<template>
  <el-transfer
    v-model="modelValue"
    :data="transferData"
    :titles="titles"
    class="custom-transfer"
    :left-check-change="handleClick"
    :right-check-change="handleClick"
    @change="handleChange"
  >
    <div slot-scope="{ option }">
      <div class="transfer-item" @click="handleClick(option)">
        <el-tag :type="option.tagType">{{ option.typeName }}</el-tag>
        <span style="margin-left: 20px">{{ option.label }}</span>
      </div>
      <div :class="{ 'checked-icon': option.checked }" />
      <i v-if="option.checked" class="icon-img el-icon-check"></i>
    </div>
  </el-transfer>
</template>

<script>
export default {
  name: 'Transfer',
  props: {
    titles: { type: Array, default: () => ['', ''] },
    data: { type: Array, default: () => [] }
  },
  data () {
    return {
      transferData: this.data,
      modelValue: []
    }
  },
  methods: {
    handleClick (option) {
      option.checked = !option.checked;
    },
    handleChange (value, direction, movedKeys) {
      this.modelValue = value;
      this.transferData.forEach((item) => {
        if (movedKeys.includes(item.key)) {
          item.checked = false;
        }
      });
      this.$emit('change', this.modelValue);
    }
  }
}

</script>

<style lang="scss">
.custom-transfer {
  display: flex;
  justify-content: center;
  align-items: center;
  .el-transfer-panel {
    width: 320px;
  }
  .el-transfer-panel__header {
    pointer-events: none;
    .el-checkbox__input,
    .el-checkbox__label span {
      display: none;
    }
    .el-checkbox__label {
      padding-left: 0;
      font-size: 14px !important;
      word-spacing: 30px;
    }
  }
  .el-transfer-panel__item {
    margin-right: 0;
    padding: 10 0 10 0;
    .el-checkbox__input {
      display: none;
    }
    .el-checkbox__label {
      padding-left: 0;
    }
  }
  .el-transfer-panel__body {
    .is-checked {
      background-color: #eaf8fe;
    }
  }
  .transfer-item {
    display: flex;
    align-items: center;
  }
  .checked-icon {
    position: absolute;
    top: 0;
    right: 0;
    height: 0;
    width: 0;
    border-top: 21px solid #0b93da;
    border-left: 22px solid transparent;
  }
  .icon-img {
    position: absolute;
    font-size: 11px;
    color: #fff;
    top: 1px;
    right: 1px;
  }
}
</style>
