<template>
  <el-tooltip v-if="needCut()" class="item" :effect="effect" placement="top" >
    <span class="content" slot="content" v-html="formatContent(this.content)"></span>
    <span :class="{success:status===3,error: status=== 2}">{{cutString()}}</span>
    {{status}}
  </el-tooltip>
  <span v-else :class="{success:status===3,error: status=== 2}">{{this.content}}</span>
</template>
<script>
export default {
  name: 'cutParagraph',
  props: {
    content: String,
    status: Number,
    splitSapMsg: Bo<PERSON>an,
    effect: {
      type: String,
      detaulf: 'light'
    },
    maxLen: {
      type: Number,
      default: 30
    }
  },
  methods: {
    formatContent (content) {
      if (this.splitSapMsg) {
        content = content.split(/\s{2,10}/).join('<br />')
      } else {
        content = content.split(/\s+/).join('<br />')
      }
      return content
    },
    cutString () {
      let ret = this.content
      try {
        ret = this.content.slice(0, this.maxLen - 3) + '...'
      } catch (err) {}
      return ret
    },
    needCut () {
      if (this.content && this.content.length > this.maxLen) {
        return true
      }
      return false
    }
  }
}
</script>
<style scoped>
.success{
  color: rgb(77, 168, 77);
}
.error{
  color: red;
}
</style>
