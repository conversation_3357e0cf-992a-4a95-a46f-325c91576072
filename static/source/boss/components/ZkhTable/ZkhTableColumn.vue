<template>
  <el-table-column
    :label="column.label"
    :min-width="column.minWidth || '100px'"
    align="center"
    :prop="column.prop"
    :class-name="colClass"
    :fixed="column.fixed"
    :sortable="column.columnSortable"
    :show-overflow-tooltip="column.showOverflowTooltip"
  >
    <template v-if="column.prop" slot-scope="{ row }">
      <template v-if=" column.type === 'default' || column.type === '' || column.type === 'enum' ">
        {{ cellContent(row, column) }}
      </template>
      <zkh-link
        v-if="column.type === 'link'"
        :targetUrl="column.valueFormat(row[column.prop], row)"
      >
        {{ cellContent(row, column) }}
      </zkh-link>
      <div v-if="column.type == 'innerInput'">
        <el-input type="textarea" :value="row[column.prop]" @change="(value)=>innerInputChange(row, column.prop, value)" @input="(value)=>innerInput(row, column.prop, value)"></el-input>
      </div>
      <el-button @click="emitRow(row)" v-if="column.type === 'button'" type="text" size="small">
        {{column.text}}
      </el-button>
      <slot v-if="column.type === 'custom'" :name="column.columnSlot" :row="row"></slot>
      <safe-phone-num
        v-if="column.type === 'phone'"
        :phone="row[column.prop]"
        :value="
          JSON.stringify({
            user: user.name,
            no: row.orderNo || '',
            value: coverMobileAndLandline(row[column.prop]),
            field: column.label,
            dataType: tagName,
          })
        "
      ></safe-phone-num>
    </template>
    <ZkhTableColumn
      :baseKey="baseKey"
      v-for="subCol in shownCols"
      :key="baseKey + '_sub_' + subCol.prop"
      :column="subCol"
      @inner-input-change="innerInputChange"
    />
  </el-table-column>
</template>

<script>
import { filterVisual } from '@/components/ZkhTable/columnConfigTransformation.js'
import { getLabelByValue, coverMobileAndLandline } from '@/utils/index.js'
import '@boss/web-components';
export default {
  name: 'ZkhTableColumn',
  props: {
    column: {
      type: Object,
      default: null
    },
    baseKey: {
      default: ''
    }
  },
  data () {
    return {
      coverMobileAndLandline
    }
  },
  computed: {
    shownCols () {
      return filterVisual((this.column && this.column.children) || [])
    },
    colClass () {
      let result = {
        mergeable: this.column.mergeable
      }
      if (this.column.mergeByKey) {
        result.mergeByKey = this.column.mergeByKey
      }
      return JSON.stringify(result)
    },
    user() {
      return window.CUR_DATA.user
    },
    tagName() {
      return this.$route && this.$route.meta && this.$route.meta.tagName
    }
  },
  methods: {
    emitRow (row) {
      this.$emit('manage-button-click', row)
    },
    innerInput (row, prop, val) {
      row[prop] = val
    },
    innerInputChange (row, prop, val) {
      this.$emit('inner-input-change', row, prop, val)
    },
    cellContent (row, column) {
      let result
      if (column.labelFormat) {
        result = column.labelFormat(row[column.prop], row)
      } else if (column.type === 'enum') {
        if (column.enmus) {
          if (column.enmus instanceof Array) {
            // [{label:'A',value:'0'}]
            result = getLabelByValue(row[column.prop], column.enmus)
          } else if (typeof column.enmus === 'function') {
            result = column.enmus()
          }
        }
      }
      if (result === undefined) {
        return column.prop ? row[column.prop] : ''
      } else {
        return result
      }
    }
  }
}
</script>

<style></style>
