<template>
  <div>
    <draggable
      class="list-group"
      tag="ul"
      :list="columnsObj.children"
      ghost-class="ghost"
      :group="group"
      draggable=".zkh-draggable-item"
      @start="isDragging = true"
      @end="isDragging = false"
    >
      <transition-group type="transition" :name="'flip-list'">
        <li
          class="zkh-table-column-prop-item"
          :class="{ 'zkh-draggable-item': col.sortable !== false }"
          v-for="col in columnsObj.children"
          :key="'p_' + col.prop"
        >
          <div class="zkh-table-column-self-prop-container">
            <span class="zkh-table-column-label">
              <i v-if="col.sortable !== false" class="el-icon-sort"></i>
              <i v-else class="el-icon-lock"></i>{{ col.label }}</span
            >

            <el-switch
              :disabled="col.visibleSwitch === false"
              v-model="col.visible"
              active-color="#13ce66"
              inactive-color="#ff4949"
            >
            </el-switch>
          </div>
          <ZkhTableColumnSet :columnsObj="col" />
        </li>
      </transition-group>
    </draggable>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import * as shortid from 'shortid'
export default {
  name: 'ZkhTableColumnSet',
  props: {
    columnsObj: {
      default: function () {
        return {}
      }
    }
  },
  data () {
    return {
      editable: true,
      isDragging: false,
      delayedDragging: false
    }
  },
  components: {
    draggable
  },
  computed: {
    group () {
      return this.columnsObj.prop || shortid.generate()
    }
  }
}
</script>

<style lang="scss" scoped>
.flip-list-move {
  transition: transform 0.5s;
}
.no-move {
  transition: transform 0s;
}

.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.zkh-table-column-prop-item {
  cursor: default;
  display: flex;
  align-items: center;
  padding: 5px;
  &.zkh-draggable-item {
    cursor: s-resize;
  }
  &:nth-child(even) {
    background-color: #f0f0f0;
  }
  &:nth-child(odd) {
    background-color: #ffffff;
  }
  .zkh-table-column-self-prop-container {
    width: 210px;
    margin-right: 5px;
    display: flex;
    justify-content: space-between;
    .zkh-table-column-label{
      i{
        margin-right: 5px;
      }
    }
  }
}
</style>
