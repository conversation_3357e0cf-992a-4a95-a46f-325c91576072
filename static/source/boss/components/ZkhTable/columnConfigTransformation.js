/**  配置提取与转换 - start **/
/**  因为对象中的函数不能使用localStorage缓存，因此需要抽离出可配置的列属性 **/
/**  可配置项为：顺序，显隐，宽度，是否可排序, 是否固定显隐 **/
// 提取可配置部分
export const createdConfigByColumns = function (columns) {
  let configRoot = {}
  let queue = []
  let configQueue = []
  let trans = ({ label, prop, visible, minWidth, sortable, visibleSwitch }) => {
    return { label, prop, visible, minWidth, sortable, visibleSwitch }
  }
  // 1. 根节点转换成新节点，分别入新旧队列
  // 2. 根节点出新旧队
  // 3. 取出一个旧树节点所有子节点
  // 4. 全部子节点转换成新节点，复制到新树根节点，并分别入新旧队列
  // 5. 循环直到队列为空
  // 遍历树形结构，根据节点属性生成相同树形结构的配置项
  for (let i = 0; i < columns.length; i++) {
    let node = columns[i]
    // 旧数据入旧队列
    queue.push(node)
    let configNode = [node].map(trans)[0]
    // 转换过的新数据入新队列
    configQueue.push(configNode)
    // config.push(configNode)
  }
  // 新树开辟对应子节点空间
  configRoot.children = [...configQueue]
  // 使用队列作为遍历缓存
  // 队列缓存
  while (queue.length > 0) {
    // 旧数据缓存出队列
    let childrenNode = queue.shift()
    let configChildrenNode = configQueue.shift()
    // // 旧数据转换为新数据
    // let configChildrenNode = [childrenNode].map(trans)[0]
    // configRoot.children.push(configChildrenNode)
    // 旧数据有子节点
    if (childrenNode.children && childrenNode.children.length > 0) {
      // 旧子节点入旧队列
      queue.push(...childrenNode.children)
      // 新树创建子节点数组,并填充入数据
      configChildrenNode.children = childrenNode.children.map(trans)
      configQueue.push(...configChildrenNode.children)
    }
  }
  return configRoot.children || []
}

// 基于targetArr的顺序和每个元素的prop属性，将baseArr相同属性的值顺序输出
const sortByTargetArr = function (targetArr, baseArr) {
  let result = []
  let oldResult = []
  let targetNotMatchArr = []
  let baseArrCopy = [...baseArr]
  for (let i = 0, len = targetArr.length; i < len; i++) {
    let prop = targetArr[i].prop
    let baseIndex = baseArrCopy.findIndex(item => {
      return item.prop === prop
    })
    if (baseIndex >= 0) {
      let target = baseArrCopy.splice(baseIndex, 1)[0]
      result.push(target)
      oldResult.push(targetArr[i])
    } else {
      targetNotMatchArr.push(targetArr[i])
    }
  }
  return {
    resultArr: result,
    targetMarchedArr: oldResult,
    targetNotMatchArr: targetNotMatchArr,
    baseNotMatchArr: baseArrCopy
  }
}
// 拿出key和可配置值
const getConfigProp = function (configObj) {
  let result = {
    prop: configObj.prop
  }
  if (configObj.visible !== undefined) {
    result.visible = configObj.visible
  }
  if (configObj.minWidth !== undefined) {
    result.minWidth = configObj.minWidth
  }
  if (configObj.children !== undefined) {
    result.children = configObj.children
  }
  return result
}

// 根据配置文件，格式化结果
export const formatByConfig = function (config, columns) {
  let resultRoot = {
    prop: 'dev_root',
    children: JSON.parse(JSON.stringify(config))
  }
  let baseRoot = {
    prop: 'dev_root',
    children: columns
  }
  // 初始两个只有根节点的队列
  let baseQueue = [baseRoot]
  let resultQueue = [resultRoot]
  // 分别出队，合并
  // 获取子元素，按照配置顺序排序后分别入队
  while (resultQueue.length > 0) {
    let baseNode = baseQueue.shift()
    let resultNode = resultQueue.shift()
    // 读取缓存的显隐信息等
    Object.assign(
      resultNode,
      Object.assign({}, { ...baseNode }, { ...getConfigProp(resultNode) })
    )
    // 将待处理的数据排序后入队
    if (baseNode.children && resultNode.children) {
      let baseChildren = baseNode.children
      let resultChildren = resultNode.children

      const sortResult = sortByTargetArr(resultChildren, baseChildren)

      baseChildren = sortResult.resultArr
      baseQueue.push(...baseChildren)
      resultQueue.push(...resultChildren)
    }
  }
  return resultRoot.children || columns
}

// 配置项缓存对比
// 将默认配置树的项目，与旧配置树的已有项顺序结合
// 默认配置多出来的，追加到对应位置最后（对应增加了几列）
// 默认配置少的，旧配置树删掉枝叶（对应删了某项）
// 文案，宽度（label, minWidth）取默认值
// 输出一个数量与默认配置完全相同的，排序和属性结合了配置项的新配置树
export const checkColumnsConfig = function (baseConfig, newConfig) {
  // 根据默认配置树生成结果配置树
  let baseRoot = {
    prop: 'dev_root',
    children: JSON.parse(JSON.stringify(baseConfig))
  }
  let newRoot = {
    prop: 'dev_root',
    children: JSON.parse(JSON.stringify(newConfig))
  }
  let changed = false

  // 初始两个只有根节点的队列
  let resultQueue = [baseRoot]
  let newQueue = [newRoot]
  // 遍历结果配置树
  while (resultQueue.length > 0) {
    let resultNode = resultQueue.shift()
    let newNode = newQueue.shift()
    // 拆掉子元素，方便合并
    let resultChildren = resultNode.children
    let newNodeChildren = newNode.children
    delete resultNode.children
    delete newNode.children
    // 读取缓存的显隐信息等
    // 理论上是相同prop
    // 合并赋值
    // resultNode = Object.assign(
    //   resultNode,
    //   { ...newNode },
    //   {
    //     label: resultNode.label,
    //     minWidth: resultNode.minWidth
    //   }
    // )
    resultNode = Object.assign(resultNode, { ...getConfigProp(newNode) })
    // 默认配置树的元素为真实元素
    if (resultChildren && resultChildren.length > 0) {
      const sortResult = sortByTargetArr(newNodeChildren, resultChildren)
      // 是否有不匹配的列
      changed =
        sortResult.targetNotMatchArr.length > 0 ||
        sortResult.baseNotMatchArr.length > 0 ||
        changed
      const newSortedArr = [
        ...sortResult.resultArr,
        ...sortResult.baseNotMatchArr
      ]

      const oldSortedArr = [
        ...sortResult.targetMarchedArr,
        ...sortResult.baseNotMatchArr
      ]
      resultNode.children = newSortedArr
      resultQueue.push(...newSortedArr)
      newQueue.push(...oldSortedArr)
    }
  }

  return {
    config: baseRoot.children || baseConfig,
    changed
  }
}

// 获取缓存中的配置
export const getConfig = function (key) {
  const CUR_DATA = window.CUR_DATA
  const name = CUR_DATA.user && CUR_DATA.user.name
  let cache = window.localStorage.getItem(key + '_' + name)
  if (cache) {
    return JSON.parse(cache) || null
  } else {
    return null
  }
}
// 把配置缓存起来
export const cachedConfig = function (key, config) {
  const CUR_DATA = window.CUR_DATA
  const name = CUR_DATA.user && CUR_DATA.user.name
  window.localStorage.setItem(key + '_' + name, JSON.stringify(config))
}
// 清除缓存
export const removeConfig = function (key) {
  const CUR_DATA = window.CUR_DATA
  const name = CUR_DATA.user && CUR_DATA.user.name
  window.localStorage.removeItem(key + '_' + name)
}

export const filterVisual = function (arr) {
  if (arr && arr.length > 0) {
    const result = arr.filter(item => {
      return item.visible !== false
    })
    return result
  } else {
    return arr
  }
}
/**  配置提取与转换 - end **/
