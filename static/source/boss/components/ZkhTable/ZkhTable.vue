<template>
  <div>
    <el-table
      ref="DataTable"
      v-loading="loading"
      @selection-change="selectionChange"
      @header-dragend="headerDragend"
      :data="data"
      border
      fit
      highlight-current-row
      :height="height"
      :span-method="objectSpanMethod"
      :empty-text="emptyText"
      @sort-change="sortChange"
      class="zkh-table"
    >
      <el-table-column
        v-if="selectable"
        fixed
        type="selection"
        width="55"
        :selectable="checkSelectable"
      >
      </el-table-column>
      <slot name="leftCols"></slot>
      <ZkhTableColumn
        v-for="col in shownCols"
        :key="baseKey + '_p_' + col.prop"
        :column="col"
        :baseKey="baseKey"
        @manage-button-click="handleManageClick"
        @inner-input-change="innerInputChange"
      >
        <template v-if="col.type === 'custom'" v-slot:[col.columnSlot]="{ row }">
          <slot :name="col.columnSlot" :row="row"></slot>
        </template>
      </ZkhTableColumn>
      <slot name="rightCols"></slot>
    </el-table>
    <el-drawer
      title="我是标题"
      :visible.sync="visible2"
      :with-header="false"
      @closed="closed"
      size="auto"
    >
      <div class="zkh-table-col-container">
        <div class="zkh-table-col-arr-container">
          <ZkhTableColumnSet :columnsObj="columnsObj" />
        </div>
        <div class="zkh-table-col-btn-container">
          <el-button @click="setVisible(false)">取 消</el-button>
          <el-button
            type="warning"
            v-show="defaultColumnsConfig && defaultColumnsConfig.length > 0"
            @click="reset"
            >重 置</el-button
          >
          <el-button @click="ok" type="primary">确 定</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import ZkhTableColumn from '@/components/ZkhTable/ZkhTableColumn'
import ZkhTableColumnSet from './ZkhTableColumnSet.vue'
import { ArrayElementEqual, deepClone } from '@/utils/index.js'
import * as shortid from 'shortid'
import { filterVisual } from '@/components/ZkhTable/columnConfigTransformation.js'

export default {
  name: 'ZkhTable',
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default () {
        return []
      }
    },
    height: {
      type: [String, Number],
      default: 500
    },
    columns: {
      type: Array,
      default () {
        return []
      }
    },
    columnEditable: {
      default: false
    },
    visible: {
      default: false
    },
    columnsConfig: {
      default: function () {
        return []
      }
    },
    defaultColumnsConfig: {
      default: function () {
        return []
      }
    },
    selectable: {
      default: false
    },
    selectionChange: {
      type: Function,
      default: function () {}
    },
    headerDragend: {
      type: Function,
      default: function () {}
    },
    checkSelectable: {
      type: Function,
      default: function () {
        return true
      }
    },
    sortChange: {
      type: Function,
      default: function () {}
    },
    emptyText: {
      type: String,
      default: '暂无数据'
    }
  },
  data () {
    return {
      visible2: false,
      columnsObj: [],
      baseColumns: [],
      baseKey: shortid.generate()
    }
  },
  components: {
    ZkhTableColumn,
    ZkhTableColumnSet
  },
  computed: {
    shownCols () {
      return filterVisual(this.baseColumns || [])
    }
  },
  watch: {
    columns: {
      handler (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.baseColumns = newVal
          this.baseKey = shortid.generate()
        }
      },
      immediate: true
    },
    visible: {
      handler (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.visible2 = newVal
          this.setVisible(newVal)
          this.doLayout()
        }
      },
      immediate: true
    },
    visible2: {
      handler (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.visible = newVal
        }
      }
    },
    columnsConfig: {
      handler (newVal, oldVal) {
        if (newVal !== oldVal) {
          this.columnsObj = {
            children: this.columnsConfig
          }
        }
      },
      immediate: true
    },
    data (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.doLayout()
      }
    }
  },
  created () {
    this.initData()
  },
  methods: {
    handleManageClick (row) {
      this.$emit('manage-button-click', row)
    },
    innerInputChange (row, prop, val) {
      this.$emit('inner-input-change', row, prop, val)
    },
    doLayout () {
      this.$nextTick(() => {
        this.$refs.DataTable.doLayout()
      })
    },
    getSelections () {
      return this.$refs.DataTable.selection
    },
    clearSelection() {
      this.$refs.DataTable.clearSelection();
    },
    toggleAllSelection () {
      this.$refs.DataTable.toggleAllSelection()
    },
    toggleRowSelection (...args) {
      this.$refs.DataTable.toggleRowSelection(...args)
    },
    initData () {},
    cellRowMerge (rowIndex, columnIndex) {
      let currentCells = []
      let prevRowCells = []
      let columnIndexList = []
      if (columnIndex instanceof Array) {
        // 多个key同时相等
        columnIndexList = [...columnIndex]
      } else {
        // 单个key
        columnIndexList = [columnIndex]
      }

      currentCells = columnIndexList.map(item => {
        return this.data[rowIndex][item]
      })
      if (rowIndex > 0) {
        prevRowCells = columnIndexList.map(item => {
          return this.data[rowIndex - 1][item]
        })
      }
      // 默认被合并
      let rowspan = 0
      let colspan = 0
      // 首行或与上一行不相同的单元格，作为起始计算值，不传空
      if (rowIndex === 0 || !ArrayElementEqual(currentCells, prevRowCells)) {
        colspan = 1
        rowspan = 1
        for (let i = rowIndex; i < this.data.length - 1; i++) {
          const nextRowCells = columnIndexList.map(item => {
            return this.data[i + 1][item]
          })
          if (ArrayElementEqual(currentCells, nextRowCells)) {
            rowspan++
          } else {
            break
          }
        }
      }
      return {
        rowspan,
        colspan
      }
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 检查指定列
      let colAttr = null
      if (column.className) {
        try {
          colAttr = JSON.parse(column.className)
        } catch (error) {}
      }
      if (column.property && colAttr && colAttr['mergeable']) {
        return this.cellRowMerge(
          rowIndex,
          colAttr['mergeByKey'] || column.property
        )
      } else {
        return {
          rowspan: 1,
          colspan: 1
        }
      }
    },

    setVisible (newVal) {
      this.$emit('update:visible', newVal || false)
    },
    closed () {
      // this.setVisible()
    },
    reset () {
      this.columnsObj = {
        children: deepClone(this.defaultColumnsConfig)
      }
    },
    ok () {
      this.$emit('columnSet', this.columnsObj.children)
    }
  }
}
</script>

<style lang="scss">
.zkh-table {
  display: flex;
  flex-direction: column;
  .el-table__header-wrapper {
    flex: 0 0 auto;
  }
  .el-table__body-wrapper {
    flex: 1;
    height: auto !important;
  }
  .el-table__fixed {
    height: auto !important; // 让固定列的高自适应，且设置!important覆盖ele-ui的默认样式
    bottom: 17px; // 固定列默认设置了定位，    position: absolute;top: 0;left: 0;只需要再设置一下bottom的值，让固定列和父元素的底部出现距离即可
  }
  .el-table__empty-block {
    width: 100% !important;
  }
  .el-table__fixed::before, .el-table__fixed-right::before {
    height: 0;
  }
}
</style>
//
<style lang="scss" scoped>
.zkh-table-col-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100vh;

  .zkh-table-col-arr-container {
    overflow-y: scroll;
    height: calc(100vh - 60px);
    padding: 10px 20px;
  }
  .zkh-table-col-btn-container {
    flex: 1;
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}
</style>
