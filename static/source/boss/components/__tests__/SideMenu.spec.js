import { mount } from '@vue/test-utils'
import SideMenu from '../SideMenu.vue';
import ZkhLink from '../ZkhLink.vue';
import { menuData } from '../__mocks__/data';

const wrapper = mount(SideMenu, {
  stubs: {
    ZkhLink,
    'el-collapse-transition': true
  }
})
wrapper.setProps({ menu: menuData, reg: /https?:\/\/boss(-admin)?(-uat)?\.zkh360\.com/gi })
describe('SideMenu', () => {
  test('is exist', () => {
    const sideMenu = wrapper.findComponent(SideMenu)
    expect(sideMenu.exists()).toBe(true)
  })

  test('is match menu', async () => {
    const zLink = wrapper.findComponent(ZkhLink)
    expect(zLink.props('targetUrl')).toBe('https://crm-sandbox.xiaoshouyi.com/account.action')
  })

  test('is show submenu', async () => {
    const menu = wrapper.find('.module-menu-ul');
    await menu.find('p').trigger('click');
    expect(wrapper.vm.$data.menudata[0].show).toBe(true);
    expect(wrapper.vm.$data.menudata[1].show).toBe(false);
  })
})
