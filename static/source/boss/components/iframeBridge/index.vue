<template>
  <div class="iframe-bridge" v-loading="true">
  </div>
</template>
<script>
import Cookies from 'js-cookie'
export default {
  name: 'IframeBridge',
  created () {
    this.initListener()
  },
  methods: {
    initListener () {
      const receiveMsgFromParent = (event) => {
        try {
          console.log('receiveMsgFromParent', event.data.source)
          const { authToken, refreshToken, env, source } = event.data
          if (source === 'gbb-admin') {
            this.setCookie(env, authToken)
            this.setCookie(env.replace(/access/, 'refresh'), refreshToken)
            setTimeout(() => {
              parent.postMessage({ success: true, source: 'boss-admin' }, '*')
            }, 200)
          }
        } catch (err) { console.log(err) }
      }
      // 监听message事件
      window.addEventListener('message', receiveMsgFromParent, false)
    },
    setCookie (env, token) {
      console.log('%c boss 设置token domain: .zkh360.com', 'color:red;font-sze:2em')
      Cookies.set(env, token, { sameSite: 'None', secure: true })
      Cookies.set(env, token, { sameSite: 'None', secure: true, domain: '.zkh360.com' })
    }
  }
}
</script>

<style lang="scss" scoped>
.iframe-bridge {
  max-width: 960px;
  min-height: 90vh;
}
</style>
