<template>
    <component
      v-if="transUrl"
      :is="ct"
      :icon="icon"
      :type="type"
      :href="transUrl"
      :to="transUrl"
      :target="targetType"
      :class="linkClass"
    >
      <slot></slot>
    </component>
</template>

<script>
import { urlTransitionByHost } from '@/filters/index.js';
export default {
  name: 'zkhLink',
  props: {
    type: {
      type: String,
      default: 'primary'
    },
    commentType: {
      type: String,
      default: 'a' // 'el-link'
    },
    icon: {
      type: String,
      default: ''
    },
    targetUrl: {
      type: String,
      default: '/'
    },
    classNames: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ct: '',
      transUrl: ''
    };
  },
  created() {
    let url = urlTransitionByHost(this.targetUrl);
    // 用于sensor分析 - 我的工作台入口区分
    this.transUrl = url;

    this.ct = this.commentTypeSet();
  },
  computed: {
    targetType() {
      if (
        this.transUrl.indexOf('http://') === 0 ||
        this.transUrl.indexOf('https://') === 0 ||
        /open=true/.test(this.transUrl)
      ) {
        return '_blank';
      } else {
        return '_self';
      }
    },
    linkClass() {
      const classObj = this.classNames.split(' ').reduce((acc, curr) => {
        acc[curr] = true;
        return acc;
      }, {});
      if (this.ct === 'a' || this.ct === 'router-link') {
        return {
          'el-link': true,
          'el-link--primary': true,
          ...classObj
        };
      } else {
        return { ...classObj };
      }
    }
  },
  methods: {
    commentTypeSet() {
      if (this.commentType === 'a') {
        if (
          this.transUrl.indexOf('http://') === 0 ||
          this.transUrl.indexOf('https://') === 0
        ) {
          return 'a';
        } else {
          return 'router-link';
        }
      } else {
        return this.commentType;
      }
    }
  }

};
</script>

<style></style>
