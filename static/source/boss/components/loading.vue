<template>
  <div class="loading" :style="loadingStyle">
    <div class="dot"></div>
    <div class="dot"></div>
    <div class="dot"></div>
    <div class="dot"></div>
    <div class="dot"></div>
  </div>
</template>
<script>
export default {
  name: 'Loading',
  props: {
    absolute: Boolean
  },
  computed: {
    loadingStyle () {
      console.log(this.absolute)
      const ret = this.absolute === true ? {
        position: 'absolute',
        left: '50%',
        transform: 'translateX(-50%)'
      } : {}
      return ret
    }
  }
}
</script>
<style lang="scss" scoped>
.loading {
  margin-top: 100px;
  margin-bottom: 100px;
  $colors: #7ef9ff, #89cff0, #4682b4, #0f52ba, #000080;
  display: flex;
  justify-content: center;
  animation-delay: 1s;

  .dot {
    position: relative;
    width: 1.5em;
    height: 1.5em;
    margin: 0.8em;
    border-radius: 50%;

    &::before {
      position: absolute;
      left: 0;
      content: "";
      width: 100%;
      height: 100%;
      background: inherit;
      border-radius: inherit;
      animation: wave 2s ease-out infinite;
    }

    @for $i from 1 through 5 {
      &:nth-child(#{$i}) {
        background: nth($colors, $i);

        &::before {
          animation-delay: $i * 0.2s;
        }
      }
    }
  }
}

@keyframes wave {
  50%,
  75% {
    transform: scale(2.5);
  }

  80%,
  100% {
    opacity: 0;
  }
}
</style>
