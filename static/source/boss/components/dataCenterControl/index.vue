<template>
  <div class="data-center-control">
    <div class="marginpadding">
      说明：本页面主要提供需要查询可以在什么看板看到自己想要的数据，通过输入指标名称，模糊搜索，点击最后一列跳转到对应的看板页面
    </div>
    <vxe-grid
      border
      auto-resize
      resizable
      show-overflow
      keep-source
      ref="aggreementGrid"
      row-id
      height="580px"
      id="aggreement_grid"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'manual', mode: 'row', showStatus: true}"
      highlight-hover-row
    >

    <template v-slot:toolbar_buttons>
      <el-form :model="searchForm" ref="searchForm" label-suffix=":" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="指标名称" prop="indexName">
              <el-input v-model="searchForm.indexName"
                        filterable
                        clearable
                        style="width:100%"
                        />
           </el-form-item>
         </el-col>
         <el-col :span="2" :style="{display: 'flex', justifyContent: 'center'}">
           <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="handleSearch"
            >
             查询
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </template>

    <template v-slot:link_default="{ row }">
      <el-link type="primary" :href="row.link" target="_blank">
        {{ row.link }}
      </el-link>
    </template>

    <template #pager>
      <vxe-pager
        :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
        :border="true"
        :current-page.sync="tablePage.currentPage"
        :page-size.sync="tablePage.pageSize"
        :total="tablePage.total"
        @page-change="handlePageChange">
      </vxe-pager>
    </template>

    </vxe-grid>
  </div>
</template>

<script>
import { qbiIndex } from '@/api/dataCenterControl'

const columns = [
  {
    field: 'indexName',
    title: '指标名称',
    minWidth: 100
  },
  {
    field: 'indexDefinition',
    title: '指标定义&计算公式',
    minWidth: 150
  },
  {
    field: 'screeningConditions',
    title: '可筛选条件',
    minWidth: 100
  },
  {
    field: 'reportName',
    title: '看板名称',
    minWidth: 100
  },
  {
    field: 'reportType',
    title: '看板类型',
    minWidth: 100
  },
  {
    field: 'workspace',
    title: '所属空间',
    minWidth: 100
  },
  {
    field: 'creator',
    title: '看板创建人',
    minWidth: 100
  },
  {
    field: 'createTime',
    title: '看板创建时间',
    minWidth: 150
  },
  {
    field: 'updateTime',
    title: '看板更新时间',
    minWidth: 150
  },
  {
    field: 'link',
    title: '看板链接',
    minWidth: 200,
    slots: {
      default: 'link_default'
    }
  }
]

export default {
  name: 'dataCenterControl',
  data () {
    return {
      searchForm: {
        indexName: ''
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      columns,
      tableData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      }
    }
  },
  created () {
    this.getQbiIndex()
  },
  methods: {
    handleSearch () {
      this.tablePage.currentPage = 1
      this.getQbiIndex()
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getQbiIndex()
    },
    async getQbiIndex () {
      try {
        this.searchLoading = true
        this.tableLoading = true
        const res = await qbiIndex({
          indexName: this.searchForm.indexName,
          current: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize
        })
        if (res.code === 200) {
          this.tableData = res.data
          this.tablePage.total = res.totalCount
          if (this.tablePage.total === 0) {
            this.$message.info('没有符合条件的看板')
          }
          this.tableLoading = false
          this.searchLoading = false
        }
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.data-center-control {
  margin: 40px;
}
.marginpadding {
  margin: 40px 0;
}
</style>
