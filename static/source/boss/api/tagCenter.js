import request from '@/utility/request'

const prefixData = '/tag-center-data'
const prefixMarking = '/tag-center-marking'
const prefixQuery = '/tag-center-query'

// 创建标签对象
export function createTagObject(data) {
  return request({
    url: `${prefixData}/aggregation/TagObject/createTagObject`,
    method: 'post',
    data
  })
}

// 修改标签对象
export function updateTagObject(data) {
  return request({
    url: `${prefixData}/aggregation/TagObject/updateTagObject`,
    method: 'post',
    data
  })
}

// 启用/禁用标签对象
export function updateTagObjectStatus(data) {
  return request({
    url: `${prefixData}/aggregation/TagObject/updateTagObjectStatus`,
    method: 'post',
    data
  })
}

// 创建分类
export function createCatagory(data) {
  return request({
    url: `${prefixData}/aggregation/Catagory/createCatagory`,
    method: 'post',
    data
  })
}

// 修改分类
export function updateCatagory(data) {
  return request({
    url: `${prefixData}/aggregation/Catagory/updateCatagory`,
    method: 'post',
    data
  })
}

// 启用/禁用分类
export function updateCatagoryStatus(data) {
  return request({
    url: `${prefixData}/aggregation/Catagory/updateCatagoryStatus`,
    method: 'post',
    data
  })
}

// 启用/禁用标签
export function updateTagStatus(data) {
  return request({
    url: `${prefixData}/aggregation/Tag/updateTagStatus`,
    method: 'post',
    data
  })
}

// 创建标签元数据
export function createTag(data) {
  return request({
    url: `${prefixData}/aggregation/Tag/createTag`,
    method: 'post',
    data
  })
}

// 修改标签元数据
export function updateTag(data) {
  return request({
    url: `${prefixData}/aggregation/Tag/updateTag`,
    method: 'post',
    data
  })
}

// 查询标签对象
export function getTagObject(data) {
  return request({
    url: `${prefixData}/aggregation/TagObject/query`,
    method: 'get',
    params: data
  })
}

// 查询分类
export function getCatagory(data) {
  return request({
    url: `${prefixData}/aggregation/Catagory/query`,
    method: 'get',
    params: data
  })
}

// 查询标签元数据
export function getTag(data) {
  return request({
    url: `${prefixData}/aggregation/Tag/query`,
    method: 'get',
    params: data
  })
}

// 查询标签对象下的所有标签
export function getTagsByTagObjectId(data) {
  return request({
    url: `${prefixData}/aggregation/Tag/getTagsByTagObjectId?tagObjectId=${data}`,
    method: 'get'
  })
}

// 打标
export function createObjectTag(data) {
  return request({
    url: `${prefixMarking}/aggregation/TagMarkingRecord/createObjectTag/batch`,
    method: 'post',
    data
  })
}

// 查询业务组
export function getBusinessName() {
  return request({
    url: `${prefixData}/aggregation/Tag/getBusinessName?status=true`,
    method: 'get'
  })
}

// 删除标签
export function deleteTagPutRecord(data) {
  return request({
    url: `${prefixMarking}/aggregation/TagMarkingRecord/deleteTagPutRecord/batch`,
    method: 'post',
    data
  })
}

// 标签查询
export function getTagMetaData(data) {
  return request({
    url: `${prefixQuery}/tags/tagMetaData`,
    method: 'get',
    data
  })
}

// 打标情况查询
export function fetchTagMarkData(data) {
  return request({
    url: `${prefixQuery}/tags/tagMetaData/batch`,
    method: 'post',
    data
  })
}

// 对象取消打标
export function deleteTagMark(data) {
  return request({
    url: `${prefixMarking}/aggregation/TagMarkingRecord/deleteTagPutRecord`,
    method: 'post',
    data
  })
}
