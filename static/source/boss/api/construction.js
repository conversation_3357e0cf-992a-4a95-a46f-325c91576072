import apiRequest from '@/utility/request'
import { MessageBox } from 'element-ui'
import Qs from 'qs' // 引入方式
const apiBuild = '/api-build'
const apiMdm = '/api-mdm'
const apiPms = '/api-pms'
const request = (...args) => {
  let flag = args[0]?.flag;
  return apiRequest(...args)
    .then(res => {
      if (flag) {
        return res
      }
      if (res.code !== 200) {
        MessageBox.alert(res.message || res.msg || '请求失败！', { type: 'error' })
        return
      }
      return res.data
    })
    .catch(err => {
      MessageBox.alert(err.message || err.msg || '请求失败！', { type: 'error' })
    })
}

export function getConfigList (params) {
  return request({
    url: `${apiBuild}/achitechive/config`,
    method: 'get',
    params
  })
}

export function getOperateLog (params) {
  return request({
    url: `${apiBuild}/achitechive/operate/log`,
    method: 'get',
    params
  })
}

export const column = [
  {
    label: '项目名称',
    prop: 'projectName'
  },
  {
    label: '集团',
    prop: 'blocName'
  },
  {
    label: '客户编码',
    prop: 'customerNumber'
  },
  {
    label: '客户名称',
    prop: 'customerName'
  },
  {
    label: '项目属性',
    prop: 'projectAttribute'
  },
  {
    label: '所属二、三级公司',
    prop: 'belongCompany2o3'
  },
  {
    label: '省份名称',
    prop: 'provinceName'
  },
  {
    label: '省份编码',
    prop: 'provinceCode'
  },
  {
    label: '市名称',
    prop: 'cityName'
  },
  {
    label: '市编码',
    prop: 'cityCode'
  },
  {
    label: '区名称',
    prop: 'areaName'
  },
  {
    label: '区编码',
    prop: 'areaCode'
  },
  {
    label: '详细地址',
    prop: 'addressDetail'
  },
  {
    label: '订单收货人',
    prop: 'contactName'
  },
  {
    label: '收货人电话',
    prop: 'contactPhone'
  },
  {
    label: '供应商编码',
    prop: 'providerId'
  },
  {
    label: '是否供应商报备项目',
    prop: 'isReported'
  },
  {
    label: '供应商',
    prop: 'providerName'
  },
  {
    label: '销售',
    prop: 'sellerName'
  },
  {
    label: '销售经理',
    prop: 'sellerManagerName'
  },
  {
    label: '进项税点',
    prop: 'taxInRate'
  },
  {
    label: '扣点',
    prop: 'deductionRage'
  },
  {
    label: '下单人',
    prop: 'deductionRage'
  },
  {
    label: '项目备注',
    prop: 'projectNotes'
  },
  {
    label: '项目状态',
    prop: 'status'
  }
]

export function createConfig (data) {
  return apiRequest({
    url: `${apiBuild}/achitechive/config`,
    method: 'post',
    data
  })
}
export function modifyConfig (data, id) {
  return apiRequest({
    url: `${apiBuild}/achitechive/config/${id}`,
    method: 'put',
    data
  })
}
export function handlePermission(data, id) {
  return apiRequest({
    url: `${apiBuild}/achitechive/config/wait/approval/${id}`,
    method: 'put',
    data
  })
}
export function deleteRow(status, id) {
  return apiRequest({
    url: `${apiBuild}/achitechive/delete/config/${id}`,
    method: 'delete',
    params: { status }
  })
}

export function modifyStatus (data, id) {
  return apiRequest({
    url: `${apiBuild}/achitechive/config/updateStatus/${id}`,
    method: 'put',
    data
  })
    .then(res => {
      if (res.code === 200 && res.success) {
        return true
      }
      MessageBox.alert(res.msg || res.message || '修改失败！', { type: 'error' })
    })
    .catch(err => {
      MessageBox.alert(err.msg || err.message || '修改失败！', { type: 'error' })
    })
}
let constructionRole = null
export function getRole () {
  if (constructionRole !== null) return new Promise((resolve, reject) => resolve(constructionRole))
  return apiRequest({
    url: `${apiBuild}/achitechive/getRole`,
    method: 'get'
  })
    .then(res => {
      if (res.code === 200) {
        constructionRole = res.data
      }
      return constructionRole
    })
}

export function getSalePendingList (params) {
  return request({
    url: `${apiBuild}/achitechive/items`,
    method: 'get',
    params
  })
}

export function getSaleRecordList (params) {
  return request({
    url: `${apiBuild}/achitechive/items/submitted`,
    method: 'get',
    params
  })
}

export function getBlocOptions () {
  return request({
    url: `${apiBuild}/achitechive/config/queryParams`,
    method: 'get'
  })
}

export function submitItems (data) {
  return request({
    url: `${apiBuild}/achitechive/items/submit`,
    method: 'post',
    data
  })
}

export function getDetail (ids) {
  return request({
    url: `${apiBuild}/achitechive/items?ids=${ids}`,
    method: 'get'
  })
}

export function getEditInfo (data) {
  return request({
    url: `${apiBuild}/achitechive/items/prepSubmit`,
    method: 'post',
    data
  })
}
export function revokeApi (id) {
  return request({
    url: `${apiBuild}/achitechive/items/submitRevoke/${id}`,
    method: 'post'
  })
}

export function getAuditPending (params) {
  return request({
    url: `${apiBuild}/achitechive/items/pending`,
    method: 'get',
    params
  })
}

export function getAuditRecord (params) {
  return request({
    url: `${apiBuild}/achitechive/items/history`,
    method: 'get',
    params
  })
}
export function batchAudit (data) {
  return request({
    url: `${apiBuild}/achitechive/items/verify`,
    method: 'put',
    data
  })
}
export function exportData (params) {
  return request({
    url: `${apiBuild}/achitechive/items/export`,
    method: 'get',
    params
  })
}
export function getProviderDetail (providerNo) {
  return apiRequest({
    url: `${apiMdm}/supplier/getByProviderNo/${providerNo}/0`,
    method: 'get'
  })
}
export function syncOrder (data) {
  return request({
    url: `${apiBuild}/achitechive/syncOrder`,
    method: 'put',
    data
  })
}
export function getPrice (params) {
  return request({
    url: `${apiBuild}/achitechive/getPrice`,
    method: 'get',
    params
  })
}
export function getBlocInfo (params) {
  return request({
    url: `${apiBuild}/achitechive/getBlocInfo`,
    method: 'get',
    params
  })
}
/*
 *  未生成采购订单列表
 * @param {*} data { any }
 * @return { Promise }
 */

export function getUngeneratedOrder(params) {
  return request({
    url: `${apiBuild}/achitechive/no/purchase/items`,
    method: 'get',
    params
  })
}
/*
 *  刷新未生成采购订单列表
 * @param {*} data { any }
 * @return { Promise }
 */

export function getSyncUngeneratedOrder(params) {
  return request({
    url: `${apiBuild}/achitechive/sync/no/purchase/items`,
    method: 'get',
    params
  })
}
/*
 *  未生成采购订单列表重推或者驳回
 * @param {*} data { any }
 * @return { Promise }
 */

export function ungeneratedOrderRepushOrReject(query) {
  const queryStr = Qs.stringify(query);
  return request({
    url: `${apiBuild}/achitechive/no/purchase/items/verify?${queryStr}`,
    method: 'put'
  })
}
/*
 *  供应商详情查询(通过name查询)
 * @param {*} data { any }
 * @return { Promise }
 */

export function getSupplierByProviderName(providerName) {
  return request({
    url: `${apiPms}/proxy/supplier/getByProviderName/${providerName}`,
    method: 'get',
    flag: true
  })
}
