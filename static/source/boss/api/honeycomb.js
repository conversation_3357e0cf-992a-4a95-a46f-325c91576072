import rawRequest from '@/utility/request'
import { MessageBox } from 'element-ui'

const request = function () {
  return rawRequest(...arguments)
    .then(res => {
      if (res.code === 200 && res.isSuccess) {
        return res.data
      } else {
        MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
      }
    })
    .catch(err => {
      MessageBox.alert(err.msg || err.message || '请求失败！', { type: 'error' })
    })
}
const apiHoneycomb = '/honeycomb'

export function getDataList (params) {
  return request({
    url: `${apiHoneycomb}/openOci/festo/getEffectDatas`,
    method: 'get',
    params
  })
}
export function getPunchImportDataList (params) {
  return request({
    url: `${apiHoneycomb}/openOci/festo/getEffectDatas`,
    method: 'get',
    params
  })
}
export function finishData (params) {
  return request({
    url: `${apiHoneycomb}/openOci/festo/finishDatas`,
    method: 'get',
    params
  })
}
export function getFestoCustomer (params) {
  return request({
    url: `${apiHoneycomb}/openOci/festo/searchCustomerOci`,
    method: 'get',
    params
  })
}

export function getPunchList (data) {
  return Promise.resolve([data])
}

export function getRpaDetail (params) {
  return request({
    url: `${apiHoneycomb}/rpa/executeState`,
    method: 'get',
    params
  })
}

export function pushRpaData (params) {
  return request({
    url: `${apiHoneycomb}/rpa/run`,
    method: 'post',
    params
  })
}
