import rawRequest, { downloadFile } from '@/utility/request'
import { MessageBox } from 'element-ui'

const request = function () {
  return rawRequest(...arguments)
    .then(res => {
      if (res.code === 200 && res.success) {
        return res.result
      } else {
        MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
      }
    })
    .catch(err => {
      MessageBox.alert(err.msg || err.message || '请求失败！', { type: 'error' })
    })
}

const priceApproval = '/price-approval'

export function getOrderDetail(params) {
  const { approvalNo } = params
  return request({
    url: priceApproval + `/approval/detail/${approvalNo}`
  })
}

export function getProductDetail(params) {
  const { approvalNo } = params
  params = { ...params }
  delete params.approvalNo
  return request({
    method: 'POST',
    url: priceApproval + `/approval/detail/${approvalNo}/product`,
    params
  })
}
export function submitApproval(data) {
  const { approvalNo, taskId } = data
  data = { ...data }
  delete data.approvalNo
  delete data.taskId
  return request({
    method: 'POST',
    url: priceApproval + `/approval/detail/${approvalNo}/${taskId}`,
    data
  })
}
export function getAuditList(type, params) {
  // type -- 类型: 1我发起的审批 2待我审批
  return request({
    url: priceApproval + `/approval/${type}/list`,
    params
  })
}
// 导出表单
export function exportForm(approvalNo) {
  return downloadFile(`${priceApproval}/approval/export/${approvalNo}/product`, null, {
    method: 'get',
    ignoreEmpty: true
  })
}
