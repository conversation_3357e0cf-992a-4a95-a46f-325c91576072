import request, { downloadFile } from '@/utility/request'

const prefix = '/data-center-front'

let adjustTypeOptions
export function getAdjustTypeOptions() {
  return new Promise(resolve => {
    if (adjustTypeOptions) {
      resolve(adjustTypeOptions)
    } else {
      request({
        url: `${prefix}/epms/cfg/adjust/type/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          adjustTypeOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(adjustTypeOptions)
      })
    }
  })
}

export function deleteConfig(id) {
  return request({
    url: `${prefix}/epms/cfg/delete/${id}`,
    method: 'put'
  })
}

export function getConfigDetail(id) {
  return request({
    url: `${prefix}/epms/cfg/item/${id}`,
    method: 'get'
  })
}
// 策略行-详情
export function getPolicyDetail(policyId) {
  return request({
    url: `${prefix}/epms/cfg/detail/${policyId}`,
    method: 'get'
  })
}

export function getConfigList(data) {
  return request({
    url: `${prefix}/epms/cfg/list`,
    method: 'post',
    data
  })
}

export function getConfigReference(data) {
  return request({
    url: `${prefix}/epms/cfg/reference`,
    method: 'post',
    data
  })
}

// 提交策略
export function saveConfig(data) {
  return request({
    url: `${prefix}/epms/cfg/save`,
    method: 'post',
    data
  })
}

// 保存策略
export function addConfig(data) {
  return request({
    url: `${prefix}/epms/cfg/add`,
    method: 'post',
    data
  })
}

export function getMaterialList(data) {
  return request({
    url: `${prefix}/epms/run/material/list`,
    method: 'post',
    data
  })
}
// 日常调价-模板下载
export function downloadDailyTemplate() {
  const url = `${prefix}/epms/run/daily/template/download`
  downloadFile(url)
}
// 日常调价详情
export function dailyImportDetail(params, runId) {
  return request({
    url: `${prefix}/epms/run/daily/import/${runId}`,
    method: 'post',
    data: params
  })
}
// 策略启动
export function runStartUp(data) {
  return request({
    url: `${prefix}/epms/run/startup`,
    method: 'post',
    data
  })
}
// 运行价格策略-编辑
export function saveRunPolciy(runId, data) {
  return request({
    url: `${prefix}/epms/run/startup/${runId}`,
    method: 'put',
    data: data
  })
}
// 获取策略运行记录列表
export function getRecordlList(data) {
  return request({
    url: `${prefix}/epms/run/record/list`,
    method: 'post',
    data
  })
}
// 获取运行状态枚举值
let runStatusOptions
export function getRunStatusOptions() {
  return new Promise(resolve => {
    if (runStatusOptions) {
      resolve(runStatusOptions)
    } else {
      request({
        url: `${prefix}/epms/run/status/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          runStatusOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(runStatusOptions)
      })
    }
  })
}
// 策略运行记录-重跑
export function reRun(runId) {
  return request({
    url: `${prefix}/epms/run/rerun/${runId}`,
    method: 'put'
  })
}
// 策略运行记录-删除
export function deleteRecord(runId) {
  return request({
    url: `${prefix}/epms/run/record/delete/${runId}`,
    method: 'delete'
  })
}
// 策略运行详情列表
export function getRunDetaillList(data) {
  return request({
    url: `${prefix}/epms/result/list`,
    method: 'post',
    data
  })
}
// 策略运行保存
export function saveRunDetaillData(data, id) {
  return request({
    url: `${prefix}/epms/result/update/${id}`,
    method: 'put',
    data
  })
}
// 策略运行批量保存
export function batchSaveRunDetaillData(data, id) {
  return request({
    url: `${prefix}/epms/result/update/batch`,
    method: 'put',
    data
  })
}
// 结果诊断下拉列表
export function getDiagnoses() {
  return request({
    url: `${prefix}/epms/result/diagnose/dropdown`,
    method: 'get'
  })
}
// 调价方向下拉列表
export function getDirection() {
  return request({
    url: `${prefix}/epms/result/direction/dropdown`,
    method: 'get'
  })
}
// 调后行动等级下拉列表
export function getActionLevels() {
  return request({
    url: `${prefix}/epms/result/action/level/dropdown`,
    method: 'get'
  })
}
// 调价预报-调价原因下拉
export function getAdjustReasons() {
  return request({
    url: `${prefix}/price/forecast/reason/dropdown`,
    method: 'get'
  })
}
// 推送状态下拉列表
export function getPushStatus() {
  return request({
    url: `${prefix}/epms/result/price/push/status/dropdown`,
    method: 'get'
  })
}
// 策略运行详情--SKU明细-导出POST
export function exportSkuDetail(data) {
  downloadFile(`${prefix}/epms/result/detail/export`, data, '', 'post')
}
// 策略运行详情--导出
export function exportResult(data) {
  downloadFile(`${prefix}/epms/result/export`, data, '', 'post')
}
// 策略运行详情--导出明细
export function exportResultDetail(data) {
  downloadFile(`${prefix}/epms/result/detail/export`, data, '', 'post')
}
// 策略运行详情-推送前诊断
export function pushDiagnose(data) {
  return request({
    url: `${prefix}/epms/result/push/diagnose/${data.runId}`,
    method: 'put',
    data
  })
}
// 策略运行详情--推送主数据
export function pushData(data) {
  return request({
    url: `${prefix}/epms/result/push/${data.policyResultQuery.runId}`,
    method: 'put',
    data
  })
}
// 策略运行详情--刷新数据
export function refreshData(runId) {
  return request({
    url: `${prefix}/epms/result/refresh/${runId}`,
    method: 'put'
  })
}
// 获取调价单列表
export function getSheetlList(data) {
  return request({
    url: `${prefix}/epms/sheet/list`,
    method: 'post',
    data
  })
}
// 价格回滚
export function sheetRollback(sheetNo) {
  return request({
    url: `${prefix}/epms/sheet/rollback/${sheetNo}`,
    method: 'put'
  })
}
// 查看推送设置
export function sheetPushInfo(pushId) {
  return request({
    url: `${prefix}/epms/sheet/push/${pushId}`,
    method: 'get'
  })
}
// 获取策略运行名称-下拉
export function getRunStrategy(data) {
  return request({
    url: `${prefix}/epms/run/dropdown`,
    method: 'post',
    data
  })
}
// 运行价格策略-启动详情
export function getStartupDetail(runId) {
  return request({
    url: `${prefix}/epms/run/startup/${runId}`,
    method: 'get'
  })
}
// 健康度模型参数-查询
export function getHealthAttribute() {
  return request({
    url: `${prefix}/epms/cfg/health/param/find/all`,
    method: 'get'
  })
}
// 健康度模型参数-保存
export function saveHealthAttribute(data) {
  return request({
    url: `${prefix}/epms/cfg/health/param/save`,
    method: 'post',
    data
  })
}
// 调价导向-列表
export function getHealthAttributeGuide() {
  return request({
    url: `${prefix}/epms/cfg/health/param/guide/find/all`,
    method: 'get'
  })
}
// 调价导向-保存
export function saveHealthAttributeGuide(data) {
  return request({
    url: `${prefix}/epms/cfg/health/param/guide/save`,
    method: 'post',
    data
  })
}

// 调价导向-行动等级-下拉
let actionLevelOptions
export function getActionLevelOptions() {
  return new Promise(resolve => {
    if (actionLevelOptions) {
      resolve(actionLevelOptions)
    } else {
      request({
        url: `${prefix}/epms/cfg/health/param/guide/actionLevel/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          actionLevelOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(actionLevelOptions)
      })
    }
  })
}

// 调价导向-调价潜力下拉
let potentialOptions
export function getPotentialOptions() {
  return new Promise(resolve => {
    if (potentialOptions) {
      resolve(potentialOptions)
    } else {
      request({
        url: `${prefix}/epms/cfg/health/param/guide/potential/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          potentialOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(potentialOptions)
      })
    }
  })
}

// 调价导向-调价方向下拉
let directionOptions
export function getDirectionOptions() {
  return new Promise(resolve => {
    if (directionOptions) {
      resolve(directionOptions)
    } else {
      request({
        url: `${prefix}/epms/cfg/health/param/guide/direction/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          directionOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(directionOptions)
      })
    }
  })
}
// 首要友商- 下拉
let friendBusinessOption
export function getFriendBusiness() {
  return new Promise(resolve => {
    if (friendBusinessOption) {
      resolve(friendBusinessOption)
    } else {
      request({
        url: `${prefix}/epms/cfg/friend/business/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          friendBusinessOption = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: key
            }
          })
        }
        resolve(friendBusinessOption)
      })
    }
  })
}
// 调前/调后健康度下拉
export function getHealthOptions() {
  return request({
    url: `${prefix}/epms/result/price/health/dropdown`,
    method: 'get'
  })
}
// 产品定位下拉框查询
export function getProductPosition() {
  return request({
    url: `${prefix}/similarity/product/position/dropdown`,
    method: 'get'
  })
}
// 备货类型下拉框查询
export function getStockType() {
  return request({
    url: `${prefix}/epms/result/stock/type/dropdown`,
    method: 'get'
  })
}
// 是否推送下拉框查询
export function getPushFlag() {
  return request({
    url: `${prefix}/epms/result/push/flag/dropdown`,
    method: 'get'
  })
}
// 经销商价格诊断下拉框查询
export function getagentDiagnose() {
  return request({
    url: `${prefix}/epms/result/agent/diagnose/dropdown`,
    method: 'get'
  })
}
// 调前诊断下拉
export function getAdjustmentDiagnose() {
  return request({
    url: `${prefix}/epms/result/pre/adjustment/diagnose/dropdown`,
    method: 'get'
  })
}
// 是否确认下拉
export function getUpdateMarker() {
  return request({
    url: `${prefix}/epms/result/price/update/marker/dropdown`,
    method: 'get'
  })
}
// 条件-包含/排除下拉
export function getRunCondition() {
  return request({
    url: `${prefix}/epms/run/condition/dropdown`,
    method: 'get'
  })
}
