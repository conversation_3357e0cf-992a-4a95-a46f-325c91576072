import rawRequest from '@/utility/request'
const oms = '/api-opc'
/**
 *未下单采购订单
 * @param {*} data {any }
 */
export function getUnOrderPurchaseOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/noPurchaseOrder`,
    method: 'get',
    params
  })
}
/**
 *供应商未确认采购订单
 * @param {*} data {any }
 */
export function getSupplierUnconfirmOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/supplierUnConfirm/orderList`,
    method: 'get',
    params
  })
}
/**
 *交期无法满足采购订单
 * @param {*} data {any }
 */
export function getUnmetDeliveryOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/deliveryDateUnSatisfied/orderList`,
    method: 'get',
    params
  })
}
/**
 *待创建交货单采购订单
 * @param {*} data {any }
 */
export function getReadyForDnOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/readyForDn/orderList`,
    method: 'get',
    params
  })
}
/**
 *直发可确认收货采购订单
 * @param {*} data {any }
 */
export function getDirectDeliverySupplierAckDnOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/directDeliverySupplierAckDn/orderList`,
    method: 'get',
    params
  })
}
/**
 *待检查签单情况采购订单
 * @param {*} data {any }
 */
export function getunCheckSignBackOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/unCheckSignBack/orderList`,
    method: 'get',
    params
  })
}
/**
 *订单已逾期采购订单 todo
 * @param {*} data {any }
 */
export function getUnOverDueOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/overDue/orderList`,
    method: 'get',
    params
  })
}
/**
 *订单临近三天逾期且供应商未发货采购订单
 * @param {*} data {any }
 */
export function getAlmostOverdueUndeliveryOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/almostOverdueUndelivery/orderList`,
    method: 'get',
    params
  })
}

/**
 *订单临近三天逾期且供应商已发货采购订单
 * @param {*} data {any }
 */
export function getAlmostOverdueDeliveryOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/almostOverdueDelivery/orderList`,
    method: 'get',
    params
  })
}

/**
 *订单临近七天可能逾期采购订单
 * @param {*} data {any }
 */
export function getAlmostOverdueInDaysOrder (params) {
  return rawRequest({
    url: `${oms}/v1/boss/customerService/almostOverdueInDays/orderList`,
    method: 'get',
    params
  })
}
