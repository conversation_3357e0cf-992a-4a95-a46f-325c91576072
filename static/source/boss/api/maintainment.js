import request from '@/utility/request'

const apiBossProduct = '/api-boss-product'

/** 客户商品快捷查询 - start ***/
// 品牌下拉列表
export const getLists = (data) => {
  return request({
    url: `${apiBossProduct}/ocr/customer/list`,
    method: 'get',
    params: data
  })
}

export const addCustomer = data => {
  return request({
    url: `${apiBossProduct}/ocr/customer/add`,
    method: 'post',
    data
  })
}

export const updateCustomer = (type, data) => {
  return request({
    url: `${apiBossProduct}/ocr/customer/${type}`,
    method: 'post',
    params: data
  })
}

export const getRelationLists = data => {
  return request({
    url: `${apiBossProduct}/ocr/relation/list`,
    method: 'get',
    params: data
  })
}

export const addRelation = data => {
  return request({
    url: `${apiBossProduct}/ocr/relation/add`,
    method: 'post',
    params: data
  })
}

export const updateRelation = data => {
  return request({
    url: `${apiBossProduct}/ocr/relation/edit`,
    method: 'post',
    params: data
  })
}

export const importRelation = (query, data) => {
  return request({
    url: `${apiBossProduct}/ocr/relation/import`,
    method: 'post',
    params: query,
    data
  })
}

export const exportRelation = (query) => {
  return request({
    url: `${apiBossProduct}/ocr/relation/export`,
    method: 'get',
    params: query
  })
}

export const updateRelationStatus = data => {
  return request({
    url: `${apiBossProduct}/ocr/relation/status`,
    method: 'post',
    params: data
  })
}

export const getTemplateList = data => {
  return request({
    url: `${apiBossProduct}/ocr/model/list`,
    method: 'get',
    params: data
  })
}

export const getTemplateDetail = data => {
  return request({
    url: `${apiBossProduct}/ocr/model/detail`,
    method: 'get',
    params: data
  })
}

export const createTemplate = data => {
  return request({
    url: `${apiBossProduct}/ocr/model/add`,
    method: 'post',
    data
  })
}

export const updateTemplate = data => {
  return request({
    url: `${apiBossProduct}/ocr/model/edit`,
    method: 'post',
    data
  })
}

export const updateTemplateStatus = data => {
  return request({
    url: `${apiBossProduct}/ocr/model/status`,
    method: 'post',
    params: data
  })
}
