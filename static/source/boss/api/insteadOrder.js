import request from '@/utility/request'

const opcFrontGoods = '/oms-opc-front/goods'

/** 客户商品快捷查询 - start ***/
// 品牌下拉列表
export function goodsBrandListSearch (data) {
  return request({
    url: `${opcFrontGoods}/get/brand/list`,
    method: 'get',
    params: data
  })
}
// 客户名称下拉列表
export function goodsCustomerListSearch (data) {
  return request({
    url: `${opcFrontGoods}/get/customer/list`,
    method: 'get',
    params: data
  })
}
// 物料组下拉列表
export function goodsGroupListSearch (data) {
  return request({
    url: `${opcFrontGoods}/get/group/list`,
    method: 'get',
    params: data
  })
}
// 根据sku编号搜索销售订单
export function goodsOrderListSearchBySku (data) {
  return request({
    url: `${opcFrontGoods}/get/order/list`,
    method: 'get',
    params: data
  })
}
// 快捷分页查找sku
export function goodsProductListSearch (data) {
  return request({
    url: `${opcFrontGoods}/get/product/list`,
    method: 'get',
    params: data
  })
}
// 根据sku编号搜索报价单列表
export function goodsQuoteListSearchBySku (data) {
  return request({
    url: `${opcFrontGoods}/get/quote/list`,
    method: 'get',
    params: data
  })
}

/** 客户商品快捷查询 - end ***/
