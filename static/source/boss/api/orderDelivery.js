import request from '@/utility/request'
import qs from 'qs';

const prefix = '/api-opc'
const dnc = '/api-dnc'
const khdms = '/api-kunhe-dms'
const opcCsc = '/api-opc-csc'
const omsSoQuery = '/oms-so-query'
const apiLogistics = '/api-logistics';

export function listDN (data) {
  return request({
    url: `${prefix}/deliveryNote/query`,
    method: 'post',
    data
  })
}

export function searchClients (query) {
  return request({
    url: `${prefix}/v1/so/template/customer/like`,
    params: { value: query }
  })
}

export function getDN (id) {
  return request({
    url: `${prefix}/deliveryNote/getDetail`,
    method: 'post',
    data: { id }
  })
}

export const updateDelivery = data => {
  return request({
    url: `${prefix}/deliveryNote/customer/update`,
    method: 'post',
    data
  })
}
export const updateDeliveryNote = data => {
  return request({
    url: `${dnc}/deliveryNote/update`,
    method: 'post',
    data
  })
}

export function getDNBackDetail (data) {
  return request({
    url: `${prefix}/deliverCallback/query`,
    method: 'post',
    data
  })
}

export function getContactDetail (contactId) {
  return request({
    url: `${prefix}/v1/contact/${contactId}`,
    method: 'get'
  })
}

export function getDNNoteDetail (data) {
  return request({
    url: `${prefix}/deliveryNoteDetail/query`,
    method: 'post',
    data
  })
}

export function updateDN (data) {
  return request({
    url: `${prefix}/deliveryNote/update`,
    method: 'post',
    data
  })
}

export function initDN (data) {
  return request({
    url: `${prefix}/deliveryNote/initDN`,
    method: 'post',
    data
  })
}

export function queryDNByGroup (data) {
  return request({
    url: `${prefix}/sapOrderDetailItem/query`,
    method: 'post',
    data
  })
}

export function queryDNByGroupV2 (data) {
  return request({
    url: `${prefix}/sapOrderDetailItem/query/v2`,
    method: 'post',
    data
  })
}

export function deliverySwitch () {
  return request({
    url: `${prefix}/deliveryNote/create/manual/switch`,
    method: 'get'
  })
}

export function queryShippingByGroup (data) {
  return request({
    url: `${prefix}/sapOrderDetailItem/queryShipping`,
    method: 'post',
    data
  })
}

export function queryShippingByGroupV2 (data) {
  return request({
    url: `${prefix}/sapOrderDetailItem/queryShipping/v2`,
    method: 'post',
    data
  })
}

export function createDN (data) {
  return request({
    url: `${prefix}/deliveryNote/create`,
    method: 'post',
    data
  })
}

// dn创建新接口
export function dncCreateDN (data) {
  return request({
    url: `${dnc}/deliveryNote/create`,
    method: 'post',
    data
  })
}

export function createShippingDN (data) {
  return request({
    url: `${prefix}/deliveryNote/shipping-create`,
    method: 'post',
    data
  })
}
export function createShippingDNV2 (data) {
  return request({
    url: `${prefix}/deliveryNote/create`,
    method: 'post',
    data
  })
}

export function submitDN (data) {
  return request({
    url: `${prefix}/deliveryNote/submit`,
    method: 'post',
    data
  })
}

export function queryDnBySoPlan (data) {
  return request({
    url: `${prefix}/deliveryNoteDetail/buildBySoPlan`,
    method: 'post',
    data
  })
}

export function cancelDN (data) {
  return request({
    url: `${prefix}/deliveryNote/cancel`,
    method: 'post',
    data
  })
}

export function deliverDN (data) {
  return request({
    url: `${prefix}/deliveryNote/deliver`,
    method: 'post',
    data
  })
}

export function initNonKhDnBatch (data) {
  return request({
    url: `${prefix}/dnNonKh/initNonKhDnBatch`,
    method: 'post',
    data
  })
}

export function postOutBoundDn (data) {
  return request({
    url: `${prefix}/dnNonKh/postOutBoundDn`,
    method: 'post',
    data
  })
}

export function postOutBoundDnWithServiceMaterial (data) {
  return request({
    url: `${prefix}/dnNonKh/postOutBoundDn/service-material`,
    method: 'post',
    data
  })
}

export function queryAvailableWithSku (data) {
  return request({
    url: `${prefix}/dnNonKh/queryAvailableWithSku`,
    method: 'post',
    data
  })
}

export function isCurrentUserLeader (dnNo) {
  return request({
    url: `${prefix}/customerService/isCurrentUserLeader?dnNo=${dnNo}`,
    method: 'post',
    data: {
      dnNo
    }
  })
}

export function unFreezeShipment (dnNo) {
  return request({
    url: `${prefix}/deliveryNote/unfreeze`,
    method: 'post',
    data: {
      dnNo,
      shipmentFreeze: 'C'
    }
  })
}

export function unFreezeInvoice (dnNo) {
  return request({
    url: `${prefix}/deliveryNote/unfreeze`,
    method: 'post',
    data: {
      dnNo,
      invoiceFreeze: 'C'
    }
  })
}

export function unfreezeDelivery (dnNo) {
  return request({
    url: `${prefix}/deliveryNote/unfreezeDelivery`,
    method: 'post',
    data: {
      dnNo
    }
  })
}

export function checkDN (data) {
  return request({
    url: `${prefix}/deliveryNote/create/pre-check`,
    method: 'post',
    data
  })
}

export function checkDNV2 (data) {
  return request({
    url: `${prefix}/deliveryNote/create/pre-check/v2`,
    method: 'post',
    data
  })
}

export function checkShippingV2 (data) {
  return request({
    url: `${prefix}/deliveryNote/create/shipping-pre-check/v2`,
    method: 'post',
    data
  })
}

export function checkShipping (data) {
  return request({
    url: `${prefix}/deliveryNote/create/shipping-pre-check`,
    method: 'post',
    data
  })
}
export function shippingCondition (data) {
  return request({
    url: `${prefix}/deliveryNote/shippingCondition/update`,
    method: 'post',
    data
  })
}

export function compensateToSap (no) {
  return request({
    url: `${prefix}/dnCompensation/compensateToSap`,
    method: 'post',
    data: [no]
  })
}

export function getOrderStatus (orderCode) {
  return request({
    url: `${khdms}/kunhe-dms/order/getOrderStatus`,
    method: 'get',
    params: {
      orderCode
    }
  })
}
export function updateWorkflowNDStatus (data) {
  return request({
    url: `${opcCsc}/common/update/status`,
    method: 'post',
    data
  })
}
export function signReceipt (data) {
  return request({
    url: `${prefix}/deliveryStatus/signReceipt`,
    method: 'post',
    data
  })
}

// 检查用户是否有DN查看权限
export function dnDetailAuthCheck (data) {
  return request({
    url: omsSoQuery + '/dn/view/auth/check',
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify(data)
  })
}

// 运输信息查询
export function getTransportInfo (custOrderNo) {
  return request({
    url: `${apiLogistics}/transportInfo/get`,
    method: 'get',
    params: {
      custOrderNo
    }
  })
}
