import request from '@/utility/request'

const ccGateway = '/api-cc-gateway'
const omsConfig = '/oms-config';
const prefix = '/api-ab';
const standard = '/api-cc-standard'

// 获取类目list
export function getCatalogTree(data) {
  return request({
    url: `${ccGateway}/api/scc/v1/catalog/tree`,
    method: 'get',
    params: data
  })
}

// 获取工厂list
export function getAllFactory(data) {
  return request({
    url: `${omsConfig}/factory-base/query/all`,
    method: 'post',
    data
  })
}
// 获取物料组
export function getMaterialGroupListApi(data) {
  return request({
    url: `${standard}/optionsets/entityTypes`,
    method: 'post',
    data
  });
}
// 获取品牌
export function getBrandListApi(params) {
  return request({
    url: `${ccGateway}/api/scc/v1/brand/page`,
    method: 'get',
    params
  });
}

// 获取全部库存地点
export function getAllPosition(params) {
  return request({
    url: `${omsConfig}/warehouse-base/position/getAll`,
    method: 'get',
    params
  })
}
// 根据工厂查库存地点
export function getPositionByFactory(data) {
  return request({
    url: `${omsConfig}/warehouse-base/position/queryByFactory`,
    method: 'post',
    data
  })
}

// 获取销售预测list
export function getSalesForecastList(data) {
  return request({
    url: `${prefix}/sales-forecast/queryPage`,
    method: 'post',
    data
  })
}

// 销售预测-修改数量
export function updateSalesForecastNum(data) {
  return request({
    url: `${prefix}/sales-forecast/update`,
    method: 'post',
    data
  })
}

// 销售预测-审核
export function approveSalesForecast(data, params) {
  return request({
    url: `${prefix}/sales-forecast/approve/list`,
    method: 'post',
    data,
    params
  })
}

// 销售预测-创建
export function createSalesForecast(data) {
  return request({
    url: `${prefix}/sales-forecast/create`,
    method: 'post',
    data
  })
}

// 销售预测-查询SKU信息
export function querySkuInfo(data) {
  return request({
    url: `${prefix}/sales-forecast/querySkuInfo`,
    method: 'post',
    data
  })
}
// 销售预测-批量导出
export function salesExport(data) {
  return request({
    url: `${prefix}/sales-forecast/export`,
    method: 'post',
    data
  })
}
// 销售预测-编辑详情
export function salesInfo(data) {
  return request({
    url: `${prefix}/sales-forecast/query/sale-forecast-info`,
    method: 'post',
    params: data,
    data
  })
}
// 销售预测-编辑-备货仓list
export function salesPositions(data) {
  return request({
    url: `${prefix}/sales-forecast/all-positions`,
    method: 'post',
    params: data,
    data
  })
}
// 销售预测-编辑-供应商list
export function salesSupplier(data) {
  return request({
    url: `${prefix}/sales-forecast/purchase-price`,
    method: 'post',
    data
  })
}
// 销售预测-编辑提交
export function updateSalesForecast(data) {
  return request({
    url: `${prefix}/sales-forecast/v2/update`,
    method: 'post',
    data
  })
}
// 销售预测-转采购计划-列表
export function salesToPurchaseList(data) {
  return request({
    url: `${prefix}/sales-forecast/purchase-plan/list`,
    method: 'post',
    data
  })
}
// 销售预测-转采购计划-导出
export function salesToPurchaseExport(data) {
  return request({
    url: `${prefix}/sales-forecast/purchase-plan/export`,
    method: 'post',
    data
  })
}
// 销售预测-转采购计划-创建
export function salesToPurchaseCreate(data, params) {
  return request({
    url: `${prefix}/sales-forecast/purchase-plan/create`,
    method: 'post',
    data,
    params
  })
}

// 采购计划列表
export function getPurchasePlanList(data) {
  return request({
    url: `${prefix}/purchase-plan/list`,
    method: 'post',
    data
  })
}
// 采购计划列表-操作
export function approvePurchasePlan(data, params) {
  return request({
    url: `${prefix}/purchase-plan/headers-approve`,
    method: 'post',
    data,
    params
  })
}

// 采购计划列表-提交
export function submitPurchasePlanItems(data) {
  return request({
    url: `${prefix}/purchase-plan/headers-submit-items`,
    method: 'post',
    data
  })
}

// 采购计划明细页面头信息
export function getPurchasePlanDetail(planOrderNo) {
  return request({
    url: `${prefix}/purchase-plan/detail?planOrderNo=${planOrderNo}`,
    method: 'post'
  })
}

// 采购计划详情页明细列表
export function getPurchasePlanDetailItems(data) {
  return request({
    url: `${prefix}/purchase-plan/detail-items`,
    method: 'post',
    data
  })
}
// 采购计划详情页-保存并提交
export function updatePurchasePlanDetailAndSubmit(data) {
  return request({
    url: `${prefix}/purchase-plan/editPage/Submit`,
    method: 'post',
    data
  })
}
// 采购计划详情页-保存
export function updatePurchasePlanDetail(data) {
  return request({
    url: `${prefix}/purchase-plan/update`,
    method: 'post',
    data
  })
}
// 采购计划详情页明细列表-删除
export function deletePurchasePlanDetailItems(data) {
  return request({
    url: `${prefix}/purchase-plan/item-delete`,
    method: 'post',
    data
  })
}

// 采购计划明细列表
export function getPurchaseDetailList(data) {
  return request({
    url: `${prefix}/purchase-plan/items-list`,
    method: 'post',
    data
  })
}
// 采购计划明细列表-审批
export function approvePurchasePlanDetail(data) {
  return request({
    url: `${prefix}/purchase-plan/items-approve`,
    method: 'post',
    data
  })
}

// 采购计划明细列表-导出
export function exportPurchasePlanDetailList(data) {
  return request({
    url: `${prefix}/purchase-plan/items-export`,
    method: 'post',
    data
  })
}
// 采购计划明细-导出
export function exportPurchasePlanDetailRows(planOrderNo) {
  return request({
    url: `${prefix}/purchase-plan/export-item-excel?planOrderNo=${planOrderNo}`,
    method: 'get'
  })
}
// 创建招标-列表查询页面
export function getPurchasePlanTendersList(data) {
  return request({
    url: `${prefix}/purchase-plan/items-page-list`,
    method: 'post',
    data
  })
}

// 创建招标
export function createPurchasePlanTenders(data) {
  return request({
    url: `${prefix}/purchase-plan/create-bids`,
    method: 'post',
    data
  })
}

// 战略备货
// 列表查询
export function getStrategyStockList(data) {
  return request({
    url: `${prefix}/stockStrategyHeader/queryList`,
    method: 'post',
    data
  })
}
// 删除
export function deleteStrategyStock(data) {
  return request({
    url: `${prefix}/stockStrategyHeader/deleteByTaskIdList`,
    method: 'post',
    data
  })
}
// 新增
export function addStrategyStock(data) {
  return request({
    url: `${prefix}/stockStrategyHeader/save`,
    method: 'post',
    data
  })
}
// 更新
export function updateStrategyStock(data) {
  return request({
    url: `${prefix}/stockStrategyHeader/update`,
    method: 'post',
    data
  })
}
// 查询头信息
export function getStrategyStockHeader(id) {
  return request({
    url: `${prefix}/stockStrategyHeader/queryHeadByTaskId?taskId=${id}`,
    method: 'get'
  })
}
// 查询备货详情
export function getStrategyStockDetail(id) {
  return request({
    url: `${prefix}/stockStrategyHeader/queryDeatil?taskId=${id}`,
    method: 'post'
  })
}
// 试算
export function calculateStrategyStock(data) {
  return request({
    url: `${prefix}/stockStrategyHeader/calculating`,
    method: 'post',
    data
  })
}
// 战略 备货 step1
// 更新行
export function updateStrategyItemForStep1(data) {
  return request({
    url: `${prefix}/stockStrategyItem/update`,
    method: 'post',
    data
  })
}
// 批量删除行
export function deleteStrategyItemForStep1(data) {
  return request({
    url: `${prefix}/stockStrategyItem/batchDeleteItem`,
    method: 'post',
    data
  })
}
// 查询行
export function getStrategyItemForStep1(params) {
  return request({
    url: `${prefix}/stockStrategyItem/queryList`,
    method: 'get',
    params
  })
}
// 导出行
export function exportStrategyItemForStep1() {
  window.open(`${prefix}/stockStrategyItem/exportStepOne`)
}
// 导入行
export function importStrategyItemForStep1(data, factory) {
  return request({
    url: `${prefix}/stockStrategyItem/import?factory=${factory}`,
    method: 'post',
    data
  })
}

// 战略 备货 step2
// 查询头信息
export function getStrategyHeaderForStep2(id) {
  return request({
    url: `${prefix}/stockStrategyHeader/queryHeaderByTaskId?taskId=${id}`,
    method: 'get'
  })
}
// 更新行
export function updateStrategyItemForStep2(data) {
  return request({
    url: `${prefix}/stockStrategyItem/aiPage/update?taskId=${data.taskId}`,
    method: 'post',
    data: data.aiItem
  })
}
// 删除行
export function deleteStrategyItemForStep2(data) {
  return request({
    url: `${prefix}/aiStockStrategyItem/batchDeleteItem?taskId=${data.taskId}&itemNoList=${data.itemNoList}`,
    method: 'post'
  })
}
// 查询行
export function getStrategyItemForStep2(params) {
  return request({
    url: `${prefix}/aiStockStrategyItem/queryAiItemList`,
    method: 'get',
    params
  })
}
// 保存ai结果前对比数据
export function checkStrategyItemForStep2Compare(data) {
  return request({
    url: `${prefix}/stockStrategyItem/checkItemCanCalculate`,
    method: 'post',
    data
  })
}
// 导出行
export function exportStrategyItemForStep2(data) {
  const url = data.taskId ? `${prefix}/stockStrategyItem/ai/export?taskId=${data.taskId}` : `${prefix}/stockStrategyItem/ai/export?stockRecommendAlgorithm=${data.stockRecommendAlgorithm ?? ''}`
  return request({
    url,
    method: 'post'
  })
}
// 下载模版
export function downloadStrategyItemForStep2(data) {
  const url = `${prefix}/stockStrategyItem/exportStepTwo?stockRecommendAlgorithm=${data.stockRecommendAlgorithm ?? ''}`
  window.open(url)
}
// 获取饼图数据
export function getPieData(data) {
  return request({
    url: `${prefix}/stockStrategyHeader/queryStockRisk?taskId=${data.taskId}`,
    method: 'get'
  })
}
//  step3 战略备货创建采购计划
export function createPurchasePlanForStrategy(data) {
  return request({
    url: `${prefix}/stockStrategyPurchasePlan/stock-strategy-save?taskId=${data.taskId}&type=${data.type}&planTime=${data.planTime}`,
    method: 'get'
  })
}
// 创建采购计划-人工采购计划
export function createPurchasePlan(data) {
  return request({
    url: `${prefix}/stockStrategyPurchasePlan/people-saveOrUpdate`,
    method: 'post',
    data
  })
}
// 创建 并  提交采购计划
export function submitPurchasePlan(data) {
  return request({
    url: `${prefix}/stockStrategyPurchasePlan/people-submit-saveOrUpdate`,
    method: 'post',
    data
  })
}
// 人工采购计划明细-导出
export function exportPurchasePlanDetail(data) {
  window.open(`${prefix}/stockStrategyPurchasePlan/export`)
}
// 人工采购计划明细-删除
export function deletePurchasePlanDetail(data) {
  return request({
    url: `${prefix}/stockStrategyPurchasePlan/people-item-delete`,
    method: 'post',
    data
  })
}
// 根据sku和factory查询行信息
export function getPurchasePlanSkuInfo(data) {
  return request({
    url: `${prefix}/stockStrategyPurchasePlan/purchasePlan-info?sku=${data.sku}&factory=${data.factory}&position=${data.position || ''}`,
    method: 'post'
  })
}
