import request from '@/utility/request'

const prefix = '/data-center-front'

const apiMapping = {
  directSale: {
    dropdown: 'price/compare/config/directSale/dropdown',
    list: 'price/compare/config/directSale/list',
    update: 'price/compare/config/directSale/update'
  },
  distribute: {
    dropdown: 'price/compare/config/distribute/dropdown',
    list: 'price/compare/config/distribute/list',
    update: 'price/compare/config/distribute/update'
  }
}

export function getCompetitor(type) {
  return request({
    url: `${prefix}/${apiMapping[type].dropdown}`
  })
}

export function getConfigList(type, data) {
  return request({
    url: `${prefix}/${apiMapping[type].list}`,
    method: 'post',
    data
  })
}

export function updateCompareConfig(type, data) {
  return request({
    url: `${prefix}/${apiMapping[type].update}`,
    method: 'put',
    data
  })
}
