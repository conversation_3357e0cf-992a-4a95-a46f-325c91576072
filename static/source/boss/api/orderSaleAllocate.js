import request from '@/utility/request'

const prefix = '/api-opc'
const apiBossOpc = 'api-opc-atp'
/**
 * 查询被匀货订单的商品行信息
 * @param {*} data
 */
export function getSourceAllocateItem (data) {
  return request({
    url: `${prefix}/v1/so/template/so/allocate/source`,
    method: 'post',
    data
  })
}

/**
 * 计算接受匀货的订单商品行信息
 * @param {*} data
 */
export function getTargetAllocateInfo (data) {
  return request({
    url: `${prefix}/v1/so/template/so/allocate/target`,
    method: 'post',
    data
  })
}

/**
 * 执行匀货操作
 * @param {*} data
 */
export function allocate (data) {
  return request({
    url: `${prefix}/v4/oms/order/doAllocate`,
    method: 'post',
    data
  })
}

/**
 * 匀货列表
 * @param {*} data
 */
export function allocateList (data) {
  const { pageNo = 1, pageSize = 20 } = data
  return request({
    url: `${apiBossOpc}/inventory/adjust/list?pageNo=${pageNo}&pageSize=${pageSize}`,
    method: 'post',
    data
  })
}

/**
 * 匀货保存
 * @param {*} data
 */
export function saveAllocate (data) {
  return request({
    url: `${apiBossOpc}/inventory/adjust`,
    method: 'post',
    data
  })
}

/**
 * 匀货详情
 * @param {*} data
 */
export function getAllocate (id) {
  return request({
    url: `${apiBossOpc}/inventory/adjust/adjust_inventory_nos/${id}`,
    method: 'get'
  })
}

/**
 * 匀货操作
 * @param {*} data
 */
export function changeAllocate (adjustInventoryNo, adjustInventoryItemNo, targetStatus) {
  return request({
    url: `${apiBossOpc}/inventory/adjust/adjust_inventory_nos/${adjustInventoryNo}/adjust_inventory_item_nos/${adjustInventoryItemNo}`,
    method: 'put',
    data: {
      targetStatus
    }
  })
}
