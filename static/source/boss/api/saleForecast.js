import request from '@/utility/request'

const prefix = '/data-center-front'
const prefixAb = '/api-ab';

export const api = ({ url, method = 'get', query, data, complete }) => {
  return request({
    url: `${prefix}${url}`,
    method,
    params: query,
    data
  }).then(res => {
    complete && complete(res)
    return res
  }).catch(err => {
    complete && complete(err)
    return { code: -1, err }
  })
}

export const getDetail = params => {
  return request({
    url: `${prefix}/sales/forecast/detail`,
    method: 'get',
    params
  })
}

// 日志详情
export const getLog = (params) => {
  return request({
    url: `${prefix}/sales/forecast/log`,
    method: 'get',
    params
  })
}

// 销售预报跟踪表列表基础信息
export const getSalesReportList = (data) => {
  return request({
    url: `${prefixAb}/sales-report-record/list`,
    method: 'post',
    data
  })
}
// 销售预报跟踪表列表月份详情信息
export const getSalesReportListExtInfo = (params, data) => {
  return request({
    url: `${prefixAb}/sales-report-record/listExtInfo`,
    method: 'post',
    data,
    params
  })
}
// 销售预报跟踪表-历史记录
export const getSalesReportLogs = (params) => {
  return request({
    url: `${prefixAb}/sales-report-record/approve-logs`,
    method: 'get',
    params
  })
}
// 销售预报跟踪表-订单信息
export const getSalesReportOrdersInfo = (params) => {
  return request({
    url: `${prefixAb}/sales-report-record/orders-info`,
    method: 'get',
    params
  })
}
// 销售预报跟踪表-关单列表
export const getSalesReportCloseList = (data) => {
  return request({
    url: `${prefixAb}/sales-report-record/close/queryList`,
    method: 'post',
    data
  })
}
// 销售预报跟踪表-批量关闭
export const batchCloseReport = (data) => {
  return request({
    url: `${prefixAb}/sales-report-record/batch/close`,
    method: 'post',
    data
  })
}
// 销售预报跟踪表-批量提交
export const batchSubmitReport = (data) => {
  return request({
    url: `${prefixAb}/sales-report-record/batchSubmit`,
    method: 'post',
    data
  })
}

// 销售预报跟踪表-可用库存
export const getAvailableStock = (params) => {
  return request({
    url: `${prefixAb}/sales-report-record/available/stock`,
    method: 'post',
    params
  })
}

// 销售预报申请列表
export const getSalesApplyList = (data) => {
  return request({
    url: `${prefixAb}/sales-report/queryList`,
    method: 'post',
    data
  })
}

// 销售预报申请表-批量驳回
export const submitSalesApply = (data) => {
  return request({
    url: `${prefixAb}/sales-report/batchSubmit`,
    method: 'post',
    data
  })
}

// 销售预报申请表-批量通过
export const approveSalesApply = (data) => {
  return request({
    url: `${prefixAb}/sales-report/batchApprove`,
    method: 'post',
    data
  })
}
// 销售预报申请表-批量驳回
export const rejectSalesApply = (data) => {
  return request({
    url: `${prefixAb}/sales-report/batchReject`,
    method: 'post',
    data
  })
}
// 销售预报申请表-批量导出
export const exportSalesApply = (data) => {
  return request({
    url: `${prefixAb}/sales-report/export`,
    method: 'post',
    data
  })
}
// 销售预报申请表-批量审核导出
export const exportBatchCheck = (data) => {
  return request({
    url: `${prefixAb}/sales-report/exportBatchCheck`,
    method: 'post',
    data
  })
}
// 销售预报申请表-批量删除
export const deleteSalesApply = (data) => {
  return request({
    url: `${prefixAb}/sales-report/delete`,
    method: 'post',
    data
  })
}
// 销售预报申请信息
export const getSalesApplyDetail = (data) => {
  return request({
    url: `${prefixAb}/sales-report-record/queryByReportNo`,
    method: 'post',
    data
  })
}

// 销售预报申请信息-oms信息
export const getSalesApplyDetailOrder = (params) => {
  return request({
    url: `${prefixAb}/sales-report/queryOrder`,
    method: 'post',
    params
  })
}

// 销售预报申请信息-订单趋势
export const getSalesApplyDetailTrends = (params) => {
  return request({
    url: `${prefixAb}/sales-report-record/order/trends`,
    method: 'post',
    params
  })
}

// 销售预报申请-新增记录是订单数据和AI预测数据
export const getSalesApplyDetailExtInfo = (data) => {
  return request({
    url: `${prefixAb}/sales-report/queryExtInfo`,
    method: 'post',
    data
  })
}

// 销售预报申请-保存预报
export const saveSalesApplyDetail = (data) => {
  return request({
    url: `${prefixAb}/sales-report/save`,
    method: 'post',
    data
  })
}
// 销售预报申请-保存并提交
export const saveAndSubmitSalesApplyDetail = (data) => {
  return request({
    url: `${prefixAb}/sales-report/saveAndSubmit`,
    method: 'post',
    data
  })
}

// 销售预报申请-根据sku和销售组织获取工厂
export const getFactorys = (params) => {
  return request({
    url: `${prefixAb}/sales-forecast/available-factory`,
    method: 'post',
    params
  })
}
// 销售预报申请-根据sku和工厂获取库位
export const getPositions = (params) => {
  return request({
    url: `${prefixAb}/sales-forecast/all-positions`,
    method: 'post',
    params
  })
}
// 销售预报跟踪表/明细表 下载导入模版
export const downloadTemplate = () => {
  return request({
    url: `${prefixAb}/sales-report/exportTemplate`,
    method: 'post'
  })
}
// 销售预报跟踪表 查看操作记录
export const getProcessList = () => {
  return request({
    url: `${prefixAb}/sales-report/queryTaskList`,
    method: 'post'
  })
}
// 销售预报跟踪表-批量导出
export const exportSalesApplyNew = (data) => {
  return request({
    url: `${prefixAb}/sales-report-record/export`,
    method: 'post',
    data
  })
}
