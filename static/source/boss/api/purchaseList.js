import request, {
  downloadFile
} from '@/utility/request'
import {
  EachTree, toTree
} from '@/utils/index.js'

const prefix = '/oms-base/purchase'
const prefixNew = '/oms-new/purchase'
const prefixPending = '/oms-new/pending'
const prefixOMS = '/oms-new'
const workflow = '/api-workflow'
const opc = '/api-opc'

export const oms = ({ url, method = 'get', query, data, complete }) => {
  return request({
    url: `${prefixOMS}${url}`,
    method,
    params: query,
    data
  }).then(res => {
    complete && complete(res)
    return res
  }).catch(err => {
    complete && complete(err)
    return { code: -1, err }
  })
}

// 请求
export function purchaseList (data) {
  return request({
    // url: `${prefix}/list`,
    url: `${prefixNew}/list`,
    method: 'post',
    data
  })
}
export function unPurchaseList (data) {
  return request({
    // url: `${prefix}/not/list`,
    url: `${prefixNew}/not/list`,
    method: 'post',
    data
  })
}
export function purchaseDetail (productNo, data) {
  return request({
    url: `${prefix}/detail/${productNo}`,
    method: 'get',
    params: data
  })
}
export function unPurchaseDetail (productNo, data) {
  return request({
    url: `${prefix}/not/detail/${productNo}`,
    method: 'get',
    params: data
  })
}
export function purchaseSODetail (salesOrderNo, data) {
  return request({
    url: `${prefix}/detail/sales/${salesOrderNo}`,
    method: 'get',
    params: data
  })
}
/** 采购异常未下单 - start ***/

// 异常未下单列表
export function pendingOrderList (data) {
  return request({
    url: `${prefixPending}/order/list/`,
    method: 'get',
    params: data
  })
}
// 新增列表项
export function pendingOrderAdd (data) {
  return request({
    url: `${prefixPending}/order/add/`,
    method: 'post',
    data: data
  })
}
// 删除列表项
export function pendingOrderDel (id) {
  return request({
    url: `${prefixPending}/order/delete/${id}`,
    method: 'delete'
  })
}
// 编辑列表项
export function pendingOrderEdit (id, data) {
  return request({
    url: `${prefixPending}/order/update/${id}`,
    method: 'put',
    data: data
  })
}
// 发送邮件 -> 确认提交
export function pendingOrderEmail () {
  return request({
    url: `${prefixPending}/order/commit/`,
    method: 'get'
  })
}
// 发送邮件
export function pendingOrderEmailStatus () {
  return request({
    url: `${prefixPending}/order/email/status`,
    method: 'post'
  })
}
/** 字典接口 - 查询字典
 * @param {*} params { excludeSplit?: string, typeSplit?: string }
 */
export function getDictionaryList (params) {
  if (reasonCache && reasonCache.length > 0) {
    return new Promise(resolve => {
      resolve(reasonCache)
    })
  } else {
    return request({
      url: `${opc}/v1/dict/values/values/all`,
      method: 'get',
      params
    }).then(res => {
      if (res.code === 200) {
        // code=>value
        // name=>label
        // childList=>children
        const result = toTree(res.data.map(item => { return { value: Number(item.code), label: item.name, parentCode: Number(item.parentCode) } }))
        reasonCache = result
      }
      return reasonCache
    })
  }
}
let reasonCache = []
// 获取未下单原因列表
export function pendingNoOrderReason () {
  if (reasonCache && reasonCache.length > 0) {
    return new Promise(resolve => {
      resolve(reasonCache)
    })
  } else {
    return request({
      url: `${prefixPending}/order/reason`,
      method: 'get'
    }).then(res => {
      if (res.code === 200) {
        // code=>value
        // name=>label
        // childList=>children
        const result = EachTree({
          childList: res.data
        },
        'children',
        item => {
          if (item.code) {
            item.value = item.code
            delete item.code
          }
          if (item.name) {
            item.label = item.name
            delete item.name
          }
          if (item.childList) {
            item.children = item.childList
            delete item.childList
          }
          return item
        }
        )

        reasonCache = result['children']
      }
      return reasonCache
    })
  }
}
// 上传文件
export function uploadFile (formData) {
  return request({
    method: 'post',
    url: prefixPending + '/order/readExcel',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 下载模板
// 暂不使用
export function downloadTemplate () {
  return request({
    method: 'get',
    responseType: 'blob',
    url: prefixPending + '/order/template/download',
    headers: {
      'Content-Type': 'application/octet-stream'
    }
  }).then(blob => {
    // FileReader主要用于将文件内容读入内存
    var reader = new FileReader()
    reader.readAsDataURL(blob)
    // onload当读取操作成功完成时调用
    reader.onload = e => {
      var a = document.createElement('a')
      // 获取文件名fileName
      var fileName = '模板.xls' // res.headers['content-disposition'].split('=')
      fileName = fileName[fileName.length - 1]
      fileName = fileName.replace(/"/g, '')
      a.download = fileName
      a.href = e.target.result
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  })
}
/** 采购异常未下单 - end ***/
/** 下单异常统计分析 - start ***/
// 未下单明细
export function pendingOrderReportPo (data) {
  return request({
    url: `${prefixPending}/order/report/po`,
    method: 'get',
    params: data
  })
}
// 主管对应物料组
export function pendingOrderReportProductGroup (data) {
  return request({
    url: `${prefixPending}/order/report/product/group`,
    method: 'get',
    params: data
  })
}
// 主管对应采购组
export function pendingOrderReportPurchaseGroup (data) {
  return request({
    url: `${prefixPending}/order/report/purchase/group`,
    method: 'get',
    params: data
  })
}

// 采购组报表下载
export function cgzDownload (date) {
  if (!Array.isArray(date)) return
  const [batchDate, endBatchDate] = date
  const url =
    `${prefixPending}/order/report/purchase/group/export/excel?batchDate=${batchDate}&endBatchDate=${endBatchDate}`
  downloadFile(url)
}
// 物料组报表下载
export function wlzDownload (date) {
  if (!Array.isArray(date)) return
  const [batchDate, endBatchDate] = date
  const url =
    `${prefixPending}/order/report/product/group/export/excel?batchDate=${batchDate}&endBatchDate=${endBatchDate}`
  downloadFile(url)
}
// 未下单明细下载
export function wxdDownload (date) {
  if (!Array.isArray(date)) return
  const [batchDate, endBatchDate] = date
  const url =
    `${prefixPending}/order/report/po/export/excel?batchDate=${batchDate}&endBatchDate=${endBatchDate}`
  downloadFile(url)
}
/** 下单异常统计分析 - end ***/

/** 工单接口服务
 * 工单处理接口
 * @param {*} params { id: string }
 *  ***/
export function creatOrder (id) {
  return request({
    url: `${workflow}/pending/order/${id}`,
    method: 'get'
  })
}
