import request from '@/utility/request'

const omsConfig = '/oms-config'
const prefix = '/api-ab'
const sellOrderHost = '/api-opc'

// 工厂列表
export function queryFactoryList(data) {
  return request({
    url: `${omsConfig}/factory-base/query/all`,
    method: 'post',
    data
  });
}

// 级库存列表
export function queryClassificationList(data) {
  return request({
    url: `${prefix}/sku-classification/operating/pageList`,
    method: 'post',
    data
  });
}

// 级库存删除
export function deleteClassification(data) {
  return request({
    url: `${prefix}/sku-classification/operating/deleteBatch`,
    method: 'post',
    data
  });
}

// 级库存导入
export function uploadClassification(data) {
  return request({
    url: `${prefix}/sku-classification/operating/upload`,
    method: 'post',
    data
  });
}

// 级库存导出
export function exportClassification(data) {
  return request({
    url: `${prefix}/sku-classification/operating/export`,
    method: 'post',
    data
  });
}

// 级库存保存
export function upsertBatchClassification(data) {
  return request({
    url: `${prefix}/sku-classification/operating/upsertBatch`,
    method: 'post',
    data
  });
}
// 获取仓
export function getwWarehouseCode (data) {
  return request({
    url: `${omsConfig}/supply-network/query/common`,
    method: 'post',
    data
  })
}
export function searchSkuList (search) {
  return request({
    url: `${sellOrderHost}/v1/so/template/sku/like`,
    params: {
      vague: search
    }
  })
}
