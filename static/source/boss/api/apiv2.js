import Axios from '@/utils/axiosWrapper.js'

const prefix = '/documentApi'
// const prefix_token = '/token-base'
const api = {

  getToken: data => Axios.get('/token-base/token', { params: data }),

  ecorpDocList: (voucherNo, data) => Axios.get(`${prefix}/ecorpDocList/${voucherNo}`, { params: data }),
  edDustbinList: voucherNo => Axios.get(`${prefix}/edDustbinList/${voucherNo}`),
  orderList: data => Axios.get('/orderList', { params: data }),
  upEcorpRemark: (id, data) => Axios.get(`${prefix}/upEcorpRemark/${id}`, { params: data }),
  orderTotal: (data) => Axios.get(`${prefix}/total`, { params: data }),

  delEcorpDoc: (data) => Axios({
    method: 'post',
    url: `${prefix}/delEcorpDoc`,
    data: data
  }),
  recoverEcorpDoc: (id, data) => Axios({
    method: 'post',
    url: `${prefix}/recoverEcorpDoc/${id}`,
    params: data
  }),

  downloadFile: (queryData) => Axios({
    method: 'post',
    url: `${prefix}/downloadFile`,
    params: queryData
  }),

  uploadFile: (id, formData, queryData) => Axios({
    method: 'post',
    url: `${prefix}/uploadFile/${id}`,
    data: formData,
    params: queryData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })

}
export default api
