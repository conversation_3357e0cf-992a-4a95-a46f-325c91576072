import request from '@/utility/request'

const apiBossOpc = '/api-opc-atp'
const apiMM = '/api-mm'

export function getSupplyDetailList (params) {
  return request({
    url: `${apiBossOpc}/boss/report/query`,
    method: 'get',
    params
  })
}

export function getBatchExport (params) {
  return request({
    url: `${apiBossOpc}/boss/report/batch/export`,
    method: 'get',
    params
  })
}

export function getWarehouseList (params) {
  return request({
    url: `${apiMM}/config/factoryAndWarehouse/page`,
    method: 'get',
    params
  })
}
