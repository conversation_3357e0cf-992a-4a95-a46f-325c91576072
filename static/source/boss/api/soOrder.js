import request from '@/utility/request'

const apiMdm = '/api-mdm'
const apiBossOpc = '/api-boss-opc'
// so订单物料组查询mdm接口
export function getProductGroupOptions (type) {
  return request({
    url: `${apiMdm}/optionset/${type}`,
    method: 'get'
  })
}
// so订单导出
export function exportSoOrders (data) {
  return request({
    url: `${apiBossOpc}/so/item/export`,
    method: 'post',
    data
  })
}
// so订单查询列表
export function getSoOrders (data) {
  return request({
    url: `${apiBossOpc}/so/item`,
    method: 'post',
    data
  })
}

export function fetchConfigFromServer () {
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=2`,
    method: 'get'
  })
}

export function saveConfig2Server (data) {
  data = data.map((d, idx) => ({
    ...d,
    parentId: 1,
    orderNo: idx + 1
  }))
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=2`,
    method: 'post',
    data
  })
}
