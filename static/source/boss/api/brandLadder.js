import request, { downloadFile } from '@/utility/request'

const prefix = '/data-center-front'

// 品牌阶梯查询
export function getBrandLadders(data) {
  return request({
    url: `${prefix}/price/brand/ladder/list`,
    method: 'post',
    data
  })
}

export function getGroupData() {
  return request({
    url: `${prefix}/price/brand/ladder/group`,
    method: 'get'
  })
}

export function getAdjustOption() {
  return request({
    url: `${prefix}/price/brand/ladder/adjust/dropdown`,
    method: 'get'
  })
}

export function getBrandLadderOption() {
  return request({
    url: `${prefix}/price/brand/ladder/dropdown`,
    method: 'get'
  })
}

export function updateBrand(id, data) {
  return request({
    url: `${prefix}/price/brand/ladder/${id}`,
    method: 'put',
    data
  })
}

export function exportBasisApi(params) {
  return downloadFile(`${prefix}/price/brand/ladder/export`, null, {
    ignoreEmpty: true,
    params
  })
}
