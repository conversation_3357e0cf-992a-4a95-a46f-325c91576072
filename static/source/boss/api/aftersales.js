import request, { downloadFile } from '@/utility/request'

const prefix = '/api-sales'
const security = '/security-api'
const apiWorkflow = '/api-workflow'
const apiVCSupplier = '/api-vc-supplier'

export const api = ({ url, method = 'get', query, data, complete }) => {
  return request({
    url: `${prefix}${url}`,
    method,
    params: query,
    data
  }).then(res => {
    complete && complete(res)
    return res
  }).catch(err => {
    complete && complete(err)
    return err
  })
}

export function exportBasisApi(params) {
  return downloadFile(`${prefix}/ticket/exportAfterSalesData`, null, {
    ignoreEmpty: true,
    params
  })
}

// 安全中心查人员
export function getUser(params) {
  return request({
    url: `${security}/account`,
    params
  })
}

// 获取工单信息
export function getWorkOrder(workOrderNo) {
  return request({
    url: `${apiWorkflow}/after/sale/getWorkOrder`,
    params: {
      workOrderNo
    }
  })
}
// vc查询供应商
export function getSupplierInfo(params) {
  return request({
    url: `${apiVCSupplier}/admin/profile/listContact`,
    params
  })
}
// 售后运营维护供应商信息
export function provideSupplierInfo(data) {
  return request({
    url: `${prefix}/ticket/service/operator/provideSupplyInfo`,
    method: 'post',
    data
  })
}
// 客户已下单-填写销售订单
export function batchAddOrder(data) {
  return request({
    url: `${prefix}/ticket/service/supplement/batchAddOrder`,
    method: 'post',
    data
  })
}

export function getAllWorkOrder (params) {
  return request({
    url: apiWorkflow + '/admin/listAllWorkOrder',
    method: 'get',
    params
  })
}
