import request from '@/utility/request'

const omsConfig = '/oms-config'
const gateway = '/api-cc-gateway'
const standard = '/api-cc-standard'

// 获取工厂list
export function getAllFactory(data) {
  return request({
    url: `${omsConfig}/factory-base/query/all`,
    method: 'post',
    data
  })
}
// 获取商品中心枚举
export function getEntityListApi(data) {
  return request({
    url: `${standard}/optionsets/entityTypes`,
    method: 'post',
    data
  });
}
// 获取品牌
export function getBrandListApi(params) {
  return request({
    url: `${gateway}/api/scc/v1/brand/page`,
    method: 'get',
    params
  });
}

// 获取类目list
export function getCatalogTree(data) {
  return request({
    url: `${gateway}/api/scc/v1/catalog/tree`,
    method: 'get',
    params: data
  })
}

// 获取履约费率列表
export function qryRateList(data) {
  return request({
    url: `${omsConfig}/fulfillment-fee/list`,
    method: 'post',
    data
  });
}

// 删除履约费率
export function delRate(data) {
  return request({
    url: `${omsConfig}/fulfillment-fee/batch-delete`,
    method: 'post',
    data
  });
}

// 履约费率导出
export function exportRate(data) {
  return request({
    url: `${omsConfig}/fulfillment-fee/export`,
    method: 'post',
    data
  })
}

// 履约费率查询
export function qryDeafaultRate(params) {
  return request({
    url: `${omsConfig}/fulfillment-fee/query-default-fee`,
    method: 'get',
    params
  });
}

// 履约费率保存
export function saveRate(data) {
  return request({
    url: `${omsConfig}/fulfillment-fee/save`,
    method: 'post',
    data
  });
}

// 履约费率更新
export function updateRate(data) {
  return request({
    url: `${omsConfig}/fulfillment-fee/update`,
    method: 'post',
    data
  });
}

// 默认履约费率保存
export function saveDefaultRate(params) {
  return request({
    url: `${omsConfig}/fulfillment-fee/update-default-fee`,
    method: 'post',
    params
  });
}

// 履约费率导入
export function importRate(data) {
  return request({
    url: `${omsConfig}/fulfillment-fee/import`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 履约费率模版下载
export function downloadTemplate(params) {
  return request({
    url: `${omsConfig}/fulfillment-fee/getExcelTemplateUrl`,
    method: 'get',
    params
  });
}
