import request, { downloadFile } from '@/utility/request'

const prefix = '/data-center-front'

export function addAdjust(data) {
  return request({
    url: `${prefix}/price/adjust/add`,
    method: 'post',
    data
  })
}

export function getAdjustList(data) {
  return request({
    url: `${prefix}/price/adjust/list`,
    method: 'post',
    data
  })
}

export function fetchDetailData(id) {
  return request({
    url: `${prefix}/price/adjust/detail/${id}`
  })
}

export function getAdjustStatusOptions() {
  return request({
    url: `${prefix}/price/adjust/status/dropdown`
  })
}

export function exportList(data) {
  return downloadFile(`${prefix}/price/adjust/sku/export`, data, {
    method: 'POST'
  })
}

export function exportSkuList(data) {
  return downloadFile(`${prefix}/price/adjust/sku/detail/export`, data, {
    method: 'POST'
  })
}

export function getSkuList(data) {
  return request({
    url: `${prefix}/price/adjust/sku/list`,
    method: 'post',
    data
  })
}

export function completeAdjustment(id) {
  return request({
    url: `${prefix}/price/adjust/sku/mark/complete/${id}`,
    method: 'put'
  })
}

export function submitPushMDM(id, data) {
  return request({
    url: `${prefix}/price/adjust/sku/push/${id}`,
    method: 'put',
    data
  })
}

export function updateSkuField(data) {
  return request({
    url: `${prefix}/price/adjust/sku/update`,
    method: 'put',
    data
  })
}

export function refreshHealth(id) {
  return request({
    url: `${prefix}/price/adjust/sku/refresh/health/${id}`,
    method: 'put'
  })
}

export function getSkuOptions(type) {
  return request({
    url: `${prefix}/price/adjust/sku/${type.toLowerCase()}/dropdown`
  })
}
