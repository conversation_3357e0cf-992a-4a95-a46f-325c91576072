import request from '@/utility/request'

const apiMsg = '/api-msg'
// 获取规则
export function getRulesApi(data) {
  return request({
    url: `${apiMsg}/msg_hub/rules`,
    method: 'get',
    params: data
  })
}
// 获取消息列表
export function getMsgListApi(data) {
  return request({
    url: `${apiMsg}/MsgManage/messages`,
    method: 'get',
    params: data
  })
}
// 获取订阅列表
export function getSubscriptionsApi(data) {
  return request({
    url: `${apiMsg}/subscription/subscriptions`,
    method: 'get',
    params: data
  })
}
// 消息订阅
export function postSubscriptionsApi(data) {
  return request({
    url: `${apiMsg}/subscription/subscription`,
    method: 'post',
    data
  })
}
// 取消订阅
export function delSubscriptionApi(data) {
  return request({
    url: `${apiMsg}/subscription/description`,
    method: 'post',
    data
  })
}
// 消息处理
export function operateApi(data) {
  return request({
    url: `${apiMsg}/MsgManage/message/operation`,
    method: 'post',
    data
  })
}
// 批量消息处理
export function operateListApi(data) {
  return request({
    url: `${apiMsg}/MsgManage/messagesOperation`,
    method: 'post',
    data
  })
}
// 获取消息提示列表
export function getMsgTipsListApi(data) {
  return request({
    url: `${apiMsg}/MsgManage/msgGroupList`,
    method: 'get',
    data
  })
}
// 导出
export function exportApi(data) {
  let urlTrail = ''
  for (let item in data) {
    urlTrail += data[item] !== '' && data[item] ? (item + '=' + data[item] + '&') : ''
  }
  console.log(urlTrail)
  window.open(`${apiMsg}/MsgManage/export${urlTrail === '' ? '' : '?' + urlTrail.substring(0, urlTrail.length - 1)}`)
}
