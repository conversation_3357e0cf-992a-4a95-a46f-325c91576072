import request from '@/utility/request'

const prefix = '/data-center-front'
const gateway = '/api-cc-gateway'

export function getTemplate(id) {
  return request({
    url: `${prefix}/price/policy/config/dyn/find/${id}`,
    method: 'get'
  })
}

export function getTemplateName(id) {
  return request({
    url: `${prefix}/price/policy/config/dyn/policy/name/${id}`,
    method: 'get'
  })
}

export function saveTemplate(data) {
  return request({
    url: `${prefix}/price/policy/config/dyn/save`,
    method: 'post',
    data
  })
}

export function fetchPolicyList(data) {
  return request({
    url: `${prefix}/price/policy/relate/reference`,
    method: 'post',
    data
  })
}

export function getHealthTemplate(id) {
  return request({
    url: `${prefix}/price/policy/config/health/find/${id}`,
    method: 'get'
  })
}

export function getHealthTemplateName(id) {
  return request({
    url: `${prefix}/price/policy/config/health/policy/name/${id}`,
    method: 'get'
  })
}

export function saveHealthTemplate(data) {
  return request({
    url: `${prefix}/price/policy/config/health/save`,
    method: 'post',
    data
  })
}

export function getHealthAttribute() {
  return request({
    url: `${prefix}/price/policy/config/health/param/find/all`,
    method: 'get'
  })
}

export function saveHealthAttribute(data) {
  return request({
    url: `${prefix}/price/policy/config/health/param/save`,
    method: 'post',
    data
  })
}

export function getHealthAttributeGuide() {
  return request({
    url: `${prefix}/price/policy/config/health/param/guide/find/all`,
    method: 'get'
  })
}

export function saveHealthAttributeGuide(data) {
  return request({
    url: `${prefix}/price/policy/config/health/param/guide/save`,
    method: 'post',
    data
  })
}

// 调价导向-行动等级-下拉
let actionLevelOptions
export function getActionLevelOptions() {
  return new Promise(resolve => {
    if (actionLevelOptions) {
      resolve(actionLevelOptions)
    } else {
      request({
        url: `${prefix}/price/policy/config/health/param/guide/actionLevel/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          actionLevelOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(actionLevelOptions)
      })
    }
  })
}

// 调价导向-调价潜力下拉
let potentialOptions
export function getPotentialOptions() {
  return new Promise(resolve => {
    if (potentialOptions) {
      resolve(potentialOptions)
    } else {
      request({
        url: `${prefix}/price/policy/config/health/param/guide/potential/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          potentialOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(potentialOptions)
      })
    }
  })
}

// 调价导向-调价方向下拉
let directionOptions
export function getDirectionOptions() {
  return new Promise(resolve => {
    if (directionOptions) {
      resolve(directionOptions)
    } else {
      request({
        url: `${prefix}/price/policy/config/health/param/guide/direction/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200) {
          directionOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(directionOptions)
      })
    }
  })
}

export function dynPriceCalculate(id) {
  return request({
    url: `${prefix}/price/policy/calculate/dyn/restart/now/${id}`,
    method: 'put'
  })
}

export function healthPriceCalculate(id) {
  return request({
    url: `${prefix}/price/policy/calculate/health/restart/now/${id}`,
    method: 'put'
  })
}

export function fetchComputedResult(type, id) {
  return request({
    url: `${prefix}/price/policy/relate/run/${type}/${id}`
  })
}

export function getCatalogueNames(data) {
  return request({
    url: `${gateway}/api/scc/v1/catalog/page`,
    method: 'post',
    data
  })
}

export function fetchDynAnalysisReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/change/analysis/${startId}`
  })
}

export function fetchDynControllableReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/controllable/${startId}`
  })
}

export function fetchDynDistributeResultReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/distribute/result/${startId}`
  })
}

export function fetchDynHealthDistributeReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/health/distribute/${startId}`
  })
}

export function fetchDynHealthOneDimensionalReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/health/one/dimensional/${startId}`
  })
}

export function fetchDynHealthQuadrantReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/health/quadrant/${startId}`
  })
}

export function fetchDynGrossMarginReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/real/gross/margin/${startId}`
  })
}

export function fetchDynTargetRateReport(startId) {
  return request({
    url: `${prefix}/price/run/dyn/target/rate/${startId}`
  })
}
