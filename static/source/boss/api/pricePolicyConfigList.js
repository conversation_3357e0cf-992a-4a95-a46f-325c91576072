import request from '@/utility/request';

const prefix = '/data-center-front';
// const apiMdm = '/api-mdm'
const gateway = '/api-cc-gateway';
const apiMdm = '/api-mdm';
// const apiPlatform = '/api-platform'

export function getPricePolicyList(data) {
  return request({
    url: `${prefix}/price/policy/relate/list`,
    method: 'post',
    data
  });
}

export function getStatusOptions() {
  return request({
    url: `${prefix}/price/policy/relate/dropdown`,
    method: 'get'
  });
}

export function getSPUOptions() {
  return request({
    url: `${prefix}/price/policy/relate/spu/level/dropdown`,
    method: 'get'
  });
}

// export function getProductPositioningAndTradeOptions() {
//   return request({
//     url: `${apiMdm}/optionset/findBatch?typeCodes=ProductPositioning%2CProductTrade`,
//     method: 'get'
//   })
// }

export function getProductPositioningAndTradeOptions() {
  return request({
    url: `${gateway}/api/scc/v1/dict/typeCodes?typeCodes=ProductPositioning%2CProductTrade`,
    method: 'get'
  });
}

export function getCommodityRange(id) {
  return request({
    url: `${prefix}/price/policy/relate/range/${id}`,
    method: 'get'
  });
}

export function copyCommodityRange(data) {
  return request({
    url: `${prefix}/price/policy/relate/range/copy`,
    method: 'post',
    data
  });
}

export function updateCommodityRange(data) {
  return request({
    url: `${prefix}/price/policy/relate/range/update`,
    method: 'post',
    data
  });
}

// export function getBrandListApi(params) {
//   return request({
//     url: `${apiMdm}/brands`,
//     method: 'get',
//     params
//   })
// }

export function getBrandListApi(params) {
  return request({
    url: `${gateway}/api/scc/v1/brand/page`,
    method: 'get',
    params
  });
}

export function setTargetRate(data) {
  return request({
    url: `${prefix}/price/policy/calculate/dyn/set/target/rate`,
    method: 'post',
    data
  });
}

export function getWebsiteOptions() {
  return request({
    url: `${prefix}/epms/run/website/dropdown`,
    method: 'get'
  });
}

// todo: 待切换接口
export function getSysuser(params) {
  return request({
    url: `${apiMdm}/sysuser`,
    method: 'get',
    params
  });
}
