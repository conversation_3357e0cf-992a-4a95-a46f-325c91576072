import rawRequest from '@/utility/request'
import { MessageBox } from 'element-ui'
import qs from 'qs'

const request = function () {
  return rawRequest(...arguments)
    .then(res => {
      // 约定code为0表示请求成功, data字段为返回数据, code不为0时显示异常, 展示message信息
      if (res.code === 0) {
        return res.data
      } else {
        MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
      }
    })
    .catch(err => {
      MessageBox.alert(err.msg || err.message || '请求失败！', { type: 'error' })
    })
}
const apiMM = '/api-mm'

/** 方生催货列表 - 查询
 * @param {*} params { }
 */
export function getExpeditingList (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return request({
    url: `${apiMM}/api/v1/fsReport/pageFsReportExpedite?${queryStr}`,
    method: 'post'
  })
}

/** 方生催货列表导出 - 查询
 * @param {*} params { }
 */
export function exportExpeditingList (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return rawRequest({
    url: `${apiMM}/api/v1/fsReport/exportFsReportExpedite?${queryStr}`,
    method: 'post'
  })
}
/** 方生提货列表 - 查询
 * @param {*} params { }
 */
export function getPickUpGoodsList (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return request({
    url: `${apiMM}/api/v1/fsReport/pageFsReportPickup?${queryStr}`,
    method: 'post'
  })
}

/** 方生提货列表导出 - 查询
 * @param {*} params { }
 */
export function exportPickUpGoodsList (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return rawRequest({
    url: `${apiMM}/api/v1/fsReport/exportFsReportPickup?${queryStr}`,
    method: 'post'
  })
}
/** 方生提货列表 - 批量发送
 * @param {*} params { }
 */
export function sendPickUpGoodsList () {
  return rawRequest({
    url: `${apiMM}/api/v1/fsReport/batchSendPickup`,
    method: 'post'
  })
}

/** 方生分货列表 - 查询
 * @param {*} params { }
 */
export function getDistributionReportList (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return request({
    url: `${apiMM}/api/v1/fsReport/pageFsReportDistribute?${queryStr}`,
    method: 'post'
  })
}
/** 方生分货列表(生成模板) - 查询
 * @param {*} params { }
 */
export function generateDistributionReportListTpl (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return rawRequest({
    url: `${apiMM}/api/v1/fsReport/generateTemplate?${queryStr}`,
    method: 'post'
  })
}
/** 方生分货列表 - 导出
 * @param {*} params { }
 */
export function exportDistributionReportList (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return rawRequest({
    url: `${apiMM}/api/v1/fsReport/exportFsReportDistribute?${queryStr}`,
    method: 'post'
  })
}
/** 方生分货列表 - 数量重新计算
 * @param {*} params { }
 */
export function reCalDistributionReportList (params) {
  const queryStr = qs.stringify(params, {
    arrayFormat: 'repeat'
  })
  return rawRequest({
    url: `${apiMM}/api/v1/fsReport/reDistribute?${queryStr}`,
    method: 'post'
  })
}
