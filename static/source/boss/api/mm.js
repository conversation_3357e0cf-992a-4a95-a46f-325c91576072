import rawRequest from '@/utility/request'
import { MessageBox } from 'element-ui'
import qs from 'qs'
import { formatPurchaseData, formatPurchaseItemData } from '@/utils/poPriceCalculate'

const request = function () {
  return rawRequest(...arguments)
    .then(res => {
      // 约定code为0表示请求成功, data字段为返回数据, code不为0时显示异常, 展示message信息
      if (res.code === 0) {
        return res.data
      } else {
        MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
      }
    })
    .catch(err => {
      MessageBox.alert(err.msg || err.message || '请求失败！', { type: 'error' })
    })
}
const apiMM = '/api-mm'
const apiMrp = '/api-mrp'
const internalApi = '/internal-api'
const simApi = '/api-sim'
const apiSECURITY = '/security-api'
const omsNew = '/oms-base'
const eCorp = '/token-base'
const sdi = '/api-sdi'

// const pmsReport = '/api-pms-report'

/** 字典接口 - 查询字典
 * @param {*} params { excludeSplit?: string, typeSplit?: string }
 */
export function getDictionaryList (params) {
  return request({
    url: `${apiMM}/config/dictionary/list`,
    method: 'get',
    params
  })
}
/** 字典接口 - 查询公司工厂 **/
export function getCompanyAndFactory () {
  return request({
    url: `${apiMM}/config/companyAndFactory/list/`,
    method: 'get'
  })
}
/** 字典接口 - 查询工厂的仓库地点列表 **/
export function getFactoryWarehouse (params) {
  return request({
    url: `${apiMM}/config/factoryAndWarehouse/list`,
    method: 'get',
    params
  })
}
/** 字典接口 - 查询采购订单收发货地址
 * @param {*} params { addressCode?: string, factoryCode: string, type: string }
 */
export function getShippingAddress (params) {
  return request({
    url: `${apiMM}/config/listShippingAddress`,
    method: 'get',
    params
  })
}
/**
 *
 * @param {*} params
 * @returns 产品定位枚举
 */
export function getProductPosition(params) {
  return rawRequest({
    url: `${apiMM}/product/productPosition`,
    method: 'get',
    params
  })
}
/**
 * 库存dms获取PO字段定义
 * @param {*} data { any }
 * @return { Promise }
 */
export function getPoFields (type, routerType) {
  return request({
    url: `${internalApi}/mm/getPoFields`,
    method: 'get',
    params: { type, routerType }
  })
}

/** 采购订单接口
 * 分页查询采购单
 * @param {*} params { orderNo?: string, orderStatus?: number, pageNo?: number, pageSize?: number, purchaseGroup?: string, factoryCode?: string, warehouseLocation?: string }
 */
export function getPurchaseOrderList (data) {
  return request({
    url: `${apiMM}/po/pagePO`,
    method: 'post',
    data
  })
}
/** 采购订单接口
 * 查询采购单详情
 * @param {*} params { orderInfo: string }
 */
export function getOrderDetail (params) {
  return request({
    url: `${apiMM}/po/get`,
    method: 'get',
    params
  })
}
/** 采购订单接口
 * createPO
 * @param {*} data {}
 */
export function createPO (data) {
  return rawRequest({
    url: `${apiMM}/po/create`,
    method: 'post',
    data
  })
}
/** 采购订单接口
 * updatePO
 * @param {*} data {}
 */
export function updatePO (data) {
  return rawRequest({
    url: `${apiMM}/po/update`,
    method: 'put',
    data
  })
}

export function getWarehouseConfig () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=z006.warehouse.config',
    method: 'get'
  })
}
export function getDisableCompanyListConfig () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=disableCompanyList.config',
    method: 'get'
  })
}

export function getMMAuth () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=boss-mm-auth.config',
    method: 'get'
  })
}
export function getMMOneProductAuth () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=boss-mm-one-product.config',
    method: 'get'
  })
}
export function getTrackingOrder () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=boss-mm-tracking-order-download',
    method: 'get'
  })
}
// 获取订单删除原因枚举
export function getDeleteReason () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=boss-mm-delete-reason',
    method: 'get'
  })
}

/** 采购订单接口
 * convertiblePO
 * @param {*} params { companyCode: string, poNoList?: Array[string], purchaseGroup?: string, suppplierNo?: string, type?: number }
 */
export function convertiblePO (params) {
  return request({
    url: `${apiMM}/po/convertible`,
    method: 'get',
    params
  })
}

/** 采购订单接口
 * 采购订单标准转直发
 * @param {*} data { itemList: Array, updateUser: string, type: number ,warehouseLocation: string }
 */
export function convertPO (data) {
  return request({
    url: `${apiMM}/po/convert`,
    method: 'post',
    data
  })
}
// 获取坤合仓编码
export function listKHWarehouseUsingPOST (data) {
  return request({
    url: `${apiMM}/config/khWarehouse/list`,
    method: 'post',
    data
  })
}
// 根据专料专供单号查询价格仓库等信息
export function getInfoBySpecialSupplySo (params) {
  return request({
    url: `${apiMM}/po/getInfoBySpecialSupplySo`,
    method: 'get',
    params
  })
}
/** 供应商相关接口
 * 供应商信息查询
 * @param {*} params { factoryCode: number, supplierNo: string }
 */
export function getSupplierInfo (params) {
  return request({
    url: `${apiMM}/supplier/get`,
    method: 'get',
    params
  })
}
/** 供应商相关接口
 * listSupplierOption
 * @param {*} params { supplierNoOrName: string }
 */
export function getSupplierOptions (params) {
  return request({
    url: `${apiMM}/supplier/options`,
    method: 'get',
    params
  })
}

/** sku相关接口
 * getProductSkuLike
 * @param {*} params { params: object }
 */
export function getProductSkuLike (params) {
  return request({
    url: `${apiMM}/product/skuLike`,
    method: 'get',
    params
  })
}

/** 品牌模糊查询
 * getBrandLike
 * @param {*} params { params: object }
 */
export function getBrandLike (params) {
  return request({
    url: `${apiMM}/product/listBrandNamesByLike`,
    method: 'get',
    params
  })
}

/** 商品详情
 * getProduct
 * @param {*} data { factoryCode: string, skuNos: string }
 */
export function getProduct (data) {
  return request({
    url: `${apiMM}/product/get`,
    method: 'post',
    data
  })
}

/** 商品价格
 * getProduct
 * @param {*} data { factoryCode: string, pageIndex: string, pageSize: string, skuNos: string }
 */
export function getProductPrice (data) {
  return rawRequest({
    url: `${apiMM}/product/getPurchasePrice`,
    method: 'post',
    data
  })
}
// 根据批次号查询价格和跟踪单号
export function querySkuBatchPrice (data) {
  return rawRequest({
    url: `${apiMM}/ido/querySkuBatchPrice`,
    method: 'post',
    data
  })
}

/** 查询采购订单收票地址
 * listBillingAddress
 * @param {*} params { factoryCode: string, purchaseGroup: string }
 */
export function listBillingAddress (params) {
  return request({
    url: `${apiMM}/config/billingAddress/list`,
    method: 'get',
    params
  })
}

/** 查询采购订单发货默认地址
 * listShippingAddress
 * @param {*} params { factoryCode: string, purchaseGroup: string, type: string }
 */
export function getShippingDefaultAddress (params) {
  return request({
    url: `${apiMM}/config/defaultReceiveAddress/get`,
    method: 'get',
    params
  })
}

/** 查询采购订单发货地址
 * listShippingAddress
 * @param {*} params { factoryCode: string, purchaseGroup: string, type: string }
 */
export function listShippingAddress (params) {
  return request({
    url: `${apiMM}/config/dispatchAddress/list`,
    method: 'get',
    params
  })
}

/** 查询sku bom
 * getBom
 * @param {*} params { factoryCode: string, skuNo: string }
 */
export function getBom (params) {
  return request({
    url: `${apiMM}/product/getBom`,
    method: 'get',
    params
  })
}

/** 查询sku inventory
 * getInventory
 * @param {*} params { factoryCode: string, skuNos: string, warehouseLocation: string }
 */
export function getInventory (params) {
  return request({
    url: `${apiMM}/product/getInventory`,
    method: 'get',
    params
  })
}

/** 查询成本中心
 * listShippingAddress
 * @param {*} params { companyCode: string }
 */
export function getCostCenter (params) {
  return request({
    url: `${apiMM}/config/costCenter/get`,
    method: 'get',
    params
  })
}

/** 物料组查询
 * searchMaterialGroup
 * @param {*} params { groupName: string }
 */
export function searchMaterialGroup (groupName) {
  return request({
    url: `${apiMM}/config/materialGroup/search`,
    method: 'get',
    params: {
      groupName
    }
  })
}

/** 删除
 * delete po
 * @param {*} params { data: { orderNo, updateUser } }
 */
export function deletePO (data) {
  return rawRequest({
    url: `${apiMM}/po/delete`,
    method: 'post',
    data
  })
}

/** 强制删除
 * forceDeletePo
 * @param {*} params { data: { orderNo, updateUser } }
 */
export function forceDeletePo (data) {
  return rawRequest({
    url: `${apiMM}/po/forceDelete`,
    method: 'post',
    data
  })
}

/** 获取所有采购组
 * getAllPurchaseGroup
 * @param {*} params {}
 */
export function getAllPurchaseGroup () {
  return request({
    url: `${apiMM}/config/purchaseGroup/all`,
    method: 'get'
  })
}

/** 获取地址
 * getAllPurchaseGroup
 * @param {*} params {}
 */
export function listCommOptions (params) {
  return request({
    url: `${apiMM}/po/commInfo/listOptions`,
    method: 'get',
    params
  })
}

/** 获取默认地址
 * getAllPurchaseGroup
 * @param {*} params { factoryCode， purchaseGroupCode，supplierNo }
 */
export function getCommDefault (params) {
  return request({
    url: `${apiMM}/po/commInfo/getDefault`,
    method: 'get',
    params
  })
}
/** PO重推SAP
 * resendSap
 * @param {*} params { orderNo }
 */
export function resendSap (data) {
  return request({
    url: `${apiMM}/po/resendSap`,
    method: 'post',
    data
  })
}
/** PO重推消息
 * resendMQ
 * @param {*} params { orderNo }
 */
export function resendMQ (data) {
  return request({
    url: `${apiMM}/po/resendMQ`,
    method: 'post',
    data
  })
}
/** 审批
 * /po/approve
 * @param {*} params { approveRemark, approveUser, currentStep, isPass, orderNo }
 */
export function approve (data) {
  return rawRequest({
    url: `${apiMM}/po/approve`,
    method: 'post',
    data
  })
}
/** 获取so详情
 * /so/get
 * @param {*} params { soNo }
 */
export function getSO (params) {
  return request({
    url: `${apiMM}/so/get`,
    method: 'get',
    params
  })
}

/** 获取履约详情
 * /so/get
 * @param {*} getAgreement { poNo }
 */
export function getAgreement (params) {
  return request({
    url: `${apiMM}/po/performance`,
    method: 'get',
    params
  })
}

/** 获取物流详情
 * /so/get
 * @param {*} getLogistics { poNo }
 */
export function getLogistics (params) {
  return request({
    url: `${apiMM}/po/logistics`,
    method: 'get',
    params
  })
}

/** 获取物流轨迹
 * /so/get
 * @param {*} getLogisticsTrack { soNo }
 */
export function getLogisticsTrack (params) {
  return rawRequest({
    url: `${apiMM}/po/logisticsTrack`,
    method: 'get',
    params
  })
}
/** 获取TC物流轨迹
 * /so/get
 * @param {*} getTcLogisticsTrack { soNo }
 */
export function getTcLogisticsTrack (params) {
  return rawRequest({
    url: `${apiMM}/po/tcLogisticsTrack`,
    method: 'get',
    params
  })
}
/**
 * 取消TC中转
 * @param {*} data { any }
 * @return { Promise<any> }
 */
export function cancelTc (data) {
  return rawRequest({
    url: `${apiMM}/po/cancelTC?orderNo=${data.orderNo}&updateUser=${data.updateUser}`,
    method: 'post',
    data
  })
}
/**
 * 删除/恢复商品行
 * @param {*} data { any }
 * @param {*} action { recover | delete }
 * @return { Promise }
 */
export function deleteOrRecover (data, action) {
  return request({
    url: `${apiMM}/po/${action}`,
    method: 'put',
    data
  })
}
/**
 * 查询审批列表
 * @param {*} data { any }
 * @return { Promise<any> }
 */
export function getAuditList (data) {
  return request({
    url: `${apiMM}/po/approveList`,
    method: 'post',
    data
  })
}
/**
 * 订单审批
 * @param {*} data { any }
 * @return { Promise<any> }
 */
export function auditOrder (data) {
  return request({
    url: `${apiMM}/po/approve`,
    method: 'post',
    data
  })
}
/**
 * 内向交货单列表
 * @param {*} data { any }
 * @return { Promise }
 */
export function idoList (data) {
  return request({
    url: `${apiMM}/ido/page`,
    method: 'post',
    data
  })
}
export function idoList2 (data) {
  return request({
    url: `${apiMM}/ido/page2`,
    method: 'post',
    data
  })
}
/**
 * 内向交货单详情
 * @param {*} orderNo { any }
 * @return { Promise }
 */
export function idoDetail (orderNo) {
  return request({
    url: `${apiMM}/ido/get?orderNo=${orderNo}`,
    method: 'get'
  })
}
/**
 * 内向交货单删除
 * @param {*} params { any }
 * @return { Promise }
 */
export function idoDelete (params) {
  return rawRequest({
    url: `${apiMM}/ido/delete`,
    method: 'delete',
    params
  })
}
/**
 * 内向交货单创建初始
 * @param {*} data { any }
 * @return { Promise }
 */
export function idoEnter (data) {
  return request({
    url: `${apiMM}/ido/entrance`,
    method: 'post',
    data
  })
}
/**
 * 内向交货单创建
 * @param {*} data { any }
 * @return { Promise }
 */
export function idoCreate (data) {
  return rawRequest({
    url: `${apiMM}/ido/create`,
    method: 'post',
    data
  })
}
/**
 * 内向交货单更新
 * @param {*} data { any }
 * @return { Promise }
 */
export function idoUpdate (data) {
  return rawRequest({
    url: `${apiMM}/ido/update`,
    method: 'put',
    data
  })
}
/**
 * 内向交货单重推SAP
 * @param {*} data { any }
 * @return { Promise }
 */
export function resendIdoSap (data) {
  const formData = new FormData()
  formData.append('orderNo', data.orderNo)
  formData.append('updateUser', data.updateUser)
  return request({
    url: `${apiMM}/ido/resendSap`,
    method: 'post',
    data: formData
  })
}
/**
 * 内向交货单重推MQ
 * @param {*} data { any }
 * @return { Promise }
 */
export function resendIdoMQ (data) {
  const formData = new FormData()
  formData.append('orderNo', data.orderNo)
  formData.append('updateUser', data.updateUser)
  return request({
    url: `${apiMM}/ido/resendMQ`,
    method: 'post',
    data: formData
  })
}
/**
 * 内向交货单过账
 * @param {*} data { any }
 * @return { Promise }
 */
export function idoPost (data) {
  const formData = new FormData()
  formData.append('orderNo', data.orderNo)
  formData.append('updateUser', data.updateUser)
  return rawRequest({
    url: `${apiMM}/ido/post`,
    method: 'post',
    data: formData
  })
}
/**
 * 内向交货单取消过账
 * @param {*} data { any }
 * @return { Promise }
 */
export function idoCancelPost (data) {
  const formData = new FormData()
  formData.append('orderNo', data.orderNo)
  formData.append('updateUser', data.updateUser)
  return rawRequest({
    url: `${apiMM}/ido/cancelPost`,
    method: 'post',
    data: formData
  })
}
/**
 * 库存申请单震坤行地址查询
 * @param {*} data { any }
 * @return { Promise }
 */
export function getZkhContactOptions (params) {
  return request({
    url: `${apiMM}/config/dispatchAddress/list`,
    method: 'get',
    params
  })
}
/**
 * 库存dms获取库存申请单字段定义
 * @param {*} data { any }
 * @return { Promise }
 */
export function getInvFields (type) {
  return request({
    url: `${internalApi}/mm/getInvFields`,
    method: 'get',
    params: { type }
  })
}
/**
 * 库存申请单查询
 * @param {*} data { any }
 * @return { Promise }
 */
export function inventorySearchApi (data) {
  return request({
    url: `${apiMM}/iao/page`,
    method: 'post',
    data
  })
}
/**
 * 库存详情
 * @param {*} data { any }
 * @return { Promise }
 */
export function getInvDetail (params) {
  return request({
    url: `${apiMM}/iao/get`,
    method: 'get',
    params
  })
}
/**
 * 查询批次库存
 * @param {*} params { any }
 * @return { Promise }
 */
export function getBatchInventory (params) {
  return request({
    url: `${apiMM}/iao/getBatchInventory`,
    method: 'get',
    params
  })
}
/**
 * 批次自动分配
 * @param {*} params { any }
 * @return { Promise }
 */
export function autoBatchNo (data) {
  return rawRequest({
    url: `${apiMM}/iao/autoBatchNo`,
    method: 'post',
    data
  })
}
/**
 * 创建库存申请单
 * @param {*} data { any }
 * @return { Promise }
 */
export function createInv (data) {
  return rawRequest({
    url: `${apiMM}/iao/create`,
    method: 'post',
    data
  })
}
/**
 * 修改库存申请单
 * @param {*} data { any }
 * @return { Promise }
 */
export function updateInv (data) {
  return rawRequest({
    url: `${apiMM}/iao/update`,
    method: 'post',
    data
  })
}
/** 库存申请单重推SAP
 * resendSap
 * @param {*} params { orderNo }
 */
export function invResendSap (data) {
  return request({
    url: `${apiMM}/iao/resendSap`,
    method: 'post',
    data
  })
}
/** 库存申请单重推消息
 * resendMQ
 * @param {*} params { orderNo }
 */
export function invResendMQ (data) {
  return request({
    url: `${apiMM}/iao/resendMQ`,
    method: 'post',
    data
  })
}
/** 删除库存申请单
 * resendMQ
 * @param {*} params { orderNo }
 */
export function invOrderDelete (params) {
  return rawRequest({
    url: `${apiMM}/iao/delete`,
    method: 'delete',
    params
  })
}
/** 库存申请单物流轨迹查询
 * resendMQ
 * @param {*} params { iaoNo }
 */
export function getExpressDetail (params) {
  return request({
    url: `${apiMM}/iao/getLogisticsInfo`,
    method: 'get',
    params
  })
}
/** 库存申请单物过账
 * postAccount
 * @param {*} data [form-data] { orderNo, updateUser }
 */
export function postAccount (data) {
  return rawRequest({
    url: `${apiMM}/iao/post`,
    method: 'post',
    data
  })
}
/** 库存申请单物审批
 * postAccount
 * @param {*} data [form-data] { orderNo, updateUser }
 */
export function postAudit (data) {
  return rawRequest({
    url: `${apiMM}/iao/approve`,
    method: 'post',
    data
  })
}
/**
 * 关联采购单
 * @param {*} data { any }
 * @return { Promise }
 */
export function getRelatedPO (params) {
  return request({
    url: `${apiMM}/iao/getCreateVO`,
    method: 'get',
    params
  })
}
/**
 * 货权转移列表
 * @param {*} data { any }
 * @return { Promise }
 */
export function getCargoRightsList(data) {
  return request({
    url: `${apiMM}/cargoRight/pageList`,
    method: 'post',
    data
  })
}
/**
 * 批量/单个货权转移
 * @param {*} data { any }
 * @return { Promise }
 */

export function batchCargoRightsRemove(data) {
  return request({
    url: `${apiMM}/cargoRight/batchTransferCargo`,
    method: 'post',
    data
  })
}
/**
 * 批量/单个货权转移重试
 * @param {*} data { any }
 * @return { Promise }
 */

export function batchCargoRightsRemoveRetry(query, data) {
  var queryStr = ''
  if (Object.prototype.toString.call(query) === '[object Object]') {
    queryStr = qs.stringify(query)
  }
  if (Object.prototype.toString.call(query) === '[object Array]') {
    query.forEach((item) => {
      queryStr += `itemKeyList=${item}&`
    })
    queryStr += `createUser=${window.CUR_DATA.user.name}`
  }

  return request({
    url: `${apiMM}/cargoRight/retryBatchTransferCargo?${queryStr}`,
    method: 'post',
    data
  })
}
/**
 * 坤合仓查询接口
 * @param {*} data { any }
 * @return { Promise }
 */

export function getKhWarehouse(data) {
  return request({
    url: `${apiMM}/config/khWarehouse/list`,
    method: 'post',
    data
  })
}
/*
 * 库存申请单列表
 * @param {*} data { any }
 * @return { Promise }
 */
export function getInventoryReqList(data) {
  return request({
    url: `${apiMM}/api/v1/report/inventoryApplyReport`,
    method: 'post',
    data
  })
}

/*
 * 操作日志报表
 * @param {*} data { any }
 * @return { Promise }
 */
export function getOperateList(data) {
  return request({
    url: `${apiMM}/entityDiffLog/page`,
    method: 'post',
    data
  })
}
/*
 * 收货与发票
 * @param {*} data { any }
 * @return { Promise }
 */
export function getReceiptAndInvoiceList(data) {
  return request({
    url: `${apiMM}/po/deliveryAndReceipt`,
    method: 'post',
    data
  })
}
/*
 * 跟单报表查询
 * @param {*} data { any }
 * @return { Promise }
 */
export function getTrackingOrderList(data) {
  return request({
    url: `${apiMM}/api/v1/report/pagePoFollowReport`,
    method: 'post',
    data
  })
}
/*
 * 跟单报表导出
 * @param {*} data { any }
 * @return { Promise }
 */
export function exportTrackingOrderDetail(data) {
  return request({
    url: `${apiMM}/api/v1/report/exportPoFollowReport`,
    method: 'post',
    data
  })
}
/*
 * 跟单报表导出（异步）
 * @param {*} data { any }
 * @return { Promise }
 */
export function exportTrackingOrderDetailAsync(data) {
  return request({
    url: `${apiMM}/api/v1/report/exportPoFollowReportAsync`,
    method: 'post',
    data
  })
}
/*
 * 跟单报表批量修改
 * @param {*} data { any }
 * @return { Promise }
 */
export function batchEditTrackingOrder(data) {
  return request({
    url: `${apiMM}/api/v1/report/batchFollowPO`,
    method: 'post',
    data
  })
}

/*
 * 库存报表
 * @param {*} data { any }
 * @return { Promise }
 */
export function getInventoryApllyList(data) {
  return rawRequest({
    url: `${simApi}/inventory-report/query`,
    method: 'post',
    data
  })
}
/*
 * 库存类型
 * @param {*} query { any }
 * @return { Promise }
 */

export function getInventoryTypes(query) {
  return rawRequest({
    url: `${simApi}/all-inventory-type`,
    method: 'get',
    query
  })
}

/*
 * 库存报表导出 异步中转
 * @param {*} data { any }
 * @return { Promise }
 */

export function exportDullInventoryList(data) {
  return rawRequest({
    url: `${simApi}/inventory-export`,
    method: 'post',
    data
  })
}
/*
 * 下载任务查询
 * @param {*} query { any }
 * @return { Promise }
 */

export function getDownloadLists(data) {
  return rawRequest({
    url: `${simApi}/query-export-task`,
    method: 'post',
    data
  })
}

/*
 * todo 查询默认仓仓调拨时间
 * @param {*} query { any }
 *  任务记录查询
 * @param {*} data { any }
 * @return { Promise }
 */
export function getTransferTime(params) {
  return rawRequest({
    url: `${apiMM}/product/defaultWarehouseTransferTime/getDeliveryDate`,
    method: 'get',
    params
  })
}

/*
 *  任务记录查询
 * @param {*} data { any }
 * @return { Promise }
 */
export function getTaskRecordsList(data) {
  return request({
    url: `${apiMM}/api/v1/task/page`,
    method: 'post',
    data
  })
}

/*
 *  任务重试功能
 * @param {*} data { any }
 * @return { Promise }
 */
export function retryTask(data) {
  const formData = new FormData()
  formData.append('updateUser', data.updateUser)
  formData.append('taskId', data.taskId)
  console.log(formData);
  return rawRequest({
    url: `${apiMM}/api/v1/task/retryTask`,
    method: 'post',
    data: formData
  })
}
export function batchRetryTask(data) {
  return request({
    url: `${apiMM}/api/v1/task/batchRetryTask`,
    method: 'post',
    data
  })
}

/** 采购订单接口
 * 分页查询委外关单列表
 * @param {*} data { factoryCode: string, orderByField?: string, orderDirection?: string, orderNos?: Array[string], pageNo: integer, pageSize : integer, purchaseGroup?: string, skuNos?: Array[string], supplierNo?: string }
 */
export function pageOutSourceClosePo (data) {
  return request({
    url: `${apiMM}/po/pageOutSourceClose`,
    method: 'post',
    data
  })
}

/** 采购订单接口
 * 委外批量关单
 * @param {*} data { itemList: Array[object], operateUser: string }
 */
export function batchCloseOutSourcePo (data) {
  return request({
    url: `${apiMM}/po/batchCloseOutSource`,
    method: 'post',
    data
  })
}
/** 采购订单接口
 * 分页查询标准关单列表
 * @param {*} data { factoryCode: string, orderByField?: string, orderDirection?: string, orderNos?: Array[string], pageNo: integer, pageSize : integer, purchaseGroup?: string, skuNos?: Array[string], supplierNo?: string }
 */
export function pageClosePo (data) {
  return request({
    url: `${apiMM}/po/pageBatchClose`,
    method: 'post',
    data
  })
}

/** 采购订单接口
 * 标准批量关单
 * @param {*} data { itemList: Array[object], operateUser: string }
 */
export function batchClosePo (data) {
  return request({
    url: `${apiMM}/po/batchClose`,
    method: 'post',
    data
  })
}

/** 安全中心接口
 * 根据用户名获取账号信息
 * @param {*} params { username: string }
 */
export function getUserInfo (params) {
  return rawRequest({
    url: `${apiSECURITY}/accounts/info/username`,
    method: 'get',
    params
  })
}

/** 安全中心接口
 * 查询全量分部信息
 */
export function getCompanyInfo () {
  return rawRequest({
    url: `${apiSECURITY}/company`,
    method: 'get'
  })
}

// /** 委外链路列表
//  * @param {*} data {any }
//  */
export function getOutSourcingReportList (data) {
  return rawRequest({
    url: `${apiMM}/api/v1/report/pageOutSourcingReport`,
    method: 'post',
    data
  })
}
// /** 导出委外链路
//  * @param {*} data {any }
//  */
export function exportOutSourcingReportList (data) {
  return rawRequest({
    url: `${apiMM}/api/v1/report/exportOutSourcingReport`,
    method: 'post',
    data
  })
}
// 物料组下拉列表
export function getMaterialGroupList (data) {
  return rawRequest({
    url: `${apiMM}/config/materialGroup/search`,
    method: 'get',
    params: data
  })
}
// 可导出任务类型

export function getExportTaskTypeList (data) {
  return rawRequest({
    url: `${simApi}/export-task-type/list`,
    method: 'get',
    params: data
  })
}

// 查询采购订单的预付款
export function listAdvancePayment (params) {
  return request({
    url: `${apiMM}/po/listAdvancePayment`,
    method: 'get',
    params
  })
}

// 采购工作台查询
export function pagePOWorkbench (data) {
  return request({
    url: `${apiMM}/api/v1/report/pagePOWorkbench`,
    method: 'post',
    data
  })
}
// 采购全流程
export function getPurchaseProcessList (data) {
  return request({
    url: `${apiMM}/api/v1/report/pagePOLifeCycle`,
    method: 'post',
    data
  })
}
// 采购工作台导出
export function exportPOWorkbench (data) {
  return request({
    url: `${apiMM}/api/v1/report/exportPOWorkbench`,
    method: 'post',
    data
  })
}

export function exportPOLifeCycle (data) {
  return request({
    url: `${apiMM}/api/v1/report/exportPOLifeCycle`,
    method: 'post',
    data
  })
}
// 采购工作台工单回调
export function workOrderCallback (params) {
  return request({
    url: `${apiMM}/api/v1/report/workOrderCallback`,
    method: 'get',
    params
  })
}

export function batchEditPurchaseProcess (params) {
  return request({
    url: `${apiMM}/po/listAdvancePayment`,
    method: 'get',
    params
  })
}

// 获取加急备注
export function getUrgentNote (params) {
  return request({
    url: `${apiMM}/ido/getUrgentNote`,
    method: 'get',
    params
  })
}
// 模板中心打印送货单
export function deliveryPrint (params) {
  return rawRequest({
    url: `${omsNew}/bossmodel/get/model`,
    method: 'get',
    params
  })
}
// oa预审批
export function oaAdvancePayment (data) {
  return request({
    url: `${apiMM}/po/oaAdvancePayment`,
    method: 'post',
    data
  })
}

export function uploadFile(data) {
  return request({
    url: `${apiMM}/file/upload`,
    method: 'post',
    data
  })
}

// 采购工作台omstab 分页查询采购异常未下单
export function getPendingOrderList (params) {
  return rawRequest({
    url: `${apiMrp}/purchasePendingOrder/list`,
    method: 'get',
    params
  }).then(res => {
    if (res.code === 200) {
      return res.data
    } else {
      MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
    }
  }).catch(err => {
    MessageBox.alert((err && err.message) || '请求失败！', { type: 'error' })
  })
}
// 采购工作台omstab 保存关联工单单号
export function saveWorkOrderNo (data) {
  return rawRequest({
    url: `${apiMrp}/purchasePendingOrder/save/workOrderNo`,
    method: 'post',
    data
  }).then(res => {
    if (res.code === 200) {
      return res.data
    } else {
      MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
    }
  }).catch(err => {
    MessageBox.alert((err && err.message) || '请求失败！', { type: 'error' })
  })
}
// 采购工作台omstab 采购未下单原因保存
export function saveReason (data) {
  return rawRequest({
    url: `${apiMrp}/purchasePendingOrder/saveOrUpdate/reason`,
    method: 'post',
    data
  }).then(res => {
    if (res.code === 200) {
      return res.data
    } else {
      MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
    }
  }).catch(err => {
    MessageBox.alert((err && err.message) || '请求失败！', { type: 'error' })
  })
}
// 合同审批
export function oaContract (data) {
  return request({
    url: `${apiMM}/po/oaContract`,
    method: 'post',
    data
  })
}
// 电子文档接口
export function batchQueryDocs (data) {
  return rawRequest({
    url: `${eCorp}/v1/ecorp/batchQueryDocs `,
    method: 'post',
    data
  }).then(res => {
    if (res.code === 200) {
      return res.data
    } else {
      MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
    }
  }).catch(err => {
    MessageBox.alert((err && err.message) || '请求失败！', { type: 'error' })
  })
}
// 采购大盘获取订单概况
export function poOverview (data) {
  return request({
    url: `${apiMM}/api/v1/poStatistics/poOverview`,
    method: 'post',
    data
  })
}

// 采购大盘获取订单概况详情
export function poOverviewDetail (data) {
  return request({
    url: `${apiMM}/api/v1/poStatistics/poOverviewDetail`,
    method: 'post',
    data
  })
}
// 采购大盘获取预付款
export function advancePaymentDetail (data) {
  return request({
    url: `${apiMM}/api/v1/poStatistics/advancePaymentDetail`,
    method: 'post',
    data
  })
}
// 采购大盘跟单预警
export function statisticsWarning (data) {
  return request({
    url: `${apiMM}/api/v1/poStatistics/poStatisticsWarn`,
    method: 'get',
    data
  })
}

// 采购大盘跟单预警详情
export function statisticsWarningDetail (data) {
  return request({
    url: `${apiMM}/api/v1/poStatistics/poStatisticsWarnDetail`,
    method: 'post',
    data
  })
}
// 采购大盘po逾期
export function overdueDetail (data) {
  return request({
    url: `${apiMM}/api/v1/poStatistics/poOverdueDetail`,
    method: 'post',
    data
  })
}
// 获取退款金额
export function getRefundableAmountApi (data) {
  return request({
    url: `${apiMM}/product/return/refundableAmount`,
    method: 'post',
    data
  })
}
// 采购大盘获取待办数量
export function getPoTodo (data) {
  return request({
    url: `${apiMM}/api/v1/poStatistics/poTodo`,
    method: 'post',
    data
  })
}
// 采购大盘获取未下采购单待办数量
export function getPoPendingOrderTodo (data) {
  return rawRequest({
    url: `${apiMrp}/purchasePendingOrder/count`,
    method: 'post',
    data
  }).then(res => {
    if (res.code === 200) {
      return res.data
    } else {
      MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error' })
    }
  }).catch(err => {
    MessageBox.alert((err && err.message) || '请求失败！', { type: 'error' })
  })
}
/** sku 更换渠道
 * getProduct
 * @param {*} data { factoryCode: string, skuNos: string }
 */
 export function displaceSku (data) {
  return request({
    url: `${apiMM}/product/displaceSku`,
    method: 'post',
    data
  })
}
/** sku 添加sku
 * getProduct
 * @param {*} data { factoryCode: string, skuNos: string }
 */
 export function addDisplaceSku (data) {
  return rawRequest({
    url: `${apiMM}/product/addDisplaceSku`,
    method: 'post',
    data
  })
}
/** sku 更换渠道
 * getProduct
 * @param {*} data { factoryCode: string, skuNos: string }
 */
 export function replaceSupplier (data) {
  return rawRequest({
    url: `${apiMM}/api/v1/displaceSku/displace`,
    method: 'post',
    data
  })
}
/** sku 切换供应商审批
 * getProduct
 * @param {*} data { factoryCode: string, skuNos: string }
 */
 export function displaceApprove (data) {
  return rawRequest({
    url: `${apiMM}/api/v1/displaceSku/displaceApprove`,
    method: 'post',
    data
  })
}
// 获取sdi字典值
export function sdiDict (params) {
  return request({
    url: `${sdi}/api/v1/sdi/dict/listDict`,
    method: 'get',
    params
  })
}
/** 换渠道 佣金建议采购价
 * @param {*} data { factoryCode: string, skuNos: string }
 */
 export function displaceVpiPrice (data) {
  return rawRequest({
    url: `${apiMM}/api/v1/displaceSku/displaceVpiPrice`,
    method: 'post',
    data
  })
}
// 是否需要拆单
 export function needSplitOrder (data) {
  return rawRequest({
    url: `${apiMM}/po/needSplitOrder`,
    method: 'post',
    data
  })
}

// 获取采购订单头信息
export function getPOHead (params) {
  return request({
    url: `${apiMM}/po/getHead`,
    method: 'get',
    params
  })
}

// 获取返利券列表
export function getRebateList (params) {
  return request({
    url: `${apiMM}/rebate/getRebateList`,
    method: 'get',
    params
  })
}

// 一键分摊
export function assign (data) {
  return request({
    url: `${apiMM}/product/assign`,
    method: 'post',
    data: formatPurchaseData(data)
  })
}

// 根据商品行含税单价，税率，数量，价格倍数，计算 含税总额、未税总额、税额、未税单价
export function calcUnTaxedPrice (data) {
  return request({
    url: `${apiMM}/product/calcUnTaxedPrice`,
    method: 'post',
    data: formatPurchaseItemData(data)
  })
}

// 行上订单数量（itemQuantity）变化时重新计算部分价费
export function assignByQuantity (data, item, currentItemQuantity, itemQuantity) {
  return request({
    url: `${apiMM}/product/assignByQuantity`,
    method: 'post',
    data: {
      data: formatPurchaseData(data),
      row: formatPurchaseItemData(item),
      preItemQuantity: currentItemQuantity,
      curItemQuantity: itemQuantity
    }
  })
}

// 删除/恢复商品行时，头上 减/加 该行上已分摊的所有价费
export function assignToggle (data, items, status) {
  return request({
    url: `${apiMM}/product/assignToggle`,
    method: 'post',
    data: {
      data: formatPurchaseData(data),
      items: items.map(item => formatPurchaseItemData(item)),
      status
    }
  })
}

// 获取批量下载模板地址
export function getPoTemplateExcelUrls () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=po-batch-create-template',
    method: 'get'
  })
}

export function getAcceptFileType () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=boss-upload-accept-file-type',
    method: 'get'
  })
}

// 查询任务
export function queryEvent (data) {
  return rawRequest({
    url: `${apiMM}/po/queryEvent`,
    method: 'post',
    data
  })
}
// 协同信息确认
export function submitEvent (data) {
  return rawRequest({
    url: `${apiMM}/po/submitEvent`,
    method: 'post',
    data
  })
}
// 修改款期为KA01校验
export function queryPaymentTerm (data) {
  return rawRequest({
    url: `${apiMM}/po/queryPaymentTerm`,
    method: 'post',
    data
  })
}
// 修改款期为KA01提交
export function submitPaymentTerm (data) {
  return rawRequest({
    url: `${apiMM}/po/submitPaymentTerm`,
    method: 'post',
    data
  })
}

export function queryApproveStatus (data) {
  return request({
    url: `${apiMM}/po/queryApproveStatus`,
    method: 'post',
    data
  })
}
