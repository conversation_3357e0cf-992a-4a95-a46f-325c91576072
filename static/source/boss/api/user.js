import request from '@/utility/request'
import store from '@/store/index.js'

export function login (data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function getInfo () {
  return request({
    url: process.env.VUE_APP_SS_URL + '/accounts/me',
    method: 'get'
    // params: {
    //   token
    // }
  })
}
// export function getInfo(token) {
//   return request({
//     url: '/user/info',
//     method: 'get',
//     params: {
//       token
//     }
//   })
// }

// export function logout () {
//   const locationUrl = window.location.href
//   const encodeLocationUrl = encodeURIComponent(locationUrl)
//   window.location.href = process.env.VUE_APP_AUTH_URL + '/server/logout?redirectUri=' +
//     encodeURIComponent(process.env.VUE_APP_AUTH_URL + '/server/oauth/authorize?response_type=code&client_id=008888&redirect_uri=' +
//       encodeURIComponent(process.env.VUE_APP_CALLBACK_URL + '/token/callback&state=' + encodeLocationUrl))
// }

export function toLogout () {
  store.dispatch('user/logout')
}
// code换token
export function queryToken (data) {
  return request({
    url: 'https://auth.uat.zkh360.com/server/oauth/token',
    method: 'post',
    data,
    transformRequest: [
      data => {
        // 将数据转换为表单数据
        let ret = ''
        for (const it in data) {
          ret +=
            encodeURIComponent(it) + '=' + encodeURIComponent(data[it]) + '&'
        }
        return ret
      }
    ],
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
