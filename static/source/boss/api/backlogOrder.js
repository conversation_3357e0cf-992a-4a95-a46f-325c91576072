import request from '@/utility/request'

const prefix = '/oms-new/backlog'
const opcFront = '/oms-opc-front/backlog'
const apiBossOpc = '/api-boss-opc'
/** 未交订单查询 - start ***/
// 导出Excel
export function exportBacklogOrders (data) {
  return request({
    url: `${prefix}/export`,
    method: 'get',
    params: data
  })
}

// 未交订单查询列表
export function getBacklogOrders (data) {
  return request({
    url: `${opcFront}/get/orders`,
    method: 'post',
    data
  })
}

// 订单类型下拉列表
export function getOrderTypeOptions () {
  return request({
    url: `${opcFront}/get/order/new_types`,
    method: 'get'
  })
}
// 订单行类型下拉列表
export function getItemTypeOptions () {
  return request({
    url: `${opcFront}/get/order/item_types`,
    method: 'get'
  })
}
/** 客户商品快捷查询 - end ***/

export function fetchConfigFromServer () {
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=1`,
    method: 'get'
  })
}
export function updateRemark (data) {
  return request({
    url: `${opcFront}/update/remark`,
    method: 'put',
    data
  })
}

export function saveConfig2Server (data) {
  data = data.map((d, index) => {
    return {
      parentId: 1,
      ...d,
      orderNo: index + 1
    }
  })
  data[0].children.forEach((child, index) => {
    child.orderNo = index + 1
  })
  data[1].children.forEach((child, index) => {
    child.orderNo = index + 1
  })
  console.log(data)
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=1`,
    method: 'post',
    data
  })
}
