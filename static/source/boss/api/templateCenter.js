import request, { } from '@/utility/request'

// const prefix = '/oms-new/bossmodel'
const omsBase = '/oms-base/bossmodel'

// const prefix = 'https://oms-search-front-fat.zkh360.com/bossmodel'

/** 模板中心 - start ***/
// 新增模板
export function addModel (data) {
  return request({
    url: `${omsBase}/addBossModel`,
    method: 'post',
    data: data
  })
}
// 获取初始化模板
export function getInitModel (modelType) {
  const url = `${omsBase}/get/init/model?modelType=${modelType}`
  return request({
    url: url,
    method: 'get'
  })
}

// 模板详情
export function getModelDetail (data) {
  return request({
    url: `${omsBase}/detail`,
    method: 'get',
    params: data
  })
}

// 获取匹配字段信息
export function goodsBrandListSearch (data) {
  return request({
    url: `${omsBase}/get/model/columns`,
    method: 'get',
    params: data
  })
}
// 分页查询模板信息
export function getModels (data) {
  return request({
    url: `${omsBase}/get/models`,
    method: 'get',
    params: data
  })
}
// 模板编辑
export function editModel (data) {
  return request({
    url: `${omsBase}/modifyModel`,
    method: 'post',
    data: data
  })
}
// 切换模板状态
export function editModelStatus (data) {
  return request({
    url: `${omsBase}/modifyModelStatus`,
    method: 'post',
    data: data
  })
}

export function getModelSizeList () {
  return request({
    url: `${omsBase}/size/list`,
    method: 'get'
  })
}

export function getModelTypeList () {
  return request({
    url: `${omsBase}/type/list`,
    method: 'get'
  })
}

/** 模板中心 - end ***/

export const templateTypeStatusOptions = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '出货单',
    value: '0'
  },
  {
    label: 'vc直发送货单',
    value: '1'
  },
  // {
  //   label: 'vc拣货单',
  //   value: '2'
  // },
  // {
  //   label: 'vc直发送货&拣货单',
  //   value: '3'
  // },
  {
    label: '直发采购单',
    value: '4'
  },
  {
    label: '对账中心模板',
    value: '5'
  },
  {
    label: '坤合标签模板',
    value: '6'
  },
  {
    label: 'vc采购单未到货明细单',
    value: '7'
  },
  {
    label: '直发送货单标签',
    value: '8'
  },
  {
    label: '直发采购单标签',
    value: '9'
  }
]
