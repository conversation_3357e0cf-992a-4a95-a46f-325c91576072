import request, { downloadFile } from '@/utility/request'
// import { getLabelByValue } from '@/utils/index.js'

const prefix = '/api-boss'
const apiMdm = '/api-mdm'
const security = '/security-api'
const omsConfig = '/oms-config'
const gateway = '/api-cc-gateway'
// const apiBossSupplyChannel = '/api-boss-supply-channel'
const apiOMSSupplyChannel = '/api-oms-supply-channel'

// 查询客户仓位列表
export function getCustomerPositions(data) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel/headers`,
    method: 'get',
    params: data
  })
}

// 根据客户名称查询客户集合
export function getCustomerListByPartName(data) {
  return request({
    url: `${prefix}/optionSet/v1/findCrmCustomerList`,
    method: 'get',
    params: data
  })
}

export function searchClients(data) {
  return request({
    url: `${prefix}/mdm/customers`,
    params: { nameOrNumberLike: data }
  })
}

export function searchSales(data) {
  return request({
    url: `${apiMdm}/customerSalesForce`,
    params: { customerNumber: data }
  })
}

export function getContact(ids) {
  return request({
    url: `${apiMdm}/contact`,
    params: { contactId: ids }
  })
}

export function getAccountByName(name) {
  return request({
    url: `${security}/account/name`,
    params: {
      name
    }
  })
}

export function fetchDetailData(id) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel/${id}`
  })
}

export function fetchDetailLog(id) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel/log/${id}`
  })
}

// export function searchProductGroup() {
//   return request({
//     url: `${apiMdm}/optionset/findBatch?typeCodes=spart`
//   })
// }

export function searchProductGroup() {
  return request({
    url: `${gateway}/api/scc/v1/dict/typeCodes?typeCodes=spart`
  })
}

export function deleteHeaderRow(id) {
  return request({
    url: `${prefix}/one_stop/supply_channel/headers/${id}/delete`,
    method: 'put'
  })
}

export function approveCustomerPosition(id, data) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel/headers/${id}/approval`,
    method: 'put',
    data
  })
}

export function deleteCustomer(id, data) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel/${id}/delete`,
    method: 'put',
    data
  })
}

export function saveData(data) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel`,
    method: 'post',
    data
  })
}

export function deleteItemRow(id) {
  return request({
    url: `${prefix}/one_stop/supply_channel/items/${id}/delete`,
    method: 'put'
  })
}
// 获取指定渠道/发货仓明细列表
export function getDesignatedChanelAndWareHouse(params) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel/list_detail`,
    method: 'get',
    params
  })
}

// 导出指定渠道/发货仓明细列表
export function exportDesignatedChanelAndWareHouse(data) {
  return downloadFile(`${apiOMSSupplyChannel}/one_stop/supply_channel/list_detail/export`, data, { method: 'POST' })
}

export function getRuleCodeList(params) {
  return request({
    url: `${omsConfig}/sku-category-rule/ruleCodes/level`,
    method: 'get',
    params
  })
}
export function getwWarehouseCode(data) {
  return request({
    url: `${omsConfig}/supply-network/query/common`,
    method: 'post',
    data
  })
}

export function queryHistoryPrice(data) {
  return request({
    url: `${apiOMSSupplyChannel}/one_stop/supply_channel/sku/queryHistoryPrice`,
    method: 'post',
    data
  })
}
