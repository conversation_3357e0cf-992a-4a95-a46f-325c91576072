import request from '@/utility/request'

const dataCenter = '/data-center-front'
// const apiMdm = '/api-mdm'
const gateway = '/api-cc-gateway'

export function getForecastList (data) {
  return request({
    url: `${dataCenter}/price/forecast/list`,
    method: 'post',
    data
  })
}
export function markPrice (data) {
  return request({
    url: `${dataCenter}/price/forecast/mark/price/adjusted`,
    method: 'put',
    data
  })
}
export function matchPrice (data) {
  return request({
    url: `${dataCenter}/price/forecast/match`,
    method: 'put',
    data
  })
}
export function uploadExcel (data) {
  return request({
    url: `${dataCenter}/price/forecast/import/excel`,
    method: 'post',
    data
  })
}
export function downloadTempl () {
  return request({
    url: `${dataCenter}/price/forecast/template/download`,
    method: 'get'
  })
}
// export function getCatalogueApi (params) {
//   return request({
//     url: `${apiMdm}/manageCatalogues`,
//     method: 'get',
//     params
//   })
// }
export function getCatalogueApi (data) {
  return request({
    url: `${gateway}/api/scc/v1/catalog/page`,
    method: 'post',
    data: {
      ...data,
      pageSize: 100,
      pageNum: 1
    }
  })
}
export function getDropdownOptions (param) {
  return request({
    url: `${dataCenter}/price/forecast/${param}/dropdown`,
    method: 'get'
  })
}
// 折扣类型下拉
export function getDiscountTypeOptions () {
  return request({
    url: `${dataCenter}/price/forecast/discont/type/dropdown`,
    method: 'get'
  })
}
// 价格类型下拉
export function getPriceTypeOptions () {
  return request({
    url: `${dataCenter}/price/forecast/price/type/dropdown`,
    method: 'get'
  })
}

// return request({
//   method: 'post',
//   url: uploadPrefix,
//   data: formData,
//   headers: {
//     'Content-Type': 'multipart/form-data'
//   }
// })
