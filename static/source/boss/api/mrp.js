import rawRequest from '@/utility/request'
import { MessageBox } from 'element-ui'

const request = function () {
  return rawRequest(...arguments)
    .then(res => {
      // 约定code为200表示请求成功, data字段为返回数据, code不为200时显示异常, 展示message信息
      if (res.code === 200) {
        return res.data
      } else {
        if (res.msg) {
          res.msg = res.msg.replace(/;/g, '<br/>')
        }
        if (res.message) {
          res.msg = res.message.replace(/;/g, '<br/>')
        }
        MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error', dangerouslyUseHTMLString: true, customClass: 'mzindex' })
      }
    })
    .catch(err => {
      if (err.msg) {
        err.msg = err.msg.replace(/;/g, '<br/>')
      }
      if (err.message) {
        err.msg = err.message.replace(/;/g, '<br/>')
      }
      MessageBox.alert(err.msg || err.message || '请求失败！', { type: 'error', dangerouslyUseHTMLString: true, customClass: 'mzindex' })
    })
}

const apiMrp = '/api-mrp'
const apiPms = '/api-pms'
const apiSECURITY = '/security-api'
const apiOms = '/api-opc'
const apiPmsSystem = '/api-pms-system'
// const apiMdm = '/api-mdm'
const gateway = '/api-cc-gateway'

/** mrp区域接口
 * 分页查询mrp区域
 * @param {*} params { pageNo?: number, pageSize?: number, mrpArea?: string, factory?: string, position?: string }
 */
export function getMrpAreaList (params) {
  return request({
    url: `${apiMrp}/mrpArea/list`,
    method: 'get',
    params
  })
}

/** mrp区域接口
 * 创建mrp区域
 * @param {*} data { code: string, description: string, factory: string, isDefault: number, type: number,position?: string, id?: number, defaultPosition?: string }
 */
export function createMrpArea (data) {
  return rawRequest({
    url: `${apiMrp}/mrpArea/create`,
    method: 'post',
    data
  })
}

/** mrp区域接口
 * 校验有无默认仓库地点
 * @param {*} params { mrpArea: string, isDefault: 1 }
 */
export function verifyDefaultWarehouse (params) {
  return request({
    url: `${apiMrp}/mrpArea/list`,
    method: 'get',
    params
  })
}

/** mrp区域接口
 * 删除mrp区域
 * @param {*} data { id: number }
 */
export function deleteMrpArea (id) {
  return request({
    url: `${apiMrp}/mrpArea/delete/${id}`,
    method: 'post'
  })
}

/** mrp区域接口
 * 批量删除mrp区域
 * @param {*} data { idList: string }
 */
export function batchDeleteMrpArea (data) {
  return rawRequest({
    url: `${apiMrp}/mrpArea/batchDelete`,
    method: 'post',
    data
  })
}

/** mrp区域接口
 * 修改mrp区域
 * @param {*} data { code: string, description: string, factory: string, isDefault: number, type: number, position: string, id: number  }
 */
export function modifyMrpArea (data) {
  return request({
    url: `${apiMrp}/mrpArea/update`,
    method: 'post',
    data
  })
}

/** mrp区域接口
 * 批量导入mrp区域
 * @param {*} data [ { code: string, description: string, factory: string, isDefault?: number, type: number,position?: string, id?: number } ]
 */
export function batchUploadMrpArea (data) {
  return request({
    url: `${apiMrp}/mrpArea/batchImport`,
    method: 'post',
    data
  })
}

/** 白名单导入接口
 * 获取白名单 列表(分页)
 * @param {*} params { pageNo?: number, pageSize?: number, buyerCode?: string, buyerName?: string, factoryCode: string, mrpArea?: string, pmode?: number, providerName?: string, providerNo?: string }
 */
export function getWhiteList (params) {
  return request({
    url: `${apiMrp}/whiteListProvider`,
    method: 'get',
    params
  })
}

/** 白名单导入接口
 * 新增白名单信息
 * @param {*} data { companyCode: string, companyName: string, buyerCode: string, buyerName: string, factoryCode: string, factoryName: string, mrpArea?: string, pmode: number, providerName: string, providerNo: string }
 */
export function provideWhite (data) {
  return rawRequest({
    url: `${apiMrp}/whiteListProvider`,
    method: 'post',
    data
  })
}

/** 白名单导入接口
 * 批量删除白名单 信息
 * @param {*} params { ids?: string }
 */
export function deleteWhite (data) {
  return rawRequest({
    url: `${apiMrp}/whiteListProvider/delete`,
    method: 'post',
    data: {
      ids: data
    }
  })
}

/** 白名单导入接口
 * 获取白名单 列表 (导出接口)
 * @param {*} params { buyerCode?: string, buyerName?: string, factoryCode: string, mrpArea?: string, pmode?: number, providerName?: string, providerNo?: string }
 */
export function exportAll (params) {
  return request({
    url: `${apiMrp}/whiteListProvider/export`,
    method: 'get',
    params
  })
}

/** 白名单导入接口
 * 批量 新增/更新白名单信息
 * @param {*} data [ { companyCode: string, companyName: string, buyerCode: string, buyerName: string, factoryCode: string, factoryName: string, mrpArea?: string, pmode: number, providerName: string, providerNo: string } ]
 */
export function batchUploadWhite (data) {
  return rawRequest({
    url: `${apiMrp}/whiteListProvider`,
    method: 'put',
    data
  })
}

/** 白名单导入接口
 * 通过前缀模糊查询mrp区域
 * @param {*} params { mrpArea?: string }
 */
export function getMrpArea (params) {
  return request({
    url: `${apiMrp}/mrpArea/queryParam`,
    method: 'get',
    params
  })
}

/** 备货策略配置
 * 分页查询备货策略
 * @param {*} params { pageNo?: number, pageSize?: number, factory?: string, mrpArea?: string, skuNo?: string, position?: string, modifier?: string }
 */
export function getStockList (params) {
  return request({
    url: `${apiMrp}/safeStock/list`,
    method: 'get',
    params
  })
}

/** 备货策略配置
 * 创建备货策略
 * @param {*} params { factory?: string, mrpArea?: string, skuNo?: string, type?: string, safeStock?: number, packagingQuantity?: string, reOrderPoint?: string, maxStock?: string }
 */
export function createStock (data) {
  return rawRequest({
    url: `${apiMrp}/safeStock/create`,
    method: 'post',
    data
  })
}

/** 备货策略配置
 * 批量删除备货策略
 * @param {*} data { idList: string }
 */
export function deleteStock (data) {
  return rawRequest({
    url: `${apiMrp}/safeStock/batchDelete`,
    method: 'post',
    data
  })
}

/** 备货策略配置
 * 修改备货策略
 * @param {*} data { factory: string, mrpArea: string, skuNo: string, type: string, safeStock: number, packagingQuantity: string, reOrderPoint: string, maxStock: string }
 */
export function updateStock (data) {
  return rawRequest({
    url: `${apiMrp}/safeStock/update`,
    method: 'post',
    data
  })
}

/** 备货策略配置
 * 批量导入备货策略
 * @param {*} data [ { factory: string, mrpArea: string, skuNo: string, type: string, safeStock: number, packagingQuantity: string, reOrderPoint: string, maxStock: string } ]
 */
export function batchUploadStock (data) {
  return rawRequest({
    url: `${apiMrp}/safeStock/batchImport`,
    method: 'post',
    data
  })
}

/** 物料需求报表-汇总
 * 分页查询采购申请
 * @param {*} params { pageNo?: number, pageSize?: number, company: string, factory: string, purchaseGroup: string, brand?: string, mrpArea?: string, productGroup?: string, skuNo?: string, startDate?: string, endDate?: string }
 */
export function getPrList (params) {
  return request({
    url: `${apiMrp}/pr/list`,
    method: 'get',
    params
  })
}

/** 物料需求报表-汇总
 * 模糊查询品牌
 * @param {*} params { brandName: string }
 */
export function getBrand (params) {
  return request({
    url: `${apiMrp}/systemConfig/brand/list`,
    method: 'get',
    params
  })
}

/** 查询所有物料组 */
export function productGroupList () {
  return request({
    url: `${apiMrp}/systemConfig/productGroup/list`,
    method: 'get'
  })
}

/** 物料需求报表-转单
 * 手动转单
 * @param {*} data [ { itemType?:string, quantity?:number, shipWarehousePositon?:string, supplier?:string, prIds?:[] } ]
 */
export function transfer2Po (data) {
  return request({
    url: `${apiMrp}/pr/transfer2Po`,
    method: 'post',
    data
  })
}

export function queryMakeUpOrder(data) {
  return request({
    url: `${apiMrp}/pr/queryMakeUpOrder`,
    method: 'post',
    data
  })
}
/** 物料需求报表-转单
 * 查询供应商详情
 * @param {*} params { providerNo: string, skuNo: string, factory?: string }
 */
export function getSupplier (params) {
  return rawRequest({
    url: `${apiMrp}/systemConfig/provider/detail`,
    method: 'get',
    params
  })
}

/** 物料需求报表-转单
 * 查询供应商详情 (至少有一个查询条件)
 * @param {*} params { activationCode?: string, supplierName?: string, supplierNo?: string, withFields?: string }
 */
export function getSupplierName (supplierNo) {
  return rawRequest({
    url: `${apiPms}/api/v1/supplier/get`,
    method: 'get',
    params: {
      supplierNo
    }
  })
}

/** 物料需求报表-直发
 * 分页查询直发采购申请
 * @param {*} params { pageNo?: number, pageSize?: number, company: string, factory: string, purchaseGroup?: string, productGroup?: string,purchaseGroup?: string, skuNo?: string, startDate?: string, endDate?: string }
 */
export function getPrDirectList (params) {
  return request({
    url: `${apiMrp}/pr/list4DirectDelivery`,
    method: 'get',
    params
  })
}

/** 手动触发自动下单
 * 自动采购下单页面
 * @param {*} data { company?: string, factory?: string, position?: string, purchaseGroup?: string, skuNo?: string, supplier?: string }
 */
export function triggerOrder (data) {
  return rawRequest({
    url: `${apiMrp}/pr/config2po`,
    method: 'post',
    data
  })
}
/** 物料需求报表-明细
 * 查询明细报表
 * @param {*} params { pageNo?: number, pageSize?: number, companyCode: string, factory: string, purchaseGroup: string, brand?: string,productGroup?: string, mrpArea?: string, skuNo?:string,startDate?: string, endDate?: string }
 */
export function getPrDetail(params) {
  return request({
    url: `${apiMrp}/purchaseRequestDetail`,
    method: 'get',
    params
  })
}

/** MRP运算结果
 * 计算结果控制器
 * @param {*} params { pageNo?: number, pageSize?: number, factory: string, skuNo: string, mrpArea?: string }
 */
export function getmrpResult(params) {
  return request({
    url: `${apiMrp}/mrpResult`,
    method: 'get',
    params
  })
}

/** 直发PR运算结果
 * 计算结果控制器
 * @param {*} params {  soNo: string, purchaseGroup?: string, skuNo?: string, soItemNo?: string }
 */
export function getPrResult(params) {
  return request({
    url: `${apiMrp}/pr/directPr`,
    method: 'get',
    params
  })
}

/** MRP运算结果
 * 运行实时mrp
 * @param {*} params { factory: string, skuNo: string, mrpArea?: string, requestAt: number }
 */
export function runRtmrp (data) {
  return request({
    url: `${apiMrp}/rtmrp/run`,
    method: 'post',
    data
  })
}

/** 直发PR运算结果
 * 运行实时mrp
 * @param {*} params soNos
 */
export function runPr (data) {
  return request({
    url: `${apiMrp}/rtmrp/runDirect`,
    method: 'post',
    data
  })
}

/** 安全中心接口
 * 根据用户名获取账号信息
 * @param {*} params { username: string }
 */
export function getUserInfo (params) {
  return rawRequest({
    url: `${apiSECURITY}/accounts/info/username`,
    method: 'get',
    params
  })
}

/** 安全中心接口
 * 查询全量分部信息
 */
export function getCompanyInfo () {
  return rawRequest({
    url: `${apiSECURITY}/company`,
    method: 'get'
  })
}

/** 安全中心接口
 * 根据域账号查询组织架构信息
 * @param {*} params { username: string }
 */
export function getUserDepartmentInfo (params) {
  return rawRequest({
    url: `${apiSECURITY}/department/info/username`,
    method: 'get',
    params
  })
}

/** 获取客户详情
 * @param {*} params { customerNumber: string, distributionChannel: string, productGroup: string, salesOrganization: string }
 */
export function getCustomerInfo (params) {
  return rawRequest({
    url: `${apiOms}/v1/customer/get`,
    method: 'get',
    params
  })
}

/** 震坤行采购中台服务
 * 根据域账号查询采购组配置
 * @param {*} params { securityUsernameList: string }
 */
export function getPurchaseGroup (params) {
  return rawRequest({
    url: `${apiPmsSystem}/api/v1/config/purchaseGroup/list`,
    method: 'get',
    params
  })
}
export function getDictionary (params) {
  return rawRequest({
    url: `${apiPmsSystem}/api/v1/config/dictionary/list`,
    method: 'get',
    params
  })
}

/** 震坤行采购中台服务
 * 获取下级采购组信息
 * @param {*} params { email: string }
 */
export function getChildrenPurchaseGroup (params) {
  return rawRequest({
    url: `${apiPmsSystem}/api/v1/config/purchaseGroup/children`,
    method: 'get',
    params
  })
}

/** 商品采购价格
 * 供应商与SKU关联关系
 * @param {*} params { vkbur: string, skuNo: string, providerNo?: string }
 */
// export function getSkuSupplier (params) {
//   return rawRequest({
//     url: `${apiMdm}/purchaseprice/providerSkuRelation`,
//     method: 'get',
//     params
//   })
// }
export function getSkuSupplier (data) {
  return rawRequest({
    url: `${gateway}/api/pp/v1/purchasePrice/page`,
    method: 'post',
    data
  })
}

/**
 * 可用库存查询
 *
 */
export function getAvailableStockList (params) {
  return request({
    url: `${apiMrp}/pr/availableStock/list`,
    method: 'get',
    params
  })
}

// ACM配置中心
export function getSpecifyColumn () {
  return rawRequest({
    url: '/api-acm-config?methodType=getConfig&id=mrp.uat.config',
    method: 'get'
  })
}

// 采购阶梯价
export function queryLadderPrice (data) {
  return rawRequest({
    url: `${apiMrp}/systemConfig/ladder/price`,
    method: 'post',
    data
  })
}

// 获取毛利率
export function queryGrossMargin (data) {
  return rawRequest({
    url: `${apiMrp}/pr/queryGrossMargin`,
    method: 'post',
    data
  })
}

export function getPurchaseCalendar (data) {
  return rawRequest({
    url: `${apiMrp}/purchaseCalender/purchaseCalender`,
    method: 'post',
    data
  })
}
// 查询是否需要拆单
export function needSplitOrder (data) {
  return rawRequest({
    url: `${apiMrp}/pr2/splitOrder`,
    method: 'post',
    data
  })
}

// 低负毛利
// 列表查询
export function getLowMraginList (data) {
  return rawRequest({
    url: `${apiMrp}/grossMargin/queryList`,
    method: 'post',
    data
  })
}
// 批量新增
export function addLowMragin (data) {
  return rawRequest({
    url: `${apiMrp}/grossMargin/insertBatch`,
    method: 'post',
    data
  })
}
// 批量修改
export function updateLowMragin (data) {
  return rawRequest({
    url: `${apiMrp}/grossMargin/updateBatch`,
    method: 'post',
    data
  })
}
// 批量删除
export function deleteLowMragin (data) {
  return rawRequest({
    url: `${apiMrp}/grossMargin/deleteBatch`,
    method: 'post',
    data
  })
}
// 根据ID查询低负毛利率
export function getLowMragin (ids) {
  return rawRequest({
    url: `${apiMrp}/grossMargin/queryByIds?ids=${ids}`,
    method: 'get'
  })
}
