import request from '@/utility/request'

const apiBoss = '/api-boss'
export function getAnnouncement () {
  return request({
    url: `${apiBoss}/publish/record`,
    method: 'get'
  })
}
export function saveAnnouncement (data) {
  return request({
    url: `${apiBoss}/publish/record`,
    method: 'put',
    data
  })
}
export function confirmAnnouncement () {
  return request({
    url: `${apiBoss}/publish/record/confirm`,
    method: 'get'
  })
}
export function ifShowAnnouncement () {
  return request({
    url: `${apiBoss}/publish/record/show`,
    method: 'get'
  })
}
