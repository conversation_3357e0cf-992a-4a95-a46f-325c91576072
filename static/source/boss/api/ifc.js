import request from '@/utility/request'

const isPre = window.location.href.includes('performanceProcessTool') && window.location.search.includes('ifcpre')
const apiIfc = isPre ? '/api-ifc-pre' : '/api-ifc'
// const apiMdm = '/api-mdm'
const gateway = '/api-cc-gateway'
/**
 * @description 客户订单信息
 */
export function orderInfo (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/orderInfo`,
    method: 'get',
    params
  })
}

/**
 * @description 仓储供应网络
 */
export function getWarehouseNetwork (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/splitOrderInfo`,
    method: 'get',
    params
  })
}

/**
 * @description 获取最大供应矩阵
 */
export function globalSupplyMatrixInfo (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/globalSupplyMatrixInfo`,
    method: 'get',
    params
  })
}

/**
 * @description 获取可用量矩阵
 */
export function availableSupplyMatrixInfo (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/availableSupplyMatrixInfo`,
    method: 'get',
    params
  })
}

/**
 * @description 获取ai挑仓结果
 */
export function aiSelectSupplyMatrixInfo (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/aiSupplyMatrixInfo`,
    method: 'get',
    params
  })
}

/**
 * @description 获取库位信息
 */
export function positionInventoryInfo (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/positionInventoryInfo`,
    method: 'get',
    params
  })
}

/**
 * @description 最终调仓结果
 */
export function selectResultInfo (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/selectResultInfo`,
    method: 'get',
    params
  })
}

/**
 * @description mdm商品数据
 */
// export function mdmProductList (skuNo) {
//   return request({
//     url: `${apiMdm}/products/skuNo/${skuNo}`,
//     method: 'get'
//   })
// }
export function mdmProductList (params) {
  return request({
    url: `${gateway}/api/cc/v1/sku/detail`,
    method: 'get',
    params
  })
}

/**
 * @description 白名单
 */
export function tabWhiteList (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/whiteList`,
    method: 'get',
    params
  })
}

/**
 * @description 仓储供应网络
 */
export function getWarehouseVersions (params) {
  return request({
    url: `${apiIfc}/routePlanVisualization/versions`,
    method: 'get',
    params
  })
}
