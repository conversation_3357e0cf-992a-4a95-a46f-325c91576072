import request from '@/utility/request'
const omsConfig = '/oms-config';
const prefix = '/api-sim';
const standard = '/api-cc-standard'

// 获取全部库存地点
export function getAllPosition(params) {
  return request({
    url: `${omsConfig}/warehouse-base/position/getAll`,
    method: 'get',
    params
  })
}
// 获取商品来源
export function getSourceType(params) {
  return request({
    url: `${standard}/optionsets/typeCodes?typeCodes=CommoditySource`,
    method: 'get',
    params
  })
}
// 列表
export function queryList(data) {
  return request({
    url: `${prefix}/off-price/queryPage`,
    method: 'post',
    data
  })
}
// 删除
export function deleteList(data) {
  return request({
    url: `${prefix}/off-price/delete`,
    method: 'post',
    data
  })
}
// 导出
export function exportList(data) {
  return request({
    url: `${prefix}/off-price/export`,
    method: 'post',
    data
  })
}
// 导入
export function importList(data) {
  return request({
    url: `${prefix}/off-price/import`,
    method: 'post',
    data
  })
}
