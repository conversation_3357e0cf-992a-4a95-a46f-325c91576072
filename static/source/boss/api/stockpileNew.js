import request from '@/utility/request';
const prefix = '/api-ab';
const omsConfig = '/oms-config';
// 模型备货
// 列表
export const getStockModeList = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/query`,
    method: 'post',
    data
  });
};
// 新增
export const addStockMode = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/add`,
    method: 'post',
    data
  });
};
// 修改
export const updateStockMode = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/update`,
    method: 'post',
    data
  });
};
// 删除
export const deleteStockMode = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/delete`,
    method: 'post',
    data
  });
};
// 试算
export const previewStockModeDetail = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/preview`,
    method: 'post',
    data
  });
};
// 试算转审核
export const convert2CheckStock = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/convert2Check`,
    method: 'post',
    data
  });
};
// 导出
export const exportStockMode = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/export`,
    method: 'post',
    data
  });
};
// 导入
export const importStockMode = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/import`,
    method: 'post',
    data
  });
};
// 查询商家进三个月有货率
export const queryBiLast3MonthSupplierStockRate = (params) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/queryBiLast3MonthSupplierStockRate`,
    method: 'get',
    params
  });
};
// 查询是否有库存
export const querySupplierStock = (params) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/querySupplierStock`,
    method: 'get',
    params
  });
};

// 模型审核
// 列表
export const getStockApprovalList = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/query`,
    method: 'post',
    data
  });
};
// 新增
export const addStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/add`,
    method: 'post',
    data
  });
};
// 修改
export const updateStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/update`,
    method: 'post',
    data
  });
};
// 关闭
export const closeStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/shutdown`,
    method: 'post',
    data
  });
};
// 删除
export const deleteStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/delete`,
    method: 'post',
    data
  });
};
// 导出
export const exportStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/export`,
    method: 'post',
    data
  });
};
// 导入
export const importStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/import`,
    method: 'post',
    data
  });
};
// 审核
export const checkStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/check`,
    method: 'post',
    data
  });
};
// 提交
export const submitStockApproval = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/commit`,
    method: 'post',
    data
  });
};
// 驳回
export const rejectStockApproval = (idSet, reason) => {
  const data = {
    idSet,
    reason
  };
  return request({
    url: `${prefix}/inventoryStrategyCheck/reject`,
    method: 'post',
    data
  });
};
// 近三月预测准确率
export const previewStockApprovalDetail = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/preview`,
    method: 'post',
    data
  });
};
// 不备货原因枚举
export const getNoStockReasonEnum = () => {
  return request({
    url: `${prefix}/inventoryStrategyCheck/notStockingReasonEnum`,
    method: 'get'
  });
};

// 公共下拉框筛选接口
// 获取工厂list
export function getAllFactory(data) {
  return request({
    url: `${omsConfig}/factory-base/query/all`,
    method: 'post',
    data
  })
}
// 根据sku factoryCode获取仓库编码
export function getWarehouseCode (data) {
  return request({
    url: `${omsConfig}/supply-network/query/common`,
    method: 'post',
    data
  })
}
// 获取全部仓网
export function getAllRuleCode() {
  return request({
    url: `${omsConfig}/sku-category-rule/ruleCodes/level?level=1`,
    method: 'get'
  })
}
// 获取全部上级仓
export function getAllParentWarehouseCode() {
  return request({
    url: `${omsConfig}/warehouse-base/query/list`,
    method: 'post',
    data: {
      page: 1,
      pageSize: 1000
    }
  })
}
// 模型备货配置
// 列表
export const getStockConfigList = (data) => {
  return request({
    url: `${prefix}/skuInventoryStrategy/queryList`,
    method: 'post',
    data
  });
};
// 获取未配置sku数量
export const getUnConfigSkuNum = () => {
  return request({
    url: `${prefix}/skuInventoryStrategy/leftNum`,
    method: 'post'
  });
}
// 更新行
export const updateStockConfigItem = (data) => {
  return request({
    url: `${prefix}/skuInventoryStrategy/save`,
    method: 'post',
    data
  });
};
// 导出
export const exportStockConfig = (data) => {
  return request({
    url: `${prefix}/skuInventoryStrategy/export`,
    method: 'post',
    data
  });
};
// 导入
export const importStockConfig = (data) => {
  return request({
    url: `${prefix}/skuInventoryStrategy/importExcel`,
    method: 'post',
    data
  });
};
// 查询试算策略列表
export const getCalStrategyList = (data) => {
  return request({
    url: `${prefix}/skuInventoryStrategy/calculateResult`,
    method: 'post',
    data
  });
};
// 提交策略
export const submitCalStrategy = (data) => {
  return request({
    url: `${prefix}/skuInventoryStrategy/confirmStrategy`,
    method: 'post',
    data
  });
};
// 查询级库存配置
export const getStockLevel = (data) => {
  return request({
    url: `${prefix}/inventoryStrategyConfig/queryLevelStock?factory=${data.factory}&warehouseCode=${data.warehouseCode}&sku=${data.sku}`,
    method: 'post'
  });
};
