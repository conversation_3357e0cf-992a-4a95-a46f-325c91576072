import request, { downloadFile } from '@/utility/request'

const prefix = '/data-center-front'
// const apiMdm = '/api-mdm'
const gateway = '/api-cc-gateway'

// 是否包规一致下拉框查询
export function getPackageSpec() {
  return request({
    url: `${prefix}/similarity/package/spec/dropdown`,
    method: 'get'
  })
}
// 产品定位下拉框查询
export function getProductPosition() {
  return request({
    url: `${prefix}/similarity/product/position/dropdown`,
    method: 'get'
  })
}
// 物料相似审核状态下拉框查询
export function getStatus() {
  return request({
    url: `${prefix}/similarity/status/dropdown`,
    method: 'get'
  })
}
// 第三方网站下拉框查询
export function getWebsite() {
  return request({
    url: `${prefix}/similarity/website/dropdown`,
    method: 'get'
  })
}
// 废弃原因下拉框查询
export function getAbandonReason() {
  return request({
    url: `${prefix}/similarity/abandon/reason/dropdown`,
    method: 'get'
  })
}
// 品牌下拉框查询
// export function getBrandListApi(data) {
//   return request({
//     url: `${apiMdm}/brands`,
//     method: 'get',
//     data
//   })
// }
export function getBrandListApi(params) {
  return request({
    url: `${gateway}/api/scc/v1/brand/page`,
    method: 'get',
    params
  })
}
// 列表查询
export function getList(data) {
  return request({
    url: `${prefix}/similarity/list`,
    method: 'post',
    data
  })
}
// 新增保存
export function addBatch(data) {
  return request({
    url: `${prefix}/similarity/add/batch`,
    method: 'put',
    data
  })
}
// 导出
export function exportList(data) {
  return downloadFile(`${prefix}/similarity/export`, data, '', 'post')
}
// 修改确认
export function modify(id, data) {
  return request({
    url: `${prefix}/similarity/modify/${id}`,
    method: 'put',
    data
  })
}
// 审核通过
export function confirm(id) {
  return request({
    url: `${prefix}/similarity/confirm/${id}`,
    method: 'put'
  })
}
// 废弃确认
export function abandon(id, data) {
  return request({
    url: `${prefix}/similarity/abandon/${id}`,
    method: 'put',
    data
  })
}
