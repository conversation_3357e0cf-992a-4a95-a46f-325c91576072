import request, { downloadFile } from '@/utility/request'

const prefix = '/data-center-front'

// 品牌阶梯查询
export function getTechValues (data) {
  return request({
    url: `${prefix}/price/added/value/list`,
    method: 'post',
    data
  })
}

export function getGroupData() {
  return request({
    url: `${prefix}/price/added/value/group`,
    method: 'get'
  })
}

export function getValueOptions () {
  return request({
    url: `${prefix}/price/added/value/dropdown`,
    method: 'get'
  })
}

export function updateAddedValue (id, data) {
  return request({
    url: `${prefix}/price/added/value/${id}`,
    method: 'put',
    data
  })
}

export function exportBasisApi(params) {
  return downloadFile(`${prefix}/price/added/value/export`, null, {
    ignoreEmpty: true,
    params
  })
}
