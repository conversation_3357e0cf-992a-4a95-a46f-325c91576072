import request from '@/utility/request'

/**
 * 交付画像接口
 * **/
const prefix = '/api-customerDelivery'

// [0] 获取角色权限列表
export function getRolePowers (data) {
  return request({
    url: `${prefix}/query/authority_manage`,
    method: 'post',
    data: data
  })
}
// [1] 获取工厂列表
export function getFactory (data) {
  return request({
    url: `${prefix}/query/factory`,
    method: 'post',
    data: data
  })
}
// [2] 添加工厂
export function createFactory (data) {
  return request({
    url: `${prefix}/eventsource/factory/create-factory`,
    method: 'post',
    data: data
  })
}
// [3] 修改工厂
export function updateFactory (data) {
  return request({
    url: `${prefix}/eventsource/factory/update-factory`,
    method: 'put',
    data: data
  })
}
// [4] 删除工厂
export function deleteFactory (data) {
  return request({
    url: `${prefix}/eventsource/factory/delete-factory`,
    method: 'post',
    data: data
  })
}
// [5] 获取工厂详情
export function getFactoryDetail (factoryId) {
  return request({
    url: `${prefix}/eventsource/factory/${factoryId}`,
    method: 'get'
  })
}

// [1] 获取权限管理列表
export function getAuthorityManage (data) {
  return request({
    url: `${prefix}/query/authority_manage`,
    method: 'post',
    data: data
  })
}
// [2] 添加权限管理
export function createAuthorityManage (data) {
  return request({
    url: `${prefix}/eventsource/authority_manage/create-authority_manage`,
    method: 'post',
    data: data
  })
}
// [3] 修改权限管理
export function updateAuthorityManage (data) {
  return request({
    url: `${prefix}/eventsource/authority_manage/update-authority_manage`,
    method: 'put',
    data: data
  })
}
// [4] 删除权限管理
export function deleteAuthorityManage (data) {
  return request({
    url: `${prefix}/eventsource/authority_manage/delete-authority_manage`,
    method: 'post',
    data: data
  })
}
// [5] 获取权限管理详情
export function getAuthorityManageDetail (id) {
  return request({
    url: `${prefix}/eventsource/authority_manage/${id}`,
    method: 'get'
  })
}

// [1] 获取服务中心负责区域列表
export function getServiceCenterArea (data) {
  return request({
    url: `${prefix}/query/service_center_area`,
    method: 'post',
    data: data
  })
}
// [2] 添加服务中心负责区域
export function createServiceCenterArea (data) {
  return request({
    url: `${prefix}/eventsource/service_center_area/create-service_center_area`,
    method: 'post',
    data: data
  })
}
// [3] 修改服务中心负责区域
export function updateServiceCenterArea (data) {
  return request({
    url: `${prefix}/eventsource/service_center_area/update-service_center_area`,
    method: 'put',
    data: data
  })
}
// [4] 删除服务中心负责区域
export function deleteServiceCenterArea (data) {
  return request({
    url: `${prefix}/eventsource/service_center_area/delete-service_center_area`,
    method: 'post',
    data: data
  })
}

// [1] 获取交付联系人列表
export function getTerminalDeliveryKeyContacts (data) {
  return request({
    url: `${prefix}/query/terminal_delivery_key_contacts`,
    method: 'post',
    data: data
  })
}

// [2] 添加交付联系人
export function createTerminalDeliveryKeyContacts (data) {
  console.log('开始调接口')
  console.log(data)
  return request({
    url: `${prefix}/eventsource/factory/create-factory-terminal_delivery_key_contacts`,
    method: 'post',
    data: data
  })
}

// [3] 修改交付联系人
export function updateTerminalDeliveryKeyContacts (data) {
  return request({
    url: `${prefix}/eventsource/factory/update-factory-terminal_delivery_key_contacts`,
    method: 'put',
    data: data
  })
}

// [4] 删除交付联系人
export function deleteTerminalDeliveryKeyContacts (data) {
  return request({
    url: `${prefix}/eventsource/factory/delete-factory-terminal_delivery_key_contacts`,
    method: 'post',
    data: data
  })
}

// [1] 获取上传失败列表
export function getUploadList (data) {
  return request({
    url: `${prefix}/batchRecord`,
    method: 'get',
    params: data
  })
}

// [1] 获取工厂修改日志列表
export function getDiffRecordList (data) {
  return request({
    url: `${prefix}/diffRecord/pagelist`,
    method: 'post',
    data: data
  })
}

// 服务中心负责区域数据-全量导出
export function exportServiceCenterAreaList (data) {
  return request({
    url: `${prefix}/serviceCenterArea/export`,
    method: 'get',
    params: data
  })
}

// 批量导出工厂基本信息
export function exportFactoryBaseInfo (data) {
  return request({
    url: `${prefix}/eventsource/dataExport/factory/export-factory-baseInfo-batch`,
    method: 'post',
    data: data
  })
}

// 批量导出工厂交货点信息
export function exportFactoryDeliveryPoints (data) {
  return request({
    url: `${prefix}/eventsource/dataExport/factory/export-factory-factory_delivery_requirement-delivery_points-batch`,
    method: 'post',
    data: data
  })
}

// 批量导出交付主管信息
export function exportFactoryDeliverySupervisor (data) {
  return request({
    url: `${prefix}/eventsource/dataExport/factory/export-factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-delivery_supervisor_contacts-batch`,
    method: 'post',
    data: data
  })
}

// 批量导出关键联系人
export function exportFactoryDeliveryKeyContacts (data) {
  return request({
    url: `${prefix}/eventsource/dataExport/factory/export-factory-terminal_delivery_key_contacts-batch`,
    method: 'post',
    data: data
  })
}

// 批量导出工厂全部信息
export function exportFactoryInfo (data) {
  return request({
    url: `${prefix}/eventsource/dataExport/factory/export-factory-batch`,
    method: 'post',
    data: data
  })
}

/**
 * 黑洞上面的接口
 * */
const prefixBlackHole = '/api-blackHole'

// 【1】获取枚举列表
export function getPropertyEnum (data) {
  return request({
    url: `${prefixBlackHole}/propertyEnum/map`,
    method: 'get',
    params: data
  })
}

// 【2】获取聚合下面的属性列表
export function getAggregationProperty (data) {
  return request({
    url: `${prefixBlackHole}/property`,
    method: 'get',
    params: data
  })
}

/**
 * 省市区查询
 * nameByEqual 名称
 * nameByLike  模糊名称
 * level 等级(1省/直辖市,2地级市,3区县,4镇/街道)
 * parentCode 父级地区编码
 * **/
const prefixProvince = '/api-area'
export function getArea (data) {
  return request({
    url: `${prefixProvince}/v2/area`,
    method: 'get',
    params: data
  })
}

/**
 * 安全中心接口
 * */
const prefixSecurtity = '/api-securtity'

// 【1】通过name获取安全中心用户列表
export function getAccountsByName (data) {
  return request({
    url: `${prefixSecurtity}/account/name`,
    method: 'get',
    params: data
  })
}

// 【2】获取所有上级信息
export function getAccountsManagers (data) {
  return request({
    url: `${prefixSecurtity}/account/info/managers`,
    method: 'get',
    params: data
  })
}

// 【3】根据主管id获取其所有下属列表信息
export function getAccountsByManagerId (managerId) {
  return request({
    url: `${prefixSecurtity}/accounts/info/manager/${managerId}`,
    method: 'get'
  })
}

/**
 * 客户中心接口
 * */
const prefixCustomer = '/api-customerCenter'

// 【1】根据客户名称模糊查询客户名称和客户编码
export function getCustomers (data) {
  return request({
    url: `${prefixCustomer}/customers/selector`,
    method: 'get',
    params: data
  })
}

// 【2】根据客户编码查询客户详细信息
export function getCustomerDetailInfo (data) {
  return request({
    url: `${prefixCustomer}/customerService/findOne`,
    method: 'post',
    data
  })
}

/**
 * 坤和
 * */
const prefixKunHe = '/api-kunhe-assistant'

// 【1】根据省市区获取交付主管
export function getStaffConfig (data) {
  return request({
    url: `${prefixKunHe}/staff/config`,
    method: 'get',
    params: data
  })
}

// 客户中心后台服务
const prefixCustomerManage = '/api-customerManage'

/**
 * @description 上报敏感数据操作记录
 * @param {*} data
 * @returns
 */
export function uploadSensitiveData (data) {
  return request({
    url: `${prefixCustomerManage}/sensitive_data/upload_sensitive_data`,
    method: 'post',
    data
  })
}
