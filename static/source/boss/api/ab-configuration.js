import rawRequest from '@/utility/request'
import { MessageBox } from 'element-ui'

const request = function () {
  return rawRequest(...arguments)
    .then(res => {
      if (res.code === 200) {
        return res.data
      } else {
        if (res.msg) {
          res.msg = res.msg.replace(/;/g, '<br/>')
        }
        if (res.message) {
          res.msg = res.message.replace(/;/g, '<br/>')
        }
        MessageBox.alert(res.msg || res.message || '请求失败！', { type: 'error', dangerouslyUseHTMLString: true, customClass: 'mzindex' })
      }
    })
    .catch(err => {
      if (err.msg) {
        err.msg = err.msg.replace(/;/g, '<br/>')
      }
      if (err.message) {
        err.msg = err.message.replace(/;/g, '<br/>')
      }
      MessageBox.alert(err.msg || err.message || '请求失败！', { type: 'error', dangerouslyUseHTMLString: true, customClass: 'mzindex' })
    })
}

const prefix = '/api-ab/sku-inventory/operating-tool/business/operator'

/** AB类配置审批
 * 按条件查询SKU备货类型待审批数据接口
 * @param {*} params { pageNo?: number, materialGroupNum: number, brandId?: number, skuNos?: string, commoditySourceTypeName?: string }
 */
export function getABStockList (params) {
  return rawRequest({
    url: `${prefix}/inventory-parameter/sku-list`,
    method: 'get',
    params
  })
}

/** AB类配置审批
 * AB类配置审批-模板下载
 */
export function getDownloadTemplate (type) {
  let url = '/inventory-type/excel-template-url'
  if (type === 'dock') {
    url = '/docked-status/excel-template-url'
  }
  return request({
    url: `${prefix}${url}`,
    method: 'get'
  })
}

/** AB类配置审批
 * 产线导入sku预校验接口
 * @param {*} params { [] }
 */
export function handelCheck (data, type) {
  let url = '/filter/stocking-sku-with-docked-tag';
  if (type === 'dock') {
    url = '/filter/docked-status'
  }
  return request({
    url: `${prefix}${url}`,
    method: 'post',
    data
  })
}

/** AB类配置审批
 * 产线导入需修改备货类型SKU清单接口
 * @param {*} params { [] }
 */
export function ImportSkuList (data, type) {
  let url = '/inventory-type/sku-list';
  if (type === 'dock') {
    url = '/docked-status/sku-list'
  }
  return request({
    url: `${prefix}${url}`,
    method: 'put',
    data
  })
}

/** AB类配置审批
 * 获取全量待审批数据的下载链接接口
 * @param {*} params { pageNo?: number, materialGroupNum: number, brandId?: number, skuNos?: string, commoditySourceTypeName?: string }
 */
export function getAllDownload (params) {
  return request({
    url: `${prefix}/inventory-parameter/sku-list/excel-url`,
    method: 'get',
    params
  })
}

/** AB类配置审批
 * 产线导入审核结果清单接口
 * @param {*} params { [] }
 */
export function handelSkuList (data) {
  return request({
    url: `${prefix}/inventory-parameter/sku-list`,
    method: 'put',
    data
  })
}

/** AB类配置审批
 * 产线触发参数计算接口
 * @param {*} params { skuInventoryBasicParameterList: array, triggerTotal: boolean }
 */
export function handelCalculate (data) {
  return request({
    url: `${prefix}/inventory-parameter/calculation/sku-list`,
    method: 'post',
    data
  })
}

/** AB类配置审批
 * 产线触发审核完成接口
 * @param {*} params { skuInventoryBasicParameterList: array, triggerTotal: boolean }
 */
export function completedAudit (data) {
  return request({
    url: `${prefix}/inventory-parameter/confirmation/sku-list`,
    method: 'post',
    data
  })
}

/** AB分类审批历史
 * 按条件查询SKU备货类型审批历史数据接口
 * @param {*} params { pageNo?: number, materialGroupNum: number, brandId?: number, sku?: string, commoditySourceTypeName?: string, gmtModifiedEnd?: string, gmtModifiedStart?: string }
 */
export function getABHistoryList (params) {
  return rawRequest({
    url: `${prefix}/inventory-parameter/history/sku-list`,
    method: 'get',
    params
  })
}

/** AB分类审批结果
 * 按条件查询SKU备货类型审批结果数据接口
 * @param {*} params { pageNo?: number, materialGroupNum: number, brandId?: number, sku?: string, commoditySourceTypeName?: string }
 */
export function getABResultList (params) {
  return rawRequest({
    url: `${prefix}/inventory-parameter/result/sku-list`,
    method: 'get',
    params
  })
}

/** A类配置结果
 * 转移备货参数
 * @param {*} params { inventoryParamsTransferList: array, targetMrpScope: string }
 */
export function skuInventoryParamsTransfer (data) {
  return rawRequest({
    url: '/api-ab/sku-inventory/operating-tool/business/skuInventoryParamsTransfer',
    method: 'post',
    data
  })
}
