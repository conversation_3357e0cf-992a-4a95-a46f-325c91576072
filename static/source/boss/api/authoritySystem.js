import rawRequest from '@/utility/request'

const apiSECURITY = '/security-api'
const prefix = '/api-security'

/** 安全中心接口
 * 根据用户名获取账号信息
 * @param {*} params { username: string }
 */
export function getUserInfo (params) {
  return rawRequest({
    url: `${apiSECURITY}/accounts/info/username`,
    method: 'get',
    params
  })
}

/** 安全中心接口
 * getAccountSystemAuthorityByWorkCode
 * @param {*} params { workCode: string }
 */
export function getUserSystem (params) {
  return rawRequest({
    url: `${apiSECURITY}/account/system/authority/${params}`,
    method: 'get'
  })
}

/** 安全中心管理后台系统接口
* 获取资源树
* @param {*} params { id?: number, appId?: number}
*/
export function getAppResources (params) {
  return rawRequest({
    url: `${prefix}/resources`,
    method: 'get',
    params
  })
}

/** 安全中心管理后台系统接口
 * 创建资源
 * @param {*} params { appClientId?: string, appId: number, children?: array, icon?: string, id?: number, link?: string, name: string, parentId?: string, type?: string, urls?: array }
 */
export function setAppResources (data) {
  return rawRequest({
    url: `${prefix}/resources`,
    method: 'post',
    data
  })
}

/** 安全中心管理后台系统接口
 * 修改资源
 * @param {*} params { appClientId?: string, appId: number, children?: array, icon?: string, id?: number, link?: string, name: string, parentId?: string, type?: string, urls?: array }
 */
export function modifyResources (resourceId, data) {
  return rawRequest({
    url: `${prefix}/resources/${resourceId}`,
    method: 'put',
    data
  })
}

/** 安全中心管理后台系统接口
 * 删除资源
 * @param {*} params { id: number }
 */
export function deleteResources (resourceId) {
  return rawRequest({
    url: `${prefix}/resources/${resourceId}`,
    method: 'delete'
  })
}

/** 安全中心管理后台系统接口
 * 上移资源
 * @param {*} params { id: number }
 */
export function upResources (resourceId) {
  return rawRequest({
    url: `${prefix}/resources/${resourceId}/up`,
    method: 'post'
  })
}

/** 安全中心管理后台系统接口
 * 下移资源
 * @param {*} params { id: number }
 */
export function downResources (resourceId) {
  return rawRequest({
    url: `${prefix}/resources/${resourceId}/down`,
    method: 'post'
  })
}

/** 安全中心管理后台系统接口
 * 获取所有角色
 * @param {*} params { id?: number, name?: string, disable?: boolean, typeId?: number, typeName?: string, appId?: number, code?: string }
 */
export function getAppRoles (params) {
  return rawRequest({
    url: `${prefix}/roles/all`,
    method: 'get',
    params
  })
}

/** 安全中心管理后台系统接口
 * 获取角色所有资源
 * @param {*} params { id: number }
 */
export function getRolesAllPower (roleId) {
  return rawRequest({
    url: `${prefix}/roles/${roleId}/resource`,
    method: 'get'
  })
}

/** 安全中心管理后台系统接口
 * 创建角色的资源
 * @param {*} params { ids: string }
 */
export function setRolesPower (roleId, ids) {
  return rawRequest({
    url: `${prefix}/roles/${roleId}/resource`,
    method: 'post',
    params: {
      ids
    }
  })
}
