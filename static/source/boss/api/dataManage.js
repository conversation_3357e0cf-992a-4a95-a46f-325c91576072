import request from '@/utility/request';

const prefix = '/api-rulecenter';
const gateway = '/api-cc-gateway';
const standard = '/api-cc-standard'
const itemCC = '/api-cc-item';
const migrateCC = '/api-cc-migrate';

/** 数据治理 - start ***/
// excel数据导入
export function getExcelImport(data) {
  return request({
    url: `${prefix}/upload`,
    method: 'post',
    data: data
  });
}
// 查询数据导入历史
export function getExcelImportHistory(data) {
  return request({
    url: `${prefix}/upload`,
    method: 'get',
    params: data
  });
}
// 待修复数据导入
export function tobeRepairImport(data) {
  return request({
    url: `${prefix}/upload/property`,
    method: 'post',
    data
  });
}
// 目录树形结构
export function getCategoriesTree(data) {
  return request({
    url: `${prefix}/categories/tree`,
    method: 'get',
    params: data
  });
}
// 新版目录树
export function getNewCategoryTree() {
  return request({
    url: `${prefix}/governTask/getCategoryTree`,
    method: 'get'
  })
}
// 属性规则列表
export function getPropertiesRuleList(params, sort1, sort2) {
  return request({
    url: `${prefix}/properties?sort=${sort1}&sort=${sort2}`,
    method: 'get',
    params: params
  });
}
// 添加属性规则
export function addProperties(data) {
  return request({
    url: `${prefix}/properties`,
    method: 'post',
    data: data
  });
}
// 删除属性规则
export function delProperties(propertyid) {
  return request({
    url: `${prefix}/properties/${propertyid}`,
    method: 'delete'
  });
}
// 通过属性id获取规则
export function getPropertyRule(propertyid) {
  return request({
    url: `${prefix}/properties/${propertyid}`,
    method: 'get'
  });
}
// 通过属性id修改属性规则
export function modifyPropertyRule(propertyid, data) {
  return request({
    url: `${prefix}/properties/${propertyid}`,
    method: 'put',
    data
  });
}
// 获取规则检验进度
export function getCategoriesProgress(categoryId, resultType) {
  return request({
    url: `${prefix}/categories/${categoryId}/precess?resultType=${resultType}`,
    method: 'get'
  });
}
// 获取版本号
export function getCategoriesVersion(categoryId) {
  return request({
    url: `${prefix}/categories/${categoryId}/version`,
    method: 'get'
  });
}
// 获取表结构
export function getTableStructure(categoryId) {
  return request({
    url: `${prefix}/properties/${categoryId}/table`,
    method: 'get'
  });
}
// 获取属性规则问题列表
export function getProblemList(param, data, sort1, sort2) {
  return request({
    url: `${prefix}/sku/problem-record?sort=${sort1}&sort=${sort2}`,
    method: 'post',
    params: param,
    data: data
  });
}
// 获取属性唯一性列表
export function getSimpleList(param, data) {
  return request({
    url: `${prefix}/sku/normal-problem-record`,
    method: 'post',
    params: param,
    data: data
  });
}
// 获取一般属性问题列表
export function getUniqueList(param, data) {
  return request({
    url: `${prefix}/sku/unique-problem-record`,
    method: 'post',
    params: param,
    data: data
  });
}
// 修改属性值
export function updatePropertiesValue(skuid, data) {
  return request({
    url: `${prefix}/sku/${skuid}/properties`,
    method: 'put',
    params: data
  });
}
// 文档上传的sku属性规则校验
export function propertiesCheck(data) {
  return request({
    url: `${prefix}/rule/skuBatchValidate`,
    method: 'post',
    params: data
  });
}
// 文档上传的sku的唯一性校验接口
export function uniqueCheck(data) {
  return request({
    url: `${prefix}/rule/skuUniqueValidate`,
    method: 'post',
    params: data
  });
}

// 文档上传的sku的一般属性校验接口
export function simpleCheck(data) {
  return request({
    url: `${prefix}/rule/normalValidate`,
    method: 'post',
    params: data
  });
}
// 属性生成定稿
export function createFinalDraft(categoryId) {
  return request({
    url: `${prefix}/finalize/${categoryId}`,
    method: 'get'
  });
}
// 查看定稿数据
export function getFinalDraftList(data) {
  return request({
    url: `${prefix}/finalize/property`,
    method: 'get',
    params: data
  });
}
// 数据导出列表查询
export function getExportList(data) {
  return request({
    url: `${prefix}/export`,
    method: 'get',
    params: data
  });
}
// 数据导出
export function exportData(querys, data) {
  return request({
    url: `${prefix}/export/exportData${querys}`,
    method: 'post',
    data
  });
}
// 数据规则
export function exportRule(querys, data) {
  return request({
    url: `${prefix}/export/exportPropertyAndRule${querys}`,
    method: 'post',
    data
  });
}
// 调整属性顺序
export function adjustPropertiesOrder(data) {
  return request({
    url: `${prefix}/properties/seqs/edit`,
    method: 'post',
    data
  });
}
// 调整类目
export function adjustCategory(data) {
  return request({
    url: `${prefix}/sku/adjust-category`,
    method: 'put',
    data
  });
}
// 获取物料组列表
export function getProductGroup(data) {
  return request({
    url: `${prefix}/fact/productGroup`,
    method: 'get',
    params: data
  });
}
// 获取规则列表
export function getFactRule(data) {
  return request({
    url: `${prefix}/fact/rule`,
    method: 'get',
    params: data
  });
}
// 获取趋势图数据
export function getTrendEchartData(data) {
  return request({
    url: `${prefix}/fact/trend`,
    method: 'get',
    params: data
  });
}
// 获取趋势图排行信息数据
export function getTrendRankData(data) {
  return request({
    url: `${prefix}/fact/rank`,
    method: 'get',
    params: data
  });
}
// 获取趋势图顶部数据信息
export function getTrendDatas(data) {
  return request({
    url: `${prefix}/record/tree`,
    method: 'get',
    params: data
  });
}
// 获取商机规则列表
export function getbusinessRuleList(data) {
  return request({
    url: `${prefix}/businesscenter/template`,
    method: 'get',
    params: data
  });
}
// 获取商机规则属性列表
export function getbusinessPropertyList(data) {
  return request({
    url: `${prefix}/businesscenter/property`,
    method: 'get',
    params: data
  });
}
// 获取商机规则属性名称选项列表
export function getbusinessPropertyNameList(data) {
  return request({
    url: `${prefix}/businesscenter/propertyList`,
    method: 'get',
    params: data
  });
}
// 添加商机规则属性
export function addbusinessProperty(data) {
  return request({
    url: `${prefix}/businesscenter`,
    method: 'post',
    data
  });
}
// 获取商机规则属性详情
export function getbusinessPropertyDetail(data) {
  return request({
    url: `${prefix}/businesscenter/propertyByConstraintId`,
    method: 'get',
    params: data
  });
}
// 修改商机规则属性
export function updatebusinessProperty(constraintId, data) {
  return request({
    url: `${prefix}/businesscenter/${constraintId}`,
    method: 'put',
    data
  });
}
// 删除商机规则属性
export function deletebusinessProperty(constraintId) {
  return request({
    url: `${prefix}/businesscenter/delete/${constraintId}`,
    method: 'delete'
  });
}
// 获取商机规则SKU维度下的属性值模板下载地址
export function getbusinessPropertyValueTemplate(data) {
  return request({
    url: `${prefix}/excel-template`,
    method: 'get',
    params: data
  });
}
// 获取商机规则客户列表（模糊查询）
export function getbusinessCustomer(data) {
  return request({
    url: `${prefix}/businesscenter/customer`,
    method: 'get',
    params: data
  });
}
// 定稿规则导出
export function getFinalizeExportData(categoryId) {
  return request({
    url: `${prefix}/finalize/exportData?categoryId=${categoryId}`,
    method: 'get'
  });
}
// 定稿数据导出（定稿页面用）
export function getExportFinalData(categoryId) {
  return request({
    url: `${prefix}/finalize/exportFinalData?categoryId=${categoryId}`,
    method: 'get'
  });
}
// 定稿数据导出(数据导出页面用)
export function exportFinalData(params, data) {
  return request({
    url: `${prefix}/finalize/exportFinalData`,
    method: 'post',
    params: params,
    data: data
  });
}
// 修改类目名称
export function modifyCategoryName(categoryId, data) {
  return request({
    url: `${prefix}/categories/${categoryId}`,
    method: 'get',
    params: data
  });
}
// 类目迁移[把一个类目移动到另一个和此类目父级同等级的类目里面]
export function modifyCategoryClass(categoryId, parentId) {
  return request({
    url: `${prefix}/categories/${categoryId}/${parentId}`,
    method: 'get'
  });
}
// 类目删除
export function deleteCategory(categoryId) {
  return request({
    url: `${prefix}/categories/delete/all/${categoryId}/category`,
    method: 'delete'
  });
}
// 删除sku
export function deleteSku(data) {
  return request({
    url: `${prefix}/sku/sku`,
    method: 'delete',
    params: data
  });
}
// 获取定稿数据列表
export function getFinalDraftSku(data) {
  return request({
    url: `${prefix}/finalize-sku`,
    method: 'get',
    params: data
  });
}
// 定稿导出审批列表查询
export function getExportAuditList(data) {
  return request({
    url: `${prefix}/export/auditList`,
    method: 'get',
    params: data
  });
}
// 定稿导出审批
export function getExportAudit(data) {
  return request({
    url: `${prefix}/finalize/auditFinalizeData`,
    method: 'post',
    params: data
  });
}
// CRM数据导入数据治理
export function importCRMData(data) {
  return request({
    url: `${prefix}/upload/import-system-data`,
    method: 'post',
    data: data
  });
}
// 数据治理数据同步到CRM
export function exportToCRM(data) {
  return request({
    url: `${prefix}/finalize/dataReflow`,
    method: 'get',
    data: data
  });
}
// 获取CRM类目
export function getCRMcategory(data) {
  return request({
    url: `${prefix}/categories/list`,
    method: 'get',
    params: data
  });
}
// 定稿数据
export function toFinalData(categoryId) {
  return request({
    url: `${prefix}/finalize/data/${categoryId}`,
    method: 'get'
  });
}
// 查询用户物料组列表
export function getUserProductList(params, data) {
  return request({
    url: `${prefix}/relation/relationList`,
    method: 'post',
    params: params,
    data: data
  });
}
// 新建用户物料组权限
export function createUserProduct(data) {
  return request({
    url: `${prefix}/relation/add`,
    method: 'post',
    data: data
  });
}
// 用户物料组删除
export function deleteUserProduct(params) {
  return request({
    url: `${prefix}/relation/delete`,
    method: 'delete',
    params
  });
}
// 获取渠道列表
export function getChannel(data) {
  return request({
    url: `${prefix}/channel`,
    method: 'get',
    params: data
  });
}
// 获取渠道列表(全部数据)
export function getAllChannel(data) {
  return request({
    url: `${prefix}/channel/list`,
    method: 'get',
    params: data
  });
}
// 新增渠道
export function createChannel(data) {
  return request({
    url: `${prefix}/channel`,
    method: 'post',
    data: data
  });
}
// 更新渠道信息
export function updateChannel(data, id) {
  return request({
    url: `${prefix}/channel/${id}`,
    method: 'put',
    data: data
  });
}
// 删除渠道信息
export function deleteChannel(id) {
  return request({
    url: `${prefix}/channel/${id}`,
    method: 'delete'
  });
}

// 获取场景列表
export function getScene(data) {
  return request({
    url: `${prefix}/scene`,
    method: 'get',
    params: data
  });
}
// 新增场景
export function createScene(data) {
  return request({
    url: `${prefix}/scene`,
    method: 'post',
    data: data
  });
}
// 更新场景信息
export function updateScene(data, id) {
  return request({
    url: `${prefix}/scene/${id}`,
    method: 'put',
    data: data
  });
}
// 删除场景信息
export function deleteScene(id) {
  return request({
    url: `${prefix}/scene/${id}`,
    method: 'delete'
  });
}

// 获取规则场景列表
export function getRuleScene(data) {
  return request({
    url: `${prefix}/scene-config`,
    method: 'get',
    params: data
  });
}
// 新增规则场景
export function createRuleScene(data) {
  return request({
    url: `${prefix}/scene-config`,
    method: 'post',
    data: data
  });
}
// 更新规则场景信息
export function updateRuleScene(data, id) {
  return request({
    url: `${prefix}/scene-config/${id}`,
    method: 'put',
    data: data
  });
}
// 删除规则场景信息
export function deleteRuleScene(id) {
  return request({
    url: `${prefix}/scene-config/${id}`,
    method: 'delete'
  });
}
// 获取所有规则
export function getAllRule() {
  return request({
    url: `${prefix}/rule/all-rule?ifConfig=true`,
    method: 'get'
  });
}
// 获取危险化学品目录列表
export function getChemicalCas(params) {
  return request({
    url: `${prefix}/chemicalCas`,
    method: 'get',
    params
  });
}
// 创建危险化学品目录
export function createChemicalCas(data) {
  return request({
    url: `${prefix}/chemicalCas`,
    method: 'post',
    data
  });
}
// 获取类目数据治理情况
export function getGovernQuery({ categoryId, problemType }) {
  return request({
    url: `${prefix}/categories/${categoryId}/dealDetailV2?problemType=${problemType}`,
    method: 'get'
  });
}

// 新版治理统计
export function getGovernStatistic(data) {
  return request({
    url: `${prefix}/governTask/getGovernStatistic`,
    method: 'post',
    data
  });
}
// 基于类目将sku数据导入商品中心
export function skuSyncToProductCenter(param) {
  return request({
    url: `${prefix}/data-reflow/export-system-data`,
    method: 'get',
    params: param
  });
}
// 基于类目进行sku同步
export function skuSync(categoryId) {
  return request({
    url: `${prefix}/data-reflow/${categoryId}/import-system-data`,
    method: 'post',
    data: {}
  });
}
// 获取sku同步状态
export function getSkuSyncStatus(param) {
  return request({
    url: `${prefix}/data-reflow/status`,
    method: 'get',
    params: param
  });
}
// 指定sku推送至商品中心
export function getAssignSkuToProductCenter(categoryId, param) {
  return request({
    url: `${prefix}/data-reflow/${categoryId}/assignSkuToProductCenter`,
    method: 'get',
    params: param
  });
}
// 同步商品中心数据至数据治理并检测类目数据
export function getAsyncAndValidate(categoryId) {
  return request({
    url: `${prefix}/categories/${categoryId}/asyncAndValidate`,
    method: 'get'
  });
}
// 下载模板
export function getUploadTemplate() {
  return request({
    url: `${prefix}/upload/getUploadTemplate`,
    method: 'get'
  });
}
// 拉取物料组
export const getCCMaterialGroup = (params) => {
  return request({
    url: `${gateway}/api/scc/v1/dict/entityTypes`,
    method: 'post',
    data: params
  });
};

// 待调整类目的sku状态变更
export function labelAdjustCategorySku(data) {
  return request({
    url: `${prefix}/sku/labelAdjustCategorySku`,
    method: 'put',
    data
  });
}

// 导出调整类目数据
export function exportCategoryAdjustSku(data, params) {
  return request({
    url: `${prefix}/export/exportCategoryAdjustSkus`,
    method: 'post',
    params,
    data
  });
}

// 待调整类目的sku状态变更
export function cancelAdjustCategorySku(data) {
  return request({
    url: `${prefix}/sku/cancelAdjustCategorySku`,
    method: 'put',
    data
  });
}

// 获取治理规则
export function qryRuleList(params) {
  return request({
    url: `${prefix}/governRule/getList`,
    method: 'get',
    params
  });
}

// 获取任务状态
export function qryTaskStatus(params) {
  return request({
    url: `${prefix}/governTask/queryTaskEnum`,
    method: 'get',
    params
  });
}

// 获取任务列
export function getTaskTableColumns(params) {
  return request({
    url: `${prefix}/governTask/getTaskTable`,
    method: 'get',
    params
  })
}

// 获取类目树对应数据
export function getTaskCategoryData(params, data) {
  return request({
    url: `${prefix}/governTask/getTaskCategoryData`,
    method: 'post',
    params,
    data
  })
}

// 获取任务列表
export function qryTaskList(params, data) {
  return request({
    url: `${prefix}/governTask/queryTaskByPage`,
    method: 'post',
    data,
    params
  });
}

// 检查治理任务名称是否重复
export function checkGovernTask(data) {
  return request({
    url: `${prefix}/governTask/checkExistsGovernTask`,
    method: 'post',
    data
  });
}

// 创建治理任务
export function createGovernTask(data) {
  return request({
    url: `${prefix}/governTask/createGovernTask`,
    method: 'post',
    data
  });
}

// 删除治理任务
export function deleteGovernTask(data) {
  return request({
    url: `${prefix}/governTask/deleteGovernTask`,
    method: 'post',
    data
  });
}

// 获取类目树(任务导出)
export function qryAllCategoryTree(params) {
  return request({
    url: `${prefix}/governTask/getCategoryTree`,
    method: 'get',
    params
  });
}

// 治理任务导入
export function importAndFix(formData = {}) {
  const { params = {}, data = {} } = formData
  return request({
    url: `${prefix}/governTask/importAndFix`,
    method: 'post',
    params,
    data
  });
}

// 治理任务导出
export function exportTask(formData = {}) {
  const { params = {}, data = {} } = formData
  return request({
    url: `${prefix}/governTask/exportTaskData`,
    method: 'post',
    params,
    data
  });
}

// 获取属性规则任务表头
export function qryPropertyRuleTable(params) {
  return request({
    url: `${prefix}/governTask/getTaskTable`,
    method: 'get',
    params
  });
}

// 导出待修复数据
export function exportTobeFixedData(data) {
  return request({
    url: `${prefix}/governTask/exportTaskData`,
    method: 'post',
    data
  })
}

// 批量合成关键词
export function batchBuildKeyword(data) {
  return request({
    url: `${prefix}/governTask/batchBuildKeyword`,
    method: 'post',
    data
  })
}

// 获取任务类目树(任务详情)
export function qryTaskCategoryTree(params) {
  return request({
    url: `${prefix}/governTask/getTaskCategoryTree`,
    method: 'get',
    params
  });
}

// 获取所有任务名称
export function getAllTaskName(params) {
  return request({
    url: `${prefix}/governTask/getAllGovernTaskName`,
    method: 'get',
    params
  });
}

// 导出基于类目的治理统计
export function exportGovernStatistics(data) {
  return request({
    url: `${prefix}/governTask/exportGovernStatistic`,
    method: 'post',
    data
  })
}

// 重新检测任务
export function reCheckGovernTask(data) {
  return request({
    url: `${prefix}/governTask/reCheckGovernTask`,
    method: 'post',
    params: data
  })
}

// 获取治理任务详细信息
export function getGovernTaskInfo(params) {
  return request({
    url: `${prefix}/governTask/getGovernTaskInfo`,
    method: 'get',
    params
  })
}

// 获取CC实体
export function getCCEntityTypes(data) {
  return request({
    url: `${prefix}/governTask/queryCCEntityTypes`,
    method: 'post',
    data
  })
}

// 获取CC枚举
export function getCCOptionSet(params) {
  return request({
    url: `${prefix}/governTask/queryCCOptionSet`,
    method: 'get',
    params
  })
}

// 检查治理任务数量
export function checkGovernTaskCondition(data) {
  return request({
    url: `${prefix}/governTask/checkGovernTaskCondition`,
    method: 'post',
    data
  })
}

// 下载删除类目模板
export function getDelTemplate() {
  return request({
    url: `${prefix}/upload/getPropertiesDelTemplate`,
    method: 'get'
  });
}

// 导入属性删除文件
export function uploadDelTemplate(data) {
  return request({
    url: `${prefix}/upload/propertiesDel`,
    method: 'post',
    data
  });
}

export function queryEnum(params) {
  return request({
    url: `${standard}/optionsets/typeCodes`,
    method: 'get',
    params
  })
}

// 根据四级类目获取包含的品牌规则信息
export function qryBrandInfoByCategory(params) {
  return request({
    url: `${prefix}/properties/queryCategoryBrandInfo`,
    method: 'get',
    params
  });
}

// 获取规则内容
export function qryRuleData(data) {
  return request({
    url: `${itemCC}/sku/publish/web/getSkuPulishRule`,
    method: 'post',
    data
  });
}

// 保存规则内容
export function saveRuleData(data) {
  return request({
    url: `${itemCC}/sku/publish/web/save`,
    method: 'post',
    data
  });
}

// 导出规则内容
export function exportRuleData(data) {
  return request({
    url: `${itemCC}/sku/publish/web/exportRule`,
    method: 'post',
    data
  });
}

// 导出规则内容
export function qryCatalogByCC(data) {
  return request({
    url: `${standard}/optionsets/entityTypes`,
    method: 'post',
    data
  });
}

export function queryUserList(params) {
  return request({
    url: `${standard}/users/search`,
    method: 'get',
    params
  })
}

export function qryBarCodeList(data) {
  return request({
    url: `${itemCC}/barcode/tasks`,
    method: 'post',
    data
  });
}

export function qryAIMatchProgress(params) {
  return request({
    url: `${itemCC}/barcode/task/process`,
    method: 'get',
    params
  });
}

export function exportBarCodeTask(data) {
  return request({
    url: `${itemCC}/barcode/task/export`,
    method: 'post',
    data
  });
}

export function newBarCodeTask(data) {
  return request({
    url: `${itemCC}/barcode/task/add`,
    method: 'post',
    data
  });
}

export function downLoadBarCodeTemp(params) {
  return request({
    url: `${migrateCC}/excel/template`,
    method: 'post',
    params
  });
}

export function qryBarCodeTaskDetail(data) {
  return request({
    url: `${itemCC}/barcode/task/detail`,
    method: 'post',
    data
  });
}

export function updateSkuBarCode(data) {
  return request({
    url: `${itemCC}/barcode/task/detail/confirm`,
    method: 'post',
    data
  });
}

export function ignoreSkuBarCode(data) {
  return request({
    url: `${itemCC}/barcode/task/detail/ignore`,
    method: 'post',
    data
  });
}

export function qryBarCodeMetaList(data) {
  return request({
    url: `${standard}/barcode/std/qry`,
    method: 'post',
    data
  })
}

export function uploadBarCodeMetaData(data) {
  return request({
    url: `${standard}/barcode/std/importData`,
    method: 'post',
    data
  })
}

export function updateBarCodeTaskDetail(data) {
  return request({
    url: `${itemCC}/barcode/task/detail/confirm`,
    method: 'post',
    data
  });
}

export function exportBarCodeTaskDetail(data) {
  return request({
    url: `${itemCC}/barcode/task/detail/export`,
    method: 'post',
    data
  });
}
/** 数据治理 - end ***/
