import request from '@/utility/request'

const kunheAssistantApi = ({ prefix = '/api-kunhe-assistant', url, method = 'get', query, data, complete }) => {
  return request({
    url: `${prefix}${url}`,
    method,
    params: query,
    data
  }).then(res => {
    complete && complete(res)
    return res
  }).catch(err => {
    complete && complete(err)
    return { code: -1, err }
  })
}

export default kunheAssistantApi
