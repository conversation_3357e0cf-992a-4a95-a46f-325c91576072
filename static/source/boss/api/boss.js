import request from '@/utility/request'

export const api = ({ prefix = '/api-boss', url, method = 'get', query, data, complete }) => {
  return request({
    url: `${prefix}${url}`,
    method,
    params: query,
    data
  }).then(res => {
    complete && complete(res)
    return res
  }).catch(err => {
    complete && complete(err)
    return { code: -1, err }
  })
}
export const apiProduct = ({ prefix = '/api-boss-product', url, method = 'get', query, data, complete }) => {
  return request({
    url: `${prefix}${url}`,
    method,
    params: query,
    data
  }).then(res => {
    complete && complete(res)
    return res
  }).catch(err => {
    complete && complete(err)
    return { code: -1, err }
  })
}
