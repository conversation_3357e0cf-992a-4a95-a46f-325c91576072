import qs from 'qs'
import request, { downloadFile } from '@/utility/request'

// import { getLabelByValue } from '@/utils/index.js'

const prefix = '/reconciliation'
const prefixCustomer = '/api-customerCenter'
const oms = '/api-opc'
const apiBossOpc = '/api-boss-opc'

// const prefix = 'https://oms-search-front-fat.zkh360.com/bossmodel'

/** 对账中心 - start ***/
// 对账依据列表
export function getBasisList (query, data) {
  const queryStr = qs.stringify(query)
  return request({
    url: `${prefix}/v1/customerStatementBasis/pageAuth?${queryStr}`,
    method: 'post',
    data
  })
}

// 对账单列表
export function getAccountCheckingList (data) {
  return request({
    url: `${prefix}/customerStatement/v1/customerStatementPage`,
    method: 'get',
    params: data
  })
}

// // 提交对账单
// export function submitStatement (data) {
//   return request({
//     url: `${prefix}/modify/model`,
//     method: 'post',
//     data: data
//   })
// }

// 对账单及对账明细人工更新行明细前置
export function beforeUpdateStatementDetailRow (data) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/manualUpdateBefore`,
    method: 'put',
    data: data
  })
}

// 对账单及对账明细人工更新行明细
export function updateStatementDetailRow (data) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/manualUpdate`,
    method: 'put',
    data: data
  })
}
// // 模板编辑
// export function editModel (data) {
//   return request({
//     url: `${prefix}/modify/model`,
//     method: 'post',
//     data: data
//   })
// }

// // 获取初始化模板
// export function getInitModel (modelType) {
//   const url = `${prefix}/get/init/model?modelType=${modelType}`
//   downloadFile(url)
// }

// 对账单详情明细列表
export function getCustomerStatementDetail (query, data) {
  const queryStr = qs.stringify(query)
  return request({
    url: `${prefix}/customerStatementDetail/v1/customerStatementDetailPage?${queryStr}`,
    method: 'post',
    data
  })
}

// 强制完成
export function forceComplate (ids) {
  return request({
    url: `${prefix}/v1/customerStatementBasis/v1/forceComplate/${ids}`,
    method: 'post'
  })
}

// 根据客户名称查询客户集合
export function getCustomerListByPartName (data) {
  return request({
    url: `${prefix}/optionSet/v1/findCrmCustomerList`,
    method: 'get',
    params: data
  })
}

// 对账单详情查询
export function getStatementDetail (no) {
  return request({
    url: `${prefix}/customerStatement/v1/detail/${no}`,
    method: 'get'
  })
}

// 导出对账单
export function exportStatement (customerStatementId) {
  return request({
    url: `${prefix}/customerStatement/templateCenter/export/v1/${customerStatementId}`,
    method: 'post'
  })
}

// 退回对账单
export function backToDraft (customerStatementId) {
  return request({
    url: `${prefix}/customerStatement/revert/v1/${customerStatementId}`,
    method: 'post'
  })
}

// 删除对账单
export function delStatement (customerStatementId) {
  return request({
    url: `${prefix}/customerStatement/del/v1/${customerStatementId}`,
    method: 'post'
  })
}
// 提交对账单
export function comeIntoEffect (customerStatementId) {
  return request({
    url: `${prefix}/customerStatement/commit/v1/${customerStatementId}`,
    method: 'post'
  })
}

// 根据对账依据生成对账单及对账明细
export function createStatementByBasis (data) {
  return request({
    url: `${prefix}/customerStatement/v1/createByBasis`,
    method: 'post',
    data
  })
}
export function createStatementByBasisPre (data) {
  return request({
    url: `${prefix}/customerStatement/v2/createByBasisPreWithAuth`,
    method: 'post',
    data
  })
}
export function getBasisIdsBySearchFields (data) {
  return request({
    url: `${prefix}/v1/customerStatementBasis/selectBasisIdsByAuth`,
    method: 'post',
    data
  })
}
// 全选返结果
export function getSelectAllBySearchFields (data, onlyCheck) {
  return request({
    url: `${prefix}/v1/customer_statement_basis/list/select_all?onlyCheck=${onlyCheck}`,
    method: 'post',
    data
  })
}
export function exportCheck (data, onlyCheck) {
  return request({
    url: `${prefix}/v1/customer_statement_basis/export?onlyCheck=${onlyCheck}`,
    method: 'post',
    data
  })
}
export function getTotalAmount (data, onlyCheck) {
  return request({
    url: `${prefix}/v1/customer_statement_basis/select_all/total_amount`,
    method: 'post',
    data
  })
}

// 对账单列表查询
export function getStatementList (data) {
  return request({
    url: `${prefix}/customerStatement/v2/customerStatementPage`,
    method: 'get',
    params: data
  })
}

// 拆分对账单
export function statementSplit (data) {
  return request({
    url: `${prefix}/customerStatement/v1/split`,
    method: 'post',
    data
  })
}

// 对账单模糊搜索
export function getStatementOptions (name, customer, no, currency) {
  return request({
    url: `${prefix}/customerStatement/v2/findCustomerStatementsByNameWithAuth/${name}/${customer}/${no}/${currency}`,
    method: 'get'
  })
}

// 对账单及对账明细人工新增行明细
export function manualCreateApi (params) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/manualCreate/`,
    method: 'post',
    data: params
  })
}

// 对账单及对账明细人工新增行明细
export function manualDeleteApi (params) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/manualDelete/${params.no}/${params.detailNos}`,
    method: 'delete'
  })
}

/** 对账中心 - end ***/

// 获取枚举类型
export function getEnum (data) {
  return request({
    url: `${prefix}/optionSet/v1/findOptionSetByType`,
    method: 'get',
    params: data
  })
}

// 对账单详情明细列表
export function findOne (data) {
  return request({
    url: `${prefixCustomer}/customerService/findOne`,
    method: 'post',
    data
  })
}

// 根据条件获取子级选项集
export function findSubOptionSetByType (data) {
  return request({
    url: `${prefix}/optionSet/v1/findSubOptionSetByType`,
    method: 'get',
    params: data
  })
}

// 对账单修改开票抬头
export function invoiceReceiverModify (data) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/invoiceReceiverModify`,
    method: 'post',
    data
  })
}

// 对账单修改单价
export function unitPriceUpdate (data) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/unitPriceUpdate`,
    method: 'post',
    data
  })
}

// 对账单修改客户物料信息
export function materialModify (data) {
  return request({
    url: `${prefix}/customerStatementDetail/v2/materialModify`,
    method: 'post',
    data
  })
}

// 对账单修改折扣
export function discountUpdate (data) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/discountUpdate`,
    method: 'post',
    data
  })
}

// 对账单全选
export function selectDetailIds (data) {
  return request({
    url: `${prefix}/customerStatementDetail/selectDetailIds`,
    method: 'post',
    data
  })
}

// 对账单明细客户付款条件查询
export function findPayCondition (data) {
  return request({
    url: `${prefix}/customerStatementDetail/v1/findPayCondition`,
    method: 'get',
    params: data
  })
}

// 物料信息单位查询
export function getUnit (data) {
  return request({
    url: `${oms}/v1/dict/values/values/list`,
    method: 'get',
    params: data
  })
}

// 销售类型描述：
let orderTypeDescStatusOptions

export function getOrderTypeDescStatusOptions () {
  return new Promise(resolve => {
    if (orderTypeDescStatusOptions) {
      resolve(orderTypeDescStatusOptions)
    } else {
      getEnum({ status: 1, typeCode: 'soCategory' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          orderTypeDescStatusOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
        }
        resolve(orderTypeDescStatusOptions)
      })
    }
  })
}
// 对账依据状态：
let statusOptions
export function getStatusOptions () {
  return new Promise(resolve => {
    if (statusOptions) {
      resolve(statusOptions)
    } else {
      getEnum({ status: 1, typeCode: 'CSBStatus' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          statusOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
          // statusOptions.unshift({
          //   label: '全部',
          //   value: ''
          // })
        }
        resolve(statusOptions)
      })
    }
  })
}

// 对账单状态：
let statementStatusOptions
export function getStatementStatusOptions () {
  return new Promise(resolve => {
    if (statementStatusOptions) {
      resolve(statementStatusOptions)
    } else {
      getEnum({ status: 1, typeCode: 'CSStatus' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          statementStatusOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
          statementStatusOptions.unshift({
            label: '全部',
            value: ''
          })
        }
        resolve(statementStatusOptions)
      })
    }
  })
}
// 交货类型描述：
let deliveryTypeDescOptions
export function getDeliveryTypeDescOptions () {
  return new Promise(resolve => {
    if (deliveryTypeDescOptions) return resolve(deliveryTypeDescOptions)
    getEnum({ status: 1, typeCode: 'dnOrderType' }).then(res => {
      if (res.status === 200 && res.datas && res.datas.length > 0) {
        deliveryTypeDescOptions = res.datas.map(item => ({ label: item.displayName, value: item.value }))
      }
      resolve(deliveryTypeDescOptions)
    })
  })
}
// 是否对账完结枚举：
let ifEndOptions
export function getIfEndOptions () {
  return new Promise(resolve => {
    if (ifEndOptions) return resolve(ifEndOptions)
    getEnum({ status: 1, typeCode: 'CSBEndStatus' }).then(res => {
      if (res.status === 200 && res.datas && res.datas.length > 0) {
        ifEndOptions = res.datas.map(item => ({ label: item.displayName, value: item.value }))
      }
      resolve(ifEndOptions)
    })
  })
}
// 对账单审核状态
let auditStatusOptions
export function getAuditStatusOptionsOptions () {
  return new Promise(resolve => {
    if (auditStatusOptions) {
      resolve(auditStatusOptions)
    } else {
      getEnum({ status: 1, typeCode: 'AuditStatus' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          auditStatusOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
          auditStatusOptions.unshift({
            label: '全部',
            value: ''
          })
        }
        resolve(auditStatusOptions)
      })
    }
  })
}
// SAP同步状态
let sapSyncOptions
export function getSapSyncOptions () {
  return new Promise(resolve => {
    if (sapSyncOptions && sapSyncOptions.length) {
      resolve(sapSyncOptions)
    } else {
      getEnum({ status: 1, typeCode: 'SapSyncStatus' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          sapSyncOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
          sapSyncOptions.unshift({
            label: '全部',
            value: ''
          })
        }
        resolve(sapSyncOptions)
      })
    }
  })
}
// 开票类型
let invoiceTypeOptions
export function getInvoiceTypeOptions () {
  return new Promise(resolve => {
    if (invoiceTypeOptions) {
      resolve(invoiceTypeOptions)
    } else {
      getEnum({ status: 1, typeCode: 'InvoiceType' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          invoiceTypeOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
        }
        resolve(invoiceTypeOptions)
      })
    }
  })
}

// 开票信息六要素
export function getCustomerInfo (customerNumber) {
  return request({
    url: `${prefixCustomer}/customers/customer_numbers/${customerNumber}`,
    method: 'get'
  })
}

// 快递公司
let expressCompanyOptions
export function getExpressCompanyOptions () {
  return new Promise(resolve => {
    if (expressCompanyOptions) {
      resolve(expressCompanyOptions)
    } else {
      getEnum({ status: 1, typeCode: 'ExpressCompany' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          expressCompanyOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
        }
        resolve(expressCompanyOptions)
      })
    }
  })
}

// 交货单对应销售订单状态
let deliveryStatusOptions
export function getDeliveryStatusOptionsOptions () {
  return new Promise(resolve => {
    if (deliveryStatusOptions) {
      resolve(deliveryStatusOptions)
    } else {
      getEnum({ status: 1, typeCode: 'DeliveryStatus' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          deliveryStatusOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
          deliveryStatusOptions.unshift({
            label: '全部',
            value: ''
          })
        }
        resolve(deliveryStatusOptions)
      })
    }
  })
}
// 外围系统枚举
let peripheralSystemOptions
export function getPeripheralSystemOptions () {
  return new Promise(resolve => {
    if (peripheralSystemOptions) {
      resolve(peripheralSystemOptions)
    } else {
      getEnum({ status: 1, typeCode: 'PeripheralSystem' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          peripheralSystemOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
        }
        resolve(peripheralSystemOptions)
      })
    }
  })
}
// 开票状态枚举
let invoicingStatusOptions
export function getInvoicingStatusOptions () {
  return new Promise(resolve => {
    if (invoicingStatusOptions) {
      resolve(invoicingStatusOptions)
    } else {
      getEnum({ status: 1, typeCode: 'invoicingStatus' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          invoicingStatusOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
        }
        resolve(invoicingStatusOptions)
      })
    }
  })
}

export const includeTaxList = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '含税',
    value: 'Z005'
  },
  {
    label: '未税',
    value: 'Z010'
  }
]

export const currencySignList = [
  {
    label: '￥',
    value: 'CNY'
  },
  {
    label: '$',
    value: 'USD'
  }
]

// 在线确认
export function onlineConfirmApi (id) {
  return request({
    url: `${prefix}/customerStatement/confirm/v1/${id}`,
    method: 'post'
  })
}
// 在线取消
export function onlineCancelApi (id) {
  return request({
    url: `${prefix}/customerStatement/cancel/v1/${id}`,
    method: 'post'
  })
}
// 推送SAP提交
export function submitPushSap (data) {
  return request({
    url: `${prefix}/customerStatement/revert/v1/`,
    method: 'post',
    data
  })
}

// 推送SAP查询
export function searchSapList (data) {
  return request({
    url: `${prefix}/customerStatement/pushSap/v1`,
    method: 'post',
    data
  })
}
// 推送SAP下一步
export function pushSapNextStep (ids) {
  return request({
    url: `${prefix}/customerStatement/nextStep/v2`,
    method: 'post',
    data: { ids }
  })
}
// 推送SAP校验
export function pushSapCheck (data) {
  return request({
    url: `${prefix}/customerStatement/pushSapOneConfirm/v1`,
    method: 'post',
    data
  })
}
// 推送SAP校验确认(税额)
export function pushSapTaxConfirm (data) {
  return request({
    url: `${prefix}/customerStatement/pushSapTaxConfirm/v1`,
    method: 'post',
    data
  })
}
// 确认推送SAP
export function pushSapSubmit (data) {
  return request({
    url: `${prefix}/customerStatement/pushSapConfirmEnd/v1`,
    method: 'post',
    data
  })
}
// 需求部门
export function demandDepartment (code) {
  return request({
    url: `${prefix}/v1/customerStatementBasis/customerDemandDepartment/${code}`,
    method: 'get'
  })
}
// 联系人查询
export function remoteContact (contactName, id) {
  return request({
    url: `${prefix}/customerStatement/v2/findContactByName`,
    method: 'get',
    params: {
      contactName,
      id
    }
  })
}
// 未开票原因
export function getUnInvoicedReasonOptions (params) {
  return request({
    url: `${prefix}/unInvoicedReasonRecord/v1/selectUnInvoicedReasonEnum`,
    method: 'get',
    params
  })
}
// 未开票原因明细
export function getUnInvoicedReasonDetailOptions (code, params) {
  return request({
    url: `${prefix}/unInvoicedReasonRecord/v1/selectUnInvoicedReasonDetailEnum/${code}`,
    method: 'get',
    params
  })
}
// 对账依据导出
export function exportBasisApi (data) {
  return request({
    url: `${prefix}/fileTask/v1/download`,
    method: 'post',
    data
  })
}
// 生成未开票记录
export function createUnInvoiceRecord (data) {
  return request({
    url: `${prefix}/unInvoicedReasonRecord/v1/create`,
    method: 'post',
    data
  })
}
// 生成未开票记录
export function getUnInvoicedReasonRecord (id) {
  return request({
    url: `${prefix}/unInvoicedReasonRecord/v1/list/${id}`,
    method: 'get'
  })
}
// 对账依据导出
export function downloadBasisTpl () {
  return downloadFile(`${prefix}/unInvoicedReasonRecord/v1/downloadTemplate`, null, {
    method: 'get',
    ignoreEmpty: true
  })
}

// 未开票责任人：
let unInvoicedResponsibleOptions

export function getUnInvoicedResponsibleOptions () {
  return new Promise(resolve => {
    if (unInvoicedResponsibleOptions) {
      resolve(unInvoicedResponsibleOptions)
    } else {
      getEnum({ status: 1, typeCode: 'UnInvoicedResponsible' }).then(res => {
        if (res.status === 200 && res.datas && res.datas.length > 0) {
          unInvoicedResponsibleOptions = res.datas.map(item => {
            return {
              label: item.displayName,
              value: item.value
            }
          })
        }
        resolve(unInvoicedResponsibleOptions)
      })
    }
  })
}

// 获取对账依据列表配置
export function fetchConfigFromServer () {
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=4`,
    method: 'get'
  })
}

// 保存对账依据列表配置
export function saveConfig2Server (data) {
  const formattedData = (data || []).map((item, index) => {
    return {
      ...item,
      scene: 4,
      parentId: 1,
      orderNo: index + 1
    }
  })
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=4`,
    method: 'post',
    data: formattedData
  })
}

// 修改对账单名称
export function updateStatementName(no, data) {
  return request({
    url: `${prefix}/customerStatement/v1/updateName/${no}/${data}`,
    method: 'post'
  })
}

export function getPendingInvoiceList (query, data) {
  const queryStr = qs.stringify(query)
  return request({
    url: `${prefix}/customerStatementDetail/v1/waitInvoicePage?${queryStr}`,
    method: 'post',
    data
  })
}

export function getPendingListIdsBySearchFields (data) {
  return request({
    url: `${prefix}/customerStatementDetail/selectWaitInvoiceSelectAll`,
    method: 'post',
    data
  })
}

// 获取待开票列表配置
export function fetchPendingInvoiceConfigFromServer () {
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=5`,
    method: 'get'
  })
}

// 保存待开票列表配置
export function savePendingInvoiceConfig2Server (data) {
  const formattedData = (data || []).map((item, index) => {
    return {
      ...item,
      scene: 5,
      parentId: 1,
      orderNo: index + 1
    }
  })
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=5`,
    method: 'post',
    data: formattedData
  })
}

// 输入收票方
export function searchClients (data) {
  return request({
    url: prefix + '/customerStatementDetail/v2/findCustomerListByNameOrNum',
    method: 'get',
    params: data
  })
}

const wfPrefix = '/api-workflow'

// 以下为原工单模块的接口调用，因为工单单独挪出去了，把原先boss项目里的工单代码做下清理，故把接口移到这里
export function departmentUser(nameLike) {
  return request({
    url: wfPrefix + '/getUser',
    method: 'get',
    params: {
      nameLike
    }
  })
}

export function getDepartmentHead(departmentId, categoryId) {
  return request({
    url: wfPrefix + '/getDepartmentHead',
    method: 'get',
    params: {
      departmentId,
      categoryId
    }
  })
}

export function departmentByName(name) {
  return request({
    url: wfPrefix + '/department/name',
    method: 'get',
    params: {
      name
    }
  })
}

export function departmentById(departmentId) {
  return request({
    url: wfPrefix + '/department/id',
    method: 'get',
    params: {
      departmentId,
      returnRoot: false
    }
  })
}

export function getMatterData(params) {
  return request({
    url: wfPrefix + '/admin/matter/parent',
    method: 'get',
    params
  })
}

export function getConfig (params) {
  return request({
    url: wfPrefix + '/admin/matter/getConfig',
    method: 'get',
    params
  })
}
// end
