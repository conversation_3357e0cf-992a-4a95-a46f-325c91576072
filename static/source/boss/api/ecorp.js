import request from '@/utility/request'
import { updateAppName } from '@/utils/config.js'

const prefix = '/documentApi' + '/v1/ecorp'
const uploadPrefix = '/ali-upload'
const apiBossOpc = '/api-boss-opc'
const apiPMSOrder = '/api-pms-order'

// 请求
export function ecorpDocList (voucherNo, data) {
  return request({
    url: `${prefix}/ecorpDocList/${voucherNo}`,
    method: 'get',
    params: data
  })
}
// 请求
export function docList (voucherNo, data) {
  return request({
    url: `${prefix}/docList/${voucherNo}`,
    method: 'get',
    params: data
  })
}
// 请求
export function picList (voucherNo, data) {
  return request({
    url: `${prefix}/picList/${voucherNo}`,
    method: 'get',
    params: data
  })
}

// 请求
export function edDustbinList (voucherNo, data) {
  return request({
    url: `${prefix}/edDustbinList/${voucherNo}`,
    method: 'get',
    params: data
  })
}
// 请求
export function orderList (data) {
  return request({
    url: apiBossOpc + '/sos',
    method: 'post',
    data
  })
}
// 备注
export function upEcorpRemark (id, data) {
  return request({
    url: `${prefix}/upEcorpRemark/${id}`,
    method: 'get',
    params: data
  })
}
// 请求
export function orderTotal (data) {
  return request({
    url: `${prefix}/total`,
    method: 'get',
    params: data
  })
}
// 请求
export function delEcorpDoc (data) {
  return request({
    method: 'post',
    url: `${prefix}/delEcorpDoc`,
    data: data
  })
}
// 请求
export function recoverEcorpDoc (id, data) {
  return request({
    method: 'post',
    url: `${prefix}/recoverEcorpDoc/${id}`,
    params: data
  })
}

// 请求
export function downloadFile (queryData) {
  return request({
    method: 'post',
    url: `${prefix}/downloadFile`,
    params: queryData
  })
}
// TODO
// 上传到阿里
export function uploadFile (formData, appName) {
  formData.append('appName', appName || updateAppName)
  return request({
    method: 'post',
    url: uploadPrefix,
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
export function testUpload (formData) {
  return request({
    method: 'post',
    url: '/api-opc/v4/oms/order/parserCustomerOrder',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 解析结果AI诊断
export function parserResultSuggestion (data) {
  return request({
    method: 'post',
    url: '/api-opc/v4/oms/order/parserResultSuggestion',
    data
  })
}
// 通知后台上传成功
export function uploadFileCallback (data) {
  return request({
    method: 'post',
    url: `${prefix}/eDocumentUpload`,
    data
  })
}
// 文件查重
export function fileChecking (queryData) {
  return request({
    method: 'get',
    url: `${prefix}/fileRepetitionJudgement`,
    params: queryData
  })
}

// 保存上传电子文档的元数据（查重上传成功二合一）
// 之前的流程是：
// 1.查重
// 2.上传到阿里
// 3.通知系统上传成功
// 新流程是：
// 1.上传到阿里
// 2.通知系统上传成功（顺便查重）
export function saveOssFileInfo (data) {
  return request({
    method: 'post',
    url: '/documentApi' + '/v2/ecorp/saveDocumentMetaData',
    data
  })
}

// 电子文档-签收附件-图片
export function elePicSignAttachment (sapOrderNo) {
  return request({
    url: '/oms-opc-front' + `/supply/attachment/pic/${sapOrderNo}`,
    method: 'get'
  })
}

// 电子文档-签收附件-文件
export function eleDocSignAttachment (sapOrderNo) {
  return request({
    url: '/oms-opc-front' + `/supply/attachment/doc/${sapOrderNo}`,
    method: 'get'
  })
}

// 分页查询采购订单商品行（第三方用）
export function pagePOItem (data) {
  return request({
    url: `${apiPMSOrder}/api/v1/po/pagePOItem`,
    method: 'post',
    data
  })
}

// 同步订单附件
export function refreshAttachment (data) {
  return request({
    url: `/api-opc/v1/so/ops/refresh/attachment/${data}`,
    method: 'post'
  })
}
