import request from '@/utility/request'

const prefixApi = '/api-atp-realtime'
const prefixOmsBase = '/oms-base'
const prefixOmsOpc = '/api-oms-opc'
const prefixOutbound = prefixOmsOpc + '/circuit/outbound'
const prefixPurchase = prefixOmsBase + '/purchase/progress'
const opcFront = '/oms-opc-front'
// const prefixDataCenter = '/data-center'
const apiBossOpc = '/api-boss-opc'
const apiBossKunhe = '/api-boss-kunhe'
const apiInvoice = '/api-invoice'
const apiOpc = '/api-opc'

// 请求订单详情
export function fetchDetailList (soVoucherId) {
  return request({
    url: prefixOmsBase + `/soDetail/${soVoucherId}`,
    method: 'get'
    // params: query
  })
}
// 请求供应状态列表
export function fetchSupplyList (query) {
  return request({
    url: opcFront + `/supply/list/${query.soVoucherId}`,
    method: 'get'
  })
}
// 请求供应状态--流程节点点亮
export function fetchSupplyNodeList (sapOrderNo) {
  return request({
    url: prefixOmsOpc + `/supply/node/${sapOrderNo}`,
    method: 'get'
  })
}

// 请求最新匹配PO记录
export function fetchMatchResult (query) {
  return request({
    url:
      prefixOmsOpc +
      `/supply/latest/match/po/${query.soVoucherId}/${query.soItemId}`,
    method: 'get'
    // params: query
  })
}
// 请求最新5条匹配PO记录
export function fetchAllMatchResult (query) {
  return request({
    url: prefixApi + '/v1/atp/allMatchResults',
    method: 'get',
    params: query
  })
}
// 请求出库单列表
export function fetchOutBoundList (query, soNo) {
  return request({
    url: `${apiBossOpc}/v2/one_stop/sos/${soNo}/dns`,
    method: 'get',
    params: query
  })
}
// 请求出库单详情列表
export function fetchOutBoundDetailList (query, outboundNo) {
  return request({
    url: `${prefixOutbound}/detail/${outboundNo}`,
    method: 'get',
    params: query
  })
}
// 请求仓内物流轨迹
export function fetchNodeStatus (outboundNo) {
  return request({
    url: `${prefixOutbound}/node/status/${outboundNo}`,
    method: 'get'
  })
}
// // 请求物流轨迹详情-非直发
// export function fetchLogisticsDetail (outboundNo) {
//   return request({
//     url: `${prefixOutbound}/logistics/track/${outboundNo}`,
//     method: 'get'
//   })
// }

// 请求物流轨迹详情-供应商直发，非直发公用
export function fetchSupplierDirectLogisticsDetail (dnNo) {
  return request({
    url: `${apiBossKunhe}/one_stop/sos/dns/${dnNo}/logistics`,
    // url: `${prefixOutbound}/circuit/outbound/logistics/dns/${dnNo}`,
    method: 'get'
  })
}

// 请求物流轨迹详情-供应商直发，非直发公用
export function fetchInvoiceLogisticsDetail (logisticsCompanyNo, logisticsNo) {
  return request({
    url: `${apiBossKunhe}/one_stop/sos/dns/logistics`,
    method: 'get',
    params: {
      logisticsCompanyNo, logisticsNo
    }
  })
}

/** -- 采购进度 -- **/
// 请求 已采购-列表
export function getPurchaseDetail (params, data) {
  return request({
    url: `${apiOpc}/v1/so/purchase/progress/list`,
    method: 'post',
    params,
    data
  })
}

export function fetchPurchasedList (sapOrderNo, data) {
  return request({
    url: `${prefixPurchase}/list/${sapOrderNo}`,
    method: 'get',
    params: data
  })
}

// 请求 未采购-列表
export function fetchPendingList (sapOrderNo, data) {
  return request({
    url: `${prefixPurchase}/pending/list/${sapOrderNo}`,
    method: 'get',
    params: data
  })
}

// 请求 未采购-待采SO汇总-列表
export function fetchPendingSOList (productNo, data) {
  return request({
    url: `${prefixPurchase}/pending/${productNo}`,
    method: 'get',
    params: data
  })
}

// 请求 已采购-PO对应SO汇总-列表
export function fetchPurchasedPOSOList (productNo, data) {
  return request({
    url: `${prefixPurchase}/poed/so/summary/${productNo}`,
    method: 'get',
    params: data
  })
}

// 请求 已采购- PO单进度
export function fetchPurchasedProgress (purchaseNo, purchaseItemNo, data) {
  return request({
    url: `${prefixPurchase}/node/purchase_nos/${purchaseNo}/purchase_item_nos/${purchaseItemNo}/vendor_self_delivery/true`,
    method: 'get',
    params: data
  })
}
/** -- 采购进度 -- end */

// 请求 发票List
export function fetchInvoiceProgressList (soNo) {
  return request({
    url: `${apiInvoice}/v1/invoice/visible/list/${soNo}`,
    method: 'get'
  })
}

// 请求 发票统计
export function fetchInvoiceStat (soNo) {
  return request({
    url: `${apiInvoice}/v1/invoice/visible/statistic/${soNo}`,
    method: 'get'
  })
}

// 请求 发票预览
export function fetchInvoiceDetail (soNo) {
  return request({
    url: `${apiInvoice}/v1/invoice/visible/detail/${soNo}`,
    method: 'get'
  })
}
// 请求 发票预览
export function getreceiveRecord (sapOrderNo) {
  return request({
    url: `${apiInvoice}/v1/invoice/receive/visible/summary/${sapOrderNo}`,
    method: 'get'
  })
}
