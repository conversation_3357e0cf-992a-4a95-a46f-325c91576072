import request from '@/utility/request'

const apiMdm = '/api-mdm'
const apiBossOpc = '/api-boss-opc'
// so订单物料组查询mdm接口
export function getProductGroupOptions (type) {
  return request({
    url: `${apiMdm}/optionset/${type}`,
    method: 'get'
  })
}
// so订单导出
export function exportDnOrders (data) {
  return request({
    url: `${apiBossOpc}/one_stop/dns/details/export`,
    method: 'post',
    data
  })
}
// so订单查询列表
export function getDnOrders (data) {
  const params = {
    pageNo: data.pageNo,
    pageSize: data.pageSize
  }
  delete data.pageNo
  delete data.pageSize
  return request({
    url: `${apiBossOpc}/one_stop/dns/details`,
    method: 'post',
    data,
    params
  })
}

export function fetchConfigFromServer () {
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=3`,
    method: 'get'
  })
}

export function saveConfig2Server (data) {
  data = data.map((d, idx) => ({
    ...d,
    parentId: 1,
    orderNo: idx + 1
  }))
  return request({
    url: `${apiBossOpc}/table/column/setting?scene=3`,
    method: 'post',
    data
  })
}
