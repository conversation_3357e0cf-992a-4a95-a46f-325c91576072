import request, { downloadFile } from '@/utility/request'

const apiBossKunhe = '/api-boss-kunhe'
const apiVcBoss = '/api-vc-boss'

export function getDirectOrders (data) {
  return request({
    url: `${apiVcBoss}/delivery/unsigned`,
    method: 'post',
    data
  })
}
export function queryDeliveryNos (data) {
  return request({
    url: `${apiBossKunhe}/order/special/query`,
    method: 'post',
    data
  })
}
export function directOrderCustomerSearch (data) {
  return request({
    url: `${apiVcBoss}/delivery/unsigned/customer`,
    method: 'get',
    params: data
  })
}

export function batchSignDelivery (params) {
  let { key, data } = params
  return request({
    url: `${apiVcBoss}/delivery/unsigned/${key}/plans/receive`,
    method: 'post',
    data
  })
}
export function getDeliveryDetail (key) {
  return request({
    url: `${apiVcBoss}/delivery/unsigned/${key}/plans`,
    method: 'get'
  })
}
export function exportApi (data) {
  return downloadFile(`${apiVcBoss}/delivery/unsigned/export`, data, {
    method: 'POST'
  })
}
export function signBatchApi (data) {
  return request({
    url: `${apiVcBoss}/delivery/unsigned/dns/receive`,
    method: 'post',
    data
  })
}
export function signSingleApi (data) {
  return request({
    url: `${apiVcBoss}/delivery/unsigned/dns/${data[0]}/receive`,
    method: 'post'
  })
}
