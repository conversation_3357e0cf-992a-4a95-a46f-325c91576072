import request from '@/utility/request';
import qs from 'qs';

const prefix = '/reconciliation';
const financialPrefix = '/api-financial';
const customerCenterPrefix = '/api-customerCenter';
/** 对账中心 - start ***/
// 未开票对账依据列表查询
export function getBasisList (query, data) {
  const queryStr = qs.stringify(query)
  return request({
    url: `${prefix}/v1/customerStatementBasis/pageUnInvoiced?${queryStr}`,
    method: 'post',
    data
  })
}

export function getDunningLetterList(data) {
  return request({
    url: `${financialPrefix}/callletter/page`,
    method: 'get',
    params: data
  })
}

export function getDunningLetterDetail(id) {
  return request({
    url: `${financialPrefix}/callletter/getCallLetter/${id}`,
    method: 'get'
  })
}

export function resendDunningLetter(id) {
  return request({
    url: `${financialPrefix}/callletter/resend/${id}`,
    method: 'get'
  })
}

// export function downloadLetterFile (id) {
//   return downloadFile(`${financialPrefix}/callletter/download/${id}`, null, {
//     method: 'get',
//     ignoreEmpty: true
//   })
// }
export function downloadLetterFile (id) {
  return request({
    url: `${financialPrefix}/callletter/download/${id}`,
    method: 'get'
  })
}

// 查询联系人列表
export function getContactList(data) {
  return request({
    url: `${customerCenterPrefix}/contactService/contactList`,
    method: 'post',
    data
  })
}
// 根据客户编码和公司主体获取催款函
export function getDunningLetter(data) {
  return request({
    url: `${financialPrefix}/callletter/pullFromBi`,
    method: 'post',
    data
  })
}
// 重新发送催款函
export function resend(data) {
  return request({
    url: `${financialPrefix}/callletter/resend`,
    method: 'post',
    data
  })
}
