import request from '@/utility/request'
import qs from 'qs';

const sellOrderHost = '/api-opc'
const atpRealtimeHost = '/api-atp-realtime'
const omsConfig = '/oms-config'
const omsSoQuery = '/oms-so-query'
const customerProductHost = '/api-customer-product'

export function listDict () {
  return request({
    url: sellOrderHost + '/v1/so/template/values/all',
    params: {
      excludeKey: 'shippingPosition,salesAreaCustomerWhite,purchaseGroup,purchaseGroupEmail,chemicalPosition'
    }
  })
}

export function getDictVersion () {
  return request({
    url: sellOrderHost + '/v1/dict/values/values/version'
  })
}

export function getOptionTypes (params) {
  let options = {
    listKey: 'soCategory',
    size: 20
  }
  options = { ...options, ...params }
  return request({
    url: sellOrderHost + '/v1/so/template/values/list',
    params: options
  })
}

/**
 * 获取订单详情
 * @param {*} omsNo
 */
export function orderDetail (params) {
  let queryStr = ''
  const serialize = function (obj) {
    const str = []
    for (let p in obj) {
      if (obj.hasOwnProperty(p) && obj[p]) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]))
      }
    }
    return str.join('&')
  }
  if (params) {
    queryStr = serialize(params)
  }
  return request({
    url: `${sellOrderHost}/v1/so/template/so/detail?${queryStr}`
  })
}

export function cancelOrder (data) {
  return request({
    url: `${sellOrderHost}/v4/oms/order/cancel`,
    method: 'post',
    data
  })
}

export function cancelOrderPart (data) {
  return request({
    url: `${sellOrderHost}/v4/oms/order/partCancel`,
    method: 'post',
    data
  })
}

export function queryCustomerNo (search) {
  return request({
    url: sellOrderHost + '/v1/so/template/customer/like',
    params: { salesOrganization: search }
  })
}

export function customerInfo (params) {
  return request({
    url: `${sellOrderHost}/v1/so/template/customer`,
    params: params
  })
}

export function skuInfo (skuNo) {
  return request({
    url: `${sellOrderHost}/v1/so/template/skuNo/${skuNo}`
  })
}

export function searchClients (query) {
  return request({
    url: sellOrderHost + '/v1/so/template/customer/like',
    params: { value: query }
  })
}

export function searchContactList (customerCode, contactName) {
  return request({
    url: sellOrderHost + '/v1/contact/list',
    params: {
      current: 1,
      customerCode,
      contactName,
      size: 20
    }
  })
}

export function searchContactListByGroup (obj) {
  const params = {
    current: 1,
    size: 20,
    ...obj
  }
  return request({
    params,
    url: sellOrderHost + '/v1/contact/list'

  })
}

/**
 * 用户选择销售范围之后，触发获取用户详情的信息
 * @param {*} customerNumber
 * @param {*} distributionChannel
 * @param {*} productGroup
 * @param {*} salesOrganization
 */
export function getClientDetail (
  customerNumber,
  distributionChannel,
  productGroup,
  salesOrganization
) {
  const params = {
    customerNumber,
    distributionChannel,
    productGroup,
    salesOrganization
  }
  return request({
    url: sellOrderHost + '/v1/so/template/customer',
    params
  })
}

export function createOrder (url, data, params) {
  return request({
    url: `${sellOrderHost}/v4/oms/${url}`,
    method: 'post',
    data,
    params
  })
}

export function skuDetail (sku) {
  return request({
    url: sellOrderHost + '/v1/so/template/skuNo/' + sku
  })
}

/**
 * 商品模糊查询接口
 * @param {*} search
 */
export function searchSkuList (search) {
  return request({
    url: sellOrderHost + '/v1/so/template/sku/like',
    params: {
      vague: search
    }
  })
}

// 查询sku接口切换白名单
export function getSearchSkuSwitch () {
  return request({
    url: sellOrderHost + '/v2/sku/like/switch'
  })
}

/**
 * 商品模糊查询-新接口
 * @param {*} search
 */
export function searchSkuListV2 (data) {
  return request({
    url: sellOrderHost + '/v2/sku/like',
    method: 'post',
    data
  })
}

/**
 * 查询精确客户物料关系
 * @param {*} search
 */
export function accurateQuery(data) {
  return request({
    url: sellOrderHost + '/v2/sku/accurateQuery',
    method: 'post',
    data
  });
}

// 客户多物料关系查询
export function searchCustomerMaterialRelation (data) {
  return request({
    url: customerProductHost + '/customerProduct/relation/service/batch/query/listByAccurateParam',
    method: 'post',
    data
  })
}

/**
 * 拉取SKU详情
 * @param {*} skuNo
 */
export function getSkuDetail (skuNo, params) {
  let url = `/v1/so/template/skuNo/${skuNo}`
  if (params) {
    const queryStr = []
    Object.keys(params).forEach(key => {
      queryStr.push(`${key}=${params[key]}`)
    })
    if (queryStr.length > 0) {
      const s = queryStr.join('&')
      url += `?${s}`
    }
  }
  return request({
    url: sellOrderHost + url
  })
}

export function setEditOrder (url, data) {
  return request({
    url: sellOrderHost + '/v4/oms/' + url,
    method: 'post',
    data
  })
}

/**
 * 根据客户编码和skuNo获取询价记录列表
 * @param {*} customerNumber
 * @param {*} skuNo
 */
export function enquiryList (customerId, skuNo, page = 1) {
  const formData = new FormData()
  formData.append('customerId', customerId)
  formData.append('skuNo', skuNo)
  return request({
    url: sellOrderHost + '/inquiryRecords',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: formData
  })
}

export function getContactList (params) {
  return request({
    params,
    url: sellOrderHost + '/v1/contact/list'
  })
}

/**
 * 获取客户订单记录
 * @param {*} customerNumber
 * @param {*} skuNo
 * @param {*} page
 */
export function orderRecordList (customerNumber, skuNo, page = 1) {
  return request({
    url: sellOrderHost + '/v1/saps/soVoucherRecords',
    params: {
      current: page,
      customerCode: customerNumber,
      skuNo
    }
  })
}

export function checkPermission () {
  return request({
    url: sellOrderHost + '/v1/so/template/so/authority/permission'
  })
}

export function checkCommonPermission (salesOrganization) {
  return request({
    url: `${sellOrderHost}/v1/so/template/authority/common/permission?salesOrganization=${salesOrganization}`
  })
}

export function orderDetailPlan (data) {
  return request({
    url: sellOrderHost + '/v1/so/template/so/detail/plan',
    params: data
  })
}

export function stoOrderDetailPlan (data) {
  return request({
    url: `${sellOrderHost}/v1/sto/planItem/list`,
    params: data
  })
}

/**
 * 获取外围订单列表
 * @param {*} data
 */
export function omsOrderList (data) {
  return request({
    url: sellOrderHost + '/v1/order/oms/list',
    method: 'POST',
    data
  })
}

/**
 * so计划行查询
 * @param {*} data
 */
export function sapOrderDetailQuery (data) {
  return request({
    url: sellOrderHost + '/sapOrderDetailItem/query',
    method: 'post',
    data
  })
}

/**
 * 解冻订单
 * @param {*} data
 */
export function freezeOrder (data) {
  return request({
    url: sellOrderHost + '/v4/oms/order/unfreeze',
    method: 'post',
    data
  })
}

/**
 * 更新预测型订单数量
 * @param {*} data
 */
export function updateForecastOrderQty (data) {
  return request({
    url: sellOrderHost + '/v4/oms/forecastOrder/updateQty',
    method: 'post',
    data
  })
}

/**
 * 快速导入sku商品
 * @param {*} data
 */
export function uploadSku (data) {
  return request({
    url: sellOrderHost + '/v1/sku/uploadSku',
    method: 'post',
    data
  })
}

export function closeForecastOrder (data) {
  return request({
    url: sellOrderHost + '/v4/oms/forecastOrder/close',
    method: 'post',
    data
  })
}

export function undoAllocate (data) {
  return request({
    url: sellOrderHost + '/v4/oms/order/undoAllocate',
    method: 'post',
    data
  })
}

export function exchange (params) {
  return request({
    url: sellOrderHost + '/v1/common/rate/exchange',
    params
  })
}

export function searchMaterial (params) {
  return request({
    url: sellOrderHost + '/v1/sku/customer/relation',
    method: 'post',
    params
  })
}

export function exportContract (params) {
  return request({
    url: '/export',
    params
  })
}

export function atpList (params) {
  return request({
    url: sellOrderHost + '/v1/atp/result/list',
    params
  })
}

export function atpRealtimeList (params) {
  return request({
    url: sellOrderHost + '/v1/atp/rt/result/list',
    params
  })
}

export function upload (data) {
  return request({
    url: sellOrderHost + '/v1/excel/batchImportSku',
    method: 'post',
    data
  })
}

export function syncSap (soNo) {
  return request({
    url: sellOrderHost + `/v4/oms/order/syncSap?soNo=${soNo}`,
    method: 'post'
  })
}

export function isAddSkuBtnDisplay (params) {
  return request({
    url: sellOrderHost + '/v1/so/template/isAddSkuBtnDisplay',
    method: 'get',
    params
  })
}

export function triggerAtp (soNo) {
  return request({
    url: atpRealtimeHost + '/real-time-atp/so',
    method: 'post',
    data: {
      demandNo: soNo,
      demandItemList: []
    }
  })
}

export function triggerStoAtp (soNo) {
  return request({
    url: atpRealtimeHost + '/real-time-atp/sto',
    method: 'post',
    data: {
      demandNo: soNo,
      demandItemList: []
    }
  })
}

export function batchUploadSo (orderType, file) {
  return request({
    url: sellOrderHost + '/v1/excel/batchImportSo',
    method: 'post',
    data: {
      orderType,
      file
    }
  })
}

export function unfreezeApi (omsNo, creditLimitFreeze, overdueFreeze, timeoutUninvoicedFreeze) {
  return request({
    url: sellOrderHost + '/v4/oms/order/credit/unfreeze',
    method: 'post',
    data: {
      omsNo,
      creditLimitFreeze,
      overdueFreeze,
      timeoutUninvoicedFreeze
    }
  })
}

export function refreshCreditApi (omsNo) {
  return request({
    url: sellOrderHost + '/v4/oms/order/creditLimit/refresh',
    method: 'post',
    data: {
      omsNo
    }
  })
}

export function rebuild (params) {
  return request({
    url: sellOrderHost + '/v4/oms/evm/consignmentWithdraw/rebuild/plan',
    method: 'GET',
    params
  })
}

/**
 * 获取集货仓
 * @param {*} data
 */
export function getDeliveryWarehouse (data) {
  return request({
    url: `${omsConfig}/supply-network/query/common`,
    method: 'post',
    data
  })
}

/**
 * 差异报表查询接口
 * @param {*} data
 */
export const queryDifferenceReport = data => {
  return request({
    url: `${sellOrderHost}/differenceReport/query`,
    method: 'post',
    data
  })
}

export function getDeliveryTime (data, params) {
  return request({
    url: sellOrderHost + '/v4/oms/order/calculateWaveDeliveryDate',
    method: 'POST',
    data,
    params
  })
}
// export function getDeliveryTime (data) {
//   return request({
//     url: simApi + '/sku-estimated-delivery-date',
//     method: 'POST',
//     data
//   })
// }
export function repushFailuredSplitOrder(params) {
  return request({
    url: sellOrderHost + '/v1/order/ops/retry',
    params
  })
}

export function getSkuPic(params) {
  return request({
    url: sellOrderHost + '/v1/sku/pic',
    params
  })
}
export function getCustomerCredit(params) {
  return request({
    url: sellOrderHost + '/v1/customer/credit/query',
    params
  })
}
export function deleteDraft(params) {
  return request({
    url: sellOrderHost + '/v4/oms/sketchOrder/delete',
    method: 'POST',
    params
  });
}
export function saveDraft(data, createWorkList = false, afterCreateValidateError = false) {
  return request({
    url: `${sellOrderHost}/v2/sketch/save?createWorkList=${createWorkList}&afterCreateValidateError=${afterCreateValidateError}`,
    method: 'POST',
    data
  });
}
// 转正式订单失败埋点
export function transformSoFailDataPoint(data) {
  return request({
    url: `${sellOrderHost}/v2/sketch/transformSoFailDataPoint`,
    method: 'POST',
    data
  });
}
// 正式订单新增行失败创建or加回草稿单
export function createOrAddSketch(data, createWorkList = false) {
  return request({
    url: `${sellOrderHost}/v2/sketch/fromSo/createOrAddSketch?createWorkList=${createWorkList}`,
    method: 'POST',
    data
  });
}
export function createWorkOrderApi(data) {
  return request({
    url: sellOrderHost + '/workOrderApi/salePrice/create',
    method: 'post',
    data
  });
}

export function batchCreateSketch(params, data) {
  return request({
    url: sellOrderHost + '/v4/oms/order/batchCreateSketch',
    method: 'POST',
    params,
    data
  });
}

export function getCustomizeOrderConfig () {
  return request({
    url: '/api-acm-config?methodType=getConfig&id=customize-order',
    method: 'get'
  })
}
// 隐藏获取更多仓位入口配置
export function getMorePositionDisabledConfig () {
  return request({
    url: '/api-acm-config?methodType=getConfig&id=more-position-disabled',
    method: 'get'
  })
}
// 获取批量下载模板地址
export function getTemplateExcelUrls () {
  return request({
    url: '/api-acm-config?methodType=getConfig&id=template-excel-urls',
    method: 'get'
  })
}

export function getOrderServiceMap() {
  return request({
    url: sellOrderHost + '/v1/kh-mdm/order/service/list',
    method: 'GET'
  })
}

export function getContactById (params) {
  return request({
    params,
    url: sellOrderHost + '/v1/customer/contact/get'
  })
}
export function updateAcceptDemand (data) {
  return request({
    data,
    url: sellOrderHost + '/v4/oms/order/update',
    method: 'POST'
  })
}
// 编辑订单更新接口
export function updateOrder (data) {
  return request({
    data,
    url: sellOrderHost + '/v4/oms/order/updateWithValidatorResult',
    method: 'POST'
  })
}
export function deliveryNoteTips (data) {
  return request({
    data,
    url: sellOrderHost + '/deliveryNote/tips',
    method: 'POST'
  })
}

export function getBackToBackRemindInfos (data) {
  return request({
    data,
    url: sellOrderHost + '/v1/so/getBackToBackRemindInfos',
    method: 'POST'
  })
}
export function getOpsAuthorization (params) {
  return request({
    url: sellOrderHost + '/v1/so/ops/authorization/config',
    method: 'get',
    params
  })
}

// 检查用户是否有订单详情查看权限
export function soDetailAuthCheck (data) {
  return request({
    url: omsSoQuery + '/so/view/auth/check',
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify(data)
  })
}

// 查询末端仓到仓库的物流LT
export function queryFinalLT (data) {
  return request({
    url: sellOrderHost + '/v1/so/queryFinalLT',
    method: 'POST',
    data
  })
}

export function getFileUrl(ossKey) {
  return request({
    url: '/documentApi/v1/ecorp/downloadFile',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: qs.stringify({
      ossKeyName: ossKey
    })
  });
}

// 保存文件元数据
export function saveDocumentMetaData (data) {
  return request({
    url: sellOrderHost + '/v1/doc/saveDocumentMetaData',
    method: 'POST',
    data
  })
}

// 修改账期下拉查询
export function queryPaymentTerms (params) {
  return request({
    url: sellOrderHost + '/v1/so/template/query/paymentTerms',
    method: 'get',
    params
  })
}
