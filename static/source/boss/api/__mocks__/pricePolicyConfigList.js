const pricePolicyListResponse = {
  "code": 200,
  "msg": "成功",
  "data": [{
    "id": "1300549031917002752",
    "materialGroupNo": null,
    "materialGroup": "个人防护",
    "primaryDirectoryNo": null,
    "primaryDirectory": "个人防护",
    "dynPolicyName": "lxd动态调价策略-个人防护",
    "dynStatusCode": 1,
    "dynStatusName": "策略已维护",
    "dynComputeTime": "2021-02-22 13:11:52",
    "healthPolicyName": "健康度策略(默认)",
    "healthStatusCode": 0,
    "healthStatusName": "策略待维护",
    "healthComputeTime": "2021-02-26 17:46:12",
    "commodityRange": 0,
    "commodityManager": null,
    "commodityDirector": null,
    "creator": "",
    "updater": "xiaodong1.li",
    "createTime": "2021-02-22 13:11:52",
    "updateTime": "2021-02-26 17:46:12",
    "brandLadderQuantity": 0,
    "addedValueQuantity": 0,
    "commodityRangeName": "默认",
    "skuQuantity": null
  }, {
    "id": "1300963249111703552",
    "materialGroupNo": null,
    "materialGroup": "球类",
    "primaryDirectoryNo": null,
    "primaryDirectory": "篮球",
    "dynPolicyName": "0301动态调价策略-个人防护",
    "dynStatusCode": 2,
    "dynStatusName": "计算中",
    "dynComputeTime": "2021-02-23 16:37:49",
    "healthPolicyName": "0301健康度策略",
    "healthStatusCode": 0,
    "healthStatusName": "策略待维护",
    "healthComputeTime": "2021-03-01 11:47:14",
    "commodityRange": 1,
    "commodityManager": null,
    "commodityDirector": null,
    "creator": "xiqi.jia",
    "updater": "xiqi.jia",
    "createTime": "2021-02-23 16:37:49",
    "updateTime": "2021-03-01 11:47:14",
    "brandLadderQuantity": 0,
    "addedValueQuantity": 0,
    "commodityRangeName": "指定范围",
    "skuQuantity": null
  }],
  "totalCount": 2
}

const getProductPositioningAndTradeOptionsResponse = {
	"code": 200,
	"msgs": ["接口调用成功"],
	"result": {
		"ProductTrade": [{
			"optionSetId": 2139,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "00",
			"displayName": "通用",
			"displayOrder": 1
		}, {
			"optionSetId": 2140,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "01",
			"displayName": "矿业行业",
			"displayOrder": 2
		}, {
			"optionSetId": 2142,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "03",
			"displayName": "水泥建材",
			"displayOrder": 3
		}, {
			"optionSetId": 2143,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "04",
			"displayName": "化工行业",
			"displayOrder": 4
		}, {
			"optionSetId": 2144,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "05",
			"displayName": "食品农副",
			"displayOrder": 5
		}, {
			"optionSetId": 2147,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "08",
			"displayName": "电力行业",
			"displayOrder": 6
		}, {
			"optionSetId": 2148,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "09",
			"displayName": "航天军工",
			"displayOrder": 7
		}, {
			"optionSetId": 2149,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "10",
			"displayName": "冶金行业",
			"displayOrder": 8
		}, {
			"optionSetId": 2150,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "11",
			"displayName": "建筑行业",
			"displayOrder": 9
		}, {
			"optionSetId": 2306,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "12",
			"displayName": "电子行业",
			"displayOrder": 10
		}, {
			"optionSetId": 2307,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "13",
			"displayName": "电气行业",
			"displayOrder": 11
		}, {
			"optionSetId": 2308,
			"optionSetTypeCode": "ProductTrade",
			"optionSetTypeName": "推荐行业",
			"optionSetValue": "14",
			"displayName": "汽车行业",
			"displayOrder": 12
		}],
		"ProductPositioning": [{
			"optionSetId": 212,
			"optionSetTypeCode": "ProductPositioning",
			"optionSetTypeName": "产品定位",
			"optionSetValue": "1",
			"displayName": "行家精选",
			"displayOrder": 1
		}, {
			"optionSetId": 213,
			"optionSetTypeCode": "ProductPositioning",
			"optionSetTypeName": "产品定位",
			"optionSetValue": "2",
			"displayName": "规格商品",
			"displayOrder": 2
		}, {
			"optionSetId": 2305,
			"optionSetTypeCode": "ProductPositioning",
			"optionSetTypeName": "产品定位",
			"optionSetValue": "3",
			"displayName": "主推商品",
			"displayOrder": 3
		}, {
			"optionSetId": 2161,
			"optionSetTypeCode": "ProductPositioning",
			"optionSetTypeName": "产品定位",
			"optionSetValue": "4",
			"displayName": "询价商品",
			"displayOrder": 4
		}, {
			"optionSetId": 215,
			"optionSetTypeCode": "ProductPositioning",
			"optionSetTypeName": "产品定位",
			"optionSetValue": "5",
			"displayName": "一般商品",
			"displayOrder": 5
		}]
	},
	"validationResults": null,
	"data": null,
	"message": null,
	"sucess": true
}

const getStatusOptionsResponse = {
	"code": 200,
	"msg": "成功",
	"data": {
		"0": "策略待维护",
		"1": "策略已维护",
		"2": "计算中",
		"3": "计算成功",
		"4": "计算失败"
	},
	"totalCount": 1
}

const getSPUOptionsResponse = {
	"code": 200,
	"msg": "成功",
	"data": {
		"1": "头部",
		"2": "颈部",
		"3": "腰部",
		"4": "尾部",
		"5": "未动销"
	},
	"totalCount": 1
}

const getBrandListApiResponse = {
	"rows": [{
		"createdBy": 1,
		"createdOn": "2013-08-21 09:23:42",
		"modifiedBy": 1,
		"modifiedOn": "2021-03-01 12:00:07",
		"tenantId": null,
		"brandId": 198177,
		"brandName": "SIEMENS/西门子",
		"brandIntroduction": "西门子股份公司是全球电子电气工程领域的领先企业，创立于1847年，主要业务集中在工业、能源、医疗、基础设施与城市四大业务领域。",
		"ifAgentCertification": 0,
		"ifObm": 0,
		"stateCode": 1,
		"description": "測試更新10000條",
		"brandStatus": 2,
		"approveUser": null,
		"approveComment": null,
		"approveTime": null,
		"ifWebDisplay": null
	}],
	"totalPage": 0,
	"rowCount": 0,
	"pageNo": 0,
	"totalRecord": 0
}

const getCommodityRangeResponse = {
	"code": 200,
	"msg": "成功",
	"data": {
		"relateId": "1300963249111703552",
		"brands": ["198177"],
		"directorys": [{
			"firstLevelDirectory": "259426",
			"secondLevelDirectory": "259454",
			"threeLevelDirectory": "260158",
			"fourthLevelDirectory": "261417"
		}, {
			"firstLevelDirectory": "259426",
			"secondLevelDirectory": "259454",
			"threeLevelDirectory": "260158",
			"fourthLevelDirectory": "261963"
		}, {
			"firstLevelDirectory": "259426",
			"secondLevelDirectory": "259454",
			"threeLevelDirectory": "260158",
			"fourthLevelDirectory": "262146"
		}],
		"productPositioning": "215",
		"recommendedIndustry": "2139",
		"spuProductLevel": 1
	},
	"totalCount": 1
}

export function getStatusOptions() {
  return new Promise((resolve, reject) => {
    resolve(getStatusOptionsResponse)
  })
}

export function getPricePolicyList() {
  return new Promise((resolve, reject) => {
    resolve(pricePolicyListResponse)
  })
}
export function getSPUOptions() {
  return new Promise((resolve, reject) => {
    resolve(getSPUOptionsResponse)
  })
}
export function getBrandListApi() {
  return new Promise((resolve, reject) => {
    resolve(getBrandListApiResponse)
  })
}
export function getProductPositioningAndTradeOptions() {
  return new Promise((resolve, reject) => {
    resolve(getProductPositioningAndTradeOptionsResponse)
  })
}
export function getCommodityRange() {
  return new Promise((resolve, reject) => {
    resolve(getCommodityRangeResponse)
  })
}
export function copyCommodityRange() {
  return new Promise((resolve, reject) => {
    resolve({ code: 200 })
  })
}
export function updateCommodityRange() {
  return new Promise((resolve, reject) => {
    resolve({ code: 200 })
  })
}
export function setTargetRate() {
  return new Promise((resolve, reject) => {
    resolve({ code: 200 })
  })
}