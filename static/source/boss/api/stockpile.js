import request, { downloadFile } from '@/utility/request'
const prefix = '/data-center-front'

let brandOptions
export function getBrandOptions() {
  return new Promise(resolve => {
    if (brandOptions) {
      resolve(brandOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/brand/name`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          brandOptions = res.data.map(item => {
            return {
              label: item.v,
              value: item.k
            }
          })
        }
        resolve(brandOptions)
      })
    }
  })
}

let materialGroupOptions
export function getMaterialGroupOptions() {
  return new Promise(resolve => {
    if (materialGroupOptions) {
      resolve(materialGroupOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/material/group`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          materialGroupOptions = res.data.map(item => {
            return {
              label: item.v,
              value: item.k
            }
          })
        }
        resolve(materialGroupOptions)
      })
    }
  })
}

let productManagerOptions
export function getProductManagerOptions() {
  return new Promise(resolve => {
    if (productManagerOptions) {
      resolve(productManagerOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/product/manager`,
        method: 'get'
      }).then(res => {
        productManagerOptions = []
        if (res.code === 200 && res.data && res.data.length > 0) {
          let obj = {}
          res.data.forEach(item => {
            !obj[item.k] && productManagerOptions.push({
              label: item.v,
              value: item.k
            })
            obj[item.k] = true
          })
          productManagerOptions = productManagerOptions.sort((a, b) => a.label.localeCompare(b.label, 'zh-Hans-CN', { sensitivity: 'accent' }))
        }
        resolve(productManagerOptions)
      })
    }
  })
}

let stockPositionCodeOptions
export function getStockPositionCodeOptions() {
  return new Promise(resolve => {
    if (stockPositionCodeOptions) {
      resolve(stockPositionCodeOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/stock/location`,
        method: 'get'
      }).then(res => {
        stockPositionCodeOptions = []
        if (res.code === 200 && res.data && res.data.length > 0) {
          let obj = {}
          res.data.forEach(item => {
            !obj[item.k] && stockPositionCodeOptions.push({
              label: item.v,
              value: item.k
            })
            obj[item.k] = true
          })
        }
        resolve(stockPositionCodeOptions)
      })
    }
  })
}

export function getSkuOptions(sku, type) {
  let url = '';

  switch (type) {
    case 'audit':
      url = 'stockup/stock/audit/sku';
      break;
    case 'history':
      url = 'stockup/stock/audit/history/sku';
      break;
    case 'blacklist':
      url = 'stockup/stock/audit/blacklist/sku';
      break;
  }

  if (!url) {
    return;
  }

  return request({
    url: `${prefix}/${url}/${sku}`,
    method: 'get'
  }).then(res => {
    if (res.code === 200 && res.data && res.data.length > 0) {
      return res.data.map(item => {
        return {
          materialDescription: item.v,
          skuNo: item.k
        }
      })
    }
  })
}

let remarkMarkOptions
export function getRemarkMarkOptions() {
  return new Promise(resolve => {
    if (remarkMarkOptions) {
      resolve(remarkMarkOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/remark/mark`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data) {
          remarkMarkOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(remarkMarkOptions)
      })
    }
  })
}

let statusOptions
export function getStatusOptions() {
  return new Promise(resolve => {
    if (statusOptions) {
      resolve(statusOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/status/dropdown`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data) {
          statusOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key],
              value: Number(key)
            }
          })
        }
        resolve(statusOptions)
      })
    }
  })
}

let stockMarkOptions
export function getStockMarkOptions() {
  return new Promise(resolve => {
    if (stockMarkOptions) {
      resolve(stockMarkOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/stock/mark`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data) {
          stockMarkOptions = Object.keys(res.data).map(key => {
            return {
              label: res.data[key] ? res.data[key] : ' ',
              value: Number(key)
            }
          })
        }
        resolve(stockMarkOptions)
      })
    }
  })
}

export function getCurrentVersion() {
  return request({
    url: `${prefix}/stockup/stock/audit/current/version`,
    method: 'get'
  })
}

export function getBasisList(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/list`,
    method: 'post',
    data
  })
}

export function updateField(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/update`,
    method: 'put',
    data
  })
}
// TODO: 账号角色必须为 产线审核或库存策略 此时会下载html文件
export function exportBasisApi(data) {
  return downloadFile(`${prefix}/stockup/stock/audit/export`, data, {
    method: 'POST'
  })
}

export function approveAll(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/submit/all/review`,
    method: 'put',
    data
  })
}

export function approveBatched(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/submit/review`,
    method: 'put',
    data
  })
}

export function acceptAll(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/review/passed/all`,
    method: 'put',
    data
  })
}

export function acceptBatched(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/review/passed`,
    method: 'put',
    data
  })
}

export function readjustBatched(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/review/adjust`,
    method: 'put',
    data
  })
}

export function rejectAll(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/review/reject/all`,
    method: 'put',
    data
  })
}

export function rejectBatched(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/review/reject`,
    method: 'put',
    data
  })
}

let historyVersionOptions
export function getHistoryVersionOptions() {
  return new Promise(resolve => {
    if (historyVersionOptions) {
      resolve(historyVersionOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/history/version`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          historyVersionOptions = res.data.map(item => {
            return {
              label: item,
              value: item
            }
          })
        }
        resolve(historyVersionOptions)
      })
    }
  })
}

let historyProductManagerOptions
export function getHistoryProductManagerOptions() {
  return new Promise(resolve => {
    if (historyProductManagerOptions) {
      resolve(historyProductManagerOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/history/product/manager`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          historyProductManagerOptions = res.data.map(item => {
            return {
              label: item.v,
              value: item.k
            }
          }).sort((a, b) => a.label.localeCompare(b.label, 'zh-Hans-CN', { sensitivity: 'accent' }))
        }
        resolve(historyProductManagerOptions)
      })
    }
  })
}

let historyMaterialGroupOptions
export function getHistoryMaterialGroupOptions() {
  return new Promise(resolve => {
    if (historyMaterialGroupOptions) {
      resolve(historyMaterialGroupOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/history/material/group`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          historyMaterialGroupOptions = res.data.map(item => {
            return {
              label: item.v,
              value: item.k
            }
          })
        }
        resolve(historyMaterialGroupOptions)
      })
    }
  })
}

export function getHistoryBasisList(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/history/list`,
    method: 'post',
    data
  })
}

// TODO: 账号角色必须为 产线审核或库存策略 此时会下载html文件
export function exportHistory(data) {
  return downloadFile(`${prefix}/stockup/stock/audit/history/export`, data, {
    method: 'POST'
  })
}

let blacklistBrandOptions
export function getBlacklistBrandOptions() {
  return new Promise(resolve => {
    if (blacklistBrandOptions) {
      resolve(blacklistBrandOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/blacklist/brand/name`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          blacklistBrandOptions = res.data.map(item => {
            return {
              label: item.v,
              value: item.k
            }
          })
        }
        resolve(blacklistBrandOptions)
      })
    }
  })
}

let blacklistMaterialGroupOptions
export function getBlacklistMaterialGroupOptions() {
  return new Promise(resolve => {
    if (blacklistMaterialGroupOptions) {
      resolve(blacklistMaterialGroupOptions)
    } else {
      request({
        url: `${prefix}/stockup/stock/audit/blacklist/material/group`,
        method: 'get'
      }).then(res => {
        if (res.code === 200 && res.data && res.data.length > 0) {
          blacklistMaterialGroupOptions = res.data.map(item => {
            return {
              label: item.v,
              value: item.k
            }
          })
        }
        resolve(blacklistMaterialGroupOptions)
      })
    }
  })
}

export function exportBlacklistBasisApi(data) {
  return downloadFile(`${prefix}/stockup/stock/audit/blacklist/export`, data, {
    method: 'POST'
  })
}

export function getBlacklistBasisList(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/blacklist/list`,
    method: 'post',
    data
  })
}

export function deleteRow(id) {
  return request({
    url: `${prefix}/stockup/stock/audit/blacklist/delete/${id}`,
    method: 'put'
  })
}

export function deleteBatched(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/blacklist/delete/batch`,
    method: 'put',
    data
  })
}

export function addBlacklist(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/blacklist/add`,
    method: 'post',
    data
  })
}

export function stockStaffCheckAll(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/review/all`,
    method: 'put',
    data
  })
}

export function stockStaffReIssue(data) {
  return request({
    url: `${prefix}/stockup/stock/audit/reissue`,
    method: 'put',
    data
  })
}
