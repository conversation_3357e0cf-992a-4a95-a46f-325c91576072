import request from '@/utility/request'

const sellOrderHost = '/api-opc'

export function list (data) {
  return request({
    url: sellOrderHost + '/v1/sto/list',
    method: 'post',
    data
  })
}

export function listStoDN (data) {
  return request({
    url: `${sellOrderHost}/stoDn/query`,
    method: 'post',
    data
  })
}

export function create (data) {
  return request({
    url: sellOrderHost + '/stoDn/create',
    method: 'POST',
    data
  })
}

export function getDN (id) {
  return request({
    url: `${sellOrderHost}/stoDn/getDetail`,
    method: 'post',
    data: { id }
  })
}

export function cancelDN (data) {
  return request({
    url: `${sellOrderHost}/stoDn/cancel`,
    method: 'post',
    data
  })
}

export function submitDN (data) {
  return request({
    url: `${sellOrderHost}/stoDn/submit`,
    method: 'post',
    data
  })
}

export function stoFactoryList () {
  return request({
    url: sellOrderHost + '/v1/sto/stoSupportedFactories'
  })
}
