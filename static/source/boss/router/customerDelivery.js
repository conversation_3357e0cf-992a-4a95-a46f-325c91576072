// SUGGESTION: valet ordering
import { favIconMap } from '@/utils/config.js'

export default {
  path: '/customerDelivery/',
  component: () => import('@/components/Router.vue'),
  meta: {
    breadcrumb: ['客户交付画像'],
    keepAlive: true, // 采用缓存
    tagName: '客户交付画像',
    favIcon: favIconMap.cm
  },
  children: [
    {
      path: 'rolePowerManage',
      component: () => import('@/pages/customerDelivery/rolePowerManage'),
      name: 'dataManageImport',
      meta: {
        breadcrumb: ['客户交付画像', '角色权限管理'],
        keepAlive: true, // 采用缓存
        tagName: '角色权限管理'
      }
    },
    {
      path: 'factoryList',
      component: () => import('@/pages/customerDelivery/factoryList'),
      name: 'factoryList',
      meta: {
        breadcrumb: ['客户交付画像', '客户工厂列表'],
        keepAlive: true, // 采用缓存
        tagName: '客户工厂列表'
      }
    },
    {
      path: 'factoryList/detail/:factoryId',
      component: () => import('@/pages/customerDelivery/factoryDetail'),
      name: 'factoryDetail',
      meta: {
        breadcrumb: ['客户交付画像', '工厂详情'],
        keepAlive: true, // 采用缓存
        tagName: '工厂详情'
      }
    },
    {
      path: 'responsibleArea',
      component: () => import('@/pages/customerDelivery/responsibleArea'),
      name: 'responsibleArea',
      meta: {
        breadcrumb: ['客户交付画像', '服务中心负责区域'],
        keepAlive: false, // 采用缓存
        tagName: '服务中心负责区域'
      }
    },
    {
      path: 'uploadList',
      component: () => import('@/pages/customerDelivery/uploadList'),
      name: 'uploadList',
      meta: {
        breadcrumb: ['客户交付画像', '交付画像上传记录'],
        keepAlive: false, // 采用缓存
        tagName: '交付画像上传记录'
      }
    },
    {
      path: 'modifyLog',
      component: () => import('@/pages/customerDelivery/modifyLog'),
      name: 'modifyLog',
      meta: {
        breadcrumb: ['客户交付画像', '修改历史记录'],
        keepAlive: false, // 采用缓存
        tagName: '修改历史记录'
      }
    }
  ]
}
