// boss设置
// 保留此路由，做旧兼容系统的重定向
export default {
  path: '/priceAudit/',
  name: 'priceAudit',
  meta: {
    breadcrumb: ['价格审批'],
    keepAlive: true, // 采用缓存
    tagName: '价格审批',
    redirect (to) {
      let oldPath = to.fullPath
      let targetPath = oldPath.replace('/priceAudit/', '/pa/priceAudit/')
      window.location.replace(targetPath)
    }
  },
  // component: () =>
  //   import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'list',
      name: 'list',
      meta: {
        breadcrumb: ['价格审批', '价格审批列表'],
        keepAlive: true,
        tagName: '价格审批列表'
      }
      // component: () =>
      //   import(
      //     /* webpackChunkName: "price-audit" */ '@/pages/priceAudit/list.vue'
      //   )
    },
    {
      path: 'audit/:id?',
      name: 'priceAuditDynamic',
      meta: {
        breadcrumb: ['价格审批', '价格审批'],
        keepAlive: true,
        tagName: '价格审批'
        // redirect (to) {
        //   window.location.replace()
        // }
      }
      // component: () =>
      //   import(
      //     /* webpackChunkName: "price-audit" */ '@/pages/priceAudit/audit.vue'
      //   )
    }
  ]
}
