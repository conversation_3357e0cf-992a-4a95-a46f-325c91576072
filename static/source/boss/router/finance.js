export default {
  path: '/financialManagement/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['认款平台'],
    keepAlive: true, // 采用缓存
    tagName: '认款平台'
  },
  children: [
    {
      path: 'receivePayment',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/financialManagement/receivePayment.vue'
        ),
      name: 'receivePayment',
      meta: {
        breadcrumb: ['认款平台', '到款认领'],
        keepAlive: true, // 采用缓存
        tagName: '到款认领'
      },
      redirect(to, from) {
        const oldPath = to.fullPath;
        window.location.replace(oldPath.replace('/financialManagement/', '/fr/financialManagement/'))
      }
    }
  ]
}
