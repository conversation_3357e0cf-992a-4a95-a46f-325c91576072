export default {
  path: '/inventoryInquiry',
  name: 'InventoryInquiry',
  meta: {
    breadcrumb: ['库存查询'],
    keepAlive: true, // 采用缓存
    tagName: '库存查询'
  }, //  meta: ['订单页'],
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'supplyDetail',
      name: 'SupplyDetail',
      meta: {
        breadcrumb: ['库存查询', '商品供需明细表'],
        keepAlive: true, // 采用缓存
        tagName: '商品供需明细表'
      },
      component: () =>
        import(
          /* webpackChunkName: "sale-order-list" */ '@/pages/supplyDetail/list'
        )
    }
  ]
}
