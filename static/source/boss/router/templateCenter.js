export default {
  path: '/template-center/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['模板中心'],
    keepAlive: true, // 采用缓存
    tagName: '模板中心'
  }, //  {
  //   title: '代客下单',
  //   icon: 'search'
  // },
  // alwaysShow: true,
  children: [
    {
      path: '',
      component: () =>
        import(
          /* webpackChunkName: "template-center-list" */ '@/pages/templateCenter/list.vue'
        ),
      name: 'tcList',
      meta: {
        breadcrumb: ['模板中心', '模板查询'],
        keepAlive: true, // 采用缓存
        tagName: '模板查询'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'add',
      component: () =>
        import(
          /* webpackChunkName: "template-center-set" */ '@/pages/templateCenter/set.vue'
        ),
      name: 'addTcList',
      meta: {
        breadcrumb: ['模板中心', '新增模板'],
        keepAlive: true, // 采用缓存
        tagName: '新增模板'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'edit/:templateId',
      component: () =>
        import(
          /* webpackChunkName: "template-center-set" */ '@/pages/templateCenter/set.vue'
        ),
      name: 'editTcList',
      meta: {
        breadcrumb: ['模板中心', '编辑模板'],
        keepAlive: true, // 采用缓存
        tagName: '编辑模板'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'invoiceDocuemnt',
      component: () => import(/* webpackChunkName: "template-center-invoice" */ '@/pages/templateCenter/Invoice.vue'),
      name: 'invoiceDocuemnt',
      meta: {
        breadcrumb: ['模板中心', '获取单据'],
        keepAlive: true,
        tagName: '获取单据'
      }
    }
  ]
}
