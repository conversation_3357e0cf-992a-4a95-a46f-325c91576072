export default {
  path: '/classification/',
  component: () => import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['级库存配置'],
    keepAlive: true, // 采用缓存
    tagName: '级库存配置'
  },
  children: [
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/classification/List'
        ),
      name: 'classificationList',
      meta: {
        breadcrumb: ['级库存配置', '级库存配置列表'],
        keepAlive: true,
        tagName: '级库存配置列表'
      }
    }
  ]
}
