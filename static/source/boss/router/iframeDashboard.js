export default {
  path: '/iframe-dashboard/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['数据大盘'],
    keepAlive: true,
    tagName: '数据大盘'
  },
  children: [
    {
      path: 'omsKunhe',
      component: () =>
        import(
          /* webpackChunkName: "oms-kunhe" */ '@/pages/iframeDashboard/omsKunhe.vue'
        ),
      name: 'omsKunhe',
      meta: {
        breadcrumb: ['数据大盘', 'OMS下发坤合大盘'],
        keepAlive: false,
        tagName: 'OMS下发坤合大盘'
      }
    },
    {
      path: 'omsOrderCenter',
      component: () =>
        import(
          /* webpackChunkName: "oms-order-center" */ '@/pages/iframeDashboard/omsOrderCenter.vue'
        ),
      name: 'omsOrderCenter',
      meta: {
        breadcrumb: ['数据大盘', 'OMS订单中心监控大盘'],
        keepAlive: false,
        tagName: 'OMS订单中心监控大盘'
      }
    },
    {
      path: 'orderSaleManage',
      component: () =>
        import(
          /* webpackChunkName: "order-sale-manage" */ '@/pages/iframeDashboard/orderSaleManage.vue'
        ),
      name: 'orderSaleManage',
      meta: {
        breadcrumb: ['数据大盘', '全渠道订单操作大盘'],
        keepAlive: false,
        tagName: '全渠道订单操作大盘'
      }
    },
    {
      path: 'gbbOrderManage',
      component: () =>
        import(
          /* webpackChunkName: "gbb-order-manage" */ '@/pages/iframeDashboard/gbbOrderManage.vue'
        ),
      name: 'gbbOrderManage',
      meta: {
        breadcrumb: ['数据大盘', '工邦邦订单操作大盘'],
        keepAlive: false,
        tagName: '工邦邦订单操作大盘'
      }
    },
    {
      path: 'kunheCore',
      component: () =>
        import(
          /* webpackChunkName: "kunhe-core" */ '@/pages/iframeDashboard/kunheCore.vue'
        ),
      name: 'kunheCore',
      meta: {
        breadcrumb: ['数据大盘', '坤合核心监控大盘'],
        keepAlive: false,
        tagName: '坤合核心监控大盘'
      }
    },
    {
      path: 'qtsCore',
      component: () =>
        import(
          /* webpackChunkName: "qts-core" */ '@/pages/iframeDashboard/qtsCore.vue'
        ),
      name: 'qtsCore',
      meta: {
        breadcrumb: ['数据大盘', '询报价实时大盘'],
        keepAlive: false,
        tagName: '询报价实时大盘'
      }
    },
    {
      path: 'serviceCenter',
      component: () =>
        import(
          /* webpackChunkName: "service-center" */ '@/pages/iframeDashboard/serviceCenter.vue'
        ),
      name: 'serviceCenter',
      meta: {
        breadcrumb: ['数据大盘', '服务中心大盘'],
        keepAlive: false,
        tagName: '服务中心大盘'
      }
    },
    {
      path: 'indexBI',
      component: () =>
        import(
          /* webpackChunkName: "service-center" */ '@/pages/iframeDashboard/serviceNormal.vue'
        ),
      name: 'indexBI',
      meta: {
        breadcrumb: ['数据大盘', '客服日常看板'],
        keepAlive: false,
        tagName: '客服日常看板'
      }
    },
    {
      path: 'direct',
      component: () =>
        import(
          /* webpackChunkName: "direct" */ '@/pages/iframeDashboard/direct.vue'
        ),
      name: 'direct',
      meta: {
        breadcrumb: ['数据大盘', '直发实时大盘'],
        keepAlive: false,
        tagName: '直发实时大盘'
      }
    },
    {
      path: 'positionModify',
      component: () =>
        import(
          /* webpackChunkName: "position-modify" */ '@/pages/iframeDashboard/positionModify.vue'
        ),
      name: 'positionModify',
      meta: {
        breadcrumb: ['数据大盘', '改仓操作实时大盘'],
        keepAlive: false,
        tagName: '改仓操作实时大盘'
      }
    },
    {
      path: 'receiveAddress',
      component: () =>
        import(
          /* webpackChunkName: "receive-address" */ '@/pages/iframeDashboard/receiveAddress.vue'
        ),
      name: 'receiveAddress',
      meta: {
        breadcrumb: ['数据大盘', '收货实时大盘'],
        keepAlive: false,
        tagName: '收货实时大盘'
      }
    },
    {
      path: 'order',
      component: () =>
        import(
          /* webpackChunkName: "order" */ '@/pages/iframeDashboard/order.vue'
        ),
      name: 'order',
      meta: {
        breadcrumb: ['数据大盘', '订单实时大盘'],
        keepAlive: false,
        tagName: '订单实时大盘'
      }
    },
    {
      path: 'onlineIndicator',
      component: () =>
        import(
          /* webpackChunkName: "onlineIndicator" */ '@/pages/iframeDashboard/onlineIndicator.vue'
        ),
      name: 'onlineIndicator',
      meta: {
        breadcrumb: ['数据大盘', 'ZKH线上化指标实时大盘'],
        keepAlive: false,
        tagName: 'ZKH线上化指标实时大盘'
      }
    }
  ]
}
