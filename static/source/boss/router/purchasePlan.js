export default {
  path: '/purchasePlan/',
  component: () => import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['采购计划'],
    keepAlive: true, // 采用缓存
    tagName: '采购计划'
  },
  children: [
    {
      path: 'salesList',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/SalesList'
        ),
      name: 'SalesList',
      meta: {
        breadcrumb: ['销量预测', '销量预测列表'],
        keepAlive: true,
        tagName: '销量预测列表'
      }
    },
    {
      path: 'toPurchase/:ids?',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/ToPurchase'
        ),
      name: 'ToPurchase',
      meta: {
        breadcrumb: ['采购计划', '转采购计划'],
        keepAlive: true,
        tagName: '转采购计划'
      }
    },
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/PurchaseList'
        ),
      name: 'purchasePlanList',
      meta: {
        breadcrumb: ['采购计划', '采购计划列表'],
        keepAlive: true,
        tagName: '采购计划列表'
      }
    },
    {
      path: 'detail/:id',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/PurchaseDetail'
        ),
      name: 'purchasePlanDetail',
      meta: {
        breadcrumb: ['采购计划', '采购计划详情'],
        keepAlive: true,
        tagName: '采购计划详情'
      }
    },
    {
      path: 'edit/:id',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/PurchaseDetail'
        ),
      name: 'purchasePlanEdit',
      meta: {
        breadcrumb: ['采购计划', '采购计划编辑'],
        keepAlive: true,
        tagName: '采购计划编辑'
      }
    },
    {
      path: 'detailList',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/PurchaseDetailList'
        ),
      name: 'PurchaseDetailList',
      meta: {
        breadcrumb: ['采购计划', '采购计划明细表'],
        keepAlive: true,
        tagName: '采购计划明细表'
      }
    },
    {
      path: 'tenders',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/Tenders'
        ),
      name: 'purchasePlanTenders',
      meta: {
        breadcrumb: ['采购计划', '采购计划招标'],
        keepAlive: true,
        tagName: '采购计划招标'
      }
    },
    {
      path: 'strategyStock',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/StrategyStock'
        ),
      name: 'strategyStock',
      meta: {
        breadcrumb: ['采购计划', '战略备货列表'],
        keepAlive: true,
        tagName: '战略备货列表'
      }
    },
    {
      path: 'strategyStockDetail/:id',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/StrategyStockDetail'
        ),
      name: 'strategyStockDetail',
      meta: {
        breadcrumb: ['采购计划', '战略备货详情'],
        keepAlive: true,
        tagName: '战略备货详情-查看'
      }
    },
    {
      path: 'strategyStockEdit/0',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/StrategyStockEdit'
        ),
      name: 'strategyStockEdit',
      meta: {
        breadcrumb: ['采购计划', '战略备货详情'],
        keepAlive: true,
        tagName: '战略备货详情-创建'
      }
    },
    {
      path: 'strategyStockEdit/:id',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/StrategyStockEdit'
        ),
      name: 'strategyStockEdit',
      meta: {
        breadcrumb: ['采购计划', '战略备货详情'],
        keepAlive: true,
        tagName: '战略备货详情-编辑'
      }
    },
    {
      path: 'createPlan',
      component: () =>
        import(
          /* webpackChunkName: "sale-classification-list" */ '@/pages/purchasePlan/Create'
        ),
      name: 'createPlan',
      meta: {
        breadcrumb: ['采购计划', '战略备货列表'],
        keepAlive: true,
        tagName: '创建采购计划'
      }
    }
  ]
}
