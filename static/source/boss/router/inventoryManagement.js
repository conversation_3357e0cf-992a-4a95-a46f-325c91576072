export default {
  path: '/inventoryManagement/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['库存管理'],
    keepAlive: true,
    tagName: '库存管理'
  },
  children: [
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "inventoryManagement-list" */ '@/pages/inventoryManagement/list.vue'
        ),
      name: 'inventoryManagementList',
      meta: {
        breadcrumb: ['库存管理', '库存申请单列表'],
        keepAlive: true,
        tagName: '库存申请单列表'
      }
    },
    {
      path: 'create',
      component: () =>
        import(
          /* webpackChunkName: "inventoryManagement-create" */ '@/pages/inventoryManagement/create.vue'
        ),
      name: 'inventoryManagementCreate',
      meta: {
        breadcrumb: ['库存管理', '创建库存申请单'],
        keepAlive: true,
        tagName: '创建库存申请单'
      }
    },
    {
      path: 'create/:id',
      component: () =>
        import(
          /* webpackChunkName: "inventoryManagement-create" */ '@/pages/inventoryManagement/create.vue'
        ),
      name: 'inventoryManagementCreateDynamic',
      meta: {
        breadcrumb: ['库存管理', '库存申请单创建'],
        keepAlive: true,
        tagName: '库存申请单创建'
      }
    },
    {
      path: 'detail/:id',
      component: () =>
        import(
          /* webpackChunkName: "inventoryManagement-detail" */ '@/pages/inventoryManagement/detail.vue'
        ),
      name: 'inventoryManagementDetail',
      meta: {
        breadcrumb: ['库存管理', '库存申请单详情'],
        keepAlive: true,
        tagName: '库存申请单详情'
      }
    },
    {
      path: 'edit/:id',
      component: () =>
        import(
          /* webpackChunkName: "inventoryManagement-edit" */ '@/pages/inventoryManagement/create.vue'
        ),
      name: 'inventoryManagementEdit',
      meta: {
        breadcrumb: ['库存管理', '库存申请单修改'],
        keepAlive: true,
        tagName: '库存申请单修改'
      }
    },
    {
      path: 'cargoRightsList',
      component: () =>
        import(
        /* webpackChunkName: "inventoryManagement-cargoRightsList" */ '@/pages/inventoryManagement/cargoRightsList.vue'
        ),
      name: 'cargoRightsRemove',
      meta: {
        breadcrumb: ['库存管理', '寄售货权转移'],
        keepAlive: true,
        tagName: '寄售货权转移'
      }

    }
  ]
}
