export default {
  path: '/tagCenter',
  name: 'tagCenter',
  meta: {
    breadcrumb: ['标签中心'],
    keepAlive: true, // 采用缓存
    tagName: '标签中心'
  },
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'tagObjectManagement',
      component: () =>
        import(
            /* webpackChunkName: "policyRunDetail" */ '@/pages/tagCenter/tagObjectManagement.vue'
        ),
      name: 'tagObjectManagement',
      meta: {
        breadcrumb: ['标签中心', '标签对象管理'],
        keepAlive: true,
        tagName: '标签对象管理'
      }
    },
    {
      path: 'tagManagement',
      component: () =>
        import(
            /* webpackChunkName: "policyRunDetail" */ '@/pages/tagCenter/tagManagement.vue'
        ),
      name: 'tagManagement',
      meta: {
        breadcrumb: ['标签中心', '标签管理'],
        keepAlive: true,
        tagName: '标签管理'
      }
    },
    {
      path: 'tagCatalogManagement',
      component: () =>
        import(
            /* webpackChunkName: "policyRunDetail" */ '@/pages/tagCenter/tagCatalogManagement.vue'
        ),
      name: 'tagCatalogManagement',
      meta: {
        breadcrumb: ['标签中心', '类目管理'],
        keepAlive: true,
        tagName: '类目管理'
      }
    },
    {
      path: 'tagMark',
      component: () =>
        import(
            /* webpackChunkName: "policyRunDetail" */ '@/pages/tagCenter/tagMark.vue'
        ),
      name: 'tagMark',
      meta: {
        breadcrumb: ['标签中心', '打标情况'],
        keepAlive: true,
        tagName: '打标情况'
      }
    }
  ]
}
