// 公告中心
export default {
  path: '/announcementCenter/',
  name: 'announcementCenter',
  meta: {
    breadcrumb: ['公告中心'],
    keepAlive: true, // 采用缓存
    tagName: '公告中心'
  }, // meta: ['订单页'],
  component: () => import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'announcement',
      name: 'announcement',
      meta: {
        breadcrumb: ['公告中心', '公告'],
        keepAlive: true,
        tagName: '公告'
      },
      component: () =>
        import(
          /* webpackChunkName: "announcement" */ '@/pages/announcementCenter/announcement.vue'
        )
    }
  ]
}
