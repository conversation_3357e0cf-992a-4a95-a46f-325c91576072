export default {
  path: '/mrp/',
  component: () => import('@/components/Router.vue'),
  meta: {
    breadcrumb: ['MRP报表'],
    keepAlive: true,
    tagName: 'MRP报表'
  },
  children: [
    {
      path: 'mrpList',
      component: () => import(/* webpackChunkName: "mrp-list" */ '@/pages/mrp/list.vue'),
      name: 'mrpList',
      meta: {
        breadcrumb: ['MRP区域', '列表'],
        keepAlive: true,
        tagName: 'MRP区域设置'
      }
    },
    {
      path: 'whiteList',
      component: () => import(/* webpackChunkName: "mrp-whiteList" */ '@/pages/mrp/whiteList.vue'),
      name: 'whiteList',
      meta: {
        breadcrumb: ['MRP区域', '列表'],
        keepAlive: true,
        tagName: '白名单导入'
      }
    },
    {
      path: 'stockStrategy',
      component: () => import(/* webpackChunkName: "mrp-stockStrategy" */ '@/pages/mrp/stockStrategy.vue'),
      name: 'stockStrategy',
      meta: {
        breadcrumb: ['MRP区域', '列表'],
        keepAlive: true,
        tagName: '备货策略配置'
      }
    },
    {
      path: 'mrpResult',
      component: () => import(/* webpackChunkName: "mrp-mrpResult" */ '@/pages/mrp/mrpResult.vue'),
      name: 'mrpResult',
      meta: {
        breadcrumb: ['MRP区域', '列表'],
        keepAlive: true,
        tagName: 'MRP运算结果'
      }
    },
    {
      path: 'summary',
      component: () => import(/* webpackChunkName: "mrp-summary" */ '@/pages/mrp/summary.vue'),
      name: 'summary',
      meta: {
        breadcrumb: ['MRP区域', '汇总'],
        keepAlive: true,
        tagName: '物料需求报表 - 汇总'
      }
    },
    {
      path: 'detail/:id?',
      component: () => import(/* webpackChunkName: "mrp-detail" */ '@/pages/mrp/detail.vue'),
      name: 'detail',
      meta: {
        breadcrumb: ['MRP区域', '明细'],
        keepAlive: true,
        tagName: '物料需求报表 - 明细'
      }
    },
    {
      path: 'orderTransfer/:ids?',
      component: () => import(/* webpackChunkName: "mrp-orderTransfer" */ '@/pages/mrp/orderTransfer.vue'),
      name: 'orderTransfer',
      meta: {
        breadcrumb: ['MRP区域', '转单'],
        keepAlive: true,
        tagName: '物料需求报表 - 转单'
      }
    },
    {
      path: 'direct',
      component: () => import(/* webpackChunkName: "mrp-direct" */ '@/pages/mrp/direct.vue'),
      name: 'direct',
      meta: {
        breadcrumb: ['MRP区域', '直发'],
        keepAlive: true,
        tagName: '物料需求报表 - 直发'
      }
    },
    {
      path: 'orderHand',
      component: () => import(/* webpackChunkName: "mrp-orderHand" */ '@/pages/mrp/orderHand.vue'),
      name: 'orderHand',
      meta: {
        breadcrumb: ['MRP区域', '列表'],
        keepAlive: true,
        tagName: '自动下单功能'
      }
    },
    {
      path: 'vc-vpi',
      component: () => import(/* webpackChunkName: "mrp-vc-vpi" */ '@/pages/mrp/vcVpi.vue'),
      name: 'vc-vpi',
      meta: {
        breadcrumb: ['MRP区域', '列表'],
        keepAlive: true,
        tagName: 'VPI订单需求'
      }
    },
    {
      path: 'grossMargin',
      name: 'grossMargin',
      meta: {
        breadcrumb: ['MRP区域', '低负毛利配置'],
        keepAlive: true,
        tagName: '低负毛利配置'
      },
      component: () =>
        import(
          /* webpackChunkName: "grossMarginlist" */ '@/pages/mrp/grossMargin/list.vue'
        )
    }
  ]
}
