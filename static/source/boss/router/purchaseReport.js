export default {
  path: '/purchaseReport/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['报表'],
    keepAlive: true,
    tagName: '报表'
  },
  children: [
    {
      path: 'inventoryReqList',
      component: () =>
        import(
          /* webpackChunkName: "purchaseReport-inventoryReqList" */ '@/pages/purchaseReport/list.vue'
        ),
      name: 'inventoryReqList',
      meta: {
        breadcrumb: ['报表', '库存申请单明细'],
        keepAlive: true,
        tagName: '库存申请单明细'
      }
    },
    {
      path: 'downLoadList',
      component: () =>
        import(
          /* webpackChunkName: "purchaseReport-downLoadList" */ '@/pages/purchaseReport/downLoadList.vue'
        ),
      name: 'downLoadList',
      meta: {
        breadcrumb: ['报表', '操作日志'],
        keepAlive: true,
        tagName: '下载专区'
      }
    },
    {
      path: 'logList',
      component: () =>
        import(
          /* webpackChunkName: "purchaseReport-logList" */ '@/pages/purchaseReport/operatelogList.vue'
        ),
      name: 'loglist',
      meta: {
        breadcrumb: ['报表', '操作日志'],
        keepAlive: true,
        tagName: '操作日志'
      }
    },
    {
      path: 'trackingOrder',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-tracking-order" */ '@/pages/orderPurchase/trackingOrder.vue'
        ),
      name: 'trackingOrder',
      meta: {
        breadcrumb: ['报表', '跟单报表'],
        keepAlive: true,
        tagName: '跟单报表'
      }
    },
    {
      path: 'realtimeInventory',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-inventory" */ '@/pages/orderPurchase/inventory.vue'
        ),
      name: 'orderPurchaseInventory',
      meta: {
        breadcrumb: ['采购订单', '实时库存'],
        keepAlive: true,
        tagName: '实时库存'
      }
    },
    {
      path: 'outsourcingLinkReport',
      component: () =>
        import(
          /* webpackChunkName: "outsourcing-link-report" */ '@/pages/purchaseReport/outsourcingLinkReport.vue'
        ),
      name: 'outsourcingLinkReport',
      meta: {
        breadcrumb: ['报表', '委外链路'],
        keepAlive: true,
        tagName: '委外链路'
      }
    },
    {
      path: 'expeditingReport',
      component: () =>
        import(
          /* webpackChunkName: "expediting-report" */ '@/pages/purchaseReport/expeditingReport.vue'
        ),
      name: 'expeditingReport',
      meta: {
        breadcrumb: ['报表', '芳生催货报表'],
        keepAlive: true,
        tagName: '芳生催货报表'
      }
    },
    {
      path: 'pickUpGoods',
      component: () =>
        import(
          /* webpackChunkName: "pickUp-goods" */ '@/pages/purchaseReport/pickUpGoods.vue'
        ),
      name: 'pickUpGoods',
      meta: {
        breadcrumb: ['报表', '芳生提货报表'],
        keepAlive: true,
        tagName: '芳生提货报表'
      }
    },
    {
      path: 'distributionReport',
      component: () =>
        import(
          /* webpackChunkName: "distribution-report" */ '@/pages/purchaseReport/distributionReport.vue'
        ),
      name: 'distributionReport',
      meta: {
        breadcrumb: ['报表', '芳生分货报表'],
        keepAlive: true,
        tagName: '芳生分货报表'
      }
    },
    {
      path: 'purchaseProcess',
      component: () =>
        import(
          /* webpackChunkName: "purchase-process" */ '@/pages/purchaseReport/purchaseProcess.vue'
        ),
      name: 'purchaseProcess',
      meta: {
        breadcrumb: ['报表', '采购全流程'],
        keepAlive: true,
        tagName: '采购全流程'
      }
    },
    {
      path: 'inventoryListOfSpecials',
      component: () =>
        import(
          /* webpackChunkName: "purchase-process" */ '@/pages/purchaseReport/inventoryListOfSpecials.vue'
        ),
      name: 'inventoryListOfSpecials',
      meta: {
        breadcrumb: ['报表', '特价商品库存明细表'],
        keepAlive: true,
        tagName: '特价商品库存明细表'
      }
    }
  ]
}
