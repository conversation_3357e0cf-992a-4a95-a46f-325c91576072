// import Vue from 'vue'
// import Router from 'vue-router'
import Boss from '@client/boss'
import store from '@/store'
import { addDynamicRoutes } from '../utils/dynamicRoutes'

import { buildSoDetailLink, isInWhiteList } from '@/utils/index.js'
import routerAccount from './account'
import routerAfterSales from './afterSales'
import routerPurchaseOrder from './purchaseOrder'
import routerInvMgt from './inventoryManagement'
import routerAllocation from './allocation'
import routerAnnoucement from './annoucement'
import routerFinance from './finance'
import routerForecast from './forecast'
import routerClassification from './classification'
import routerPurchasePlan from './purchasePlan'
import routerInsteadOrder from './insteadOrder'
import routerInventory from './inventory'
import routerLast500 from './last500'
import routerLogdetail from './logdetail'
import routerOrderDelivery from './orderDelivery'
import routerPurchase from './purchase'
import routerSap from './sap'
import routerSetting from './setting'
import routerTemplateCenter from './templateCenter'
import routerWarehouse from './warehouse'
import routerShipmentManagement from './shipmentManagement'
import routerConstruction from './construction'
import routerDataManage from './dataManage'
import routerCustomerDelivery from './customerDelivery'
import accountReceivableManagement from './accountReceivableManagement'
import routerRuleCenter from './ruleCenter'
import inboundDeliveryOrder from './inboundDeliveryOrder'
import mrp from './mrp'
import purchaseReport from './purchaseReport'
import routerCustomizedOrder from './customizedOrder'
import stockpileStrategy from './stockpileStrategy'
import messageCenter from './messageCenter'
import priceAudit from './priceAudit' // 保留路由，防止三方系统有依赖，做重定向
// import priceAuditBak from './priceAudit_bak'
import authoritySystem from './authoritySystem'
import tagCenter from './tagCenter'
import _ from 'lodash'
import { favIconMap } from '@/utils/config.js'
const { Router } = Boss

const routerPush = Router.prototype.push

Router.prototype.push = function push(location) {
  console.log(location)
  try {
    if (
      localStorage.getItem('open-in-new-tab') !== 'boss' &&
      isInWhiteList(location)
    ) {
      let query = '?'
      if (location.query && Object.keys(location.query).length) {
        for (let key in location.query) {
          query += key + '=' + location.query[key] + '&'
        }
        query = query.slice(0, -1)
      } else {
        query = ''
      }
      return window.open(location.path + query)
    } else {
      routerPush.call(this, location).catch(error => error)
    }
  } catch (err) {
    console.log(err)
    routerPush.call(this, location).catch(error => error)
  }
}
// 跳转销售订单详情页面，
// 包含hash以支持页面tab切换，
// url路径中混入key来支持导航tab
// query: {soNo, sapOrderNo, id, refresh}
// hash
Router.prototype.jumpToSoOrderDetail = function ({ query, hash, tagName }) {
  const url = buildSoDetailLink({ query, hash, tagName })
  const inPage = query.inPage
  try {
    if (localStorage.getItem('open-in-new-tab') !== 'boss' && !inPage) {
      return window.open(url)
    } else {
      routerPush.call(this, url).catch(error => error)
    }
  } catch (err) {
    console.log(err)
    routerPush.call(this, url).catch(error => error)
  }
}

const option = {
  mode: 'history',
  base: '/',
  routes: [
    {
      path: '/',
      name: 'home',
      meta: {
        breadcrumb: ['首页'],
        keepAlive: true, // 采用缓存
        tagName: '首页'
      }, // meta: ['订单页'],
      component: () => import(/* webpackChunkName: "home" */ '@/pages/Home.vue')
    },
    {
      path: '/workflow/*',
      redirect: to => {
        // 将/workflow/开头的路径重定向到/wf/开头的路径
        const newUrl = to.fullPath.replace('/workflow/', '/wf/')
        window.location.href = newUrl
      }
    },
    // 子路由
    routerAccount,
    routerAfterSales,
    routerAllocation,
    routerAnnoucement,
    routerFinance,
    routerForecast,
    routerClassification,
    routerPurchasePlan,
    routerInsteadOrder,
    routerInventory,
    routerLast500,
    routerLogdetail,
    routerOrderDelivery,
    routerPurchase,
    routerSap,
    accountReceivableManagement,
    routerSetting,
    routerTemplateCenter,
    routerWarehouse,
    routerShipmentManagement,
    routerRuleCenter,
    routerConstruction,
    routerPurchaseOrder,
    routerInvMgt,
    inboundDeliveryOrder,
    mrp,
    purchaseReport,
    routerDataManage,
    routerCustomerDelivery,
    routerCustomizedOrder,
    stockpileStrategy,
    messageCenter,
    priceAudit,
    // priceAuditBak,
    authoritySystem,
    tagCenter
    // {
    //   path: '/workbench',
    //   name: 'workbench',
    //   meta: {
    //     breadcrumb: ['我的工作台'],
    //     keepAlive: true, // 采用缓存
    //     tagName: '我的工作台'
    //   },
    //   component: () => import(/* webpackChunkName: "home" */ '@/pages/workbench')
    // }
    // routerPurchaseOrder //展示隐藏未上线的MM迁移路由
  ]
}

const router = new Router(option)
const addRoutes = (next = () => { }) => {
  if (!window.dashboardRoutesLoaded && !isInWhiteList(location)) {
    addDynamicRoutes('dashboard', router).finally(next)
  } else {
    _.isFunction(next) && next()
  }
}

// 由工作台待办项跳转到boss，且会使用router-tag保活的页面，需要在进入页面前去掉路由中的query部分，
// 否则router-tag会把带query和不带query的路由识别为两个不同的页面，导致保活组件出bug
const routesMayWithQuery = [
  '/orderSale/formal',
  '/customizedOrder/list',
  '/after-sales/list',
  '/inventoryInquiry/supplyDetail',
  '/mrp/direct',
  '/mrp/summary'
]

const isRoutesMayWithQuery = location => {
  const name = location.path || location.pathname
  if (location && name) {
    const founded = routesMayWithQuery.find(item => item === name)
    return !!founded
  }
  return false
}

router.beforeEach((to, from, next) => {
  console.log('to', to);
  // 倒序遍历，即路由匹配度越高的，重定向规则优先级最高
  const redirectItem = _.findLast(to.matched, routeItem => {
    return routeItem?.meta?.redirect
  })
  if (redirectItem) {
    redirectItem.meta.redirect(to, from)
    next(false)
  } else {
    if (!_.isEmpty(to.query) && isRoutesMayWithQuery(to)) {
      localStorage.setItem(to.path, JSON.stringify(to.query))
      next(to.path)
    } else {
      next()
    }
  }
})

router.beforeResolve((to, from, next) => {
  // console.log(store.state)
  if (!store.state.menuLoaded && !isInWhiteList(location)) {
    store
      .dispatch('getMenu')
      .then(menu => {
        // console.log('menu', menu)
        // 没有权限，replace 页面
        if (menu.length === 0) {
          window.location.href = '/unauthorized'
          next()
        } else {
          next()
        }
      })
      .catch(err => {
        console.log(err)
        location.href = '/unauthorized'
      })
  } else {
    next()
  }
})

router.afterEach((to, from) => {
  // 倒序遍历，即路由匹配度越高的，重定向规则优先级最高
  const favIconCustomer = _.findLast(to.matched, routeItem => {
    return routeItem?.meta?.favIcon
  })
  const defaultIcon = favIconMap.boss
  const currentIcon = document.querySelector('link[rel*=\'icon\']')
  if (favIconCustomer) {
    // 当前路由指定了favIcon
    if (currentIcon.href !== favIconCustomer.meta.favIcon) {
      // 当前favIcon不是指定的favIvon
      currentIcon.href = favIconCustomer.meta.favIcon
    }
  } else if (currentIcon.href !== defaultIcon) {
    // 当前路由未指定favIcon，且当前favIcon不是默认值，则置为默认
    currentIcon.href = defaultIcon
  }

  const $wrap = document.querySelector('.el-scrollbar__wrap')
  $wrap && $wrap.scrollTo(0, 0)
})
const constantRouterMap = option.routes
router.$addRoutes = params => {
  router.matcher = new Router({
    mode: 'history',
    base: '/',
    routes: constantRouterMap
  }).matcher
  router.addRoutes(params)
}
router.onReady(addRoutes)
export default router
