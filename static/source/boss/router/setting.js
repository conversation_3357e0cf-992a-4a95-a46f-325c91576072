// boss设置
export default {
  path: '/settings/',
  name: 'settings',
  meta: {
    breadcrumb: ['设置中心'],
    keepAlive: true, // 采用缓存
    tagName: '设置中心'
  },
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'tabSetting',
      name: 'tabSetting',
      meta: {
        breadcrumb: ['设置中心', 'tab设置'],
        keepAlive: true,
        tagName: 'tab设置'
      },
      component: () =>
        import(
          /* webpackChunkName: "tabSetting" */ '@/pages/settings/tabSetting.vue'
        )
    }
  ]
}
