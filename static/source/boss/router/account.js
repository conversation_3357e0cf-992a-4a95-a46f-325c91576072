export default {
  path: '/account-statement-center/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['对账中心'],
    keepAlive: true, // 采用缓存
    tagName: '对账中心',
    redirect (to, from) {
      let oldPath = to.fullPath
      let targetPath = oldPath.replace('/account-statement-center/', '/fi/')
      try {
        if (!from.name) {
          window.location.replace(targetPath)
        } else {
          window.open(targetPath, '_blank')
        }
      } catch (e) {
        console.log(e)
        window.open(targetPath, '_blank')
      }
    }
  },
  children: [
    {
      path: 'basisList',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/basisList.vue'
        ),
      name: 'accoutBasisList',
      meta: {
        breadcrumb: ['对账中心', '对账依据'],
        keepAlive: true, // 采用缓存
        tagName: '对账依据'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'statement',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/statementList.vue'
        ),
      name: 'statementList',
      meta: {
        breadcrumb: ['对账中心', '对账单'],
        keepAlive: true, // 采用缓存
        tagName: '对账单'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    // {
    //   path: 'statement/add',
    //   component: () =>
    //     import(
    //       /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/statementSet.vue'
    //     ),
    //   name: 'statementAdd',
    //   meta: {
    //     breadcrumb: ['对账中心', '创建对账单'],
    //     keepAlive: true, // 采用缓存
    //     tagName: '创建对账单'
    //   } //  meta: {
    //   //   title: '客户商品快捷查询'
    //   // }
    // },
    {
      path: 'statement/detail/:accountId',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/statementDetail.vue'
        ),
      name: 'statementDetail',
      meta: {
        breadcrumb: ['对账中心', '对账单详情'],
        keepAlive: true, // 采用缓存
        tagName: '对账单详情'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'quick-create',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/statementQuickCreate.vue'
        ),
      name: 'quickCreate',
      meta: {
        breadcrumb: ['对账中心', '快捷生成对账单'],
        keepAlive: true, // 采用缓存
        tagName: '快捷生成对账单'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'config',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/configList.vue'
        ),
      name: 'accountList',
      meta: {
        breadcrumb: ['对账中心', '对账配置'],
        keepAlive: true, // 采用缓存
        tagName: '对账配置'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'config/add',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/configSet.vue'
        ),
      name: 'configAdd',
      meta: {
        breadcrumb: ['对账中心', '创建对账配置'],
        keepAlive: true, // 采用缓存
        tagName: '创建对账配置'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'config/edit/:configId',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/accountStatement/configSet.vue'
        ),
      name: 'configEdit',
      meta: {
        breadcrumb: ['对账中心', '编辑对账配置'],
        keepAlive: true, // 采用缓存
        tagName: '编辑对账配置'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'pendingInvoiceList',
      component: () =>
        import(
          /* webpackChunkName: "pendingInvoiceList" */ '@/pages/accountStatement/pendingInvoiceList.vue'
        ),
      name: 'pendingInvoiceList',
      meta: {
        breadcrumb: ['对账中心', '待开票列表'],
        keepAlive: true, // 采用缓存
        tagName: '待开票列表'
      }
    }
  ]
}
