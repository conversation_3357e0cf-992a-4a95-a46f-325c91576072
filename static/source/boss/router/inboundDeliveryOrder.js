export default {
  path: '/inboundDeliveryOrder/',
  component: () => import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['内向交货单'],
    keepAlive: true, // 采用缓存
    tagName: '内向交货单'
  },
  children: [
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "inbound-elivery-order-list" */ '@/pages/inboundDeliveryOrder/list'
        ),
      name: 'inboundDeliveryOrderList',
      meta: {
        breadcrumb: ['内向交货单', '内向交货单列表'],
        keepAlive: true,
        tagName: '内向交货单列表'
      }
    },
    {
      path: 'list2',
      component: () =>
        import(
          /* webpackChunkName: "inbound-elivery-order-list" */ '@/pages/inboundDeliveryOrder/list2'
        ),
      name: 'inboundDeliveryOrderList2',
      meta: {
        breadcrumb: ['内向交货单', '内向交货单列表'],
        keepAlive: true,
        tagName: '内向交货单列表'
      }
    },
    {
      path: 'create/:id',
      component: () =>
        import(
          /* webpackChunkName: "inbound-elivery-order-create" */ '@/pages/inboundDeliveryOrder/create'
        ),
      name: 'inboundDeliveryOrderCreate',
      meta: {
        breadcrumb: ['内向交货单', '内向交货单创建'],
        keepAlive: true,
        tagName: '内向交货单创建'
      }
    },
    {
      path: 'detail/:id',
      component: () =>
        import(
          /* webpackChunkName: "inbound-elivery-order-detail" */ '@/pages/inboundDeliveryOrder/detail'
        ),
      name: 'inboundDeliveryOrderDetail',
      meta: {
        breadcrumb: ['内向交货单', '内向交货单详情'],
        keepAlive: true,
        tagName: '内向交货单详情'
      }
    },
    {
      path: 'edit/:id',
      component: () =>
        import(
          /* webpackChunkName: "inbound-elivery-order-edit" */ '@/pages/inboundDeliveryOrder/edit'
        ),
      name: 'inboundDeliveryOrderEdit',
      meta: {
        breadcrumb: ['内向交货单', '内向交货单修改'],
        keepAlive: true,
        tagName: '内向交货单修改'
      }
    }
  ]
}
