// boss设置
export default {
  path: '/ruleCenter',
  name: 'ruleCenter',
  meta: {
    breadcrumb: ['规则中心'],
    keepAlive: true, // 采用缓存
    tagName: '规则中心'
  },
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'list',
      name: 'ruleCenterList',
      meta: {
        breadcrumb: ['规则中心', '规则中心列表'],
        keepAlive: true,
        tagName: '规则中心列表'
      },
      component: () => import(/* webpackChunkName: "rule-center-list" */ '@/pages/ruleCenter/List.vue')
    }
  ]
}
