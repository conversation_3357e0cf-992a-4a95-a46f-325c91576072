export default {
  path: '/shipmentManagement/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['基础配置'],
    keepAlive: true, // 采用缓存
    tagName: '基础配置'
  },
  children: [
    {
      path: 'basisList',
      component: () =>
        import(
          /* webpackChunkName: "shipment-management-basisList" */ '@/pages/shipmentManagement/basisList.vue'
        ),
      name: 'shipBasisList',
      meta: {
        breadcrumb: ['基础配置', '指定供货渠道/集货仓列表'],
        keepAlive: true, // 采用缓存
        tagName: '指定供货渠道/集货仓列表'
      }
    },
    {
      path: 'designatedChannelAndWareHouse',
      component: () =>
        import(
          /* webpackChunkName: "designated-channel-andWareHouse" */ '@/pages/shipmentManagement/designatedChannelAndWareHouse.vue'
        ),
      name: 'designatedChannelAndWareHouse',
      meta: {
        breadcrumb: ['基础配置', '指定供货渠道/集货仓列表明细'],
        keepAlive: true, // 采用缓存
        tagName: '指定供货渠道/集货仓列表明细'
      }
    },
    {
      path: 'create/:id',
      component: () =>
        import(
          /* webpackChunkName: "shipment-management-creation" */ '@/pages/shipmentManagement/create.vue'
        ),
      name: 'shipmentCreation',
      meta: {
        breadcrumb: ['基础配置', '指定供货渠道/集货仓'],
        keepAlive: true, // 采用缓存
        tagName: '指定供货渠道/集货仓'
      }
    },
    {
      path: 'detail/:id',
      component: () =>
        import(
          /* webpackChunkName: "shipment-management-detail" */ '@/pages/shipmentManagement/detail.vue'
        ),
      name: 'shipmentDetail',
      meta: {
        breadcrumb: ['基础配置', '指定供货渠道/集货仓'],
        keepAlive: true, // 采用缓存
        tagName: '指定供货渠道/集货仓'
      }
    },
    {
      path: 'createStc/:id',
      component: () =>
        import(
          /* webpackChunkName: "shipment-management-creation" */ '@/pages/shipmentManagement/createStc.vue'
        ),
      name: 'shipmentCreationStc',
      meta: {
        breadcrumb: ['基础配置', '指定供货渠道'],
        keepAlive: true, // 采用缓存
        tagName: '创建指定供货渠道'
      }
    },
    {
      path: 'detailStc/:id',
      component: () =>
        import(
          /* webpackChunkName: "shipment-management-detail" */ '@/pages/shipmentManagement/detailStc.vue'
        ),
      name: 'shipmentDetailStc',
      meta: {
        breadcrumb: ['基础配置', '指定供货渠道'],
        keepAlive: true, // 采用缓存
        tagName: '指定供货渠道详情'
      }
    },
    {
      path: 'reportOrder',
      component: () =>
        import(
          /* webpackChunkName: "shipment-management-reportOrder" */ '@/pages/shipmentManagement/reportOrder.vue'
        ),
      name: 'reportOrder',
      meta: {
        breadcrumb: ['基础配置', '报备订单配置'],
        keepAlive: true, // 采用缓存
        tagName: '报备订单配置'
      }
    },
    {
      path: 'performanceRateCFG',
      component: () =>
        import(
          /* webpackChunkName: "performanceRateCFG" */ '@/pages/shipmentManagement/performanceRateCFG/index.vue'
        ),
      name: 'performanceRateCFG',
      meta: {
        breadcrumb: ['基础配置', '仓库履约费率配置'],
        keepAlive: true, // 采用缓存
        tagName: '仓库履约费率配置'
      }
    }
  ]
}
