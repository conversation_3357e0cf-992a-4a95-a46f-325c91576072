export default {
  path: '/construction/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['建筑行业'],
    keepAlive: true,
    tagName: '建筑行业'
  },
  children: [
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "list" */ '@/pages/construction/list.vue'
        ),
      name: 'constructionList',
      meta: {
        breadcrumb: ['建筑行业', '建筑行业项目基础数据管理'],
        keepAlive: true,
        tagName: '建筑行业项目基础数据管理'
      }
    },
    {
      path: 'sale',
      component: () =>
        import(
          /* webpackChunkName: "sale" */ '@/pages/construction/sale.vue'
        ),
      name: 'sale',
      meta: {
        breadcrumb: ['建筑行业', '销售订单采购价确认'],
        keepAlive: true,
        tagName: '销售订单采购价确认'
      }
    },
    {
      path: 'audit',
      component: () =>
        import(
          /* webpackChunkName: "audit" */ '@/pages/construction/audit.vue'
        ),
      name: 'audit',
      meta: {
        breadcrumb: ['建筑行业', '内审申请单'],
        keepAlive: true,
        tagName: '内审申请单'
      }
    },
    {
      path: 'edit/:id',
      component: () =>
        import(
          /* webpackChunkName: "edit" */ '@/pages/construction/edit.vue'
        ),
      name: 'edit',
      meta: {
        breadcrumb: ['建筑行业', '申请单编辑'],
        keepAlive: true,
        tagName: '申请单编辑'
      }
    }
  ]
}
