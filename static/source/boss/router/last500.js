export default {
  path: '/last500/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['最后500米'],
    keepAlive: true,
    tagName: '最后500米'
  },
  children: [
    {
      path: 'areas',
      component: () =>
        import(
          /* webpackChunkName: "last500-area-list" */ '@/pages/last500/AreaList.vue'
        ),
      name: 'last500AreaList',
      meta: {
        breadcrumb: ['最后500米', '服务区域'],
        keepAlive: true,
        tagName: '服务区域'
      }
    },
    {
      path: 'area/:id',
      component: () =>
        import(
          /* webpackChunkName: "last500-area-detail" */ '@/pages/last500/AreaDetail.vue'
        ),
      name: 'last500AreaDetail',
      meta: {
        breadcrumb: ['最后500米', '服务区域配置'],
        keepAlive: false,
        tagName: '服务区域配置'
      }
    }
  ]
}
