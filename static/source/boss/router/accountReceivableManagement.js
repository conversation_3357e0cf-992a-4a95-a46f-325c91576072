export default {
  path: '/account-receivable-management/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['应收款管理'],
    keepAlive: true, // 采用缓存
    tagName: '应收款管理'
  },
  children: [
    {
      path: 'basisList',
      component: () =>
        import(
          /* webpackChunkName: "account-receivable-management" */ '@/pages/accountReceivableManagement/basisList.vue'
        ),
      name: 'receivableBasisList',
      meta: {
        breadcrumb: ['应收款管理', '未开票管理列表'],
        keepAlive: true, // 采用缓存
        tagName: '未开票管理列表'
      },
      redirect(to, from) {
        const oldPath = to.fullPath;
        window.location.replace(oldPath.replace('/account-receivable-management/basisList', '/fi/receivableBasisList?open=true'))
      }
    },
    {
      path: 'dunningLetterList',
      component: () =>
        import(
          /* webpackChunkName: "dunning-letter-list" */ '@/pages/dunningLetter/list.vue'
        ),
      name: 'dunningLetterList',
      meta: {
        breadcrumb: ['应收款管理', '催款函列表'],
        keepAlive: true, // 采用缓存
        tagName: '催款函列表'
      },
      redirect(to, from) {
        const oldPath = to.fullPath;
        window.location.replace(oldPath.replace('/account-receivable-management/', '/fi/'))
      }
    },
    {
      path: 'dunningLetterDetail/:id',
      component: () =>
        import(
          /* webpackChunkName: "dunning-letter-detail" */ '@/pages/dunningLetter/detail.vue'
        ),
      name: 'dunningLetterDetail',
      meta: {
        breadcrumb: ['应收款管理', '催款函详情'],
        keepAlive: true, // 采用缓存
        tagName: '催款函详情'
      },
      redirect(to, from) {
        const oldPath = to.fullPath;
        window.location.replace(oldPath.replace('/account-receivable-management/', '/fi/'))
      }
    }
  ]
}
