export default {
  path: '/warehousing',
  name: 'warehousing',
  meta: {
    breadcrumb: ['销售视角'],
    keepAlive: true, // 采用缓存
    tagName: '出库单'
  },
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      // key为了区分不同详情页，方便tab组件缓存
      path: 'detail/:key',
      name: 'WarehousingDetail',
      meta: {
        breadcrumb: ['销售视角', '出库单详情'],
        keepAlive: true, // 采用缓存
        tagName: '出库单详情'
      },
      component: () =>
        import(
          /* webpackChunkName: "warehousing-detail" */ '@/pages/warehousing/detail'
        )
    }
  ]
}
