export default {
  path: '/authoritySystem/',
  component: () => import('@/components/Router.vue'),
  meta: {
    breadcrumb: ['系统&权限'],
    keepAlive: true,
    tagName: '系统&权限'
  },
  children: [
    {
      path: 'relatedSystem',
      component: () => import(/* webpackChunkName: "authoritySystem-relatedSystem" */ '@/pages/authoritySystem/relatedSystem.vue'),
      name: 'relatedSystem',
      meta: {
        breadcrumb: ['相关系统查询', '列表'],
        keepAlive: true,
        tagName: '相关系统查询'
      }
    },
    {
      path: 'resourceManagement',
      component: () => import(/* webpackChunkName: "authoritySystem-resourceManagement" */ '@/pages/authoritySystem/resourceManagement.vue'),
      name: 'resourceManagement',
      meta: {
        breadcrumb: ['资源管理', '菜单'],
        keepAlive: true,
        tagName: '资源管理'
      }
    },
    {
      path: 'rolesPower',
      component: () => import(/* webpackChunkName: "authoritySystem-rolesPower" */ '@/pages/authoritySystem/rolesPower.vue'),
      name: 'rolesPower',
      meta: {
        breadcrumb: ['角色赋权', '列表'],
        keepAlive: true,
        tagName: '角色赋权'
      }
    }
  ]
}
