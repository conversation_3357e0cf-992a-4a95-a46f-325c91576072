export default {
  path: '/messageCenter/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['消息中心'],
    keepAlive: true, // 采用缓存
    tagName: '消息中心'
  },
  children: [
    {
      path: 'centerList',
      component: () =>
        import(
          /* webpackChunkName: "account-statement-center" */ '@/pages/messageCenter/list.vue'
        ),
      name: 'centerList',
      meta: {
        breadcrumb: ['消息中心'],
        keepAlive: true, // 采用缓存
        tagName: '消息中心'
      }
    }
  ]
}
