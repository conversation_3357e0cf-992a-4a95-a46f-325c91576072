// SAP orders
export default {
  path: '/orderSale',
  name: 'OrderSale',
  meta: {
    breadcrumb: ['销售视角'],
    keepAlive: true, // 采用缓存
    tagName: '销售视角'
  }, //  meta: ['订单页'],
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'formal',
      name: 'OrderSaleList',
      meta: {
        breadcrumb: ['销售视角', '销售订单列表'],
        keepAlive: true, // 采用缓存
        tagName: '销售订单列表'
      },
      component: () =>
        import(
          /* webpackChunkName: "sale-order-list" */ '@/pages/orderSale/list'
        )
    },
    {
      // key为了区分不同详情页，方便tab组件缓存
      path: 'formal/detail/:key',
      name: 'OrderSaleDetail',
      meta: {
        breadcrumb: ['销售视角', '销售订单详情'],
        keepAlive: true, // 采用缓存
        tagName: '销售订单详情'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-sale-detail" */ '@/pages/orderSale/detail'
        )
    },
    {
      // key为了区分不同详情页，方便tab组件缓存
      path: 'formal/invoice/detail/:key',
      name: 'InvoiceProgressDetail',
      meta: {
        breadcrumb: ['销售视角', '发票进度物流详情'],
        keepAlive: true, // 采用缓存
        tagName: '发票进度物流详情'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-sale-detail" */ '@/pages/orderSale/invoice/detail'
        )
    },
    {
      path: 'formal/create/:companycode/:categorycode',
      name: 'OrderSaleCreate',
      meta: {
        breadcrumb: ['销售视角', '创建销售订单'],
        keepAlive: true, // 采用缓存
        tagName: '创建销售订单'
      },
      redirect(to, from) {
        const oldPath = to.fullPath;
        window.location.replace(oldPath.replace('/orderSale/formal/', '/sr/'))
      },
      component: () =>
        import(
          /* webpackChunkName: "order-sale-create" */ '@/pages/orderSale/create'
        )
    },
    {
      path: 'formal/edit/:sapOrderNo',
      name: 'OrderSaleEdit',
      meta: {
        breadcrumb: ['销售视角', '编辑销售订单'],
        keepAlive: true, // 采用缓存
        tagName: '编辑销售订单'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-sale-edit" */ '@/pages/orderSale/edit'
        )
    },
    {
      path: 'periphery',
      name: 'PeripheryList',
      meta: {
        breadcrumb: ['销售视角', '外围订单列表'],
        keepAlive: true, // 采用缓存
        tagName: '外围订单列表'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-sale-periphery" */ '@/pages/orderSale/periphery'
        )
    },
    {
      path: 'backlogOrder',
      name: 'BacklogOrderList',
      meta: {
        breadcrumb: ['销售视角', '未交订单跟踪'],
        keepAlive: true, // 采用缓存
        tagName: '未交订单跟踪'
      },
      component: () =>
        import(
          /* webpackChunkName: "backlogorder-list" */ '@/pages/backlogOrder/list'
        )
    },
    {
      path: 'soOrder',
      name: 'SoOrderList',
      meta: {
        breadcrumb: ['销售视角', '销售订单明细表'],
        keepAlive: true, // 采用缓存
        tagName: '销售订单明细表'
      },
      component: () =>
        import(
          /* webpackChunkName: "soOrder-list" */ '@/pages/soOrder/list'
        )
    },
    {
      path: 'dnOrder',
      name: 'dnOrderList',
      meta: {
        breadcrumb: ['销售视角', '交货单明细表'],
        keepAlive: true, // 采用缓存
        tagName: '交货单明细表'
      },
      component: () =>
        import(
          /* webpackChunkName: "dnOrder-list" */ '@/pages/dnOrder/list'
        )
    },
    {
      path: 'directOrder',
      name: 'DirectOrderList',
      meta: {
        breadcrumb: ['销售视角', '已发未收（直发）'],
        keepAlive: true, // 采用缓存
        tagName: '已发未收列表'
      },
      component: () =>
        import(
          /* webpackChunkName: "directorder-list" */ '@/pages/directOrder/list'
        )
    },
    {
      // key为了区分不同详情页，方便tab组件缓存
      path: 'directOrder/detail/:key',
      name: 'DirectOrderDetail',
      meta: {
        breadcrumb: ['销售视角', '已发未收（直发）'],
        keepAlive: true, // 采用缓存
        tagName: '直发单签收'
      },
      component: () =>
        import(
          /* webpackChunkName: "direct-order-detail" */ '@/pages/directOrder/detail'
        )
    },
    {
      path: 'atplist',
      name: 'AtpList',
      meta: {
        breadcrumb: ['销售视角', '商品占库报表'],
        keepAlive: true, // 采用缓存
        tagName: '商品占库报表'
      },
      component: () =>
        import(
          /* webpackChunkName: "atp-list" */ '@/pages/orderSale/atp'
        )
    },
    {
      path: 'atpRealtimelist',
      name: 'AtpRealtimeList',
      meta: {
        breadcrumb: ['销售视角', '商品实时占库报表'],
        keepAlive: true, // 采用缓存
        tagName: '商品实时占库报表'
      },
      component: () =>
        import(
          /* webpackChunkName: "atp-realtime-list" */ '@/pages/orderSale/atpRealtime'
        )
    },
    {
      path: 'stoList',
      name: 'StoList',
      meta: {
        breadcrumb: ['采购视角', '调拨单交货'],
        keepAlive: true, // 采用缓存
        tagName: '调拨单交货'
      },
      component: () =>
        import(
          /* webpackChunkName: "sto-list" */ '@/pages/orderSale/sto.vue'
        )
    },
    {
      path: 'batch',
      name: 'Batch',
      meta: {
        breadcrumb: ['采购视角', '批量上传'],
        keepAlive: true, // 采用缓存
        tagName: '批量上传'
      },
      component: () =>
        import(
          /* webpackChunkName: "so-batch" */ '@/pages/orderSale/batch.vue'
        )
    },
    {
      path: 'close',
      name: 'CLOSE',
      meta: {
        breadcrumb: ['采购视角', '批量关单'],
        keepAlive: true, // 采用缓存
        tagName: '批量关单'
      },
      component: () =>
        import(
          /* webpackChunkName: "so-batch-close" */ '@/pages/orderSale/batchClose.vue'
        )
    },
    { path: 'csToDoList',
      name: 'CSTOLIST',
      meta: {
        breadcrumb: ['销售视角', '客服待办事项'],
        keepAlive: true, // 采用缓存
        tagName: '客服待办事项'
      },
      component: () =>
        import(
          /* webpackChunkName: "cs-todo-list" */ '@/pages/orderSale/csTodoList.vue'
        )
    },
    {
      path: 'differentReport',
      name: 'differentReport',
      meta: {
        breadcrumb: ['中转集货', '在途虚拟仓差异报表'],
        keepAlive: true, // 采用缓存
        tagName: '在途虚拟仓差异报表'
      },
      component: () =>
        import(
          /* webpackChunkName: "so-different-report" */ '@/pages/orderSale/differentReport.vue'
        )
    },
    { path: 'performanceProcessTool',
      name: 'PERFORMANCEPROCESSTOOL',
      meta: {
        breadcrumb: ['履约流程'],
        keepAlive: true, // 采用缓存
        tagName: '履约流程'
      },
      component: () =>
        import(
          /* webpackChunkName: "performanceProcessTool" */ '@/pages/orderSale/performanceProcessTool.vue'
        )
    }
  ]
}
