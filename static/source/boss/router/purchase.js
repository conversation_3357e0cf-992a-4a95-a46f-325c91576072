// 采购单
export default {
  path: '/purchase/',
  name: 'purchase',
  meta: {
    breadcrumb: ['采购视角'],
    keepAlive: true, // 采用缓存
    tagName: '采购视角'
  }, // meta: ['订单页'],
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'purchaseList',
      name: 'purchaseList',
      meta: {
        breadcrumb: ['采购视角', '采购订单列表'],
        keepAlive: true, // 采用缓存
        tagName: '采购订单列表'
      }, //  meta: ['购买订单页'],
      component: () =>
        import(
          /* webpackChunkName: "purchase-list" */ '@/pages/purchaseList/purchaseList.vue'
        )
    },
    {
      path: 'abnormalNoOrder',
      name: 'abnormalNoOrder',
      meta: {
        breadcrumb: ['采购视角', '异常未下单'],
        keepAlive: true, // 采用缓存
        tagName: '异常未下单'
      }, //  meta: ['购买订单页'],
      component: () =>
        import(
          /* webpackChunkName: "abnormalNoOrder" */ '@/pages/purchaseList/abnormalNoOrder/index.vue'
        )
    }
  ]
}
