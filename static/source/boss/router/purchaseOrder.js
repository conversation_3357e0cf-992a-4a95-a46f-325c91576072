export default {
  path: '/orderPurchase/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['采购订单'],
    keepAlive: true,
    tagName: '采购订单'
  },
  children: [
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-list" */ '@/pages/orderPurchase/list.vue'
        ),
      name: 'orderPurchaseList',
      meta: {
        breadcrumb: ['采购订单', '列表'],
        keepAlive: true,
        tagName: '采购订单列表'
      }
    },
    {
      path: 'audit',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-audit" */ '@/pages/orderPurchase/audit.vue'
        ),
      name: 'orderPurchaseAudit',
      meta: {
        breadcrumb: ['采购订单', '审批'],
        keepAlive: true,
        tagName: '订单审批'
      }
    },
    {
      path: 'create/:id',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-create" */ '@/pages/orderPurchase/create.vue'
        ),
      name: 'orderPurchaseCreate',
      meta: {
        breadcrumb: ['采购订单', '创建'],
        keepAlive: true,
        tagName: '创建采购订单'
      }
    },
    {
      path: 'detail/:id',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-detail" */ '@/pages/orderPurchase/detail.vue'
        ),
      name: 'orderPurchaseDetail',
      meta: {
        breadcrumb: ['采购订单', '创建'],
        keepAlive: true,
        tagName: '采购订单详情'
      }
    },
    {
      path: 'edit/:id',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-edit" */ '@/pages/orderPurchase/edit.vue'
        ),
      name: 'orderPurchaseEdit',
      meta: {
        breadcrumb: ['采购订单', '修改'],
        keepAlive: true,
        tagName: '采购订单修改'
      }
    },
    {
      path: 'directToStandard',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-directToStandard" */ '@/pages/orderPurchase/directToStandard.vue'
        ),
      name: 'orderPurchaseDirectToStandard',
      meta: {
        breadcrumb: ['采购订单', '直发转标准'],
        keepAlive: true,
        tagName: '直发转标准'
      }
    },
    {
      path: 'standardToDirect',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-standardToDirect" */ '@/pages/orderPurchase/standardToDirect.vue'
        ),
      name: 'orderPurchaseStandardToDirect',
      meta: {
        breadcrumb: ['采购订单', '标准转直发'],
        keepAlive: true,
        tagName: '标准转直发'
      }
    },
    {
      path: 'outsourcingClose',
      component: () =>
        import(
          /* webpackChunkName: "orderPurchase-standardToDirect" */ '@/pages/orderPurchase/outsourcingClose.vue'
        ),
      name: 'orderPurchaseOutsourcingClose',
      meta: {
        breadcrumb: ['采购订单', '批量关单'],
        keepAlive: true,
        tagName: '批量关单'
      }
    },
    {
      path: 'punchOrder',
      component: () =>
        import(
          /* webpackChunkName: "punch-order" */ '@/pages/orderPurchase/punchOrder.vue'
        ),
      name: 'punchOrder',
      meta: {
        breadcrumb: ['采购订单', 'Punch订单选品'],
        keepAlive: true,
        tagName: 'Punch订单选品'
      }
    },
    {
      path: 'purchaseWorkbench',
      component: () =>
        import(
          /* webpackChunkName: "purchase-workshop" */ '@/pages/orderPurchase/purchaseWorkbench.vue'
        ),
      name: 'purchaseWorkbench',
      meta: {
        breadcrumb: ['采购订单', '采购待办事项'],
        keepAlive: true,
        tagName: '采购待办事项'
      }
    },
    {
      path: 'purchaseDashboard',
      component: () =>
        import(
          /* webpackChunkName: "purchase-workshop" */ '@/pages/orderPurchase/purchaseDashboard.vue'
        ),
      name: 'purchaseDashboard',
      meta: {
        breadcrumb: ['采购订单', '采购工作台大盘'],
        keepAlive: true,
        tagName: '采购工作台大盘'
      }
    },
    {
      path: 'wmsCoa/:type',
      component: () =>
        import(
          /* webpackChunkName: "wms-coa" */ '@/pages/orderPurchase/wmsCoa.vue'
        ),
      name: 'wmsCoa',
      meta: {
        breadcrumb: ['采购订单', 'COA合格证管理'],
        keepAlive: true,
        tagName: 'COA合格证管理'
      }
    }
  ]
}
