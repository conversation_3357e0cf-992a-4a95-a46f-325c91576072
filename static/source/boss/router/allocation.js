export default {
  path: '/allocation/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['匀货'],
    keepAlive: true,
    tagName: '匀货'
  },
  children: [
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "allocation-list" */ '@/pages/supplyDetail/allocationList.vue'
        ),
      name: 'allocationList',
      meta: {
        breadcrumb: ['匀货列表'],
        keepAlive: true,
        tagName: '匀货列表'
      }
    }
  ]
}
