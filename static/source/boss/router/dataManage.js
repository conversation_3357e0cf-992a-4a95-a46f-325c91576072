// SUGGESTION: valet ordering
export default {
  path: '/dataManage/',
  component: () => import('@/components/Router.vue'),
  meta: {
    breadcrumb: ['数据治理'],
    keepAlive: true, // 采用缓存
    tagName: '数据治理'
  },
  children: [
    {
      path: 'purchaseMaintenance',
      component: () => import('@/pages/dataManage/purchaseMaintenance'),
      name: 'dataManagePurchaseMaintenance',
      meta: {
        breadcrumb: ['数据维护', '采购价格问题维护'],
        keepAlive: true, // 采用缓存
        tagName: '采购价格问题维护'
      }
    },
    {
      path: 'salesMaintenance',
      component: () => import('@/pages/dataManage/salesMaintenance'),
      name: 'dataManageSalesMaintenance',
      meta: {
        breadcrumb: ['数据维护', '销售价格问题维护'],
        keepAlive: true, // 采用缓存
        tagName: '销售价格问题维护'
      }
    },
    {
      path: 'import',
      component: () => import('@/pages/dataManage/import'),
      name: 'dataManageImportRoute',
      meta: {
        breadcrumb: ['数据治理', '数据导入'],
        keepAlive: true, // 采用缓存
        tagName: '数据导入'
      }
    },
    {
      path: 'export',
      component: () => import('@/pages/dataManage/export'),
      name: 'dataManageExport',
      meta: {
        breadcrumb: ['数据治理', '数据导出'],
        keepAlive: true, // 采用缓存
        tagName: '数据导出'
      }
    },
    {
      path: 'propertyRule',
      component: () => import('@/pages/dataManage/propertyRule/index'),
      name: 'dataManagePropertyRule',
      meta: {
        breadcrumb: ['规则管理', '属性规则配置'],
        keepAlive: true, // 采用缓存
        tagName: '属性规则配置'
      }
    },
    {
      path: 'viewFinalDraft',
      component: () => import('@/pages/dataManage/viewFinalDraft'),
      name: 'dataManageViewFinalDraft',
      meta: {
        breadcrumb: ['数据维护', '定稿管理'],
        keepAlive: true, // 采用缓存
        tagName: '定稿管理'
      }
    },
    {
      path: 'propertyRepair',
      component: () => import('@/pages/dataManage/propertyRepair'),
      name: 'dataManagePropertyRepair',
      meta: {
        breadcrumb: ['数据维护', '属性问题维护'],
        keepAlive: true, // 采用缓存
        tagName: '属性问题维护'
      }
    },
    {
      path: 'uniqueRepair',
      component: () => import('@/pages/dataManage/uniqueRepair'),
      name: 'dataManageUniqueRepair',
      meta: {
        breadcrumb: ['数据维护', '唯一性问题维护'],
        keepAlive: true, // 采用缓存
        tagName: '唯一性问题维护'
      }
    },
    {
      path: 'simpleRepair',
      component: () => import('@/pages/dataManage/simpleRepair'),
      name: 'dataManageSimpleRepair',
      meta: {
        breadcrumb: ['数据维护', '一般属性问题维护'],
        keepAlive: true, // 采用缓存
        tagName: '一般属性问题维护'
      }
    },
    {
      path: 'trendStatistics',
      component: () => import('@/pages/dataManage/trendStatistics'),
      name: 'trendStatistics',
      meta: {
        breadcrumb: ['数据维护', '趋势看板'],
        keepAlive: true, // 采用缓存
        tagName: '趋势看板'
      }
    },
    {
      path: 'businessCenter/rule',
      component: () =>
        import(
          /* webpackChunkName: "businessCenter-rule" */ '@/pages/dataManage/businessCenter/rule'
        ),
      name: 'businessCenterRule',
      meta: {
        breadcrumb: ['数据治理', '商机规则管理'],
        keepAlive: true, // 采用缓存
        tagName: '商机规则管理'
      }
    },
    {
      path: 'businessCenter/ruleDetail/:type/:id/:scene', // type: 1 详情 2 编辑 id:categoryId   scene: DIRECTLY 直发 INDIRECTLY 非直发
      component: () =>
        import(
          /* webpackChunkName: "businessCenter-ruleDetail" */ '@/pages/dataManage/businessCenter/ruleDetail'
        ),
      name: 'businessCenterRuleDetail',
      meta: {
        breadcrumb: ['数据治理', '商机规则详情'],
        keepAlive: true, // 采用缓存
        tagName: '商机规则详情'
      }
    },
    {
      path: 'exportApprove',
      component: () =>
        import(
          /* webpackChunkName: "businessCenter-ruleDetail" */ '@/pages/dataManage/exportApprove'
        ),
      name: 'dataManageExportApprove',
      meta: {
        breadcrumb: ['数据治理', '导出定稿审批'],
        keepAlive: true, // 采用缓存
        tagName: '导出定稿审批'
      }
    },
    {
      path: 'productGroupPowerManage',
      component: () =>
        import(
          /* webpackChunkName: "businessCenter-ruleDetail" */ '@/pages/dataManage/productGroupPowerManage'
        ),
      name: 'productGroupPowerManage',
      meta: {
        breadcrumb: ['数据治理', '物料组权限管理'],
        keepAlive: true, // 采用缓存
        tagName: '物料组权限管理'
      }
    },
    {
      path: 'channelManage',
      component: () =>
        import('@/pages/dataManage/channelAndRule/channelManage'),
      name: 'channelManage',
      meta: {
        breadcrumb: ['数据治理', '渠道管理'],
        keepAlive: true, // 采用缓存
        tagName: '渠道管理'
      }
    },
    {
      path: 'sceneManage/:id', // id:渠道id
      component: () => import('@/pages/dataManage/channelAndRule/sceneManage'),
      name: 'sceneManage',
      meta: {
        breadcrumb: ['数据治理', '场景管理'],
        keepAlive: true, // 采用缓存
        tagName: '场景管理'
      }
    },
    {
      path: 'ruleSceneConfig',
      component: () =>
        import('@/pages/dataManage/channelAndRule/ruleSceneConfig'),
      name: 'ruleSceneConfig',
      meta: {
        breadcrumb: ['数据治理', '规则场景配置'],
        keepAlive: true, // 采用缓存
        tagName: '规则场景配置'
      }
    },
    {
      path: 'ruleFactor',
      component: () => import('@/pages/dataManage/channelAndRule/ruleFactor'),
      name: 'ruleFactor',
      meta: {
        breadcrumb: ['数据治理', '规则因子'],
        keepAlive: true, // 采用缓存
        tagName: '规则因子'
      }
    },
    {
      path: 'dangerousGoods',
      component: () => import('@/pages/dataManage/dangerousGoods'),
      name: 'dangerousGoods',
      meta: {
        breadcrumb: ['数据治理', '危险化学品名录'],
        keepAlive: true, // 采用缓存
        tagName: '危险化学品名录'
      }
    },
    {
      path: 'governQuery',
      component: () => import('@/pages/dataManage/governQuery'),
      name: 'governQuery',
      meta: {
        breadcrumb: ['数据治理', '治理情况查询'],
        keepAlive: true, // 采用缓存
        tagName: '治理情况查询'
      }
    },
    {
      path: 'taskManage',
      component: () => import('@/pages/dataManage/taskManage/index'),
      name: 'taskManage',
      meta: {
        breadcrumb: ['数据治理', '任务管理'],
        keepAlive: true, // 采用缓存
        tagName: '任务管理'
      }
    },
    {
      path: 'dataGovernStatistics',
      component: () => import('@/pages/dataManage/dataGovernStatistics/index'),
      name: 'dataGovernStatistics',
      meta: {
        breadcrumb: ['数据治理', '数据治理统计'],
        keepAlive: true, // 采用缓存
        tagName: '数据治理统计'
      }
    },
    {
      path: 'strategyConfig',
      component: () => import('@/pages/dataManage/strategyConfig/index'),
      name: 'dataManageStrategyConfig',
      meta: {
        breadcrumb: ['规则管理', '商品发布策略'],
        keepAlive: true, // 采用缓存
        tagName: '商品发布策略'
      }
    },
    {
      path: 'barCodeMng',
      component: () => import('@/pages/dataManage/barCodeMng/index'),
      name: 'barCodeMng',
      meta: {
        breadcrumb: ['数据治理', '条码匹配'],
        keepAlive: true, // 采用缓存
        tagName: '条码匹配'
      }
    },
    {
      path: 'barCodeMng/taskInfo/:taskId',
      component: () => import('@/pages/dataManage/barCodeMng/components/taskInfo'),
      name: 'taskInfo',
      meta: {
        breadcrumb: ['数据治理', '条码匹配'],
        keepAlive: true, // 采用缓存
        tagName: '任务详情'
      }
    },
    {
      path: 'barCodeMeta',
      component: () => import('@/pages/dataManage/barCodeMeta/index'),
      name: 'barCodeMeta',
      meta: {
        breadcrumb: ['数据治理', '条形码列表'],
        keepAlive: true, // 采用缓存
        tagName: '条形码列表'
      }
    }
  ]
};
