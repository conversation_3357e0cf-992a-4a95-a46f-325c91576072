export default {
  path: '/stockpile-strategy',
  name: 'stockpileStrategy',
  meta: {
    breadcrumb: ['备货策略'],
    keepAlive: true, // 采用缓存
    tagName: '备货策略'
  },
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'checklist',
      name: 'checklist',
      meta: {
        breadcrumb: ['备货策略', '备货审核'],
        keepAlive: true,
        tagName: '备货审核'
      },
      component: () =>
        import(
          /* webpackChunkName: "Checklist" */ '@/pages/stockpileStrategy/checklist.vue'
        )
    },
    {
      path: 'history',
      name: 'history',
      meta: {
        breadcrumb: ['备货策略', '备货历史'],
        keepAlive: true,
        tagName: '备货历史'
      },
      component: () =>
        import(
          /* webpackChunkName: "History" */ '@/pages/stockpileStrategy/history.vue'
        )
    },
    {
      path: 'blacklist',
      name: 'blacklist',
      meta: {
        breadcrumb: ['备货策略', '备货黑名单'],
        keepAlive: true,
        tagName: '备货黑名单'
      },
      component: () =>
        import(
          /* webpackChunkName: "Blacklist" */ '@/pages/stockpileStrategy/blacklist.vue'
        )
    },
    {
      path: 'approvalList',
      name: 'approvalList',
      meta: {
        breadcrumb: ['备货策略', 'AB类配置审批'],
        keepAlive: true,
        tagName: 'AB类配置审批'
      },
      component: () =>
        import(
          '@/pages/stockpileStrategy/approvalList.vue'
        )
    },
    {
      path: 'approvalHistory',
      name: 'approvalHistory',
      meta: {
        breadcrumb: ['备货策略', 'AB分类审批历史'],
        keepAlive: true,
        tagName: 'AB分类审批历史'
      },
      component: () =>
        import(
          '@/pages/stockpileStrategy/approvalHistory.vue'
        )
    },
    {
      path: 'approvalResult',
      name: 'approvalResult',
      meta: {
        breadcrumb: ['备货策略', 'A类配置结果'],
        keepAlive: true,
        tagName: 'A类配置结果'
      },
      component: () =>
        import(
          '@/pages/stockpileStrategy/approvalResult.vue'
        )
    },
    {
      path: 'newModelStocklist',
      name: 'newModelStocklist',
      meta: {
        breadcrumb: ['新备货策略', '新备货审核'],
        keepAlive: true,
        tagName: '新模型备货配置'
      },
      component: () =>
        import(
          /* webpackChunkName: "newModelStocklist" */ '@/pages/stockpileStrategy/newModelStock/list.vue'
        )
    },
    {
      path: 'newModelStockApproval',
      name: 'newModelStockApproval',
      meta: {
        breadcrumb: ['新备货策略', '新备货审核'],
        keepAlive: true,
        tagName: '新模型备货审核'
      },
      component: () =>
        import(
          /* webpackChunkName: "newModelStockApproval" */ '@/pages/stockpileStrategy/newModelStock/approval.vue'
        )
    },
    {
      path: 'config',
      name: 'config',
      meta: {
        breadcrumb: ['备货策略'],
        keepAlive: true,
        tagName: '备货策略'
      },
      component: () =>
        import(
          /* webpackChunkName: "newModelStockApproval" */ '@/pages/stockpileStrategy/newModelStock/config.vue'
        )
    }
  ]
}
