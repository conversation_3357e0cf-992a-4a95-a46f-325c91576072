export default {
  path: '/logDetail/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['日志明细报表'],
    keepAlive: true,
    tagName: '日志明细报表'
  },
  children: [
    {
      path: 'orderSaleLogDetail',
      component: () =>
        import(
          /* webpackChunkName: "orderSaleLogDetail" */ '@/pages/logDetail/orderSaleLogDetail.vue'
        ),
      name: 'orderSaleLogDetail',
      meta: {
        breadcrumb: ['销售订单日志明细'],
        keepAlive: true,
        tagName: '销售订单日志明细'
      }
    },
    {
      path: 'queryLog',
      component: () =>
        import(
          /* webpackChunkName: "queryLog" */ '@/pages/logDetail/queryLog.vue'
        ),
      name: 'queryLog',
      meta: {
        breadcrumb: ['日志查询'],
        keepAlive: true,
        tagName: '日志查询'
      }
    }
  ]
}
