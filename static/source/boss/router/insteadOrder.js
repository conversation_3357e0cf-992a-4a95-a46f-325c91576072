// SUGGESTION: valet ordering
export default {
  path: '/insteadOrder/',
  component: () => import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['代客下单'],
    keepAlive: true, // 采用缓存
    tagName: '代客下单'
  }, //  {
  //   title: '代客下单',
  //   icon: 'search'
  // },
  // alwaysShow: true,
  children: [
    {
      path: 'quickSearch',
      component: () => import(/* webpackChunkName: "quick-search" */ '@/pages/insteadOrder/quickSearch/QuickSearch'),
      name: 'QuickSearch',
      meta: {
        breadcrumb: ['代客下单', '客户商品快捷查询'],
        keepAlive: true, // 采用缓存
        tagName: '客户商品快捷查询'
      } //  meta: {
      //   title: '客户商品快捷查询'
      // }
    },
    {
      path: 'maintainment',
      component: () => import(/* webpackChunkName: "maintainment-list" */ '@/pages/insteadOrder/maintainment/List'),
      name: 'MaintainmentList',
      meta: {
        breadcrumb: ['代客下单', '客户物料关系维护'],
        keepAlive: true, // 采用缓存
        tagName: '客户物料关系维护'
      }
    },
    {
      path: 'maintainmentv3',
      component: () => import(/* webpackChunkName: "maintainment-list-v3" */ '@/pages/insteadOrder/maintainmentV3/List'),
      name: 'MaintainmentListV3',
      meta: {
        breadcrumb: ['代客下单', '客户物料管理中心'],
        keepAlive: true, // 采用缓存
        tagName: '客户物料管理中心'
      }
    },
    {
      path: 'maintainment/:id',
      component: () => import(/* webpackChunkName: "maintainment-detail" */ '@/pages/insteadOrder/maintainment/Detail'),
      name: 'MaintainmentDetail',
      meta: {
        breadcrumb: ['代客下单', '客户物料关系维护详情'],
        keepAlive: true, // 采用缓存
        tagName: '客户物料关系维护详情'
      }
    },
    {
      path: 'maintainmentv3/detail/:id',
      component: () => import(/* webpackChunkName: "maintainment-detail-v3" */ '@/pages/insteadOrder/maintainmentV3/Detail'),
      name: 'MaintainmentDetailV3',
      meta: {
        breadcrumb: ['代客下单', '客户物料关系详情'],
        keepAlive: true, // 采用缓存
        tagName: '客户物料关系详情'
      }
    },
    {
      path: 'relation/template',
      component: () => import(/* webpackChunkName: "relation-template-list" */ '@/pages/insteadOrder/maintainment/TemplateList'),
      name: 'RelationTemplateList',
      meta: {
        breadcrumb: ['代客下单', '模板列表'],
        keepAlive: true, // 采用缓存
        tagName: '模板列表'
      }
    },
    {
      path: 'relation/template/detail/:id',
      component: () => import(/* webpackChunkName: "relation-template-detail" */ '@/pages/insteadOrder/maintainment/TemplateDetail'),
      name: 'RelationTemplateDetail',
      meta: {
        breadcrumb: ['代客下单', '模板详情'],
        keepAlive: true, // 采用缓存
        tagName: '模板详情'
      }
    },
    {
      path: 'priceCenter/list',
      component: () => import(/* webpackChunkName: "priceCenter-list" */ '@/pages/insteadOrder/priceCenter/List'),
      name: 'priceCenter-list',
      meta: {
        breadcrumb: ['代客下单', '客户商品价格列表'],
        keepAlive: true, // 采用缓存
        tagName: '客户商品价格列表'
      }
    },
    {
      path: 'priceCenter/detail/:id',
      component: () => import(/* webpackChunkName: "priceCenter-detail" */ '@/pages/insteadOrder/priceCenter/Detail'),
      name: 'priceCenter-detail',
      meta: {
        breadcrumb: ['代客下单', '客户商品价格详情'],
        keepAlive: true, // 采用缓存
        tagName: '客户商品价格详情'
      }
    }
  ]
}
