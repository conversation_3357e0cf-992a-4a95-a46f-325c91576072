export default {
  path: '/customizedOrder/',
  component: () => import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['客制化订单'],
    keepAlive: true, // 采用缓存
    tagName: '客制化订单'
  },
  children: [
    {
      path: 'list',
      component: () => import(/* webpackChunkName: "customized-order-list" */ '@/pages/customizedOrder/List'),
      name: 'customizedOrderList',
      meta: {
        breadcrumb: ['客制化订单', '手动上传送货单'],
        keepAlive: true, // 采用缓存
        tagName: '手动上传送货单'
      }
    }
  ]
}
