export default {
  path: '/orderDelivery',
  name: 'OrderDelivery',
  meta: {
    breadcrumb: ['销售视角'],
    keepAlive: true, // 采用缓存
    tagName: '销售视角'
  },
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  children: [
    {
      path: 'list',
      name: 'OrderDeliveryList',
      meta: {
        breadcrumb: ['销售视角', '交货订单列表'],
        keepAlive: true, // 采用缓存
        tagName: '交货订单列表'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-list" */ '@/pages/orderDelivery/list'
        )
    },
    {
      path: 'create/:id',
      name: 'OrderDeliveryCreate',
      meta: {
        breadcrumb: ['销售视角', '创建交货单'],
        keepAlive: true, // 采用缓存
        tagName: '创建交货单'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-create" */ '@/pages/orderDelivery/create'
        )
    },
    {
      path: 'stoCreate/:id',
      name: 'OrderDeliveryStoCreate',
      meta: {
        breadcrumb: ['销售视角', '创建调拨交货单'],
        keepAlive: true, // 采用缓存
        tagName: '创建调拨交货单'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-sto—create" */ '@/pages/orderDelivery/stoCreate'
        )
    },
    {
      path: 'edit/:id',
      name: 'OrderDeliveryEdit',
      meta: {
        breadcrumb: ['销售视角', '编辑交货单'],
        keepAlive: true, // 采用缓存
        tagName: '编辑交货单'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-edit" */ '@/pages/orderDelivery/edit'
        )
    },
    {
      path: 'stoEdit/:id',
      name: 'OrderDeliveryStoEdit',
      meta: {
        breadcrumb: ['销售视角', '编辑调拨交货单'],
        keepAlive: true, // 采用缓存
        tagName: '编辑调拨交货单'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-sto-edit" */ '@/pages/orderDelivery/stoEdit'
        )
    },
    {
      path: 'detail/:id',
      name: 'OrderDeliveryDetail',
      meta: {
        breadcrumb: ['销售视角', '交货单详情'],
        keepAlive: true, // 采用缓存
        tagName: '交货单详情'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-detail" */ '@/pages/orderDelivery/detailTab'
        )
    },
    {
      path: 'stoDetail/:id',
      name: 'OrderDeliveryStoDetail',
      meta: {
        breadcrumb: ['销售视角', '交货调拨单详情'],
        keepAlive: true, // 采用缓存
        tagName: '交货调拨单详情'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-sto-detail" */ '@/pages/orderDelivery/stoDetail'
        )
    },
    {
      path: 'stolist',
      name: 'OrderDeliveryStoList',
      meta: {
        breadcrumb: ['销售视角', '交货调拨订单列表'],
        keepAlive: true, // 采用缓存
        tagName: '交货调拨订单列表'
      },
      component: () =>
        import(
          /* webpackChunkName: "order-delivery-sto-list" */ '@/pages/orderDelivery/stoList'
        )
    }
  ]
}
