export default {
  path: '/saleForecast/',
  component: () => import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['销售预报'],
    keepAlive: true, // 采用缓存
    tagName: '销售预报'
  },
  children: [
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "sale-forecast-list" */ '@/pages/saleForecast/List'
        ),
      name: 'saleForecaseList',
      meta: {
        breadcrumb: ['销售预报', '销售预报列表'],
        keepAlive: true,
        tagName: '销售预报列表'
      }
    },
    {
      path: 'detail',
      component: () =>
        import(
          /* webpackChunkName: "sale-forecast-detail" */ '@/pages/saleForecast/Detail'
        ),
      name: 'saleForecaseDetail',
      meta: {
        breadcrumb: ['销售预报', '销售预报信息'],
        keepAlive: true,
        tagName: '销售预报信息'
      }
    },
    {
      path: 'track/list',
      component: () =>
        import(
          /* webpackChunkName: "sale-forecast-list" */ '@/pages/saleForecast/TrackList'
        ),
      name: 'saleForecaseTrackList',
      meta: {
        breadcrumb: ['销售预报', '销售预报跟踪表'],
        keepAlive: true,
        tagName: '销售预报跟踪表'
      }
    },
    {
      path: 'apply/list',
      component: () =>
        import(
          /* webpackChunkName: "sale-forecast-list" */ '@/pages/saleForecast/ApplyList'
        ),
      name: 'saleForecaseApplyList',
      meta: {
        breadcrumb: ['销售预报', '销售预报申请明细表'],
        keepAlive: true,
        tagName: '销售预报申请明细表'
      }
    },
    {
      path: 'apply/:type/:id',
      component: () =>
        import(
          /* webpackChunkName: "sale-forecast-list" */ '@/pages/saleForecast/ApplyDetail'
        ),
      name: 'saleForecaseApplyDetail',
      meta: {
        breadcrumb: ['销售预报', '销售预报申请明细'],
        keepAlive: true,
        tagName: '销售预报申请明细'
      }
    }
  ]
}
