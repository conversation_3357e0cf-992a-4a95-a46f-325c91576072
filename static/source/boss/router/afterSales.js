export default {
  path: '/after-sales/',
  component: () =>
    import(/* webpackChunkName: "router-view" */ '@/components/Router.vue'),
  meta: {
    breadcrumb: ['售后中心'],
    keepAlive: true,
    tagName: '售后中心'
  },
  children: [
    {
      path: 'form/:id',
      component: () =>
        import(
          /* webpackChunkName: "aftersales-form" */ '@/pages/afterSales/Form.vue'
        ),
      name: 'aftersalesForm',
      meta: {
        breadcrumb: ['售后中心', '创建售后申请单'],
        keepAlive: true,
        tagName: '创建售后申请单'
      }
    },
    {
      path: 'list',
      component: () =>
        import(
          /* webpackChunkName: "aftersales-list" */ '@/pages/afterSales/List.vue'
        ),
      name: 'aftersalesList',
      meta: {
        breadcrumb: ['售后中心', '售后申请单列表'],
        keepAlive: true,
        tagName: '售后申请单列表'
      }
    },
    {
      path: 'detail/:id',
      component: () =>
        import(
          /* webpackChunkName: "aftersales-detail" */ '@/pages/afterSales/Detail/Index.vue'
        ),
      name: 'aftersalesDetail',
      meta: {
        breadcrumb: ['售后中心', '售后申请单'],
        keepAlive: false,
        tagName: '售后申请单'
      }
    },
    {
      path: 'receive/list',
      component: () =>
        import(
          /* webpackChunkName: "aftersales-detail" */ '@/pages/afterSales/ReceiveList.vue'
        ),
      name: 'ReceiveList',
      meta: {
        breadcrumb: ['售后中心', '收票列表'],
        keepAlive: false,
        tagName: '收票列表'
      }
    }
  ]
}
