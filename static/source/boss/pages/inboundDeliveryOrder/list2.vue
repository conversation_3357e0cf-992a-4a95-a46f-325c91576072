<template>
  <div class="list-container" v-loading="tableLoading">
    <el-form :model="searchForm" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="单号检索">
            <el-input
              v-model="searchForm.orderNos"
              clearable
              placeholder="交货单号 / SAP交货单号 / 采购单号，同时支持200个单号，以空格分隔"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SKU编码">
            <RemoteSku :data.sync="searchForm.sku" @change="val=>searchForm.sku=val" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="工厂代码">
            <el-select
              v-model="searchForm.factoryCode"
              filterable
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in dictList.supplierFactory"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库地点">
            <el-select
              v-model="searchForm.warehouseLocation"
              filterable
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in warehouseList"
                :key="item.factoryCode+item.warehouseLocationCode"
                :label="item.factoryCode+' '+item.warehouseLocationCode+' '+item.warehouseLocationName"
                :value="item.factoryCode+'_'+item.warehouseLocationCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="提货类型">
            <el-select
              v-model="searchForm.pickupType"
              filterable
              clearable
              style="width:100%"
            >
              <el-option label="空" value="00"></el-option>
              <el-option
                v-for="item in dictList.pickupType"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单据状态">
            <el-select
              v-model="searchForm.sapStatus"
              filterable
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in dictList.sapStatus"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="过账状态">
            <el-select
              v-model="searchForm.postStatus"
              filterable
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in dictList.postStatus"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商">
            <RemoteSupplier :data.sync="searchForm.supplierNo" @change="val=>searchForm.supplierNo=val"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="采购姓名">
            <el-select
              v-model="searchForm.purchaseGroup"
              filterable
              clearable
              style="width:100%"
              placeholder="选择采购员"
            >
              <el-option
                v-for="item in purchaseList"
                :key="item.groupCode"
                :label="item.groupCode+' '+item.userName"
                :value="item.groupCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="删除状态">
            <el-select
              v-model="searchForm.isDeleted"
              filterable
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in dictList['isDeleted']"
                :key="item.value"
                :label="item.name || '未删除'"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="凭证日期">
            <el-date-picker
              v-model="createTime"
              type="daterange"
              style="width:100%"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始"
              end-placeholder="结束"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-button
            type="primary"
            style="width: 80px; float:right;"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      height="400"
      align="center"
      id="ido_list"
      :custom-config="tableCustom"
      :data="listData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
    >
      <template v-slot:toolbar_buttons>
        <!-- <el-button type="primary" size="mini">收货入库</el-button> -->
      </template>
      <template v-slot:supplierName_default="{ row }">
        {{row.supplierNo + ' ' + row.supplierName}}
      </template>
      <template v-slot:orderNo_default="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{row.orderNo}}</el-link>
      </template>
      <template v-slot:postStatus_default="{ row }">
        {{row.postStatus + ' ' + ((dictList.postStatus||[]).find(item => item.value === row.postStatus) || {}).name}}
      </template>
      <template v-slot:pickupType_default="{ row }">
        {{row.pickupType ? row.pickupType + ' ' + ((dictList.pickupType||[]).find(item => item.value === row.pickupType) || {}).name : ''}}
      </template>
      <template v-slot:purchaseGroup_default="{ row }">
        {{row.purchaseGroup + ' ' + (purchaseList.find(item => item.groupCode === row.purchaseGroup) || {}).userName}}
      </template>
      <template v-slot:warehouseLocation_default="{ row }">
        {{(row.warehouseLocation || '') + ' ' + ((warehouseList.find(item => item.warehouseLocationCode === row.warehouseLocation) || {}).warehouseLocationName || '')}}
      </template>
      <template v-slot:factoryCode_default="{ row }">
        {{row.factoryCode + ' ' + ((dictList.supplierFactory||[]).find(item => item.value === row.factoryCode) || {}).name}}
      </template>
      <template v-slot:dmsSendStatus_default="{ row }">
        {{((dictList.msgSendStatus||[]).find(item => item.value === row.dmsSendStatus.toString()) || {}).name}}
      </template>
    </vxe-grid>
    <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="pageNo"
      :limit.sync="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getOrderList"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { idoList2 } from '@/api/mm'
import { RemoteSku, RemoteSupplier } from '@boss-ui/ui'
import Pagination from '@/components/Pagination'

const columns = [
  {
    field: 'orderNo',
    title: '交货单号',
    width: 120,
    slots: {
      // 使用插槽模板渲染
      default: 'orderNo_default'
    }
  },
  {
    field: 'sapOrderNo',
    title: 'SAP交货单号',
    width: 120
  },
  {
    field: 'poNo',
    title: '采购单号',
    width: 120
  },
  {
    field: 'poItemNo',
    title: '商品行',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    width: 80
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    width: 200
  },
  {
    field: 'deliveryQuantity',
    title: '交货数量',
    width: 80
  },
  {
    field: 'warehouseLocation',
    title: '仓库地点',
    width: 120,
    slots: {
      default: 'warehouseLocation_default'
    }
  },
  {
    field: 'factoryCode',
    title: '工厂',
    width: 200,
    slots: {
      default: 'factoryCode_default'
    }
  },
  {
    field: 'postStatus',
    title: '过账状态',
    width: 120,
    slots: {
      default: 'postStatus_default'
    }
  },
  {
    field: 'pickupType',
    title: '提货类型',
    width: 160,
    slots: {
      // 使用插槽模板渲染
      default: 'pickupType_default'
    }
  },
  {
    field: 'carrierName',
    title: '承运商',
    width: 90
  },
  {
    field: 'driverName',
    title: '司机',
    width: 180
  },
  {
    field: 'driverTel',
    title: '司机电话',
    width: 100
  },
  {
    field: 'supplierName',
    title: '供应商',
    width: 200,
    slots: {
      // 使用插槽模板渲染
      default: 'supplierName_default'
    }
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    width: 130,
    slots: {
      // 使用插槽模板渲染
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'createUser',
    title: '创建人',
    width: 130
  },
  {
    field: 'createTime',
    title: '凭证日期',
    width: 180
  },
  {
    field: 'sapResult',
    title: 'SAP返回消息',
    width: 240,
    showOverflow: true
  },
  {
    field: 'dmsSendStatus',
    title: '坤合消息状态',
    width: 180,
    slots: {
      // 使用插槽模板渲染
      default: 'dmsSendStatus_default'
    }
  }
]
export default {
  data () {
    return {
      searchForm: {
        isDeleted: '0'
      },
      listData: [],
      createTime: [],
      total: 0,
      pageSize: 10,
      pageNo: 0,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      columns
    }
  },
  components: {
    Pagination,
    RemoteSku,
    RemoteSupplier
  },
  created() {
    if (this.purchaseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    this.getOrderList()
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {},
      warehouseList: state => state.orderPurchase.warehouseList,
      purchaseList: state => state.orderPurchase.purchaseList
    })
  },
  methods: {
    handleSearch () {
      this.getOrderList()
    },
    clean (obj) {
      for (var propName in obj) {
        if (!obj[propName]) {
          delete obj[propName]
        }
        if (propName === 'pickupType' && obj[propName] === '00') {
          obj[propName] = ''
        }
      }
    },
    getOrderList () {
      const param = {
        ...this.searchForm,
        pageNo: this.pageNo,
        pageSize: this.pageSize
      }
      if (this.createTime && this.createTime.length === 2) {
        param.createTimeBegin = this.createTime[0]
        param.createTimeEnd = this.createTime[1]
      }
      if (param.orderNos) {
        const orderNos = param.orderNos.split(' ').filter(item => !!item)
        if (orderNos.length > 0) {
          param.orderNos = orderNos
        }
      }
      if (param.warehouseLocation) {
        const warehouseLocationList = param.warehouseLocation.split('_').filter(item => !!item)
        if (warehouseLocationList && warehouseLocationList.length === 2) {
          param.warehouseLocation = warehouseLocationList[1]
        }
      }
      this.clean(param)
      param.isDeleted = Number(param.isDeleted)
      this.tableLoading = true
      idoList2(param).then(result => {
        this.tableLoading = false
        if (result) {
          const { rows, total } = result
          this.listData = rows || []
          this.total = total
        }
      })
    },
    toDetail (row) {
      this.$router.push({
        path: `/inboundDeliveryOrder/detail/${row.orderNo}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list-container {
  padding: 10px;
  padding-right: 15px;
}
</style>
