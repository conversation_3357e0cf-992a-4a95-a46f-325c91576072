<template>
  <div class="container" v-loading="loading">
    <el-row type="flex" justify="space-between">
      <el-col :span="2"></el-col>
      <el-col :span="24" style="text-align:right">
        <el-button class="btn" type="primary" @click="handleRefresh">刷新</el-button>
        <el-button
          v-if="getButtonAuth('采购管理', '收货入库')"
          class="btn"
          type="primary"
          @click="handlePost"
          :disabled="isDeleted||(detailData&&detailData.isKHWarehouse===0&&detailData.pickupType==='06')"
        >
          收货入库
        </el-button>
        <el-button v-if="getButtonAuth('采购管理', '修改交货单')" class="btn" type="primary" @click="handleEdit" :disabled="isDeleted||isDisableEdit">修改交货单</el-button>
        <el-button v-if="getButtonAuth('采购管理', '取消过账')" class="btn" type="danger" plain @click="handleCancelPost"
          :disabled="isDeleted||(detailData&&!(detailData.isKHWarehouse===0&&detailData.pickupType===null))">取消过账</el-button>
        <el-button v-if="getButtonAuth('采购管理', '取消交货单')" class="btn" type="danger" plain @click="handleDelete" :disabled="isDeleted">取消交货单</el-button>
      </el-col>
    </el-row>
    <DividerHeader>基本信息</DividerHeader>
    <el-row v-for="(fields, idx) in baseInfo" :key="'base'+idx" :gutter="10" >
      <el-col :span="field.span||6" v-for="field in fields" :key="field.field">
        <span class="title">{{field.title}}:</span>
        <span class="value">{{detailData[field.field]}}</span>
        <el-button
          size="mini"
          type="primary" plain
          v-if="field.field==='sapOrderNo'"
          @click="handleResend"
          style="margin-left: 5px"
        >推送S4</el-button>
      </el-col>
    </el-row>
    <DividerHeader>交货信息</DividerHeader>
    <el-row v-for="(fields, idx) in deliveryInfo" :key="'delivery'+idx">
      <el-col :span="field.span||6" v-for="field in fields" :key="field.field">
        <span class="title">{{field.title}}:</span>
        <span class="value">{{field.field === 'isInvoiceAttached' ? mapCheckboxValue(detailData[field.field]) : detailData[field.field]}}</span>
      </el-col>
    </el-row>
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      ref="grid"
      height="400"
      id="grid"
      align="center"
      :custom-config="tableCustom"
      :data="detailData.itemList"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
    >
      <template v-slot:toolbar_buttons>
      </template>
      <template v-slot:factoryCode_default="{ row }">
        {{row.factoryCode + ' ' + ((dictList.supplierFactory||[]).find(item => item.value === row.factoryCode) || {}).name}}
      </template>
      <template v-slot:isDeleted_default="{ row }">
        {{ row.isDeleted === 1 ? '已删除' : '' }}
      </template>
      <template v-slot:warehouseLocation_default="{ row }">
        {{(row.warehouseLocation || '') + ' ' + ((warehouseList.find(item => item.warehouseLocationCode === row.warehouseLocation) || {}).warehouseLocationName || '')}}
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { idoDetail, idoDelete, resendIdoSap, resendIdoMQ, idoPost, idoCancelPost } from '@/api/mm'
import { submitErrorHandler } from '@/utils/mm'
import { getButtonAuth } from '@/utils/auth'
import DividerHeader from '@/components/DividerHeader'

const columns = [
  {
    field: 'isDeleted',
    title: '状态',
    width: 120,
    slots: {
      // 使用插槽模板渲染
      default: 'isDeleted_default'
    }
  },
  {
    field: 'poNo',
    title: '采购单号',
    width: 120
  },
  {
    field: 'poItemNo',
    title: '商品行',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    width: 80
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    width: 200
  },
  {
    field: 'warehouseLocation',
    title: '仓库地点',
    width: 120,
    slots: {
      default: 'warehouseLocation_default'
    }
  },
  {
    field: 'itemQuantity',
    title: '订单数量',
    width: 80
  },
  {
    field: 'deliveryQuantity',
    title: '交货数量',
    width: 80
  },
  {
    field: 'receivedQuantity',
    title: '已收数量',
    width: 80
  },
  {
    field: 'unit',
    title: '订单单位',
    width: 80
  },
  {
    field: 'batchNo',
    title: '批次',
    width: 80
  },
  {
    field: 'supplierBatchNo',
    title: '供应商批次',
    width: 100
  },
  {
    field: 'productionDate',
    title: '生产日期',
    width: 120
  },
  {
    field: 'shelfLifeDays',
    title: '保质期',
    width: 80
  },
  {
    field: 'shortestShelfLifeDays',
    title: '入库保质期下限',
    width: 150
  },
  {
    field: 'factoryCode',
    title: '工厂',
    width: 200,
    slots: {
      // 使用插槽模板渲染
      default: 'factoryCode_default'
    }
  }
]

export default {
  data () {
    return {
      detailData: {},
      loading: false,
      columns,
      baseInfo: [
        [
          { field: 'orderNo', title: '交货单号' },
          { field: 'sapOrderNo', title: 'SAP交货单号' },
          { field: 'postStatusText', title: '过账状态' },
          { field: 'deliveryTime', title: '交货日期' }
        ],
        [
          { field: 'createUser', title: '创建人' },
          { field: 'createTime', title: '创建时间' },
          { field: 'sapResult', title: 'SAP返回消息', span: 12 }
        ]
      ],
      deliveryInfo: [
        [
          { field: 'supplierContactName', title: '供应商联系人' },
          { field: 'supplierContactPhone', title: '供应商联系人电话' },
          { field: 'supplierAddress', title: '供应商地址', span: 12 }
        ],
        [
          { field: 'receiveContactName', title: '收货联系人' },
          { field: 'receiveContactPhone', title: '收货联系人电话' },
          { field: 'receiveAddress', title: '收货地址', span: 12 }
        ],
        [
          { field: 'pickupTypeText', title: '提货类型' },
          { field: 'deliveryRemark', title: '交货备注' }
        ],
        [
          { field: 'soNo', title: '销售单号' },
          { field: 'collectedFeeAmount', title: '到付金额' },
          { field: 'isInvoiceAttached', title: '附发票' }
        ]
      ],
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      }
    }
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    this.getDetail()
  },
  components: {
    DividerHeader
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {},
      warehouseList: state => state.orderPurchase.warehouseList
    }),
    isDisableEdit () {
      const { isKHWarehouse, pickupType, postStatus } = (this.detailData || {})
      return this.detailData && (isKHWarehouse === 1 || (isKHWarehouse === 0 && pickupType === '06') || postStatus !== 'A')
    },
    isDeleted () {
      const { isDeleted } = (this.detailData || {})
      return isDeleted === 1
    }
  },
  methods: {
    getButtonAuth,
    mapCheckboxValue (val) {
      return val === 1 ? '是' : '否'
    },
    handleRefresh () {
      this.getDetail()
    },
    getDetail () {
      const id = this.$route.params.id
      this.loading = true
      idoDetail(id).then(result => {
        this.loading = false
        const { postStatus, pickupType,
          supplierProvinceText = '', supplierCityText = '', supplierRegionText = '', supplierStreetText = '', supplierAddressDetail = '',
          receiveProvinceText = '', receiveCityText = '', receiveRegionText = '', receiveStreetText = '', receiveAddressDetail = ''
        } = result
        const postStatusText = postStatus ? ((this.dictList.postStatus || []).find(item => item.value === postStatus) || {}).name : ''
        const pickupTypeText = postStatus ? ((this.dictList.pickupType || []).find(item => item.value === pickupType) || {}).name : ''
        const supplierAddress = `${supplierProvinceText || ''}${supplierCityText || ''}${supplierRegionText || ''}${supplierStreetText || ''}${supplierAddressDetail || ''}`
        const receiveAddress = `${receiveProvinceText || ''}${receiveCityText || ''}${receiveRegionText || ''}${receiveStreetText || ''}${receiveAddressDetail || ''}`
        this.detailData = {
          ...result,
          postStatusText,
          pickupTypeText,
          supplierAddress,
          receiveAddress
        }
      })
    },
    handleRefreshReq (fn, params) {
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      fn(params).then(res => {
        loading.close()
        if (res) {
          const { code, data, msg } = res
          if (code === 0) {
            this.getDetail()
          } else {
            let errMsg = ''
            submitErrorHandler(errMsg, data, msg)
          }
        }
      })
    },
    handleDelete () {
      this.$confirm('请确认是否要取消该内向交货单？', '取消交货单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning'
      }).then(() => {
        const orderNo = this.$route.params.id
        const updateUser = window.CUR_DATA.user && window.CUR_DATA.user.name
        this.handleRefreshReq(idoDelete, { orderNo, updateUser })
      }).catch(err => console.log(err))
    },
    handleResend () {
      const updateUser = window.CUR_DATA.user && window.CUR_DATA.user.name
      const id = this.$route.params.id
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      Promise.all([
        resendIdoSap({
          orderNo: id,
          updateUser
        }),
        resendIdoMQ({
          orderNo: id,
          updateUser
        })
      ]).then(res => {
        this.getDetail()
        loading.close()
      })
    },
    handlePost () {
      const updateUser = window.CUR_DATA.user && window.CUR_DATA.user.name
      const orderNo = this.$route.params.id
      const { isKHWarehouse, pickupType } = this.detailData
      if (isKHWarehouse === 0 && !pickupType) {
        this.$confirm('请确认是否要对该内向交货单收货入库？', '收货入库', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        }).then(() => {
          this.handleRefreshReq(idoPost, { orderNo, updateUser })
        })
      } else {
        this.handleRefreshReq(idoPost, { orderNo, updateUser })
      }
    },
    handleCancelPost () {
      this.$confirm('请确认是否要取消过账？', '取消过账', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning'
      }).then(() => {
        const updateUser = window.CUR_DATA.user && window.CUR_DATA.user.name
        const orderNo = this.$route.params.id
        this.handleRefreshReq(idoCancelPost, { orderNo, updateUser })
      }).catch(err => console.log(err))
    },
    handleEdit () {
      const id = this.$route.params.id
      this.$closeTag(this.$route.path)
      this.$router.push(`/inboundDeliveryOrder/edit/${id}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  margin: 10px;
  padding: 0 15px;
  .el-row {
    margin-bottom: 20px;
  }
  .title {
    font-size: 14px;
    margin-right: 5px;
    color: #8c8c8c;
  }
  .value {
    font-size: 14px;
    color: #262626;
  }
  .btn {
    width: 90px;
  }
}
</style>
