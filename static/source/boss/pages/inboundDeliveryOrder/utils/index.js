import padStart from 'lodash/padStart'
import { MessageBox } from 'element-ui'

export function formatItemList (data) {
  const { itemList } = data
  const newItemList = itemList.map(item => {
    const newItem = {
      ...item
    }
    if (item.batchNo) {
      newItem.batchNo = padStart(item.batchNo, 10, '0')
    }
    return newItem
  })
  return newItemList
}

export function formatErrMsg (data, msg) {
  let errMsg = '订单创建失败！<br>'
  if (data) {
    const { failReasonList = [] } = data
    if (failReasonList.length > 0) {
      const title = reason => reason.index === -1 ? '抬头:' : `第${(reason.index || 0) + 1}行:`
      errMsg += failReasonList.map(reason => title(reason) + `${reason.reason}`).join('<br>')
    } else if (msg) {
      errMsg = msg
    }
  } else if (msg) {
    errMsg += msg
  }
  MessageBox.alert(errMsg, '失败', {
    type: 'error',
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确定'
  })
}
