<template>
  <vxe-grid
    border
    resizable
    keep-source
    show-overflow
    ref="grid"
    height="400"
    id="grid"
    style="margin-bottom:50px"
    align="center"
    :custom-config="tableCustom"
    :data="itemList"
    :columns="columns"
    :toolbar-config="tableToolbar"
    :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
    :checkbox-config="{checkMethod: checCheckbox}"
    @checkbox-change="checkboxChangeEvent"
    @checkbox-all="checkboxChangeEvent"
  >
    <template v-slot:toolbar_buttons>
      <el-button type="primary" size="mini" @click="handleDeleteItem">删除项目行</el-button>
    </template>
    <template v-slot:supplierName_default="{ row }">
      {{row.supplierNo + ' ' + row.supplierName}}
    </template>
    <template v-slot:isDeleted_default="{ row }">
      {{ row.isDeleted === 1 ? '已删除' : '' }}
    </template>
    <template v-slot:deliveryQuantity_default="{ row }">
      <el-input-number
        v-model="row.deliveryQuantity"
        size="mini"
        :min="0"
        :precision="3"
        :disabled="row.deliveryQuantity===0"
        placeholder="交货数量"
      />
    </template>
    <template v-slot:batchNo_default="{ row }">
      <el-input
        style="width:210px"
        v-if="row.isBatchNoEditable"
        type="text"
        v-model="row.batchNo"
        size="mini"
        placeholder="请输入批次(字母大写+数字）"
        maxlength="10"
        show-word-limit
        clearable
        @keyup.native="row.batchNo=row.batchNo.replace(/[^0-9A-Z]/g,'')"
      ></el-input>
      <span>{{row.batchNo}}</span>
    </template>
    <template v-slot:supplierBatchNo_default="{ row }">
      <el-input type="text" v-model="row.supplierBatchNo" size="mini" placeholder="请输入供应商批次"></el-input>
    </template>
    <template v-slot:productionDate_default="{ row }">
      <el-date-picker
        style="width:100%"
        v-model="row.productionDate"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期">
      </el-date-picker>
    </template>
    <template v-slot:factoryCode_default="{ row }">
      {{row.factoryCode + ' ' + ((dictList.supplierFactory||[]).find(item => item.value === row.factoryCode) || {}).name}}
    </template>
    <template v-slot:warehouseLocation_default="{ row }">
      {{(row.warehouseLocation || '') + ' ' + ((warehouseList.find(item => item.warehouseLocationCode === row.warehouseLocation) || {}).warehouseLocationName || '')}}
    </template>
  </vxe-grid>
</template>

<script>
import { mapState } from 'vuex'

const columns = (type) => {
  const arr = [
    { type: 'checkbox', width: 60 },
    {
      field: 'poNo',
      title: '采购单号',
      width: 120
    },
    {
      field: 'poItemNo',
      title: '商品行',
      width: 80
    },
    {
      field: 'skuNo',
      title: 'SKU编码',
      width: 80
    },
    {
      field: 'materialDescription',
      title: '物料描述',
      width: 200
    },
    {
      field: 'warehouseLocation',
      title: '仓库地点',
      width: 120,
      slots: {
        default: 'warehouseLocation_default'
      }
    },
    {
      field: 'itemQuantity',
      title: '订单数量',
      width: 80
    },
    {
      field: 'deliveryQuantity',
      title: '交货数量',
      width: 180,
      slots: {
        default: 'deliveryQuantity_default'
      }
    },
    {
      field: 'receivedQuantity',
      title: '已过账数量',
      width: 90
    },
    {
      field: 'unit',
      title: '订单单位',
      width: 80
    },
    {
      field: 'batchNo',
      title: '批次',
      width: 220,
      slots: {
        default: 'batchNo_default'
      }
    },
    {
      field: 'supplierBatchNo',
      title: '供应商批次',
      width: 150,
      slots: {
        default: 'supplierBatchNo_default'
      }
    },
    {
      field: 'productionDate',
      title: '生产日期',
      width: 200,
      slots: {
        default: 'productionDate_default'
      }
    },
    {
      field: 'shelfLifeDays',
      title: '保质期',
      width: 120
    },
    {
      field: 'shortestShelfLifeDays',
      title: '入库保质期下限',
      width: 160
    },
    {
      field: 'supplierName',
      title: '供应商',
      width: 200,
      slots: {
        default: 'supplierName_default'
      }
    },
    {
      field: 'factoryCode',
      title: '工厂',
      width: 180,
      slots: {
        default: 'factoryCode_default'
      }
    }
  ]
  if (type === 'edit') {
    arr.splice(1, 0, {
      field: 'isDeleted',
      title: '状态',
      width: 120,
      slots: {
        default: 'isDeleted_default'
      }
    })
  }
  return arr
}

export default {
  data () {
    return {
      columns: columns(this.type),
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      selectRecords: [],
      rules: {}
    }
  },
  props: {
    inboundData: {
      type: Object,
      default: () => ({
        itemList: []
      })
    },
    type: {
      type: String
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {},
      warehouseList: state => state.orderPurchase.warehouseList
    }),
    itemList () {
      return this.inboundData.itemList
    }
  },
  methods: {
    checCheckbox ({ row }) {
      return row.isDeleted !== 1 && row.deliveryQuantity > 0
    },
    checkboxChangeEvent ({ records }) {
      this.selectRecords = records
      this.$emit('selectedItemChange', records)
    },
    handleDeleteItem () {
      const len = (this.inboundData.itemList || []).length
      for (let i = len - 1; i >= 0; i--) {
        const item = this.inboundData.itemList[i]
        const found = this.selectRecords.find(record => {
          return record.poNo === item.poNo && record.poItemNo === item.poItemNo
        })
        if (found) {
          if (this.type === 'create') {
            this.inboundData.itemList.splice(i, 1)
          } else {
            this.$set(this.inboundData.itemList, i, {
              ...this.inboundData.itemList[i],
              isDeleted: 1
            })
          }
        }
      }
      this.selectRecords = []
      this.$emit('selectedItemChange', this.selectRecords)
    }
  }
}
</script>

<style lang="scss" scoped>
#grid {
  margin-bottom: 50px;
}
</style>
