<template>
  <el-form :model="inboundData" style="width: 100%" ref="orderForm" label-width="150px" label-suffix=":" :rules="rules">
    <el-row :gutter="20">
      <el-col :span="16">
        <el-form-item label="供应商">
          <el-input
            :value="inboundData.supplierNo+' '+inboundData.supplierName"
            disabled
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="供应商联系人">
          <el-select
            v-model="inboundData.supplierContactName"
            clearable
            filterable
            allow-create
            style="width:100%"
            @change="handleChangeSupplierContactName"
          >
            <el-option
              v-for="(item,idx) in supplierContactList"
              :key="idx+item.phone"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="供应商联系人电话">
          <el-input
            v-model="inboundData.supplierContactPhone"
            clearable
            placeholder="供应商联系人电话"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="22">
        <el-form-item label="供应商地址">
          <el-select
            v-model="inboundData.supplierAddress"
            clearable
            filterable
            :disabled="newAddress"
            @change="handleChangeSupplierAddress"
            style="width:100%"
          >
            <el-option
              v-for="(item,idx) in supplierAddressList"
              :key="idx+item.detail"
              :label="fullDetailAddress(item)"
              :value="fullDetailAddress(item)">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="2">
        <el-button style="width: 100%" :disabled="newAddress" @click="addAddress">新增</el-button>
      </el-col>
      <el-col v-if="newAddress" :span="10">
        <el-form-item label="">
          <ProvinceCascader
            v-model="inboundData.provinceArray"
            :maxDepth="4"
            :checkStrictly="true"
            @provinceChange="provinceChange"
            style="width:100%"
          />
        </el-form-item>
      </el-col>
      <el-col v-if="newAddress" :span="12">
        <el-input
          clearable
          v-model="inboundData.supplierAddressDetail"
          placeholder="详细地址"
          @change="handleDetailAddress"
        />
      </el-col>
      <el-col v-if="newAddress" :span="2">
        <el-button style="width: 100%" @click="delAddress">删除</el-button>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="收货联系人">
          <el-select
            v-model="inboundData.receiveContactName"
            clearable
            allow-create
            filterable
            style="width:100%"
            @change="handleChangeReceiveContactName"
          >
            <el-option
              v-for="(item, idx) in receiveContactList"
              :key="idx+item.phone"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="收货联系人电话">
          <el-input
            v-model="inboundData.receiveContactPhone"
            clearable
            placeholder="收货联系人电话"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="收货地址">
          <el-select
              v-model="inboundData.receiveAddress"
              clearable
              filterable
              style="width:100%"
              @change="handleChangeReceiveAddress"
            >
              <el-option
                v-for="(item,idx) in receiveAddressList"
                :key="idx+item.detail"
                :label="fullDetailAddress(item)"
                :value="fullDetailAddress(item)">
              </el-option>
            </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item label="交货备注">
          <el-input
            type="textarea"
            v-model="inboundData.deliveryRemark"
            clearable
            show-word-limit
            :maxlength="200"
            placeholder="交货备注"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="提货类型">
          <el-select
            v-model="inboundData.pickupType"
            filterable
            clearable
            style="width:100%"
          >
            <el-option
              v-for="item in dictList.pickupType"
              :key="item.value"
              :label="item.value+' '+item.name"
              :value="item.value"
              :disabled="['02', '06'].includes(item.value)"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="3">
        <el-form-item label="附发票">
          <el-checkbox
            v-model="inboundData.isInvoiceAttached"
            :true-label="1"
            :false-label="0"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="销售订单号">
          <el-input
            v-model="inboundData.soNo"
            clearable
            placeholder="自提直送，此项必填"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="到付金额">
          <el-input-number
            style="width:236px"
            v-model="inboundData.collectedFeeAmount"
            clearable
            :min="0"
            :step="1"
            :precision="2"
            placeholder="到付，此项必填"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import { safeRun } from '@/utils/index'
import ProvinceCascader from '@/components/SearchFields/provinceCascaderAsync'
export default {
  components: {
    ProvinceCascader
  },
  data () {
    return {
      newAddress: false,
      rules: {}
    }
  },
  props: [
    'inboundData',
    'receiptAddressList', 'receiptContactList', 'receiveAddressList',
    'receiveContactList', 'supplierAddressList', 'supplierContactList'
  ],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {}
    })
  },
  methods: {
    delAddress () {
      this.newAddress = false
      this.inboundData.provinceArray = []
      this.inboundData.supplierAddressDetail = ''
      this.inboundData.supplierAddress = ''
    },
    handleDetailAddress (value) {
      this.inboundData.supplierAddressDetail = value
    },
    provinceChange (array) {
      safeRun(() => {
        console.log(array)
        // 省市区街道赋值
        if (!array || array.length === 0) {
          this.inboundData.supplierProvince = ''
          this.inboundData.supplierCity = ''
          this.inboundData.supplierRegion = ''
          this.inboundData.supplierStreet = ''
          return
        }
        this.inboundData.supplierProvince = array[0].value
        this.inboundData.supplierCity = (array[1] && array[1].value) || ''
        this.inboundData.supplierRegion = (array[2] && array[2].value) || ''
        this.inboundData.supplierStreet = (array[3] && array[3].value) || ''
        this.inboundData.supplierAddress = array[0].label
        if (array[1] && array[1].label) {
          this.inboundData.supplierAddress += array[1].label
        }
        if (array[2] && array[2].label) {
          this.inboundData.supplierAddress += array[2].label
        }
        if (array[3] && array[3].label) {
          this.inboundData.supplierAddress += array[3].label
        }
      })
    },
    addAddress() {
      this.newAddress = true
      this.setAddress('supplier')
      this.inboundData.supplierAddress = ''
    },
    fullDetailAddress (item) {
      let ret = ''
      ret = (item.provinceText || '') +
        (item.cityText || '') +
        (item.regionText || '') +
        (item.streetText || '') +
        (item.detail || '')
      return ret
    },
    handleChangeSupplierContactName (val) {
      if (val) {
        const supplierContact = this.supplierContactList.find(item => item.name === val)
        if (supplierContact) {
          this.inboundData.supplierContactPhone = supplierContact.phone
        }
      } else {
        this.inboundData.supplierContactPhone = ''
      }
    },
    handleChangeSupplierAddress (val) {
      if (val) {
        const supplierAddress = this.supplierAddressList.find(item => this.fullDetailAddress(item) === val)
        if (supplierAddress) {
          this.setAddress('supplier', supplierAddress)
        }
      } else {
        this.setAddress('supplier')
      }
    },
    setAddress (type, address = {}) {
      const {
        detail = '', city = '', province = '',
        region = '', street = '', postCode = ''
      } = address || {}
      this.inboundData[`${type}AddressDetail`] = detail
      this.inboundData[`${type}City`] = city
      this.inboundData[`${type}Province`] = province
      this.inboundData[`${type}Region`] = region
      this.inboundData[`${type}Street`] = street
      this.inboundData[`${type}AddressPostCode`] = postCode
    },
    handleChangeReceiveContactName (val) {
      if (val) {
        const contact = this.receiveContactList.find(item => item.name === val)
        if (contact) {
          this.inboundData.receiveContactPhone = contact.phone
        }
      } else {
        this.inboundData.receiveContactPhone = ''
      }
    },
    handleChangeReceiveAddress (val) {
      if (val) {
        const address = this.receiveAddressList.find(item => this.fullDetailAddress(item) === val)
        if (address) {
          this.setAddress('receive', address)
        }
      } else {
        this.setAddress('receive')
      }
    },
    submit (formName) {
      this.inboundData.newAddress = this.newAddress
      this.$emit('submit', this.inboundData)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
