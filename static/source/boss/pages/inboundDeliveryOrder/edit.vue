<template>
  <div class="create-container" v-loading="loading">
    <ItemForm
      ref="orderForm"
      :inboundData="inboundData"
      :receiptAddressList="receiptAddressList"
      :receiptContactList="receiptContactList"
      :receiveAddressList="receiveAddressList"
      :receiveContactList="receiveContactList"
      :supplierAddressList="supplierAddressList"
      :supplierContactList="supplierContactList"
      @submit="handleSubmit"
    />
    <ItemTable :inboundData="inboundData" type="edit"/>
    <div class="fixed-create">
      <el-button @click="handleReturn" size="large" style="width:100px">返回</el-button>
      <el-button type="primary" @click="handleModify" size="large" style="width:100px">修改</el-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { listCommOptions, idoDetail, idoUpdate } from '@/api/mm'
import ItemTable from './components/itemTable'
import ItemForm from './components/itemForm'
import { formatErrMsg } from './utils'

export default {
  data () {
    return {
      inboundData: {
        itemList: []
      },
      loading: false,
      receiptAddressList: [],
      receiptContactList: [],
      receiveAddressList: [],
      receiveContactList: [],
      supplierAddressList: [],
      supplierContactList: []
    }
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    this.getDetail()
  },
  components: {
    ItemForm, ItemTable
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {}
    }),
    isDisableEdit () {
      const { isKHWarehouse, pickupType } = (this.detailData || {})
      return this.detailData && (isKHWarehouse === 1 || (isKHWarehouse === 0 && pickupType === '06'))
    }
  },
  methods: {
    getDetail () {
      const id = this.$route.params.id
      this.loading = true
      idoDetail(id).then(result => {
        this.loading = false
        const { postStatus, pickupType, itemList, supplierNo, supplierName, collectedFeeAmount,
          supplierProvinceText = '', supplierCityText = '', supplierRegionText = '', supplierStreetText = '', supplierAddressDetail = '',
          receiveProvinceText = '', receiveCityText = '', receiveRegionText = '', receiveStreetText = '', receiveAddressDetail = ''
        } = result
        const postStatusText = postStatus ? ((this.dictList.postStatus || []).find(item => item.value === postStatus) || {}).name : ''
        const pickupTypeText = postStatus ? ((this.dictList.pickupType || []).find(item => item.value === pickupType) || {}).name : ''
        const supplierAddress = `${supplierProvinceText || ''}${supplierCityText || ''}${supplierRegionText || ''}${supplierStreetText || ''}${supplierAddressDetail || ''}`
        const receiveAddress = `${receiveProvinceText || ''}${receiveCityText || ''}${receiveRegionText || ''}${receiveStreetText || ''}${receiveAddressDetail || ''}`
        this.inboundData = {
          ...result,
          postStatusText,
          pickupTypeText,
          supplierAddress,
          receiveAddress,
          collectedFeeAmount: collectedFeeAmount === null ? undefined : collectedFeeAmount
        }
        let factoryCode = ''
        if (itemList) {
          this.inboundData.itemList = itemList.map(item => ({
            ...item,
            isBatchNoEditable: postStatus === 'A',
            supplierNo,
            supplierName
          }))
          if (itemList.length > 0) {
            factoryCode = itemList[0].factoryCode
          }
        }
        if (supplierNo && factoryCode) {
          this.queryAddress(supplierNo, factoryCode)
        }
      })
    },
    async queryAddress (supplierNo, factoryCode) {
      const { receipt, receive, supplier } = await listCommOptions({
        supplierNo,
        factoryCode
      })
      if (receipt) {
        const { addressOptions = [], contactOptions = [] } = receipt
        this.receiptAddressList = addressOptions
        this.receiptContactList = contactOptions
      }
      if (receive) {
        const { addressOptions = [], contactOptions = [] } = receive
        this.receiveAddressList = addressOptions
        this.receiveContactList = contactOptions
      }
      if (supplier) {
        const { addressOptions = [], contactOptions = [] } = supplier
        this.supplierAddressList = addressOptions
        this.supplierContactList = contactOptions
      }
    },
    handleModify () {
      this.$refs.orderForm.submit('orderForm')
    },
    handleReturn () {
      const id = this.$route.params.id
      this.$closeTag(this.$route.path)
      setTimeout(() => {
        this.$router.push({
          path: `/inboundDeliveryOrder/detail/${id}`
        })
      })
    },
    handleSubmit (data) {
      const { collectedFeeAmount, isInvoiceAttached } = data
      if (collectedFeeAmount > 500 && isInvoiceAttached === 0) {
        this.$confirm('到付金额超出500元，请确认是否需要附发票？', '提示', {
          confirmButtonText: '忽略并提交',
          cancelButtonText: '返回勾选',
          closeOnClickModal: false,
          type: 'warning',
          center: true
        }).then(() => {
          this.handleSubmitReq(data)
        })
      } else {
        this.handleSubmitReq(data)
      }
    },
    handleSubmitReq (data) {
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const updateUser = window.CUR_DATA.user && window.CUR_DATA.user.name
      idoUpdate({
        ...data,
        updateUser
      }).then(res => {
        loading.close()
        if (res) {
          const { code, data, msg } = res
          if (code === 0 && data) {
            const { orderNo } = data
            if (orderNo) {
              this.$message.success('更新内向交货单成功！')
              this.$closeTag(this.$route.path)
              this.$router.push(`/inboundDeliveryOrder/detail/${orderNo}`)
            }
          } else if (code !== 0) {
            formatErrMsg(data, msg)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.create-container {
  margin: 10px 0;
  width: 100%;
}
.fixed-create {
  text-align: center;
  margin-top: 10px;
  position: fixed;
  bottom: 0px;
  z-index: 1000;
  padding: 10px;
  width:inherit;
  max-width:inherit;
  transform: translateX(-10px);
  background-color: whitesmoke;
}
</style>
