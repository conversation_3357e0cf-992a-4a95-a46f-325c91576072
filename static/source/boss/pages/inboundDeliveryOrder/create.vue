<template>
  <div class="create-container" v-loading="detailLoading">
    <ItemForm
      ref="orderForm"
      :inboundData="inboundData"
      :receiptAddressList="receiptAddressList"
      :receiptContactList="receiptContactList"
      :receiveAddressList="receiveAddressList"
      :receiveContactList="receiveContactList"
      :supplierAddressList="supplierAddressList"
      :supplierContactList="supplierContactList"
      @submit="handleSubmit"
    />
    <ItemTable
      :inboundData="inboundData"
      type="create"
      @selectedItemChange="handleItemChange"
    />
    <div class="fixed-create">
      <el-button type="primary" @click="handleSave" size="large" style="width:100px">创建</el-button>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { idoEnter, listCommOptions, idoCreate } from '@/api/mm'
import ItemTable from './components/itemTable'
import ItemForm from './components/itemForm'
import { formatErrMsg } from './utils'

export default {
  data () {
    return {
      detailLoading: false,
      inboundData: {
        isInvoiceAttached: 0,
        pickupType: '',
        deliveryRemark: '',
        supplierAddress: null,
        supplierContactName: null,
        supplierContactPhone: null,
        itemList: [],
        receiveContactName: null,
        receiveContactPhone: null,
        receiveAddress: null,
        receiveAddressDetail: null,
        supplierAddressDetail: null
      },
      receiptAddressList: [],
      receiptContactList: [],
      receiveAddressList: [],
      receiveContactList: [],
      supplierAddressList: [],
      supplierContactList: [],
      selectRecords: [],
      rules: {}
    }
  },
  components: {
    ItemTable, ItemForm
  },
  created () {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    const id = this.$route.params.id
    if (id) {
      const ids = id.split('_')
      this.detailLoading = true
      idoEnter(ids).then(data => {
        this.detailLoading = false
        const { itemList, supplierNo, supplierName, supplierContactName, supplierContactPhone,
          supplierProvince, supplierCity, supplierRegion, supplierStreet, supplierAddressDetail,
          receiveContactName, receiveContactPhone, deliveryRemark,
          receiveProvince, receiveCity, receiveRegion, receiveStreet, receiveAddressDetail,
          pickupType
        } = data
        this.inboundData.deliveryRemark = deliveryRemark
        this.inboundData.supplierNo = supplierNo
        this.inboundData.supplierName = supplierName
        this.inboundData.receiveProvince = receiveProvince
        this.inboundData.receiveCity = receiveCity
        this.inboundData.receiveRegion = receiveRegion
        this.inboundData.receiveStreet = receiveStreet
        this.inboundData.receiveAddressDetail = receiveAddressDetail
        this.inboundData.receiveContactName = receiveContactName
        this.inboundData.receiveContactPhone = receiveContactPhone
        this.inboundData.receiveAddress = (data.receiveProvinceText || '') +
                  (data.receiveCityText || '') +
                  (data.receiveRegionText || '') +
                  (data.receiveStreetText || '') +
                  (data.receiveAddressDetail || '')

        this.inboundData.supplierProvince = supplierProvince
        this.inboundData.supplierCity = supplierCity
        this.inboundData.supplierRegion = supplierRegion
        this.inboundData.supplierStreet = supplierStreet
        this.inboundData.supplierAddressDetail = supplierAddressDetail
        this.inboundData.supplierContactName = supplierContactName
        this.inboundData.supplierContactPhone = supplierContactPhone
        this.inboundData.supplierAddress = (data.supplierProvinceText || '') +
                  (data.supplierCityText || '') +
                  (data.supplierRegionText || '') +
                  (data.supplierStreetText || '') +
                  (data.supplierAddressDetail || '')
        if (pickupType && pickupType !== '02') {
          this.inboundData.pickupType = pickupType
        }
        let factoryCode = ''
        if (itemList) {
          this.inboundData.itemList = itemList.map(item => ({
            ...item,
            isBatchNoEditable: !item.batchNo,
            supplierNo,
            supplierName
          }))
          if (itemList.length > 0) {
            factoryCode = itemList[0].factoryCode
          }
        }
        if (supplierNo && factoryCode) {
          this.queryAddress(supplierNo, factoryCode)
        }
        // 内向交货单交货备注
        // getUrgentNote({ poNos: ids.join(',') })
        //   .then(data => {
        //     console.log(data)
        //     if (data) {
        //       this.inboundData.deliveryRemark = data
        //     }
        //   })
      })
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {},
      warehouseList: state => state.orderPurchase.warehouseList
    })
  },
  methods: {
    async queryAddress (supplierNo, factoryCode) {
      const { receipt, receive, supplier } = await listCommOptions({
        supplierNo,
        factoryCode
      })
      if (receipt) {
        const { addressOptions = [], contactOptions = [] } = receipt
        this.receiptAddressList = addressOptions
        this.receiptContactList = contactOptions
      }
      if (receive) {
        const { addressOptions = [], contactOptions = [] } = receive
        this.receiveAddressList = addressOptions
        this.receiveContactList = contactOptions
      }
      if (supplier) {
        const { addressOptions = [], contactOptions = [] } = supplier
        this.supplierAddressList = addressOptions
        this.supplierContactList = contactOptions
      }
    },
    handleSave () {
      this.$refs.orderForm.submit('orderForm')
    },
    validateAddress (data) {
      let pass = true
      if (data.newAddress) {
        const { supplierProvince, supplierCity, supplierRegion, supplierAddressDetail } = data
        if (!supplierProvince || !supplierCity || !supplierRegion || !supplierAddressDetail) {
          pass = false
          this.$message.error('供应商地址：省市区及详细地址必填！')
        }
      }
      return pass
    },
    handleSubmitReq (data) {
      console.log(data)
      if (!this.validateAddress(data)) return
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const { itemList } = data
      idoCreate({
        ...data,
        itemList: (this.selectRecords && this.selectRecords.length > 0) ? this.selectRecords : itemList,
        createUser: window.CUR_DATA.user && window.CUR_DATA.user.name
      }).then(res => {
        loading.close()
        if (res) {
          const { code, data, msg } = res
          if (code === 0 && data) {
            const { orderNo } = data
            if (orderNo) {
              this.$message.success('创建内向交货单成功！')
              this.$closeTag(this.$route.path)
              this.$router.push({
                path: '/inboundDeliveryOrder/detail/' + orderNo
              })
            }
          } else if (code !== 0) {
            formatErrMsg(data, msg)
          }
        }
      })
    },
    handleSubmit (data) {
      const { collectedFeeAmount, isInvoiceAttached } = data
      if (collectedFeeAmount > 500 && isInvoiceAttached === 0) {
        this.$confirm('到付金额超出500元，请确认是否需要附发票？', '提示', {
          confirmButtonText: '忽略并提交',
          cancelButtonText: '返回勾选',
          closeOnClickModal: false,
          type: 'warning',
          center: true
        }).then(() => {
          this.handleSubmitReq(data)
        })
      } else {
        this.handleSubmitReq(data)
      }
    },
    handleItemChange (items) {
      this.selectRecords = items
    }
  }
}
</script>

<style lang="scss" scoped>
.create-container {
  padding: 10px 0px;
}
.fixed-create{
  text-align: center;
  margin-top: 10px;
  position: fixed;
  bottom: 0px;
  z-index: 1000;
  padding: 10px;
  width: 100%;
  transform: translateX(-10px);
  background-color: whitesmoke;
  button{
    margin-left: -200px;
  }
}
</style>
