<template>
  <div class="push-sap-container" v-loading="pushSapDialogData.loading.dialogLoading">
    <div class="step1" v-show="pushSapDialogData.step === 0">
      <h1>第一步：选中对账单明细，提交SAP发票草稿，产生应收</h1>
      <div class="search-group">
        <el-form label-width="100px" label-position="right" ref="pushSapDialogStep1Form" :model="pushSapDialogData">
          <el-row>
            <el-col :span="16">
              <el-form-item label="订单号" prop="soNo" >
                <el-input clearable v-model="pushSapDialogData.soNo" placeholder="外围订单号、销售订单号、客户订单号 均支持搜索，同时支持5000个单号，以空格分隔"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="SAP同步状态" prop="sapSyncStatus">
                <el-select v-model="pushSapDialogData.sapSyncStatus" placeholder="请选择">
                  <el-option v-for="item in pushSapDialogData.sapSyncOptions" :key="item.label"  :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="对账依据编号" prop="basisNo">
                <el-input clearable v-model="pushSapDialogData.basisNo" placeholder="同时支持5000个单号，以空格分隔"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="16">
              <el-form-item label="产品编码" prop="productSku">
                <el-input clearable v-model="pushSapDialogData.productSku" placeholder="产品编号、客户物料号 均支持搜索，同时支持500个单号，以空格分隔"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="领料人" prop="productPicker">
                <el-input clearable v-model="pushSapDialogData.productPicker" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="需求部门" prop="requirementsDept">
                <el-input clearable v-model="pushSapDialogData.requirementsDept" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="收票联系人" prop="ticketContactName">
                <el-input clearable v-model="pushSapDialogData.ticketContactName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="收货联系人" prop="shippingContactName">
                <el-input clearable v-model="pushSapDialogData.shippingContactName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单联系人" prop="orderContactName">
                <el-input clearable v-model="pushSapDialogData.orderContactName" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否含税" prop="conditionType">
                <el-select clearable v-model="pushSapDialogData.conditionType" placeholder="请选择">
                  <el-option v-for="item in pushSapDialogData.conditionTypeOptions" :key="item.label"  :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否成品油" prop="refinedOil">
                <el-select clearable v-model="pushSapDialogData.refinedOil" placeholder="请选择">
                  <el-option v-for="item in pushSapDialogData.refinedOilOptions" :key="item.label"  :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="收票方：" prop="invoiceReceiver">
                <RemoteCustomer ref="customer" v-model="pushSapDialogData.invoiceReceiver" getValue :rowCount="100" :options="[{'customerNumber': '客户编码', 'customerName': '客户名称'}]" showItem />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8" :offset="8" style="text-align:center;display:flex;justify-content:center;">
              <el-button type="primary" @click="searchPushSap" :loading="pushSapDialogData.loading.searchLoading">查询</el-button>
              <el-button type="default" @click="resetSearchPushSap">重置</el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="statistics">
        <span>对账含税总金额：{{fixedDecimalPlace2(pushSapDialogData.moneySummary.checkAmountTax)}}</span>
        <span>对账未税总金额：{{fixedDecimalPlace2(pushSapDialogData.moneySummary.checkAmount)}}</span>
      </div>

      <vxe-table
        border
        show-overflow
        highlight-hover-row
        ref="pushSapDialogTable"
        height="300"
        :data="pushSapDialogData.list"
        :loading="pushSapDialogData.loading.tableLoading"
        @checkbox-all="selectChangeEvent"
        @checkbox-change="selectChangeEvent"
      >
        <vxe-column type="checkbox" width="60"></vxe-column>
        <vxe-column :width="column.width || 120" :key="index"
          v-for="(column, index) in pushSapDialogData.renderStep1Fields"
          :title="column.label"
          :show-overflow="column.showOverflow"
          >
          <template #default="{ row }">
            <div v-if="column.format">{{column.format(row[column.prop])}}</div>
            <div v-else>{{row[column.prop]}}</div>
          </template>
        </vxe-column>
      </vxe-table>
      <div class="push-sap-button">
        <el-button type="default" :disabled="cancelStatus" @click="cancelPushSap">取消</el-button>
        <el-button type="primary" :disabled="disableNextStepStatus" @click="nextSapStep">下一步</el-button>
      </div>
    </div>
    <div class="step2" v-show="pushSapDialogData.step >= 1">
      <h1>第二步：修改SAP发票信息，确认后即提交SAP开票</h1>
      <div class="diff-icon">
        <p v-if="pushSapDialogData.tipStatus.multiInvoicingByMail !=='0'"><i class="el-icon-warning"></i>凭邮件开票 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiReturnOffset !=='0'"><i class="el-icon-warning"></i>退换货抵消 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiMergeBilling !=='0'"><i class="el-icon-warning"></i>合并开票 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiBillingRobot !=='0'"><i class="el-icon-warning"></i>开票机器人 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiShowDiscount !=='0'"><i class="el-icon-warning"></i>显示折扣 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiTicketContact !== '0'"><i class="el-icon-warning"></i>收票联系人 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiOrderContact !== '0'"><i class="el-icon-warning"></i>订单联系人 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiIfDocMailed !=='0'"><i class="el-icon-warning"></i>附邮寄资料 有多个值</p>
        <p v-if="pushSapDialogData.tipStatus.multiExpressCompany !=='0'"><i class="el-icon-warning"></i>快递公司 有多个值</p>
      </div>
      <div class="step2-checkbox">
        <el-checkbox-group style="margin-right: 30px" v-model="pushSapDialogData.checkbox1" @change="checkboxChange1">
          <el-checkbox v-for="(item, index) in pushSapDialogData.checkboxOptions1" :label="item.value" :key="index">{{item.label}}</el-checkbox>
        </el-checkbox-group>
        <el-checkbox-group v-model="pushSapDialogData.checkbox" @change="checkboxChange">
          <el-checkbox v-for="(item, index) in pushSapDialogData.checkboxOptions" :label="item.value" :key="index">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </div>
      <el-form ref="pushSapDialogStep2Form" :inline="true" :model="pushSapDialogData.secondStepFields">
        <el-row :gutter="20" v-for="(fieldsList, index) in pushSapDialogData.renderStep2Fields" :key="index">
          <el-col v-for="(field, index) in fieldsList" :key="index"  :span="field.span || 12">
            <el-form-item v-if="field.prop === 'ticketContactName'" :label="field.label" :prop="field.prop" label-width="120px">
              <el-select
                style="width: 240px"
                filterable
                remote
                reserve-keyword
                v-model="pushSapDialogData.secondStepFields[field.prop]"
                @change="ticketContactNameChange"
                :remote-method="debounceRemoteTicketSearch"
                :loading="pushSapDialogData.loading.ticketLoading"
                placeholder="请输入关键词"
                clearable>
                <el-option style="color:#ccc" disabled :value="-1" v-show="pushSapDialogData.secondStepFields.ticketContact && pushSapDialogData.secondStepFields.ticketContact.length >=20">
                  已展示部分联系人，其他联系人请输入字符进行查询
                </el-option>
                <el-option
                  v-for="(item, itemIdx) in pushSapDialogData.secondStepFields.ticketContact"
                  :key="item.contact"
                  :label="item.contactName"
                  :disabled="itemIdx===0"
                  :value="item.contact">
                  <div
                    class="ba-row-start selectClientItem"
                    :style="{fontWeight:itemIdx===0?'bold':'normal'}"
                  >
                    <div>{{ item.contactName }}</div>
                    <div>{{ item.contact }}</div>
                    <div>{{ item.contactPhone || '--' }}</div>
                    <div>
                      <el-tooltip v-if="item.address" effect="dark" placement="top" :content="item.address">
                        <div>{{ item.address || '--' }}</div>
                      </el-tooltip>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-else-if="field.prop === 'orderContactName'" :label="field.label" :prop="field.prop" label-width="120px">
              <el-select
                style="width: 240px"
                filterable
                remote
                reserve-keyword
                :remote-method="debounceRemoteOrderSearch"
                @change="orderContactNameChange"
                :loading="pushSapDialogData.loading.orderLoading"
                v-model="pushSapDialogData.secondStepFields[field.prop]"
                placeholder="请输入关键词"
                clearable>
                <el-option style="color:#ccc" disabled :value="-1" v-show="pushSapDialogData.secondStepFields.orderContact && pushSapDialogData.secondStepFields.orderContact.length >=20">
                  已展示部分联系人，其他联系人请输入字符进行查询
                </el-option>
                <el-option
                  v-for="(item, itemIdx) in pushSapDialogData.secondStepFields.orderContact"
                  :key="item.contact"
                  :label="item.contactName"
                  :disabled="itemIdx===0"
                  :value="item.contact">
                  <div
                    class="ba-row-start selectClientItem"
                    :style="{fontWeight:itemIdx===0?'bold':'normal'}"
                  >
                    <div>{{ item.contactName }}</div>
                    <div>{{ item.contact }}</div>
                    <div>{{ item.contactPhone || '--' }}</div>
                    <div>
                      <el-tooltip v-if="item.address" effect="dark" placement="top" :content="item.address">
                        <div>{{ item.address || '--' }}</div>
                      </el-tooltip>
                    </div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-else-if="field.prop === 'invoiceType'" :label="field.label" :prop="field.prop" label-width="120px">
              <el-select
                style="width: 240px"
                v-model="pushSapDialogData.secondStepFields[field.prop]"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in pushSapDialogData.secondStepFields.invoiceTypeOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
              <el-tooltip v-if="customerInfo" effect="dark" placement="top">
                <div slot="content">
                  收票方：{{customerInfo.customerName}}<br/>
                  税号：{{customerInfo.corporationTaxNum}}<br/>
                  开户行：{{customerInfo.bankName}}<br/>
                  账号：{{customerInfo.bankNumber}}<br/>
                  地址：{{customerInfo.registerAddress}}<br/>
                  电话：{{customerInfo.invoiceTelePhone}}
                </div>
                <span style="margin-left: 10px;color:#597bee;">开票信息</span>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-else-if="field.prop === 'expressCompany'" :label="field.label" :prop="field.prop" label-width="120px">
              <el-select
                style="width: 240px"
                v-model="pushSapDialogData.secondStepFields[field.prop]"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in pushSapDialogData.secondStepFields.expressCompanyOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-else-if="field.prop === 'sfType' && pushSapDialogData.secondStepFields['expressCompany'] === '2'" :label="field.label" :prop="field.prop" label-width="120px">
              <el-radio v-model="pushSapDialogData.secondStepFields[field.prop]" label="01">顺丰特快</el-radio>
              <el-radio v-model="pushSapDialogData.secondStepFields[field.prop]" label="02">顺丰标快</el-radio>
            </el-form-item>
            <el-form-item v-else-if="field.prop === 'ticketAddress'" :label="field.label" :prop="field.prop" label-width="120px">
              <span>{{pushSapDialogData.secondStepFields[field.prop]}}</span>
            </el-form-item>
            <el-form-item v-else-if="field.label && field.prop !== 'sfType'" :label="field.label" :prop="field.prop" label-width="120px">
              <el-input
                :type="field.type || ''"
                :title="pushSapDialogData.secondStepFields[field.prop]"
                style="width: 240px"
                @input="(val) => handleInputChange(val, field.maxlength, field.prop)"
                v-model="pushSapDialogData.secondStepFields[field.prop]"
                :placeholder="'请输入' + field.label"
                :disabled="field.disable"
                ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="push-sap-button">
        <el-button type="default" @click="prevStep">上一步</el-button>
        <el-button type="primary" @click="pushSapSubmit" :loading="pushSapDialogData.loading.submitLoading">确定</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getSapSyncOptions,
  getInvoiceTypeOptions,
  searchSapList,
  pushSapNextStep,
  remoteContact,
  pushSapSubmit,
  getExpressCompanyOptions,
  pushSapTaxConfirm,
  getCustomerInfo
} from '@/api/accountStatement.js'
import { pushSapDialogData, secondStepFields, debounce } from './pushSAPConstants'
import { fixedDecimalPlace } from '@/filters/index.js'
import { RemoteCustomer } from '@kun-ui/remote-customer'
const fixedDecimalPlace2 = fixedDecimalPlace.bind(null, 2)
const contactHeader = {
  contactName: '联系人',
  contact: '联系人编号',
  contactPhone: '联系人电话',
  address: '联系人地址'
}
export default {
  name: 'pushSapComponent',
  props: {
    pushSapVisible: Boolean,
    id: Number,
    detailId: Number,
    customerNumber: String

  },
  components: {
    RemoteCustomer
  },
  data () {
    return {
      pushSapDialogData,
      debounceRemoteOrderSearch: null,
      debounceRemoteTicketSearch: null,
      noteReplacement: false,
      customerInfo: null
    }
  },
  created () {
    this.debounceRemoteOrderSearch = debounce(this.remoteOrderSearch, 800)
    this.debounceRemoteTicketSearch = debounce(this.remoteTicketSearch, 800)
  },
  mounted () {
    // console.log(this.pushSapDialogData.secondStepFields.invoiceTypeOptions)
    this.resetPushSapData()
    getSapSyncOptions().then(data => {
      this.pushSapDialogData.sapSyncOptions = data
    })
    getInvoiceTypeOptions().then(data => {
      this.pushSapDialogData.secondStepFields.invoiceTypeOptions = data
    })
    getExpressCompanyOptions().then(data => {
      this.pushSapDialogData.secondStepFields.expressCompanyOptions = data
    })
    console.log('Push Sap Component mounted!')
  },
  computed: {
    disableNextStepStatus () {
      let ret = false
      if (this.pushSapDialogData.sapSelections.length === 0) ret = true
      if (this.pushSapDialogData.loading.searchLoading) ret = true
      return ret
    },
    cancelStatus () {
      return this.pushSapDialogData.loading.searchLoading
    },
    contactSearchId () {
      return this.pushSapDialogData.sapSelections.length ? this.pushSapDialogData.sapSelections[0].id : ''
    }
  },
  methods: {
    remoteTicketSearch (key, force) {
      if (key.trim() !== '' || force === true) {
        this.pushSapDialogData.loading.ticketLoading = true
        remoteContact(key, this.contactSearchId)
          .then(res => {
            if (res && res.status === 200 && res.datas && res.datas.length) {
              this.pushSapDialogData.secondStepFields.ticketContact = [contactHeader, ...res.datas]
            } else {
              this.pushSapDialogData.secondStepFields.ticketContact = []
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '查询失败！')
            this.pushSapDialogData.secondStepFields.ticketContact = []
          })
          .finally(() => {
            this.pushSapDialogData.loading.ticketLoading = false
          })
      }
    },
    remoteOrderSearch (key, force) {
      if (key.trim() !== '' || force === true) {
        this.pushSapDialogData.loading.orderLoading = true
        remoteContact(key, this.contactSearchId)
          .then(res => {
            if (res && res.status === 200 && res.datas && res.datas.length) {
              this.pushSapDialogData.secondStepFields.orderContact = [contactHeader, ...res.datas]
            } else {
              this.pushSapDialogData.secondStepFields.orderContact = []
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '查询失败！')
            this.pushSapDialogData.secondStepFields.orderContact = []
          })
          .finally(() => {
            this.pushSapDialogData.loading.orderLoading = false
          })
      }
    },
    checkboxChange1(val) {
      let tipStatus = this.pushSapDialogData.tipStatus
      if (!Array.isArray(val) || val.some(v => v.toUpperCase === undefined)) return
      val.forEach(val => {
        let status = 'multi' + val.toUpperCase()[0] + val.slice(1)
        if (tipStatus[status] !== '0') {
          this.pushSapDialogData.tipStatus[status] = '0'
        }
      })
      try {
        const reg = /\s+/
        // let _financialNote = this._financialNote
        let financialNote = this.pushSapDialogData.secondStepFields.financialNote
        let _customerRefNum = this._customerRefNum
        if (~val.indexOf('includeCustomerRefNum')) {
          if (_customerRefNum) {
            if (this.pushSapDialogData.secondStepFields.financialNote) {
              this.pushSapDialogData.secondStepFields.financialNote =
              this.pushSapDialogData.secondStepFields.financialNote + ' ' +
              _customerRefNum
            } else {
              this.pushSapDialogData.secondStepFields.financialNote = _customerRefNum
            }
          }
        } else {
          if (financialNote === _customerRefNum) {
            this.pushSapDialogData.secondStepFields.financialNote = ''
          } else if (reg.test(financialNote)) {
            this.pushSapDialogData.secondStepFields.financialNote = financialNote.split(/\s+/)[0]
          }
        }
      } catch (err) { console.log(err) }
    },
    checkboxChange (val) {
      if (!val.length) return
      let tipStatus = this.pushSapDialogData.tipStatus
      if (!Array.isArray(val) || val.some(v => v.toUpperCase === undefined)) return
      val.forEach(val => {
        let status = 'multi' + val.toUpperCase()[0] + val.slice(1)
        if (tipStatus[status] !== '0') {
          this.pushSapDialogData.tipStatus[status] = '0'
        }
      })
    },
    fixedDecimalPlace2,
    handleInputChange(val, maxlength, field) {
      if (val.length > maxlength) {
        this.$message.info(`最多只能输入${maxlength}个字符`);
        this.$set(this.pushSapDialogData.secondStepFields, field, val.slice(0, maxlength));
      }
    },
    ticketContactNameChange (contact) {
      if (!contact) {
        this.pushSapDialogData.secondStepFields.ticketContactPhone = ''
        this.pushSapDialogData.secondStepFields.ticketAddress = ''
        this.pushSapDialogData.secondStepFields.ticketAddressId = ''
        this.pushSapDialogData.secondStepFields.ticketContactName = ''
        this.pushSapDialogData.secondStepFields.ticketContactId = ''
        return
      }
      if (this.pushSapDialogData.tipStatus.multiTicketContact !== '0') {
        this.pushSapDialogData.tipStatus.multiTicketContact = '0'
      }
      const ticketSelected = this.pushSapDialogData.secondStepFields.ticketContact.filter(ti => ti.contact === contact)
      if (ticketSelected && ticketSelected[0]) {
        this.pushSapDialogData.secondStepFields.ticketContactPhone = ticketSelected[0].contactPhone
        this.pushSapDialogData.secondStepFields.ticketAddress = ticketSelected[0].address
        this.pushSapDialogData.secondStepFields.ticketAddressId = ticketSelected[0].addressId
        this.pushSapDialogData.secondStepFields.ticketContactName = ticketSelected[0].contactName
        this.pushSapDialogData.secondStepFields.ticketContactId = ticketSelected[0].contact
      }
    },
    orderContactNameChange (contact) {
      if (!contact) {
        this.pushSapDialogData.secondStepFields.orderContactPhone = ''
        this.pushSapDialogData.secondStepFields.orderContactName = ''
        this.pushSapDialogData.secondStepFields.orderContactId = ''
        return
      }
      if (this.pushSapDialogData.tipStatus.multiOrderContact !== '0') {
        this.pushSapDialogData.tipStatus.multiOrderContact = '0'
      }
      const orderSelected = this.pushSapDialogData.secondStepFields.orderContact.filter(ti => ti.contact === contact)
      if (orderSelected && orderSelected[0]) {
        this.pushSapDialogData.secondStepFields.orderContactPhone = orderSelected[0].contactPhone
        this.pushSapDialogData.secondStepFields.orderContactName = orderSelected[0].contactName
        this.pushSapDialogData.secondStepFields.orderContactId = orderSelected[0].contact
      }
    },
    selectChangeEvent({ checked, records }) {
      this.pushSapDialogData.sapSelections = records
      this.calculateMoney()
    },
    handleSapSelectionChange (rows) {
      this.pushSapDialogData.sapSelections = rows
      this.calculateMoney()
    },
    getSelectionOfProp (prop) {
      return this.pushSapDialogData.sapSelections.map(sel => sel[prop]).reduce((x, y) => Number(x) + Number(y), 0)
    },
    getMultiSelOfProp (prop1, prop2) {
      return this.pushSapDialogData.sapSelections.map(sel => {
        console.log(Number(sel[prop1]) * Number(sel[prop2]))
        return Number(sel[prop1]) * Number(sel[prop2])
      }).reduce((x, y) => Number(x) + Number(y), 0) || 0
    },
    calculateMoney () {
      let checkAmountTax = this.getSelectionOfProp('checkAmountTax')
      let checkAmount = this.getSelectionOfProp('checkAmount')
      let profitLossAmountTax = this.getSelectionOfProp('profitLossAmountTax')
      let verificationAmount = this.getSelectionOfProp('verificationAmount')
      this.pushSapDialogData.moneySummary = {
        checkAmountTax,
        checkAmount,
        profitLossAmountTax,
        verificationAmount
      }
    },
    trimParam (obj) {
      for (let o in obj) {
        try {
          obj[o] = obj[o].trim()
        } catch (err) {}
      }
    },
    searchPushSap () {
      const searchParams = {
        id: this.id,
        basisNo: this.pushSapDialogData.basisNo,
        soNo: this.pushSapDialogData.soNo,
        sapSyncStatus: this.pushSapDialogData.sapSyncStatus,
        productSku: this.pushSapDialogData.productSku,
        productPicker: this.pushSapDialogData.productPicker,
        requirementsDept: this.pushSapDialogData.requirementsDept,
        ticketContactName: this.pushSapDialogData.ticketContactName,
        shippingContactName: this.pushSapDialogData.shippingContactName,
        orderContactName: this.pushSapDialogData.orderContactName,
        refinedOil: this.pushSapDialogData.refinedOil,
        conditionType: this.pushSapDialogData.conditionType,
        invoiceReceiver: this.pushSapDialogData.invoiceReceiver
      }
      this.trimParam(searchParams)
      const soNoList = searchParams.soNo.split(/\s+|,|，/).filter(e => e)
      // if (soNoList.length > 5000) {
      //   return this.$message.error('销售订单号最多支持5000个单号！')
      // }
      const basisNoList = searchParams.basisNo.split(/\s+|,|，/).filter(e => e)
      // if (basisNoList.length > 5000) {
      //   return this.$message.error('交货单号最多支持5000个单号！')
      // }
      const productSkuList = searchParams.productSku.split(/\s+|,|，/).filter(e => e)
      if (productSkuList.length > 500) {
        return this.$message.error('产品编号最多支持500个！')
      }
      this.pushSapDialogData.loading.searchLoading = true
      this.pushSapDialogData.loading.tableLoading = true
      searchParams.soNo = soNoList.join(',')
      searchParams.basisNo = basisNoList.join(',')
      searchParams.productSku = productSkuList.join(',')
      searchSapList(searchParams)
        .then(res => {
          if (res && res.status === 200 && res.datas) {
            this.pushSapDialogData.list = res.datas
            this.pushSapDialogData.list = this.pushSapDialogData.list.map(li => {
              return {
                ...li,
                checkAmount: this.fixedDecimalPlace2(li.checkAmount),
                checkAmountTax: this.fixedDecimalPlace2(li.checkAmountTax)
              }
            })
          } else {
            this.$message.error('查询失败！')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '查询失败！')
        })
        .finally(() => {
          this.pushSapDialogData.loading.searchLoading = false
          this.pushSapDialogData.loading.tableLoading = false
        })
    },
    resetSearchPushSap () {
      this.$refs.pushSapDialogStep1Form.resetFields()
      this.$refs.pushSapDialogStep2Form.resetFields()
    },
    prevStep () {
      this.pushSapDialogData.step = 0
      this.pushSapDialogData.checkbox = []
      this.pushSapDialogData.checkbox1 = []
      this.pushSapDialogData.secondStepFields = {
        ...secondStepFields,
        invoiceTypeOptions: this.pushSapDialogData.secondStepFields.invoiceTypeOptions,
        expressCompanyOptions: this.pushSapDialogData.secondStepFields.expressCompanyOptions
      }
      // console.log(this.pushSapDialogData.secondStepFields.invoiceTypeOptions)
      this.pushSapDialogData.tipStatus = {
        invoiceType: '',
        multiBillingRobot: '',
        multiInvoicingByMail: '',
        multiMergeBilling: '',
        multiReturnOffset: '',
        multiIfDocMailed: '',
        multiExpressCompany: '',
        multiShowDiscount: '',
        multiTicketContact: '',
        multiOrderContact: ''
      }
    },
    async nextSapStep () {
      this.pushSapDialogData.loading.dialogLoading = true
      try {
        let checkResult = await pushSapNextStep(this.pushSapDialogData.sapSelections.map(s => s.id).join(','))
        if (checkResult && checkResult.status === 200 && checkResult.datas) {
          this.pushSapDialogData.secondStepFields = {
            ...this.pushSapDialogData.secondStepFields,
            ...checkResult.datas,
            orderContact: [],
            ticketContact: []
          }
          // 去除原来逻辑
          this._customerRefNum = this.pushSapDialogData.secondStepFields.customerRefNum
          this._financialNote = this.pushSapDialogData.secondStepFields.financialNote
          if (this.pushSapDialogData.secondStepFields.customerRefNum) {
            if (this.pushSapDialogData.secondStepFields.financialNote) {
              this.pushSapDialogData.secondStepFields.financialNote =
              this.pushSapDialogData.secondStepFields.financialNote + ' ' +
              this.pushSapDialogData.secondStepFields.customerRefNum
            } else {
              this.noteReplacement = true
              this.pushSapDialogData.secondStepFields.financialNote =
              this.pushSapDialogData.secondStepFields.customerRefNum
            }
          }
          [
            'invoicingByMail',
            'ifDocMailed',
            'returnOffset',
            'mergeBilling',
            'billingRobot',
            'showDiscount'
          ].forEach(item => {
            if (checkResult.datas[item] > 0) {
              this.pushSapDialogData.checkbox.push(item)
            }
          })
          this.pushSapDialogData.checkbox1.push('includeCustomerRefNum')
          this.pushSapDialogData.tipStatus = {
            invoiceType: checkResult.datas.invoiceType,
            multiBillingRobot: checkResult.datas.multiBillingRobot,
            multiInvoicingByMail: checkResult.datas.multiInvoicingByMail,
            multiExpressCompany: (checkResult.datas.expressCompanyName || '').split(',').length > 1 ? '1' : '0',
            multiMergeBilling: checkResult.datas.multiMergeBilling,
            multiReturnOffset: checkResult.datas.multiReturnOffset,
            multiShowDiscount: checkResult.datas.multiShowDiscount,
            multiIfDocMailed: checkResult.datas.multiIfDocMailed
          }
          let {
            defaultOrderContact,
            defaultTicketContact
          } = this.pushSapDialogData.secondStepFields
          this.pushSapDialogData.tipStatus.multiOrderContact = '1'
          if (defaultOrderContact && defaultOrderContact.contact) {
            this.pushSapDialogData.secondStepFields.orderContactName = defaultOrderContact.contactName
            this.pushSapDialogData.secondStepFields.orderContactPhone = defaultOrderContact.contactPhone
            this.pushSapDialogData.secondStepFields.orderContactId = defaultOrderContact.contact
            this.pushSapDialogData.tipStatus.multiOrderContact = '0'
          }
          this.pushSapDialogData.tipStatus.multiTicketContact = '1'
          if (defaultTicketContact && defaultTicketContact.contact) {
            this.pushSapDialogData.secondStepFields.ticketContactName = defaultTicketContact.contactName
            this.pushSapDialogData.secondStepFields.ticketContactPhone = defaultTicketContact.contactPhone
            this.pushSapDialogData.secondStepFields.ticketAddress = defaultTicketContact.address
            this.pushSapDialogData.secondStepFields.ticketAddressId = defaultTicketContact.addressId
            this.pushSapDialogData.secondStepFields.ticketContactId = defaultTicketContact.contact
            this.pushSapDialogData.tipStatus.multiTicketContact = '0'
          }
          this.pushSapDialogData.secondStepFields.sfType = this.pushSapDialogData.secondStepFields.sfType || '02'
          this.pushSapDialogData.step += 1
          this.remoteTicketSearch('', true)
          this.remoteOrderSearch('', true)
          getCustomerInfo(this.pushSapDialogData.sapSelections[0].invoiceReceiver).then(data => {
            this.customerInfo = data.result
          }).catch(() => {
            this.customerInfo = null
          })
        } else {
          this.$message.error(checkResult.message || checkResult.msg || '核验失败！')
          this.customerInfo = null
        }
      } catch (err) {
        this.$message.error(err.message || err.msg || '核验失败！')
        this.customerInfo = null
      } finally {
        this.pushSapDialogData.loading.dialogLoading = false
      }
    },
    cancelPushSap () {
      this.resetSearchPushSap()
      this.resetPushSapData()
      this.$refs.pushSapDialogTable.setAllCheckboxRow(false)
      this.$emit('close-push-sap-dialog', true)
    },
    validateSubmit () {
      let ret = true
      const validList = [
        { prop: 'invoiceType', label: '开票类型' },
        { prop: 'ticketContactName', label: '收票联系人' },
        { prop: 'ticketContactPhone', label: '收票联系人电话' },
        { prop: 'ticketAddress', label: '收票地址' },
        { prop: 'orderContactName', label: '订单联系人' },
        { prop: 'orderContactPhone', label: '订单联系人电话' }
      ]
      if (this.pushSapDialogData.secondStepFields &&
          this.pushSapDialogData.secondStepFields.dnTypes &&
          this.pushSapDialogData.secondStepFields.dnTypes.indexOf('LR') !== -1) {
        if (!this.pushSapDialogData.secondStepFields.originalInvoiceDraftNum &&
          this.pushSapDialogData.sapSelections.some(item => item.soType === 'ZRE1' || item.soType === 'Z011')) {
          this.$message.error('原发票草稿号不能为空！')
          return false
        }
      }
      for (let item of validList) {
        if (!this.pushSapDialogData.secondStepFields[item.prop]) {
          this.$message.error(`${item.label}不能为空！`)
          return false
        }
      }
      if (
        this.pushSapDialogData.secondStepFields.shippingInfo &&
        this.pushSapDialogData.secondStepFields.shippingInfo.length > 30) {
        this.$message.error('寄票备注最多30个字符！')
        return false
      }
      if (
        this.pushSapDialogData.secondStepFields.originalInvoiceDraftNum &&
        this.pushSapDialogData.secondStepFields.originalInvoiceDraftNum.length > 50) {
        this.$message.error('原发票草稿号最多50个字符！')
        return false
      }
      if (
        this.pushSapDialogData.secondStepFields.espRefVoucherNum &&
        this.pushSapDialogData.secondStepFields.espRefVoucherNum.length > 16) {
        this.$message.error('ESP参考凭证号最多16个字符！')
        return false
      }
      // if (
      //   (this.pushSapDialogData.secondStepFields.financialNote || '').length > 100) {
      //   this.$message.error('发票备注总字数不能超过100个字符！')
      //   return false
      // }
      return ret
    },
    async pushSapSubmit () {
      if (!this.validateSubmit()) return
      this.pushSapDialogData.loading.submitLoading = true
      this.pushSapDialogData.loading.dialogLoading = true
      let opts = this.pushSapDialogData.checkboxOptions.concat(this.pushSapDialogData.checkboxOptions1).map(opt => opt.value)
      let checkbox = this.pushSapDialogData.checkbox.concat(this.pushSapDialogData.checkbox1)
      opts.forEach(item => {
        if (~checkbox.indexOf(item)) {
          this.pushSapDialogData.secondStepFields[item] = 'X'
        } else {
          this.pushSapDialogData.secondStepFields[item] = null
        }
      })
      const submitParams = {
        ...this.pushSapDialogData.secondStepFields,
        ids: this.pushSapDialogData.sapSelections.map(s => s.id).join(',')
      }
      Object.keys(submitParams).forEach(key => {
        if (/multi/gi.test(key)) {
          delete submitParams[key]
        }
      })
      delete submitParams['invoiceTypeOptions']
      delete submitParams['expressCompanyOptions']
      delete submitParams['invoiceTypeName']
      delete submitParams['orderContact']
      delete submitParams['ticketContact']
      delete submitParams['dnTypes']
      delete submitParams['defaultTicketContact']
      delete submitParams['defaultOrderContact']
      delete submitParams['customerRefNum']

      if (submitParams['ticketContactId']) {
        submitParams['ticketContact'] = submitParams['ticketContactId'];
        delete submitParams['ticketContactId']
      }
      if (submitParams['orderContactId']) {
        submitParams['orderContact'] = submitParams['orderContactId'];
        delete submitParams['orderContactId']
      }
      submitParams['financialNote'] = submitParams['financialNote'].substr(0, 100)
      if (submitParams['expressCompany'] !== '2') {
        submitParams['sfType'] = ''
      }
      console.log(submitParams)
      let sapTax = await pushSapTaxConfirm(submitParams)
      if (sapTax.status === 200) {
        this.pushSapConfirm(submitParams)
      } else {
        this.$confirm(sapTax.message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.pushSapConfirm(submitParams)
        }).catch(() => {
          this.cancelPushSap()
          this.pushSapDialogData.loading.submitLoading = false
          this.pushSapDialogData.loading.dialogLoading = false
        })
      }
    },
    async pushSapConfirm (submitParams) {
      try {
        let submitResult = await pushSapSubmit(submitParams)
        if (submitResult.status === 200) {
          this.$message.success('提交成功！')
          setTimeout(() => {
            this.cancelPushSap()
            this.prevStep()
            this.$emit('refresh-statement-detail', true)
          }, 820)
        } else {
          this.$message.error(submitResult.msg || submitResult.message || '提交失败！')
        }
      } catch (err) {
        this.$message.error(err.msg || err.message || '提交失败！')
      } finally {
        setTimeout(() => {
          this.pushSapDialogData.loading.submitLoading = false
          this.pushSapDialogData.loading.dialogLoading = false
        }, 100)
      }
    },
    resetPushSapData () {
      const sapSyncOptions = this.pushSapDialogData.sapSyncOptions
      const invoiceTypeOptions = this.pushSapDialogData.secondStepFields.invoiceTypeOptions
      const expressCompanyOptions = this.pushSapDialogData.secondStepFields.expressCompanyOptions
      this.pushSapDialogData = {
        ...pushSapDialogData,
        secondStepFields: {
          invoiceType: '',
          shippingInfo: '',
          financialNote: '',
          customerRefNum: '',
          originalInvoiceDraftNum: '',
          espRefVoucherNum: '',
          ticketContactName: '',
          ticketContactPhone: '',
          ticketAddress: '',
          ticketAddressId: '',
          ticketContactId: '',
          orderContactName: '',
          orderContactPhone: '',
          orderContactId: '',
          ticketContact: [],
          orderContact: [],
          invoiceTypeOptions,
          expressCompanyOptions
        },
        tipStatus: {
          invoiceType: '',
          multiBillingRobot: '',
          multiInvoicingByMail: '',
          multiMergeBilling: '',
          multiReturnOffset: '',
          multiShowDiscount: '',
          multiIfDocMailed: '',
          multiExpressCompany: '',
          multiTicketContact: '',
          multiOrderContact: ''
        },
        moneySummary: {
          checkAmountTax: 0,
          checkAmount: 0,
          profitLossAmountTax: 0,
          verificationAmount: 0
        },
        list: [],
        checkbox: [],
        checkbox1: [],
        sapSelections: [],
        step: 0,
        basisNo: '',
        soNo: '',
        sapSyncOptions
      }
    }
  }
}
</script>
<style lang="less" scoped>
.push-sap-container{
  h1{
    text-align: center;
    margin-top: -20px;
    margin-bottom: 20px;
  }
  .statistics{
    margin-bottom: 20px;
    span{
      margin-right: 20px;
    }
  }
  .el-autocomplete,
  .el-input,
  .el-select {
    width: 100%;
  }
  .push-sap-button{
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  .step2-checkbox{
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }
  .diff-icon{
    margin-left: 50px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    i{
      color: orangered;
      margin-right: 10px;
      margin-left: 10px;
    }
  }
}
.ba-row-start{
  display: flex;
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  width: 800px;
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 420px;
    overflow: auto;
  }
}
</style>
