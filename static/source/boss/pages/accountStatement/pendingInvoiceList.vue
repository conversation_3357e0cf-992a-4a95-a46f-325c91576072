<template>
  <div class="app-container pending-invoice-list-container">
    <div class="filter-container">
      <el-form
        label-width="120px"
        label-position="right"
        ref="searchForm"
        :model="searchForm"
      >
        <el-row>
          <el-col :span="16">
            <el-form-item label="订单号" prop="orderNo">
              <el-input
                clearable
                v-model="searchForm.orderNo"
                placeholder="支持外围订单号，销售订单号，客户订单号搜索，用逗号分隔，最大支持500个"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SAP同步状态" prop="sapSyncStatus" v-if="visible">
              <el-select
                v-model="searchForm.sapSyncStatus"
                multiple
                collapse-tags
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in sapSyncOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="对账单名称" prop="customerStatementName">
              <el-input
                v-model.trim="searchForm.customerStatementName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="对账单明细编号" prop="no">
              <el-input
                v-model.trim="searchForm.no"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户编号" prop="customer">
              <RemoteCustomer v-model="searchForm.customer" getValue placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="对账依据编号" prop="basisNo">
              <el-input
                clearable
                v-model="searchForm.basisNo"
                placeholder="同时支持5000个单号，以空格分隔"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户名称" prop="otherCustomer">
              <RemoteCustomer v-model="searchForm.otherCustomer" placeholder="请输入关键词" />
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="产品编码" prop="productSku">
              <el-input
                clearable
                v-model="searchForm.productSku"
                placeholder="产品编号、客户物料号 均支持搜索，同时支持500个单号，以空格分隔"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="领料人" prop="productPicker">
              <el-input
                clearable
                v-model="searchForm.productPicker"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="需求部门" prop="requirementsDept">
              <el-input
                clearable
                v-model="searchForm.requirementsDept"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收票联系人" prop="ticketContactName">
              <el-input
                clearable
                v-model="searchForm.ticketContactName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收货联系人" prop="shippingContactName">
              <el-input
                clearable
                v-model="searchForm.shippingContactName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单联系人" prop="orderContactName">
              <el-input
                clearable
                v-model="searchForm.orderContactName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否含税" prop="conditionType">
              <el-select
                clearable
                v-model="searchForm.conditionType"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in conditionTypeOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否成品油" prop="refinedOil">
              <el-select
                clearable
                v-model="searchForm.refinedOil"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in refinedOilOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="8">
            <el-form-item label="物料组" prop="productGroup">
              <MaterialGroup v-model="searchForm.productGroup" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客服" prop="customerServiceName">
              <el-input
                clearable
                v-model="searchForm.customerServiceName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客服主管" prop="customerServiceSupervisor">
              <el-input
                clearable
                v-model="searchForm.customerServiceSupervisor"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
         <el-col :span="8">
            <el-form-item label="是否重开标示" prop="ifInvoiceRedo">
              <el-select
                clearable
                v-model="searchForm.ifInvoiceRedo"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in ifInvoiceRedoOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" class="align-right">
            <el-button
              type="primary"
              @click="handleFilter"
              :loading="listLoading"
              >查询</el-button
            >
            <el-button type="default" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="pending-invoice-table-container">
      <el-row type="flex" class="result-title" justify="space-between">
        <el-col :span="16">
          <div class="statistics">
            <span
              >对账含税总金额：{{
                fixedDecimalPlace2(moneySummary.checkAmountTax)
              }}</span
            >
            <span
              >对账未税总金额：{{
                fixedDecimalPlace2(moneySummary.checkAmount)
              }}</span
            >
            <span>税额：{{ fixedDecimalPlace2(moneySummary.totalTax) }}</span>
          </div>
        </el-col>
        <el-col :span="8" style="text-align: right">
          <el-button @click="showColumnSet">自定义列</el-button>
          <el-button type="warning" @click="checkAll" :loading="checkAllLoading"
            >全选</el-button
          >
          <el-button type="primary" @click="pushSAP">开票</el-button>
        </el-col>
      </el-row>
      <zkh-table
        ref="basisTable"
        :selectionChange="selectionChange"
        :selectable="true"
        :loading="listLoading"
        :data="pendingList"
        :headerDragend="headerDragend"
        :columnsConfig="columnsConfigDeepCopy"
        :defaultColumnsConfig="defaultColumnsConfig"
        :visible.sync="columnsSetVisible"
        :columns="columns"
        :height="500"
        @columnSet="columnSet"
        :sortChange="handleSortChange"
      >
        <template v-slot:leftCols>
          <el-table-column
            label="对账单明细编号"
            min-width="120px"
            align="center"
            prop="no"
          />
          <el-table-column
            label="对账单名称"
            min-width="150px"
            align="center"
            prop="customerStatementName"
          >
            <template slot-scope="{ row }">
              <router-link
                class="el-link el-link--primary"
                :to="'/account-statement-center/statement/detail/' + row.customerStatementNo"
              >
                {{ row.customerStatementName }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(col, index) in leftColumns"
            :key="index"
            :label="col.label"
            :min-width="col.minWidth"
            align="center"
            :prop="col.prop"
          />
          <el-table-column min-width="140px" align="center" label="对账数量">
            <template slot-scope="{ row }">
              <span>
                {{ row.checkNumber | fixedDecimalPlace3 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            min-width="140px"
            align="center"
            label="对账金额（含税）"
            sortable="custom"
            prop="checkAmountTax"
          >
            <template slot-scope="{ row }">
              <span>
                {{ row.checkAmountTax | fixedDecimalPlace2 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column min-width="120px" align="center" label="税额">
            <template slot-scope="{ row }">
              <span>
                {{ row.tax | fixedDecimalPlace2 }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            min-width="120px"
            align="center"
            label="对账金额（未税）"
          >
            <template slot-scope="{ row }">
              <span>
                {{ row.checkAmount | fixedDecimalPlace2 }}
              </span>
            </template>
          </el-table-column>
        </template>
      </zkh-table>
      <pagination
        v-show="total > 0"
        :total="total"
        align="right"
        :page.sync="listQueryInfo.current"
        :limit.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList"
      />
    </div>
    <el-dialog
      width="1020px"
      class="push-sap-dialog"
      title="推送SAP发票信息"
      :visible.sync="pushSapVisible"
      :show-close="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <PushSAPForm
        :detailId="pushSapDetailedId"
        :checkResult="checkResult"
        :onClose="closePushSapDialog"
        :onSuccess="handlePushSuccess"
      />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination';
import MaterialGroup from '@/components/SearchFields/materialGroup';
import {
  createdConfigByColumns,
  formatByConfig,
  checkColumnsConfig
} from '@/components/ZkhTable/columnConfigTransformation.js';
import PushSAPForm from './components/pushSAPForm';
import getColumns, { leftColumns } from './pendingInvoiceColumns.js';
import { fixedDecimalPlace } from '@/filters/index.js';
import {
  pushSapNextStep,
  getSapSyncOptions,
  getPendingInvoiceList,
  getCustomerListByPartName,
  getPendingListIdsBySearchFields,
  fetchPendingInvoiceConfigFromServer,
  savePendingInvoiceConfig2Server
} from '@/api/accountStatement.js';
import { get } from 'lodash';
import { RemoteCustomer } from '@kun-ui/remote-customer'

const fixedDecimalPlace2 = fixedDecimalPlace.bind(null, 2);
const fixedDecimalPlace3 = fixedDecimalPlace.bind(null, 3);
export default {
  name: 'PendingInvoiceList',
  components: {
    Pagination,
    MaterialGroup,
    PushSAPForm,
    RemoteCustomer
  },
  data() {
    return {
      checkedAllFlag: false,
      checkedAllIds: '',
      checkedIds: '',
      checkAllLoading: false,
      columnsConfigDeepCopy: [],
      listLoading: false,
      pendingList: [], // 对账单明细
      columnsConfig: [],
      defaultColumnsConfig: [],
      columnsSetVisible: false,
      leftColumns: leftColumns,
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      pushSapVisible: false,
      total: 0,
      columns: [],
      sapSyncOptions: [],
      refinedOilOptions: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      conditionTypeOptions: [
        { label: '含税', value: 'Z005' },
        { label: '未税', value: 'Z010' }
      ],
      ifInvoiceRedoOptions: [
        { label: '是', value: 'X' },
        { label: '否', value: 'Z' }
      ],
      customerNoLoading: false,
      customerNoOptions: [],
      searchForm: {
        customerStatementName: '',
        orderNo: '',
        no: '',
        otherCustomer: '',
        customer: '',
        productGroup: '',
        sapSyncStatus: ['1', '3', '5', '7'],
        basisNo: '',
        productSku: '',
        productPicker: '',
        requirementsDept: '',
        ticketContactName: '',
        shippingContactName: '',
        orderContactName: '',
        conditionType: '',
        refinedOil: '',
        idOrder: 1,
        fkstk: '',
        checkAmountTaxOrder: '',
        customerServiceName: '',
        customerServiceSupervisor: '',
        ifInvoiceRedo: ''
      },
      moneySummary: {
        checkAmountTax: 0,
        checkAmount: 0,
        totalTax: 0,
        profitLossAmountTax: 0,
        verificationAmount: 0
      },
      pushSapDetailedId: null,
      rowSelections: [],
      checkResult: null,
      tableServerConfig: null
    };
  },
  props: {
    condition: {
      type: Object,
      default() {
        return {};
      }
    },
    visible: {
      type: Boolean,
      default: true
    },
    diableInitialFetchList: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    condition(newVal, oldVal) {
      this.searchForm = Object.assign(this.searchForm, newVal)
      this.handleFilter()
    }
  },
  async created() {
    const tableConfigResponse = await fetchPendingInvoiceConfigFromServer();
    if (tableConfigResponse) {
      this.tableServerConfig = tableConfigResponse;
    }

    this.initColumns();
  },
  mounted() {
    getSapSyncOptions().then((data) => {
      this.sapSyncOptions = data;
    });
    if (!this.diableInitialFetchList) {
      this.getList();
    }
  },
  filters: {
    fixedDecimalPlace2,
    fixedDecimalPlace3
  },
  methods: {
    fixedDecimalPlace2,
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      if (this.condition) {
        this.searchForm = { ...this.searchForm, ...this.condition }
      }
      this.handleFilter();
    },
    selectionChange(rows) {
      this.checkedAllFlag = false;
      this.checkedAllIds = '';
      this.rowSelections = rows;
      this.calculateMoney();
      this.pushSapDetailedId = get(rows, '0.id');
    },
    getSelectionOfProp(prop) {
      return this.rowSelections
        .map((sel) => sel[prop])
        .reduce((x, y) => Number(x) + Number(y), 0);
    },
    calculateMoney() {
      const checkAmountTax = this.getSelectionOfProp('checkAmountTax');
      const checkAmount = this.getSelectionOfProp('checkAmount');
      const profitLossAmountTax = this.getSelectionOfProp(
        'profitLossAmountTax'
      );
      const verificationAmount = this.getSelectionOfProp('verificationAmount');
      const totalTax = this.getSelectionOfProp('tax');
      this.moneySummary = {
        checkAmountTax,
        checkAmount,
        profitLossAmountTax,
        verificationAmount,
        totalTax
      };
    },
    setDefaultCache(cols) {
      const config = createdConfigByColumns(cols);
      this.columnsConfig = config;
      this.columns = cols;
    },
    openPushSapDialog() {
      this.pushSapVisible = true;
    },
    closePushSapDialog() {
      this.checkResult = null;
      this.pushSapVisible = false;
    },
    handlePushSuccess() {
      this.$refs.basisTable.clearSelection();
      this.handleFilter();
      this.closePushSapDialog();
    },
    pushSAP() {
      if (this.checkedAllFlag) {
        if (this.checkAllLoading) return this.$message.error('请先完成全选！');
        this.handlePreInvoicing(this.checkedAllIds, true);
        return;
      }
      const tb = this.$refs.basisTable;
      let selectRows = tb.getSelections();
      if (selectRows.length > 0) {
        this.checkedIds = selectRows.map((item) => item.id).join(',');
        this.handlePreInvoicing(this.checkedIds);
      } else {
        this.$message.error('请选择要开票的行');
      }
    },
    async handlePreInvoicing(ids, all) {
      try {
        const confirmResult = await this.$confirm(
          `${all ? '已全选，' : ''}是否确认${all ? '全部' : ''}开票`,
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
            center: true
          }
        );
        if (confirmResult) {
          try {
            const checkResult = await pushSapNextStep(ids);
            if (
              checkResult &&
              checkResult.status === 200 &&
              checkResult.datas
            ) {
              this.checkResult = {
                ...checkResult,
                datas: { ...checkResult.datas, ids }
              };
              this.openPushSapDialog();
            } else {
              this.$message.error(
                checkResult.message || checkResult.msg || '核验失败！'
              );
            }
          } catch (err) {
            this.$message.error(err.message || err.msg || '核验失败！');
          }
        }
      } catch {}
    },
    // 远程查找客户
    remoteCustomerMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.customerNoLoading = true;
        getCustomerListByPartName({
          customerName: key
        }).then((res) => {
          this.customerNoLoading = false;
          if (res.status === 200) {
            if (res.datas && res.datas.length > 0) {
              this.customerNoOptions = res.datas.map((item) => {
                return {
                  value: item.customerNumber,
                  label: item.customerName
                };
              });
            } else {
              this.customerNoOptions = [];
            }
          } else {
            this.customerNoOptions = [];
          }
        });
      } else {
        this.customerNoOptions = [];
      }
    },
    formatParams(param) {
      if (param.orderNo && param.orderNo.split) {
        let soNoList = param.orderNo.split(/\s+|,|，/).filter((e) => e);
        param.orderNo = soNoList.join(',');
      }
      if (param.basisNo && param.basisNo.split) {
        let basisNoList = param.basisNo.split(/\s+|,|，/).filter((e) => e);
        param.basisNo = basisNoList.join(',');
      }
      if (param.productSku && param.productSku.split) {
        let productSkuList = param.productSku
          .split(/\s+|,|，/)
          .filter((e) => e);
        if (productSkuList.length > 500) {
          this.$message.error('产品编号最多支持500个！');
          return false;
        }
        param.productSku = productSkuList.join(',');
      }
      this.transArrayToJoin(param, 'sapSyncStatus');
      return true;
    },
    transArrayToJoin(obj, prop) {
      if (Array.isArray(obj[prop])) {
        obj[prop] = obj[prop].filter((e) => e).join(',');
      }
    },
    showColumnSet() {
      this.columnsConfigDeepCopy = JSON.parse(
        JSON.stringify(this.columnsConfig)
      );
      this.columnsSetVisible = true;
    },
    async checkAll() {
      const tb = this.$refs.basisTable;
      this.pendingList.forEach((row) => {
        tb.toggleRowSelection(row, true);
      });
      this.checkedAllFlag = true;
      let param = { ...this.searchForm };
      if (this.prefixCustomer) {
        param.customerCode = this.prefixCustomer;
      }
      if (this.prefixCurrency) {
        param.currency = this.prefixCurrency;
      }
      if (this.prefixGroupNo !== undefined) {
        param.parentId = this.prefixGroupNo;
      }
      if (this.prefixStatus) {
        param.status = this.prefixStatus;
      }
      if (this.prefixStatementNo) {
        param.statementNo = this.prefixStatementNo;
      }
      this.formatParams(param);
      this.checkAllLoading = true;
      let response = await getPendingListIdsBySearchFields(param);
      this.checkAllLoading = false;
      console.log(response);
      if (response.status !== 200) {
        this.$message.error(response.error || '全选失败！');
        return;
      }
      this.$message.success('全选成功！');
      this.$set(
        this.moneySummary,
        'checkAmountTax',
        response.datas.checkTotalAmountTax
      );
      this.$set(
        this.moneySummary,
        'checkAmount',
        response.datas.checkTotalAmount
      );
      this.$set(this.moneySummary, 'totalTax', response.datas.taxTotalAmount);
      this.checkedAllIds = response.datas.ids.join(',');
    },
    getList() {
      let param = {
        ...this.searchForm
      };
      this.formatParams(param);
      const query = {
        page: this.listQueryInfo.current,
        size: this.listQueryInfo.pageSize
      };
      this.listLoading = true;
      getPendingInvoiceList(query, param)
        .then((res) => {
          this.listLoading = false;
          if (res.status === 200) {
            if (res.datas) {
              const data = res.datas;
              this.total = data.total;
              this.pendingList = data.list.map((li) => {
                return {
                  ...li,
                  checkAmount: this.fixedDecimalPlace2(li.checkAmount),
                  checkAmountTax: this.fixedDecimalPlace2(li.checkAmountTax),
                  tax: this.fixedDecimalPlace2(li.tax)
                };
              });
            }
          } else {
            this.$message.error(res.message);
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    initColumns() {
      try {
        const defaultColumns = getColumns();
        this.defaultColumnsConfig = createdConfigByColumns(defaultColumns);
        this.setDefaultCache(getColumns());
        if (this.tableServerConfig) {
          const checkResult = checkColumnsConfig(
            this.defaultColumnsConfig,
            this.tableServerConfig
          );
          let columnsConfig = checkResult.config;
          if (checkResult.changed) {
            console.log('对比本地不一致, 上传table配置至服务器！');
            savePendingInvoiceConfig2Server(columnsConfig);
          }
          console.log('从服务端获取table配置并应用！');
          this.columnsConfig = columnsConfig;
          this.columns = formatByConfig(this.columnsConfig, getColumns());
        } else {
          console.log('上传table配置至服务器！');
          savePendingInvoiceConfig2Server(this.defaultColumnsConfig);
        }
      } catch (error) {
        this.setDefaultCache(getColumns());
      }
    },
    headerDragend(newWidth, oldWidth, column, event) {
      const label = column.label;
      newWidth = Math.trunc(newWidth); // 列宽只能允许整数
      console.log(`修改: ${label} width ${oldWidth} -> ${newWidth}`);
      this.columnsConfig.forEach((config) => {
        if (config.label === label) {
          config.minWidth = newWidth + 'px';
        }
      });
      this.defaultColumnsConfig = JSON.parse(
        JSON.stringify(this.columnsConfig)
      );
      // cachedConfig('backlogOrderColumnsConfig', this.columnsConfig)
      console.log('保存table配置到服务器');
      savePendingInvoiceConfig2Server(this.defaultColumnsConfig).catch(
        (err) => {
          console.log(err);
          this.$message.error(err.msg || err.message || '保存配置失败！');
        }
      );
    },
    columnSet(newConfig) {
      if (newConfig) {
        this.columnsConfig = newConfig;
        this.columns = formatByConfig(this.columnsConfig, getColumns());
        // cachedConfig('backlogOrderColumnsConfig', newConfig)
      }
      this.columnsSetVisible = false;
      console.log('保存列设置');
      console.log(newConfig);
      savePendingInvoiceConfig2Server(newConfig).catch((err) => {
        console.log(err);
        this.$message.error(err.msg || err.message || '保存配置失败！');
      });
    },
    // 排序
    handleSortChange(column, prop, order) {
      // console.log(column, prop, order)
      if (prop === 'checkAmountTax') {
        this.searchForm.idOrder = ''
        this.searchForm.checkAmountTaxOrder = (order === 'ascending' ? 0 : 1)
      }
      this.handleFilter()
    }
  }
};
</script>

<style lang="scss">
.pending-invoice-list-container {
  .filter-container {
    padding-top: 18px;
    padding-right: 10px;
    background-color: #f4f4f4;
  }

  .pending-invoice-table-container {
    .result-title {
      height: 50px;
      line-height: 50px;
    }
    .statistics {
      span {
        margin-right: 20px;
      }
    }
  }
  .el-select {
    width: 100%;
  }
  .align-right {
    text-align: right;
  }
}
</style>
