<template>
  <div class="app-container template-center-container">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="7">
            <el-form-item label="模板编号：" prop="modelCode">
              <el-input
                v-model.trim="searchForm.modelCode"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="模板名称：" prop="modelName">
              <el-input
                v-model.trim="searchForm.modelName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="模板状态：" prop="modelStatus">
              <el-select v-model="searchForm.modelStatus" placeholder="请选择">
                <el-option
                  v-for="item in templateStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <!-- <el-form-item> -->
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="templateLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="模板类型：" prop="modelType">
              <el-select v-model="searchForm.modelType" placeholder="请选择">
                <el-option
                  v-for="item in templateTypeStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="模板优先级：" prop="modelOrder">
              <el-input
                v-model.trim="searchForm.modelOrder"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="template-center-search-result-container">
      <div class="template-center-table-control-container">
        <el-button type="primary" icon="el-icon-plus" @click="addTemplate"
          >新增</el-button
        >
      </div>
      <el-table
        v-loading="templateLoading"
        :data="templateList"
        border
        fit
        highlight-current-row
        height="500"
      >
        <el-table-column label="模板编号" align="center" prop="modelCode" />
        <el-table-column label="模板名称" align="center" prop="modelName" />
        <el-table-column label="模板类型" align="center" prop="modelType">
          <template slot-scope="{ row }">
            {{ row.modelType | getLabel }}
          </template>
        </el-table-column>
        <el-table-column
          label="模板优先级"
          min-width="100px"
          align="center"
          prop="modelOrder"
        />
        <el-table-column label="操作" align="center" class-name="small-padding">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" @click="editTemplate(row)"
              >编辑</el-button
            >
            <el-button type="text" size="mini" @click="effectiveSwitch(row)">{{
              row.modelStatus == 0 ? '失效' : '生效'
            }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout=" sizes, prev, pager, next, jumper"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'

import {
  getModels,
  editModelStatus,
  templateTypeStatusOptions
} from '@/api/templateCenter.js'

const templateStatusOptions = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '生效',
    value: '0'
  },
  {
    label: '失效',
    value: '1'
  }
]
export default {
  name: 'AccountList',
  data () {
    return {
      templateTypeStatusOptions: templateTypeStatusOptions, // 模板类型 下拉选项
      templateStatusOptions: templateStatusOptions, // 模板状态 下拉选项
      templateLoading: false,

      searchForm: {
        modelCode: '',
        modelName: '',
        modelStatus: '',
        modelType: '',
        modelOrder: ''
      }, // 绑定搜索参数

      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      rules: {},
      templateList: [],
      total: 0
    }
  },
  filters: {
    getLabel (val) {
      if (val !== '') {
        const type = templateTypeStatusOptions.find(item => {
          return item.value === val + ''
        })

        return type ? type.label || '' : ''
      } else {
        return ''
      }
    }
  },
  components: {
    Pagination
  },
  created () {
    this.getList()
  },
  methods: {
    handleFilter () {
      this.listQueryInfo.current = 1
      this.total = 0
      this.getList()
    },

    initParam () {
      return { ...this.searchForm }
    },
    getList () {
      let param = this.initParam()
      param.current = this.listQueryInfo.current
      param.pageSize = this.listQueryInfo.pageSize
      this.templateLoading = true
      getModels(param)
        .then(res => {
          this.templateLoading = false
          if (res.code === 200) {
            this.total = res.totalCount
            if (res.data) {
              this.templateList = res.data
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(() => {
          this.templateLoading = false
        })
    },
    editTemplate (row) {
      this.$router.push('/template-center/edit/' + row.modelId)
    },
    addTemplate () {
      this.$router.push('/template-center/add')
    },
    effectiveSwitch (row) {
      const newStatus = row.modelStatus + '' === '0' ? 1 : 0
      const text = newStatus === 1 ? '失效' : '生效'
      const param = {
        modelId: row.modelId,
        modelStatus: newStatus
      }
      editModelStatus(param)
        .then(res => {
          if (res.code === 200) {
            row.modelStatus = newStatus
            this.$message.success('已' + text)
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(e => {
          let res = e.response ? e.response.data : {}
          this.$message.error(res.message || res.msg || res.error || e)
        })
    }
  },
  activated () {
    let edited = sessionStorage.getItem('templateEdit')
    if (edited) {
      this.getList()
      sessionStorage.removeItem('templateEdit')
    }
  }
}
</script>

<style lang="scss" scoped>
.template-center-container {
  .filter-container {
    padding-top: 18px;
    background-color: #f4f4f4;
  }
  .template-center-search-result-container {
    .template-center-table-control-container {
      text-align: right;
      padding: 10px 20px;
    }
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
}
</style>
