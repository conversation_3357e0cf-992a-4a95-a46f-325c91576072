import { fixedDecimalPlace } from '@/filters/index.js'
import { deepClone } from '@/utils/index.js'
import {
  includeTaxList
} from '@/api/accountStatement.js'
let includeTaxOptions = []
includeTaxOptions.push(...includeTaxList)
includeTaxOptions.shift()
const fixedDecimalPlace2 = fixedDecimalPlace.bind(null, 2)
const fixedDecimalPlace6 = fixedDecimalPlace.bind(null, 6)
// const fixedDecimalPlace3 = fixedDecimalPlace.bind(null, 3)
const statusMapEnums = {
  1: '待同步',
  2: '同步SAP成功',
  3: '同步SAP失败',
  4: '产生发票草稿成功',
  5: '产生发票草稿失败',
  6: '财务过账成功',
  7: '财务过账失败'
}
const statusMap = (status) => {
  return statusMapEnums[status]
}

export const leftColumns = [
  { label: '对账单明细编号', prop: 'no', type: 'default', visible: true, minWidth: '120px' },
  // { label: '对账单编号', prop: 'customerStatementNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户', prop: 'customer', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户名称', prop: 'customerName', type: 'default', visible: true, minWidth: '120px' }
  // { label: '对账数量', prop: 'checkNumber', type: 'default', visible: true, minWidth: '120px' },
  // { label: '对账含税金额', prop: 'checkAmountTax', type: 'default', visible: true, minWidth: '120px' },
  // { label: '对账未税金额', prop: 'checkAmount', type: 'default', visible: true, minWidth: '120px' },
  // { label: '盈亏数量', prop: 'profitLossNumber', type: 'default', visible: true, minWidth: '120px' },
  // { label: '盈亏金额', prop: 'customerNo', type: 'default', visible: true, minWidth: '120px' },
  // { label: '备注原因', prop: 'comment', type: 'default', visible: true, minWidth: '120px' }
]
const deliveryStatusOptions = {
  B: '部分交货',
  C: '已交货'
}
const signStatusOptions = {
  0: '未签收',
  1: '已签收'
}
const ifInvoiceRedoOptions = {
  0: '未重开',
  1: '重开'
}
const columns = [
  { label: '对账依据编号', prop: 'basisNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '对账依据行编号', prop: 'basisDetailNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '盈亏数量', columnSlot: 'profitLossNumber', prop: 'profitLossNumber', type: 'custom', visible: true, minWidth: '140px' },
  { label: '盈亏金额', columnSlot: 'profitLossAmount', prop: 'profitLossAmount', type: 'custom', visible: true, minWidth: '120px' },
  { label: '备注原因', columnSlot: 'comment', prop: 'comment', type: 'custom', visible: true, minWidth: '160px' },
  { label: '销售订单描述', prop: 'soDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货类型描述', prop: 'dnDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售订单号', prop: 'soNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售订单行号', prop: 'soDetailNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户是否签收', prop: 'signStatus', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return signStatusOptions[val] } },
  { label: '交货状态', prop: 'deliveryStatus', type: 'enum', visible: true, minWidth: '160px', labelFormat (val) { return deliveryStatusOptions[val] } },
  { label: 'oms交货单号', prop: 'omsDnNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'oms交货单行号', prop: 'omsDnItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'oms订单号', prop: 'omsSoNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'oms订单行号', prop: 'omsSoItemNo', type: 'default', visible: true, minWidth: '120px' },
  // { label: '交货单对应销售订单状态', prop: 'sdStatus', type: 'default', visible: true, minWidth: '160px' },
  { label: '客户订单号', prop: 'customerOrderNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '产品编号', prop: 'productSku', type: 'default', visible: true, minWidth: '120px' },
  { label: '产品描述', prop: 'productDesc', type: 'default', visible: true, minWidth: '240px' },
  { label: '销售单位', prop: 'salesUnit', type: 'default', visible: true, minWidth: '120px' },
  { label: '库存地点描述', prop: 'inventoryLocationDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料号', prop: 'customerProductNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户品名', prop: 'customerProductName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户规格型号', prop: 'customerProductSpecifications', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料数量', prop: 'customerProductNumber', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace6(val) } },
  { label: '客户物料单价', prop: 'customerProductUnitPrice', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '客户物料单位', prop: 'customerProductUnit', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货数量', prop: 'deliveryQty', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货总金额', columnSlot: 'deliveryTotalAmount', prop: 'deliveryTotalAmount', type: 'custom', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },

  { label: '核销数量', prop: 'verificationNumber', type: 'default', visible: true, minWidth: '120px' },
  { label: '核销金额', columnSlot: 'verificationAmount', prop: 'verificationAmount', type: 'custom', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '应收确认中数量', prop: 'confirmingReceivableQuantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '应收确认中金额', prop: 'confirmingReceivableAmount', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '已确认应收数量', prop: 'confirmedReceivableQuantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '已确认应收金额', prop: 'confirmedReceivableAmount', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },

  { label: '税率', prop: 'taxRate', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  // { label: '税额', prop: 'tax', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '含税/未税', prop: 'conditionType', type: 'enum', visible: true, minWidth: '120px', enmus: includeTaxOptions },
  { label: '含税单价', prop: 'unitPriceTax', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '未税单价', prop: 'unitPrice', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '折扣总额', prop: 'discountTotalAmount', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '折扣后单价（未税）', prop: 'discountUnitPrice', type: 'default', visible: true, minWidth: '140px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '折扣后单价（含税）', prop: 'discountUnitPriceTax', type: 'default', visible: true, minWidth: '140px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '折扣后总额', columnSlot: 'discountedTotalAmount', prop: 'discountedTotalAmount', type: 'custom', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '货币', prop: 'currency', type: 'default', visible: true, minWidth: '120px' },
  { label: '汇率', prop: 'exchangeRate', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return fixedDecimalPlace2(val) } },
  { label: '工厂名称', prop: 'factoryName', type: 'default', visible: true, minWidth: '120px' },
  { label: '分销渠道描述', prop: 'distChannelDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售组织', prop: 'salesOrgDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '成品油', prop: 'refinedOil', type: 'default', visible: true, minWidth: '120px' },
  { label: '实际交货日期', prop: 'actDeliveryDate', type: 'default', visible: true, minWidth: '140px' },
  { label: '单据创建日期', prop: 'dnCreateTime', type: 'default', visible: true, minWidth: '140px' },
  { label: '收货联系人', prop: 'shippingContactName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单联系人', prop: 'orderContactName', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票联系人', prop: 'ticketContactName', type: 'default', visible: true, minWidth: '120px' },
  { label: '需求部门', prop: 'requirementsDept', type: 'default', visible: true, minWidth: '120px' },
  { label: '领料人', prop: 'productPicker', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服名称', prop: 'customerServiceName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售员名称', prop: 'salesName', type: 'default', visible: true, minWidth: '120px' },
  { label: '集团销售名称', prop: 'groupSalesName', type: 'default', visible: true, minWidth: '120px' },
  { label: '所属集团编码', prop: 'groupNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '所属集团名称', prop: 'groupName', type: 'default', visible: true, minWidth: '120px' },
  { label: '后补订单', prop: 'backupOrder', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单原因', prop: 'orderReason', type: 'default', visible: true, minWidth: '120px' },
  { label: '凭邮件开票', prop: 'invoicingByMail', type: 'default', visible: true, minWidth: '120px' },
  { label: '发票草稿号', prop: 'invoiceDraftNumber', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票方', prop: 'invoiceReceiver', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票方名称', prop: 'invoiceReceiverName', type: 'default', visible: true, minWidth: '120px' },
  { label: '付款条款', prop: 'payCondition', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SAP同步结果', prop: 'sapSyncRes', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SAP同步状态', prop: 'sapSyncStatus', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return statusMap(val) } },
  { label: '对账单明细重开标示', prop: 'ifInvoiceRedo', type: 'default', visible: true, minWidth: '130px', labelFormat (val) { return ifInvoiceRedoOptions[val] } },
  { label: '对账单明细开票子状态', prop: 'invoiceSubstatus', type: 'default', visible: true, minWidth: '140px', labelFormat (val) { return ifInvoiceRedoOptions[val] } },
  { label: '对账单明细开票子状态描述', prop: 'invoiceSubstatusDesc', type: 'default', visible: true, minWidth: '170px', labelFormat (val) { return ifInvoiceRedoOptions[val] } },
  { label: '父订单号', prop: 'espParentOrderNumber', type: 'default', visible: true, minWidth: '120px' },
  { label: '三方订单号', prop: 'espThirdOrder', type: 'default', visible: true, minWidth: '120px' },
  { label: '成本中心名称', prop: 'espCustomerCostCenterName', type: 'default', visible: true, minWidth: '120px' },
  { label: '用户名称', prop: 'espUserName', type: 'default', visible: true, minWidth: '120px' },
  { label: '是否自动对账', prop: 'autoBilling', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return val === 'X' ? '是' : '否' } },
  { label: 'ID', prop: 'id', type: 'default', visible: false, minWidth: '120px' }
]

const getColumns = function () {
  return deepClone(columns)
}
export default getColumns
