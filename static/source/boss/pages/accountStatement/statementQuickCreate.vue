<template>
  <div class="template-set-container">
    <el-form
      ref="modelForm"
      :rules="rules"
      :model="modelData"
      label-width="150px"
      class="template-set-form"
    >
      <el-form-item label="模板名称：" prop="modelName">
        <div class="single-item-body">
          <el-input maxlength="50" clearable v-model="modelData.modelName" />
        </div>
      </el-form-item>
      <el-form-item label="模板类型：" prop="modelType">
        <div class="single-item-body">
          <el-select
            v-model="modelData.modelType"
            clearable
            placeholder="请选择"
          >
            <el-option
              v-for="item in templateTypeStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="+item.value"
            ></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="模板命中维度：" required>
        <div
          class="el-form el-form--inline"
          v-for="(col, index) in modelData.matchColumnLogics"
          :key="col.key"
        >
          <!-- 字段名 -->
          <el-form-item
            :prop="`matchColumnLogics[${index}].columnName`"
            :rules="{
              required: true,
              message: '请选择字段名称',
              trigger: 'change'
            }"
          >
            <el-select
              :value="col"
              filterable
              placeholder="请选择"
              value-key="columnName"
              @change="
                obj => {
                  columnNameSelect(obj, index)
                }
              "
            >
              <el-option
                v-for="item in fieldList"
                :key="item.columnName"
                :label="item.label"
                :value="item"
              >
                <span style="float: left">{{ item.label }}</span>
                <span
                  style="float: right; color: #8492a6; font-size: 13px; margin-left: 10px;"
                  >{{ item.columnName }}</span
                >
              </el-option>

              <!-- <el-option
                v-for="item in fieldList"  label: item.matchColumnDesc,
              value: item.matchColumnCode
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option> -->
            </el-select>
          </el-form-item>
          <!-- 比较运算符 -->
          <el-form-item prop="operType">
            <el-select v-model="col.operType" clearable placeholder="请选择">
              <el-option
                v-for="item in comparisonOperatorList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <!-- 字段值 -->
          <el-form-item prop="columnData">
            <el-input clearable maxlength="200" v-model="col.columnData" />
          </el-form-item>
          <el-button
            v-if="modelData.matchColumnLogics.length > 1"
            @click="delModelMarchCondition(col, index)"
            >删除</el-button
          >
          <el-button
            v-if="index == modelData.matchColumnLogics.length - 1"
            type="primary"
            @click="addModelMarchCondition"
            >添加</el-button
          >
          <br v-if="index != modelData.matchColumnLogics.length - 1" />
          <!-- 逻辑运算符，最后一个失效 -->
          <el-form-item
            v-if="index != modelData.matchColumnLogics.length - 1"
            prop="logicType"
          >
            <el-select
              style="margin:20px 0;"
              v-model="col.logicType"
              placeholder="请选择"
            >
              <el-option
                v-for="item in logicalOperatorList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item>
        <div class="text-content">
          {{ marchText }}
        </div>
      </el-form-item>
      <el-form-item label="命中维度排序：" prop="modelOrder">
        <div class="single-item-body">
          <el-input-number
            v-model="modelData.modelOrder"
            controls-position="right"
            :min="1"
            :max="99"
            :precision="0"
            :step="1"
          ></el-input-number>
          <!-- <el-input clearable v-model.number="modelData.modelOrder" /> -->
        </div>
      </el-form-item>
      <!-- accept -->
      <el-form-item label="定制模板：" ref="excelUpload" prop="excelModelUrl">
        <el-upload
          ref="upload"
          action="/upload"
          style="display: inline-block"
          :show-file-list="false"
          :multiple="false"
          :http-request="httpRequestHandle"
          :before-upload="$validateFileType"
          accept=".csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          :data="{}"
        >
          <el-button type="primary" plain size="mini">
            上传模板
            <i class="el-icon-upload el-icon--right" />
          </el-button>
        </el-upload>
        <el-link style="margin-left:10px;" type="info" @click="getTemplate"
          >获取初始模板</el-link
        >
        <i
          style="margin-left:10px;color:#4caf50;"
          v-if="modelData.excelModelUrl"
          class="el-icon-circle-check"
        >
          已上传</i
        >
        <el-link
          v-if="modelData.excelModelUrl"
          :href="
            'https://view.officeapps.live.com/op/view.aspx?src=' +
              encodeURIComponent(modelData.excelModelUrl)
          "
          style="margin-left:10px;"
          type="success"
          @click="getTemplate"
          target="_blank"
          >预览</el-link
        >
      </el-form-item>
      <el-form-item label>
        <el-button @click="cancel">关闭</el-button>
        <el-button type="primary" :loading="submitLoading" @click="ok"
          >提交</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  addModel,
  goodsBrandListSearch,
  editModel,
  getInitModel,
  getModelDetail,
  templateTypeStatusOptions
} from '@/api/templateCenter.js'
import { uploadFile } from '@/api/ecorp'
import * as shortid from 'shortid'

// 新增编辑公用
export default {
  name: 'AccountSet',
  data () {
    let opts = [...templateTypeStatusOptions]
    opts.shift()
    return {
      fileSizeLimit: 10, // 上传文件大小限制，单位 MB
      templateTypeStatusOptions: opts, // 模板类型选项列表
      fieldList: [], // 模板类型对应的维度列表
      comparisonOperatorList: [
        { label: '=', value: '=' },
        { label: '!=', value: '!=' }
      ], // 比较运算符
      logicalOperatorList: [
        {
          label: 'and',
          value: 'and'
        },
        {
          label: 'or',
          value: 'or'
        }
      ], // 逻辑运算符
      modelData: {
        excelModelUrl: '',
        matchColumnLogics: [
          {
            columnData: '',
            columnName: '',
            logicType: 'and',
            operType: '=',
            matchTable: '',
            key: shortid.generate()
          }
        ],
        modelCode: 0,
        modelId: '',
        modelName: '',
        modelOrder: 1,
        modelStatus: 0,
        modelType: 0
      },
      isEdit: true,
      rules: {
        modelName: [
          {
            required: true,
            message: '模板名称不能为空'
          }
        ],
        modelType: [
          {
            required: true,
            // type: 'number',
            message: '模板类型不能为空'
          }
        ],
        modelOrder: [
          {
            required: true,
            message: '命中维度排序不能为空'
          },
          {
            type: 'number',
            message: '请输入数字'
          }
        ],
        excelModelUrl: [
          {
            required: true,
            // type: 'number',
            message: '定制模板不能为空'
          }
        ],
        matchColumnLogics: [
          {
            validator: (rule, value, callback) => {
              console.log(value)
            },
            trigger: 'change'
          }
        ]
      }
    }
  },
  watch: {
    'modelData.modelType': {
      handler (newVal, oldVal) {
        this.changeModelType()
      }
    }
  },
  created () {
    this.initData()
  },
  computed: {
    marchText () {
      let text = ''
      if (this.modelData && this.modelData.matchColumnLogics) {
        this.modelData.matchColumnLogics.forEach((item, index, arr) => {
          let itemText = ''
          if (
            item.columnName &&
            item.operType &&
            (item.logicType || index === arr.length - 1)
          ) {
            itemText +=
              item.columnName +
              ' ' +
              item.operType +
              ' ' +
              '\'' +
              item.columnData +
              '\'' +
              (index === arr.length - 1 ? '' : ' ' + item.logicType) +
              ' '
          }
          text += itemText
        })
      }
      return text.trim()
    }
  },
  methods: {
    columnNameSelect (obj, index) {
      let item = { ...this.modelData.matchColumnLogics[index] }

      item.columnName = obj.columnName
      // this.modelData.matchColumnLogics[index].columnName=obj.matchColumnDesc
      item.matchTable = obj.matchTable

      this.$set(this.modelData.matchColumnLogics, index, item)
    },
    changeModelType () {
      this.matchColumnLogics = []
      this.excelModelUrl = ''
      this.modelOrder = 0
      this.excelModelUrl = ''
      this.getModelColumns()
    },
    // 删除模板命中维度
    delModelMarchCondition (col, index) {
      this.modelData.matchColumnLogics.splice(index, 1)
    },
    // 添加模板命中维度
    addModelMarchCondition () {
      this.modelData.matchColumnLogics.push({
        columnData: '',
        columnName: '',
        logicType: 'and',
        operType: '=',
        key: shortid.generate()
      })
    },
    initData () {
      this.isEdit = this.$route.name === 'editTcList'
      // 如果是编辑，则获取页面信息
      if (this.isEdit) {
        const modelId = this.$route.params.templateId
        getModelDetail({ modelId: modelId }).then(res => {
          if (res) {
            this.modelData = res
            this.getModelColumns()
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        this.getModelColumns()
      }
    },
    getModelColumns () {
      let param = { modelType: this.modelData.modelType }
      goodsBrandListSearch(param).then(res => {
        if (res && res.length > 0) {
          this.fieldList = res.map(item => {
            return {
              label: item.matchColumnDesc,
              columnName: item.matchColumnCode,
              matchTable: item.matchTable
            }
          })
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    cancel () {
      // this.$EventBus.$emit('closeTag', this.$route.path)
      this.$closeTag(this.$route.path)
    },
    ok () {
      this.$refs['modelForm'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          if (this.isEdit) {
            this.editModel()
          } else {
            this.addModel()
          }
        } else {
        }
      })
    },
    addModel () {
      let param = { ...this.modelData }
      delete param.modelCode
      delete param.modelId
      delete param.modelStatus
      addModel(param)
        .then(res => {
          this.submitLoading = false
          if (res.code === 200) {
            this.$message.success('添加成功')
            sessionStorage.setItem('templateEdit', 1)
            this.cancel()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(() => {
          this.submitLoading = false
        })
    },
    editModel () {
      let param = { ...this.modelData }
      delete param.modelCode
      delete param.modelStatus
      editModel(param)
        .then(res => {
          this.submitLoading = false
          if (res.code === 200) {
            this.$message.success('修改成功')
            sessionStorage.setItem('templateEdit', 1)
            this.cancel()
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(e => {
          this.$message.error(e)
          this.submitLoading = false
        })
    },
    getTemplate () {
      getInitModel(this.modelData.modelType)
    },
    httpRequestHandle (file) {
      if (!file.data) {
        this.$message.error('上传文件参数缺失！')
        return
      }

      // 校验大小
      const isGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit
      if (isGtLimit) {
        this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.5)',
        customClass: 'loadingCustomer'
      })
      this.fileName = encodeURIComponent(file.file.name)
      const formData = new FormData()
      formData.append('file', file.file)
      uploadFile(formData, 'onestop')
        .then(response => {
          loading.close()
          if (response && response.length > 0) {
            const objectKey = response[0].objectKey
            this.modelData.excelModelUrl = `https://zkh360-onestop.oss-cn-hangzhou.aliyuncs.com/${objectKey}`
            this.$refs.excelUpload.clearValidate()
          } else {
            this.$message.error('文件上传失败，请重新尝试')
          }
        })
        .catch(e => {
          this.$message.error('文件上传失败，请重新尝试')
          loading.close()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.template-set-container {
  .template-set-form {
    margin-top: 30px;
    .single-item-body {
      max-width: 200px;
    }
  }
  .el-form-item {
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .el-select {
    width: 100%;
  }
  .text-content {
    padding: 20px;
    max-width: 800px;
    min-height: 60px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
}
</style>
