<template>
  <div class="app-container account-statement-container">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="对账单名称：" prop="name">
              <el-input
                v-model.trim="searchForm.name"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户编号：" prop="customer">
              <RemoteCustomer v-model="searchForm.customer" getValue placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户名称：" prop="otherCustomer">
              <RemoteCustomer v-model="searchForm.otherCustomer" placeholder="请输入关键词" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客服：" prop="customerServiceName">
              <el-input
                v-model.trim="searchForm.customerServiceName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="确认时间：">
              <el-col :span="11">
                <el-form-item prop="confirmTimeStart">
                  <el-date-picker
                    clearable
                    v-model="searchForm.confirmTimeStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="confirmTimeEnd">
                  <el-date-picker
                    clearable
                    v-model="searchForm.confirmTimeEnd"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间：">
              <el-col :span="11">
                <el-form-item prop="gmtCreateStart">
                  <el-date-picker
                    clearable
                    v-model="searchForm.gmtCreateStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="gmtCreateEnd">
                  <el-date-picker
                    clearable
                    v-model="searchForm.gmtCreateEnd"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="状态：" prop="status" v-if="visible">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in statementStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建人：" prop="createdBy">
              <el-input
                v-model.trim="searchForm.createdBy"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户是否签收：" prop="signStatus">
              <el-select
                multiple
                v-model="searchForm.signStatus"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in signStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="交货状态：" prop="deliveryStatus">
              <el-select
                v-model="searchForm.deliveryStatus"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in deliveryStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="订单号：" prop="orderNo">
              <el-input
                v-model="searchForm.orderNo"
                placeholder="外围订单号、销售订单号、客户订单号 均支持搜索"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="对账依据编号：" prop="basisNo">
              <el-input
                v-model="searchForm.basisNo"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="对账单编号：" prop="no">
              <el-input
                v-model="searchForm.no"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="statement-search-result-container" ref="resultContainer">
      <el-row type="flex" class="result-title" justify="space-between">
        <el-col :span="16">对账单列表</el-col>
        <el-col :span="8" style="text-align:right;">
        <el-link type="primary" @click="toDownLoadList" style="margin-right: 10px">下载专区</el-link>
          <el-button
            type="primary"
            icon="el-icon-search"
            :loading="listLoading"
            @click="handleFilter"
            >查询</el-button
          >
          <el-button icon="el-icon-refresh-left" @click="handleReset">重置</el-button>
        </el-col>
      </el-row>
      <el-table
        v-loading="listLoading"
        :data="statementList"
        border
        fit
        highlight-current-row
        :height="calcHeight"
        @sort-change="handleSortChange"
      >
        <el-table-column
          label="对账单名称"
          min-width="150px"
          align="center"
          prop="name"
        >
          <template slot-scope="{ row }">
            <!-- <zkh-link
              :targetUrl="'/account-statement-center/statement/detail/' + row.no">{{ row.name }}</zkh-link> -->
              <el-link type="primary" :underline="false" @click="statementDetail(row)">{{row.name}}</el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="对账单编号"
          min-width="150px"
          align="center"
          prop="no"
        />
        <el-table-column
          label="状态"
          min-width="100px"
          align="center"
          prop="status"
        >
          <template slot-scope="{ row }">
            {{ getLabelByValue(row.status, statementStatusOptions) }}
          </template>
        </el-table-column>
        <!-- <el-table-column label="oms交货单号" min-width="100px" align="center" prop="omsDnNo" />
        <el-table-column label="oms交货单行号" min-width="100px" align="center" prop="omsDnItemNo" />
        <el-table-column label="oms订单号" min-width="100px" align="center" prop="omsSoNo" />
        <el-table-column label="oms订单行号" min-width="100px" align="center" prop="omsSoItemNo" /> -->
        <el-table-column
          label="创建时间"
          min-width="100px"
          align="center"
          prop="gmtCreate"
          sortable="custom"
        />
        <el-table-column
          label="确认时间"
          min-width="100px"
          align="center"
          prop="confirmGmtCreate"
          sortable="custom"
        />
        <!-- <el-table-column
          label="审核时间"
          min-width="100px"
          align="center"
          prop="auditTime"
        /> -->
        <el-table-column
          label="创建人"
          min-width="100px"
          align="center"
          prop="createdBy"
        />

        <el-table-column
          label="客户"
          min-width="100px"
          align="center"
          prop="customer"
        />
        <el-table-column
          label="客户名称"
          min-width="100px"
          align="center"
          prop="customerName"
        />
        <el-table-column
          label="对账配置编号"
          min-width="100px"
          align="center"
          prop="configRule"
        />

        <el-table-column
          label="货币"
          min-width="100px"
          align="center"
          prop="currency"
        />

        <el-table-column
          label="对账含税总金额"
          min-width="130px"
          align="center"
          prop="checkTotalAmountTax"
          sortable="custom"
        >
          <template slot-scope="{ row }">
            {{ row.checkTotalAmountTax | fixedDecimalPlace2 }}
          </template>
        </el-table-column>

        <el-table-column
          label="对账未税总金额"
          min-width="120px"
          align="center"
          prop="checkTotalAmount"
        >
          <template slot-scope="{ row }">
            {{ row.checkTotalAmount | fixedDecimalPlace2 }}
          </template>
        </el-table-column>
        <el-table-column
          label="核销总金额"
          min-width="120px"
          align="center"
          prop="verificationTotalAmountTax"
        >
          <template slot-scope="{ row }">
            {{ row.verificationTotalAmountTax | fixedDecimalPlace2 }}
          </template>
        </el-table-column>
        <el-table-column
          label="盈亏总金额"
          min-width="120px"
          align="center"
          prop="profitLossTotalAmountTax"
        >
          <template slot-scope="{ row }">
            {{ row.profitLossTotalAmountTax | fixedDecimalPlace2 }}
          </template>
        </el-table-column>
        <el-table-column
          label="应收确认中总金额"
          min-width="120px"
          align="center"
          prop="confirmingTotalAmountTax"
        >
          <template slot-scope="{ row }">
            {{ row.confirmingTotalAmountTax | fixedDecimalPlace2 }}
          </template>
        </el-table-column>
        <el-table-column
          label="已确认应收总金额"
          min-width="120px"
          align="center"
          prop="confirmedTotalAmountTax"
        >
          <template slot-scope="{ row }">
            {{ row.confirmedTotalAmountTax | fixedDecimalPlace2 }}
          </template>
        </el-table-column>

        <el-table-column
          label="客服"
          min-width="100px"
          align="center"
          prop="customerService"
        />
        <el-table-column
          label="销售"
          min-width="100px"
          align="center"
          prop="sales"
        />
        <el-table-column
          label="日志"
          min-width="200px"
          align="center"
          prop="log"
        />
        <el-table-column
          label="备注"
          min-width="200px"
          align="center"
          prop="comment"
        />
        <el-table-column
          label="id"
          min-width="100px"
          align="center"
          prop="id"
        />
        <el-table-column fixed="right" label="操作" min-width="220px" align="center">
          <template slot-scope="{ row }">
            <el-button
              v-if="row.status === '2'"
              type="text"
              @click="toConfirm(row)"
              >在线确认</el-button
            >
            <el-button
              v-if="row.status === '2'"
              type="text"
              @click="backToDraft(row)"
              >退回草稿</el-button
            >
            <el-button type="text" @click="exportStatement(row)"
              >导出对账单</el-button
            >
            <el-button
              v-if="row.status === '3' && row.cancel === '1'"
              type="text"
              @click="toCancel(row)"
              >取消</el-button
            >
            <el-button
              v-if="
                row.status === '1' &&
                  (row.auditStatus === '1' || row.auditStatus === '3')
              "
              type="text"
              @click="comeIntoEffect(row)"
              >提交生效</el-button
            >
            <el-button
              v-if="row.status === '1'"
              type="text"
              @click="delStatement(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getStatementList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'

// import clip from '@/utils/clipboard' // use clipboard directly

import { getLabelByValue } from '@/utils/index.js'
import { fixedDecimalPlace } from '@/filters/index.js'
// import { downloadByLink, isValidLink } from '@/utility/request'
import {
  // createStatementByBasis,
  getAuditStatusOptionsOptions,
  getStatementStatusOptions,
  getStatementList,
  backToDraft,
  delStatement,
  comeIntoEffect,
  getCustomerListByPartName,
  exportStatement,
  onlineConfirmApi,
  onlineCancelApi
} from '@/api/accountStatement.js'
import { RemoteCustomer } from '@kun-ui/remote-customer'
const fixedDecimalPlace2 = fixedDecimalPlace.bind(null, 2)
// 页面搜索条件初始化
const getInitSearchParams = function () {
  return {
    name: '', // 对账单名称
    customer: '', // 客户编号
    otherCustomer: '', // 客户编码(下拉框)
    factory: '', // 工厂
    status: '', // 状态
    customerServiceName: '', // 客服
    createdBy: '', // 创建人
    auditStatus: '', // 审核状态
    confirmTimeStart: '', // 确认时间（起始）
    confirmTimeEnd: '', // 确认时间（结束）
    gmtCreateStart: '', // 创建时间（起始）
    gmtCreateEnd: '', // 创建时间（结束）
    signStatus: '', // 签收状态
    deliveryStatus: '', // 交货状态
    checkTotalAmountTaxOrder: '', // 对账单金额排序
    idOrder: 1 // 默认按照id排序
  }
}

export default {
  name: 'StatementList',
  filters: {
    fixedDecimalPlace2: function (val) {
      return fixedDecimalPlace2(val)
    }
  },
  data () {
    return {
      auditStatusOptions: [], // 对账单审核状态 下拉选项 '1' 无需审核，'2' 待审核， '3' 审核通过， '4' 驳回
      statementStatusOptions: [], // 对账单状态 下拉选项  '1' 草稿， '2' 对账中， '3' 对账完成

      customerNoLoading: false,

      listLoading: false, // 查询按钮loading
      customerNoOptions: [],

      searchForm: {}, // 绑定搜索参数

      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      rules: {},
      statementList: [],
      total: 0,
      deliveryStatusOptions: [
        { label: '部分交货', value: 'B' },
        { label: '已交货', value: 'C' }
      ],
      signStatusOptions: [
        { label: '未签收', value: '0' },
        { label: '已签收', value: '1' }
      ]
    }
  },
  props: {
    condition: {
      type: Object,
      default() {
        return {};
      }
    },
    visible: {
      type: Boolean,
      default: true
    },
    diableInitialFetchList: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Pagination,
    RemoteCustomer
  },
  watch: {
    condition(newVal, oldVal) {
      this.searchForm = Object.assign(this.searchForm, newVal)
      this.handleFilter()
    }
  },
  created () {
    Promise.all([
      getStatementStatusOptions().then(data => {
        this.statementStatusOptions = data
      }),
      getAuditStatusOptionsOptions().then(data => {
        this.auditStatusOptions = data
      })
    ]).then(() => {
      this.searchForm = getInitSearchParams()
      if (!this.diableInitialFetchList) {
        this.handleFilter()
      }
    })
  },
  computed: {
    calcHeight () {
      let height = window.innerHeight
      if (height < 710) {
        return 450
      }
      return 550
    }
  },
  methods: {
    getLabelByValue,
    getStatementList () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.listLoading = true
          let param = { ...this.searchForm }
          param.pageNum = this.listQueryInfo.current
          param.pageSize = this.listQueryInfo.pageSize
          if (param.signStatus && Array.isArray(param.signStatus)) {
            param.signStatus = param.signStatus.filter(e => e).join(',')
          }
          this.statementList = []
          getStatementList(param)
            .then(res => {
              this.listLoading = false
              if (res.status === 200) {
                if (res.datas) {
                  const data = res.datas
                  this.total = data.total
                  this.statementList = data.list
                }
              } else {
                this.$message.error(res.message)
              }
            })
            .catch(() => {
              this.listLoading = false
            })
        }
      })
    },
    // 查询按钮
    handleFilter () {
      this.listQueryInfo.current = 1
      this.getStatementList()
    },
    // 重置按钮
    handleReset () {
      this.searchForm = getInitSearchParams()
      if (this.condition) {
        this.searchForm = { ...this.searchForm, ...this.condition }
      }
      // this.$refs['searchForm'].resetFields()
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerListByPartName({
          customerName: key
        }).then(res => {
          this.customerNoLoading = false
          if (res.status === 200) {
            if (res.datas && res.datas.length > 0) {
              this.customerNoOptions = res.datas.map(item => {
                return {
                  value: item.customerNumber,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    },
    // 提交生效
    comeIntoEffect (row) {
      this.$confirm('是否对该对账单提交生效', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        comeIntoEffect(row.id)
          .then(res => {
            if (res.status === 200) {
              this.getStatementList()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch(e => {
            let res = e.response ? e.response.data : {}
            this.$message.error(res.message || res.msg || res.error || e)
          })
      })
    },
    // 退回草稿
    backToDraft (row) {
      this.$confirm('是否将该对账单退回到草稿', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        backToDraft(row.id)
          .then(res => {
            if (res.status === 200) {
              this.getStatementList()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch(e => {
            let res = e.response ? e.response.data : {}
            this.$message.error(res.message || res.msg || res.error || e)
          })
      })
    },
    // 在线确认
    toConfirm (row) {
      this.$confirm('是否确认该对账单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        console.log(row)
        onlineConfirmApi(row.id)
          .then(res => {
            if (res && res.status === 200) {
              this.$message.success(res.msg || '操作成功！')
              this.getStatementList()
            } else {
              this.$message.error(res.message || res.msg || '操作失败！')
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '确认失败！')
          })
      })
    },
    // 取消
    toCancel (row) {
      this.$confirm('是否取消该对账单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        onlineCancelApi(row.id)
          .then(res => {
            if (res && res.status === 200) {
              this.$message.success(res.msg || '操作成功！')
              this.getStatementList()
            } else {
              this.$message.error(res.message || '该对账单正在开票，无法取消！')
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '取消失败！')
          })
      })
    },
    // 导出对账单
    exportStatement (row) {
      exportStatement(row.no)
        .then(res => {
          console.log(res)
          if (res?.status === 200) {
            this.$message.success('申请导出对账单成功，请在下载专区查看导出内容！')
            try {
              this.$closeTag('/purchaseReport/downLoadList')
            } catch {

            }
            setTimeout(() => {
              this.$router.push({
                path: '/purchaseReport/downLoadList'
              })
            }, 600)
          } else {
            this.$message.error(res.msg || res.message || '导出失败！')
          }
          // if (res && res.status === 200 && res.datas) {
          //   if (isValidLink(res.datas)) {
          //     downloadByLink(res.datas)
          //     this.$message.success('导出成功！')
          //   } else {
          //     this.$message.error('导出链接不合法！')
          //   }
          // } else {
          //   this.$message.error(res.msg || res.message || '导出失败！')
          // }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '导出失败！')
        })
    },
    // 跳转下载专区
    toDownLoadList () {
      try {
        this.$closeTag('/purchaseReport/downLoadList')
        this.$router.push('/purchaseReport/downLoadList');
      } catch {}
    },
    // 删除对账单
    delStatement (row) {
      this.$confirm('是否要删除该对账单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        delStatement(row.id)
          .then(res => {
            if (res.status === 200) {
              this.getStatementList()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch(e => {
            let res = e.response ? e.response.data : {}
            this.$message.error(res.message || res.msg || res.error || e)
          })
      })
    },
    // 排序
    handleSortChange ({ column, prop, order }) {
      // console.log(column, prop, order)
      if (prop === 'checkTotalAmountTax') {
        this.searchForm.idOrder = ''
        this.searchForm.gmtCreateOrder = ''
        this.searchForm.confirmTimeOrder = ''
        this.searchForm.checkTotalAmountTaxOrder = (order === 'ascending' ? 0 : 1)
      }
      if (prop === 'gmtCreate') {
        this.searchForm.idOrder = ''
        this.searchForm.checkTotalAmountTaxOrder = ''
        this.searchForm.confirmTimeOrder = ''
        this.searchForm.gmtCreateOrder = (order === 'ascending' ? 0 : 1)
      }
      if (prop === 'confirmGmtCreate') {
        this.searchForm.idOrder = ''
        this.searchForm.checkTotalAmountTaxOrder = ''
        this.searchForm.gmtCreateOrder = ''
        this.searchForm.confirmTimeOrder = (order === 'ascending' ? 0 : 1)
      }
      this.handleFilter()
    },
    statementDetail(row) {
      this.$EventBus.$emit('pruneCacheEntry', [`/account-statement-center/statement/detail/${row.no}`]);
      this.$router.push(`/account-statement-center/statement/detail/${row.no}`);
    }
  },
  activated () {
    const needReload = sessionStorage.getItem('statementReload')
    if (needReload) {
      sessionStorage.removeItem('statementReload')
      this.getStatementList()
    }
  }
}
</script>

<style lang="scss" scope></style>
<style lang="scss">
.account-statement-container {
  .filter-container {
    padding-top: 18px;
    padding-right: 10px;
    background-color: #f4f4f4;
    // .el-form-item {
    //   margin-bottom: 10px;
    // }
  }
  .tag-container {
    padding-left: 10px;
    padding-bottom: 5px;
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .customer-tips {
    background: #ffffff;
    margin-top: 15px;
    margin-bottom: 15px;
    padding-left: 0;
    font-size: 14px;
    span {
      text-align: center;
      color: red;
      font-size: 14px;
    }
  }
  .statement-search-result-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin: 10px 0;
    & > {
      .result-title {
        height: 50px;
        padding: 0 10px 0 20px;
        line-height: 50px;
        font-size: 16px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
