<template>
  <div class="un-invoiced-reason-record-container" v-loading="loading.dialogLoading">
    <div class="content">
      <el-table :data="tableData" style="width: 100%" >
      <el-table-column align="center" prop="createdBy" label="操作人" width="100" />
      <el-table-column align="center" prop="unInvoicedReason" label="未开票原因" width="300">
        <template slot-scope="{ row }">
          <div class="reason-item" v-for="(item, index) in reasonList(row.unInvoicedReason)" :key="index">
            <el-tag :type="formatType(item, '0')">{{ formatReason(item, '0') }}</el-tag>
              <i class="el-icon-minus"/>
            <el-tag :type="formatType(item, '1')">{{ formatReason(item, '1') }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="unInvoicedRemark" label="未开票详情" >
        <template slot-scope="scope">
          <el-popover
            v-if="needShorten(scope.row.unInvoicedRemark, 18)"
            placement="top"
            title=""
            width="200"
            trigger="hover"
            :content="scope.row.unInvoicedRemark">
            <div slot="reference">{{shorten(scope.row.unInvoicedRemark, 18)}}</div>
          </el-popover>
          <span v-else>
            {{ scope.row.unInvoicedRemark }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="unInvoicedResponsible" label="责任人">
        <template slot-scope="scope">
          {{
            getUnInvoicedResponsibleLabel(scope.row.unInvoicedResponsible)
          }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="estimatedInvoicingDate" label="预计开票日期" width="120px" />
      <el-table-column align="center" prop="gmtModified" label="操作时间" />
    </el-table>
    </div>
  </div>
</template>
<script>
import {
  getUnInvoicedReasonRecord,
  getUnInvoicedResponsibleOptions
} from '@/api/accountStatement.js'
export default {
  name: 'inReRecordShow',
  props: {
    visible: Boolean,
    inReRecordShowId: [ String, Number ]
  },
  data () {
    return {
      loading: {
        dialogLoading: false
      },
      tableData: [],
      unInvoicedResponsibleOptions: [],
      tagEnumMap: {
        '订单未交齐': '',
        '待签收/入库': 'success',
        '对账开票': 'info',
        '售后退还': 'warning',
        '后补订单': 'danger',
        '客户原因': ''
      }
    }
  },
  watch: {
    visible (newVal, oldVal) {
      if (newVal) {
        this.loading.dialogLoading = true
        this.getRecord(this.inReRecordShowId)
      }
    }
  },
  created () {
    Promise.all([
      this.getRecord(this.inReRecordShowId),
      getUnInvoicedResponsibleOptions().then(data => {
        this.unInvoicedResponsibleOptions = data
      })
    ])
  },
  methods: {
    getUnInvoicedResponsibleLabel (value) {
      const category = this.unInvoicedResponsibleOptions.find(opt => opt.value === value)
      if (category) {
        return category.label
      }
      return value
    },
    needShorten (content, len = 30) {
      let ret = false
      if (content && content.length > len) ret = true
      return ret
    },
    shorten (content, len = 30) {
      content && (content = content.slice(0, len - 3) + '...')
      return content
    },
    reasonList (reason) {
      return reason.split(/\s+/)
    },
    formatType (unInvoicedReason, type) {
      let reason = ''
      try {
        const text = unInvoicedReason.split('-')[type]
        reason = this.tagEnumMap[text]
      } catch (err) {}
      return reason
    },
    formatReason (unInvoicedReason, type) {
      let ret = ''
      try { ret = unInvoicedReason.split('-')[type] } catch (err) {}
      return ret
    },
    getRecord (id) {
      getUnInvoicedReasonRecord(id)
        .then(res => {
          if (res && res.status === 200 && res.datas) {
            this.tableData = res.datas
          } else {
            this.$message.error(res.msg || res.message || '获取未开票记录失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '获取未开票记录失败！')
        })
        .finally(() => {
          this.loading.dialogLoading = false
        })
    },
    closeDialog () {
      this.$emit('un-invoiced-reason-show-dialog', true)
    },
    closeInvoiceDialog () {
      this.closeDialog()
    }
  }
}
</script>
<style lang="less" scoped>
.un-invoiced-reason-record-container{
  .content{
    .reason-item{
      margin-top: 3px;
      // text-align: left;
      i{
        margin-left: 2px;
        margin-right: 2px;
      }
    }
  }
}
</style>
