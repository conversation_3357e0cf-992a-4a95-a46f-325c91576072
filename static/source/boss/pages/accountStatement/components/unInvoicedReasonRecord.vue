<template>
  <div class="un-invoiced-reason-container" v-loading="loading.dialogLoading">
    <div class="content">
      <div
        class="item"
        v-for="(invoiceReason, index) in invoiceReasonData"
        :key="index"
      >
        <el-form label-width="120px" label-position="right">
          <el-row>
            <el-col :span="10">
              <el-form-item label="未开票原因：">
                <el-select
                  v-model="invoiceReason.unInvoicedReason"
                  @change="handleReasonChange(invoiceReason, index)"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in filterNone(unInvoicedReasonOptions)"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="14">
              <el-form-item label="原因细分：">
                <el-select
                  v-model="invoiceReason.unInvoicedReasonDetail"
                  placeholder="请选择"
                  clearable
                >
                  <el-option
                    v-for="item in invoiceReason.unInvoicedReasonDetailOptions"
                    :key="item.label"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <Department
                v-if="visible"
                :key="index"
                @change="(res) => changeRelativePerson(res, invoiceReason, index)"
                valueLabel="责任人"
                required
              />
            </el-col>
            <el-col :span="8">
              <el-form-item label="预计开票日期：">
                <el-date-picker
                  v-model="invoiceReason.estimatedInvoicingDate"
                  type="date"
                  placeholder="请选择日期"
                  value-format="yyyy-MM-dd"></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="14">
              <el-form-item label="未开票详情：">
                <el-input
                  type="textarea"
                  :rows="2"
                  placeholder="请输入内容"
                  maxlength="101"
                  @input="remarkChange(invoiceReason)"
                  v-model="invoiceReason.unInvoicedRemark"
                >
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="invoiceReason.remarkExceed">
            <span class="remark-exceed-error"> 未开票详情最多100字符！ </span>
          </el-row>
        </el-form>
      </div>
      <el-button
        size="mini"
        style="margin-left: 50px"
        icon="el-icon-plus"
        @click="addReason"
      ></el-button>
      <el-button
        size="mini"
        style="margin-left: 10px"
        icon="el-icon-minus"
        @click="removeReason"
      ></el-button>
    </div>
    <footer>
      <el-button @click="closeInvoiceDialog">取消</el-button>
      <el-button @click="createUnInvoiceRecord" type="primary">确定</el-button>
    </footer>
  </div>
</template>
<script>
import Department from './Department'
import {
  createUnInvoiceRecord,
  getUnInvoicedReasonDetailOptions
} from '@/api/accountStatement.js'
import moment from 'moment'
const invoiceReasonData = [
  {
    unInvoicedReason: '',
    unInvoicedReasonDetailOptions: [],
    unInvoicedReasonDetail: '',
    unInvoicedResponsiblePerson: [],
    unInvoicedRemark: '',
    estimatedInvoicingDate: ''
  }
]
export default {
  components: {
    Department
  },
  name: 'unInvoicedReasonRecord',
  props: {
    visible: Boolean,
    unInvoicedReasonOptions: Array,
    basisId: String
  },
  data () {
    return {
      unInvoicedReasonDetailOptions: [],
      invoiceReasonData,
      loading: {
        dialogLoading: false
      }
    }
  },
  created () {},
  mounted () {
    this.invoiceReasonData[0].unInvoicedReasonDetailOptions = this.unInvoicedReasonDetailOptions
  },
  watch: {
    visible (newVal, oldVal) {
      if (newVal === false) {
        this.invoiceReasonData = [
          {
            unInvoicedReason: '',
            unInvoicedReasonDetailOptions: [],
            unInvoicedReasonDetail: '',
            unInvoicedResponsiblePerson: [],
            unInvoicedRemark: ''
          }
        ]
      }
    }
  },
  methods: {
    changeRelativePerson (res, invoiceReason, index) {
      invoiceReason.unInvoicedResponsiblePerson = res
    },
    filterNone (list) {
      return (Array.isArray(list) && list.filter(li => li.value !== '0')) || []
    },
    remarkChange (reason) {
      console.log(reason)
      if (reason.unInvoicedRemark.length > 100) {
        reason.remarkExceed = true
        reason.unInvoicedRemark = reason.unInvoicedRemark.slice(0, 100)
      } else {
        reason.remarkExceed = false
      }
    },
    addReason () {
      this.invoiceReasonData.push({
        unInvoicedReason: '',
        unInvoicedReasonDetail: '',
        unInvoicedResponsiblePerson: [],
        unInvoicedRemark: '',
        unInvoicedReasonDetailOptions: []
      })
    },
    removeReason () {
      if (this.invoiceReasonData.length > 1) {
        this.invoiceReasonData.pop()
      }
    },
    closeDialog (done) {
      this.invoiceReasonData = [
        {
          unInvoicedReason: '',
          unInvoicedReasonDetail: '',
          unInvoicedResponsiblePerson: [],
          unInvoicedRemark: '',
          unInvoicedReasonDetailOptions: []
        }
      ]
      this.$emit('un-invoiced-reason-dialog', true)
      if (done) {
        this.$emit('un-invoiced-reason-done', true)
      }
    },
    validateParam (recordList) {
      console.log(recordList)
      let ret = true
      if (recordList.some(record => {
        if (!record.unInvoicedReason ||
            !record.unInvoicedReasonDetail ||
            !record.unInvoicedResponsiblePerson.length ||
            !record.unInvoicedRemark
        ) {
          return true
        }
      })) {
        this.$message.error('未开票原因，原因细分，责任人，未开票详情必填！')
        return false
      }
      if (recordList.some(record => {
        if (record.unInvoicedResponsiblePerson.length > 1) {
          return true
        }
      })) {
        this.$message.error('责任人不能选择多人！')
        return false
      }
      if (recordList.some(record => record.unInvoicedRemark.length > 100)) {
        this.$message.error('未开票详情最多100字符！')
        return false
      }
      if (recordList.some(record => moment(record.estimatedInvoicingDate).isBefore(moment().format('YYYY-MM-DD')))) {
        this.$message.error('预计开票日期需大于等于当前日期！')
        return false
      }
      const duplicateList = recordList.map(record => record.unInvoicedReason + '_' + record.unInvoicedReasonDetail)
      console.log(duplicateList)
      if (duplicateList.length !== [...new Set(duplicateList)].length) {
        this.$message.error('未开票原因与原因细分不能重复！')
        return false
      }
      return ret
    },
    createUnInvoiceRecord () {
      console.log(this.invoiceReasonData)
      let recordList = this.invoiceReasonData.map(reason => {
        const tmp = { ...reason }
        delete tmp.unInvoicedReasonDetailOptions
        delete tmp.remarkExceed
        // if (tmp.unInvoicedReasonDetail) {
        //   tmp.unInvoicedReasonDetail = tmp.unInvoicedReasonDetail.filter(e => e).join(',')
        // }
        return tmp
      })
      if (!this.validateParam(recordList)) return
      recordList = recordList.map((record) => {
        record.unInvoicedResponsiblePerson = `${record.unInvoicedResponsiblePerson[0].id}`
        return record
      })
      const data = {
        basisId: this.basisId,
        recordList
      }
      createUnInvoiceRecord(data)
        .then(res => {
          if (res && res.status === 200) {
            this.$message.success(res.message)
            this.closeDialog(true)
          } else {
            this.$message.error(res.msg || res.message || '操作失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '操作失败！')
        })
    },
    closeInvoiceDialog () {
      this.closeDialog()
    },
    handleReasonChange (reason) {
      console.log(reason)
      reason.unInvoicedReasonDetail = ''
      if (!reason.unInvoicedReason) {
        reason.unInvoicedReasonDetailOptions = []
        return
      }
      getUnInvoicedReasonDetailOptions(reason.unInvoicedReason, { deleted: false })
        .then(res => {
          if (res && res.status === 200 && res.datas) {
            reason.unInvoicedReasonDetailOptions = res.datas.map(data => ({
              label: data.name, value: data.code
            }))
          }
        })
    }
  }
}
</script>
<style lang="less" scoped>
.un-invoiced-reason-container{
  .content .item span.remark-exceed-error{
    color: red;
    margin-left: 120px;
  }
  footer{
    display: flex;
    justify-content: center;
  }
}
</style>
