<template>
  <div>
    <el-row :gutter="20" v-if="showSearch || !defaultList">
      <!-- <el-col :span="8">
        <el-form-item :label="departmentLabel || '通过部门选择'">
          <el-cascader
            v-model="department"
            :props="departmentProps"
            :show-all-levels="false"
            separator="-"
            clearable
            style="margin-right: 5px"
            @change="changeDepartment"
          />
        </el-form-item>
      </el-col> -->
      <el-col :span="8" v-if="showSearch || !defaultList">
        <el-form-item :label="userLabel || '通过名称查找'">
          <el-select
            v-model="person"
            filterable
            remote
            reserve-keyword
            placeholder="请输入姓名"
            :remote-method="queryUser"
            :loading="loading"
            value-key="id"
            @change="changePerson"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.name + '(' + item.departmentName + ')'"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="roleListProp">
        <el-form-item :label="userLabel || '通过角色查找'">
          <el-select
            v-model="role"
            reserve-keyword
            placeholder="请选择角色"
            value-key="id"
            @change="changeRole"
          >
            <el-option
              v-for="item in roleList"
              :key="item.id"
              :label="item.name"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
        <el-col :span="8" v-if="matters.length !== 0">
          <el-form-item label="通过事项查找">
            <el-cascader
            v-model="matter"
            :options="matters"
            :props="matterProps"
            :show-all-levels="false"
            separator="-"
            clearable
            style="margin-right: 5px"
            @change="changeMatter"
          />
          </el-form-item>
        </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item :label="valueLabel || '选择人员'" :required="required">
          <el-tag
            v-for="person in itemList"
            :key="person.id"
            closable
            @close="closeTag(person)"
            style="margin-right: 15px"
          >
            {{ person.name }}({{ person.departmentName }})
            <span class="role-tag">{{ person.role }}</span>
          </el-tag>
          <span v-if="!itemList || itemList.length === 0">
            <el-tag type="info">空</el-tag>
            <el-tooltip
              class="item"
              effect="dark"
              content="请通过上面的选项查询选择"
              placement="top"
            >
              <i class="el-icon-warning" style="margin-left: 10px"></i>
            </el-tooltip>
          </span>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  departmentUser,
  getDepartmentHead,
  departmentByName,
  departmentById,
  getMatterData,
  getConfig
} from '@/api/accountStatement';
import { formatDepartmentTree } from '@/utils/index';

export default {
  props: [
    'valueLabel',
    'departmentLabel',
    'userLabel',
    'required',
    'categoryId',
    'defaultList',
    'referOrderRoles',
    'roleListProp',
    'defaultRoleList',
    'showSearch' // 增加一个showSearch， 表示在有defaultList时， 也出现名称的搜索框 zj
  ],
  data() {
    return {
      department: null,
      person: [],
      userList: [],
      itemList: [],
      role: '',
      loading: false,
      departmentProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          let name = '';
          if (level === 0) {
            departmentByName(name).then((res) => {
              if (res && res.code === 0) {
                const departments = res.data.map((item) => {
                  return {
                    value: item.id,
                    label: item.name,
                    children: []
                  };
                });
                resolve(departments);
              }
            });
          } else if (level === 1) {
            const {
              data: { value }
            } = node;
            departmentById(value).then((res) => {
              if (res && res.code === 0 && res.data.sons) {
                const de = formatDepartmentTree(res.data);
                resolve(de);
              }
            });
          } else {
            const { hasChildren, children } = node;
            if (hasChildren && children && children.length > 0) {
              resolve([]);
            }
          }
          resolve([]);
        }
      },
      matter: null,
      matters: [],
      matterProps: {
        checkStrictly: true,
        lazy: true,
        lazyLoad(node, resolve) {
          const { value } = node;
          const params = {
            categoryId: this.categoryId,
            id: value
          }
          getMatterData(params).then((res) => {
            if (res && res.code === 0) {
              const matters = res.data.map((item) => {
                return {
                  value: item.id,
                  label: item.name,
                  leaf: !item.children,
                  ...res.data
                };
              });
              resolve(matters);
            }
          });
        }
      },
      roleList: [
        { name: '商品经理', id: 'goodsManager' },
        { name: '采购员', id: 'buyer' },
        { name: '客服', id: 'customerService' },
        { name: '销售', id: 'salesman' },
        { name: '售后运营', id: 'afterSales' }
      ]
    };
  },
  components: {},
  computed: {},
  watch: {
    categoryId (newValue) {
      this.categoryId = newValue
      getMatterData({ categoryId: newValue }).then(res => {
        if (res && res.code === 0) {
          this.matters = res.data.map((item) => {
            return {
              value: item.id,
              label: item.name,
              leaf: !item.children,
              ...res.data
            };
          });
        }
      })
    },
    roleListProp (newVal) {
      this.roleListProp = newVal
    },
    defaultRoleList (newVal) {
      if (newVal) {
        this.itemList = newVal
        this.uniqueArr()
        this.$emit('change', this.itemList);
      }
    }
  },
  methods: {
    queryUser(val) {
      return departmentUser(val.trim()).then((res) => {
        if (res && res.code === 0) {
          this.userList = res.data.map(item => {
            return {
              ...item,
              userId: item.id,
              userName: item.username
            }
          });
        }
      });
    },
    changeDepartment(val) {
      const len = (val || []).length;
      if (len > 0) {
        const last = val[len - 1];
        const categoryId = this.categoryId || 0;
        getDepartmentHead(last, categoryId).then((res) => {
          if (res && res.code === 0) {
            (res.data || []).map((item) => {
              if (item) {
                if (!this.itemList) {
                  this.itemList = [];
                }
                if (!this.itemList.some((user) => user.id === item.id)) {
                  this.itemList.push(item);
                  this.$emit('change', this.itemList);
                }
              }
            });
          }
        });
      }
    },
    changeMatter (val) {
      const len = (val || []).length;
      if (len > 0) {
        const params = {
          categoryId: this.categoryId || 0,
          matterId: val[len - 1]
        }
        getConfig(params).then(res => {
          if (res && res.code === 0 && res.data) {
            const userList = res.data.map(item => {
              return {
                id: item.userId,
                name: item.userName,
                departmentId: item.departmentId,
                departmentName: item.departmentName
              }
            })
            if (this.itemList.length <= 0) {
              this.itemList = userList;
            } else {
              this.itemList = this.itemList.concat(userList)
              this.uniqueArr()
            }
            this.$emit('change', this.itemList);
          }
        })
      }
    },
    changePerson(val) {
      if (val && !this.itemList.some((user) => user.id === val.id)) {
        this.itemList.push(val);
        this.$emit('change', this.itemList);
      }
    },
    closeTag(val) {
      this.person = {}
      this.role = ''
      if (val) {
        const { id } = val;
        if (id) {
          const idx = this.itemList.findIndex((item) => item.id === id);
          if (idx >= 0) {
            this.itemList.splice(idx, 1);
            this.$emit('change', this.itemList);
          }
        }
      }
    },
    changeRole(val) {
      switch (val.id) {
        case 'goodsManager':
          this.getRoleList(this.referOrderRoles.goodsManagerOaUsers, val)
          break;
        case 'buyer':
          this.getRoleList(this.referOrderRoles.buyerOaUsers, val)
          break;
        case 'customerService':
          this.getRoleList(this.referOrderRoles.customerServiceOaUsers, val)
          break;
        case 'salesman':
          this.getRoleList(this.referOrderRoles.sellerOaUsers, val)
          break;
        case 'afterSales':
          this.getRoleList(this.referOrderRoles.afterSalesOaUsers, val)
          break;
      }
    },
    getRoleList(arr, val) {
      if (arr) {
        arr.map(i => {
          i.role = val.name
        })
        this.itemList = this.itemList.concat(arr)
        console.log(this.itemList);
        this.uniqueArr()
        this.$emit('change', this.itemList);
      } else {
        this.$message.error('缺乏角色查询人员必要条件！')
      }
    },
    uniqueArr() {
      var ids = [...new Set(this.itemList.map(i => i.id))]
      let newArr = []
      this.itemList.forEach(item => {
        if (ids.includes(item.id) && (newArr.find(i => item.id === i.id) === undefined)) {
          newArr.push(item)
        }
      })
      this.itemList = newArr
    }
  },
  mounted() {
    if (this.defaultList) {
      this.itemList = [...this.defaultList]
      this.itemList.map(item => {
        item.id = item.userId
      })
      this.$emit('change', this.itemList);
    }
  }
};
</script>

<style lang="scss">
.role-tag {
  position: absolute;
  margin-top: -13px;
  margin-left: -15px;
  background: #84b4f4e8;
  color: #fff;
  border-radius: 8px;
  padding: 0 5px;
  line-height: 18px;
}
</style>
