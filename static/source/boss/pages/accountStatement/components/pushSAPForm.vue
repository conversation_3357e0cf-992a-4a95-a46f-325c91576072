<template>
  <div class="push-sap-form">
    <h1>修改SAP发票信息，确认后即提交SAP开票</h1>
    <div class="diff-icon">
      <p v-if="tipStatus.multiInvoicingByMail !== '0'">
        <i class="el-icon-warning"></i>凭邮件开票 有多个值
      </p>
      <p v-if="tipStatus.multiReturnOffset !== '0'">
        <i class="el-icon-warning"></i>退换货抵消 有多个值
      </p>
      <p v-if="tipStatus.multiMergeBilling !== '0'">
        <i class="el-icon-warning"></i>合并开票 有多个值
      </p>
      <p v-if="tipStatus.multiBillingRobot !== '0'">
        <i class="el-icon-warning"></i>开票机器人 有多个值
      </p>
      <p v-if="tipStatus.multiShowDiscount !== '0'">
        <i class="el-icon-warning"></i>显示折扣 有多个值
      </p>
      <p v-if="tipStatus.multiTicketContact !== '0'">
        <i class="el-icon-warning"></i>收票联系人 有多个值
      </p>
      <p v-if="tipStatus.multiOrderContact !== '0'">
        <i class="el-icon-warning"></i>订单联系人 有多个值
      </p>
      <p v-if="tipStatus.multiIfDocMailed !== '0'">
        <i class="el-icon-warning"></i>附邮寄资料 有多个值
      </p>
      <p v-if="tipStatus.multiExpressCompany !== '0'">
        <i class="el-icon-warning"></i>快递公司 有多个值
      </p>
    </div>
    <div class="step2-checkbox">
      <el-checkbox-group
        style="margin-right: 30px"
        v-model="checkbox1"
        @change="checkboxChange1"
      >
        <el-checkbox
          v-for="(item, index) in checkboxOptions1"
          :label="item.value"
          :key="index"
          >{{ item.label }}</el-checkbox
        >
      </el-checkbox-group>
      <el-checkbox-group v-model="checkbox" @change="checkboxChange">
        <el-checkbox
          v-for="(item, index) in checkboxOptions"
          :label="item.value"
          :key="index"
          >{{ item.label }}</el-checkbox
        >
      </el-checkbox-group>
    </div>
    <el-form ref="pushSapForm" :inline="true" :model="submitForm">
      <el-row
        :gutter="20"
        v-for="(fieldsList, index) in renderStep2Fields"
        :key="index"
      >
        <el-col :span="12" v-for="(field, index) in fieldsList" :key="index">
          <el-form-item
            v-if="field.prop === 'ticketContactName'"
            :label="field.label"
            :prop="field.prop"
            label-width="120px"
          >
            <el-select
              style="width: 178px"
              filterable
              remote
              reserve-keyword
              v-model="submitForm[field.prop]"
              @change="ticketContactNameChange"
              :remote-method="debounceRemoteTicketSearch"
              :loading="loading.ticketLoading"
              placeholder="请输入关键词"
              clearable
            >
              <el-option
                style="color: #ccc"
                disabled
                :value="-1"
                v-show="
                  submitForm.ticketContact &&
                  submitForm.ticketContact.length >= 20
                "
              >
                已展示部分联系人，其他联系人请输入字符进行查询
              </el-option>
              <el-option
                v-for="(item, itemIdx) in submitForm.ticketContact"
                :key="item.contact"
                :label="item.contactName"
                :disabled="itemIdx === 0"
                :value="item.contact"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{ fontWeight: itemIdx === 0 ? 'bold' : 'normal' }"
                >
                  <div>{{ item.contactName }}</div>
                  <div>{{ item.contact }}</div>
                  <div>{{ item.contactPhone || "--" }}</div>
                  <div>{{ item.address || "--" }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-else-if="field.prop === 'orderContactName'"
            :label="field.label"
            :prop="field.prop"
            label-width="120px"
          >
            <el-select
              style="width: 178px"
              filterable
              remote
              reserve-keyword
              :remote-method="debounceRemoteOrderSearch"
              @change="orderContactNameChange"
              :loading="loading.orderLoading"
              v-model="submitForm[field.prop]"
              placeholder="请输入关键词"
              clearable
            >
              <el-option
                style="color: #ccc"
                disabled
                :value="-1"
                v-show="
                  submitForm.orderContact &&
                  submitForm.orderContact.length >= 20
                "
              >
                已展示部分联系人，其他联系人请输入字符进行查询
              </el-option>
              <el-option
                v-for="(item, itemIdx) in submitForm.orderContact"
                :key="item.contact"
                :label="item.contactName"
                :disabled="itemIdx === 0"
                :value="item.contact"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{ fontWeight: itemIdx === 0 ? 'bold' : 'normal' }"
                >
                  <div>{{ item.contactName }}</div>
                  <div>{{ item.contact }}</div>
                  <div>{{ item.contactPhone || "--" }}</div>
                  <div>{{ item.address || "--" }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-else-if="field.prop === 'invoiceType'"
            :label="field.label"
            :prop="field.prop"
            label-width="120px"
          >
            <el-select
              style="width: 178px"
              v-model="submitForm[field.prop]"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in submitForm.invoiceTypeOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-else-if="field.prop === 'expressCompany'"
            :label="field.label"
            :prop="field.prop"
            label-width="120px"
          >
            <el-select
              style="width: 178px"
              v-model="submitForm[field.prop]"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in submitForm.expressCompanyOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            v-else-if="field.label"
            :label="field.label"
            :prop="field.prop"
            label-width="120px"
          >
            <el-input
              :title="submitForm[field.prop]"
              style="width: 178px"
              @input="
                (val) => handleInputChange(val, field.maxlength, field.prop)
              "
              v-model="submitForm[field.prop]"
              :placeholder="'请输入' + field.label"
              :disabled="field.disable"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="push-sap-button">
      <el-button type="default" @click="cancelPush">取消</el-button>
      <el-button
        type="primary"
        @click="pushSapSubmit"
        :loading="loading.submitLoading"
        >确定</el-button
      >
    </div>
  </div>
</template>

<script>
import {
  remoteContact,
  pushSapSubmit,
  getInvoiceTypeOptions,
  getExpressCompanyOptions
} from '@/api/accountStatement.js';
import { chunk, debounce } from 'lodash';
const contactHeader = {
  contactName: '联系人',
  contact: '联系人编号',
  contactPhone: '联系人电话',
  address: '联系人地址'
};
export default {
  name: 'PushSAPForm',
  props: {
    checkResult: {
      type: Object
    },
    onClose: {
      type: Function,
      default: function () {}
    },
    onSuccess: {
      type: Function,
      default: function () {}
    },
    detailId: Number
  },
  data() {
    return {
      tipStatus: {
        invoiceType: '',
        multiBillingRobot: '',
        multiInvoicingByMail: '',
        multiMergeBilling: '',
        multiReturnOffset: '',
        multiShowDiscount: '',
        ticketContact: '',
        orderContact: ''
      },
      checkbox: [],
      checkbox1: [],
      checkboxOptions1: [
        { value: 'includeCustomerRefNum', label: '包含客户参考号' }
      ],
      checkboxOptions: [
        { value: 'invoicingByMail', label: '凭邮件开票' },
        { value: 'ifDocMailed', label: '附邮寄资料' },
        { value: 'returnOffset', label: '退换货抵消' },
        { value: 'mergeBilling', label: '合并开票' },
        { value: 'billingRobot', label: '开票机器人' },
        { value: 'showDiscount', label: '显示折扣' }
      ],
      submitForm: {
        invoiceType: '',
        shippingInfo: '',
        financialNote: '',
        customerRefNum: '',
        originalInvoiceDraftNum: '',
        espRefVoucherNum: '',
        ticketContactName: '',
        ticketContactPhone: '',
        ticketAddress: '', // 手票地址
        ticketAddressId: '', // 手票地址id
        orderContactName: '',
        orderContactPhone: '',
        ticketContact: [],
        orderContact: [],
        invoiceTypeOptions: [],
        expressCompanyOptions: []
      },
      renderStep2Fields: chunk(
        [
          { label: '开票类型', prop: 'invoiceType' },
          { label: '寄票备注', prop: 'shippingInfo', maxlength: 30 },
          { label: '发票备注', prop: 'financialNote', maxlength: 100 },
          { label: '开票申请单号', prop: 'billingApplyNumber' },
          {
            label: '原发票草稿号',
            prop: 'originalInvoiceDraftNum',
            maxlength: 50
          },
          { label: 'ESP参考凭证号', prop: 'espRefVoucherNum', maxlength: 16 },
          { label: '收票联系人', prop: 'ticketContactName' },
          { label: '电话', prop: 'ticketContactPhone', disable: true },
          { label: '收票地址', prop: 'ticketAddress', disable: true },
          { label: '', prop: null },
          { label: '订单联系人', prop: 'orderContactName' },
          { label: '电话', prop: 'orderContactPhone', disable: true },
          { label: '快递公司', prop: 'expressCompany' }
        ],
        2
      ),
      debounceRemoteOrderSearch: null,
      debounceRemoteTicketSearch: null,
      loading: {
        ticketLoading: false,
        orderLoading: false,
        submitLoading: false
      }
    };
  },
  watch: {
    checkResult: {
      handler(checkResult) {
        if (checkResult) {
          this.submitForm = {
            ...this.submitForm,
            ...checkResult.datas,
            orderContact: [],
            ticketContact: []
          };
          // 去除原来逻辑
          this._customerRefNum = this.submitForm.customerRefNum;
          this._financialNote = this.submitForm.financialNote;
          if (this.submitForm.customerRefNum) {
            if (this.submitForm.financialNote) {
              this.submitForm.financialNote =
                this.submitForm.financialNote +
                ' ' +
                this.submitForm.customerRefNum;
            } else {
              this.noteReplacement = true;
              this.submitForm.financialNote = this.submitForm.customerRefNum;
            }
          }
          [
            'invoicingByMail',
            'ifDocMailed',
            'returnOffset',
            'mergeBilling',
            'billingRobot',
            'showDiscount'
          ].forEach((item) => {
            if (checkResult.datas[item] > 0) {
              this.checkbox.push(item);
            }
          });
          this.checkbox1.push('includeCustomerRefNum');
          this.tipStatus = {
            invoiceType: checkResult.datas.invoiceType,
            multiBillingRobot: checkResult.datas.multiBillingRobot,
            multiInvoicingByMail: checkResult.datas.multiInvoicingByMail,
            multiExpressCompany:
              (checkResult.datas.expressCompanyName || '').split(',').length > 1
                ? '1'
                : '0',
            multiMergeBilling: checkResult.datas.multiMergeBilling,
            multiReturnOffset: checkResult.datas.multiReturnOffset,
            multiShowDiscount: checkResult.datas.multiShowDiscount,
            multiIfDocMailed: checkResult.datas.multiIfDocMailed
          };
          let { defaultOrderContact, defaultTicketContact } = this.submitForm;
          this.tipStatus.multiOrderContact = '1';
          if (defaultOrderContact && defaultOrderContact.contact) {
            this.submitForm.orderContactName = defaultOrderContact.contactName;
            this.submitForm.orderContactPhone =
              defaultOrderContact.contactPhone;
            this.submitForm.orderContactId = defaultOrderContact.contact;
            this.tipStatus.multiOrderContact = '0';
          }
          this.tipStatus.multiTicketContact = '1';
          if (defaultTicketContact && defaultTicketContact.contact) {
            this.submitForm.ticketContactName =
              defaultTicketContact.contactName;
            this.submitForm.ticketContactPhone =
              defaultTicketContact.contactPhone;
            this.submitForm.ticketAddress = defaultTicketContact.address;
            this.submitForm.ticketAddressId = defaultTicketContact.addressId;
            this.submitForm.ticketContactId = defaultTicketContact.contact;
            this.tipStatus.multiTicketContact = '0';
          }
          this.remoteTicketSearch('', true);
          this.remoteOrderSearch('', true);
        }
      },
      immediate: true
    }
  },
  created() {
    this.debounceRemoteOrderSearch = debounce(this.remoteOrderSearch, 800);
    this.debounceRemoteTicketSearch = debounce(this.remoteTicketSearch, 800);
  },
  mounted() {
    getInvoiceTypeOptions().then((data) => {
      this.submitForm.invoiceTypeOptions = data;
    });
    getExpressCompanyOptions().then((data) => {
      this.submitForm.expressCompanyOptions = data;
    });
  },
  methods: {
    cancelPush() {
      this.onClose();
    },
    remoteTicketSearch(key, force) {
      if (key.trim() !== '' || force === true) {
        this.loading.ticketLoading = true;
        remoteContact(key, this.detailId)
          .then((res) => {
            if (res && res.status === 200 && res.datas && res.datas.length) {
              this.submitForm.ticketContact = [contactHeader, ...res.datas];
            } else {
              this.submitForm.ticketContact = [];
            }
          })
          .catch((err) => {
            this.$message.error(err.msg || err.message || '查询失败！');
            this.submitForm.ticketContact = [];
          })
          .finally(() => {
            this.loading.ticketLoading = false;
          });
      }
    },
    remoteOrderSearch(key, force) {
      if (key.trim() !== '' || force === true) {
        this.loading.orderLoading = true;
        remoteContact(key, this.detailId)
          .then((res) => {
            if (res && res.status === 200 && res.datas && res.datas.length) {
              this.submitForm.orderContact = [contactHeader, ...res.datas];
            } else {
              this.submitForm.orderContact = [];
            }
          })
          .catch((err) => {
            this.$message.error(err.msg || err.message || '查询失败！');
            this.submitForm.orderContact = [];
          })
          .finally(() => {
            this.loading.orderLoading = false;
          });
      }
    },
    ticketContactNameChange(contact) {
      if (!contact) {
        this.submitForm.ticketContactPhone = '';
        this.submitForm.ticketAddress = '';
        this.submitForm.ticketAddressId = '';
        this.submitForm.ticketContactName = '';
        this.submitForm.ticketContactId = '';
        return;
      }
      if (this.tipStatus.multiTicketContact !== '0') {
        this.tipStatus.multiTicketContact = '0';
      }
      const ticketSelected = this.submitForm.ticketContact.filter(
        (ti) => ti.contact === contact
      );
      if (ticketSelected && ticketSelected[0]) {
        this.submitForm.ticketContactPhone = ticketSelected[0].contactPhone;
        this.submitForm.ticketAddress = ticketSelected[0].address;
        this.submitForm.ticketAddressId = ticketSelected[0].addressId;
        this.submitForm.ticketContactName = ticketSelected[0].contactName;
        this.submitForm.ticketContactId = ticketSelected[0].contact;
      }
    },
    orderContactNameChange(contact) {
      if (!contact) {
        this.submitForm.orderContactPhone = '';
        this.submitForm.orderContactName = '';
        this.submitForm.orderContactId = '';
        return;
      }
      if (this.tipStatus.multiOrderContact !== '0') {
        this.tipStatus.multiOrderContact = '0';
      }
      const orderSelected = this.submitForm.orderContact.filter(
        (ti) => ti.contact === contact
      );
      if (orderSelected && orderSelected[0]) {
        this.submitForm.orderContactPhone = orderSelected[0].contactPhone;
        this.submitForm.orderContactName = orderSelected[0].contactName;
        this.submitForm.orderContactId = orderSelected[0].contact;
      }
    },
    checkboxChange1(val) {
      let tipStatus = this.tipStatus;
      if (!Array.isArray(val) || val.some((v) => v.toUpperCase === undefined)) {
        return;
      }
      val.forEach((val) => {
        let status = 'multi' + val.toUpperCase()[0] + val.slice(1);
        if (tipStatus[status] !== '0') {
          this.tipStatus[status] = '0';
        }
      });
      try {
        const reg = /\s+/;
        // let _financialNote = this._financialNote
        let financialNote = this.submitForm.financialNote;
        let _customerRefNum = this._customerRefNum;
        if (~val.indexOf('includeCustomerRefNum')) {
          if (_customerRefNum) {
            if (this.submitForm.financialNote) {
              this.submitForm.financialNote =
                this.submitForm.financialNote + ' ' + _customerRefNum;
            } else {
              this.submitForm.financialNote = _customerRefNum;
            }
          }
        } else {
          if (financialNote === _customerRefNum) {
            this.submitForm.financialNote = '';
          } else if (reg.test(financialNote)) {
            this.submitForm.financialNote = financialNote.split(/\s+/)[0];
          }
        }
      } catch (err) {
        console.log(err);
      }
    },
    checkboxChange(val) {
      if (!val.length) return;
      let tipStatus = this.tipStatus;
      if (!Array.isArray(val) || val.some((v) => v.toUpperCase === undefined)) {
        return;
      }
      val.forEach((val) => {
        let status = 'multi' + val.toUpperCase()[0] + val.slice(1);
        if (tipStatus[status] !== '0') {
          this.tipStatus[status] = '0';
        }
      });
    },
    handleInputChange(val, maxlength, field) {
      if (val.length > maxlength) {
        this.$message.info(`最多只能输入${maxlength}个字符`);
        this.$set(this.submitForm, field, val.slice(0, maxlength));
      }
    },
    validateSubmit() {
      let ret = true;
      const validList = [
        { prop: 'invoiceType', label: '开票类型' },
        { prop: 'ticketContactName', label: '收票联系人' },
        { prop: 'ticketContactPhone', label: '收票联系人电话' },
        { prop: 'ticketAddress', label: '收票地址' },
        { prop: 'orderContactName', label: '订单联系人' },
        { prop: 'orderContactPhone', label: '订单联系人电话' }
      ];
      if (
        this.submitForm &&
        this.submitForm.dnTypes &&
        this.submitForm.dnTypes.indexOf('LR') !== -1
      ) {
        if (
          !this.submitForm.originalInvoiceDraftNum &&
          this.checkbox.indexOf('returnOffset') === -1
        ) {
          this.$message.error('原发票草稿号不能为空！');
          return false;
        }
      }
      for (let item of validList) {
        if (!this.submitForm[item.prop]) {
          this.$message.error(`${item.label}不能为空！`);
          return false;
        }
      }
      if (
        this.submitForm.shippingInfo &&
        this.submitForm.shippingInfo.length > 30
      ) {
        this.$message.error('寄票备注最多30个字符！');
        return false;
      }
      if (
        this.submitForm.originalInvoiceDraftNum &&
        this.submitForm.originalInvoiceDraftNum.length > 50
      ) {
        this.$message.error('原发票草稿号最多50个字符！');
        return false;
      }
      if (
        this.submitForm.espRefVoucherNum &&
        this.submitForm.espRefVoucherNum.length > 16
      ) {
        this.$message.error('ESP参考凭证号最多16个字符！');
        return false;
      }
      if ((this.submitForm.financialNote || '').length > 100) {
        this.$message.error('发票备注总字数不能超过100个字符！');
        return false;
      }
      return ret;
    },
    async pushSapSubmit() {
      if (!this.validateSubmit()) return;
      this.loading.submitLoading = true;
      let opts = this.checkboxOptions
        .concat(this.checkboxOptions1)
        .map((opt) => opt.value);
      let checkbox = this.checkbox.concat(this.checkbox1);
      opts.forEach((item) => {
        if (~checkbox.indexOf(item)) {
          this.submitForm[item] = 'X';
        } else {
          this.submitForm[item] = null;
        }
      });
      const submitParams = {
        ...this.submitForm,
        ids: this.checkResult.datas.ids
      };
      Object.keys(submitParams).forEach((key) => {
        if (/multi/gi.test(key)) {
          delete submitParams[key];
        }
      });
      delete submitParams['invoiceTypeOptions'];
      delete submitParams['expressCompanyOptions'];
      delete submitParams['invoiceTypeName'];
      delete submitParams['orderContact'];
      delete submitParams['ticketContact'];
      delete submitParams['dnTypes'];
      delete submitParams['defaultTicketContact'];
      delete submitParams['defaultOrderContact'];
      delete submitParams['customerRefNum'];

      if (submitParams['ticketContactId']) {
        submitParams['ticketContact'] = submitParams['ticketContactId'];
        delete submitParams['ticketContactId'];
      }
      if (submitParams['orderContactId']) {
        submitParams['orderContact'] = submitParams['orderContactId'];
        delete submitParams['orderContactId'];
      }
      console.log(submitParams);
      try {
        let submitResult = await pushSapSubmit(submitParams);
        if (submitResult.status === 200) {
          this.$message.success('提交成功！');
          this.onSuccess(submitResult);
        } else {
          this.$message.error(
            submitResult.msg || submitResult.message || '提交失败！'
          );
        }
      } catch (err) {
        this.$message.error(err.msg || err.message || '提交失败！');
      } finally {
        setTimeout(() => {
          this.loading.submitLoading = false;
        }, 100);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.push-sap-form {
  .el-autocomplete,
  .el-input,
  .el-select {
    width: 100%;
  }
  .push-sap-button {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  .step2-checkbox {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
  }
  .diff-icon {
    margin-left: 50px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    i {
      color: orangered;
      margin-right: 10px;
      margin-left: 10px;
    }
  }
}
.ba-row-start {
  display: flex;
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
    overflow: auto;
  }
}
</style>
