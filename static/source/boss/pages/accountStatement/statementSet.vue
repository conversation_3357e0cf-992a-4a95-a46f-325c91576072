<template>
  <div class="statement-set-container">
    <DividerHeader>对账单信息</DividerHeader>
    <div class="statement-set-block">
      <div class="statement-set-block-title">
        <div>
          <span v-show="!isNameEditing">
            <span>{{ customerStatementDTO.name }}</span>
            <el-button class="ml-10" @click="handleEditName" type="primary">编辑</el-button>
          </span>
          <span v-show="isNameEditing" class="flex">
            <el-input style="min-width: 300px;" v-model="newStatementName" />
            <el-button class="ml-10" @click="handleSaveName" type="primary">保存</el-button>
            <el-button class="ml-10" @click="handleCancelEditName">取消</el-button>
          </span>
        </div>
        <div>
          <el-button
            v-if="this.customerStatementDTO.status == '2'"
            type="primary" @click="onlineConfirm">在线确认</el-button>
          <el-button
            v-if="this.customerStatementDTO.status == '3' && this.customerStatementDTO.cancel == '1'"
            type="primary" @click="onlineCancel">取消</el-button>
          <el-button
            v-if="
              customerStatementDTO.status === '1' &&
                (customerStatementDTO.auditStatus === '1' ||
                  customerStatementDTO.auditStatus === '3')
            "
            type="primary"
            plain
            @click="comeIntoEffect"
            >提交生效</el-button
          >
          <el-button
            v-if="customerStatementDTO.status === '2'"
            type="primary"
            plain
            @click="backToDraft"
            >退回草稿</el-button
          >

          <el-button type="primary" plain @click="exportStatement"
            >导出对账单</el-button
          >
          <el-button
            v-if="customerStatementDTO.status === '1'"
            type="warning"
            @click="delStatement"
            >删除</el-button
          >
        </div>
      </div>
      <div class="statement-set-block-item">
        <el-row>
          <!-- <el-col :span="8">对账单编号：{{ customerStatementDTO.no }}</el-col> -->
          <el-col :span="8"
            >对账日期：{{ customerStatementConfigDTO.checkTime }}</el-col
          >
          <el-col :span="8"
            >状态：{{
              getStatusLabelByValue(customerStatementDTO.status)
            }}</el-col
          >
        </el-row>
        <el-row>
          <el-col :span="8"
            >创建者：{{ customerStatementDTO.createdBy }}</el-col
          >
          <el-col :span="8"
            >客服：{{ customerStatementDTO.customerService }}</el-col
          >
          <el-col :span="8">销售：{{ customerStatementDTO.sales }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="8"
            >创建时间：{{ customerStatementDTO.gmtCreate }}</el-col
          >
          <el-col :span="8"
            >确认时间：{{ customerStatementDTO.confirmTime }}</el-col
          >
          <el-col :span="8"></el-col>
        </el-row>
      </div>
    </div>
    <DividerHeader>客户信息</DividerHeader>
    <div class="statement-set-block">
      <div class="statement-set-block-title">
        <div>
          <span
            v-if="customerStatementDTO.customerName"
            style="margin-right:40px;"
            >{{ customerStatementDTO.customerName }}</span
          >
          <span>{{ customerStatementDTO.customer }}</span>
        </div>
      </div>
    </div>
    <DividerHeader>对账明细信息</DividerHeader>
    <div class="detail-search-part">
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right">
        <el-row>
          <el-col :span="14">
            <el-form-item label="订单号：" prop="soNo">
              <el-input
                v-model="searchForm.soNo"
                placeholder="支持客户订单号、销售订单号、外围系统订单号搜索，同时支持5000个单号，以空格分隔"
                clearable/></el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="产品编号：" prop="productSku">
              <el-input
                v-model="searchForm.productSku"
                placeholder="产品编号、客户物料号 均支持搜索，同时支持500个单号，以空格分隔"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="交货单号：" prop="basisNo">
              <el-input
                v-model="searchForm.basisNo"
                placeholder="同时支持5000个单号，以空格分隔"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="SAP同步状态：" prop="sapSyncStatusIds">
              <el-select
                v-model="searchForm.sapSyncStatusIds"
                clearable
                multiple
                placeholder="请选择"
              >
                <el-option
                  v-for="item in sapSyncOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7" style="text-align:right;">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="statementDetailLoading"
              @click="statementDetailSearch"
              >查询</el-button>
            <el-button icon="el-icon-search" @click="handleReset">重置</el-button>
            <el-button @click="checkAll" :loading="checkAllLoading">全选</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="statement-set-block">
      <div class="statement-set-detail-container">
        <!-- <div class="statement-set-detail-info">
          <span
            >对账含税总金额：{{
              fixedDecimalPlace2(customerStatementDTO.checkTotalAmountTax)
            }}</span>
          <span
            >对账未税总金额：{{
              fixedDecimalPlace2(customerStatementDTO.checkTotalAmount)
            }}</span>
          <span
            >盈亏含税总金额：{{
              fixedDecimalPlace2(customerStatementDTO.profitLossTotalAmountTax)
            }}</span>
          <span
            >核销含税总金额：{{
              fixedDecimalPlace2(
                customerStatementDTO.verificationTotalAmountTax
              )
            }}</span>
            <span
            >税额：{{
              customerStatementDTO.taxTotalAmount
                ? fixedDecimalPlace2(customerStatementDTO.taxTotalAmount)
                : ""
            }}</span>
        </div> -->
        <div class="statement-set-detail-action" v-if="this.customerStatementDTO.status == '3'">
          <el-button type="primary" @click="openPushSapDialog">推送SAP</el-button>
          <el-button icon="el-icon-s-unfold" @click="showColumnSet" circle></el-button>
        </div>
        <div class="statement-set-detail-action" v-if="isDraft">
          <el-switch
            disabled
            v-model="updateType"
            active-color="#13ce66"
            inactive-color="#409eff"
            active-text="量差"
            inactive-text="价差"
            active-value="1"
            inactive-value="2"
            class="switch-btn-first"
          >
          </el-switch>
          <el-button type="primary" @click="openModifyInvoiceDialog(234)" :disabled="this.changeStatementObj.checkModify">修改单价</el-button>
          <el-button type="primary" @click="openModifyInvoiceDialog(235)" :disabled="this.changeStatementObj.checkModify">修改折扣</el-button>
          <el-button type="primary" @click="openModifyInvoiceDialog(236)" :disabled="this.changeStatementObj.checkModify">修改收票抬头</el-button>
          <el-button type="primary" @click="openModifyInvoiceDialog(237)">修改客户信息</el-button>
          <el-button type="primary" @click="openSplitDialog">拆分对账单</el-button>
          <el-button type="primary" @click="openAddStatementRow">新增行</el-button>
          <el-button type="primary" plain @click="delStatementRow">删除行</el-button>
          <el-button icon="el-icon-s-unfold" @click="showColumnSet" circle></el-button>
        </div>
        <div class="statement-set-detail-info">
          <span
            >对账含税总金额：{{
              fixedDecimalPlace2(customerStatementDTO.checkTotalAmountTax)
            }}</span>
          <span
            >对账未税总金额：{{
              fixedDecimalPlace2(customerStatementDTO.checkTotalAmount)
            }}</span>
          <span
            >盈亏含税总金额：{{
              fixedDecimalPlace2(customerStatementDTO.profitLossTotalAmountTax)
            }}</span>
          <span
            >核销含税总金额：{{
              fixedDecimalPlace2(
                customerStatementDTO.verificationTotalAmountTax
              )
            }}</span>
            <span
            >税额：{{
              customerStatementDTO.taxTotalAmount
                ? fixedDecimalPlace2(customerStatementDTO.taxTotalAmount)
                : ""
            }}</span>
        </div>
      </div>
      <div class="statement-set-detail-table-container">
        <zkh-table
          ref="detailTable"
          :selectionChange="selectionChange"
          :selectable="isDraft"
          :loading="detailListLoading"
          :columnEditable="true"
          :data="customerStatementDetailDTOs"
          :columnsConfig="columnsConfigDeepCopy"
          :defaultColumnsConfig="defaultColumnsConfig"
          :visible.sync="columnsSetVisible"
          :columns="columns"
          :height="500"
          @columnSet="columnSet">
          <template v-slot:leftCols>
            <el-table-column
              v-for="(col, index) in leftColumns"
              :key="index"
              :label="col.label"
              :min-width="col.minWidth"
              align="center"
              :prop="col.prop"
            />
            <el-table-column min-width="140px" align="center" label="对账数量">
              <template slot-scope="{ row }">
                <span v-if="!isDraft || updateType == '2'">
                  {{ row.checkNumber | fixedDecimalPlace3 }}
                </span>
                <el-input-number
                  v-if="updateType == '1' && isDraft"
                  style="width: 100px"
                  :precision="3"
                  :controls="false"
                  v-model="row.checkNumber"
                  :maxlength="30"
                  clearable
                  @change="newValue => { changeRowField(newValue, row, 'checkNumber')}"
                />
              </template>
            </el-table-column>
            <el-table-column min-width="120px" align="center" label="对账金额（含税）">
              <template slot-scope="{ row }">
                <span v-if="!isDraft || updateType == '1'">
                  {{row.checkAmountTax | fixedDecimalPlace2}}
                </span>
                <div v-else placement="bottom" trigger="click">
                  <el-input-number
                    :precision="2"
                    :controls="false"
                    style="width: 100px"
                    :value="row.checkAmountTax"
                    clearable
                    :maxlength="30"
                    @change=" newValue => { changeRowField(newValue, row, 'checkAmountTax') }"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column min-width="120px" align="center" label="税额">
              <template slot-scope="{ row }">
                <span v-if="!isDraft">
                  {{row.tax | fixedDecimalPlace2}}
                </span>
                <div v-else placement="bottom" trigger="click">
                  <el-input-number
                    :precision="2"
                    style="width: 100px"
                    :controls="false"
                    :value="row.tax"
                    clearable
                    :maxlength="30"
                    @change=" newValue => { changeRowField(newValue, row, 'taxUpdateValue', 3) }"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column min-width="120px" align="center" label="对账金额（未税）">
              <template slot-scope="{ row }">
                <span v-if="!isDraft || updateType == '1'">
                  {{row.checkAmount | fixedDecimalPlace2}}
                </span>
                <div v-else placement="bottom" trigger="click">
                  <el-input-number
                    :precision="2"
                    style="width: 100px"
                    :controls="false"
                    :value="row.checkAmount"
                    clearable
                    :maxlength="30"
                    @change=" newValue => { changeRowField(newValue, row, 'checkAmount') }"
                  />
                </div>
              </template>
            </el-table-column>
          </template>
          <template v-slot:[profitLossNumber]="{ row }">
            <span v-if="!isDraft || updateType == '2'">
              {{ row.profitLossNumber | fixedDecimalPlace3 }}
            </span>
            <el-input-number
              v-if="updateType == '1' && isDraft"
              style="width: 100px"
              :precision="3"
              :controls="false"
              v-model="row.profitLossNumber"
              :maxlength="30"
              clearable
              @change=" newValue => { changeRowField(newValue, row, 'profitLossNumber') }"
            />
          </template>
          <template v-slot:[profitLossAmount]="{ row }">
            <span v-if="!isDraft || updateType == '1'">
              {{
                (isInclusiveTax(row.conditionType)
                  ? row.profitLossAmountTax
                  : row.profitLossAmount) | fixedDecimalPlace2
              }}
            </span>
            <div v-else>
              <el-input-number
                style="width: 100px"
                :precision="2"
                :controls="false"
                v-if="isInclusiveTax(row.conditionType)"
                :value="row.profitLossAmountTax"
                :maxlength="30"
                clearable
                @change=" newValue => { changeRowField(newValue, row, 'profitLossAmountTax') }"
              />
              <el-input-number
                style="width: 100px"
                :precision="2"
                :controls="false"
                v-if="!isInclusiveTax(row.conditionType)"
                :value="row.profitLossAmount"
                :maxlength="30"
                clearable
                @change="newValue => { changeRowField(newValue, row, 'profitLossAmount')}"
              />
            </div>
          </template>
          <template v-slot:[comment]="{ row }">
            <span v-if="!isDraft">
              {{ row.comment }}
            </span>
            <el-input
              v-else
              v-model="row.comment"
              type="textarea"
              :maxlength="255"
              show-word-limit
              :autosize="true"
              @change=" newValue => { changeRowField(newValue, row, 'comment') }"
            />
          </template>
          <template v-slot:[deliveryTotalAmount] = "{ row }">
            <span>
              {{
                (isInclusiveTax(row.conditionType)
                  ? row.deliveryTotalAmountTax
                  : row.deliveryTotalAmount) | fixedDecimalPlace2
              }}
            </span>
          </template>
          <template v-slot:[discountedTotalAmount] = "{ row }">
            <span>
              {{
                (isInclusiveTax(row.conditionType)
                  ? row.discountedTotalAmountTax
                  : row.discountedTotalAmount) | fixedDecimalPlace2
              }}
            </span>
          </template>
          <template v-slot:[verificationAmount] = "{ row }">
            <span>
              {{
                (isInclusiveTax(row.conditionType)
                  ? row.verificationAmountTax
                  : row.verificationAmount) | fixedDecimalPlace2
              }}
            </span>
          </template>
        </zkh-table>
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @pagination="getDetailList"
        />
      </div>
    </div>
    <el-dialog width="400px" title="修改单价" :visible.sync="changeStatementObj.modifyPriceVisible">
      <el-form label-width="100px" :model="modifyPriceObj" ref="modifyPriceForm" :rules="modifyPriceObj.rules">
        <el-form-item label="单价：" prop="price">
          <el-input v-model="modifyPriceObj.price" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注原因：" prop="reason">
          <el-cascader
            v-model="modifyPriceObj.reason"
            :options="modifyPriceObj.reasonOptions"
            placeholder="请选择"
            style="width: 260px"
            clearable>
          </el-cascader>
        </el-form-item>
      </el-form>
      <div class="split-statement">
        <el-button type="default" @click="cancelModifyVisible(234)">取消</el-button>
        <el-button type="primary" @click="confirmModifyPrice">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog width="400px" title="修改折扣" :visible.sync="changeStatementObj.modifyDiscountVisible">
      <el-form label-width="100px" :model="modifyDiscountObj" ref="modifyDiscountForm" :rules="modifyDiscountObj.rules">
        <el-form-item label="折扣类型：" prop="discountType">
          <el-select v-model="modifyDiscountObj.discountType" clearable>
            <el-option
              v-for="item in modifyDiscountObj.discountTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="折扣：" prop="discount">
          <el-input v-model="modifyDiscountObj.discount" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="备注原因：" prop="reason">
          <el-cascader
            v-model="modifyDiscountObj.reason"
            :options="modifyDiscountObj.reasonOptions"
            placeholder="请选择"
            style="width: 260px"
            clearable>
          </el-cascader>
        </el-form-item>
      </el-form>
      <div class="split-statement">
        <el-button type="default" @click="cancelModifyVisible(235)">取消</el-button>
        <el-button type="primary" @click="confirmModifyDiscount">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog width="400px" title="修改收票抬头" :visible.sync="changeStatementObj.modifyInvoiceVisible">
      <el-form label-width="120px" :model="modifyInvoiceObj" ref="modifyInvoiceForm" :rules="modifyInvoiceObj.rules">
        <el-form-item label="输入收票方：" prop="ids">
          <RemoteCustomer ref="customer" v-model="modifyInvoiceObj.ids" @change="changeInvoice" getValue :rowCount="100" :options="[{'customerNumber': '客户编码', 'customerName': '客户名称'}]" showItem />
        </el-form-item>
        <el-form-item label="客户名称：" prop="invoiceReceiver">
          <el-input v-model="modifyInvoiceObj.invoiceReceiver" clearable></el-input>
        </el-form-item>
        <el-form-item label="备注原因：" prop="reason">
          <el-cascader
            v-model="modifyInvoiceObj.reason"
            :options="modifyInvoiceObj.reasonOptions"
            placeholder="请选择"
            style="width: 240px"
            clearable>
          </el-cascader>
        </el-form-item>
      </el-form>
      <div class="split-statement">
        <el-button type="default" @click="cancelModifyVisible(236)">取消</el-button>
        <el-button type="primary" @click="confirmModifyInvoice">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog width="400px" title="修改客户信息" :visible.sync="changeStatementObj.modifyProductVisible">
      <el-form label-width="120px" :model="modifyProductObj" ref="modifyProductForm" :rules="modifyProductObj.rules">
        <el-form-item label="客户订单号：" prop="customerOrderNo">
          <el-input v-model="modifyProductObj.customerOrderNo" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户物料号：" prop="customerProductNo">
          <el-input v-model="modifyProductObj.customerProductNo" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户品名：" prop="customerProductName">
          <el-input v-model="modifyProductObj.customerProductName" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户规格型号：" prop="customerProductSpecifications">
          <el-input v-model="modifyProductObj.customerProductSpecifications" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="客户物料单位：" prop="customerProductUnit">
          <el-select v-model="modifyProductObj.customerProductUnit" placeholder="请输入" filterable reserve-keyword allow-create clearable :disabled="this.modifyProductObj.oilLines.length > 0">
            <el-option
              v-for="item in modifyProductObj.unitOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客户物料数量：" prop="customerProductNumber">
          <el-input v-model="modifyProductObj.customerProductNumber" placeholder="请输入" clearable :disabled="this.modifyProductObj.oilLines.length > 0"></el-input>
        </el-form-item>
        <p class="error-info" v-if="this.modifyProductObj.oilLines.length > 0">第{{this.modifyProductObj.oilLines.join(',')}}行是成品油，不支持修改客户物料数量和客户物料单位</p>
        <!-- <el-form-item v-if="modifyProductObj.content === 11" style="margin-left: 10px">
          <el-radio-group v-model="modifyProductObj.value" style="display: flex; justify-content: space-between">
              <el-radio :label="1">以主数据为准</el-radio>
              <el-radio :label="2">以订单为准</el-radio>
            </el-radio-group>
        </el-form-item> -->
      </el-form>
      <div class="split-statement">
        <el-button type="default" @click="cancelModifyVisible(237)">取消</el-button>
        <el-button type="primary" @click="confirmModifyProduct">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog width="400px" title="拆分对账单" :visible.sync="splitStatementObj.splitStatementVisible" :show-close="false">
      <el-form :model="splitStatementObj" ref="splitStatementForm" :inline="true" label-width="120px" :rules="splitStatementObj.rules">
        <el-form-item label="是否新对账单：" prop="isNewStatement">
          <el-select
            @change="statementNameChange"
            v-model="splitStatementObj.isNewStatement"
            placeholder="请选择"
            clearable>
            <el-option label="是" value="1"></el-option>
            <el-option label="否" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="splitStatementObj.isNewStatement!='1'" label="对账单名称：" prop="statementName">
          <el-select
            v-model="splitStatementObj.statementName"
            filterable
            remote
            clearable
            reserve-keyword
            placeholder="请输入"
            :remote-method="remoteStatement"
            :loading="splitStatementObj.remoteStatementLoading">
            <el-option
              v-for="item in splitStatementObj.statementList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-else label="对账单名称：" prop="newName">
          <el-input
            v-model="splitStatementObj.newName"
            clearable
          />
        </el-form-item>
      </el-form>
      <div class="split-statement">
        <el-button type="default" @click="cancelSplit">取消</el-button>
        <el-button type="primary" :loading="splitStatementObj.submitLoading" @click="splitStatement">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog width="1200px" title="请勾选要生成对账单明细的对账依据" :visible.sync="addStatementRowObj.visible" :show-close="false">
      <BasisList
        ref="BasisList"
        noSearchBg
        diableInitialFetchList
        hideTableHeader
        showHeaderCheckAll
        hideCustomer
        />
      <div class="split-statement" style="justify-content: flex-end; margin-right: 20px;">
        <el-button type="default" @click="cancelAddStatementRow">取消</el-button>
        <el-button type="primary" :loading="addStatementRowObj.submitLoading" @click="addStatementRow">确认创建</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="1020px"
      class="push-sap-dialog"
      title="推送SAP发票信息"
      :visible.sync="pushSapVisible"
      :show-close="true"
      :before-close="handlebeforeClose"
      :close-on-click-modal="false">
      <PushSap
        ref="pushSap"
        :id="customerStatementDTO.id"
        :detailId="pushSapDetailedId"
        :customerNumber="customerStatementDTO.customer"
        @refresh-statement-detail="refreshStatementDetail"
        @close-push-sap-dialog="closePushSapDialog" />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import BasisList from './basisList'
import getColumns, { leftColumns } from './detailSetColumns.js'
import PushSap from './pushSAPComponent'
import {
  getStatementStatusOptions,
  getStatementOptions,
  statementSplit,
  getStatementDetail,
  backToDraft,
  comeIntoEffect,
  delStatement,
  exportStatement,
  getCustomerStatementDetail,
  beforeUpdateStatementDetailRow,
  updateStatementDetailRow,
  currencySignList,
  manualCreateApi,
  manualDeleteApi,
  getSapSyncOptions,
  onlineConfirmApi,
  onlineCancelApi,
  updateStatementName,
  findSubOptionSetByType,
  searchClients,
  invoiceReceiverModify,
  unitPriceUpdate,
  materialModify,
  discountUpdate,
  selectDetailIds,
  getUnit
} from '@/api/accountStatement.js'
import {
  createdConfigByColumns,
  formatByConfig,
  getConfig,
  cachedConfig,
  checkColumnsConfig
  // removeConfig
} from '@/components/ZkhTable/columnConfigTransformation.js'
import DividerHeader from '@/components/DividerHeader'
import { getLabelByValue, debounce } from '@/utils/index.js'
import { fixedDecimalPlace } from '@/filters/index.js'
// import { downloadByLink, isValidLink } from '@/utility/request'
import { RemoteCustomer } from '@kun-ui/remote-customer'
const fixedDecimalPlace2 = fixedDecimalPlace.bind(null, 2)
const fixedDecimalPlace3 = fixedDecimalPlace.bind(null, 3)
// 新增编辑公用
export default {
  name: 'AccountSet',
  components: {
    Pagination,
    DividerHeader,
    BasisList,
    PushSap,
    RemoteCustomer
  },
  data () {
    return {
      profitLossNumber: 'profitLossNumber',
      profitLossAmount: 'profitLossAmount',
      comment: 'comment',
      deliveryTotalAmount: 'deliveryTotalAmount',
      discountedTotalAmount: 'discountedTotalAmount',
      verificationAmount: 'verificationAmount',
      columnsConfigDeepCopy: [],
      columnsConfig: [],
      columnsSetVisible: false,
      pushSapVisible: false,
      splitStatementObj: {
        rules: {
          isNewStatement: [
            {
              required: true,
              message: '请选择是否新对账单'
            }
          ],
          statementName: [
            {
              required: true,
              message: '对账单名称不能为空'
            }
          ]
        },
        isNewStatement: '',
        statementName: '',
        no: '',
        newName: '',
        otherNo: '',
        detailNos: [],
        lines: [],
        splitStatementVisible: false, // 拆分对账单
        statementList: [],
        remoteStatementLoading: false,
        remoteSearchName: '',
        submitLoading: false
      },
      addStatementRowObj: {
        visible: false,
        rules: [],
        submitLoading: false
      },
      fileSizeLimit: 10, // 上传文件大小限制，单位 MB
      statementStatusOptions: [], // 对账单状态 下拉选项  '1' 草稿， '2' 对账中， '3' 对账完成
      fieldList: [], // 模板类型对应的维度列表
      customerDTO: {}, // 对账单客户属性
      customerStatementConfigDTO: {}, // 对账配置
      customerStatementDTO: {}, // 对账单属性
      customerStatementDetailDTOs: [], // 对账单明细
      updateType: '1', // 对账单详情修改类型 1/2=数量/金额
      rules: {},
      detailListLoading: false,
      columns: [],
      sapSyncOptions: [],
      leftColumns: leftColumns,
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      detailList: [],
      total: 0,
      searchForm: {},
      haveAuth: 1,
      statementDetailLoading: false,
      isNameEditing: false,
      newStatementName: '',
      changeStatementObj: {
        modifyInvoiceVisible: false, // 修改收票抬头弹窗
        modifyPriceVisible: false, // 修改单价弹窗
        modifyDiscountVisible: false, // 修改折扣弹窗
        modifyProductVisible: false, // 修改客户物料信息弹窗
        detailNos: [], // 选择的对账单明细集合
        checkModify: false // ZRE1类型的订单不可修改
      },
      modifyPriceObj: {
        price: '',
        reason: '',
        reasonOptions: [], // 备注原因下拉框
        rules: {
          price: [
            {
              required: true,
              message: '请填写单价'
            },
            { validator: this.valPrice, trigger: 'blur' }
          ],
          reason: [
            {
              required: true,
              message: '请选择备注原因'
            }
          ]
        }
      },
      modifyDiscountObj: {
        discountType: '',
        discount: '',
        reason: '',
        discountTypeOptions: [], // 折扣类型下拉框
        reasonOptions: [], // 备注原因下拉框
        rules: {
          discountType: [
            {
              required: true,
              message: '请选择折扣类型'
            }
          ],
          discount: [
            {
              required: true,
              message: '请填写折扣'
            },
            { validator: this.valDiscount, trigger: 'blur' }
          ],
          reason: [
            {
              required: true,
              message: '请选择备注原因'
            }
          ]
        }
      },
      modifyInvoiceObj: {
        ids: '', // 修改收票抬头客户编码
        invoiceReceiver: '', // 修改收票抬头客户名称
        reason: '', // 修改收票抬头备注原因
        idOptions: [], // 客户编码下拉框
        reasonOptions: [], // 备注原因下拉框
        rules: {
          ids: [
            {
              required: true,
              message: '请填写客户编码'
            }
          ],
          invoiceReceiver: [
            {
              required: true,
              message: '请填写客户名称'
            }
          ],
          reason: [
            {
              required: true,
              message: '请选择备注原因'
            }
          ]
        },
        isLoading: false,
        current: 1,
        preKeyWords: ''
      },
      modifyProductObj: {
        unitOptions: [], // 物料单位下拉框
        customerOrderNo: '',
        customerProductNo: '',
        customerProductName: '',
        customerProductSpecifications: '',
        customerProductUnit: '',
        customerProductNumber: '',
        rules: {
          customerProductUnit: [
            { validator: this.valProductUnit, trigger: 'blur' }
          ],
          customerProductNumber: [
            { validator: this.valProductNumber, trigger: 'blur' }
          ]
        },
        oilLines: []
      },
      checkAllLoading: false,
      checkedAllFlag: false, // 是否全选
      checkedAllData: '' // 全选数据
    }
  },
  filters: {
    fixedDecimalPlace2: fixedDecimalPlace2,
    fixedDecimalPlace3: fixedDecimalPlace3
  },
  created () {
    this.initData()
    this.initColumns()
    this.remoteSearchDebounced = debounce(() => {
      getStatementOptions(
        this.splitStatementObj.remoteSearchName,
        this.customerStatementDTO.customer,
        this.customerStatementDTO.no,
        this.customerStatementDTO.currency
      ).then(res => {
        if (res && res.datas) {
          this.splitStatementObj.statementList = res.datas.map(data => ({
            label: data.name,
            value: data.no
          }))
        }
      })
        .finally(() => {
          this.splitStatementObj.remoteStatementLoading = false
        })
    }, 600)
    getSapSyncOptions().then(data => {
      this.sapSyncOptions = data
    })
    this.initReason('234')
    this.initReason('236')
    this.getUnit()
  },
  computed: {
    pushSapDetailedId () {
      return this.customerStatementDetailDTOs &&
              this.customerStatementDetailDTOs[0] &&
              this.customerStatementDetailDTOs[0].id
    },
    isDraft () {
      return this.customerStatementDTO.status === '1'
    },
    currency () {
      return (
        getLabelByValue(this.customerStatementDTO.currency, currencySignList) ||
        '￥'
      )
    }
  },
  methods: {
    formatParams (param) {
      if (param.soNo && param.soNo.split) {
        let soNoList = param.soNo.split(/\s+|,|，/).filter(e => e)
        // if (soNoList.length > 5000) {
        //   this.$message.error('订单号最多支持5000个！')
        //   return false
        // }
        param.soNo = soNoList.join(',')
      }
      if (param.basisNo && param.basisNo.split) {
        let basisNoList = param.basisNo.split(/\s+|,|，/).filter(e => e)
        // if (basisNoList.length > 5000) {
        //   this.$message.error('交货单号最多支持5000个！')
        //   return false
        // }
        param.basisNo = basisNoList.join(',')
      }
      if (param.productSku && param.productSku.split) {
        let productSkuList = param.productSku.split(/\s+|,|，/).filter(e => e)
        if (productSkuList.length > 500) {
          this.$message.error('产品编号最多支持500个！')
          return false
        }
        param.productSku = productSkuList.join(',')
      }
      this.transArrayToJoin(param, 'sapSyncStatusIds')
      return true
    },
    transArrayToJoin (obj, prop) {
      if (Array.isArray(obj[prop])) {
        obj[prop] = obj[prop].filter(e => e).join(',')
      }
    },
    statementDetailSearch () {
      let valid = this.formatParams({ ...this.searchForm })
      if (!valid) return
      this.statementDetailLoading = true
      this.getDetailList()
    },
    handleReset () {
      this.searchForm = {}
    },
    initColumns () {
      try {
        this.defaultColumnsConfig = createdConfigByColumns(getColumns())
        let columnsConfig = getConfig('statementDetailSet')

        if (columnsConfig && columnsConfig.length > 0) {
          const checkResult = checkColumnsConfig(
            this.defaultColumnsConfig,
            columnsConfig
          )
          columnsConfig = checkResult.config
          if (checkResult.changed) {
            console.log('checkResult.changed', checkResult.changed)
            cachedConfig('statementDetailSet', columnsConfig)
          }
          this.columnsConfig = columnsConfig
          this.columns = formatByConfig(this.columnsConfig, getColumns())
        } else {
          console.log('use default config')
          this.setDefaultCache(getColumns())
        }
      } catch (error) {
        this.setDefaultCache(getColumns())
      }
    },
    setDefaultCache (cols) {
      const config = createdConfigByColumns(cols)
      this.columnsConfig = config
      this.columns = cols
    },
    columnSet (newConfig) {
      console.log('set columnSet', newConfig)
      if (newConfig) {
        this.columnsConfig = newConfig
        this.columns = formatByConfig(this.columnsConfig, getColumns())
        cachedConfig('statementDetailSet', newConfig)
      }
      this.columnsSetVisible = false
    },
    showColumnSet () {
      this.columnsConfigDeepCopy = JSON.parse(
        JSON.stringify(this.columnsConfig)
      )

      this.columnsSetVisible = true
    },
    onlineConfirm () {
      this.$confirm('是否确认该对账单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then(_ => {
        const loading = this.$loading({
          target: '.statement-set-container'
        })
        onlineConfirmApi(this.customerStatementDTO.id)
          .then(res => {
            if (res && res.status === 200) {
              this.$message.success(res.msg || '操作成功！')
              this.initData()
            } else {
              this.$message.error(res.message || res.msg || '操作失败！')
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '确认失败！')
          })
          .finally(_ => {
            loading.close()
          })
      })
    },
    onlineCancel () {
      this.$confirm('是否取消该对账单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then(_ => {
        onlineCancelApi(this.customerStatementDTO.id)
          .then(res => {
            if (res && res.status === 200) {
              this.$message.success(res.msg || '操作成功！')
              this.initData()
            } else {
              this.$message.error(res.message || '该对账单正在开票，无法取消！')
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '取消失败！')
          })
      })
    },
    openPushSapDialog () {
      this.pushSapVisible = true
    },
    closePushSapDialog (close) {
      this.pushSapVisible = false
    },
    refreshStatementDetail() {
      this.initData()
    },
    handlebeforeClose (done) {
      this.$refs.pushSap.cancelPushSap && this.$refs.pushSap.cancelPushSap()
      console.log(this.$refs.pushSap.pushSapDialogData)
      done()
    },
    fixedDecimalPlace2 (val) {
      return this.currency + fixedDecimalPlace2(val)
    },
    // Z005 含税
    // Z010 未税
    // 是否含税
    isInclusiveTax (conditionType) {
      return conditionType === 'Z005'
    },
    // 有盈亏时备注不能为空
    rowCommentErrorCheck (row) {
      if (
        row.comment === '' ||
        row.comment === undefined ||
        row.comment === null
      ) {
        const amount = this.isInclusiveTax(row.conditionType)
          ? row.profitLossAmountTax
          : row.profitLossAmount
        if (Number(row.profitLossNumber)) {
          // 盈亏数量 非零
          return true
        } else if (Number(amount)) {
          // 盈亏金额 非零
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    },
    getStatusLabelByValue (status) {
      return getLabelByValue(status, this.statementStatusOptions)
    },
    initData () {
      this.columns = getColumns()
      this.initColumns()
      getStatementStatusOptions().then(data => {
        this.statementStatusOptions = data
      })
      this.accountId = this.$route.params.accountId
      getStatementDetail(this.accountId).then(res => {
        if (res.status === 200) {
          if (res.datas) {
            this.customerDTO = res.datas.customerDTO // 对账单客户属性
            this.customerStatementConfigDTO =
              res.datas.customerStatementConfigDTO // 对账配置
            this.customerStatementDTO = res.datas.customerStatementDTO // 对账单属性
            this.haveAuth = res.datas.haveAuth // 能否推送SAP
            // this.customerStatementDetailDTOs =
            //   res.datas.customerStatementDetailDTOs // 对账单明细
            this.getDetailList()
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },

    ok () {
      this.$refs['modelForm'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          if (this.isEdit) {
            this.editModel()
          } else {
            this.addModel()
          }
        } else {
        }
      })
    },
    // 导出对账单
    exportStatement () {
      exportStatement(this.customerStatementDTO.no)
        .then(res => {
          console.log(res)
          if (res?.status === 200) {
            this.$message.success('申请导出对账单成功，请在下载专区查看导出内容！')
            try {
              this.$closeTag('/purchaseReport/downLoadList')
            } catch {

            }
            setTimeout(() => {
              this.$router.push({
                path: '/purchaseReport/downLoadList'
              })
            }, 600)
          } else {
            this.$message.error(res.msg || res.message || '导出失败！')
          }
          // setTimeout(() => {
          //     this.$router.push({
          //       path: '/purchaseReport/downLoadList'
          //     })
          //   }, 600)
          // if (res && res.status === 200 && res.datas) {
          //   if (isValidLink(res.datas)) {
          //     downloadByLink(res.datas)
          //     this.$message.success('导出成功！')
          //   } else {
          //     this.$message.error('导出链接不合法！')
          //   }
          // } else {
          //   this.$message.error(res.msg || res.message || '导出失败！')
          // }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '导出失败！')
        })
    },
    // 提交生效
    comeIntoEffect () {
      this.$confirm('是否对该对账单提交生效', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        const loading = this.$loading({
          target: '.statement-set-container'
        })
        comeIntoEffect(this.customerStatementDTO.id)
          .then(res => {
            if (res.status === 200) {
              this.initData()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch(e => {
            let res = e.response ? e.response.data : {}
            this.$message.error(res.message || res.msg || res.error || e)
          })
          .finally(_ => {
            loading.close()
          })
      })
    },
    // 退回草稿
    backToDraft () {
      this.$confirm('是否将该对账单退回到草稿', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        const loading = this.$loading({
          target: '.statement-set-container'
        })
        backToDraft(this.customerStatementDTO.id)
          .then(res => {
            if (res.status === 200) {
              this.initData()
            } else {
              this.$message.error(res.message)
            }
          })
          .catch(e => {
            let res = e.response ? e.response.data : {}
            this.$message.error(res.message || res.msg || res.error || e)
          })
          .finally(_ => {
            loading.close()
          })
      })
    },
    // 删除对账单
    delStatement () {
      this.$confirm('是否要删除该对账单', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        delStatement(this.customerStatementDTO.id)
          .then(res => {
            if (res.status === 200) {
              this.$message.success('删除成功')
              setTimeout(() => {
                sessionStorage.setItem('statementReload', 1)
                this.$closeTag(this.$route.path)
              }, 2000)
            } else {
              this.$message.error(res.message)
            }
          })
          .catch(e => {
            let res = e.response ? e.response.data : {}
            this.$message.error(res.message || res.msg || res.error || e)
          })
      })
    },
    // 分页
    getDetailList () {
      let param = {
        no: this.customerStatementDTO.no,
        ...this.searchForm
      }
      this.formatParams(param)
      this.detailListLoading = true
      getCustomerStatementDetail({
        page: this.listQueryInfo.current,
        size: this.listQueryInfo.pageSize
      }, param)
        .then(res => {
          this.detailListLoading = false
          if (res.status === 200) {
            if (res.datas) {
              const data = res.datas
              this.total = data.total
              this.customerStatementDetailDTOs = data.list
            }
            this.initModify()
          } else {
            this.$message.error(res.message)
          }
        })
        .finally(() => {
          this.detailListLoading = false
          this.statementDetailLoading = false
        })
    },
    // changeMoney (newValue, row, key) {
    //   row[key] = newValue
    // },
    // 修改原因
    changeRowField (newValue, row, key, updateType) {
      if (newValue === '') {
        return
      }
      console.log(newValue, key)
      if (newValue <= 0 && key === 'checkNumber') {
        return this.$message.error('对账数量必须大于0！')
      }
      row.reason = newValue
      let param = {
        checkAmount: row.checkAmount,
        checkAmountTax: row.checkAmountTax,
        checkNumber: row.checkNumber,
        comment: row.comment,
        detailNo: row.no,
        no: this.customerStatementDTO.no,
        profitLossAmount: row.profitLossAmount,
        profitLossAmountTax: row.profitLossAmountTax,
        profitLossNumber: row.profitLossNumber,
        updateType: updateType || this.updateType
      }
      row[key] = newValue
      param[key] = newValue
      beforeUpdateStatementDetailRow(param).then(res => {
        if (res.message && res.status !== 200) {
          this.$confirm(res.message, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            updateStatementDetailRow(param).then(res => {
              if (res.status === 200) {
                this.$message.success(res.message)
                this.initData()
              } else {
                this.customerStatementDetailDTOs = [];
                this.getDetailList()
                this.$message.error(res.message || res.error)
              }
            })
          }).catch(() => {
            this.customerStatementDetailDTOs = [];
            this.getDetailList()
          })
        } else {
          this.$message.success(res.message)
          this.initData()
        }
      })
    },
    remoteStatement (val) {
      if (val && val.trim() !== '') {
        this.splitStatementObj.remoteStatementLoading = true
        this.splitStatementObj.remoteSearchName = val.trim()
        this.remoteSearchDebounced()
      }
    },
    // 判断某些行能不能选
    // checkSelectable (row) {
    //   // 订单类型=ZRE1的对账单明细无法选中多选框
    //   if (row.soType === 'ZRE1') {
    //     return false
    //   } else {
    //     return true
    //   }
    // },
    selectionChange (lines) {
      if (lines && lines.length > 0) {
        this.modifyInvoiceObj.ids = lines[0].invoiceReceiver
        // this.remoteInvoice(this.modifyInvoiceObj.ids)
        this.modifyProductObj.customerOrderNo = lines[0].customerOrderNo
        this.modifyProductObj.customerProductName = lines[0].customerProductName
        this.modifyProductObj.customerProductNo = lines[0].customerProductNo
        this.modifyProductObj.customerProductNumber = lines[0].customerProductNumber
        this.modifyProductObj.customerProductSpecifications = lines[0].customerProductSpecifications
        this.modifyProductObj.customerProductUnit = lines[0].customerProductUnit
        this.modifyInvoiceObj.invoiceReceiver = lines[0].customerName
        // this.remoteInvoice(this.modifyInvoiceObj.ids)
      }
      this.checkedAllFlag = false;
      this.checkedAllData = []
      this.splitStatementObj.detailNos = lines.map(line => line.no)
      this.splitStatementObj.lines = lines
      this.changeStatementObj.detailNos = lines.map(line => line.id)
      const temp = []
      lines.map(line => {
        if (line.refinedOil === '1') {
          this.customerStatementDetailDTOs.map((value, index) => {
            if (value.id === line.id) {
              temp.push(index + 1)
            }
          })
        }
      })
      this.modifyProductObj.oilLines = temp.sort();
      this.changeStatementObj.checkModify = lines.some(line => line.soType === 'ZRE1')
      if (this.changeStatementObj.checkModify) {
        const csNums = lines.filter(line => line.soType === 'ZRE1').map(item => item.no).join(',')
        this.$message.error(`对账单明细${csNums}为ZRE1-退货销售订单，不能修改单价/折扣/收票抬头!`)
      }
    },
    // 打开修改收票抬头弹窗
    openModifyInvoiceDialog (val) {
      if (!this.changeStatementObj.detailNos.length) return this.$message.error('请选择对账单明细行！')
      switch (val) {
        case 234:
          this.changeStatementObj.modifyPriceVisible = true
          break;
        case 235:
          this.changeStatementObj.modifyDiscountVisible = true
          break;
        case 236:
          this.changeStatementObj.modifyInvoiceVisible = true
          break;
        case 237:
          this.changeStatementObj.modifyProductVisible = true
          break;
      }
    },
    // 备注原因
    initReason(val) {
      let temp = []
      findSubOptionSetByType({ parentId: val, status: 1, subTypeCode: 'OpReason' }).then(res => {
        if (res && res.status === 200) {
          temp = res.datas.map(data => ({
            label: data.displayName,
            value: data.value
          }))
          temp.map(data => {
            findSubOptionSetByType({ parentId: Number(data.value) + 258, status: 1, subTypeCode: 'OpReasonSub' }).then(res => {
              if (res && res.status === 200) {
                const child = res.datas.map(val => ({
                  label: val.displayName,
                  value: val.value
                }))
                temp = Object.assign(data, { children: child })
              }
            })
          })
        }
        if (val === '234') {
          this.modifyPriceObj.reasonOptions = temp
          this.modifyDiscountObj.reasonOptions = temp
        } else {
          this.modifyInvoiceObj.reasonOptions = temp
        }
      })
    },
    // 打开修改弹窗设置默认值
    initModify() {
      findSubOptionSetByType({ parentId: 235, status: 1, subTypeCode: 'OpAttribute' }).then(res => {
        if (res && res.status === 200) {
          this.modifyDiscountObj.discountTypeOptions = res.datas.map(data => ({
            label: data.displayName,
            value: data.displayOrder
          }))
          if (!this.modifyDiscountObj.discountType) {
            this.modifyDiscountObj.discountType = this.modifyDiscountObj.discountTypeOptions[0].value
          }
        }
      })
      // this.modifyInvoiceObj.ids = this.customerStatementDetailDTOs[0].invoiceReceiver
      // this.remoteInvoice(this.modifyInvoiceObj.ids)
      // this.modifyInvoiceObj.invoiceReceiver = this.customerStatementDetailDTOs[0].customerName
      findSubOptionSetByType({ parentId: 237, status: 1, subTypeCode: 'OpAttribute' }).then(res => {
        if (res && res.status === 200) {
          this.modifyProductObj.contentOptions = res.datas.map(data => ({
            label: data.displayName,
            value: data.displayOrder
          }))
        }
      })
    },
    // 关闭修改弹窗
    cancelModifyVisible (val) {
      switch (val) {
        case 234:
          this.changeStatementObj.modifyPriceVisible = false
          break;
        case 235:
          this.changeStatementObj.modifyDiscountVisible = false
          break;
        case 236:
          this.changeStatementObj.modifyInvoiceVisible = false
          break;
        case 237:
          this.changeStatementObj.modifyProductVisible = false
          break;
      }
    },
    // changeInvoiceReason (val) {
    //   console.log(val);
    //   this.modifyInvoiceObj.reason = val[val.length - 1]
    // },
    getUnit() {
      const params = {
        listKey: 'customerQuantityUnit',
        size: -1
      }
      getUnit(params).then(res => {
        if (res && res.code === 200) {
          this.modifyProductObj.unitOptions = res.data.records
        }
      })
    },
    // 修改单价
    confirmModifyPrice () {
      this.$refs['modifyPriceForm'].validate(valid => {
        if (!valid) return
        if (this.checkedAllFlag) {
          if (this.checkAllLoading) return this.$message.error('请先完成全选！');
        }
        const params = {
          detailIds: this.checkedAllFlag ? this.checkedAllData : this.changeStatementObj.detailNos.join(','),
          no: this.customerStatementDetailDTOs[0].customerStatementNo,
          // reason: this.modifyInvoiceObj.reasonOptions.filter((item) => item.value === this.modifyInvoiceObj.reason)[0].label
          opReason: this.modifyPriceObj.reason[this.modifyPriceObj.reason.length - 1],
          opValue: this.modifyPriceObj.price
        }
        unitPriceUpdate(params).then(res => {
          if (res && res.status === 200) {
            this.$message.success('修改成功')
            this.initData()
          } else {
            this.$message.error(res.message || '修改失败')
          }
        })
          .catch(err => {
            this.$message.error(err.message || err.msg || '修改失败！')
          })
          .finally(_ => {
            this.changeStatementObj.modifyPriceVisible = false
          })
      })
    },
    // 修改折扣
    confirmModifyDiscount () {
      this.$refs['modifyDiscountForm'].validate(valid => {
        if (!valid) return
        if (this.checkedAllFlag) {
          if (this.checkAllLoading) return this.$message.error('请先完成全选！');
        }
        const params = {
          detailIds: this.checkedAllFlag ? this.checkedAllData : this.changeStatementObj.detailNos.join(','),
          no: this.customerStatementDetailDTOs[0].customerStatementNo,
          discountType: this.modifyDiscountObj.discountType,
          opReason: this.modifyDiscountObj.reason[this.modifyDiscountObj.reason.length - 1],
          opValue: this.modifyDiscountObj.discount
        }
        discountUpdate(params).then(res => {
          if (res && res.status === 200) {
            this.$message.success('修改成功')
            this.initData()
          } else {
            this.$message.error(res.message || '修改失败')
          }
        })
          .catch(err => {
            this.$message.error(err.message || err.msg || '修改失败！')
          })
          .finally(_ => {
            this.changeStatementObj.modifyDiscountVisible = false
          })
      })
    },
    // 修改收票抬头
    confirmModifyInvoice () {
      this.$refs['modifyInvoiceForm'].validate(valid => {
        if (!valid) return
        if (this.checkedAllFlag) {
          if (this.checkAllLoading) return this.$message.error('请先完成全选！');
        }
        const params = {
          ids: this.checkedAllFlag ? this.checkedAllData : this.changeStatementObj.detailNos.join(','),
          modifyValue: this.modifyInvoiceObj.ids,
          no: this.customerStatementDetailDTOs[0].customerStatementNo,
          // reason: this.modifyInvoiceObj.reasonOptions.filter((item) => item.value === this.modifyInvoiceObj.reason)[0].label
          reason: this.modifyInvoiceObj.reason[this.modifyInvoiceObj.reason.length - 1]
        }
        invoiceReceiverModify(params).then(res => {
          if (res && res.status === 200) {
            this.$message.success('修改成功')
            this.initData()
          } else {
            this.$message.error(res.message || '修改失败')
          }
        })
          .catch(err => {
            this.$message.error(err.message || err.msg || '修改失败！')
          })
          .finally(_ => {
            this.changeStatementObj.modifyInvoiceVisible = false
          })
      })
    },
    // 修改客户物料信息
    confirmModifyProduct () {
      this.$refs['modifyProductForm'].validate(valid => {
        if (!valid) return
        if (this.checkedAllFlag) {
          if (this.checkAllLoading) return this.$message.error('请先完成全选！');
        }
        const params = {
          ids: this.checkedAllFlag ? this.checkedAllData : this.changeStatementObj.detailNos.join(','),
          no: this.customerStatementDetailDTOs[0].customerStatementNo,
          customerOrderNo: this.modifyProductObj.customerOrderNo,
          customerProductName: this.modifyProductObj.customerProductName,
          customerProductNo: this.modifyProductObj.customerProductNo,
          customerProductNumber: this.modifyProductObj.customerProductNumber,
          customerProductSpecifications: this.modifyProductObj.customerProductSpecifications,
          customerProductUnit: this.modifyProductObj.customerProductUnit
        }
        materialModify(params).then(res => {
          if (res && res.status === 200) {
            this.$message.success('修改成功')
            this.initData()
          } else {
            this.$message.error(res.message || '修改失败')
          }
        })
          .catch(err => {
            this.$message.error(err.message || err.msg || '修改失败！')
          })
          .finally(_ => {
            this.changeStatementObj.modifyProductVisible = false
          })
      })
    },
    remoteInvoice (value) {
      let This = this
      This.modifyInvoiceObj.isLoading = true
      This.modifyInvoiceObj.current = 1
      This.modifyInvoiceObj.preKeyWords = value
      const params = { nameOrNumberLike: value, page: This.modifyInvoiceObj.current, size: 10 }
      searchClients(params).then(res => {
        if (res && res.status === 200) {
          This.modifyInvoiceObj.idOptions = [
            {
              customerNumber: '客户编码',
              customerName: '客户名称'
            },
            ...res.datas.list
          ]
          this.$refs.customer.customerList = This.modifyInvoiceObj.idOptions
          if (res.datas.list.length > 0) {
            This.modifyInvoiceObj.current++
          }
          This.modifyInvoiceObj.isLoading = false
          if (This.modifyInvoiceObj.current === 2) {
            This.$nextTick(() => {
              let container = this.$refs.customer.$refs.customer.$children[1].$el.getElementsByClassName('el-select-dropdown__wrap')[0]
              let list = this.$refs.customer.$refs.customer.$children[1].$el.getElementsByClassName('el-select-dropdown__list')[0]
              container.scrollTop = 0
              container.addEventListener('scroll', function (e) {
                let containerHeight = container.offsetHeight
                let listHeight = list.offsetHeight
                if (container.scrollTop > listHeight - containerHeight) {
                  if (!This.modifyInvoiceObj.isLoading) {
                    This.searchClientsAgain()
                  }
                }
              }, true)
            })
          }
        }
      })
    },
    searchClientsAgain() {
      this.modifyInvoiceObj.isLoading = true
      const params = { nameOrNumberLike: this.modifyInvoiceObj.preKeyWords, page: this.modifyInvoiceObj.current, size: 10 }
      searchClients(params).then(res => {
        if (res && res.status === 200) {
          if (res.datas.list.length > 0) {
            this.modifyInvoiceObj.current++
          }
          this.modifyInvoiceObj.idOptions = this.modifyInvoiceObj.idOptions.concat(res.datas.list)
          this.$refs.customer.customerList = this.modifyInvoiceObj.idOptions
          this.modifyInvoiceObj.isLoading = false
        }
      })
    },
    changeInvoice(value, customerName) {
      // const arr = this.modifyInvoiceObj.idOptions.filter(item => item.customerNumber === value)
      // if (arr && arr.length > 0) {
      //   this.modifyInvoiceObj.invoiceReceiver = arr[0].customerName
      // }
      this.modifyInvoiceObj.invoiceReceiver = customerName
      // this.modifyInvoiceObj.invoiceReceiver = this.modifyInvoiceObj.idOptions.filter(item => item.customerNumber === value)?.[0].customerName
    },
    handleProductChange(val) {
      console.log(val);
      const arr = [{ id: 6, value: 'customerOrderNo' }, { id: 7, value: 'customerProductNo' }, { id: 8, value: 'customerProductName' }, { id: 9, value: 'customerProductSpecifications' }, { id: 10, value: 'customerProductUnit' }, { id: 11, value: 'customerProductNumber' }]
      const temp = arr.filter((value) => val === value.id)[0].value
      this.modifyProductObj.value = this.customerStatementDetailDTOs[0][temp]
    },
    valDiscount(rule, value, callback) {
      if (this.modifyDiscountObj.discountType === 3) {
        if (value && !/^0\.(\d{1,4})?$/.test(Number(value))) {
          callback(new Error('请输入0-1的小数，最多支持四位小数'));
        } else {
          callback()
        }
      } else {
        if (value && !/^(-?)\d+(\.\d{1,2})?$/.test(Number(value))) {
          callback(new Error('小数点后只能保留两位'));
        } else {
          callback()
        }
      }
    },
    valPrice(rule, value, callback) {
      if (value && !/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,6})?$/.test(Number(value))) {
        callback(new Error('小数点后最多保留六位'));
      } else {
        callback()
      }
    },
    valContent(rule, value, callback) {
      if (this.modifyProductObj.content === 11) {
        if (value && !/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,6})?$/.test(Number(value))) {
          callback(new Error('小数点后最多保留六位'));
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    valProductUnit (rule, value, callback) {
      if (this.modifyProductObj.customerProductNumber && Number(this.modifyProductObj.customerProductNumber) !== 0 && !value) {
        callback(new Error('请填写客户物料单位'))
      } else {
        callback()
      }
    },
    valProductNumber(rule, value, callback) {
      if (this.modifyProductObj.customerProductUnit) {
        if (!value) {
          callback(new Error('请填写客户物料数量'))
        } else if (!/(^[1-9](\d+)?(\.\d{1,6})?$)|(^\d\.\d{1,6}$)/.test(Number(value))) {
          callback(new Error('数量必须大于0，且小数点后最多保留六位'));
        }
      }
      if (value && !/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,6})?$/.test(Number(value))) {
        callback(new Error('小数点后最多保留六位'));
      } else {
        callback()
      }
    },
    openSplitDialog () {
      if (!this.splitStatementObj.detailNos.length) return this.$message.error('请选择对账单明细行！')
      // if (this.total <= 1) return this.$message.error('对账单明细行只有一行不能拆分对账单！')
      // if (this.splitStatementObj.detailNos.length >= this.total) return this.$message.error('不能选择全部对账单明细行！')
      this.splitStatementObj.splitStatementVisible = true
    },
    pushSAP () {
      console.log('pushSAP')
    },
    // 拆分对账单
    splitStatement () {
      this.$refs['splitStatementForm'].validate(valid => {
        if (!valid) return
        console.log(this.splitStatementObj)
        this.splitStatementObj.submitLoading = true
        let isNew = this.splitStatementObj.isNewStatement
        let data = {
          detailNos: this.splitStatementObj.detailNos.join(','),
          isNew,
          no: this.splitStatementObj.lines[0].customerStatementNo,
          otherNo: this.splitStatementObj.statementName
        }
        if (isNew === '1') {
          data.newName = this.splitStatementObj.newName
        }
        statementSplit(data)
          .then(res => {
            if (res.status === 200) {
              this.$message.success('操作成功！')
              this.cancelSplit()
              this.resetSplitDialog()
              this.getDetailList()
              this.reCalcTax()
              if (res.datas) {
                this.$router.push(`/account-statement-center/statement/detail/${res.datas}`)
              }
            } else {
              this.$message.error(res.message || res.msg || '操作失败！')
            }
          })
          .catch(err => {
            this.$message.error(err.message || err.msg || '操作失败！')
          })
          .finally(_ => {
            console.log('finally!')
            this.splitStatementObj.submitLoading = false
          })
      })
    },
    reCalcTax () {
      this.accountId = this.$route.params.accountId
      getStatementDetail(this.accountId).then(res => {
        if (res.status === 200) {
          if (res.datas) {
            this.customerDTO = res.datas.customerDTO // 对账单客户属性
            this.customerStatementConfigDTO =
              res.datas.customerStatementConfigDTO // 对账配置
            this.customerStatementDTO = res.datas.customerStatementDTO // 对账单属性
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    cancelSplit () {
      this.splitStatementObj.splitStatementVisible = false
      this.splitStatementObj.submitLoading = false
      this.$refs['splitStatementForm'].resetFields()
    },
    resetSplitDialog () {
      this.splitStatementObj.isNewStatement = ''
      this.splitStatementObj.statementName = ''
      this.splitStatementObj.newName = ''
      this.splitStatementObj.detailNos = []
      this.splitStatementObj.lines = []
    },
    // 拆分对账单
    statementNameChange (val) {
      this.splitStatementObj.statementName = ''
      this.$refs['splitStatementForm'].clearValidate()
    },
    openAddStatementRow () {
      this.addStatementRowObj.visible = true
      setTimeout(() => {
        const basisList = this.$refs.BasisList
        const customer = this.customerStatementDetailDTOs[0].customer
        const currency = this.customerStatementDTO.currency
        const groupNo = this.customerStatementDTO.groupNo
        const statementNo = this.customerStatementDTO.no
        basisList && (basisList.prefixCustomer = customer)
        basisList && (basisList.prefixCurrency = currency)
        basisList && (basisList.prefixGroupNo = groupNo)
        basisList && (basisList.prefixStatementNo = statementNo)
        basisList && (basisList.prefixStatus = '2,3')
        basisList && basisList.getBasisList && basisList.getBasisList()
      }, 600)
    },
    cancelAddStatementRow () {
      this.addStatementRowObj.visible = false
      this.addStatementRowObj.submitLoading = false
    },
    // 新增行
    addStatementRow () {
      const basisList = this.$refs.BasisList
      const tb = basisList.$refs.basisTable
      if (basisList.checkAllLoading) {
        return this.$message.error('请先完成全选！')
      }
      let selectRows = tb.getSelections()
      console.log(basisList, tb, selectRows)
      let data = {
        ids: selectRows.map(i => i.id).join(','),
        no: this.customerStatementDetailDTOs[0].customerStatementNo
      }
      if (basisList.checkedAllData && basisList.checkedAllData.length > 0) {
        data.ids = basisList.checkedAllData.join(',')
      }
      this.addStatementRowObj.submitLoading = true
      manualCreateApi(data)
        .then(res => {
          if (res && res.status === 200) {
            this.$message.success('创建成功')
            this.cancelAddStatementRow()
            this.initData()
          } else {
            this.$message.error(res.msg || res.message || '创建失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '创建失败！')
        })
        .finally(() => {
          this.addStatementRowObj.submitLoading = false
        })
    },
    //  删除行
    delStatementRow () {
      if (!this.splitStatementObj.lines.length) return this.$message.error('请选择需要删除的对账单明细行！')
      console.log('delStatementRow')
      this.$confirm('是否确认删除对账单明细行？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then((a, b, c) => {
        let data = {
          detailNos: this.splitStatementObj.lines.map(l => l.no).join(','),
          no: this.splitStatementObj.lines[0].customerStatementNo
        }
        this.detailListLoading = true
        manualDeleteApi(data)
          .then(res => {
            if (res && res.status === 200) {
              this.$message.success('删除成功')
              this.initData()
            } else {
              this.$message.error(res.msg || res.message || '删除失败！')
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '删除失败！')
          })
          .finally(() => {
            this.detailListLoading = false
          })
      }).catch(err => console.log(err))
    },
    handleEditName() {
      this.isNameEditing = true;
      this.newStatementName = this.customerStatementDTO.name;
    },
    handleSaveName() {
      updateStatementName(this.customerStatementDTO.no, this.newStatementName)
        .then(res => {
          if (res && res.status === 200) {
            this.$message.success('修改成功')
            this.customerStatementDTO.name = this.newStatementName;
            this.isNameEditing = false;
          } else {
            this.$message.error(res.msg || res.message || '修改失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '修改失败！')
        })
    },
    handleCancelEditName() {
      this.isNameEditing = false;
    },
    // 全选
    checkAll () {
      let param = { ...this.searchForm }
      param.no = this.customerStatementDetailDTOs[0].customerStatementNo
      this.formatParams(param)
      this.checkAllLoading = true;
      selectDetailIds(param).then(res => {
        if (res && res.status === 200) {
          if (res.datas) {
            this.$message.success('全选成功！')
            this.customerStatementDetailDTOs.forEach(row => {
              // if (row.soType !== 'ZRE1') {
              //   this.$refs.detailTable.toggleRowSelection(row, true)
              // }
              this.$refs.detailTable.toggleRowSelection(row, true)
            })
            this.checkedAllFlag = true;
            this.checkedAllData = res.datas;
            this.checkAllLoading = false;
          }
        } else {
          this.$message.error(res.message || '全选失败')
          this.checkAllLoading = false;
        }
      })
        .catch(err => {
          this.$message.error(err.msg || err.message || '全选失败')
          this.checkAllLoading = false;
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.statement-set-container {
  padding-bottom: 20px;
  .statement-set-block {
    .statement-set-block-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 40px 0 30px;
      font-size: 18px;
    }
    .ml-10 {
      margin-left: 10px;
    }
    .flex {
      display: flex;
    }
    .statement-set-block-item {
      padding: 0 30px;
      margin: 20px 0;
      font-size: 15px;
      & > div {
        margin-top: 10px;
      }
    }
    .statement-set-block-upload-container {
      padding: 0 30px;
      margin-top: 20px;
      display: flex;
      .statement-set-block-file-list {
        margin-left: 10px;
        flex: 1;
        & > ul > li {
          padding: 0;
          margin-bottom: 5px;
          text-align: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          a {
            display: inline-block;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            justify-content: left;
            max-width: 100%;
          }
        }
      }
    }
    .statement-set-detail-container {
      // display: flex;
      // justify-content: space-between;
      align-items: center;
      padding: 0 40px 0 30px;
      margin-bottom: 10px;
      .statement-set-detail-info {
        & > span {
          margin-right: 10px;
          &:last-child {
            margin-right: 0;
          }
        }
      }
      .statement-set-detail-action {
        justify-content: flex-end;
        margin-bottom: 10px;
        text-align: right;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        .switch-btn-first {
          margin-right: 20px;
        }
      }
    }
    .statement-set-detail-table-container {
      padding: 0 10px;
    }
  }
  .template-set-form {
    margin-top: 30px;
    .single-item-body {
      max-width: 200px;
    }
  }
  .el-form-item {
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .el-select {
    width: 100%;
  }
  .text-content {
    padding: 20px;
    max-width: 800px;
    min-height: 60px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
  .pseudo-input-box {
    text-align: left;
    min-height: 36px;
    &.input-error {
      border-color: #ff7268;
    }
  }
  .split-statement{
    display: flex;
    justify-content: space-around;
  }
  .error-info {
    color: #ff7268;
    font-size: 12px;
    line-height: 1;
    padding: 4px 10px 20px 12px;
    top: 100%;
    left: 0;
    margin-top: -10px;
  }
}
.selectClientItem {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 300px;
  }
}
</style>
