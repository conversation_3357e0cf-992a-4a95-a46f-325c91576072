<template>
  <div class="statement-detail">
    <el-tabs v-model="activeName">
      <el-tab-pane label="对账单详情" name="first">
        <StatementSet />
      </el-tab-pane>
      <el-tab-pane label="操作日志" name="second">
        <iframe
          ref="iframe"
          :height="height"
          width="100%"
          scrolling="auto"
          :src="url"
          frameborder="0"
        >
        </iframe>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import StatementSet from './statementSet.vue'
  export default {
    components: {
      StatementSet
    },
    data() {
      return {
        activeName: 'first',
        loaded: false,
        height: '900px',
        accountId: ''
      };
    },
    mounted() {
      const { accountId } = this.$route.params
      this.accountId = accountId
      this.initPostIframeMessage()
    },
    computed: {
      url () {
        let ret = `https://boss.zkh360.com/fn/statementRecord/${this.accountId}`
        if (/boss-uat/.test(location.href)) {
          ret = `https://boss-uat-5.zkh360.com/fn/statementRecord/${this.accountId}`
        }
        if (/fetest|localhost|local/.test(location.href)) {
          ret = `http://local.zkh360.com:9004/fn/statementRecord/${this.accountId}`
        }
        return ret
        }
    },
    methods: {
      initPostIframeMessage () {
        try {
          const username = window.CUR_DATA.user.name
          this.$refs.iframe.onload = () => {
            this.$refs.iframe.contentWindow.postMessage({
              username
            }, '*')
          }
        } catch (err) { console.log(err) }
      }
    }
  };
</script>
<style lang="scss" scoped>
.statement-detail {
  padding: 0 20px 20px;
}
</style>
