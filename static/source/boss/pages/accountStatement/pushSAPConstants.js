
const renderStep1Fields = [
  { label: '对账单明细编号', prop: 'no', width: 220, showOverflow: true },
  { label: '对账依据编号', prop: 'basisNo', width: 180, showOverflow: true },
  { label: '关联订单号', prop: 'soNo', width: 180, showOverflow: true },
  { label: '销售订单类型描述', prop: 'soType' },
  { label: '交货类型描述', prop: 'dnDesc' },
  { label: '交货数量', prop: 'deliveryQty' },
  { label: '对账数量', prop: 'checkNumber' },
  { label: '对账含税总金额', prop: 'checkAmountTax' },
  { label: '产品编码', prop: 'productSku', width: 180, showOverflow: true },
  { label: 'oms交货单号', prop: 'omsDnNo', width: 180, showOverflow: true },
  { label: 'oms交货单行号', prop: 'omsDnItemNo', width: 180, showOverflow: true },
  { label: 'oms订单号', prop: 'omsSoNo', width: 180, showOverflow: true },
  { label: 'oms订单行号', prop: 'omsSoItemNo', width: 180, showOverflow: true },
  { label: '领料人', prop: 'productPicker' },
  { label: '需求部门', prop: 'requirementsDept' },
  { label: '订单联系人', prop: 'orderContactName' },
  { label: '收票联系人', prop: 'ticketContactName' },
  { label: '收货联系人', prop: 'shippingContactName' },
  { label: '是否含税', prop: 'conditionType', format(val) { return val === 'Z005' ? '含税' : '未税' } },
  { label: '是否成品油', prop: 'refinedOil', format(val) { return val === '1' ? '是' : '否' } },
  { label: '收票方', prop: 'invoiceReceiver', showOverflow: true },
  { label: '收票方名称', prop: 'invoiceReceiverName', width: 180, showOverflow: true }
]
let renderStep2Fields = [
  { label: '开票类型', prop: 'invoiceType' },
  { label: '开票申请单号', prop: 'billingApplyNumber' },
  { label: '发票备注', prop: 'financialNote', type: 'textarea' },
  { label: '寄票备注', prop: 'shippingInfo', maxlength: 30, type: 'textarea' },
  { label: '原发票草稿号', prop: 'originalInvoiceDraftNum', maxlength: 50 },
  { label: 'ESP参考凭证号', prop: 'espRefVoucherNum', maxlength: 16 },
  { label: '收票联系人', prop: 'ticketContactName' },
  { label: '电话', prop: 'ticketContactPhone', disable: true },
  { label: '收票地址', prop: 'ticketAddress', disable: true, span: 24, type: 'span' },
  { label: '', prop: null },
  { label: '订单联系人', prop: 'orderContactName' },
  { label: '电话', prop: 'orderContactPhone', disable: true },
  { label: '快递公司', prop: 'expressCompany' },
  { label: '快递服务产品', prop: 'sfType' }
]
const secondStepFields = {
  invoiceType: '',
  shippingInfo: '',
  financialNote: '',
  customerRefNum: '',
  originalInvoiceDraftNum: '',
  espRefVoucherNum: '',
  ticketContactName: '',
  ticketContactPhone: '',
  ticketAddress: '', // 手票地址
  ticketAddressId: '', // 手票地址id
  orderContactName: '',
  orderContactPhone: '',
  ticketContact: [],
  orderContact: [],
  invoiceTypeOptions: [],
  expressCompanyOptions: [],
  sfType: ''
}
const chunk = (arr, size) =>
  Array.from({ length: Math.ceil(arr.length / size) }, (v, i) => arr.slice(i * size, i * size + size))
renderStep2Fields = chunk(renderStep2Fields, 2)
const pushSapDialogData = {
  renderStep1Fields,
  renderStep2Fields,
  secondStepFields,
  moneySummary: {
    checkAmountTax: 0,
    checkAmount: 0,
    profitLossAmountTax: 0,
    verificationAmount: 0
  },
  tipStatus: {
    invoiceType: '',
    multiBillingRobot: '',
    multiInvoicingByMail: '',
    multiMergeBilling: '',
    multiReturnOffset: '',
    multiShowDiscount: '',
    ticketContact: '',
    orderContact: ''
  },
  sapSyncOptions: [],
  step: 0,
  basisNo: '',
  productSku: '',
  soNo: '',
  sapSyncStatus: '',
  sapSelections: [],
  list: [],
  checkbox: [],
  checkbox1: [],
  checkboxOptions1: [
    { value: 'includeCustomerRefNum', label: '包含客户参考号' }
  ],
  checkboxOptions: [
    { value: 'invoicingByMail', label: '凭邮件开票' },
    { value: 'ifDocMailed', label: '附邮寄资料' },
    { value: 'returnOffset', label: '退换货抵消' },
    { value: 'mergeBilling', label: '合并开票' },
    { value: 'billingRobot', label: '开票机器人' },
    { value: 'showDiscount', label: '显示折扣' }
  ],
  refinedOilOptions: [
    { label: '是', value: '1' },
    { label: '否', value: '0' }
  ],
  conditionTypeOptions: [
    { label: '含税', value: 'Z005' },
    { label: '未税', value: 'Z010' }
  ],
  loading: {
    searchLoading: false,
    tableLoading: false,
    dialogLoading: false,
    submitLoading: false,
    ticketLoading: false,
    orderLoading: false
  },
  disable: {
    prevStep: false
  }
}
const debounce = function (func, delay = 800) {
  let timer
  return function () {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, arguments)
      timer = null
    }, delay)
  }
}
const throttle = function (func, delay = 800) {
  let timer
  return function () {
    if (timer) return
    timer = setTimeout(() => {
      func.apply(this, arguments)
      timer = null
    }, delay)
  }
}
export {
  pushSapDialogData,
  renderStep1Fields,
  renderStep2Fields,
  secondStepFields,
  debounce,
  throttle
}
