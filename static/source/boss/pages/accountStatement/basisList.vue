<template>
  <div class="app-container account-statement-container">
    <div class="filter-container" :style="noSearchBg?{backgroundColor:'white'}:''">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
        label-position="right">
        <el-row>
          <el-col :span="14">
            <el-form-item label="对账依据编号：" prop="basisNo">
              <el-input
                v-model="searchForm.basisNo"
                placeholder="同时支持5000个单号，以空格分隔"
                clearable/></el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="行编号：" prop="basisItemNo">
              <el-input
                v-model.trim="searchForm.basisItemNo"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="客户名称：" prop="customerCode2">
              <RemoteCustomer :disabled="hideCustomer" :value="searchForm.customerCode2" @change="changeCustomerCode2" placeholder="请输入关键词"/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户编号：" prop="customerCode">
              <RemoteCustomer v-model="searchForm.customerCode" getValue :disabled="hideCustomer" placeholder="请输入" @change="customerCodeBlur"/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="库存描述：" prop="locationDesc">
              <el-input
                v-model.trim="searchForm.locationDesc"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button icon="el-icon-search" @click="handleReset">重置</el-button>
            <el-button
              type="primary"
              circle
              :icon="caretIcon"
              @click="searchSonditionOpen = !searchSonditionOpen"
            />
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="客服：" prop="customerServiceName">
              <el-input
                v-model.trim="searchForm.customerServiceName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客服主管：" prop="superCustomerServiceName">
              <el-input
                v-model.trim="searchForm.superCustomerServiceName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="物料组：" prop="productGroup">
              <material-group v-model="searchForm.productGroup" :multiple="true"/>
            </el-form-item>
          </el-col>
          </el-row>
          <el-row v-show="searchSonditionOpen">
          <el-col :span="14">
            <el-form-item label="产品编码：" prop="materiel">
              <el-input
                v-model="searchForm.materiel"
                placeholder="产品编号、客户物料号 均支持搜索，同时支持500个单号，以空格分隔"
                clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="订单联系人：" prop="orderContactName">
              <el-input
                v-model="searchForm.orderContactName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="收货联系人：" prop="receiverName">
              <el-input
                v-model="searchForm.receiverName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="收票联系人：" prop="receivingInvoiceName">
              <el-input
                v-model="searchForm.receivingInvoiceName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="对账状态：" prop="status" v-if="visible">
              <el-select
                multiple
                v-model="searchForm.status"
                placeholder="请选择"
                clearable
                :disabled="hideCustomer">
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="订单号：" prop="orderNo">
              <el-input
                v-model="searchForm.orderNo"
                placeholder="外围订单号、销售订单号、客户订单号、父订单号、三方订单号 均支持搜索，同时支持5000个单号，以空格分隔"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="外围系统：" prop="peripheralSystem">
              <el-select
                multiple
                v-model="searchForm.peripheralSystem"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in peripheralSystemOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="交货类型描述：" prop="deliveryType">
              <el-select
                filterable
                multiple
                v-model="searchForm.deliveryType"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in deliveryTypeDescOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="需求部门：" prop="department">
              <el-select
                multiple
                v-model="searchForm.department"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in departmentOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="销售类型描述：" prop="orderType">
              <el-select
                filterable
                multiple
                v-model="searchForm.orderType"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in orderTypeDescStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="所属集团名称：" prop="parentName">
              <el-input
                v-model.trim="searchForm.parentName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="销售组织：" prop="salesOrganization">
              <el-input
                v-model.trim="searchForm.salesOrganization"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="是否签收：" prop="isSign">
              <el-select
                v-model="searchForm.isSign"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in booleanOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="交货状态：" prop="deliveryStatus">
              <el-select
                multiple
                v-model="searchForm.deliveryStatus"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in deliveryStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="实际交货日期：">
              <el-col :span="11">
                <el-form-item prop="realDeliveryStartDate">
                  <el-date-picker
                    clearable
                    v-model="searchForm.realDeliveryStartDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="realDeliveryEndDate">
                  <el-date-picker
                    clearable
                    v-model="searchForm.realDeliveryEndDate"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="未开票责任人：" prop="unInvoicedResponsible">
              <el-select
                multiple
                v-model="searchForm.unInvoicedResponsible"
                placeholder="请选择" clearable>
                <el-option
                  v-for="item in unInvoicedResponsibleOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="未开票原因：" prop="unInvoicedReason">
              <el-select
                v-model="searchForm.unInvoicedReason"
                placeholder="请选择"
                multiple
                clearable>
                <el-option
                  v-for="item in unInvoicedReasonOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="开票状态：" prop="invoicingStatus" v-if="visible" >
              <el-select
                v-model="searchForm.invoicingStatus" multiple
                placeholder="请选择" clearable>
                <el-option
                  v-for="item in invoicingStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="原因细分：" prop="unInvoicedReasonDetail">
              <el-select
                v-model="searchForm.unInvoicedReasonDetail"
                placeholder="请选择"
                multiple
                clearable>
                <el-option
                  v-for="item in unInvoicedReasonDetailOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen" type="flex">
          <el-col :span="7">
            <el-form-item label="未开票天数：">
              <div class="el-col-8"><el-input placeholder="最小天数" clearable v-model.trim="searchForm.unInvoicedMinDays"></el-input></div>
              <div class="line el-col el-col-2">-</div>
              <div class="el-col-8"><el-input placeholder="最大天数" clearable v-model.trim="searchForm.unInvoicedMaxDays"></el-input></div>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="后补订单：" prop="backupOrder">
              <el-select
                v-model="searchForm.backupOrder"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in backupOrderStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="是否整单交齐：" prop="isWholeDelivery">
              <el-select
                v-model="searchForm.isWholeDelivery"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in booleanOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen" type="flex">
          <el-col :span="7">
            <el-form-item label="是否签单返回：" prop="isSignBack">
              <el-select
                v-model="searchForm.isSignBack"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in booleanOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="成本中心名称：" prop="customerCostCenterName">
              <el-input v-model="searchForm.customerCostCenterName" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="用户名称：" prop="espUserName">
              <el-input v-model="searchForm.espUserName" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="7">
            <el-form-item label="客户对账日-当前日期：" prop="daysLeftFlag" label-width="160px" >
              <el-select
                v-model="searchForm.daysLeftFlag"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in daysLeftFlagOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-row v-show="searchSonditionOpen" type="flex">
          <el-col :span="7">
            <el-form-item label="客户行号：" prop="customerItemNo">
              <el-input v-model="searchForm.customerItemNo" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
     <div class="statistics">
      <span
        >对账含税总金额：{{
          fixedDecimalPlace2(moneySummary.reconciliationAmountRemainingWithTax)
        }}</span
      >
      <span
        >对账未税总金额：{{
          fixedDecimalPlace2(moneySummary.reconciliationAmountRemainingWithoutTax)
        }}</span
      >
      <span>税额：{{ fixedDecimalPlace2(moneySummary.totalTax) }}</span>
    </div>
    <div class="backlog-search-result-container">
      <el-row v-if="!hideTableHeader" type="flex" class="result-title" justify="space-between">
        <el-col :span="8">对账依据列表</el-col>
        <el-col  style="text-align:right;">
          <el-button @click="showColumnSet">自定义列</el-button>
          <el-button type="primary" @click="openImportDialog">导入</el-button>
          <el-button :loading="exportLoading" @click="exportBasis">导出</el-button>
          <!-- <el-button type="warning" @click="createStatementAll" :loading="createStatementAllLoading">全部生成对账单</el-button> -->
          <el-button type="warning" @click="checkAll" :loading="checkAllLoading" >全选</el-button>
          <el-button type="primary" @click="createStatement">生成对账单</el-button>
          <el-button type="default" @click="openInvoiceDialog">未开票原因记录</el-button>
          <el-button type="success" v-if="showForceComplete" @click="forceComplete">强制完成</el-button>
        </el-col>
      </el-row>
      <el-row v-if="showHeaderCheckAll" type="flex" class="result-title" justify="space-between">
        <el-col :span="8">对账依据列表</el-col>
        <el-col  style="text-align:right;">
          <el-button type="warning" @click="checkAll" :loading="checkAllLoading">全选</el-button>
        </el-col>
      </el-row>
      <zkh-table
        ref="basisTable"
        :selectable="true"
        :loading="basisListLoading"
        :data="basisList"
        :columns="columns"
        :height="500"
        :headerDragend="headerDragend"
        :columnEditable="true"
        :visible.sync="columnsSetVisible"
        :columnsConfig="columnsConfigDeepCopy"
        :defaultColumnsConfig="defaultColumnsConfig"
        :checkSelectable="checkSelectable"
        @columnSet="columnSet"
        :selectionChange="handleSapSelectionChange"
        :sortChange="handleSortChange"
        @manage-button-click="handleManageClick"
      ></zkh-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10,20,30,50,100]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="() => { changePage = true; getBasisList()}"
    />
    <el-dialog
      width="800px"
      title="未开票原因记录"
      class="invoice-reason-dialog"
      :visible.sync="invoiceReasonDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <UnInvoicedReasonRecord
        :basisId="basisId"
        :visible.sync="invoiceReasonDialog"
        :unInvoicedReasonOptions="unInvoicedReasonOptions"
        :unInvoicedReasonDetailOptions="unInvoicedReasonDetailOptions"
        ref="unInvoicedReasonRecord"
        @un-invoiced-reason-dialog="closeInvoiceDialog"
        @un-invoiced-reason-done="invoiceDialogDone"
      />
    </el-dialog>
    <el-dialog
      width="800px"
      title="未开票记录"
      :visible.sync="inReRecordShow"
      :close-on-click-modal="false">
      <InReRecordShow
        :visible.sync="inReRecordShow"
        :inReRecordShowId="inReRecordShowId"
        ref="InReRecordShow"
        @un-invoiced-reason-show-dialog="closeInvoiceRecordDialog"
      />
    </el-dialog>
    <el-dialog
      width="320px"
      title="导入"
      :visible.sync="showImportDialog"
      :close-on-click-modal="false">
      <div style="display:flex;justify-content:space-around">
        <el-button :loading="downloadLoading" @click="downloadTemplate">下载模板</el-button>
        <el-upload class="btn-upload" action="/reconciliation/unInvoicedReasonRecord/v1/upload" :show-file-list="false"
          :on-success="onUploadSuccess"
          :before-upload="beforeUpload"
          :on-error="onUploadError"
          :accept="acceptFileType.commonType"
          name="file">
          <el-button :loading="importLoading" size="small" type="primary">导入</el-button>
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { FileTypeMapping } from '@/utils/enums';
import Pagination from '@/components/Pagination'
import UnInvoicedReasonRecord from './components/unInvoicedReasonRecord'
import InReRecordShow from './components/inReRecordShow.vue'
import getColumns from './basisColumns.js'
import {
  createdConfigByColumns,
  formatByConfig,
  getConfig,
  // cachedConfig,
  checkColumnsConfig,
  removeConfig
} from '@/components/ZkhTable/columnConfigTransformation.js'
import MaterialGroup from '@/components/SearchFields/materialGroup'
import { fixedDecimalPlace } from '@/filters/index.js';
import { mapState } from 'vuex'

import {
  getBasisList,
  createStatementByBasis,
  createStatementByBasisPre,
  getOrderTypeDescStatusOptions,
  getStatusOptions,
  getDeliveryTypeDescOptions,
  getPeripheralSystemOptions,
  getUnInvoicedReasonDetailOptions,
  getInvoicingStatusOptions,
  getUnInvoicedReasonOptions,
  getUnInvoicedResponsibleOptions,
  getCustomerListByPartName,
  forceComplate,
  getBasisIdsBySearchFields,
  getSelectAllBySearchFields,
  demandDepartment,
  exportCheck,
  downloadBasisTpl,
  fetchConfigFromServer,
  saveConfig2Server,
  getTotalAmount
} from '@/api/accountStatement.js'
import { RemoteCustomer } from '@kun-ui/remote-customer'
import { xor, pullAll, debounce } from 'lodash'

// 页面搜索条件初始化
const getInitSearchParams = function () {
  return {
    basisNo: '', // 对账依据编号
    basisItemNo: '', // 对账依据行编号
    customerCode: '', // 客户编号
    customerCode2: '', // 客户名称
    orderType: '', // 销售类型描述
    status: '', // 对账状态
    customerServiceName: '', // 客服
    materiel: '', // 产品名称
    superCustomerServiceName: '', // 客服主管
    orderNo: '', // 订单号
    orderContactName: '', // 订单联系人
    receiverName: '', // 收货联系人
    receivingInvoiceName: '', // 收票联系人
    parentName: '', // 所属集团名称
    salesOrganization: '', // 销售组织
    // signStatus: '', // 客户是否签收
    deliveryStatus: '', // 交货状态
    locationDesc: '', // 库存地点描述
    deliveryType: '', // 交货类型描述
    realDeliveryStartDate: '', // 实际交货日期-起始
    peripheralSystem: '', // 外围系统
    department: '', // 需求部门
    backupOrder: '', // 后补订单
    realDeliveryEndDate: '', // 实际交货日期-截止
    unInvoicedResponsible: '', // 未开票责任人
    unInvoicedReason: '', // 未开票原因
    unInvoicedReasonDetail: '', // 未开票原因明细
    invoicingStatus: ['A', 'B'], // 开票状态
    unInvoicedMinDays: '', // 未开票最小天数
    unInvoicedMaxDays: '', // 未开票最大天数
    daysLeftFlag: '', // 客户对账日-当前日期
    daysLeftOrder: '', // 客户对账日-当前日期排序
    idOrder: 1,
    customerCostCenterName: '', // 成本中心名称
    espUserName: '', // esp用户名称
    customerItemNo: '' // 客户行号
  }
}

const runPromiseInSeries = async (callbacks) => {
  for (const callback of callbacks) {
    try {
      await callback();
    } catch (error) {
      return Promise.reject(new Error('cancel'));
    }
  }
}

const fixedDecimalPlace2 = fixedDecimalPlace.bind(null, 2);
export default {
  name: 'BasisList',
  data () {
    return {
      orderTypeDescStatusOptions: [], // 销售订单类型描述 下拉选项
      statusOptions: [], // 对账依据状态 下拉选项
      peripheralSystemOptions: [], // 外围系统下拉选项
      departmentOptions: [], // 需求部门下拉选项
      deliveryTypeDescOptions: [], // 交货类型描述 下拉选项
      customerNoLoading: false,
      basisListLoading: false, // 查询按钮loading
      customerNoOptions: [],
      searchForm: {}, // 绑定搜索参数
      columnsSetVisible: false, // 列设置弹框显隐
      defaultColumnsConfig: [],
      multipleSelection: [],
      deliveryStatusOptions: [
        { label: '未交货', value: 'A' },
        { label: '部分交货', value: 'B' },
        { label: '已交货', value: 'C' }
      ],
      changePage: false, // 是否切换页码
      // signStatusOptions: [
      //   { label: '未签收', value: '0' },
      //   { label: '已签收', value: '1' }
      // ],
      backupOrderStatusOptions: [
        { label: '是', value: 'X' },
        { label: '否', value: 'Z' }
      ],
      booleanOptions: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      daysLeftFlagOptions: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      rules: {},
      basisList: [],
      total: 0,
      columns: [],
      columnsConfig: [],
      columnsConfigDeepCopy: [],
      prefixCustomer: '',
      prefixCurrency: '',
      prefixGroupNo: '',
      prefixStatus: '',
      prefixStatementNo: '',
      createStatementAllLoading: false,
      searchSonditionOpen: false,
      checkedAllFlag: false,
      checkedAllData: [],
      unInvoicedReasonDetailOptions: [],
      unInvoicedResponsibleOptions: [], // 未开票责任人
      invoicingStatusOptions: [],
      unInvoicedReasonOptions: [],
      exportLoading: false,
      invoiceReasonDialog: false,
      basisId: '',
      inReRecordShow: false,
      inReRecordShowId: '',
      checkAllLoading: false,
      showImportDialog: false,
      downloadLoading: false,
      importLoading: false,
      tableServerConfig: null,
      moneySummary: {
        reconciliationAmountRemainingWithTax: 0,
        reconciliationAmountRemainingWithoutTax: 0,
        totalTax: 0
      }
    }
  },
  props: {
    noSearchBg: {
      type: Boolean,
      default: false
    },
    hideTableHeader: {
      type: Boolean,
      default: false
    },
    diableInitialFetchList: {
      type: Boolean,
      default: false
    },
    showHeaderCheckAll: {
      type: Boolean,
      default: false
    },
    hideCustomer: {
      type: Boolean,
      default: false
    },
    manualCreateStatement: {
      type: Function,
      default: null
    },
    condition: {
      type: Object,
      default() {
        return {};
      }
    },
    visible: {
      type: Boolean,
      default: true
    }
  },
  components: {
    Pagination,
    UnInvoicedReasonRecord,
    InReRecordShow,
    MaterialGroup,
    RemoteCustomer
  },
  watch: {
    'searchForm.unInvoicedReason' (newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        this.searchForm.unInvoicedReasonDetail = ''
        this.unInvoicedReasonDetailOptions = []
        if (!newVal.length) return
        getUnInvoicedReasonDetailOptions(newVal)
          .then(res => {
            if (res && res.status === 200 && res.datas) {
              this.unInvoicedReasonDetailOptions = res.datas.map(data => ({
                label: data.name, value: data.code
              }))
            }
          })
      }
    },
    condition(newVal, oldVal) {
      this.searchForm = Object.assign(this.searchForm, newVal)
      this.handleFilter()
    }
  },
  async created () {
    const tableConfigResponse = await fetchConfigFromServer()
    if (tableConfigResponse) {
      this.tableServerConfig = tableConfigResponse
    }
    Promise.all([
      getOrderTypeDescStatusOptions().then(data => {
        this.orderTypeDescStatusOptions = data
      }),
      getStatusOptions().then(data => {
        this.statusOptions = data
      }),
      getDeliveryTypeDescOptions().then(data => {
        this.deliveryTypeDescOptions = data
      }),
      getPeripheralSystemOptions().then(data => {
        this.peripheralSystemOptions = data
      }),
      getInvoicingStatusOptions().then(data => {
        this.invoicingStatusOptions = data
      }),
      getUnInvoicedResponsibleOptions().then(data => {
        this.unInvoicedResponsibleOptions = data
      }),
      getUnInvoicedReasonOptions({ deleted: false }).then(res => {
        if (res && res.status === 200 && res.datas) {
          this.unInvoicedReasonOptions = res.datas.map(data => ({
            label: data.name, value: data.code
          }))
        }
      })
    ]).then(() => {
      this.initColumns()
      this.searchForm = getInitSearchParams()
      if (this.condition) {
        this.searchForm = { ...this.searchForm, ...this.condition }
      }
      if (!this.diableInitialFetchList) {
        this.handleFilter()
      }
    })
    this.getTotalAmountDebounced = debounce(async () => {
      this.handleGetTotalAmount()
    }, 500)
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType
    }),
    caretIcon () {
      if (this.searchSonditionOpen) {
        return 'el-icon-caret-top'
      } else {
        return 'el-icon-caret-bottom'
      }
    },
    getQueryOrderType () {
      if (!this.searchForm.orderType) {
        return []
      } else {
        return this.searchForm.orderType.split(',')
      }
    },
    showForceComplete () {
      let ret = false
      try {
        const firstMenu = this.$store.state.menu.filter(menu => menu.name === '对账中心')[0]
        const secondMenu = firstMenu.children.filter(menu => menu.name === '对账依据')[0]
        const thirdButton = secondMenu.children.filter(menu => menu.name === '强制完成')[0]
        if (thirdButton) {
          ret = true
        }
      } catch (err) {}
      return ret
    }
  },
    filters: {
    fixedDecimalPlace2
  },
  methods: {
    fixedDecimalPlace2,
    async handleGetTotalAmount () {
      if (this.checkedAllData.length === 0) {
        this.moneySummary = {
          reconciliationAmountRemainingWithTax: 0,
          reconciliationAmountRemainingWithoutTax: 0,
          totalTax: 0
        }
        return
      }
      const res = await getTotalAmount({ basisIds: this.checkedAllData })
      if (res?.status === 200) {
        const { checkTotalAmountTax: reconciliationAmountRemainingWithTax, checkTotalAmount: reconciliationAmountRemainingWithoutTax, taxTotalAmount: totalTax } = res.datas
        this.moneySummary = {
          reconciliationAmountRemainingWithTax,
          reconciliationAmountRemainingWithoutTax,
          totalTax
        }
      } else {
        this.moneySummary = {
          reconciliationAmountRemainingWithTax: 0,
          reconciliationAmountRemainingWithoutTax: 0,
          totalTax: 0
        }
        this.$message.error(res.message || '获取对账总额失败！')
      }
    },
    handleSortChange({ column, prop, order }) {
      // this.searchForm.daysLeftOrder = 0;
      if (prop === 'reconciliationAmountRemaining') {
        this.searchForm.idOrder = ''
        this.searchForm.daysLeftOrder = ''
        this.searchForm.wadatIstOrder = ''
        this.searchForm.reconciliationAmountRemainingOrder = (order === 'ascending' ? 0 : 1)
      }
      if (prop === 'wadatIst') {
        this.searchForm.idOrder = ''
        this.searchForm.daysLeftOrder = ''
        this.searchForm.reconciliationAmountRemainingOrder = ''
        this.searchForm.wadatIstOrder = (order === 'ascending' ? 0 : 1)
      }
      if (prop === 'daysLeft') {
        this.searchForm.idOrder = ''
        this.searchForm.wadatIstOrder = ''
        this.searchForm.reconciliationAmountRemainingOrder = ''
        this.searchForm.daysLeftOrder = (order === 'ascending' ? 0 : 1)
      }
      this.handleFilter();
    },
    beforeUpload (file) {
      if (!this.$validateFileType(file)) return false
      this.importLoading = true
    },
    onUploadSuccess (response) {
      this.importLoading = false
      if (response && response.status === 200) {
        this.$message.success(response.message || '导入成功！')
      } else {
        this.$message.error((response && response.message) || '导入失败！')
      }
    },
    onUploadError (error) {
      console.log(error)
      this.importLoading = false
      this.$message.error((error && error.msg) || (error && error.message) || '上传失败')
    },
    downloadTemplate () {
      this.downloadLoading = true
      downloadBasisTpl()
        .then(res => {
          this.$message.success((res && res.msg) || '下载成功！')
        })
        .catch(err => {
          this.$message.error((err && err.msg) || '下载失败！')
        })
        .finally(() => {
          this.downloadLoading = false
        })
    },
    openImportDialog () {
      this.showImportDialog = true
    },
    closeInvoiceRecordDialog () {
      this.inReRecordShow = false
    },
    openInvoiceDialog () {
      let data = ''
      if (this.checkedAllFlag) {
        data = this.checkedAllData.join(',')
        if (this.checkAllLoading) return this.$message.error('请先完成全选！')
      } else {
        const tb = this.$refs.basisTable
        let selectRows = tb.getSelections()
        if (selectRows.length > 0) {
          const ids = selectRows.map(item => item.id).join(',')
          data = ids
        } else {
          this.$message.error('请选择要生成记录的行！')
          return
        }
      }
      this.basisId = data
      this.invoiceReasonDialog = true
    },
    closeInvoiceDialog () {
      this.invoiceReasonDialog = false
    },
    invoiceDialogDone () {
      this.handleFilter()
    },
    handleManageClick (row) {
      this.inReRecordShow = true
      this.inReRecordShowId = row.id
    },
    async handleSapSelectionChange (selection) {
      if (this.basisList.length === 0) {
        return
      }
      if (this.checkedAllFlag) {
        const diffData = xor(this.multipleSelection, selection)
        let diffIds = diffData.map(item => item.id)
        if (diffData.length > 0 && !this.changePage) {
          if (this.multipleSelection.length > selection.length) {
            this.checkedAllData = pullAll(this.checkedAllData, diffIds)
          } else {
            this.checkedAllData = this.checkedAllData.concat(diffIds)
          }
          this.multipleSelection = selection
        } else if (this.changePage) {
          this.multipleSelection = this.basisList
          const tb = this.$refs.basisTable
          this.basisList.forEach(row => {
            if (this.checkedAllData.indexOf(row.id) === -1 && row.fkstk !== 'C') {
              this.multipleSelection = this.multipleSelection.filter(item => item.id !== row.id)
              tb.toggleRowSelection(row, false)
            }
          })
        } else {
          this.multipleSelection = selection
        }
        this.changePage = false
        this.checkedAllData = [...new Set(this.checkedAllData)]
        this.getTotalAmountDebounced()
      } else {
        this.multipleSelection = selection
        this.calculateMoney();
      }
    },
    calculateMoney() {
      const reconciliationAmountRemainingWithTax = this.getSelectionOfProp('reconciliationAmountRemainingWithTax');
      const reconciliationAmountRemainingWithoutTax = this.getSelectionOfProp('reconciliationAmountRemainingWithoutTax');
      const totalTax = this.getSelectionOfProp('reconciliationAmountRemainingTax');
      this.moneySummary = {
        reconciliationAmountRemainingWithTax,
        reconciliationAmountRemainingWithoutTax,
        totalTax
      };
    },
    getSelectionOfProp(prop) {
      const tb = this.$refs.basisTable
      let selectRows = tb.getSelections()
      return selectRows
        .map((sel) => sel[prop])
        .reduce((x, y) => Number(x) + Number(y), 0);
    },
    customerCodeBlur () {
      console.log(this.searchForm.customerCode)
      this.departmentOptions = []
      this.searchForm.department = ''
      this.searchDemandDepartment(this.searchForm.customerCode)
    },
    searchDemandDepartment (value) {
      if (!value) return
      demandDepartment(value)
        .then(res => {
          if (res && res.status === 200) {
            this.departmentOptions = res.datas
          }
        })
        .catch(err => {
          this.departmentOptions = []
          this.searchForm.department = ''
          this.$message.error(err.msg || err.message || '查询需求部门失败！')
        })
    },
    changeCustomerCode2 (value) {
      console.log(value)
      this.departmentOptions = []
      this.searchForm.department = ''
      this.searchForm.customerCode2 = value
      this.searchDemandDepartment(value)
    },
    showColumnSet () {
      this.columnsConfigDeepCopy = JSON.parse(
        JSON.stringify(this.columnsConfig)
      )

      this.columnsSetVisible = true
    },
    initColumns () {
      try {
        const defaultColumns = getColumns()
        this.defaultColumnsConfig = getConfig('basisColumnsConfig') || createdConfigByColumns(defaultColumns)
        this.setDefaultCache(getColumns())
        if (this.tableServerConfig) {
          const checkResult = checkColumnsConfig(
            this.defaultColumnsConfig,
            this.tableServerConfig
          )
          let columnsConfig = checkResult.config
          if (checkResult.changed) {
            console.log('对比本地不一致, 上传table配置至服务器！')
            saveConfig2Server(columnsConfig).then(() => {
              removeConfig('basisColumnsConfig')
            })
          }
          console.log('从服务端获取table配置并应用！')
          this.columnsConfig = columnsConfig
          this.columns = formatByConfig(this.columnsConfig, getColumns())
        } else {
          console.log('上传table配置至服务器！')
          saveConfig2Server(this.defaultColumnsConfig)
        }
      } catch (error) {
        this.setDefaultCache(getColumns())
      }
    },
    headerDragend (newWidth, oldWidth, column, event) {
      const label = column.label
      newWidth = Math.trunc(newWidth) // 列宽只能允许整数
      console.log(`修改: ${label} width ${oldWidth} -> ${newWidth}`)
      this.columnsConfig.forEach(config => {
        if (config.label === label) {
          config.minWidth = newWidth + 'px'
        }
      })
      this.defaultColumnsConfig = JSON.parse(JSON.stringify(this.columnsConfig))
      // cachedConfig('backlogOrderColumnsConfig', this.columnsConfig)
      console.log('保存table配置到服务器')
      saveConfig2Server(this.defaultColumnsConfig)
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '保存配置失败！')
        })
    },
    columnSet (newConfig) {
      if (newConfig) {
        this.columnsConfig = newConfig
        this.columns = formatByConfig(this.columnsConfig, getColumns())
        // cachedConfig('backlogOrderColumnsConfig', newConfig)
      }
      this.columnsSetVisible = false
      console.log('保存列设置')
      console.log(newConfig)
      saveConfig2Server(newConfig)
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '保存配置失败！')
        })
    },
    setDefaultCache (cols) {
      const config = createdConfigByColumns(cols)
      this.columnsConfig = config
      this.columns = cols
    },
    // 判断某些行能不能选
    checkSelectable (row) {
      // 对账完成的依据无法选中多选框
      if (row.fkstk === 'C') {
        return false
      } else {
        return true
      }
    },
    handleCreateStatement (ids, all) {
      this.$confirm(`${all ? '已全选，' : ''}是否确认生成${all ? '全部' : ''}对账单`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then(() => {
        createStatementByBasisPre({ ids })
          .then(res => {
            if (res.status === 200) {
              const confirmCallback = (content) => () => this.$confirm(content, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                closeOnClickModal: false,
                type: 'warning',
                center: true
              })

              let confirmResult = null;
              if (Array.isArray(res.datas)) {
                confirmResult = () => runPromiseInSeries(res.datas.map((content) => confirmCallback(content)))
              } else {
                confirmResult = confirmCallback(res.datas)
              }

              confirmResult().then(() => {
                createStatementByBasis({ ids })
                  .then(res => {
                    if (res.status === 200) {
                      const data = res.datas
                      this.$router.push(
                        '/account-statement-center/statement/detail/' + data
                      )
                    } else {
                      this.$message.error(res.message)
                    }
                  })
                  .catch(e => {
                    let res = e.response ? e.response.data : {}
                    this.$message.error(res.message || res.msg || res.error || e)
                  })
              }).catch(err => {
                console.error(err)
              })
            } else if (res.status === 500) {
              this.$message.info(res.message)
            } else {
              this.$message.error(res.message || res.msg || '操作失败！')
            }
          })
          .catch(err => {
            this.$message.error(err.message || err.msg || '操作失败！')
          })
      })
    },
    async exportBasis () {
      const columnsToExport = this.columnsConfig.filter((column) => column.visible && column.prop !== 'manage');
      const columns = columnsToExport.map((col) => col.prop);
      let param = { ...this.searchForm, fieldNameList: columns }
      this.trimParams(param)
      let valid = this.formatParams(param)
      if (!valid) return
      this.exportLoading = true
      const data = {
        param: param,
        fileType: FileTypeMapping.CUSTOMER_STATEMENT_BASIS
      }
      let onlyCheckRes = await exportCheck(data, true)
      this.exportLoading = false
      if (onlyCheckRes?.status !== 200) {
        this.$message.error(onlyCheckRes.message || '下载')
        return
      }
      this.$confirm(onlyCheckRes.datas.doubleCheckMsg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        this.exportLoading = true
        let res = await exportCheck(data, false)
        this.exportLoading = false
        if (res.status === 200) {
          this.$message.success((res && res.datas.exportResultMsg) || '导出成功！');
        } else {
          this.$message.error((res && res.message) || '导出失败！');
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消导出!'
        });
      });
    },
    async checkAll () {
      if (this.checkedAllFlag) {
        this.$message.error('当前已经是全选状态！')
        return
      }
      let param = { ...this.searchForm }
      delete param.pageNum
      delete param.pageSize
      if (this.prefixCustomer) {
        param.customerCode = this.prefixCustomer
      }
      if (this.prefixCurrency) {
        param.currency = this.prefixCurrency
      }
      if (this.prefixGroupNo !== undefined) {
        param.parentId = this.prefixGroupNo
      }
      if (this.prefixStatus) {
        param.status = this.prefixStatus
      }
      if (this.prefixStatementNo) {
        param.statementNo = this.prefixStatementNo
      }
      this.formatParams(param)
      let onlyCheckRes = await getSelectAllBySearchFields(param, true)
      this.checkAllLoading = false
      if (onlyCheckRes?.status !== 200) {
        this.$message.error(onlyCheckRes.message || '全选失败！')
        return
      }
      if (!onlyCheckRes.datas.doubleCheckMsg) {
        this.handleCheckRes(onlyCheckRes)
        return
      }
      this.$confirm(onlyCheckRes.datas.doubleCheckMsg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async() => {
          this.handleCheckAll(param)
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消全选!'
          });
        });
    },
    async handleCheckAll (param) {
      this.checkAllLoading = true
      let response = await getSelectAllBySearchFields(param, false)
      this.checkAllLoading = false
      this.handleCheckRes(response)
    },
    handleCheckRes(response) {
      if (response.status !== 200) {
        this.$message.error(response.message || '全选失败！')
        return
      }
      this.$message.success('全选成功！')
      if (!this.checkedAllFlag && this.multipleSelection.length < this.basisList.filter(item => item.fkstk !== 'C').length) {
        const tb = this.$refs.basisTable
        tb.toggleAllSelection()
      }
      this.checkedAllData = response.datas.selectedBasisIds
      this.checkedAllFlag = true
      const { checkTotalAmountTax: reconciliationAmountRemainingWithTax, checkTotalAmount: reconciliationAmountRemainingWithoutTax, taxTotalAmount: totalTax } = response.datas.selectedBasisCalculateResult
      this.moneySummary = {
        reconciliationAmountRemainingWithTax,
        reconciliationAmountRemainingWithoutTax,
        totalTax
      }
    },
    async createStatementAll () {
      try {
        this.createStatementAllLoading = true
        let param = { ...this.searchForm }
        delete param.pageNum
        delete param.pageSize
        this.formatParams(param)
        let response = await getBasisIdsBySearchFields(param)
        if (response && response.status === 200) {
          if (response.datas) {
            this.handleCreateStatement(response.datas)
          } else {
            this.$message.error('没有数据！')
          }
        } else {
          this.$message.error(response.msg || response.message || '查询失败！')
        }
      } catch (err) {
        console.log(err)
        this.$message.error('查询失败！')
      } finally {
        this.createStatementAllLoading = false
      }
    },
    // 生成对账单
    createStatement () {
      if (this.checkedAllFlag) {
        if (this.checkAllLoading) return this.$message.error('请先完成全选！')
        this.handleCreateStatement(this.checkedAllData.join(','), true)
        return
      }
      const tb = this.$refs.basisTable
      let selectRows = tb.getSelections()
      if (selectRows.length > 0) {
        const ids = selectRows.map(item => item.id).join(',')
        this.handleCreateStatement(ids)
      } else {
        this.$message.error('请选择要生成对账单的行')
      }
    },
    // 强制完成
    forceComplete () {
      const tb = this.$refs.basisTable
      let selectRows = tb.getSelections()
      if (selectRows.length > 0) {
        this.$confirm('是否要将选中行强制完成', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning',
          center: true
        }).then(() => {
          const formData = selectRows.map(item => item.id).join(',')
          forceComplate(encodeURIComponent(formData))
            .then(res => {
              if (res.status === 200) {
                this.getBasisList()
              } else {
                this.$message.error(res.message)
              }
            })
            .catch(e => {
              let res = e.response ? e.response.data : {}
              this.$message.error(res.message || res.msg || res.error || e)
            })
        })
      } else {
        this.$message.error('请选择要强制完成的行')
      }
    },
    transArrayToJoin (obj, prop) {
      if (Array.isArray(obj[prop])) {
        obj[prop] = obj[prop].filter(e => e).join(',')
      }
    },
    formatParams (param) {
      if (param.orderNo && param.orderNo.split) {
        let orderNoList = param.orderNo.split(/\s+|,|，/).filter(e => e)
        // if (orderNoList.length > 5000) {
        //   this.$message.error('订单号最多支持5000个！')
        //   return false
        // }
        param.orderNo = orderNoList.join(',')
      }
      if (param.basisNo && param.basisNo.split) {
        let basisNoList = param.basisNo.split(/\s+|,|，/).filter(e => e)
        // if (basisNoList.length > 5000) {
        //   this.$message.error('对账依据编号最多支持5000个！')
        //   return false
        // }
        param.basisNo = basisNoList.join(',')
      }
      if (param.materiel && param.materiel.split) {
        let materielList = param.materiel.split(/\s+|,|，/).filter(e => e)
        if (materielList.length > 500) {
          this.$message.error('产品编号最多支持500个！')
          return false
        }
        param.materiel = materielList.join(',')
      }
      this.transArrayToJoin(param, 'deliveryType')
      this.transArrayToJoin(param, 'orderType')
      this.transArrayToJoin(param, 'status')
      this.transArrayToJoin(param, 'peripheralSystem')
      this.transArrayToJoin(param, 'department')
      // this.transArrayToJoin(param, 'signStatus')
      this.transArrayToJoin(param, 'deliveryStatus')
      this.transArrayToJoin(param, 'productGroup')
      this.transArrayToJoin(param, 'unInvoicedReason')
      this.transArrayToJoin(param, 'unInvoicedReasonDetail')
      this.transArrayToJoin(param, 'unInvoicedResponsible')
      this.transArrayToJoin(param, 'invoicingStatus')

      param.demandDepartment = param.department
      delete param['department']
      return true
    },
    trimParams (obj) {
      for (let key in obj) {
        if (obj[key] && obj[key].trim) {
          obj[key] = obj[key].trim()
        }
      }
    },
    getBasisList () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.trimParams(this.searchForm)
          let param = { ...this.searchForm }
          const query = {
            pageNum: this.listQueryInfo.current,
            pageSize: this.listQueryInfo.pageSize
          }
          this.basisList = []
          if (this.prefixCustomer) {
            param.customerCode = this.prefixCustomer
          }
          if (this.prefixCurrency) {
            param.currency = this.prefixCurrency
          }
          if (this.prefixGroupNo !== undefined) {
            param.parentId = this.prefixGroupNo
          }
          if (this.prefixStatus) {
            param.status = this.prefixStatus
          }
          if (this.prefixStatementNo) {
            param.statementNo = this.prefixStatementNo
          }
          let valid = this.formatParams(param)
          if (!valid) return
          this.basisListLoading = true
          getBasisList(query, param)
            .then(res => {
              this.basisListLoading = false
              if (res.status === 200) {
                if (res.datas) {
                  const data = res.datas
                  this.total = data.total
                  this.basisList = data.list
                  if (this.checkedAllFlag) {
                    const tb = this.$refs.basisTable
                    tb.toggleAllSelection()
                  }
                }
              } else {
                this.$message.error(res.message)
              }
            })
            .catch(() => {
              this.basisListLoading = false
            })
        }
      })
    },
    // 查询按钮
    handleFilter () {
      this.listQueryInfo.current = 1
      this.checkedAllFlag = false
      this.moneySummary = {
        reconciliationAmountRemainingWithTax: 0,
        reconciliationAmountRemainingWithoutTax: 0,
        totalTax: 0
      }
      this.multipleSelection = []
      this.getBasisList()
    },
    // 重置按钮
    handleReset () {
      this.searchForm = getInitSearchParams()
      this.checkedAllFlag = false
      if (this.condition) {
        this.searchForm = { ...this.searchForm, ...this.condition }
      }
      this.departmentOptions = []
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerListByPartName({
          customerName: key
        }).then(res => {
          this.customerNoLoading = false
          if (res.status === 200) {
            if (res.datas && res.datas.length > 0) {
              this.customerNoOptions = res.datas.map(item => {
                return {
                  value: item.customerNumber,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    }
  }
}
</script>

<style lang="scss" scope></style>
<style lang="scss">
.account-statement-container {
  .filter-container {
    padding-top: 18px;
    padding-right: 10px;
    background-color: #f4f4f4;
    // .el-form-item {
    //   margin-bottom: 10px;
    // }
  }
  .tag-container {
    padding-left: 10px;
    padding-bottom: 5px;
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .customer-tips {
    background: #ffffff;
    margin-top: 15px;
    margin-bottom: 15px;
    padding-left: 0;
    font-size: 14px;
    span {
      text-align: center;
      color: red;
      font-size: 14px;
    }
  }
  .backlog-search-result-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin: 10px 0;
    & > {
      .result-title {
        height: 50px;
        padding: 0 10px 0 20px;
        line-height: 50px;
        font-size: 16px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
  .statistics {
    height: 40px;
    line-height: 50px;
    span {
      margin-right: 20px;
    }
  }
}
</style>
