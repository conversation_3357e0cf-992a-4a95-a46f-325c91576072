<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" v-if="type != 1">
        <div class="logistics_con">
          <el-row class="main_info">
            <el-col :span="24">
              <span class="nav-to-express" @click="toDnDetail(outboundInfo.dnNo)">DN：{{ outboundInfo.dnNo }}</span>
              <span>当前状态：<em>{{ outboundInfo.curStatus }}</em></span>
              <span>下一节点：<em>{{ outboundInfo.nextStatus }}</em></span>
            </el-col>
          </el-row>
          <el-row class="logistics_main">
            <template v-for="(node, index) in nodeList">
              <el-col
                :key="'node' + index"
                :span="2"
                class="node"
                :class="['node0' + (index + 1), { active: node.nodeReach }]">
                <span class="warehouse">{{ node.warehouseName }}</span>
                <span class="icon" />
                <em class="status">{{ node.nodeName }}</em>
                <span class="date">{{ node.nodeTime }}</span>
              </el-col>
              <el-col
                v-show="index != nodeList.length - 1"
                :key="'arrow' + index"
                :span="2"
                class="arrow">
                <img
                  v-show="
                    node.nodeReach &&
                      nodeList[index + 1] &&
                      nodeList[index + 1].nodeReach
                  "
                  :src="images.arrow03"
                />
                <img
                  v-show="
                    node.nodeReach &&
                      !(nodeList[index + 1] && nodeList[index + 1].nodeReach)
                  "
                  :src="images.arrow02"
                />
                <img v-show="!node.nodeReach" :src="images.arrow01" />
              </el-col>
            </template>
          </el-row>
        </div>
      </el-col>
    </el-row>

    <el-row class="logistics_detail_con">
      <iframe :src="`${logisticsUrl}/sr/logistics?soNo=${soNo}&deliveryNo=${deliveryNo}`" frameborder="0" width="100%" height="400px" style="display:block;"></iframe>
      <!-- <template v-if="this.type != 1">
      </template>
      <template v-else>
        <h3>
          该订单被<em>【{{ waybillInfo.carrierName }}】</em>承运，运单号：<em>{{ waybillInfo.waybillList }}</em>
        </h3>
        <el-col :span="24">
          <ul>
            <li
              v-for="(log, index) in logisticsList"
              :key="index"
              :class="index === 0 ? 'active' : ''">
              <em class="time">{{ log.trackTime }}</em>
              <div class="remark" :class="{ bold: log.bold }">
                <span v-show="log.type == 1">【{{ log.address }}】</span
                >{{ log.remark }}
              </div>
            </li>
          </ul>
        </el-col>
      </template> -->
    </el-row>

    <el-row class="list_con" v-if="type != 1">
      <el-col :span="24">
        <h2>出库详情</h2>
        <el-table
          v-loading="listLoading"
          :data="detailListData"
          border
          fit
          highlight-current-row
          stripe
          style="width: 100%">
          <el-table-column align="center" label="行号">
            <template slot-scope="scope">
              <span>{{ scope.row.lineNum }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品编码">
            <template slot-scope="scope">
              <span>{{ scope.row.productNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品名称">
            <template slot-scope="scope">
              <span>{{ scope.row.productName }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="出库数量">
            <template slot-scope="scope">
              <span>{{ scope.row.outboundQuantity }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="detailListData[0] && detailListData[0].batchNumber"
            align="center"
            label="批次号"
          >
            <template slot-scope="scope">
              <span>{{ scope.row.batchNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="质量状态">
            <template slot-scope="scope">
              <span>{{ scope.row.qualityStatus }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="detailListData[0] && detailListData[0].supplierBatch"
            align="center"
            label="供应商批次">
            <template slot-scope="scope">
              <span>{{ scope.row.supplierBatch }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="首批次">
            <template slot-scope="scope">
              <span>{{ scope.row.firstBatch }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.pageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="getOutboundDetailList"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination/index.vue'

import {
  fetchOutBoundDetailList,
  fetchNodeStatus,
  fetchSupplierDirectLogisticsDetail
} from '@/api/orderSupplyList'

import { api } from '@/api/boss'

import arrow01 from '@/assets/orderSupplyList/arrow01.png'
import arrow02 from '@/assets/orderSupplyList/arrow02.png'
import arrow03 from '@/assets/orderSupplyList/arrow03.png'

export default {
  name: 'warehouseDetail',
  components: {
    Pagination
  },
  filters: {},
  data () {
    return {
      listLoading: false,
      outboundInfo: {}, // 出库单信息，包括快递公司名称、DN单号等
      waybillInfo: {}, // 运单
      nodeList: [], // 仓内流转记录
      outboundNo: this.$route.query.outboundNo, // 出库单号
      omsDeliveryNo: this.$route.query.omsDeliveryNo, // 出库单号（新接口oms出库单号）
      dnId: this.$route.query.dnId,
      type: this.$route.query.type, // 0 非直发 、1 供应商直发
      listQuery: {
        pageSize: 10,
        current: 1
      },
      total: 0,
      detailListData: [], // 出库单详情列表数据
      logisticsList: [], // 物流轨迹详情
      images: {
        arrow01: arrow01,
        arrow02: arrow02,
        arrow03: arrow03
      },
      detailRetry: true,
      nodeRetry: true,
      nodeNo: this.$route.query.outboundNo,
      detailNo: this.$route.query.outboundNo,
      soNo: this.$route.query.soNo,
      deliveryNo: this.$route.query.deliveryNo,
      khCode: 0,
      logisticsUrl: /local/.test(window.location.href) ? 'http://local.zkh360.com:5174' : window.location.origin
    }
  },
  created () {
    if (!this.outboundNo && !this.omsDeliveryNo) {
      this.$notify.error('缺少必要参数！')
      return
    }
    // 出库单详情
    if (this.type + '' !== '1') {
      // 流程 列表
      this.getNodeStatus()
      this.getOutboundDetailList()
    }
    // 物流信息
    // this.getLogisticsDetail()
    // this.getCode()
    this.khCode = this.omsDeliveryNo
  },
  methods: {
    toDnDetail (no) {
      if (!no) return
      this.$router.push({
        path: `/orderDelivery/detail/${no}`
      })
    },
    getCode () {
      api({
        prefix: '/api-logistics',
        url: '/transportInfo/queryMulti',
        method: 'POST',
        data: [this.omsDeliveryNo || this.outboundNo],
        complete: res => {
          console.log(res)
          if (res.success && res.result && res.result.length) {
            const [ data ] = res.result
            this.khCode = data && data.orderNo
          }
        }
      })
    },
    // 查询出库单详情列表
    getOutboundDetailList () {
      this.listLoading = true
      fetchOutBoundDetailList(this.listQuery, this.detailNo)
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.detailListData = response.data
              this.total = response.totalCount
            }
          } else {
            this.$notify.error(response.msg)
          }
        })
        .finally(() => {
          this.listLoading = false
          // strange logic, if dnNo returns [] then fetch with omsDeliveryNo
          if (this.detailListData.length === 0 && this.detailRetry) {
            this.detailRetry = false
            this.detailNo = this.omsDeliveryNo || this.outboundNo
            this.getOutboundDetailList()
          }
        })
    },
    // 查询仓内物流轨迹
    getNodeStatus () {
      fetchNodeStatus(this.nodeNo)
        .then(response => {
          if (response.code === 200) {
            this.outboundInfo = response.data
            this.nodeList = response.data && response.data.nodeList
          } else {
            this.$notify.error(response.msg)
          }
        })
        .finally(() => {
          // strange logic, if dnNo returns [] then fetch with omsDeliveryNo
          if (!this.outboundInfo.curStatus && !this.outboundInfo.carrierName && this.nodeRetry) {
            this.nodeRetry = false
            this.nodeNo = this.omsDeliveryNo || this.outboundNo
            this.getNodeStatus()
          }
        })
    },
    // 查询物流轨迹详情
    getLogisticsDetail () {
      fetchSupplierDirectLogisticsDetail(this.outboundNo).then(response => {
        if (response.code === 200) {
          const { carrierName, arrivalTime, packageQuantity, waybillList } = {
            ...response.data
          }
          this.waybillInfo = {
            carrierName,
            arrivalTime,
            packageQuantity,
            waybillList
          }

          this.logisticsList = response.data && response.data.contList
        } else {
          this.$notify.error(response.msg)
        }
      })
    }
  }
}
</script>

<style scoped>
/*  物流节点 */
.logistics_con {
  font-size: 12px;
  color: #333333;
  height: 220px;
  background-color: #ffffff;
  border: solid 1px #dfe4ed;
  padding: 0 15px;
}
.logistics_con em {
  font-weight: bold;
  font-style: normal;
}
.logistics_con .main_info {
  margin-top: 12px;
}
.logistics_con .main_info span {
  margin-right: 20px;
}
.logistics_con .dnNo {
  font-size: 12px;
}
.logistics_main {
  margin: 25px 0;
}
.logistics_con .node {
  min-width: 75px;
}
.logistics_con .node * {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #333;
  line-height: 1;
  white-space: nowrap;
}
.logistics_con .node .icon {
  width: 45px;
  height: 45px;
  background: #e2e2e2 url('~@/assets/orderSupplyList/logistics_icons.png')
    no-repeat;
  border-radius: 100%;
  margin: 10px auto;
}
.logistics_con .node.active .icon {
  background-color: #42b983;
  background-position-y: -45px;
}
.logistics_con .node01 .icon {
  background-position: 0 0;
}
.logistics_con .node02 .icon {
  background-position: -45px 0;
}
.logistics_con .node03 .icon {
  background-position: -90px 0;
}
.logistics_con .node04 .icon {
  background-position: -135px 0;
}
.logistics_con .node05 .icon {
  background-position: -180px 0;
}
.logistics_con .node .date {
  color: #666666;
  margin-top: 10px;
}
.logistics_con .arrow {
  min-width: 104px;
  line-height: 96px;
  height: 111px;
}
.detail_info {
  color: #666666;
  line-height: 20px;
}
.detail_info i {
  color: #1890ff;
  font-style: normal;
}
.detail_info em {
  color: #333333;
}
/* 前置单号列表 */
.list_con {
  margin-top: 5px;
}
.list_con h2 {
  font-size: 16px;
  color: #666666;
  font-weight: bold;
}
/* 物流轨迹详情 */
.logistics_detail_con {
  /* height: 360px; */
  background-color: #ffffff;
  border: solid 1px #e2e2e2;
  margin-top: 20px;
}
.logistics_detail_con h3 {
  background-color: #f4f4f4;
  font-size: 12px;
  color: #666666;
  padding: 13px 10px;
  font-weight: normal;
  margin: 0;
  line-height: 1.25;
}
.logistics_detail_con h3 em {
  color: #333333;
  font-style: normal;
  font-weight: bold;
}
.logistics_detail_con ul {
  max-height: 317px;
  overflow-y: auto;
  margin: 0;
  padding: 10px 20px;
}
.logistics_detail_con li {
  font-size: 12px;
  color: #333333;
  position: relative;
  padding-left: 150px;
}
.logistics_detail_con li .time {
  color: #666666;
  font-style: normal;
  position: absolute;
  top: 13px;
  left: 0;
}
.logistics_detail_con li .remark {
  border-left: 1px dashed #bdbdbd;
  padding: 13px 10px 13px 50px;
}
.logistics_detail_con li .bold {
  font-weight: bold;
}
.logistics_detail_con li .remark::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 146px;
  width: 9px;
  height: 9px;
  background-color: #cecece;
  border-radius: 100%;
}
.logistics_detail_con li.active .remark::before {
  background-color: #1890ff;
}
.nav-to-express{
  cursor: pointer;
  color: steelblue;
}
</style>
