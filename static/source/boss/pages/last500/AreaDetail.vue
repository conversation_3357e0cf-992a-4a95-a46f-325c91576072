<template>
  <div class="page page-last500-area" v-loading="loading.page">

    <div class="module-filter">
      <div class="info-group">
        <div class="info bg">
          <p><i class="el-icon-location-information"></i></p>
          <p class="badge">{{ info.province }}</p>
          <p class="badge">{{ info.city }}</p>
          <p class="badge">{{ info.region }}</p>
        </div>
        <div class="info" v-show="manager.nickName">
          <p class="badge"><strong>所属大区：{{ manager.managerAreaName }}</strong></p>
          <p class="badge"><strong>区域负责人：{{ manager.nickName }}</strong></p>
        </div>
      </div>
      <div class="btn-groups">
        <el-button type="primary" @click="refresh">刷新</el-button>
        <el-button type="primary" @click="create">新增</el-button>
      </div>
    </div>

    <div class="module-list">
      <el-table :data="list" style="width: 100%">
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="nickName" label="姓名"></el-table-column>
        <el-table-column prop="phone" label="电话"></el-table-column>
        <el-table-column prop="dimension" label="命中维度">
          <template slot-scope="{ row }">
            {{ map.dimension[row.dimension] }} {{ row.dimension == 1 ? row.staffCustomers.map(item =>
            item.customerCode).join(', '):'' }}
          </template>
        </el-table-column>
        <el-table-column width="80px" label="状态">
          <template slot-scope="{ row }">
            <el-switch v-model="row.status" active-color="#13ce66" inactive-color="#ff4949" @change="afterSwitch(row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column width="150px" label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" @click="edit(row)" v-show="row.status">编辑</el-button>
            <el-button type="text" :loading="claimLoadingStatus[row.id]" @click="claim(row)"
              v-show="row.status&&row.dimension == 1">认领交货单</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="filter.page" :page-sizes="[10, 20, 50]" :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>

    <el-dialog title="新增" :visible.sync="show.create" custom-class="dialog-last500">
      <el-form :model="customer" :rules="rules" ref="form" v-loading="loading.staff">
        <el-form-item label="账号" prop="name">
          <el-autocomplete v-model="customer.name" :fetch-suggestions="querySearchAsync" placeholder="请输入内容"
            @select="handleSelectUser"></el-autocomplete>
        </el-form-item>
        <el-form-item label="姓名">
          <span class="grey">{{ customer.nickName }}</span>
        </el-form-item>
        <el-form-item label="电话">
          <span class="grey">{{ customer.phone }}</span>
        </el-form-item>
        <el-form-item label="命中维度" prop="line">
          <el-radio :disabled="secondDeliverStatus" v-model="customer.dimension" :label="0">区域随机</el-radio>
          <el-radio v-model="customer.dimension" :label="1">指定客户</el-radio>
        </el-form-item>

        <div class="module-customer-list" v-show="show.customer && customer.dimension === 1">
          <span>添加客户 </span>
          <customer-select v-model="form.name" @select="afterSelectCustomer" class="module-customer-select">
          </customer-select>
          <el-table :data="customer.staffCustomers" style="width: 100%">
            <el-table-column prop="customerCode" label="客户编码"></el-table-column>
            <el-table-column prop="customerName" label="客户名称"></el-table-column>
            <el-table-column label="收货人为交付主管">
              <template slot-scope="scope">
                <el-checkbox @change="$event => handleDHChange(scope)" v-model="scope.row.deliveryHead" :true-label="1" :false-label="2"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="坤合面单显示">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.sheetDisplay"
                  placeholder="请选择"
                  :disabled="scope.row.deliveryHead === 1"
                >
                  <el-option :value="2" label="客户" />
                  <el-option :value="1" label="交付主管" />
                </el-select>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <a href="javascript:;" @click="remove(scope)">删除</a>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <el-form-item v-if="customer.dimension" label="第二交付主管" class="mt-20">
          <el-select
            v-model="contacts"
            placeholder="请选择联系人"
            multiple
            filterable
            remote
            reserve-keyword
            @change="selectContacts"
            :remote-method="remoteMethod">
            <el-option
              v-for="item in contactsOptions"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="save">保存</el-button>
      </p>
    </el-dialog>
  </div>
</template>

<script>
import kunheAssistantApi from '@/api/kunheAssistant'
import CustomerSelect from '@/components/boss/CustomerSelect'

export default {
  data() {
    return {
      type: 'create',
      id: 0,
      filter: {
        serviceAreaId: 0,
        page: 0,
        pageSize: 10
      },
      info: {},
      manager: {},
      account: {},
      claimLoadingStatus: {},
      list: [],
      total: 0,
      customer: {
        name: '',
        nickName: '',
        phone: '',
        userId: '',
        dimension: 0,
        serviceAreaId: 0,
        staffCustomers: []
      },
      form: {
        name: ''
      },
      map: {
        dimension: {
          0: '区域随机',
          1: '指定客户'
        }
      },
      rules: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      current: {
        staff: 0
      },
      loading: {
        page: false,
        staff: false
      },
      show: {
        customer: false,
        create: false
      },
      contactsOptions: [],
      contacts: '',
      contactsList: [],
      secondDeliverStatus: false
    }
  },
  components: {
    CustomerSelect
  },
  computed: {
    isCreate() {
      return this.type === 'create'
    }
  },
  methods: {
    create() {
      this.show.create = true
      this.show.customer = false
      this.customer = {
        name: '',
        nickName: '',
        phone: '',
        userId: '',
        dimension: 0,
        serviceAreaId: 0,
        staffCustomers: []
      }
      this.type = 'create'
    },
    claim(row) {
      const customerString = row.staffCustomers.map(item => item.customerCode).join(', ')
      this.$confirm(`确认将 ${customerString} 在 ${this.info.province} ${this.info.city} ${this.info.region} 的交货单认领给 ${row.nickName} 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$set(this.claimLoadingStatus, row.id, true)
        kunheAssistantApi({
          url: '/staff/claim?staffConfigId=' + row.id,
          method: 'POST',
          complete: (res) => {
            this.$set(this.claimLoadingStatus, row.id, false)
            if (res.code === 200) {
              console.log(res.data)
              this.getStaffs(this.id)
              this.$message({
                type: 'success',
                message: '认领成功!'
              });
            } else {
              this.$message.error(res.msg || res.message || '认领失败！')
            }
          }
        })
      }).catch(() => {

      });
    },
    edit(row) {
      this.show.create = true
      this.show.customer = true
      this.type = 'update'
      this.current.staff = row.id
      this.getStaff(row.id)
      // 第二责任人
      this.secondDeliverStatus = false
      this.contactsOptions = []
      this.contactsList = []
      this.contacts = []
      this.getSecondDelivery(row.id)
    },
    getSecondDelivery () {
      kunheAssistantApi({
        url: '/staff_config_second_delivery/second_delivery_details?staffConfigId=' + this.current.staff,
        method: 'GET',
        complete: (res) => {
          if (res.code === 200) {
            const data = res.data
            this.idMap = {}
            this.secondDeliverStatus = true
            data.forEach(item => {
              if (!this.contacts.includes(item.userId)) {
                this.contacts.push(item.userId)
                this.contactsList.push(item)
                this.contactsOptions.push(item)
              }
              this.idMap[`${item.customerCode}_${item.userId}`] = item.id
            })
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
        }
      }
    })
    },
    save() {
      this.customer.serviceAreaId = this.id
      this.$refs.form.validate(valid => {
        if (valid) {
          kunheAssistantApi({
            url: this.isCreate ? '/staff' : `/staff/${this.current.staff}`,
            data: this.customer,
            method: this.isCreate ? 'POST' : 'PUT',
            complete: (res) => {
              this.show.create = false
              if (res.code === 200) {
                console.log(res.data)
                this.getStaffs(this.id)
              } else {
                this.$message.error(res.msg || res.message || '获取失败！')
              }
            }
          })
          if (this.customer.dimension === 1) {
            this.updateSecondDelivery()
          }
        }
      })
    },
    selectContacts (value) {
      let key = value[value.length - 1]
      this.contactsOptions.forEach(item => {
        if (item.userId === key) {
          this.contactsList.push(item)
        }
      })
    },
    updateSecondDelivery() {
      const data = []
      this.contactsList.forEach(item => {
        if (this.customer.staffCustomers.length) {
          this.customer.staffCustomers.forEach(item2 => {
            data.push({
              customerCode: item2.customerCode,
              customerName: item2.customerName,
              name: item.name,
              nickName: item.nickName,
              phone: item.phone,
              serviceAreaId: this.filter.serviceAreaId,
              status: 1,
              userId: item.userId,
              staffConfigId: this.current.staff,
              id: this.idMap[`${item2.customerCode}_${item.userId}`]
            })
          })
        } else {
          data.push({
            customerCode: null,
            customerName: null,
            name: item.name,
            nickName: item.nickName,
            phone: item.phone,
            serviceAreaId: this.filter.serviceAreaId,
            status: 1,
            userId: item.userId,
            staffConfigId: this.current.staff,
            id: null
          })
        }
      })
      kunheAssistantApi({
        url: `/staff_config_second_delivery/add_or_update?staffConfigId=${this.current.staff}`,
        data: {
          updateList: data
        },
        method: 'POST',
        complete: (res) => {
          console.log(res);
        }
      })
    },
    remove(scope) {
      this.customer.staffCustomers.splice(scope.$index, 1)
    },
    handleDHChange(scope) {
      console.log();
      this.customer.staffCustomers.forEach(c => {
        if (c.customerCode === scope.row.customerCode && scope.row.deliveryHead === 1) {
          c.sheetDisplay = 1
        }
      })
    },
    afterSelectCustomer(customer) {
      if (!customer) return
      const duplicated = this.customer.staffCustomers.find(item => item.customerCode === customer.customerCode)
      if (duplicated) return
      customer.deliveryHead = 2
      customer.sheetDisplay = 1
      this.customer.staffCustomers.push(customer)
    },
    querySearchAsync(qs, cb) {
      const opts = []
      if (!qs) {
        return cb(opts)
      }
      this.getAccount(qs, res => {
        // handle res
        res && res.forEach(item => {
          item.value = item.name
        })
        cb(res)
      })
    },
    remoteMethod(qs) {
      this.querySearchAsync(qs, (list) => {
        this.contactsOptions = list
      })
    },
    getAccount(name = '', callback) {
      kunheAssistantApi({
        url: '/staff/domain/user',
        query: {
          name
        },
        method: 'GET',
        complete: (res) => {
          if (res.code === 200) {
            console.log(res.data)
            callback && callback(res.data)
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    handleSelectUser(item) {
      console.log(item)
      this.show.customer = true
      Object.assign(this.customer, {
        nickName: item.nickName,
        phone: item.phone,
        userId: item.userId
      })
    },
    getStaff(id) {
      this.loading.staff = true
      kunheAssistantApi({
        url: `/staff/${id}`,
        method: 'GET',
        complete: (res) => {
          this.loading.staff = false
          if (res.code === 200) {
            console.log(res.data)
            this.customer = res.data
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    getStaffs(id) {
      this.filter.serviceAreaId = id
      kunheAssistantApi({
        url: '/staff',
        query: this.filter,
        method: 'GET',
        complete: (res) => {
          if (res.code === 200) {
            console.log(res.data)
            res.data.forEach(item => {
              item.status = !!item.status
            })
            this.list = res.data
            this.total = res.totalCount
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    handleSizeChange(res) {
      this.filter.pageSize = res
      this.refresh()
    },
    handleCurrentChange(res) {
      this.filter.page = res
      this.refresh()
    },
    afterSwitch(row) {
      kunheAssistantApi({
        url: `/staff/${row.id}/status`,
        method: 'PUT',
        complete: (res) => {
          if (res.code === 200) {
            this.$message.success('更新成功')
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    getInfo(id, callback) {
      this.loading.page = true
      kunheAssistantApi({
        url: `/area/${id}`,
        method: 'GET',
        complete: (res) => {
          this.loading.page = false
          if (res.code === 200) {
            console.log(res.data)
            this.info = res.data
            this.getManager(this.info.province)
            callback && callback()
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    getManager(province) {
      kunheAssistantApi({
        url: '/area/manager',
        query: {
          provinceName: province
        },
        complete: res => {
          if (res.code === 200) {
            const [manager] = res.data
            this.manager = manager
          } else {
            console.log(res)
          }
        }
      })
    },
    refresh() {
      this.getInfo(this.id, () => {
        this.getStaffs(this.id)
      })
    }
  },
  mounted() {
    this.id = this.$route.params.id
    this.refresh()
  }
}
</script>

<style lang="less" src="@/style/component.less">

</style>
<style lang="less">
.page-last500-area {
  padding: 30px;

  .module-filter {
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .module-customer-select {
    margin-bottom: 20px;
  }

  .cell {
    @color: #506fd6;

    a {
      color: @color;

      &:hover {
        color: lighten(@color, 10%);
      }
    }
  }

  .dialog-last500 {
    .el-form {
      margin: 0 20px;
    }

    .grey {
      background-color: #f6f6f6;
      padding: 8px;
      border-radius: 3px;
    }
  }

  .info-group {
    display: flex;
  }

  .info {
    display: flex;
    padding: 10px;
    border-radius: 3px;
    margin-right: 10px;

    &.bg {
      background-color: #f6f6f6;
    }

    p {
      margin-right: 20px;
    }
  }
  .mt-20 {
    margin-top: 20px;
  }
}
</style>
