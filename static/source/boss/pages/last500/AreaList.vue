<template>
  <div class="page page-last500-areas">
    <div class="module-filter">
      <el-form :inline="true">
        <el-form-item label="省">
          <el-select v-model="filter.province" placeholder="请选择" @change="afterChangeProvince" clearable>
            <el-option v-for="item in option.province" :key="item.id" :label="item.province" :value="item.province"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="市" v-show="filter.province">
          <el-select v-model="filter.city" placeholder="请选择" @change="afterChangeCity" clearable>
            <el-option v-for="item in cityOption" :key="item.id" :label="item.city" :value="item.city"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区" v-show="filter.city">
          <el-select v-model="filter.region" placeholder="请选择" @change="afterChangeRegion" clearable>
            <el-option v-for="item in regionOption" :key="item.id" :label="item.region" :value="item.region"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filter.status" placeholder="请选择" @change="afterChangeStatus">
            <el-option v-for="item in option.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="search">搜索</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="module-list" v-loading="loading.list">
      <el-table :data="list" style="width: 100%">
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="province" label="省"></el-table-column>
        <el-table-column prop="city" label="市"></el-table-column>
        <el-table-column prop="region" label="区"></el-table-column>
        <el-table-column width="80px" label="状态">
          <template slot-scope="{ row }">
            <el-switch v-model="row.status" active-color="#13ce66" inactive-color="#ff4949" @change="afterSwitch(row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column width="80px" label="操作">
          <template slot-scope="{ row }">
            <router-link :to="{ path: `/last500/area/${row.id}`}" v-show="row.status">编辑</router-link>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.page"
        :page-sizes="[10, 20, 50]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import api from '@/api/kunheAssistant'

export default {
  data () {
    return {
      filter: {
        province: null,
        city: null,
        region: null,
        status: null,
        page: 0,
        pageSize: 20
      },
      loading: {
        list: false
      },
      option: {
        province: [],
        status: [
          { label: '全部', value: null },
          { label: '生效', value: 1 },
          { label: '失效', value: 0 }
        ]
      },
      list: [],
      total: 0
    }
  },
  computed: {
    cityOption () {
      const province = this.filter.province
      if (!province) return []
      const city = this.option.province.find(item => item.province === province)
      return city ? city.serviceAreaCityResults : []
    },
    regionOption () {
      const city = this.filter.city
      if (!city) return []
      const region = this.cityOption.find(item => item.city === city)
      console.log(region)
      return region ? region.serviceAreaRegionResultList : []
    }
  },
  methods: {
    boolize (val) {
      return !!val
    },
    afterSwitch (row) {
      api({
        url: `/area/${row.id}/status`,
        method: 'PUT',
        complete: (res) => {
          if (res.code === 200) {
            this.$message.success('更新成功')
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    getOption () {
      api({
        url: '/area/init',
        method: 'GET',
        complete: (res) => {
          if (res.code === 200) {
            this.option.province = res.data
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    search () {
      this.loading.list = true
      this.filter.province = this.filter.province || null
      this.filter.city = this.filter.city || null
      this.filter.region = this.filter.region || null
      api({
        url: '/area',
        query: this.filter,
        method: 'GET',
        complete: (res) => {
          this.loading.list = false
          if (res.code === 200) {
            console.log(res.data)
            res.data.forEach(item => {
              item.status = !!item.status
            })
            this.list = res.data
            this.total = res.totalCount
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.page = res
      this.search()
    },
    afterChangeProvince () {
      this.filter.city = null
      this.filter.region = null
      this.filter.page = 0
    },
    afterChangeCity () {
      this.filter.region = null
      this.filter.page = 0
    },
    afterChangeRegion () {
      this.filter.page = 0
    },
    afterChangeStatus () {
      this.filter.page = 0
    }
  },
  mounted () {
    this.search()
    this.getOption()
  }
}
</script>

<style lang="less" src="@/style/component.less"></style>
<style lang="less">
  .page-last500-areas {
    padding: 30px;
    .cell {
      @color: #506fd6;
      a {
        color: @color;
        &:hover {
          color: lighten(@color, 10%);
        }
      }
    }
  }
</style>
