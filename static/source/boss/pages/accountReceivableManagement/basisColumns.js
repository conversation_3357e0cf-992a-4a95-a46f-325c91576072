
import { deepClone } from '@/utils/index.js'
import { fixedDecimalPlace } from '@/filters/index.js'
import {
  getStatusOptions,
  getDeliveryStatusOptionsOptions,
  getInvoicingStatusOptions,
  getUnInvoicedResponsibleOptions,
  includeTaxList
} from '@/api/accountStatement.js'

let statusOptions = []
getStatusOptions().then(data => {
  statusOptions.push(...data)
})

let deliveryStatusOptionsOptions = []
getDeliveryStatusOptionsOptions().then(data => {
  deliveryStatusOptionsOptions.push(...data)
  deliveryStatusOptionsOptions.shift()
})
let invoicingStatusOptions = []
getInvoicingStatusOptions().then(data => {
  invoicingStatusOptions.push(...data)
})
let includeTaxOptions = []
includeTaxOptions.push(...includeTaxList)
includeTaxOptions.shift()

let unInvoicedResponsibleOptions = []
getUnInvoicedResponsibleOptions().then(data => {
  unInvoicedResponsibleOptions.push(...data)
})
const fixedDecimalPlace2 = fixedDecimalPlace.bind(null, 2)
const fixedDecimalPlace3 = fixedDecimalPlace.bind(null, 3)
// const deliveryStatusOptions = {
//   B: '部分交货',
//   C: '已交货'
// }
const signStatusOptions = {
  0: '未签收',
  1: '已签收'
}
const columns = [
  { label: '开票依据编号', prop: 'zvbeln', type: 'default', visible: true, fixed: true, sortable: false, visibleSwitch: false, minWidth: '100px' },
  { label: '开票依据行编号', prop: 'posnr', type: 'default', visible: true, fixed: true, sortable: false, visibleSwitch: false, minWidth: '110px' },
  { label: '对账状态', prop: 'status', type: 'enum', visible: true, fixed: true, sortable: false, visibleSwitch: false, minWidth: '80px', enmus: statusOptions },
  { label: '客户', prop: 'customerCode', type: 'default', visible: true, fixed: true, sortable: false, visibleSwitch: false, minWidth: '80px' },
  { label: '客户名称', prop: 'customerName', type: 'default', visible: true, fixed: true, sortable: false, visibleSwitch: false, minWidth: '80px' },
  { label: '销售订单描述', prop: 'orderTypeDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货类型描述', prop: 'lfartT', type: 'default', visible: true, minWidth: '120px' },
  // { label: '交货状态', prop: 'deliveryStatus', type: 'default', visible: true, minWidth: '120px', labelFormat (val) { return deliveryStatusOptions[val] } },
  { label: '客户是否签收', prop: 'signStatus', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return signStatusOptions[val] } },
  { label: '交货状态', prop: 'deliveryStatus', type: 'enum', visible: true, minWidth: '120px', enmus: deliveryStatusOptionsOptions },
  { label: '开票状态', prop: 'fkstk', type: 'enum', visible: true, minWidth: '120px', enmus: invoicingStatusOptions },
  { label: '销售订单号', prop: 'vgbel', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售订单行号', prop: 'vgpos', type: 'default', visible: true, minWidth: '120px' },
  { label: 'oms交货单号', prop: 'omsDnNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'oms交货单行号', prop: 'omsDnItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'oms订单号', prop: 'omsSoNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'oms订单行号', prop: 'omsSoItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户订单号', prop: 'customerReferenceNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '产品编号', prop: 'materiel', type: 'default', visible: true, minWidth: '120px' },
  { label: '产品描述', prop: 'sapMaterialName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售单位', prop: 'salesUnit', type: 'default', visible: true, minWidth: '120px' },
  { label: '库存地点描述', prop: 'locationDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料号', prop: 'zmatnr1', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户品名', prop: 'zmaktx1', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户规格型号', prop: 'zkhggxh', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料数量', prop: 'zkwmeng', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '客户物料单价', prop: 'znetpr', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '客户物料单位', prop: 'zmeins1', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货数量', prop: 'lgmng', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '交货总金额', prop: 'deliveryTotalAmount', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '已确认应收数量', prop: 'confirmedReceivableQuantity', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '已确认应收金额', prop: 'confirmedReceivableAmount', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '应收确认中数量', prop: 'confirmingReceivableQuantity', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '应收确认中金额', prop: 'confirmingReceivableAmount', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '已完成对账数量', prop: 'completedReconciliationQuantity', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '已完成对账金额', prop: 'completedReconciliationAmount', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '对账中数量', prop: 'reconciliationQuantity', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '对账中金额', prop: 'reconciliationAmount', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '剩余对账数量', prop: 'reconciliationQuantityRemaining', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '剩余对账金额', prop: 'reconciliationAmountRemaining', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '盈亏数量', prop: 'profitLossQuantity', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '盈亏金额', prop: 'profitLossAmount', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '税率', prop: 'taxRate', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '税额', prop: 'tax', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '含税/未税', prop: 'conditionType', type: 'enum', visible: true, minWidth: '120px', enmus: includeTaxOptions },
  { label: '含税单价', prop: 'priceWithTax', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '未税单价', prop: 'priceWithoutTax', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '折扣总额', prop: 'discountPrice', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '折扣后单价（未税）', prop: 'discountPriceWithoutTax', type: 'default', visible: true, minWidth: '140px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '折扣后单价（含税）', prop: 'discountPriceWithTax', type: 'default', visible: true, minWidth: '140px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '折扣后总额', prop: 'discountAfterTotalAmount', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '货币', prop: 'currency', type: 'default', visible: true, minWidth: '120px' },
  { label: '汇率', prop: 'exchangeRate', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace2(val) } },
  { label: '工厂名称', prop: 'werksT', type: 'default', visible: true, minWidth: '120px' },
  { label: '分销渠道描述', prop: 'distributionChannelDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售组织', prop: 'salesOrganizationDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '成品油', prop: 'ifOil', type: 'default', visible: true, minWidth: '120px' },
  { label: '实际交货日期', prop: 'wadatIst', type: 'default', visible: true, minWidth: '120px' },
  { label: '单据创建日期', prop: 'erdat', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货联系人', prop: 'receiverName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单联系人', prop: 'orderContactName', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票联系人', prop: 'receivingInvoiceName', type: 'default', visible: true, minWidth: '120px' },
  { label: '需求部门', prop: 'xqbm', type: 'default', visible: true, minWidth: '120px' },
  { label: '领料人', prop: 'zfield3', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服名称', prop: 'customerServiceName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售员名称', prop: 'sellerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '集团销售名称', prop: 'groupSalesName', type: 'default', visible: true, minWidth: '120px' },
  { label: '所属集团编码', prop: 'parentId', type: 'default', visible: true, minWidth: '120px' },
  { label: '所属集团名称', prop: 'parentName', type: 'default', visible: true, minWidth: '120px' },
  { label: '后补订单', prop: 'backupOrder', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单原因', prop: 'orderReason', type: 'default', visible: true, minWidth: '120px' },
  { label: '凭邮件开票', prop: 'invoicingByMail', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售订单数量', prop: 'quantity', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '销售订单类型', prop: 'orderType', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货类型', prop: 'lfart', type: 'default', visible: true, minWidth: '120px' },
  { label: '外围系统订单号', prop: 'bstkdE', type: 'default', visible: true, minWidth: '120px' },
  { label: '外围系统', prop: 'bsarkE', type: 'default', visible: true, minWidth: '120px' },
  { label: '产品名称', prop: 'materielName', type: 'default', visible: true, minWidth: '120px' },
  { label: '物料组', prop: 'productGroup', type: 'default', visible: true, minWidth: '120px' },
  { label: '物料组描述', prop: 'productGroupName', type: 'default', visible: true, minWidth: '120px' },
  { label: '品牌', prop: 'brandName', type: 'default', visible: true, minWidth: '120px' },
  { label: '品牌编号', prop: 'brandId', type: 'default', visible: true, minWidth: '120px' },
  { label: '规格型号', prop: 'specification', type: 'default', visible: true, minWidth: '120px' },
  { label: '条件数量', prop: 'conditionalPricingUnit', type: 'default', visible: true, minWidth: '120px', labelFormat(val) { return fixedDecimalPlace3(val) } },
  { label: '创建者', prop: 'creator', type: 'default', visible: true, minWidth: '120px' },
  { label: '工厂', prop: 'werks', type: 'default', visible: true, minWidth: '120px' },
  { label: '分销渠道编号', prop: 'distributionChannel', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售组织编号', prop: 'salesOrganization', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单下单日期', prop: 'orderCreateTime', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服主管', prop: 'customerServiceSupervisorName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售主管', prop: 'salesSupervisorName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服经理', prop: 'customerServiceManagerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售经理', prop: 'salesManagerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售办公室', prop: 'salesOffice', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售办公室描述', prop: 'salesOfficeDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售组', prop: 'vkgrpKey', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售组描述', prop: 'vkgrpKeyDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '省份名称', prop: 'z002n', type: 'default', visible: true, minWidth: '120px' },
  { label: '城市名称', prop: 'z004n', type: 'default', visible: true, minWidth: '120px' },
  { label: '地区名称', prop: 'z006n', type: 'default', visible: true, minWidth: '120px' },
  { label: '自主下单标识', prop: 'orderLabel', type: 'default', visible: true, minWidth: '120px' },
  { label: '付款条件', prop: 'paymentTerm', type: 'default', visible: true, minWidth: '120px' },
  { label: '未开票天数', prop: 'unInvoicedDays', type: 'default', visible: true, minWidth: '120px' },
  { label: '未开票原因', prop: 'unInvoicedReason', type: 'default', visible: true, minWidth: '120px' },
  { label: '未开票详情', prop: 'unInvoicedRemark', type: 'default', visible: true, minWidth: '120px' },
  {
    label: '责任人角色',
    prop: 'unInvoicedResponsible',
    type: 'enum',
    visible: true,
    minWidth: '120px',
    enmus: unInvoicedResponsibleOptions
  },
  {
    label: '未开票责任人',
    prop: 'unInvoicedResponsiblePersonName',
    type: 'default',
    visible: true,
    minWidth: '120px'
  },
  { label: '操作', prop: 'manage', type: 'button', text: '未开票记录', visible: true, fixed: 'right', sortable: false, visibleSwitch: false, minWidth: '100px', onClick(val) { console.log(val) } }
]

const getColumns = function () {
  return deepClone(columns)
}
export default getColumns
