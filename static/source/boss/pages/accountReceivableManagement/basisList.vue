<template>
  <div class="app-container account-statement-container">
    <div
      class="filter-container"
      :style="noSearchBg ? { backgroundColor: 'white' } : ''"
    >
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="14">
            <el-form-item label="开票依据编号：" prop="basisNo">
              <el-input
                v-model="searchForm.basisNo"
                placeholder="同时支持5000个单号，以空格分隔"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="行编号：" prop="basisItemNo">
              <el-input
                v-model.trim="searchForm.basisItemNo"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left: 10px">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="客户名称：" prop="customerCode2">
              <el-select
                :disabled="hideCustomer"
                @change="changeCustomerCode2"
                :value="searchForm.customerCode2"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="customerNoLoading"
                clearable
              >
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户编号：" prop="customerCode">
              <el-input
                :disabled="hideCustomer"
                @blur="customerCodeBlur"
                v-model.trim="searchForm.customerCode"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="库存描述：" prop="locationDesc">
              <el-input
                v-model.trim="searchForm.locationDesc"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left: 10px">
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
            <el-button
              type="primary"
              circle
              :icon="caretIcon"
              @click="searchSonditionOpen = !searchSonditionOpen"
            />
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="客服：" prop="customerServiceName">
              <el-input
                v-model.trim="searchForm.customerServiceName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客服主管：" prop="superCustomerServiceName">
              <el-input
                v-model.trim="searchForm.superCustomerServiceName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="产品编码：" prop="materiel">
              <el-input
                v-model.trim="searchForm.materiel"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="订单联系人：" prop="orderContactName">
              <el-input
                v-model="searchForm.orderContactName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="收货联系人：" prop="receiverName">
              <el-input
                v-model="searchForm.receiverName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="收票联系人：" prop="receivingInvoiceName">
              <el-input
                v-model="searchForm.receivingInvoiceName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="对账状态：" prop="status">
              <el-select
                multiple
                v-model="searchForm.status"
                placeholder="请选择"
                clearable
                :disabled="hideCustomer"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="订单号：" prop="orderNo">
              <el-input
                v-model="searchForm.orderNo"
                placeholder="外围订单号、销售订单号、客户订单号 均支持搜索，同时支持5000个单号，以空格分隔"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="外围系统：" prop="peripheralSystem">
              <el-select
                multiple
                v-model="searchForm.peripheralSystem"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in peripheralSystemOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="交货类型描述：" prop="deliveryType">
              <el-select
                filterable
                multiple
                v-model="searchForm.deliveryType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in deliveryTypeDescOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="需求部门：" prop="department">
              <el-select
                multiple
                v-model="searchForm.department"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in departmentOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="销售类型描述：" prop="orderType">
              <el-select
                filterable
                multiple
                v-model="searchForm.orderType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in orderTypeDescStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="所属集团名称：" prop="parentName">
              <el-input
                v-model.trim="searchForm.parentName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="销售组织：" prop="salesOrganization">
              <el-input
                v-model.trim="searchForm.salesOrganization"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户是否签收：" prop="signStatus">
              <el-select
                multiple
                v-model="searchForm.signStatus"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in signStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="交货状态：" prop="deliveryStatus">
              <el-select
                v-model="searchForm.deliveryStatus"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in deliveryStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="实际交货日期：">
              <el-col :span="11">
                <el-form-item prop="realDeliveryStartDate">
                  <el-date-picker
                    clearable
                    v-model="searchForm.realDeliveryStartDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="realDeliveryEndDate">
                  <el-date-picker
                    clearable
                    v-model="searchForm.realDeliveryEndDate"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="止"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="未开票责任人：" prop="unInvoicedResponsible">
              <el-select
                multiple
                v-model="searchForm.unInvoicedResponsible"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in unInvoicedResponsibleOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="未开票原因：" prop="unInvoicedReason">
              <el-select
                v-model="searchForm.unInvoicedReason"
                placeholder="请选择"
                multiple
                clearable
              >
                <el-option
                  v-for="item in unInvoicedReasonOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="开票状态：" prop="invoicingStatus">
              <el-select
                v-model="searchForm.invoicingStatus"
                multiple
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in invoicingStatusOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="原因细分：" prop="unInvoicedReasonDetail">
              <el-select
                v-model="searchForm.unInvoicedReasonDetail"
                placeholder="请选择"
                multiple
                clearable
              >
                <el-option
                  v-for="item in unInvoicedReasonDetailOptions"
                  :key="item.label"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
          <el-row v-show="searchSonditionOpen" type="flex">
          <el-form-item label="未开票天数：">
            <div class="el-col-8"><el-input placeholder="最小天数" clearable v-model.trim="searchForm.unInvoicedMinDays"></el-input></div>
            <div class="line el-col el-col-2">-</div>
            <div class="el-col-8"><el-input placeholder="最大天数" clearable v-model.trim="searchForm.unInvoicedMaxDays"></el-input></div>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div class="backlog-search-result-container">
      <el-row
        v-if="!hideTableHeader"
        type="flex"
        class="result-title"
        justify="space-between"
      >
        <el-col :span="8">开票依据列表</el-col>
        <el-col style="text-align: right">
          <el-button @click="showColumnSet">自定义列</el-button>
          <!-- 针对客服角色，有全部功能权限
                针对非客服角色（用户!=客服角色），有查询、自定义列的权限
            -->
          <template v-if="isServices">
            <el-button type="primary" @click="openImportDialog">导入</el-button>
            <el-button :loading="exportLoading" @click="exportBasis"
              >导出</el-button
            >
            <el-button
              type="warning"
              @click="checkAll"
              :loading="checkAllLoading"
              >全选</el-button
            >
            <el-button type="default" @click="openInvoiceDialog"
              >未开票原因记录</el-button
            >
          </template>
        </el-col>
      </el-row>
      <zkh-table
        ref="basisTable"
        :selectable="true"
        :loading="basisListLoading"
        :data="basisList"
        :columns="columns"
        :height="500"
        :columnEditable="true"
        :visible.sync="columnsSetVisible"
        :columnsConfig="columnsConfigDeepCopy"
        :defaultColumnsConfig="defaultColumnsConfig"
        :checkSelectable="checkSelectable"
        @columnSet="columnSet"
        :selectionChange="handleSapSelectionChange"
        @manage-button-click="handleManageClick"
      ></zkh-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10, 20, 30, 50, 100, 200, 500]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
    <el-dialog
      width="800px"
      title="未开票原因记录"
      class="invoice-reason-dialog"
      :visible.sync="invoiceReasonDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <UnInvoicedReasonRecord
        :basisId="basisId"
        :visible.sync="invoiceReasonDialog"
        :unInvoicedReasonOptions="unInvoicedReasonOptions"
        :unInvoicedReasonDetailOptions="unInvoicedReasonDetailOptions"
        ref="unInvoicedReasonRecord"
        @un-invoiced-reason-dialog="closeInvoiceDialog"
        @un-invoiced-reason-done="invoiceDialogDone"
      />
    </el-dialog>
    <el-dialog
      width="800px"
      title="未开票记录"
      :visible.sync="inReRecordShow"
      :close-on-click-modal="false"
    >
      <InReRecordShow
        :visible.sync="inReRecordShow"
        :inReRecordShowId="inReRecordShowId"
        ref="InReRecordShow"
        @un-invoiced-reason-show-dialog="closeInvoiceRecordDialog"
      />
    </el-dialog>
    <el-dialog
      width="320px"
      title="导入"
      :visible.sync="showImportDialog"
      :close-on-click-modal="false"
    >
      <div style="display: flex; justify-content: space-around">
        <el-button :loading="downloadLoading" @click="downloadTemplate"
          >下载模板</el-button
        >
        <el-upload
          class="btn-upload"
          action="/reconciliation/unInvoicedReasonRecord/v1/upload"
          :show-file-list="false"
          :on-success="onUploadSuccess"
          :before-upload="beforeUpload"
          :on-error="onUploadError"
          :accept="acceptFileType.commonType"
          name="file"
        >
          <el-button :loading="importLoading" size="small" type="primary"
            >导入</el-button
          >
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import UnInvoicedReasonRecord from '../accountStatement/components/unInvoicedReasonRecord'
import InReRecordShow from '../accountStatement/components/inReRecordShow.vue'
import getColumns from './basisColumns.js'
import {
  createdConfigByColumns,
  formatByConfig,
  getConfig,
  cachedConfig,
  checkColumnsConfig
} from '@/components/ZkhTable/columnConfigTransformation.js'
import { getBasisList } from '@/api/accountReceivableManagement.js'
import {
  createStatementByBasis,
  createStatementByBasisPre,
  getOrderTypeDescStatusOptions,
  getStatusOptions,
  getDeliveryTypeDescOptions,
  getPeripheralSystemOptions,
  getUnInvoicedReasonDetailOptions,
  getInvoicingStatusOptions,
  getUnInvoicedReasonOptions,
  getUnInvoicedResponsibleOptions,
  getCustomerListByPartName,
  getBasisIdsBySearchFields,
  demandDepartment,
  exportBasisApi,
  downloadBasisTpl
} from '@/api/accountStatement.js'

// 页面搜索条件初始化
const getInitSearchParams = function () {
  return {
    basisNo: '', // 开票依据编号
    basisItemNo: '', // 开票依据行编号
    customerCode: '', // 客户编号
    customerCode2: '', // 客户名称
    orderType: '', // 销售类型描述
    status: '', // 对账状态
    customerServiceName: '', // 客服
    materiel: '', // 产品名称
    superCustomerServiceName: '', // 客服主管
    orderNo: '', // 订单号
    orderContactName: '', // 订单联系人
    receiverName: '', // 收货联系人
    receivingInvoiceName: '', // 收票联系人
    parentName: '', // 所属集团名称
    salesOrganization: '', // 销售组织
    signStatus: '', // 客户是否签收
    deliveryStatus: '', // 交货状态
    locationDesc: '', // 库存地点描述
    deliveryType: '', // 交货类型描述
    realDeliveryStartDate: '', // 实际交货日期-起始
    peripheralSystem: '', // 外围系统
    department: '', // 需求部门
    realDeliveryEndDate: '', // 实际交货日期-截止
    unInvoicedResponsible: '', // 未开票责任人
    unInvoicedReason: '', // 未开票原因
    unInvoicedReasonDetail: '', // 未开票原因明细
    invoicingStatus: ['A', 'B'], // 开票状态
    unInvoicedMinDays: '', // 未开票最小天数
    unInvoicedMaxDays: '' // 未开票最大天数
  }
}

export default {
  name: 'BasisList',
  data () {
    return {
      orderTypeDescStatusOptions: [], // 销售订单类型描述 下拉选项
      statusOptions: [], // 对账依据状态 下拉选项
      peripheralSystemOptions: [], // 外围系统下拉选项
      departmentOptions: [], // 需求部门下拉选项
      deliveryTypeDescOptions: [], // 交货类型描述 下拉选项
      customerNoLoading: false,
      basisListLoading: false, // 查询按钮loading
      customerNoOptions: [],
      searchForm: {}, // 绑定搜索参数
      columnsSetVisible: false, // 列设置弹框显隐
      defaultColumnsConfig: [],
      deliveryStatusOptions: [
        { label: '未交货', value: 'A' },
        { label: '部分交货', value: 'B' },
        { label: '已交货', value: 'C' }
      ],
      signStatusOptions: [
        { label: '未签收', value: '0' },
        { label: '已签收', value: '1' }
      ],
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      rules: {},
      basisList: [],
      total: 0,
      columns: [],
      columnsConfig: [],
      columnsConfigDeepCopy: [],
      prefixCustomer: '',
      prefixCurrency: '',
      prefixGroupNo: '',
      prefixStatus: '',
      prefixStatementNo: '',
      createStatementAllLoading: false,
      searchSonditionOpen: false,
      checkedAllFlag: false,
      checkedAllData: [],
      unInvoicedReasonDetailOptions: [],
      unInvoicedResponsibleOptions: [], // 未开票责任人
      invoicingStatusOptions: [],
      unInvoicedReasonOptions: [],
      exportLoading: false,
      invoiceReasonDialog: false,
      basisId: '',
      inReRecordShow: false,
      inReRecordShowId: '',
      checkAllLoading: false,
      showImportDialog: false,
      downloadLoading: false,
      importLoading: false
    }
  },
  props: {
    noSearchBg: {
      type: Boolean,
      default: false
    },
    hideTableHeader: {
      type: Boolean,
      default: false
    },
    hideCustomer: {
      type: Boolean,
      default: false
    },
    manualCreateStatement: {
      type: Function,
      default: null
    }
  },
  components: {
    Pagination,
    UnInvoicedReasonRecord,
    InReRecordShow
  },
  watch: {
    'searchForm.unInvoicedReason' (newVal, oldVal) {
      if (newVal !== oldVal && newVal) {
        this.searchForm.unInvoicedReasonDetail = ''
        this.unInvoicedReasonDetailOptions = []
        if (!newVal.length) return
        getUnInvoicedReasonDetailOptions(newVal).then((res) => {
          if (res && res.status === 200 && res.datas) {
            this.unInvoicedReasonDetailOptions = res.datas.map((data) => ({
              label: data.name,
              value: data.code
            }))
          }
        })
      }
    }
  },
  created () {
    Promise.all([
      getOrderTypeDescStatusOptions().then((data) => {
        this.orderTypeDescStatusOptions = data
      }),
      getStatusOptions().then((data) => {
        this.statusOptions = data
      }),
      getDeliveryTypeDescOptions().then((data) => {
        this.deliveryTypeDescOptions = data
      }),
      getPeripheralSystemOptions().then((data) => {
        this.peripheralSystemOptions = data
      }),
      getInvoicingStatusOptions().then((data) => {
        this.invoicingStatusOptions = data.filter((opt) => opt.value !== 'C')
      }),
      getUnInvoicedResponsibleOptions().then((data) => {
        this.unInvoicedResponsibleOptions = data
      }),
      getUnInvoicedReasonOptions({ deleted: false }).then((res) => {
        if (res && res.status === 200 && res.datas) {
          this.unInvoicedReasonOptions = res.datas.map((data) => ({
            label: data.name,
            value: data.code
          }))
        }
      })
    ]).then(() => {
      this.initColumns()
      this.searchForm = getInitSearchParams()
      this.handleFilter()
    })
  },
  computed: {
    ...mapState(['userRole']),
    isServices () {
      return !!~this.userRole.indexOf('data-客服')
    },
    acceptFileType () {
      return this.$store.state.orderCommon.acceptFileType || {}
    },
    caretIcon () {
      if (this.searchSonditionOpen) {
        return 'el-icon-caret-top'
      } else {
        return 'el-icon-caret-bottom'
      }
    },
    getQueryOrderType () {
      if (!this.searchForm.orderType) {
        return []
      } else {
        return this.searchForm.orderType.split(',')
      }
    }
  },
  methods: {
    beforeUpload (file) {
      if (!this.$validateFileType(file)) return false
      this.importLoading = true
    },
    onUploadSuccess (response) {
      this.importLoading = false
      if (response && response.status === 200) {
        this.$message.success(response.message || '导入成功！')
      } else {
        this.$message.error((response && response.message) || '导入失败！')
      }
    },
    onUploadError (error) {
      console.log(error)
      this.importLoading = false
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      )
    },
    downloadTemplate () {
      this.downloadLoading = true
      downloadBasisTpl()
        .then((res) => {
          this.$message.success((res && res.msg) || '下载成功！')
        })
        .catch((err) => {
          this.$message.error((err && err.msg) || '下载失败！')
        })
        .finally(() => {
          this.downloadLoading = false
        })
    },
    openImportDialog () {
      this.showImportDialog = true
    },
    closeInvoiceRecordDialog () {
      this.inReRecordShow = false
    },
    openInvoiceDialog () {
      let data = ''
      if (this.checkedAllFlag) {
        data = this.checkedAllData
        if (this.checkAllLoading) return this.$message.error('请先完成全选！')
      } else {
        const tb = this.$refs.basisTable
        let selectRows = tb.getSelections()
        if (selectRows.length > 0) {
          const ids = selectRows.map((item) => item.id).join(',')
          data = ids
        } else {
          this.$message.error('请选择要生成记录的行！')
          return
        }
      }
      this.basisId = data
      this.invoiceReasonDialog = true
    },
    closeInvoiceDialog () {
      this.invoiceReasonDialog = false
    },
    invoiceDialogDone () {
      this.handleFilter()
    },
    handleManageClick (row) {
      this.inReRecordShow = true
      this.inReRecordShowId = row.id
    },
    handleSapSelectionChange () {
      this.checkedAllFlag = false
      this.checkedAllData = []
    },
    customerCodeBlur () {
      console.log(this.searchForm.customerCode)
      this.departmentOptions = []
      this.searchForm.department = ''
      this.searchDemandDepartment(this.searchForm.customerCode)
    },
    searchDemandDepartment (value) {
      if (!value) return
      demandDepartment(value)
        .then((res) => {
          if (res && res.status === 200) {
            this.departmentOptions = res.datas
          }
        })
        .catch((err) => {
          this.departmentOptions = []
          this.searchForm.department = ''
          this.$message.error(err.msg || err.message || '查询需求部门失败！')
        })
    },
    changeCustomerCode2 (value) {
      console.log(value)
      this.departmentOptions = []
      this.searchForm.department = ''
      this.searchForm.customerCode2 = value
      this.searchDemandDepartment(value)
    },
    columnSet (newConfig) {
      if (newConfig) {
        this.columnsConfig = newConfig
        this.columns = formatByConfig(this.columnsConfig, getColumns())
        cachedConfig('accountReceivableManagement_basisColumnsConfig', newConfig)
      }
      this.columnsSetVisible = false
    },
    showColumnSet () {
      this.columnsConfigDeepCopy = JSON.parse(
        JSON.stringify(this.columnsConfig)
      )

      this.columnsSetVisible = true
    },
    initColumns () {
      try {
        this.defaultColumnsConfig = createdConfigByColumns(getColumns())
        let columnsConfig = getConfig('accountReceivableManagement_basisColumnsConfig')

        if (columnsConfig && columnsConfig.length > 0) {
          const checkResult = checkColumnsConfig(
            this.defaultColumnsConfig,
            columnsConfig
          )
          columnsConfig = checkResult.config
          if (checkResult.changed) {
            cachedConfig('accountReceivableManagement_basisColumnsConfig', columnsConfig)
          }
          this.columnsConfig = columnsConfig
          this.columns = formatByConfig(this.columnsConfig, getColumns())
        } else {
          this.setDefaultCache(getColumns())
        }
      } catch (error) {
        this.setDefaultCache(getColumns())
      }
    },
    setDefaultCache (cols) {
      const config = createdConfigByColumns(cols)
      this.columnsConfig = config
      this.columns = cols
    },
    // 判断某些行能不能选
    checkSelectable (row) {
      // 对账完成的依据无法选中多选框
      if (row.fkstk === 'C') {
        return false
      } else {
        return true
      }
    },
    handleCreateStatement (ids, all) {
      this.$confirm(
        `${all ? '已全选，' : ''}是否确认生成${all ? '全部' : ''}对账单`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning',
          center: true
        }
      ).then(() => {
        createStatementByBasisPre({ ids })
          .then((res) => {
            if (res.status === 200) {
              this.$confirm(res.datas, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                closeOnClickModal: false,
                type: 'warning',
                center: true
              }).then(() => {
                createStatementByBasis({ ids })
                  .then((res) => {
                    if (res.status === 200) {
                      const data = res.datas
                      this.$router.push(
                        '/account-statement-center/statement/detail/' + data
                      )
                    } else {
                      this.$message.error(res.message)
                    }
                  })
                  .catch((e) => {
                    let res = e.response ? e.response.data : {}
                    this.$message.error(
                      res.message || res.msg || res.error || e
                    )
                  })
              })
            } else if (res.status === 500) {
              this.$message.info(res.message)
            } else {
              this.$message.error(res.message || res.msg || '操作失败！')
            }
          })
          .catch((err) => {
            this.$message.error(err.message || err.msg || '操作失败！')
          })
      })
    },
    exportBasis () {
      const columnsToExport = this.columnsConfig.filter((column) => column.visible && column.prop !== 'manage');
      const columns = columnsToExport.map((col) => col.prop).join(',');
      let param = { ...this.searchForm }
      this.trimParams(param)
      let valid = this.formatParams(param)
      if (!valid) return
      this.exportLoading = true
      exportBasisApi({ ...param, fieldNameList: columns })
        .catch((err) => {
          this.$message.error(err.msg || err.message || '下载失败！')
        })
        .finally(() => {
          this.exportLoading = false
        })
    },
    async checkAll () {
      const tb = this.$refs.basisTable
      this.basisList.forEach((row) => {
        if (row.fkstk !== 'C') {
          tb.toggleRowSelection(row, true)
        }
      })
      this.checkedAllFlag = true
      let param = { ...this.searchForm }
      delete param.pageNum
      delete param.pageSize
      this.formatParams(param)
      this.checkAllLoading = true
      let response = await getBasisIdsBySearchFields(param)
      this.checkAllLoading = false
      this.$message.success('全选成功！')
      this.checkedAllData = response.datas
    },
    async createStatementAll () {
      try {
        this.createStatementAllLoading = true
        let param = { ...this.searchForm }
        delete param.pageNum
        delete param.pageSize
        this.formatParams(param)
        let response = await getBasisIdsBySearchFields(param)
        if (response && response.status === 200) {
          if (response.datas) {
            this.handleCreateStatement(response.datas)
          } else {
            this.$message.error('没有数据！')
          }
        } else {
          this.$message.error(response.msg || response.message || '查询失败！')
        }
      } catch (err) {
        console.log(err)
        this.$message.error('查询失败！')
      } finally {
        this.createStatementAllLoading = false
      }
    },
    transArrayToJoin (obj, prop) {
      if (Array.isArray(obj[prop])) {
        obj[prop] = obj[prop].filter((e) => e).join(',')
      }
    },
    formatParams (param) {
      if (param.orderNo && param.orderNo.split) {
        let orderNoList = param.orderNo.split(/\s+|,|，/).filter((e) => e)
        // if (orderNoList.length > 500) {
        //   this.$message.error('订单号最多支持500个！')
        //   return false
        // }
        param.orderNo = orderNoList.join(',')
      }
      if (param.basisNo && param.basisNo.split) {
        let basisNoList = param.basisNo.split(/\s+|,|，/).filter((e) => e)
        // if (basisNoList.length > 500) {
        //   this.$message.error('开票依据编号最多支持500个！')
        //   return false
        // }
        param.basisNo = basisNoList.join(',')
      }
      this.transArrayToJoin(param, 'deliveryType')
      this.transArrayToJoin(param, 'orderType')
      this.transArrayToJoin(param, 'status')
      this.transArrayToJoin(param, 'peripheralSystem')
      this.transArrayToJoin(param, 'department')
      this.transArrayToJoin(param, 'signStatus')
      this.transArrayToJoin(param, 'unInvoicedReason')
      this.transArrayToJoin(param, 'unInvoicedReasonDetail')
      this.transArrayToJoin(param, 'unInvoicedResponsible')
      this.transArrayToJoin(param, 'invoicingStatus')

      param.demandDepartment = param.department
      delete param['department']
      return true
    },
    trimParams (obj) {
      for (let key in obj) {
        if (obj[key] && obj[key].trim) {
          obj[key] = obj[key].trim()
        }
      }
    },
    getBasisList () {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.trimParams(this.searchForm)
          let param = { ...this.searchForm }
          const query = {
            pageNum: this.listQueryInfo.current,
            pageSize: this.listQueryInfo.pageSize
          }
          this.basisList = []
          if (this.prefixCustomer) {
            param.customerCode = this.prefixCustomer
          }
          if (this.prefixCurrency) {
            param.currency = this.prefixCurrency
          }
          if (this.prefixGroupNo !== undefined) {
            param.parentId = this.prefixGroupNo
          }
          if (this.prefixStatus) {
            param.status = this.prefixStatus
          }
          if (this.prefixStatementNo) {
            param.statementNo = this.prefixStatementNo
          }
          let valid = this.formatParams(param)
          if (!valid) return
          if (!param.invoicingStatus) {
            // 数据来源：基于对账依据列表，对账依据列表中开票状态!=已开票的对账依据的数据
            param.invoicingStatus = 'A,B'
          }
          this.basisListLoading = true
          getBasisList(query, param)
            .then((res) => {
              this.basisListLoading = false
              if (res.status === 200) {
                if (res.datas) {
                  const data = res.datas
                  this.total = data.total
                  this.basisList = data.list
                }
              } else {
                this.$message.error(res.message)
              }
            })
            .catch(() => {
              this.basisListLoading = false
            })
        }
      })
    },
    // 查询按钮
    handleFilter () {
      this.listQueryInfo.current = 1
      if (!this.searchForm.invoicingStatus.length) {
        this.searchForm.invoicingStatus = ['A', 'B']
      }
      this.getBasisList()
    },
    // 重置按钮
    handleReset () {
      this.searchForm = getInitSearchParams()
      this.departmentOptions = []
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerListByPartName({
          customerName: key
        }).then((res) => {
          this.customerNoLoading = false
          if (res.status === 200) {
            if (res.datas && res.datas.length > 0) {
              this.customerNoOptions = res.datas.map((item) => {
                return {
                  value: item.customerNumber,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    }
  }
}
</script>

<style lang="scss" scope></style>
<style lang="scss">
.account-statement-container {
  .filter-container {
    padding-top: 18px;
    padding-right: 10px;
    background-color: #f4f4f4;
    // .el-form-item {
    //   margin-bottom: 10px;
    // }
  }
  .tag-container {
    padding-left: 10px;
    padding-bottom: 5px;
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .customer-tips {
    background: #ffffff;
    margin-top: 15px;
    margin-bottom: 15px;
    padding-left: 0;
    font-size: 14px;
    span {
      text-align: center;
      color: red;
      font-size: 14px;
    }
  }
  .backlog-search-result-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin: 10px 0;
    & > {
      .result-title {
        height: 50px;
        padding: 0 10px 0 20px;
        line-height: 50px;
        font-size: 16px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
