<template>
  <el-table :data="newTableList" style="width: 100%" max-height="450">
    <el-table-column align="center" type="index" label="序号" width="50" />
    <el-table-column align="center" prop="sapNo" label="SAP订单号" width="140" />
    <el-table-column align="center" prop="omsNo" label="OMS订单号" width="140" />
    <el-table-column align="center" prop="sapItemNo" label="SAP行号" width="80" />
    <el-table-column align="center" prop="sku" label="SKU" />
    <el-table-column align="center" prop="materialGroup" label="物料组" />
    <el-table-column align="center" prop="material" label="物料描述" width="250">
      <template slot-scope="{row}">
        <div style="display:flex;">
          <img v-if="row.skuUrl" class="order-img" :src="formatPicUrl(row.skuUrl)" :alt="row.sku">
          <span>{{row.material}}</span>
        </div>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="manufacturerMaterialNo" label="制造商型号" />
    <el-table-column align="center" prop="taxIncludeUnitPrice" label="含税单价" />
    <el-table-column align="center" prop="orderQuantity" label="订单数量" />
    <el-table-column align="center" prop="taxTotalPrice" label="含税总价" />
    <el-table-column align="center" prop="quantityUnit" label="单位" />
    <el-table-column align="center" prop="applyQuantity" label="行申请数量" width="140">
      <template slot-scope="{row}">
        <span v-if="disabled">{{row.applyQuantity}}</span>
        <el-input-number v-else :controls="false" v-model="row.applyQuantity"
          @change="(value) => handleNumberChange(row, value)" :precision="3"
          :min="0" :max="row.workOrderCount ? row.workOrderCount : row.orderQuantity" label="s"></el-input-number>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="applyPrice" label="行申请金额" />
    <el-table-column align="center" prop="batchNo" label="批次号/采购单号/供应商编码" width="200px">
      <template slot-scope="{row}">
        <el-select v-model="row.poInfo" @change="handlePoChange(row)">
          <el-option v-for="(item, index) in row.poInfos" :key="item + index" :value="item">
            <span>{{ item }}</span>
            <el-button type="text" @click="handleCopy(item)" style="margin-left: 5px">复制</el-button>
          </el-option>
        </el-select>
      </template>
    </el-table-column>
    <el-table-column v-if="!disabled" align="center" label="操作">
      <template slot-scope="{row}">
        <el-button type="text" @click="delRow(row)">删除</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { toFixedByRadix } from '@/utils/price'
import { api } from '@/api/aftersales'
import clip from '@/utils/clipboard'
export default {
  props: {
    disabled: Boolean,
    serviceAfterSales: String,
    tableList: {
      type: Array,
      default: () => []
    },
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {
      newTableList: []
    }
  },
  methods: {
    handleNumberChange (row, number) {
      const list = this.tableList.map(_row => {
        if (row === _row) {
          _row.applyQuantity = number
          _row.applyPrice = toFixedByRadix(_row.applyQuantity * _row.taxIncludeUnitPrice, 6)
        }
        return _row
      })
      this.$emit('changeTableList', list)
    },
    delRow (row) {
      const list = this.tableList.filter(li => li !== row)
      console.log(row, list)
      this.$confirm('请确认是否删除该条明细?', '提示', { type: 'warning' })
        .then(() => {
          this.$emit('changeTableList', list)
          this.$message({ type: 'success', message: '删除成功!' })
        })
    },
    formatPicUrl (url) {
      if (/pathfinder/.test(url)) {
        return url.replace(/\?.+/, '?x-oss-process=style/common_style')
      }
      return url
    },
    selectPoByOmsNo (data, index) {
      api({
        url: '/ticket/service/selectPoByOmsNo',
        query: {
          omsNo: data.omsNo,
          omsItemNo: data.omsItemNo,
          sku: data.sku
        },
        complete: res => {
          console.log(res);
          const temp = res.data.map(item => (item.batchNo || '无') + '/' + (item.poNo || '无') + '/' + (item.supplierNo || '无'))
          this.$set(this.newTableList[index], 'poInfos', temp)
          this.tableList[index].poInfo = this.tableList[index].poInfo ? this.tableList[index].poInfo : this.tableList[index].poInfos[0]
          const poInfos = this.tableList[index].poInfo.split('/');
          if (poInfos.length) {
            this.tableList[index].batchNo = poInfos[0];
            this.tableList[index].poNo = poInfos[1];
            this.tableList[index].supplierNo = poInfos[2];
          }
        }
      })
    },
    handlePoChange (row) {
      const list = this.tableList.map(_row => {
        if (row === _row) {
          _row.poInfo = row.poInfo
          const poInfos = row.poInfo.split('/');
          if (poInfos.length) {
            _row.batchNo = poInfos[0];
            _row.poNo = poInfos[1];
            _row.supplierNo = poInfos[2];
          }
        }
        return _row
      })
      this.$emit('changeTableList', list)
    },
    handleCopy (item) {
      clip(item, event, () => {
        const content = '复制成功'
        this.$message({
          message: content,
          type: 'success'
        })
      })
    }
  },
  watch: {
    applyData (val) {
      this.form = val
    },
    tableList (newVal, oldVal) {
      if (newVal !== oldVal) {
        this.newTableList = newVal
          this.newTableList.map((item, index) => {
            this.selectPoByOmsNo(item, index)
          })
      }
    }
  }
}
</script>

<style lang="less">
img.order-img{
  width: 60px;
  height: 60px;
}
</style>
