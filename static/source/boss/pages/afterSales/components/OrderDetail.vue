<template>
  <el-dialog
    :visible.sync="showOrderDetail"
    title="销售订单明细" width="80%"
    :before-close="handleClose">
    <div v-loading="loading.pageLoading">
      <el-form ref="searchForm" :inline="true" :model="formData" :rules="rules" label-width="100px">
        <el-form-item style="width: auto" label="订单号：" prop="sapNo" >
          <el-input clearable style="width: 260px;" v-model="formData.sapNo" placeholder="支持SAP/OMS/外围系统/客户订单号"></el-input>
        </el-form-item>
        <el-form-item style="width: auto" label="SKU编号：" prop="sku" >
          <el-input clearable v-model="formData.sku" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item style="width: 80px">
          <el-button type="primary" :loading="loading.searchLoading" @click="getList">查询</el-button>
        </el-form-item>
      </el-form>
      <div class="result"><p>查询结果：</p>
        <el-table :data="tableList" style="width: 100%" max-height="450"
          :loading="loading.tableLoading" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column align="center" prop="sku" label="SKU" />
          <el-table-column align="center" prop="materialGroup" label="物料组" />
          <el-table-column align="center" prop="material" label="物料描述" />
          <el-table-column align="center" prop="manufacturerMaterialNo" label="制造商型号" />
          <el-table-column align="center" prop="taxIncludeUnitPrice" label="含税单价" />
          <el-table-column align="center" prop="orderQuantity" label="订单数量" />
          <el-table-column align="center" prop="taxTotalPrice" label="含税总价" />
          <el-table-column align="center" prop="quantityUnit" label="单位" />
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addOrderDetail">确认添加</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { api as afterSaleApi } from '@/api/aftersales'
export default {
  data () {
    return {
      formData: { sapNo: '', sku: '' },
      rules: {
        sapNo: [{ required: true, message: '请输入单号', trigger: 'blur' }]
      },
      loading: {
        searchLoading: false,
        pageLoading: false,
        tableLoading: false
      },
      tableList: [],
      selectedItems: []
    }
  },
  props: {
    showOrderDetail: {
      type: Boolean,
      default: false
    },
    customerNo: String
  },
  methods: {
    flipList (list) {
      try {
        this.tableList = list.reduce((prev, next) => {
          prev = prev.concat(next.afterSaleSoItemBOList.map(itemList => ({
            ...next, ...itemList
          })))
          return prev
        }, [])
      } catch (err) { console.log(err) }
    },
    handleSelectionChange (val) {
      this.selectedItems = val
    },
    addOrderDetail () {
      const data = this.selectedItems.map(item => {
        let applyPrice = item.applyPrice
        try { applyPrice = Math.round(item.applyPrice * 100) / 100 } catch (err) { console.log(err) }
        return { ...item, applyPrice }
      })
      if (!data.length) return this.$message.error('请至少勾选一项订单明细！')
      this.closeDialog()
      this.$emit('addOrder', data)
    },
    getList () {
      this.$refs['searchForm'].validate(valid => {
        if (!valid) return
        if (!this.customerNo) return this.$message.error('请输入客户名称或编码！')
        this.loading.pageLoading = true
        afterSaleApi({
          url: '/ticket/order/list',
          method: 'POST',
          data: {
            sku: this.formData.sku,
            sapNo: this.formData.sapNo,
            customerNo: this.customerNo
          },
          complete: (res) => {
            console.log(res.data)
            if (res && res.data) {
              this.tableList = res.data.map(row => ({
                ...row,
                applyQuantity: row.orderQuantity,
                applyPrice: row.orderQuantity * row.taxIncludeUnitPrice
              }))
            } else {
              this.tableList = []
            }
            this.loading.pageLoading = false
          }
        })
      })
    },
    closeDialog () {
      this.clearState()
      this.$emit('close-dialog')
    },
    clearState () {
      this.formData = { order: '', sku: '' }
      this.tableList = []
      this.$refs['searchForm'].resetFields()
    },
    handleClose (done) {
      this.closeDialog()
    }
  }
}
</script>

<style lang="less" scoped>
.dialog-footer{
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
