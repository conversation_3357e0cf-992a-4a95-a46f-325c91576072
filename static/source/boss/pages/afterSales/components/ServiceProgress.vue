<template>
  <div class="module-progress">
      <h4 class="block-title">售后进度</h4>
      <div class="module-progress-content">
        <!-- 卡片 -->
        <div class="module-progress-card" v-if="progressType('SUBMIT', 'EXAMINE_CUSTOMER_CONFIRMATION')[0]">
          <h4>提交申请</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="operation">
                <span v-show="progressType('SUBMIT', 'EXAMINE_CUSTOMER_CONFIRMATION')[0].createdTime">{{ formatTime(progressType('SUBMIT', 'EXAMINE_CUSTOMER_CONFIRMATION')[0].createdTime) }}</span>
                <span v-show="progressType('SUBMIT', 'EXAMINE_CUSTOMER_CONFIRMATION')[0].createdBy">操作者: {{ progressType('SUBMIT', 'EXAMINE_CUSTOMER_CONFIRMATION')[0].createdBy }}</span>
                <span v-show="progressType('SUBMIT', 'EXAMINE_CUSTOMER_CONFIRMATION')[0].source">申请渠道: {{ progressType('SUBMIT', 'EXAMINE_CUSTOMER_CONFIRMATION')[0].source }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="module-progress-card">
          <h4>审核进度</h4>
          <div class="progress-content-line" v-if="form.serviceTicket && form.serviceTicket.newLogicTag !== '1' && progressType('EXAMINE', 'EXAMINE_OPERATION_AUDIT')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_OPERATION_AUDIT'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-PROCESSING">客服确认</strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_OPERATION_AUDIT'" />
          <div class="progress-content-line" v-if="form.serviceTicket && form.serviceTicket.examine && form.serviceTicket.examine.state === 'OPERATION_REJECT' && progressType('EXAMINE', 'EXAMINE_OPERATION_REJECT')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_OPERATION_REJECT'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span><strong class="status-REFUSE">运营已驳回</strong></span>
                  </div>
                </div>
                <div class="body express-info">
                  <p><strong>驳回原因：</strong>{{form.serviceTicket.examine.explain}}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_OPERATION_REJECT'" />

          <!-- 返厂服务才展示 start -->
          <div class="progress-content-line" v-if="form.decide.type === 'REPAIR' && progressType('EXAMINE', 'TICKET_TYPE_REPAIR')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'TICKET_TYPE_REPAIR'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span class="status-COMFIRM"><strong>售后类型：</strong>{{form.decide.typeName}}</span>
                  </div>
                </div>
                <div class="body express-info">
                  <p><strong>服务提供方：</strong>{{form.repair.type === 'SELF' ? '自营' : '供应商'}}</p>
                  <p><strong>厂家收货联系人：</strong>{{form.repair.returnContact}}</p>
                  <p><strong>厂家收货电话：</strong>{{form.repair.returnPhone}}</p>
                  <p><strong>厂家收货地址：</strong>{{form.repair.returnAddress}}</p>
                </div>
              </div>
            </div>
          </div>
          <!-- 返厂服务才展示 end -->
          <!-- 上门服务才展示 start -->
          <div class="progress-content-line" v-if="form.decide.type === 'VISIT' && progressType('EXAMINE', 'TICKET_TYPE_VISIT')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'TICKET_TYPE_VISIT'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span class="status-COMFIRM"><strong>服务类型：</strong>上门服务</span>
                  </div>
                </div>
                <div class="body express-info">
                  <p><strong>服务提供方：</strong>{{form.visit.type === 'SELF' ? '自营' : '供应商'}}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'EXAMINE'" :secondState="'TICKET_TYPE_VISIT'" />
          <!-- 上门服务才展示 end -->
          <!-- 远程指导才展示 start -->
          <div class="progress-content-line" v-if="form.decide.type === 'REMOTE' && progressType('EXAMINE', 'TICKET_TYPE_REMOTE')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'TICKET_TYPE_REMOTE'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span class="status-COMFIRM"><strong>服务类型：</strong>远程指导</span>
                  </div>
                </div>
                <div class="body express-info">
                  <p><strong>服务提供方：</strong>{{form.remote.type === 'SELF' ? '自营' : '供应商'}}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'EXAMINE'" :secondState="'TICKET_TYPE_REMOTE'" />
          <!-- 远程指导才展示 end -->
          <!-- 补发服务才展示 start -->
          <div class="progress-content-line" v-if="form.decide.type === 'REISSUE' && progressType('EXAMINE', 'TICKET_TYPE_REISSUE')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'TICKET_TYPE_REISSUE'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span class="status-COMFIRM"><strong>服务类型：</strong>补发</span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.serviceTicket && form.serviceTicket.reissue">
                  <p><strong>服务提供方：</strong>{{form.serviceTicket.reissue.type === 'SELF' ? '自营' : '供应商'}}</p>
                </div>
              </div>
            </div>
          </div>
          <!-- 补发服务才展示 end -->
          <TempReply :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_OPERATION_AGREE'" />
          <!-- 安装类才展示 start -->
          <div class="progress-content-line" v-if="form.serviceTicket && form.serviceTicket.newLogicTag === '1' && progressType('EXAMINE', 'EXAMINE_OPERATION_PROCESS')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_OPERATION_PROCESS'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-PROCESSING">客服确认</strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_OPERATION_PROCESS'" />
          <div class="progress-content-line" v-if="form.serviceTicket && form.serviceTicket.newLogicTag === '1' && progressType('EXAMINE', 'EXAMINE_DELIVERY_PROCESS')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_DELIVERY_PROCESS'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span class="status-COMFIRM"><strong>售后服务信息</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.serviceTicket">
                  <p><strong>供应商：</strong>{{form.serviceTicket.supplyNo + ' ' + form.serviceTicket.supplyName}}</p>
                  <p><strong>供应商售后联系人：</strong>{{form.serviceTicket.supplyContact}}</p>
                  <p><strong>供应商售后联系电话：</strong>{{form.serviceTicket.supplyContactTelephone}}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'EXAMINE'" :secondState="'EXAMINE_DELIVERY_PROCESS'" />
          <div class="progress-content-line" v-if="form.serviceTicket && form.serviceTicket.newLogicTag === '1' && progressType('VISIT', 'VISIT_NO_NEED')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'VISIT'" :secondState="'VISIT_NO_NEED'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span class="status-COMFIRM"><strong>无需上门</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.serviceTicket">
                  <p><strong>不上门原因：</strong>{{form.serviceTicket.noVisitReason}}</p>
                  <div v-if="form.serviceTicket.visitFinishExplain"><strong>回复内容：</strong>{{form.serviceTicket.visitFinishExplain}}</div>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'VISIT'" :secondState="'VISIT_NO_NEED'" />
          <!-- 安装类才展示 end -->
        </div>
        <div class="module-progress-card" v-if="form.serviceTicket.operateAgentName && progressType('HANDOVER', 'HANDOVER_DEFAULT')[0]">
          <h4>任务交接</h4>
          <div class="progress-content">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'HANDOVER'" :secondState="'HANDOVER_DEFAULT'" />
              <div class="card">
                <div class="body">
                  <p><strong>任务接收人: </strong>{{ form.serviceTicket.operateAgentName }}</p>
                  <p v-if="progressType('HANDOVER', 'HANDOVER_DEFAULT')[0] && progressType('HANDOVER', 'HANDOVER_DEFAULT')[0].interimCommentsInfoList[0] && progressType('HANDOVER', 'HANDOVER_DEFAULT')[0].interimCommentsInfoList[0].context"><strong>交接描述: </strong>{{ progressType('HANDOVER', 'HANDOVER_DEFAULT')[0].interimCommentsInfoList[0].context }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="module-progress-card" v-if="form.serviceTicket.deliveryManagerNickName && progressType('HANDOVER', 'HANDOVER_DELIVERY')[0]">
          <h4>任务交接</h4>
          <div class="progress-content">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'HANDOVER'" :secondState="'HANDOVER_DELIVERY'" />
              <div class="card">
                <div class="body">
                  <p><strong>任务接收人: </strong>{{ form.serviceTicket.deliveryManagerNickName }}</p>
                  <p v-if="progressType('HANDOVER', 'HANDOVER_DELIVERY')[0] && progressType('HANDOVER', 'HANDOVER_DELIVERY')[0].interimCommentsInfoList[0] && progressType('HANDOVER', 'HANDOVER_DELIVERY')[0].interimCommentsInfoList[0].context"><strong>交接描述: </strong>{{ progressType('HANDOVER', 'HANDOVER_DELIVERY')[0].interimCommentsInfoList[0].context }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 返厂服务才展示 start-->
        <div class="module-progress-card" v-if="form.decide.type === 'REPAIR' && /RETURNED|SUCCESS/i.test(form.goods.state)">
          <h4>退货进度</h4>
          <div class="progress-content-line" v-if="/RETURNED|SUCCESS/i.test(form.goods.state) && progressType('GOODS', 'GOODS_RETURNED')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'GOODS'" :secondState="'GOODS_RETURNED'" />
              <div class="card">
                <div class="head" :class="{'border': form.expressInfoMap.GOODS || form.goods.orderNum}">
                  <div class="group title">
                    <span><strong class="status-RETURNED">买家已退货</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.expressInfoMap.GOODS">
                  <p><strong>退货方式: </strong>{{ map.logisticsType[form.expressInfoMap.GOODS.type] }}</p>
                  <p v-if="form.expressInfoMap.GOODS.type !== 'SELF'"><strong>物流单号: </strong>{{ form.expressInfoMap.GOODS['number'] }}</p>
                  <p v-if="form.expressInfoMap.GOODS.type !== 'SELF'"><strong>物流公司: </strong>{{ form.expressInfoMap.GOODS['company'] }}</p>
                  <p class="show-explain" v-if="form.expressInfoMap.GOODS['content']"><strong>说明: </strong>{{ form.expressInfoMap.GOODS['content'] }}</p>
                  <div class="certificates" v-if="expressReturn">
                    <p><strong>物流凭证: </strong></p>
                    <Preview :data="expressReturn"></Preview>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'GOODS'" :secondState="'GOODS_RETURNED'" />
          <div class="progress-content-line" v-if="/SUCCESS/i.test(form.goods.state) && progressType('REPAIR', 'REPAIR_RECEIVED')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'REPAIR'" :secondState="'REPAIR_RECEIVED'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">退货完成</strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 返厂服务才展示 end -->
        <div
          class="module-progress-card"
          v-if="(form[serviceType] && form[serviceType].state === 'WORKING' && form[serviceType].workId) ||
          /RECEIVED|QUOTED|SENDOFF|SUCCESS/i.test(form.repair.state) ||
          /DATE|SUCCESS|FINISH/i.test(form.visit.state) ||
          /DATE|SUCCESS|FINISH/i.test(form.remote.state)
        ">
          <h4>服务进度</h4>
          <div class="progress-content-line" v-if="form[serviceType].workId && progressType(this.serviceType.toUpperCase(), `${this.serviceType.toUpperCase()}_WORKING`)[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="this.serviceType.toUpperCase()" :secondState="`${this.serviceType.toUpperCase()}_WORKING`"/>
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span>创建工单:
                      <el-button type="text" size="medium" @click="routeToWorkflow(`/wf/detail/${form[serviceType].workId}`)">{{ form[serviceType].workId }}</el-button>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 返厂？ start -->
          <div class="progress-content-line" v-if="/RECEIVED|QUOTED|SENDOFF|SUCCESS/i.test(form.repair.state) && progressType('REPAIR', 'REPAIR_RECEIVED')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'REPAIR'" :secondState="'REPAIR_RECEIVED'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span><strong class="status-RECEIVED">厂家已收货</strong></span>
                  </div>
                </div>
                <div class="body" v-if="form.serviceTicket">
                  <p v-if="form.serviceTicket.repairFinishTime"><strong>预计完成时间: </strong>{{ form.serviceTicket.repairFinishTime }}</p>
                  <p class="show-explain" v-if="form.serviceTicket.repairExplain"><strong>说明: </strong>{{ form.serviceTicket.repairExplain }}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'REPAIR'" :secondState="'REPAIR_RECEIVED'" />
          <div class="progress-content-line" v-if="form.decide.type === 'REPAIR' && /QUOTED|SENDOFF|SUCCESS/i.test(form.repair.state) && form.serviceTicket && progressType('REPAIR', 'REPAIR_QUOTED')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'REPAIR'" :secondState="'REPAIR_QUOTED'" />
              <div class="card">
                <div class="head" :class="{'border': form.serviceTicket.supplierReceivingNote}">
                  <div class="group title">
                    <span><strong class="status-COMFIRM">厂家已报价</strong></span>
                  </div>
                </div>
                <div class="body">
                  <p class="show-explain" v-if="form.serviceTicket.supplierReceivingNote"><strong>报价说明: </strong>{{ form.serviceTicket.supplierReceivingNote }}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'REPAIR'" :secondState="'REPAIR_QUOTED'" v-if="form.decide.type === 'REPAIR' && form.serviceTicket.supplement"/>
          <div class="progress-content-line" v-if="/SENDOFF|SUCCESS/i.test(form.repair.state) && progressType('REPAIR', 'REPAIR_SENDOFF')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'REPAIR'" :secondState="'REPAIR_SENDOFF'" />
              <div class="card">
                <div class="head" :class="{'border': serviceExpress || form.serviceTicket.repairFinishExplain}">
                  <div class="group title">
                    <span><strong class="status-SENDOFF">厂家已寄回</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="serviceExpress || form.serviceTicket.repairFinishExplain">
                  <p v-if="serviceExpress.number"><strong>物流单号: </strong>{{ serviceExpress.number }}</p>
                  <p v-if="serviceExpress.company"><strong>物流公司: </strong>{{ serviceExpress.company }}</p>
                  <div class="certificates" v-if="expressOfServiceType">
                    <p><strong>物流凭证: </strong></p>
                    <Preview :data="expressOfServiceType"></Preview>
                  </div>
                    <p class="show-explain" v-if="form.serviceTicket.repairFinishExplain"><strong>说明: </strong>{{ form.serviceTicket.repairFinishExplain }}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'REPAIR'" :secondState="'REPAIR_SENDOFF'" />
          <!-- 返厂? end -->
          <!-- 上门？ start-->
          <div class="progress-content-line" v-if="/DATE|SUCCESS|FINISH/i.test(form.visit.state) && progressType('VISIT', 'VISIT_DATE')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'VISIT'" :secondState="'VISIT_DATE'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span><strong class="status-DATE">已预约</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.serviceTicket">
                  <p v-if="form.serviceTicket.visitTime"><strong>预约时间: </strong>{{ form.serviceTicket.visitTime }}</p>
                  <p v-if="form.serviceTicket.visitServicePerson"><strong>服务人员: </strong>{{ form.serviceTicket.visitServicePerson }}</p>
                  <p v-if="form.serviceTicket.visitServicePhone"><strong>服务人员电话: </strong>{{ form.serviceTicket.visitServicePhone }}</p>
                </div>
                <div class="body" v-if="form.serviceTicket"><p class="show-explain" v-if="form.serviceTicket.visitExplain"><strong>说明: </strong>{{ form.serviceTicket.visitExplain }}</p></div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'VISIT'" :secondState="'VISIT_DATE'" />
          <div class="progress-content-line" v-if="/SUCCESS|FINISH/i.test(form.visit.state) && progressType('VISIT', 'VISIT_SUCCESS')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'VISIT'" :secondState="'VISIT_SUCCESS'" />
              <div class="card">
                <div class="head" :class="{'border': form.serviceTicket.visitFinishExplain || form.certificates.RECEIPT}">
                  <div class="group title">
                    <span><strong class="status-SENDOFF">上门完成</strong></span>
                  </div>
                </div>
                <div class="body" v-if="form.serviceTicket">
                  <p class="show-explain" v-if="form.serviceTicket.visitFinishExplain"><strong>说明: </strong>{{ form.serviceTicket.visitFinishExplain }}</p>
                  <div class="certificates" v-if="form.certificates && form.certificates.RECEIPT">
                    <p><strong>服务签收单: </strong></p>
                    <Preview :data="form.certificates.RECEIPT"></Preview>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'VISIT'" :secondState="'VISIT_SUCCESS'" />
          <!-- 上门？ end-->
        <!-- 远程指导 - 服务进度 - start-->
        <div
          class="progress-content-line"
          v-if="
            /DATE|SUCCESS|FINISH/i.test(form.remote.state) && progressType('REMOTE', 'REMOTE_DATE')[0]
          "
        >
          <div class="card-group">
            <OperationProgress :progress="progress" :state="'REMOTE'" :secondState="'REMOTE_DATE'" />
            <div class="card">
              <div class="head border">
                <div class="group title">
                  <span><strong class="status-DATE">已预约</strong></span>
                </div>
              </div>
              <div class="body express-info" v-if="form.remote">
                <p v-if="form.remote.appointmentTime">
                  <strong>预约时间: </strong>{{ form.remote.appointmentTime }}
                </p>
                <p v-if="form.remote.servicePerson">
                  <strong>服务人员: </strong>{{ form.remote.servicePerson }}
                </p>
                <p v-if="form.remote.servicePhone">
                  <strong>服务人员电话: </strong>{{ form.remote.servicePhone }}
                </p>
              </div>
              <div class="body" v-if="form.remote">
                <p class="show-explain" v-if="form.remote.appointmentNote">
                  <strong>说明: </strong>{{ form.remote.appointmentNote }}
                </p>
                <div class="certificates" v-if="form.certificates && form.certificates.REMOTE">
                  <p><strong>安装视频/手册: </strong></p>
                  <Preview :data="form.certificates.REMOTE"></Preview>
                </div>
              </div>
            </div>
          </div>
        </div>
        <TempReply :progress="progress" :state="'REMOTE'" :secondState="'REMOTE_DATE'" />
        <div
          class="progress-content-line"
          v-if="
            /SUCCESS|FINISH/i.test(form.remote.state) && progressType('REMOTE', 'REMOTE_SUCCESS')[0]
          "
        >
          <div class="card-group">
            <OperationProgress
              :progress="progress"
              :state="'REMOTE'"
              :secondState="'REMOTE_SUCCESS'"
            />
            <div class="card">
              <div
                class="head"
                :class="{
                  border: form.remote.finishNote || form.certificates.RECEIPT,
                }"
              >
                <div class="group title">
                  <span><strong class="status-SENDOFF">远程指导完成</strong></span>
                </div>
              </div>
              <div class="body" v-if="form.remote">
                <p class="show-explain" v-if="form.remote.finishNote">
                  <strong>说明: </strong>{{ form.remote.finishNote }}
                </p>
              </div>
            </div>
          </div>
        </div>
        <TempReply :progress="progress" :state="'VISIT'" :secondState="'VISIT_SUCCESS'" />
        <!-- 远程指导 - 服务进度 - end-->
          <div class="progress-content-line" v-if="(/SUCCESS/i.test(form.repair.state) || /FINISH/i.test(form.visit.state) ||  /FINISH/i.test(form.remote.state))  && progressType(this.serviceType.toUpperCase(), `${this.serviceType.toUpperCase()}_FINISH`)[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="this.serviceType.toUpperCase()" :secondState="`${this.serviceType.toUpperCase()}_FINISH`" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">服务完成</strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="this.serviceType.toUpperCase()" :secondState="`${this.serviceType.toUpperCase()}_FINISH`" />
        </div>
          <!-- 补发服务进度start -->
        <div class="module-progress-card" v-if="form.serviceTicket && form.serviceTicket.reissue && (/SENT|SUCCESS/i.test(form.serviceTicket.reissue.state) || form.serviceTicket.reissue.workId || (progressType('REISSUE', 'REISSUE_PENDING')[0] && progressType('REISSUE', 'REISSUE_PENDING')[0].interimCommentsInfoList))">
          <h4>服务进度</h4>
          <TempReply :progress="progress" :state="'REISSUE'" :secondState="'REISSUE_PENDING'" />
          <div class="progress-content-line" v-if="form.serviceTicket.reissue.workId && progressType('REISSUE', 'REISSUE_PENDING')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'REISSUE'" :secondState="'REISSUE_PENDING'"/>
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <!-- <span><strong :class="`status-${form[serviceType].state}`">{{ map[`${serviceType}Status`][form.repair.state] }}</strong></span> -->
                    <span>创建工单:
                      <el-button type="text" size="medium" @click="routeToWorkflow(`/wf/detail/${form.serviceTicket.reissue.workId}`)">{{ form.serviceTicket.reissue.workId }}</el-button>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="progress-content-line" v-if="form.serviceTicket.reissue && /PENDING|SENT|SUCCESS/i.test(form.serviceTicket.reissue.state) && progressType('REISSUE', 'REISSUE_SENT')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'REISSUE'" :secondState="'REISSUE_SENT'" />
              <div class="card">
                <div class="head" :class="{'border': serviceExpress}">
                  <div class="group title">
                    <span><strong class="status-SENDOFF">供应商已发货</strong></span>
                  </div>
                </div>
                <div v-for="item in form.reissueExpressList" :key="item.id" style="margin: 0 10px; padding: 10px 0; border-bottom: 1px solid #ddd">
                  <div class="body express-info">
                    <p v-if="item.type"><strong>配送方式: </strong>{{ logisticsType(item.type) }}</p>
                    <p v-if="item.number"><strong>物流单号: </strong>{{ item.number }}</p>
                    <p v-if="item.company"><strong>物流公司: </strong>{{ item.company }}</p>
                    <div class="certificates" v-if="expressById(item.id)">
                      <p><strong>物流凭证: </strong></p>
                      <Preview :data="expressById(item.id)"></Preview>
                    </div>
                      <p class="show-explain" v-if="item.content"><strong>说明: </strong>{{ item.content }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'REISSUE'" :secondState="'REISSUE_SENT'" />
          <div class="progress-content-line" v-if="form.serviceTicket.reissue && /SUCCESS/i.test(form.serviceTicket.reissue.state)  && progressType('REISSUE', 'REISSUE_SUCCESS')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'REISSUE'" :secondState="'REISSUE_SUCCESS'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">服务完成</strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
          <!-- 补发服务进度end -->
        <div class="module-progress-card" v-if="form.serviceTicket.supplement || form.serviceTicket.needPay === 0">
          <h4>补单进度</h4>
          <div class="progress-content-line" v-if="form.serviceTicket.supplement && /CONFIRM|ORDERS|SUCCESS|REFUSE/i.test(form.serviceTicket.supplement.state) && progressType('SUPPLEMENT', 'SUPPLEMENT_CONFIRM')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'SUPPLEMENT'" :secondState="'SUPPLEMENT_CONFIRM'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span><strong class="status-COMFIRM">服务需收费</strong></span>
                  </div>
                </div>
                <div class="body" v-if="form.supplementInfo && form.supplementInfo.supplementOrderInfoList && form.supplementInfo.supplementOrderInfoList.length">
                  <el-table :data="form.supplementInfo.supplementOrderInfoList" style="width: 700px;" align="center">
                    <el-table-column prop="skuNo" label="SKU"></el-table-column>
                    <el-table-column prop="skuDesc" label="物料描述" width="250px"></el-table-column>
                    <el-table-column prop="quantity" label="数量">
                    </el-table-column>
                  </el-table>
                </div>
                <div class="body express-info" v-if="form.serviceTicket.supplement">
                  <!-- <p v-if="form.serviceTicket.supplement.sku"><strong>维修服务物料: </strong>{{ form.serviceTicket.supplement.sku }}</p> -->
                  <div class="certificates" v-if="form.certificates && form.certificates.QUOTATION">
                    <p><strong>报价单: </strong></p>
                    <Preview :data="form.certificates.QUOTATION"></Preview>
                  </div>
                  <p class="show-explain" v-if="form.serviceTicket.supplement.explain"><strong>说明: </strong>{{ form.serviceTicket.supplement.explain }}</p>
                </div>
              </div>
            </div>
          </div>
          <TempReply :progress="progress" :state="'SUPPLEMENT'" :secondState="'SUPPLEMENT_CONFIRM'" />
          <div class="progress-content-line" v-if="form.serviceTicket.supplement && /ORDERS|SUCCESS/i.test(form.serviceTicket.supplement.state) && progressType('SUPPLEMENT', 'SUPPLEMENT_ORDERS')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'SUPPLEMENT'" :secondState="'SUPPLEMENT_ORDERS'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <div>已创建SO:
                      <div v-if="omsNoList() && omsNoList().length">
                        <div v-for="(omsNo, index) in omsNoList()" :key="index">
                          <el-button v-if="omsNo" type="text" @click="jumpToSoOrder(omsNo)" style="font-size: 18px">{{ omsNo }}</el-button>
                        </div>
                      </div>
                      <!-- <el-button v-if="form.serviceTicket.supplement && form.serviceTicket.supplement.omsNo" type="text" @click="jumpToSoOrder" style="font-size: 18px">{{ form.serviceTicket.supplement.omsNo }}</el-button> -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="progress-content-line" v-if="form.serviceTicket.supplement && /SUCCESS/i.test(form.serviceTicket.supplement.state) && progressType('SUPPLEMENT', 'SUPPLEMENT_SUCCESS')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'SUPPLEMENT'" :secondState="'SUPPLEMENT_SUCCESS'" />
              <div class="card">
                <div class="head" :class="{'border': form.serviceTicket.supplement.pmsNo}">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">补单完成</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.serviceTicket.supplement.pmsNo">
                  <span>PO：{{ form.serviceTicket.supplement.pmsNo }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="progress-content-line" v-if="form.serviceTicket.supplement && /REFUSE/i.test(form.serviceTicket.supplement.state) && progressType('SUPPLEMENT', 'SUPPLEMENT_REFUSE')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'SUPPLEMENT'" :secondState="'SUPPLEMENT_REFUSE'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-REFUSE">客服拒绝付费 </strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="progress-content-line" v-if="form.serviceTicket.needPay === 0 && progressType('SUPPLEMENT', 'SUPPLEMENT_NO_PAY')[0]">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'SUPPLEMENT'" :secondState="'SUPPLEMENT_NO_PAY'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-REFUSE">服务无需付费 </strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="module-progress-card" v-if="/SUCCESS|CANCEL|REFUSED|OBSOLETE/.test(form.state)">
          <h4>售后进度</h4>
          <div class="progress-content" v-if="form.state === 'SUCCESS'">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'END'" :secondState="'TICKET_SUCCESS'" />
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">完结</strong></span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="progress-content" v-if="form.state === 'CANCEL'">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'END'" :secondState="'TICKET_CANCEL'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">售后单已取消</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.decide">
                  <p><strong>取消原因：</strong>{{form.decide.cancelReason}}</p>
                  <p v-if="form.decide.cancelExplain"><strong>说明：</strong>{{form.decide.cancelExplain}}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="progress-content" v-if="form.state === 'REFUSED'">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'END'" :secondState="'TICKET_REFUSED'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">售后单已被拒绝</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.decide">
                  <p><strong>拒绝原因：</strong>{{form.decide.description}}</p>
                  <p v-if="form.decide.refuseReason"><strong>拒绝说明：</strong>{{form.decide.refuseReason}}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="progress-content" v-if="form.state === 'OBSOLETE'">
            <div class="card-group">
              <OperationProgress :progress="progress" :state="'END'" :secondState="'TICKET_OBSOLETE'" />
              <div class="card">
                <div class="head border">
                  <div class="group title">
                    <span><strong class="status-SUCCESS">售后单已被作废</strong></span>
                  </div>
                </div>
                <div class="body express-info" v-if="form.obsolete">
                  <p><strong>作废原因：</strong>{{map.obsoleteType[form.obsolete.type]}}</p>
                  <p v-if="form.decide.refuseReason"><strong>说明：</strong>{{form.obsolete.reason}}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>
<script>
import Preview from '../components/Preview'
import OperationProgress from '../components/OperationProgress'
import TempReply from '../components/TempReply'
import * as shortid from 'shortid'
import { api as bossApi } from '@/api/boss'
import { routeToWorkflow } from '@/utils'
export default {
  data () {
    return {
      mockData: [
        { type: '上门', people: 'gu', phone: '23333333' },
        { type: '售后类型', people: 'renyuan', phone: '122222' }
      ]
    }
  },
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    map: {
      type: Object,
      default: () => ({})
    },
    progress: {
      type: Array,
      default: () => ([])
    }
  },
  components: {
    Preview,
    OperationProgress,
    TempReply
  },
  created() {
  },
  watch: {
    progress(newVal, oldVal) {
      console.log('progress', newVal, oldVal);
    }
  },
  computed: {
    serviceType () {
      if (this.form.repair.state) return 'repair'
      if (this.form.visit.state) return 'visit'
      if (this.form.remote.state) return 'remote'
      if (this.form.detection.state) return 'detection'
      if (this.form.serviceTicket?.reissue?.state) return 'reissue'
      return ''
    },
    serviceExpress () {
      return this.form.expressInfoMap[this.serviceType.toUpperCase()]
    },
    expressOfServiceType () {
      const id = this.serviceExpress && this.serviceExpress.id
      return (
        this.form.certificates &&
        this.form.certificates.EXPRESS &&
        this.form.certificates.EXPRESS.filter(item => item.ownerId === id)
      )
    },
    expressReturn () {
      const id =
        this.form.expressInfoMap &&
        this.form.expressInfoMap.GOODS &&
        this.form.expressInfoMap.GOODS.id
      return (
        this.form.certificates &&
        this.form.certificates.EXPRESS &&
        this.form.certificates.EXPRESS.filter(item => item.ownerId === id)
      )
    }
  },
  methods: {
    routeToWorkflow,
    formatTime (dateString) {
      if (dateString) {
        const t = new Date(dateString)
        const date = dateString.split(' ')[0]
        const time = t.toTimeString().split(' ')[0]
        return `${date} ${time}`
      }
      return dateString
    },
    progressType(state, secondState) {
      return this.progress.filter(item => item.state === state && item.secondState === secondState)
    },
    omsNoList () {
      const list = this.form.supplementInfo && this.form.supplementInfo.supplementOrderInfoList && this.form.supplementInfo.supplementOrderInfoList.map(item => item.omsNo)
      return Array.from(new Set(list)) || []
    },
    jumpToSoOrder (omsNo) {
      // const { omsNo } = this.form.serviceTicket?.supplement
      bossApi({
        prefix: '/api-opc',
        url: '/v1/so/template/so/detail',
        query: {
          omsNo
        },
        complete: res => {
          if (res.code === 200 && res.data) {
            this.$router.jumpToSoOrderDetail({
              query: {
                soNo: omsNo,
                sapOrderNo: res?.data?.soVoucherId,
                id: shortid.generate(),
                refresh: true
              }
            })
          } else {
            this.$message.error(res?.message || res?.msg || '操作失败');
          }
        }
      })
    },
    expressById (id) {
      return (
        this.form.certificates &&
        this.form.certificates.EXPRESS &&
        this.form.certificates.EXPRESS.filter(item => item.ownerId === id)
      )
    },
    logisticsType (val) {
      const type = {
        'VISIT': '上门自提',
        'EXPRESS': '物流快递',
        'SELF': '客户自运',
        'OTHER': '其他'
      }
      return type[val] || ''
    }
  }
}
</script>

<style lang="less" src="../Detail/style.less"></style>
