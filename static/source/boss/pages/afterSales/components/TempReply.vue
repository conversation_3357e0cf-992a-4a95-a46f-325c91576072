<template>
  <div class="progress-content-line" v-if="progressType(state, secondState)[0] && progressType(state, secondState)[0].interimCommentsInfoList">
    <div class="card-group" v-for="item in progressType(state, secondState)[0].interimCommentsInfoList" :key="item.id">
      <div class="operation">
        <span>{{ formatTime(item.createdTime) }}</span>
        <span v-show="item.createdBy">操作者: {{ item.createdBy }}</span>
        <span v-show="item.department">部门: {{ item.department }}</span>
        <span>来源: BOSS</span>
      </div>
      <div class="card">
        <div class="head border">
          <div class="group title">
            <span class="status-COMFIRM"><strong>临时回复</strong></span>
          </div>
        </div>
        <div class="body express-info pre-line">
          <div>
            <strong>回复内容: </strong>
            <div>{{ item.context }}</div>
          </div>
          <div class="certificates" v-if="item.certificateInfos">
            <p><strong>附件: </strong></p>
            <Preview :data="item.certificateInfos"></Preview>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Preview from '../components/Preview'
export default {
  props: {
    state: {
      type: String,
      default: ''
    },
    secondState: {
      type: String,
      default: ''
    },
    progress: {
      type: Array,
      default: () => ([])
    }
  },
  components: {
    Preview
  },
  watch: {
    progress(newVal, oldVal) {
      // console.log('progress', newVal, oldVal);
    }
  },
  computed: {
    // certificateInfos () {
    //   return this.progressType(state, secondState)[0].interimCommentsInfoList
    // }
  },
  methods: {
    formatTime (dateString) {
      if (dateString) {
        const t = new Date(dateString)
        const date = dateString.split('T')[0]
        const time = t.toTimeString().split(' ')[0]
        return `${date} ${time}`
      }
      return dateString
    },
    progressType(state, secondState) {
      return this.progress.filter(item => item.state === state && item.secondState === secondState)
    }
  }
}
</script>

<style lang="less" src="../Detail/style.less"></style>
