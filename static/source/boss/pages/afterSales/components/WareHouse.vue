<template>
  <el-select
    popper-class="module-select-warehouse"
    filterable
    clearable
    remote
    :placeholder="placeholder"
    :value="name"
    :remote-method="getList"
    :loading="loading.list"
    @change="afterChange"
    @clear="afterClear">
    <el-option v-for="(item, index) in option.list" :key="index" :label="item.warehouseDescr" :value="item.warehouseId">
    </el-option>
  </el-select>
</template>

<script>
import { api } from '@/api/boss'

export default {
  data () {
    return {
      name: this.value,
      loading: {
        list: false
      },
      option: {
        list: []
      }
    }
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: {
      type: String
    }
  },
  watch: {
    value (val) {
      this.name = val
      this.$emit('inpput', val)
    }
  },
  methods: {
    afterChange (val) {
      const target = this.option.list.find(item => item.warehouseId === val)
      this.$emit('input', target ? target.warehouseId : '')
      this.$emit('select', target)
    },
    afterClear () {
      this.$emit('input', '')
    },
    getList (query) {
      if (!query) return
      this.loading.list = true
      api({
        prefix: '/api-sales',
        url: '/ticket/warehouse',
        query: {
          warehouseDescr: query
        }
      }).then(res => {
        if (res.code === 200 && res.data) {
          this.option.list = res.data
        } else {
          this.option.list = []
        }
        this.loading.list = false
      }).catch(err => {
        console.log(err)
        this.option.list = []
        this.loading.list = false
      })
    }
  }
}
</script>

<style>

</style>
