<template>
  <div class="operation" v-if="progressType(state, secondState)[progressType(state, secondState).length - 1]">
    <span v-show="progressType(state, secondState)[progressType(state, secondState).length - 1].createdTime">{{ formatTime(progressType(state, secondState)[progressType(state, secondState).length - 1].createdTime) }}</span>
    <span v-show="progressType(state, secondState)[progressType(state, secondState).length - 1].createdBy">操作者: {{ progressType(state, secondState)[progressType(state, secondState).length - 1].createdBy }}</span>
    <span v-show="progressType(state, secondState)[progressType(state, secondState).length - 1].department">部门: {{ progressType(state, secondState)[progressType(state, secondState).length - 1].department }}</span>
    <span v-show="progressType(state, secondState)[progressType(state, secondState).length - 1].timeConsuming">耗时: {{ progressType(state, secondState)[progressType(state, secondState).length - 1].timeConsuming }}</span>
    <span v-show="progressType(state, secondState)[progressType(state, secondState).length - 1].source">来源: {{ progressType(state, secondState)[progressType(state, secondState).length - 1].source }}</span>
  </div>
</template>

<script>
export default {
  props: {
    state: {
      type: String,
      default: ''
    },
    secondState: {
      type: String,
      default: ''
    },
    progress: {
      type: Array,
      default: () => ([])
    }
  },
  watch: {
    progress(newVal, oldVal) {
      // console.log('progress', newVal, oldVal);
    }
  },
  methods: {
    formatTime (dateString) {
      if (dateString) {
        const t = new Date(dateString)
        const date = dateString.split(' ')[0]
        const time = t.toTimeString().split(' ')[0]
        return `${date} ${time}`
      }
      return dateString
    },
    progressType(state, secondState) {
      return this.progress.filter(item => item.state === state && item.secondState === secondState)
    }
  }
}
</script>

<style lang="less" src="../Detail/style.less"></style>
