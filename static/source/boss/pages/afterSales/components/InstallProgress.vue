<template>
  <div>
    <div class="banner" v-if="(isServices || isAdmin) && form.currentServiceState">
      {{`当前售后单“${form.currentServiceState}”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
      <el-button type="primary" v-if="form.button && form.button.customerCancelRequestButton" @click="handleInstallStatus('cancel')">取消售后申请</el-button>
      <el-button type="primary" v-if="form.button && form.button.customerEditApplyInfoButton" @click="handleInstallStatus('edit')">编辑申请信息</el-button>
      <el-button type="primary" v-if="form.button && form.button.customerInterimReplyButton" @click="handleInstallStatus('tempReply', getCurProgress.state , getCurProgress.secondState)">临时回复</el-button>
      <el-button type="primary" v-if="form.button && form.button.customerConfirmAndCreateOrderButton" @click="handleInstallStatus('supplement')">确认并下单</el-button>
      <el-button type="primary" v-if="form.button && form.button.customerConfirmServiceCompleteButton" @click="handleInstallStatus('confirmServiceComplete')">确认服务完成</el-button>
      <el-button type="primary" v-if="form.button && form.button.customerAddOrderButton" @click="handleInstallStatus('addSO')">客户已下单</el-button>
      <el-button type="primary" v-if="form.button && form.button.customerRejectPayButton" @click="handleInstallStatus('rejectPay')">拒绝付费</el-button>
    </div>
    <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.currentServiceState">
      {{`当前售后单“${form.currentServiceState}”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
      <el-button type="primary" v-if="form.button && form.button.operatorEditInfoButton" @click="handleInstallStatus('serviceInfo')">维护服务信息</el-button>
      <el-button type="primary" v-if="form.button && form.button.operatorHandoverButton" @click="handleInstallStatus('handoff')">任务交接</el-button>
      <el-button type="primary" v-if="form.button && form.button.operatorInterimReplyButton" @click="handleInstallStatus('tempReply', getCurProgress.state , getCurProgress.secondState)">临时回复</el-button>
      <el-button type="primary" v-if="form.button && form.button.operatorCreateMaterialButton" @click="handleInstallStatus('needPay')">创建售后物料</el-button>
    </div>

    <!-- 维护服务信息 -->
    <el-dialog title="维护服务信息" center :visible.sync="show.serviceInfo" width="900px" custom-class="dialog-aftersale" @open="openServiceInfoDialog">
      <el-form :inline="true" :model="dialog.serviceInfo" :rules="rules.serviceInfo" v-loading="loading.serviceInfoFormLoading" ref="serviceInfoRef">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="供应商" prop="supply" style="width: 100%">
              <SelectSupplier v-model="dialog.serviceInfo.supply" :showValue="true" @change="handleSupplyChange" />
            </el-form-item>
          </el-col>
        </el-row>
            <el-form-item label="供应商售后联系人" prop="supplyContact">
              <el-input v-model="dialog.serviceInfo.supplyContact" clearable placeholder="请输入" />
            </el-form-item>
            <el-form-item label="供应商售后联系电话" prop="supplyMobile">
              <el-input v-model="dialog.serviceInfo.supplyMobile" clearable placeholder="请输入" />
            </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitServiceInfo" :loading="loading.submitServiceInfoLoading">提交</el-button>
        <el-button type="primary" @click="show.serviceInfo = false">取消</el-button>
      </p>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getSupplierInfo, provideSupplierInfo } from '@/api/aftersales'
import SelectSupplier from './SelectSupplier.vue'
import { checkPhone } from '@/utils/validate'

export default {
  data() {
    return {
      dialog: {
        serviceInfo: {
          supply: '',
          supplyNo: '',
          supplyName: '',
          supplyContact: '',
          supplyMobile: ''
        }
      },
      rules: {
        serviceInfo: {
          supplyContact: [{ required: true, message: '请输入', trigger: 'blur' }],
          supplyMobile: [{ required: true, validator: checkPhone }]
        }
      },
      loading: {
        serviceInfoFormLoading: false,
        submitServiceInfoLoading: false
      }
    }
  },
  props: {
    form: Object,
    show: Object,
    progress: Array
  },
  watch: {
    form: {
      handler (newVal) {
        console.log('数据变化了', newVal)
        this.$forceUpdate()
      },
      deep: true
    }
  },
  components: {
    SelectSupplier
  },
  computed: {
    ...mapState(['userRole', 'user']),
    isAdmin () {
      return !!~this.userRole.indexOf('boss-售后超管')
    },
    // 是否是客服
    isServices () {
      return (!!~this.userRole.indexOf('data-客服')) || (!!~this.userRole.indexOf('boss-客服'))
    },
    // 是否是运营
    isOperates () {
      return !!~this.userRole.indexOf('商品中心-售后运营')
    },
    // 是否是采购
    isBuyer () {
      if (this.userRole.find(item => ['PMS采购员', 'PMS采购员(非产品)', 'PMS采购经理', 'PMS采购经理(非产品)', 'PMS采购员(非产品采购)'].indexOf(item) > -1)) {
        return true;
      }
      return false;
    },
    // 上门服务进度
    visitState () {
      return this.form.visit && this.form.visit.state
    },
    getCurProgress () {
      return (this.progress.length && this.progress[this.progress.length - 1]) || {}
    }
  },
  methods: {
    handleInstallStatus(type, firstState, secondState) {
      this.$emit('handleInstallStatus', type, firstState, secondState)
    },
    openServiceInfoDialog () {
      const order = this.form.orderDetails.find(item => item.supplierNo);
      if (order) {
        this.dialog.serviceInfo.supply = { supplierNo: order.supplierNo };
      }
    },
    handleSupplyChange(supplier) {
      this.dialog.serviceInfo.supplyNo = supplier.supplierNo;
      this.dialog.serviceInfo.supplyName = supplier.supplierName;
      this.getSupplierInfo(this.dialog.serviceInfo.supplyNo)
    },
    getSupplierInfo(supplierNo) {
      this.loading.serviceInfoFormLoading = true;
      getSupplierInfo({ parentIdTypeCode: 3, supplierNo }).then(res => {
        if (res.data && res.data.data) {
          const data = res.data.data.find(item => item.responsibleType === 5);
          if (data) {
            this.dialog.serviceInfo.supplyContact = data.fullName;
            this.dialog.serviceInfo.supplyMobile = data.telephone1 || data.mobilePhone1;
          }
        }
      })
        .catch(err => console.log(err))
        .finally(() => {
          this.loading.serviceInfoFormLoading = false;
        })
    },
    submitServiceInfo() {
      this.$refs.serviceInfoRef.validate(valid => {
        console.log(this.form)
        if (valid) {
          this.loading.submitServiceInfoLoading = true;
          const data = {
            ...this.dialog.serviceInfo,
            ticketId: this.form.id
          }
          delete data.supply;
          provideSupplierInfo(data).then(res => {
            if (res.code === 200) {
              this.$message.success('提交成功')
            } else {
              this.$message.error(res.message || '提交失败')
            }
          })
          .catch(err => console.log(err))
          .finally(() => {
            this.show.serviceInfo = false;
            this.loading.submitServiceInfoLoading = false;
            this.$emit('getInfo', this.form.id)
            this.$emit('getProgress')
          })
        }
      })
    }
  }
}
</script>

<style>
</style>
