<template>
  <el-select
    :value="value"
    :placeholder="`选择${title}`"
    filterable
    remote
    reserve-keyword
    clearable
    style="width: 100%"
    value-key="contactId"
    :disabled="disabled"
    :remote-method="selectContact"
    :loading="loading"
    @change="changeContact"
  >
    <el-option
      v-show="contactList && contactList.length >= 20"
      style="color: #ccc"
      disabled
      :value="-1"
    >
      已展示部分联系人，其他联系人请输入字符进行查询
    </el-option>
    <el-option
      v-for="(item, index) in contactList"
      :key="item.contactId"
      :label="isAddress ? item.address : item.contactName"
      :value="item"
      :disabled="index === 0 || !item.address"
    >
      <div
        class="ba-row-start selectClientItem"
        :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
      >
        <div>{{ item.contactName }}</div>
        <div>{{ item.contactId }}</div>
        <div>{{ item.contactPhone || '--' }}</div>
        <div>{{ item.address || '--' }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import request from '@/utility/request'

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      loading: false,
      contactList: [{
        contactName: '联系人',
        contactId: '联系人编号',
        contactPhone: '联系人电话',
        address: '联系人地址'
      }],
      customerCode: ''
    }
  },
  props: {
    title: { type: String, default: '' },
    value: [String, Object],
    disabled: { type: Boolean, default: false },
    isAddress: { type: Boolean, default: false },
    remoteMethod: { type: Function, default: null }
  },
  methods: {
    async selectContact (contactName) {
      this.loading = true;
      const res = await request({
        url: '/api-opc/v1/contact/list',
        method: 'get',
        params: {
          current: 1,
          size: 20,
          customerCode: this.customerCode,
          contactName
        }
      });
      if (res?.code === 200) {
        this.contactList = [
          {
            contactName: '联系人',
            contactId: '联系人编号',
            contactPhone: '联系人电话',
            address: '联系人地址'
          },
          ...res?.data?.records
        ];
      }
      this.loading = false;
  },

  initContact (customerNo) {
    this.customerCode = customerNo;
    this.selectContact();
  },
  async setContact (
    contactName,
    customerNo,
    contactId,
    contactPhone,
    contactAddress,
  ) {
    this.customerCode = customerNo;
    await this.selectContact(contactName);
    const res = this.contactList.find((item) => item.contactId === contactId);
    if (res) {
      this.$emit('change', res);
    } else {
      const contact = {
        contactName: contactName,
        contactId: contactId,
        contactPhone: contactPhone,
        address: contactAddress
      };
      this.$emit('change', contact);
      this.contactList.push(contact);
    }
    // emit('update:data', contactList)
  },
  changeContact (val) {
    this.$emit('change', val);
  }
  }
  // computed: {
  //   contactData: {
  //     get: () => this.data,
  //     set: (val) => emit('update:data', val)
  //   }
  // }
}

</script>

<style scoped lang="scss">
.ba-row-start {
  display: flex;
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
    overflow: auto;
  }
}
</style>
