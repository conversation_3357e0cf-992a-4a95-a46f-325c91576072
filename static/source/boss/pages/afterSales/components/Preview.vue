<template>
  <div class="module-preview">
    <div class="img" v-for="(item, index) in data" :key="index">
      <a :href="formatPicUrl(item.url)" target="_blank" v-if="isImage(item.url)">
        <img :src="formatPicUrl(item.url)" :alt="item.name">
      </a>
      <a :href="item.url" target="_blank" :alt="item.name" v-else>{{item.name}}</a>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array
    }
  },
  methods: {
    formatPicUrl (url) {
      if (/pathfinder/i.test(url)) {
        return url.replace(/\?.+/, '?x-oss-process=style/common_style')
      }
      return url
    },
    isImage (str) {
      return /(jpe?g|png|gif|ico|webp)(\?.+)?$/.test(str)
    }
  }
}
</script>

<style lang="less">
.module-preview{
  display: flex;
  flex-wrap: wrap;
  .img{
    max-width: 200px;
    margin-right: 10px;
    img{
      display: block;
      max-width: 100%;
    }
  }
  a{
    display: flex;
    color: #597bee;
    text-align: center;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f8f8f8;
    padding: 10px;
  }
}
</style>
