import { api, getUser, batchAddOrder } from '@/api/aftersales'
import { api as bossApi } from '@/api/boss'
import { orderList } from '@/api/ecorp'
import { orderDetail } from '@/api/orderSale'
import Judge from '../Judge'
import OrderInfo from '../components/OrderInfo'
import Preview from '../components/Preview'
import ServiceProgress from '../components/ServiceProgress'
import InstallProgress from '../components/InstallProgress'
import { mapState } from 'vuex'
import { upload } from '@/utils/upload'
import { remove, cloneDeep, uniqBy } from 'lodash'
import { RemoteSKU } from '@kun-ui/remote-sku'
import * as shortid from 'shortid'
import { coverMobileAndLandline, routeToWorkflow } from '@/utils/index'
import {
  repairStatus,
  visitStatus,
  returnStatus,
  refundStatus,
  invoiceStatus,
  replaceStatus,
  logisticsType,
  reissueLogisticsType,
  oaStatus,
  type,
  getMap,
  originalType,
  payPlatform,
  invoiceType,
  materialGroup,
  company,
  invoiceHandler,
  returnInvoiceType,
  obsoleteType,
  source,
  ruleNumber,
  repairState,
  visitProgressType,
  remoteProgressType
} from '../option'
import '@boss/web-components';

export default {
  data () {
    return {
      coverMobileAndLandline,
      afterSaleInfo: [
        { label: '申请类型', key: 'createType' },
        { label: '申请渠道', key: 'source' },
        { label: '申请原因', key: 'reason' },
        { label: '申请数量', key: 'quantity' },
        { label: '申请金额', key: 'money' },
        { label: '客户联系人', key: 'customerContact' },
        { label: '客户手机号', key: 'customerPhone' },
        { label: '客户收货联系人', key: 'contact' },
        { label: '客户收货电话', key: 'phone' },
        { label: '客户收货地址', key: 'address' },
        { label: '问题描述', key: 'question' }
      ],
      expressInfo: [
        { label: '退货寄送方式', key: 'type' },
        { label: '物流单号', key: 'number' },
        { label: '物流公司', key: 'company' },
        { label: '联系电话', key: 'phone' },
        { label: '自运司机姓名', key: 'contact' },
        { label: '自运司机电话', key: 'driverPhone' },
        { label: '自运司机牌号', key: 'car' },
        { label: '退货说明', key: 'content' }
      ],
      id: this.$route.params.id,
      uploadUrl: '/fe-upload/api/upload/',
      rule: {
        reject: {
          reason: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        obsolete: {
          reason: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        order: {
          orderNum: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        logistics: {
          type: [{ required: true, message: '请输入', trigger: 'blur' }],
          number: [{ required: true, message: '请输入', trigger: 'blur' }],
          company: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        invoice: {
          reInvoiceNum: [
            { required: true, message: '请输入发票号', trigger: 'blur' }
          ],
          companyId: [{ required: true, message: '请输入', trigger: 'blur' }],
          invoiceTitle: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          invoiceType: [{ required: true, message: '请输入', trigger: 'blur' }],
          invoiceDate: [{ required: true, message: '请输入', trigger: 'blur' }],
          invoiceNum: [{ required: true, message: '请输入', trigger: 'blur' }],
          unsolved: [{ required: true, message: '请输入', trigger: 'blur' }],
          draftNo: [{ required: true, message: '请输入', trigger: 'blur' }],
          type: [{ required: true, message: '请输入', trigger: 'blur' }],
          originalType: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          reason: [{ required: true, message: '请输入', trigger: 'blur' }],
          certificates: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        refund: {
          company: [{ required: true, message: '请输入', trigger: 'blur' }],
          number: [{ required: true, message: '请输入', trigger: 'blur' }],
          bank: [{ required: true, message: '请输入', trigger: 'blur' }],
          way: [{ required: true, message: '请输入', trigger: 'blur' }],
          account: [{ required: true, message: '请输入', trigger: 'blur' }],
          oldDate: [{ required: true, message: '请输入', trigger: 'blur' }],
          amount: [{ required: true, message: '请输入', trigger: 'blur' }],
          oldAmount: [
            { required: true, message: '请输入', trigger: 'blur' },
            { validator: ruleNumber, trigger: 'blur' }
          ],
          companyId: [{ required: true, message: '请输入', trigger: 'blur' }],
          reason: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        cancel: {
          reason: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        tempReply: {
          context: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        judgeServiceType: {
          afterSaleType: [{ required: true, message: '请输入', trigger: 'blur' }],
          serverSupplier: [{ required: true, message: '请输入', trigger: 'blur' }],
          receivePerson: [{ required: true, message: '请输入', trigger: 'blur' }],
          telephone: [{ required: true, message: '请输入', trigger: 'blur' }],
          address: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        refuse: {
          reason: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        handoff: {
          person: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        serviceExpress: {
          state: [{ required: true, message: '请输入', trigger: 'blur' }],
          repairFinishTime: [{ required: true, message: '请输入', trigger: 'blur' }],
          company: [{ required: true, message: '请输入', trigger: 'blur' }],
          number: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        remote: {
          state: [{ required: true, message: '请输入', trigger: 'blur' }],
          // visitTime: [{ required: true, message: '请输入', trigger: 'blur' }],
          servicePerson: [{ required: true, message: '请输入', trigger: 'blur' }],
          servicePhone: [{ required: true, message: '请输入', trigger: 'blur' }]
          // certificates: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        visit: {
          state: [{ required: true, message: '请输入', trigger: 'blur' }],
          visitTime: [{ required: true, message: '请输入', trigger: 'blur' }],
          visitServicePerson: [{ required: true, message: '请输入', trigger: 'blur' }],
          visitServicePhone: [{ required: true, message: '请输入', trigger: 'blur' }],
          certificates: [{ required: true, message: '请输入', trigger: 'blur' }]
        },
        needPay: {
          supplementSku: [{ required: true, message: '请输入', trigger: 'blur' }]
        }
      },
      load: {
        detail: false,
        addOrderLoading: false,
        submitOrderLoading: false
      },
      info: {
        order: {}
      },
      flag: {
        info: true
      },
      requesting: false,
      dialog: {
        obsolete: { reason: '', type: 0 },
        reject: { reason: '', refuseReason: '' },
        cancel: { reason: '', explain: '' },
        logistics: {
          certificates: [],
          type: ''
        },
        order: { orderNum: '' },
        repair: { state: '' },
        invoice: {
          certificates: []
        },
        return: {},
        refund: {},
        tempReply: {
          context: '',
          certificates: []
        },
        judgeServiceType: {
          afterSaleType: '',
          serverSupplier: '',
          receivePerson: '',
          telephone: '',
          address: ''
        },
        refuse: {
          reason: ''
        },
        handoff: {
          person: '',
          comment: ''
        },
        serviceExpress: {
          state: '',
          certificates: []
        },
        visit: {
          state: '',
          certificates: []
        },
        remote: {
          state: '',
          certificates: []
        },
        needPay: {
          certificates: []
        },
        addSO: {
          omsNo: ''
        }
      },
      show: {
        obsolete: false,
        fillInvoice: false,
        approve: false,
        reject: false,
        cancel: false,
        logistics: false,
        order: false,
        repair: false,
        editReturn: false,
        editInvoice: false,
        editRefund: false,
        confirmApplyInfo: false,
        tempReply: false,
        judgeServiceType: false,
        refuse: false,
        handoff: false,
        serviceExpress: false,
        confirmServiceComplete: false,
        rejectPay: false,
        visit: false,
        remote: false,
        needPay: false,
        addSO: false, // 客户已下单-填写销售订单
        serviceInfo: false // 维护服务信息

      },
      option: {
        repairStatus,
        visitStatus,
        detectionStatus: repairStatus,
        returnStatus,
        invoiceStatus,
        replaceStatus,
        logisticsType,
        reissueLogisticsType,
        payPlatform,
        refundStatus,
        originalType,
        invoiceType,
        company,
        materialGroup,
        oaStatus,
        returnInvoiceType,
        obsoleteType,
        repairState,
        visitProgressType,
        remoteProgressType
      },
      map: {
        type: getMap(type),
        repairStatus: getMap(repairStatus),
        visitStatus: getMap(visitStatus),
        detectionStatus: getMap(repairStatus),
        returnStatus: getMap(returnStatus),
        invoiceStatus: getMap(invoiceStatus),
        replaceStatus: getMap(replaceStatus),
        refundStatus: getMap(refundStatus),
        logisticsType: getMap(logisticsType),
        reissueLogisticsType: getMap(reissueLogisticsType),
        obsoleteType: getMap(obsoleteType),
        oaStatus: getMap(oaStatus),
        source: getMap(source),
        materialGroup: getMap(materialGroup),
        repairState: getMap(repairState),
        visitProgressType: getMap(visitProgressType),
        remoteProgressType: getMap(remoteProgressType),
        state: {
          PROCESSING: '处理中',
          PENDING: '审核中',
          APPROVING: '审批中',
          RETURNED: '已退回',
          SUCCESS: '已完成'
        }
      },
      type: {
        logistics: '',
        order: ''
      },
      form: {
        apply: {},
        certificates: {},
        decide: {},
        goods: {},
        refund: {},
        replace: {},
        invoice: {},
        detection: {},
        visit: {},
        repair: {},
        source: 'BOSS',
        state: '',
        serviceTicket: {},
        remote: {}
      },
      formInvoiceType: '',
      formRefundType: '',
      judgeData: {
        apply: {},
        certificates: [],
        decide: {},
        goods: {},
        refund: {},
        replace: {},
        invoice: {},
        detection: {},
        visit: {},
        remote: {},
        repair: {}
      },
      progress: [],
      firstState: '', // 服务进度一级state
      secondState: '', // 服务进度二级state
      userList: [],
      supplementSkuTableData: [], // 创建服务物料表格数据
      orderTableData: [], // 客户已下单表格数据
      isPro: /pro/.test(window.CUR_DATA.env),
      oaHost: /uat|local/.test(window.location.href)
        ? 'https://testoa.zkh360.com'
        : 'https://oa.zkh360.com'
    }
  },
  components: {
    Judge,
    OrderInfo,
    Preview,
    ServiceProgress,
    InstallProgress,
    RemoteSKU
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    }
    ),
    rules () {
      const cloneRules = cloneDeep(this.rule)
      cloneRules.logistics.content = [{ required: this.type.logistics === 'UPDATEREISSUE', message: '请输入', trigger: 'blur' }]
      return cloneRules
    },
    user() {
      return window.CUR_DATA.user
    },
    createOrderStatus () {
      let ret = false
      if (this.form.state !== 'PROCESSING') return false
      if (
        this.form[this.serviceType] &&
        /CREATING/.test(this.form[this.serviceType].state)
      ) {
        // 返厂、检查增加买家已退限制条件
        if (this.serviceType === 'repair' || this.serviceType === 'detection') {
          if (this.form.goods.state === 'RETURNED') {
            ret = true
          }
        } else {
          ret = true
        }
      }
      console.log('createOrderStatus', ret)
      return ret
    },
    ...mapState(['userRole']),
    isAdmin () {
      return !!~this.userRole.indexOf('boss-售后超管')
    },
    // 是否是客服
    isServices () {
      return (!!~this.userRole.indexOf('data-客服')) || (!!~this.userRole.indexOf('boss-客服'))
    },
    // 是否是运营
    isOperates () {
      return !!~this.userRole.indexOf('商品中心-售后运营')
    },
    // 是否是采购
    isBuyer () {
      if (this.userRole.find(item => ['PMS采购员', 'PMS采购员(非产品)', 'PMS采购经理', 'PMS采购经理(非产品)', 'PMS采购员(非产品采购)'].indexOf(item) > -1)) {
        return true;
      }
      return false;
    },
    serviceType () {
      if (this.form.repair.state) return 'repair'
      if (this.form.visit.state) return 'visit'
      if (this.form.remote.state) return 'remote'
      if (this.form.detection.state) return 'detection'
      if (this.form.serviceTicket?.reissue?.state) return 'reissue'
      return ''
    },
    serviceExpress () {
      return this.form.expressInfoMap[this.serviceType.toUpperCase()]
    },
    expressReturn () {
      const id =
        this.form.expressInfoMap &&
        this.form.expressInfoMap.GOODS &&
        this.form.expressInfoMap.GOODS.id
      return (
        this.form.certificates &&
        this.form.certificates.EXPRESS &&
        this.form.certificates.EXPRESS.filter(item => item.ownerId === id)
      )
    },
    expressInvoice () {
      const id =
        this.form.expressInfoMap &&
        this.form.expressInfoMap.INVOICE &&
        this.form.expressInfoMap.INVOICE.id
      return (
        this.form.certificates &&
        this.form.certificates.EXPRESS &&
        this.form.certificates.EXPRESS.filter(item => item.ownerId === id)
      )
    },
    expressOfServiceType () {
      const id = this.serviceExpress && this.serviceExpress.id
      return (
        this.form.certificates &&
        this.form.certificates.EXPRESS &&
        this.form.certificates.EXPRESS.filter(item => item.ownerId === id)
      )
    },
    createType () {
      return this.form.apply.createType
    },
    isCANCEL () {
      return this.createType === 'CANCEL'
    },
    isGOODS () {
      return this.createType === 'GOODS'
    },
    isREPLACE () {
      return this.createType === 'REPLACE'
    },
    isREFUND () {
      return this.createType === 'REFUND'
    },
    isINVOICE () {
      return this.createType === 'INVOICE'
    },
    isREPAIR () {
      return this.createType === 'REPAIR'
    },
    isVISIT () {
      return this.createType === 'VISIT'
    },
    isDETECTION () {
      return this.createType === 'DETECTION'
    },
    isSERVICE () {
      return this.createType === 'SERVICE'
    },
    // 是否是服务类-安装流程
    isINSTALL () {
      return this.form.serviceTicket && this.form.serviceTicket.newLogicTag === '1'
    },
    // 服务类引导区进度
    serviceState () {
      return this.form.serviceTicket && this.form.serviceTicket.examine && this.form.serviceTicket.examine.state
    },
    // 返厂服务进度
    repairState () {
      return this.form.repair && this.form.repair.state
    },
    // 上门服务进度
    visitState () {
      return this.form.visit && this.form.visit.state
    },
    // 远程指导进度
    remoteState () {
      return this.form.remote && this.form.remote.state
    },
    // 补发进度
    reissueState () {
      return this.form?.serviceTicket?.reissue?.state
    },
    // 服务需付费/服务无需付费按钮出现条件
    isNeedPay () {
      return this.form.serviceTicket && this.form.serviceTicket.needPay !== 0 && (this.form.serviceTicket.supplement === null || (this.form.serviceTicket.supplement && this.form.serviceTicket.supplement.state === 'CONFIRM'))
    },
    // 拒绝付费按钮显示条件
    isRefusePay () {
      return this.form.serviceTicket && this.form.serviceTicket.needPay === 1 && this.form.serviceTicket.supplement && this.form.serviceTicket.supplement.state === 'CONFIRM'
    },
    // 确认并下单按钮显示条件
    isComfirm () {
      return this.form.serviceTicket &&
        this.form.serviceTicket.serviceType !== 'TIE_IN_SALE' &&
        this.form.serviceTicket.needPay !== 0 &&
        this.form.serviceTicket.supplement &&
        (this.form.serviceTicket.supplement.sku !== null || this.form.supplementInfo.supplementOrderInfoList) &&
        !/SUCCESS|REFUSE/.test(this.form.serviceTicket.supplement.state) &&
        (!this.form.serviceTicket.supplement.omsNo && this.form.supplementInfo.supplementOrderInfoList.every(item => !item.omsNo))
    },
    // 客户已下单按钮出现条件
    isAddOrder () {
      return this.form.serviceTicket &&
      this.form.serviceTicket.needPay !== 0 &&
      this.form.serviceTicket.supplement &&
      /CONFIRM|ORDERS/.test(this.form.serviceTicket.supplement.state) &&
      ((this.form.serviceTicket.supplement.sku !== null && !this.form.serviceTicket.supplement.omsNo) || (this.form.supplementInfo.supplementOrderInfoList && this.form.supplementInfo.supplementOrderInfoList.some(item => !item.omsNo)))
    }
  },
  methods: {
    routeToWorkflow,
    handleLogisticsType (val) {
      if (val === 'VISIT' || val === 'OTHER') {
        this.rules.logistics.number = [{ required: false }]
        this.rules.logistics.company = [{ required: false }]
      } else {
        this.rules.logistics.number = [{ required: true, message: '请输入', trigger: 'blur' }]
        this.rules.logistics.company = [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    },
    handleExceed () {
      this.$message.error('最多上传5个附件！')
    },
    formatTime (dateString) {
      if (dateString) {
        const t = new Date(dateString)
        const date = dateString.split('T')[0]
        const time = t.toTimeString().split(' ')[0]
        return `${date} ${time}`
      }
      return dateString
    },
    handleServiceStatus (type, firstState, secondState) {
      this.show[type] = true
      let cloneData
      switch (type) {
        // 编辑，跳回到表单申请页
        case 'edit':
          this.$router.push({
            path: `/after-sales/form/${this.id}?type=update&tagName=编辑售后单`
          })
          break
        // 临时回复
        case 'tempReply':
          this.firstState = firstState
          this.secondState = secondState
          break
        // 创建服务工单
        case 'createRepair':
          routeToWorkflow(`/wf/create/${this.id}`)
          break
        // 更新服务进度 -- 返厂
        case 'serviceExpress':
          if (this.repairState === 'WORKING') {
            this.dialog.serviceExpress.state = 'RECEIVED'
          } else {
            this.dialog.serviceExpress.state = 'SENDOFF'
          }
          break
        // 更新服务进度 -- 上门
        case 'visit':
          if (this.visitState === 'WORKING') {
            this.dialog.visit.state = 'DATE'
          } else {
            this.dialog.visit.state = 'SUCCESS'
          }
          break
        // 更新服务进度 -- 远程指导
        case 'remote':
          if (this.remoteState === 'WORKING') {
            this.dialog.remote.state = 'DATE'
          } else {
            this.dialog.remote.state = 'SUCCESS'
          }
          break
        // 确认并下单
        case 'supplement':
          this.$confirm('请确认是否下单?', '提示', {
            type: 'warning'
          }).then(() => {
            this.toCreateOrderSale()
          })
          break
        // 服务无需付费
        case 'noPay':
          this.$confirm('确认此次售后服务无需付费?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiNoPay()
          })
          break
        // 填写、编辑物流信息
        case 'logistics':
        this.show.logistics = true
        this.type.logistics = firstState
        cloneData =
            JSON.parse(JSON.stringify(this.form.expressInfoMap.GOODS || '')) ||
            {}
        if (/REISSUE/.test(this.type.logistics)) {
          cloneData =
            JSON.parse(JSON.stringify(this.form.expressInfoMap?.REISSUE || '')) ||
            {}
          cloneData.type = cloneData.type || 'EXPRESS'
        }
        cloneData.certificates = this.form.certificates.EXPRESS?.filter(item => item.ownerId === this.form.expressInfoMap?.REISSUE?.id) || []
        this.dialog.logistics = cloneData
        break
        // 服务需收费
        case 'needPay':
        cloneData =
            JSON.parse(JSON.stringify(this.form.serviceTicket.supplement || '')) ||
            {}
          cloneData.certificates = this.form.certificates.QUOTATION || []
          this.dialog.needPay = Object.assign({
            supplementSku: cloneData.sku,
            certificates: cloneData.certificates,
            supplementExplain: cloneData.explain
          })
        break
      }
    },
    handleStatus (type) {
      this.show[type] = true
      let cloneData
      switch (type) {
        // 点击同意，弹出判定页
        case 'approve':
          this.judgeData.decide.needInvoice = false
          this.judgeData.decide.needRefund = false
          this.judgeData.invoice.invoiceTitle = this.form.order.customerName
          this.judgeData.invoice.unsolved = this.form.apply.money
          this.judgeData.goods.amount = this.form.apply.money
          this.judgeData.refund.amount = this.form.apply.money
          this.judgeData.refund.oldAmount = this.form.apply.money
          this.getBank()
          break
        // 编辑，跳回到表单申请页
        case 'edit':
          // TODO
          this.$router.push({
            path: `/after-sales/form/${this.id}?type=update&tagName=编辑售后单`
          })
          break
        // OA
        case 'submitOAReturn':
          this.$confirm('请确认是否提交退货审批?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiOAReturnStart()
          })
          break
        case 'finishOAReturn':
          this.$confirm('请确认退货审批是否已完成?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiOAReturnSuccess()
          })
          break
        case 'submitOAInvoice':
          this.$confirm('请确认是否提交退票审批?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiOAInvoiceStart()
          })
          break
        case 'finishOAInvoice':
          this.$confirm('请确认退票审批是否已完成?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiOAInvoiceSuccess()
          })
          break
        case 'submitOARefund':
          this.$confirm('请确认是否提交退款审批?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiOARefundStart()
          })
          break
        case 'finishOARefund':
          this.$confirm('请确认退款审批是否已完成?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiOARefundSuccess()
          })
          break
        case 'fillOAInvoice':
          this.show.editInvoice = true
          cloneData = JSON.parse(JSON.stringify(this.form.invoice || '')) || {}
          cloneData.certificates = this.form.certificates
            ? this.form.certificates.INVOICE
            : []
          this.dialog.invoice = Object.assign(cloneData, {
            invoiceTitle: this.form.order.customerName,
            unsolved: this.form.apply.money
          })
          this.formInvoiceType = 'fill'
          break
        case 'editInvoice':
          cloneData = JSON.parse(JSON.stringify(this.form.invoice || '')) || {}
          cloneData.certificates = this.form.certificates
            ? this.form.certificates.INVOICE
            : []
          this.dialog.invoice = cloneData
          this.formInvoiceType = 'edit'
          break
        case 'editReturn':
          this.dialog.return =
            JSON.parse(JSON.stringify(this.form.goods || '')) || {}
          this.dialog.return.certificates = this.form.certificates.GOODS || []
          break
        case 'fillOARefund':
          this.show.editRefund = true
          this.dialog.refund = Object.assign({}, JSON.parse(JSON.stringify(this.form.refund || '')), {
            amount: this.form.apply.money,
            oldAmount: this.form.apply.money
          })
          this.formRefundType = 'fill'
          break
        case 'editRefund':
          this.dialog.refund =
            JSON.parse(JSON.stringify(this.form.refund || '')) || {}
          this.formRefundType = 'edit'
          break
        case 'submitReturned':
          this.show.logistics = true
          cloneData =
            JSON.parse(JSON.stringify(this.form.expressInfoMap.GOODS || '')) ||
            {}
          cloneData.certificates = this.form.certificates.EXPRESS || []
          this.dialog.logistics = cloneData
          this.type.logistics = 'GOODS'
          // this.apiGoodsExpress()
          break
        case 'finishReturn':
          this.apiGoodsFinish()
          break
        case 'submitInvoiced':
          this.show.logistics = true
          cloneData =
            JSON.parse(
              JSON.stringify(this.form.expressInfoMap.INVOICE || '')
            ) || {}
          cloneData.certificates = []
          this.dialog.logistics = cloneData
          this.type.logistics = 'INVOICE'
          // this.apiInvoiceExpress()
          break
        case 'finishInvoice':
          this.apiInvoiceFinish()
          break
        case 'fillInvoice':
          this.show.fillInvoice = true
          break
        case 'finishRefund':
          this.$confirm('请确认是否完成退款?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiRefundFinish()
          })
          break
        case 'finishReplaced':
          this.apiReplaceFinish()
          break
        case 'createRepair':
          // 外跳
          routeToWorkflow(`/wf/create/${this.id}`)
          break
        case 'updateRepair':
          this.show.repair = true
          this.dialog.repair.state =
            this.form[this.serviceType].state === 'PENDING'
              ? 'WORKING'
              : this.form[this.serviceType].state
          break
        case 'finishRepair':
          this[`api${this.upcaseCamel(this.serviceType)}Finish`]()
          break
        case 'agreeToCancel':
          this.$confirm('请确认是否订单已取消?', '提示', {
            type: 'warning'
          }).then(() => {
            this.apiOrderCancel()
          })
          break
        case 'returnOrder':
          this.show.order = true
          this.dialog.order.orderNum = this.form.goods.orderNum || ''
          this.type.order = 'GOODS'
          break
        case 'replaceOrder':
          this.show.order = true
          this.dialog.order.orderNum = this.form.replace.orderNum || ''
          this.type.order = 'REPLACE'
          break
        default:
          break
      }
    },
    getInfo (id) {
      this.load.detail = true
      this.id = id
      api({
        url: `/ticket/${id}`,
        complete: res => {
          if (res.code === 200) {
            const info = res.data
            this.form = Object.assign(this.form, info)
            this.form.apply.customerContact = info.order.customerContact
            this.form.apply.customerPhone = info.order.customerPhone

            this.form.remote = info.serviceTicket.remote
            // initialize default data
            const isPENDING = info.state === 'PENDING'

            this.form.decide = isPENDING
              ? { needInvoice: false, needRefund: false, description: '', type: info.apply.createType }
              : this.form.decide || {}

            this.form.goods = isPENDING
              ? { aspect: '1', way: 'SELF', type: 'WAREHOUSE', receiveContact: info.apply.contact, receivePhone: info.apply.phone, receiveAddress: info.apply.address, reason: '', goodsContact: '', phone: '', address: '', amount: 0, sku: '' }
              : this.form.goods || {}

            this.form.refund = isPENDING
              ? { company: info.order.customerName, number: info.order.customerNo, bank: '', account: '' }
              : this.form.refund || {}

            this.form.replace = isPENDING
              ? { contact: info.apply.contact, phone: info.apply.phone, address: info.apply.address }
              : this.form.replace || {}

            this.form.repair = isPENDING
              ? { way: 'SELF', type: 'SUPPLIER', contact: info.apply.contact, phone: info.apply.phone, address: info.apply.address, customerContact: info.apply.contact, customerPhone: info.apply.phone, customerAddress: info.apply.address, returnContact: '', returnPhone: '', returnAddress: '' }
              : this.form.repair || {}

            this.form.invoice = isPENDING
              ? { way: 'SELF', needReInvoice: 0, receiveContact: info.apply.contact, receivePhone: info.apply.phone, receiveAddress: info.apply.address, contact: invoiceHandler.name, phone: invoiceHandler.phone, address: invoiceHandler.address }
              : this.form.invoice || {}

            this.form.visit = isPENDING
              ? { type: 'SELF', contact: info.apply.contact, phone: info.apply.phone, address: info.apply.address }
              : this.form.visit || {}

            this.form.remote = isPENDING
              ? { type: 'SELF', contact: info.apply.contact, phone: info.apply.phone, address: info.apply.address }
              : this.form.remote || {}

            this.form.reissue = isPENDING
              ? { contact: info.apply.contact, phone: info.apply.phone, address: info.apply.address }
              : this.form.reissue || {}

            this.form.detection = isPENDING
              ? { way: 'SELF', contact: info.apply.contact, phone: info.apply.phone, address: info.apply.address, customerContact: info.apply.contact, customerPhone: info.apply.phone, customerAddress: info.apply.address, returnContact: '', returnPhone: '', returnAddress: '' }
              : this.form.detection || {}

            const cloneData = JSON.parse(JSON.stringify(this.form))
            cloneData.certificates = isPENDING ? [] : cloneData.certificates
            this.judgeData = cloneData
            console.log(this.judgeData)
            this.flag.info = true
            // this.getOrderInfo(info.order)
          } else {
            this.flag.info = false
            this.$message.error('API：获取数据失败')
          }
          this.load.detail = false
        }
      })
    },
    getProgress () {
      api({
        url: '/ticket/service/getProgress',
        method: 'GET',
        query: { id: this.id },
        complete: data => {
          if (data.code === 200) {
            this.progress = data.data
          } else {
            this.$message.error(data.msg || data.message || '操作失败')
          }
        }
      })
    },
    submitApprove () {
      const id = this.$route.params.id

      this.$refs.judge.$refs.judge.validate(valid => {
        if (valid) {
          console.log(id, this.judgeData)
          const submitData = JSON.parse(JSON.stringify(this.judgeData))
          submitData.decide.oaSaleNickName = submitData.order.saleNickName
          submitData.decide.oaSaleWorkCode = submitData.order.saleWorkCode
          submitData.decide.oaStaffNickName = submitData.order.staffNickName
          submitData.decide.oaStaffWorkCode = submitData.order.staffWorkCode
          delete submitData.order
          delete submitData.apply
          api({
            url: `/ticket/${id}/decide/agree`,
            method: 'PUT',
            data: submitData,
            complete: data => {
              console.log('info', data)
              if (data.code !== 200) {
                this.$message.error(data.message || '请求失败')
              }
              if (data && data.code === 200) {
                this.show.approve = false
                this.getInfo(this.id)
                this.getProgress()
              }
            }
          })
        }
      })
    },
    changeLogisticsNumber (val) {
      if (/REISSUE/.test(this.form.decide.type)) {
        if (val) {
          api({
            url: `/standard/customer/getLogisticsCompanyName?number=${val}`,
            method: 'GET',
            complete: res => {
              console.log(res)
              if (res?.code === 200) {
                this.dialog.logistics = {
                  ...this.dialog.logistics,
                  code: res?.data?.comCode,
                  company: res?.data?.name
                }
              } else {
                this.$message.error(res?.msg || res?.message || '查询失败')
                this.dialog.logistics = {
                  ...this.dialog.logistics,
                  code: '',
                  company: ''
                }
              }
            }
          })
        } else {
          this.dialog.logistics = {
            ...this.dialog.logistics,
            code: '',
            company: ''
          }
        }
      }
    },
    submitLogistics () {
      if (this.dialog['logistics'].certificates.filter(item => item.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      this.$refs.formLogistics.validate(valid => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.dialog.logistics || ''))
          data = {
            certificates: data.certificates,
            expressInfo: data,
            source: 'BOSS'
          }
          switch (this.type.logistics) {
            case 'GOODS':
              this.apiGoodsExpress(data)
              break
            case 'INVOICE':
              this.apiInvoiceExpress(data)
              break
            case 'UPDATE':
              this.apiUpdateGoodsExpress(data)
              break
            case 'REISSUE':
              this.submitReissueLogistics()
              break
            case 'UPDATEREISSUE':
              this.submitReissueLogistics()
              break
            default:
              break
          }
          this.show.logistics = false
        }
      })
    },
    // 补发-填写/编辑物流信息
    submitReissueLogistics () {
      let data = JSON.parse(JSON.stringify(this.dialog.logistics || ''))
          data = {
            certificateInfos: data.certificates,
            expressInfo: data,
            id: this.id
          }
      api({
        url: this.type.logistics === 'REISSUE' ? '/ticket/service/reissue/confirmExpressInfo' : '/ticket/service/reissue/editExpressInfo',
        method: 'POST',
        data: data,
        complete: res => {
          console.log(res)
          if (res.code === 200) {
            this.$message.success('操作成功')
            this.getInfo(this.id)
            this.getProgress()
          } else {
            this.$message.error(res.msg || res.message || '操作失败')
          }
        }
      })
    },
    // 补发-确认服务完成
    apiReissueFinish () {
      api({
        url: `/ticket/service/reissue/finish?id=${this.id}`,
        method: 'POST',
        complete: res => {
          if (res.code === 200) {
            this.$message.success('操作成功')
            this.getInfo(this.id)
            this.getProgress()
          } else {
            this.$message.error(res.msg || res.message || '操作失败')
          }
        }
      })
    },
    submitOrderId () {
      this.$refs.formOrder.validate(valid => {
        if (valid) {
          const data = this.dialog.order
          switch (this.type.order) {
            case 'GOODS':
              this.apiGoodsOrder(data)
              break
            case 'REPLACE':
              this.apiReplaceOrder(data)
              break
            default:
              break
          }
          this.show.order = false
        }
      })
    },
    upcaseCamel (str) {
      if (str) {
        return str.slice(0, 1).toUpperCase() + str.slice(1)
      }
      return str
    },
    updateRepairStatus () {
      const apiName = this.upcaseCamel(this.serviceType)
      this[`api${apiName}State`](this.dialog.repair)
      this.show.repair = false
    },
    submitEditedReturn () {
      const data = this.dialog.return
      const certificates = this.dialog.return.certificates
      delete data.certificates
      if (
        !data.aspect ||
        !data.reason ||
        !data.oaGroupId ||
        !data.amount ||
        !data.sku
      ) {
        return this.$message.error('请填写必填项！')
      }
      this.apiOAReturn({ goods: data, certificates })
      this.show.editReturn = false
      this.dialog.return.certificates = []
    },
    submitEditedInvoice () {
      this.$refs.formDialogInvoice.validate(valid => {
        if (valid) {
          const cloneData = JSON.parse(JSON.stringify(this.dialog.invoice))
          if (this.formInvoiceType === 'edit') {
            this.apiOAInvoice({
              invoice: cloneData,
              certificates: cloneData.certificates,
              source: 'BOSS'
            })
          }
          if (this.formInvoiceType === 'fill') {
            this.apiOAFillInvoice({
              invoice: cloneData,
              certificates: cloneData.certificates,
              source: 'BOSS'
            })
          }
          this.show.editInvoice = false
        }
      })
    },
    submitEditedRefund () {
      this.$refs.formDialogRefund.validate(valid => {
        if (valid) {
          const data = this.dialog.refund
          if (this.formRefundType === 'edit') {
            this.apiOARefund({ refund: data })
          }
          if (this.formRefundType === 'fill') {
            this.apiOAFillRefund({ refund: data })
          }
          this.show.editRefund = false
        }
      })
    },
    submitEditedRefundAfter () {
      console.log('submitEditedRefundAfter')
      this.$refs.reInvoiceNum.validate(valid => {
        if (valid) {
          const data = {
            reInvoiceNum: this.form.invoice.reInvoiceNum,
            id: this.id
          }
          api({
            url: `/ticket/${this.id}/re/invoice/number`,
            data,
            method: 'PUT',
            complete: data => {
              this.show.fillInvoice = false
              if (data.code === 200) {
                this.$message.success('保存成功！')
              }
              this.getInfo(this.id)
              this.getProgress()
            }
          })
        }
      })
    },
    apiObsolete () {
      this.$refs.formObsolete.validate(valid => {
        if (valid) {
          this.update(
            '/obsolete',
            {
              obsolete: this.dialog.obsolete
            },
            res => {
              this.show.obsolete = false
            }
          )
        }
      })
    },
    apiReject () {
      this.$refs.formReject.validate(valid => {
        if (valid) {
          this.update('/decide/refuse', this.dialog.reject, res => {
            this.show.reject = false
          })
        }
      })
    },
    apiCancel () {
      // this.$confirm('请确认是否取消申请售后?', '提示', {
      //   type: 'warning'
      // }).then(() => {
      //   this.update(
      //     '/cancel',
      //     {
      //       source: 'BOSS'
      //     },
      //     res => {
      //       this.show.cancel = false
      //       if (res.code === 200) {
      //         this.$message.success('操作成功')
      //       }
      //     }
      //   )
      // })
      this.$refs.formCancel.validate(valid => {
        if (valid) {
          this.update('/cancel', { ...this.dialog.cancel, source: 'BOSS' }, res => {
            this.show.cancel = false
          })
        }
      })
    },
    apiDetectionFinish () {
      this.update('/detection/finish', null, res => {
        console.log('info', res)
        if (res.code === 200) {
          // eslint-disable-line
        }
      })
    },
    apiDetectionState (data) {
      this.update('/detection/state', data, res => {
        console.log('info', res)
        if (res.code === 200) {
          // eslint-disable-line
        }
      })
    },
    apiGoodsExpress (data) {
      this.update('/goods/express', data)
    },
    apiUpdateGoodsExpress (data) {
      this.update('/update/goods/express', data)
    },
    apiGoodsFinish () {
      this.update('/goods/finish')
    },
    apiGoodsOrder (data) {
      this.update('/goods/order/number', data)
    },
    apiInvoiceExpress (data) {
      this.update('/invoice/express', data)
    },
    apiInvoiceFinish () {
      this.update('/invoice/finish')
    },

    // OA
    apiOAReturn (data) {
      this.update('/oa/goods', data)
    },
    apiOAReturnStart () {
      this.update('/oa/goods/start')
    },
    apiOAReturnSuccess () {
      this.update('/oa/goods/success')
    },
    //
    apiOAFillInvoice (data) {
      this.update('/fill/oa/invoice', data)
    },
    apiOAInvoice (data) {
      this.update('/oa/invoice', data)
    },
    apiOAInvoiceStart () {
      this.update('/oa/invoice/start')
    },
    apiOAInvoiceSuccess () {
      this.update('/oa/invoice/success')
    },
    //
    apiOAFillRefund (data) {
      this.update('/fill/oa/refund', data)
    },
    apiOARefund (data) {
      this.update('/oa/refund', data)
    },
    apiOARefundStart () {
      this.update('/oa/refund/start')
    },
    apiOARefundSuccess () {
      this.update('/oa/refund/success')
    },
    //
    apiOrderCancel () {
      this.update('/order/cancel')
    },
    apiRefundFinish () {
      this.update('/refund/finish')
    },
    apiRepairFinish () {
      this.update('/repair/finish')
    },
    apiRepairState (data) {
      this.update('/repair/state', data)
    },
    apiReplaceFinish () {
      this.update('/replace/finish')
    },
    apiReplaceOrder (data) {
      this.update('/replace/order/number', data)
    },
    apiVisitState (data) {
      this.update('/visit/state', data)
    },
    apiVisitFinish () {
      this.update('/visit/finish')
    },
    apiRemoteState (data) {
      this.updateRemote('/remote/state', data)
    },
    apiRemoteFinish () {
      this.updateRemote('/remote/finish')
    },
    updateRemote(url, data, complete) {
      if (this.requesting) return
      this.requesting = true
      api({
        url: `/ticket/service${url}`,
        data: {
          id: this.id,
          ...data
        },
        method: 'POST',
        complete: data => {
          this.requesting = false
          if (data.code === 200) {
            this.$message.success(data.message || '请求成功')
          } else {
            this.$message.error(data.message || '请求失败')
          }
          complete && complete(data)
          this.getInfo(this.id)
          this.getProgress()
        }
      })
    },
    update (url, data, complete) {
      if (this.requesting) return
      this.requesting = true
      api({
        url: `/ticket/${this.id}${url}`,
        data,
        method: 'PUT',
        complete: data => {
          this.requesting = false
          if (data.code === 200) {
            this.$message.success(data.message || '请求成功')
          } else {
            this.$message.error(data.message || '请求失败')
          }
          complete && complete(data)
          this.getInfo(this.id)
          this.getProgress()
        }
      })
    },
    cancel () {
      this.update(`/ticket/${this.id}/cancel`, this.form)
    },
    getOrderInfo (order) {
      const { sku, sapNo, skuUrl, sapItemNo } = order
      bossApi({
        prefix: '/api-boss-opc',
        url: '/aftersale',
        query: {
          skuNo: sku,
          sapOrderNos: sapNo
        },
        complete: res => {
          if (res.code === 200) {
            const data = (res.data && res.data.length && res.data[0]) || {}
            const skuData = (data.afterSaleSoItemBOList || []).find(
              item => item.sapItemNo === sapItemNo
            )
            // 需要增加描述字段
            data.materialDesc = order.material
            data.materialGroup = order.materialGroup
            data.sku = sku
            data.pic =
              skuUrl ||
              '//static.zkh360.com/all/image/2020-10-10/qs_product.d711611cb741d1d7c6d4a22cd2f943b4-2b9821.jpg'
            this.info.order = Object.assign(data, skuData)
          }
        }
      })
    },
    afterUpload (res, file, fileList, type) {
      const map = {
        return: 'GOODS'
      }
      const lists = fileList.map(item => {
        const link = item.response
          ? item.response && item.response.data && item.response.data.link
          : item.url
        return {
          name: item.name,
          url: link,
          type: map[type]
        }
      })
      this.dialog[type].certificates = lists
    },
    async serviceAfterUpload (file, type) {
      file.onError = () => {
        const lists = this.dialog[type].certificates
        const idx = lists.findIndex(item => item.uid === file.uid)
        this.dialog[type].certificates.splice(idx, 1)
      }
      const map = {
        return: 'GOODS',
        serviceExpress: 'GOODS',
        visit: 'RECEIPT',
        invoice: 'INVOICE',
        tempReply: 'INTERIM_COMMENT',
        logistics: 'EXPRESS',
        needPay: 'QUOTATION',
        remote: 'REMOTE'
      }
      this.dialog[type].certificates.push({
        uid: file.file.uid,
        name: file.file.name,
        status: 'ready',
        type: map[type]
      })
      const response = await upload('aftersales/certificates/detail', file.file);
      if (response) {
        this.dialog[type].certificates.filter(item => {
          if (item.uid === file.file.uid) {
            item.url = response?.url
          }
        })
      } else {
        remove(this.dialog[type].certificates, function (item) {
          return item.uid === file.file.uid
        })
        // this.$message.error('上传失败')
      }
    },
    afterRemove (file, fileList, type) {
      this.dialog[type].certificates = fileList
    },
    serviceBeforeRemove (file, fileList, type) {
      const lists = this.dialog[type].certificates
      const idx = lists.findIndex(item => item.uid === file.uid)
      this.dialog[type].certificates.splice(idx, 1)
      return false
    },
    serviceAfterRemove (file, fileList, type) {
      remove(this.dialog[type].certificates, function (item) {
        return item.uid === file.uid
      });
    },
    afterExceed () {
      this.$message.error('超出文件数量限制')
    },
    afterChangeDate (date) {
      console.log(date)
    },
    getMaterialGroup () {
      api({
        url: '/ticket/material/group',
        method: 'PUT',
        complete: res => {
          console.log(res)
          if (res.code === 200) {
            this.option.materialGroup = res.data.map(item => {
              return {
                label: item.productGroupName,
                value: item.productGroupId
              }
            })
            this.map.materialGroup = getMap(this.option.materialGroup)
          }
        }
      })
    },
    getBank () {
      api({
        url: '/ticket/bank',
        query: {
          customerNo: this.form.order.customerNo
        },
        complete: res => {
          console.log(res)
          if (res.code === 200) {
            const data = res.data
            console.log(this.judgeData, data)
            this.judgeData.refund.account = data.account
            this.judgeData.refund.bank = data.bank
          }
        }
      })
    },
    selectMaterialGroup (id) {
      const item = this.map.materialGroup[id]
      this.dialog.return.oaGroupName = item
    },
    getProgressId(firstState, secondState) {
      return this.progress.filter(item => item.state === firstState && item.secondState === secondState)[0]?.id
    },
    submitTempReply (firstState, secondState) {
      if (this.dialog['tempReply'].certificates.filter(item => item.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      this.$refs.formDialogTempReply.validate(valid => {
        if (valid) {
          const data = {
            certificateInfos: this.dialog.tempReply.certificates,
            context: this.dialog.tempReply.context,
            progressId: this.getProgressId(firstState, secondState),
            // progressId: this.form.serviceTicket.id || 0,
            ticketId: this.id
          }
          api({
            url: '/ticket/service/interimReply',
            method: 'POST',
            data: data,
            complete: data => {
              if (data && data.code === 200) {
                this.$message.success(data.message || '请求成功')
                this.show.tempReply = false
                this.dialog.tempReply = {
                  context: '',
                  certificates: []
                }
                this.getInfo(this.id)
                this.getProgress()
              } else {
                this.$message.error(data.message || '请求失败')
              }
            }
          })
        }
      })
    },
    changeJudgeServiceType(val) {
      if (val === 'REISSUE') {
        this.dialog.judgeServiceType.serverSupplier = 'SUPPLIER'
      } else {
        this.dialog.judgeServiceType.serverSupplier = ''
      }
    },
    submitJudgeServiceType() {
      this.$refs.judgeServiceType.validate(valid => {
        if (valid) {
          const submitData = JSON.parse(JSON.stringify(this.judgeData))
          submitData.decide.type = this.dialog.judgeServiceType.afterSaleType
          submitData.repair.returnContact = this.dialog.judgeServiceType.receivePerson
          submitData.repair.returnPhone = this.dialog.judgeServiceType.telephone
          submitData.repair.returnAddress = this.dialog.judgeServiceType.address
          submitData[`${this.dialog.judgeServiceType.afterSaleType.toLowerCase()}`].type = this.dialog.judgeServiceType.serverSupplier
          // if (this.dialog.judgeServiceType.afterSaleType === 'VISIT') {
          //   submitData.visit.type = this.dialog.judgeServiceType.serverSupplier
          // } else {
          //   submitData.repair.type = this.dialog.judgeServiceType.serverSupplier
          // }
          api({
            url: '/ticket/service/decide/agree',
            method: 'PUT',
            query: { id: this.id },
            data: submitData,
            complete: data => {
              if (data.code !== 200) {
                this.$message.error(data.message || '请求失败')
              }
              if (data && data.code === 200) {
                this.show.judgeServiceType = false
                this.getInfo(this.id)
                this.getProgress()
              }
            }
          })
        }
      })
    },
    submitServiceExpress() {
      if (this.dialog['serviceExpress'].certificates.filter(item => item.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      this.$refs.serviceExpress.validate(valid => {
        if (valid) {
          const { state, repairFinishTime, number, company, certificates, repairExplain } = this.dialog.serviceExpress
          const data = {
            state
          }
          if (state === 'RECEIVED') {
            data.repairFinishTime = repairFinishTime
            data.repairExplain = repairExplain
          }
          if (state === 'SENDOFF') {
            data.expressInfo = {
              number,
              company,
              ticketType: 'REPAIR'
            }
            data.certificateInfos = certificates
            data.repairFinishExplain = repairExplain
          }
            this.apiRepairState(data)
            this.dialog.serviceExpress.repairExplain = ''
            this.show.serviceExpress = false
        }
      })
    },
    submitVisit() {
      if (this.dialog['visit'].certificates.filter(item => item.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      this.$refs.visit.validate(valid => {
        if (valid) {
          const { state, visitTime, visitServicePerson, visitServicePhone, certificates, visitExplain } = this.dialog.visit
          const data = {
            state
          }
          if (state === 'DATE') {
            data.visitTime = visitTime
            data.visitServicePerson = visitServicePerson
            data.visitServicePhone = visitServicePhone
            data.visitExplain = visitExplain
          }
          if (state === 'SUCCESS') {
            data.certificateInfos = certificates
            data.visitFinishExplain = visitExplain
            data.expressInfo = {
              ticketType: 'VISIT'
            }
          }
          this.apiVisitState(data)
          this.dialog.visit.visitExplain = ''
          this.show.visit = false
        }
      })
    },
    submitRemote() {
      if (this.dialog['remote'].certificates.filter(item => item.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      this.$refs.remote.validate(valid => {
        if (valid) {
          const { state, appointmentTime, servicePerson, servicePhone, certificates, appointmentNote, finishNote } = this.dialog.remote
          let data = {}
          if (state === 'DATE') {
            data = {
              state,
              appointmentTime,
              servicePerson,
              servicePhone,
              certificateInfos: certificates,
              appointmentNote
            }
          }
          if (state === 'SUCCESS') {
            data = {
              state,
              finishNote
            }
          }
          this.apiRemoteState(data)
          this.show.remote = false
        }
      })
    },
    // 确认申请信息
    apiConfirm () {
      api({
        url: '/ticket/service/confirm',
        method: 'PUT',
        query: { ticketId: Number(this.id) },
        complete: data => {
          if (data.code === 200) {
            this.$message.success('确认成功！')
            this.show.confirmApplyInfo = false
          } else {
            this.$message.error(data.msg || data.message || '确认失败')
          }
          this.getInfo(this.id)
          this.getProgress()
        }
      })
    },
    // 运营驳回
    apiRefuse() {
      this.$refs.formRefuse.validate(valid => {
        if (valid) {
          const data = {
            id: Number(this.id),
            rejectReason: this.dialog.refuse.reason
          }
          api({
            url: '/ticket/service/decide/reject',
            method: 'PUT',
            query: data,
            complete: data => {
              if (data.code === 200) {
                this.$message.success('驳回成功！')
                this.show.refuse = false
              } else {
                this.$message.error(data.msg || data.message || '确认失败')
              }
              this.getInfo(this.id)
              this.getProgress()
            }
          })
        }
      })
    },
    // 查找任务交接人
    queryUser (val) {
      getUser({ nickname: val }).then(res => {
        this.userList = res.content.map(item => {
          return {
            id: item.id,
            nickname: item.nickname,
            departmentName: item.departmentName,
            owerWorkCode: item.owerWorkCode
          }
        })
      })
    },
    // 任务交接
    apiHandoff () {
      this.$refs.formHandoff.validate(valid => {
        if (valid) {
          const data = {
            ticketId: Number(this.id),
            operateAgentId: this.dialog.handoff.person.id,
            operateAgentName: this.dialog.handoff.person.nickname,
            operateAgentWorkCode: this.dialog.handoff.person.owerWorkCode,
            comment: this.dialog.handoff.comment,
            handoverType: 1
          }
          api({
            url: '/ticket/service/handover',
            method: 'POST',
            data: data,
            complete: data => {
              if (data.code === 200) {
                this.$message.success('操作成功！')
                this.show.handoff = false
              } else {
                this.$message.error(data.msg || data.message || '操作失败')
              }
              this.getInfo(this.id)
              this.getProgress()
            }
          })
        }
      })
    },
    // 更新服务进度时，清空弹窗的值
    changeServiceExpress (val) {
      this.dialog.serviceExpress = { state: val, certificates: [] }
    },
    changeVisit (val) {
      this.dialog.visit = { state: val, certificates: [] }
    },
    changeRemote (val) {
      this.dialog.remote = { state: val, certificates: [] }
    },
    // 服务需付费提交
    submitNeedPay () {
      if (this.dialog['needPay'].certificates.filter(item => item.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      if (!this.supplementSkuTableData.length) {
        return this.$message.error('请添加维修服务物料！')
      }
      const data = {
        ticketId: this.id,
        // supplementSku: this.dialog.needPay.supplementSku,
        supplementSkuList: this.supplementSkuTableData,
        supplementExplain: this.dialog.needPay.supplementExplain,
        certificateInfos: this.dialog.needPay.certificates
      }
      api({
        url: '/ticket/service/supplement',
        method: 'PUT',
        query: { id: this.id },
        data: data,
        complete: data => {
          if (data.code === 200) {
            this.$message.success('操作成功！')
            this.show.needPay = false
          } else {
            this.$message.error(data.msg || data.message || '操作失败')
          }
          this.getInfo(this.id)
          this.getProgress()
        }
      })
    },
    // 服务无需付费
    apiNoPay () {
      api({
        url: '/ticket/service/noPay',
        method: 'PUT',
        query: { id: this.id },
        complete: data => {
          if (data.code === 200) {
            this.$message.success('操作成功！')
          } else {
            this.$message.error(data.msg || data.message || '操作失败')
          }
          this.getInfo(this.id)
          this.getProgress()
        }
      })
    },
    // 返厂&上门 确认服务完成
    submitServiceComplete () {
      this[`api${this.upcaseCamel(this.serviceType)}Finish`]()
      this.show.confirmServiceComplete = false
    },
    // 拒绝付费
    submitRejectPay () {
      api({
        url: '/ticket/service/supplement/refuse',
        method: 'PUT',
        query: { id: this.id },
        complete: data => {
          if (data.code === 200) {
            this.$message.success('操作成功！')
            this.show.rejectPay = false
          } else {
            this.$message.error(data.msg || data.message || '操作失败')
          }
          this.getInfo(this.id)
          this.getProgress()
        }
      })
    },
    // 确认并下单 跳转创建订单页面
    async toCreateOrderSale () {
      const res = await orderDetail({ omsNo: this.form.orderDetails[0]?.omsNo })
      this.$router.push({
        path: `/orderSale/formal/create/${this.form.orderDetails[0]?.salesOrganization}/Z001`,
        query: {
          customerNo: this.form.order?.customerNo, // 客户编码
          salesOrganization: this.form.orderDetails[0]?.salesOrganization, // 销售范围
          distributionChannel: this.form.orderDetails[0]?.distributionChannel, // 分销渠道
          productGroup: this.form.orderDetails[0]?.productGroup, // 产品组
          receiverContact: res.data?.receiverContact, // 收货联系人
          receivingInvoiceContact: res.data?.receivingInvoiceContact, // 收票联系人
          orderContact: res.data?.orderContact, // 订单联系人
          skuInfoList: JSON.stringify(this.form?.supplementInfo?.supplementOrderInfoList.map(item => {
            return { skuNo: item.skuNo, quantity: item.quantity }
          })), // sku编码可能有多个？
          quantity: '1',
          directSupplierType: this.form[this.serviceType]?.type === 'SELF' ? 0 : 1, // 直发类型
          position: '1004',
          omsNo: this.form.ticketNo, // 外围系统订单号
          orderSource: 'SH'
        }
      })
    },
    addOrder() {
      this.load.addOrderLoading = true;
      const param = {
        voucherNos: this.dialog.addSO.omsNo.split(/\s|,|;|，|；/).filter(s => s && s.trim()),
        current: 1,
        pageSize: 20
      }
      orderList(param).then(res => {
        if (res.code === 200 && res.data) {
          const validItem = res.data.find(item => item.soNo === this.dialog.addSO.omsNo);
          if (validItem) {
            this.orderTableData.push({ ...this.dialog.addSO, orderType: validItem.type, uuid: shortid.generate() })
          } else {
            this.$message.error('销售订单号无效，请检查！')
          }
        } else {
          this.$message.error('销售订单查询失败！')
        }
        this.load.addOrderLoading = false;
      }).catch(e => {
        this.load.addOrderLoading = false;
      })
    },
    deleteOrder(row) {
      this.orderTableData = this.orderTableData.filter(item => item.uuid !== row.uuid)
    },
    submitAddSO() {
      if (this.orderTableData.length) {
        this.load.submitOrderLoading = true;
        const data = {
          ticketId: this.form.id,
          omsOrderList: this.orderTableData.map(item => {
            return {
              omsNo: item.omsNo,
              orderType: item.orderType
            }
          })
        }
        delete data.supply;
        batchAddOrder(data).then(res => {
          if (res.code === 200) {
            this.$message.success('提交成功')
            this.show.addSO = false;
            this.getInfo(this.id)
            this.getProgress()
          } else {
            this.$message.error(res.message || '提交失败')
          }
        })
        .catch(err => console.log(err))
        .finally(() => {
          this.load.submitOrderLoading = false;
        })
      } else {
        this.$message.warning('请添加销售订单！')
      }
    },
    toWorkflow () {
      window.open(`https://${this.isPro ? 'boss' : 'boss-uat'}.zkh360.com/wf/detail/${this.form?.serviceTicket?.workOrderNo}`)
    },
    openNeedPayDialog () {
      try {
        if (this.form.supplementInfo && this.form.supplementInfo.supplementOrderInfoList) {
          this.supplementSkuTableData = this.form.supplementInfo.supplementOrderInfoList.map(item => {
            return {
              supplementSku: item.skuNo,
              supplementSkuDesc: item.skuDesc,
              supplementSkuQuantity: item.quantity,
              uuid: item.id
            }
          })
        } else {
          this.supplementSkuTableData = []
        }
        // 组合销售场景 自动关联上游外围订单下所有组合商品的服务的订单行物料
        if (this.form.serviceTicket?.serviceType === 'TIE_IN_SALE' && this.form.orderDetails?.length > 0) {
          const arr = this.form.orderDetails
            .filter(item => item.relateType === '2' && this.supplementSkuTableData.findIndex(sku => sku.supplementSku === item.sku) === -1)
            .reduce((acc, cur) => {
              const foundSku = acc.find(item => item.supplementSku === cur.sku)
              if (foundSku) {
                foundSku.supplementSkuQuantity += cur.applyQuantity
              } else {
                acc.push({
                  supplementSku: cur.sku,
                  supplementSkuDesc: cur.material,
                  supplementSkuQuantity: cur.applyQuantity,
                  uuid: cur.id
                })
              }
              return acc;
            }, [])
            if (arr.length > 0) {
              this.supplementSkuTableData.push(...arr)
            }
        }
      } catch (err) {
        console.log(err)
      }
    },
    openAddSODialog () {
      if (this.form.serviceTicket?.serviceType === 'TIE_IN_SALE' && this.form.orderDetails?.length > 0) {
        const filteredData = this.form.orderDetails?.filter(item => item.relateType === '2') || [];
        this.orderTableData = uniqBy(filteredData, 'omsNo')
          ?.map(item => ({
            omsNo: item.omsNo,
            uuid: shortid.generate()
        })) || []
      }
    },
    changeSupplementSku(val, materialDescription) {
      this.dialog.needPay.sku = { supplementSku: val, supplementSkuDesc: materialDescription }
    },
    addSupplementSku() {
      if (this.dialog.needPay.sku) {
        if (this.supplementSkuTableData.some(item => item.supplementSku === this.dialog.needPay.sku.supplementSku)) {
          this.$message.warning('该sku已存在，请勿重复添加！')
          return;
        }
        this.supplementSkuTableData.push({ ...this.dialog.needPay.sku, supplementSkuQuantity: 1, uuid: shortid.generate() })
      }
    },
    deleteSupplementSku(row) {
      this.supplementSkuTableData = this.supplementSkuTableData.filter(item => item.uuid !== row.uuid)
    }
  },
  mounted () {
    this.getInfo(this.id)
    this.getProgress()
    this.getMaterialGroup()
  },
  beforeRouteUpdate (to, from, next) {
    next()
    this.getInfo(this.$route.params.id)
    this.getProgress()
  }
}
