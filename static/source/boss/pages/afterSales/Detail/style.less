.page-aftersales-wrapper {
  height: calc(100vh - 108px);
  display: flex;
  flex-direction: column;
}
.page-aftersales-detail {
  padding: 30px;
  overflow: scroll;
  min-height: 0%;
  .form-column {
    .el-form-item__label {
      width: 100px;
    }
    .el-form-item {
      width: 100%;
    }
  }
  .block-title{
    background-color: #d7f1f9;
    padding: 12px;
    &.title-with-btn {
      display: flex;
      justify-content: space-between;
    }
  }
  .show-explain{
    width: 100%;
    word-break: break-all;
  }
}
.guide {
  padding: 30px 30px 0 30px;
  width: 100%;
}
.module-guide{
  border: 1px solid #ddd;
  background-color: #eee;
  padding: 20px;
  // border-radius: 5px;
  .banner {
    font-size: 18px;
    font-weight: 800;
    line-height: 2.4;
  }
}
.module-info-customer{
  margin: 20px;
  display: flex;
  font-size: 16px;
  p{
    margin-top:10px;
    margin-right: 40px;
  }
  label{
    margin-right: 20px;
  }
}
.module-aftersale-info{
  border: 1px solid #ddd;
  padding: 20px;
  margin-bottom: 20px;
  .flex-box{
    display: flex;
    flex-flow: wrap;
  }
  p{
    width: 33.33333333%;
    margin: 10px 0;
  }
}
.certificates{
  width: 100%;
  .preview{
    display: flex;
  }
}
.module-progress-content{
  border: 1px solid #ddd;
  padding: 16px;
}
.module-progress-card{
  display: flex;
  margin-bottom: 10px;
  flex-wrap: wrap;
  justify-content: flex-end;
  h4 {
    width: 100px;
  }
  .progress-content{
    flex: 1;
  }
  .progress-content-line {
    width: calc(100% - 100px);
  }
  .operation{
    font-size: 12px;
    color: #999;
    span{
      margin: 0 20px 0 0;
    }
  }
  .card{
    border: 1px solid #ddd;
    box-shadow: 0 0 10px rgba(20,20,20,.3);
    margin: 10px 0;
    padding: 10px;
    .head{
      padding: 10px;
      display: flex;
      justify-content: space-between;
      &.border{
        border-bottom: 3px solid #ddd;
        margin-bottom: 10px;
      }
    }
    .body {
      padding: 0 10px;
      p {
        margin: 5px 0;
      }
    }
    .title{
      font-size: 18px;
      .status-APPROVING,
      .status-PENDING,
      .status-RECEIVED,
      .status-DELIVERING,
      .status-CREATING,
      .status-RECEIVED,
      .status-DATE,
      .status-WORKING,
      .status-PROCESSING{
        color: #F50;
      }
      .status-SENDOFF,
      .status-DELIVERED,
      .status-RETURNED{
        color:#FFC107;
      }
      .status-REFUSE,
      .status-SUCCESS {
        color: #4CAF50;
      }
      .status-SUCCESS-invoice {
        color: #FFC107;
      }
      .status-REINVOICE {
        color: #4CAF50;
      }
      .status-COMFIRM {
        color: #597bee;
      }
      a {
        color: #597bee;
        &:hover{
          color: lighten(#597bee, 10%);
        }
      }
      span {
        margin: 0 16px 0 0;
      }
    }
    .express-info{
      flex-flow: wrap;
      display: flex;
      p{
        width: 50%;
      }
    }
  }
}
.el-dialog.dialog-aftersale{
  .el-form-item__label {
    width: 160px;
  }
  .el-form-item{
    width: 48%;
  }
}
.dialog-aftersale{
  .el-dialog__header{
    padding: 16px;
    background-color: #597bee;
  }
  .el-dialog__title{
    color: #fff;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: #fff;
  }
  .el-upload-list__item {
    transition: none;
  }
  .el-upload--picture-card,
  .el-upload-list--picture-card .el-upload-list__item {
    width: 100px;
    height: 100px;
    line-height: 100px;
  }
  .el-upload-list__item-name{
    width: 200px;
  }
  .long-input{
    input{
      width: 560px;
    }
    textarea{
      width: 560px;
    }
  }
  .inner-text {
    display: flex;
    justify-content: center;
  }
  .service-express-item {
    width: 220px;
  }
}
.modals{
  .el-form-item__label {
    width: 120px;
  }
  input.el-input__inner {
    width: 200px;
  }
}
.pre-line {
  white-space: pre-line;
}