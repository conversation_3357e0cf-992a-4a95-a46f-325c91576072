<template>
<div class="page-aftersales-wrapper">
  <div class="guide">
    <!-- 服务类引导区 -->
    <div class="module-guide" v-if="flag.info && form.apply.createType === 'SERVICE'">
      <InstallProgress v-if="isINSTALL" :form="form" :progress="progress" :show="show" @handleInstallStatus="handleServiceStatus" @getInfo="getInfo" @getProgress="getProgress" />
      <div v-if="!isINSTALL">
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PENDING' && serviceState === 'CUSTOMER_CONFIRMATION'">
          {{`当前售后单“待客服确认”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <!-- <el-button type="primary" @click="handleServiceStatus('confirmApplyInfo')">确认申请信息</el-button> -->
          <el-button type="primary" @click="handleServiceStatus('edit')">编辑申请信息</el-button>
          <el-button type="primary" @click="handleServiceStatus('cancel')">取消售后申请</el-button>
        </div>
        <!-- 确认信息准确 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PENDING' && serviceState === 'OPERATION_AUDIT'">
          {{`当前售后单“待运营审核”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('cancel')">取消售后申请</el-button>
          <el-button type="primary" @click="handleServiceStatus('edit')">编辑申请信息</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AUDIT')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PENDING' && serviceState === 'OPERATION_AUDIT'">
          {{`当前售后单“待运营审核”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('judgeServiceType')">判定服务类型</el-button>
          <el-button type="primary" @click="handleServiceStatus('refuse')">驳回</el-button>
          <el-button type="primary" @click="handleServiceStatus('handoff')" v-if="!form.serviceTicket.operateAgentId">任务交接</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AUDIT')">临时回复</el-button>
        </div>
        <!-- 驳回 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PENDING' && serviceState === 'OPERATION_REJECT'">
          {{`当前售后单“运营驳回待客服确认”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('reject')">拒绝售后申请</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_REJECT')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PENDING' && serviceState === 'OPERATION_REJECT'">
          {{`当前售后单“运营驳回待客服确认”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_REJECT')">临时回复</el-button>
        </div>

        <!-- 返厂服务 start -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && form.goods && form.goods.state === 'PENDING'">
          {{`当前售后单“待买家退货”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('logistics', 'GOODS')">填写物流信息</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AGREE')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && form.goods && form.goods.state === 'PENDING'">
          {{`当前售后单“待买家退货”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AGREE')">临时回复</el-button>
        </div>
        <!-- 填写物流信息 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && repairState === 'WORKING' && form.goods && form.goods.state === 'RETURNED'">
          {{ `当前售后单“待厂家收货”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'GOODS', 'GOODS_RETURNED')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('logistics', 'UPDATE')">编辑物流信息</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && repairState === 'WORKING' && form.goods && form.goods.state === 'RETURNED'">
          {{`当前售后单“待厂家收货”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('createRepair')" v-if="form.repair && !form.repair.workId">创建服务工单</el-button>
          <el-button type="primary" @click="handleServiceStatus('serviceExpress')">更新服务进度</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'GOODS', 'GOODS_RETURNED')">临时回复</el-button>
        </div>
        <!-- 更新服务进度--厂家已收货 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && /RECEIVED|QUOTED/.test(repairState)">
          {{`当前售后单“厂家已收货”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REPAIR', 'REPAIR_RECEIVED')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('supplement')" v-if="isComfirm">确认并下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('addSO')" v-if="isAddOrder">客户已下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('rejectPay')" v-if="isRefusePay">拒绝付费</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && /RECEIVED|QUOTED/.test(repairState)">
          {{`当前售后单“厂家已收货”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('serviceExpress')">更新服务进度</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REPAIR', 'REPAIR_RECEIVED')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('needPay')" v-if="isNeedPay">服务需付费</el-button>
          <el-button type="primary" @click="handleServiceStatus('noPay')" v-if="isNeedPay">服务无需付费</el-button>
        </div>
        <!-- 更新服务进度--厂家已寄出 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && repairState === 'SENDOFF'">
          {{ `当前售后单“厂家已寄回”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('confirmServiceComplete')">确认服务完成</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REPAIR', 'REPAIR_SENDOFF')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('supplement')" v-if="isComfirm">确认并下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('addSO')" v-if="isAddOrder">客户已下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('rejectPay')" v-if="isRefusePay">拒绝付费</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && repairState === 'SENDOFF'">
          {{`当前售后单“厂家已寄回”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REPAIR', 'REPAIR_SENDOFF')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('needPay')" v-if="isNeedPay">服务需付费</el-button>
          <el-button type="primary" @click="handleServiceStatus('noPay')" v-if="isNeedPay">服务无需付费</el-button>
        </div>
        <!-- 返厂 end -->

        <!-- 上门 start -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && visitState === 'WORKING'">
          {{`当前售后单“待预约上门时间”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AGREE')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && visitState === 'WORKING'">
          {{`当前售后单“待预约上门时间”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('createRepair')" v-if="form.visit && !form.visit.workId">创建服务工单</el-button>
          <el-button type="primary" @click="handleServiceStatus('visit')">更新服务进度</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AGREE')">临时回复</el-button>
        </div>
        <!-- 更新服务进度-已预约 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && visitState === 'DATE'">
          {{`当前售后单“已预约待上门”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'VISIT', 'VISIT_DATE')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('supplement')" v-if="isComfirm">确认并下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('addSO')" v-if="isAddOrder">客户已下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('rejectPay')" v-if="isRefusePay">拒绝付费</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && visitState === 'DATE'">
          {{`当前售后单“已预约待上门”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('visit')">更新服务进度</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'VISIT', 'VISIT_DATE')">临时回复</el-button>
        </div>
        <!-- 更新服务进度-服务已完成 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && visitState === 'SUCCESS'">
          {{`当前售后单“上门完成”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('confirmServiceComplete')">确认服务完成</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'VISIT', 'VISIT_SUCCESS')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('supplement')" v-if="isComfirm">确认并下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('addSO')" v-if="isAddOrder">客户已下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('rejectPay')" v-if="isRefusePay">拒绝付费</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && visitState === 'SUCCESS'">
          {{`当前售后单“上门完成”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'VISIT', 'VISIT_SUCCESS')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('needPay')" v-if="isNeedPay">服务需付费</el-button>
          <el-button type="primary" @click="handleServiceStatus('noPay')" v-if="isNeedPay">服务无需付费</el-button>
        </div>
        <!-- 上门 end -->

        <!-- 远程指导 start -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && remoteState === 'WORKING'">
          {{`当前售后单“待预约远程指导”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AGREE')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && remoteState === 'WORKING'">
          {{`当前售后单“待预约远程指导”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('createRepair')" v-if="form.remote && !form.remote.workId">创建服务工单</el-button>
          <el-button type="primary" @click="handleServiceStatus('remote')">更新服务进度</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'EXAMINE', 'EXAMINE_OPERATION_AGREE')">临时回复</el-button>
        </div>
        <!-- 更新服务进度-已预约 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && remoteState === 'DATE'">
          {{`当前售后单“已预约待远程指导”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REMOTE', 'REMOTE_DATE')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && remoteState === 'DATE'">
          {{`当前售后单“已预约待远程指导”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('remote')">更新服务进度</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REMOTE', 'REMOTE_DATE')">临时回复</el-button>
        </div>
        <!-- 更新服务进度-服务已完成 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && remoteState === 'SUCCESS'">
          {{`当前售后单“远程指导完成”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('confirmServiceComplete')">确认服务完成</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REMOTE', 'REMOTE_SUCCESS')">临时回复</el-button>
          <!-- <el-button type="primary" @click="handleServiceStatus('supplement')" v-if="isComfirm">确认并下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('addSO')" v-if="isAddOrder">客户已下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('rejectPay')" v-if="isRefusePay">拒绝付费</el-button> -->
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && remoteState === 'SUCCESS'">
          {{`当前售后单“远程指导完成”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REMOTE', 'REMOTE_SUCCESS')">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('needPay')" v-if="isNeedPay">服务需付费</el-button>
          <el-button type="primary" @click="handleServiceStatus('noPay')" v-if="isNeedPay">服务无需付费</el-button>
        </div>
        <!-- 上门 end -->

        <!-- 补发 start -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && reissueState === 'PENDING'">
          {{`当前售后单“待供应商发货”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REISSUE', 'REISSUE_PENDING')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && reissueState === 'PENDING'">
          {{`当前售后单“待供应商发货”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('logistics', 'REISSUE')">填写物流信息</el-button>
          <el-button type="primary" @click="handleServiceStatus('createRepair')" v-if="form.serviceTicket && form.serviceTicket.reissue && !form.serviceTicket.reissue.workId">创建服务工单</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REISSUE', 'REISSUE_PENDING')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && reissueState === 'SENT'">
          {{`当前售后单“待客户收货”，${form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('confirmServiceComplete')">确认服务完成</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REISSUE', 'REISSUE_SENT')">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && reissueState === 'SENT'">
          {{`当前售后单“待客户收货”，${form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''}你可以`}}
          <el-button type="primary" @click="handleServiceStatus('logistics', 'UPDATEREISSUE')">编辑物流信息</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', 'REISSUE', 'REISSUE_SENT')">临时回复</el-button>
        </div>
        <!-- 补发 end -->

        <!-- 确认服务完成--返厂＋上门 -->
        <div class="banner" v-if="(isServices || isAdmin) && form.state === 'PROCESSING' && (form.repair.state === 'SUCCESS' || form.visit.state === 'FINISH' || form.remote.state === 'FINISH')">
          {{
            (form.serviceTicket && form.serviceTicket.supplement && form.serviceTicket.supplement.omsNo) || (form.supplementInfo && form.supplementInfo.supplementOrderInfoList && form.supplementInfo.supplementOrderInfoList.some(item => item.omsNo))
              ? form[serviceType].type === 'SELF'
                ? `当前售后单“客服已下单”，${
                    form.showProcessorForCustomer
                      ? `当前处理人：${form.showProcessorForCustomer}，`
                      : ''
                  }你可以`
                : `当前售后单“待采购下单”，${
                    form.showProcessorForCustomer
                      ? `当前处理人：${form.showProcessorForCustomer}，`
                      : ''
                  }你可以`
              : `当前售后单“待确认费用”，${
                  form.showProcessorForCustomer ? `当前处理人：${form.showProcessorForCustomer}，` : ''
                }你可以`
          }}
          <el-button type="primary" @click="handleServiceStatus('supplement')" v-if="isComfirm">确认并下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('addSO')" v-if="isAddOrder">客户已下单</el-button>
          <el-button type="primary" @click="handleServiceStatus('rejectPay')" v-if="isRefusePay">拒绝付费</el-button>
          <el-button type="primary" @click="handleServiceStatus('tempReply', serviceType.toUpperCase(), `${serviceType.toUpperCase()}_FINISH`)">临时回复</el-button>
        </div>
        <div class="banner" v-if="(isOperates || isBuyer || isAdmin) && form.state === 'PROCESSING' && (form.repair.state === 'SUCCESS' || form.visit.state === 'FINISH' || form.remote.state === 'FINISH')">
          {{
            form.serviceTicket && form.serviceTicket.supplement && form.serviceTicket.supplement.omsNo
              ? form[serviceType].type === 'SELF'
                ? `当前售后单“客服已下单”，${
                    form.showProcessorForOperator
                      ? `当前处理人：${form.showProcessorForOperator}，`
                      : ''
                  }你可以`
                : `当前售后单“待采购下单”，${
                    form.showProcessorForOperator
                      ? `当前处理人：${form.showProcessorForOperator}，`
                      : ''
                  }你可以`
              : `当前售后单“待确认费用”，${
                  form.showProcessorForOperator ? `当前处理人：${form.showProcessorForOperator}，` : ''
                }你可以`
          }}
          <el-button type="primary" @click="handleServiceStatus('tempReply', serviceType.toUpperCase(), `${serviceType.toUpperCase()}_FINISH`)">临时回复</el-button>
          <el-button type="primary" @click="handleServiceStatus('needPay')" v-if="isNeedPay">服务需付费</el-button>
          <el-button type="primary" @click="handleServiceStatus('noPay')" v-if="isNeedPay">服务无需付费</el-button>
        </div>
      </div>
      <div class="banner" v-if="form.state === 'SUCCESS'">
        当前售后单“服务完成”
      </div>
      <!-- 取消售后申请 -->
      <div class="banner" v-if="form.state === 'CANCEL'">
        当前售后单“已取消”
      </div>
      <!-- 拒绝售后申请 -->
      <div class="banner" v-if="form.state === 'REFUSED'">
        当前售后单“已被拒绝”
      </div>
      <!-- 作废售后申请 -->
      <div class="banner" v-if="form.state === 'OBSOLETE'">当前售后申请已被作废</div>
    </div>

    <!-- 非服务类引导区 -->
  <div class="module-guide" v-if="flag.info && form.apply.createType !== 'SERVICE'">
      <div class="banner" v-if="form.state === 'PENDING'">
        当前售后申请单还未审核，请
        <el-button type="primary" @click="handleStatus('approve')">同意</el-button> 或
        <el-button type="danger" @click="handleStatus('reject')">拒绝</el-button>
        <br />
        <template v-if="form.apply && form.apply.source && form.apply.source !== 'GW'">
          您还可以操作
          <el-button type="primary" @click="handleStatus('edit')">编辑</el-button> 或
          <el-button @click="handleStatus('cancel')">取消售后</el-button>
        </template>
      </div>
      <div class="banner" v-if="form.state === 'CANCEL'">当前售后申请已被取消</div>
      <div class="banner" v-if="form.state === 'REFUSED'">当前售后申请已被驳回</div>
      <div class="banner" v-if="form.state === 'OBSOLETE'">当前售后申请已被作废</div>
      <div class="banner" v-if="/PROCESSING|APPROVING/.test(form.state)">您已同意处理该售后申请单，售后类型： {{ map.type[form.decide.type] }}</div>
      <div class="banner" v-if="form.invoice.needReInvoice && form.invoice.state === 'SUCCESS' && form.state === 'PROCESSING'">
        如果补开发票已经处理完成，请操作：
        <el-button type="primary" @click="handleStatus('fillInvoice')" >
          填写发票号
        </el-button>
      </div>
      <!-- OA -->
      <div class="banner" v-if="isAdmin && form.oaProcess && form.oaProcess.goods && form.oaProcess.goods.state === 'PENDING' && /GOODS|REPLACE/.test(form.decide.type)">
        因涉及退货，需提交至OA审批
        <el-button type="primary" @click="handleStatus('submitOAReturn')">提交退货审批</el-button>
      </div>
      <div class="banner" v-if="isAdmin && form.oaProcess && form.oaProcess.invoice && form.oaProcess.invoice.state === 'PENDING' && /INVOICE|GOODS|REPLACE/.test(form.decide.type)">
        因涉及退票，需提交至OA审批
        <el-button type="primary" @click="handleStatus('submitOAInvoice')">提交退票审批</el-button>
      </div>
      <div class="banner" v-if="isAdmin && ((/REFUND|CANCEL/.test(form.decide.type) && form.oaProcess && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING')
                  || (/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING') && (form.goods && form.goods.state === 'SUCCESS' && (form.decide.needInvoice && form.invoice && /SUCCESS|REINVOICE/.test(form.invoice.state))))">
        因涉及退款，需提交至OA审批
        <el-button type="primary" @click="handleStatus('submitOARefund')">提交退款审批</el-button>
      </div>
      <div class="banner" v-if="/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess && form.oaProcess.invoice && form.oaProcess.invoice.state === 'PENDING'">
        因涉及退票，需填写OA退票信息<el-button type="primary" @click="handleStatus('fillOAInvoice')">填写退票信息</el-button>
      </div>
      <div class="banner" v-if="/GOODS|REPLACE/.test(form.decide.type)
      && form.oaProcess && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING'
      && form.goods && form.goods.state === 'SUCCESS'
      && (form.decide.needInvoice && form.invoice && /SUCCESS|REINVOICE/.test(form.invoice.state))
      || /GOODS|REPLACE/.test(form.decide.type)
      && form.oaProcess && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING'
      && form.goods && form.goods.state === 'SUCCESS' && !form.decide.needInvoice">
        因涉及退款，需填写OA退款信息<el-button type="primary" @click="handleStatus('fillOARefund')">填写退款信息</el-button>
      </div>

      <!-- OTHER -->
      <div class="banner" v-if="form.state === 'PROCESSING' && form.goods && form.goods.state === 'PENDING' && /GOODS|REPLACE|REPAIR|DETECTION/.test(form.decide.type)">
        待买家退回商品，如买家已退，更新物流信息，请操作
        <el-button type="primary" @click="handleStatus('submitReturned')">买家已退</el-button>
      </div>
      <div class="banner" v-if="form.state === 'PROCESSING' && form.goods && /PENDING|RETURNED/.test(form.goods.state) && /GOODS|REPLACE/.test(form.decide.type) && !form.goods.orderNum">
        请您创建销售退货订单，如已创建，请填写销售退货订单号
        <el-button type="primary" @click="handleStatus('returnOrder')">填写订单号</el-button>
      </div>
      <div class="banner" v-if="isAdmin && form.state === 'PROCESSING' && form.goods && /RETURNED/.test(form.goods.state) && /GOODS|REPLACE|REPAIR|DETECTION/.test(form.decide.type)">
        如果退货已经处理完成，请操作
        <el-button type="primary" @click="handleStatus('finishReturn')" >退货完成</el-button>
      </div>

      <div class="banner" v-if="form.state === 'PROCESSING' && form.invoice && form.invoice.state === 'PENDING'">
        待买家退票，如果已退，更新物流信息，请操作
        <el-button type="primary" @click="handleStatus('submitInvoiced')">买家已退</el-button>
      </div>
      <div class="banner" v-if="form.state === 'PROCESSING' && form.replace && form.replace.state === 'PENDING' && /GOODS|REPLACE/.test(form.decide.type) && !form.replace.orderNum">
        请您创建销售换货订单，如已创建，请填写销售换货订单号
        <el-button type="primary" @click="handleStatus('replaceOrder')">填写订单号</el-button>
      </div>
      <div class="banner" v-if="isAdmin && form.state === 'PROCESSING' && form.invoice && form.invoice.state === 'RETURNED'">
        如果退票已经处理完成，请操作
        <el-button type="primary" @click="handleStatus('invoiceFinished')">退票完成</el-button>
      </div>

      <div class="banner" v-if="form.oaProcess && form.oaProcess.refund && form.oaProcess.refund.state === 'SUCCESS' && form.state === 'PROCESSING' && form.refund && /PENDING/.test(form.refund.state)">
        退款审批已通过，请跟进财务打款进度，如果财务已完成付款，请操作
        <el-button type="primary" @click="handleStatus('finishRefund')">退款完成</el-button>
      </div>

      <div class="banner" v-if="createOrderStatus">
        您已同意售后申请，请创建工单，协同相关人员进行服务吧
        <el-button type="primary" @click="handleStatus('createRepair')">创建工单</el-button>
      </div>
      <div class="banner" v-if="isAdmin && form.state === 'PROCESSING' && form[serviceType] && /PENDING|WORKING|DATE|RECEIVED/.test(form[serviceType].state)">
        售后申请处理中，更新进度，请操作
        <el-button type="primary" @click="handleStatus('updateRepair')">更新工单进度</el-button>
      </div>
      <div class="banner" v-if="form.state === 'PROCESSING' && form[serviceType] && /RETURNED/.test(form[serviceType].state)">
        厂家已维修完成，并已寄出，请跟进客户收货进度，如果客户已收货，请操作
        <el-button type="primary" @click="handleStatus('repaired')">买家已收货</el-button>
      </div>

      <!-- CANCEL -->
      <div class="banner" v-if="form.state === 'PROCESSING' && form.decide.type === 'CANCEL'">
        您已同意取消订单，请进行订单拦截并取消该商品行，如已取消，请操作
        <el-button type="primary" @click="handleStatus('agreeToCancel')">订单已取消</el-button>
      </div>

      <div class="banner" v-if="form.state === 'SUCCESS'">当前售后申请已经处理完毕！</div>

    </div>
    <div class="module-guide" v-if="!flag.info">
      <div class="banner">该售后单不存在</div>
    </div>
  </div>
  <div class="page page-aftersales-detail" v-loading="load.detail">

    <div class="module-detail" v-if="flag.info">
      <div class="module-info">
        <h3 class="block-title">申请信息</h3>
        <div class="module-info-customer">
          <p>
            <span class="label">售后类型：</span>{{map.type[form.apply.createType]}}
          </p>
          <p>
            <span class="label">申请单号：</span>{{form.ticketNo}}
          </p>
          <p>
            <span class="label">申请渠道：</span>{{map.source[form.apply.source]}}
          </p>
          <p v-if="form.serviceTicket && form.serviceTicket.workOrderNo">
            <span class="label">工单号：</span><el-button type="text" @click="toWorkflow">{{form.serviceTicket.workOrderNo}}</el-button>
          </p>
        </div>
        <h3 class="block-title">客户信息</h3>
        <div class="module-info-customer">
          <p>
            <span class="label">客户：</span>{{form.order && form.order.customerName}}
          </p>
          <p>
            <span class="label">客户编码：</span>{{form.order && form.order.customerNo}}
          </p>
        </div>
        <h3 class="block-title">订单信息</h3>
        <order-info :tableList="form.orderDetails" disabled :serviceAfterSales="form.apply.createType"></order-info>
        <!-- 作废 btn -->
        <h3 class="block-title title-with-btn">
          <span>售后申请</span>
          <el-button type="primary" v-if="(isSERVICE && isServices && form.state !== 'OBSOLETE' && (visitState === 'WORKING' || (form.goods && form.goods.state === 'PENDING'))) || (!isSERVICE && ((form.state === 'PROCESSING'
            && !/APPROVING/.test(form.oaProcess && form.oaProcess.invoice && form.oaProcess.invoice.state)
            && !/APPROVING/.test(form.oaProcess && form.oaProcess.refund && form.oaProcess.refund.state)) ||
            (form.state === 'APPROVING' && /FAILED/.test(form.oaProcess && form.oaProcess.refund && form.oaProcess.refund.state)) ||
            (form.state === 'APPROVING' && /FAILED/.test(form.oaProcess && form.oaProcess.goods && form.oaProcess.goods.state)))) "
            @click="handleStatus('obsolete')">作废</el-button>
        </h3>
        <div class="module-aftersale-info">
          <!-- <div class="flex-box">
            <p><strong>申请单号</strong> : {{ form.ticketNo }}</p>
            <p v-if="form.apply.source"><strong>申请渠道</strong> : {{ map.source[form.apply.source] }} </p>
          </div> -->
          <div class="flex-box">
            <p v-if="form.apply.createType"><strong>申请类型</strong> : {{ map.type[form.apply.createType] }} </p>
            <p v-if="isSERVICE && form.serviceTicket.operateName"><strong>售后运营</strong> : {{ form.serviceTicket.operateName }} <span v-if="form.serviceTicket.operateAgentName">(代运营：{{ form.serviceTicket.operateAgentName }}）</span></p>
            <p v-if="form.apply.reason" class="show-explain"><strong>申请原因</strong> : {{ form.apply.reason }} </p>
          </div>
          <div class="flex-box">
            <!-- <p v-if="isGOODS || isREPLACE"><strong>申请数量</strong> : {{ form.apply.quantity }} </p> -->
            <p v-if="isGOODS || isREPLACE || isINVOICE || isREFUND || isCANCEL"><strong>申请金额</strong> : {{ form.apply.money }} </p>
          </div>
          <div class="flex-box">
            <p v-if="form.apply.customerContact"><strong>客户联系人</strong> : {{ form.apply.customerContact }} </p>
            <p v-if="form.apply.customerPhone"><strong>客户手机号</strong> :
              <safe-phone-num
                :phone="form.apply.customerPhone"
                :value="
                  JSON.stringify({
                    user: user.name,
                    no: form.ticketNo,
                    value: coverMobileAndLandline(form.apply.customerPhone),
                    field: '客户手机号',
                    dataType: '售后订单',
                  })
                "
              ></safe-phone-num>
            </p>
            <p v-if="isSERVICE && form.serviceTicket && form.serviceTicket.expectedTime"><strong>客户期望服务时间</strong> : {{ form.serviceTicket.expectedTime }} </p>
          </div>
          <div class="flex-box" v-if="isGOODS || isREPLACE || isREPAIR || isVISIT || isDETECTION || isSERVICE">
            <p><strong>{{isSERVICE ? '客户服务联系人' : '客户收货联系人'}}</strong> : {{ form.apply.contact }} </p>
            <p><strong>{{isSERVICE ? '客户服务收货电话' : '客户收货电话'}}</strong> :
              <safe-phone-num
                :phone="form.apply.phone"
                :value="
                  JSON.stringify({
                    user: user.name,
                    no: form.ticketNo,
                    value: coverMobileAndLandline(form.apply.phone),
                    field: '客户收货电话',
                    dataType: '售后订单',
                  })
                "
              ></safe-phone-num>
            </p>
            <p><strong>{{isSERVICE ? '客户服务收货地址' : '客户收货地址'}}</strong> : {{ form.apply.address }} </p>
          </div>
          <div class="flex-box" style="width:100%">
            <p v-if="form.apply.question" class="show-explain pre-line"><strong>问题描述</strong> : {{ form.apply.question }}</p>
          </div>
          <div class="certificates">
            <p><strong>售后凭证</strong></p>
            <preview :data="form.certificates.TICKET"></preview>
          </div>
        </div>
      </div>
    </div>
    <!-- 服务类售后进度 -->
    <service-progress :form="form" :map="map" :progress="progress" v-if="form.apply.createType === 'SERVICE'"/>
    <!-- 非服务类售后进度 -->
    <div class="module-progress" v-if="!/PENDING/i.test(form.state) && form.apply.createType !== 'SERVICE'">
      <h3 class="block-title">售后进度</h3>
      <div class="module-progress-content">
        <!-- 卡片 -->
        <div class="module-progress-card" v-if="/APPROVING|PROCESSING|SUCCESS|OBSOLETE/i.test(form.state)">
          <h4>判定结果</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="card">
                <judge :propsData="judgeData" :info="true"></judge>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="form.oaProcess">
          <h4>OA进度</h4>
          <div class="progress-content">
            <div class="card-group" v-if="form.oaProcess.goods">
              <div class="operation">
                <span>{{ formatTime(form.oaProcess.goods.updateTime) }}</span>
                <span v-show="form.oaProcess.goods.operator">操作者: {{ form.oaProcess.goods.operator }}</span>
              </div>
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong>退货审批: </strong><strong :class="`status-${form.oaProcess.goods.state}`">{{ map.oaStatus[form.oaProcess.goods.state] }}</strong></span>
                    <span v-if="form.oaProcess.goods.number && form.oaProcess.goods.requestId">审批单号:
                      <a :href="`${oaHost}/workflow/request/ViewRequestForwardSPA.jsp?requestid=${form.oaProcess.goods.requestId}`" target="_blank">{{ form.oaProcess.goods.number }}</a>
                    </span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('submitOAReturn')" v-if="isAdmin && form.state === 'APPROVING' && form.oaProcess.goods && form.oaProcess.goods.state === 'PENDING' && /GOODS|REPLACE/.test(form.decide.type)">提交退货审批</el-button>
                    <el-button type="primary" @click="handleStatus('editReturn')" v-if="form.state === 'APPROVING' && form.oaProcess.goods && form.oaProcess.goods.state === 'FAILED' && /GOODS|REPLACE/.test(form.decide.type)">修改提交退货</el-button>
                    <el-button type="primary" @click="handleStatus('finishOAReturn')" v-if="isAdmin && form.state === 'APPROVING' && form.oaProcess.goods && form.oaProcess.goods.state === 'APPROVING' && /GOODS|REPLACE/.test(form.decide.type)">退货审批通过</el-button>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-group" v-if="form.oaProcess.invoice">
              <div class="operation">
                <span>{{ formatTime(form.oaProcess.invoice.updateTime) }}</span>
                <span v-show="form.oaProcess.invoice.operator">操作者: {{ form.oaProcess.invoice.operator }}</span>
              </div>
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong>退票审批: </strong><strong :class="`status-${form.oaProcess.invoice.state}`">{{ map.oaStatus[form.oaProcess.invoice.state] }}</strong></span>
                    <span v-if="form.oaProcess.invoice.number && form.oaProcess.invoice.requestId">审批单号:
                      <a :href="`${oaHost}/workflow/request/ViewRequestForwardSPA.jsp?requestid=${form.oaProcess.invoice.requestId}`" target="_blank">{{ form.oaProcess.invoice.number }}</a>
                    </span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('fillOAInvoice')" v-if="/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess.invoice && form.oaProcess.invoice.state === 'PENDING'">填写退票信息</el-button>
                    <el-button type="primary" @click="handleStatus('submitOAInvoice')" v-if="isAdmin && form.oaProcess.invoice && form.oaProcess.invoice.state === 'PENDING'">提交退票审批</el-button>
                    <el-button type="primary" @click="handleStatus('editInvoice')" v-if="form.oaProcess.invoice && form.oaProcess.invoice.state === 'FAILED'">修改提交退票</el-button>
                    <el-button type="primary" @click="handleStatus('finishOAInvoice')" v-if="isAdmin && form.oaProcess.invoice && form.oaProcess.invoice.state === 'APPROVING'">退票审批通过</el-button>
                  </div>
                </div>
              </div>
            </div>
            <div class="card-group" v-if="form.oaProcess.refund">
              <div class="operation">
                <span>{{ formatTime(form.oaProcess.refund.updateTime) }}</span>
                <span v-show="form.oaProcess.refund.operator">操作者: {{ form.oaProcess.refund.operator }}</span>
              </div>
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong>退款审批: </strong><strong :class="`status-${form.oaProcess.refund.state}`">{{ map.oaStatus[form.oaProcess.refund.state] }}</strong></span>
                    <span v-if="form.oaProcess.refund.number">审批单号: {{ form.oaProcess.refund.number }}</span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('fillOARefund')"
                    v-if="/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING' && form.goods && form.goods.state === 'SUCCESS' && (form.decide.needInvoice && form.invoice && /SUCCESS|REINVOICE/.test(form.invoice.state))
                    || /GOODS|REPLACE/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING' && form.goods && form.goods.state === 'SUCCESS' && !form.decide.needInvoice">
                      填写退款信息
                    </el-button>
                    <el-button type="primary" @click="handleStatus('submitOARefund')"
                    v-if="isAdmin && ((/REFUND|CANCEL/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING')
                    || (/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING') && (form.goods && form.goods.state === 'SUCCESS' && (form.decide.needInvoice && form.invoice && /SUCCESS|REINVOICE/.test(form.invoice.state)))
                    || (/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'PENDING') && (form.goods && form.goods.state === 'SUCCESS' && !form.decide.needInvoice))">
                      提交退款审批
                    </el-button>
                    <el-button type="primary" @click="handleStatus('editRefund')"
                    v-if="(/REFUND|CANCEL/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'FAILED')
                    || (/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'FAILED') && (form.goods && form.goods.state === 'SUCCESS' && (form.decide.needInvoice && form.invoice && /SUCCESS|REINVOICE/.test(form.invoice.state)))
                    || (/GOODS|REPLACE/.test(form.decide.type) && form.oaProcess.refund && form.oaProcess.refund.state === 'FAILED') && (form.goods && form.goods.state === 'SUCCESS' && !form.decide.needInvoice)">
                      修改提交退款</el-button>
                    <el-button type="primary" @click="handleStatus('finishOARefund')" v-if="isAdmin && form.oaProcess.refund && form.oaProcess.refund.state === 'APPROVING'">退款审批通过</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="form.goods.state">
          <h4>退货进度</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="operation">
                <span>{{ formatTime(form.goods.updateTime) }}</span>
                <span v-show="form.goods.operator">操作者: {{ form.goods.operator }}</span>
              </div>
              <div class="card">
                <div class="head" :class="{'border': form.expressInfoMap.GOODS || form.goods.orderNum}">
                  <div class="group title">
                    <span><strong :class="`status-${form.goods.state}`">{{ map.returnStatus[form.goods.state] }}</strong></span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('submitReturned')" v-if="form.state === 'PROCESSING' && form.goods && form.goods.state === 'PENDING' && /GOODS|REPLACE|REPAIR|DETECTION/.test(form.decide.type)">买家已退</el-button>
                    <el-button type="primary" @click="handleStatus('returnOrder')" v-if="form.state === 'PROCESSING' && form.goods && /PENDING|RETURNED/.test(form.goods.state) && /GOODS|REPLACE/.test(form.decide.type)">{{ form.goods.orderNum ? '修改' : '填写' }}订单号</el-button>
                    <el-button type="primary" @click="handleStatus('finishReturn')" v-if="isAdmin && form.state === 'PROCESSING' && form.goods && /RETURNED/.test(form.goods.state) && /GOODS|REPLACE|REPAIR|DETECTION/.test(form.decide.type)">退货完成</el-button>
                  </div>
                </div>
                <div class="body express-info">
                  <template v-if="form.expressInfoMap.GOODS">
                    <p><strong>退货寄送方式</strong> : {{ map.logisticsType[form.expressInfoMap.GOODS.type] }}</p>
                    <p v-if="form.expressInfoMap.GOODS.type !== 'SELF'"><strong>物流单号</strong> : {{ form.expressInfoMap.GOODS['number'] }}</p>
                    <p v-if="form.expressInfoMap.GOODS.type !== 'SELF'"><strong>物流公司</strong> : {{ form.expressInfoMap.GOODS['company'] }}</p>
                    <p><strong>联系电话</strong> : {{ form.expressInfoMap.GOODS['phone'] }}</p>
                    <p v-if="form.expressInfoMap.GOODS.type === 'SELF'"><strong>自运司机姓名</strong> : {{ form.expressInfoMap.GOODS['contact'] }}</p>
                    <p v-if="form.expressInfoMap.GOODS.type === 'SELF'"><strong>自运司机电话</strong> : {{ form.expressInfoMap.GOODS['driverPhone'] }}</p>
                    <p v-if="form.expressInfoMap.GOODS.type === 'SELF'"><strong>自运司机牌号</strong> : {{ form.expressInfoMap.GOODS['car'] }}</p>
                    <p class="show-explain"><strong>退货说明</strong> : {{ form.expressInfoMap.GOODS['content'] }}</p>
                    <div class="certificates">
                      <p>
                        <strong>退回凭证</strong>
                      </p>
                      <preview :data="expressReturn"></preview>
                    </div>
                  </template>
                  <p v-if="form.goods.orderNum"><strong>退货订单号</strong>: {{ form.goods.orderNum }}</p>
                  <!-- <p><strong>退货订单状态</strong></p> -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="form.invoice.state">
          <h4>退票进度</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="operation">
                <span>{{ formatTime(form.invoice.updateTime) }}</span>
                <span v-show="form.invoice.operator">操作者: {{ form.invoice.operator }}</span>
              </div>
              <div class="card">
                <div class="head" :class="{'border': form.expressInfoMap && form.expressInfoMap.INVOICE}">
                  <div class="group title">
                    <span>
                      <strong :class="`status-${form.invoice.state+(form.invoice.state === 'SUCCESS' && form.invoice.needReInvoice?'-invoice':'')}`">
                        {{ map.invoiceStatus[form.invoice.state] }}
                      </strong>
                    </span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('submitInvoiced')" v-if="form.state === 'PROCESSING' && form.invoice && /PENDING/.test(form.invoice.state)">买家已退</el-button>
                    <el-button type="primary" @click="handleStatus('finishInvoice')" v-if="isAdmin && form.state === 'PROCESSING' && form.invoice && /RETURNED/.test(form.invoice.state)">退票完成</el-button>
                    <el-button type="primary" @click="handleStatus('fillInvoice')" v-if="form.invoice.needReInvoice && form.invoice.state === 'SUCCESS' && form.state === 'PROCESSING'">填写发票号</el-button>
                  </div>
                </div>
                <div class="body express-info" v-if="form.expressInfoMap && form.expressInfoMap.INVOICE">
                  <p><strong>退货寄送方式</strong> : {{ map.logisticsType[form.expressInfoMap.INVOICE.type] }}</p>
                  <p v-if="form.expressInfoMap.INVOICE.type !== 'SELF'"><strong>物流单号</strong> : {{ form.expressInfoMap.INVOICE['number'] }}</p>
                  <p v-if="form.expressInfoMap.INVOICE.type !== 'SELF'"><strong>物流公司</strong> : {{ form.expressInfoMap.INVOICE['company'] }}</p>
                  <p><strong>联系电话</strong> : {{ form.expressInfoMap.INVOICE['phone'] }}</p>
                  <p v-if="form.expressInfoMap.INVOICE.type === 'SELF'"><strong>自运司机姓名</strong> : {{ form.expressInfoMap.INVOICE['contact'] }}</p>
                  <p v-if="form.expressInfoMap.INVOICE.type === 'SELF'"><strong>自运司机电话</strong> : {{ form.expressInfoMap.INVOICE['driverPhone'] }}</p>
                  <p v-if="form.expressInfoMap.INVOICE.type === 'SELF'"><strong>自运司机牌号</strong> : {{ form.expressInfoMap.INVOICE['car'] }}</p>
                  <p class="show-explain"><strong>退货说明</strong> : {{ form.expressInfoMap.INVOICE['content'] }}</p>
                  <div class="certificates">
                    <p><strong>退回凭证</strong></p>
                    <preview :data="expressInvoice"></preview>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="form.refund.state">
          <h4>退款进度</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="operation">
                <span>{{ formatTime(form.refund.updateTime) }}</span>
                <span v-show="form.refund.operator">操作者: {{ form.refund.operator }}</span>
              </div>
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong :class="`status-${form.refund.state}`">{{ map.refundStatus[form.refund.state] }}</strong></span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('finishRefund')" v-if="form.oaProcess.refund && form.oaProcess.refund.state === 'SUCCESS' && form.state === 'PROCESSING' && form.refund && /PENDING/.test(form.refund.state)">退款完成</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="form.replace.state">
          <h4>换货进度</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="operation">
                <span>{{ formatTime(form.replace.updateTime) }}</span>
                <span v-show="form.replace.operator">操作者: {{ form.replace.operator }}</span>
              </div>
              <div class="card">
                <div class="head" :class="{'border': form.replace.orderNum}">
                  <div class="group title">
                    <span><strong :class="`status-${form.replace.state}`">{{ map.replaceStatus[form.replace.state] }}</strong></span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('replaceOrder')" v-if="form.state === 'PROCESSING' && form.decide.type === 'REPLACE' && form.replace && form.replace.state === 'PENDING'">{{ form.replace.orderNum ? '修改' : '填写' }}订单号</el-button>
                    <el-button type="primary" @click="handleStatus('finishReplaced')" v-if="isAdmin && form.state === 'PROCESSING' && form.decide.type === 'REPLACE' && form.replace && form.replace.state === 'PENDING'">已发货</el-button>
                  </div>
                </div>
                <div class="body" v-if="form.replace.orderNum">
                  <p><strong>换货订单号</strong> : {{ form.replace.orderNum }}</p>
                  <!-- <p><strong>换货订单状态</strong> : {{ map.replaceStatus[form.replace.state] }}</p> -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="form.cancel && form.cancel.state">
          <h4>订单进度</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="operation">
                <span>{{ formatTime(form.cancel.updateTime) }}</span>
                <span v-show="form.cancel.operator">操作者: {{ form.cancel.operator }}</span>
              </div>
              <div class="card">
                <div class="head">
                  <div class="group title">
                    <span><strong :class="`status-${form.cancel.state}`">{{ form.cancel.state === 'PENDING' ? '待取消' : '已取消'}}</strong></span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('agreeToCancel')" v-if="form.cancel.state === 'PENDING'">订单已取消</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="form[serviceType] && form[serviceType].state">
          <h4>服务进度</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="operation">
                <span>{{ formatTime(form[serviceType].updateTime) }}</span>
                <span v-show="form[serviceType].operator">操作者: {{ form[serviceType].operator }}</span>
              </div>
              <div class="card">
                <div class="head" :class="{'border': serviceExpress}">
                  <div class="group title">
                    <span><strong :class="`status-${form[serviceType].state}`">{{ map[`${serviceType}Status`][form[serviceType].state] }}</strong></span>
                    <span v-if="form[serviceType].workId">工单号：
                      <el-button type="text" size="medium" @click="routeToWorkflow(`/wf/detail/${form[serviceType].workId}`)">{{ form[serviceType].workId }}</el-button>
                    </span>
                  </div>
                  <div class="group">
                    <el-button type="primary" @click="handleStatus('createRepair')" v-if="createOrderStatus">创建工单</el-button>
                    <el-button type="primary" @click="handleStatus('updateRepair')" v-if="isAdmin && form.state === 'PROCESSING' && form[serviceType] && /PENDING|WORKING|DATE|RECEIVED/.test(form[serviceType].state)">更新工单进度</el-button>
                    <el-button type="primary" @click="handleStatus('finishRepair')" v-if="form.state === 'PROCESSING' && form[serviceType] && /SENDOFF/.test(form[serviceType].state)">买家已收货</el-button>
                  </div>
                </div>
                <div class="body express-info" v-if="serviceExpress">
                  <p><strong>退货寄送方式</strong> : {{ map.logisticsType[serviceExpress.type] }} </p>
                  <p v-if="serviceExpress.number"><strong>物流单号</strong> : {{ serviceExpress.number }} </p>
                  <p v-if="serviceExpress.company"><strong>物流公司</strong> : {{ serviceExpress.company }} </p>
                  <p v-if="serviceExpress.phone"><strong>联系电话</strong> : {{ serviceExpress.phone }} </p>
                  <p v-if="serviceExpress.content" class="show-explain"><strong>退货说明</strong> : {{ serviceExpress.content }} </p>
                  <p v-if="serviceExpress.contact"><strong>自运司机名称</strong> : {{ serviceExpress.contact }} </p>
                  <p v-if="serviceExpress.driverPhone"><strong>自运司机手机</strong> : {{ serviceExpress.driverPhone }} </p>
                  <p v-if="serviceExpress.car"><strong>自运车牌号</strong> : {{ serviceExpress.car }} </p>
                  <div class="certificates">
                    <p><strong>售后凭证</strong></p>
                    <preview :data="expressOfServiceType"></preview>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="module-progress-card" v-if="/CANCEL|REFUSED|SUCCESS|OBSOLETE/.test(form.state)">
          <h4>售后状态</h4>
          <div class="progress-content">
            <div class="card-group">
              <div class="card">
                <div class="head" :class="{'border': form.obsolete && form.obsolete.reason }">
                  <div class="group title">
                    <p><strong :class="`status-${form.state}`">{{ form.stateName }}</strong></p>
                  </div>
                </div>
                <div class="body">
                  <template v-if="form.obsolete">
                    <p class="show-explain"><strong>作废原因</strong> : {{ map.obsoleteType[form.obsolete.type] }} </p>
                    <p class="show-explain"><strong>说明</strong> : {{ form.obsolete.reason }} </p>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 卡片:end -->
      </div>
    </div>

    <el-dialog title="填写判定结果" center :visible.sync="show.approve" width="90%" custom-class="dialog-aftersale">
      <judge :propsData="judgeData" ref="judge"></judge>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitApprove">提交</el-button>
      </p>
    </el-dialog>

    <!-- 拒绝原因 -->
    <el-dialog title="填写拒绝原因" center :visible.sync="show.reject" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :rules="rules.reject" :model="dialog.reject" ref="formReject">
        <el-form-item label="拒绝原因" prop="reason" style="width:100%">
          <el-input v-model="dialog.reject.reason" class="long-input" maxlength="255"></el-input>
        </el-form-item>
        <el-form-item label="拒绝说明" prop="refuseReason" style="width:100%">
          <el-input type="textarea" v-model="dialog.reject.refuseReason" class="long-input"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="apiReject">确认</el-button>
        <el-button type="primary" @click="show.reject = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 作废原因 -->
    <el-dialog title="填写作废原因" center :visible.sync="show.obsolete" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :rules="rules.obsolete" :model="dialog.obsolete" ref="formObsolete">
        <el-form-item label="作废原因" prop="type">
          <el-select v-model="dialog.obsolete.type" placeholder="请选择">
            <el-option v-for="item in option.obsoleteType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="说明" prop="reason" style="width:100%">
          <el-input v-model="dialog.obsolete.reason" type="textarea" style="width:400px" class="long-input"  maxlength="200"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="apiObsolete">确认</el-button>
        <el-button type="primary" @click="show.obsolete = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 填写物流信息 -->
    <el-dialog :title="/UPDATE/.test(type.logistics) ? '编辑物流信息' : '填写物流信息'" center :visible.sync="show.logistics" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :model="dialog.logistics" class="modals" :rules="rules.logistics" ref="formLogistics">
        <el-form-item :label="/REISSUE/.test(form.decide.type) ? '配送方式' : '退货方式'" prop="type">
          <el-select v-model="dialog.logistics.type" placeholder="请选择" @change="handleLogisticsType">
            <el-option v-for="item in (/REISSUE/.test(form.decide.type) ? option.reissueLogisticsType : option.logisticsType)" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="number" v-if="dialog.logistics.type !== 'SELF' && dialog.logistics.type !== 'OTHER'">
          <el-input v-model="dialog.logistics.number" placeholder="请输入"  maxlength="80" @change="changeLogisticsNumber"></el-input>
        </el-form-item>
        <el-form-item label="物流公司" prop="company" v-if="dialog.logistics.type !== 'SELF' && dialog.logistics.type !== 'OTHER'">
          <el-input v-model="dialog.logistics.company" placeholder="请输入"  maxlength="80" :disabled="/REISSUE/.test(form.decide.type)"></el-input>
        </el-form-item>
        <el-form-item label="上传凭证" style="width: 100%">
          <!-- <el-upload class="upload-demo" drag
            list-type="picture"
            :limit="5"
            :with-credentials="true"
            :action="uploadUrl"
            :on-exceed="handleExceed"
            :on-success="(res, file, fileList) => afterUpload(res, file, fileList, 'logistics')"
            :on-error="(res, file, fileList) => afterUpload(res, file, fileList, 'logistics')"
            :on-remove="(file, fileList) => afterRemove(file, fileList, 'logistics')"
            :file-list="dialog.logistics.certificates">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload> -->
            <el-upload
              class="upload-demo"
              list-type="picture"
              multiple
              :with-credentials="true"
              action="/upload"
              drag
              :accept="acceptFileType.commonType"
              :before-upload="$validateFileType"
              :show-file-list="true"
              :before-remove="(file, fileList) => serviceBeforeRemove(file, fileList, 'logistics')"
              :http-request="(file) => serviceAfterUpload(file, 'logistics')"
              :on-remove="(file, fileList) => serviceAfterRemove(file, fileList, 'logistics')"
              :file-list="dialog.logistics.certificates">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
        </el-form-item>
        <el-form-item :label="/REISSUE/.test(form.decide.type) ? '备注说明' : '退货说明'" style="width: 100%" prop="content">
          <el-input v-model="dialog.logistics.content" placeholder="请输入"  maxlength="255" type="textarea" class="long-input"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitLogistics">确认</el-button>
        <el-button type="primary" @click="show.logistics = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 填写订单号 -->
    <el-dialog title="填写订单号" center :visible.sync="show.order" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :model="dialog.order" :rules="rules.order" ref="formOrder">
        <el-form-item label="订单号" prop="orderNum">
          <el-input v-model="dialog.order.orderNum"  maxlength="80"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitOrderId">确认</el-button>
        <el-button type="primary" @click="show.order = false">取消</el-button>
      </p>
    </el-dialog>

    <el-dialog title="更新工单进度" center :visible.sync="show.repair" width="900px" custom-class="dialog-aftersale">
      <el-form>
        <el-form-item label="工单进度">
          <el-select v-model="dialog.repair.state" placeholder="请选择">
            <el-option v-for="item in (option[`${serviceType}Status`] ? /repair|detection/.test(serviceType) ? option[`${serviceType}Status`].slice(2, -1) : option[`${serviceType}Status`].slice(2) : [])" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateRepairStatus">确认</el-button>
        <el-button type="primary" @click="show.repair = false">取消</el-button>
      </p>
    </el-dialog>

    <el-dialog title="修改退货审批" center :visible.sync="show.editReturn" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" class="form-column">
        <el-form-item label="退货责任方" required>
          <el-radio v-model="dialog.return.aspect" label="1">客户责任</el-radio>
          <el-radio v-model="dialog.return.aspect" label="0">我方责任</el-radio>
        </el-form-item>
        <el-form-item label="退货金额" required>
          <el-input-number v-model="dialog.return.amount" :controls="false" :min="0"  maxlength="80"></el-input-number>
        </el-form-item>
        <el-form-item label="SKU" required>
          <el-input v-model="dialog.return.sku"  maxlength="50"/>
        </el-form-item>
        <el-form-item label="物料组" required>
          <el-select v-model="dialog.return.oaGroupId" placeholder="请选择" filterable @change="selectMaterialGroup">
            <el-option v-for="item in option.materialGroup" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="退货原因" required>
          <el-input v-model="dialog.return.reason"  maxlength="255"></el-input>
        </el-form-item>
        <el-form-item label="退货附件" prop="certificates" style="width:100%">
          <el-upload class="upload-demo"
            list-type="picture-card"
            :limit="5"
            :with-credentials="true"
            :action="uploadUrl"
            :accept="acceptFileType.commonType"
            :before-upload="$validateFileType"
            :on-exceed="handleExceed"
            :on-success="(res, file, fileList) => afterUpload(res, file, fileList, 'return')"
            :on-error="(res, file, fileList) => afterUpload(res, file, fileList, 'return')"
            :on-remove="(file, fileList) => afterRemove(file, fileList, 'return')"
            :file-list="dialog.return.certificates">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditedReturn">提交</el-button>
        <el-button type="primary" @click="show.editReturn = false">取消</el-button>
      </p>
    </el-dialog>

    <el-dialog title="填写退票信息" center :visible.sync="show.editInvoice" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :model="dialog.invoice" :rules="rules.invoice" ref="formDialogInvoice">
        <el-form-item label="销售方名称" prop="companyId">
          <el-select v-model="dialog.invoice.companyId" filterable placeholder="请选择">
            <el-option v-for="item in option.company" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发票抬头名称" prop="invoiceTitle">
          <el-input v-model="dialog.invoice.invoiceTitle" placeholder="请输入"  maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="发票类型" prop="invoiceType">
          <el-select v-model="dialog.invoice.invoiceType" placeholder="请选择">
            <el-option v-for="item in option.invoiceType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发票开具日期" prop="invoiceDate">
          <el-date-picker v-model="dialog.invoice.invoiceDate" type="date" placeholder="选择日期"></el-date-picker>
        </el-form-item>
        <el-form-item label="金税发票号码" prop="invoiceNum">
          <el-input v-model="dialog.invoice.invoiceNum" placeholder="请输入"  maxlength="80"></el-input>
        </el-form-item>
        <el-form-item label="未税金额" prop="unsolved">
          <el-input-number v-model="dialog.invoice.unsolved" placeholder="请输入" :precision="2" :min="0" :controls="false"></el-input-number>
        </el-form-item>
        <el-form-item label="借项/贷项发票草稿号" prop="draftNo">
          <el-input v-model="dialog.invoice.draftNo"  maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="原件类型" prop="originalType">
          <el-select v-model="dialog.invoice.originalType" placeholder="请选择">
            <el-option v-for="item in option.originalType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="退票类型" prop="type">
          <el-select v-model="dialog.invoice.type" placeholder="请选择">
            <el-option v-for="item in option.returnInvoiceType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="退票具体原因" prop="reason">
          <el-input v-model="dialog.invoice.reason" type="textarea" :rows="5"  maxlength="255"></el-input>
        </el-form-item>
        <el-form-item label="退票附件" prop="certificates" style="width:100%">
          <el-upload class="upload-demo"
            list-type="picture-card"
            :limit="5"
            :with-credentials="true"
            :action="uploadUrl"
            :accept="acceptFileType.commonType"
            :before-upload="$validateFileType"
            :on-exceed="handleExceed"
            :on-success="(res, file, fileList) => afterUpload(res, file, fileList, 'invoice')"
            :on-error="(res, file, fileList) => afterUpload(res, file, fileList, 'invoice')"
            :on-remove="(file, fileList) => afterRemove(file, fileList, 'invoice')"
            :file-list="dialog.invoice.certificates">
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditedInvoice">提交</el-button>
        <el-button type="primary" @click="show.editInvoice = false">取消</el-button>
      </p>
    </el-dialog>

    <el-dialog title="填写退款信息" center :visible.sync="show.editRefund" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :model="dialog.refund" :rules="rules.refund" ref="formDialogRefund">
        <el-form-item label="收款单位" prop="company">
          <el-input v-model="dialog.refund.company" placeholder="请输入"  maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="客户代码" prop="number">
          <el-input v-model="dialog.refund.number" placeholder="请输入"  maxlength="50"></el-input>
        </el-form-item>
        <el-form-item label="开户行" prop="bank">
          <el-input v-model="dialog.refund.bank" placeholder="请输入"  maxlength="80"></el-input>
        </el-form-item>
        <el-form-item label="付款方式" prop="way">
          <el-select v-model="dialog.refund.way" placeholder="请选择">
            <el-option v-for="item in option.payPlatform" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="银行账户" prop="account">
          <el-input v-model="dialog.refund.account" placeholder="请输入"  maxlength="80"></el-input>
        </el-form-item>
        <el-form-item label="原收款日期" prop="oldDate">
          <el-date-picker v-model="dialog.refund.oldDate" type="date" placeholder="选择日期" @change="afterChangeDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="退款金额" prop="amount">
          <el-input-number v-model="dialog.refund.amount" placeholder="请输入" :precision="2" :min="0" :controls="false"  maxlength="80"></el-input-number>
        </el-form-item>
        <el-form-item label="原收款金额" prop="oldAmount">
          <el-input-number v-model="dialog.refund.oldAmount" placeholder="请输入" :precision="2" :min="0" :controls="false"  maxlength="80"></el-input-number>
        </el-form-item>
        <el-form-item label="公司名称" prop="companyId">
          <el-select v-model="dialog.refund.companyId" placeholder="请选择">
            <el-option v-for="item in option.company" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="退款说明" prop="reason">
          <el-input v-model="dialog.refund.reason" type="textarea" :rows="5" placeholder="请输入"  maxlength="255"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditedRefund">提交</el-button>
        <el-button type="primary" @click="show.editRefund = false">取消</el-button>
      </p>
    </el-dialog>

    <el-dialog title="填写发票号" center :visible.sync="show.fillInvoice" width="600px" >
      <el-form :inline="true" :model="form.invoice" :rules="rules.invoice" ref="reInvoiceNum">
        <el-form-item style="width: 100%" label="补开发票号" prop="reInvoiceNum" required>
          <el-input v-model="form.invoice.reInvoiceNum" placeholder="请输入"  maxlength="80"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditedRefundAfter">确认</el-button>
        <el-button @click="show.fillInvoice = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 确认申请信息 -->
    <el-dialog title="确认申请信息" center :visible.sync="show.confirmApplyInfo" width="900px" custom-class="dialog-aftersale">
      <h3 class="inner-text">已与客户确认信息准确无误？</h3>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="apiConfirm">确认</el-button>
        <el-button type="primary" @click="show.confirmApplyInfo = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 取消原因 -->
    <el-dialog title="填写取消原因" center :visible.sync="show.cancel" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :rules="rules.cancel" :model="dialog.cancel" ref="formCancel">
        <el-form-item label="取消原因" prop="reason" style="width:100%">
          <el-input v-model="dialog.cancel.reason" class="long-input" maxlength="255"></el-input>
        </el-form-item>
        <el-form-item label="取消说明" prop="explain" style="width:100%">
          <el-input type="textarea" v-model="dialog.cancel.explain" class="long-input"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="apiCancel">确认</el-button>
        <el-button type="primary" @click="show.cancel = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 临时回复 -->
    <el-dialog title="临时回复" center :visible.sync="show.tempReply" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :model="dialog.tempReply" :rules="rules.tempReply" ref="formDialogTempReply">
        <el-form-item label="回复内容" prop="context" style="width:100%">
          <el-input v-model="dialog.tempReply.context" type="textarea" :rows="5"  maxlength="255" class="long-input"></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="certificates" style="width:100%">
            <el-upload
              class="upload-demo"
              list-type="picture"
              multiple
              :with-credentials="true"
              action="/upload"
              drag
              :accept="acceptFileType.commonType"
              :before-upload="$validateFileType"
              :show-file-list="true"
              :http-request="(file) => serviceAfterUpload(file, 'tempReply')"
              :before-remove="(file, fileList) => serviceBeforeRemove(file, fileList, 'tempReply')"
              :on-remove="(file, fileList) => serviceAfterRemove(file, fileList, 'tempReply')"
              :file-list="dialog.tempReply.certificates">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTempReply(firstState, secondState)">提交</el-button>
        <el-button type="primary" @click="show.tempReply = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 判定服务类型 -->
    <el-dialog title="判定服务类型" center :visible.sync="show.judgeServiceType" width="1000px" custom-class="dialog-aftersale">
      <el-form :model="dialog.judgeServiceType" ref="judgeServiceType" :inline="true" :rules="rules.judgeServiceType">
        <el-form-item label="售后类型" style="width: 60%" prop="afterSaleType">
          <el-radio-group v-model="dialog.judgeServiceType.afterSaleType" @input="changeJudgeServiceType">
            <el-radio label="VISIT">上门服务</el-radio>
            <el-radio label="REPAIR">返厂服务</el-radio>
            <el-radio label="REISSUE">补发</el-radio>
            <el-radio label="REMOTE">远程指导</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="服务提供方" style="width: 36%" prop="serverSupplier">
          <el-radio-group v-model="dialog.judgeServiceType.serverSupplier" :disabled="dialog.judgeServiceType.afterSaleType === 'REISSUE'">
            <el-radio label="SELF">自营</el-radio>
            <el-radio label="SUPPLIER">供应商</el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="dialog.judgeServiceType.afterSaleType === 'REPAIR'">
          <el-form-item label="厂家收货联系人" prop="receivePerson">
            <el-input v-model="dialog.judgeServiceType.receivePerson"  maxlength="50" placeholder="请输入"/>
          </el-form-item>
          <el-form-item label="厂家收货电话" prop="telephone">
            <el-input v-model="dialog.judgeServiceType.telephone"  maxlength="50" placeholder="请输入"/>
          </el-form-item>
          <el-form-item label="厂家收货地址" prop="address">
            <el-input v-model="dialog.judgeServiceType.address"  maxlength="255" placeholder="请输入"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitJudgeServiceType">提交</el-button>
        <el-button type="primary" @click="show.judgeServiceType = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 驳回原因 -->
    <el-dialog title="填写驳回原因" center :visible.sync="show.refuse" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :rules="rules.refuse" :model="dialog.refuse" ref="formRefuse">
        <el-form-item label="驳回原因" prop="reason" style="width:100%">
          <el-input type="textarea" v-model="dialog.refuse.reason" class="long-input" :rows="5" maxlength="200" show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="apiRefuse">确认</el-button>
        <el-button type="primary" @click="show.refuse = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 任务交接 -->
    <el-dialog title="任务交接" center :visible.sync="show.handoff" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :rules="rules.handoff" :model="dialog.handoff" ref="formHandoff">
        <el-form-item label="任务接收人" prop="person" style="width:100%">
          <el-select
            v-model="dialog.handoff.person"
            filterable
            remote
            reserve-keyword
            placeholder="请输入姓名"
            :remote-method="queryUser"
            value-key="id"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickname + '(' + item.departmentName + ')'"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交接描述" prop="comment" style="width:100%">
          <el-input type="textarea" v-model="dialog.handoff.comment" class="long-input"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="apiHandoff">确认</el-button>
        <el-button type="primary" @click="show.handoff = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 更新服务进度 - 返厂 -->
    <el-dialog title="更新服务进度" center :visible.sync="show.serviceExpress" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" class="form-column" :rules="rules.serviceExpress" :model="dialog.serviceExpress" ref="serviceExpress">
        <el-form-item label="服务进度" prop="state">
          <el-select v-model="dialog.serviceExpress.state" placeholder="请选择" class="service-express-item" @change="changeServiceExpress">
            <el-option v-for="item in (form.repair && form.repair.state ? option.repairState : option.visitProgressType) " :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="dialog.serviceExpress.state === 'RECEIVED'">
          <el-form-item label="预计完成时间" prop="repairFinishTime" style="width: 100%">
            <!-- <el-date-picker v-model="dialog.serviceExpress.repairFinishTime" type="date" placeholder="请选择" value-format="yyyy-MM-dd"></el-date-picker> -->
            <el-input v-model="dialog.serviceExpress.repairFinishTime" placeholder="请输入"></el-input>
          </el-form-item>
        </div>
        <div v-if="dialog.serviceExpress.state === 'SENDOFF'">
          <el-form-item label="物流单号" prop="number">
            <el-input v-model="dialog.serviceExpress.number"  maxlength="50" placeholder="请输入" class="service-express-item"/>
          </el-form-item>
          <el-form-item label="物流公司" prop="company">
            <el-input v-model="dialog.serviceExpress.company"  maxlength="50" placeholder="请输入" class="service-express-item"/>
          </el-form-item>
          <el-form-item label="物流凭证" prop="certificates" style="width:100%">
            <el-upload
              class="upload-demo"
              list-type="picture"
              multiple
              :with-credentials="true"
              action="/upload"
              drag
              :accept="acceptFileType.commonType"
              :before-upload="$validateFileType"
              :show-file-list="true"
              :http-request="(file) => serviceAfterUpload(file, 'serviceExpress')"
              :before-remove="(file, fileList) => serviceBeforeRemove(file, fileList, 'serviceExpress')"
              :on-remove="(file, fileList) => serviceAfterRemove(file, fileList, 'serviceExpress')"
              :file-list="dialog.serviceExpress.certificates">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
          </el-form-item>
        </div>
        <el-form-item label="说明" prop="repairExplain" style="width: 100%">
          <el-input v-model="dialog.serviceExpress.repairExplain"  maxlength="255" placeholder="请输入" class="long-input" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitServiceExpress">提交</el-button>
        <el-button type="primary" @click="show.serviceExpress = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 更新服务进度 - 上门 -->
    <el-dialog title="更新服务进度" center :visible.sync="show.visit" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" class="form-column" :rules="rules.visit" :model="dialog.visit" ref="visit">
        <el-form-item label="服务进度" prop="state">
          <el-select v-model="dialog.visit.state" placeholder="请选择" class="service-express-item" @change="changeVisit">
            <el-option v-for="item in (form.repair && form.repair.state ? option.repairState : option.visitProgressType) " :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="dialog.visit.state === 'DATE'">
          <el-form-item label="预约时间" prop="visitTime" style="width: 100%">
            <el-input v-model="dialog.visit.visitTime" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="服务人员" prop="visitServicePerson">
            <el-input v-model="dialog.visit.visitServicePerson"  maxlength="50" placeholder="请输入" class="service-express-item"/>
          </el-form-item>
          <el-form-item label="服务人员电话" prop="visitServicePhone">
            <el-input v-model="dialog.visit.visitServicePhone"  maxlength="50" placeholder="请输入" class="service-express-item"/>
          </el-form-item>
        </div>
        <div v-if="dialog.visit.state === 'SUCCESS'">
          <el-form-item label="服务签收单" prop="certificates" style="width:100%">
            <el-upload
              class="upload-demo"
              list-type="picture"
              multiple
              :with-credentials="true"
              action="/upload"
              drag
              :accept="acceptFileType.commonType"
              :before-upload="$validateFileType"
              :show-file-list="true"
              :http-request="(file) => serviceAfterUpload(file, 'visit')"
              :before-remove="(file, fileList) => serviceBeforeRemove(file, fileList, 'visit')"
              :on-remove="(file, fileList) => serviceAfterRemove(file, fileList, 'visit')"
              :file-list="dialog.visit.certificates">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
          </el-form-item>
        </div>
        <el-form-item label="说明" prop="visitExplain" style="width: 100%">
          <el-input v-model="dialog.visit.visitExplain"  maxlength="255" placeholder="请输入" class="long-input" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitVisit">提交</el-button>
        <el-button type="primary" @click="show.visit = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 更新服务进度 - 远程指导 -->
    <el-dialog title="更新服务进度" center :visible.sync="show.remote" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" class="form-column" :rules="rules.remote" :model="dialog.remote" ref="remote">
        <el-form-item label="服务进度" prop="state">
          <el-select v-model="dialog.remote.state" placeholder="请选择" class="service-express-item" @change="changeRemote">
            <el-option v-for="item in option.remoteProgressType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <div v-if="dialog.remote.state === 'DATE'">
          <el-form-item label="服务人员" prop="servicePerson">
            <el-input v-model="dialog.remote.servicePerson"  maxlength="50" placeholder="请输入" class="service-express-item"/>
          </el-form-item>
          <el-form-item label="服务人员电话" prop="servicePhone">
            <el-input v-model="dialog.remote.servicePhone"  maxlength="50" placeholder="请输入" class="service-express-item"/>
          </el-form-item>
          <el-form-item label="预约时间" prop="appointmentTime" style="width: 100%">
            <el-input v-model="dialog.remote.appointmentTime" placeholder="请输入"></el-input>
          </el-form-item>
          <el-form-item label="安装视频/手册" prop="certificates" style="width: 100%">
            <el-upload
              class="upload-demo"
              list-type="picture"
              multiple
              :with-credentials="true"
              action="/upload"
              drag
              :accept="acceptFileType.commonType"
              :before-upload="$validateFileType"
              :show-file-list="true"
              :http-request="(file) => serviceAfterUpload(file, 'remote')"
              :before-remove="(file, fileList) => serviceBeforeRemove(file, fileList, 'remote')"
              :on-remove="(file, fileList) => serviceAfterRemove(file, fileList, 'remote')"
              :file-list="dialog.remote.certificates">
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
          </el-form-item>
          <el-form-item label="说明" prop="appointmentNote" style="width: 100%">
          <el-input v-model="dialog.remote.appointmentNote"  maxlength="255" placeholder="请输入" class="long-input" type="textarea"></el-input>
        </el-form-item>
        </div>
        <div v-if="dialog.remote.state === 'SUCCESS'">
          <el-form-item label="说明" prop="finishNote" style="width: 100%">
            <el-input v-model="dialog.remote.finishNote"  maxlength="255" placeholder="请输入" class="long-input" type="textarea"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRemote">提交</el-button>
        <el-button type="primary" @click="show.remote = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 更新服务进度 - 服务需付费 -->
    <el-dialog title="创建售后物料" center :visible.sync="show.needPay" @open="openNeedPayDialog" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" class="form-column" :model="dialog.needPay" :rules="rules.needPay" ref="needPay" style="flex: 0 1 auto;">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="维修服务物料" prop="supplementSku" style="width: 60%">
              <RemoteSKU :data.sync="dialog.needPay.supplementSku" @change="changeSupplementSku" size="medium" style="width: 300px" />
            </el-form-item>
            <el-form-item style="width: 20%">
              <el-button type="primary" @click="addSupplementSku">添加</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-table :data="supplementSkuTableData" style="width: 700px; margin: 0 auto 20px auto; text-align: center;" align="center">
          <el-table-column prop="supplementSku" label="SKU"></el-table-column>
          <el-table-column prop="supplementSkuDesc" label="物料描述" width="250px"></el-table-column>
          <el-table-column prop="supplementSkuQuantity" label="数量">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.supplementSkuQuantity"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="operation" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="deleteSupplementSku(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-form-item label="上传报价单" prop="certificates" style="width:100%">
          <el-upload
            class="upload-demo"
            list-type="picture"
            multiple
            :with-credentials="true"
            action="/upload"
            drag
            :accept="acceptFileType.commonType"
            :before-upload="$validateFileType"
            :show-file-list="true"
            :http-request="(file) => serviceAfterUpload(file, 'needPay')"
            :before-remove="(file, fileList) => serviceBeforeRemove(file, fileList, 'needPay')"
            :on-remove="(file, fileList) => serviceAfterRemove(file, fileList, 'needPay')"
            :file-list="dialog.needPay.certificates">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
        </el-form-item>
        <el-form-item label="说明" prop="supplementExplain" style="width: 100%">
          <el-input v-model="dialog.needPay.supplementExplain"  maxlength="255" placeholder="请输入" class="long-input" type="textarea"></el-input>
        </el-form-item>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitNeedPay">提交</el-button>
        <el-button type="primary" @click="show.needPay = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 客户已下单 - 填写销售订单 -->
    <el-dialog title="填写销售订单" center :visible.sync="show.addSO" @open="openAddSODialog" width="900px" custom-class="dialog-aftersale">
      <el-form :inline="true" :model="dialog.addSO" ref="addSO" style="flex: 0 1 auto;">
        <el-row :gutter="24">
          <el-col :span="24">
            <el-form-item label="销售订单" prop="omsNo" style="width: 60%">
              <el-input v-model="dialog.addSO.omsNo" clearable size="medium" style="width: 300px" />
            </el-form-item>
            <el-form-item style="width: 20%">
              <el-button type="primary" @click="addOrder" :loading="load.addOrderLoading">添加</el-button>
            </el-form-item>
          </el-col>
        </el-row>
        <el-table :data="orderTableData" style="width: 700px; margin: 0 auto 20px auto; text-align: center;" align="center">
          <el-table-column prop="omsNo" label="销售订单号"></el-table-column>
          <el-table-column prop="operation" label="操作">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="deleteOrder(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAddSO" :loading="load.submitOrderLoading">提交</el-button>
        <el-button type="primary" @click="show.addSO = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 确认服务完成 -->
    <el-dialog title="确认申请信息" center :visible.sync="show.confirmServiceComplete" width="900px" custom-class="dialog-aftersale">
      <h3 class="inner-text">已与客户确认服务完成？</h3>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitServiceComplete">确认</el-button>
        <el-button type="primary" @click="show.confirmServiceComplete = false">取消</el-button>
      </p>
    </el-dialog>

    <!-- 拒绝付费 -->
    <el-dialog title="确认申请信息" center :visible.sync="show.rejectPay" width="900px" custom-class="dialog-aftersale">
      <h3 class="inner-text">拒绝付费意味着：由于服务费用未达成一致，已与客户确认取消该售后服务，</h3>
      <h3 class="inner-text">若该服务为返厂服务，售后终止后厂家将按照售后申请单中的客户地址将货物寄回。</h3>
      <h3 class="inner-text">（提醒：可通过【临时回复】与运营协商报价事宜，沟通无果后再拒绝）</h3>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitRejectPay">确认</el-button>
        <el-button type="primary" @click="show.rejectPay = false">取消</el-button>
      </p>
    </el-dialog>
  </div>
  </div>
</template>

<script src="./detail.js"></script>

<style lang="less" src="./style.less"></style>
