<template>
  <div class="module module-aftersales-judge" v-show="show">

    <el-form :model="form" :rules="rules" ref="judge" :class="{'judge-info': info}">
      <div class="form-group">
        <h3 class="present" v-if="info">{{ map.type[form.decide.type] }}</h3>
        <!-- <h3 v-else>填写判定结果</h3> -->
        <div class="form-flex-box">
          <el-row style="width:100%">
            <el-col :span="8">
              <el-form-item label="售后类型" prop="decide.type" v-if="!info" style="width: 100%">
                <el-select v-model="form.decide.type" placeholder="请选择" @change="afterSelectType">
                  <el-option v-for="item in option.type.filter(item => item.value !== 'SERVICE')" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item  v-if="info && form.decide.oaStaffNickName" label="客服：" prop="order.staffNickName">
                {{form.decide.oaStaffNickName}}
              </el-form-item>
              <el-form-item label="客服" prop="order.staffNickName" v-if="!info && (isGOODS || isREPLACE || isREFUND || isINVOICE)" style="width: 100%">
                <AfterSaleStaff :value="form.order.staffNickName" @change="(val,value) => handleStaffChange('staff', val, value)" placeholder="请输入"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="销售：" prop="order.saleNickName" v-if="info && form.decide.oaSaleNickName">
                {{form.decide.oaSaleNickName}}
              </el-form-item>
              <el-form-item label="销售" prop="order.saleNickName" v-if="!info && (isGOODS || isREPLACE)" style="width: 100%">
                <span slot="label">
                  <el-tooltip class="item" effect="dark" placement="top"
                    content="由于OA要求销售工号必须存在，所以当销售为合伙人时，请选择销售经理；当销售为震坤行时，请选择客服。">
                    <i class="el-icon-warning"><span style="line-height: 32px">销售</span></i>
                  </el-tooltip>
                </span>
                <AfterSaleStaff :value="form.order.saleNickName" @change="(val,value) => handleStaffChange('sale', val, value)" placeholder="请输入"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="售后说明" prop="decide.description" style="width: 100%;margin-top:20px">
            <p v-if="info" style="width:100%;word-break:break-all;">: {{ form.decide.description || '未填写' }}</p>
            <el-input v-model="form.decide.description" type="textarea" :rows="5" style="width:500px" v-else placeholder="请输入"  maxlength="255"></el-input>
          </el-form-item>
          <el-form-item label="退票" prop="decide.needInvoice" v-if="isGOODS || isREPLACE">
            <el-radio v-model="form.decide.needInvoice" :label="true" :disabled="info">需要</el-radio>
            <el-radio v-model="form.decide.needInvoice" :label="false" :disabled="info">不需要</el-radio>
          </el-form-item>
          <el-form-item label="退款" prop="decide.needRefund" v-if="isGOODS || isREPLACE || isCANCEL">
            <el-radio v-model="form.decide.needRefund" :label="true" :disabled="info">需要</el-radio>
            <el-radio v-model="form.decide.needRefund" :label="false" :disabled="info">不需要</el-radio>
          </el-form-item>
        </div>
      </div>

      <div key="form.decide.goods" class="form-group" v-if="isGOODS || isREPLACE">
        <h3>退货信息</h3>
        <div class="form-flex-box">
          <el-form-item label="退货责任方" prop="goods.aspect">
            <p v-if="info"> : {{ form.goods.aspect === '1' ? '客户责任' : '我方责任' }}</p>
            <template v-else>
              <el-radio v-model="form.goods.aspect" label="1" :disabled="info">客户责任</el-radio>
              <el-radio v-model="form.goods.aspect" label="0" :disabled="info">我方责任</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="退货金额" prop="goods.amount">
            <p v-if="info"> : {{ form.goods.amount }}</p>
            <el-input-number v-else v-model="form.goods.amount" :disabled="info" placeholder="请输入" :precision="2" :min="0" :controls="false"  maxlength="50"></el-input-number>
          </el-form-item>
          <el-form-item label="物料组" prop="goods.oaGroupId">
            <span slot="label">
              <el-tooltip class="item" effect="dark" placement="top" content="航利退货需选择“航利”为物料组，其余选正常物料组即可">
                <span><i class="el-icon-warning"></i>物料组</span>
              </el-tooltip>
            </span>
            <p v-if="info"> : {{ form.goods.oaGroupName }}</p>
            <el-select v-else v-model="form.goods.oaGroupId" placeholder="请选择" :disabled="info" filterable @change="selectMaterialGroup">
              <el-option v-for="item in option.materialGroup" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="退货方式" prop="goods.way">
            <p v-if="info"> : {{ form.goods.way === 'SELF' ? '自行退回' : '上门取件' }}</p>
            <template v-else>
              <el-radio v-model="form.goods.way" label="PARCEL" :disabled="info">上门取件</el-radio>
              <el-radio v-model="form.goods.way" label="SELF" :disabled="info">自行退回</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="退货类别" prop="goods.type" v-if="form.goods.way === 'SELF'">
            <p v-if="info"> : {{ form.goods.type === 'WAREHOUSE' ? '仓库' : '供应商' }}</p>
            <template v-else>
              <el-radio v-model="form.goods.type" :disabled="info" label="WAREHOUSE">仓库</el-radio>
              <el-radio v-model="form.goods.type" :disabled="info" label="SUPPLIER" @change="afterChangeGooodsType">供应商</el-radio>
            </template>
          </el-form-item>
          <!-- TODO -->
          <el-form-item label="退货仓库" prop="goods.warehouse" v-if="form.goods.type === 'WAREHOUSE'">
            <p v-if="info"> : {{ form.goods.warehouse }}</p>
            <template v-else>
              <ware-house v-model="form.goods.warehouse" @select="data => afterSelectWareHouse(data, 'goods')"></ware-house>
            </template>
          </el-form-item>
          <el-form-item label="SKU" prop="goods.sku">
            <p v-if="info" style="word-break: break-all;"> : {{ form.goods.sku }}</p>
            <el-input v-else v-model="form.goods.sku" :disabled="info" placeholder="请输入" maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="客户取件联系人" prop="goods.receiveContact" v-if="form.goods.way === 'PARCEL'">
            <p v-if="info"> : {{ form.goods.receiveContact }}</p>
            <el-input v-else v-model="form.goods.receiveContact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="客户取件电话" prop="goods.receivePhone" v-if="form.goods.way === 'PARCEL'">
            <p v-if="info"> : {{ form.goods.receivePhone }}</p>
            <el-input v-else v-model="form.goods.receivePhone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="客户取件地址" prop="goods.receiveAddress" v-if="form.goods.way === 'PARCEL'">
            <p v-if="info"> : {{ form.goods.receiveAddress }}</p>
            <el-input v-else v-model="form.goods.receiveAddress" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>

          <el-form-item label="退货联系人" prop="goods.goodsContact" v-if="form.goods.way === 'SELF'">
            <p v-if="info"> : {{ form.goods.goodsContact }}</p>
            <el-input v-else v-model="form.goods.goodsContact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="退货电话" prop="goods.phone" v-if="form.goods.way === 'SELF'">
            <p v-if="info"> : {{ form.goods.phone }}</p>
            <el-input v-else v-model="form.goods.phone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="退货地址" prop="goods.address" v-if="form.goods.way === 'SELF'">
            <p v-if="info"> : {{ form.goods.address }}</p>
            <el-input v-else v-model="form.goods.address" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>
          <el-form-item label="退货原因" prop="goods.reason" style="width:100%" key="goods.reason">
            <p v-if="info"> : {{ form.goods.reason }}</p>
            <el-input v-else v-model="form.goods.reason" type="textarea" :rows="5" :disabled="info" placeholder="请输入"  maxlength="255"></el-input>
          </el-form-item>
          <el-form-item label="退货明细附件" prop="goods.attachment" style="width:100%">
            <preview v-if="info" :data="form.certificates.GOODS"></preview>
            <el-upload v-if="!info" class="btn-upload"
              list-type="picture-card"
              :accept="acceptFileType.commonType"
              :with-credentials="true"
              :action="uploadUrl"
              :before-upload="$validateFileType"
              :on-remove="(file,fileList)=>afterRemove(file,fileList,'GOODS')"
              :on-success="(res,file,fileList)=>afterUpload(res,file,fileList,'GOODS')"
              :on-error="(res,file,fileList)=>afterUpload(res,file,fileList,'GOODS')"
              :file-list="form.certificates.filter(cer => cer.type === 'GOODS')"
              :limit="5" :on-exceed="handleExceed" name="file">
              <el-button :loading="importLoading" size="small" type="primary">上传附件</el-button>
            </el-upload>
          </el-form-item>
        </div>
      </div>

      <div key="form.decide.replace" class="form-group" v-if="isREPLACE">
        <h3>换货信息</h3>
        <div class="form-flex-box">
          <el-form-item label="客户收货联系人" prop="replace.contact">
            <p v-if="info"> : {{ form.replace.contact }}</p>
            <el-input v-else v-model="form.replace.contact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="客户收货电话" prop="replace.phone">
            <p v-if="info"> : {{ form.replace.phone }}</p>
            <el-input v-else v-model="form.replace.phone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="客户收货地址" prop="replace.address">
            <p v-if="info"> : {{ form.replace.address }}</p>
            <el-input v-else v-model="form.replace.address" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>
        </div>
      </div>

      <div key="form.decide.invoice" class="form-group" v-if="form.decide.needInvoice || isINVOICE">
        <h3>退票信息</h3>
        <div class="form-flex-box">
          <el-form-item label="退票方式" prop="invoice.way">
            <p v-if="info"> : {{ form.invoice.way === 'SELF' ? '自行退回' : '上门取件' }}</p>
            <template v-else>
              <el-radio v-model="form.invoice.way" :disabled="info" label="PARCEL">上门取件</el-radio>
              <el-radio v-model="form.invoice.way" :disabled="info" label="SELF">自行退回</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="补开发票" prop="invoice.needReInvoice">
            <p v-if="info"> : {{ formateBoolean(form.invoice.needReInvoice) }}</p>
            <template v-else>
              <el-radio v-model="form.invoice.needReInvoice" :disabled="info" :label="1">需要</el-radio>
              <el-radio v-model="form.invoice.needReInvoice" :disabled="info" :label="0">不需要</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="客户取件联系人" prop="invoice.receiveContact" v-if="form.invoice.way === 'PARCEL'">
            <p v-if="info"> : {{ form.invoice.receiveContact }}</p>
            <el-input v-else v-model="form.invoice.receiveContact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="客户取件电话" prop="invoice.receivePhone" v-if="form.invoice.way === 'PARCEL'">
            <p v-if="info"> : {{ form.invoice.receivePhone }}</p>
            <el-input v-else v-model="form.invoice.receivePhone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="客户取件地址" prop="invoice.receiveAddress" v-if="form.invoice.way === 'PARCEL'">
            <p v-if="info"> : {{ form.invoice.receiveAddress }}</p>
            <el-input v-else v-model="form.invoice.receiveAddress" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>
          <el-form-item label="退票联系人" prop="invoice.contact" v-if="form.invoice.way === 'SELF'">
            <p v-if="info"> : {{ form.invoice.contact }}</p>
            <el-input v-else v-model="form.invoice.contact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="退票电话" prop="invoice.phone" v-if="form.invoice.way === 'SELF'">
            <p v-if="info"> : {{ form.invoice.phone }}</p>
            <el-input v-else v-model="form.invoice.phone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="退票地址" prop="invoice.address" v-if="form.invoice.way === 'SELF'">
            <p v-if="info"> : {{ form.invoice.address }}</p>
            <el-input v-else v-model="form.invoice.address" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>
          <template v-if="!(isGOODS || isREPLACE) || (info && form.invoice.draftNo)">
            <el-form-item label="销售方名称" prop="invoice.companyId">
              <p v-if="info"> : {{ map.company[form.invoice.companyId] }}</p>
              <el-select v-else v-model="form.invoice.companyId" filterable placeholder="请选择" :disabled="info">
                <el-option v-for="item in option.company" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发票抬头名称" prop="invoice.invoiceTitle">
              <p v-if="info"> : {{ form.invoice.invoiceTitle }}</p>
              <el-input v-else v-model="form.invoice.invoiceTitle" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
            </el-form-item>
            <el-form-item label="发票类型" prop="invoice.invoiceType">
              <p v-if="info"> : {{ map.invoiceType[form.invoice.invoiceType] }}</p>
              <el-select v-else v-model="form.invoice.invoiceType" placeholder="请选择" :disabled="info">
                <el-option v-for="item in option.invoiceType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发票开具日期" prop="invoice.invoiceDate">
              <p v-if="info"> : {{ form.invoice.invoiceDate && form.invoice.invoiceDate.replace(/T.+/, '') }}</p>
              <el-date-picker v-else v-model="form.invoice.invoiceDate" type="date" placeholder="选择日期" :disabled="info" @change="afterChangeDate"></el-date-picker>
            </el-form-item>
            <el-form-item label="金税发票号码" prop="invoice.invoiceNum">
              <p v-if="info"> : {{ form.invoice.invoiceNum }}</p>
              <el-input v-else v-model="form.invoice.invoiceNum" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
            </el-form-item>
            <el-form-item label="未税金额" prop="invoice.unsolved">
              <p v-if="info"> : {{ form.invoice.unsolved }}</p>
              <el-input-number v-else v-model="form.invoice.unsolved" :disabled="info" placeholder="请输入" :precision="2" :min="0" :controls="false" maxlength="80"></el-input-number>
            </el-form-item>
            <el-form-item label="借项/贷项发票草稿号" prop="invoice.draftNo">
              <p v-if="info"> : {{ form.invoice.draftNo }}</p>
              <el-input v-else v-model="form.invoice.draftNo" :disabled="info" placeholder="请输入" maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="原件类型" prop="invoice.originalType">
              <p v-if="info"> : {{ map.originalType[form.invoice.originalType] }}</p>
              <el-select v-else v-model="form.invoice.originalType" placeholder="请选择" :disabled="info">
                <el-option v-for="item in option.originalType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="退票类型" prop="invoice.type">
              <p v-if="info"> : {{ map.returnInvoiceType[form.invoice.type] }}</p>
              <el-select v-else v-model="form.invoice.type" placeholder="请选择" :disabled="info">
                <el-option v-for="item in option.returnInvoiceType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="退票具体原因" prop="invoice.reason" style="width:100%">
              <p v-if="info"> : {{ form.invoice.reason }}</p>
              <el-input v-else v-model="form.invoice.reason" style="width:300px" type="textarea" :rows="5" :disabled="info" placeholder="请输入"  maxlength="255"></el-input>
            </el-form-item>
            <el-form-item></el-form-item>
            <el-form-item label="退票附件" prop="certificates" style="width:100%">
              <preview v-if="info" :data="form.certificates.INVOICE"></preview>
              <el-upload v-else class="upload-demo"
                list-type="picture-card"
                :accept="acceptFileType.commonType"
                :disabled="info"
                :with-credentials="true"
                :before-upload="$validateFileType"
                :on-success="(res,file,fileList)=>afterUpload(res,file,fileList,'INVOICE')"
                :on-error="(res,file,fileList)=>afterUpload(res,file,fileList,'INVOICE')"
                :on-remove="(file,fileList)=>afterRemove(file,fileList,'INVOICE')"
                :action="uploadUrl"
                :limit="5"
                :on-exceed="handleExceed"
                :file-list="form.certificates.filter(cer => cer.type === 'INVOICE')">
                <el-button size="small" type="primary">点击上传</el-button>
                <div slot="tip" class="el-upload__tip">最大15M/个</div>
              </el-upload>
            </el-form-item>
          </template>
        </div>
      </div>
      <div key="form.decide.refund" class="form-group" v-if="(!info && (isREFUND || (isCANCEL && form.decide.needRefund)) || (info && form.refund && form.refund.bank && (form.oaProcess && (!form.oaProcess.refund || form.oaProcess.refund.state !== 'PENDING') )))">
      <!-- <div key="form.decide.refund" class="form-group" v-if="(form.decide.needRefund && !(isGOODS ||isREPLACE)) || isREFUND || (info && form.decide.needRefund && form.refund.bank)"> -->
      <!-- <div key="form.decide.refund" class="form-group" v-if="(form.oaProcess && (!form.oaProcess.refund || form.oaProcess.refund.state !== 'PENDING') )&& ((form.decide.needRefund && !(isGOODS ||isREPLACE)) || isREFUND || (info && form.decide.needRefund && form.refund.bank))"> -->
        <h3>退款信息</h3>
        <div class="form-flex-box">
          <el-form-item label="收款单位" prop="refund.company">
            <p v-if="info"> : {{ form.refund.company }}</p>
            <el-input v-else v-model="form.refund.company" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="客户代码" prop="refund.number">
            <p v-if="info"> : {{ form.refund.number }}</p>
            <el-input v-else v-model="form.refund.number" :disabled="info" placeholder="请输入" maxlength="80"></el-input>
          </el-form-item>
          <el-form-item label="开户行" prop="refund.bank">
            <p v-if="info"> : {{ form.refund.bank }}</p>
            <el-input v-else v-model="form.refund.bank" :disabled="info" placeholder="请输入" maxlength="80"></el-input>
          </el-form-item>
          <el-form-item label="付款方式" prop="refund.way">
            <p v-if="info"> : {{ map.payPlatform[form.refund.way] }}</p>
            <el-select v-else v-model="form.refund.way" placeholder="请选择" :disabled="info">
              <el-option v-for="item in option.payPlatform" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="银行账户" prop="refund.account">
            <p v-if="info"> : {{ form.refund.account }}</p>
            <el-input v-else v-model="form.refund.account" :disabled="info" placeholder="请输入" maxlength="80"></el-input>
          </el-form-item>
          <el-form-item label="原收款日期" prop="refund.oldDate">
            <p v-if="info"> : {{ form.refund.oldDate && form.refund.oldDate.replace(/T.+/, '') }}</p>
            <el-date-picker v-else v-model="form.refund.oldDate" type="date" placeholder="选择日期" :disabled="info" @change="afterChangeDate"></el-date-picker>
          </el-form-item>
          <el-form-item label="退款金额" prop="refund.amount">
            <p v-if="info"> : {{ form.refund.amount }}</p>
            <el-input-number v-else v-model="form.refund.amount" :disabled="info" placeholder="请输入" :precision="2" :min="0" :controls="false" maxlength="80"></el-input-number>
          </el-form-item>
          <el-form-item label="原收款金额" prop="refund.oldAmount">
            <p v-if="info"> : {{ form.refund.oldAmount }}</p>
            <el-input-number v-else v-model="form.refund.oldAmount" :disabled="info" placeholder="请输入" :precision="2" :min="0" :controls="false" maxlength="80"></el-input-number>
          </el-form-item>
          <el-form-item label="公司名称" prop="refund.companyId">
            <p v-if="info"> : {{ map.company[form.refund.companyId] }}</p>
            <el-select v-else v-model="form.refund.companyId" placeholder="请选择" :disabled="info">
              <el-option v-for="item in option.company" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="退款说明" prop="refund.reason">
            <p v-if="info"> : {{ form.refund.reason }}</p>
            <el-input v-else v-model="form.refund.reason" type="textarea" :rows="5" :disabled="info" placeholder="请输入" maxlength="255"></el-input>
          </el-form-item>
        </div>
      </div>

      <div key="form.decide.repair" class="form-group" v-if="isREPAIR">
        <h3>返厂信息</h3>
        <div class="form-flex-box">
          <el-form-item label="返厂方式" prop="repair.way">
            <p v-if="info"> : {{ form.repair.way === 'SELF' ? '自行退回' : '上门取件' }}</p>
            <template v-else>
              <el-radio v-model="form.repair.way" :disabled="info" label="PARCEL">上门取件</el-radio>
              <el-radio v-model="form.repair.way" :disabled="info" label="SELF">自行退回</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="返厂类型" prop="repair.type">
            <p v-if="info"> : {{ form.repair.type === 'SELF' ? '自主维修' : '供应商维修' }}</p>
            <template v-else>
              <el-radio v-model="form.repair.type" :disabled="info" label="SELF">自主维修</el-radio>
              <el-radio v-model="form.repair.type" :disabled="info" label="SUPPLIER">供应商维修</el-radio>
            </template>
          </el-form-item>
          <el-form-item></el-form-item>
          <template v-if="form.repair.way === 'PARCEL'">
            <el-form-item label="客户取件联系人" prop="repair.contact">
              <p v-if="info"> : {{ form.repair.contact }}</p>
              <el-input v-else v-model="form.repair.contact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="客户取件电话" prop="repair.phone">
              <p v-if="info"> : {{ form.repair.phone }}</p>
              <el-input v-else v-model="form.repair.phone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="客户取件地址" prop="repair.address">
              <p v-if="info"> : {{ form.repair.address }}</p>
              <el-input v-else v-model="form.repair.address" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
            </el-form-item>
          </template>
          <template v-if="form.repair.way === 'SELF'">
            <el-form-item label="退货仓库" prop="repair.warehouse">
              <p v-if="info"> : {{ form.repair.warehouse }}</p>
              <template v-else>
                <ware-house v-model="form.repair.warehouse" @select="data => afterSelectWareHouse(data, 'repair')"></ware-house>
              </template>
            </el-form-item>
            <el-form-item label="退货联系人" prop="repair.returnContact">
              <p v-if="info"> : {{ form.repair.returnContact }}</p>
              <el-input v-else v-model="form.repair.returnContact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="退货电话" prop="repair.returnPhone">
              <p v-if="info"> : {{ form.repair.returnPhone }}</p>
              <el-input v-else v-model="form.repair.returnPhone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="退货地址" prop="repair.returnAddress">
              <p v-if="info"> : {{ form.repair.returnAddress }}</p>
              <el-input v-else v-model="form.repair.returnAddress" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
            </el-form-item>
          </template>
          <el-form-item label="客户收货联系人" prop="repair.customerContact">
            <p v-if="info"> : {{ form.repair.customerContact }}</p>
            <el-input v-else v-model="form.repair.customerContact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="客户收货电话" prop="repair.customerPhone">
            <p v-if="info"> : {{ form.repair.customerPhone }}</p>
            <el-input v-else v-model="form.repair.customerPhone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="客户收货地址" prop="repair.customerAddress">
            <p v-if="info"> : {{ form.repair.customerAddress }}</p>
            <el-input v-else v-model="form.repair.customerAddress" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>
        </div>
      </div>

      <div key="form.decide.visit" class="form-group" v-if="isVISIT">
        <h3>上门信息</h3>
        <div class="form-flex-box">
          <el-form-item label="上门类型" prop="visit.type">
            <p v-if="info"> : {{ form.visit.type === 'SELF' ? '自主' : '供应商' }}</p>
            <template v-else>
              <el-radio v-model="form.visit.type" :disabled="info" label="SELF">自主</el-radio>
              <el-radio v-model="form.visit.type" :disabled="info" label="SUPPLIER">供应商</el-radio>
            </template>
          </el-form-item>
          <el-form-item label="上门联系人" prop="visit.contact">
            <p v-if="info"> : {{ form.visit.contact }}</p>
            <el-input v-else v-model="form.visit.contact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="上门联系电话" prop="visit.phone">
            <p v-if="info"> : {{ form.visit.phone }}</p>
            <el-input v-else v-model="form.visit.phone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="上门服务地址" prop="visit.address">
            <p v-if="info"> : {{ form.visit.address }}</p>
            <el-input v-else v-model="form.visit.address" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>
        </div>
      </div>

      <div key="form.decide.detection" class="form-group" v-if="isDETECTION">
        <h3>检测信息</h3>
        <div class="form-flex-box">
          <el-form-item label="退货方式" prop="detection.way">
            <p v-if="info"> : {{ form.detection.way === 'SELF' ? '自行退回' : '上门取件' }}</p>
            <template v-else>
              <el-radio v-model="form.detection.way" :disabled="info" label="PARCEL">上门取件</el-radio>
              <el-radio v-model="form.detection.way" :disabled="info" label="SELF">自行退回</el-radio>
            </template>
          </el-form-item>
          <template  v-if="form.detection.way === 'PARCEL'">
            <el-form-item label="客户取件联系人" prop="detection.contact">
              <p v-if="info"> : {{ form.detection.contact }}</p>
              <el-input v-else v-model="form.detection.contact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="客户取件电话" prop="detection.phone">
              <p v-if="info"> : {{ form.detection.phone }}</p>
              <el-input v-else v-model="form.detection.phone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="客户取件地址" prop="detection.address">
              <p v-if="info"> : {{ form.detection.address }}</p>
              <el-input v-else v-model="form.detection.address" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
            </el-form-item>
          </template>
          <template v-if="form.detection.way === 'SELF'">
            <el-form-item label="退货仓库" prop="detection.warehouse">
              <p v-if="info"> : {{ form.detection.warehouse }}</p>
              <template v-else>
                <ware-house v-model="form.detection.warehouse" @select="item => afterSelectWareHouse(item, 'detection')"></ware-house>
              </template>
            </el-form-item>
            <el-form-item label="退货联系人" prop="detection.returnContact">
              <p v-if="info"> : {{ form.detection.returnContact }}</p>
              <el-input v-else v-model="form.detection.returnContact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
            </el-form-item>
            <el-form-item label="退货电话" prop="detection.returnPhone">
              <p v-if="info"> : {{ form.detection.returnPhone }}</p>
              <el-input v-else v-model="form.detection.returnPhone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
            </el-form-item>
            <el-form-item label="退货地址" prop="detection.returnAddress">
              <p v-if="info"> : {{ form.detection.returnAddress }}</p>
              <el-input v-else v-model="form.detection.returnAddress" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
            </el-form-item>
          </template>
          <el-form-item label="客户收货联系人" prop="detection.customerContact">
            <p v-if="info"> : {{ form.detection.customerContact }}</p>
            <el-input v-else v-model="form.detection.customerContact" :disabled="info" placeholder="请输入"  maxlength="50"></el-input>
          </el-form-item>
          <el-form-item label="客户收货电话" prop="detection.customerPhone">
            <p v-if="info"> : {{ form.detection.customerPhone }}</p>
            <el-input v-else v-model="form.detection.customerPhone" :disabled="info" placeholder="请输入"  maxlength="20"></el-input>
          </el-form-item>
          <el-form-item label="客户收货地址" prop="detection.customerAddress">
            <p v-if="info"> : {{ form.detection.customerAddress }}</p>
            <el-input v-else v-model="form.detection.customerAddress" :disabled="info" placeholder="请输入"  maxlength="80"></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
// TODO materialGroup
import {
  type,
  payPlatform,
  originalType,
  invoiceType,
  company,
  materialGroup,
  getMap,
  returnInvoiceType,
  ruleNumber,
  ruleNumber0
} from './option'
import AfterSaleStaff from '@/components/SearchFields/afterSaleStaff'
import Preview from './components/Preview'
import WareHouse from './components/WareHouse'
import { api } from '@/api/aftersales'

export default {
  data () {
    return {
      importLoading: false,
      option: {
        type,
        payPlatform,
        originalType,
        invoiceType,
        company,
        materialGroup,
        returnInvoiceType
      },
      map: {
        type: getMap(type),
        payPlatform: getMap(payPlatform),
        originalType: getMap(originalType),
        invoiceType: getMap(invoiceType),
        company: getMap(company),
        materialGroup: getMap(materialGroup),
        returnInvoiceType: getMap(returnInvoiceType)
      },
      uploadUrl: '/fe-upload/api/upload/',
      show: true,
      rules: {
        'order.staffNickName': [{ required: true, message: '请输入', trigger: 'blur' }],
        'order.saleNickName': [{ required: true, message: '请输入', trigger: 'blur' }],
        'decide.type': [{ required: true, message: '请选择', trigger: 'blur' }],
        'decide.staffNickName': [{ required: true, message: '请选择', trigger: 'blur' }],
        'decide.saleNickName': [{ required: true, message: '请选择', trigger: 'blur' }],
        // 'decide.description': [{ required: true, message: '请输入', trigger: 'blur' }],
        'decide.needInvoice': [{ required: true, message: '请选择', trigger: 'blur' }],
        'decide.needRefund': [{ required: true, message: '请选择', trigger: 'blur' }],
        'goods.sku': [{ required: true, message: '请输入', trigger: 'blur' }],
        'goods.aspect': [{ required: true, message: '请选择', trigger: 'blur' }],
        'goods.amount': [
          { required: true, message: '请输入', trigger: 'blur' },
          { validator: ruleNumber0, trigger: 'blur' }
        ],
        'goods.oaGroupId': [{ required: true, message: '请选择', trigger: 'blur' }],
        'goods.way': [{ required: true, message: '请选择', trigger: 'blur' }],
        'goods.type': [{ required: true, message: '请选择', trigger: 'blur' }],
        'goods.receiveContact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'goods.receivePhone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'goods.receiveAddress': [{ required: true, message: '请输入', trigger: 'blur' }],
        'goods.goodsContact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'goods.phone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'goods.address': [{ required: true, message: '请输入', trigger: 'blur' }],
        'goods.reason': [{ required: true, message: '请输入', trigger: 'blur' }],
        'replace.contact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'replace.phone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'replace.address': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.way': [{ required: true, message: '请选择', trigger: 'blur' }],
        'invoice.needReInvoice': [{ required: true, message: '请选择', trigger: 'blur' }],
        'invoice.receiveContact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.receivePhone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.receiveAddress': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.contact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.phone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.address': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.companyId': [{ required: true, message: '请选择', trigger: 'blur' }],
        'invoice.invoiceTitle': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.invoiceType': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.invoiceDate': [{ required: true, message: '请选择', trigger: 'blur' }],
        'invoice.invoiceNum': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.unsolved': [
          { required: true, message: '请输入', trigger: 'blur' },
          { validator: ruleNumber, trigger: 'blur' }
        ],
        'invoice.draftNo': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.originalType': [{ required: true, message: '请选择', trigger: 'blur' }],
        'invoice.type': [{ required: true, message: '请选择', trigger: 'blur' }],
        'invoice.reason': [{ required: true, message: '请输入', trigger: 'blur' }],
        'invoice.certificates': [{ required: true, message: '请输入', trigger: 'blur' }],
        'refund.company': [{ required: true, message: '请输入', trigger: 'blur' }],
        'refund.number': [{ required: true, message: '请输入', trigger: 'blur' }],
        'refund.bank': [{ required: true, message: '请输入', trigger: 'blur' }],
        'refund.way': [{ required: true, message: '请选择', trigger: 'blur' }],
        'refund.account': [{ required: true, message: '请输入', trigger: 'blur' }],
        'refund.oldDate': [{ required: true, message: '请输入', trigger: 'blur' }],
        'refund.amount': [
          { required: true, message: '请输入', trigger: 'blur' },
          { validator: ruleNumber0, trigger: 'blur' }
        ],
        'refund.oldAmount': [
          { required: true, message: '请输入', trigger: 'blur' },
          { validator: ruleNumber, trigger: 'blur' }
        ],
        'refund.companyId': [{ required: true, message: '请选择', trigger: 'blur' }],
        'refund.reason': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.way': [{ required: true, message: '请选择', trigger: 'blur' }],
        'repair.type': [{ required: true, message: '请选择', trigger: 'blur' }],
        'repair.contact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.phone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.address': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.returnContact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.returnPhone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.returnAddress': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.customerContact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.customerPhone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'repair.customerAddress': [{ required: true, message: '请输入', trigger: 'blur' }],
        'visit.type': [{ required: true, message: '请选择', trigger: 'blur' }],
        'visit.contact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'visit.phone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'visit.address': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.way': [{ required: true, message: '请选择', trigger: 'blur' }],
        'detection.contact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.phone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.address': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.returnContact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.returnPhone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.returnAddress': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.customerContact': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.customerPhone': [{ required: true, message: '请输入', trigger: 'blur' }],
        'detection.customerAddress': [{ required: true, message: '请输入', trigger: 'blur' }],
        'certificates': [{ required: true, message: '请上传', trigger: 'blur' }]
      }
    }
  },
  computed: {
    acceptFileType () {
      return this.$store.state.orderCommon.acceptFileType || {}
    },
    form () {
      return this.propsData || {
        apply: {},
        certificates: [],
        decide: {},
        goods: {},
        refund: {},
        replace: {},
        invoice: {},
        detection: {},
        visit: {},
        repair: {}
      }
    },
    type () {
      return this.form.decide.type
    },
    isCANCEL () {
      return this.type === 'CANCEL'
    },
    isGOODS () {
      return this.type === 'GOODS'
    },
    isREPLACE () {
      return this.type === 'REPLACE'
    },
    isREFUND () {
      return this.type === 'REFUND'
    },
    isINVOICE () {
      return this.type === 'INVOICE'
    },
    isREPAIR () {
      return this.type === 'REPAIR'
    },
    isVISIT () {
      return this.type === 'VISIT'
    },
    isDETECTION () {
      return this.type === 'DETECTION'
    }
  },
  components: {
    Preview,
    AfterSaleStaff,
    WareHouse
  },
  props: {
    propsData: Object,
    info: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    data (val) {
      this.form = val
    }
  },
  mounted () {
    console.log(this.propsData, this.info)
    if (this.info) {
      for (let key in this.rules) {
        this.rules[key].forEach(item => {
          item.required = false
        })
      }
    }
    try {
      this.form.goods.sku = this.form.goods.orderDetails.map(order => order.sapNo).join(' ')
    } catch (err) {}
    this.form.certificates.INVOICE = this.form.certificates.INVOICE ? this.form.certificates.INVOICE : []
    this.form.certificates.GOODS = this.form.certificates.GOODS ? this.form.certificates.GOODS : []
    console.log(this.form)
    this.getMaterialGroup()
  },
  methods: {
    getMaterialGroup () {
      api({
        url: '/ticket/material/group',
        method: 'PUT',
        complete: res => {
          console.log(res)
          if (res.code === 200) {
            this.option.materialGroup = res.data.map(item => {
              return {
                label: item.productGroupName,
                value: item.productGroupId
              }
            })
            this.map.materialGroup = getMap(this.option.materialGroup)
          }
        }
      })
    },
    formateBoolean (res) {
      if (res === true || res === 'true') {
        return '是'
      } else if (res === false || res === 'false') {
        return '否'
      }
      return res
    },
    handleStaffChange (type, val, value) {
      console.log(type, val, value)
      console.log(this.form.order)
      if (type === 'staff') {
        if (!value) {
          this.form.order.staffEmail = ''
          this.form.order.staffId = ''
          this.form.order.staffNickName = ''
          this.form.order.staffWorkCode = ''
          return
        }
        this.form.order.staffEmail = value.email
        this.form.order.staffId = value.userId
        this.form.order.staffNickName = value.nickName
        this.form.order.staffWorkCode = value.workCode
      }
      if (type === 'sale') {
        if (!value) {
          this.form.order.saleId = ''
          this.form.order.saleNickName = ''
          this.form.order.saleWorkCode = ''
          return
        }
        this.form.order.saleId = value.userId
        this.form.order.saleNickName = value.nickName
        this.form.order.saleWorkCode = value.workCode
      }
    },
    beforeUpload (file) {
      this.importLoading = true
    },
    onUploadSuccess (response) {
      this.importLoading = false
      console.log(response)
      if (response && response.status === 200) {
        this.$message.success(response.message || '导入成功！')
      } else {
        this.$message.error((response && response.message) || '导入失败！')
      }
    },
    onUploadError (error) {
      console.log(error)
      this.importLoading = false
      this.$message.error((error && error.msg) || (error && error.message) || '上传失败')
    },
    afterSelectType () {
      this.form.decide.needInvoice = false
      this.form.decide.needRefund = false
    },
    selectMaterialGroup (id) {
      const item = this.map.materialGroup[id]
      this.form.goods.oaGroupName = item
    },
    handleExceed () {
      this.$message.error('最多上传5个附件！')
    },
    afterUpload (res, file, fileList, type) {
      this.importLoading = false
      console.log(res, file, fileList)
      const lists = fileList.map(item => {
        const link = item.response ? item.response && item.response.data && item.response.data.link : item.url
        return { name: item.name, type, url: link }
      })
      const otherList = this.form.certificates.filter(cert => cert.type !== type)
      this.form.certificates = otherList.concat(lists)
    },
    afterRemove (file, fileList, type) {
      let list = fileList
      this.form.certificates = this.form.certificates.filter(cert => cert.type !== type)
      this.form.certificates = this.form.certificates.concat(list)
    },
    afterExceed () {
      this.$message.error('超出文件数量限制')
    },
    afterChangeDate (date) {
      console.log(date)
    },
    afterSelectWareHouse (item, type) {
      // this.form[type].returnAddress = item.address1
      if (!item) return
      let contact = 'returnContact'
      let phone = 'returnPhone'
      let address = 'returnAddress'

      if (type === 'goods') {
        contact = 'goodsContact'
        phone = 'phone'
        address = 'address'
      }

      this.form[type][contact] = item.contact2
      this.form[type][phone] = item.contact2Tel1
      this.form[type][address] = item.address4
    },
    afterChangeGooodsType (type) {
      // reset
      if (type === 'SUPPLIER') {
        this.form.goods.goodsContact = ''
        this.form.goods.phone = ''
        this.form.goods.address = ''
      }
      this.$forceUpdate()
      console.log(type)
    }
  }
}
</script>

<style lang="less">
.module-aftersales-judge {
  .el-input-number{
    width: 120px;
    input {
      width: 100%;
    }
  }
  .judge-info {
    padding: 20px;
    .el-form-item__label{
      padding: 0 6px 0 0;
      font-weight: bold;
      color: #333;
    }
    .el-form-item__content{
      flex: 1;
      p{
        width: 100%;
        word-break: break-all;
      }
    }
    .form-group {
      h3 {
        font-size: 16px;
        color: #506fd6;
      }
      .present {
        border-bottom: 3px solid #ddd;
        font-size: 18px;
        color: #506fd6;
      }
    }
  }
  .present{
    font-weight: bold;
  }
  .el-input__inner {
    width: auto;
  }
  .form-group{
    margin-bottom: 20px;
    h3{
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }
    .form-flex-box {
      display: flex;
      flex-flow: wrap;
      .el-form-item{
        display: flex;
        width: 33.33333333%;
        margin-bottom: 12px;
      }
      @media screen and (max-width: 1280px){
        .el-form-item{
          width: 50%;
        }
      }
    }
  }
}

</style>
