<template>
  <div class="page page-aftersales-list">
    <div class="module-filter">
      <div class="filter-group filters">
        <el-form :inline="true">
          <el-form-item label="售后申请单号">
            <el-input v-model="filter.ticketNo" placeholder="请输入售后单号，最多支持10个，以空格分隔" clearable></el-input>
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="filter.orderNo" placeholder="支持SAP、OMS、外围、客户订单号" clearable></el-input>
          </el-form-item>
          <el-form-item label="工单号">
            <el-input v-model="filter.workOrderNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="客户名称">
            <Client v-model="filter.customerNo" placeholder="请输入客户名称或编号" />
            <!-- <el-input v-model="filter.customerNo" placeholder="请输入客户名称或编号" clearable></el-input> -->
          </el-form-item>
          <el-form-item label="申请类型">
            <el-select v-model="filter.createTypeList" placeholder="请选择" clearable multiple>
              <el-option v-for="item in option.type" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="售后类型">
            <el-select v-model="filter.typeList" placeholder="请选择" clearable multiple>
              <el-option v-for="item in option.afterSaleType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <!-- <div v-show="show.hiddenFilter"> -->
            <el-form-item label="客服" prop="staffId">
              <!-- <el-input v-model="filter.staff" placeholder="请输入" clearable></el-input> -->
              <selectSecurityUser getLabel v-model="filter.staff" :username="filter.staff" />
            </el-form-item>
            <el-form-item label="申请渠道">
              <el-select v-model="filter.sourceList" placeholder="请选择" clearable multiple>
                <el-option v-for="item in option.source" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="售后状态">
            <el-select v-model="filter.stateList" placeholder="请选择" clearable multiple>
              <el-option v-for="item in option.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
            <el-form-item label="SKU">
              <el-input v-model="filter.sku" placeholder="请输入" clearable></el-input>
            </el-form-item>
            <el-form-item label="售后子状态">
            <el-select v-model="filter.subStateName" placeholder="请选择" clearable filterable>
              <el-option v-for="item in option.subStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="采购员" prop="buyerId">
            <SelectSecurityUser v-model="filter.buyerId" />
          </el-form-item>
          <el-row>
            <el-form-item label="申请日期" style="width:auto">
              <el-date-picker v-model="dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onDatePick"></el-date-picker>
            </el-form-item>
            <el-form-item style="width:auto; margin-left:10px">
              <el-button type="primary" icon="el-icon-search" @click="bindFilterSearch">查询</el-button><el-button @click="reset">重置</el-button>
              <!-- <div class="btn-toggle" @click="toggle" :class="{on: show.hiddenFilter}">
                <i class="el-icon-caret-top"></i>
              </div> -->
              <el-form-item label="待我处理" style="width:auto">
                <el-switch v-model="filter.todo"></el-switch>
              </el-form-item>
            </el-form-item>
          <!-- </div> -->
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="module-list">
      <div class="module-list-header">
        <p>售后申请单列表</p>
        <div>
          <el-button v-if="isShowFinanceExportButton" @click="ticketExportVisible = true">财务退票数据导出</el-button>
          <el-button v-if="buttonInfos.list_export" @click="exportAfterSales">导出</el-button>
          <!-- <el-button type="primary" @click="applyEmptyAfterSale">新增</el-button> -->
          <el-popover v-if="buttonInfos.list_create" placement="bottom" v-model="isShowCreateButton">
          <div class="align-center">
            <div v-for="item in option.createType" :key="item.value" style="text-align: center" :class="item.value !== 'GOODS' ? 'mt10' : ''" >
              <el-button type="primary" @click="handleClick(item.value)" style="width:100px"> {{ item.label }}</el-button>
            </div>
          </div>
          <el-button type="primary" slot="reference" style="margin-left: 5px">创建售后申请</el-button>
        </el-popover>
        </div>
      </div>
      <el-table :data="list" class="boss-table" v-loading="loading.list" max-height="500">
        <!-- <el-table-column show-overflow-tooltip label="操作" fixed="left">
          <template slot-scope="scope">
            <router-link :to="{ path: `/after-sales/detail/${scope.row.id}` }">查看详情</router-link>
          </template>
        </el-table-column> -->
        <el-table-column show-overflow-tooltip prop="ticketNo" label="售后单号" width="160px" fixed="left">
          <template slot-scope="scope">
            <el-link v-loading="loading.btn" @click="viewDetail(scope.row)">{{ scope.row.ticketNo}}</el-link>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="omsNo" label="订单号" width="200px" fixed="left">
          <template slot-scope="{ row }">
            <MultiLineTooltip
              :title="row.omsNo || ''"
              :lineList="getOrderNoTooltip(row)"
              :click="handleRouter"
              :copy="handleCopy"
            />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="customerName" label="客户名称" width="160px"/>
        <el-table-column show-overflow-tooltip prop="typeName" label="售后类型" fixed="left"/>
        <el-table-column show-overflow-tooltip prop="createTypeName" label="申请类型"/>
        <!-- <el-table-column show-overflow-tooltip prop="createTypeName" label="申请原因"/> -->
        <el-table-column show-overflow-tooltip prop="stateName" label="售后状态" />
        <el-table-column show-overflow-tooltip prop="subStateName" label="售后子状态" width="90px" />
        <el-table-column show-overflow-tooltip prop="sku" label="SKU" />
        <el-table-column show-overflow-tooltip prop="workOrderNo" label="工单" width="120px" />
        <el-table-column show-overflow-tooltip prop="staff" label="客服" />
        <!-- <el-table-column show-overflow-tooltip prop="staff" label="销售" /> -->
        <el-table-column show-overflow-tooltip prop="creator" label="创建人" />
        <el-table-column show-overflow-tooltip prop="customerContact" label="客户申请人" />
        <el-table-column show-overflow-tooltip prop="createdTime" label="申请时间" width="140px">
          <template slot-scope="scope">
            <span>{{ scope.row.createdTime ? moment(scope.row.createdTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
            <!-- <span>{{ formatTime(scope.row.createdTime) }}</span> -->
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip prop="source" label="申请渠道"></el-table-column>
      </el-table>
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.current"
        :page-sizes="[10, 20]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <el-dialog title="退票数据导出" :visible.sync="ticketExportVisible" width="600px" center destroy-on-close>
      <el-form :model="ticketExportForm" ref="ticketExportForm">
        <el-form-item label="退票流程发起时间范围" prop="ticketExportTime" label-width="170px" :rules="[{required: true, message: '请选择时间范围', trigger: 'change'}]">
          <el-date-picker
            v-model="ticketExportForm.ticketExportTime"
            :picker-options="pickerOptions"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="ticketExportSubmit">确认</el-button>
        <el-button @click="ticketExportVisible = false">取消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { api, exportBasisApi } from '@/api/aftersales'
import { afterSaleType, type, status, source, getMap, createType } from './option'
import Client from '@/components/SearchFields/client'
import SelectSecurityUser from '@/components/SearchFields/selectSecurityUser'
import MultiLineTooltip from '@/components/MultiLineTooltip'
import clip from '@/utils/clipboard'
import { getSearchDataFromUrlQuery } from '@/utils/index.js'
import * as shortid from 'shortid'
import moment from 'moment'
export default {
  components: {
    Client,
    SelectSecurityUser,
    MultiLineTooltip
  },
  data () {
    return {
      moment,
      loading: {
        list: false,
        btn: false
      },
      show: {
        hiddenFilter: false
      },
      dateRange: [],
      filter: {
        current: 1,
        pageSize: 10,
        sort: 'id,desc',
        ticketNo: '',
        orderNo: '',
        workOrderNo: '',
        createTypeList: [],
        customerNo: '',
        staff: '',
        staffId: '',
        buyerId: '',
        typeList: [],
        stateList: [],
        sourceList: [],
        createdTime: '',
        createdTimeTo: '',
        sku: '',
        todo: true,
        subStateName: ''
      },
      option: {
        source,
        type,
        status,
        afterSaleType,
        createType,
        subStatus: []
      },
      map: {
        type: getMap(type),
        status: getMap(status)
      },
      list: [],
      total: 0,
      isShowCreateButton: false,
      buttonInfos: {},
      isShowFinanceExportButton: false,
      ticketExportVisible: false,
      ticketExportForm: {
        ticketExportTime: []
      },
      pickerMinDate: '',
      pickerOptions: {
        onPick: (obj) => {
          this.pickerMinDate = new Date(obj.minDate).getTime()
        }
        // disabledDate: (time) => {
        //   const day1 = 1 * 24 * 3600 * 1000 // 一天的毫秒数
        //   if (this.pickerMinDate) {
        //     // 选中一个最小日期后，最大选择范围是31天（前后31天且不能大于当前日期）
        //     let maxTime = this.pickerMinDate + day1 * 30
        //     let minTime = this.pickerMinDate - day1 * 30
        //     return time.getTime() >= maxTime || time.getTime() <= minTime || time.getTime() >= moment().endOf('day').valueOf()
        //   } else {
        //     return time.getTime() >= moment().endOf('day').valueOf()
        //   }
        // }
      }
    }
  },
  methods: {
    jumpToDetail (row) {
      console.log(row)
      const { omsNo, sapNo, id } = row
      this.$router.jumpToSoOrderDetail({
        tagName: `${sapNo || ''}订单`,
        query: { soNo: omsNo, sapOrderNo: sapNo, id, refresh: true }
      })
    },
    string2Array (str) {
      let ret = []
      try { ret = str.split(/,/) } catch (err) {}
      return ret
    },
    applyEmptyAfterSale () {
      console.log('新增售后')
      this.$router.push({
        path: `/after-sales/form/${Math.random().toString(16).slice(2)}?type=createEmpty`
      })
    },
    handleClick (type) {
      this.isShowCreateButton = false;
      const isPro = window.CUR_DATA.env === 'pro'
      switch (type) {
        case 'CANCEL':
        case 'SERVICE':
          this.$router.push({
            path: `/after-sales/form/${Math.random().toString(16).slice(2)}?type=createEmpty`,
            query: {
              createType: type
            }
          })
          break;
        case 'GOODS':
          window.open('/as/return/create');
          break;
        case 'REPLACE':
          window.open('/as/replace/create');
          break;
        case 'REFUND':
          window.open('/as/refund/create');
          break;
        case 'INVOICE':
          window.open('/as/invoice/create');
          break;
        case 'CONFIRM':
          // this.$router.push({
          //     path: '/workflow/create/0?essence=0&categoryIdAfSales=618'
          //   })
          window.open(`/wf/create/0?essence=0&categoryIdAfSales=${isPro ? 618 : 960}&creatSource=28`);
          break;
      }
    },
    viewDetail (row) {
      this.loading.btn = true;
      api({
        url: '/ticket',
        query: { ticketNo: row.ticketNo },
        complete: (res) => {
          if (res.code === 200 && res.data.data) {
            const resType = res.data?.data[0]?.type || res.data?.data[0]?.createType;
            // const isPro = window.CUR_DATA.env === 'pro'
            switch (resType) {
              case 'SERVICE':
              case 'VISIT':
              case 'REMOTE':
              case 'REPAIR':
              case 'REISSUE':
                this.$router.push({
                  path: `/after-sales/detail/${row.id}`
                })
                break;
              case 'GOODS':
              case 'REPLACE':
              case 'INVOICE':
              case 'REFUND':
              case 'CANCEL':
              case 'OMS_GOODS':
                const type = {
                  GOODS: 'return',
                  REPLACE: 'replace',
                  INVOICE: 'invoice',
                  REFUND: 'refund',
                  CANCEL: 'cancel',
                  OMS_GOODS: 'omsReturn'
                }
                if (row.ticketTableStoreType) {
                  if (row.ticketTableStoreType === 'OLD') {
                    this.$router.push({
                      path: `/after-sales/detail/${row.id}`
                    })
                  } else {
                    window.open(`/as/${type[resType]}/detail/${row.id}`);
                  }
                } else {
                  this.$message.error('缺少必要参数')
                }
            }
          }
          this.loading.btn = false;
        }
      })
    },
    exportAfterSales() {
      this.loading.list = true
      exportBasisApi(this.filter)
        .catch((err) => {
          this.$message.error(err.msg || err.message || '下载失败！')
        })
        .finally(() => {
          this.loading.list = false
        })
      // downloadFile('/api-sales/ticket/exportAfterSalesData', this.filter)
      //     return downloadFile(`${prefix}/stockup/stock/audit/history/export`, data, {
      //   method: 'POST'
      // })
    },
    formatTime (dateString) {
      const t = new Date(dateString)
      const date = dateString.split('T')[0]
      const time = t.toTimeString().split(' ')[0]
      return `${date} ${time}`
    },
    toggle () {
      this.show.hiddenFilter = !this.show.hiddenFilter
    },
    reset () {
      for (const key in this.filter) {
        this.filter[key] = ''
      }
      this.dateRange = []
      this.filter.sort = 'id,desc'
      this.filter.current = 1
      this.filter.pageSize = 10
    },
    onDatePick (date) {
      const [ min, max ] = date
      // console.log(this.filter, date)
      this.filter.createdTime = min
      this.filter.createdTimeTo = max
    },
    bindFilterSearch () {
      this.filter.current = 1
      this.search()
    },
    search () {
      this.loading.list = true
      this.filter.ticketNo = this.filter.ticketNo.replace(/[\s|,|，|;|；|、]+/g, ' ')
      api({
        url: '/ticket',
        query: { ...this.filter },
        complete: (res) => {
          console.log(res.data)
          if (res.code === 200 && res.data.data) {
            this.list = res.data.data
            this.total = res.data.totalCount
          }
          this.loading.list = false
        }
      })
    },
    showListButton () {
      api({
        url: '/ticket/common/listButtonInfos',
        complete: (res) => {
          this.buttonInfos = res?.data
        }
      })
    },
    // 财务退票导出按钮权限
    showFinanceExportButton () {
      api({
        url: '/standard/ticket/register/authority',
        complete: (res) => {
          this.isShowFinanceExportButton = res?.data
        }
      })
    },
    // 获取子状态枚举
    getSubStateOptions () {
      api({
        url: '/ticket/common/listSubState',
        complete: (res) => {
          if (res.code === 200 && res.data) {
            this.option.subStatus = res.data.map(item => ({ label: item, value: item }))
          }
        }
      })
    },
    // 退票导出
    ticketExportSubmit () {
      this.$refs.ticketExportForm.validate((valid) => {
        if (valid) {
          api({
            url: '/standard/ticket/export',
            method: 'post',
            data: {
              startTime: this.ticketExportForm.ticketExportTime && this.ticketExportForm.ticketExportTime[0],
              endTime: this.ticketExportForm.ticketExportTime && this.ticketExportForm.ticketExportTime[1]
            },
            complete: (res) => {
              if (res.code === 200) {
                this.$message.success(res?.data?.msg || '导出成功')
              } else {
                this.$message.error(res?.message || res?.msg || '导出失败')
              }
              this.ticketExportVisible = false
            }
          })
        }
      })
    },
    getOrderNoTooltip (item) {
      return item ? [
        { title: 'OMS订单号', children: item.omsNo ? item.omsNo.split(',') : [''], type: 'oms', row: item },
        { title: 'SAP订单号', children: item.sapNo ? item.sapNo.split(',') : [''], type: 'sap', row: item },
        { title: '客户订单号', children: item.customerOrderNo ? item.customerOrderNo.split(',') : [''] },
        { title: '外围订单号', children: item.bizNo ? item.bizNo.split(',') : [''] }
      ] : []
    },
    handleCopy (no) {
      clip(no, event, () => {
        const content = '复制成功'
        this.$message({
          message: content,
          type: 'success'
        })
      })
    },
    handleRouter (type, no, row) {
      const soNo = type === 'oms' ? no : ''
      const sapOrderNo = type === 'sap' ? no : ''
      const soNoArr = row?.omsNo?.split(',') || []
      const sapOrderNoArr = row?.sapNo?.split(',') || []
      const soNoIndex = soNoArr.findIndex(item => item === no)
      const sapOrderNoIndex = sapOrderNoArr.findIndex(item => item === no)
      this.$router.jumpToSoOrderDetail({
        query: {
          soNo: soNo || (sapOrderNoIndex !== -1 && soNoArr[sapOrderNoIndex]) || '',
          sapOrderNo: sapOrderNo || (soNoIndex !== -1 && sapOrderNoArr[soNoIndex]) || '',
          id: shortid.generate(),
          refresh: true
        }
      })
    },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.current = res
      this.search()
    }
  },
  mounted () {
    const query = JSON.parse(localStorage.getItem('/after-sales/list')) || {}
    // 使用初始查询条件查询一次后需要清除该初始条件
    // setItem 参见 router/index.js > beforeEach
    localStorage.removeItem('/after-sales/list')
    this.filter = {
      ...this.filter,
      ...getSearchDataFromUrlQuery(query)
    }
    this.search()
    this.showListButton()
    this.showFinanceExportButton()
    this.getSubStateOptions()
  }
}
</script>

<style lang="less">
.page-aftersales-list {
  padding: 30px;
  .el-form {
    .el-form-item__label {
      width: 100px;
    }
    input.el-input__inner{
      width: 300px;
    }
  }
  .filters {
    flex: 1;
  }
  .group-btns{
    // width: 130px;
    display: flex;
    justify-content: flex-end;
    align-items: flex-start;
  }
  .btn-toggle{
    width: 32px;
    height: 32px;
    line-height: 32px;
    color: #fff;
    border-radius: 32px;
    text-align: center;
    background-color: #597bee;
    margin: 10px 0 0;
    transform: rotate(-180deg);
    transition: transform .3s ease;
    &.on{
      transform: rotate(0deg);
    }
  }
  .module-filter {
    margin-bottom: 20px;
    display: flex;
    .el-form--inline .el-form-item {
      margin-right: 0;
      width: 33%;
    }
    @media screen and (max-width: 1280px){
      .el-form--inline .el-form-item {
        width: 50%;
      }
    }
  }
  .module-list {
    &-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .el-table {
      width: 100%;
      margin-bottom: 20px;
    }
    a{
      color: #597bee;
    }
  }
  .el-pagination {
    text-align: right;
  }
  .colorful{
    color:#597bee;
  }
  .linkable{
    cursor: pointer;
  }
  .align-center {
  text-align: center;
}
.mt10 {
  margin-top: 10px;
}
}
</style>
