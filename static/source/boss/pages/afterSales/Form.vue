<template>
  <div class="page page-aftersales-application-form" v-loading="pageLoading">
    <OrderDetail :customerNo="customerNo" :showOrderDetail="showOrderDetail" @addOrder="addOrder" @close-dialog="showOrderDetail = false" />
    <div class="module-application-form">
      <!-- <h3 class="block-title">售后申请单</h3> -->
      <el-form :inline="true" :model="form.apply" :rules="rules" ref="form">
      <div class="block">
        <h3>申请类型</h3>
        <el-form-item label="申请类型" prop="createType" class="pad pad-24" style="margin-top: 20px; margin-bottom: 0px;">
          <el-select clearable v-model="form.apply.createType" placeholder="请选择" @change="afterSelectType" disabled>
            <el-option v-for="item in option.type" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="block">
        <h3>客户信息</h3>
        <div class="customer">
          <span class="label">客户</span>
          <CustomerSelect :disabled="disableCustomer" ref="customerSelect" style="width: 300px;" placeholder="请输入客户编号或客户名称"
            :value="defaultValue" @change="afterSelectCustomer" class="module-CustomerSelect"></CustomerSelect>
          客户编码：{{customerNo}}
        </div>
      </div>
      <div class="block">
        <h3>订单信息
          <el-button v-if="!this.disableOrder" @click="openOrderDetailDialog" type="primary">添加明细</el-button>
        </h3>
        <OrderInfo :tableList="tableList" @changeTableList="changeTableList" :applyData="form"></OrderInfo>
      </div>
      <div class="block">
        <h3>售后申请</h3>
        <div class="form-info">
          <!-- <el-form :inline="true" :model="form.apply" :rules="rules" ref="form">
            <el-form-item label="申请类型" prop="createType" class="pad pad-24 no-border-top">
              <el-select clearable v-model="form.apply.createType" placeholder="请选择" @change="afterSelectType">
                <el-option v-for="item in option.type" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item> -->
            <el-form-item label="申请原因" prop="reason" class="pad pad-12">
              <el-select v-model="form.apply.reason" placeholder="请选择">
                <el-option v-for="item in option.reason[form.apply.createType]" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item v-if="isSERVICE" label="客户期望服务时间" prop="expectedTime" class="pad pad-12">
                <el-date-picker v-model="form.apply.expectedTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择日期"></el-date-picker>
              </el-form-item>
            <div>
              <!-- <el-form-item label="申请数量" prop="quantity" class="pad pad-12" >
                <el-input-number v-model="form.apply.quantity" :min="0" :max="form.order.quantity" @change="afterQuantity" :controls="false"></el-input-number>
              </el-form-item>
              <el-form-item label="申请金额" prop="money" class="pad pad-12" >
                <el-input-number v-model="form.apply.money" :max="form.order.taxTotalPrice || 99999999" :min="0" :precision="6" :controls="false"></el-input-number>
              </el-form-item> -->
              <el-form-item v-if="isCANCEL || isGOODS || isREPLACE || isREFUND || isINVOICE" label="申请总额" prop="money" class="pad pad-12" >
                <el-input-number v-model="form.apply.money" :max="maxTotalMoney" :min="0" :precision="6" :controls="false"></el-input-number>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="客户申请人" prop="customerContact" class="pad pad-12">
                <el-input v-model="form.apply.customerContact" maxlength="50"></el-input>
              </el-form-item>
              <el-form-item label="客户手机号" prop="customerPhone" class="pad pad-12">
                <el-input v-model="form.apply.customerPhone" maxlength="20"></el-input>
              </el-form-item>
            </div>
            <div v-if="isGOODS || isREPLACE || isREPAIR || isVISIT || isDETECTION || isSERVICE">
              <el-form-item :label="isSERVICE ? '客户服务联系人' : '客户收货联系人'" prop="contact" class="pad pad-12">
                <ContactSelect ref="contactRef" v-model="contact" :title="isSERVICE ? '客户服务联系人' : '客户收货联系人'" @change="handleChangeContact" />
              </el-form-item>
              <el-form-item :label="isSERVICE ? '客户服务收货电话' : '客户收货电话'" prop="phone" class="pad pad-12">
                <el-input v-model="form.apply.phone" maxlength="20" disabled></el-input>
              </el-form-item>
              <el-form-item :label="isSERVICE ? '客户服务收货地址' : '客户收货地址'" prop="address" class="pad pad-24">
                <el-input v-model="form.apply.address" maxlength="80" class="input-address" disabled></el-input>
              </el-form-item>
            </div>
            <el-form-item label="问题描述" prop="question" class="pad pad-24">
              <el-input v-model="form.apply.question" type="textarea" :rows="8" maxlength="255" style="width:720px;" placeholder="请输入内容"></el-input>
            </el-form-item>
            <el-form-item label="上传凭证" prop="" class="pad pad-24">
              <!-- <el-upload class="upload-demo" drag :with-credentials="true" :limit="5" list-type="picture"
                :on-exceed="afterExceed"
                :on-preview="handlePictureCardPreview"
                :before-upload="handleBeforeUpload"
                :action="uploadUrl" :on-remove="(file,fileList)=>afterRemove(file,fileList,'TICKET')"
                :on-progress="onUploading" :before-remove="beforeRemove"
                :on-success="(res,file,fileList)=>afterUpload(res,file,fileList,'TICKET')"
                :on-error="(res,file,fileList)=>afterUpload(res,file,fileList,'TICKET')"
                :file-list="form.certificates.filter(cer => cer.type === 'TICKET')">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <div class="el-upload__tip" slot="tip">每次只限传1个文件，多个附件请分多次上次, 最多可上传5个文件</div>
              </el-upload> -->
              <el-upload
                class="upload-demo"
                list-type="picture"
                :accept="acceptFileType.commonType"
                :with-credentials="true"
                :on-exceed="afterExceed"
                multiple
                :before-upload="$validateFileType"
                :on-preview="handlePictureCardPreview"
                :before-remove="serviceBeforeRemove"
                action="/upload"
                drag
                :show-file-list="true"
                :http-request="(file) => serviceAfterUpload(file, 'TICKET')"
                :on-remove="(file, fileList) => serviceAfterRemove(file, fileList, 'TICKET')"
                :file-list="form.certificates.filter(cer => cer.type === 'TICKET')">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <!-- <div class="el-upload__tip" slot="tip">单次最多可上传5个文件</div> -->
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="">
              </el-dialog>
            </el-form-item>
        </div>
        <!-- </el-form> -->
      </div>
      </el-form>
      <div class="module-btns center">
        <el-button type="danger" @click="submit" :loading="requesting">提交</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { api, getWorkOrder } from '@/api/aftersales'
// import { api as bossApi } from '@/api/boss'
import { type, reason } from './option'
import OrderInfo from './components/OrderInfo'
import OrderDetail from './components/OrderDetail'
import ContactSelect from './components/ContactSelect'
import CustomerSelect from '@/components/SearchFields/client'
import { toFixedByRadix } from '@/utils/price'
import { upload } from '@/utils/upload'
import { remove } from 'lodash'
export default {
  data () {
    return {
      pageLoading: false,
      tableList: [],
      showOrderDetail: false,
      option: {
        type,
        reason
      },
      requesting: false,
      customerNo: '',
      customerName: '',
      customerContact: null,
      contact: null,
      form: {
        apply: {
          address: '',
          contact: '',
          createType: this.$route.query.createType,
          money: 0,
          operator: '',
          phone: '',
          quantity: 0,
          question: '',
          reason: '',
          customerContact: '',
          customerPhone: '',
          ticketWay: 'AGENCY', // AGENCY|INDEPENDENT
          source: 'BOSS',
          expectedTime: ''
        },
        certificates: [],
        order: {},
        serviceTicket: {}
      },
      dialogVisible: false,
      dialogImageUrl: '',
      rules: {
        // totalMoney: [{ required: true, message: '请选择', trigger: 'blur' }],
        createType: [{ required: true, message: '请选择', trigger: 'blur' }],
        reason: [{ required: true, message: '请选择', trigger: 'blur' }],
        quantity: [{ required: true, message: '请选择', trigger: 'blur' }],
        money: [{ required: true, message: '请选择', trigger: 'blur' }],
        customerContact: [{ required: true, message: '请输入', trigger: 'blur' }],
        customerPhone: [{ required: true, message: '请输入', trigger: 'blur' }],
        // 注：不是合同，后端拼错
        contact: [{ required: true, message: '请输入', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入', trigger: 'blur' }],
        address: [{ required: true, message: '请输入', trigger: 'blur' }],
        question: [
          { required: true, message: '请输入', trigger: 'blur' },
          { max: 255, message: '长度在 255 个字符内', trigger: 'blur' }
        ],
        expectedTime: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      uploadUrl: '/fe-upload/api/upload/'
    }
  },
  components: {
    OrderInfo,
    OrderDetail,
    CustomerSelect,
    ContactSelect
  },
  computed: {
    acceptFileType () {
      return this.$store.state.orderCommon.acceptFileType || {}
    },
    disableCustomer () {
      return this.$route.query.type !== 'createEmpty'
    },
    disableOrder () {
      return this.$route.query.type === 'workflow' || this.form.serviceTicket?.serviceType === 'TIE_IN_SALE'
    },
    queryType () {
      return this.$route.query.type
    },
    maxTotalMoney () {
      let max = 0
      if (this.tableList.length) {
        max = this.tableList.map(row => row.applyPrice).reduce((x, y) => x + y)
        max = toFixedByRadix(max, 6)
      }
      return max
    },
    defaultValue () {
      let customerNo = this.customerNo
      let _customerName = this._customerName
      if (_customerName) {
        return _customerName
      } else {
        return customerNo
      }
    },
    type () {
      return this.form.apply.createType
    },
    isCANCEL () {
      return this.type === 'CANCEL'
    },
    isGOODS () {
      return this.type === 'GOODS'
    },
    isREPLACE () {
      return this.type === 'REPLACE'
    },
    isREFUND () {
      return this.type === 'REFUND'
    },
    isINVOICE () {
      return this.type === 'INVOICE'
    },
    isREPAIR () {
      return this.type === 'REPAIR'
    },
    isVISIT () {
      return this.type === 'VISIT'
    },
    isDETECTION () {
      return this.type === 'DETECTION'
    },
    isSERVICE () {
      return this.type === 'SERVICE'
    }
  },
  methods: {
    handleChangeContact (val) {
      this.form.contactId = val?.contactId;
      this.form.apply.contact = val?.contactName;
      this.form.apply.phone = val?.contactPhone;
      this.form.apply.address = val?.address;
      this.form.apply.province = val?.provinceName;
      this.form.apply.city = val?.cityName;
      this.form.apply.region = val?.regionName;
    },
    handleBeforeUpload(file) {
      console.log(file);
      if ((file.size / 1024 / 1024) > 1) {
        this.$message.error('文件大小不得超过100M');
        return false;
      }
    },
    calcTotalMoney () {
      if (this.tableList.length) {
        this.form.apply.money = this.tableList.map(row => row.applyPrice).reduce((x, y) => x + y)
      } else { this.form.apply.money = 0 }
    },
    setDefaultValue () {
      const [ data ] = this.tableList
      this.form.apply.customerContact = data.orderContactName
      this.form.apply.customerPhone = data.orderContactPhone
      this.$refs.contactRef.setContact(
        data.receiverName,
        this.customerNo,
        data.receiverContact,
        data.receiverPhone,
        `${data.receiverCity}${data.receiverDistrict}${data.receiverAddress}`,
      );
      // this.form.apply.address = `${data.receiverCity}${data.receiverDistrict}${data.receiverAddress}`
      // this.form.apply.contact = data.receiverName
      // this.form.apply.phone = data.receiverPhone
    },
    addOrder (list) {
      console.log(list)
      const tmpTableList = this.tableList.map(li => li.sapNo + li.sapItemNo)
      const tmpList = list.filter(li => {
        if (tmpTableList.some(sapSku => sapSku === li.sapNo + li.sapItemNo)) { return false }
        return true
      })
      this.tableList = [...this.tableList, ...tmpList]
      this.calcTotalMoney()
      this.setDefaultValue()
    },
    changeTableList (list) {
      this.tableList = list
      this.calcTotalMoney()
    },
    openOrderDetailDialog () {
      this.showOrderDetail = true
    },
    afterSelectCustomer (customerNo, customer) {
      this._customerName = ''
      if (!customerNo) {
        this.customerNo = ''
        this.customerName = ''
      } else {
        this.customerNo = customer.customerNumber
        this.customerName = customer.customerName
      }
      this.resetInfo()
    },
    resetInfo () {
      this.tableList = [];
      this.customerContact = null;
      this.contact = null;
      this.form.apply = {
        address: '',
        contact: '',
        createType: this.$route.query.createType,
        money: 0,
        operator: '',
        phone: '',
        quantity: 0,
        question: '',
        reason: '',
        customerContact: '',
        customerPhone: '',
        ticketWay: 'AGENCY', // AGENCY|INDEPENDENT
        source: 'BOSS'
      }
    },
    afterSelectType () {
      this.form.apply.reason = ''
    },
    handleRemove (file) {
      const lists = this.form.certificates
      const idx = lists.findIndex(item => item.uid === file.uid)
      this.form.certificates.splice(idx, 1)
      console.log(file, this.form.certificates)
    },
    handlePictureCardPreview (file) {
      if (/\.(jpe?g|png|gif|bmp)/.test(file.url)) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      } else {
        window.open(file.url)
      }
    },
    onUploading () {
      this.isUploading = true
    },
    afterUpload (res, file, fileList, type) {
      this.isUploading = false
      console.log(res, file, fileList)
      const lists = fileList.map(item => {
        const link = item.response ? item.response && item.response.data && item.response.data.link : item.url
        return { name: item.name, type, url: link }
      })
      console.log(lists)
      const otherList = this.form.certificates.filter(cert => cert.type !== type)
      this.form.certificates = otherList.concat(lists)
    },
    afterExceed (...args) {
      this.$message.error('超出文件数量限制')
    },
    beforeRemove () {
      if (this.isUploading) return false
    },
    afterRemove (file, fileList, type) {
      let list = fileList
      this.form.certificates = this.form.certificates.filter(cert => cert.type !== type)
      this.form.certificates = this.form.certificates.concat(list)
    },
    serviceBeforeRemove (file) {
      const lists = this.form.certificates
      const idx = lists.findIndex(item => item.uid === file.uid)
      this.form.certificates.splice(idx, 1)
      return false
    },
    async serviceAfterUpload (file, type) {
      file.onError = () => {
        const lists = this.form.certificates
        const idx = lists.findIndex(item => item.uid === file.uid)
        this.form.certificates.splice(idx, 1)
      }
      this.form.certificates.push({
        uid: file.file.uid,
        name: file.file.name,
        status: 'ready',
        type
      })
      const response = await upload('aftersales/certificates/create', file.file);
      if (response) {
        this.form.certificates.filter(item => {
          if (item.uid === file.file.uid) {
            item.url = response?.url
          }
        })
      } else {
        const lists = this.form.certificates
        const idx = lists.findIndex(item => item.uid === file.uid)
        this.form.certificates.splice(idx, 1)
        // this.$message.error('上传失败')
      }
    },
    serviceAfterRemove (file, fileList, type) {
      remove(this.form.certificates, function (item) {
        return item.uid === file.uid
      });
    },
    submit () {
      if (this.requesting) return
      if (this.form.certificates.filter(item => item.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      this.$refs.form.validate((valid) => {
        if (!valid) {
          this.$message.error('请检查表单必填项！')
        }
        if (valid) {
          if (!this.customerNo) return this.$message.error('请输入客户信息！')
          if (!this.tableList.length) return this.$message.error('请添加订单信息！')
          if (this.tableList.some(order => (order.applyQuantity === undefined || order.applyQuantity === null || order.applyQuantity === 0))) return this.$message.error('行申请数量必填且不能为零！')
          this.form.order.customerContact = this.form.apply.customerContact
          this.form.order.customerPhone = this.form.apply.customerPhone
          this.form.order.customerNo = this.customerNo
          this.form.order.customerName = this.customerName
          this.form.expectedTime = this.form.apply.expectedTime
          if (this.$route.query.type === 'workflow') {
            this.form.workOrderNo = this.$route.params?.id
            this.form.workOrderItemNoList = this.$route.query?.itemIds
          }
          const isCreate = /create|workflow/i.test(this.$route.query.type)
          const url = isCreate ? '/ticket/batch/ticket' : `/ticket/${this.$route.params.id}`
          const method = isCreate ? 'post' : 'put'
          this.requesting = true
          const data = {
            ...this.form,
            orderList: this.tableList.map((order, index) => ({
              ...order,
              sequenceNo: index + 1
            }))
          }
          api({
            url,
            method,
            data,
            complete: (res) => {
              if (res.code === 200) {
                this.$message.success('申请成功')
                setTimeout(() => {
                  this.$closeTag(this.$route.path)
                  this.$router.replace({
                    path: `/after-sales/detail/${res.data.id}`
                  })
                }, 1000)
              } else {
                this.$message.error(res.message || '请求失败')
              }
              this.requesting = false
            }
          })
        } else {
          return false
        }
      })
    },
    cancel () {
      this.$confirm('编辑信息将丢失，请确认是否取消', '提示', { type: 'warning' })
        .then(() => {
          this.$closeTag(this.$route.path)
        })
    },
    getUpdateInfo (id) {
      this.pageLoading = true
      api({
        url: `/ticket/${id}`,
        complete: (res) => {
          this.pageLoading = false
          if (res.code === 200) {
            const { order = {}, orderDetails, apply, certificates = {}, workOrderNo, workOrderItemNoList, serviceTicket } = res.data
            console.log(order)
            this.form.serviceTicket = serviceTicket;
            this.form.apply = Object.assign(this.form.apply, apply)
            this.form.apply.contact = ''
            if (order) {
              this.form.apply.customerContact = order.customerContact
              this.form.apply.customerPhone = order.customerPhone
              this.customerNo = order.customerNo
              this.customerName = order.customerName
              this._customerName = order.customerName
              if (serviceTicket.contactId) {
                setTimeout(() => {
                  this.$refs.contactRef.setContact(
                    apply.contact,
                    this.customerNo,
                    serviceTicket.contactId,
                    apply.phone,
                    apply.address,
                );
                }, 100);
              }
            }
            this.tableList = orderDetails.map(row => {
              return {
                ...row,
                applyQuantity: row.applyQuantity || row.orderQuantity,
                applyPrice: toFixedByRadix(row.applyQuantity * row.taxIncludeUnitPrice, 6)
              }
            })
            console.log(this.tableList)
            this.calcTotalMoney()
            this.form.certificates = certificates.TICKET || []
            this.form.apply.expectedTime = res.data?.serviceTicket?.expectedTime
            this.form.workOrderNo = workOrderNo
            this.form.workOrderItemNoList = workOrderItemNoList
          } else {
            this.$message.error('API：获取数据失败')
          }
        }
      })
    },
    initCreateOrder (data) {
      this.pageLoading = true
      api({
        url: '/ticket/batch/order/item/list',
        method: 'POST',
        data,
        complete: (res) => {
          console.log(res)
          this.pageLoading = false
          if (res && res.data) {
            this.tableList = res.data.map(row => ({
              ...row,
              applyQuantity: row.applyQuantity || row.orderQuantity,
              applyPrice: toFixedByRadix(row.orderQuantity * row.taxIncludeUnitPrice, 6)
            }))
            this.calcTotalMoney()
          }
        }
      })
    },
    parseParamArray (param) {
      let ret = (param && param.split && param.split(',')) || []
      return ret
    },
    constructParam (customerNo, sku, sapItemNos, sapOrderNos) {
      let ret = []
      try {
        const skuList = this.parseParamArray(sku)
        const sapItemNo = this.parseParamArray(sapItemNos)
        const sapOrderNoList = this.parseParamArray(sapOrderNos)
        skuList.forEach((sku, index) => {
          ret.push({ customerNo, sku, sapItemNo: sapItemNo[index], sapNo: sapOrderNoList[index] })
        })
      } catch (err) { console.log(err) }
      return ret
    },
    initWorkflow (workOrderNo) {
      getWorkOrder(workOrderNo).then(res => {
        if (res?.code === 0 && res?.data) {
          api({
            url: '/ticket/order/list',
            method: 'POST',
            data: {
              // sku: res.data.sku,
              sapNo: res.data.referOrderNo,
              customerNo: res.data.clientCode
            },
            complete: (list) => {
              if (list && list.data) {
                const itemIds = this.$route.query?.itemIds.split(',')
                this.tableList = list?.data.filter(row => itemIds.includes(row.omsItemNo) || itemIds.includes(row.sapItemNo)).map(row => {
                  const selectedRow = res?.data?.workOrderItem.find(item => item.lineNumber === row.omsItemNo || item.lineNumber === row.sapItemNo)
                  return {
                    ...row,
                    applyQuantity: selectedRow.count,
                    workOrderCount: selectedRow.count,
                    workOrderItemNo: selectedRow.lineNumber,
                    applyPrice: toFixedByRadix(row.orderQuantity * row.taxIncludeUnitPrice, 6)
                  }
                })
                const [order] = this.tableList
                this.customerNo = order.customerNo
                this.customerName = order.customerName
                this._customerName = order.customerName
                this.setDefaultValue()
              } else {
                this.tableList = []
              }
            }
          })
          } else {
            this.$message.error('售后申请单初始化失败，请稍后重试')
          }
        })
    }
  },
  mounted () {
    const sku = this.$route.params.id
    const { type } = this.$route.query
    if (type === 'create') {
      const { createType, customerNo, customerName, sapItemNos, sapOrderNos, rc, rn, rp, ra, oc, op, province, city, region } = this.$route.query
      this.form.apply.createType = createType
      this.customerNo = customerNo
      this.customerName = customerName
      this._customerName = customerName
      const data = this.constructParam(customerNo, sku, sapItemNos, sapOrderNos)
      this.initCreateOrder(data)
      // this.form.apply.address = ra
      // this.form.apply.contact = rn
      // this.form.apply.phone = rp
      this.$refs.contactRef.setContact(
        rn,
        this.customerNo,
        rc,
        rp,
        ra,
      );
      this.form.apply.customerContact = oc
      this.form.apply.customerPhone = op
      this.form.apply.province = province
      this.form.apply.city = city
      this.form.apply.region = region
    }
    if (type === 'createEmpty') {
      console.log('新建售后申请单')
    }
    if (type === 'update') {
      this.getUpdateInfo(sku)
    }
    // 工单跳转服务类售后
    if (type === 'workflow') {
      this.form.apply.createType = 'SERVICE'
      this.initWorkflow(sku)
    }
    console.log(this.customerName)
  }
}
</script>
<style lang="less" src="@/style/component.less"></style>
<style lang="less">
.module-application-form{
  border-radius: 5px;
  margin: 30px;
  .upload-demo{
    .el-upload-list__item {
      transition: none;
    }
    .el-upload-list--picture-card .el-upload-list__item-thumbnail {
      width: auto;
    }
    .card-preview-p {
      height: 100%;
      padding: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      word-break: break-all;
    }
  }
  .customer{
    margin: 20px;
    span.label{
      margin-right: 20px;
      &::before{
        content: '*';
        color: red;
      }
    }
  }
  .block-title{
    font-size: 24px;
    text-align: center;
    color: #ff7268;
    padding: 30px 0;
  }
  .block{
    margin-bottom: 20px;
    h3{
      padding: 12px;
      background-color: #d7f1f9;
      display: flex;
      justify-content: space-between;
    }
  }
  .form-info{
    padding: 10px;
    border: 1px solid #ddd;
    .el-form-item__label{
      width: 135px;
    }
    .el-input__inner {
      width: 240px;
      text-align: left;
    }
    .input-address{
      .el-input__inner {
        width: 720px;
      }
    }
    .pad{
      padding: 10px;
      margin: 0;
    }
    .pad-24{
      width: 100%;
    }
    .pad-12{
      width: 50%;
    }
  }
}
</style>
