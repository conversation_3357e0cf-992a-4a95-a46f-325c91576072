export const guide = [
  '当前售后申请已被取消',
  '当前售后申请已被驳回',
  '您已同意处理该售后申请单，售后类型：【@售后类型】',
  '因涉及退货，需提交至OA审批',
  '因涉及退票，需提交至OA审批',
  '因涉及退款，需提交至OA审批',
  '待买家退回商品，如买家已退，更新物流信息，请操作',
  '请您创建销售退货订单，如已创建，请填写销售退货订单号',
  '请您创建销售换货订单，如已创建，请填写销售换货订单号',
  '待买家退票，如果已退，更新物流信息，请操作',
  '如果退票已经处理完成，请操作',
  '您已同意售后申请，请创建工单，协同相关人员进行服务吧',
  '厂家已维修完成，并已寄出，请跟进客户收货进度，如果客户已收货，请操作',
  '您已同意取消订单，请进行订单拦截并取消该商品行，如已取消，请操作',
  '当前售后申请已经处理完毕'
]

export const afterSaleType = [
  { label: '取消订单', value: 'CANCEL' },
  { label: '退货', value: 'GOODS' },
  { label: '换货', value: 'REPLACE' },
  { label: '仅退款', value: 'REFUND' },
  { label: '仅退票', value: 'INVOICE' },
  { label: '返厂服务', value: 'REPAIR' },
  { label: '上门服务', value: 'VISIT' },
  { label: '补发', value: 'REISSUE' },
  { label: '订单变更', value: 'OMS_GOODS' },
  { label: '远程指导', value: 'REMOTE' }
  // { label: '检测', value: 'DETECTION' },
  // { label: '服务类', value: 'SERVICE' }
]
export const type = [
  { label: '取消订单', value: 'CANCEL' },
  { label: '退货', value: 'GOODS' },
  { label: '换货', value: 'REPLACE' },
  { label: '仅退款', value: 'REFUND' },
  { label: '仅退票', value: 'INVOICE' },
  // { label: '返厂维修', value: 'REPAIR' },
  // { label: '上门服务', value: 'VISIT' },
  // { label: '检测', value: 'DETECTION' },
  { label: '服务类', value: 'SERVICE' },
  { label: '订单变更', value: 'OMS_GOODS' }
]
export const createType = [
  { label: '退货', value: 'GOODS' },
  { label: '换货', value: 'REPLACE' },
  { label: '仅退款', value: 'REFUND' },
  { label: '仅退票', value: 'INVOICE' },
  { label: '服务类', value: 'SERVICE' },
  { label: '确认售后类型', value: 'CONFIRM' }
]

export const oaStatus = [
  { label: '待提交', value: 'PENDING' },
  { label: '审批中', value: 'APPROVING' },
  { label: '通过', value: 'SUCCESS' },
  { label: '不通过', value: 'FAILED' }
]

export const status = [
  { label: '待审核', value: 'PENDING' },
  { label: '已驳回', value: 'REFUSED' },
  { label: '已取消', value: 'CANCEL' },
  { label: '审核中', value: 'APPROVING' },
  { label: '售后中', value: 'PROCESSING' },
  { label: '已作废', value: 'OBSOLETE' },
  { label: '已完成', value: 'SUCCESS' }
]

export const repairStatus = [
  { label: '待创建工单', value: 'CREATING' },
  { label: '待派工', value: 'PENDING' },
  { label: '已派工', value: 'WORKING' },
  { label: '厂家已收货', value: 'RECEIVED' },
  { label: '厂家已寄回', value: 'SENDOFF' },
  { label: '买家已收货', value: 'SUCCESS' }
]
export const visitStatus = [
  { label: '待创建工单', value: 'CREATING' },
  { label: '待派工', value: 'PENDING' },
  { label: '已派工', value: 'WORKING' },
  { label: '已预约', value: 'DATE' },
  { label: '已完成', value: 'SUCCESS' }
]
export const replaceStatus = [
  { label: '待发货', value: 'PENDING' },
  { label: '派送中', value: 'DELIVERING' },
  { label: '已派送', value: 'DELIVERED' },
  { label: '已发货', value: 'SUCCESS' }
]
export const refundStatus = [
  { label: 'INIT', value: 'INIT' },
  { label: '待退款', value: 'PENDING' },
  { label: '退款完成', value: 'SUCCESS' }
]

export const returnStatus = [
  { label: '', value: 'INIT' },
  { label: '待买家退货', value: 'PENDING' },
  { label: '买家已退货', value: 'RETURNED' },
  { label: '退货完成', value: 'SUCCESS' }
]

export const invoiceStatus = [
  { label: '', value: 'INIT' },
  { label: '待买家退票', value: 'PENDING' },
  { label: '买家已退票', value: 'RETURNED' },
  { label: '退票完成', value: 'SUCCESS' },
  { label: '补开票完成', value: 'REINVOICE' }
]

export const logisticsType = [
  // { label: '上门自提', value: 'VISIT' },
  { label: '物流快递', value: 'EXPRESS' },
  { label: '客户自运', value: 'SELF' }
]
export const reissueLogisticsType = [
  // { label: '上门自提', value: 'VISIT' },
  { label: '物流/快递', value: 'EXPRESS' },
  { label: '其他', value: 'OTHER' }
]

export const originalType = [
  { label: '发票原件', value: 0 },
  { label: '红字信息表', value: 1 },
  { label: '其它', value: 2 }
]
export const invoiceType = [
  { label: '专票', value: 0 },
  { label: '普票', value: 1 },
  { label: '电子普票', value: 2 }
]
export const returnInvoiceType = [
  { label: '若客户开票信息不正确', value: 0 },
  { label: '若客户开票信息更换', value: 1 },
  { label: '若发票货物或劳务名称、规格、数量、金额、备注等有误', value: 2 },
  { label: '若属于退换货', value: 3 },
  { label: '若客户认证不通过,若发票专用章盖章不清晰', value: 4 },
  { label: '发票被毁损或污染等', value: 6 }
]

export const obsoleteType = [
  { label: '客户原因', value: 0 },
  { label: '客服原因', value: 1 }
]

// 更新服务进度类型 - 返厂
export const repairState = [
  { label: '厂家已收货', value: 'RECEIVED' },
  { label: '厂家已寄回', value: 'SENDOFF' }
]
// 更新服务进度类型 - 上门
export const visitProgressType = [
  { label: '已预约', value: 'DATE' },
  { label: '上门完成', value: 'SUCCESS' }
]

// 更新服务进度类型 - 远程指导
export const remoteProgressType = [
  { label: '已预约', value: 'DATE' },
  { label: '指导完成', value: 'SUCCESS' }
]

const getOption = (arr) => {
  return arr.map(item => ({ label: item, value: item }))
}

export const reason = {
  CANCEL: getOption([
    '重复下单',
    '需求变更，暂不需要',
    '物料下错，需修改重新下单',
    '抬头错误，需更换抬头重新下单',
    '物料参数有误，需重新修改下单',
    '物料停产或有区域限制',
    '交期过长，无法等待',
    '其他'
  ]),
  REFUND: getOption([
    '支付方式需要重新选择',
    '未发货，仅退款，取消订单',
    '下单金额变更',
    '退货退款',
    '其他'
  ]),
  REPLACE: getOption([
    '产品发错，使用部门无法正常使用',
    '交期过长，无法等待',
    '数量错误',
    '无人收货，拒收',
    '使用效果不好，不喜欢',
    '产品缺失部件',
    '选型错误',
    '产品描述和实际不符',
    '相关产品证明文件缺失',
    '产品质量问题',
    '保质期异常',
    '产品安装、检测问题',
    '运输问题，货物破损',
    '需求变更',
    '其他'
  ]),
  GOODS: getOption([
    '需求变更',
    '数量错误',
    '选型错误',
    '交期过长，无法等待',
    '无人收货，拒收',
    '产品描述和实际不符',
    '产品缺失部件',
    '相关产品证明文件缺失',
    '保质期异常',
    '运输问题，货物破损',
    '产品质量问题',
    '产品安装、检测问题',
    '产品发错，使用部门无法正常使用',
    '使用效果不好，不喜欢',
    '其他'
  ]),
  INVOICE: getOption([
    '客户开票信息不正确',
    '客户开票信息更换',
    '发票货物或劳务名称、规格、数量、金额、备注等有误',
    '客户认证不通过',
    '若发票专用章盖章不清晰',
    '发票被损毁或污染等',
    '其他'
  ]),
  VISIT: getOption([
    '散装零件，需要安装',
    '保质期内故障维修',
    '保质期外故障维修',
    '产品精度不准需要调试',
    '叉车等设备保养',
    '油品检测',
    '其他'
  ]),
  REPAIR: getOption([
    '产品内置系统、程序升级',
    '产品质量问题，不合格（如不通电、按钮失灵等）',
    '电子仪器精度不准',
    '更换零件',
    '产品运输问题导致轻微损坏',
    '其他'
  ]),
  DETECTION: getOption([
    '常规产品的检测',
    '产品不精准，需要检测',
    '有第三方检测机构检验要求',
    '其他'
  ]),
  SERVICE: getOption([
    '维修',
    '检测',
    '安装',
    '培训',
    // '补发',
    '其他'
  ])
}

export const payPlatform = [
  { label: '微信', value: 0 },
  { label: '电汇', value: 1 },
  { label: '支付宝', value: 2 },
  { label: '银联', value: 3 }
]

export const source = [
  { label: '官网', value: 'GW' },
  { label: 'GBB', value: 'GBB' },
  // { label: '尊享', value: 'ENJOY' },
  { label: 'BOSS', value: 'BOSS' },
  { label: 'ESP', value: 'ESP' }
]

export const company = [
  { label: '安丹达工业技术（上海）有限公司', value: 2000 },
  { label: '北京坤联网络科技有限公司', value: 2800 },
  { label: '上海坤骏材料科技有限公司', value: 2700 },
  { label: '震坤行工业超市（上海）有限公司', value: 1000 },
  { label: '震坤行工业超市（上海）有限公司深圳分公司', value: 3000 },
  { label: '上海坤合供应链管理有限公司', value: 1900 },
  { label: '上海坤合供应链管理有限公司深圳分公司', value: 3001 },
  { label: '上海坤米供应链有限公司', value: 2500 },
  { label: '上海工邦邦工业技术有限公司', value: 1300 },
  { label: '深圳市坤同智能仓储科技有限公司', value: 1500 },
  { label: '上海航利实业有限公司', value: 2900 },
  { label: '震坤行工业超市（上海）有限公司无锡分公司', value: 1100 },
  { label: '震坤行工业超市（上海）有限公司杭州分公司', value: 1600 },
  { label: '震坤行工业超市（上海）有限公司成都分公司', value: 1700 },
  { label: '震坤行工业超市（上海）有限公司淄博分公司', value: 1800 },
  { label: '上海工邦邦工业技术有限公司西安分公司', value: 2300 },
  { label: '上海工邦邦工业技术有限公司苏州分公司', value: 3700 },
  { label: '上海坤合供应链管理有限公司深圳分公司', value: 3001 },
  { label: '上海震坤行供应链有限公司', value: 3800 },
  { label: '震坤行工业超市（上海）有限公司长沙分公司', value: 3500 },
  { label: '震坤行工业超市（上海）有限公司东莞分公司', value: 3300 },
  { label: '震坤行工业超市（上海）有限公司苏州分公司', value: 3400 },
  { label: '震坤行工业超市（上海）有限公司武汉分公司', value: 3200 },
  { label: '震坤行工业超市（苏州）有限公司', value: 1200 },
  { label: '震坤行网络技术（南京）有限公司', value: 3600 },
  { label: '震坤行网络有限公司', value: 3100 }
]

export const materialGroup = [
  { label: '3M-胶水', value: 1 },
  { label: 'Adco', value: 2 },
  { label: 'Araldite', value: 3 },
  { label: 'DC-Ebig', value: 4 },
  { label: 'DC-Ibud', value: 5 },
  { label: 'DOW建筑胶', value: 6 },
  { label: 'DOW聚氨酯', value: 7 },
  { label: 'GRE', value: 8 },
  { label: 'Loctite', value: 9 },
  { label: 'MOLYKOTE', value: 10 },
  { label: 'MRO-安防', value: 11 },
  { label: 'MRO-搬运存储', value: 12 },
  { label: 'MRO-办公用品', value: 13 },
  { label: 'MRO-传动', value: 14 },
  { label: 'MRO-电力行业', value: 15 },
  { label: 'MRO-电气低压配电', value: 16 },
  { label: 'MRO-电气电线电缆', value: 17 },
  { label: 'MRO-电气自动化', value: 18 },
  { label: 'MRO-动力工具', value: 19 },
  { label: 'MRO-个人防护', value: 20 },
  { label: 'MRO-工具', value: 21 },
  { label: 'MRO-工位设备', value: 22 },
  { label: 'MRO-焊接', value: 23 },
  { label: 'MRO-航科行业', value: 24 },
  { label: 'MRO-化学试剂', value: 25 },
  { label: 'MRO-建筑行业', value: 26 },
  { label: 'MRO-金属加工', value: 27 },
  { label: 'MRO-紧固密封件', value: 28 },
  { label: 'MRO-流体设备', value: 29 },
  { label: 'MRO-其他', value: 30 },
  { label: 'MRO-气动液压', value: 31 },
  { label: 'MRO-切削刀具', value: 32 },
  { label: 'MRO-清洁', value: 33 },
  { label: 'MRO-实验室仪器耗材', value: 34 },
  { label: 'MRO-铁建行业', value: 35 },
  { label: 'MRO-冶金行业', value: 36 },
  { label: 'MRO-仪器仪表', value: 37 },
  { label: 'MRO-照明', value: 38 },
  { label: 'Others-油', value: 39 },
  { label: 'Shell-总部', value: 40 },
  { label: 'Zymet', value: 41 },
  { label: '代购组不允许退货，请联系万士泓', value: 42 },
  { label: '化学', value: 43 },
  { label: '胶带', value: 44 },
  { label: '金属加工液', value: 45 },
  { label: '空气动力', value: 46 },
  { label: '普油', value: 47 },
  { label: '清洗防锈', value: 48 },
  { label: '润滑大客户', value: 49 },
  { label: '塑胶', value: 50 },
  { label: '涂料记号标识', value: 51 },
  { label: '脱模', value: 52 },
  { label: '真空', value: 53 },
  { label: '自主品牌', value: 54 },
  { label: 'OEM紧固件', value: 55 },
  { label: 'VPI（VPI退货请在退货理由中填写101单号）', value: 56 },
  { label: 'MRO-制冷暖通', value: 57 },
  { label: '点胶设备', value: 58 },
  { label: '陶氏', value: 59 },
  { label: '品牌胶', value: 60 },
  { label: 'MRO-胶', value: 61 },
  { label: 'SHELL-无锡分', value: 62 },
  { label: 'SHELL-杭州分', value: 63 },
  { label: 'SHELL-成都分', value: 64 },
  { label: 'SHELL-西安分', value: 65 },
  { label: 'SHELL-淄博分', value: 66 }
]

export const invoiceHandler = {
  name: '丁丽丽',
  phone: '15000323128',
  address: '上海市闵行区申滨路36号丽宝广场T4座6层'
}

export const getMap = option => {
  return option.reduce((res, item) => {
    res[item.value] = item.label
    return res
  }, {})
}

export const ruleNumber = (rule, value, callback) => {
  if (Number(value) <= 0) {
    callback(new Error('必须大于0'))
  } else {
    callback()
  }
}
export const ruleNumber0 = (rule, value, callback) => {
  if (Number(value) < 0) {
    callback(new Error('必须大于等于0'))
  } else {
    callback()
  }
}
