<template>
  <div class="app-container purchasePlan-tenders">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="物料组：" prop="materialGroupIds">
               <el-select
                v-model="searchForm.materialGroupIds"
                filterable
                clearable
                placeholder="请输入物料组，支持多选"
                style="width: 100%"
                remote
                multiple
                reserve-keyword
                :remote-method="remoteMaterialGroupIdMethod"
                :loading="materialGroupIdLoading"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌：" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item.brandId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划月份：" prop="planTimeFrom">
              <el-date-picker
                clearable
                v-model="searchForm.planTimeFrom"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择起始月份"
              />
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-date-picker
                clearable
                v-model="searchForm.planTimeTo"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择结束月份"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类目" prop="category">
              <el-cascader
                collapse-tags
                style="width: 100%"
                v-model="searchForm.category"
                ref="casRef"
                :options="categoryList"
                clearable
                filterable
                :props="cascaderProps"
                :emitPath="false"
                placeholder="请选择目录"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="6">
            <el-form-item label="采购计划单号" prop="planOrderNo">
              <el-input
                v-model="searchForm.planOrderNo"
                placeholder="请输入采购计划单号"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKU" prop="sku">
              <el-input
                v-model="searchForm.sku"
                placeholder="请输入SKU"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库存地点" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                clearable
                placeholder="请选择库存地点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in positions"
                  :key="item.position"
                  :value="item.position"
                  :label="`${item.position} ${item.positionName}`"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-table
      v-loading="basisListLoading"
      :data="basisList"
      border
      fit
      row-key="id"
      id="multipleTable"
      ref="multipleTable"
      @selection-change="tableSelectionChange"
    >
      <el-table-column
        type="selection"
        min-width="40"
        fixed="left"
        :selectable="checkSelectable"
        :reserve-selection="true"
      />
      <el-table-column prop="planOrderNo" label="采购计划单号" width="110" />
      <el-table-column
        prop="planItemNo"
        label="行号"
        width="60"
        align="center"
      />
      <el-table-column prop="sku" label="SKU" width="100" />
      <el-table-column prop="position" label="库存地点" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ `${row.position} ${row.positionName}` }}
          </template>
        </el-table-column>
      <el-table-column
        prop="skuName"
        label="物料描述"
        show-overflow-tooltip
        min-width="140"
      />
      <el-table-column
        prop="waitQty"
        label="待招标量"
        align="right"
        width="100"
      >
        <template slot-scope="{ row }">
          {{ row.waitQty === null ? "--" : numFormatThousand(row.waitQty) }}
        </template>
      </el-table-column>
      <el-table-column prop="unit" label="单位" align="center" width="60" />
      <el-table-column prop="price" label="参考单价" align="right" width="80">
        <template slot-scope="{ row }">
          {{ row.price === null ? "--" : numFormatThousand(row.price) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="waitPrice"
        label="待招标金额"
        align="right"
        width="100"
      >
        <template slot-scope="{ row }">
          {{ row.waitPrice === null ? "--" : numFormatThousand(row.waitPrice) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="planQty"
        label="总计划采购量"
        align="right"
        width="100"
      >
        <template slot-scope="{ row }">
          {{ row.planQty === null ? "--" : numFormatThousand(row.planQty) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="planPrice"
        label="总计划金额"
        align="right"
        width="100"
      >
        <template slot-scope="{ row }">
          {{ row.planPrice === null ? "--" : numFormatThousand(row.planPrice) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="sendQty"
        label="已下发招标量"
        align="right"
        width="100"
      >
        <template slot-scope="{ row }">
          {{ row.sendQty === null ? "--" : numFormatThousand(row.sendQty) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="sendPrice"
        label="已下发招标金额"
        align="right"
        width="110"
      >
        <template slot-scope="{ row }">
          {{ row.sendPrice === null ? "--" : numFormatThousand(row.sendPrice) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="materialGroup"
        label="物料组"
        align="center"
        width="120"
      />
      <el-table-column
        prop="brandName"
        label="品牌"
        align="center"
        width="120"
      />
      <el-table-column
        prop="fourthCatalogName"
        label="类目"
        align="center"
        width="200"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{
            `${row.firstCatalogName} / ${row.secondCatalogName} / ${row.thirdCatalogName} / ${row.fourthCatalogName}`
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="planTime"
        label="计划月份"
        align="center"
        width="120"
      />
    </el-table>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div class="fixed">
      <span>选中数量合计：{{ numFormatThousand(selectNum) }}</span>
      <span>选中金额合计：{{ numFormatThousand(selectMoney) }}</span>
      <el-button type="primary" @click="openDialog">下一步</el-button>
    </div>
    <el-dialog
      width="1000px"
      title="当前招标计划量"
      class="add-dialog"
      :visible.sync="isOpenDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="isOpenDialog = false"
    >
      <el-table :data="openData.list" border fit>
        <el-table-column prop="planOrderNo" label="采购计划单号" width="110" />
        <el-table-column
          prop="planItemNo"
          label="行号"
          width="60"
          align="center"
        />
        <el-table-column prop="sku" label="SKU" width="100" />
        <el-table-column prop="position" label="库存地点" width="100" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ `${row.position} ${row.positionName}` }}
          </template>
        </el-table-column>
        <el-table-column
          prop="skuName"
          label="物料描述"
          show-overflow-tooltip
        />
        <el-table-column
          prop="waitQty"
          label="本次招标量"
          align="right"
          width="140"
        >
          <template slot-scope="{ row }">
            <el-input-number
              v-model="row.waitQty"
              style="width: 100%"
              :min="0"
              :max="row.planQty - row.sendQty"
              :controls="false"
              :precision="2"
            />
          </template>
        </el-table-column>
        <el-table-column
          prop="price"
          label="参考单价"
          align="right"
          width="100"
        >
          <template slot-scope="{ row }">
            {{ row.price === null ? "--" : numFormatThousand(row.price) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="waitPrice"
          label="本次招标金额"
          align="right"
          width="100"
        >
          <template slot-scope="{ row }">
            {{
              numFormatThousand(Number((row.waitQty * row.price).toFixed(2)))
            }}
          </template>
        </el-table-column>
      </el-table>
      <footer class="align-right">
        <div class="btns">
          <div>
            <span style="margin-right: 10px"
              >选中数量合计：{{
                openData.selectNum
                  ? numFormatThousand(openData.selectNum)
                  : openData.selectNum
              }}</span
            >
            <span
              >选中金额合计：{{
                openData.selectMoney
                  ? numFormatThousand(openData.selectMoney)
                  : openData.selectMoney
              }}</span
            >
          </div>
          <div>
            <el-button @click="isOpenDialog = false">取消</el-button>
            <el-button
              @click="submitAddDialog"
              type="primary"
              :loading="openLoading"
              >创建招标</el-button
            >
          </div>
        </div>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCatalogTree,
  getBrandListApi,
  getMaterialGroupListApi,
  getPurchasePlanTendersList,
  createPurchasePlanTenders,
  getAllPosition
} from '@/api/purchasePlan';
import { cloneDeep } from 'lodash';
import { numFormatThousand } from '@/utils/index';
import moment from 'moment';

export default {
  name: 'purchasePlanTenders',
  data() {
    return {
      isPro: /pro/.test(window.CUR_DATA.env),
      numFormatThousand,
      brandIdOptions: [],
      brandIdLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      categoryList: [],
      basisListLoading: false,
      positions: [],
      basisList: [],
      selectRows: [],
      selectNum: 0,
      selectMoney: 0,
      searchForm: {
        materialGroupIds: [],
        brandNo: '',
        planTimeFrom: moment(new Date()).format('yyyy-MM'),
        planTimeTo: '',
        sku: '',
        category: [],
        factory: ''
      },
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      isOpenDialog: false,
      openLoading: false,
      openData: {},
      cascaderProps: {
        value: 'id',
        label: 'name',
        checkStrictly: true
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(this.searchForm.planTimeFrom)).getTime()
        }
      }
    };
  },
  created() {
    Promise.all([
      getCatalogTree({ type: 0 }).then((res) => {
        if (res.success === true && res.data && res.data.length) {
          this.categoryList = this.getTreeData(res.data);
        }
      })
    ]);
    this.handleFilter();
    this.getPositions();
  },
  watch: {
    'openData.list': {
      handler(nval, oval) {
        if (!nval) {
          return;
        }
        let num = 0;
        let money = 0;
        nval.forEach((a) => {
          num += a.waitQty;
          money += a.waitQty * a.price;
        });
        this.openData.selectNum = Number(num.toFixed(2));
        this.openData.selectMoney = Number(money.toFixed(2));
      },
      deep: true
    },
    selectRows: {
      handler(nval, oval) {
        let num = 0;
        let money = 0;
        nval.forEach((a) => {
          num += a.waitQty;
          money += a.waitQty * a.price;
        });
        this.selectNum = Number(num.toFixed(2));
        this.selectMoney = Number(money.toFixed(2));
      },
      deep: true
    }
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
      this.$refs.multipleTable && this.$refs.multipleTable.clearSelection();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      delete this.searchForm.planTimeTo;
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'
        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      } else {
        this.materialGroupOptions = [];
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    async getPositions () {
      let res = await getAllPosition()
      if (res.status === 200) {
        this.positions = res.result;
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      param.category[0] && (param.firstCatalogId = param.category[0]);
      param.category[1] && (param.secondCatalogId = param.category[1]);
      param.category[2] && (param.thirdCatalogId = param.category[2]);
      param.category[3] && (param.fourthCatalogId = param.category[3]);
      this.basisList = [];
      this.basisListLoading = true;
      getPurchasePlanTendersList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              this.basisList = res.data.map((a) => {
                a.waitQty = Number((a.planQty - a.sendQty).toFixed(2));
                a.waitPrice = Number((a.planPrice - a.sendPrice).toFixed(2));
                a.id = `${a.planOrderNo}-${a.planItemNo}`;
                return a;
              });
              this.total = res.total;
            }
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    tableSelectionChange(val) {
      this.selectRows = val;
    },
    checkSelectable(row) {
      return row.waitQty > 0;
    },
    openDialog() {
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请先选中数据！' });
        return;
      }
      this.isOpenDialog = true;
      let list = cloneDeep(this.selectRows).sort((a, b) => {
        if (a.planOrderNo > b.planOrderNo) {
          return -1;
        } else if (a.planOrderNo < b.planOrderNo) {
          return 1;
        } else {
          return a.planItemNo > b.planItemNo ? 1 : -1;
        }
      });
      this.openData = {
        list,
        selectNum: this.selectNum,
        selectMoney: this.selectMoney
      };
    },
    submitAddDialog() {
      let params = this.openData.list.map((a) => {
        return {
          planItemNo: a.planItemNo,
          planOrderNo: a.planOrderNo,
          sku: a.sku,
          planQty: a.waitQty,
          planTime: a.planTime
        };
      });
      this.openLoading = true;
      createPurchasePlanTenders(params)
        .then((res) => {
          this.openLoading = false;
          if (res.code === 200) {
            this.isOpenDialog = false;
            this.openData = {};
            this.handleFilter();
            this.$refs.multipleTable && this.$refs.multipleTable.clearSelection();
            // 跳转商机
            window.open(
              `https://${
                this.isPro ? 'boc-admin' : 'boc-admin-uat'
              }.zkh360.com/bid/list`
            );
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.openLoading = false;
        });
    }
  }
};
</script>

<style lang="scss">
.purchasePlan-tenders {
  padding-bottom: 60px;
  .fixed {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    height: 60px;
    position: fixed;
    bottom: 0;
    right: 0;
    width: 100%;
    padding-right: 20px;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    z-index: 9;
    background: #fff;
    & > span {
      margin-right: 20px;
    }
  }
  .btns {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0;
  }
}
</style>
