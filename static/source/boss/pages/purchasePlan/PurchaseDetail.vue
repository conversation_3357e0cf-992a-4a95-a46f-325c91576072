<template>
  <div class="app-container purchasePlan-detail">
    <el-row class="top" :loading="loading">
      <el-col :span="8">
        <b>计划单号：</b>
        <span>{{ detail.planOrderNo }}</span>
      </el-col>
      <el-col :span="8">
        <b>物料组：</b>
        <span>{{ detail.materialGroup }}</span>
      </el-col>
      <el-col :span="8">
        <b>品牌：</b>
        <span>{{ detail.brandName }}</span>
      </el-col>
      <el-col :span="8">
        <b>工厂：</b>
        <span>{{ detail.factory }}&ensp;{{ detail.factoryName }}</span>
      </el-col>
      <el-col :span="8">
        <b>类型：</b>
        <span>{{ typeMap[detail.type] }}</span>
      </el-col>
      <el-col :span="8">
        <b>计划年月：</b>
        <span>{{ detail.planTime }}</span>
      </el-col>
      <el-col :span="8">
        <b>总计划采购量：</b>
        <span>{{ numFormat(detail.totalQty) }}</span>
      </el-col>
      <el-col :span="8">
        <b>总计划采购金额：</b>
        <span>{{ numFormat(detail.totalPrice) }} </span>
      </el-col>
      <el-col :span="8">
        <b>运营策略：</b>
        <span>{{ suggestedMap[detail.operationType] }}</span>
      </el-col>
      <el-col :span="8">
        <b>已下发招标量：</b>
        <span>{{ numFormat(detail.sendQty) }} </span>
      </el-col>
      <el-col :span="8">
        <b>已下发招标金额：</b>
        <span>{{ numFormat(detail.sendPrice) }} </span>
      </el-col>
      <el-col :span="8">
        <b>OA流程：</b>
        <el-link type="primary" :underline="false" @click="toOA">{{ detail.oaNo }}</el-link>
      </el-col>
      <el-col :span="8">
        <b>已采购量：</b>
        <span>{{ numFormat(detail.totalPurchasedQty) }} </span>
      </el-col>
      <el-col :span="8">
        <b>已采购金额：</b>
        <span>{{ numFormat(detail.totalPurchasedPrice) }} </span>
      </el-col>
      <el-col :span="8">
        <b>备注：</b>
        <el-input
          v-if="type==='edit'"
          type="textarea"
          v-model="detail.remark"
          rows="2"
        ></el-input>
        <span v-else>{{ detail.remark }}</span>
      </el-col>
      <el-col :span="8">
        <b>计划单状态：</b>
        <span>{{ statusMap[detail.status] }} </span>
      </el-col>
    </el-row>
    <div class="sub-title">计划明细</div>
    <div style="text-align: right;margin-bottom: 8px;">
      <el-button v-if="type === 'edit'" @click="handleDelete" type="danger">批量删除</el-button>
      <el-button @click="handleExport" :loading="exportLoading" type="primary">导出明细</el-button>
    </div>
    <vxe-grid
      border
      resizable
      keep-source
      align="center"
      :max-height="500"
      id="multipleTable"
      ref="multipleTable"
      :loading="listLoading"
      :custom-config="tableCustom"
      :data="list"
      :columns="columns"
      :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
    >
      <template v-slot:sku="{ row }">
        <div style="display: flex; flex-wrap: wrap">
          {{ row.sku }}
          <el-tag
            size="small"
            v-for="item in row.tagsDesc"
            :key="item"
            style="margin-left: 6px; margin-bottom: 4px"
            >{{ item }}</el-tag
          >
        </div>
      </template>
      <template v-slot:fourth_catalog_name="{ row }">
        {{
          `${row.firstCatalogName} / ${row.secondCatalogName} / ${row.thirdCatalogName} / ${row.fourthCatalogName}`
        }}
      </template>
      <template v-slot:stockReason="{ row }">
        <el-input
          :disabled="type !== 'edit' || row.confirmStatus === 1 || row.itemType !== '人工'"
          v-model="row.stockReason"
          style="width: 100%"
          maxlength="20"
        />
      </template>
      <template v-slot:position="{ row, rowIndex }">
        <!-- <el-select
          v-if="type === 'edit'"
          :loading="row.positionLoading"
          v-model="row.positionObj"
          filterable
          style="width: 100%"
          value-key="code"
          @focus="getPositions(row, rowIndex)"
        >
          <el-option
            v-for="item in row.positions"
            :key="item.code"
            :value="item"
            :label="`${item.code} ${item.name}`"
          >
          </el-option>
        </el-select>
        <span v-else>{{ row.position }}&ensp;{{ row.positionName }}</span> -->
        <span>{{ row.position }}&ensp;{{ row.positionName }}</span>
      </template>
      <template v-slot:planQty="{ row, column, $rowIndex }">
        <el-input-number
          v-if="type === 'edit' && row.confirmStatus != 1"
          v-model="row.planQty"
          style="width: 100%"
          :min="0"
          :precision="2"
          :controls="false"
          @blur="caculate(row, $rowIndex)"
        />
        <span v-else>{{
          row[column.property] === null
            ? "--"
            : numFormatThousand(row[column.property])
        }}</span>
      </template>
       <template v-slot:supplier="{ row, rowIndex }">
        <el-select
          v-if="type === 'edit' && ['Consignment', 'Purchase'].includes(detail.operationType) && row.confirmStatus != 1"
          :loading="row.supplierLoading"
          v-model="row.supplierObj"
          filterable
          style="width: 100%"
          value-key="supplierCode"
          @focus="getSuppliers(row, rowIndex)"
        >
          <el-option
            v-for="item in row.suppliers"
            :disabled="detail.operationType === 'Consignment' && !item.consignment"
            :key="item.supplierCode"
            :value="item"
            :label="item.supplierName"
          >
          </el-option>
        </el-select>
        <span v-else>{{ row.supplierName || ''}}</span>
      </template>
      <template v-slot:number="{ row, column }">
        {{
          row[column.property] === null
            ? "--"
            : numFormatThousand(row[column.property])
        }}
      </template>
      <template v-slot:actualPurchasePrice="{ row, column, $rowIndex }">
        <div v-if="type === 'edit' && row.confirmStatus != 1 && detail.operationType !== 'Bid'" style="display: flex;">
          <el-input-number
            v-model="row.actualPurchasePrice"
            style="width: 90%"
            :min="0"
            :precision="2"
            @blur="caculate(row, $rowIndex)"
            :controls="false"
          />
          <i v-if="row.actualPurchasePrice > row.price" class="el-icon-top" style="color: #F56C6C;line-height: 32px"></i>
          <i v-if="row.actualPurchasePrice < row.price" class="el-icon-bottom" style="color: #67C23A;line-height: 32px"></i>
        </div>
        <span v-else>{{
          row[column.property] === null
            ? "--"
            : numFormatThousand(row[column.property])
        }}</span>
      </template>
    </vxe-grid>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div class="footer-btns" v-if="type === 'edit'">
      <el-button v-if="showSaveBtn" type="primary" :loading="loading" @click="handleSave"
        >保存</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">保存并提交</el-button>
      <el-button @click="goback">取消</el-button>
    </div>
  </div>
</template>

<script>
import {
  getPurchasePlanDetail,
  getPurchasePlanDetailItems,
  salesPositions,
  salesSupplier,
  deletePurchasePlanDetailItems,
  updatePurchasePlanDetail,
  exportPurchasePlanDetailRows,
  updatePurchasePlanDetailAndSubmit
} from '@/api/purchasePlan';
import { numFormatThousand } from '@/utils/index';
import { typeMap, suggestedMap, purchaseDetailColumns, statusMap } from './const';

export default {
  name: 'purchasePlanDetail',
  data() {
    return {
      numFormatThousand,
      typeMap,
      suggestedMap,
      tableCustom: {
        storage: true
      },
      columns: purchaseDetailColumns,
      statusMap,
      type: 'detail',
      id: '',
      detail: {},
      list: [],
      loading: false,
      listLoading: false,
      exportLoading: false,
      total: 0,
      showSaveBtn: false,
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  created() {
    this.type = this.$route.path.split('/')[2];
    this.id = this.$route.path.split('/')[3];
    this.getDetail();
    this.getList();
    this.columns =
      this.type === 'edit' && (this)
        ? [
            {
              field: 'checkbox',
              type: 'checkbox',
              title: '',
              width: 60,
              fixed: 'left'
            },
            ...purchaseDetailColumns
          ]
        : [
            {
              field: 'planItemNo',
              title: '行号',
              width: 60,
              fixed: 'left'
            },
            {
              field: 'poRemark',
              title: '转单情况',
              width: 140
            },
            ...purchaseDetailColumns.slice(1)
          ];
  },
  methods: {
    getDetail() {
      this.loading = true;
      getPurchasePlanDetail(this.id)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            if (res.data) {
              this.detail = res.data;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      this.listLoading = true;
      getPurchasePlanDetailItems({
        planOrderNo: this.id,
        ...this.listQueryInfo
      })
        .then((res) => {
          this.listLoading = false;
          if (res.code === 200) {
            if (res.data) {
              this.list = res.data.map((a) => {
                return {
                  ...a,
                  positionObj: { code: a.position, name: a.positionName },
                  positions: [{ code: a.position, name: a.positionName }],
                  supplierObj: a.supplierCode ? { supplierCode: a.supplierCode, supplierName: a.supplierName } : {},
                  suppliers: a.supplierCode ? [{ supplierCode: a.supplierCode, supplierName: a.supplierName }] : [],
                  moqAndMpq: `${a.purchaseMoq ?? '0'}/${a.purchaseMpq ?? '0'}`
                }
              });
              this.total = res.total;
              this.showSaveBtn = this.list.every(it => it.confirmStatus === 2);
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    // 获取备货仓list
    async getPositions(row, index) {
      if (row.getPositions) {
        return
      }
      this.$set(this.list[index], 'positionLoading', true)
      let res = await salesPositions({ factory: this.detail.factory, skuNo: row.sku });
      this.$set(this.list[index], 'positionLoading', false)
      if (res.code === 200) {
        let arr = [];
        res.data.forEach((a) => {
          a.allPosition.forEach((b) => {
            arr.push({
              ...b,
              warehouseCode: a.warehouseCode,
              warehouseName: a.warehouseName
            });
          });
        });
        this.$set(this.list[index], 'positions', arr || [{ code: row.position, name: row.positionName }])
        this.$set(this.list[index], 'getPositions', true)
      } else {
        this.$set(this.list[index], 'positions', [{ code: row.position, name: row.positionName }])
        this.$message.error({ message: res.msg });
      }
    },
    // 获取供应商list
    async getSuppliers(row, index) {
      if (row.getsuppliers) {
        return
      }
      this.$set(this.list[index], 'supplierLoading', true)
      let res = await salesSupplier({ factory: this.detail.factory, sku: row.sku });
      this.$set(this.list[index], 'supplierLoading', false)
      if (res.code === 200) {
        this.$set(this.list[index], 'suppliers', res.data || [{ supplierCode: row.supplierCode, supplierName: row.supplierName }])
        this.$set(this.list[index], 'getSuppliers', true)
      } else {
        this.$set(this.list[index], 'suppliers', [{ supplierCode: row.supplierCode, supplierName: row.supplierName }])
        this.$message.error({ message: res.msg });
      }
    },
    numFormat(num) {
      return typeof num === 'number' ? numFormatThousand(num) : num;
    },
    // 批量删除行
    async handleDelete() {
      const selectedList = this.$refs.multipleTable.getCheckboxRecords()
      if (!selectedList.length) {
        this.$message.error('请选择要删除的行')
        return
      }
      const ids = selectedList.map(item => item.id)
      const res = await deletePurchasePlanDetailItems({ ids })
      if (res.code === 200) {
        this.$message.success('删除成功')
        this.getDetail();
        this.getList();
      } else {
        this.$message.error(res.msg)
      }
    },
    handleExport() {
      this.exportLoading = true;
      exportPurchasePlanDetailRows(this.id)
        .then((res) => {
          this.exportLoading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.exportLoading = false;
          this.$message.error({ message: '导出失败！' });
        });
    },
    async handleSave() {
      let params = {
        planOrderNo: this.detail.planOrderNo,
        remark: this.detail.remark,
        purchasePlanItemUpdateInfos: this.list.map((a) => {
          return {
            id: a.id,
            planItemNo: a.planItemNo,
            planOrderNo: a.planOrderNo,
            operationType: this.detail.operationType,
            planQty: Number(a.planQty),
            supplierNo: a.supplierObj.supplierCode || a.supplierCode,
            supplierName: a.supplierObj.supplierName || a.supplierName,
            actualPurchasePrice: a.actualPurchasePrice,
            sku: a.sku,
            onWayQty: a.onWayQty,
            inStockQty: a.inStockQty,
            stockReason: a.stockReason
          }
        })
      }
      this.loading = true;
      let res = await updatePurchasePlanDetail(params);
      this.loading = false;
      if (res.code === 200) {
        this.$message.success({ message: '保存成功！' });
      } else {
        this.$message.error({ message: res.msg });
      }
    },
    async handleSubmit() {
      let params = {
        planOrderNo: this.detail.planOrderNo,
        remark: this.detail.remark,
        purchasePlanItemUpdateInfos: this.list.map((a) => {
          return {
            id: a.id,
            planItemNo: a.planItemNo,
            planOrderNo: a.planOrderNo,
            operationType: this.detail.operationType,
            planQty: Number(a.planQty),
            supplierNo: a.supplierObj.supplierCode || a.supplierCode,
            supplierName: a.supplierObj.supplierName || a.supplierName,
            actualPurchasePrice: a.actualPurchasePrice,
            sku: a.sku,
            onWayQty: a.onWayQty,
            inStockQty: a.inStockQty,
            stockReason: a.stockReason
          }
        })
      }
      this.loading = true;
      let res = await updatePurchasePlanDetailAndSubmit(params);
      this.loading = false;
      if (res.code === 200) {
        this.$message.success({ message: '提交成功！' });
        this.$router.go(-1);
        this.$closeTag(this.$route.path);
      } else {
        this.$message.error({ message: res.msg });
      }
    },
    goback() {
      this.$router.back();
      this.$closeTag(this.$route.path);
    },
    toOA() {
      window.open(`https://${window.CUR_DATA.env === 'pro' ? 'oa' : 'testoa3'}.zkh360.com/workflow/request/ViewRequest.jsp?requestid=${this.detail.oaId}`)
    },
    caculate(row, index) {
      let planPrice = 0
      if (!row.planQty || !row.actualPurchasePrice) {
        planPrice = Number((row.planQty * row.price).toFixed(6))
      } else {
        planPrice = Number((row.planQty * row.actualPurchasePrice).toFixed(6))
      }
      this.$set(this.list[index], 'planPrice', planPrice)
    }
  }
};
</script>

<style lang="scss">
.purchasePlan-detail {
  .top {
    margin-bottom: 20px;
    .el-col {
      min-height: 40px;
      display: flex;
      align-items: center;
      b {
        width: 130px;
        text-align: right;
        display: inline-block;
        line-height: 40px;
      }
      span {
        flex: 1;
      }
    }
  }
  .sub-title {
    font-size: 16px;
    color: #333;
    text-indent: 20px;
    background-color: #ccc;
    height: 32px;
    line-height: 32px;
    margin-bottom: 20px;
  }
  .footer-btns {
    text-align: center;
    margin-top: 10px;
  }
}
</style>
