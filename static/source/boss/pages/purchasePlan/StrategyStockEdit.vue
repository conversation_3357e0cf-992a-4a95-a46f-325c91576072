<template>
  <div class="app-container" style="padding-bottom: 50px;">
    <div class="step-wrap">
      <el-steps :active="currentStep" align-center finish-status="success" simple>
        <el-step title="步骤1：选择目标备货商品" />
        <el-step title="步骤2：AI推荐&人工确认" />
        <el-step title="步骤3：创建采购计划" />
      </el-steps>
    </div>
    <div v-loading="loading" class="form-wrap">
      <component :is="currentComponent" :ref="`stepComponent${currentStep}`" :detail-info="detailInfo" :disabled="disabled" :task-id="taskId" @import-success="getDetail" @back-step1="handleBackStep1" @status="handleStatus" />
    </div>
    <div class="strategy-fixed-btn-wrap">
      <el-button @click="handleCancel">取消</el-button>
      <el-button v-if="currentStep !== 2" :disabled="disabled" type="primary" @click="handleSave(false)">保存</el-button>
      <!-- <el-button v-if="currentStep > 0" :disabled="disabled" type="primary" @click="handlePrev">上一步</el-button> -->
      <el-button :disabled="disabled || createDisabled" type="primary" @click="handleNext">{{ currentStep === 0 ? '去AI试算' : currentStep === 1 ? '去创建采购计划' : '创建采购计划' }}</el-button>
    </div>
  </div>
</template>

<script>
import Step1 from './components/strategyStock/Step1.vue'
import Step2 from './components/strategyStock/Step2.vue'
import Step3 from './components/strategyStock/Step3.vue'
import { getStrategyStockDetail, getStrategyHeaderForStep2 } from '@/api/purchasePlan'
export default {
  components: {
    Step1,
    Step2,
    Step3
  },
  data() {
    return {
      currentStep: 0,
      detailInfo: {},
      status: '',
      disabled: false,
      taskId: '',
      type: '',
      loading: false
    }
  },
  async mounted() {
    this.taskId = this.$route.params.id || '0'
    if (this.taskId === '0') {
      this.currentStep = 0
    } else {
      await this.getStatus()
      // 根据状态初始化当前步骤
      if (this.status === 'New') {
        this.currentStep = 0
        this.getDetail()
      } else if (this.status === 'Calculating' || this.status === 'WaitCalculating' || this.status === 'AgainCalculating' || this.status === 'Done') {
        this.currentStep = 1
      } else if (this.status === 'PurchasePland') {
        this.currentStep = 2
      }
    }
  },
  unmounted() {
    if (this.taskId === '0') {
      this.currentStep = 0
      this.detailInfo = {}
    }
  },
  computed: {
    currentComponent: function() {
      return `Step${this.currentStep + 1}`
    },
    createDisabled: function() {
      if (this.currentStep === 1) {
        return this.status !== 'Done'
      } else {
        return false
      }
    }
  },
  methods: {
    handleStatus(status) {
      this.status = status
    },
    async getDetail() {
      this.loading = true
      try {
        const res = await getStrategyStockDetail(this.taskId)
        if (res.code === 200) {
          this.detailInfo = res.data
        }
      } catch (error) {
        this.$message.error(error.message)
      } finally {
        this.loading = false
      }
    },
    async getStatus() {
      const res = await getStrategyHeaderForStep2(this.taskId)
      if (res.code === 200) {
        this.status = res.data.status
      }
    },
    handleBackStep1() {
      this.currentStep = 0
      this.getDetail()
    },
    handleCancel() {
      this.$confirm('请确保已经保存当前信息', '确认取消?', {
      }).then(() => {
        this.$closeTag(this.$route.path);
        this.$router.push({
          path: '/purchasePlan/strategyStock'
        })
      })
    },
    handlePrev() {
      this.$confirm('请确保已经保存当前信息', '确认退回上一步?', {
      }).then(() => {
        this.currentStep--
      })
    },
    async handleNext() {
      const res = await this.handleSave(true)
      if (res) {
        this.currentStep++
      }
    },
    async handleSave(isSubmit) {
      // 保存接口
      const curComp = this.$refs['stepComponent' + this.currentStep]
      this.loading = true
      const res = await curComp.handleSave(isSubmit)
      this.loading = false
      if (res && this.currentStep === 0) {
        this.taskId = res.taskId
        this.detailInfo = {
          taskId: res.taskId,
          status: 'New'
        }
      }
      if (res && this.currentStep === 2) {
        // 跳转采购计划列表
        return false
      }
      return res
    }
  }
}
</script>

<style>
.el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: 65%;
}
.strategy-fixed-btn-wrap {
  position: fixed;
  width: calc(100% - 264px);
  z-index: 99;
  bottom: 0;
  padding: 12px 24px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  box-shadow: rgb(25 26 35 / 20%) 0px -1px 20px 0px;
}
</style>
