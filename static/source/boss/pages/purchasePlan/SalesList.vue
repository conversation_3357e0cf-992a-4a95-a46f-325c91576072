<template>
  <div class='app-container salesForecast-list'>
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="130px"
        label-position="right"
        :show-message="false"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="物料组：" prop="materialGroupIds">
              <el-select
                v-model="searchForm.materialGroupIds"
                filterable
                clearable
                placeholder="请输入物料组，支持多选"
                style="width: 100%"
                remote
                multiple
                reserve-keyword
                :remote-method="remoteMaterialGroupIdMethod"
                :loading="materialGroupIdLoading"
                value-key="id"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类型：" prop="type">
              <el-select
                v-model="searchForm.type"
                clearable
                placeholder="请选择类型"
                style="width: 100%"
              >
                <el-option label="年度" value="YEAR"></el-option>
                <el-option label="月度" value="MONTH"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌：" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
                @change="(val) => handleChange(val, 'brand')"
                value-key="brandId"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划月份：" prop="planTimeFrom">
              <el-date-picker
                clearable
                v-model="searchForm.planTimeFrom"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择起始月份"
              />
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-date-picker
                clearable
                v-model="searchForm.planTimeTo"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择结束月份"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="search-row" label="SKU：" prop="sku">
              <el-input
                v-model="searchForm.sku"
                placeholder="多个sku用空格隔开，最多50个"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类目：" prop="category">
              <el-cascader
                collapse-tags
                style="width: 100%"
                v-model="searchForm.category"
                ref="casRef"
                :options="categoryList"
                clearable
                filterable
                :props="cascaderProps"
                :emitPath="false"
                placeholder="请选择目录"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工厂：" prop="factory">
              <el-select
                v-model="searchForm.factory"
                filterable
                clearable
                placeholder="请输入关键词"
                style="width: 100%"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.factory"
                  :label="`${item.factory} ${item.factoryName}`"
                  :value="item.factory"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态：" prop="status">
              <el-select
                v-model="searchForm.status"
                clearable
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="草稿" value="0"></el-option>
                <el-option label="待审核" value="1"></el-option>
                <el-option label="已通过" value="2"></el-option>
                <el-option label="已驳回" value="-1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="累计预测准确率：" prop="forecastRateMin">
              <el-input-number
                v-model="searchForm.forecastRateMin"
                clearable
                placeholder="最小值"
                style="width: 45%"
                :controls="false"
                :min="0"
                :max="100"
              ></el-input-number>
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-input-number
                v-model="searchForm.forecastRateMax"
                clearable
                placeholder="最大值"
                style="width: 45%"
                :controls="false"
                :min="0"
                :max="100"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="销售客户数：" prop="customerNumMin">
              <el-input-number
                v-model="searchForm.customerNumMin"
                clearable
                placeholder="最小值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-input-number
                v-model="searchForm.customerNumMax"
                clearable
                placeholder="最大值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <vxe-grid
        border
        resizable
        keep-source
        align="center"
        :max-height="screenHeight - 320"
        id="multipleTable"
        ref="multipleTable"
        :loading="basisListLoading"
        :custom-config="tableCustom"
        :data="basisList"
        :columns="saleColumns"
        :toolbar-config="tableToolbar"
        :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
        :sort-config="{ remote: true }"
        @checkbox-all="selectAll"
        @checkbox-change="selectChange"
        @sort-change="sortChange"
      >
        <template v-slot:toolbar_buttons>
          <div style="text-align: right">
            <el-button
              v-if="role1"
              :loading="basisListLoading"
              type="primary"
              @click="editDlgShow('')"
              >新增</el-button
            >
            <el-button
              v-if="role1"
              type="primary"
              :loading="basisListLoading"
              @click="handleSave()"
            >
              批量提交
            </el-button>
            <el-button
              v-if="role2"
              type="primary"
              :loading="basisListLoading"
              @click="() => handleOperate(true, 'accept')"
              >批量通过</el-button
            >
            <el-button
              v-if="role2"
              :loading="basisListLoading"
              type="primary"
              @click="() => handleOperate(false, 'reject')"
              >批量驳回</el-button
            >
            <!-- <el-button
              :loading="basisListLoading"
              type="primary"
              @click="() => handleOperate('cancel')"
              >批量撤回</el-button
            > -->
            <el-button
              v-if="role1"
              :loading="basisListLoading || exportLoading"
              type="primary"
              @click="handleExport"
              >批量导出</el-button
            >
            <el-upload
              v-if="role1"
              action="/api-ab/sales-forecast/import"
              :show-file-list="false"
              :on-success="onUploadSuccess"
              :on-error="onUploadError"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              style="display: inline-block; margin-left: 10px"
              name="excelFile"
            >
              <el-button
                type="primary"
                style="margin-right: 5px"
                :loading="basisListLoading || importLoading"
                >批量导入</el-button
              >
            </el-upload>
            <el-button
              v-if="!!~userRole.indexOf('采购计划运营')"
              :loading="basisListLoading"
              type="primary"
              @click="toPurchase"
              >转采购计划</el-button
            >
          </div>
        </template>
        <template v-slot:sku="{ row }">
          <div style="display: flex; flex-wrap: wrap">
            {{ row.sku }}
            <el-tag
              size="small"
              v-for="item in row.tagsDesc"
              :key="item"
              style="margin-left: 6px; margin-bottom: 4px"
              >{{ item }}</el-tag
            >
          </div>
        </template>
        <template v-slot:fourth_catalog_name="{ row }">
          {{
            `${row.firstCatalogName} / ${row.secondCatalogName} / ${row.thirdCatalogName} / ${row.fourthCatalogName}`
          }}
        </template>
        <template v-slot:factory="{ row }"
          >{{ row.factory }}&ensp;{{ row.factoryName }}</template
        >
        <template v-slot:forecast_rate="{ row }">
          <el-link
            type="primary"
            @click="openMsgDialog(row.historyDataList || [])"
            >{{
              row.forecastRate === null ? "--" : row.forecastRate + "%"
            }}</el-link
          >
        </template>
        <template v-slot:avg_forecast_rate="{ row }">
          {{ row.avgForecastRate === null ? "--" : row.avgForecastRate + "%" }}
        </template>
        <template v-slot:customer_num="{ row }">
          {{
            row.customerNum === null || row.customerNum === undefined
              ? "--"
              : numFormatThousand(row.customerNum)
          }}
        </template>
        <template v-slot:forecast_sales_qty="{ row }">
          <el-link type="primary" @click="openDetailDialog(row.id)">
            {{
              row.forecastSalesQty === null
                ? "--"
                : numFormatThousand(row.forecastSalesQty)
            }}
          </el-link>
        </template>
        <template v-slot:number="{ row, column }">
          {{
            row[column.property] === null
              ? "--"
              : numFormatThousand(row[column.property])
          }}
        </template>
        <template v-slot:status="{ row }">
          {{ statusText[row.status] }}
        </template>
        <template v-slot:action="{ row }">
          <el-button
            v-if="(row.status == 0 || row.status == -1) && role1"
            type="text"
            @click="editDlgShow(row.id)"
          >
            编辑
          </el-button>
          <el-button
            v-if="(row.status == 0 || row.status == -1) && role1"
            type="text"
            @click="handleSave(row)"
          >
            提交
          </el-button>
          <el-button
            v-if="row.status == 1 && role2"
            type="text"
            @click="handleOperate(true, 'accept', row.id)"
          >
            通过
          </el-button>
          <el-button
            v-if="row.status == 1 && role2"
            type="text"
            @click="handleOperate(false, 'reject', row.id)"
          >
            驳回
          </el-button>
          <!-- <el-button
            v-if="row.status == 1 || row.status == 2"
            type="text"
            @click="handleOperate('cancel', row.id)"
          >
            撤回
          </el-button> -->
        </template>
      </vxe-grid>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <edit-dlg
      :editVisible="editVisible"
      :editId="editId"
      :cancel="editDlgHide"
      :factoryList="factoryList"
      :planTime="searchForm.planTimeFrom"
    ></edit-dlg>

    <el-dialog
      width="600px"
      title="历史数据"
      class="msg-dialog"
      :visible.sync="isOpenMsgDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="openMsgDialog(null)"
    >
      <el-table :data="openMsgData" border fit>
        <el-table-column prop="forecastTime" label="时间" align="center" />
        <el-table-column prop="forecastQty" label="预测销量" align="right" />
        <el-table-column prop="actualQty" label="实际销量" align="right" />
      </el-table>
    </el-dialog>
    <el-dialog
      width="900px"
      title="预测明细"
      class="msg-dialog"
      :visible.sync="isOpenDetailDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="openDetailDialog(null)"
    >
      <el-table
        :data="openDetailData"
        :loading="openDetailLoading"
        border
        fit
        max-height="500"
      >
        <el-table-column
          v-for="item in saleDetailColumns"
          :prop="item.field"
          :key="item.field"
          :label="item.title"
          :width="item.width"
          :align="item.align"
          :show-overflow-tooltip="item.showOverflow"
        >
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { numFormatThousand } from '@/utils/index';
import { saleColumns, saleDetailColumns } from './const';
import { mapState } from 'vuex';
import {
  getCatalogTree,
  getAllFactory,
  getMaterialGroupListApi,
  getBrandListApi,
  getSalesForecastList,
  approveSalesForecast,
  updateSalesForecastNum,
  salesExport,
  salesInfo
} from '@/api/purchasePlan';
import editDlg from './components/editDlg.vue';
import moment from 'moment';

export default {
  name: 'SalesList',
  components: { editDlg },
  data() {
    return {
      numFormatThousand,
      brandIdOptions: [],
      brandIdLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      categoryList: [],
      factoryList: [],
      basisListLoading: false,
      basisList: [],
      selectRows: [],
      searchForm: {
        materialGroupIds: [],
        brandNo: '',
        planTimeFrom: moment(new Date()).format('yyyy-MM'),
        planTimeTo: '',
        sku: '',
        category: [],
        factory: ''
      },
      orderByField: '',
      orderByAscOrDesc: '',
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      editVisible: false,
      editId: '',
      cascaderProps: {
        value: 'id',
        label: 'name',
        checkStrictly: true
      },
      statusText: {
        0: '草稿',
        1: '待审核',
        2: '已通过',
        '-1': '已驳回'
      },
      isOpenMsgDialog: false,
      openMsgData: [],
      isOpenDetailDialog: false,
      openDetailLoading: false,
      openDetailData: [],
      saleColumns,
      saleDetailColumns,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      screenHeight: document.body.clientHeight,
      exportLoading: false,
      importLoading: false,
      validateData: {
        show: false,
        data: null
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(this.searchForm.planTimeFrom)).getTime()
        }
      }
    };
  },
  computed: {
    ...mapState(['userRole']),
    role1() {
      return !!~this.userRole.indexOf('备货策略') || !!~this.userRole.indexOf('备货策略审核');
    },
    role2() {
      return !!~this.userRole.indexOf('备货策略审核');
    }
  },
  created() {
    Promise.all([
      getCatalogTree({ type: 0 }).then((res) => {
        if (res.success === true && res.data && res.data.length) {
          this.categoryList = this.getTreeData(res.data);
        }
      }),
      getAllFactory().then((res) => {
        if (res.status === 200) {
          this.factoryList = res.result;
        }
      })
    ]);
    // this.handleFilter();
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
      console.log(this.$refs.multipleTable, 1233333)
      this.$refs.multipleTable && this.$refs.multipleTable.clearCheckboxRow();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      delete this.searchForm.forecastRateMax;
      delete this.searchForm.customerNumMax;
      delete this.searchForm.planTimeTo;
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'
        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      } else {
        this.materialGroupOptions = [];
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      if (
        this.searchForm.forecastRateMin &&
        this.searchForm.forecastRateMax &&
        this.searchForm.forecastRateMin > this.searchForm.forecastRateMax
      ) {
        this.$message.error({ message: '预测准确率最小值不能大于最大值！' });
        return;
      }
      if (
        this.searchForm.customerNumMin &&
        this.searchForm.customerNumMax &&
        this.searchForm.customerNumMin > this.searchForm.customerNumMax
      ) {
        this.$message.error({ message: '销售客户数最小值不能大于最大值！' });
        return;
      }
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      this.orderByField && (param.orderByField = this.orderByField);
      this.orderByAscOrDesc && (param.orderByAscOrDesc = this.orderByAscOrDesc);
      param.category[0] && (param.firstCatalogId = param.category[0]);
      param.category[1] && (param.secondCatalogId = param.category[1]);
      param.category[2] && (param.thirdCatalogId = param.category[2]);
      param.category[3] && (param.fourthCatalogId = param.category[3]);
      param.materialGroupIds = param?.materialGroupIds.map((a) => a.id)
      param.brandNo = param?.brandNo?.brandId || param?.brandNo || ''
      param.sku = param.sku ? param.sku.split(' ').filter(item => !!item) : []
      if (param.sku.length > 50) {
        this.$message.error({ message: 'SKU最多为50个！' });
        return;
      }
      this.basisList = [];
      this.basisListLoading = true;
      getSalesForecastList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              this.basisList = res.data;
              this.total = res.total;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    editDlgShow(id) {
      this.editVisible = true;
      this.editId = id;
    },
    editDlgHide(flag) {
      this.editVisible = false;
      flag && this.getList();
    },
    async openDetailDialog(id) {
      this.isOpenDetailDialog = !!id;
      this.openDetailData = [];
      if (id) {
        this.openDetailLoading = true;
        let res = await salesInfo({ id });
        this.openDetailLoading = false;
        if (res.code === 200) {
          this.openDetailData = res?.data?.details || [];
        } else {
          this.$message.error({ message: res.msg });
        }
      }
    },
    async handleOperate(check, action, id) {
      let ids = id ? [id] : this.selectRows.map((a) => a.id);
      if (ids.length === 0) {
        this.$message.warning({ message: '请先选中数据！' });
        return;
      }
      this.basisListLoading = true;
      let res = await approveSalesForecast({ action, ids }, { check });
      this.basisListLoading = false;
      if (res.code === 200) {
        this.$message.success({ message: '操作成功！' });
        this.getList();
        this.$refs.multipleTable && this.$refs.multipleTable.clearCheckboxRow();
      } else if (res.code === 208 && res.data) {
        this.$confirm(res.data, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        }).then(() => {
          this.handleOperate(false, action, id)
        })
      } else {
        this.$message.error({ message: res.msg });
      }
    },
    async handleSave(row) {
      let arr = row ? [row] : this.selectRows;
      if (arr.length === 0) {
        this.$message.warning({ message: '请先选中数据！' });
        return;
      }
      let params = arr.map((item) => {
        return { id: item.id, sku: item.sku };
      });
      this.basisListLoading = true;
      let res = await updateSalesForecastNum(params);
      this.basisListLoading = false;
      if (res.code === 200) {
        this.$message.success({ message: '操作成功！' });
        this.getList();
        this.$refs.multipleTable && this.$refs.multipleTable.clearCheckboxRow();
      } else {
        this.$message.error({ message: res.msg });
      }
    },
    openMsgDialog(data) {
      this.isOpenMsgDialog = !!data;
      this.openMsgData = data || [];
    },
    selectChange({ checked, records, row }) {
      this.selectRows = records;
    },
    selectAll({ checked, records }) {
      this.selectRows = records;
    },
    // 排序
    sortChange({ order, property }) {
      this.orderByAscOrDesc = order;
      this.orderByField = property;
      this.getList();
    },
    handleExport() {
      if (
        this.searchForm.forecastRateMin &&
        this.searchForm.forecastRateMax &&
        this.searchForm.forecastRateMin > this.searchForm.forecastRateMax
      ) {
        this.$message.error({ message: '预测准确率最小值不能大于最大值！' });
        return;
      }
      if (
        this.searchForm.customerNumMin &&
        this.searchForm.customerNumMax &&
        this.searchForm.customerNumMin > this.searchForm.customerNumMax
      ) {
        this.$message.error({ message: '销售客户数最小值不能大于最大值！' });
        return;
      }
      let param = { ...this.searchForm };
      this.orderByField && (param.orderByField = this.orderByField);
      this.orderByAscOrDesc && (param.orderByAscOrDesc = this.orderByAscOrDesc);
      param.category[0] && (param.firstCatalogId = param.category[0]);
      param.category[1] && (param.secondCatalogId = param.category[1]);
      param.category[2] && (param.thirdCatalogId = param.category[2]);
      param.category[3] && (param.fourthCatalogId = param.category[3]);
      param.materialGroupIds = param?.materialGroupIds.map((a) => a.id)
      param.brandNo = param?.brandNo?.brandId || param?.brandNo || ''
      param.sku = param.sku ? param.sku.split(' ').filter(item => !!item) : []
      if (param.sku.length > 50) {
        this.$message.error({ message: 'SKU最多为50个！' });
        return;
      }
      this.exportLoading = true;
      salesExport(param)
        .then((res) => {
          this.exportLoading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.exportLoading = false;
          this.$message.error({ message: '导出失败！' });
        });
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.importLoading = true;
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.code === 200) {
        this.$message.success(response.msg || '导入成功！');
      } else {
        this.$message.error((response && response.msg) || '导入失败！');
      }
    },
    onUploadError(error) {
      console.log(error);
      this.importLoading = false;
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    },
    toPurchase() {
      if (
        this.searchForm.forecastRateMin &&
        this.searchForm.forecastRateMax &&
        this.searchForm.forecastRateMin > this.searchForm.forecastRateMax
      ) {
        this.$message.error({ message: '预测准确率最小值不能大于最大值！' });
        return;
      }
      if (
        this.searchForm.customerNumMin &&
        this.searchForm.customerNumMax &&
        this.searchForm.customerNumMin > this.searchForm.customerNumMax
      ) {
        this.$message.error({ message: '销售客户数最小值不能大于最大值！' });
        return;
      }
      let params = {};
      Object.keys(this.searchForm).forEach((key) => {
        let value = this.searchForm[key];
        if (value === null || value === undefined || value.length === 0) {
          return;
        }
        params[key] = value;
      });
      params = encodeURIComponent(JSON.stringify(params));
      const path = `/purchasePlan/toPurchase/${params}`;
      this.$closeTag(path);
      setTimeout(() => {
        this.$router.push({
          path
        });
      }, 0);
    }
  }
};
</script>

<style lang="scss">
.salesForecast-list {
  .el-input-number {
    .el-input__inner {
      text-align: right;
    }
  }
  .el-form {
    .el-input-number {
      .el-input__inner {
        text-align: left;
      }
    }
  }
  .align-right {
    text-align: right;
  }
  .name {
    min-height: 32px;
    line-height: 16px;
    display: flex;
    align-items: center;
  }
}
</style>
