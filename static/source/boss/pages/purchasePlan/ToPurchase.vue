<template>
  <div class="app-container salesForecast-toPuchase">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="130px"
        label-position="right"
        :show-message="false"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="物料组：" prop="materialGroupIds">
              <el-select
                v-model="searchForm.materialGroupIds"
                filterable
                clearable
                placeholder="请输入物料组，支持多选"
                style="width: 100%"
                remote
                multiple
                reserve-keyword
                :remote-method="remoteMaterialGroupIdMethod"
                :loading="materialGroupIdLoading"
                value-key="id"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类型：" prop="type">
              <el-select
                v-model="searchForm.type"
                clearable
                placeholder="请选择类型"
                style="width: 100%"
              >
                <el-option label="年度" value="YEAR"></el-option>
                <el-option label="月度" value="MONTH"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌：" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
                value-key="brandId"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划月份：" prop="planTimeFrom">
              <el-date-picker
                clearable
                v-model="searchForm.planTimeFrom"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择起始月份"
              />
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-date-picker
                clearable
                v-model="searchForm.planTimeTo"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择结束月份"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="search-row" label="SKU：" prop="sku">
              <el-input
                v-model="searchForm.sku"
                placeholder="多个sku用空格隔开，最多50个"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类目：" prop="category">
              <el-cascader
                collapse-tags
                style="width: 100%"
                v-model="searchForm.category"
                ref="casRef"
                :options="categoryList"
                clearable
                filterable
                :props="cascaderProps"
                :emitPath="false"
                placeholder="请选择目录"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工厂：" prop="factory">
              <el-select
                v-model="searchForm.factory"
                filterable
                clearable
                placeholder="请输入关键词"
                style="width: 100%"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.factory"
                  :label="`${item.factory} ${item.factoryName}`"
                  :value="item.factory"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="累计预测准确率：" prop="forecastRateMin">
              <el-input-number
                v-model="searchForm.forecastRateMin"
                clearable
                placeholder="最小值"
                style="width: 45%"
                :controls="false"
                :min="0"
                :max="100"
              ></el-input-number>
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-input-number
                v-model="searchForm.forecastRateMax"
                clearable
                placeholder="最大值"
                style="width: 45%"
                :controls="false"
                :min="0"
                :max="100"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="销售客户数：" prop="customerNumMin">
              <el-input-number
                v-model="searchForm.customerNumMin"
                clearable
                placeholder="最小值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-input-number
                v-model="searchForm.customerNumMax"
                clearable
                placeholder="最大值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货库位：" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                clearable
                placeholder="请选择库存地点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in positions"
                  :key="item.position"
                  :value="item.position"
                  :label="`${item.position} ${item.positionName}`"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="建议运营策略：" prop="suggestedOperationType">
              <el-select
                v-model="searchForm.suggestedOperationType"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in suggestedOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <vxe-grid
        border
        resizable
        keep-source
        align="center"
        :max-height="screenHeight - 320"
        id="multipleTable"
        ref="multipleTable"
        :loading="basisListLoading"
        :custom-config="tableCustom"
        :data="basisList"
        :columns="columns"
        :toolbar-config="tableToolbar"
        :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
        :sort-config="{ remote: true }"
        @checkbox-all="selectAll"
        @checkbox-change="selectChange"
      >
        <template v-slot:toolbar_buttons>
          <div style="text-align: right">
            <el-button
              type="primary"
              :loading="basisListLoading"
              @click="toPuechaseFn(true)"
            >
              创建采购计划
            </el-button>
            <el-button
              :loading="basisListLoading || exportLoading"
              type="primary"
              @click="handleExport"
              >导出</el-button
            >
            <el-upload
              action="/api-ab/sales-forecast/purchase-plan/import"
              :show-file-list="false"
              :on-success="onUploadSuccess"
              :on-error="onUploadError"
              :before-upload="beforeUpload"
              accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              style="display: inline-block; margin-left: 10px"
              name="excelFile"
            >
              <el-button
                type="primary"
                style="margin-right: 5px"
                :loading="basisListLoading || importLoading"
                >导入采购计划</el-button
              >
            </el-upload>
          </div>
        </template>
        <template v-slot:sku="{ row }">
          <div style="display: flex; flex-wrap: wrap">
            {{ row.sku }}
            <el-tag
              size="small"
              v-for="item in row.tagsDesc"
              :key="item"
              style="margin-left: 6px; margin-bottom: 4px"
              >{{ item }}</el-tag
            >
          </div>
        </template>
        <template v-slot:fourth_catalog_name="{ row }">
          {{
            `${row.firstCatalogName} / ${row.secondCatalogName} / ${row.thirdCatalogName} / ${row.fourthCatalogName}`
          }}
        </template>
        <template v-slot:factory="{ row }"
          >{{ row.factory }}&ensp;{{ row.factoryName }}</template
        >
        <template v-slot:forecast_rate="{ row }">
          <el-link
            type="primary"
            @click="openMsgDialog(row.historyDataList || [])"
            >{{
              row.forecastRatePercent === null ? "--" : row.forecastRatePercent
            }}</el-link
          >
        </template>
        <template v-slot:avg_forecast_rate="{ row }">
          {{ row.avgForecastRatePercent === null ? "--" : row.avgForecastRatePercent }}
        </template>
        <template v-slot:customer_num="{ row }">
          {{
            row.customerNum === null || row.customerNum === undefined
              ? "--"
              : numFormatThousand(row.customerNum)
          }}
        </template>
        <template v-slot:number="{ row, column }">
          {{ row[column.property] === null ? "--" : numFormatThousand(row[column.property]) }}
        </template>
        <template v-slot:plan_qty_header="{ row }">
          <span class="tooltip-span">计划采购量</span>
          <el-tooltip effect="dark" content="计划采购量=计划月份预测销量-当前在途可用量-当前在库可用量-已审核计划采购量-待审核计划采购量+已采购量">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template v-slot:plan_qty="{ row }">
          <el-input-number
            v-model="row.planQty"
            style="width: 100%"
            :min="0"
            :precision="2"
            :controls="false"
          />
        </template>
        <template v-slot:actualPurchasePrice="{ row }">
          <div v-if="row.suggestedOperationType !== 'Bid'" style="display: flex;">
            <el-input-number
              v-model="row.actualPurchasePrice"
              style="width: 90%"
              :min="0"
              :precision="2"
              :controls="false"
            />
            <i v-if="row.actualPurchasePrice > row.purchasePrice" class="el-icon-top" style="color: #F56C6C;line-height: 32px"></i>
            <i v-if="row.actualPurchasePrice < row.purchasePrice" class="el-icon-bottom" style="color: #67C23A;line-height: 32px"></i>
          </div>
          <div v-else>
            <el-input
              v-model="row.actualPurchasePrice"
              style="width: 100%"
              :min="0"
              :precision="2"
              :controls="false"
              disabled
            />
          </div>
        </template>
        <template v-slot:suggested_operation_type="{ row }">
          <el-select
            v-model="row.suggestedOperationType"
            filterable
            style="width: 100%"
            @change="operationTypeChange(row)"
          >
            <el-option
              v-for="item in (row.type === 'YEAR' ? [suggestedOptions[2]]: suggestedOptions)"
              :key="item.key"
              :value="item.key"
              :label="item.label"
            >
            </el-option>
          </el-select>
        </template>
         <template v-slot:suggested_supplier_code="{ row }">
          <el-select
            v-if="['Consignment', 'Purchase'].includes(row.suggestedOperationType)"
            v-model="row.suggestedSupplier"
            filterable
            style="width: 100%"
            :loading="row.loading"
            @focus="handleFocus(row)"
            value-key="supplierCode"
          >
            <el-option
              v-for="item in row.suppliers"
              :disabled="row.suggestedOperationType === 'Consignment' && !item.consignment"
              :key="item.supplierCode"
              :value="item"
              :label="item.supplierName"
            >
            </el-option>
          </el-select>
        </template>

        <template v-slot:price="{ row }">
          {{ row.price === null ? "--" : numFormatThousand(row.price) }}
        </template>
        <template v-slot:total_price="{ row }">
          <span>{{
            row.totalPrice === null ? "--" : numFormatThousand(getPurchasePrice(row))
          }}</span>
        </template>
        <template v-slot:accept_rate="{ row }">
          {{ row.acceptRate === null ? "--" : row.acceptRate + "%" }}
        </template>
      </vxe-grid>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      width="600px"
      title="历史数据"
      class="msg-dialog"
      :visible.sync="isOpenMsgDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="openMsgDialog(null)"
    >
      <el-table :data="openMsgData" border fit>
        <el-table-column prop="forecastTime" label="时间" align="center" />
        <el-table-column prop="forecastQty" label="预测销量" align="right" />
        <el-table-column prop="actualQty" label="实际销量" align="right" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { numFormatThousand } from '@/utils/index';
import { toPurchaseColumns, suggestedOptions } from './const';
import { mapState } from 'vuex';
import {
  getCatalogTree,
  getAllFactory,
  getMaterialGroupListApi,
  getBrandListApi,
  salesToPurchaseList,
  salesToPurchaseExport,
  salesToPurchaseCreate,
  salesSupplier,
  getAllPosition
} from '@/api/purchasePlan';
import moment from 'moment';

export default {
  name: 'salesForecastList',
  data() {
    return {
      numFormatThousand,
      brandIdOptions: [],
      brandIdLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      categoryList: [],
      factoryList: [],
      positions: [],
      basisListLoading: false,
      basisList: [],
      selectRows: [],
      searchForm: {
        materialGroupIds: [],
        brandNo: '',
        planTimeFrom: '',
        planTimeTo: '',
        sku: '',
        category: [],
        factory: ''
      },
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      editVisible: false,
      editId: '',
      suggestedOptions,
      cascaderProps: {
        value: 'id',
        label: 'name',
        checkStrictly: true
      },
      isOpenMsgDialog: false,
      openMsgData: [],
      columns: toPurchaseColumns,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      screenHeight: document.body.clientHeight,
      exportLoading: false,
      importLoading: false,
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(this.searchForm.planTimeFrom)).getTime()
        }
      }
    };
  },
  computed: {
    ...mapState(['userRole']),
    isConfirm() {
      return !!~this.userRole.indexOf('备货策略审核');
    }
  },
  created() {
    Promise.all([
      getCatalogTree({ type: 0 }).then((res) => {
        if (res.success === true && res.data && res.data.length) {
          this.categoryList = this.getTreeData(res.data);
        }
      }),
      getAllFactory().then((res) => {
        if (res.status === 200) {
          this.factoryList = res.result;
        }
      }),
      getAllPosition().then((res) => {
        if (res.status === 200) {
          this.positions = res.result;
        }
      })
    ]);
    if (this.$route.params.ids) {
      let query = JSON.parse(decodeURIComponent(this.$route.params.ids))
      delete query.status
      if (!query?.planTimeFrom && !query?.planTimeTo) {
        this.searchForm.planTimeFrom = moment(new Date()).format('yyyy-MM')
      }
      this.searchForm = { ...this.searchForm, ...query }
      query?.brandNo?.brandName && this.remoteBrandIdMethod(query?.brandNo?.brandName)
      if (query?.materialGroupIds?.length > 0) {
        this.materialGroupOptions = query?.materialGroupIds
      }
      this.handleFilter();
    }
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
      this.$refs.multipleTable && this.$refs.multipleTable.clearCheckboxRow();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      delete this.searchForm.forecastRateMax;
      delete this.searchForm.customerNumMax;
      delete this.searchForm.customerNumMin;
      delete this.searchForm.customerNumMin;
      delete this.searchForm.planTimeTo;
    },
    getPurchasePrice(row) {
      if (!row.planQty || !row.actualPurchasePrice) return row.totalPrice >= 0 ? row.totalPrice : ''
      return (row.planQty * row.actualPurchasePrice)?.toFixed(2)
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'
        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      } else {
        this.materialGroupOptions = [];
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      if (
        this.searchForm.forecastRateMin &&
        this.searchForm.forecastRateMax &&
        this.searchForm.forecastRateMin > this.searchForm.forecastRateMax
      ) {
        this.$message.error({ message: '预测准确率最小值不能大于最大值！' });
        return;
      }
      if (
        this.searchForm.customerNumMin &&
        this.searchForm.customerNumMax &&
        this.searchForm.customerNumMin > this.searchForm.customerNumMax
      ) {
        this.$message.error({ message: '销售客户数最小值不能大于最大值！' });
        return;
      }
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      param.category[0] && (param.firstCatalogId = param.category[0]);
      param.category[1] && (param.secondCatalogId = param.category[1]);
      param.category[2] && (param.thirdCatalogId = param.category[2]);
      param.category[3] && (param.fourthCatalogId = param.category[3]);
      param.materialGroupIds = param?.materialGroupIds.map((a) => a.id)
      param.brandNo = param?.brandNo?.brandId || param?.brandNo || ''
      param.sku = param.sku ? param.sku.split(' ').filter(item => !!item) : []
      if (param.sku.length > 50) {
        this.$message.error({ message: 'SKU最多为50个！' });
        return;
      }
      this.basisList = [];
      this.basisListLoading = true;
      salesToPurchaseList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              this.basisList = res.data.map((a) => {
                return {
                  ...a,
                  suggestedSupplier: a.suggestedSupplierCode ? { supplierCode: a.suggestedSupplierCode, supplierName: a.suggestedSupplierName } : {},
                  suppliers: a.suggestedSupplierCode ? [{ supplierCode: a.suggestedSupplierCode, supplierName: a.suggestedSupplierName }] : []
                }
              });
              this.total = res.total;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    async toPuechaseFn(check) {
      let data = this.selectRows.map((a) => {
        return {
          brandNo: a.brandNo,
          factory: a.factory,
          inStockQty: a.inStockQty,
          materialGroupId: a.materialGroupId,
          onWayQty: a.onWayQty,
          planQty: a.planQty,
          planTime: a.forecastTime,
          position: a.position,
          salesForecastDetailId: a.salesForecastDetailId,
          sku: a.sku,
          suggestedOperationType: a.suggestedOperationType,
          suggestedSupplierCode: a?.suggestedSupplier?.supplierCode,
          suggestedSupplierName: a?.suggestedSupplier?.supplierName,
          type: a.type,
          actualPurchasePrice: a.suggestedOperationType !== 'Bid' ? a.actualPurchasePrice : ''
        }
      });
      if (this.selectRows.length === 0) {
        this.$message.warning({ message: '请先选中数据！' });
        return;
      }
      let errors = []
      data.forEach((a, i) => {
        if (a.suggestedOperationType !== 'Bid' && !a.suggestedSupplierCode) {
          errors.push(i + 1);
        }
      })
      if (errors.length) {
        this.$message.warning(
              `请先填写第${errors.join('、')}行建议供应商！`
            );
        return;
      }
      this.basisListLoading = true;
      let res = await salesToPurchaseCreate(data, { check });
      this.basisListLoading = false;
      if (res.code === 200) {
        this.$message.success({ message: '操作成功！' });
        this.getList();
        this.$refs.multipleTable && this.$refs.multipleTable.clearCheckboxRow();
      } else if (res.code === 208 && res.data) {
        this.$confirm(res.data, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        }).then(() => {
          this.toPuechaseFn(false)
        })
      } else {
        this.$message.error({ message: res.msg });
      }
    },
    openMsgDialog(data) {
      this.isOpenMsgDialog = !!data;
      this.openMsgData = data || [];
    },
    selectChange({ checked, records, row }) {
      this.selectRows = records;
    },
    selectAll({ checked, records }) {
      this.selectRows = records;
    },
    handleExport() {
      if (
        this.searchForm.forecastRateMin &&
        this.searchForm.forecastRateMax &&
        this.searchForm.forecastRateMin > this.searchForm.forecastRateMax
      ) {
        this.$message.error({ message: '预测准确率最小值不能大于最大值！' });
        return;
      }
      if (
        this.searchForm.customerNumMin &&
        this.searchForm.customerNumMax &&
        this.searchForm.customerNumMin > this.searchForm.customerNumMax
      ) {
        this.$message.error({ message: '销售客户数最小值不能大于最大值！' });
        return;
      }
      let param = { ...this.searchForm };
      param.category[0] && (param.firstCatalogId = param.category[0]);
      param.category[1] && (param.secondCatalogId = param.category[1]);
      param.category[2] && (param.thirdCatalogId = param.category[2]);
      param.category[3] && (param.fourthCatalogId = param.category[3]);
      param.materialGroupIds = param?.materialGroupIds.map((a) => a.id)
      param.brandNo = param?.brandNo?.brandId || param?.brandNo || ''
      param.sku = param.sku ? param.sku.split(' ').filter(item => !!item) : []
      if (param.sku.length > 50) {
        this.$message.error({ message: 'SKU最多为50个！' });
        return;
      }
      this.exportLoading = true;
      salesToPurchaseExport(param)
        .then((res) => {
          this.exportLoading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.exportLoading = false;
          this.$message.error({ message: '导出失败！' });
        });
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.importLoading = true;
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.code === 200) {
        this.$message.success(response.msg || '导入成功！');
        this.getList();
      } else {
        this.$message.error((response && response.msg) || '导入失败！');
      }
    },
    onUploadError(error) {
      console.log(error);
      this.importLoading = false;
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    },
    async handleFocus(row) {
      if (row.getSuppliers) {
        return
      }
      this.$set(row, 'loading', true)
      let res = await salesSupplier({ factory: row.factory, sku: row.sku });
      this.$set(row, 'loading', false)
      if (res.code === 200) {
        this.$set(row, 'suppliers', res.data || [{ supplierCode: row.suggestedSupplierCode, supplierName: row.suggestedSupplierName }])
        this.$set(row, 'getSuppliers', true)
      } else {
        this.$set(row, 'suppliers', [{ supplierCode: row.suggestedSupplierCode, supplierName: row.suggestedSupplierName }])
        this.$message.error({ message: res.msg });
      }
    },
    operationTypeChange(row) {
      this.$set(row, 'suggestedSupplier', null)
      this.$set(row, 'actualPurchasePrice', '')
    }
  }
};
</script>

<style lang="scss">
.salesForecast-toPuchase {
  .el-input-number {
    .el-input__inner {
      text-align: right;
    }
  }
  .el-form {
    .el-input-number {
      .el-input__inner {
        text-align: left;
      }
    }
  }
  .align-right {
    text-align: right;
  }
  .name {
    min-height: 32px;
    line-height: 16px;
    display: flex;
    align-items: center;
  }
}
</style>
