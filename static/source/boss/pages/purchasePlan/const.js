export const saleColumns = [{
    type: 'checkbox',
    width: 40,
    fixed: 'left'
  },
  {
    field: 'typeName',
    title: '类型',
    width: 80,
    showOverflow: 'tooltip'
  },
  {
    field: 'sku',
    title: 'SKU',
    width: 150,
    align: 'left',
    slots: {
      default: 'sku'
    }
  },
  {
    field: 'skuName',
    title: '物料描述',
    width: 160,
    showOverflow: 'tooltip'
  },
  {
    field: 'brandName',
    title: '品牌',
    width: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'materialGroup',
    title: '物料组',
    width: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'fourthCatalogName',
    title: '类目',
    width: 200,
    align: 'left',
    showOverflow: 'tooltip',
    slots: {
      default: 'fourth_catalog_name'
    }
  },
  {
    field: 'factory',
    title: '工厂',
    width: 160,
    align: 'left',
    showOverflow: 'tooltip',
    slots: {
      default: 'factory'
    }
  },
  {
    field: 'forecastRate',
    title: '累计预测准确率(近三个月)',
    width: 100,
    align: 'right',
    slots: {
      default: 'forecast_rate'
    }
  },
  {
    field: 'avgForecastRate',
    title: '平均预测准确率(近三个月)',
    width: 100,
    align: 'right',
    slots: {
      default: 'avg_forecast_rate'
    }
  },
  {
    field: 'customerNum',
    title: '销售客户数(近三个月)',
    width: 90,
    align: 'right',
    slots: {
      default: 'customer_num'
    }
  },
  {
    field: 'forecastTime',
    title: '计划月份',
    width: 80,
    showOverflow: 'tooltip'
  },
  {
    field: 'forecastSalesQty',
    title: '计划月份预测销量',
    width: 150,
    align: 'right',
    sortable: true,
    slots: {
      default: 'forecast_sales_qty'
    }
  },
  {
    field: 'unit',
    title: '单位',
    width: 60
  },
  {
    field: 'onWayQty',
    title: '当前在途可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'inStockQty',
    title: '当前在库可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'forecastPurchaseQty',
    title: '预测采购量',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  // {
  //   field: 'planQty',
  //   title: '计划采购量',
  //   width: 130,
  //   align: 'right',
  //   sortable: true,
  //   slots: {
  //     default: 'plan_qty'
  //   }
  // },
  {
    field: 'price',
    title: '参考单价',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  // {
  //   field: 'totalPrice',
  //   title: '参考计划采购金额',
  //   width: 120,
  //   align: 'right',
  //   slots: {
  //     default: 'total_price'
  //   }
  // },
  // {
  //   field: 'acceptRate',
  //   title: '采纳比率',
  //   width: 80,
  //   align: 'right',
  //   slots: {
  //     default: 'accept_rate'
  //   }
  // },
  {
    field: 'totalPrice',
    title: '预测采购金额',
    width: 120,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'approvedPlanQty',
    title: '已审核计划采购量',
    width: 120,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'status',
    title: '状态',
    width: 80,
    slots: {
      default: 'status'
    }
  },
  {
    field: 'action',
    title: '操作',
    width: 140,
    align: 'left',
    slots: {
      default: 'action'
    }
  }
]

export const saleDetailColumns = [{
    field: 'stockUpPosition',
    title: '备货库位编号',
    width: 100,
    showOverflow: 'tooltip'
  },
  {
    field: 'stockUpPositionName',
    title: '备货仓名称',
    width: 140,
    showOverflow: 'tooltip'
  },
  {
    field: 'forecastSalesQty',
    title: '计划月份预测销量',
    width: 120,
    showOverflow: 'tooltip',
    align: 'right'
  },
  {
    field: 'unit',
    title: '单位',
    width: 50,
    align: 'center',
    showOverflow: 'tooltip'
  },
  {
    field: 'suggestedSupplierName',
    title: '建议供应商',
    showOverflow: 'tooltip'
  },
  {
    field: 'approvedPlanQty',
    title: '已审核计划采购量',
    width: 120,
    align: 'right'
  },
  {
    field: 'nonApprovedPlanQty',
    title: '待审核计划采购量',
    width: 120,
    align: 'right'
  }
]

export const toPurchaseColumns = [{
    type: 'checkbox',
    width: 40,
    fixed: 'left'
  },
  {
    field: 'typeName',
    title: '类型',
    width: 80,
    showOverflow: 'tooltip'
  },
  {
    field: 'sku',
    title: 'SKU',
    width: 150,
    align: 'left',
    slots: {
      default: 'sku'
    }
  },
  {
    field: 'skuName',
    title: '物料描述',
    width: 160,
    showOverflow: 'tooltip'
  },
  {
    field: 'position',
    title: '备货库位编号',
    width: 100,
    showOverflow: 'tooltip'
  },
  {
    field: 'positionName',
    title: '备货仓名称',
    width: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'brandName',
    title: '品牌',
    width: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'materialGroup',
    title: '物料组',
    width: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'fourthCatalogName',
    title: '类目',
    width: 200,
    align: 'left',
    showOverflow: 'tooltip',
    slots: {
      default: 'fourth_catalog_name'
    }
  },
  {
    field: 'factory',
    title: '工厂',
    width: 160,
    align: 'left',
    showOverflow: 'tooltip',
    slots: {
      default: 'factory'
    }
  },
  {
    field: 'forecastRatePercent',
    title: '累计预测准确率(近三个月)',
    width: 100,
    align: 'right',
    slots: {
      default: 'forecast_rate'
    }
  },
  {
    field: 'avgForecastRatePercent',
    title: '平均预测准确率(近三个月)',
    width: 100,
    align: 'right',
    slots: {
      default: 'avg_forecast_rate'
    }
  },
  {
    field: 'customerNum',
    title: '销售客户数(近三个月)',
    width: 90,
    align: 'right',
    slots: {
      default: 'customer_num'
    }
  },
  {
    field: 'forecastTime',
    title: '计划月份',
    width: 80,
    showOverflow: 'tooltip'
  },
  {
    field: 'forecastSalesQty',
    title: '计划月份预测销量',
    width: 150,
    align: 'right',
    sortable: true,
    slots: {
      default: 'number'
    }
  },
  {
    field: 'unit',
    title: '单位',
    width: 60
  },
  {
    field: 'onWayQty',
    title: '当前在途可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'inStockQty',
    title: '当前在库可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'forecastPurchaseQty',
    title: '预测采购量',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'approvedPlanQty',
    title: '已审核计划采购量',
    width: 120,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'nonApprovedPlanQty',
    title: '待审核计划采购量',
    width: 120,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'purchasedQty',
    title: '已采购量',
    width: 100,
    slots: {
      default: 'number'
    }
  },
  {
    field: 'price',
    title: '参考单价',
    width: 100,
    slots: {
      default: 'price'
    }
  },
  {
    field: 'actualPurchasePrice',
    title: '实际采购价格',
    width: 120,
    align: 'right',
    slots: {
      default: 'actualPurchasePrice'
    }
  },
  {
    field: 'totalPrice',
    title: '计划采购金额',
    width: 120,
    slots: {
      default: 'total_price'
    }
  },
  {
    field: 'planQty',
    title: '计划采购量',
    width: 120,
    fixed: 'right',
    slots: {
      default: 'plan_qty',
      header: 'plan_qty_header'
    }
  },
  {
    field: 'suggestedOperationType',
    title: '建议运营策略',
    width: 120,
    fixed: 'right',
    slots: {
      default: 'suggested_operation_type'
    }
  },
  {
    field: 'suggestedSupplierCode',
    title: '建议供应商',
    width: 140,
    fixed: 'right',
    slots: {
      default: 'suggested_supplier_code'
    }
  }
]

export const typeOptions = [{
    key: 'YEAR',
    label: '年度'
  },
  {
    key: 'MONTH',
    label: '月度'
  }
]

export const suggestedOptions = [{
    key: 'Consignment',
    label: '寄售补货'
  },
  {
    key: 'Purchase',
    label: '直接采购'
  },
  {
    key: 'Bid',
    label: '招投标'
  }
]

export const statusOptions = [{
    key: 0,
    label: '草稿'
  },
  {
    key: 1,
    label: '待审核'
  },
  {
    key: 2,
    label: '已通过'
  },
  {
    key: -1,
    label: '已驳回'
  }
]
export const confirmStatusOptions = [{
  key: 0,
  label: '待确认'
},
{
  key: 1,
  label: '已确认'
},
{
  key: -1,
  label: '已驳回'
}
]

export const typeMap = {
  'YEAR': '年度',
  'MONTH': '月度'
}

export const suggestedMap = {
  'Consignment': '寄售补货',
  'Purchase': '直接采购',
  'Bid': '招投标'
}

export const statusMap = {
  '0': '草稿',
  '1': '待审核',
  '2': '已通过',
  '-1': '已驳回',
  '-2': '已作废'
}

export const sourceMap = {
  'strategy': '战略备货',
  'page': '人工',
  'forecast': '销量预测'
}

export const purchaseDetailColumns = [{
    field: 'planItemNo',
    title: '行号',
    width: 60,
    fixed: 'left'
  },
  {
    field: 'sku',
    title: 'SKU',
    width: 150,
    align: 'left',
    slots: {
      default: 'sku'
    }
  },
  {
    field: 'skuName',
    title: '物料描述',
    width: 140,
    showOverflow: 'tooltip'
  },
  {
    field: 'skuName',
    title: '类目',
    width: 140,
    showOverflow: 'tooltip',
    slots: {
      default: 'fourth_catalog_name'
    }
  },
  {
    field: 'itemType',
    title: '数据类型',
    width: 100,
    showOverflow: 'tooltip'
  },
  {
    field: 'stockReason',
    title: '备货原因',
    width: 140,
    showOverflow: 'tooltip',
    slots: {
      default: 'stockReason'
    }
  },
  {
    field: 'position',
    title: '库存地点',
    width: 200,
    showOverflow: 'tooltip',
    slots: {
      default: 'position'
    }
  },
  {
    field: 'forecastSalesQty',
    title: '预测销量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'inStockQty',
    title: '在库可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'moqAndMpq',
    title: 'MOQ/MPQ',
    width: 110,
    align: 'right'
  },
  {
    field: 'onWayQty',
    title: '在途可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'forecastPurchaseQty',
    title: '预测采购量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'planQty',
    title: '计划采购量',
    width: 110,
    align: 'right',
    slots: {
      default: 'planQty'
    }
  },
  {
    field: 'aiQty',
    title: 'AI计算采购量',
    width: 110,
    align: 'right'
  },
  {
    field: 'unit',
    title: '单位',
    width: 80
  },
  {
    field: 'price',
    title: '参考单价',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'actualPurchasePrice',
    title: '实际采购价格',
    width: 110,
    align: 'right',
    slots: {
      default: 'actualPurchasePrice'
    }
  },
  {
    field: 'planPrice',
    title: '计划采购额',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'supplierCode',
    title: '供应商',
    width: 200,
    slots: {
      default: 'supplier'
    }
  },
  {
    field: 'beforeStockDays',
    title: '备货前周转天数',
    align: 'right',
    width: 110
  },
  {
    field: 'afterStockDays',
    title: '备货后预计库存周转天数',
    align: 'right',
    width: 110
  },
  {
    field: 'sendQty',
    title: '已下发招标量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'sendPrice',
    title: '已下发招标金额',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'purchasedQty',
    title: '已采购量',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'purchasedPrice',
    title: '已采购金额',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'confirmStatusDesc',
    title: '明细确认状态',
    align: 'center',
    width: 100
  },
  {
    field: 'rejectReason',
    title: '驳回原因',
    width: 140,
    showOverflow: 'tooltip'
  }
]
export const purchaseDetaulListColumns = [
  {
    type: 'checkbox',
    width: 40,
    fixed: 'left'
  },
  {
    field: 'planOrderNo',
    title: '计划单号',
    width: 100
  },
  {
    field: 'confirmStatusDesc',
    title: '明细确认状态',
    align: 'center',
    width: 100
  },
  {
    field: 'planTime',
    title: '计划年月',
    width: 80
  },
  {
    field: 'factory',
    title: '工厂',
    width: 160,
    align: 'left',
    showOverflow: 'tooltip',
    slots: {
      default: 'factory'
    }
  },
  {
    field: 'typeName',
    title: '类型',
    width: 80,
    showOverflow: 'tooltip'
  },
  {
    field: 'operationTypeName',
    title: '运营策略',
    width: 80,
    showOverflow: 'tooltip'
  },
  {
    field: 'materialGroup',
    title: '物料组',
    width: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'brandName',
    title: '品牌',
    width: 120,
    showOverflow: 'tooltip'
  },
  {
    field: 'skuName',
    title: '类目',
    width: 140,
    showOverflow: 'tooltip',
    slots: {
      default: 'fourth_catalog_name'
    }
  },
  {
    field: 'statusDesc',
    title: '计划单状态',
    width: 80
  },
  {
    field: 'planItemNo',
    title: '行号',
    width: 60
  },
  {
    field: 'sku',
    title: 'SKU',
    width: 150,
    align: 'left',
    slots: {
      default: 'sku'
    }
  },
  {
    field: 'skuName',
    title: '物料描述',
    width: 160,
    showOverflow: 'tooltip'
  },
  {
    field: 'positionName',
    title: '库存地点',
    width: 200,
    showOverflow: 'tooltip'
  },
  {
    field: 'forecastSalesQty',
    title: '预测销量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'inStockQty',
    title: '在库可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'onWayQty',
    title: '在途可用量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'onWayQty',
    title: '建议采购量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'planQty',
    title: '计划采购量',
    width: 120,
    slots: {
      default: 'number'
    }
  },
  {
    field: 'unit',
    title: '单位',
    width: 60
  },
  {
    field: 'price',
    title: '参考单价',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'actualPurchasePrice',
    title: '实际采购价格',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'planPrice',
    title: '计划采购额',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'supplierName',
    title: '供应商',
    width: 200
  },
  {
    field: 'beforeStockDays',
    title: '备货前周转天数',
    align: 'right',
    width: 110
  },
  {
    field: 'afterStockDays',
    title: '备货后预计库存周转天数',
    align: 'right',
    width: 110
  },
  {
    field: 'risk',
    title: '备货风险',
    width: 110
  },
  {
    field: 'sendQty',
    title: '已下发招标量',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'sendPrice',
    title: '已下发招标金额',
    width: 110,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'purchasedQty',
    title: '已采购量',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'purchasedPrice',
    title: '已采购金额',
    width: 100,
    align: 'right',
    slots: {
      default: 'number'
    }
  },
  {
    field: 'action',
    title: '操作',
    width: 100,
    fixed: 'right',
    slots: {
      default: 'action'
    }
  }
]
// 战略备货相关常量
// 状态枚举
export const stockStatusOptions = [{
  key: 'New',
  label: '草稿'
},
{
  key: 'Calculating',
  label: 'AI试算中'
},
{
  key: 'Done',
  label: '试算完成'
},
{
  key: 'PurchasePland',
  label: '已转采购计划'
}]
// 备货类型枚举
export const stockTypeOptions = [{
  key: 'Yhl',
  label: '压货类备货'
},
{
  key: 'Jcb',
  label: '降本类备货'
},
{
  key: 'Yqxlzz',
  label: '预期销量增长'
},
{
  key: 'Gyscdq',
  label: '供应市场短缺'
},
{
  key: 'Jjxbh',
  label: '不可抗力等紧急性备货'
}]

// 备货推荐算法枚举
export const stockRecommendOptions = [{
  key: 'Money',
  label: '满足目标金额'
},
{
  key: 'TurnOver',
  label: '满足周转天数'
}]

// 运营策略枚举
export const operationTypes = [{
  key: 'Consignment',
  label: '寄售'
},
{
  key: 'Purchase',
  label: '直接采购'
}]
// 目标商品类型
export const stockRecommendSkuTypeList = [{
  key: 'System_Recommend',
  label: '系统推荐SKU'
},
{
  key: 'Manual_Appoint',
  label: '人工指定SKU'
}]
