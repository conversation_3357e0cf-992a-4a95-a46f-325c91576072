<template>
  <el-select
    :style="{width:width}"
    :value="value"
    ref="selectSecurityUser"
    filterable
    remote
    clearable
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :loading="loading"
  >
    <el-option
      v-for="item in userOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value"></el-option>
  </el-select>
</template>
<script>
import {
  getAccountByName
} from '@/components/SearchFields/api'
import {
  spDebounce
} from '@/utils/index'
export default {
  name: 'SelectSecurityUser',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    width: {
      type: String,
      default: 'auto'
    },
    value: [ String, Number ],
    pageNo: {
      type: Number,
      default: 1
    },
    rowCount: {
      type: Number,
      default: 10
    },
    getLabel: {
      type: <PERSON>olean,
      default: false
    },
    username: {
      type: String,
      default: ''
    }
  },
  created () {
    this.remoteMethod = spDebounce(this.remoteMethod)
    // if (this.props.username) {
    //   this.remoteMethod(this.props.userName)
    // }
  },
  mounted() {

  },
  watch: {
    username(val) {
      if (this.value && val) { // 初始有值时无法回显，手动push初始选项到options里
        if (!this.userOptions.find(item => item.label === val)) {
          this.userOptions.push({
            value: this.value,
            label: val
          })
        }
      }
    }
  },
  data () {
    return {
      userOptions: [],
      loading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      let value
      try {
        value = this.userOptions.filter(item => item.value === val)[0]
      } catch (err) { console.log(err) }
      this.$emit('change', val, value)
    },
    // 远程查找用户
    remoteMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading = true
        getAccountByName(key).then(res => {
          if (res && res.length) {
            this.userOptions = res.map(item => {
              return {
                value: item.username,
                label: item.nickname,
                ...item
              }
            });
          } else {
            this.userOptions = []
          }
        }).finally(() => {
          this.loading = false
        })
      } else {
        this.userOptions = []
      }
    }
  }
}
</script>
