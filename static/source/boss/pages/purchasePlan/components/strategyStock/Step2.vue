<template>
  <el-card class="mt-20px">
    <h3>
      AI推荐结果
      <span class="font-size-12 color-#888 font-400">可修改或新增SKU、配置实际参考单价、修改备货库位后，进行二次AI试算。</span>
    </h3>
    <div class="flex" style="margin-top: 12px;">
      <div class="flex h-32px items-center">
        <label>战略备货编号：</label>
        <span style="color: #FF0000">{{ detailInfo.taskId }}</span>
      </div>
      <div class="flex pl-24px h-32px items-center">
        <label>目标推荐算法名：</label>
        <span style="color: #FF0000">{{ detailInfo.stockRecommendAlgorithm === 'Money' ? '满足目标金额' : '满足目标周转天数' }}</span>
      </div>
    </div>
    <div class="flex">
      <div class="flex h-32px items-center">
        <label>工厂：</label>
        <span style="color: #FF0000">{{ factory }}</span>
      </div>
      <div class="flex pl-24px h-32px items-center">
        <label>物料组：</label>
        <span style="color: #FF0000">{{ detailInfo.materialGroup }}</span>
      </div>
      <div v-if="detailInfo.stockRecommendAlgorithm === 'Money'" class="flex pl-24px h-32px items-center">
        <label>供应商：</label>
        <span style="color: #FF0000">{{ detailInfo.supplierName + '('+ detailInfo.supplierNo +')' }}</span>
      </div>
      <div class="flex pl-24px h-32px items-center">
        <label>运营策略：</label>
        <span style="color: #FF0000">{{ operationType }}</span>
      </div>
      <div class="flex pl-24px h-32px items-center">
        <label>战略备货类型：</label>
        <span style="color: #FF0000">{{ stockStrategyType }}</span>
      </div>
    </div>
    <div class="flex h-32px items-center">
      <label>推荐方式：</label>
      <span style="color: #FF0000">{{ isManualAppoint ? '人工指定SKU' : 'AI推荐SKU' }}</span>
    </div>
    <DividerHeader>备货信息总览</DividerHeader>
    <div class="flex">
      <h4>备货收益</h4>
      <div class="flex-1 flex flex-wrap">
        <div v-if="detailInfo.stockRecommendAlgorithm === 'Money'" class="flex pl-24px h-32px items-center">
          <label>目标备货金额：</label>
          <span style="color: #FF0000">{{ detailInfo.stockTurnoverAmount }}</span>
        </div>
        <div v-else class="flex pl-24px h-32px items-center">
          <label>目标备货周转天数：</label>
          <span style="color: #FF0000">{{ detailInfo.aiStockTurnoverDays }}</span>
        </div>
        <div class="flex pl-24px h-32px items-center">
          <label>计划备货总金额：</label>
          <span style="color: #FF0000">{{ detailInfo.aiStockTotalAmount }}</span>
        </div>
        <div class="flex pl-24px h-32px items-center">
          <label>计划备货SKU总数量：</label>
          <span style="color: #FF0000">{{ detailInfo.aiStockTotalSku }}</span>
        </div>
        <div class="flex pl-24px h-32px items-center">
          <label>计划备货SKU+DC总数量：</label>
          <span style="color: #FF0000">{{ detailInfo.aiStockTotalSkuDc }}</span>
        </div>
        <div class="flex pl-24px h-32px items-center">
          <label>备货后总周转天数：</label>
          <span style="color: #FF0000">{{ detailInfo.aiStockTotalTurnoverDays }}</span>
        </div>
        <div class="flex pl-24px h-32px items-center">
          <label>备货前总运营成本：</label>
          <span style="color: #FF0000">{{ detailInfo.aiBeforeStockOperationCost }}</span>
        </div>
        <div class="flex pl-24px h-32px items-center">
          <label>备货后总运营成本：</label>
          <span style="color: #FF0000">{{ detailInfo.aiAfterStockOperationCost }}</span>
        </div>
      </div>
    </div>
    <div class="flex">
      <h4 style="flex-shrink: 0;">备货风险</h4>
      <div v-if="chartDatas.length" class="flex-1 flex">
        <div v-for="(item, key) in chartDatas" :id="`chart${key}`" :key="key" style="width: 320px; height: 260px;"></div>
      </div>
    </div>
    <DividerHeader  style="margin-top: 24px;">
      <span>备货信息明细</span>
    </DividerHeader>
    <div v-loading="importLoading">
      <div class="flex button-wrap">
        <el-button :disabled="disabled" type="primary" @click="addItem">新增</el-button>
        <el-button type="primary" @click="handleExport({ taskId: taskId })">导出明细</el-button>
        <el-button type="danger" :loading="delLoading" :disabled="disabled" @click="handleDelete">批量删除</el-button>
        <el-dropdown style="margin-left: 10px;margin-right: 10px;">
          <el-button type="primary" :disabled="disabled" :loading="importLoading">批量导入<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-upload
                :action="`/api-ab/stockStrategyItem/ai/import?taskId=${taskId}`"
                :show-file-list="false"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :before-upload="beforeUpload"
                accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                style="display: inline-block; margin-left: 10px"
                name="file"
              >
                <el-button
                  type="text"
                  style="margin-right: 5px"
                  :loading="importLoading"
                  :disabled="disabled"
                  >导入</el-button
                >
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button type="text" :disabled="disabled" :loading="importLoading"  @click="handleDownloadTemplate">
                下载模版
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" :disabled="disabled" @click="handlePreview">AI试算</el-button>
      </div>
      <el-table
        v-loading="listLoading"
        border
        :data="list"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="55" align="center" />
        <el-table-column label="*备货SKU" width="200" align="center">
          <template slot="header">
            <span class="required">备货SKU</span>
          </template>
          <template slot-scope="{row,$index}">
            <div class="flex justify-center">
              <el-select
                v-model="row.sku"
                filterable
                placeholder="请输入SKU"
                style="width: 118px"
                remote
                :disabled="(isManualAppoint && row.id > 0) || disabled"
                :remote-method="(val) => remoteSku(val, row)"
                :loading="row.skuLoading"
                @change="(val) => skuChange(val, $index)"
              >
                <el-option
                  v-for="item in row.skuList"
                  :key="item.skuNo"
                  :label="`${item.skuNo}`"
                  :value="item.skuNo"
                >
                </el-option>
              </el-select>
              <div v-if="row.tags && row.tags.length > 0">
                <el-tag type="danger" size="mini" style="margin-left: 4px;margin-top: 2px;" v-for="item in row.tags" :key="item">{{ item }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="skuDesc" width="200" label="物料描述" show-overflow-tooltip />
        <el-table-column align="center" prop="sourceTypeName" width="100" label="商品来源" show-overflow-tooltip />
        <el-table-column align="center" prop="itemType" width="100" label="数据类型" show-overflow-tooltip />
        <el-table-column align="center" prop="status" width="100" label="推荐状态" show-overflow-tooltip >
          <template slot-scope="{ row }">
            {{ (statusOptions.find(item => item.key === row.status) || {}).label }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="remark" width="100" label="修改备注" show-overflow-tooltip />
        <el-table-column v-if="detailInfo.stockRecommendAlgorithm  === 'TurnOver'" align="center" prop="supplierNo" width="140" label="*供应商" show-overflow-tooltip>
          <template slot="header">
            <span class="required">供应商</span>
          </template>
          <template slot-scope="{row,$index}">
            <Supplier @change="(val) => supplierChange(val ,$index)" :disabled="disabled" v-model="row.supplierNo" :key="row.sku" :sku-no="row.sku" :factory="detailInfo.factory" />
          </template>
        </el-table-column>
        <el-table-column v-if="detailInfo.stockRecommendAlgorithm  !== 'TurnOver'" align="center" prop="supplierNo" width="140" label="供应商" show-overflow-tooltip>
          <template slot-scope>
            {{ detailInfo.supplierNo + ' ' + (detailInfo.supplierName || '') }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="purchasePrice" width="120" label="参考单价（基本单位）" show-overflow-tooltip />
        <el-table-column label="*实际采购价格（基本单位）" width="140" align="center">
          <template slot="header">
            <span class="required">实际采购价格（基本单位）</span>
          </template>
          <template slot-scope="{row}">
            <div class="flex">
              <el-input-number
                style="width: 90%;"
                v-model="row.actualPurchasePrice"
                :disabled="disabled"
                @change="(val) => handleChange(row)"
                :controls="false"
                :min="0"
              />
              <i v-if="row.actualPurchasePrice > row.purchasePrice" class="el-icon-top" style="color: #F56C6C;line-height: 32px"></i>
              <i v-if="row.actualPurchasePrice < row.purchasePrice" class="el-icon-bottom" style="color: #67C23A;line-height: 32px"></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="*计划采购量" width="140" align="center">
          <template slot="header">
            <span class="required">计划采购量</span>
          </template>
          <template slot-scope="{row}">
            <el-input-number
              style="width: 100%;"
              v-model="row.planQty"
              :disabled="disabled"
              @change="(val) => handleChange(row)"
              :controls="false"
              :min="0"
            />
          </template>
        </el-table-column>
        <el-table-column label="计划采购额" prop="purchasePrice" width="140" align="center">
          <template slot-scope="{row}">
            {{ getPurchasePrice(row) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="aiQty" width="140" label="AI试算采购量" show-overflow-tooltip />
        <el-table-column align="center" prop="purchaseMoq" width="120" label="采购MOQ" show-overflow-tooltip />
        <el-table-column align="center" prop="purchaseMpq" width="120" label="采购MPQ" show-overflow-tooltip />
        <el-table-column label="*库位" width="200" align="center">
          <template slot="header">
            <span class="required">库位</span>
          </template>
          <template slot-scope="{row,$index}">
            <el-select
              v-model="row.position"
              filterable
              placeholder="请选择库位"
              style="width: 100%"
              :disabled="(isManualAppoint && row.id > 0) || disabled"
              :loading="row.positionLoading"
              @change="(val) => positionChange(val, $index)"
            >
              <el-option
                v-for="a in positionList"
                :key="a.code"
                :value="a.code"
                :label="`${a.code} ${a.name}`"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="inStockQty" width="120" label="在库可用量" show-overflow-tooltip />
        <el-table-column align="center" prop="onWayQty" width="120" label="在途可用量" show-overflow-tooltip />
        <el-table-column align="center" prop="forecastPurchaseQty" width="120" label="预测采购量" show-overflow-tooltip>
          <template slot="header">
            <span class="tooltip-span">预测采购量</span>
            <el-tooltip effect="dark" content="预测采购量=备货数量+在库可用量+在途可用量">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ getForecastPurchaseQty(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="afterStockInventoryQty" width="120" label="备货后库存" show-overflow-tooltip />
        <el-table-column align="center" prop="beforeStockInventoryAmount" width="120" label="备货前库存金额" show-overflow-tooltip />
        <el-table-column align="center" prop="afterStockInventoryAmount" width="120" label="备货后库存金额" show-overflow-tooltip />
        <el-table-column align="center" prop="beforeStockOperationCost" width="120" label="备货前运营成本" show-overflow-tooltip />
        <el-table-column align="center" prop="afterStockOperationCost" width="120" label="备货后运营成本" show-overflow-tooltip />
        <el-table-column align="center" prop="beforeStockTurnoverDays" width="120" label="备货前周转（天）" show-overflow-tooltip />
        <el-table-column align="center" prop="afterStockTurnoverDays" width="120" label="备货后周转（天）" show-overflow-tooltip />
      </el-table>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script>
import DividerHeader from '@/components/DividerHeader';
import Supplier from '@/pages/stockpileStrategy/components/selectSkuSupplier.vue';
import {
  calculateStrategyStock,
  getPositionByFactory,
  getStrategyHeaderForStep2,
  updateStrategyItemForStep2,
  deleteStrategyItemForStep2,
  checkStrategyItemForStep2Compare,
  getPurchasePlanSkuInfo,
  exportStrategyItemForStep2,
  getStrategyItemForStep2,
  getPieData,
  getAllFactory,
  downloadStrategyItemForStep2
} from '@/api/purchasePlan'
import { searchSkuList } from '@/api/orderSale';
import { stockStatusOptions, stockTypeOptions, operationTypes } from '../../const'
const echarts = window.echarts
export default {
  props: {
    taskId: String,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    DividerHeader,
    Supplier
  },
  computed: {
    isManualAppoint() {
      return this.detailInfo.stockRecommendSkuType === 'Manual_Appoint'
    },
    factory() {
      return this.factoryList?.find(item => item.factory === this.detailInfo.factory)?.factoryName
    },
    operationType() {
      return this.operationTypes.find(item => item.key === this.detailInfo.operationType)?.label
    },
    stockStrategyType() {
      return this.stockTypeOptions.find(item => item.key === this.detailInfo.stockStrategyType)?.label
    }
  },
  data() {
    return {
      chart: null,
      importLoading: false,
      listLoading: false,
      delLoading: false,
      detailInfo: {},
      list: [],
      listQueryInfo: {
        pageNum: 1,
        pageSize: 20
      },
      originPageInfo: {
        pageNum: 1,
        pageSize: 20
      },
      chartDatas: [],
      total: 0,
      positionList: [],
      selectedList: [],
      factoryList: [],
      statusOptions: stockStatusOptions,
      stockTypeOptions,
      operationTypes
    };
  },
  methods: {
    getPurchasePrice(row) {
      if (row.planQty >= 0 && row.actualPurchasePrice >= 0) {
        return (row.planQty * row.actualPurchasePrice)?.toFixed(6)
      }
      return row.purchasePrice >= 0 ? row.purchasePrice : ''
    },
    getForecastPurchaseQty(row) {
      return (row.inStockQty + row.onWayQty + row.planQty) || ''
    },
    async handleSave(isSubmit) {
      if (!this.list?.length) {
        return true
      }
      return new Promise(async (resolve) => {
        setTimeout(async () => {
          try {
            const res = await checkStrategyItemForStep2Compare({ ...this.detailInfo, itemList: this.list })
            if (res.code !== 200) {
              !this.msgIns && this.$message.error({
                message: res.msg.replaceAll('\n', '<br>'),
                duration: 5000,
                dangerouslyUseHTMLString: true
              })
              resolve(false)
            } else {
              resolve(true)
              !isSubmit && this.$message.success('保存成功')
            }
          } catch (error) {
            console.log(error)
            resolve(false)
          }
        }, 100)
      })
    },
    async handlePreview() {
      // 试算之前校验， 如果草稿状态无需校验
      let res = false
      if (this.detailInfo.status === 'New') {
        res = true
      } else {
        res = await this.handleSave(true)
      }
      if (!res) {
        return false
      }
      const loading = this.$loading({
        lock: true,
        text: 'AI试算中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      try {
        const res = await calculateStrategyStock([this.taskId])
        if (res.code === 200) {
          this.$message.success('试算成功')
          this.init()
          return true
        } else {
          this.$confirm(res.msg, '试算失败', {
            center: true,
            confirmButtonText: '返回上一步',
            showCancelButton: false,
            callback: () => {
              loading.close()
              this.$emit('back-step1')
            }
          })
          return false
        }
      } catch (error) {
        this.$message.error(error.msg || error.message || '试算失败')
      } finally {
        loading.close()
      }
    },
    // 添加行
    addItem() {
      this.list.push({})
    },
    // 批量删除行
    async handleDelete() {
      if (!this.selectedList.length) {
        this.$message.error('请选择要删除的行')
        return
      }
      const itemNoList = this.selectedList.map(item => item.itemNo).filter(Boolean)
      if (!itemNoList.length) {
        this.list = this.list.filter(item => !this.selectedList.includes(item))
        return
      }
      this.delLoading = true
      const res = await deleteStrategyItemForStep2({ taskId: this.taskId, itemNoList })
      this.delLoading = false
      if (res.code === 200) {
        this.$message.success('删除成功')
        this.list = this.list.filter(item => !this.selectedList.includes(item))
      } else {
        this.$message.error(res.msg)
      }
    },
    handleSelectionChange(rows) {
      this.selectedList = rows
    },
    // 远程查找sku列表
    async remoteSku(search, row) {
      const params = search;
      this.$set(row, 'skuLoading', true)
      const res = await searchSkuList(params)
      this.$set(row, 'skuLoading', false)
      if (res.code === 200) {
        this.$set(row, 'skuList', Array.isArray(res.data) && res.data.length > 0 ? res.data : [])
      } else {
        this.$set(row, 'skuList', [])
        this.$message.error(res.msg);
      }
    },
    // sku更改后清空行数据
    async skuChange(val, index) {
      let row = this.list[index]
      let item = row.skuList.find((a) => a.skuNo === val)
      const res = await getPurchasePlanSkuInfo({
        factory: this.detailInfo.factory,
        sku: val
      });
      if (res.code === 200) {
        const {
          price = '',
          forecastPurchaseQty = 0,
          actualPurchasePrice
        } = res.data[0];
        const forecastPrice = price ? price * forecastPurchaseQty : 0;
        row = {
          ...res.data[0],
          purchasePrice: price,
          forecastPurchaseQty,
          forecastPrice,
          actualPurchasePrice: actualPurchasePrice || price,
          skuDesc: item?.materialDescribe || '',
          skuList: row.skuList || [],
          itemType: '人工'
        }
      } else {
        row = {
          sku: val,
          skuDesc: item?.materialDescribe || '',
          skuList: row.skuList || []
        }
      }
      this.list[index] = row;
      this.list = [...this.list]
    },
    async positionChange(val, index) {
      let row = this.list[index]
      let item = this.positionList.find((a) => a.code === val)
      this.$set(row, 'positionName', item.name)
      this.handleChange(row)
    },
    async supplierChange(val, index) {
      let row = this.list[index]
      this.handleChange(row)
    },
    async handleChange(row) {
      if (row.sku && row.position && row.planQty >= 0 && row.actualPurchasePrice >= 0) {
        if (row.planQty === 0) {
          this.$message.error('计划采购量需大于0')
          return
        }
        if (row.actualPurchasePrice === 0) {
          this.$message.error('实际采购价需大于0')
          return
        }
        const params = { ...row }
        if (this.detailInfo.stockRecommendAlgorithm !== 'TurnOver') {
          params.supplierNo = this.detailInfo.supplierNo
          params.supplierName = this.detailInfo.supplierName
        }
        delete params.skuList
        const res = await updateStrategyItemForStep2({
          taskId: this.taskId,
          aiItem: params
        })
        if (res.code !== 200) {
          this.msgIns = this.$message({
            type: 'error',
            message: res.msg
          })
          setTimeout(() => {
            this.msgIns = null
          }, 1500)
        } else {
          this.$message({
            type: 'success',
            message: '行更新成功'
          })
          this.$set(row, 'id', res.data.id)
          this.$set(row, 'itemNo', res.data.itemNo)
          this.$set(row, 'itemType', '人工')
        }
      }
    },
    async handleExport(data) {
      const res = await exportStrategyItemForStep2(data)
      if (res.code === 200) {
        this.$message.success({ message: res.data || '导出成功' });
      } else {
        this.$message.error({ message: res.msg || '导出失败' });
      }
    },
    handleDownloadTemplate() {
      downloadStrategyItemForStep2({ stockRecommendAlgorithm: this.detailInfo.stockRecommendAlgorithm })
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.importLoading = true;
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.code === 200) {
        this.$message.success(response.msg || '导入成功！');
        this.init()
      } else {
        this.$message.error((response && response.msg) || '导入失败！');
      }
    },
    onUploadError(error) {
      console.log(error);
      this.importLoading = false;
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    },
    async init() {
      const res = await getStrategyHeaderForStep2(this.taskId)
      if (res.code === 200) {
        this.detailInfo = res.data
        this.$emit('status', this.detailInfo.status)
        console.log(this.detailInfo.status)
        // this.list = res.data.itemList || []
        if (!this.positionList.length) {
          getPositionByFactory([this.detailInfo.factory]).then(res => {
            this.positionList = res.result.map(item => {
              return {
                code: item.position,
                name: item.positionName
              }
            })
          })
        }
        if (this.detailInfo.status === 'New') {
          const isSuc = await this.handlePreview()
          if (isSuc === false) {
            return
          }
        }
      } else {
        this.$message.error(res.msg || '获取数据失败')
      }
      this.getList()
      const data = await getPieData({ taskId: this.taskId })
      this.chartDatas = data.data?.chartDatas || []
      setTimeout(() => {
        this.chartDatas.map((it, key) => {
          const chart = document.getElementById('chart' + key)
          if (chart) {
            this.setChart(chart, it)
          }
        })
      }, 100)
    },
    async handleCurrentChange(val) {
      if (!this.disabled) {
        const res = await this.handleSave(true)
        if (!res) {
          this.listQueryInfo.pageNum = this.originPageInfo.pageNum
          return
        }
      }
      this.listQueryInfo.pageNum = val;
      this.originPageInfo = {
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      }
      this.getList();
    },
    async handleSizeChange(val) {
      if (!this.disabled) {
        const res = await this.handleSave(true)
        if (!res) {
          this.listQueryInfo.pageSize = this.originPageInfo.pageSize
          return
        }
      }
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.originPageInfo = {
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      }
      this.getList();
    },
    async getList() {
      this.listLoading = true;
      const res = await getStrategyItemForStep2({ ...this.listQueryInfo, taskId: this.taskId });
      this.listLoading = false;
      if (res.code === 200) {
        this.list = res.data.records;
        this.total = res.data.total;
      } else {
        this.$message.error(res.msg);
      }
    },
    setChart(dom, pieData) {
      const chart = echarts.init(dom);
      const data = pieData.dataList.map((item, key) => {
        return {
          value: item,
          name: pieData.legendList[key]
        };
      });
      const option = {
        title: {
          text: pieData.name,
          textStyle: {
            fontWeight: '400',
            fontSize: 14,
            color: '#d7ab70'
          },
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '0',
          top: 'center'
        },
        series: [
          {
            name: 'sku数量',
            type: 'pie',
            width: '160',
            height: '160',
            radius: '50%',
            center: ['40%', '50%'], // 设置饼图位置
            label: {
              formatter: '{c}',
              show: true,
              position: 'inside'
            },
            data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      chart.setOption(option)
    }
  },
  created() {
    getAllFactory().then((res) => {
      if (res.status === 200) {
        this.factoryList = res.result;
      }
    })
  },
  async mounted() {
    this.init()
  }
}
</script>

<style scoped>
.required {
  &:before {
    content: '*';
    color: #ff7268;
    margin-right: 4px;
  }
  &::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
.font-size-12 {
  font-size: 12px;
}
.color-#888 {
  color: #888;
}
.font-400 {
  font-weight: 400;
}
.mt-20px {
  margin-top: 20px;
}
.pl-24px {
  padding-left: 24px;
}
.flex {
  display: flex;
}
.flex-wrap {
  flex-wrap: wrap;
}
.flex-1 {
  flex: 1;
}
.item-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.h-32px {
  height: 32px;
}
.w-320px {
  width: 320px;
}
.button-wrap {
  justify-content: flex-end;
  margin-bottom: 12px;
}
</style>
