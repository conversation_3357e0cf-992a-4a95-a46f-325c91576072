<template>
  <el-form ref="searchForm" :model="searchForm" :rules="formRules" label-width="120px" label-suffix=":">
    <div style="border: 1px solid #e6ebf5; border-radius: 4px;padding: 0 20px;margin-top: 20px;">
      <Title title="选择计划月份" />
      <el-row>
        <el-col :span="6">
          <el-form-item label="计划月份" prop="planTime">
            <el-date-picker
              clearable
              v-model="searchForm.planTime"
              :disabled="disabled"
              type="month"
              value-format="yyyy-MM"
              :picker-options="pickerOptions"
              style="width: 100%"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型" prop="type">
            <el-select style="width: 100%" v-model="searchForm.type" placeholder="请选择类型" disabled>
              <el-option label="月度" value="MONTH" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script>
import Title from './Title.vue'
import { createPurchasePlanForStrategy } from '@/api/purchasePlan'
export default {
  props: {
    detailInfo: Object,
    taskId: String,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Title
  },
  data() {
    return {
      searchForm: {
        planTime: this.detailInfo.planTime || '',
        type: 'MONTH'
      },
      formRules: {
        planTime: [
          { required: true, message: '请选择计划月份', trigger: 'change' }
        ]
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < new Date().getTime()
        }
      }
    }
  },
  methods: {
    handleSave() {
      this.$refs.searchForm.validate(async valid => {
        if (valid) {
          try {
            const res = await createPurchasePlanForStrategy({ ...this.searchForm, taskId: this.taskId })
            if (res.code === 200) {
              this.$message.success('创建成功')
              this.$closeTag(this.$route.path);
              // 跳转采购计划列表
              this.$router.push({
                path: '/purchasePlan/list'
              })
            } else {
              this.$message.error(res.msg)
            }
          } catch (error) {
            this.$message.error(error.msg || error.message || '创建失败')
          }
        }
      })
    }
  }
}
</script>

<style>

</style>
