<template>
  <el-form ref="searchForm" :validate-on-rule-change="false" :model="searchForm" :rules="formRules" label-width="120px" class="search-form" label-suffix=":">
    <div style="border: 1px solid #e6ebf5; border-radius: 4px;padding: 0 20px;margin-top: 20px;">
      <Title title="选择目标推荐算法&历史数据参考时间" />
      <el-row>
        <el-col :span="16">
          <el-form-item label="目标推荐算法" prop="stockRecommendAlgorithm">
            <el-radio-group style="width: 100%;display: flex;" v-model="searchForm.stockRecommendAlgorithm" :disabled="disabled">
              <el-radio style="flex: 1;" label="Money" border>
                <span>满足目标金额的备货推荐算法</span>
              </el-radio>
              <el-radio style="flex: 1;" label="TurnOver" border>
                <span>满足目标周转天数的备货推荐算法</span>
              </el-radio>
            </el-radio-group>
            <div class="flex">
              <span class="tips" style="padding-right: 20px;">对于给定目标备货金额，利用0-1整数规划算法，推荐出备货后资金占用成本和仓租成本最低的SKU和备货数量</span>
              <span class="tips" style="padding-left: 20px;">要求备货后的周转天数小于输入目标值，根据历史交货数据推荐出备货数量</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="历史交货数据参考时间" prop="historyDeliveryDays">
            <el-select v-model="searchForm.historyDeliveryDays" style="width: 100%;" :disabled="disabled">
              <el-option v-for="item in [90, 120, 180, 365]" :label="item" :key="item" :value="item" />
            </el-select>
            <span class="tips" style="width: 100%;">例：参考时间为365天，计算结果越接近过去全年需求情况；参考时间为90天，计算结果越符合最近时段的需求情况，但可参考订单会相对变少。</span>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div style="border: 1px solid #e6ebf5; border-radius: 4px;padding: 0 20px;margin-top: 20px;">
      <Title :title="searchForm.stockRecommendAlgorithm === 'Money' ? '配置目标金额等信息' : '配置目标周转天数等信息'" />
      <el-row>
        <el-col :span="8">
          <el-form-item v-if="searchForm.stockRecommendAlgorithm === 'Money'" label="目标备货金额" prop="stockTurnoverAmount">
            <el-input-number v-model="searchForm.stockTurnoverAmount" style="width: 100%;" :disabled="disabled" placeholder="请输入含税备货金额" :controls="false" :min="0.000001" :max="9999999999" />
          </el-form-item>
          <el-form-item v-if="searchForm.stockRecommendAlgorithm === 'TurnOver'" label="目标周转天数" prop="stockTurnoverDays">
            <el-input-number v-model="searchForm.stockTurnoverDays" style="width: 100%;" :disabled="disabled" placeholder="请输入目标周转天数" :controls="false" :min="1" :precision="0" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工厂" prop="factory">
            <el-select
              v-model="searchForm.factory"
              filterable
              clearable
              :disabled="disabled"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factory"
                :label="`${item.factory} ${item.factoryName}`"
                :value="item.factory"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料组" prop="materialGroupId">
            <MaterialGroup v-model="searchForm.materialGroupId" @getlabel="(val) => materialGroupName = val" :default-label="materialGroupName" :disabled="disabled" style="width: 100%" placeholder="输入关键词" />
          </el-form-item>
        </el-col>
        <el-col v-if="isShowSupplier" :span="8">
          <el-form-item label="供应商" prop="supplierNo">
            <Supplier v-model="searchForm.supplierNo" @getlabel="(val) => supplierName = val" :supplier-name="supplierName" :disabled="disabled" style="width: 100%" placeholder="输入关键词" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运营策略" prop="operationType">
            <el-select style="width: 100%" v-model="searchForm.operationType" :disabled="disabled" placeholder="请选择">
              <el-option v-for="item in operationTypes" :label="item.label" :key="item.key" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="战略备货类型" prop="stockStrategyType">
            <el-select style="width: 100%" v-model="searchForm.stockStrategyType" :disabled="disabled" placeholder="请选择">
              <el-option v-for="item in stockTypeOptions" :label="item.label" :key="item.key" :value="item.key" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div style="border: 1px solid #e6ebf5; border-radius: 4px;padding: 0 20px;margin-top: 20px;">
      <Title title="选择目标备货商品" />
      <span style="color: #999;font-size: 11px;margin-bottom: 10px;display: block;">可根据品牌、类目，系统推荐备货SKU，指定SKU请录入SKU，指定库位。<br />参与算法计算&推荐的sku、仓位必须同时满足：sku不为售完即止或停用状态；sku在参考时间内有交货记录；sku在备货库位客户数≥2；sku在wms有体积重量数据。</span>
      <el-form-item label="推荐方式" prop="stockRecommendSkuType">
        <el-radio-group v-model="searchForm.stockRecommendSkuType" :disabled="disabled">
          <el-radio-button v-for="(item, index) in stockRecommendSkuTypeList" :key="index" :label="item.key" border>
            <span>{{ item.label }}</span>
          </el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-row v-if="searchForm.stockRecommendSkuType === 'System_Recommend'">
        <el-col :span="8">
          <el-form-item label="品牌" prop="brandNo">
            <Brand v-model="searchForm.brandNo" @change="changeCatalogOrBrand" @getlabel="(val) => brandName = val" :multiple="true" :default-label="brandName" :disabled="disabled" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类目" prop="catalogId">
            <AllCatalog @change="changeCatalogOrBrand" v-model="searchForm.catalogId" :multiple="true" :checkStrictly="true" :disabled="disabled" style="width: 100%" placeholder="支持多个" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="searchForm.stockRecommendSkuType === 'Manual_Appoint'">
        <el-col v-loading="importLoading" :span="16">
          <el-form-item label="SKU/库位">
            <vxe-table
              ref="tableRef"
              border
              :scroll-y="{ gt: 100, enabled: true }"
              :data="list"
              :max-height="600"
            >
              <vxe-column type="checkbox" width="55" />
              <vxe-column title="SKU" width="200">
                <template slot-scope="{row}">
                  <el-select
                    v-model="row.sku"
                    filterable
                    placeholder="请输入SKU"
                    style="width: 100%"
                    remote
                    clearable
                    :disabled="disabled"
                    :remote-method="(val) => remoteSku(val, row)"
                    :loading="row.skuLoading"
                    @change="(val) => skuChange(val, row)"
                  >
                    <el-option
                      v-for="item in row.skuList"
                      :key="item.skuNo"
                      :label="`${item.skuNo}`"
                      :value="item.skuNo"
                    >
                    </el-option>
                  </el-select>
                </template>
              </vxe-column>
              <vxe-column align="left" field="skuDesc" title="物料描述" show-overflow />
              <vxe-column title="库位" width="240">
                <template slot-scope="{row}">
                  <el-select
                    v-model="row.position"
                    filterable
                    placeholder="请选择库位"
                    style="width: 100%"
                    :disabled="disabled"
                  >
                    <el-option
                      v-for="a in positionList"
                      :key="a.code"
                      :value="a.code"
                      :label="`${a.name}`"
                    ></el-option>
                  </el-select>
                </template>
              </vxe-column>
            </vxe-table>
          </el-form-item>
        </el-col>
        <el-col :span="7" :offset="1">
          <el-button type="primary" :disabled="disabled" @click="addItem">新增</el-button>
          <el-button type="danger" :disabled="disabled" @click="handleDelete">批量删除</el-button>
          <el-dropdown style="margin-left: 10px;">
            <el-button type="primary" :disabled="disabled" :loading="importLoading">批量导入<i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <el-upload
                  :action="`/api-ab/stockStrategyItem/import?factory=${searchForm.factory}`"
                  :show-file-list="false"
                  :on-success="onUploadSuccess"
                  :on-error="onUploadError"
                  :before-upload="beforeUpload"
                  :disabled="disabled"
                  accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                  style="display: inline-block; margin-left: 10px"
                  name="file"
                >
                  <el-button
                    type="text"
                    style="margin-right: 5px"
                    :disabled="disabled"
                    :loading="importLoading"
                    >导入</el-button
                  >
                </el-upload>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-button type="text" :disabled="disabled" :loading="importLoading"  @click="handleDownloadTemplate">
                  下载模版
                </el-button>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-col>
      </el-row>
    </div>
  </el-form>
</template>

<script>
import Title from './Title.vue'
import MaterialGroup from '@/components/SearchFields/materialGroup';
import Brand from './Brand';
import Supplier from '@/components/SearchFields/consSupplier';
import AllCatalog from '@/components/SearchFields/catagoryCascader';
import { searchSkuList } from '@/api/orderSale';
import { getAllFactory, updateStrategyStock, addStrategyStock, getPositionByFactory, updateStrategyItemForStep1, exportStrategyItemForStep1, getMaterialGroupListApi } from '@/api/purchasePlan'
import { stockTypeOptions, stockRecommendOptions, operationTypes, stockRecommendSkuTypeList } from '../../const'
export default {
  props: {
    detailInfo: Object,
    taskId: String,
    disabled: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Title,
    AllCatalog,
    MaterialGroup,
    Brand,
    Supplier
  },
  data() {
    return {
      list: [],
      selectedList: [],
      positionList: [],
      factoryList: [],
      stockTypeOptions,
      stockRecommendOptions,
      stockRecommendSkuTypeList,
      operationTypes,
      importLoading: false,
      isShowSupplier: false,
      searchForm: {
        stockRecommendAlgorithm: 'Money',
        stockRecommendSkuType: 'System_Recommend',
        historyDeliveryDays: 180
      },
      brandName: [],
      materialGroupName: '',
      catalogName: [],
      supplierName: '',
      formRules: {
        stockRecommendAlgorithm: [
          { required: true, message: '请选择推荐算法', trigger: 'change' }
        ],
        stockRecommendSkuType: [
          { required: true, message: '请选择推荐SKU类型', trigger: 'change' }
        ],
        historyDeliveryDays: [
          { required: true, message: '请输入历史交货数据参考时间', trigger: 'blur' }
        ],
        factory: [
          { required: true, message: '请选择工厂', trigger: 'change' }
        ],
        stockTurnoverAmount: [
          { required: false, message: '请输入备货金额', trigger: 'blur' }
        ],
        stockTurnoverDays: [
          { required: false, message: '请输入周转天数', trigger: 'blur' }
        ],
        materialGroupId: [
          { required: true, message: '请选择物料组', trigger: 'change' }
        ],
        supplierNo: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        operationType: [
          { required: true, message: '请选择运营策略', trigger: 'change' }
        ],
        stockStrategyType: [
          { required: true, message: '请选择战略备货类型', trigger: 'change' }
        ],
        brandNo: [
          { required: true, message: '品牌/类目至少选一个', trigger: 'change' }
        ],
        catalogId: [
          { required: true, message: '品牌/类目至少选一个', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    'detailInfo': {
      handler: function(val) {
        if (val.stockRecommendAlgorithm) {
          const { brandNo, brandName } = this.formatBrandList(val.extendList)
          const catalogId = this.formatCatalogList(val.extendList)
          this.searchForm = {
            stockRecommendAlgorithm: val.stockRecommendAlgorithm,
            stockRecommendSkuType: val.stockRecommendSkuType,
            factory: val.factory,
            stockTurnoverAmount: val.stockTurnoverAmount,
            stockTurnoverDays: val.stockTurnoverDays,
            materialGroupId: val.materialGroupId + '',
            supplierNo: val.supplierNo,
            operationType: val.operationType,
            stockStrategyType: val.stockStrategyType,
            brandNo,
            catalogId,
            historyDeliveryDays: val.historyDeliveryDays
          }
          this.brandName = brandName
          this.materialGroupName = val.materialGroup
          this.supplierName = val.supplierName
          if (val.stockRecommendSkuType === 'Manual_Appoint') {
            this.list = val.itemList
          }
          this.changeCatalogOrBrand()
        }
      },
      deep: true,
      immediate: true
    },
    'searchForm.stockRecommendAlgorithm': {
      handler: function(val) {
        if (val === 'TurnOver') {
          this.formRules.stockTurnoverDays[0].required = true
          this.formRules.stockTurnoverAmount[0].required = false
          this.isShowSupplier = false
          this.searchForm.stockTurnoverAmount = undefined
        } else {
          this.formRules.stockTurnoverDays[0].required = false
          this.formRules.stockTurnoverAmount[0].required = true
          this.isShowSupplier = true
          this.searchForm.stockTurnoverDays = undefined
        }
      },
      immediate: true
    },
    'searchForm.stockRecommendSkuType': {
      handler(val) {
        if (val !== 'System_Recommend') {
          this.formRules.brandNo[0].required = false
          this.formRules.catalogId[0].required = false
        } else {
          this.changeCatalogOrBrand()
        }
      }
    },
    'searchForm.factory': {
      handler(val) {
        if (val) {
          getPositionByFactory([val]).then(res => {
            this.positionList = res.result.map(item => {
              return {
                code: item.position,
                name: item.position + item.positionName
              }
            })
          })
        }
      },
      immediate: true
    }
  },
  created() {
    getAllFactory().then((res) => {
      if (res.status === 200) {
        this.factoryList = res.result;
      }
    })
  },
  methods: {
    changeCatalogOrBrand() {
      if (this.searchForm.catalogId?.length && this.searchForm.brandNo?.length) {
        this.formRules.brandNo[0].required = true
        this.formRules.catalogId[0].required = true
      } else if (!this.searchForm.catalogId?.length && !this.searchForm.brandNo?.length) {
        this.formRules.brandNo[0].required = true
        this.formRules.catalogId[0].required = true
      } else {
        if (this.searchForm.brandNo?.length) {
          this.formRules.brandNo[0].required = true
          this.formRules.catalogId[0].required = false
        } else {
          this.formRules.catalogId[0].required = true
          this.formRules.brandNo[0].required = false
        }
      }
    },
    // 添加行
    addItem() {
      this.$refs.searchForm.validate(async valid => {
        if (valid) {
          this.list.push({})
        }
      })
    },
    // 批量删除行
    async handleDelete() {
      const selectedList = this.$refs.tableRef.getCheckboxRecords()
      if (!selectedList.length) {
        this.$message.error('请选择要删除的行')
        return
      }
      this.list = this.list.filter(item => !selectedList.includes(item))
    },

    // 格式化展示参数
    formatBrandList(list) {
      const brandList = list?.filter(item => item.extendType === 'brand') ?? []
      return {
        brandNo: brandList.map(item => item.brandNo),
        brandName: brandList.map(item => item.brandName)
      }
    },
    formatCatalogList(list) {
      const catalogList = list?.filter(item => item.extendType === 'catalog') ?? []
      const catalogId = catalogList.map(item => {
        return [item.firstCatalogId, item.secondCatalogId, item.thirdCatalogId, item.fourthCatalogId].filter(Boolean)
      })
      return catalogId
    },
    // 格式化请求参数
    async getBrandList(brandList) {
      if (brandList?.length) {
        const res = await getMaterialGroupListApi({
          filter: {
            brandId: brandList.join(','),
            stateCode: 1
          },
          bizCode: 'cc_web',
          limit: -1,
          entityType: 'entity.brand'
        })
        return res.data?.map((item) => {
          return {
            brandNo: item.id,
            brandName: item.name,
            extendType: 'brand',
            taskId: this.taskId
          }
        })
      }
      return []
    },
    // 格式化请求参数
    getCatalogList(catalogId) {
      if (catalogId?.length) {
        return catalogId.map((item) => {
          return {
            firstCatalogId: item[0],
            secondCatalogId: item[1] ?? '',
            thirdCatalogId: item[2] ?? '',
            fourthCatalogId: item[3] ?? '',
            extendType: 'catalog',
            taskId: this.taskId
          }
        })
      }
      return []
    },
    handleSave() {
      return new Promise((resolve) => {
        if (this.searchForm.stockRecommendSkuType === 'Manual_Appoint' && this.list.length === 0) {
          this.$message.error('请添加SKU')
          resolve(false)
          return
        }
        this.$refs.searchForm.validate(async valid => {
          if (valid) {
            try {
              const historyDeliveryDays = this.searchForm.historyDeliveryDays ?? 180
              const itemList = this.searchForm.stockRecommendSkuType === 'Manual_Appoint' ? this.list.map(item => {
                return {
                  sku: item.sku,
                  skuDesc: item.skuDesc,
                  position: item.position,
                  positionName: item.positionName
                }
              }) : []
              let extendList = []
              const brandNo = this.searchForm.stockRecommendSkuType === 'Manual_Appoint' ? undefined : this.searchForm.brandNo
              const catalogId = this.searchForm.stockRecommendSkuType === 'Manual_Appoint' ? undefined : this.searchForm.catalogId
              const brandList = await this.getBrandList(brandNo)
              extendList.push(...brandList)
              extendList.push(...this.getCatalogList(catalogId))
              const supplierNo = this.searchForm.stockRecommendAlgorithm === 'Money' ? this.searchForm.supplierNo : undefined
              const params = {
                ...this.searchForm,
                brandNo: undefined,
                catalogId: undefined,
                supplierNo,
                taskId: this.taskId === '0' ? undefined : this.taskId,
                creator: this.detailInfo.creator || window.CUR_DATA.user.name,
                status: this.taskId === '0' ? 'New' : this.detailInfo.status,
                historyDeliveryDays,
                materialGroup: this.materialGroupName,
                supplierName: this.searchForm.stockRecommendAlgorithm === 'Money' ? this.supplierName : undefined,
                itemList,
                extendList
              }
              const res = this.taskId === '0' ? await addStrategyStock(params) : await updateStrategyStock(params)
              if (res.code === 200) {
                this.$message.success('保存成功')
                if (this.taskId === '0') {
                  resolve({
                    taskId: res.data
                  })
                } else {
                  resolve({
                    taskId: this.taskId
                  })
                }
              } else {
                this.$message.error(res.msg, 8000)
                resolve(false)
              }
            } catch (error) {
              const msg = error.msg || error.message || '保存失败'
              this.$message({
                message: msg.replaceAll('\n', '<br>'),
                type: 'error',
                duration: 4000,
                dangerouslyUseHTMLString: true
              })
              resolve(false)
            }
          } else {
            resolve(false)
          }
        })
      })
    },
    // 远程查找sku列表
    async remoteSku(search, row) {
      const params = search;
      this.$set(row, 'skuLoading', true)
      const res = await searchSkuList(params)
      this.$set(row, 'skuLoading', false)
      if (res.code === 200) {
        this.$set(row, 'skuList', Array.isArray(res.data) && res.data.length > 0 ? res.data : [])
      } else {
        this.$set(row, 'skuList', [])
        this.$message.error(res.msg);
      }
    },
    // sku更改后清空行数据
    async skuChange(val, row) {
      const item = row.skuList.find((a) => a.skuNo === val)
      row.skuDesc = item?.materialDescribe || ''
    },
    handleChange(row) {
      if (row.sku && row.position) {
        updateStrategyItemForStep1({
          taskId: this.taskId,
          itemList: [row]
        })
      }
    },
    handleDownloadTemplate() {
      exportStrategyItemForStep1()
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.file = file;
      this.importLoading = true;
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.code === 200) {
        this.$message.success(response.msg || '导入成功！');
        const oriList = response.data.map(item => {
          return {
            sku: item.sku,
            skuDesc: item?.skuDesc || '',
            position: item.position
          }
        });
        this.list = oriList
      } else {
        this.$message.error((response && response.msg) || '导入失败！');
      }
    },
    onUploadError(error) {
      console.log(error);
      this.importLoading = false;
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    }
  }
}
</script>

<style scoped>
.flex {
  display: flex;
}
.tips {
  color: #999;
  font-size: 11px;
  display: block;
  width: 50%;
  line-height: 16px;
  margin-top: 8px;
}
</style>
