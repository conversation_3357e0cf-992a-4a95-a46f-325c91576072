<template>
  <el-select
    :value="value"
    filterable
    remote
    clearable
    :placeholder="placeholder"
    :multiple="multiple"
    :collapseTags="collapseTags"
    @change="handleSelectChange"
    :remote-method="remoteBrandIdMethod"
    :disabled="disabled"
    :loading="brandIdLoading">
    <el-option
      v-for="item in brandIdOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value">
    </el-option>
  </el-select>
</template>
<script>
import { getBrandListApi } from '@/api/purchasePlan'
export default {
  name: 'remoteBrandComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: [ String, Number, Array ],
    getLabel: Boolean,
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    defaultLabel: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      brandIdOptions: [],
      brandIdLoading: false
    }
  },
  mounted () {
    this.getInitOptions(this.defaultLabel)
  },
  watch: {
    defaultLabel (val) {
      console.log(val)
      this.getInitOptions(val)
    }
  },
  methods: {
    getInitOptions (val) {
      if (val?.length && this.value) {
        const options = val.map((item, key) => {
          return {
            value: this.value[key],
            label: item
          }
        })
        this.brandIdOptions = this.brandIdOptions.length ? this.brandIdOptions : options
      }
    },
    handleSelectChange (val) {
      this.$emit('change', val)
      console.log(val)
      const curOptions = this.brandIdOptions.filter(it => val?.includes(it.value))
      this.$emit('getlabel', curOptions?.map(it => it.label))
    },
    // 远程查找品牌
    remoteBrandIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.brandIdLoading = true
        getBrandListApi({
          brandName: key,
          pageSize: 20,
          stateCode: 1
        }).then(res => {
          this.brandIdLoading = false
          if (res.success) {
            if (res.data?.length > 0) {
              this.brandIdOptions = res.data.map(item => {
                return {
                  value: item.brandId,
                  label: item.brandName
                }
              })
            } else {
              this.brandIdOptions = []
            }
          } else {
            this.brandIdOptions = []
          }
        })
      } else {
        // this.brandIdOptions = []
      }
    }
  }
}
</script>
