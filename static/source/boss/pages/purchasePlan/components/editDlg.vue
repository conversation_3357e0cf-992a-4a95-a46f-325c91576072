<template>
  <el-dialog
    width="1000px"
    :title="editId ? '编辑销量预测' : '新增销量预测'"
    class="add-dialog"
    :visible="editVisible"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    @close="cancel(false)"
  >
    <el-form
      ref="addForm"
      :model="addForm"
      label-width="160px"
      label-position="right"
      :rules="rules"
      :disabled="!!addForm.id"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="工厂：" prop="factory">
            <el-select
              v-model="addForm.factory"
              filterable
              clearable
              placeholder="请输入关键词"
              @change="getSku"
              style="width: 100%"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factory"
                :label="`${item.factory} ${item.factoryName}`"
                :value="item.factory"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型：" prop="type">
            <el-select
              v-model="addForm.type"
              placeholder="请选择类型"
              style="width: 100%"
              @change="changeType"
            >
              <el-option label="年度" value="YEAR"></el-option>
              <el-option label="月度" value="MONTH"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="SKU：" prop="sku">
            <el-input
              v-model.trim="addForm.sku"
              style="width: 100%"
              @blur="getSku"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物料描述：" prop="skuName">
            <div class="name">{{ addForm.skuName }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="计划月份：" prop="planTime">
            <el-date-picker
              v-model="addForm.planTime"
              type="month"
              value-format="yyyy-MM"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品牌：" prop="brandName">{{
            addForm.brandName
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="当前在途可用量：" prop="onWayQty">{{
            addForm.onWayQty
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="当前在库可用量：" prop="inStockQty">{{
            addForm.inStockQty
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="计划月份预测销量：" prop="forecastSalesQty">{{
            addForm.forecastSalesQty
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位：" prop="unit">{{
            addForm.unit
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="预测采购量：" prop="forecastPurchaseQty">{{
            addForm.forecastPurchaseQty
          }}</el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="参考单价：" prop="price">{{
            addForm.price
          }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="预测采购金额：">{{
            addForm.price &&
            addForm.forecastPurchaseQty &&
            (addForm.price * addForm.forecastPurchaseQty).toFixed(2)
          }}</el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="addDetail">
      <span>预测明细</span>
      <el-button type="primary" size="mini" @click="handleAddDetail"
        >增加</el-button
      >
    </div>
    <el-table
      :data="details"
      border
      fit
      style="margin: 10px 0"
      max-height="500"
    >
      <el-table-column
        prop="stockUpWarehouseCode"
        label="备货仓"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-select
            v-model="row.position"
            filterable
            style="width: 100%"
            value-key="code"
          >
            <el-option
              v-for="item in positions"
              :key="item.code"
              :value="item"
              :label="item.name"
            >
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column
        prop="forecastSalesQty"
        label="计划月份预测销量"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-input-number
            v-model="row.forecastSalesQty"
            style="width: 100%"
            :min="0"
            :precision="2"
            :controls="false"
            @blur="caculateQty"
          />
        </template>
      </el-table-column>
      <el-table-column prop="action" label="操作" align="center" width="80">
        <template slot-scope="{ row, $index }">
          <el-link type="primary" @click="deleteDetail(row, $index)">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <footer class="align-right">
      <el-button @click="cancel(false)">取消</el-button>
      <el-button @click="submitAddDialog" type="primary" :loading="loading"
        >确定</el-button
      >
    </footer>
  </el-dialog>
</template>

<script>
import {
  salesInfo,
  querySkuInfo,
  createSalesForecast,
  updateSalesForecast,
  salesPositions,
  salesSupplier
} from '@/api/purchasePlan';
import { suggestedOptions } from '../const';

export default {
  props: ['editVisible', 'editId', 'cancel', 'factoryList', 'planTime'],
  data() {
    return {
      addForm: {
        planTime: ''
      },
      details: [],
      positions: [],
      suppliers: [],
      loading: false,
      suggestedOptions,
      deleteList: [],
      rules: {
        factory: [
          { required: true, message: '工厂不能为空！', trigger: 'change' }
        ],
        type: [
          { required: true, message: '类型不能为空！', trigger: 'change' }
        ],
        sku: [{ required: true, message: 'SKU不能为空！', trigger: 'blur' }],
        planTime: [
          { required: true, message: '计划月份不能为空！', trigger: 'change' }
        ],
        forecastSalesQty: [
          {
            required: true,
            message: '计划月份预测销量不能为空！',
            trigger: 'blur'
          },
          {
            pattern: /^(?!0$).*/,
            message: '计划月份预测销量不能为0！',
            trigger: 'blur'
          },
          {
            pattern: /^(\d+)(\.\d{0,2})?$/,
            message: '计划月份预测销量最多为两位小数',
            trigger: 'blur'
          }
        ]
      }
    };
  },
  watch: {
    editVisible(val) {
      if (val) {
        this.$refs.addForm && this.$refs.addForm.clearValidate();
        this.initData();
        this.deleteList = [];
      }
    }
  },
  methods: {
    async initData() {
      this.loading = true;
      if (this.editId) {
        let res = await salesInfo({ id: this.editId });
        if (res.code === 200) {
          this.loading = false;
          this.addForm = {
            ...res.data.salesForecastDto,
            planTime: res?.data?.salesForecastDto?.forecastTime || ''
          };
          this.details = res.data.details
            ? res.data.details.map((a) => {
                return {
                  ...a,
                  position: {
                    code: a.stockUpPosition,
                    name: a.stockUpPositionName,
                    warehouseCode: a.stockUpWarehouseCode
                  },
                  suggestedSupplierCode: {
                    supplierCode: a.suggestedSupplierCode,
                    supplierName: a.suggestedSupplierName
                  }
                };
              })
            : [];
          this.getSalesPositions(this.addForm.factory, this.addForm.sku);
          this.getSalesSupplier(this.addForm.factory, this.addForm.sku);
        } else {
          this.$message.error({ message: res.msg });
        }
      } else {
        this.loading = false;
        this.addForm = {
          planTime: this.planTime,
          sku: '',
          onWayQty: 0,
          inStockQty: 0,
          forecastSalesQty: 0,
          forecastPurchaseQty: 0,
          price: 0
        };
        this.details = [];
        this.positions = [];
        this.suppliers = [];
      }
    },
    async getSku() {
      if (this.addForm.factory && this.addForm.sku) {
        this.loading = true;
        let res = await querySkuInfo({
          factory: this.addForm.factory,
          sku: this.addForm.sku
        });
        this.loading = false;
        if (res.code === 200 && res.data) {
          this.addForm = { ...this.addForm, ...res.data };
          this.getSalesPositions(this.addForm.factory, this.addForm.sku);
          this.getSalesSupplier(this.addForm.factory, this.addForm.sku);
          this.details.length === 0 && this.details.push({});
        } else {
          this.$message.error({ message: res.msg });
        }
      }
    },
    // 获取备货仓list
    async getSalesPositions(factory, skuNo) {
      let res = await salesPositions({ factory, skuNo });
      if (res.code === 200) {
        let arr = [];
        res.data.forEach((a) => {
          a.allPosition.forEach((b) => {
            arr.push({
              ...b,
              warehouseCode: a.warehouseCode,
              warehouseName: a.warehouseName
            });
          });
        });
        this.positions = arr;
      } else {
        this.positions = [];
        this.$message.error({ message: res.msg });
      }
    },
    changeType() {
      if (this.addForm.type === 'YEAR') {
        this.details = this.details.map((a) => {
          return {
            ...a,
            suggestedOperationType: 'Bid'
          }
        })
      }
    },
    // 获取供应商list
    async getSalesSupplier(factory, sku) {
      let res = await salesSupplier({ factory, sku });
      if (res.code === 200) {
        this.suppliers = res.data || [];
      } else {
        this.suppliers = [];
        this.$message.error({ message: res.msg });
      }
    },
    handleAddDetail() {
      if (!this.addForm.factory || !this.addForm.sku) {
        this.$message.warning('请先填写工厂及sku！');
        return;
      }
      if (!this.addForm.skuName) {
        this.$message.warning('sku信息获取失败，请重新输入sku！');
        return;
      }
      this.details.push({});
    },
    deleteDetail(row, index) {
      this.details.splice(index, 1);
      this.caculateQty();
      row.id && this.deleteList.push({ id: row.id, status: 0 })
    },
    caculateQty() {
      let num = 0;
      this.details.forEach((a) => {
        console.log(a.forecastSalesQty);
        num += a.forecastSalesQty || 0;
      });
      this.addForm.forecastSalesQty = Number(num.toFixed(2));
      let forecastPurchaseQty =
        num - this.addForm.inStockQty - this.addForm.onWayQty;
      this.addForm.forecastPurchaseQty = Number(
        (forecastPurchaseQty < 0 ? 0 : forecastPurchaseQty).toFixed(2)
      );
    },
    submitAddDialog() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          if (!this.addForm.price) {
            this.$message.warning({ message: '参考单价不能为空，请更换SKU！' });
            return;
          }
          if (this.details.length === 0) {
            this.$message.warning({ message: '预测明细至少有一条！' });
            return;
          }
          let arr = [...this.deleteList];
          let errors = [];
          this.details.forEach((a, i) => {
            let temp = {
              forecastSalesQty: a?.forecastSalesQty,
              suggestedOperationType: a?.suggestedOperationType,
              stockUpPosition: a?.position?.code,
              stockUpPositionName: a?.position?.name,
              stockUpWarehouseCode: a?.position?.warehouseCode,
              status: 1
            };
            let flag = ['Consignment', 'Purchase'].includes(a?.suggestedOperationType)
            if (flag) {
              temp.suggestedSupplierCode = a?.suggestedSupplierCode?.supplierCode
              temp.suggestedSupplierName = a?.suggestedSupplierCode?.supplierName
            }
            a.id && (temp.id = a.id);
            arr.push(temp);
            (!a.position ||
              !a.forecastSalesQty) &&
              errors.push(i + 1);
          });
          if (errors.length) {
            this.$message.warning(
              `预测明细第${errors.join('、')}行信息填写不完整！`
            );
            return
          }
          this.loading = true;
          let methods = this.addForm.id
            ? updateSalesForecast
            : createSalesForecast;
          let res = await methods({
            ...this.addForm,
            details: arr
          });
          this.loading = false;
          if (res.code === 200) {
            this.$message.success({ message: '新增成功！' });
            this.cancel(true);
          } else {
            this.$message.error({ message: res.msg });
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.addDetail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  & > span {
    font-size: 16px;
  }
}
</style>
