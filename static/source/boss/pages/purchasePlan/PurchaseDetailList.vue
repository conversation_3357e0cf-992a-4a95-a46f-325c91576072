<template>
  <div class="app-container">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="140px"
        label-position="right"
        :show-message="false"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="计划单号" prop="planOrderNo">
              <el-input
                v-model="searchForm.planOrderNo"
                placeholder="请输入计划单号"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组" prop="materialGroupIds">
              <el-select
                v-model="searchForm.materialGroupIds"
                filterable
                clearable
                placeholder="请输入物料组，支持多选"
                style="width: 100%"
                remote
                multiple
                reserve-keyword
                :remote-method="remoteMaterialGroupIdMethod"
                :loading="materialGroupIdLoading"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item.brandId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划月份：" prop="planTimeFrom">
              <el-date-picker
                clearable
                v-model="searchForm.planTimeFrom"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择起始月份"
              />
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-date-picker
                clearable
                v-model="searchForm.planTimeTo"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择结束月份"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="SKU" prop="sku">
              <el-input
                v-model="searchForm.sku"
                placeholder="多个sku用空格隔开，最多50个"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库存地点" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                clearable
                placeholder="请选择库存地点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in positions"
                  :key="item.position"
                  :value="item.position"
                  :label="`${item.position} ${item.positionName}`"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="供应商" prop="supplierNo">
              <el-select
                v-model="searchForm.supplierNo"
                placeholder="请选择供应商"
                filterable
                clearable
                remote
                style="width:100%"
                :remote-method="remoteSupplier"
                :loading="supplierLoading"
              >
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.supplierNo"
                  :value="item.supplierNo"
                  :label="`${item.supplierNo} ${item.supplierName}`"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类型" prop="type">
              <el-select
                v-model="searchForm.type"
                clearable
                placeholder="请选择类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="运营策略" prop="operationType">
              <el-select
                v-model="searchForm.operationType"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择运营策略"
              >
                <el-option
                  v-for="item in suggestedOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划单状态" prop="status">
              <el-select
                v-model="searchForm.status"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择计划单状态"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="明细确认状态" prop="confirmStatus">
              <el-select
                v-model="searchForm.confirmStatus"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择明细确认状态"
              >
                <el-option
                  v-for="item in confirmStatusOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货后预计周转天数" prop="stockDaysMin">
              <el-input-number
                v-model="searchForm.stockDaysMin"
                clearable
                placeholder="最小值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-input-number
                v-model="searchForm.stockDaysMax"
                clearable
                placeholder="最大值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <vxe-grid
        border
        resizable
        keep-source
        align="center"
        :max-height="screenHeight - 320"
        id="multipleTable"
        ref="multipleTable"
        :loading="basisListLoading"
        :custom-config="tableCustom"
        :data="basisList"
        :columns="columns"
        :toolbar-config="tableToolbar"
        :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
        :sort-config="{ remote: true }"
        @checkbox-all="selectAll"
        @checkbox-change="selectChange"
      >
        <template v-slot:toolbar_buttons>
          <div style="text-align: right">
            <el-button
              :loading="basisListLoading || exportLoading"
              type="primary"
              @click="handleExport"
              >导出</el-button
            >
            <el-button
              v-if="isConfirm"
              :loading="basisListLoading"
              type="primary"
              @click="handleOperate('confirm')"
              >批量确认</el-button
            >
             <el-button
              v-if="isConfirm"
              :loading="basisListLoading"
              type="primary"
              @click="handleOperate('cancel')"
              >取消确认</el-button
            >
             <el-button
              v-if="isConfirm"
              :loading="basisListLoading"
              type="primary"
              @click="handleReject"
              >批量驳回</el-button
            >
          </div>
        </template>
        <template v-slot:sku="{ row }">
          <div style="display: flex; flex-wrap: wrap">
            {{ row.sku }}
            <el-tag
              size="small"
              v-for="item in row.tagsDesc"
              :key="item"
              style="margin-left: 6px; margin-bottom: 4px"
              >{{ item }}</el-tag
            >
          </div>
        </template>
        <template v-slot:fourth_catalog_name="{ row }">
          {{
            `${row.firstCatalogName} / ${row.secondCatalogName} / ${row.thirdCatalogName} / ${row.fourthCatalogName}`
          }}
        </template>
        <template v-slot:factory="{ row }"
          >{{ row.factory }}&ensp;{{ row.factoryName }}</template
        >
        <template v-slot:supplier="{ row }"
          >{{ row.supplierCode }}&ensp;{{ row.supplierName }}</template
        >
        <template v-slot:number="{ row, column }">
          {{
            row[column.property] === null
              ? "--"
              : numFormatThousand(row[column.property])
          }}
        </template>
        <template v-slot:action="{ row }">
          <el-button
            v-if="isConfirm && [0, -1].includes(row.status) && [0, -1].includes(row.confirmStatus)"
            type="text"
            @click="handleOperate('confirm', [row])"
          >
            确认
          </el-button>
          <el-button
            v-if="isConfirm && [0, -1].includes(row.status) && [1].includes(row.confirmStatus)"
            type="text"
            @click="handleOperate('cancel', [row])"
          >
            取消确认
          </el-button>
          <el-button
            v-if="isConfirm && [0, -1].includes(row.status) && [0].includes(row.confirmStatus)"
            type="text"
            @click="handleReject([row])"
          >
            驳回
          </el-button>
        </template>
      </vxe-grid>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      title="驳回原因"
      :visible.sync="rejectData.show"
      width="600px"
      :show-close="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <el-input
        type="textarea"
        rows="4"
        v-model="rejectData.reason"
      ></el-input>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleRejectConfirm" :loading="basisListLoading">提交</el-button>
        <el-button type="primary" @click="handleRejectHide">取消</el-button>
      </p>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPurchaseDetailList,
  approvePurchasePlanDetail,
  exportPurchasePlanDetailList,
  getMaterialGroupListApi,
  getBrandListApi,
  getAllPosition
} from '@/api/purchasePlan';
import { getSupplierOptions } from '@/api/mm';
import { numFormatThousand } from '@/utils/index'
import { mapState } from 'vuex';
import {
  typeOptions,
  statusOptions,
  confirmStatusOptions,
  suggestedOptions,
  typeMap,
  suggestedMap,
  statusMap,
  purchaseDetaulListColumns
} from './const';
import moment from 'moment';

export default {
  name: 'purchasePlanDetailList',
  data() {
    return {
      numFormatThousand,
      suggestedOptions,
      typeOptions,
      statusOptions,
      confirmStatusOptions,
      typeMap,
      suggestedMap,
      statusMap,
      brandIdOptions: [],
      brandIdLoading: false,
      supplierOptions: [],
      supplierLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      basisListLoading: false,
      exportLoading: false,
      basisList: [],
      searchForm: {
        materialGroupIds: [],
        planTimeFrom: moment(new Date()).format('yyyy-MM'),
        planTimeTo: ''
      },
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      selectedList: [],
      positions: [],
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      screenHeight: document.body.clientHeight,
      columns: purchaseDetaulListColumns,
      selectRows: [],
      rejectData: {
        show: false,
        reason: '',
        data: []
      },
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(this.searchForm.planTimeFrom)).getTime()
        }
      }
    };
  },
  computed: {
    ...mapState(['userRole']),
    isConfirm() {
      return !!~this.userRole.indexOf('采购计划策略');
    }
  },
  created() {
    // this.handleFilter();
    this.getPositions();
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      delete this.searchForm.stockDaysMax;
      delete this.searchForm.planTimeTo;
    },
    async getPositions () {
      let res = await getAllPosition()
      if (res.status === 200) {
        this.positions = res.result;
      }
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'

        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      } else {
        this.materialGroupOptions = [];
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    // 远程查找供应商
    remoteSupplier(query) {
      const key = query.trim();
      if (key !== '') {
        this.supplierLoading = true;
        getSupplierOptions({
          supplierNoOrName: key
        }).then((res) => {
          this.supplierLoading = false;
          this.supplierOptions = res || []
        });
      } else {
        this.supplierOptions = [];
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      if (
        this.searchForm.stockDaysMin &&
        this.searchForm.stockDaysMax &&
        this.searchForm.stockDaysMin > this.searchForm.stockDaysMax
      ) {
        this.$message.error({ message: '备货后预计周转天数最小值不能大于最大值！' });
        return;
      }
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      param.sku = param.sku ? param.sku.split(' ').filter(item => !!item) : []
      if (param.sku.length > 50) {
        this.$message.error({ message: 'SKU最多为50个！' });
        return;
      }
      this.basisList = [];
      this.basisListLoading = true;
      getPurchaseDetailList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              this.basisList = res.data;
              this.total = res.total;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    gotoDetail(id, type) {
      this.$router.push(`./${type}/${id}`);
    },
    gotoTenders() {
      this.$router.push('./tenders');
    },
    selectChange({ checked, records, row }) {
      this.selectRows = records;
    },
    selectAll({ checked, records }) {
      this.selectRows = records;
    },
    handleReject(data) {
      let params = data?.length ? data : this.selectRows;
      if (params.length === 0) {
        this.$message.warning({ message: '请先选中数据！' });
        return;
      }
      this.rejectShow = true;
      this.rejectData = {
        show: true,
        reason: '',
        data: params
      }
    },
    handleRejectHide() {
      this.rejectData = {
        show: false,
        reason: '',
        data: []
      }
    },
    handleRejectConfirm() {
      if (!this.rejectData.reason) {
        this.$message.warning({ message: '驳回原因不能为空' });
        return;
      }
      this.handleOperate('reject', this.rejectData.data)
    },
    async handleOperate(operation, data) {
      let params = data?.length ? data : this.selectRows;
      if (params.length === 0) {
        this.$message.warning({ message: '请先选中数据！' });
        return;
      }
      params = params.map((a) => {
        return {
          confirmStatus: a.confirmStatus,
          id: a.id,
          planItemNo: a.planItemNo,
          planOrderNo: a.planOrderNo,
          rejectReason: operation === 'reject' ? this.rejectData.reason : a.rejectReason,
          operation
        }
      })
      this.basisListLoading = true;
      let res = await approvePurchasePlanDetail(params);
      this.basisListLoading = false;
      if (res.code === 200) {
        operation === 'reject' && this.handleRejectHide()
        this.$message.success({ message: '操作成功！' });
        this.getList();
        this.$refs.multipleTable && this.$refs.multipleTable.clearCheckboxRow();
      } else {
        this.$message.error({ message: res.msg });
      }
    },
    handleExport() {
      if (
        this.searchForm.stockDaysMin &&
        this.searchForm.stockDaysMax &&
        this.searchForm.stockDaysMin > this.searchForm.stockDaysMax
      ) {
        this.$message.error({ message: '备货后预计周转天数最小值不能大于最大值！' });
        return;
      }
      let param = { ...this.searchForm };
      param.sku = param.sku ? param.sku.split(' ').filter(item => !!item) : []
      if (param.sku.length > 50) {
        this.$message.error({ message: 'SKU最多为50个！' });
        return;
      }
      this.exportLoading = true;
      exportPurchasePlanDetailList(param)
        .then((res) => {
          this.exportLoading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.exportLoading = false;
          this.$message.error({ message: '导出失败！' });
        });
    }
  }
};
</script>

<style lang="scss">
.search-result-container {
  margin-top: 10px;
}
</style>
