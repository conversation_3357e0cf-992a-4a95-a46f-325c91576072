<template>
  <el-form ref="searchForm" style="padding-bottom: 68px;" :validate-on-rule-change="false" :model="searchForm" :rules="formRules" label-width="120px" class="search-form" label-suffix=":">
    <div style="border: 1px solid #e6ebf5; border-radius: 4px;padding: 0 20px;margin-top: 20px;">
      <Title title="基础信息" />
      <el-row>
        <el-col :span="6">
          <el-form-item label="计划单号" prop="planOrderNo">
            <el-input v-model="searchForm.planOrderNo" style="width: 100%;" disabled placeholder="保存后自动生成" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="工厂" prop="factory">
            <el-select
              v-model="searchForm.factory"
              filterable
              clearable
              :disabled="disabled"
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factory"
                :label="`${item.factory} ${item.factoryName}`"
                :value="item.factory"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物料组" prop="materialGroupId">
            <MaterialGroup v-model="searchForm.materialGroupId" @getlabel="(val) => materialGroupName = val" :default-label="materialGroupName" :disabled="disabled" style="width: 100%" placeholder="输入关键词" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="品牌" prop="brandNo">
            <Brand v-model="searchForm.brandNo" @getlabel="(val) => brandName = val" :default-label="brandName" :disabled="disabled" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="类型" prop="type">
            <el-select
              v-model="searchForm.type"
              clearable
              @change="handleTypeChange"
              placeholder="请选择类型"
              style="width: 100%"
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.key"
                :value="item.key"
                :label="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="计划年月" prop="planTime">
            <el-date-picker
              clearable
              v-model="searchForm.planTime"
              :disabled="disabled"
              type="month"
              value-format="yyyy-MM"
              style="width: 100%"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="运营策略" prop="operationType">
            <el-select style="width: 100%" v-model="searchForm.operationType" :disabled="disabled" placeholder="请选择">
              <el-option v-for="item in operationTypes" :disabled="searchForm.type === 'YEAR'" :label="item.label" :key="item.key" :value="item.key" />
              <el-option label="招投标" key="Bid" value="Bid" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </div>
    <div style="border: 1px solid #e6ebf5; border-radius: 4px;padding: 0 20px 20px;margin-top: 20px;">
      <Title title="计划明细" />
      <div class="flex button-wrap">
        <el-button :disabled="disabled" type="primary" @click="addItem">新增</el-button>
        <el-dropdown style="margin-left: 10px;">
          <el-button type="primary" :disabled="disabled" :loading="importLoading">批量导入<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-upload
                :action="`/api-ab/stockStrategyPurchasePlan/import?factory=${searchForm.factory}`"
                :show-file-list="false"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :before-upload="beforeUpload"
                accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                style="display: inline-block; margin-left: 10px"
                name="file"
              >
                <el-button
                  type="text"
                  style="margin-right: 5px"
                  :loading="importLoading"
                  :disabled="disabled"
                  >导入</el-button
                >
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button type="text" :disabled="disabled" :loading="importLoading"  @click="handleExport">
                下载模版
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button style="margin-left: 10px;" :disabled="disabled" type="danger" @click="handleDelete">批量删除</el-button>
      </div>
      <vxe-table
        ref="tableRef"
        :virtual-scroll="true"
        :scroll-y="{ gt: 100, enabled: true }"
        border
        :data="list"
        :max-height="600"
      >
        <vxe-column align="center" type="checkbox" width="50" fixed="left"/>
        <vxe-column title="*SKU" width="140" align="center">
          <template slot="header">
            <span class="required">SKU</span>
          </template>
          <template slot-scope="{row,rowIndex}">
            <el-select
              v-if="!row.import"
              v-model="row.sku"
              filterable
              placeholder="请输入SKU"
              style="width: 100%"
              remote
              :remote-method="(val) => remoteSku(val, row)"
              :loading="row.skuLoading"
              @change="(val) => skuChange(val, rowIndex)"
            >
              <el-option
                v-for="item in row.skuList"
                :key="item.skuNo"
                :label="`${item.skuNo}`"
                :value="item.skuNo"
              >
              </el-option>
            </el-select>
            <el-input
              v-else
              v-model="row.sku"
              @focus="handleClick(row)"
              placeholder="请输入SKU"
              style="width: 100%"
              @change="(val) => skuChange(val, rowIndex)"
            />
          </template>
        </vxe-column>
        <vxe-column align="center" field="skuName" width="200" title="物料描述" show-overflow-tooltip />
        <vxe-column align="center" field="catalogName" width="120" title="类目" show-overflow-tooltip />
        <vxe-column align="center" field="itemType" width="120" title="数据类型" show-overflow-tooltip />
        <vxe-column align="center" field="stockReason" width="140" title="备货原因" show-overflow-tooltip>
          <template slot-scope="{row}">
            <el-input v-model="row.stockReason" :disabled="row.itemType !== '人工'" placeholder="请输入备货原因" maxlength="20" clearable />
          </template>
        </vxe-column>
        <vxe-column align="center" field="sourceTypeName" width="100" title="商品来源" show-overflow-tooltip />
        <vxe-column title="*库存地点" width="200" align="center">
          <template slot="header">
            <span class="required">库存地点</span>
          </template>
          <template slot-scope="{row,rowIndex}">
            <el-select
              v-if="!row.import"
              v-model="row.position"
              filterable
              placeholder="请选择库位"
              style="width: 100%"
              :loading="row.positionLoading"
              @change="(val) => positionChange(val, rowIndex)"
            >
              <el-option
                v-for="a in positionList"
                :key="a.code"
                :value="a.code"
                :label="`${a.code} ${a.name}`"
              ></el-option>
            </el-select>
            <el-input
              v-else
              v-model="row.position"
              placeholder="请输入"
              style="width: 100%"
              @focus="handleClick(row)"
              @change="(val) => positionChange(val, rowIndex)"
            />
          </template>
        </vxe-column>
        <vxe-column title="*计划采购量" width="120" align="center">
          <template slot="header">
            <span class="required">计划采购量</span>
          </template>
          <template slot-scope="{row}">
            <el-input-number
              style="width: 100%;"
              v-model="row.planQty"
              :controls="false"
              :min="0"
            />
          </template>
        </vxe-column>
        <vxe-column align="center" field="aiQty" width="120" title="AI计算采购量" show-overflow-tooltip />
        <vxe-column align="center" field="supplierNo" width="160" title="*建议供应商" show-overflow-tooltip >
          <template slot="header">
            <span class="required">建议供应商</span>
          </template>
          <template slot-scope="{row}">
            <Supplier v-if="!row.import" v-model="row.supplierNo" :sku-no="row.sku" :factory="searchForm.factory" />
            <el-input
              v-else
              @focus="handleClick(row)"
              v-model="row.supplierNo"
              placeholder="请输入"
              style="width: 100%"
            />
          </template>
        </vxe-column>
        <vxe-column align="center" field="inStockQty" width="120" title="在库可用量" show-overflow-tooltip />
        <vxe-column align="center" field="moqAndMpq" width="120" title="MOQ/MPQ" show-overflow-tooltip />
        <vxe-column align="center" field="onWayQty" width="120" title="在途可用量" show-overflow-tooltip />
        <vxe-column align="center" field="unit" width="120" title="单位" show-overflow-tooltip />
        <vxe-column align="center" field="price" width="120" title="参考单价" show-overflow-tooltip />
        <vxe-column title="实际采购价格" width="140" align="center">
          <template v-if="searchForm.operationType !== 'Bid'" slot="header">
            <span class="required">实际采购价格</span>
          </template>
          <template slot-scope="{row}">
            <div v-if="searchForm.operationType !== 'Bid'" class="flex">
              <el-input-number
                style="width: 90%;"
                v-model="row.actualPurchasePrice"
                :controls="false"
                :min="0"
              />
              <span>
                <i v-if="row.actualPurchasePrice > row.price" class="el-icon-top" style="color: #F56C6C;line-height: 32px"></i>
                <i v-if="row.actualPurchasePrice < row.price" class="el-icon-bottom" style="color: #67C23A;line-height: 32px"></i>
              </span>
            </div>
            <div v-else>
              <el-input
                style="width: 90%;"
                disabled
                v-model="row.actualPurchasePrice"
              />
            </div>
          </template>
        </vxe-column>
        <vxe-column title="计划采购额" field="purchasePrice" width="140" align="center">
          <template slot-scope="{row}">
            {{ getPurchasePrice(row) }}
          </template>
        </vxe-column>
        <vxe-column align="center" field="beforeStockTurnoverDays" width="120" title="备货前周转（天）" show-overflow-tooltip />
        <vxe-column align="center" field="afterStockTurnoverDays" width="120" title="备货后周转（天）" show-overflow-tooltip />
        <!-- <vxe-column align="center" fixed="right" prop="operate" width="60" label="操作">
          <template slot-scope="{row}">
            <el-button type="text" @click="handleDelete(row)">删除</el-button>
          </template>
        </vxe-column> -->
      </vxe-table>
    </div>
    <div class="strategy-fixed-btn-wrap">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button type="primary" @click="handleSubmit">保存并提交</el-button>
    </div>
  </el-form>
</template>

<script>
import Title from './components/strategyStock/Title.vue'
import MaterialGroup from '@/components/SearchFields/materialGroup';
import Brand from '@/components/SearchFields/brand';
import Supplier from '@/pages/stockpileStrategy/components/selectSkuSupplier.vue';
import { searchSkuList } from '@/api/orderSale';
import {
  getWarehouseCode
} from '@/api/stockpileNew';
import { getAllFactory, submitPurchasePlan, createPurchasePlan, getPositionByFactory, exportPurchasePlanDetail, getPurchasePlanSkuInfo } from '@/api/purchasePlan'
import { operationTypes, typeOptions } from './const'
export default {
  components: {
    Title,
    MaterialGroup,
    Brand,
    Supplier
  },
  data() {
    return {
      selectedList: [],
      list: [],
      positionList: [],
      factoryList: [],
      operationTypes,
      typeOptions,
      importLoading: false,
      disabled: false,
      searchForm: {
        factory: '',
        materialGroupId: '',
        brandNo: '',
        type: '',
        operationType: '',
        planOrderNo: ''
      },
      brandName: '',
      materialGroupName: '',
      factoryName: '',
      formRules: {
        factory: [
          { required: true, message: '请选择工厂', trigger: 'change' }
        ],
        materialGroupId: [
          { required: true, message: '请选择物料组', trigger: 'change' }
        ],
        operationType: [
          { required: true, message: '请选择运营策略', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        brandNo: [
          { required: true, message: '请选择品牌', trigger: 'change' }
        ],
        planTime: [
          { required: true, message: '请选择计划年月', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    'searchForm.factory': {
      handler(val) {
        if (val) {
          getPositionByFactory([val]).then(res => {
            this.positionList = res.result.map(item => {
              return {
                code: item.position,
                name: item.positionName
              }
            })
          })
          this.factoryName = this.factoryList.find(item => item.factory === val)?.factoryName || ''
        }
      },
      immediate: true
    },
    'searchForm.operationType': function (val) {
      if (val === 'Consignment' || val === 'Purchase') {
        this.formRules.brandNo[0].required = false
      } else {
        this.formRules.brandNo[0].required = true
      }
      if (val === 'Bid') {
        this.list = this.list.map(it => {
          return {
            ...it,
            actualPurchasePrice: ''
          }
        })
      }
    }
  },
  created() {
    getAllFactory().then((res) => {
      if (res.status === 200) {
        this.factoryList = res.result;
      }
    })
    this.rowId = 0
  },
  unmounted() {
    this.searchForm = {
      factory: '',
      materialGroupId: '',
      brandNo: '',
      type: '',
      operationType: '',
      planOrderNo: ''
    }
    this.list = []
  },
  methods: {
    handleSelectionChange (val) {
      this.selectedList = val
    },
    getPurchasePrice(row) {
      if (this.searchForm.operationType === 'Bid') {
        if (row.planQty >= 0 && row.price >= 0) {
          return (row.planQty * row.price)?.toFixed(6)
        } else {
          return ''
        }
      }
      if (row.planQty >= 0 && row.actualPurchasePrice >= 0) {
        return (row.planQty * row.actualPurchasePrice)?.toFixed(6)
      }
      return row.purchasePrice >= 0 ? row.purchasePrice : ''
    },
    handleTypeChange(val) {
      if (val === 'YEAR') {
        this.searchForm.operationType = 'Bid'
      }
    },
    // 添加行
    addItem() {
      this.$refs.searchForm.validate(async valid => {
        if (valid) {
          this.list.push({ rowId: ++this.rowId })
        }
      })
    },
    // 批量删除行
    async handleDelete() {
      const selectedList = this.$refs.tableRef.getCheckboxRecords()
      if (!selectedList.length) {
        this.$message.error('请选择要删除的行')
        return
      }
      this.list = this.list.filter(item => !selectedList.includes(item))
    },
    handleSave(type) {
      this.$refs.searchForm.validate(async valid => {
        if (valid) {
          if (this.list.length === 0) {
            this.$message.warning('请添加采购计划明细')
            return
          }
          try {
            const params = {
              ...this.searchForm,
              creator: window.CUR_DATA.user.name,
              factoryName: this.factoryName,
              materialGroup: this.materialGroupName,
              brandName: this.brandName,
              planItemVOList: this.list.map(item => {
                return {
                  ...item,
                  positionList: [],
                  skuList: []
                }
              })
            }
            const res = type === 'submit' ? await submitPurchasePlan(params) : await createPurchasePlan(params)
            if (res.code === 200) {
              this.$message.success(type === 'submit' ? '提交成功' : '保存成功')
              this.searchForm.planOrderNo = res.data
              this.$router.push({
                path: '/purchasePlan/list'
              })
            } else {
              this.$message.error({
                message: res.msg.replaceAll('\n', '<br>'),
                duration: 8000,
                dangerouslyUseHTMLString: true
              })
            }
          } catch (error) {
            this.$message.error(error.msg || error.message || '操作失败')
          }
        }
      })
    },
    handleSubmit() {
      this.handleSave('submit')
    },
    handleCancel() {
      this.$router.back()
      this.$closeTag(this.$route.path);
    },
    // 导出
    handleExport() {
      exportPurchasePlanDetail()
    },
    // 远程查找sku列表
    async remoteSku(search, row) {
      const params = search;
      this.$set(row, 'skuLoading', true)
      const res = await searchSkuList(params)
      this.$set(row, 'skuLoading', false)
      if (res.code === 200) {
        this.$set(row, 'skuList', Array.isArray(res.data) && res.data.length > 0 ? res.data : [])
      } else {
        this.$set(row, 'skuList', [])
        this.$message.error(res.msg);
      }
    },
    // sku更改后清空行数据
    async skuChange(val, index) {
      let row = this.list[index]
      let item = row.skuList?.find((a) => a.skuNo === val)
      const { data } = await getPurchasePlanSkuInfo({
        factory: this.searchForm.factory,
        sku: val
      });
      const {
        price = '',
        forecastPurchaseQty = 0
      } = data[0];
      const forecastPrice = price ? price * forecastPurchaseQty : 0;
      row = {
        ...data[0],
        rowId: row.rowId,
        sku: val,
        skuName: item?.materialDescribe || '',
        catalogName: data[0].firstCatalogName + '/' + data[0].secondCatalogName + '/' + data[0].thirdCatalogName + '/' + data[0].fourthCatalogName,
        skuList: row.skuList || [],
        forecastPurchaseQty,
        forecastPrice,
        actualPurchasePrice: this.searchForm.operationType !== 'Bid' ? price : '',
        moqAndMpq: `${data[0].purchaseMoq ?? '0'}/${data[0].purchaseMpq ?? '0'}`,
        aiQty: data[0].aiQty ?? 0,
        itemType: data[0].itemType || '人工'
      }
      this.$set(row, 'positionLoading', true)
      let res = await getWarehouseCode({ factorySet: [this.searchForm.factory], skuSet: [val], positionScope: 1 })
      this.$set(row, 'positionLoading', false)
      if (res.result?.length) {
        let arr = []
        res.result.forEach((a) => {
          arr = [...arr, ...a.allPosition]
        })
        row.positionList = arr
      } else {
        this.$message.error(res.msg);
      }
      this.list[index] = row;
      this.list = [...this.list]
    },
    async positionChange(val, index) {
      let row = this.list[index]
      let item = row.positionList?.find((a) => a.code === val)
      const { data } = await getPurchasePlanSkuInfo({
        factory: this.searchForm.factory,
        sku: row.sku,
        position: val
      });
      console.log(data[0])
      row = {
        ...row,
        onWayQty: data[0]?.onWayQty,
        inStockQty: data[0]?.inStockQty,
        positionName: item?.name
      }
      this.list[index] = row;
      this.list = [...this.list]
    },
    handleClick(row) {
      this.$set(row, 'import', false)
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      return new Promise((resolve, reject) => {
        this.$refs.searchForm.validate(async valid => {
          if (valid) {
            this.importLoading = true;
            resolve()
          } else {
            console.log(valid)
            reject(valid)
          }
        })
      })
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.code === 200) {
        this.$message.success(response.msg || '导入成功！');
        this.list = response.data.map(item => {
          return {
            ...item,
            import: true,
            rowId: ++this.rowId,
            actualPurchasePrice: this.searchForm.operationType !== 'Bid' ? item.actualPurchasePrice : '',
            catalogName: item.firstCatalogName + '/' + item.secondCatalogName + '/' + item.thirdCatalogName + '/' + item.fourthCatalogName,
            moqAndMpq: `${item.purchaseMoq ?? '0'}/${item.purchaseMpq ?? '0'}`,
            aiQty: item.aiQty ?? 0,
            itemType: item.itemType || '人工'
          }
        });
      } else {
        this.$message.error((response && response.msg) || '导入失败！');
      }
    },
    onUploadError(error) {
      console.log(error);
      this.importLoading = false;
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    }
  }
}
</script>

<style scoped>
.required {
  &:before {
    content: '*';
    color: #ff7268;
    margin-right: 4px;
  }
  &::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
.flex {
  display: flex;
}
.button-wrap {
  justify-content: flex-end;
  margin-bottom: 12px;
}
.tips {
  color: #999;
  font-size: 11px;
  display: block;
  width: 50%;
  line-height: 16px;
  margin-top: 8px;
}
.strategy-fixed-btn-wrap {
  position: fixed;
  width: calc(100% - 264px);
  z-index: 99;
  bottom: 0;
  padding: 12px 24px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  box-shadow: rgb(25 26 35 / 20%) 0px -1px 20px 0px;
}
</style>
