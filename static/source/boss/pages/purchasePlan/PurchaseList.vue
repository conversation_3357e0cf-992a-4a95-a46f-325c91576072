<template>
  <div class="app-container">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
        :show-message="false"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="采购计划单号：" prop="planOrderNo">
              <el-input
                v-model="searchForm.planOrderNo"
                placeholder="请输入采购计划单号"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组：" prop="materialGroupIds">
              <el-select
                v-model="searchForm.materialGroupIds"
                filterable
                clearable
                placeholder="请输入物料组，支持多选"
                style="width: 100%"
                remote
                multiple
                reserve-keyword
                :remote-method="remoteMaterialGroupIdMethod"
                :loading="materialGroupIdLoading"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌：" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item.brandId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划月份：" prop="planTimeFrom">
              <el-date-picker
                clearable
                v-model="searchForm.planTimeFrom"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择起始月份"
              />
              <span
                style="display: inline-block; width: 10%; text-align: center"
                >-</span
              >
              <el-date-picker
                clearable
                v-model="searchForm.planTimeTo"
                type="month"
                value-format="yyyy-MM"
                style="width: 45%"
                placeholder="请选择结束月份"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
          <el-form-item label="类型：" prop="type">
            <el-select
                v-model="searchForm.type"
                clearable
                placeholder="请选择类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="运营策略：" prop="suggestedOperationType">
              <el-select
                v-model="searchForm.suggestedOperationType"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择运营策略"
              >
                <el-option
                  v-for="item in suggestedOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="计划单状态：" prop="status">
              <el-select
                v-model="searchForm.status"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择计划单状态"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="申请人" prop="creator">
              <select-security-user v-model="searchForm.creator" width="100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <div style="text-align: right; margin-bottom: 10px">
        <el-button
          v-if="role1 || role2"
          type="primary"
          :loading="basisListLoading"
          @click="createPlan"
          >创建</el-button
        >
        <el-button
          v-if="role1 || role2"
          type="primary"
          :loading="basisListLoading"
          @click="handleOperate(true, 'subject')"
          >批量提交</el-button
        >
        <el-button
          v-if="role2"
          type="primary"
          :loading="basisListLoading"
          @click="handleOperate(false, 'approve')"
          >批量通过</el-button
        >
        <el-button
          v-if="role2"
          :loading="basisListLoading"
          @click="handleOperate(false, 'reject')"
          >批量驳回</el-button
        >
        <el-button
          v-if="role1 || role2"
          :loading="basisListLoading"
          @click="handleOperate(false,'withdraw')"
          >批量撤回</el-button
        >
        <el-button
          v-if="role1 || role2"
          type="primary"
          :loading="basisListLoading"
          @click="gotoTenders"
          >创建招标</el-button
        >
      </div>
      <el-table v-loading="basisListLoading" :data="basisList" border fit @selection-change="handleSelectionChange">
        <el-table-column align="center" type="selection" width="50" fixed="left"/>
        <el-table-column prop="planOrderNo" label="计划单号" min-width="110">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="gotoDetail(row.planOrderNo, 'detail')">
              {{ row.planOrderNo }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="计划来源" width="100" align="center">
          <template slot-scope="{ row }">
            {{ sourceMap[row.source] }}
          </template>
        </el-table-column>
        <el-table-column prop="planTime" label="计划年月" width="80" align="center"/>
        <el-table-column prop="factory" label="工厂" min-width="140" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.factory }}&ensp;{{ row.factoryName }}
          </template>
        </el-table-column>
        <el-table-column prop="materialGroup" label="物料组" min-width="120" show-overflow-tooltip/>
        <el-table-column prop="type" label="类型" width="80" align="center">
          <template slot-scope="{ row }">
            {{ typeMap[row.type] }}
          </template>
        </el-table-column>
        <el-table-column prop="operationType" label="运营策略" align="center" width="80">
          <template slot-scope="{ row }">
            {{ suggestedMap[row.operationType] }}
          </template>
        </el-table-column>
        <el-table-column prop="brandName" label="品牌" width="100" show-overflow-tooltip/>
        <el-table-column
          prop="totalQty"
          label="总计划采购量"
          align="right"
          width="110"
        >
          <template slot-scope="{ row }">
            {{ row.totalQty === null ? '--' : numFormatThousand(row.totalQty)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="totalPrice"
          label="总计划采购额"
          align="right"
          width="110"
        >
          <template slot-scope="{ row }">
            {{ row.totalPrice === null ? '--' : numFormatThousand(row.totalPrice)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="sendQty"
          label="已下发招标量"
          align="right"
          width="110"
        >
          <template slot-scope="{ row }">
            {{ row.sendQty === null ? '--' : numFormatThousand(row.sendQty)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="sendPrice"
          label="已下发招标金额"
          align="right"
          width="110"
        >
          <template slot-scope="{ row }">
            {{ row.sendPrice === null ? '--' : numFormatThousand(row.sendPrice)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="totalPurchasedQty"
          label="已采购量"
          align="right"
          width="110"
        >
          <template slot-scope="{ row }">
            {{ row.totalPurchasedQty === null ? '--' : numFormatThousand(row.totalPurchasedQty)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="totalPurchasedPrice"
          label="已采购金额"
          align="right"
          width="110"
        >
          <template slot-scope="{ row }">
            {{ row.totalPurchasedPrice === null ? '--' : numFormatThousand(row.totalPurchasedPrice)}}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="计划单状态" width="70" align="center">
          <template slot-scope="{ row }">
            {{ statusMap[row.status + ''] }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="申请人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="action" label="操作" width="120" fixed="right">
          <template slot-scope="{ row }">
            <el-button
              v-if="(row.status == 0 || row.status == -1) && (role1 || role2) && row.ifEdit"
              type="text"
              @click="gotoDetail(row.planOrderNo, 'edit')"
            >
              编辑
            </el-button>
            <el-button
              v-if="(row.status == 0 || row.status == -1) && (role1 || role2) && row.ifEdit"
              type="text"
              @click="handleOperate(true, 'subject', row)"
            >
              提交
            </el-button>
            <el-button
              v-if="row.status == 2 && role2"
              type="text"
              @click="handleOperate(false, 'withdraw', row)"
            >
              撤回
            </el-button>
            <el-button
              v-if="row.status == 1 && ['Consignment', 'Bid'].includes(row.operationType) && role2"
              type="text"
              @click="handleOperate(false, 'approve', row)"
            >
              通过
            </el-button>
            <el-button
              v-if="row.status == 1 && ['Consignment', 'Bid'].includes(row.operationType) && role2"
              type="text"
              @click="handleOperate(false, 'reject', row)"
            >
              驳回
            </el-button>
            <el-button
              v-if="row.status == 2 && (role3 || isCurUser(row.creator))"
              type="text"
              @click="handleOperateConfirm(false, 'invalid', row)"
            >
              作废
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { getPurchasePlanList, approvePurchasePlan, getMaterialGroupListApi, getBrandListApi, submitPurchasePlanItems } from '@/api/purchasePlan';
import { numFormatThousand } from '@/utils/index'
import { typeOptions, statusOptions, suggestedOptions, typeMap, suggestedMap, statusMap, sourceMap } from './const';
import { mapState } from 'vuex';
import moment from 'moment';
import selectSecurityUser from './components/SelectUser.vue';

export default {
  components: { selectSecurityUser },
  name: 'purchasePlanList',
  data() {
    return {
      numFormatThousand,
      suggestedOptions,
      typeOptions,
      statusOptions,
      typeMap,
      sourceMap,
      suggestedMap,
      statusMap,
      brandIdOptions: [],
      brandIdLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      basisListLoading: false,
      basisList: [],
      searchForm: {
        planOrderNo: '',
        materialGroupIds: [],
        brandNo: '',
        planTimeFrom: moment(new Date()).format('yyyy-MM'),
        planTimeTo: ''
      },
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      selectedList: [],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(this.searchForm.planTimeFrom)).getTime()
        }
      }
    };
  },
  computed: {
    ...mapState(['userRole']),
    ...mapState(['userInfo']),
    role1() {
      return !!~this.userRole.indexOf('采购计划运营');
    },
    role2() {
      return !!~this.userRole.indexOf('采购计划-产线负责人');
    },
    role3() {
      return !!~this.userRole.indexOf('采购计划策略');
    }
  },
  created() {
    const planOrderNo = this.$route.query.planOrderNo || '';
    this.searchForm.planOrderNo = planOrderNo.replaceAll(',', ' ');
    this.handleFilter();
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      from.name === 'purchasePlanEdit' && vm.getList()
    })
  },
  methods: {
    isCurUser(creator) {
      const username = window.CUR_DATA.user.name
      return creator === username
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      delete this.searchForm.planTimeTo;
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'

        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      } else {
        this.materialGroupOptions = [];
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      this.basisList = [];
      this.basisListLoading = true;
      getPurchasePlanList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              this.basisList = res.data;
              this.total = res.total;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    gotoDetail(id, type) {
      this.$router.push(`./${type}/${id}`);
    },
    gotoTenders() {
      this.$router.push('./tenders');
    },
    createPlan() {
      this.$router.push('./createPlan');
    },
    handleSelectionChange (val) {
      this.selectedList = val
    },
    handleOperateConfirm(check, operation, row) {
      this.$confirm('作废采购计划会将未转为采购订单的行从MRP清除，不再执行转单。此操作无法撤销，请确认！', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.handleOperate(check, operation, row)
      })
    },
    async handleOperate(check, operation, row) {
      let params = row ? [row] : this.selectedList;
      if (params.length === 0) {
        this.$message.warning({ message: '请先选择数据！' });
        return;
      }
      const isSubmit = operation === 'subject';
      params = params.map((a) => {
        return isSubmit ? a.planOrderNo : {
          id: a.id,
          operation: operation,
          planOrderNo: a.planOrderNo,
          remark: a.remark,
          status: a.status
        }
      })
      this.basisListLoading = true;
      let res = isSubmit ? await submitPurchasePlanItems(params) : await approvePurchasePlan(params, { check })
      this.basisListLoading = false;
      if (res.code === 200) {
        this.$message.success('操作成功！')
        this.getList();
      } else if (res.code === 208 && res.data) {
        this.$confirm(res.data, {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        }).then(() => {
          this.handleOperate(false, operation, row)
        })
      } else {
        this.$message.error(res.msg || '操作失败！');
      }
    }
  }
};
</script>

<style lang="scss"></style>
