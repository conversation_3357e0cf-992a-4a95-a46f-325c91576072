<template>
  <div class="app-container" style="padding-bottom: 50px;">
    <div class="step-wrap">
      <el-steps :active="currentStep" align-center finish-status="success" simple>
        <el-step title="步骤1：选择目标备货商品" />
        <el-step title="步骤2：AI推荐&人工确认" />
        <el-step title="步骤3：创建采购计划" />
      </el-steps>
    </div>
    <div v-loading="loading" class="form-wrap">
      <component :is="currentComponent" :ref="`stepComponent${currentStep}`" :detail-info="detailInfo" :disabled="disabled" :task-id="taskId" @import-success="getDetail" />
    </div>
    <div class="strategy-fixed-btn-wrap">
      <el-button @click="handleCancel">返回</el-button>
    </div>
  </div>
</template>

<script>
import Step1 from './components/strategyStock/Step1.vue'
import Step2 from './components/strategyStock/Step2.vue'
import Step3 from './components/strategyStock/Step3.vue'
import { getStrategyStockDetail, getStrategyHeaderForStep2 } from '@/api/purchasePlan'
export default {
  components: {
    Step1,
    Step2,
    Step3
  },
  data() {
    return {
      currentStep: 0,
      detailInfo: {},
      status: '',
      disabled: true,
      taskId: '',
      type: '',
      loading: false
    }
  },
  async mounted() {
    this.taskId = this.$route.params.id
    this.type = this.$route.query.type
    if (this.taskId === '0') {
      this.currentStep = 0
    } else {
      await this.getStatus()
      // 根据状态初始化当前步骤
      if (this.status === 'New') {
        this.currentStep = 0
        this.getDetail()
      } else {
        this.currentStep = 1
      }
    }
  },
  computed: {
    currentComponent: function() {
      return `Step${this.currentStep + 1}`
    }
  },
  methods: {
    async getDetail() {
      this.loading = true
      try {
        const res = await getStrategyStockDetail(this.taskId)
        if (res.code === 200) {
          this.detailInfo = res.data
        }
      } catch (error) {
        this.$message.error(error.message)
      } finally {
        this.loading = false
      }
    },
    async getStatus() {
      const res = await getStrategyHeaderForStep2(this.taskId)
      if (res.code === 200) {
        this.status = res.data.status
      }
    },
    handleCancel() {
      this.$closeTag(this.$route.path);
      this.$router.push({
        path: '/purchasePlan/strategyStock'
      })
    }
  }
}
</script>

<style>
.el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: 65%;
}
.strategy-fixed-btn-wrap {
  position: fixed;
  width: calc(100% - 264px);
  z-index: 99;
  bottom: 0;
  padding: 12px 24px;
  background: #fff;
  display: flex;
  justify-content: flex-end;
  box-shadow: rgb(25 26 35 / 20%) 0px -1px 20px 0px;
}
</style>
