<template>
  <div class="app-container">
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="120px"
      label-position="right"
      :show-message="false"
    >
      <el-row>
        <el-col :span="6">
          <el-form-item label="工厂" prop="factory">
            <el-select
              v-model="searchForm.factory"
              filterable
              clearable
              placeholder="请选择"
              style="width: 100%"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factory"
                :label="`${item.factory} ${item.factoryName}`"
                :value="item.factory"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="物料组" prop="materialGroupId">
            <el-select
              v-model="searchForm.materialGroupId"
              filterable
              clearable
              placeholder="请输入物料组，支持多选"
              style="width: 100%"
              remote
              multiple
              collapseTags
              reserve-keyword
              :remote-method="remoteMaterialGroupIdMethod"
              :loading="materialGroupIdLoading"
            >
              <el-option
                v-for="item in materialGroupOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="供应商" prop="supplierNo">
            <el-select
              v-model="searchForm.supplierNo"
              placeholder="请选择供应商"
              filterable
              clearable
              remote
              multiple
              collapseTags
              style="width:100%"
              :remote-method="remoteSupplier"
              :loading="supplierLoading"
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.supplierNo"
                :value="item.supplierNo"
                :label="`${item.supplierNo} ${item.supplierName}`"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="战略备货类型" prop="stockStrategyType">
            <el-select
              v-model="searchForm.stockStrategyType"
              filterable
              clearable
              multiple
              collapseTags
              style="width: 100%"
              placeholder="请选择"
            >
              <el-option
                v-for="item in stockTypeOptions"
                :key="item.key"
                :value="item.key"
                :label="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="状态" prop="status">
            <el-select
              v-model="searchForm.status"
              filterable
              clearable
              multiple
              collapseTags
              style="width: 100%"
              placeholder="请选择状态"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.key"
                :value="item.key"
                :label="item.label"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="采购计划单号" prop="planOrderNo">
            <el-input
              v-model="searchForm.planOrderNo"
              placeholder="请输入采购计划单号"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请时间" prop="planTimeFrom">
            <el-date-picker
              clearable
              v-model="searchForm.beginApplyTime"
              value-format="yyyy-MM-dd"
              style="width: 45%"
              placeholder="请选择起始时间"
            />
            <span
              style="display: inline-block; width: 10%; text-align: center"
              >-</span
            >
            <el-date-picker
              clearable
              v-model="searchForm.endApplyTime"
              value-format="yyyy-MM-dd"
              style="width: 45%"
              placeholder="请选择结束时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="申请人" prop="creator">
            <select-security-user v-model="searchForm.creator" width="100%" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="6">
          <el-form-item label="备货任务ID" prop="taskId">
            <el-input
              v-model="searchForm.taskId"
              placeholder="请输入备货任务ID"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="18" style="text-align: right">
          <el-button
            type="primary"
            icon="el-icon-search"
            :loading="basisListLoading"
            @click="handleFilter"
          >
            查询
          </el-button>
          <el-button icon="el-icon-search" @click="handleReset">重置</el-button>
          <el-button
            v-if="role1 || role2"
            type="primary"
            @click="gotoDetail(0, 'create')"
          >
            创建
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <div style="margin-top: 18px;">
      <el-table v-loading="basisListLoading" :min-height="400" :data="basisList" border fit @selection-change="handleSelectionChange">
        <el-table-column prop="factory" label="工厂" align="center" width="150" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.factory }}{{ factoryList.find(item => item.factory === scope.row.factory).factoryName }}
          </template>
        </el-table-column>
        <el-table-column prop="taskId" label="备货任务ID" align="center" width="120" show-overflow-tooltip/>
        <el-table-column prop="stockStrategyType" label="战略备货类型" align="center" width="120" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ stockTypeOptions.find(item => item.key === row.stockStrategyType).label }}
          </template>
        </el-table-column>
        <el-table-column prop="aiStockTotalSku" label="战略SKU数量" align="center" width="120" show-overflow-tooltip/>
        <el-table-column
          prop="aiStockTotalNum"
          label="备货总数量"
          align="center"
          width="120"
        >
          <template slot-scope="{ row }">
            {{ row.aiStockTotalSku === null ? '--' : numFormatThousand(row.aiStockTotalSku)}}
          </template>
        </el-table-column>
        <el-table-column
          prop="aiStockTotalAmount"
          label="备货总金额"
          align="center"
          width="120"
        >
          <template slot-scope="{ row }">
            {{ row.aiStockTotalAmount === null ? '--' : numFormatThousand(row.aiStockTotalAmount)}}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="{ row }">
            {{ (statusOptions.find(item => item.key === row.status) || {}).label }}
          </template>
        </el-table-column>
        <el-table-column prop="stockRecommendAlgorithm" label="备货推荐算法" width="120" align="center">
          <template slot-scope="{ row }">
            {{ stockRecommendOptions.find(item => item.key === row.stockRecommendAlgorithm).label }}
          </template>
        </el-table-column>
        <el-table-column prop="planOrderNo" label="计划单号" min-width="120" align="center" />
        <el-table-column prop="supplierNo" label="供应商" min-width="140" show-overflow-tooltip align="center">
          <template slot-scope="{ row }">
            {{ (row.supplierNo || '') + ' ' + (row.supplierName || '') }}
          </template>
        </el-table-column>
        <el-table-column prop="materialGroup" label="物料组" min-width="120" show-overflow-tooltip align="center" >
          <template slot-scope="{ row }">
            {{ row.materialGroupId + ' ' + row.materialGroup }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="申请人" width="100" show-overflow-tooltip align="center" />
        <el-table-column prop="gmtCreate" label="申请时间" width="140" show-overflow-tooltip align="center" />
        <el-table-column v-if="role1 || role2" prop="action" label="操作" width="136" fixed="right" align="center">
          <template slot-scope="{ row }">
            <el-button
              v-if="!['PurchasePland'].includes(row.status)"
              type="text"
              @click="gotoDetail(row.taskId, 'edit')"
            >
              编辑
            </el-button>
            <el-button
              v-if="!['PurchasePland'].includes(row.status)"
              type="text"
              @click="handleDelete(row.taskId)"
            >
              删除
            </el-button>
            <el-button
              type="text"
              @click="gotoDetail(row.taskId, 'view')"
            >
              查看
            </el-button>
            <el-button
              v-if="['PurchasePland'].includes(row.status)"
              type="text"
              @click="gotoPlan(row.planOrderNo)"
            >
              查看采购计划
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { getStrategyStockList, deleteStrategyStock, getMaterialGroupListApi, getAllFactory } from '@/api/purchasePlan';
import { getSupplierOptions } from '@/api/mm';
import { numFormatThousand } from '@/utils/index'
import { stockStatusOptions, stockTypeOptions, stockRecommendOptions } from './const';
import { mapState } from 'vuex';
import moment from 'moment';
import selectSecurityUser from './components/SelectUser.vue';

export default {
  components: { selectSecurityUser },
  name: 'strategyStock',
  data() {
    return {
      numFormatThousand,
      stockTypeOptions,
      statusOptions: stockStatusOptions,
      stockRecommendOptions,
      supplierOptions: [],
      supplierLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      basisListLoading: false,
      basisList: [],
      factoryList: [],
      searchForm: {
        materialGroupIds: [],
        planOrderNo: '',
        taskId: '',
        beginApplyTime: moment(new Date()).format('yyyy-MM') + '-01',
        endApplyTime: ''
      },
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      selectedList: []
    };
  },
  computed: {
    ...mapState(['userRole']),
    role1() {
      return !!~this.userRole.indexOf('采购计划运营');
    },
    role2() {
      return !!~this.userRole.indexOf('采购计划-产线负责人');
    }
  },
  created() {
    this.handleFilter();
    getAllFactory().then((res) => {
      if (res.status === 200) {
        this.factoryList = res.result;
      }
    })
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.searchForm.beginApplyTime = ''
      this.searchForm.endApplyTime = ''
      this.handleFilter()
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'

        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      }
    },
    // 远程查找供应商
    remoteSupplier(query) {
      const key = query.trim();
      if (key !== '') {
        this.supplierLoading = true;
        getSupplierOptions({
          supplierNoOrName: key
        }).then((res) => {
          this.supplierLoading = false;
          this.supplierOptions = res || []
        });
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      if (this.searchForm.beginApplyTime && this.searchForm.endApplyTime && this.searchForm.beginApplyTime > this.searchForm.endApplyTime) {
        this.$message.error('开始时间不能大于结束时间');
        return;
      }
      const status = this.searchForm.status?.join(',');
      const stockStrategyType = this.searchForm.stockStrategyType?.join(',');
      const materialGroupId = this.searchForm.materialGroupId?.join(',');
      const supplierNo = this.searchForm.supplierNo?.join(',');
      let param = {
        ...this.searchForm,
        status,
        stockStrategyType,
        materialGroupId,
        supplierNo,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      this.basisList = [];
      this.basisListLoading = true;
      getStrategyStockList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              this.basisList = res.data.records;
              this.total = res.data.total;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    gotoDetail(id, type) {
      const path = type === 'view' ? `./strategyStockDetail/${id}` : `./strategyStockEdit/${id}`
      this.$router.push(path);
    },
    gotoPlan(planOrderNo) {
      this.$closeTag('/purchasePlan/list');
      this.$router.push(`./list?planOrderNo=${planOrderNo}`);
    },
    handleSelectionChange (val) {
      this.selectedList = val
    },
    async handleDelete(id) {
      // if (this.selectedList.length === 0) {
      //   this.$message.error({ message: '请选择要删除的策略', duration: 6000 });
      //   return
      // }
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteStrategyStock([id])
        if (res.code !== 200) {
          this.$message.error(res.message || res.msg)
          return
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.handleFilter()
      })
    }
  }
};
</script>

<style lang="scss"></style>
