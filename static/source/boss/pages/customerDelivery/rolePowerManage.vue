<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="fifter">
        <el-row :span="24" class="searchBox">
          <el-col :span="2" class="dialogTitle">角色：</el-col>
          <el-col :span="4" >
            <el-select
              style="width: 100%;"
              v-model="search.role"
              @change="roleChange"
              placeholder="请选择">
              <el-option
                v-for="item in roleOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="2" class="dialogTitle">属性：</el-col>
          <el-col :span="4" >
            <el-select
              style="width: 100%;"
              class="companyOptions"
              v-model="search.propertyName"
              ref="appFormDepartSelect1"
              placeholder="请选择属性">
              <el-option
                :value="search.propertyName"
                style="height: auto; padding:0;"
              >
                <el-tree
                  :data="postOptions2"
                  :props="defaultProps"
                  show-checkbox
                  node-key="id"
                  class="departTree"
                  :load="loadNode"
                  :check-strictly="true"
                  lazy
                  ref="tree1"
                  @check="handleCheckClickAppForm1"
                ></el-tree>
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5" style="padding-left: 10px" >
            <el-button @click="searchList" icon="el-icon-search" size="small" type="primary">查询</el-button>
            <el-button @click="searchReset" icon="el-icon-close" size="small" type="info">清空</el-button>
          </el-col>
          <el-col :span="7" style="text-align: right;">
            <el-button style="margin-right: 20px;"  icon="el-icon-plus" size="medium" @click="openRuleDialog" type="primary">添加角色权限</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="c_tables">
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column align="center" prop="id" label="id"></el-table-column>
          <el-table-column align="center" prop="role" label="角色">
            <template slot-scope="scope">
              {{scope.row.role_name || scope.row.role}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="property_code" label="属性">
            <template slot-scope="scope">
              {{scope.row.property_name || scope.row.property_code}}
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button @click="modifyApp(scope.row, scope.$index)" type="text" size="small">修改</el-button>
              <el-button @click="deleteApp(scope.row, scope.$index)" type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
      <!--添加弹出框-->
      <el-dialog
        width="580px"
        title="配置信息"
        class="dialogClass"
        :visible.sync="showAddDialog"
        :close-on-click-modal="false">
        <div class="ruleset">
          <div class="rulesetcont">
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>角色：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.role"
                  :loading="loading.role"
                  @change="roleChange"
                  placeholder="请选择">
                  <el-option
                    v-for="item in roleOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>属性：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  class="companyOptions"
                  v-model="appForm.propertyName"
                  ref="appFormDepartSelect"
                  placeholder="请选择属性">
                  <el-option
                    :value="appForm.propertyName"
                    style="height: auto; padding:0;"
                  >
                    <el-tree
                      :data="postOptions2"
                      :props="defaultProps"
                      show-checkbox
                      node-key="id"
                      class="departTree"
                      :load="loadNode"
                      :check-strictly="true"
                      lazy
                      ref="tree"
                      @check="handleCheckClickAppForm"
                    ></el-tree>
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <!--<el-row :span="24">-->
              <!--<el-col :span="6" class="dialogTitle"><i>*</i>是否有权限：</el-col>-->
              <!--<el-col :span="16">-->
               <!--<div style="padding-top: 10px;">-->
                 <!--<el-radio v-model="appForm.ispower" label="1">是</el-radio>-->
                 <!--<el-radio v-model="appForm.ispower" label="2">否</el-radio>-->
               <!--</div>-->
              <!--</el-col>-->
            <!--</el-row>-->
          </div>
          <el-row :span="24">
            <el-col :span="24" style="text-align: right;">
              <el-button size="medium" @click="closeAddDialog" style="margin-left: 20px;">关闭</el-button>
              <el-button size="medium" @click="storeAddDialog" type="primary">确认</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>
      <!--添加弹出框 end-->
    </div>
  </div>
</template>

<script>
import {
  getAggregationProperty,
  getAuthorityManage,
  createAuthorityManage,
  updateAuthorityManage,
  deleteAuthorityManage
} from '@/api/customerDelivery'
import API from './common/config.ts'
import Pagination from '@/components/Pagination'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        app2: false,
        submit: false,
        role: false,
        property: false
      },
      selectPage: { // 搜索页数
        app2: 1,
        pageSize: 100
      },
      selectKey: { // 储存的搜索关键字
        app2: ''
      },
      postOptions2: [ // 岗位选项
      ],
      defaultProps: {
        children: 'children',
        label: 'propertyName'
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      search: { // 搜索值
        role: '',
        propertyCode: '',
        propertyName: ''
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        role: '', // 角色code
        roleName: '', // 角色名字
        propertyName: '', // 属性name
        propertyCode: '', // 属性code
        ispower: '1', // 是否有权限
        subCode: '' // 提交给接口的code全路径
      },
      total: 0,
      tableData: [],
      roleOptions: [
        {
          id: 'cs',
          name: '客服'
        },
        {
          id: 'cs_manager',
          name: '客服经理'
        },
        {
          id: 'delivery_manager',
          name: '交付主管'
        },
        {
          id: 'service_center_manager',
          name: '服务中心经理'
        },
        {
          id: 'service_center_area_manager',
          name: '区域服务中心经理'
        },
        {
          id: 'service_center_general_manager',
          name: '服务中心总经理'
        }
      ],
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetApps()
    this.goGetAllPostSearch2()
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.role || This.search.propertyCode) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  methods: {
    roleChange (res) {
      console.log(res)
      let This = this
      let roleOptions = This.roleOptions
      roleOptions.forEach((item) => {
        if (item.id === res) {
          This.appForm.roleName = item.name
        }
      })
    },
    loadNode (node, resolve) {
      // 加载子节点
      let This = this
      console.log(node)
      if (node.id === 0) {
        return resolve([])
      }
      let data = {
        aggregationId: This.aggregationId,
        parentId: node.data.id
      }
      getAggregationProperty(data).then(res => {
        if (res) {
          This.treeData = This.treeData.concat(res.content)
          return resolve(res.content)
        } else {
          return resolve([])
        }
      }).catch(() => {
        return resolve([])
      })
    },
    handleCheckClickAppForm1 (data) {
      // 搜索选择
      let propertyId = data.id // 属性id
      let propertyName = data.propertyName // 属性名字
      let arr = []
      arr.push(propertyId)
      this.$refs.tree1.setCheckedKeys([])
      this.$refs.tree1.setCheckedKeys(arr)
      this.$set(this.search, 'propertyName', propertyName)
      this.$refs.appFormDepartSelect1.blur()

      let propertyNodes = this.$refs.tree1.getCheckedNodes()
      let treeData = this.treeData
      let newIds = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newNames = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newCodes = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      propertyNodes.forEach((item) => {
        getParentIds(item)
      })
      // 通过node的parentId找到其父级的id，并储存起来
      function getParentIds (node) {
        let id = node.id
        let code = node.propertyCode
        let name = node.propertyName
        let parentId = node.parentId
        if (!newIds.includes(id)) {
          newIds.push(id)
          newNames.push(name)
          newCodes.push(code)
          if (parentId === -1) {
            return false
          } else {
            treeData.forEach((item) => {
              if (item.id === parentId) {
                getParentIds(item)
              }
            })
          }
        } else {
          // 存在则其父级都寻找过了不需要再寻找了
          return false
        }
      }
      this.$refs.tree1.setCheckedKeys(newIds)
      newCodes = newCodes.reverse()
      let subCode = 'factory' + '-' + newCodes.join('-')
      this.$set(this.search, 'propertyCode', subCode)
    },
    handleCheckClickAppForm (data) {
      let propertyId = data.id // 属性id
      let propertyName = data.propertyName // 属性名字
      let arr = []
      arr.push(propertyId)
      this.$refs.tree.setCheckedKeys([])
      this.$refs.tree.setCheckedKeys(arr)
      this.$set(this.appForm, 'propertyName', propertyName)
      this.$refs.appFormDepartSelect.blur()

      let propertyNodes = this.$refs.tree.getCheckedNodes()
      let treeData = this.treeData
      let newIds = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newNames = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newCodes = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      propertyNodes.forEach((item) => {
        getParentIds(item)
      })
      // 通过node的parentId找到其父级的id，并储存起来
      function getParentIds (node) {
        let id = node.id
        let code = node.propertyCode
        let name = node.propertyName
        let parentId = node.parentId
        if (!newIds.includes(id)) {
          newIds.push(id)
          newNames.push(name)
          newCodes.push(code)
          if (parentId === -1) {
            return false
          } else {
            treeData.forEach((item) => {
              if (item.id === parentId) {
                getParentIds(item)
              }
            })
          }
        } else {
          // 存在则其父级都寻找过了不需要再寻找了
          return false
        }
      }
      this.$refs.tree.setCheckedKeys(newIds)
      newCodes = newCodes.reverse()
      let subCode = 'factory' + '-' + newCodes.join('-')
      this.$set(this.appForm, 'subCode', subCode)
    },
    goGetAllPostSearch2 (name) {
      let This = this
      // 触发搜索时走这里
      if (!name) {
        name = ''
        This.selectKey.app2 = ''
      } else {
        // 按照名字搜索的时候不用分页
        This.selectKey.app2 = name
      }
      // 触发搜索说明搜索值改变了，分页要从第一页开始
      This.selectPage.app2 = 1
      This.goGetAllPost2()
    },
    goGetAllPost2 () {
      // 根据视图id去获取属性列表
      let This = this
      let name = This.selectKey.app2
      if (This.loading.app2) {
        // 防止短时间重复加载
        return false
      }
      let data = {
        name: name,
        page: 0,
        size: 200,
        parentId: -1,
        aggregationId: API.aggregationId // 所属聚合id 要区分正式测试的,
      }
      This.loading.app2 = true
      getAggregationProperty(data).then(res => {
        setTimeout(function () {
          This.loading.app2 = false
        }, 1000)
        if (res.content.length > 0) {
          // 第一页时重新赋值
          This.postOptions2 = res.content
          This.treeData = res.content
          // 下拉分页
          console.log('获取到的值')
          console.log(This.postOptions2)
        }
      })
    },
    openRuleDialog () {
      // 打开添加数据窗口
      this.appForm = { // 初始化form应用的内容
        id: '',
        role: '', // 角色code
        roleName: '', // 角色名字
        propertyName: '', // 属性name
        propertyCode: '', // 属性code
        ispower: '1', // 是否有权限
        subCode: '' // 提交给接口的code全路径
      }
      this.currAppInfo = null
      this.showAddDialog = true
      this.goGetAllPostSearch2()
    },
    closeAddDialog () {
      this.showAddDialog = false
    },
    storeAddDialog () {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp (row, index) {
      console.log('修改数据')
      console.log(row)
      let propertyCode = row.property_code.split('-')
      let maxIndex = propertyCode.length - 1
      propertyCode = propertyCode[maxIndex]
      if (row.property_code.indexOf('factory-') === -1) {
        propertyCode = row.property_code
      }
      row.propertyCode = propertyCode
      row.propertyName = row.property_name || propertyCode
      row.subCode = row.property_code
      row.roleName = row.role_name || row.role
      this.appForm = row
      this.currAppInfo = row
      console.log(row)
      this.showAddDialog = true
      console.log(this.appForm)
      this.goGetAllPostSearch2()
    },
    deleteApp (row, index) {
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          aggregationId: row.id, // 数据id
          id: row.id // 数据id
        }
        deleteAuthorityManage(data).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(res.message)
          }
        })
      }).catch(() => {
        console.log('取消删除')
      })
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    goGetApps () {
      // 【1】获取数据列表
      let This = this
      let conditionList = []
      if (This.search.role) {
        let unit = {
          'compareData': This.search.role,
          'operateChar': '=',
          'searchField': 'role'
        }
        conditionList.push(unit)
      }
      if (This.search.propertyCode) {
        let unit = {
          'compareData': This.search.propertyCode,
          'operateChar': '=',
          'searchField': 'property_code'
        }
        conditionList.push(unit)
      }
      let data = {
        pageNum: This.listQueryInfo.current,
        pageSize: This.listQueryInfo.pageSize,
        code: 'authority_manage_batch',
        aggregationCode: 'authority_manage',
        aggregationType: 'authority_manage',
        conditionList: conditionList
      }
      getAuthorityManage(data).then(res => {
        if (res.code === 0) {
          let result = res.data.result
          if (result) {
            This.tableData = result
            This.total = res.data.total
          } else {
            This.tableData = []
          }
        } else {
          This.$message.error(res.message)
        }
        This.loading.table = false
        This.loading.page = false
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = {
        role: '',
        propertyCode: '',
        propertyName: ''
      }
      if (!state) {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    gocreateApps () {
      // 新建、编辑数据
      let This = this
      let role = this.appForm.role
      let roleName = this.appForm.roleName
      let propertyCode = this.appForm.subCode
      let propertyName = this.appForm.propertyName
      This.searchReset('create')
      if (!role) {
        this.$message.error('请选择角色')
        return false
      }
      if (!propertyCode) {
        this.$message.error('请选择属性')
        return false
      }
      let appId = this.appForm.id // 修改数据id
      console.log(appId)
      let data = {
        aggregationId: appId,
        id: appId,
        operate: 'write',
        role: role,
        role_name: roleName,
        blackhole_id: API.blackholeId,
        property_name: propertyName,
        property_code: propertyCode // 属性全路径
      }
      if (appId) {
        // 修改数据
        updateAuthorityManage(data, appId).then(res => {
          if (res) {
            This.$message.success('修改成功')
            // 修改成功更新数据列表
            This.goGetApps()
            this.showAddDialog = false
          }
        })
      } else {
        // delete data.id
        // 添加数据
        createAuthorityManage(data).then(res => {
          if (res.code === 0) {
            This.$message.success('创建成功')
            // 新增成功创建数据列表
            This.tableData.unshift(res.data)
            This.showAddDialog = false
          } else {
            This.$message.success(res.message)
          }
        })
      }
    },
    gotGetRole (key) {
      console.log('获取值')
      console.log(key)
      this.loading.role = true
    },
    gotGetProperty (key) {
      console.log('获取值')
      console.log(key)
      this.loading.property = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/static/css/common.scss'; /*引入公共样式*/
</style>
