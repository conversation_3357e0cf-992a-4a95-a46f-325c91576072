<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="c_tables">
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column align="center" prop="id" width="100" label="id"></el-table-column>
          <el-table-column align="center" prop="createdTime" label="上传时间">
            <template slot-scope="scope">
              {{scope.row.createdTime | afterDateformat}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="processType" label="上传类型"></el-table-column>
          <el-table-column align="center" prop="creator.name" label="上传人"></el-table-column>
          <el-table-column align="center" label="上传情况">
            <template slot-scope="scope">
             <p>{{scope.row.processResult}}</p>
              <a v-if="scope.row.rejectedDataUrl" :href="scope.row.rejectedDataUrl" target="_blank">下载</a>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getUploadList
} from '@/api/customerDelivery'
import Pagination from '@/components/Pagination'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        role: false,
        property: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      search: { // 搜索值
        role: '',
        property: ''
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        role: [], // 角色
        property: [], // 属性
        ispower: '1' // 是否有权限
      },
      total: 0,
      tableData: [],
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetApps()
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.role || This.search.property) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  filters: {
    /**
     * @param {string} date
     * 格式化后台返回的此种格式的时间
     * 2021-07-28T01:53:53.000+0000
     */
    afterDateformat: function (date) {
      if (date && date.indexOf('T')) {
        const dateee = new Date(date).toJSON()
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      } else {
        return ''
      }
    }
  },
  methods: {
    openRuleDialog () {
      // 打开添加数据窗口
      this.appForm = { // 初始化form应用的内容
        id: '',
        role: [], // 角色
        property: [], // 属性
        ispower: '1' // 是否有权限
      }
      this.currAppInfo = null
      this.showAddDialog = true
    },
    closeAddDialog () {
      this.showAddDialog = false
    },
    storeAddDialog () {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp (row, index) {
      console.log('修改数据')
      this.appForm = {
        id: row.id, // 修改时候的appid
        role: row.role, // 角色
        property: row.property, // 属性
        ispower: row.ispower // 是否有权限
      }
      this.currAppInfo = row
      this.showAddDialog = true
    },
    deleteApp (row, index) {
      this.$confirm('您确认要删除此岗位吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        console.log(1)
      }).catch(() => {
        console.log('取消删除')
      })
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    goGetApps () {
      // 【1】获取数据列表
      let This = this
      let data = {
        page: This.listQueryInfo.current - 1,
        size: This.listQueryInfo.pageSize,
        aggregationCode: 'factory'
      }
      console.log('数据')
      console.log(data)
      getUploadList(data).then(res => {
        if (res) {
          This.tableData = res.content
          This.total = res.totalElements
        }
        This.loading.table = false
        This.loading.page = false
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = {
        role: '',
        property: ''
      }
      if (!state) {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    gocreateApps () {
      // 新建、编辑数据
      let This = this
      let role = this.appForm.role
      let property = this.appForm.property
      This.searchReset('create')
      if (role.length <= 0) {
        this.$message.error('请选择角色')
        return false
      }
      if (property.length <= 0) {
        this.$message.error('请选择属性')
        return false
      }
      let appId = this.appForm.id // 修改数据id
      console.log(appId)
    },
    gotGetRole (key) {
      console.log('获取值')
      console.log(key)
      this.loading.role = true
    },
    gotGetProperty (key) {
      console.log('获取值')
      console.log(key)
      this.loading.property = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/static/css/common.scss'; /*引入公共样式*/
</style>
