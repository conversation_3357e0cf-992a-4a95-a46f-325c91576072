.zZindex {
  z-index:99999 !important;
}
.formts {
p.ts {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
}
.dialogTitle {
  text-align: right;
  box-sizing: border-box;
  padding-right: 10px;
  line-height: 32px;
i {
  color: red;
  position: relative;
  top: 2px;
  right: 4px;
}
}
.rulesetcont {
  padding-bottom: 15px;
.el-row {
  margin-bottom: 10px;
}
}
.v-modal {
  z-index: 1998;
}
.page_center {
.searchBox {
  padding: 20px 0;
  border: 1px solid #eee;
  margin: 20px 0;
}
.dialogTitle {
  text-align: right;
  box-sizing: border-box;
  padding-right: 2px;
  line-height: 32px;
i {
  color: red;
  position: relative;
  top:3px;
  right: 4px;
}
}
.c_pagination {
  text-align: right;
  padding: 20px;
}
.el-row {
  margin-bottom: 10px;
.item-btn {
  position: relative;
.item-close {
  position: absolute;
  right:-8px;
  top:-8px;
  font-size: 16px;
}
}
}
}
.empty {
  padding: 150px 0;
  text-align: center;
p {
  font-size: 16px;
  color: #999;
}
}
.selectLoading {
  font-size: 12px;
  color: #ccc;
  text-align: center;
  height: 14px;
  line-height: 14px;
}
.cover-cell{
  cursor: pointer;
  &:hover {
    color: #409eff;
  }
}