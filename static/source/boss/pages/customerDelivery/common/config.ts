import * as env from '@server/env'

let factoryTemplateUat = { // 工厂上传的excel模板__UAT
  createFactory: 'https://static.zkh360.com/file/2025-06-12/541c8ad64deb44202f256b517bbd6b87-1749721218475.xlsm', // 创建工厂
  updateFactory: '', // 修改工厂
  createContact: '', // 创建联系人
  updateFactoryBaseinfo: 'https://static.zkh360.com/file/2025-06-12/9cd1a0c4115ea6de83569d6f3c64d0d2-1749721280493.xlsx', // 批量修改工厂信息
  updateDeliveryPoints: 'https://static.zkh360.com/file/2025-06-12/24777da19368e65e912627f4418860dd-1749721377083.xlsx', // 批量修改交货点信息
  updateDeliverySupervisor: 'https://static.zkh360.com/file/2025-06-12/8c21be20db55331c5156bffaba8bf237-1749721508075.xlsx', // 批量修改交主管配置
  updateServiceCenterArea: 'https://static.zkh360.com/file/2025-06-12/1e6324f544a5bb3b866349f470f371c6-1749721584048.xlsx' // 批量修改区域服务中心经理
}
let factoryTemplatePro = { // 工厂上传的excel模板__PRO
  createFactory: 'https://static.zkh360.com/file/2025-06-12/476e5f1fe099781f895d2629e620f197-1749721620015.xlsm', // 创建工厂
  updateFactory: '', // 修改工厂
  createContact: 'https://static.zkh360.com/file/2025-06-12/017a319220e3449d5c16e6ef1090dc99-1749721661198.xlsx', // 创建联系人
  updateFactoryBaseinfo: 'https://static.zkh360.com/file/2025-06-12/9bb296671802a57b1e4f09c450c4eca0-1749721699001.xlsx', // 批量修改工厂信息
  updateDeliveryPoints: 'https://static.zkh360.com/file/2025-06-12/df74b6cc66d02c32947913a13f4aba13-1749721736286.xlsx', // 批量修改交货点信息
  updateDeliverySupervisor: 'https://static.zkh360.com/file/2025-06-12/de9c09c98167b956e78ba8d42c810c7f-1749721802987.xlsx', // 批量修改交主管配置
  updateServiceCenterArea: 'https://static.zkh360.com/file/2025-06-12/1e6324f544a5bb3b866349f470f371c6%20%281%29-1749721849608.xlsx' // 批量修改区域服务中心经理
}
const config = {
  local: {
    aggregationCode: 'factory',
    aggregationId: 1, // 工厂聚合id
    serviceCenterAreaAggregationId: 3, // 服务中心负责区域聚合id
    blackholeId: 17,
    factoryTemplate: factoryTemplateUat
  },
  dev: {
    aggregationCode: 'factory',
    aggregationId: 1, // 工厂聚合id
    serviceCenterAreaAggregationId: 3, // 服务中心负责区域聚合id
    blackholeId: 17,
    factoryTemplate: factoryTemplateUat
  },
  qa: {
    aggregationCode: 'factory',
    aggregationId: 1, // 工厂聚合id
    serviceCenterAreaAggregationId: 3, // 服务中心负责区域聚合id
    blackholeId: 17,
    factoryTemplate: factoryTemplateUat
  },
  prerelease: {
    aggregationCode: 'factory',
    aggregationId: 114, // 工厂聚合id
    serviceCenterAreaAggregationId: 117, // 服务中心负责区域聚合id
    blackholeId: 17,
    factoryTemplate: factoryTemplatePro
  },
  release: {
    aggregationCode: 'factory',
    aggregationId: 114, // 工厂聚合id
    serviceCenterAreaAggregationId: 117, // 服务中心负责区域聚合id
    blackholeId: 17,
    factoryTemplate: factoryTemplatePro
  }
}
const type = env.LOCAL ? 'local' : env.DEV ? 'dev' : env.QA ? 'qa' : env.PRERELEASE ? 'prerelease' : 'release'
export default config[type]