<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="fifter">
        <el-row :span="24" class="searchBox">
          <el-col :span="1" class="dialogTitle">省：</el-col>
          <el-col :span="4" >
            <el-select
              style="width: 100%;"
              v-model="search.province_code"
              filterable
              @change="provinceChangeSearch"
              placeholder="请选择">
              <el-option
                v-for="item in provinces"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1" class="dialogTitle">市：</el-col>
          <el-col :span="4" >
            <el-select
              style="width: 100%;"
              v-model="search.city_code"
              filterable
              @change="cityChangeSearch"
              placeholder="请选择">
              <el-option
                v-for="item in searchCities"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="1" class="dialogTitle">区：</el-col>
          <el-col :span="4" >
            <el-select
              style="width: 100%;"
              v-model="search.area_code"
              filterable
              @change="areaChangeSearch"
              placeholder="请选择">
              <el-option
                v-for="item in searchAreas"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5" style="padding-left: 10px" >
            <el-button @click="searchList" icon="el-icon-search" size="small" type="primary">查询</el-button>
            <el-button @click="searchReset" icon="el-icon-close" size="small" type="info">清空</el-button>
          </el-col>
          <el-col :span="4" style="text-align: right;">
            <el-button style="margin-right: 20px;"  icon="el-icon-plus" size="medium" @click="openRuleDialog" type="primary">新建</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="c_tables">
        <el-row :span="24" class="factoryListTop">
          <el-col :span="12"><div style=" font-size: 16px; font-weight: 600; line-height: 36px;">服务中心负责区域</div></el-col>
          <el-col :span="12" style="text-align: right;">
            <el-button icon="el-icon-download" v-if="isOKExportBatch" style="margin-right: 20px;" @click="exportData()" size="medium" type="text">批量导出</el-button>
            <el-button icon="el-icon-upload" v-if="isOKModifyBatch" style="margin-right: 20px;" @click="uploadFile()" size="medium" type="text">批量修改</el-button>
          </el-col>
        </el-row>
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column align="center" prop="id" width="100" label="编号"></el-table-column>
          <el-table-column align="center" prop="service_center_province" label="省"></el-table-column>
          <el-table-column align="center" prop="service_center_city" label="市"></el-table-column>
          <el-table-column align="center" prop="service_center_area" label="区"></el-table-column>
          <el-table-column align="center" prop="service_center_manager_name" label="所属服务中心经理"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button @click="modifyApp(scope.row, scope.$index)" type="text" size="small">编辑</el-button>
              <el-button @click="deleteApp(scope.row, scope.$index)" type="text" size="small">删除</el-button>
              <el-button v-if="scope.row.status" @click="stopApp(scope.row, scope.$index)" type="text" size="small">停用</el-button>
              <el-button v-if="!scope.row.status" @click="startApp(scope.row, scope.$index)" type="text" size="small">启用</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
      <!--添加弹出框-->
      <el-dialog
        width="580px"
        title="配置信息"
        class="dialogClass"
        :visible.sync="showAddDialog"
        :close-on-click-modal="false">
        <div class="ruleset">
          <div class="rulesetcont">
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>省：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.service_center_province"
                  filterable
                  @change="provinceChangeAppForm"
                  placeholder="请选择">
                  <el-option
                    v-for="item in provinces"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>市：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.service_center_city"
                  filterable
                  @change="cityChangeAppForm"
                  placeholder="请选择">
                  <el-option
                    v-for="item in appFormCities"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>区：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.service_center_area"
                  @change="areaChangeAppForm"
                  filterable
                  placeholder="请选择">
                  <el-option
                    v-for="item in appFormAreas"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle" style="font-size: 12px;"><i>*</i>所属服务中心经理：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.service_center_manager_domain_name"
                  filterable
                  remote
                  :remote-method="gotGetAccount1"
                  @change="managerChange"
                  placeholder="请选择(输入中文/英文名字搜索)">
                  <el-option
                    v-for="item in accountOptions1"
                    :key="item.username"
                    :label="item.nickname"
                    :value="item.username">
                    <span>{{ item.nickname }}</span>
                    <span>({{ item.username }})</span>
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <!--<el-row :span="24">-->
              <!--<el-col :span="6" class="dialogTitle" style="font-size: 12px;"><i>*</i>所属服务中心总经理：</el-col>-->
              <!--<el-col :span="16">-->
                <!--<el-select-->
                  <!--style="width: 100%;"-->
                  <!--v-model="appForm.GManager"-->
                  <!--filterable-->
                  <!--remote-->
                  <!--:remote-method="gotGetAccount2"-->
                  <!--:loading="loading.account"-->
                  <!--placeholder="请选择(输入中文/英文名字搜索)">-->
                  <!--<el-option-->
                    <!--v-for="item in accountOptions2"-->
                    <!--:key="item.username"-->
                    <!--:label="item.nickname"-->
                    <!--:value="item.username">-->
                    <!--<span>{{ item.nickname }}</span>-->
                    <!--<span>({{ item.username }})</span>-->
                  <!--</el-option>-->
                <!--</el-select>-->
              <!--</el-col>-->
            <!--</el-row>-->
          </div>
          <el-row :span="24">
            <el-col :span="24" style="text-align: right;">
              <el-button size="medium" @click="closeAddDialog" style="margin-left: 20px;">关闭</el-button>
              <el-button size="medium" :loading="loading.create" @click="storeAddDialog" type="primary">确认</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>
      <!--添加弹出框 end-->
      <!--上传-->
      <el-dialog
        width="580px"
        title="上传文件"
        class="dialogClass"
        :visible.sync="showUploadDialog"
        :close-on-click-modal="false">
        <div class="ruleset">
          <div class="uploadBox" style="text-align: left;">
            <el-upload
              style="width: 100%;"
              ref="upload"
              drag
              accept=".xlsx,.xls,.xlsm"
              :action="uploadUrl"
              :on-success="onUploadSuccess"
              :before-upload="beforeUpload"
              :on-error="onUploadError"
              name="excel"
              :file-list="fileList"
              :show-file-list="true"
              :auto-upload="false"
              :limit="limit"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处上传，或<em>点击上传</em></div>
            </el-upload>
          </div>
          <el-row :span="24" style="margin-bottom: 0; padding-top: 5px;">
            <el-col :span="12" style="text-align: left; padding-top: 10px; ">
              <i style="font-size: 12px; color: red; position: relative; top: 2px; margin-right:6px;">*</i>只能上传.xlsx,.xls,.xlsm文件
            </el-col>
            <el-col :span="12" style="text-align: right;">
              <el-button size="medium" type="text" style="margin-left: 20px;"><a :href="demoUrl" :download="demoName">下载导入模板</a></el-button>
              <el-button size="medium" @click="uploadSubmit()" type="primary">上传</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>
      <!--上传 end-->
    </div>
  </div>
</template>

<script>
import {
  getArea,
  getAccountsByName,
  getServiceCenterArea,
  createServiceCenterArea,
  updateServiceCenterArea,
  deleteServiceCenterArea,
  exportServiceCenterAreaList
} from '@/api/customerDelivery'
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import API from './common/config.ts'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        account: false,
        create: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      search: { // 搜索值
        province_code: '',
        province: '',
        city_code: '',
        city: '',
        area_code: '',
        area: ''
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        service_center_province: '',
        service_center_province_code: '',
        service_center_city: '',
        service_center_city_code: '',
        service_center_area: '',
        service_center_area_code: '',
        service_center_manager_name: '', // 所选经理
        service_center_manager_domain_name: '', // 所选经理域账号
        service_center_no: '', // 服务中心编号
        customer_no: '', // 客户编号
        status: true // 状态 true 启用 false 禁用
      },
      total: 0,
      tableData: [],
      accountOptions1: [], // 用户列表
      accountOptions2: [], // 用户列表
      provinces: [], // 省
      searchCities: [], // 搜索部分市
      appFormCities: [], // 添加，修改表单部分市
      searchAreas: [], // 搜索部分区
      appFormAreas: [], // 添加，修改表单部分区
      showUploadDialog: false,
      uploadUrl: '/api-customerDelivery/eventsource/uploadAndProcess/service_center_area/update-service_center_area-batch/1', // 上传url
      demoName: '',
      demoUrl: API.factoryTemplate.updateServiceCenterArea,
      fileList: [],
      limit: 1,
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetApps()
    this.goGetArea(1)
    this.gotGetAccount()
  },
  computed: {
    ...mapState(['userRole']),
    isOKExportBatch: function () {
      // 是否有权限批量导出
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-服务中心总经理') ||
        userRoleList.includes('CD-区域服务中心经理')
      return isok
    },
    isOKModifyBatch: function () {
      // 是否有权限批量修改
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-服务中心总经理') ||
        userRoleList.includes('CD-区域服务中心经理')
      return isok
    }
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.province) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  methods: {
    beforeUpload (file) {
      console.log('beforeUpload')
      console.log(file);
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false;
      }
      if (!this.$validateFileType(file)) return false
      this.fileName = file.name;
      this.loading.import = true
    },
    onUploadSuccess (response) {
      console.log('success')
      console.log(response)
      this.loading.import = false
      this.$message.success('上传成功！')
      this.showUploadDialog = false
      this.fileList = []
      this.goGetApps()
    },
    onUploadError (error) {
      console.log('error')
      console.log(error)
      this.loading.import = false
      this.$message.error((error && error.msg) || (error && error.message) || '上传失败')
    },
    gotGetAccount1 (name) {
      console.log('name1=' + name)
      this.gotGetAccount(name, 1)
    },
    gotGetAccount2 (name) {
      console.log('name2=' + name)
      this.gotGetAccount(name, 2)
    },
    gotGetAccount (name, state) {
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        page: 0,
        size: 200,
        name: name
      }
      getAccountsByName(data).then(res => {
        if (res) {
          if (state === 1) {
            This.accountOptions1 = res
          } else if (state === 2) {
            This.accountOptions2 = res
          } else {
            This.accountOptions1 = res
            This.accountOptions2 = res
          }
        }
      })
    },
    managerChange (res) {
      let accountOptions1 = this.accountOptions1
      accountOptions1.forEach((item) => {
        if (item.username === res) {
          this.appForm.service_center_manager_domain_name = item.username
          this.appForm.service_center_manager_name = item.nickname
        }
      })
    },
    provinceChangeSearch (res) {
      // console.log('切换省')
      this.searchCities = []
      this.searchAreas = []
      this.search.city = ''
      this.search.city_code = ''
      this.search.area = ''
      this.search.area_code = ''
      let provinces = this.provinces
      provinces.forEach((item) => {
        if (item.code === res) {
          this.search.province_code = item.code
          this.search.province = item.name
        }
      })
      this.goGetArea(2, res, 1)
    },
    cityChangeSearch (res) {
      // console.log('切换市')
      this.searchAreas = []
      this.search.area = ''
      let searchCities = this.searchCities
      searchCities.forEach((item) => {
        if (item.code === res) {
          this.search.city_code = item.code
          this.search.city = item.name
        }
      })
      this.goGetArea(3, res, 1)
    },
    areaChangeSearch (res) {
      // console.log('切换区')
      let searchAreas = this.searchAreas
      searchAreas.forEach((item) => {
        if (item.code === res) {
          this.search.area_code = item.code
          this.search.area = item.name
        }
      })
    },
    provinceChangeAppForm (res) {
      // console.log('切换省')
      this.appFormCities = []
      this.appFormAreas = []
      this.appForm.service_center_city = ''
      this.appForm.service_center_city_code = ''
      this.appForm.service_center_area = ''
      this.appForm.service_center_area_code = ''
      let provinces = this.provinces
      provinces.forEach((item) => {
        if (item.code === res) {
          this.appForm.service_center_province_code = item.code
          this.appForm.service_center_province = item.name
        }
      })
      this.goGetArea(2, res, 2)
    },
    cityChangeAppForm (res) {
      // console.log('切换市')
      this.appFormAreas = []
      this.appForm.service_center_area = ''
      this.appForm.service_center_area_code = ''
      let cities = this.appFormCities
      cities.forEach((item) => {
        if (item.code === res) {
          this.appForm.service_center_city_code = item.code
          this.appForm.service_center_city = item.name
        }
      })
      this.goGetArea(3, res, 2)
    },
    areaChangeAppForm (res) {
      let areas = this.appFormAreas
      areas.forEach((item) => {
        if (item.code === res) {
          this.appForm.service_center_area_code = item.code
          this.appForm.service_center_area = item.name
        }
      })
    },
    openRuleDialog () {
      // 打开添加数据窗口
      this.appForm = { // 初始化form应用的内容
        id: '',
        service_center_province: '',
        service_center_province_code: '',
        service_center_city: '',
        service_center_city_code: '',
        service_center_area: '',
        service_center_area_code: '',
        service_center_manager_name: '', // 所选经理
        service_center_manager_domain_name: '', // 所选经理域账号
        service_center_no: '', // 服务中心编号
        customer_no: '', // 客户编号
        status: true // 状态 true 启用 false 禁用
      }
      this.currAppInfo = null
      this.showAddDialog = true
    },
    closeAddDialog () {
      this.showAddDialog = false
    },
    storeAddDialog () {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp (row, index) {
      console.log('修改数据')
      this.appForm = JSON.parse(JSON.stringify(row))
      this.currAppInfo = row
      this.showAddDialog = true
    },
    deleteApp (row, index) {
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          aggregationId: row.id, // 数据id
          id: row.id // 数据id
        }
        deleteServiceCenterArea(data).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(res.message)
          }
        })
      }).catch(() => {
        console.log('取消删除')
      })
    },
    stopApp (row, index) {
      console.log('停用')
      this.appForm = row
      this.appForm.status = false
      this.gocreateApps(1)
    },
    startApp (row, index) {
      console.log('启用')
      this.appForm = row
      this.appForm.status = true
      this.gocreateApps(2)
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    goGetArea (level, parentCode, position) {
      /**
       * level: 1省/直辖市,2地级市,3区县,4镇/街道
       * parentCode: 父级地区编码
       * position: 1 搜索部分 2 添加、修改表单部分
       * **/
      let This = this
      let data = {
        level: level,
        parentCode: parentCode
      }
      getArea(data).then(res => {
        if (res.code === 200) {
          if (level === 1) {
            // 省
            This.provinces = res.data
          } else if (level === 2) {
            // 市
            if (position === 1) {
              This.searchCities = res.data
            } else {
              This.appFormCities = res.data
            }
          } else if (level === 3) {
            // 区
            if (position === 1) {
              This.searchAreas = res.data
            } else {
              This.appFormAreas = res.data
            }
          }
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    goGetApps () {
      // 获取数据列表
      let This = this
      let conditionList = []
      if (This.search.province) {
        let unit = {
          'compareData': This.search.province,
          'operateChar': '=',
          'searchField': 'service_center_province'
        }
        conditionList.push(unit)
      }
      if (This.search.city) {
        let unit = {
          'compareData': This.search.city,
          'operateChar': '=',
          'searchField': 'service_center_city'
        }
        conditionList.push(unit)
      }
      if (This.search.area) {
        let unit = {
          'compareData': This.search.area,
          'operateChar': '=',
          'searchField': 'service_center_area'
        }
        conditionList.push(unit)
      }
      let data = {
        pageNum: This.listQueryInfo.current,
        pageSize: This.listQueryInfo.pageSize,
        code: 'service_center_area_batch',
        aggregationCode: 'service_center_area',
        aggregationType: 'service_center_area',
        conditionList: conditionList
      }
      getServiceCenterArea(data).then(res => {
        if (res.code === 0) {
          let result = res.data.result
          if (result) {
            This.tableData = result
            This.total = res.data.total
          } else {
            This.tableData = []
          }
        } else {
          This.$message.error(res.message)
        }
        This.loading.table = false
        This.loading.page = false
      }).catch(() => {
        This.loading.table = false
        This.loading.page = false
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = {
        role: '',
        property: ''
      }
      if (!state) {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    gocreateApps (state) {
      // 新建、编辑数据
      let This = this
      let data = this.appForm
      // This.searchReset('create')
      if (!data.service_center_province || !data.service_center_city || !data.service_center_area || !data.service_center_manager_name) {
        this.$message.error('请将信息填写完整')
        return false
      }
      let appId = this.appForm.id // 修改数据id
      this.loading.create = true
      data.aggregationId = appId
      data.id = appId
      if (appId) {
        // 修改数据
        updateServiceCenterArea(data, appId).then(res => {
          if (res.code === 0) {
            if (state === 1) {
              This.$message.success('已停用')
            } else if (state === 2) {
              This.$message.success('已启用')
            } else {
              This.$message.success('修改成功')
            }
            // 修改成功更新数据列表
            This.goGetApps()
            this.showAddDialog = false
          } else {
            This.$message.error(res.message)
          }
          this.loading.create = false
        })
      } else {
        // 添加数据
        createServiceCenterArea(data).then(res => {
          if (res.code === 0) {
            This.$message.success('创建成功')
            // 新增成功创建数据列表
            this.tableData.push(res.data)
            this.showAddDialog = false
          } else {
            This.$message.error(res.message)
          }
          this.loading.create = false
        })
      }
    },
    gotGetRole (key) {
      console.log('获取值')
      console.log(key)
      this.loading.role = true
    },
    gotGetProperty (key) {
      console.log('获取值')
      console.log(key)
      this.loading.property = true
    },
    exportData () {
      let This = this
      This.loading.page = true
      let data = {
        provinceName: This.search.province,
        cityName: This.search.city,
        areaName: This.search.area
      }
      exportServiceCenterAreaList(data).then(res => {
        if (res) {
          This.$message.error(res.message)
        } else {
          This.$message.success('请在企业微信中查看')
        }
        This.loading.page = false
      })
    },
    uploadFile () {
      this.fileList = []
      this.showUploadDialog = true
    },
    uploadSubmit () {
      this.$refs.upload.submit()
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/static/css/common.scss'; /*引入公共样式*/
</style>
<style lang="scss" >
.el-upload {
  width: 100%;
}
.el-upload-dragger {
  width: 100%;
}
</style>
