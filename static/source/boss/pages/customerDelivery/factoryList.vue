<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="fifter">
        <el-row :span="24" class="searchBox">
          <el-col :span="3" class="dialogTitle">客户名称：</el-col>
          <el-col :span="9" >
            <el-select
              style="width: 100%;"
              class="customerName"
              v-model="search.customer_name"
              clearable
              filterable
              multiple
              remote
              :remote-method="goGetCustomers"
              placeholder="请选择(输入名称搜索)">
              <el-option
                v-for="item in customerOptions"
                :key="item.customerName"
                :label="item.customerName"
                :value="item.customerName">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3" class="dialogTitle">客户编码：</el-col>
          <el-col :span="9" >
            <el-input
              placeholder="请输入客户编码"
              v-model="search.customer_code"
              clearable
            />
          </el-col>
          <el-col :span="3" class="dialogTitle">工厂名称：</el-col>
          <el-col :span="9" >
            <el-select
              style="width: 100%;"
              class="customerName"
              v-model="search.factory_name"
              clearable
              filterable
              multiple
              remote
              :remote-method="goGetFactory"
              placeholder="请选择(输入名称搜索)">
              <el-option
                v-for="item in factoryOptions"
                :key="item.baseInfo.factory_name"
                :label="item.baseInfo.factory_name"
                :value="item.baseInfo.factory_name">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3" class="dialogTitle">服务中心经理：</el-col>
          <el-col :span="9" >
            <el-select
              style="width: 100%;"
              class="customerName"
              v-model="search.service_center_manager"
              clearable
              filterable
              multiple
              remote
              :remote-method="goGetAccounts"
              placeholder="请选择(输入名称搜索)">
              <el-option
                v-for="item in accountOptions"
                :key="item.nickname"
                :label="item.nickname"
                :value="item.nickname">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3" class="dialogTitle">交付主管：</el-col>
          <el-col :span="9" >
            <el-select
              style="width: 100%;"
              class="customerName"
              v-model="search.delivery_supervisor_name"
              clearable
              filterable
              multiple
              remote
              :remote-method="goGetAccounts"
              placeholder="请选择(输入名称搜索)">
              <el-option
                v-for="item in accountOptions"
                :key="item.nickname"
                :label="item.nickname"
                :value="item.nickname">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3" class="dialogTitle">客服：</el-col>
          <el-col :span="9" >
            <el-select
              style="width: 100%;"
              class="customerName"
              v-model="search.customer_service_name"
              clearable
              filterable
              multiple
              remote
              :remote-method="goGetAccounts"
              placeholder="请选择(输入名称搜索)">
              <el-option
                v-for="item in accountOptions"
                :key="item.nickname"
                :label="item.nickname"
                :value="item.nickname">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="3" class="dialogTitle">客服经理：</el-col>
          <el-col :span="9" >
            <el-select
              style="width: 100%;"
              class="customerName"
              v-model="search.customer_service_manager"
              clearable
              filterable
              multiple
              remote
              :remote-method="goGetAccounts"
              placeholder="请选择(输入名称搜索)">
              <el-option
                v-for="item in accountOptions"
                :key="item.nickname"
                :label="item.nickname"
                :value="item.nickname">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="9" :offset="3" style="text-align: left;">
            <el-button @click="searchList" icon="el-icon-search" size="small" type="primary">查询</el-button>
            <el-button @click="searchReset" icon="el-icon-close" size="small" type="info">清空</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="c_tables">
        <el-row :span="24" class="factoryListTop">
          <el-col :span="12"><div class="title">工厂列表</div></el-col>
          <el-col :span="12" style="text-align: right;">
            <el-popover
              placement="bottom"
              ref="popover"
              trigger="hover">
              <div class="groupHandle" style="text-align: left; padding-left: 10px;">
                <p @click="exportFile(1)">导出工厂信息</p>
                <p @click="exportFile(2)">导出交付要求</p>
                <p @click="exportFile(3)">导出交付主管配置</p>
                <p v-if="isOKExportContacts" @click="exportFile(4)">导出交付联系人</p>
                <p @click="exportFile(5)">导出工厂全部交付信息</p>
              </div>
              <el-button v-if="isOKExport" style="margin-right: 20px;" slot="reference" icon="el-icon-download" size="medium" type="text">批量导出</el-button>
            </el-popover>
            <el-button v-if="isOKCreate" style="margin-right: 20px;" icon="el-icon-circle-plus-outline" size="medium" @click="openRuleDialog" type="text">新建工厂</el-button>
            <el-popover
              placement="bottom"
              trigger="hover">
              <div class="groupHandle" style="text-align: left; padding-left: 10px;">
                <p v-if="isOKCreate" @click="uploadFile(1)">批量新建工厂</p>
                <!--<p class="zhihui" >批量修改工厂信息</p>-->
                <p @click="uploadFile(3)">批量新建交付联系人</p>
                <p v-if="isUploadBatch" @click="uploadFile(4)">批量修改工厂信息</p>
                <p v-if="isUploadBatch" @click="uploadFile(5)">批量修改交货点信息</p>
                <p v-if="isModifyBatch" @click="uploadFile(6)">批量修改交付主管配置</p>
              </div>
              <el-button v-if="isUploadBatch" slot="reference" style="margin-right: 20px;" icon="el-icon-upload" size="medium" type="text">批量上传</el-button>
            </el-popover>
            <el-button v-if="isUploadBatch" style="margin-right: 20px;"  icon="el-icon-tickets" size="medium" @click="toUploadList" type="text">上传历史</el-button>
          </el-col>
        </el-row>
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column align="center" prop="id" label="id"></el-table-column>
          <el-table-column align="center" prop="role" label="工厂名称">
            <template slot-scope="scope">
              <div class="ahref" @click="toDetail(scope.row.id)">{{scope.row.baseInfo.factory_name}}</div>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="baseInfo.customer_info.customer_code" label="客户编码"></el-table-column>
          <el-table-column align="center" width="200" prop="baseInfo.customer_info.customer_name" label="客户名称"></el-table-column>
          <el-table-column align="center" prop="baseInfo.factory_type" label="工厂类型"></el-table-column>
          <el-table-column align="center" width="150" prop="factory_delivery_requirement.delivery_points[0].point_basic_info.point_name" label="交货点名称"></el-table-column>
          <el-table-column align="center" prop="" label="交付主管">
            <template slot-scope="scope">
              <p v-for="(item, index) in scope.row.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts" :item="item" :key="index">
                {{item.delivery_supervisor_name}}
              </p>
              <!--<p>{{scope.row.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[0].delivery_supervisor_name}}</p>-->
              <!--<p v-if="scope.row.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[1].delivery_supervisor_name">{{scope.row.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[1].delivery_supervisor_name}}</p>-->
            </template>
          </el-table-column>
          <el-table-column align="center" prop="baseInfo.factory_province" label="省"></el-table-column>
          <el-table-column align="center" prop="baseInfo.factory_city" label="市"></el-table-column>
          <el-table-column align="center" prop="baseInfo.factory_area" label="区"></el-table-column>
          <el-table-column align="center" width="120" label="操作">
            <template slot-scope="scope">
              <el-button @click="toDetail(scope.row.id)" type="text" size="small">详情</el-button>
              <el-button style="color: #dd0000;" v-if="!isJustReadPermission" @click="deleteApp(scope.row, scope.$index)" type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
      <!--新建工厂弹出框-->
      <createFactoryForm ref="createFactoryForm" ></createFactoryForm>
      <!--新建工厂弹出框 end-->
      <!--上传-->
      <el-dialog
        width="580px"
        :title="dialogTitle"
        class="dialogClass"
        :visible.sync="showUploadDialog"
        :close-on-click-modal="false">
        <div class="ruleset">
          <div class="uploadBox" style="text-align: left;">
            <el-upload
              style="width: 100%;"
              ref="upload"
              drag
              accept=".xlsx,.xls,.xlsm"
              :action="uploadUrl"
              :on-success="onUploadSuccess"
              :before-upload="beforeUpload"
              :on-error="onUploadError"
              name="excel"
              :file-list="fileList"
              :show-file-list="true"
              :auto-upload="false"
              :limit="limit"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处上传，或<em>点击上传</em></div>
            </el-upload>
          </div>
          <el-row :span="24" style="margin-bottom: 0; padding-top: 5px;">
            <el-col :span="12" style="text-align: left; padding-top: 10px; ">
              <i style="font-size: 12px; color: red; position: relative; top: 2px; margin-right:6px;">*</i>只能上传.xlsx,.xls,.xlsm文件
            </el-col>
            <el-col :span="12" style="text-align: right;">
              <el-button size="medium" type="text" style="margin-left: 20px;"><a :href="demoUrl" >下载导入模板</a></el-button>
              <el-button size="medium" @click="uploadSubmit()" type="primary">上传</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>
      <!--上传 end-->
    </div>
  </div>
</template>

<script>
import {
  exportFactoryBaseInfo,
  exportFactoryDeliveryPoints,
  exportFactoryDeliverySupervisor,
  exportFactoryDeliveryKeyContacts,
  exportFactoryInfo,
  getFactory,
  deleteFactory,
  getCustomers,
  getAccountsByName
} from '@/api/customerDelivery'
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import createFactoryForm from './components/createFactoryForm'
import API from './common/config.ts'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        role: false,
        property: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      search: { // 搜索值
        customer_name: [],
        customer_code: '',
        factory_name: [],
        factory_code: '',
        service_center_manager: [],
        delivery_supervisor_name: [],
        customer_service_name: [],
        customer_service_manager: []
      },
      total: 0,
      tableData: [],
      customerOptions: [], // 客户列表
      factoryOptions: [], // 工厂列表
      accountOptions: [], // 账户列表
      showUploadDialog: false,
      uploadUrl: '', // 上传url
      uploadUrl1: '/api-customerDelivery/eventsource/uploadAndProcess/factory/create-factory-batch/2', // 批量新建工厂
      uploadUrl2: '/api-customerDelivery/eventsource/uploadAndProcess/factory/update-factory-batch/2', // 批量修改工厂
      uploadUrl3: '/api-customerDelivery/eventsource/uploadAndProcess/factory/create-factory-terminal_delivery_key_contacts-batch/1', // 批量新建交付联系人
      uploadUrl4: '/api-customerDelivery/eventsource/uploadAndProcess/factory/update-factory-baseInfo-batch/1', // 批量修改工厂信息
      uploadUrl5: '/api-customerDelivery/eventsource/uploadAndProcess/factory/update-factory-factory_delivery_requirement-delivery_points-batch/2', // 批量修改交货点信息
      uploadUrl6: '/api-customerDelivery/eventsource/uploadAndProcess/factory/update-factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-delivery_supervisor_contacts-batch/1', // 批量修改交主管配置
      demoUrl: '',
      demoUrl1: API.factoryTemplate.createFactory,
      demoUrl2: API.factoryTemplate.updateFactory,
      demoUrl3: API.factoryTemplate.createContact,
      demoUrl4: API.factoryTemplate.updateFactoryBaseinfo,
      demoUrl5: API.factoryTemplate.updateDeliveryPoints,
      demoUrl6: API.factoryTemplate.updateDeliverySupervisor,
      currAppInfo: null, // 当前app信息,修改时候传递给接口使用
      fileList: [],
      dialogTitle: '',
      limit: 1
    }
  },
  components: {
    Pagination,
    createFactoryForm
  },
  created () {
    this.goGetApps()
    console.log('当前用户角色')
    console.log(this.userRole)
  },
  computed: {
    ...mapState(['userRole']),
    isOKExport: function () {
      // 是否有权限导出工厂
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-服务中心总经理') ||
        userRoleList.includes('CD-区域服务中心经理') ||
        userRoleList.includes('data-客服') ||
        userRoleList.includes('data-客服总监') ||
        userRoleList.includes('boss-客户服务中心经理')
      return isok
    },
    isOKCreate: function () {
      // 是否有权限创建工厂、批量创建工厂
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-服务中心总经理') ||
        userRoleList.includes('CD-区域服务中心经理') ||
        userRoleList.includes('data-客服') ||
        userRoleList.includes('data-客服总监') ||
        userRoleList.includes('boss-客户服务中心经理')
      return isok
    },
    isOKExportContacts: function () {
      // 是否有权限导出联系人
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-服务中心总经理')
      return isok
    },
    isModifyBatch: function () {
      // 是否有权限批量修改
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-服务中心总经理') ||
        userRoleList.includes('CD-区域服务中心经理') ||
        userRoleList.includes('data-客服') ||
        userRoleList.includes('data-客服总监') ||
        userRoleList.includes('boss-客户服务中心经理')
      return isok
    },
    isJustReadPermission: function () {
      // 是否只有查看工厂列表和详情的权限，没有操作权限(没有其他角色且只有“查看全部工厂权限”角色时为true)
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-查看全部工厂权限')
      return isok
    },
    isUploadBatch: function () {
      // 是否有权限批量上传、上传历史
      let userRoleList = this.userRole
      let isok = userRoleList.includes('boss-服务中心总经理') ||
        userRoleList.includes('CD-区域服务中心经理') ||
        userRoleList.includes('data-客服') ||
        userRoleList.includes('data-客服总监') ||
        userRoleList.includes('boss-客户服务中心经理') ||
        userRoleList.includes('boss-交付主管')
      return isok
    }
  },
  methods: {
    beforeUpload (file) {
      console.log('beforeUpload')
      console.log(file);
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false;
      }
      if (!this.$validateFileType(file)) return false
      this.fileName = file.name;
      this.loading.import = true
    },
    onUploadSuccess (response) {
      console.log('success')
      console.log(response)
      this.loading.import = false
      this.$message.success(response.message || '上传成功！')
      this.showUploadDialog = false
      this.fileList = []
    },
    onUploadError (error) {
      console.log('error')
      console.log(error)
      this.loading.import = false
      this.$message.error((error && error.msg) || (error && error.message) || '上传失败')
    },
    openRuleDialog () {
      // 打开添加数据窗口
      this.currAppInfo = null
      this.$refs.createFactoryForm.openRuleDialog()
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    deleteApp (row, index) {
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          aggregationId: row.id, // 数据id
          id: row.id // 数据id
        }
        deleteFactory(data).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(res.message)
          }
        })
      }).catch(() => {
        console.log('取消删除')
      })
    },
    goGetApps () {
      // 【1】获取数据列表
      let This = this
      let conditionList = []
      if (This.search.customer_name.length > 0) {
        let unit = {
          'compareData': This.search.customer_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_name'
        }
        conditionList.push(unit)
      }
      if (This.search.customer_code) {
        let unit = {
          'compareData': This.search.customer_code.trim(),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_code'
        }
        conditionList.push(unit)
      }
      if (This.search.factory_name.length > 0) {
        let unit = {
          'compareData': This.search.factory_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.factory_name'
        }
        conditionList.push(unit)
      }
      if (This.search.factory_code) {
        let unit = {
          'compareData': This.search.factory_code.trim(),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.factory_code'
        }
        conditionList.push(unit)
      }
      if (This.search.service_center_manager.length > 0) {
        let unit = {
          'compareData': This.search.service_center_manager.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'factory_delivery_requirement.delivery_points.delivery_configuration_resource_information.service_center_manager'
        }
        conditionList.push(unit)
      }
      if (This.search.delivery_supervisor_name.length > 0) {
        let unit = {
          'compareData': This.search.delivery_supervisor_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'factory_delivery_requirement.delivery_points.delivery_configuration_resource_information.delivery_supervisor_contacts.delivery_supervisor_name'
        }
        conditionList.push(unit)
      }
      if (This.search.customer_service_name.length > 0) {
        let unit = {
          'compareData': This.search.customer_service_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_service_name'
        }
        conditionList.push(unit)
      }
      if (This.search.customer_service_manager.length > 0) {
        let unit = {
          'compareData': This.search.customer_service_manager.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_service_manager'
        }
        conditionList.push(unit)
      }
      let skipAuthority = false
      if (this.isJustReadPermission) {
        // 只有阅读权限
        skipAuthority = true
      }
      let data = {
        pageNum: This.listQueryInfo.current,
        pageSize: This.listQueryInfo.pageSize,
        code: 'factory_batch',
        aggregationCode: 'factory',
        aggregationType: 'factory',
        conditionList: conditionList,
        skipAuthority
      }
      getFactory(data).then(res => {
        if (res.code === 0) {
          // This.tableData = res.data.result
          let result = res.data.result
          if (result) {
            /**
            let dataList = []
            result.forEach((item) => {
              let unit = {
                baseInfo: null,
                factory_delivery_requirement: null
              }
              unit.baseInfo = JSON.parse(item.baseInfo)
              unit.factory_delivery_requirement = JSON.parse(item.factory_delivery_requirement)
              unit.id = item.id
              dataList.push(unit)
            })
            console.log('列表')
            console.log(dataList)
            This.tableData = dataList**/
            This.tableData = result
            This.total = res.data.total
          } else {
            This.tableData = []
          }
          This.loading.table = false
          This.loading.page = false
        } else {
          This.$message.error(res.message)
          This.loading.table = false
          This.loading.page = false
        }
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = { // 搜索值
        customer_name: [],
        customer_code: '',
        factory_name: [],
        service_center_manager: [],
        delivery_supervisor_name: [],
        customer_service_name: [],
        customer_service_manager: []
      }
      if (!state) {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    gotGetRole (key) {
      this.loading.role = true
    },
    gotGetProperty (key) {
      this.loading.property = true
    },
    toDetail (id) {
      this.$router.push(`./factoryList/detail/${id}`)
    },
    goPushData (data) {
      this.tableData.unshift(data)
    },
    toUploadList () {
      this.$router.push('./uploadList')
    },
    goGetCustomers (name) {
      // 获取用户客户中心用户列表
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        customerName: name,
        rowCount: 50,
        pageNo: 1
      }
      getCustomers(data).then(res => {
        if (res.code === '0000') {
          This.customerOptions = res.result.rows
        } else {
          This.$message.error(res.message)
        }
      })
    },
    goGetFactory (name) {
      // 获取搜索框里的工厂列表
      let This = this
      let conditionList = []
      if (name) {
        let unit = {
          'compareData': name.trim(),
          'operateChar': 'nestedLike',
          'searchField': 'baseInfo.factory_name'
        }
        conditionList.push(unit)
      }
      let data = {
        pageNum: 1,
        pageSize: 50,
        code: 'factory_batch',
        aggregationCode: 'factory',
        aggregationType: 'factory',
        conditionList: conditionList
      }
      getFactory(data).then(res => {
        if (res.code === 0) {
          let result = res.data.result
          if (result) {
            This.factoryOptions = result
          }
        }
      })
    },
    goGetAccounts (name) {
      // 获取搜索框里的账户列表
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        page: 0,
        size: 50,
        name: name
      }
      getAccountsByName(data).then(res => {
        if (res) {
          This.accountOptions = res
        }
      })
    },
    uploadFile (type) {
      if (type === 1) {
        // console.log('新建工厂')
        this.uploadUrl = this.uploadUrl1
        this.demoUrl = this.demoUrl1
        this.dialogTitle = '批量新建工厂'
      } else if (type === 2) {
        // console.log('修改工厂')
        this.uploadUrl = this.uploadUrl2
        this.demoUrl = this.demoUrl2
        this.dialogTitle = '批量修改工厂信息'
      } else if (type === 3) {
        // console.log('新建联系人')
        this.uploadUrl = this.uploadUrl3
        this.demoUrl = this.demoUrl3
        this.dialogTitle = '批量新建交付联系人'
      } else if (type === 4) {
        // console.log('批量修改工厂信息')
        this.uploadUrl = this.uploadUrl4
        this.demoUrl = this.demoUrl4
        this.dialogTitle = '批量修改工厂信息'
      } else if (type === 5) {
        // console.log('批量修改交货点信息')
        this.uploadUrl = this.uploadUrl5
        this.demoUrl = this.demoUrl5
        this.dialogTitle = '批量修改交货点信息'
      } else if (type === 6) {
        // console.log('批量修改交付主管配置')
        this.uploadUrl = this.uploadUrl6
        this.demoUrl = this.demoUrl6
        this.dialogTitle = '批量修改交付主管配置'
      }
      this.fileList = []
      this.showUploadDialog = true
    },
    uploadSubmit () {
      this.$refs.upload.submit()
    },
    exportFile (type) {
      let This = this
      This.loading.page = true
      This.$refs.popover.doClose()
      let conditionList = []
      if (This.search.customer_name.length > 0) {
        let unit = {
          'compareData': This.search.customer_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_name'
        }
        conditionList.push(unit)
      }
      if (This.search.customer_code) {
        let unit = {
          'compareData': This.search.customer_code.trim(),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_code'
        }
        conditionList.push(unit)
      }
      if (This.search.factory_name.length > 0) {
        let unit = {
          'compareData': This.search.factory_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.factory_name'
        }
        conditionList.push(unit)
      }
      if (This.search.factory_code) {
        let unit = {
          'compareData': This.search.factory_code.trim(),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.factory_code'
        }
        conditionList.push(unit)
      }
      if (This.search.service_center_manager.length > 0) {
        let unit = {
          'compareData': This.search.service_center_manager.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'factory_delivery_requirement.delivery_points.delivery_configuration_resource_information.service_center_manager'
        }
        conditionList.push(unit)
      }
      if (This.search.delivery_supervisor_name.length > 0) {
        let unit = {
          'compareData': This.search.delivery_supervisor_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'factory_delivery_requirement.delivery_points.delivery_configuration_resource_information.delivery_supervisor_contacts.delivery_supervisor_name'
        }
        conditionList.push(unit)
      }
      if (This.search.customer_service_name.length > 0) {
        let unit = {
          'compareData': This.search.customer_service_name.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_service_name'
        }
        conditionList.push(unit)
      }
      if (This.search.customer_service_manager.length > 0) {
        let unit = {
          'compareData': This.search.customer_service_manager.join(','),
          'operateChar': 'nestedIn',
          'searchField': 'baseInfo.customer_info.customer_service_manager'
        }
        conditionList.push(unit)
      }
      let data = {
        conditionList: conditionList
      }
      if (type === 1) {
        // 导出工厂信息
        exportFactoryBaseInfo(data).then(res => {
          if (res) {
            This.$message.error(res.message)
          } else {
            This.$message.success('请在企业微信中查看')
          }
          This.loading.page = false
        })
      } else if (type === 2) {
        // 导出交付要求
        exportFactoryDeliveryPoints(data).then(res => {
          if (res) {
            This.$message.error(res.message)
          } else {
            This.$message.success('请在企业微信中查看')
          }
          This.loading.page = false
        })
      } else if (type === 3) {
        // 导出交付主管配置
        exportFactoryDeliverySupervisor(data).then(res => {
          if (res) {
            This.$message.error(res.message)
          } else {
            This.$message.success('请在企业微信中查看')
          }
          This.loading.page = false
        })
      } else if (type === 4) {
        // 导出交付联系人
        exportFactoryDeliveryKeyContacts(data).then(res => {
          if (res) {
            This.$message.error(res.message)
          } else {
            This.$message.success('请在企业微信中查看')
          }
          This.loading.page = false
        })
      } else if (type === 5) {
        // 导出工厂全量信息
        exportFactoryInfo(data).then(res => {
          if (res) {
            This.$message.error(res.message)
          } else {
            This.$message.success('请在企业微信中查看')
          }
          This.loading.page = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../style/colors.scss";
@import './common/static/css/common.scss';

.searchBox {
  padding-left: 10px !important;
  padding-right: 50px !important;
  margin-top: 0 !important;
  .el-col {
    margin-bottom: 10px;
  }
}
.factoryListTop {
  .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 36px;
  }
}
.groupHandle {
  text-align: center;
  padding:10px 0;
  p {
    padding:5px 0;
    font-size: 12px;
    cursor: pointer;
    &:hover {
      color: $color-blue-200;
    }
  }
  .zhihui {
    cursor: not-allowed;
    color: #ccc;
    &:hover {
      color: #ccc;
    }
  }
}
.rulesetcont {
  border-top: 1px solid #eee;
  .ztitle {
    font-size: 14px;
    font-weight: 600;
    padding: 10px 0;
  }
  .dialogTitle {
    text-align: left;
  }
}
.ahref {
  cursor: pointer;
  color: $color-blue-200;
}
</style>
<style lang="scss">
@import "../../style/colors.scss";
.dialogClass {
  .el-dialog__body {
    padding-top: 5px !important;
  }
  .el-dialog__title {
    font-size: 16px;
    text-align: center;
  }
  .tab {
    cursor: pointer;
    color: $color-blue-200;
  }
}
.el-upload {
  width: 100%;
}
.el-upload-dragger {
  width: 100%;
}
.customerName {
  .el-select__tags {
    white-space: nowrap;
    overflow-x: auto;
    >span {
      max-width:80% !important;
      display: inline-block !important;
      overflow: hidden !important;
      overflow-x: auto !important;
      &::-webkit-scrollbar {
        height:6px;
      }
      /* 滚动槽 */
      &::-webkit-scrollbar-track {
        border-radius:10px;
      }
      /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        border-radius:10px;
        background:rgba(0,0,0,0.1);
      }
    }
  }
}
</style>
