<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="factoryDetail">
        <div class="factoryName" :data-userRole="userRoleCode"><i class="el-icon-office-building"></i>{{factoryDetail && factoryDetail.baseInfo.factory_name}}</div>
        <div class="factoryTab">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="工厂详情" name="1"></el-tab-pane>
            <el-tab-pane label="交付要求" name="2"></el-tab-pane>
            <el-tab-pane label="交付联系人" name="3"></el-tab-pane>
          </el-tabs>
          <div class="edit" v-if="isEdit">
            <el-button v-if="activeName === '3'" icon="el-icon-plus" size="mini" type="primary" @click="createConcats">新增</el-button>
            <el-button v-else-if="(activeName === '1' && factoryinfoIsEdit) || (activeName === '2' && deliveryRequiredIsEdit)" icon="el-icon-check" size="mini" type="primary" @click="factoryStore">保存</el-button>
            <el-button v-else icon="el-icon-edit" size="mini" type="primary" @click="factoryEdit">编辑</el-button>
          </div>
        </div>
        <div class="factoryCont">
          <div v-show="activeName === '1'">
            <factoryInfo v-if="userRoleCode && factoryDetail" ref="factoryInfo" :factoryDetail="factoryDetail" :userRoleCode="userRoleCode" :rolePowers="rolePowers" :pageIsEdit="factoryinfoIsEdit" :factoryId="factoryId"></factoryInfo>
          </div>
          <div v-show="activeName === '2'">
            <deliveryRequired v-if="userRoleCode && factoryDetail" ref="deliveryRequired" :factoryDetail="factoryDetail" :userRoleCode="userRoleCode" :rolePowers="rolePowers" :pageIsEdit="deliveryRequiredIsEdit" :factoryId="factoryId"></deliveryRequired>
          </div>
          <div v-show="activeName === '3'">
            <deliveryContacts v-if="userRoleCode && optionsList && factoryDetail" ref="deliveryContacts" :factoryDetail="factoryDetail" :userRoleCode="userRoleCode" :rolePowers="rolePowers" :optionsList="optionsList" :factoryId="factoryId"></deliveryContacts>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getFactoryDetail,
  getPropertyEnum,
  getRolePowers,
  getAccountsManagers
} from '@/api/customerDelivery'
import { mapState } from 'vuex'
import API from './common/config.ts'
import factoryInfo from './components/factoryInfo'
import deliveryRequired from './components/deliveryRequired'
import deliveryContacts from './components/deliveryContacts'
export default {
  data () {
    return {
      loading: {
        page: true
      },
      activeName: '1',
      factoryId: this.$route.params.factoryId, // 工厂id
      pageParams: this.$route.params, // {id:'1',type:'1'} id:数据id type 1 详情 2 编辑
      factoryinfoIsEdit: false, // 工厂基础信息在编辑中
      deliveryRequiredIsEdit: false, // 交付要求在编辑中
      optionsList: null, // 枚举列表
      userRoleCode: '', // 当前用户角色
      factoryDetail: null, // 工厂详情
      user: window.CUR_DATA.user && window.CUR_DATA.user.name,
      rolePowers: [] // 当前用户所属角色的权限（字段）列表
    }
  },
  components: {
    factoryInfo,
    deliveryRequired,
    deliveryContacts
  },
  created () {
    this.goGetPropertyEnum()
    this.goGetFactoryDetail()
    // console.log('当前用户角色')
    // console.log(this.userRole)
    // console.log(this.isServiceCenterGeneralMamager)
  },
  computed: {
    ...mapState(['userRole']),
    isServiceCenterGeneralMamager () {
      // 判断当前用户是不是服务中心总经理
      let userRole = this.userRole.toString()
      return !!~userRole.indexOf('boss-服务中心总经理')
    },
    // isJustReadPermission: function () {
    //   // 是否只有查看工厂列表和详情的权限，没有操作权限(没有其他角色且只有“查看全部工厂权限”角色时为true)
    //   let userRoleList = this.userRole
    //   let isok = !userRoleList.includes('boss-服务中心总经理') &&
    //     !userRoleList.includes('CD-区域服务中心经理') &&
    //     !userRoleList.includes('data-客服') &&
    //     !userRoleList.includes('data-客服总监') &&
    //     !userRoleList.includes('boss-客户服务中心经理') &&
    //     userRoleList.includes('boss-查看全部工厂权限')
    //   return isok
    // },
    isEdit () {
      let userRoleList = this.userRole
      let isok = ''
      if (userRoleList.includes('boss-查看全部工厂权限')) {
        if (userRoleList.includes('boss-服务中心总经理')) {
          isok = true
        } else {
          isok = false
        }
      } else {
        isok = userRoleList.includes('boss-服务中心总经理') ||
        userRoleList.includes('CD-区域服务中心经理') ||
        userRoleList.includes('data-客服') ||
        userRoleList.includes('data-客服总监') ||
        userRoleList.includes('boss-客户服务中心经理') ||
        userRoleList.includes('boss-交付主管')
      }
      return isok
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log('切换tab')
      console.log(tab, event)
    },
    factoryEdit () {
      // console.log('activeName=' + this.activeName)
      if (this.activeName === '1') {
        this.factoryinfoIsEdit = true // 工厂基础信息在编辑中
        this.$refs.factoryInfo.edit()
      } else if (this.activeName === '2') {
        this.deliveryRequiredIsEdit = true // 交付要求在编辑中
        this.$refs.deliveryRequired.edit()
      }
    },
    factoryStore () {
      if (this.activeName === '1') {
        this.$refs.factoryInfo.store()
      } else if (this.activeName === '2') {
        this.$refs.deliveryRequired.store()
      }
    },
    modifyEditState (type) {
      if (type === '1') {
        this.factoryinfoIsEdit = false
      } else if (type === '2') {
        this.deliveryRequiredIsEdit = false
      }
    },
    createConcats () {
      this.$refs.deliveryContacts.openRuleDialog()
    },
    goGetRolePowers () {
      let This = this
      let role = this.userRoleCode
      let data = {
        pageNum: 1,
        pageSize: 100,
        code: 'authority_manage_batch',
        conditionList: [
          {
            'compareData': API.blackholeId,
            'operateChar': '=',
            'searchField': 'blackhole_id'
          },
          {
            'compareData': role,
            'operateChar': '=',
            'searchField': 'role'
          }
        ]
      }
      getRolePowers(data).then(res => {
        if (res.code === 0) {
          // This.tableData = res.data.result
          let result = res.data.result
          if (result) {
            This.rolePowers = result
          }
          This.loading.page = false
        } else {
          This.$message.error(res.message)
          This.loading.page = false
        }
      })
    },
    getFactoryInfo (res) {
      this.factoryDetail.baseInfo = res
    },
    goGetPropertyEnum () {
      // 获取所有枚举值列表
      let This = this
      let data = {
        aggregationCode: API.aggregationCode, // 聚合code 要区分正式测试的
        blackholeId: API.blackholeId // 所属黑洞id 要区分正式测试的
      }
      getPropertyEnum(data).then(res => {
        if (res) {
          This.optionsList = res.enumMap
        }
      })
    },
    goGetFactoryDetail () {
      // 【1】获取数据详情
      let This = this
      let factoryId = this.factoryId
      getFactoryDetail(factoryId).then(res => {
        if (res) {
          let deliveryPoints = res.factory_delivery_requirement.delivery_points
          deliveryPoints.forEach((item, index) => {
            if (!deliveryPoints[index].point_basic_info) {
              deliveryPoints[index].point_basic_info = {
                point_name: ''
              }
            }
          })
          res.factory_delivery_requirement.delivery_points = deliveryPoints
          This.factoryDetail = res
          if (This.isServiceCenterGeneralMamager) {
            // 服务中心总经理
            This.userRoleCode = 'service_center_general_manager'
            This.goGetRolePowers()
          } else if (res.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_domain_account === This.user) {
            // 服务中心经理
            This.userRoleCode = 'service_center_manager'
            This.goGetRolePowers()
          } else {
            // 交付主管
            let deliveries = res.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts
            deliveries.forEach((item) => {
              if (item.delivery_supervisor_domain_account === This.user) {
                This.userRoleCode = 'delivery_manager'
                This.goGetRolePowers()
              }
            })
            if (!This.userRoleCode) {
              // 区域服务中心经理（“服务中心经理”请求安全中心接口，获取当前服务中心经理的所有上级账号，匹配当前用户，（有）匹配到证明当前登录用户是区域服务中心经理，（没有）没有匹配到则是客服）
              if (res.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_domain_account) {
                let data = {
                  username: res.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_domain_account
                }
                console.log('开始请求接口')
                console.log(data)
                getAccountsManagers(data).then((res) => {
                  if (res) {
                    res.forEach((item) => {
                      if (item.username === This.user) {
                        This.userRoleCode = 'service_center_area_manager'
                        This.goGetRolePowers()
                      }
                    })
                    if (!This.userRoleCode) {
                      This.userRoleCode = 'cs'
                      This.goGetRolePowers()
                    }
                  } else {
                    This.userRoleCode = 'cs'
                    This.goGetRolePowers()
                  }
                })
              } else {
                This.userRoleCode = 'cs'
                This.goGetRolePowers()
              }
            } else {
              This.userRoleCode = 'cs'
              This.goGetRolePowers()
            }
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../style/colors.scss";
@import './common/static/css/common.scss';

.searchBox {
  padding-left: 20px !important;
  padding-right: 20px !important;
  margin-top: 0 !important;
  .el-col {
    margin-bottom: 10px;
  }
}
.factoryListTop {
  .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 36px;
  }
}
.groupHandle {
  text-align: center;
  padding:10px 0;
  min-height: 100px;
  p {
    padding:5px 0;
    font-size: 12px;
    cursor: pointer;
    &:hover {
      color: $color-blue-200;
    }
  }
}
.rulesetcont {
  border-top: 1px solid #eee;
  .ztitle {
    font-size: 14px;
    font-weight: 600;
    padding: 10px 0;
  }
  .dialogTitle {
    text-align: left;
  }
}
.ahref {
  cursor: pointer;
  color: $color-blue-200;
}
.factoryName {
  font-size: 16px;
  font-weight: 600;
  padding: 10px;
  border-bottom: 2px solid #E4E7ED;
  i {
    font-size: 20px;
    width: 20px;
    height: 20px;
    font-weight: 600;
    margin-right:   4px;
    display: inline-block;
  }
}
.factoryTab {
  position: relative;
  .edit {
    position: absolute;
    right: 10px;
    top: 5px;
  }
}
</style>
<style lang="scss">
@import "../../style/colors.scss";
.createfactoryDialog {
  .el-dialog__body {
    padding-top: 5px !important;
  }
  .el-dialog__title {
    font-size: 16px;
    text-align: center;
  }
  .tab {
    cursor: pointer;
    color: $color-blue-200;
  }
}
.factoryCont {
  padding: 0 20px;
  .readonly {
    input {
      box-sizing: border-box;
      border: none;
    }
  }
}
.factoryFormModel {
  .title {
    font-size: 14px;
    font-weight: bold;
    padding: 20px 10px;
  }
  .form {
    border-left: 1px solid #efefef;
    border-top: 1px solid #efefef;
    .el-row {
      margin-bottom: 0;
      .el-col {
        height: 40px;
        line-height: 40px;
        font-size: 14px;
        border-bottom: 1px solid #efefef;
        border-right: 1px solid #efefef;
        padding:0 15px;
      }
      .el-col:nth-of-type(odd) {
        background: #f7f7f7;
        text-align: center;
      }
    }
  }
}
</style>
