<template>
  <div>
    <!--新建工厂弹出框-->
    <el-dialog
      width="580px"
      title="新建工厂交付要求"
      class="dialogClass createfactoryDialog"
      :visible.sync="showAddDialog"
      :close-on-click-modal="false">
      <div class="ruleset">
        <div class="rulesetcont" v-if="optionsList">
          <!--基本信息-->
          <div class="model">
            <div class="ztitle" :offset="1">工厂基本信息</div>
            <el-row :span="24">
              <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>所属客户：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.baseInfo.customer_info.customer_name"
                  filterable
                  remote
                  @change="related_customer_change"
                  :remote-method="goGetCustomers"
                  placeholder="请选择(输入名字搜索)">
                  <el-option
                    v-for="item in customerOptions"
                    :key="item.customerNumber"
                    :label="item.customerName"
                    :value="item.customerNumber">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>工厂名称：</el-col>
              <el-col :span="16">
                <el-input
                  v-model="appForm.baseInfo.factory_name"
                  @input="factoryNameChange"
                  placeholder="请输入"
                />
              </el-col>
            </el-row>
            <!--<el-row :span="24">-->
              <!--<el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>工厂编号：</el-col>-->
              <!--<el-col :span="16">-->
                <!--<el-input-->
                  <!--v-model="appForm.baseInfo.factory_code"-->
                  <!--placeholder="请输入"-->
                <!--/>-->
              <!--</el-col>-->
            <!--</el-row>-->
            <el-row :span="24">
              <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>省：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.baseInfo.factory_province"
                  filterable
                  @change="provinceChange"
                  placeholder="请选择">
                  <el-option
                    v-for="item in provinces"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>市：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.baseInfo.factory_city"
                  filterable
                  @change="cityChange"
                  placeholder="请选择">
                  <el-option
                    v-for="item in cities"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>区：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.baseInfo.factory_area"
                  filterable
                  @change="areaChange"
                  placeholder="请选择">
                  <el-option
                    v-for="item in areas"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>详细地址：</el-col>
              <el-col :span="16">
                <el-input
                  v-model="appForm.baseInfo.factory_address"
                  placeholder="请输入"
                />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>工厂类型：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.baseInfo.factory_type"
                  filterable
                  placeholder="请选择">
                  <el-option
                    v-for="item in optionsList['factory-baseInfo-factory_type']"
                    :key="item"
                    :label="item"
                    :value="item">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
          </div>
          <!--基本信息 end-->
          <div class="tab" v-show="!detailState" @click="tabDetailState">点击编辑详细交付信息 <i class="el-icon-arrow-down" style="font-size: 14px; width:10px; height: 10px;"></i></div>
          <!--交货点列表-->
          <div v-if="detailState">
            <!--交货点名称-->
            <div class="model">
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>交货点名称：</el-col>
                <el-col :span="16">
                  <el-input
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].point_basic_info.point_name"
                    placeholder="请输入"
                  />
                </el-col>
              </el-row>
            </div>
            <!--交货点名称 end-->
            <!--工厂交付评分-->
            <div class="model">
              <div class="ztitle">工厂难度定义</div>
              <!--<el-row :span="24">-->
                <!--<el-col :span="6" :offset="1" class="dialogTitle">工厂交付级别：</el-col>-->
                <!--<el-col :span="16">-->
                  <!--<el-select-->
                    <!--style="width: 100%;"-->
                    <!--v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.delivery_class"-->
                    <!--filterable-->
                    <!--placeholder="请选择">-->
                    <!--<el-option-->
                      <!--v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-delivery_class']"-->
                      <!--:key="item"-->
                      <!--:label="item"-->
                      <!--:value="item">-->
                    <!--</el-option>-->
                  <!--</el-select>-->
                <!--</el-col>-->
              <!--</el-row>-->
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">入场分数定义：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.entry_score_definition"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-entry_score_definition']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">卸货工具分数定义：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.unload_tool_score_definition"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-unload_tool_score_definition']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">卸货场景分数定义：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.unload_scene_score_definition"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-unload_scene_score_definition']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">清点分数定义：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.check_score_definition"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-check_score_definition']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">验收分数定义：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.acceptance_score_definition"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-acceptance_score_definition']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">上架分数定义：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.stack_shelf_score_definition"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-stack_shelf_score_definition']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
            </div>
            <!--工厂交付评分 end-->
            <!--交付配置资源-->
            <div class="model">
              <div class="ztitle" :offset="1">交付配置资源信息</div>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">服务中心经理：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager"
                    filterable
                    remote
                    :remote-method="gotGetAccount"
                    @change="goGetAccountsByManagerId"
                    :loading="loading.account"
                    placeholder="请选择(输入中文/英文名字搜索)">
                    <el-option
                      v-for="item in accountOptions"
                      :key="item.username"
                      :label="item.nickname"
                      :value="item.username">
                      <span>{{ item.nickname }}</span>
                      <span>({{ item.username }})</span>
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <!--
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">是否有交付主管：</el-col>
                <el-col :span="16">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_flag" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_flag" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>-->
              <div>
                <el-row :span="24">
                  <el-col :span="11" :offset="1" class="dialogTitle"><div style="font-size: 12px; font-weight: 600;">交付主管信息：</div></el-col>
                  <el-col :span="11" class="dialogTitle"><div class="cha" @click="searchDeliverySupervisor">根据省市区查询</div></el-col>
                </el-row>
                <el-row :span="24">
                  <el-col :span="22" :offset="1">
                    <div style="padding:10px 0; background: #f8f8f8;">
                      <div v-for="(item, index) in appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts" :item="item" :key="index">
                        <el-row :span="24">
                          <el-col :span="3" :offset="1" class="dialogTitle" style="text-align: right;">姓名：</el-col>
                          <el-col :span="8">
                            <el-select
                              style="width: 100%;"
                              v-model="item.delivery_supervisor_domain_account"
                              filterable
                              clearable
                              @change="directorChange(index)"
                              placeholder="请输入姓名搜索">
                              <el-option
                                v-for="itemi in directorOptions"
                                :key="itemi.username"
                                :label="itemi.nickname"
                                :value="itemi.username">
                                <span>{{ itemi.nickname }}</span>
                                <span>({{ itemi.username }})</span>
                              </el-option>
                            </el-select>
                          </el-col>
                          <el-col :span="3" class="dialogTitle" style="text-align: right;">电话：</el-col>
                          <el-col :span="7">
                            <el-input
                              v-model="item.delivery_supervisor_phonenumber"
                              placeholder="请输入"
                            />
                          </el-col>
                        </el-row>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">是否自营覆盖：</el-col>
                <el-col :span="16">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.self_operated_flag" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.self_operated_flag" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">EVM配备情况：</el-col>
                <el-col :span="16">
                  <el-input
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.EVM_configuration"
                    placeholder="分别填写格子机、弹簧机、称重机等机器数量"
                  />
                </el-col>
              </el-row>
            </div>
            <!--交付配置资源 end-->
            <!--人员车辆要求-->
            <div class="model">
              <div class="ztitle" :offset="1">人员车辆要求</div>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">防疫要求：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement"
                    filterable
                    multiple
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement']"
                      :key="item"
                      :label="item"
                      :value="item"
                      :disabled="appForm.factory_delivery_requirement &&
                       ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0])
                       || (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0]))"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">培训要求：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.train_requirement"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-train_requirement']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">服装要求：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement"
                    multiple
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement']"
                      :key="item"
                      :label="item"
                      :value="item"
                      :disabled="appForm.factory_delivery_requirement &&
                       ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0])
                       || (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0]))"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">证件：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement"
                    multiple
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement']"
                      :key="item"
                      :label="item"
                      :value="item"
                      :disabled="appForm.factory_delivery_requirement &&
                       ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0])
                       || (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0]))"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">禁忌：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo"
                    filterable
                    multiple
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo']"
                      :key="item"
                      :label="item"
                      :value="item"
                      :disabled="appForm.factory_delivery_requirement &&
                      ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0]) ||
                       (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0]))"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">禁入厂车辆：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles"
                    filterable
                    multiple
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles']"
                      :key="item"
                      :label="item"
                      :value="item"
                      :disabled="appForm.factory_delivery_requirement &&
                      ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0])
                       ||
                       (item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1])
                      ||
                       ((item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0] || item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1]) &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1]
                       ))"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">车辆配置要求：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement"
                    filterable
                    multiple
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement']"
                      :key="item"
                      :label="item"
                      :value="item"
                      :disabled="appForm.factory_delivery_requirement &&
                      ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0]) ||
                       (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0]))"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">是否要求空车离场：</el-col>
                <el-col :span="16">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.leave_empty_requirement_flag" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.leave_empty_requirement_flag" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-row :span="24">
                  <el-col :span="6" :offset="1" class="dialogTitle">人员车辆要求备注：</el-col>
                  <el-col :span="16">
                    <el-input
                      type="textarea"
                      v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.person_vehicle_requirements_remark"
                      placeholder="请输入"
                    />
                  </el-col>
                </el-row>
              </el-row>
            </div>
            <!--人员车辆要求 end-->
            <!--资料要求-->
            <div class="model">
              <div class="ztitle" :offset="1">资料要求</div>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">送货单模板：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_template"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_template']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">送货单份数：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_amount"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_amount']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">送货单样式：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_style"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_style']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">是否加盖红章：</el-col>
                <el-col :span="16">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.red_seal_flag" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.red_seal_flag" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">标签模板：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_template"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-label_template']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">标签位置：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_location"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-label_location']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="10" :offset="1" class="dialogTitle">标签信息与实际装箱数量一致：</el-col>
                <el-col :span="12">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_information_equal_actual_packing_quantity" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_information_equal_actual_packing_quantity" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">装箱清单：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.pack_list"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-pack_list']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
            </div>
            <!--资料要求 end-->
            <!--交付要求-->
            <div class="model">
              <div class="ztitle" :offset="1">交付要求</div>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">入厂是否预约：</el-col>
                <el-col :span="16">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_reservation_flag" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_reservation_flag" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24" v-if="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_reservation_flag === '是'">
                <el-col :span="6" :offset="1" class="dialogTitle">入厂流程：</el-col>
                <el-col :span="16">
                  <el-input
                    type="textarea"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_process"
                    placeholder="请输入"
                  />
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">验收流程：</el-col>
                <el-col :span="16">
                  <el-input
                    type="textarea"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.acceptance_process"
                    placeholder="请输入"
                  />
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">卸货地点：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.unload_location"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-unload_location']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="8" :offset="1" class="dialogTitle">是否需要送到使用人：</el-col>
                <el-col :span="14">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.send_to_user_flag" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.send_to_user_flag" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="8" :offset="1" class="dialogTitle">是否震坤行卸货：</el-col>
                <el-col :span="14">
                  <div style="padding-top: 6px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.zkh_unload_flag" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.zkh_unload_flag" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">卸货场景：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.unload_scene"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-unload_scene']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">客户是否提供叉车：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.customer_forklift_flag"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-customer_forklift_flag']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">清点货品：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.check_sales"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-check_sales']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">5S要求：</el-col>
                <el-col :span="16">
                  <div style="padding-top: 8px;">
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.requirement_5s" label="是">是</el-radio>
                    <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.requirement_5s" label="否">否</el-radio>
                  </div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">EVM补货频率：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.EVM_replenishment_frequency"
                    filterable
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-EVM_replenishment_frequency']"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1" class="dialogTitle">厂内交付服务：</el-col>
                <el-col :span="16">
                  <el-select
                    style="width: 100%;"
                    v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service"
                    filterable
                    multiple
                    placeholder="请选择">
                    <el-option
                      v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service']"
                      :key="item"
                      :label="item"
                      :value="item"
                      :disabled="appForm.factory_delivery_requirement &&
                      ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7] &&
                      appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] &&
                      appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] === optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7])
                       ||
                       (item === optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7] &&
                       appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7]))"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="6" :offset="1">上传附件：</el-col>
                <el-col :span="16" style="height: auto; line-height: 26px;">
                  <div>
                    <div style="width: 100px;"><el-button @click="fileUpload" type="primary">选择文件</el-button></div>
                    <div>
                      <p class="fileRow" v-for="(itemF, indexF) in appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info" :item="itemF" :key="indexF">
                        <span>附件{{indexF+1}}：<a :href="itemF.url">{{itemF.fileName}}</a></span>
                        <span style="font-size: 12px;" @click="deleteFile(indexF)">删除</span>
                      </p>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <!--交付要求 end-->
          </div>
          <!--交货点列表 end-->
        </div>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right;">
            <el-button size="medium" @click="closeAddDialog" style="margin-left: 20px;">关闭</el-button>
            <el-button size="medium" :loading="loading.add" @click="storeAddDialog" type="primary">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--新建工厂弹出框 end-->
    <!--上传-->
    <el-dialog
      width="580px"
      title="上传附件"
      class="dialogClass"
      :visible.sync="showUploadDialog"
      :close-on-click-modal="false">
      <div class="ruleset">
        <div class="uploadBox" style="text-align: left;">
          <el-upload
            style="width: 100%;"
            ref="upload"
            drag
            :accept="acceptFileType.commonType"
            :action="uploadUrl"
            :on-success="onUploadSuccess"
            :before-upload="beforeUpload"
            :on-error="onUploadError"
            name="file"
            :file-list="fileList"
            :show-file-list="true"
            :auto-upload="true"
            :limit="limit"
            :multiple="true"
            :on-change="handleFileChange"
            :before-remove="handleFileRemove"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处上传，或<em>点击上传</em></div>
          </el-upload>
        </div>
        <el-row :span="24" style="margin-bottom: 0; padding-top: 5px;">
          <!--<el-col :span="12" style="text-align: left; padding-top: 10px; ">-->
          <!--<i style="font-size: 12px; color: red; position: relative; top: 2px; margin-right:6px;">*</i>只能上传.xlsx,.xls,.xlsm文件-->
          <!--</el-col>-->
          <el-col :span="24" style="text-align: right;">
            <!--<el-button size="medium" type="text" style="margin-left: 20px;"><a :href="demoUrl" >下载导入模板</a></el-button>-->
            <el-button size="medium" @click="uploadSubmit()" type="primary">提交</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--上传 end-->
  </div>
</template>

<script>
import {
  getArea,
  getAccountsByName,
  getAccountsByManagerId,
  getCustomers,
  getPropertyEnum,
  createFactory,
  updateFactory,
  getStaffConfig,
  getCustomerDetailInfo
} from '@/api/customerDelivery'
import { mapState } from 'vuex'
import API from '../common/config.ts'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        role: false,
        property: false,
        add: false
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        baseInfo: { // 工厂信息
          customer_info: { // 客户信息
            customer_name: '', // 客户名字
            customer_code: '' // 客户编码
          },
          factory_name: '', // 工厂名称
          factory_province: '', // 省
          factory_province_code: '', // 省code
          factory_city: '', // 市
          factory_city_code: '', // 市code
          factory_area: '', // 区
          factory_area_code: '', // 区code
          factory_address: '', // 详细地址
          factory_type: '' // 工厂类型
        },
        factory_delivery_requirement: { // 工厂交付要求
          delivery_points: [ // 交货点列表
            {
              point_basic_info: {
                point_name: '' // 交货点名称
              },
              delivery_score: { // 交付评分
                delivery_class: '', // 级别
                entry_score_definition: '', // 入场
                unload_tool_score_definition: '', // 卸货工具
                unload_scene_score_definition: '', // 卸货场景
                check_score_definition: '', // 清点
                acceptance_score_definition: '', // 验收
                stack_shelf_score_definition: '' // 上架
              },
              delivery_configuration_resource_information: { // 交付配置资源信息
                service_center_manager: '', // 服务中心经理姓名
                service_center_manager_domain_account: '', // 服务中心经理域账号
                service_center_manager_id: '', // 服务中心经理id
                delivery_supervisor_flag: '是', // 是否有交付主管
                delivery_supervisor_contacts: [ // 交付主管信息
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  },
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  },
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  }
                ],
                self_operated_flag: '', // 是否自营覆盖
                EVM_configuration: '' // EVM配备情况
              },
              person_vehicle_requirements: { // 人员车辆要求
                epidemic_prevention_requirement: [], // 防疫要求
                train_requirement: '', // 培训要求
                cloth_requirement: [], // 服装要求
                certificate_requirement: [], // 证件要求
                taboo: [], // 禁忌要求
                prohibited_vehicles: [], // 禁入场车辆要求
                vehicle_configuration_requirement: [], // 车辆配置要求
                leave_empty_requirement_flag: '', // 是否要求空车离场
                person_vehicle_requirements_remark: '' // 人员车辆要求备注
              },
              data_requirement: { // 资料要求
                delivery_note_template: '', // 送货单模板
                delivery_note_amount: '', // 送货单份数
                delivery_note_style: '', // 送货单样式
                red_seal_flag: '', // 是否加盖红章
                label_template: '', // 标签模板
                label_location: '', // 标签位置
                label_information_equal_actual_packing_quantity: '', // 标签信息与实际装箱数量一致
                pack_list: '' // 装箱清单
              },
              delivery_requirement: {
                entrance_reservation_flag: '', // 入场是否预约
                entrance_process: '', // 入厂流程
                acceptance_process: '', // 验收流程
                unload_location: '', // 卸货地点
                send_to_user_flag: '', // 是否需要送到使用人
                zkh_unload_flag: '', // 是否震坤行卸货
                unload_scene: '', // 卸货场景
                customer_forklift_flag: '', // 客户是/否提供叉车
                check_sales: '', // 清点货品
                requirement_5s: '', // 5S要求
                EVM_replenishment_frequency: '', // EVM补货频率
                inplant_delivery_service: [], // 厂内交付服务
                attachment_info: [] // 附件
              }
            }
          ]
        }
      },
      showAddDialog: false,
      detailState: false,
      optionsList: null, // 所有枚举的列表
      provinces: [], // 省
      cities: [], // 市
      areas: [], // 区
      customerOptions: [], // 客户列表
      accountOptions: [], // 安全中心账户列表（选出服务中心经理）
      directorOptions: [], // 服务中心经理的下级（选出交付主管）
      uploadUrl: '/fe-upload/api/upload/',
      uploadUrl2: '/api-customerDelivery/batchRecord/attachmentUpload',
      currfileName: '', // 文件在本地的名字
      fileList: [], // 文件列表
      fileListOss: [], // 上传完成返回的文件列表
      showUploadDialog: false,
      limit: 50
    }
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    })
  },
  created () {
    this.goGetArea(1)
    this.goGetPropertyEnum()
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.role || This.search.property) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  methods: {
    deleteFile (index) {
      // 删除附件
      console.log(index)
      let This = this
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info.splice(index, 1)
      }).catch(() => {
        console.log('取消删除')
      })
    },
    fileUpload () {
      this.showUploadDialog = true
    },
    // 上传发生变化钩子
    handleFileChange(file, fileList) {
      // this.fileList = fileList
    },
    // 删除之前钩子
    handleFileRemove(file, fileList) {
      // this.fileList = fileList
      let This = this
      let fileListOss = This.fileListOss
      fileListOss.forEach((itemi, index) => {
        if (file.name === itemi.fileName) {
          fileListOss.splice(index, 1)
        }
      })
      This.fileListOss = fileListOss
    },
    beforeUpload (file) {
      console.log('beforeUpload')
      console.log(file)
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false;
      }
      if (!this.$validateFileType(file)) return false
      this.currfileName = file.name
    },
    onUploadSuccess (response, file, fileList) {
      console.log('success')
      console.log(response)
      console.log('1', file)
      console.log('2', fileList)
      let This = this
      if (response && response.data.link) {
        let fileUnit = {
          fileName: file.name,
          url: response.data.link
        }
        This.fileListOss.push(fileUnit)
      }
      // This.showUploadDialog = false
      console.log('3', This.fileListOss)
      console.log('4', This.fileList)
    },
    onUploadError (error) {
      console.log('error')
      console.log(error)
      // this.showUploadDialog = false
      // this.$message.error((error && error.msg) || (error && error.message) || '上传失败')
    },
    uploadSubmit () {
      // this.$refs.upload.submit()
      let This = this
      let fileListOss = This.fileListOss
      console.log('要上傳的文档')
      console.log('fileListOss', fileListOss)
      This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info = This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info.concat(JSON.parse(JSON.stringify(This.fileListOss)))
      This.showUploadDialog = false
      This.fileListOss = []
      This.fileList = []
    },
    searchDeliverySupervisor () {
      // 根据省市区查询交付主管
      console.log('根据省市区查询交付主管')
      let This = this
      let data = {
        province: This.appForm.baseInfo.factory_province || '',
        city: This.appForm.baseInfo.factory_city || '',
        region: This.appForm.baseInfo.factory_area || ''
      }
      if (!This.appForm.baseInfo.factory_province) {
        return false
      }
      getStaffConfig(data).then((res) => {
        if (res.code === 1) {
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[0].delivery_supervisor_name = res.data.nickName
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[0].delivery_supervisor_phonenumber = res.data.phone
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[0].delivery_supervisor_domain_account = res.data.name
          let isHave = false
          for (let i = 0; i < This.directorOptions.length; i++) {
            if (This.directorOptions[i].username === res.data.name) {
              isHave = true
              break
            }
          }
          if (!isHave) {
            This.directorOptions.push({
              username: res.data.name,
              nickname: res.data.nickName
            })
          }
        } else {
          if (res.msg) {
            This.$message.error(res.msg)
          }
        }
      })
    },
    factoryNameChange (name) {
      this.appForm.factory_delivery_requirement.delivery_points[0].point_basic_info.point_name = name
    },
    provinceChange (res) {
      // console.log('切换省')
      this.cities = []
      this.areas = []
      this.appForm.baseInfo.factory_city = ''
      this.appForm.baseInfo.factory_city_code = ''
      this.appForm.baseInfo.factory_area = ''
      this.appForm.baseInfo.factory_area_code = ''
      let provinces = this.provinces
      provinces.forEach((item) => {
        if (item.code === res) {
          this.appForm.baseInfo.factory_province_code = item.code
          this.appForm.baseInfo.factory_province = item.name
        }
      })
      this.goGetArea(2, res)
    },
    cityChange (res) {
      // console.log('切换市')
      this.areas = []
      this.appForm.baseInfo.factory_area = ''
      this.appForm.baseInfo.factory_area_code = ''
      let cities = this.cities
      cities.forEach((item) => {
        if (item.code === res) {
          this.appForm.baseInfo.factory_city_code = item.code
          this.appForm.baseInfo.factory_city = item.name
        }
      })
      this.goGetArea(3, res)
    },
    areaChange (res) {
      console.log('切换区')
      console.log(res)
      let areas = this.areas
      areas.forEach((item) => {
        if (item.code === res) {
          this.appForm.baseInfo.factory_area_code = item.code
          this.appForm.baseInfo.factory_area = item.name
        }
      })
      // this.goGetArea(3, res)
    },
    goGetArea (level, parentCode) {
      /**
       * level: 1省/直辖市,2地级市,3区县,4镇/街道
       * parentCode: 父级地区编码
       * position: 1 搜索部分 2 添加、修改表单部分
       * **/
      let This = this
      let data = {
        level: level,
        parentCode: parentCode
      }
      getArea(data).then(res => {
        if (res.code === 200) {
          if (level === 1) {
            // 省
            This.provinces = res.data
          } else if (level === 2) {
            // 市
            This.cities = res.data
          } else if (level === 3) {
            // 区
            This.areas = res.data
          }
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    gotGetAccount (name) {
      // 获取账户
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        page: 0,
        size: 200,
        name: name
      }
      getAccountsByName(data).then(res => {
        if (res) {
          This.accountOptions = res
        }
      })
    },
    goGetPropertyEnum () {
      // 获取所有枚举值列表
      let This = this
      let data = {
        aggregationCode: API.aggregationCode, // 聚合code 要区分正式测试的
        blackholeId: API.blackholeId // 所属黑洞id 要区分正式测试的
      }
      getPropertyEnum(data).then(res => {
        if (res) {
          This.optionsList = res.enumMap
        }
      })
    },
    goGetCustomers (name) {
      // 获取用户客户中心用户列表
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        customerName: name,
        rowCount: 50,
        pageNo: 1
      }
      getCustomers(data).then(res => {
        if (res.code === '0000') {
          This.customerOptions = res.result.rows
        } else {
          This.$message.error(res.message)
        }
      })
    },
    goGetAccountsByManagerId (res) {
      // 通过id获取其下级
      let This = this
      let accountOptions = This.accountOptions
      let currAccountInfo = null // 当前选中的用户信息
      accountOptions.forEach((item) => {
        if (item.username === res) {
          currAccountInfo = item
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager = item.nickname
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_domain_account = item.username
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_id = item.id
        }
      })
      if (!currAccountInfo) {
        return false
      }
      // 根据服务中心经理id去获取其下级列表
      getAccountsByManagerId(currAccountInfo.id).then(res => {
        if (res) {
          This.directorOptions = res
        }
      })
    },
    directorChange (index) {
      let This = this
      if (!This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_domain_account) {
        This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_name = ''
      } else {
        let directorOptions = This.directorOptions
        directorOptions.forEach((item) => {
          if (item.username === This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_domain_account) {
            This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_name = item.nickname
            This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_domain_account = item.username
          }
        })
      }
    },
    tabDetailState () {
      this.detailState = true
    },
    related_customer_change (res) {
      console.log(res)
      let customerOptions = this.customerOptions
      customerOptions.forEach((item) => {
        if (item.customerNumber === res) {
          this.appForm.baseInfo.customer_info.customer_code = item.customerNumber
          this.appForm.baseInfo.customer_info.customer_name = item.customerName
          this.goGetCustomerDetailInfo(item.customerNumber)
        }
      })
    },
    goGetCustomerDetailInfo (customerNumber) {
      let This = this
      if (!This.appForm.baseInfo.factory_province_code && !This.appForm.baseInfo.factory_city_code && !This.appForm.baseInfo.factory_area_code && !This.appForm.baseInfo.factory_address) {
        // 省市区都不存在时才自动写入
        let data = {
          customerNumber: customerNumber
        }
        getCustomerDetailInfo(data).then(res => {
          if (res.code === '0000') {
            if (res.result.province) {
              This.appForm.baseInfo.factory_province_code = res.result.province
              This.appForm.baseInfo.factory_province = res.result.provinceName
              This.goGetArea(2, res.result.province)
            }
            if (res.result.city) {
              This.appForm.baseInfo.factory_city_code = res.result.city
              This.appForm.baseInfo.factory_city = res.result.cityName
              This.goGetArea(3, res.result.city)
            }
            if (res.result.region) {
              This.appForm.baseInfo.factory_area_code = res.result.region
              This.appForm.baseInfo.factory_area = res.result.regionName
            }
            if (res.result.streetName) {
              This.appForm.baseInfo.factory_address = res.result.streetName
            }
          }
        })
      }
    },
    openRuleDialog () {
      // 打开添加数据窗口
      this.appForm = { // 添加form应用的内容
        id: '', // 修改时候的appid
        baseInfo: { // 工厂信息
          customer_info: {
            customer_name: '', // 所属客户
            customer_code: '' // 所属客户编码
          },
          factory_name: '', // 工厂名称
          factory_province: '', // 省
          factory_province_code: '', // 省code
          factory_city: '', // 市
          factory_city_code: '', // 市code
          factory_area: '', // 区
          factory_area_code: '', // 区code
          factory_address: '', // 详细地址
          factory_type: '' // 工厂类型
        },
        factory_delivery_requirement: { // 工厂交付要求
          delivery_points: [ // 交货点列表
            {
              point_basic_info: {
                point_name: '' // 交货点名称
              },
              delivery_score: { // 交付评分
                delivery_class: '', // 级别
                entry_score_definition: '', // 入场
                unload_tool_score_definition: '', // 卸货工具
                unload_scene_score_definition: '', // 卸货场景
                check_score_definition: '', // 清点
                acceptance_score_definition: '', // 验收
                stack_shelf_score_definition: '' // 上架
              },
              delivery_configuration_resource_information: { // 交付配置资源信息
                service_center_manager: '', // 服务中心经理姓名
                service_center_manager_domain_account: '', // 服务中心经理域账号
                service_center_manager_id: '', // 服务中心经理id
                delivery_supervisor_flag: '是', // 是否有交付主管
                delivery_supervisor_contacts: [ // 交付主管信息
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  },
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  },
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  }
                ],
                self_operated_flag: '', // 是否自营覆盖
                EVM_configuration: '' // EVM配备情况
              },
              person_vehicle_requirements: { // 人员车辆要求
                epidemic_prevention_requirement: [], // 防疫要求
                train_requirement: '', // 培训要求
                cloth_requirement: [], // 服装要求
                certificate_requirement: [], // 证件要求
                taboo: [], // 禁忌要求
                prohibited_vehicles: [], // 禁入场车辆要求
                vehicle_configuration_requirement: [], // 车辆配置要求
                leave_empty_requirement_flag: '', // 是否要求空车离场
                person_vehicle_requirements_remark: '' // 人员车辆要求备注
              },
              data_requirement: { // 资料要求
                delivery_note_template: '', // 送货单模板
                delivery_note_amount: '', // 送货单份数
                delivery_note_style: '', // 送货单样式
                red_seal_flag: '', // 是否加盖红章
                label_template: '', // 标签模板
                label_location: '', // 标签位置
                label_information_equal_actual_packing_quantity: '', // 标签信息与实际装箱数量一致
                pack_list: '' // 装箱清单
              },
              delivery_requirement: {
                entrance_reservation_flag: '', // 入场是否预约
                entrance_process: '', // 入厂流程
                acceptance_process: '', // 验收流程
                unload_location: '', // 卸货地点
                send_to_user_flag: '', // 是否需要送到使用人
                zkh_unload_flag: '', // 是否震坤行卸货
                unload_scene: '', // 卸货场景
                customer_forklift_flag: '', // 客户是/否提供叉车
                check_sales: '', // 清点货品
                requirement_5s: '', // 5S要求
                EVM_replenishment_frequency: '', // EVM补货频率
                inplant_delivery_service: [], // 厂内交付服务
                attachment_info: [] // 附件
              }
            }
          ]
        }
      }
      this.currAppInfo = null
      this.showAddDialog = true
    },
    closeAddDialog () {
      this.showAddDialog = false
    },
    storeAddDialog () {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp (row, index) {
      console.log('修改数据')
      this.appForm = {
        id: row.id, // 修改时候的appid
        role: row.role, // 角色
        property: row.property, // 属性
        ispower: row.ispower // 是否有权限
      }
      this.currAppInfo = row
      this.showAddDialog = true
    },
    gocreateApps () {
      // 新建、编辑数据
      let This = this
      This.$parent.searchReset('create')
      let appId = this.appForm.id // 修改数据id
      let data = this.appForm
      if (!data.baseInfo.customer_info.customer_code || !data.baseInfo.factory_name || !data.baseInfo.factory_province || !data.baseInfo.factory_city || !data.baseInfo.factory_area || !data.baseInfo.factory_address || !data.baseInfo.factory_type || !data.factory_delivery_requirement.delivery_points[0].point_basic_info.point_name) {
        this.$message.error('请将工厂基本信息填写完整')
        return false
      }
      data.id = appId
      let contacts = data.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts
      let isTrue = true // 手机号格式是否正确
      let isMatch = true // 交付主管和交付主管手机号是否匹配（伴生关系，同有同无，不能只填一个）

      // 交付主管逻辑处理
      let mobileReg = /^1\d{10}$/
      let telReg = /^0\d{2,3}-?\d{7,8}$/
      contacts.forEach((item, index) => {
        if ((item.delivery_supervisor_phonenumber && item.delivery_supervisor_name) || (!item.delivery_supervisor_phonenumber && !item.delivery_supervisor_name)) {
          // 两个都存在或者两个都不存在时是正确格式
        } else {
          isMatch = false
        }
        if (item.delivery_supervisor_phonenumber && !telReg.test(item.delivery_supervisor_phonenumber) && !mobileReg.test(item.delivery_supervisor_phonenumber)) {
          isTrue = false
        }
      })
      // 交付主管逻辑处理 end

      if (!isMatch) {
        this.$message.error('交付主管和交付主管手机号必须同时填或者同时不填')
        return false
      }
      if (!isTrue) {
        this.$message.error('交付主管手机号格式不正确')
        return false
      }
      This.loading.add = true
      if (appId) {
        // 修改数据
        updateFactory(data, appId).then(res => {
          if (res) {
            This.$message.success('修改成功')
            // 修改成功更新数据列表
            This.$parent.goGetApps()
            this.showAddDialog = false
          }
          This.loading.add = false
        }).catch(() => {
          This.loading.add = false
        })
      } else {
        delete data.id
        // 添加数据
        createFactory(data).then(res => {
          if (res.code === 0) {
            This.$message.success('创建成功')
            // 新增成功创建数据列表
            This.$parent.goPushData(res.data)
            this.showAddDialog = false
          } else {
            This.$message.error(res.message)
          }
          This.loading.add = false
        }).catch(() => {
          This.loading.add = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/colors.scss";
@import '../common/static/css/common.scss';

.searchBox {
  padding-left: 20px !important;
  padding-right: 20px !important;
  margin-top: 0 !important;
  .el-col {
    margin-bottom: 10px;
  }
}
.factoryListTop {
  .title {
    font-size: 16px;
    font-weight: 600;
    line-height: 36px;
  }
}
.groupHandle {
  text-align: center;
  padding:10px 0;
  min-height: 100px;
  p {
    padding:5px 0;
    font-size: 12px;
    cursor: pointer;
    &:hover {
      color: $color-blue-200;
    }
  }
}
.cha {
  font-size: 12px;
  text-align: right;
  cursor: pointer;
  color: $color-blue-200;
  opacity: 0.8;
  &:hover {
    opacity: 1;
  }
}
.rulesetcont {
  border-top: 1px solid #eee;
  .ztitle {
    font-size: 14px;
    font-weight: 600;
    padding: 10px 0;
  }
  .dialogTitle {
    text-align: left;
  }
}
.fileRow {
  a {
    cursor: pointer;
    color: #5f84ff;
    &:hover {
      text-decoration: underline;
    }
  }
  span:nth-of-type(2) {
    cursor: pointer;
    color: red;
    margin-left: 20px;
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
<style lang="scss">
@import "../../../style/colors.scss";
.createfactoryDialog {
  .el-dialog__body {
    padding-top: 5px !important;
  }
  .el-dialog__title {
    font-size: 16px;
    text-align: center;
  }
  .tab {
    cursor: pointer;
    color: $color-blue-200;
    opacity: 0.8;
    &:hover {
      opacity: 1;
    }
  }
}
</style>
