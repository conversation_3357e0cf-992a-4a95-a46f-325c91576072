<template>
  <div v-loading="loading.page">
    <!--交货点名称-->
    <div class="factoryFormModel">
      <div class="form">
        <el-row :span="24">
          <el-col :span="4">交货点名称</el-col>
          <el-col :span="20">
            <el-input v-if="checkPower('allIsOk')" v-model="appForm.factory_delivery_requirement.delivery_points[0].point_basic_info.point_name" placeholder="请输入" />
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].point_basic_info.point_name}}</div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--交货点名称 end-->
    <!--交付评分-->
    <div class="factoryFormModel" v-if="optionsList">
      <div class="title">工厂难度定义</div>
      <div class="form" v-if="appForm.factory_delivery_requirement.delivery_points[0].delivery_score">
        <el-row :span="24">
          <el-col :span="4">工厂交付级别</el-col>
          <el-col :span="8">
            <!--<el-select-->
              <!--v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_score-delivery_class')"-->
              <!--style="width: 100%;"-->
              <!--v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.delivery_class"-->
              <!--filterable-->
              <!--placeholder="请选择">-->
              <!--<el-option-->
                <!--v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-delivery_class']"-->
                <!--:key="item"-->
                <!--:label="item"-->
                <!--:value="item">-->
              <!--</el-option>-->
            <!--</el-select>-->
            <div class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_score.delivery_class}}</div>
          </el-col>
          <el-col :span="4">入厂分数定义</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_score-entry_score_definition')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.entry_score_definition"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-entry_score_definition']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_score.entry_score_definition}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">卸货工具分数定义</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_score-unload_tool_score_definition')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.unload_tool_score_definition"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-unload_tool_score_definition']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_score.unload_tool_score_definition}}</div>
          </el-col>
          <el-col :span="4">卸货场景分数定义</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_score-unload_scene_score_definition')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.unload_scene_score_definition"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-unload_scene_score_definition']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_score.unload_scene_score_definition}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">清点分数定义</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_score-check_score_definition')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.check_score_definition"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-check_score_definition']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_score.check_score_definition}}</div>
          </el-col>
          <el-col :span="4">验收分数定义</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_score-acceptance_score_definition')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.acceptance_score_definition"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-acceptance_score_definition']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_score.acceptance_score_definition}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">上架分数定义</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_score-stack_shelf_score_definition')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_score.stack_shelf_score_definition"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_score-stack_shelf_score_definition']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_score.stack_shelf_score_definition}}</div>
          </el-col>
          <el-col :span="4"></el-col>
          <el-col :span="8">
          </el-col>
        </el-row>
      </div>
    </div>
    <!--交付评分 end-->
    <!--交付配置资源信息-->
    <div class="factoryFormModel">
      <div class="title">
        <span>交付配置资源信息</span>
        <span class="cha" v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-delivery_supervisor_contacts')" @click="searchDeliverySupervisor">根据省市区查询</span>
      </div>
      <div class="form" v-if="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information">
        <el-row :span="24">
          <el-col :span="4">服务中心经理</el-col>
          <el-col :span="8">
            <el-select
              style="width: 100%;"
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-service_center_manager')"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager"
              filterable
              remote
              :remote-method="gotGetAccount"
              @change="managerChange"
              :loading="loading.account"
              placeholder="请选择(输入中文/英文名字搜索)">
              <el-option
                v-for="item in accountOptions"
                :key="item.username"
                :label="item.nickname"
                :value="item.username">
                <span>{{ item.nickname }}</span>
                <span>({{ item.username }})</span>
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager}}</div>
          </el-col>
          <el-col :span="4">是否自营覆盖</el-col>
          <el-col :span="8">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-self_operated_flag')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.self_operated_flag" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.self_operated_flag" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.self_operated_flag}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">EVM配备情况</el-col>
          <el-col :span="20">
            <el-input v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-EVM_configuration')" v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.EVM_configuration" placeholder="请输入" />
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.EVM_configuration}}</div>
          </el-col>
          <!--
          <el-col :span="4">是否有交付主管</el-col>
          <el-col :span="8">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-delivery_supervisor_flag')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_flag" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_flag" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_flag}}</div>
          </el-col>-->
        </el-row>
        <div>
          <el-row :span="24" v-for="(item, index) in appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts" :item="item" :key="index">
            <el-col :span="4">交付主管{{index + 1}}</el-col>
            <el-col :span="8">
              <el-select
                style="width: 100%;"
                v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-delivery_supervisor_contacts')"
                v-model="item.delivery_supervisor_name"
                filterable
                clearable
                @change="directorChange(index)"
                placeholder="请输入姓名搜索">
                <el-option
                  v-for="itemi in directorOptions"
                  :key="itemi.username"
                  :label="itemi.nickname"
                  :value="itemi.username">
                  <span>{{ itemi.nickname }}</span>
                  <span>({{ itemi.username }})</span>
                </el-option>
              </el-select>
              <div v-else class="resValue" >{{item.delivery_supervisor_name}}</div>
            </el-col>
            <el-col :span="4">交付主管{{index + 1}}联系方式</el-col>
            <el-col :span="8">
              <el-input v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_configuration_resource_information-delivery_supervisor_contacts')" v-model="item.delivery_supervisor_phonenumber" placeholder="请输入" />
              <div v-else class="resValue" >{{item.delivery_supervisor_phonenumber}}</div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
    <!--交付配置资源信息 end-->
    <!--人员车辆要求-->
    <div class="factoryFormModel">
      <div class="title">人员车辆要求</div>
      <div class="form" v-if="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements">
        <el-row :span="24">
          <el-col :span="4">防疫要求</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement"
              filterable
              multiple
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement']"
                :key="item"
                :label="item"
                :value="item"
                :disabled="appForm.factory_delivery_requirement &&
                       ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0])
                       || (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-epidemic_prevention_requirement'][0]))"
              >
              </el-option>
            </el-select>
            <div v-else class="resValue" :title="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement.join(',')">{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.epidemic_prevention_requirement.join(',')}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">禁忌</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo"
              filterable
              multiple
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo']"
                :key="item"
                :label="item"
                :value="item"
                :disabled="appForm.factory_delivery_requirement &&
                ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0]) ||
                 (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0] &&
                 appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] &&
                 appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-taboo'][0]))"
              >
              </el-option>
            </el-select>
            <div v-else class="resValue" :title="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo.join(',')">{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.taboo.join(',')}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">培训要求</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-train_requirement')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.train_requirement"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-train_requirement']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.train_requirement}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">禁入厂车辆</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles"
              filterable
              multiple
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles']"
                :key="item"
                :label="item"
                :value="item"
                :disabled="appForm.factory_delivery_requirement &&
                ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0])
                 ||
                 (item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1])
                ||
                 ((item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0] || item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1]) &&
                 appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] &&
                 appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][0] &&
                 appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-prohibited_vehicles'][1]
                 ))"
              >
              </el-option>
            </el-select>
            <div v-else class="resValue" :title="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles.join(',')">{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.prohibited_vehicles.join(',')}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">服装要求</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement"
              multiple
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement']"
                :key="item"
                :label="item"
                :value="item"
                :disabled="appForm.factory_delivery_requirement &&
                       ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0])
                       || (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-cloth_requirement'][0]))"
              >
              </el-option>
            </el-select>
            <div v-else class="resValue" :tile="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement.join(',')">{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.cloth_requirement.join(',')}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">车辆配置要求</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement"
              filterable
              multiple
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement']"
                :key="item"
                :label="item"
                :value="item"
                :disabled="appForm.factory_delivery_requirement &&
                ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] &&
                appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0]) ||
                 (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0] &&
                 appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] &&
                 appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-vehicle_configuration_requirement'][0]))"
              >
              </el-option>
            </el-select>
            <div v-else class="resValue" :title="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement.join(',')">{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.vehicle_configuration_requirement.join(',')}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">证件</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement"
              filterable
              multiple
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement']"
                :key="item"
                :label="item"
                :value="item"
                :disabled="appForm.factory_delivery_requirement &&
                       ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0])
                       || (item === optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] &&
                       appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-certificate_requirement'][0]))"
              >
              </el-option>
            </el-select>
            <div v-else class="resValue" :title="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement.join(',')">{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.certificate_requirement.join(',')}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">是否要求空车离场</el-col>
          <el-col :span="20" style="height: auto; min-height: 40px;">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-leave_empty_requirement_flag')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.leave_empty_requirement_flag" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.leave_empty_requirement_flag" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.leave_empty_requirement_flag}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4" style="line-height: 60px; height: 60px;">人员车辆要求备注</el-col>
          <el-col :span="20" style="min-height: 60px; height: auto; padding:2px 10px;">
            <el-input type="textarea" v-if="checkPower('factory-factory_delivery_requirement-delivery_points-person_vehicle_requirements-person_vehicle_requirements_remark')" v-model="appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.person_vehicle_requirements_remark" placeholder="请输入" />
            <div v-else style="line-height: 25px; white-space:normal; word-break:break-all; word-wrap: break-word;">{{appForm.factory_delivery_requirement.delivery_points[0].person_vehicle_requirements.person_vehicle_requirements_remark}}</div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--人员车辆要求 end-->
    <!--资料要求-->
    <div class="factoryFormModel">
      <div class="title">资料要求</div>
      <div class="form" v-if="appForm.factory_delivery_requirement.delivery_points[0].data_requirement">
        <el-row :span="24">
          <el-col :span="4">送货单模板</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_template')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_template"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_template']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_template}}</div>
          </el-col>
          <el-col :span="5">送货单份数</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_amount')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_amount"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_amount']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_amount}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">送货单样式</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_style')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_style"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-delivery_note_style']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.delivery_note_style}}</div>
          </el-col>
          <el-col :span="5">标签模板</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-label_template')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_template"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-label_template']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_template}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">标签位置</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-label_location')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_location"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-label_location']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_location}}</div>
          </el-col>
          <el-col :span="5"><div style="white-space: nowrap; word-break: keep-all; font-size: 12px;">标签信息与实际装箱数量一致</div></el-col>
          <el-col :span="7">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-label_information_equal_actual_packing_quantity')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_information_equal_actual_packing_quantity" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_information_equal_actual_packing_quantity" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.label_information_equal_actual_packing_quantity}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">是否加盖红章</el-col>
          <el-col :span="8">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-red_seal_flag')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.red_seal_flag" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.red_seal_flag" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.red_seal_flag}}</div>
          </el-col>
          <el-col :span="5">装箱清单</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-data_requirement-pack_list')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].data_requirement.pack_list"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-data_requirement-pack_list']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].data_requirement.pack_list}}</div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--资料要求 end-->
    <!--交付要求-->
    <div class="factoryFormModel">
      <div class="title">交付要求</div>
      <div class="form" v-if="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement">
        <el-row :span="24">
          <el-col :span="5">入厂是否预约</el-col>
          <el-col :span="19">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-entrance_reservation_flag')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_reservation_flag" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_reservation_flag" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_reservation_flag}}</div>
          </el-col>
        </el-row>
        <el-row :span="24" v-if="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_reservation_flag === '是'">
          <el-col :span="5" style="line-height: 60px; height: 60px;">入厂流程</el-col>
          <el-col :span="19" style="min-height: 60px; height: auto; padding:2px 10px;">
            <el-input type="textarea" v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-entrance_process')" v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_process" placeholder="请输入" />
            <div v-else style="line-height: 25px; white-space:normal; word-break:break-all; word-wrap: break-word;">{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.entrance_process}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="5" style="line-height: 60px; height: 60px;">验收流程</el-col>
          <el-col :span="19" style="min-height: 60px; height: auto; padding:2px 10px;">
            <el-input type="textarea" v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-acceptance_process')" v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.acceptance_process" placeholder="请输入" />
            <div v-else style="line-height: 25px; white-space:normal; word-break:break-all; word-wrap: break-word;">{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.acceptance_process}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="5">卸货地点</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-unload_location')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.unload_location"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-unload_location']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.unload_location}}</div>
          </el-col>
          <el-col :span="5">是否需要送到使用人</el-col>
          <el-col :span="7">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-send_to_user_flag')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.send_to_user_flag" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.send_to_user_flag" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.send_to_user_flag}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="5">是否震坤行卸货</el-col>
          <el-col :span="7">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-zkh_unload_flag')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.zkh_unload_flag" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.zkh_unload_flag" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.zkh_unload_flag}}</div>
          </el-col>
          <el-col :span="5">卸货场景</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-unload_scene')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.unload_scene"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-unload_scene']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.unload_scene}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="5">客户是否提供叉车</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-customer_forklift_flag')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.customer_forklift_flag"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-customer_forklift_flag']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.customer_forklift_flag}}</div>
          </el-col>
          <el-col :span="5">清点货品</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-check_sales')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.check_sales"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-check_sales']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.check_sales}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="5">5S要求</el-col>
          <el-col :span="7">
            <div v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-requirement_5s')">
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.requirement_5s" label="是">是</el-radio>
              <el-radio v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.requirement_5s" label="否">否</el-radio>
            </div>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.requirement_5s}}</div>
          </el-col>
          <el-col :span="5">EVM补货频率</el-col>
          <el-col :span="7">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-EVM_replenishment_frequency')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.EVM_replenishment_frequency"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-EVM_replenishment_frequency']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.EVM_replenishment_frequency}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="5">厂内交付服务</el-col>
          <el-col :span="19" style="height: auto; min-height: 40px;">
            <el-select
              v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service')"
              style="width: 100%;"
              v-model="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service"
              filterable
              multiple
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service']"
                :key="item"
                :label="item"
                :value="item"
                :disabled="appForm.factory_delivery_requirement &&
                ((item !== optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7] &&
                appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] &&
                appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] === optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7])
                 ||
                 (item === optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7] &&
                 appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] &&
                 appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service[0] !== optionsList['factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service'][7]))"
              >
              </el-option>
            </el-select>
            <div v-else class="resValue" :title="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service.join(',')">{{appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.inplant_delivery_service.join(',')}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="5">上传附件</el-col>
          <el-col :span="19" style="height: auto; line-height: 26px; min-height: 40px; padding: 7px 10px; border-left: 1px solid #eee;">
            <div style="display: flex;">
              <div style="width: 100px;" v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service')"><el-button @click="fileUpload" type="primary">选择文件</el-button></div>
              <div  style="flex: 1;">
                <div v-if="appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info">
                <p class="fileRow" v-for="(itemF, indexF) in appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info" :item="itemF" :key="indexF">
                  <span>附件{{indexF+1}}：<a :href="itemF.url">{{itemF.fileName}}</a></span>
                  <span style="font-size: 12px;" v-if="checkPower('factory-factory_delivery_requirement-delivery_points-delivery_requirement-inplant_delivery_service')" @click="deleteFile(indexF)">删除</span>
                </p>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--交付要求 end-->
    <!--上传-->
    <el-dialog
      width="580px"
      title="上传附件"
      class="dialogClass"
      :visible.sync="showUploadDialog"
      :close-on-click-modal="false">
      <div class="ruleset">
        <div class="uploadBox" style="text-align: left;">
          <el-upload
            style="width: 100%;"
            ref="upload"
            drag
            :accept="acceptFileType.commonType"
            :action="uploadUrl"
            :on-success="onUploadSuccess"
            :before-upload="beforeUpload"
            :on-error="onUploadError"
            name="file"
            :file-list="fileList"
            :show-file-list="true"
            :auto-upload="true"
            :limit="limit"
            :multiple="true"
            :on-change="handleFileChange"
            :before-remove="handleFileRemove"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处上传，或<em>点击上传</em></div>
          </el-upload>
        </div>
        <el-row :span="24" style="margin-bottom: 0; padding-top: 5px;">
          <!--<el-col :span="12" style="text-align: left; padding-top: 10px; ">-->
            <!--<i style="font-size: 12px; color: red; position: relative; top: 2px; margin-right:6px;">*</i>只能上传.xlsx,.xls,.xlsm文件-->
          <!--</el-col>-->
          <el-col :span="24" style="text-align: right;">
            <!--<el-button size="medium" type="text" style="margin-left: 20px;"><a :href="demoUrl" >下载导入模板</a></el-button>-->
            <el-button size="medium" @click="uploadSubmit()" type="primary">提交</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--上传 end-->
  </div>
</template>

<script>
import {
  getAccountsByName,
  getAccountsByManagerId,
  getPropertyEnum,
  updateFactory,
  getStaffConfig
} from '@/api/customerDelivery'
import { mapState } from 'vuex'
import API from '../common/config.ts'
export default {
  props: ['pageIsEdit', 'factoryId', 'userRoleCode', 'rolePowers', 'factoryDetail'],
  data () {
    return {
      loading: {
        page: false,
        table: true,
        role: false,
        property: false
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        factory_delivery_requirement: { // 工厂交付要求
          delivery_points: [ // 交货点列表
            {
              point_basic_info: {
                point_name: '' // 交货点名称
              },
              delivery_score: { // 交付评分
                delivery_class: '', // 级别
                entry_score_definition: '', // 入厂
                unload_tool_score_definition: '', // 卸货工具
                unload_scene_score_definition: '', // 卸货场景
                check_score_definition: '', // 清点
                acceptance_score_definition: '', // 验收
                stack_shelf_score_definition: '' // 上架
              },
              delivery_configuration_resource_information: { // 交付配置资源信息
                service_center_manager: '', // 服务中心经理姓名
                service_center_manager_domain_account: '', // 服务中心经理域账号
                service_center_manager_id: '', // 服务中心经理id
                delivery_supervisor_flag: '是', // 是否有交付主管
                delivery_supervisor_contacts: [ // 交付主管信息
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  },
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  },
                  {
                    delivery_supervisor_name: '', // 姓名
                    delivery_supervisor_phonenumber: '', // 手机号
                    delivery_supervisor_domain_account: '' // 域账号
                  }
                ],
                self_operated_flag: '', // 是否自营覆盖
                EVM_configuration: '' // EVM配备情况
              },
              person_vehicle_requirements: { // 人员车辆要求
                epidemic_prevention_requirement: [], // 防疫要求
                train_requirement: '', // 培训要求
                cloth_requirement: [], // 服装要求
                certificate_requirement: [], // 证件要求
                taboo: [], // 禁忌要求
                prohibited_vehicles: [], // 禁入厂车辆要求
                vehicle_configuration_requirement: [], // 车辆配置要求
                leave_empty_requirement_flag: '', // 是否要求空车离场
                person_vehicle_requirements_remark: '' // 人员车辆要求备注
              },
              data_requirement: { // 资料要求
                delivery_note_template: '', // 送货单模板
                delivery_note_amount: '', // 送货单份数
                delivery_note_style: '', // 送货单样式
                red_seal_flag: '', // 是否加盖红章
                label_template: '', // 标签模板
                label_location: '', // 标签位置
                label_information_equal_actual_packing_quantity: '', // 标签信息与实际装箱数量一致
                pack_list: '' // 装箱清单
              },
              delivery_requirement: {
                entrance_reservation_flag: '', // 入厂是否预约
                entrance_process: '', // 入厂流程
                acceptance_process: '', // 验收流程
                unload_location: '', // 卸货地点
                send_to_user_flag: '', // 是否需要送到使用人
                zkh_unload_flag: '', // 是否震坤行卸货
                unload_scene: '', // 卸货场景
                customer_forklift_flag: '', // 客户是/否提供叉车
                check_sales: '', // 清点货品
                requirement_5s: '', // 5S要求
                EVM_replenishment_frequency: '', // EVM补货频率
                inplant_delivery_service: [], // 厂内交付服务
                attachment_info: [] // 附件
              }
            }
          ]
        }
      },
      optionsList: null, // 所有枚举的列表
      customerOptions: [], // 客户列表
      accountOptions: [], // 安全中心账户列表（选出服务中心经理）
      directorOptions: [], // 服务中心经理的下级（选出交付主管）
      provinceOptions: [
        {
          id: 1,
          name: '北京市'
        },
        {
          id: 2,
          name: '河北省'
        },
        {
          id: 3,
          name: '河南省'
        },
        {
          id: 4,
          name: '上海市'
        }
      ],
      uploadUrl: '/fe-upload/api/upload/',
      uploadUrl2: '/api-customerDelivery/batchRecord/attachmentUpload',
      currfileName: '', // 文件在本地的名字
      fileList: [], // 文件列表
      fileListOss: [], // 上传完成返回的文件列表
      showUploadDialog: false,
      limit: 50
    }
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    })
  },
  created () {
    this.goGetPropertyEnum()
    this.appForm = this.factoryDetail
    console.log('详情')
    console.log(this.factoryDetail)
    let managerId = this.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_id
    if (managerId) {
      this.goGetAccountsByManagerId(managerId)
    }
  },
  methods: {
    deleteFile (index) {
      // 删除附件
      console.log(index)
      let This = this
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info.splice(index, 1)
      }).catch(() => {
        console.log('取消删除')
      })
    },
    fileUpload () {
      this.showUploadDialog = true
    },
    // 上传发生变化钩子
    handleFileChange(file, fileList) {
      // this.fileList = fileList
    },
    // 删除之前钩子
    handleFileRemove(file, fileList) {
      // this.fileList = fileList
      let This = this
      let fileListOss = This.fileListOss
      fileListOss.forEach((itemi, index) => {
        if (file.name === itemi.fileName) {
          fileListOss.splice(index, 1)
        }
      })
      This.fileListOss = fileListOss
    },
    beforeUpload (file) {
      console.log('beforeUpload')
      console.log(file)
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false;
      }
      if (!this.$validateFileType(file)) return false
      this.currfileName = file.name
    },
    onUploadSuccess (response, file, fileList) {
      console.log('success')
      console.log(response)
      console.log('1', file)
      console.log('2', fileList)
      let This = this
      if (response && response.data.link) {
        let fileUnit = {
          fileName: file.name,
          url: response.data.link
        }
        This.fileListOss.push(fileUnit)
      }
      // This.showUploadDialog = false
      console.log('3', This.fileListOss)
      console.log('4', This.fileList)
    },
    onUploadError (error) {
      console.log('error')
      console.log(error)
      // this.showUploadDialog = false
      // this.$message.error((error && error.msg) || (error && error.message) || '上传失败')
    },
    uploadSubmit () {
      // this.$refs.upload.submit()
      let This = this
      let fileListOss = This.fileListOss
      console.log('要上傳的文档')
      console.log('fileListOss', fileListOss)
      if (!This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info) {
        This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info = []
      }
      This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info = This.appForm.factory_delivery_requirement.delivery_points[0].delivery_requirement.attachment_info.concat(JSON.parse(JSON.stringify(This.fileListOss)))
      This.showUploadDialog = false
      This.fileListOss = []
      This.fileList = []
    },
    searchDeliverySupervisor () {
      // 根据省市区查询交付主管
      console.log('根据省市区查询交付主管')
      let This = this
      let data = {
        province: This.appForm.baseInfo.factory_province || '',
        city: This.appForm.baseInfo.factory_city || '',
        region: This.appForm.baseInfo.factory_area || ''
      }
      if (!This.appForm.baseInfo.factory_province) {
        return false
      }
      getStaffConfig(data).then((res) => {
        if (res.code === 1) {
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[0].delivery_supervisor_name = res.data.nickName
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[0].delivery_supervisor_phonenumber = res.data.phone
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[0].delivery_supervisor_domain_account = res.data.name
          let isHave = false
          for (let i = 0; i < This.directorOptions.length; i++) {
            if (This.directorOptions[i].username === res.data.name) {
              isHave = true
              break
            }
          }
          if (!isHave) {
            This.directorOptions.push({
              username: res.data.name,
              nickname: res.data.nickName
            })
          }
        } else {
          if (res.msg) {
            This.$message.error(res.msg)
          }
        }
      })
    },
    checkPower (code) {
      // 表单状态：pageIsEdit true 编辑状态 false 查看状态
      let pageIsEdit = this.pageIsEdit
      // console.log('============================================')
      // console.log('当前用户所属角色')
      // console.log(this.userRoleCode)
      // console.log('当前用户所属角色含有修改权限的字段列表')
      // console.log(this.rolePowers)
      // console.log('当前字段全路径code')
      // console.log(code)
      let result = true
      if (pageIsEdit) {
        let hasPower = false
        if (code === 'allIsOk') {
          // 所有有查看权限的人都有修改权限
          hasPower = true
        } else {
          this.rolePowers.forEach((item) => {
            // 包含两种情况，1.含有父级别code，则父级别下面所有属性都有权限 2.两个code完全相等，则仅仅这个属性有权限
            if (code.startsWith(item.property_code)) {
              hasPower = true
              return false
            }
          })
        }
        if (hasPower) {
          result = true // 有修改权限
        } else {
          result = false // 没有修改权限
        }
      } else {
        result = false // 没有修改权限
      }
      // console.log('code=' + code, result)
      return result
    },
    edit () {
    },
    store () {
      // 编辑保存
      let This = this
      let appId = this.appForm.id // 修改数据id
      let data = this.appForm
      data.aggregationId = appId
      data.id = appId
      let contacts = data.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts
      let isTrue = true // 手机号格式是否正确
      let isMatch = true // 交付主管和交付主管手机号是否匹配（伴生关系，同有同无，不能只填一个）

      // 交付主管逻辑处理
      let mobileReg = /^1\d{10}$/
      let telReg = /^0\d{2,3}-?\d{7,8}$/
      contacts.forEach((item, index) => {
        if ((item.delivery_supervisor_phonenumber && item.delivery_supervisor_name) || (!item.delivery_supervisor_phonenumber && !item.delivery_supervisor_name)) {
          // 两个都存在或者两个都不存在时是正确格式
        } else {
          isMatch = false
        }
        if (item.delivery_supervisor_phonenumber && !telReg.test(item.delivery_supervisor_phonenumber) && !mobileReg.test(item.delivery_supervisor_phonenumber)) {
          isTrue = false
        }
      })
      // 交付主管逻辑处理 end

      if (!isMatch) {
        this.$message.error('交付主管和交付主管手机号必须同时填或者同时不填')
        return false
      }
      if (!isTrue) {
        this.$message.error('交付主管手机号格式不正确')
        return false
      }
      This.loading.page = true
      updateFactory(data, appId).then(res => {
        if (res.code === 0) {
          This.$message.success('修改成功')
          This.$parent.modifyEditState('2')
          // let deliveryClass = res.data.factory_delivery_requirement.delivery_points[0].delivery_score.delivery_class
          // This.appForm.factory_delivery_requirement.delivery_points[0].delivery_score.delivery_class = deliveryClass
          let deliveryScore = res.data.factory_delivery_requirement.delivery_points[0].delivery_score
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_score = deliveryScore
        } else {
          This.$message.error(res.message)
        }
        This.loading.page = false
      })
    },
    goGetPropertyEnum () {
      // 获取所有枚举值列表
      let This = this
      let data = {
        aggregationCode: API.aggregationCode, // 聚合code 要区分正式测试的
        blackholeId: API.blackholeId // 所属黑洞id 要区分正式测试的
      }
      getPropertyEnum(data).then(res => {
        if (res) {
          This.optionsList = res.enumMap
        }
      })
    },
    directorChange (index) {
      let This = this
      if (!This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_name) {
        This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_domain_account = ''
      } else {
        let directorOptions = This.directorOptions
        directorOptions.forEach((item) => {
          if (item.username === This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_name) {
            This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_name = item.nickname
            This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.delivery_supervisor_contacts[index].delivery_supervisor_domain_account = item.username
          }
        })
      }
    },
    gotGetAccount (name) {
      // 获取账户
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        page: 0,
        size: 200,
        name: name
      }
      getAccountsByName(data).then(res => {
        if (res) {
          This.accountOptions = res
        }
      })
    },
    managerChange (res) {
      // 通过id获取其下级
      let This = this
      let accountOptions = This.accountOptions
      let currAccountInfo = null // 当前选中的用户信息
      console.log(res)
      accountOptions.forEach((item) => {
        if (item.username === res) {
          currAccountInfo = item
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager = item.nickname
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_domain_account = item.username
          This.appForm.factory_delivery_requirement.delivery_points[0].delivery_configuration_resource_information.service_center_manager_id = item.id
        }
      })
      if (!currAccountInfo) {
        return false
      }
      console.log('选中信息')
      console.log(currAccountInfo)
      this.goGetAccountsByManagerId(currAccountInfo.id)
    },
    goGetAccountsByManagerId (managerId) {
      // 根据服务中心经理id去获取其下级列表
      let This = this
      getAccountsByManagerId(managerId).then(res => {
        if (res) {
          This.directorOptions = res
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/colors.scss";
@import '../common/static/css/common.scss';
.resValue{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.cha {
  font-size: 14px;
  text-align: right;
  cursor: pointer;
  float: right;
  color: $color-blue-200;
  opacity: 0.8;
  font-weight: normal;
  &:hover {
    opacity: 1;
  }
}
</style>
<style lang="scss">
@import "../../../style/colors.scss";
/*.el-select__tags {*/
  /*white-space: nowrap;*/
  /*overflow: hidden;*/
  /*text-overflow: ellipsis;*/
  /*height: 28px;*/
/*}*/
.fileRow {
  a {
    cursor: pointer;
    color: #5f84ff;
    &:hover {
      text-decoration: underline;
    }
  }
  span:nth-of-type(2) {
    cursor: pointer;
    color: red;
    margin-left: 20px;
    &:hover {
      text-decoration: underline;
    }
  }
}
.el-upload {
  width: 100%;
}
.el-upload-dragger {
  width: 100%;
}
</style>
