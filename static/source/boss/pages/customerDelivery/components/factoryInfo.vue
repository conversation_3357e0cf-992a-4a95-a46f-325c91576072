<template>
  <div>
    <!--工厂基本信息-->
    <div class="factoryFormModel">
      <div class="title">基本信息</div>
      <div class="form" v-if="appForm.baseInfo && optionsList">
        <el-row :span="24">
          <el-col :span="4">工厂名称</el-col>
          <el-col :span="8">
            <el-input v-if="checkPower('factory-baseInfo-factory_name')" v-model="appForm.baseInfo.factory_name" placeholder="请输入" />
            <div v-else class="resValue" >{{appForm.baseInfo.factory_name}}</div>
          </el-col>
          <el-col :span="4">工厂ID</el-col>
          <el-col :span="8">
            <!--<el-input v-if="checkPower('factory-baseInfo-factory_name')" v-model="appForm.id" placeholder="请输入" />-->
            <div class="resValue" >{{appForm.id}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">所属客户名称</el-col>
          <el-col :span="8">
            <el-select
              style="width: 100%;"
              v-if="checkPower('factory-baseInfo-customer_info-customer_name')"
              v-model="appForm.baseInfo.customer_info.customer_name"
              filterable
              remote
              @change="related_customer_change"
              :remote-method="goGetCustomers"
              placeholder="请选择(输入名字搜索)">
              <el-option
                v-for="item in customerOptions"
                :key="item.customerNumber"
                :label="item.customerName"
                :value="item.customerNumber">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.baseInfo.customer_info && appForm.baseInfo.customer_info.customer_name}}</div>
          </el-col>
          <el-col :span="4">工厂类型</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-baseInfo-factory_type')"
              style="width: 100%;"
              v-model="appForm.baseInfo.factory_type"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in optionsList['factory-baseInfo-factory_type']"
                :key="item"
                :label="item"
                :value="item">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.baseInfo.factory_type}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">工厂所在省</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-baseInfo-factory_province')"
              style="width: 100%;"
              v-model="appForm.baseInfo.factory_province_code"
              filterable
              @change="provinceChange"
              placeholder="请选择">
              <el-option
                v-for="item in provinces"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.baseInfo.factory_province}}</div>
          </el-col>
          <el-col :span="4">工厂所在市</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-baseInfo-factory_city')"
              style="width: 100%;"
              v-model="appForm.baseInfo.factory_city"
              filterable
              @change="cityChange"
              placeholder="请选择">
              <el-option
                v-for="item in cities"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.baseInfo.factory_city}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">工厂所在区</el-col>
          <el-col :span="8">
            <el-select
              v-if="checkPower('factory-baseInfo-factory_area')"
              style="width: 100%;"
              v-model="appForm.baseInfo.factory_area"
              filterable
              @change="areaChange"
              placeholder="请选择">
              <el-option
                v-for="item in areas"
                :key="item.code"
                :label="item.name"
                :value="item.code">
              </el-option>
            </el-select>
            <div v-else class="resValue" >{{appForm.baseInfo.factory_area}}</div>
          </el-col>
          <el-col :span="4">工厂详细地址</el-col>
          <el-col :span="8">
            <el-input v-if="checkPower('factory-baseInfo-factory_address')" v-model="appForm.baseInfo.factory_address" placeholder="请输入" />
            <div v-else class="resValue" >{{appForm.baseInfo.factory_address}}</div>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4">对接客服</el-col>
          <el-col :span="8">
            <div class="resValue">{{appForm.extrainfo && appForm.extrainfo.partner5Names}}</div>
          </el-col>
          <el-col :span="4">对接销售</el-col>
          <el-col :span="8">
            <div class="resValue">{{appForm.extrainfo && appForm.extrainfo.partner6Names}}</div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--工厂基本信息 end-->
  </div>
</template>

<script>
import {
  getArea,
  getCustomers,
  getPropertyEnum,
  updateFactory
} from '@/api/customerDelivery'
import API from '../common/config.ts'
export default {
  props: ['pageIsEdit', 'factoryId', 'userRoleCode', 'rolePowers', 'factoryDetail'],
  data () {
    return {
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        baseInfo: { // 工厂信息
          customer_info: {
            customer_name: '', // 所属客户
            customer_code: '' // 所属客户编码
          },
          factory_name: '', // 工厂名称
          factory_province: '', // 省
          factory_city: '', // 市
          factory_area: '', // 区
          factory_address: '', // 详细地址
          factory_type: '' // 工厂类型
        }
      },
      optionsList: null, // 所有枚举的列表
      customerOptions: [], // 客户列表
      provinces: [], // 省
      cities: [], // 市
      areas: [] // 区
    }
  },
  created () {
    console.log('数据')
    console.log(this.appForm)
    this.goGetArea(1)
    this.goGetPropertyEnum()
    this.appForm = this.factoryDetail
    if (this.appForm.baseInfo.factory_province_code) {
      this.goGetArea(2, this.appForm.baseInfo.factory_province_code)
    }
    if (this.appForm.baseInfo.factory_city_code) {
      this.goGetArea(3, this.appForm.baseInfo.factory_city_code)
    }
  },
  methods: {
    checkPower (code) {
      // 表单状态：pageIsEdit true 编辑状态 false 查看状态
      let pageIsEdit = this.pageIsEdit
      // console.log('============================================')
      // console.log('当前用户所属角色')
      // console.log(this.userRoleCode)
      // console.log('当前用户所属角色含有修改权限的字段列表')
      // console.log(this.rolePowers)
      // console.log('当前字段全路径code')
      // console.log(code)
      let result = true
      if (pageIsEdit) {
        let hasPower = false
        this.rolePowers.forEach((item) => {
          if (item.property_code === code) {
            hasPower = true
            return false
          }
        })
        if (hasPower) {
          result = true // 有修改权限
        } else {
          result = false // 没有修改权限
        }
      } else {
        result = false // 没有修改权限
      }
      return result
    },
    edit () {

    },
    store () {
      // 编辑保存
      let This = this
      let appId = this.appForm.id // 修改数据id
      let data = this.appForm
      console.log(data)
      if (!data.baseInfo.customer_info.customer_code || !data.baseInfo.factory_name || !data.baseInfo.factory_province || !data.baseInfo.factory_city || !data.baseInfo.factory_area || !data.baseInfo.factory_address || !data.baseInfo.factory_type) {
        this.$message.error('请将工厂基本信息填写完整')
        return false
      }
      data.aggregationId = appId
      data.id = appId
      updateFactory(data, appId).then(res => {
        if (res.code === 0) {
          This.$message.success('修改成功')
          This.$parent.modifyEditState('1')
        } else {
          This.$message.error(res.message)
        }
      })
    },
    provinceChange (res) {
      // console.log('切换省')
      this.cities = []
      this.areas = []
      this.appForm.baseInfo.factory_city = ''
      this.appForm.baseInfo.factory_city_code = ''
      this.appForm.baseInfo.factory_area = ''
      this.appForm.baseInfo.factory_area_code = ''
      let provinces = this.provinces
      provinces.forEach((item) => {
        if (item.code === res) {
          this.appForm.baseInfo.factory_province_code = item.code
          this.appForm.baseInfo.factory_province = item.name
        }
      })
      this.goGetArea(2, res)
    },
    cityChange (res) {
      // console.log('切换市')
      this.areas = []
      this.appForm.baseInfo.factory_area = ''
      this.appForm.baseInfo.factory_area_code = ''
      let cities = this.cities
      cities.forEach((item) => {
        if (item.code === res) {
          this.appForm.baseInfo.factory_city_code = item.code
          this.appForm.baseInfo.factory_city = item.name
        }
      })
      this.goGetArea(3, res)
    },
    areaChange (res) {
      // console.log('切换区')
      let areas = this.areas
      areas.forEach((item) => {
        if (item.code === res) {
          this.appForm.baseInfo.factory_area_code = item.code
          this.appForm.baseInfo.factory_area = item.name
        }
      })
      // this.goGetArea(3, res)
    },
    goGetArea (level, parentCode) {
      /**
       * level: 1省/直辖市,2地级市,3区县,4镇/街道
       * parentCode: 父级地区编码
       * position: 1 搜索部分 2 添加、修改表单部分
       * **/
      let This = this
      let data = {
        level: level,
        parentCode: parentCode
      }
      getArea(data).then(res => {
        if (res.code === 200) {
          if (level === 1) {
            // 省
            This.provinces = res.data
          } else if (level === 2) {
            // 市
            This.cities = res.data
          } else if (level === 3) {
            // 区
            This.areas = res.data
          }
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    goGetPropertyEnum () {
      // 获取所有枚举值列表
      let This = this
      let data = {
        aggregationCode: API.aggregationCode, // 聚合code 要区分正式测试的
        blackholeId: API.blackholeId // 所属黑洞id 要区分正式测试的
      }
      getPropertyEnum(data).then(res => {
        if (res) {
          This.optionsList = res.enumMap
        }
      })
    },
    goGetCustomers (name) {
      // 获取用户客户中心用户列表
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        customerName: name,
        rowCount: 50,
        pageNo: 1
      }
      getCustomers(data).then(res => {
        if (res.code === '0000') {
          This.customerOptions = res.result.rows
        } else {
          This.$message.error(res.message)
        }
      })
    },
    related_customer_change (res) {
      console.log(res)
      let customerOptions = this.customerOptions
      customerOptions.forEach((item) => {
        if (item.customerNumber === res) {
          this.appForm.baseInfo.customer_info.customer_code = item.customerNumber
          this.appForm.baseInfo.customer_info.customer_name = item.customerName
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/colors.scss";
@import '../common/static/css/common.scss';
.resValue {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
<style lang="scss">
@import "../../../style/colors.scss";
</style>
