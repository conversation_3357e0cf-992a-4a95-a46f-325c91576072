<template>
  <div>
    <el-table v-loading="loading.table" :data="tableData" border fit highlight-current-row>
      <template slot="empty">
        <div class="empty">
          <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
        </div>
      </template>
      <el-table-column align="center" prop="id" label="联系人ID"></el-table-column>
      <el-table-column align="center" prop="key_contact_name" label="姓名"></el-table-column>
      <el-table-column align="center" prop="key_contact_type" label="类型"></el-table-column>
      <el-table-column align="center" prop="key_contact_phonenumber" label="联系方式">
        <template v-slot="{ row }">
          <div :key="row.id">
            <el-tooltip content="点击查看完整号码" placement="bottom">
              <span class="cover-cell" @click="showTextAll($event, row.key_contact_phonenumber, row.id)">{{
                coverMobileAndLandline(row.key_contact_phonenumber) }}
              </span>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="key_contact_influence_description" label="影响力描述"></el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button @click="modifyApp(scope.row, scope.$index)" type="text" size="small">编辑</el-button>
          <el-button @click="deleteApp(scope.row, scope.$index)" type="text" size="small">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="c_pagination">
      <pagination v-show="total > 0" :total="total" align="right" :page.sync="listQueryInfo.current"
        :limit.sync="listQueryInfo.pageSize" layout=" total, prev, pager, next, jumper" @pagination="pageClick" />
    </div>
    <!--新建联系人弹出框-->
    <el-dialog width="580px" title="新增交付联系人" class="dialogClass" :visible.sync="showAddDialog"
      :close-on-click-modal="false">
      <div class="ruleset">
        <div class="rulesetcont">
          <el-row :span="24">
            <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>姓名：</el-col>
            <el-col :span="16">
              <el-input v-model="appForm.terminal_delivery_key_contacts.key_contact_name" placeholder="请输入" />
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>手机号：</el-col>
            <el-col :span="16">
              <el-input v-model="appForm.terminal_delivery_key_contacts.key_contact_phonenumber" placeholder="请输入" />
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="6" :offset="1" class="dialogTitle"><i>*</i>联系人类型：</el-col>
            <el-col :span="16">
              <el-select style="width: 100%;" v-model="appForm.terminal_delivery_key_contacts.key_contact_type" filterable
                placeholder="请选择">
                <el-option
                  v-for="item in (optionsList && optionsList['factory-terminal_delivery_key_contacts-key_contact_type'])"
                  :key="item" :label="item" :value="item">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="6" :offset="1" class="dialogTitle">影响力描述：</el-col>
            <el-col :span="16">
              <el-input type="textarea" v-model="appForm.terminal_delivery_key_contacts.key_contact_influence_description"
                placeholder="请简要描述联系人情况" />
            </el-col>
          </el-row>
        </div>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right;">
            <el-button size="medium" @click="closeAddDialog" style="margin-left: 20px;">关闭</el-button>
            <el-button size="medium" :loading="loading.create" @click="storeAddDialog" type="primary">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--新建联系人弹出框 end-->
  </div>
</template>

<script>
import {
  getFactoryDetail,
  createTerminalDeliveryKeyContacts,
  updateTerminalDeliveryKeyContacts,
  deleteTerminalDeliveryKeyContacts,
  uploadSensitiveData
} from '@/api/customerDelivery'
import { coverMobileAndLandline } from '@/utils/index'
import Pagination from '@/components/Pagination'
export default {
  props: ['optionsList', 'factoryId', 'userRoleCode', 'factoryDetail'],
  data() {
    return {
      loading: {
        page: true,
        table: true,
        role: false,
        property: false,
        create: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      search: { // 搜索值
        role: '',
        property: ''
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        terminal_delivery_key_contacts: [
          {
            key_contact_name: '',
            key_contact_phonenumber: '',
            key_contact_type: '', // 联系人类型
            key_contact_influence_description: '' // 影响力描述
          }
        ]
      },
      terminal_delivery_key_contacts: null, // 末端联系人列表
      total: 0,
      tableData: [],
      provinceOptions: [
        {
          id: 1,
          name: '北京市'
        },
        {
          id: 2,
          name: '河北省'
        },
        {
          id: 3,
          name: '河南省'
        },
        {
          id: 4,
          name: '上海市'
        }
      ],
      showAddDialog: false,
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
    Pagination
  },
  created() {
    this.tableData = this.factoryDetail.terminal_delivery_key_contacts || []
    this.loading.table = false
    this.loading.page = false
  },
  methods: {
    coverMobileAndLandline(phone) {
      return coverMobileAndLandline(phone)
    },
    showTextAll($event, text, key) {
      uploadSensitiveData({ dataType: '客户中心交付联系人', dataPrimaryKey: key, operationFieldName: '联系方式', operationUserName: window.CUR_DATA.user.name })
      const oldDom = $event.target
      const $parent = oldDom.parentNode
      const newDom = document.createElement(oldDom.tagName)
      newDom.innerText = text
      $parent.replaceChild(newDom, oldDom)
    },
    openRuleDialog() {
      // 打开添加数据窗口
      this.appForm = { // 初始化form应用的内容
        id: '',
        terminal_delivery_key_contacts: {
          key_contact_name: '',
          key_contact_phonenumber: '',
          key_contact_type: '', // 联系人类型
          key_contact_influence_description: '' // 影响力描述
        }
      }
      this.currAppInfo = null
      this.showAddDialog = true
    },
    closeAddDialog() {
      this.showAddDialog = false
    },
    storeAddDialog() {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp(row, index) {
      console.log('修改数据')
      this.appForm = { // 初始化form应用的内容
        id: row.id,
        terminal_delivery_key_contacts: row
      }
      this.currAppInfo = row
      this.showAddDialog = true
    },
    deleteApp(row, index) {
      let This = this
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let data = {
          aggregationId: This.factoryId, // 聚合id
          id: This.factoryId, // 聚合id
          terminal_delivery_key_contacts: row.id // 属性数据id
        }
        deleteTerminalDeliveryKeyContacts(data).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(res.message)
          }
        })
      }).catch(() => {
        console.log('取消删除')
      })
    },
    pageClick(page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
    },
    goGetApps() {
      // 【1】获取数据详情从中拿到联系人列表
      let This = this
      This.loading.table = false
      This.loading.page = false
      let factoryId = this.factoryId
      getFactoryDetail(factoryId).then(res => {
        if (res) {
          if (res.terminal_delivery_key_contacts) {
            This.tableData = res.terminal_delivery_key_contacts
          }
        }
        This.loading.table = false
        This.loading.page = false
      })
    },
    gocreateApps() {
      // 新建、编辑数据
      let This = this
      let appForm = this.appForm
      let formdata = appForm.terminal_delivery_key_contacts
      if (!formdata.key_contact_name || !formdata.key_contact_phonenumber || !formdata.key_contact_type) {
        this.$message.error('请将信息填写完整')
        return false
      }
      let mobileReg = /^1\d{10}$/
      let telReg = /^0\d{2,3}-?\d{7,8}$/
      if (!telReg.test(formdata.key_contact_phonenumber) && !mobileReg.test(formdata.key_contact_phonenumber)) {
        this.$message.error('手机号格式不正确')
        return false
      }
      let appId = this.appForm.id // 修改数据id
      this.loading.create = true
      let data = {
        aggregationId: This.factoryId,
        id: This.factoryId,
        terminal_delivery_key_contacts: [appForm.terminal_delivery_key_contacts]
      }
      if (appId) {
        // 修改数据
        updateTerminalDeliveryKeyContacts(data, appId).then(res => {
          if (res.code === 0) {
            This.$message.success('修改成功')
            // 修改成功更新数据列表
            This.goGetApps()
            This.showAddDialog = false
          } else {
            This.$message.error(res.message)
          }
          This.loading.create = false
        })
      } else {
        // 添加数据
        createTerminalDeliveryKeyContacts(data).then(res => {
          if (res.code === 0) {
            This.$message.success('创建成功')
            // 新增成功创建数据列表
            This.tableData = res.data.terminal_delivery_key_contacts
            This.showAddDialog = false
          } else {
            This.$message.error(res.message)
          }
          This.loading.create = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "../../../style/colors.scss";
@import '../common/static/css/common.scss';
</style>
<style lang="scss">
@import "../../../style/colors.scss";
</style>
