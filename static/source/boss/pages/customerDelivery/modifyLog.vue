<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="fifter">
        <el-row :span="24" class="searchBox" style="margin-bottom: 0;">
          <el-row :span="24" style="margin-bottom: 0;">
            <el-col :span="2" class="dialogTitle">类型：</el-col>
            <el-col :span="6" >
              <el-select
                style="width: 100%;"
                v-model="search.type"
                @change="typeChange"
                clearable
                placeholder="请选择(输入名字搜索)">
                <el-option
                  v-for="item in typeOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-col>
            <!--<el-col :span="3" class="dialogTitle">客户名称：</el-col>-->
            <!--<el-col :span="13" >-->
            <!--<el-select-->
            <!--style="width: 100%;"-->
            <!--class="customerName"-->
            <!--v-model="search.customer_name"-->
            <!--clearable-->
            <!--filterable-->
            <!--multiple-->
            <!--remote-->
            <!--@change="related_customer_change"-->
            <!--:remote-method="goGetCustomers"-->
            <!--placeholder="请选择(输入名字搜索)">-->
            <!--<el-option-->
            <!--v-for="item in customerOptions"-->
            <!--:key="item.customerName"-->
            <!--:label="item.customerName"-->
            <!--:value="item.customerName">-->
            <!--</el-option>-->
            <!--</el-select>-->
            <!--</el-col>-->
            <el-col :span="2" class="dialogTitle">修改人：</el-col>
            <el-col :span="6" >
              <el-input
                placeholder="请输入修改人"
                v-model="search.modifier"
                clearable
              />
            </el-col>
            <el-col v-if="search.type !== 'service_center_area'" :span="2" class="dialogTitle">工厂名称：</el-col>
            <el-col v-if="search.type !== 'service_center_area'" :span="6" >
              <el-select
                style="width: 100%;"
                v-model="search.factory_id"
                clearable
                filterable
                remote
                @change="related_factory_change"
                :remote-method="goGetFactory"
                placeholder="请选择(输入名字搜索)">
                <el-option
                  v-for="item in factoryOptions"
                  :key="item.id"
                  :label="item.baseInfo.factory_name"
                  :value="item.id">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row :span="24" style="margin-bottom: 0;">
            <el-col :span="2" class="dialogTitle">属性：</el-col>
            <el-col :span="6" >
              <el-select
                style="width: 100%;"
                class="companyOptions"
                v-model="search.propertyName"
                ref="appFormDepartSelect1"
                clearable
                @clear="setPropertyEmpty"
                placeholder="请选择属性">
                <el-option
                  :value="search.propertyName"
                  style="height: auto; padding:0;"
                >
                  <el-tree
                    :data="postOptions2"
                    :props="defaultProps"
                    show-checkbox
                    node-key="id"
                    class="departTree"
                    :load="loadNode"
                    :check-strictly="false"
                    lazy
                    ref="tree1"
                    @check="handleCheckClickAppForm1"
                  ></el-tree>
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="2" class="dialogTitle">操作：</el-col>
            <el-col :span="6" >
              <el-select
                style="width: 100%;"
                v-model="search.opCode"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in opOptions"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="6" :offset="2" style="text-align: right;">
              <el-button @click="searchList" icon="el-icon-search" size="small" type="primary">查询</el-button>
              <el-button @click="searchReset" icon="el-icon-close" size="small" type="info">清空</el-button>
            </el-col>
          </el-row>
        </el-row>
      </div>
      <div class="c_tables">
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column align="center" prop="id" width="100" label="id"></el-table-column>
          <el-table-column align="center" prop="changeBy" width="120" label="修改人"></el-table-column>
          <el-table-column align="center" prop="changeTime" width="90" label="修改时间"></el-table-column>
          <el-table-column align="center" prop="changeFieldChName" width="300" label="属性"></el-table-column>
          <el-table-column align="center" prop="oldValue" label="修改前">
            <template slot-scope="scope">
              {{scope.row.oldValue}}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="newValue" label="修改后">
            <template slot-scope="scope">
              {{scope.row.newValue}}
            </template>
          </el-table-column>
          <!--<el-table-column align="center" prop="id" label="描述"></el-table-column>-->
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getFactory,
  getDiffRecordList,
  getAggregationProperty,
  getCustomers
} from '@/api/customerDelivery'
import API from './common/config.ts'
import Pagination from '@/components/Pagination'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        role: false,
        property: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      search: { // 搜索值
        customer_name: '',
        factory_name: '',
        factory_id: '',
        modifier: '',
        propertyCode: '',
        propertyName: '',
        type: '',
        typeChild: '',
        opCode: ''
      },
      total: 0,
      tableData: [],
      customerOptions: [], // 客户列表
      factoryOptions: [], // 工厂列表
      selectPage: { // 搜索页数
        app2: 1,
        pageSize: 100
      },
      selectKey: { // 储存的搜索关键字
        app2: ''
      },
      postOptions2: [ // 属性选项
      ],
      defaultProps: {
        children: 'children',
        label: 'propertyName'
      },
      typeOptions: [ // 类型
        {
          id: 'factory',
          name: '工厂'
        },
        {
          id: 'service_center_area',
          name: '区域服务中心范围'
        }
      ],
      opOptions: [ // 操作
        {
          code: 'create',
          name: '创建'
        },
        {
          code: 'update',
          name: '修改'
        },
        {
          code: 'delete',
          name: '删除'
        }
      ],
      typeChildOptions: [], // 子类型
      currAggregationId: API.aggregationId, // 当前选中的工厂聚合id
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetApps()
    this.goGetAllPostSearch2()
    this.goGetFactory()
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.customer_name || This.search.factory_name || This.search.factory_id || This.search.modifier || This.search.propertyCode || This.search.type || This.search.typeChild || This.search.opCode) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  filters: {
    /**
     * @param {string} date
     * 格式化后台返回的此种格式的时间
     * 2021-07-28T01:53:53.000+0000
     */
    afterDateformat: function (date) {
      if (date && date.indexOf('T')) {
        const dateee = new Date(date).toJSON()
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      } else {
        return ''
      }
    }
  },
  methods: {
    goGetFactory (name) {
      // 获取工厂列表
      let This = this
      let conditionList = []
      if (!name) {
        name = ''
      } else {
        let unit = {
          'compareData': name.trim(),
          'operateChar': 'nestedLike',
          'searchField': 'baseInfo.factory_name'
        }
        conditionList.push(unit)
      }
      let data = {
        pageNum: 1,
        pageSize: 50,
        code: 'factory_batch',
        aggregationCode: 'factory',
        aggregationType: 'factory',
        conditionList: conditionList
      }
      getFactory(data).then(res => {
        if (res.code === 0) {
          This.factoryOptions = res.data.result
        } else {
          This.$message.error(res.message)
        }
      })
    },
    related_factory_change (res) {
      let factoryOptions = this.factoryOptions
      factoryOptions.forEach((item) => {
        if (item.id === res) {
          this.search.factory_id = item.id
          this.search.factory_name = item.baseInfo.factory_name
        }
      })
    },
    typeChange (id) {
      let This = this
      if (id === 'factory') {
        // 选中工厂
        This.currAggregationId = API.aggregationId
      } else {
        // 选中区域服务中心范围
        This.currAggregationId = API.serviceCenterAreaAggregationId
      }
      This.search.factory_name = ''
      This.search.factory_id = ''
      This.search.propertyCode = ''
      This.search.propertyName = ''
      This.goGetAllPostSearch2()
    },
    goGetAllPostSearch2 (name) {
      let This = this
      // 触发搜索时走这里
      if (!name) {
        name = ''
        This.selectKey.app2 = ''
      } else {
        // 按照名字搜索的时候不用分页
        This.selectKey.app2 = name
      }
      // 触发搜索说明搜索值改变了，分页要从第一页开始
      This.selectPage.app2 = 1
      This.goGetAllPost2()
    },
    goGetAllPost2 () {
      // 根据视图id去获取属性列表
      let This = this
      let name = This.selectKey.app2
      if (This.loading.app2) {
        // 防止短时间重复加载
        return false
      }
      let data = {
        name: name,
        page: 0,
        size: 200,
        parentId: -1,
        aggregationId: This.currAggregationId // 所属聚合id 要区分正式测试的,
      }
      This.loading.app2 = true
      getAggregationProperty(data).then(res => {
        setTimeout(function () {
          This.loading.app2 = false
        }, 1000)
        if (res.content.length > 0) {
          // 第一页时重新赋值
          This.postOptions2 = res.content
          This.treeData = res.content
          // 下拉分页
          console.log('获取到的值')
          console.log(This.postOptions2)
        }
      })
    },
    loadNode (node, resolve) {
      // 加载子节点
      let This = this
      console.log(node)
      if (node.id === 0) {
        return resolve([])
      }
      let data = {
        aggregationId: This.aggregationId,
        parentId: node.data.id
      }
      getAggregationProperty(data).then(res => {
        if (res) {
          This.treeData = This.treeData.concat(res.content)
          return resolve(res.content)
        } else {
          return resolve([])
        }
      }).catch(() => {
        return resolve([])
      })
    },
    handleCheckClickAppForm1 (data) {
      let propertyNodes = this.$refs.tree1.getCheckedNodes()
      // 搜索选择
      let treeData = this.treeData
      let newIds = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newNamesArr = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newNames = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newCodesArr = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newCodes = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      propertyNodes.forEach((item) => {
        newNames = []
        newCodes = []
        getParentIds(item)
      })
      // 通过node的parentId找到其父级的id，并储存起来
      function getParentIds (node) {
        let id = node.id
        let code = node.propertyCode
        let name = node.propertyName
        let parentId = node.parentId
        if (!newIds.includes(id)) {
          newNames.push(name)
          newCodes.push(code)
          if (parentId === -1) {
            newNamesArr.push(newNames[0]) // 名字只取最后一级显示
            newCodesArr.push(newCodes.reverse().join('-'))
            return false
          } else {
            treeData.forEach((item) => {
              if (item.id === parentId) {
                getParentIds(item)
              }
            })
          }
        } else {
          // 存在则其父级都寻找过了不需要再寻找了
          newNamesArr.push(newNames[0]) // 名字只取最后一级显示
          newCodesArr.push(newCodes.reverse().join('-'))
          return false
        }
      }
      console.log(newNamesArr)
      console.log(newCodesArr)
      this.$set(this.search, 'propertyCode', newCodesArr.join(','))
      this.$set(this.search, 'propertyName', newNamesArr.join(','))
    },
    handleCheckClickAppForm1_dan (data) {
      // 单选代码
      // 搜索选择
      let propertyId = data.id // 属性id
      let propertyName = data.propertyName // 属性名字
      let arr = []
      arr.push(propertyId)
      this.$refs.tree1.setCheckedKeys([])
      this.$refs.tree1.setCheckedKeys(arr)
      this.$set(this.search, 'propertyName', propertyName)
      this.$refs.appFormDepartSelect1.blur()

      let propertyNodes = this.$refs.tree1.getCheckedNodes()
      let treeData = this.treeData
      let newIds = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newNames = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      let newCodes = [] // 新数组的组合(为了实现子级选中父级必定选中的效果)
      propertyNodes.forEach((item) => {
        getParentIds(item)
      })
      // 通过node的parentId找到其父级的id，并储存起来
      function getParentIds (node) {
        let id = node.id
        let code = node.propertyCode
        let name = node.propertyName
        let parentId = node.parentId
        if (!newIds.includes(id)) {
          newIds.push(id)
          newNames.push(name)
          newCodes.push(code)
          if (parentId === -1) {
            return false
          } else {
            treeData.forEach((item) => {
              if (item.id === parentId) {
                getParentIds(item)
              }
            })
          }
        } else {
          // 存在则其父级都寻找过了不需要再寻找了
          return false
        }
      }
      this.$refs.tree1.setCheckedKeys(newIds)
      newCodes = newCodes.reverse()
      // let subCode = 'factory' + '-' + newCodes.join('-')
      let subCode = newCodes.join('-')
      this.$set(this.search, 'propertyCode', subCode)
    },
    goGetCustomers (name) {
      // 获取用户客户中心用户列表
      let This = this
      if (!name) {
        name = ''
      }
      let data = {
        customerName: name,
        rowCount: 50,
        pageNo: 1
      }
      getCustomers(data).then(res => {
        if (res.code === '0000') {
          This.customerOptions = res.result.rows
        } else {
          This.$message.error(res.message)
        }
      })
    },
    related_customer_change (res) {
      console.log(res)
      let customerOptions = this.customerOptions
      customerOptions.forEach((item) => {
        if (item.customerNumber === res) {
          this.appForm.baseInfo.customer_info.customer_code = item.customerNumber
          this.appForm.baseInfo.customer_info.customer_name = item.customerName
        }
      })
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    goGetApps () {
      // 【1】获取数据列表
      let This = this
      console.log('搜索值')
      console.log(This.search)
      let data = {
        pageNum: This.listQueryInfo.current,
        pageSize: This.listQueryInfo.pageSize,
        changeBy: This.search.modifier,
        changeFieldName: This.search.propertyCode,
        aggregationId: This.search.factory_id,
        blackHoleId: API.blackholeId,
        aggregationType: This.search.type,
        op: This.search.opCode
      }
      getDiffRecordList(data).then(res => {
        console.log('返回值')
        console.log(res)
        if (res.code === 0) {
          This.tableData = res.data.result
          This.total = res.data.total
        } else {
          This.$message.error(res.message)
        }
        This.loading.table = false
        This.loading.page = false
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = {
        customer_name: '',
        factory_name: '',
        factory_id: '',
        modifier: '',
        propertyCode: '',
        propertyName: '',
        type: '',
        typeChild: '',
        opCode: ''
      }

      this.$refs.tree1.setCheckedKeys([])
      if (!state) {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    setPropertyEmpty () {
      this.search = {
        propertyName: ''
      }
      this.$refs.tree1.setCheckedKeys([])
    },
    gotGetRole (key) {
      console.log('获取值')
      console.log(key)
      this.loading.role = true
    },
    gotGetProperty (key) {
      console.log('获取值')
      console.log(key)
      this.loading.property = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import './common/static/css/common.scss'; /*引入公共样式*/

.searchBox {
  padding-left: 10px !important;
  padding-right: 50px !important;
  margin-top: 0 !important;
  .el-col {
    margin-bottom: 10px;
  }
}
</style>
<style lang="scss" >
.customerName {
  .el-select__tags {
    white-space: nowrap;
    overflow-x: auto;
    > span {
      max-width: 80% !important;
      display: inline-block !important;
      overflow: hidden !important;
      overflow-x: auto !important;
      &::-webkit-scrollbar {
        height: 6px;
      }
      /* 滚动槽 */
      &::-webkit-scrollbar-track {
        border-radius: 10px;
      }
      /* 滚动条滑块 */
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(0, 0, 0, 0.1);
      }
    }
  }
}
</style>
