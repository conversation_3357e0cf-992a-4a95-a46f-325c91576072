<template>
  <div class="order-hand">
    <el-card class="box-card">
      <el-form ref="ruleForm" :rules="rules" :model="searchForm" style="width: 100%" label-suffix=":" label-width="80px">
        <el-row :gutter="40">
          <el-col :span="10">
            <el-form-item label="采购员" prop="purchaseGroup">
              <el-select
                v-if="setPower(userRole)"
                v-model="searchForm.purchaseGroup"
                filterable
                default-first-option
                clearable
                style="width:100%"
                placeholder="请选择采购员"
                >
                <el-option
                  v-for="item in purchaseList"
                  :key="item.groupCode+item.userName"
                  :label="item.groupCode+ ' '+item.userName"
                  :value="item.groupCode">
                </el-option>
              </el-select>
              <SelectPurchaseGroup v-else :data.sync="searchForm.purchaseGroup" :multiple="false"/>
            </el-form-item>
          </el-col>
          <el-col :span="10">
          <el-form-item label="SKU" prop="skuNo">
              <el-input
                v-model="searchForm.skuNo"
                filterable
                clearable
                style="width:100%"
                placeholder="最多支持100个SKU按空格隔开搜索"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="10">
            <el-form-item label="公司" prop="company">
              <el-select
                v-model="searchForm.company"
                filterable
                default-first-option
                clearable
                style="width:100%"
                placeholder="请选择公司"
              >
                <el-option
                  v-for="item in companyFactoryList"
                  :key="item.companyCode"
                  :label="item.companyCode+' '+item.companyName"
                  :value="item.companyCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="工厂" prop="factory">
              <el-select
                v-model="searchForm.factory"
                filterable
                default-first-option
                clearable
                style="width:100%"
                placeholder="请选择工厂"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.factoryCode"
                  :label="item.factoryCode+' '+item.factoryName"
                  :value="item.factoryCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="40">
          <el-col :span="10">
            <el-form-item label="供应商" prop="supplierNo">
              <SelectSupplier
                clearable
                :data.sync="searchForm.supplier"
                @change="handleChange('supplier', $event)"
                />
            </el-form-item>
          </el-col>
          <el-col :span="10">
          <el-form-item label="库存地点" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                default-first-option
                clearable
                style="width:100%"
                placeholder="请选择库存地点"
              >
                <el-option
                  v-for="item in warehouseListFilterByFactory"
                  :key="item.factoryCode + item.warehouseLocationCode"
                  :label="`${item.warehouseLocationCode} ${item.warehouseLocationName}`"
                  :value="item.warehouseLocationCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" :style="{display: 'flex', justifyContent: 'center'}">
            <el-button
              type="primary"
              style="width: 80px"
              :loading="searchLoading"
              @click="handleSearch"
            >
              下单
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-dialog title="手动下单结果" :visible.sync="dialogTableVisible">
      <div>成功行数：{{ result.num && result.num.success }}； 失败行数：{{ result.num && result.num.fail }}</div>
      <el-table :data="result.row">
        <el-table-column property="externalOrderNo" label="关联订单号"></el-table-column>
        <el-table-column property="externalOrderItemNo" label="关联订单行号"></el-table-column>
        <el-table-column property="orderNo" label="采购订单号">
          <template slot-scope="scope">
            <el-link @click="toDetail(scope.row)"
                 type="primary">{{ scope.row.orderNo }}</el-link>
          </template>
        </el-table-column>
        <el-table-column property="orderType" label="采购订单类型"></el-table-column>
        <el-table-column property="msg" label="结果"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { safeRun } from '@/utils/index'
import { triggerOrder, getUserInfo } from '@/api/mrp'
import SelectPurchaseGroup from './components/selectPurchaseGroup.vue'
import { setPower } from './utils'

export default {
  name: 'orderHand',
  components: { SelectSupplier, SelectPurchaseGroup },
  data () {
    return {
      factoryList: [],
      warehouseListFilterByFactory: [],
      searchForm: {
        company: '',
        factory: '',
        purchaseGroup: '',
        skuNo: '',
        supplierNo: '',
        supplier: {},
        position: ''
      },
      searchLoading: false,
      rules: {
        purchaseGroup: [
          { required: true, message: '采购员必填', trigger: 'blur' }
        ],
        company: [
          { required: true, message: '公司必填', trigger: 'blur' }
        ],
        factory: [
          { required: true, message: '工厂必填', trigger: 'blur' }
        ]
      },
      dialogTableVisible: false,
      result: {}
    }
  },
  async created () {
    let pList = []
    if (this.companyInfoList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryCompanyInfoList'))
    }
    if (!Object.keys(this.dictList).length) {
      pList.push(this.$store.dispatch('orderCommon/queryDictList'))
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (this.purchaseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (!this.userRole || this.userRole.length === 0) {
      pList.push(this.$store.dispatch('getUserRole'))
    }
    await Promise.all(pList)
    await this.getUserCompany()
  },
  computed: {
    ...mapState({
      companyInfoList: state => state.mrp.companyInfoList,
      dictList: state => state.orderCommon.dictList || {},
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      purchaseList: state => state.orderPurchase.purchaseList,
      warehouseList: state => state.orderPurchase.warehouseList,
      userRole: state => state.userRole
    })
  },
  watch: {
    'searchForm.company': function (newValue) {
      this.factoryList = (this.companyFactoryList.find(item => item.companyCode === newValue) || {}).factoryList
      this.searchForm.factory = ''
    },
    'searchForm.factory': function (newVal) {
      this.warehouseListFilterByFactory = this.warehouseList.filter(w => w.factoryCode === newVal)
      this.searchForm.position = ''
    },
    factoryList: {
      handler: function (newValue) {
        if (newValue.length === 1) {
          this.searchForm.factory = this.factoryList[0].factoryCode
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    setPower,
    async getUserCompany () {
      try {
        const res = await getUserInfo({
          username: window.CUR_DATA.user && window.CUR_DATA.user.name
        })
        let defaultCompany = (this.companyInfoList.find(item => item.subCompanyId === res.subCompany) || {}).code
        if (defaultCompany) {
          this.searchForm.company = defaultCompany
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleChange (type, event) {
      if (type === 'supplier') {
        this.searchForm.supplierNo = event.supplierNo
      }
    },
    formatParams(params) {
      let form = { ...params };
      form.skuNo = safeRun(() =>
        form.skuNo
          .split(/\s/).filter((e) => e)
      )
      delete form.supplier
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.skuNo.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
      })
      return ret
    },
    handleSearch () {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          try {
            let data = this.formatParams(this.searchForm);
            if (!this.validate(data)) return
            this.searchLoading = true
            data = {
              ...data,
              supplier: this.searchForm.supplierNo
            }
            const res = await triggerOrder(data)
            console.log(res);
            if (res.code === 200) {
              this.result = res.data
              this.dialogTableVisible = true
            } else {
              this.$message.error(res.msg)
            }
            this.searchLoading = false
          } catch (error) {
            this.searchLoading = false
            console.log(error);
          }
        } else {
          return false;
        }
      })
    },
    toDetail (row) {
      this.$router.push(`/orderPurchase/detail/${row.orderNo}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.order-hand {
  padding: 20px;
}
</style>
