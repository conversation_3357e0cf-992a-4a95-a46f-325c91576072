<template>
  <div class="order-transfer" v-loading="tableLoading"
       element-loading-text="加载中，请不要刷新浏览器">
    <el-dialog
      title="提示"
      :visible.sync="downloadVisible"
      width="360px"
      center>
      <h4 style="text-align: center">请选择下载数据范围</h4>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleDownExcel(false)">下载全部</el-button>
        <el-button type="primary" @click="handleDownExcel(true)">下载订货日行</el-button>
      </span>
    </el-dialog>
    <vxe-grid
      column-key
      border
      resizable
      keep-source
      show-overflow
      show-header-overflow
      ref="orderTransferGrid"
      :height="tableHeight"
      id="transfer_grid"
      size="mini"
      row-id="id"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="transferList"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'cell', showIcon: false, showStatus: true}"
      :checkbox-config="{checkMethod:checkMethod}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
      @cell-dblclick="cellClick"
      highlight-hover-row
      :scroll-y="{gt: 20}"
      :row-class-name="genRowColor"
    >
      <template v-slot:toolbar_buttons>
        <el-form :model="searchForm"
                 ref="searchForm"
                 :rules="validateRules"
                 label-suffix=":"
                 label-width="90px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="供应商" prop="supplier" style="margin: 0;">
                <SelectSupplier
                  clearable
                  :data.sync="searchForm.supplier"
                  @change="handleChange('supplier', $event)"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-button
                type="primary"
                style="width: 100px"
                :loading="batchLoading"
                @click="handleBatch"
              >
                批量分配
              </el-button>
              <el-button
                style="width: 100px"
                @click="autoBatch"
              >
                自动分配
              </el-button>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单类型"
                            prop="itemType"
                            style="margin: 0;">
                <el-select v-model="searchForm.itemType"
                           filterable
                           default-first-option
                           style="width:100%">
                  <el-option v-for="item in options"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-button
                type="primary"
                style="width: 100px"
                @click="handleBatchType"
              >
                批量分配
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </template>

      <template v-slot:toolbar_tools>
        <el-button type="primary"
                   style="width: 80px"
                   @click="transferPO"
                   :disabled="disabled">
          生成PO
        </el-button>
        <el-button type="primary"
                   style="width: 80px"
                   :disabled="disabling"
                   @click="downloadVisible = true">
          下载
        </el-button>
      </template>

      <template v-slot:orderNo_default="{ row }">
        <el-popover v-if="row.orderNo"
                    placement="top"
                    trigger="hover">
          <span v-for="(no, index) in handleOrderNo(row.orderNo)" :key="no">
            <el-link @click="toDetail(no)" type="primary">{{ no }}
              <span v-if="index < (handleOrderNo(row.orderNo).length -1)" style="padding: 0 5px;">
               |
              </span>
            </el-link>
          </span>
          <span slot="reference">
            {{ row.orderNo }}
          </span>
        </el-popover>
      </template>

      <template v-slot:itemType_default="{ row }">
        <el-select
          v-model="row.itemType"
          @change="changeType(row)"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
            :disabled="!!row.prType ? !['Z001', 'Z005', 'Z003'].includes(item.value) : false"
          >
          </el-option>
        </el-select>
      </template>
      <template v-slot:ifUseBiAmountStrategy_default="{ row }">
      <span v-if="row.itemType !== 'Z003' && row.prType === 0 && row.biPageBeginAmountList && row.biPageBeginAmountList.length > 0">
        <el-select
          v-model="row.ifUseBiAmountStrategy"
          style="width: 120px"
          @change="val => handleChangeIfUseBiAmountStrategy(val, row)"
        >
          <el-option
            v-for="item in ifUseBiAmountStrategyOptions"
            :key="item.label"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
        <el-button
          type="text"
          @click="handleShowBiAmountStrategyDialog(row)"
        >
          查看
        </el-button>
        </span>
      </template>
      <template v-slot:quantity_default="{ row }">
        <el-tooltip v-if="row.isLadderPrice" effect="dark" content="价格已变更" placement="top">
          <i class="el-icon-warning" style="color: red; font-size: 12px"></i>
        </el-tooltip>
        <span>{{ row.quantity }}</span>
      </template>
      <template v-slot:quantity_edit="{ row }">
        <el-input-number :disabled="!!row.prType" v-model="row.quantity" style="width:100%" :controls="false" size="mini" @change="() => handleQueryMakeUpOrder([row], true)" />
      </template>
      <template v-slot:quantityUnit_default="{ row }">
        {{ formatQuantityUnit(row.quantityUnit) || '' }}
      </template>
      <template v-slot:customInstructions_default="{ row }">
        <el-popover
          placement="top-start"
          width="200"
          trigger="hover"
        >
          <div v-html="row.customInstructions"></div>
          <span slot="reference">{{ handleCustomInstructions(row.customInstructions) }}</span>
        </el-popover>
      </template>
      <template v-slot:position_default="{ row }">
        {{ row.position + ' ' + (warehouseList.find(item => item.warehouseLocationCode === row.position) || {}).warehouseLocationName }}
      </template>

      <template v-slot:shipWarehousePositon_default="{ row }">
        <el-select
          v-if="row.itemType === 'Z003'"
          clearable
          filterable
          v-model="row.shipWarehousePositon"
          placeholder="仓库地点编码"
          style="width:100%"
        >
          <el-option
            v-for="item in warehouseListFilterByFactory"
            :key="item.warehouseLocationCode"
            :label="`${item.warehouseLocationCode} ${item.warehouseLocationName}`"
            :value="item.warehouseLocationCode"
          />
        </el-select>
        <div v-else> {{ row.shipWarehousePositon }}</div>
      </template>

      <template v-slot:supplier_default="{ row }">
        <div v-if="row.itemType === 'Z003'"> {{ row.supplier }}</div>
        <SelectSkuSupplier
          v-else
          :data.sync="row.supplier"
          @change="handleChange2(row,'supplier', $event)"
          :disabled="!!row.prType"
          :factory="row.factory"
          :skuNo="row.skuNo"
        />
      </template>
      <template v-slot:replaceFirstSupplierReason_default="{ row }">
        <el-select
          v-model="row.replaceFirstSupplierReason"
          :disabled="isSpmSupplier(row)"
          size="mini"
          filterable
          clearable
          :class="{ 'orderReason-select': row.replaceFirstSupplierReason=== '其他' }"
        >
          <el-option
            v-for="item in reasonOptions"
            :key="item.value"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
        <el-input
          v-if="row.replaceFirstSupplierReason === '其他'"
          v-model="row.newReplaceFirstSupplierReason"
          size="mini"
          style="width: 100px"
          :maxlength="200"
          :disabled="isSpmSupplier(row)"
        />
      </template>
      <template v-slot:paymentTermsCodeName_default="{ row }">
        {{ (row.supplier1Detail && row.supplier) ? row.supplier1Detail.paymentTermsCodeName : '' }}
      </template>

      <template v-slot:isFree_default="{ row }">
        <el-checkbox
          v-model="row.isFree"
          :true-label="1"
          :false-label="0"
          @change="changeFree(row)"
          :checked="row.isFree === 1"
          :disabled="row.itemType === 'Z003' || isVpi(row)"
        >
        </el-checkbox>
      </template>

      <template v-slot:unitPrice_default="{ row }">
        <div v-if="row.itemType === 'Z003'"> {{ getPurchasePrice(row.supplier1Detail) }}</div>
        <el-input-number
          v-else
          v-model="row.unitPrice"
          @change="handelChangeUnitPrice(row)"
          :disabled="isVpi(row)"
          style="width: 100%"
        >
        </el-input-number>
      </template>

      <template v-slot:meinsName_default="{ row }">
        {{ row.prType === 3 ? (row.fppPurchasePriceVo && row.fppPurchasePriceVo.unitName || '') : (row.supplier1Detail && row.supplier) ? row.supplier1Detail.meinsName : '' }}
      </template>

      <template v-slot:currency_default="{ row }">
        {{ getCurrency(row.supplier1Detail.currency, row.supplier1Detail) }}
      </template>

      <template v-slot:taxRate_default="{ row }">
        <div v-if="row.itemType === 'Z003'"> {{ row.supplier1Detail.taxRate }}</div>
        <el-select
          v-else
          v-model="row.taxRate"
          filterable
          :disabled="isVpi(row)"
        >
          <el-option
            v-for="item in dictList.purchaseTaxRate"
            :key="item.id"
            :label="item.code +' '+ item.name"
            :value="item.parentCode"
          >
          </el-option>
        </el-select>
      </template>
      <template v-slot:operation_default="{row}">
        <el-tooltip v-if="row.mrpPrintRemark" class="item" effect="dark" :content="row.mrpPrintRemark" placement="top">
          <i class="el-icon-edit-outline" style="color: red;" @click="editRow(row)"></i>
        </el-tooltip>
        <i v-else class="el-icon-edit" @click="editRow(row)"></i>
      </template>
    </vxe-grid>
    <BiAmountStrategyDialog
      :showDialog.sync="BiAmountStrategyDialog"
      :data="rowDetail"
      @updateItemDetail="val=>handleUpdateItem(val)"
    ></BiAmountStrategyDialog>
    <el-dialog
      title="添加备注"
      :visible.sync="centerDialogVisible"
      width="30%"
      :before-close="handleClose"
      center>
      <el-input
        type="textarea"
        placeholder="请输入备注内容"
        v-model="mrpPrintRemark"
        maxlength="100"
        show-word-limit
      >
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addMrpPrintRemark">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { transfer2Po, getSupplier, getPrList, getDictionary, queryMakeUpOrder, getPurchaseCalendar, queryLadderPrice } from '@/api/mrp'
import { writeFile } from '@boss/excel'
import { cloneDeep } from 'lodash'
import { formatPrListData, getCurrency, getPurchasePrice, changeUnitPrice, filterHiddenFields, cellClick, getSplitOrderTips } from './utils'
import { getIds, removeIds } from '@/storage/index'
import moment from 'moment'
import Sortable from 'sortablejs'
import SelectSkuSupplier from './components/selectSkuSupplier.vue'
import { throttle, initVersion } from '@/utils/index'
import { transferColumns, supplierCertList } from './constants'
import BiAmountStrategyDialog from './components/transfer/BiAmountStrategyDialog.vue'

export default {
  name: 'orderTransfer',
  components: { SelectSupplier, SelectSkuSupplier, BiAmountStrategyDialog },
  data() {
    return {
      downloadVisible: false,
      options: [
        { value: 'Z001', label: 'Z001 国内采购订单' },
        { value: 'Z002', label: 'Z002 进口采购订单' },
        { value: 'Z003', label: 'Z003 库存转储采购订单' },
        { value: 'Z005', label: 'Z005 寄售采购订单' },
        { value: 'Z006', label: 'Z006 委外采购订单' },
        { value: 'Z013', label: 'Z013 客户代购采购订单' }
      ],
      ifUseBiAmountStrategyOptions: [
        { value: true, label: '应用' },
        { value: false, label: '不应用' }
      ],
      validateRules: {
        itemType: [
          { required: true, message: '订单类型不能为空！', trigger: ['change', 'blur'] }
        ]
      },
      searchForm: {
        supplier: {},
        itemType: ''
      },
      batchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons',
          tools: 'toolbar_tools'
        }
      },
      columns: transferColumns,
      reasonOptions: [],
      transferList: [],
      selectList: [],
      disabled: true,
      transferForm: {
        company: '',
        factory: '',
        purchaseGroup: [],
        brand: '',
        brands: {},
        mrpArea: [],
        productGroup: [],
        skuNo: '',
        endDate: '',
        isBufferLimit: '',
        isUrgent: 0,
        isVpi: 0,
        isPrivateMaterial: 0
      },
      warehouseListFilterByFactory: [],
      disabling: true,
      tableHeight: 800,
      BiAmountStrategyDialog: false,
      rowDetail: {},
      centerDialogVisible: false,
      row: {},
      mrpPrintRemark: ''
    }
  },
  async created() {
    const pList = []
    if (!Object.keys(this.dictList).length) {
      pList.push(this.$store.dispatch('orderCommon/queryDictList'))
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (this.purchaseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (this.productList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryProductGroup'))
    }
    getDictionary({ type: 'orderReason' }).then(data => {
      this.reasonOptions = data.data.filter(item => item.description === '申请下单原因')
    })
    await Promise.all(pList)
    if (getIds('mrp-orderTransfer-ids')) {
      if (this.$route.params.ids) {
        let query = JSON.parse(decodeURIComponent(this.$route.params.ids))
        this.$set(this.transferForm, 'company', query.company)
        this.$set(this.transferForm, 'factory', query.factory)
        this.$set(this.transferForm, 'purchaseGroup', query.purchaseGroup)
        this.$set(this.transferForm, 'skuNo', query.skuNo)
        this.$set(this.transferForm, 'productGroup', query.productGroup)
        this.$set(this.transferForm, 'mrpArea', query.mrpArea)
        this.$set(this.transferForm, 'brand', query.brand)
        this.$set(this.transferForm, 'endDate', query.endDate)
        this.$set(this.transferForm, 'isBufferLimit', query.isBufferLimit)
        this.$set(this.transferForm, 'isUrgent', query.isUrgent)
        this.$set(this.transferForm, 'isVpi', query.isVpi)
        this.$set(this.transferForm, 'isPrivateMaterial', query.isPrivateMaterial)
        this.$set(this.transferForm, 'isCustomInstructions', query.isCustomInstructions)
        this.$set(this.transferForm, 'isFppPr', query.isFppPr)
        this.$set(this.transferForm, 'soItemNo', query.soItemNo)
        this.$set(this.transferForm, 'soNo', query.soNo)
        this.$set(this.transferForm, 'ifGrossMargin', query.ifGrossMargin)
        this.$set(this.transferForm, 'isOrderException', query.isOrderException)
        console.log('query', query)
      }
    }
    await this.getPrListData()
    this.columnDrop()
  },
  beforeMount() {
    initVersion({ newVersion: '20220713', versionKey: 'mrpTransferVersionKey', columKey: 'orderTransferGrid_fullColumn' })
    const fullcolumns = JSON.parse(localStorage.getItem('orderTransferGrid_fullColumn'))
    if (fullcolumns) {
      this.columns = fullcolumns.map(item => {
        item.field = item.property
        return item
      })
    }
  },
  mounted() {
    this.$nextTick(function () {
      this.selfAdaption()
      window.addEventListener('resize', this.selfAdaption, false)
    })
  },
  beforeDestroy() {
    removeIds('mrp-orderTransfer-ids')
    if (this.sortable2) {
      this.sortable2.destroy()
    }
    window.removeEventListener('resize', this.selfAdaption, false)
  },
  computed: {
    ...mapState({
      dictList: state => state.orderCommon.dictList || {},
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      purchaseList: state => state.orderPurchase.purchaseList,
      warehouseList: state => state.orderPurchase.warehouseList,
      productList: state => state.mrp.productList
    })
  },
  watch: {
    'transferForm.factory': function (newVal) {
      this.warehouseListFilterByFactory = this.warehouseList.filter(w => {
        return w.factoryCode === newVal
      })
    }
  },
  methods: {
    getPurchasePrice,
    getCurrency,
    changeUnitPrice,
    cellClick,
    genRowColor({ row }) {
      const orderToday = typeof row?.orderCalendar?.orderToday === 'number' ? row.orderCalendar.orderToday : null
      return !orderToday && orderToday === 0 ? 'dis-order' : ''
    },
    addMrpPrintRemark() {
      this.row.mrpPrintRemark = this.mrpPrintRemark
      this.handleClose()
    },
    editRow(row) {
      this.centerDialogVisible = true
      this.row = row
      this.mrpPrintRemark = this.row.mrpPrintRemark
    },
    handleClose() {
      this.mrpPrintRemark = ''
      this.centerDialogVisible = false
    },
    handleChangeIfUseBiAmountStrategy() {
      this.handleUpdateItem()
      this.$forceUpdate()
    },
    handleUpdateItem(row) {
      const idx = this.transferList.findIndex(item => item === row)
      if (idx > -1) {
        this.$set(this.transferList, idx, row)
      }
    },
    handelChangeUnitPrice(row) {
      changeUnitPrice(row)
      this.handleQueryMakeUpOrder([row])
    },
    handleShowBiAmountStrategyDialog(row) {
      this.rowDetail = row
      console.log('this.rowDetail', this.rowDetail)
      this.BiAmountStrategyDialog = true
    },
    isSpmSupplier(item) {
      // const supplier1Data = item.purchasePriceVOList.find(item => item.priority === 0) || {}
      // if (item?.supplier.split(' ')[0] && item?.supplier.split(' ')[0] === supplier1Data?.providerNo) {
      //   return true
      // }
      return false
    },
    handleCustomInstructions(customInstructions) {
      return customInstructions ? customInstructions.split('<br>').join('') : ''
    },
    handleOrderNo(orderNo) {
      if (!orderNo) {
        return orderNo
      }
      return orderNo.split(/[|,,]/)
    },
    isVpi(item) {
      return !!item.prType
    },
    selfAdaption: throttle(function selfAdaption() {
      this.tableHeight = window.innerHeight - this.$refs.orderTransferGrid.$el.offsetTop - 130
      if (this.tableHeight < 260) {
        this.tableHeight = 260
      }
    }, 800),
    async getPrListData() {
      try {
        this.selectList = []
        let params = {
          ...this.transferForm,
          action: 'createPo',
          pageNo: 1,
          pageSize: -1
        }
        this.tableLoading = true
        const res = await getPrList(params)
        const listData = res.records.map(item => {
          return formatPrListData(item)
        })
        this.filterData(listData)
        this.tableLoading = false
      } catch (error) {
        this.tableLoading = false
        console.log(error)
      }
    },
    filterData(listData) {
      listData = listData.map(item => {
        this.setItemType(item)
        return item
      })
      // let ids = this.$route.params.ids.split(',')
      let ids = getIds('mrp-orderTransfer-ids')
      for (let i = 0; i < listData.length; i++) {
        for (let j = 0; j < ids.length; j++) {
          if (listData[i].id === +ids[j]) {
            if (this.isVpi(listData[i])) {
              listData[i].itemType = 'Z001'
            }
            if (listData[i].itemType !== 'Z003' && listData[i].prType === 0 && listData[i]?.biPageBeginAmountList && listData[i]?.biPageBeginAmountList.length > 0) {
              listData[i].ifUseBiAmountStrategy = true
            }
            if (listData[i].prType === 3) {
              listData[i].itemType = listData[i]?.fppPurchasePriceVo?.itemType
              listData[i].supplier = listData[i]?.fppPurchasePriceVo?.supplierNo
              listData[i].unitPrice = listData[i]?.fppPurchasePriceVo?.price
              listData[i].taxRate = listData[i]?.fppPurchasePriceVo?.taxRate
            }
            this.transferList.push(listData[i])
          }
        }
      }
      if (this.transferList.length === 0) {
        this.$message.info('无符合条件的物料需求')
        this.disabling = true
      } else {
        this.disabling = false
      }
    },
    setItemType(item) {
      if (item.productVO.productGroup === 190 && item.supplier1Detail.currency === 1) {
        item.itemType = 'Z013'
      } else if (item.mrpArea === item.supplier1Detail.zoneWashHouse) {
        if (item.supplier1Detail && item.supplier1Detail.currency === 1 && item.supplier1Detail.esokz === '0') {
          item.itemType = 'Z001'
        } else if (item.supplier1Detail && item.supplier1Detail.currency !== 1 && item.supplier1Detail.esokz === '0') {
          item.itemType = 'Z002'
        } else if (item.supplier1Detail && item.supplier1Detail.esokz === '3') {
          item.itemType = 'Z006'
        } else if (item.supplier1Detail && item.supplier1Detail.esokz === '2') {
          item.itemType = 'Z005'
        } else {
          item.itemType = ''
        }
      } else {
        item.itemType = ''
      }
    },
    handleChange(type, event) {
      if (type === 'supplier') {
        this.searchForm.supplierNo = event.supplierNo
      }
    },
    handleChange2(row, type, event) {
      if (type === 'supplier') {
        row.supplier = event.providerNo + ' ' + event.providerName
      }
      this.changeSupplier(row)
    },
    async handleQueryMakeUpOrder(list, isQuantity, isChangeSupplier) {
      // 更新数量调用阶梯价查询
      if (isQuantity) {
        if (!(list?.some(item => !item.supplier))) {
          const data = list.map(item => ({
            providerNo: item.supplier.split(' ')[0],
            skuNo: item.skuNo,
            factory: item.factory,
            quantity: item.quantity
          }))
          const res = await this.getLadderPrice(data)
          if (res?.length) {
            list.forEach((listItem, idx) => {
              if (res?.[idx]?.providerNo) {
                // 更改数量判断阶梯价
                const oPrice = listItem?.unitPrice
                const nPrice = this.getPurchasePrice(res?.[idx])
                if (oPrice !== nPrice && !isChangeSupplier) {
                  listItem.isLadderPrice = true
                } else {
                  listItem.isLadderPrice = false
                }
                this.$set(listItem, 'supplier1Detail', { ...res?.[idx] })
                listItem.unitPrice = nPrice
                listItem.taxRate = listItem.supplier1Detail.taxRate
              }
            })
          }
        }
      }
      list.map(item => {
        this.transferList.map(transfer => {
          if (item.id === transfer.id) {
            transfer.biPageBeginAmountList = []
            transfer.ifUseBiAmountStrategy = false
            transfer.strategyType = 'system_strategy_amount'
          }
        })
      })
      let data = []
      list.map(item => {
        const { company, createMark, factory, id, mrpArea, prType, purchaseGroup, skuNo, supplier, unitPrice, quantity } = item
        console.log('supplier', supplier)
        const supplierNo = supplier && supplier.split(' ') ? supplier.split(' ')[0] : supplier
        if (!(company && createMark && factory && mrpArea && prType === 0 && purchaseGroup && skuNo && unitPrice && supplierNo && quantity)) {
          return
        }
        data.push({
          company, createMark, factory, id, mrpArea, prType, purchaseGroup, skuNo, supplier: supplierNo, unitPrice, quantity
        })
      })
      if (data.length < 1) {
        return
      }
      const res = await queryMakeUpOrder(data)
      if (res) {
        res.map(item => {
          this.transferList.map(transfer => {
            if (item.id === transfer.id) {
              transfer.biPageBeginAmountList = item.biBeginAmountList
              if (item.biBeginAmountList && item.biBeginAmountList.length > 0) {
                transfer.ifUseBiAmountStrategy = true
              } else {
                transfer.ifUseBiAmountStrategy = false
              }
            }
          })
        })
      }
    },
    changeType(row) {
      if (row.itemType === 'Z003') {
        row.supplier = ''
        row.supplier1Detail = {}
        row.unitPrice = ''
        row.taxRate = ''
        row.isFree = 0
        row.biPageBeginAmountList = []
        row.ifUseBiAmountStrategy = false
      } else {
        row.shipWarehousePositon = ''
      }
    },
    changeFree(row) {
      if (row.isFree) {
        row.unitPrice = 0
        row.taxRate = '0'
      } else {
        row.unitPrice = this.getPurchasePrice(row.supplier1Detail)
        row.taxRate = row.supplier1Detail.taxRate
      }
    },
    async changeSupplier(row) {
      this.handleQueryMakeUpOrder([row], true, true)
      this.setItemType(row)
      // 订货日历
      this.updateOrderCalendar([row])
    },
    // 更新订货日历
    async updateOrderCalendar(list) {
      const params = list?.map(item => {
        const { brand, factory, productGroup, mrpArea, supplier } = item
        return ({
          brandId: brand,
          factory,
          mrpArea,
          productGroupCode: productGroup,
          supplierNo: supplier.split(' ')[0]
        })
      }) || []
      try {
        const result = await getPurchaseCalendar(params)
        if (result.code === 200 && result?.data?.length) {
          result.data.forEach((row, idx) => {
            this.$set(list[idx], 'orderCalendar', { ...row })
          })
        } else {
          this.$message.error(result?.msg || '获取订货日历信息出错')
        }
      } catch (err) {
        console.log(err)
      }
    },
    formatQuantityUnit(value) {
      if (value && this.dictList['quantityUnit']) {
        const unit = this.dictList['quantityUnit'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    selectChange({ checked, records, row }) {
      if (checked) {
        this.selectList = records
      } else {
        this.selectList = this.selectList.filter(item => item.id !== row.id)
      }
      this.disable()
    },
    selectAll({ checked, records }) {
      if (checked) {
        this.selectList = records
      } else {
        this.selectList = []
      }
      this.disable()
    },
    disable() {
      if (this.selectList.length > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    handleBatchType() {
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          if (this.selectList.length === 0) {
            return this.$message.error('未选择要修改的采购订单')
          }
          this.selectList = this.selectList.map(item => {
            if (!this.isVpi(item)) {
              item.itemType = this.searchForm.itemType
              this.changeType(item)
            }
            return item
          })
        } else {
          return false
        }
      })
    },
    async handleBatch() {
      if (!this.searchForm.supplierNo) {
        return this.$message.error('供应商文本框未输入')
      }
      if (this.selectList.length === 0) {
        return this.$message.error('未选择要修改的采购订单')
      }
      this.batchLoading = true
      this.selectList = await this.clone(cloneDeep(this.selectList))
      this.transferList = this.transferList.map(item => {
        let idxValue = this.selectList.find(value => value.id === item.id && item.itemType !== 'Z003' && !this.isVpi(item))
        if (idxValue) {
          item = idxValue
        }
        return item
      })
      this.$refs.orderTransferGrid.setCheckboxRow(this.selectList, true)
      this.handleQueryMakeUpOrder(this.selectList)
      // 订货日历
      this.updateOrderCalendar(this.selectList)
      this.batchLoading = false
    },
    async clone(itemList) {
      const supplierNo = this.searchForm.supplier.supplierNo
      const supplierName = this.searchForm.supplier.supplierName
      const data = itemList.map(item => ({
        providerNo: supplierNo,
        skuNo: item.skuNo,
        factory: item.factory,
        quantity: item.quantity
      }))
      const res = await this.getLadderPrice(data)
      if (res?.length) {
        itemList.forEach((item, idx) => {
          if (item.itemType !== 'Z003') {
            this.$set(item, 'supplier1Detail', { ...res[idx] })
            item.supplier = supplierNo + ' ' + supplierName
            item.unitPrice = this.getPurchasePrice(item.supplier1Detail)
            item.taxRate = item.supplier1Detail.taxRate
            item.isFree = 0
            this.setItemType(item)
          }
        })
      }
      return itemList
      // let idxValue = this.selectList.find(value => value.id === item.id && item.itemType !== 'Z003')
      // if (idxValue) {
      //   const supplierNo = this.searchForm.supplier.supplierNo
      //   item.supplier = supplierNo + ' ' + this.searchForm.supplier.supplierName
      //   const val = await this.getSupplierDetail({
      //     providerNo: supplierNo,
      //     skuNo: item.skuNo,
      //     factory: item.factory
      //   })
      //   this.$set(item, 'supplier1Detail', { ...val })
      //   item.unitPrice = this.getPurchasePrice(item.supplier1Detail)
      //   item.taxRate = item.supplier1Detail.taxRate
      //   item.isFree = 0
      //   this.setItemType(item)
      // }
      // return item
    },
    async getSupplierDetail(params) {
      try {
        if (!params.providerNo) {
          console.log('params', params)
          this.$message.error('请重新选择供应商!')
          return
        }
        const res = await getSupplier(params)
        if (res.code === 200) {
          return res.data
        } else {
          return {}
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getLadderPrice(params) {
      try {
        const res = await queryLadderPrice(params)
        if (res.code === 200) {
          return res.data
        } else {
          return {}
        }
      } catch (error) {
        console.log(error)
      }
    },
    autoBatch() {
      this.selectList = this.selectList.map(item => {
        item.supplier = !this.isVpi(item) ? this.purchasePriceVOList0(item) : item.supplier
        return item
      })
      this.transferList = this.transferList.map(item => {
        let idxValue = this.selectList.find(value => value.id === item.id && item.itemType !== 'Z003' && !this.isVpi(item))
        if (idxValue) {
          item = idxValue
        }
        return item
      })
      this.handleQueryMakeUpOrder(this.selectList)
      // 订货日历
      this.updateOrderCalendar(this.selectList)
    },
    purchasePriceVOList0(row) {
      const first = row.purchasePriceVOList.find(item => item.priority === 0)
      if (first && first.providerNo) {
        this.$set(row, 'supplier1Detail', { ...first })
        row.unitPrice = this.getPurchasePrice(row.supplier1Detail)
        row.taxRate = row.supplier1Detail.taxRate
        this.setItemType(row)
        return first.providerNo + ' ' + first.providerName
      } else {
        this.$set(row, 'supplier1Detail', {})
        row.unitPrice = undefined
        row.taxRate = ''
        return ''
      }
    },
    async checkPurchaseCalendar() {
      let flag = true
      const errorList = []
      this.selectList.forEach(item => {
        if (item?.orderCalendar && item.orderCalendar.orderToday === 0) {
          errorList.push(`${item?.factory}-${item?.skuNo}-${item?.mrpArea}-${item?.supplier?.split(' ')?.[0]}`)
          flag = false
        }
      })
      return !flag ? this.$confirm(`以下行非订货日(工厂-sku-mrp区域-供应商)：</br>${errorList?.toString().replace(/,/g, '</br>')}`, '操作提示', {
        confirmButtonText: '继续',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }) : Promise.resolve()
    },
    async transferPO() {
      this.selectList = this.selectList.filter(item => typeof item.orderNo !== 'string' || item.orderNo === '')
      try {
        let supplierNoAndName = {}
        this.selectList.forEach(item => {
          if (item.supplier) {
            const supplierNo = item.supplier.split(' ')[0]
            supplierNoAndName[supplierNo] = item.supplier
          }
        })
        // 非空校验
        this.selectList = this.selectList.map(item => {
          if (!item.itemType || (item.itemType !== 'Z003' && !(item.supplier && typeof item.unitPrice === 'number' && item.taxRate))) {
            item.msg = '订单类型、分配供应商、采购价格、进项税不能为空'
          } else {
            item.msg = ''
          }
          return item
        })
        this.transferList = this.transferList.map(value => {
          let idx = this.selectList.findIndex(val => val.id === value.id)
          if (idx !== -1) {
            value.msg = this.selectList[idx].msg
          }
          return value
        })
        // 订货日校验
        await this.checkPurchaseCalendar()

        if (this.selectList.find(val => val.msg === '订单类型、分配供应商、采购价格、进项税不能为空')) return

        this.loading = this.$loading({
          background: 'rgba(0, 0, 0, 0.5)'
        })
        const selectList = cloneDeep(this.selectList).map(item => {
          item.unitPrice = item.unitPrice >= 0 ? Number(item.unitPrice) : item.unitPrice
          item.taxRate = (this.dictList.purchaseTaxRate.find(val => val.parentCode === item.taxRate) || {}).code
          item.currency = item.supplier1Detail.currency
          item.supplier = item.supplier ? item.supplier.split(' ')[0] : null
          const { meins, meinsName } = item.supplier1Detail
          if (meins && meinsName) {
            item.purchaseUnitCode = item.supplier1Detail.meins
            item.purchaseUnitName = item.supplier1Detail.meinsName
          } else if (item.quantityUnit) {
            item.purchaseUnitCode = item.quantityUnit
            item.purchaseUnitName = this.formatQuantityUnit(item.quantityUnit)
          }
          if (item.itemType === 'Z003') {
            item.ifUseBiAmountStrategy = null
          }
          if (!item.strategyType) {
            item.strategyType = 'system_strategy_amount'
          }
          const supplier1Data = item.purchasePriceVOList.find(item => item.priority === 0) || {}
          if (supplier1Data) {
            item.tacticsName = supplier1Data.tacticsName
            item.tacticsDetail = supplier1Data.tacticsDetail
          }
          if (item.supplier && item?.supplier === supplier1Data?.providerNo) {
            item.isSpmSupplier = true
          } else {
            item.isSpmSupplier = false
          }
          item.biBeginAmountList = item.biPageBeginAmountList
          delete item.biPageBeginAmountList
          item.replaceFirstSupplierReason = item.replaceFirstSupplierReason && item.replaceFirstSupplierReason === '其他' ? item.newReplaceFirstSupplierReason : item.replaceFirstSupplierReason
          return item
        })
        const splitOrderData = selectList.map(item => {
          return {
            factory: item.factory,
            position: item.position,
            shippingPosition: item.shipWarehousePositon,
            purchaseGroup: item.purchaseGroup,
            supplierNo: item.supplier,
            skuNo: item.skuNo,
            orderType: item.itemType
          }
        })
        await getSplitOrderTips(this, splitOrderData)
        const res = await transfer2Po(selectList)
        this.transferList = this.transferList.map(value => {
          let idx = res.findIndex(val => val.id === value.id)
          if (idx !== -1) {
            value.msg = res[idx].msg
            value.orderNo = res[idx].orderNo
            // value.supplier = supplierNoAndName[value.supplier]
          }
          return value
        })
        this.$refs.orderTransferGrid.setCheckboxRow(this.selectList, false)
        this.selectList = []
        this.disable()
        this.loading.close()
      } catch (error) {
        this.loading.close()
        console.log(error)
      }
    },
    checkMethod({ row }) {
      if (row.orderNo) {
        return false
      } else {
        return true
      }
    },
    handleDownExcel(isToday) {
      let mapping = {
        msg: '消息',
        orderNo: '采购订单',
        itemType: '订单类型',
        factory: '工厂',
        skuNo: 'SKU',
        materialDescribe: 'SKU描述',
        skuTypeName: 'SKU类型',
        originQuantity: '缺货数量',
        quantity: '申请数量',
        quantityUnit: '基本单位',
        position: '收货库位',
        shipWarehousePositon: '发货库位',
        supplier: '分配供应商',
        paymentTermsCodeName: '付款条件',
        isFree: '免费',
        unitPrice: '采购价格',
        meinsName: '采购单位',
        currency: '币种',
        taxRate: '进项税',
        prNo: '销售订单号/行号',
        isOrderException: '下单异常',
        pendingReasonDesc: '未采购说明',
        supplierCert: '合格证标示'
      }
      // mapping = dragFields('orderTransferGrid_fullColumn', mapping, this)
      mapping = filterHiddenFields(mapping, 'transfer_grid')

      let list = this.transferList.map(item => {
        const data = { ...item }
        const {
          msg,
          orderNo,
          itemType,
          factory,
          skuNo,
          skuTypeName,
          originQuantity,
          quantity,
          quantityUnit,
          position,
          shipWarehousePositon,
          supplier,
          isFree,
          unitPrice,
          taxRate,
          productVO: {
            materialDescribe
          },
          supplier1Detail: {
            paymentTermsCodeName,
            meinsName,
            currency,
            currencyName
          },
          orderCalendar
        } = data
        return {
          prNo: item.prNo + '/' + item.pritemNo,
          isOrderException: item.isOrderException ? '是' : '否',
          pendingReasonDesc: (item.pendingReasonName || '') + ' ' + (item.detailPendingReasonDesc || '') + ' ' + (item.pendingReasonDesc || ''),
          msg,
          orderNo,
          itemType,
          factory,
          skuNo,
          materialDescribe,
          skuTypeName,
          originQuantity,
          quantity,
          quantityUnit: this.formatQuantityUnit(quantityUnit) || '',
          position: position + ' ' + (this.warehouseList.find(item => item.warehouseLocationCode === position) || {}).warehouseLocationName,
          shipWarehousePositon,
          supplier,
          paymentTermsCodeName,
          isFree: (isFree || isFree === 1) ? '是' : '否',
          unitPrice,
          meinsName,
          currency: this.getCurrency(currency, { currencyName }),
          taxRate: (this.dictList.purchaseTaxRate.find(item => item.parentCode === taxRate || item.code === taxRate) || {}).name,
          orderCalendar,
          supplierCert: supplierCertList.find(it => it.value === item.supplierCert)?.label || ''
        }
      })

      // 过滤非订货日
      if (isToday) {
        list = list.filter(item => {
          return !(item?.orderCalendar?.orderToday === 0)
        })
      }

      const allList = list.map(data => {
        Object.keys(data).forEach(key => {
          if (mapping[key]) {
            data[mapping[key]] = data[key]
          }
          delete data[key]
        })
        return data
      })
      this.downloadVisible = false
      writeFile(allList, `物料需求报表 - 转单 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: Object.values(mapping) })
    },
    toDetail(no) {
      this.$router.push(`/orderPurchase/detail/${no}`)
    },
    columnDrop() {
      this.$nextTick(() => {
        const $table = this.$refs.orderTransferGrid
        this.sortable2 = Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
          handle: '.vxe-header--column:not(.col--fixed)',
          onEnd: ({ item, newIndex, oldIndex }) => {
            const { fullColumn, tableColumn } = $table.getTableColumn()
            const targetThElem = item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[newIndex]
            if (newColumn.fixed) {
              // 错误的移动
              if (newIndex > oldIndex) {
                wrapperElem.insertBefore(targetThElem, wrapperElem.children[oldIndex])
              } else {
                wrapperElem.insertBefore(wrapperElem.children[oldIndex], targetThElem)
              }
              return this.$message.error('固定列不允许拖动！')
            }
            // 转换真实索引
            const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
            const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
            // 移动到目标列
            const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
            fullColumn.splice(newColumnIndex, 0, currRow)
            $table.loadColumn(fullColumn)
            localStorage.setItem('orderTransferGrid_fullColumn', JSON.stringify(fullColumn))
          }
        })
      })
    }
  }
}
</script>
<style lang="scss">
  .dis-order {
    background-color: rgb(255, 238, 238);
  }
</style>
<style lang="scss" scoped>
  .order-transfer {
    padding: 5px 20px;
  }

  .orderReason-select {
    width: 100px !important;
  }
</style>
