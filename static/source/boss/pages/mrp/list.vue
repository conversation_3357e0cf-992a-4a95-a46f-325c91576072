<template>
  <div class="list-container">
    <el-form :model="searchForm" ref="searchFrom" style="width: 100%" label-suffix=":" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="MRP区域">
            <el-select
              clearable
              filterable
              default-first-option
              v-model="searchForm.MRPCode"
              placeholder="MRP区域编码"
              style="width:100%"
            >
              <el-option
                v-for="item in mrpAreaList"
                :key="item.code+item.description"
                :label="item.code + ' ' +item.description"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="工厂">
            <el-select
              v-model="searchForm.factory"
              filterable
              clearable
              default-first-option
              style="width:100%"
              placeholder="请选择工厂"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factoryCode+item.factoryName"
                :label="item.factoryCode+' '+item.factoryName"
                :value="item.factoryCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="仓库地点">
            <el-select
                clearable
                filterable
                default-first-option
                v-model="searchForm.warehouse"
                placeholder="仓库地点编码"
                style="width:100%"
            >
              <el-option
                  v-for="item in warehouseListFilterByFactory"
                  :key="item.warehouseLocationCode"
                  :label="`${item.warehouseLocationCode} ${item.warehouseLocationName}`"
                  :value="item.warehouseLocationCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2" :style="{display: 'flex', justifyContent: 'flex-end'}">
          <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <vxe-grid
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="mrpListGrid"
      row-id="id"
      height="740px"
      id="mrp_grid"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'manual', mode: 'row', autoClear: false, showIcon: false, showStatus: true}"
      :checkbox-config="{reserve: true}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
      highlight-hover-row
    >
      <template v-slot:toolbar_buttons>
        <el-button type="danger" size="mini" plain @click="removeCheckboxRow" :disabled="dis">批量删除</el-button>
        <el-button type="primary" size="mini" @click="handleDownExcel" :disabled="disabling">全部下载</el-button>
      </template>
      <template v-slot:toolbar_tools>
        <el-button type="primary" size="mini" @click="addArea">新增区域</el-button>
        <el-button type="primary" size="mini" plain  @click="handleDownloadTemplateHeader">下载模板</el-button>
        <el-upload
          ref="upload"
          style="display:inline-block;margin-left:10px"
          accept=".xlsx"
          :before-upload="$validateFileType"
          action="/api-mrp/mrpArea/batchImport"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="handleChange"
          multiple
          :show-file-list="false"
        >
          <el-button type="primary" size="mini" plain class="btn">批量上传</el-button>
        </el-upload>
      </template>
      <template v-slot:type_default="{ row }">
        {{ row.type + ' ' + (row.type === 1 ? '工厂' : '仓库地点') }}
      </template>
      <template v-slot:description_default="{ row }">
        {{ (mrpAreaList.find(item => item.code === row.code) || {}).description }}
      </template>
      <template v-slot:factoryCode_default="{ row }">
        {{ row.factory + ' ' + (factoryList.find(item => item.factoryCode === row.factory) || {}).factoryName }}
      </template>
      <template v-slot:position_default="{ row }">
        {{row.position ? row.position + ' ' +  (warehouseList.find(item => item.warehouseLocationCode === row.position) || {}).warehouseLocationName : ''}}
      </template>
      <template v-slot:checkbox_default="{ row }">
        <el-checkbox v-show="row.type === 2" v-model="row.isDefault" :checked="row.isDefault===1" :disabled="!$refs.mrpListGrid.isActiveByRow(row)" @change="changeChecked(row)">
        </el-checkbox>
      </template>
      <template v-slot:data_default="{ row }">
        {{ row.gmtModify ? row.gmtModify.slice(0,10) : '' || row.gmtCreate ? row.gmtCreate.slice(0,10) : '' }}
      </template>
      <template v-slot:operation="{ row }">
        <div v-if="$refs.mrpListGrid.isActiveByRow(row)">
          <el-button type="text" size="medium" @click="saveRow(row)">保存</el-button>
          <el-button type="text" size="medium" @click="cancel(row)">取消</el-button>
        </div>
        <div  v-else class="operation">
          <el-button type="text" size="medium" v-if="row.type === 2" @click="addWarehouse(row)">新增库位</el-button>
          <el-button type="text" @click="editRow(row)" size="medium">修改</el-button>
          <el-button type="text" @click="removeRow(row)" size="medium">删除</el-button>
        </div>
      </template>
      <template #pager>
        <vxe-pager
          :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
          :border="true"
          :current-page.sync="tablePage.currentPage"
          :page-size.sync="tablePage.pageSize"
          :total="tablePage.total"
          @page-change="handlePageChange">
        </vxe-pager>
      </template>
    </vxe-grid>

    <add-area
      ref="children"
      :dialogTitle="dialogTitle"
      :visible.sync="dialogVisible"
      :dialogForm="dialogForm"
      :rules="rules"
      :disabled="disabled"
      :getMrpList="getMrpList"
      @updateVisible="updateVisible"
      @resetPopupData="resetPopupData"
      @submitPopupData="submitPopupData"
      @modifyRulesFalse="modifyRulesFalse"
      @modifyRulesTrue="modifyRulesTrue"
      @updateDefaultPosition="updateDefaultPosition"
      @updatePosition="updatePosition"
      @warehouseListFilter="warehouseListFilter"
      :factoryList="factoryList"
      :warehouseList="warehouseList"
      :warehouseListFilterByFactory1="warehouseListFilterByFactory1"
    >
    </add-area>

    <el-dialog
      width="30%"
      :visible.sync="verifyVisible"
      :modal="false"
      :close-on-click-modal="false"
    >
     <span>MRP区域{{ row.code }}原始默认仓储地点为{{ row.defaultPosition }}，请确认是否更改默认仓储地点?</span>
     <span slot="footer" class="dialog-footer">
      <el-button
      type="primary"
      @click="modifyDefaultWarehouse">是</el-button>
      <el-button @click="unmodifyDefault">否</el-button>
    </span>
    </el-dialog>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getMrpAreaList, modifyMrpArea, deleteMrpArea, batchUploadMrpArea, verifyDefaultWarehouse, batchDeleteMrpArea } from '@/api/mrp'
import addArea from './components/add'
import { excelUrls } from './constants'
import { writeFile } from '@boss/excel'
import moment from 'moment'
import { getFactoryList } from './utils'

const columns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    field: 'code',
    title: 'MRP区域编码',
    minWidth: 120,
    fixed: 'left'
  },
  {
    field: 'description',
    title: 'MRP区域描述',
    minWidth: 120,
    fixed: 'left',
    editRender: { name: 'input' }
  },
  {
    field: 'type',
    title: 'MRP区域类型',
    slots: {
      default: 'type_default'
    },
    minWidth: 80
  },
  {
    field: 'factory',
    title: '工厂',
    slots: {
      default: 'factoryCode_default'
    },
    minWidth: 120
  },
  {
    field: 'position',
    title: '仓库地点',
    slots: {
      default: 'position_default'
    },
    minWidth: 120
  },
  {
    title: '默认仓库地点',
    slots: {
      default: 'checkbox_default'
    },
    minWidth: 120
  },
  {
    field: 'gmtModify' || 'gmtCreate',
    title: '保存日期',
    slots: {
      default: 'data_default'
    },
    minWidth: 80
  },
  {
    title: '操作',
    slots: {
      default: 'operation'
    },
    minWidth: 150,
    fixed: 'right'
  }
]
export default {
  name: 'mrpList',
  components: {
    addArea
  },
  data () {
    return {
      searchForm: {
        MRPCode: '',
        factory: '',
        warehouse: ''
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons',
          tools: 'toolbar_tools'
        }
      },
      columns,
      tableData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 15
      },
      dialogVisible: false,
      dialogTitle: '',
      dialogForm: {
        code: '',
        description: '',
        factory: '',
        type: '',
        defaultPosition: '',
        position: ''
      },
      rules: {
        code: [
          { required: true, message: 'MRP区域编码不能为空！', trigger: ['change', 'blur'] },
          { max: 10, message: 'MRP区域编码不能超过 10 个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: 'MRP区域描述不能为空！', trigger: ['change', 'blur'] },
          { max: 40, message: 'MRP区域描述不能超过 40 个字符', trigger: 'blur' }
        ],
        defaultPosition: [
          { required: false }
        ]
      },
      disabled: false,
      loading: null,
      fileList: [],
      file: '',
      verifyVisible: false,
      warehouseListFilterByFactory: [],
      warehouseListFilterByFactory1: [],
      row: {
        code: '',
        description: '',
        factory: '',
        isDefault: '',
        type: '',
        position: '',
        id: '',
        defaultPosition: ''
      },
      flag: false,
      selectList: [],
      dis: true,
      disabling: true
    }
  },
  async created () {
    const pList = []
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (!this.mrpAreaList || this.mrpAreaList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryMrpAreaList'))
    }
    await Promise.all(pList)
    this.getMrpList()
    await this.setObjectArray()
  },
  computed: {
    ...mapState({
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      warehouseList: state => state.orderPurchase.warehouseList,
      mrpAreaList: state => state.mrp.mrpAreaList
    }),
    factoryList () {
      return getFactoryList(this.companyFactoryList)
    }
  },
  methods: {
    setObjectArray () {
      let obj = {}
      for (let i = 0; i < this.warehouseList.length; i++) {
        if (!obj[this.warehouseList[i].warehouseLocationCode]) {
          this.warehouseListFilterByFactory.push(this.warehouseList[i])
          obj[this.warehouseList[i].warehouseLocationCode] = true
        }
      }
    },
    handleSearch () {
      this.tablePage.currentPage = 1
      this.getMrpList()
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getMrpList()
    },
    async getMrpList () {
      this.tableLoading = true
      this.searchLoading = true
      try {
        const params = {
          pageNo: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          mrpArea: this.searchForm.MRPCode,
          factory: this.searchForm.factory,
          position: this.searchForm.warehouse
        }
        const res = await getMrpAreaList(params)
        this.tableData = res.records.map(item => {
          if (item.isDefault === 0) {
            item.isDefault = false
          } else if (item.isDefault === 1) {
            item.isDefault = true
          }
          return item
        })
        this.tablePage.total = res.total
        if (this.tablePage.total === 0) {
          this.$message.info('没有符合条件的MRP区域列表')
          this.disabling = true
        } else {
          this.disabling = false
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
      }
    },
    updateVisible (val) {
      this.dialogVisible = val
    },
    resetPopupData () {
      this.dialogVisible = false
    },
    submitPopupData () {
      this.dialogVisible = false
    },
    updateDefaultPosition () {
      this.dialogForm.defaultPosition = ''
    },
    updatePosition () {
      this.dialogForm.position = ''
    },
    warehouseListFilter (val) {
      this.warehouseListFilterByFactory1 = this.warehouseList.filter(w => {
        return w.factoryCode === val
      })
    },
    addArea () {
      this.dialogTitle = '新增MRP区域'
      this.rules.defaultPosition = [
        { required: true, message: '默认仓库地点不能为空！', trigger: ['change', 'blur'] }
      ]
      if (this.dialogForm.type === 1) {
        this.rules.defaultPosition = [
          { required: false }
        ]
      }
      this.dialogForm = {
        code: '',
        description: '',
        factory: '1000',
        type: 2,
        defaultPosition: '',
        position: ''
      }
      this.disabled = false
      this.updateVisible(true)
      // 移除表单项的校验结果
      setTimeout(() => {
        this.$refs.children.$refs.dialogForm.clearValidate()
      }, 0)
    },
    async addWarehouse (row) {
      this.dialogTitle = '新增现有MRP区域库位'
      this.rules.defaultPosition = [
        { required: false }
      ]
      this.dialogForm = {
        code: row.code,
        description: row.description,
        factory: row.factory,
        type: row.type,
        defaultPosition: '',
        position: ''
      }
      if (row.isDefault) {
        this.dialogForm.defaultPosition = row.position
      } else {
        const res = await verifyDefaultWarehouse({ mrpArea: this.dialogForm.code, factory: this.dialogForm.factory, isDefault: 1 })
        if (res.total > 0) {
          this.dialogForm.defaultPosition = res.records[0].position
        }
      }
      this.disabled = true
      this.updateVisible(true)
    },
    editRow (row) {
      this.$refs.mrpListGrid.setActiveRow(row)
    },
    saveRow (row) {
      this.row = row
      if (row.isDefault === 0) {
        this.row.isDefault = false
      } else if (row.isDefault === 1) {
        this.row.isDefault = true
      }
      this.$refs.mrpListGrid.clearActived().then(async () => {
        if (!this.row.description) {
          this.$message.error('MRP区域描述为必填项')
          // 还原行数据
          return this.$refs.mrpListGrid.revertData(row)
        }
        try {
          const res = await verifyDefaultWarehouse({ mrpArea: row.code, isDefault: 1 })
          if (this.row.isDefault && res.total === 1 && this.row.isDefault !== res.records[0].position && this.flag) {
            this.verifyVisible = true
            this.row.defaultPosition = res.records[0].position
            this.flag = false
          } else if (!this.row.isDefault && this.flag) {
            this.$message.error(`MRP区域${row.code}无默认仓储地点，请先确定此区域最新的默认仓储地点！`)
            this.flag = false
            return this.$refs.mrpListGrid.revertData(row)
          } else {
            this.saveModify(this.row)
          }
        } catch (error) {
          console.log(error);
        }
      })
    },
    modifyDefaultWarehouse () {
      this.saveModify(this.row)
      this.verifyVisible = false
    },
    unmodifyDefault () {
      this.row.isDefault = 0
      this.saveModify(this.row)
      this.verifyVisible = false
    },
    async saveModify (row) {
      if (row.isDefault) {
        row.isDefault = 1
      } else {
        row.isDefault = 0
      }
      this.tableLoading = true
      try {
        await modifyMrpArea(row)
        this.getMrpList()
        this.tableLoading = false
      } catch (error) {
        this.tableLoading = false
        console.log(error);
      }
    },
    cancel (row) {
      const $grid = this.$refs.mrpListGrid
      $grid.clearActived().then(() => {
        // 还原行数据
        $grid.revertData(row)
      })
    },
    removeRow (row) {
      this.$confirm('您确定要删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await deleteMrpArea(row.id)
        this.getMrpList()
      }).catch(() => {

      });
    },
    handleDownloadTemplateHeader () {
      window.open(excelUrls.mrpAreaImport)
    },
    handleChange (file, fileList) {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      this.fileList = [fileList[fileList.length - 1]]; // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file.raw;
      let reader = new FileReader()
      reader.readAsArrayBuffer(this.file)
      let factoryMap = {}
      this.companyFactoryList.forEach(company => {
        const factoryList = company.factoryList
        factoryList.forEach(f => {
          factoryMap[f.factoryCode] = ''
        })
      })
      let warehouseMap = {}
      this.warehouseList.forEach(w => {
        const key = `${w.factoryCode}-${w.warehouseLocationCode}`
        warehouseMap[key] = ''
      })
      reader.onload = async () => {
        let buffer = reader.result
        let bytes = new Uint8Array(buffer)
        let length = bytes.byteLength
        let binary = ''
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        let XLSX = require('xlsx')
        let wb = XLSX.read(binary, {
          type: 'binary'
        })
        let outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]], { header: 1, raw: true })
        outdata.splice(0, 3)

        for (let i = 0; i < outdata.length; i++) {
          outdata[i] = {
            code: outdata[i][0],
            description: outdata[i][1],
            type: outdata[i][2],
            factory: outdata[i][3],
            position: outdata[i][4],
            isDefault: outdata[i][5]
          }
        }
        outdata = outdata.filter(item =>
          !(item.code === undefined && item.description === undefined && item.type === undefined && item.factory === undefined && item.position === undefined && item.isDefault === undefined))

        let alertArr = []
        for (let i = 0; i < outdata.length; i++) {
          if (!outdata[i].code) {
            alertArr.push(`${i + 4}行MRP区域编码必填`)
          }
          if (!outdata[i].description) {
            alertArr.push(`${i + 4}行MRP区域描述必填`)
          }
          if (!outdata[i].type) {
            alertArr.push(`${i + 4}行MRP区域类型必填`)
          }
          if (!outdata[i].factory) {
            alertArr.push(`${i + 4}行工厂必填`)
          } else {
            if (!factoryMap.hasOwnProperty(outdata[i].factory)) {
              alertArr.push(`${i + 4}行工厂不合法`)
            }
          }

          // 仓库地点合法性校验
          if (outdata[i].type === 2) {
            const factory = outdata[i].factory
            const position = outdata[i].position
            const key = `${factory}-${position}`
            if (!warehouseMap.hasOwnProperty(key)) {
              alertArr.push(`${i + 4}行仓库地点不合法`)
            }
          }
        }
        if (alertArr.length > 0) {
          this.loading.close()
          return this.$message({
            message: alertArr.join('<br>'),
            type: 'error',
            dangerouslyUseHTMLString: true,
            showClose: true,
            duration: 10000
          })
        }
        try {
          await batchUploadMrpArea(outdata)
          this.getMrpList()
          this.loading.close()
        } catch (error) {
          this.loading.close()
          console.log(error);
        }
      }
    },
    changeChecked () {
      this.flag = true
    },
    modifyRulesFalse () {
      this.rules.defaultPosition = [
        { required: false }
      ]
    },
    modifyRulesTrue () {
      this.rules.defaultPosition = [
        { required: true, message: '默认仓库地点不能为空！', trigger: ['change', 'blur'] }
      ]
    },
    selectChange ({ checked, records, row }) {
      if (checked) {
        this.selectList.push(row)
      } else {
        this.selectList = this.selectList.filter(item => item.id !== row.id)
      }
      this.disable()
    },
    selectAll ({ checked, reserves, records }) {
      if (checked) {
        this.selectList = [...reserves, ...records]
      } else {
        this.selectList = reserves
      }
      this.disable()
    },
    disable () {
      if (this.selectList.length > 0) {
        this.dis = false
      } else {
        this.dis = true
      }
    },
    removeCheckboxRow () {
      this.$confirm('您确定要删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteCheckbox()
      }).catch(() => {

      });
    },
    async deleteCheckbox () {
      try {
        let data = this.selectList.map(item => item.id)
        data = data.join(' ')
        const res = await batchDeleteMrpArea({ idList: data })
        if (res.code === 200) {
          this.$message.success(res.msg)

          this.getMrpList()
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.mrpListGrid.setCheckboxRow(this.selectList, false)
        this.selectList = []
        this.disable()
      } catch (error) {
        console.log(error);
      }
    },
    handleDownExcel () {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const params = {
        pageNo: 1,
        pageSize: -1,
        mrpArea: this.searchForm.MRPCode,
        factory: this.searchForm.factory,
        position: this.searchForm.warehouse
      }
      getMrpAreaList(params).then((data) => {
        this.exportExcel(data.records)
        this.loading.close()
      }).catch((error) => {
        console.log(error);
        this.loading.close()
      })
    },
    exportExcel (listData) {
      const mapping = {
        code: 'MRP区域编码',
        description: 'MRP区域描述',
        type: 'MRP区域类型',
        factory: '工厂',
        position: '仓库地点',
        isDefault: '默认仓库地点',
        gmtModify: '保存日期'
      }

      const list = listData.map(item => {
        const {
          code,
          description,
          type,
          factory,
          position,
          isDefault,
          gmtModify,
          gmtCreate
        } = item
        return {
          code,
          description,
          type: type === 1 ? '1 工厂' : '2 仓库地点',
          factory: factory + ' ' + (this.factoryList.find(item => item.factoryCode === factory) || {}).factoryName,
          position: position ? position + ' ' + (this.warehouseList.find(item => item.warehouseLocationCode === position) || {}).warehouseLocationName : '',
          isDefault: isDefault === 1 ? '是' : '否',
          gmtModify: gmtModify ? gmtModify.slice(0, 10) : '' || gmtCreate ? gmtCreate.slice(0, 10) : ''
        }
      })

      const allList = list.map(data => {
        Object.keys(data).forEach(key => {
          if (mapping[key]) {
            data[mapping[key]] = data[key];
            delete data[key]
          }
        })
        return data
      })

      writeFile(allList, `MRP区域设置 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: ['MRP区域编码', 'MRP区域描述', 'MRP区域类型', '工厂', '仓库地点', '默认仓库地点', '保存日期'] })
    }
  },
  watch: {
    'searchForm.factory': function (newVal, oldVal) {
      this.warehouseListFilterByFactory = this.warehouseList.filter(w => {
        return w.factoryCode === newVal
      })
      this.searchForm.position = ''
    },
    'warehouseList': {
      handler: function (newVal, oldVal) {
        const self = this
        this.warehouseListFilterByFactory1 = this.warehouseList.filter(w => {
          return w.factoryCode === self.dialogForm.factory
        })
      },
      deep: true,
      immediate: true
    },
    'dialogForm.factory': function (newVal, oldVal) {
      this.warehouseListFilter(newVal)
    }
  }
}
</script>

<style lang="scss" scoped>
.list-container {
  padding: 20px;
}
.operation {
  display: flex;
  justify-content: space-evenly;
}
.mzindex{
  z-index:9000 !important;
}
</style>
