<template>
  <div class="list-container">
    <DividerHeader>
      查询条件
      <i
        :class="[
          'alignRight',
          hideResult ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
        ]"
        style="float: right; margin: 13px 10px 0 0"
        @click="fold"
      ></i>
    </DividerHeader>
    <SummaryForm
      ref="ruleForm"
      :hideResult="hideResult"
      :purchaseList="purchaseList"
      :searchLoading="searchLoading"
      :userRole="userRole"
      :initHistoryMounted="initHistoryMounted"
      @getReportType="getReportType"
      @filterPurchaseList="filterPurchaseList"
      @handleSearch="handleSearch"
      :soParams="soParams"
    />

    <div
      v-if="!reportType"
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
    >
      <SummaryGrid
        ref="summaryGrid"
        :tableHeight="tableHeight"
        :listData="listData"
        :columns="columns"
        :tableToolbar="tableToolbar"
        :scroll="scroll"
        :transferSearchForm="transferSearchForm"
        :cacheSearchForm="cacheSearchForm"
        :handleDownExcel="handleDownExcel"
        :purchaseList="purchaseList"
        :formatQuantityUnit="formatQuantityUnit"
        :getNameAddQuantity="getNameAddQuantity"
        :tablePage="tablePage"
        :handlePageChange="handlePageChange"
      />
    </div>

    <div
      v-else
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
    >
      <vxe-grid
        column-key
        border
        resizable
        auto-resize
        keep-source
        show-overflow
        show-header-overflow
        ref="avariableGrid"
        :height="tableHeight"
        id="mrp-avariable_grid"
        size="mini"
        row-id="id"
        align="center"
        :custom-config="tableCustom"
        :data="aStockListData"
        :columns="columns"
        :toolbar-config="tableToolbar"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
        @cell-dblclick="cellClick"
        highlight-hover-row
        :scroll-y="{ gt: 20 }"
        @scroll="scroll"
      >
        <template v-slot:toolbar_tools>
          <el-button
            type="primary"
            style="width: 80px; margin-right: 10px"
            :disabled="listData.length === 0"
            @click="handleDownExcel"
          >
            下载
          </el-button>
        </template>
        <template v-slot:mrpArea_default="{ row }">
          {{ row.mrpArea + ' ' + row.mrpAreaDesc }}
        </template>
        <template v-slot:purchaseGroup_default="{ row }">
          {{
            row.purchaseGroup +
            ' ' +
            (
              purchaseList.find(
                (item) => item.groupCode === row.purchaseGroup
              ) || {}
            ).userName
          }}
        </template>
        <template v-slot:stockingStrategy_default="{ row }">
          {{ getStockingStrategyText(row) }}
        </template>
        <template v-slot:quantityUnit_default="{ row }">
          {{ formatQuantityUnit(row.quantityUnit) }}
        </template>
        <template #pager>
          <vxe-pager
            :layouts="[
              'Sizes',
              'PrevJump',
              'PrevPage',
              'Number',
              'NextPage',
              'NextJump',
              'FullJump',
              'Total'
            ]"
            :page-sizes="[100, 500, 1000, 2000]"
            :border="true"
            :current-page.sync="aTablePage.currentPage"
            :page-size.sync="aTablePage.pageSize"
            :total="aTablePage.total"
            @page-change="handlePageChange"
          >
          </vxe-pager>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getPrList, getAvailableStockList } from '@/api/mrp'
import { safeRun, throttle, initVersion } from '@/utils/index'
import { writeFile } from '@boss/excel'
import {
  summarMapping,
  summaryColumns,
  availableStockColumns,
  availableStockDownloadColumns,
  stockingStrategy,
  stockingStrategyEnum,
  supplierCertList
} from './constants'
import {
  formatPrListData,
  esokzName,
  getCurrency,
  setPower,
  getPurchasePrice,
  fold,
  parsePurchaseGroup,
  filterHiddenFields,
  dragFields,
  cellClick,
  getBomItemInventoryVoList
} from './utils'
import moment from 'moment'
import DividerHeader from '@/components/DividerHeader'
import SummaryForm from './components/summary/form.vue'
import SummaryGrid from './components/summary/grid.vue'
import Sortable from 'sortablejs'
import { pick, isEmpty, isEqual } from 'lodash'

export default {
  name: 'mrpSummary',
  components: {
    DividerHeader,
    SummaryForm,
    SummaryGrid
  },
  data() {
    return {
      searchForm: {},
      lastSearchForm: {}, // 记录上次搜索条件
      clearListData: false, // 是否清除之前的列表数据
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        zoom: true,
        slots: {
          tools: 'toolbar_tools'
        }
      },
      columns: summaryColumns,
      stockingStrategy,
      listData: [],
      aStockListData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 500
      },
      aTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 500
      },
      loading: null,
      cacheSearchForm: '',
      is_extending: true,
      disabling: true,
      hideResult: true,
      tableHeight: 590,
      transferSearchForm: '',
      filtPurchaseList: [],
      command: '',
      reportType: false,
      soParams: {} // 紧急工单跳转过来的传参
    }
  },
  async created() {
    const pList = []
    if (!Object.keys(this.dictList).length) {
      pList.push(this.$store.dispatch('orderCommon/queryDictList'))
    }
    if (this.purchaseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(
        this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
      )
    }
    if (!this.userRole || this.userRole.length === 0) {
      pList.push(this.$store.dispatch('getUserRole'))
    }
    await Promise.all(pList)
    this.columnDrop()
  },
  beforeMount() {
    initVersion({
      newVersion: '20240611',
      versionKey: 'mrpSummaryVersionKey',
      columKey: 'summaryGrid_fullColumn'
    })
    this.initHistoryMounted()
  },
  mounted() {
    this.$nextTick(function () {
      this.selfAdaption()
      window.addEventListener('resize', this.selfAdaption, false)
    })
    this.soParams = JSON.parse(localStorage.getItem('/mrp/summary'))
    console.log(localStorage.getItem('/mrp/summary'));
  },

  // beforeRouteEnter(to, from, next) {
  //   next((vm) => {
  //     vm.soParams = to?.query
  //   })
  // },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
    window.removeEventListener('resize', this.selfAdaption, false)
  },
  computed: {
    ...mapState({
      dictList: (state) => state.orderCommon.dictList || {},
      purchaseList: (state) => state.orderPurchase.purchaseList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      userRole: (state) => state.userRole
    }),
    tableKeyValue() {
      let obj = {}
      availableStockDownloadColumns.forEach((item) => {
        obj[item.field] = item.title
      })
      return obj
    },
    tableFields() {
      let arr = []
      availableStockDownloadColumns.forEach((item) => {
        arr.push(item.field)
      })
      return arr
    },
    getSafeStockText: () => {
      return function (row) {
        //  3个模式
        // 1：安全库存 → 安全库存
        // 2：ROP补批量 → 再订货点/舍入值
        // 3：ROP补最大 → 再订货点/最大库存
        const safeStockText = {
          [stockingStrategyEnum.SAFESTOCK]: row.safeStock,
          [stockingStrategyEnum.ROPBATCH]: `${row.reOrderPoint}/${row.packagingQuantity}`,
          [stockingStrategyEnum.ROPMAXSTOCK]: `${row.reOrderPoint}/${row.maxStock}`
        }
        // // console.log(row)
        if (!row.stockingStrategy) return ''
        if (row.stockingStrategy) {
          return safeStockText[row.stockingStrategy]
        }
      }
    },
    getStockingStrategyText() {
      return (row) => {
        if (!row.stockingStrategy) return ''
        if (row.stockingStrategy) {
          return (
            row.stockingStrategy +
            '-' +
            (
              stockingStrategy.find(
                (item) => item.value === row.stockingStrategy
              ) || { label: '' }
            ).label
          )
        }
      }
    }
  },
  methods: {
    getPurchasePrice,
    setPower,
    getCurrency,
    esokzName,
    cellClick,
    getBomItemInventoryVoList,
    selfAdaption: throttle(function selfAdaption() {
      if (!this.reportType) {
        this.tableHeight =
          window.innerHeight -
          this.$refs.summaryGrid.$refs.summaryGrid.$el.offsetTop -
          130
      } else {
        this.tableHeight =
          window.innerHeight - this.$refs.avariableGrid.$el.offsetTop - 130
      }
      if (this.tableHeight < 260) {
        this.tableHeight = 260
      }
    }, 800),
    getReportType(data) {
      this.reportType = data
      if (!data && this.clearListData) {
        this.listData = []
        this.tablePage.total = 0
      }
      if (data && this.clearListData) {
        this.aStockListData = []
        this.aTablePage.total = 0
      }
      this.clearListData = false

      this.columnDrop()
    },
    processNullToZero(value) {
      return value || 0
    },
    initHistoryMounted() {
      // debugger
      let fullcolumns
      if (!this.reportType && localStorage.getItem('summaryGrid_fullColumn')) {
        fullcolumns = JSON.parse(localStorage.getItem('summaryGrid_fullColumn'))
      }
      if (this.reportType && localStorage.getItem('avariableGrid_fullColumn')) {
        fullcolumns = JSON.parse(
          localStorage.getItem('avariableGrid_fullColumn')
        )
      }
      if (fullcolumns) {
        this.columns = fullcolumns.map((item) => {
          item.field = item.property
          return item
        })
      } else {
        if (!this.reportType) {
          this.columns = summaryColumns
        } else {
          this.columns = availableStockColumns
        }
      }
    },
    filterPurchaseList(list) {
      this.filtPurchaseList = list
    },
    fold() {
      if (!this.reportType) {
        fold(
          this,
          this.$refs.summaryGrid.$refs.summaryGrid,
          'hideResult',
          this.hideResult
        )
      } else {
        fold(this, this.$refs.avariableGrid, 'hideResult', this.hideResult)
      }
    },
    formatQuantityUnit(value) {
      if (value && this.dictList) {
        const unit = this.dictList['quantityUnit'].find(
          (item) => '' + item.code === value
        )
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    handleSearch(data) {
      this.searchForm = data
      this.cacheSearchForm = JSON.stringify({ ...this.searchForm })
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$refs.summaryGrid &&
          this.$refs.summaryGrid.$refs.summaryGrid.clearFilter()
          !this.reportType ? this.tablePage.currentPage = 1 : this.aTablePage.currentPage = 1
          this.getPrListData()
          if (this.$refs.summaryGrid) {
            // 父组件修改子组件的值
            this.$refs.summaryGrid.selectList = []
          }
        } else {
          return false
        }
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      // 没有点击查询按钮时，直接点击分页获取查询条件
      this.searchForm = this.$refs.ruleForm.searchForm
      this.cacheSearchForm = JSON.stringify({ ...this.searchForm })
      if (!this.reportType) {
        this.tablePage.currentPage = currentPage
        this.tablePage.pageSize = pageSize
      } else {
        this.aTablePage.currentPage = currentPage
        this.aTablePage.pageSize = pageSize
      }
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.getPrListData()
          if (this.$refs.summaryGrid) {
            // 父组件修改子组件的值
            this.$refs.summaryGrid.selectList = []
          }
        } else {
          return false
        }
      })
    },
    formatParams(params) {
      let form = { ...params }
      form.skuNo = safeRun(() => form.skuNo.split(/\s/).filter((e) => e))
      // form.mrpArea = safeRun(() =>
      //   form.mrpArea
      //     .split(/\s/).filter((e) => e)
      // );
      delete form.brands
      return form
    },
    validate(params) {
      let ret = true
      safeRun(() => {
        if (params.skuNo.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
        if (params.productGroup.length > 10) {
          ret = false
          this.$message.error('最多支持10个物料组按空格隔开搜索！')
        }
        if (params.mrpArea.length > 10) {
          ret = false
          this.$message.error('最多支持10个MRP区域按空格隔开搜索！')
        }
      })
      return ret
    },
    async getPrListData() {
      try {
        let params = this.formatParams(this.searchForm)
        if (!this.validate(params)) return

        parsePurchaseGroup(
          params,
          setPower(this.userRole),
          this.filtPurchaseList
        )

        if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
        }
        if (Array.isArray(params.productGroup)) {
          params.productGroup = params.productGroup.join(' ')
        }
        if (Array.isArray(params.mrpArea)) {
          params.mrpArea = params.mrpArea.join(' ')
        }
        if (Array.isArray(params.purchaseGroup)) {
          params.purchaseGroup = params.purchaseGroup.join(' ')
        }

        if (!params.purchaseGroup) {
          return this.$message.error('没有匹配的采购组')
        }

        this.transferSearchForm = JSON.stringify(params)
        this.tableLoading = true
        this.searchLoading = true

        // const res = await getPrList(params)
        let res
        if (!this.reportType) {
          params = {
            ...params,
            pageNo: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize
          }
          res = await getPrList(params)
          this.listData = res.records.map((item) => {
            return formatPrListData(item)
          })

          this.tablePage.total = res.total
          if (this.listData.length === 0) {
            this.$message.info('无符合条件的物料需求')
            this.disabling = true
          } else {
            this.disabling = false
          }
        } else {
          params = {
          ...params,
            pageNo: this.aTablePage.currentPage,
            pageSize: this.aTablePage.pageSize
          }
          res = await getAvailableStockList(params)
          this.aStockListData = res.records

          this.aTablePage.total = res.total

          if (this.aStockListData.length === 0) {
            this.$message.info('无符合条件的物料需求')
            this.disabling = true
          } else {
            this.disabling = false
          }
        }

        if (isEmpty(this.lastSearchForm)) {
          this.lastSearchForm = { ...this.searchForm }
        } else if (!isEqual(this.searchForm, this.lastSearchForm)) {
          // 搜索成功后判断搜索条件是否改变，改变则标识切换负需求/可用库存后需要清空之前的data
          this.lastSearchForm = { ...this.searchForm }
          this.clearListData = true
        }

        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error)
        this.is_extending = false
      }
    },
    async getExtendPrListData() {
      try {
        let params = this.formatParams(this.searchForm)
        if (!this.validate(params)) return

        parsePurchaseGroup(
          params,
          setPower(this.userRole),
          this.filtPurchaseList
        )

        if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
        }
        if (Array.isArray(params.productGroup)) {
          params.productGroup = params.productGroup.join(' ')
        }
        if (Array.isArray(params.mrpArea)) {
          params.mrpArea = params.mrpArea.join(' ')
        }
        if (Array.isArray(params.purchaseGroup)) {
          params.purchaseGroup = params.purchaseGroup.join(' ')
        }

        this.tableLoading = true
        this.searchLoading = true
        if (!this.reportType) {
          this.tablePage.currentPage++
          params = {
            ...params,
            pageNo: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize
          }

          const res = await getPrList(params)
          const list = res.records.map((item) => {
            return formatPrListData(item)
          })
          this.listData = this.listData.concat(list)
        } else {
          this.aTablePage.currentPage++
          params = {
            ...params,
            pageNo: this.aTablePage.currentPage,
            pageSize: this.aTablePage.pageSize
          }
          const res = await getAvailableStockList(params)
          this.aStockListData = this.aStockListData.concat(res.records)
        }

        this.is_extending = true
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error)
        this.is_extending = false
      }
    },
    scroll({ scrollTop }) {
      let compareHeight
      if (
        (!this.reportType &&
          this.$refs.summaryGrid.$refs.summaryGrid.isMaximized()) ||
        (this.reportType && this.$refs.avariableGrid.isMaximized())
      ) {
        compareHeight =
          this.tablePage.pageSize * this.tablePage.currentPage * 36 - 805
      } else {
        // 是否折叠时显示的grid高度不同
        compareHeight =
          this.tablePage.pageSize * this.tablePage.currentPage * 36 -
          this.tableHeight +
          120
      }
      if (scrollTop >= compareHeight) {
        if (this.is_extending) {
          this.is_extending = false
          this.getExtendPrListData()
        }
      }
    },
    async handleDownExcel() {
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = this.formatParams(this.searchForm)
          if (!this.validate(params)) return

          parsePurchaseGroup(
            params,
            setPower(this.userRole),
            this.filtPurchaseList
          )

          if (Array.isArray(params.skuNo)) {
            params.skuNo = params.skuNo.join(' ')
          }
          if (Array.isArray(params.productGroup)) {
            params.productGroup = params.productGroup.join(' ')
          }
          if (Array.isArray(params.mrpArea)) {
            params.mrpArea = params.mrpArea.join(' ')
          }
          if (Array.isArray(params.purchaseGroup)) {
            params.purchaseGroup = params.purchaseGroup.join(' ')
          }

          if (!params.purchaseGroup) {
            return this.$message.error('没有匹配的采购组')
          }

          params = {
            ...params,
            pageNo: 1,
            pageSize: -1,
            action: 'download'
          }
          this.loading = this.$loading({
            background: 'rgba(0, 0, 0, 0.5)'
          })
          if (!this.reportType) {
            getPrList(params)
              .then((res) => {
                const list = res.records.map((item) => {
                  return formatPrListData(item)
                })
                this.exportExcel(list)
                this.loading.close()
              })
              .catch((error) => {
                console.log(error)
                this.loading.close()
              })
          } else {
            getAvailableStockList(params)
              .then((res) => {
                const list = res.records
                this.exportExcel(list)
                this.loading.close()
              })
              .catch((error) => {
                console.log(error)
                this.loading.close()
              })
          }
        } else {
          return false
        }
      })
    },
    pickPointedPropety(object, keyArr) {
      return pick(object, keyArr)
    },
    exportExcel(listData) {
      if (!this.reportType) {
        let mapping = summarMapping
        mapping = dragFields('summaryGrid_fullColumn', mapping, this)
        mapping = filterHiddenFields(mapping, 'mrp-summary_grid')
        const list = listData.map((item) => {
          const {
            factory,
            skuNo,
            mrpArea,
            mrpAreaDesc,
            purchaseGroup,
            isSpec,
            quantity,
            quantityUnit,
            specialDemandQuantity,
            skuFactoryStrategy,
            originQuantity,
            replenishmentQuantity,
            fsoQuantity,
            soQuantity,
            safeStockQuantity,
            onWayQuantity,
            wipQuantity,
            prType,
            cloudWareHouse,
            abutmentStock,
            totalWeight,
            inventoryVo: {
              selfInventory1,
              selfInventory1Quantity,
              selfInventory2,
              selfInventory2Quantity,
              promotionInventory,
              promotionInventoryQuantity,
              otherInventory1,
              otherInventory1Name,
              otherInventory1Quantity,
              otherTransportationInventory1,
              otherTransportationInventory1Name,
              otherTransportationInventory1Quantity,
              hignStorageInStock1MrpArea,
              hignStorageInStock1MrpAreaName,
              hignStorageInStock1Quantity,
              hignStorageInStock2MrpArea,
              hignStorageInStock2MrpAreaName,
              hignStorageInStock2Quantity
            },
            productVO: {
              inventoryTypeName,
              materialDescribe,
              productGroup,
              productGroupName,
              mstaeAllSwitch,
              productPositioningName
            },
            inStockQuantity,
            otherQuantity,
            orderCalendar,
            supplier1Detail,
            supplier2Detail,
            supplier3Detail,
            recommendSupplier1Detail,
            recommendSupplier2Detail,
            recommendSupplier3Detail,
            customerSpecifiedPurchasePriceVO,
            poItemHistoryPrice,
            grossMargin,
            grossAmount
          } = item
          const isVpi = !!prType
          const relSupplier1 = (isVpi || item.customerSpecified) ? customerSpecifiedPurchasePriceVO : supplier1Detail
          return {
            externalOrderNo: item.externalOrderNo,
            externalOrderItemNo: item.externalOrderItemNo,
            isSpecialOrder: item.prTag?.isSpecialOrder ? '是' : '否',
            isOutOfStock: item.prTag?.isOutOfStock ? '是' : '否',
            isMstaeState: item.prTag?.isMstaeState ? '是' : '否',
            isUrgent: item.prTag?.isUrgent ? '是' : '否',
            supplierCert: supplierCertList.find(it => it.value === item.supplierCert)?.label || '',
            soDeliveryDate: item.soDeliveryDate || '',
            supplierArriveDate: item.supplierArriveDate || '',
            pendingOrderReason: (item.pendingReasonName || '') + ' ' + (item.detailPendingReasonDesc || '') + ' ' + (item.pendingReasonDesc || ''),
            mpq: item.supplier1Detail?.purchaseMpq || '',
            inStockQuantity,
            otherQuantity,
            skuTagMsg: this.getSkuTagMsg(item),
            skuNo,
            factory,
            mrpArea,
            mrpAreaDesc,
            originQuantity,
            replenishmentQuantity,
            fsoQuantity,
            soQuantity,
            safeStockQuantity,
            onWayQuantity,
            wipQuantity,
            materialDescribe,
            productGroup,
            productGroupName,
            inventoryTypeName,
            cloudWareHouse: cloudWareHouse || (productGroup === 430 ? '无结果' : ''),
            abutmentStock: abutmentStock || (productGroup === 430 ? '无结果' : ''),
            totalWeight: totalWeight || (productGroup === 430 ? '未维护单重' : ''),
            mstaeAllSwitch: mstaeAllSwitch === 1 ? '是' : '否',
            purchaseGroup,
            userName: (
              this.purchaseList.find(
                (item) => item.groupCode === purchaseGroup
              ) || {}
            ).userName,
            isSpec: isSpec === 1 ? '有' : '',
            quantity,
            quantityUnit: this.formatQuantityUnit(quantityUnit),
            skuFactoryStrategy,
            productPositioningName,
            specialDemandQuantity: -specialDemandQuantity,
            selfInventory1: this.getNameAddQuantity(
              selfInventory1,
              selfInventory1Quantity
            ),
            selfInventory2: this.getNameAddQuantity(
              selfInventory2,
              selfInventory2Quantity
            ),
             promotionInventoryQuantity: this.getNameAddQuantity(
              promotionInventory,
              promotionInventoryQuantity
            ),
            otherInventory1:
              otherInventory1Quantity > 0
                ? otherInventory1Quantity +
                '/' +
                otherInventory1 +
                '/' +
                otherInventory1Name
                : '',
            otherTransportationInventory1:
              otherTransportationInventory1Quantity > 0
                ? otherTransportationInventory1Quantity +
                '/' +
                otherTransportationInventory1 +
                '/' +
                otherTransportationInventory1Name
                : '',
            hignStorageInStock1MrpArea:
              hignStorageInStock1Quantity > 0
                ? hignStorageInStock1Quantity +
                '/' +
                hignStorageInStock1MrpArea +
                '/' +
                hignStorageInStock1MrpAreaName
                : '',
            hignStorageInStock2MrpArea:
              hignStorageInStock2Quantity > 0
                ? hignStorageInStock2Quantity +
                '/' +
                hignStorageInStock2MrpArea +
                '/' +
                hignStorageInStock2MrpAreaName
                : '',
            purchasePriceVOList0:
              (isVpi
                ? customerSpecifiedPurchasePriceVO.providerNo
                : supplier1Detail.providerNo) || '',
            bomItemInventory: this.getBomItemInventoryVoList(item),
            purchasePrice0Name:
              (isVpi
                ? customerSpecifiedPurchasePriceVO.providerName
                : supplier1Detail.providerName) || '',
            purchasePrice0esokz: supplier1Detail.providerNo
              ? this.esokzName(supplier1Detail.esokz)
              : '',
            purchasePrice0currency: this.getCurrency(supplier1Detail.currency),
            purchasePrice0taxRate: isVpi
              ? customerSpecifiedPurchasePriceVO.providerNo
                ? customerSpecifiedPurchasePriceVO.taxRate + '%'
                : ''
              : supplier1Detail.providerNo
                ? supplier1Detail.taxRate + '%'
                : '',
            purchasePrice0meins:
              this.formatQuantityUnit(supplier1Detail.meins) || '',
            purchasePrice0purchaseMoq: supplier1Detail.purchaseMoq || '',
            purchasePrice0ifBid: supplier1Detail.providerNo
              ? supplier1Detail.ifBid || '否'
              : '',
            purchasePrice0ifIncludeFreight: supplier1Detail.providerNo
              ? supplier1Detail.ifIncludeFreight === 1
                ? '是'
                : '否'
              : '',
            purchasePrice0freightRate: supplier1Detail.freightRate || '',
            purchasePrice0paymentTermsCodeName:
              supplier1Detail.paymentTermsCodeName || '',
            purchasePrice0ifUpInventory: supplier1Detail.providerNo
              ? supplier1Detail.ifUpInventory || 0
              : '',
            purchasePrice0supplierClassifyName:
              supplier1Detail.supplierClassifyName || '',
            purchasePrice0oemDirectSupplier: relSupplier1.providerNo ? (relSupplier1.oemDirectSupplier === 1 ? '是' : '否') : '',
            purchasePrice0idnlf: supplier1Detail.idnlf,
            purchasePrice0ifSignFrameworkAgreement: supplier1Detail.providerNo
              ? supplier1Detail.ifSignFrameworkAgreement === 1
                ? '是'
                : '否'
              : '',
            purchasePrice0Price: this.getPurchasePrice(
              isVpi ? customerSpecifiedPurchasePriceVO : supplier1Detail
            ),
            purchasePrice0LeadTime: supplier1Detail.leadTime,
            purchasePrice0NearestOrderDate: orderCalendar.nearestOrderDate,
            purchasePrice0tacticsName: supplier1Detail.tacticsName,
            purchasePrice0ifAgency: supplier1Detail.ifAgency === 0 ? '否' : supplier1Detail.ifAgency === 1 ? '是' : '',
            purchasePrice0ifTradeCertificate: supplier1Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier1Detail.ifTradeCertificate) ? '是' : '',
            purchasePriceVOList1: supplier2Detail.providerNo || '',
            purchasePrice1Name: supplier2Detail.providerName || '',
            purchasePrice1ifUpInventory: supplier2Detail.providerNo
              ? supplier2Detail.ifUpInventory || 0
              : '',
            purchasePrice1ifIncludeFreight: supplier2Detail.providerNo
              ? supplier2Detail.ifIncludeFreight === 1
                ? '是'
                : '否'
              : '',
            purchasePrice1freightRate: supplier2Detail.freightRate || '',
            purchasePrice1supplierClassifyName:
              supplier2Detail.supplierClassifyName || '',
            purchasePrice1oemDirectSupplier: supplier2Detail.providerNo ? (supplier2Detail.oemDirectSupplier === 1 ? '是' : '否') : '',
            purchasePrice1idnlf: supplier2Detail.idnlf,
            purchasePrice1ifSignFrameworkAgreement: supplier2Detail.providerNo
              ? supplier2Detail.ifSignFrameworkAgreement === 1
                ? '是'
                : '否'
              : '',
            purchasePrice1Price: this.getPurchasePrice(supplier2Detail),
            purchasePrice1LeadTime: supplier2Detail.leadTime,
            purchasePrice1tacticsName: supplier2Detail.tacticsName,
            purchasePrice1ifAgency: supplier2Detail.ifAgency === 0 ? '否' : supplier2Detail.ifAgency === 1 ? '是' : '',
            purchasePrice1ifTradeCertificate: supplier2Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier2Detail.ifTradeCertificate) ? '是' : '',
            purchasePriceVOList2: supplier3Detail.providerNo || '',
            purchasePrice2Name: supplier3Detail.providerName || '',
            purchasePrice2ifUpInventory: supplier3Detail.providerNo
              ? supplier3Detail.ifUpInventory || 0
              : '',
            purchasePrice2ifIncludeFreight: supplier3Detail.providerNo
              ? supplier3Detail.ifIncludeFreight === 1
                ? '是'
                : '否'
              : '',
            purchasePrice2freightRate: supplier3Detail.freightRate || '',
            purchasePrice2supplierClassifyName:
              supplier3Detail.supplierClassifyName || '',
            purchasePrice2oemDirectSupplier: supplier3Detail.providerNo ? (supplier3Detail.oemDirectSupplier === 1 ? '是' : '否') : '',
            purchasePrice2idnlf: supplier3Detail.idnlf,
            purchasePrice2ifSignFrameworkAgreement: supplier3Detail.providerNo
              ? supplier3Detail.ifSignFrameworkAgreement === 1
                ? '是'
                : '否'
              : '',
            purchasePrice2Price: this.getPurchasePrice(supplier3Detail),
            purchasePrice2LeadTime: supplier3Detail.leadTime,
            purchasePrice2tacticsName: supplier3Detail.tacticsName,
            poItemHistoryPrice,
            purchasePrice2ifAgency: supplier3Detail.ifAgency === 0 ? '否' : supplier3Detail.ifAgency === 1 ? '是' : '',
            purchasePrice2ifTradeCertificate: supplier3Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier3Detail.ifTradeCertificate) ? '是' : '',
            recommendSupplier1Detail,
            recommendSupplier2Detail,
            recommendSupplier3Detail,
            grossMargin,
            ifGrossMargin: item.ifGrossMargin ? '是' : '否',
            stableMark: item.stableMark === 'Y' ? '是' : '否',
            grossAmount
          }
        })

        const allList = list.map((data) => {
          Object.keys(data).forEach((key) => {
            if (mapping[key]) {
              data[mapping[key]] = data[key]
            }
            delete data[key]
          })
          return data
        })
        console.log(allList, 'all')
        writeFile(
          allList,
          `物料需求报表 – 汇总 – 负需求 ${moment(new Date()).format(
            'YYYY-MM-DD HH-mm-ss'
          )}.xlsx`,
          { header: Object.values(mapping) }
        )
      } else {
        let mapping = this.tableKeyValue
        mapping = dragFields('avariableGrid_fullColumn', mapping, this)
        mapping = filterHiddenFields(mapping, 'mrp-avariable_grid')
        const list = listData.map((row) => {
          let filterRow = this.pickPointedPropety(row, this.tableFields)
          filterRow.userName = (
            this.purchaseList.find(
              (item) => item.groupCode === row.purchaseGroup
            ) || {}
          ).userName
          filterRow.stockingStrategy = (
            stockingStrategy.find(
              (item) => item.value === row.stockingStrategy
            ) || {}
          ).label
          filterRow.quantityUnit = this.formatQuantityUnit(row.quantityUnit)
          return filterRow
        })
        const allList = list.map((item) => {
          const data = { ...item }
          Object.keys(data).forEach((key) => {
            if (mapping[key]) {
              data[mapping[key]] = data[key]
            }
            delete data[key]
          })
          return data
        })
        writeFile(
          allList,
          `物料需求报表 – 汇总 – 可用库存 ${moment(new Date()).format(
            'YYYY-MM-DD HH-mm-ss'
          )}.xlsx`,
          { header: Object.values(mapping) }
        )
      }
    },
    getNameAddQuantity(code, quantity) {
      let find = this.warehouseList.find(
        (item) => item.warehouseLocationCode === code
      )
      if (find && quantity > 0) {
        return quantity + '/' + code + '/' + find.warehouseLocationName
      } else {
        return ''
      }
    },
    columnDrop() {
      this.$nextTick(() => {
        // const $table = this.$refs.avariableGrid
        let $table
        if (!this.reportType) {
          $table = this.$refs.summaryGrid.$refs.summaryGrid
        } else {
          $table = this.$refs.avariableGrid
        }
        this.sortable2 = Sortable.create(
          $table.$el.querySelector(
            '.body--wrapper>.vxe-table--header .vxe-header--row'
          ),
          {
            handle: '.vxe-header--column:not(.col--fixed)',
            onEnd: ({ item, newIndex, oldIndex }) => {
              const { fullColumn, tableColumn } = $table.getTableColumn()

              // 目标表头
              const targetThElem = item
              const wrapperElem = targetThElem.parentNode
              // 被移到位置的数据
              const newColumn = fullColumn[newIndex]
              if (newColumn.fixed) {
                // 错误的移动
                if (newIndex > oldIndex) {
                  wrapperElem.insertBefore(
                    targetThElem,
                    wrapperElem.children[oldIndex]
                  )
                } else {
                  // （被移到位置的数据）前插入 （要移动的数据）
                  wrapperElem.insertBefore(
                    // 要移动的数据
                    wrapperElem.children[oldIndex],
                    // 被移到位置的数据
                    targetThElem
                  )
                }
                return this.$message.error('固定列不允许拖动！')
              }
              // 转换真实索引
              const oldColumnIndex = $table.getColumnIndex(
                // 找到数据
                tableColumn[oldIndex]
              )
              const newColumnIndex = $table.getColumnIndex(
                tableColumn[newIndex]
              )
              // 移动到目标列
              const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
              fullColumn.splice(newColumnIndex, 0, currRow)
              $table.loadColumn(fullColumn)

              if (!this.reportType) {
                localStorage.setItem(
                  'summaryGrid_fullColumn',
                  JSON.stringify(fullColumn)
                )
              } else {
                localStorage.setItem(
                  'avariableGrid_fullColumn',
                  JSON.stringify(fullColumn)
                )
              }
            }
          }
        )
      })
    },
    getSkuTagMsg(row) {
      if (row.leadTime === 0) {
        return '此商品未获取期货LT;'
      }
      return ''
    }
  }
}
</script>

<style lang="scss" scoped>
  .list-container {
    padding: 5px 20px;
  }

  .el-icon-arrow-up.alignRight {
    font-size: 25px;
  }

  .el-icon-arrow-down.alignRight {
    font-size: 25px;
  }
</style>
