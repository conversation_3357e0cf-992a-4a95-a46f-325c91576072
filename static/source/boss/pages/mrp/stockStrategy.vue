<template>
  <div class="stock-strategy">
    <el-form ref="ruleForm" :model="searchForm" style="width: 100%" label-suffix=":" label-width="80px">
      <el-row :gutter="40">
        <el-col :span="10">
          <el-form-item label="工厂" prop="factory">
            <el-select
              v-model="searchForm.factory"
              filterable
              default-first-option
              clearable
              multiple
              collapse-tags
              style="width:100%"
              placeholder="请选择工厂"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factoryCode+item.factoryName"
                :label="item.factoryCode+' '+item.factoryName"
                :value="item.factoryCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="MRP区域" prop="mrpArea">
            <el-input
              v-model="searchForm.mrpArea"
              placeholder="最多10个值,空格隔开"
              clearable
              style="width:100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="10">
          <el-form-item label="修改者" prop="modifier">
            <el-input
              v-model="searchForm.modifier"
              clearable
              style="width:100%"
              placeholder="单值输入，域账号/来源系统"
            />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="SKU" prop="skuNo">
            <el-input
              v-model="searchForm.skuNo"
              clearable
              style="width:100%"
              placeholder="最多100个值,空格隔开"
            />
          </el-form-item>
        </el-col>
        <el-col :span="4" :style="{display: 'flex',justifyContent: 'flex-end'}">
          <el-button
            type="primary"
            style="width: 80px; margin-right: 45.19px "
            :loading="searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <vxe-grid
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="stockGrid"
      height="688"
      id="stock_grid"
      row-id="id"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'manual', mode: 'row', autoClear: false, showIcon: false, showStatus: true}"
      :checkbox-config="{reserve: true}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
      highlight-hover-row
    >
      <template v-slot:toolbar_buttons>
        <el-button v-if="show" type="danger" size="mini" plain @click="removeCheckboxRow" :disabled="disabled">批量删除</el-button>
        <el-button type="primary" size="mini" @click="handleDownExcel">全部下载</el-button>
      </template>

      <template v-slot:toolbar_tools>
        <el-button v-if="show" type="primary" size="mini" @click="addStock">新增配置</el-button>
        <el-button v-if="show" type="primary" size="mini" plain @click="handleDownloadTemplateHeader">下载模板</el-button>
        <el-upload
          v-if="show"
          ref="upload"
          style="display:inline-block;margin-left:10px"
          accept=".xlsx"
          :before-upload="$validateFileType"
          action="/api-mrp/safeStock/batchImport"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="handleUpload"
          :show-file-list="false"
        >
          <el-button type="primary" size="mini" plain class="btn">批量上传</el-button>
        </el-upload>
      </template>

      <template v-slot:type_default="{ row }">
        {{ getType(row.type) }}
      </template>

      <template v-slot:modifier_default="{ row }">
        {{ row.modifier === 'BI' ? row.modifier +' '+ row.forecastDate : row.modifier }}
      </template>

      <template v-slot:operation="{ row }">
        <div v-if="$refs.stockGrid.isActiveByRow(row)">
          <el-button type="text" size="medium" @click="saveRow(row)">保存</el-button>
          <el-button type="text" size="medium" @click="cancel(row)">取消</el-button>
        </div>
        <div  v-else class="operation">
          <el-button type="text" @click="editRow(row)" size="medium" :style="{marginLeft: 0}" :disabled="!show">修改</el-button>
        </div>
      </template>

      <template #pager>
        <vxe-pager
          :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
          :border="true"
          :current-page.sync="tablePage.currentPage"
          :page-size.sync="tablePage.pageSize"
          :total="tablePage.total"
          @page-change="handlePageChange">
        </vxe-pager>
      </template>
    </vxe-grid>

    <addAndModify
      ref="children"
      :dialogTitle="dialogTitle"
      :visible.sync="dialogVisible"
      :disabling="disabling"
      :getStockListData="getStockListData"
      @updateVisible="updateVisible"
      @resetPopupData="resetPopupData"
      @submitPopupData="submitPopupData"
      :factoryList="factoryList"
      :dialogForm="dialogForm"
      @updateDialogForm="updateDialogForm"
      @emptyToZero="emptyToZero"
      :mrpAreaList="mrpAreaList"
      :mrpAreaListFilterByFactory="mrpAreaListFilterByFactory"
    >
    </addAndModify>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getStockList, deleteStock, batchUploadStock, updateStock, getUserInfo } from '@/api/mrp'
import { safeRun } from '@/utils/index'
import { writeFile } from '@boss/excel'
import { excelUrls } from './constants'
import addAndModify from './components/addAndModify'
import moment from 'moment'
import { getFactoryList } from './utils'

const columns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    field: 'factory',
    title: '工厂',
    minWidth: 50,
    fixed: 'left'
  },
  {
    field: 'skuNo',
    title: 'SKU',
    minWidth: 80,
    fixed: 'left'
  },
  {
    field: 'skuDesc',
    title: 'SKU描述',
    minWidth: 80
  },
  {
    field: 'productGroup',
    title: '物料组',
    minWidth: 80
  },
  {
    field: 'productGroupDesc',
    title: '物料组描述',
    minWidth: 120
  },
  {
    field: 'mrpArea',
    title: 'MRP区域',
    minWidth: 80
  },
  {
    field: 'mrpAreaDesc',
    title: 'MRP区域描述',
    minWidth: 120
  },
  {
    field: 'type',
    title: '备货策略',
    minWidth: 80,
    slots: {
      default: 'type_default'
    }
  },
  {
    field: 'safeStock',
    title: '安全库存',
    minWidth: 80
  },
  {
    field: 'reOrderPoint',
    title: '再订货点',
    minWidth: 80
  },
  {
    field: 'packagingQuantity',
    title: '舍入值',
    minWidth: 80
  },
  {
    field: 'maxStock',
    title: '最大库存',
    minWidth: 80
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 80
  },
  {
    field: 'modifier',
    title: '修改者',
    minWidth: 120,
    slots: {
      default: 'modifier_default'
    }
  },
  {
    title: '操作',
    slots: {
      default: 'operation'
    },
    minWidth: 80,
    fixed: 'right'
  }
]

export default {
  name: 'stockStrategy',
  components: {
    addAndModify
  },
  data () {
    return {
      searchForm: {
        factory: [],
        mrpArea: '',
        skuNo: '',
        modifier: ''
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons',
          tools: 'toolbar_tools'
        }
      },
      columns,
      tableData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 15
      },
      dialogTitle: '',
      dialogVisible: false,
      selectList: [],
      fileList: [],
      file: '',
      disabled: true,
      disabling: false,
      dialogForm: {},
      mrpAreaListFilterByFactory: [],
      show: false
    }
  },
  async created () {
    const pList = []
    if (this.companyInfoList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryCompanyInfoList'))
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (!this.mrpAreaList || this.mrpAreaList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryMrpAreaList'))
    }
    await Promise.all(pList)
    await this.getUserRole()
    await this.getStockListData()
  },
  computed: {
    ...mapState({
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      mrpAreaList: state => state.mrp.mrpAreaList,
      companyInfoList: state => state.mrp.companyInfoList
    }),
    factoryList () {
      return getFactoryList(this.companyFactoryList)
    }
  },
  watch: {
    'dialogForm.factory': function (newValue) {
      this.mrpAreaListFilter(newValue)
    }
  },
  methods: {
    async getUserRole () {
      try {
        const res = await getUserInfo({
          username: window.CUR_DATA.user && window.CUR_DATA.user.name
        })
        let defaultCompany = (this.companyInfoList.find(item => item.subCompanyId === res.subCompany) || {}).code
        if (defaultCompany) {
          this.searchForm.factory = ((this.companyFactoryList.find(item => item.companyCode === defaultCompany) || {}).factoryList || []).map(item => item.factoryCode)
        }
        const roleInfo = res.roleInfoList.find(item => item.name === 'MRP-计划团队' || item.name === 'MRP-管理员')
        if (roleInfo) {
          this.show = true
        }
      } catch (error) {
        console.log(error);
      }
    },
    getType (type) {
      if (type === 1 || type === '1') {
        return '1-安全库存 '
      }
      if (type === 2 || type === '2') {
        return '2-ROP补批量'
      }
      if (type === 3 || type === '3') {
        return '3-ROP补到最大库存'
      }
      if (type === 4 || type === '4') {
        return '4-EVM叫料'
      }
    },
    handleSearch () {
      this.tablePage.currentPage = 1
      this.getStockListData()
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getStockListData()
    },
    formatParams(params) {
      let form = { ...params };
      form.skuNo = safeRun(() =>
        form.skuNo
          .split(/\s/).filter((e) => e)
      );
      form.mrpArea = safeRun(() =>
        form.mrpArea
          .split(/\s/).filter((e) => e)
      );
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.skuNo.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
        if (params.mrpArea.length > 10) {
          ret = false
          this.$message.error('最多支持10个MRP区域按空格隔开搜索！')
        }
      })
      return ret
    },
    async getStockListData () {
      try {
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return
        if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
        }
        if (Array.isArray(params.mrpArea)) {
          params.mrpArea = params.mrpArea.join(' ')
        }
        if (Array.isArray(params.factory)) {
          params.factory = params.factory.join(' ')
        }
        params = {
          ...params,
          pageNo: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await getStockList(params)
        this.tableData = res.records
        this.tablePage.total = res.total
        if (this.tableData.length === 0) {
          this.$message.info('没有符合条件的备货策略')
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
      }
    },
    updateVisible (val) {
      this.dialogVisible = val
    },
    resetPopupData () {
      this.dialogVisible = false
    },
    submitPopupData () {
      this.dialogVisible = false
    },
    updateDialogForm (prop) {
      this.dialogForm[prop] = ''
    },
    emptyToZero (prop) {
      this.dialogForm[prop] = 0
    },
    mrpAreaListFilter (val) {
      this.mrpAreaListFilterByFactory = this.mrpAreaList.filter(item => item.factory === val)
    },
    addStock () {
      this.dialogTitle = '新增策略参数配置'
      this.dialogVisible = true
      this.disabling = false
      this.dialogForm = {
        factory: '1000',
        mrpArea: '',
        skuNo: '',
        type: '',
        safeStock: '',
        reOrderPoint: '',
        packagingQuantity: '',
        maxStock: '',
        remark: ''
      }
      // 移除表单项的校验结果
      setTimeout(() => {
        this.$refs.children.$refs.dialogForm.clearValidate()
      }, 0)
    },
    selectChange ({ checked, row }) {
      if (checked) {
        this.selectList.push(row)
      } else {
        this.selectList = this.selectList.filter(item => item.id !== row.id)
      }
      this.disable()
    },
    selectAll ({ checked, reserves, records }) {
      if (checked) {
        this.selectList = [...reserves, ...records]
      } else {
        this.selectList = reserves
      }
      this.disable()
    },
    disable () {
      if (this.selectList.length > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    removeCheckboxRow () {
      this.$confirm('您确定要删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteCheckbox()
      }).catch(() => {

      });
    },
    async deleteCheckbox () {
      try {
        let data = this.selectList.map(item => item.id)
        data = data.join(' ')
        const res = await deleteStock({ idList: data })
        if (res.code === 200) {
          this.$message.success(res.data)
          this.selectList = []
          this.disable()
          this.getStockListData()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleDownExcel () {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      let params = { ...this.searchForm }
      if (Array.isArray(params.factory)) {
        params.factory = params.factory.join(' ')
      }
      params = {
        ...params,
        pageNo: 1,
        pageSize: -1
      }
      getStockList(params).then((res) => {
        const list = res.records
        this.exportExcel(list)
        this.loading.close()
      }).catch((error) => {
        console.log(error);
        this.loading.close()
      })
    },
    exportExcel (listData) {
      const mapping = {
        factory: '工厂',
        skuNo: 'SKU',
        skuDesc: 'SKU描述',
        productGroup: '物料组',
        productGroupDesc: '物料组描述',
        mrpArea: 'MRP区域',
        mrpAreaDesc: 'MRP区域描述',
        type: '备货策略',
        safeStock: '安全库存',
        reOrderPoint: '再订货点',
        packagingQuantity: '舍入值',
        maxStock: '最大库存',
        remark: '备注',
        modifier: '修改者'
      }

      const allList = listData.map(data => {
        if (data.modifier === 'BI') {
          data.modifier = data.modifier + ' ' + data.forecastDate
        }
        data.type = this.getType(data.type)
        Object.keys(data).forEach(key => {
          if (mapping[key]) {
            data[mapping[key]] = data[key];
            delete data[key]
          }
        })
        delete data.positionList
        delete data.batchSize
        delete data.forecastDate
        delete data.id
        delete data.creator
        return data
      })

      writeFile(allList, `MRP备货策略 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: ['工厂', 'SKU', 'SKU描述', '物料组', '物料组描述', 'MRP区域', 'MRP区域描述', '备货策略', '安全库存', '再订货点', '舍入值', '最大库存', '备注', '修改者'] })
    },
    handleDownloadTemplateHeader () {
      window.open(excelUrls.stockImport)
    },
    handleUpload (file, fileList) {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      this.fileList = [fileList[fileList.length - 1]]; // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file.raw;
      let reader = new FileReader()
      reader.readAsArrayBuffer(this.file)
      reader.onload = async () => {
        let buffer = reader.result
        let bytes = new Uint8Array(buffer)
        let length = bytes.byteLength
        let binary = ''
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        let XLSX = require('xlsx')
        let wb = XLSX.read(binary, {
          type: 'binary'
        })
        let outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]]) // 只解析了一个Excel表格第一个sheet页面
        outdata.splice(0, 2)

        for (let i = 0; i < outdata.length; i++) {
          for (let j = i + 1; j < outdata.length; j++) {
            if (outdata[i].factory === outdata[j].factory && outdata[i].skuNo === outdata[j].skuNo && outdata[i].mrpArea === outdata[j].mrpArea) {
              this.loading.close()
              return this.$message({
                message: `${j + 4}行${outdata[j].factory}工厂下，${outdata[j].skuNo}SKU在${outdata[j].mrpArea}区域策略冲突，请只保留一条`,
                type: 'error',
                showClose: true,
                duration: 3000
              })
            }
          }
        }

        let alertArr = []
        for (let i = 0; i < outdata.length; i++) {
          if (!outdata[i].factory) {
            alertArr.push(`${i + 4}行工厂必填！`)
          }
          if (!outdata[i].skuNo) {
            alertArr.push(`${i + 4}行物料必填！`)
          }
          if (!outdata[i].mrpArea) {
            alertArr.push(`${i + 4}行MRP区域必填！`)
          }
          if (!outdata[i].type) {
            alertArr.push(`${i + 4}行备货策略必填！`)
          }
          if (outdata[i].safeStock === undefined && (outdata[i].type === '1' || outdata[i].type === 1)) {
            alertArr.push(`${i + 4}行安全库存必填！`)
          }
          if (outdata[i].type === '1' || outdata[i].type === 1) {
            outdata[i].reOrderPoint = 0
            outdata[i].packagingQuantity = 0
            outdata[i].maxStock = 0
          }
          if (outdata[i].reOrderPoint === undefined && (outdata[i].type === '2' || outdata[i].type === '3' || outdata[i].type === 2 || outdata[i].type === 3)) {
            alertArr.push(`${i + 4}行再订货点（ROP）必填！`)
          }
          if (outdata[i].packagingQuantity === undefined && (outdata[i].type === '2' || outdata[i].type === 2)) {
            alertArr.push(`${i + 4}行舍入值必填！`)
          }
          if (outdata[i].type === '2' || outdata[i].type === 2) {
            outdata[i].safeStock = 0
            outdata[i].maxStock = 0
          }
          if (outdata[i].maxStock === undefined && (outdata[i].type === '3' || outdata[i].type === 3)) {
            alertArr.push(`${i + 4}行最大库存必填！`)
          }
          if (outdata[i].type === '3' || outdata[i].type === 3) {
            outdata[i].safeStock = 0
            outdata[i].packagingQuantity = 0
          }
        }

        if (alertArr.length > 0) {
          this.loading.close()
          return this.$message({
            message: alertArr.join('<br>'),
            type: 'error',
            dangerouslyUseHTMLString: true,
            showClose: true,
            duration: 10000,
            offset: 50
          })
        }

        try {
          const res = await batchUploadStock(outdata)
          if (res.code === 200) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
          this.getStockListData()
          this.loading.close()
        } catch (error) {
          this.loading.close()
          console.log(error);
        }
      }
    },
    editRow (row) {
      this.$refs.stockGrid.setActiveRow(row)
      this.dialogTitle = '修改'
      this.dialogVisible = true
      this.disabling = true
      this.dialogForm = { ...row }
      // this.dialogForm.sku = row.skuNo
      // this.dialogForm.mrpAreaObj = row.mrpArea
    },
    saveRow (row) {
      this.$refs.stockGrid.clearActived().then(async () => {
        if (!row.type) {
          this.$message.error('备货策略不能为空！')
          return this.$refs.stockGrid.revertData(row)
        }
        if ((row.type === 1 || row.type === '1') && !row.safeStock) {
          this.$message.error('安全库存不能为空！')
          return this.$refs.stockGrid.revertData(row)
        }
        if ((row.type === 2 || row.type === 3 || row.type === '2' || row.type === '3') && !row.reOrderPoint) {
          this.$message.error('再订货点不能为空！')
          return this.$refs.stockGrid.revertData(row)
        }
        if ((row.type === '2' || row.type === 2) && !row.packagingQuantity) {
          this.$message.error('舍入值不能为空！')
          return this.$refs.stockGrid.revertData(row)
        }
        if ((row.type === '3' || row.type === 3) && !row.maxStock) {
          this.$message.error('最大库存不能为空！')
          return this.$refs.stockGrid.revertData(row)
        }
        try {
          this.tableLoading = true
          await updateStock(row)
          this.getStockListData()
          this.tableLoading = false
        } catch (error) {
          this.tableLoading = false
          console.log(error);
        }
      })
    },
    cancel (row) {
      const $grid = this.$refs.stockGrid
      $grid.clearActived().then(() => {
        // 还原行数据
        $grid.revertData(row)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.stock-strategy {
  padding: 20px;
}

</style>
