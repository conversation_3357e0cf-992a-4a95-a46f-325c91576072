export const excelUrls = {
  mrpAreaImport: '//files.zkh360.com/mrp/MRP%E5%8C%BA%E5%9F%9F%E9%85%8D%E7%BD%AE%E6%A8%A1%E6%9D%BF.xlsx',
  whiteImport: '//static.zkh360.com/file/2022-11-29/MRP%E7%99%BD%E5%90%8D%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
  stockImport: '//static.zkh360.com/file/resource/order/MRP%E5%A4%87%E8%B4%A7%E7%AD%96%E7%95%A5%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
}

export const stockingStrategy = [
  {
    label: '安全库存',
    value: 1
  },
  {
    label: 'ROP补批量',
    value: 2
  },
  {
    label: 'ROP补到最大库存',
    value: 3
  },
  {
    label: 'EVM叫料',
    value: 4
  }
]
export const supplierCertList = [
  {
    label: '空',
    value: '0'
  },
  {
    label: '供应商合格证',
    value: '1'
  },
  {
    label: '标准合格证',
    value: '2'
  },
  {
    label: '允许发货后补',
    value: '3'
  }
]
export const stockingStrategyEnum = {
  SAFESTOCK: 1,
  ROPBATCH: 2,
  ROPMAXSTOCK: 3
}

export const availableStockColumns = [
  {
    field: 'factory',
    title: '工厂'
    // width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU'
    // width: 80
  },
  {
    field: 'skuDesc',
    title: 'SKU描述'
    // width: 120
  },
  {
    field: 'mrpArea',
    title: 'MRP区域',
    width: 120,
    slots: {
      default: 'mrpArea_default'
    }
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    // width: 120,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'allStockQuantity',
    title: '总可用库存' //
  },
  {
    field: 'availableQuantity',
    title: '在库可用库存' //
  },
  {
    field: 'quantityUnit',
    title: '单位', //
    width: 80,
    slots: {
      default: 'quantityUnit_default'
    }
  },
  {
    field: 'stockingStrategy',
    title: '备货策略', //
    width: 100,
    slots: {
      default: 'stockingStrategy_default'
    }
  },
  {
    field: 'safeStock',
    title: '安全库存' //
  },
  {
    field: 'reOrderPoint',
    title: '再订货点' //
  },
  {
    field: 'packagingQuantity',
    title: '舍入值' //
  },
  {
    field: 'maxStock',
    title: '最大库存' //
  }

]

export const availableStockDownloadColumns = [
  {
    field: 'factory',
    title: '工厂'
    // width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU'
    // width: 80
  },
  {
    field: 'skuDesc',
    title: 'SKU描述'
    // width: 120
  },
  {
    field: 'mrpArea',
    title: 'MRP区域编码',
    width: 120,
    slots: {
      default: 'mrpArea_default'
    }
  },
  {
    field: 'mrpAreaDesc',
    title: 'MRP区域描述',
    width: 120,
    slots: {
      default: 'mrpArea_default'
    }
  },
  {
    field: 'purchaseGroup',
    title: '采购员编码',
    // width: 120,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'userName',
    title: '采购员姓名',
    // width: 120,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'allStockQuantity',
    title: '总可用库存' //
  },
  {
    field: 'availableQuantity',
    title: '在库可用库存' //
  },
  {
    field: 'quantityUnit',
    title: '单位', //
    width: 80,
    slots: {
      default: 'quantityUnit_default'
    }
  },
  {
    field: 'stockingStrategy',
    title: '备货策略', //
    width: 80,
    slots: {
      default: 'stockingStrategy_default'
    }
  },
  {
    field: 'safeStock',
    title: '安全库存' //
  },
  {
    field: 'reOrderPoint',
    title: '再订货点' //
  },
  {
    field: 'packagingQuantity',
    title: '舍入值' //
  },
  {
    field: 'maxStock',
    title: '最大库存' //
  }
]

export const summaryColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    showHeaderOverflow: false
  },
  {
    field: 'factory',
    title: '工厂',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU',
    width: 120,
    slots: {
      default: 'skuNo_default'
    }
  },
  {
    field: 'materialDescribe',
    title: 'SKU描述',
    width: 120
  },
  {
    field: 'productGroup',
    title: '物料组',
    width: 80,
    slots: {
      default: 'productGroup_default'
    }
  },
  {
    field: 'productPositioningName',
    title: '产品定位',
    width: 120,
    slots: {
      default: 'productPositioningName_default'
    }
  },
  {
    field: 'mstaeAllSwitch',
    title: '售完即止',
    width: 80,
    slots: {
      default: 'mstaeAllSwitch_default'
    }
  },
  {
    field: 'mrpArea',
    title: 'MRP区域',
    width: 120,
    slots: {
      default: 'mrpArea_default'
    }
  },
  {
    field: 'externalOrderNo',
    title: '销售订单号',
    width: 160
  },
  {
    field: 'externalOrderItemNo',
    title: '销售订单行号',
    width: 160
  },
  {
    field: 'soDeliveryDate',
    title: '请求到仓日期',
    width: 140
  },
  {
    field: 'deliveryDate',
    title: '请求送达日期',
    width: 140
  },
  {
    field: 'supplierArriveDate',
    title: '供应商最早送货日期',
    width: 120
  },
  {
    field: 'isSpecialOrder',
    title: '是否专料专用',
    formatter: ({ row }) => row.prTag?.isSpecialOrder === 1 ? '是' : '否',
    width: 100
  },
  {
    field: 'isUrgent',
    title: '紧急订单',
    width: 80,
    formatter: ({ row }) => row.prTag?.isUrgent === 1 ? '是' : '否'
  },
  {
    field: 'isOutOfStock',
    title: '是否供应商缺货',
    formatter: ({ row }) => row.prTag?.isOutOfStock === 1 ? '是' : '否',
    width: 110
  },
  {
    field: 'isMstaeState',
    title: '是否售完即止',
    formatter: ({ row }) => row.prTag?.isMstaeState === 1 ? '是' : '否',
    width: 100
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    width: 100,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'inventoryTypeName',
    title: '备货类型',
    minWidth: 80
  },
  {
    field: 'supplierCert',
    title: '合格证标识',
    formatter: ({ row }) => supplierCertList.find(it => it.value === row.supplierCert)?.label || '',
    minWidth: 90
  },
  {
    field: 'isSpec',
    title: '特需备注',
    width: 80,
    slots: {
      default: 'isSpec_default'
    }
  },
  {
    field: 'pendingReasonDesc',
    title: '未采购说明',
    formatter: ({ row }) => (row.pendingReasonName || '') + ' ' + (row.detailPendingReasonDesc || '') + ' ' + (row.pendingReasonDesc || ''),
    width: 140
  },
  {
    field: 'originQuantity',
    title: '缺货数量',
    width: 100
  },
  {
    field: 'replenishmentQuantity',
    title: '补货数量',
    width: 100
  },
  {
    field: 'totalWeight',
    title: '缺货合计重量(Kg)',
    width: 120,
    slots: {
      default: 'totalWeight_default'
    }
  },
  {
    field: 'quantity',
    title: '申请数量',
    width: 80,
    slots: {
      default: 'quantity_default'
    }
  },
  {
    field: 'quantityUnit',
    title: '单位',
    width: 50,
    slots: {
      default: 'quantityUnit_default'
    }
  },
  {
    field: 'specialDemandQuantity',
    title: '特采仓需求',
    width: 90,
    slots: {
      default: 'specialDemandQuantity_default'
    }
  },
  {
    field: 'skuFactoryStrategy',
    title: '库位策略',
    width: 120
  },
  {
    field: 'selfInventory1',
    title: '本仓库存1',
    width: 120,
    slots: {
      default: 'selfInventory1_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.selfInventory1Quantity && row.inventoryVo.selfInventory1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'selfInventory2',
    title: '本仓库存2',
    width: 120,
    slots: {
      default: 'selfInventory2_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.selfInventory2Quantity && row.inventoryVo.selfInventory2Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'promotionInventoryQuantity',
    title: '本仓促销仓库存',
    width: 110,
    slots: {
      default: 'promotionInventoryQuantity_default'
    }
  },
  {
    field: 'otherInventory1',
    title: '他仓在库1',
    width: 120,
    titleHelp: { message: '在库库存 - ATP占用 - 安全库存' },
    slots: {
      default: 'otherInventory1_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.otherInventory1Quantity && row.inventoryVo.otherInventory1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'otherTransportationInventory1',
    title: '他仓可用',
    width: 120,
    titleHelp: { message: '在库库存 + 在途库存 - ATP占用 - 安全库存' },
    slots: {
      default: 'otherTransportationInventory1_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.otherTransportationInventory1Quantity && row.inventoryVo.otherTransportationInventory1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'hignStorageInStock1MrpArea',
    title: '高库龄库存1',
    minWidth: 120,
    titleHelp: { message: '高库龄在库 - ATP占用 - 安全库存' },
    slots: {
      default: 'hignStorageInStock1MrpArea_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.hignStorageInStock1Quantity && row.inventoryVo.hignStorageInStock1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'hignStorageInStock2MrpArea',
    title: '高库龄库存2',
    minWidth: 120,
    slots: {
      default: 'hignStorageInStock2MrpArea_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.hignStorageInStock2Quantity && row.inventoryVo.hignStorageInStock2Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'bomItemInventory',
    title: '组件总可用量',
    minWidth: 120,
    titleHelp: { message: '在库 + 在途 - ATP占用' },
    slots: {
      default: 'bomItemInventory_default'
    }
  },
  {
    field: 'purchasePriceVOList0',
    title: '第一优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList0'
    },
    showOverflow: 'ellipsis',
    sortable: true,
    sortBy: 'supplier1Detail.providerNo',
    sortType: 'string'
  },
  {
    field: 'purchasePrice0Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice0Price_default'
    }
  },
  {
    field: 'supplier1Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'cloudWareHouse',
    title: '可信云仓',
    width: 100,
    slots: {
      default: 'cloudWareHouse_default'
    }
  }, {
    field: 'abutmentStock',
    title: '对接库存数量',
    width: 100,
    slots: {
      default: 'abutmentStock_default'
    }
  },
  {
    field: 'purchasePriceVOList1',
    title: '第二优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList1'
    },
    showOverflow: 'ellipsis'
  },
  {
    field: 'purchasePrice1Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice1Price_default'
    }
  },
  {
    field: 'supplier2Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'purchasePriceVOList2',
    title: '第三优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList2'
    },
    showOverflow: 'ellipsis'
  },
  {
    field: 'purchasePrice2Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice2Price_default'
    }
  },
  {
    field: 'supplier3Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'recommendSupplier',
    title: '其他供应商',
    width: 150,
    slots: {
      default: 'recommendSupplier_default'
    }
  },
  {
    field: 'inStockQuantity',
    title: '在库库存',
    width: 80
  },
  {
    field: 'fsoQuantity',
    title: '需求数量（预报）',
    width: 120
  },
  {
    field: 'soQuantity',
    title: '需求数量（订单）',
    width: 120
  },
  {
    field: 'safeStockQuantity',
    title: '安全库存',
    width: 80
  },
  {
    field: 'onWayQuantity',
    title: '在途数量',
    width: 80
  },
  {
    field: 'wipQuantity',
    title: '计划订单',
    width: 80
  },
  {
    field: 'otherQuantity',
    title: '其他需求',
    width: 80
  },
  {
    title: '操作',
    slots: {
      default: 'operation'
    },
    width: 80,
    fixed: 'right'
  },
  {
    field: 'poItemHistoryPrice',
    title: '采购历史价格(日期/数量/价格)',
    width: 140,
    slots: {
      default: 'poItemHistoryPrice_default'
    }
  },
  {
    field: 'grossMargin',
    title: '毛利率(%)',
    width: 80
  },
  {
    field: 'grossAmount',
    title: '毛利额',
    width: 80
  },
  {
    field: 'stableMark',
    title: '低负毛利是否已导出',
    formatter: ({ cellValue }) => {
      return cellValue === 'Y' ? '是' : '否'
    },
    width: 100
  },
  {
    field: 'ifGrossMargin',
    title: '是否低负毛利',
    formatter: ({ cellValue }) => {
      return cellValue ? '是' : '否'
    },
    width: 100
  }
]

export const summarMapping = {
  factory: '工厂',
  skuNo: 'SKU',
  skuTagMsg: '异常',
  materialDescribe: 'SKU描述',
  productGroup: '物料组编码',
  productGroupName: '物料组名字',
  mrpArea: 'MRP区域编码',
  mrpAreaDesc: 'MRP区域描述',
  purchaseGroup: '采购员编码',
  userName: '采购员名字',
  inventoryTypeName: '备货类型',
  supplierCert: '合格证标识',
  externalOrderNo: '销售订单号',
  externalOrderItemNo: '销售订单行号',
  isSpecialOrder: '是否专料专用',
  isOutOfStock: '是否供应商缺货',
  isMstaeState: '是否售完即止',
  isUrgent: '是否急单',
  soDeliveryDate: '销售请求送达日期',
  // deliveryDate: '请求送达日期',
  supplierArriveDate: '供应商最早送货日期',
  mpq: 'MPQ',
  pendingOrderReason: '未采购说明',
  isSpec: '特需备注',
  originQuantity: '缺货数量',
  replenishmentQuantity: '补货数量',
  totalWeight: '缺货合计重量(Kg)',
  quantity: '申请数量',
  quantityUnit: '单位',
  specialDemandQuantity: '特采仓需求',
  skuFactoryStrategy: '库位策略',
  productPositioningName: '产品定位',
  selfInventory1: '本仓库存1',
  selfInventory2: '本仓库存2',
  promotionInventoryQuantity: '本仓促销仓库存',
  otherInventory1: '他仓在库1',
  otherTransportationInventory1: '他仓可用',
  hignStorageInStock1MrpArea: '高库龄库存1',
  hignStorageInStock2MrpArea: '高库龄库存2',
  bomItemInventory: '组件可用量',
  purchasePriceVOList0: '第一优先供应商编码',
  purchasePrice0Name: '第一优先供应商名字',
  purchasePrice0tacticsName: '策略类型(第一优先供应商)',
  purchasePrice0ifAgency: '是否代理品牌(第一优先供应商)',
  purchasePrice0ifTradeCertificate: '是否原厂品牌(第一优先供应商)',
  purchasePrice0esokz: '默认运营路线(第一优先供应商)',
  purchasePrice0currency: '币种(第一优先供应商)',
  purchasePrice0taxRate: '进项税(第一优先供应商)',
  purchasePrice0meins: '订单单位(第一优先供应商)',
  purchasePrice0purchaseMoq: '最小起订单(第一优先供应商)',
  purchasePrice0ifBid: '招投标(第一优先供应商)',
  purchasePrice0ifIncludeFreight: '含运费(第一优先供应商)',
  purchasePrice0freightRate: '单件运费(第一优先供应商)',
  purchasePrice0paymentTermsCodeName: '付款条款(第一优先供应商)',
  purchasePrice0ifUpInventory: '对接库存(第一优先供应商)',
  purchasePrice0ifSignFrameworkAgreement: '年框(第一优先供应商)',
  purchasePrice0supplierClassifyName: '供应商分类(第一优先供应商)',
  purchasePrice0oemDirectSupplier: 'OEM直发(第一优先供应商)',
  purchasePrice0idnlf: '供应商物料号(第一优先供应商)',
  purchasePrice0Price: '含税价格(第一优先供应商)',
  purchasePrice0LeadTime: '期货LT(第一优先供应商)',
  cloudWareHouse: '可信云仓',
  abutmentStock: '对接库存数量',
  purchasePrice0NearestOrderDate: '最近一次订货日(第一优先供应商)',
  purchasePriceVOList1: '第二优先供应商编码',
  purchasePrice1Name: '第二优先供应商名字',
  purchasePrice1tacticsName: '策略类型(第二优先供应商)',
  purchasePrice1ifAgency: '是否代理品牌(第二优先供应商)',
  purchasePrice1ifTradeCertificate: '是否原厂品牌(第二优先供应商)',
  purchasePrice1ifUpInventory: '对接库存(第二优先供应商)',
  purchasePrice1ifIncludeFreight: '含运费(第二优先供应商)',
  purchasePrice1freightRate: '单件运费(第二优先供应商)',
  purchasePrice1ifSignFrameworkAgreement: '年框(第二优先供应商)',
  purchasePrice1supplierClassifyName: '供应商分类(第二优先供应商)',
  purchasePrice1oemDirectSupplier: 'OEM直发(第二优先供应商)',
  purchasePrice1idnlf: '供应商物料号(第二优先供应商)',
  purchasePrice1Price: '含税价格(第二优先供应商)',
  purchasePrice1LeadTime: '期货LT(第二优先供应商)',
  purchasePriceVOList2: '第三优先供应商编码',
  purchasePrice2Name: '第三优先供应商名字',
  purchasePrice2tacticsName: '策略类型(第三优先供应商)',
  purchasePrice2ifAgency: '是否代理品牌(第三优先供应商)',
  purchasePrice2ifTradeCertificate: '是否原厂品牌(第三优先供应商)',
  purchasePrice2ifUpInventory: '对接库存(第三优先供应商)',
  purchasePrice2ifIncludeFreight: '含运费(第三优先供应商)',
  purchasePrice2freightRate: '单件运费(第三优先供应商)',
  purchasePrice2ifSignFrameworkAgreement: '年框(第三优先供应商)',
  purchasePrice2supplierClassifyName: '供应商分类(第三优先供应商)',
  purchasePrice2oemDirectSupplier: 'OEM直发(第三优先供应商)',
  purchasePrice2idnlf: '供应商物料号(第三优先供应商)',
  purchasePrice2Price: '含税价格(第三优先供应商)',
  purchasePrice2LeadTime: '期货LT(第三优先供应商)',
  inStockQuantity: '在库库存',
  fsoQuantity: '需求数量（预报）',
  soQuantity: '需求数量（订单）',
  safeStockQuantity: '安全库存',
  onWayQuantity: '在途数量',
  wipQuantity: '计划订单',
  otherQuantity: '其他需求',
  poItemHistoryPrice: '采购历史价格(日期/数量/价格)',
  recommendSupplier1Detail: '潜在供应商1',
  recommendSupplier2Detail: '潜在供应商2',
  recommendSupplier3Detail: '潜在供应商3',
  grossMargin: '毛利率(%)',
  ifGrossMargin: '是否低负毛利',
  stableMark: '低负毛利是否已导出',
  grossAmount: '毛利额'
}

export const directColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    showHeaderOverflow: false
  },
  {
    field: 'msg',
    title: '消息',
    width: 80,
    fixed: 'left'
  },
  {
    field: 'poNo',
    title: '采购订单',
    width: 120,
    slots: {
      default: 'poNo_default'
    },
    fixed: 'left'
  },
  {
    field: 'factory',
    title: '工厂',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU',
    slots: {
      default: 'skuNo_default'
    },
    width: 120
  },
  {
    field: 'materialDescribe',
    title: 'SKU描述',
    width: 120
  },
  {
    field: 'skuTypeName',
    title: 'SKU类型',
    width: 120
  },
  {
    field: 'productPositioningName',
    title: '产品定位',
    width: 120,
    slots: {
      default: 'productPositioningName_default'
    }
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    width: 100,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'inventoryTypeName',
    title: '备货类型',
    width: 80
  },
  {
    field: 'supplierCert',
    title: '合格证标识',
    formatter: ({ row }) => supplierCertList.find(it => it.value === row.supplierCert)?.label || '',
    minWidth: 90
  },
  {
    field: 'productGroup',
    title: '物料组',
    width: 80,
    slots: {
      default: 'productGroup_default'
    }
  },
  {
    field: 'quantity',
    title: '申请数量',
    width: 80
  },
  {
    field: 'quantityUnit',
    title: '基本单位',
    width: 80,
    slots: {
      default: 'quantityUnit_default'
    }
  },
  {
    field: 'customerSpecified',
    title: '指定渠道',
    width: 80,
    slots: {
      default: 'customerSpecified_default'
    }
  },
  {
    field: 'directWareHouse',
    title: '直发对应仓',
    width: 100,
    slots: {
      default: 'directWareHouse_default'
    }
  },
  {
    field: 'selfInventory1',
    title: '本仓库存1',
    width: 120,
    slots: {
      default: 'selfInventory1_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.selfInventory1Quantity && row.inventoryVo.selfInventory1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'selfInventory2',
    title: '本仓库存2',
    width: 120,
    slots: {
      default: 'selfInventory2_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.selfInventory2Quantity && row.inventoryVo.selfInventory2Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'otherInventory1',
    title: '他仓在库1',
    width: 120,
    titleHelp: { message: '在库库存 - ATP占用 - 安全库存' },
    slots: {
      default: 'otherInventory1_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.otherInventory1Quantity && row.inventoryVo.otherInventory1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'otherTransportationInventory1',
    title: '他仓可用',
    width: 120,
    titleHelp: { message: '在库库存 + 在途库存 - ATP占用 - 安全库存' },
    slots: {
      default: 'otherTransportationInventory1_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.otherTransportationInventory1Quantity && row.inventoryVo.otherTransportationInventory1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'hignStorageInStock1MrpArea',
    title: '高库龄库存1',
    minWidth: 120,
    titleHelp: { message: '高库龄在库 - ATP占用 - 安全库存' },
    slots: {
      default: 'hignStorageInStock1MrpArea_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.hignStorageInStock1Quantity && row.inventoryVo.hignStorageInStock1Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'hignStorageInStock2MrpArea',
    title: '高库龄库存2',
    minWidth: 120,
    slots: {
      default: 'hignStorageInStock2MrpArea_default'
    },
    filters: [
      {
        label: '有可用库存',
        value: 1
      },
      {
        label: '无可用库存',
        value: 0
      }
    ],
    filterMultiple: false,
    filterMethod: function (data) {
      const { value, row } = data
      const hasStorage = row.inventoryVo && row.inventoryVo.hignStorageInStock2Quantity && row.inventoryVo.hignStorageInStock2Quantity > 0
      if (value === 1) {
        return hasStorage
      }
      if (value === 0) {
        return !hasStorage
      }
    }
  },
  {
    field: 'skuFactoryStrategy',
    title: '库位策略',
    width: 120
  },
  {
    field: 'supplier',
    title: '分配供应商',
    width: 200,
    slots: {
      default: 'supplier_default'
    }
  },
  {
    field: 'replaceFirstSupplierReason',
    title: '推优替换原因',
    width: 260,
    slots: {
      default: 'replaceFirstSupplierReason_default'
    }
  },
  {
    field: 'supplierDetail.paymentTermsCodeName',
    title: '付款条件',
    width: 100
  },
  {
    field: 'isFree',
    title: '免费',
    width: 50,
    slots: {
      default: 'isFree_default'
    }
  },
  {
    field: 'unitPrice',
    title: '采购价格',
    width: 160,
    slots: {
      default: 'unitPrice_default'
    }
  },
  {
    field: 'meinsName',
    title: '采购单位',
    width: 80,
    slots: {
      default: 'meinsName_default'
    }
  },
  {
    field: 'currency',
    title: '币种',
    width: 80,
    slots: {
      default: 'currency_default'
    }
  },
  {
    field: 'taxRate',
    title: '进项税',
    width: 100,
    slots: {
      default: 'taxRate_default'
    }
  },
  {
    field: 'supplierDetail.purchaseMoq',
    title: '最小起订量',
    width: 80
  },
  {
    field: 'saleMoq',
    title: '销售MOQ',
    width: 80
  },
  {
    field: 'supplierDetail.idnlf',
    title: '供应商物料号',
    width: 120
  },
  {
    field: 'externalOrderNo',
    title: '销售订单',
    width: 120,
    slots: {
      default: 'externalOrderNo_default'
    }
  },
  {
    field: 'externalOrderItemNo',
    title: '销售订单行',
    width: 90
  },
  {
    field: 'pendingOrderReason',
    title: '未采购说明',
    width: 120,
    slots: {
      default: 'pendingOrderReason_default'
    }
  },
  {
    field: 'soCreateDate',
    title: '销售订单创建日期',
    width: 120,
    slots: {
      default: 'soCreateDate_default'
    }
  },
  {
    field: 'soDeliveryDate',
    title: '请求送达日期',
    width: 120,
    slots: {
      default: 'deliveryDate_default'
    }
  },
  {
    field: 'supplierArriveDate',
    title: '供应商最早送货日期',
    width: 120
  },
  {
    field: 'isSpecialOrder',
    title: '是否专料专用',
    formatter: ({ row }) => row.prTag?.isSpecialOrder === 1 ? '是' : '否',
    width: 100
  },
  {
    field: 'isUrgent',
    title: '紧急订单',
    width: 80,
    formatter: ({ row }) => row.prTag?.isUrgent === 1 ? '是' : '否'
  },
  {
    field: 'isOutOfStock',
    title: '是否供应商缺货',
    formatter: ({ row }) => row.prTag?.isOutOfStock === 1 ? '是' : '否',
    width: 110
  },
  {
    field: 'isMstaeState',
    title: '是否售完即止',
    formatter: ({ row }) => row.prTag?.isMstaeState === 1 ? '是' : '否',
    width: 100
  },
  {
    field: 'productSaleName',
    title: '销售',
    width: 80
  },
  {
    field: 'productCustomerName',
    title: '客服',
    width: 80
  },
  {
    field: 'customerName',
    title: '客户',
    width: 80,
    slots: {
      default: 'customerNo_default'
    }
  },
  {
    field: 'receiverAddress',
    title: '收货地址',
    width: 120,
    slots: {
      default: 'receiverAddress_default'
    }
  },
  {
    field: 'remark',
    title: '客户需求备注',
    width: 120
  },
  {
    field: 'customInstructions',
    title: '定制说明',
    width: 150,
    showOverflow: 'ellipsis',
    slots: {
      default: 'customInstructions_default'
    }
  },
  {
    field: 'purchasePriceVOList0',
    title: '第一优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList0'
    },
    showOverflow: 'ellipsis',
    sortable: true,
    sortBy: 'supplier1Detail.providerNo',
    sortType: 'string'
  },
  {
    field: 'purchasePrice0Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice0Price_default'
    }
  },
  {
    field: 'supplier1Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'purchasePriceVOList1',
    title: '第二优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList1'
    },
    showOverflow: 'ellipsis'
  },
  {
    field: 'purchasePrice1Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice1Price_default'
    }
  },
  {
    field: 'supplier2Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'purchasePriceVOList2',
    title: '第三优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList2'
    },
    showOverflow: 'ellipsis'
  },
  {
    field: 'purchasePrice2Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice2Price_default'
    }
  },
  {
    field: 'supplier3Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'recommendSupplier',
    title: '其他供应商',
    width: 150,
    slots: {
      default: 'recommendSupplier_default'
    }
  },
  {
    field: 'prNo',
    title: '采购申请',
    width: 120
  },
  {
    field: 'prItemNo',
    title: '采购申请行',
    width: 80
  },
  {
    field: 'hasDeliveryManager',
    title: '是否有交付主管',
    width: 110,
    slots: {
      default: 'hasDeliveryManager_default'
    }
  },
  {
    field: 'signingBack',
    title: '签单返回',
    width: 90,
    slots: {
      default: 'signingBack_default'
    }
  },
  {
    field: 'poItemHistoryPrice',
    title: '采购历史价格(日期/数量/价格)',
    width: 140,
    slots: {
      default: 'poItemHistoryPrice_default'
    }
  },
  {
    field: 'grossMargin',
    title: '毛利率(%)',
    width: 80
  },
  {
    field: 'grossAmount',
    title: '毛利额',
    width: 80
  },
  {
    field: 'stableMark',
    title: '低负毛利是否已导出',
    formatter: ({ cellValue }) => {
      return cellValue === 'Y' ? '是' : '否'
    },
    width: 100
  },
  {
    field: 'ifGrossMargin',
    title: '是否低负毛利',
    formatter: ({ cellValue }) => {
      return cellValue ? '是' : '否'
    },
    width: 100
  },
  {
    field: 'mrpPrintRemark',
    title: '操作',
    width: 50,
    fixed: 'right',
    slots: {
      default: 'mrpPrintRemark_default'
    }
  }
]

export const directOptions = [
  { value: 'Z001', label: 'Z001 国内采购订单' },
  { value: 'Z002', label: 'Z002 进口采购订单' },
  { value: 'Z006', label: 'Z006 委外采购订单' },
  { value: 'Z013', label: 'Z013 客户代购采购订单' }
]

export const directMapping = {
  msg: '消息',
  poNo: '采购订单',
  factory: '工厂',
  skuNo: 'SKU',
  materialDescribe: 'SKU描述',
  skuTypeName: 'SKU类型',
  purchaseGroup: '采购员编码',
  userName: '采购员名字',
  inventoryTypeName: '备货类型',
  supplierCert: '合格证标识',
  productGroup: '物料组编码',
  productGroupName: '物料组名字',
  quantity: '申请数量',
  quantityUnit: '基本单位',
  customerSpecified: '指定渠道',
  directWareHouse: '直发对应仓编码',
  directWareHouseDesc: '直发对应仓名字',
  selfInventory1: '本仓库存1',
  selfInventory2: '本仓库存2',
  otherInventory1: '他仓在库1',
  otherTransportationInventory1: '他仓可用',
  hignStorageInStock1MrpArea: '高库龄库存1',
  hignStorageInStock2MrpArea: '高库龄库存2',
  skuFactoryStrategy: '库位策略',
  productPositioningName: '产品定位',
  supplier: '分配供应商编码',
  providerName: '分配供应商名字',
  paymentTermsCodeName: '付款条件',
  isFree: '免费',
  unitPrice: '采购价格',
  meinsName: '采购单位',
  currency: '币种',
  taxRate: '进项税',
  purchaseMoq: '最小起订量',
  saleMoq: '销售MOQ',
  idnlf: '供应商物料号',
  externalOrderNo: '销售订单',
  externalOrderItemNo: '销售订单行',
  pendingOrderReason: '未采购说明',
  soCreateDate: '销售订单创建日期',
  soDeliveryDate: '请求送达日期',
  isSpecialOrder: '是否专料专用',
  isOutOfStock: '是否供应商缺货',
  isMstaeState: '是否售完即止',
  supplierArriveDate: '供应商最早送货日期',
  isOrderException: '下单异常',
  isUrgent: '紧急订单',
  productSaleName: '销售',
  productCustomerName: '客服',
  customerNo: '客户编码',
  customerName: '客户名字',
  receiverAddress: '收货地址',
  remark: '客户需求备注',
  customInstructions: '定制说明',
  purchasePriceVOList0: '第一优先供应商编码',
  purchasePrice0Name: '第一优先供应商名字',
  purchasePrice0tacticsName: '策略类型(第一优先供应商)',
  purchasePrice0ifAgency: '是否代理品牌(第一优先供应商)',
  purchasePrice0ifTradeCertificate: '是否原厂品牌(第一优先供应商)',
  purchasePrice0esokz: '默认运营路线(第一优先供应商)',
  purchasePrice0currency: '币种(第一优先供应商)',
  purchasePrice0taxRate: '进项税(第一优先供应商)',
  purchasePrice0meins: '订单单位(第一优先供应商)',
  purchasePrice0purchaseMoq: '最小起订单(第一优先供应商)',
  purchasePrice0ifBid: '招投标(第一优先供应商)',
  purchasePrice0ifIncludeFreight: '含运费(第一优先供应商)',
  purchasePrice0freightRate: '单件运费(第一优先供应商)',
  purchasePrice0paymentTermsCodeName: '付款条款(第一优先供应商)',
  purchasePrice0ifUpInventory: '对接库存(第一优先供应商)',
  purchasePrice0ifSignFrameworkAgreement: '年框(第一优先供应商)',
  purchasePrice0supplierClassifyName: '供应商分类(第一优先供应商)',
  purchasePrice0oemDirectSupplier: 'OEM直发(第一优先供应商)',
  purchasePrice0idnlf: '供应商物料号(第一优先供应商)',
  purchasePrice0Price: '含税价格(第一优先供应商)',
  purchasePrice0LeadTime: '期货LT(第一优先供应商)',
  purchasePriceVOList1: '第二优先供应商编码',
  purchasePrice1Name: '第二优先供应商名字',
  purchasePrice1tacticsName: '策略类型(第二优先供应商)',
  purchasePrice1ifAgency: '是否代理品牌(第二优先供应商)',
  purchasePrice1ifTradeCertificate: '是否原厂品牌(第二优先供应商)',
  purchasePrice1ifUpInventory: '对接库存(第二优先供应商)',
  purchasePrice1ifIncludeFreight: '含运费(第二优先供应商)',
  purchasePrice1freightRate: '单件运费(第二优先供应商)',
  purchasePrice1ifSignFrameworkAgreement: '年框(第二优先供应商)',
  purchasePrice1supplierClassifyName: '供应商分类(第二优先供应商)',
  purchasePrice1oemDirectSupplier: 'OEM直发(第二优先供应商)',
  purchasePrice1idnlf: '供应商物料号(第二优先供应商)',
  purchasePrice1Price: '含税价格(第二优先供应商)',
  purchasePrice1LeadTime: '期货LT(第二优先供应商)',
  purchasePriceVOList2: '第三优先供应商编码',
  purchasePrice2Name: '第三优先供应商名字',
  purchasePrice2tacticsName: '策略类型(第三优先供应商)',
  purchasePrice2ifAgency: '是否代理品牌(第三优先供应商)',
  purchasePrice2ifTradeCertificate: '是否原厂品牌(第三优先供应商)',
  purchasePrice2ifUpInventory: '对接库存(第三优先供应商)',
  purchasePrice2ifIncludeFreight: '含运费(第三优先供应商)',
  purchasePrice2freightRate: '单件运费(第三优先供应商)',
  purchasePrice2ifSignFrameworkAgreement: '年框(第三优先供应商)',
  purchasePrice2supplierClassifyName: '供应商分类(第三优先供应商)',
  purchasePrice2oemDirectSupplier: 'OEM直发(第三优先供应商)',
  purchasePrice2idnlf: '供应商物料号(第三优先供应商)',
  purchasePrice2Price: '含税价格(第三优先供应商)',
  purchasePrice2LeadTime: '期货LT(第三优先供应商)',
  prNo: '采购申请',
  prItemNo: '采购申请行',
  hasDeliveryManager: '是否有交付主管',
  signingBack: '签单返回',
  mrpPrintRemark: '打印备注',
  poItemHistoryPrice: '采购历史价格(日期/数量/价格)',
  recommendSupplier1Detail: '潜在供应商1',
  recommendSupplier2Detail: '潜在供应商2',
  recommendSupplier3Detail: '潜在供应商3',
  grossMargin: '毛利率(%)',
  ifGrossMargin: '是否低负毛利',
  stableMark: '低负毛利是否已导出',
  grossAmount: '毛利额'
}

export const detailColumns = [
  {
    field: 'factory',
    title: '工厂',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU',
    slots: {
      default: 'skuNo_default'
    },
    width: 120
  },
  {
    field: 'skuDesc',
    title: 'SKU描述',
    width: 120
  },
  {
    field: 'productGroup',
    title: '物料组',
    width: 120,
    slots: {
      default: 'productGroup_default'
    }
  },
  {
    field: 'productPositioningName',
    title: '产品定位',
    width: 120,
    slots: {
      default: 'productPositioningName_default'
    }
  },
  {
    field: 'mrpArea',
    title: 'MRP区域',
    width: 120,
    slots: {
      default: 'mrpArea_default'
    }
  },
  {
    field: 'position',
    title: '仓库地点',
    width: 120,
    slots: {
      default: 'position_default'
    }
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    width: 120,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'inventoryTypeName',
    title: '备货类型',
    minWidth: 80
  },
  {
    field: 'mstaeAllSwitch',
    title: '售完即止',
    width: 80,
    slots: {
      default: 'mstaeAllSwitch_default'
    }
  },
  {
    field: 'mrpType',
    title: '单据类型',
    width: 80,
    slots: {
      default: 'mrpType_default'
    }
  },
  {
    field: 'recordNo',
    title: '单据号',
    width: 80,
    slots: {
      default: 'mrpTypeOrder_default'
    }
  },
  {
    field: 'recordItemNo',
    title: '单据行号',
    width: 80,
    slots: {
      default: 'recordItemNo'
    }
  },
  {
    field: 'recordCreateDate',
    title: '单据日期',
    width: 80,
    slots: {
      default: 'recordCreateDate_default'
    }
  },
  {
    field: 'deliveryDate',
    title: '到货日期',
    width: 80,
    slots: {
      default: 'deliveryDate_default'
    }
  },
  {
    field: 'originQuantity',
    title: '收货/需求',
    width: 80,
    slots: {
      default: 'originQuantity_default'
    }
  },
  {
    field: 'demandQuantity',
    title: '可用数量',
    width: 80,
    slots: {
      default: 'availableQuantity_default'
    }
  },
  {
    field: 'isUrgent',
    title: '特殊订单',
    width: 80,
    slots: {
      default: 'isUrgent_default'
    }
  },
  {
    field: 'isSpec',
    title: '特需备注',
    width: 80,
    slots: {
      default: 'isSpec_default'
    }
  },
  {
    field: 'pendingReasonDesc',
    title: '未采购说明',
    formatter: ({ row }) => (row.pendingReasonName || '') + ' ' + (row.detailPendingReasonDesc || '') + ' ' + (row.pendingReasonDesc || ''),
    width: 140
  },
  {
    field: 'ltBuffer',
    title: '采购buffer',
    width: 80
  },
  {
    field: 'saleForecastQuantity',
    title: '销售预报', //
    width: 80,
    slots: {
      default: 'saleForecastQuantity_default'
    }
  },
  {
    field: 'saleQuantity',
    title: '销售订单', //
    width: 80,
    slots: {
      default: 'saleQuantity_default'
    }
  },
  {
    field: 'procurementQuantity',
    title: '采购订单', //
    width: 80,
    slots: {
      default: 'procurementQuantity_default'
    }
  },
  {
    field: 'procurementApplyQuantity', //
    title: '采购申请',
    width: 80,
    slots: {
      default: 'procurementApplyQuantity_default'
    }
  },
  {
    field: 'productQuantity',
    title: '生产订单', //
    width: 80,
    slots: {
      default: 'productQuantity_default'
    }
  },
  {
    field: 'planQuantity',
    title: '计划订单', //
    width: 80,
    slots: {
      default: 'planQuantity_default'
    }
  },
  {
    field: 'deliveryQuantity',
    title: '交货单', //
    width: 80,
    slots: {
      default: 'deliveryQuantity_default'
    }
  },
  {
    field: 'inventoryQuantity',
    title: '库存', //
    width: 80,
    slots: {
      default: 'inventoryQuantity_default'
    }
  },
  {
    field: 'promotionInventoryQuantity',
    title: '促销仓库存', //
    width: 90
  },
  {
    field: 'customerNo', //
    title: '客户',
    width: 80,
    slots: {
      default: 'customerName_default'
    }
  },
  {
    field: 'productCustomerName', //
    title: '客服',
    width: 100
  },
  {
    field: 'productCustomerManagerName', //
    title: '客服经理',
    width: 80
  },
  {
    field: 'productSaleName', //
    title: '销售',
    width: 80
  },
  {
    field: 'productSaleManagerName', //
    title: '销售总监',
    width: 80
  },
  {
    field: 'evmOperatorName',
    title: 'EVM运营专员',
    width: 90
  },
  {
    field: 'purchasePriceVOList0',
    title: '第一优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList0'
    },
    showOverflow: 'ellipsis'
  },
  {
    field: 'purchasePrice0Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice0Price_default'
    }
  },
  {
    field: 'supplier1Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'purchasePriceVOList1',
    title: '第二优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList1'
    },
    showOverflow: 'ellipsis'
  },
  {
    field: 'purchasePrice1Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice1Price_default'
    }
  },
  {
    field: 'supplier2Detail.leadTime',
    title: '期货LT',
    width: 80
  },
  {
    field: 'purchasePriceVOList2',
    title: '第三优先供应商',
    width: 150,
    slots: {
      default: 'purchasePriceVOList2'
    },
    showOverflow: 'ellipsis'
  },
  {
    field: 'purchasePrice2Price',
    title: '含税价格',
    width: 80,
    slots: {
      default: 'purchasePrice2Price_default'
    }
  },
  {
    field: 'supplier3Detail.leadTime',
    title: '期货LT',
    width: 80
  }
]

export const detailMapping = {
  factory: '工厂',
  skuNo: 'SKU',
  skuDesc: 'SKU描述',
  productGroup: '物料组编码',
  productGroupName: '物料组名字',
  productPositioningName: '产品定位',
  mrpArea: 'MRP区域编码',
  mrpAreaDesc: 'MRP区域描述',
  position: '仓库地点编码',
  warehouseLocationName: '仓库地点名字',
  purchaseGroup: '采购员编码',
  userName: '采购员名字',
  inventoryTypeName: '备货类型',
  mstaeAllSwitch: '售完即止',
  mrpType: '单据类型',
  recordNo: '单据号',
  recordItemNo: '单据行号',
  recordCreateDate: '单据日期',
  deliveryDate: '到货日期',
  originQuantity: '收货/需求',
  demandQuantity: '可用数量',
  isUrgent: '特殊订单',
  isSpec: '特需备注',
  pendingOrderReason: '未采购说明',
  ltBuffer: '采购buffer',
  saleForecastQuantity: '销售预报',
  saleQuantity: '销售订单',
  procurementQuantity: '采购订单',
  procurementApplyQuantity: '采购申请',
  productQuantity: '生产订单',
  planQuantity: '计划订单',
  deliveryQuantity: '交货单',
  inventoryQuantity: '库存',
  promotionInventoryQuantity: '促销仓库存',
  customerNo: '客户编码',
  customerName: '客户名字',
  productCustomerName: '客服',
  productCustomerManagerName: '客服经理',
  productSaleName: '销售',
  productSaleManagerName: '销售总监',
  evmOperatorName: 'EVM运营专员',
  purchasePriceVOList0: '第一优先供应商编码',
  purchasePrice0Name: '第一优先供应商名字',
  purchasePrice0tacticsName: '策略类型(第一优先供应商)',
  purchasePrice0ifAgency: '是否代理品牌(第一优先供应商)',
  purchasePrice0ifTradeCertificate: '是否原厂品牌(第一优先供应商)',
  purchasePrice0currency: '币种(第一优先供应商)',
  purchasePrice0taxRate: '进项税(第一优先供应商)',
  purchasePrice0meins: '订单单位(第一优先供应商)',
  purchasePrice0purchaseMoq: '最小起订单(第一优先供应商)',
  purchasePrice0ifBid: '招投标(第一优先供应商)',
  purchasePrice0ifIncludeFreight: '含运费(第一优先供应商)',
  purchasePrice0freightRate: '单件运费(第一优先供应商)',
  purchasePrice0paymentTermsCodeName: '付款条款(第一优先供应商)',
  purchasePrice0ifUpInventory: '对接库存(第一优先供应商)',
  purchasePrice0ifSignFrameworkAgreement: '年框(第一优先供应商)',
  purchasePrice0supplierClassifyName: '供应商分类(第一优先供应商)',
  purchasePrice0oemDirectSupplier: 'OEM直发(第一优先供应商)',
  purchasePrice0idnlf: '供应商物料号(第一优先供应商)',
  purchasePrice0Price: '含税价格(第一优先供应商)',
  purchasePrice0LeadTime: '期货LT(第一优先供应商)',
  purchasePrice0Esokz: '供应商信息类别(第一优先供应商)',
  purchasePriceVOList1: '第二优先供应商编码',
  purchasePrice1Name: '第二优先供应商名字',
  purchasePrice1tacticsName: '策略类型(第二优先供应商)',
  purchasePrice1ifAgency: '是否代理品牌(第二优先供应商)',
  purchasePrice1ifTradeCertificate: '是否原厂品牌(第二优先供应商)',
  purchasePrice1ifUpInventory: '对接库存(第二优先供应商)',
  purchasePrice1ifIncludeFreight: '含运费(第二优先供应商)',
  purchasePrice1freightRate: '单件运费(第二优先供应商)',
  purchasePrice1ifSignFrameworkAgreement: '年框(第二优先供应商)',
  purchasePrice1supplierClassifyName: '供应商分类(第二优先供应商)',
  purchasePrice1oemDirectSupplier: 'OEM直发(第二优先供应商)',
  purchasePrice1idnlf: '供应商物料号(第二优先供应商)',
  purchasePrice1Price: '含税价格(第二优先供应商)',
  purchasePrice1LeadTime: '期货LT(第二优先供应商)',
  purchasePrice1Esokz: '供应商信息类别(第二优先供应商)',
  purchasePriceVOList2: '第三优先供应商编码',
  purchasePrice2Name: '第三优先供应商名字',
  purchasePrice2tacticsName: '策略类型(第三优先供应商)',
  purchasePrice2ifAgency: '是否代理品牌(第三优先供应商)',
  purchasePrice2ifTradeCertificate: '是否原厂品牌(第三优先供应商)',
  purchasePrice2ifUpInventory: '对接库存(第三优先供应商)',
  purchasePrice2ifIncludeFreight: '含运费(第三优先供应商)',
  purchasePrice2freightRate: '单件运费(第三优先供应商)',
  purchasePrice2ifSignFrameworkAgreement: '年框(第三优先供应商)',
  purchasePrice2supplierClassifyName: '供应商分类(第三优先供应商)',
  purchasePrice2oemDirectSupplier: 'OEM直发(第三优先供应商)',
  purchasePrice2idnlf: '供应商物料号(第三优先供应商)',
  purchasePrice2Price: '含税价格(第三优先供应商)',
  purchasePrice2LeadTime: '期货LT(第三优先供应商)',
  purchasePrice2Esokz: '供应商信息类别(第三优先供应商)'
}
export const transferColumns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    showHeaderOverflow: false
  },
  {
    field: 'msg',
    title: '消息',
    width: 50,
    fixed: 'left'
  },
  {
    field: 'orderNo',
    title: '采购订单',
    width: 150,
    fixed: 'left',
    showOverflow: 'ellipsis',
    slots: {
      default: 'orderNo_default'
    }
  },
  {
    field: 'itemType',
    title: '订单类型',
    width: 100,
    slots: {
      default: 'itemType_default'
    }
  },
  {
    field: 'externalOrderNo',
    title: '销售订单号/行号',
    formatter: ({ cellValue, row }) => cellValue ? cellValue + '/' + row.externalOrderItemNo : '',
    width: 160
  },
  {
    field: 'isOrderException',
    title: '下单异常',
    formatter: ({ cellValue }) => cellValue ? '是' : '否',
    width: 100
  },
  {
    field: 'pendingReasonDesc',
    title: '未采购说明',
    formatter: ({ row }) => (row.pendingReasonName || '') + ' ' + (row.detailPendingReasonDesc || '') + ' ' + (row.pendingReasonDesc || ''),
    width: 140
  },
  {
    field: 'supplierCert',
    title: '合格证标识',
    formatter: ({ row }) => supplierCertList.find(it => it.value === row.supplierCert)?.label || '',
    minWidth: 90
  },
  {
    field: 'factory',
    title: '工厂',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU',
    width: 80
  },
  {
    field: 'materialDescribe',
    title: 'SKU描述',
    width: 120
  },
  {
    field: 'ifUseBiAmountStrategy',
    title: '凑单策略',
    width: 200,
    slots: {
      default: 'ifUseBiAmountStrategy_default'
    }
  },
  {
    field: 'skuTypeName',
    title: 'SKU类型',
    width: 120
  },
  {
    field: 'originQuantity',
    title: '缺货数量',
    width: 100
  },
  {
    field: 'quantity',
    title: '申请数量',
    width: 120,
    editRender: { name: '$input', enabled: true, props: { type: 'number' } },
    slots: {
      default: 'quantity_default',
      edit: 'quantity_edit'
    }
  },
  {
    field: 'quantityUnit',
    title: '基本单位',
    width: 100,
    slots: {
      default: 'quantityUnit_default'
    }
  },
  {
    field: 'position',
    title: '收货库位',
    width: 120,
    slots: {
      default: 'position_default'
    }
  },
  {
    field: 'shipWarehousePositon',
    title: '发货库位',
    width: 120,
    slots: {
      default: 'shipWarehousePositon_default'
    }
  },
  {
    field: 'customInstructions',
    title: '定制说明',
    width: 150,
    showOverflow: 'ellipsis',
    slots: {
      default: 'customInstructions_default'
    }
  },
  {
    field: 'supplier',
    title: '分配供应商',
    width: 200,
    slots: {
      default: 'supplier_default'
    },
    sortable: true,
    sortBy: 'supplier',
    sortType: 'string'
  },
  {
    field: 'replaceFirstSupplierReason',
    title: '推优替换原因',
    width: 260,
    slots: {
      default: 'replaceFirstSupplierReason_default'
    }
  },
  {
    field: 'paymentTermsCodeName',
    title: '付款条件',
    width: 80,
    slots: {
      default: 'paymentTermsCodeName_default'
    }
  },
  {
    field: 'isFree',
    title: '免费',
    width: 50,
    slots: {
      default: 'isFree_default'
    }
  },
  {
    field: 'unitPrice',
    title: '采购价格',
    width: 160,
    slots: {
      default: 'unitPrice_default'
    }
  },
  {
    field: 'meinsName',
    title: '采购单位',
    width: 80,
    slots: {
      default: 'meinsName_default'
    }
  },
  {
    field: 'currency',
    title: '币种',
    width: 80,
    slots: {
      default: 'currency_default'
    }
  },
  {
    field: 'taxRate',
    title: '进项税',
    width: 120,
    slots: {
      default: 'taxRate_default'
    }
  },
  {
    field: 'msg',
    title: '操作',
    width: 50,
    fixed: 'right',
    slots: {
      default: 'operation_default'
    }
  }
]
