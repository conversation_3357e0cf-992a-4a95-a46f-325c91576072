<template>
  <div class="mrp-result">
    <el-card>
      <div slot="header">
        <el-radio-group
          v-model="activeName"
          @change="handleTab"
          style="margin-bottom: 30px"
        >
          <el-radio-button label="mrp"
          >入仓运算
          </el-radio-button>
          <el-radio-button label="pr">直发查询</el-radio-button>
        </el-radio-group>
      </div>
      <el-form
        v-if="activeName === 'mrp'"
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        style="width: 100%"
        label-suffix=":"
        label-width="90px"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="SKU" prop="skuNo" required>
              <el-input
                v-model="searchForm.skuNo"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工厂" prop="factory" required>
              <el-select
                v-model="searchForm.factory"
                filterable
                default-first-option
                clearable
                style="width: 100%"
                placeholder="请选择工厂"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.factoryCode + item.factoryName"
                  :label="item.factoryCode + ' ' + item.factoryName"
                  :value="item.factoryCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="MRP区域" prop="mrpArea" required>
              <el-select
                clearable
                filterable
                default-first-option
                v-model="searchForm.mrpArea"
                placeholder="MRP区域编码"
                style="width: 100%"
              >
                <el-option
                  v-for="item in mrpAreaListFilterByFactory"
                  :key="item.code + item.description"
                  :label="item.code + ' ' + item.description"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :span="3"
            :style="{ display: 'flex', justifyContent: 'space-between' }"
          >
            <el-button
              type="primary"
              style="width: 80px"
              :loading="searchLoading"
              @click="handleSearch('searchForm')"
            >
              查询
            </el-button>
            <el-button type="primary" :loading="MRPLoading" @click="handleMRP('searchForm')">
              运行MRP
            </el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-form
        v-if="activeName === 'pr'"
        ref="prForm"
        :rules="rules"
        :model="prForm"
        style="width: 100%"
        label-suffix=":"
        label-width="90px"
      >
        <el-row :gutter="20">
          <el-col :span="8" v-for="item in prFormFields" :key="item.prop">
            <el-form-item :label="item.label" :prop="item.prop" :rules="item.rules||[]">
              <el-input
                v-if="item.type==='input'"
                v-model="prForm[item.prop]"
                clearable
                style="width: 100%"
              />
              <el-select
                v-if="item.type==='purchaseGroup' && setPower(userRole)"
                v-model="prForm.purchaseGroup"
                allow-create
                filterable
                clearable
                multiple
                collapse-tags
                default-first-option
                style="width: 100%"
                placeholder="请选择采购员"
              >
                <el-option
                  v-for="item in purchaseList"
                  :key="item.groupCode + item.userName"
                  :label="item.groupCode + ' ' + item.userName"
                  :value="item.groupCode"
                >
                </el-option>
              </el-select>
              <SelectPurchaseGroup
                v-if="item.type==='purchaseGroup'&& !setPower(userRole)"
                :data.sync="prForm.purchaseGroup"
                @getUserPurchaseGroup="handelPurchaseGroup"
                v-on="$listeners"
              />
            </el-form-item>
          </el-col>
          <el-col
            :span="3"
            :style="{ display: 'flex', justifyContent: 'space-between' }"
          >
            <el-button
              type="primary"
              style="width: 80px"
              :loading="searchLoading"
              @click="handleSearch('prForm')"
            >
              查询
            </el-button>
            <el-button type="primary" :loading="MRPLoading" @click="handleMRP('prForm')">
              运行MRP
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div v-if="activeName === 'mrp'">
      <DividerHeader>
        商品信息
        <i
          :class="[
            'alignRight',
            hideProductInfo ? 'el-icon-arrow-down' : 'el-icon-arrow-up',
          ]"
          style="float: right; margin: 13px 10px 0 0"
          @click="
            () => {
              hideProductInfo = !hideProductInfo;
            }
          "
        ></i>
      </DividerHeader>
      <el-row v-show="hideProductInfo" class="marginleft">
        <el-col :span="25">
          <div class="grid-content">
            物料状态:{{ getSkuStatus(resultMsg.skuStatus) }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            采购类型:{{ pmodeName(resultMsg.purchaseType) }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            批量:{{ getMrpBatch(resultMsg.mrpBatch) }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            采购员:{{ resultMsg.buyerStr === null ? '无' : resultMsg.buyerStr }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            采购MOQ:{{ resultMsg.moq === null ? '无' : resultMsg.moq }}
          </div>
        </el-col>
        <el-col :span="25">
          <!-- purchasePriceVOList？ .leadTime -->
          <div class="grid-content">
            采购提前期:{{
              resultMsg.leadTime === null ? '无' : resultMsg.leadTime
            }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            安全库存:{{
              resultMsg.safeStockQuantity === null
                ? '无'
                : resultMsg.safeStockQuantity
            }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            再订货点:{{ resultMsg.rop === null ? '无' : resultMsg.rop }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            舍入值:{{ resultMsg.roq === null ? '无' : resultMsg.roq }}
          </div>
        </el-col>
        <el-col :span="25">
          <div class="grid-content">
            最大库存水平:{{
              resultMsg.maxStock === null ? '无' : resultMsg.maxStock
            }}
          </div>
        </el-col>
      </el-row>
    </div>
    <DividerHeader>
      MRP运算结果
      <i
        :class="[
          'alignRight',
          hideResult ? 'el-icon-arrow-down' : 'el-icon-arrow-up',
        ]"
        style="float: right; margin: 13px 10px 0 0"
        @click="
          () => {
            hideResult = !hideResult;
          }
        "
      ></i>
    </DividerHeader>
    <vxe-grid
      v-show="hideResult"
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="resultGrid"
      height="640"
      id="result_grid"
      row-id="id"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="listData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
      highlight-hover-row
      :scroll-y="{ gt: 100 }"
    >
      <template v-slot:toolbar_buttons>
        <el-row class="resultRow" v-if="resultMsg.runTime">
          <el-col :span="6"> 最近一次运行日期: {{ resultMsg.runDateTime }}</el-col>
        </el-row>
      </template>

      <template v-slot:purchaseGroup_default="{ row }">
        {{ row.purchaseGroup + ' ' + (purchaseList.find(item => item.groupCode === row.purchaseGroup) || {}).userName }}
      </template>

      <template v-slot:factory_default="{ row }">
        {{ row.factory + ' ' + (factoryList.find(item => item.factoryCode === row.factory) || {}).factoryName }}
      </template>

      <template v-slot:status_default="{ row }">
        {{ filterStatus(row.status) }}
      </template>

      <template v-slot:deliveryDate_default="{ row }">
        {{ row.deliveryDate ? row.deliveryDate.slice(0, 10) : '' }}
      </template>

      <template v-slot:mrpType_default="{ row }">
        {{
          getMrpType(
            row.mrpType,
            row.stockingStrategy,
            row.isCalculate,
            row.createMark
          )
        }}
      </template>
      <template v-slot:mrpTypeOrder_default="{ row }">
        {{ row.recordNo ? row.recordNo + '/' + row.recordItemNo : '' }}
      </template>
      <template #pager v-if="activeName === 'mrp'">
        <vxe-pager
          :layouts="[
            'Sizes',
            'PrevJump',
            'PrevPage',
            'Number',
            'NextPage',
            'NextJump',
            'FullJump',
            'Total',
          ]"
          :border="true"
          :current-page.sync="tablePage.currentPage"
          :page-size.sync="tablePage.pageSize"
          :total="tablePage.total"
          @page-change="handlePageChange"
        >
        </vxe-pager>
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import DividerHeader from '@/components/DividerHeader'
import { mapState } from 'vuex'
import { getmrpResult, getPrResult, getPurchaseGroup, getUserInfo, runRtmrp, runPr } from '@/api/mrp'
import { getFactoryList, getMrpType, setPower } from './utils'
import { mrpResult, prResult, prFormFields } from './const'
import SelectPurchaseGroup from './components/selectPurchaseGroup.vue'

export default {
  name: 'mrpResult',
  components: {
    DividerHeader,
    SelectPurchaseGroup
  },
  data() {
    return {
      userPurchase: [],
      hideProductInfo: true,
      hideResult: true,
      prFormFields,
      searchForm: {
        factory: '',
        mrpArea: '',
        skuNo: ''
      },
      prForm: {
        soNo: '',
        soItemNo: '',
        skuNo: '',
        purchaseGroup: []
      },
      searchLoading: false,
      MRPLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      columns: mrpResult,
      listData: [],
      selectList: [],
      rules: {
        skuNo: [
          {
            required: true,
            message: 'SKU不能为空！',
            trigger: ['change', 'blur']
          }
        ],
        factory: [
          {
            required: true,
            message: '工厂不能为空！',
            trigger: ['change', 'blur']
          }
        ],
        mrpArea: [
          {
            required: true,
            message: 'MRP区域不能为空！',
            trigger: ['change', 'blur']
          }
        ]
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      resultMsg: {},
      mrpAreaListFilterByFactory: [],
      activeName: 'mrp'
    }
  },
  async created() {
    const pList = []
    if (this.companyInfoList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryCompanyInfoList'))
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(
        this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
      )
    }
    if (!this.mrpAreaList || this.mrpAreaList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryMrpAreaList'))
    }
    if (this.purchaseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    await Promise.all(pList)
    if (this.setPower(this.userRole)) {
      this.userPurchaseGroup()
    }
    this.getUserRole()
  },
  computed: {
    ...mapState({
      companyInfoList: (state) => state.mrp.companyInfoList,
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      mrpAreaList: (state) => state.mrp.mrpAreaList,
      purchaseList: (state) => state.orderPurchase.purchaseList,
      userRole: (state) => state.userRole
    }),
    factoryList() {
      return getFactoryList(this.companyFactoryList)
    }
  },
  watch: {
    'searchForm.factory': function (newValue) {
      this.searchForm.mrpArea = ''
      this.mrpAreaListFilterByFactory = this.mrpAreaList.filter(
        (item) => item.factory === newValue
      )
    }
  },
  methods: {
    getMrpType,
    setPower,
    filterStatus(str) {
      return str === 'Y' ? '已转单' : str === 'N' ? '未转单' : ''
    },
    handelPurchaseGroup(list) {
      if (list.length === 1) {
        this.userPurchase = list.map(item => item.groupCode)
      }
    },
    async userPurchaseGroup() {
      const res = await getPurchaseGroup({
        securityUsernameList: window.CUR_DATA.user && window.CUR_DATA.user.name
      })
      this.userPurchase = res.data.map(item => item.groupCode)
      this.prForm.purchaseGroup = this.userPurchase
    },
    handleTab(tab) {
      this.columns = tab === 'mrp' ? mrpResult : prResult
      this.clear()
    },
    clear() {
      this.listData = []
      this.tablePage.total = 0
      this.resultMsg = {}
    },
    async getUserRole() {
      try {
        const res = await getUserInfo({
          username: window.CUR_DATA.user && window.CUR_DATA.user.name
        })
        let defaultCompany = (
          this.companyInfoList.find(
            (item) => item.subCompanyId === res.subCompany
          ) || {}
        ).code
        if (defaultCompany) {
          this.searchForm.factory = (
            (
              this.companyFactoryList.find(
                (item) => item.companyCode === defaultCompany
              ) || {}
            ).factoryList || []
          ).map((item) => item.factoryCode)[0]
          console.log(defaultCompany, this.searchForm.factory)
        }
      } catch (error) {
        console.log(error)
      }
    },
    handleMRP(formKey) {
      this.$refs[formKey].validate(async (valid) => {
        if (valid) {
          try {
            this.MRPLoading = true
            if (formKey === 'searchForm') {
              const params = {
                ...this.searchForm,
                requestAt: Date.now()
              }
              await runRtmrp(params)
            } else {
              const data = {
                soNo: this.prForm.soNo
              }
              await runPr(data)
            }
            this.handleSearch(formKey)
            this.MRPLoading = false
          } catch (error) {
            this.MRPLoading = false
            console.log(error)
          }
        } else {
          return false
        }
      })
    },
    handleSearch(formKey) {
      this.$refs[formKey].validate((valid) => {
        if (valid) {
          this.tablePage.currentPage = 1
          if (formKey === 'searchForm') {
            this.getResult()
          } else {
            this.getPrResult()
          }
        } else {
          return false
        }
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.$refs.searchForm.validate((valid) => {
        if (valid) {
          this.getResult()
        } else {
          return false
        }
      })
    },
    async getResult() {
      try {
        const params = {
          ...this.searchForm,
          pageNo: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await getmrpResult(params)
        if (res) {
          this.listData = res.records
          this.tablePage.total = res.total
          this.resultMsg = res
        } else {
          this.listData = []
          this.tablePage.total = 0
          this.resultMsg = {}
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error)
      }
    },
    async getPrResult() {
      try {
        const params = {
          ...this.prForm
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await getPrResult(params)
        if (res) {
          this.listData = res
        } else {
          this.listData = []
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
      }
    },
    changeMrpCode(type, event) {
      if (type === 'mrpAreaObj') {
        this.searchForm.mrpArea = event
      }
    },
    pmodeName(pmode) {
      if (pmode === '1') {
        return '标准'
      }
      if (pmode === '2') {
        return '生产'
      }
      if (pmode === '3') {
        return '委外加工'
      }
      if (pmode === '4') {
        return '寄售'
      }
      if (pmode === '5') {
        return '外购'
      }
      if (pmode === null) {
        return '无'
      }
      return ''
    },
    getMrpBatch(mrpBatch) {
      if (mrpBatch === 'EX') {
        return '日批'
      }
      if (mrpBatch === 'MB') {
        return '月批'
      }
      if (mrpBatch === null) {
        return '无'
      } else {
        return '年批'
      }
    },
    getSkuStatus(skuStatus) {
      if (skuStatus === '0') {
        return '停用'
      } else if (skuStatus === null) {
        return '无'
      } else if (typeof skuStatus === 'string') {
        return '启用'
      } else {
        return ''
      }
    }
  }
}
</script>
<style lang="scss">
  .mrp-result {
    .el-card__header {
      padding-bottom: 0;
    }
  }
</style>
<style lang="scss" scoped>
  .mrp-result {
    padding: 20px;
  }

  .resultRow {
    .el-col-6 {
      margin-left: 10px;
      font-size: 14px;
      color: #606266;
      line-height: 32px;
    }
  }

  .marginleft {
    margin-left: 10px;
  }

  ::v-deep .el-col-25 {
    width: 20%;
    font-size: 14px;
    color: #606266;
    line-height: 32px;
  }

  .el-icon-arrow-up.alignRight {
    font-size: 25px;
    float: right;
    margin: 15px 10px 0 0;
  }

  .el-icon-arrow-down.alignRight {
    font-size: 25px;
    float: right;
    margin: 15px 10px 0 0;
  }
</style>
