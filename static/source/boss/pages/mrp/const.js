export const mrpResult = [
  {
    field: 'deliveryDate',
    title: '需求日期',
    minWidth: 80,
    slots: {
      default: 'deliveryDate_default'
    }
  },
  {
    field: 'mrpType',
    title: 'mrp单据',
    minWidth: 120,
    slots: {
      default: 'mrpType_default'
    }
  },
  {
    field: '',
    title: '单据号/行号',
    minWidth: 80,
    slots: {
      default: 'mrpTypeOrder_default'
    }
  },
  {
    field: 'originQuantity',
    title: '供给/需求数量',
    minWidth: 80
  },
  {
    field: 'availableQuantity',
    title: '可用数量',
    minWidth: 80
  },
  {
    field: 'position',
    title: '仓库地点',
    minWidth: 80
  },
  {
    field: 'outPosition',
    title: '发货仓库地点',
    minWidth: 80
  }
]

export const prResult = [
  {
    field: 'factory',
    title: '工厂',
    minWidth: 80,
    slots: {
      default: 'factory_default'
    }
  },
  {
    field: 'soItemNo',
    title: 'SO行号',
    minWidth: 80
  },
  {
    field: 'soNo',
    title: 'SO单号',
    minWidth: 80
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    minWidth: 80,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    minWidth: 80
  },
  {
    field: 'quantity',
    title: '数量',
    minWidth: 80
  },
  {
    field: 'status',
    title: '转单状态',
    minWidth: 80,
    slots: {
      default: 'status_default'
    }
  },
  {
    field: 'poNo',
    title: 'PO单号',
    minWidth: 120
  },
  {
    field: 'msg',
    title: '未生成负需求原因或转单信息',
    minWidth: 80
  }
]

export const prFormFields = [{
  type: 'input',
  label: 'SO订单号',
  prop: 'soNo',
  rules: [{ required: true, message: '请输入要查询的SO单号' }]
}, {
  type: 'input',
  label: 'SO行号',
  prop: 'soItemNo'
}, {
  type: 'input',
  label: 'SKU编码',
  prop: 'skuNo'
}, {
  type: 'purchaseGroup',
  label: '采购员',
  prop: 'purchaseGroup'
}]
