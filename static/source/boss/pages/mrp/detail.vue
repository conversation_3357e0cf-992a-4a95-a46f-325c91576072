<template>
  <div class="detail">
    <DividerHeader>
      查询条件
      <i :class="['alignRight', hideResult ? 'el-icon-arrow-down' : 'el-icon-arrow-up']" style="float: right; margin: 13px 10px 0 0;" @click="fold"></i>
    </DividerHeader>
    <DetailForm ref="ruleForm"
                :hideResult="hideResult"
                :purchaseList="purchaseList"
                :searchLoading="searchLoading"
                :userRole="userRole"
                @filterPurchaseList="filterPurchaseList"
                @handleSearch="handleSearch"/>

    <vxe-grid column-key
              v-loading="tableLoading"
              element-loading-text="加载中，请不要刷新浏览器"
              border
              resizable
              keep-source
              show-overflow
              show-header-overflow
              ref="detailGrid"
              :height="tableHeight"
              id="mrp_detail_grid"
              size="mini"
              row-id="id"
              align="center"
              :loading="tableLoading"
              :custom-config="tableCustom"
              :data="listData"
              :columns="columns"
              :toolbar-config="tableToolbar"
              :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}"
              :row-class-name="tableRowClassName"
              :scroll-y="{gt: 20}"
              @cell-dblclick="cellClick"
              @scroll="scroll">
      <template v-slot:toolbar_tools>
        <el-switch
          v-model="sapOrder"
          active-text="SAP销售订单号"
          inactive-text="OMS销售订单号"
          :inactive-value="false"
          :active-value="true"
          style="margin-right: 20px"
        />
        <el-button type="primary"
                    style="width: 80px; margin-right: 10px;"
                    :disabled="disabled"
                    @click="handleDownExcel"
                    v-if="show">
          下载
        </el-button>
      </template>

      <template v-slot:productGroup_default="{ row }">
        {{ (row.productVO.productGroup || '') + ' '+ (row.productVO.productGroupName || '') }}
      </template>
      <template v-slot:skuNo_default="{ row }">
       <span class="is-vpi"  v-if="row.prType === 1">
          <img src="../../assets/images/v.png" class="product-vpi-icon " />
        </span>
        <span class="is-vpi" v-if="row.prType === 2" style="padding-left: 2px">
          指
        </span>
        <span>{{row.skuNo}}</span>
      </template>

      <template v-slot:mrpArea_default="{ row }">
        {{ row.mrpArea + ' ' + row.mrpAreaDesc }}
      </template>

      <template v-slot:position_default="{ row }">
        {{ row.position ? row.position + ' ' + (warehouseList.find(item => item.warehouseLocationCode === row.position) || {}).warehouseLocationName : '' }}
      </template>

      <template v-slot:purchaseGroup_default="{ row }">
        {{ row.purchaseGroup + ' ' + (purchaseList.find(item => item.groupCode === row.purchaseGroup) || {}).userName }}
      </template>

      <template v-slot:productPositioningName_default="{ row }">
        {{ row.productVO.productPositioningName }}
      </template>

      <template v-slot:mstaeAllSwitch_default="{ row }">
        {{ row.productVO.mstaeAllSwitch=== 1 ? '是' : '否'}}
      </template>

      <template v-slot:mrpType_default="{ row }">
         {{ getMrpType(row.mrpType,row.stockingStrategy, row.isCalculate, row.createMark) }}
      </template>

      <template v-slot:mrpTypeOrder_default="{ row }">
        <el-tooltip v-if="row.isPreOrder && row.preOrderCheckTime" effect="light" :content="row.preOrderCheckTime && row.preOrderCheckTime.slice(0,10)" placement="top">
          <el-button size="mini" circle type="danger" style="border: none;color: white;padding: 2px 2px;">预</el-button>
        </el-tooltip>
        <el-button v-else-if="row.isPreOrder" size="mini" circle type="danger" style="border: none;color: white;padding: 2px 2px;">预</el-button>
        <span v-if="sapOrder && row.mrpType === 'so'">
          {{ row.externalOrderNo }}
        </span>
        <span v-else>
           {{ row.recordNo }}
        </span>
      </template>

      <template v-slot:recordItemNo="{ row }">
        <span v-if="sapOrder && row.mrpType === 'so'">
          {{ row.externalOrderItemNo }}
        </span>
        <span v-else>
           {{ row.recordItemNo }}
        </span>
      </template>

      <template v-slot:recordCreateDate_default="{ row }">
        {{ row.recordCreateDate && row.recordCreateDate.slice(0,10) }}
      </template>

      <template v-slot:deliveryDate_default="{ row }">
        {{ row.deliveryDate && row.deliveryDate.slice(0,10) }}
      </template>

      <template v-slot:originQuantity_default="{ row }">
        {{ getOriginQuantity(row) }}
      </template>

      <template v-slot:availableQuantity_default="{ row }">
        <span :style="{color: 'red'}" v-if="row.demandQuantity < 0">{{ row.demandQuantity }}</span>
        <span v-else>{{ row.demandQuantity }}</span>
      </template>

      <template v-slot:saleForecastQuantity_default="{ row }">
        {{ row.mrpType === 'so' && row.itemType === 'Z002' ? row.originQuantity : ''}}
      </template>
      <template v-slot:saleQuantity_default="{ row }">
        {{ row.mrpType === 'so' && row.itemType !== 'Z002' ? row.originQuantity : ''}}
      </template>
      <template v-slot:procurementQuantity_default="{ row }">
        {{ row.mrpType.slice(0,2) === 'po'  ? row.originQuantity : ''}}
      </template>
      <template v-slot:procurementApplyQuantity_default="{ row }">
        {{ row.mrpType === 'pr'  ? row.originQuantity : ''}}
      </template>
      <template v-slot:productQuantity_default="{ row }">
        {{ row.mrpType.slice(0,3) === 'pro' ? row.originQuantity : ''}}
      </template>
      <template v-slot:planQuantity_default="{ row }">
        {{ row.mrpType === 'wip'  ? row.originQuantity : ''}}
      </template>
      <template v-slot:deliveryQuantity_default="{ row }">
        {{ row.mrpType === 'ido' || row.mrpType === 'dnP' || row.mrpType === 'dnD' ? row.originQuantity : ''}}
      </template>

      <template v-slot:inventoryQuantity_default="{ row }">
        {{ row.mrpType === 'stock'  ? row.originQuantity : '' }}
      </template>

      <template v-slot:customerName_default="{ row }">
        {{ row.customerNo ? row.customerNo + ' ' + row.customerName : ' ' }}
      </template>

      <template v-slot:isUrgent_default="{ row }">
        {{ row.mrpType === 'so' && row.urgentMsg ? row.urgentMsg.split(' ')[1] : '' }}
      </template>

      <template v-slot:isSpec_default="{ row }">
        {{ row.mrpType === 'so' && row.specMsg ? row.specMsg.split(' ')[7].slice(0,-1) : '' }}
      </template>
      <template v-slot:purchasePriceVOList0="{ row }">
        <el-popover v-if="row.supplier1Detail.providerNo"
                    placement="top"
                    width="800"
                    trigger="hover"
                    popper-class="align">
           <el-row>
            <el-col :span="8">
              综合得分：{{row.supplier1Detail.totalScore}}
            </el-col>
            <el-col :span="4">
              执行建议：{{getGrade(row.supplier1Detail.grade)}}
            </el-col>
            <el-col :span="6" style="display: flex">
              风险提示：
              <el-tooltip class="item" effect="dark" :content="row.supplier1Detail.riskWarn" placement="top-start">
                <span style="width: 100px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{row.supplier1Detail.riskWarn}}</span>
              </el-tooltip>
            </el-col>
            <el-col :span="6">
              策略类型：{{row.supplier1Detail.tacticsName}}
            </el-col>
          </el-row>
          <el-row>
          <el-col :span="24" style="display: flex">
              策略详情：
              <el-tooltip class="item" effect="dark" :content="row.supplier1Detail.tacticsDetail" placement="top-start">
                <span style="width: 400px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{row.supplier1Detail.tacticsDetail}}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              是否代理品牌：{{ row.supplier1Detail.ifAgency === 0 ? '否' : row.supplier1Detail.ifAgency === 1 ? '是' : '' }}
            </el-col>
            <el-col :span="16" style="display: flex">
              是否原厂品牌：
              {{ row.supplier1Detail.ifTradeCertificate === 0 ? '否' : [1,2].includes(row.supplier1Detail.ifTradeCertificate) ? '是' : '' }}
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              默认运营路线：{{esokzName(row.supplier1Detail.esokz)}}
            </el-col>
            <el-col :span="4">
              币种：{{ getCurrency(row.supplier1Detail.currency, row.supplier1Detail) }}
            </el-col>
            <el-col :span="6">
              进项税：{{row.supplier1Detail.taxRate + '%' || ''}}
            </el-col>
            <el-col :span="6">
              订单单位：{{ formatQuantityUnit(row.supplier1Detail.meins) }}
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              最小起订单：{{row.supplier1Detail.purchaseMoq || ''}}
            </el-col>
            <el-col :span="4">
              招投标：{{getIfBid(row.supplier1Detail.ifBid)}}
            </el-col>
            <el-col :span="6">
              含运费：{{ row.supplier1Detail.ifIncludeFreight === 1 ? '是' : '否' }}
            </el-col>
            <el-col :span="6">
              单件运费：{{row.supplier1Detail.freightRate || ''}}
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              付款条款：{{row.supplier1Detail.paymentTermsCodeName || ''}}
            </el-col>
            <el-col :span="4">
              对接库存：{{row.supplier1Detail.ifUpInventory || 0}}
            </el-col>
            <el-col :span="6">
              供应商分类: {{ row.supplier1Detail.supplierClassifyName || '' }}
            </el-col>
            <el-col :span="6">
              年框: {{ row.supplier1Detail.ifSignFrameworkAgreement  === 1 ? '是' : '否'  }}
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              供应商物料号：{{ row.supplier1Detail.idnlf }}
            </el-col>
            <el-col :span="4">
              OEM直发：{{
                row.supplier1Detail.oemDirectSupplier === 1 ? '是' : '否'
              }}
            </el-col>
          </el-row>
          <el-button slot="reference"
                     type="text"
                     :style="{border: 'none'}">
            {{ row.supplier1Detail.providerNo + ' '+ row.supplier1Detail.providerName }}
          </el-button>
        </el-popover>
      </template>

      <template v-slot:purchasePrice0Price_default="{ row }">
        {{ getPurchasePrice(row.supplier1Detail) }}
      </template>

      <template v-slot:purchasePriceVOList1="{ row }">
        <el-popover v-if="row.supplier2Detail.providerNo"
                    placement="top"
                    width="600"
                    trigger="hover">
          <el-row :gutter="20">
             <el-row>
            <el-col :span="8">
              综合得分：{{row.supplier2Detail.totalScore}}
            </el-col>
            <el-col :span="8">
              执行建议：{{getGrade(row.supplier2Detail.grade)}}
            </el-col>
            <el-col :span="8" style="display: flex">
              风险提示：
              <el-tooltip class="item" effect="dark" :content="row.supplier2Detail.riskWarn" placement="top-start">
                <span style="width: 100px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{row.supplier2Detail.riskWarn}}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              策略类型：{{row.supplier2Detail.tacticsName}}
            </el-col>
          <el-col :span="16" style="display: flex">
              策略详情：
              <el-tooltip class="item" effect="dark" :content="row.supplier2Detail.tacticsDetail" placement="top-start">
                <span style="width: 200px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{row.supplier2Detail.tacticsDetail}}</span>
              </el-tooltip>
            </el-col>
          </el-row>
            <el-row>
              <el-col :span="8">
                是否代理品牌：{{ row.supplier2Detail.ifAgency === 0 ? '否' : row.supplier2Detail.ifAgency === 1 ? '是' : '' }}
              </el-col>
              <el-col :span="16" style="display: flex">
                是否原厂品牌：
                {{ row.supplier2Detail.ifTradeCertificate === 0 ? '否' : [1,2].includes(row.supplier2Detail.ifTradeCertificate) ? '是' : '' }}
              </el-col>
            </el-row>
            <el-col :span="8">
              对接库存：{{row.supplier2Detail.ifUpInventory || 0}}
            </el-col>
            <el-col :span="8">
              含运费：{{ row.supplier2Detail.ifIncludeFreight === 1 ? '是' : '否' }}
            </el-col>
            <el-col :span="8">
              单件运费：{{row.supplier2Detail.freightRate || ''}}
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              供应商分类: {{ row.supplier2Detail.supplierClassifyName || '' }}
            </el-col>
            <el-col :span="8">
              年框: {{ row.supplier2Detail.ifSignFrameworkAgreement  === 1 ? '是' : '否'  }}
            </el-col>
            <el-col :span="8">
              OEM直发: {{ row.supplier2Detail.oemDirectSupplier === 1 ? '是' : '否' }}
            </el-col>
            <el-col :span="8">
              供应商物料号：{{ row.supplier2Detail.idnlf }}
            </el-col>
          </el-row>
          <el-button slot="reference"
                     type="text"
                     :style="{border: 'none'}">
            {{ row.supplier2Detail.providerNo + ' '+ row.supplier2Detail.providerName }}
          </el-button>
        </el-popover>
      </template>

       <template v-slot:purchasePrice1Price_default="{ row }">
        {{ getPurchasePrice(row.supplier2Detail) }}
      </template>

      <template v-slot:purchasePriceVOList2="{ row }">
        <el-popover v-if="row.supplier3Detail.providerNo"
                    placement="top"
                    width="600"
                    trigger="hover">
          <el-row :gutter="20">
             <el-row>
            <el-col :span="8">
              综合得分：{{row.supplier3Detail.totalScore}}
            </el-col>
            <el-col :span="8">
              执行建议：{{getGrade(row.supplier3Detail.grade)}}
            </el-col>
            <el-col :span="8" style="display: flex">
              风险提示：
              <el-tooltip class="item" effect="dark" :content="row.supplier3Detail.riskWarn" placement="top-start">
                <span style="width: 100px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{row.supplier3Detail.riskWarn}}</span>
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              策略类型：{{row.supplier3Detail.tacticsName}}
            </el-col>
          <el-col :span="16" style="display: flex">
              策略详情：
              <el-tooltip class="item" effect="dark" :content="row.supplier3Detail.tacticsDetail" placement="top-start">
                <span style="width: 200px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{row.supplier3Detail.tacticsDetail}}</span>
              </el-tooltip>
            </el-col>
          </el-row>
            <el-row>
              <el-col :span="8">
                是否代理品牌：{{ row.supplier3Detail.ifAgency === 0 ? '否' : row.supplier3Detail.ifAgency === 1 ? '是' : '' }}
              </el-col>
              <el-col :span="16" style="display: flex">
                是否原厂品牌：
                {{ row.supplier3Detail.ifTradeCertificate === 0 ? '否' : [1,2].includes(row.supplier3Detail.ifTradeCertificate) ? '是' : '' }}
              </el-col>
            </el-row>
            <el-col :span="8">
              对接库存：{{row.supplier3Detail.ifUpInventory || 0}}
            </el-col>
            <el-col :span="8">
              含运费：{{ row.supplier3Detail.ifIncludeFreight === 1 ? '是' : '否' }}
            </el-col>
            <el-col :span="8">
              单件运费：{{ row.supplier3Detail.freightRate || '' }}
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              供应商分类: {{ row.supplier3Detail.supplierClassifyName || '' }}
            </el-col>
            <el-col :span="8">
              年框: {{ row.supplier3Detail.ifSignFrameworkAgreement  === 1 ? '是' : '否'  }}
            </el-col>
            <el-col :span="8">
              OEM直发: {{ row.supplier3Detail.oemDirectSupplier === 1 ? '是' : '否' }}
            </el-col>
            <el-col :span="8">
              供应商物料号：{{ row.supplier3Detail.idnlf }}
            </el-col>
          </el-row>
          <el-button slot="reference"
                     type="text"
                     :style="{border: 'none'}">
            {{ row.supplier3Detail.providerNo + ' '+ row.supplier3Detail.providerName }}
          </el-button>
        </el-popover>
      </template>

      <template v-slot:purchasePrice2Price_default="{ row }">
        {{ getPurchasePrice(row.supplier3Detail) }}
      </template>

      <template #pager>
        <vxe-pager :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
                   :page-sizes="[100,500,1000,2000]"
                   :border="true"
                   :current-page.sync="tablePage.currentPage"
                   :page-size.sync="tablePage.pageSize"
                   :total="tablePage.total"
                   @page-change="handlePageChange">
        </vxe-pager>
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getPrDetail } from '@/api/mrp'
import { safeRun, throttle, initVersion } from '@/utils/index'
import { writeFile } from '@boss/excel'
import { detailColumns, detailMapping } from './constants'
import { formatPrListData, getMrpType, getIfBid, esokzName, getCurrency, getGrade, setPower, getPurchasePrice, fold, parsePurchaseGroup, filterHiddenFields, dragFields, cellClick } from './utils'
import DividerHeader from '@/components/DividerHeader'
import moment from 'moment'
import DetailForm from './components/detail/form.vue'
import Sortable from 'sortablejs'

export default {
  name: 'mrpDetail',
  components: {
    DetailForm,
    DividerHeader
  },
  data () {
    return {
      searchForm: {},
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        zoom: true,
        slots: {
          tools: 'toolbar_tools'
        }
      },
      columns: detailColumns,
      listData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 500
      },
      disabled: true,
      loading: null,
      isSpec: false,
      msgLoading: false,
      is_extending: true,
      hideResult: true,
      tableHeight: 590,
      filtPurchaseList: [],
      show: false,
      sapOrder: false
    }
  },
  async created () {
    const pList = []
    if (!Object.keys(this.dictList).length) {
      pList.push(this.$store.dispatch('orderCommon/queryDictList'))
    }
    if (this.purchaseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (!this.userRole || this.userRole.length === 0) {
      pList.push(this.$store.dispatch('getUserRole'))
    }
    await Promise.all(pList)
    if (this.userRole.find(item => item === 'MRP-采购经理' || item === 'MRP-采购总监' || item === 'MRP-管理员' || item === 'MRP-采购员' || item === 'MRP-EVM运营')) {
      this.show = true
    }
    this.columnDrop()
  },
  beforeMount () {
    initVersion({ newVersion: '20240117', versionKey: 'mrpDetailVersionKey', columKey: 'detailGrid_fullColumn' })
    const fullcolumns = JSON.parse(localStorage.getItem('detailGrid_fullColumn'))
    if (fullcolumns) {
      this.columns = fullcolumns.map(item => {
        item.field = item.property
        return item
      })
    }
  },
  mounted () {
    this.$nextTick(function () {
      this.selfAdaption()
      window.addEventListener('resize', this.selfAdaption, false)
    })
  },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
    window.removeEventListener('resize', this.selfAdaption, false)
  },
  computed: {
    ...mapState({
      dictList: state => state.orderCommon.dictList || {},
      purchaseList: state => state.orderPurchase.purchaseList,
      warehouseList: state => state.orderPurchase.warehouseList,
      userRole: state => state.userRole
    })
  },
  methods: {
    getIfBid,
    getPurchasePrice,
    setPower,
    getCurrency,
    esokzName,
    getMrpType,
    cellClick,
    getGrade,
    selfAdaption: throttle(function selfAdaption() {
      this.tableHeight = window.innerHeight - this.$refs.detailGrid.$el.offsetTop - 130;
      if (this.tableHeight < 260) {
        this.tableHeight = 260
      }
    }, 800),
    filterPurchaseList (list) {
      this.filtPurchaseList = list
    },
    fold () {
      fold(this, this.$refs.detailGrid, 'hideResult', this.hideResult)
    },
    tableRowClassName ({ row, rowIndex }) {
      if (row.mrpType === 'stock') {
        if (row.isCalculate === 1) {
          return 'highlight-row'
        } else {
          return 'grown-row'
        }
      } else if (row.mrpType === 'pr' ||
        row.mrpType === 'wip' ||
        row.mrpType === 'strP' ||
        row.mrpType === 'strD' ||
        row.mrpType === 'rel') {
        return 'virtual-row'
      }
    },
    formatQuantityUnit (value) {
      if (value && this.dictList['quantityUnit']) {
        const unit = this.dictList['quantityUnit'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    handleSearch (data) {
      this.searchForm = data
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.tablePage.currentPage = 1
          this.getPrDetailData()
        } else {
          return false;
        }
      })
    },
    handlePageChange ({ currentPage, pageSize }) {
      // 没有点击查询按钮时，直接点击分页获取查询条件
      this.searchForm = this.$refs.ruleForm.searchForm
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.getPrDetailData()
        } else {
          return false;
        }
      })
    },
    formatParams (params) {
      let form = { ...params };
      form.skuNo = safeRun(() =>
        form.skuNo
          .split(/\s/).filter((e) => e)
      );
      // form.mrpArea = safeRun(() =>
      //   form.mrpArea
      //     .split(/\s/).filter((e) => e)
      // );
      delete form.createTime
      delete form.brands
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.skuNo.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
        if (params.productGroup.length > 10) {
          ret = false
          this.$message.error('最多支持10个物料组按空格隔开搜索！')
        }
        if (params.mrpArea.length > 50) {
          ret = false
          this.$message.error('最多支持50个MRP区域搜索！')
        }
      })
      return ret
    },
    async getPrDetailData () {
      try {
        if (this.searchForm.createTime === null) {
          this.searchForm.createTime = []
        }
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return

        parsePurchaseGroup(params, setPower(this.userRole), this.filtPurchaseList)

        if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
        }
        if (Array.isArray(params.productGroup)) {
          params.productGroup = params.productGroup.join(' ')
        }
        if (Array.isArray(params.mrpArea)) {
          params.mrpArea = params.mrpArea.join(' ')
        }
        if (Array.isArray(params.purchaseGroup)) {
          params.purchaseGroup = params.purchaseGroup.join(' ')
        }

        if (!params.purchaseGroup) {
          return this.$message.error('没有匹配的采购组')
        }

        params = {
          ...params,
          pageNo: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          startDate: this.searchForm.createTime[0],
          endDate: this.searchForm.createTime[1]
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await getPrDetail(params)
        this.listData = res.records.map(item => {
          return formatPrListData(item, 'detail')
        })
        this.tablePage.total = res.total
        if (this.listData.length === 0) {
          this.$message.info('无符合条件的物料需求')
          this.disabled = true
        } else {
          this.disabled = false
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
        this.is_extending = false
      }
    },
    async getExtendPrDetailData () {
      try {
        if (this.searchForm.createTime === null) {
          this.searchForm.createTime = []
        }
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return

        parsePurchaseGroup(params, setPower(this.userRole), this.filtPurchaseList)

        if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
        }
        if (Array.isArray(params.productGroup)) {
          params.productGroup = params.productGroup.join(' ')
        }
        if (Array.isArray(params.mrpArea)) {
          params.mrpArea = params.mrpArea.join(' ')
        }
        if (Array.isArray(params.purchaseGroup)) {
          params.purchaseGroup = params.purchaseGroup.join(' ')
        }
        this.tablePage.currentPage++
        params = {
          ...params,
          pageNo: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          startDate: this.searchForm.createTime[0],
          endDate: this.searchForm.createTime[1]
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await getPrDetail(params)
        const list = res.records.map(item => {
          return formatPrListData(item, 'detail')
        })
        this.listData = this.listData.concat(list)
        this.is_extending = true
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
        this.is_extending = false
      }
    },
    scroll ({ scrollTop }) {
      let compareHeight
      if (this.$refs.detailGrid.isMaximized()) {
        compareHeight = this.tablePage.pageSize * this.tablePage.currentPage * 36 - 805
      } else {
        // 是否折叠时显示的grid高度不同
        compareHeight = this.tablePage.pageSize * this.tablePage.currentPage * 36 - this.tableHeight + 120
      }
      if (scrollTop >= compareHeight) {
        if (this.is_extending) {
          this.is_extending = false
          this.getExtendPrDetailData()
        }
      }
    },
    async handleDownExcel () {
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.searchForm.createTime === null) {
            this.searchForm.createTime = []
          }
          let params = this.formatParams(this.searchForm);
          if (!this.validate(params)) return

          parsePurchaseGroup(params, setPower(this.userRole), this.filtPurchaseList)

          if (Array.isArray(params.skuNo)) {
            params.skuNo = params.skuNo.join(' ')
          }
          if (Array.isArray(params.productGroup)) {
            params.productGroup = params.productGroup.join(' ')
          }
          if (Array.isArray(params.mrpArea)) {
            params.mrpArea = params.mrpArea.join(' ')
          }
          if (Array.isArray(params.purchaseGroup)) {
            params.purchaseGroup = params.purchaseGroup.join(' ')
          }

          if (!params.purchaseGroup) {
            return this.$message.error('没有匹配的采购组')
          }

          params = {
            ...params,
            pageNo: 1,
            pageSize: -1,
            startDate: this.searchForm.createTime[0],
            endDate: this.searchForm.createTime[1]
          }
          this.loading = this.$loading({
            background: 'rgba(0, 0, 0, 0.5)'
          })
          getPrDetail(params).then((res) => {
            const list = res.records.map(item => {
              return formatPrListData(item, 'detail')
            })
            this.exportExcel(list)
            this.loading.close()
          }).catch((error) => {
            console.log(error);
            this.loading.close()
          })
        } else {
          return false;
        }
      })
    },
    exportExcel (listData) {
      let mapping = detailMapping
      mapping = dragFields('detailGrid_fullColumn', mapping, this)
      mapping = filterHiddenFields(mapping, 'mrp_detail_grid')

      const list = listData.map(item => {
        const {
          skuNo,
          factory,
          skuDesc,
          productGroup,
          productGroupName,
          mrpArea,
          mrpAreaDesc,
          position,
          purchaseGroup,
          mrpType,
          stockingStrategy,
          recordNo,
          recordItemNo,
          externalOrderNo,
          externalOrderItemNo,
          itemType,
          recordCreateDate,
          deliveryDate,
          originQuantity,
          demandQuantity,
          urgentMsg,
          specMsg,
          customerNo,
          customerName,
          productCustomerName,
          productCustomerManagerName,
          productSaleName,
          productSaleManagerName,
          evmOperatorName,
          createMark,
          ltBuffer,
          isCalculate,
          promotionInventoryQuantity,
          productVO: {
            inventoryTypeName,
            mstaeAllSwitch,
            productPositioningName
          },
          supplier1Detail,
          supplier2Detail,
          supplier3Detail,
          customerSpecifiedPurchasePriceVO,
          prType
        } = item
        const isVpi = !!prType
        const relSupplier1 = (isVpi || item.customerSpecified) ? customerSpecifiedPurchasePriceVO : supplier1Detail
        return {
          pendingOrderReason: (item.pendingReasonName || '') + ' ' + (item.detailPendingReasonDesc || '') + ' ' + (item.pendingReasonDesc || ''),
          skuNo,
          factory,
          skuDesc,
          productGroup,
          productGroupName,
          productPositioningName,
          mrpArea,
          mrpAreaDesc,
          position,
          warehouseLocationName: position ? (this.warehouseList.find(item => item.warehouseLocationCode === position) || {}).warehouseLocationName : '',
          purchaseGroup,
          userName: purchaseGroup ? (this.purchaseList.find(item => item.groupCode === purchaseGroup) || {}).userName : '',
          inventoryTypeName,
          mstaeAllSwitch: mstaeAllSwitch === 1 ? '是' : '否',
          mrpType: this.getMrpType(mrpType, stockingStrategy, isCalculate, createMark),
          recordNo: (this.sapOrder && mrpType === 'so') ? externalOrderNo : recordNo,
          recordItemNo: (this.sapOrder && mrpType === 'so') ? externalOrderItemNo : recordItemNo,
          recordCreateDate: recordCreateDate && recordCreateDate.slice(0, 10),
          deliveryDate: deliveryDate && deliveryDate.slice(0, 10),
          originQuantity: this.getOriginQuantity(item),
          demandQuantity,
          isUrgent: mrpType === 'so' && urgentMsg ? urgentMsg.split(' ')[1] : '',
          isSpec: mrpType === 'so' && specMsg ? specMsg.split(' ')[7].slice(0, -1) : '',
          ltBuffer,
          saleForecastQuantity: mrpType === 'so' && itemType === 'Z002' ? originQuantity : '',
          saleQuantity: mrpType === 'so' && itemType !== 'Z002' ? originQuantity : '',
          procurementQuantity: mrpType.slice(0, 2) === 'po' ? originQuantity : '',
          procurementApplyQuantity: mrpType === 'pr' ? originQuantity : '',
          productQuantity: mrpType.slice(0, 3) === 'pro' ? originQuantity : '',
          planQuantity: mrpType === 'wip' ? originQuantity : '',
          deliveryQuantity: mrpType === 'ido' || mrpType === 'dnP' || mrpType === 'dnD' ? originQuantity : '',
          inventoryQuantity: mrpType === 'stock' ? originQuantity : '',
          promotionInventoryQuantity,
          customerNo,
          customerName,
          productCustomerName,
          productCustomerManagerName,
          productSaleName,
          productSaleManagerName,
          evmOperatorName,
          purchasePriceVOList0: supplier1Detail.providerNo || '',
          purchasePrice0Name: supplier1Detail.providerName || '',
          purchasePrice0esokz: supplier1Detail.providerNo ? this.esokzName(supplier1Detail.esokz) : '',
          purchasePrice0Esokz: supplier1Detail.providerNo ? this.esokzName(supplier1Detail.esokz) : '',
          purchasePrice1Esokz: supplier2Detail.providerNo ? this.esokzName(supplier2Detail.esokz) : '',
          purchasePrice2Esokz: supplier3Detail.providerNo ? this.esokzName(supplier3Detail.esokz) : '',
          purchasePrice0currency: this.getCurrency(supplier1Detail.currency, supplier1Detail),
          purchasePrice0taxRate: supplier1Detail.providerNo ? supplier1Detail.taxRate + '%' : '',
          purchasePrice0meins: this.formatQuantityUnit(supplier1Detail.meins) || '',
          purchasePrice0purchaseMoq: supplier1Detail.purchaseMoq || '',
          purchasePrice0ifBid: supplier1Detail.providerNo ? (supplier1Detail.ifBid || '否') : '',
          purchasePrice0ifIncludeFreight: supplier1Detail.providerNo ? (supplier1Detail.ifIncludeFreight === 1 ? '是' : '否') : '',
          purchasePrice0freightRate: supplier1Detail.freightRate || '',
          purchasePrice0paymentTermsCodeName: supplier1Detail.paymentTermsCodeName || '',
          purchasePrice0ifUpInventory: supplier1Detail.providerNo ? (supplier1Detail.ifUpInventory || 0) : '',
          purchasePrice0supplierClassifyName: supplier1Detail.supplierClassifyName || '',
          purchasePrice0oemDirectSupplier: relSupplier1.providerNo ? (relSupplier1.oemDirectSupplier === 1 ? '是' : '否') : '',
          purchasePrice0idnlf: supplier1Detail.idnlf,
          purchasePrice0ifSignFrameworkAgreement: supplier1Detail.providerNo ? (supplier1Detail.ifSignFrameworkAgreement === 1 ? '是' : '否') : '',
          purchasePrice0Price: this.getPurchasePrice(supplier1Detail),
          purchasePrice0LeadTime: supplier1Detail.leadTime,
          purchasePrice0tacticsName: supplier1Detail.tacticsName,
          purchasePrice0ifAgency: supplier1Detail.ifAgency === 0 ? '否' : supplier1Detail.ifAgency === 1 ? '是' : '',
          purchasePrice0ifTradeCertificate: supplier1Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier1Detail.ifTradeCertificate) ? '是' : '',
          purchasePriceVOList1: supplier2Detail.providerNo || '',
          purchasePrice1Name: supplier2Detail.providerName || '',
          purchasePrice1ifUpInventory: supplier2Detail.providerNo ? (supplier2Detail.ifUpInventory || 0) : '',
          purchasePrice1ifIncludeFreight: supplier2Detail.providerNo ? (supplier2Detail.ifIncludeFreight === 1 ? '是' : '否') : '',
          purchasePrice1freightRate: supplier2Detail.freightRate || '',
          purchasePrice1supplierClassifyName: supplier2Detail.supplierClassifyName || '',
          purchasePrice1oemDirectSupplier: supplier2Detail.providerNo ? (supplier2Detail.oemDirectSupplier === 1 ? '是' : '否') : '',
          purchasePrice1idnlf: supplier2Detail.idnlf,
          purchasePrice1ifSignFrameworkAgreement: supplier2Detail.providerNo ? (supplier2Detail.ifSignFrameworkAgreement === 1 ? '是' : '否') : '',
          purchasePrice1Price: this.getPurchasePrice(supplier2Detail),
          purchasePrice1LeadTime: supplier2Detail.leadTime,
          purchasePrice1tacticsName: supplier2Detail.tacticsName,
          purchasePrice1ifAgency: supplier2Detail.ifAgency === 0 ? '否' : supplier2Detail.ifAgency === 1 ? '是' : '',
          purchasePrice1ifTradeCertificate: supplier2Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier2Detail.ifTradeCertificate) ? '是' : '',
          purchasePriceVOList2: supplier3Detail.providerNo || '',
          purchasePrice2Name: supplier3Detail.providerName || '',
          purchasePrice2ifUpInventory: supplier3Detail.providerNo ? (supplier3Detail.ifUpInventory || 0) : '',
          purchasePrice2ifIncludeFreight: supplier3Detail.providerNo ? (supplier3Detail.ifIncludeFreight === 1 ? '是' : '否') : '',
          purchasePrice2freightRate: supplier3Detail.freightRate || '',
          purchasePrice2supplierClassifyName: supplier3Detail.supplierClassifyName || '',
          purchasePrice2oemDirectSupplier: supplier3Detail.providerNo ? (supplier3Detail.oemDirectSupplier === 1 ? '是' : '否') : '',
          purchasePrice2idnlf: supplier3Detail.idnlf,
          purchasePrice2ifSignFrameworkAgreement: supplier3Detail.providerNo ? (supplier3Detail.ifSignFrameworkAgreement === 1 ? '是' : '否') : '',
          purchasePrice2Price: this.getPurchasePrice(supplier3Detail),
          purchasePrice2LeadTime: supplier3Detail.leadTime,
          purchasePrice2tacticsName: supplier3Detail.tacticsName,
          purchasePrice2ifAgency: supplier3Detail.ifAgency === 0 ? '否' : supplier3Detail.ifAgency === 1 ? '是' : '',
          purchasePrice2ifTradeCertificate: supplier3Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier3Detail.ifTradeCertificate) ? '是' : ''
        }
      });

      const allList = list.map(data => {
        Object.keys(data).forEach(key => {
          if (mapping[key]) {
            data[mapping[key]] = data[key];
          }
          delete data[key]
        })
        return data
      })

      writeFile(allList, `物料需求报表 – 明细 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: Object.values(mapping) })
    },
    getOriginQuantity (row) {
      if (row.mrpType === 'rop' && row.stockingStrategy === 2) {
        return row.originQuantity + '/' + row.roq
      }
      if (row.mrpType === 'rop' && row.stockingStrategy === 3) {
        return row.originQuantity + '/' + row.maxStock
      }
      if (row.mrpType === 'stock' && row.isCalculate === 0) {
        return ''
      }
      return row.originQuantity
    },
    columnDrop () {
      this.$nextTick(() => {
        const $table = this.$refs.detailGrid
        this.sortable2 = Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
          handle: '.vxe-header--column:not(.col--fixed)',
          onEnd: ({ item, newIndex, oldIndex }) => {
            const { fullColumn, tableColumn } = $table.getTableColumn()
            const targetThElem = item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[newIndex]
            if (newColumn.fixed) {
              // 错误的移动
              if (newIndex > oldIndex) {
                wrapperElem.insertBefore(targetThElem, wrapperElem.children[oldIndex])
              } else {
                wrapperElem.insertBefore(wrapperElem.children[oldIndex], targetThElem)
              }
              return this.$message.error('固定列不允许拖动！')
            }
            // 转换真实索引
            const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
            const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
            // 移动到目标列
            const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
            fullColumn.splice(newColumnIndex, 0, currRow)
            $table.loadColumn(fullColumn)
            localStorage.setItem('detailGrid_fullColumn', JSON.stringify(fullColumn))
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail {
  padding: 5px 20px;
}
.el-icon-arrow-down.alignRight {
  font-size: 25px;
}
.el-icon-arrow-up.alignRight {
  font-size: 25px;
}
.is-vpi{
  color: white;
  background-color: red;
  border-radius: 50%;
  margin-right: 5px;
  .product-vpi-icon {
    width: 10px;
    height: 10px;
    margin-left: 1px;
    padding-top: 2px;
  }
}
</style>

<style lang="scss">
.el-popover.popover {
  text-align: center;
}
.el-popover.align {
  text-align: left;
}
.highlight-row {
  background-color: #FCFDD3;
}
.grown-row {
  color: #bfbfbf;
}
</style>
