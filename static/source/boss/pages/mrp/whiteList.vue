<template>
  <div class="list-container">
    <el-form ref="ruleForm" :rules="formRules" :model="searchForm" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="公司" prop="companyCode" required>
            <el-select
              v-model="searchForm.companyCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
              placeholder="请选择公司"
            >
              <el-option
                v-for="item in companyFactoryList"
                :key="item.companyCode"
                :label="item.companyCode+' '+item.companyName"
                :value="item.companyCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工厂" prop="factoryCode" required>
            <el-select
              v-model="searchForm.factoryCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
              placeholder="请选择工厂"
            >
              <el-option
                v-for="item in searchForm.factoryList"
                :key="item.factoryCode"
                :label="item.factoryCode+' '+item.factoryName"
                :value="item.factoryCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="类型" prop="type">
            <el-select
              clearable
              filterable
              default-first-option
              v-model="searchForm.type"
              style="width:100%"
            >
              <el-option
                v-for="item in whiteListType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="MRP区域" prop="mrpArea">
            <el-select
              multiple
              collapse-tags
              clearable
              filterable
              default-first-option
              v-model="searchForm.mrpArea"
              placeholder="最多支持10个MRP区域按空格隔开搜索！"
              style="width:100%"
            >
              <el-option
                v-for="item in mrpAreaListFilterByFactory"
                :key="item.code+item.description"
                :label="item.code + ' ' +item.description"
                :value="item.code"
              />
            </el-select>
            <!-- <MrpCode
              multiple
              clearable
              placeholder="最多支持10个MRP区域按空格隔开搜索！"
              :data.sync="searchForm.mrpAreaObj"
              @change="changeMrpCode('mrpAreaObj', $event)"
              :describe="true"
            /> -->
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商" prop="providerNo">
            <SelectSupplier
              clearable
              :data.sync="searchForm.supplier"
              @change="handleChange('supplier', $event)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购员" prop="buyerCode">
            <el-select
              v-model="searchForm.buyerCode"
              filterable
              default-first-option
              clearable
              multiple
              collapse-tags
              style="width:100%"
              placeholder="请选择采购员"
            >
              <el-option
                v-for="item in purchaseList"
                :key="item.groupCode+item.userName"
                :label="item.groupCode+ ' '+item.userName"
                :value="item.groupCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="品牌" prop="brands">
            <SelectBrand clearable
                         style="width:100%"
                         :data.sync="searchForm.brands"
                         :multiple="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="4" :offset="22">
          <el-button
            type="primary"
            :style="{width: '80px',marginLeft: '40px'}"
            :loading="searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <vxe-grid
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="whiteGrid"
      height="688"
      id="white_grid"
      row-id="id"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
      :checkbox-config="{reserve: true}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
      highlight-hover-row
    >
      <template v-slot:toolbar_buttons>
        <el-button v-if="show" type="danger" size="mini" plain @click="removeCheckboxRow" :disabled="disabled">批量删除</el-button>
        <el-button type="primary" size="mini" @click="handleDownExcel" :disabled="disabling">全部下载</el-button>
      </template>

      <template v-slot:toolbar_tools>
        <el-button v-if="show" type="primary" size="mini" @click="addWhite">新建名单</el-button>
        <el-button v-if="show" type="primary" size="mini" plain @click="handleDownloadTemplateHeader">下载模板</el-button>
        <el-upload
          v-if="show"
          ref="upload"
          style="display:inline-block;margin-left:10px"
          accept=".xlsx"
          :before-upload="$validateFileType"
          action="/api-mrp/whiteListProvider"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="handleUpload"
          :show-file-list="false"
        >
          <el-button type="primary" size="mini" plain class="btn">模板上传</el-button>
        </el-upload>
      </template>

      <template v-slot:companyCode_default="{ row }">
        {{ row.companyCode + ' ' + (companyFactoryList.find(item => item.companyCode === row.companyCode) || {}).companyName }}
      </template>

      <template v-slot:factoryCode_default="{ row }">
        {{ row.factoryCode + ' ' + row.factoryName }}
      </template>

      <template v-slot:providerNo_default="{ row }">
        {{ row.providerNo + ' ' + row.providerName }}
      </template>

      <template v-slot:pmode_default="{ row }">
        {{ row.pmode === 0 ? '标准' : '寄售' }}
      </template>

      <template v-slot:buyerName_default="{ row }">
        {{ row.buyerCode + ' ' + row.buyerName }}
      </template>
      <template v-slot:type_default="{ row }">
        {{ getType(row.type) }}
      </template>
      <template v-slot:mrpArea_default="{ row }">
        {{ row.mrpArea ? row.mrpArea + ' ' + row.mrpAreaDesc : '' }}
      </template>
      <template v-slot:brandName_default="{ row }">
        {{ brandName(row.brandList) }}
      </template>

      <template v-slot:data_default="{ row }">
        {{ row.saveDate.slice(0, 10) }}
      </template>

      <template #pager>
        <vxe-pager
          :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
          :page-sizes="[100,500,1000]"
          :border="true"
          :current-page.sync="tablePage.currentPage"
          :page-size.sync="tablePage.pageSize"
          :total="tablePage.total"
          @page-change="handlePageChange">
        </vxe-pager>
      </template>
    </vxe-grid>

    <el-dialog
      title="创建白名单"
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="handleClose"
    >
      <el-form ref="dialogForm" :model="dialogForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="150px" :hide-required-asterisk="false">
        <el-form-item label="公司" prop="companyCode" required>
          <el-select
            v-model="dialogForm.companyCode"
            filterable
            clearable
            style="width:100%"
            placeholder="请选择公司"
          >
            <el-option
              v-for="item in companyFactoryList"
              :key="item.companyCode"
              :label="item.companyCode+' '+item.companyName"
              :value="item.companyCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工厂" prop="factoryCode" required>
          <el-select
            v-model="dialogForm.factoryCode"
            filterable
            clearable
            style="width:100%"
            placeholder="请选择工厂"
          >
            <el-option
              v-for="item in dialogForm.factoryList"
              :key="item.factoryCode"
              :label="item.factoryCode+' '+item.factoryName"
              :value="item.factoryCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="供应商" prop="providerNo">
          <SelectSupplier
            clearable
            :data.sync="dialogForm.supplier"
            @change="handleChange2('supplier', $event)"
          />
        </el-form-item>
        <el-form-item label="默认运营路线" prop="pmode" required>
          <el-select
            v-model.number="dialogForm.pmode"
            filterable
            clearable
            style="width:100%"
            placeholder="请选择默认运营路线"
          >
            <el-option :value="0" label="标准采购"></el-option>
            <el-option :value="2" label="寄售采购"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采购员" prop="buyerCode" required>
          <el-select
            v-model="dialogForm.buyerCode"
            filterable
            clearable
            style="width:100%"
            placeholder="请选择采购员"
          >
            <el-option
              v-for="item in purchaseList"
              :key="item.groupCode+item.userName"
              :label="item.groupCode+ ' '+item.userName"
              :value="item.groupCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="品牌" prop="brands">
          <SelectBrand clearable
                       style="width:100%"
                       :data.sync="dialogForm.brands"
                       :multiple="true"
                       @change="handleChange('brands', $event)" />
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select
            clearable
            filterable
            default-first-option
            v-model="dialogForm.type"
            style="width:100%"
          >
            <el-option
              v-for="item in whiteListType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="MRP区域" prop="mrpArea">
          <el-select
            :disabled="dialogForm.type==='OMS'"
            clearable
            filterable
            v-model="dialogForm.mrpArea"
            placeholder="MRP区域编码"
            style="width:100%"
          >
            <el-option
              v-for="item in mrpAreaListFilterByFactory1"
              :key="item.code+item.description"
              :label="item.code + ' ' +item.description"
              :value="item.code"
            />
          </el-select>
          <!-- <MrpCode
            clearable
            :data.sync="dialogForm.mrpAreaObj"
            @change="changeMrpCode2('mrpAreaObj', $event)"
            :describe="true"
          /> -->
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="insert">保 存</el-button>
        <el-button type="info" @click="cancel">取 消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
// import MrpCode from './components/mrpCode'
import { getWhiteList, provideWhite, deleteWhite, exportAll, batchUploadWhite, getUserInfo } from '@/api/mrp'
import { flattenDeep } from 'lodash'
import { writeFile } from '@boss/excel'
import { excelUrls } from './constants'
import SelectBrand from '@/pages/mrp/components/selectBrand'
import { safeRun } from '@/utils/index'
import moment from 'moment'

const whiteListType = [{
  value: null,
  label: '全部'
}, {
  value: 'MRP',
  label: '汇总'
}, {
  value: 'OMS',
  label: '直发'
}]
const columns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'companyCode',
    title: '公司',
    slots: {
      default: 'companyCode_default'
    },
    minWidth: 200
  },
  {
    field: 'factoryCode',
    title: '工厂',
    slots: {
      default: 'factoryCode_default'
    },
    minWidth: 200
  },
  {
    field: 'providerNo',
    title: '供应商',
    minWidth: 80,
    slots: {
      default: 'providerNo_default'
    }
  },
  {
    field: 'pmode',
    title: '默认运营路线',
    minWidth: 120,
    slots: {
      default: 'pmode_default'
    }
  },
  {
    field: 'buyerName',
    title: '采购员',
    minWidth: 120,
    slots: {
      default: 'buyerName_default'
    }
  },
  {
    field: 'type',
    title: '类型',
    minWidth: 80,
    slots: {
      default: 'type_default'
    }
  },
  {
    field: 'mrpArea',
    title: 'MRP区域',
    minWidth: 120,
    slots: {
      default: 'mrpArea_default'
    }
  },
  {
    field: 'brandName',
    title: '品牌',
    minWidth: 120,
    slots: {
      default: 'brandName_default'
    }
  },
  {
    field: 'saveDate',
    title: '保存日期',
    slots: {
      default: 'data_default'
    },
    minWidth: 80
  }
]
export default {
  name: 'mrpWhiteList',
  components: {
    SelectSupplier,
    SelectBrand
    // MrpCode
  },
  data() {
    return {
      searchForm: {
        factoryList: [],
        companyCode: '',
        factoryCode: '',
        mrpArea: '',
        // mrpAreaObj: [],
        providerNo: '',
        supplier: {},
        buyerCode: [],
        type: null
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons',
          tools: 'toolbar_tools'
        }
      },
      columns,
      whiteListType,
      tableData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 100
      },
      dialogVisible: false,
      dialogForm: {
        factoryList: [],
        companyCode: '',
        factoryCode: '',
        providerNo: '',
        supplier: {},
        pmode: '',
        buyerCode: '',
        mrpArea: '',
        brands: [],
        type: null
      },
      formRules: {
        companyCode: [
          { required: true, message: '公司不能为空！', trigger: ['change', 'blur'] }
        ],
        factoryCode: [
          { required: true, message: '工厂不能为空！', trigger: ['change', 'blur'] }
        ]
      },
      rules: {
        companyCode: [
          { required: true, message: '公司不能为空！', trigger: ['change', 'blur'] }
        ],
        factoryCode: [
          { required: true, message: '工厂不能为空！', trigger: ['change', 'blur'] }
        ],
        providerNo: [
          { required: true, message: '供应商不能为空！', trigger: ['change', 'blur'] }
        ],
        pmode: [
          { required: true, message: '默认运营路线不能为空！', trigger: ['change', 'blur'] }
        ],
        buyerCode: [
          { required: true, message: '采购员不能为空！', trigger: ['change', 'blur'] }
        ]
      },
      selectList: [],
      fileList: [],
      file: '',
      disabled: true,
      disabling: true,
      mrpAreaListFilterByFactory: [],
      mrpAreaListFilterByFactory1: [],
      show: false
    }
  },
  async created() {
    const pList = []
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (this.purchaseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    if (this.companyInfoList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryCompanyInfoList'))
    }
    if (!this.mrpAreaList || this.mrpAreaList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryMrpAreaList'))
    }
    await Promise.all(pList)
    await this.getUserCompany()
  },
  computed: {
    ...mapState({
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      purchaseList: state => state.orderPurchase.purchaseList,
      companyInfoList: state => state.mrp.companyInfoList,
      mrpAreaList: state => state.mrp.mrpAreaList
    })
  },
  watch: {
    'searchForm.companyCode': function (newValue) {
      this.searchForm.factoryList = (this.companyFactoryList.find(item => item.companyCode === newValue) || {}).factoryList || []
      this.searchForm.factoryCode = ''
    },
    'dialogForm.companyCode': function (newValue) {
      this.dialogForm.factoryList = (this.companyFactoryList.find(item => item.companyCode === newValue) || {}).factoryList || []
      this.dialogForm.factoryCode = ''
    },
    'searchForm.factoryList': {
      handler: function (newValue) {
        if (newValue.length === 1) {
          this.searchForm.factoryCode = this.searchForm.factoryList[0].factoryCode
          this.mrpAreaListFilterByFactory = this.mrpAreaList.filter(item => item.factory === this.searchForm.factoryList[0].factoryCode)
        }
      },
      deep: true,
      immediate: true
    },
    'searchForm.factoryCode': function (val) {
      this.searchForm.mrpArea = ''
      this.mrpAreaListFilterByFactory = this.mrpAreaList.filter(item => item.factory === val)
    },
    'dialogForm.factoryCode': function (val) {
      this.dialogForm.mrpArea = ''
      this.mrpAreaListFilterByFactory1 = this.mrpAreaList.filter(item => item.factory === val)
    },
    'dialogForm.type': function (val) {
      if (val === 'OMS') {
        this.dialogForm.mrpArea = ''
      }
    }
  },
  methods: {
    getType(type) {
      const filter = this.whiteListType.filter(item => item.value === type)
      return filter?.[0]?.label || '全部'
    },
    brandName(brandList) {
      return brandList.map(brand => {
        return brand.brandId + ' ' + brand.brandName
      }).join(' ')
    },
    async getUserCompany() {
      try {
        const res = await getUserInfo({
          username: window.CUR_DATA.user && window.CUR_DATA.user.name
        })
        let defaultCompany = (this.companyInfoList.find(item => item.subCompanyId === res.subCompany) || {}).code
        if (defaultCompany) {
          this.searchForm.companyCode = defaultCompany
        }
        const roleInfo = res.roleInfoList.find(item => item.name === 'MRP-采购经理' || item.name === 'MRP-采购总监' || item.name === 'MRP-管理员')
        if (roleInfo) {
          this.show = true
        }
      } catch (error) {
        console.log(error)
      }
    },
    handleChange(type, event) {
      if (type === 'supplier') {
        this.searchForm.providerNo = event.supplierNo
      }
    },
    handleChange2(type, event) {
      if (type === 'supplier') {
        this.dialogForm.providerNo = event.supplierNo
      }
    },
    changeMrpCode(type, event) {
      if (type === 'mrpAreaObj') {
        this.searchForm.mrpArea = event.join(' ')
      }
    },
    changeMrpCode2(type, event) {
      if (type === 'mrpAreaObj') {
        this.dialogForm.mrpArea = event
      }
    },
    handleSearch() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.tablePage.currentPage = 1
          this.getWhiteListData()
        } else {
          return false
        }
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.getWhiteListData()
        } else {
          return false
        }
      })
    },
    formatParams(params) {
      let form = { ...params }
      // form.mrpArea = safeRun(() =>
      //   form.mrpArea
      //     .split(/\s/).filter((e) => e)
      // );
      form.brandIds = form.brands.map(item => item.brandId).join(',')
      delete form.factoryList
      delete form.brands
      // delete form.mrpAreaObj
      delete form.supplier
      return form
    },
    validate(params) {
      let ret = true
      safeRun(() => {
        if (params.mrpArea.length > 10) {
          ret = false
          this.$message.error('最多支持10个MRP区域按空格隔开搜索！')
        }
      })
      return ret
    },
    async getWhiteListData() {
      try {
        let params = this.formatParams(this.searchForm)
        if (!this.validate(params)) return
        if (Array.isArray(params.mrpArea)) {
          params.mrpArea = params.mrpArea.join(' ')
        }
        if (Array.isArray(params.buyerCode)) {
          params.buyerCode = params.buyerCode.join(' ')
        }
        params = {
          ...params,
          pageNo: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await getWhiteList(params)
        this.tableData = res.records
        this.tablePage.total = res.total
        if (this.tableData.length === 0) {
          this.$message.info('没有符合条件的白名单')
          this.disabling = true
        } else {
          this.disabling = false
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error)
      }
    },
    addWhite() {
      this.dialogVisible = true
    },
    insert() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          this.creatWhite()
        } else {
          return false
        }
      })
    },
    async creatWhite() {
      try {
        const data = {
          ...this.dialogForm,
          companyName: (this.companyFactoryList.find(item => item.companyCode === this.dialogForm.companyCode) || {}).companyName,
          factoryName: (this.dialogForm.factoryList.find(item => item.factoryCode === this.dialogForm.factoryCode) || {}).factoryName,
          providerName: this.dialogForm.supplier.supplierName,
          buyerName: (this.purchaseList.find(item => item.groupCode === this.dialogForm.buyerCode) || {}).userName,
          brandIds: this.dialogForm.brands.map(item => item.brandId).join(',')
        }
        delete data.brands
        const res = await provideWhite(data)
        if (res.code === 200) {
          this.$refs.dialogForm.resetFields()
          this.dialogForm.supplier = {}
          delete this.dialogForm.mrpAreaObj
          this.dialogVisible = false
          this.$message.success(res.msg)
        } else {
          this.$message({
            type: 'error',
            message: res.msg,
            dangerouslyUseHTMLString: true,
            customClass: 'mzindex'
          })
        }
      } catch (error) {
        console.log(error)
        this.dialogVisible = false
      }
    },
    cancel() {
      this.$refs.dialogForm.resetFields()
      this.dialogForm.supplier = {}
      delete this.dialogForm.mrpAreaObj
      this.dialogVisible = false
    },
    handleClose() {
      this.$refs.dialogForm.resetFields()
      this.dialogForm.supplier = {}
      delete this.dialogForm.mrpAreaObj
      this.dialogVisible = false
    },
    selectChange({ checked, records, row }) {
      if (checked) {
        this.selectList.push(row)
      } else {
        this.selectList = this.selectList.filter(item => item.id !== row.id)
      }
      this.disable()
    },
    selectAll({ checked, reserves, records }) {
      if (checked) {
        this.selectList = [...reserves, ...records]
      } else {
        this.selectList = reserves
      }
      this.disable()
    },
    disable() {
      if (this.selectList.length > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    removeCheckboxRow() {
      this.$confirm('您确定要删除该数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteCheckbox()
      }).catch(() => {

      })
    },
    async deleteCheckbox() {
      try {
        let data = flattenDeep(this.selectList.map(item => item.ids))
        const res = await deleteWhite(data)
        if (res.code === 200) {
          this.$message.success(res.data)
          this.selectList = []
          this.disable()
          this.getWhiteListData()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        console.log(error)
      }
    },
    handleDownExcel() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = this.formatParams(this.searchForm)
          if (!this.validate(params)) return
          if (Array.isArray(params.mrpArea)) {
            params.mrpArea = params.mrpArea.join(' ')
          }
          if (Array.isArray(params.buyerCode)) {
            params.buyerCode = params.buyerCode.join(' ')
          }
          this.loading = this.$loading({
            background: 'rgba(0, 0, 0, 0.5)'
          })
          params = {
            ...params,
            // buyerName: (this.purchaseList.find(item => item.groupCode === this.searchForm.buyerCode) || {}).userName,
            providerName: this.searchForm.supplier.supplierName
          }
          exportAll(params).then((data) => {
            this.exportExcel(data)
            this.loading.close()
          }).catch((error) => {
            console.log(error)
            this.loading.close()
          })
        } else {
          return false
        }
      })
    },
    exportExcel(listData) {
      const mapping = {
        companyCode: '公司',
        factoryCode: '工厂',
        providerNo: '供应商',
        pmode: '默认运营路线',
        buyerCode: '采购员',
        type: '类型',
        mrpArea: 'MRP区域',
        brandName: '品牌',
        saveDate: '保存日期'
      }

      const list = listData.map(item => {
        const {
          buyerCode,
          buyerName,
          companyCode,
          companyName,
          factoryCode,
          factoryName,
          providerNo,
          providerName,
          type,
          mrpArea,
          pmode,
          saveDate,
          brandList
        } = item
        return {
          buyerCode: buyerCode + ' ' + buyerName,
          companyCode: companyCode + ' ' + companyName,
          factoryCode: factoryCode + ' ' + factoryName,
          providerNo: providerNo + ' ' + providerName,
          type: this.getType(type),
          mrpArea,
          pmode: pmode === 0 ? '标准' : '寄售',
          brandName: this.brandName(brandList),
          saveDate: saveDate.slice(0, 10)
        }
      })

      const allList = list.map(data => {
        Object.keys(data).forEach(key => {
          if (mapping[key]) {
            data[mapping[key]] = data[key]
            delete data[key]
          }
        })
        return data
      })

      writeFile(allList, `白名单清单 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: ['公司', '工厂', '供应商', '默认运营路线', '采购员', '类型', 'MRP区域', '品牌', '保存日期'] })
    },
    handleDownloadTemplateHeader() {
      window.open(excelUrls.whiteImport)
    },
    handleUpload(file, fileList) {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      this.fileList = [fileList[fileList.length - 1]] // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file.raw
      let reader = new FileReader()
      reader.readAsArrayBuffer(this.file)
      reader.onload = async () => {
        let buffer = reader.result
        let bytes = new Uint8Array(buffer)
        let length = bytes.byteLength
        let binary = ''
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        let XLSX = require('xlsx')
        let wb = XLSX.read(binary, {
          type: 'binary'
        })
        let outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        outdata.splice(0, 1)
        if (outdata.length === 0) {
          this.$message({
            message: '导入数据为空！',
            type: 'error',
            dangerouslyUseHTMLString: true,
            showClose: true,
            duration: 10000
          })
          this.loading.close()
          return
        }
        // type为OMS (直发)忽略区域字段
        outdata.forEach((item) => {
          if (item?.type === 'OMS') {
            item.mrpArea = null
          }
        })
        try {
          const res = await batchUploadWhite(outdata)
          if (res.code === 200) {
            this.$message.success(res.msg)
          } else {
            if (res.msg) {
              res.msg = res.msg.replace(/\n/g, '<br/>')
            }
            this.$message({
              message: res.msg,
              type: 'error',
              dangerouslyUseHTMLString: true,
              showClose: true,
              duration: 10000
            })
          }
          this.loading.close()
        } catch (error) {
          this.loading.close()
          console.log(error)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .list-container {
    padding: 20px;
  }

  .dialog-footer {
    display: flex;
    justify-content: center;
  }

  .mzindex {
    z-index: 9000 !important;
  }
</style>
