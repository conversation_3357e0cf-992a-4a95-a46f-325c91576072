<template>
  <div class="list-container">
    <DividerHeader>
      查询条件
      <i :class="['alignRight', hideResult ? 'el-icon-arrow-down' : 'el-icon-arrow-up']" style="float: right; margin: 13px 10px 0 0;" @click="fold"></i>
    </DividerHeader>
    <DirectForm ref="ruleForm"
                :hideResult="hideResult"
                :purchaseList="purchaseList"
                :searchLoading="searchLoading"
                :userRole="userRole"
                :initHistoryMounted="initHistoryMounted"
                @getReportType="getReportType"
                @filterPurchaseList="filterPurchaseList"
                @handleSearch="handleSearch"
                :soParams="soParams" />
    <div
      v-if="!reportType"
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
    >
      <DividerHeader>
        操作信息
      </DividerHeader>
      <vxe-grid column-key
                border
                resizable
                keep-source
                show-overflow
                show-header-overflow
                ref="directGrid"
                :height="tableHeight"
                id="mrp_direct_grid"
                size="mini"
                row-id="id"
                align="center"
                :custom-config="tableCustom"
                :data="listData"
                :columns="columns"
                :toolbar-config="tableToolbar"
                :edit-config="{trigger: 'click', mode: 'cell', showIcon: false, showStatus: true}"
                :checkbox-config="{checkMethod:checkMethod}"
                @checkbox-all="selectAll"
                @checkbox-change="selectChange"
                @cell-dblclick="cellClick"
                highlight-hover-row
                :scroll-y="{gt: 20}"
                @scroll="scroll"
      >
        <template v-slot:toolbar_buttons>
          <el-form :model="operationForm"
                  ref="operationForm"
                  :rules="validateRules"
                  label-suffix=":"
                  label-width="80px">
            <el-row :gutter="20">
              <el-col :span="2">
                <el-select v-model="operationForm.filters"
                          filterable
                          default-first-option
                          @change="filerCheckbox"
                          style="width: 100px">
                  <el-option :value="0" label="全部"></el-option>
                  <el-option :value="1" label="正常需求"></el-option>
                  <el-option :value="2" label="异常需求"></el-option>
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-form-item label="供应商"
                              prop="supplier"
                              style="margin: 0;">
                  <SelectSupplier clearable
                                  :data.sync="operationForm.supplier"
                                  @change="handleChange('supplier', $event)" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-button type="primary"
                          style="width: 100px"
                          :loading="batchLoading"
                          @click="handleBatch">
                  批量分配
                </el-button>
                <el-button style="width: 100px"
                          @click="autoBatch">
                  自动分配
                </el-button>
              </el-col>
              <el-col :span="6">
                <el-form-item label="PO类型"
                              prop="itemType"
                              style="margin: 0;">
                  <el-select v-model="operationForm.itemType"
                            filterable
                            default-first-option
                            style="width:100%"
                            @change="changeType">
                    <el-option v-for="item in directOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>

        <template v-slot:toolbar_tools>
          <el-button
            v-if="canTransfer"
            type="primary"
            style="width: 80px"
            @click="transferPO"
            :disabled="disabled">
            生成PO
          </el-button>
          <el-button type="primary"
                    style="width: 80px; margin-right: 10px;"
                    :disabled="disabling"
                    @click="handleDownExcel"
                    v-if="show">
            下 载
          </el-button>
        </template>

        <template v-slot:poNo_default="{ row }">
          <el-link style="margin-right: 5px" v-for="(item, index) in (row.poNo || '').split(',').filter(Boolean)" :key="index" @click="toDetail(item)"
            type="primary">{{ item }}
          </el-link>
        </template>
        <template v-slot:skuNo_default="{ row }">
          <span class="is-vpi" v-if="row.prType === 1">
            <img src="../../assets/images/v.png" class="product-vpi-icon " />
          </span>
          <span class="is-vpi" v-if="row.prType === 2" style="padding-left: 2px">
            指
          </span>
          <span class="is-vpi" v-if="row.prTag.isUrgent === 1" style="padding-left: 2px">急</span>
          <span class="is-vpi" v-if="row.prTag.isSpecialOrder === 1" style="padding-left: 2px">专</span>
          <span>{{ row.skuNo }}</span>
        </template>

        <template v-slot:purchaseGroup_default="{ row }">
          {{ row.purchaseGroup + ' ' + (purchaseList.find(item => item.groupCode === row.purchaseGroup) || {}).userName }}
        </template>

        <template v-slot:productGroup_default="{ row }">
          {{ (row.productVO.productGroup || '') + ' ' + (row.productVO.productGroupName || '') }}
        </template>

        <template v-slot:productPositioningName_default="{ row }">
          {{ row.productVO.productPositioningName }}
        </template>

        <template v-slot:quantityUnit_default="{ row }">
          {{ formatQuantityUnit(row.quantityUnit) }}
        </template>

        <template v-slot:customerSpecified_default="{ row }">
          <el-checkbox v-model="row.soVoucherVo.customerSpecified"
                      :checked="row.soVoucherVo.customerSpecified === 1"
                      :true-label="1"
                      :false-label="0"
                      disabled>
          </el-checkbox>
        </template>

        <template v-slot:directWareHouse_default="{ row }">
          {{ row.directWareHouse ? row.directWareHouse + ' ' + row.directWareHouseDesc : '' }}
        </template>

        <template v-slot:selfInventory1_default="{ row }">
          {{
            getNameAddQuantity(
              row.inventoryVo.selfInventory1,
              row.inventoryVo.selfInventory1Quantity
            )
          }}
        </template>

        <template v-slot:selfInventory2_default="{ row }">
          {{
            getNameAddQuantity(
              row.inventoryVo.selfInventory2,
              row.inventoryVo.selfInventory2Quantity
            )
          }}
        </template>
        <template v-slot:otherInventory1_default="{ row }">
          {{
            row.inventoryVo.otherInventory1Quantity > 0
              ? row.inventoryVo.otherInventory1Quantity +
              '/' +
              row.inventoryVo.otherInventory1 +
              '/' +
              row.inventoryVo.otherInventory1Name
              : ''
          }}
        </template>
        <template v-slot:otherTransportationInventory1_default="{ row }">
          {{
            row.inventoryVo.otherTransportationInventory1Quantity > 0
              ? row.inventoryVo.otherTransportationInventory1Quantity +
              '/' +
              row.inventoryVo.otherTransportationInventory1 +
              '/' +
              row.inventoryVo.otherTransportationInventory1Name
              : ''
          }}
        </template>
        <template v-slot:hignStorageInStock1MrpArea_default="{ row }">
          {{
            row.inventoryVo.hignStorageInStock1Quantity > 0
              ? row.inventoryVo.hignStorageInStock1Quantity +
              '/' +
              row.inventoryVo.hignStorageInStock1MrpArea +
              '/' +
              row.inventoryVo.hignStorageInStock1MrpAreaName
              : ''
          }}
        </template>

        <template v-slot:hignStorageInStock2MrpArea_default="{ row }">
          {{
            row.inventoryVo.hignStorageInStock2Quantity > 0
              ? row.inventoryVo.hignStorageInStock2Quantity +
              '/' +
              row.inventoryVo.hignStorageInStock2MrpArea +
              '/' +
              row.inventoryVo.hignStorageInStock2MrpAreaName
              : ''
          }}
        </template>

        <template v-slot:supplier_default="{ row }">
          <div v-if="row.soVoucherVo.customerSpecified === 1"> {{ row.supplier }}</div>
          <SelectSkuSupplier
            v-else
            :data.sync="row.supplier"
            @change="handleChange2(row,'supplier', $event)"
            :disabled="!!row.prType"
            :factory="row.factory"
            :skuNo="row.skuNo"
          />
        </template>
        <template v-slot:replaceFirstSupplierReason_default="{ row }">
          <!-- <el-input
            v-model="row.replaceFirstSupplierReason"
            style="width: 100%"
            :disabled="row.supplier && row.supplier.split(' ')[0] && row.supplier1Detail.providerNo && row.supplier.split(' ')[0] === row.supplier1Detail.providerNo"
            maxlength="200"
            >
          </el-input> -->
          <el-select
            v-model="row.replaceFirstSupplierReason"
            :disabled="row.supplier && row.supplier.split(' ')[0] && row.supplier1Detail.providerNo && row.supplier.split(' ')[0] === row.supplier1Detail.providerNo"
            size="mini"
            filterable
            clearable
            :class="{ 'orderReason-select': row.replaceFirstSupplierReason=== '其他' }"
          >
            <el-option
              v-for="item in reasonOptions"
              :key="item.value"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
          <el-input
            v-if="row.replaceFirstSupplierReason === '其他'"
            v-model="row.newReplaceFirstSupplierReason"
            size="mini"
            style="width: 80px"
            :maxlength="200"
            :disabled="row.supplier && row.supplier.split(' ')[0] && row.supplier1Detail.providerNo && row.supplier.split(' ')[0] === row.supplier1Detail.providerNo"
          />
        </template>
        <template v-slot:isFree_default="{ row }">
          <el-checkbox v-model="row.isFree"
                      :true-label="1"
                      :false-label="0"
                      :checked="row.isFree===1"
                      @change="handleFree(row)"
                      :disabled="row.soVoucherVo.customerSpecified===1"
          >
          </el-checkbox>
        </template>
        <template v-slot:pendingOrderReason_default="{ row }">
          <span>
            {{reasonType(row)}}
            {{reasonTypeDetail(row)}}
            {{row.pendingReasonDesc}}
          </span>
        </template>
        <template v-slot:unitPrice_default="{ row }">
          <div v-if="row.soVoucherVo.customerSpecified===1 || isVpi(row)"> {{ row.supplierDetail.unitPrice }}</div>
          <el-input-number
            v-else
            v-model="row.unitPrice"
            @change="changeUnitPriceFn(row)"
            style="width: 100%"
          >
          </el-input-number>
        </template>

        <template v-slot:meinsName_default="{ row }">
          <span v-if="row.soVoucherVo.customerSpecified===1 || isVpi(row)">{{ row.customerSpecifiedPurchasePriceVO ? row.customerSpecifiedPurchasePriceVO.meinsName : '' }}</span>
          <span v-else>{{ row.supplierDetail.meinsName }}</span>
        </template>
        <template v-slot:customInstructions_default="{ row }">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
          >
            <div v-html="row.customInstructions"></div>
            <span slot="reference">{{ handleCustomInstructions(row.customInstructions) }}</span>
          </el-popover>
        </template>
        <template v-slot:currency_default="{ row }">
          {{ getCurrency(row.supplierDetail.currency, row.supplierDetail) }}
        </template>

        <template v-slot:taxRate_default="{ row }">
          <div v-if="row.soVoucherVo.customerSpecified===1 || isVpi(row)"> {{ row.supplierDetail.taxRate ? row.supplierDetail.taxRate + '%' : '' }}</div>
          <el-select
            v-else
            v-model="row.taxRate"
            filterable
            @change="getRate(row)"
          >
            <el-option
              v-for="item in dictList.purchaseTaxRate"
              :key="item.id"
              :label="item.code +' '+ item.name"
              :value="item.parentCode"
            >
            </el-option>
          </el-select>
        </template>

        <template v-slot:externalOrderNo_default="{ row }">
          <el-tooltip v-if="row.isPreOrder && row.preOrderCheckTime" effect="light" :content="row.preOrderCheckTime && row.preOrderCheckTime.slice(0,10)" placement="top">
            <el-button size="mini" circle type="danger" style="border: none;color: white;padding: 2px;">预</el-button>
          </el-tooltip>
          <el-button v-else-if="row.isPreOrder" size="mini" circle type="danger" style="border: none;color: white;padding: 2px;">预</el-button>
          {{ row.externalOrderNo }}
        </template>

        <template v-slot:soCreateDate_default="{ row }">
          {{ row.soVoucherVo.soCreateDate && row.soVoucherVo.soCreateDate.slice(0, 10) }}
        </template>
        <template v-slot:deliveryDate_default="{ row }">
          {{ row.soDeliveryDate && row.soDeliveryDate.slice(0, 10) }}
        </template>
        <template v-slot:isUrgent_default="{ row }">
          {{ row.isUrgent === 1 ? '有' : '' }}
        </template>

        <template v-slot:customerNo_default="{ row }">
          {{ row.soVoucherVo.customerNo ? row.soVoucherVo.customerNo + ' ' + row.soVoucherVo.customerName : '' }}
        </template>

        <template v-slot:receiverName_default="{ row }">
          {{ row.soVoucherVo.hasDeliveryManager === '1' ? row.soVoucherVo.deliveryManagerName : row.soVoucherVo.receiverName }}
        </template>

        <template v-slot:receiverPhone_default="{ row }">
          {{ row.soVoucherVo.hasDeliveryManager === '1' ? row.soVoucherVo.deliveryManagerPhone : row.soVoucherVo.receiverPhone }}
        </template>

        <template v-slot:receiverAddress_default="{ row }">
          {{
            (row.soVoucherVo.receiverProvince || '') + (row.soVoucherVo.receiverCity || '') + (row.soVoucherVo.receiverDistrict || '') + (row.soVoucherVo.receiverTown || '') + (row.soVoucherVo.receiverAddress || '')
          }}
        </template>

        <template v-slot:purchasePriceVOList0="{ row }">
          <el-popover v-if="isVpi(row) ? row.customerSpecifiedPurchasePriceVO.providerNo : row.supplier1Detail.providerNo"
                      placement="top"
                      width="800"
                      trigger="hover"
                      popper-class="align">
            <el-row>
              <el-col :span="8">
                综合得分：{{ row.supplier1Detail.totalScore }}
              </el-col>
              <el-col :span="4">
                执行建议：{{ getGrade(row.supplier1Detail.grade) }}
              </el-col>
              <el-col :span="6" style="display: flex">
                风险提示：
                <el-tooltip class="item" effect="dark" :content="row.supplier1Detail.riskWarn" placement="top-start">
                  <span style="width: 100px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{ row.supplier1Detail.riskWarn }}</span>
                </el-tooltip>
              </el-col>
              <el-col :span="6">
                策略类型：{{ row.supplier1Detail.tacticsName }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24" style="display: flex">
                策略详情：
                <el-tooltip class="item" effect="dark" :content="row.supplier1Detail.tacticsDetail" placement="top-start">
                  <span style="width: 400px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{ row.supplier1Detail.tacticsDetail }}</span>
                </el-tooltip>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                是否代理品牌：{{ row.supplier1Detail.ifAgency === 0 ? '否' : row.supplier1Detail.ifAgency === 1 ? '是' : '' }}
              </el-col>
              <el-col :span="8" style="display: flex">
                是否原厂品牌：
                {{ row.supplier1Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(row.supplier1Detail.ifTradeCertificate) ? '是' : '' }}
              </el-col>
              <el-col :span="8" style="display: flex">
                MPQ：
                {{ row.supplier1Detail.purchaseMpq || '' }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                默认运营路线：{{ esokzName(row.supplier1Detail.esokz) }}
              </el-col>
              <el-col :span="4">
                币种：{{ getCurrency(row.supplier1Detail.currency, row.supplierDetail) }}
              </el-col>
              <el-col :span="6">
                进项税：{{ isVpi(row) ? row.customerSpecifiedPurchasePriceVO.taxRate : row.supplier1Detail.taxRate }} {{ '%' || '' }}
              </el-col>
              <el-col :span="6">
                订单单位：{{ formatQuantityUnit(row.supplier1Detail.meins) }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                最小起订单：{{ row.supplier1Detail.purchaseMoq || '' }}
              </el-col>
              <el-col :span="4">
                招投标：{{ getIfBid(row.supplier1Detail.ifBid) }}
              </el-col>
              <el-col :span="6">
                含运费：{{ row.supplier1Detail.ifIncludeFreight === 1 ? '是' : '否' }}
              </el-col>
              <el-col :span="6">
                单件运费：{{ row.supplier1Detail.freightRate || '' }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                付款条款：{{ row.supplier1Detail.paymentTermsCodeName || '' }}
              </el-col>
              <el-col :span="4">
                对接库存：{{ row.supplier1Detail.ifUpInventory || 0 }}
              </el-col>
              <el-col :span="6">
                供应商分类: {{ row.supplier1Detail.supplierClassifyName || '' }}
              </el-col>
              <el-col :span="6">
                年框: {{ row.supplier1Detail.ifSignFrameworkAgreement === 1 ? '是' : '否' }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                供应商物料号：{{ row.supplier1Detail.idnlf }}
              </el-col>
              <el-col :span="4">
                OEM直发: {{ row.supplier1Detail.oemDirectSupplier === 1 ? '是' : '否' }}
              </el-col>
            </el-row>
            <span slot="reference">
              <span @click="handleCopy(isVpi(row) ? row.customerSpecifiedPurchasePriceVO.providerNo : row.supplier1Detail.providerNo,$event)"
                    style="display:inline-block;cursor:pointer;margin:0 5px;"
                    title="点击复制供应商编码">
                <i class="el-icon-document-copy"></i>
              </span>
              <span :style="{color: '#409eff'}">
              {{
                  isVpi(row) ? row.customerSpecifiedPurchasePriceVO.providerNo + ' ' + row.customerSpecifiedPurchasePriceVO.providerName : row.supplier1Detail.providerNo + ' ' + row.supplier1Detail.providerName
                }}
              </span>
            </span>
          </el-popover>
        </template>

        <template v-slot:purchasePrice0Price_default="{ row }">
          {{ getPurchasePrice(isVpi(row) ? row.customerSpecifiedPurchasePriceVO : row.supplier1Detail) }}
        </template>

        <template v-slot:purchasePriceVOList1="{ row }">
          <el-popover v-if="row.supplier2Detail.providerNo"
                      placement="top"
                      width="600"
                      trigger="hover">
            <el-row :gutter="20">
              <el-row>
                <el-col :span="8">
                  综合得分：{{ row.supplier2Detail.totalScore }}
                </el-col>
                <el-col :span="8">
                  执行建议：{{ getGrade(row.supplier2Detail.grade) }}
                </el-col>
                <el-col :span="8" style="display: flex">
                  风险提示：
                  <el-tooltip class="item" effect="dark" :content="row.supplier2Detail.riskWarn" placement="top-start">
                    <span style="width: 100px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{ row.supplier2Detail.riskWarn }}</span>
                  </el-tooltip>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  策略类型：{{ row.supplier2Detail.tacticsName }}
                </el-col>
                <el-col :span="16" style="display: flex">
                  策略详情：
                  <el-tooltip class="item" effect="dark" :content="row.supplier2Detail.tacticsDetail" placement="top-start">
                    <span style="width: 200px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{ row.supplier2Detail.tacticsDetail }}</span>
                  </el-tooltip>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  是否代理品牌：{{ row.supplier2Detail.ifAgency === 0 ? '否' : row.supplier2Detail.ifAgency === 1 ? '是' : '' }}
                </el-col>
                <el-col :span="8" style="display: flex">
                  是否原厂品牌：
                  {{ row.supplier2Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(row.supplier2Detail.ifTradeCertificate) ? '是' : '' }}
                </el-col>
                <el-col :span="8" style="display: flex">
                  MPQ：
                  {{ row.supplier2Detail.purchaseMpq || '' }}
                </el-col>
              </el-row>
              <el-col :span="8">
                对接库存：{{ row.supplier2Detail.ifUpInventory || 0 }}
              </el-col>
              <el-col :span="8">
                含运费：{{ row.supplier2Detail.ifIncludeFreight === 1 ? '是' : '否' }}
              </el-col>
              <el-col :span="8">
                单件运费：{{ row.supplier2Detail.freightRate || '' }}
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                供应商分类: {{ row.supplier2Detail.supplierClassifyName || '' }}
              </el-col>
              <el-col :span="8">
                年框: {{ row.supplier2Detail.ifSignFrameworkAgreement === 1 ? '是' : '否' }}
              </el-col>
              <el-col :span="8">
                OEM直发: {{ row.supplier2Detail.oemDirectSupplier === 1 ? '是' : '否' }}
              </el-col>
              <el-col :span="8">
                供应商物料号：{{ row.supplier2Detail.idnlf }}
              </el-col>
            </el-row>
            <span slot="reference">
              <span @click="handleCopy(row.supplier2Detail.providerNo,$event)"
                    style="display:inline-block;cursor:pointer;margin:0 5px;"
                    title="点击复制供应商编码">
                <i class="el-icon-document-copy"></i>
              </span>
              <span :style="{color: '#409eff'}">
                {{ row.supplier2Detail.providerNo + ' ' + row.supplier2Detail.providerName }}
              </span>
            </span>
          </el-popover>
        </template>

        <template v-slot:purchasePrice1Price_default="{ row }">
          {{ getPurchasePrice(row.supplier2Detail) }}
        </template>

        <template v-slot:purchasePriceVOList2="{ row }">
          <el-popover v-if="row.supplier3Detail.providerNo"
                      placement="top"
                      width="600"
                      trigger="hover">
            <el-row :gutter="20">
              <el-row>
                <el-col :span="8">
                  综合得分：{{ row.supplier3Detail.totalScore }}
                </el-col>
                <el-col :span="8">
                  执行建议：{{ getGrade(row.supplier3Detail.grade) }}
                </el-col>
                <el-col :span="8" style="display: flex">
                  风险提示：
                  <el-tooltip class="item" effect="dark" :content="row.supplier3Detail.riskWarn" placement="top-start">
                    <span style="width: 100px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{ row.supplier3Detail.riskWarn }}</span>
                  </el-tooltip>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  策略类型：{{ row.supplier3Detail.tacticsName }}
                </el-col>
                <el-col :span="16" style="display: flex">
                  策略详情：
                  <el-tooltip class="item" effect="dark" :content="row.supplier3Detail.tacticsDetail" placement="top-start">
                    <span style="width: 200px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;;">{{ row.supplier3Detail.tacticsDetail }}</span>
                  </el-tooltip>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  是否代理品牌：{{ row.supplier3Detail.ifAgency === 0 ? '否' : row.supplier3Detail.ifAgency === 1 ? '是' : '' }}
                </el-col>
                <el-col :span="8" style="display: flex">
                  是否原厂品牌：
                  {{ row.supplier3Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(row.supplier3Detail.ifTradeCertificate) ? '是' : '' }}
                </el-col>
                <el-col :span="8" style="display: flex">
                  MPQ：
                  {{ row.supplier3Detail.purchaseMpq || '' }}
                </el-col>
              </el-row>
              <el-col :span="8">
                对接库存：{{ row.supplier3Detail.ifUpInventory || 0 }}
              </el-col>
              <el-col :span="8">
                含运费：{{ row.supplier3Detail.ifIncludeFreight === 1 ? '是' : '否' }}
              </el-col>
              <el-col :span="8">
                单件运费：{{ row.supplier3Detail.freightRate || '' }}
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                供应商分类: {{ row.supplier3Detail.supplierClassifyName || '' }}
              </el-col>
              <el-col :span="8">
                年框: {{ row.supplier3Detail.ifSignFrameworkAgreement === 1 ? '是' : '否' }}
              </el-col>
              <el-col :span="8">
                OEM直发: {{ row.supplier3Detail.oemDirectSupplier === 1 ? '是' : '否' }}
              </el-col>
              <el-col :span="8">
                供应商物料号：{{ row.supplier3Detail.idnlf }}
              </el-col>
            </el-row>
            <el-button slot="reference"
                      type="text"
                      :style="{border: 'none'}">
              {{ row.supplier3Detail.providerNo + ' ' + row.supplier3Detail.providerName }}
            </el-button>
          </el-popover>
        </template>

        <template v-slot:purchasePrice2Price_default="{ row }">
          {{ getPurchasePrice(row.supplier3Detail) }}
        </template>

        <template v-slot:hasDeliveryManager_default="{ row }">
          {{ row.soVoucherVo.hasDeliveryManager === '1' || row.soVoucherVo.hasDeliveryManager === '2' ? '是' : '否' }}
        </template>

        <template v-slot:signingBack_default="{ row }">
          {{ formatSigningBack(row.soVoucherVo.signingBack) }}
        </template>

        <template v-slot:mrpPrintRemark_default="{ row }">
          <el-tooltip v-if="row.mrpPrintRemark" class="item" effect="dark" :content="row.mrpPrintRemark" placement="top">
            <i class="el-icon-edit-outline" style="color: red;" @click="editRow(row)"></i>
          </el-tooltip>
          <i v-else class="el-icon-edit" @click="editRow(row)"></i>
        </template>

        <template v-slot:poItemHistoryPrice_default="{ row }">
          {{ row.poItemHistoryPrice || '-' }}
        </template>

        <template v-slot:recommendSupplier_default="{ row }">
          <div v-html="recommendSupplierStr(row.recommendSupplierList)"></div>
        </template>

        <template #pager>
          <vxe-pager :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
                    :page-sizes="[100,500,1000,2000]"
                    :border="true"
                    :current-page.sync="tablePage.currentPage"
                    :page-size.sync="tablePage.pageSize"
                    :total="tablePage.total"
                    @page-change="handlePageChange">
          </vxe-pager>
        </template>
      </vxe-grid>
    </div>

    <div
      v-else
      v-loading="tableLoading"
      element-loading-text="加载中，请不要刷新浏览器"
    >
      <vxe-grid
        column-key
        border
        resizable
        auto-resize
        keep-source
        show-overflow
        show-header-overflow
        ref="avariableGrid"
        :height="tableHeight"
        id="mrp-avariable_grid"
        size="mini"
        row-id="id"
        align="center"
        :custom-config="tableCustom"
        :data="aStockListData"
        :columns="columns"
        :toolbar-config="tableToolbar"
        :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
        @cell-dblclick="cellClick"
        highlight-hover-row
        :scroll-y="{ gt: 20 }"
        @scroll="scroll"
      >
        <template v-slot:toolbar_tools>
          <el-button
            type="primary"
            style="width: 80px; margin-right: 10px"
            :disabled="aStockListData.length === 0"
            @click="handleDownExcel"
          >
            下载
          </el-button>
        </template>
        <template v-slot:mrpArea_default="{ row }">
          {{ row.mrpArea + ' ' + row.mrpAreaDesc }}
        </template>
        <template v-slot:purchaseGroup_default="{ row }">
          {{
            row.purchaseGroup +
            ' ' +
            (
              purchaseList.find(
                (item) => item.groupCode === row.purchaseGroup
              ) || {}
            ).userName
          }}
        </template>
        <template v-slot:stockingStrategy_default="{ row }">
          {{ getStockingStrategyText(row) }}
        </template>
        <template v-slot:quantityUnit_default="{ row }">
          {{ formatQuantityUnit(row.quantityUnit) }}
        </template>
        <template #pager>
          <vxe-pager
            :layouts="[
              'Sizes',
              'PrevJump',
              'PrevPage',
              'Number',
              'NextPage',
              'NextJump',
              'FullJump',
              'Total'
            ]"
            :page-sizes="[100, 500, 1000, 2000]"
            :border="true"
            :current-page.sync="aTablePage.currentPage"
            :page-size.sync="aTablePage.pageSize"
            :total="aTablePage.total"
            @page-change="handlePageChange"
          >
          </vxe-pager>
        </template>
      </vxe-grid>
    </div>

    <el-dialog
      title="添加备注"
      :visible.sync="centerDialogVisible"
      width="30%"
      :before-close="handleClose"
      center>
      <el-input
        type="textarea"
        placeholder="请输入备注内容"
        v-model="mrpPrintRemark"
        maxlength="100"
        show-word-limit
      >
      </el-input>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addMrpPrintRemark">确 定</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getPrDirectList, getSupplier, transfer2Po, getDictionary, getSupplierName, queryLadderPrice, queryGrossMargin, getAvailableStockList } from '@/api/mrp'
import { getDictionaryList } from '@/api/purchaseList.js'
import { safeRun, throttle, initVersion } from '@/utils/index'
import { writeFile } from '@boss/excel'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import DividerHeader from '@/components/DividerHeader'
import { directColumns, directOptions, directMapping, availableStockColumns, availableStockDownloadColumns, stockingStrategy, supplierCertList } from './constants'
import {
  formatDirectPrListData, esokzName, getCurrency, getGrade, getIfBid, setPower, getPurchasePrice, fold, handleCopy, parsePurchaseGroup, changeUnitPrice,
  filterHiddenFields, cellClick, recommendSupplierStr, dragFields, getSplitOrderTips
} from './utils'
import { cloneDeep, pick, isEmpty, isEqual } from 'lodash'
import moment from 'moment'
import Sortable from 'sortablejs'
import SelectSkuSupplier from './components/selectSkuSupplier.vue'
import DirectForm from './components/direct/form.vue'
import {
  getReasonTypeOneList,
  getReasonTypeTwoList
} from '@/filters/index.js'
export default {
  name: 'mrpDirect',
  components: { SelectSupplier, DividerHeader, SelectSkuSupplier, DirectForm },
  data() {
    return {
      searchForm: {},
      lastSearchForm: {}, // 记录上次搜索条件
      clearListData: false, // 是否清除之前的列表数据
      operationForm: {
        supplier: {},
        filters: 1,
        itemType: 'Z001',
        supplierNo: ''
      },
      searchLoading: false,
      batchLoading: false,
      tableLoading: false,
      reasonOptions: [],
      mrpSupplierName: '',
      pendingReasonOptions: [],
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        zoom: true,
        slots: {
          buttons: 'toolbar_buttons',
          tools: 'toolbar_tools'
        }
      },
      columns: directColumns,
      allList: [],
      listData: [],
      aStockListData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 500
      },
      aTablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 500
      },
      disabled: true,
      loading: null,
      selectList: [],
      directOptions,
      validateRules: {
        itemType: [
          { required: true, message: 'PO类型不能为空！', trigger: ['change', 'blur'] }
        ]
      },
      is_extending: true,
      disabling: true,
      hideResult: true,
      showResult: true,
      tableHeight: 480,
      filtPurchaseList: [],
      show: false,
      centerDialogVisible: false,
      mrpPrintRemark: '',
      row: {},
      reportType: false,
      soParams: {} // 紧急工单跳转过来的传参
    }
  },
  async created() {
    const pList = []
    if (!Object.keys(this.dictList).length) {
      pList.push(this.$store.dispatch('orderCommon/queryDictList'))
    }
    if (this.purchaseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (!this.userRole || this.userRole.length === 0) {
      pList.push(this.$store.dispatch('getUserRole'))
    }
    await Promise.all(pList)
    getDictionary({ type: 'orderReason' }).then(data => {
      this.reasonOptions = data.data.filter(item => item.description === '申请下单原因')
    })
    getDictionaryList({ includeKey: 'pendingReason' }).then(data => {
      this.pendingReasonOptions = data
    })
    if (this.userRole.find(item => item === 'MRP-采购经理' || item === 'MRP-采购总监' || item === 'MRP-管理员' || item === 'MRP-采购员' || item === 'MRP-EVM运营')) {
      this.show = true
    }
    this.columnDrop()
  },
  beforeMount() {
    initVersion({ newVersion: '20240117', versionKey: 'mrpDirectVersionKey', columKey: 'directGrid_fullColumn' })
    // const fullcolumns = JSON.parse(localStorage.getItem('directGrid_fullColumn'))
    // if (fullcolumns) {
    //   this.columns = fullcolumns.map(item => {
    //     item.field = item.property
    //     return item
    //   })
    // }
    this.initHistoryMounted()
  },
  mounted() {
    this.$nextTick(function () {
      this.selfAdaption()
      window.addEventListener('resize', this.selfAdaption, false)
    })
    this.soParams = JSON.parse(localStorage.getItem('/mrp/direct'))
  },
  // beforeRouteEnter(to, from, next) {
  //   next(vm => {
  //     vm.soParams = to?.query
  //   })
  // },
  beforeDestroy() {
    if (this.sortable2) {
      this.sortable2.destroy()
    }
    window.removeEventListener('resize', this.selfAdaption, false)
  },
  computed: {
    ...mapState({
      dictList: state => state.orderCommon.dictList || {},
      purchaseList: state => state.orderPurchase.purchaseList,
      warehouseList: state => state.orderPurchase.warehouseList,
      userRole: state => state.userRole
    }),
    canTransfer() {
      return !!~this.userRole.indexOf('MRP-采购员') || !!~this.userRole.indexOf('MRP-采购经理') || !!~this.userRole.indexOf('MRP-采购总监') || !!~this.userRole.indexOf('MRP-采购值班账号') || !!~this.userRole.indexOf('data-采购员') || !!~this.userRole.indexOf('data-采购经理') || !!~this.userRole.indexOf('data-采购总监')
    },
    tableKeyValue() {
      let obj = {}
      availableStockDownloadColumns.forEach((item) => {
        obj[item.field] = item.title
      })
      return obj
    },
    tableFields() {
      let arr = []
      availableStockDownloadColumns.forEach((item) => {
        arr.push(item.field)
      })
      return arr
    },
    getStockingStrategyText() {
      return (row) => {
        if (!row.stockingStrategy) return ''
        if (row.stockingStrategy) {
          return (
            row.stockingStrategy +
            '-' +
            (
              stockingStrategy.find(
                (item) => item.value === row.stockingStrategy
              ) || { label: '' }
            ).label
          )
        }
      }
    }
  },
  methods: {
    getIfBid,
    handleCopy,
    getPurchasePrice,
    setPower,
    getCurrency,
    esokzName,
    changeUnitPrice,
    cellClick,
    getGrade,
    handleShowSupplierName(supplierNo) {
      if (supplierNo) {
        getSupplierName(supplierNo).then(res => {
          if (res?.code === 0) {
            this.mrpSupplierName = res.data.supplierName
          } else {
            this.mrpSupplierName = ''
          }
        })
      } else {
        this.mrpSupplierName = ''
      }
    },
    recommendSupplierStr,
    initHistoryMounted() {
      // debugger
      let fullcolumns
      if (!this.reportType && localStorage.getItem('directGrid_fullColumn')) {
        fullcolumns = JSON.parse(localStorage.getItem('directGrid_fullColumn'))
      }
      if (this.reportType && localStorage.getItem('avariableGrid_fullColumn')) {
        fullcolumns = JSON.parse(
          localStorage.getItem('avariableGrid_fullColumn')
        )
      }
      if (fullcolumns) {
        this.columns = fullcolumns.map((item) => {
          item.field = item.property
          return item
        })
      } else {
        if (!this.reportType) {
          this.columns = directColumns
        } else {
          this.columns = availableStockColumns
        }
      }
    },
    handleCustomInstructions(customInstructions) {
      return customInstructions ? customInstructions.split('<br>').join('') : ''
    },
    isVpi(item) {
      return !!item.prType
    },
    selfAdaption: throttle(function selfAdaption() {
      if (!this.reportType) {
        this.tableHeight =
          window.innerHeight -
          this.$refs.directGrid.$el.offsetTop -
          130
      } else {
        this.tableHeight =
          window.innerHeight - this.$refs.avariableGrid.$el.offsetTop - 130
      }
      if (this.tableHeight < 260) {
        this.tableHeight = 260
      }
    }, 800),
    filerCheckbox(val) {
      if (val === 2) {
        this.listData = this.allList.filter(item => item.poNo || (item.isPreOrder && !item.preOrderCheckTime))
      }
      if (val === 1) {
        this.listData = this.allList.filter(item => !(item.poNo || (item.isPreOrder && !item.preOrderCheckTime)))
      }
      if (val === 0) {
        this.listData = [...this.allList]
      }
    },
    filterPurchaseList(list) {
      this.filtPurchaseList = list
    },
    fold() {
      if (!this.reportType) {
        fold(
          this,
          this.$refs.directGrid,
          'hideResult',
          this.hideResult
        )
      } else {
        fold(this, this.$refs.avariableGrid, 'hideResult', this.hideResult)
      }
    },
    formatQuantityUnit(value) {
      if (value && this.dictList) {
        const unit = this.dictList['quantityUnit'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    formatSigningBack(value) {
      if (value && this.dictList) {
        const unit = this.dictList['signingBack'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    reasonType (row) {
      let list = getReasonTypeOneList(this.pendingReasonOptions) || []
      const find = list.find(item => Number(item.value) === Number(row.pendingReason))
      return (find && find.label) || ''
    },
    reasonTypeDetail (row) {
      let list = getReasonTypeTwoList(this.pendingReasonOptions, row.pendingReason) || []
      const find = list.find(item => Number(item.value) === Number(row.detailPendingReason))
      return (find && find.label) || ''
    },
    handleSearch(data) {
      this.searchForm = data
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (!this.reportType) {
            this.operationForm.filters = 1
            this.tablePage.currentPage = 1
            this.$refs.operationForm.resetFields()
            this.operationForm.supplier = {}
          } else {
            this.aTablePage.currentPage = 1
          }
          this.selectList = []
          this.getPrListData()
        } else {
          return false
        }
      })
    },
    handlePageChange({ currentPage, pageSize }) {
      // 没有点击查询按钮时，直接点击分页获取查询条件
      this.searchForm = this.$refs.ruleForm.searchForm
      if (!this.reportType) {
        this.tablePage.currentPage = currentPage
        this.tablePage.pageSize = pageSize
      } else {
        this.aTablePage.currentPage = currentPage
        this.aTablePage.pageSize = pageSize
      }

      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.getPrListData()
        } else {
          return false
        }
      })
    },
    formatParams(params) {
      let form = { ...params }
      form.skuNo = safeRun(() =>
        form.skuNo
          .split(/\s/).filter((e) => e)
      )
      form.soNo = safeRun(() =>
        form.soNo
          .split(/\s/).filter((e) => e)
      )
      delete form.createTime
      delete form.brands
      return form
    },
    validate(params) {
      let ret = true
      safeRun(() => {
        if (params.skuNo.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
        if (params.productGroup.length > 10) {
          ret = false
          this.$message.error('最多支持10个物料组按空格隔开搜索！')
        }
        if (params.soNo.length > 100) {
          ret = false
          this.$message.error('最多支持100个销售订单按空格隔开搜索！')
        }
      })
      return ret
    },
    async getPrListData() {
      try {
        if (this.searchForm.createTime === null) {
          this.searchForm.createTime = []
        }
        let params = this.formatParams(this.searchForm)
        if (!this.validate(params)) return

        parsePurchaseGroup(params, setPower(this.userRole), this.filtPurchaseList)

        if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
        }
        if (Array.isArray(params.productGroup)) {
          params.productGroup = params.productGroup.join(' ')
        }
        if (Array.isArray(params.soNo)) {
          params.soNo = params.soNo.join(' ')
        }
        if (Array.isArray(params.purchaseGroup)) {
          params.purchaseGroup = params.purchaseGroup.join(' ')
        }

        if (!params.purchaseGroup) {
          return this.$message.error('没有匹配的负责人')
        }

        this.tableLoading = true
        this.searchLoading = true
        let res
        // 负需求
        if (!this.reportType) {
          params = {
            ...params,
            action: 'query',
            pageNo: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
            startDate: this.searchForm.createTime[0],
            endDate: this.searchForm.createTime[1]
          }

          res = await getPrDirectList(params)
          this.allList = res.records.map(item => {
            return formatDirectPrListData(item)
          })
          this.filerCheckbox(this.operationForm.filters)

          this.tablePage.total = res.total

          if (this.listData.length === 0) {
            this.$message.info('无符合条件的物料需求')
            this.disabling = true
          } else {
            this.disabling = false
          }
        } else { // 可用库存
          params = {
            ...params,
            pageNo: this.aTablePage.currentPage,
            pageSize: this.aTablePage.pageSize,
            startDate: this.searchForm.createTime[0],
            endDate: this.searchForm.createTime[1]
          }
          res = await getAvailableStockList(params)
          this.aStockListData = res.records

          this.aTablePage.total = res.total

          if (this.aStockListData.length === 0) {
            this.$message.info('无符合条件的物料需求')
            this.disabling = true
          } else {
            this.disabling = false
          }
        }

        if (isEmpty(this.lastSearchForm)) {
          this.lastSearchForm = { ...this.searchForm }
        } else if (!isEqual(this.searchForm, this.lastSearchForm)) {
          // 搜索成功后判断搜索条件是否改变，改变则标识切换负需求/可用库存后需要清空之前的data
          this.lastSearchForm = { ...this.searchForm }
          this.clearListData = true
        }

        // const res = await getPrDirectList(params)
        // this.allList = res.records.map(item => {
        //   return formatDirectPrListData(item)
        // })
        this.tableLoading = false

        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error)
        this.is_extending = false
      }
    },
    async getExtendPrListData(page) {
      try {
        if (this.searchForm.createTime === null) {
          this.searchForm.createTime = []
        }
        let params = this.formatParams(this.searchForm)
        if (!this.validate(params)) return

        parsePurchaseGroup(params, setPower(this.userRole), this.filtPurchaseList)

        if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
        }
        if (Array.isArray(params.productGroup)) {
          params.productGroup = params.productGroup.join(' ')
        }
        if (Array.isArray(params.soNo)) {
          params.soNo = params.soNo.join(' ')
        }
        if (Array.isArray(params.purchaseGroup)) {
          params.purchaseGroup = params.purchaseGroup.join(' ')
        }

        this.tableLoading = true
        this.searchLoading = true
        if (!this.reportType) {
          this.tablePage.currentPage++
          params = {
            ...params,
            pageNo: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
            startDate: this.searchForm.createTime[0],
            endDate: this.searchForm.createTime[1]
          }

          const res = await getPrDirectList(params)
          const list = res.records.map((item) => {
            return formatDirectPrListData(item)
          })
          this.allList = this.allList.concat(list)
          this.filerCheckbox(this.operationForm.filters)
        } else {
          this.aTablePage.currentPage++
          params = {
            ...params,
            pageNo: this.aTablePage.currentPage,
            pageSize: this.aTablePage.pageSize,
            startDate: this.searchForm.createTime[0],
            endDate: this.searchForm.createTime[1]
          }

          const res = await getAvailableStockList(params)
          this.aStockListData = this.aStockListData.concat(res.records)
        }

        // const res = await getPrDirectList(params)
        // const list = res.records.map(item => {
        //   return formatDirectPrListData(item)
        // })
        // this.allList = this.allList.concat(list)
        // this.filerCheckbox(this.operationForm.filters)
        this.is_extending = true
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error)
        this.is_extending = false
      }
    },
    scroll({ scrollTop }) {
      let compareHeight
      if (
        (!this.reportType &&
          this.$refs.directGrid.isMaximized()) ||
        (this.reportType && this.$refs.avariableGrid.isMaximized())
      ) {
        compareHeight = this.tablePage.pageSize * this.tablePage.currentPage * 36 - 805
      } else {
        // 是否折叠时显示的grid高度不同
        compareHeight = this.tablePage.pageSize * this.tablePage.currentPage * 36 - this.tableHeight + 120
      }
      if (scrollTop >= compareHeight) {
        if (this.is_extending) {
          this.is_extending = false
          this.getExtendPrListData()
        }
      }
    },
    selectChange({ checked, records, row }) {
      if (checked) {
        this.selectList.push(row)
      } else {
        this.selectList = this.selectList.filter(item => item.id !== row.id)
      }
      this.disable()
    },
    selectAll({ checked, reserves, records }) {
      if (checked) {
        this.selectList = records
      } else {
        this.selectList = []
      }
      this.disable()
    },
    disable() {
      if (this.selectList.length > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    handleChange(type, event) {
      if (type === 'supplier') {
        this.operationForm.supplierNo = event.supplierNo
      }
    },
    handleChange2(row, type, event) {
      if (type === 'supplier') {
        row.supplier = event.providerNo + ' ' + event.providerName
      }
      this.changeSupplier(row)
    },
    handleFree(row) {
      if (row.isFree) {
        row.unitPrice = 0
        row.taxRate = '0'
      } else {
        row.unitPrice = getPurchasePrice(row.supplierDetail)
        row.taxRate = row.supplierDetail.taxRate
      }
    },
    getMsg(row, data) {
      let msg = ''
      if (row?.quantity && data?.purchaseMoq && row.quantity < data.purchaseMoq) {
        msg += '申请数量小于采购MOQ、'
      }
      if (row?.saleMoq && data?.purchaseMoq && data.purchaseMoq > row.saleMoq) {
        msg += '采购MOQ大于销售MOQ、'
      } else if (!row?.saleMoq || !data?.purchaseMoq) {
        msg += `未查询到${!row?.saleMoq ? '销售' : '采购'}MOQ、`
      } else {
        msg += '、'
      }
      return msg.substr(0, msg.length - 1)
    },
    async changeSupplier(row) {
      const val = await this.getLadderPrice([{
        providerNo: row.supplier.split(' ')[0],
        skuNo: row.skuNo,
        factory: row.factory,
        quantity: row.quantity
      }])
      if (val?.[0]?.providerNo) {
        const meins = (row.soVoucherVo.customerSpecified === 1 || this.isVpi(row)) ? (row.supplier ? row.quantityUnit : '') : val?.[0]?.meins
        if (row?.quantityUnit !== meins) {
          row.msg = '采购、基本单位不一致'
        } else {
          row.msg = this.getMsg(row, val[0])
        }
        this.$set(row, 'supplierDetail', { ...val?.[0] })
        row.unitPrice = getPurchasePrice(row.supplierDetail)
        row.taxRate = row.supplierDetail.taxRate
        this.getRate(row);
      }
    },
    async handleBatch() {
      if (!this.operationForm.supplierNo) {
        return this.$message.error('供应商文本框未输入')
      }
      if (this.selectList.length === 0) {
        return this.$message.error('未选择要修改的采购订单')
      }
      this.batchLoading = true
      this.selectList = await this.clone(cloneDeep(this.selectList))
      this.listData = this.listData.map(item => {
        let idxValue = this.selectList.find(value => value.id === item.id && item.soVoucherVo.customerSpecified === 0)
        if (idxValue) {
          item = idxValue
        }
        return item
      })
      this.$refs.directGrid.setCheckboxRow(this.selectList, true)
      this.batchLoading = false
    },
    async clone(itemList) {
      const supplierNo = this.operationForm.supplier.supplierNo
      const supplierName = this.operationForm.supplier.supplierName
      const data = itemList.map(item => ({
        providerNo: supplierNo,
        skuNo: item.skuNo,
        factory: item.factory,
        quantity: item.quantity
      }))
      const res = await this.getLadderPrice(data)
      if (res?.length) {
        itemList.forEach((item, idx) => {
          if (item.soVoucherVo.customerSpecified === 0 && !item.prType) {
            const meins = (item.soVoucherVo.customerSpecified === 1 || this.isVpi(item)) ? (item.supplier ? item.quantityUnit : '') : res[idx]?.meins
            if (item?.quantityUnit !== meins) {
              item.msg = '采购、基本单位不一致'
            } else {
              item.msg = this.getMsg(item, res[idx])
            }
            this.$set(item, 'supplierDetail', { ...res[idx] })
            item.supplier = supplierNo + ' ' + supplierName
            item.unitPrice = this.getPurchasePrice(item.supplierDetail)
            item.taxRate = item.supplierDetail.taxRate
            item.isFree = 0
          }
        })
      }
      return itemList
      // let idxValue = this.selectList.find(value => value.id === item.id && item.soVoucherVo.customerSpecified === 0 && !value.prType)
      // if (idxValue) {
      //   const supplierNo = this.operationForm.supplier.supplierNo
      //   item.supplier = supplierNo + ' ' + this.operationForm.supplier.supplierName
      //   const val = await this.getSupplierDetail({
      //     providerNo: supplierNo,
      //     skuNo: item.skuNo,
      //     factory: item.factory
      //   })
      //   this.$set(item, 'supplierDetail', { ...val })
      //   item.unitPrice = item.supplierDetail.unitPrice
      //   item.taxRate = item.supplierDetail.taxRate
      //   item.isFree = 0
      // }
      // return item
    },
    async getLadderPrice(params) {
      try {
        const res = await queryLadderPrice(params)
        if (res.code === 200) {
          return res.data
        } else {
          return {}
        }
      } catch (error) {
        console.log(error)
      }
    },
    async getSupplierDetail(params) {
      try {
        if (!params.providerNo) {
          console.log('params', params)
          this.$message.error('请重新选择供应商')
          return
        }
        const res = await getSupplier(params)
        if (res.code === 200) {
          return res.data
        } else {
          return {}
        }
      } catch (error) {
        console.log(error)
      }
    },
    autoBatch() {
      this.selectList = this.selectList.map(item => {
        if (item?.prType) {
          return item
        }
        if (item && item.supplier1Detail && item.supplier1Detail.providerNo && item.soVoucherVo.customerSpecified === 0) {
          const { providerNo, providerName, taxRate } = item.supplier1Detail
          item.supplierDetail = item.supplier1Detail
          item.supplier = `${providerNo} ${providerName}`
          item.unitPrice = getPurchasePrice(item.supplier1Detail)
          item.taxRate = taxRate
          if (item?.quantityUnit !== item.supplierDetail.meins) {
            item.msg = '采购、基本单位不一致'
          } else {
            item.msg = this.getMsg(item, item)
          }
        } else if (item.soVoucherVo.customerSpecified === 0) {
          item.supplierDetail = {}
          item.supplier = ''
          item.unitPrice = undefined
          item.taxRate = ''
        }
        return item
      })
      this.listData = this.listData.map(item => {
        let idxValue = this.selectList.find(value => value.id === item.id && item.soVoucherVo.customerSpecified === 0)
        if (idxValue) {
          item = idxValue
        }
        return item
      })
    },
    changeType() {
      this.listData.map((val) => {
        val.itemType = this.operationForm.itemType
        return val
      })
    },
    transferPO() {
      const hasLowGrossMargin = this.selectList.some(item => item.ifGrossMargin)
      if (hasLowGrossMargin) {
        this.$confirm('当前选中的负需求，存在低负毛利，请确认后进行转单！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.confirmTransferPO()
        })
      } else {
        this.confirmTransferPO()
      }
    },
    confirmTransferPO() {
      this.$refs.operationForm.validate(async (valid) => {
        if (valid) {
          this.selectList = this.selectList.filter(item => item.poNo === null || item.poNo.length === 0)
          try {
            let supplierNoAndName = {}
            this.selectList.forEach(item => {
              if (item.supplier) {
                const supplierNo = item.supplier.split(' ')[0]
                supplierNoAndName[supplierNo] = item.supplier
              }
            })
            // 非空校验
            this.selectList = this.selectList.map(item => {
              if (item.itemType !== 'Z003' && !(item.supplier && typeof item.unitPrice === 'number' && item.taxRate)) {
                item.msg = '分配供应商、采购价格、进项税不能为空'
              } else {
                item.msg = ''
              }
              return item
            })
            this.listData = this.listData.map(value => {
              let idx = this.selectList.findIndex(val => val.id === value.id)
              if (idx !== -1) {
                value.msg = this.selectList[idx].msg
              }
              return value
            })
            if (this.selectList.find(val => val.msg === '分配供应商、采购价格、进项税不能为空')) return

            this.loading = this.$loading({
              background: 'rgba(0, 0, 0, 0.5)'
            })
            this.selectList = this.selectList.map(item => {
              item.unitPrice = item.unitPrice ? Number(item.unitPrice) : item.unitPrice
              item.taxRate = (this.dictList.purchaseTaxRate.find(val => val.parentCode === item.taxRate) || {}).code
              item.currency = item.supplierDetail.currency
              item.supplier = item.supplier ? item.supplier.split(' ')[0] : null
              const { meins, meinsName } = item.supplierDetail
              if (meins && meinsName && item.soVoucherVo.customerSpecified === 0) {
                item.purchaseUnitCode = item.supplierDetail.meins
                item.purchaseUnitName = item.supplierDetail.meinsName
              } else if (item.quantityUnit) {
                item.purchaseUnitCode = item.quantityUnit
                item.purchaseUnitName = this.formatQuantityUnit(item.quantityUnit)
              }
              item.quantityUnitName = item.quantityUnit ? this.formatQuantityUnit(item.quantityUnit) : ''
              item.itemType = this.operationForm.itemType
              if (item.supplier1Detail) {
                item.tacticsName = item.supplier1Detail.tacticsName
                item.tacticsDetail = item.supplier1Detail.tacticsDetail
              }
              if (item.supplier && item?.supplier === item?.supplier1Detail?.providerNo) {
                item.isSpmSupplier = true
              } else {
                item.isSpmSupplier = false
              }
              item.replaceFirstSupplierReason = item.replaceFirstSupplierReason && item.replaceFirstSupplierReason === '其他' ? item.newReplaceFirstSupplierReason : item.replaceFirstSupplierReason
              return item
            })
            const splitOrderData = this.selectList.map(item => {
              return {
                factory: item.factory,
                position: item.position,
                purchaseGroup: item.purchaseGroup,
                supplierNo: item.supplier,
                skuNo: item.skuNo,
                soNo: item.externalOrderNo,
                soItemNo: item.externalOrderItemNo,
                orderType: item.itemType
              }
            })
            await getSplitOrderTips(this, splitOrderData)
            const res = await transfer2Po(this.selectList)

            let list = this.listData.map(value => {
              let idx = res.findIndex(val => val.id === value.id)
              if (idx !== -1) {
                value.supplier = supplierNoAndName[value.supplier]
                value.msg = res[idx].msg
                value.poNo = res[idx].orderNo
              }
              return value
            })
            this.listData = list

            this.$refs.directGrid.setCheckboxRow(this.selectList, false)
            this.selectList = []
            this.disable()
            this.loading.close()
          } catch (error) {
            this.loading.close()
            console.log(error)
          }
        } else {
          return false
        }
      })
    },
    checkMethod({ row }) {
      if (row.poNo || (row.isPreOrder && !row.preOrderCheckTime)) {
        return false
      } else {
        return true
      }
    },
    async handleDownExcel() {
      this.$refs.ruleForm.$refs.ruleForm.validate((valid) => {
        if (valid) {
          let params = this.formatParams(this.searchForm)
          if (!this.validate(params)) return

          parsePurchaseGroup(
            params,
            setPower(this.userRole),
            this.filtPurchaseList
          )

          if (Array.isArray(params.skuNo)) {
          params.skuNo = params.skuNo.join(' ')
          }
          if (Array.isArray(params.productGroup)) {
            params.productGroup = params.productGroup.join(' ')
          }
          if (Array.isArray(params.soNo)) {
            params.soNo = params.soNo.join(' ')
          }
          if (Array.isArray(params.purchaseGroup)) {
            params.purchaseGroup = params.purchaseGroup.join(' ')
          }

          if (!params.purchaseGroup) {
            return this.$message.error('没有匹配的采购组')
          }
          // this.tablePage.currentPage++
          params = {
            ...params,
            pageNo: 1,
            pageSize: -1,
            action: 'download'
          }
          this.tableLoading = true
          this.searchLoading = true
          if (!this.reportType) {
            getPrDirectList(params)
              .then((res) => {
                const list = res.records.map((item) => {
                  return formatDirectPrListData(item)
                })
                this.exportExcel(list)
                this.is_extending = true
              })
              .catch((error) => {
                console.log(error)
                this.is_extending = false
              })
              .finally(() => {
                this.tableLoading = false
                this.searchLoading = false
              })
          } else {
            getAvailableStockList(params)
              .then((res) => {
                const list = res.records
                this.exportExcel(list)
              })
              .catch((error) => {
                console.log(error)
              })
              .finally(() => {
                this.tableLoading = false
                this.searchLoading = false
              })
          }
        } else {
          return false
        }
      })
    },
    exportExcel(listData) {
      if (!this.reportType) {
        let mapping = directMapping
      // mapping = dragFields('directGrid_fullColumn', mapping, this)
      mapping = filterHiddenFields(mapping, 'mrp_direct_grid')
      const list = listData.map(data => {
          const item = { ...data }
          const {
            msg,
            poNo,
            skuNo,
            factory,
            skuTypeName,
            purchaseGroup,
            quantity,
            quantityUnit,
            supplier,
            isFree,
            unitPrice,
            taxRate,
            directWareHouse,
            skuFactoryStrategy,
            directWareHouseDesc,
            externalOrderNo,
            externalOrderItemNo,
            prNo,
            prItemNo,
            prType,
            mrpPrintRemark,
            customInstructions,
            mrpSupplier,
            soVoucherVo: {
              customerSpecified,
              soCreateDate,
              productSaleName,
              productCustomerName,
              customerNo,
              customerName,
              receiverName,
              receiverPhone,
              receiverProvince,
              receiverCity,
              receiverDistrict,
              receiverTown,
              receiverAddress,
              remark,
              hasDeliveryManager,
              signingBack
            },
            productVO: {
              materialDescribe,
              productGroup,
              productGroupName,
              inventoryTypeName,
              productPositioningName
            },
            supplierDetail: {
              providerName,
              paymentTermsCodeName,
              meinsName,
              currency,
              purchaseMoq,
              idnlf
            },
            supplier1Detail,
            supplier2Detail,
            supplier3Detail,
            recommendSupplier1Detail,
            recommendSupplier2Detail,
            recommendSupplier3Detail,
            customerSpecifiedPurchasePriceVO,
            soDeliveryDate,
            poItemHistoryPrice,
            saleMoq,
            inventoryVo: {
              selfInventory1,
              selfInventory1Quantity,
              selfInventory2,
              selfInventory2Quantity,
              otherInventory1,
              otherInventory1Name,
              otherInventory1Quantity,
              otherTransportationInventory1,
              otherTransportationInventory1Name,
              otherTransportationInventory1Quantity,
              hignStorageInStock1MrpArea,
              hignStorageInStock1MrpAreaName,
              hignStorageInStock1Quantity,
              hignStorageInStock2MrpArea,
              hignStorageInStock2MrpAreaName,
              hignStorageInStock2Quantity
            },
            grossMargin,
            grossAmount
          } = item
          const isVpi = !!prType
          const relSupplier1 = (isVpi || item.customerSpecified) ? customerSpecifiedPurchasePriceVO : supplier1Detail
          return {
            isSpecialOrder: item.prTag?.isSpecialOrder ? '是' : '否',
            isOutOfStock: item.prTag?.isOutOfStock ? '是' : '否',
            isMstaeState: item.prTag?.isMstaeState ? '是' : '否',
            isUrgent: item.prTag?.isUrgent ? '是' : '否',
            supplierArriveDate: item.supplierArriveDate || '',
            isOrderException: item.isOrderException ? '是' : '否',
            supplierCert: supplierCertList.find(it => it.value === item.supplierCert)?.label || '',
            msg,
            poNo,
            skuNo,
            factory,
            materialDescribe,
            skuTypeName,
            purchaseGroup,
            mrpSupplier,
            productPositioningName,
            customInstructions: this.handleCustomInstructions(customInstructions),
            userName: (this.purchaseList.find(item => item.groupCode === purchaseGroup) || {}).userName,
            productGroup,
            productGroupName,
            inventoryTypeName,
            quantity,
            quantityUnit: this.formatQuantityUnit(quantityUnit),
            customerSpecified: customerSpecified === 1 ? '是' : '否',
            directWareHouse,
            skuFactoryStrategy,
            directWareHouseDesc,
            supplier: supplier.split(' ')[0],
            providerName,
            paymentTermsCodeName,
            isFree: (isFree || isFree === 1) ? '是' : '否',
            unitPrice,
            meinsName: customerSpecified === 1 ? (customerSpecifiedPurchasePriceVO ? customerSpecifiedPurchasePriceVO.meinsName : '') : meinsName,
            currency: this.getCurrency(currency, supplier1Detail),
            taxRate: taxRate ? taxRate + '%' : '',
            purchaseMoq,
            idnlf,
            externalOrderNo,
            externalOrderItemNo,
            soCreateDate: soCreateDate ? soCreateDate.slice(0, 10) : '',
            soDeliveryDate: soDeliveryDate ? soDeliveryDate.slice(0, 10) : '',
            productSaleName,
            productCustomerName,
            customerNo,
            customerName,
            receiverName,
            receiverPhone,
            receiverAddress: (receiverProvince || '') + (receiverCity || '') + (receiverDistrict || '') + (receiverTown || '') + (receiverAddress || ''),
            remark,
            purchasePriceVOList0: (isVpi ? customerSpecifiedPurchasePriceVO.providerNo : supplier1Detail.providerNo) || '',
            purchasePrice0Name: (isVpi ? customerSpecifiedPurchasePriceVO.providerName : supplier1Detail.providerName) || '',
            purchasePrice0esokz: supplier1Detail.providerNo ? this.esokzName(supplier1Detail.esokz) : '',
            purchasePrice0currency: this.getCurrency(supplier1Detail.currency, supplier1Detail),
            purchasePrice0taxRate: isVpi ? (customerSpecifiedPurchasePriceVO.providerNo ? customerSpecifiedPurchasePriceVO.taxRate + '%' : '') : (supplier1Detail.providerNo ? supplier1Detail.taxRate + '%' : ''),
            purchasePrice0meins: this.formatQuantityUnit(supplier1Detail.meins) || '',
            purchasePrice0purchaseMoq: supplier1Detail.purchaseMoq || '',
            purchasePrice0ifBid: supplier1Detail.providerNo ? (supplier1Detail.ifBid || '否') : '',
            purchasePrice0ifIncludeFreight: supplier1Detail.providerNo ? (supplier1Detail.ifIncludeFreight === 1 ? '是' : '否') : '',
            purchasePrice0freightRate: supplier1Detail.freightRate || '',
            purchasePrice0paymentTermsCodeName: supplier1Detail.paymentTermsCodeName || '',
            purchasePrice0ifUpInventory: supplier1Detail.providerNo ? (supplier1Detail.ifUpInventory || 0) : '',
            purchasePrice0supplierClassifyName: supplier1Detail.supplierClassifyName || '',
            purchasePrice0oemDirectSupplier: relSupplier1.providerNo ? (relSupplier1.oemDirectSupplier === 1 ? '是' : '否') : '',
            purchasePrice0idnlf: supplier1Detail.idnlf,
            purchasePrice0ifSignFrameworkAgreement: supplier1Detail.providerNo ? (supplier1Detail.ifSignFrameworkAgreement === 1 ? '是' : '否') : '',
            purchasePrice0Price: this.getPurchasePrice(isVpi ? customerSpecifiedPurchasePriceVO : supplier1Detail),
            purchasePrice0LeadTime: supplier1Detail.leadTime,
            purchasePrice0tacticsName: supplier1Detail.tacticsName,
            purchasePrice0ifAgency: supplier1Detail.ifAgency === 0 ? '否' : supplier1Detail.ifAgency === 1 ? '是' : '',
            purchasePrice0ifTradeCertificate: supplier1Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier1Detail.ifTradeCertificate) ? '是' : '',
            purchasePriceVOList1: supplier2Detail.providerNo || '',
            purchasePrice1Name: supplier2Detail.providerName || '',
            purchasePrice1ifUpInventory: supplier2Detail.providerNo ? (supplier2Detail.ifUpInventory || 0) : '',
            purchasePrice1ifIncludeFreight: supplier2Detail.providerNo ? (supplier2Detail.ifIncludeFreight === 1 ? '是' : '否') : '',
            purchasePrice1freightRate: supplier2Detail.freightRate || '',
            purchasePrice1supplierClassifyName: supplier2Detail.supplierClassifyName || '',
            purchasePrice1oemDirectSupplier: supplier2Detail.providerNo ? (supplier2Detail.oemDirectSupplier === 1 ? '是' : '否') : '',
            purchasePrice1idnlf: supplier2Detail.idnlf,
            purchasePrice1ifSignFrameworkAgreement: supplier2Detail.providerNo ? (supplier2Detail.ifSignFrameworkAgreement === 1 ? '是' : '否') : '',
            purchasePrice1Price: this.getPurchasePrice(supplier2Detail),
            purchasePrice1LeadTime: supplier2Detail.leadTime,
            purchasePrice1tacticsName: supplier2Detail.tacticsName,
            purchasePrice1ifAgency: supplier2Detail.ifAgency === 0 ? '否' : supplier2Detail.ifAgency === 1 ? '是' : '',
            purchasePrice1ifTradeCertificate: supplier2Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier2Detail.ifTradeCertificate) ? '是' : '',
            purchasePriceVOList2: supplier3Detail.providerNo || '',
            purchasePrice2Name: supplier3Detail.providerName || '',
            purchasePrice2ifUpInventory: supplier3Detail.providerNo ? (supplier3Detail.ifUpInventory || 0) : '',
            purchasePrice2ifIncludeFreight: supplier3Detail.providerNo ? (supplier3Detail.ifIncludeFreight === 1 ? '是' : '否') : '',
            purchasePrice2freightRate: supplier3Detail.freightRate || '',
            purchasePrice2supplierClassifyName: supplier3Detail.supplierClassifyName || '',
            purchasePrice2oemDirectSupplier: supplier3Detail.providerNo ? (supplier3Detail.oemDirectSupplier === 1 ? '是' : '否') : '',
            purchasePrice2idnlf: supplier3Detail.idnlf,
            purchasePrice2ifSignFrameworkAgreement: supplier3Detail.providerNo ? (supplier3Detail.ifSignFrameworkAgreement === 1 ? '是' : '否') : '',
            purchasePrice2Price: this.getPurchasePrice(supplier3Detail),
            purchasePrice2LeadTime: supplier3Detail.leadTime,
            purchasePrice2tacticsName: supplier3Detail.tacticsName,
            purchasePrice2ifAgency: supplier3Detail.ifAgency === 0 ? '否' : supplier3Detail.ifAgency === 1 ? '是' : '',
            purchasePrice2ifTradeCertificate: supplier3Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(supplier3Detail.ifTradeCertificate) ? '是' : '',
            prNo,
            prItemNo,
            hasDeliveryManager: hasDeliveryManager === '1' || hasDeliveryManager === '2' ? '是' : '否',
            signingBack: this.formatSigningBack(signingBack),
            mrpPrintRemark,
            poItemHistoryPrice,
            recommendSupplier1Detail,
            recommendSupplier2Detail,
            recommendSupplier3Detail,
            saleMoq,
            selfInventory1: this.getNameAddQuantity(
              selfInventory1,
              selfInventory1Quantity
            ),
            selfInventory2: this.getNameAddQuantity(
              selfInventory2,
              selfInventory2Quantity
            ),
            otherInventory1:
              otherInventory1Quantity > 0
                ? otherInventory1Quantity +
                '/' +
                otherInventory1 +
                '/' +
                otherInventory1Name
                : '',
            otherTransportationInventory1:
              otherTransportationInventory1Quantity > 0
                ? otherTransportationInventory1Quantity +
                '/' +
                otherTransportationInventory1 +
                '/' +
                otherTransportationInventory1Name
                : '',
            hignStorageInStock1MrpArea:
              hignStorageInStock1Quantity > 0
                ? hignStorageInStock1Quantity +
                '/' +
                hignStorageInStock1MrpArea +
                '/' +
                hignStorageInStock1MrpAreaName
                : '',
            hignStorageInStock2MrpArea:
              hignStorageInStock2Quantity > 0
                ? hignStorageInStock2Quantity +
                '/' +
                hignStorageInStock2MrpArea +
                '/' +
                hignStorageInStock2MrpAreaName
                : '',
            grossMargin,
            ifGrossMargin: item.ifGrossMargin ? '是' : '否',
            stableMark: item.stableMark === 'Y' ? '是' : '否',
            grossAmount
          }
      })
      const allList = list.map(data => {
          Object.keys(data).forEach(key => {
            if (mapping[key]) {
              data[mapping[key]] = data[key]
            }
            delete data[key]
          })
          return data
      })
      writeFile(allList, `物料需求报表 – 直发 - 负需求 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: Object.values(mapping) })
      } else {
        let mapping = this.tableKeyValue
        mapping = dragFields('avariableGrid_fullColumn', mapping, this)
        mapping = filterHiddenFields(mapping, 'mrp-avariable_grid')
        const list = listData.map((row) => {
          let filterRow = pick(row, this.tableFields)
          filterRow.stockingStrategy = (
            stockingStrategy.find(
              (item) => item.value === row.stockingStrategy
            ) || {}
          ).label
          filterRow.quantityUnit = this.formatQuantityUnit(row.quantityUnit)
          return filterRow
        })
        const allList = list.map((item) => {
          const data = { ...item }
          Object.keys(data).forEach((key) => {
            if (mapping[key]) {
              data[mapping[key]] = data[key]
            }
            delete data[key]
          })
          return data
        })
        writeFile(
          allList,
          `物料需求报表 – 直发 – 可用库存 ${moment(new Date()).format(
            'YYYY-MM-DD HH-mm-ss'
          )}.xlsx`,
          { header: Object.values(mapping) }
        )
      }
    },
    toDetail(poNo) {
      this.$router.push(`/orderPurchase/detail/${poNo}`)
    },
    columnDrop() {
      this.$nextTick(() => {
        let $table
        if (!this.reportType) {
          $table = this.$refs.directGrid
        } else {
          $table = this.$refs.avariableGrid
        }
        this.sortable2 = Sortable.create($table.$el.querySelector('.body--wrapper>.vxe-table--header .vxe-header--row'), {
          handle: '.vxe-header--column:not(.col--fixed)',
          onEnd: ({ item, newIndex, oldIndex }) => {
            const { fullColumn, tableColumn } = $table.getTableColumn()
            const targetThElem = item
            const wrapperElem = targetThElem.parentNode
            const newColumn = fullColumn[newIndex]
            if (newColumn.fixed) {
              // 错误的移动
              if (newIndex > oldIndex) {
                wrapperElem.insertBefore(targetThElem, wrapperElem.children[oldIndex])
              } else {
                wrapperElem.insertBefore(wrapperElem.children[oldIndex], targetThElem)
              }
              return this.$XModal.message({ content: '固定列不允许拖动！', status: 'error' })
            }
            // 转换真实索引
            const oldColumnIndex = $table.getColumnIndex(tableColumn[oldIndex])
            const newColumnIndex = $table.getColumnIndex(tableColumn[newIndex])
            // 移动到目标列
            const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
            fullColumn.splice(newColumnIndex, 0, currRow)
            $table.loadColumn(fullColumn)

            if (!this.reportType) {
                localStorage.setItem(
                  'directGrid_fullColumn',
                  JSON.stringify(fullColumn)
                )
              } else {
                localStorage.setItem(
                  'avariableGrid_fullColumn',
                  JSON.stringify(fullColumn)
                )
              }
            // localStorage.setItem('directGrid_fullColumn', JSON.stringify(fullColumn))
          }
        })
      })
    },
    editRow(row) {
      this.centerDialogVisible = true
      this.row = row
      this.mrpPrintRemark = this.row.mrpPrintRemark
    },
    handleClose() {
      this.mrpPrintRemark = ''
      this.centerDialogVisible = false
    },
    addMrpPrintRemark() {
      this.row.mrpPrintRemark = this.mrpPrintRemark
      this.handleClose()
    },
    getReportType(data) {
      this.reportType = data
      if (!data && this.clearListData) {
        this.listData = []
        this.tablePage.total = 0
      }
      if (data && this.clearListData) {
        this.aStockListData = []
        this.aTablePage.total = 0
      }
      this.clearListData = false

      this.columnDrop()
    },
    getNameAddQuantity(code, quantity) {
      let find = this.warehouseList.find(
        (item) => item.warehouseLocationCode === code
      )
      if (find && quantity > 0) {
        return quantity + '/' + code + '/' + find.warehouseLocationName
      } else {
        return ''
      }
    },
    async getRate(row) {
      let params = {
        baseUnit: this.formatQuantityUnit(row.quantityUnit),
        factory: row.factory,
        purchasePrice: row.unitPrice,
        purchasePriceUnit:
          (row.soVoucherVo.customerSpecified === 1 || this.isVpi(row))
            ? (row.supplier ? this.formatQuantityUnit(row.quantityUnit) : '')
            : row.supplierDetail.meinsName,
        quantity: row.quantity,
        skuNo: row.skuNo,
        soItemNo: row.externalOrderItemNo,
        soNo: row.externalOrderNo,
        supplier: row.supplier,
        taxCode: row.taxRate,
        umren: row.supplierDetail.umren,
        umrez: row.supplierDetail.umrez
      };
      const res = await queryGrossMargin(params)
      if (res.code === 200 && res.data) {
        row.grossMargin = res.data.grossMargin
        row.grossAmount = res.data.grossAmount
      } else {
        this.$message.error(res.msg || res.message)
      }
    },
    changeUnitPriceFn(row) {
      this.changeUnitPrice(row);
      this.getRate(row);
    }
  }
}
</script>

<style lang="scss" scoped>
  .list-container {
    padding: 5px 20px;
  }

  .el-icon-arrow-down.alignRight {
    font-size: 25px;
  }

  .el-icon-arrow-up.alignRight {
    font-size: 25px;
  }

  .orderReason-select {
    width: 100px !important;
  }
</style>

<style lang="scss">
  .list-container {
    .vxe-table--render-default.size--mini .vxe-body--column.col--ellipsis > .vxe-cell, .vxe-table--render-default.size--mini .vxe-footer--column.col--ellipsis > .vxe-cell, .vxe-table--render-default.size--mini .vxe-header--column.col--ellipsis > .vxe-cell {
      max-height: 100px;
    }
  }

  .el-popover.popover {
    text-align: center;
  }

  .el-popover.align {
    text-align: left;
  }

  .is-vpi {
    color: white;
    background-color: red;
    border-radius: 50%;
    margin-right: 5px;

    .product-vpi-icon {
      width: 10px;
      height: 10px;
      margin-left: 1px;
      padding-top: 2px;
    }
  }
</style>
