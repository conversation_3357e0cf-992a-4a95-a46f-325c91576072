import clip from '@/utils/clipboard'
import { buildOptions } from '@/utils/mm'
import { safeRun } from '@/utils/index'
import { needSplitOrder } from '@/api/mrp'

const ifBidList = {
  '1': '第一名中标',
  '2': '第二名中标',
  '3': '第三名中标'
}

// 汇总 转单 明细
export function formatPrListData(data, type = '') {
  if (data.productVO === null) {
    data.productVO = {}
  }
  if (data.purchasePriceVOList === null) {
    data.purchasePriceVOList = []
  }
  if (data.prList === null) {
    data.prList = []
  }
  if (data.customerSpecifiedPurchasePriceVO === null) {
    data.customerSpecifiedPurchasePriceVO = {}
  }
  const { inventoryTypeName, materialDescribe, productGroup, productGroupName, mstaeAllSwitch } = data.productVO
  const { purchasePriceVOList = [], recommendSupplierList = [], prList = [], prType = 0, customerSpecifiedPurchasePriceVO = {} } = data
  const supplier1Detail = purchasePriceVOList.find(item => item.priority === 0) || {}
  const isVpi = (!!prType && type !== 'detail')
  const supplier1DetailSupplier = supplier1Detail.providerNo ? `${supplier1Detail.providerNo} ${supplier1Detail.providerName}` : ''
  const customerSpecifiedVOSupplier = customerSpecifiedPurchasePriceVO.providerNo ? `${customerSpecifiedPurchasePriceVO.providerNo} ${customerSpecifiedPurchasePriceVO.providerName}` : ''
  const recommendSupplier1Detail = recommendSupplierList?.find(item => item.level === 1) || null
  const recommendSupplier2Detail = recommendSupplierList?.find(item => item.level === 2) || null
  const recommendSupplier3Detail = recommendSupplierList?.find(item => item.level === 3) || null
  return {
    ...data,
    inventoryTypeName,
    materialDescribe,
    productGroup,
    productGroupName,
    mstaeAllSwitch,
    supplier1Detail: supplier1Detail,
    supplier2Detail: purchasePriceVOList.find(item => item.priority === 1) || {},
    supplier3Detail: purchasePriceVOList.find(item => item.priority === 2) || {},
    recommendSupplier1Detail: recommendSupplier1Detail ? `${recommendSupplier1Detail.factory || ''}-${recommendSupplier1Detail.skuNo || ''}-${recommendSupplier1Detail.supplierNo || ''} ${recommendSupplier1Detail.supplierName || ''}` : '',
    recommendSupplier2Detail: recommendSupplier2Detail ? `${recommendSupplier2Detail.factory || ''}-${recommendSupplier2Detail.skuNo || ''}-${recommendSupplier2Detail.supplierNo || ''} ${recommendSupplier2Detail.supplierName || ''}` : '',
    recommendSupplier3Detail: recommendSupplier3Detail ? `${recommendSupplier3Detail.factory || ''}-${recommendSupplier3Detail.skuNo || ''}-${recommendSupplier3Detail.supplierNo || ''} ${recommendSupplier3Detail.supplierName || ''}` : '',
    prIds: prList.map(val => val.id),
    shipWarehousePositon: '',
    supplier: isVpi ? customerSpecifiedVOSupplier : supplier1DetailSupplier,
    unitPrice: isVpi ? getPurchasePrice(customerSpecifiedPurchasePriceVO) : getPurchasePrice(supplier1Detail),
    taxRate: isVpi ? customerSpecifiedPurchasePriceVO.taxRate : supplier1Detail.taxRate,
    supplierDetail: isVpi ? customerSpecifiedPurchasePriceVO : {},
    isFree: 0,
    customerSpecifiedPurchasePriceVO,
    orderCalendar: isVpi ? customerSpecifiedPurchasePriceVO : supplier1Detail
  }
}

// 直发 分配供应商问题
export function formatDirectPrListData(data) {
  if (data.productVO === null) {
    data.productVO = {}
  }
  if (data.purchasePriceVOList === null) {
    data.purchasePriceVOList = []
  }
  if (data.prList === null) {
    data.prList = []
  }
  if (data.customerSpecifiedPurchasePriceVO === null) {
    data.customerSpecifiedPurchasePriceVO = {}
  }
  const {
    customerSpecified,
    soCreateDate,
    productSaleName,
    productCustomerName,
    customerNo,
    customerName,
    receiverName,
    receiverPhone,
    receiverProvince,
    receiverCity,
    receiverDistrict,
    receiverTown,
    receiverAddress,
    remark,
    hasDeliveryManager,
    signingBack
  } = data.soVoucherVo
  const { materialDescribe, productGroup, productGroupName, inventoryTypeName } = data.productVO
  const { purchasePriceVOList = [], recommendSupplierList = [], prList = [], customerSpecifiedPurchasePriceVO = {}, saleMoq, purchaseMoq, quantityUnit, prType = 0 } = data
  const recommendSupplier1Detail = recommendSupplierList?.find(item => item.level === 1) || null
  const recommendSupplier2Detail = recommendSupplierList?.find(item => item.level === 2) || null
  const recommendSupplier3Detail = recommendSupplierList?.find(item => item.level === 3) || null
  const isVpi = !!prType
  let msg = ''
  const meins = (customerSpecified === 1 || isVpi) ? quantityUnit : customerSpecifiedPurchasePriceVO?.meins
  const supplier = customerSpecifiedPurchasePriceVO.providerNo ? `${customerSpecifiedPurchasePriceVO.providerNo} ${customerSpecifiedPurchasePriceVO.providerName}` : ''
  if (supplier && meins !== quantityUnit) {
    msg = '采购、基本单位不一致'
  } else if (supplier && saleMoq && purchaseMoq) {
    msg = purchaseMoq > saleMoq ? '采购MOQ大于销售MOQ' : ''
  } else if (supplier && (!saleMoq || !purchaseMoq)) {
    msg = `未查询到${!saleMoq ? '销售' : '采购'}MOQ`
  } else {
    msg = ''
  }
  return {
    ...data,
    inventoryTypeName,
    customerSpecified,
    soCreateDate,
    productSaleName,
    productCustomerName,
    customerNo,
    customerName,
    receiverName,
    receiverPhone,
    receiverProvince,
    receiverCity,
    receiverDistrict,
    receiverTown,
    receiverAddress,
    remark,
    hasDeliveryManager,
    signingBack,
    materialDescribe,
    productGroup,
    productGroupName,
    supplier1Detail: purchasePriceVOList.find(item => item.priority === 0) || {},
    supplier2Detail: purchasePriceVOList.find(item => item.priority === 1) || {},
    supplier3Detail: purchasePriceVOList.find(item => item.priority === 2) || {},
    recommendSupplier1Detail: recommendSupplier1Detail ? `${recommendSupplier1Detail.factory || ''}-${recommendSupplier1Detail.skuNo || ''}-${recommendSupplier1Detail.supplierNo || ''} ${recommendSupplier1Detail.supplierName || ''}` : '',
    recommendSupplier2Detail: recommendSupplier2Detail ? `${recommendSupplier2Detail.factory || ''}-${recommendSupplier2Detail.skuNo || ''}-${recommendSupplier2Detail.supplierNo || ''} ${recommendSupplier2Detail.supplierName || ''}` : '',
    recommendSupplier3Detail: recommendSupplier3Detail ? `${recommendSupplier3Detail.factory || ''}-${recommendSupplier3Detail.skuNo || ''}-${recommendSupplier3Detail.supplierNo || ''} ${recommendSupplier3Detail.supplierName || ''}` : '',
    prIds: prList.map(val => val.id),
    supplier,
    unitPrice: getPurchasePrice(customerSpecifiedPurchasePriceVO),
    taxRate: customerSpecifiedPurchasePriceVO.taxRate,
    supplierDetail: customerSpecifiedPurchasePriceVO,
    shipWarehousePositon: '',
    isFree: 0,
    msg
  }
}

// 通过公司列表获取工厂列表
export function getFactoryList(companyFactoryList) {
  let factoryList = []
  companyFactoryList.forEach(item => {
    factoryList = [...factoryList, ...item.factoryList]
  })
  return factoryList
}

export function getMrpType(mrpType, stockingStrategy, isCalculate, createMark) {
  if (mrpType === 'fpp') {
    return '采购计划'
  }
  if (mrpType === 'rop' && stockingStrategy === 2) {
    return '再订货点/补批量'
  }
  if (mrpType === 'rop' && stockingStrategy === 3) {
    return '再订货点/最大库存'
  }
  if (mrpType === 'rop' && stockingStrategy === 4) {
    return '再订货点/EVM叫料'
  }
  if (mrpType === 'safeS') {
    return '安全库存'
  }
  if (mrpType === 'so') {
    return '销售订单'
  }
  if (mrpType === 'poD') {
    return '转储需求'
  }
  if (mrpType === 'poP' || mrpType === 'poC') {
    return '采购订单'
  }
  if (mrpType === 'pr') {
    return '采购申请'
  }
  if (mrpType === 'ido') {
    return '内向交货单'
  }
  if (mrpType === 'iaoD' || mrpType === 'iaoP') {
    return '库存申请单'
  }
  if (mrpType === 'rel') {
    return '相关需求'
  }
  if (mrpType === 'strP' || mrpType === 'strD') {
    if (createMark === 'EVM') {
      return 'EVM计划调拨单'
    }
    return '计划调拨单'
  }
  if (mrpType === 'dnP') {
    return 'DN单'
  }
  if (mrpType === 'dnD') {
    return '销售出库单'
  }
  if (mrpType === 'dnS') {
    return '调拨出库单'
  }
  if (mrpType === 'stock') {
    if (isCalculate === 0) {
      return '库存地点'
    }
    return '库存'
  }
  if (mrpType === 'bo') {
    return '商机'
  }
  if (mrpType === 'wip') {
    return '计划订单'
  }
  if (mrpType === 'proP' || mrpType === 'proC') {
    return '生产订单'
  } else {
    return ''
  }
}

export function esokzName(esokz) {
  if (esokz === '0') {
    return '采买'
  }
  if (esokz === '2') {
    return '寄售'
  }
  if (esokz === '3') {
    return ' 外协加工'
  }
  return ''
}

export function getIfBid(ifBid) {
  return ifBidList[ifBid]
}

const gradeList = {
  '-1': '强烈不推荐',
  '0': '不推荐',
  '1': '推荐',
  '2': '强烈推荐'
}

export function getGrade(grade) {
  return gradeList[String(grade)]
}

export function getCurrency(currency, supplier1Detail) {
  const { currencyName } = supplier1Detail || {}
  if (currencyName) {
    return currencyName
  }
  if (currency === 1) {
    return '人民币'
  }
  if (currency === 10) {
    return '新加坡币'
  }
  if (currency === 11) {
    return '韩元'
  }
  if (currency === 3) {
    return '美元'
  }
  if (currency === 4) {
    return '英镑'
  }
  if (currency === 5) {
    return '欧元'
  }
  if (currency === 6) {
    return '港元'
  }
  if (currency === 7) {
    return '日元'
  }
  if (currency === 8) {
    return '澳元'
  }
  if (currency === 13) {
    return '新台币'
  }
  if (currency === 9) {
    return '瑞士法郎'
  } else {
    return ''
  }
}

export function formatterOtherInventory1(row) {
  return row.inventoryVo.otherInventory1Quantity > 0 ? row.inventoryVo.otherInventory1Quantity + '/' + row.inventoryVo.otherInventory1 + '/' + row.inventoryVo.otherInventory1Name : ''
}

export function formatterOtherTransportationInventory1(row) {
  return row.inventoryVo.otherTransportationInventory1Quantity > 0 ? row.inventoryVo.otherTransportationInventory1Quantity + '/' + row.inventoryVo.otherTransportationInventory1 + '/' + row.inventoryVo.otherTransportationInventory1Name : ''
}

export function formatterHignStorageInStock1MrpArea(row) {
  return row.inventoryVo.hignStorageInStock1Quantity > 0 ? row.inventoryVo.hignStorageInStock1Quantity + '/' + row.inventoryVo.hignStorageInStock1MrpArea + '/' + row.inventoryVo.hignStorageInStock1MrpAreaName : ''
}

export function formatterHignStorageInStock2MrpArea(row) {
  return row.inventoryVo.hignStorageInStock2Quantity > 0 ? row.inventoryVo.hignStorageInStock2Quantity + '/' + row.inventoryVo.hignStorageInStock2MrpArea + '/' + row.inventoryVo.hignStorageInStock2MrpAreaName : ''
}

// 获取有效价格
export function getPurchasePrice(detail) {
  if ((detail?.ladderPrice || detail?.unitPrice) && !detail.validPrice) {
    detail.unitPrice = 0
  }
  if (detail?.ladderPrice && detail.validPrice) {
    detail.unitPrice = detail.ladderPrice
  }
  return detail?.unitPrice || 0
}

// 通过用户角色设置权限
export function setPower(userRole) {
  return userRole.find(item => item === 'MRP-采购总监' || item === 'MRP-管理员' || item === 'MRP-EVM运营' || item === 'MRP-采购值班账号')
}

// 折叠时列表高度自适应
export function fold(self, refName, key, isHide) {
  self[key] = !isHide
  self.$nextTick(function () {
    self.tableHeight = window.innerHeight - refName.$el.offsetTop - 130
    if (self.tableHeight < 260) {
      self.tableHeight = 260
    }
  })
}

// 复制供应商编码
export function handleCopy(no) {
  clip(no, event, () => {
    const content = '复制成功'
    this.$message({
      message: content,
      type: 'success'
    })
  })
}

// 单元格被双击时会触发该事件
export function cellClick(config) {
  try {
    const { cell } = config
    let content = cell.innerText
    clip(content, event, () => {
      this.$message({
        message: '复制成功',
        type: 'success'
      })
    })
  } catch (err) {
    console.log(err)
  }
}

// *号匹配
export function parsePurchaseGroup(data, purchaseGroupRole, purchaseList) {
  console.log(data.purchaseGroup)
  if (Array.isArray(data.purchaseGroup) && data.purchaseGroup.length) {
    const pgOptions = purchaseGroupRole ? buildOptions('purchaseGroup') : purchaseList.map(item => ({
      name: item.userName,
      value: item.groupCode,
      securityUsername: item.securityUsername
    }))
    const groups = []
    const matchedGroups = data.purchaseGroup.filter(group => /\*/gi.test(group)).map(item => item.toUpperCase())
    for (let group of matchedGroups) {
      const rp = group.replace(/\*/gi, '')
      groups.push(...pgOptions.filter(item => item.value.indexOf(rp) > -1).map(item => item.value))
    }
    const newList = data.purchaseGroup.filter(group => !/\*/gi.test(group))
    newList.push(...groups)
    data.purchaseGroup = newList
  }
}

// 小数点后小于等于6位数
export function changeUnitPrice(row) {
  const idx = String(row.unitPrice).indexOf('.')
  if (idx !== -1) {
    const precision = String(row.unitPrice).length - idx
    if (precision > 6) {
      row.unitPrice = Number(String(row.unitPrice).slice(0, idx + 7))
    }
  }
}

// 下载指定列
function getHiddenFields(id) {
  let hiddenProps = []
  safeRun(() => {
    if (localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE) {
      hiddenProps = JSON.parse(localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE)[id].split(',')
    }
  })
  hiddenProps.forEach(item => {
    if (item === 'skuNo') {
      hiddenProps.push('skuTagMsg')
    }
    if (item === 'productGroup') {
      hiddenProps.push('productGroupName')
    }
    if (item === 'mrpArea') {
      hiddenProps.push('mrpAreaDesc')
    }
    if (item === 'purchaseGroup') {
      hiddenProps.push('userName')
    }
    if (item === 'purchasePriceVOList0') {
      hiddenProps.push('purchasePrice0Name', 'purchasePrice0esokz', 'purchasePrice0currency', 'purchasePrice0taxRate', 'purchasePrice0meins', 'purchasePrice0purchaseMoq', 'purchasePrice0ifBid', 'purchasePrice0ifIncludeFreight', 'purchasePrice0freightRate', 'purchasePrice0paymentTermsCodeName', 'purchasePrice0ifUpInventory', 'purchasePrice0ifSignFrameworkAgreement', 'purchasePrice0supplierClassifyName')
    }
    if (item === 'purchasePriceVOList1') {
      hiddenProps.push('purchasePrice1Name', 'purchasePrice1ifUpInventory', 'purchasePrice1ifIncludeFreight', 'purchasePrice1freightRate', 'purchasePrice1ifSignFrameworkAgreement', 'purchasePrice1supplierClassifyName')
    }
    if (item === 'purchasePriceVOList2') {
      hiddenProps.push('purchasePrice2Name', 'purchasePrice2ifUpInventory', 'purchasePrice2ifIncludeFreight', 'purchasePrice2freightRate', 'purchasePrice2ifSignFrameworkAgreement', 'purchasePrice2supplierClassifyName')
    }
    if (item === 'supplier1Detail.leadTime') {
      hiddenProps.push('purchasePrice0LeadTime')
    }
    if (item === 'supplier2Detail.leadTime') {
      hiddenProps.push('purchasePrice1LeadTime')
    }
    if (item === 'supplier3Detail.leadTime') {
      hiddenProps.push('purchasePrice2LeadTime')
    }
    if (item === 'position') {
      hiddenProps.push('warehouseLocationName')
    }
    if (item === 'customerNo') {
      hiddenProps.push('customerName')
    }
    if (item === 'directWareHouse') {
      hiddenProps.push('directWareHouseDesc')
    }
    if (item === 'supplier') {
      hiddenProps.push('providerName')
    }
    if (item === 'supplierDetail.paymentTermsCodeName') {
      hiddenProps.push('paymentTermsCodeName')
    }
    if (item === 'supplierDetail.purchaseMoq') {
      hiddenProps.push('purchaseMoq')
    }
    if (item === 'supplierDetail.idnlf') {
      hiddenProps.push('idnlf')
    }
  })
  return hiddenProps
}

export function filterHiddenFields(mapping, id) {
  // 导出过滤隐藏的列
  if (getHiddenFields(id).length) {
    Object.keys(mapping).forEach(key => {
      if (getHiddenFields(id).find(prop => key === prop)) {
        delete mapping[key]
      }
    })
  }
  return mapping
}

// 组件总用量
export function getBomItemInventoryVoList(row) {
  let getBomItemInventoryVo = ''
  if (row?.inventoryVo.bomItemInventoryVoList && row?.inventoryVo.bomItemInventoryVoList.length > 0) {
    row.inventoryVo.bomItemInventoryVoList.map(item => {
      getBomItemInventoryVo = getBomItemInventoryVo + item.skuNo + '/' + item.quantity + '/' + item.mrpArea + '/' + item.mrpAreaName + ';'
    })
  } else return ''
  return getBomItemInventoryVo
}

// 导出拖拽后的列
export function dragFields(key, mapping, self) {
  const fullcolumns = JSON.parse(localStorage.getItem(key))
  if (fullcolumns) {
    let map = {}
    fullcolumns.map(item => {
      if (item.property) {
        map[item.property] = mapping[item.property]
        if (item.property === 'skuNo' && mapping.skuTagMsg) {
          self.$set(map, 'skuTagMsg', mapping.skuTagMsg)
        }
        if (item.property === 'productGroup') {
          self.$set(map, 'productGroupName', mapping.productGroupName)
        }
        if (item.property === 'mrpArea') {
          self.$set(map, 'mrpAreaDesc', mapping.mrpAreaDesc)
        }
        if (item.property === 'purchaseGroup') {
          self.$set(map, 'userName', mapping.userName)
        }
        if (item.property === 'purchasePriceVOList0') {
          self.$set(map, 'purchasePrice0Name', mapping.purchasePrice0Name)
          self.$set(map, 'purchasePrice0esokz', mapping.purchasePrice0esokz)
          self.$set(map, 'purchasePrice0currency', mapping.purchasePrice0currency)
          self.$set(map, 'purchasePrice0taxRate', mapping.purchasePrice0taxRate)
          self.$set(map, 'purchasePrice0meins', mapping.purchasePrice0meins)
          self.$set(map, 'purchasePrice0purchaseMoq', mapping.purchasePrice0purchaseMoq)
          self.$set(map, 'purchasePrice0ifBid', mapping.purchasePrice0ifBid)
          self.$set(map, 'purchasePrice0ifIncludeFreight', mapping.purchasePrice0ifIncludeFreight)
          self.$set(map, 'purchasePrice0freightRate', mapping.purchasePrice0freightRate)
          self.$set(map, 'purchasePrice0paymentTermsCodeName', mapping.purchasePrice0paymentTermsCodeName)
          self.$set(map, 'purchasePrice0ifUpInventory', mapping.purchasePrice0ifUpInventory)
          self.$set(map, 'purchasePrice0supplierClassifyName', mapping.purchasePrice0supplierClassifyName)
          self.$set(map, 'purchasePrice0ifSignFrameworkAgreement', mapping.purchasePrice0ifSignFrameworkAgreement)
        }
        if (item.property === 'purchasePriceVOList1') {
          self.$set(map, 'purchasePrice1Name', mapping.purchasePrice1Name)
          self.$set(map, 'purchasePrice1ifUpInventory', mapping.purchasePrice1ifUpInventory)
          self.$set(map, 'purchasePrice1ifIncludeFreight', mapping.purchasePrice1ifIncludeFreight)
          self.$set(map, 'purchasePrice1freightRate', mapping.purchasePrice1freightRate)
          self.$set(map, 'purchasePrice1supplierClassifyName', mapping.purchasePrice1supplierClassifyName)
          self.$set(map, 'purchasePrice1ifSignFrameworkAgreement', mapping.purchasePrice1ifSignFrameworkAgreement)
        }
        if (item.property === 'purchasePriceVOList2') {
          self.$set(map, 'purchasePrice2Name', mapping.purchasePrice2Name)
          self.$set(map, 'purchasePrice2ifUpInventory', mapping.purchasePrice2ifUpInventory)
          self.$set(map, 'purchasePrice2ifIncludeFreight', mapping.purchasePrice2ifIncludeFreight)
          self.$set(map, 'purchasePrice2freightRate', mapping.purchasePrice2freightRate)
          self.$set(map, 'purchasePrice2supplierClassifyName', mapping.purchasePrice2supplierClassifyName)
          self.$set(map, 'purchasePrice2ifSignFrameworkAgreement', mapping.purchasePrice2ifSignFrameworkAgreement)
        }
        if (item.property === 'supplier1Detail.leadTime') {
          self.$set(map, 'purchasePrice0LeadTime', mapping.purchasePrice0LeadTime)
          delete map[item.property]
        }
        if (item.property === 'supplier2Detail.leadTime') {
          self.$set(map, 'purchasePrice1LeadTime', mapping.purchasePrice1LeadTime)
          delete map[item.property]
        }
        if (item.property === 'supplier3Detail.leadTime') {
          self.$set(map, 'purchasePrice2LeadTime', mapping.purchasePrice2LeadTime)
          delete map[item.property]
        }
        if (item.property === 'position' && mapping.warehouseLocationName) {
          self.$set(map, 'warehouseLocationName', mapping.warehouseLocationName)
        }
        if (item.property === 'customerNo') {
          self.$set(map, 'customerName', mapping.customerName)
        }
        if (item.property === 'directWareHouse' && mapping.directWareHouseDesc) {
          self.$set(map, 'directWareHouseDesc', mapping.directWareHouseDesc)
        }
        if (item.property === 'supplier' && mapping.providerName) {
          self.$set(map, 'providerName', mapping.providerName)
        }
        if (item.property === 'supplierDetail.paymentTermsCodeName') {
          self.$set(map, 'paymentTermsCodeName', mapping.paymentTermsCodeName)
          delete map[item.property]
        }
        if (item.property === 'supplierDetail.purchaseMoq') {
          self.$set(map, 'purchaseMoq', mapping.purchaseMoq)
          delete map[item.property]
        }
        if (item.property === 'supplierDetail.idnlf') {
          self.$set(map, 'idnlf', mapping.idnlf)
          delete map[item.property]
        }
      }
      return map
    })
    return map
  }
  return mapping
}

export function recommendSupplierStr(list) {
  let str = ''
  const tempList = list && list.length > 0 ? [...list] : null
  tempList && tempList.sort((a, b) => a.level - b.level).forEach((item) => {
    str += `潜在供应商${item.level}：${item.factory || ''}-${item.skuNo ||
    ''}-${item.supplierNo || ''}-${item.supplierName || ''}\n`
  })
  return str
}

// 获取是否需要拆单的提示
export async function getSplitOrderTips(self, data) {
  const res = await needSplitOrder(data)
  if (res && res.code === 200) {
    if (res.data && res.data.needSplit && res.data.msg) {
      try {
        let tips = res.data.msg.replace(/\n/g, '<br/>')
        await self.$confirm(`<div class="btb-wrapper">${tips}</div>`, '自动拆单确认', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
        return Promise.resolve(res.data.needSplit)
      } catch (err) {
        console.log(err)
        return Promise.reject(err)
      }
    }
  } else {
    self.$notify.error('获取自动拆单信息失败！')
  }
}
export const formatNumToPercent = (cellValue) => {
  return cellValue >= 0 ? parseFloat((Number(cellValue) * 100).toFixed(2)) : '';
};

export const formatPercentToNum = (cellValue) => {
  return cellValue >= 0 ? parseFloat((Number(cellValue) / 100).toFixed(4)) : undefined;
};
/**
 * 将“公司”非00结尾均调整为“00”后赋值
 */
export const hackCompanyCode = (companyCode) => {
  const prefix = companyCode.slice(0, -2);
  // 获取最后两位字符
  const suffix = companyCode.slice(-2);
  // 如果最后两位不是"00"，则替换为"00"
  if (suffix !== '00') {
      return prefix + '00';
  }
  // 如果已经是"00"，则返回原字符串
  return companyCode;
}
