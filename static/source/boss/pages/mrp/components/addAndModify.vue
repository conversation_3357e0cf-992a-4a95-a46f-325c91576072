<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      :before-close="handleClose"
    >
      <el-form ref="dialogForm" :model="dialogForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="120px" :hide-required-asterisk="false">
        <el-form-item label="工厂" prop="factory" required>
          <el-select
            v-model="dialogForm.factory"
            filterable
            clearable
            style="width:100%"
            :disabled="disabling"
            @change="changeFactory(dialogForm.factory)"
          >
            <el-option
              v-for="item in factoryList"
              :key="item.factoryCode+item.factoryName"
              :label="item.factoryCode+' '+item.factoryName"
              :value="item.factoryCode">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="SKU" prop="skuNo" required>
          <SelectSku
            placeholder="单值输入，支持编码+描述模糊搜索"
            :data.sync="dialogForm.skuNo"
            :disabled="disabling"
          />
        </el-form-item>

        <el-form-item label="MRP区域" prop="mrpArea" required>
          <el-select
            clearable
            filterable
            v-model="dialogForm.mrpArea"
            placeholder="单值输入，支持编码+描述模糊搜索"
            :disabled="disabling"
            style="width:100%"
          >
            <el-option
              v-for="item in mrpAreaListFilterByFactory"
              :key="item.code+item.description"
              :label="item.code + ' ' +item.description"
              :value="item.code"
            />
          </el-select>
          <!-- <MrpCode
            placeholder="单值输入，支持编码+描述模糊搜索"
            clearable
            :data.sync="dialogForm.mrpArea"
            :describe="true"
            :disabled="disabling"
          /> -->
        </el-form-item>

        <el-form-item label="备货策略" prop="type" required>
        <el-select
          v-model.number="dialogForm.type"
          filterable
          clearable
          style="width:100%"
          @change="changeType"
          placeholder="请选择备货策略"
        >
          <el-option :value="1" label="1-安全库存"></el-option>
          <el-option :value="2" label="2-ROP补批量"></el-option>
          <el-option :value="3" label="3-ROP补到最大库存"></el-option>
          <el-option :value="4" label="4-EVM叫料"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item v-show="dialogForm.type === 1" label="安全库存" prop="safeStock">
        <el-input
          v-model.number="dialogForm.safeStock"
          filterable
          clearable
          style="width:100%"
          placeholder="单值输入"
        />
      </el-form-item>

      <el-form-item v-show="[2, 3, 4].includes(dialogForm.type)" label="再订货点" prop="reOrderPoint">
        <el-input
          v-model.number="dialogForm.reOrderPoint"
          filterable
          clearable
          style="width:100%"
          placeholder="单值输入"
        />
      </el-form-item>

      <el-form-item v-show="[2, 4].includes(dialogForm.type)" label="舍入值" prop="packagingQuantity">
        <el-input
          v-model.number="dialogForm.packagingQuantity"
          filterable
          clearable
          style="width:100%"
          placeholder="单值输入"
        />
      </el-form-item>

      <el-form-item v-show="dialogForm.type === 3" label="最大库存" prop="maxStock">
        <el-input
          v-model.number="dialogForm.maxStock"
          filterable
          clearable
          style="width:100%"
          placeholder="单值输入"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="dialogForm.remark"
          filterable
          clearable
          style="width:100%"
        />
      </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addOrModify">保 存</el-button>
        <el-button type="info"  @click="callOff">取 消</el-button>
      </span>
    </el-dialog>
</template>

<script>
import SelectSku from './selectSku'
// import MrpCode from './mrpCode'
import { createStock, updateStock } from '@/api/mrp'

export default {
  name: 'addAndModify',
  components: {
    SelectSku
    // MrpCode
  },
  data () {
    return {
      rules: {
        factory: [
          { required: true, message: '工厂不能为空！', trigger: ['change', 'blur'] }
        ],
        skuNo: [
          { required: true, message: 'SKU不能为空！', trigger: ['change', 'blur'] }
        ],
        mrpArea: [
          { required: true, message: 'MRP区域不能为空！', trigger: ['change', 'blur'] }
        ],
        type: [
          { required: true, message: '备货策略不能为空！', trigger: ['change', 'blur'] }
        ],
        safeStock: [
          { required: false }
        ],
        reOrderPoint: [
          { required: false }
        ],
        packagingQuantity: [
          { required: false }
        ],
        maxStock: [
          { required: false }
        ]
      }
    }
  },
  props: {
    dialogTitle: {
      type: String,
      default: '标题'
    },
    visible: {
      type: Boolean,
      default: false
    },
    disabling: {
      type: Boolean,
      default: false
    },
    factoryList: {
      type: Array
    },
    getStockListData: {
      type: Function
    },
    dialogForm: {
      type: Object,
      default: () => ({})
    },
    mrpAreaList: {
      type: Array
    },
    mrpAreaListFilterByFactory: {
      type: Array
    }
  },
  computed: {
    dialogVisible: {
      get () {
        return this.visible
      },
      set (val) {
        this.$emit('updateVisible', val)
      }
    }
  },
  methods: {
    changeSku (type, event) {
      if (type === 'sku') {
        this.dialogForm.skuNo = event
      }
    },
    changeMrpCode2 (type, event) {
      if (type === 'mrpAreaObj') {
        this.dialogForm.mrpArea = event
      }
    },
    changeFactory (val) {
      this.$emit('mrpAreaListFilter', val)
      this.$emit('updateDialogForm', 'mrpArea')
    },
    changeType () {
      if (this.dialogForm.type === 1) {
        this.rules.safeStock = [
          { required: true, message: '安全库存不能为空！', trigger: ['change', 'blur'] }
        ]
        this.rules.reOrderPoint = [
          { required: false }
        ]
        this.rules.packagingQuantity = [
          { required: false }
        ]
        this.rules.maxStock = [
          { required: false }
        ]
        this.$emit('updateDialogForm', 'reOrderPoint')
        this.$emit('updateDialogForm', 'packagingQuantity')
        this.$emit('updateDialogForm', 'maxStock')
      } else if ([2, 4].includes(this.dialogForm.type)) {
        this.rules.reOrderPoint = [
          { required: true, message: '再订货点不能为空！', trigger: ['change', 'blur'] }
        ]
        this.rules.packagingQuantity = [
          { required: true, message: '舍入值不能为空！', trigger: ['change', 'blur'] }
        ]
        this.rules.safeStock = [
          { required: false }
        ]
        this.rules.maxStock = [
          { required: false }
        ]
        this.$emit('updateDialogForm', 'safeStock')
        this.$emit('updateDialogForm', 'maxStock')
      } else if (this.dialogForm.type === 3) {
        this.rules.reOrderPoint = [
          { required: true, message: '再订货点不能为空！', trigger: ['change', 'blur'] }
        ]
        this.rules.maxStock = [
          { required: true, message: '最大库存不能为空！', trigger: ['change', 'blur'] }
        ]
        this.rules.safeStock = [
          { required: false }
        ]
        this.rules.packagingQuantity = [
          { required: false }
        ]
        this.$emit('updateDialogForm', 'safeStock')
        this.$emit('updateDialogForm', 'packagingQuantity')
      }
    },
    addOrModify () {
      if (this.dialogTitle === '新增策略参数配置') {
        this.$refs.dialogForm.validate((valid) => {
          if (valid) {
            this.createStockStrategy()
            this.$emit('submitPopupData')
          } else {
            return false;
          }
        })
      }
      if (this.dialogTitle === '修改') {
        this.$refs.dialogForm.validate((valid) => {
          if (valid) {
            this.updateStockStrategy()
            this.$emit('submitPopupData')
          } else {
            return false;
          }
        })
      }
    },
    async updateStockStrategy () {
      try {
        // delete this.dialogForm.sku
        // delete this.dialogForm.mrpAreaObj
        if (this.dialogForm.maxStock === '') {
          this.$emit('emptyToZero', 'maxStock')
        }
        if (this.dialogForm.packagingQuantity === '') {
          this.$emit('emptyToZero', 'packagingQuantity')
        }
        if (this.dialogForm.reOrderPoint === '') {
          this.$emit('emptyToZero', 'reOrderPoint')
        }
        if (this.dialogForm.safeStock === '') {
          this.$emit('emptyToZero', 'safeStock')
        }
        const res = await updateStock(this.dialogForm)
        if (res.code === 200) {
          this.$refs.dialogForm.resetFields()
          this.$message.success(res.msg)
          this.getStockListData()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        console.log(error);
      }
    },
    async createStockStrategy () {
      try {
        // delete this.dialogForm.sku
        // delete this.dialogForm.mrpAreaObj
        const res = await createStock(this.dialogForm)
        if (res.code === 200) {
          this.$refs.dialogForm.resetFields()
          this.$message.success(res.msg)
          this.getStockListData()
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        console.log(error);
      }
    },
    callOff () {
      // delete this.dialogForm.sku
      // delete this.dialogForm.mrpAreaObj
      this.$refs.dialogForm.resetFields()
      this.$emit('submitPopupData')
    },
    handleClose () {
      // delete this.dialogForm.sku
      // delete this.dialogForm.mrpAreaObj
      this.$refs.dialogForm.resetFields()
      this.$emit('submitPopupData')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
