<template>
  <el-select
    v-model="product"
    filterable
    remote
    clearable
    multiple
    reserve-keyword
    style="width:100%"
    value-key="productGroup"
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteProductGroupIdMethod"
    :loading="productGroupIdLoading"
  >
    <el-option
      v-for="item in productGroupIdOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value"></el-option>
  </el-select>
</template>
<script>
import {
  getMaterialGroupList
} from '@/components/SearchFields/api.js'
export default {
  name: 'remoteMaterialGroupComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    data: [Object, String, Array],
    getLabel: Boolean
  },
  data () {
    return {
      productGroupIdOptions: [],
      productGroupIdLoading: false
    }
  },
  computed: {
    product: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      this.$emit('change', val)
    },
    // 远程查找物料组
    remoteProductGroupIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.productGroupIdLoading = true
        getMaterialGroupList({
          group_name: key
        }).then(res => {
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.productGroupIdOptions = res.data.contents.map(item => {
                return {
                  value: item.productGroupNum,
                  label: item.productGroupName
                }
              })
            } else {
              this.productGroupIdOptions = []
            }
          } else {
            this.productGroupIdOptions = []
          }
        }).finally(() => {
          this.productGroupIdLoading = false
        })
      } else {
        this.productGroupIdOptions = []
      }
    }
  }
}
</script>
