<template>
<el-select
  ref="brands"
  v-model="brands"
  placeholder="请选择品牌"
  filterable
  default-first-option
  remote
  :multiple="multiple"
  reserve-keyword
  style="width:100%"
  value-key="brandId"
  :disabled="disabled"
  :clearable="clearable"
  :remote-method="remoteMethod"
  :loading="loading"
  @change="handleChange"
>
  <el-option
    v-for="(item, index) in brandList"
    :key="item.brandId"
    :label="item.brandName"
    :value="item"
    :disabled="index===0"
  >
    <div
      class="selectItem"
      :style="{fontWeight:index===0?'bold':'normal'}"
    >
      <div>{{ item.brandId || '--' }}</div>
      <div>{{ item.brandName || '--' }}</div>
    </div>
  </el-option>
</el-select>
</template>

<script>
import { getBrand } from '@/api/mrp';
export default {
  props: {
    data: [Object, String, Array],
    disabled: Boolean,
    defaultValue: <PERSON>olean,
    clearable: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      brandList: []
    }
  },
  created () {
    if (this.defaultValue) {
      this.brandList = [
        {
          brandId: '品牌id',
          brandName: '品牌名称'
        },
        this.data
      ]
    }
  },
  computed: {
    brands: {
      get () {
        window._console.blue(this.data)
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    remoteMethod (val) {
      if (val) {
        this.loading = true
        getBrand({
          brandName: val
        }).then(data => {
          this.loading = false
          this.brandList = [
            {
              brandId: '品牌id',
              brandName: '品牌名称'
            },
            ...data
          ]
        })
      } else {
        this.brandList = [
          {
            brandId: '品牌id',
            brandName: '品牌名称'
          }
        ]
      }
    },
    handleChange (val) {
      this.$emit('change', val)
    }
  }
}
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
  }
}
</style>
