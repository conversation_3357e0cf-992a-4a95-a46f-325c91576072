<template>
  <el-select ref="mrpArea"
             v-model="mrpAreaObj"
             :placeholder="placeholder"
             filterable
             remote
             reserve-keyword
             style="width:100%"
             value-key="mrpArea"
             :multiple="multiple"
             :disabled="disabled"
             :clearable="clearable"
             :remote-method="remoteMethod"
             :loading="loading"
             @change="handleChange">
    <el-option v-for="(item, index) in mrpAreaList"
               :key="item.id"
               :label="item.code +' '+ item.description"
               :value="item.code"
               :disabled="index===0">
      <div class="selectItem"
           :style="{fontWeight:index===0?'bold':'normal'}">
        <!-- <div>{{ item.id || '--' }}</div> -->
        <div>{{ item.code || '--' }}</div>
        <div v-if="describe">{{item.description || '--'}}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { getMrpArea } from '@/api/mrp';
export default {
  props: {
    data: [Object, String, Array],
    disabled: Boolean,
    defaultValue: Boolean,
    clearable: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: 'MRP区域编码'
    },
    describe: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      loading: false,
      mrpAreaList: []
    }
  },
  created () {
    if (this.defaultValue) {
      this.mrpAreaList = [
        {
          // id: 'MRP区域id',
          code: 'MRP区域编码',
          description: 'MRP描述'
        },
        this.data
      ]
    }
  },
  computed: {
    mrpAreaObj: {
      get () {
        window._console.blue(this.data)
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    remoteMethod (val) {
      if (val) {
        this.loading = true
        getMrpArea({
          mrpArea: val
        }).then(data => {
          this.loading = false
          let result = []
          let obj = {}
          for (let i = 0; i < data.length; i++) {
            if (!obj[data[i].code]) {
              result.push(data[i])
              obj[data[i].code] = true
            }
          }
          this.mrpAreaList = [
            {
              // id: 'MRP区域id',
              code: 'MRP区域编码',
              description: 'MRP描述'
            },
            ...result
          ]
        })
      } else {
        this.mrpAreaList = [
          {
            //   id: 'MRP区域id',
            code: 'MRP区域编码',
            description: 'MRP描述'
          }
        ]
      }
    },
    handleChange (val) {
      this.$emit('change', val)
    }
  }
}
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
  }
}
</style>
