<template>
  <el-select
    v-model="supplier"
    placeholder="选择供应商"
    filterable
    default-first-option
    remote
    reserve-keyword
    style="width:100%"
    :disabled="disabled"
    :remote-method="remoteMethod"
    :loading="loading"
    @change="handleChange"
  >
    <el-option
      v-for="(item, index) in supplierList"
      :key="item.providerNo"
      :label="item.providerName"
      :value="item"
      :disabled="index===0"
    >
      <div
        class="selectItem"
        :style="{fontWeight:index===0?'bold':'normal'}"
      >
        <div>{{ item.providerNo || '--' }}</div>
        <div>{{ item.providerName || '--' }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { getSkuSupplier } from '@/api/mrp'
export default {
  props: {
    list: {
      type: Array
    },
    data: {
      type: String
    },
    disabled: {
      type: Boolean,
      default: false
    },
    factory: {
      type: String
    },
    skuNo: {
      type: String
    }
  },
  data() {
    return {
      loading: false,
      supplierList: this.list || []
    }
  },
  computed: {
    supplier: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    remoteMethod (val) {
      if (val) {
        this.loading = true

        // 主数据目前没有维护工厂 '2701' '2702' '2703'
        let factory = this.factory
        if (factory === '2701' || factory === '2702' || factory === '2703') {
          factory = '2700'
        }

        getSkuSupplier({
          factoryCodes: factory,
          skuCodes: this.skuNo,
          pageNum: 1,
          pageSize: 500
        }).then(res => {
          this.loading = false
          if (res && res.data) {
            const temp = res.data.map(item => ({
              providerNo: item.supplierCode,
              providerName: item.supplierName.split(' ')[1],
              supplierName: item.supplierName
            })).filter((item) => item.supplierName.includes(val))
            this.supplierList = [
              {
                providerNo: '供应商编码',
                providerName: '供应商名称'
              },
              ...temp
            ]
          }
        })
      } else {
        this.supplierList = [
          {
            providerNo: '供应商编码',
            providerName: '供应商名称'
          }
        ]
      }
    },
    handleChange (val) {
      this.$emit('change', val, this.supplierList)
    }
  }
}
</script>

<style scoped>
.selectItem {
  display: flex;
}
.selectItem div:nth-child(1) {
  width: 100px;
}
.selectItem div:nth-child(2) {
  overflow: auto;
  width: 220px;
}
</style>
