<template>
  <el-select
    ref="purchase"
    v-model="purchaseGroup"
    value-key="purchaseGroup"
    allow-create
    filterable
    clearable
    :multiple="multiple"
    collapse-tags
    default-first-option
    style="width:100%"
    placeholder="请选择采购员"
    @change="handleChange"
  >
    <el-option
      v-for="item in purchaseList"
      :key="item.groupCode"
      :label="item.groupCode + ' ' + item.userName"
      :value="item.groupCode"
      :disabled="item.disabled"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getPurchaseGroup, getChildrenPurchaseGroup } from '@/api/mrp';
import { mapState } from 'vuex';

export default {
  props: {
    data: [Array, String],
    multiple: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      purchaseList: [],
      userPurchaseList: []
    };
  },
  computed: {
    ...mapState({
      email: (state) => state.user.email
    }),
    purchaseGroup: {
      get() {
        window._console.blue(this.data);
        return this.data;
      },
      set(val) {
        this.$emit('update:data', val);
      }
    }
  },
  async created() {
    if (!this.email || this.email.length === 0) {
      await this.$store.dispatch('getUserRole');
    }
    await this.handelPurchaseGroup();
    this.$emit('getUserPurchaseGroup', this.userPurchaseList);
    this.$emit('filterPurchaseList', this.purchaseList);
  },
  methods: {
    async handelPurchaseGroup() {
      try {
        const result = await Promise.all([
          getPurchaseGroup({
            securityUsernameList:
              window.CUR_DATA.user && window.CUR_DATA.user.name
          }),
          getChildrenPurchaseGroup({
            email: this.email
          })
        ]);
        const [result1 = {}, result2 = {}] = result;
        this.userPurchaseList = [...result1.data];
        this.purchaseList = [...result2.data];
        if (this.userPurchaseList.length === 1 && this.multiple) {
          this.purchaseGroup = this.userPurchaseList.map(
            (item) => item.groupCode
          );
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleChange(val) {
      this.$emit('change', val);
    }
  }
};
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    overflow: auto;
    width: 220px;
  }
}
</style>
