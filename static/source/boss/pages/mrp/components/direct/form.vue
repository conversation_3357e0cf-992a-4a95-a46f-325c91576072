<template>
  <el-form
    ref="ruleForm"
    :rules="rules"
    :model="searchForm"
    style="width: 100%"
    label-suffix=":"
    label-width="120px"
    v-show="hideResult"
  >
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="公司" prop="company" required>
          <el-select
            v-model="searchForm.company"
            filterable
            default-first-option
            clearable
            style="width: 100%"
            placeholder="请选择公司"
          >
            <el-option
              v-for="item in companyFactoryList"
              :key="item.companyCode"
              :label="item.companyCode + ' ' + item.companyName"
              :value="item.companyCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="工厂" prop="factory" required>
          <el-select
            v-model="searchForm.factory"
            filterable
            default-first-option
            clearable
            style="width: 100%"
            placeholder="请选择工厂"
          >
            <el-option
              v-for="item in factoryList"
              :key="item.factoryCode"
              :label="item.factoryCode + ' ' + item.factoryName"
              :value="item.factoryCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="采购员" prop="purchaseGroup" required>
          <el-select
            v-if="setPower(userRole)"
            v-model="searchForm.purchaseGroup"
            allow-create
            filterable
            collapse-tags
            default-first-option
            clearable
            multiple
            style="width: 100%"
            placeholder="请选择采购员"
          >
            <el-option
              v-for="item in purchaseList"
              :key="item.groupCode + item.userName"
              :label="item.groupCode + ' ' + item.userName"
              :value="item.groupCode"
            >
            </el-option>
          </el-select>
          <SelectPurchaseGroup
            v-else
            :data.sync="searchForm.purchaseGroup"
            v-on="$listeners"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="VPI物料" prop="isVpi">
          <el-select
            v-model="searchForm.isVpi"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="1" label="是" :value="1" />
            <el-option :key="0" label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="SKU" prop="skuNo">
          <el-input
            v-model="searchForm.skuNo"
            filterable
            clearable
            type="textarea"
            :rows="1"
            style="width: 100%"
            placeholder="最多支持100个SKU按空格隔开搜索"
          />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="物料组" prop="productGroup">
          <el-select
            v-model="searchForm.productGroup"
            filterable
            default-first-option
            clearable
            multiple
            collapse-tags
            style="width: 100%"
            placeholder="最多支持10个物料组按空格隔开搜索"
          >
            <el-option
              v-for="item in productList"
              :key="item.productGroupNum + Math.random()"
              :label="item.productGroupNum + ' ' + item.productGroupName"
              :value="item.productGroupNum"
            >
            </el-option>
          </el-select>
          <!-- <el-input v-model="searchForm.productGroup"
                      filterable
                      clearable /> -->
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="品牌" prop="brandId">
          <SelectBrand
            clearable
            style="width:100%"
            :data.sync="searchForm.brands"
            @change="handleChange"
            />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="SO创建日期" prop="createTime">
          <el-date-picker
            v-model="searchForm.createTime"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
            style="width: 100%"
          >
          </el-date-picker>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="销售订单号" prop="soNo">
          <el-input v-model="searchForm.soNo" placeholder="支持多个按空格隔开搜索" type="textarea" :rows="1" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="销售订单行号" prop="soItemNo">
          <el-input :disabled="searchForm.soNo ? false : true" :placeholder="searchForm.soNo ? '请输入' : '请先输入订单号'" v-model="searchForm.soItemNo" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="专料专用" prop="isPrivateMaterial">
          <el-select
            v-model="searchForm.isPrivateMaterial"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="1" label="是" :value="1" />
            <el-option :key="0" label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="供应商缺货" prop="outOfStock">
          <el-select
            v-model="searchForm.outOfStock"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="-1" label="全部" :value="2" />
            <el-option :key="1" label="是" :value="1" />
            <el-option :key="0" label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="售完即止" prop="mstaeState">
          <el-select
            v-model="searchForm.mstaeState"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="-1" label="全部" :value="2" />
            <el-option :key="1" label="是" :value="1" />
            <el-option :key="0" label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="下单异常" prop="isOrderException">
          <el-select
            v-model="searchForm.isOrderException"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="-1" label="全部" :value="2" />
            <el-option :key="1" label="是" :value="1" />
            <el-option :key="0" label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label="紧急订单" prop="isUrgent">
          <el-select
            v-model="searchForm.isUrgent"
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="0" label="全部" :value="0" />
            <el-option :key="1" label="有" :value="1" />
            <el-option :key="2" label="无" :value="2" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item :style="{ marginLeft: '64px' }" label-width="0px">
          <el-switch
            v-model="reportType"
            active-text="可用库存"
            inactive-text="负需求"
            :inactive-value="false"
            :active-value="true"
            @change="switchTable"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item label="低负毛利" prop="ifGrossMargin">
          <el-select
            v-model="searchForm.ifGrossMargin"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="1" label="是" :value="true" />
            <el-option :key="0" label="否" :value="false" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0px" style="padding-left: 120px;">
          <el-checkbox
            v-model="searchForm.isBufferLimit"
            :true-label="1"
            :false-label="0"
            style="line-height: 30px">
            仅显示Buffer内负需求
          </el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :span="6" :style="{ display: 'flex', justifyContent: 'center' }">
        <div>
          <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="validateSearch"
          >
            查询
          </el-button>
          <a href="https://doc.weixin.qq.com/doc/w3_ANQAwQYoADY6K0M30odTQuPCcObO0?scode=AAcAtAcPAAkXzrVdhBAKIA2gYlADY" class="link-a" target="_blank">查询不到负需求？</a>
        </div>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import SelectPurchaseGroup from '../selectPurchaseGroup.vue'
import SelectBrand from '../selectBrand.vue'
import { getUserInfo, getPurchaseGroup } from '@/api/mrp'
import { setPower, hackCompanyCode } from '../../utils'

export default {
  name: 'directForm',
  props: {
    hideResult: Boolean,
    purchaseList: Array,
    searchLoading: Boolean,
    userRole: Array,
    soParams: Object,
    initHistoryMounted: Function

  },
  components: {
    SelectPurchaseGroup,
    SelectBrand
  },
  data() {
    return {
      factoryList: [],
      searchForm: {
        company: '',
        factory: '',
        productGroup: [],
        skuNo: '',
        createTime: [],
        soNo: '',
        isVpi: 0,
        purchaseGroup: [],
        isUrgent: '',
        brandId: '',
        brands: {},
        isOrderException: '',
        soItemNo: '',
        isBufferLimit: 1,
        ifGrossMargin: ''
      },
      reportType: false,
      rules: {
        company: [
          { required: true, message: '公司不能为空！', trigger: ['change', 'blur'] }
        ],
        factory: [
          { required: true, message: '工厂不能为空！', trigger: ['change', 'blur'] }
        ],
        purchaseGroup: [
          { required: true, message: '采购员不能为空！', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      productList: state => state.mrp.productList,
      companyInfoList: state => state.mrp.companyInfoList
    })
  },
  watch: {
    'searchForm.company': function (newValue) {
      this.factoryList = (this.companyFactoryList.find(item => item.companyCode === newValue) || {}).factoryList || []
      this.searchForm.factory = ''
    },
    factoryList: {
      handler: function (newValue) {
        if (newValue.length > 0) {
          this.searchForm.factory = this.factory || this.factoryList[0].factoryCode
        }
      },
      deep: true,
      immediate: true
    },
    'soParams': function (newVal) {
      this.searchForm.skuNo = newVal?.sku ? newVal.sku.replace(/,/g, ' ') : this.searchForm.skuNo
      this.searchForm.isUrgent = newVal?.isUrgent && Number(newVal?.isUrgent) >= 0 ? Number(newVal.isUrgent) : this.searchForm.isUrgent
      this.searchForm.isVpi = newVal?.isVpi ? '' : 0
    },
    'searchForm.soNo': function (newValue) {
      if (!newValue) {
        this.searchForm.soItemNo = ''
      }
    }
  },
  async created() {
    const pList = []
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (this.productList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryProductGroup'))
    }
    if (this.companyInfoList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryCompanyInfoList'))
    }
    await Promise.all(pList)
    const query = JSON.parse(localStorage.getItem('/mrp/direct')) || {}
    // 使用初始查询条件查询一次后需要清除该初始条件
    // setItem 参见 router/index.js > beforeEach
    localStorage.removeItem('/mrp/direct')
    // 获取URL查询参数
    const {
      company,
      factory,
      purchaseGroup,
      soNo,
      soitemNo,
      skuNo,
      ifGrossMargin,
      isVpi = '',
      isPrivateMaterial = '',
      isUrgent = '',
      isBufferLimit = ''
    } = query
    if (company) {
      this.searchForm.company = this.filterCompanyByFactory(factory)
      this.searchForm.factory = factory
      this.searchForm.purchaseGroup = [purchaseGroup]
      this.searchForm.skuNo = skuNo
      this.searchForm.soNo = soNo
      this.searchForm.soItemNo = soitemNo
      this.searchForm.ifGrossMargin = ifGrossMargin ? ifGrossMargin === 'true' : ''
      this.searchForm.isVpi = isVpi ? Number(isVpi) : ''
      this.searchForm.isPrivateMaterial = isPrivateMaterial ? Number(isPrivateMaterial) : ''
      this.searchForm.approvalEndDate = ''
      this.searchForm.isUrgent = isUrgent ? Number(isUrgent) : ''
      this.searchForm.isBufferLimit = isBufferLimit ? Number(isBufferLimit) : ''
      this.validateSearch()
      // 初始化进入页面存下factory，防止watch factoryList时facotry被清空；1秒后清空，防止切换选项还是取存下的值
      this.factory = factory
      setTimeout(() => {
        this.factory = ''
      }, 1000)
    } else {
      if (this.setPower(this.userRole)) {
        this.userPurchaseGroup()
      }
      this.getUserCompany()
    }
  },
  methods: {
    setPower,
    async getUserCompany() {
      try {
        const res = await getUserInfo({
          username: window.CUR_DATA.user && window.CUR_DATA.user.name
        })
        let defaultCompany = (this.companyInfoList.find(item => item.subCompanyId === res.subCompany) || {}).code
        if (defaultCompany) {
          this.searchForm.company = defaultCompany
        }
      } catch (error) {
        console.log(error)
      }
    },
    async userPurchaseGroup() {
      const res = await getPurchaseGroup({
        securityUsernameList: window.CUR_DATA.user && window.CUR_DATA.user.name
      })
      this.searchForm.purchaseGroup = res.data.map(item => item.groupCode)
    },
    handleChange(event) {
      this.searchForm.brandId = event.brandId
    },
    filterCompanyByFactory(factoryCode) {
      return hackCompanyCode(this.companyFactoryList.find(item => {
        const factoryList = item.factoryList.map(item => item.factoryCode)
        return factoryList.includes(factoryCode)
      })?.companyCode || '')
    },
    validateSearch() {
      const data = { ...this.searchForm }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('handleSearch', data)
        } else {
          return false
        }
      })
    },
    switchTable() {
      this.$emit('getReportType', this.reportType)
      this.initHistoryMounted()
    }
  }
}
</script>

<style lang="scss" scope>
  .link-a {
    padding-left: 10px;
    color: #489ad0;
    font-size: 14px;
  }
</style>
