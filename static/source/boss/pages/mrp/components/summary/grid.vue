<template>
  <vxe-grid
    column-key
    border
    resizable
    auto-resize
    keep-source
    show-overflow
    show-header-overflow
    ref="summaryGrid"
    :height="tableHeight"
    id="mrp-summary_grid"
    size="mini"
    row-id="id"
    align="center"
    :loading="tableLoading"
    :custom-config="tableCustom"
    :data="listData"
    :columns="columns"
    :toolbar-config="tableToolbar"
    :edit-config="{ trigger: 'click', mode: 'cell', showStatus: true }"
    @checkbox-all="selectAll"
    @checkbox-change="selectChange"
    @cell-dblclick="cellClick"
    highlight-hover-row
    :scroll-y="{ gt: 20 }"
    @scroll="scroll"
    :row-class-name="genRowColor"
  >
    <template v-slot:toolbar_tools>
      <el-button
        v-if="canTransfer"
        type="primary"
        style="width: 80px"
        :disabled="disabled"
        @click="handleTransfer"
      >
        转单
      </el-button>
      <el-button
        type="primary"
        style="width: 80px; margin-right: 10px"
        :disabled="listData.length === 0"
        @click="handleDownExcel"
      >
        下载
      </el-button>
    </template>

    <template v-slot:skuNo_default="{ row }">
      <el-tooltip effect="light" v-if="row.leadTime === 0">
        <div slot="content">
          <div v-if="row.leadTime === 0">此商品未获取期货LT;</div>
        </div>
        <i
          class="el-icon-warning"
          :style="{ marginRight: '5px', color: 'red' }"
        ></i>
      </el-tooltip>
      <span class="is-vpi" v-if="row.prType === 1">
        <img src="../../../../assets/images/v.png" class="product-vpi-icon" />
      </span>
      <span class="is-vpi span" v-if="row.prType === 2">指</span>
      <span class="is-vpi span" v-if="row.prType === 3">计</span>
      <span class="is-vpi span" v-if="row.prTag && row.prTag.isUrgent === 1">急</span>
      <span class="is-vpi span" v-if="row.prTag && row.prTag.isSpecialOrder === 1">专</span>
      <span>{{ row.skuNo }}</span>
    </template>

    <template v-slot:productGroup_default="{ row }">
      {{
        (row.productVO.productGroup || '') +
        ' ' +
        (row.productVO.productGroupName || '')
      }}
    </template>

    <template v-slot:mrpArea_default="{ row }">
      {{ row.mrpArea + ' ' + row.mrpAreaDesc }}
    </template>

    <template v-slot:purchaseGroup_default="{ row }">
      {{
        row.purchaseGroup +
        ' ' +
        (
          purchaseList.find((item) => item.groupCode === row.purchaseGroup) ||
          {}
        ).userName
      }}
    </template>

    <template v-slot:mstaeAllSwitch_default="{ row }">
      {{ row.productVO.mstaeAllSwitch === 1 ? '是' : '否' }}
    </template>

    <template v-slot:productPositioningName_default="{ row }">
      {{ row.productVO.productPositioningName }}
    </template>

    <template v-slot:isUrgent_default="{ row }">
      <el-popover
        v-if="row.isUrgent !== 0"
        placement="top"
        trigger="hover"
        popper-class="popover"
        @show="handleMsg(row.urgentMsg)"
      >
        <span v-if="msgLoading">加载中……</span>
        <el-table :data="urgentMsg" v-else border>
          <el-table-column
            property="soNo"
            label="销售订单号/销售订单行"
            width="180"
          ></el-table-column>
          <el-table-column property="number" label="数量"></el-table-column>
          <el-table-column label="定制说明">
            <span v-html="row.customInstructions"></span>
          </el-table-column>
          <el-table-column
            property="remark"
            label="特殊订单类型"
            width="150"
          ></el-table-column>
          <el-table-column property="customer" label="客服"></el-table-column>
        </el-table>
        <el-button
          slot="reference"
          type="text"
          :style="{ border: 'none', color: 'black' }"
        >
          {{ row.isUrgent !== 0 ? '有' : '' }}
        </el-button>
      </el-popover>
    </template>

    <template v-slot:isSpec_default="{ row }">
      <el-popover
        v-if="row.isSpec === 1"
        placement="top"
        trigger="hover"
        popper-class="popover"
        @show="handleMsg(row.specMsg)"
      >
        <span v-if="msgLoading">加载中……</span>
        <el-table :data="specMsg" v-else border>
          <el-table-column
            property="soNo"
            label="销售订单号/销售订单行"
            width="180"
          ></el-table-column>
          <el-table-column property="number" label="数量"></el-table-column>
          <el-table-column
            property="remark"
            label="销售订单行备注内容"
            width="150"
          ></el-table-column>
          <el-table-column property="customer" label="客服"></el-table-column>
        </el-table>
        <el-button
          slot="reference"
          type="text"
          :style="{ border: 'none', color: 'black' }"
        >
          {{ row.isSpec === 1 ? '有' : '' }}
        </el-button>
      </el-popover>
    </template>

    <template v-slot:quantity_default="{ row }">
      <el-popover placement="top" width="800" trigger="click">
        <el-table :data="row.prList" border>
          <el-table-column property="factory" label="工厂"></el-table-column>
          <el-table-column property="skuNo" label="SKU"></el-table-column>
          <el-table-column property="mrpArea" label="MRP区域"></el-table-column>
          <el-table-column property="prNo" label="采购申请"></el-table-column>
          <el-table-column
            property="prItemNo"
            label="采购申请行"
          ></el-table-column>
          <el-table-column property="quantity" label="数量"></el-table-column>
          <el-table-column property="quantityUnit" label="单位">
            <template slot-scope="{ row }">
              <span>{{ formatQuantityUnit(row.quantityUnit) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            property="approvalDate"
            label="建议订货日期"
          ></el-table-column>
        </el-table>
        <el-button type="text" slot="reference">{{ row.quantity }}</el-button>
      </el-popover>
    </template>

    <template v-slot:quantityUnit_default="{ row }">
      {{ formatQuantityUnit(row.quantityUnit) }}
    </template>
    <template v-slot:totalWeight_default="{ row }">
      {{ row.totalWeight || (row.productVO.productGroup === 430 ? '未维护单重':'') }}
    </template>
    <template v-slot:cloudWareHouse_default="{ row }">
      {{ row.cloudWareHouse || (row.productVO.productGroup === 430 ? '无结果':'') }}
    </template>
    <template v-slot:abutmentStock_default="{ row }">
      {{ row.abutmentStock ||(row.productVO.productGroup === 430 ? '无结果':'') }}
    </template>
    <template v-slot:specialDemandQuantity_default="{ row }">
      {{ -row.specialDemandQuantity }}
    </template>

    <template v-slot:selfInventory1_default="{ row }">
      {{
        getNameAddQuantity(
          row.inventoryVo.selfInventory1,
          row.inventoryVo.selfInventory1Quantity
        )
      }}
    </template>

    <template v-slot:selfInventory2_default="{ row }">
      {{
        getNameAddQuantity(
          row.inventoryVo.selfInventory2,
          row.inventoryVo.selfInventory2Quantity
        )
      }}
    </template>

    <template v-slot:promotionInventoryQuantity_default="{ row }">
      {{
        getNameAddQuantity(
          row.inventoryVo.promotionInventory,
          row.inventoryVo.promotionInventoryQuantity
        )
      }}
    </template>

    <template v-slot:otherInventory1_default="{ row }">
      {{
        row.inventoryVo.otherInventory1Quantity > 0
          ? row.inventoryVo.otherInventory1Quantity +
          '/' +
          row.inventoryVo.otherInventory1 +
          '/' +
          row.inventoryVo.otherInventory1Name
          : ''
      }}
    </template>

    <template v-slot:otherTransportationInventory1_default="{ row }">
      {{
        row.inventoryVo.otherTransportationInventory1Quantity > 0
          ? row.inventoryVo.otherTransportationInventory1Quantity +
          '/' +
          row.inventoryVo.otherTransportationInventory1 +
          '/' +
          row.inventoryVo.otherTransportationInventory1Name
          : ''
      }}
    </template>

    <template v-slot:hignStorageInStock1MrpArea_default="{ row }">
      {{
        row.inventoryVo.hignStorageInStock1Quantity > 0
          ? row.inventoryVo.hignStorageInStock1Quantity +
          '/' +
          row.inventoryVo.hignStorageInStock1MrpArea +
          '/' +
          row.inventoryVo.hignStorageInStock1MrpAreaName
          : ''
      }}
    </template>

    <template v-slot:hignStorageInStock2MrpArea_default="{ row }">
      {{
        row.inventoryVo.hignStorageInStock2Quantity > 0
          ? row.inventoryVo.hignStorageInStock2Quantity +
          '/' +
          row.inventoryVo.hignStorageInStock2MrpArea +
          '/' +
          row.inventoryVo.hignStorageInStock2MrpAreaName
          : ''
      }}
    </template>
    <template v-slot:bomItemInventory_default="{ row }">
      {{ getBomItemInventoryVoList(row) }}
    </template>
    <template v-slot:purchasePriceVOList0="{ row }">
      <el-popover
        v-if="
          isVpi(row)
            ? row.customerSpecifiedPurchasePriceVO.providerNo
            : row.supplier1Detail.providerNo
        "
        placement="top"
        width="800"
        trigger="hover"
        popper-class="align"
      >
        <el-row>
          <el-col :span="8">
            综合得分：{{ row.supplier1Detail.totalScore }}
          </el-col>
          <el-col :span="4">
            执行建议：{{ getGrade(row.supplier1Detail.grade) }}
          </el-col>
          <el-col :span="6" style="display: flex">
            风险提示：
            <el-tooltip
              class="item"
              effect="dark"
              :content="row.supplier1Detail.riskWarn"
              placement="top-start"
            >
              <span
                style="
                  width: 100px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis; ;
                "
              >{{ row.supplier1Detail.riskWarn }}</span
              >
            </el-tooltip>
          </el-col>
          <el-col :span="6">
            策略类型：{{ row.supplier1Detail.tacticsName }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="display: flex">
            策略详情：
            <el-tooltip
              class="item"
              effect="dark"
              :content="row.supplier1Detail.tacticsDetail"
              placement="top-start"
            >
              <span
                style="
                  width: 400px;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis; ;
                "
              >{{ row.supplier1Detail.tacticsDetail }}</span
              >
            </el-tooltip>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            是否代理品牌：{{ row.supplier1Detail.ifAgency === 0 ? '否' : row.supplier1Detail.ifAgency === 1 ? '是' : '' }}
          </el-col>
          <el-col :span="8" style="display: flex">
            是否原厂品牌：
            {{ row.supplier1Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(row.supplier1Detail.ifTradeCertificate) ? '是' : '' }}
          </el-col>
          <el-col :span="8" style="display: flex">
            MPQ：
            {{ row.supplier1Detail.purchaseMpq || '' }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            默认运营路线：{{ esokzName(row.supplier1Detail.esokz) }}
          </el-col>
          <el-col :span="4">
            币种：{{
              getCurrency(row.supplier1Detail.currency, row.supplier1Detail)
            }}
          </el-col>
          <el-col :span="6">
            进项税：{{
              isVpi(row)
                ? row.customerSpecifiedPurchasePriceVO.taxRate
                : row.supplier1Detail.taxRate
            }}
            {{ '%' || '' }}
          </el-col>
          <el-col :span="6">
            订单单位：{{ formatQuantityUnit(row.supplier1Detail.meins) }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            最小起订单：{{ row.supplier1Detail.purchaseMoq || '' }}
          </el-col>
          <el-col :span="4">
            招投标：{{ getIfBid(row.supplier1Detail.ifBid) }}
          </el-col>
          <el-col :span="6">
            含运费：{{
              row.supplier1Detail.ifIncludeFreight === 1 ? '是' : '否'
            }}
          </el-col>
          <el-col :span="6">
            单件运费：{{ row.supplier1Detail.freightRate || '' }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            付款条款：{{ row.supplier1Detail.paymentTermsCodeName || '' }}
          </el-col>
          <el-col :span="4">
            对接库存：{{
              row.supplier1Detail.ifUpInventory === 1 ? '是' : '否'
            }}
          </el-col>
          <el-col :span="6">
            供应商分类: {{ row.supplier1Detail.supplierClassifyName || '' }}
          </el-col>
          <el-col :span="6">
            年框:
            {{
              row.supplier1Detail.ifSignFrameworkAgreement === 1 ? '是' : '否'
            }}
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            供应商物料号：{{ row.supplier1Detail.idnlf }}
          </el-col>
          <el-col :span="4">
            OEM直发：{{
              row.supplier1Detail.oemDirectSupplier === 1 ? '是' : '否'
            }}
          </el-col>
          <el-col :span="8">
            最近一次订货日：{{ row.orderCalendar.nearestOrderDate }}
          </el-col>
        </el-row>
        <span slot="reference">
          <span
            @click="
              handleCopy(
                isVpi(row)
                  ? row.customerSpecifiedPurchasePriceVO.providerNo
                  : row.supplier1Detail.providerNo,
                $event
              )
            "
            style="display: inline-block; cursor: pointer; margin: 0 5px"
            title="点击复制供应商编码"
          >
            <i class="el-icon-document-copy"></i>
          </span>
          <span :style="{ color: '#409eff' }">
            {{
              isVpi(row)
                ? row.customerSpecifiedPurchasePriceVO.providerNo +
                ' ' +
                row.customerSpecifiedPurchasePriceVO.providerName
                : row.supplier1Detail.providerNo +
                ' ' +
                row.supplier1Detail.providerName
            }}
          </span>
        </span>
      </el-popover>
    </template>

    <template v-slot:purchasePrice0Price_default="{ row }">
      {{
        getPurchasePrice(
          isVpi(row)
            ? row.customerSpecifiedPurchasePriceVO
            : row.supplier1Detail
        )
      }}
    </template>

    <template v-slot:purchasePriceVOList1="{ row }">
      <el-popover
        v-if="row.supplier2Detail.providerNo"
        placement="top"
        width="600"
        trigger="hover"
      >
        <el-row :gutter="20">
          <el-row>
            <el-col :span="8">
              综合得分：{{ row.supplier2Detail.totalScore }}
            </el-col>
            <el-col :span="8">
              执行建议：{{ getGrade(row.supplier2Detail.grade) }}
            </el-col>
            <el-col :span="8" style="display: flex">
              风险提示：
              <el-tooltip
                class="item"
                effect="dark"
                :content="row.supplier2Detail.riskWarn"
                placement="top-start"
              >
                <span
                  style="
                    width: 100px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis; ;
                  "
                >{{ row.supplier2Detail.riskWarn }}</span
                >
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              策略类型：{{ row.supplier2Detail.tacticsName }}
            </el-col>
            <el-col :span="16" style="display: flex">
              策略详情：
              <el-tooltip
                class="item"
                effect="dark"
                :content="row.supplier2Detail.tacticsDetail"
                placement="top-start"
              >
                <span
                  style="
                    width: 200px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis; ;
                  "
                >{{ row.supplier2Detail.tacticsDetail }}</span
                >
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              是否代理品牌：{{ row.supplier2Detail.ifAgency === 0 ? '否' : row.supplier2Detail.ifAgency === 1 ? '是' : '' }}
            </el-col>
            <el-col :span="8" style="display: flex">
              是否原厂品牌：
              {{ row.supplier2Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(row.supplier2Detail.ifTradeCertificate) ? '是' : '' }}
            </el-col>
            <el-col :span="8" style="display: flex">
              MPQ：
              {{ row.supplier2Detail.purchaseMpq || '' }}
            </el-col>
          </el-row>
          <el-col :span="8">
            对接库存：{{ row.supplier2Detail.ifUpInventory || 0 }}
          </el-col>
          <el-col :span="8">
            含运费：{{
              row.supplier2Detail.ifIncludeFreight === 1 ? '是' : '否'
            }}
          </el-col>
          <el-col :span="8">
            单件运费：{{ row.supplier2Detail.freightRate || '' }}
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            供应商分类: {{ row.supplier2Detail.supplierClassifyName || '' }}
          </el-col>
          <el-col :span="8">
            年框:
            {{
              row.supplier2Detail.ifSignFrameworkAgreement === 1 ? '是' : '否'
            }}
          </el-col>
          <el-col :span="8">
            OEM直发: {{ row.supplier2Detail.oemDirectSupplier === 1 ? '是' : '否' }}
          </el-col>
          <el-col :span="8">
            供应商物料号：{{ row.supplier2Detail.idnlf }}
          </el-col>
        </el-row>
        <span slot="reference">
          <span
            @click="handleCopy(row.supplier2Detail.providerNo, $event)"
            style="display: inline-block; cursor: pointer; margin: 0 5px"
            title="点击复制供应商编码"
          >
            <i class="el-icon-document-copy"></i>
          </span>
          <span :style="{ color: '#409eff' }">
            {{
              row.supplier2Detail.providerNo +
              ' ' +
              row.supplier2Detail.providerName
            }}
          </span>
        </span>
      </el-popover>
    </template>

    <template v-slot:purchasePrice1Price_default="{ row }">
      {{ getPurchasePrice(row.supplier2Detail) }}
    </template>

    <template v-slot:purchasePriceVOList2="{ row }">
      <el-popover
        v-if="row.supplier3Detail.providerNo"
        placement="top"
        width="600"
        trigger="hover"
      >
        <el-row :gutter="20">
          <el-row>
            <el-col :span="8">
              综合得分：{{ row.supplier3Detail.totalScore }}
            </el-col>
            <el-col :span="8">
              执行建议：{{ getGrade(row.supplier3Detail.grade) }}
            </el-col>
            <el-col :span="8" style="display: flex">
              风险提示：
              <el-tooltip
                class="item"
                effect="dark"
                :content="row.supplier3Detail.riskWarn"
                placement="top-start"
              >
                <span
                  style="
                    width: 100px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis; ;
                  "
                >{{ row.supplier3Detail.riskWarn }}</span
                >
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              策略类型：{{ row.supplier3Detail.tacticsName }}
            </el-col>
            <el-col :span="16" style="display: flex">
              策略详情：
              <el-tooltip
                class="item"
                effect="dark"
                :content="row.supplier3Detail.tacticsDetail"
                placement="top-start"
              >
                <span
                  style="
                    width: 200px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis; ;
                  "
                >{{ row.supplier3Detail.tacticsDetail }}</span
                >
              </el-tooltip>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              是否代理品牌：{{ row.supplier3Detail.ifAgency === 0 ? '否' : row.supplier3Detail.ifAgency === 1 ? '是' : '' }}
            </el-col>
            <el-col :span="8" style="display: flex">
              是否原厂品牌：
              {{ row.supplier3Detail.ifTradeCertificate === 0 ? '否' : [1, 2].includes(row.supplier3Detail.ifTradeCertificate) ? '是' : '' }}
            </el-col>
            <el-col :span="8" style="display: flex">
              MPQ：
              {{ row.supplier3Detail.purchaseMpq || '' }}
            </el-col>
          </el-row>
          <el-col :span="8">
            对接库存：{{ row.supplier3Detail.ifUpInventory || 0 }}
          </el-col>
          <el-col :span="8">
            含运费：{{
              row.supplier3Detail.ifIncludeFreight === 1 ? '是' : '否'
            }}
          </el-col>
          <el-col :span="8">
            单件运费：{{ row.supplier3Detail.freightRate || '' }}
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            供应商分类: {{ row.supplier3Detail.supplierClassifyName || '' }}
          </el-col>
          <el-col :span="8">
            年框:
            {{
              row.supplier3Detail.ifSignFrameworkAgreement === 1 ? '是' : '否'
            }}
          </el-col>
          <el-col :span="8">
            OEM直发: {{ row.supplier3Detail.oemDirectSupplier === 1 ? '是' : '否' }}
          </el-col>
          <el-col :span="8">
            供应商物料号：{{ row.supplier3Detail.idnlf }}
          </el-col>
        </el-row>
        <el-button slot="reference" type="text" :style="{ border: 'none' }">
          {{
            row.supplier3Detail.providerNo +
            ' ' +
            row.supplier3Detail.providerName
          }}
        </el-button>
      </el-popover>
    </template>

    <template v-slot:purchasePrice2Price_default="{ row }">
      {{ getPurchasePrice(row.supplier3Detail) }}
    </template>

    <template v-slot:recommendSupplier_default="{ row }">
      <div v-html="recommendSupplierStr(row.recommendSupplierList)"></div>
    </template>

    <template v-slot:operation="{ row }">
      <el-link @click="toDetail(row)" type="primary">详情</el-link>
    </template>
    <template v-slot:poItemHistoryPrice_default="{ row }">
      {{ row.poItemHistoryPrice || '-' }}
    </template>
    <template #pager>
      <vxe-pager
        :layouts="[
          'Sizes',
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'FullJump',
          'Total'
        ]"
        :page-sizes="[100, 500, 1000, 2000]"
        :border="true"
        :current-page.sync="tablePage.currentPage"
        :page-size.sync="tablePage.pageSize"
        :total="tablePage.total"
        @page-change="handlePageChange"
      >
      </vxe-pager>
    </template>
  </vxe-grid>
</template>

<script>
import {
  cellClick,
  esokzName,
  getCurrency,
  handleCopy,
  getPurchasePrice,
  getIfBid,
  getBomItemInventoryVoList,
  getGrade,
  recommendSupplierStr
} from '../../utils'
import { setIds } from '@/storage/index'
import { getCustomerInfo } from '@/api/mrp'
import { mapState } from 'vuex'

export default {
  name: 'summaryGrid',
  props: {
    tableHeight: Number,
    tableLoading: Boolean,
    listData: Array,
    columns: Array,
    tableToolbar: Object,
    tablePage: Object,
    handlePageChange: Function,
    scroll: Function,
    transferSearchForm: String,
    cacheSearchForm: String,
    handleDownExcel: Function,
    purchaseList: Array,
    formatQuantityUnit: Function,
    getNameAddQuantity: Function
  },
  computed: {
    ...mapState(['userRole']),
    canTransfer() {
      return !!~this.userRole.indexOf('MRP-采购员') || !!~this.userRole.indexOf('MRP-采购经理') || !!~this.userRole.indexOf('MRP-采购总监') || !!~this.userRole.indexOf('MRP-采购值班账号') || !!~this.userRole.indexOf('data-采购员') || !!~this.userRole.indexOf('data-采购经理') || !!~this.userRole.indexOf('data-采购总监')
    }
  },
  data() {
    return {
      tableCustom: {
        storage: true
      },
      specMsg: [],
      urgentMsg: [],
      msgLoading: false,
      disabled: true,
      selectList: []
    }
  },
  watch: {
    selectList: {
      handler: function () {
        this.disable()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getIfBid,
    getPurchasePrice,
    handleCopy,
    getCurrency,
    esokzName,
    cellClick,
    getBomItemInventoryVoList,
    getGrade,
    recommendSupplierStr,
    genRowColor({ row }) {
      const orderToday = typeof row?.orderCalendar?.orderToday === 'number' ? row.orderCalendar.orderToday : null
      return !orderToday && orderToday === 0 ? 'dis-order' : ''
    },
    isVpi(item) {
      return !!item.prType
    },
    async handleMsg(msg) {
      this.urgentMsg = []
      this.specMsg = []
      if (msg && !this.msgLoading) {
        this.msgLoading = true
        const arr = msg.split(';')
        for (let i = 0; i < arr.length; i++) {
          if (arr[i]) {
            const item = arr[i].split(' ')
            const params = {
              customerNumber: item[2],
              distributionChannel: item[3],
              productGroup: item[4],
              salesOrganization: item[5]
            }
            try {
              const res = await getCustomerInfo(params)
              let specMsg = {
                soNo: item[0],
                number: item[6],
                remark: item[7],
                customer: res.data.customerServiceName
              }
              let urgentMsg = {
                soNo: item[0],
                number: item[6],
                remark: item[1],
                customer: res.data.customerServiceName
              }
              this.specMsg.push(specMsg)
              this.urgentMsg.push(urgentMsg)
              this.msgLoading = false
            } catch (error) {
              this.msgLoading = false
              console.log(error)
            }
          }
        }
      }
    },
    selectChange({ checked, records, row }) {
      if (checked) {
        this.selectList.push(row)
      } else {
        this.selectList = this.selectList.filter((item) => item.id !== row.id)
      }
      this.disable()
    },
    selectAll({ checked, records }) {
      if (checked) {
        this.selectList = records
      } else {
        this.selectList = []
      }
      this.disable()
    },
    disable() {
      if (this.selectList.length > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    handleTransfer() {
      // let ids = (this.selectList.map(item => item.id)).join(',') url地址过长问题
      const hasLowGrossMargin = this.selectList.some(item => item.ifGrossMargin)
      if (hasLowGrossMargin) {
        this.$confirm('当前选中的负需求，存在低负毛利，请确认后进行转单！', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.confirmTransfer()
        })
      } else {
        this.confirmTransfer()
      }
    },
    confirmTransfer() {
      const params = encodeURIComponent(this.transferSearchForm)
      const path = `/mrp/orderTransfer/${params}`
      this.$closeTag(path)
      setIds(
        'mrp-orderTransfer-ids',
        this.selectList.map((item) => item.id)
      )
      setTimeout(() => {
        this.$router.push({
          path
        })
      }, 0)
    },
    toDetail(row) {
      if (this.$refs.summaryGrid.isCheckedByCheckboxRow(row)) {
        this.$router.push({
          path: `/mrp/detail/${row.skuNo}_${row.mrpArea}`,
          query: {
            searchForm: encodeURIComponent(this.cacheSearchForm)
          }
        })
      } else {
        this.$router.push({
          path: '/mrp/detail/' + row.skuNo,
          query: {
            searchForm: encodeURIComponent(this.cacheSearchForm)
          }
        })
      }
    }
  }
}
</script>

<style lang="scss">
  .el-popover.popover {
    text-align: center;
  }

  .el-popover.align {
    text-align: left;
  }
  .dis-order {
    background-color: rgb(255, 238, 238);
  }
</style>
<style lang="scss" scoped>
  .is-vpi {
    color: white;
    background-color: red;
    border-radius: 50%;
    margin-right: 5px;

    .product-vpi-icon {
      width: 10px;
      height: 10px;
      margin-left: 1px;
      padding-top: 2px;
    }

    &.span {
      display: inline-block;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
    }
  }
</style>
