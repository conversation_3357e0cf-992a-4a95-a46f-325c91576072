<template>
  <div class="add-area">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="50%"
      :modal="false"
      :close-on-click-modal="false"
      :before-close="handleClose"
    >
      <el-form ref="dialogForm" :model="dialogForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="150px" :hide-required-asterisk="false">
        <el-form-item label="MRP区域编码" prop="code">
          <el-input
            v-model="dialogForm.code"
            filterable
            clearable
            style="width:200px"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item label="MRP区域描述" prop="description">
          <el-input
            v-model="dialogForm.description"
            filterable
            clearable
            style="width:100%"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item label="工厂" prop="factory" required>
          <el-select
            v-model="dialogForm.factory"
            filterable
            clearable
            style="width:100%"
            :disabled="disabled"
            @change="changeFactory(dialogForm.factory)"
          >
            <el-option
              v-for="item in factoryList"
              :key="item.factoryCode+item.factoryName"
              :label="item.factoryCode+' '+item.factoryName"
              :value="item.factoryCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="MRP区域类型" prop="type" required>
          <el-select
            v-model.number="dialogForm.type"
            filterable
            style="width:200px"
            :disabled="disabled"
            @change="changeType"
          >
            <el-option :value="1" label="1 工厂"></el-option>
            <el-option :value="2" label="2 仓库地点"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-show="dialogForm.type == 2" label="默认仓库地点" prop="defaultPosition">
          <el-select
              clearable
              filterable
              style="width:100%"
              v-model="dialogForm.defaultPosition"
              placeholder="请选择仓库地点"
          >
            <el-option
                v-for="item in warehouseListFilterByFactory1"
                :key="item.warehouseLocationCode"
                :label="`${item.warehouseLocationCode} ${item.warehouseLocationName}`"
                :value="item.warehouseLocationCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="dialogForm.type == 2" label="其他仓库地点" prop="position">
          <el-select
              clearable
              filterable
              multiple
              style="width:100%"
              v-model="dialogForm.position"
              placeholder="其他仓库地点输入不能超过20个"
          >
            <el-option
                v-for="item in warehouseListFilterByFactory1"
                :key="item.warehouseLocationCode"
                :label="`${item.warehouseLocationCode} ${item.warehouseLocationName}`"
                :value="item.warehouseLocationCode"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addMrp">保 存</el-button>
        <el-button type="info"  @click="cancel">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog
      width="30%"
      :visible.sync="saveVisible"
      :modal="false"
      :close-on-click-modal="false"
    >
     <span>MRP区域{{ dialogForm.code }}原始默认仓储地点为{{ defaultPosition }}，请确认是否更改默认仓储地点?</span>
     <span slot="footer" class="dialog-footer">
      <el-button type="primary"  @click="modifyDefaultWarehouse">是</el-button>
      <el-button @click="unmodifyDefault">否</el-button>
    </span>
    </el-dialog>
  </div>
</template>

<script>
// import { mapState } from 'vuex'
import { verifyDefaultWarehouse, createMrpArea } from '@/api/mrp'
import { safeRun } from '@/utils/index'

export default {
  name: 'addArea',
  props: {
    dialogTitle: {
      type: String,
      default: '标题'
    },
    visible: {
      type: Boolean,
      default: false
    },
    dialogForm: {
      typeof: Object,
      default: {}
    },
    rules: {
      typeof: Object,
      default: {}
    },
    disabled: {
      type: Boolean,
      default: false
    },
    getMrpList: {
      type: Function
    },
    factoryList: {
      type: Array
    },
    warehouseList: {
      type: Array
    },
    warehouseListFilterByFactory1: {
      type: Array
    }
  },
  data () {
    return {
      saveVisible: false,
      defaultPosition: ''
    }
  },
  created () {

  },
  computed: {
    dialogVisible: {
      get () {
        return this.visible
      },
      set (val) {
        this.$emit('updateVisible', val)
      }
    }
  },
  methods: {
    cancel () {
      this.$refs.dialogForm.resetFields()
      this.$emit('resetPopupData')
    },
    formatParams(data) {
      let form = { ...data };
      // if (form.position.includes(' ')) {
      //   form.position = safeRun(() =>
      //     form.position
      //       .split(/\s/).filter((e) => e)
      //   );
      // }
      return form;
    },
    validate (data) {
      let ret = true
      safeRun(() => {
        if (data.position.length > 20) {
          ret = false
          this.$message.error('其他仓库地点输入不能超过20个！')
        }
      })
      return ret
    },
    addMrp () {
      if (this.dialogForm.type === 1) {
        this.$refs.dialogForm.validate(async (valid) => {
          if (valid) {
            this.createMrp()
          }
        })
        return
      }
      this.$refs.dialogForm.validate(async (valid) => {
        if (valid) {
          try {
            const res = await verifyDefaultWarehouse({ mrpArea: this.dialogForm.code, isDefault: 1 })
            if (this.dialogForm.defaultPosition && res.total === 1 && this.dialogForm.defaultPosition !== res.records[0].position) {
              this.saveVisible = true
              this.defaultPosition = res.records[0].position
            } else if (!this.dialogForm.defaultPosition && res.total === 0) {
              this.$message.error(`MRP区域${this.dialogForm.code}无默认仓储地点，请先确定此区域最新的默认仓储地点！`)
            } else {
              this.createMrp()
            }
          } catch (error) {
            console.log(error);
          }
        } else {
          return false
        }
      });
    },
    modifyDefaultWarehouse () {
      this.saveVisible = false
      this.createMrp()
    },
    unmodifyDefault () {
      this.$emit('updateDefaultPosition')
      this.saveVisible = false
    },
    async createMrp () {
      try {
        let data = this.formatParams(this.dialogForm)
        if (!this.validate(data)) return
        if (this.dialogForm.defaultPosition) {
          data.isDefault = 1
        } else {
          data.isDefault = 0
        }
        for (let i = 0; i < data.position.length; i++) {
          if (data.position[i].length > 4) {
            return this.$message.error('仓库地点不能超过 4 个字符')
          }
        }
        if (Array.isArray(data.position)) {
          data.position = data.position.join(' ')
        }
        const res = await createMrpArea(data)
        if (res.success) {
          this.$refs.dialogForm.resetFields()
          this.$emit('submitPopupData')
          this.getMrpList()
          this.$store.dispatch('mrp/queryMrpAreaList')
          this.$message.success(res.msg)
        } else {
          if (res.msg) {
            res.msg = res.msg.replace(/;/g, '<br/>')
          }
          this.$message({
            type: 'error',
            message: res.msg,
            dangerouslyUseHTMLString: true,
            customClass: 'mzindex'
          })
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleClose () {
      this.$refs.dialogForm.resetFields()
      this.$emit('submitPopupData')
    },
    changeType () {
      if (this.dialogForm.type === 1) {
        this.$emit('modifyRulesFalse')
      } else {
        this.$emit('modifyRulesTrue')
      }
    },
    changeFactory (val) {
      this.$emit('warehouseListFilter', val)
      this.$emit('updatePosition')
      this.$emit('updateDefaultPosition')
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: center;
}
.mzindex{
  z-index:9000 !important;
}

</style>
