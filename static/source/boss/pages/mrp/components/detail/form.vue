<template>
  <el-form
    ref="ruleForm"
    :rules="rules"
    :model="searchForm"
    style="width: 100%"
    label-suffix=":"
    label-width="80px"
    v-show="hideResult"
  >
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="公司" prop="companyCode" required>
          <el-select
            v-model="searchForm.companyCode"
            filterable
            default-first-option
            clearable
            style="width: 100%"
            placeholder="请选择公司"
          >
            <el-option
              v-for="item in companyFactoryList"
              :key="item.companyCode"
              :label="item.companyCode + ' ' + item.companyName"
              :value="item.companyCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="工厂" prop="factory" required>
          <el-select
            v-model="searchForm.factory"
            filterable
            default-first-option
            clearable
            style="width: 100%"
            placeholder="请选择工厂"
          >
            <el-option
              v-for="item in factoryList"
              :key="item.factoryCode"
              :label="item.factoryCode + ' ' + item.factoryName"
              :value="item.factoryCode"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="采购员" prop="purchaseGroup" required>
          <el-select
            v-if="setPower(userRole)"
            v-model="searchForm.purchaseGroup"
            allow-create
            filterable
            collapse-tags
            default-first-option
            clearable
            multiple
            style="width: 100%"
            placeholder="请选择采购员"
          >
            <el-option
              v-for="item in purchaseList"
              :key="item.groupCode + item.userName"
              :label="item.groupCode + ' ' + item.userName"
              :value="item.groupCode"
            >
            </el-option>
          </el-select>
          <SelectPurchaseGroup
            v-else
            :data.sync="searchForm.purchaseGroup"
            @getUserPurchaseGroup="handelPurchaseGroup"
            v-on="$listeners"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="SKU" prop="skuNo">
          <el-input
            v-model="searchForm.skuNo"
            clearable
            type="textarea"
            :rows="1"
            style="width: 100%"
            placeholder="最多支持100个SKU按空格隔开搜索"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物料组" prop="productGroup">
          <el-select
            v-model="searchForm.productGroup"
            filterable
            default-first-option
            clearable
            multiple
            collapse-tags
            style="width: 100%"
            placeholder="最多支持10个物料组搜索"
          >
            <el-option
              v-for="item in productList"
              :key="item.productGroupNum + Math.random()"
              :label="item.productGroupNum + ' ' + item.productGroupName"
              :value="item.productGroupNum"
            >
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="MRP区域" prop="mrpArea">
          <el-select
            clearable
            filterable
            default-first-option
            v-model="searchForm.mrpArea"
            placeholder="最多支持10个MRP区域搜索"
            style="width: 100%"
            multiple
            collapse-tags
          >
            <el-option
              v-for="item in mrpAreaListFilterByFactory"
              :key="item.code + item.description"
              :label="item.code + ' ' + item.description"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="品牌" prop="brand">
          <SelectBrand
            clearable
            :data.sync="searchForm.brands"
            :defaultValue="true"
            @change="handleChange('brands', $event)"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="VPI物料" prop="isVpi">
          <el-select
            v-model="searchForm.isVpi"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="1" label="是" :value="1" />
            <el-option :key="0" label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="单据号" prop="recordNo">
          <el-input v-model="searchForm.recordNo" placeholder="支持多个按空格隔开搜索" type="textarea" :rows="1" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item label="单据行号" prop="recordItemNo">
          <el-input :disabled="searchForm.recordNo ? false : true" :placeholder="searchForm.recordNo ? '请输入' : '请先输入订单号'" v-model="searchForm.recordItemNo" />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="下单异常" prop="isOrderException">
          <el-select
            v-model="searchForm.isOrderException"
            clearable
            style="width: 100%"
            placeholder="请选择"
          >
            <el-option :key="-1" label="全部" :value="2" />
            <el-option :key="1" label="是" :value="1" />
            <el-option :key="0" label="否" :value="0" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col
        :span="8"
        :style="{ display: 'flex', justifyContent: 'flex-end' }"
      >
        <el-button
          type="primary"
          style="width: 80px"
          :loading="searchLoading"
          @click="validateSearch"
        >
          查询
        </el-button>
        <el-button style="width: 80px; margin-right: 75px;" @click="resetForm">
          重置
        </el-button>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import { getUserInfo, getPurchaseGroup } from '@/api/mrp'
import SelectPurchaseGroup from '../selectPurchaseGroup.vue'
import SelectBrand from '../selectBrand.vue'
import { setPower } from '../../utils'

export default {
  name: 'detailForm',
  components: {
    SelectBrand,
    SelectPurchaseGroup
  },
  props: {
    hideResult: Boolean,
    purchaseList: Array,
    searchLoading: Boolean,
    userRole: Array
  },
  data () {
    return {
      userCompany: '',
      userFactory: '',
      userPurchase: [],
      factoryList: [],
      searchForm: {
        companyCode: '',
        factory: '',
        purchaseGroup: [],
        brand: '',
        brands: {},
        isVpi: '',
        mrpArea: [],
        productGroup: [],
        skuNo: '',
        createTime: [],
        isOrderException: ''
      },
      mrpAreaListFilterByFactory: [],
      rules: {
        companyCode: [
          { required: true, message: '公司不能为空！', trigger: ['change', 'blur'] }
        ],
        factory: [
          { required: true, message: '工厂不能为空！', trigger: ['change', 'blur'] }
        ],
        purchaseGroup: [
          { required: true, message: '采购员不能为空！', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  async created () {
    const pList = []
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (this.productList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryProductGroup'))
    }
    if (this.companyInfoList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryCompanyInfoList'))
    }
    if (!this.mrpAreaList || this.mrpAreaList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryMrpAreaList'))
    }
    await Promise.all(pList)
    if (this.$route.params.id) {
      if (this.$route.query.searchForm && typeof this.$route.query.searchForm === 'string') {
        let query = JSON.parse(decodeURIComponent(this.$route.query.searchForm))
        const skuNo = this.$route.params.id.split('_')[0]
        this.$set(this.searchForm, 'companyCode', query.company)
        this.factoryList = this.companyFactoryList.find(item => item.companyCode === this.searchForm.companyCode || {}).factoryList
        this.$set(this.searchForm, 'factory', query.factory)
        this.$set(this.searchForm, 'purchaseGroup', query.purchaseGroup)
        this.$set(this.searchForm, 'skuNo', skuNo)
        if (this.$route.params.id.split('_')[1]) {
          const mrpArea = [this.$route.params.id.split('_')[1]]
          this.$set(this.searchForm, 'mrpArea', mrpArea)
        }
      }
      await this.validateSearch()
    } else {
      if (this.setPower(this.userRole)) {
        this.userPurchaseGroup()
      }
      this.getUserCompany()
    }
  },
  computed: {
    ...mapState({
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      productList: state => state.mrp.productList,
      companyInfoList: state => state.mrp.companyInfoList,
      mrpAreaList: state => state.mrp.mrpAreaList
    })
  },
  watch: {
    'searchForm.companyCode': function (newValue, oldValue) {
      this.factoryList = (this.companyFactoryList.find(item => item.companyCode === newValue) || {}).factoryList || []
      if (oldValue) {
        this.searchForm.factory = ''
      }
    },
    factoryList: {
      handler: function (newValue) {
        if (newValue.length === 1) {
          this.userFactory = this.factoryList[0].factoryCode
          this.searchForm.factory = this.factoryList[0].factoryCode
          this.mrpAreaListFilterByFactory = this.mrpAreaList.filter(item => item.factory === this.factoryList[0].factoryCode)
        }
      },
      deep: true,
      immediate: true
    },
    'searchForm.factory': function (newValue) {
      this.mrpAreaListFilterByFactory = this.mrpAreaList.filter(item => item.factory === newValue)
    },
    'searchForm.recordNo': function (newValue) {
      if (!newValue) {
        this.searchForm.recordItemNo = ''
      }
    }
  },
  methods: {
    setPower,
    handelPurchaseGroup (list) {
      if (list.length === 1) {
        this.userPurchase = list.map(item => item.groupCode)
      }
    },
    async userPurchaseGroup () {
      const res = await getPurchaseGroup({
        securityUsernameList: window.CUR_DATA.user && window.CUR_DATA.user.name
      })
      this.userPurchase = res.data.map(item => item.groupCode)
      this.searchForm.purchaseGroup = this.userPurchase
    },
    async getUserCompany () {
      try {
        const res = await getUserInfo({
          username: window.CUR_DATA.user && window.CUR_DATA.user.name
        })
        let defaultCompany = (this.companyInfoList.find(item => item.subCompanyId === res.subCompany) || {}).code
        if (defaultCompany) {
          this.userCompany = defaultCompany
          this.searchForm.companyCode = defaultCompany
        }
      } catch (error) {
        console.log(error);
      }
    },
    handleChange (type, event) {
      if (type === 'brands') {
        this.searchForm.brand = event.brandId
      }
    },
    validateSearch () {
      const data = { ...this.searchForm }
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.$emit('handleSearch', data)
        } else {
          return false;
        }
      })
    },
    resetForm () {
      this.$refs.ruleForm.resetFields()
      this.searchForm.brands = {}
      this.searchForm.companyCode = this.userCompany
      this.searchForm.factory = this.userFactory
      this.searchForm.purchaseGroup = this.userPurchase
    }
  }
};
</script>

<style>
</style>
