<template>
<el-select
  ref="sku"
  v-model="sku"
  :placeholder="placeholder"
  filterable
  remote
  reserve-keyword
  clearable
  style="width:100%"
  value-key="skuNo"
  :disabled="disabled"
  :remote-method="remoteMethod"
  :loading="loading"
  @change="handleSelectChange"
>
  <el-option
    v-for="(item, index) in skuList"
    :key="item.skuNo"
    :label="item.skuNo+' '+item.materialDescription"
    :value="item.skuNo"
    :disabled="index===0"
  >
    <div
      class="selectItem"
      :style="{fontWeight:index===0?'bold':'normal'}"
    >
      <div>{{ item.skuNo || '--' }}</div>
      <div>{{ item.materialDescription || '--' }}</div>
    </div>
  </el-option>
</el-select>
</template>

<script>
import { getProductSkuLike } from '@/api/mm';
import { spDebounce } from '@/utils/index';
export default {
  name: 'SelectSku',
  props: {
    data: {
      type: [String, Object]
    },
    disabled: {
      type: Boolean
    },
    placeholder: {
      type: String,
      default: '选择SKU'
    }
  },
  data() {
    return {
      loading: false,
      skuList: []
    }
  },
  created () {
    this.remoteMethod = spDebounce(this.remoteMethod, 1000, this)
  },
  computed: {
    sku: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      let materialDescription = ''
      if (val) {
        const found = this.skuList.find(item => item.skuNo === val)
        if (found) {
          materialDescription = found.materialDescription
        }
      }
      this.$emit('change', val, materialDescription)
    },
    remoteMethod (val) {
      if (val) {
        this.loading = true
        getProductSkuLike({
          vague: val
        }).then(data => {
          this.loading = false
          if (data) {
            this.skuList = [
              {
                skuNo: 'skuNo',
                materialDescription: '物料描述'
              },
              ...data
            ]
          }
        })
      } else {
        this.skuList = [
          {
            skuNo: 'skuNo',
            materialDescription: '物料描述'
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    overflow: auto;
    width: 220px;
  }
}
</style>
