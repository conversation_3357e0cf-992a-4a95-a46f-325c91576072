<template>
  <div class="BiAmountStrategyDialog">
    <el-dialog
      title="凑单策略"
      :visible.sync="showDlg"
      width="50%"
      center>
      <div style="margin-bottom: 10px;">
        <el-row :gutter="20" style="margin-bottom: 10px;">
          <el-col :span="16" :title="biAmountStrategyData.supplier" style="text-overflow:ellipsis;overflow:hidden;">
            供应商：{{ biAmountStrategyData.supplier }}
          </el-col>
          <el-col :span="8">
            起订金额：{{ haveBiPageBeginAmountList ? biAmountStrategyData.biPageBeginAmountList[0].initialAmount : '' }}
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            凑单金额：{{ haveBiPageBeginAmountList ? biAmountStrategyData.biPageBeginAmountList[0].totalAmount : '' }}
          </el-col>
          <el-col :span="8">
            原始需求金额：{{ haveBiPageBeginAmountList ? biAmountStrategyData.biPageBeginAmountList[0].sourceAmount : '' }}
            <el-popover
              placement="right"
              title=""
              width="400"
              trigger="hover"
              >
              <span>
                表示该行真实负需求的采购金额
              </span>
              <span slot="reference">
                 <i class="el-icon-question"></i>
              </span>
            </el-popover>
          </el-col>
          <el-col :span="8">
            剩余金额：{{ haveBiPageBeginAmountList  ? biAmountStrategyData.biPageBeginAmountList[0].lackAmount : '' }}
            <el-popover
              placement="right"
              title=""
              width="400"
              trigger="hover"
              >
              <span>
                = 起订金额 - 凑单总金额 - 原始需求金额， 负数表示已凑够起订金额
              </span>
              <span slot="reference">
                 <i class="el-icon-question"></i>
              </span>
            </el-popover>
          </el-col>
        </el-row>
      </div>
      <el-table :data="biAmountStrategyData.biPageBeginAmountList"  header-align="center">
        <el-table-column property="sku" label="凑单sku" width="120"></el-table-column>
        <el-table-column property="skuDesc" label="sku描述"></el-table-column>
        <el-table-column property="unitName" label="采购单位" align="center" width="100" ></el-table-column>
        <el-table-column property="quantity" label="凑单数量" width="200" align="center">
          <template slot-scope={row}>
            <el-input-number
              v-model="row.quantity"
              size="mini"
              style="width: 100%"
              :precision="2"
              :min="0"
              @change="handleChangeQuantity(row)"
            />
          </template>
        </el-table-column>
        <el-table-column property="price" label="含税单价" width="150" align="center"></el-table-column>
        <el-table-column property="currentAmount" label="金额" width="150" align="center"></el-table-column>
      </el-table>
      <div style="margin-top: 20px; font-size: 12px;color: #a6a3a3">
        凑单逻辑：按凑单sku清单（BI算法提供）优先级，依次使用凑单sku。<br>
          如果凑单sku备货策略为安全库存，则凑单数量=MOQ；<br>
          如果凑单sku备货策略为ROP/ROQ，则凑单数量=max(MOQ，ROQ)；<br>
          如果凑单sku备货策略为ROP/补最大库存，则凑单数量=最大库存；<br>
          如果凑单sku没有备货，则凑单数量=MOQ；
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveItem">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BiAmountStrategyDialog',
  data () {
    return {
    }
  },
  props: [
    'data', 'showDialog'
  ],
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    biAmountStrategyData () {
      return this.data
    },
    haveBiPageBeginAmountList() {
      return this.biAmountStrategyData.biPageBeginAmountList && this.biAmountStrategyData.biPageBeginAmountList.length > 0
    }
  },
  methods: {
    handleChangeQuantity(row) {
      row.currentAmount = (row.quantity * row.price).toFixed(3)
      this.biAmountStrategyData.strategyType = 'customization_strategy_amount'
      const totalAmount = this.biAmountStrategyData.biPageBeginAmountList.reduce(function(total, curr) {
        return total + Number(curr.currentAmount);
      }, 0)
      const lackAmount = (this.biAmountStrategyData.biPageBeginAmountList[0].initialAmount - totalAmount -
      this.biAmountStrategyData.biPageBeginAmountList[0].sourceAmount).toFixed(3)
      this.biAmountStrategyData.biPageBeginAmountList.forEach(item => {
        item.totalAmount = totalAmount
        item.lackAmount = Number(lackAmount)
      })
    },
    saveItem() {
      this.showDlg = false
      this.$emit('updateItemDetail', this.biAmountStrategyData)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
