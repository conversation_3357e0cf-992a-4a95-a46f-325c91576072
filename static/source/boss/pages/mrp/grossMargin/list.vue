<template>
  <div class="app-container stockpile-strategy-checklist">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="业务类型" prop="type">
              <el-select
                v-model="searchForm.type"
                placeholder="请选择"
                filterable
                clearable
              >
                <el-option
                  v-for="item in typeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组" prop="productGroup">
              <MaterialGroup v-model="searchForm.productGroup" multiple collapse-tags style="width: 100%" placeholder="输入关键词" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brand">
              <Brand v-model="searchForm.brand" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="后台二级类目" prop="category">
              <AllCatalog v-model="searchForm.category" :checkStrictly="true" :multiple="false" style="width: 100%" placeholder="输入关键词" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="行采购未税金额" prop="itemUnrateAmo">
              <el-input-number
                v-model="searchForm.minItemUnrateAmo"
                clearable
                placeholder="最小值0"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
              <span style="display: inline-block; width: 10%; text-align: center">-</span>
              <el-input-number
                v-model="searchForm.maxItemUnrateAmo"
                clearable
                placeholder="最大值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="行毛利率" prop="itemGrossMargin">
              <el-input-number
                v-model="searchForm.minItemGrossMargin"
                clearable
                placeholder="最小值0"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>
              <span style="display: inline-block; width: 10%; text-align: center">-</span>
              <el-input-number
                v-model="searchForm.maxItemGrossMargin"
                clearable
                placeholder="最大值"
                style="width: 45%"
                :controls="false"
                :min="0"
              ></el-input-number>

            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="供应商编码" prop="supplier">
              <Supplier v-model="searchForm.supplier" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户编码" prop="customer">
              <Customer v-model="searchForm.customer" head-name="客户" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container" style="margin-top: 20px;">
      <el-row>
        <el-col style="margin-top: 12px;" :span="4">查询结果</el-col>
        <el-col
          :span="20"
          style="
            text-align: right;
            justify-content: flex-end;
            align-items: center;
          "
        >
          <el-button
            type="primary"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            @click="handleExportAll"
            :loading="exportLoading"
          >
            导出
          </el-button>
          <el-button
            type="danger"
            @click="handleBatchDel"
          >
            批量删除
          </el-button>
        </el-col>
      </el-row>
      <zkh-table
        ref="tableRef"
        style="margin-top: 10px;"
        :loading="basisListLoading"
        :data="basisList"
        :selectable="true"
        height="420"
        id="mrp_gross_grid"
        :columns="stockModelListColunms"
        :row-key="row => row.id"
      >
        <template #action="scope">
          <a class="primary-blue mr-4px cursor-pointer" @click="handleEdit(scope.row)">修改</a>
          <a class="primary-blue mr-4px cursor-pointer" @click="handleDelSingle(scope.row.id)">删除</a>
        </template>
      </zkh-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
    <AddDialog
      v-if="addDialogVisible"
      :visible="addDialogVisible"
      :detail="detail"
      :actionType="actionType"
      :loading="loading"
      @submit="handleCreate"
      @update:visible="(value) => addDialogVisible = value"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination';
import {
  getLowMraginList,
  addLowMragin,
  updateLowMragin,
  deleteLowMragin
} from '@/api/mrp';
import { stockModelListColunms, typeOptions, exportMapping } from './constant';
import AllCatalog from '@/components/SearchFields/secondLevelDirectory';
import MaterialGroup from './ProductGroup.vue';
import Brand from '@/components/SearchFields/brand';
import Customer from '@/components/SearchFields/consCustomer';
import Supplier from './Supplier';
import AddDialog from './listAddDialog';
import moment from 'moment';
import { writeFile } from '@boss/excel'
import { formatPercentToNum, formatNumToPercent } from '../utils/index'

export default {
  name: 'grossMargin',
  data() {
    return {
      basisListLoading: false,
      exportLoading: false,
      typeOptions,
      loading: false,
      basisList: [],
      searchForm: {
        brand: '',
        category: '',
        customer: '',
        productGroup: '',
        type: 0,
        supplier: ''
      },
      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      total: 0,
      addDialogVisible: false,
      stockModelListColunms,
      detail: {},
      actionType: ''
    };
  },
  components: {
    Pagination,
    AddDialog,
    AllCatalog,
    MaterialGroup,
    Brand,
    Customer,
    Supplier
  },
  async created() {
    this.handleFilter();
  },
  methods: {
    prepareSearchParam() {
      if (this.searchForm.minItemGrossMargin >= 0 && this.searchForm.maxItemGrossMargin >= 0 && this.searchForm.minItemGrossMargin > this.searchForm.maxItemGrossMargin) {
        this.$message.error({ message: '行毛利率最小值不能大于最大值！' });
        return false
      }
      if (this.searchForm.minItemUnrateAmo >= 0 && this.searchForm.maxItemUnrateAmo >= 0 && this.searchForm.minItemUnrateAmo > this.searchForm.maxItemUnrateAmo) {
        this.$message.error({ message: '行采购未税金额最小值不能大于最大值！' });
        return false
      }
      const productGroup = this.searchForm.productGroup ? this.searchForm.productGroup.join(',') : undefined;
      const { minItemGrossMargin, maxItemGrossMargin } = this.searchForm
      return { ...this.searchForm, pageNum: this.listQueryInfo.current, pageSize: this.listQueryInfo.pageSize, productGroup, minItemGrossMargin: formatPercentToNum(minItemGrossMargin), maxItemGrossMargin: formatPercentToNum(maxItemGrossMargin) };
    },
    getSelctedRows() {
      const selectableRows = this.$refs.tableRef.getSelections()
      return selectableRows
    },
    // 新增按钮
    handleAdd() {
      this.addDialogVisible = true
      this.actionType = 'add'
    },
    // 删除按钮
    async handleDelete(ids) {
      const res = await deleteLowMragin(ids)
      if (res.code !== 200) {
        this.$message.error(res.message || res.msg)
        return
      }
      this.$message({
        type: 'success',
        message: '删除成功!'
      });
      this.handleFilter()
    },
    // 单个删除
    handleDelSingle(id) {
      this.$confirm('请确认是否删除！', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.handleDelete([id])
      })
    },
    // 批量删除
    handleBatchDel() {
      const selectableRows = this.getSelctedRows()
      if (selectableRows.length === 0) {
        this.$message.warning('请选择要删除的记录!')
        return
      }
      this.$confirm('请确认是否删除！', '批量删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.handleDelete(selectableRows.map(it => it.id))
      })
    },

    // 编辑按钮
    handleEdit(row) {
      this.detail = row
      this.actionType = 'edit'
      this.addDialogVisible = true
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.searchForm.productGroup = []
      this.searchForm.maxItemGrossMargin = undefined
      this.searchForm.minItemGrossMargin = undefined
      this.searchForm.maxItemUnrateAmo = undefined
      this.searchForm.minItemUnrateAmo = undefined
      this.handleFilter();
    },
    // 创建配置
    async handleCreate(formFields) {
      try {
        this.loading = true
        const res = this.actionType === 'edit' ? await updateLowMragin([{ ...formFields }]) : await addLowMragin([{ ...formFields }])
        if (res.code === 200) {
          this.$message.success(this.actionType === 'edit' ? '更新成功！' : '新增成功')
          this.addDialogVisible = false
          this.handleFilter()
        } else {
          this.$message.error({
            message:
              (res && (res.message || res.msg)) || '更新失败！',
            duration: 6000
          });
        }
      } catch (error) {
        this.$message.error('更新失败！')
      } finally {
        this.loading = false
      }
    },
    async handleExportAll() {
      const mapping = exportMapping
      const params = this.prepareSearchParam()
      if (!params) return
      params.pageSize = 1000
      this.exportLoading = true
      const res = await getLowMraginList(params)
      const allList = res.data.records.map((item) => {
        const data = { ...item }
        Object.keys(data).forEach((key) => {
          if (mapping[key]) {
            if (key === 'itemGrossMargin') {
              data[mapping[key]] = item.grossMarginChar + formatNumToPercent(data[key]) + '%'
            } else if (key === 'itemUnrateAmo') {
              data[mapping[key]] = item.amoChar + data[key]
            } else if (key === 'type') {
              data[mapping[key]] = typeOptions.find((item) => item.value === data[key]).label
            } else if (key === 'supplier') {
              data[mapping[key]] = (data[key] || '') + ' ' + (data['supplierName'] || '')
            } else if (key === 'customer') {
              data[mapping[key]] = (data[key] || '') + ' ' + (data['customerName'] || '')
            } else {
              data[mapping[key]] = data[key]
            }
          }
          delete data[key]
        })
        return data
      })
      writeFile(
        allList,
        `低负毛利配置 ${moment(new Date()).format(
          'YYYY-MM-DD HH-mm-ss'
        )}.xlsx`,
        { header: Object.values(mapping) }
      )
      this.exportLoading = false
    },
    getBasisList() {
      const param = this.prepareSearchParam();
      if (!param) return
      this.basisList = [];
      this.basisListLoading = true;
      getLowMraginList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              const data = res.data.records;
              this.total = res.data.total;
              this.basisList = data;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    }
  }
};
</script>

<style lang="scss">
.stockpile-strategy-checklist {
  .tabelCellError {
    input {
      border: 1px solid #ff4949;
    }
    &:hover {
      input {
        border: 1px solid #ff4949;
      }
    }
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }
  .justify-between {
    justify-content: space-between;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .fw-500 {
    font-weight: 500;
  }
  .primary-blue {
    color: #7a95f1;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .input-number--micro {
    width: 100px;
  }
  .text-no-overflow {
    .cell {
      text-overflow: unset;
    }
  }
  .align-right {
    text-align: right;
  }
}
.upload {
  display: inline;
}
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.mr-4px {
  margin-right: 4px;
}
.cursor-pointer {
  cursor: pointer;
}
</style>
