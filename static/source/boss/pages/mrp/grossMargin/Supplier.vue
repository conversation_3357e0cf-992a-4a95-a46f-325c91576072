<template>
  <el-select
    ref="supplier"
    :value="value"
    placeholder="输入关键词"
    filterable
    remote
    reserve-keyword
    style="width: 100%"
    value-key="supplierNo"
    :disabled="disabled"
    :clearable="clearable"
    :multiple="multiple"
    :collapseTags="collapseTags"
    :remote-method="remoteMethod"
    :loading="loading"
    default-first-option
    @change="handleChange"
  >
    <el-option
      v-for="(item, index) in supplierList"
      :key="item.supplierNo"
      :label="showValue ? item.supplierNo : item.supplierName"
      :value="item.supplierNo"
      :disabled="index === 0 || item.isValid === 0"
    >
      <div class="supplier-select-item" :style="supplierSelectItem(index)">
        <div style="width: 100px">{{ item.supplierNo || '--' }}</div>
        <div style="width: 200px">{{ item.supplierName || '--' }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import request from '@/utility/request'

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      supplierList: [],
      loading: false
      // value: this.value
    }
  },
  props: {
    value: [Object, String],
    disabled: { type: Boolean, default: false },
    clearable: { type: Boolean, default: true },
    selectedSupplier: { type: Object, default: () => ({}) },
    showValue: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false },
    collapseTags: { type: Boolean, default: false }
  },
 // computed: {
 //   supplierSelectItem (index) {
 //     return {
 //       fontWeight: index === 0 ? 'bold' : 'normal',
 //       display: 'flex'
 //     };
 //   }
 // },
  methods: {
    // 获取供应商
    remoteMethod (val) {
      if (val) {
        this.loading = true;
        request({
          url: '/api-mm/supplier/options',
          method: 'get',
          params: {
            supplierNoOrName: val
          }
        })
          .then((res) => {
            if (res?.status !== 200 && res?.code !== 0) return;
            this.supplierList = [
              {
                supplierNo: '供应商编码',
                supplierName: '供应商名称'
              },
              ...res.data
            ];
            // if (this.value) {
            //   const supplier = this.supplierList.filter(item => item.supplierNo === this.value);
            //   if (supplier?.length) {
            //     this.$emit('change', supplier[0])
            //   }
            // }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.supplierList = [
          {
            supplierNo: '供应商编码',
            supplierName: '供应商名称'
          }
        ];
      }
    },
    init (supplier) {
      if (supplier) {
        this.remoteMethod(supplier)
      }
    },
    handleChange (value) {
      this.$emit('change', value)
    },
    supplierSelectItem (index) {
      return {
        fontWeight: index === 0 ? 'bold' : 'normal',
        display: 'flex'
      };
    }
  },
  mounted() {
    this.init(this.value);
  }
}
</script>
<style lang="scss">
  .supplier-select-item {
    display: flex;
    div:nth-child(1) {
      width: 100px;
    }
    div:nth-child(2) {
      width: 220px;
    }
}
</style>
