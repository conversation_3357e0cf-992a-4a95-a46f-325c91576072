<template>
  <el-dialog
    width="60%"
    style="max-height: 800px;"
    :title="actionType === 'add' ? '新增' : '编辑'"
    :visible.sync="showDlg"
    @closed="$emit('update:visible', false)"
  >
    <el-form
      ref="form"
      :model="formField"
      :rules="formRules"
      :validate-on-rule-change="true"
      label-width="120px"
      label-position="right"
      label-suffix=":"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="业务类型" prop="type" required>
            <el-select
              v-model="formField.type"
              placeholder="请选择"
              filterable
              clearable
            >
              <el-option
                v-for="item in typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="优先级" prop="level" required>
            <el-select
              v-model="formField.level"
              placeholder="请选择"
              filterable
              clearable
            >
              <el-option
                v-for="item in [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]"
                :key="item"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料组" prop="productGroup" required>
            <MaterialGroup v-model="formField.productGroup" style="width: 100%" placeholder="输入关键词" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="品牌" prop="brand">
            <Brand v-model="formField.brand" :default-label="brandName" @getlabel="handleGetBrandName" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="后台二级类目" prop="category">
            <AllCatalog v-model="formField.category" :default-label="categoryName" @getlabel="handleGetCategoryName" :multiple="false" style="width: 100%" placeholder="输入关键词" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商编码" prop="supplier">
            <Supplier v-model="formField.supplier" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="客户编码" prop="customer">
            <Customer v-model="formField.customer" head-name="客户" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行采购未税金额" prop="itemUnrateAmo" label-width="152px">
            <el-input v-model="formField.itemUnrateAmo" :min="0" style="width: 100%">
              <el-select v-model="formField.amoChar" slot="prepend" style="width: 80px;" placeholder="请选择">
                <el-option label="大于" value=">"></el-option>
                <el-option label="大于等于" value=">="></el-option>
                <el-option label="小于" value="<"></el-option>
                <el-option label="小于等于" value="<="></el-option>
                <el-option label="等于" value="="></el-option>
              </el-select>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="行毛利率" prop="itemGrossMargin">
            <el-input type="number" @change="itemGrossMarginBlur" v-model="formField.itemGrossMargin" :min="0" style="width: 100%">
              <el-select v-model="formField.grossMarginChar" slot="prepend" style="width: 80px;" placeholder="请选择">
                <el-option label="大于" value=">"></el-option>
                <el-option label="大于等于" value=">="></el-option>
                <el-option label="小于" value="<"></el-option>
                <el-option label="小于等于" value="<="></el-option>
                <el-option label="等于" value="="></el-option>
              </el-select>
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <footer class="align-right">
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button :loading="loading" @click="submit" type="primary">确定</el-button>
    </footer>
  </el-dialog>
</template>
<script>
import AllCatalog from '@/components/SearchFields/secondLevelDirectory';
import MaterialGroup from './ProductGroup';
import Brand from '@/components/SearchFields/brand';
import Customer from '@/components/SearchFields/consCustomer';
import Supplier from './Supplier';
import { typeOptions } from './constant'
import { formatPercentToNum, formatNumToPercent } from '../utils/index'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {}
    },
    actionType: {
      type: String,
      default: 'add'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  components: {
    AllCatalog,
    MaterialGroup,
    Brand,
    Customer,
    Supplier
  },
  created() {
    if (this.actionType === 'edit') {
      this.formField.supplier = this.detail.supplier
      this.formField.brand = this.detail.brand
      this.formField.category = this.detail.category ? Number(this.detail.category) : ''
      this.formField.itemUnrateAmo = this.detail.itemUnrateAmo
      this.formField.itemGrossMargin = formatNumToPercent(this.detail.itemGrossMargin)
      this.formField.type = this.detail.type
      this.formField.productGroup = this.detail.productGroup ? Number(this.detail.productGroup) : ''
      this.formField.customer = this.detail.customer
      this.formField.level = this.detail.level
      this.formField.amoChar = this.detail.amoChar
      this.formField.grossMarginChar = this.detail.grossMarginChar
      this.productGroupName = this.detail.productGroupName
      this.brandName = this.detail.brandName
      this.categoryName = this.detail.categoryName
      this.customerName = this.detail.customerName
    }
  },
  data: function() {
    return {
      formField: {
        brand: '',
        category: '',
        customer: '',
        productGroup: '',
        type: 2,
        level: 0,
        supplier: '',
        itemUnrateAmo: undefined,
        itemGrossMargin: undefined,
        grossMarginChar: '<',
        amoChar: '>'
      },
      formRules: {
        productGroup: [
          { required: true, message: '请选择物料组', trigger: 'change' }
        ],
        level: [
          { required: true, message: '请选择优先级', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择业务类型', trigger: 'change' }
        ],
        itemUnrateAmo: [
          { required: true, message: '请输入行采购未税金额', trigger: 'blur' }
        ],
        itemGrossMargin: [
          { required: true, message: '请输入行毛利率', trigger: 'blur' }
        ]
      },
      supplierName: '',
      brandName: '',
      categoryName: '',
      customerName: '',
      productGroupName: '',
      typeOptions: typeOptions.slice(1)
    }
  },
  computed: {
    showDlg: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const submitFiled = { ...this.formField }
          submitFiled.id = this.actionType === 'edit' ? this.detail.id : undefined
          submitFiled.itemGrossMargin = formatPercentToNum(this.formField.itemGrossMargin)
          this.$emit('submit', submitFiled)
        } else {
          return false
        }
      })
    },
    handleGetBrandName(name) {
      this.formField.brandName = name
    },
    handleGetCategoryName(name) {
      this.formField.categoryName = name
    },
    itemGrossMarginBlur(val) {
      console.log(val)
      if (val < 0) {
        this.formField.itemGrossMargin = 0
      }
    }
  }
}
</script>
