<template>
  <el-select
    :value="value"
    filterable
    clearable
    :multiple="multiple"
    :disabled="disabled"
    collapse-tags
    style="width: 100%"
    @change="handleChange"
    placeholder="请选择"
  >
    <el-option
      v-for="item in productList"
      :key="item.productGroupNum"
      :label="item.productGroupNum + ' ' + item.productGroupName"
      :value="item.productGroupNum"
    >
    </el-option>
  </el-select>
</template>

<script>
import { mapState } from 'vuex'
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: [Object, String, Number],
    disabled: { type: Boolean, default: false },
    multiple: { type: Boolean, default: false }
  },
  computed: {
    ...mapState({
      productList: state => state.mrp.productList
    })
  },
  created() {
    if (!this.productList.length) {
      this.$store.dispatch('mrp/queryProductGroup')
    }
  },
  methods: {
    handleChange (value) {
      this.$emit('change', value)
    }
  }
}
</script>
