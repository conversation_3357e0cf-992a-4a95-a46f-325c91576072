import { formatNumToPercent } from '../utils/index'
export const stockModelListColunms = [
  {
    label: '业务类型',
    prop: 'type',
    minWidth: 80,
    align: 'center',
    labelFormat: (ceilValue) => {
      return typeOptions.find((item) => {
        return item.value === ceilValue
      }).label
    },
    type: ''
  },
  {
    label: '优先级',
    prop: 'level',
    minWidth: 80,
    align: 'center',
    type: ''
  },
  {
    label: '物料组',
    prop: 'productGroupName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => {
      return ceilValue ? (row.productGroup || '') + ' ' + ceilValue : ''
    },
    type: ''
  },
  {
    label: '品牌',
    prop: 'brandName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => {
      return ceilValue ? (row.brand || '') + ' ' + ceilValue : ''
    },
    type: ''
  },
  {
    label: '后台二级类目',
    prop: 'categoryName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '供应商编码',
    prop: 'supplier',
    minWidth: 160,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => {
      return ceilValue ? ceilValue + ' ' + (row.supplierName || '') : ''
    },
    type: ''
  },
  {
    label: '客户编码',
    prop: 'customer',
    minWidth: 160,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => {
      return ceilValue ? ceilValue + ' ' + (row.customerName || '') : ''
    },
    type: ''
  },
  {
    label: '行采购未税金额',
    prop: 'itemUnrateAmo',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => {
      return String(ceilValue) ? row.amoChar + ' ' + ceilValue : ''
    },
    type: ''
  },
  {
    label: '行毛利率',
    prop: 'itemGrossMargin',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => {
      return String(ceilValue) ? row.grossMarginChar + ' ' + formatNumToPercent(ceilValue) + '%' : ''
    },
    type: ''
  },
  {
    label: '创建人',
    prop: 'creator',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '操作',
    prop: 'operate',
    minWidth: 80,
    align: 'center',
    fixed: 'right',
    type: 'custom',
    columnSlot: 'action'
  }
]

export const typeOptions = [
  {
    label: '全部',
    value: 0
  },
  {
    label: '差价',
    value: 1
  },
  {
    label: 'vpi',
    value: 2
  }
]

export const exportMapping = {
  'type': '业务类型',
  'level': '优先级',
  'productGroupName': '物料组',
  'brandName': '品牌',
  'categoryName': '后台类目',
  'supplier': '供应商编码',
  'customer': '客户编码',
  'itemUnrateAmo': '行采购未税金额',
  'itemGrossMargin': '行毛利率',
  'creator': '创建人'
}
