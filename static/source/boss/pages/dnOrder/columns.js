// import { orderLabelFilter } from '@/filters/index.js'
import { deepClone } from '@/utils/index.js'

const columns = [
  { label: 'OMS订单号', prop: 'omsOrderNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'OMS订单行号', prop: 'omsOrderItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SAP订单号', prop: 'sapOrderNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SAP订单行号', prop: 'sapOrderItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '外围订单号', prop: 'peripheralOrderNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户名称', prop: 'customerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单联系人', prop: 'orderContactName', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货联系人', prop: 'receiverName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单类型', prop: 'orderType', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单类型名称', prop: 'orderTypeName', type: 'default', visible: true, minWidth: '120px' },
  { label: '工厂', prop: 'factoryCode', type: 'default', visible: true, minWidth: '120px' },
  { label: '工厂名称', prop: 'factoryName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单创建者', prop: 'orderCreator', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服', prop: 'customerServiceName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服主管', prop: 'customerServiceSupName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服经理', prop: 'customerServiceManagerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售', prop: 'salesName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售经理', prop: 'salesManagerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售总监', prop: 'salesDirectorName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户订单号', prop: 'customerReferenceNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户订单行号', prop: 'customerOrderNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售订单日期', prop: 'soGmtCreate', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户要求交期', prop: 'requestedDeliveryDate', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单备注', prop: 'orderNote', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单行备注', prop: 'remark', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单原因', prop: 'orderedReason', type: 'default', visible: true, minWidth: '120px' },
  // { label: '折扣总额', prop: 'totalDiscount', type: 'default', visible: true, minWidth: '120px' },
  { label: '冻结状态', prop: 'deliveryFreezeStatus', type: 'default', visible: true, minWidth: '120px' },
  { label: '冻结原因', prop: 'deliveryFreeze', type: 'default', visible: true, minWidth: '120px' },
  // { label: '退货原因', prop: 'returnReasonType', type: 'default', visible: true, minWidth: '120px' },
  { label: '退货详情', prop: 'returnReason', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货省', prop: 'receiverProvince', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货市', prop: 'receiverCity', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货区', prop: 'receiverDistrict', type: 'default', visible: true, minWidth: '120px' },
  { label: '需求部门', prop: 'sapDemandDepartment', type: 'default', visible: true, minWidth: '120px' },
  { label: '责任人', prop: 'responsiblePerson', type: 'default', visible: true, minWidth: '120px' },
  { label: '自主下单标签', prop: 'orderLabel', type: 'default', visible: true, minWidth: '120px' },
  { label: '自动开票', prop: 'autoBilling', type: 'default', visible: true, minWidth: '120px' },
  { label: '凭邮件开票', prop: 'invoicingByMail', type: 'default', visible: true, minWidth: '120px' },
  { label: '后补订单', prop: 'backupOrder', type: 'default', visible: true, minWidth: '120px' },
  { label: 'EVM运营', prop: 'evmOperationSpecialist', type: 'default', visible: true, minWidth: '120px' },
  { label: '合并开票要求', prop: 'mergeBillingDemand', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户编码', prop: 'customerNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售组织', prop: 'salesOrganization', type: 'default', visible: true, minWidth: '120px' },
  { label: '分销渠道', prop: 'distributionChannel', type: 'default', visible: true, minWidth: '120px' },
  { label: '分销渠道名', prop: 'distributionChannelName', type: 'default', visible: true, minWidth: '120px' },
  { label: '所属集团编码', prop: 'customerParentId', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SKU', prop: 'skuNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '品牌', prop: 'brandName', type: 'default', visible: true, minWidth: '120px' },
  { label: '物料组', prop: 'productGroupName', type: 'default', visible: true, minWidth: '120px' },
  { label: '物料描述', prop: 'materialDescribe', type: 'default', visible: true, minWidth: '220px' },
  { label: '规格型号', prop: 'manufacturerMaterialNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '产品经理', prop: 'ownerId', type: 'default', visible: true, minWidth: '120px' },
  { label: '成品油', prop: 'ifOil', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料号', prop: 'customerMaterialNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单行数量', prop: 'quantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售单位', prop: 'sellingUnit', type: 'default', visible: true, minWidth: '120px' },
  { label: '含未税标识', prop: 'conditionTypeName', type: 'default', visible: true, minWidth: '120px' },
  { label: '未税单价', prop: 'untaxedUnitPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '未税总价', prop: 'discountTotalUntaxedPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '含税单价', prop: 'taxIncludedUnitPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '含税总价', prop: 'taxTotalPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '货币', prop: 'currency', type: 'default', visible: true, minWidth: '120px' },
  { label: '已交货数量', prop: 'deliveredQuantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '已交货金额', prop: 'clearedAmount', type: 'default', visible: true, minWidth: '120px' },
  { label: '发货详情', prop: 'deliveryDetail', columnSlot: 'deliveryDetail', type: 'custom', visible: true, minWidth: '120px' },
  // { label: '折扣单价', prop: 'discountAmount', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料描述', prop: 'customerMaterialName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户规格型号', prop: 'customerSpecificationModel', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料数量', prop: 'customerMaterialQuantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料单位', prop: 'customerMaterialUnit', type: 'default', visible: true, minWidth: '120px' },
  { label: '首个交期', prop: 'deliveryDate', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SAP交货单号', prop: 'sapDeliveryNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SAP交货单行号', prop: 'sapDeliveryItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'OMS交货单号', prop: 'omsDeliveryNo', type: 'default', visible: true, minWidth: '120px' },
  { label: 'OMS交货单行号', prop: 'omsDeliveryItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货单日期', prop: 'dnGmtCreate', type: 'default', visible: true, minWidth: '120px' },
  { label: '计划发货日期', prop: 'planDeliveryDate', type: 'default', visible: true, minWidth: '120px' },
  { label: '实际交货日期', prop: 'actualDeliveryDate', type: 'default', visible: true, minWidth: '120px' },
  { label: '是否需要隐藏logo', prop: 'hideLogo', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货单类型', prop: 'deliveryType', type: 'default', visible: true, minWidth: '120px' },
  { label: '交货单类型描述', prop: 'deliveryTypeDesc', type: 'default', visible: true, minWidth: '120px' },
  { label: '开票状态', prop: 'invoiceStatus', type: 'default', visible: true, minWidth: '120px' },
  { label: '已开票数量', prop: 'invoicedQuantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '已开票金额', prop: 'invoicedAmount', type: 'default', visible: true, minWidth: '120px' },
  { label: '未开票数量', prop: 'unInvoicedQuantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '未开票金额', prop: 'unInvoicedAmount', type: 'default', visible: true, minWidth: '120px' },
  { label: '毛重', prop: 'roughWeight', type: 'default', visible: true, minWidth: '120px' },
  { label: '净重', prop: 'netWeight', type: 'default', visible: true, minWidth: '120px' },
  { label: '过账状态', prop: 'statusMsg', type: 'default', visible: true, minWidth: '120px' },
  { label: '库存地点', prop: 'warehouseCode', type: 'default', visible: true, minWidth: '120px' },
  { label: '物流单号', prop: 'logisticsCode', type: 'default', visible: true, minWidth: '120px' },
  { label: '物流公司', prop: 'logisticsName', type: 'default', visible: true, minWidth: '120px' },
  { label: '领用人', prop: 'demandUser', type: 'default', visible: true, minWidth: '120px' }
  // { label: '销售订单行号', prop: 'sapItemNo', type: 'default', visible: true, minWidth: '120px' },
  // { label: '行项目类别', prop: 'itemType', type: 'default', visible: true, minWidth: '120px' },
  // { label: '商品经理', prop: 'productOwner', type: 'default', visible: true, minWidth: '120px' },
  // { label: '外围系统来源订单号', prop: 'orderNo', type: 'default', visible: true, minWidth: '160px' },
  // { label: '外围系统来源', prop: 'orderSource', type: 'default', visible: true, minWidth: '130px' },
  // { label: '所属集团名称', prop: 'companyName', type: 'default', visible: true, minWidth: '120px' },
  // { label: '订单联系人姓名', prop: 'orderContactName', type: 'default', visible: true, minWidth: '120px' },
  // { label: '订单联系人电话', prop: 'orderContactPhone', type: 'default', visible: true, minWidth: '120px' },
  // { label: '订单省', prop: 'orderContactProvince', type: 'default', visible: true, minWidth: '120px' },
  // { label: '订单市', prop: 'orderContactCity', type: 'default', visible: true, minWidth: '120px' },
  // { label: '订单区', prop: 'orderContactDistrict', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收货联系人姓名', prop: 'receiverName', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收货联系人电话', prop: 'receiverPhone', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收票联系人', prop: 'receivingInvoiceContact', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收票联系人姓名', prop: 'receivingInvoiceName', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收票联系人电话', prop: 'receivingInvoicePhone', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收票省', prop: 'receivingInvoiceProvince', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收票市', prop: 'receivingInvoiceCity', type: 'default', visible: true, minWidth: '120px' },
  // { label: '收票区', prop: 'receivingInvoiceDistrict', type: 'default', visible: true, minWidth: '120px' },
  // { label: '含税未税标识文本', prop: 'conditionType', type: 'default', visible: true, minWidth: '120px' },
  // { label: '订单数量', prop: 'quantity', type: 'default', visible: true, minWidth: '120px' },
  // { label: '箱规', prop: 'productPackage', type: 'default', visible: true, minWidth: '120px' },
  // { label: '税额', prop: 'tax', type: 'default', visible: true, minWidth: '120px' },
  // { label: '订单描述', prop: 'headerText', type: 'default', visible: true, minWidth: '120px' },
  // { label: '客户参考日期', prop: 'customerReferenceDate', type: 'default', visible: true, minWidth: '120px' },
  // { label: 'Lead Time', prop: 'plannedDeliveryDays', type: 'default', visible: true, minWidth: '150px' },
  // // { label: '订单数量', prop: 'quantity', type: 'default', visible: true, minWidth: '120px' },
  // { label: '交货状态', prop: 'deliveryStatus', type: 'default', visible: true, minWidth: '120px' },
  // { label: '未交货数量', prop: 'nonClearedQty', type: 'default', visible: true, minWidth: '120px' },
  // { label: '未交货金额', prop: 'nonDeliveryPrice', type: 'default', visible: true, minWidth: '120px' },
  // // { label: '客服总监', prop: 'customerServiceDirectorName', type: 'default', visible: true, minWidth: '120px' },
  // { label: '参考销售订单号', prop: 'referenceOrderNo', type: 'default', visible: true, minWidth: '120px' },
  // { label: '参考订单行项目号', prop: 'referenceOrderItem', type: 'default', visible: true, minWidth: '130px' },
  // { label: '客户订单序列号', prop: 'customerItemNo', type: 'default', visible: true, minWidth: '120px' },
  // // { label: '库存地点名称', prop: 'positionName', type: 'default', visible: true, minWidth: '120px' },
  // { label: '当前库存', prop: 'currentNum', type: 'default', visible: true, minWidth: '120px' },
  // { label: '仓库修改原因', prop: 'positionModifyReason', type: 'default', visible: true, minWidth: '120px' },
  // { label: '仓库修改详情', prop: 'positionModifyDetail', type: 'default', visible: true, minWidth: '120px' },
  // { label: 'VPI物料', prop: 'vpiMateriel', type: 'default', visible: true, minWidth: '120px' },
  // { label: '叫料客户', prop: 'bidCustomer', type: 'default', visible: true, minWidth: '120px' },
  // { label: '自动发货', prop: 'autoDelivery', type: 'default', visible: true, minWidth: '120px' },
  // { label: '自动分批', prop: 'autoBating', type: 'default', visible: true, minWidth: '120px' },
  // { label: '自主或代理下单标签', prop: 'orderLabel', type: 'default', visible: true, minWidth: '150px', labelFormat (val) { return orderLabelFilter(val) } },
  // { label: '签收单', prop: 'signStatus', type: 'default', visible: true, minWidth: '120px' }
]

// [
//   {
//     label: '销售订单信息',
//     prop: 'SOInfo',
//     visible: true,
//     type: 'parent',
//     children: SOcolumns
//   },
//   {
//     label: '采购信息',
//     prop: 'POInfo',
//     visible: true,
//     type: 'parent',
//     children: POcolumns
//   }
// ]

export default function getColumns () {
  return deepClone(columns)
}
