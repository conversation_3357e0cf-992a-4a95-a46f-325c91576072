<template>
  <div class="construction-edit">
    <div class="table">
      <div class="title">
        <span>
          申请单号：{{title.id}}，
          申请日期：{{title.submitDate}}，
          所属集团：{{title.blocName}}，
          申请人：{{title.submitUser}}
        </span>
        <div class="edit">
          <el-popover
            placement="bottom"
            style="margin-right: 20px;"
            width="200"
            trigger="hover"
            content="点击后基础数据内会生成所需完善草稿，补充完整并启用后再次点击会自动填充供应商和采购价">
            <el-button slot="reference" type="default" icon="el-icon-question" :loading="loading.syncOrder" @click="syncOrder">自动填充</el-button>
          </el-popover>
          <el-button type="primary" :disabled="!selections.length" icon="el-icon-edit" @click="showBatchEdit = true">批量编辑</el-button>
        </div>
      </div>
      <el-table border v-loading="loading.tableLoading" :data="tableList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" fixed="left"/>
        <el-table-column
          v-for="(item,index) in columns"
          :key="item.id || index"
          align="center"
          show-overflow-tooltip
          :label="item.label"
          :prop="item.prop"
          :width="item.width">
          <template slot-scope="{row}">
            <span v-if="item.label === '采购方式'">
              {{enums.purchaseMethod[row[item.prop]]}}</span>
            <span v-else-if="item.prop === 'provinceName'">
              {{trimString(row.provinceName, row.cityName, row.areaName)}}</span>
            <span v-else>{{row[item.prop]}}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip label="操作" fixed="right">
          <template slot-scope="{row}">
            <el-button type="text" @click="showEditDialog(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="manage">
      <el-button type="danger" @click="cancel">取消</el-button>
      <el-button type="primary" :loading="loading.submitLoading" @click="submitEdit">提交</el-button>
    </div>
    <el-dialog
      width="800px"
      title="申请单编辑"
      :visible.sync="showDialog"
      :before-close="beforeClose"
      :show-close="false">
      <div class="form">
        <el-form
          ref="editForm"
          :model="editData"
          label-width="160px"
          label-position="right">
        <el-row>
          <el-col :span="12">
            <el-form-item label="自营分润：" prop="profitShare">
              <el-input v-model="editData.profitShare" clearable placeholder="请输入自采所需分成的供应商"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商：" prop="providerName">
              <Supplier v-if="showSearchSupplier" placeholder="请输入供应商名称" getLabel style="width:100%" @change="handleProviderChange" v-model="editData.providerName" />
              <el-select style="width:100%" v-else placeholder="请选择" v-model="editData.providerName" @change="handleProviderSelect">
                <el-option
                  v-for="(item,index) in editData.achitechiveProviderInfoList"
                  :key="index"
                  :label="item.providerName"
                  :value="item.providerName"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购方式：" prop="purchaseMethod">
              <el-select style="width:100%" clearable v-model.trim="editData.purchaseMethod" @change="purchaseMethodChange">
                <el-option
                  v-for="(item,index) in purchaseMethodOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商编码：" prop="providerId">
              <el-input disabled v-model="editData.providerId" placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="正常扣点：" prop="deductionRage">
              <el-input disabled v-model="editData.deductionRage" placeholder=""/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="进项税点：" prop="taxInRate">
              <el-input disabled v-model="editData.taxInRate" placeholder=""/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="正常采购价：" prop="purchasePrice">
              <el-input disabled v-model="editData.purchasePrice" placeholder=""/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请低毛利%：" prop="vipDeductionRage">
              <el-input-number @change="getPrice" style="width:100%" :min="-100" :max="100" v-model="editData.vipDeductionRage" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请原因：" prop="vipMsg">
              <el-input v-model="editData.vipMsg" placeholder="请填写OA低毛利审批流程单号"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="低毛利采购价：" prop="vipPurchasePrice">
              <el-input disabled v-model="editData.vipPurchasePrice" :placeholder="editData.purchaseMethod == 0 ?'请输入':''" />
            </el-form-item>
          </el-col>
        </el-row>
        </el-form>
      </div>
      <div class="dialog-button">
        <el-button type="primary" @click="submitDialog" :loading="loading.submitLoading">确认</el-button>
        <el-button @click="closeEditDialog">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="800px"
      title="批量编辑(请注意，提交后选中订单行将会全部被修改)"
      :visible.sync="showBatchEdit"
      :before-close="beforeClose"
      :show-close="false">
      <div class="form">
        <el-form
          ref="editForm"
          :model="batchEditData"
          label-width="160px"
          label-position="right">
        <el-row>
          <el-col :span="12">
            <el-form-item label="采购方式：" prop="purchaseMethod">
              <el-select style="width:100%" clearable v-model.trim="batchEditData.purchaseMethod" @change="purchaseMethodChangeBatch">
                <el-option
                  v-for="(item,index) in purchaseMethodOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商：" prop="providerName">
              <Supplier
                placeholder="请输入供应商名称"
                getLabel
                style="width:100%"
                @change="handleProviderChangeBatch"
                v-model="batchEditData.providerName" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="自营分润：" prop="profitShare">
              <el-input v-model="batchEditData.profitShare" clearable placeholder="请输入自采分利供应商"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商编码：" prop="providerId">
              <el-input disabled v-model="batchEditData.providerId" placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请低毛利%：" prop="vipDeductionRage">
              <el-input-number @blur="getPrice" style="width:100%" :min="0" :max="100" v-model="batchEditData.vipDeductionRage" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请原因：" prop="vipMsg">
              <el-input v-model="batchEditData.vipMsg" placeholder="请填写OA低毛利审批流程单号"/>
            </el-form-item>
          </el-col>
        </el-row>
        </el-form>
      </div>
      <div class="dialog-button">
        <el-button type="primary" @click="submitBatch" :loading="loading.submitLoading">确认修改</el-button>
        <el-button @click="closeEditDialog">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { submitItems, getEditInfo, syncOrder, getPrice } from '@/api/construction'
import Supplier from '@/components/SearchFields/consSupplier'
import { trimString } from '@/utils/index'
import { safeRun } from '../../utils'
export default {
  name: 'constructionEdit',
  components: {
    Supplier
  },
  data () {
    return {
      title: {
        id: '',
        submitDate: '',
        blocName: '',
        submitUser: ''
      },
      tableList: [],
      loading: {
        tableLoading: false,
        syncOrder: false,
        submitLoading: false
      },
      showDialog: false,
      showBatchEdit: false,
      editData: {},
      batchEditData: {
        purchaseMethod: '',
        providerName: '',
        profitShare: '',
        providerId: '',
        vipDeductionRage: undefined,
        vipMsg: ''
      },
      rawEditData: {},
      purchaseMethodOptions: [
        { label: '自采', value: 0 },
        { label: '他采', value: 1 }
      ],
      enums: {
        purchaseMethod: {
          0: '自采',
          1: '他采'
        }
      },
      selections: [],
      columns: [
        { label: '创建日期', prop: 'createDate', width: '100px' },
        { label: '创建时间', prop: 'createTime' },
        { label: '采购组描述', prop: 'purchaseGroupName' },
        { label: '物料组描述', prop: 'materialGroupName' },
        { label: '所属项目', prop: 'belongProject' },
        { label: '客户订单号', prop: 'customerOrderNo', width: '100px' },
        { label: '集团', prop: 'blocName' },
        { label: '客户名称', prop: 'customerName', width: '100px' },
        { label: '收货人', prop: 'receiverName' },
        { label: '收货人电话', prop: 'receiverPhone', width: '100px' },
        { label: '销售订单', prop: 'orderNo', width: '100px' },
        { label: '收货省市区', prop: 'provinceName' },
        { label: '订单行号', prop: 'itemNo' },
        { label: '物料编码', prop: 'materialNo' },
        { label: '物料描述', prop: 'materialName' },
        { label: '销售单位', prop: 'saleUnit' },
        { label: '订单数量', prop: 'materialCount' },
        { label: '含税单价', prop: 'taxInPrice' },
        { label: '销售', prop: 'sellerName' },
        { label: '订单备注', prop: 'remark' },
        { label: '自营分润', prop: 'profitShare' },
        { label: '供应商', prop: 'providerName', width: '100px' },
        { label: '采购方式', prop: 'purchaseMethod' },
        { label: '供应商编号', prop: 'providerId' },
        { label: '进项税点', prop: 'taxInRate' },
        { label: '正常采购价', prop: 'purchasePrice' },
        { label: '正常扣点', prop: 'deductionRage' },
        { label: '低毛利采购价', prop: 'vipPurchasePrice', width: '120px' },
        { label: '申请低毛利%', prop: 'vipDeductionRage', width: '100px' },
        { label: '申请原因', prop: 'vipMsg' }
      ]
    }
  },
  computed: {
    purchaseMethodStatus() {
      return this.editData.purchaseMethod === 0
    },
    showSearchSupplier () {
      if (this.editData.purchaseMethod === 0) return true
      if (this.editData.achitechiveProviderInfoList && this.editData.achitechiveProviderInfoList.length > 0) return false
      return true
    }
  },
  mounted () {
    this.initData()
  },
  methods: {
    trimString,
    handleSelectionChange (rows) {
      this.selections = rows
    },
    getPrice () {
      if (!this.editData.vipDeductionRage) return
      console.log('trigger getPrice')
      const data = {
        deductionRage: this.editData.vipDeductionRage,
        taxInPrice: this.editData.taxInPrice,
        materialCount: this.editData.materialCount,
        taxInRate: this.editData.taxInRate
      }
      for (let prop in data) {
        if (!data[prop]) return
      }
      getPrice(data)
        .then(res => {
          if (res) {
            this.editData.vipPurchasePrice = res
            this.$forceUpdate()
          }
        })
    },
    syncOrder () {
      this.loading.syncOrder = true
      const data = new FormData()
      data.append('orderNos', this.tableList.map(row => row.orderNo).join(','))
      syncOrder(data)
        .finally(() => {
          this.loading.syncOrder = false
          this.initData()
        })
    },
    initScroll () {
      try {
        document.querySelector('.el-table__body-wrapper').scrollLeft = 1e5
      } catch (err) {}
    },
    purchaseMethodChange (method) {
      // 自采0 他采1
      console.log(method)
      this.editData.providerId = ''
      this.editData.providerName = ''
      if (method === 0) {
        this.editData.taxInRate = ''
        this.editData.purchasePrice = ''
        this.editData.deductionRage = ''
        this.editData.vipDeductionRage = undefined
        this.editData.vipPurchasePrice = ''
        safeRun(() => {
          const item = this.editData.achitechiveProviderInfoList
          if (item.length) {
            this.editData.profitShare = item[0].providerId + item[0].providerName
          }
        })
      }
      if (method === 1) {
        this.editData.vipPurchasePrice = ''
      }
    },
    purchaseMethodChangeBatch (method) {
      // 自采0 他采1
      if (method === 0) {
        this.batchEditData.providerName = ''
        this.batchEditData.profitShare = ''
        this.batchEditData.providerId = ''
        this.batchEditData.vipDeductionRage = undefined
        this.batchEditData.vipMsg = ''
      }
      console.log(method)
    },
    closeEditDialog () {
      this.showDialog = false
      this.showBatchEdit = false
      this.editData = {}
      this.rawEditData = {}
      this.batchEditData = {
        purchaseMethod: '',
        providerName: '',
        profitShare: '',
        providerId: '',
        vipDeductionRage: undefined,
        vipMsg: ''
      }
    },
    handleProviderSelect (name) {
      console.log(name)
      safeRun(() => {
        const item = this.editData.achitechiveProviderInfoList.filter(item => item.providerName === name)[0]
        this.editData.providerId = item.providerId
        this.editData.taxInRate = item.taxInRate
        this.editData.purchasePrice = item.purchasePrice
        this.editData.deductionRage = item.deductionRage
      })
    },
    handleProviderChange (val, obj) {
      if (!obj) obj = {}
      this.editData.providerId = obj.providerNo
      if (this.editData.purchaseMethod === 0) {
        if (obj.providerNo) {
          this.editData.profitShare = obj.providerNo + obj.providerName
        }
      }
    },
    handleProviderChangeBatch (val, obj) {
      if (!obj) obj = {}
      this.batchEditData.providerId = obj.providerNo
      if (this.batchEditData.purchaseMethod === 0) {
        if (obj.providerNo) {
          this.batchEditData.profitShare = obj.providerNo + obj.providerName
        }
      }
    },
    beforeClose (done) {
      done && done()
    },
    batchGetPrice (row, vipDeductionRage) {
      console.log('trigger batchGetPrice')
      const data = {
        deductionRage: vipDeductionRage,
        taxInPrice: row.taxInPrice,
        materialCount: row.materialCount,
        taxInRate: row.taxInRate
      }
      for (let prop in data) {
        if (!data[prop]) return
      }
      getPrice(data)
        .then(res => {
          if (res) {
            this.$set(row, 'vipPurchasePrice', res)
            // this.$forceUpdate()
          }
        })
    },
    submitBatch () {
      this.selections.forEach(row => {
        for (let prop in this.batchEditData) {
          row[prop] = this.batchEditData[prop]
        }
      })
      if (this.batchEditData.vipDeductionRage) {
        Promise.all(this.selections.map(row => this.batchGetPrice(row, this.batchEditData.vipDeductionRage)))
      }
      if (this.batchEditData.purchaseMethod === 0) {
        this.selections.forEach(row => {
          row.providerName = '自营'
          row.providerId = ''
          row.taxInRate = ''
          row.purchasePrice = ''
          row.deductionRage = ''
          row.vipPurchasePrice = ''
          row.vipDeductionRage = ''
        })
      }
      console.log(this.batchEditData, this.selections, this.tableList)
      this.closeEditDialog()
    },
    showEditDialog (row) {
      this.showDialog = true
      this.editData = { ...row }
      this.rawEditData = { ...row }
    },
    formatRoute (route) {
      console.log(route)
      let ret = []
      safeRun(() => {
        ret = route.split(',')
          .map(s => s.trim())
          .filter(e => e)
          .map(x => ({
            orderNo: x.split('-')[0],
            itemNo: x.split('-')[1]
          }))
      })
      return ret
    },
    initData () {
      console.log(this.$route.params.id)
      this.loading.tableLoading = true
      const data = this.formatRoute(this.$route.params.id)
      // data.append('itemIds', this.formatRoute(this.$route.params.id))
      getEditInfo(data)
        .then(res => {
          if (res) {
            this.tableList = res.achitechiveItemsBOS
            delete res.achitechiveItemsBOS
            this.title = res
          }
        })
        .finally(() => {
          this.loading.tableLoading = false
        })
    },
    cancel () {
      this.$closeTag(this.$route.path)
    },
    submitDialog () {
      this.showDialog = false
      const idx = this.tableList.map(item => item.id).indexOf(this.editData.id)
      this.tableList.splice(idx, 1, this.editData)
    },
    formatData (list) {
      let ret = {}
      let tmp = list.map(item => {
        const data = {
          id: item.id,
          deductionRage: item.deductionRage,
          profitShare: item.profitShare,
          providerId: item.providerId,
          providerName: item.providerName,
          purchaseMethod: item.purchaseMethod,
          purchasePrice: item.purchasePrice,
          taxInRate: item.taxInRate,
          vipDeductionRage: item.vipDeductionRage,
          vipMsg: item.vipMsg,
          vipPurchasePrice: item.vipPurchasePrice
        }
        if (data.purchaseMethod === 0) {
          data.providerName = '自营'
          data.providerId = ''
        }
        return data
      })
      ret = {
        ...this.title,
        achitechiveItemsDetailsReq: tmp
      }
      return ret
    },
    submitEdit () {
      if (this.tableList.some(row => {
        if (row.purchaseMethod === 1 && (!row.deductionRage || !row.providerName)) return true
      })) {
        return this.$message.error('采购方式为他采，未填写供应商、正常扣点。请补充对应供应商基础数据后再提交！')
      }
      if (this.tableList.some(row => {
        if (row.purchaseMethod !== 1 && row.purchaseMethod !== 0) return true
      })) {
        return this.$message.error('采购方式为必填！')
      }
      this.loading.submitLoading = true
      this.loading.tableLoading = true
      const data = this.formatData(this.tableList)
      this.closeEditDialog()
      submitItems(data)
        .then(res => {
          if (res) {
            this.$confirm('提交成功！', '操作提示', {
              confirmButtonText: '跳转到提交详情',
              cancelButtonText: '关闭',
              type: 'success'
            }).then(() => {
              setTimeout(() => {
                this.$router.push('/construction/sale#recordAndSearch')
              }, 100)
            }).finally(() => {
              this.$closeTag(this.$route.path)
            })
          }
        })
        .finally(() => {
          this.loading.submitLoading = false
          this.loading.tableLoading = false
        })
    }
  }
}
</script>
<style lang="scss" scoped>
.construction-edit{
  margin: 10px;
  margin-right: 25px;
  .table{
    padding: 5px;
    .title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .button-group {
      float: right;
      margin-bottom: 10px;
      margin-right: 5px;
    }
  }
  .manage {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    button{
      margin-right: 30px;
      margin-left: 30px;
    }
  }
  .dialog-button{
    display: flex;
    justify-content: center;
    button{
      margin-right: 20px;
      margin-left: 20px;
    }
  }
}
</style>
