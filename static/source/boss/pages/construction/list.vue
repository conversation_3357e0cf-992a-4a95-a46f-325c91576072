<template>
  <div class="construction-list">
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="140px"
      class="construction-form"
      label-position="right">
      <el-row>
        <el-col :span="7">
          <el-form-item label="客户名称：" prop="customerName">
            <RemoteCustomer
              style="width:100%"
              getLabel
              headName="客户"
              v-model="searchForm.customerName"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="项目名称：" prop="projectName">
            <el-input
              v-model="searchForm.projectName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="销售：" prop="sellerName">
            <el-input
              v-model="searchForm.sellerName"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="3" style="padding-left:10px;">
          <el-button
            type="primary"
            icon="el-icon-search"
            :loading="loading.searchLoading"
            @click="handleSubmit"
            >查询</el-button>
        </el-col>
        <el-col :span="7">
          <el-form-item label="供应商：" prop="providerNameCode">
            <el-input
              v-model="searchForm.providerNameCode"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="收货省市区：" prop="provinceArray">
            <ProvinceCascader :checkStrictly="true" @provinceChange="provinceChange" style="width:100%" v-model="searchForm.provinceArray" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="订单收货人：" prop="contactName">
            <Contact getLabel style="width:100%" v-model="searchForm.contactName" />
          </el-form-item>
        </el-col>
        <el-col :span="3" style="padding-left:10px;">
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-col>
        <el-col :span="7">
          <el-form-item label="联系人电话：" prop="contactPhone">
            <el-input
              v-model.trim="searchForm.contactPhone"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="集团：" prop="blocNumber">
            <el-select style="width:100%" clearable v-model.trim="searchForm.blocNumber">
              <el-option
                v-for="(item,index) in blocOptions"
                :key="index"
                :label="item.blocName"
                :value="item.blocNumber"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="项目状态：" prop="status">
            <el-select style="width:100%" clearable v-model.trim="searchForm.status">
              <el-option
                v-for="(item,index) in statusOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7">
          <el-form-item label="未维护：" prop="sellerInfo">
            <el-select style="width:100%" clearable v-model.trim="searchForm.sellerInfo">
              <el-option
                v-for="(item,index) in sellerInfoOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="详细地址：" prop="addressDetail">
            <el-input
              v-model.trim="searchForm.addressDetail"
              placeholder="请输入关键字"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table">
      <div class="button-group">
        <el-button type="primary" @click="showDialog = true">新建项目</el-button>
      </div>
      <el-table border :data="tableList" v-loading="loading.tableLoading" :height="`calc(100vh - 480px)`">
        <el-table-column show-overflow-tooltip align="center" label="项目编号" type="index" />
        <el-table-column show-overflow-tooltip align="center" label="项目名称" width="200px" prop="projectName" />
        <el-table-column show-overflow-tooltip align="center" label="集团" width="200px" prop="blocName" />
        <el-table-column show-overflow-tooltip align="center" label="客户编码" prop="customerNumber" />
        <el-table-column show-overflow-tooltip align="center" label="客户名称" width="160px" prop="customerName" />
        <el-table-column show-overflow-tooltip align="center" label="项目属性" prop="projectAttribute" />
        <el-table-column show-overflow-tooltip align="center" label="所属二、三级公司" width="200px" prop="belongCompany2o3" />
        <el-table-column show-overflow-tooltip align="center" label="省市区" prop="provinceName" width="200px">
          <template slot-scope="{row}">
            {{trimString(row.provinceName, row.cityName, row.areaName)}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="详细地址" width="300px" prop="addressDetail" />
        <el-table-column show-overflow-tooltip align="center" label="订单收货人" prop="contactName" />
        <el-table-column show-overflow-tooltip align="center" label="收货人电话" prop="contactPhone" />
        <el-table-column show-overflow-tooltip align="center" label="供应商编码" prop="providerId" />
        <el-table-column show-overflow-tooltip align="center" label="是否供应商报备项目" width="160px" prop="isReported">
          <template slot-scope="{row}">
            {{ enums.isReported[row.isReported] }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="供应商" width="160px" prop="providerName" />
        <el-table-column show-overflow-tooltip align="center" label="销售" prop="sellerName" />
        <el-table-column show-overflow-tooltip align="center" label="销售经理" prop="sellerManagerName" />
        <el-table-column show-overflow-tooltip align="center" label="进项税点" prop="taxInRate">
          <template slot-scope="{row}">
            {{ row.taxInRate && row.taxInRate + '%' }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="扣点" prop="deductionRage">
          <template slot-scope="{row}">
            {{ row.deductionRage && row.deductionRage + '%' }}
          </template>
        </el-table-column>
        <el-table-column  show-overflow-tooltip align="center" label="下单人" prop="deductionRage">
          <template slot-scope="{row}">
              {{row.orderCreator}}
         </template>
        </el-table-column>
        <el-table-column  show-overflow-tooltip align="center" label="项目备注" prop="projectNotes">
          <template slot-scope="{row}">
              {{row.projectNotes}}
         </template>
        </el-table-column>

        <el-table-column show-overflow-tooltip align="center" fixed="right" label="项目状态" prop="status">
          <template slot-scope="{row}">
            {{ enums.status[row.status] }}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" fixed="right" label="操作" prop="manage" width="250">
          <template slot-scope="{row}">
            <el-button type="text" @click="editRow(row)" v-if="hasPremission('编辑',row)">编辑</el-button>
            <el-button  type="text" @click="changeStatus('启用',row)" v-if="hasPremission('启用',row)">启用</el-button>
            <el-button  type="text" @click="changeStatus('停用',row)" v-if="hasPremission('停用',row)">停用</el-button>
            <el-button  type="text" @click="changeStatus('作废',row)" v-if="hasPremission('作废',row)">作废</el-button>
            <el-button  type="text" @click="detail(row)" v-if="hasPremission('查看',row)">查看</el-button>
            <el-button  type="text" @click="deleteRow(row)" v-if="hasPremission('删除',row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagi" v-if="pageInfo.total > 10">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageInfo.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>
    <ListDialog v-if="showDialog" :detailData="createData" @close="closeDialog"/>
      <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose">
    <el-form label-position="top" label-width="80px" :model="form" ref="form" :rules="rules"  >
       <el-form-item prop="stopOrVoidreason" :label="`${formItemLabelVarial}原因`">
        <el-select v-model="form.stopOrVoidreason" style="width:100%">
          <el-option v-for="(item,index ) in stopOrVoidreasonListMap[formItemLabelVarial]" :key="index" :label="item.value" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="stopOrVoidexplain" :label="`${formItemLabelVarial}说明`">
        <el-input  v-model="form.stopOrVoidexplain"></el-input>
      </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
     </span>
    </el-dialog>
  </div>
</template>
<script>
import RemoteCustomer from '@/components/SearchFields/consCustomer'
import Contact from '@/components/SearchFields/consContact'
import ProvinceCascader from '@/components/SearchFields/provinceCascaderAsync'
import { getConfigList, modifyStatus, deleteRow } from '@/api/construction'
import { stopOrVoidreasonListMap } from './help'
import ListDialog from './components/listDialog'
import { trimString, safeRun } from '@/utils/index'
import { mapState } from 'vuex'
export default {
  name: 'constructionList',
  components: {
    RemoteCustomer,
    ListDialog,
    Contact,
    ProvinceCascader
  },
  data () {
    return {
      dialogVisible: false,
      showDialog: false,
      createData: null,
      // currentStatus: '',
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      searchForm: {
        customerName: '',
        projectName: '',
        sellerName: '',
        providerNameCode: '',
        contactName: '',
        contactPhone: '',
        blocName: '',
        blocNumber: '',
        sellerInfo: '',
        provinceArray: []
      },
      loading: {
        searchLoading: false,
        tableLoading: false
      },
      tableList: [],
      statusOptions: [
        { label: '草稿', value: 2 },
        { label: '待审批', value: 4 },
        { label: '已启用', value: 1 },
        { label: '已停用', value: 0 },
        { label: '已作废', value: 3 }
      ],
      sellerInfoOptions: [
        { label: '无销售', value: 0 },
        { label: '无供应商信息', value: 2 }
      ],
      // enums: {
      //   status: {
      //     0: '未启用',
      //     1: '已启用',
      //     2: '草稿',
      //     3: '停用'
      //   },
      //   isReported: {
      //     0: '否',
      //     1: '是'
      //   }
      // }
      enums: {
        status: {
          0: '已停用',
          1: '已启用',
          2: '草稿',
          3: '已作废',
          4: '待审批'
        },
        isReported: {
          0: '否',
          1: '是'
        }
      },
      form: {
        stopOrVoidreason: '',
        stopOrVoidexplain: ''

      },
      rules: {
        stopOrVoidreason: [
          {
            required: true, message: '请输入', trigger: 'blur'
          }
        ]
      },
      formItemLabelVarial: '停用',
      stopOrVoidreasonListMap,
      rowId: '',
      operatetionName: ''

    }
  },
  computed: {
    ...mapState({
      blocOptions: state => state.construction.blocOptions,
      menu: state => state.menu
    }),

    // 获取操作权限
    getPermissions () {
      return this.menu.filter(menu => menu.name === '建筑项目&订单管理')[0].children[0].children
    }
  },
  methods: {
    trimString,
    provinceChange (array) {
      safeRun(() => {
        if (!array || array.length === 0) {
          this.searchForm.provinceCode = ''
          this.searchForm.cityCode = ''
          this.searchForm.areaCode = ''
        }
        this.searchForm.provinceCode = array[0].value
        this.searchForm.cityCode = (array[1] && array[1].value) || ''
        this.searchForm.areaCode = (array[2] && array[2].value) || ''
      })
    },
    handleCurrentChange (val) {
      this.pageInfo.pageNo = val
      this.handleSearch()
    },
    handleSizeChange (val) {
      this.pageInfo.pageSize = val
      this.handleSearch()
    },
    changeStatusText (row) {
      let text = ''
      if (row.status === 0) text = '启用'
      if (row.status === 1) text = '停用'
      return text
    },
    closeDialog (refresh) {
      this.showDialog = false
      this.createData = null
      if (refresh === 'refresh') {
        this.handleSearch()
      }
    },

    hasPremission(premissionName, { status, enableStatus }) {
      // const ownerPermission = this.getPermissions.filter(premission => premission.name === premissionName).length > 0
      const ownerPermission = true
      //  项目状态：草稿（2），  操作：编辑,删除
      // 项目状态：待审批(4)， 操作：编辑,启用,删除
      // 项目状态：已启用(1)， 操作：停用,作废
      // 项目状态：已停用(0)， 操作：编辑, 启用,作废
      // 项目状态：已作废(3)， 操作：查看
      if (ownerPermission && ~['编辑', '删除'].indexOf(premissionName) && status === 2) {
        return true
      }
      if (ownerPermission && ((~['编辑', '启用', '删除'].indexOf(premissionName) && enableStatus === 0) || (~['编辑', '启用'].indexOf(premissionName) && enableStatus === 1)) && status === 4) {
        return true
      }
      if (ownerPermission && ~['停用', '作废', '查看'].indexOf(premissionName) && status === 1) {
        return true
      }
      if (ownerPermission && ~['编辑', '启用', '作废'].indexOf(premissionName) && status === 0) {
        return true
      }
      if (ownerPermission && premissionName === '查看' && status === 3) {
        return true
      }

      return false
    },
    editRow (row) {
      this.createData = row
      this.showDialog = true
    },
    detail(row) {
      this.createData = row
      this.showDialog = true
    },
    deleteRow(row) {
      this.$confirm('是否确认删除该项目记录，删除后将无法恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteRow(row.status, row.id).then((res) => {
          if (res && res.code === 200) {
            this.handleSearch()
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    changeStatus (operatetionName, row) {
      let status = 0
      const id = row.id
      if (operatetionName === '启用' && (row.status === 0 || row.status === 4)) status = 1
      if (operatetionName === '停用' && row.status === 1) status = 0
      if (operatetionName === '作废' && (row.status === 0 || row.status === 1)) {
        status = 3
      }
      this.rowId = row.id
      this.operatetionName = operatetionName
      this.status = status
      if (operatetionName === '停用') {
        this.formItemLabelVarial = '停用'
        this.dialogVisible = true
      }
      if (operatetionName === '作废') {
        this.formItemLabelVarial = '作废'
        this.dialogVisible = true
      }
      if (operatetionName === '启用') {
        this.$confirm('是否确认启用改项目记录，请确认项目信息已填写完整', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.uploadStatus({ status, id }, id)
        })
      }
    },
    async uploadStatus(params, id) {
      let res = await modifyStatus(params, id)
      if (res) {
        this.handleSearch()
        this.$message({
          type: 'success',
          message: `${this.operatetionName}成功!`
        });
      }
    },
    handleClose(done) {
      this.$refs['form'].resetFields();
      done();
    },
    cancel() {
      this.$refs['form'].resetFields();
      this.dialogVisible = false
    },
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = { id: this.rowId, status: this.status }
          Object.assign(params, this.form)
          this.uploadStatus(params, this.rowId)
          this.dialogVisible = false
          this.$refs['form'].resetFields();
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleReset () {
      this.$refs['searchForm'].resetFields()
      this.searchForm.provinceCode = ''
      this.searchForm.cityCode = ''
      this.searchForm.areaCode = ''
    },
    handleSubmit () {
      this.pageInfo.pageNo = 1
      this.handleSearch()
    },
    formatParams (params) {
      const p = { ...params }
      delete p.provinceArray
      return p
    },
    handleSearch () {
      this.loading.searchLoading = true
      this.loading.tableLoading = true
      const params = {
        ...this.searchForm,
        pageSize: this.pageInfo.pageSize,
        pageNo: this.pageInfo.pageNo
      }
      const data = this.formatParams(params)
      getConfigList(data)
        .then(res => {
          if (res) {
            this.pageInfo.total = res.total
            this.pageInfo.pageNo = res.pageNum
            this.tableList = res.list
          }
        })
        .finally(() => {
          this.loading.searchLoading = false
          this.loading.tableLoading = false
        })
    },
    init () {
      if (this.blocOptions && this.blocOptions.length === 0) {
        this.$store.dispatch('construction/getBlocOptions')
      }
      this.handleSearch()
    }

  },
  created () {
    this.init()
  }
}
</script>
<style lang="scss" scoped>
.construction-list{
  margin: 10px;
  margin-right: 25px;
  overflow: hidden;
  .construction-form {
    height: 220px;
  }
  .table{
    padding: 5px;
    // height: calc(100% - 260px) !important;
    .button-group {
      float: right;
      margin-bottom: 10px;
      margin-right: 5px;
    }
  }
  .pagi{
    height: 35px;
    float: right;
    margin-bottom: 20px;
    padding-right: 10px;
  }
}
::v-deep .el-dialog {
   display: flex;
   flex-direction: column;
   margin:0 !important;
   position:absolute;
   top:50%;
   left:50%;
   transform:translate(-50%,-50%);
   /*height:600px;*/
   max-height:calc(100% - 30px);
   max-width:calc(100% - 30px);
}
::v-deep .el-dialog .el-dialog__body {
   flex:1;
   overflow: auto;
}

</style>
