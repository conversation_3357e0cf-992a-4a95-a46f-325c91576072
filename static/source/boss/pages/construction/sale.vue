<template>
  <div class="construction-list">
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="140px"
      label-position="right">
      <el-row>
        <el-col :span="7">
          <el-form-item label="客户名称：" prop="customerName">
            <RemoteCustomer
              style="width:100%"
              getLabel
              headName="客户"
              v-model="searchForm.customerName"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="物料组：" prop="materialGroupName">
            <el-input
              v-model="searchForm.materialGroupName"
              placeholder="请选择"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="采购组：" prop="purchaseGroupName">
            <el-input
              v-model="searchForm.purchaseGroupName"
              placeholder="请选择"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="3" style="padding-left:10px;">
          <el-button
            type="primary"
            icon="el-icon-search"
            :loading="loading.searchLoading"
            @click="pageOneSearch"
            >查询</el-button>
        </el-col>
        <el-col :span="7">
          <el-form-item label="物料编码：" prop="materialNo">
            <el-input
              v-model="searchForm.materialNo"
              placeholder="多个可用空格隔开"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="收货省市区：" prop="provinceArray">
            <ProvinceCascader :checkStrictly="true" @provinceChange="provinceChange" style="width:100%" v-model="searchForm.provinceArray" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="收货人：" prop="receiverName">
            <el-input
              v-model="searchForm.receiverName"
              placeholder="多个可用空格隔开"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="3" style="padding-left:10px;">
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-col>
        <el-col :span="7">
          <el-form-item label="订单号：" prop="orderNo">
            <el-input
              v-model="searchForm.orderNo"
              placeholder="可输入客户订单号或销售订单号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="集团：" prop="blocNumber">
            <el-select style="width:100%" clearable v-model.trim="searchForm.blocNumber">
              <el-option
                v-for="(item,index) in blocOptions"
                :key="index"
                :label="item.blocName"
                :value="item.blocNumber"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7" v-show="activeName != 'pending'">
          <el-form-item label="审批状态：" prop="status">
            <el-select style="width:100%" clearable v-model.trim="searchForm.status">
              <el-option
                v-for="(item,index) in statusOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="采购方式：" prop="purchaseMethod">
            <el-select style="width:100%" clearable v-model.trim="searchForm.purchaseMethod">
              <el-option
                v-for="(item,index) in purchaseMethodOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7" v-show="activeName != 'pending'">
          <el-form-item label="是否低毛利：" prop="isLowProfit">
            <el-select style="width:100%" clearable v-model.trim="searchForm.isLowProfit">
              <el-option
                v-for="(item,index) in isLowProfitOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="订单创建日期：" prop="dateRange">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.dateRange"
              type="daterange"
              align="right"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        <el-col v-show="activeName == 'pending' " :span="7">
          <el-form-item label="未维护：" prop="sellerInfo">
            <el-select style="width:100%" clearable v-model.trim="searchForm.sellerInfo">
              <el-option
                v-for="(item,index) in sellerInfoOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-show="activeName != 'pending'">
        <el-col :span="7">
          <el-form-item label="未维护：" prop="sellerInfo">
            <el-select style="width:100%" clearable v-model.trim="searchForm.sellerInfo">
              <el-option
                v-for="(item,index) in sellerInfoOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="table">
      <div class="tab-and-button">
        <el-tabs class="tab" v-model="activeName" @tab-click="pageOneSearch" type="card">
          <el-tab-pane label="待处理" name="pending"></el-tab-pane>
          <el-tab-pane label="提交记录" name="record"></el-tab-pane>
        </el-tabs>
        <div class="export-button">
          <el-button size="mini" type="primary" @click="exportSale(activeName === 'pending'?1:2)">批量导出</el-button>
        </div>
      </div>
        <Pending v-loading="loading.tableLoading" @export="exportSale" v-if="activeName === 'pending'" :propsData="pendingData" />
      <div style="height: calc(100vh - 500px); overflow: auto;" v-if="activeName === 'record'">
        <Record v-loading="loading.tableLoading" @export="exportSale"  :propsData="recordData" @revoke="revoke" />
      </div>
    </div>
    <div class="pagi">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageInfo.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import RemoteCustomer from '@/components/SearchFields/consCustomer'
import ProvinceCascader from '@/components/SearchFields/provinceCascaderAsync'
import { getSalePendingList as pendingList, getSaleRecordList as recordList, revokeApi } from '@/api/construction'
import Pending from './components/SalePending'
import Record from './components/SaleRecord'
import { mapState } from 'vuex'
import { safeRun } from '@/utils/index'
export default {
  name: 'constructionSale',
  components: {
    RemoteCustomer, Pending, Record, ProvinceCascader
  },
  data () {
    return {
      searchForm: {
        customerName: '',
        materialGroupName: '',
        purchaseGroupName: '',
        materialNo: '',
        receiverName: '',
        blocName: '',
        blocNumber: '',
        orderNo: '',
        status: '',
        purchaseMethod: '',
        isLowProfit: '',
        dateRange: [],
        startTime: '',
        endTime: '',
        sellerInfo: '',
        provinceArray: []
      },
      loading: {
        searchLoading: false,
        tableLoading: false
      },
      activeName: 'pending',
      pendingData: {},
      recordData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      purchaseMethodOptions: [
        { label: '自采', value: 0 },
        { label: '他采', value: 1 }
      ],
      isLowProfitOptions: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ],
      statusOptions: [
        { label: '待审核', value: 1 },
        { label: '驳回', value: 2 },
        { label: '已通过', value: 3 }
      ],
      sellerInfoOptions: [
        { label: '无销售', value: 0 },
        { label: '无供应商信息', value: 2 }
      ]
    }
  },
  computed: {
    ...mapState({
      blocOptions: state => state.construction.blocOptions
    })
  },
  watch: {
    $route: {
      handler: function (newVal, oldVal) {
        if (/#record/.test(newVal.hash)) {
          this.activeName = 'record'
          if (newVal.hash === '#recordAndSearch') {
            console.log(newVal)
            setTimeout(this.pageOneSearch, 200)
          }
        }
      },
      immediate: true
    }
  },
  methods: {
    exportSale (type) {
      const api = {
        1: '/achitechive/items/exportSellerUnSubmit',
        2: '/achitechive/items/exportSellerSubmit'
      }
      const data = this.getParams()
      let url = '?'
      for (let d in data) {
        url += `${d}=${data[d]}&`
      }
      url = url.slice(0, -1)
      const destUrl = `/api-build${api[type]}/${url}`
      window.open(destUrl)
    },
    provinceChange (array) {
      safeRun(() => {
        if (!array || array.length === 0) {
          this.searchForm.provinceCode = ''
          this.searchForm.cityCode = ''
          this.searchForm.areaCode = ''
        }
        this.searchForm.provinceCode = array[0].value
        this.searchForm.cityCode = (array[1] && array[1].value) || ''
        this.searchForm.areaCode = (array[2] && array[2].value) || ''
      })
    },
    revoke (item) {
      this.loading.tableLoading = true
      revokeApi(item.id)
        .then(res => {
          if (res === 'success') this.$message.success('操作成功！')
          setTimeout(this.pageOneSearch, 800)
        })
        .finally(() => {
          this.loading.tableLoading = true
        })
    },
    pageOneSearch (tab) {
      safeRun(() => {
        location.hash = tab.name ? '#' + tab.name : ''
      })
      this.handleSearch()
      this.pageInfo.pageNo = 1
    },
    handleSizeChange (size) {
      this.pageInfo.pageSize = size
      this.handleSearch()
    },
    handleCurrentChange (pageNo) {
      this.pageInfo.pageNo = pageNo
      this.handleSearch()
    },
    handleReset () {
      this.$refs['searchForm'].resetFields()
      this.searchForm.provinceCode = ''
      this.searchForm.cityCode = ''
      this.searchForm.areaCode = ''
    },
    formatParams (params) {
      const p = { ...params }
      for (let param in p) {
        if (p[param] === '' || p[param] === undefined) {
          delete p[param]
        }
      }
      safeRun(() => {
        if (p.dateRange.length) {
          p.startTime = p.dateRange[0]
          p.endTime = p.dateRange[1]
          delete p.dateRange
        }
        delete p.provinceArray
      })
      return p
    },
    getList (type, data) {
      let searchApi = recordList
      if (type === 'pending') searchApi = pendingList
      if (type === 'record') searchApi = recordList
      return searchApi(data)
        .then(res => {
          if (res) {
            this.fillChildren(type, res)
            this.pageInfo.total = res.total
          }
        })
    },
    fillChildren (type, res) {
      if (type === 'pending') {
        this.pendingData = {
          updateDateTime: res.updateDateTime,
          total: res.total,
          list: res.list
        }
      } else {
        this.recordData = res.list || []
      }
    },
    getParams () {
      const tmp = {
        ...this.searchForm,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      }
      return this.formatParams(tmp)
    },
    handleSearch () {
      this.loading.searchLoading = true
      this.loading.tableLoading = true
      const data = this.getParams()
      this.getList(this.activeName, data)
        .finally(() => {
          this.loading.searchLoading = false
          this.loading.tableLoading = false
        })
    }
  },
  created () {
    this.handleSearch()
    if (this.blocOptions && this.blocOptions.length === 0) {
      this.$store.dispatch('construction/getBlocOptions')
    }
  }
}
</script>
<style lang="scss" scoped>
.construction-list{
  margin: 10px;
  .table{
    .tab-and-button{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tab{
        flex-grow: 1;
      }
    }
    .button-group {
      float: right;
      margin-bottom: 10px;
      margin-right: 5px;
    }
  }
  .pagi{
    float: right;
    margin-bottom: 20px;
    padding-right: 10px;
  }
}
</style>
