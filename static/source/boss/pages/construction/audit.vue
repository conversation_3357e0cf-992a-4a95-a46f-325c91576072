<template>
  <div class="construction-list">
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="140px"
      label-position="right"
      v-if="activeName==='pending'||activeName==='record'"
      >
      <el-row>
        <el-col :span="7">
          <el-form-item label="客户名称：" prop="customerName">
            <RemoteCustomer
              style="width:100%"
              headName="客户"
              getLabel
              v-model="searchForm.customerName"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7" >
          <el-form-item label="申请人：" prop="submitUser">
            <el-input
              v-model="searchForm.submitUser"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="项目名称：" prop="belongProject">
            <el-input
              v-model="searchForm.belongProject"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="3" style="padding-left:10px;">
          <el-button
            type="primary"
            icon="el-icon-search"
            :loading="loading.searchLoading"
            @click="handleSearch"
            >查询</el-button>
        </el-col>
        <el-col :span="7">
          <el-form-item label="收货省市区：" prop="provinceArray">
            <ProvinceCascader :checkStrictly="true" @provinceChange="provinceChange" style="width:100%" v-model="searchForm.provinceArray" />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="收货人：" prop="receiverName">
            <el-input
              v-model="searchForm.receiverName"
              placeholder="多个可用空格隔开"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="物料编码：" prop="materialNo">
            <el-input
              v-model="searchForm.materialNo"
              placeholder="多个可用空格隔开"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="3" style="padding-left:10px;">
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
        </el-col>
        <el-col :span="7">
          <el-form-item label="订单号：" prop="orderNo">
            <el-input
              v-model="searchForm.orderNo"
              placeholder="可输入客户订单号或销售订单号"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="集团：" prop="blocNumber">
            <el-select style="width:100%" clearable v-model.trim="searchForm.blocNumber">
              <el-option
                v-for="(item,index) in blocOptions"
                :key="index"
                :label="item.blocName"
                :value="item.blocNumber"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="审批状态：" prop="status">
            <el-select style="width:100%" clearable v-model.trim="searchForm.status">
              <el-option
                v-for="(item,index) in statusOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="采购方式：" prop="purchaseMethod">
            <el-select style="width:100%" clearable v-model.trim="searchForm.purchaseMethod">
              <el-option
                v-for="(item,index) in purchaseMethodOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="是否低毛利：" prop="isLowProfit">
            <el-select style="width:100%" clearable v-model.trim="searchForm.isLowProfit">
              <el-option
                v-for="(item,index) in isLowProfitOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="订单创建日期：" prop="dateRange">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.dateRange"
              type="datetimerange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7">
          <el-form-item label="订单审核时间：" prop="reviewDateRange">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.reviewDateRange"
              type="datetimerange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form  ref="searchForm"
      :model="searchForm" class="padding-box" label-width="140px" label-position="right" style="margin-bottom:10px" v-else>
      <el-row :gutter="20">
         <el-col :span="8">
          <el-form-item label="客户名称：" prop="customerName">
            <RemoteCustomer
              style="width:100%"
              headName="客户"
              getLabel
              v-model="searchForm.customerName"
            />
          </el-form-item>
        </el-col>
         <el-col :span="8">
          <el-form-item label="项目名称：" prop="belongProject">
            <el-input
              v-model="searchForm.belongProject"
              placeholder="请输入"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货人：" prop="receiverName">
            <el-input
              v-model="searchForm.receiverName"
              placeholder="多个可用空格隔开"
              clearable
            />
          </el-form-item>
        </el-col>
         <el-col :span="8">
          <el-form-item label="物料编码：" prop="materialNo">
            <el-input
              v-model="searchForm.materialNo"
              placeholder="多个可用空格隔开"
              clearable
            />
          </el-form-item>
        </el-col>
         <el-col :span="8">
          <el-form-item label="订单号：" prop="orderNo">
            <el-input
              v-model="searchForm.orderNo"
              placeholder="可输入销售订单号"
              clearable
            />
          </el-form-item>
        </el-col>
         <el-col :span="8">
          <el-form-item label="采购方式：" prop="purchaseMethod">
            <el-select style="width:100%" clearable v-model.trim="searchForm.purchaseMethod">
              <el-option
                v-for="(item,index) in purchaseMethodOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="订单创建日期：" prop="dateRange">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.dateRange"
              type="datetimerange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
          <div class="btn-container" style="min-width:240px;float:left">
             <el-button
            type="primary"
            icon="el-icon-search"
            :loading="loading.searchLoading"
            @click="handleSearch"
            >查询</el-button>
             <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
             <el-button icon="el-icon-refresh" @click="refresh" :disabled="refreshBtnDisabled">刷新</el-button>
          </div>
      </el-row>

    </el-form>

    <div class="table">
      <div class="tab-and-button">
        <el-tabs class="tab" v-model="activeName" type="card" @tab-click="tabClick">
          <el-tab-pane label="待处理" name="pending"></el-tab-pane>
          <el-tab-pane label="处理记录" name="record"></el-tab-pane>
          <el-tab-pane label="未生成采购订单" name="ungeneratedOrder"></el-tab-pane>
        </el-tabs>
        <div class="export-button" v-if="activeName === 'record'">
          <el-button size="mini" type="primary" @click="exportAuditExcel">批量导出</el-button>
        </div>
      </div>
      <div style="max-height: calc(100vh - 500px); overflow: auto;" v-if="activeName === 'pending' || activeName === 'record'">
        <Pending v-loading="loading.tableLoading" v-if="activeName === 'pending'" @setPendingItem="setPendingItem" :propsData="pendingData" @audit="batchAudit"/>
        <Record v-loading="loading.tableLoading" v-if="activeName === 'record'" :propsData="recordData"/>
      </div>
      <UngeneratedOrder v-loading="loading.tableLoading"  v-if="activeName === 'ungeneratedOrder'" :propsData="ungeneratedOrderData"  @uploadingList="uploadingList"></UngeneratedOrder>
    </div>
    <div class="pagi">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageInfo.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageInfo.total">
      </el-pagination>
    </div>
  </div>
</template>
<script>
import RemoteCustomer from '@/components/SearchFields/consCustomer'
import ProvinceCascader from '@/components/SearchFields/provinceCascaderAsync'
import { getAuditPending as pendingList, getAuditRecord as recordList, getUngeneratedOrder as ungeneratedOrderList, batchAudit, exportData, getSyncUngeneratedOrder } from '@/api/construction'
import Pending from './components/AuditPending'
import Record from './components/AuditRecord'
import UngeneratedOrder from './components/ungeneratedOrder'
import { safeRun } from '@/utils/index'
import { toExcel } from '@/utils/jsonToExcel'
import { mapState } from 'vuex'
import debounce from 'lodash/debounce'

export default {
  name: 'constructionAudit',
  components: {
    RemoteCustomer, Pending, Record, ProvinceCascader, UngeneratedOrder
  },
  computed: {
    ...mapState({
      blocOptions: state => state.construction.blocOptions
    }),
    refreshBtnDisabled() {
      return Object.keys(this.trimEmptyParams(this.searchForm)) <= 0
    }
  },
  data () {
    return {
      searchForm: {
        customerName: '',
        materialGroupName: '',
        purchaseGroupName: '',
        materialNo: '',
        belongProject: '',
        submitUser: '',
        receiverName: '',
        blocNumber: '',
        status: '',
        purchaseMethod: '',
        isLowProfit: '',
        dateRange: [],
        reviewDateRange: [],
        provinceArray: [],
        startTime: '',
        endTime: ''
      },
      loading: {
        searchLoading: false,
        tableLoading: false
      },
      activeName: 'pending',
      pendingData: [],
      recordData: [],
      ungeneratedOrderData: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      purchaseMethodOptions: [
        { label: '自采', value: 0 },
        { label: '他采', value: 1 }
      ],
      isLowProfitOptions: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ],
      statusOptions: [
        { label: '未提交', value: 0 },
        { label: '待审核', value: 1 },
        { label: '驳回', value: 2 },
        { label: '已通过', value: 3 }
      ]
    }
  },
  methods: {
    setPendingItem (index, item) {
      this.$set(this.pendingData, index, item)
    },
    uploadingList() {
      this.handleSearch()
    },
    mapEnums (prop, value) {
      return safeRun(() => this[prop].filter(item => item.value === value)[0].label) || value
    },
    provinceChange (array) {
      console.log(array)
      safeRun(() => {
        if (!array || array.length === 0) {
          this.searchForm.provinceCode = ''
          this.searchForm.cityCode = ''
          this.searchForm.areaCode = ''
        }
        this.searchForm.provinceCode = array[0].value
        this.searchForm.cityCode = (array[1] && array[1].value) || ''
        this.searchForm.areaCode = (array[2] && array[2].value) || ''
      })
    },
    formateExportData (data) {
      const base = {
        createDate: '创建日期',
        createTime: '创建时间',
        purchaseGroupName: '采购组描述',
        materialGroupName: '物料组描述',
        belongProject: '所属项目',
        customerOrderNo: '客户订单号',
        blocName: '集团',
        customerName: '客户名称',
        receiverName: '收货人',
        receiverPhone: '收货人电话',
        orderNo: '销售订单',
        provinceName: '省市区',
        addressDetail: '详细地址',
        itemNo: '订单行号',
        materialNo: '物料编码',
        materialName: '物料描述',
        saleUnit: '销售单位',
        materialCount: '订单数量',
        taxInPrice: '含税单价',
        sellerName: '销售',
        remark: '订单备注',
        profitShare: '自营分润',
        providerName: '供应商',
        purchaseMethod: '采购方式',
        providerId: '供应商编号',
        taxInRate: '进项税点',
        purchasePrice: '正常采购价',
        deductionRage: '正常扣点',
        vipPurchasePrice: '申请特殊采购价',
        vipDeductionRage: '申请特殊扣点',
        vipMsg: '申请原因',
        status: '审核状态'
      }
      data = data.map(item => {
        return {
          createDate: item.createDate || '',
          createTime: item.createTime || '',
          purchaseGroupName: item.purchaseGroupName || '',
          materialGroupName: item.materialGroupName || '',
          belongProject: item.belongProject || '',
          customerOrderNo: item.customerOrderNo || '',
          blocName: item.blocName || '',
          customerName: item.customerName || '',
          receiverName: item.receiverName || '',
          receiverPhone: item.receiverPhone || '',
          orderNo: item.orderNo || '',
          provinceName: item.provinceName || '',
          addressDetail: item.addressDetail || '',
          itemNo: item.itemNo || '',
          materialNo: item.materialNo || '',
          materialName: item.materialName || '',
          saleUnit: item.saleUnit || '',
          materialCount: item.materialCount || '',
          taxInPrice: item.taxInPrice || '',
          sellerName: item.sellerName || '',
          remark: item.remark || '',
          profitShare: item.profitShare || '',
          providerName: item.providerName || '',
          purchaseMethod: this.mapEnums('purchaseMethodOptions', item.purchaseMethod) || '',
          providerId: item.providerId || '',
          taxInRate: item.taxInRate || '',
          purchasePrice: item.purchasePrice || '',
          deductionRage: item.deductionRage || '',
          vipPurchasePrice: item.vipPurchasePrice || '',
          vipDeductionRage: item.vipDeductionRage || '',
          vipMsg: item.vipMsg || '',
          status: this.mapEnums('statusOptions', item.status) || ''
        }
      })
      data.unshift(base)
      console.log(data)
      return data
    },
    exportValidate (data) {
      if (!data.reviewStartTime || !data.reviewEndTime) {
        return this.$message.error('导出请选择订单审核日期，时间跨度最大为14天！')
      }
      return safeRun(() => {
        if (Math.abs(new Date(data.reviewEndTime) - new Date(data.reviewStartTime)) / (1e3 * 60 * 60 * 24) > 14) {
          return this.$message.error('时间跨度最大为14天！')
        }
        return true
      })
    },
    exportAuditExcel () {
      let data = { ...this.searchForm }
      data = this.trimEmptyParams(data)
      if (this.exportValidate(data) !== true) return
      console.log(data)
      this.loading.tableLoading = true
      exportData(data)
        .then(res => {
          console.log(res)
          if (res && res.length) {
            // const date = new Date().toISOString().split(/\.|T/)
            const name = '导出记录' + new Date().toLocaleString()
            toExcel(this.formateExportData(res), name)
          } else {
            this.$message.info('没有数据！')
          }
        })
        .finally(() => {
          this.loading.tableLoading = false
        })
    },
    batchAudit (code, rows) {
      const data = new FormData()
      data.append('operateCode', code)
      data.append('itemIds', rows.map(row => row.id).join(','))
      this.loading.tableLoading = true
      batchAudit(data)
        .then(res => {
          if (res) {
            this.$message.success('操作成功！')
            setTimeout(this.handleSearch, 800)
          }
        })
        .finally(() => {
          this.loading.tableLoading = false
        })
    },
    tabClick () {
      this.handleSearch()
      this.pageInfo.pageNo = 1
    },
    handleSizeChange (size) {
      this.pageInfo.pageSize = size
      // this.handleSearch()
      this.paginationGetList()
    },
    handleCurrentChange (pageNo) {
      this.pageInfo.pageNo = pageNo
      // this.handleSearch()
      this.paginationGetList()
    },
    handleReset () {
      // this.$refs['searchForm'].resetFields()
      for (var k in this.searchForm) {
        if (k === 'dateRange' || k === 'reviewDateRange' || k === 'provinceArray') {
          this.searchForm[k] = []
        } else {
          this.searchForm[k] = ''
        }
      }
      this.searchForm.provinceCode = ''
      this.searchForm.cityCode = ''
      this.searchForm.areaCode = ''
      this.pageInfo.pageNo = 1
      this.pageInfo.pageSize = 10
    },
    getList (type, params) {
      let searchApi = recordList
      if (type === 'pending') searchApi = pendingList
      if (type === 'record') searchApi = recordList
      if (type === 'ungeneratedOrder') searchApi = ungeneratedOrderList
      return searchApi(params)
        .then(res => {
          if (res) {
            this.fillChildren(type, res)
            this.pageInfo.total = res.total
          }
        })
    },
    fillChildren (type, res) {
      if (type === 'pending') {
        this.pendingData = res.list || []
      } else if (type === 'record') {
        this.recordData = res.list || []
      } else if (type === 'ungeneratedOrder') {
        this.ungeneratedOrderData = res.list || []
      }
    },
    trimEmptyParams (params) {
      const p = { ...params }
      for (let param in p) {
        if (p[param] === undefined || p[param] === '') {
          delete p[param]
        }
      }
      safeRun(() => {
        if (p.dateRange.length) {
          p.startTime = p.dateRange[0]
          p.endTime = p.dateRange[1]
        }
      })
      safeRun(() => {
        if (p.reviewDateRange.length) {
          p.reviewStartTime = p.reviewDateRange[0]
          p.reviewEndTime = p.reviewDateRange[1]
        }
      })
      delete p.dateRange
      delete p.reviewDateRange
      delete p.provinceArray
      return p
    },
    handleSearch () {
      this.loading.searchLoading = true
      this.loading.tableLoading = true
      const data = {
        ...this.searchForm,
        pageNo: 1,
        pageSize: this.pageInfo.pageSize
      }
      this.getList(this.activeName, this.trimEmptyParams(data))
        .finally(() => {
          this.loading.searchLoading = false
          this.loading.tableLoading = false
        })
    },
    paginationGetList() {
      this.loading.searchLoading = true
      this.loading.tableLoading = true
      const data = {
        ...this.searchForm,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize
      }
      this.getList(this.activeName, this.trimEmptyParams(data))
        .finally(() => {
          this.loading.searchLoading = false
          this.loading.tableLoading = false
        })
    },
    refresh: debounce(function() {
      // this.loading.searchLoading = true
      // this.loading.tableLoading = true
      const data = {
        ...this.searchForm,
        pageNo: this.pageInfo.pageNo,
        pageSize: this.pageInfo.pageSize,
        operType: 'INIT_PART_DATA'
      }
      getSyncUngeneratedOrder(this.trimEmptyParams(data)).then((res) => {
        if (res) {
          this.$message.warning('数据正在同步 请您稍等')
        }
      })
    }, 1000)
  },
  created () {
    this.handleSearch()
    if (this.blocOptions && this.blocOptions.length === 0) {
      this.$store.dispatch('construction/getBlocOptions')
    }
  }
}
</script>
<style lang="scss" scoped>
.construction-list{
  margin: 10px;
  .table{
    .tab-and-button{
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tab{
        flex-grow: 1;
      }
    }
    .button-group {
      float: right;
      margin-bottom: 10px;
      margin-right: 6px;
    }
  }
  .pagi{
    float: right;
    margin-bottom: 20px;
    padding-right: 10px;
  }
  .padding-box {
    padding: 10px 20px 0 5px;
    .btn-container {
      margin-left: 140px;
      min-width: 168px;

    }
  }
}
</style>
