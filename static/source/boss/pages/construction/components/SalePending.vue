<template>
  <div class="pending">
    <div class="text-and-button">
      <div class="text">
        <span>（待处理行数：<strong>{{ propsData.total }}</strong>）</span>
        <span>更新时间：<strong>{{ propsData.updateDateTime }}</strong></span>
      </div>
      <div class="button">
        <el-button :disabled="!selections.length" size="mini" @click="batchSubmit">批量提交</el-button>
      </div>
    </div>
    <el-table border @selection-change="handleSelectionChange" :data="compData" :height="tableHeight">
      <el-table-column type="selection" fixed="left"/>
      <el-table-column align="center" show-overflow-tooltip width="120px" label="创建日期" prop="createDate" />
      <el-table-column align="center" show-overflow-tooltip label="创建时间" prop="createTime" />
      <el-table-column align="center" show-overflow-tooltip label="驳回备注" prop="rejectRemarks" />
      <el-table-column align="center" show-overflow-tooltip label="采购组描述" prop="purchaseGroupName" />
      <el-table-column align="center" show-overflow-tooltip label="物料组描述" prop="materialGroupName" />
      <el-table-column align="center" show-overflow-tooltip label="所属项目" prop="belongProject" />
      <el-table-column align="center" show-overflow-tooltip width="120px" label="客户订单号" prop="customerOrderNo" />
      <el-table-column align="center" show-overflow-tooltip width="120px" label="集团" prop="blocName" />
      <el-table-column align="center" show-overflow-tooltip width="200px" label="客户名称" prop="customerName" />
      <el-table-column align="center" show-overflow-tooltip label="收货人" prop="receiverName" />
      <el-table-column align="center" show-overflow-tooltip width="100px" label="收货人电话" prop="receiverPhone" />
      <el-table-column align="center" show-overflow-tooltip width="100px" label="销售订单" prop="orderNo" />
      <el-table-column align="center" show-overflow-tooltip label="省市区" prop="provinceName" width="200px">
        <template slot-scope="{ row }">
          {{ trimString(row.provinceName, row.cityName, row.areaName) }}
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip label="详细地址" prop="addressDetail" width="200px"/>
      <el-table-column align="center" show-overflow-tooltip label="订单行号" prop="itemNo" />
      <el-table-column align="center" show-overflow-tooltip label="物料编码" prop="materialNo" />
      <el-table-column align="center" show-overflow-tooltip width="220px" label="物料描述" prop="materialName" />
      <el-table-column align="center" show-overflow-tooltip label="销售单位" prop="saleUnit" />
      <el-table-column align="center" show-overflow-tooltip label="订单数量" prop="materialCount" />
      <el-table-column align="center" show-overflow-tooltip label="含税单价" prop="taxInPrice" />
      <el-table-column align="center" show-overflow-tooltip label="建筑销售" prop="sellerName" />
      <el-table-column align="center" show-overflow-tooltip label="订单备注" prop="remark" />
      <el-table-column align="center" show-overflow-tooltip width="100px" label="自营分润" prop="profitShare" />
      <el-table-column align="center" show-overflow-tooltip label="供应商" width="140px" prop="providerName" />
      <el-table-column align="center" show-overflow-tooltip label="采购方式" prop="purchaseMethod">
        <template slot-scope="{ row }">
          {{ enums.purchaseMethod[row.purchaseMethod] }}
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip width="140px" label="供应商编号" prop="providerId" />
      <el-table-column align="center" show-overflow-tooltip width="140px" label="进项税点" prop="taxInRate" />
      <el-table-column align="center" show-overflow-tooltip width="140px" label="正常采购价" prop="purchasePrice" />
      <el-table-column align="center" show-overflow-tooltip width="140px" label="正常扣点" prop="deductionRage" />
      <el-table-column align="center" show-overflow-tooltip label="操作" fixed="right" prop="city">
        <template slot-scope="{ row }">
          <el-button type="text" @click="submit(row)">提交审批</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { trimString, safeRun } from '@/utils/index';
export default {
  name: 'pending',
  props: {
    propsData: Object
  },
  data () {
    return {
      selections: [],
      tableHeight: 500,
      enums: {
        status: {
          0: '未启用',
          1: '已启用',
          2: '草稿',
          3: '停用'
        },
        isReported: {
          0: '否',
          1: '是'
        },
        purchaseMethod: {
          0: '自采',
          1: '他采'
        }
      }
    }
  },
  computed: {
    compData() {
      const list = this.propsData.list
      safeRun(() => {
        list.forEach(item => {
          if (item.achitechiveProviderInfoList.length === 0) {
            if (item.purchaseMethod === 0) {
              item.profitShare = '未维护数据'
            } else if (item.purchaseMethod === 1) {
              item.providerName = '未维护数据'
              item.providerId = '未维护数据'
              item.taxInRate = '未维护数据'
              item.purchasePrice = '未维护数据'
              item.deductionRage = '未维护数据'
            }
          }
        })
      })
      console.log(list)
      return list
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 500
      console.log('nextTick', window.innerHeight, this.tableHeight)
    })
    window.onresize = () => {
      this.tableHeight = window.innerHeight - 500
      console.log('nextTick', window.innerHeight, this.tableHeight)
    }
  },
  methods: {
    exportToExcel () {
      this.$emit('export', 1)
    },
    handleSelectionChange (val) {
      this.selections = val
    },
    submit(row) {
      console.log(row)
      this.$router.push(`/construction/edit/${row.orderNo + '-' + row.itemNo}`)
    },
    batchSubmit() {
      this.$router.push(`/construction/edit/${this.selections.map(item => item.orderNo + '-' + item.itemNo).join(',')}`)
    },
    trimString
  }
};
</script>
<style lang="scss" scoped>
.pending {
  height: 100%;
  .export-button{
    overflow: hidden;
    button{
      float: right;
      margin-right: 5px;
    }
  }
  .text-and-button {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
    margin-bottom: 10px;
    .button {
      margin-right: 5px;
    }
  }
}
</style>
