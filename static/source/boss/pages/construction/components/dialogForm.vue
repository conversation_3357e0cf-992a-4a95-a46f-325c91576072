<template>
  <div class="form">
    <el-form
      ref="createForm"
      :model="cloneData"
      label-width="160px"
      label-position="right"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item label="项目名称：" prop="projectName">
            <el-input v-model="cloneData.projectName" placeholder="请输入" :disabled="cloneData.status===3 || cloneData.status===1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="集团：" prop="blocName">
            <el-select
              clearable
              @change="handleBlocChange"
              v-model="cloneData.blocName"
              style="width: 100%"
              :disabled="cloneData.status===3 || cloneData.status===1"
            >
              <el-option
                v-for="(item, index) in saleOrgList"
                :key="item.salesOrganization + index"
                :value="item.salesOrganizationName"
                :label="item.salesOrganizationName"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户名称：" prop="customerName">
            <Customer
              :disabled="cloneData.status===3 || cloneData.status===1"
              headName="客户"
              placeholder="输入名称/编码"
              style="width: 100%"
              @change="handleCustomerChange"
              v-model="cloneData.customerName"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户编码：" prop="customerNumber">
            <el-input
              disabled
              v-model="cloneData.customerNumber"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目属性：" prop="projectAttribute">
            <el-input
            :disabled="cloneData.status===3 || cloneData.status===1"
              v-model="cloneData.projectAttribute"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属二、三级公司：" prop="belongCompany2o3">
            <el-input
            :disabled="cloneData.status===3 || cloneData.status===1"
              v-model="cloneData.belongCompany2o3"
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目省市区：" prop="provinceArray">
            <el-input
              :value="`${cloneData.provinceName || '--'}/${
                cloneData.cityName || '--'
              }/${cloneData.areaName || '--'}`"
              v-if="!isAddressModified"
              disabled
              style="width: 216px"
            />
            <ProvinceCascader
              v-else
              @provinceChange="provinceChange"
              v-model="cloneData.provinceArray"
              style="width: 216px"
               :disabled="cloneData.status===3 || cloneData.status===1"
              @change="isAddressModified = false"
            />
            <span style="margin-left: 6px">
              <i
                class="el-icon-edit"
                @click="isAddressModified = true"
                v-if="!isAddressModified"
              ></i>
              <i
                class="el-icon-close"
                @click="isAddressModified = false"
                v-else
              ></i>
            </span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="详细地址：" prop="addressDetail">
            <el-input v-model="cloneData.addressDetail" placeholder="请输入" :disabled="cloneData.status===3 || cloneData.status===1" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单收货人：" prop="contactName">
            <Contact
              ref="contact"
              style="width: 100%"
              placeholder="输入名称"
              :customerNumber="contact.customerNumber"
              :distributionChannel="contact.distributionChannel"
              :productGroup="contact.productGroup"
              :salesOrganization="contact.salesOrganization"
              @change="handleContactChange"
              v-model="cloneData.contactName"
              :disabledU="cloneData.status===3 || cloneData.status===1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单收货人电话：" prop="contactPhone">
            <el-input
              disabled
              v-model="cloneData.contactPhone"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单收货人地址：" prop="contactAddress">
            <el-input
              disabled
              v-model="cloneData.contactAddress"
              placeholder=""
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商：" prop="providerName">
            <Supplier
              placeholder="输入名称"
               :disabled="cloneData.status===3 || cloneData.status===1"
              getLabel
              style="width: 100%"
              @change="handleProviderChange"
              v-model="cloneData.providerName"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否供应商报备项目：" prop="isReported">
            <el-select v-model="cloneData.isReported" style="width: 100%" :disabled="cloneData.status===3 || cloneData.status===1">
              <el-option :value="1" label="是"></el-option>
              <el-option :value="0" label="否"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="供应商编码：" prop="providerId">
            <el-input disabled v-model="cloneData.providerId" placeholder="" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="销售：" prop="sellerOaName">
            <el-input
            :disabled="cloneData.status===3 || cloneData.status===1"
              v-model="cloneData.sellerOaName"
              placeholder="请输入域账号例 ying.chen"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="销售经理：" prop="sellerManagerOaName">
            <el-input
            :disabled="cloneData.status===3 || cloneData.status===1"
              v-model="cloneData.sellerManagerOaName"
              placeholder="请输入域账号例 ying.chen"
            />
          </el-form-item>
        </el-col>
        <!-- roleInfo !== 2 -->
        <el-col :span="12">
          <el-form-item label="进项税点%：" prop="taxInRate">
            <el-input-number
              style="width: 100%"
              :min="0"
              :max="100"
              :disabled="cloneData.status===3 || cloneData.status===1"
              v-model="cloneData.taxInRate"
              placeholder="请输入"
            >
            </el-input-number>
          </el-form-item>
        </el-col>
        <!-- roleInfo !== 2 -->
        <el-col :span="12">
          <el-form-item label="扣点%：" prop="deductionRage">
            <el-input-number
              style="width: 100%"
              :min="0"
              :max="100"
              :disabled="cloneData.status===3 || cloneData.status===1"
              v-model="cloneData.deductionRage"
              placeholder="请输入"
            >
            </el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="下单人：" prop="orderCreator">
            <el-input
              v-model="cloneData.orderCreator"
              :disabled="cloneData.status===3 || cloneData.status===1"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item label="项目备注：" prop="projectNotes">
            <el-input
             :disabled="cloneData.status===3 || cloneData.status===1"
              v-model="cloneData.projectNotes"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="button">
      <el-button  type="primary"  @click="submit('保存并提交审批')" v-if="cloneData.status===2||cloneData.status===0">保存并提交审批</el-button>
      <el-button type="primary" v-if="cloneData.status===2||cloneData.status===4||cloneData.status===0||detailData===null" @click="submit('确认提交')" :loading="loading.submitLoading"
        >确认保存</el-button
      >
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import Customer from '@/components/SearchFields/consCustomer'
import Supplier from '@/components/SearchFields/consSupplier'
import Contact from '@/components/SearchFields/consContact'
import ProvinceCascader from '@/components/SearchFields/provinceCascaderAsync'
import {
  createConfig,
  modifyConfig,
  handlePermission,
  getRole,
  // getProviderDetail,
  getBlocInfo,
  getSupplierByProviderName
} from '@/api/construction'
import { safeRun } from '../../../utils'

export default {
  name: 'dialogForm',
  props: {
    detailData: Object
  },
  components: {
    Customer,
    Contact,
    ProvinceCascader,
    Supplier
  },
  data() {
    return {
      cloneData: {
        blocName: '',
        blocNumber: '',
        contactName: '',
        providerName: '',
        providerId: '',
        provinceArray: [],
        orderCreator: '',
        projectNotes: ''
      },
      loading: {
        submitLoading: false
      },
      contact: {
        customerNumber: '',
        distributionChannel: '',
        productGroup: '',
        salesOrganization: ''
      },
      saleOrgList: [],
      roleInfo: 0,
      isAddressModified: false,
      rateMap: {
        0: 0,
        10: 9,
        11: 1,
        2: 3,
        3: 6,
        4: 5,
        5: 7,
        7: 13
      }
    }
  },
  methods: {
    handleBlocChange(val) {
      let item
      safeRun(() => {
        item = this.saleOrgList.filter(
          (item) => item.salesOrganizationName === val
        )[0]
        this.cloneData.blocNumber = item.salesOrganization
      })
      item = item || {}
      this.contact.distributionChannel = item.distributionChannel
      this.contact.productGroup = item.productGroup
      this.contact.salesOrganization = item.salesOrganization
      setTimeout(() => {
        try {
          this.$refs.contact.search('')
        } catch (err) {}
      }, 200)
    },
    provinceChange(array) {
      if (array.length === 0) {
        this.cloneData.provinceName = null
        this.cloneData.provinceCode = null
        this.cloneData.cityName = null
        this.cloneData.cityCode = null
        this.cloneData.areaName = null
        this.cloneData.areaCode = null
      }
      try {
        this.cloneData.provinceName = array[0].label
        this.cloneData.provinceCode = array[0].value
        this.cloneData.cityName = array[1].label
        this.cloneData.cityCode = array[1].value
        this.cloneData.areaName = array[2].label
        this.cloneData.areaCode = array[2].value
      } catch (err) {}
    },
    handleProviderChange(val, obj) {
      if (!obj) {
        obj = {
          providerNo: ''
        }
      }
      this.cloneData.providerId = obj.providerNo
      if (!obj.providerName) {
        this.cloneData.taxInRate = ''
        return
      }
      getSupplierByProviderName(obj.providerName).then(res => {
        if (res) {
          this.cloneData.taxInRate = this.rateMap[res[0]?.ecvatGroup]
          console.log(res)
        } else {
          this.cloneData.taxInRate = ''
        }
      }).catch(err => {
        console.log(err)
        this.cloneData.taxInRate = ''
      })
    },
    handleCustomerChange(val, obj) {
      this.cloneData.blocName = ''
      this.cloneData.blocNumber = ''
      this.handleBlocChange(null)
      if (!obj) {
        obj = {
          customerName: '',
          contactPhone: '',
          contactAddress: ''
        }
      }
      this.cloneData.customerName = obj.customerName
      this.cloneData.customerNumber = obj.customerNumber
      this.contact.customerNumber = obj.customerNumber
      getBlocInfo({ customerNo: obj.customerNumber })
        .then((blocOptions) => {
          console.log(blocOptions)
          if (blocOptions) {
            this.saleOrgList = [
              {
                salesOrganizationName: blocOptions.customerName,
                salesOrganization: blocOptions.customerNumber
              }
            ]
            if (obj.saleOrgList[0]) {
              this.saleOrgList[0] = {
                ...obj.saleOrgList[0],
                ...this.saleOrgList[0]
              }
            }
          } else {
            this.saleOrgList[0] = {
              salesOrganizationName: '',
              salesOrganization: '',
              distributionChannel: '',
              productGroup: ''
            }
          }
        })
        .finally(() => {
          console.log(this.saleOrgList)
          safeRun(() => {
            this.cloneData.blocName = this.saleOrgList[0].salesOrganizationName
            this.cloneData.blocNumber = this.saleOrgList[0].salesOrganization
          })
        })
    },
    handleContactChange(val, obj) {
      if (!obj) {
        obj = {
          contactName: '',
          contactId: '',
          contactPhone: '',
          address: ''
        }
      }
      this.cloneData.contactName = obj.contactName
      this.cloneData.contactId = obj.contactId
      this.cloneData.contactPhone = obj.contactPhone
      this.cloneData.contactAddress = obj.address
    },
    formatParams(data, operateName) {
      let ret = { ...data }
      if (operateName === '保存并提交审批') {
        // data.status = 4
        ret.status = 4
      }
      delete ret.provinceArray
      delete ret.sellerName
      delete ret.sellerManagerName
      return ret
    },
    submit(operateName) {
      if (this.roleInfo === 0) return this.$message.error('没有权限！')
      this.loading.submitLoading = true
      const params = this.formatParams(this.cloneData, operateName)
      let api = createConfig
      let id = params.id
      if (this.detailData) {
        api = modifyConfig
      }
      if (operateName === '保存并提交审批') {
        api = handlePermission
      }
      api(params, id)
        .then((res) => {
          if (res.code === 200 && res.success === true) {
            this.$message.success('操作成功！')
            setTimeout(() => {
              this.closeDialog('refresh')
            }, 200)
          } else {
            this.$message.error(res.msg || res.message || '操作失败！')
          }
        })
        .catch((err) => {
          this.$message.error(err.msg || err.message || '操作失败！')
        })
        .finally(() => {
          this.loading.submitLoading = false
        })
    },
    cancel() {
      this.closeDialog()
    },
    closeDialog(refresh) {
      if (!this.loading.submitLoading) {
        this.$emit('close', refresh)
        this.cloneData = {}
      }
    },
    save() {},
    deepClone(obj) {
      return JSON.parse(JSON.stringify(obj))
    },
    beforeClose(done) {
      this.closeDialog()
      done && done()
    },
    formatData() {
      if (this.detailData) {
        this.cloneData = this.deepClone(this.detailData)
        this.contact.customerNumber = this.cloneData.customerNumber
        this.cloneData.provinceArray = [
          this.cloneData.provinceCode,
          this.cloneData.cityCode,
          this.cloneData.areaCode
        ]
      }
    }
  },
  mounted() {
    this.formatData()
    getRole().then((data) => {
      if (data !== null) {
        // 获取当前用户角色信息（0：无权限用户；1：销售；2：内审）
        this.roleInfo = data
      }
    })
  }
}
</script>

<style lang="scss" scoped>
.button {
  display: flex;
  justify-content: center;
}
</style>
