<template>
    <div>
     <vxe-table
           border
           highlight-hover-row
          ref="detailTable"
          show-overflow
          align="center"
          size="small"
          fit
          :height="tableHeight"
          :data="ungeneratedOrderDataC">
          <template  v-for="item in colums">
            <vxe-table-column v-if="item.field==='purchaseMethod'" :key="item.field" :title="item.title" :width="item.width" align="center">
               <template  v-slot="{row}">
                    <span v-if="row.purchaseMethod==1">他采</span>
                    <span v-if="row.purchaseMethod==0">自采</span>
                </template>
            </vxe-table-column>
              <vxe-table-column v-else :key="item.field" :field="item.field" :title="item.title" :width="item.width" align="center"> </vxe-table-column>
          </template>

            <vxe-table-column  title="操作" width="150" align="center" >
              <template #default="{ row }" >
                <template v-for="item in actionsType" >
                  <el-button type="text" @click="rePushOReject(item,row)" :key="item.operateCode">{{item.actionName}}</el-button>
                </template>
              </template>
           </vxe-table-column>
     </vxe-table>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="30%"
      :before-close="handleClose">
    <el-form label-position="top" label-width="80px" :model="form" ref="form" :rules="rules"  >
      <el-form-item prop="rejectRemarks" label="驳回备注">
        <el-input type="textarea" v-model="form.rejectRemarks"></el-input>
      </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="confirm">确 定</el-button>
     </span>
    </el-dialog>
    </div>
</template>

<script>
import { ungeneratedOrderRepushOrReject } from '@/api/construction'
const colums = [
  {
    title: '订单创建时间',
    field: 'createDateTime',
    width: 150
  },
  {
    title: '所属项目',
    field: 'belongProject',
    width: 150
  },
  {
    title: '客户名称',
    field: 'customerName',
    width: 150
  },
  {
    title: '收货人',
    field: 'receiverName',
    width: 150
  },
  {
    title: '销售',
    field: 'sellerName',
    width: 150
  },
  {
    title: '订单号',
    field: 'orderNo',
    width: 150
  },
  {
    title: '客户订单号',
    field: 'customerOrderNo',
    width: 150
  },
  // itemNo (integer, optional): 订单行号 ,
  {
    title: '商品行',
    field: 'itemNo',
    width: 150
  },
  {
    title: 'sku编码',
    field: 'materialNo',
    width: 150
  },
  // materialName
  {
    title: '商品名称',
    field: 'materialName',
    width: 150
  },
  {
    title: '订单数量',
    field: 'materialCount',
    width: 150
  },
  {
    title: '采购方式',
    field: 'purchaseMethod',
    width: 150
  },
  {
    title: '供应商名称',
    field: 'providerName',
    width: 150
  },
  {
    title: '采购',
    field: 'purchaseGroupName',
    width: 150
  }
  // {
  //   title: '备注',
  //   field: 'remark',
  //   width: 150
  // }

]
export default {
  props: {
    propsData: {
      type: Array,
      default: () => []
    }

  },
  data() {
    return {
      tableHeight: 500,
      allAlign: 'center',
      colums,
      tableData: [{
        createTime: '2021-5-11',
        projectBelogns: '所属项目'
      },
      {
        createTime: '2021-5-12',
        projectBelogns: '所属项目'

      }

      ],
      actionsType: [
        {
          operateCode: 3,
          // actionName: '通过'
          actionName: '重推'
        },
        {
          operateCode: 4,
          actionName: '驳回'
        }
      ],
      dialogVisible: false,
      form: {
        rejectRemarks: ''
      },
      rules: {
        rejectRemarks: [
          {
            required: true, message: '请输入驳回原因', trigger: 'blur'

          }
        ]

      },
      parmas: ''

    };
  },
  computed: {
    ungeneratedOrderDataC() {
      return this.propsData
    }

  },

  mounted() {
    this.$nextTick(() => {
      this.tableHeight = window.innerHeight - 500
    })
    window.onresize = () => {
      this.tableHeight = window.innerHeight - 500
    }
  },
  watch: {
    propsData: {
      deep: true,
      handler(newVal) {
      }

    }
  },
  methods: {
    rePushOReject(item, row) {
      this.parmas = {
        userName: window.CUR_DATA.user && window.CUR_DATA.user.name,
        itemIds: [row.id].toString(),
        operateCode: item.operateCode
      }
      // 重推
      if (item.operateCode === 3) {
        this.ungeneratedOrderRepushOrReject(this.parmas)
        setTimeout(() => {
          this.$emit('uploadingList')
        }, 1000)

        // 驳回
      } else if (item.operateCode === 4) {
        this.dialogVisible = true
      }
    },
    async ungeneratedOrderRepushOrReject(parmas) {
      let res = await ungeneratedOrderRepushOrReject(parmas)
      if (res) {
        console.log('sucess');
      } else {
        console.log('error');
      }
    },
    handleClose(done) {
      this.$refs['form'].resetFields();
      done();
      // this.$confirm('确认关闭？')
      //   .then(_ => {
      //     done();
      //   })
      //   .catch(_ => {});
    },
    cancel() {
      this.$refs['form'].resetFields();
      this.dialogVisible = false
    },
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          Object.assign(this.parmas, this.form)
          this.ungeneratedOrderRepushOrReject(this.parmas)
          this.dialogVisible = false
          this.$refs['form'].resetFields();
          setTimeout(() => {
            this.$emit('uploadingList')
          }, 1000)
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  },
  components: {

  }
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
   display: flex;
   flex-direction: column;
   margin:0 !important;
   position:absolute;
   top:50%;
   left:50%;
   transform:translate(-50%,-50%);
   /*height:600px;*/
   max-height:calc(100% - 30px);
   max-width:calc(100% - 30px);
}
::v-deep .el-dialog .el-dialog__body {
   flex:1;
   overflow: auto;
}

</style>
