<template>
  <div class="record">
    <div v-if="!propsData.length" class='no-data'>没有数据</div>
    <div class="record-item" v-for="(item,index) in propsData" :key="index">
      <div class="text-and-button">
        <div class="text">
          <span class="container">
            申请单号：<strong>{{item.id}}</strong>,
            申请时间：<strong>{{item.submitDate}}</strong>,
            所属集团：<strong>{{item.blocName}}</strong>,
            申请人：<strong>{{item.submitUser}}</strong>
            <span class="right">
              是否低毛利：<strong :style="{color:item.isLowProfit === 1 ? 'red': ''}">{{enums.isLowProfit[item.isLowProfit]}}</strong>,
              状态：<strong :style="{color:item.status == 2 ? 'red': ''}">{{enums.status[item.status]}}</strong>,
              审核时间：<strong>{{item.reviewDate}}</strong>
            </span>
          </span>
        </div>
      </div>
      <el-table border :data="item.achitechiveItemsBOS">
        <el-table-column show-overflow-tooltip align="center" label="创建日期" width="120px" prop="createDate" />
        <el-table-column show-overflow-tooltip align="center" label="创建时间" prop="createTime" />
        <el-table-column show-overflow-tooltip align="center" label="采购组描述" prop="purchaseGroupName" />
        <el-table-column show-overflow-tooltip align="center" label="物料组描述" prop="materialGroupName" />
        <el-table-column show-overflow-tooltip align="center" label="所属项目" prop="belongProject" />
        <el-table-column show-overflow-tooltip align="center" label="客户订单号" prop="customerOrderNo" />
        <el-table-column show-overflow-tooltip align="center" label="集团" width="120px" prop="blocName" />
        <el-table-column show-overflow-tooltip align="center" label="客户名称" width="120px" prop="customerName" />
        <el-table-column show-overflow-tooltip align="center" label="收货人" prop="receiverName" />
        <el-table-column show-overflow-tooltip align="center" label="收货人电话" width="120px" prop="receiverPhone" />
        <el-table-column show-overflow-tooltip align="center" label="销售订单" width="120px" prop="orderNo" />
        <el-table-column show-overflow-tooltip align="center" label="省市区" prop="provinceName" width="200px">
          <template slot-scope="{row}">
            {{trimString(row.provinceName, row.cityName, row.areaName)}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="详细地址" prop="addressDetail" width="200px"/>
        <el-table-column show-overflow-tooltip align="center" label="订单行号" prop="itemNo" />
        <el-table-column show-overflow-tooltip align="center" label="物料编码" prop="materialNo" />
        <el-table-column show-overflow-tooltip align="center" label="物料描述" width="200px" prop="materialName" />
        <el-table-column show-overflow-tooltip align="center" label="销售单位" prop="saleUnit" />
        <el-table-column show-overflow-tooltip align="center" label="订单数量" prop="materialCount" />
        <el-table-column show-overflow-tooltip align="center" label="含税单价" prop="taxInPrice" />
        <el-table-column show-overflow-tooltip align="center" label="销售" prop="sellerName" />
        <el-table-column show-overflow-tooltip align="center" label="订单备注" prop="remark" />
        <el-table-column show-overflow-tooltip align="center" label="自营分润" prop="profitShare" />
        <el-table-column show-overflow-tooltip align="center" label="供应商" width="120px" prop="providerName" />
        <el-table-column show-overflow-tooltip align="center" label="采购方式" prop="purchaseMethod" >
          <template slot-scope="{row}">
            {{enums.purchaseMethod[row.purchaseMethod]}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="供应商编号" prop="providerId" />
        <el-table-column show-overflow-tooltip align="center" label="进项税点" prop="taxInRate" />
        <el-table-column show-overflow-tooltip align="center" label="正常采购价" prop="purchasePrice" />
        <el-table-column show-overflow-tooltip align="center" label="正常扣点" prop="deductionRage" />
        <el-table-column show-overflow-tooltip align="center" label="申请特殊采购价" width="120px" prop="vipPurchasePrice" />
        <el-table-column show-overflow-tooltip align="center" label="申请特殊扣点" width="120px" prop="vipDeductionRage" />
        <el-table-column show-overflow-tooltip align="center" label="申请原因" prop="vipMsg" />
        <el-table-column show-overflow-tooltip align="center" label="审核状态" prop="status" >
          <template slot-scope="{row}">
            {{ enums.status[row.status] }}
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { trimString } from '@/utils/index'
export default {
  name: 'auditRecord',
  props: {
    propsData: Array
  },
  data () {
    return {
      enums: {
        status: {
          0: '未提交',
          1: '待审核',
          2: '驳回',
          3: '已通过'
        },
        isLowProfit: {
          0: '否',
          1: '是'
        },
        purchaseMethod: {
          0: '自采',
          1: '他采'
        }
      }
    }
  },
  methods: {
    trimString,
    audit (row, type) {}
  }
}
</script>
<style lang="scss" scoped>
.record{
  .no-data{
    text-align: center;
  }
  .export-button{
    overflow: hidden;
    button{
      float: right;
    }
  }
  .record-item {
    margin-top:5px;
    margin-bottom:5px;
    .text-and-button{
      margin-bottom: 10px;
      .button{
        margin-right: 5px;
      }
      span.container{
        display: inline-block;
        width: 100%;
        span.right{
          float:  right;
          margin-right: 10px;
        }
      }
    }
  }
}
</style>
