<template>
<!--     :show-close="false"
 -->
  <el-dialog
    width="860px"
    :title="`${detailData ? '编辑' : '创建'}基础数据`"
    :visible="true"
    @before-close="handleClose"
     v-on="$listeners"
  >
    <el-tabs v-if="detailData" v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="基础数据" name="first">
        <dialogForm :detailData="detailData" v-on="$listeners" />
      </el-tab-pane>
      <el-tab-pane label="修改日志" name="second">
        <el-table
          v-loading="logTable.logLoading"
          :data="logTable.logList"
          border
          highlight-current-row
        >
          <el-table-column
            label="修改时间"
            min-width="200px"
            align="center"
            prop="gmtModified"
            show-overflow-tooltip
          />
          <el-table-column
            label="修改人"
            min-width="100px"
            align="center"
            prop="modifierName"
            show-overflow-tooltip
          />
          <el-table-column
            label="修改字段"
            min-width="150px"
            align="center"
            prop="modifierField"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            label="修改前"
            min-width="150px"
            align="center"
            prop="modifierFront"
            show-overflow-tooltip
          />
          <el-table-column
            label="修改后"
            min-width="150px"
            align="center"
            prop="modifierAfter"
            show-overflow-tooltip
          />
        </el-table>
        <pagination
          :total="logTable.total"
          align="right"
          :page.sync="logTable.current"
          :limit.sync="logTable.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          @pagination="getLogList"
        />
      </el-tab-pane>
    </el-tabs>

    <dialogForm v-else :detailData="detailData" v-on="$listeners" />
  </el-dialog>
</template>
<script>
import dialogForm from './dialogForm.vue';
import { getOperateLog, column } from '@/api/construction';
import Pagination from '@/components/Pagination';

export default {
  name: 'create',
  props: {
    detailData: Object
  },
  components: {
    Pagination,
    dialogForm
  },
  data() {
    return {
      column,
      activeName: 'first',
      logTable: {
        logLoading: false,
        logList: [],
        total: 0,
        current: 1,
        pageSize: 10
      }
    };
  },
  methods: {
    handleClose() {
      this.$emit('close')
    },
    handleClick(tab) {
      const { name } = tab;
      if (name === 'second') {
        this.getLogList();
      }
    },
    async getLogList() {
      try {
        this.logTable.logLoading = true;
        const params = {
          pageNo: this.logTable.current,
          pageSize: this.logTable.pageSize,
          modifierId: this.detailData.id
        };
        const res = await getOperateLog(params);
        this.logTable.total = res.total;
        this.logTable.logList = res.list;
        this.logTable.logLoading = false;
      } catch (error) {
        console.log(error);
        this.logTable.logLoading = false;
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
