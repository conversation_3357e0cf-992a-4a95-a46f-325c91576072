<template>
  <div class="page page-template-invoice" v-loading="loading">
    <el-form :model="filter" :rules="rules" ref="form" label-width="120px" label-suffix="：">
      <el-form-item label="模板匹配方式">
        <el-radio-group v-model="modelMatchType" >
          <el-radio label="1">按模板条件</el-radio>
          <el-radio label="2">指定模板编号</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="modelMatchType === '2'" label="模板编号" prop="modelCode">
        <el-input v-model.number="filter.modelCode" placeholder="请输入" clearable style="width: 192px"/>
      </el-form-item>
      <el-form-item label="单据类型" prop="modelType">
        <!-- <el-select v-model="filter.modelType" placeholder="请选择">
          <el-option v-for="item in option.modelType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select> -->
        <TemplateTypeSelect v-model="filter.modelType"/>
      </el-form-item>
      <el-form-item label="文件格式">
        <el-select v-model="filter.pdf" placeholder="请选择">
          <el-option v-for="item in option.fileType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="单据号码" prop="orderNo">
        <el-input type="textarea" resize="vertical" v-model="filter.orderNo" :placeholder="placeholder" clearable style="width: 400px;margin-right: 10px;"/>
        <el-button type="primary" @click="getInvoice">获取单据</el-button>
      </el-form-item>
    </el-form>
    <div class="file-preview" v-show="loaded">
      <el-table
        v-if="tableList.length"
        :data="tableList"
        style="width: 700px"
        highlight-current-row
      >
        <el-table-column label="模版编号" align="center" prop="modelCode" />
        <el-table-column label="模版名称" align="center" prop="modelName" />
        <el-table-column label="所属客户" align="center" prop="customerCode" />
        <el-table-column label="命中维度排序" align="center" prop="modelSeq" />
        <el-table-column label="单据文件" align="center" prop="matchUrl">
          <template slot-scope="{ row }">
            <el-tooltip :content="row.matchUrl">
              <el-link type="primary" :href="generateLink(row.matchUrl)" target="_blank">{{ getFileExt(row.matchUrl) }}</el-link>
            </el-tooltip>
            <el-button
              v-if="getFileExt(row.matchUrl) === 'EXCEL'"
              type="primary" size="mini"
              style="margin-top: 10px"
              @click="downloadTemplate(row.matchUrl)"
              icon="el-icon-download">
              下载文件
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <p class="tip" v-else>无匹配单据文件</p>
    </div>
  </div>
</template>

<script>
import TemplateTypeSelect from './TemplateTypeSelect.vue';
import { api } from '@/api/boss'

export default {
  components: {
    TemplateTypeSelect
  },
  data () {
    return {
      loaded: false,
      filter: {
        modelCode: '',
        modelType: 0,
        pdf: 1,
        orderNo: ''
      },
      modelMatchType: '1',
      rules: {
        modelCode: [
          { required: true, message: '请输入模板编号', trigger: ['blur', 'change'] },
          { pattern: /^\d+$/, message: '请输入数字', trigger: ['blur', 'change'] }
        ],
        orderNo: [{ required: true, message: '请输入单据号码', trigger: ['blur', 'change'] }]
      },
      option: {
        modelType: [
          { label: '坤合送货单', value: 0 },
          { label: 'vc直发送货单', value: 1 },
          // { label: 'vc直发拣货单', value: 2 },
          // { label: 'vc直发送货&拣货单', value: 3 },
          { label: '基于采购单的送货单', value: 4 },
          { label: '对账单', value: 5 },
          { label: '坤和标签', value: 6 },
          { label: '直发送货单标签', value: 8 },
          { label: '直发采购单标签', value: 9 }
        ],
        fileType: [
          { label: 'PDF', value: 1 },
          { label: 'EXCEL', value: 0 }
        ]
      },
      tableList: [],
      loading: false,
      map: {
        placeholder: {
          0: '请输入oms交货单号 20******',
          1: '请输入VC送货单号 VC-SHD******',
          2: '请输入VC送货单号 VC-SHD******',
          3: '请输入VC送货单号 VC-SHD******',
          4: '请输入SAP采购单号 30******',
          5: '请输入对账依据编号',
          6: '请输入oms交货单号 20******',
          8: '请输入VC送货单号 VC-SHD******',
          9: '请输入SAP采购单号 30******'
        }
      }
    }
  },
  computed: {
    placeholder () {
      return this.map.placeholder[this.filter.modelType]
    },
    isSpecType () {
      return this.filter.modelType === 0
    }
  },
  methods: {
    isExcel (filename) {
      return /\.xlsx/i.test(filename)
    },
    isPdf (filename) {
      return /\.pdf/i.test(filename)
    },
    generateLink (filename) {
      if (this.isExcel(filename)) {
        filename = `https://view.officeapps.live.com/op/view.aspx?src=${filename}`
      }
      return filename
    },
    downloadTemplate (filename) {
      window.open(filename)
    },
    getFileExt (filename) {
      if (this.isExcel(filename)) return 'EXCEL'
      if (this.isPdf(filename)) return 'PDF'
    },
    getInvoice () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.getModel()
        }
      })
    },
    getModel () {
      this.loading = true
      this.loaded = false
      const query = this.filter
      if (this.modelMatchType === '1') delete query.modelCode
      api({
        prefix: '/oms-base',
        url: '/bossmodel/new/get/model',
        query,
        complete: res => {
          this.loading = false
          this.loaded = true
            if (res) {
              this.tableList = res.map(item => {
                return { ...item, matchUrl: item.matchResultUrl }
              })
            }
        }
      })
    }
  }
}
</script>

<style lang="less">
  .file-preview {
    border-top: 1px solid #ddd;
    padding-top: 20px;
    margin-top: 30px;
    .tip {
      padding: 10px;
      background: #fff6f6;
      color: #ff8484;
    }
  }
</style>
