<template>
  <el-select
    :value="value"
    clearable
    placeholder="请选择"
    @change="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="+item.value"
    ></el-option>
  </el-select>
</template>

<script>
import {
  getModelTypeList
} from '@/api/templateCenter.js';

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Number
    }
  },
  data() {
    return {
      options: []
    }
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val)
    }
  },
  mounted() {
    getModelTypeList().then((res) => {
      if (res && res.data) {
        let options = []
        for (let [k, value] of Object.entries(res.data)) {
          options.push({
            label: value,
            value: k
          })
        }
        this.options = options
      }
    })
  }
}
</script>
