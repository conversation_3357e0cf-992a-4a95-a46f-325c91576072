<template>
  <div class="template-set-container">
    <el-row>
      <el-col :span="modelData.modelType === 13 ? 8 : 12">
        <el-form
          ref="modelForm"
          :rules="rules"
          :model="modelData"
          label-width="150px"
          class="template-set-form"
        >
          <el-form-item label="模板名称：" prop="modelName">
            <div class="single-item-body">
              <el-input
                maxlength="50"
                clearable
                placeholder="请输入模板名称"
                v-model="modelData.modelName"
              />
            </div>
          </el-form-item>
          <el-form-item v-if="isEdit" label="模板编码：" prop="modelCode">
            <div class="single-item-body">
              <el-input
                maxlength="50"
                clearable
                placeholder="请输入模板编码"
                v-model="modelData.modelCode"
                disabled
              />
            </div>
          </el-form-item>
          <el-form-item v-if="isEdit" label="模板状态：" prop="modelStatus">
            <div class="single-item-body">
              <el-select v-model="modelData.modelStatus" placeholder="请选择" disabled>
                <el-option
                  v-for="item in templateStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="所属客户：" prop="customerNo">
            <div class="single-item-body">
              <Customer
                @change="handleCustomerChange"
                v-model="modelData.customerNo"
                :initCustomer="{
                  value: modelData.customerNo,
                  label: modelData.customerName
                }"
                style="width: 100%"
              />
            </div>
          </el-form-item>
          <el-form-item label="模板类型：" prop="modelType">
            <div class="single-item-body">
              <TemplateTypeSelect v-model="modelData.modelType" />
            </div>
          </el-form-item>
          <el-form-item
            v-if="showModelSize"
            label="打印尺寸"
            prop="modelSize"
          >
            <div class="single-item-body">
              <el-select v-model="modelData.modelSize" placeholder="请选择">
                <el-option v-for="item in modelSizeOptions" :key="item" :value="item" >
                  {{ item }}
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <el-form-item label="模板命中维度：" v-if="fieldList.length > 0" required>
            <div
              class="el-form el-form--inline"
              v-for="(col, index) in modelData.matchColumnLogics"
              :key="col.key"
            >
              <!-- 字段名 -->
              <el-form-item
                :prop="`matchColumnLogics[${index}].columnName`"
                :rules="{
                  required: true,
                  message: '请选择字段名称',
                  trigger: 'change',
                }"
              >
                <el-select
                  :value="col"
                  filterable
                  placeholder="请选择"
                  value-key="columnName"
                  @change="(obj) => columnNameSelect(obj, index)"
                >
                  <el-option
                    v-for="item in fieldList"
                    :key="item.columnName"
                    :label="item.label + ' - ' + item.columnName"
                    :value="item"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span
                      style="
                        float: right;
                        color: #8492a6;
                        font-size: 13px;
                        margin-left: 10px;
                      "
                      >{{ item.columnName }}</span
                    >
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- 比较运算符 -->
              <el-form-item prop="operType">
                <el-select v-model="col.operType" clearable placeholder="请选择">
                  <el-option
                    v-for="item in generateComparisonOperatorList(col.isDateColumn,col.notEqual)"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <!-- 字段值 -->
              <el-form-item prop="columnData">
                <el-date-picker
                  v-if="col.isDateColumn"
                  v-model="col.columnData"
                  value-format="timestamp"
                  type="date"
                  placeholder="选择日期"
                >
                </el-date-picker>
                <el-input v-else clearable v-model="col.columnData" />
              </el-form-item>
              <el-button
                v-if="modelData.matchColumnLogics.length > 1"
                @click="delModelMarchCondition(col, index)"
              >
                删除</el-button
              >
              <el-button
                v-if="index == modelData.matchColumnLogics.length - 1"
                type="primary"
                @click="addModelMarchCondition"
              >
                添加</el-button
              >
              <br v-if="index != modelData.matchColumnLogics.length - 1" />
              <!-- 逻辑运算符，最后一个失效 -->
              <el-form-item
                v-if="index != modelData.matchColumnLogics.length - 1"
                prop="logicType"
              >
                <el-select
                  style="margin: 20px 0"
                  v-model="col.logicType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in logicalOperatorList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item v-if="fieldList.length > 0">
            <div class="text-content">
              {{ marchText }}
            </div>
          </el-form-item>
          <el-form-item label="命中维度排序：" prop="modelOrder">
            <div class="single-item-body">
              <el-input-number
                v-model="modelData.modelOrder"
                controls-position="right"
                :min="1"
                :max="99"
                :precision="0"
                :step="1"
              ></el-input-number>
            </div>
          </el-form-item>
          <el-row>
            <el-col :span="12" v-if="modelData.modelType !== 13">
              <el-form-item label="模板打印份数" prop="modelPrintNum">
                <el-input-number
                  v-model="modelData.modelPrintNum"
                  controls-position="right"
                  :min="1"
                  :max="99"
                  :precision="0"
                  :step="1"
                ></el-input-number>
              </el-form-item>
              </el-col>
              <el-col :span="12">
              <el-form-item label="是否页眉展示交货单号" prop="needDnNo" v-if="ifNeedDnNoShow" label-width="160px">
                <el-switch v-model="modelData.needDnNo"></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- accept -->
          <el-form-item label="定制模板：" ref="excelUpload" prop="excelModelUrl">
            <el-upload
              ref="upload"
              action="/upload"
              style="display: inline-block"
              :show-file-list="false"
              :multiple="false"
              :http-request="httpRequestHandle"
              :before-upload="$validateFileType"
              accept=".xls, .xlsx, ,.csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              :data="{}"
            >
              <el-button type="primary" plain size="mini">
                上传模板
                <i class="el-icon-upload el-icon--right" />
              </el-button>
            </el-upload>
            <el-link style="margin-left: 10px" type="info" @click="getTemplate"
              >获取初始模板</el-link
            >
            <i
              style="margin-left: 10px; color: #4caf50"
              v-if="modelData.excelModelUrl"
              class="el-icon-circle-check"
              >已上传</i
            >
            <el-link
              v-if="modelData.excelModelUrl"
              :href="
                'https://view.officeapps.live.com/op/view.aspx?src=' +
                encodeURIComponent(modelData.excelModelUrl)
              "
              style="margin-left: 10px"
              type="success"
              @click="getTemplate"
              target="_blank"
              >预览</el-link
            >
          </el-form-item>
          <el-form-item label>
            <el-button
              v-if="modelData.excelModelUrl"
              type="primary" size="mini"
              @click="downloadTemplate"
              icon="el-icon-download el-icon--right"
              >下载模版</el-button
            >
          </el-form-item>
          <el-form-item label>
            <el-button @click="cancel">关闭</el-button>
            <el-button type="primary" :loading="submitLoading" @click="handleSubmit"
              >提交</el-button
            >
            <el-upload
              ref="upload"
              action="/"
              :disabled="uploading"
              style="display: inline-block"
              :show-file-list="false"
              :multiple="false"
              accept=".pdf,.png,.jpg,.jpeg,.docx"
              :http-request="httpRequestHandle1"
              :before-upload="$validateFileType"
              :data="{}"
            >
              <el-button v-if="modelData.modelType === 13" :disabled="uploading" :loading="uploading" style="margin-left: 10px" type="primary" plain>
                {{ uploading ? '解析中' : '测试客户订单解析结果' }}
                <i class="el-icon-upload el-icon--right" />
              </el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        <div style="margin-left: 10px">
          <el-button v-if="previewData" type="success" @click="showAiQa = !showAiQa">
              AI问答
            </el-button>
            <div v-if="showAiQa && previewData" class="ai-qa-container">
              <el-input
                type="textarea"
                :rows="3"
                placeholder="请输入您的问题描述"
                v-model="aiQuestion"
              ></el-input>
              <el-button
                type="primary"
                style="margin: 10px 0"
                :loading="aiLoading"
                :disabled="!aiQuestion"
                @click="handleAiQa"
              >
                提交问题
              </el-button>
              <div v-if="aiAnswer !== null" class="markdown-container">
                <div v-html="compiledMarkdown" class="markdown-body"></div>
              </div>
            </div>
        </div>
      </el-col>
      <el-col v-if="previewData" :span="9" style="padding-right: 20px">
        <span style="margin-bottom: 10px">客户订单内容 (本次解析请求仅用于模板配置验证, 不会创建订单)</span>
        <pre class="preview-data">{{ JSON.stringify(previewData, null, 4) }}</pre>
      </el-col>
      <el-col v-if="previewData" :span="7" style="padding-right: 20px">
        <span style="margin-bottom: 10px">订单转换结果</span>
        <pre class="preview-data">{{ JSON.stringify(previewData['data']['Z.订单转换结果'], null, 4) }}</pre>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  addModel,
  goodsBrandListSearch,
  editModel,
  getInitModel,
  getModelDetail,
  getModelSizeList
} from '@/api/templateCenter.js';
import { uploadFile, testUpload, parserResultSuggestion } from '@/api/ecorp';
import * as shortid from 'shortid';
import TemplateTypeSelect from './TemplateTypeSelect.vue';
import Customer from '../../components/SearchFields/customer';
import { isEmpty } from 'lodash'
import marked from 'marked'

const templateStatusOptions = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '生效',
    value: 0
  },
  {
    label: '失效',
    value: 1
  }
]

// 新增编辑公用
export default {
  components: {
    Customer,
    TemplateTypeSelect
  },
  data() {
    return {
      fileSizeLimit: 10, // 上传文件大小限制，单位 MB
      templateStatusOptions: templateStatusOptions, // 模板状态 下拉选项
      fieldList: [], // 模板类型对应的维度列表
      comparisonOperatorList: [
        { label: '=', value: '=' },
        { label: '!=', value: '!=' },
        { label: 'in', value: 'in' }
      ], // 比较运算符
      logicalOperatorList: [
        {
          label: 'and',
          value: 'and'
        },
        {
          label: 'or',
          value: 'or'
        }
      ], // 逻辑运算符
      modelData: {
        customerNo: '',
        excelModelUrl: '',
        matchColumnLogics: [
          {
            columnData: '',
            columnName: '',
            logicType: 'and',
            operType: '=',
            matchTable: '',
            key: shortid.generate()
          }
        ],
        modelCode: 0,
        modelId: '',
        modelName: '',
        modelOrder: 1,
        modelPrintNum: 1,
        modelStatus: 0,
        modelType: 0,
        needDnNo: true,
        modelSize: ''
      },
      submitLoading: false,
      isEdit: true,
      rules: {
        modelName: [
          {
            required: true,
            message: '模板名称不能为空'
          }
        ],
        modelType: [
          {
            required: true,
            // type: 'number',
            message: '模板类型不能为空'
          }
        ],
        modelSize: [

        ],
        modelOrder: [
          {
            required: true,
            message: '命中维度排序不能为空'
          },
          {
            type: 'number',
            message: '请输入数字'
          }
        ],
        modelPrintNum: [
          {
            required: true,
            message: '模板打印份数不能为空'
          },
          {
            type: 'number',
            message: '请输入数字'
          }
        ],

        excelModelUrl: [
          {
            required: true,
            // type: 'number',
            message: '定制模板不能为空'
          }
        ],
        matchColumnLogics: [
          {
            validator: (rule, value, callback) => {
              console.log(value);
            },
            trigger: 'change'
          }
        ]
      },
      sizeObj: {},
      showModelSize: false,
      modelSizeOptions: [],
      uploading: false,
      previewData: null,
      showAiQa: false,
      aiQuestion: '',
      aiAnswer: null,
      aiLoading: false
    };
  },
  watch: {
    'modelData.modelType': {
      handler(newVal, oldVal) {
        this.changeModelType(newVal);
      }
    }
  },
  created() {
    this.initData();
    getModelSizeList().then(res => {
      if (res && res.data) {
        this.sizeObj = res.data
      }
    })
  },
  computed: {
    marchText() {
      let text = '';
      if (this.modelData && this.modelData.matchColumnLogics) {
        this.modelData.matchColumnLogics.forEach((item, index, arr) => {
          let itemText = '';
          if (
            item.columnName &&
            item.operType &&
            (item.logicType || index === arr.length - 1)
          ) {
            itemText +=
              item.columnName +
              ' ' +
              item.operType +
              ' ' +
              '\'' +
              item.columnData +
              '\'' +
              (index === arr.length - 1 ? '' : ' ' + item.logicType) +
              ' ';
          }
          text += itemText;
        });
      }
      return text.trim();
    },
    ifNeedDnNoShow() {
      return this.modelData.modelType === 0 || this.modelData.modelType === 1 || this.modelData.modelType === 4
    },
    compiledMarkdown() {
      return this.aiAnswer ? marked.parse(this.aiAnswer) : '';
    }
  },
  methods: {
    httpRequestHandle1(file) {
      if (!file.data) {
        return this.$message.error('上传文件参数缺失！');
      }
      if (!this.modelData.excelModelUrl) {
        return this.$message.error('请先上传模板后再测试')
      }
      // 校验大小
      // const isGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit;
      // if (isGtLimit) {
      //   return this.$message.error(
      //     '上传文件不能超过' + this.fileSizeLimit + 'MB!'
      //   );
      // }
      // const loading = this.$loading({
      //   lock: true,
      //   text: '解析中......',
      //   spinner: 'el-icon-loading',
      //   background: 'rgba(0, 0, 0, 0.5)'
      // });
      // file.file.name = encodeURIComponent(file.file.name);
      this.uploading = true
      this.aiAnswer = null
      const formData = new FormData();
      formData.append('file', file.file);
      formData.append('customerOrderConfigFileUrl', this.modelData.excelModelUrl);
      formData.append('customerNo', this.modelData._customerNo);

      testUpload(formData)
        .then((response) => {
          this.uploading = false
          this.previewData = response;
          if (response && response.code === 200) {
            this.previewData = response;
            this.previewData['模板编码'] = this.modelData.modelCode;
          } else {
            this.$alert(response?.msg || '文件上传失败，请重新尝试', '错误', {
              customClass: 'uploadMsg',
              type: 'error'
            })
          }
        })
        .catch((e) => {
          this.$alert('文件上传失败，请重新尝试', '错误', {
            customClass: 'uploadMsg',
            type: 'error'
          })
          this.uploading = false
        });
    },
    handleCustomerChange(val, obj) {
      console.log(val, obj);
      if (!obj) obj = { value: '', label: '' };
      this.modelData.customerNo = obj.value;
      this.modelData.customerName = obj.label;
    },
    generateComparisonOperatorList(isDataColumn, notEqual) {
      let list = [...this.comparisonOperatorList];
      if (isDataColumn) {
        list.push({ label: '>=', value: '>=' });
        list.push({ label: '<=', value: '<=' });
      }
      if (notEqual) {
        list = list.filter(item => item.label !== '!=')
      }
      return list;
    },
    columnNameSelect(obj, index) {
      let item = { ...this.modelData.matchColumnLogics[index] };
      console.log(obj, index, item);
      item.columnName = obj.columnName;
      item.columnData = '';
      item.isDateColumn = obj.isDateColumn;
      item.notEqual = obj.notEqual
      if (
        !item.isDateColumn &&
        (item.operType === '>=' || item.operType === '<=')
      ) {
        item.operType = '';
      }
      item.matchTable = obj.matchTable;
      item.operType = '';

      this.$set(this.modelData.matchColumnLogics, index, item);
    },
    changeModelType(val) {
      this.matchColumnLogics = [];
      this.excelModelUrl = '';
      this.modelOrder = 1;
      this.modelPrintNum = 1;
      this.excelModelUrl = '';
      this.previewData = null

      this.showModelSize = !isEmpty(this.sizeObj?.[val]?.sizeList)
      // this.modelSizeRequired = this.sizeObj?.[val]?.required || false
      if (this.sizeObj?.[val]?.required) {
        this.rules.modelSize = [{
            required: true,
            message: '打印尺寸不能为空'
          }]
      } else {
        this.rules.modelSize = []
      }
      this.modelSizeOptions = this.sizeObj?.[val]?.sizeList || []
      if (val === 10) this.modelData.modelSize = '80*50mm'

      this.getModelColumns();
    },
    // 删除模板命中维度
    delModelMarchCondition(col, index) {
      this.modelData.matchColumnLogics.splice(index, 1);
    },
    // 添加模板命中维度
    addModelMarchCondition() {
      this.modelData.matchColumnLogics.push({
        columnData: '',
        columnName: '',
        logicType: 'and',
        operType: '=',
        key: shortid.generate()
      });
    },
    processEditData(matchColumnLogics) {
      for (var i = 0; i < this.fieldList.length; i++) {
        for (var j = 0; j < matchColumnLogics.length; j++) {
          if (this.fieldList[i].columnName === matchColumnLogics[j].columnName) {
            matchColumnLogics[j].notEqual = this.fieldList[i].notEqual
          }
        }
      }
    },
    initData() {
      this.isEdit = this.$route.name === 'editTcList';
      // 如果是编辑，则获取页面信息
      if (this.isEdit) {
        const { modelType } = this.$route.query
        this.modelData.modelType = modelType
        const modelId = this.$route.params.templateId;
        this.getModelColumns();
        getModelDetail({ modelId }).then((res) => {
          if (res) {
            this.processEditData(res.matchColumnLogics)
            this.modelData = res;
            this.modelData.needDnNo = res.needDnNo !== 1
            if (this.modelData.customerNo) {
              this.modelData._customerNo = this.modelData.customerNo;
              this.modelData.customerNo = this.modelData.customerName;
            }
          } else {
            this.$message.error(res.msg);
          }
        });
      } else {
        this.getModelColumns();
      }
    },
    initDateMatchCol() {
      /* 时间复杂度O(n), 空间复杂度O(n) */
      let map = {};
      this.fieldList.forEach((column, index) => {
        if (column.isDateColumn === true) {
          map[column.columnName] = true;
        }
      });
      this.modelData.matchColumnLogics.forEach((column, index) => {
        if (map[column.columnName]) {
          column.isDateColumn = true;
        }
      });
    },
    async getModelColumns() {
      let param = { modelType: this.modelData.modelType };
      let res = await goodsBrandListSearch(param)
      if (res && res.length > 0) {
        this.fieldList = res.map((item) => {
          return {
            label: item.matchColumnDesc,
            columnName: item.matchColumnCode,
            matchTable: item.matchTable,
            isDateColumn: item.isDateColumn,
            notEqual: item.notEqual
          };
        });
        this.initDateMatchCol();
      } else {
        this.fieldList = []
        res.msg && this.$message.error(res.msg);
      }
      this.formatDate(this.modelData, 'get');
    },
    cancel() {
      this.$closeTag(this.$route.path);
    },
    handleSubmit() {
      this.$refs['modelForm'].validate((valid, items) => {
        if (valid) {
          this.submitLoading = true;
          if (this.isEdit) {
            this.editModel();
          } else {
            this.addModel();
          }
        } else {
          console.log(items);
        }
      });
    },
    formatDate(param, isGet) {
      param &&
        param.matchColumnLogics &&
        param.matchColumnLogics.forEach((item, index) => {
          if (isGet) {
            try {
              if (item.isDateColumn) item.columnData = Number(item.columnData);
            } catch (err) {
              console.log(err);
            }
          }
        });
    },
    addModel() {
      let param = { ...this.modelData };
      param.needDnNo = this.modelData.needDnNo === true ? 0 : 1
      delete param.modelCode;
      delete param.modelId;
      delete param.modelStatus;
      addModel(param)
        .then((res) => {
          this.submitLoading = false;
          if (res.code === 200) {
            this.$message.success('添加成功');
            sessionStorage.setItem('templateEdit', 1);
            this.cancel();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch(() => {
          this.submitLoading = false;
        });
    },
    editModel() {
      let param = { ...this.modelData };
      param.needDnNo = this.modelData.needDnNo === true ? 0 : 1
      delete param.modelCode;
      delete param.modelStatus;
      console.log(this.modelData);
      console.log(param);
      if (param.customerNo && !/\b[a-zA-Z]+\d+/.test(param.customerNo)) {
        param.customerNo = param._customerNo;
      }
      editModel(param)
        .then((res) => {
          this.submitLoading = false;
          if (res.code === 200) {
            this.$message.success('修改成功');
            sessionStorage.setItem('templateEdit', 1);
            this.cancel();
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          this.$message.error(e);
          this.submitLoading = false;
        });
    },
    getTemplate() {
      getInitModel(this.modelData.modelType).then(res => {
        window.open(res, '_blank')
      })
    },
    downloadTemplate () {
      window.open(this.modelData.excelModelUrl)
    },
    httpRequestHandle(file) {
      if (!file.data) {
        return this.$message.error('上传文件参数缺失！');
      }
      // 校验大小
      const isGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit;
      if (isGtLimit) {
        return this.$message.error(
          '上传文件不能超过' + this.fileSizeLimit + 'MB!'
        );
      }
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.5)',
        customClass: 'loadingCustomer'
      });
      this.fileName = encodeURIComponent(file.file.name);
      const formData = new FormData();
      formData.append('file', file.file);
      uploadFile(formData, 'onestop')
        .then((response) => {
          loading.close();
          if (response && response.length > 0) {
            const objectKey = response[0].objectKey;
            this.modelData.excelModelUrl = `https://zkh360-onestop.oss-cn-hangzhou.aliyuncs.com/${objectKey}`;
            this.$refs.excelUpload.clearValidate();
          } else {
            this.$message.error('文件上传失败，请重新尝试');
          }
        })
        .catch((e) => {
          this.$message.error('文件上传失败，请重新尝试');
          loading.close();
        });
    },
    handleAiQa() {
      if (!this.aiQuestion) {
        this.$message.warning('请输入问题描述');
        return;
      }
      this.aiLoading = true;
      // 准备请求数据
      const requestData = {
          content: this.previewData,
          question: this.aiQuestion
      };
      parserResultSuggestion(requestData).then(response => {
        const resp = response;
        if (resp.code !== 200) {
          this.aiAnswer = JSON.stringify(resp, null, 2);
        } else {
          this.aiAnswer = resp.data?.text || '未获取到有效回答';
        }
      }).catch(error => {
        this.aiAnswer = `请求出错: ${error.message || '未知错误'}`;
      }).finally(() => {
        this.aiLoading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.template-set-container {
  .template-set-form {
    margin-top: 30px;
    .single-item-body {
      max-width: 200px;
    }
  }
  .el-form-item {
    .el-form-item {
      margin-bottom: 0;
    }
  }
  .el-select {
    width: 100%;
  }
  .text-content {
    padding: 20px;
    max-width: 800px;
    min-height: 60px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
  }
  .preview-data {
    background: #f5f5f5;
    padding: 10px;
    // border-radius: 4px;
    height: calc(100vh - 160px);
    overflow: auto;
  }
  .ai-qa-container {
    width: 100%;
    margin-top: 10px;
  }
  .markdown-container {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    background-color: #fff;
    max-height: 400px;
    overflow-y: auto;
    word-wrap: break-word;
  }
}
</style>
<!-- <style scoped>
.preview-card {
  margin-bottom: 20px;
  height: calc(100vh - 160px);
  overflow-y: auto;
}
.data-item {
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}
.data-key {
  font-weight: bold;
  color: #409EFF;
  margin-right: 10px;
}
.data-value {
  color: #67C23A;
}
pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> -->
