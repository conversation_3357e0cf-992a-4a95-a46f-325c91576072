<template>
  <div class="app-container" v-loading="loading.containerLoading">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="130px"
        label-position="right">
        <el-row>
          <el-col :span="7">
            <el-form-item label="客户名称：" prop="customerNo">
              <el-select
                v-model="searchForm.customerNo"
                filterable
                remote
                style="width: 100%"
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="loading.customerNoLoading"
                clearable>
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="销售订单号：" prop="sapOrderNo">
              <el-input
                v-model="searchForm.sapOrderNo"
                placeholder="支持输入100个订单号，空格分隔"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="发货天数：" prop="deliveryDay">
              <el-select
                style="width: 35%"
                v-model="searchForm.deliveryDayFlag"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="(item,index) in deliveryDayFlagOptions"
                  :key="index+'_'+item.value"
                  :label="item.value"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-input
                v-if="searchForm.deliveryDayFlag != '>=<='"
                style="width: 60%;float:right"
                v-model.trim="searchForm.deliveryDay"
                placeholder="请输入"
                clearable/>
              <el-input
                v-if="searchForm.deliveryDayFlag == '>=<='"
                style="width: 30%;margin-left:2px"
                v-model.trim="searchForm.deliveryDayStart"
                placeholder="请输入"
                clearable/>
              <el-input
                v-if="searchForm.deliveryDayFlag == '>=<='"
                style="width: 30%;float:right"
                v-model.trim="searchForm.deliveryDayEnd"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="loading.searchButton"
              @click="handleSearch"
              >查询</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="销售：" prop="sellerName">
              <el-input
                v-model.trim="searchForm.sellerName"
                placeholder="请输入"
                clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客服：" prop="customerServiceName">
              <el-input
                v-model.trim="searchForm.customerServiceName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="发货方式：" prop="deliveryWay">
              <el-select
                style="width: 100%"
                v-model="searchForm.deliveryWay"
                placeholder="请选择"
                clearable>
                <el-option value="1" label="物流公司配送" ></el-option>
                <el-option value="2" label="自主车辆配送" ></el-option>
                <el-option value="3" label="震坤行自提" ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button icon="el-icon-refresh-left" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="SKU：" prop="sku">
              <el-input
                v-model.trim="searchForm.sku"
                placeholder="请输入SKU"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="是否签单返回：" prop="signingBacks">
              <el-select
                multiple
                style="width: 100%"
                v-model="searchForm.signingBacks"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in signingBacksOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="是否上传签单：" prop="isSign">
              <el-select
                style="width: 100%"
                v-model="searchForm.isSign"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in isSignOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="收货状态：" prop="receiveStatus">
              <el-select
                style="width: 100%"
                v-model="searchForm.receiveStatus"
                placeholder="请选择"
                clearable>
                <el-option
                  v-for="item in receiveStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户编码：" prop="customerNoAlias">
              <el-input
                v-model.trim="searchForm.customerNoAlias"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户订单号：" prop="customerReferenceNo">
              <el-input
                v-model.trim="searchForm.customerReferenceNo"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="订单原因：" prop="reason">
              <el-select
                style="width: 100%"
                v-model="searchForm.reason"
                placeholder="请选择"
                clearable
              >
              <el-option
                v-for="item in orderReasonList"
                :key="`${item.code}${item.label}${item.parentCode}`"
                :label="`${item.code} ${item.name}`"
                :value="item.code"
              />
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="7">
             <el-form-item label="收货联系人姓名：" prop="receiverName">
              <el-input
                v-model="searchForm.receiverName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
           </el-col>
           <el-col :span="7">
             <el-form-item label="送货单号" prop="deliveryNo">
              <el-input
                v-model.trim="searchForm.deliveryNo"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
           </el-col>
           <el-col :span="7">
             <el-form-item label="订单联系人姓名：" prop="orderContactName">
              <el-input
                v-model.trim="searchForm.orderContactName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
           </el-col>

        </el-row>
      </el-form>
    </div>
    <div class="manage-part">
      <el-checkbox v-model="searchForm.delayToday">展示晚于今日发货的订单</el-checkbox>
      <el-button type="primary" @click="exportForm" :loading="loading.exportLoading">导出</el-button>
      <!-- <el-button type="primary" @click="batchSubmit" :loading="loading.batchSubmitLoading">批量确认</el-button> -->
    </div>
    <el-table
      ref="tableRef"
      v-loading="loading.table"
      :data="listData"
      :span-method="arraySpanMethod"
      @selection-change="handleSelectionChange"
      border fit highlight-current-row stripe
      style="width: 100%">
      <el-table-column align="center" label="销售订单号" width="100px" >
        <template slot-scope="scope">
          <a style="color:#597bee;cursor:pointer;" @click="SOlinkFilter(scope.row.sapOrderNo, scope.row)">
            {{scope.row.sapOrderNo}}
          </a>
        </template>
      </el-table-column>
      <el-table-column align="center" label="客户订单号" prop="customerReferenceNo" width="100px" />
      <el-table-column align="center" label="客户名称" prop="customerName" width="200px" />
      <el-table-column align="center" label="客户编码" prop="customerNo" width="80px" />
      <el-table-column align="center" label="客服" prop="customerServiceName" />
      <el-table-column align="center" label="是否需要签单返回" prop="signingBack" width="120px" >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.signingBack == '0'?'无签单':
              scope.row.signingBack == '1'?'签单正本':
              scope.row.signingBack == '2'?'签单副本':
              scope.row.signingBack == '3'?'电子签收':''
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="VC发货单号" prop="deliveryNo" width="200px" />
      <el-table-column align="center" label="特殊交付订单" prop="deliveryNo" width="100px">
        <template slot-scope="{row}">
          <el-button
            v-if="row.specialDeliveryOrder && row.specialDeliveryOrder.isSpecialDeliveryOrder"
            type="text"
            @click="showSpOrder(row)">是
          </el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="签收状态" prop="receiveStatus" >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.receiveStatus == 'A'?'未签收':
              scope.row.receiveStatus == 'B'?'部分签收':
              scope.row.receiveStatus == 'C'?'全部签收': ''
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="发货日期" prop="deliveryTime" width="100px">
        <template slot-scope="scope">
          <div>
            {{ scope.row.deliveryTime | formateTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="发货天数" prop="deliveryDay" />
        <!-- <template slot-scope="scope">
          <span>{{scope.row.endDeliveryDay - scope.row.startDeliveryDay || ''}}</span>
        </template>
      </el-table-column> -->
      <el-table-column align="center" label="发货方式" prop="deliveryWay" width="120px">
        <template slot-scope="scope">
          <div>
            {{
              scope.row.deliveryWay == '1' ? '物流公司配送' :
              scope.row.deliveryWay == '2' ? '自主车辆配送' :
              scope.row.deliveryWay == '3' ? '震坤行自提' : ''
            }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="物流名称" prop="logisticsName" />
      <el-table-column align="center" label="物流单号" prop="logisticsNo" width="140px">
        <template slot-scope="scope">
          <div @click="viewExpressDetail(scope.row)" class="view-express-detail">
            {{ scope.row.logisticsNo }}
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="商品列表" prop="productInfos" width="200px">
          <template slot-scope="scope">
            <el-popover
              placement="top-start"
              title=""
              width="200"
              trigger="hover"
              :content="formateProductItem(scope.row.productInfos,'hover')">
              <div slot="reference">{{formateProductItem(scope.row.productInfos,'short')}}</div>
            </el-popover>
          </template>
      </el-table-column>
      <!-- <el-table-column align="center" label="操作" width="160px" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="ensureReceiveByOrder(scope.row)">整单确认</el-button>
          <el-button type="text" @click="ensureReceiveByLine(scope.row)">分行确认</el-button>
        </template>
      </el-table-column> -->
      <!-- <el-table-column type="selection" width="55" fixed="right" /> -->
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getDirectOrderList"
    />
    <SpecialOrder
      :visible.sync="visible"
      :source="source"
      />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import SpecialOrder from './specialOrder'
import {
  getDirectOrders,
  directOrderCustomerSearch,
  exportApi,
  signBatchApi,
  signSingleApi
} from '@/api/directOrder.js'
import { SOlinkFilter } from '@/filters/index.js'

export default {
  name: 'directOrderList',
  data () {
    return {
      visible: false,
      source: {},
      listLoading: false,
      customerNoOptions: [],
      listData: [],
      spanArr: [], // 二维数组，用于存放单元格合并规则（可能存在多列需要计算行合并，所以是二维数组）
      position: 0, // 用于存储相同项的开始index
      total: 0,
      searchForm: {
        customerNo: '',
        customerNoAlias: '',
        deliveryDayFlag: '',
        sapOrderNo: '',
        deliveryDay: '',
        signingBacks: '',
        sellerName: '',
        receiveStatus: '',
        customerServiceName: '',
        customerReferenceNo: '',
        deliveryWay: '',
        sku: '',
        delayToday: false,
        orderContactName: ''
      },
      signingBacksOptions: [
        { label: '无签单', value: '0' },
        { label: '签单正本', value: '1' },
        { label: '签单副本', value: '2' },
        { label: '电子签单', value: '3' }
      ],
      isSignOptions: [
        { value: '0', label: '未上传' },
        { value: '1', label: '已上传' }
      ],
      receiveStatusOptions: [
        { value: 'A', label: '未签收' },
        { value: 'B', label: '部分签收' },
        { value: 'C', label: '全部签收' }
      ],
      deliveryDayFlagOptions: [
        { value: '>=' },
        { value: '<=' },
        { value: '=' },
        { value: '>=<=' }
      ],
      loading: {
        containerLoading: false,
        searchButton: false,
        table: false,
        customerNoLoading: false,
        exportLoading: false,
        batchSubmitLoading: false
      },
      searchSonditionOpen: false,
      multiSelections: [],
      listQuery: {
        pageSize: 10,
        page: 1
      },
      mergeColumn: [0, 1, 2, 3, 4, 5]
    }
  },
  filters: {
    formateTime: (val) => {
      if (/T/.test(val)) {
        val = val.split('T')[0]
      }
      return val
    }
  },
  beforeRouteEnter (to, from, next) {
    next(vm => {
      vm.getDirectOrderList && vm.getDirectOrderList()
    })
  },
  components: {
    Pagination, SpecialOrder
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    caretIcon () {
      if (this.searchSonditionOpen) {
        return 'el-icon-caret-top'
      } else {
        return 'el-icon-caret-bottom'
      }
    },
    orderReasonList() {
      return this.$store.state.orderCommon.dictList.orderReason
    }
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
  },
  methods: {
    showSpOrder (row) {
      this.visible = true
      this.source = row.specialDeliveryOrder || {}
    },
    // 【查看】跳转到物流详情
    viewExpressDetail (row) {
      console.log(row)
      if (!row.logisticsCode) {
        return this.$message.error('物流公司编码不能为空！')
      }
      // let key = (row.logisticsNo || '') + (row.logisticsName || '')
      // this.$router.push({
      //   path: '/orderSale/formal/invoice/detail/' + key,
      //   query: {
      //     companyNo: row.logisticsCode,
      //     logisticsNo: row.logisticsNo,
      //     tagName: '物流详情'
      //   }
      // })
      window.open(`/sr/logistics?soNo=${row.soNo}&logisticsCode=${row.logisticsNo}&logisticsCompanyNo=${row.logisticsCode}`)
    },
    signBatchCommon (formData, single) {
      let signApi = signBatchApi
      let desc = '是否将所选出货单进行批量确认收货'
      if (single) { signApi = signSingleApi }
      if (single) { desc = '是否对整单进行确认' }
      this.$confirm(desc, '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then(() => {
        this.loading.containerLoading = true
        signApi(formData)
          .then(res => {
            console.log(res)
            if (res && res.code === 200) {
              this.$message.success(res.msg || res.message || '操作成功！')
              setTimeout(() => {
                this.handleSearch()
              }, 800)
            } else {
              this.$message.error(res.msg || res.message || '操作失败！')
            }
          })
          .catch(err => {
            this.$message.error(err.msg || err.message || '操作失败！')
          })
          .finally(() => {
            this.loading.containerLoading = false
            this.$refs.tableRef.clearSelection()
            this.multiSelections = []
          })
      })
    },
    batchSubmit () {
      const formData = this.multiSelections.map(item => item.deliveryNo)
      if (!formData.length) return this.$message.error('请选择行！')
      this.signBatchCommon(formData)
    },
    exportForm () {
      let queryParasm = this.formatParam(this.searchForm)
      this.loading.exportLoading = true
      exportApi(queryParasm)
        .then(_ => {
          this.$message.success('导出成功！')
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '导出失败！')
        })
        .finally(() => {
          this.loading.exportLoading = false
        })
    },
    handleSelectionChange (rows) {
      console.log(rows)
      this.multiSelections = rows
    },
    formateProductItem (list, type) {
      let ret = ''
      if (!list) list = []
      if (type === 'short') {
        ret = list.map(l => l.productName).filter(r => r).join('；')
        if (ret.length > 20) {
          ret = ret.slice(0, 20) + '...'
          return ret
        }
      }
      if (type === 'hover') {
        ret = list.map(l => l.skuNo + ' ' + l.productName).join('；')
      }
      return ret
    },
    SOlinkFilter (val, row) {
      this.$router.push(SOlinkFilter(val, row))
    },
    handleSearch () {
      this.listQuery.page = 1
      this.getDirectOrderList()
    },
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading.customerNoLoading = true
        directOrderCustomerSearch({
          name: key
        }).then(res => {
          this.loading.customerNoLoading = false
          if (res.code === 200) {
            if (res.data && res.data.length > 0) {
              this.customerNoOptions = res.data.map(item => {
                return {
                  value: item.customerNo,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    },
    handleReset () {
      this.$refs['searchForm'].resetFields()
      this.searchForm.deliveryDayFlag = ''
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data) {
      const idx = 0 // 此处定义第0列需要计算行合并
      const prop = 'sapOrderNo' // 此处定义第0列的属性名，用于数据比较
      this.spanArr[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1)
          this.position = 0
        } else {
          if (data[index][prop] === data[index - 1][prop]) {
            // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr[idx].push(1)
            this.position = index
          }
        }
      })
      // console.log(this.spanArr)
    },
    // 表格单元格合并
    arraySpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (~this.mergeColumn.indexOf(columnIndex)) {
        const _row = this.spanArr[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        // console.log('第' + rowIndex + '行', '第' + 0 + '列', 'rowspan:' + _row, 'colspan:' + _col)
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    formateListData (data) {
      let tmpData = []
      data.forEach(d => {
        tmpData.push(...d.deliveryOrders.map(order => {
          let randomId = (Math.random() * 1e10).toString(16)
          return {
            sapOrderNo: d.sapOrderNo,
            soNo: d.soNo,
            sellerName: d.sellerName,
            deliveryTime: d.deliveryTime,
            deliveryDay: d.deliveryDay,
            customerServiceName: d.customerServiceName,
            customerReferenceNo: d.customerReferenceNo,
            customerNo: d.customerNo,
            customerName: d.customerName,
            signingBack: d.signingBack,
            randomId,
            ...order
          }
        }))
      })
      this.listData = tmpData
      this.getSpanArr(this.listData)
    },
    formatParam (param) {
      let queryParasm = { ...param }
      // if (queryParasm.delayToday) {
      //   queryParasm.delayToday = 1
      // } else {
      //   queryParasm.delayToday = 0
      // }
      if (queryParasm && Array.isArray(queryParasm.signingBacks)) {
        queryParasm.signingBacks = queryParasm.signingBacks.join(',')
      }
      if (queryParasm && queryParasm.customerNoAlias && !queryParasm.customerNo) {
        queryParasm.customerNo = queryParasm.customerNoAlias
        delete queryParasm.customerNoAlias
      }
      try {
        queryParasm.sapOrderNo = queryParasm.sapOrderNo.split(/\s+|,|，/).filter(e => e).join(',')
      } catch (err) {}
      if (queryParasm) {
        if (queryParasm.deliveryDayFlag === '<=') {
          queryParasm.startDeliveryDay = queryParasm.deliveryDay
        }
        if (queryParasm.deliveryDayFlag === '>=') {
          queryParasm.startDeliveryDay = queryParasm.deliveryDay
        }
        if (queryParasm.deliveryDayFlag === '=') {
          queryParasm.startDeliveryDay = queryParasm.deliveryDay
        }
        if (queryParasm.deliveryDayFlag === '>=<=') {
          queryParasm.startDeliveryDay = queryParasm.deliveryDayStart
          queryParasm.endDeliveryDay = queryParasm.deliveryDayEnd
          delete queryParasm.deliveryDayStart
          delete queryParasm.deliveryDayEnd
        }
        queryParasm.operator = queryParasm.deliveryDayFlag
        delete queryParasm.deliveryDay
        delete queryParasm.deliveryDayFlag
        return queryParasm
      }
    },
    isInt (num) {
      // eslint-disable-next-line
      return Number(num) == num && Number.isInteger(Number(num)) && num > 0
    },
    checkDeliveryDay (param) {
      /* eslint-disable */
      if (param.deliveryDayFlag !== '>=<=') {
        if ( param.deliveryDay && !this.isInt(param.deliveryDay)) {
          this.$message.error('发货天数必须为正整数！')
          return false
        }
      } else {
        if (!param.deliveryDayStart || !param.deliveryDayEnd) {
          this.$message.error('请填写起止发货天数！')
          return false
        }
        if (
          param.deliveryDayStart && !this.isInt(param.deliveryDayStart) ||
          param.deliveryDayEnd && !this.isInt(param.deliveryDayEnd)) {
          this.$message.error('发货天数必须为正整数！')
          return false
        }
      }
      return true
    },
    getDirectOrderList () {
      let queryParasm = {
        ...this.searchForm,
        ...this.listQuery
      }
      if (!this.checkDeliveryDay(queryParasm)) return
      this.loading.table = true
      queryParasm = this.formatParam(queryParasm)
      getDirectOrders(queryParasm)
        .then(res => {
          this.loading.table = false
          if (res.code === 200) {
            this.total = res.totalCount
            if (res.data) {
              console.log(res.data)
              this.listData = res.data
              this.rawList = res.data
              this.formateListData(this.listData)
            }
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch(err => {
          this.listData = []
          console.log(err)
        })
        .finally(() => {
          this.loading.table = false
        })
    },
    ensureReceive (row) {
      console.log(row)
      this.$router.push(`directOrder/detail/${row.deliveryNo}`)
    },
    ensureReceiveByLine (row) {
      console.log(row)
      try {
        let dt = new Date(row.deliveryTime).getTime() > Date.now() + 1000 * 60 * 59 * 24
        if (dt) {
          return this.$message.error('还未到发货时间，暂不支持收货！')
        }
      } catch (err) { console.log(err) }
      this.$router.push(`directOrder/detail/${row.deliveryNo}`)
    },
    ensureReceiveByOrder (row) {
      try {
        let dt = new Date(row.deliveryTime).getTime() > Date.now() + 1000 * 60 * 59 * 24
        if (dt) {
          return this.$message.error('还未到发货时间，暂不支持收货！')
        }
      } catch (err) { console.log(err) }
      this.signBatchCommon([row.deliveryNo], true)
    }
  }
}
</script>
<style lang="scss" scoped>
.manage-part{
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  label{
    margin-right: 20px;
  }
}
.brief-info{
  margin: 20px;
  span{
    margin-left: 20px;
  }
}
.view-express-detail{
  color: steelblue;
  cursor: pointer;
}
</style>
