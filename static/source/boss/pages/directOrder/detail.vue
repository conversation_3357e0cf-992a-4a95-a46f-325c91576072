<template>
  <div class="app-container" v-loading="loading.container">
    <p class="delivery-info">送货单信息</p>
    <div class="brief-info">
      <span>订单状态：
        {{
          brief.receiveStatus == 'A'?'未签收':
          brief.receiveStatus == 'B'?'部分签收':
          brief.receiveStatus == 'C'?'全部签收': ''
        }}
      </span>
      <span>快递单号：
        <span class="logistics" v-for="(no,idx) in brief.logisticsNo" :key="idx" @click="viewExpressDetail(no)">
          {{no}}
        </span>
      </span>
      <span>物流名称：{{brief.logisticsName}}</span>
    </div>
    <div class="submit">
      <el-button @click="batchSubmit">批量确认商品行</el-button>
    </div>
    <el-table
      v-loading="loading.table"
      :data="listData"
      ref="detailTable"
      border
      fit
      highlight-current-row
      stripe
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column align="center" label="项目行" prop="deliveryLineNo" />
      <el-table-column align="center" label="商品编号" prop="skuNo" />
      <el-table-column align="center" label="商品描述" prop="productDesc" width="250"/>
      <el-table-column align="center" label="签收状态" prop="receiveStatus">
        <template slot-scope="scope">
          <span>
            {{
              scope.row.receiveStatus == 'A'?'未签收':
              scope.row.receiveStatus == 'B'?'部分签收':
              scope.row.receiveStatus == 'C'?'全部签收': ''
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="订单数量" prop="quantity" />
      <el-table-column align="center" label="订单单位" prop="quantityUnit" />
      <el-table-column align="center" label="发货数量（采购单位数量）" prop="deliveryQuantity" />
      <el-table-column align="center" label="发货数量单位（采购单位）" prop="unit" />
      <el-table-column align="center" label="已收货数量（采购单位数量）" prop="hasReceivedQuantity" />
      <el-table-column align="center" label="确认收货数量（采购单位数量）" >
        <template slot-scope="scope">
          <div>
            <el-input v-model="scope.row.receiveQuantity" clearable/>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="确认收货数量单位（采购单位）" prop="receiveUnit" />
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.current"
      :limit.sync="listQuery.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getDirectOrderList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { getDeliveryDetail, batchSignDelivery } from '@/api/directOrder.js'
export default {
  name: 'directOrderList',
  data () {
    return {
      brief: {
        logisticsName: '',
        logisticsNo: [],
        receiveStatus: ''
      },
      multiSelections: [],
      listData: [],
      total: 0,
      listQuery: {
        pageSize: 10,
        current: 1
      },
      loading: {
        container: false,
        batchConfirm: false,
        table: false
      }
    }
  },
  components: {
    Pagination // disable-eslint-line
  },
  created () {
    this.initDetail()
  },
  methods: {
    initDetail () {
      if (this.$route.params.key) {
        this.loading.container = true
        getDeliveryDetail(this.$route.params.key)
          .then(res => {
            if (res && res.code === 200 && res.data) {
              if (res.data.length > 0) {
                this.brief = {
                  logisticsName: res.data[0].logisticsName,
                  logisticsCode: res.data[0].logisticsCode,
                  logisticsNo: this.formatDeliveryNo(res.data[0].logisticsNo),
                  deliveryNo: res.data[0].deliveryNo,
                  receiveStatus: res.data[0].receiveStatus
                }
                this.total = 0
                this.listData = res.data[0].deliveryOrderPlanInfos
                console.log(this.listData)
                this.initManualSelection()
              }
            } else {
              this.$message.error('获取送货单详情失败！')
            }
          })
          .finally(() => {
            this.loading.container = false
          })
      }
    },
    formatDeliveryNo (no) {
      // 物流单号可能有多个
      let ret
      try {
        ret = no.split(/\s+/).filter(e => e)
      } catch (err) { ret = [] }
      return ret
    },
    viewExpressDetail (no) {
      // 查看物流详情
      const logisticsCode = this.brief.logisticsCode
      if (!logisticsCode) {
        return this.$message.error('物流公司编码不能为空！')
      }
      let key = (no || '') + (logisticsCode || '')
      this.$router.push({
        path: '/orderSale/formal/invoice/detail/' + key,
        query: {
          companyNo: logisticsCode,
          logisticsNo: no,
          tagName: '物流详情'
        }
      })
    },
    initManualSelection () {
      setTimeout(() => {
        this.$refs.detailTable.toggleAllSelection()
        this.listData = this.listData.map(data => {
          let receiveQuantity = Number(data.deliveryQuantity) - Number(data.hasReceivedQuantity) || ''
          return {
            ...data,
            receiveQuantity
          }
        })
      }, 200)
    },
    batchSubmit () {
      if (this.multiSelections.length === 0) {
        return this.$message.error('请选择要确认的商品行！')
      }
      if (this.multiSelections.some(sec => {
        let ret = parseFloat(sec.receiveQuantity) != sec.receiveQuantity // eslint-disable-line
        if (/\.\d{4}/.test(sec.receiveQuantity)) {
          ret = true
        }
        if (sec.receiveQuantity > sec.deliveryQuantity) { // eslint-disable-line
          ret = true
        }
        return ret
      })) {
        return this.$message.error('请输入要确认行的【确认收货数量】，只能输入数字，最多3位小数，不能大于发货数量！')
      }
      this.loading.container = true
      let param = {
        key: this.brief.deliveryNo,
        data: this.multiSelections.map(sec => ({
          deliveryItemPlanId: sec.id,
          receiveQuantity: sec.receiveQuantity
          // soNo: sec.soNo,
          // soItemNo: sec.soItemNo
        }))
      }
      // todo vc接口校验审核状态
      batchSignDelivery(param)
        .then(res => {
          if (res && res.code === 200 && res.msg === '成功') {
            this.$message.success('批量确认成功！')
            setTimeout(() => {
              this.$closeTag(this.$route.path)
            }, 1000)
          } else {
            this.$message.error(res.msg || res.message || '批量确认失败！')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('批量确认失败！')
        })
        .finally(() => {
          this.loading.container = false
          this.$refs.detailTable.clearSelection()
          console.log(this.multiSelections)
        })
    },
    handleSelectionChange (rows) {
      console.log(rows)
      this.multiSelections = rows
    },
    getDirectOrderList () {
      this.loading.table = true
      setTimeout(() => {
        this.loading.table = false
      }, 1000)
    }
  }
}
</script>
<style lang="scss" scoped>
.delivery-info{
  margin: 20px 0px;
  margin-left: 10px;
}
.brief-info{
  margin: 20px 0px;
  background-color: #f0f2f5;
  height: 40px;
  line-height: 40px;
  span:not(:first-child){
    margin-left: 40px;
  }
  span:first-child{
    margin-left: 10px;
  }
  .logistics{
    color: steelblue;
    cursor: pointer;
  }
}
.submit{
  float: right;
  margin-bottom: 10px;
}
</style>
