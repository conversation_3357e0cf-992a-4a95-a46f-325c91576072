<template>
  <el-dialog
    title="特殊详情"
    class="special-order"
    :visible="visible"
    width="800px"
    :before-close="handleClose">
    <el-table
      style="width: 100%"
      v-if="dataList.length"
      :data="dataList"
      border
      fit
      stripe
      >
      <el-table-column align="center" label="特殊类型" prop="type" width="100px" />
      <el-table-column align="left" label="详情" prop="detail">
        <template slot-scope="{row}">
          <pre v-if="row.type === '商品特殊'" v-html="row.detail"></pre>
          <span v-else>{{row.detail}}</span>
        </template>
      </el-table-column>
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="$emit('update:visible', false)">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'directOrderList',
  props: ['visible', 'source'],
  data () {
    return {}
  },
  computed: {
    dataList () {
      const dataList = []
      const { isSpecialOrder, specialOrder, isSpecialCustomer, specialCustomer, isSpecialProduct, specialProductList } = this.source || {}
      if (isSpecialOrder) {
        const {
          isFirstOrder,
          isTooBigOrder,
          weight,
          volume
        } = specialOrder
        const detail = [
          { prop: 'isFirstOrder', value: isFirstOrder, label: '首单', isBoolean: true },
          { prop: 'isTooBigOrder', value: isTooBigOrder, label: '整单超重超大', isBoolean: true }
        ]
        let detailString = this.buildDetail(detail)
        if (isTooBigOrder && weight) {
          detailString += ` (${weight}kg、${volume}m³) `
        }
        const item = {
          type: '订单特殊',
          detail: detailString
        }
        dataList.push(item)
      }
      if (isSpecialCustomer) {
        let {
          entranceReservationFlag,
          inplantDeliveryService,
          leaveEmptyRequirementFlag,
          needMoveUpstairs,
          needTraining,
          needWorkPermit,
          sendToUserFlag,
          vehicleConfigurationRequirement
        } = specialCustomer
        inplantDeliveryService = inplantDeliveryService.filter(item => {
          if (item === '协助上架' || item === '协助系统入库') return true
          return false
        })
        vehicleConfigurationRequirement = vehicleConfigurationRequirement.filter(item => {
          if (item === '需要固定车辆并提前报备') return true
          return false
        })
        const detail = [
          { prop: 'entranceReservationFlag', value: entranceReservationFlag, label: '入厂预约', isBoolean: true },
          { prop: 'needWorkPermit', value: needWorkPermit, label: '入厂工作证', isBoolean: true },
          { prop: 'vehicleConfigurationRequirement', value: vehicleConfigurationRequirement, label: '需要固定车辆并提前报备', isArray: true },
          { prop: 'needTraining', value: needTraining, label: '每次进厂参加安全培训', isBoolean: true },
          { prop: 'needMoveUpstairs', value: needMoveUpstairs, label: '需人工搬运上楼', isBoolean: true },
          { prop: 'sendToUserFlag', value: sendToUserFlag, label: '需要送到使用人', isBoolean: true },
          { prop: 'inplantDeliveryService', value: inplantDeliveryService, label: '协助上架、协助系统入库', isArray: true },
          { prop: 'leaveEmptyRequirementFlag', value: leaveEmptyRequirementFlag, label: '空车离场', isBoolean: true }
        ]
        const item = {
          type: '客户特殊',
          detail: this.buildDetail(detail)
        }
        dataList.push(item)
      }
      if (isSpecialProduct && Array.isArray(specialProductList)) {
        const data = { type: '商品特殊', detail: '' }
        const details = []
        specialProductList.filter(item => item.isSpecial).forEach(product => {
          const {
            isBreak,
            isHeteroType,
            isChemical,
            isHazmat,
            isRefrigerated,
            isFrozen,
            isTooBig,
            skuNo
          } = product
          const detail = [
            { prop: 'isBreak', value: isBreak, label: '易碎', isBoolean: true },
            { prop: 'isHeteroType', value: isHeteroType, label: '异形', isBoolean: true },
            { prop: 'isChemical', value: isChemical, label: '化学品', isBoolean: true },
            { prop: 'isHazmat', value: isHazmat, label: '危化品', isBoolean: true },
            { prop: 'isRefrigerated', value: isRefrigerated, label: '冷藏品', isBoolean: true },
            { prop: 'isFrozen', value: isFrozen, label: '冷冻品', isBoolean: true },
            { prop: 'isTooBig', value: isTooBig, label: '商品超重超大', isBoolean: true }
          ]
          details.push(skuNo + ': ' + this.buildDetail(detail))
        })
        data.detail = details.join('\n')
        dataList.push(data)
      }
      return dataList
    },
    productList () {
      let productList = []
      const { isSpecialProduct, specialProductList } = this.source || {}
      if (isSpecialProduct && Array.isArray(specialProductList)) {
        productList = specialProductList
      }
      return productList
    }
  },
  methods: {
    buildDetail (list) {
      return list.map(item => {
        let { value, label, isBoolean, isArray } = item
        if (value === null || value === undefined || value.length === 0) return false
        if (isArray) {
          return value.filter(Boolean).join('、')
        }
        if (isBoolean && !value) {
          return false
        }
        return label
      }).filter(Boolean).join('、')
    },
    handleClose (done) {
      done && done()
      this.$emit('update:visible', false)
      this.$emit('update:source', {})
    },
    mapBoolean(value) {
      return value == null ? '' : value ? '是' : '否'
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
