<template>
  <div class="purchase-process" v-loading="pageLoading">
    <PurchaseProcessForm
      ref="searchForm"
      :searchForm.sync="searchForm"
      :showFilter="showFilter"
      :buildOptions="buildOptions"
    >
      <template v-slot:button-group="{info}">
        <div v-if="info.rowIndex + 1 >= info.getRows.length" class="button-group">
          <el-button type="primary" :loading="searchLoading" @click="validateSearch">查询</el-button>
          <el-button type="default" @click="resetFields">重置</el-button>
          <el-button type="default" :icon="showFilter?'el-icon-arrow-up':'el-icon-arrow-down'" @click="handleFold">{{showFilter ? '收起' : '展开'}}</el-button>
        </div>
      </template>
    </PurchaseProcessForm>
    <PurchaseProcessGrid
      ref="grid"
      :tableLoading="tableLoading"
      :listData="listData"
      :spanArray="spanArray"
      :tableInfo="tableInfo"
      :updateLoading="updateLoading"
      :updateVisible="updateVisible"
      @exportDetail="validExportDetail"
      @handleTableInfo="handleTableInfo"
      @performanceView="performanceView"
      @showEditDialog="showEditDialog"
      @handleSearch="handleSearch"
      @setSelections="setSelections"
      @goFollow="goFollow"
    >
    <template v-slot:button-group="{selections}">
      <span class="button-group">
        <el-switch
          v-model="orderType"
          @change="handleOrderTypeChange"
          active-text="SAP订单号"
          inactive-text="OMS订单号">
        </el-switch>
        <!-- <el-button v-if="getButtonAuth('采购全流程', 'BI报表') || true" type="warning">BI报表</el-button> -->
        <el-button v-if="getButtonAuth('采购全流程', '批量跟单') || true" :disabled="!selections.length" type="primary" @click="batchGoFollow">批量跟单</el-button>
        <el-button v-if="getButtonAuth('采购全流程', '导出明细') || true" type="primary" @click="exportDetail">导出明细</el-button>
      </span>
    </template>
    </PurchaseProcessGrid>
    <BatchUpdate
      @uploadSuccess="validSearch"
      :updateVisible="updateVisible"
      :batchUpdateVisible="batchUpdateVisible"
    />
    <ResultDialog
      :dataResult="dataResult"
      :updateVisible="updateVisible"
      :resultVisible="resultVisible"
    />
    <PerformanceView
      :performanceViewVisible="performanceViewVisible"
      :performanceNodeList="performanceNodeList"
      :referWorkOrderList="referWorkOrderList"
      :updateVisible="updateVisible"
    />
  </div>
</template>

<script>
import PurchaseProcessForm from './components/purchaseProcess/form.vue'
import PurchaseProcessGrid from './components/purchaseProcess/grid.vue'
import ResultDialog from './components/purchaseProcess/ResultDialog'
import BatchUpdate from './components/purchaseProcess/BatchUpdateDialog'
import PerformanceView from './components/purchaseProcess/performanceView'
import { buildOptions, getAllDictList } from '@/utils/mm'
import { deepClone, safeRun } from '@/utils/index.js'
import { getPurchaseProcessList, exportPOLifeCycle } from '@/api/mm'
import { mapState } from 'vuex'
import { getButtonAuth } from '@/utils/auth'

export default {
  name: 'purchaseProcess',
  provide () {
    return {
      setDataResult: this.setDataResult
    }
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole
    }),
    refsForm () {
      return this.$refs.searchForm.$refs.searchForm
    },
    refsGrid () {
      return this.$refs.grid
    },
    refsTable () {
      return this.$refs.grid.$refs.purchaseProcessGrid
    }
  },
  components: {
    PurchaseProcessForm, PurchaseProcessGrid, ResultDialog, BatchUpdate, PerformanceView
  },
  data () {
    return {
      showFilter: true,
      searchForm: {
        poNoList: '',
        skuNoList: '',
        deliveryDate: '',
        factoryCode: '',
        purchaseGroupList: [],
        warehouseLocationList: [],
        isDeliveryDone: null,
        isDirect: null,
        isUrgent: null,
        isConfirmed: null
      },
      orderType: false,
      tableInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 10
      },
      tableLoading: false,
      pageLoading: false,
      searchLoading: false,
      editBatchOrder: false,
      listData: [],
      spanArray: [],
      sortInfo: {},
      resultVisible: false,
      batchUpdateVisible: false,
      dataResult: {
        failCount: 0,
        successCount: 0,
        detailList: []
      },
      performanceViewVisible: false,
      performanceNodeList: [],
      referWorkOrderList: [],
      selections: []
    }
  },
  mounted () {
    (async() => {
      await getAllDictList(this)
      this.initSearchFields()
      // this.validateSearch()
    })()
  },
  methods: {
    getButtonAuth,
    setSelections (selections) {
      this.selections = selections
    },
    goFollowByPoNo (poNo) {
      try {
        this.$closeTag('/purchaseReport/trackingOrder')
      } catch (err) {}
      this.$router.push({
        path: '/purchaseReport/trackingOrder',
        query: {
          poNo
        }
      })
    },
    batchGoFollow () {
      const poNo = this.selections.map(item => item.poNo).join(' ')
      this.goFollowByPoNo(poNo)
    },
    goFollow (row) {
      const { poNo } = row
      this.goFollowByPoNo(poNo)
    },
    randomId () {
      return Math.trunc(Math.random() * 1e16).toString(16)
    },
    handleOrderTypeChange (type) {
      console.log(type)
      type = type ? 'sap' : 'oms'
      const table = this.refsGrid
      table.setColumns(type)
    },
    string2Array (string) {
      if (Array.isArray(string)) return string
      let ret = []
      if (string && string.split) {
        ret = string.split(/\s+|，|,/).filter(Boolean)
      }
      return ret
    },
    formatSubmit (form) {
      const data = deepClone(form)
      if (Array.isArray(data.deliveryDate)) {
        data.deliveryDateBegin = data.deliveryDate[0]
        data.deliveryDateEnd = data.deliveryDate[1]
      } else {
        data.deliveryDateBegin = ''
        data.deliveryDateEnd = ''
      }

      data.poNoList = this.string2Array(data.poNoList)
      data.skuNoList = this.string2Array(data.skuNoList)

      if (Array.isArray(data.warehouseLocationList)) {
        data.warehouseLocationList = data.warehouseLocationList.map(item => item.warehouseLocationCode ? item.warehouseLocationCode : item)
      }

      for (let item in data) {
        if (typeof data[item] === 'string' && data[item].trim) {
          data[item] = data[item].trim()
        }
      }
      delete data.deliveryDate
      return data
    },
    validateListLength (data) {
      let ret = true
      if (data.poNoList && data.poNoList.length > 200) {
        this.$message.error('采购订单号最多200个！')
        ret = false
      }
      return ret
    },
    validateSearch () {
      let data = this.formatSubmit(this.searchForm)
      if (!this.validateListLength(data)) return

      this.tableInfo.pageNo = 1
      if (Array.isArray(data.poNoList) && data.poNoList.length) {
        this.refsForm.clearValidate()
        this.handleSearch('initPageNo')
      } else {
        this.refsForm.validate(async (valid) => {
          if (valid) {
            this.handleSearch('initPageNo')
          }
        })
      }
    },
    resetFields () {
      this.refsForm.resetFields()
    },
    handleFold () {
      this.showFilter = !this.showFilter
      const table = this.refsGrid
      setTimeout(() => {
        safeRun(() => {
          table.calcLeftHeight()
        })
      }, 100)
    },
    buildOptions (prop) {
      const boolList = [ 'isDeliveryDone', 'isDirect', 'isUrgent', 'isConfirmed' ]
      if (boolList.find(item => item === prop)) {
        return [
          { name: '是', value: 1 },
          { name: '否', value: 0 }
        ]
      }
      return buildOptions(prop)
    },
    initSearchFields () {
      this.findDefaultPurchaseGroup()
      this.initFields()
    },
    initFields () {
      this.searchForm.isDeliveryDone = 0
    },
    findDefaultPurchaseGroup () {
      const options = this.buildOptions('purchaseGroupList')
      const username = window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
      const purchaseGroup = options.find(option => option.securityUsername === username)
      console.log('findDefaultPurchaseGroup...', purchaseGroup)
      if (purchaseGroup && purchaseGroup.value) {
        this.searchForm.purchaseGroupList = [purchaseGroup.value]
      }
    },
    parsePurchaseGroup(data) {
      if (Array.isArray(data.purchaseGroupList) && data.purchaseGroupList.length) {
        const pgOptions = this.buildOptions('purchaseGroupList')
        const groups = []
        const matchedGroups = data.purchaseGroupList.filter(group => /\*/gi.test(group)).map(item => item.toUpperCase())
        for (let group of matchedGroups) {
          const rp = group.replace(/\*/gi, '')
          groups.push(...pgOptions.filter(item => item.value.indexOf(rp) > -1).map(item => item.value))
        }
        const newList = data.purchaseGroupList.filter(group => !/\*/gi.test(group))
        newList.push(...groups)
        data.purchaseGroupList = newList
      }
    },
    emptyValue (value) {
      return value ? value + '' : ''
    },
    getHiddenFields () {
      let hiddenProps = []
      safeRun(() => {
        if (localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE) {
          hiddenProps = JSON.parse(localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE)['purchase-process-grid'].split(',')
        }
      })
      return hiddenProps
    },
    filterHiddenFields (columns) {
      // 导出过滤隐藏的列
      let hiddenProps = []
      safeRun(() => {
        if (localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE) {
          hiddenProps = JSON.parse(localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE)['purchase-process-grid'].split(',')
        }
      })
      if (hiddenProps.length) {
        columns = columns.filter(colProp => {
          return !hiddenProps.find(prop => colProp.field === prop)
        })
      }
      return columns
    },
    showEditDialog () {
      this.editBatchOrder = true
    },
    updateLoading (loading) {
      this.pageLoading = loading
    },
    validSearch () {
      if (this.searchForm.poNoList) {
        this.refsForm.clearValidate()
        this.handleSearch()
        return
      }
      this.refsForm.validate(valid => {
        if (valid) {
          this.handleSearch()
        }
      })
    },
    validExportDetail () {
      if (this.searchForm.poNoList) {
        this.refsForm.clearValidate()
        this.exportDetail()
        return
      }
      this.refsForm.validate(valid => {
        if (valid) {
          this.exportDetail()
        }
      })
    },
    filterFields (data) {
      const fields = this.getHiddenFields()
      if (Array.isArray(fields) && fields.length) {
        data.excludeFields = fields
      }
    },
    exportDetail () {
      const form = this.formatSubmit(this.searchForm)
      const data = { ...form }
      this.pageLoading = true
      exportPOLifeCycle(data)
        .then(res => {
          if (res === null) {
            this.$message.success('导出成功，请在下载专区查看导出内容！')
            setTimeout(() => {
              this.$router.push({
                path: '/purchaseReport/downLoadList'
              })
            }, 600)
          }
          console.log(res)
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    setDataResult (data) {
      this.dataResult = data
      this.resultVisible = true
    },
    updateVisible (type, visible) {
      this[type] = visible
      if (type === 'resultVisible' && !visible) {
        this.dataResult = {
          failCount: 0,
          successCount: 0,
          detailList: []
        }
        this.validSearch()
      }
      if (type === 'performanceViewVisible' && !visible) {
        this.performanceNodeList = []
      }
    },
    validateDateRange (data) {
      let ret = true
      const { deliveryDateBegin, deliveryDateEnd } = data
      if (deliveryDateBegin && deliveryDateEnd) {
        const dayRange = Math.abs(new Date(deliveryDateBegin).getTime() - new Date(deliveryDateEnd).getTime()) / (1e3 * 3600 * 24)
        console.log(dayRange)
      }
      return ret
    },
    handleSearch () {
      try {
        this.refsTable.clearCheckboxRow()
        this.refsGrid.clearSelections()
      } catch (err) {
        console.log(err)
      }
      const data = this.formatSubmit(this.searchForm)
      data.pageNo = this.tableInfo.pageNo
      data.pageSize = this.tableInfo.pageSize

      this.pageLoading = true
      this.parsePurchaseGroup(data)
      console.log('handleSearch:', data)
      getPurchaseProcessList(data)
        .then(res => {
          if (res) this.setDataList(res)
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    performanceView (node) {
      this.performanceViewVisible = true
      this.performanceNodeList = node.performanceNodeList
      this.referWorkOrderList = node.referWorkOrderList
    },
    handleTableInfo (data) {
      this.tableInfo = { ...this.tableInfo, ...data }
      this.handleSearch()
    },
    // 处理表格数据，得到需合并的规则
    getSpanArray (data) {
      const idx = 0 // 此处定义第0列需要计算行合并
      const prop = 'randomId' // 此处定义第0列的属性名，用于数据比较
      this.spanArray[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArray[idx].push(1)
          this.position = 0
        } else {
          if (data[index][prop] && (data[index][prop] === data[index - 1][prop])) {
            // 有相同项
            this.spanArray[idx][this.position] += 1
            this.spanArray[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArray[idx].push(1)
            this.position = index
          }
        }
      })
    },
    findLastIndex (array, prop, keyword) {
      let ret = -1
      for (let i = array.length - 1; i > -1; i--) {
        if (typeof array[i][prop] === 'string' && array[i][prop].indexOf(keyword) === -1 && array[i][prop]) {
          ret = i
          break;
        }
      }
      return ret
    },
    setNodeName (row) {
      let item = {}
      try {
        let nodes = row.performanceNodeList.sort((x, y) => x.sort - y.sort)
        const index = this.findLastIndex(nodes, 'content', '无需')
        if (index !== -1) item = nodes[index]
      } catch (err) {
        console.log(err)
      }
      return item.nodeName || ''
    },
    formatMergeData (rows) {
      let data = []
      rows.forEach(row => {
        try {
          row.performanceNodeList = row.performanceNodeList.sort((x, y) => x.sort - y.sort)
        } catch (err) { console.log(err) }
        if (Array.isArray(row.performanceDetailList) && row.performanceDetailList.length) {
          const randomId = this.randomId()
          row.performanceDetailList.forEach(perf => {
            data.push({ ...row, ...perf, randomId })
          })
        } else {
          data.push(row)
        }
      })
      data.forEach(item => {
        item.nodeName = this.setNodeName(item)
      })
      this.getSpanArray(data)
      return data
    },
    setDataList (response) {
      const { total, pageNo, rows } = response
      this.listData = this.formatMergeData(rows)
      this.tableInfo.total = total
      this.tableInfo.pageNo = pageNo
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .vxe-header--row .col--checkbox .vxe-cell.c--tooltip {
  padding: 2px 0px;
  margin-left: -10px;
}
.purchase-process {
  padding: 10px 0;
  margin-right: 20px;
  .button-group{
    margin-right: 5px;
    float: right;
  }
}
</style>
