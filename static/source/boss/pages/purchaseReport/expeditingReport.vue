<template>
  <div class="app-container" v-loading="pageLoading">
     <InventoryForm ref="inventoryForm">
       <template #default>
          <el-button type="primary" @click="search">查询</el-button>
            <el-button  @click="reset">重置</el-button>
       </template>
     </InventoryForm>
      <InventoryGrid  :loading="loading" ref="grid" :mergeTableHead="mergeTableHead" :mergeTableList="mergeTableList" :dynamicTableHead="dynamicTableHead" :dynamicTableList="dynamicTableList">
         <template #default>
           <div class="inventory-upload-wrapper">
             <div class="action">
              <el-button size="small" @click="exportExpeditingList" style="margin-left:20px" :disabled="mergeTableList&&mergeTableList.length<=0" >导出明细</el-button>
             </div>
           </div>
          </template>
       </InventoryGrid>
      <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
       @pagination="getPaginationList"
      />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getExpeditingList, exportExpeditingList } from '@/api/fsReport'
import { subTableHead, TableFields, columns, tableFrontFields } from './help'
import _, { cloneDeep } from 'lodash'

import InventoryForm from './components/expeditingReport/form'
import InventoryGrid from './components/expeditingReport/grid'
import Pagination from '@/components/Pagination'

export default {
  data () {
    return {
      pageLoading: false,
      listQueryInfo: {
        pageNo: 1,
        pageSize: 50
      },
      uploadLoading: null,
      loading: false,
      total: 0,
      exportDataList: [],
      // 动态表头
      dynamicTableHead: [],
      // 动态表头表格数据
      dynamicTableList: [],
      // 合并表头
      mergeTableHead: [],
      // 合并的表格数据
      mergeTableList: [],
      initData: ''

    }
  },
  components: { InventoryForm, InventoryGrid, Pagination },
  computed: {
    ...mapState({
      warehouseList: (state) => state.orderPurchase.warehouseList,
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList
    })
  },
  async mounted() {
    this.pageLoading = true
    const pList = []
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    pList.push(this.initiGetExpeditingList())
    await Promise.all(pList).then((res) => {
      this.initData = res[res.length - 1]
      this.getAreaWareHoseDynamicTableRelatedData(this.initData)
    })
    this.pageLoading = false
  },
  methods: {
    initiGetExpeditingList() {
      this.loading = true
      const queryParams = {
        pageNo: 1,
        pageSize: 50
      }
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      queryParams.isSend = 0
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      return getExpeditingList(queryParams).then((res) => {
        this.total = (res && res.f1.total) || 0
        this.loading = false
        return res
      }).catch((err) => {
        console.log(err);
      })
    },
    getSearchExpeditingList() {
      this.loading = true
      const queryParams = {}
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      return getExpeditingList(queryParams).then((res) => {
        this.total = (res && res.f1.total) || 0
        this.loading = false
        this.getAreaWareHoseDynamicTableRelatedData(res)
        return res
      }).catch((err) => {
        console.log(err);
      })
    },
    getPaginationList() {
      this.loading = true
      const queryParams = {}
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getExpeditingList(queryParams).then((res) => {
        this.loading = false
        this.total = (res && res.f1.total) || 0
        this.getAreaWareHoseDynamicTableRelatedData(res)
      }).catch((err) => {
        console.log(err);
      })
    },
    search() {
      this.$refs.inventoryForm.$refs['searchForm'].validate((valid) => {
        if (valid) {
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.skuNoList && this.$refs.inventoryForm.copySearchForm.skuNoList.length > 500) {
            this.$alert('成品SKU不应该超过500个')
            return
          }
          this.listQueryInfo.pageNo = 1
          this.getSearchExpeditingList()
        } else {
          return false;
        }
      });
    },
    reset() {
      for (var k in this.$refs.inventoryForm.searchForm) {
        if (this.$refs.inventoryForm.searchForm.hasOwnProperty(k)) {
          if (k === 'factoryCode') {
            this.$refs.inventoryForm.searchForm[k] = '1000'
          } else if (k === 'version') {
            this.$refs.inventoryForm.searchForm[k] = 0
          } else {
            this.$refs.inventoryForm.searchForm[k] = ''
          }
        }
      }
      this.getSearchExpeditingList()
    },
    pickPointedPropety(object, keyArr) {
      return _.pick(object, keyArr)
    },
    exportExpeditingList() {
      exportExpeditingList(this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm).then((res) => {
        if (res.code === 0) {
          this.$message.success('导出成功，请在下载专区查看导出内容！')
          try {
            this.$closeTag('/purchaseReport/downLoadList')
          } catch {

          }
          setTimeout(() => {
            this.$router.push({
              path: '/purchaseReport/downLoadList'
            })
          }, 600)
        }
      })
    },
    // 获取区域仓动态表格数据
    getAreaWareHoseDynamicTableRelatedData(data) {
      const dynamicTableHead = [];
      data && data.f0 && data.f0.length > 0 && data.f0.forEach((warehouseNameTitle, index) => {
        subTableHead.forEach((subWarehouseNameTitle) => {
          let objHead = { field: '', title: '', width: 200, warehouseLocation: '', helpKey: '' };
          objHead.field = `${subWarehouseNameTitle.field}_${warehouseNameTitle.warehouseLocation}`
          objHead.title = `${warehouseNameTitle.warehouseName}-${subWarehouseNameTitle.title}`
          objHead.warehouseLocation = `${warehouseNameTitle.warehouseLocation}`
          objHead.helpKey = `${subWarehouseNameTitle.field}`
          dynamicTableHead.push(objHead)
          this.dynamicTableHead = dynamicTableHead
        })
      })
      // 注意表头重复添加问题
      const tableColumns = cloneDeep(columns)
      tableColumns.splice(tableFrontFields.length, 0, ...this.dynamicTableHead)
      this.mergeTableHead = tableColumns
      const mergeTableList = []
      data && data.f1 && data.f1.rows && data.f1.rows.length > 0 && data.f1.rows.forEach((record) => {
        let staticTableData = this.pickPointedPropety(record, TableFields)
        record.fsReportExpediteRegionList.forEach((region) => {
          let tableField = {}
          this.dynamicTableHead.forEach(header => {
            if (region.warehouseLocation === header.warehouseLocation) {
              tableField[header.field] = region[header.helpKey]
            }
          })
          // 组合列表行数据
          Object.assign(staticTableData, tableField)
        })
        // 注意表格数据是否重复添加
        mergeTableList.push(staticTableData)
      })
      this.mergeTableList = mergeTableList
    }
  }

}
</script>

<style lang="scss" scoped>
.inventory-upload-wrapper {
  .action {
    float: right;
  }
}
</style>
