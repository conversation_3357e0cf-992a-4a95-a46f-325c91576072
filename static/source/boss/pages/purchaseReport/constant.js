export const inventorySpecialsListColunms = [
  {
    label: 'SKU编码',
    prop: 'skuNo',
    minWidth: 100,
    align: 'center',
    type: 'link',
    valueFormat: (ceilValue) => {
      return `https://pc${window.CUR_DATA.env === 'pro' ? '' : '-uat'}.zkh360.com/product/skuDetail/${ceilValue}`
    }
  },
  {
    label: '状态及标签',
    prop: 'factoryStatusAndSkuTags',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    type: 'custom',
    columnSlot: 'factoryStatusAndSkuTags'
  },
  {
    label: '商品展示图',
    prop: 'headImgV2Url',
    minWidth: 120,
    align: 'center',
    type: 'custom',
    columnSlot: 'productImage'
  },
  {
    label: '产品名称',
    prop: 'productName',
    minWidth: 160,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '品牌',
    prop: 'brandName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '物料组',
    prop: 'materialGroupName',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '类目',
    prop: 'catalogName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    type: 'custom',
    columnSlot: 'catalogName'
  },
  {
    label: '商品来源',
    prop: 'skuSourceName',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '制造商型号/订货号',
    prop: 'manuFacturerMaterialNoAndDirectoryNo',
    minWidth: 160,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '核心规格',
    prop: 'coreSpecification',
    minWidth: 160,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '直销标准售价',
    prop: 'directSalesPrice',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '分销标准售价',
    prop: 'distributionSalesPrice',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '促销特价',
    prop: 'offPrice',
    minWidth: 100,
    align: 'center',
    type: 'custom',
    columnSlot: 'offPrice'
  },
  {
    label: '直销折扣率',
    prop: 'directSalesPriceRate',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '可售库存数量',
    prop: 'availableQty',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: 'custom',
    columnSlot: 'availableQty'
  },
  {
    label: '销售MOQ',
    prop: 'saleMoq',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '库存地点',
    prop: 'positionName',
    minWidth: 160,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '批次',
    prop: 'batchNo',
    minWidth: 160,
    align: 'center',
    type: ''
  },
  {
    label: '在库数量',
    prop: 'inStockQty',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '已占用数量',
    prop: 'occupyQty',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '工厂',
    prop: 'factoryName',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '考核成本单价(折损后)',
    prop: 'accessCostPrice',
    minWidth: 160,
    align: 'center',
    type: ''
  },
  {
    label: '考核成本金额(折损后)',
    prop: 'accessCostTotalPrice',
    minWidth: 160,
    align: 'center',
    type: ''
  },
  {
    label: '财务成本单价(折损后)',
    prop: 'financialCostPrice',
    minWidth: 160,
    align: 'center',
    type: ''
  },
  {
    label: '财务成本金额(折损后)',
    prop: 'financialCostTotalPrice',
    minWidth: 160,
    align: 'center',
    type: ''
  },
  {
    label: '实际成本单价',
    prop: 'actualCostPrice',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '实际成本金额',
    prop: 'actualCostTotalPrice',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '生产日期',
    prop: 'formatProduceTime',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '保质期',
    prop: 'guaranteePeriod',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '剩余期效',
    prop: 'leftGuaranteePeriod',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '入库日期',
    prop: 'formatWarehousingTime',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '库存天数',
    prop: 'warehousingDays',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '操作',
    prop: 'operate',
    minWidth: 70,
    align: 'center',
    fixed: 'right',
    type: 'custom',
    columnSlot: 'action'
  }
]
export const onlineOptions = [
  {
    value: 1,
    label: '是'
  },
  {
    value: 0,
    label: '否'
  }
]
