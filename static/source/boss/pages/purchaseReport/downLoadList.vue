<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-suffix=":"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="下载任务" prop="taskType">
              <el-select
                v-model="searchForm.taskType"
                default-first-option
                filterable
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in downLoadListTypes"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- 0进行中，1已生成，2生成失败 -->
          <el-col :span="6">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="searchForm.status"
                default-first-option
                filterable
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="申请时间" prop="taskTime">
              <el-date-picker
                style="width:100%"
                v-model="searchForm.taskTime"
                type="daterange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </el-col>
          <div style="display:inline-block;margin-left:20px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </div>
        </el-row>
      </el-form>
    </div>
    <el-table border :data="tableData" v-loading="loading" height="calc(100vh - 250px)" fixed-header>
      <el-table-column
        :show-overflow-tooltip="item.field !== 'remark'"
        align="center"
        v-for="item in columns"
        :key="item.field"
        :prop="item.field"
        :label="item.title"
        :width="item.width"
        :min-width="item.minWidth"
      >
        <template slot-scope="scope">
          <div v-if="item.field === 'downloadUrl'">
            <el-link
              type="primary"
              :disabled="scope.row.status === 0 || scope.row.status === 2||scope.row.status === 3"
              :href="scope.row.downloadUrl"
            >
              下载
            </el-link>
          </div>
          <div v-else-if="item.field === 'type'">
            {{((downLoadListTypes||[]).find(item=>item.id==scope.row.type)||{}).name }}
          </div>
          <div v-else-if="item.field === 'progress'">
            {{ scope.row.progress != null ? (scope.row.progress + '%') : '' }}
          </div>
          <div v-else-if="item.field === 'status'">
            <span v-if="scope.row.status === 0">
              进行中
            </span>
            <span v-if="scope.row.status === 1">
              已生成
            </span>
            <span v-if="scope.row.status === 2" >
              <el-tooltip class="item" effect="dark"  :content="`${scope.row.remark}`" placement="top">
                  <span>
                    <i class="el-icon-warning" style="color:red"></i>
                    <span> 生成失败</span>
                    </span>
               </el-tooltip>
            </span>
            <span v-if="scope.row.status === 3">
               已失效
            </span>
          </div>
          <div v-else-if="item.field === 'remark'">
            <div v-html="marked(scope.row.remark || '')"></div>
          </div>
          <div v-else>{{ scope.row[item.field] }}</div>
        </template>
      </el-table-column>
    </el-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getPaginationDownloadLists"
    />
  </div>
</template>

<script>
import { getDownloadLists, getExportTaskTypeList } from '@/api/mm'
import Pagination from '@/components/Pagination'
import { marked } from 'marked';

const renderer = new marked.Renderer();
renderer.link = (href, title, text) => {
  return `<a href="${href}" target="_blank" ${title ? `title="${title}"` : ''}>${text}</a>`;
};

marked.setOptions({
  breaks: true,
  gfm: true,
  renderer: renderer
});

const columns = [
  {
    field: 'id',
    title: '序号',
    width: 80
  },
  {
    field: 'type',
    title: '任务类型',
    width: 180
  },
  {
    field: 'creator',
    title: '申请人',
    width: 150
  },
  {
    field: 'gmtCreate',
    title: '申请时间',
    width: 150
  },
  {
    field: 'status',
    title: '状态',
    width: 100
  },
  {
    field: 'progress',
    title: '进度',
    width: 100
  },
  {
    field: 'downloadUrl',
    title: '操作',
    width: 80
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 200
  }
]
export default {
  data() {
    return {
      searchForm: {
        taskType: '',
        taskTime: [],
        status: ''
      },
      tableData: [],
      columns,
      listQueryInfo: {
        pageNo: 1,
        pageSize: 20
      },
      loading: false,
      downLoadListTypes: [],
      statusList: [
        {
          id: 0,
          name: '进行中'
        },
        {
          id: 1,
          name: '已生成'
        },
        {
          id: 2,
          name: '生成失败'
        },
        {
          id: 3,
          name: '已失效'
        }
      ],
      total: 0
    }
  },
  computed: {},
  created() {
    this.getDownloadLists()
    this.getExportTaskTypeList()
  },
  mounted() {},
  // watch: {
  //   'searchForm.taskTime': {
  //     deep: true,
  //     handler(val) {
  //       if (val && val.length) {
  //         this.searchForm.beginTaskTime = val[0]
  //         this.searchForm.endTaskTime = val[1]
  //       }
  //     }

  //   }
  // },
  methods: {
    marked: marked,
    search() {
      this.getDownloadLists();
    },
    reset() {
      this.$refs['searchForm'].resetFields()
      this.getDownloadLists()
    },
    getDownloadLists() {
      this.loading = true
      const queryParams = {}
      queryParams.current = this.listQueryInfo.pageNo
      queryParams.size = this.listQueryInfo.pageSize
      queryParams.beginTaskTime = this.searchForm.taskTime && this.searchForm.taskTime.length ? this.searchForm.taskTime[0] : ''
      queryParams.endTaskTime = this.searchForm.taskTime && this.searchForm.taskTime.length ? this.searchForm.taskTime[1] : ''
      Object.assign(queryParams, this.searchForm)
      getDownloadLists(queryParams).then((res) => {
        this.tableData = res.records
        this.total = res.total
        this.loading = false
      })
    },
    getPaginationDownloadLists() {
      this.loading = true
      const queryParams = {}
      queryParams.current = this.listQueryInfo.pageNo
      queryParams.size = this.listQueryInfo.pageSize
      queryParams.beginTaskTime = this.searchForm.taskTime && this.searchForm.taskTime.length ? this.searchForm.taskTime[0] : ''
      queryParams.endTaskTime = this.searchForm.taskTime && this.searchForm.taskTime.length ? this.searchForm.taskTime[1] : ''
      Object.assign(queryParams, this.searchForm)
      getDownloadLists(queryParams).then((res) => {
        this.tableData = res.records
        this.total = res.total
        this.loading = false
      })
    },
    getExportTaskTypeList() {
      getExportTaskTypeList().then((res) => {
        if (res && res.code === 200) {
          let dic = res.data
          for (var k in dic) {
            let obj = {};
            obj.id = k;
            obj.name = dic[k]
            this.downLoadListTypes.push(obj)
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  },
  components: {
    Pagination
  }
}
</script>

<style scoped lang="scss">
.app-container {
  height: calc(100vh - 110px);
  display: flex;
  flex-direction: column;
}

.el-table {
  flex: 1;
  overflow: auto;
}

.el-table .cell {
  white-space: pre-wrap;
}

.el-pagination {
  margin-top: 20px;
}
</style>
