<template>
  <div>
    <div class="app-container order-list-components">
      <div class="filter-container">
        <el-form
          ref="searchForm"
          :rules="rules"
          :model="searchForm"
          label-width="120px"
          label-suffix=":"
        >
          <el-row>
            <el-col :span="12">
              <el-form-item label="订单号" prop="orderNo">
                <el-input
                  v-model="searchForm.orderNo"
                  placeholder="BOSS单号 或 SAP单号 均支持搜索，同时支持10个单号，以空格或换行符分隔"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="变更日期" prop="changeDate">
                <el-date-picker
                  v-model="searchForm.changeDate"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="SKU编码" prop="skuNo">
                <el-input
                  v-model="searchForm.skuNo"
                  placeholder="最多支持30个sku按空格隔开搜索"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="单据类型" prop="orderType">
                <el-select
                  v-model="searchForm.orderType"
                  filterable
                  clearable
                  style="width:100%"
                >
                  <el-option
                    v-for="item in orderTypeList"
                    :key="item.value"
                    :label="item.value + ' ' + item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="单据类型" prop="entityType">
                <el-select
                  v-model="searchForm.entityType"
                  filterable
                  clearable
                  style="width:100%"
                >
                  <el-option
                    v-for="item in orderTypeListCustom"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col>
              <el-form-item  class="min-width">
                <el-button type="primary" @click="search('searchForm')"
                  >查询</el-button
                >
                <el-button type="primary" @click="reset('searchForm')"
                  >重置</el-button
                >
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="operate-table">
        <p class="notify">
          列表说明：若商品行号、计划/组件/固资行号均为空，则表示修改抬头数据；若商品行号不为空，计划/组件/固资行号为空，则表示修改商品行数据；否则表示修改计划/组件/固资数据。
        </p>
        <el-table
          :data="operateList"
          :span-method="arraySpanMethod"
          :header-cell-style="{ 'text-align': 'center' }"
          border
          v-loading="loading"
          :height="gridHeight">
          <el-table-column
            prop="orderNo"
            label="BOSS单号"
            width="120"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="sapOrderNo"
            label="SAP单号"
            width="120"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="supplierName"
            label="供应商"
            width="200"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="变更时间"
            width="180"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="createUser"
            label="操作人"
            width="180"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="itemNo"
            label="商品行号"
            width="120"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="计划/组件/固资行号"
            width="180"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.planNo"> {{ scope.row.planNo }}</span>
              <span v-if="scope.row.componentNo">
                {{ scope.row.componentNo }}</span
              >
              <span v-if="scope.row.fixedAssetsNo">
                {{ scope.row.fixedAssetsNo }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="skuNo"
            label="SKU编码"
            width="129"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="变更字段"
            width="180"
            prop="property"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="变更字段"
            width="180"
            prop="propertyName"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="变更前"
            width="180"
            prop="oldValue"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            label="变更后"
            width="180"
            prop="newValue"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <!-- 单元格合并辅助行  -->

          <el-table-column
          v-if="false"
            prop="unique"
            label="unique"
            width="120"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table>
        <pagination
          v-show="total >= 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.pageNo"
          :limit.sync="listQueryInfo.pageSize"
          layout="total, sizes,prev, pager, next, jumper"
          @pagination="getPaginationOperateList"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getOperateList } from '@/api/mm'
import Pagination from '@/components/Pagination'
import _ from 'lodash'
import * as moment from 'moment'
import { mapState } from 'vuex'
import { throttle, safeRun } from '@/utils/index'

export default {
  props: {},
  data() {
    return {
      gridHeight: 400,
      searchForm: {
        orderNo: '',
        changeDate: '',
        start: '',
        end: '',
        skuNo: '',
        orderType: '',
        entityType: 0

      },
      rules: {
        orderNo: [
          {
            required: true,
            message: '请输入BOSS单号 或 SAP订单号',
            trigger: 'blur'
          }
        ],
        entityType: [
          {
            required: true,
            message: '请选择单据类型',
            trigger: 'blur'
          }
        ]
      },
      listQueryInfo: {
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      // 货权转移列表
      operateList: [],
      // 表格数据加载
      loading: false,
      // 物料组
      time: moment().format('YYYY-MM-DD'),
      mergeColumn: [0, 1, 2, 3, 4],
      spanArr: [], // 二维数组，用于存放单元格合并规则（可能存在多列需要计算行合并，所以是二维数组）
      position: '',
      orderTypeListCustom: [
        {
          name: '采购单',
          value: 0
        },
        {
          name: '内向交货单',
          value: 1
        },
        {
          name: '库存申请单',
          value: 2
        }
      ]
    }
  },
  computed: {
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      dictList: (state) => state.orderPurchase.dictList
    }),
    copySearchForm() {
      this.clean(this.searchForm)
      let processdSearchForm = {}
      for (var k in this.searchForm) {
        if (k === 'orderNo' || k === 'skuNo') {
          processdSearchForm[k] = this.searchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else if (k === 'changeDate') {
          processdSearchForm.start = this.searchForm.changeDate[0]
          processdSearchForm.end = this.searchForm.changeDate[1]
        } else {
          processdSearchForm[k] = this.searchForm[k]
        }
      }
      return processdSearchForm
    },
    orderTypeList() {
      return this.dictList['orderType']
    }
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    console.log(this.$root)
  },
  mounted() {
    setTimeout(this.calcLeftHeight, 100)
    this._gridResizeHandler = throttle(this.calcLeftHeight, 600)
    window.addEventListener('resize', this._gridResizeHandler, false)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this._gridResizeHandler, false)
  },
  methods: {
    calcLeftHeight () {
      safeRun(() => {
        const form = document.querySelector('.filter-container')
        const table = document.querySelector('.operate-table')
        const innerHeight = window.innerHeight - 130
        const existHeight = form.offsetHeight + table.offsetHeight
        // 已占据高度
        // console.log(`existHeight: ${existHeight}, innerHeight: ${innerHeight}`)
        const paddingHeight = innerHeight - existHeight
        const floorHeight = Math.floor(paddingHeight * 400 / 400)
        // console.log(`floorHeight: ${floorHeight}, gridHeight: ${this.gridHeight}`)
        if (floorHeight > 5 || floorHeight < -5) {
          this.gridHeight += floorHeight
          if (this.gridHeight <= 400) {
            this.gridHeight = 400
          }
        }
      })
    },
    search(formName) {
      if (this.copySearchForm.skuNoList?.length > 30) {
        this.$alert('SKU编码不应该超过200项')
        return false
      }
      // if (!this.copySearchForm.orderNo) {
      //   this.$alert('请输入采购单号 或 SAP订单号')
      //   return false
      // }
      if (this.copySearchForm.orderNo?.length > 10) {
        this.$alert('Boss单号 或 SAP订单号不应该超过10项')
        return false
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.getOperateList()
        } else {
          return false
        }
      })
    },
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
      delete this.searchForm.materialGroupNumTmp
    },
    // 清楚所有查询参数
    reset(formName) {
      // this.$refs[formName].resetFields()
      for (var k in this.searchForm) {
        if (k === 'entityType') {
          this.searchForm[k] = 0
        } else {
          this.searchForm[k] = ''
        }
      }
    },
    // 列表查詢
    getOperateList() {
      this.loading = true
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 10
      let query = {
        pageNo: this.listQueryInfo.pageNo,
        pageSize: this.listQueryInfo.pageSize
      }
      Object.assign(this.copySearchForm, query)
      getOperateList(this.copySearchForm).then((res) => {
        this.loading = false
        this.total = (res && res.total) || 0
        this.operateList = this.fomatterOperateList(res && res.rows)
        this.operateList.forEach(item => {
          item.unique = `${item.orderNo}_${item.createTime}_${item.createUser}`
        })
        console.log(this.operateList);
        this.getSpanArr(this.operateList)
      })
    },
    pickPointedPropety(object, keyArr) {
      return _.pick(object, keyArr)
    },
    // 表格单元格合并
    arraySpanMethod({ row, column, rowIndex, columnIndex }) {
      if (this.mergeColumn.indexOf(columnIndex) !== -1) {
        const _row = this.spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        // console.log('第' + rowIndex + '行', '第' + 0 + '列', 'rowspan:' + _row, 'colspan:' + _col)
        return {
          rowspan: _row,
          colspan: _col
        }
      } else {
        return false
      }
    },
    // methods中定义方法
    getSpanArr(data) {
      let that = this
      // 页面展示的数据，不一定是全部的数据，所以每次都清空之前存储的 保证遍历的数据是最新的数据。以免造成数据渲染混乱
      that.spanArr = []
      that.pos = 0
      // 遍历数据
      data.forEach((item, index) => {
        // 判断是否是第一项
        if (index === 0) {
          this.spanArr.push(1)
          this.pos = 0
        } else {
          // 不是第一项时，就根据标识去存储
          if (data[index].unique === data[index - 1].unique) {
            // 查找到符合条件的数据时每次要把之前存储的数据+1
            this.spanArr[this.pos] += 1
            this.spanArr.push(0)
          } else {
            // 没有符合的数据时，要记住当前的index
            this.spanArr.push(1)
            this.pos = index
          }
        }
      })
    },

    fomatterOperateList(operateList) {
      let processedRowList = []
      operateList.forEach((rowItem) => {
        let headCommon = this.pickPointedPropety(rowItem, [
          'id',
          'createUser',
          'createTime',
          'updateUser',
          'updateTime',
          'orderNo',
          'sapOrderNo',
          'supplierName',
          'unique'
        ])
        // 抬头数据
        rowItem.diff &&
          rowItem.diff.forEach((headDiffItem) => {
            let headRowTep = {}
            Object.assign(headRowTep, headDiffItem, headCommon)
            processedRowList.push(headRowTep)
          })
        // if (rowItem.diff?.length > 0) {
          // 商品行数据
        rowItem.itemList &&
            rowItem.itemList.forEach((goodsDiffRelated) => {
              let goodsCommon = this.pickPointedPropety(goodsDiffRelated, [
                'itemNo',
                'skuNo'
              ])
              goodsDiffRelated.diff &&
                goodsDiffRelated.diff.forEach((goodsDiffItem) => {
                  let goodRowTemp = {}
                  Object.assign(
                    goodRowTemp,
                    goodsDiffItem,
                    headCommon,
                    goodsCommon
                  )
                  processedRowList.push(goodRowTemp)
                })
              goodsDiffRelated.planList &&
                goodsDiffRelated.planList.forEach((planDiffRelated) => {
                  let planCommon = this.pickPointedPropety(planDiffRelated, [
                    'planNo'
                  ])
                  planDiffRelated.diff &&
                    planDiffRelated.diff.forEach((planDiffItem) => {
                      let planRowTemp = {}
                      Object.assign(
                        planRowTemp,
                        planDiffItem,
                        planCommon,
                        goodsCommon,
                        headCommon
                      )
                      processedRowList.push(planRowTemp)
                    })
                })
              goodsDiffRelated.componentList &&
                goodsDiffRelated.componentList.forEach(
                  (componentDiffRelated) => {
                    let componnentCom = this.pickPointedPropety(
                      componentDiffRelated,
                      ['componentNo']
                    )
                    componentDiffRelated.diff &&
                      componentDiffRelated.diff.forEach((componnetItem) => {
                        let componnetTemp = {}
                        Object.assign(
                          componnetTemp,
                          componnetItem,
                          componnentCom,
                          goodsCommon,
                          headCommon
                        )
                        processedRowList.push(componnetTemp)
                      })
                  }
                )
              goodsDiffRelated.fixedAssetsList &&
                goodsDiffRelated.fixedAssetsList.forEach((goodsDiffRelated) => {
                  let fixedAssetsCom = this.pickPointedPropety(
                    goodsDiffRelated,
                    ['fixedAssetsNo']
                  )
                  goodsDiffRelated.diff &&
                    goodsDiffRelated.diff.forEach((fixedAssetsItem) => {
                      let fixedAssetsTmp = {}
                      Object.assign(
                        fixedAssetsTmp,
                        fixedAssetsItem,
                        fixedAssetsCom,
                        goodsCommon,
                        headCommon
                      )
                      processedRowList.push(fixedAssetsTmp)
                    })
                })
            })
        // }
      })
      return processedRowList
    },
    getPaginationOperateList() {
      this.loading = true
      let query = {
        pageNo: this.listQueryInfo.pageNo,
        pageSize: this.listQueryInfo.pageSize
      }
      Object.assign(this.copySearchForm, query)
      getOperateList(this.copySearchForm).then((res) => {
        this.loading = false
        this.total = res.total
        this.operateList = this.fomatterOperateList(res.rows)
        this.operateList.forEach(item => {
          item.unique = `${item.orderNo}_${item.createTime}_${item.createUser}`
        })
        this.getSpanArr(this.operateList)
      })
    }
  },
  components: {
    Pagination
  }
}
</script>

<style scoped lang="scss">
.notify {
  line-height: 16px;
  font-size: 12px;
  margin-bottom: 5px;
}
.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
  width: 100%;
}
.min-width {
  min-width: 130px;
}
.hide {
  visibility: hidden;
}
</style>
