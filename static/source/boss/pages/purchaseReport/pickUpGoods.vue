<template>
  <div class="app-container" v-loading="pageLoading">
     <InventoryForm ref="inventoryForm">
       <template #default>
          <el-button type="primary" @click="search">查询</el-button>
            <el-button  @click="reset">重置</el-button>
       </template>
     </InventoryForm>
      <InventoryGrid :list="list"  :loading="loading" ref="grid">
         <template #default>
           <div class="inventory-upload-wrapper">
             <div class="action">
                <el-button type="primary" @click="saveSnapshot" :disabled="snapshotDisable">存为快照</el-button>
              <el-button size="small" @click="exportList" style="margin-left:20px" :disabled="list&&list.length<=0" >导出明细</el-button>
             </div>
           </div>
          </template>
       </InventoryGrid>
      <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
       @pagination="getPaginationList"
      />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getPickUpGoodsList, exportPickUpGoodsList, sendPickUpGoodsList } from '@/api/fsReport'
import _ from 'lodash'

import InventoryForm from './components/pickUpGoods/form'
import InventoryGrid from './components/pickUpGoods/grid'
import Pagination from '@/components/Pagination'

export default {
  data () {
    return {
      pageLoading: false,
      list: [],
      listQueryInfo: {
        pageNo: 1,
        pageSize: 50
      },
      loading: false,
      total: 0

    }
  },
  components: { InventoryForm, InventoryGrid, Pagination },
  computed: {
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList
    }),

    snapshotDisable() {
      return this.$refs.inventoryForm.searchForm.version === 1
    }
  },
  async created() {
    this.pageLoading = true
    const pList = []
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    await Promise.all(pList)
    this.pageLoading = false
  },
  mounted() {
    this.getPickUpGoodsList()
  },
  methods: {
    getPickUpGoodsList() {
      this.loading = true
      const queryParams = {}
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getPickUpGoodsList(queryParams).then((res) => {
        this.loading = false
        this.list = res.rows
        this.total = res.total
      }).catch((err) => {
        console.log(err);
      })
    },
    getPaginationList() {
      this.loading = true
      const queryParams = {}
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getPickUpGoodsList(queryParams).then((res) => {
        this.loading = false
        this.list = res.rows
        this.total = res.total
      }).catch((err) => {
        console.log(err);
      })
    },
    search() {
      this.$refs.inventoryForm.$refs['searchForm'].validate((valid) => {
        if (valid) {
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.skuNoList && this.$refs.inventoryForm.copySearchForm.skuNoList.length > 500) {
            this.$alert('SKU编码不应该超过500个')
            return
          }
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.poNoList && this.$refs.inventoryForm.copySearchForm.poNoList.length > 10) {
            this.$alert('采购订单不应该超过10个')
            return
          }
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.supplierOrderNoList && this.$refs.inventoryForm.copySearchForm.supplierOrderNoList.length > 10) {
            this.$alert('供应商订单不应该超过10个')
            return
          }
          this.listQueryInfo.pageNo = 1
          this.getPickUpGoodsList()
        } else {
          return false;
        }
      });
    },
    reset() {
      for (var k in this.$refs.inventoryForm.searchForm) {
        if (this.$refs.inventoryForm.searchForm.hasOwnProperty(k)) {
          if (k === 'factoryCode') {
            this.$refs.inventoryForm.searchForm[k] = '1000'
          } else if (k === 'version') {
            this.$refs.inventoryForm.searchForm[k] = 0
          } else {
            this.$refs.inventoryForm.searchForm[k] = ''
          }
        }
      }
      this.getPickUpGoodsList()
    },
    pickPointedPropety(object, keyArr) {
      return _.pick(object, keyArr)
    },
    exportList() {
      exportPickUpGoodsList(this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm).then((res) => {
        if (res && res.code === 0) {
          this.$message.success('导出成功，请在下载专区查看导出内容！')
          try {
            this.$closeTag('/purchaseReport/downLoadList')
          } catch {

          }
          setTimeout(() => {
            this.$router.push({
              path: '/purchaseReport/downLoadList'
            })
          }, 600)
        }
      })
    },

    saveSnapshot() {
      this.$confirm('将当前的提货、催货数据存为快照?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        sendPickUpGoodsList().then((res) => {
          if (res && res.code === 0) {
            this.$message.success('快照存取成功')
            this.getPickUpGoodsList()
          } else {
            this.$message.error(res.msg)
          }
        }).catch((err) => { console.log(err) })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    }

  }
}
</script>

<style lang="scss" scoped>
.inventory-upload-wrapper {
  .action {
    float: right;
  }
}
</style>
