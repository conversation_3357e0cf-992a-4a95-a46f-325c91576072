<template>
    <el-select
      v-model="skuNo"
      placeholder="选择物料组"
      filterable
      default-first-option
      remote
      reserve-keyword
      clearable
      style="width:100%"
      size="mini"
      value-key="materialGroupNum"
      :disabled="disabled"
      :remote-method="remoteMethod"
      :loading="loading"
      @change="handleSelectChange"
      @clear="handleClear"
    >
      <el-option
        v-for="(item) in skuList"
        :key="item.materialGroupNum"
        :label="item.materialGroupNum+' '+item.materialGroupName"
        :value="item"
      >
      </el-option>
    </el-select>
</template>

<script>
import { searchMaterialGroup } from '@/api/mm';
export default {
  props: ['row', 'data', 'materialList', 'disabled', 'materialGroup'],
  data() {
    return {
      loading: false,
      skuList: this.materialList || []
    }
  },
  computed: {
    skuNo: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    handleClear() {
      this.$emit('clear')
    },
    handleSelectChange (val) {
      if (val) {
        this.$emit('change', val, this.row)
      }
    },
    remoteMethod (val) {
      if (val != null) {
        this.loading = true
        searchMaterialGroup(val).then(data => {
          this.loading = false
          if (data) {
            this.skuList = data
          }
        })
      } else {
        this.skuList = []
      }
    }
  },
  watch: {
    materialList (val) {
      this.skuList = val
    }
  }
}
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
  }
}
</style>
