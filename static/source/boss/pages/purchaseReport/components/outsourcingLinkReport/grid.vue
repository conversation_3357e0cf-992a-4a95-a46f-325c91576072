<template>
  <vxe-grid
    border
    resizable
    keep-source
    show-overflow
    ref="inventoryGrid"
    id="inventory_grid"
    align="center"
    height="500"
    :loading="loading"
    :custom-config="tableCustom"
    :data="gridList"
    :columns="columns"
    :toolbar-config="tableToolbar"
    :edit-config="{trigger: 'click', mode: 'row', showStatus: true}">
    <template v-slot:toolbar_buttons>
      <slot></slot>
    </template>
    <template v-slot:upperNo="{ row }">
      <div v-for="(item, index) in mapOrder(row.upperNo)" :key="index">
        <span class="link-to-detail" @click="toPODetail(item.order)">{{item.order}}</span>
        <span>/{{item.text}}</span>
      </div>
    </template>
    <template v-slot:componentIntransitOrderList="{ row }">
      <div v-for="(item, index) in mapOrder(row.componentIntransitOrderList)" :key="index">
        <span class="link-to-detail" @click="toPODetail(item.order)">{{item.order}}</span>
        <span>/{{item.text}}</span>
      </div>
    </template>
    <template v-slot:componentIntransitInventoryList="{ row }">
      <div v-for="(item, index) in mapOrder(row.componentIntransitInventoryList)" :key="index">
        <span class="link-to-detail" @click="toPODetail(item.order)">{{item.order}}</span>
        <span>/{{item.text}}</span>
      </div>
    </template>
    <template v-slot:outSourcingIAOList="{ row }">
      <div v-for="(item, index) in mapOrder(row.outSourcingIAOList)" :key="index">
        <span class="link-to-detail" @click="toInvDetail(item.order)">{{item.order}}</span>
        <span>/{{item.text}}</span>
      </div>
    </template>
    <template v-slot:systemStatus="{row}">
      {{mapArray(row.systemStatus)}}
    </template>
    <template v-slot:systemStatusText="{row}">
      {{mapArray(row.systemStatusText)}}
    </template>
    <template v-slot:asnNo="{row}">
      {{mapArray(row.asnNo)}}
    </template>
    <template v-slot:asnStatus="{row}">
      {{mapArray(row.asnStatus)}}
    </template>
    <template v-slot:checkBatchList="{row}">
      {{mapArray(row.checkBatchList)}}
    </template>
    <template v-slot:checkQuantityList="{row}">
      {{mapArray(row.checkQuantityList)}}
    </template>
    <template v-slot:postInQuantityList="{row}">
      {{mapArray(row.postInQuantityList)}}
    </template>
    <template v-slot:checkCreateDate="{row}">
      {{mapArray(row.checkCreateDate)}}
    </template>
    <template v-slot:checkApproveDate="{row}">
      {{mapArray(row.checkApproveDate)}}
    </template>
    <template v-slot:codeList="{row}">
      {{mapArray(row.codeList)}}
    </template>
    <template v-slot:shortTextList="{row}">
      {{mapArray(row.shortTextList)}}
    </template>
    <template v-slot:checkResultList="{row}">
      {{mapArray(row.checkResultList)}}
    </template>
    <template v-slot:="{ row }">
      {{row.componentIntransitInventoryList}}
    </template>
    <template v-slot:purchaseGroup_default="{ row }">
      {{row.purchaseGroup + ' ' + ((purchaseGroupList||[]).find(item => item.groupCode === row.purchaseGroup) || {}).userName}}
    </template>
    <template v-slot:isUrgent_default="{ row }">
      {{mapBoolean(row.isUrgent)}}
    </template>
    <template v-slot:componentWarehouseLocation_default="{ row }">
      {{row.componentWarehouseLocation&&(row.componentWarehouseLocation + ' ' + ((warehouseList||[]).find(item => item.warehouseLocationCode === row.componentWarehouseLocation) || {}).warehouseLocationName)}}
    </template>
    <template v-slot:warehouseLocation_default="{ row }">
      {{row.warehouseLocation&&(row.warehouseLocation + ' ' + ((warehouseList||[]).find(item => item.warehouseLocationCode === row.warehouseLocation) || {}).warehouseLocationName)}}
    </template>
    <template v-slot:approveStep_default="{ row }">
        {{ ((dictList['approveStatus']||[]).find(item => item.value == row.approveStep) || {}).name}}
      </template>
      <template v-slot:supplierNo_default="{row}">
        {{row.supplierNo +' '+ row.supplierName }}
      </template>
  </vxe-grid>
</template>

<script>
import { mapState } from 'vuex'

import * as moment from 'moment'
const columns = [
  {
    field: 'upperNo',
    title: '上阶委外订单',
    showOverflow: false,
    type: 'html',
    width: 200,
    slots: {
      default: 'upperNo'
    }
  },
  {
    field: 'isUrgent',
    title: '是否急单',
    width: 100,
    slots: {
      default: 'isUrgent_default'
    }
  },
  {
    field: 'urgentNote',
    title: '加急标识备注',
    width: 100
  },
  {
    field: 'poNo',
    title: '采购订单',
    width: 100
  },
  {
    field: 'itemNo',
    title: '项目行',
    width: 100
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    width: 100,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'createDate',
    title: '创建日期',
    width: 150
  },
  {
    field: 'componentSkuNo',
    title: '组件SKU',
    width: 150
  },
  {
    field: 'componentMaterialDescription',
    title: '组件SKU描述',
    width: 150
  },
  {
    field: 'thousandWeight',
    title: '千支重/kg',
    width: 150
  },
  {
    field: 'componentIntransitOrderList',
    title: '组件在途订单',
    showOverflow: false,
    type: 'html',
    width: 200,
    slots: {
      default: 'componentIntransitOrderList'
    }
  },
  {
    field: 'componentIntransitInventoryList',
    title: '组件在途可用库存',
    width: 200,
    slots: {
      default: 'componentIntransitInventoryList'
    }
  },
  {
    field: 'outSourcingIAOList',
    title: '委外发/退料单',
    showOverflow: false,
    type: 'html',
    width: 200,
    slots: {
      default: 'outSourcingIAOList'
    }
  },
  {
    field: 'componentWarehouseLocation',
    title: '组件库存地点',
    width: 200,
    slots: {
      default: 'componentWarehouseLocation_default'
    }
  },
  {
    field: 'componentRequiredQuantity',
    title: '组件需求数量',
    width: 100
  },
  {
    field: 'hasMakerQuantity',
    title: '已制单数量',
    width: 100
  },
  {
    field: 'unMakerQuantity',
    title: '未制单数量',
    width: 80
  },
  {
    field: 'availableMakerQuantity',
    title: '可制单数量',
    width: 80
  },
  {
    field: 'balanceStockQuantity',
    title: '结余库存',
    width: 80
  },
  {
    field: 'availableStockQuantity',
    title: '可分配数量',
    width: 80
  },
  {
    field: 'totalMakerUnPostQuantity',
    title: '累计制单未过账',
    width: 150
  },
  {
    field: 'componentStockQuantity',
    title: '组件在库库存',
    width: 150
  },
  {
    field: 'inStockQuantity',
    title: '成品在库可用库存（良品仓）',
    width: 200
  },
  {
    field: 'supplierQuantity',
    title: '供应商库存',
    width: 80
  },
  {
    field: 'approveStep',
    title: '审批状态',
    width: 80,
    slots: {
      default: 'approveStep_default'
    }
  },
  {
    field: 'supplierNo',
    title: '供应商',
    width: 120,
    slots: {
      default: 'supplierNo_default'
    }
  },
  {
    field: 'skuNo',
    title: '成品SKU',
    width: 120
  },
  {
    field: 'materialDescription',
    title: '成品SKU描述',
    width: 120
  },
  {
    field: 'itemQuantity',
    title: '订单数量',
    width: 80
  },
  {
    field: 'unit',
    title: '订单单位',
    width: 80
  },
  {
    field: 'itemDeliveryDate',
    title: '交货日期',
    width: 150
  },
  {
    field: 'warehouseLocation',
    title: '成品库存地点',
    width: 200,
    slots: {
      default: 'warehouseLocation_default'
    }
  },
  {
    field: 'receivedQuantity',
    title: '已收数量',
    width: 100
  },
  {
    field: 'outstandingQuantity',
    title: '未收数量',
    width: 100
  },
  {
    field: 'createUser',
    title: '创建人',
    width: 100
  },
  {
    field: 'systemStatus',
    title: '系统状态',
    width: 100,
    slots: {
      default: 'systemStatus'
    }
  },
  {
    field: 'systemStatusText',
    title: '系统状态描述',
    width: 100,
    slots: {
      default: 'systemStatusText'
    }
  },
  {
    field: 'asnNo',
    title: 'ASN单号',
    width: 100,
    slots: {
      default: 'asnNo'
    }
  },
  {
    field: 'asnStatus',
    title: 'ASN状态',
    width: 100,
    slots: {
      default: 'asnStatus'
    }
  },
  {
    field: 'checkBatchList',
    title: '检验批次',
    width: 100,
    slots: {
      default: 'checkBatchList'
    }
  },
  {
    field: 'checkQuantityList',
    title: '报检数量',
    width: 100,
    slots: {
      default: 'checkQuantityList'
    }
  },
  {
    field: 'postInQuantityList',
    title: '入库数量',
    width: 100,
    slots: {
      default: 'postInQuantityList'
    }
  },
  {
    field: 'checkCreateDate',
    title: '质检审批创建时间',
    width: 120,
    slots: {
      default: 'checkCreateDate'
    }
  },
  {
    field: 'checkApproveDate',
    title: '质检单的审批时间',
    width: 140,
    slots: {
      default: 'checkApproveDate'
    }
  },
  {
    field: 'codeList',
    title: '代码',
    width: 100,
    slots: {
      default: 'codeList'
    }
  },
  {
    field: 'shortTextList',
    title: '短文本',
    width: 100,
    slots: {
      default: 'shortTextList'
    }
  },
  {
    field: 'checkResultList',
    title: '检验结果',
    width: 100,
    slots: {
      default: 'checkResultList'
    }
  }
]
export default {
  data () {
    return {
      columns,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      time: moment().format('YYYY-MM-DD'),
      gridList: []
    }
  },
  props: {
    list: {
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    list: {
      deep: true,
      handler(val) {
        if (val) {
          this.gridList = val
        }
      }
    }
  },
  computed: {
    ...mapState({
      dictList: (state) => state.orderPurchase.dictList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      purchaseGroupList: (state) => state.orderPurchase.purchaseList,
      searchMaterialGroupList: (state) => state.orderPurchase.searchMaterialGroupList
    })
  },
  methods: {
    toPODetail (orderNo) {
      if (!orderNo) return
      this.$router.push({
        path: `/orderPurchase/detail/${orderNo}`,
        query: {
          tagName: `${orderNo}详情`
        }
      })
    },
    toInvDetail (orderNo) {
      if (!orderNo) return
      this.$router.push({
        path: `/inventoryManagement/detail/${orderNo}`,
        query: {
          tagName: `${orderNo}详情`
        }
      })
    },
    mapBoolean (data) {
      // eslint-disable-next-line eqeqeq
      return data == 1 ? '是' : '否'
    },
    hasValidOrder (str) {
      let order = str;
      let text = str;
      try {
        let m1 = str.match(/(.*?)\//)
        if (m1 && m1[1]) {
          order = m1[1]
        }
      } catch (err) { console.log(err) }
      try {
        let m2 = str.match(/(?:.*?)\/(.*)/)
        if (m2 && m2[1]) {
          text = m2[1]
        }
      } catch (err) { console.log(err) }
      return { order, text }
    },
    mapOrder (array) {
      if (!Array.isArray(array) || !array.length) return ''
      return array.map(item => {
        const { order, text } = this.hasValidOrder(item)
        return {
          order, text
        }
      })
    },
    mapArray (array) {
      if (!Array.isArray(array)) return ''
      return array.join('\n')
    }
  }
}
</script>

<style lang="scss" scoped>
span.link-to-detail{
  color: #597bee;
  cursor: pointer;
}
</style>
