<template>
  <div class="inventory-form" >
    <el-form
      :model="searchForm"
      ref="searchForm"
      style="width: 100%"
      label-suffix=":"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="10">
          <el-col :span="8">
          <el-form-item label="工厂名称" prop="factory">
            <el-select
              v-model="searchForm.factory"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="(item,index) in factoryList"
                :key="index"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="成品SKU" prop="skuNoList">
            <el-input
              v-model="searchForm.skuNoList"
              clearable
              placeholder="多个sku用空格隔开，最多500个"
            />
          </el-form-item>
        </el-col>
         <el-col :span="8">
          <el-form-item label="供应商" prop="supplierNo">
            <SelectSupplier
              clearable
              :data.sync="searchForm.supplierNo"
              @change="(val) => (searchForm.supplierNo = val)"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="采购订单" prop="poNoList">
              <el-input
                v-model="searchForm.poNoList"
                clearable
                placeholder="多个采购订单用空格隔开，最多200个"
              />
            </el-form-item>
        </el-col>
          <el-col :span="8">
            <el-form-item label="组件SKU" prop="componentSkuNoList">
              <el-input
                v-model="searchForm.componentSkuNoList"
                clearable
                placeholder="多个sku用空格隔开，最多500个"
              />
            </el-form-item>
        </el-col>
         <el-col :span="8">
         <el-form-item label="物料组" prop="materialGroupNum">
            <MaterialGroup
              ref="materialGroup"
              :defaultSearchValue="430"
              :style="{ width: '100%' }"
              v-model="searchForm.materialGroupNum"
            />
          </el-form-item>
          </el-col>
      </el-row>
      <el-row :gutter="10">
         <el-col :span="8">
          <el-form-item label="采购姓名" prop="purchaseGroup">
            <el-select
              filterable
              default-first-option
              v-model="searchForm.purchaseGroup"
              placeholder="请选择"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in purchaseGroupList"
                :key="item.id"
                :label="item.groupCode + ' ' + item.userName"
                :value="item.groupCode"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
          <el-col :span="8">
          <el-form-item label="组件仓位" prop="componentWarehouseLocation">
            <el-select
              v-model="searchForm.componentWarehouseLocation"
              filterable
              default-first-option
              clearable
              style="width: 100%"
              placeholder="请选择仓库地点"
            >
                <el-option
                  v-for="(item, index) in storeWarehouseListFilterByFactory"
                  :key="index"
                 :label="
                  `【工厂${item.factoryCode}】${item.warehouseLocationCode} ${item.warehouseLocationName}`
                "
                :value="`${item.factoryCode}_${item.warehouseLocationCode}`"
                >
                </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
              <div style="display:inline-block;">
                <slot></slot>
              </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectSupplier from '@/pages/purchaseReport/components/outsourcingLinkReport/SelectSupplier'

import MaterialGroup from '../SelectMaterialGroupDefault'
import { deepClone } from '@/utils/index.js'

export default {
  data() {
    return {
      searchForm: {
        skuNoList: '',
        factory: '1000',
        poNoList: '',
        componentSkuNoList: '',
        componentWarehouseLocation: '',
        supplierNo: '',
        // 采购姓名 purchaseGroup==》
        purchaseGroup: '',
        materialGroupNum: 430

      },
      rules: {
        factory: [
          {
            required: true,
            message: '请输入',
            trigger: 'change'
          }
        ]
      },
      warehouseListFilterByFactory: [],
      leftDayStartErr: false,
      leftDayEndErr: false,
      stockAgeStartErr: false,
      stockAgeEndErr: false
    }
  },
  components: { SelectSupplier, MaterialGroup },
  computed: {
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      factoryList: (state) => state.orderPurchase.factoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      purchaseGroupList: (state) => state.orderPurchase.purchaseList
    }),
    copySearchForm() {
      const cloneSearchForm = deepClone(this.searchForm)
      this.clean(cloneSearchForm)
      let processdSearchForm = {}
      for (var k in cloneSearchForm) {
        if (k === 'skuNoList' || k === 'poNoList' || k === 'componentSkuNoList') {
          processdSearchForm[k] = cloneSearchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else if (k === 'productionTime') {
          processdSearchForm.productionStartDate =
            cloneSearchForm.productionTime[0]
          processdSearchForm.productionEndDate =
            cloneSearchForm.productionTime[1]
          delete cloneSearchForm.productionTime
        } else if (k === 'componentWarehouseLocation') {
          processdSearchForm[k] = cloneSearchForm[k].split('_')[1]
        } else {
          processdSearchForm[k] = cloneSearchForm[k]
        }
      }
      return processdSearchForm
    },
    storeWarehouseListFilterByFactory() {
      const factoryCodeArr = this.searchForm.factory
      let a = null;
      if (factoryCodeArr) {
        let tmpWarehouseListFilterByFactory = []
        tmpWarehouseListFilterByFactory = this.warehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCodeArr)
        a = tmpWarehouseListFilterByFactory
      }
      return a
    }

  },
  watch: {
    'searchForm.factory'(factoryCode) {
      this.warehouseListFilterByFactory = this.warehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCode)
    }

  },
  methods: {
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.inline-block {
  position: absolute;
  width: 100px;
}
</style>
