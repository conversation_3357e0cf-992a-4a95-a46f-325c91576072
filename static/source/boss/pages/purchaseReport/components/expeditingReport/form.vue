<template>
  <div class="inventory-form">
    <el-form
      :model="searchForm"
      ref="searchForm"
      style="width: 100%"
      label-suffix=":"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="10" class="flex-row-wrapper">
          <el-col :span="8">
          <el-form-item label="工厂名称" prop="factoryCode">
            <el-select
              disabled
              v-model="searchForm.factoryCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="(item,index) in factoryList"
                :key="index"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SKU编码" prop="skuNoList">
            <el-input
              v-model="searchForm.skuNoList"
              clearable
              placeholder="最多500个SKU按空格或换行符隔开搜索"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
           <el-form-item label="创建日期"
                        prop="createTime">
            <el-date-picker v-model="searchForm.createTime" style="width:100%"
                            :disabled="searchForm.isSend===0"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
           <el-form-item label="数据版本"
                        prop="isSend" >
            <el-select v-model="searchForm.isSend" clearable style="width:100%">
              <el-option v-for="(item,index) in versionList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
         <el-col :span="8">
           <el-form-item label="是否停用"
                        prop="skuStatus">
             <el-select v-model="searchForm.skuStatus" clearable>
               <el-option  v-for="(item,index) in skuStatus" :key="index" :label="item.label" :value="item.value"></el-option>
             </el-select>
          </el-form-item>
        </el-col>
        <el-col  :span="8">
          <el-form-item label="催货总数" prop="totalExpedite2NonZero">
            <el-select v-model="searchForm.totalExpedite2NonZero" clearable>
              <el-option  v-for="(item,index) in expeditingPlusNumbr" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
         <el-col :span="8">
          <el-form-item>
              <div style="display:inline-block;">
                <slot></slot>
              </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { deepClone } from '@/utils/index.js'
import { versionList, skuStatus, expeditingPlusNumbr } from '@/pages/purchaseReport/help'
import moment from 'moment'

export default {
  data() {
    return {
      searchForm: {
        factoryCode: '1000',
        skuNoList: '',
        isSend: 0,
        createTime: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        totalExpedite2NonZero: 1

      },
      rules: {
        factoryCode: [
          {
            required: true,
            message: '请输入',
            trigger: 'change'
          }
        ],
        isSend: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ]
      },
      versionList,
      skuStatus,
      expeditingPlusNumbr
    }
  },
  watch: {
    'searchForm.isSend'(val) {
      if (val === 0) {
        this.searchForm.createTime = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      if (val === 1) {
        this.searchForm.createTime = []
      }
    }

  },
  computed: {
    ...mapState({
      factoryList: (state) => state.orderPurchase.factoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList
    }),
    copySearchForm() {
      const cloneSearchForm = deepClone(this.searchForm)
      this.clean(cloneSearchForm)
      let processdSearchForm = {}
      for (var k in cloneSearchForm) {
        if (k === 'skuNoList') {
          processdSearchForm[k] = cloneSearchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else if (k === 'createTime') {
          processdSearchForm.createDateStart =
            cloneSearchForm.createTime[0]
          processdSearchForm.createDateEnd =
            cloneSearchForm.createTime[1]
          delete cloneSearchForm.createTime
        } else {
          processdSearchForm[k] = cloneSearchForm[k]
        }
      }
      return processdSearchForm
    }

  },
  methods: {
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.inline-block {
  position: absolute;
  width: 100px;
}
.flex-row-wrapper{
    display: flex;
    flex-wrap: wrap;
  }
</style>
