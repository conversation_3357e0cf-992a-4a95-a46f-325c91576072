<template>
   <vxe-grid
    border
    resizable
    keep-source
    show-overflow
    ref="inventoryGrid"
    id="inventory_grid"
    align="center"
    height="500"
    :loading="loading"
    :custom-config="tableCustom"
    :data="mergeTableList"
    :columns="mergeTableHead"
    :toolbar-config="tableToolbar"
    :edit-config="{trigger: 'click', mode: 'row', showStatus: true}">
    <template v-slot:toolbar_buttons>
      <slot></slot>
    </template>
    <template v-slot:isSent_default="{ row }">
      {{mapBoolean(row.isSent)}}
    </template>
    <template v-slot:skuStatus_default="{ row }">
      {{mapBoolean(row.skuStatus)}}
    </template>
  </vxe-grid>
</template>

<script>
import { mapState } from 'vuex'

import * as moment from 'moment'
export default {
  data () {
    return {
      columns: [],
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      time: moment().format('YYYY-MM-DD'),
      gridList: []
    }
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    mergeTableHead: {
      type: Array,
      default: () => []
    },
    mergeTableList: {
      type: Array,
      default: () => []
    }

  },
  watch: {
    list: {
      deep: true,
      handler(val) {
        if (val) {
          this.gridList = val
        }
      }
    },
    mergeTableHead: {
      deep: true,
      handler(val) {
        if (val) {
          this.mergeTableHead = val
        }
      }

    },
    mergeTableList: {
      deep: true,
      handler(val) {
        if (val) {
          this.mergeTableList = val
        }
      }
    }

  },
  computed: {
    ...mapState({
      dictList: (state) => state.orderPurchase.dictList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      purchaseGroupList: (state) => state.orderPurchase.purchaseList,
      searchMaterialGroupList: (state) => state.orderPurchase.searchMaterialGroupList
    })
  },
  created() {
    console.log(new Date().getTime())
    console.log(JSON.stringify(this.dynamicTableHead))
    console.log(this.columns)
    this.columns.concat(this.dynamicTableHead)
    // console.log()
    console.log(this.columns)
  },
  mounted() {

  },
  methods: {
    mapBoolean (data) {
      // eslint-disable-next-line eqeqeq
      return data == 1 ? '是' : '否'
    }

  }
}
</script>

<style lang="scss" scoped>

</style>
