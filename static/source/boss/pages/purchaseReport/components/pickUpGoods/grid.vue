<template>
   <vxe-grid
    border
    resizable
    keep-source
    show-overflow
    ref="inventoryGrid"
    id="inventory_grid"
    align="center"
    height="500"
    :loading="loading"
    :custom-config="tableCustom"
    :data="gridList"
    :columns="columns"
    :toolbar-config="tableToolbar"
    :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
     @checkbox-change="checkboxChangeEvent"
     @checkbox-all="checkboxChangeEvent"

    >
    <template v-slot:toolbar_buttons>
      <slot></slot>
    </template>
     <template v-slot:isSent_default="{ row }">
      {{mapBoolean(row.isSent)}}
    </template>
     <template v-slot:skuStatus_default="{ row }">
      {{mapBoolean(row.skuStatus)}}
    </template>
  </vxe-grid>
</template>

<script>
import { mapState } from 'vuex'

import * as moment from 'moment'
const columns = [

  {

    field: 'poNo',
    title: '采购订单',
    width: 100
  },
  {
    field: 'poItemNo',
    title: '项目行',
    width: 100
  },
  {
    field: 'supplierOrderNo',
    title: '供应商订单号',
    width: 100
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    width: 100
  },
  {
    field: 'coreSpecification',
    title: '核心规格',
    width: 100
  },
  {
    field: 'qualityExecutionStandard',
    title: '执行标准',
    width: 100
  },
  {
    field: 'uomIdName',
    title: '基本单位',
    width: 100
  },
  {
    field: 'warehouseLocation',
    title: '库存地点',
    width: 100
  },
  {
    field: 'itemQuantity',
    title: '订单数量',
    width: 100
  },
  {
    field: 'receiveQuantity',
    title: '已交数量',
    width: 100
  },
  {
    field: 'unreceiveQuantity',
    title: '未交数量',
    width: 100
  },
  {
    field: 'intransitQuantity',
    title: '在途数量',
    width: 100
  },
  {

    field: 'itemDeliveryDate',
    title: '交货日期',
    width: 150
  },
  {
    field: 'purchaseMpq',
    title: '采购MPQ',
    width: 100
  },
  {
    field: 'pickupQuantity',
    title: '提货数量',
    width: 100
  },
  {
    field: 'pickupQuantityBox',
    title: '提货箱数',
    width: 100
  },
  {
    field: 'remark',
    title: '备注',
    width: 200
  },
  {
    field: 'factoryCode',
    title: '工厂代码',
    width: 100
  },
  {
    field: 'manufacturerMaterialNo',
    title: '制造商型号',
    width: 200
  },
  {
    field: 'materialDescribe',
    title: '物料描述',
    width: 400
  },
  {
    field: 'surfaceTreatment',
    title: '表面处理',
    width: 100
  },
  {
    field: 'isSent',
    title: '是否发送',
    width: 100,
    slots: {
      default: 'isSent_default'
    }
  },

  {
    field: 'gmtCreate',
    title: '创建时间',
    width: 150
  },
  {

    field: 'skuStatus',
    title: '是否启用',
    width: 100,
    slots: {
      default: 'skuStatus_default'

    }
  }

]

export default {
  data () {
    return {
      columns,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      time: moment().format('YYYY-MM-DD'),
      gridList: [],
      isAllChecked: false,
      isIndeterminate: false,
      selectRecords: []
    }
  },
  props: {
    list: {
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    list: {
      deep: true,
      handler(val) {
        if (val) {
          this.gridList = val
        }
      }
    }
  },
  computed: {
    ...mapState({
      dictList: (state) => state.orderPurchase.dictList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      purchaseGroupList: (state) => state.orderPurchase.purchaseList,
      searchMaterialGroupList: (state) => state.orderPurchase.searchMaterialGroupList
    })
  },
  methods: {
    mapBoolean (data) {
      // eslint-disable-next-line eqeqeq
      return data == 1 ? '是' : '否'
    },
    checkboxChangeEvent ({ records }) {
      const $grid = this.$refs.inventoryGrid
      this.isAllChecked = $grid.isAllCheckboxChecked()
      this.isIndeterminate = $grid.isAllCheckboxIndeterminate()
      this.selectRecords = records
      this.$emit('selectRecords', records)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
