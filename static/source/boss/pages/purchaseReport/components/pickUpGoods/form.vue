<template>
  <div class="inventory-form">
    <el-form
      :model="searchForm"
      ref="searchForm"
      style="width: 100%"
      label-suffix=":"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="10" class="flex-row-wrapper">
          <el-col :span="8">
          <el-form-item label="工厂名称" prop="factoryCode">
            <el-select
             disabled
              v-model="searchForm.factoryCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
              @change="changeFac"
            >
              <el-option
                v-for="(item,index) in factoryList"
                :key="index"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SKU编码" prop="skuNoList">
            <el-input
              v-model="searchForm.skuNoList"
              clearable
              placeholder="多个sku用空格隔开，最多500个"
            />
          </el-form-item>
        </el-col>
         <el-col :span="8">
           <el-form-item label="创建日期"
                        prop="createTime">
            <el-date-picker style="width:100%" v-model="searchForm.createDate"
                            :disabled="searchForm.isSend===0"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
            <el-form-item label="采购订单" prop="poNoList">
              <el-input
                v-model="searchForm.poNoList"
                clearable
                placeholder="采购单号或SAP订单号均支持，最多10个单号按空格分隔"
              />
            </el-form-item>
        </el-col>
          <el-col :span="8">
           <el-form-item label="执行标准"
                        prop="qualityExecutionStandard">
             <el-input v-model="searchForm.qualityExecutionStandard" placeholder="请输入执行标准"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
           <el-form-item label="核心规格"
                        prop="coreSpecification">
             <el-input v-model="searchForm.coreSpecification" placeholder="请输入核心规格"></el-input>
          </el-form-item>
        </el-col>
          <el-col :span="8">
          <el-form-item label="库存地点" prop="warehouseLocation">
            <el-select
              v-model="searchForm.warehouseLocation"
              filterable
              default-first-option
              clearable
              style="width: 100%"
              placeholder="请选择库存地点"
            >
                <el-option
                  v-for="(item, index) in storeWarehouseListFilterByFactory"
                  :key="index"
                 :label="
                  `【工厂${item.factoryCode}】${item.warehouseLocationCode} ${item.warehouseLocationName}`
                "
                :value="`${item.factoryCode}_${item.warehouseLocationCode}`"
                >
                </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
            <el-form-item label="供应商订单" prop="supplierOrderNoList">
              <el-input
                v-model="searchForm.supplierOrderNoList"
                clearable
                placeholder="最多10个单号按空格分隔"
              />
            </el-form-item>
        </el-col>
        <el-col :span="8">
           <el-form-item label="交货日期"
                        prop="itemDeliveryDate">
            <el-date-picker style="width:100%" v-model="searchForm.itemDeliveryDate"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            clearable>
            </el-date-picker>
          </el-form-item>
        </el-col>
         <el-col :span="8">
           <el-form-item label="数据版本"
                        prop="isSend">
            <el-select v-model="searchForm.isSend" clearable style="width:100%">
              <el-option v-for="(item,index) in versionList" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
         <el-col :span="8">
           <el-form-item label="制造商型号"
                        prop="manufacturerMaterialNo">
             <el-input v-model="searchForm.manufacturerMaterialNo" placeholder="请输入制造商型号"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
           <el-form-item label="是否停用"
                        prop="skuStatus">
             <el-select v-model="searchForm.skuStatus" clearable>
               <el-option  v-for="(item,index) in skuStatus" :key="index" :label="item.label" :value="item.value"></el-option>
             </el-select>
          </el-form-item>
        </el-col>
        <el-col  :span="8">
          <el-form-item label="提货数量" prop="pickupNonZero">
            <el-select v-model="searchForm.pickupNonZero" clearable>
              <el-option  v-for="(item,index) in pickUpGoodsPlusNumbr" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
         <el-col :span="8">
          <el-form-item>
              <div style="display:inline-block;">
                <slot></slot>
              </div>
          </el-form-item>
        </el-col>
    </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { versionList, skuStatus, pickUpGoodsPlusNumbr } from '@/pages/purchaseReport/help'
import moment from 'moment'

import { deepClone } from '@/utils/index.js'

export default {
  data() {
    return {
      searchForm: {
        factoryCode: '1000',
        skuNoList: '',
        poNoList: '',
        qualityExecutionStandard: '',
        coreSpecification: '',
        warehouseLocation: '',
        supplierOrderNoList: '',
        createDate: [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')],
        isSend: 0,
        manufacturerMaterialNo: '',
        pickupNonZero: 1

      },
      rules: {
        factoryCode: [
          {
            required: true,
            message: '请输入',
            trigger: 'change'
          }
        ],
        isSend: [
          {
            required: true,
            message: '请选择',
            trigger: 'change'
          }
        ]
      },
      versionList,
      skuStatus,
      pickUpGoodsPlusNumbr

    }
  },
  watch: {
    'searchForm.isSend'(val) {
      if (val === 0) {
        this.searchForm.createDate = [moment().format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]
      }
      if (val === 1) {
        this.searchForm.createDate = []
      }
    }

  },
  computed: {
    ...mapState({
      factoryList: (state) => state.orderPurchase.factoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList
    }),
    copySearchForm() {
      const cloneSearchForm = deepClone(this.searchForm)
      this.clean(cloneSearchForm)
      let processdSearchForm = {}
      for (var k in cloneSearchForm) {
        if (k === 'skuNoList' || k === 'poNoList' || k === 'supplierOrderNoList') {
          processdSearchForm[k] = cloneSearchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else if (k === 'createDate') {
          processdSearchForm.createDateStart =
            cloneSearchForm.createDate[0]
          processdSearchForm.createDateEnd =
            cloneSearchForm.createDate[1]
          delete cloneSearchForm.createDate
        } else if (k === 'itemDeliveryDate') {
          processdSearchForm.itemDeliveryDateStart =
            cloneSearchForm.itemDeliveryDate[0]
          processdSearchForm.itemDeliveryDateEnd =
            cloneSearchForm.itemDeliveryDate[1]
          delete cloneSearchForm.itemDeliveryDate
        } else if (k === 'warehouseLocation') {
          processdSearchForm[k] = cloneSearchForm[k].split('_')[1]
        } else {
          processdSearchForm[k] = cloneSearchForm[k]
        }
      }
      return processdSearchForm
    },
    storeWarehouseListFilterByFactory() {
      const factoryCodeArr = this.searchForm.factoryCode
      let a = null;
      if (factoryCodeArr) {
        let tmpWarehouseListFilterByFactory = []
        tmpWarehouseListFilterByFactory = this.warehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCodeArr)
        a = tmpWarehouseListFilterByFactory
      }
      return a
    }

  },
  methods: {
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    },
    changeFac() {
      this.searchForm.warehouseLocation = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.action {
  overflow: hidden;
  .btn {
    float:right
  }
}
.flex-row-wrapper{
    display: flex;
    flex-wrap: wrap;
  }

</style>
