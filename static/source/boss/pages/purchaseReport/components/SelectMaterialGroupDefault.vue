<template>
  <el-select
  :value="value"
    filterable
    default-first-option
    remote
    clearable
    :multiple="multiple"
    :collapseTags="collapseTags"
    @change="handleSelectChange"
    :placeholder="placeholder"
    :remote-method="remoteProductGroupIdMethod"
    :disabled="disabled"
    :loading="productGroupIdLoading"
  >
    <el-option
      v-for="item in productGroupIdOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value"></el-option>
  </el-select>
</template>
<script>

import {
  getMaterialGroupList
} from '@/api/mm'
import { mapState } from 'vuex'
export default {
  name: 'remoteMaterialGroupComponent',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    value: [ String, Number, Array ],
    getLabel: Boolean,
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    defaultSearchValue: {
      type: [String, Number],
      default: '' // OEM紧固件
    }
  },
  computed: {
    ...mapState({
      searchMaterialGroupList: (state) => state.orderPurchase.searchMaterialGroupList
    }),
    defaultOption () {
      let option = ''
      const list = this.searchMaterialGroupList
      if (Array.isArray(list) && list.length) {
        const options = list.map(item => ({ value: item.materialGroupNum, label: item.materialGroupName }))
        // eslint-disable-next-line eqeqeq
        const defaultOption = options.find(opt => opt.value == this.defaultSearchValue)
        if (defaultOption) {
          option = defaultOption
        }
      }
      return option
    }
  },
  data () {
    return {
      productGroupIdOptions: [],
      productGroupIdLoading: false
    }
  },
  watch: {
    productGroupIdOptions: {
      deep: true,
      handler(val) {
        if (val) {
          this.productGroupIdOptions = val
        }
      }
    }
  },
  mounted() {
    if (this.defaultSearchValue && !this.defaultOption) {
      this.remoteProductGroupIdMethod(String(this.defaultSearchValue))
      return
    }
    if (this.defaultOption) {
      this.productGroupIdOptions = [this.defaultOption]
    }
  },
  methods: {
    concatOptions() {
      let ret = this.productGroupIdOptions
      // eslint-disable-next-line eqeqeq
      if (this.defaultOption && this.defaultOption.value && !ret.find(item => item.value == this.defaultOption.value)) {
        this.productGroupIdOptions = ret.concat(this.defaultOption)
      }
    },
    handleSelectChange (val) {
      this.$emit('change', val)
    },
    // 远程查找物料组
    async remoteProductGroupIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.productGroupIdLoading = true
        return getMaterialGroupList({
          groupName: key
        }).then(res => {
          if (res.code === 0) {
            if (res.data && res.data.length > 0) {
              this.productGroupIdOptions = res.data.map(item => {
                return {
                  value: item.materialGroupNum,
                  label: item.materialGroupName
                }
              })
            } else {
              this.productGroupIdOptions = []
            }
          } else {
            this.productGroupIdOptions = []
          }
        }).finally(() => {
          this.productGroupIdLoading = false
        })
      } else {
        this.productGroupIdOptions = []
        return Promise.resolve()
      }
    }
  }
}
</script>
