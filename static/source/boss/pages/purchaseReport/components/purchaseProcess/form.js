const forms = [
  { name: '采购单号', prop: 'poNoList', type: 'input', placeholder: '采购单号 或 SAP订单号 均支持搜索，同时支持200个单号，按空格或换行分隔', required: false, span: 12 },
  { name: 'SKU编码', prop: 'skuNoList', type: 'input', placeholder: '多个SKU编码按空格或换行分隔', required: false, span: 6 },
  { name: 'PO交货日期', prop: 'deliveryDate', type: 'date-picker', required: false, span: 6 },

  { name: '工厂名称', prop: 'factoryCode', type: 'select', enums: 'factoryCode', clearable: true, required: false, span: 12 },
  { name: '采购姓名', prop: 'purchaseGroupList', type: 'select', enums: 'purchaseGroupList', multiple: true, required: false, span: 6 },
  { name: '是否已清', prop: 'isDeliveryDone', type: 'select', enums: 'isDeliveryDone', hideCode: true, clearable: true, required: false, span: 6 },

  { name: '仓库地点', prop: 'warehouseLocationList', type: 'select', enums: 'warehouseLocationList', multiple: true, required: false, span: 6 },
  { name: '是否直发', prop: 'isDirect', type: 'select', enums: 'isDirect', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '是否加急', prop: 'isUrgent', type: 'select', enums: 'isUrgent', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '交期确认', prop: 'isConfirmed', type: 'select', enums: 'isConfirmed', hideCode: true, clearable: true, required: false, span: 6 }
]

/**
 * 生成form校验rules
 * @param {*} forms
 * @returns form rules
 */
const fillRules = (forms) => {
  let rules = {}
  forms.forEach(rule => {
    if (rule.required) {
      rules[rule.prop] = { required: true, message: `请选择${rule.name}！`, trigger: ['change', 'blur'] }
    }
  })
  return rules
}
const defaultSpan = 6
const arraySumEqual = (array, val) => {
  let acc = array.reduce((prev, next) => {
    if (next) { prev += parseInt(next.span || defaultSpan) }
    return prev
  }, 0)
  return acc >= val
}

const fillArray = (array, data) => {
  const len = array.length ? array.length - 1 : 0
  if (!Array.isArray(array[len])) array[len] = []
  if (!arraySumEqual(array[len], 24)) {
    array[len].push(data)
  } else {
    array[len + 1] = [data]
  }
}

const sortRows = (forms) => {
  let array = []
  for (let form of forms) {
    fillArray(array, form)
  }
  window._console.red(array)
  return array
}

export {
  forms,
  fillRules,
  sortRows
}
