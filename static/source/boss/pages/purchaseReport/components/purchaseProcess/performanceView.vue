<template>
  <el-dialog
    title="履约节点"
    width="940px"
    destroy-on-close
    :visible="performanceViewVisible"
    :before-close="beforeClose"
  >
    <div class="node">
      <el-steps
        v-for="(lines, index) in divideNodeList"
        :key="index"
        :active="finActive(lines, index)"
        :space="110"
        align-center
        finish-status="success">
        <el-step style="min-height: 120px;" v-for="(node, i) in lines" :key="index + i" :index="index*singleLineNodeNo+i" :title="node.nodeName" :status="nodeIcon(node)">
          <div slot="icon">
            <span>{{index*singleLineNodeNo+i+1}}</span>
          </div>
          <div slot="description">
            <span>{{node.content}}</span>
          </div>
        </el-step>
      </el-steps>
    </div>
    <div class="table">
      <el-table
        fit
        border
        highlight-current-row
        :data="referWorkOrderList"
        >
        <el-table-column
          v-for="(item, index) in getColumns()"
          align="center"
          show-overflow-tooltip
          :key="index"
          :type="item.type"
          :min-width="item.minWidth"
          :label="item.label"
          :prop="item.prop" />
      </el-table>
    </div>
  </el-dialog>
</template>

<script>

export default {
  props: {
    performanceViewVisible: Boolean,
    referWorkOrderList: Array,
    performanceNodeList: Array,
    updateVisible: Function
  },
  data () {
    return {
      singleLineNodeNo: 8,
      activeLines: 0
    }
  },
  computed: {
    divideNodeList () {
      const lines = []
      console.log(this.performanceNodeList)
      this.performanceNodeList.forEach((node, index) => {
        if (!lines.length) {
          return lines.push([node])
        }
        if (lines[lines.length - 1].length < this.singleLineNodeNo) {
          lines[lines.length - 1].push(node)
        } else {
          lines[lines.length] = [node]
        }
      })
      return lines
    }
  },
  methods: {
    findLastIndex (array, prop, keyword) {
      let ret = -1
      for (let i = array.length - 1; i > -1; i--) {
        if (typeof array[i][prop] === 'string' && array[i][prop].indexOf(keyword) === -1 && array[i][prop]) {
          ret = i
          break;
        }
      }
      return ret
    },
    finActive (lines, index) {
      const findIndex = this.findLastIndex(lines, 'content', '无需')
      if (findIndex !== -1) {
        this.activeLines = Math.max(this.activeLines, index)
      }
      console.log(lines, index, this.activeLines, findIndex)
      if (index < this.activeLines) {
        return lines.length
      }
      return findIndex
    },
    nodeIcon (node) {
      const statusMap = {
        '0': 'wait',
        '1': 'success',
        '-1': 'error'
      }
      return statusMap[node.nodeStatus]
    },
    beforeClose (done) {
      done && done()
      this.activeLines = 0
      this.updateVisible('performanceViewVisible', false)
    },
    getColumns () {
      const columns = [
        { label: '序号', type: 'index' },
        { label: '工单编号', prop: 'wordOrderNo', minWidth: '140' },
        { label: '前置节点', prop: 'prePerformanceNode' },
        { label: '工单状态', prop: 'status' },
        { label: '问题描述', prop: 'question' },
        { label: '关联单据', prop: 'referOrderNo' },
        { label: '创建人', prop: 'createUser' },
        { label: '待处理人', prop: 'relatedUser', minWidth: '140' },
        { label: '创建时间', prop: 'createTime', minWidth: '140' }
      ]
      return columns
    }
  }
}
</script>

<style lang="scss" scoped>
.desc {
  margin: 10px 0;
  .strong {
    font-weight: bold;
  }
}
.batch-btn {
  width: 120px;
}
</style>
