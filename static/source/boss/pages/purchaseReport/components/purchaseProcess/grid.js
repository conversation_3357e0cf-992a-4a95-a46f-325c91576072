const columns = [
  { type: 'checkbox', fixed: 'left', width: 60 },
  { title: '采购类型', field: 'purchaseType', fixed: 'left', width: 100 },
  { title: '采购订单', field: 'poNo', slot: 'poNo', fixed: 'left', width: 140 },
  { title: '项目行', field: 'itemNo', fixed: 'left', width: 100 },
  { title: '履约节点', field: 'nodeName', slot: 'nodeName', width: 100 },
  { title: '工单', field: 'referWorkOrderList', slot: 'referWorkOrderList', width: 200 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '采购', field: 'purchaseUser', width: 100 },
  { title: 'SKU编码', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '仓库地点', field: 'warehouseLocation', slot: 'warehouseLocation', width: 180 },
  { title: '交货日期', field: 'itemDeliveryDate', width: 100 },
  { title: '交期已确认', field: 'isConfirmed', isBoolean: true, width: 100 },
  { title: '订单数量', field: 'itemQuantity', width: 100 },
  { title: '未清数量', field: 'unReceivedQuantity', width: 100 },
  { title: '是否加急', field: 'isUrgent', isBoolean: true, width: 100 },
  { title: '履约状况', field: 'performanceStatus', slot: 'performanceStatus', headerSlot: 'performanceStatusHeader', width: 120 },
  { title: 'PO-SO交期差', field: 'poSoDeliveryDateGap', headerSlot: 'poSoDeliveryDateGapHeader', width: 160 },
  { title: '销售订单', field: 'soNo', slot: 'soNo', width: 100 },
  { title: 'SO项目行', field: 'soItemNo', width: 100 },
  { title: 'SO仓库地点', field: 'soWarehouseLocation', slot: 'soWarehouseLocation', width: 180 },
  { title: 'SO需求数量', field: 'soRequireQuantity', headerSlot: 'soRequireQuantityHeader', width: 140 },
  { title: 'SO要求交期', field: 'soDeliveryDate', width: 100 },
  { title: '转储订单', field: 'stoNo', width: 100 },
  { title: 'STO项目行', field: 'stoItemNo', width: 100 },
  { title: 'STO交货日期', field: 'stoDeliveryDate', width: 100 },
  { title: '是否允许分批', field: 'autoBatching', width: 100 },
  { title: '客户交期敏感', field: 'isDeliverySensitive', isBoolean: true, width: 100 },
  { title: 'SO未到货比例', field: 'soUnReceivedRatio', slot: 'soUnReceivedRatio', headerSlot: 'soUnReceivedRatioHeader', width: 120 },
  { title: 'OA流程编号', field: 'oaNo', width: 100 },
  { title: '申请人', field: 'oaCreateUser', width: 100 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'saleUser', width: 100 }
]

export {
  columns
}
