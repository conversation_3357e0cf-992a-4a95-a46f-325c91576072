<template>
  <el-dialog
    title="批量更新跟单"
    :visible="batchUpdateVisible"
    width="600px"
    :before-close="beforeClose"
  >
    <el-row>
      <div class="desc">
        <span class="strong">可修改字段：</span>
        包括采购订单号、项目行、计划行、标签、计划交货日期、订单交期确认标志、急单标识、客户接受供应商交期、备注文本
      </div>
    </el-row>
    <el-row style="margin-bottom:10px;display:flex;justify-content:space-around;">
      <el-button class="batch-btn" @click="handleDownloadTempl" type="primary" plain>下载模板</el-button>
      <Upload @uploadSuccess="uploadSuccess" :updateVisible="updateVisible" />
    </el-row>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import Upload from './upload'

export default {
  props: {
    batchUpdateVisible: {
      required: true,
      type: Boolean,
      default: false
    },
    updateVisible: {
      type: Function,
      default: () => {}
    }
  },
  components: {
    Upload
  },
  data () {
    return {
      // templateUrl: '//static.zkh360.com/file/resource/trackingOrder/%E6%89%B9%E9%87%8F%E8%B7%9F%E5%8D%95%E6%A8%A1%E6%9D%BFV3.xlsx'
      templateUrl: '//static.zkh360.com/file/resource/my-order-20210917/%E6%89%B9%E9%87%8F%E8%B7%9F%E5%8D%95%E6%A8%A1%E6%9D%BFV5.xlsx'
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    })
  },
  methods: {
    uploadSuccess () {
      this.updateVisible('batchUpdateVisible', false)
      this.$emit('uploadSuccess')
    },
    beforeClose (done) {
      this.updateVisible('batchUpdateVisible', false)
      done()
    },
    handleDownloadTempl () {
      window.open(this.templateUrl)
    }
  }
}
</script>

<style lang="scss" scoped>
.desc {
  margin: 10px 0;
  .strong {
    font-weight: bold;
  }
}
.batch-btn {
  width: 120px;
}
</style>
