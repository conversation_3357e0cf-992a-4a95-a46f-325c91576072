<template>
  <div class="purchase-process-grid-table" ref="girdTable">
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      highlight-hover-row
      highlight-current-row
      show-header-overflow
      ref="purchaseProcessGrid"
      id="purchase-process-grid"
      size="small"
      align="center"
      :data="listData"
      :scroll-x="{gt: -1}"
      :scroll-y="{gt: -1}"
      :loading="tableLoading"
      :height="gridHeight"
      :span-method="mergeRowMethod"
      :custom-config="tableCustom"
      :columns="getColumns()"
      :toolbar-config="tableToolbar"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
      @cell-dblclick="cellClick"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, enabled: true}">
      <template v-slot:toolbar_buttons>
        <div class="title-group">
          <slot name="button-group" :selections="selections"></slot>
        </div>
      </template>
      <template #sapOrderNo="{ row }">
        <span v-if="row.sapOrderNo" class="link-to-detail" @click="viewDetail(row.poNo, 'poNo')">{{row.sapOrderNo}}</span>
        <el-button v-if="row.sapOrderNo" style="margin-left:10px" type="text" size="mini" @click="$emit('goFollow', row)">跟单</el-button>
      </template>
      <template #poNo="{ row }">
        <span v-if="row.poNo" class="link-to-detail" @click="viewDetail(row.poNo, 'poNo')">{{row.poNo}}</span>
        <el-button v-if="row.poNo" style="margin-left:10px" type="text" size="mini" @click="$emit('goFollow', row)">跟单</el-button>
      </template>
      <template #nodeName="{ row }">
        <span class="link-to-detail" @click="$emit('performanceView', row)">{{row.nodeName}}</span>
      </template>
      <template #referWorkOrderList="{ row }">
        <div v-for="(item, index) in row.referWorkOrderList" :key="index">
          <span class="link-to-detail" @click="viewDetail(item.wordOrderNo, 'workflow')">{{item.wordOrderNo}}{{item.prePerformanceNode ? `(${item.prePerformanceNode})`: ''}}</span>
        </div>
      </template>
      <template #soNo="{ row }">
        <span class="link-to-detail" @click="viewSODetail(row)">{{row.soNo}}</span>
      </template>
      <template #sapSoNo="{ row }">
        <span class="link-to-detail" @click="viewSODetail(row)">{{row.sapSoNo}}</span>
      </template>
      <template #soUnReceivedRatio="{ row }">
        <span>{{(row.soUnReceivedRatio || (row.soUnReceivedRatio == 0)) ? parseFloat(row.soUnReceivedRatio * 100).toFixed(2) + '%': ''}}</span>
      </template>
      <template #soUnReceivedRatioHeader="{ column }">
        <span>
          {{column.title}}
          <el-tooltip class="item" effect="dark" content="(SO未交货数量-占在库数量) / SO订单数量" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
      </template>
      <template #warehouseLocation="{ row }">
        <span>{{mapValue('warehouseLocation', row.warehouseLocation, true)}}</span>
      </template>
      <template #soWarehouseLocation="{ row }">
        <span>{{mapValue('warehouseLocation', row.soWarehouseLocation, true)}}</span>
      </template>
      <template #performanceStatusHeader="{ column }">
        <span>
          {{column.title}}
          <el-tooltip class="item" effect="dark" content="PO-SO交期差大于0则为交期异常（标红），否则为交期满足SO" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
      </template>
      <template #performanceStatus="{ row }">
        <span :class="{'red-text': row.performanceStatus === '交期异常'}">
          {{row.performanceStatus}}
        </span>
      </template>
      <template #poSoDeliveryDateGapHeader="{ column }">
        <span>
          {{column.title}}
          <el-tooltip class="item" effect="dark" content="不涉及转储：交期差=项目行交货日期-SO要求交期；涉及转储：交期差=项目行交货日期+仓仓调拨时效+1-SO要求交期" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
      </template>
      <template #soRequireQuantityHeader="{ column }">
        <span>
          {{column.title}}
          <el-tooltip class="item" effect="dark" content="若为直发单，则指SO单据数量（库存单位）；若不为直发单，则特指SO占该PO的数量（库存单位）。举例：SO订单数量为10个，占在库6个，另外4个通过该PO单+行满足(ATP结果)，则此处显示4。" placement="top">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </span>
      </template>

      <template #isConfirmed="{ row }">
        <span :class="{'green-text': !!row.isConfirmed, 'red-text': !row.isConfirmed}">
          {{mapBoolean(row.isConfirmed)}}
        </span>
      </template>
      <template #isUrgent="{ row }">
        <span :class="{'green-text': !row.isUrgent, 'red-text': !!row.isUrgent}">
          {{mapBoolean(row.isUrgent)}}
        </span></template>
      <template #isDeliverySensitive="{ row }">
        <span :class="{'green-text': !row.isDeliverySensitive, 'red-text': !!row.isDeliverySensitive}">
          {{mapBoolean(row.isDeliverySensitive)}}
        </span></template>
    </vxe-grid>
    <div class="pagination" >
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tableInfo.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="tableInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableInfo.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { columns } from './grid.js'
import { buildOptions } from '@/utils/mm'
import { throttle, safeRun, deepClone, routeToWorkflow } from '@/utils/index'
import moment from 'moment'
import clip from '@/utils/clipboard'

export default {
  name: 'purchaseProcessGrid',
  props: {
    listData: {
      type: Array,
      default: () => []
    },
    spanArray: {
      type: Array,
      default: () => []
    },
    tableInfo: {
      type: Object,
      default: () => ({})
    },
    tableLoading: Boolean,
    updateLoading: Function,
    updateVisible: Function
  },
  data () {
    const newCreateTime = moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD')
    console.log(new Date(newCreateTime))
    return {
      gridHeight: 400,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      dateOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(newCreateTime)).getTime()
        }
      },
      columns: deepClone(columns),
      mergeColumn: Array.from({ length: 17 }, (_, i) => i),
      selections: []
    }
  },
  methods: {
    buildOptions,
    cellClick (config) {
      try {
        const { cell } = config
        let content = cell.innerText
        clip(content, event, () => {
          this.$message({
            message: '复制成功',
            type: 'success'
          })
        })
      } catch (err) {
        console.log(err)
      }
    },
    clearSelections () {
      this.selections = []
    },
    selectAll ({ records }) {
      this.selections = records
      this.$emit('setSelections', this.selections)
    },
    selectChange ({ records }) {
      this.selections = records
      this.$emit('setSelections', this.selections)
    },
    // 表格单元格合并
    mergeRowMethod ({ row, column, rowIndex, columnIndex }) {
      if (~this.mergeColumn.indexOf(columnIndex)) {
        const _row = this.spanArray[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        // console.log('第' + rowIndex + '行', '第' + 0 + '列', 'rowspan:' + _row, 'colspan:' + _col)
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    setColumns (type) {
      const switchMap = {
        oms: [
          { title: '采购订单', field: 'poNo', slot: 'poNo' },
          { title: '项目行', field: 'itemNo' },
          { title: '销售订单', field: 'soNo', slot: 'soNo' },
          { title: 'SO项目行', field: 'soItemNo' },
          { title: '转储订单', field: 'stoNo' },
          { title: 'STO项目行', field: 'stoItemNo' }
        ],
        sap: [
          { title: '采购订单', field: 'sapOrderNo', slot: 'sapOrderNo' },
          { title: '项目行', field: 'sapItemNo' },
          { title: '销售订单', field: 'sapSoNo', slot: 'sapSoNo' },
          { title: 'SO项目行', field: 'sapSoItemNo' },
          { title: '转储订单', field: 'sapStoNo' },
          { title: 'STO项目行', field: 'sapStoItemNo' }
        ]
      }
      const columns = this.columns
      columns.forEach(column => {
        switchMap[type].forEach(item => {
          if (column.title === item.title) {
            column.field = item.field
            if (column.slots && column.slots.default && item.slot) {
              column.slots.default = item.slot
            }
          }
        })
      })
      this.columns = columns
      this.$nextTick(() => {
        try {
          console.log('reloadColumn')
          console.log(this.columns)
          this.$refs.purchaseProcessGrid.reloadColumn(this.columns)
        } catch (err) {
          console.log(err)
        }
      })
    },
    getColumns () {
      const htmlList = [ 'performanceNodeList', 'referWorkOrderList' ]
      this.columns.forEach(column => {
        if (column.slot) {
          column.slots = { default: column.slot }
          if (htmlList.find(item => item === column.slot)) {
            column.type = 'html'
            column.showOverflow = false
          }
          delete column.slot
        }
        if (column.headerSlot) {
          if (column.slots) {
            column.slots.header = column.headerSlot
          } else {
            column.slots = { header: column.headerSlot }
          }
          delete column.headerSlot
        }
        if (column.isBoolean) {
          column.slots = { default: column.field }
        }
      })
      return this.columns
    },
    viewSODetail (row) {
      const { soNo, sapOrderNo, orderNo } = row
      this.$router.jumpToSoOrderDetail({
        tagName: `${sapOrderNo || ''}订单`,
        query: {
          soNo: soNo,
          orderNo,
          sapOrderNo: sapOrderNo,
          refresh: true
        }
      })
    },
    viewDetail (orderNo, type) {
      switch (type) {
        case 'workflow':
          routeToWorkflow(`/wf/detail/${orderNo}`)
          break;
        case 'poNo':
          this.$router.push({
            path: `/orderPurchase/detail/${orderNo}`,
            query: {
              tagName: `${orderNo}详情`
            }
          });
          break;
      }
    },
    calcLeftHeight: throttle(function calcLeftHeight () {
      safeRun(() => {
        const form = document.querySelector('.purchase-process-grid-form')
        const table = document.querySelector('.purchase-process-grid-table')
        const innerHeight = window.innerHeight - 140
        // 跟单报表页面container总高度
        const existHeight = form.offsetHeight + table.offsetHeight
        // 已占据高度
        // console.log(`existHeight: ${existHeight}, innerHeight: ${innerHeight}`)
        const paddingHeight = innerHeight - existHeight
        const floorHeight = Math.floor(paddingHeight * 400 / 452)
        // console.log(`floorHeight: ${floorHeight}, gridHeight: ${this.gridHeight}`)
        if (floorHeight > 5 || floorHeight < -5) {
          this.gridHeight += floorHeight
          if (this.gridHeight <= 400) {
            this.gridHeight = 400
          }
        }
      })
    }, 600),
    toDetail (row) {
      this.$router.push({
        path: `/orderPurchase/detail/${row.poNo}`,
        query: { tagName: `${row.poNo}详情` }
      })
    },
    emptyValue (value) {
      return value ? value + '' : ''
    },
    mapValue (prop, value, withCode) {
      const options = buildOptions(prop)
      if (Array.isArray(options)) {
        // eslint-disable-next-line eqeqeq
        const item = buildOptions(prop).find(item => item.value == value)
        if (item) {
          return withCode ? item.value + ' ' + this.emptyValue(item.name) : item.name
        }
      }
    },
    mapBoolean (value, falsyValue) {
      if (value === null || value === undefined || value === '') return ''
      return value ? '是' : falsyValue !== undefined ? falsyValue : '否'
    },
    formatDate (data) {
      const tmpData = deepClone(data)
      tmpData && tmpData.forEach && tmpData.forEach(item => {
        if (item.planDeliveryDate) {
          item.planDeliveryDate = item.planDeliveryDate.replace(/\//ig, '-')
        }
      })
      return tmpData
    },
    handleSizeChange (val) {
      this.$emit('handleTableInfo', { pageSize: val })
    },
    handleCurrentChange (val) {
      this.$emit('handleTableInfo', { pageNo: val })
    }
  },
  mounted() {
    setTimeout(this.calcLeftHeight, 100)
    window.addEventListener('resize', this.calcLeftHeight, false)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.calcLeftHeight, false)
  }
}
</script>

<style lang="scss" scoped>
.purchase-process-grid-table {
  overflow: hidden;
  .title-group{
    display: flex;
    justify-content: flex-end;
    span{
      line-height: 32px;
      height: 33px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .button-group {
      min-width: 300px;
      display: inline-block;
      text-align: right;
    }
  }
  span.link-to-detail{
    color: #597bee;
    cursor: pointer;
  }
  .pagination{
    margin-top: 20px;
    float:right;
  }
  .green-text{
    color: green;
  }
  .red-text{
    color: red;
  }
}
</style>
