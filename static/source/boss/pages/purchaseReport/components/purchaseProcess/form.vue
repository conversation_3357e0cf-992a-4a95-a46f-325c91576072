<template>
  <el-form class="purchase-process-grid-form" :model="searchForm" ref="searchForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="100px">
    <el-row v-for="(row, rowIndex) in getRows" :gutter="10" :key="rowIndex">
      <template v-if="showFilter ? true: rowIndex < 1">
        <el-col v-for="(column, index) in row" :key="index" :span="column.span">
          <el-form-item :label="column.name" :prop="column.prop">
            <el-select
              v-if="column.type === 'select'"
              v-model="searchForm[column.prop]"
              style="width: 100%"
              collapse-tags
              :multiple="column.multiple"
              filterable
              :clearable="column.clearable"
              default-first-option
            >
              <el-option
                v-for="item in buildOptions(column.enums)"
                :key="item.value"
                :label="column.hideCode ? item.name : item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
            <SelectSupplier
              v-if="column.type === 'custom' && column.prop === 'supplierNoList'"
              :multiple="column.multiple"
              @change="handleSupplierChange"
              :data.sync="searchForm[column.prop]"
              collapse-tags
            />
            <SelectBrand
              v-if="column.type === 'custom' && column.prop === 'brandNameList'"
              :data.sync="searchForm[column.prop]"
              :multiple="column.multiple"
              collapse-tags
            />
            <SelectMaterialGroup
              v-if="column.type === 'custom' && column.prop === 'materialGroupNumList'"
              :data.sync="searchForm[column.prop]"
              :multiple="column.multiple"
              collapse-tags
            />
            <el-date-picker
              v-if="column.type === 'date-picker'"
              v-model="searchForm[column.prop]"
              style="width:100%"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始"
              end-placeholder="结束"
            >
            </el-date-picker>
            <el-input
              v-if="column.type === 'input'"
              v-model="searchForm[column.prop]"
              clearable
              @change="(value)=>handleInputChange(column.prop, value)"
              :placeholder="column.placeholder || column.name"
            />
          </el-form-item>
        </el-col>
      </template>
      <slot name="button-group" :info="{row, rowIndex, getRows}"></slot>
    </el-row>
  </el-form>
</template>
<script>
import { mapState } from 'vuex'
import SelectBrand from '@/pages/orderPurchase/components/common/SelectBrand'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import SelectMaterialGroup from '@/pages/orderPurchase/components/common/SelectMaterialGroup'
import { fillRules, forms, sortRows } from './form'

export default {
  name: 'purchaseProcessForm',
  props: ['searchForm', 'showFilter', 'buildOptions'],
  data () {
    const rules = fillRules(forms)
    return {
      rules,
      searchLoading: false
    }
  },
  components: { SelectSupplier, SelectBrand, SelectMaterialGroup },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      purchaseGroupList: state => state.orderPurchase.purchaseList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList
    }),
    getRows () {
      return sortRows(forms)
    }
  },
  methods: {
    handleInputChange (prop, value) {
      console.log(prop, value, this.rules)
    },
    handleSupplierChange (val) {
      console.log(val)
    }
  }
}
</script>
<style scoped lang="scss">

</style>
