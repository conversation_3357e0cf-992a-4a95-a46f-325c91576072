<template>
  <div class="inventory-form">
    <el-form
      :model="searchForm"
      ref="searchForm"
      style="width: 100%"
      label-suffix=":"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="10"  class="flex-row-wrapper">
          <el-col :span="8">
          <el-form-item label="工厂名称" prop="factoryCode">
            <el-select
              disabled
              v-model="searchForm.factoryCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="(item,index) in factoryList"
                :key="index"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SKU编码" prop="skuNoList">
            <el-input
              v-model="searchForm.skuNoList"
              clearable
              placeholder="最多500个SKU按空格或换行符隔开搜索"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
           <el-form-item label="创建日期"
                        prop="createTime">
            <el-date-picker style="width:100%" v-model="searchForm.createTime"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            clearable>
            </el-date-picker>
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
           <el-form-item label="执行标准"
                        prop="qualityExecutionStandard">
             <el-input v-model="searchForm.qualityExecutionStandard" placeholder="请输入执行标准"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
           <el-form-item label="核心规格"
                        prop="coreSpecification">
             <el-input v-model="searchForm.coreSpecification" placeholder="请输入核心规格"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
           <el-form-item label="是否停用"
                        prop="skuStatus">
             <el-select v-model="searchForm.skuStatus" clearable>
               <el-option  v-for="(item,index) in skuStatus" :key="index" :label="item.label" :value="item.value"></el-option>
             </el-select>
          </el-form-item>
        </el-col>
         <el-col  :span="8">
          <el-form-item label="可分配数量" prop="distributeNonZero">
            <el-select v-model="searchForm.distributeNonZero" clearable>
              <el-option  v-for="(item,index) in distributionPlusNumbr" :key="index" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
              <div style="display:inline-block;">
                <slot></slot>
              </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { deepClone } from '@/utils/index.js'
import { skuStatus, distributionPlusNumbr } from '@/pages/purchaseReport/help'

export default {
  data() {
    return {
      searchForm: {
        factoryCode: '1000',
        skuNoList: '',
        qualityExecutionStandard: '',
        coreSpecification: '',
        distributeNonZero: 1
      },
      rules: {
        factoryCode: [
          {
            required: true,
            message: '请输入',
            trigger: 'change'
          }
        ]
      },
      skuStatus,
      distributionPlusNumbr
    }
  },
  computed: {
    ...mapState({
      factoryList: (state) => state.orderPurchase.factoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList
    }),
    copySearchForm() {
      const cloneSearchForm = deepClone(this.searchForm)
      this.clean(cloneSearchForm)
      let processdSearchForm = {}
      for (var k in cloneSearchForm) {
        if (k === 'skuNoList') {
          processdSearchForm[k] = cloneSearchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else {
          processdSearchForm[k] = cloneSearchForm[k]
        }
      }
      return processdSearchForm
    },
    storeWarehouseListFilterByFactory() {
      const factoryCodeArr = this.searchForm.factory
      let a = null;
      if (factoryCodeArr) {
        let tmpWarehouseListFilterByFactory = []
        tmpWarehouseListFilterByFactory = this.warehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCodeArr)
        a = tmpWarehouseListFilterByFactory
      }
      return a
    }

  },
  methods: {
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.inline-block {
  position: absolute;
  width: 100px;
}
.flex-row-wrapper{
    display: flex;
    flex-wrap: wrap;
  }

</style>
