<template>
  <div class="app-container stockpile-strategy-checklist">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="物料组" prop="materialGroupIds">
              <MaterialGroup v-model="searchForm.materialGroupIds" multiple style="width: 100%" placeholder="输入关键词，支持多个" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类目" prop="category">
              <AllCatalog v-model="searchForm.category" style="width: 100%" :multiple="false" :checkStrictly="true" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brandNo">
              <Brand v-model="searchForm.brandNo" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKU" prop="skuSetStr">
              <el-input
                v-model="searchForm.skuSetStr"
                type="text"
                placeholder="支持多个SKU，用逗号或空格分隔"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="仓库" prop="positions">
              <el-select
                v-model="searchForm.positions"
                placeholder="请选择"
                filterable
                multiple
                clearable
              >
                <el-option
                  v-for="item in positionOptions"
                  :key="item.position"
                  :label="item.position + ' ' + item.positionName"
                  :value="item.position"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品来源" prop="sourceType">
              <el-select
                v-model="searchForm.sourceType"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in sourceOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="仅展示可售商品" prop="online">
              <el-select v-model="searchForm.online">
                <el-option
                  v-for="item in onlineOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="批次号" prop="batchNostr">
              <el-input
                v-model="searchForm.batchNostr"
                type="text"
                placeholder="支持多个，用逗号或空格分隔"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <div class="flex flex-end mb-10 mt-18">
        <el-button
          type="primary"
          class="mr-10"
          @click="handleExportAll"
          :loading="exportLoading"
        >
          导出
        </el-button>
        <el-upload
          v-if="isStockAdmin"
          name="serviceFile"
          class="upload mr-10"
          action="/api-sim/off-price/import"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
          :multiple="false"
          :limit="1"
          ref="upload"
          :show-file-list="true"
          :before-upload="handleBeforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
        >
          <el-button :loading="uploadLoading" size="small" type="default">导入特价商品</el-button>
        </el-upload>
        <el-button
          v-if="isStockAdmin"
          type="primary"
          class="mr-10"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>
      <zkh-table
        :loading="basisListLoading"
        ref="inventoryTable"
        :data="basisList"
        :selectable="true"
        height="420"
        :columns="inventorySpecialsListColunms"
        :row-key="row => row.id"
      >
        <template #productImage="{ row }">
          <el-popover placement="top" :popper-style="{ width: '385px', height: '294px' }" trigger="hover">
            <template #reference>
              <el-image style="width: 80px; height: 60px; flex-shrink: 0" :src="row.headImgV2Url" fit="cover">
                <template #error>
                  <div class="image-slot">无主图</div>
                </template>
              </el-image>
            </template>
            <el-image style="width: 360px; height: 270px; border: 1px solid #f5f5f5;" :src="row.headImgV2Url">
              <template #error>
                <div class="image-slot image-slot-big">无主图</div>
              </template>
            </el-image>
          </el-popover>
        </template>
        <template #catalogName="{ row }">
          {{ row.firstCatalogName }}/{{ row.secondCatalogName }}/{{ row.thirdCatalogName }}/{{ row.fourthCatalogName }}
        </template>
        <template #factoryStatusAndSkuTags="{ row }">
          <div v-html="row.factoryStatusAndSkuTags"></div>
        </template>
        <template #offPrice="{ row }">
          <span class="red">{{ row.offPrice }}</span>
        </template>
        <template #availableQty="{ row }">
          <span class="red">{{ row.availableQty }}</span>
        </template>
        <template #action="scope">
          <a v-if="isStockAdmin" class="primary-blue mr-4px cursor-pointer" @click="handleDelete([scope.row])">删除</a>
        </template>
      </zkh-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Pagination from '@/components/Pagination';
import {
  deleteList,
  queryList,
  exportList,
  getAllPosition,
  getSourceType
} from '@/api/inventoryListOfSpecials';
import { inventorySpecialsListColunms, onlineOptions } from './constant';
import AllCatalog from '@/components/SearchFields/allDirectoryCascader';
import MaterialGroup from '@/components/SearchFields/materialGroup';
import Brand from '@/components/SearchFields/brand';

export default {
  name: 'inventorylistofspecials',
  data() {
    return {
      basisListLoading: false,
      positionOptions: [],
      sourceOptions: [],
      onlineOptions,
      exportLoading: false,
      uploadLoading: false,
      basisList: [],
      searchForm: {
        brandId: '',
        category: '',
        materialGroupIds: [],
        skuSetStr: '',
        positions: [],
        sourceType: '',
        online: 1
      },
      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      total: 0,
      inventorySpecialsListColunms
    };
  },
  components: {
    Pagination,
    AllCatalog,
    MaterialGroup,
    Brand
  },
  async created() {
    this.handleFilter();
    getAllPosition().then((data) => {
      this.positionOptions = data.result;
    })
    getSourceType().then((data) => {
      this.sourceOptions = data.data.commoditysource;
    })
  },
  computed: {
    ...mapState(['userRole']),
    isStockAdmin() {
      return !!~this.userRole.indexOf('data-特价商品库存管理员');
    }
  },
  methods: {
    prepareSearchParam() {
      const firstCatalogId = this.searchForm.category[0];
      const secondCatalogId = this.searchForm.category[1];
      const thirdCatalogId = this.searchForm.category[2];
      const fourthCatalogId = this.searchForm.category[3];
      const skuNos = !this.searchForm.skuSetStr ? undefined : this.searchForm.skuSetStr.split(/[\n\r\s;；, ]/g).filter(Boolean);
      const batchNos = !this.searchForm.batchNostr ? undefined : this.searchForm.batchNostr.split(/[\n\r\s;；, ]/g).filter(Boolean);
      return {
        ...this.searchForm,
        current: this.listQueryInfo.current,
        size: this.listQueryInfo.pageSize,
        skuNos,
        batchNos,
        firstCatalogId,
        secondCatalogId,
        thirdCatalogId,
        fourthCatalogId
      };
    },
    getSelctedRows() {
      const selectableRows = this.$refs.inventoryTable.getSelections()
      return selectableRows
    },
    // 批量删除按钮
    handleBatchDelete() {
      const selectableRows = this.getSelctedRows()
      if (selectableRows.length === 0) {
        this.$message.warning('请选择要删除的记录!')
        return
      }
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.handleDeleteReq(selectableRows)
      })
    },
    // 删除按钮
    handleDelete(rows) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.handleDeleteReq(rows)
      })
    },
    // 删除接口
    async handleDeleteReq(rows) {
      const res = await deleteList(rows.map(item => item.id))
      if (!res.success) {
        this.$message.error(res.message || res.msg)
        return
      }
      this.$message({
        type: 'success',
        message: '删除成功!'
      });
      this.handleFilter();
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.handleFilter();
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file)) return false
      this.uploadLoading = true;
    },
    handleUploadSuccess(response) {
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
      if (response.success) {
        this.$message.success(response.message || response.msg || '上传成功！');
        this.handleFilter();
      } else {
        this.$message.error({
          message:
            (response && (response.message || response.msg)) || '上传失败！',
          duration: 6000
        });
      }
    },
    handleUploadError(error) {
      this.uploadLoading = false;
      this.$message.error({
        message:
          (error && error.msg) || (error && error.message) || '上传失败!',
        duration: 6000
      });
    },
    async handleExportAll() {
      const param = this.prepareSearchParam();
      this.exportLoading = true;
      try {
        const res = await exportList({ ...param });
        if (res.success) {
          this.$message.success({
            message: res.data || '导出成功！',
            duration: 6000
          });
        } else {
          this.$message.error({
            message:
              (res && (res.data || res.msg)) || '导出失败！',
            duration: 6000
          })
        }
      } catch (error) {
        this.$message.error({
          message: '导出失败！',
          duration: 6000
        });
      } finally {
        this.exportLoading = false;
      }
    },
    getBasisList() {
      const param = this.prepareSearchParam();
      this.basisList = [];
      this.basisListLoading = true;
      queryList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 0) {
            if (res.data) {
              const data = res.data.records;
              this.total = res.data.total;
              this.basisList = data;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    }
  }
};
</script>

<style lang="scss">
.stockpile-strategy-checklist {
  .tabelCellError {
    input {
      border: 1px solid #ff4949;
    }
    &:hover {
      input {
        border: 1px solid #ff4949;
      }
    }
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }
  .flex-end {
    justify-content: flex-end;
  }
  .justify-between {
    justify-content: space-between;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .fw-500 {
    font-weight: 500;
  }
  .primary-blue {
    color: #7a95f1;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mt-18 {
    margin-top: 18px;
  }
  .input-number--micro {
    width: 100px;
  }
  .text-no-overflow {
    .cell {
      text-overflow: unset;
    }
  }
  .align-right {
    text-align: right;
  }
}
.upload {
  display: inline;
}
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.mr-4px {
  margin-right: 4px;
}
.cursor-pointer {
  cursor: pointer;
}
.red {
  color: #ff4949;
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 60px;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 14px;
}
.image-slot-big {
  width: 360px;
  height: 270px;
}
</style>
