// 方生催货区域动态表头字段
export const subTableHead = [
  { title: '催货', field: 'regionWarehouseExpedite2' },
  { title: '在途', field: 'regionWarehouseIntransit2' },
  { title: '库存', field: 'regionWarehouseStock2' },
  { title: '订单', field: 'regionWarehouseOrder2' },
  { title: '预报', field: 'regionWarehouseForecast2' },
  { title: '发货', field: 'regionWarehouseSent2' }
]
// 方生催货静态表头前半段字段集合
export const tableFrontFields = ['skuNo', 'qualityExecutionStandard', 'coreSpecification', 'surfaceTreatment', 'pmode', 'uomIdName', 'purchaseMoq', 'purchaseMpq', 'totalExpedite2', 'totalExpediteBox2', 'totalTake2', 'totalTakeBox2', 'outsourcingExpedite2', 'outsourcingIntransit2', 'outsourcingStock2', 'outsourcingDemand2', 'outsourcingBalance2', 'fsStockingPickup2', 'fsStockingExpedite2', 'fsStockingIntransit2', 'fsStockingStock2', 'fsStockingBalance2', 'fsStockingDemand2', 'fsStockingSafeStock2']
// 方生催货静态表后半段字段集合
export const tableEndFields = ['factoryCode', 'materialDescribe', 'gmtCreate', 'isSent']
// 方生催货静态字段
export const TableFields = ['skuNo', 'qualityExecutionStandard', 'coreSpecification', 'surfaceTreatment', 'pmode', 'uomIdName', 'purchaseMoq', 'purchaseMpq', 'totalExpedite2', 'totalExpediteBox2', 'totalTake2', 'totalTakeBox2', 'outsourcingExpedite2', 'outsourcingIntransit2', 'outsourcingStock2', 'outsourcingDemand2', 'outsourcingBalance2', 'fsStockingPickup2', 'fsStockingExpedite2', 'fsStockingIntransit2', 'fsStockingStock2', 'fsStockingBalance2', 'fsStockingDemand2', 'fsStockingSafeStock2', 'factoryCode', 'materialDescribe', 'gmtCreate', 'isSent', 'skuStatus']
// 方生催货列表配置项
export const columns = [
  {

    field: 'skuNo',
    title: 'SKU编码',
    width: 100
  },
  {

    field: 'qualityExecutionStandard',
    title: '执行标准',
    width: 100
  },
  {

    field: 'coreSpecification',
    title: '核心规格',
    width: 100
  },
  {

    field: 'surfaceTreatment',
    title: '表面处理',
    width: 100
  },
  {

    field: 'pmode',
    title: '运营路线',
    width: 100
  },
  {

    field: 'uomIdName',
    title: '基本单位',
    width: 100
  },
  {

    field: 'purchaseMoq',
    title: '采购MOQ',
    width: 100
  },
  {

    field: 'purchaseMpq',
    title: '采购MPQ',
    width: 100
  },
  {

    field: 'totalExpedite2',
    title: '催货总数',
    width: 100
  },
  {

    field: 'totalExpediteBox2',
    title: '催货总箱数',
    width: 100
  },
  {
    // totalTake2
    field: 'totalTake2',
    title: '提货总数',
    width: 100
  },
  {

    field: 'totalTakeBox2',
    title: '提货总箱数',
    width: 100
  },
  {

    field: 'outsourcingExpedite2',
    title: '委外-催货',
    width: 100
  },
  {

    field: 'outsourcingIntransit2',
    title: '委外-在途',
    width: 100
  },
  {

    field: 'outsourcingStock2',
    title: '委外-库存',
    width: 100
  },
  {

    field: 'outsourcingDemand2',
    title: '委外-需求',
    width: 100
  },
  {

    field: 'outsourcingBalance2',
    title: '委外-结余',
    width: 100
  },
  {

    field: 'fsStockingPickup2',
    title: '芳生备货仓-提货',
    width: 200
  },
  {

    field: 'fsStockingExpedite2',
    title: '芳生备货仓-催货',
    width: 200
  },
  {

    field: 'fsStockingIntransit2',
    title: '芳生备货仓-在途',
    width: 200
  },
  {

    field: 'fsStockingStock2',
    title: '芳生备货仓-库存',
    width: 200
  },
  {

    field: 'fsStockingBalance2',
    title: '芳生备货仓-结余',
    width: 200
  },
  {

    field: 'fsStockingDemand2',
    title: '芳生备货仓-需求',
    width: 200
  },
  {

    field: 'fsStockingSafeStock2',
    title: '芳生备货仓-安全库存',
    width: 200
  },
  // {

  //   field: 'skuNo',
  //   title: '深圳紧固件仓-催货',
  //   width: 100
  // },
  // {

  //   field: 'skuNo',
  //   title: '深圳紧固件仓-在途',
  //   width: 100
  // },
  // {

  //   field: 'skuNo',
  //   title: '深圳紧固件仓-库存',
  //   width: 100
  // },
  // {

  //   field: 'skuNo',
  //   title: '深圳紧固件仓-订单',
  //   width: 100
  // },
  // {

  //   field: 'skuNo',
  //   title: '深圳紧固件仓-预报',
  //   width: 100
  // },
  // {

  //   field: 'skuNo',
  //   title: '深圳紧固件仓-发货',
  //   width: 100
  // },
  {

    field: 'factoryCode',
    title: '工厂代码',
    width: 100
  },
  {

    field: 'materialDescribe',
    title: '物料描述',
    width: 200
  },
  {

    field: 'gmtCreate',
    title: '创建时间',
    width: 150
  },
  {
    field: 'isSent',
    title: '是否发送',
    width: 100,
    slots: {
      default: 'isSent_default'
    }
  },
  {

    field: 'skuStatus',
    title: '是否启用',
    width: 100,
    slots: {
      default: 'skuStatus_default'

    }
  }

]
// 方生分货列表配置项
export const distributionListColumns = [
  {
    type: 'checkbox',
    width: 50
  },
  {

    field: 'skuNo',
    title: 'SKU编码',
    width: 100
  },
  {

    field: 'coreSpecification',
    title: '核心规格',
    width: 200
  },
  {

    field: 'qualityExecutionStandard',
    title: '执行标准',
    width: 200
  },
  {

    field: 'surfaceTreatment',
    title: '表面处理',
    width: 100
  },
  {

    field: 'materialDescribe',
    title: '物料描述',
    width: 100
  },
  {

    field: 'uomIdName',
    title: '基本单位',
    width: 100
  },
  {

    field: 'distributeQuantity',
    title: '可分配数量',
    width: 100
  },
  {

    field: 'balanceQuantity',
    title: '结余数量',
    width: 100
  },
  {

    field: 'skuStatus',
    title: '是否启用',
    width: 100,
    slots: {
      default: 'skuStatus_default'

    }
  }

]
// 方生分货静态字段
// export const distributionTableFields = ['skuNo', 'coreSpecification', 'qualityExecutionStandard', 'surfaceTreatment', 'materialDescribe', 'uomIdName', 'distributeQuantity', 'balanceQuantity', 'id', 'skuStatus']
export const distributionTableFields = ['skuNo', 'coreSpecification', 'qualityExecutionStandard', 'surfaceTreatment', 'materialDescribe', 'uomIdName', 'distributeQuantity', 'balanceQuantity', 'id', 'skuStatus']
export const distributionsubTableHead = [
  { title: '仓名', field: 'distributeQuantityRegion' }
]
export const distributionTableFrontFields = ['skuNo', 'coreSpecification', 'qualityExecutionStandard', 'surfaceTreatment', 'materialDescribe', 'uomIdName', 'distributeQuantity', 'balanceQuantity']
export const distributionTableEndFields = []
export const versionList = [
  {
    label: '最新版本',
    value: 0
  },
  {
    label: '快照版本',
    value: 1
  }

]
export const skuStatus = [
  {
    label: '启用',
    value: 1
  },
  {
    label: '停用',
    value: 0
  }
]
export const expeditingPlusNumbr = [
  {
    label: '不显示催货总数为0的数据',
    value: 1
  },
  {
    label: '显示催货总数为0的数据',
    value: 0
  }
]
export const pickUpGoodsPlusNumbr = [
  {
    label: '不显示提货总数为0的数据',
    value: 1
  },
  {
    label: '显示提货总数为0的数据',
    value: 0
  }
]
export const distributionPlusNumbr = [
  {
    label: '不显示可分配数量为0的数据',
    value: 1
  },
  {
    label: '显示可分配数量为0的数据',
    value: 0
  }
]
