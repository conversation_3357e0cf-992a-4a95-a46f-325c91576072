<template>
  <div class="app-container" v-loading="pageLoading">
     <InventoryForm ref="inventoryForm">
       <template #default>
          <el-button type="primary" @click="search">查询</el-button>
            <el-button  @click="reset">重置</el-button>
       </template>
     </InventoryForm>
      <InventoryGrid :list="list" @selectRecords="selectRecords" :loading="loading" ref="grid" :mergeTableHead="mergeTableHead" :mergeTableList="mergeTableList" :dynamicTableHead="dynamicTableHead" :dynamicTableList="dynamicTableList">
         <template #default>
           <el-row type="flex" class="row-bg" justify="space-between">
             <!-- todo regex-->
                <el-form :model="form" ref="form" label-suffix=":" :inline="true" :rules="rules" >
                  <el-form-item label="优先分配比例%" prop="distributeRatio">
                    <el-input v-model="form.distributeRatio"/>
                  </el-form-item>
                   <el-form-item>
                    <el-button type="primary" @click="confirm">确定</el-button>
                   </el-form-item>
                </el-form>
               <div class="action">
                  <el-button @click="generateTpl">生成模版</el-button>
                  <el-button size="small" @click="exportDistributionReportList" style="margin-left:20px" :disabled="mergeTableList&&mergeTableList.length<=0" >导出明细</el-button>
                </div>

           </el-row>
          </template>
       </InventoryGrid>
      <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
       @pagination="getPaginationList"
      />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getDistributionReportList, generateDistributionReportListTpl, exportDistributionReportList, reCalDistributionReportList } from '@/api/fsReport'
import { distributionsubTableHead, distributionTableFields, distributionListColumns, distributionTableFrontFields } from './help'
import _, { cloneDeep } from 'lodash'

import InventoryForm from './components/distributionReport/form'
import InventoryGrid from './components/distributionReport/grid'
import Pagination from '@/components/Pagination'

export default {
  data () {
    return {
      pageLoading: false,
      list: [],
      listQueryInfo: {
        pageNo: 1,
        pageSize: 50
      },
      uploadLoading: null,
      loading: false,
      total: 0,
      // 动态表头
      dynamicTableHead: [],
      // 动态表头表格数据
      dynamicTableList: [],
      // 合并表头
      mergeTableHead: [],
      // 合并的表格数据
      mergeTableList: [],
      // 转储单
      stoList: [],
      // 预调拨单
      iaoList: [],
      selectRecordsList: [],
      form: {
        distributeRatio: 5

      },
      initData: '',
      rules: {
        distributeRatio: [
          {
            pattern: /^(0|[1-9]\d{0,2})(\.\d{1,2})?$/, message: '请输入有效优先分配比例', trigger: 'blur'
          }
        ]

      }

    }
  },
  components: { InventoryForm, InventoryGrid, Pagination },
  computed: {
    ...mapState({
      warehouseList: (state) => state.orderPurchase.warehouseList,
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList
    })
  },

  async  mounted() {
    this.pageLoading = true
    const pList = []
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    pList.push(this.initigetDistributionReportList())
    await Promise.all(pList).then((res) => {
      this.initData = res[res.length - 1]
      this.getAreaWareHoseDynamicTableRelatedData(this.initData)
    })
    this.pageLoading = false
  },
  methods: {
    initigetDistributionReportList() {
      this.loading = true
      const queryParams = {}
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      return getDistributionReportList(queryParams).then((res) => {
        this.total = res.f1.total
        this.loading = false
        return res
      }).catch((err) => {
        console.log(err);
      })
    },
    getDistributionReportList() {
      this.loading = true
      const queryParams = {}
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      return getDistributionReportList(queryParams).then((res) => {
        this.loading = false
        this.total = (res && res.f1.total) || 0
        this.getAreaWareHoseDynamicTableRelatedData(res)
        return res
      }).catch((err) => {
        console.log(err);
      })
    },
    getPaginationList() {
      this.loading = true
      const queryParams = {}
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getDistributionReportList(queryParams).then((res) => {
        this.loading = false
        this.total = (res && res.f1.total) || 0
        this.getAreaWareHoseDynamicTableRelatedData(res)
      }).catch((err) => {
        console.log(err);
      })
    },
    search() {
      this.$refs.inventoryForm.$refs['searchForm'].validate((valid) => {
        if (valid) {
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.skuNoList && this.$refs.inventoryForm.copySearchForm.skuNoList.length > 500) {
            this.$alert('成品SKU不应该超过500个')
            return
          }
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.poNoList && this.$refs.inventoryForm.copySearchForm.poNoList.length > 200) {
            this.$alert('采购订单不应该超过200个')
            return
          }
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.componentSkuNoList && this.$refs.inventoryForm.copySearchForm.componentSkuNoList.length > 500) {
            this.$alert('组件SKU不应该超过500个')
            return
          }
          this.getDistributionReportList()
        } else {
          return false;
        }
      });
    },
    reset() {
      for (var k in this.$refs.inventoryForm.searchForm) {
        if (this.$refs.inventoryForm.searchForm.hasOwnProperty(k)) {
          if (k === 'factoryCode') {
            this.$refs.inventoryForm.searchForm[k] = '1000'
          } else {
            this.$refs.inventoryForm.searchForm[k] = ''
          }
        }
      }
      this.getDistributionReportList()
    },
    pickPointedPropety(object, keyArr) {
      return _.pick(object, keyArr)
    },
    exportDistributionReportList() {
      exportDistributionReportList(this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm).then((res) => {
        if (res.code === 0) {
          this.$message.success('导出成功，请在下载专区查看导出内容！')
          try {
            this.$closeTag('/purchaseReport/downLoadList')
          } catch {

          }
          setTimeout(() => {
            this.$router.push({
              path: '/purchaseReport/downLoadList'
            })
          }, 600)
        }
      })
    },
    // 获取区域仓动态表格数据
    getAreaWareHoseDynamicTableRelatedData(data) {
      const dynamicTableHead = [];
      data && data.f0 && data.f0.length > 0 && data.f0.forEach((warehouseNameTitle, index) => {
        distributionsubTableHead.forEach((subWarehouseNameTitle) => {
          let objHead = { field: '', title: '', width: 200, warehouseLocation: '', helpKey: '' };
          objHead.field = `${subWarehouseNameTitle.field}_${index}`
          objHead.title = `${warehouseNameTitle.warehouseName}`
          objHead.warehouseLocation = `${warehouseNameTitle.warehouseLocation}`
          objHead.helpKey = `${subWarehouseNameTitle.field}`
          dynamicTableHead.push(objHead)
          this.dynamicTableHead = dynamicTableHead
        })
      })
      // 注意表头重复添加问题
      const tableColumns = cloneDeep(distributionListColumns)
      tableColumns.splice(distributionTableFrontFields.length + 1, 0, ...this.dynamicTableHead)
      this.mergeTableHead = tableColumns

      const mergeTableList = []
      data && data.f1 && data.f1.rows && data.f1.rows.length > 0 && data.f1.rows.forEach((record) => {
        let staticTableData = this.pickPointedPropety(record, distributionTableFields)
        record.fsReportDistributeRegionList.forEach((region) => {
          let tableField = {}
          this.dynamicTableHead.forEach(header => {
            if (region.warehouseLocation === header.warehouseLocation) {
              tableField[header.field] = region[header.helpKey]
            }
          })
          // 组合列表行数据
          Object.assign(staticTableData, tableField)
        })
        // 注意表格数据是否重复添加
        mergeTableList.push(staticTableData)
      })
      this.mergeTableList = mergeTableList
    },
    generateTpl() {
      const queryParams = {}
      let selectIds = []
      this.selectRecordsList.forEach((item) => {
        selectIds.push(item.id)
      })
      queryParams.distributeIdList = selectIds
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      generateDistributionReportListTpl(queryParams).then((res) => {
        if (res.code === 0) {
          this.$message.success('导出成功，请在下载专区查看导出内容！')
          try {
            this.$closeTag('/purchaseReport/downLoadList')
          } catch {

          }
          setTimeout(() => {
            this.$router.push({
              path: '/purchaseReport/downLoadList'
            })
          }, 600)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    selectRecords(records) {
      this.selectRecordsList = records
    },
    confirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          const queryParams = {}
          this.listQueryInfo.pageNo = 1
          this.listQueryInfo.pageSize = 50
          queryParams.pageNo = this.listQueryInfo.pageNo
          queryParams.pageSize = this.listQueryInfo.pageSize
          queryParams.distributeRatio = this.form.distributeRatio
          Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
          reCalDistributionReportList(queryParams).then((res) => {
            this.loading = false
            if (res && res.code === 0) {
              this.$message.success('优先分配比例成功')
              this.getDistributionReportList()
            } else {
              this.$message.error(res.msg)
            }
          }).catch((err) => {
            console.log(err);
          })
        } else {
          return false
        }
      })
    }

  }

}
</script>
