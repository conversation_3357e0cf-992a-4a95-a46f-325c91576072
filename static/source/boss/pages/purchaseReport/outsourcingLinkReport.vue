<template>
  <div class="app-container" v-loading="pageLoading">
    <InventoryForm ref="inventoryForm">
      <template #default>
        <el-button type="primary" @click="search">查询</el-button>
          <el-button  @click="reset">重置</el-button>
      </template>
    </InventoryForm>
      <InventoryGrid :list="list"  :loading="loading" ref="grid">
        <template #default>
          <div class="inventory-upload-wrapper">
            <div class="action">
            <el-button size="small" @click="exportList" style="margin-left:20px" :disabled="list&&list.length<=0" >导出明细</el-button>
            </div>
          </div>
        </template>
      </InventoryGrid>
      <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getPaginationOutSourcingReportList"
      />
  </div>
</template>

<script>
import { writeFile } from '@boss/excel'
import { mapState } from 'vuex'

import { getOutSourcingReportList, exportOutSourcingReportList } from '@/api/mm'
import _ from 'lodash'

import InventoryForm from './components/outsourcingLinkReport/form'
import InventoryGrid from './components/outsourcingLinkReport/grid'
import Pagination from '@/components/Pagination'

export default {
  data () {
    return {
      pageLoading: false,
      list: [],
      listQueryInfo: {
        pageNo: 1,
        pageSize: 50
      },
      uploadLoading: null,
      loading: false,
      total: 0,
      exportDataList: []

    }
  },
  components: { InventoryForm, InventoryGrid, Pagination },
  computed: {
    ...mapState({
      dictList: (state) => state.orderPurchase.dictList,
      purchaseGroupList: (state) => state.orderPurchase.purchaseList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      searchMaterialGroupList: (state) => state.orderPurchase.searchMaterialGroupList
    }),
    tableKeyValue() {
      let obj = {}
      this.$refs.grid.columns.forEach((item) => {
        obj[item.field] = item.title
      })
      return obj
    },
    tableHead() {
      let arr = []
      this.$refs.grid.columns.forEach((item) => {
        arr.push(item.title)
      })
      return arr
    }
  },
  async created() {
    this.pageLoading = true
    const pList = []
    if (JSON.stringify(this.dictList) === '{}') {
      pList.push(this.$store.dispatch('orderPurchase/queryDictList'))
    }
    if (!this.purchaseGroupList || this.purchaseGroupList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryPurchaseGroup'))
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    }
    if (!this.searchMaterialGroupList || this.searchMaterialGroupList.length === 0) {
      pList.push(this.$store.dispatch('orderPurchase/getSearchMaterialGroup'))
    }
    await Promise.all(pList)
    this.pageLoading = false
  },
  methods: {
    getOutSourcingReportList() {
      this.loading = true
      const queryParams = {}
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getOutSourcingReportList(queryParams).then((res) => {
        this.loading = false
        if (res.code === 0) {
          this.list = res.data.rows
          this.total = res.data.total
        } else if (res.status !== 200 || res.status !== 0) {
          this.$message.error(res.msg);
        }
      }).catch((err) => {
        console.log(err);
      })
    },
    getPaginationOutSourcingReportList() {
      this.loading = true
      const queryParams = {}
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getOutSourcingReportList(queryParams).then((res) => {
        this.loading = false
        if (res.code === 0) {
          this.list = res.data.rows
          this.total = res.data.total
        } else if (res.status !== 200 || res.status !== 0) {
          this.$message.error(res.msg);
        }
      }).catch((err) => {
        console.log(err);
      })
    },
    search() {
      this.$refs.inventoryForm.$refs['searchForm'].validate((valid) => {
        if (valid) {
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.skuNoList && this.$refs.inventoryForm.copySearchForm.skuNoList.length > 500) {
            this.$alert('成品SKU不应该超过500个')
            return
          }
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.poNoList && this.$refs.inventoryForm.copySearchForm.poNoList.length > 200) {
            this.$alert('采购订单不应该超过200个')
            return
          }
          if (this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm.componentSkuNoList && this.$refs.inventoryForm.copySearchForm.componentSkuNoList.length > 500) {
            this.$alert('组件SKU不应该超过500个')
            return
          }
          this.listQueryInfo.pageNo = 1
          this.getOutSourcingReportList()
        } else {
          return false;
        }
      });
    },
    reset() {
      for (var k in this.$refs.inventoryForm.searchForm) {
        if (this.$refs.inventoryForm.searchForm.hasOwnProperty(k)) {
          if (k === 'factory') {
            this.$refs.inventoryForm.searchForm[k] = '1000'
          } else if (k === 'leftStartDays' || k === 'leftEndDays' || k === 'startStockAge' || k === 'endStockAge') {
            this.$refs.inventoryForm.searchForm[k] = undefined
          } else if (k === 'materialGroupNum') {
            this.$refs.inventoryForm.searchForm[k] = 430
          } else {
            this.$refs.inventoryForm.searchForm[k] = ''
          }
        }
      }
      const invForm = this.$refs.inventoryForm
      if (invForm && invForm.$refs && invForm.$refs.materialGroup) {
        const group = invForm.$refs.materialGroup
        if (group.concatOptions) {
          group.concatOptions()
        }
      }
    },
    pickPointedPropety(object, keyArr) {
      return _.pick(object, keyArr)
    },
    flatten(maybeArray) {
      if (Array.isArray(maybeArray)) {
        return maybeArray.join('\n');
      }
      return maybeArray;
    },
    mapBool (booleanLike) {
      return booleanLike ? '是' : '否'
    },
    // 导出明细
    async exportList() {
      console.log(this.warehouseList)
      const queryParams = {}
      queryParams.pageNo = this.listQueryInfo.pageNo
      queryParams.pageSize = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      let res = await exportOutSourcingReportList(queryParams)
      this.exportDataList = res.data
      if (this.exportDataList.length > 10000) {
        this.$message.warning('单次导出明细不得超过1万条，请拆分导出！')
        return
      }
      const mapping = this.tableKeyValue
      const arrayList = [
        'upperNo', 'componentIntransitOrderList', 'componentIntransitInventoryList', 'outSourcingIAOList',
        'systemStatus', 'systemStatusText', 'asnNo', 'asnStatus', 'checkBatchList', 'checkQuantityList',
        'postInQuantityList', 'checkCreateDate', 'checkApproveDate', 'codeList', 'shortTextList', 'checkResultList'
      ]
      this.exportDataList = this.exportDataList.map((row) => {
        let filterRow = this.pickPointedPropety(row,
          [
            'poNo', 'itemNo', 'purchaseGroup', 'createDate', 'componentSkuNo',
            'componentMaterialDescription', 'componentWarehouseLocation', 'componentRequiredQuantity',
            'hasMakerQuantity', 'unMakerQuantity', 'availableMakerQuantity', 'balanceStockQuantity',
            'availableStockQuantity', 'totalMakerUnPostQuantity', 'componentStockQuantity', 'supplierQuantity',
            'approveStep', 'supplierNo', 'supplierName', 'skuNo', 'materialDescription', 'itemQuantity', 'unit',
            'itemDeliveryDate', 'warehouseLocation', 'receivedQuantity', 'outstandingQuantity', 'createUser',
            'upperNo', 'componentIntransitOrderList',
            'isUrgent', 'urgentNote', 'componentIntransitInventoryList', 'outSourcingIAOList', 'inStockQuantity',
            'systemStatus', 'systemStatusText', 'asnNo', 'asnStatus', 'checkBatchList', 'checkQuantityList',
            'postInQuantityList', 'checkCreateDate', 'checkApproveDate', 'codeList', 'shortTextList', 'checkResultList'
          ]
        )
        filterRow.supplierNo = row.supplierNo + ' ' + row.supplierName
        filterRow.componentWarehouseLocation = filterRow.componentWarehouseLocation && (filterRow.componentWarehouseLocation + ' ' + ((this.warehouseList || []).find(item => item.warehouseLocationCode === filterRow.componentWarehouseLocation) || {}).warehouseLocationName)
        filterRow.warehouseLocation = filterRow.warehouseLocation && (filterRow.warehouseLocation + ' ' + ((this.warehouseList || []).find(item => item.warehouseLocationCode === filterRow.warehouseLocation) || {}).warehouseLocationName)

        arrayList.forEach(prop => {
          filterRow[prop] = this.flatten(filterRow[prop])
        })

        filterRow.isUrgent = this.mapBool(filterRow.isUrgent)

        delete filterRow.supplierName
        return filterRow
      })
      this.exportDataList.map((item) => {
        item.purchaseGroup = item.purchaseGroup + ' ' + ((this.purchaseGroupList || []).find(it => it.groupCode === item.purchaseGroup) || {}).userName
        item.approveStep = ((this.dictList.approveStatus || []).find(it => Number(it.value) === item.approveStep) || {}).name
        return item
      })
      const listData = this.exportDataList.map((item) => {
        const data = { ...item }
        Object.keys(data).forEach((key) => {
          if (mapping[key]) {
            data[mapping[key]] = data[key]
            delete data[key]
          }
        })
        return data
      })
      console.log(listData)
      writeFile(listData, `委外链路导出明细-${new Date().toISOString().split('T')[0]}.xlsx`, { header: this.tableHead })
    }

  }
}
</script>

<style lang="scss" scoped>
.inventory-upload-wrapper {
  .action {
    float: right;
  }
}
</style>
