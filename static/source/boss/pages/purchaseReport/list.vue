<template>
  <div class="app-container order-list-components">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
        label-suffix=":"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="单号检索" prop="orderNoList">
              <el-input
                v-model="searchForm.orderNoList"
                placeholder="支持PMS申请单号、SAP申请单号、关联单据号检索，最多200个"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="单据类型" prop="orderType">
              <el-select
                v-model="searchForm.orderType"
                filterable
                default-first-option
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in buildOptions('iaoType')"
                  :key="item.name"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="单据状态" prop="orderStatus">
              <el-select
                v-model="searchForm.orderStatus"
                default-first-option
                filterable
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in buildOptions('iaoStatus')"
                  :key="item.name"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="创建人员" prop="createUser">
              <el-input
                v-model.trim="searchForm.createUser"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="批次编号" prop="batchNo">
              <el-input
                v-model.trim="searchForm.batchNo"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工厂名称" prop="factoryCode">
              <el-select
                v-model="searchForm.factoryCode"
                default-first-option
                filterable
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in companyFactoryList"
                  :key="item.companyCode"
                  :label="item.companyCode + ' ' + item.companyName"
                  :value="item.companyCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收工厂名称" prop="receiveFactoryCode ">
              <el-select
                v-model="searchForm.receiveFactoryCode"
                filterable
                default-first-option
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in companyFactoryList"
                  :key="item.companyCode"
                  :label="item.companyCode + ' ' + item.companyName"
                  :value="item.companyCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="创建日期" prop="createDate">
              <el-date-picker
                v-model="searchForm.createDate"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="仓库地点" prop="warehouseLocation">
              <el-select
                v-model="searchForm.warehouseLocation"
                filterable
                default-first-option
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in warehouseList"
                  :key="item.factoryCode + item.warehouseLocationCode"
                  :label="
                    item.warehouseLocationCode +
                      ' ' +
                      item.warehouseLocationName
                  "
                  :value="item.warehouseLocationCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收仓库地点" prop="receiveWarehouseLocation">
              <el-select
                v-model="searchForm.receiveWarehouseLocation"
                filterable
                default-first-option
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in warehouseList"
                  :key="item.factoryCode + item.warehouseLocationCode"
                  :label="
                    item.warehouseLocationCode +
                      ' ' +
                      item.warehouseLocationName
                  "
                  :value="item.warehouseLocationCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="过账人员" prop="postUser">
              <el-input
                v-model.trim="searchForm.postUser"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="供应商" prop="supplierData">
              <SelectSupplier
                :data.sync="searchForm.supplierData"
                @change="(val) => (handleChangeSupplier(val))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKU编码" prop="skuNoList">
              <el-input
                v-model="searchForm.skuNoList"
                placeholder="最多200个按空格或换行符隔开"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接收SKU编码" prop="receiveSkuNoList">
              <el-input

                v-model="searchForm.receiveSkuNoList"
                placeholder="最多200个按空格或换行符隔开"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="过账日期" prop="postDate">
              <el-date-picker
                v-model="searchForm.postDate"
                @change="changePostDate"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="物料组" prop="materialGroupNum">
              <SelectMaterialGroup
                :data.sync="searchForm.materialGroupNumTmp"
                :materialList="materialList"
                @change="(val) => handleChangeMaterialGroup(val)"
                @clear="handleClearMaterialGroup()"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="创建方式" prop="createWay">
              <el-select
                style="width:100%"
                v-model="searchForm.createWay"
                clearable
                placeholder="">
                <el-option
                  v-for="item in buildOptions('iaoCreateWay')"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :offset="6" :span="6">
            <el-form-item  style="min-width:130px;float: right;">
              <el-button type="primary" @click="search">查询</el-button>
              <el-button @click="reset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      ref="aggreementGrid"
      id="aggreement_grid"
      align="center"
      height="450"
      :loading="loading"
      :custom-config="tableCustom"
      :data="inventoryReqList"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{ trigger: 'click', mode: 'row', showStatus: true }"
    >
      <!--   status 1是绿 2是黄  3是红 :disabled="pageData.length<=0"-->
      <template v-slot:toolbar_buttons>
        <vxe-toolbar>
          <template #tools>
          <el-button :loading="exportLoading" v-if="getButtonAuth('库存申请单明细', '导出明细')" type="primary" @click="exportDetail" :disabled="inventoryReqList.length<=0">导出明细</el-button>
        </template>
        </vxe-toolbar>
      </template>
      <template v-slot:status_default="{ row }">
        <span
          class="circle"
          :class="row.status === 1 ? 'green' : ''"
          v-if="row.status === 1"
        >
        </span>
        <span
          class="circle"
          :class="row.status === 2 ? 'red' : ''"
          v-if="row.status === 2"
        >
        </span>
        <span
          class="circle"
          :class="row.status === 3 ? 'yellow' : ''"
          v-if="row.status === 3"
        >
        </span>
      </template>
      <template v-slot:orderNo_default="{ row }">
        <el-link @click="toDetail(row)" type="primary">{{
          row.orderNo
        }}</el-link>
      </template>
      <template v-slot:orderType_default="{ row }">
        {{
          (
            (buildOptions('iaoType') || []).find(
              (item) => item.value === row.orderType
            ) || {}
          ).label
        }}
      </template>
      <template v-slot:orderStatus_default="{ row }">
        {{
          (
            (buildOptions('orderStatus') || []).find(
              (item) => item.value == row.orderStatus
            ) || {}
          ).label
        }}
      </template>
      <template v-slot:createWay_default="{ row }">
        {{
          (
            (buildOptions('iaoCreateWay') || []).find(
              (item) => item.value == row.createWay
            ) || {}
          ).label
        }}
      </template>
      <template v-slot:postDate_default="{ row }">
        {{ row.postDate | dateForamte('YYYY-MM-DD')}}
      </template>
      <template v-slot:certificateDate_default="{ row }">
        {{ row.certificateDate | dateForamte('YYYY-MM-DD')}}
      </template>
      <template v-slot:factoryCode_default="{ row }">
        {{
         row.factoryCode&& (row.factoryCode +
            ' ' +
            (
              (dictList.supplierFactory || []).find(
                (item) => item.value === row.factoryCode
              ) || {}
            ).name)
        }}
      </template>
      <template v-slot:unit_default="{ row }">
        {{
          lodashStartWith(row.orderNo) ? row.unit_default : row.inventoryUnit
        }}
      </template>
      <template v-slot:receiveFactoryCode_default="{ row }">
        {{
         row.receiveFactoryCode&&(row.receiveFactoryCode +
            ' ' +
            (
              (dictList.supplierFactory || []).find(
                (item) => item.value === row.receiveFactoryCode
              ) || {}
            ).name)
        }}
      </template>
      <template v-slot:warehouseLocation_default="{ row }">
        {{
         row.warehouseLocation&& ((row.warehouseLocation || '') +
            ' ' +
            ((
              warehouseList.find(
                (item) => item.warehouseLocationCode === row.warehouseLocation
              ) || {}
            ).warehouseLocationName || ''))
        }}
      </template>
        <template v-slot:receiveWarehouseLocation_default="{ row }">
        {{
         row.receiveWarehouseLocation&& ((row.receiveWarehouseLocation || '') +
            ' ' +
            ((
              warehouseList.find(
                (item) => item.warehouseLocationCode === row.receiveWarehouseLocation
              ) || {}
            ).warehouseLocationName || ''))
        }}
      </template>
      <template v-slot:materialGroupNum_default="{ row }">
        {{
         row.materialGroupNum&& ((row.materialGroupNum || '') +
            ' ' +
            ((
              searchMaterialGroupList.find(
                (item) => item.materialGroupNum === row.materialGroupNum
              ) || {}
            ).materialGroupName || ''))
        }}
      </template>
      <template v-slot:generalLedgerAccount_default="{ row }">
        {{
         row.generalLedgerAccount&& ((row.generalLedgerAccount || '') +
            ' ' +
            ((
              dictList.iaoGeneralLedger.find(
                (item) => item.value === row.generalLedgerAccount
              ) || {}
            ).name || ''))
        }}
      </template>
      <!-- 成本中心 -->
        <template v-slot:costCenter_default="{ row }">
        {{
         row.costCenter&& ((row.costCenter || '') +
            ' ' +
            ((
             costCenterList.find(
                (item) => item.costCenter === row.costCenter
              ) || {}
            ).description || ''))
        }}
      </template>
    </vxe-grid>
    <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
       @pagination="getPaginationInventoryReqList"
      layout="total, sizes, prev, pager, next, jumper"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { getInventoryReqList, searchMaterialGroup, getCostCenter, getCompanyAndFactory } from '@/api/mm'
import Pagination from '@/components/Pagination'
import { safeRun } from '@/utils/index'
import SelectMaterialGroup from '@/pages/purchaseReport/components/SelectMaterialGroup'
import { writeFile } from '@boss/excel'
import * as moment from 'moment'
import { getButtonAuth } from '@/utils/auth'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import _, { cloneDeep } from 'lodash'

const columns = [
  {
    field: 'status',
    title: '状态',
    width: 120,
    slots: {
      // 使用插槽模板渲染
      default: 'status_default'
    }
  },
  {
    field: 'orderNo',
    title: '申请单号',
    width: 120,
    slots: {
      default: 'orderNo_default'
    }
  },
  {
    field: 'sapOrderNo',
    title: 'SAP申请单号',
    width: 120
  },
  {
    field: 'itemNo',
    title: '行号',
    width: 120
  },
  {
    field: 'orderType',
    title: '单据类型',
    width: 120,
    slots: {
      default: 'orderType_default'
    }
  },
  {
    field: 'orderStatus',
    title: '单据状态',
    width: 120,
    slots: {
      default: 'orderStatus_default'
    }
  },
  {
    field: 'createWay',
    title: '创建方式',
    width: 120,
    slots: {
      default: 'createWay_default'
    }
  },
  {
    field: 'postDate',
    title: '过账日期',
    width: 120,
    slots: {
      default: 'postDate_default'
    }
  },
  {
    field: 'factoryCode',
    title: '工厂',
    width: 200,
    slots: {
      // 使用插槽模板渲染
      default: 'factoryCode_default'
    }
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    width: 120
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    width: 200
  },
  {
    field: 'quantity',
    title: '交货数量',
    width: 80
  },
  {
    field: 'unit',
    title: '单位',
    width: 80,
    slots: {
      default: 'unit_default'
    }
  },
  { field: 'postQuantity', title: '已过账数量', width: 96 },
  { field: ' batchNo', title: '批次', width: 80 },
  {
    field: 'warehouseLocation',
    title: '库存地点',
    width: 120,
    slots: {
      default: 'warehouseLocation_default'
    }
  },
  {
    field: 'supplierData',
    title: '供应商',
    width: 120
  },
  {
    field: 'receiveFactoryCode',
    title: '接收工厂',
    width: 200,
    slots: {
      // 使用插槽模板渲染
      default: 'receiveFactoryCode_default'
    }
  },
  {
    field: 'receiveWarehouseLocation',
    title: '接收库存地点',
    width: 120,
    slots: {
      default: 'receiveWarehouseLocation_default'
    }
  },
  {
    field: 'receiveSkuNo',
    title: '接收SKU编码',
    width: 120
  },
  {
    field: 'receiveMaterialDescription',
    title: '接收物料描述',
    width: 200
  },
  {
    field: 'generalLedgerAccount',
    title: '总账科目',
    width: 200,
    slots: {
      default: 'generalLedgerAccount_default'
    }
  },

  {
    field: 'costCenter',
    title: '成本中心',
    width: 200,
    slots: {
      default: 'costCenter_default'

    }
  },
  {
    field: 'referNo',
    title: '参考单号',
    width: 200
  },

  {
    field: 'referItemNo',
    title: '参考行号',
    width: 200
  },
  {
    field: 'receivedQuantity',
    title: '寄售出货数量',
    width: 96
  },
  {
    field: 'finishedSku',
    title: '成品SKU',
    width: 80
  },
  {
    field: 'finishedSkuDescription',
    title: '成品物料描述',
    width: 200
  },
  {
    field: 'surface',
    title: '表面处理',
    width: 200
  },
  {
    field: 'weight',
    title: '单只重',
    width: 200
  },
  {
    field: 'totalWeight',
    title: '半成品重量',
    width: 200
  },
  {
    field: 'orderRemark',
    title: '抬头备注',
    width: 200
  },
  {
    field: 'projectText',
    title: '项目文本',
    width: 200
  },
  {
    field: 'createUser',
    title: '创建人',
    width: 80
  },
  {
    field: 'certificateDate',
    title: '凭证日期',
    width: 120,
    slots: {
      default: 'certificateDate_default'
    }
  },
  {
    field: 'postUser',
    title: '过账人',
    width: 80
  },
  {
    field: 'materialGroupNum',
    title: '物料组',
    width: 200,
    slots: {
      default: 'materialGroupNum_default'
    }
  }
]

export default {
  props: {},
  data() {
    return {
      searchForm: {
        // 申请单号
        orderNoList: '',
        // 单据类型
        orderType: '',
        // 单据状态
        orderStatus: '',
        // 创建人员
        createUser: '',
        // 批次编号
        batchNo: '',
        // 工厂名称
        factoryCode: '',
        // 接收工厂名称
        receiveFactoryCode: '',
        // 创建日期
        createTimeBegin: '',
        createTimeEnd: '',
        // createDate: [],
        createDate: '',
        createWay: '',
        // 参考单号
        referNo: '',
        // 仓库地点
        warehouseLocation: '',
        // 接收仓库地点
        receiveWarehouseLocation: '',
        // 过账人员
        postUser: '',
        // 供应商
        supplierData: '',
        // SKU编码
        skuNoList: '',
        // 接收SKU编码
        receiveSkuNoList: '',
        // 过账日期
        postDateBegin: '',
        postDateEnd: '',
        postDate: '',
        // 物料组
        materialGroupNum: '',
        // 按照凭证日期降序
        orderByField: 'postDate',
        orderDirection: 'DESC'
      },
      materialGroupNumTmp: '',
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      columns,
      rules: {
        factoryName: [
          { required: true, message: '请输入工厂名称', trigger: 'blur' }
        ]
      },
      listQueryInfo: {
        pageNo: 1,
        pageSize: 50
      },
      total: 0,
      // 存库申请单列表
      inventoryReqList: [],
      // 表格数据加载
      loading: false,
      exportLoading: false,
      postDate: '',
      createDate: '',
      // 物料组
      materialList: [],
      time: moment().format('YYYY-MM-DD'),
      statusList: [
        {
          status: 1,
          name: '绿'
        },
        {
          status: 2,
          name: '黄'
        },
        {
          status: 3,
          name: '红'
        }

      ],
      costCenterDataList: []
    }
  },
  components: {
    Pagination,
    SelectSupplier,
    SelectMaterialGroup
  },
  watch: {
    costCenterDataList: {
      deep: true,
      handler(val) {
      }
    }

  },
  created() {
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    if (!this.purchaseGroupList || this.purchaseGroupList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    if (!this.searchMaterialGroupList || this.searchMaterialGroupList.length === 0) {
      this.$store.dispatch('orderPurchase/getSearchMaterialGroup')
    }
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    this.getMaterialGroup()
  },
  async mounted() {
    if (!this.costCenterList || this.costCenterList.length === 0) {
      const companyFactoryList = await getCompanyAndFactory()
      companyFactoryList.forEach((companyCode) => {
        getCostCenter(companyCode).then((res) => {
          this.costCenterDataList = this.costCenterDataList.concat(res)
          this.$store.commit({ type: 'orderPurchase/SET_COST_CENTER', costCenterList: this.costCenterDataList })
        })
      })
    }
  },
  computed: {
    copySearchForm() {
      this.clean(this.searchForm)
      let processdSearchForm = {}
      for (var k in this.searchForm) {
        if (
          k === 'orderNoList' ||
          k === 'skuNoList' ||
          k === 'receiveSkuNoList'
        ) {
          processdSearchForm[k] = this.searchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else if (k === 'postDate') {
          processdSearchForm.postDateBegin = this.searchForm.postDate[0]
          processdSearchForm.postDateEnd = this.searchForm.postDate[1]
        } else if (k === 'createDate') {
          processdSearchForm.createTimeBegin = this.searchForm.createDate[0]
          processdSearchForm.createTimeEnd = this.searchForm.createDate[1]
        } else {
          processdSearchForm[k] = this.searchForm[k]
        }
      }
      delete processdSearchForm.supplierData
      delete processdSearchForm.materialGroupNumTmp
      return processdSearchForm
    },
    tableKeyValue() {
      let obj = {}
      this.columns.forEach((item) => {
        obj[item.field] = item.title
      })
      obj['customerCode'] = '客户编码'
      return obj
    },
    tableHead() {
      let arr = []
      this.columns.forEach((item) => {
        arr.push(item.title)
      })
      arr.push('客户编码')
      return arr
    },
    // pageData() {
    //   let start = (this.listQueryInfo.pageNo - 1) * this.listQueryInfo.pageSize
    //   let end = this.listQueryInfo.pageNo * this.listQueryInfo.pageSize
    //   return this.inventoryReqList.slice(start, end)
    // },
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      dictList: (state) => state.orderPurchase.dictList,
      searchMaterialGroupList: (state) => state.orderPurchase.searchMaterialGroupList,
      costCenterList: (state) => state.orderPurchase.costCenterList
    })
  },
  methods: {
    handleChangeSupplier(val) {
      this.searchForm.supplierData = val
      if (val) {
        this.searchForm.supplier = val.supplierNo
        this.searchForm.supplierName = val.supplierName
      } else {
        this.searchForm.supplier = undefined
        this.searchForm.supplierName = undefined
      }
    },
    getButtonAuth,
    pickPointedPropety(object, keyArr) {
      return _.pick(object, keyArr)
    },
    // 查询列表
    search() {
      if (this.copySearchForm.skuNoList?.length > 200) {
        this.$alert('SKU编码不应该超过200个')
        return
      }
      if (this.copySearchForm.receiveSkuNoList?.length > 200) {
        this.$alert('接受SKU编码不应该超过200个')
        return
      }
      if (this.copySearchForm.batchNo?.length > 200) {
        this.$alert('批次号不应该超过200个')
        return
      }
      if (this.copySearchForm.orderNoList?.length > 200) {
        this.$alert('检索单号不应该超过200个')
        return
      }
      if (this.copySearchForm.orderNoList?.length > 200) {
        this.$alert('一次最多支持查询200个单号!')
        return
      }
      console.log('------')
      this.getInventoryReqList()
    },
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    },
    // 清楚所有查询参数
    reset() {
      // this.searchForm.materialGroupNumTmp = ''
      // this.$refs['searchForm'].resetFields()
      for (var k in this.searchForm) {
        this.searchForm[k] = ''
      }
      // this.getInventoryReqList()
    },
    // 列表查詢
    getInventoryReqList() {
      this.loading = true
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      let query = {
        pageNo: this.listQueryInfo.pageNo,
        pageSize: this.listQueryInfo.pageSize
      }

      Object.assign(this.copySearchForm, query)
      console.log('this.copySearchForm', this.copySearchForm, query)
      getInventoryReqList(this.copySearchForm).then((res) => {
        this.loading = false
        this.total = res.total
        this.inventoryReqList = res.rows
      })
    },
    getPaginationInventoryReqList() {
      this.loading = true
      let query = {
        pageNo: this.listQueryInfo.pageNo,
        // pageSize: 10000
        pageSize: this.listQueryInfo.pageSize
      }
      Object.assign(this.copySearchForm, query)
      getInventoryReqList(this.copySearchForm).then((res) => {
        console.log(res);
        this.loading = false
        this.total = res.total
        this.inventoryReqList = res.rows
      })
    },

    // 获取
    async exportDetail() {
      // 列表查詢
      let query = {
        pageNo: this.listQueryInfo.pageNo,
        pageSize: 10000
      }
      Object.assign(this.copySearchForm, query)
      this.exportLoading = true
      let { total, rows } = await getInventoryReqList(this.copySearchForm)
      this.exportLoading = false
      this.total = total
      this.inventoryReqList = rows
      if (this.total.length > 10000) {
        this.$message.warning('单次导出明细不得超过1万条，请拆分导出！')
        return
      }
      const mapping = this.tableKeyValue
      let copyInventoryReqList = cloneDeep(this.inventoryReqList)
      copyInventoryReqList = copyInventoryReqList.map((row) => {
        let filterRow = this.pickPointedPropety(row, ['status', 'orderNo', 'sapOrderNo', 'itemNo', 'orderType', 'orderStatus', 'createWay', 'postDate', 'factoryCode', 'skuNo', 'materialDescription', 'quantity', 'unit', 'postQuantity', ' batchNo', 'warehouseLocation', 'supplierName', 'receiveFactoryCode', 'receiveWarehouseLocation', 'receiveSkuNo', 'receiveMaterialDescription', 'generalLedgerAccount', 'costCenter', 'referNo', 'referItemNo', 'receivedQuantity', 'finishedSku', 'finishedSkuDescription', 'surface', 'weight', 'totalWeight', 'orderRemark', 'projectText', 'createUser', 'certificateDate', 'postUser', 'materialGroupNum', 'customerCode'])
        filterRow.status = this.statusList.find(item => item.status === filterRow.status).name
        filterRow.certificateDate = filterRow.certificateDate && moment(filterRow.certificateDate).format('YYYY-MM-DD')
        filterRow.postDate = filterRow.postDate && moment(filterRow.postDate).format('YYYY-MM-DD')
        filterRow.orderType = (
          (this.buildOptions('iaoType') || []).find(
            (it) => it.value === filterRow.orderType
          ) || {}
        ).label
        filterRow.orderStatus = (
          (this.buildOptions('orderStatus') || []).find(
            (it) => Number(it.value) === filterRow.orderStatus
          ) || {}
        ).label
        filterRow.createWay = (
            (this.buildOptions('iaoCreateWay') || []).find(
              (item) => item.value === row.createWay
            ) || {}
          ).label
        filterRow.factoryCode = filterRow.factoryCode +
            ' ' +
            (
              (this.dictList.supplierFactory || []).find(
                (it) => it.value === filterRow.factoryCode
              ) || {}
            ).name
        filterRow.receiveFactoryCode = filterRow.receiveFactoryCode && (filterRow.receiveFactoryCode +
            ' ' +
            (
              (this.dictList.supplierFactory || []).find(
                (it) => it.value === filterRow.receiveFactoryCode
              ) || {}
            ).name)
        filterRow.warehouseLocation =
             (filterRow.warehouseLocation || '') +
            ' ' +
            ((
              this.warehouseList.find(
                (it) => it.warehouseLocationCode === filterRow.warehouseLocation
              ) || {}
            ).warehouseLocationName || '')
        filterRow.receiveWarehouseLocation = filterRow.receiveWarehouseLocation && ((filterRow.receiveWarehouseLocation || '') +
            ' ' +
            ((
              this.warehouseList.find(
                (it) => it.warehouseLocationCode === filterRow.receiveWarehouseLocation
              ) || {}
            ).warehouseLocationName || ''))
        filterRow.materialGroupNum =
            ((
              this.searchMaterialGroupList.find(
                (item) => item.materialGroupNum === filterRow.materialGroupNum
              ) || {}
            ).materialGroupName || '')
            // 总账科目
        filterRow.generalLedgerAccount = filterRow.generalLedgerAccount && ((filterRow.generalLedgerAccount || '') +
            ' ' +
            ((
              this.dictList.iaoGeneralLedger.find(
                (item) => item.value === filterRow.generalLedgerAccount
              ) || {}
            ).name || ''))
        // 成本中心
        filterRow.costCenter = filterRow.costCenter && ((filterRow.costCenter || '') +
            ' ' +
            ((
              this.costCenterList.find(
                (item) => item.costCenter === filterRow.costCenter
              ) || {}
            ).description || ''))
        return filterRow
      })
      let listData = copyInventoryReqList.map((item) => {
        const data = { ...item }
        Object.keys(data).forEach((key) => {
          if (mapping[key]) {
            data[mapping[key]] = data[key]
            delete data[key]
          }
        })
        return data
      })
      writeFile(listData, '库存申请单明细.xlsx', { header: this.tableHead })
    },
    buildOptions(prop) {
      if (prop === 'orderType') prop = 'iaoType'
      if (prop === 'orderStatus') prop = 'iaoStatus'
      let ret = []
      safeRun(() => {
        if (!this.dictList[prop]) return
        ret = this.dictList[prop].map((prop) => ({
          value: prop.value,
          label: prop.name
        }))
      })
      return ret
    },
    changePostDate(val) {},
    toDetail(row) {
      this.$router.push(`/inventoryManagement/detail/${row.orderNo}`)
    },
    lodashStartWith(OrderNo) {
      return _.startsWith(OrderNo, '628')
    },
    // 查询物料组
    getMaterialGroup() {
      // 需要添加条件
      searchMaterialGroup('').then((data) => {
        if (data) {
          this.materialList = data
        }
      })
    },
    handleClearMaterialGroup() {
      this.searchForm.materialGroupNum = ''
      this.searchForm.materialGroupNumTmp = ''
    },
    handleChangeMaterialGroup(val) {
      this.searchForm.materialGroupNum = val.materialGroupNum
    }
  },
  filters: {
    dateForamte(input, fmtstring = 'YYYY-MM-DD') {
      if (input) {
        return moment(input).format(fmtstring);
      } else {
        return '';
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.btn-container {
  overflow: hidden;
  padding-bottom: 10px;
  .mini-container {
    margin-left: 120px;
  }
  .float-rigth {
    float: right;
  }
}
.min-width {
  min-width: 130px;
}
.pagination-container {
  background-color: #fff;
  padding: 16px;
}
.circle {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
}
.green {
  background-color: green;
}

.red {
  background-color: red;
}
.yellow {
  background-color: yellow;
}

.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
  width: 100%;
}
.button-group{
  width: 100%;
  display: flex;
  justify-content:flex-end;
  margin: 10px 0px;
}
</style>
