<template>
  <div class="tab-setting">
    <div class="content">
      <h2 @dblclick="click">设置新开页面方式</h2>
      <el-radio-group v-model="radio" @change="radioChange">
        <el-radio :label="1">部分页面浏览器窗口新开tab</el-radio>
        <el-radio :label="2">BOSS内新开tab页面（原有方式）</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
export default {
  name: 'tabSetting',
  data () {
    return {
      radio: 1
    }
  },
  methods: {
    click () {
      console.log('this.$closeTag(this.$route.path) will work')
      setTimeout(() => {
        this.$closeTag(this.$route.path)
      }, 1000)
    },
    radioChange (value) {
      if (value === 2) {
        localStorage.setItem('open-in-new-tab', 'boss')
      } else {
        localStorage.removeItem('open-in-new-tab')
      }
      this.$message.success('修改成功，即将刷新页面重置配置！')
      setTimeout(() => {
        window.location.reload()
      }, 2000)
    }
  },
  mounted () {
    if (localStorage.getItem('open-in-new-tab') === 'boss') {
      this.radio = 2
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-setting{
  max-width: 800px;
  margin: auto;
  margin-top: 20px;
  .content{
    display: flex;
    padding: 20px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    h2{
      margin: 20px;
    }
  }
}
</style>
