
export const columns = [
  { label: 'SKU', prop: 'sku' },
  { label: '物料描述', prop: 'materialDes', minWidth: '200px' },
  { label: '基本单位', prop: 'baseUnit' },
  {
    label: '工厂',
    prop: 'factoryName',
    minWidth: '100px',
    format: (row) => {
      const { factoryNo, factoryName } = row
      const res = (factoryNo || '') + (factoryName || '')
      const result = {
        res
      }
      return result
    }
  },
  {
    label: '库位',
    prop: 'positionNo',
    format: (row) => {
      const { positionNo, positionName } = row
      const res = (positionNo || '') + (positionName || '')
      const result = {
        res
      }
      return result
    }
  },
  {
    label: '安全库存',
    prop: 'safeStock',
    format: (row) => {
      const { safeStock } = row
      const res = (safeStock || '')
      const result = {
        res
      }
      return result
    }
  },
  { label: '供需类型', prop: 'typeName' },
  { label: '供需编号',
    prop: 'atpNo',
    minWidth: '160px',
    format: (row) => {
      let { atpNo, typeName } = row
      let result = {
        type: null,
        no: atpNo,
        res: atpNo
      }
      if (typeName === '销售出库单') {
        let tmp = atpNo.split('/')[0]
        let suffix = atpNo.split('/')
        suffix.shift()
        result.type = 'dn'
        result.no = tmp
        result.res = suffix.join('/')
      }
      if (typeName === '销售订单') {
        let tmp = atpNo.split('/')[0]
        let suffix = atpNo.split('/')
        suffix.shift()
        result.type = 'so'
        result.no = tmp
        result.res = suffix.join('/')
      }
      if (typeName === '采购订单' || typeName === '转储单-入' || typeName === '转储单-出') {
        let tmp = atpNo.split('/')[0]
        let suffix = atpNo.split('/')
        suffix.shift()
        result.type = 'po'
        result.no = tmp
        result.res = suffix.join('/')
      }
      return result
    }
  },
  { label: '计划日期', prop: 'planDay', minWidth: '120px' },
  { label: '修改日期', prop: 'createDay', minWidth: '160px' },
  { label: '首个交期', prop: 'deliveryDate', minWidth: '160px' },
  { label: '供需数量',
    prop: 'supplyAndProvideNumber',
    colorRed: (value) => {
      if (value && value.indexOf('-') !== -1) {
        return 'red'
      }
    }
  },
  { label: '配额供给', prop: 'ifsupplyquota' },
  { label: '已占数量', prop: 'matchNumber' },
  { label: '未占数量', prop: 'unOccupyNum' },
  { label: '未占原因', prop: 'unOccupyReason' },
  { label: '占用类型', prop: 'matchType' },
  { label: '可用在库量', prop: 'inLibNum' },
  { label: '可用在途量', prop: 'onRoadNum' },
  { label: '可用供给量', prop: 'supplyNum' },
  { label: '客户名称', prop: 'customerName', minWidth: '160px' },
  { label: '客户编号', prop: 'customerNo' },
  { label: '客服', prop: 'customerServiceName' },
  { label: '销售', prop: 'saleName' },
  { label: '采购', prop: 'buyerName' },
  { label: '客服经理', prop: 'customerServerManagerName' },
  { label: '客服主管', prop: 'customerServerChargeName' }
]

export const summaryColumns = [
  { label: 'SKU', prop: 'sku' },
  { label: '物料描述', prop: 'materialDes', minWidth: '200px' },
  { label: '基本单位', prop: 'baseUnit' },
  {
    label: '工厂',
    prop: 'factoryNo',
    format: (row) => {
      const { factoryNo, factoryName } = row
      return (factoryNo || '') + (factoryName || '')
    }
  },
  {
    label: '库位',
    prop: 'positionNo',
    format: (row) => {
      const { positionNo, positionName } = row
      return (positionNo || '') + (positionName || '')
    }
  },
  {
    label: '安全库存',
    prop: 'safeStock',
    format: (row) => {
      const { safeStock } = row
      return (safeStock || '')
    }
  },
  { label: '在库库存', prop: 'onLabNum' },
  { label: '在途数量', prop: 'routeNum' },
  { label: '已占在库数量', prop: 'matchNumber' },
  { label: '已占在途数量', prop: 'onOccupyRoadNum' },
  { label: '未占数量', prop: 'unOccupyNum' },
  { label: '可用在库量', prop: 'inLibNum' },
  { label: '可用在途量', prop: 'onRoadNum' },
  { label: '可用供给量', prop: 'supplyNum' }
]
