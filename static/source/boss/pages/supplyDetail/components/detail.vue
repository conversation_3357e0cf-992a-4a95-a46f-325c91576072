
<template>
  <div class="allocation-detail">
    <el-steps :active="activeStatus">
      <el-step title="申请"></el-step>
      <el-step title="审核中"></el-step>
      <el-step title="已完成"></el-step>
    </el-steps>
    <el-row type="flex" class="allocation-title" justify="space-between">
      <el-col :span="12">
        <div class="title">匀货申请</div>
      </el-col>
      <el-col :span="12">
        <div class="title" style="text-align:right">
          申请人：{{data.applyUserName ||user}} 申请日期:{{data.applyDate || time}}
        </div>
      </el-col>
    </el-row>
    <el-form :model="allocationData" label-width="80px" ref="allocationForm">
      <el-row>
        <el-col :span="8">
          <el-form-item label="SKU:">
            <span class="allocation-strong">{{data.sku}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料描述:">
            <span class="allocation-strong">{{data.materialDes}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="基本单位:">
            <span class="allocation-strong">{{data.baseUnit}}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="工厂:">
            <span class="allocation-strong">{{data.factoryNo}}{{data.factoryName}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="库存地点:">
            <span class="allocation-strong">{{data.positionNo}}{{data.positionName}}</span>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="失败原因:">
            <span class="allocation-strong">{{data.errMsg || ''}}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="备注:">
            <span v-if="data.adjustInventoryNo" class="allocation-strong">{{data.notes || ''}}</span>
            <el-input v-else v-model="allocationData.notes" placeholder="备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <DividerHeader>匀出信息</DividerHeader>
    <el-table
      :data="outList"
      style="width: 100%"
    >
      <el-table-column
        v-for="col in tableList"
        :key="col.prop"
        :prop="col.prop"
        :label="col.title"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="已占用量"
        align="center"
        prop="matchNumber"
      ></el-table-column>
      <el-table-column
        label="匀货数量"
        width="140px"
        align="center"
        prop="adjustNumber"
      >
        <template slot-scope="{row}">
          <el-input-number
            v-if="!data.adjustInventoryNo"
            v-model="row.adjustNumber"
            :min="1"
            :max="10000000"
          />
          <span v-else>{{row.adjustNumber}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        width="80px"
        align="center"
        prop="adjustStatus"
      >
        <template slot-scope="{row}">
          <span>{{allocationStatusInfo[row.adjustStatus] | allocationStatusInfoFilter(data.errMsg)}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="优先级互换"
        width="80px"
        align="center"
        prop="priorityInterchange"
      >
        <template slot-scope="{row}">
          <span>{{row.priorityInterchange === 'Y' ? '是' : '否'}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        width="130"
        fixed="right"
        v-if="data.action!=='view'"
        class-name="small-padding"
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="!isFinalStatus(row)&&data.action==='agree'"
            type="text"
            size="mini"
            @click="toAgree(row)"
          >
            同意
          </el-button>
          <el-button
            v-if="!isFinalStatus(row)&&data.action==='reject'"
            type="text"
            size="mini"
            @click="toReject(row)"
          >
            驳回
          </el-button>
          <el-button
            v-if="!isFinalStatus(row)&&data.action==='withdraw'"
            type="text"
            size="mini"
            @click="toWithdraw(row)"
          >
            撤回
          </el-button>
          <el-button
            v-if="!data.adjustInventoryNo"
            type="text" size="mini" @click="toDel(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="allocation-right">匀出合计：{{outCount}}</div>
    <DividerHeader>匀入信息</DividerHeader>
    <el-table
      :data="inFilterList"
      style="width: 100%">
      <el-table-column
        v-for="col in tableList"
        :key="col.prop"
        :prop="col.prop"
        :label="col.title"
        align="center"
      >
      </el-table-column>
      <el-table-column
        label="未占用量"
        align="center"
        prop="unOccupyStockNum"
      ></el-table-column>
      <el-table-column
        label="匀货数量"
        width="140px"
        align="center"
        prop="adjustNumber"
      >
        <template slot-scope="{row}">
          <el-input-number
            v-if="!data.adjustInventoryNo"
            v-model="row.adjustNumber"
            :min="0"
            :max="Number(row.unOccupyStockNum)"
          />
          <span v-else>{{row.adjustNumber}}</span>
        </template>
      </el-table-column>
       <el-table-column
        label="操作"
        align="center"
        width="130"
        fixed="right"
        v-if="data.action!=='view'"
        class-name="small-padding"
      >
        <template slot-scope="{ row }">
          <el-button
            v-if="!data.adjustInventoryNo"
            type="text" size="mini" @click="toInDel(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="allocation-right">匀入合计：{{inCount}}</div>
    <div class="allocation-btn-row" v-if="!data.adjustInventoryNo">
      <el-button class="allocation-button" type="primary" @click="submitForm('allocationForm')">提交</el-button>
      <el-button class="allocation-button" @click="cancel('allocationForm')">取消</el-button>
    </div>
  </div>
</template>

<script>
import * as moment from 'moment'
import DividerHeader from '@/components/DividerHeader'
import { saveAllocate, changeAllocate } from '@/api/orderSaleAllocate'
import { allocationStatusInfo, allocationStatusInfoFilter } from '../constants'

const CUR_DATA = window.CUR_DATA

export default {
  components: {
    DividerHeader
  },
  props: [
    'data', 'activeTab'
  ],
  data () {
    return {
      allocationData: {},
      tableList: [
        { title: '所有人', prop: 'ownerUserName' },
        { title: '单据类型', prop: 'sourceTypeName' },
        { title: '单据编号', prop: 'receiptNo' },
        { title: '单据行号', prop: 'receiptItemNo' },
        { title: '需求数量', prop: 'supplyAndProvideNumber' },
        { title: '占用类型', prop: 'adjustInventoryTypeName' }
      ],
      user: CUR_DATA.user && CUR_DATA.user.name,
      time: moment().format('YYYY-MM-DD HH:mm:ss'),
      inExcludeRow: [],
      outExcludeRow: [],
      allocationStatusInfo
    }
  },
  filters: {
    allocationStatusInfoFilter
  },
  computed: {
    activeStatus () {
      if (this.data && this.data.adjustStatus === 'INIT') {
        return 1
      } else if (this.data && this.data.adjustStatus === 'NEW') {
        return 2
      }
      return 3
    },
    inFilterList () {
      return this.data
        ? (this.data.inItems || []).filter(item => {
          let flag = this.outExcludeRow.find((a) => a.atpNo === item.atpNo && a.matchType === item.matchType)
          return !flag
        })
        : []
    },
    outList () {
      return this.data
        ? (this.data.outItems || []).filter(item => this.inExcludeRow.indexOf(item.atpNo) === -1)
        : []
    },
    inCount () {
      return this.inFilterList.reduce((total, item) => {
        const num = item.adjustNumber ? Math.abs(item.adjustNumber) : 0
        return total + (num || 0)
      }, 0)
    },
    outCount () {
      return this.outList.reduce((total, item) => {
        const num = item.adjustNumber ? Math.abs(item.adjustNumber) : 0
        return total + (num || 0)
      }, 0)
    }
  },
  methods: {
    isFinalStatus (row) {
      const { adjustStatus } = row
      return ['FINISHED', 'REJECTED', 'MANUAL_CANCEL', 'AUTO_CANCEL'].indexOf(adjustStatus) > -1
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { notes } = this.allocationData
          const { baseUnit, factoryName, factoryNo, materialDes,
            positionName, positionNo, sku } = this.data
          const outFormat = this.outList
          const inFormat = this.inFilterList

          if (this.inCount !== this.outCount) {
            this.$message.error('匀入总数量应等于匀出总数量！')
            return
          }
          let inZero = inFormat.find((a) => !a.adjustNumber)
          if (inZero) {
            this.$message.error('匀货数量不能为零！')
            return
          }

          const loading = this.$loading({
            lock: true,
            background: 'rgba(0, 0, 0, 0.5)'
          })
          saveAllocate({
            baseUnit,
            factoryName,
            factoryNo,
            materialDes,
            positionName,
            positionNo,
            sku,
            notes,
            items: [
              ...outFormat,
              ...inFormat
            ]
          }).then(res => {
            loading.close()
            if (res && res.code !== 200 && res.msg) {
              this.$alert(res.msg.split(/[\s\n]/).join('<br>'), '错误', {
                dangerouslyUseHTMLString: true,
                type: 'error'
              })
            }
            if (res && res.code === 200) {
              this.$emit('close')
              this.$closeTag('/allocation/list')
              this.$router.push({
                path: '/allocation/list'
              })
            }
          })
        } else {
          return false
        }
      })
    },
    cancel (formName) {
      this.$refs[formName].resetFields()
      this.$emit('close')
    },
    toDel (row) {
      this.$confirm('确定删除？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then(() => {
        this.inExcludeRow.push(row.atpNo)
      })
    },
    toInDel (row) {
      this.$confirm('确定删除？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then(() => {
        this.outExcludeRow.push({ atpNo: row.atpNo, matchType: row.matchType })
      })
    },
    toOperation (row, type) {
      const { adjustInventoryNo, adjustInventoryItemNo } = row
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      changeAllocate(adjustInventoryNo, adjustInventoryItemNo, type).then(res => {
        loading.close()
        if (res && res.code === 500) {
          this.$alert(res.msg, '错误', {
            type: 'error'
          })
        }
        if (res && res.code === 200) {
          this.$alert('操作成功', '成功', {
            confirmButtonText: '确定',
            callback: action => {
              this.$emit('close', true)
            }
          })
        }
      })
    },
    toAgree (row) {
      this.toOperation(row, 'FINISHED')
    },
    toReject (row) {
      this.toOperation(row, 'REJECTED')
    },
    toWithdraw (row) {
      this.toOperation(row, 'MANUAL_CANCEL')
    }
  }
}
</script>

<style lang="scss" scoped>
  .allocation-detail {
    padding: 0 10px;
  }
  .allocation-title {
    background: #409EFF;
    color: #fff;
    margin: 5px 0;
    .title {
      line-height: 3;
      padding: 0 10px;
    }
  }
  .allocation-right {
    text-align: right;
    margin: 20px 0;
    padding-right: 20px;
  }
  .allocation-btn-row {
    margin: 10px;
    text-align: center;
  }
  .allocation-strong {
    color: #000;
  }
</style>
