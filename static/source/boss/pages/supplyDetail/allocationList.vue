<template>
  <div class="allocation-list">
    <el-form
      :model="queryData"
      ref="allocationForm"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="14">
          <el-form-item label="SKU" prop="sku">
            <el-input v-model="queryData.sku" placeholder="多个sku用空格隔开，最多输入10个sku" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="工厂编号" prop="sku">
            <el-input v-model="queryData.factoryNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="3" style="text-align:center">
          <el-button type="primary" class="allocation-button" @click="submitForm('allocationForm')">查询</el-button>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="7">
          <el-form-item label="库存地点编号" prop="sku">
            <el-input v-model="queryData.positionNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="申请人" prop="sku">
            <el-input v-model="queryData.applyUserName" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="申请日期" prop="sku">
            <el-date-picker
              clearable
              v-model="queryData.date"
              type="daterange"
              range-separator="~"
              value-format="yyyy-MM-dd"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="3" style="text-align:center">
          <el-button class="allocation-button" @click="resetForm('allocationForm')">重置</el-button>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="7">
          <el-form-item label="匀货单单号" prop="adjustInventoryNo">
            <el-input v-model="queryData.adjustInventoryNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="匀入单号" prop="inReceiptNo">
            <el-input v-model="queryData.inReceiptNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="匀出单号" prop="outReceiptNo">
            <el-input v-model="queryData.outReceiptNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-tabs v-model="allocationActive" @tab-click="handleTabClick" class="allocation-tabs">
      <el-tab-pane label="待处理" name="0">
      </el-tab-pane>
      <el-tab-pane label="我的申请" name="1">
      </el-tab-pane>
      <el-tab-pane label="历史处理" name="2">
      </el-tab-pane>
      <el-tab-pane label="历史申请" name="3">
      </el-tab-pane>
    </el-tabs>
    <el-table
      :height="400"
      :data="listData"
      v-loading="loading"
      style="width: 100%"
    >
      <el-table-column
        v-for="col in tableList"
        :key="col.prop"
        :prop="col.prop"
        :label="col.title"
        :show-overflow-tooltip="!!col.showOverflowTooltip"
        align="center"
      >
        <template slot-scope="{row}">
          <el-link type="primary" @click="handleView(row)" v-if="col.prop === 'adjustInventoryNo'">{{row[col.prop]}}</el-link>
          <span v-else-if="col.format" class="towLine" :title="col.format(row)">
            {{ col.format(row) }}
          </span>
          <span v-else class="towLine" :title="row[col.prop]">
            {{ row[col.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button @click="handleAgree(scope.row)" type="text" size="mini" v-if="allocationActive==='0'">
            同意
          </el-button>
          <el-button @click="handleReject(scope.row)" type="text" size="mini" v-if="allocationActive==='0'">
            驳回
          </el-button>
          <el-button @click="handleWithdraw(scope.row)" type="text" size="mini" v-if="allocationActive==='1'">
            撤回
          </el-button>
          <el-button @click="handleView(scope.row)" type="text" size="mini" v-if="allocationActive!=='0'&&allocationActive!=='1'">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      style="text-align:right"
      v-show="total > 0"
      :total="total"
      :page.sync="current"
      :limit.sync="pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="query"
    />
    <el-dialog
      title="匀货详情"
      :visible.sync="allocationVisible"
      :destroy-on-close="true"
      width="1000px"
    >
      <AllocationDetail
        :data="allocationData"
        @close="handleClose"
      />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import AllocationDetail from '@/pages/supplyDetail/components/detail'
import { allocateList, getAllocate } from '@/api/orderSaleAllocate'
import { allocationStatusInfo, allocationStatusInfoFilter } from './constants'

const tabData = {
  0: 'NEED_ME_TO_DO',
  1: 'MY_APPLY_PENDING',
  2: 'MY_HANDLED',
  3: 'MY_APPLY_HANDLED'
}

export default {
  components: {
    Pagination, AllocationDetail
  },
  data () {
    return {
      loading: false,
      queryData: {},
      listData: [],
      tableList: [
        { title: '申请单号', prop: 'adjustInventoryNo' },
        { title: '申请日期', prop: 'applyDate' },
        { title: '申请人', prop: 'applyUserName' },
        { title: 'SKU', prop: 'sku' },
        { title: '物料描述', prop: 'materialDes' },
        { title: '单位', prop: 'baseUnit' },
        { title: '工厂', prop: 'factoryNo', format: (row) => `${row.factoryNo}${row.factoryName}` },
        { title: '库存地点', prop: 'positionNo', format: (row) => `${row.positionNo}${row.positionName}` },
        { title: '匀入单据', prop: 'inReceiptNoList', format: (row) => `${row.inReceiptNoList ? row.inReceiptNoList.join(',') : ''}` },
        { title: '匀出单据', prop: 'outReceiptNo' },
        { title: '匀货类型', prop: 'receiptNo', format: (row) => `${this.checkType(row.adjustInventoryType)}` },
        { title: '匀出数量', prop: 'adjustNumber' },
        { title: '错误消息', prop: 'errMsg', showOverflowTooltip: true },
        { title: '备注', prop: 'notes' },
        { title: '状态', prop: 'adjustStatus', format: (row) => `${allocationStatusInfoFilter(allocationStatusInfo[row.adjustStatus], row.errMsg)}` }
      ],
      allocationActive: '0',
      allocationVisible: false,
      allocationData: {},
      current: 1,
      pageSize: 20,
      total: 0
    }
  },
  mounted () {
    this.query()
  },
  methods: {
    handleTabClick (tab, event) {
      this.current = 1
      this.query()
    },
    checkType (type) {
      return ({
        IN: '匀入',
        OUT: '匀出'
      })[type]
    },
    clean (obj) {
      for (var propName in obj) {
        if (!obj[propName]) {
          delete obj[propName]
        }
      }
    },
    query () {
      const { applyUserName, factoryNo, positionNo, sku, inReceiptNo, outReceiptNo, adjustInventoryNo, date } = this.queryData
      const activeTab = parseInt(this.allocationActive, 10)
      const data = {
        applyUserName,
        factoryNo,
        positionNo,
        inReceiptNo,
        outReceiptNo,
        adjustInventoryNo,
        workspaceTab: tabData[activeTab],
        pageNo: this.current,
        pageSize: this.pageSize
      }
      if (sku) {
        data.skus = sku.split(' ').filter(item => item)
      }
      if (date && date.length === 2) {
        data.beginApplyDate = date[0]
        data.endApplyDate = date[1]
      }
      this.loading = true
      this.clean(data)
      allocateList(data).then(res => {
        this.loading = false
        console.log(res, 1233333)
        if (res && res.code === 200) {
          this.listData = res.data
          this.total = res.totalCount
        } else if (res && res.msg) {
          this.$alert(res.msg, '错误', {
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
      })
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.query()
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    resetForm (formName) {
      this.$refs[formName].resetFields()
      this.query()
    },
    getDetail (row, callback) {
      getAllocate(row.adjustInventoryNo).then(res => {
        if (res && res.code === 200) {
          this.allocationVisible = true
          callback && callback(res.data)
        }
      })
    },
    handleView (row) {
      this.getDetail(row, (data) => {
        this.allocationData = {
          ...data,
          action: 'view'
        }
      })
    },
    handleAgree (row) {
      this.getDetail(row, (data) => {
        this.allocationData = {
          ...data,
          action: 'agree'
        }
      })
    },
    handleReject (row) {
      this.getDetail(row, (data) => {
        this.allocationData = {
          ...data,
          action: 'reject'
        }
      })
    },
    handleWithdraw (row) {
      this.getDetail(row, (data) => {
        this.allocationData = {
          ...data,
          action: 'withdraw'
        }
      })
    },
    handleClose (refresh) {
      this.allocationVisible = false
      if (refresh) {
        this.query()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.allocation-list {
  margin-right: 15px;
  padding: 20px;
  .allocation-button {
    width: 70px;
  }
  .allocation-tabs {
    margin: 0 20px;
  }
}

.towLine {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
