<template>
  <div class="supply-detail">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="14">
            <el-form-item label="SKU：" prop="skuNos">
              <el-input
                v-model="searchForm.skuNos"
                placeholder="多个sku用逗号隔开, 最多输入10个SKU"
                clearable
                @change="changeOrderNoOpp"
                :disabled="disableGroup1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="库存地点编号：" prop="positionNo">
              <el-select
                v-model="searchForm.positionNo"
                clearable
                filterable
                default-first-option
                @change="changePositionNo"
                :disabled="disablePosition"
              >
                <el-option
                  v-for="item in setwarehouseList"
                  :key="item.factoryCode+item.warehouseLocationCode"
                  :label="`${item.warehouseLocationCode} ${item.warehouseLocationName}`"
                  :value="item.warehouseLocationCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="text-align: center">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="loading.searchLoading"
              @click="getSupplyDetailList"
              >查询</el-button
            >
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="工厂编号：" prop="factoryNo">
              <el-input
                v-model.trim="searchForm.factoryNo"
                placeholder="请输入"
                clearable
                @change="changeOrderNoOpp"
                :disabled="disableGroup1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="销售订单：" prop="orderNo">
              <el-input
                v-model.trim="searchForm.orderNo"
                placeholder="请输入"
                clearable
                :disabled="disableOrderNo"
                @change="changeOrderNo"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户：" prop="customerNo">
              <el-select
                v-model="searchForm.customerNo"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="customerNoLoading"
                clearable
                @change="changeCustomerNo"
                :disabled="disableCustomer"
              >
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="text-align: center">
            <el-button icon="el-icon-refresh-left" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="展示安全库存:" prop="needSafeStock">
              <el-select
                clearable
                v-model="searchForm.needSafeStock"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in needSafeStockOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="content">
      <el-row type="flex" justify="space-between">
        <el-col :span="6"><h3>商品供需明细表</h3></el-col>
        <el-col :span="18" style="text-align: right">
          <el-switch
            v-model="omsOrder"
            active-text="OMS订单号"
            inactive-text="SAP订单号"
            :inactive-value="false"
            :active-value="true"
            style="margin-right: 20px"
            @change="changeSummary"
          />

          <el-switch
            v-model="summary"
            active-text="汇总"
            inactive-text="明细"
            @change="changeSummary"
          />
          <el-button
            type="primary"
            style="margin-left: 20px"
            :disabled="allocationList.length === 0 || omsOrder"
            @click="handleAllocation"
          >
            匀货申请
          </el-button>
          <a
            class="el-button el-button--primary el-button--small"
            type="primary"
            target="_blank"
            v-show="this.searchForm.skuNos"
            :href="exportLink"
            >导出</a
          >
          <el-button type="primary" plain @click="showDialog">
            批量导出
          </el-button>
        </el-col>
      </el-row>
      <el-table
        ref="tableRef"
        v-loading="loading.tableLoading"
        :data="tableData"
        :span-method="arraySpanMethod"
        border
        height="500"
        @selection-change="handleSelectionChange"
        v-show="!summary"
      >
        <el-table-column
          fixed="left"
          type="selection"
          width="55"
          :selectable="checkSelectable"
        >
        </el-table-column>
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth || 0"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="column.format">
              <span v-if="column.format(scope.row).type">
                <span
                  class="column-dn"
                  @click="navToDnOrSo(column.format(scope.row), scope.row)"
                  >{{ column.format(scope.row).no }}</span
                >
                {{ "/" + column.format(scope.row).res }}
              </span>
              <!-- <span v-else-if="column.format(scope.row).type == 'dn'">
              <span class="column-dn" @click="navToDn(column.format(scope.row).no)">{{column.format(scope.row).no}}</span>
              {{'/' + column.format(scope.row).res}}
            </span> -->
              <span v-else>{{ column.format(scope.row).res }}</span>
            </span>
            <span
              v-else-if="
                column.colorRed &&
                column.colorRed(scope.row[column.prop]) == 'red'
              "
              style="color: red"
              >{{ scope.row[column.prop] }}</span
            >
            <span v-else>{{ scope.row[column.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-table
        ref="tableRef"
        v-loading="loading.tableLoading"
        :data="tableData"
        border
        height="500"
        v-show="summary"
      >
        <el-table-column
          v-for="column in summaryColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth || 0"
          align="center"
        >
          <template slot-scope="{ row }">
            <span v-if="column.format">
              {{ column.format(row) }}
            </span>
            <span v-else>{{ row[column.prop] }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      title="匀货详情"
      :visible.sync="allocationVisible"
      :destroy-on-close="true"
      width="1100px"
    >
      <AllocationDetail
        :data="allocationData"
        @close="allocationVisible = false"
      />
    </el-dialog>

    <el-dialog
      :visible.sync="dialogVisible"
      width="80%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <el-form
        ref="dialogForm"
        :model="dialogForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="16">
            <el-form-item label="SKU：" prop="skuNos">
              <el-input
                v-model="dialogForm.skuNos"
                placeholder="多个sku用逗号隔开, 最多输入500个SKU"
                clearable
                @change="changeOrderNoOpp2"
                :disabled="disableGroup2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="库存地点编号：" prop="positionNos">
              <el-select
                v-model="dialogForm.positionNos"
                placeholder="支持多值"
                clearable
                filterable
                multiple
                collapse-tags
                @change="changePositionNo2"
                :disabled="disablePosition2"
              >
                <el-option
                  v-for="item in setwarehouseList"
                  :key="item.factoryCode+item.warehouseLocationCode"
                  :label="`${item.warehouseLocationCode} ${item.warehouseLocationName}`"
                  :value="item.warehouseLocationCode"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="工厂编号：" prop="factoryNo">
              <el-input
                v-model.trim="dialogForm.factoryNo"
                placeholder="请输入"
                clearable
                @change="changeOrderNoOpp2"
                :disabled="disableGroup2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="销售订单：" prop="orderNo">
              <el-input
                v-model.trim="dialogForm.orderNo"
                placeholder="请输入"
                clearable
                :disabled="disableOrderNo2"
                @change="changeOrderNo2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户：" prop="customerNo">
              <el-select
                v-model="dialogForm.customerNo"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="customerNoLoading"
                clearable
                @change="changeCustomerNo2"
                :disabled="disableCustomer2"
              >
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="展示安全库存:" prop="needSafeStock">
              <el-select
                clearable
                v-model="dialogForm.needSafeStock"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in needSafeStockOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            :style="{ display: 'flex', justifyContent: 'flex-end' }"
          >
            <el-switch
              v-model="sapOrder"
              active-text="OMS订单号"
              inactive-text="SAP订单号"
              :inactive-value="false"
              :active-value="true"
              style="margin-right: 20px"
            />

            <el-switch
              v-model="detail"
              active-text="汇总"
              inactive-text="明细"
            />
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="batchExport">后台导出</el-button>
        <el-button @click="cancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import uniqBy from 'lodash/uniqBy'
import AllocationDetail from '@/pages/supplyDetail/components/detail'
import { goodsCustomerListSearch } from '@/api/insteadOrder'
import { getSupplyDetailList, getBatchExport, getWarehouseList } from '@/api/supplyDetail.js'
import { columns, summaryColumns } from './columns'
import {
  // throttle,
  safeRun,
  trimParams
} from '@/utils/index'
import endsWith from 'lodash/endsWith'
import { isEmpty } from 'lodash'
const needSafeStockOption = [
  { label: '是', value: true },
  { label: '否', value: false }
]
export default {
  name: 'supply-detail',
  components: {
    AllocationDetail
  },
  // mounted () {
  //   this.adjustTableHeight()
  //   this.innerAdjustTableHeight = throttle(this.adjustTableHeight)
  //   window.onresize = this.innerAdjustTableHeight
  //   window.addEventListener('resize', this.innerAdjustTableHeight)
  // },
  // unmounted () {
  //   window.removeEventListener('resize', this.innerAdjustTableHeight)
  // },
  data () {
    return {
      // innerAdjustTableHeight: null,
      searchForm: {
        skuNos: '',
        positionNo: '',
        factoryNo: '',
        orderNo: '',
        needSafeStock: true
      },
      dialogForm: {},
      tableData: [],
      columns,
      summaryColumns,
      needSafeStockOption,
      loading: {
        searchLoading: false,
        tableLoading: false
      },
      listQueryInfo: {
        total: 0,
        pageNo: 0,
        pageSize: 10
      },
      spanArr: [],
      customerNoLoading: false,
      customerNoOptions: [],
      position: '',
      allocationList: [],
      allocationData: {},
      omsOrder: false,
      sapOrder: false,
      summary: false,
      detail: false,
      allocationVisible: false,
      // sku+工厂
      disableGroup1: false,
      disablePosition: false,
      disableCustomer: false,
      disableOrderNo: false,
      dialogVisible: false,
      disableGroup2: false,
      disablePosition2: false,
      disableCustomer2: false,
      disableOrderNo2: false,
      warehouseList: [],
      setwarehouseList: []
    }
  },
  async created () {
    const query = JSON.parse(localStorage.getItem('/inventoryInquiry/supplyDetail')) || {}
    this.searchForm = {
      ...this.searchForm,
      ...query,
      needSafeStock: query?.needSafeStock ? query.needSafeStock === 'true' : true,
      orderNo: query.orderNo || ''
    }
    this.summary = query.summary === 'true'
    const res = await getWarehouseList()
    this.warehouseList = res.data
    this.setObjectArray()
    if (!isEmpty(query)) {
      this.getSupplyDetailList()
    }

    // 使用初始查询条件查询一次后需要清除该初始条件
    // setItem 参见 router/index.js > beforeEach
    localStorage.removeItem('/inventoryInquiry/supplyDetail')
  },
  computed: {
    exportLink () {
      let filters = []
      for (const key in this.searchForm) {
        if (this.searchForm[key]) {
          filters.push(`${key}=${this.searchForm[key]}`)
        }
      }
      filters.push(`summary=${this.summary}`, `omsOrder=${this.omsOrder}`)

      return `/api-opc-atp/boss/report/export?${filters.join('&')}`
    }
  },
  methods: {
    setObjectArray () {
      let obj = {}
      for (let i = 0; i < this.warehouseList.length; i++) {
        if (!obj[this.warehouseList[i].warehouseLocationCode]) {
          this.setwarehouseList.push(this.warehouseList[i])
          obj[this.warehouseList[i].warehouseLocationCode] = true
        }
      }
    },
    setOrderNoDisableStatus2 () {
      if (
        this.dialogForm.skuNos ||
        this.dialogForm.positionNos.length ||
        this.dialogForm.customerNo ||
        this.dialogForm.factoryNo
      ) {
        this.disableOrderNo2 = true
      } else {
        this.disableOrderNo2 = false
      }
    },
    changeOrderNo2 (val) {
      if (val) {
        ['skuNos', 'factoryNo', 'customerNo'].forEach((attr) => {
          this.dialogForm[attr] = ''
        })
        this.dialogForm.positionNos = []
        this.disableGroup2 = true
        this.disablePosition2 = true
        this.disableCustomer2 = true
      } else {
        this.disableGroup2 = false
        this.disablePosition2 = false
        this.disableCustomer2 = false
      }
    },
    changeOrderNoOpp2 (val) {
      if (val) {
        this.dialogForm.orderNo = ''
        this.disableOrderNo2 = true
      } else {
        this.setOrderNoDisableStatus2()
        this.disableGroup2 = false
      }
    },
    changePositionNo2 (val) {
      if (val.length) {
        this.dialogForm.orderNo = ''
        this.disableOrderNo2 = true
        this.dialogForm.customerNo = ''
        this.disableCustomer2 = true
      } else {
        this.setOrderNoDisableStatus2()
        this.disableCustomer2 = false
      }
    },
    changeCustomerNo2 (val) {
      if (val) {
        this.dialogForm.orderNo = ''
        this.dialogForm.positionNos = []
        this.disableOrderNo2 = true
        this.disablePosition2 = true
      } else {
        this.disablePosition2 = false
        this.setOrderNoDisableStatus2()
      }
    },
    showDialog () {
      this.dialogVisible = true
      this.dialogForm = { ...this.searchForm }
      if (this.searchForm.positionNo) {
        this.$set(this.dialogForm, 'positionNos', [this.searchForm.positionNo])
      }
      this.detail = this.summary
      this.sapOrder = this.omsOrder
      this.disableGroup2 = this.disableGroup1
      this.disablePosition2 = this.disablePosition
      this.disableCustomer2 = this.disableCustomer
      this.disableOrderNo2 = this.disableOrderNo
    },
    formatParams (params) {
      let form = { ...params }
      form.skuNos = safeRun(() =>
        form.skuNos.split(/\s+|,|，/).filter((e) => e)
      )
      delete form.positionNo
      return form
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.skuNos.length > 500) {
          ret = false
          this.$message.error('最多支持500个SKU按逗号隔开搜索！')
        }
      })
      return ret
    },
    async batchExport () {
      try {
        let params = this.formatParams(this.dialogForm)
        if (!this.validate(params)) return
        if (Array.isArray(params.skuNos)) {
          params.skuNos = params.skuNos.join(',')
        }
        if (Array.isArray(params.positionNos)) {
          params.positionNos = params.positionNos.join(',')
        }
        if (!params.skuNos) delete params.skuNos
        if (!params.positionNos) delete params.positionNos
        if (!params.factoryNo) delete params.factoryNo
        if (!params.orderNo) delete params.orderNo
        if (!params.customerNo) delete params.customerNo

        params.summary = this.detail
        params.omsOrder = this.sapOrder
        const res = await getBatchExport(params)
        if (res.code === 200) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.cancel()
      } catch (error) {
        console.log(error)
      }
    },
    cancel () {
      this.dialogVisible = false
      this.$refs['dialogForm'].resetFields()
      this.disableCustomer2 = false
      this.disablePosition2 = false
      this.disableGroup2 = false
      this.disableOrderNo2 = false
    },
    checkSelectable (row) {
      return (row.typeId === '4' || row.typeId === '6' || row.typeId === '1') && !endsWith(String(row?.positionNo), '88')
    },
    navToDnOrSo (data, row) {
      const { type, no } = data
      const { soId, soOrderNo } = row
      if (type === 'dn') {
        this.navToDn(no)
      } else if (type === 'so') {
        this.navToSo(no, soId, soOrderNo)
      } else if (type === 'po') {
        this.navToPo(no)
      }
    },
    navToPo (po) {
      this.$router.push(`/orderPurchase/detail/${po}`)
    },
    navToSo (soNo, soId, soOrderNo) {
      this.$router.jumpToSoOrderDetail({
        tagName: `${soNo || ''}订单`,
        query: {
          soNo: soOrderNo,
          sapOrderNo: soNo,
          id: soId,
          refresh: true
        }
      })
    },
    navToDn (dn) {
      this.$router.push({
        path: `/orderDelivery/detail/${dn}`
      })
      // this.$router.push({
      //   path: '/warehousing/detail/' + dn,
      //   query: {
      //     type: 0, //  0 非直发 、1 供应商直发
      //     outboundNo: dn
      //   }
      // })
    },
    // adjustTableHeight () {
    //   try {
    //     const { top } = this.$refs.tableRef.$el.getBoundingClientRect()
    //     const { clientHeight } = document.documentElement || document.body
    //     this.$nextTick(() => {
    //       this.tableHeight = clientHeight - top - 40
    //     })
    //   } catch (err) {
    //     console.log(err)
    //   }
    // },
    handleReset () {
      this.$refs['searchForm'].resetFields()
      this.disableCustomer = false
      this.disablePosition = false
      this.disableGroup1 = false
      this.disableOrderNo = false
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data, idx, prop) {
      this.spanArr[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1)
          this.position = 0
        } else {
          if (
            data[index][prop] === data[index - 1][prop] &&
            data[index].positionNo === data[index - 1].positionNo &&
            data[index].factoryNo === data[index - 1].factoryNo
          ) {
            // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr[idx].push(1)
            this.position = index
          }
        }
      })
      console.log(this.spanArr)
    },
    // 表格单元格合并
    arraySpanMethod ({ row, column, rowIndex, columnIndex }) {
      // console.log(row, column, rowIndex, columnIndex)
      if ([1, 2, 3, 4, 5, 6].indexOf(columnIndex) !== -1) {
        const _row = this.spanArr[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        // console.log('第' + rowIndex + '行', '第' + columnIndex + '列', 'rowspan:' + _row, 'colspan:' + _col)
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    deleteEmptyString (params) {
      for (let param in params) {
        if (!params[param]) {
          delete params[param]
        }
      }
    },
    getSupplyDetailList () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.loading.searchLoading = true
          this.loading.tableLoading = true
          let params = { ...this.searchForm }
          trimParams(params)
          if (params.skuNos && typeof params.skuNos === 'string') {
            params.skuNos = params.skuNos
              .split(/\s+|,|，/)
              .filter((e) => e)
              .join(',')
          }
          if (params.positionNo && typeof params.positionNo === 'string') {
            params.positionNo = params.positionNo.trim()
          }
          params.pageNo = this.listQueryInfo.pageNo
          params.pageSize = this.listQueryInfo.pageSize
          this.deleteEmptyString(params)
          params.summary = this.summary
          params.omsOrder = this.omsOrder
          getSupplyDetailList(params)
            .then(res => {
              console.log(res)
              this.tableData = []
              if (res && res.code === 200 && res.data) {
                this.tableData = res.data
                this.listQueryInfo.total = res.totalCount
                this.getSpanArr(this.tableData, 0, 'sku')
                this.getSpanArr(this.tableData, 1, 'materialDes')
                this.getSpanArr(this.tableData, 2, 'baseUnit')
                this.getSpanArr(this.tableData, 3, 'factoryNo')
                this.getSpanArr(this.tableData, 4, 'factoryName')
                this.getSpanArr(this.tableData, 5, 'positionNo')
                this.getSpanArr(this.tableData, 6, 'positionName')
              }
              if (res && res.code !== 200 && res.msg) {
                this.$alert(res.msg, '错误', {
                  type: 'error'
                })
              }
            })
            .catch(err => {
              this.$message.error(err.msg || err.message || '查询失败！')
              this.tableData = []
            })
            .finally(() => {
              this.loading.searchLoading = false
              this.loading.tableLoading = false
            })
        }
      })
    },
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        goodsCustomerListSearch({
          customer_name: key,
          pageSize: 100
        }).then(res => {
          this.customerNoLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.customerNoOptions = res.data.contents.map(item => {
                return {
                  value: item.customerNo,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    },
    changeSummary () {
      this.getSupplyDetailList()
    },
    handleSelectionChange (val) {
      this.allocationList = val
    },
    formatAllocation (item) {
      const {
        atpNo,
        typeId,
        typeName,
        customerServiceId,
        customerServiceName,
        buyerId,
        buyerName,
        matchNumber
      } = item
      const noList = atpNo.split('/') || []
      const receiptNo = noList[0]
      const receiptItemNo = noList[1]
      const matchNumInt = matchNumber ? Math.abs(matchNumber) : 0
      let result = {
        receiptNo,
        receiptItemNo,
        sourceType: typeId,
        sourceTypeName: typeName,
        adjustNumber: matchNumInt,
        matchNumber: matchNumInt
      }
      if (typeId === '4') {
        result = {
          ...result,
          ownerType: 'PURCHASER',
          ownerUserId: customerServiceId,
          ownerUserName: customerServiceName
        }
      }
      if (typeId === '6') {
        result = {
          ...result,
          ownerType: 'CUSTOMER_SERVICE',
          ownerUserId: buyerId,
          ownerUserName: buyerName
        }
      }
      if (typeId === '1') {
        result = {
          ...result,
          ownerType: 'PRODUCT_GROUP_ASSIGN_USER'
        }
      }
      return result
    },
    setOrderNoDisableStatus () {
      if (
        this.searchForm.skuNos ||
        this.searchForm.positionNo ||
        this.searchForm.customerNo ||
        this.searchForm.factoryNo
      ) {
        this.disableOrderNo = true
      } else {
        this.disableOrderNo = false
      }
    },
    changeOrderNo (val) {
      if (val) {
        ['skuNos', 'positionNo', 'factoryNo', 'customerNo'].forEach((attr) => {
          this.searchForm[attr] = ''
        })
        this.disableGroup1 = true
        this.disablePosition = true
        this.disableCustomer = true
      } else {
        this.disableGroup1 = false
        this.disablePosition = false
        this.disableCustomer = false
      }
    },
    changeOrderNoOpp (val) {
      if (val) {
        this.searchForm.orderNo = ''
        this.disableOrderNo = true
      } else {
        this.setOrderNoDisableStatus()
        this.disableGroup1 = false
      }
    },
    changePositionNo (val) {
      if (val) {
        this.searchForm.orderNo = ''
        this.disableOrderNo = true
        this.searchForm.customerNo = ''
        this.disableCustomer = true
      } else {
        this.setOrderNoDisableStatus()
        this.disableCustomer = false
      }
    },
    changeCustomerNo (val) {
      if (val) {
        this.searchForm.orderNo = ''
        this.searchForm.positionNo = ''
        this.disableOrderNo = true
        this.disablePosition = true
      } else {
        this.disablePosition = false
        this.setOrderNoDisableStatus()
      }
    },
    handleAllocation () {
      let msg = ''
      const uniqArr = uniqBy(
        this.allocationList,
        item => `${item.sku}_${item.positionNo}_${item.factoryNo}`
      )
      if (uniqArr.length !== 1) {
        msg = '同sku ，同工厂，同库存地点数据才可匀货'
      }
      if (!msg) {
        // 支持库存
        const hasStock = this.allocationList.some(
          item => item.matchType === '在库' || item.typeId === '1'
        )
        if (!hasStock) {
          msg = '未勾选匀出单据'
        }
      }
      if (!msg) {
        const inArr = this.allocationList.filter(
          item => ['在途', '未占'].indexOf(item.matchType) > -1
        )
        if (!inArr || !inArr.length) {
          msg = '未勾选匀入单据'
        } else {
          let arr = [];
          inArr.forEach((a) => {
            if (arr.includes(a.atpNo)) {
              msg = `匀入订单@订单号${a.atpNo.split('/')[0]}行号${a.atpNo.split('/')[1]} 重复选择`
            } else {
              arr.push(a.atpNo)
            }
          })
        }
      }
      if (!msg) {
        let inArr = []
        let outArr = []
        this.allocationList.forEach((item) => {
          let arr = item.atpNo.split('/')
          if (['在途', '未占'].indexOf(item.matchType) > -1) {
            inArr.push(`${arr[0]}/${arr[1]}`)
          }
          if (item.matchType === '在库' || item.typeId === '1') {
            outArr.push(`${arr[0]}/${arr[1]}`)
          }
        })
        inArr.forEach((a) => {
          if (outArr.includes(a)) {
            msg = '单据编号行号相同不可匀货!'
          }
        })
      }
      if (msg) {
        this.$alert(msg, {
          type: 'error'
        })
      } else {
        let adjustNum = 0;
        let outItems = this.allocationList
          .filter(item => item.matchType === '在库' || item.typeId === '1') // 支持库存
          .map(item => {
            item = {
              ...item,
              ...this.formatAllocation(item),
              adjustInventoryType: 'OUT',
              adjustInventoryTypeName: '匀出',
              adjustStatus: 'INIT'
            }
            // adjustNumber为0时在申请中会变为1，所以在计算默认时也为1
            adjustNum += (item.adjustNumber || 1)
            return item
          })
        let inItems = this.allocationList
          .filter(item => ['在途', '未占'].indexOf(item.matchType) > -1)
          .map(item => {
            item = {
              ...item,
              unOccupyStockNum: Number(item.unOccupyStockNum),
              ...this.formatAllocation(item),
              adjustInventoryType: 'IN',
              adjustInventoryTypeName: '匀入'
            }
            if (adjustNum >= Number(item.unOccupyStockNum)) {
              item.adjustNumber = Number(item.unOccupyStockNum)
            } else {
              item.adjustNumber = adjustNum > 0 ? adjustNum : 0
            }
            adjustNum = adjustNum - Number(item.unOccupyStockNum)
            return item
          })
        const {
          baseUnit,
          factoryName,
          factoryNo,
          materialDes,
          positionName,
          positionNo,
          sku
        } = this.allocationList[0]
        this.allocationData = {
          baseUnit,
          factoryName,
          factoryNo,
          materialDes,
          positionName,
          positionNo,
          sku,
          outItems,
          inItems
        }
        this.allocationVisible = true
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.supply-detail {
  margin-top: 10px;
  span.column-dn {
    color: steelblue;
    cursor: pointer;
  }
}
.content {
  width: 95%;
  margin: auto;
  h3 {
    margin: 10px;
  }
}
.el-select {
  width: 100%;
}
</style>
