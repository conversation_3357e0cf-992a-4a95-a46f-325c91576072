<template>
  <div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      height="500"
      :span-method="objectSpanMethod"
    >
      <el-table-column label="主管" min-width="100px" align="center" prop="purchaseManagerName" />
      <el-table-column label="物料组" min-width="100px" align="center" prop="productGroupName" />
      <el-table-column label="Y" min-width="100px" align="center">
        <el-table-column label="采购订单" min-width="100px" align="center" prop="orderNumber" />
        <el-table-column label="转储订单" min-width="100px" align="center" prop="transferNumber" />
        <el-table-column label="合计" min-width="100px" align="center" prop="yesTotalNumber" />
      </el-table-column>
      <el-table-column label="N" min-width="100px" align="center" prop="noNumber" />
      <el-table-column label="合计" min-width="100px" align="center" prop="totalNumber" />
      <el-table-column label="下单异常率" min-width="100px" align="center" prop="abnormalRate" />
    </el-table>
  </div>
</template>

<script>
import { pendingOrderReportProductGroup } from '@/api/purchaseList.js'
export default {
  // 主管对应物料组
  props: {
    batchDate: {
      type: [String, Array],
      default: () => ([])
    },
    queryTime: {
      type: [String, Number, Array],
      default: ''
    }
  },
  data () {
    return {
      list: [],
      listLoading: false,
      searchForm: {
        batchDate: '',
        endBatchDate: ''
      }
    }
  },
  watch: {
    // batchDate: {
    //   handler(newValue, oldValue) {
    //     if (newValue !== oldValue) {
    //       this.searchForm.batchDate = newValue
    //       this.handleFilter()
    //     }
    //   },
    //   immediate: true
    // },
    queryTime (newValue, oldValue) {
      if (newValue !== oldValue && this.batchDate && Array.isArray(this.batchDate)) {
        const [ batchDate, endBatchDate ] = this.batchDate
        console.log(this.batchDate, batchDate, endBatchDate)
        this.searchForm.batchDate = batchDate
        this.searchForm.endBatchDate = endBatchDate
        this.handleFilter()
      }
    }
  },
  methods: {
    getList () {
      this.listLoading = true
      pendingOrderReportProductGroup(this.searchForm)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list = response.data
          } else {
            this.$notify.error(response.msg)
            // 报错时清空数据
            this.list = []
            this.total = 0
            // this.hasDocCnt = 0
            // this.noDocCnt = 0
          }
          this.listLoading = false
        })
        .catch(e => {
          this.listLoading = false
        })
    },
    cellRowMerge (rowIndex, columnIndex) {
      const currentCell = this.list[rowIndex][columnIndex]
      let prevRowCell = null
      if (rowIndex > 0) {
        prevRowCell = this.list[rowIndex - 1][columnIndex]
      }
      // 默认被合并
      let rowspan = 0
      let colspan = 0
      // 首行或与上一行不相同的单元格，作为起始计算值，不传空
      if (rowIndex === 0 || currentCell !== prevRowCell) {
        colspan = 1
        rowspan = 1
        for (let i = rowIndex; i < this.list.length - 1; i++) {
          const nextRowCell = this.list[i + 1][columnIndex]
          if (currentCell === nextRowCell) {
            rowspan++
          } else {
            break
          }
        }
      }
      return {
        rowspan,
        colspan
      }
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      // 只合并第0列
      if (columnIndex === 0) {
        return this.cellRowMerge(rowIndex, column.property)
      }
    },
    handleFilter () {
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
