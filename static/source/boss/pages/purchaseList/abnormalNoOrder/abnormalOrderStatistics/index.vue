<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="150px"
        inline
      >
        <el-form-item label="选择查看报表日期：" prop="batchDate">
          <el-date-picker
            v-model="searchForm.batchDate"
            align="right"
            type="daterange"
            range-separator="至"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            :picker-options="pickerOptions"
          />
        </el-form-item>
        <el-form-item label>
          <el-button type="primary" icon="el-icon-search" @click="handleFilter"
            >查询</el-button
          >
          <el-button v-if="havecgb" icon="el-icon-download" @click="cgzDownload"
            >下载采购组报表</el-button
          >
          <el-button v-if="havewlz" icon="el-icon-download" @click="wlzDownload"
            >下载物料组报表</el-button
          >
          <el-button v-if="havewxd" icon="el-icon-download" @click="wxdDownload"
            >下载采购组明细</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane v-if="havecgb" label name="cgz">
        <span slot="label">主管对应采购组</span>
        <PurchasingGroupReport
          :batch-date="batchDate"
          :query-time="queryTime"
        />
      </el-tab-pane>
      <el-tab-pane v-if="havewlz" name="wlz">
        <span slot="label">主管对应物料组</span>
        <MaterialsGroupReport :batch-date="batchDate" :query-time="queryTime" />
      </el-tab-pane>
      <el-tab-pane v-if="havewxd" name="wxd">
        <span slot="label">未下单明细</span>
        <OutstandingDetails :batch-date="batchDate" :query-time="queryTime" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MaterialsGroupReport from './MaterialsGroupReport'
import OutstandingDetails from './OutstandingDetails'
import PurchasingGroupReport from './PurchasingGroupReport'
// import { changeQueryByTab } from '@/utils/index.js'
import { dateFormat } from '@/filters/index.js'
import { cgzDownload, wlzDownload, wxdDownload } from '@/api/purchaseList.js'
export default {
  components: {
    PurchasingGroupReport,
    OutstandingDetails,
    MaterialsGroupReport
  },
  data () {
    return {
      pickerOptions: {
        // 禁用明天和之后的日期
        disabledDate (time) {
          return (
            time.getTime() > new Date(new Date().toLocaleDateString()).getTime()
          )
        }
      },
      havecgb: true,
      havewlz: true,
      havewxd: true,
      batchDate: '',
      activeName: 'cgz', // cgz wlz wxd
      searchForm: {
        batchDate: dateFormat('yyyy-MM-dd', new Date())
      },
      queryTime: '',
      rules: {
        batchDate: [
          {
            // type: 'date',
            required: true,
            message: '请选择查看报表日期',
            trigger: 'change'
          }
        ]
      }
    }
  },
  watch: {
    // activeName: {
    //   handler (newName, oldName) {
    //     this.$route.query.grTab = newName
    //     // changeQueryByTab('grTab', newName)
    //   },
    //   immediate: true
    // }
  },
  mounted () {
    this.initData()
    this.handleFilter()
  },
  methods: {
    handleFilter () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.queryTime = Date.now()
          this.batchDate = this.searchForm.batchDate
        }
      })
    },
    initData () {
      const grTab = this.$route.query.grTab || 'cgz'
      this.activeName = grTab
    },
    // 采购组报表下载
    cgzDownload () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          cgzDownload(this.searchForm.batchDate)
        }
      })
    },
    // 物料组报表下载
    wlzDownload () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          wlzDownload(this.searchForm.batchDate)
        }
      })
    },
    // 未下单明细下载
    wxdDownload () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          wxdDownload(this.searchForm.batchDate)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
