<template>
  <div>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      height="500"
      :span-method="objectSpanMethod"
    >
      <el-table-column label="主管" min-width="100px" align="center" prop="purchaseManagerName" />
      <el-table-column label="采购组描述" min-width="100px" align="center" prop="purchaseName" />
      <el-table-column label="Y" min-width="100px" align="center">
        <el-table-column label="采购订单" min-width="100px" align="center" prop="orderNumber" />
        <el-table-column label="转储订单" min-width="100px" align="center" prop="transferNumber" />
        <el-table-column label="合计" min-width="100px" align="center" prop="yesTotalNumber" />
      </el-table-column>
      <el-table-column label="N" min-width="100px" align="center" prop="noNumber" />
      <el-table-column label="合计" min-width="100px" align="center" prop="totalNumber" />
      <el-table-column label="下单异常率" min-width="100px" align="center" prop="abnormalRate" />
    </el-table>
  </div>
</template>

<script>
import { pendingOrderReportPurchaseGroup } from '@/api/purchaseList.js'
export default {
  // 主管对应采购组
  props: {
    batchDate: {
      type: [String, Array],
      default: () => ([])
    },
    queryTime: {
      type: [String, Number, Array],
      default: ''
    }
  },
  data () {
    return {
      list: [],
      listLoading: false,
      searchForm: {
        batchDate: '',
        endBatchDate: ''
      }
    }
  },
  watch: {
    // batchDate: {
    //   handler(newValue, oldValue) {
    //     if (newValue !== oldValue) {
    //       this.searchForm.batchDate = newValue
    //       this.handleFilter()
    //     }
    //   },
    //   immediate: true
    // },
    queryTime (newValue, oldValue) {
      if (newValue !== oldValue && this.batchDate && Array.isArray(this.batchDate)) {
        const [ batchDate, endBatchDate ] = this.batchDate
        this.searchForm.batchDate = batchDate
        this.searchForm.endBatchDate = endBatchDate
        this.handleFilter()
      }
    }
  },
  methods: {
    getList () {
      this.listLoading = true
      pendingOrderReportPurchaseGroup(this.searchForm)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list = this.formatTableData(response.data)
          } else {
            this.$notify.error(response.msg)
            // 报错时清空数据
            this.list = []
            this.total = 0
            // this.hasDocCnt = 0
            // this.noDocCnt = 0
          }
          this.listLoading = false
        })
        .catch(e => {
          this.listLoading = false
        })
    },
    rowSum (a, b) {
      const newObj = { ...a }
      if (b) {
        newObj.orderNumber = a.orderNumber + b.orderNumber
        newObj.transferNumber = a.transferNumber + b.transferNumber
        newObj.yesTotalNumber = a.yesTotalNumber + b.yesTotalNumber
        newObj.noNumber = a.noNumber + b.noNumber
        newObj.totalNumber = a.totalNumber + b.totalNumber
      }
      return newObj
    },
    SetAbnormalRate (row) {
      row.abnormalRate =
        ((row.noNumber * 10000) / row.totalNumber).toFixed(0) / 100 + '%'
    },
    formatTableData (data) {
      if (data && data.length > 0) {
        // 1. 遍历表格，2. 根据主管分别汇总，3. 如果主管多于1个，则最后累加统计所有值
        // 4. 只有一行时不进行任何汇总

        let masterCount = 0 // 主管数量统计
        const firstRow = data[0] // 首行
        let currentMaster = firstRow.purchaseManagerName // 当前值
        let masterSumaryObj = { ...firstRow } // 初始主管汇总
        let totalObj = {
          ...firstRow
        } // 初始合计
        const newList = [firstRow] // 新列表
        newList[0].dataType = 'data' // data：数据，summary：master汇总，total：合计（大汇总）
        // 注意索引，从第二个到结束位置+1（结束后自动增加主管汇总）
        for (let i = 1; i <= data.length; i++) {
          const row = data[i]
          // 判断该行主管是否是当前主管，或者后面已经没有主管了
          if (!row || row.purchaseManagerName !== currentMaster) {
            // 新主管或者没有主管了，则1.额外添加一行主管汇总，2.当前主管更新
            const sumaryRow = {
              ...masterSumaryObj
            }
            sumaryRow.dataType = 'summary'
            sumaryRow.purchaseManagerName = currentMaster + '汇总'
            this.SetAbnormalRate(sumaryRow)
            // 添加一行主管汇总
            newList.push(sumaryRow)
            // 主管数据+1
            masterCount++
            if (row) {
              // 更新当前主管名称
              currentMaster = row.purchaseManagerName
              // 主管累加从新计数
              masterSumaryObj = { ...row }
            }
          } else {
            // 旧主管，则汇总累加
            masterSumaryObj = this.rowSum(masterSumaryObj, row)
          }
          if (row) {
            row.dataType = 'data'
            // 不管新旧，合计值累加
            totalObj = this.rowSum(totalObj, row)
            // 当前行推入
            newList.push({ ...row })
          }
        }
        if (masterCount === 1) {
          // 只有一条，不汇总
        } else {
          // 推入合计行
          const totalRow = { ...totalObj }
          totalRow.dataType = 'total'
          totalRow.purchaseManagerName = '合计'
          this.SetAbnormalRate(totalRow)
          newList.push(totalRow)
        }
        return newList
      } else {
        return []
      }
    },
    cellRowMerge (rowIndex, columnIndex) {
      const currentCell = this.list[rowIndex][columnIndex]
      let prevRowCell = null
      if (rowIndex > 0) {
        prevRowCell = this.list[rowIndex - 1][columnIndex]
      }
      // 默认被合并
      let rowspan = 0
      let colspan = 0
      // 首行或与上一行不相同的单元格，作为起始计算值，不传空
      if (rowIndex === 0 || currentCell !== prevRowCell) {
        colspan = 1
        rowspan = 1
        for (let i = rowIndex; i < this.list.length - 1; i++) {
          const nextRowCell = this.list[i + 1][columnIndex]
          if (currentCell === nextRowCell) {
            rowspan++
          } else {
            break
          }
        }
      }
      return {
        rowspan,
        colspan
      }
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      const rowType = row.dataType
      // 只合并第0列
      if (rowType === 'data') {
        if (columnIndex === 0) {
          return this.cellRowMerge(rowIndex, column.property)
        }
      } else if (rowType === 'summary') {
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2
          }
        } else if (columnIndex === 1) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      } else if (rowType === 'total') {
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: 2
          }
        } else if (columnIndex === 1) {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      return {
        rowspan: 1,
        colspan: 1
      }
    },
    handleFilter () {
      this.searchForm.page = 1
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped></style>
