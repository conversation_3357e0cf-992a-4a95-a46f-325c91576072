<template>
  <div>
    <el-table v-loading="listLoading" :data="list" border fit highlight-current-row height="500">
<el-table-column
        label="日期"
        min-width="100px"
        align="center"
        prop="batchDate"
      />
      <el-table-column
        label="未下单天数"
        min-width="100px"
        align="center"
        prop="delayDays"
      />
      <el-table-column label="汇总原因" min-width="200px" align="center">
        <template slot-scope="{ row }">
          {{ getReasonTypeOneText(row.reasonTypeOne) }}
        </template>
      </el-table-column>
      <el-table-column label="汇总原因明细" min-width="200px" align="center">
        <template slot-scope="{ row }">
          {{ getReasonTypeTwoText(row.reasonTypeOne, row.reasonTypeTwo) }}
        </template>
      </el-table-column>
      <el-table-column label="未下单原因" width="200px" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.reason }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="物料编码"
        min-width="100px"
        align="center"
        prop="productNumber"
      />
      <el-table-column label="物料描述" width="120px" align="center">
        <template slot-scope="{ row }">
          <el-tooltip class="item" effect="dark" :content="row.productName">
            <span class="max-row-2">{{ row.productName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="库存地点"
        width="80px"
        align="center"
        prop="location"
      />
      <el-table-column
        label="库存地点描述"
        width="110px"
        align="center"
        prop="locationName"
      />
      <el-table-column
        label="物料组"
        min-width="100px"
        align="center"
        prop="productGroup"
      />
      <el-table-column
        label="物料组描述"
        min-width="100px"
        align="center"
        prop="productGroupName"
      />
      <el-table-column
        label="单据类型"
        min-width="100px"
        align="center"
        prop="sapOrderType"
      />

      <el-table-column
        label="单据号"
        width="80px"
        align="center"
        prop="sapOrderNo"
      />
      <el-table-column
        label="单据日期"
        width="100px"
        align="center"
        prop="orderDate"
      />
      <el-table-column
        label="到期日期"
        width="100px"
        align="center"
        prop="expiryDate"
      />
      <el-table-column
        label="可用数量"
        width="100px"
        align="center"
        prop="availableQuantity"
      />
      <el-table-column
        label="客户代码"
        width="100px"
        align="center"
        prop="customerNumber"
      />
      <el-table-column
        label="客户名称"
        width="100px"
        align="center"
        prop="customerName"
      />
      <el-table-column
        label="客服"
        width="100px"
        align="center"
        prop="customerServiceName"
      />
      <el-table-column
        label="跟单客服"
        width="100px"
        align="center"
        prop="followCustomerServiceName"
      />
      <el-table-column
        label="销售"
        width="100px"
        align="center"
        prop="sellerName"
      />
      <el-table-column
        label="EVM运营专员"
        width="100px"
        align="center"
        prop="evmBusinessName"
      />
      <el-table-column
        label="采购员编码"
        width="100px"
        align="center"
        prop="purchaseNumber"
      />
      <el-table-column
        label="采购员描述"
        width="100px"
        align="center"
        prop="purchaseName"
      />
      <el-table-column
        label="商品经理名称"
        width="100px"
        align="center"
        prop="productManagerName"
      />
      <el-table-column label="填写状态" width="100px" align="center">
        <template slot-scope="{ row }">
          {{
            row.statusId | statusTextById
          }}
        </template>
      </el-table-column>
      <el-table-column label="已发邮件" width="100px" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.sendStatusId == 1" style="color:#13ce66;">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="searchForm.current"
      :limit.sync="searchForm.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {
  pendingOrderReportPo,
  pendingNoOrderReason
} from '@/api/purchaseList.js'
import {
  getReasonTypeOneList,
  getReasonTypeOneText,
  getReasonTypeTwoText,
  getReasonTypeTwoList
} from '@/filters/index.js'
const statusOption = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '已填写',
    value: '1'
  },
  {
    label: '未填写',
    value: '0'
  }
]
export default {
  // 未下单明细
  filters: {
    statusTextById (val) {
      return statusOption.find(item => {
        return item.value === val + ''
      }).label
    }
  },
  components: {
    Pagination
  },
  props: {
    batchDate: {
      type: [String, Array],
      default: () => ([])
    },
    queryTime: {
      type: [String, Number, Array],
      default: ''
    }
  },
  data () {
    return {
      list: [],
      total: 0,
      reasonOptions: [],
      listLoading: false,
      searchForm: {
        statusId: '',
        batchDate: '',
        endBatchDate: '',
        current: 1,
        pageSize: 10
      }
    }
  },
  computed: {
    reasonTypeOneList () {
      return getReasonTypeOneList(this.reasonOptions)
    }
  },
  watch: {
    // batchDate: {
    //   handler(newValue, oldValue) {
    //     if (newValue !== oldValue) {
    //       this.searchForm.batchDate = newValue
    //       this.handleFilter()
    //     }
    //   },
    //   immediate: true
    // },
    queryTime (newValue, oldValue) {
      if (newValue !== oldValue && this.batchDate && Array.isArray(this.batchDate)) {
        const [ batchDate, endBatchDate ] = this.batchDate
        this.searchForm.batchDate = batchDate
        this.searchForm.endBatchDate = endBatchDate
        this.handleFilter()
      }
    }
  },
  created () {
    // 初始化未下单原因
    pendingNoOrderReason().then(data => {
      this.reasonOptions = data
      // this.handleFilter()
    })
  },
  methods: {
    getReasonTypeOneText (reasonOne) {
      return getReasonTypeOneText(this.reasonTypeOneList, reasonOne)
    },
    getReasonTypeTwoText (reasonOne, reasonTwo) {
      return getReasonTypeTwoText(
        this.getReasonTypeTwoList(reasonOne),
        reasonTwo
      )
    },
    getReasonTypeTwoList (reasonOne) {
      return getReasonTypeTwoList(this.reasonOptions, reasonOne)
    },
    getList () {
      this.listLoading = true
      const param = {
        ...this.searchForm
      }
      pendingOrderReportPo(param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list = response.data
            this.total = response.totalCount || 0
            // this.hasDocCnt = response.data.hasDocCnt || 0
            // this.noDocCnt = response.data.noDocCnt || 0
          } else {
            this.$notify.error(response.msg)
            // 报错时清空数据
            this.list = []
            this.total = 0
            // this.hasDocCnt = 0
            // this.noDocCnt = 0
          }
          this.listLoading = false
        })
        .catch(e => {
          this.listLoading = false
        })
    },
    handleFilter () {
      this.searchForm.page = 1
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
