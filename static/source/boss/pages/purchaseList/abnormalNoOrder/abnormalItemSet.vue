<template>
  <div class="abnormal-order-set-dialog">
    <el-form ref="dialogForm" :rules="rules" :model="data" label-width="150px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="未下单天数" prop="delayDays">
            <el-input v-model.number="data.delayDays" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="未下单原因" prop="reason">
            <el-input
              v-model="data.reason"
              type="textarea"
              :maxlength="255"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="汇总原因" prop="reasonTypeOne">
            <el-select
              :value="data.reasonTypeOne || ''"
              placeholder="请选择"
              @change="
                newValue => {
                  rowReasonTypeOneChange(newValue)
                }
              "
            >
              <el-option
                v-for="item in reasonTypeOneList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="汇总原因明细" prop="reasonTypeTwo">
            <el-select
              :value="data.reasonTypeTwo || ''"
              placeholder="请选择"
              @change="
                newValue => {
                  rowReasonTypeTwoChange(newValue)
                }
              "
            >
              <el-option
                v-for="item in getReasonTypeTwoList(data.reasonTypeOne)"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="物料编码" prop="productNumber">
            <el-input v-model="data.productNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物料描述" prop="productName">
            <el-input v-model="data.productName" type="textarea" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="库存地点" prop="location">
            <el-input v-model="data.location" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="库存地点描述" prop="locationName">
            <el-input v-model="data.locationName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="物料组" prop="productGroup">
            <el-input v-model.number="data.productGroup" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物料组描述" prop="productGroupName">
            <el-input v-model.number="data.productGroupName" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="单据类型" prop="sapOrderType">
            <el-input v-model="data.sapOrderType" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单据号" prop="sapOrderNo">
            <el-input v-model="data.sapOrderNo" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="单据日期" prop="orderDate">
            <el-date-picker
              v-model="data.orderDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="到期日期" prop="expiryDate">
            <el-date-picker
              v-model="data.expiryDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="可用数量" prop="availableQuantity">
            <el-input v-model="data.availableQuantity" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户代码" prop="customerNumber">
            <el-input v-model="data.customerNumber" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="客户名称" prop="customerName">
            <el-input v-model="data.customerName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品经理名称" prop="productManagerName">
            <el-input v-model="data.productManagerName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="客服" prop="customerServiceName">
            <el-input v-model="data.customerServiceName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="跟单客服" prop="followCustomerServiceName">
            <el-input v-model="data.followCustomerServiceName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="销售" prop="sellerName">
            <el-input v-model="data.sellerName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="EVM运营专员" prop="evmBusinessName">
            <el-input v-model="data.evmBusinessName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item label="采购员编码" prop="purchaseNumber">
            <el-input v-model="data.purchaseNumber" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采购员描述" prop="purchaseName">
            <el-input v-model="data.purchaseName" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col>
          <!-- <el-form-item label="填写状态" prop="sapAddDate"></el-form-item> -->
        </el-col>
      </el-row>
      <el-form-item label>
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="ok"
          >确定</el-button
        >
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import {
  pendingNoOrderReason,
  pendingOrderAdd,
  pendingOrderEdit
} from '@/api/purchaseList.js'
import {
  getReasonTypeOneList,
  getReasonTypeOneText,
  getReasonTypeTwoText,
  getReasonTypeTwoList
} from '@/filters/index.js'
export default {
  props: {
    editObj: {
      type: Object,
      default: null
    },

    defaultTime: {
      type: String,
      default: ''
    }
  },

  data () {
    return {
      data: {},
      rules: {
        // delayDays: [
        //   {
        //     required: true,
        //     message: '请填写未下单天数',
        //     trigger: 'change'
        //   },
        //   {
        //     validator: (rule, value, callback) => {
        //       setTimeout(() => {
        //         if (value === '' || isNaN(value)) {
        //           callback(new Error('请输入数字值'))
        //         } else {
        //           callback()
        //         }
        //       }, 100)
        //     },
        //     trigger: 'blur'
        //   }
        // ],
        reasonTypeOne: [
          {
            required: true,
            validator: (rule, value, callback) => {
              setTimeout(() => {
                if (
                  !Number.isInteger(this.data.reasonTypeOne) ||
                  this.data.reasonTypeOne === 0
                ) {
                  callback(new Error('请选择汇总原因'))
                } else {
                  callback()
                }
              }, 100)
            },
            trigger: 'change'
          }
        ],
        reasonTypeTwo: [
          {
            required: true,
            validator: (rule, value, callback) => {
              setTimeout(() => {
                if (!Number.isInteger(this.data.reasonTypeTwo) || this.data.reasonTypeTwo === 0) {
                  callback(new Error('请选择汇总原因'))
                } else {
                  callback()
                }
              }, 100)
            },
            trigger: 'change'
          }
        ],
        productNumber: [
          {
            required: true,
            message: '请填写物料编码',
            trigger: 'change'
          }
        ],
        // productName: [
        //   {
        //     required: true,
        //     message: '请填写物料描述',
        //     trigger: 'change'
        //   }
        // ],
        // productGroup: [
        //   {
        //     required: true,
        //     message: '请填写物料组',
        //     trigger: 'change'
        //   },
        //   {
        //     validator: (rule, value, callback) => {
        //       setTimeout(() => {
        //         if (value === '' || isNaN(value)) {
        //           callback(new Error('请输入数字值'))
        //         } else {
        //           callback()
        //         }
        //       }, 100)
        //     },
        //     trigger: 'blur'
        //   }
        // ],
        // productGroupName: [
        //   {
        //     required: true,
        //     message: '请填写物料组描述',
        //     trigger: 'change'
        //   }
        // ],
        // sapOrderType: [
        //   {
        //     required: true,
        //     message: '请填写单据类型',
        //     trigger: 'change'
        //   }
        // ],
        // sapOrderNo: [
        //   {
        //     required: true,
        //     message: '请填写单据号',
        //     trigger: 'change'
        //   }
        // ],
        location: [
          {
            required: true,
            message: '请填写库存地点',
            trigger: 'change'
          },
          {
            validator: (rule, value, callback) => {
              setTimeout(() => {
                if (value === '' || isNaN(value)) {
                  callback(new Error('请输入数字值'))
                } else {
                  callback()
                }
              }, 100)
            },
            trigger: 'blur'
          }
        ],
        // locationName: [
        //   {
        //     required: true,
        //     message: '请填写库存地点描述',
        //     trigger: 'change'
        //   }
        // ],

        // orderDate: [
        //   {
        //     // type: 'date',
        //     required: true,
        //     message: '请选择单据日期',
        //     trigger: 'change'
        //   }
        // ],
        // expiryDate: [
        //   {
        //     // type: 'date',
        //     required: true,
        //     message: '请选择到期日期',
        //     trigger: 'change'
        //   }
        // ],
        availableQuantity: [
          {
            required: true,
            message: '请填写可用数量',
            trigger: 'change'
          },
          {
            validator: (rule, value, callback) => {
              setTimeout(() => {
                if (value === '' || isNaN(value)) {
                  callback(new Error('请输入数字值'))
                } else {
                  callback()
                }
              }, 100)
            },
            trigger: 'blur'
          }
        ]
        // customerNumber: [
        //   {
        //     required: true,
        //     message: '请填写客户代码',
        //     trigger: 'change'
        //   }
        // ],
        // customerName: [
        //   {
        //     required: true,
        //     message: '请填写客户名称',
        //     trigger: 'change'
        //   }
        // ],
        // sellerName: [
        //   {
        //     required: true,
        //     message: '请填写销售',
        //     trigger: 'change'
        //   }
        // ],
        // customerServiceName: [
        //   {
        //     required: true,
        //     message: '请填写客服',
        //     trigger: 'change'
        //   }
        // ],
        // purchaseNumber: [
        //   {
        //     required: true,
        //     message: '请填写采购员编码',
        //     trigger: 'change'
        //   }
        // ],
        // purchaseName: [
        //   {
        //     required: true,
        //     message: '请填写采购员描述',
        //     trigger: 'change'
        //   }
        // ],
        // productManagerName: [
        //   {
        //     required: true,
        //     message: '商品经理名称',
        //     trigger: 'change'
        //   }
        // ]
      },
      reasonOptions: [],
      submitLoading: false
    }
  },
  computed: {
    reasonTypeOneList () {
      return getReasonTypeOneList(this.reasonOptions)
    }
  },
  watch: {
    editObj: {
      handler () {
        this.initData()
      },
      immediate: true
    }
  },
  created () {
    pendingNoOrderReason().then(data => {
      this.reasonOptions = data
    })
  },
  methods: {
    getReasonTypeOneText (reasonOne) {
      return getReasonTypeOneText(this.reasonTypeOneList, reasonOne)
    },
    getReasonTypeTwoText (reasonOne, reasonTwo) {
      return getReasonTypeTwoText(
        this.getReasonTypeTwoList(reasonOne),
        reasonTwo
      )
    },
    getReasonTypeTwoList (reasonOne) {
      return getReasonTypeTwoList(this.reasonOptions, reasonOne)
    },
    rowReasonTypeOneChange (reasonOne) {
      this.$set(this.data, 'reasonTypeOne', reasonOne)
      this.$set(this.data, 'reasonTypeTwo', 0)
    },
    rowReasonTypeTwoChange (reasonTwo) {
      this.$set(this.data, 'reasonTypeTwo', reasonTwo)
    },
    // optionsChange (itemList) {
    //   this.data.reasonTypeOne = itemList[0]
    //   this.data.reasonTypeTwo = itemList[1]
    // },
    initData () {
      this.resetForm()
      if (this.editObj) {
        this.data = { ...this.editObj }
      } else {
        this.data = {}
      }
      if (!this.data.batchDate) {
        this.data.batchDate = this.defaultTime
      }
    },
    ok () {
      this.$refs['dialogForm'].validate(valid => {
        if (valid) {
          this.submitLoading = true
          if (!this.editObj) {
            // 新增
            pendingOrderAdd(this.data)
              .then(res => {
                this.submitLoading = false
                if (res.code === 200) {
                  this.resetForm()
                  this.$emit('ok', this.data)
                } else {
                  this.$message.error(res.msg || '新增失败')
                }
              })
              .catch(() => {
                this.submitLoading = false
              })
          } else {
            // 修改
            const param = { ...this.data }
            delete param.gmtCreate
            delete param.gmtModify
            pendingOrderEdit(this.data.id, param)
              .then(res => {
                this.submitLoading = false
                if (res.code === 200) {
                  this.resetForm()
                  this.$emit('ok', this.data)
                } else {
                  this.$message.error(res.msg || '新增失败')
                }
              })
              .catch(() => {
                this.submitLoading = false
              })
          }
        }
      })
    },

    resetForm () {
      this.data = {}
      if (this.$refs['dialogForm']) {
        this.$refs['dialogForm'].resetFields()
      }
    },
    cancel () {
      this.resetForm()
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="scss">
.abnormal-order-set-dialog {
  .el-select {
    width: 100%;
  }
}
</style>
