<template>
  <div class="app-container">
    <div class="filter-container">
      Hi, {{ principal }} 今天是{{ dateString }} 请操作今日的未下单事项
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane v-if="personalShown" label name="gr">
        <span slot="label">个人：待处理的异常未下单</span>
        <abnormalNoOrderList />
      </el-tab-pane>
      <el-tab-pane v-if="statisticsShown" name="tj">
        <span slot="label">下单异常统计分析</span>
        <abnormalOrderStatistics />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { dateFormat } from '@/filters/index.js'
// import { changeQueryByTab } from '@/utils/index.js'
import abnormalNoOrderList from './abnormalNoOrderList'
import abnormalOrderStatistics from './abnormalOrderStatistics/index.vue'

export default {
  name: 'AbnormalNoOrder',
  filters: {},
  components: {
    abnormalNoOrderList: abnormalNoOrderList,
    abnormalOrderStatistics: abnormalOrderStatistics
  },
  data () {
    return {
      activeName: 'gr', // gr tj
      dateString: dateFormat('yyyy-MM-dd', new Date())
    }
  },

  computed: {
    principal () {
      return this.$store.state.user.principal
    },
    personalShown () {
      return true
    },
    statisticsShown () {
      return true
    }
  },
  watch: {
    // activeName: {
    //   handler (newName, oldName) {
    //     this.$route.query.tab = newName
    //     // changeQueryByTab('tab', newName)
    //   },
    //   immediate: true
    // }
  },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      const tab = this.$route.query.tab || 'gr'
      if (tab === 'gr') {
        this.activeName = 'gr'
      } else {
        this.activeName = 'tj'
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
