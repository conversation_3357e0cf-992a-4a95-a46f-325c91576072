<template>
  <div v-loading="loading.autofill">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="100px"
      >
        <el-row>
          <el-col :span="5">
            <el-form-item label="填写状态：" prop="statusId">
              <el-select v-model="searchForm.statusId">
                <el-option
                  v-for="item in statusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  style="width:100%"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="选择日期：" prop="batchDate">
              <el-date-picker
                v-model="searchForm.batchDate"
                align="right"
                unlink-panels
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item class="item_width" label="工单状态:" prop="status">
              <el-select
                v-model="searchForm.status"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in orderStatus"
                  :key="item.key"
                  :label="item.value"
                  :name="item.value"
                  :value="item.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item class="item_width" label="工单编号:" prop="workOrderNo">
              <el-input
                v-model.trim="searchForm.workOrderNo"
                placeholder="请输入"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="2" :style="{display: 'flex', justifyContent: 'flex-end'}">
            <el-button type="primary" icon="el-icon-search" @click="handleFilter"
              >查询</el-button
            >
          </el-col>
        </el-row>
      </el-form>

      <el-row type="flex" class="row-bg" justify="space-between">
        <el-col :span="16">
          <el-link href="/oms-new/pending/order/template/download"
            ><el-button
              type="primary"
              icon="el-icon-download"
              @click="downloadTemplate"
              >模板下载</el-button
            ></el-link
          >
          <!-- ATTENTION: action 未知 -->
          <el-upload
            v-if="!emailSended"
            ref="upload"
            action="/upload"
            style="display: inline-block;margin-left:10px;margin-right:10px;"
            :show-file-list="false"
            accept=".csv, .xlsx, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            :before-upload="$validateFileType"
            :multiple="false"
            :http-request="httpRequestHandle"
          >
            <el-button type="success" icon="el-icon-upload2"
              >上传excel</el-button
            >
          </el-upload>
          <el-button v-else disabled type="success" icon="el-icon-upload2"
            >上传excel</el-button
          >
          <span v-if="!emailSended" style="font-size:12px;color:red;"
            >重新上传EXL会替换当前界面所有行</span
          >
          <span v-else style="font-size:12px;color:#666;"
            >当天确认提交后不能再上传编辑</span
          >
        </el-col>
        <!-- QUESTION: 这块内容需要优化 -->
        <el-col :span="8" style="text-align:right;">
          <span v-if="emailSended">
            <el-tooltip content="确认提交后不能再新增修改" placement="top">
              <div style="display:inline-block;">
                <el-button
                  :disabled="emailSended"
                  type="primary"
                  icon="el-icon-circle-plus-outline"
                  @click="addRow"
                  >新增行</el-button
                >
              </div>
            </el-tooltip>
            <el-tooltip content="当天只能提交1次" placement="top">
              <div style="display:inline-block;">
                <el-button
                  :disabled="emailSended"
                  type="success"
                  icon="el-icon-s-promotion"
                  @click="openSendEmailDialog"
                  >确认提交</el-button
                >
              </div>
            </el-tooltip>
          </span>
          <span v-else>
            <el-button type="primary" icon="el-icon-circle-plus-outline" @click="autofill">自动填充</el-button>
            <el-button
              type="primary"
              icon="el-icon-circle-plus-outline"
              @click="addRow"
              >新增行</el-button
            >
            <el-button
              type="success"
              icon="el-icon-s-promotion"
              @click="openSendEmailDialog"
              >确认提交</el-button
            >
          </span>
          <el-link v-if="searchForm.workOrderNo && searchForm.status" :href="`/oms-new/pending/order/export?statusId=${searchForm.statusId}&batchDate=${searchForm.batchDate}&current=${searchForm.current}&pageSize=${searchForm.pageSize}&workOrderNo=${searchForm.workOrderNo}&status=${searchForm.status}`">
            <el-button type="primary" style="margin-left: 10px" :disabled="disabled" icon="el-icon-download">全部下载</el-button>
          </el-link>
          <el-link v-else-if="searchForm.workOrderNo" :href="`/oms-new/pending/order/export?statusId=${searchForm.statusId}&batchDate=${searchForm.batchDate}&current=${searchForm.current}&pageSize=${searchForm.pageSize}&workOrderNo=${searchForm.workOrderNo}`">
            <el-button type="primary" style="margin-left: 10px" :disabled="disabled" icon="el-icon-download">全部下载</el-button>
          </el-link>
          <el-link v-else-if="searchForm.status" :href="`/oms-new/pending/order/export?statusId=${searchForm.statusId}&batchDate=${searchForm.batchDate}&current=${searchForm.current}&pageSize=${searchForm.pageSize}&status=${searchForm.status}`">
            <el-button type="primary" style="margin-left: 10px" :disabled="disabled" icon="el-icon-download">全部下载</el-button>
          </el-link>
          <el-link v-else :href="`/oms-new/pending/order/export?statusId=${searchForm.statusId}&batchDate=${searchForm.batchDate}&current=${searchForm.current}&pageSize=${searchForm.pageSize}`">
            <el-button type="primary" style="margin-left: 10px" :disabled="disabled" icon="el-icon-download">全部下载</el-button>
          </el-link>
        </el-col>
      </el-row>
    </div>

    <el-table
      v-loading="listLoading"
      :data="list"
      ref="table"
      border
      fit
      highlight-current-row
      :max-height="500"
    >
      <el-table-column
        label="日期"
        min-width="100px"
        align="center"
        prop="batchDate"
      />
      <el-table-column
        label="未下单天数"
        min-width="100px"
        align="center"
        prop="delayDays"
      />

      <el-table-column label="汇总原因" min-width="200px" align="center">
        <template slot-scope="{ row }">
          <span v-if="emailSended || row.sendStatusId == 1">
            {{ getReasonTypeOneText(row.reasonTypeOne) }}
          </span>
          <el-select
            v-else
            :value="row.reasonTypeOne || ''"
            placeholder="请选择"
            @change="
              newValue => {
                rowReasonTypeOneChange(newValue, row)
              }
            "
          >
            <el-option
              v-for="item in reasonTypeOneList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="汇总原因明细" min-width="200px" align="center">
        <template slot-scope="{ row }">
          <span v-if="emailSended || row.sendStatusId == 1">
            {{ getReasonTypeTwoText(row.reasonTypeOne, row.reasonTypeTwo) }}
          </span>
          <el-select
            v-else
            :value="row.reasonTypeTwo || ''"
            placeholder="请选择"
            @change="
              newValue => {
                rowReasonTypeTwoChange(newValue, row)
              }
            "
          >
            <el-option
              v-for="item in getReasonTypeTwoList(row.reasonTypeOne)"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="未下单原因" width="200px" align="center">
        <template slot-scope="{ row }">
          <span v-if="emailSended || row.sendStatusId == 1">
            {{ row.reason }}
          </span>
          <el-popover v-else placement="bottom" width="400" trigger="click">
            <el-input
              v-model="row.reason"
              type="textarea"
              :maxlength="255"
              show-word-limit
              :autosize="true"
              @change="
                newValue => {
                  changeReasonContent(newValue, row)
                }
              "
            />
            <div slot="reference" class="pseudo-input-box el-textarea__inner">
              {{ row.reason }}
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column
        label="物料编码"
        min-width="100px"
        align="center"
        prop="productNumber"
      />
      <el-table-column label="物料描述" width="120px" align="center">
        <template slot-scope="{ row }">
          <el-tooltip class="item" effect="dark" :content="row.productName">
            <span class="max-row-2">{{ row.productName }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column
        label="库存地点"
        width="80px"
        align="center"
        prop="location"
      />
      <el-table-column
        label="库存地点描述"
        width="110px"
        align="center"
        prop="locationName"
      />
      <el-table-column
        label="物料组"
        min-width="100px"
        align="center"
        prop="productGroup"
      />
      <el-table-column
        label="物料组描述"
        min-width="100px"
        align="center"
        prop="productGroupName"
      />
      <el-table-column
        label="单据类型"
        min-width="100px"
        align="center"
        prop="sapOrderType"
      />

      <el-table-column
        label="单据号"
        width="80px"
        align="center"
        prop="sapOrderNo"
      />
      <el-table-column
        label="单据日期"
        width="100px"
        align="center"
        prop="orderDate"
      />
      <el-table-column
        label="到期日期"
        width="100px"
        align="center"
        prop="expiryDate"
      />
      <el-table-column
        label="可用数量"
        width="100px"
        align="center"
        prop="availableQuantity"
      />
      <el-table-column
        label="客户代码"
        width="100px"
        align="center"
        prop="customerNumber"
      />
      <el-table-column
        label="客户名称"
        width="100px"
        align="center"
        prop="customerName"
      />
      <el-table-column
        label="客服"
        width="100px"
        align="center"
        prop="customerServiceName"
      />
      <el-table-column
        label="跟单客服"
        width="100px"
        align="center"
        prop="followCustomerServiceName"
      />
      <el-table-column
        label="销售"
        width="100px"
        align="center"
        prop="sellerName"
      />
      <el-table-column
        label="EVM运营专员"
        width="100px"
        align="center"
        prop="evmBusinessName"
      />
      <el-table-column
        label="采购员编码"
        width="100px"
        align="center"
        prop="purchaseNumber"
      />
      <el-table-column
        label="采购员描述"
        width="100px"
        align="center"
        prop="purchaseName"
      />
      <el-table-column
        label="商品经理名称"
        width="100px"
        align="center"
        prop="productManagerName"
      />

      <el-table-column label="填写状态" width="100px" align="center">
        <template slot-scope="{ row }">{{
          row.statusId | statusTextById
        }}</template>
      </el-table-column>
      <el-table-column label="已发邮件" align="center">
        <template slot-scope="{ row }">
          <span v-if="row.sendStatusId == 1" style="color:#13ce66;">是</span>
          <span v-else>否</span>
        </template>
      </el-table-column>
      <el-table-column
        label="工单编号"
        width="200px"
        align="center"
        prop="workOrderNo"
      >
        <template slot-scope="{ row }">
          <el-link @click="gotoDetail(row.workOrderNo)"  type="primary">
            {{ row.workOrderNo }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column
        label="工单状态"
        width="100px"
        align="center"
        prop="statusText"
      />
      <el-table-column
        label="工单创建日期"
        width="140px"
        align="center"
        prop="createTime"
      />
      <el-table-column
        label="当前处理人"
        width="100px"
        align="center"
        prop="operateUser"
      />
      <el-table-column
        label="操作"
        align="center"
        width="160px"
        fixed="right"
        class-name="small-padding"
      >
        <template slot-scope="{ row }">
          <el-button v-if="row.status === 6 || !row.workOrderNo" type="text" size="mini" @click="createdOrder(row.id)"
            >发起工单</el-button
          >
          <el-button v-if="row.sendStatusId != 1" type="text" size="mini" @click="editRow(row)"
            >编辑</el-button
          >
          <el-button v-if="row.sendStatusId != 1" type="text" size="mini" @click="delRow(row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="searchForm.current"
      :limit.sync="searchForm.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
    <transition>
      <el-dialog
        :visible.sync="dialogEditVisible"
        width="80%"
        append-to-body
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div slot="title">
          <span style="padding-right: 10px;">{{
            edittingRow == null ? '新增' : '修改'
          }}</span>
        </div>
        <abnormalItemSet
          :edit-obj="edittingRow"
          :default-time="searchForm.batchDate"
          @cancel="dialogEditVisible = false;edittingRow = null"
          @ok="rowSet"
        />
      </el-dialog>
    </transition>
    <transition>
      <el-dialog
        :visible.sync="dialogSendMailVisible"
        width="400px"
        append-to-body
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div slot="title">
          <span style="padding-right: 10px;">确认提交</span>
        </div>
        <!-- <el-form ref="dialogForm" :rules="rules" :model="data" label-width="150px"> -->
        <div>
          <p>确认提交后当日将不能再编辑/添加内容</p>
          <p>23：00将自动发送邮件给对应的商品经理、客服、销售并更新至销售订单流程内</p>
        </div>
        <div slot="footer" class="dialog-footer space-around-footer">
          <el-button @click="dialogSendMailVisible = false;sendMailLoading = false" >取 消</el-button>
          <el-button
            type="primary"
            :loading="sendMailLoading"
            @click="sendEmail"
            >确定提交</el-button
          >
        </div>
      </el-dialog>
    </transition>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {
  pendingOrderList,
  uploadFile,
  oms,
  // pendingOrderAdd,
  pendingOrderEdit,
  pendingOrderDel,
  pendingOrderEmail,
  pendingOrderEmailStatus,
  pendingNoOrderReason,
  creatOrder
} from '@/api/purchaseList.js'
import abnormalItemSet from './abnormalItemSet'
import { validEmail } from '@/utils/validate.js'
import {
  getReasonTypeOneList,
  getReasonTypeOneText,
  getReasonTypeTwoText,
  getReasonTypeTwoList,
  dateFormat
} from '@/filters/index.js'
import { routeToWorkflow } from '@/utils'
const statusOption = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '已填写',
    value: '1'
  },
  {
    label: '未填写',
    value: '0'
  }
]
const orderStatus = [{
  key: 1,
  value: '进行中'
},
{
  key: 5,
  value: '已完成'
},
{
  key: 6,
  value: '已作废'
}]
export default {
  filters: {
    statusTextById (val) {
      return statusOption.find(item => {
        return item.value === val + ''
      }).label
    }
  },
  components: {
    abnormalItemSet,
    Pagination
  },
  // 个人待处理的异常未下单
  data () {
    return {
      emailPlaceholder:
        '多个邮箱地址请以 , 或 ; 隔开，包含其他信息的邮箱需要以<>包括，例如："aaa"<<EMAIL>>,"bbb"<<EMAIL>>; <EMAIL>',
      emailSended: false, // 本日是否发过邮件
      dialogEditVisible: false, // 增删改对话框
      dialogSendMailVisible: false, // 发送邮件对话框
      pickerOptions: {
        // 禁用明天和之后的日期
        disabledDate (time) {
          return (
            time.getTime() > new Date(new Date().toLocaleDateString()).getTime()
          )
        }
      },
      fileSizeLimit: 100, // 上传文件大小限制，单位 MB
      fileName: '', // 上传文件名
      listLoading: false,
      searchForm: {
        statusId: '',
        batchDate: '',
        current: 1,
        pageSize: 10
      },
      rules: {
        batchDate: [
          {
            // type: 'date',
            required: true,
            message: '请选择日期',
            trigger: 'change'
          }
        ]
      },
      statusOption: statusOption,
      list: [],
      total: 0,
      edittingRow: null, // 编辑中的行
      reasonOptions: [],
      sendMailLoading: false,
      loading: {
        autofill: false
      },
      sendMailData: {
        emails: ''
      },
      sendMailRules: {
        emails: [
          {
            required: true,
            validator: (rule, value, callback) => {
              // if (!value) {
              //   return callback(new Error('请选择未下单分类'))
              // }
              setTimeout(() => {
                if (!this.sendMailData.emails) {
                  callback(new Error('请填写正确的邮箱地址'))
                } else {
                  const emailList = this.getEmailListByString(
                    this.sendMailData.emails
                  )
                  let error = false

                  if (emailList.length === 0) {
                    // 没有邮箱
                    error = true
                  } else if (
                    // 有错误数据
                    emailList.filter(s => {
                      return s === null
                    }).length > 0
                  ) {
                    // 有错误邮箱
                    error = true
                  } else if (
                    // 全都为空
                    emailList.filter(s => {
                      return s === ''
                    }).length === emailList.length
                  ) {
                    error = true
                  }
                  if (error) {
                    // 有错误邮箱
                    callback(new Error('请填写正确的邮箱地址'))
                  } else {
                    callback()
                  }
                }
              }, 100)
            },
            trigger: 'change'
          }
        ]
      },
      orderStatus,
      disabled: false
    }
  },
  computed: {
    reasonTypeOneList () {
      return getReasonTypeOneList(this.reasonOptions)
    }
  },
  created () {
    // 初始化未下单原因
    this.searchForm.batchDate = dateFormat('yyyy-MM-dd', new Date())
    this.initEmailStatus()
    pendingNoOrderReason().then(data => {
      this.reasonOptions = data
      this.handleFilter()
    })
  },
  watch: {
    list () {
      if (this.$refs.table && this.$refs.table.doLayout) {
        console.log('doLayout', this.$refs.table.doLayout)
        this.$refs.table.doLayout();
      }
    }
  },
  methods: {
    downloadTemplate () {},
    autofill () {
      this.loading.autofill = true
      oms({
        url: '/pending/order/auto/fill',
        method: 'GET',
        complete: res => {
          this.loading.autofill = false
          if (res.code === 200) {
            this.$message.success('自动填充成功')
            this.getList()
          } else {
            this.$message.error('请检查行内容，有缺失无法填充')
          }
        }
      })
    },
    initEmailStatus () {
      pendingOrderEmailStatus().then(data => {
        if (data.code === 200) {
          this.emailSended = data.data || false
        }
      })
    },
    getReasonTypeOneText (reasonOne) {
      return getReasonTypeOneText(this.reasonTypeOneList, reasonOne)
    },
    getReasonTypeTwoText (reasonOne, reasonTwo) {
      return getReasonTypeTwoText(
        this.getReasonTypeTwoList(reasonOne),
        reasonTwo
      )
    },
    // getReasonTypeText (reasonOne, reasonTwo) {
    //   return getReasonTypeText(this.reasonOptions, reasonOne, reasonTwo)
    // },
    getReasonTypeTwoList (reasonOne) {
      return getReasonTypeTwoList(this.reasonOptions, reasonOne)
    },
    rowReasonTypeOneChange (reasonOne, row) {
      row.reasonTypeOne = reasonOne
      row.reasonTypeTwo = 0
      pendingOrderEdit(row.id, row).then(() => {
        // this.getList()
      })
    },
    rowReasonTypeTwoChange (reasonTwo, row) {
      row.reasonTypeTwo = reasonTwo
      pendingOrderEdit(row.id, row).then(() => {
        // this.getList()
      })
    },
    changeReasonType (newValue, row) {
      row.reasonTypeOne = newValue[0]
      row.reasonTypeTwo = newValue[1]
      pendingOrderEdit(row.id, row).then(() => {
        // this.getList()
      })
    },
    changeReasonContent (newValue, row) {
      row.reason = newValue
      pendingOrderEdit(row.id, row).then(() => {
        // this.getList()
      })
    },
    httpRequestHandle (params) {
      const file = params.file
      // if (!file.data) {
      //   this.$message.error('上传文件参数缺失！')
      //   return
      // }

      // 校验大小
      const isGtLimit = file.size / 1024 / 1024 > this.fileSizeLimit
      if (isGtLimit) {
        this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)',
        customClass: 'loadingCustomer'
      })
      this.fileName = encodeURIComponent(file.name)

      // 请求成功，开始上传
      const formData = new FormData()
      formData.append('file', file)
      uploadFile(formData)
        .then(response => {
          loading.close()
          const _data = response.data
          if (response.code !== 200) {
            let errorMsg = ''
            let errorTitle = ''
            if (_data && _data.failList && _data.failList.length > 0) {
              errorMsg = _data.failList.join('\r\n')
              errorTitle = response.msg
            } else {
              errorTitle = '上传出错'
              errorMsg = response.msg
            }
            this.$alert(errorMsg, errorTitle, {
              customClass: 'message-pre-line',
              confirmButtonText: '确定'
            })
          } else {
            this.$message({
              message: '文件上传成功',
              type: 'success'
            })
            // 上传成功，通知后台
            this.getList()
          }
        })
        .catch(e => {
          this.$message.error('文件上传失败，请重新尝试')
          loading.close()
        })
    },
    getList () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.listLoading = true
          const param = {
            ...this.searchForm
          }
          pendingOrderList(param)
            .then(response => {
              if (response.code === 200 && response.data) {
                this.list = response.data
                this.total = response.totalCount || 0
                if (this.total > 0) {
                  this.disabled = false
                } else {
                  this.disabled = true
                }
                // this.hasDocCnt = response.data.hasDocCnt || 0
                // this.noDocCnt = response.data.noDocCnt || 0
              } else {
                this.$notify.error(response.msg)
                // 报错时清空数据
                this.list = []
                this.total = 0
                // this.hasDocCnt = 0
                // this.noDocCnt = 0
              }
              this.listLoading = false
            })
            .catch(e => {
              this.listLoading = false
            })
        }
      })
    },
    handleFilter () {
      this.searchForm.current = 1
      this.getList()
    },
    addRow () {
      this.edittingRow = null
      this.dialogEditVisible = true
      //
    },
    editRow (row) {
      this.edittingRow = row
      this.dialogEditVisible = true
    },
    rowSet (row, index) {
      this.dialogEditVisible = false
      this.getList()
    },
    delRow (row) {
      this.$confirm('是否要删除该行？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        pendingOrderDel(row.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.getList()
        })
      })
    },
    getEmailListByString (emailStr) {
      const list = emailStr.split(/,|;|，|；/).map(s => {
        const str = s && s.trim()
        if (str) {
          const pattern = '\\<(.+?)\\>'
          const email = str.match(pattern) ? str.match(pattern)[1] : str
          return validEmail(email) ? email : null
        } else {
          return ''
        }
      })
      return list
    },
    openSendEmailDialog () {
      this.dialogSendMailVisible = true
    },
    sendEmail () {
      this.sendMailLoading = true
      pendingOrderEmail()
        .then(res => {
          this.sendMailLoading = false
          const _data = res.data
          if (res.code === 0) {
            this.emailSended = true
            this.dialogSendMailVisible = false
            this.getList()
            this.$message({
              message: '操作成功！',
              type: 'success'
            })
          } else {
            let errorMsg = ''
            let errorTitle = ''
            if (_data && _data.failList && _data.failList.length > 0) {
              errorMsg = _data.failList.join('\r\n')
              errorTitle = res.msg
              this.$alert(errorMsg, errorTitle, {
                customClass: 'message-pre-line',
                confirmButtonText: '确定'
              })
            } else {
              errorTitle = '操作失败！'
              errorMsg = res.msg
              this.$message({
                message: errorMsg || '操作失败！',
                type: 'error'
              })
            }
          }
        })
        .catch(() => {
          this.$message.error('操作失败')
          this.sendMailLoading = false
        })
    },
    async createdOrder (id) {
      try {
        const res = await creatOrder(id)
        if (res.code === 200) {
          routeToWorkflow('/wf/create/0', {
            essence: 1,
            data: encodeURIComponent(JSON.stringify(res.data))
          })
        }
      } catch (error) {
        console.log(error);
      }
    },
    gotoDetail(workOrderNo) {
      routeToWorkflow('/wf/detail/' + workOrderNo)
    }
  },
  activated () {
    this.handleFilter()
  }
}
</script>
<style lang="scss">
.message-pre-line {
  .el-message-box__message {
    p {
      white-space: pre-line;
    }
  }
}
.max-row-2 {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
<style lang="scss" scoped>
.pseudo-input-box {
  text-align: left;
  min-height: 36px;
}
.space-around-footer {
  display: flex;
  justify-content: space-around;
}
</style>
