<template>
  <div class="app-container">
    <div class="filter-container">
      <el-form ref="searchForm" :model="searchForm" label-width="150px" inline>
        <el-form-item label="采购员：" prop="buyer">
          <el-input
            v-model.trim="searchForm.buyer"
            placeholder="输入采购员名称"
            clearable
          />
        </el-form-item>
        <el-form-item
          v-show="purchaseNoDisabled"
          label="采购单号："
          prop="purchaseNo"
        >
          <el-input
            v-model.trim="searchForm.purchaseNo"
            placeholder="输入SAP采购单号"
            clearable
          />
        </el-form-item>
        <el-form-item
          v-show="purchaseStatusDisabled"
          label="采购进度："
          prop="statusCode"
        >
          <el-select
            v-model="searchForm.statusCode"
            placeholder="请选择"
            @change="handleFilter(1)"
          >
            <el-option
              v-for="item in purchaseStatusOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="商品编码：" prop="productNo">
          <el-input
            v-model.trim="searchForm.productNo"
            placeholder="输入SKU编号"
            clearable
          />
        </el-form-item>
        <!-- <el-form-item label="查询SO逾期：" prop="salesOrderNo">
          <el-input
            v-model.trim="searchForm.salesOrderNo"
            :disabled="salesOrderNoDisabled"
            placeholder="输入SAP销售订单号"
            clearable
          />
        </el-form-item>-->

        <!-- <el-form-item v-show="purchaseOrderTypeDisabled" label="订单类型：" prop="purchaseOrderType">
          <el-select
            v-model="searchForm.purchaseOrderType"
            multiple
            placeholder="请选择"
            @change="handleFilter(1)"
          >
            <el-option
              v-for="item in orderTypeOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>-->
        <el-form-item label>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click="handleFilter(1)"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div style="margin:5px 0;">
      <el-button
        type="warning"
        icon="el-icon-folder-delete"
        @click="toAbnormalList()"
        >异常未下单在线提交</el-button
      >
    </div>
    <el-tabs v-show="!searchForm.salesOrderNo" v-model="activeName">
      <el-tab-pane label name="first">
        <span slot="label"> <i class="el-icon-s-order" /> 采购订单列表 </span>
        <el-table
          v-loading="listLoading"
          :data="list"
          border
          fit
          highlight-current-row
          height="500"
        >
          <el-table-column label="商品编码" align="center" prop="productNo" />
          <el-table-column label="商品描述" min-width="100px" align="center">
            <template slot-scope="{ row }">
              <div class="multiText" :title="row.productName">
                {{ row.productName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="采购员" align="center" prop="buyer" />
          <el-table-column label="采购单号" align="center">
            <template slot-scope="{ row }">
              <div>{{ row.purchaseNo }}</div>
            </template>
          </el-table-column>

          <!-- <el-table-column label="采购单类型" align="center">
            <template slot-scope="{row}">
              <div>{{ row.purchaseOrderType }}</div>
            </template>
          </el-table-column>-->
          <el-table-column label="采购单行号" align="center">
            <template slot-scope="{ row }">
              <div>{{ row.purchaseLine }}</div>
            </template>
          </el-table-column>
          <el-table-column
            label="计划行号"
            align="center"
            prop="purchasePlanLine"
          />
          <el-table-column
            label="供应商名称"
            align="center"
            prop="providerName"
          >
            <template slot-scope="{ row }">
              <div class="multiText" :title="row.providerName">
                {{ row.providerName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="供应商编码"
            align="center"
            prop="providerNo"
          />
          <el-table-column
            label="到货仓库"
            align="center"
            prop="warehouseName"
          />
          <el-table-column label="到货数量" align="center" prop="quantity" />
          <el-table-column label="下单日期" align="center">
            <template slot-scope="{ row }">
              <div>{{ row.orderDate }}</div>
            </template>
          </el-table-column>
          <el-table-column label="预计到货时间" align="center">
            <template slot-scope="{ row }">
              <div>{{ row.plannedArrivalDate }}</div>
            </template>
          </el-table-column>
          <el-table-column label="采购进度" align="center">
            <template slot-scope="{ row }">
              <div :class="getStatusNameClass(row.statusName)">
                {{ row.statusName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="详情"
            align="center"
            class-name="small-padding"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                :disabled="!row.purchaseNo"
                @click="toDtl(row)"
                >查看缺货销售订单</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.pageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="getList"
        />
      </el-tab-pane>
      <el-tab-pane name="second">
        <span slot="label">
          <i class="el-icon-document-delete" /> 未采SKU列表
        </span>
        <el-table
          v-loading="listLoading2"
          :data="list2"
          border
          fit
          highlight-current-row
          height="500"
        >
          <el-table-column
            label="商品编码"
            min-width="100px"
            align="center"
            prop="productNo"
          />
          <el-table-column label="商品描述" align="center">
            <template slot-scope="{ row }">
              <div class="multiText" :title="row.productName">
                {{ row.productName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="采购员" align="center" prop="buyer" />
          <el-table-column
            label="累计订单需求数量"
            align="center"
            prop="requiredQuantity"
          />
          <el-table-column
            label="在库数量"
            align="center"
            prop="stockQuantity"
          />
          <el-table-column
            label="在途数量"
            align="center"
            prop="transitQuantity"
          />
          <el-table-column
            label="缺货数量"
            align="center"
            prop="needQuantity"
          />
          <!-- <el-table-column label="需采数量" align="center" prop="needQuantity" /> -->
          <el-table-column
            label="需求仓库"
            align="center"
            prop="warehouseName"
          />
          <!-- <el-table-column label="供应状态" align="center">
            <template slot-scope="{row}">
              <div :class="getSupplyStatusClass(row.statusName)">{{ row.statusName }}</div>
            </template>
          </el-table-column>-->

          <el-table-column
            label="详情"
            align="center"
            class-name="small-padding"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                :disabled="!row.productNo"
                @click="toDtl2(row)"
                >查看待采销售订单</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total2 > 0"
          :total="total2"
          :page.sync="listQuery.current2"
          :limit.sync="listQuery.pageSize2"
          layout="total, prev, pager, next, jumper"
          @pagination="getList"
        />
      </el-tab-pane>
    </el-tabs>

    <el-dialog :visible.sync="dialogSalesOrderTableVisible" width="1500px">
      <div slot="title">
        采购单
        <span class="title-highlight">{{ detailPurchaseNo }}</span>
        对应的销售订单汇总
      </div>

      <el-table
        v-loading="listLoading3"
        :data="list3"
        border
        fit
        highlight-current-row
        height="600"
      >
        <el-table-column
          label="商品编码"
          min-width="100px"
          align="center"
          prop="productNo"
        />
        <!-- <el-table-column label="商品描述" min-width="100px" align="center" prop="productName" /> -->
        <el-table-column label="销售/调拨单号" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="toOrderDetail(row)">
              {{ row.salesOrderNo }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="采购单"
          min-width="100px"
          align="center"
          prop="purchaseNo"
        />
        <!-- <el-table-column label="客户要求整单" min-width="100px" align="center" prop="wholeOrder" /> -->
        <el-table-column
          label="订单行号"
          min-width="100px"
          align="center"
          prop="orderLine"
        />
        <el-table-column
          label="订单数量"
          min-width="100px"
          align="center"
          prop="orderQuantity"
        />
        <el-table-column
          label="订单创建时间"
          min-width="100px"
          align="center"
          prop="orderDate"
        />
        <el-table-column
          label="订单行创建时间"
          min-width="100px"
          align="center"
        >
          <template slot-scope="{ row }">
            <div>{{ row.orderLineDate }}</div>
          </template>
        </el-table-column>

        <el-table-column
          label="发货仓库"
          min-width="100px"
          align="center"
          prop="warehouseName"
        />
        <el-table-column
          label="客服/采购"
          min-width="100px"
          align="center"
          prop="customerService"
        />
        <el-table-column
          label="销售"
          min-width="100px"
          align="center"
          prop="sales"
        />

        <el-table-column label="客户要求交期" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <div>{{ row.customerReqDelivery }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="采购预计到货时间"
          min-width="100px"
          align="center"
          prop="estimatedDeliveryTime"
        />
        <el-table-column label="逾期天数" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <div :class="getStatusNameClass(row.statusName)">
              {{ row.statusName }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total3 > 0"
        :total="total3"
        :page.sync="listQuery.current3"
        :limit.sync="listQuery.pageSize3"
        layout="total, prev, pager, next, jumper"
        @pagination="getList3"
      />
    </el-dialog>
    <el-dialog :visible.sync="dialogStockoutTableVisible" width="1400px">
      <div slot="title">
        <span class="title-highlight">{{ detailProductNo }}</span> 缺货汇总
      </div>
      <el-table
        v-loading="listLoading4"
        :data="list4"
        border
        fit
        highlight-current-row
        height="500"
      >
        <el-table-column
          label="商品编码"
          min-width="100px"
          align="center"
          prop="productNo"
        />
        <el-table-column label="销售/调拨单号" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="toOrderDetail(row)">
              {{ row.salesOrderNo }}
            </el-link>
          </template>
        </el-table-column>
        <!-- <el-table-column label="客户要求整单" min-width="100px" align="center" prop="wholeOrder" /> -->
        <el-table-column
          label="订单行号"
          min-width="100px"
          align="center"
          prop="orderLine"
        />
        <el-table-column
          label="订单行数量"
          min-width="100px"
          align="center"
          prop="orderLineQuantity"
        />
        <el-table-column
          label="缺货数量"
          min-width="100px"
          align="center"
          prop="unpurchasedQuantity"
        />
        <el-table-column label="订单日期" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <div>{{ row.orderDate }}</div>
          </template>
        </el-table-column>
        <el-table-column label="订单行日期" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <div>{{ row.orderLineDate }}</div>
          </template>
        </el-table-column>
        <el-table-column label="客户要求交期" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <!-- 不是时间戳 -->
            <div>{{ row.customerReqDelivery }}</div>
          </template>
        </el-table-column>
        <el-table-column label="客服名称" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <!-- 不是时间戳 -->
            <div>{{ row.customerService }}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="缺货数量" min-width="100px" align="center" prop="unpurchasedQuantity" /> -->

        <el-table-column label="未下单天数" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <div :class="getStatusNameClass(row.statusName)">
              {{ row.statusName }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total4 > 0"
        :total="total4"
        :page.sync="listQuery.current4"
        :limit.sync="listQuery.pageSize4"
        layout="total, prev, pager, next, jumper"
        @pagination="getList4"
      />
    </el-dialog>
    <!-- 单查SO逾期 -->
    <div v-show="!!searchForm.salesOrderNo">
      <el-table
        v-loading="listLoading5"
        :data="list5"
        border
        fit
        highlight-current-row
        height="500"
      >
        <el-table-column
          label="商品编码"
          min-width="100px"
          align="center"
          prop="productNo"
        />
        <el-table-column label="销售/调拨单号" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <el-link type="primary" @click="toOrderDetail(row)">
              {{ row.salesOrderNo }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="客户要求整单"
          min-width="100px"
          align="center"
          prop="wholeOrder"
        />
        <el-table-column
          label="订单行号"
          min-width="100px"
          align="center"
          prop="orderLine"
        />
        <el-table-column
          label="订单数量"
          min-width="100px"
          align="center"
          prop="orderQuantity"
        />
        <el-table-column
          label="销售订单行创建时间"
          min-width="100px"
          align="center"
        >
          <template slot-scope="{ row }">
            <div>{{ row.orderLineDate }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="客服/采购"
          min-width="100px"
          align="center"
          prop="sales"
        />
        <el-table-column
          label="采购单"
          min-width="100px"
          align="center"
          prop="purchaseNo"
        />
        <el-table-column label="客户要求交期" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <div>{{ row.customerReqDelivery }}</div>
          </template>
        </el-table-column>
        <el-table-column label="未下单天数" min-width="100px" align="center">
          <template slot-scope="{ row }">
            <div :class="getStatusNameClass(row.statusName)">
              {{ row.statusName }}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total5 > 0"
        :total="total5"
        :page.sync="listQuery.current5"
        :limit.sync="listQuery.pageSize5"
        layout="total, prev, pager, next, jumper"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
// import Cache from '@/utils/cache.js'
import {
  purchaseList,
  unPurchaseList,
  purchaseDetail,
  unPurchaseDetail,
  purchaseSODetail
} from '@/api/purchaseList'
import { getSupplyStatusClass, getStatusNameClass } from '@/filters/index.js'

const orderTypeOption = [
  {
    label: 'Z001',
    value: 'Z001'
  },
  {
    label: 'Z002',
    value: 'Z002'
  },
  {
    label: 'Z003',
    value: 'Z003'
  },
  {
    label: 'Z005',
    value: 'Z005'
  },
  {
    label: 'Z006',
    value: 'Z006'
  },
  {
    label: 'Z011',
    value: 'Z011'
  },
  {
    label: 'Z013',
    value: 'Z013'
  }
]
const purchaseStatusOption = [
  {
    label: '全部',
    value: 0
  },
  {
    label: '已逾期',
    value: 1
  },
  {
    label: '即将逾期',
    value: 2
  },
  {
    label: '正常交期',
    value: 3
  }
]
export default {
  name: 'PurchaseList',
  components: {
    Pagination
  },
  filters: {},
  data () {
    return {
      listLoading: false,
      listLoading2: false,
      listLoading3: false,
      listLoading4: false,
      listLoading5: false,
      searchForm: {
        productNo: '',
        buyer: '',
        purchaseNo: '',
        statusCode: 0,
        salesOrderNo: '',
        purchaseOrderType: []
      },
      detailPurchaseNo: '',
      detailProductNo: '',
      formParam: {},
      activeName: 'first',
      dialogSalesOrderTableVisible: false,
      dialogStockoutTableVisible: false,

      validFlag: true, // 表单验证 true-通过，false-未通过
      list: [],
      list2: [],
      list3: [],
      list4: [],
      list5: [],
      total: 0,
      total2: 0,
      total3: 0,
      total4: 0,
      total5: 0,
      listQuery: {
        pageSize: 10,
        current: 1,
        pageSize2: 10,
        current2: 1,
        pageSize3: 10,
        current3: 1,
        pageSize4: 10,
        current4: 1,
        pageSize5: 10,
        current5: 1
      },
      orderTypeOption: orderTypeOption,
      purchaseStatusOption: purchaseStatusOption
    }
  },

  computed: {
    buyerDisabled () {
      return false
    },
    purchaseNoDisabled () {
      return this.activeName === 'first'
    },
    purchaseStatusDisabled () {
      return this.activeName === 'first'
    },
    productNoDisabled () {
      return false
    },
    salesOrderNoDisabled () {
      return !!(
        this.searchForm.buyer ||
        this.searchForm.purchaseNo ||
        this.searchForm.productNo
      )
    },
    purchaseOrderTypeDisabled () {
      return this.activeName === 'first'
    }
  },
  watch: {
    activeName: function () {
      this.handleFilter()
    },
    'searchForm.salesOrderNo': function (newValue) {
      if (newValue !== '') {
        this.searchForm.purchaseStatus = 0
        this.searchForm.purchaseOrderType = []
      }
    }
  },
  created () {
    this.initData()
  },

  methods: {
    // 跳转到异常未下单列表
    toAbnormalList () {
      this.$router.push('/purchase/abnormalNoOrder?tab=gr')
    },
    initData () {
      this.getList()
    },
    getSupplyStatusClass: getSupplyStatusClass,
    getStatusNameClass: getStatusNameClass,

    getSONDetail () {
      this.listLoading5 = true
      const param = {
        pageSize: this.listQuery.pageSize5,
        current: this.listQuery.current5
      }
      purchaseSODetail(this.searchForm.salesOrderNo, param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.listLoading5 = response.data
            this.total5 = response.totalCount || 0
            this.listLoading5 = false
          } else {
            this.$notify.error(response.msg)

            // 报错时清空数据
            this.list5 = []
            this.total5 = 0
            this.listLoading5 = false
          }
        })
        .catch(e => {
          this.listLoading5 = false
        })
    },
    getList () {
      if (this.searchForm.salesOrderNo) {
        this.getSONDetail()
      } else {
        const activeName = this.activeName
        let httpRequest
        if (activeName === 'first') {
          httpRequest = purchaseList
          this.listLoading = true
        } else {
          httpRequest = unPurchaseList
          this.listLoading2 = true
        }

        this.dealFormParam()
        const param = { ...this.formParam }
        httpRequest(param)
          .then(response => {
            if (response.code === 200 && response.data) {
              if (activeName === 'first') {
                this.list = response.data
                this.total = response.totalCount || 0
                this.listLoading = false
              } else {
                this.list2 = response.data
                this.total2 = response.totalCount || 0
                this.listLoading2 = false
              }
            } else {
              this.$notify.error(response.msg)
              if (activeName === 'first') {
                // 报错时清空数据
                this.list = []
                this.total = 0
                this.listLoading = false
              } else {
                // 报错时清空数据
                this.list2 = []
                this.total2 = 0
                this.listLoading2 = true
              }
            }
          })
          .catch(e => {
            if (activeName === 'first') {
              this.listLoading = false
            } else {
              this.listLoading2 = false
            }
          })
      }
    },
    handleFilter (force) {
      if (this.validFlag) {
        if (
          force === 1 ||
          (this.activeName === 'first' && this.list.length === 0) ||
          (this.activeName !== 'first' && this.list2.length === 0)
        ) {
          if (this.activeName === 'first') {
            this.listQuery.current = 1
          } else {
            this.listQuery.current2 = 1
          }
          this.getList()
        }
      }
    },
    // 处理搜索表单数据
    dealFormParam () {
      this.formParam = JSON.parse(JSON.stringify(this.searchForm))
      // 采购进度选择全部时，删除此入参
      if (this.formParam.statusCode === 0) {
        delete this.formParam.statusCode
      }

      if (this.activeName !== 'first') {
        delete this.formParam.statusCode
        delete this.formParam.purchaseNo
        // delete this.formParam.purchaseOrderType
      }
      delete this.formParam.purchaseOrderType

      // 2.多选订单类型orderTypes数组改为逗号分隔
      this.formParam.purchaseOrderType =
        this.searchForm.purchaseOrderType &&
        this.searchForm.purchaseOrderType.join(',')

      if (this.activeName === 'first') {
        this.formParam.pageSize = this.listQuery.pageSize
        this.formParam.current = this.listQuery.current
      } else {
        this.formParam.pageSize = this.listQuery.pageSize2
        this.formParam.current = this.listQuery.current2
      }
    },
    getList3 () {
      this.dialogSalesOrderTableVisible = true
      this.listLoading3 = true
      const param = {
        purchaseNo: this.detailPurchaseNo,
        sapOrderNo: this.soVoucherId,
        pageSize: this.listQuery.pageSize3,
        current: this.listQuery.current3
      }
      purchaseDetail(this.detailProductNo, param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list3 = response.data
            this.total3 = response.totalCount || 0
            this.listLoading3 = false
          } else {
            this.$notify.error(response.msg)

            // 报错时清空数据
            this.list3 = []
            this.total3 = 0
            this.listQuery.current3 = 1
            this.listLoading3 = false
          }
        })
        .catch(e => {
          this.listLoading3 = false
        })
    },
    getList4 () {
      this.dialogStockoutTableVisible = true
      this.listLoading4 = true
      const param = {
        sapOrderNo: this.soVoucherId,
        pageSize: this.listQuery.pageSize4,
        current: this.listQuery.current4
      }
      unPurchaseDetail(this.detailProductNo, param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list4 = response.data
            this.total4 = response.totalCount || 0
            this.listLoading4 = false
          } else {
            this.$notify.error(response.msg)

            // 报错时清空数据
            this.list4 = []
            this.listQuery.current4 = 1
            this.total4 = 0
            this.listLoading4 = false
          }
        })
        .catch(e => {
          this.listLoading4 = false
        })
    },
    // 跳转到订单详情
    toDtl (row) {
      this.listLoading3 = false
      this.list3 = []
      this.listQuery.current3 = 1
      this.total3 = 0
      this.detailPurchaseNo = row.purchaseNo
      this.detailProductNo = row.productNo
      this.getList3()
    },
    // 跳转到订单详情
    toDtl2 (row) {
      this.listLoading4 = false
      this.list4 = []
      this.listQuery.current4 = 1
      this.total4 = 0
      this.detailProductNo = row.productNo

      this.getList4()
    },
    toOrderDetail (row) {
      this.dialogSalesOrderTableVisible = false
      this.dialogStockoutTableVisible = false
      this.$nextTick(() => {
        this.$router.jumpToSoOrderDetail({
          query: {
            sapOrderNo: row.salesOrderNo,
            refresh: true
          },
          hash: {
            point: 'cg'
          }
          // path: `/orderSale/formal/detail?soNo=&soVoucherId=${row.salesOrderNo}&refresh=true#point=cg`
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.customer-tips {
  background: #ffffff;
  margin-top: 20px;
  padding-left: 0;

  span {
    font-size: 12px;
    margin-right: 20px;
  }

  button {
    margin-right: 10px;
  }
}

.filter-container {
  padding-top: 10px;
  background-color: #f4f4f4;

  .el-form-item {
    margin-bottom: 10px;
  }
}

.pagination-container {
  text-align: right;
}
</style>
