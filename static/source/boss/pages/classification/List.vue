<template>
  <div class="app-container page-classification-list" :class="{'isWindows': isWindows}" v-loading="loading">
    <div class="filter-container">
      <el-form :inline="true">
        <el-form-item label="商品">
          <el-input v-model.trim="filter.skus" placeholder="请输入商品SKU" @change="afterChangeFilter"></el-input>
        </el-form-item>
        <el-form-item label="来源">
          <el-select v-model="filter.sourceType" placeholder="请选择来源" @change="afterChangeFilter">
            <el-option label="自动-指定仓" value="自动-指定仓"></el-option>
            <el-option label="人工" value="人工"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工厂">
          <el-select v-model="filter.factory" placeholder="请选择工厂" @change="afterChangeFilter">
            <el-option
              v-for="item in factoryList"
              :key="item.factory"
              :label="item.factory + ' ' + item.factoryName"
              :value="item.factory"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="reset">重置</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="module-list">
      <div class="btn-group">
          <el-upload
            action="/api-ab/sku-classification/operating/upload"
            :show-file-list="false"
            :on-success="onUploadSuccess"
            :before-upload="beforeUpload"
            :on-error="onUploadError"
            accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            style="display:inline-block"
            name="excelFile">
            <el-button type="primary" style="margin-right:5px;" v-if="isManager">导入</el-button>
          </el-upload>
          <el-button type="primary" @click="exportList" v-if="isManager">导出</el-button>
          <el-button type="primary" @click="toDetail(null, '新增级库存', false)" v-if="isManager">新增</el-button>
      </div>

      <el-table :data="list" style="width: 100%" border>
        <el-table-column show-overflow-tooltip prop="sku" label="SKU" width="100px"></el-table-column>
        <el-table-column show-overflow-tooltip prop="skuName" label="商品名称"></el-table-column>
        <el-table-column show-overflow-tooltip prop="brandName" label="品牌"></el-table-column>
        <el-table-column show-overflow-tooltip prop="materialGroupName" label="物料组"></el-table-column>
        <el-table-column show-overflow-tooltip prop="sourceType" label="来源" width="100px"></el-table-column>
        <el-table-column show-overflow-tooltip prop="factory" label="工厂编号" width="80px"></el-table-column>
        <el-table-column show-overflow-tooltip prop="factoryName" label="工厂名称"></el-table-column>
        <el-table-column fixed="right" label="操作" width="120px">
          <template slot-scope="{ row }">
            <el-button type="text" @click="toDetail(row, '查看级库存', true)">查看</el-button>
            <el-button v-if="row.sourceType == '人工' && isManager" type="text" @click="toDetail(row, '编辑级库存', false)">编辑</el-button>
            <el-button v-if="row.sourceType == '人工' && isManager" type="text" @click="deleteItem(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.pageNum"
        :page-sizes="[10, 20, 50]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
    <el-dialog
      v-if="dialogVisible"
      :title="dialogData.title"
      :show-close="true"
      :visible.sync="dialogVisible"
      :destroy-on-close="true"
      top="10px"
      width="1250px"
    >
      <EditDialog
        :dialogData.sync="dialogData"
        :factoryList="factoryList"
        :search="search"
        :cancel="cancel"
      />
    </el-dialog>
  </div>
</template>

<script>
import { queryFactoryList, queryClassificationList, deleteClassification, exportClassification } from '@/api/classification.js'
import { mapState } from 'vuex'
import EditDialog from './EditDialog.vue'

export default {
  data () {
    return {
      factoryList: [],
      loading: false,
      filter: {
        factory: '',
        skus: '',
        sourceType: '',
        pageNum: 1,
        pageSize: 10
      },
      list: [],
      total: 0,
      dialogData: {},
      dialogVisible: false,
      isWindows: /windows|win32|win64/i.test(window.navigator.userAgent)
    }
  },
  components: { EditDialog },
  computed: {
    ...mapState([
      'userRole'
    ]),
    isManager () {
      return !!~this.userRole.indexOf('级库存管理员')
    }
  },
  watch: {
    dialogVisible(val, oldVal) {
      if (!val) {
        this.dialogData = {}
        this.skuList = []
      }
    }
  },
  methods: {
    getFactoryList() {
      queryFactoryList().then((res) => {
        if (res.status === 200) {
          this.factoryList = res.result || []
        } else {
            this.$message.error(res.msg || res.message || '请求失败！')
        }
      })
    },
    search () {
      this.loading = true
      queryClassificationList(this.filter).then((res) => {
        if (res.code === 200 && res.data) {
          this.list = res.data
          this.total = res.total
        } else {
          this.$message.error(res.msg || res.message || '操作失败！')
        }
        this.loading = false
      })
    },
    reset() {
      this.filter = {
        factory: '',
        skus: '',
        sourceType: '',
        pageNum: this.filter.pageNum,
        pageSize: this.filter.pageSize
      }
    },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.filter.pageNum = 1
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.pageNum = res
      this.search()
    },
    afterChangeFilter () {
      this.filter.pageNum = 1
    },
    beforeUpload (file) {
      this.loading = true
      return this.$validateFileType(file)
    },
    onUploadSuccess (response) {
      this.loading = false
      if (response && response.code === 200) {
        this.$message.success(response.data || '导入成功！')
      } else {
        this.$message.error((response && response.msg) || '导入失败！')
      }
    },
    onUploadError (error) {
      this.loading = false
      this.$message.error((error && error.msg) || (error && error.message) || '上传失败')
    },
    // 导出
    async exportList() {
      this.loading = true
      let res = await exportClassification(this.filter)
      this.loading = false
      if (res && res.code === 200) {
        this.$message.success(res.data)
      } else {
        this.$message.error(res.msg || res.message || '导出失败！')
      }
    },
    // 删除列表数据
    deleteItem(row) {
      this.$confirm('是否要删除该数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        type: 'warning',
        center: true
      }).then(() => {
        deleteClassification([{ factory: row.factory, sku: row.sku }]).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功！')
            this.search()
          } else {
            this.$message.error(res.msg || '删除失败！')
          }
        })
      })
    },
    // 新增编辑查看
    toDetail(data, title, disabled) {
      this.dialogVisible = true
      data = data || {
        sku: '',
        skuName: '',
        factory: '',
        sourceType: '人工',
        classificationWhsList: [{ type: 'add' }]
      }
      this.dialogData = {
        sku: data ? data.sku : '',
        skuName: data ? data.skuName : '',
        factory: data ? data.factory : '',
        sourceType: data ? data.sourceType : '人工',
        classificationWhsList: (data && data.classificationWhsList && data.classificationWhsList.length) ? [...data.classificationWhsList] : [{ type: 'add' }],
        title,
        disabled
      }
    },
    cancel() {
      this.dialogVisible = false
      this.dialogData = {}
      this.skuList = []
    }
  },
  mounted () {
    this.getFactoryList()
    this.search()
  }
}
</script>

<style lang="less" src="@/style/component.less"></style>
<style lang="scss">
.page-classification-list {
  padding: 20px;
  .el-table table thead th {
    border-right: 1px solid #e2e2e2;
  }
  .btn-group {
    text-align: right;
    margin-bottom: 10px;
  }
}

.isWindows {
  .el-table__fixed {
    height: auto !important;
    bottom: 16px;
  }
}
.classification-dialog {
  .sub-title {
    font-size: 16px;
    color: #333;
    text-indent: 20px;
    background-color: #ccc;
    height: 32px;
    line-height: 32px;
    margin-bottom: 20px;
  }
  .classification-add {
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
