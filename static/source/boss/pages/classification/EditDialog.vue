<template>
  <div class="classification-dialog">
    <el-form
      label-width="100px"
      :disabled="dialogData.title !== '新增级库存'"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="商品编号">
            <el-select
              v-model="dialogData.sku"
              filterable
              remote
              value-key="skuNo"
              placeholder="请输入商品编号/名称"
              :remote-method="getSkuList"
              :loading="skuNoLoading"
              style="width: 100%"
              @change="changeSku"
            >
              <el-option
                style="width: 500px"
                v-for="item in skuList"
                :key="item.skuNo"
                :label="`${item.skuNo} ${item.materialDescribe}`"
                :value="item.skuNo"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="商品名称">
            <el-input
              v-model.trim="dialogData.skuName"
              :disabled="true"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="工厂">
            <el-select
              v-model="dialogData.factory"
              placeholder="请选择工厂"
              style="width: 100%"
              @change="changeFactory"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factory"
                :label="item.factory + ' ' + item.factoryName"
                :value="item.factory"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="来源">
            <el-input
              v-model.trim="dialogData.sourceType"
              :disabled="true"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="sub-title">级库存</div>
    <div class="classification-add" v-if="!dialogData.disabled">
      <el-button type="primary" @click="addRow">新增</el-button>
    </div>
    <vxe-table
      max-height="420"
      size="small"
      border
      align="center"
      :data="dialogData.classificationWhsList"
      :scroll-x="{ gt: -1 }"
      :scroll-y="{ gt: 10 }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        showStatus: true,
        enabled: true,
      }"
    >
      <vxe-table-column field="index" title="序号" width="100px">
        <template #default="{ rowIndex }">{{rowIndex + 1}}</template>
      </vxe-table-column>
      <vxe-table-column field="upWarehouseCode" title="上级仓编号">
        <template #default="{ row, rowIndex }">
          <span v-if="row.type == 'add'">
            <el-select
              v-model="row.upWarehouseCode"
              placeholder="请选择上级仓"
              filterable
              style="width: 100%"
              @change="
                (data) => changeWarehouse(data, rowIndex, 'upWarehouseName')
              "
            >
              <el-option
                v-for="item in warehouseList"
                :key="item.warehouseCode"
                :label="item.warehouseName"
                :value="item.warehouseCode"
              >
              </el-option>
            </el-select>
          </span>
          <span v-else>{{ row.upWarehouseCode }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="upWarehouseName"
        title="上级仓名称"
      ></vxe-table-column>
      <vxe-table-column field="warehouseCode" title="下级仓编号">
        <template #default="{ row, rowIndex }">
          <span v-if="row.type == 'add'">
            <el-select
              v-model="row.warehouseCode"
              placeholder="请选择上级仓"
              filterable
              style="width: 100%"
              @change="
                (data) => changeWarehouse(data, rowIndex, 'warehouseName')
              "
            >
              <el-option
                v-for="item in warehouseList"
                :key="item.warehouseCode"
                :label="item.warehouseName"
                :value="item.warehouseCode"
              >
              </el-option>
            </el-select>
          </span>
          <span v-else>{{ row.warehouseCode }}</span>
        </template>
      </vxe-table-column>
      <vxe-table-column
        field="warehouseName"
        title="下级仓名称"
      ></vxe-table-column>
      <vxe-table-column
        title="操作"
        v-if="!dialogData.disabled"
        width="160px"
      >
        <template #default="{ rowIndex }">
          <el-button type="text" @click="deleteRow(rowIndex)">删除</el-button>
        </template>
      </vxe-table-column>
    </vxe-table>
    <div class="module-btns center">
      <el-button v-if="!dialogData.disabled" type="primary" @click="submit" :loading="loading">提交</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
import { getwWarehouseCode, searchSkuList, upsertBatchClassification } from '@/api/classification.js'

export default {
  name: 'classification-dialog',
  data() {
    return {
      skuList: [],
      skuNoLoading: false,
      warehouseList: [],
      loading: false
    };
  },
  props: {
    dialogData: {
      type: Object,
      default: () => ({})
    },
    factoryList: {
      type: Array,
      default: () => []
    },
    search: {
      type: Function,
      default: function () {}
    },
    cancel: {
      type: Function,
      default: function () {}
    }
  },
  mounted() {
    this.getwWarehouseList()
  },
  methods: {
    // 获取商品编号list
    getSkuList(query) {
      const key = query.trim()
      if (key !== '') {
        this.skuNoLoading = true
        searchSkuList(key).then(res => {
          this.skuNoLoading = false
          if (res.code === 200 && res.data) {
            if (res.data.length > 0) {
              this.skuList = res.data
            } else {
              this.skuList = []
            }
          } else {
            this.skuList = []
          }
        })
      } else {
        this.skuList = []
      }
    },
    changeSku(data) {
      let item = this.skuList.find((a) => a.skuNo === data)
      item && (this.dialogData.skuName = item.materialDescribe)
      this.getwWarehouseList()
    },
    changeFactory(data) {
      let item = this.skuList.find((a) => a.factory === data)
      item && (this.dialogData.factoryName = item.factoryName)
      this.getwWarehouseList()
    },
    // 获取仓
    getwWarehouseList() {
      if (!this.dialogData.factory || !this.dialogData.sku) {
        this.warehouseList = []
        return
      }
      getwWarehouseCode({ factorySet: [this.dialogData.factory], skuSet: [this.dialogData.sku] }).then((res) => {
        if (res.status === 200 && res.result) {
          this.warehouseList = res.result
        }
      })
    },
    changeWarehouse(data, index, name) {
      let item = this.warehouseList.find((a) => a.warehouseCode === data)
      item && (this.dialogData.classificationWhsList[index][name] = item.warehouseName)
    },
    // 弹框增加上级仓下级仓
    addRow() {
      this.dialogData.classificationWhsList.push({ type: 'add' })
    },
    // 弹框删除上级仓下级仓
    deleteRow(index) {
      if (this.dialogData.classificationWhsList.length === 1) {
        this.$message.warning('仓库信息至少存在一行！')
        return
      }
      this.dialogData.classificationWhsList.splice(index, 1)
    },
    submit() {
      if (!this.dialogData.sku) {
        this.$message.warning('请选择商品！')
        return
      }
      if (!this.dialogData.factory) {
        this.$message.warning('请选择工厂！')
        return
      }
      let index = this.dialogData.classificationWhsList.findIndex((a) => !a.warehouseCode || !a.upWarehouseCode)
      if (index > -1) {
        this.$message.warning(`第${index + 1}行信息填写不完全！`)
        return
      }
      let params = this.dialogData.classificationWhsList.map((item) => {
        return {
          factory: this.dialogData.factory,
          sku: this.dialogData.sku,
          upWarehouseCode: item.upWarehouseCode,
          warehouseCode: item.warehouseCode
        }
      })
      upsertBatchClassification(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('保存成功！')
          this.cancel()
          this.search()
        } else {
          this.$message.error(res.msg || '保存失败！')
        }
      })
    }
  }
};
</script>

<style lang="less" src="@/style/component.less"></style>
<style lang="scss">
.classification-dialog {
  .module-btns {
    margin: 20px 0;
  }
}
</style>
