// OUTSOURCING_IN("20", "委外发料单"),
// OUTSOURCING_OUT("21", "委外退料单"),
// PRE_RECEIVE("22","预领用单"),
// COST_IN("18", "费用性领料单"),
// COST_OUT("19", "费用性退料单"),
// RETURN("28", "采购退货单"),
// CONSIGNMENT_TRANSFER("30", "寄售调拨单"),
// PRE_TRANSFER("31", "预调拨单"),
// WAREHOUSE_INNER_TRANSFER("13", "库内调拨单"),
// FACTORY_TRANSFER("29", "工厂间调拨单"),
// INVENTORY_ADJUST("27", "库存调整单"),
// INVENTORY_DUMP("23", "库存报废单");
// 32 寄售盘亏单
// 33 EVM盘点单
// 34 坤合盘点单
// 35 盘点单
/**
 * baseinfo为创建页&编辑页的配置
 * table为创建&编辑页&详情页的配置
 */
export const columnSettings = {
  type20: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', '工厂', '仓库地点', '批次',
      '交货数量', '已过账数量', '单位', '需求数量',
      '参考单号', '参考行号', '项目文本'
    ]
  },
  type21: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', '工厂', '仓库地点', '批次',
      '交货数量', '已过账数量', '单位', '需求数量',
      '参考单号', '参考行号', '项目文本'
    ]
  },
  type22: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', '工厂', '仓库地点', '批次',
      '交货数量', '单位', '总账科目', '成本中心', '项目文本'
    ]
  },
  type18: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '来源系统', '来源系统单号', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, { name: '仓库地点', disabled: 0 }, '批次',
      { name: '交货数量', disabled: 0 }, '已过账数量', 'OA流程编号', 'OA行号', 'OA类型', '单位', { name: '总账科目', disabled: 0 }, { name: '成本中心', disabled: 0 }, '项目文本'
    ]
  },
  type19: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次',
      { name: '交货数量', disabled: 0 }, '已过账数量', '单位',
      { name: '总账科目', disabled: 0 }, { name: '成本中心', disabled: 0 }, '项目文本'
    ]
  },
  type28: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', { name: 'SKU编码', disabled: 1 }, '物料描述', '工厂', { name: '仓库地点', disabled: 1 }, '批次',
      '交货数量', '已过账数量', { name: '单位', prop: 'unit' }, '需求数量',
      '参考单号', '参考行号', '项目文本', '资产卡片号'
    ]
  },
  type30: {
    baseInfo: [{ name: '供应商', disabled: 0, type: 'custom' }, '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '接收仓库地点', '批次',
      '交货数量', '已过账数量', '单位', '项目文本'
    ]
  },
  type31: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '接收仓库地点', '批次',
      '交货数量', '已过账数量', '单位', '项目文本'
    ]
  },
  type13: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '接收仓库地点', '批次',
      '交货数量', '已过账数量', '单位', '项目文本'
    ]
  },
  type29: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '接收工厂', '接收仓库地点', '批次',
      '交货数量', '已过账数量', '单位', '项目文本'
    ]
  },
  type27: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量', '已过账数量',
      '单位', '接收SKU编码', '接收物料描述', '项目文本'
    ]
  },
  type23: {
    baseInfo: [
      '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', { name: '责任归属', prop: 'responsibilityOwner', required: true }, '抬头备注'
    ],
    table: [
      '项目行', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量', '已过账数量',
      '单位', '成本中心', '项目文本'
    ]
  },
  type32: {
    baseInfo: [
      '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '流程编号', '抬头备注'
    ],
    table: [
      '项目行', '状态', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '寄售客户', '批次', '批次数量', '交货数量', '批次剩余数量',
      '交货金额', '已过账数量', '项目文本'
    ]
  },
  type14: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', '状态', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量',
      '已过账数量', '单位', '项目文本'
    ]
  },
  type15: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', '状态', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量',
      '已过账数量', '单位', '项目文本'
    ]
  },
  type16: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', '状态', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量',
      '已过账数量', '单位', '项目文本'
    ]
  },
  type17: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', '状态', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量',
      '已过账数量', '单位', '项目文本'
    ]
  },
  type33: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', '状态', '客户编码', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量',
      '已过账数量', '单位', '项目文本'
    ]
  },
  type34: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '关联单据', '供应商', '抬头备注'],
    table: [
      '项目行', '状态', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '批次', '交货数量',
      '已过账数量', '单位', '项目文本'
    ]
  },
  type35: {
    baseInfo: ['单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '供应商', '抬头备注'],
    table: [
      '项目行', '状态', 'SKU编码', '物料描述', { name: '工厂', disabled: 0 }, '仓库地点', '库存类型', '交货数量',
      '已过账数量', '单位', '批次', '项目文本'
    ]
  }
}

export const detailColumnSettings = {
  columnSetting: [
    '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '来源系统', '来源系统单号', '抬头备注',
    { name: '坤合返回消息', span: 16 }, 'sim返回消息', { name: 'SAP返回消息', span: 24 }
  ],
  columnSetting23: [
    '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '抬头备注',
    { name: '责任归属', prop: 'responsibilityOwner', required: true },
    { name: '坤合返回消息', span: 16 }, 'sim返回消息', { name: 'SAP返回消息', span: 24 }
  ],
  columnSetting32: [
    '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '来源系统', '来源系统单号', '抬头备注', '流程编号',
    { name: '坤合返回消息', span: 16 }, 'sim返回消息', { name: 'SAP返回消息', span: 24 }
  ]
}

export function getColumnSetting(type, routerType = 'create', partType = 'table') {
  let setting = columnSettings[`type${type}`][partType]
  if (/detail|edit/gi.test(routerType) && partType === 'table' && type !== '32') {
    if (setting[1] !== '状态') {
      setting.splice(1, 0, '状态')
    }
  }
  if (/create/gi.test(routerType)) {
    setting = setting.filter(item => item !== '已过账数量')
  }
  if (/detail/gi.test(routerType) && partType === 'baseInfo') {
    setting = setting.filter(item => item !== '关联单据')
  }
  return setting
}
