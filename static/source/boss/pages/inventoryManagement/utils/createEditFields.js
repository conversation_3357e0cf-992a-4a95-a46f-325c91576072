export const createFields = [
  'createTime',
  'createUser',
  'itemList',
  'orderRemark',
  'orderType',
  'source',
  'sourceOrderNo',
  'supplierNo',
  'transportAddressDetail',
  'transportCity',
  'transportContactName',
  'transportContactNumber',
  'transportDirection',
  'transportPostCode',
  'transportProvince',
  'transportRegion',
  'transportStreet',
  'zkhAddressDetail',
  'zkhCity',
  'zkhContactName',
  'zkhContactNumber',
  'zkhPostCode',
  'zkhProvince',
  'zkhRegion',
  'responsibilityOwner',
  'zkhStreet'
]
export const updateFields = [
  'itemList',
  'orderNo',
  'orderRemark',
  'transactionId',
  'transportAddressDetail',
  'transportCity',
  'transportContactName',
  'transportContactNumber',
  'transportDirection',
  'transportPostCode',
  'transportProvince',
  'transportRegion',
  'transportStreet',
  'updateUser',
  'zkhAddressDetail',
  'zkhCity',
  'zkhContactName',
  'zkhContactNumber',
  'zkhPostCode',
  'zkhProvince',
  'zkhRegion',
  'responsibilityOwner',
  'zkhStreet'
]
export const itemListFields = [
  'oaNo',
  'oaItemNo',
  'oaType',
  'batchNo',
  'costCenter',
  'factoryCode',
  'generalLedgerAccount',
  'projectText',
  'quantity',
  'receiveFactoryCode',
  'receiveSkuNo',
  'receiveWarehouseLocation',
  'referItemNo',
  'referNo',
  'skuNo',
  'warehouseLocation',
  'customerCode',
  'batchQuantity'
]
export const updateItemListFields = [
  'oaNo',
  'oaItemNo',
  'oaType',
  'batchNo',
  'costCenter',
  'factoryCode',
  'generalLedgerAccount',
  'isDeleted',
  'itemNo',
  'projectText',
  'quantity',
  'receiveBatchNo',
  'receiveFactoryCode',
  'receiveSkuNo',
  'receiveWarehouseLocation',
  'referItemNo',
  'referNo',
  'skuNo',
  'warehouseLocation',
  'customerCode',
  'batchQuantity'
]

// https://wiki.zkh360.com/confluence/pages/viewpage.action?pageId=*********#id-%E5%BA%93%E5%AD%98%E7%94%B3%E8%AF%B7%E5%8D%95%E4%B8%80%E8%87%B4%E6%80%A7%E6%94%B9%E9%80%A0-12.%E9%80%9A%E7%94%A8%E7%9A%84%E5%BA%93%E5%AD%98%E7%94%B3%E8%AF%B7%E5%8D%95%E6%8D%AE%E5%87%AD%E8%AF%81
export const cantCreateIaoType = ['14', '15', '16', '17']
