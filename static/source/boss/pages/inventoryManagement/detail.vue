<template>
  <div class="inventory-detail" v-loading="pageLoading">
    <div class="card-wrapper">
      <div class="tab">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="基本信息" name="baseInfo"></el-tab-pane>
          <el-tab-pane label="通讯信息" name="contact"></el-tab-pane>
          <el-tab-pane label="物流信息" name="express"></el-tab-pane>
        </el-tabs>
      </div>
      <div class="card-container">
          <div class="card-title">
            <BaseInfo v-if="activeName === 'baseInfo'" :finalFields="finalFields" :detail="detail" :getInvDetail="getInvDetail" :updateLoading="updateLoading"/>
            <ContactInfo v-if="activeName === 'contact'" :finalFields="finalFields" :detail="detail"/>
            <ExpressInfo v-if="activeName === 'express'" :finalFields="finalFields" :detail="detail" :expressData="expressData" :updateExpressData="updateExpressData"/>
          </div>
      </div>
      <div class="card-container">
        <div class="card-table">
          <ItemTable
            class="table-body"
            :detail="detail"
            :finalFields="finalFields"
            :columnSetting="columnSettingTable"
            :costCenterList="costCenterList"
            @selectionChange="selectionChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import BaseInfo from './components/detail/title/baseInfo'
import ContactInfo from './components/detail/title/contactInfo'
import ExpressInfo from './components/detail/title/expressInfo'
import ItemTable from './components/detail/table/index'
import { mapState } from 'vuex'
import { getInvDetail, getCostCenter as getCostCenterApi } from '@/api/mm'
import { getAllDictList } from '@/utils/mm'
import { getColumnSetting } from './utils/tableColumnSetting'

export default {
  name: 'inventoryManagementDetail',
  components: {
    BaseInfo, ContactInfo, ExpressInfo, ItemTable
  },
  data () {
    return {
      pageLoading: false,
      activeName: 'baseInfo',
      rules: {},
      orderType: '',
      columnSettingTable: {},
      detail: {
        itemList: []
      },
      expressData: null,
      costCenterList: []
    }
  },
  async mounted() {
    if (!this.orderNo) {
      this.$message.error('缺少订单号！')
    }
    await this.getInvDetail()
    if (!this.fields[this.orderType] || !this.fields[this.orderType].length) {
      await this.$store.dispatch('inventoryManagement/getInvFields', {
        orderType: this.orderType,
        routerType: 'detail'
      })
    }
    await getAllDictList(this)
    this.pageLoading = false
  },
  computed: {
    ...mapState({
      fields: state => state.inventoryManagement.detail,
      dictList: state => state.orderPurchase.dictList
    }),
    finalFields() {
      let ret = []
      if (Array.isArray(this.fields[this.orderType])) {
        ret = this.fields[this.orderType]
      }
      return ret
    },
    orderNo () {
      return this.$route.params.id
    }
  },
  methods: {
    async getCostCenter(response) {
      if (response && response.itemList && response.itemList[0] && response.itemList[0].factoryCode) {
        return getCostCenterApi({
          companyCode: response.itemList[0].factoryCode,
          costCenter: ''
        }).then(data => {
          this.costCenterList = data
        })
      }
    },
    updateExpressData (data) {
      this.expressData = data
    },
    updateLoading (loading) {
      this.pageLoading = loading
    },
    async getInvDetail () {
      const param = {
        orderNo: this.orderNo
      }
      this.pageLoading = true
      let response = await getInvDetail(param)
      if (!response) {
        this.pageLoading = false
        return console.error('请求失败！')
      }
      await this.getCostCenter(response)
      this.columnSettingTable = getColumnSetting(response.orderType, 'detail', 'table')
      response.itemList.forEach(item => {
        item.supplierNo = response.supplierNo
        item.supplierName = response.supplierName
      })
      this.detail = response
      this.orderType = response.orderType
      this.pageLoading = false
    },
    handleClick (tab) {},
    selectionChange (tab) {}
  }
}
</script>
<style lang="scss" scoped>
.inventory-detail{
  @import './styles/card-layout.scss';
  .button-group{
    display: flex;
    justify-content:flex-end;
  }
}
</style>
