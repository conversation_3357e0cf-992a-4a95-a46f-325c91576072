<template>
  <div class="inventory-list" v-loading="loading.pageLoading">
    <el-form :model="searchForm" ref="searchForm" label-width="140px" label-suffix=":" :rules="rules">
      <el-col v-for="item in getColumns" :span="item.span || 8" :key="item.prop">
        <el-form-item :label="item.name" :prop="item.prop">
          <el-input
            v-if="item.type==='input'"
            v-model="searchForm[item.prop]"
            :disabled="Boolean(item.disabled)"
            :placeholder="setPlaceholder(item)"
            clearable
          />
          <el-input
            v-else-if="item.type==='textarea'"
            type="textarea"
            v-model="searchForm[item.prop]"
            :disabled="Boolean(item.disabled)"
            :placeholder="`请输入${item.name}`"
            clearable
          />
          <SelectSupplier
            ref="supplier"
            v-else-if="item.type==='custom' && item.prop === 'supplier'"
            :data.sync="searchForm[item.prop]"
            @change="handleSupplierChange"
            clearable
          />
          <el-date-picker
            v-else-if="item.type==='daterange'"
            v-model="searchForm[item.prop]"
            :disabled="Boolean(item.disabled)"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width:100%;margin-bottom:1px;"
            clearable />
          <el-select
            v-else-if="item.type==='select'"
            v-model="searchForm[item.prop]"
            filterable
            clearable
            :disabled="Boolean(item.disabled)"
            style="width:100%"
          >
            <el-option
              v-for="item in buildOptions(item.optionProp || item.prop, true)"
              :key="item.label"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <div class="button-group">
        <el-button :loading="loading.searchLoading" class="search-button" type="primary" @click="handleSearchInit">查询</el-button>
        <el-button class="reset-button" @click="resetForm">重置</el-button>
      </div>
    </el-form>
    <div class="button-group">
      <el-button v-if="getButtonAuth('库存申请单列表', '批量创建寄售盘亏单')" type="primary" @click="handleOpenBatchCreateDlg('32')">批量创建寄售盘亏单</el-button>
      <el-button v-if="getButtonAuth('库存申请单列表', '创建寄售盘亏单')" type="primary" @click="handleOpenCreateDlg('32')">创建寄售盘亏单</el-button>
      <el-button v-if="getButtonAuth('库存申请单列表', '批量创建申请单')" type="primary" @click="handleOpenBatchCreateDlg()">批量创建申请单</el-button>
      <el-button v-if="getButtonAuth('库存申请单列表', '创建申请单')" type="primary" @click="handleOpenCreateDlg()">创建申请单</el-button>
    </div>
    <ItemTable
      :loading="loading.tableLoading"
      :finalFields="fields"
      :itemList="table.itemList"
      @selectionChange="selectionChange"
    />
    <div class="pagination" v-if="table.total > -1">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="table.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="table.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="table.total">
      </el-pagination>
    </div>
    <CreateBatchOrderDialog
      v-if="createBatchOrder"
      :show-dialog.sync="createBatchOrder"
      :createBatchOrderType="createBatchOrderType"
      :buildOptions="buildOptions"
      @uploadSuccess="uploadSuccess"
    />
    <el-dialog
      title="创建库存申请单"
      :visible.sync="showCreate"
      :destroy-on-close="true"
      width="600px"
    >
      <div class="dialog-body">
        <p>单据类型：</p>
        <el-select v-model="createOrderType">
          <el-option
            v-for="item in createOrderTypeOptions"
            :key="item.name"
            :label="item.label"
            :value="item.value"
            >
          </el-option>
        </el-select>
      </div>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createInv">确定</el-button>
        <el-button @click="showCreate = false">取消</el-button>
      </p>
    </el-dialog>
    <ResultDlg
      :resultVisible="resultVisible"
      :resultData="resultData"
      :closeResultDlg="closeResultDlg"
    />
  </div>
</template>
<script>
import ItemTable from './components/list/table/index'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { mapState } from 'vuex'
import { safeRun, deepClone } from '@/utils/index'

import { sortColumn, getAllDictList } from '@/utils/mm'
import { inventorySearchApi } from '@/api/mm'
import CreateBatchOrderDialog from './components/list/BatchCreateDlg'
import ResultDlg from './components/list/ResultDlg'
import { getButtonAuth } from '@/utils/auth'
import { cantCreateIaoType } from './utils/createEditFields'

export default {
  name: 'inventoryManagementList',
  components: {
    ItemTable, SelectSupplier, CreateBatchOrderDialog, ResultDlg
  },
  data () {
    return {
      resultVisible: false,
      resultData: {},
      showCreate: false,
      orderOptions: [],
      createOrderType: '20',
      rules: {},
      table: {
        itemList: [],
        total: 0,
        pageNo: 1,
        pageSize: 20
      },
      loading: {
        pageLoading: false,
        searchLoading: false,
        tableLoading: false
      },
      searchForm: {
        certificateDate: undefined,
        createUser: undefined,
        orderStatus: undefined,
        orderType: undefined,
        postDate: undefined,
        sapOrderNo: undefined,
        supplier: undefined,
        supplierName: undefined,
        supplierNo: undefined,
        transportMessage: undefined,
        orderNoList: ''
      },
      columnSetting: [
        { name: '订单号检索', prop: 'orderNoList', span: 16 }, '单据类型', '单据状态', '供应商', '创建人', '凭证日期', '过账日期', '传输信息', '来源系统单号', '创建方式'
      ],
      selections: [],
      createBatchOrder: false,
      createBatchOrderType: ''
    }
  },
  async created() {
    this.loading.pageLoading = true
    await getAllDictList(this)
    await this.$store.dispatch('inventoryManagement/getListFields', 'list')
    this.handleSearch(true)
    this.loading.pageLoading = false
    this.initQuery()
  },
  computed: {
    ...mapState({
      fields: state => state.inventoryManagement.list,
      dictList: state => state.orderPurchase.dictList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.fields.filter(data => data.field === 'list-search')
        ret = sortColumn(ret, this.columnSetting)
      })
      return ret
    },
    createOrderTypeOptions () {
      const options = this.buildOptions('iaoType');
      if (this.createOrderType === '32') return options.filter(item => item.value === '32');
      return options.filter(item => !['32', '33', '34'].includes(item.value));
    }
  },
  methods: {
    getButtonAuth,
    initQuery() {
      const { orderNo } = this.$route.query;
      if (orderNo) {
        this.searchForm.orderNoList = orderNo;
      }
    },
    uploadSuccess (resultData) {
      this.resultData = resultData
      this.resultVisible = true
      this.handleSearch()
    },
    closeResultDlg () {
      this.resultData = {}
      this.resultVisible = false
    },
    setPlaceholder(item) {
      let placeholder = `请输入${item.name}`
      if (/orderNo/.test(item.prop)) {
        placeholder = '支持PMS申请单号、SAP申请单号、关联单据号搜索，按空格隔开，最多查询200个订单'
      }
      if (/createUser/.test(item.prop)) {
        placeholder = '域账号,如san.zhang'
      }
      return placeholder
    },
    handleSearchInit () {
      this.table.pageNo = 1
      this.handleSearch()
    },
    handleOpenBatchCreateDlg (type = '') {
      this.createBatchOrder = true
      this.createBatchOrderType = type;
    },
    handleOpenCreateDlg (type = '') {
      this.showCreate = true
      this.createOrderType = type || '20';
    },
    createInv () {
      this.showCreate = false
      this.$router.push(`/inventoryManagement/create/${this.createOrderType}`)
    },
    handleSizeChange (size) {
      this.table.pageSize = size
      this.handleSearch()
    },
    handleCurrentChange (pageNo) {
      this.table.pageNo = pageNo
      this.handleSearch()
    },
    selectionChange (rows) {
      this.selectionChange = rows
    },
    resetForm () {
      this.$refs.searchForm.resetFields()
      this.searchForm.supplierNo = undefined
      this.searchForm.supplierName = undefined
      this.searchForm.certificateDate = undefined
      this.searchForm.postDate = undefined
    },
    formatSubmit (data) {
      let ret = deepClone(data)
      ret.pageSize = this.table.pageSize
      ret.pageNo = this.table.pageNo
      if (Array.isArray(ret.certificateDate)) {
        ret.certificateDateBegin = ret.certificateDate[0]
        ret.certificateDateEnd = ret.certificateDate[1]
        delete ret.certificateDate
      }
      if (Array.isArray(ret.postDate)) {
        ret.postDateBegin = ret.postDate[0]
        ret.postDateEnd = ret.postDate[1]
        delete ret.postDate
      }
      if (ret.orderNoList) {
        ret.orderNoList = ret.orderNoList.split(/\s+|,|，/).filter(e => e)
      } else {
        ret.orderNoList = []
      }
      delete ret.supplier
      // 删除undefined null属性
      for (let prop in ret) {
        if (ret[prop] === undefined || ret[prop] === null) {
          delete ret[prop]
        }
      }
      return ret
    },
    handleSearch(isInit) {
      let isNoSearch = false
      if (isInit) {
        Object.keys(this.searchForm).forEach(key => {
          if (this.searchForm[key]) {
            isNoSearch = true
          }
        })
        if (!isNoSearch) {
          return
        }
      }
      const data = this.formatSubmit(this.searchForm)
      if (data.orderNoList.length > 200) {
        return this.$message.error('最多输入200个单号！')
      }
      this.loading.searchLoading = true
      this.loading.tableLoading = true
      inventorySearchApi(data)
        .then(res => {
          if (res) {
            this.table.total = res.total
            this.table.itemList = res.rows
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '查询失败！')
        })
        .finally(() => {
          this.loading.searchLoading = false
          this.loading.tableLoading = false
        })
    },
    handleSupplierChange (supplier) {
      if (!supplier) {
        supplier = { supplierNo: '', supplierName: '' }
      }
      this.searchForm.supplierNo = supplier.supplierNo
      this.searchForm.supplierName = supplier.supplierName
    },
    buildOptions (prop, showCantCreateIaoType = false) {
      if (prop === 'orderType') prop = 'iaoType'
      if (prop === 'orderStatus') prop = 'iaoStatus'
      if (prop === 'createWay') prop = 'iaoCreateWay'
      let ret = []
      safeRun(() => {
        if (!this.dictList[prop]) return
        ret = this.dictList[prop].map(prop => ({
          value: prop.value,
          label: prop.name
        }))
      })
      if (prop === 'iaoType' && !showCantCreateIaoType) {
        ret = ret.filter(item => !cantCreateIaoType.includes(item.value))
      }
      return ret
    }
  }
}
</script>
<style lang="less" scoped>
.inventory-list{
  min-height: 500px;
  margin: 10px;
  margin-right: 25px;
  padding-bottom: 50px;
  .search-button{
    margin-left: 20px;
  }
  .button-group{
    width: 100%;
    display: flex;
    justify-content:flex-end;
    margin: 10px 0px;
  }
  .pagination{
    margin-top: 20px;
    float:right;
  }
  .dialog-body{
    display: flex;
    align-items: center;
    p{
      margin-left: 80px;
    }
  }
}
</style>
