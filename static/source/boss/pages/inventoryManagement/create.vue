<template>
  <div class="inventory-create" v-loading="pageLoading">
    <div class="card-wrapper">
      <el-form :model="createData" ref="createForm" label-width="140px" label-suffix=":" :rules="rules">
        <div class="tab">
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="基本信息" name="baseInfo"></el-tab-pane>
            <el-tab-pane label="通讯信息" name="contact"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="card-container">
            <div class="card-title">
              <BaseInfo
                v-show="activeName === 'baseInfo'"
                :finalFields="finalFields"
                :createData.sync="createData"
                :getRelateOrder="getRelateOrder"
                :tableLoading="tableLoading"
                :disableRelateSku="disableRelateSku"
                :columnSetting="columnSettingBase"
                :isEdit="isEdit"
              />
              <ContactInfo
                v-show="activeName === 'contact'"
                ref="contactInfo"
                :disableContact="disableContact"
                :transportOptions="transportOptions"
                :zkhOptions="zkhOptions"
                :clearValidate="clearValidate"
                :finalFields="finalFields"
                :createData="createData"/>
            </div>
        </div>
      </el-form>
      <div class="card-container">
        <div class="card-table">
          <div class="table-header">
            <div class="button-group">
              <el-button type="primary" :loading="autoBatchLoading" :disabled="disableBatchButton" @click="autoBatch">批次自动分配</el-button>
              <el-button type="primary" :disabled="disableCopyLine" @click="copyLine">复制项目行</el-button>
              <el-button type="primary" v-if="showImportButton" @click="showBatchImport">快速导入</el-button>
              <el-button type="danger" @click="toggleLine">删除/恢复项目行</el-button>
            </div>
          </div>
          <ItemTable
            class="table-body"
            ref="itemTable"
            @selectionChange="selectionChange"
            @addItem="addItem"
            @updateWarehouseLocation="updateWarehouseLocation"
            @updateFactoryCode="updateFactoryCode"
            @updateCustomer="updateCustomer"
            :createData="createData"
            :isEdit="isEdit"
            :batchData="batchData"
            :finalFields="finalFields"
            :itemList="createData.itemList"
            :updateBatchVisible="updateBatchVisible"
            :updateBatchNo="updateBatchNo"
            :batchVisible="batchVisible"
            :batchLoading="batchLoading"
            :tableLoading="tableLoading"
            :columnSetting="columnSettingTable"
            :disabledBySupplier="disabledBySupplier"
            :isKunhe="isKunhe"
            :filterByProp="filterByProp"
            :canChooseBatch="canChooseBatch"
            :costCenterList="costCenterList"
          />
        </div>
      </div>
    </div>
    <div class="fixed-create" ref="fixedCreate">
      <div class="btn-group">
        <el-button v-if="isEdit" type="primary" @click="cancelSubmit('orderForm')" style="width:100px">取消修改</el-button>
        <el-button type="primary" @click="handleSubmit('orderForm')" style="width:100px">
          {{isEdit ? '保存修改' : '确认提交' }}
        </el-button>
      </div>
    </div>
    <BatchImport
      :visible.sync="batchImportVisible"
      :orderType="orderType"
      @import="handleBatchImport"
    />
  </div>
</template>
<script>
import BaseInfo from './components/create/title/BaseInfo'
import ContactInfo from './components/create/title/ContactInfo'
import BatchImport from './components/create/batchImport'
import ItemTable from './components/create/table/index'
import { mapState } from 'vuex'
import { safeRun, deepClone } from '@/utils/index'
import * as shortid from 'shortid'
import padStart from 'lodash/padStart'
import { getAllDictList, registerReCalcStyle, unRegisterReCalcStyle, submitErrorHandler, getSplitOrderTips } from '@/utils/mm'
import {
  createInv, updateInv, getRelatedPO, listCommOptions, getProduct,
  getBatchInventory, autoBatchNo, getInvDetail as getDetail, getKhWarehouse,
  getZkhContactOptions, getCostCenter as getCostCenterApi
} from '@/api/mm'
import { createFields, updateFields, itemListFields, updateItemListFields } from './utils/createEditFields'
import { getColumnSetting } from './utils/tableColumnSetting'

export default {
  name: 'inventoryManagementCreateOrEdit',
  components: {
    BaseInfo, ContactInfo, ItemTable, BatchImport
  },
  data () {
    return {
      pageLoading: false,
      columnSettingTable: [],
      columnSettingBase: [],
      activeName: 'baseInfo',
      createData: {
        createUser: '',
        createTime: '',
        orderType: '',
        orderStatus: '',
        orderNo: '',
        certificateDate: '',
        postDate: '',
        transportMessage: '',
        relateOrderNo: '',
        supplier: '',
        supplierNo: '',
        supplierName: '',
        orderRemark: '',
        transportAddressDetail: '',
        transportAddress: '',
        transportCity: '',
        transportContactName: '',
        transportContactNumber: '',
        transportDirection: '',
        transportPostCode: '',
        transportProvince: '',
        transportRegion: '',
        transportStreet: '',
        zkhAddressDetail: '',
        zkhAddress: '',
        zkhCity: '',
        zkhContactName: '',
        zkhContactNumber: '',
        zkhPostCode: '',
        zkhProvince: '',
        zkhRegion: '',
        zkhStreet: '',
        responsibilityOwner: '',
        itemList: [],
        provinceArray: []
      },
      selections: [],
      transportOptions: {
        addressOptions: [],
        contactOptions: []
      },
      zkhOptions: {
        addressOptions: [],
        contactOptions: []
      },
      batchVisible: false,
      batchLoading: false,
      batchData: {
        inventoryList: []
      },
      tableLoading: false,
      autoBatchLoading: false,
      // 禁用联系人编辑
      disableContact: false,
      // 禁用批次自动分配
      // disableBatchButton: false,
      // 允许手动添加空白行
      userAddLine: false,
      // 禁用关联SKU
      disableRelateSku: false,
      isEdit: false,
      orderNo: '',
      orderType: '',
      // 某个工厂下所有坤和仓list
      khWarehouseList: [],
      needTransOptions: true,
      needZkhOptions: true,
      costCenterList: [],
      submitSortNos: [],
      batchImportVisible: false,
      khWarehouseListCode: '',
      needSplit: false // 是否需要拆单
    }
  },
  async mounted() {
    // 判断编辑还是创建
    this.setPageStatus()
    // 初始化提交按钮样式
    this.initReflow()

    this.pageLoading = true
    // 获取所有字典、枚举
    await getAllDictList(this)
    if (this.isEdit) {
      await this.initEdit()
    } else {
      await this.initCreate()
    }
    await this.initCommon()
    this.pageLoading = false
  },
  beforeDestory () {
    unRegisterReCalcStyle(this.listener)
  },
  computed: {
    ...mapState({
      fields: state => state.inventoryManagement.create,
      warehouseList: state => state.orderPurchase.warehouseList,
      dictList: state => state.orderPurchase.dictList
    }),
    showImportButton () {
      const list = ['23', '18', '13', '27', '32']
      return list.find(item => item === this.orderType)
    },
    finalFields() {
      let ret = []
      if (Array.isArray(this.fields[this.orderType])) {
        ret = this.fields[this.orderType]
      }
      return ret
    },
    rules () {
      const finalFields = this.reactiveContactRules(this.finalFields)
      let rules = {}
      for (let field of finalFields) {
        if (field.required) {
          const trigger = ~['custom', 'select', 'input'].indexOf(field.type) ? ['change', 'blur'] : 'blur'
          rules[field.prop] = [
            { required: true, message: `${field.name}必填`, trigger }
          ]
        }
      }
      return rules
    },
    disabledBySupplier () {
      console.log(this.createData.supplierNo)
      // 必须输入供应商然后才能添加项目行
      const suppliertDisableSkuLsit = [ '30' ]
      if (suppliertDisableSkuLsit.includes(this.orderType) && !this.createData.supplierNo) {
        return true
      }
      return false
    },
    disableBatchButton () {
      let { itemList, orderType } = this.createData
      if (orderType === '21') return true
      itemList = itemList.filter(item => !item.isEmptyLine)
      if (itemList.every(item => !this.canChooseBatch(item))) {
        return true
      }
      if (orderType === '13') {
        if (itemList.every(item => !!this.isKunhe(item.warehouseLocation))) {
          return true
        }
      }
      if (itemList.every(item => !item.warehouseLocation)) {
        return false
      }
      return false
    },
    disableCopyLine () {
      const disableList = ['20', '21', '28']
      const editDisableList = ['18', '13', '29', '31']
      if (this.isEdit && editDisableList.includes(this.orderType)) {
        return true
      }
      if (disableList.includes(this.orderType)) {
        return true
      }
      return false
    }
  },
  methods: {
    showBatchImport () {
      this.batchImportVisible = true
    },
    async handleBatchImport (data) {
      console.log(data, this.costCenterList)
      if (!data.length) return
      this.createData.itemList = this.createData.itemList.filter(item => !item.isEmptyLine)
      data.forEach(item => {
        item.itemNo = this.getItemNo()
        if (this.orderType === '13') {
          item.receiveWarehouseLocationOptions = []
        }
      })
      let initContact = false
      if (!this.createData.itemList.length) {
        initContact = true
      }
      await this.addItemList(data)
      if (!this.costCenterList.length) this.initCostCenter()
      if (initContact) this.initContactOptions()

      this.initKhWarehouseQuery()
      this.addItemEmptyLine()
    },
    async getCostCenter(code, keyword = '') {
      getCostCenterApi({
        companyCode: code,
        costCenter: keyword
      }).then(data => {
        this.costCenterList = data
      })
    },
    updateWarehouseLocation (row) {
      const idx = this.createData.itemList.findIndex(item => item === row)
      // 清空批次信息
      row.batchNo = ''
      this.reactiveUpdateRow(row)
      if (idx === this.getFirstValidLineToQueryContact()) {
        this.initContactOptions(idx)
      }
    },
    updateFactoryCode (row) {
      const idx = this.createData.itemList.findIndex(item => item === row)
      if (idx === this.getFirstValidLineToQueryContact()) {
        this.initKhWarehouseQuery()
        this.initContactOptions(idx)
      }
      if (row.factoryCode) {
        this.getCostCenter(row.factoryCode)
      }
    },
    updateCustomer (value, item, row) {
      this.reactiveUpdateRow(row)
    },
    canChooseBatch (item) {
      // window._console.black(item.warehouseLocation, item.materialGroupNum, this.isKunhe(item.warehouseLocation))
      // 判断项目行能够手动查看批次
      let ret = false
      const { orderType } = this
      if ((orderType !== '21' && orderType !== '32') && !item.warehouseLocation) return false
      if (!item.factoryCode) return false
      switch (orderType) {
        case '20':
          ret = true;
          break
        case '18':
          if (!this.isKunhe(item.warehouseLocation)) {
            ret = true
          }
          break
        case '21':
          ret = true
          break
        case '23':
          ret = true
          break
        case '28':
          if (item.projectCategory === 'S') {
            ret = false
            break;
          }
          ret = true
          break
        case '27':
          ret = true
          break
        case '13':
          // if (!this.isKunhe(item.warehouseLocation)) {
          //   ret = true
          // }
          ret = true
          break;
        case '32':
          if (item.customerCode) {
            ret = true
          }
          break;
        case '35':
          ret = true
          break;
      }
      return ret
    },
    isKunhe (warehouseLocation) {
      const isKunhe = this.khWarehouseList.find(item => item.warehouseLocationCode === warehouseLocation)
      return !!isKunhe
    },
    async initCommon () {
      // 获取创建、编辑字段
      await this.getInvFields()
      // 初始化各种订单类型
      await this.initByOrderType()
    },
    async initCreate () {
      this.orderType = this.$route.params.id
      const orderType = this.$route.params.id
      this.createData.orderType = orderType
      safeRun(() => {
        this.createData.createUser = window.CUR_DATA.user.name
      })
      if (!this.orderType) return this.$message.error('需要指定订单类型！')
      this.initTransportDirection()
    },
    async initEdit () {
      this.orderNo = this.$route.params.id
      const param = { orderNo: this.orderNo }
      let response = await getDetail(param)
      if (!response) return console.error('请求失败！')
      this.formatReponse(response)
      this.initKhWarehouseQuery()
      this.initCostCenter()
    },
    initCostCenter () {
      const item = this.createData.itemList[0]
      if (item && item.factoryCode) {
        this.getCostCenter(item.factoryCode)
      }
    },
    formatReponse (response) {
      this.orderType = response.orderType
      if (Array.isArray(response.itemList)) {
        response.itemList = response.itemList.map(item => {
          let ret = {
            ...item,
            uuid: shortid.generate(),
            // 详情编辑返回项目行
            __itemNo: true
          }
          this.findOptions(ret, 'warehouseLocation', item.warehouseLocation)
          this.findOptions(ret, 'receiveWarehouseLocation', item.receiveWarehouseLocation)
          console.log(ret)
          return ret
        })
      }
      this.populateContact(response)
      this.createData = response
      console.log(response)
      // this.fillValidProp(this.createData, response)
    },
    populateContact (response) {
      response.transportAddress =
      (response.transportProvinceText || '') +
      (response.transportCityText || '') +
      (response.transportRegionText || '') +
      (response.transportStreetText || '') +
      (response.transportAddressDetail || '')
      response.zkhAddress =
      (response.zkhProvinceText || '') +
      (response.zkhCityText || '') +
      (response.zkhRegionText || '') +
      (response.zkhStreetText || '') +
      (response.zkhAddressDetail || '')
    },
    fillValidProp(src, dest) {
      for (let prop in src) {
        if (dest[prop]) {
          src[prop] = dest[prop]
        }
      }
    },
    setPageStatus () {
      if (/edit/.test(location.href)) {
        this.isEdit = true
      }
    },
    async initByOrderType () {
      const orderType = this.isEdit ? 'edit' : 'create'
      this.columnSettingTable = getColumnSetting(this.orderType, orderType, 'table')
      this.columnSettingBase = getColumnSetting(this.orderType, 'create', 'baseInfo')

      this.initTitleStatus()
      this.initUserAddLine()
      // await this.initContactOptions()
      this.clearValidate()
    },
    initTransportDirection () {
      // 初始化运输方向默认值
      // transportDirection 0: 无运输, 1: 震坤行 -> 运输地址, 2: 运输地址 -> 震坤行
      const list0 = ['30', '31', '13', '29', '27', '23', '32', '35']
      const list1 = ['20', '18', '28']
      const list2 = ['21', '19']
      if (list0.includes(this.orderType)) {
        this.createData.transportDirection = 0
      }
      if (list1.includes(this.orderType)) {
        this.createData.transportDirection = 1
      }
      if (list2.includes(this.orderType)) {
        this.createData.transportDirection = 2
      }
    },
    initTitleStatus () {
      const contactDisable = [
        '27', '23', '29', '13', '31', '30', '32'
      ]
      if (contactDisable.includes(this.orderType)) {
        this.disableContact = true
      }
      // const batchButtonDisable = [
      //   '19', '21', '30', '31', '22', '29'
      // ]
      // if (batchButtonDisable.includes(this.orderType)) {
      //   this.disableBatchButton = true
      // }
      const relateSkuDisable = [
        '18', '19', '30', '31', '13', '22', '29', '27', '23', '32'
      ]
      if (relateSkuDisable.includes(this.orderType)) {
        this.disableRelateSku = true
      }
    },
    initUserAddLine () {
      const allowTypes = [
        '18', '19', '23', '27', '13', '30', '31', '22', '29', '32', '35'
      ]
      if (allowTypes.includes(this.orderType)) {
        this.userAddLine = true
      }
      if (this.userAddLine) {
        this.addItemEmptyLine()
      }
    },
    async getInvFields () {
      if (!this.fields[this.orderType] || !this.fields[this.orderType].length) {
        await this.$store.dispatch('inventoryManagement/getInvFields', {
          orderType: this.orderType,
          routerType: 'create'
        })
      }
    },
    clearValidate () {
      safeRun(this.$refs.createForm.clearValidate)
    },
    clearSelections () {
      // 清除复选框以及records
      safeRun(() => {
        this.selections = []
        this.$refs.itemTable.$refs.detailTable.clearCheckboxRow()
      })
    },
    setRequired (finalFields, key, value) {
      finalFields.forEach(field => {
        if (~field.prop.indexOf(key)) {
          field.required = value
        }
      })
    },
    reactiveContactRules (finalFields) {
      finalFields = deepClone(finalFields)
      finalFields = finalFields.filter(field => /(zkh|transport)(Address|ContactName|ContactNumber)/gi.test(field.prop) || field.prop === 'responsibilityOwner')
      const { transportDirection } = this.createData
      switch (transportDirection) {
        case 1:
          this.setRequired(finalFields, 'transport', 1)
          this.setRequired(finalFields, 'zkh', 1); break;
        case 2:
          this.setRequired(finalFields, 'transport', 1)
          this.setRequired(finalFields, 'zkh', 1); break;
        case 0:
          this.setRequired(finalFields, 'transport', 0)
          this.setRequired(finalFields, 'zkh', 0); break;
      }
      this.setRequired(finalFields, 'responsibilityOwner', 1)
      console.log(finalFields)
      return finalFields
    },
    async getTransOptions (transParams) {
      const transRes = await listCommOptions(transParams)
      if (transRes && transRes.supplier) {
        this.transportOptions = transRes.supplier
        if (!transRes.supplier.contactOptions) this.transportOptions.contactOptions = []
        if (!transRes.supplier.addressOptions) this.transportOptions.addressOptions = []
      }
    },
    async getZkhOptions (zkhParams) {
      let zkhRes = await getZkhContactOptions(zkhParams)
      if (zkhRes) {
        zkhRes = zkhRes.map(item => ({
          name: item.receiveContactName,
          phone: item.receiveContactPhone,
          province: item.receiveProvince,
          provinceText: item.receiveProvinceText,
          city: item.receiveCity,
          cityText: item.receiveCityText,
          region: item.receiveRegion,
          regionText: item.receiveRegionText,
          street: item.receiveStreet,
          streetText: item.receiveStreetText,
          detail: item.receiveAddressDetail,
          addressDetail: item.receiveAddressDetail
        }))
        const zkhResContact = zkhRes.map(item => ({
          name: item.name,
          phone: item.phone
        }))
        this.zkhOptions = {
          contactOptions: zkhResContact,
          addressOptions: zkhRes
        }
      }
    },
    async initContactOptions(idx = 0) {
      if (this.disableContact) return
      if (!(this.createData.itemList && this.createData.itemList.length)) return
      if (!this.createData.itemList[idx].factoryCode) return this.$message.error('查询通讯信息请选择工厂！')

      // 初始化联系人逻辑
      if (this.needTransOptions && this.createData.supplierNo) {
        // 运输地址联系人
        const transParams = {
          supplierNo: this.createData.supplierNo,
          factoryCode: this.createData.itemList[idx].factoryCode
        }
        safeRun(() => window._console.red('传输信息下拉获取'))
        await this.getTransOptions(transParams)
      }
      if (this.needZkhOptions) {
        // 震坤行联系人
        if (!this.createData.itemList[idx].warehouseLocation) return
        const zkhParams = {
          addressCode: this.createData.itemList[idx].warehouseLocation.slice(0, 2) + '01',
          factoryCode: this.createData.itemList[idx].factoryCode,
          type: '01'
        }
        safeRun(() => window._console.red('震坤行信息下拉获取'))
        await this.getZkhOptions(zkhParams)
      }
    },
    toggleLine () {
      if (!this.selections.length) return
      const { orderType } = this.createData
      const editDisableList = ['18', '13', '29', '31']
      console.log(this.selections)
      if (this.isEdit && editDisableList.includes(orderType) && this.selections.some(sel => sel.isDeleted)) {
        return this.$message.error('不能恢复已删除的行！')
      }
      const notLocal = this.selections.filter(sel => !sel.userAdded && sel.__itemNo)
      if (notLocal.some(item => item.isDeleted) && notLocal.some(item => !item.isDeleted)) {
        return this.$message.error('不能同时勾选已删除和未删除的商品行进行操作！')
      }
      const statusType = notLocal[0] && notLocal[0].isDeleted === 0 ? '删除' : '恢复'
      let content = `此操作将删除手动添加行，${statusType}其余商品行, 是否继续?`
      if (this.selections.every(item => item.userAdded || !item.__itemNo)) {
        content = '此操作将删除所选商品行, 是否继续?'
      }
      if (this.selections.every(item => !item.userAdded && item.__itemNo)) {
        content = `此操作将${statusType}所选商品行, 是否继续?`
      }

      this.$confirm(content, '操作提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const prevItem = this.getItemSnapshot()
        if (this.selections.length === this.createData.itemList.length) {
          this.createData.supplierNo = ''
          this.createData.supplierName = ''
        }
        // 删除手动添加行
        const handList = this.selections.filter(sel => sel.userAdded || !sel.__itemNo)
        const itemList = this.createData.itemList.filter(item => !handList.includes(item))

        // 根据状态删除/恢复其余商品行
        itemList.forEach((item, index) => {
          if (this.selections.some(sel => sel === item)) {
            item.isDeleted = statusType === '删除' ? 1 : 0
          }
        })
        this.$set(this.createData, 'itemList', itemList)
        this.clearSelections()
        if (!this.createData.itemList.length) {
          this.resetContact()
        }
        this.compareWithPrevItem(prevItem, 'ref')
      })
    },
    getFirstValidLineToQueryContact () {
      let ret = null
      const { itemList } = this.createData
      for (let index = 0; index < itemList.length; index++) {
        if (itemList[index] && itemList[index].isDeleted !== 1 && !itemList[index].isEmptyLine) {
          ret = index
          break
        }
      }
      return ret
    },
    getItemSnapshot () {
      const idx = this.getFirstValidLineToQueryContact()
      let item = null
      if (idx !== null) item = this.createData.itemList[idx]
      return item
    },
    compareWithPrevItem (prevItem, ref) {
      // 判断第一个有效商品行，并查询默认信息
      const index = this.getFirstValidLineToQueryContact()
      if (index !== null) {
        const thisItem = this.getItemSnapshot()
        if (ref && prevItem !== thisItem) {
          this.initContactOptions(index)
        } else if (!(prevItem && thisItem && (prevItem.referNo + prevItem.referItemNo) === (thisItem.referNo + thisItem.referItemNo))) {
          this.initContactOptions(index)
        }
      }
      return index === null
    },
    resetContact () {
      this.createData.transportAddress = ''
      this.createData.transportAddressDetail = ''
      this.createData.transportCity = ''
      this.createData.transportCityText = ''
      this.createData.transportContactName = ''
      this.createData.transportContactNumber = ''
      this.createData.transportPostCode = ''
      this.createData.transportProvince = ''
      this.createData.transportProvinceText = ''
      this.createData.transportRegion = ''
      this.createData.transportRegionText = ''
      this.createData.transportStreet = ''
      this.createData.transportStreetText = ''
      this.createData.zkhAddressDetail = ''
      this.createData.zkhAddress = ''
      this.createData.zkhCity = ''
      this.createData.zkhCityText = ''
      this.createData.zkhContactName = ''
      this.createData.zkhContactNumber = ''
      this.createData.zkhPostCode = ''
      this.createData.zkhProvince = ''
      this.createData.zkhProvinceText = ''
      this.createData.zkhRegion = ''
      this.createData.zkhRegionText = ''
      this.createData.zkhStreet = ''
      this.createData.zkhStreetText = ''
      this.transportOptions = {
        addressOptions: [],
        contactOptions: []
      }
      this.zkhOptions = {
        addressOptions: [],
        contactOptions: []
      }
      safeRun(() => {
        this.$refs.contactInfo.delAddress()
        setTimeout(this.clearValidate)
      })
    },
    filterInvalidItem (itemList) {
      let ret = []
      ret = itemList.filter(item => !item.isEmptyLine).filter(item => {
        return item.skuNo && item.factoryCode && item.warehouseLocation && item.quantity
      })
      return ret
    },
    validateAutoBatch (itemList) {
      let ret = true
      let retMsg = ''

      if (itemList.filter(item => !item.isEmptyLine).some((item, index) => {
        if (!item.skuNo) {
          retMsg += `请输入第${index + 1}行SKU编码！ <br />`
        }
        if (!item.factoryCode) {
          retMsg += `请输入第${index + 1}行工厂！ <br />`
        }
        if (!item.warehouseLocation && this.orderType !== '32') {
          retMsg += `请输入第${index + 1}行仓库地点！ <br />`
        }
        if (!item.customerCode && this.orderType === '32') {
          retMsg += `请输入第${index + 1}行寄售客户！ <br />`
        }
        if (!item.quantity) {
          retMsg += `请输入第${index + 1}行交货数量！ <br />`
        }
        return !item.skuNo ||
          !item.factoryCode ||
          (!item.warehouseLocation && this.orderType !== '32') ||
          (!item.customerCode && this.orderType === '32') ||
          !item.quantity
      })) {
        ret = false
        this.$alert(retMsg, '校验失败', {
          type: 'error',
          customClass: 'uploadMsg',
          dangerouslyUseHTMLString: true
        })
      }
      return ret
    },
    fillItemList (data, tmpItemList) {
      if (Array.isArray(data)) {
        const tmpMap = {}
        data.forEach(item => {
          tmpMap[item.sortNo] ? tmpMap[item.sortNo].push(item) : tmpMap[item.sortNo] = [item]
        })
        const lastItem = tmpItemList[tmpItemList.length - 1] || {}
        let lastItemNo = lastItem.itemNo
        const allExtraList = []
        /* {"AA0001": [{ batchNo: 'xxx', xxx: 'xxx' },{ batchNo: 'xxx', xxx: 'xxx' }] } */
        for (let sort in tmpMap) {
          /* sort: "AA0001", tmpMap[sort]: [{ batchNo: 'xxx', xxx: 'xxx' },{ batchNo: 'xxx', xxx: 'xxx' }] */
          const item = tmpItemList.find(item => item.itemNo === sort)
          if (item && tmpMap[sort][0]) {
            item.batchNo = tmpMap[sort][0].batchNo
            item.quantity = tmpMap[sort][0].quantity
            item.batchQuantity = tmpMap[sort][0].batchQuantity
          }
          let extraList = tmpMap[sort].slice(1)
          if (extraList.length) {
            extraList = extraList.map((extra, index) => {
              const tmp = { ...item }
              if (lastItemNo) {
                const newItemNo = Number(lastItemNo) + 1
                const newNo = padStart(newItemNo, 4, '0')
                tmp.itemNo = newNo
                lastItemNo = newNo
                delete tmp.__itemNo
                window._console.red('lastItemNo: ', lastItemNo)
              }
              delete tmp._XID
              return { ...tmp, ...extra, uuid: shortid.generate() }
            })
            allExtraList.push(...extraList)
          }
        }
        if (allExtraList.length) {
          const emptyLine = tmpItemList.find(item => item.isEmptyLine)
          tmpItemList = tmpItemList.filter(item => !item.isEmptyLine).filter(item => {
            if (this.submitSortNos.some(submitNo => item.itemNo === submitNo)) {
              return data.some(da => da.sortNo === item.itemNo)
            }
            return item
          })
          tmpItemList.push(...allExtraList)
          if (emptyLine) {
            tmpItemList.push(emptyLine)
          }
        }
        // this.createData.itemList = tmpItemList
        this.$set(this.createData, 'itemList', tmpItemList)
      }
    },
    autoBatch () {
      console.log('批次自动分配')
      const { itemList } = this.createData
      if (!itemList.length) return
      // const fList = this.filterInvalidItem(itemList)
      if (!this.validateAutoBatch(itemList)) return
      let tmpItemList = deepClone(itemList)

      let data = tmpItemList
        .filter(item => !item.isEmptyLine)
        .filter(this.canChooseBatch)
      if (!data.length) return

      data = data.map(row => ({
        batchNo: row.batchNo,
        factory: row.factoryCode,
        quantity: row.quantity,
        skuNo: row.skuNo,
        sortNo: row.itemNo,
        warehouseLocation: row.warehouseLocation,
        customerCode: row.customerCode,
        orderType: this.orderType
      }))
      console.log(data)
      this.submitSortNos = data.map(item => item.sortNo)
      this.autoBatchLoading = true
      autoBatchNo(data)
        .then(res => {
          const { code, data, msg } = res
          if (code === 0) {
            this.fillItemList(data, tmpItemList)
          } else if (code === -1 && Array.isArray(data) && data.length) {
            this.$confirm(`${msg} 是否仍要进行批次自动分配？（分配后，超出部分的数量将直接删除）`, '操作提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.fillItemList(data, tmpItemList)
            })
          } else {
            let errMsg = ''
            submitErrorHandler(errMsg, data, msg)
          }
        })
        .finally(() => {
          this.autoBatchLoading = false
        })
    },
    copyLine () {
      if (!this.selections.length) return
      let { itemList } = this.createData
      itemList = deepClone(itemList)
      this.$confirm('请确认是复制选中项目行？', '操作提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let list = deepClone(this.selections)
        itemList = itemList.filter(item => !item.isEmptyLine)
        const lastItem = itemList[itemList.length - 1] || {}
        const lastItemNo = lastItem.itemNo
        list = list.map((item, index) => {
          const retItem = {
            ...item,
            uuid: shortid.generate(),
            isCopyLine: true,
            batchNo: '',
            batchQuantity: ''
          }
          if (lastItemNo) {
            const newItemNo = Number(lastItemNo) + index + 1
            const newNo = padStart(newItemNo, 4, '0')
            retItem.itemNo = newNo
          }
          delete retItem._XID
          return retItem
        })
        if (this.isEdit) {
          // todo 区分创建和编辑的复制行
          itemList.push(...list)
        } else {
          itemList.splice(itemList.length, 0, ...list)
        }
        this.createData.itemList = itemList.filter(item => !item.isEmptyLine)
        this.clearSelections()
        if (this.userAddLine) {
          this.addItemEmptyLine()
        }
      })
    },
    reactiveUpdateRow (row, itemNo) {
      let index = this.createData.itemList.findIndex(item => item === row)
      if (itemNo) {
        index = this.createData.itemList.findIndex(item => item.itemNo === itemNo)
      }
      if (index > -1) {
        this.$set(this.createData.itemList, index, row)
      }
    },
    filterByProp(prop) {
      const map = {}
      return (item) => {
        if (!map[item[prop]]) {
          map[item[prop]] = true
          return true
        }
      }
    },
    findOptions(row, prop, value) {
      const options = this.warehouseList.filter(warehouse => {
        const { warehouseLocationCode, warehouseLocationName } = warehouse
        // eslint-disable-next-line eqeqeq
        return (warehouseLocationCode && warehouseLocationCode.indexOf(value) != -1) || (warehouseLocationName && warehouseLocationName.indexOf(value) != -1)
      }).map(item => ({
        name: item.warehouseLocationName,
        value: item.warehouseLocationCode
      })).filter(this.filterByProp('value')).slice(0, 20)

      row[`${prop}Options`] = options
    },
    async addItem (val, row, userValue = null) {
      const { itemList } = this.createData || []
      this.pageLoading = true

      let productInfo = await getProduct({
        factoryCode: row.factoryCode,
        skuNos: [val]
      })
      productInfo = { ...productInfo }
      if (userValue) productInfo = { ...productInfo, ...userValue }
      if (productInfo && productInfo[0]) {
        row.materialDescription = productInfo[0].materialDescription
        row.inventoryUnit = productInfo[0].unitName
        row.warehouseLocation = productInfo[0].warehouseLocationCode
        this.findOptions(row, 'warehouseLocation', row.warehouseLocation)
        if (row.receiveWarehouseLocation) {
          this.findOptions(row, 'receiveWarehouseLocation', row.receiveWarehouseLocation)
        }
      }
      row.skuNo = val
      if (row.isEmptyLine) {
        delete row.isEmptyLine
      }
      if (!row.projectText) {
        row.projectText = ''
      }
      if (itemList[0] === row) {
        this.initContactOptions()
      }
      this.reactiveUpdateRow(row)
      this.initKhWarehouseQuery()
      if (userValue) {
        itemList.push(row)
      } else if (this.userAddLine && itemList[itemList.length - 1] === row) {
        this.addItemEmptyLine()
      }
      this.pageLoading = false
    },
    async addItemList (items) {
      const { itemList } = this.createData || []
      this.pageLoading = true

      let productInfoList = await getProduct({
        factoryCode: items[0].factoryCode,
        skuNos: items.map(item => item.skuNo)
      })
      if (!productInfoList) {
        this.pageLoading = false
        return
      }
      items.forEach(item => {
        let newItem = { ...item }
        let productInfo = productInfoList.find(info => info.skuNo === item.skuNo)
        if (productInfo) {
          newItem.materialDescription = productInfo.materialDescription
          newItem.inventoryUnit = productInfo.unitName
          if (!newItem.warehouseLocation) {
            newItem.warehouseLocation = productInfo.warehouseLocationCode
          }
          this.findOptions(newItem, 'warehouseLocation', newItem.warehouseLocation)
          if (newItem.receiveWarehouseLocation) {
            this.findOptions(newItem, 'receiveWarehouseLocation', newItem.receiveWarehouseLocation)
          }
        }
        if (newItem.isEmptyLine) {
          delete newItem.isEmptyLine
        }
        if (!newItem.projectText) {
          newItem.projectText = ''
        }
        if (itemList[0] === newItem) {
          this.initContactOptions()
        }
        this.reactiveUpdateRow(newItem)
        itemList.push(newItem)
      })
      this.pageLoading = false
    },
    selectionChange (rows) {
      this.selections = rows
    },
    assignWithCondition (source, dest, fn) {
      for (let prop in dest) {
        fn(source, dest, prop)
      }
    },
    validateRelateOrder (res) {
      let valid = true
      if (!res) return false
      if (this.createData.supplierNo) {
        if (res.supplierNo !== this.createData.supplierNo || res.supplierName !== this.createData.supplierName) {
          this.$message.error('申请单的供应商应唯一，请拆分处理！')
          valid = false
        }
      }
      // if (this.createData.itemList.some(item => item.referNo === this.createData.relateOrderNo)) {
      //   this.$message.warning('已经添加该关联单据商品行！')
      //   valid = false
      // }
      if (this.createData.itemList.length + res.itemList.length > 500) {
        this.$message.error('申请单的项目行数不能超过500行，请拆分处理！')
        valid = false
      }
      return valid
    },
    deleteOldRelateNo (itemList) {
      return itemList.filter(item => item.referNo !== this.createData.relateOrderNo)
    },
    async getRelateOrder () {
      if (!this.createData.relateOrderNo) return
      this.tableLoading = true
      const params = {
        orderType: this.orderType,
        referNo: this.createData.relateOrderNo
      }
      let rawItemList = this.createData.itemList.filter(item => !item.isEmptyLine)
      let _tmpLength
      const relateRes = await getRelatedPO(params)
      if (!this.validateRelateOrder(relateRes)) {
        this.tableLoading = false
        return
      }
      if (relateRes && relateRes.itemList) {
        relateRes.itemList.forEach(item => {
          item.supplierNo = relateRes.supplierNo
          item.supplierName = relateRes.supplierName
        })
        relateRes.itemList.forEach(this.addItemNoList)
        const itemList = this.deleteOldRelateNo([...rawItemList])
        _tmpLength = itemList.length
        itemList.push(...relateRes.itemList)
        itemList.forEach(item => {
          this.findOptions(item, 'warehouseLocation', item.warehouseLocation)
          this.findOptions(item, 'receiveWarehouseLocation', item.receiveWarehouseLocation)
        })
        this.$set(this.createData, 'itemList', itemList)
        if (this.userAddLine) {
          this.addItemEmptyLine()
        }
      }
      this.assignWithCondition(this.createData, relateRes, (source, dest, prop) => {
        if (prop === 'itemList') return
        if (dest[prop]) source[prop] = dest[prop]
      })
      console.log(deepClone(rawItemList))
      if (_tmpLength === 0 && relateRes) {
        this.populateContact(this.createData)
        Promise.all([
          this.initKhWarehouseQuery(),
          this.initContactOptions()
        ])
      }
      this.tableLoading = false
    },
    async initKhWarehouseQuery () {
      let { itemList } = this.createData
      if (!itemList || !itemList[0] || !itemList[0].factoryCode || (itemList[0].factoryCode === this.khWarehouseListCode && this.khWarehouseList.length)) return
      const data = {
        factoryCode: itemList[0].factoryCode
      }
      const khWarehouseList = await getKhWarehouse(data)
      this.khWarehouseList = khWarehouseList
      this.khWarehouseListCode = itemList[0].factoryCode
    },
    sleep(time = 200) {
      return new Promise((resolve) => setTimeout(resolve, time))
    },
    updateBatchNo (row, itemNo) {
      this.reactiveUpdateRow(row, itemNo)
    },
    async updateBatchVisible (visible, batchData) {
      if (!batchData) {
        this.batchVisible = visible
        return
      }
      const { orderType } = this
      let { supplierNo } = this.createData

      const data = {
        factoryCode: batchData.factoryCode,
        skuNo: batchData.skuNo,
        warehouseLocation: batchData.warehouseLocation
      }
      if (orderType === '21') {
        data.referNo = batchData.referNo
        data.orderType = orderType
        data.supplierNo = supplierNo
        delete data.warehouseLocation
      }
      if (orderType === '28') {
        data.orderType = orderType
        data.referNo = batchData.referNo
      }
      if (orderType === '32') {
        data.orderType = orderType
        data.customerCode = batchData.customerCode
        delete data.warehouseLocation
      }
      await this.sleep(200)
      this.pageLoading = true
      this.batchLoading = true
      this.batchData.inventoryList = []
      getBatchInventory(data)
      .then(res => {
          if (res) {
            this.batchVisible = visible
            this.batchData = {
              ...batchData,
              inventoryList: res
            }
          }
        })
        .finally(() => {
          this.pageLoading = false
          this.batchLoading = false
        })
    },
    addItemEmptyLine () {
      const { orderType } = this.createData
      const editDisableList = ['18', '13', '29', '31']
      if (this.isEdit && editDisableList.includes(orderType)) {
        return
      }
      const item = {
        uuid: shortid.generate(),
        isEmptyLine: true,
        // userAdded 物理删除
        userAdded: true,
        warehouseLocationOptions: [],
        receiveWarehouseLocationOptions: [],
        projectText: '',
        itemNo: this.getItemNo()
      }
      // 编辑状态需要增加isDeleted属性
      if (this.isEdit) item.isDeleted = 0
      this.createData.itemList.push(item)
    },
    addItemNoList (item, index) {
      const itemList = this.createData.itemList.filter(item => !item.isEmptyLine)
      const len = itemList.length
      let _itemNo = '0000'
      if (len > 0) {
        let { itemNo } = itemList[len - 1]
        if (!itemNo) itemNo = '0000'
        _itemNo = itemNo
      }
      const numItemNo = Number(_itemNo) + 1 + index
      _itemNo = padStart(numItemNo, 4, '0')
      item.uuid = shortid.generate()
      item.itemNo = _itemNo
      console.log(_itemNo, index)
    },
    getItemNo () {
      const { itemList } = this.createData
      const len = itemList.length
      if (len > 0) {
        const { itemNo } = itemList[len - 1]
        if (itemNo) {
          const numItemNo = Number(itemNo) + 1
          return padStart(numItemNo, 4, '0')
        }
      }
      return '0001'
    },
    formatItemNo (data) {
      if (data && data.itemList) {
        data.itemList.forEach(item => {
          if (!item.__itemNo) {
            delete item.itemNo
          }
          if (item.batchNo === '') {
            item.batchNo = null
          }
        })
      }
    },
    formatSubmit (data) {
      let tmp = deepClone(data)
      this.formatItemNo(tmp)
      let ret = {}
      const fields = this.isEdit ? updateFields : createFields
      for (let field of fields) ret[field] = tmp[field]

      const itemFields = this.isEdit ? updateItemListFields : itemListFields
      ret.itemList = []
      tmp && tmp.itemList && tmp.itemList.filter(row => !row.isEmptyLine).filter(row => {
        if (row.isCopyLine) row.itemNo = null
        return row
      }).forEach(item => {
        let tmp = {}
        for (let field of itemFields) tmp[field] = item[field]
        if (Object.keys(tmp).length) ret.itemList.push(tmp)
      })
      return ret
    },
    validProps (fields) {
      let ret = true
      for (let prop in fields) {
        if (/zkh|transport/.test(prop)) {
          ret = false
        }
      }
      return ret
    },
    async validateForm () {
      return new Promise((resolve, reject) => {
        this.$refs.createForm.validate((valid, fields) => {
          if (!this.validProps(fields)) {
            this.activeName = 'contact'
            return reject(valid)
          }
          if (!valid) return reject(valid)
          resolve(valid)
        })
      })
    },
    formatUpdate (data) {
      safeRun(() => {
        data.updateUser = window.CUR_DATA.user.name
      })
    },
    cancelSubmit () {
      const { orderNo } = this.createData
      this.$closeTag(this.$route.path)
      setTimeout(() => {
        this.$router.push({
          path: `/inventoryManagement/detail/${orderNo}`,
          query: { tagName: `${orderNo}详情` }
        })
      })
    },
    validateAddress () {
      const {
        transportDirection,
        transportProvince,
        zkhProvince,
        transportCity,
        zkhCity,
        transportRegion,
        zkhRegion,
        orderType
      } = this.createData
      const directionList = [0, 1, 2]
      let ret = true
      // eslint-disable-next-line eqeqeq
      if (transportDirection == 1) {
        if (!transportProvince || !transportCity || !transportRegion) {
          ret = false
          this.$message.error('运输方向为震坤行->运输地址时，运输地址省市区地址必填，请重新选择运输地址或新增输入！')
        }
      }
      // eslint-disable-next-line eqeqeq
      if (transportDirection == 2) {
        if (!zkhProvince || !zkhCity || !zkhRegion) {
          ret = false
          this.$message.error('运输方向为运输地址->震坤行时，震坤行地址省市区地址必填')
        }
      }
      if (orderType === '28' && !directionList.includes(transportDirection)) {
        ret = false
        this.$message.error('采购退货单运输方向必填!')
      }
      return ret
    },
    validateQuantity () {
      const { itemList } = this.createData
      let ret = ''
      itemList.filter(item => !item.isEmptyLine).forEach((item, index) => {
        if (!item.quantity || item.quantity <= 0) {
          ret += `第${index + 1}行交货数量必须大于0！<br />`
        }
      })
      if (ret) {
        this.$alert(ret, '校验失败', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      } else {
        return true
      }
    },
    async handleSubmit (form) {
      if (!await this.validateForm()) return
      if (!this.validateQuantity()) return
      if (!this.validateAddress()) return
      const data = this.formatSubmit(this.createData)
      console.log(data)
      const splitOrderData = this.createData.itemList.filter(item => !item.isEmptyLine).map(item => {
        return {
          factoryCode: item.factoryCode,
          warehouseLocationCode: item.warehouseLocation,
          skuNo: item.skuNo
        }
      })
      this.needSplit = await getSplitOrderTips(this, splitOrderData);
      this.pageLoading = true
      let submitApi = createInv
      let operationType = '创建'
      if (this.isEdit) {
        operationType = '编辑'
        this.formatUpdate(data)
        submitApi = updateInv
      }
      submitApi(data)
        .then(res => {
          if (res) {
            const { code, data, msg } = res
            if (code === 0 && data) {
              const { orderNo } = data
              if (orderNo) {
                this.$alert(`库存申请单${orderNo}提交成功！`, `${operationType}成功`, {
                  confirmButtonText: '确定',
                  type: 'success',
                  callback: _action => {
                    if (this.needSplit) {
                      this.$closeTag('/inventoryManagement/list')
                      setTimeout(() => {
                        this.$router.push({
                          path: '/inventoryManagement/list',
                          query: { orderNo }
                        })
                      }, 300)
                    } else {
                      this.$closeTag(this.$route.path)
                      setTimeout(() => { this.$router.replace({ path: `/inventoryManagement/detail/${orderNo}` }) }, 100)
                    }
                  }
                })
              }
            } else if (code !== 0) {
              let errMsg = ''
              submitErrorHandler(errMsg, data, msg)
            }
          }
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    initReflow () {
      this.reflow()
      this.listener = () => setTimeout(this.reflow, 200)
      registerReCalcStyle(this.listener)
    },
    reflow () {
      safeRun(() => {
        this.$refs.fixedCreate.style.width = `calc(100% - ${parseInt(document.querySelector('.main-side').clientWidth) + 10}px)`
      })
    },
    handleClick (tab) {}
  }
}
</script>
<style lang="scss" scoped>
.inventory-create{
  @import './styles/card-layout.scss';
  .button-group{
    display: flex;
    justify-content:flex-end;
    margin: 10px 0px;
  }
  .fixed-create{
    margin-top: 10px;
    position: fixed;
    bottom: 0px;
    z-index: 1000;
    padding: 10px;
    width: 100%;
    border-top: solid 2px #bfb2b23d;
    background-color: white;
    .btn-group{
      display: flex;
      margin-right: 30px;
      justify-content: flex-end;
    }
  }
}
</style>
