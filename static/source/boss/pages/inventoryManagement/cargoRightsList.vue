<template>
  <div class="app-container order-list-components">
    <div class="filter-container">
      <!-- 工厂是下拉框 供应商和仓库地点是前端模糊查询(下拉框)  批次和SKU是100项做成分割（textarea）-->
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="工厂：" prop="factory">
              <el-select
                v-model="searchForm.factory"
                filterable
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in companyFactoryList"
                  :key="item.companyCode"
                  :label="item.companyCode + ' ' + item.companyName"
                  :value="item.companyCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商：" prop="supplier">
              <RemoteSupplier
                :data.sync="searchForm.supplier"
                @change="(val) => (searchForm.supplier = val)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SKU编码" prop="skuList">
              <el-input
                autosize
                type="textarea"
                v-model="searchForm.skuList"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="仓库地点：" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                clearable
                style="width:100%"
              >
                <el-option
                  v-for="item in compWarehouseLocation"
                  :key="item.factoryCode + item.warehouseLocationCode"
                  :label="
                    item.warehouseLocationCode +
                      ' ' +
                      item.warehouseLocationName
                  "
                  :value="item.warehouseLocationCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="批次" prop="firstBatchList">
              <el-input
                type="textarea"
                autosize
                v-model="searchForm.firstBatchList"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商批次" prop="supplierBatch">
              <el-input
                v-model.trim="searchForm.supplierBatch"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <div class="btn-container">
          <div class="mini-container">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="primary" @click="reset">重置</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="btn-container">
      <div class="flex-box">
        <div>说明：</div>
        <div class="notify">
          <span
            >1）查询时，SKU编码和批次至少填写一项。2）仅可对单页的数据进行批量货权转移，可修改单页显示条数便于批量操作。</span
          >
          <br />
          <span class="red">
            3）坤合货权转移存在一定的滞后性,请勿重复发起请求</span
          >
        </div>
      </div>

      <div>
        <el-button
          v-if="getButtonAuth('寄售货权转移', '批量货权转移')"
          type="primary"
          @click="batchCargoRigthsRemove"
          :disabled="tableSelection.length <= 0"
          >批量货权转移</el-button
        >
        <el-button
          v-if="getButtonAuth('寄售货权转移', '批量重试')"
          type="primary"
          @click="batchCargoRightsRemoveRetry()"
          :disabled="tableSelection.length <= 0"
          >批量重试</el-button
        >
      </div>
    </div>
    <el-table
      :data="cargoRightsList"
      border
      v-loading="loading"
      @selection-change="handleSelectionChange"
      :cell-style="headStyle"
      :header-row-style="headStyle"
      :header-cell-style="headStyle"
    >
      <el-table-column type="selection" width="55" :selectable="checkboxSelect">
      </el-table-column>
      <el-table-column prop="status " label="状态" width="80">
        <!-- （0：未处理；1：处理成功(delete)；-1：处理失败，-2：处理异常） -->
        <template slot-scope="scope">
          <span v-if="scope.row.status === 0"></span>
          <span v-if="scope.row.status === 1">转移成功</span>
          <span
            v-if="scope.row.status === -2"
            :class="scope.row.status === -2 ? 'red' : ''"
            >转移失败</span
          >
          <span
            v-if="scope.row.status === -1"
            :class="scope.row.status === -1 ? 'red' : ''"
            >系统异常!!</span
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="msg"
        label="消息"
        width="250"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span v-html="scope.row.msg"></span>
        </template>
      </el-table-column>
      <el-table-column prop="sku" label="SKU编码" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.sku }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="materialDescription"
        label="物料描述"
        width="120"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.materialDescription }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="firstBatch" label="批次" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.firstBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="supplierBatch" label="供应商批次" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.supplierBatch }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="supplierName"
        label="供应商"
        width="120"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.supplierName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="position"
        label="仓库地点"
        width="120"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.position }}{{ scope.row.warehouseName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="supplierConsignmentQty"
        label="寄售在库数量"
        width="160"
      >
        <template slot-scope="scope">
          <el-input-number
            v-model="scope.row.supplierConsignmentQty"
            :min="0"
            :max="scope.row.supplierConsignmentQty"
            label="描述文字"
            :disabled="scope.row.status === 1"
          ></el-input-number>
        </template>
      </el-table-column>
      <el-table-column prop="unitName" label="单位" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.unitName }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="factoryName "
        label="工厂"
        width="120"
        :show-overflow-tooltip="true"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.factory }}{{ scope.row.factoryName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="produceTime" label="生产日期" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.produceTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="zip" label="失效日期" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.invalidTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="warehousingTime" label="入库日期" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.warehousingTime }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="120" align="center">
        <template slot-scope="scope">
          <el-button
            @click.native.prevent="batchCargoRigthsSingelRemove(scope.row)"
            v-if="scope.row.status == 0 && getButtonAuth('寄售货权转移', '展示操作列按钮')"
            type="text"
            size="small"
          >
            一键转移
          </el-button>
          <el-button
            v-if="(scope.row.status === -1 || scope.row.status === -2) && getButtonAuth('寄售货权转移', '展示操作列按钮')"
            @click.native.prevent="batchCargoRightSigleRemoveRetry(scope.row)"
            type="text"
            size="small"
            >重试</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total >= 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.size"
      layout="total, sizes,prev, pager, next, jumper"
      @pagination="getPaginationCargoRightsList"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { RemoteSupplier } from '@boss-ui/ui'
import {
  getCargoRightsList,
  batchCargoRightsRemove,
  batchCargoRightsRemoveRetry
} from '@/api/mm'
import Pagination from '@/components/Pagination'
import { getButtonAuth } from '@/utils/auth'
import { getUserCompany } from '@/utils/mm'
//  （0：未处理；1：处理成功；-1：处理失败，-2：处理异常） ,
//   按钮/勾选===》 转移失败/空（optional） 可选
// 变更！！   失败，异常=》 重试
export default {
  props: {},
  data() {
    return {
      searchForm: {
        factory: '',
        // string
        supplier: '',
        // Array
        skuList: '',
        // string
        position: '',
        // Array
        firstBatchList: '',
        // string
        supplierBatch: ''
      },
      rules: {
        factory: [
          { required: true, message: '请输入工厂名称', trigger: ['change', 'blur'] }
        ]
      },
      listQueryInfo: {
        current: 1,
        size: 50
      },
      total: 0,
      // 货权转移列表
      cargoRightsList: [],
      // 表格选中行
      tableSelection: [],
      // 表格数据加载
      loading: false,
      // 重试接口参数
      retryArr: [],
      // 一键转移接口参数
      removeArr: []
    }
  },
  components: {
    Pagination,
    RemoteSupplier
  },
  created() {
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    this.getUserCompany()
  },
  mounted() {},
  computed: {
    compWarehouseLocation () {
      const { factory } = this.searchForm
      const { warehouseList } = this
      const list = warehouseList.filter(item => item.factoryCode === factory)
      return list
    },
    copySearchForm() {
      this.clean(this.searchForm)
      let processdSearchForm = {}
      for (var k in this.searchForm) {
        if (k === 'skuList' || k === 'firstBatchList') {
          processdSearchForm[k] = this.searchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else {
          processdSearchForm[k] = this.searchForm[k]
        }
      }
      return processdSearchForm
    },
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList
    })
  },
  watch: {
    tableSelection: {
      //   // 失败（-1），异常（-2）参数 重试接口参数过滤
      handler(newVal) {
        this.retryArr =
          newVal &&
          newVal.filter((rowItem) => {
            return rowItem.status === -1 || rowItem.status === -2
          })
        this.removeArr =
          newVal &&
          newVal.filter((rowItem) => {
            return rowItem.status === 0
          })
      },
      deep: true
    }
  },
  methods: {
    async getUserCompany() {
      const defaultCompany = await getUserCompany();
      this.searchForm.factory = defaultCompany;
    },
    getButtonAuth,
    // 查询列表
    search() {
      this.$refs.searchForm.validate(valid => {
        if (valid) {
          if (!this.searchForm.skuList && !this.searchForm.firstBatchList) {
            this.$alert('SKU编码和批次至少填写一项')
            return
          }
          if (this.copySearchForm.skuList?.length > 100) {
            this.$alert('输入SKU编码不应该超过100项')
            return
          }
          if (this.copySearchForm.firstBatchList?.length > 100) {
            this.$alert('输入批次号不应该超过100项')
            return
          }

          this.getCargoRightsList()
        } else {
          return false
        }
      })
    },
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    },
    // 清楚所有查询参数
    reset() {
      this.$refs['searchForm'].resetFields()
    },
    // 列表查詢
    getCargoRightsList() {
      this.loading = true
      this.listQueryInfo.current = 1
      this.listQueryInfo.size = 50
      let query = {
        current: this.listQueryInfo.current,
        size: this.listQueryInfo.size
      }
      Object.assign(this.copySearchForm, query)
      getCargoRightsList(this.copySearchForm).then((res) => {
        this.loading = false
        this.cargoRightsList = res.skuAvailList
        this.total = res.total
      })
    },
    getPaginationCargoRightsList() {
      this.loading = true
      let query = {
        current: this.listQueryInfo.current,
        size: this.listQueryInfo.size
      }
      Object.assign(this.copySearchForm, query)
      getCargoRightsList(this.copySearchForm).then((res) => {
        this.loading = false
        this.cargoRightsList = res.skuAvailList
        this.total = res.total
      })
    },
    // 勾选数据项
    handleSelectionChange(selection) {
      selection.forEach((item) => {
        item.createUser = window.CUR_DATA.user.name
      })
      this.tableSelection = selection
    },
    // 批量
    checkboxSelect(row, index) {
      switch (row.status) {
        case -1:
          return true
        case -2:
          return true
        case 1:
          return false
        case 0:
          return true
      }
    },
    // 批量转移
    batchCargoRigthsRemove() {
      if (this.retryArr.length > 0) {
        this.$alert('请勾选只包含一键转移的选项')
        return
      }
      this.loading = true
      batchCargoRightsRemove(this.tableSelection)
        .then((res) => {
          let resList = Object.keys(res)
          this.tableSelection.forEach((item, index) => {
            let str = `${item.sku}_${item.firstBatch}_${item.factory}`
            let tmpKey = resList.find(
              (combinationItem) => combinationItem === str
            )
            if (tmpKey) {
              item.msg = res[tmpKey].msg
              item.status = res[tmpKey].code
              if (res[tmpKey].code === 0) {
                item.status = 1
              }
            }
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 单个转移
    batchCargoRigthsSingelRemove(tableSelection) {
      this.loading = true
      tableSelection.createUser = window.CUR_DATA.user.name
      let tableSelectio = []
      tableSelectio.push(tableSelection)
      batchCargoRightsRemove(tableSelectio)
        .then((res) => {
          let resList = Object.keys(res)
          tableSelectio.forEach((item, index) => {
            let tmpKey = resList[index]
            item.msg = res[tmpKey].msg
            item.status = res[tmpKey].code
            if (res[tmpKey].code === 0) {
              item.status = 1
            }
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 批量重试
    batchCargoRightsRemoveRetry() {
      if (this.removeArr.length > 0) {
        this.$alert('请勾选只包含重试的选项')
        return
      }
      this.loading = true
      let itemKeyListTmp = []
      // 参数过滤 成功数据过滤掉
      this.tableSelection = this.tableSelection.filter(
        (item) => item.status !== 1
      )
      this.tableSelection.forEach((item) => {
        let str = `${item.sku}_${item.firstBatch}_${item.factory}`
        itemKeyListTmp.push(str)
      })

      batchCargoRightsRemoveRetry(itemKeyListTmp)
        .then((res) => {
          let resList = Object.keys(res)
          this.tableSelection.forEach((item, index) => {
            let str = `${item.sku}_${item.firstBatch}_${item.factory}`
            let tmpKey = resList.find(
              (combinationItem) => combinationItem === str
            )
            if (tmpKey) {
              item.msg = res[tmpKey].msg
              item.status = res[tmpKey].code
              if (res[tmpKey].code === 0) {
                item.status = 1
              }
            }
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 单个重试
    batchCargoRightSigleRemoveRetry(tableSelection) {
      this.loading = true
      let tableSelectio = []
      tableSelectio.push(tableSelection)
      let str = `${tableSelection.sku}_${tableSelection.firstBatch}_${tableSelection.factory}`
      batchCargoRightsRemoveRetry({
        itemKeyList: str,
        createUser: window.CUR_DATA.user.name
      })
        .then((res) => {
          let resList = Object.keys(res)
          tableSelectio.forEach((item, index) => {
            let tmpKey = resList[index]
            item.msg = res[tmpKey].msg
            item.status = res[tmpKey].code
            if (res[tmpKey].code === 0) {
              item.status = 1
            }
          })
        })
        .finally(() => {
          this.loading = false
        })
    },
    headStyle() {
      return 'text-align:center'
    }
  }
}
</script>
<style scoped lang="less">
.btn-container {
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  .mini-container {
    margin-left: 120px;
  }
  .notify {
    width: 750px;
    line-height: 16px;
    font-size: 12px;
    margin-bottom: 5px;
  }
}
.flex-box {
  display: flex;
}
.ellips {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 250px;
}
.pagination-container {
  background-color: #fff;
  padding: 16px;
}
.red {
  color: red;
}
</style>
