<template>
  <el-dialog
    title="操作结果"
    :visible.sync="resultVisible"
    :before-close="closeResultDlg"
    :close-on-click-modal="false"
    width="600px"
  >
    <div class="notice">
      <span>{{resultData.title}}</span>
    </div>
    <el-table max-height="400" :data="resultData.list">
      <el-table-column align="center" width="70" prop="状态" label="状态">
        <template slot-scope="{row}">
          <el-tag v-if="row.index" type="danger" effect="dark">失败</el-tag>
          <el-tag v-else type="success" effect="dark">成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" width="110" prop="index" label="EXCEL行号" show-overflow-tooltip />
      <el-table-column align="center" width="60" prop="identifier" label="识别号" show-overflow-tooltip />
      <el-table-column align="center" prop="orderNo" label="订单号" show-overflow-tooltip />
      <el-table-column align="center" prop="reason" label="失败描述" show-overflow-tooltip />
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" type="primary" @click="downloadExcel()">下载EXCEL</el-button>
      <el-button size="mini" @click="closeResultDlg">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { writeFile } from '@boss/excel'
export default {
  name: 'resultDialog',
  props: {
    resultData: {
      type: Object,
      default: () => ({
        title: '',
        list: []
      })
    },
    closeResultDlg: {
      type: Function,
      default: () => {}
    },
    resultVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      header: ['状态', 'EXCEL行号', '识别号', '订单号', '失败描述'],
      props: ['status', 'index', 'identifier', 'orderNo', 'reason']
    }
  },
  methods: {
    downloadExcel () {
      if (this.resultData.list && this.resultData.list.length) {
        const fileName = new Date().toLocaleDateString().replace(/\//g, '-') + '操作结果导出明细.xlsx'
        const fileData = []
        this.resultData.list.forEach(data => {
          const item = {
            '状态': data.index ? '失败' : '成功',
            'EXCEL行号': data.index ? data.index : '',
            '识别号': data.identifier ? data.identifier : '',
            '订单号': data.orderNo ? data.orderNo : '',
            '失败描述': data.reason ? data.reason : ''
          }
          fileData.push(item)
        })
        writeFile(fileData, fileName, { raw: false })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.notice{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: -20px;
  margin-bottom: 5px;
}
</style>
