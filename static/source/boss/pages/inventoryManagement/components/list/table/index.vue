<template>
  <div class='list-table'>
    <vxe-table
      border
      highlight-hover-row
      show-overflow
      height="500"
      align="center"
      size="small"
      :loading="loading"
      :scroll-y="{gt: 500}"
      :data="itemList"
      :checkbox-config="{checkMethod}"
      @checkbox-change="onTableSelectionChange"
      @checkbox-all="onTableSelectionAllChange"
    >
      <vxe-table-column
        v-for="col in getColumns"
        :width="col.width"
        :key="col.name"
        :field="col.prop"
        :title="col.name"
      >
        <template slot-scope="{row,rowIndex}">
          <el-input-number
            v-if="col.type==='number'"
            v-model="row[col.prop]"
            size="mini"
            :placeholder="col.name"
            :disabled="disabled"
            :precision="0"
            :min="0"
            :max="999"
            @change="val=>handleNumberChange(val,row)"
          />
          <el-input
            v-else-if="col.type==='input'"
            v-model="row[col.prop]"
            size="mini"
            :placeholder="col.name"
            :maxlength="col.maxlength||80"
            :disabled="disabled"
            @change="val=>handleInputeChange(val,col.prop,row)"
          />
          <el-checkbox
            v-else-if="col.type==='checkbox'"
            v-model="row[col.prop]"
            true-label="1"
            false-label="0"
            @change="val=>handleChangeCheckbox(val,col.prop,row)"
          />
          <el-date-picker
            v-else-if="col.type==='date-picker'"
            v-model="row[col.prop]"
            clearable
            size="mini"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            style="width:100%"
            :disabled="disabled"
            @change="val=>handleChangeItemDate(val,row)"
          />
          <el-select
            v-else-if="col.type==='select'"
            v-model="row[col.prop]"
            :disabled="disabled"
            filterable
            clearable
            size="mini"
            @change="val=>changeWarehouseLocation(col.prop,val,rowIndex,row)"
          >
            <el-option
              v-for="item in buildOptions(row.factoryCode)"
              :key="item.label"
              :label="item.label+' '+item.value"
              :value="item.value">
            </el-option>
          </el-select>
          <span v-else-if="col.type ==='text'" style="text-align:center;">
            <el-link v-if="col.prop === 'orderNo'" @click="toDetail(row)" type="primary">{{row.orderNo}}</el-link>
            <span v-else-if="col.prop === 'supplier'">{{formatString(3, row.supplierNo, row.supplierName)}}</span>
            <span v-else>{{getLabel(col.prop, row[col.prop])}}</span>
          </span>
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { mapState } from 'vuex'
import { formatString, sortColumn } from '@/utils/mm'
export default {
  name: 'list-table',
  props: ['finalFields', 'itemList', 'loading'],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(data => data.field === 'list-table')
        console.log(ret)
        ret = sortColumn(ret, this.columnSetting)
      })
      return ret
    }
  },
  data () {
    return {
      columnSetting: ['申请单号', 'SAP申请单号', '单据类型', '单据状态', '创建方式', '供应商', '创建人', '凭证日期', '过账日期', '传输信息']
    }
  },
  methods: {
    formatString,
    getLabel (prop, value) {
      return (
          (this.buildOptions(prop) || []).find(
            // eslint-disable-next-line eqeqeq
            (it) => it.value == value
          ) || {}
        ).label || value
    },
    handleItemChange (value, prop) {
      console.log(value, prop)
    },
    buildOptions (prop) {
      if (prop === 'orderType') prop = 'iaoType'
      if (prop === 'orderStatus') prop = 'iaoStatus'
      if (prop === 'createWay') prop = 'iaoCreateWay'
      let ret = []
      safeRun(() => {
        if (!this.dictList[prop]) return
        ret = this.dictList[prop].map(prop => ({
          value: prop.value,
          label: prop.name
        }))
      })
      return ret
    },
    checkMethod ({ row }) {
      return true
    },
    onTableSelectionChange ({ records }) {
      this.$emit('selectionChange', records)
    },
    onTableSelectionAllChange ({ records }) {
      this.$emit('selectionChange', records)
    },
    toDetail (row) {
      this.$router.push({
        path: `/inventoryManagement/detail/${row.orderNo}`,
        query: { tagName: `${row.orderNo}详情` }
      })
    }
  },
  created () {
    console.log(this.createData)
  }
}
</script>
<style lang="scss" scoped>

</style>
