<template>
  <el-dialog
    title="批量创建订单"
    :visible.sync="dlgVisible"
    width="600px"
    @open="openDlg"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form :model="batchData" :rules="rules" label-width="150px">
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="batchData.orderType" placeholder="请选择订单类型">
          <el-option
            v-for="item in orderTypeOptions"
            :key="item.value"
            :label="item.value+' '+item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="notice">注意：批量模板上传成功后，将会直接生成申请单，请确认信息无误后进行上传！</div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:showDialog', false)">取 消</el-button>
      <el-button @click="handleDownload" type="primary" plain>下载批量创建模板</el-button>
      <el-upload
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        :action="uploadAction"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button type="primary" >上传批量创建模板</el-button>
      </el-upload>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    },
    createBatchOrderType: { type: String, default: '' },
    buildOptions: Function
  },
  data () {
    return {
      loading: null,
      batchData: {
        orderType: this.createBatchOrderType ? this.createBatchOrderType : '20'
      },
      rules: {
        orderType: [
          { required: true, message: '请选择订单类型', trigger: 'change' }
        ]
      },
      resultData: {}
    }
  },
  computed: {
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    uploadAction () {
      return `/internal-api/mm/uploadInvExcel?orderType=${this.batchData.orderType}`
    },
    orderTypeOptions () {
      const options = this.buildOptions('iaoType');
      if (this.createBatchOrderType === '32') {
        return options.filter(item => item.value === this.createBatchOrderType)
      }
      return options.filter(item => !['32', '33', '34'].includes(item.value))
    }
  },
  methods: {
    openDlg () {
      if (this.$refs['batchData']) {
        this.$refs['batchData'].resetFields()
      }
    },
    handleDownload () {
      if (this.batchData.orderType === '32') {
        window.open('https://static.zkh360.com/file/2024-12-25/%E9%9D%9EEVM%E5%AF%84%E5%94%AE%E7%9B%98%E4%BA%8F%E5%8D%95%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA%E6%A8%A1%E6%9D%**********.xlsx')
      } else if (this.batchData.orderType === '35') {
        window.open('https://static.zkh360.com/file/2025-05-23/%E5%BA%93%E5%AD%98%E7%9B%98%E7%82%B9%E5%8D%95%E6%A8%A1%E6%9D%BF-1747972673789.xlsx')
      } else {
        window.open('//static.zkh360.com/file/resource/orderPurchase/%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA%E5%BA%93%E5%AD%98%E7%94%B3%E8%AF%B7%E5%8D%95V7.xlsx')
      }
    },
    processErrMsg (res) {
      const { data } = res
      if (data) {
        const { totalCount, failCount, successCount, importResultList } = data
        let title = `结果信息如下：总共${totalCount}单，其中成功${successCount}单，失败${failCount}单。`
        this.resultData.title = title
        if (!Array.isArray(importResultList)) return
        importResultList.forEach(ipRes => {
          if (Array.isArray(ipRes.failReasonList)) {
            ipRes.failReasonList = ipRes.failReasonList.map(item => ({
              ...item,
              ...ipRes
            }))
          }
        })
        this.resultData.list = importResultList.reduce((prev, next) => {
          if (next && Array.isArray(next.failReasonList)) {
            prev.push(...next.failReasonList)
          } else {
            prev.push(next)
          }
          return prev
        }, [])
      }
    },
    handleSuccess (res) {
      if (this.loading) {
        this.loading.close()
      }
      if (res && res.code === 0) {
        this.processErrMsg(res)
        this.$emit('update:showDialog', false)
        this.$emit('uploadSuccess', this.resultData)
      }
      if (res && res.code !== 0 && res.msg) {
        this.$alert(res.msg, '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false
      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleError () {
      if (this.loading) {
        this.loading.close()
      }
    }
  }
}
</script>

<style lang="scss">
.uploadMsg .el-message-box__message {
  max-height: 500px;
  overflow: auto;
}
</style>
