<template>
  <div class='detail-table'>
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      ref="detailGrid"
      height="400"
      id="detail_grid"
      align="center"
      :scroll-y="{gt: 300}"
      :custom-config="tableCustom"
      :data="detail.itemList"
      :columns="getColumns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}">
      <template v-slot:toolbar_buttons>
        <div class="text">
          <span v-if="detail.orderType === '32'">交货总金额：{{ detail.deliveryTotalAmount }}</span>
        </div>
      </template>
      <template v-slot:isDeleted="{row}">
        {{row.isDeleted == 1 ? '已删除' : ''}}
      </template>
      <template v-slot:factoryCode="{row}">
        {{formatString(3, row.factoryCode, readNameFromDic('factoryCode', row.factoryCode))}}
      </template>
      <template v-slot:receiveFactoryCode="{row}">
        {{formatString(3, row.receiveFactoryCode, readNameFromDic('receiveFactoryCode', row.receiveFactoryCode))}}
      </template>
      <template v-slot:warehouseLocation="{row}">
        {{formatString(3, row.warehouseLocation, readNameFromDic('warehouseLocation', row.warehouseLocation))}}
      </template>
      <template v-slot:receiveWarehouseLocation="{row}">
        {{formatString(3, row.receiveWarehouseLocation, readNameFromDic('receiveWarehouseLocation', row.receiveWarehouseLocation))}}
      </template>
      <template v-slot:generalLedgerAccount="{row}">
        {{formatString(3, row.generalLedgerAccount, readNameFromDic('iaoGeneralLedger', row.generalLedgerAccount))}}
      </template>
      <template v-slot:costCenter="{row}">
        {{formatString(3, row.costCenter, readFromCostCenter(row.costCenter))}}
      </template>
      <template v-slot:inventoryUnit="{row}">
        {{findUnitName(row.inventoryUnit)}}
      </template>
      <template v-slot:oaType="{row}">
        {{findOaType('oaType', row.oaType)}}
      </template>
      <template v-slot:unit="{row}">
        {{findUnitName(row.unit)}}
      </template>
    </vxe-grid>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { readNameFromDic, formatString, sortColumn, moveInArray } from '@/utils/mm'
import { mapState } from 'vuex'
export default {
  name: 'detailTable',
  props: ['finalFields', 'detail', 'columnSetting', 'costCenterList'],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(data => data.field === 'table' && data.isDetail === 1)
        ret = sortColumn(ret, this.columnSetting)
        if (this.detail.orderType === '20') {
          moveInArray(ret, '参考单号', 'name', 3)
          moveInArray(ret, '参考行号', 'name', 4)
        }
        const mapList = [
          'isDeleted', 'factoryCode', 'receiveFactoryCode', 'warehouseLocation', 'receiveWarehouseLocation',
          'generalLedgerAccount', 'costCenter', 'inventoryUnit', 'unit', 'oaType'
        ]
        ret = ret.map(item => {
          let tmp = {
            _field: item.field,
            title: item.name,
            field: item.prop,
            width: item.width || 120
          }
          mapList.forEach(prop => {
            if (item.prop === prop) {
              tmp.slots = { default: prop }
            }
          })
          return tmp
        })
      })
      // console.log(ret)
      return ret
    }
  },
  data () {
    return {
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      }
    }
  },
  methods: {
    readNameFromDic,
    formatString,
    findUnitName (value) {
      const unitList = this.dictList['orderUnit']
      if (Array.isArray(unitList) && unitList.length) {
        const item = unitList.find(item => item.value === value)
        if (item && item.name) {
          value = item.name
        }
      }
      return value
    },
    findOaType (type, value) {
      const typeList = this.dictList[type]
      let ret = value
      safeRun(() => {
        if (typeList) {
          // eslint-disable-next-line eqeqeq
          const find = typeList.find(item => item.value == value)
          if (find && find.name) {
            ret = find.name
          }
        }
      })
      return ret
    },
    readFromCostCenter (value) {
      console.log(this.costCenterList)
      if (this.costCenterList) {
        const item = this.costCenterList.find(center => center.costCenter === value)
        if (item) {
          return item.description
        }
      }
    },
    toDetail (row) {
      this.$router.push(`/inventoryManagement/detail/${row.orderNo}`)
    },
    handleItemChange (value, prop) {
      console.log(value, prop)
    },
    buildOptions (props) {
      return []
    },
    checkMethod ({ row }) {
      return true
    },
    onTableSelectionChange ({ records }) {
      this.$emit('selectionChange', records)
    },
    onTableSelectionAllChange ({ records }) {
      this.$emit('selectionChange', records)
    }
  },
  created () {
    console.log(this.createData)
  }
}
</script>
<style lang="scss" scoped>
</style>
