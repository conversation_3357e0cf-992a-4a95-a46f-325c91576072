<template>
  <div class="contact-info">
    <el-form label-width="160px">
      <el-row>
        <el-col class="row-item" v-for="item in getColumns" :key="item.name" :span="item.span || 6">
          <span class="label">{{labelColon(item.name) || ''}}</span>
          <span class="content" v-if="item.prop==='transportAddress'">{{showAddress('transport')}}</span>
          <span class="content" v-else-if="item.prop==='zkhAddress'">{{showAddress('zkh')}}</span>
          <span class="content" v-else-if="/contact/gi.test(item.prop)">{{detail[item.prop]}}</span>
          <span class="content" v-else-if="item.type==='select'">{{readNameFromDic(item.prop, detail[item.prop])}}</span>
          <span class="content" v-else>{{detail[item.prop]}}</span>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { labelColon, readNameFromDic, sortColumn } from '@/utils/mm'
export default {
  name: 'contactInfo',
  props: ['detail', 'finalFields'],
  data () {
    return {
      columnSetting: [
        '运输方向', '运输地址联系人', '运输联系人电话', '运输地址', '震坤行联系人', '震坤行联系人电话', '震坤行地址'
      ]
    }
  },
  computed: {
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(field => field.field === 'contact')
        ret = sortColumn(ret, this.columnSetting)
      })
      return ret
    }
  },
  methods: {
    labelColon,
    readNameFromDic,
    joinWithSpace(...args) {
      return args.map(item => item).join(' ')
    },
    showAddress (type) {
      let ret = ''
      if (type === 'zkh') {
        const { zkhProvinceText, zkhCityText, zkhRegionText, zkhStreetText, zkhAddressDetail } = this.detail
        ret = this.joinWithSpace(zkhProvinceText, zkhCityText, zkhRegionText, zkhStreetText, zkhAddressDetail)
      }
      if (type === 'transport') {
        const { transportProvinceText, transportCityText, transportRegionText, transportStreetText, transportAddressDetail } = this.detail
        ret = this.joinWithSpace(transportProvinceText, transportCityText, transportRegionText, transportStreetText, transportAddressDetail)
      }
      return ret
    },
    handleClick () {}
  },
  created () {}
}
</script>
<style scoped >
.row-item{
  margin-bottom: 10px;
}
</style>
