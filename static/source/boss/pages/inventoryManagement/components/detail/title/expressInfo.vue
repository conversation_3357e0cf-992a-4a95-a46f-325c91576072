<template>
  <div class="express-info">
    <vxe-table
      border
      highlight-hover-row
      show-overflow
      height="250"
      align="center"
      size="small"
      :loading="tableLoading"
      :scroll-y="{gt: 500}"
      :data="dataList"
    >
      <vxe-table-column
        v-for="col in getColumns"
        :key="col.name"
        :field="col.prop"
        :title="col.name"
      >
        <template slot-scope="{row,rowIndex}">
          <span v-if="row.prop === 'index'">{{rowIndex}}</span>
          <span v-else>{{row[col.prop]}}</span>
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { labelColon, readNameFromDic } from '@/utils/mm'
import { getExpressDetail } from '@/api/mm'
export default {
  name: 'expressInfo',
  props: ['detail', 'finalFields', 'expressData', 'updateExpressData'],
  computed: {
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(field => field.field === 'expressInfo')
      })
      return ret
    }
  },
  data () {
    return {
      tableLoading: false,
      dataList: []
    }
  },
  methods: {
    labelColon,
    readNameFromDic,
    getExpressDetail () {
      const params = {
        iaoNo: this.detail.orderNo
      }
      this.tableLoading = true
      getExpressDetail(params)
        .then(res => {
          if (res) {
            if (res.taskInfoList) {
              res.taskInfoList = res.taskInfoList.map((item, index) => ({
                ...item,
                index,
                status: res.status
              }))
              this.dataList = res.taskInfoList
            }
            this.updateExpressData(res)
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    joinWithSpace(...args) {
      return args.map(item => item).join(' ')
    },
    showAddress (type) {
      let ret = ''
      if (type === 'zkh') {
        const { zkhProvinceText, zkhCityText, zkhRegionText, zkhStreetText, zkhAddressDetail } = this.detail
        ret = this.joinWithSpace(zkhProvinceText, zkhCityText, zkhRegionText, zkhStreetText, zkhAddressDetail)
      }
      if (type === 'transport') {
        const { transportProvinceText, transportCityText, transportRegionText, transportStreetText, transportAddressDetail } = this.detail
        ret = this.joinWithSpace(transportProvinceText, transportCityText, transportRegionText, transportStreetText, transportAddressDetail)
      }
      return ret
    },
    handleClick () {}
  },
  created () {
    if (!this.expressData) {
      this.getExpressDetail()
    } else {
      this.dataList = this.expressData.taskInfoList || []
    }
  }
}
</script>
<style scoped >
.row-item{
  margin-bottom: 10px;
}
</style>
