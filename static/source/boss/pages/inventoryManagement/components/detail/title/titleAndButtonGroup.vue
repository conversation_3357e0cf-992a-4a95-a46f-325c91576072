<template>
  <div class="title-button-group">
    <div class="left">
      <span style="font-size: 20px;vertical-align: middle;">
        {{readNameFromDic('iaoType', this.detail.orderType)}}
        {{detail.orderNo}}
        {{readNameFromDic('iaoStatus', detail.orderStatus) ? '- ' + readNameFromDic('iaoStatus', detail.orderStatus) : ''}}
      </span>
      <el-tag v-if="detail && detail.sapStatus === 0" type="default">待推送</el-tag>
      <el-tag v-if="detail && detail.sapStatus === 1" type="success">推送中</el-tag>
      <el-tag v-if="detail && detail.sapStatus === 2" type="danger">推送失败</el-tag>
      <el-tag v-if="detail && detail.sapStatus === 3" type="success">推送成功</el-tag>
    </div>
    <div class="right">
      <el-button type="primary" @click="getInvDetail">刷新</el-button>
      <template v-if="detail && !cantCreateIaoType.includes(detail.orderType)">
        <el-button v-if="['23', '35'].includes(detail.orderType) && getButtonAuth('库存申请单列表', '审批申请单')" type="primary" :disabled="disableAudit" @click="_=>postAccountOrAudit('audit', 'post')">审批</el-button>
        <el-button v-if="getButtonAuth('库存申请单列表', '过账')" type="primary" :disabled="disablePost" @click="_=>postAccountOrAudit('account', 'post')">过账</el-button>
        <el-button v-if="getButtonAuth('库存申请单列表', '修改申请单')" type="primary" :disabled="disableModify" @click="_=>modifyOrder('modify')">修改申请单</el-button>
        <el-button v-if="getButtonAuth('库存申请单列表', '取消申请单')" type="danger" :disabled="disableDel" @click="_=>modifyOrder('delete')">取消申请单</el-button>
      </template>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { mapKeyToValue, readNameFromDic, submitErrorHandler } from '@/utils/mm'
import { invOrderDelete, postAccount, postAudit } from '@/api/mm'
import { safeRun } from '@/utils/index'
import { getButtonAuth } from '@/utils/auth'
import { cantCreateIaoType } from '@/pages/inventoryManagement/utils/createEditFields'
export default {
  name: 'titleAndButtonGroup',
  props: ['detail', 'getInvDetail', 'updateLoading'],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      orderType: state => state.orderPurchase.dictList.orderType
    }),
    orderName () {
      let ret = ''
      safeRun(() => {
        // eslint-disable-next-line
        ret = this.dictList.iaoType.find(item => item.value == this.detail.orderType)
        if (ret) ret = ret.name
      })
      return ret
    },
    disableModify () {
      const { transportMessage, orderStatus, orderType } = this.detail
      if (['32', '33', '34', '35'].includes(orderType)) {
        return true
      }
      // eslint-disable-next-line eqeqeq
      if (transportMessage == '1' || (orderStatus != 1 && orderStatus != 7)) {
        return true
      }
      return false
    },
    disablePost () {
      const { transportMessage, orderStatus, orderType } = this.detail
      if (orderType === '27') {
        if (transportMessage === 0) {
          return false
        }
        return true
      }
      // eslint-disable-next-line eqeqeq
      if (transportMessage == 1 || (orderStatus == 5 || orderStatus == 6 || orderStatus == 2)) {
        return true
      }
      const disableList = [ '22', '29', '30', '31', '32', '33', '34', '35' ]
      if (disableList.includes(orderType)) {
        return true
      }
      return false
    },
    disableAudit () {
      const { transportMessage, orderStatus, orderType } = this.detail
      // eslint-disable-next-line eqeqeq
      if ((orderType == '23' || orderType == '35') && transportMessage == 1 && orderStatus === 9) {
        // 需要传输至坤合， 且 orderStatus == 9 坤合占库成功 审批按钮才开放
        return false
      }
      return true
    },
    disableDel () {
      const { orderStatus } = this.detail
      // eslint-disable-next-line eqeqeq
      if (orderStatus == 2 || orderStatus == 5 || orderStatus == 6) {
        return true
      }
      return false
    }
  },
  data () {
    return {
      cantCreateIaoType
    }
  },
  methods: {
    mapKeyToValue,
    getButtonAuth,
    readNameFromDic,
    modifyOrder (type) {
      if (type === 'delete') {
        this.$confirm('请确认是否取消申请单？', '操作提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.updateLoading(true)
          const params = {
            orderNo: this.detail.orderNo,
            updateUser: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
          }
          invOrderDelete(params)
            .then(res => {
              if (res) {
                const { code, data, msg } = res
                if (code === 0) {
                  this.$alert('取消成功！', '操作提示', {
                    confirmButtonText: '确定',
                    type: 'success',
                    callback: _action => {
                      this.getInvDetail()
                    }
                  })
                } else {
                  let errMsg = ''
                  submitErrorHandler(errMsg, data, msg)
                }
              } else {
                this.$alert('操作失败！', '操作提示', {
                  confirmButtonText: '确定',
                  type: 'error',
                  callback: _action => {
                    this.getInvDetail()
                  }
                })
              }
            })
            .finally(() => {
              this.updateLoading(false)
            })
        })
      }
      if (type === 'modify') {
        const id = this.$route.params.id
        this.$closeTag(this.$route.path)
        setTimeout(() => {
          this.$router.push({
            path: `/inventoryManagement/edit/${id}`,
            query: { tagName: `${id}修改` }
          })
        }, 100)
      }
    },
    postAccountOrAudit (account, type) {
      let title = ''
      let content = ''
      let requestApi = postAccount
      if (account === 'account') {
        title = type === 'post' ? '过账' : '取消过账'
        content = type === 'post' ? '请确认是否要对该对账单过账？' : '请确认是否要取消过账？'
      }
      if (account === 'audit') {
        requestApi = postAudit
        title = '审批'
        content = '请确认是否要提交审批？'
      }
      this.$confirm(content, title, {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const data = new FormData()
        safeRun(() => {
          data.append('orderNo', this.detail.orderNo)
          data.append('updateUser', window.CUR_DATA.user.name)
        })
        this.updateLoading(true)
        requestApi(data)
          .then(res => {
            if (res) {
              const { code, data, msg } = res
              if (code === 0 && data === null) {
                this.$alert(`${title}成功！`, '操作提示', {
                  confirmButtonText: '确定'
                })
              } else if (code !== 0) {
                let errMsg = `${title}失败！<br>`
                submitErrorHandler(errMsg, data, msg)
              }
            }
          })
          .finally(() => {
            this.updateLoading(false)
            this.getInvDetail()
          })
      })
    }
  },
  created () {
    console.log(this.detail)
  }
}
</script>
<style lang="scss" scoped>
.title-button-group{
  margin-top:10px;
  margin-bottom:10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left span{
    margin-right: 10px;
  }
}
</style>
