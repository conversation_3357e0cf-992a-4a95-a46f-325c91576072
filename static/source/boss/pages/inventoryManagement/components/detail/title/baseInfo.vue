<template>
  <div class="base-info">
    <Title :detail="detail" :updateLoading="updateLoading" :getInvDetail="getInvDetail" />
    <el-form label-width="130px" label-position="right">
      <el-row :gutter="10">
        <el-col class="row-item" v-for="item in getColumns" :span="item.span || 8" :key="item.name">
          <span class="label">{{labelColon(item.name) || ''}}</span>
          <span class="content" v-if="item.prop==='simResult'" :style="{color: detail.simStatus === 2 ? 'red' : ''}">
            {{detail[item.prop]}}
          </span>
          <span class="content" v-else-if="item.prop==='iaoType'">{{readNameFromDic(item.prop, detail.orderType)}}</span>
          <span class="content" v-else-if="item.prop==='iaoStatus'">{{readNameFromDic(item.prop, detail.orderStatus)}}</span>
          <el-link class="content" v-else-if="item.prop==='processInstanceId'" target="_blank" :href="`/wb/so-task/list?processInstanceId=${detail.processInstanceId}&status=`">{{detail.processInstanceId}}</el-link>
          <span class="content" v-else-if="item.type==='select'">{{readNameFromDic(item.prop, detail[item.prop])}}</span>
          <span class="content" v-else>
            <CutParagraph :content="detail[item.prop]" splitSapMsg :maxLen="100" effect="dark" />
          </span>
          <el-button
            v-if="item.prop === 'orderNo'"
            @click="resendSap"
            size="mini"
            class="mini-button"
            type="primary"
            >推送S4/坤合
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import CutParagraph from '@/components/mm/CutParagraph'
import { mapKeyToValue, labelColon, formatString, sortColumn, readNameFromDic } from '@/utils/mm'
import { invResendSap, invResendMQ } from '@/api/mm'
import Title from './titleAndButtonGroup'
import { safeRun } from '@/utils/index'
import { detailColumnSettings } from '@/pages/inventoryManagement/utils/tableColumnSetting.js'

export default {
  name: 'baseInfo',
  components: { Title, CutParagraph },
  props: ['detail', 'finalFields', 'getInvDetail', 'updateLoading'],
  data () {
    return {
      // columnSetting: [
      //   '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '来源系统', '来源系统单号', '抬头备注',
      //   { name: '坤合返回消息', span: 16 }, 'sim返回消息', { name: 'SAP返回消息', span: 24 }
      // ],
      // columnSetting23: [
      //   '单据类型', '申请单号', '单据状态', '创建人员', '凭证日期', '过账日期', '传输信息', '抬头备注',
      //   { name: '责任归属', prop: 'responsibilityOwner', required: true },
      //   { name: '坤合返回消息', span: 16 }, 'sim返回消息', { name: 'SAP返回消息', span: 24 }
      // ]
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      purchaseList: state => state.orderPurchase.purchaseList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(field => field.field === 'baseInfo')
        // ret = sortColumn(ret, this.detail.orderType === '23' ? this.columnSetting23 : this.columnSetting)
        const specialType = ['23', '32']
        const columnSettings = specialType.includes(this.detail.orderType) ? detailColumnSettings[`columnSetting${this.detail.orderType}`] : detailColumnSettings.columnSetting
        ret = sortColumn(ret, columnSettings)
        ret = ret.map(item => {
          if (item.prop === 'orderType') item.prop = 'iaoType'
          if (item.prop === 'orderStatus') item.prop = 'iaoStatus'
          return item
        })
      })
      return ret
    }
  },
  methods: {
    formatString,
    mapKeyToValue,
    labelColon,
    readNameFromDic,
    async resendSap () {
      this.updateLoading(true)
      const data = new FormData()
      safeRun(() => {
        data.append('orderNo', this.detail.orderNo)
        data.append('updateUser', window.CUR_DATA.user.name)
      })
      await Promise.all([invResendSap(data), invResendMQ(data)])
      this.getInvDetail()
    },
    handleClick () {}
  },
  created () {}
}
</script>
<style lang="scss" scoped>
.base-info{
  .mini-button{
    padding: 1px 5px;
    height: 22px;
  }
  .row-item{
    margin-bottom: 10px;
    line-height:21px;
    height: 21px;
    button{
      margin-left: 5px;
    }
  }
  .cut-two-line {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .cut-one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .word-no-wrap {
    white-space: nowrap;
  }
  .container{
    .body{
      display: flex;
      align-items: center;
      flex-direction: column;
    }
    .text{
      display: flex;
      margin: 10px;
      width: 90%;
      span{
        white-space:nowrap
      }
    }
    .dialog-footer{
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
