<template>
  <div class='base-info'>
    <el-row :gutter="20" type="flex" style="flex-wrap: wrap;">
      <el-col v-for="item in getColumns" :span="item.span || 8" :key="item.prop">
        <el-form-item :label="item.name" :prop="item.prop">
          <div slot="label" v-if="createData.orderType == '30' && item.prop==='supplier'&&item.type==='custom'">
            <el-tooltip
              v-if="createData.orderType == '30' && item.prop==='supplier'&&item.type==='custom'"
              class="item" effect="dark" content="请选择寄售供应商" placement="top">
              <el-button style="font-size: 13px;color: #606266;" type="text" icon="el-icon-info">供应商</el-button>
            </el-tooltip>
            <span v-else>{{item.name}}</span>
          </div>
          <el-input
            v-if="item.prop==='supplier'&&item.type==='input'"
            :disabled="Boolean(item.disabled)"
            :placeholder="`请输入${item.name}`"
            :value="formatSupplier(createData.supplierNo, createData.supplierName)"
          />
          <SelectSupplier
            v-else-if="item.prop==='supplier'&&item.type==='custom'"
            :data.sync="createData[item.prop]"
            :disabled="Boolean(item.disabled)"
            :purchaseData="createData"
            :tooltip="createData.orderType == '30' ? '请输入寄售供应商' : ''"
            clearable
            @change="handleSupplierChange"
          />
          <el-input
            v-else-if="item.type==='input'"
            v-model="createData[item.prop]"
            :disabled="Boolean(item.disabled) || (disableRelateSku && !isSourceProp(item.prop))"
            :placeholder="`请输入${item.name}`"
          >
            <el-button :disabled="disableRelateSku" :loading="tableLoading" v-if="item.prop == 'relateOrderNo'" slot="append" @click="getRelateOrder">获取</el-button>
          </el-input>
          <el-input
            v-else-if="item.type==='textarea'"
            type="textarea"
            :maxlength="200"
            show-word-limit
            :autosize="{ minRows: 3, maxRows: 4}"
            v-model="createData[item.prop]"
            :disabled="Boolean(item.disabled)"
            :placeholder="`请输入${item.name}`"
          />
          <el-date-picker
            v-else-if="item.type==='daterange'"
            v-model="createData[item.prop]"
            :disabled="Boolean(item.disabled)"
            style="width:100%"
            clearable type="date" placeholder="选择日期"
          />
          <el-select
            v-else-if="item.prop==='responsibilityOwner'"
            v-model="createData[item.prop]"
            filterable
            :disabled="Boolean(item.disabled)"
            @change="val => handleItemChange(val, item.prop)"
            style="width:100%"
          >
            <el-option
              v-for="option in buildOptions(item.prop)"
              :key="option.label"
              :label="option.label"
              :value="parseValue(option.value, item.prop)">
            </el-option>
          </el-select>
          <el-input
            v-else-if="item.type==='select'"
            :value="readFromOptions(createData[item.prop], buildOptions(item.prop))"
            :disabled="Boolean(item.disabled)"
            placeholder="请输入"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { sortColumn } from '@/utils/mm'
import { mapState } from 'vuex'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
export default {
  name: 'EditBaseInfo',
  props: ['finalFields', 'createData', 'getRelateOrder', 'tableLoading', 'disableRelateSku', 'columnSetting', 'isEdit'],
  components: {
    SelectSupplier
  },
  data () {
    return {}
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(data => data.field === 'baseInfo' && data.isCreate === 1)
        ret = sortColumn(ret, this.columnSetting)
      })
      return ret
    }
  },
  methods: {
    isSourceProp (prop) {
      const list = ['source', 'sourceOrderNo']
      return list.find(item => item === prop)
    },
    readFromOptions (value, options) {
      // eslint-disable-next-line eqeqeq
      let item = options.find(option => option.value == value)
      if (item && item.label) {
        return item.label
      }
    },
    formatSupplier(no, name) {
      let ret = ''
      if (no) ret = no + ' '
      if (name) ret = ret + ' ' + name
      return ret
    },
    parseValue (value, prop) {
      let ret = value
      const numberProps = ['orderStatus', 'transportMessage']
      if (numberProps.includes(prop)) {
        ret = Number(value)
      }
      return ret
    },
    handleSupplierChange (val) {
      if (val) {
        this.createData.supplierNo = val.supplierNo
        this.createData.supplierName = val.supplierName
      } else {
        this.createData.supplierNo = undefined
        this.createData.supplierName = undefined
      }
    },
    handleItemChange (value, prop) {
      console.log(value, prop)
    },
    buildOptions (prop) {
      if (prop === 'orderType') prop = 'iaoType'
      if (prop === 'orderStatus') prop = 'iaoStatus'
      let ret = []
      safeRun(() => {
        if (!this.dictList[prop]) return
        ret = this.dictList[prop].map(prop => ({
          value: prop.value,
          label: prop.name
        }))
      })
      return ret
    }
  }
}
</script>
<style lang="scss" scoped>
.base-info{
  .fixed-button{
    position: fixed;
  }
}
</style>
