<template>
  <div class="contact-info">
    <el-row :gutter="20">
      <el-col v-for="item in getColumns" :key="item.prop"  :span="item.span">
        <el-row v-if="item.prop == 'transportDirection'" :gutter="20">
          <el-col :span="8">
            <el-form-item :label="item.name" :prop="item.prop">
              <el-select
                v-model="createData[item.prop]"
                clearable
                filterable
                :disabled="Boolean(item.disabled) || disableContact"
                @change="val => handleItemChange(item.prop, val)"
                style="width:100%"
              >
                <el-option
                  v-for="item in buildOptions(item.prop)"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <div ref="tips" class="tips">
              <span>
                <i class="el-icon-warning"></i>无需坤合物流选择“无运输”；坤合物流发往运输地址，选择“震坤行->运输地址”；坤合物流提货至震坤行，选择“运输地址->震坤行”
              </span>
            </div>
          </el-col>
        </el-row>
        <el-row v-else-if="item.prop == 'transportAddress'" :gutter="10">
          <el-col :span="22">
            <el-form-item :label="item.name" :prop="!newAddress ? item.prop: ''">
              <el-select
                v-model="createData[item.prop]"
                clearable
                filterable
                :disabled="Boolean(item.disabled) || disableContact || newAddress"
                @change="val => handleItemChange(item.prop, val)"
                style="width:100%"
              >
                <el-option
                  v-for="item in buildOptions(item.prop)"
                  :key="item.label"
                  :label="(item.label || '')+' '+item.value"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col v-if="item.prop === 'transportAddress'" :span="2">
            <el-button style="width: 100%" :disabled="disableContact" @click="addAddress">新增</el-button>
          </el-col>
          <el-col v-if="newAddress" :span="10">
            <el-form-item label="" :prop="createData.transportAddress">
              <ProvinceCascader
                v-model="createData.provinceArray"
                :maxDepth="4"
                :checkStrictly="true"
                @provinceChange="provinceChange"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="newAddress" :span="12">
            <el-input
              clearable
              v-model="createData.transportAddressDetail"
              placeholder="详细地址"
              @change="handleDetailAddress"
            />
          </el-col>
          <el-col v-if="newAddress" :span="2">
            <el-button style="width: 100%" @click="delAddress">删除</el-button>
          </el-col>
        </el-row>
        <el-form-item v-else :label="item.name" :prop="item.prop">
          <el-input
            v-if="item.type==='input'"
            v-model="createData[item.prop]"
            clearable
            :disabled="Boolean(item.disabled) || disableContact"
            :placeholder="`请输入${item.name}`"
          />
          <el-select
            v-else-if="item.type==='select'"
            v-model="createData[item.prop]"
            clearable
            filterable
            :allow-create="/contactName/gi.test(item.prop)"
            :disabled="Boolean(item.disabled) || disableContact"
            @change="val => handleItemChange(item.prop, val)"
            style="width:100%"
          >
            <el-option
              v-for="item in buildOptions(item.prop)"
              :key="item.label"
              :label="(item.label || '')+' '+item.value"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { sortColumn, delDuplicateProp } from '@/utils/mm'
import { mapState } from 'vuex'
import ProvinceCascader from '@/components/SearchFields/provinceCascaderAsync'
export default {
  name: 'create-contact-info',
  props: [
    'finalFields', 'createData', 'transportOptions', 'zkhOptions',
    'disableContact', 'clearValidate'
  ],
  components: {
    ProvinceCascader
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(data => data.field === 'contact' && data.isCreate === 1)
        ret = sortColumn(ret, this.columnSetting)
      })
      return ret
    }
  },
  created () {
    console.log(this.createData)
  },
  data () {
    return {
      columnSetting: [
        '运输方向', '运输地址联系人', '运输联系人电话', '运输地址', '震坤行联系人', '震坤行联系人电话', '震坤行地址'
      ],
      newAddress: false,
      contactAddress: [
        'transportAddress', 'transportContactName',
        'zkhAddress', 'zkhContactName'
      ]
    }
  },
  methods: {
    fullDetailAddress (item) {
      let ret = ''
      safeRun(() => {
        ret = (item.provinceText || '') +
                (item.cityText || '') +
                (item.regionText || '') +
                (item.streetText || '') +
                (item.detail || '')
      })
      return ret
    },
    setPhone(type, name) {
      safeRun(() => {
        let options = this.transportOptions.contactOptions
        if (/zkh/.test(type)) {
          options = this.zkhOptions.contactOptions
        }
        let contact = options.find(item => item.name === name)
        if (!contact) return
        this.createData[`${type.replace('Name', 'Number')}`] = contact.phone
      })
    },
    setAddress (type, val) {
      safeRun(() => {
        let options = this.transportOptions.addressOptions
        if (/zkh/.test(type)) {
          options = this.zkhOptions.addressOptions
        }
        let address
        if (val === null || val === 'clear' || val === '') {
          address = { detail: '', city: '', postCode: '', province: '', region: '', street: '' }
        } else {
          address = options.find(item => this.fullDetailAddress(item) === val)
        }
        if (!address) return
        let prop = type === 'zkhAddress' ? 'zkh' : 'transport'
        this.createData[`${prop}AddressDetail`] = address.detail
        this.createData[`${prop}City`] = address.city
        this.createData[`${prop}PostCode`] = address.postCode
        this.createData[`${prop}Province`] = address.province
        this.createData[`${prop}Region`] = address.region
        this.createData[`${prop}Street`] = address.street
        if (val === '') {
          this.createData[`${prop}Address`] = val
        }
        console.log(this.createData.transportAddress)
        console.log(this.createData.zkhAddress)
      })
    },
    handleItemChange (prop, val) {
      console.log(prop, val)
      switch (prop) {
        case 'transportDirection':
          this.createData.transportDirection = val;
          setTimeout(() => {
            this.clearValidate()
          })
          break;
        case 'transportContactName':
          this.setPhone('transportContactName', val); break;
        case 'zkhContactName':
          this.setPhone('zkhContactName', val); break;
        case 'zkhAddress':
          this.setAddress('zkhAddress', val); break;
        case 'transportAddress':
          this.setAddress('transportAddress', val); break;
      }
    },
    provinceChange (array) {
      safeRun(() => {
        console.log(array)
        // 省市区街道赋值
        if (!array || array.length === 0) {
          this.createData.transportProvince = ''
          this.createData.transportCity = ''
          this.createData.transportRegion = ''
          this.createData.transportStreet = ''
          return
        }
        this.createData.transportProvince = array[0].value
        this.createData.transportCity = (array[1] && array[1].value) || ''
        this.createData.transportRegion = (array[2] && array[2].value) || ''
        this.createData.transportStreet = (array[3] && array[3].value) || ''
      })
    },
    handleDetailAddress (value) {
      this.createData.transportAddressDetail = value
    },
    addAddress () {
      this.newAddress = true
      this.setAddress('transportAddress', 'clear')
      this.clearValidate()
      this.createData.transportAddress = ''
    },
    delAddress () {
      this.newAddress = false
      this.createData.provinceArray = []
      this.createData.transportAddressDetail = ''
    },
    handleChangeItem (value, prop) {
      console.log(value, prop)
    },
    buildAddressOptions (prop) {
      // delDuplicateProp
      if (/address/gi.test(prop)) {
        let options = this.transportOptions
        if (prop === 'zkhAddress') {
          options = this.zkhOptions
        }
        let list = options.addressOptions.map(address => ({
          ...address,
          value: this.fullDetailAddress(address)
        }))
        return delDuplicateProp(list, 'value')
      }
      if (/contactName/gi.test(prop)) {
        let options = this.transportOptions
        if (prop === 'zkhContactName') {
          options = this.zkhOptions
        }
        let list = options.contactOptions.map(contact => ({
          ...contact,
          value: contact.name
        }))
        return delDuplicateProp(list, 'value')
      }
    },
    buildOptions (prop) {
      if (this.contactAddress.includes(prop)) return this.buildAddressOptions(prop)
      let ret = []
      if (prop === 'transportDirection') {
        safeRun(() => {
          if (!this.dictList[prop]) return
          ret = this.dictList[prop].map(prop => ({
            value: Number(prop.value),
            label: prop.name
          }))
        })
      }
      return ret
    }
  }
}
</script>
<style scoped lang="less">
.contact-info{
  .tips{
    height: 32px;
    display: flex;
    align-items: center;
    margin-left: 13px;
    i{
      font-size: 1.2em;
    }
  }
}
</style>
