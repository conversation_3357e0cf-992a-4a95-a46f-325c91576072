<template>
  <el-dialog
    title="快速导入商品"
    :show-close="false"
    :visible.sync="visible"
    :destroy-on-close="true"
    :before-close="handleClose"
    width="600px"
  >
    <div class="batch-import">
      <div>
        <p>请在输入框内输入{{placeholder('trim')}}，用空格隔开。一行对应一条单号，请注意换行。</p>
        <span class="strong">支持直接在Excel中复制粘贴至输入框内</span>，快速导入，最多不超过{{maxLine}}行。
      </div>
      <div class="batch-import-input-row">
        <el-input
          :autosize="{ minRows: 4, maxRows: 6}"
          class="batch-import-input"
          type="textarea"
          :placeholder="placeholder()"
          v-model="inputText"
          resize="none"
          clearable
        >
        </el-input>
      </div>
      <div class="batch-import-btn-group">
        <el-button class="batch-import-btn" type="primary" @click="handleImport">确认导入</el-button>
        <el-button class="batch-import-btn" @click="handleCancel">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>

export default {
  props: ['visible', 'orderType'],
  data() {
    return {
      maxLine: 500,
      inputText: ''
    }
  },
  methods: {
    handleImport () {
      if (this.inputText) {
        const batchImportItemList = []
        let rows = this.inputText.split('\n')
        if (rows && rows.length > this.maxLine) {
          rows = rows.slice(0, this.maxLine)
          this.$message.error(`本次输入行数大于${this.maxLine}行，超出部分已自动截断！`)
        }
        if (rows && rows.length > 0) {
          rows.filter(row => row).map(row => row.trim()).forEach(row => {
            const strList = row.split(/\s+|,|，/).map(str => str.trim()).filter(row => row)
            if (!strList.length) return

            const rowData = { skuNo: strList[0] }
            rowData['factoryCode'] = strList[1] || ''
            if (this.orderType !== '32') {
              rowData['warehouseLocation'] = strList[2] || ''
            }
            switch (this.orderType) {
              case '23':
                rowData['quantity'] = strList[3] || ''
                rowData['costCenter'] = strList[4] || ''
                break;
              case '18':
                rowData['quantity'] = strList[3] || ''
                rowData['generalLedgerAccount'] = strList[4] || ''
                rowData['costCenter'] = strList[5] || ''
                break;
              case '13':
                rowData['receiveWarehouseLocation'] = strList[3] || ''
                rowData['quantity'] = strList[4] || ''
                break;
              case '27':
                rowData['quantity'] = strList[3] || ''
                rowData['receiveSkuNo'] = strList[4] || ''
                break;
              case '32':
                rowData['customerCode'] = strList[2] || ''
                rowData['quantity'] = strList[3] || ''
                break;
            }
            rowData.quantity = parseFloat(rowData.quantity)
            batchImportItemList.push(rowData)
          })
          console.log(JSON.stringify(batchImportItemList))
          setTimeout(() => {
            this.$emit('import', batchImportItemList)
            this.handleClose()
          }, 200)
        }
      }
    },
    handleCancel () {
      this.handleClose()
    },
    handleClose (done) {
      done && done()
      this.$emit('update:visible', false)
      this.inputText = ''
    },
    placeholder (trim) {
      // ['23', '18', '13', '27']
      const { orderType } = this
      let ret = ''
      if (orderType === '23') {
        ret = '例如：\nSKU编码 工厂代码 仓库地点代码 数量 成本中心代码'
        if (!trim) ret += '\nAA0001 1000 1201 10 D001'
      }
      if (orderType === '18') {
        ret = '例如：\nSKU编码 工厂代码 仓库地点代码 数量 总账科目代码 成本中心代码'
        if (!trim) ret += '\nAA0001 1000 1201 10 0066010705 D001'
      }
      if (orderType === '13') {
        ret = '例如：\nSKU编码 工厂代码 仓库地点代码 接收仓库地点代码 数量'
        if (!trim) ret += '\nAA0001 1000 9201 9219 10'
      }
      if (orderType === '27') {
        ret = '例如：\nSKU编码 工厂代码 仓库地点代码 数量 接收SKU编码'
        if (!trim) ret += '\nAA0001 1000 9219 10 BB0001'
      }
      if (orderType === '32') {
        ret = '例如：\nSKU编码 工厂代码 寄售客户 数量'
        if (!trim) ret += '\nAA0001 1000 A99996 10'
      }
      if (trim) {
        ret = ret.replace('例如：', '')
      }
      return ret
    }
  }
}
</script>

<style lang="scss">
.batch-import-btn-group{
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style>
