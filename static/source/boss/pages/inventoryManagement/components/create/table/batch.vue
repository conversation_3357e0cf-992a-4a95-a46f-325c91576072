<template>
  <el-dialog
    title="选择批次"
    :visible.sync="batchVisible"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :before-close="_=>updateBatchVisible(false)"
    width="760px"
  >
    <div class="dialog-body" v-loading="batchLoading">
      <el-row style="margin: 10px;">
        <el-col :span="8">
          <span>工厂：</span>
          <span>{{batchData.factoryCode}}</span>
        </el-col>
        <el-col :span="8">
          <span>SKU：</span>
          <span>{{batchData.skuNo}}</span>
        </el-col>
        <el-col v-if="orderType !== '32'" :span="8">
          <span>库存地点：</span>
          <span>{{batchData.warehouseLocation}}</span>
        </el-col>
        <el-col v-if="orderType === '32'" :span="8">
          <span>寄售客户：</span>
          <span>{{batchData.customerCode + ' ' + batchData.customerName}}</span>
        </el-col>
        <el-col :span="8">
          <span>在库可用数量：</span>
          <span>{{totalQuantity}}</span>
        </el-col>
      </el-row>
      <el-table :data="batchData.inventoryList || []">
        <el-table-column label="选中" width="60">
          <template slot-scope="{ row }">
            <el-radio class="radio" v-model="batchData.batchNo" :label="row.firstBatch"><span></span></el-radio>
          </template>
        </el-table-column>
        <el-table-column label="批次" prop="firstBatch" width="120"></el-table-column>
        <el-table-column label="库存类型" prop="inventoryType" width="140"></el-table-column>
        <el-table-column label="批次在库库存" prop="supplierConsignmentQty" width="120"></el-table-column>
        <el-table-column label="单位" prop="unit"></el-table-column>
        <el-table-column label="入库日期" prop="warehousingTime" width="120"></el-table-column>
        <el-table-column label="生产日期" prop="produceTime" width="120"></el-table-column>
        <el-table-column label="失效日期" prop="invalidTime" width="120"></el-table-column>
        <el-table-column label="供应商批次" prop="supplierBatch"></el-table-column>
      </el-table>
    </div>
    <p slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitBatch">确定</el-button>
      <el-button @click="_=>updateBatchVisible(false)">取消</el-button>
    </p>
  </el-dialog>
</template>
<script>
export default {
  name: 'batch',
  props: [ 'updateBatchVisible', 'updateBatchNo', 'batchVisible', 'batchData', 'batchLoading' ],
  computed: {
    totalQuantity () {
      const { inventoryList } = this.batchData
      if (Array.isArray(inventoryList)) {
        const ret = inventoryList.reduce((init, a) => init + parseInt(a.supplierConsignmentQty), 0)
        return isNaN(parseInt(ret)) ? '' : parseInt(ret)
      } else {
        return ''
      }
    },
    orderType () {
      return this.$route.params.id
    }
  },
  methods: {
    submitBatch () {
      let data = { ...this.batchData };
      const selectedRow = this.batchData.inventoryList.find(item => item.firstBatch === this.batchData.batchNo)
      if (selectedRow) {
        data = {
          ...this.batchData,
          batchQuantity: selectedRow.supplierConsignmentQty
        }
      }
      this.updateBatchNo(data, this.batchData.itemNo)
      this.updateBatchVisible(false)
    }
  }
}
</script>
