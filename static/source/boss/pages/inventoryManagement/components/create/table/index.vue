<template>
  <div class='create-table'>
    <vxe-table
      border
      highlight-hover-row
      ref="detailTable"
      show-overflow
      highlight-current-row
      height="400"
      align="center"
      size="small"
      :scroll-y="{gt: 0}"
      :data="itemList"
      :loading="tableLoading"
      :auto-resize="true"
      :checkbox-config="{checkMethod: checCheckbox}"
      @checkbox-change="onTableSelectionChange"
      @checkbox-all="onTableSelectionAllChange"
    >
      <vxe-table-column class-name="checkbox-column" type="checkbox" title="" align="center"></vxe-table-column>
      <vxe-table-column
        v-for="col in getColumns"
        align="center"
        :width="col.width || 120"
        :key="col.name"
        :field="col.name"
        :title="col.name"
      >
        <template v-slot:header>
          <span v-bind:class="{required: col.required===true}" >{{col.name}}</span>
        </template>
        <template slot-scope="{row,rowIndex}">
          <span v-if="col.prop === 'itemNo'">{{renderItemNo(row, rowIndex)}}</span>
          <span v-else-if="col.prop === 'isDeleted'">
            {{row.isDeleted ? '已删除' : ''}}
          </span>
          <SelectSku
            v-else-if="col.prop==='skuNo' || col.prop==='receiveSkuNo' && col.type === 'custom'"
            :data.sync="row[col.prop]"
            :extraQuery="extraQuery(col.prop)"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)"
            @change="val=>handleChange(col.prop, val, row)"
          />
          <RemoteCustomer
            v-else-if="col.type === 'custom' && col.prop === 'customerCode'"
            v-model="row[col.prop]"
            ref="customer"
            :initCustomer="row.initCustomer"
            :style="{ width: '100%' }"
            @change="(val, item) => handleCustomerChange(val, item, row)"
          />
          <el-select
            v-else-if="col.prop==='costCenter'"
            v-model="row[col.prop]"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)"
            filterable
            default-first-option
            clearable
            size="mini"
          >
            <el-option
              v-for="item in costCenterList"
              :key="item.costCenter"
              :label="item.costCenter+' '+item.description"
              :value="item.costCenter">
            </el-option>
          </el-select>
          <el-select
            v-else-if="col.prop==='generalLedgerAccount'"
            v-model="row[col.prop]"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)"
            filterable
            default-first-option
            clearable
            size="mini"
          >
            <el-option
              v-for="item in buildOptions(col.prop, row)"
              :key="item.value"
              :label="item.value+' '+item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input-number
            v-else-if="col.type==='input-number'"
            v-model="row[col.prop]"
            size="mini"
            :placeholder="col.name"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)"
            :precision="3"
            :min="0"
            @change="val=>handleChange(col.prop, val, row)"
          />
          <el-input
            v-else-if="col.type==='input'"
            v-model="row[col.prop]"
            size="mini"
            :placeholder="col.name"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row) || disabledByOA(col.prop)"
            @change="val=>handleChange(col.prop, val, row)"
          />
          <el-input
            v-else-if="col.type==='textarea'"
            v-model="row[col.prop]"
            type="textarea"
            size="mini"
            :placeholder="col.name"
            :maxlength="200"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)"
          />
          <el-checkbox
            v-else-if="col.type==='checkbox'"
            v-model="row[col.prop]"
            true-label="1"
            false-label="0"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)"
            @change="val=>handleChange(col.prop, val, row)"
          />
          <el-date-picker
            v-else-if="col.type==='date-picker'"
            v-model="row[col.prop]"
            clearable
            size="mini"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期"
            style="width:100%"
            :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)"
            @change="val=>handleChange(col.prop, val, row)"
          />
          <template v-else-if="col.type==='select'">
            <span v-if="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row)">
              {{readNameFromDic(formatProp(col.prop), row[col.prop])}}
            </span>
            <el-select
              v-else
              v-model="row[col.prop]"
              default-first-option
              filterable
              remote
              clearable
              size="mini"
              :disabled="Boolean(col.disabled) || disabledBySupplier || disabledByProp(col.prop, row) || disabledByOA(col.prop)"
              @change="val=>handleChange(col.prop, val, row)"
              :remote-method="val=>remoteMethod(val, col.prop, row)"
            >
              <el-option
                v-for="item in buildOptions(col.prop, row)"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </template>
          <span v-else-if="col.prop ==='batchNo'">
            <el-input
              v-if="createData.orderType === '35'"
              v-model="row[col.prop]"
              :disabled="!canChooseBatch(row)"
              placeholder="请输入批次号"
              @change="updateBatchNo(row)"
              style="width: 80%;"
            />
            <span v-else v-html="batchNoLine(row[col.prop])"></span>
            <el-button type="text" @click="showBatch(row)" :disabled="!canChooseBatch(row)">
              查看
            </el-button>
          </span>
          <span v-else>{{findUnitName(col.prop, row[col.prop])}}</span>
        </template>
      </vxe-table-column>
    </vxe-table>
    <Batch
      :batchVisible="batchVisible"
      :batchLoading="batchLoading"
      :updateBatchVisible="updateBatchVisible"
      :updateBatchNo="updateBatchNo"
      :batchData="batchData"
    />
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import SelectSku from '@/pages/orderPurchase/components/common/SelectSku'
import padStart from 'lodash/padStart'
import Batch from './batch'
import { readNameFromDic, buildOptions, sortColumn } from '@/utils/mm'
import { mapState } from 'vuex'
import RemoteCustomer from '@/components/SearchFields/customer';

export default {
  name: 'create-table',
  data () {
    return {}
  },
  props: [
    'finalFields', 'createData', 'isEdit', 'itemList', 'batchVisible', 'batchLoading', 'updateBatchNo',
    'tableLoading', 'updateBatchVisible', 'batchData', 'columnSetting', 'disabledBySupplier',
    'khWarehouseList', 'isKunhe', 'canChooseBatch', 'costCenterList', 'filterByProp'
  ],
  components: {
    Batch, SelectSku, RemoteCustomer
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      warehouseList: state => state.orderPurchase.warehouseList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(data => data.field === 'table' && data.isCreate === 1)
        ret = sortColumn(ret, this.columnSetting)
      })
      return ret
    },
    disableModify () {
      const { transportMessage, orderStatus } = this.createData
      console.log('transportMessage, orderStatus')
      console.log(transportMessage, orderStatus)
      // eslint-disable-next-line eqeqeq
      if (transportMessage == '1' || (orderStatus != 1 && orderStatus != 7)) {
        return true
      }
      return false
    }
  },
  methods: {
    readNameFromDic,
    padStart,
    disabledByOA (prop) {
      const propList = ['oaNo', 'oaItemNo', 'oaType']
      if (propList.find(item => item === prop) && this.isEdit && this.disableModify) {
        return true
      }
      return false
    },
    getOptions (val) {
      const options = this.warehouseList.filter(warehouse => {
        const { warehouseLocationCode, warehouseLocationName } = warehouse
        // eslint-disable-next-line eqeqeq
        return (warehouseLocationCode && warehouseLocationCode.indexOf(val) != -1) || (warehouseLocationName && warehouseLocationName.indexOf(val) != -1)
      }).map(item => ({
        name: item.warehouseLocationName,
        value: item.warehouseLocationCode
      })).filter(this.filterByProp('value'))
      return options
    },
    remoteMethod (val, prop, row) {
      console.log(val, prop, row, row[`${prop}Options`])
      const options = this.getOptions(val).slice(0, 20)
      setTimeout(() => {
        row[`${prop}Options`] = options || []
      }, 300)
    },
    formatProp (prop) {
      const propsMap = {
        generalLedgerAccount: 'iaoGeneralLedger',
        receiveWarehouseLocation: 'warehouseLocation',
        receiveFactoryCode: 'factoryCode'
      }
      return propsMap[prop] || prop
    },
    extraQuery(prop) {
      if (prop === 'skuNo' && this.createData.orderType === '27') {
        return { containsDisable: 1 }
      }
    },
    findUnitName (prop, value) {
      const propList = [ 'inventoryUnit', 'unit', 'inventoryId' ]
      if (!propList.includes(prop)) return value
      const unitList = this.dictList['orderUnit']
      if (Array.isArray(unitList) && unitList.length) {
        const item = unitList.find(item => item.value === value)
        if (item && item.name) {
          value = item.name
        }
      }
      return value
    },
    disabledByPreserve(prop, row) {
      const { orderType } = this.createData
      const editDisableList = ['18', '13', '29', '31']
      const propList = ['skuNo', 'factoryCode', 'generalLedgerAccount', 'costCenter']
      if (this.isEdit && editDisableList.includes(orderType) && propList.includes(prop)) {
        return true
      }
    },
    disabledByKhWarehouse (row) {
      const isKunhe = this.isKunhe(row.warehouseLocation)
      console.log(isKunhe, row.materialGroupNum)
      if (isKunhe && row.materialGroupNum === 430) {
        return false
      }
      return true
    },
    disabledByProp (prop, row) {
      if (row.isDeleted) {
        return true
      }
      if (prop === 'skuNo') {
        if (row.referNo) {
          return true
        }
      }
      if (prop === 'costCenter') {
        if (!row.factoryCode) {
          return true
        }
      }
      if (this.disabledByPreserve(prop, row)) {
        return true
      }
    },
    showBatch(row) {
      const { orderType } = this.createData
      if (!row.skuNo) return this.$message.error('请输入SKU！')
      if (!row.factoryCode) return this.$message.error('请输入工厂！')
      if ((orderType !== '21' && orderType !== '32') && !row.warehouseLocation) return this.$message.error('请选择仓库地点！')
      if (orderType === '32' && !row.customerCode) return this.$message.error('请选择寄售客户！')
      this.updateBatchVisible(true, row)
    },
    renderItemNo(row, index) {
      if (!row.itemNo || !this.isEdit) {
        row.itemNo = padStart(index + 1, 4, '0')
      }
      // if (row) {
      //   return padStart(index + 1, 4, '0')
      // }
      return row.itemNo
    },
    batchNoLine (nos) {
      if (!nos) return ''
      let ret = nos.split(/\s+/).filter(e => e).join('/') + ' '
      return ret
    },
    handleChangeAdapter(prop, val, row) {
      let adapter = this[`${prop}Adapter`]
      if (!adapter) {
        adapter = this.defaultAdapter
      }
      adapter(val, row)
    },
    defaultAdapter (val, row) {
      console.log(val, row)
    },
    factoryCodeAdapter (val, row) {
      console.log('factoryCodeAdapter', val, row)
      this.$emit('updateFactoryCode', row)
    },
    quantityAdapter (val, row) {
      console.log(val, row)
    },
    warehouseLocationAdapter (val, row) {
      console.log(val, row)
      this.$emit('updateWarehouseLocation', row)
    },
    skuNoAdapter (val, row) {
      if (val) {
        this.$emit('addItem', val, row)
      }
    },
    handleChange (type, val, row) {
      this.handleChangeAdapter(type, val, row)
    },
    handleCustomerChange (value, item, row) {
      if (item) {
        row.customerName = item.label
        row.initCustomer = item
        // this.$emit('updateCustomer', value, item, row)
      }
    },
    handleItemChange (value, prop) {
      console.log(value, prop)
    },
    buildOptions (prop, row) {
      if (prop === 'generalLedgerAccount') prop = 'iaoGeneralLedger'
      // if (prop === 'receiveWarehouseLocation') prop = 'warehouseLocation'
      if (prop === 'factoryCode' || prop === 'receiveFactoryCode') prop = 'factoryCode2'
      if (/warehouseLocation/gi.test(prop)) {
        // options数量过大, 采用本地mock模糊搜索方法
        return row[`${prop}Options`] || []
      }
      return buildOptions(prop)
    },
    checCheckbox ({ row }) {
      return !row.isEmptyLine
    },
    onTableSelectionChange ({ records }) {
      this.$emit('selectionChange', records)
    },
    onTableSelectionAllChange ({ records }) {
      this.$emit('selectionChange', records)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .checkbox-column .vxe-cell.c--tooltip{
  padding: 2px 0px;
  margin-left: -8px;
}
</style>
