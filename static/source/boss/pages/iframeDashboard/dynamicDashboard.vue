<template>
  <div class="page page-home page-container">
    <Loading v-if="!loaded" absolute />
    <iframe ref="iframe" :height="height"
      width="100%" scrolling="auto" @load="onload"
      :src="url" frameborder="0">
    </iframe>
  </div>
</template>
<script>
import Loading from '@/components/loading'
import { setEnv } from './utils'
export default {
  name: 'DynamicDashboard',
  components: {
    Loading
  },
  data () {
    return {
      loaded: false,
      url: '',
      timer: null,
      timer2: null,
      height: '1300px'
    }
  },
  created () {
    this.setSrc()
  },
  watch: {
    '$route.path': function(nv, ov) {
      this.setSrc()
    }
  },
  methods: {
    onload () { this.loaded = true },
    setSrc () {
      this.url = ''
      clearTimeout(this.timer)
      clearTimeout(this.timer2)
      this.timer = setTimeout(() => {
        this.loaded = false
        setEnv(this)
        this.url = this.$route.meta.url
        try {
          const match = /height=(\d+)/.exec(this.url)
          if (match && match[1]) {
            this.height = match[1] + 'px'
            console.log(`set height from child: ${this.height}`)
          }
        } catch (err) {}
      }, 350)
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container{
  padding: 0px;
  overflow: hidden;
  position: relative;
}
</style>
