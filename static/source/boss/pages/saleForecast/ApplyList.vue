<template>
  <div class="app-container salesForecast-applyList">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
        :show-message="false"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="预报申请单号" prop="reportNo">
              <el-input
                v-model.trim="searchForm.reportNo"
                placeholder="请输入预报申请单号"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" >
            <el-form-item label="客户" prop="customerSet">
              <CustomerSelect v-model="searchForm.customerSet" multiple :multipleLimit="20" placeholder="请输入客户名称或编号" style="width: 100%"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库位" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                clearable
                placeholder="请选择库存地点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in positions"
                  :key="item.position"
                  :value="item.position"
                  :label="`${item.position} ${item.positionName}`"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item.brandId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item class="search-row" label="SKU" prop="skuNo">
              <el-input
                v-model="searchForm.skuNo"
                placeholder="请输入SKU，以空格分隔"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组" prop="materialGroupId">
              <el-select
                v-model="searchForm.materialGroupId"
                filterable
                clearable
                multiple
                placeholder="请输入物料组"
                style="width: 100%"
                remote
                reserve-keyword
                :remote-method="remoteMaterialGroupIdMethod"
                :loading="materialGroupIdLoading"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工厂" prop="factory">
              <el-select
                v-model="searchForm.factory"
                filterable
                clearable
                placeholder="请输入关键词"
                style="width: 100%"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.factory"
                  :label="`${item.factory} ${item.factoryName}`"
                  :value="item.factory"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态" prop="statusSet">
              <el-select
                v-model="searchForm.statusSet"
                filterable
                clearable
                multiple
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option
                  v-for="item in applyStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" >
            <el-form-item label="申请人" prop="submitterName">
              <el-input
                v-model.trim="searchForm.submitterName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" >
            <el-form-item label="销售" prop="salesName">
              <el-input
                v-model.trim="searchForm.salesName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" >
            <el-form-item label="采购" prop="purchaseName">
              <el-input
                v-model.trim="searchForm.purchaseName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" >
            <el-form-item label="审核人" prop="auditorName">
              <el-input
                v-model.trim="searchForm.auditorName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="预报月份" prop="startDate">
              <el-date-picker
                clearable
                v-model="searchForm.startDate"
                type="month"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="请选择起始月份"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="" prop="endDate">
              <span slot="label" style="text-align: center; display: inline-block; width: 100px">~</span>
              <el-date-picker
                clearable
                v-model="searchForm.endDate"
                type="month"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="请选择结束月份"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="loading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="flex-between">
      <div style="color: red">未参考预报数会在当月最后一天晚八点自动清零</div>
      <div>
        <el-button v-if="role1" type="primary" @click="handleProcessList">个人任务处理进度</el-button>
        <el-button v-if="role1" type="primary" @click="toDel(0, 'edit')">预报申请</el-button>
        <el-button
          v-if="role1 || role2"
          :loading="loading"
          type="primary"
          @click="handleExport"
          >批量导出</el-button
        >
        <el-dropdown style="margin-left: 10px;margin-right: 10px;"  v-if="role1">
          <el-button type="primary" :loading="loading">批量导入<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-upload
                action="/api-ab/sales-report/importExcel"
                :show-file-list="false"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :before-upload="beforeUpload"
                accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                style="display: inline-block; margin-left: 10px"
                name="excelFile"
              >
                <el-button
                  type="text"
                  style="margin-right: 5px"
                  :loading="loading"
                  >导入</el-button
                >
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button type="text" :loading="loading" @click="handleDownloadTemplate">
                下载模版
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button v-if="role1" type="primary" :loading="loading" @click="batchSubmit()">批量提交</el-button>
        <el-button v-if="role2" type="primary" :loading="loading" @click="batchApprove()">批量通过</el-button>
        <el-button v-if="role2" type="primary" :loading="loading" @click="rejectShow()">批量驳回</el-button>
        <el-dropdown style="margin-left: 10px;"  v-if="role2">
          <el-button type="primary" :loading="loading">批量审核<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-button type="text"  @click="handleBatchCheck">
                批量审核导出
              </el-button>
            </el-dropdown-item>
            <el-dropdown-item>
               <el-upload
                action="/api-ab/sales-report/importBatchCheck"
                :show-file-list="false"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :before-upload="beforeUpload"
                accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                name="excelFile"
              >
                <el-button type="text" :loading="loading">批量审核导入</el-button>
              </el-upload>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <div class="search-result-container">
      <el-table
        border
        :data="basisList"
        :max-height="screenHeight - 200"
        style="width: 100%; margin-top: 10px"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column :selectable="(row) => row.status !== 2" align="center" type="selection" width="50" fixed="left"/>
        <el-table-column align="left" prop="risk" label="预报风险" width="180" fixed="left">
          <template slot-scope="{row}">
            <el-tooltip v-for="item in (row.risk || [])" :key="item.key" :content="item.tooltip">
              <el-tag :type="item.level === 'hight' ? 'danger' : 'warning'" style="margin-right: 10px">{{ item.text }}</el-tag>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="errorMsg" label="审核异常" width="180" fixed="left" show-overflow-tooltip></el-table-column>
        <el-table-column align="left" prop="skuNo" label="SKU" width="120" fixed="left" show-overflow-tooltip></el-table-column>
        <el-table-column align="left" prop="reportNo" label="预报申请单号" width="150">
          <template slot-scope="{row}">
            <el-link type="primary" @click="toDel(row.reportNo, 'detail')">{{ row.reportNo }}</el-link>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="customerName" label="客户" width="180" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.customerCode || ''} ${row.customerName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="salesProfileDesc" label="销售范围" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column align="left" prop="factory" label="工厂" width="160" show-overflow-tooltip>
          <template slot-scope="{row}">{{ `${row.factory || ''} ${row.factoryName || ''}` }}</template>
        </el-table-column>
        <el-table-column align="left" prop="positionName" label="库位" width="160" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.position || ''} ${row.positionName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="skuDesc" label="物料描述" width="180" show-overflow-tooltip />
        <el-table-column align="left" prop="skuTagsDesc" label="商品标签" width="160" show-overflow-tooltip />
        <el-table-column align="left" prop="brandName" label="品牌" width="120" show-overflow-tooltip />
        <el-table-column align="left" prop="materialGroupName" label="物料组" width="120" show-overflow-tooltip />
        <el-table-column align="right" prop="safeStockQty" label="安全库存" width="120">
          <template slot-scope="{row}">{{ numFormat(row.safeStockQty )}}</template>
        </el-table-column>
        <el-table-column align="right" prop="sysSuggestQty" label="系统建议量" width="120">
          <template slot-scope="{row}">{{ numFormat(row.sysSuggestQty )}}</template>
        </el-table-column>
        <el-table-column align="right" prop="reportQty" label="最新预报数量" width="120">
          <template slot-scope="{row}">{{ numFormat(row.reportQty )}}</template>
        </el-table-column>
        <el-table-column align="left" prop="reportReason" label="预报说明" width="180" show-overflow-tooltip />
        <el-table-column align="left" prop="batchDate" label="预报月份" width="120" show-overflow-tooltip />
        <el-table-column align="right" prop="forecastRate" label="近三个月预报准确度" width="130">
          <template slot-scope="{row}">{{ row.forecastRate ? row.forecastRate + '%' : row.forecastRate}}</template>
        </el-table-column>
        <el-table-column align="left" prop="lt" label="LT" width="120" show-overflow-tooltip />
        <el-table-column align="right" prop="oldReportQty" label="原预报数量" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">{{ numFormat(row.oldReportQty )}}</template>
        </el-table-column>
         <el-table-column align="right" prop="refQty" label="已参考订单数量" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">{{ numFormat(row.refQty )}}</template>
        </el-table-column>
        <el-table-column align="right" prop="unRefQty" label="未参考预报数量" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">{{ numFormat(row.unRefQty )}}</template>
        </el-table-column>
        <el-table-column align="right" prop="qty" label="调整量" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">
            <i v-if="row.adjustQty > 0" class="el-icon-top" style="color: #67C23A"></i>
            <i v-if="row.adjustQty < 0" class="el-icon-bottom" style="color: #F56C6C"></i>
            <span>{{ [null, undefined].includes(row.adjustQty) ? "--" : numFormatThousand(Math.abs(row.adjustQty)) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="soQty" label="实际订单量" width="120" show-overflow-tooltip>
          <template slot-scope="{row}">{{ numFormat(row.soQty )}}</template>
        </el-table-column>
        <el-table-column align="center" label="实际订单量（历史）/ 实际交货量（历史）" width="400" show-overflow-tooltip>
          <el-table-column v-for="(item, key) in times" :key="item" align="center" :label="item" width="100" show-overflow-tooltip>
            <template slot-scope="{row}">{{ row.timesData[key] }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" prop="bigRocks" label="是否大石头客户" width="110">
          <template slot-scope="{row}">{{ row.bigRocks === 1 ? '是' : '否'}}</template>
        </el-table-column>
        <el-table-column align="left" prop="bigRocksRate" label="大石头客户销售占比" width="130" show-overflow-tooltip>
          <template slot-scope="{row}">{{ row.bigRocksRate ? row.bigRocksRate + '%' : row.bigRocksRate}}</template>
        </el-table-column>
        <el-table-column align="center" prop="productPositioningName" label="商品定位" width="120" show-overflow-tooltip />
        <el-table-column align="center" prop="salesName" label="销售" width="100" show-overflow-tooltip />
        <el-table-column align="center" prop="purchaseName" label="采购" width="100" show-overflow-tooltip />
        <el-table-column align="center" prop="approveName" label="审核人" width="100" show-overflow-tooltip />
        <el-table-column align="center" prop="submitterName" label="申请人" width="100" show-overflow-tooltip />
        <el-table-column align="center" prop="notes" label="驳回原因" width="180" show-overflow-tooltip />
        <el-table-column align="center" prop="statusName" label="状态" width="80" show-overflow-tooltip />
        <el-table-column align="left" prop="action" label="操作" width="140" fixed="right">
          <template slot-scope="{row}">
            <div class="btns">
              <el-link
                v-if="role2 && row.status === 1"
                type="primary"
                style="margin-right: 10px;"
                @click="batchApprove(row)"
              >通过</el-link>
              <el-link
                v-if="role2 && row.status === 1"
                type="primary"
                style="margin-right: 10px;"
                @click="rejectShow(row)"
              >驳回</el-link>
              <el-link
                v-if="role1 && [0, -1].includes(row.status)"
                type="primary"
                style="margin-right: 10px;"
                @click="toDel(row.reportNo, 'edit')"
              >编辑</el-link>
              <el-link
                v-if="role1 && [0, -1].includes(row.status)"
                type="primary"
                style="margin-right: 10px;"
                @click="batchSubmit(row)"
              >提交</el-link>
              <el-link
                v-if="role1 && [0, -1].includes(row.status)"
                type="primary"
                style="margin-right: 10px;"
                @click="batchDelete(row)"
              >删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
     <el-dialog
      width="600px"
      title="驳回"
      :visible.sync="rejectVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div style="display: flex; align-items: center; margin-bottom: 10px;">
        <span>驳回原因：</span>
        <el-input
          type="textarea"
          v-model="rejectData.notes"
          style="flex: 1;"
          :rows="4"
        ></el-input>
      </div>
      <div style="text-align: right">
        <el-button type="default" @click="rejectVisible = false">取消</el-button>
        <el-button type="primary" @click="batchReject" :loading="loading">确认</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="800px"
      title="个人任务处理进度"
      :visible.sync="processListVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-table
        border
        :data="processList"
        :max-height="500"
        style="width: 100%; margin-top: 10px"
        v-loading="processListLoading"
      >
        <el-table-column prop="taskTime" label="时间" align="center" width="120" how-overflow-tooltip></el-table-column>
        <el-table-column prop="operator" label="操作人" align="center" width="120"></el-table-column>
        <el-table-column prop="taskType" label="操作记录" align="center" width="140" how-overflow-tooltip></el-table-column>
        <el-table-column prop="fileName" label="表格名称" align="center" width="120" how-overflow-tooltip></el-table-column>
        <el-table-column prop="taskStatus" label="当前处理状态" align="center" width="160" how-overflow-tooltip></el-table-column>
        <el-table-column prop="url" label="报错数据导出" align="center" width="120">
          <template slot-scope="scope">
            <a v-if="scope.row.url" :href="scope.row.url" target="_blank">下载</a>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { numFormatThousand } from '@/utils/index';
import { applyStatus } from './option'
import { mapState } from 'vuex';
import moment from 'moment';
import CustomerSelect from '@/components/SearchFields/client'
import {
  getMaterialGroupListApi,
  getBrandListApi,
  getAllPosition,
  getAllFactory
} from '@/api/purchasePlan';
import {
  getSalesApplyList,
  approveSalesApply,
  rejectSalesApply,
  exportSalesApply,
  deleteSalesApply,
  submitSalesApply,
  exportBatchCheck,
  downloadTemplate,
  getProcessList
} from '@/api/saleForecast';

export default {
  name: 'applyList',
  components: { CustomerSelect },
  data() {
    return {
      numFormatThousand,
      applyStatus,
      brandIdOptions: [],
      brandIdLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      factoryList: [],
      positions: [],
      loading: false,
      basisList: [],
      searchForm: {
        reportNo: '',
        customerSet: [],
        position: null,
        brandNo: '',
        skuNo: '',
        materialGroupId: [],
        factory: '',
        statusSet: [],
        submitterName: '',
        salesName: '',
        purchaseName: '',
        auditorName: '',
        startDate: moment().format('YYYY-MM'),
        endDate: ''
      },
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      screenHeight: document.body.clientHeight,
      selectedList: [],
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(this.searchForm.startDate)).getTime()
        }
      },
      rejectVisible: false,
      rejectData: { idList: [], notes: '' },
      times: [],
      processListVisible: false,
      processList: [],
      processListLoading: false
    };
  },
  computed: {
    ...mapState(['userRole']),
    role1() {
      return !!~this.userRole.indexOf('data-销售');
    },
    role2() {
      return !!~this.userRole.indexOf('data-预报审核');
    }
  },
  created() {
    getAllPosition().then((res) => {
      if (res.status === 200) {
        this.positions = res.result;
      }
    })
    getAllFactory().then((res) => {
      if (res.status === 200) {
        this.factoryList = res.result;
      }
    })
    this.handleFilter();
    this.times = this.getLast4Months()
  },
  activated () {
    const needReload = sessionStorage.getItem('applyListReload')
    if (needReload) {
      sessionStorage.removeItem('applyListReload')
      this.handleFilter()
    }
  },
  methods: {
    handleProcessList() {
      this.processListLoading = true;
      this.processListVisible = true;
      getProcessList()
        .then((res) => {
          this.processListLoading = false;
          if (res.code === 200) {
            this.processList = res.data;
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.processListLoading = false;
          this.$message.error({ message: '请求失败！' });
        });
    },
    numFormat(num) {
      return typeof num === 'number' ? numFormatThousand(num) : num;
    },
    numFormatWithDefault(num) {
      return typeof num === 'number' ? numFormatThousand(num) : '--';
    },
    // 获取前4个月数据
    getLast4Months() {
      let monthsData = []
      for (var i = 1; i <= 4; i++) {
        monthsData.push(moment().subtract(i, 'months').format('YYYY-MM'))
      }
      return monthsData.reverse();
    },
    // 导入模版
    handleDownloadTemplate() {
      this.loading = true;
      downloadTemplate()
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.loading = false;
          this.$message.error({ message: '下载失败！' });
        });
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
      this.$refs.multipleTable && this.$refs.multipleTable.clearSelection();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.searchForm = {
        reportNo: '',
        customerSet: [],
        position: null,
        brandNo: '',
        skuNo: '',
        materialGroupId: [],
        factory: '',
        statusSet: [],
        submitterName: '',
        salesName: '',
        purchaseName: '',
        auditorName: '',
        startDate: '',
        endDate: ''
      }
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'
        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      } else {
        this.materialGroupOptions = [];
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    getList() {
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      param.skuNo = param.skuNo ? param.skuNo.split(' ').filter(item => !!item) : [];
      this.loading = true;
      getSalesApplyList(param)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.basisList = (res.data || []).map((a) => {
              return {
                ...a,
                adjustQty: (a.reportQty || 0) - (a.oldReportQty || 0),
                timesData: [
                  this.numFormatWithDefault(a.fourMonthAgoSoQty) + ' | ' + this.numFormatWithDefault(a.fourMonthAgoDnQty),
                  this.numFormatWithDefault(a.threeMonthAgoSoQty) + ' | ' + this.numFormatWithDefault(a.threeMonthAgoDnQty),
                  this.numFormatWithDefault(a.twoMonthAgoSoQty) + ' | ' + this.numFormatWithDefault(a.twoMonthAgoDnQty),
                  this.numFormatWithDefault(a.oneMonthAgoSoQty) + ' | ' + this.numFormatWithDefault(a.oneMonthAgoDnQty)
                ]
              }
            });
            this.total = res.total;
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    handleExport() {
      let param = { ...this.searchForm };
      param.skuNo = param.skuNo ? param.skuNo.split(' ').filter(item => !!item) : [];
      this.loading = true;
      exportSalesApply(param)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.loading = false;
          this.$message.error({ message: '导出失败！' });
        });
    },
    handleBatchCheck() {
      let param = { ...this.searchForm };
      param.skuNo = param.skuNo ? param.skuNo.split(' ').filter(item => !!item) : [];
      this.loading = true;
      exportBatchCheck(param)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.loading = false;
          this.$message.error({ message: '导出失败！' });
        });
    },
    toDel(id, type) {
      this.$router.push({
        path: `/saleForecast/apply/${type}/${id}`
      })
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.loading = true;
    },
    onUploadSuccess(response) {
      this.loading = false;
      if (response && response.code === 200) {
        this.$message.success(response.data || '导入成功！');
      } else {
        let msg = ((response && response.msg) || '导入失败！').replace(/\n/g, '<br/>')
        this.$message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          showClose: true,
          duration: 3000
        })
      }
    },
    onUploadError(error) {
      this.loading = false;
      let msg = ((error && error.msg) || (error && error.message) || '上传失败').replace(/\n/g, '<br/>')
        this.$message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          showClose: true,
          duration: 3000
        })
    },
     handleSelectionChange (val) {
      this.selectedList = val
    },
    async batchApprove(row) {
      let idList = row ? [row.reportId] : this.selectedList.map((a) => a.reportId)
      if (idList.length === 0) {
        this.$message.warning('请先选中数据！')
        return
      }
      this.loading = true;
      approveSalesApply({ idList })
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$message.success(res.data || '通过成功！');
            this.handleFilter()
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.loading = false;
          this.$message.error({ message: '操作失败！' });
        });
    },
    rejectShow(row) {
      let idList = row ? [row.reportId] : this.selectedList.map((a) => a.reportId)
      if (idList.length === 0) {
        this.$message.warning('请先选中数据！')
        return
      }
      this.rejectVisible = true
      this.rejectData = { idList, notes: '' }
    },
    async batchReject() {
      if (!this.rejectData.notes) {
        this.$message.warning('请填写驳回原因！')
        return
      }
      this.loading = true;
      rejectSalesApply(this.rejectData)
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.$message.success('驳回成功！');
            this.handleFilter()
            this.rejectVisible = false
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.loading = false;
          this.$message.error({ message: '操作失败！' });
        });
    },
    async batchDelete (row) {
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.loading = true;
        let res = await deleteSalesApply({ idList: [ row.reportId ] })
        this.loading = false;
        if (res.code === 200) {
          this.$message.success('删除成功！')
          this.handleFilter()
        } else {
          this.$message.error(res.msg)
        }
      }).catch(() => {})
    },
    async batchSubmit (row) {
      let idList = row ? [row.reportId] : this.selectedList.map((a) => a.reportId)
      if (idList.length === 0) {
        this.$message.warning('请先选中数据！')
        return
      }
      this.loading = true;
      let res = await submitSalesApply({ idList })
      this.loading = false;
      if (res.code === 200) {
        this.$message.success('提交成功！')
        this.handleFilter()
      } else {
        this.$message.error(res.msg)
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.salesForecast-applyList {
  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
  }

}
</style>
