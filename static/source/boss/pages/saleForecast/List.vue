<template>
  <div class="app-container page-sale-forecast-list" :class="{'isWindows': isWindows}" v-loading="loading.pageLoading">
    <!-- <div class="btn-tutorial">
      <i class="el-icon-video-play"></i>
      <a :href="tutorial_link" target="_blank">观看教程</a>
    </div> -->
    <div class="module-filter">
      <el-form :inline="true">
        <el-form-item label="客户名称">
          <el-input v-model.trim="filter.customerName" @change="afterChangeFilter"></el-input>
        </el-form-item>
        <el-form-item label="客户代码">
          <el-input v-model.trim="filter.customerCode" @change="afterChangeFilter"></el-input>
        </el-form-item>
        <el-form-item label="物料编号">
          <el-input v-model.trim="filter.productNo" @change="afterChangeFilter"></el-input>
        </el-form-item>
        <el-form-item label="审核标识">
          <el-select v-model="filter.auditMark" placeholder="请选择" @change="afterChangeFilter">
            <el-option v-for="item in option.auditOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filter.statusCode" placeholder="请选择" @change="afterChangeFilter">
            <el-option v-for="item in option.status" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- role access -->
        <el-form-item label="销售">
          <el-input v-model="filter.seller" @change="afterChangeFilter"></el-input>
        </el-form-item>
        <el-form-item label="客服">
          <el-input v-model="filter.customerService" @change="afterChangeFilter"></el-input>
        </el-form-item>
        <!-- <el-form-item label="工厂">
          <el-select v-model="filter.factory" filterable clearable placeholder="请选择" @change="afterChangeFilter">
            <el-option
              v-for="item in factoryList"
              :key="item.factory"
              :label="`${item.factory} ${item.factoryName}`"
              :value="item.factory"
            >
            </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="search" :loading="loading.searchLoading">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="module-tip">
      <p><strong>首次使用预报系统，请先查看使用教程。</strong><a target="_blank" href="https://short-phx.yunxuetang.cn/spktk7rv">立即查看&gt;&gt;</a></p>
      <p><strong>遇到报错和问题，请点击右侧文档自查。</strong><a target="_blank" href="https://doc.weixin.qq.com/doc/w3_AIwA4QYlADYOqLADwFeQgaCOqHAGs?scode=AAcAtAcPAAk0zkEA1YAdMAhAZxANk">销售预报Q&A&gt;&gt;</a></p>
    </div>

    <div class="module-list">
      <div class="module-btns between">
        <div class="btn-group">
          <!-- <el-button type="primary" @click="effectAll()" v-if="isServices">全部生效</el-button> -->
          <a v-if="isBuyer" class="el-button el-button--primary el-button--small" :href="`/data-center-front/sales/forecast/export/audit/mark?${filterQuerystring}`" target="_blank">导出</a>
          <a v-if="!isBuyer" class="el-button el-button--primary el-button--small" :href="`/data-center-front/sales/forecast/export?${filterQuerystring}`" target="_blank">导出</a>
          <span class="tip" v-if="tip && isSaler">{{ tip }}</span>
        </div>
      </div>

      <el-table :data="list" style="width: 100%" height="550" border v-loading="loading.list" @selection-change="handleSelectionChange">
        <el-table-column fixed type="selection" label="序号"></el-table-column>
        <el-table-column fixed prop="customerName" label="客户名称" width="150px"></el-table-column>
        <el-table-column fixed prop="customerCode" label="客户代码"></el-table-column>
        <el-table-column fixed prop="productNo" label="物料代号" width="100px">
          <template slot-scope="{ row }">
            <p>{{row.productNo}}</p>
            <el-tag
              class="mb-4 mr-4"
              v-for="item in (row.skuTags || [])"
              :key="item"
              type="danger"
              effect="dark">
              {{ item }}
            </el-tag>
            <el-tooltip
              v-for="item in (row.tags || [])"
              :key="item.key"
              :content="item.tooltip"
              placement="top"
            >
              <el-tag
                class="mb-4 mr-4"
                effect="dark"
                :type="item.level == 'high' ? 'danger' : 'warning'"
                >
                {{ item.text }}
              </el-tag>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip fixed prop="productDesc" label="物料描述" width="120px"></el-table-column>
        <el-table-column prop="statusCodeName" label="状态"></el-table-column>
        <el-table-column prop="batchDate" label="期间"></el-table-column>
        <el-table-column prop="createTypeName" label="是否新增"></el-table-column>
        <el-table-column prop="auditMarkName" label="审核标识"></el-table-column>
        <!-- <el-table-column prop="auditor" label="审核人"></el-table-column> -->
        <el-table-column prop="customerService" label="客服"></el-table-column>
        <el-table-column prop="seller" label="销售员"></el-table-column>

        <el-table-column prop="fastSlowMoving" label="快动"></el-table-column>
        <el-table-column prop="isConsignmentName" label="是否寄售"></el-table-column>
        <el-table-column prop="bigRocksRate" label="大石头比例" width="100px"></el-table-column>

        <el-table-column prop="productPosition" label="产品定位"></el-table-column>
        <el-table-column prop="ifWeb" label="是否上官网" width="100px"></el-table-column>
        <el-table-column prop="customerLevel" label="客户分级"></el-table-column>
        <el-table-column prop="salesOrg" label="销售组织" width="200px"></el-table-column>
        <el-table-column prop="distributionChannel" label="分销渠道"></el-table-column>
        <el-table-column prop="productGroup" label="产品组"></el-table-column>
        <el-table-column prop="productGroupName" label="物料组"></el-table-column>
        <el-table-column prop="purchaseGroup" label="采购组"></el-table-column>
        <el-table-column prop="unit" label="销售单位"></el-table-column>

        <el-table-column prop="evm" label="是否EVM寄售">
          <template slot-scope="scope">
            <el-select v-model="scope.row.evm" placeholder="请选择" @change="save(scope.row)" disabled>
              <el-option v-for="item in option.evmStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="businessType" label="生意类型"></el-table-column>
        <el-table-column prop="leadTime" label="leadtime"></el-table-column>
        <!-- <el-table-column prop="factory" label="工厂">
          <template slot-scope="scope">
            {{ `${scope.row.factory || ''} ${scope.row.factoryName || ''}` }}
          </template>
        </el-table-column> -->
        <el-table-column prop="stockLocationCode" label="备货仓位" width="220px">
          <template slot-scope="scope">
            <!-- <el-select v-model="scope.row.stockLocationCode" placeholder="请选择" @change="save(scope.row)" disabled filterable>
              <el-option v-for="item in option.stockLocationCode[scope.row.companyCode]" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select> -->
            {{ getStockLocationCode(scope.row) }}
          </template>
        </el-table-column>

        <el-table-column prop="lastFourMonthDeliveryQuantity" :label="`${getMonth(-4)}月已交货量`"></el-table-column>
        <el-table-column prop="lastThreeMonthDeliveryQuantity" :label="`${getMonth(-3)}月已交货量`"></el-table-column>
        <el-table-column prop="lastTwoMonthDeliveryQuantity" :label="`${getMonth(-2)}月已交货量`"></el-table-column>
        <el-table-column prop="lastOneMonthDeliveryQuantity" :label="`${getMonth(-1)}月已交货量`"></el-table-column>

        <el-table-column prop="thisMonthNotDeliveryQuantity" label="当月待发数量"></el-table-column>
        <el-table-column prop="nextMonthNotDeliveryQuantity" :label="`${getMonth(1)}月待发数量`"></el-table-column>
        <el-table-column prop="threeMonthNotDeliveryQuantity" :label="`${getMonth(2)}月待发数量`"></el-table-column>
        <el-table-column prop="fourMonthNotDeliveryQuantity" :label="`${getMonth(3)}月待发数量`"></el-table-column>
        <el-table-column prop="fiveMonthNotDeliveryQuantity" :label="`${getMonth(4)}月待发数量`"></el-table-column>
        <el-table-column prop="sixMonthNotDeliveryQuantity" :label="`${getMonth(5)}月待发数量`"></el-table-column>
        <el-table-column prop="sevenMonthNotDeliveryQuantity" :label="`${getMonth(6)}月待发数量`"></el-table-column>

        <el-table-column prop="thisEarlyMonthNotDeliveryQuantity" label="当月初Z001订单待发数量" width="120px"></el-table-column>
        <el-table-column prop="nextEarlyMonthNotDeliveryQuantity" :label="`${getMonth(1)}月初Z001订单待发数量`" width="120px"></el-table-column>
        <el-table-column prop="threeEarlyMonthNotDeliveryQuantity" :label="`${getMonth(2)}月初Z001订单待发数量`" width="120px"></el-table-column>
        <el-table-column prop="fourEarlyMonthNotDeliveryQuantity" :label="`${getMonth(3)}月初Z001订单待发数量`" width="120px"></el-table-column>
        <el-table-column prop="fiveEarlyMonthNotDeliveryQuantity" :label="`${getMonth(4)}月初Z001订单待发数量`" width="120px"></el-table-column>
        <el-table-column prop="sixEarlyMonthNotDeliveryQuantity" :label="`${getMonth(5)}月初Z001订单待发数量`" width="120px"></el-table-column>
        <el-table-column prop="sevenEarlyMonthNotDeliveryQuantity" :label="`${getMonth(6)}月初Z001订单待发数量`" width="120px"></el-table-column>

        <el-table-column prop="thisMonthForecastQuantity" label="当月预报数量" width="130px">
          <template slot-scope="scope">
            <el-input type="number" class="number-input" v-model="scope.row.thisMonthForecastQuantity" :min="0" :disabled="isServices"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="nextMonthForecastQuantity" :label="`${getMonth(1)}月预报数量`" width="130px">
          <template slot-scope="scope">
            <el-input type="number" class="number-input" v-model="scope.row.nextMonthForecastQuantity" :min="0" :disabled="isServices"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="threeMonthForecastQuantity" :label="`${getMonth(2)}月预报数量`" width="130px">
          <template slot-scope="scope">
            <el-input type="number" class="number-input" v-model="scope.row.threeMonthForecastQuantity" :min="0" :disabled="isServices"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="fourMonthForecastQuantity" :label="`${getMonth(3)}月预报数量`" width="130px">
          <template slot-scope="scope">
            <el-input type="number" class="number-input" v-model="scope.row.fourMonthForecastQuantity" :min="0" :disabled="isServices"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="fiveMonthForecastQuantity" :label="`${getMonth(4)}月预报数量`" width="130px">
          <template slot-scope="scope">
            <el-input type="number" class="number-input" v-model="scope.row.fiveMonthForecastQuantity" :min="0" :disabled="isServices"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="sixMonthForecastQuantity" :label="`${getMonth(5)}月预报数量`" width="130px">
          <template slot-scope="scope">
            <el-input type="number" class="number-input" v-model="scope.row.sixMonthForecastQuantity" :min="0" :disabled="isServices || (!isServices && getValidMaterialGroup(scope.row))"></el-input>
          </template>
        </el-table-column>
        <el-table-column prop="sevenMonthForecastQuantity" :label="`${getMonth(6)}月预报数量`" width="130px">
          <template slot-scope="scope">
            <el-input type="number" class="number-input" v-model="scope.row.sevenMonthForecastQuantity" :min="0" :disabled="isServices || (!isServices && getValidMaterialGroup(scope.row))"></el-input>
          </template>
        </el-table-column>

        <el-table-column prop="thisMonthChangeQuantity" label="当月需要增加或者减少的量 " width="120px"></el-table-column>
        <el-table-column prop="nextMonthChangeQuantity" :label="`${getMonth(1)}月需要增加或者减少的量`" width="120px"></el-table-column>
        <el-table-column prop="threeMonthChangeQuantity" :label="`${getMonth(2)}月需要增加或者减少的量`" width="120px"></el-table-column>
        <el-table-column prop="fourMonthChangeQuantity" :label="`${getMonth(3)}月需要增加或者减少的量`" width="120px"></el-table-column>
        <el-table-column prop="fiveMonthChangeQUantity" :label="`${getMonth(4)}月需要增加或者减少的量`" width="120px"></el-table-column>
        <el-table-column prop="sixMonthChangeQuantity" :label="`${getMonth(5)}月需要增加或者减少的量`" width="120px"></el-table-column>
        <el-table-column prop="sevenMonthChangeQuantity" :label="`${getMonth(6)}月需要增加或者减少的量`" width="120px"></el-table-column>

        <el-table-column prop="isNewCustomer" label="新老客户"></el-table-column>
        <el-table-column prop="forecastAccuracyCustomer" label="客户预报准确率"></el-table-column>
        <el-table-column prop="forecastAccuracySale" label="销售预报准备率"></el-table-column>
        <el-table-column prop="suggestDiscount" label="建议调整比例"></el-table-column>

        <el-table-column prop="productManager" label="产品计划"></el-table-column>
        <el-table-column show-overflow-tooltip prop="auditRemark" label="审核备注" width="150px"></el-table-column>

        <el-table-column prop="createTime" label="提交日期"></el-table-column>
        <el-table-column prop="creator" label="提交人"></el-table-column>
        <el-table-column prop="updateTime" label="修改日期"></el-table-column>
        <el-table-column prop="updater" label="修改人"></el-table-column>
        <el-table-column prop="frequency" label="本月预报次" width="100px"></el-table-column>
        <el-table-column prop="bdSeller" label="BD销售"></el-table-column>
        <el-table-column prop="stockType" label="备货类型"></el-table-column>
        <el-table-column prop="suggestPrice" label="建议销售价" width="100px"></el-table-column>

        <el-table-column fixed="right" align="center" label="操作" :width="isSalerBD ? '350px' : '300px'">
          <template slot-scope="{ row }">
            <el-button type="primary" @click="showDetail(row)">详情</el-button>
            <el-button type="primary" @click="showLog(row)">操作日志</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog title="预报详情"
                :visible.sync="dialogVisible"
                width="60%"
                center>
          <div v-loading="loading.dialog">
            <el-row>
              <el-col :span="8">
                客户编码：{{ dialogData.customerCode }}
              </el-col>
              <el-col :span="16">
                客户名称：{{ dialogData.customerName }}
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                商品编码：{{ dialogData.productNo }}
              </el-col>
              <el-col :span="16">
                物料描述：{{ dialogData.productDesc }}
              </el-col>
            </el-row>
            <el-table :data="dialogData.orderDetailList" border stripe max-height="400" highlight-current-row>
              <el-table-column
                prop="sapOrderNo"
                label="预报订单号">
              </el-table-column>
              <el-table-column
                prop="sapItemNo"
                label="预报行号">
              </el-table-column>
              <el-table-column
                prop="companyCode"
                label="工厂">
              </el-table-column>
              <el-table-column
                prop="stockLocation"
                label="仓位">
              </el-table-column>
              <el-table-column
                prop="quantity"
                label="订单数量">
              </el-table-column>
              <el-table-column
                prop="clearedQty"
                label="已参考数量">
              </el-table-column>
              <el-table-column
                prop="firstDeliveryDate"
                label="请求发货日期">
              </el-table-column>
            </el-table>
            <el-pagination
              background
              @current-change="currentChange"
              :current-page="dialog.current"
              :page-size="dialog.pageSize"
              layout="total, prev, pager, next, jumper"
              :total="dialog.total">
            </el-pagination>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
          </span>
      </el-dialog>
      <el-dialog title="操作日志"
                :visible.sync="logDialogVisible"
                width="60%"
                center>
          <div v-loading="loading.logDialog">
            <el-table :data="logData" border stripe max-height="400" highlight-current-row :span-method="arraySpanMethod">
              <el-table-column
                prop="operateTime"
                label="操作时间">
              </el-table-column>
              <el-table-column
                prop="operator"
                label="操作人">
              </el-table-column>
              <el-table-column
                prop="operateType"
                label="操作类型">
              </el-table-column>
              <el-table-column
                prop="field"
                label="变更字段">
              </el-table-column>
              <el-table-column
                prop="originValue"
                label="变更前">
              </el-table-column>
              <el-table-column
                prop="updateValue"
                label="变更后">
              </el-table-column>
            </el-table>
            <el-pagination
              background
              @current-change="logCurrentChange"
              :current-page="logDialog.current"
              :page-size="logDialog.pageSize"
              layout="total, prev, pager, next, jumper"
              :total="logDialog.total">
            </el-pagination>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="logDialogVisible = false">确 定</el-button>
          </span>
      </el-dialog>

      <el-dialog title="拒绝" :visible.sync="show.reject">
        <el-form :inline="true" :model="form" ref="form">
          <el-form-item label="理由" prop="reason">
            <el-input type="textarea" :rows="3" placeholder="请输入内容" v-model="form.reason" width="400px"></el-input>
          </el-form-item>
        </el-form>
        <p slot="footer" class="dialog-footer">
          <el-button type="primary" @click="audit(selectedData, 'reject')">提交</el-button>
        </p>
      </el-dialog>

      <el-dialog
        title="操作提示"
        :visible.sync="confirmDialogVisible"
        append-to-body
        width="500px">
        <div v-html="failContent"></div>
        <span slot="footer" class="dialog-footer">
          <el-button type="warning" @click="downloadDetail">下载失败原因明细</el-button>
          <el-button type="default" @click="confirmDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="continueSubmit">{{this.continueType === 'upload' ? '继续上传' : '继续提交'}}</el-button>
        </span>
      </el-dialog>

      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.current"
        :page-sizes="[10, 20, 50]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import { api, getDetail, getLog } from '@/api/saleForecast.js'
// import { getAllFactory } from '@/api/salesForecast';
import { mapState } from 'vuex'
import { status, evmStatus, auditOptions } from './option'

export default {
  data () {
    return {
      dialogVisible: false,
      logDialogVisible: false,
      confirmDialogVisible: false,
      dialogData: {},
      logData: [],
      show: {
        reject: false
      },
      tip: '',
      loading: {
        searchLoading: false,
        pageLoading: false,
        submit: false,
        list: false,
        customer: false,
        upload: false,
        inlineSave: false,
        dialog: false,
        logDialog: false,
        allSubmit: false
      },
      dialog: {
        current: 1,
        pageSize: 10,
        total: 0,
        id: ''
      },
      logDialog: {
        current: 1,
        pageSize: 10,
        total: 0,
        id: ''
      },
      spanArr: [],
      uploadData: {},
      failContent: '',
      continueType: '',
      failExcelUrl: '',
      isContinue: false,
      selectedIds: [],
      select: {
        list: []
      },
      filter: {
        productNo: null,
        customerCode: null,
        customerName: null,
        statusCode: null,
        seller: '',
        customerService: '',
        current: 1,
        pageSize: 10
      },
      option: {
        status,
        auditOptions,
        evmStatus,
        stockLocationCode: {}
      },
      map: {
        stockLocationCode: {}
      },
      form: {
        reason: ''
      },
      selectedData: [],
      table: [
      ],
      list: [],
      total: 0,
      tutorial_link: '//zkh360.yunxuetang.cn/kng/view/video/0f16002a33c94bee8594f0df7f1b2fe8.html?m=1',
      month: new Date().getMonth() + 1,
      isWindows: /windows|win32|win64/i.test(window.navigator.userAgent),
      factoryList: []
    }
  },
  components: {
  },
  computed: {
    ...mapState([
      'userRole'
    ]),
    filterQuerystring () {
      let arr = []
      for (const key in this.filter) {
        if (this.filter[key] !== null) {
          arr.push(`${key}=${this.filter[key]}`)
        }
      }
      return arr.join('&')
    },
    isBuyer () {
      return (!!~this.userRole.indexOf('data-采购员')) || (!!~this.userRole.indexOf('data-销售BD经理')) || (!!~this.userRole.indexOf('商品中心-备货审核'))
    },
    isSalerBD () {
      return !!~this.userRole.indexOf('data-销售BD经理')
    },
    isSaler () {
      return !!~this.userRole.indexOf('data-销售')
    },
    isServices () {
      return !!~this.userRole.indexOf('data-客服')
    }
  },
  // created() {
  //   getAllFactory().then((res) => {
  //     if (res.status === 200) {
  //       this.factoryList = res.result;
  //     }
  //   })
  // },
  methods: {
    getValidMaterialGroup (row) {
      const list = [
        'MOLYKOTE',
        'Others-油',
        'Shell',
        '化学',
        '胶带&标签',
        '金属加工液',
        '普油',
        '清洗防锈',
        '润滑大客户',
        '塑胶',
        '涂料记号标识',
        '脱模',
        'MRO胶',
        'OEM胶',
        '胶黏剂']
      return !~list.indexOf(row.productGroupName)
    },
    getStockLocationCode(row) {
      const list = this.option.stockLocationCode[row.companyCode];
      var res = null;
      if (list) {
        res = list.find(item => item.value === row.stockLocationCode)
      }
      return (res && res.label) || row.stockLocationCode || '';
    },
    getMonth (n) {
      let t = (this.month + n) % 12
      t = t <= 0 ? t + 12 : t
      return t
    },
    search () {
      this.loading.searchLoading = true
      this.loading.list = true
      // compatiable with server-side
      this.filter.productNo = this.filter.productNo || null
      this.filter.customerName = this.filter.customerName || null
      api({
        url: '/sales/forecast/list',
        method: 'get',
        query: this.filter,
        complete: res => {
          if (res.code === 200 && res.data) {
            this.list = res.data
            this.total = res.totalCount
          } else {
            this.$message.error(res.msg || res.message || '操作失败！')
          }
          this.loading.searchLoading = false
          this.loading.list = false
        }
      })
    },

    beforeUpload () {
      this.loading.list = true
      this.loading.upload = true
    },
    onUploadComplete (res, file, fileList) {
      this.loading.list = false
      this.loading.upload = false
      if (res.code === 200 && res.data && res.data.length) {
        this.filter.current = 1
        this.search()
      } else if (res.code === 5001) {
        // this.uploadAgain(res, fileList)
        this.confirmDialogVisible = true
        this.continueType = 'upload'
        this.failContent = res?.msg
        this.failExcelUrl = res?.data
      } else {
        this.$message.error(res.msg || res.message || '操作失败！')
      }
    },
    uploadAgain (res, fileList) {
      // this.$confirm(res.msg, '操作提示', {
      //     confirmButtonText: '继续提交',
      //     dangerouslyUseHTMLString: true,
      //     type: 'warning'
      //   }).then(async () => {
          this.uploadData.isContinue = true;
          // this.$refs.upload.uploadFiles = fileList
          const length = this.$refs.upload.uploadFiles.length - 1
          this.$refs.upload.uploadFiles[length].status = 'ready'
          this.$refs.upload.submit()
          this.uploadData = {};
        // })
    },
    add () {
      this.$router.push({
        path: '/saleForecast/detail',
        query: {
          type: 'create'
        }
      })
    },
    batchSubmit (type) {
      if (this.select.list.length === 0) {
        return this.$message.error('请选择数据条目')
      }
      this.loading.submit = true
      if (this.isSaler) {
        const ids = this.select.list.map(item => {
          return item.id
        })
        this.submit(ids)
        this.loading.submit = false
        // const rqs = this.select.list.map(item => {
        //   return this.submit(item)
        // })
        // Promise.all(rqs).then(() => {
        //   this.loading.submit = false
        //   this.search()
        // }).catch(() => {
        //   this.loading.submit = false
        // })
      }
      if (this.isServices) {
        this.$confirm('<div><p style="color: #f00;">点击生效仅改变预报状态，不会自动做预报单。</p><p>请确认是否已手工创建对应预报单？</p></div>', '提示', {
          dangerouslyUseHTMLString: true
        }).then(() => {
          this.takeEffect(this.select.list, () => {
            this.loading.submit = false
            this.search()
          })
        }).catch(() => {})
      }

      if (this.isBuyer) {
        this.audit(this.select.list, type, () => {
          this.loading.submit = false
          this.search()
        })
      }
    },
    handleSelectionChange (res) {
      this.select.list = res
      this.selectedData = res
    },
    async save (data) {
      this.loading.inlineSave = true
      const loca = this.option.stockLocationCode[data.companyCode].find(item => item.value === data.stockLocationCode)
      data.stockLocation = loca && loca.label
      data.isContinue = false
      const res = await api({
        url: '/sales/forecast/review/update',
        method: 'put',
        data,
        complete: res => {
          if (res.code === 200) {
            this.$message.success('保存成功')
          } else if (res.code === 5001) {
            this.saveAgain(res, data)
          } else {
            this.$message.error(res.msg || res.message || '保存失败！')
          }
        }
      })
      this.loading.inlineSave = false
      return res
    },
    saveAgain (res, data) {
      data.isContinue = true;
      this.$confirm(res.msg, '操作提示', {
          confirmButtonText: '继续保存',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        }).then(async () => {
          await api({
            url: '/sales/forecast/review/update',
            method: 'put',
            data,
            complete: res => {
              if (res.code !== 200) {
                this.$message.error(res.msg || res.message || '保存失败！')
              }
            }
          })
        })
    },
    // 批量操作提示-继续提交
    continueSubmit () {
      this.isContinue = true;
      switch (this.continueType) {
        // 全部提交
        case 'all':
          this.reviewAll()
          break;
        // 批量提交
        case 'batch':
          this.submitAgainApi(this.selectedIds)
          break;
        // 上传
        case 'upload':
          this.uploadAgain()
          break;
        default:
          break;
      }
      this.isContinue = false;
      this.confirmDialogVisible = false;
    },
    async submit (data) {
      this.loading.pageLoading = true
      // 经业务确认后，在提交流程中去掉保存操作
      // const res = await this.save(data)
      // if (res.code !== 200) {
      //   this.loading.pageLoading = false
      //   return
      // }
      const ids = Array.isArray(data) ? data : [data.id]
      return api({
        url: '/sales/forecast/submit/review',
        method: 'put',
        data: {
          idList: ids,
          isContinue: false
        },
        complete: res => {
          if (res.code === 200) {
            this.$message.success('提交成功')
            this.search()
          } else if (res.code === 5001) {
            if (res.data) {
              this.continueType = 'batch'
              this.failContent = res?.msg
              this.confirmDialogVisible = true;
              this.failExcelUrl = res.data;
              this.selectedIds = ids;
            } else {
              this.submitAgain(res, ids)
            }
          } else {
            this.$message.error(res.error || res.msg || res.message || '请求失败！')
          }
          this.loading.pageLoading = false
        }
      })
    },
    submitAgain (res, data) {
      this.$confirm(res.msg, '操作提示', {
          confirmButtonText: '继续提交',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        }).then(() => {
          this.submitAgainApi(data)
        })
    },
    submitAgainApi (data) {
      api({
        url: '/sales/forecast/submit/review',
        method: 'put',
        data: {
          idList: data,
          isContinue: true
        },
        complete: res => {
          if (res.code === 200) {
            this.$message.success('提交成功');
            this.search()
          } else {
            this.$message.error(res.msg || res.message || '保存失败！')
          }
          this.loading.pageLoading = false
          this.confirmDialogVisible = false;
        }
      })
    },
    // 下载失败原因excel
    downloadDetail () {
      if (this.failExcelUrl) {
        window.open(this.failExcelUrl)
      }
    },
    async showDetail (row) {
      this.dialogVisible = true
      this.dialog.id = row.id
      this.getDetailList()
    },
    async showLog (row) {
      this.logDialogVisible = true
      this.logDialog.id = row.id
      this.getLog()
    },
    currentChange (current) {
      this.dialog.current = current
      this.getDetailList()
    },
    logCurrentChange (current) {
      this.logDialog.current = current
      this.getLog()
    },
    async getDetailList () {
      try {
        this.loading.dialog = true
        const res = await getDetail({ id: this.dialog.id, current: this.dialog.current, pageSize: this.dialog.pageSize })
        this.dialogData = res.data
        this.dialog.total = res.totalCount
        this.loading.dialog = false
      } catch (error) {
        console.log(error);
        this.loading.dialog = false
      }
    },
    async getLog () {
      try {
        this.loading.logDialog = true
        const res = await getLog({ id: this.logDialog.id, current: this.logDialog.current, pageSize: this.logDialog.pageSize })
        this.logData = res.data
        this.formatLogData()
        this.getSpanArr(this.logData, 0, 'operateTime')
        this.logDialog.total = res.totalCount
        this.loading.logDialog = false
      } catch (error) {
        console.log(error);
        this.loading.logDialog = false
      }
    },
    formatLogData () {
      const result = []
      this.logData.forEach(data => {
        const identityCode = Math.random()
        data.operateList = data.operateList.map(column => ({
          ...column, ...data, identityCode
        }))
        result.push(...data.operateList)
      })
      this.logData = result
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data, idx, prop) {
      this.spanArr[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1)
          this.position = 0
        } else {
          if (data[index][prop] === data[index - 1][prop] &&
            data[index].identityCode === data[index - 1].identityCode
          ) {
            // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr[idx].push(1)
            this.position = index
          }
        }
      })
    },
    // 表格单元格合并
    arraySpanMethod ({ row, column, rowIndex, columnIndex }) {
      if ([0, 1, 2].indexOf(columnIndex) !== -1) {
        const _row = this.spanArr[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return { rowspan: _row, colspan: _col }
      }
    },
    // TODO: Refactor
    audit (data, type, callback) {
      type = type === 'resolve' ? 'passed' : 'reject'

      let idList
      if (data.length) {
        idList = data.map(item => item.id)
      } else {
        if (!data.id) return this.$message.error('请选择数据条目')
        idList = [data.id]
      }
      let item = null;
      let arr = []
      if (type === 'passed') {
        if (data.length) {
          data.map((a) => {
            arr = [...arr, ...(a.tags || [])];
          })
        } else {
          arr = data.tags || []
        }
      }
      item = arr.find((a) => a.level === 'high')
      if (item) {
        this.$confirm('存在高风险预报行，建议供应商备货或寄售后再通过预报，确定要通过审批吗？', {
          confirmButtonText: '通过',
          cancelButtonText: '取消',
          closeOnClickModal: false,
          type: 'warning'
        }).then(() => {
          this.auditFn(idList, type, callback);
        }).catch(() => {
          this.loading.submit = false
        })
      } else {
        this.auditFn(idList, type, callback);
      }
    },
    auditFn (idList, type, callback) {
      this.loading.pageLoading = true
      api({
        url: `/sales/forecast/review/${type}`,
        method: 'put',
        data: type === 'reject' ? {
          idList,
          reason: this.form.reason
        } : idList,
        complete: res => {
          if (res.code === 200) {
            this.$message.success('请求成功！')
            this.search()
          } else {
            this.$message.error(res.msg || res.message || '请求失败！')
          }
          this.cleanDialog()
          this.loading.pageLoading = false
          callback && callback()
        }
      })
    },
    takeEffect (data, callback) {
      api({
        url: '/sales/forecast/review/effect',
        method: 'put',
        data: data.map(item => item.id),
        complete: res => {
          if (res.code === 200) {
            this.$message.success('请求成功！')
          } else {
            this.$message.error(res.msg || res.message || '请求失败！')
          }
          callback && callback()
        }
      })
    },
    cleanDialog () {
      this.show.reject = false
      this.form.reason = ''
    },
    reject (data) {
      if (data) {
        this.selectedData = [data]
        this.show.reject = true
      } else {
        if (this.select.list.length === 0) {
          this.$message.error('请选择数据条目')
        } else {
          this.show.reject = true
        }
      }
    },
    getOptionStatus () {
      api({
        url: '/sales/forecast/status/dropdown',
        method: 'get',
        complete: res => {
          if (res.code === 200 && res.data) {
            let status = Object.keys(res.data).map(key => {
              return {
                label: res.data[key],
                value: parseInt(key, 10)
              }
            })
            status.unshift({ label: '全部', value: null })
            this.option.status = status
          } else {
            this.$message.error(res.msg || res.error || res.message || '请求失败！')
          }
        }
      })
    },
    // resolve (type) {
    //   this.audit('resolve')
    // },
    // reject () {
    //   this.audit('reject')
    // },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.current = res
      this.search()
    },
    afterChangeFilter () {
      this.filter.current = 1
    },
    getStockLocation (callback) {
      api({
        url: '/sales/forecast/stockLocation/dropdown/linkage',
        method: 'GET',
        complete: res => {
          if (res.code === 200) {
            const cloneData = JSON.parse(JSON.stringify(res.data))
            Object.keys(cloneData).forEach(key => {
              cloneData[key] = cloneData[key].map(item => {
                return {
                  value: item.stockLocation,
                  label: item.stockLocationDes
                }
              })
            })
            this.option.stockLocationCode = cloneData
            callback && callback(res)
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    getTip () {
      api({
        url: '/sales/forecast/review/tip',
        method: 'GET',
        complete: res => {
          if (res.code === 200) {
            this.tip = res.data
          } else {
            this.$message.error(res.error || res.message || '请求失败！')
          }
        }
      })
    },
    getCompanyCode (callback) {
      api({
        url: '/sales/forecast/companycode/dropdown',
        method: 'GET',
        complete: res => {
          if (res.code === 200) {
            this.option.companyCode = res.data.map(item => {
              return {
                value: item.key,
                label: item.value
              }
            })

            this.map.companyCode = res.data.reduce((pre, item) => {
              pre[item.value] = item.key
              return pre
            }, {})

            callback && callback(res)
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },
    passAll () {
      api({
        url: '/sales/forecast/review/all/passed',
        data: this.filter,
        method: 'PUT',
        complete: res => {
          if (res.code === 200) {
            this.$message.success('数据批量处理中，结果会以邮件的形式返回给您')
            this.search()
          } else {
            this.$message.error(res.msg || res.message || '请求失败！')
          }
        }
      })
    },
    approvalAll () {
      api({
        url: '/sales/forecast/approval/all',
        data: this.filter,
        method: 'PUT',
        complete: res => {
          if (res.code === 200) {
            this.$message.success('异步审批中，请稍后')
            this.search()
          } else {
            this.$message.error(res.msg || res.message || '请求失败！')
          }
        }
      })
    },
    reviewAll () {
      this.loading.allSubmit = true;
      api({
        url: '/sales/forecast/submit/all/review',
        data: {
          ...this.filter,
          isContinue: this.isContinue
        },
        method: 'PUT',
        complete: res => {
          if (res.code === 200) {
            this.$message.success('数据批量处理中，结果会以邮件的形式返回给您')
            this.search()
          } else if (res.code === 5001) {
            this.continueType = 'all'
            this.failContent = res?.msg
            this.confirmDialogVisible = true;
          } else {
            this.$message.error(res.msg || res.message || '请求失败！')
          }
          this.loading.allSubmit = false;
        }
      })
    },
    effectAll () {
      this.$confirm('<div><p style="color: #f00;">点击生效仅改变预报状态，不会自动做预报单。</p><p>请确认是否已手工创建对应预报单？</p></div>', '提示', {
        dangerouslyUseHTMLString: true
      }).then(() => {
        api({
          url: '/sales/forecast/review/all/effect',
          data: this.filter,
          method: 'PUT',
          complete: res => {
            if (res.code === 200) {
              this.$message.success('数据批量处理中，结果会以邮件的形式返回给您')
              this.search()
            } else {
              this.$message.error(res.msg || res.message || '请求失败！')
            }
          }
        })
      }).catch(() => {})
    }
  },
  mounted () {
    this.getOptionStatus()
    this.getTip()
    this.getCompanyCode()
    this.getStockLocation(() => {
      this.search()
    })
    this.$msgbox({
      title: '系统下线公告',
      message: `1）各位领导同事好，老销售预报系统即将下线，目前老预报系统已不再支持新增、审核预报！<br />
      2）已提报的预报订单（未清数量），已迁移至新系统，后续新增、修改、审核预报，烦请跳转至新预报系统进行操作，感谢各位的支持！<br />
      3）点击跳转至新预报系统：<a href="/saleForecast/track/list" target="_blank">去提交预报</a>；&nbsp;&nbsp;<a href="/saleForecast/apply/list" target="_blank">去审核预报</a>；`,
      dangerouslyUseHTMLString: true,
      confirmButtonText: '关闭通知'
    })
  }
}
</script>

<style lang="less" src="@/style/component.less"></style>
<style lang="scss">
.page-sale-forecast-list {
  padding: 20px;
  .el-table table thead th {
    border-right: 1px solid #e2e2e2;
  }
  .btn-upload {
    display: inline-block;
    margin: 0 0 0 10px;
  }
  .el-upload-list{
    display: none;
  }
  .tip{
    margin: 0 0 0 20px;
    color: #f00;
  }
  .number-input {
    input {
      padding: 0 0 0 15px;
    }
  }
  .db-line {
    white-space: initial;
    min-width: 50px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    // overflow: hidden;
    text-overflow: ellipsis;
  }
  .module-tip {
    margin-top: -20px;
    margin-bottom: 20px;
    color: #f00;
    a {
      color: #597bee;
      &:hover {
        text-decoration: underline;
      }
    }
  }

  .mb-4 {
    margin-bottom: 4px;
  }

  .mr-4 {
    margin-right: 4px;
  }
}
.btn-tutorial {
  position: fixed;
  top: 120px;
  right: -10px;
  border: 1px solid #597bee;
  border-radius: 40px 0 0 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  width: 110px;
  padding-right: 10px;
  color: #597bee;
  transition: all ease 0.2s;
  z-index: 999;
  background-color: #fff;
  i{
    font-size: 20px;
    vertical-align: middle;
    margin-right: 2px;
  }
  a {
    display: inline-block;
    // visibility: hidden;
    color: #597bee;
    &:hover {
      color: darken($color: #597bee, $amount: 10)
    }
  }
  &:hover {
    background-color: #f3f6ff;
    // a {
      // visibility: visible;
    // }
    // right: -10px;
  }
}
.isWindows {
  .el-table__fixed {
    height: auto !important;
    bottom: 16px;
  }
}
</style>
