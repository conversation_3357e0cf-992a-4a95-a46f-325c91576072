<template>
  <div class="app-container salesForecast-trackList">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
        :show-message="false"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="客户" prop="customerSet">
              <CustomerSelect v-model="searchForm.customerSet" multiple :multipleLimit="20" placeholder="请输入客户名称或编号" style="width: 100%"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库位" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                clearable
                placeholder="请选择库存地点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in positions"
                  :key="item.position"
                  :value="item.position"
                  :label="`${item.position} ${item.positionName}`"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="search-row" label="SKU" prop="skuNo">
              <el-input
                v-model="searchForm.skuNo"
                placeholder="请输入SKU，以空格分隔"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item.brandId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组" prop="materialGroupId">
              <el-select
                v-model="searchForm.materialGroupId"
                filterable
                clearable
                multiple
                placeholder="请输入物料组"
                style="width: 100%"
                remote
                reserve-keyword
                :remote-method="remoteMaterialGroupIdMethod"
                :loading="materialGroupIdLoading"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工厂" prop="factory">
              <el-select
                v-model="searchForm.factory"
                filterable
                clearable
                placeholder="请输入关键词"
                style="width: 100%"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.factory"
                  :label="`${item.factory} ${item.factoryName}`"
                  :value="item.factory"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否EVM寄售" prop="isEvm">
              <el-select
                v-model="searchForm.isEvm"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option label="是" :value="1"></el-option>
                <el-option label="否" :value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <span style="display: inline-block; width: 120px;text-align: right; padding-right: 12px;">展示过去</span>
            <el-input-number
              v-model="historyMonths"
              :controls="false"
              :min="3"
              :max="12"
              style="width: 60px"
            />
            个月预报
          </el-col>
          <el-col :span="24" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="loading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div style="color: red">
      <h4>注意事项：</h4>
      1）未参考预报数会在当月最后一天晚八点自动清零。<br />
      2）仅允许修改本月（包含）以后的预报数据，草稿&已通过&已驳回状态支持修改，待审核状态批导自动过滤，不支持修改！<br />
      3）批量导入为覆盖逻辑！例：若当前已提报200预报量，重新上传预报量为0，将导致200预报被取消！
    </div>
    <div class="flex-between">
      <div style="color: red">
        <h4>系统使用教程：</h4>
        1）首次使用预报系统，请先查看使用教程。<a href="https://doc.weixin.qq.com/doc/w3_ARcAMgbEANoBkXRa3DfSqmE5aTGZc?scode=AAcAtAcPAAkJJZe9i4ARcAMgbEANo" target="_blank">立即查看>></a><br/>
        2）遇到报错和问题，请点击右侧文档自查。<a href="https://doc.weixin.qq.com/doc/w3_ARcAMgbEANoV0mCQntpQiG0tZKPKx?scode=AAcAtAcPAAk0bopDQwARcAMgbEANo" target="_blank">销售预报Q&A>></a>
      </div>
      <div class="icon-tips">
        <img :src="icons['0']" />
        <span>草稿</span>
        <img :src="icons['1']" />
        <span>待审核</span>
        <img :src="icons['2']" />
        <span>已通过</span>
        <img :src="icons['-1']" />
        <span>已驳回</span>
      </div>
      <div class="flex-btns">
        <el-button v-if="role1" type="primary" @click="handleProcessList">个人任务处理进度</el-button>
        <el-button v-if="role1" type="primary" @click="toDel">预报申请</el-button>
        <el-button
          v-if="role1"
          type="primary"
          @click="batchCinform"
          :loading="loading || exportLoading"
        >批量提交</el-button>
        <el-button
          v-if="role1 || role2"
          :loading="loading || exportLoading"
          type="primary"
          @click="handleExport"
          >批量导出</el-button
        >
        <el-dropdown style="margin-left: 10px;margin-right: 10px;"  v-if="role1">
          <el-button type="primary" :loading="loading || importLoading">批量导入<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-upload
                action="/api-ab/sales-report/importExcel"
                :show-file-list="false"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :before-upload="beforeUpload"
                accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                style="display: inline-block; margin-left: 10px"
                name="excelFile"
              >
                <el-button
                  type="text"
                  style="margin-right: 5px"
                  :loading="loading || importLoading"
                  >导入</el-button
                >
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button type="text" :loading="importLoading"  @click="handleDownloadTemplate">
                下载模版
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button v-if="role1" type="primary" @click="closeVisible = true">批量关闭</el-button>
      </div>
    </div>
    <div class="search-result-container">
      <el-table
        border
        :data="basisList"
        :max-height="screenHeight - 200"
        style="width: 100%; margin-top: 10px"
        v-loading="loading"
        @selection-change="selectionChange"
      >
        <el-table-column type="selection" width="55" v-if="role1" fixed></el-table-column>
        <el-table-column align="left" prop="customerName" label="客户" width="200" show-overflow-tooltip fixed>
          <template slot-scope="{row}">
            {{ `${row.customerCode || ''} ${row.customerName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="skuNo" label="SKU" width="120" show-overflow-tooltip fixed></el-table-column>
        <el-table-column align="left" prop="salesProfileDesc" label="销售范围" width="200" show-overflow-tooltip></el-table-column>
        <el-table-column align="left" prop="factoryName" label="工厂" width="200" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.factory || ''} ${row.factoryName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="positionName" label="库位" width="200" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.position || ''} ${row.positionName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="skuDesc" label="物料描述" width="200" show-overflow-tooltip />
        <el-table-column align="left" prop="skuTagsDesc" label="商品标签" width="160" show-overflow-tooltip />
        <el-table-column align="left" prop="brandName" label="品牌" width="160" show-overflow-tooltip />
        <el-table-column align="left" prop="materialGroupName" label="物料组" width="160" show-overflow-tooltip />
        <el-table-column align="center" prop="isEvm" label="是否EVM寄售" width="140" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ row.isEvm === 1 ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="safeStockQty" label="安全库存" width="140" show-overflow-tooltip />
        <el-table-column align="right" prop="lt" label="LT" width="80" show-overflow-tooltip />
        <el-table-column align="right" prop="forecastRate" label="近三个月预报准确度" width="140">
          <template slot-scope="{row}">
            {{ row.forecastRate ? row.forecastRate + '%' : row.forecastRate }}
          </template>
        </el-table-column>
        <el-table-column v-for="time in times" :key="time" :label="time" align="center">
          <el-table-column align="right" prop="sysSuggestQty" label="系统建议量/未参考量/实际订单量/实际交货量" width="280">
            <template slot-scope="{row}" v-if="row.obj &&
            row.obj[time]">
              <div>{{ `${numFormat(row, time, 'sysSuggestQty')} / ${numFormat(row, time, 'unReferQty')} / ${numFormat(row, time, 'soQty')} / ${numFormat(row, time, 'deliveryQty')}` }}</div>
              <div class="divider"></div>
              <div class="divider-bottom">
                <div class="edit-input" v-if="row.obj[time].edit">
                  <el-input-number
                    v-model="row.obj[time].reportQtyEdit"
                    :min="0"
                    :controls="false"
                    :precision="2"
                    :class="{ 'input-error': row.obj[time].reportQtyEdit ===  undefined || row.obj[time].reportQtyEdit < 0 }"
                    style="width: 100px"
                    @blur="reportQtyBlur(row.obj[time])"
                  />
                  <img
                    :src="iconReset"
                    class="reset"
                    @click="resetEdit(row.obj[time])"
                  />
                </div>
                <span class="edit-box" v-else>
                  <el-link type="primary" @click="omsShow(row, time)">{{ numFormat(row, time, 'reportQtyEdit') }}</el-link>
                  <i
                    v-if="row.obj[time].allowEdit && ![1, 3].includes(row.obj[time].status)"
                    class="el-icon-edit-outline edit"
                    style="color:#597bee;font-size: 16px"
                    @click="handleEdit(row.obj[time])"
                  ></i>
                </span>

                <span class="flex1">
                  <i v-if="row.obj[time].adjustQty > 0" class="el-icon-top" style="color: #67C23A"></i>
                  <i v-if="row.obj[time].adjustQty < 0" class="el-icon-bottom" style="color: #F56C6C"></i>
                  {{[null, undefined].includes(row.obj[time].adjustQty) ? "--" : numFormatThousand(Math.abs(row.obj[time].adjustQty))}}
                </span>

               <el-tooltip :content="row.obj[time].statusName" placement="bottom-end">
                  <img :src="icons[row.obj[time].status + '']" style="width: 18px"/>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="center" prop="action" label="操作" width="160" fixed="right">
          <template slot-scope="{row}">
            <div class="btns">
            <el-link type="primary" style="margin-right: 10px;" @click="logShow(row)">历史记录</el-link>
            <el-link type="primary" @click="trendShow(row)">订单趋势</el-link>
            <el-link type="primary" @click="stockShow(row)">可用库存分布</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog
      width="800px"
      title="订单信息"
      :visible.sync="omsVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="omsShow(null, '')"
    >
      <el-table :data="omsList" :loading="omsLoading" border fit>
        <el-table-column prop="soNo" label="OMS单号" align="left" width="240">
          <template slot-scope="{row}">
            <el-link type="primary" @click="toOms(row)">{{`${row.soNo || ''}-${row.soItemNo || ''}`}}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="sapNo" label="SAP单号" align="left" width="240">
          <template slot-scope="{row}">
            {{`${row.sapNo || ''}-${row.sapItemNo || ''}`}}
          </template>
        </el-table-column>
        <el-table-column prop="reportQty" label="预报数量" align="right">
          <template slot-scope="{row}">
            {{ typeof row.reportQty === 'number' ? numFormatThousand(row.reportQty) : row.reportQty }}
          </template>
        </el-table-column>
        <el-table-column prop="actualQty" label="已参考订单数量" align="right">
          <template slot-scope="{row}">
            {{ typeof row.refQty === 'number' ? numFormatThousand(row.refQty) : row.refQty }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      width="1000px"
      title="历史记录"
      :visible.sync="logVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="logShow(null)"
    >
      <div style="margin-bottom: 10px">
        <span>选择预报月份：</span>
        <el-date-picker
          v-model="logMonth"
          type="month"
          placeholder="请选择预报月份"
          value-format="yyyy-MM"
          @change="getLog"
        />
      </div>
      <el-table :data="logList" :loading="logLoading" border fit :max-height="420">
        <el-table-column prop="operateTime" label="时间" align="center" show-overflow-tooltip/>
        <el-table-column prop="operator" label="操作人" align="left" show-overflow-tooltip></el-table-column>
        <el-table-column prop="actionDesc" label="操作记录" align="left" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reportNo" label="预报申请单号" align="left" show-overflow-tooltip></el-table-column>
        <el-table-column prop="nowQty" label="预报数量" align="right">
          <template slot-scope="{row}">
            {{ typeof row.nowQty === 'number' ? numFormatThousand(row.nowQty) : row.nowQty }}
          </template>
        </el-table-column>
        <el-table-column prop="adQty" label="调整数量" align="right">
          <template slot-scope="{row}">
            <i v-if="row && row.adjustQty > 0" class="el-icon-top" style="color: #67C23A"></i>
            <i v-if="row && row.adjustQty < 0" class="el-icon-bottom" style="color: #F56C6C"></i>
            <span>{{ [null, undefined].includes(row.adjustQty) ? "--" : numFormatThousand(Math.abs(row.adjustQty)) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" align="left" show-overflow-tooltip></el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      width="800px"
      title="客户订单趋势"
      :visible.sync="trendVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="trendShow(null)"
    >
      <div id="trend-echart" v-loading="trendLoading" style="width: 760px; height:400px;"></div>
    </el-dialog>
    <el-dialog
      width="1000px"
      title="批量关闭"
      :visible="closeVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      :show-close="false"
    >
      <close-dlg
        :close="closeShow"
        :factoryList="factoryList"
        :positions="positions"
      />
    </el-dialog>
    <el-dialog
      width="600px"
      title="可用库存分布"
      :visible.sync="stockVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="stockShow(null)"
    >
      <el-table
        border
        :data="stockList"
        :max-height="500"
        style="width: 100%; margin-top: 10px"
        v-loading="stockLoading"
      >
        <el-table-column prop="positionCode" label="库位" align="left">
          <template slot-scope="{row}">
            {{ `${row.positionCode || ''} ${row.positionName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column prop="availableQty" label="可用库存" align="right">
          <template slot-scope="{row}">
            {{ typeof row.availableQty === 'number' ? numFormatThousand(row.availableQty) : row.availableQty }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      width="800px"
      title="个人任务处理进度"
      :visible.sync="processListVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-table
        border
        :data="processList"
        :max-height="500"
        style="width: 100%; margin-top: 10px"
        v-loading="processListLoading"
      >
        <el-table-column prop="taskTime" label="时间" align="center" width="120" how-overflow-tooltip></el-table-column>
        <el-table-column prop="operator" label="操作人" align="center" width="120"></el-table-column>
        <el-table-column prop="taskType" label="操作记录" align="center" width="140" how-overflow-tooltip></el-table-column>
        <el-table-column prop="fileName" label="表格名称" align="center" width="120" how-overflow-tooltip></el-table-column>
        <el-table-column prop="taskStatus" label="当前处理状态" align="center" width="160" how-overflow-tooltip></el-table-column>
        <el-table-column prop="url" label="报错数据导出" align="center" width="120">
          <template slot-scope="scope">
            <a v-if="scope.row.url" :href="scope.row.url" target="_blank">下载</a>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { buildSoDetailLink, numFormatThousand } from '@/utils/index';
import { mapState } from 'vuex';
import moment from 'moment';
import {
  getAllFactory,
  getMaterialGroupListApi,
  getBrandListApi,
  getAllPosition
} from '@/api/purchasePlan';
import {
  getSalesReportList,
  getSalesReportListExtInfo,
  getSalesReportLogs,
  getSalesReportOrdersInfo,
  exportSalesApplyNew,
  batchSubmitReport,
  getAvailableStock,
  downloadTemplate,
  getProcessList
} from '@/api/saleForecast';
import CustomerSelect from '@/components/SearchFields/client'
import closeDlg from './closeDlg'
import icon0 from '@/assets/images/track_icon_0.png'
import icon1 from '@/assets/images/track_icon_1.png'
import icon2 from '@/assets/images/track_icon_2.png'
import icon3 from '@/assets/images/track_icon_3.png'
import iconReset from '@/assets/images/track_icon_reset.png'

const echarts = window.echarts
export default {
  name: 'tarckList',
  components: { CustomerSelect, closeDlg },
  data() {
    return {
      numFormatThousand,
      brandIdOptions: [],
      brandIdLoading: false,
      materialGroupOptions: [],
      materialGroupIdLoading: false,
      factoryList: [],
      positions: [],
      loading: false,
      basisList: [],
      selectedList: [],
      times: [],
      searchForm: {
        customerSet: [],
        position: null,
        skuNo: '',
        brandNo: '',
        materialGroupId: [],
        factory: '',
        isEvm: ''
      },
      historyMonths: 3,
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      screenHeight: document.body.clientHeight,
      exportLoading: false,
      importLoading: false,
      icons: {
        '-1': icon0,
        '0': icon1,
        '1': icon2,
        '3': icon2,
        '2': icon3
      },
      iconReset,

      omsVisible: false,
      omsLoading: false,
      omsList: [],

      logVisible: false,
      logId: '',
      logMonth: '',
      logList: [],
      logLoading: false,

      trendVisible: false,
      trendLoading: false,

      closeVisible: false,
      processListVisible: false,
      processList: [],
      processListLoading: false,
      stockVisible: false,
      stockLoading: false,
      stockList: []
    };
  },
  computed: {
    ...mapState(['userRole']),
    role1() {
      return !!~this.userRole.indexOf('data-销售');
    },
    role2() {
      return !!~this.userRole.indexOf('data-预报审核');
    }
  },
  created() {
    getAllFactory().then((res) => {
      if (res.status === 200) {
        this.factoryList = res.result;
      }
    })
    getAllPosition().then((res) => {
      if (res.status === 200) {
        this.positions = res.result;
      }
    })
    this.handleFilter();
  },
  methods: {
    numFormat(row, time, name) {
      let num = row.obj && row.obj[time] && row.obj[time][name]
      return typeof num === 'number' ? numFormatThousand(num) : '--';
    },
    handleProcessList() {
      this.processListLoading = true;
      this.processListVisible = true;
      getProcessList()
        .then((res) => {
          this.processListLoading = false;
          if (res.code === 200) {
            this.processList = res.data;
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.processListLoading = false;
          this.$message.error({ message: '请求失败！' });
        });
    },
    // 导入模版
    handleDownloadTemplate() {
      this.importLoading = true;
      downloadTemplate()
        .then((res) => {
          this.importLoading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.importLoading = false;
          this.$message.error({ message: '下载失败！' });
        });
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.searchForm = {
        customerSet: [],
        position: null,
        skuNo: '',
        brandNo: '',
        materialGroupId: [],
        factory: '',
        isEvm: ''
      }
      this.historyMonths = 3;
    },
    // 远程查找物料组
    remoteMaterialGroupIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.materialGroupIdLoading = true;
        getMaterialGroupListApi({
          name: key,
          bizCode: 'cc_web',
          entityType: 'entity.productgroup'
        }).then((res) => {
          this.materialGroupIdLoading = false;
          if (res.success) {
            this.materialGroupOptions = res.data || [];
          } else {
            this.materialGroupOptions = [];
          }
        });
      } else {
        this.materialGroupOptions = [];
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    async getList() {
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize,
        historyMonths: this.historyMonths
      };
      param.skuNo = param.skuNo ? param.skuNo.split(' ').filter(item => !!item) : [];
      this.times = this.getTimes(this.historyMonths)
      this.loading = true;
      let res = await getSalesReportList(param)
      this.loading = false;
      if (res.code === 200) {
        this.basisList = res.data || [];
        this.total = res.total || 0;
        if (res.data.length) {
          this.getExtInfo(res.data.map((a) => a.id))
        }
      } else {
        this.$message.error({ message: res.msg, duration: 6000 });
      }
    },
    async getExtInfo(ids) {
      let res = await getSalesReportListExtInfo({ historyMonths: this.historyMonths }, ids)
      if (res.code === 200) {
        if (res.data) {
          this.basisList = this.basisList.map((a) => {
            let item = res.data.find((b) => b.id === a.id)
            if (item) {
              let obj = {};
              (item.monthInfoList || []).forEach((b) => {
                b.adjustQty = (b.reportQty || 0) - (b.oldReportQty || 0)
                b.reportQtyEdit = b.reportQty
                obj[b.batchDate] = b
              })
              return {
                ...a,
                ...item,
                obj
              }
            } else {
              return a
            }
          });
        }
      } else {
        this.$message.error({ message: res.msg, duration: 6000 });
      }
    },
    getTimes(num) {
      let result = []
       for (let i = -num; i <= 7; i++) {
        result.push(moment().month(moment().month() + i).format('YYYY-MM'))
       }
      return result
    },
    handleExport() {
      let param = { ...this.searchForm };
      param.skuNo = param.skuNo ? param.skuNo.split(' ').filter(item => !!item) : [];
      this.exportLoading = true;
      exportSalesApplyNew(param)
        .then((res) => {
          this.exportLoading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.exportLoading = false;
          this.$message.error({ message: '导出失败！' });
        });
    },
    toDel() {
      this.$router.push({
        path: '/saleForecast/apply/edit/0'
      })
    },
    selectionChange(val) {
      this.selectedList = val;
    },
    batchCinform() {
      if (this.selectedList.length === 0) {
        this.$message.warning({ message: '请先选择数据！' });
        return;
      }
      let list = [];
      let emptyList = []
      this.selectedList.forEach((a) => {
        let monthInfoList = []
        let items = Object.values(a.obj)
        items.forEach((b) => {
          if (b.edit) {
            monthInfoList.push({
              batchDate: b.batchDate,
              reportQty: b.reportQtyEdit,
              reportReason: ''
            })
            if (b.reportQtyEdit === '' || b.reportQtyEdit === null || b.reportQtyEdit === undefined || b.reportQtyEdit < 0) {
              emptyList.push(b)
            }
          }
        })
        if (monthInfoList.length > 0) {
          list.push({
            monthInfoList,
            customerCode: a.customerCode,
            distributionChannel: a.distributionChannel,
            factory: a.factory,
            factoryName: a.factoryName,
            isEvm: a.isEvm,
            position: a.position,
            positionName: a.positionName,
            productGroup: a.productGroup,
            salesOrg: a.salesOrg,
            salesProfileDesc: a.salesProfileDesc,
            skuNo: a.skuNo
          })
        }
      })
      if (list.length === 0) {
        this.$message.warning({ message: '没有更改过的数据！' });
        return;
      }
      if (emptyList.length > 0) {
        this.$message.warning({ message: '有未填写的预报，请填写后重新提交' });
        return;
      }
      this.exportLoading = true;
      batchSubmitReport(list)
        .then((res) => {
          this.exportLoading = false;
          if (res.code === 200) {
            this.$message.success({ message: res.data });
            this.handleFilter()
          } else {
            this.$message.error({ message: res.msg });
          }
        })
        .catch(() => {
          this.exportLoading = false;
          this.$message.error({ message: '批量提交失败！' });
        });
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.importLoading = true;
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.code === 200) {
        this.$message.success(response.data || '导入成功！');
        this.getList();
      } else {
        let msg = ((response && response.msg) || '导入失败！').replace(/\n/g, '<br/>')
        this.$message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          showClose: true,
          duration: 3000
        })
      }
    },
    onUploadError(error) {
      console.log(error);
      this.importLoading = false;
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    },
    handleEdit(data) {
      this.$set(data, 'edit', true)
    },
    reportQtyBlur(data) {
      let adjustQty = (data.reportQtyEdit || 0) - (data.oldReportQty || 0)
      this.$set(data, 'adjustQty', adjustQty)
    },
    resetEdit(data) {
      let adjustQty = (data.reportQty || 0) - (data.oldReportQty || 0)
      this.$set(data, 'edit', false)
      this.$set(data, 'reportQtyEdit', data.reportQty)
      this.$set(data, 'adjustQty', adjustQty)
    },
    async omsShow(row, month) {
      this.omsVisible = !!row;
      this.omsLoading = true;
      this.omsList = [];
      if (!row) { return }
      let res = await getSalesReportOrdersInfo({ id: row.id, month })
      this.omsLoading = false;
      if (res.code === 200) {
        this.omsList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
    },
    toOms(row) {
      const url = buildSoDetailLink({
        query: {
          soNo: row.soNo || '',
         sapOrderNo: row.sapNo || ''
        }
      })
      window.open(url)
    },
    logShow(row) {
      this.logVisible = !!row;
      this.logId = row?.id || '';
      this.logMonth = moment().format('YYYY-MM');
      this.logLoading = true;
      this.logList = [];
      row && this.getLog()
    },
    async getLog() {
      let res = await getSalesReportLogs({ id: this.logId, month: this.logMonth })
      this.logLoading = false;
      if (res.code === 200) {
        this.logList = (res.data || []).map((a) => {
          return {
            ...a,
            adjustQty: (a.nowQty || 0) - (a.oldQty - 0)
          }
        })
      } else {
        this.$message.error(res.msg);
      }
    },
    trendShow(row) {
      this.trendVisible = !!row;
      this.trendLoading = true;
      if (!row) { return }
      this.$nextTick(() => {
        let xData = this.times
        let yData = [[], [], [], []]
        this.times.forEach((a) => {
          let item = (row.monthInfoList || []).find((b) => b.batchDate === a)
          yData[0].push(item?.sysSuggestQty || 0)
          yData[1].push(item?.oldReportQty || 0)
          yData[2].push(item?.soQty || 0)
          yData[3].push(item?.deliveryQty || 0)
        })
        this.setChart(xData, yData)
        this.trendLoading = false;
      })
    },
    async stockShow(row) {
      this.stockVisible = !!row;
      this.stockLoading = true;
      if (!row) { return }
      let res = await getAvailableStock({
        factoryCode: row.factory,
        positionCode: row.position,
        skuNo: row.skuNo
      })
      if (res.code === 200) {
        this.stockList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
      this.stockLoading = false;
    },
    setChart (xdata, ydata) {
      const myChart = echarts.init(document.getElementById('trend-echart'))
      // 指定图表的配置项和数据
      const option = {
        tooltip: {
          formatter: function (a, b, c, d) {
            let str = a.seriesName + '：' + a.value + '<br />日期：' + a.name
            return str
          }
        },
        legend: {
          bottom: 0,
          itemHeight: 10, // 改变圆圈大小
          itemGap: 10, // 改变item间距
          data: ['建议量', '已预报量', '实际订单量', '实际交货量']
        },
        grid: {
          top: '5%',
          left: '1%',
          right: '5%  ',
          bottom: '10%',
          containLabel: true
        },
        label: {
          show: true,
          position: 'top'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xdata
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '建议量',
            type: 'line',
            symbol: 'circle',
            stack: '全部',
            data: ydata[0],
            smooth: true,
            color: '#5383f1', // 改变折线点的颜色
            lineStyle: {
              color: '#5383f1' // 改变折线颜色
            }
          },
          {
            name: '已预报量',
            type: 'line',
            symbol: 'circle',
            stack: '预报量',
            data: ydata[1],
            smooth: true,
            color: '#55c6ef',
            lineStyle: {
              color: '#55c6ef'
            }
          },
          {
            name: '实际订单量',
            type: 'line',
            symbol: 'circle',
            stack: '实际订单量',
            data: ydata[2],
            smooth: true,
            color: '#915eea',
            lineStyle: {
              color: '#915eea'
            }
          },
          {
            name: '实际交货量',
            type: 'line',
            symbol: 'circle',
            stack: '实际交货量',
            data: ydata[3],
            smooth: true,
            color: '#d7ab00',
            lineStyle: {
              color: '#d7ab00'
            }
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
    },
    closeShow(flag) {
      this.closeVisible = false
      flag && this.getList()
    }
  }
};
</script>

<style lang="scss">
.salesForecast-trackList {
  .flex-between {
    display: flex;
    align-items: flex-end;
    margin-top: 10px;
    .icon-tips {
      flex: 1;
      width: 0;
      min-width: 290px;
      display: flex;
      align-items: center;
      img {
        margin-left: 10px;
        margin-right: 4px;
        width: 18px;
      }
    }
    .flex-btns {
      display: flex;
      flex-wrap: nowrap;
    }
  }
  .divider {
    height: 1px;
    width: calc(100% + 20px);
    border-top: 1px solid #EBEEF5;
    margin: 8px 0 8px -10px;
  }
  .divider-bottom {
    display: flex;
    align-items: center;
    .edit-box {
      padding: 0 6px;
      height: 32px;
      line-height: 28px;
      border: 1px solid rgb(220, 223, 230);
      border-radius: 4px;
      text-align: left;
      width: 100px;
      background: #F5F7FA;
      position: relative;
      font-size: 12px;
      .edit {
        position: absolute;
        right: 2px;
        bottom: 8px;
        cursor: pointer;
      }
    }
    .edit-input {
      position: relative;
      input {
        text-align: left;
        padding-right: 30px;
      }
      .reset {
        position: absolute;
        right: 2px;
        bottom: 8px;
        cursor: pointer;
        width: 18px;
      }
    }
    .flex1 {
      flex: 1;
      text-align: center;
    }
  }
}
.input-error {
  border: 1px solid #f56c6c;
  border-radius: 5px;
}
</style>
