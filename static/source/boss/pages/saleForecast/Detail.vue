<template>
  <div class="app-container page-sale-forecast-detail" v-loading="loading.pageLoading">

    <div class="module-list">
      <div class="module-btns left">
        <div class="btn-group flex align-start">
          <a class="el-button el-button--small btn-get-template" href="/data-center-front/sales/forecast/template/download" target="_blank">获取模板</a>
          <el-upload class="btn-upload" action="/data-center-front/sales/forecast/resolve/excel" accept=".xlsx,.xls,.xlsm"
            :on-success="onUploadComplete" :on-error="onUploadComplete" :before-upload="beforeUpload" name="file">
            <el-button size="small" type="primary">导入</el-button>
          </el-upload>
          <span class="tip">
            <span>注：仅胶油类物料组支持做7个月预报，其余物料组填入第6/7个月的预报数量无效。</span>
            <span>要在上海预报备货的MRO物料，仓位请选择“上海总仓-MRO品类仓”</span>
          </span>
        </div>
      </div>

      <el-table :data="pageList" style="width: 100%" v-loading="loading.list">
        <!-- <el-table-column fixed type="selection" label="序号"></el-table-column> -->

        <template v-for="(item, index) in keys">
          <el-table-column :prop="item.prop" :label="item.label" :key="index" :width="item.prop === 'companyCode' ? '200px' : 'auto'">
            <template slot="header">
              <span>{{ item.label }} <strong class="red" v-if="item.required">*</strong> </span>
            </template>
            <template slot-scope="scope">
              <!-- {{ scope.row.companyCode }} -->
              <el-select v-model="scope.row[item.prop]" placeholder="请选择" v-if="item.prop === 'companyCode'" @change="afterChangeCompanyCode(scope.row)">
                <el-option v-for="item in option.companyCode" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <el-input v-model.trim="scope.row[item.prop]" v-else-if="item.prop === 'productNo'" @change="afterChangeProductNo(scope.row)"></el-input>
              <el-select v-model="scope.row[item.prop]" placeholder="请选择" v-else-if="item.prop === 'stockLocationCode'" @focus="() => changesalePosition(scope.row)" filterable>
                <el-option v-for="item in option.stockLocationCode" :key="item.value" :label="item.name" :value="item.code"></el-option>
              </el-select>
              <el-select v-model="scope.row[item.prop]" placeholder="请选择" v-else-if="item.prop === 'evm'">
                <el-option v-for="item in option.evmStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              <!-- <el-select v-model="scope.row[item.prop]" placeholder="请选择" value-key="factoryCode" v-else-if="item.prop === 'factory'">
                <el-option v-for="item in scope.row.factoryList" :key="item.factoryCode" :label="`${item.factoryCode} ${item.factoryName}`" :value="item"></el-option>
              </el-select> -->
              <el-input v-model.trim="scope.row[item.prop]" v-else-if="item.type === 'string'"></el-input>
              <el-input type="number" class="number-input" v-model="scope.row[item.prop]" :min="0" v-else-if="item.type === 'number'"></el-input>
            </template>
          </el-table-column>
        </template>

        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button @click="remove(scope)">删除 - </el-button>
          </template>
        </el-table-column>
      </el-table>

      <a class="btn-add-excel" @click="add">
        <span>添加 +</span>
      </a>

      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.current"
        :page-sizes="[10, 20, 50]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>

    </div>

    <div class="module-btns right">
      <div class="btn-group">
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </div>

  </div>
</template>

<script>

import { api } from '@/api/saleForecast.js'
import { status, evmStatus } from './option'
import { getDeliveryWarehouse } from '@/api/orderSale'

export default {
  data () {
    return {
      month: new Date().getMonth() + 1,
      loading: {
        pageLoading: false,
        list: false,
        customer: false
      },
      filter: {
        customerName: '',
        customerCode: null,
        customerStatus: null,
        current: 1,
        pageSize: 10
      },
      option: {
        status,
        evmStatus,
        companyCode: [],
        stockLocationCode: {},
        customer: []
      },
      map: {
        stockLocationCode: {},
        companyCode: {}
      },
      list: []
    }
  },
  components: {
  },
  created () {
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
  },
  computed: {
    companyFactoryList () {
      return this.$store.state.orderPurchase.companyFactoryList
    },
    pageList () {
      const start = (this.filter.current - 1) * this.filter.pageSize
      const list = this.list.slice(start, start + this.filter.pageSize)
      return list
    },
    total () {
      return this.list.length
    },
    keys () {
      return [
        { label: '公司名称', prop: 'companyCode', type: 'string', required: true },
        { label: '客户代码', prop: 'customerCode', type: 'string', required: true },
        { label: '物料编号', prop: 'productNo', type: 'string', required: true },
        // { label: '工厂', prop: 'factory', type: 'object', required: true },
        { label: '最多发货仓', prop: 'stockLocationCode', type: 'string', required: true },
        { label: '当月预报数量', prop: 'thisMonthForecastQuantity', type: 'number' },
        { label: `${this.getMonth(1)}月预报数量`, prop: 'nextMonthForecastQuantity', type: 'number' },
        { label: `${this.getMonth(2)}月预报数量`, prop: 'threeMonthForecastQuantity', type: 'number' },
        { label: `${this.getMonth(3)}月预报数量`, prop: 'fourMonthForecastQuantity', type: 'number' },
        { label: `${this.getMonth(4)}月预报数量`, prop: 'fiveMonthForecastQuantity', type: 'number' },
        { label: `${this.getMonth(5)}月预报数量`, prop: 'sixMonthForecastQuantity', type: 'number' },
        { label: `${this.getMonth(6)}月预报数量`, prop: 'sevenMonthForecastQuantity', type: 'number' },
        { label: '是否EVM寄售', prop: 'evm', type: 'string', required: true }
      ]
    }
  },
  methods: {
    getMonth (n) {
      let t = (this.month + n) % 12
      t = t <= 0 ? t + 12 : t
      return t
    },
    validate () {
      let valid = true
      this.list.forEach(item => {
        for (let i = 0; i < this.keys.length; i++) {
          const key = this.keys[i]
          if (key.required && typeof item[key.prop] === 'string' && item[key.prop].replace(/^\s+|\s+$/g, '') === '') {
            valid = false
            break
          }
        }
      })
      return valid
    },
    submit () {
      const valid = this.validate()
      if (!valid) {
        return this.$message.error('请补全必填字段')
      }
      this.loading.pageLoading = true
      // let params = this.list.map((a) => {
        // let item = {
        //   ...a,
        //   factory: a.factory.factoryCode,
        //   factoryName: a.factory.factoryName
        // }
        // delete item.factoryList
        // return item
      // })
      api({
        url: '/sales/forecast/batch/add',
        method: 'POST',
        data: this.list,
        complete: res => {
          console.log('res', res)
          this.loading.pageLoading = false
          if (res.code === 200) {
            this.$message.success('保存成功！')
            setTimeout(() => {
              this.$closeTag(this.$route.path)
            }, 1000)
          } else {
            this.$message.error(res.msg || res.message || '保存失败！')
          }
        }
      })
    },
    beforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.loading.list = true
    },
    onUploadComplete (res) {
      this.loading.list = false
      let self = this
      if (res.code === 200 && res.data && res.data.length) {
        const list = res.data
        list.forEach(item => {
          item.companyCode = self.map.companyCode[item.companyName]
          item.stockLocationCode = self.map.stockLocationCode[item.companyCode] && self.map.stockLocationCode[item.companyCode][item.stockLocation]
        })
        this.list.push(...list)
      } else {
        this.$message.error(res.msg || res.message || '操作失败！')
      }
    },
    // +/-
    add () {
      this.filter.current = Math.ceil(this.total / this.filter.pageSize)
      this.list.push({
        companyCode: null,
        customerCode: '',
        productNo: '',
        stockLocationCode: null,
        evm: '',
        thisMonthForecastQuantity: null,
        nextMonthForecastQuantity: null,
        threeMonthForecastQuantity: null,
        fourMonthForecastQuantity: null,
        // factory: null,
        fiveMonthForecastQuantity: null
      })
    },
    remove (scope) {
      const start = (this.filter.current - 1) * this.filter.pageSize
      this.list.splice(start + scope.$index, 1)
    },

    getCompanyCode (callback) {
      api({
        url: '/sales/forecast/companycode/dropdown',
        method: 'GET',
        complete: res => {
          if (res.code === 200) {
            this.option.companyCode = res.data.map(item => {
              return {
                value: item.key,
                label: item.value
              }
            })

            this.map.companyCode = res.data.reduce((pre, item) => {
              pre[item.value] = item.key
              return pre
            }, {})

            callback && callback(res)
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },

    afterChangeCompanyCode (data) {
      data.stockLocationCode = null
      this.$forceUpdate()
      data.factorySet = ((this.companyFactoryList.find(item => Number(item.companyCode) === data.companyCode) || {}).factoryList || []).map(item => item.factoryCode)
      // data.factoryList = (this.companyFactoryList.find(item => Number(item.companyCode) === data.companyCode) || {}).factoryList || []
      // data.factory = null
      // data.factorySet = data.factoryList.map(item => item.factoryCode)
    },

    afterChangeProductNo (data) {
      data.stockLocationCode = null
      this.$forceUpdate()
    },
    async changesalePosition (row) {
      try {
        const data = {
          skuSet: [row.productNo],
          factorySet: row.factorySet,
          positionScope: 1
        }
        const res = await getDeliveryWarehouse(data)
        this.option.stockLocationCode = res.result.filter(item => item.allPosition && item.allPosition.length > 0).flatMap(item => {
          item.allPosition.map(val => {
            val.name = val.code + ' ' + val.name
            return val
          })
          return item.allPosition
        })
      } catch (error) {
        console.log(error);
      }
    },
    getStockLocation (callback) {
      api({
        url: '/sales/forecast/stockLocation/dropdown/linkage',
        method: 'GET',
        complete: res => {
          if (res.code === 200) {
            const cloneData = JSON.parse(JSON.stringify(res.data))
            let stockLocationOption = {}
            let stockLocationMap = {}
            Object.keys(cloneData).forEach(key => {
              stockLocationOption[key] = cloneData[key].map(item => {
                return {
                  value: item.stockLocation,
                  label: item.stockLocationDes
                }
              })

              stockLocationMap[key] = cloneData[key].reduce((res, item) => {
                res[item.stockLocationDes] = item.stockLocation
                return res
              }, {})
            })
            this.option.stockLocationCode = stockLocationOption
            this.map.stockLocationCode = stockLocationMap

            callback && callback(res)
          } else {
            this.$message.error(res.msg || res.message || '获取失败！')
          }
        }
      })
    },

    handleSizeChange (res) {
      this.filter.pageSize = res
    },

    handleCurrentChange (res) {
      this.filter.current = res
    }
  },
  mounted () {
    this.getCompanyCode()
    this.getStockLocation()
  }
}
</script>

<style lang="less" src="@/style/component.less"></style>
<style lang="scss">
.page-sale-forecast-detail {
  padding: 20px;
  .el-upload-list{
    display: none;
  }
  .module-list{
    margin-bottom: 20px;

     .flex {
       display: flex;
     }
     .align-start {
       align-items: flex-start;
     }
    .tip{
      margin: 0 0 0 20px;
      color: #f00;
      display: flex;
      flex-direction: column;
    }
  }
  .cell{
    .red {
      color: #f00;
    }
  }
  .btn-add-excel {
    display: block;
    text-align: center;
    padding: 10px;
    border: 1px solid #eee;
    margin: 10px auto 0 auto;
    border-radius: 3px;
    cursor: pointer;

    &:hover{
      background-color: #f8f8f8;
    }
  }
  .number-input {
    input {
      padding: 0 0 0 15px;
    }
  }
  .btn-upload{
    display: inline-block;
    margin: 0 0 0 10px;
  }
}
</style>
