<template>
  <div class="app-container trackList-closeDlg">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="80px"
        label-position="right"
        :show-message="false"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="客户" prop="customerCode">
              <CustomerSelect v-model="searchForm.customerCode" placeholder="请输入客户名称或编号" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brandNo">
              <el-select
                v-model="searchForm.brandNo"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
                style="width: 100%"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.brandId"
                  :label="item.brandName"
                  :value="item.brandId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item class="search-row" label="SKU" prop="skuNo">
              <el-input
                v-model="searchForm.skuNo"
                placeholder="请输入SKU"
                clearable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item label="工厂" prop="factory">
              <el-select
                v-model="searchForm.factory"
                filterable
                clearable
                placeholder="请输入关键词"
                style="width: 100%"
              >
                <el-option
                  v-for="item in factoryList"
                  :key="item.factory"
                  :label="`${item.factory} ${item.factoryName}`"
                  :value="item.factory"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="库位" prop="position">
              <el-select
                v-model="searchForm.position"
                filterable
                clearable
                placeholder="请选择库存地点"
                style="width: 100%"
              >
                <el-option
                  v-for="item in positions"
                  :key="item.position"
                  :value="item.position"
                  :label="`${item.position} ${item.positionName}`"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="预报月份" prop="batchDateMin">
              <el-date-picker
                clearable
                v-model="searchForm.batchDateMin"
                type="month"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="请选择起始月份"
              />
            </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item label="" prop="batchDateMax">
              <span slot="label" style="text-align: center; display: inline-block; width: 55px">~</span>
              <el-date-picker
                clearable
                v-model="searchForm.batchDateMax"
                type="month"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="请选择结束月份"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right;margin-top: 10px;">
            <el-button type="primary" :loading="loading" @click="handleFilter">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <el-table
        border
        :data="basisList"
        :max-height="500"
        style="width: 100%; margin-top: 10px"
        v-loading="loading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column align="left" prop="customerCode" label="客户" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.customerCode || ''} ${row.customerName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="salesProfileDesc" label="销售范围" show-overflow-tooltip></el-table-column>
        <el-table-column align="left" prop="skuNo" label="SKU" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.skuNo || ''} ${row.skuTagsDesc || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="factory" label="工厂" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.factory || ''} ${row.factoryName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="left" prop="position" label="库位" show-overflow-tooltip>
          <template slot-scope="{row}">
            {{ `${row.position || ''} ${row.positionName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="batchDate" label="预报月份" width="80"></el-table-column>
        <el-table-column align="right" prop="reportQty" label="预报数量">
          <template slot-scope="{row}">
            {{typeof row.reportQty === 'number' ? numFormatThousand(row.reportQty) : row.reportQty }}
          </template>
        </el-table-column>
         <el-table-column align="right" prop="refQty" label="已参考订单数量">
          <template slot-scope="{row}">
            {{typeof row.refQty === 'number' ? numFormatThousand(row.refQty) : row.refQty }}
          </template>
        </el-table-column>
         <el-table-column align="right" prop="unReferQty" label="未参考预报数量">
          <template slot-scope="{row}">
            {{typeof row.unReferQty === 'number' ? numFormatThousand(row.unReferQty) : row.unReferQty }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="referOrderNo" label="是否EVM寄售">
          <template slot-scope="{row}">{{ row.isEvm === 1 ? '是' : '否'}}</template>
        </el-table-column>
      </el-table>
    </div>
    <div style="text-align: right; padding-top: 10px">
      <el-pagination
        v-show="total > 0"
        background
        :current-page.sync="listQueryInfo.pageNum"
        :page-size.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50]"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div class="btns">
      <el-button type="primary" :loading="loading || btnLoading" @click="handleClick">关闭预报</el-button>
      <el-button @click="close(true)">取消</el-button>
    </div>
  </div>
</template>

<script>
import { numFormatThousand } from '@/utils/index';
import { getBrandListApi } from '@/api/purchasePlan';
import { getSalesReportCloseList, batchCloseReport } from '@/api/saleForecast';
import CustomerSelect from '@/components/SearchFields/client'

export default {
  name: 'tarckCloseDlg',
  props: [ 'close', 'factoryList', 'positions' ],
  components: { CustomerSelect },
  data() {
    return {
      numFormatThousand,
      brandIdOptions: [],
      brandIdLoading: false,
      loading: false,
      btnLoading: false,
      basisList: [],
      selectRows: [],
      searchForm: {
        batchDateMin: '',
        batchDateMax: ''
      },
      listQueryInfo: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      screenHeight: document.body.clientHeight,
      pickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(this.searchForm.batchDateMin)).getTime()
        }
      }
    };
  },
  methods: {
    disabledDate(time) {
      const now = new Date();
      return time.getTime() < now.getTime();
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.pageNum = 1;
      this.getList();
      this.$refs.multipleTable && this.$refs.multipleTable.clearSelection();
    },
    // 远程查找品牌
    remoteBrandIdMethod(query) {
      const key = query.trim();
      if (key !== '') {
        this.brandIdLoading = true;
        getBrandListApi({
          brandName: key
        }).then((res) => {
          this.brandIdLoading = false;
          if (res.success) {
            this.brandIdOptions = res.data || [];
          } else {
            this.brandIdOptions = [];
          }
        });
      } else {
        this.brandIdOptions = [];
      }
    },
    handleCurrentChange(val) {
      this.listQueryInfo.pageNum = val;
      this.getList();
    },
    handleSizeChange(val) {
      this.listQueryInfo.pageSize = val;
      this.listQueryInfo.pageNum = 1;
      this.getList();
    },
    async getList() {
      let param = {
        ...this.searchForm,
        pageNum: this.listQueryInfo.pageNum,
        pageSize: this.listQueryInfo.pageSize
      };
      param.skuNo = param.skuNo ? param.skuNo.split(' ').filter(item => !!item) : [];
      this.loading = true;
      let res = await getSalesReportCloseList(param)
      this.loading = false;
      if (res.code === 200) {
        this.basisList = res.data || [];
        this.total = res.total || 0;
      } else {
        this.$message.error({ message: res.msg, duration: 6000 });
      }
    },
    handleSelectionChange (val) {
      this.selectRows = val
    },
    async handleClick() {
      if (this.selectRows.length === 0) {
        this.$message.error('请先选择数据！')
        return
      }
      let detailIds = this.selectRows.map((a) => a.detailId)
      this.btnLoading = true
      let res = await batchCloseReport(detailIds)
      this.btnLoading = false
      if (res.code === 200) {
        this.$message.success({ message: '操作成功！' });
        this.getList();
        // this.close(true)
      } else {
        let msg = (res.msg || '操作失败！').replace(/\n/g, '<br/>')
        this.$message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          showClose: true,
          duration: 3000
        })
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.btns {
  text-align: center;
  &:first-child {
    margin-right: 10px;
  }
}
</style>
