<template>
  <div class="app-container salesForecast-applyDtail">
    <el-row class="top">
      <el-col :span="24">
        <b>预报申请单号:</b>
        <span v-if="id !== '0'">{{ id }}</span>
      </el-col>
      <el-col :span="7">
        <b class="required">客户:</b>
        <CustomerSelect
          v-if="id === '0'"
          v-model="detail.customerCode"
          class="input"
          ref="customerSelect"
          placeholder="请输入客户编号或客户名称"
          @change="afterSelectCustomer"
          :disabled="!!list.length"
        ></CustomerSelect>
        <div v-else>{{ `${detail.customerCode || ''} ${detail.customerName || ''}` }}</div>
      </el-col>
      <el-col :span="7">
        <b class="required">销售范围:</b>
        <el-select
          v-if="id === '0'"
          v-model="detail.saleOrgObj"
          class="input"
          value-key="value"
          @change="afterSelectSaleOrg"
          :disabled="!!list.length"
        >
          <el-option
            v-for="(item, i) in saleOrgList"
            :key="i"
            :label="item.label"
            :value="item"
          >
          </el-option>
        </el-select>
        <div v-else>{{ detail.salesProfileDesc || '' }}</div>
      </el-col>
      <el-col :span="5">
        <b>申请人:</b>
        <span>{{ detail.submitterName }}</span>
      </el-col>
      <el-col :span="5">
        <b>是否EVM寄售:</b>
        <el-checkbox
          v-model="detail.isEvm"
          :true-label="1"
          :false-label="0"
          :disabled="id !== '0' || !detail.saleOrgObj || detail.saleOrgObj.distributionChannel !== '06' || !!list.length"
        ></el-checkbox>
      </el-col>
    </el-row>
    <div class="flex-between">
      <div style="color: red">
        1、未参考预报数会在当月最后一天晚八点自动清零。<br/>
        2、仅允许修改本月（包含）以后的预报数据，草稿&已通过&已驳回状态支持修改，待审核状态批导自动过滤，不支持修改！
      </div>
      <div class="icon-tips">
        <img :src="icons['0']" />
        <span>草稿</span>
        <img :src="icons['1']" />
        <span>待审核</span>
        <img :src="icons['2']" />
        <span>已通过</span>
        <img :src="icons['-1']" />
        <span>已驳回</span>
      </div>
      <el-button
        type="primary"
        v-if="type === 'edit' && isConfirm"
        :disabled="!detail.saleOrgObj"
        @click="addItem"
      >新增</el-button>
    </div>
    <div class="search-result-container">
      <el-table
        border
        :data="list"
        :max-height="screenHeight - 320"
        style="width: 100%; margin-top: 10px"
        v-loading="loading"
      >
        <el-table-column align="left" prop="referOrderNo" label="SKU" width="180" show-overflow-tooltip>
          <template slot="header">
            <span class="required">SKU</span>
          </template>
          <template slot-scope="{row,$index}">
            <el-select
              v-if="!row.id"
              v-model="row.skuNo"
              filterable
              placeholder="请输入SKU"
              style="width: 100%"
              remote
              :remote-method="(val) => remoteSku(val, row)"
              :loading="row.skuLoading"
              @change="(val) => skuChange(val, $index)"
            >
              <el-option
                v-for="item in row.skuList"
                :key="item.skuNo"
                :label="`${item.skuNo} ${item.materialDescribe}`"
                :value="item.skuNo"
              >
              </el-option>
            </el-select>
            <span v-else>{{ row.skuNo || ''}}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="skuDesc" label="物料描述" width="180" show-overflow-tooltip />
        <el-table-column align="left" prop="skuTagsDesc" label="商品标签" width="160" show-overflow-tooltip />
        <el-table-column align="left" prop="factory" label="工厂" width="200" show-overflow-tooltip>
          <template slot="header">
            <span class="required">工厂</span>
          </template>
          <template slot-scope="{row,$index}">
            <el-select
              v-if="!row.id"
              v-model="row.factory"
              filterable
              placeholder="请选择工厂"
              style="width: 100%"
              :loading="row.factoryLoading"
              @change="(val) => factoryChange(val, $index)"
            >
              <el-option
                v-for="a in row.factoryList"
                :key="a.id"
                :value="a.factory"
                :label="`${a.factory} ${a.factoryName}`"
              ></el-option>
            </el-select>
            <span v-else>{{ `${row.factory} ${row.factoryName || ''}` }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="referOrderNo" label="库位" width="200" show-overflow-tooltip>
          <template slot="header">
            <span class="required">库位</span>
          </template>
          <template slot-scope="{row,$index}">
            <el-select
              v-if="!row.id"
              v-model="row.position"
              filterable
              placeholder="请选择库位"
              style="width: 100%"
              :loading="row.positionLoading"
              @change="(val) => positionChange(val, $index)"
            >
              <el-option
                v-for="a in row.positionList"
                :key="a.code"
                :value="a.code"
                :label="`${a.code} ${a.name}`"
                :disabled="!detail.isEvm && a.qualityStatus === '11'"
              ></el-option>
            </el-select>
            <span v-else>{{ `${row.position} ${row.positionName || ''}` }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="safeStockQty" label="安全库存量" width="160">
          <template slot-scope="{row}">
            {{ [null, undefined].includes(row.safeStockQty) ? "--" : numFormatThousand(row.safeStockQty) }}
          </template>
        </el-table-column>
        <el-table-column align="right" prop="moq" label="销售MOQ/MPQ" width="160">
          <template slot-scope="{row}">
            <span>{{ [null, undefined].includes(row.moq) ? "--" : numFormatThousand(row.moq) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="right" prop="lt" label="LT" width="80"></el-table-column>
        <el-table-column v-for="time in times" :key="time" :label="time" align="center">
          <el-table-column align="right" prop="sysSuggestQty" label="系统建议量/未参考量/实际订单量" width="240">
            <template slot-scope="{row}" v-if="row.obj && row.obj[time]">
              <div>{{ `${numFormat(row, time, 'sysSuggestQty')} / ${numFormat(row, time, 'unReferQty')} / ${numFormat(row, time, 'soQty')}` }}</div>
              <div class="divider"></div>
              <div class="divider-bottom">
                <div class="edit-input" v-if="row.obj[time].edit">
                  <el-input-number
                    v-model="row.obj[time].reportQtyEdit"
                    :min="0"
                    :controls="false"
                    :precision="2"
                    style="width: 100px"
                    :class="{ 'input-error': row.obj[time].reportQtyEdit ===  undefined || row.obj[time].reportQtyEdit < 0 }"
                    @blur="reportQtyBlur(row.obj[time])"
                  />
                  <img
                    :src="iconReset"
                    class="reset"
                    @click="resetEdit(row.obj[time])"
                  />
                </div>
                <span class="edit-box" v-else>
                  <el-link v-if="row.id" type="primary" @click="omsShow(row, time)">{{ numFormat(row, time, 'reportQtyEdit') }}</el-link>
                  <span v-else>{{ numFormat(row, time, 'reportQtyEdit') }}</span>

                  <i
                    v-if="type === 'edit' && row.obj[time].allowEdit && ![1, 3].includes(row.obj[time].status)"
                    class="el-icon-edit-outline edit"
                    style="color:#597bee;font-size: 16px"
                    @click="handleEdit(row.obj[time])"
                  ></i>
                </span>

                <span class="flex1">
                  <i v-if="row.obj[time].adjustQty > 0" class="el-icon-top" style="color: #67C23A"></i>
                  <i v-if="row.obj[time].adjustQty < 0" class="el-icon-bottom" style="color: #F56C6C"></i>
                  {{[null, undefined].includes(row.obj[time].adjustQty) ? "--" : numFormatThousand(Math.abs(row.obj[time].adjustQty))}}
                </span>

               <el-tooltip :content="row.obj[time].statusName" placement="bottom-end">
                <img :src="icons[row.obj[time].status + '']" style="width: 18px"/>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column align="right" prop="reportReason" label="预报说明" width="180">
          <template slot-scope="{row}">
            <el-input
              v-if="type === 'edit' && row.position && row.obj"
              v-model="row.reportReason"
              type="textarea"
              rows="3"
            />
            <span v-else>{{ row.reportReason }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="salesName" label="销售" width="160" show-overflow-tooltip />
        <el-table-column align="center" prop="purchaseName" label="采购" width="160" show-overflow-tooltip />
        <el-table-column align="center" prop="approveName" label="审核人" width="160" show-overflow-tooltip />
        <el-table-column align="center" prop="reportTime" label="申请时间" width="160" show-overflow-tooltip />
        <el-table-column align="left" prop="action" label="操作" width="110" fixed="right">
          <template slot-scope="{row, $index}">
            <el-link v-if="type === 'edit' && isConfirm && ([0, -1].includes(row.status) || !row.status)" type="primary" @click="deleteItem($index)">删除</el-link>
            <el-link type="primary" @click="stockShow(row)">可用库存分布</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="btns">
      <el-button
        type="primary"
        v-if="type === 'edit' && isConfirm"
        :disabled="!list.length"
        :loading="btnLoading"
        @click="save"
      >提交</el-button>
      <el-button @click="goback">取消</el-button>
    </div>
    <el-dialog
      width="800px"
      title="订单信息"
      :visible.sync="omsVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="omsShow(null, '')"
    >
      <el-table :data="omsList" :loading="omsLoading" border fit>
        <el-table-column prop="soNo" label="OMS单号" align="left" width="240">
          <template slot-scope="{row}">
            <el-link type="primary" @click="toOms(row)">{{`${row.soNo || ''}-${row.soItemNo || ''}`}}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="sapNo" label="SAP单号" align="left" width="240">
          <template slot-scope="{row}">
            {{`${row.sapNo || ''}-${row.sapItemNo || ''}`}}
          </template>
        </el-table-column>
        <el-table-column prop="reportQty" label="预报数量" align="right">
          <template slot-scope="{row}">
            {{ typeof row.reportQty === 'number' ? numFormatThousand(row.reportQty) : row.reportQty }}
          </template>
        </el-table-column>
        <el-table-column prop="actualQty" label="已参考订单数量" align="right">
          <template slot-scope="{row}">
            {{ typeof row.refQty === 'number' ? numFormatThousand(row.refQty) : row.refQty }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog
      width="800px"
      title="客户订单趋势"
      :visible.sync="trendVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="trendShow(null)"
    >
      <div id="trend-echart" v-loading="trendLoading" style="width: 760px; height:400px;"></div>
    </el-dialog>
     <el-dialog
      width="600px"
      title="可用库存分布"
      :visible.sync="stockVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="stockShow(null)"
    >
      <el-table
        border
        :data="stockList"
        :max-height="500"
        style="width: 100%; margin-top: 10px"
        v-loading="stockLoading"
      >
        <el-table-column prop="positionCode" label="库位" align="left">
          <template slot-scope="{row}">
            {{ `${row.positionCode || ''} ${row.positionName || ''}` }}
          </template>
        </el-table-column>
        <el-table-column prop="availableQty" label="可用库存" align="right">
          <template slot-scope="{row}">
            {{ typeof row.availableQty === 'number' ? numFormatThousand(row.availableQty) : row.availableQty }}
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { buildSoDetailLink, numFormatThousand } from '@/utils/index';
import { mapState } from 'vuex';
import moment from 'moment';
import CustomerSelect from '@/components/SearchFields/client'
import { searchSkuList } from '@/api/orderSale';
import {
  getSalesApplyDetail,
  getSalesApplyDetailExtInfo,
  getFactorys,
  getPositions,
  getSalesReportOrdersInfo,
  getSalesApplyDetailTrends,
  getSalesReportListExtInfo,
  batchSubmitReport,
  getAvailableStock
 } from '@/api/saleForecast';
import icon0 from '@/assets/images/track_icon_0.png'
import icon1 from '@/assets/images/track_icon_1.png'
import icon2 from '@/assets/images/track_icon_2.png'
import icon3 from '@/assets/images/track_icon_3.png'
import iconReset from '@/assets/images/track_icon_reset.png'

const echarts = window.echarts
export default {
  name: 'applyDetail',
  components: { CustomerSelect },
  data() {
    return {
      id: '',
      historyMonths: 0,
      numFormatThousand,
      saleOrgList: [],
      loading: false,
      btnLoading: false,
      detail: {
        isEvm: 0
      },
      list: [],
      times: [],
      screenHeight: document.body.clientHeight,
      omsVisible: false,
      omsLoading: false,
      omsList: [],
      trendVisible: false,
      trendLoading: false,
      stockVisible: false,
      stockLoading: false,
      stockList: [],
      icons: {
        '-1': icon0,
        '0': icon1,
        '1': icon2,
        '3': icon2,
        '2': icon3
      },
      iconReset
    };
  },
  computed: {
    ...mapState(['userRole']),
    isConfirm() {
      return !!~this.userRole.indexOf('data-销售');
    }
  },
  created() {
    this.id = this.$route.params.id
    this.type = this.$route.params.type
    if (this.id !== '0') {
      this.getDetail();
    }
    this.times = this.getTimes(this.historyMonths)
  },
  methods: {
    numFormat(row, time, name) {
      let num = row.obj && row.obj[time] && row.obj[time][name]
      return typeof num === 'number' ? numFormatThousand(num) : '--';
    },
    disabledDate(time) {
      const now = new Date();
      const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() + 6, 1);
      return time.getTime() > sixMonthsAgo.getTime() || time.getTime() < now.getTime();
    },
    getDetail() {
      this.loading = true;
      getSalesApplyDetail({ reportNo: this.id })
        .then((res) => {
          this.loading = false;
          if (res.code === 200) {
            this.list = (res.data || [])
            let item = this.list[0] || {}
            this.detail = {
              ...item,
              saleOrgObj: {
                salesOrganization: item.salesOrg,
                distributionChannel: item.distributionChannel,
                productGroup: item.productGroup,
                label: item.salesProfileDesc
              }
            }
            if (this.list.length) {
              this.getExtInfo(this.list.map((a) => a.id))
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.loading = false;
        });
    },
    getTimes(num) {
      let result = []
       for (let i = -num; i <= 7; i++) {
        result.push(moment().month(moment().month() + i).format('YYYY-MM'))
       }
      return result
    },
    async getExtInfo(ids) {
      let res = await getSalesReportListExtInfo({ historyMonths: this.historyMonths }, ids)
      if (res.code === 200) {
        if (res.data) {
          this.list = this.list.map((a) => {
            let item = res.data.find((b) => b.id === a.id)
            if (item) {
              let obj = {};
              (item.monthInfoList || []).forEach((b) => {
                b.adjustQty = (b.reportQty || 0) - (b.oldReportQty || 0)
                b.reportQtyEdit = b.reportQty
                obj[b.batchDate] = b
              })
              return {
                ...a,
                ...item,
                obj
              }
            } else {
              return a
            }
          });
        }
      } else {
        this.$message.error({ message: res.msg, duration: 6000 });
      }
    },
    handleEdit(data) {
      this.$set(data, 'edit', true)
    },
    reportQtyBlur(data) {
      let adjustQty = (data.reportQtyEdit || 0) - (data.oldReportQty || 0)
      this.$set(data, 'adjustQty', adjustQty)
    },
    resetEdit(data) {
      let adjustQty = (data.reportQty || 0) - (data.oldReportQty || 0)
      this.$set(data, 'edit', false)
      this.$set(data, 'reportQtyEdit', data.reportQty)
      this.$set(data, 'adjustQty', adjustQty)
    },
    afterSelectCustomer(val, item) {
      this.detail.customerName = item.customerName;
      this.saleOrgList = item.saleOrgList.map((item) => {
        return {
          ...item,
          value: `${item.salesOrganization}/${item.distributionChannel}/${item.productGroup}`,
          label: `${item.salesOrganization}/${item.distributionChannel}/${item.productGroup} ${item.salesOrganizationName} ${item.distributionChannelName} ${item.productGroupName}`
        }
      })
      delete this.detail.saleOrgObj
      this.detail.isEvm = 0
      this.list = []
    },
    afterSelectSaleOrg(val) {
      this.list = []
      if (val.distributionChannel !== '06' && !!this.detail.isEvm) {
        this.detail.isEvm = 0
      }
    },
    addItem() {
      this.list.push({})
    },
    // 远程查找sku列表
    async remoteSku(search, row) {
      let params = search;
      this.$set(row, 'skuLoading', true)
      let res = await searchSkuList(params)
      this.$set(row, 'skuLoading', false)
      if (res.code === 200) {
        this.$set(row, 'skuList', Array.isArray(res.data) && res.data.length > 0 ? res.data : [])
      } else {
        this.$set(row, 'skuList', [])
        this.$message.error(res.msg);
      }
    },
    // sku更改后清空行数据并请求工厂列表
    async skuChange(val, index) {
      let row = this.list[index]
      let item = row.skuList.find((a) => a.skuNo === val)
      row = {
        skuNo: val,
        skuDesc: item?.materialDescribe || '',
        skuList: row.skuList || [],
        batchDate: row.batchDate || ''
      }
      this.$set(row, 'factoryLoading', true)
      let res = await getFactorys({ salesOrg: this.detail.saleOrgObj?.salesOrganization, skuNo: row.skuNo })
      this.$set(row, 'factoryLoading', false)
      if (res.code === 200) {
        row.factoryList = res.data
      } else {
        this.$message.error(res.msg);
      }
      this.list[index] = row;
      this.list = [...this.list]
    },
    // 工厂切换后清除库位信息及数字信息
    async factoryChange(val, index) {
      let row = this.list[index]
      let item = row.factoryList.find((a) => a.factory === val)
      row = {
        skuNo: row.skuNo,
        skuDesc: row.skuDesc,
        skuList: row.skuList,
        factory: val,
        factoryName: item?.factoryName || '',
        factoryList: row.factoryList,
        positionList: [],
        batchDate: row.batchDate || ''
      }
      this.$set(row, 'positionLoading', true)
      let res = await getPositions({ factory: row.factory, skuNo: row.skuNo, customerNo: this.detail.customerCode })
      this.$set(row, 'positionLoading', false)
      if (res.code === 200) {
        let arr = []
        res.data.forEach((a) => {
          arr = [...arr, ...a.allPosition]
        })
        row.positionList = arr
      } else {
        this.$message.error(res.msg);
      }
      this.list[index] = row;
      this.list = [...this.list]
    },
    async positionChange(val, index) {
      let row = this.list[index]
      let item = row.positionList.find((a) => a.code === val)
      this.$set(row, 'positionName', item.name)
      this.batchDateChange(val, item.name, index)
    },
    async batchDateChange (position, posiitonName, index) {
      let row = this.list[index]
      if (position) {
        let params = {
          position,
          posiitonName,
          customerCode: this.detail.customerCode,
          salesOrg: this.detail?.saleOrgObj?.salesOrganization,
          distributionChannel: this.detail?.saleOrgObj?.distributionChannel,
          productGroup: this.detail?.saleOrgObj?.productGroup,
          isEvm: this.detail.isEvm === 1,
          factory: row.factory,
          factoryName: row.factoryName,
          skuNo: row.skuNo
        }
        let res = await getSalesApplyDetailExtInfo(params)
        if (res.code === 200) {
          let obj = {};
          (res?.data?.monthDataList || []).forEach((b) => {
            b.adjustQty = (b.reportQty || 0) - (b.oldReportQty || 0)
            b.reportQtyEdit = b.reportQty
            obj[b.batchDate] = b
          })
          this.list[index] = {
            ...row,
            obj,
            safeStockQty: res?.data?.safeStockQty,
            moq: res?.data?.moq,
            lt: res?.data?.lt,
            salesName: res?.data?.salesName,
            purchaseName: res?.data?.purchaseName,
            approveName: res?.data?.approveName,
            skuTagsDesc: res?.data?.skuTagsDesc || ''
          }
          this.list = [...this.list]
        } else {
          this.$message.error(res.msg);
        }
      }
    },
    async save() {
      if (!this.list.length) {
        this.$message.warning('明细行不能为空！');
        return
      }
      let flag = this.list.find((a) => !a.position || !a.factory || !a.skuNo)
      if (flag) {
        this.$message.warning('请先填写必填项！');
        return
      }
      let params = []
      let emptyList = []
      this.list.forEach((a) => {
        let monthInfoList = []
        let items = Object.values(a.obj)
        items.forEach((b) => {
          if (b.edit) {
            monthInfoList.push({
              batchDate: b.batchDate,
              reportQty: b.reportQtyEdit,
              reportReason: a.reportReason
            })
            if (b.reportQtyEdit === '' || b.reportQtyEdit === null || b.reportQtyEdit === undefined || b.reportQtyEdit < 0) {
              emptyList.push(b)
            }
          }
        })
        if (monthInfoList.length > 0) {
          params.push({
            monthInfoList,
            customerCode: this.detail.customerCode,
            salesOrg: this.detail.saleOrgObj.salesOrganization,
            distributionChannel: this.detail.saleOrgObj.distributionChannel,
            productGroup: this.detail.saleOrgObj.productGroup,
            salesProfileDesc: this.detail.saleOrgObj.label,
            isEvm: this.detail.isEvm,
            factory: a.factory,
            factoryName: a.factoryName,
            position: a.position,
            positionName: a.positionName,
            skuNo: a.skuNo
          })
        }
      })
      if (params.length === 0) {
        this.$message.warning({ message: '没有更改过的数据！' });
        return;
      }
      if (emptyList.length > 0) {
        this.$message.warning({ message: '有未填写的预报，请填写后重新提交' });
        return;
      }
      this.btnLoading = true
      let res = await batchSubmitReport(params)
      this.btnLoading = false
      if (res.code === 200) {
        this.$message.success('操作成功！');
        sessionStorage.setItem('applyListReload', 1)
        this.goback()
      } else {
        this.$message.error(res.msg);
      }
    },
    async omsShow(row, month) {
      this.omsVisible = !!row;
      if (!row) { return }
      this.omsLoading = true;
      this.omsList = [];
      let res = await getSalesReportOrdersInfo({ id: row.id, month })
      if (res.code === 200) {
        this.omsList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
      console.log(this.omsLoading, 12333333)
      this.omsLoading = false;
    },
    toOms(row) {
      const url = buildSoDetailLink({
        query: {
          soNo: row.soNo || '',
         sapOrderNo: row.sapNo || ''
        }
      })
      window.open(url)
    },
    async trendShow(row) {
      this.trendVisible = !!row;
      this.trendLoading = true;
      let res = await getSalesApplyDetailTrends({ reportId: row.id })
      if (res.code === 0) {
        this.trendLoading = false;
        return;
      }
      let data = res?.data || []
      let xData = []
      let yData = [[], [], []]
      data.forEach((a) => {
        xData.push(a.batchDate)
        yData[0].push(a?.sysSuggestQty || 0)
        yData[1].push(a?.reportQty || 0)
        yData[2].push(a?.soQty || 0)
      })
      this.setChart(xData, yData)
      this.trendLoading = false;
    },
    setChart (xdata, ydata) {
      const myChart = echarts.init(document.getElementById('trend-echart'))
      // 指定图表的配置项和数据
      const option = {
        tooltip: {
          formatter: function (a, b, c, d) {
            let str = a.seriesName + '：' + a.value + '<br />日期：' + a.name
            return str
          }
        },
        legend: {
          bottom: 0,
          itemHeight: 10, // 改变圆圈大小
          itemGap: 10, // 改变item间距
          data: ['建议量', '预报量', '实际订单量']
        },
        grid: {
          top: '5%',
          left: '1%',
          right: '5%  ',
          bottom: '10%',
          containLabel: true
        },
        label: {
          show: true,
          position: 'top'
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xdata
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '建议量',
            type: 'line',
            symbol: 'circle',
            stack: '全部',
            data: ydata[0],
            smooth: true,
            color: '#5383f1', // 改变折线点的颜色
            lineStyle: {
              color: '#5383f1' // 改变折线颜色
            }
          },
          {
            name: '预报量',
            type: 'line',
            symbol: 'circle',
            stack: '预报量',
            data: ydata[1],
            smooth: true,
            color: '#55c6ef',
            lineStyle: {
              color: '#55c6ef'
            }
          },
          {
            name: '实际订单量',
            type: 'line',
            symbol: 'circle',
            stack: '实际订单量',
            data: ydata[2],
            smooth: true,
            color: '#915eea',
            lineStyle: {
              color: '#915eea'
            }
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
    },
    async stockShow(row) {
      this.stockVisible = !!row;
      this.stockLoading = true;
      if (!row) { return }
      let res = await getAvailableStock({
        factoryCode: row.factory,
        positionCode: row.position,
        skuNo: row.skuNo
      })
      if (res.code === 200) {
        this.stockList = res.data || [];
      } else {
        this.$message.error(res.msg);
      }
      this.stockLoading = false;
    },
    deleteItem(index) {
      console.log(index, 1233333)
      this.list.splice(index, 1)
    },
    goback() {
      this.$router.back();
      this.$closeTag(this.$route.path);
    }
  }
};
</script>

<style lang="scss">
.salesForecast-applyDtail {
  .top {
    margin-bottom:20px;
    .el-col {
      display: flex;
      min-height: 40px;
      align-items: center;
      margin-bottom: 10px;
    }
    b {
      display: inline-block;
      width: 100px;
      text-align: right;
      margin-right: 12px;
    }
    div {
      flex: 1;
    }
    .input {
      width: calc(100% - 120px)!important;
    }
  }
  .required {
    &:before {
      content: '*';
      color: #ff7268;
      margin-right: 4px;
    }
    &::after {
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
    }
  }
  .btns {
    text-align: center;
    margin-top: 20px;
  }
  .flex-between {
    display: flex;
    align-items: flex-end;
    .icon-tips {
      flex: 1;
      width: 0;
      min-width: 290px;
      display: flex;
      align-items: center;
      img {
        margin-left: 10px;
        margin-right: 4px;
        width: 18px;
      }
    }
  }
  .divider {
    height: 1px;
    width: calc(100% + 20px);
    border-top: 1px solid #EBEEF5;
    margin: 8px 0 8px -10px;
  }
  .divider-bottom {
    display: flex;
    align-items: center;
    .edit-box {
      padding: 0 6px;
      height: 32px;
      line-height: 28px;
      border: 1px solid rgb(220, 223, 230);
      border-radius: 4px;
      text-align: left;
      width: 100px;
      background: #F5F7FA;
      position: relative;
      font-size: 12px;
      .edit {
        position: absolute;
        right: 2px;
        bottom: 8px;
        cursor: pointer;
      }
    }
    .edit-input {
      position: relative;
      input {
        text-align: left;
        padding-right: 30px;
      }
      .reset {
        position: absolute;
        right: 2px;
        bottom: 8px;
        cursor: pointer;
        width: 18px;
      }
    }
    .flex1 {
      flex: 1;
      text-align: center;
    }
  }
}
.input-error {
  border: 1px solid #f56c6c;
  border-radius: 5px;
}
</style>
