<template>
<div class="atp">
  <div class="atp-query">
    <el-form :inline="true" ref="atpForm" :model="atpQueryData"
      label-width="145px" class="atp-form" :rules="rules"
    >
      <el-form-item label="商品编码" class="atp-form-item" prop="sku">
        <el-input style="width:220px" v-model="atpQueryData.sku" placeholder="商品编码" class="form-item"></el-input>
      </el-form-item>
      <el-form-item label="工厂" class="atp-form-item" prop="factory">
        <el-select style="width:220px" v-model="atpQueryData.factory" placeholder="工厂" class="form-item"
          filterable
          clearable
        >
          <el-option
            v-for="item in factoryList"
            :key="item.code"
            :label="item.code+item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="库位" class="atp-form-item" prop="position">
        <el-select style="width:220px" v-model="atpQueryData.position" placeholder="库位" class="form-item"
          filterable
          clearable
        >
          <el-option
            v-for="item in getPositionList(atpQueryData.factory)"
            :key="item.code"
            :label="item.code+item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="库存类型" class="atp-form-item">
        <el-select style="width:220px" v-model="atpQueryData.providerType" placeholder="库存类型" class="form-item"
          filterable
          clearable
        >
          <el-option label="在库" value="STOCK" />
          <el-option label="在途" value="PO" />
        </el-select>
      </el-form-item>
      <el-form-item class="atp-form-item" style="margin-left: 30px">
        <el-button class="atp-btn" type="primary" @click="onSubmit('atpForm')">查询</el-button>
        <el-button class="atp-btn" @click="onReset('atpForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
  <p class="atp-row">
    <span>在库可用数量总和: {{ availableStockQuantity }}</span>
    <span style="margin-left: 20px">需求数量总和: {{ matchingQuantity }}</span>
  </p>
  <el-table
    border
    fit
    highlight-current-row
    :data="atpListData"
    style="width: 100%"
  >
    <el-table-column
      v-for="col in atpColumnPart"
      align="center"
      :key="col.prop"
      :prop="col.prop"
      :label="col.label"
      :width="col.width"
    >
    </el-table-column>
  </el-table>
  <div class="pagination-container">
    <pagination
      v-show="atpTotal > 0"
      :total="atpTotal"
      :page.sync="atpQueryData.page"
      :limit.sync="atpQueryData.size"
      layout="total, prev, pager, next, jumper"
      @pagination="queryStoData"
    />
  </div>
</div>
</template>

<script>
import { atpList } from '@/api/orderSale.js'
import Pagination from '@/components/Pagination'
import { requestWithLoading } from './utils'

export default {
  data () {
    return {
      atpQueryData: {
        page: 1,
        size: 20
      },
      atpListData: [],
      atpTotal: 0,
      availableStockQuantity: 0,
      matchingQuantity: 0,
      atpColumnPart: [
        { prop: 'demandVoucherId', label: '需求单号' },
        { prop: 'demandItemId', label: '需求行号' },
        { prop: 'demandCustomerServiceName', label: '客服' },
        { prop: 'demandSellerName', label: '销售' },
        { prop: 'demandPlanDate', label: '需求计划日期', width: '150' },
        { prop: 'demandQuantity', label: '需求数量' },
        { prop: 'matchingQuantity', label: '确认数量' },
        { prop: 'providerType', label: '确认库存类型' },
        { prop: 'onWayType', label: '在途库存类型' },
        { prop: 'providerPlanDate', label: '供给计划日期' },
        { prop: 'providerVoucherId', label: '供给单号' },
        { prop: 'providerItemId', label: '供给行号' },
        { prop: 'providerPlanId', label: '供给计划行号' }
      ],
      isLoading: false,
      rules: {
        factory: [
          { required: true, message: '请选择工厂', trigger: 'blur' }
        ],
        sku: [
          { required: true, message: '请输入商品编码', trigger: 'blur' }
        ],
        position: [
          { required: true, message: '请选择库位', trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    Pagination
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    factoryList () {
      return (this.dictList && this.dictList['factory']) ? this.dictList['factory']
        .filter(item => {
          return item.parentCode === ''
        }) : []
    }
  },
  created () {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
  },
  methods: {
    getDictName (type, code) {
      const group = (code && this.dictList && this.dictList[type]) ? this.dictList[type].find(item => {
        return item.code === code
      }) : ''
      return group ? group.name : ''
    },
    getPositionList (factoryCode) {
      return (factoryCode && this.dictList) ? this.dictList['position'].filter(item => {
        return item.parentCode === factoryCode && item.code !== -1
      }) : []
    },
    queryStoData () {
      const { deliveryTime, page, size } = this.atpQueryData
      const params = {
        ...this.atpQueryData,
        current: page,
        size: size
      }
      delete params.page
      delete params.deliveryTime
      if (deliveryTime && deliveryTime.length === 2) {
        params['deliveryBeginTime'] = deliveryTime[0]
        params['deliveryEndTime'] = deliveryTime[1]
      }
      requestWithLoading(this, atpList(params), data => {
        const { atpMatchResultList, availableStockQuantity, matchingQuantity } = data
        this.atpListData = atpMatchResultList.records.map(item => {
          const { providerType, onWayType } = item
          let provider = providerType
          let onway = onWayType
          if (providerType === 'STOCK') {
            provider = '在库'
          } else if (providerType === 'PO') {
            provider = '在途'
          }
          if (onWayType === 'po') {
            onway = '采购订单'
          } else if (onWayType === 'sto') {
            onway = '调拨单'
          }
          return {
            ...item,
            providerType: provider,
            onWayType: onway
          }
        })
        this.atpTotal = atpMatchResultList.total
        this.availableStockQuantity = availableStockQuantity
        this.matchingQuantity = matchingQuantity
      })
    },
    onSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.queryStoData()
        } else {
          return false
        }
      })
    },
    onReset (formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.atp {
 &-query {
   padding: 10px 0;
   .form-item {
     width: 220px;
   }
 }
 &-btn {
   width: 90px;
 }
 &-row {
   font-size: 14px;
   color: #909399;
   padding: 20px;
   display: flex;
   align-items: center;
 }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}
</style>
