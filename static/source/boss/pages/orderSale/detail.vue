<template>
  <div v-if="creator!=='qsc'" class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane name="dd">
        <span slot="label">
          <i class="order_tab_icon icon_dd" /> 订单详情
        </span>
        <DetailInfo
          :voucher-no="listQuery.soVoucherId"
          :active-name="activeName"
          :so-no="soNo"
          @init="handleInit"
        />
      </el-tab-pane>

      <el-tab-pane name="gy">
        <span slot="label">
          <i class="order_tab_icon icon_gy" /> 履约状况
        </span>
        <supply-status
          v-if="activeName === 'gy'"
          :voucher-no="listQuery.soVoucherId"
          :active-name="activeName"
          @change-tab="changeTab"
        />
      </el-tab-pane>

      <el-tab-pane name="cg">
        <span slot="label">
          <i class="order_tab_icon icon_cg" /> 采购进度
        </span>
        <purchase-progress
          v-if="activeName === 'cg'"
          ref="purchaseProgress"
          :voucher-no="listQuery.soVoucherId"
          :so-no="soNo"
          :active-name="activeName"
          @change-tab="changeTab"
        />
      </el-tab-pane>

      <el-tab-pane name="cp">
        <span slot="label">
          <i class="order_tab_icon icon_cp" /> 仓配进度
        </span>
        <warehousing-logistics
          v-if="activeName === 'cp'"
          :voucher-no="listQuery.soVoucherId"
          :active-name="activeName"
          @change-tab="changeTab"
        />
      </el-tab-pane>

      <el-tab-pane name="in">
        <span slot="label">
          <i class="order_tab_icon icon_in" /> 发票进度
        </span>
        <invoice-progress
          v-if="activeName === 'in'"
          :voucher-no="listQuery.soVoucherId"
          :active-name="activeName"
          @change-tab="changeTab"
        />
      </el-tab-pane>

      <el-tab-pane name="rc">
        <span slot="label">
          <i class="order_tab_icon icon_in" /> 收款记录
        </span>
        <ReceiveRecord
          v-if="activeName === 'rc'"
          :voucher-no="listQuery.soVoucherId"
          :active-name="activeName"
          @change-tab="changeTab"
        />
      </el-tab-pane>

      <el-tab-pane name="dz">
        <span slot="label">
          <i class="order_tab_icon icon_dz" /> 电子文档
        </span>
        <ele-document
          v-if="activeName === 'dz'"
          :voucher-no="listQuery.soVoucherId"
          :active-name="activeName"
          :order-no="soNo"
          :orderInfo="orderInfo"
        />
      </el-tab-pane>

      <el-tab-pane name="gd">
        <span slot="label">
          <i class="order_tab_icon icon_in" /> 工单记录
        </span>
        <WorkflowRecord
          v-if="activeName === 'gd'"
          :voucher-no="listQuery.soVoucherId"
          :so-no="listQuery.soNo"
          :active-name="activeName"
        />
      </el-tab-pane>

      <el-tab-pane name="ly" v-if="showLyTab">
        <span slot="label">
          <i class="order_tab_icon icon_in" /> 履约流程
        </span>
        <PerformanceProcess
          v-if="activeName === 'ly'"
          :orderInfo="orderInfo"
          :active-name="activeName"
          @change-tab="changeTab"/>
      </el-tab-pane>

      <el-tab-pane name="log">
        <span slot="label">
          <i class="order_tab_icon icon_in" /> 操作日志
        </span>
        <OperationLog v-if="activeName === 'log'" :soNo="soNo" />
      </el-tab-pane>
    </el-tabs>
  </div>
  <div v-else-if="creator==='qsc'">
    <el-alert
      title="企数采订单暂不支持查看"
      type="error"
      center
      show-icon
      :closable="false"
    >
    </el-alert>
  </div>
</template>

<script>
import DetailInfo from './components/detail/DetailInfo'
import SupplyStatus from './components/detail/supplyStatus'
import PurchaseProgress from './components/detail/purchaseProgress'
import InvoiceProgress from './components/detail/invoiceProgress'
import ReceiveRecord from './components/detail/receiveRecord'
import PerformanceProcess from './components/detail/performanceProcess'
import WarehousingLogistics from './components/detail/warehousingLogistics'
import EleDocument from './components/detail/eleDocument'
import WorkflowRecord from './components/detail/workflowRecord'
import OperationLog from './components/detail/OperationLog'
import { getHashObject } from '@/utils/index.js'
import { tabWhiteList } from '@/api/ifc'
// import { run, destroy } from '@/utils/dify';

export default {
  name: 'OrderDetail',
  components: {
    DetailInfo,
    SupplyStatus,
    WarehousingLogistics,
    EleDocument,
    PurchaseProgress,
    InvoiceProgress,
    ReceiveRecord,
    PerformanceProcess,
    WorkflowRecord,
    OperationLog
  },
  provide () {
    return {
      detailVM: this
    }
  },
  filters: {},
  data () {
    return {
      id: '',
      activeName: '', // 当前标签页
      listQuery: {
        soVoucherId: this.$route.query.soVoucherId,
        soNo: this.$route.query.soNo
      },
      orderInfo: {},
      soNo: this.$route.query.soNo,
      orderNo: this.$route.query.orderNo,
      refresh: true,
      creator: '',
      lyWhiteList: []
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    activeName: {
      handler (newName, oldName) {
        if (oldName !== '' && newName !== '') {
          location.hash = 'point=' + newName
        }
      },
      immediate: true
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderServiceDict () {
      return this.$store.state.orderCommon.orderServiceDict || {}
    },
    showLyTab() {
      const CUR_DATA = window.CUR_DATA
      const name = CUR_DATA.user && CUR_DATA.user.name
      let inWhiteList = this.lyWhiteList.includes('ALL') || this.lyWhiteList.includes(name)
      return inWhiteList && this.orderInfo.orderNo
    }
  },
  mounted() {
    // run('mdwCcLAR2F2uWY9g');
  },
  unmounted() {
    // destroy();
  },
  created () {
    this.initCatch()
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    if (JSON.stringify(this.orderServiceDict) === '{}') {
      this.$store.dispatch('orderCommon/getOrderServiceDict')
    }
    this.setDocTitle()
    this.getLyWhiteList()
  },
  methods: {
    setDocTitle () {
      let originTitle = document.title
      try {
        document.title = `订单${this.$route.query.soVoucherId || ''}`
      } catch (err) {
        document.title = originTitle
      }
    },
    getLyWhiteList () {
      tabWhiteList().then(res => {
        let data = res.result || []
        this.lyWhiteList = data
      })
    },
    changeTab (name) {
      this.activeName = name
    },
    initCatch () {
      this.id = this.$route.query.id
      this.activeName = getHashObject(location.href).point || 'dd'
    },
    handleInit (data) {
      this.orderInfo = data
      if (!this.listQuery.soVoucherId) {
        this.listQuery.soVoucherId = data.sapOrderNo
      }
      const { creator } = data
      if (creator) {
        this.creator = creator
      }
    }
  }
}
</script>

<style scoped>
.order_tab_icon {
  background: url('~@/assets/orderSupplyList/tab_icons.png') no-repeat;
  width: 14px;
  height: 14px;
  display: inline-block;
  position: relative;
  top: 2px;
}
.order_tab_icon.icon_dd {
  background-position: 0 0;
}
.order_tab_icon.icon_gy {
  background-position: -14px 0;
}
.order_tab_icon.icon_cg {
  background-position: -28px 0;
}
.order_tab_icon.icon_cp {
  background-position: -42px 0;
}
.order_tab_icon.icon_dz {
  background-position: -56px 0;
}
.is-active .order_tab_icon {
  background-position-y: -14px;
}
</style>
