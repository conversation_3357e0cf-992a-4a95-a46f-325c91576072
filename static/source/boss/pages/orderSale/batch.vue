<template>
  <div class="batch">
    <DividerHeader>批量订单修改----订单抬头字段</DividerHeader>
    <div class="batch-info">下载批量订单修改模板，将需要修改的订单号填写并维护对应字段，上传模板即可完成修改。
      支持修改订单抬头字段，包括客户参考日期、开票类型、收货联系人、收票联系人、订单联系人、寄票备注、发票备注、自动发货、允许分批、快递公司、附资料邮寄、客户订单号、附COA、附MSDS、附指定文件（邮件提供）、是否自营配送。若要清空某字段，输入*号进行上传即可。
    </div>
    <div class="batch-btn" style="margin-bottom:50px">
      <el-button type="primary" plain @click="handleDownloadTemplateHeader" class="btn">下载模板</el-button>
      <el-upload
        ref="headerUpload"
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        :action="uploadHeaderAction"
        :show-file-list="false"
        :on-success="handleHeaderSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button type="primary" class="btn">上传模板</el-button>
      </el-upload>
    </div>
    <DividerHeader>批量订单修改----商品行字段</DividerHeader>
    <div class="batch-info">下载批量订单修改模板，将需要修改的订单号与行号填写，并维护对应字段，上传模板即可完成修改。
      支持修改商品行字段，包括数量、单价、首个交期、库位、客户物料编号、客户规格型号、客户物料名称、客户行号、客户物料数量、客户物料单位。
      若要清空某字段，输入*号进行上传即可。
    </div>
    <div class="batch-btn">
      <el-button type="primary" plain @click="handleDownload" class="btn">下载模板</el-button>
      <el-upload
        ref="upload"
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        :action="uploadItemAction"
        :show-file-list="false"
        :on-success="handleItemSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button type="primary" class="btn">上传模板</el-button>
      </el-upload>
    </div>
    <CustomerDateConfirmDlg :show-dialog.sync="showCustomerDateConfirmDlg" :fail-content="failContent" @submit="submitEditAgain" />
    <SupplierConfirmDlg :show-dialog.sync="showSupplierConfirmDlg" :fail-content="failContent" @submit="submitEditSupplierAgain" />
  </div>
</template>

<script>
import DividerHeader from '@/components/DividerHeader'
import CustomerDateConfirmDlg from './components/edit/CustomerDateConfirmDlg'
import SupplierConfirmDlg from './components/edit/SupplierConfirmDlg'
import qs from 'qs';

// import { excelUrls } from './constants'

export default {
  components: {
    DividerHeader,
    CustomerDateConfirmDlg,
    SupplierConfirmDlg
  },
  data () {
    return {
      loading: null,
      showCustomerDateConfirmDlg: false,
      showSupplierConfirmDlg: false,
      failContent: '',
      ignoreDate: false,
      totalList: [],
      ignoreDateIndex: '',
      refuseDateIndex: '',
      supplierDeliveryConflictParams: null,
      needValidateOrderNo: true
    }
  },
  computed: {
    excelUrls () {
      return this.$store.state.orderCommon.excelUrls
    },
    uploadHeaderAction () {
      let query = {
        needValidateOrderNo: this.needValidateOrderNo
      }
      return `/api-opc/v1/excel/batchUpdateSoHeader?${qs.stringify(query)}`
    },
    uploadItemAction () {
      let query = {
        ignoreDateIndex: '',
        refuseDateIndex: '',
        hasCreateIndex: ''
      };
      if (this.ignoreDate) {
        query.ignoreDateIndex = this.ignoreDateIndex;
        query.refuseDateIndex = this.refuseDateIndex;
        query.hasCreateIndex = this.totalList.filter(item => item.resStatus === 'success').map(item => item.orderIndex).join(',')
      }
      if (this.supplierDeliveryConflictParams) {
        query = {
          ...query,
          ...this.supplierDeliveryConflictParams
        }
      }
      return `/api-opc/v1/excel/batchUpdateSoItem?${qs.stringify(query)}`
    }
  },
  methods: {
    handleDownloadTemplateHeader () {
      // to-do otd4灰度环境匹配的模板，后续要删掉
      // const isGray = window.location.href.includes('boss-gray') || window.location.href.includes('boss-uat-4')
      // const url = isGray ? this.excelUrls.batchTemplateHeaderGray : this.excelUrls.batchTemplateHeader
      // window.open(url)
      window.open(this.excelUrls.batchTemplateHeader)
    },
    handleDownload () {
      // to-do otd4灰度环境匹配的模板，后续要删掉
      // const isGray = window.location.href.includes('boss-gray') || window.location.href.includes('boss-uat-4')
      // const url = isGray ? this.excelUrls.batchTemplateLineGray : this.excelUrls.batchTemplateLine
      // window.open(url)
      window.open(this.excelUrls.batchTemplateLine)
    },
     handleHeaderSuccess (res) {
      if (this.loading) {
        this.loading.close()
      }
      let msg = ''
      if (res && res.code === 200) {
        const { data: { successSapOrderNoList: successInfoList, failSapOrderNoList: failInfoList } } = res
        msg = `成功${successInfoList.length}单，失败${failInfoList.length}单。`
        if (failInfoList.length > 0) {
          const failMsgList = failInfoList.map(f => `失败原因：${f}`)
          msg += `
            失败信息如下：
            ${failMsgList.join('<br>')}`
          }
        this.$alert(msg, '结果', {
          customClass: 'custom-return-error',
          dangerouslyUseHTMLString: true
        })
        setTimeout(() => {
          try {
            this.$refs.headerUpload.clearFiles()
          } catch (error) {
            console.log(error)
          }
        }, 800)
      } else if (res && res.code === 2000104) {
        this.$confirm(res.msg, '操作提示', {
          type: 'warning',
          confirmButtonText: '确认'
        }).then(() => {
          this.needValidateOrderNo = false
          let action = () => {
            const length = this.$refs.headerUpload.uploadFiles.length - 1
            this.$refs.headerUpload.uploadFiles[length].status = 'ready'
            this.$refs.headerUpload.submit()
          }
          setTimeout(action, 200)
        }).catch(err => console.log(err))
      } else {
        msg = res ? res.msg : '上传错误'
        this.$alert(msg, '错误', {
          customClass: 'custom-return-error',
          dangerouslyUseHTMLString: true,
          type: 'error'
        })
      }
    },
    handleItemSuccess (res) {
      if (this.loading) {
        this.loading.close()
      }
      this.supplierDeliveryConflictParams = null
      let msg = ''
      if (res && res.code === 200) {
        const { data } = res
        this.ignoreDate = false
        this.totalList = [
          ...(data.successList || []).map(item => ({
            resStatus: 'success',
            ...item
          })),
          ...(data.failList || []).map(item => ({
            resStatus: 'fail',
            ...item
          }))].filter(Boolean);
        if (data && Array.isArray(data.failList) && data.failList.length) {
          this.hasCreateList = data.failList.map(item => item.orderIndex).join(',')
          let content = `成功${data.successCount || 0}单，失败${data.failCount || 0}单。<br/>`
          try {
            content += data.failList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组` : ''}订单的失败原因：<br/>` + item.msgList.join('<br/>')).join('<br/>')
          } catch (err) {
            console.log(err)
          }
          const contentMsg =
            `
            <div style="max-height: 300px;overflow: auto">
            <pre>${content}</pre>
            </div>
            `
          // 交期二次确认
          // if (data.highestPriorityFailReason === 'confirm') {
          //   this.showCustomerDateConfirmDlg = true;
          //   this.failContent = contentMsg;
          //   return;
          // }
          // 直发二次确认
          if (data.highestPriorityFailReason === 'supplierDeliveryConflict') {
            this.showSupplierConfirmDlg = true;
            this.failContent = contentMsg;
            this.supplierDeliveryConflictParams = {
              ...(data?.preRequestParamMap || {}),
              acceptSupplierDirectIndex: data.failList.map((a) => a.orderIndex).join(',')
            }
            return;
          }
          this.$alert(contentMsg, '结果', {
            dangerouslyUseHTMLString: true
          })
        } else if (res.data.failSapOrderNoList && Array.isArray(res.data.failSapOrderNoList) && res.data.failSapOrderNoList.length) {
          let content = res.data.failSapOrderNoList.join('<br/>')
          const contentMsg =
            `
            <div style="max-height: 300px;overflow: auto">
            <pre>${content}</pre>
            </div>
            `
          this.$alert(contentMsg, '结果', {
            dangerouslyUseHTMLString: true
          })
        } else if (res.data.failCount === 0 && res.data.successCount) {
          let msg = ''
          if (res.data.successList && res.data.successList.length) {
            msg += res.data.successList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组` : ''}订单：<br/>` + item.msgList.join('<br/>')).join('<br/>')
          } else {
            msg = res.msg
          }
          const content =
          `
          <div style="max-height: 300px;overflow: auto">
          ${msg}
          </div>
          `
          this.$alert(content, '操作提示', {
            type: 'success',
            dangerouslyUseHTMLString: true
           })
        }
        setTimeout(() => {
          try {
            this.$refs.upload.clearFiles()
          } catch (error) {
            console.log(error)
          }
        }, 800)
      } else {
        msg = res ? res.msg : '上传错误'
        this.$alert(msg, '错误', {
          customClass: 'custom-return-error',
          dangerouslyUseHTMLString: true,
          type: 'error'
        })
      }
    },
    submitEditAgain(type) {
      try {
        this.ignoreDate = true
        const indexStr = this.totalList.map(item => item.orderIndex).join(',')
        if (type === 'refuse') {
          this.refuseDateIndex = indexStr;
          this.ignoreDateIndex = ''
        } else {
          this.ignoreDateIndex = indexStr
          this.refuseDateIndex = '';
        }
        let action = () => {
          const length = this.$refs.upload.uploadFiles.length - 1
          this.$refs.upload.uploadFiles[length].status = 'ready'
          this.$refs.upload.submit()
        }
        setTimeout(action, 200)
      } catch (err) {
        console.log(err)
      } finally {
        this.showCustomerDateConfirmDlg = false;
      }
    },
     submitEditSupplierAgain(type) {
      try {
        if (type === 'cancel') {
          this.supplierDeliveryConflictParams = null
          return
        }
        const indexStr = this.totalList.map(item => item.orderIndex).join(',')
        if (type === 'comfirm') {
          this.refuseDateIndex = indexStr;
        }
        let action = () => {
          const length = this.$refs.upload.uploadFiles.length - 1
          this.$refs.upload.uploadFiles[length].status = 'ready'
          this.$refs.upload.submit()
        }
        setTimeout(action, 200)
      } catch (err) {
        console.log(err)
      } finally {
        this.showSupplierConfirmDlg = false;
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false
      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleError () {
      if (this.loading) {
        this.loading.close()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.batch {
  .batch-info {
    width: 80%;
    padding: 10px;
    margin-left: 10px;
    line-height: 1.6;
  }
  .batch-btn {
    margin: 0 20px;
    .btn {
      width: 100px;
    }
  }
}
</style>
