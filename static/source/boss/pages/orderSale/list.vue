<template>
  <div class="app-container order-list-components">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
      >
        <el-row>
          <el-col :span="16">
            <el-form-item label="订单号检索：" prop="voucherNos">
              <el-input
                v-model="searchForm.voucherNos"
                placeholder="外围\OMS\SAP\客户参考号 均支持搜索，同时支持10个单号，以空格分隔"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户名称：" prop="customerNo">
              <el-select
                v-model="searchForm.customerNo"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="customerNoLoading"
                clearable>
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="附件状态：" prop="upStatus">
              <el-select
                clearable
                v-model="searchForm.upStatus"
                placeholder="请选择"
                @change="handleFilter"
              >
                <el-option
                  v-for="item in upStatusOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客服人员：" prop="customerServiceName">
              <el-input
                v-model.trim="searchForm.customerServiceName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="销售人员：" prop="sellerName">
              <el-input
                v-model.trim="searchForm.sellerName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="订单类型：" prop="orderTypes">
              <el-select
                clearable
                v-model="searchForm.orderTypes"
                multiple
                :multiple-limit="multipleLimit"
                placeholder="请选择"
                @change="handleFilter"
              >
                <el-option
                  v-for="item in orderTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单来源：" prop="orderSourceSet">
              <el-select
                v-model="searchForm.orderSourceSet"
                clearable
                multiple
                placeholder="请选择">
                <el-option
                  v-for="item in orderSourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否自主下单：" prop="autoOrder">
              <el-select
                v-model="searchForm.autoOrder"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in autoOrderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="状态：" prop="soFreeze">
              <el-select
                v-model="searchForm.soFreeze"
                clearable
                placeholder="请选择">
                <el-option label="有交货冻结" value="DELIVERY_FREEZE"></el-option>
                <el-option label="有信用额度冻结" value="CREDIT_FREEZE"></el-option>
                <el-option label="有信用逾期冻结" value="OVERDUE_FREEZE"></el-option>
                <el-option label="超期未开冻结" value="TIMEOUT_UNINVOICED_FREEZE"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交货冻结原因：" prop="deliveryFreezeReasons">
              <el-select
                v-model="searchForm.deliveryFreezeReasons"
                clearable
                multiple
                placeholder="请选择">
                <el-option
                  v-for="item in deliveryFreezeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发货状态：" prop="deliveryStatusList">
              <el-select
                v-model="searchForm.deliveryStatusList"
                multiple
                clearable
                placeholder="请选择">
                <el-option label="未发货" value="NOT_DELIVERY"></el-option>
                <el-option label="部分发货" value="PART_DELIVERY"></el-option>
                <el-option label="全部发货" value="ALL_DELIVERY"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票状态：" prop="invoiceStatusList">
              <el-select
                v-model="searchForm.invoiceStatusList"
                multiple
                clearable
                placeholder="请选择">
                <el-option label="未开票" value="NO"></el-option>
                <el-option label="部分开票" value="PART"></el-option>
                <el-option label="全部开票" value="ALL"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下发SAP状态：" prop="requestSapStatus">
              <el-select
                v-model="searchForm.requestSapStatus"
                clearable
                placeholder="请选择">
                <el-option label="处理中" value="SENT"></el-option>
                <el-option label="失败" value="FAIL"></el-option>
                <el-option label="成功" value="SUCCESS"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col  :span="8">
            <el-form-item label="订单联系人姓名">
              <el-input v-model.trim="searchForm.orderContactName" placeholder="请输入"  clearable></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="16">
            <el-form-item label="订单创建日期：" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                align="right"
                style="width:100%"
                unlink-panels
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单是否取消：" prop="allCancel">
              <el-select
                v-model="searchForm.allCancel"
                clearable
                placeholder="请选择">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="预订单：" prop="preOrderTagList">
              <el-select
                v-model="searchForm.preOrderTagList"
                placeholder="请选择"
                clearable
                multiple
              >
                <el-option label="否" value="NO_PRE_ORDER"></el-option>
                <el-option label="审批中" value="PRE_ORDER"></el-option>
                <el-option label="审批通过" value="PRE_ORDER_APPROVE"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="后补订单：" prop="backupOrder">
              <el-select
                v-model="searchForm.backupOrder"
                clearable
                placeholder="请选择">
                <el-option label="是" :value="true"></el-option>
                <el-option label="否" :value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="2">
            <el-button
              class="filterBtn"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
              >查询
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row type="flex" justify="space-between" class="orderListTop">
      <el-col :span="12">
        <span>列表内订单总数：{{ total }}</span>
      </el-col>
      <el-col :span="12">
        <div style="display:inline-block;margin-right:10px">
          <el-link type="primary" @click="toDownLoadList" style="margin-right: 10px">下载专区</el-link>
          <el-button type="primary" @click="handleOpenCreateCustomerDlg">创建客户订单</el-button>
          <el-tooltip v-if="!multipleSections.length" content="请勾选5个以内订单上传，文件小于10M" placement="top">
            <el-button :disabled="!multipleSections.length" type="primary" plain>
              批量上传文件
              <i class="el-icon-upload el-icon--right" />
            </el-button>
          </el-tooltip>
          <el-popover v-else placement="top" width="120" trigger="click" >
            <div class="content" style="display: flex;flex-direction: column;">
              <el-button type="primary" plain size="mini" @click="showUploadDialog(multipleSections,'general', 'existBatchOrder')">订单附件</el-button> <br />
            <el-button type="primary" plain size="mini" @click="showUploadDialog(multipleSections,'other', 'existBatchOrder')">其他</el-button>
            </div>
            <el-button type="primary" plain size="mini" :disabled="!multipleSections.length"  slot="reference">
              批量上传文件
              <i class="el-icon-upload el-icon--right" />
            </el-button>
          </el-popover>
        </div>
        <el-button type="primary" @click="handleOpenBatchCreateDlg" plain>批量创建订单</el-button>
        <el-button type="primary" @click="handleOpenCreateDlg">创建订单</el-button>
      </el-col>
    </el-row>
    <el-table ref="multipleTable" v-loading="listLoading"
      :data="list" border fit
      highlight-current-row height="500"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <el-table-column label="OMS订单号" min-width="100px" align="center" prop="soNo" />
      <el-table-column label="外围订单号" min-width="100px" align="center" prop="orderNo" />
      <el-table-column label="SAP订单号" min-width="100px" align="center" prop="sapOrderNo" />
      <el-table-column label="订单类型" width="100px" align="center" prop="type" />
      <el-table-column label="是否整单" width="100px" align="center" prop="wholeOrder" >
        <template slot-scope="{ row }">
          <span :class="{ 'success-color': row.wholeOrder == '是' }" >
            {{ row.wholeOrder }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="客户名称" min-width="100px" align="center" prop="customerName" />
      <el-table-column label="发货状态" min-width="100px" align="center" prop="deliveryStatusDisplay" />
      <el-table-column label="开票状态" min-width="100px" align="center" prop="invoiceStatusDisplay" />
      <el-table-column label="下发SAP状态" min-width="100px" align="center" prop="requestSapStatusDisplay" >
      <template slot-scope="scope">
        <div>
          <i v-if="scope.row.requestSapStatusDisplay == '失败'" style="color:red" class="el-icon-warning"></i>
          <span>{{scope.row.requestSapStatusDisplay}}</span>
        </div>
      </template>
      </el-table-column>
      <el-table-column label="客户订单号" min-width="100px" align="center" prop="customerReferenceNo" />
      <el-table-column label="订单来源" width="100px" align="center" prop="orderSource" />
      <el-table-column label="销售" width="80px" align="center" prop="sellerName" />
      <el-table-column label="客服" width="80px" align="center" prop="customerServiceName" />
      <el-table-column label="创建时间" width="100px" align="center" prop="sapAddDate" />
      <el-table-column label="操作" align="center" width="130" fixed="right" class-name="small-padding">
        <template slot-scope="{ row }">
          <el-button type="text" size="mini" @click="toDtl(row)" >查看详情</el-button>
          <br />
          <el-popover placement="top" width="120" trigger="click" >
            <div class="content" style="display: flex;flex-direction: column;">
            <el-button type="primary" plain size="mini" @click="showUploadDialog(row,'general', 'existOrder')">订单附件</el-button> <br />
            <el-button type="primary" plain size="mini" @click="showUploadDialog(row,'other', 'existOrder')">其他</el-button>
            </div>
            <el-button type="primary" plain size="mini" :disabled="!row.sapOrderNo"  slot="reference">
              {{ row.hasAppendix | uploadTxtFilter }}
              <i class="el-icon-upload el-icon--right" />
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
    <!-- 创建订单的选择公司类型弹框 -->
    <CreateOrderDialog :show-dialog.sync="createOrder" />
    <CreateCustomerOrderDialog :show-dialog.sync="createCustomerOrder" />
    <CreateBatchOrderDialog v-if="createBatchOrder" :show-dialog.sync="createBatchOrder" />
    <el-dialog
      title="上传成功"
      :visible.sync="uploadDialogVisible">
      <span>可进入订单详情管理电子文档</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="noMoreShown">不再显示</el-button>
        <el-button @click="uploadDialogVisible = false">了解</el-button>
        <el-button type="primary" @click="manageDocs">文档管理</el-button>
      </span>
    </el-dialog>
    <el-dialog title="上传附件" :visible.sync="uploadDialogStatus" :before-close="handleUploadClose" :show-close="false">
      <div v-loading="uploadLoading" class="dialog-body" style="display:flex;align-items: center;flex-direction:column">
        <!-- <span v-if="uploadType === 'general'" style="margin:10px;color:#597bee">
          可将文件直接拖拽到改区域、或者点击上传按钮，仅支持PDF附件哟
        </span> -->
        <span style="margin:10px;color:#597bee">
          可将文件直接拖拽到该区域，或者点击上传按钮
        </span>
        <el-upload ref="uploadDialog" action="/ali-upload"
          style="display: inline-block;" drag
          :show-file-list="true" multiple
          :with-credentials="true" :limit="5"
          :data="{appName: omsAppName}"
          :on-success="handleUploadSucess"
          :on-remove="handleUploadRemove"
          :on-error="handleUploadError"
          :accept="acceptFileType.commonType"
          :before-upload="handleBeforeUpload"
          :on-exceed="handleUploadExceed"
          >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text"><em>点击或拖拽上传</em></div>
        </el-upload>
        <div v-if="uploadType === 'general'" style="display:flex; align-items: center;margin: 10px;">
          <span>打印方向</span>
          <el-select v-model="printDirection" style="width: 120px;margin-left: 10px;">
            <el-option value="cross" label="横向"></el-option>
            <el-option value="vertical" label="纵向"></el-option>
          </el-select>
        </div>
        <span slot="footer" class="dialog-footer" style="align-self:flex-end;margin-top:20px;">
          <el-button @click="cancelUpload">取消</el-button>
          <el-button @click="submitUpload" type="primary">提交</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { orderList } from '@/api/ecorp'
import { goodsCustomerListSearch } from '@/api/insteadOrder.js'
import * as sellOrder from '@/api/orderSale'
import CreateOrderDialog from './components/list/CreateOrderDlg'
import CreateCustomerOrderDialog from './components/list/CreateCustomerOrderDlg'
import CreateBatchOrderDialog from './components/list/BatchCreateDlg'
import { downloadFile } from '@/utility/request.js'
import { spDebounce, getSearchDataFromUrlQuery } from '@/utils/index.js'
import moment from 'moment'
import _ from 'lodash';
// import { run, destroy } from '@/utils/dify';
// import { delete } from 'vue/types/umd'

const prefixFront = '/oms-new'
const upStatusOption = [
  { label: '已上传附件', value: '1' },
  { label: '未上传附件', value: '0' }
]

export default {
  name: 'OrderList',
  components: {
    Pagination,
    CreateOrderDialog,
    CreateCustomerOrderDialog,
    CreateBatchOrderDialog
  },
  filters: {
    uploadTxtFilter (val) {
      if (val === '1') { return '继续上传' } else {
        return '上传文档'
      }
    }
  },
  data () {
    // var checkDateRange = (rule, value, callback) => {
    //   const createTimeFrom = value[0]
    //   const createTimeTo = value[1]
    //   if (
    //     moment(createTimeTo)
    //       .subtract(3, 'month')
    //       .isAfter(createTimeFrom)
    //   ) {
    //     this.validFlag = false
    //     callback(new Error('时间段只能支持3个月内，请重新选择'))
    //   } else {
    //     this.validFlag = true
    //     callback()
    //   }
    // }
    return {
      omsAppName: window.omsAppName,
      uploadType: '',
      printDirection: 'cross',
      uploadLoading: false,
      returnMsg: '',
      uploadList: [],
      uploadDialogStatus: false,
      isBatching: false,
      multipleSections: [],
      responseList: [],
      componentsName: 'fomalOrder',
      fileSizeLimit: 40, // 上传文件大小限制，单位 MB
      fileName: '', // 上传文件名
      listLoading: false,
      customerNoOptions: [],
      customerNoLoading: false,
      searchForm: {
        voucherNos: '',
        customer: '',
        upStatus: '',
        orderTypes: [],
        dateRange: '', // 日期区间（数组）
        startDate: '',
        endDate: '',
        customerNo: '', // 客户订单号
        customService: '', // 客服
        customServiceManager: '', // 客服经理
        sellerName: '', // 销售人员
        customerServiceName: '', // 销售人员
        orderSource: '',
        orderSourceSet: [],
        autoOrder: '',
        soFreeze: '',
        deliveryFreezeReasons: '',
        creditFreeze: '',
        deliveryStatusList: [],
        invoiceStatusList: [],
        requestSapStatus: '', // 下发SAP状态,
        orderContactName: '',
        allCancel: '',
        backupOrder: '',
        preOrderTagList: []
      },
      formParam: {},
      validFlag: true, // 表单验证 true-通过，false-未通过
      list: [],
      total: 0,
      attachmentTotal: 0,
      noAttachmentTotal: 0,
      listQuery: {
        page: 1,
        size: 20
      },
      upStatusOption,
      autoOrderOptions: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ],
      rules: {
        // dateRange: [ { validator: checkDateRange, trigger: 'blur' } ]
      },
      multipleLimit: 5,
      selectCompany: {
        name: '',
        type: ''
      },
      createOrder: false,
      createCustomerOrder: false,
      uploadDialogVisible: false,
      uploadDialogData: {},
      createBatchOrder: false,
      docUploadScene: '' // 文件上传场景，用于埋点
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    acceptFileType () {
      return this.$store.state.orderCommon.acceptFileType || {}
    },
    orderTypeOption () {
      return this.dictList && this.dictList['soCategory'] ? this.dictList['soCategory'].map(item => {
        return { label: item.name, value: item.code }
      }) : []
    },
    deliveryFreezeOption () {
      let arr = this.dictList && this.dictList['deliveryFreeze']?.map(item => {
        return { label: item.name, value: item.code }
      })
      .filter(item => ['26', '28', '30', '31', '99'].includes(item.value))
      arr && arr.push({ label: '其他', value: 'other' })
      arr = _.orderBy(arr, item => ['99', '30', '31', '26', '28', 'other'].indexOf(item.value))
      return arr || []
    },
    orderSourceOptions () {
      return this.dictList.orderSource?.filter(item => item.status === 'normal' && item.supportQuery === '1')?.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.code
        }
      }) || []
    }
  },
  mounted() {
    // run('mdwCcLAR2F2uWY9g');
  },
  activated() {
    // run('mdwCcLAR2F2uWY9g');
  },
  unmounted() {
    // destroy();
  },
  deactivated() {
    // destroy();
  },
  created () {
    this.componentsName = this.$route.name
    const query = JSON.parse(localStorage.getItem('/orderSale/formal')) || {}
    // 使用初始查询条件查询一次后需要清除该初始条件
    // setItem 参见 router/index.js > beforeEach
    localStorage.removeItem('/orderSale/formal')
    // const query = this.$route.query
    this.searchForm = {
      ...this.searchForm,
      ...this.transformRouteQuery(query)
    }

    this.getList()
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    this.throwCheckMsg = spDebounce(this.throwCheckMsg)
  },
  methods: {
    transformRouteQuery (urlQuery) {
      const query = getSearchDataFromUrlQuery(urlQuery);
      const { startDate, endDate } = query;
      if (startDate && endDate) {
        query.dateRange = [startDate, endDate];
      }
      return {
        ...query,
        allCancel: query.allCancel ? query.allCancel === 'true' : '',
        backupOrder: query.backupOrder ? query.backupOrder === 'true' : ''
      }
    },
    // 跳转下载专区
    toDownLoadList () {
      try {
        this.$closeTag('/purchaseReport/downLoadList')
        this.$router.push('/purchaseReport/downLoadList');
      } catch {}
    },
    throwCheckMsg (callback) {
      callback && callback()
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      if (this.uploadList.some((item) => item.name === file.name)) {
        this.$message.error({
          message: '文件已存在'
        });
        return false;
      }
      const size = file.size / 1024 / 1024
      const isGtLimit = size > this.fileSizeLimit
      // const isPDF = /pdf/i.test(file.name)
      let pass = true
      if (isGtLimit) {
        pass = false
        this.returnMsg += `【${file.name}】大小：${size}M，上传文件不能超过` + this.fileSizeLimit + 'MB！<br/>'
      }
      // if (this.uploadType === 'general' && !isPDF) {
      //   pass = false
      //   this.returnMsg += `【${file.name}】不是pdf文件类型，只能上传PDF文件！<br/>`
      // }
      if (!pass) {
        this.throwCheckMsg(() => {
          this.$message({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: this.returnMsg
          })
          this.returnMsg = ''
        })
      }
      return pass
    },
    saveFileInfo (sapOrderNo, docMetaDataList = []) {
      const queryData = {
        source: 'BOSS',
        dimension: 'order',
        docUploadScene: this.docUploadScene,
        businessId: sapOrderNo,
        docMetaDataList
      }
      return sellOrder.saveDocumentMetaData(queryData)
    },
    resetUploadData () {
      this.uploadDialogStatus = false
      this.uploadListRows = []
      this.uploadList = []
      this.$refs.uploadDialog && this.$refs.uploadDialog.clearFiles()
      this.$refs.multipleTable.clearSelection()
    },
    cancelUpload () {
      this.resetUploadData()
    },
    async submitUpload () {
      if (this.uploadList.filter(file => file.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      const uploadListRows = this.uploadListRows
      const uploadList = this.uploadList.map(upload => ({
        bucketName: upload.bucketName,
        fileName: upload.name,
        ossKey: upload.ossKey,
        attachmentType: this.uploadType,
        upUserName: window.CUR_DATA.user && window.CUR_DATA.user.name,
        printDirection: this.uploadType === 'general' ? this.printDirection : '',
        uploadTime: moment(new Date()).format('yyyy-MM-DD HH:mm:ss')
      }))
      let responseList = []
      if (!uploadList.length) return this.$message.error('请上传文件！')
      this.uploadLoading = true
      for (let row of uploadListRows) {
        const res = await this.saveFileInfo(row.sapOrderNo, uploadList)
        res.businessId = row.sapOrderNo
        responseList.push(res)
      }
      this.uploadLoading = false
      console.log(responseList)
      if (uploadListRows.length > 1) {
        this.toConfirmBatch(responseList)
      } else {
        if (responseList[0] && responseList[0].code !== 200) {
          this.$message.error(responseList[0].msg || responseList[0].message || '上传失败！')
        } else {
          this.toConfirm(uploadListRows[0].sapOrderNo, uploadListRows[0].id)
        }
      }
      this.resetUploadData()
    },
    handleUploadClose (done) {
      this.cancelUpload()
      done && done()
    },
    showUploadDialog (row, type, docUploadScene) {
      this.uploadType = type || this.uploadType
      this.docUploadScene = docUploadScene
      this.uploadListRows = Array.isArray(row) ? row : [row]
      this.uploadListRows = this.uploadListRows.filter(row => row.sapOrderNo)
      this.uploadDialogStatus = true
    },
    handleUploadSucess (res, file, fileList) {
      this.uploadList = fileList.map(file => ({
        ...file,
        fileName: file.response && file.response[0] && file.response[0].name,
        ossKey: file.response && file.response[0] && file.response[0].objectKey,
        bucketName: file.response && file.response[0] && file.response[0].bucketName
      }))
      console.log(this.uploadList)
    },
    handleUploadRemove (file, fileList) {
      this.uploadList = fileList.map(file => ({
        ...file,
        fileName: file.response && file.response[0] && file.response[0].name,
        ossKey: file.response && file.response[0] && file.response[0].objectKey,
        bucketName: file.response && file.response[0] && file.response[0].bucketName
      }))
    },
    handleUploadError (error) {
      this.$message.error(error.error || error.message || '上传失败！')
    },
    handleUploadExceed (error) {
      console.log(error)
      this.$message.error(error.error || error.message || '文件最多上传5个！')
    },
    queryCustomerNo (querystring, callback) {
      sellOrder.queryCustomerNo()
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        goodsCustomerListSearch({
          customer_name: key,
          pageSize: 100
        }).then(res => {
          this.customerNoLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.customerNoOptions = res.data.contents.map(item => {
                return {
                  value: item.customerNo,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    },
    getList () {
      this.listLoading = true
      this.dealFormParam()
      const param = {
        ...this.formParam,
        current: this.listQuery.page,
        pageSize: this.listQuery.size
      }
      orderList(param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list = response.data
            this.total = response.totalCount || 0
            // this.hasDocCnt = response.data.hasDocCnt || 0
            // this.noDocCnt = response.data.noDocCnt || 0
          } else {
            this.$notify.error(response.msg)
            // 报错时清空数据
            this.list = []
            this.total = 0
            // this.hasDocCnt = 0
            // this.noDocCnt = 0
          }
          this.listLoading = false
        })
        .catch(e => {
          this.listLoading = false
        })
    },
    handleFilter () {
      if (this.validFlag) {
        this.listQuery.page = 1
        this.getList()
      }
    },
    // 处理搜索表单数据（去除日期区间dateRange，避免后台接收参数时报错）
    dealFormParam () {
      // 1.去除日期区间dateRange，避免后台接收参数时报错
      if (this.searchForm.dateRange === null) {
        this.searchForm.dateRange = ''
      }
      this.searchForm.startDate = this.searchForm.dateRange[0]
      this.searchForm.endDate = this.searchForm.dateRange[1]
      this.formParam = JSON.parse(JSON.stringify(this.searchForm))

      delete this.formParam.dateRange

      this.formParam.voucherNos =
        this.formParam.voucherNos
          .split(/\s|,|;|，|；/)
          .filter(function (s) {
            return s && s.trim()
          })
      // if (!this.formParam.deliveryStatus) {
      //   delete this.formParam.deliveryStatus
      // }
      // if (!this.formParam.invoiceStatus) {
      //   delete this.formParam.invoiceStatus
      // }
      if (!this.formParam.requestSapStatus) {
        delete this.formParam.requestSapStatus
      }
      if (!this.formParam.soFreeze) {
        delete this.formParam.soFreeze
      }
      if (!this.formParam.creditFreeze) {
        delete this.formParam.creditFreeze
      }
      this.formParam.deliveryFreezeReasons = this.searchForm.deliveryFreezeReasons
        ? this.searchForm.deliveryFreezeReasons.join(',')
        : ''
      // 2.多选订单类型orderTypes数组改为逗号分隔
      // this.formParam.orderTypes =
      //   this.searchForm.orderTypes && this.searchForm.orderTypes.join(',')
    },
    // 跳转到订单详情
    toDtl (row) {
      const { soNo, sapOrderNo, type, orderNo } = row
      if (type !== 'ZQT1') {
        this.$router.jumpToSoOrderDetail({
          tagName: `${sapOrderNo || ''}订单`,
          query: {
            soNo: soNo,
            orderNo,
            sapOrderNo: sapOrderNo,
            id: row.id,
            refresh: true
          }
        })
      } else {
        this.$alert('暂不支持查看', '提醒', {
          type: 'warning'
        })
      }
    },
    handleSelectionChange (sections) {
      this.multipleSections = sections
    },
    toConfirmBatch (message) {
      console.log(message)
      let success = message.every(msg => msg.code === 200)
      let part = message.some(msg => msg.code === 200)//eslint-disable-line
      let fail = message.every(msg => msg.code !== 200)
      let statusMap = { part: '部分成功', success: '操作成功', fail: '操作失败' }
      let status = ''
      if (success) {
        status = statusMap.success
      } else if (fail) {
        status = statusMap.fail
      } else {
        status = statusMap.part
      }
      let msg = `
      <p>
    ${message
    .map(x => `订单号${x.businessId}   : 上传${x.code === 200 ? '成功' : '失败: '} ${x.code === 200 ? '' : x.msg}`)
    .map(x => `<p>${x}</p>`).join('')}
      </p>
      `
      this.$alert(msg, status, {
        confirmButtonText: success ? '确定' : '重试',
        dangerouslyUseHTMLString: true
      })
    },
    noMoreShown () {
      this.uploadDialogVisible = false
      this.setNoMoreShown()
    },
    setNoMoreShown () {
      localStorage.setItem('uploadNoMoreShown', true)
    },
    getNoMoreShown () {
      return localStorage.getItem('uploadNoMoreShown') === 'true'
    },
    manageDocs () {
      this.uploadDialogVisible = false
      let { voucherNo, id } = this.uploadDialogData
      this.$router.jumpToSoOrderDetail({
        query: { sapOrderNo: voucherNo, id, refresh: true },
        hash: { point: 'dz' }
      })
    },
    toConfirm (voucherNo, id) {
      console.log(voucherNo, id)
      if (!this.getNoMoreShown()) {
        this.uploadDialogVisible = true
        this.uploadDialogData = { voucherNo, id }
      } else {
        this.$message.success('上传成功！')
      }
    },
    // 列表导出
    exportList () {
      this.dealFormParam()
      const param = { ...this.formParam }
      let exportUrl = `${prefixFront}/orderList/export/excel?`
      for (var key in param) {
        exportUrl += key + '=' + param[key] + '&'
      }
      downloadFile(exportUrl)
    },
    handleOpenCreateDlg () {
      sellOrder.checkPermission().then(res => {
        if (res && res.code === 200) {
          this.createOrder = true
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    },
    handleOpenCreateCustomerDlg () {
      sellOrder.checkPermission().then(res => {
        if (res && res.code === 200) {
          this.createCustomerOrder = true
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    },
    handleOpenBatchCreateDlg () {
      sellOrder.checkPermission().then(res => {
        if (res && res.code === 200) {
          this.createBatchOrder = true
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.orderListTop {
  margin: 20px 0 10px;

  .el-col:nth-child(1) {
    display: flex;
    align-items: center;

    span {
      margin: 0 10px;
      color: #909399;
    }
  }

  .el-col:nth-child(2) {
    text-align: right;
  }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}

.el-autocomplete,
.el-select {
  width: 100%;
}

.filterBtn {
  float: right;
}
.el-row{
    display:flex;
    flex-wrap: wrap;
}
</style>
