<template>
  <el-dialog
    title="批量创建订单"
    :visible.sync="dlgVisible"
    width="800px"
    center
    @open="openDlg"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form :model="batchData" :rules="rules" label-width="170px">
      <el-form-item label="选择公司范围" prop="companyRange">
        <el-select v-model="batchData.companyRange" filterable placeholder="请选择公司范围">
          <el-option
            v-for="item in companyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="isSupportedCompany(item.code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择订单类型" prop="orderType">
        <el-select v-model="batchData.orderType" filterable placeholder="请选择订单类型">
          <el-option
            v-for="item in orderTypeList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="disabledBatchType(item.code)"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="整单是否接受系统日期" prop="refuseSystemDeliveryDate">
        <el-select clearable v-model="batchData.refuseSystemDeliveryDate" placeholder="请选择">
          <el-option key="X" label="是" value="X" />
          <el-option key="Z" label="否" value="Z" />
        </el-select>
      </el-form-item> -->
    </el-form>
    <div class="notice">*批量模板在上传后，将会直接生成订单，请确认信息无误后进行上传！上传后请在订单列表搜索客户来查询对应订单即可</div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:showDialog', false)">取 消</el-button>
      <el-button @click="handleDownload" type="primary" plain>下载批量创建模板</el-button>
      <el-upload
        ref="upload"
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        :action="uploadAction"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button type="primary" >上传批量创建模板</el-button>
      </el-upload>
      <el-upload
        ref="uploadRef"
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        :action="uploadActionDraft"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button v-if="isNormalBatchOrder" type="primary" >上传批量创建模板且只保存为草稿订单</el-button>
      </el-upload>
    </div>
    <el-dialog
      title="操作提示"
      :visible.sync="dialogVisible"
      append-to-body
      width="500px"
      :before-close="handleClose">
      <div class="tips-container" v-html="failContent"></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="downloadDetail">下载失败原因明细</el-button>
        <el-button type="default" @click="closeDialog">确 定</el-button>
        <!-- <el-button v-if="showCreateDraft" type="primary" @click="createCustomizeDraft">暂时创建草稿</el-button> -->
        <el-button v-if="showCreateOrderAgain" type="primary" @click="uploadAgain">继续建单</el-button>
      </span>
    </el-dialog>
    <CustomerDateConfirmDlg :show-dialog.sync="showCustomerDateConfirmDlg" :fail-content="failContent" @submit="createWorkOrder" />
  </el-dialog>

</template>
<script>
import { supportedCompanyList, supportedOrderTypeList, orderTypeToCompany } from '@/pages/orderSale/constants'
import qs from 'qs';
import { batchCreateSketch } from '@/api/orderSale';
import { mapState } from 'vuex';
import CustomerDateConfirmDlg from '@/pages/orderSale/components/edit/CustomerDateConfirmDlg'

export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    }
  },
  components: {
    CustomerDateConfirmDlg
  },
  data() {
    return {
      rules: {
        companyRange: [
          { required: true, message: '请选择公司范围', trigger: 'change' }
        ],
        orderType: [
          { required: true, message: '请选择订单类型', trigger: 'change' }
        ]
      },
      batchData: {
        companyRange: '1000',
        orderType: 'Z001'
      },
      companyRangeList: [], // 公司范围列表
      loading: null,
      ignoreDate: false,
      dialogVisible: false,
      hasCreateList: '',
      totalList: [],
      failExcelUrl: '',
      failContent: '',
      createDraft: false,
      showCreateOrderAgain: false,
      showCreateDraft: true,
      needCustomerRelCheck: true,
      createDraftQuery: {},
      supplierDeliveryConflictParams: null, // 接受供应商直发冲突
      showCustomerDateConfirmDlg: false,
      ignoreDateIndex: '', // 已确认(接受标期),继续创建的组号
      refuseDateIndex: '', // 不接受标期,继续创建的组号
      createWorkListIndex: '', // 保存草稿并触发协议价审批的组号
      createSketchIndex: '', // 保存草稿的组号
      uploadResData: {}, // 第一次上传的结果
      showPriorityFailReasonIndex: 0, // 按照优先级弹窗的序号,
      sketchOrderNoList: '' // 上传失败后保存草稿的草稿单号，用于跳转草稿列表
    }
  },
  created() {
    this.initValue()
  },
  computed: {
    ...mapState({
      customizeOrder: state => state.orderCommon.customizeOrder
    }),
    dlgVisible: {
      get() {
        return this.showDialog
      },
      set(val) {
        this.$emit('update:showDialog', val)
      }
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    excelUrls() {
      return this.$store.state.orderCommon.excelUrls
    },
    companyList() {
      return this.dictList &&
        this.dictList['companyScope']
        ? this.dictList['companyScope'].filter(item =>
          supportedCompanyList.indexOf(item.code) > -1)
        : []
    },
    orderTypeList() {
      return this.dictList &&
        this.dictList['soCategory']
        ? this.dictList['soCategory'].filter(item => {
          return supportedOrderTypeList.indexOf(item.code) >= 0 && item.code !== 'Z013'
        })
        : []
    },
    isNormalBatchOrder() {
      return /Z001|Z005|Z007|Z008|Z012/gim.test(this.batchData.orderType)
    },
    uploadAction () {
      const orderType = this.batchData.orderType
      const company = this.batchData.companyRange
      const refuseSDD = this.batchData.refuseSystemDeliveryDate
      let query = {
        orderType,
        company
        // needCustomerRelCheck: this.needCustomerRelCheck
      }
      if (refuseSDD === 'X') {
        query.refuseSDD = refuseSDD
      }
      // if (this.createDraft && this.createDraftQuery) {
      //   console.log(qs.stringify(this.createDraftQuery))
      //   return `/api-opc/v4/oms/order/batchCreateSketchV2?${qs.stringify(this.createDraftQuery)}`
      // }
      query.createWorkListIndex = this.createWorkListIndex;
      query.createSketchIndex = this.createSketchIndex;
      query.ignoreDateIndex = ''
      query.refuseDateIndex = ''
      query.hasCreateIndex = ''
      if (this.ignoreDate) {
        query.ignoreDateIndex = this.ignoreDateIndex;
        query.refuseDateIndex = this.refuseDateIndex;
        query.hasCreateIndex = this.totalList.filter(item => item.resStatus === 'success').map(item => item.orderIndex).join(',')
      }
      if (this.supplierDeliveryConflictParams) {
        query = {
          ...query,
          ...this.supplierDeliveryConflictParams
        }
      }

      if (this.isNormalBatchOrder) {
        return `/api-opc/v4/oms/order/batchCreateSo/v2?${qs.stringify(query)}`
      }
      return `/api-opc/v1/excel/batchImportSo?${qs.stringify(query)}`
    },
    uploadActionDraft () {
      const orderType = this.batchData.orderType
      const company = this.batchData.companyRange
      const refuseSDD = this.batchData.refuseSystemDeliveryDate
      let query = {
        orderType,
        company
        // needCustomerRelCheck: this.needCustomerRelCheck
      }
      if (refuseSDD === 'X') {
        query.refuseSDD = refuseSDD
      }
      query.createWorkListIndex = this.createWorkListIndex;
      query.createSketchIndex = this.createSketchIndex;
      query.ignoreDateIndex = ''
      query.refuseDateIndex = ''
      query.hasCreateIndex = ''
      query = {
        ...query,
        totalDirectSketch: true
      }
      return `/api-opc/v4/oms/order/batchCreateSo/v2?${qs.stringify(query)}`
    }
  },
  methods: {
    disabledBatchType (code) {
      const batchType = ['Z001', 'Z005', 'Z007', 'Z008', 'Z012', 'Z014'];
     return !(batchType.includes(code))
    },
    createCustomizeDraft(fileList = [], createWorkList = false) {
      console.log('createCustomizeDraft');
      this.createDraft = true;
      this.createDraftQuery = {
        indexStr: this.totalList.filter(item => item.resStatus === 'fail').map(e => e.orderIndex).join(','),
        orderType: this.batchData.orderType,
        createWorkList
      };
      try {
        let action = () => {
          if (fileList.length) {
            this.$refs.upload.uploadFiles = fileList
          }
          const length = this.$refs.upload.uploadFiles.length - 1
          this.$refs.upload.uploadFiles[length].status = 'ready'
          this.$refs.upload.submit()
        }
        setTimeout(action, 200)
      } catch (err) {
        console.log(err)
      }
    },
    uploadAgain() {
      try {
        this.needCustomerRelCheck = false;
        let action = () => {
          const length = this.$refs.upload.uploadFiles.length - 1
          this.$refs.upload.uploadFiles[length].status = 'ready'
          this.$refs.upload.submit()
        }
        setTimeout(action, 200)
      } catch (err) {
        console.log(err)
      }
    },
    downloadDetail() {
      if (this.failExcelUrl) {
        window.open(this.failExcelUrl)
      }
    },
    closeDialog() {
      this.dialogVisible = false;
      // 跳转草稿列表
      if (this.sketchOrderNoList) {
        window.open(`/sr/draft/list?voucherNoList=${this.sketchOrderNoList}`)
      }
    },
    handleClose(done) {
      done && done()
    },
    isSupportedCompany(item) {
      const orderType = this.batchData.orderType
      if (orderType) {
        const companyList = orderTypeToCompany[orderType]
        // 晋云链禁止采用批量上传
        if (item === '6000') {
          return item === '6000'
        }
        if (companyList && companyList.length > 0) {
          return !companyList.some(c => c === item)
        }
      }
      return false
    },
    isSupportedOrderType(item) {
      const companyCode = this.batchData.companyRange
      if (companyCode) {
        const companyList = orderTypeToCompany[item]
        if (companyList && companyList.length > 0) {
          return !companyList.some(c => c === companyCode)
        }
      }
      return false
    },
    initValue() {
      this.batchData.companyRange = '1000'
      this.batchData.orderType = 'Z001'
    },
    openDlg() {
      if (this.$refs['batchData']) {
        this.$refs['batchData'].resetFields()
      }
      this.initValue()
    },
    handleDownload() {
      const orderType = this.batchData.orderType
      if (orderType === 'Z014') {
        window.open(this.excelUrls.batchImportZ002)
      } else {
        window.open(this.excelUrls.batchImportZ001)
      }
    },
    handleUploadMsg1(res) {
      let msg = ''
      if (res && res.code === 200) {
        const { data } = res;
        let content = `成功${data.successCount || 0}单，失败${data.failCount || 0}单。<br/>`
        if (data.failExcelUrl) {
          try {
            content += data.failList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组` : ''}订单的失败原因：<br/>` + item.msgList.join('<br/>')).join('<br/>')
          } catch (err) {
            console.log(err)
          }
          this.$confirm(`<div style="max-height: 300px;overflow: auto">${content}</div>`, '操作提示', {
            dangerouslyUseHTMLString: true,
            type: 'warning',
            confirmButtonText: '下载失败原因明细'
          })
            .then(() => {
              window.open(data.failExcelUrl)
            })
            .catch(err => console.log(err))
          return;
        }
        try {
          content += data.failList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组` : ''}` + `${item.sketchOrderNo ? `订单已生成草稿单${item.sketchOrderNo}，失败原因：<br/>` : '订单失败原因：<br/>'}` + item.msgList.join('<br/>')).join('<br/>')
        } catch (err) {
          console.log(err)
        }
        this.$confirm(`<div style="max-height: 300px;overflow: auto">${content}</div>`, '操作提示', {
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '确定'
        })
          .then(() => {
            // 跳转草稿列表
            const soNo = data.failList.map(item => item.sketchOrderNo).join(',')
            window.open(`/sr/draft/list?voucherNoList=${soNo}`)
          })
          .catch(err => console.log(err))
      } else {
        msg = res ? res.msg : '上传错误'
        this.$alert(msg, '错误', {
          customClass: 'uploadMsg',
          type: 'error'
        })
      }
    },
    handleUploadMsg2(res) {
      const { data: { successInfoList, failInfoList } } = res
      let msg = `成功${successInfoList.length}单，失败${failInfoList.length}单。`
      if (failInfoList.length > 0) {
        const failMsgList = failInfoList.map(f => `客户：${f.customerNo}，销售范围：${f.salesOrganization}，失败原因：${f.failReason}；`)
        msg += `
          失败信息如下：<br>
          ${failMsgList.join('<br>')}`
      }
      const content =
        `
      <div style="max-height: 300px;overflow: auto">
      ${msg}
      </div>
      `
      this.$alert(content, '结果', {
        customClass: 'uploadMsg',
        dangerouslyUseHTMLString: true
      })
    },
    handleUploadSuccess(res, file, fileList) {
      if (this.loading) {
        this.loading.close()
      }
      const orderType = this.batchData.orderType
      if (orderType === 'Z001' || orderType === 'Z012') {
        this.handleUploadMsg1(res)
      } else {
        this.handleUploadMsg2(res)
      }
    },
    handleSuccess(res, file, fileList) {
      console.log('batchData.companyRange', this.batchData)
      const orderType = this.batchData.orderType
      if (this.loading) {
        this.loading.close()
      }
      if (this.createDraft) {
        this.createDraft = false
        this.dialogVisible = false
        this.createDraftQuery = {}
      }
      this.dialogVisible = false
      this.needCustomerRelCheck = true;
      let msg = ''
      if (res && res.code === 200) {
        if (!/Z001|Z005|Z007|Z012/gim.test(orderType)) {
          msg = this.processErrMsg1(res)
        } else {
          msg = 'hide'
          if (!this.preCheckDate(res, fileList)) return
        }
        if (msg !== 'hide') {
          const content =
            `
          <div style="max-height: 300px;overflow: auto">
          ${msg}
          </div>
          `
          this.$alert(content, '结果', {
            customClass: 'uploadMsg',
            dangerouslyUseHTMLString: true
          })
        }
        if (res.data.failCount === 0 && res.data.successCount) {
          let msg = ''
          if (res.data.successList && res.data.successList.length) {
            msg += res.data.successList
              .sort((a, b) => a.orderIndex - b.orderIndex)
              .map(item => `${item.orderIndex ? `${item.orderIndex}组` : ''}订单： ${item.orderNo || ''}<br/>` + (item.msgList ? item.msgList.join('<br/>') : '')).join('<br/>')
          } else {
            msg = res.msg
          }
          const content =
            `
          <div style="max-height: 300px;overflow: auto">
          ${msg}
          </div>
          `
          this.$alert(content, '操作提示', {
            type: 'success',
            dangerouslyUseHTMLString: true
          })
        }
        setTimeout(() => {
          try {
            this.$refs.upload.clearFiles()
          } catch (error) {
            console.log(error)
          }
        }, 800)
        this.$emit('update:showDialog', false)
      } else {
        msg = res ? res.msg : '上传错误'
        this.$alert(msg, '错误', {
          customClass: 'uploadMsg',
          type: 'error'
        })
      }
    },
    preCheckDate (res, fileList) {
      this.supplierDeliveryConflictParams = null
      const { data } = res
      this.ignoreDate = false
      this.totalList = [
        ...(data.successList || []).map(item => ({
          resStatus: 'success',
          ...item
        })),
        ...(data.failList || []).map(item => ({
          resStatus: 'fail',
          ...item
        }))].filter(Boolean);
      if (data && Array.isArray(data.failList) && data.failList.length) {
        this.hasCreateList = data.failList.map(item => item.orderIndex).join(',')
        this.sketchOrderNoList = data.failList.map(item => item?.sketchOrderNo)?.join(',')
        let content = `成功${data.successCount || 0}单，失败${data.failCount || 0}单。<br/>`
        try {
          content += data.successList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组成功` : ''}外围单号：` + item.orderNo).join('<br/>') + '<br>'
          content += data.failList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组` : ''}订单的失败原因：<br/>` + item.msgList.join('<br/>')).join('<br/>')
        } catch (err) {
          console.log(err)
        }
        if (data.failExcelUrl) {
          this.failExcelUrl = data.failExcelUrl
          this.failContent = content.replace(/\n/g, '<br/>')
          this.dialogVisible = true
          if (data.failCheckType === '1') {
            this.showCreateOrderAgain = true; // 多物料关系再次建单按钮，同时隐藏创建草稿按钮
            this.showCreateDraft = false;
          } else {
            this.showCreateOrderAgain = false;
            this.showCreateDraft = true;
          }
          return
        }
        if (data.highestPriorityFailReasonMap) {
          this.ignoreDate = true;
          this.uploadResData = data;
          this.showPriorityFailReasonErr(data, 0, fileList)
          return;
        }
        this.$alert(content, '操作提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true,
          callback: (action) => {
            // 跳转草稿列表
            if (action === 'confirm' && this.sketchOrderNoList) {
              window.open(`/sr/draft/list?voucherNoList=${this.sketchOrderNoList}`)
            }
          }
        })
      }
      return true;
    },
    formatDraftQuery(createWorkList = false) {
      this.createDraft = true;
      this.createDraftQuery = {
        indexStr: this.totalList.filter(item => item.resStatus === 'fail').map(e => e.orderIndex).join(','),
        orderType: this.batchData.orderType,
        createWorkList
      };
    },
    showPriorityFailReasonErr(data, index, fileList) {
      this.showPriorityFailReasonIndex = index;
      const keys = Object.keys(data.highestPriorityFailReasonMap);
      if (index < keys.length) {
        const key = keys[index];
        const indexList = data.highestPriorityFailReasonMap[key] || []
        let message = indexList.map(index => {
          const curItem = data.failList.find(item => item.orderIndex === index);
          const msgList = curItem && curItem.highestPriorityMsgMap && curItem.highestPriorityMsgMap[key];
          return msgList ? `${index}组订单的失败原因：<br>${msgList?.join('<br>')}；` : ''
        })?.join('<br>');
        let confirmButtonText = '';
        let cancelButtonText = '取消';
        let callback = null;
        switch (key) {
          // 交期二次确认
          // case 'confirm':
          //   this.showCustomerDateConfirmDlg = true;
          //   this.failContent = message;
          //   return false;
          // 协议价卡控
          case 'negotiatedFailed':
            confirmButtonText = '保存草稿并提交价格审批';
            callback = () => {
              console.log('negotiatedFailed');
              this.createWorkListIndex = indexList.join(',');
              this.showPriorityFailReasonErr(data, index + 1, fileList)
            }
            break;
          // 暂时保存草稿
          case 'soUrgentFreeze':
            confirmButtonText = '暂时创建草稿';
            callback = () => {
              console.log('soUrgentFreeze');
              this.createSketchIndex = indexList.join(',');
              this.showPriorityFailReasonErr(data, index + 1, fileList)
            }
            break;
          // 供应商直发卡控
          case 'supplierDeliveryConflict':
            confirmButtonText = '已确认，直接创建订单';
            callback = () => {
              console.log('supplierDeliveryConflict');
              this.supplierDeliveryConflictParams = {
                ...(data?.preRequestParamMap || {}),
                acceptSupplierDirectIndex: data.failList.map((a) => a.orderIndex).join(',')
              }
              this.showPriorityFailReasonErr(data, index + 1, fileList)
            }
            break;
        }
        this.$confirm(`<div style="min-width:400px;max-height: 300px;overflow: auto">${message}</div`, '提示', {
          confirmButtonText,
          cancelButtonText,
          type: 'warning',
          dangerouslyUseHTMLString: true,
          callback: action => {
            if (action === 'confirm') {
              callback && callback()
            } else {
              console.log(action)
            }
          }
        });
      } else {
        try {
          let action = () => {
            if (fileList) {
              this.$refs.upload.uploadFiles = fileList
            }
            const length = this.$refs.upload.uploadFiles.length - 1
            this.$refs.upload.uploadFiles[length].status = 'ready'
            this.$refs.upload.submit()
          }
          setTimeout(action, 200)
        } catch (err) {
          console.log(err)
        }
      }
      return false
    },
    createWorkOrder(type) {
      // 交期二次确认
      this.ignoreDate = true
      this.needCustomerRelCheck = false;
      const indexStr = this.totalList.map(item => item.orderIndex).join(',')
      if (type === 'refuse') {
        this.refuseDateIndex = indexStr;
        this.ignoreDateIndex = ''
      } else {
        this.ignoreDateIndex = indexStr
        this.refuseDateIndex = '';
      }
      try {
        this.showPriorityFailReasonErr(this.uploadResData, this.showPriorityFailReasonIndex + 1)
      } catch (err) {
        console.log(err)
      } finally {
        this.showCustomerDateConfirmDlg = false;
      }
    },
    createWorkOrder1(contentMsg, fileList) {
      // 价格审批卡控
      this.$confirm(contentMsg, '操作提示', {
        confirmButtonText: '保存草稿并提交价格审批',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        this.createCustomizeDraft(fileList, true)
      })
        .catch(() => { })
      return false
    },
    createWorkOrder2(orderType, contentMsg, fileList, rawData) {
      if (/Z001/gmi.test(orderType)) {
        this.$confirm(contentMsg, '操作提示', {
          customClass: 'uploadMsg',
          dangerouslyUseHTMLString: true,
          type: 'warning',
          confirmButtonText: '暂时创建草稿',
          cancelButtonText: '确认'
        }).then(() => {
          if (this.customizeOrder) {
            this.createCustomizeDraft(fileList)
            return
          }
          const query = { indexStr: this.hasCreateList }
          batchCreateSketch(query, rawData)
            .then(res => {
              console.log(res);
              let msg = ''
              if (res && res.code === 200) {
                msg = this.processErrMsg3(res)
                this.$alert(msg, '保存草稿操作提示', {
                  dangerouslyUseHTMLString: true,
                  type: 'info'
                })
              }
            })
            .catch(err => {
              this.$alert(err.msg || err.message || '保存草稿失败！', '操作提示', {
                type: 'error'
              })
            })
        })
          .catch(() => { })
      }
    },
    createWorkOrder3(contentMsg, fileList, data) {
      this.$confirm(contentMsg, '操作提示', {
        confirmButtonText: '已确认，直接创建订单',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      }).then(() => {
        this.supplierDeliveryConflictParams = {
          ...(data?.preRequestParamMap || {}),
          acceptSupplierDirectIndex: data.failList.map((a) => a.orderIndex).join(',')
        }
        try {
          let action = () => {
            this.$refs.upload.uploadFiles = fileList
            const length = this.$refs.upload.uploadFiles.length - 1
            this.$refs.upload.uploadFiles[length].status = 'ready'
            this.$refs.upload.submit()
          }
          setTimeout(action, 200)
        } catch (err) {
          console.log(err)
        }
      })
        .catch(() => { })
      return false
    },
    formatPriceMsg(msg) {
      msg += '';
      return msg.replace(/\]|\[/gmi, '').replace(/,/gmi, '\n');
    },
    processErrMsg1(res) {
      const { data: { successInfoList, failInfoList } } = res
      let msg = `成功${successInfoList.length}单，失败${failInfoList.length}单。`
      if (failInfoList.length > 0) {
        const failMsgList = failInfoList.map(f => `客户：${f.customerNo}，销售范围：${f.salesOrganization}，失败原因：${f.failReason}；`)
        msg += `
          失败信息如下：<br>
          ${failMsgList.join('<br>')}`
      }
      return msg
    },
    processErrMsg2(res) {
      const {
        failCount,
        failList,
        successCount
        // successList
      } = res.data
      let msg = `成功${successCount}单，失败${failCount}单。`
      if (failList.length > 0) {
        const failMsgList = failList.map(f => {
          const { orderIndex, msgList } = f
          return `${orderIndex}组订单的失败原因：<br>${msgList.join('<br>')}；`
        })
        msg += `<br>${failMsgList.join('<br>')}`
      }
      return msg
    },
    processErrMsg3(res) {
      const {
        failCount,
        failList,
        successCount
        // successList
      } = res.data
      let msg = `成功${successCount}单，失败${failCount}单。`
      if (failList.length > 0) {
        const failMsgList = failList.map(f => {
          const { orderIndex, msgList } = f
          return `${orderIndex}组订单的失败原因：<br>${msgList.join('<br>')}；`
        })
        msg += `<br>${failMsgList.join('<br>')}`
      }
      return msg
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleError() {
      if (this.loading) {
        this.loading.close()
      }
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 400px;
  padding-right: 10px;
}
.notice {
  font-size: 12px;
}
.tips-container{
  overflow: auto;
  max-height: 600px;
}
</style>

<style>
.uploadMsg {
  width: 700px;
}
.el-dialog__body {
  padding-bottom: 10px;
}
.el-message-box__content {
  min-width: 350px;
}
</style>
