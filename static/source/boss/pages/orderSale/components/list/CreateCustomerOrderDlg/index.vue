<!--
 * @Author: l<PERSON>zhi<PERSON>
 * @Date: 2024-12-22 13:20:57
 * @LastEditors: luozhikai
 * @LastEditTime: 2025-04-16 13:14:35
 * @Description: file content
-->
<template>
  <el-dialog
    title="创建客户订单"
    :visible.sync="dlgVisible"
    width="800px"
    center
    @open="openDlg"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form :model="batchData" :rules="rules" label-width="170px">
      <el-form-item label="选择公司范围" prop="companyRange">
        <el-select v-model="batchData.companyRange" filterable placeholder="请选择公司范围">
          <el-option
            v-for="item in companyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="isSupportedCompany(item.code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择订单类型" prop="orderType">
        <el-select v-model="batchData.orderType" filterable placeholder="请选择订单类型">
          <el-option
            v-for="item in orderTypeList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="disabledBatchType(item.code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择客户" prop="customerNo">
        <Customer
            v-model="batchData.customerNo"
            @selectChange="handleCustomerChange"
            style="width: 400px"
          />
      </el-form-item>
      <el-form-item label="客户订单模板" prop="modelCode">
        <el-select v-model="batchData.modelCode" clearable filterable placeholder="请选择客户订单模板">
          <el-option
            v-for="item in modelList"
            :key="item.modelCode"
            :label="item.modelName"
            :value="item.modelCode"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!-- <div class="notice">*批量模板在上传后，将会直接生成订单，请确认信息无误后进行上传！上传后请在订单列表搜索客户来查询对应订单即可</div> -->
    <div class="file-list-container" v-if="fileList.length > 0">
      <div class="file-list-title">已选择的文件：</div>
      <div class="file-list">
        <div v-for="(file, index) in fileList" :key="index" class="file-item">
          <i class="el-icon-document"></i>
          <span class="file-name">{{ file.name }}</span>
          <i class="el-icon-close" @click="removeFile(index)"></i>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-upload
        ref="upload"
        action="#"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleFileChange"
        :before-upload="handleBeforeUpload"
        :on-exceed="handleExceed"
        multiple
        :limit="10"
        accept=".xlsx,.pdf,.xls,.csv,.png,.jpg,.jpeg,.gif,.docx"
        style="display: inline-block"
      >
        <el-button
          type="primary"
          style="margin-right: 20px"
          :disabled="uploadDisabled"
        >
          选择客户文件
        </el-button>
      </el-upload>
      <el-button
        type="primary"
        style="margin-right: 10px"
        :disabled="uploadDisabled || fileList.length === 0"
        @click="uploadFiles(false)"
      >
        上传附件-创建订单
      </el-button>
      <el-button
        type="primary"
        style="margin-right: 10px"
        :disabled="uploadDisabled || fileList.length === 0"
        @click="uploadFiles(true)"
      >
        上传附件-保存草稿
      </el-button>
      <el-button @click="$emit('update:showDialog', false)">取 消</el-button>
    </div>
    <el-dialog
      title="操作提示"
      :visible.sync="dialogVisible"
      append-to-body
      width="500px"
      :before-close="handleClose">
      <div class="tips-container" v-html="failContent"></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="warning" @click="downloadDetail">下载失败原因明细</el-button>
        <el-button type="default" @click="closeDialog">确 定</el-button>
        <el-button v-if="showCreateOrderAgain" type="primary" @click="uploadAgain">继续建单</el-button>
      </span>
    </el-dialog>
  </el-dialog>

</template>
<script>
import { supportedCompanyList, supportedOrderTypeList, orderTypeToCompany } from '@/pages/orderSale/constants'
import qs from 'qs';
import Customer from '@/components/SearchFields/customer'
import { getModels } from '@/api/templateCenter'
import request from '@/utility/request'
import { marked } from 'marked'

const renderer = new marked.Renderer();
renderer.link = (href, title, text) => {
  return `<a href="${href}" target="_blank" ${title ? `title="${title}"` : ''}>${text}</a>`;
};

marked.setOptions({
  breaks: true,
  gfm: true,
  renderer: renderer
});

export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    }
  },
  components: {
    Customer
  },
  data () {
    return {
      fileList: [],
      rules: {
        companyRange: [
          { required: true, message: '请选择公司范围', trigger: 'change' }
        ],
        orderType: [
          { required: true, message: '请选择订单类型', trigger: 'change' }
        ],
        customerNo: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        modelCode: [
          { required: true, message: '请选择客户订单模板', trigger: 'change' }
        ]
      },
      batchData: {
        companyRange: '1000',
        orderType: 'Z001',
        customerNo: '',
        modelCode: ''
      },
      companyRangeList: [], // 公司范围列表
      loading: null,
      ignoreDate: false,
      dialogVisible: false,
      hasCreateList: '',
      totalList: [],
      failExcelUrl: '',
      failContent: '',
      createDraft: false,
      showCreateOrderAgain: false,
      showCreateDraft: true,
      needCustomerRelCheck: true,
      createDraftQuery: {},
      supplierDeliveryConflictParams: null, // 接受供应商直发冲突
      ignoreDateIndex: '', // 已确认(接受标期),继续创建的组号
      refuseDateIndex: '', // 不接受标期,继续创建的组号
      createWorkListIndex: '', // 保存草稿并触发协议价审批的组号
      createSketchIndex: '', // 保存草稿的组号
      uploadResData: {}, // 第一次上传的结果
      sketchOrderNoList: '', // 上传失败后保存草稿的草稿单号，用于跳转草稿列表
      modelList: []
    }
  },
  created () {
    this.initValue()
  },
  computed: {
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.initValue()
        this.$emit('update:showDialog', val)
      }
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    companyList () {
      return this.dictList &&
        this.dictList['companyScope']
        ? this.dictList['companyScope'].filter(item =>
          supportedCompanyList.indexOf(item.code) > -1)
        : []
    },
    orderTypeList () {
      return this.dictList &&
        this.dictList['soCategory']
        ? this.dictList['soCategory'].filter(item => {
          return supportedOrderTypeList.indexOf(item.code) >= 0 && item.code !== 'Z013'
        })
        : []
    },
    uploadAction () {
      const orderType = this.batchData.orderType
      const company = this.batchData.companyRange
      let query = {
        orderType,
        company,
        modelCode: this.batchData.modelCode,
        customerNo: this.batchData.customerNo
        // needCustomerRelCheck: this.needCustomerRelCheck
      }
      query.createWorkListIndex = this.createWorkListIndex;
      query.createSketchIndex = this.createSketchIndex;
      query.ignoreDateIndex = ''
      query.refuseDateIndex = ''
      query.hasCreateIndex = ''
      if (this.ignoreDate) {
        query.ignoreDateIndex = this.ignoreDateIndex;
        query.refuseDateIndex = this.refuseDateIndex;
        query.hasCreateIndex = this.totalList.filter(item => item.resStatus === 'success').map(item => item.orderIndex).join(',')
      }
      if (this.supplierDeliveryConflictParams) {
        query = {
          ...query,
          ...this.supplierDeliveryConflictParams
        }
      }

      // if (this.isNormalBatchOrder) {
        return '/api-opc/v4/oms/order/createCustomerOrder/batch/async?' + qs.stringify(query)
      // }
      // return `/api-opc/v1/excel/batchImportSo?${qs.stringify(query)}`
    },
    uploadActionDraft () {
      const orderType = this.batchData.orderType
      const company = this.batchData.companyRange
      let query = {
        orderType,
        company,
        modelCode: this.batchData.modelCode,
        customerNo: this.batchData.customerNo
        // needCustomerRelCheck: this.needCustomerRelCheck
      }
      query.createWorkListIndex = this.createWorkListIndex;
      query.createSketchIndex = this.createSketchIndex;
      query.ignoreDateIndex = ''
      query.refuseDateIndex = ''
      query.hasCreateIndex = ''
      query = {
        ...query,
        totalDirectSketch: true
      }
      return '/api-opc/v4/oms/order/createCustomerOrder/batch/async?' + qs.stringify(query)
    },
    uploadDisabled () {
      return !this.batchData.modelCode || !this.batchData.customerNo
    }
  },
  methods: {
    handleCustomerChange (val) {
      getModels({
        customerNo: val,
        modelStatus: 0,
        modelType: 13,
        matchModel: true,
        current: 1,
        pageSize: 50
      }).then(res => {
        console.log(res);
        if (res.code === 200) {
          this.modelList = res.data
          this.batchData.modelCode = res.data[0]?.modelCode || ''
        }
      })
    },
    disabledBatchType (code) {
      const batchType = ['Z001', 'Z005', 'Z006', 'Z007', 'Z008', 'Z012', 'Z014'];
     return !(batchType.includes(code))
    },
    uploadAgain () {
      try {
        this.needCustomerRelCheck = false;
        let action = () => {
          const length = this.$refs.upload.uploadFiles.length - 1
          this.$refs.upload.uploadFiles[length].status = 'ready'
          this.$refs.upload.submit()
        }
        setTimeout(action, 200)
      } catch (err) {
        console.log(err)
      }
    },
    downloadDetail () {
      if (this.failExcelUrl) {
        window.open(this.failExcelUrl)
      }
    },
    closeDialog () {
      this.dialogVisible = false;
      // 跳转草稿列表
      if (this.sketchOrderNoList) {
        window.open('/sr/draft/list?voucherNoList=' + this.sketchOrderNoList)
      }
    },
    handleClose (done) {
      done && done()
    },
    isSupportedCompany (item) {
      const orderType = this.batchData.orderType
      if (orderType) {
        const companyList = orderTypeToCompany[orderType]
        // 晋云链禁止采用批量上传
        if (item === '6000') {
          return item === '6000'
        }
        if (companyList && companyList.length > 0) {
          return !companyList.some(c => c === item)
        }
      }
      return false
    },
    initValue () {
      this.batchData.companyRange = '1000'
      this.batchData.orderType = 'Z001'
      this.batchData.modelCode = ''
      this.batchData.customerNo = ''
    },
    openDlg () {
      this.initValue()
      this.fileList = []
    },
    handleSuccess (res, file, fileList) {
      console.log('batchData.companyRange', this.batchData)
      if (this.loading) {
        this.loading.close()
      }
      if (this.createDraft) {
        this.createDraft = false
        this.dialogVisible = false
        this.createDraftQuery = {}
      }
      this.dialogVisible = false
      this.needCustomerRelCheck = true;
      let msg = ''
      if (res && res.code === 200) {
        msg = res.msg
        this.$alert(marked(msg), '', {
          customClass: 'uploadMsg',
          dangerouslyUseHTMLString: true
        })
        setTimeout(() => {
          try {
              this.fileList = []
          } catch (error) {
            console.log(error)
          }
        }, 800)
      } else {
        msg = res?.msg || '上传错误'
        this.$alert(marked(msg), '错误', {
          customClass: 'uploadMsg',
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    handleError (error) {
      try {
        if (this.loading) {
          this.loading.close()
        }
        let msg = (error && error.msg) || (error && error.message) || '请求时间过长, 请稍后前往草稿订单列表确认创建结果'
        this.$alert(msg, '错误', {
          customClass: 'uploadMsg',
          type: 'error'
        })
      } catch (error) {
        let msg = (error && error.msg) || (error && error.message) || '请稍后前往草稿订单列表确认创建结果'
        this.$alert('请求异常, ' + msg, '错误', {
          customClass: 'uploadMsg',
          type: 'error'
        })
      }
    },
    preCheckDate (res, fileList) {
      this.supplierDeliveryConflictParams = null
      const { data } = res
      this.ignoreDate = false
      this.totalList = [
        ...(data.successList || []).map(item => ({
          resStatus: 'success',
          ...item
        })),
        ...(data.failList || []).map(item => ({
          resStatus: 'fail',
          ...item
        }))].filter(Boolean);
      if (data && Array.isArray(data.failList) && data.failList.length) {
        this.hasCreateList = data.failList.map(item => item.orderIndex).join(',')
        this.sketchOrderNoList = data.failList.map(item => item?.sketchOrderNo)?.join(',')
        let content = '成功' + (data.successCount || 0) + '单，失败' + (data.failCount || 0) + '单。<br/>'
        try {
          content += data.successList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组成功` : ''}外围单号：` + item.orderNo).join('<br/>') + '<br>'
          content += data.failList.sort((a, b) => a.orderIndex - b.orderIndex).map(item => `${item.orderIndex ? `${item.orderIndex}组` : ''}订单的失败原因：<br/>` + item.msgList.join('<br/>')).join('<br/>')
        } catch (err) {
          console.log(err)
        }
        if (data.failExcelUrl) {
          this.failExcelUrl = data.failExcelUrl
          this.failContent = content.replace(/\n/g, '<br/>')
          this.dialogVisible = true
          if (data.failCheckType === '1') {
            this.showCreateOrderAgain = true; // 多物料关系再次建单按钮，同时隐藏创建草稿按钮
            this.showCreateDraft = false;
          } else {
            this.showCreateOrderAgain = false;
            this.showCreateDraft = true;
          }
          return
        }
        // if (data.highestPriorityFailReasonMap) {
        //   this.ignoreDate = true;
        //   this.uploadResData = data;
        //   this.showPriorityFailReasonErr(data, 0, fileList)
        //   return;
        // }
        this.$alert(content, '操作提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true,
          callback: (action) => {
            // 跳转草稿列表
            if (action === 'confirm' && this.sketchOrderNoList) {
              window.open('/sr/draft/list?voucherNoList=' + this.sketchOrderNoList)
            }
          }
        })
      }
      return true;
    },
    processErrMsg1 (res) {
      const { data: { successList, failList } } = res
      let msg = '成功' + successList.length + '单，失败' + failList.length + '单。'
      if (failList.length > 0) {
        const failMsgList = failList.map(f => f.failReasonSet.join('、'))
        msg += `
          失败信息如下：<br>
          ${failMsgList.join('<br>')}`
      }
      return msg
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleExceed (files, fileList) {
      this.$message.warning('当前限制选择 10 个文件，本次选择了 ' + files.length + ' 个文件，共选择了 ' + (files.length + fileList.length) + ' 个文件')
    },
    // selectFiles方法不再需要，由el-upload组件自动处理

    handleFileChange(file, files) {
      // 清空已选择的文件, el-upload的on-change本质上还是会每个文件触发一次本方法, 并且已选择的文件会被自动添加到files中
      this.$refs.upload.clearFiles()
      // 检查文件数量限制 - 由el-upload的limit和on-exceed处理，这里只是额外检查
      if (this.fileList.length + files.length > 10) {
        this.$message.warning('当前限制选择 10 个文件，已超出限制')
        return
      }
      // 检查重复文件名
      let fileNames = new Set()
      this.fileList.forEach(file => {
        fileNames.add(file.name)
      })
        if (fileNames.has(file.name)) {
          this.$message.warning('不能上传同名文件: ' + file.name)
          return
        }
        fileNames.add(file.name)
        this.fileList.push(file)
    },
    removeFile(index) {
      this.fileList.splice(index, 1)
    },
    uploadFiles(isDraft) {
      if (this.fileList.length === 0) {
        this.$message.error('请先选择文件')
        return
      }
      // 显示加载中
      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })

      // 创建FormData对象
      const formData = new FormData()
      this.fileList.forEach(file => {
        // 处理el-upload的文件对象，获取原始文件
        const rawFile = file.raw || file
        formData.append('files', rawFile)
      })

      // 确定上传地址
      const url = isDraft ? this.uploadActionDraft : this.uploadAction
      // 发送请求
      request({
        url: url,
        method: 'post',
        data: formData
      })
      .then(response => {
          this.handleSuccess(response, null, this.fileList)
        })
        .catch(error => {
          this.handleError(error, null, this.fileList)
        })
        .finally(() => {
          if (this.loading) {
            this.loading.close()
          }
        })
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 400px;
  padding-right: 10px;
}
.notice {
  font-size: 12px;
}
.tips-container {
  overflow: auto;
  max-height: 600px;
}
.template-notice {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}
.file-list-container {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
  background-color: #f9f9f9;
}
.file-list-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}
.file-list {
  max-height: 200px;
  overflow-y: auto;
}
.file-item {
  display: flex;
  align-items: center;
  padding: 5px 0;
  border-bottom: 1px dashed #ebeef5;
}
.file-item:last-child {
  border-bottom: none;
}
.file-name {
  flex: 1;
  margin: 0 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.el-icon-document {
  color: #909399;
}
.el-icon-close {
  color: #c0c4cc;
  cursor: pointer;
}
.el-icon-close:hover {
  color: #f56c6c;
}
/* 禁用按钮样式 */
.el-button.is-disabled {
  background-color: #c0c4cc;
  border-color: #c0c4cc;
}
</style>

<style>
.uploadMsg {
  width: 700px;
}
.el-dialog__body {
  padding-bottom: 10px;
}
.el-message-box__content {
  min-width: 350px;
}
/* 禁用按钮样式 */
.el-button.is-disabled {
  background-color: #c0c4cc;
  border-color: #c0c4cc;
}
</style>
