<template>
  <el-dialog
    title="创建订单"
    :visible.sync="dlgVisible"
    width="600px"
    center
    @open="openDlg"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form ref="createOrderDialog" :model="createOrderDialog" :rules="rules" label-width="150px">
      <el-form-item label="选择公司范围" prop="companyRange">
        <el-select v-model="createOrderDialog.companyRange" filterable placeholder="请选择公司范围">
          <el-option
            v-for="item in companyList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="isSupportedCompany(item.code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择订单类型" prop="orderType">
        <el-select v-model="createOrderDialog.orderType" filterable placeholder="请选择订单类型">
          <el-option
            v-for="item in orderTypeList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
            :disabled="isSupportedOrderType(item.code)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="选择订单依据" prop="orderBasis" v-if="isShowOrderBasis">
        <el-select v-model="createOrderDialog.orderBasis" clearable filterable placeholder="请选择订单依据">
          <el-option
            v-for="item in dictList['orderBasis']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:showDialog', false)">取 消</el-button>
      <el-button type="primary" @click="submit('createOrderDialog')">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { supportedCompanyList, supportedOrderTypeList, orderTypeToCompany } from '@/pages/orderSale/constants'

export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      rules: {
        companyRange: [
          { required: true, message: '请选择公司范围', trigger: 'change' }
        ],
        orderType: [
          { required: true, message: '请选择订单类型', trigger: 'change' }
        ]
      },
      createOrderDialog: {
        companyRange: '1000',
        orderType: 'Z001',
        orderBasis: 'CUSTOMER_ORDER'
      },
      companyRangeList: [] // 公司范围列表
    }
  },
  created () {
    this.initValue()
  },
  computed: {
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    companyList () {
      return this.dictList &&
        this.dictList['companyScope']
        ? this.dictList['companyScope'].filter(item =>
          supportedCompanyList.indexOf(item.code) > -1)
        : []
    },
    orderTypeList () {
      return this.dictList &&
        this.dictList['soCategory']
        ? this.dictList['soCategory'].filter(item => {
          return supportedOrderTypeList.indexOf(item.code) >= 0 && item.code !== 'Z013'
        })
        : []
    },
    isShowOrderBasis () {
      return /Z001|Z005|Z012/gim.test(this.createOrderDialog.orderType)
    }
  },
  methods: {
    isSupportedCompany (item) {
      const orderType = this.createOrderDialog.orderType
      if (orderType) {
        const companyList = orderTypeToCompany[orderType]
        if (companyList && companyList.length > 0) {
          return !companyList.some(c => c === item)
        }
      }
      return false
    },
    isSupportedOrderType (item) {
      const forecastList = ['Z002']
      if (forecastList.includes(item)) return true
      const companyCode = this.createOrderDialog.companyRange
      if (companyCode) {
        const companyList = orderTypeToCompany[item]
        if (companyList && companyList.length > 0) {
          return !companyList.some(c => c === companyCode)
        }
      }
      return false
    },
    initValue () {
      this.createOrderDialog.companyRange = '1000'
      this.createOrderDialog.orderType = 'Z001'
    },
    openDlg () {
      if (this.$refs['createOrderDialog']) {
        this.$refs['createOrderDialog'].resetFields()
      }
      this.initValue()
    },
    submit (name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          const companyValue = this.createOrderDialog.companyRange
          const orderTypeValue = this.createOrderDialog.orderType
          const orderBasis = this.createOrderDialog.orderBasis
          if (this.isShowOrderBasis && orderBasis !== 'CUSTOMER_ORDER') {
            window.open(`/sr/createSpecialOrder?companyValue=${companyValue}&orderTypeValue=${orderTypeValue}&orderBasis=${orderBasis}`)
            return;
          }
          if (companyValue && companyValue) {
            // const key = shortid.generate()
            window.open(`/sr/create/${companyValue}/${orderTypeValue}`)
            this.$emit('update:showDialog', false)
          }
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.el-select {
  width: 400px;
  padding-right: 10px;
}
</style>
