<template>
  <el-dialog
    :title="title"
    :visible.sync="dlgVisible"
    append-to-body
    width="750px"
  >
    <div class="tips-container" v-html="failContent"></div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:showDialog', false)"
        >取消</el-button
      >
      <el-button type="primary" @click="submit('receipt')">接受标期并提交</el-button>
      <el-button type="primary" @click="submit('refuse')">不接受标期并提交</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '操作提示'
    },
    failContent: {
      type: String,
      default: ''
    }
  },
  data () {
    return {}
  },
  computed: {
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    submit(type) {
      this.$emit('submit', type)
    }
  }
};
</script>

<style>
</style>
