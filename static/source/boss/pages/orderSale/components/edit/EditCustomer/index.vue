<template>
  <el-dialog
    title="客户详情"
    :visible.sync="showDlg"
    :show-close="false"
    width="800px"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form
      ref="form"
      label-position="left"
      label-width="110px"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="售达方">
            <el-input v-model="orderData.customerName" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="送达方" prop="send">
            <el-input v-model="orderData.customerName" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="收票方">
            <el-select
              ref="customer"
              v-model="orderData.invoiceReceiver"
              filterable
              clearable
              remote
              reserve-keyword
              placeholder="请输入客户编号/名称"
              style="flex:1"
              :disabled="clientDetail&&clientDetail.vflag"
              :remote-method="queryCustomerList"
              :loading="loadingCustomer"
              @change="handleCustomerChange"
            >
              <el-option
                v-for="(item, index) in customerList"
                :key="item.customerId"
                :label="item.customerName"
                :value="item.customerNumber"
                :disabled="index===0||!isCustomerAvailable(item)"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{fontWeight:index===0?'bold':'normal'}"
                >
                  <div>{{ item.customerNumber }}</div>
                  <div>{{ item.cityName }}</div>
                  <div>{{ item.customerName }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款方" prop="send">
            <el-input v-model="orderData.invoiceReceiverName" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="客户来源">
            <el-input v-model="clientDetail.customerSourceName" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款条件">
            <el-select v-model="orderData.paymentTerm" placeholder="付款条件">
              <el-option
                v-for="item in paymentTermsOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="自营配送">
            <el-select v-model="orderData.serviceCenterSelfTransport" placeholder="是否自营配送">
              <el-option
                v-for="item in dictList['serviceCenterSelfTransport']"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="含税/未税">
            <el-input :value="orderDetail.isTax == '0' ? '未税' : '含税'" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="销售办事处">
            <el-select v-model="clientDetail.salesOffice" :disabled="true" placeholder="销售办事处">
              <el-option
                v-for="item in dictList['salesOffice']"
                :key="item.parentCode"
                :label="item.name"
                :value="item.parentCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="销售组">
            <el-select v-model="clientDetail.salesGroup" :disabled="true" placeholder="销售办事处">
              <el-option
                v-for="item in dictList['salesGroup']"
                :key="item.parentCode"
                :label="item.name"
                :value="item.parentCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="接受供应商直发" prop="acceptSupplierDelivery">
            <el-select v-model="orderData.acceptSupplierDelivery" placeholder="是否接受供应商直发">
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="客户订单号">
            <el-input v-model="orderData.customerReferenceNo" placeholder="客户订单号" />
          </el-form-item>
        </el-col>
         <!-- <el-col :span="12">
          <el-form-item label="客户参考日期">
            <el-date-picker v-model="orderData.customerReferenceDate" value-format="yyyy-MM-dd" type="date" placeholder="选择客户参考日期" />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row v-if="clientDetail.saleOrgVO" :gutter="40">
        <el-col :span="24">
          <el-form-item label="销售范围" prop="salesRange">
            <el-input :disabled="true" :value="`${clientDetail.saleOrgVO.salesOrganization}/${clientDetail.saleOrgVO.distributionChannel}/${clientDetail.saleOrgVO.productGroup}  ${clientDetail.saleOrgVO.salesOrganizationName}，${clientDetail.saleOrgVO.distributionChannelName}，${clientDetail.saleOrgVO.productGroupName}`" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="btnGroup ba-row-center">
      <el-button type="primary" @click="submit">确认保存</el-button>
      <el-button type="primary" plain @click="$emit('update:showDialog', false)">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>

import {
  searchClients, queryPaymentTerms
} from '@/api/orderSale'

export default {
  props: ['showDialog', 'orderDetail', 'clientDetail', 'invoiceReceiverList', 'onSubmit'],
  data() {
    return {
      loadingCustomer: false,
      customerList: [],
      paymentTermsOptions: [],
      orderData: {}
    }
  },
  computed: {
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get() {
        return this.showDialog
      },
      set(val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  watch: {
    showDialog(newValue) {
      if (newValue) {
        this.orderData = { ...this.orderDetail }
        const { invoiceReceiver, invoiceReceiverName } = this.orderDetail
        if (this.invoiceReceiverList && this.invoiceReceiverList.length > 0) {
          this.customerList = [...this.invoiceReceiverList]
        } else {
          this.customerList = [
            {
              customerNumber: '客户编码',
              customerName: '客户名称',
              cityName: '城市'
            },
            {
              customerNumber: invoiceReceiver,
              customerId: invoiceReceiver,
              customerName: invoiceReceiverName,
              cityName: '--'
            }
          ]
        }
        this.queryPaymentTermsList()
      } else {
        this.orderData = {}
      }
    }
  },
  methods: {
    handleCustomerChange(val) {
      const customer = this.customerList.find(item => item.customerNumber === val)
      if (customer) {
        this.orderData.invoiceReceiverName = customer.customerName
      }
    },
    isCustomerAvailable(customer) {
      const { salesOrganization } = this.orderData
      if (customer && salesOrganization && salesOrganization.length > 2) {
        const { saleOrgList } = customer
        return (saleOrgList || []).find(item =>
          item.salesOrganization &&
          item.salesOrganization.length >= 2 &&
          item.salesOrganization.substring(0, 2) === salesOrganization.substring(0, 2))
      }
      return false
    },
    queryCustomerList(customer) {
      this.loadingCustomer = true
      searchClients(customer).then(res => {
        this.loadingCustomer = false
        if (res && res.code === 200) {
          this.customerList = [
            {
              customerNumber: '客户编码',
              customerName: '客户名称',
              cityName: '城市'
            },
            ...res.data
          ]
        }
      }).catch(res => {
        this.loadingCustomer = true
      })
    },
    queryPaymentTermsList () {
      const { customerNo, salesOrganization } = this.orderData
      const params = {
        customerCode: customerNo,
        salesOrganization
      }
      queryPaymentTerms(params).then(res => {
        if (res && res.code === 200) {
          this.paymentTermsOptions = res.data
        } else {
          this.paymentTermsOptions = []
        }
      }).catch(err => console.log(err))
    },
    getPaymentTerm (val) {
      const paymentTerms = this.dictList['paymentTerms']
      if (paymentTerms) {
        const paymentItem = paymentTerms.find(item => item.code === val)
        if (paymentItem) {
          return paymentItem.name
        }
      }
      return ''
    },
    submit() {
      this.$emit('onSubmit', this.orderData, this.customerList)
      this.$emit('update:showDialog', false)
      this.$emit('getDeliveryDate')
    }
  }
}
</script>

<style scoped lang="scss">
.el-select,
.el-date-editor {
  width: 100%;
}

.ba-row-center{
  display: flex;
  align-items: center;
  justify-content: center;
}

.ba-row-start{
  display: flex;
}

.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
    overflow: auto;
  }
}
</style>
