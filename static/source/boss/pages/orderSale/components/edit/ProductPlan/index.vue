<template>
  <el-dialog
    title="计划行详情"
    :visible.sync="showDlg"
    :show-close="false"
    width="900px"
    @closed="$emit('update:showDialog', false)"
  >
    <el-table border :data="list.filter(item => item.operationType !== 3)" width="100%">
      <el-table-column prop="deliveryDate" label="交货日期" align="center" width="200">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.deliveryDate"
            placeholder="选择交货日期"
            :style="{width: '150px'}"
            size="mini"
            type="date"
            value-format="yyyy-MM-dd"
            :disabled="scope.row.disabled||scope.row.deliveryQuantity>0"
          />
        </template>
      </el-table-column>
      <el-table-column prop="itemQuantity" label="订单数量" align="center" width="200">
        <template slot-scope="scope">
          <el-input-number
            v-model.number="scope.row.itemQuantity"
            class="EditOrder-orderNum"
            size="mini"
            :style="{width: '150px'}"
            :min="0"
            :step="1"
            :precision="0"
            :disabled="scope.row.disabled||scope.row.deliveryQuantity>0"
          />
        </template>
      </el-table-column>
      <el-table-column prop="confirmQuantity" label="确认数量" align="center" />
      <el-table-column prop="confirmedQtyType" label="确认类型" align="center" >
        <template slot-scope="{row}" >
          <el-tag v-if="row.confirmedQtyType==='onWay'" type="info">在途</el-tag>
          <el-tag v-if="row.confirmedQtyType==='inStock'" type="success">在库</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="quantityUnit" label="销售单位" align="center" />
      <!-- <el-table-column prop="deliveryQuantity" label="已交货数量" align="center" /> -->
      <el-table-column prop="itemPlanType" label="计划行类别" width="100" align="center" />
      <el-table-column fixed="right" label="操作" width="60" align="center">
        <template slot-scope="{row, $index}">
          <el-button v-if="!row.disabled&&!row.deliveryQuantity>0"
            type="text" size="mini" @click="handleDel($index)">
              删除
            </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="ba-row-center btnGroup">
      <el-button type="primary" @click="addline">新增计划行</el-button>
      <el-button type="primary" @click="saveline">确认保存</el-button>
      <el-button @click="$emit('update:showDialog', false)">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import moment from 'moment'
export default {
  props: ['showDialog', 'skuDetail', 'quantityList'],
  data () {
    return {
      list: []
    }
  },
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  watch: {
    showDialog (newValue) {
      if (newValue) {
        const { data } = this.skuDetail
        this.list = (data.itemPlanDTOList || []).slice().map(item => {
          const { itemQuantity, sapItemPlanNo, soItemPlanNo } = item
          return {
            ...item,
            quantityUnit: data.quantityUnit,
            operationType: item.operationType || 2,
            disabled: itemQuantity === 0 && !!(sapItemPlanNo || soItemPlanNo)
          }
        })
      } else {
        this.list = []
      }
    }
  },
  methods: {
    handleDel (idx) {
      if (this.list[idx]) {
        const { sapItemPlanNo, soItemPlanNo } = this.list[idx]
        if (sapItemPlanNo === undefined || soItemPlanNo === undefined) {
          this.list.splice(idx, 1)
        } else {
          const data = {
            ...this.list[idx],
            operationType: 3
          }
          this.list.splice(idx, 1)
          this.list.push(data)
        }
      }
    },
    addline () {
      if (this.list && this.list.length > 0) {
        const firstItem = this.list[0]
        if (firstItem) {
          const copyItem = {
            ...firstItem,
            deliveryQuantity: 0,
            confirmQuantity: 0,
            itemQuantity: 0,
            operationType: 1
          }
          delete copyItem.sapItemPlanNo
          delete copyItem.soItemPlanNo
          this.list.push(copyItem)
        }
      }
    },
    saveline () {
      const { index, data } = this.skuDetail
      // const newList = [...this.goodsList]
      const filteredNewList = this.list.filter(item => item.operationType !== 3)
      const newListTotal = filteredNewList.reduce((total, item) => (total + Number(item.itemQuantity)), 0)
      const limitTotal = this.quantityList[index]
      const isDateNotValid = filteredNewList.some(item => !item.deliveryDate)
      if (newListTotal > limitTotal) {
        this.$message.error('计划行数量不能超过订单数量！')
      } else if (isDateNotValid) {
        this.$message.error('交货日期不能为空！')
      } else {
        data.itemPlanDTOList = this.list

        data.quantity = newListTotal
        const dateList = filteredNewList.map(item => new Date(item.deliveryDate).getTime()).sort()
        const deliveryDate = moment(dateList[0] || Date.now()).format('YYYY-MM-DD')
        data.deliveryDate = deliveryDate
        this.$emit('submitProductPlan', {
          index,
          data
        })
        this.list = []
        this.$emit('update:showDialog', false)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.btnGroup {
  margin-top: 30px;
}
</style>
<style lang="scss">
.EditOrder-orderNum {
  input {
    text-align: center;
  }
}
</style>
