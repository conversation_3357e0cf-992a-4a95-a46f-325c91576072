<template>
  <el-dialog
    :show-close="false"
    title="商品详情"
    :visible.sync="showDlg"
    top="10px"
    width="900px"
    @closed="$emit('update:showDialog', false)"
    @open="openDialog"
  >
    <DetailForm
      ref="productDetailEle"
      :key="productDetailKey"
      :sku-detail="skuDetail"
      :client-detail="clientDetail"
      :show-dialog="showDialog"
      :order-detail="orderDetail"
      :formatSKU="formatSKU"
      @submit="handleSave"
      v-on="$listeners"
    />
    <div class="ba-center">
      <el-button type="primary" plain @click="$emit('update:showDialog', false)">关闭</el-button>
      <el-button type="primary" @click="submit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import DetailForm from './form'
import * as shortid from 'shortid'

export default {
  components: {
    DetailForm
  },
  props: ['showDialog', 'skuDetail', 'clientDetail', 'submitProductDetail', 'orderDetail', 'formatSKU'],
  data () {
    return {
      productDetailKey: shortid.generate()
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    handleSave (data) {
      console.log(data);
      this.$emit('submitProductDetail', data)
      this.$emit('update:showDialog', false)
    },
    openDialog () {
      this.productDetailKey = shortid.generate()
    },
    submit () {
      this.$refs.productDetailEle.submit('productDetailEle')
    }
  }
}
</script>
<style scoped lang="scss">
.el-row {
  margin-bottom: 20px;
}
.inputRow {
  display: flex;
  align-items: center;
  .inputLabel {
    width: 110px;
    text-align: right;
    white-space: nowrap;
  }
  .inputContent {
    flex: 1;
  }
  .inputTip {
    cursor: pointer;
    margin-top: 5px;
    display: inline-block;
  }
}
.tipTable {
  width: 500px;
}
.ba-center {
  text-align: center;
}
</style>
