<template>
<div>
  <el-form v-loading="loading" ref="productDetailEle" :model="detail" :rules="rules" label-width="135px">
    <DividerHeader>商品信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="客户物料号" prop="customerMaterialNo">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户物料号</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-input v-model="detail.customerMaterialNo" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户行号" prop="customerOrderNo">
          <el-input v-model="detail.customerOrderNo" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="制造商型号" prop="manufacturerNo">
          <el-input v-model="sku.manufacturerNo" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户规格型号" prop="customerSpecificationModel">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户规格型号</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-input v-model="detail.customerSpecificationModel" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="商品编号">
          <SelectSkus ref="selectSku" :disabled="detail.addType !== '1'" :selectedSku="selectedSku" :customerNo="orderDetail.customerNo" @handleSelectSku="addSkuToTable" :customerMaterialNo="detail.customerMaterialNo" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="商品描述" prop="sapMaterialName">
          <el-input v-model="detail.sapMaterialName" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="客户物料名称" prop="customerMaterialName">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户物料名称</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-input v-model="detail.customerMaterialName" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="detail.quantity"
            style="width:100%"
            :min="0"
            :max="detail.referType === '03' ? detail.replenishNum : 1000000000"
            :precision="3"
            :step="1"
            @change="handleQuantityChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户物料数量" prop="customerMaterialQuantity">
          <template slot="label">
            <div class="label">
              <span>客户物料数量</span>
              <customerMaterialTable v-if="customerMaterialTableData.length > 1" :tableList="customerMaterialTableData" @change="handleMaterialClick" />
              <el-tooltip
                :content="`当前商品数量与客户物料数量的比例为${rate}`"
                effect="dark"
                placement="top"
                v-if="!!rate"
              >
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
          </template>
          <el-input-number style="width:100%" v-model="detail.customerMaterialQuantity" :precision="6" :step="1" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="单位" prop="quantityUnit">
          <el-select v-model="detail.quantityUnit" placeholder="请选择单位" @change="changeQuantityUnit" disabled style="width: 100%">
            <el-option
              v-for="item in detail.packageInfoList"
              :key="item.unit"
              :label="item.unitName"
              :value="item.unit"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户物料单位" prop="customerMaterialUnit">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户物料单位</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-select v-model="detail.customerMaterialUnit" placeholder="请选择" style="width:100%"
            clearable filterable allow-create>
            <el-option
              v-for="item in dictList['customerQuantityUnit']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>仓位信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="工厂" prop="factory">
          <el-select
            v-model="detail.factory"
            placeholder="请选择工厂"
            style="width:100%"
            @change="val => changeRow(val, 'factory')">
            <el-option
              v-for="item in factoryList"
              :key="item.code"
              :label="(item.code > 0 ? item.code : '')+item.name"
              :value="item.code"
              :disabled="isFactoryDisable(detail, item.code)"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="选择直发" prop="directDeliverySupplier">
          <el-select
            :disabled="isService||isEnableDirectDeliverySupplier"
            v-model="detail.directDeliverySupplier"
            placeholder="请选择"
            style="width:100%"
            @change="val => changeRow(val, 'directDeliverySupplier')"
          >
            <el-option
              v-for="item in directDeliverySupplierList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
              :disabled="isService"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="发货仓" prop="position">
          <el-select
            v-model="detail.position"
            filterable
            placeholder="请选择发货仓"
            style="width:100%"
            @change="val => changeRow(val, 'position')"
          >
            <div v-if="detail.directDeliverySupplier === '0'" slot="empty" style="text-align:center">
              <p style="margin-top:10px;color:grey">无匹配数据</p>
              <p style="margin:10px 10px 0 10px;color:grey" v-if="morePositionDisabled">所选仓不在仓网内，如有疑问请联系IT</p>
            </div>
            <el-option
              v-for="item in (detail.directDeliverySupplier === '0' || !detail.directDeliverySupplier) ? trimDulp(detail.simPositionList || []) : trimDulp(positionList)"
              :key="item.code"
              :label="(item.code !== -1 ? item.code : '')+item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="集货仓" prop="deliveryPosition">
          <el-select
            v-model="detail.deliveryPosition"
            filterable
            clearable
            placeholder="请选择集货仓"
            style="width:100%"
            :disabled="detail.clearedQty > 0 || !orderDetail.isCollectWhiteList || detail.referType === '03'"
            @focus="val => changeRow(val, 'deliveryPosition')"
            @change="() => selectdeliveryWarehouseCode(detail)"
          >
            <el-option
              v-for="item in trimDulp(deliveryPositionList)"
              :key="item.code + item.parentCode + item.id"
              :label="item.code+' '+item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="仓库修改原因" prop="positionModifyReason">
          <el-select
            v-model="detail.positionModifyReason"
            placeholder="请选择修改原因"
            style="width:100%"
            clearable
          >
            <el-option
              v-for="item in positionModifyReasonOptions"
              :key="item.code"
              :disabled="item.status === 'stop'"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="仓库修改详情" prop="positionModifyDetail">
          <el-input
            v-model="detail.positionModifyDetail"
            prop="customerMaterialName"
            placeholder="仓库修改详情"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>价格信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="未税单价" prop="freeTaxPrice" :disabled="orderDetail.isTax == 1">
          <el-input-number
            v-model="detail.freeTaxPrice"
            style="width:100%"
            :min="0"
            :precision="6"
            :step="1"
            @change="unTaxedChange"
            :disabled="isFree||orderDetail.isTax == 1"
          />
          <el-dropdown v-if="orderDetail.isTax != 1" trigger="click" placement="bottom">
            <el-link class="link" size="mini" :underline="false" type="primary">
              建议价格
            </el-link>
            <el-dropdown-menu slot="dropdown" :show-timeout="50">
              <el-dropdown-item style="padding: 0">
                <el-table :data="unTaxedTable" border style="width: 100%" size="mini">
                  <el-table-column prop="name" label="价格项" width="150" align="center" />
                  <el-table-column prop="price" label="含税单价" width="100" align="center" />
                  <el-table-column prop="rate" label="税率" width="100" align="center">
                    <template slot="header">
                      <RequiredColTitle>税率</RequiredColTitle>
                    </template>
                    <template slot-scope="scope">{{scope.row.rate}}</template>
                  </el-table-column>
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="含税单价" prop="taxPrice">
          <el-input-number
            v-model="detail.taxPrice"
            style="width:100%"
            :min="0"
            :precision="6"
            :step="1"
            @change="taxedChange"
            :disabled="isFree||orderDetail.isTax != 1"
          />
          <el-dropdown v-if="orderDetail.isTax == 1" trigger="click" placement="bottom">
            <el-link class="link" size="mini" :underline="false" type="primary">
              建议价格
            </el-link>
            <el-dropdown-menu slot="dropdown" :show-timeout="50">
              <el-dropdown-item style="padding: 0">
                <el-table :data="taxedTable" border style="width: 100%" size="mini">
                  <el-table-column prop="name" label="价格项" width="150" align="center" />
                  <el-table-column prop="price" label="未税单价" width="100" align="center" />
                  <el-table-column prop="rate" label="税率" width="100" align="center">
                    <template slot="header">
                      <RequiredColTitle>税率</RequiredColTitle>
                    </template>
                    <template slot-scope="scope">{{scope.row.rate}}</template>
                  </el-table-column>
                </el-table>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="商品未税总金额" prop="freeTotalPrice">
          <el-input :value="detail.freeTotalPrice||0" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="商品含税总金额" prop="taxTotalPrice">
          <el-input :value="detail.taxTotalPrice||0" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="折扣类型">
          <el-select v-model="detail.discountConditionType" placeholder="请选择" disabled clearable filterable style="width: 100%">
            <el-option
              v-for="item in dictList['discountConditionType']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣金额">
          <el-input-number
            v-model="detail.discountAmount"
            :precision="6"
            :step="1"
            :min="0"
            style="width:100%"
            placeholder="请输入折扣金额"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>其他</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="4" style="text-align:center">
        <el-form-item label="" prop="needScrapingCode" label-width="0">
          <el-checkbox v-model="detail.needScrapingCode">需要刮码</el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :span="4">
        <el-form-item label="" prop="customerDateSensitive" label-width="0">
          <el-checkbox
            v-model="detail.customerDateSensitive"
            true-label="X"
            false-label="Z"
          >
            客户交期敏感
          </el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="客户期望送达日期" prop="customerDate">
          <el-date-picker v-model="detail.customerDate" :picker-options="pickerOptions" @focus="focusDatePicker" value-format="yyyy-MM-dd" type="date" placeholder="客户期望送达日期" style="width: 100%" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="12">
        <el-form-item v-if="!isForecast" label="请求发货日期" prop="deliveryDate">
          <el-date-picker v-model="detail.deliveryDate" :disabled="detail.addType === '1'" value-format="yyyy-MM-dd" type="date" placeholder="请求发货日期" style="width: 100%" />
        </el-form-item>
      </el-col> -->
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="减少大类原因" prop="largeReduceReason">
          <el-select
            v-model="detail.largeReduceReason"
            placeholder="请选择"
            style="width:100%"
            clearable
          >
            <el-option
              v-for="item in largeReduceReasonOptions"
              :key="item.code"
              :disabled="item.status ==='stop'"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="减少大类详情" prop="largeReduceReasonDesc">
          <el-input v-model="detail.largeReduceReasonDesc" placeholder="大类减少原因说明" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="领用人" prop="demandUser">
          <el-input v-model="detail.demandUser" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="需求部门" prop="demandDepartment">
          <el-input v-model="detail.demandDepartment" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="detail.remark" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="采购备注" prop="purchaseNote">
          <el-input v-model="detail.purchaseNote" />
        </el-form-item>
      </el-col>
    </el-row>
    <template v-if="showCustom">
      <el-row :gutter="10" v-for="(custom, index) in (detail && detail.customPropertyList || [])" :key="index">
        <el-col :span="12">
          <el-form-item :label="`定制属性${index + 1}`">
            <el-input readonly disabled :value="custom.customProperty" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="`定制属性备注${index + 1}`" >
            <el-input v-model="custom.customPropertyRemark" :placeholder="custom.placeholder" maxlength="50" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <el-row :gutter="10" v-if="orderDetail.orderType==='Z018'">
      <el-col :span="12">
        <el-form-item label="租赁截止日期" prop="rentalDueDate">
          <el-date-picker v-model="detail.rentalDueDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="固定资产编号" prop="fixedAssetsId">
          <el-input v-model="detail.fixedAssetsId" placeholder="请输入固定资产编号" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
  <DiffCustomerMaterial :show-dialog.sync="showDiffDialog" :resultMaterialObj="resultMaterialObj" :inputMaterialObj="inputMaterialObj" @submit="handleDiffMaterialRelation" />
</div>
</template>

<script>
import endsWith from 'lodash/endsWith'
import * as utils from '@/utils/index'
import RequiredColTitle from '@/pages/orderSale/components/common/RequiredColTitle'
import { getFactoryList, calculateTaxPrice, calculateFreeTaxPrice, getDisabledDate, requestWithLoading } from '@/pages/orderSale/utils'
import { isServiceOrder, isForecastOrder, isFreeOrder, isEnableDirectDeliverySupplier } from '@/pages/orderSale/utils/orderType'
import { getDeliveryWarehouse, searchCustomerMaterialRelation, getSkuDetail, accurateQuery } from '@/api/orderSale'
import DividerHeader from '@/components/DividerHeader'
import customerMaterialTable from '@/pages/orderSale/components/common/customerMaterialTable.vue';
import { formatPrice } from '@/utils'
import SelectSkus from '@/pages/orderSale/components/common/SelectSkus.vue';
import DiffCustomerMaterial from '@/pages/orderSale/components/common/DiffCustomerMaterial.vue';
import { isDiffCustomerRelation } from '@/pages/orderSale/utils/index'

const defaultRules = {
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  directDeliverySupplier: [
    { required: true, message: '请选择选择直发', trigger: 'blur' }
  ],
  factory: [
    { required: true, message: '请选择工厂', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请选择发货仓', trigger: 'blur' }
  ]
}

const nullSkuRules = {
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  customerMaterialName: [
    { required: true, message: '请输入客户物料名称', trigger: 'blur' }
  ]
}

export default {
  components: {
    RequiredColTitle,
    DividerHeader,
    customerMaterialTable,
    SelectSkus,
    DiffCustomerMaterial
  },
  props: ['showDialog', 'skuDetail', 'clientDetail', 'orderDetail', 'formatSKU'],
  data () {
    const { data, sku } = this.skuDetail
    const [activeUnitItem = {}] = sku
      ? (data.packageInfoList || []).filter(item => data.quantityUnit === item.unit || data.quantityUnit === item.unitName)
      : {}
    const oldConversion = activeUnitItem.conversion
    let customerMaterialUnit = data.customerMaterialUnit
    if (data && data.customerMaterialUnit && !utils.isNumber(data.customerMaterialUnit) && this.dictList) {
      const foundItem = this.dictList['quantityUnit'].find(item => item.name === data.customerMaterialUnit)
      if (foundItem) {
        customerMaterialUnit = foundItem.code
      }
    }
    const { needScrapingCode, discountConditionType } = data
    return {
      detail: {
        ...data,
        needScrapingCode: needScrapingCode === 'X',
        discountConditionType: discountConditionType || 'ZK01',
        customerMaterialUnit
      },
      sku,
      positionList: [],
      deliveryPositionList: [],
      oldConversion,
      rules: {},
      customerMaterialTableData: [],
      loading: false,
      rate: '',
      showDiffDialog: false,
      formatedSku: {},
      resultMaterialObj: {}, // 查出来的客户物料关系
      inputMaterialObj: {}, // 页面手动输入的客户物料关系
      oldSku: {}, // 记录上一次的sku信息
      pickerOptions: {
        disabledDate: (time) => {
          const specifiedReceiptDayOfWeek = typeof this.orderDetail?.specifiedReceiptDayOfWeek === 'string' ? this.orderDetail?.specifiedReceiptDayOfWeek?.split(',') : this.orderDetail?.specifiedReceiptDayOfWeek
          const receiptTimeCategory = this.orderDetail?.receiptTimeCategory
          const check = !['Z002', 'Z014'].includes(this.orderDetail?.orderType) && !['8', 'X'].includes(this.orderDetail?.bidCustomer)
          return getDisabledDate(time, specifiedReceiptDayOfWeek, receiptTimeCategory, check)
        }
      }
    }
  },
  created () {
    this.setRules()
    this.setPositionList()
    this.changedeliveryPosition()
    // 灰度，在白名单内才会调用接口
    if (this.searchSkuSwitch) {
      this.initRate();
      this.searchCustomerMaterialRelation();
    }
  },
  computed: {
    morePositionDisabled () {
      return this.$store.state.orderCommon.morePositionDisabled
    },
    searchSkuSwitch () {
      return this.$store.state.orderCommon.searchSkuSwitch
    },
    largeReduceReasonOptions () {
      let options = this.dictList['largeReduceReason'];
      const typeOptions = options.filter(option => option.parentCode === this.orderDetail.orderType)
      if (typeOptions.length) {
        options = typeOptions
      } else {
        options = options.filter(option => option.parentCode === '')
      }
      const a = options.filter(option => option.status !== 'stop')
      const b = options.filter(option => option.status === 'stop')
      return [...a, ...b]
    },
    positionModifyReasonOptions () {
      let originOptions = this.dictList['positionModifyReason'];
      let options = originOptions;
      const typeOptions = options.filter(option => option.parentCode === this.orderDetail.orderType)
      if (typeOptions.length) {
        options = typeOptions
      } else {
        options = options.filter(option => option.parentCode === '')
      }
      // Z007 兼容历史数据
      if (originOptions && this.orderDetail.orderType === 'Z007') {
        const finalList = []
        for (let item of originOptions) {
          if (!finalList.find(fItem => fItem.code === item.code)) {
            finalList.push(item)
          } else {
            const findIndex = finalList.findIndex(fItem => fItem.code === item.code)
            if (item.parentCode === 'Z007') {
              finalList.splice(findIndex, 1)
              finalList.push(item)
            }
          }
        }
        return [...finalList.filter(e => e.status !== 'stop'), ...finalList.filter(e => e.status === 'stop')]
      }
      const a = options.filter(option => option.status !== 'stop')
      const b = options.filter(option => option.status === 'stop')
      return [...a, ...b]
    },
    showCustom () {
      return (/z001/gim.test(this.orderDetail?.orderType) && this.detail?.customPropertyList?.length > 0)
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    productIndex () {
      const { index } = this.skuDetail
      return index
    },
    isService () {
      return isServiceOrder(this.orderDetail.orderType)
    },
    isForecast () {
      return this.orderDetail && this.orderDetail.orderType
        ? isForecastOrder(this.orderDetail.orderType)
        : false
    },
    isFree () {
      return isFreeOrder(this.orderDetail.orderType)
    },
    isEnableDirectDeliverySupplier () {
      return isEnableDirectDeliverySupplier(this.orderDetail.orderType)
    },
    unTaxedTable () {
      const { factory } = this.detail
      if (this.sku) {
        const { factoryProductPriceVOMap } = this.sku
        if (factoryProductPriceVOMap && factory) {
          const priceMap = factoryProductPriceVOMap[factory]
          if (priceMap) {
            const { dealerMinPrice, suggestPrice, taxRate } = priceMap
            let res = [
              {
                name: '建议销售价',
                price: suggestPrice ? utils.formatPrice(suggestPrice) : '--',
                rate: taxRate ? `${taxRate}%` : '--'
              }
            ]
            if (this.orderDetail?.distributionChannel === '02') {
              res.push({
                name: '最低折扣价',
                price: dealerMinPrice ? utils.formatPrice(dealerMinPrice) : '--',
                rate: taxRate ? `${taxRate}%` : '--'
              })
            }
            return res;
          }
        }
      }
      return []
    },
    taxedTable () {
      const { factory } = this.detail
      if (this.sku) {
        const { factoryProductPriceVOMap } = this.sku
        if (factory && factoryProductPriceVOMap) {
          const priceMap = factoryProductPriceVOMap[factory]
          if (priceMap) {
            const { dealerMinPrice, suggestPrice, taxRate } = priceMap
            let res = [
              {
                name: '建议销售价',
                price: suggestPrice ? utils.formatPrice(suggestPrice) : '--',
                rate: taxRate ? `${taxRate}%` : '--'
              }
            ]
            if (this.orderDetail?.distributionChannel === '02') {
              res.push({
                name: '最低折扣价',
                price: dealerMinPrice ? utils.formatPrice(dealerMinPrice) : '--',
                rate: taxRate ? `${taxRate}%` : '--'
              })
            }
            return res;
          }
        }
      }
      return []
    },
    formatedTaxRate () {
      const { data } = this.skuDetail
      if (data.taxRate != null) {
        return `${Math.round(data.taxRate * 100)}%`
      }
      return '--'
    },
    factoryList () {
      const { distributionChannel, salesOrganization } = this.orderDetail
      if (distributionChannel && salesOrganization) {
        return getFactoryList(distributionChannel, salesOrganization, this.dictList)
      }
      return []
    },
    directDeliverySupplierList () {
      return this.dictList['directDeliverySupplier'].filter(item => {
        if (this.detail.addType !== '1') {
          return parseInt(item.code, 10) < 2
        } else {
          return true
        }
      })
    },
    selectedSku () {
      const sku = {
        ...this.sku,
        index: 1
      }
      return sku
    }
  },
  methods: {
    validatePrice (rule, value, callback) {
      if (!value || value === 0) {
        callback(new Error('请填写价格'));
      } else {
        callback();
      }
    },
    setRules () {
      if (!this.sku.skuNo) {
        this.rules = nullSkuRules
        this.rules.quantity = { required: false }
      } else {
        this.rules = defaultRules;
      }
      if (this.orderDetail.isTax === '1') {
        this.rules = {
          ...this.rules,
          taxPrice: [
            { required: true, message: '请选择含税单价', trigger: 'blur' }
          ]
        }
      } else {
        this.rules = {
          ...this.rules,
          freeTaxPrice: [
            { required: true, message: '请选择未税单价', trigger: 'blur' }
          ]
        }
      }
      if (this.orderDetail.orderType === 'Z018') {
        this.rules = {
          ...this.rules,
          rentalDueDate: [
            { required: true, message: '请选择租赁截止日期', trigger: 'blur' }
          ],
          fixedAssetsId: [
            { required: true, message: '请输入固定资产编号', trigger: 'blur' }
          ]
        }
      }
      if (this.orderDetail.orderType === 'Z014') {
        this.rules = {
          ...this.rules,
          customerDate: [
            { required: true, message: '请选择客户期望送达日期', trigger: 'blur' }
          ]
        }
      }
    },
    focusDatePicker () {
      const specifiedReceiptDayOfWeek = typeof this.orderDetail?.specifiedReceiptDayOfWeek === 'string' ? this.orderDetail?.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day) : this.orderDetail?.specifiedReceiptDayOfWeek?.filter(day => !!day)
      const receiptTimeCategory = this.orderDetail?.receiptTimeCategory
      if ((receiptTimeCategory === 'Z' || receiptTimeCategory === false) && specifiedReceiptDayOfWeek?.length && specifiedReceiptDayOfWeek?.every(day => !['0', '01', '02', '03', '04', '05'].includes(day))) {
        this.$message.warning('无可选日期，请修改客户指定日收货或工作日与周末均可收货!')
      }
    },
    trimDulp (positionList) {
      let ret = []
      let existsMap = {}
      positionList.forEach(item => {
        let key = item.code
        if (item.parentCode && item.id) {
          key += item.parentCode + item.id
        }
        if (item.factory) {
          key += item.factory
        }
        if (existsMap[key]) return
        existsMap[key] = true
        ret.push(item)
      })
      return ret
    },
    isFactoryDisable (row, code) {
      if (row && code) {
        const { skuFactoryPriceMap } = row
        if (skuFactoryPriceMap) {
          const c = parseInt(code, 10)
          return !(skuFactoryPriceMap && skuFactoryPriceMap[c] && skuFactoryPriceMap[c].taxRate != null)
        }
      }
      return true
    },
    unTaxedChange (value) {
      this.detail.freeTotalPrice = utils.formatPrice(value * this.detail.quantity)
      if (!isNaN(this.detail.taxRate)) {
        this.detail.taxPrice = calculateTaxPrice(value, this.detail.taxRate)
        this.detail.taxTotalPrice = utils.formatPrice(this.detail.taxPrice * this.detail.quantity)
      }
    },
    taxedChange (value) {
      this.detail.taxTotalPrice = utils.formatPrice(value * this.detail.quantity)
      if (!isNaN(this.detail.taxRate)) {
        this.detail.freeTaxPrice = calculateFreeTaxPrice(value, this.detail.taxRate)
        this.detail.freeTotalPrice = utils.formatPrice(this.detail.freeTaxPrice * this.detail.quantity)
      }
    },
    changeQuantityUnit (unit) {
      const { data } = this.skuDetail
      const list = (data.packageInfoList || []).filter(item => item.unit === unit)
      this.detail.quantity = this.detail.quantity * this.oldConversion / (list[0] || {}).conversion
      this.oldConversion = (list[0] || {}).conversion
    },
    async changeRow (value, type) {
      const { factory } = this.detail
      if (type === 'directDeliverySupplier') {
        let positionList = factory
          ? this.dictList['position'].filter(item => item.parentCode === factory)
          : []
        if (value === '1') {
          positionList = positionList.filter(item => endsWith(item.code, '04'))
        }
        this.detail.position = ''
        this.positionList = positionList
      } else if (type === 'factory') {
        const simPositionList = await this.setSimPosition(this.detail);
        this.detail.simPositionList = simPositionList;
        const positionList = this.dictList['position'].filter(item => {
          if (this.orderDetail && this.orderDetail.orderType === 'Z005') {
            return item.parentCode === value && endsWith(item.code, '04')
          }
          return item.parentCode === value
        })
        if (value === '1000' || value === '1300') {
          const position = positionList && positionList.length > 0 ? positionList[0].code : ''
          this.detail.position = position
          this.positionList = positionList
        } else {
          this.positionList = positionList
          this.detail.position = ''
        }
        this.detail.deliveryPosition = ''
      } else if (type === 'position') {
        // const orderType = this.orderDetail.orderType
        // const { mtart } = this.detail
        // if (orderType !== 'Z013' &&
        //   orderType !== 'Z018' &&
        //   orderType !== 'Z008' &&
        //   orderType !== 'Z009' &&
        //   mtart !== 'Z002'
        // ) {
        //   this.detail['directDeliverySupplier'] = (value && endsWith(String(value), '04')) ? '1' : '0'
        // }
      } else if (type === 'deliveryPosition') {
        this.changedeliveryPosition()
      }
    },
    setPositionList () {
      const { factory, directDeliverySupplier } = this.detail
      const positionList = factory ? this.dictList['position'].filter(item => item.parentCode === factory) : []
      if (this.orderDetail &&
        (isServiceOrder(this.orderDetail.orderType) ||
          directDeliverySupplier === '1' ||
          this.orderDetail.orderType === 'Z018')) {
          this.positionList = positionList.filter(item => endsWith(item.code, '04'))
      } else if (directDeliverySupplier === '2') {
        if (directDeliverySupplier === '2') {
          positionList.unshift({
            code: -1,
            name: '自动挑仓'
          })
          this.positionList = positionList
        }
      } else {
        this.positionList = positionList
      }
    },
    async setSimPosition(row) {
      const data = {
        skuSet: [row.materiel],
        factorySet: [row.factory],
        positionScope: 1
      }
      const res = await getDeliveryWarehouse(data)
      let posList = []
      if (res && res.status === 200 && Array.isArray(res.result)) {
        const codeMap = {}
        res.result.reduce((prev, next) => {
          prev.push(...next.allPosition)
          return prev
        }, []).forEach(item => {
          const key = `${item.factory}_${item.code}_${item.name}`
          if (!codeMap[key]) {
            codeMap[key] = true
            posList.push(item)
          }
        })
        console.log(posList)
      }
      return posList
    },
    changedeliveryPosition () {
      const data = {
        skuSet: [this.detail.materiel],
        factorySet: [this.detail.factory],
        positionScope: 3
      }
      getDeliveryWarehouse(data).then((res) => {
        if (res && res.result) {
          const deliveryPositionList = res.result.filter(item => item.allPosition && item.allPosition.length > 0).flatMap(
            item => {
              item.allPosition.map(val => {
                val.warehouseCode = item.warehouseCode
                return val
              })
              return item.allPosition
            })
          this.deliveryPositionList = deliveryPositionList
          this.$emit('getWarehouseCodeList', this.trimDulp(deliveryPositionList))
        }
      }).catch(error => {
        console.log(error);
      })
    },
    selectdeliveryWarehouseCode (detail) {
      detail.deliveryWarehouseCode = (this.deliveryPositionList.find(item => item.code === detail.deliveryPosition) || {}).warehouseCode || ''
    },
    submit (formName) {
      const { index } = this.skuDetail
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const { positionModifyReason, positionModifyDetail, largeReduceReasonDesc, quantity, customerMaterialQuantity } = this.detail
          if (!this.sku.skuNo && (!quantity || parseFloat(quantity) === 0) && (!customerMaterialQuantity || parseFloat(customerMaterialQuantity) === 0)) {
            this.$message({
              message: '数量和客户物料数量不能同时为0！',
              type: 'error'
            })
            return
          }
          if (largeReduceReasonDesc?.length > 50) {
            this.$message({
              message: '减少大类详情最多50字符！',
              type: 'error'
            })
          } else if (positionModifyReason && !positionModifyDetail) {
            this.$message({
              message: '仓库修改详情必须填写',
              type: 'error'
            })
          } else {
            const { needScrapingCode } = this.detail
            this.$emit('submit', {
              index,
              data: {
                ...this.detail,
                needScrapingCode: needScrapingCode ? 'X' : 'Z'
              }
            })
          }
        } else {
          return false
        }
      })
    },
    openMaterialDialog () {
      this.showMaterialDialog = true;
    },
    async searchCustomerMaterialRelation() {
      try {
        this.loading = true;
        const { customerNo } = this.orderDetail
        const { skuNo } = this.sku
        const data = [{
          customerCode: customerNo,
          zkhSkuNo: skuNo
          }]
        const res = await searchCustomerMaterialRelation({ queryList: data });
        if (res.code === 200) {
          if (Array.isArray(res.data) && res.data.length > 0) {
            this.customerMaterialTableData = res.data
            // if (this.customerMaterialTableData.length === 1) {
            //   const [item] = this.customerMaterialTableData
            //   this.initMaterialInfo(item)
            // }
          } else {
            this.customerMaterialTableData = []
          }
        } else {
          this.customerMaterialTableData = []
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    handleMaterialClick (item) {
      // this.initMaterialInfo(item)
      this.detail.customerMaterialNo = item.customerMaterialNo;
      this.detail.customerSpecificationModel = item.customerMaterialSpecification;
      this.detail.customerMaterialName = item.customerMaterialName;
      this.detail.customerMaterialUnit = item.customerMaterialUnit;
      if (
        item.customerMaterialStandardQuantity &&
        item.zkhSkuStandardQuantity &&
        parseFloat(item.customerMaterialStandardQuantity) !== 0 &&
        parseFloat(item.zkhSkuStandardQuantity) !== 0
      ) {
        this.rate = formatPrice(parseFloat(item.customerMaterialStandardQuantity) / parseFloat(item.zkhSkuStandardQuantity), 6);
        this.detail.customerMaterialQuantity = this.detail.quantity * this.rate;
      } else {
        this.rate = ''
      }
    },
    initMaterialInfo (item) {
      if (!this.detail.customerMaterialNo) {
        this.detail.customerMaterialNo = item.customerMaterialNo;
      }
      if (!this.detail.customerSpecificationModel) {
        this.detail.customerSpecificationModel = item.customerMaterialSpecification;
      }
      if (!this.detail.customerMaterialName) {
        this.detail.customerMaterialName = item.customerMaterialName;
      }
      if (!this.detail.customerMaterialUnit) {
        this.detail.customerMaterialUnit = item.customerMaterialUnit;
      }
      if (
        !this.detail.customerMaterialQuantity &&
        item.customerMaterialStandardQuantity &&
        item.zkhSkuStandardQuantity &&
        parseFloat(item.customerMaterialStandardQuantity) !== 0 &&
        parseFloat(item.zkhSkuStandardQuantity) !== 0
      ) {
        this.rate = formatPrice(parseFloat(item.customerMaterialStandardQuantity) / parseFloat(item.zkhSkuStandardQuantity), 6);
        this.detail.customerMaterialQuantity = this.detail.quantity * this.rate;
      } else {
        this.rate = ''
      }
    },
    handleQuantityChange (val) {
      if (this.searchSkuSwitch) {
        if (val !== null && this.rate) {
          this.detail.customerMaterialQuantity = val * this.rate;
        }
      }
    },
    initRate () {
      const { skuUnitCount, customerSkuUnitCount } = this.detail;
      if (
        skuUnitCount && customerSkuUnitCount &&
        parseFloat(skuUnitCount) !== 0 &&
        parseFloat(customerSkuUnitCount) !== 0
        ) {
          this.rate = formatPrice(parseFloat(customerSkuUnitCount) / parseFloat(skuUnitCount), 6);
        }
        if (!this.detail.customerMaterialQuantity) {
          this.detail.customerMaterialQuantity = formatPrice(this.detail.quantity * this.rate, 6);
      }
    },
    async handleDiffMaterialRelation (type, obj) {
      try {
        if (type === 'cancel') {
          this.sku.skuNo = this.detail.skuNo || ''
        } else {
          await this.handleSku()
          this.detail.customerMaterialNo = obj.customerMaterialNo;
          this.detail.customerSpecificationModel = obj.customerSpecificationModel;
          this.detail.customerMaterialName = obj.customerMaterialName;
          this.detail.customerMaterialUnit = obj.customerMaterialUnit;
        }
        this.showDiffDialog = false;
      } catch (err) {
        console.log(err)
      }
    },
    isDiffCustomerRelation (result, detail) {
      this.resultMaterialObj = {
        customerMaterialNo: result.customerMaterialNo,
        customerMaterialName: result.customerMaterialName,
        customerMaterialUnit: result.customerMaterialUnit,
        customerSpecificationModel: result.customerSpecificationModel
      }
      this.inputMaterialObj = {
        customerMaterialNo: detail.customerMaterialNo,
        customerMaterialName: detail.customerMaterialName,
        customerMaterialUnit: detail.customerMaterialUnit,
        customerSpecificationModel: detail.customerSpecificationModel
      }
      return isDiffCustomerRelation(this.resultMaterialObj, this.inputMaterialObj)
    },
    async handleSku () {
      this.detail = {
        ...this.detail,
        ...this.formatedSku,
        quantity: this.detail.quantity || this.formatedSku.quantity,
        customerMaterialQuantity: this.detail.customerMaterialQuantity || this.formatedSku.customerMaterialQuantity,
        freeTaxPrice: this.detail.freeTaxPrice || this.formatedSku.freeTaxPrice,
        taxPrice: this.detail.quantity || this.formatedSku.taxPrice
      };
      if (this.orderDetail.isTax === 1) {
        this.detail.freeTotalPrice = utils.formatPrice(this.detail.freeTaxPrice * this.detail.quantity)
      } else {
        this.detail.taxTotalPrice = utils.formatPrice(this.detail.taxPrice * this.detail.quantity)
      }
      await this.setPositionList();
      const simPositionList = await this.setSimPosition(this.detail);
      this.$set(this.detail, 'simPositionList', simPositionList)
      this.setRules()
      this.initRate()
      this.searchCustomerMaterialRelation()
    },
    handleDiff () {
      // 判断查出的客户物料关系和页面手动输入的是否一致，不一致则弹窗提醒
      const isDiff = this.isDiffCustomerRelation(this.formatedSku, this.detail)
      if (isDiff) {
        this.showDiffDialog = true;
      } else {
        this.handleSku();
      }
    },
    addSkuToTable (currentSelectSku) {
      if (currentSelectSku) {
        // 将下拉框失焦，避免下拉框展开
        setTimeout(() => {
          this.$refs.selectSku.$refs.select.blur()
        }, 80)
        const { skuNo } = currentSelectSku
        const { customerNo, salesOrganization, distributionChannel, productGroup } = this.orderDetail || {}
        requestWithLoading(this, getSkuDetail(skuNo, {
          customerNo,
          salesOrganization,
          distributionChannel,
          productGroup,
          orderType: this.orderType
        }), async detail => {
          this.sku = {
            ...this.sku,
            ...detail
          }
          this.formatedSku = this.formatSKU(currentSelectSku, detail)
          this.oldSku = utils.deepClone(this.detail)
          console.log(this.formatedSku, this.detail)
          if (currentSelectSku.dataSource !== '商品中心') {
            this.handleDiff();
          } else {
            // 来源为商品中心，查询是否有客户物料关系，有则比较不同，没有则按不覆盖处理
            const data = {
              sku: currentSelectSku.skuNo,
              customerCode: this.orderDetail.customerNo
            };
            const res = await accurateQuery(data);
            if (res.data.length > 0) {
              this.handleDiff();
            } else {
              await this.handleSku();
              this.detail.customerMaterialNo = this.oldSku.customerMaterialNo;
              this.detail.customerSpecificationModel = this.oldSku.customerSpecificationModel;
              this.detail.customerMaterialName = this.oldSku.customerMaterialName;
              this.detail.customerMaterialUnit = this.oldSku.customerMaterialUnit;
            }
          }
        })
      } else {
        this.sku.skuNo = ''
      }
      this.setRules()
    }
  }
}
</script>
<style scoped>
.label {
  position: relative;
}
</style>
