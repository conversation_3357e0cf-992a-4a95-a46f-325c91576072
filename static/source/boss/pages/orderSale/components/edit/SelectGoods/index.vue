<template>
<el-row :gutter="20">
  <el-col :span="12">
    <el-form-item
      class="search-row"
      label="选择商品"
    >
      <el-select
        class="search-input"
        popper-class="scroll-option"
        v-model="currentSelectSku"
        filterable
        clearable
        remote
        reserve-keyword
        :value-key="searchSkuSwitch ? 'index' : 'skuNo'"
        placeholder="请输入商品编号/名称"
        :remote-method="searchSkuList"
        :loading="isLoading"
      >
        <el-option
          v-for="(item, index) in skuList"
          :key="index + '_' + item.skuNo"
          :label="'【'+item.skuNo+'】'+item.materialDescribe"
          :value="item"
          :disabled="index===0"
        >
          <div
            v-if="searchSkuSwitch"
            class="ba-row-start selectSkuItem"
            :style="{fontWeight:index===0?'bold':'normal'}"
          >
            <div>{{ item.skuNo }}</div>
            <div>{{ `${item.materialDescribe || ''}`}}</div>
            <div>{{ `${item.customerSkuNo || ''}`}}</div>
            <div>{{ `${item.customerSkuName || ''}`}}</div>
            <div>{{ `${item.customerSkuUnitCount || ''}`}}</div>
            <div>{{ `${item.customerSkuUnit || ''}`}}</div>
            <div>{{ `${item.customerSkuSpecification || ''}`}}</div>
            <el-button type="text" @click="toDataSource(item)">{{ `${item.dataSource || ''}`}}</el-button>
          </div>
          <div
            v-else
            class="ba-row-start selectSkuItem"
            :style="{fontWeight:index===0?'bold':'normal'}"
          >
            <div>{{ item.skuNo }}</div>
            <div>{{ `${item.materialDescribe || ''}`}}</div>
          </div>
        </el-option>
      </el-select>
    </el-form-item>
  </el-col>
  <el-col :span="12">
    <el-button type="primary" @click="addSkuToTable">确认添加</el-button>
    <el-button type="primary" @click="addNullSkuToTable">新增空行</el-button>

  </el-col>
</el-row>
</template>

<script>
import { searchSkuList, searchSkuListV2 } from '@/api/orderSale'
import { sensors } from '@/utils/index.js'

export default {
  data () {
    return {
      skuList: [],
      isLoading: false,
      currentSelectSku: null,
      searchKeyWord: ''
    }
  },
  props: ['orderData'],
  computed: {
    searchSkuSwitch () {
      return this.$store.state.orderCommon.searchSkuSwitch
    }
  },
  methods: {
    searchSkuList (val) {
      this.isLoading = true;
      this.searchKeyWord = val;
      if (this.searchSkuSwitch) {
        if (val) {
          const data = {
            vague: val,
            customerCode: this.orderData?.customerNo
          }
          searchSkuListV2(data).then(res => {
            if (res.code === 200) {
              if (Array.isArray(res.data) && res.data.length > 0) {
                this.skuList = res.data.map((item, index) => ({
                  index: index + 1,
                  ...item
                }))
                this.skuList.unshift({
                  skuNo: '商品编号',
                  materialDescribe: '商品描述',
                  customerSkuNo: '客户物料号',
                  customerSkuName: '客户物料名称',
                  customerSkuUnitCount: '客户物料数量',
                  customerSkuUnit: '客户物料数量单位',
                  customerSkuSpecification: '客户物料规格型号',
                  dataSource: ''
                })
              }
            } else {
              this.skuList = [];
            }
            this.isLoading = false;
          })
        }
      } else {
        const params = val
        searchSkuList(params).then(res => {
          if (res.code === 200) {
            if (Array.isArray(res.data) && res.data.length > 0) {
              this.skuList = [
                {
                  skuNo: '商品编号',
                  materialDescribe: '商品描述'
                },
                ...res.data
              ]
            }
          }
          this.isLoading = false;
        })
      }
    },
    addSkuToTable () {
      if (this.currentSelectSku) {
        const { skuNo, materialDescribe, customerSkuNo, customerSkuName, customerSkuUnitCount, customerSkuUnit, customerSkuSpecification, dataSource, matchField } = this.currentSelectSku;
        const data = {
          key_word: this.searchKeyWord,
          sku_no: skuNo,
          product_description: materialDescribe,
          customer_materiel_no: customerSkuNo,
          customer_materiel_name: customerSkuName,
          customer_materiel_quantity: customerSkuUnitCount,
          customer_materiel_quantity_unit: customerSkuUnit,
          customer_materiel_specifications_no: customerSkuSpecification,
          data_source: dataSource,
          match_route: matchField
        }
        this.searchSkuSwitch && sensors('SoCreateSelectProductConfirmButtonClick', data) // 点击确认添加埋点
        this.$emit('selectedSku', this.currentSelectSku)
      }
    },
    addNullSkuToTable () {
      this.$emit('addNullSku')
    },
    toDataSource (item) {
      if (item.dataSource) {
        switch (item.dataSource) {
          case 'QTS':
            window.open(`https://qts-uat.zkh360.com/sales/inquiry/detail/${item.referenceNo}`);
            break;
          case 'SO':
            this.$router.jumpToSoOrderDetail({
              query: {
                soNo: item.referenceNo
              }
            });
            break;
          case 'BOSS':
          case 'BOSS_OCR':
            window.open(`/insteadOrder/maintainmentV3?id=${item.id}`)
            break;
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-input {
  width: 100%;
}

.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
    flex-shrink: 0;
  }
  div:nth-child(2) {
    width: 400px;
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
    flex-shrink: 0;
  }
  div:nth-child(3),
  div:nth-child(4),
  div:nth-child(5){
    width: 90px;
    flex-shrink: 0;
    text-overflow:ellipsis;
    overflow:hidden;
  }
  div:nth-child(6){
    width: 120px;
  }
  div:nth-child(7){
    width: 90px;
    text-overflow:ellipsis;
    overflow:hidden;
  }
}
</style>
<style lang="scss">
.scroll-option.el-select-dropdown{
    max-width: 1200px;

    .el-select-dropdown__item {
      width: fit-content !important;
    }
}

</style>
