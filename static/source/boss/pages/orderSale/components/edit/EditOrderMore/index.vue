<template>
  <el-dialog
    :show-close="false"
    title="编辑更多信息"
    :visible="showDlg"
    top="10px"
    width="830px"
    custom-class="CreateOrder-EditMore"
    @open="openDialog"
    @closed="$emit('update:showDialog', false)"
  >
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="交货信息" name="delivery">
        <DeliveryInfo
          ref="deliveryEle"
          :key="deliveryKey"
          :order-detail="orderDetail"
          :client-detail="clientDetail"
          :goodsList="goodsList"
          v-on="$listeners"
          @changeGoodsList="changeGoodsList"
          @close="closeDialog"
          @submit="submitDelivery"
        />
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="invoice">
        <InvoiceInfo
          ref="invoiceEle"
          :key="invoiceKey"
          :order-detail="orderDetail"
          :client-detail="clientDetail"
          v-on="$listeners"
          @submit="submitInvoice"
          @close="closeDialog"
        />
      </el-tab-pane>
    </el-tabs>
    <div class="ba-row-center">
      <el-button type="primary" @click="submit">确认保存</el-button>
      <el-button @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import DeliveryInfo from './EditMoreTab/DeliveryInfo'
import InvoiceInfo from './EditMoreTab/InvoiceInfo'
import * as shortid from 'shortid'
export default {
  components: {
    DeliveryInfo,
    InvoiceInfo
  },
  props: ['showDialog', 'orderDetail', 'clientDetail', 'goodsList'],
  data () {
    return {
      activeName: 'delivery',
      deliveryKey: shortid.generate(),
      invoiceKey: shortid.generate()
    }
  },
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    openDialog () {
      this.deliveryKey = shortid.generate()
      this.invoiceKey = shortid.generate()
    },
    closeDialog () {
      this.$emit('update:showDialog', false)
      this.$refs.deliveryEle.$refs['deliveryEle'].resetFields()
      this.$refs.invoiceEle.$refs['invoiceEle'].resetFields()
    },
    handleClick (tab, event) {
      console.log(tab, event)
    },
    changeGoodsList (value, field) {
      this.$emit('changeGoodsList', value, field)
    },
    submitDelivery (delivery) {
      this.$emit('submitDelivery', delivery)
    },
    submitInvoice (invoice) {
      this.$emit('submitInvoice', invoice)
    },
    submit () {
      this.$refs.deliveryEle.$refs['deliveryEle'].validate((deliveryValid) => {
        if (deliveryValid) {
          this.$refs.invoiceEle.$refs['invoiceEle'].validate((invoiceValid) => {
            if (invoiceValid) {
              const invoiceResult = this.$refs.invoiceEle.submit('invoiceEle')
              if (invoiceResult !== false) {
                this.$refs.deliveryEle.submit('deliveryEle')
                this.$emit('update:showDialog', false)
                this.$emit('getDeliveryDate')
              }
            } else {
              return false;
            }
          })
        } else {
          return false;
        }
      })
    }
  }
}
</script>
<style lang="scss">
.CreateOrder-EditMore {
  .el-dialog__body {
    padding-top: 0;
  }
}
.ba-row-center{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
