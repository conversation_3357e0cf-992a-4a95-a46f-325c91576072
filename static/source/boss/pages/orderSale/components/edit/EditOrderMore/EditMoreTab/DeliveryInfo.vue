<template>
  <el-form ref="deliveryEle" label-width="140px" :rules="rules" :model="orderData">
    <DividerHeader>订单信息</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-checkbox v-model="orderData.backupOrder" label="后补订单" />
      </el-col>
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.autoBatching"
          label="允许分批"
          @change="allowAutoBatching"
        />
      </el-col>
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.paid"
          label="已付款"
        />
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-checkbox v-model="orderData.clearSlackStock" label="清呆滞库存" />
      </el-col>
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.virtualReturn"
          label="不向客户展示"
        />
      </el-col>
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.autoDelivery"
          label="自动发货"
        />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="未发货条件">
          <el-select v-model="orderData.bidCustomer" placeholder="请选择未发货条件" style="width: 100%">
            <el-option
              v-for="item in dictList['noDeliveryReason']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
              :disabled="item.status === 'stop'"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="预收款金额" prop="collectionAmount">
          <el-input v-model="orderData.collectionAmount" placeholder="预收款金额"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" >
      <el-col :span="12">
        <el-form-item label="项目号"  prop="projectNo">
          <el-input  v-model="orderData.projectNo" placeholder="请输入项目号" maxlength="40"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="供应商代码">
          <el-input v-model="orderData.supplierAccount" placeholder="请输入供应商代码" maxlength="10" style="width: 100%"/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="付款备注" prop="paymentNote">
          <el-input v-model="orderData.paymentNote" maxlength="50" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="合同备注" prop="agreementNote">
          <el-input v-model="orderData.agreementNote" maxlength="50" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="客户期望送达日期" prop="wholeCustomerReferenceDate">
            <el-date-picker
              v-model="orderData.wholeCustomerReferenceDate"
              :disabled="disabledCustomerReferenceDate"
              :picker-options="pickerOptions"
              @focus="focusDatePicker"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              @change="handleRefDate"
              placeholder="选择日期"
              style="width:100%"
            />
          </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>随货文件要求</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.attachOrder"
          label="附订单"
        />
      </el-col>
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.attachCoa"
          label="附COA"
        />
      </el-col>
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.attachMsds"
          label="附MSDS"
        />
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.attachTds"
          label="附TDS"
        />
      </el-col>
      <el-col :span="6">
        <el-checkbox
          v-model="orderData.specifiedDocument"
          label="其他随货资料"
        />
      </el-col>
      <!-- <el-col :span="6">
        <el-checkbox
          v-model="orderData.referenceStandardShippingReq"
          label="参考标准客户出货要求文件"
        />
      </el-col> -->
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="送货单模板">
          <el-select
            v-model="orderData.deliverySlipTemplate"
            placeholder="请选择送货单模板"
          >
            <el-option
              v-for="item in dictList['deliverySlipTemplate']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="打印份数">
          <el-input-number :min="0" :max="99" :step="1" v-model="orderData.printNum"  :precision="0" placeholder="请输入打印份数"/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="orderData.dnPaperReq" field="dnPaperReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService clearable v-model="orderData.dnIncidentalWay" field="dnIncidentalWay" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="orderData.dnSignatureReq" field="dnSignatureReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="标签模板">
          <el-select
            v-model="orderData.labelTemplate"
            placeholder="请选择标签模板"
          >
            <el-option
              v-for="item in dictList['labelTemplate']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="orderData.otherLabelReq" field="otherLabelReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="orderData.fastenerLabelReq" field="fastenerLabelReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="orderData.labelPasteWay" field="labelPasteWay" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="合格证标识">
          <el-select
            v-model="orderData.certificateIdentification"
            placeholder="请选合格证标识"
          >
            <el-option
              v-for="item in dictList['certificateIdentification']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="签单返回">
          <el-select
            v-model="orderData.signingBack"
            placeholder="请选择签单返回"
          >
            <el-option
              v-for="item in dictList['signingBack']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
              :disabled="item.status==='stop'"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>仓储要求</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label-width="0" prop="hideLogo" style="padding-left:20px">
          <el-checkbox v-model="orderData.hideLogo" label="隐藏logo" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="orderData.fastenerDetect" field="fastenerDetect" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService
          v-model="orderData.packagingReq"
          dictName="dictList"
          field="packagingReq"
          :isMultiple="true"
          :isForceReset="true"
          defaultLabel="包装要求"
        />
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="orderData.fastenerSpecialPackageReq" field="fastenerSpecialPackageReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="交货其他备注">
          <el-input v-model="orderData.deliveryOtherNote" maxlength="50" />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>运输要求</DividerHeader>
    <el-row :gutter="20">
      <el-col :span="12" style="margin-top: 5px">
        <el-checkbox
          v-model="orderData.exportProcessingZone"
          label="保税区/出口加工区"
          style="margin-left: 20px"
        />
      </el-col>
      <el-col style="margin-left: -10px;" :span="12">
        <el-form-item label="预约送货" prop="scheduleDelivery">
          <el-select
            v-model="orderData.scheduleDelivery"
            placeholder="请选择预约送货方式"
            clearable
          >
            <el-option
              v-for="item in dictList['scheduleDelivery']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="orderData.specifiedReceiptDayOfWeek" field="specifiedReceiptDayOfWeek" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="orderData.specifiedReceiptTime" field="specifiedReceiptTime" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="装运条件">
          <el-select
            v-model="orderData.shippingCondition"
            placeholder="请选择装运条件"
          >
            <el-option
              v-for="item in dictList['shippingConditions']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="送卸货要求">
          <el-select
            v-model="orderData.deliveryUnloadingReq"
            placeholder="请选择送卸货要求"
          >
            <el-option
              v-for="item in dictList['deliveryUnloadingReq']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService clearable v-model="orderData.disableShipping" field="disableShipping" :limit="7" />
      </el-col>
      <!-- <el-col :span="12">
        <SelectOrderService clearable v-model="orderData.designatedShipping" field="designatedShipping" />
      </el-col> -->
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="orderData.vehicleReq" field="vehicleReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="orderData.deliveryRequirements"
          clearable
          dictName="dictList"
          dictKey="deliveryRequirement"
          field="deliveryRequirements"
          :isMultiple="true"
          :isForceReset="true"
          defaultLabel="配送员要求"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <SelectOrderService v-model="orderData.signingReq" field="signingReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService clearable v-model="orderData.forkliftRelated" field="forkliftRelated" />
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import DividerHeader from '../../../common/DividerHeader'
import { isForecastOrder } from '@/pages/orderSale/utils/orderType'
import { orderServiceFields } from '@/utils/orderService'
import SelectOrderService from '@/pages/orderSale/components/common/SelectOrderService'
import { getDisabledDate } from '@/pages/orderSale/utils'
import { isEmpty } from 'lodash'

export default {
  components: {
    DividerHeader,
    SelectOrderService
  },
  props: ['orderDetail', 'clientDetail', 'goodsList'],
  data () {
    const {
      packagingReq,
      referenceStandardShippingReq,
      autoDelivery,
      autoBatching,
      paid,
      attachOrder,
      specifiedDocument,
      exportProcessingZone,
      virtualReturn,
      bidCustomer,
      attachCoa,
      attachMsds,
      attachTds,
      hideLogo,
      shippingCondition,
      signingBack,
      deliverySlipTemplate,
      deliveryUnloadingReq,
      supplierAccount,
      labelTemplate,
      disableShipping,
      deliveryOtherNote,
      backupOrder,
      clearSlackStock,
      certificateIdentification,
      paymentNote,
      agreementNote,
      scheduleDelivery,
      printNum,
      deliveryRequirements = '',
      projectNo,
      collectionAmount,
      items
    } = this.orderDetail
    const serviceData = orderServiceFields.concat({ field: 'packagingReq', multiple: true }).reduce((prev, curr) => {
      const field = curr.field;
      const val = (curr.multiple && typeof this.orderDetail[field] === 'string') ? (this.orderDetail[field] || '').split(',').filter(Boolean) : this.orderDetail[field];
      prev[field] = val;
      return prev;
    }, {})
    return {
      orderData: {
        packagingReq,
        bidCustomer,
        referenceStandardShippingReq: referenceStandardShippingReq === 'X',
        autoDelivery: autoDelivery === 'X',
        autoBatching: autoBatching === 'X',
        paid: paid === 'X',
        attachOrder: attachOrder === 'X',
        specifiedDocument: specifiedDocument === 'X',
        exportProcessingZone: exportProcessingZone === 'X',
        virtualReturn: virtualReturn === 'X',
        attachCoa: attachCoa === 'X',
        attachMsds: attachMsds === 'X',
        attachTds: attachTds === 'X',
        hideLogo: hideLogo === 'X',
        clearSlackStock: clearSlackStock === 'X',
        backupOrder: backupOrder === 'X',
        shippingCondition,
        signingBack,
        deliverySlipTemplate,
        deliveryUnloadingReq,
        supplierAccount,
        labelTemplate,
        disableShipping,
        deliveryOtherNote,
        certificateIdentification,
        paymentNote,
        agreementNote,
        printNum,
        scheduleDelivery,
        deliveryRequirements: deliveryRequirements.split(',').filter(Boolean),
        projectNo,
        collectionAmount,
        wholeCustomerReferenceDate: autoBatching === 'Z' ? items[0].customerDate : '',
        ...serviceData
      },
      isCustomerDateRequired: false, // 客户期望送达日期是否必填
      pickerOptions: {
        disabledDate: (time) => {
          const specifiedReceiptDayOfWeek = Array.isArray(this.orderDetail?.specifiedReceiptDayOfWeek) ? this.orderDetail?.specifiedReceiptDayOfWeek : this.orderDetail?.specifiedReceiptDayOfWeek?.split(',')
          const receiptTimeCategory = this.orderDetail?.receiptTimeCategory
          const check = !['Z002', 'Z014'].includes(this.orderDetail?.orderType) && !['8', 'X'].includes(this.orderData.bidCustomer)
          return getDisabledDate(time, specifiedReceiptDayOfWeek, receiptTimeCategory, check)
        }
      }
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    disabledCustomerReferenceDate () {
      return typeof this.orderData.autoBatching === 'boolean' ? this.orderData.autoBatching : this.orderData.autoBatching === 'X'
    },
    orderServiceDict () {
      return this.$store.state.orderCommon.orderServiceDict || {}
    },
    isForecast () {
      return this.orderDetail && this.orderDetail.orderType
        ? isForecastOrder(this.orderDetail.orderType)
        : false
    },
    rules () {
      let rules = this.isForecast
        ? {
          collectionAmount: [
            {
              pattern: /^(0|[1-9]\d{0,12})(\.\d{1,2})?$/, message: '请输入有效的预收款金额', trigger: 'blur'
            }
          ]
        }
        : {
          collectionAmount: [
            {
              pattern: /^(0|[1-9]\d{0,12})(\.\d{1,2})?$/, message: '请输入有效的预收款金额', trigger: 'blur'
            }
          ]
        }
      if (this.isCustomerDateRequired) {
        rules['wholeCustomerReferenceDate'] = [{ required: true, message: '请选择客户期望送达日期', trigger: 'blur' }]
      } else {
        rules['wholeCustomerReferenceDate'] = [{ required: false }]
      }
      return rules;
    }
  },
  watch: {
    'orderData.disableShipping': {
      handler(newVal) {
        if (!isEmpty(newVal)) {
          this.orderData.designatedShipping = []
        }
      }
    },
    'orderData.designatedShipping': {
      handler(newVal) {
        if (!isEmpty(newVal)) {
          this.orderData.disableShipping = []
        }
      }
    }
  },
  methods: {
    focusDatePicker () {
      const specifiedReceiptDayOfWeek = Array.isArray(this.orderData?.specifiedReceiptDayOfWeek) ? this.orderData?.specifiedReceiptDayOfWeek?.filter(day => !!day) : this.orderData?.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day)
      const receiptTimeCategory = this.orderDetail?.receiptTimeCategory
      if ((receiptTimeCategory === 'Z' || receiptTimeCategory === false) && specifiedReceiptDayOfWeek?.length && specifiedReceiptDayOfWeek?.every(day => !['0', '01', '02', '03', '04', '05'].includes(day))) {
        this.$message.warning('无可选日期，请修改客户指定日收货或工作日与周末均可收货!')
      }
    },
    changeCheck (value, name) {
      if (value) {
        this.orderData[name] = 'X'
      } else {
        this.orderData[name] = 'Z'
      }
    },
    handleRefDate (value) {
      console.log(value)
      this.$emit('changeGoodsList', value, 'customerDate')
    },
    allowAutoBatching () {
      if (!this.orderData.autoBatching) {
        if (this.goodsList.length > 0 && this.goodsList.some(item => item.customerDate)) {
          this.isCustomerDateRequired = true;
        } else {
          this.isCustomerDateRequired = false;
        }
        // if (this.goodsList.length > 0 && this.goodsList.some(item => item.customerDate !== this.goodsList[0].customerDate)) {
        //   this.$confirm('订单发货方式修改为整单发货，请检查和确认是否清空订单行客户期望送达日期并重新维护', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning'
        //   }).then(() => {
        //     this.$emit('changeGoodsList', '', 'customerDate')
        //   }).catch(() => {
        //     this.orderData.autoBatching = true
        //   })
        // }
      } else {
        this.isCustomerDateRequired = false;
        if (this.orderData.wholeCustomerReferenceDate) {
          this.$confirm('订单发货方式修改为分批发货，请检查和确认客户期望送达日期', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.$emit('changeGoodsList', this.orderData.wholeCustomerReferenceDate, 'customerDate')
            }).catch(() => {
              this.orderData.autoBatching = false
            })
        }
      }
    },
    submit (formName) {
      this.$refs[formName].validate((valid) => {
        if (this.orderData.collectionAmount === '') {
          this.orderData.collectionAmount = '0'
        }
        if (valid) {
          this.$emit('submit', this.orderData)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.checkBox {
  margin: 20px;
  padding-left: 20px;
}
.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
</style>
<style lang="scss">
.EditOrderMore-el-select {
  width: 100%;
}
</style>
