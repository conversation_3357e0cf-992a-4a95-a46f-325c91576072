<template>
  <el-form ref="invoiceEle" :model="invoiceData.orderData" :rules="rules" label-width="140px">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="客户税号">
          <el-input v-model="invoiceData.clientData.corporationTaxNum" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开票地址和电话">
          <el-input :value="orderDetail.billingAddressAndPhone" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开户行">
          <el-input v-model="invoiceData.clientData.bankName" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="账号">
          <el-input v-model="invoiceData.clientData.bankNumber" :disabled="true" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户付款账号">
          <el-input :value="orderDetail.customerPayAccountTypeName" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="收票联系人" prop="receivingInvoiceName">
          <el-select
            v-model="invoiceData.orderData.receivingInvoiceContact"
            placeholder="选择收票联系人"
            filterable
            remote
            reserve-keyword
            style="width:100%"
            :remote-method="queryInvoiceContactList"
            :loading="loadingContact"
            @change="changeInvoiceContact"
          >
            <el-option
              v-for="(item,index) in (invoiceContactList || [])"
              :key="item.contactId"
              :label="item.contactName"
              :value="item.contactId"
              :disabled="index===0"
            >
              <div
                class="ba-row-start selectClientItem"
                :style="{fontWeight:index===0?'bold':'normal'}"
              >
                <div>{{ item.contactName }}</div>
                <div>{{ item.contactId }}</div>
                <div>{{ item.contactPhone || '--' }}</div>
                <div>{{ item.address || '--' }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="收票人电话" prop="receivingInvoicePhone">
          <el-input v-model="invoiceData.orderData.receivingInvoicePhone" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="收票地址" prop="receivingInvoiceAddress">
          <el-input v-model="invoiceData.orderData.receivingInvoiceAddress" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="寄票备注" prop="shippingInfo">
          <el-input v-model="invoiceData.orderData.shippingInfo" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxRow">
      <el-col :span="6">
        <el-form-item label-width="0" prop="invoicingByMail">
          <el-checkbox v-model="invoiceData.orderData.invoicingByMail" label="凭邮件开票" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="returnOffset">
          <el-checkbox v-model="invoiceData.orderData.returnOffset" label="退换货抵消" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="mergeBilling">
          <el-checkbox v-model="invoiceData.orderData.mergeBilling" label="合并开票" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="autoBilling">
          <el-checkbox v-model="invoiceData.orderData.autoBilling" label="自动开票" size="medium" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxRow">
      <el-col :span="6">
        <el-form-item label-width="0" prop="billingRobot">
          <el-checkbox v-model="invoiceData.orderData.billingRobot" label="开票机器人" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="showDiscount">
          <el-checkbox v-model="invoiceData.orderData.showDiscount" label="显示折扣" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="ifDocMailed">
          <el-checkbox v-model="invoiceData.orderData.ifDocMailed" label="附资料邮寄" size="medium" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="发票类型" prop="invoiceType">
          <el-select v-model="invoiceData.orderData.invoiceType" placeholder="请选择发票类型">
            <el-option
              v-for="item in dictList['invoiceType']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="合并开票要求">
          <el-select v-model="invoiceData.orderData.mergeBillingDemand" placeholder="请选择合并开票要求">
            <el-option
              v-for="item in dictList['mergeBillingDemand']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="快递公司">
          <el-select v-model="invoiceData.orderData.expressCompany" clearable placeholder="请选择快递公司">
            <el-option
                v-for="item in dictList['expressCompany']"
                :key="item.code"
                :label="item.name"
                :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="发票备注">
          <el-input v-model="invoiceData.orderData.financialNote" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import * as editOrder from '@/api/orderSale'
import { isForecastOrder } from '@/pages/orderSale/utils/orderType'
import { contactHeader as header } from '@/pages/orderSale/constants'

const contactHeader = {
  ...header,
  contactPhone: '联系人电话'
}

export default {
  props: ['orderDetail', 'clientDetail'],
  data () {
    const {
      receivingInvoiceContact,
      receivingInvoiceName,
      receivingInvoicePhone,
      receivingInvoiceAddress,
      shippingInfo,
      invoicingByMail,
      autoBilling,
      ifDocMailed,
      invoiceType,
      expressCompany,
      financialNote,
      mergeBilling,
      mergeBillingDemand,
      returnOffset,
      billingRobot,
      showDiscount
    } = this.orderDetail
    const {
      corporationTaxNum,
      invoiceAddressTelephone,
      bankName,
      bankNumber,
      paymentTermName,
      customerNumber
    } = this.clientDetail
    const invoiceContactList = [contactHeader]
    if (receivingInvoiceContact) {
      invoiceContactList.push({
        contactId: receivingInvoiceContact,
        contactName: receivingInvoiceName,
        address: receivingInvoiceAddress,
        contactPhone: receivingInvoicePhone
      })
    }
    return {
      loadingContact: false,
      invoiceData: {
        orderData: {
          receivingInvoiceContact,
          receivingInvoiceName,
          receivingInvoicePhone: receivingInvoicePhone || '--',
          receivingInvoiceAddress: receivingInvoiceAddress || '--',
          shippingInfo,
          invoicingByMail: invoicingByMail === 'X',
          autoBilling: autoBilling === 'X',
          ifDocMailed: ifDocMailed === 'X',
          mergeBilling: mergeBilling === 'X',
          returnOffset: returnOffset === 'X',
          billingRobot: billingRobot === 'X',
          showDiscount: showDiscount === 'X',
          invoiceType,
          expressCompany,
          financialNote,
          mergeBillingDemand
        },
        clientData: {
          corporationTaxNum,
          invoiceAddressTelephone,
          bankName,
          bankNumber,
          paymentTermName,
          financialNote,
          customerNumber
        }
      },
      invoiceContactList
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    isForecast () {
      return this.orderDetail && this.orderDetail.orderType
        ? isForecastOrder(this.orderDetail.orderType)
        : false
    },
    rules () {
      return this.isForecast
        ? {}
        : {
          receivingInvoiceName: [
            { required: true, message: '请选择收票联系人', trigger: 'blur' }
          ],
          receivingInvoicePhone: [
            { required: true, message: '请输入收票联系人电话', trigger: 'blur' }
          ],
          receivingInvoiceAddress: [
            { required: true, message: '请输入收票联系人地址', trigger: 'blur' }
          ]
        }
    }
  },
  methods: {
    queryInvoiceContactList (contact) {
      this.loadingContact = true
      const { customerNumber } = this.invoiceData.clientData
      const { salesOrganization, productGroup, distributionChannel } = this.orderDetail
      const param = {
        salesOrganization,
        productGroup,
        distributionChannel,
        contactName: contact,
        current: 1,
        size: 10,
        customerCode: customerNumber,
        type: 'invoice'
      }
      editOrder.getContactList(param).then(res => {
        this.loadingContact = false
        if (res && res.code === 200) {
          this.invoiceContactList = [
            contactHeader,
            ...res.data.records
          ]
        }
      })
    },
    changeInvoiceContact (val) {
      const data = this.invoiceContactList.find(item => item.contactId === val)
      if (data) {
        const { contactName, contactPhone, address } = data
        // 姓名
        this.invoiceData.orderData.receivingInvoiceName = contactName
        // 电话
        this.invoiceData.orderData.receivingInvoicePhone = contactPhone || '--'
        // 地址
        this.invoiceData.orderData.receivingInvoiceAddress = address || '--'
      }
    },
    submit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$emit('submit', this.invoiceData)
        } else {
          return false
        }
      })
    }
  }
}
</script>

<style scoped lang='scss'>
.checkBox {
  margin: 20px;
  padding-left: 20px;
}
.checkBoxRow {
  padding-left: 140px;
}
.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
</style>

<style lang="scss">
.EditOrderMore-el-select {
  width: 100%;
}
</style>
