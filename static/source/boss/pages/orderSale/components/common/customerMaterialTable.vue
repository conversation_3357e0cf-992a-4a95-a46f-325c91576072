<template>
  <el-popover trigger="click" placement="bottom" v-model="visible">
    <el-table :data="tableList" @row-click="handleClick" max-height="200px" >
      <el-table-column type="index" label="序号" width="50px"></el-table-column>
      <el-table-column label="客户物料号" prop="customerMaterialNo"></el-table-column>
      <el-table-column label="客户规格型号" prop="customerMaterialSpecification" width="100px"></el-table-column>
      <el-table-column label="客户物料名称" prop="customerMaterialName" width="100px" show-overflow-tooltip></el-table-column>
      <el-table-column label="客户物料数量" prop="customerMaterialStandardQuantity" width="100px"></el-table-column>
      <el-table-column label="客户物料单位" prop="customerMaterialUnit" width="100px"></el-table-column>
    </el-table>
    <span slot="reference" class="custom-tag">多</span>
  </el-popover>
</template>

<script>
export default {
  props: ['tableList'],
  data() {
    return {
      visible: false
    }
  },
  methods: {
    handleClick (row, column, event) {
      this.$emit('change', row)
      this.visible = false;
    }
  }
};
</script>

<style scoped>
.custom-tag {
  position:absolute;
  top: -6px;
  right: -9px;
  width: 15px;
  height: 15px;
  font-size: 12px;
  border-radius: 50%;
  background-color: #ca0b0b;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
</style>
