<template>
  <el-form-item v-if="type !== 'detail'" :label="label" :prop="field">
    <span slot="label">
      <el-tooltip v-if="field === 'labelPasteWay'" effect="dark" content="仅适用于非OEM紧固件" placement="top">
        <i class="el-icon-info"></i>
      </el-tooltip>
      {{ label }}
    </span>
    <el-select :multiple="multiple" :value="value" @change="emitChange" :placeholder="`请选择${label}`" :style="selectStyle"
      :disabled="disabledSelect" :clearable="clearable" >
      <el-option v-for="item in options" :key="item.code" :label="item.name" :value="item.code"
        :disabled="item.disabled || item.persistDisabled" />
    </el-select>
  </el-form-item>
  <span v-else>
    <el-tooltip v-if="field === 'labelPasteWay'" effect="dark" content="仅适用于非OEM紧固件" placement="top">
      <i class="el-icon-info"></i>
    </el-tooltip>
    {{ label }}：{{ detailValue }}
  </span>
</template>
<script>
import { isEmpty } from 'lodash';

export default {
  props: [
    'field',
    'value',
    'type',
    'dictName',
    'dictKey',
    'isMultiple',
    'isForceReset',
    'defaultLabel',
    'selectStyle',
    'disabledSelect',
    'limit',
    'clearable'
  ],
  computed: {
    orderServiceDict() {
      return this.$store.state.orderCommon.orderServiceDict || {};
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {};
    },
    fieldConfig() {
      return this.orderServiceDict[this.field] || {};
    },
    label() {
      return this.fieldConfig.label || this.defaultLabel;
    },
    multiple() {
      return this.fieldConfig.multiple || this.isMultiple;
    },
    forceReset() {
      return this.fieldConfig.forceReset || this.isForceReset;
    },
    detailValue() {
      if (this.type !== 'detail') {
        return '';
      }
      let value = [];
      if (!this.multiple) {
        value = [this.value];
      } else {
        value = (this.value || '').split(',');
      }
      const valueArr = [];
      value.forEach((val) => {
        const option = (this.options || []).find(({ code }) => code === val);
        if (option) {
          valueArr.push(option.name);
        }
      });
      const result = valueArr.join(',');
      if (!result && this.value) {
        return this.value
      }
      return result;
    }
  },
  data() {
    return {
      options: []
    };
  },
  watch: {
    value: {
      handler(val) {
        // 禁用货运与指定货运互斥，被对方清空时，需要把所有选项置为可选
        if (this.field === 'disableShipping' || this.field === 'designatedShipping') {
          if (isEmpty(val)) {
            this.options = this.options.map(o => ({
              ...o,
              disabled: false
            }))
          }
        }
      }
    }
  },
  mounted() {
    this.$watch(
      function () {
        return Object.keys(this.dictList).length && this.value;
      },
      function (newVal, oldVal) {
        if (newVal && !oldVal && this.dictName === 'dictList') {
          this.options = this.dictList[this.dictKey || this.field];
          this.emitChange(this.value);
        }
      },
      {
        immediate: true
      }
    );

    this.$watch(
      function () {
        return [Object.keys(this.orderServiceDict).length, this.value];
      },
      function (newVal, oldVal) {
        if (newVal[0] && (!oldVal || !oldVal[0])) {
          if (!this.dictName || this.dictName === 'orderServiceDict') {
            this.options = this.orderServiceDict[this.field] ? this.orderServiceDict[this.field].options : [];
          }
        }
        if (newVal[1] && (!oldVal || !oldVal?.[1])) {
          this.emitChange(this.value);
        }
      },
      {
        immediate: true
      }
    )
  },
  methods: {
    // 互斥选项禁用,比如1和2互斥，那么选择1之后就要把选项2禁用
    mutualExclusion(selected, option1, option2) {
      if (selected.indexOf(option1) > -1) {
        this.options = this.options.map((option) => {
          if (option.code === option2) {
            return {
              ...option,
              disabled: true
            }
          }
          return option
        })
      } else if (selected.indexOf(option2) > -1) {
        this.options = this.options.map((option) => {
          if (option.code === option1) {
            return {
              ...option,
              disabled: true
            }
          }
          return option
        })
      } else {
        this.options = this.options.map((option) => {
          return {
            ...option,
            disabled: false
          };
        });
      }
    },
    // 最多选limit项，到达上限时需要把除了已选项和无要求（code为0）项之外的其他选项置灰
    // 不能直接用el-select的multiple-limit属性，会把无要求也置灰
    maxSelectLimit(selected, limit) {
      if (selected.length >= limit) {
        this.options = this.options.map(option => {
          if (option.code === '0' || selected.indexOf(option.code) > -1) return option
          return {
            ...option,
            disabled: true
          }
        })
      } else {
        this.options = this.options.map(option => ({ ...option, disabled: false }))
      }
    },
    emitChange(val) {
      if (this.type === 'detail') {
        return;
      }

      if (this.forceReset) {
        if (this.multiple) {
          // 先判断是否选了无要求
          if (val.indexOf('0') > -1) {
            this.options = this.options.map((option) => {
              if (option.code !== '0') {
                return {
                  ...option,
                  disabled: true
                };
              }
              return option;
            });
            this.$emit('input', ['0']);
          } else if (this.field === 'dnSignatureReq' && val.indexOf('04') > -1) {
            // 签章要求的无要求选项为04
            this.options = this.options.map((option) => {
              if (option.code !== '04') {
                return {
                  ...option,
                  disabled: true
                };
              }
              return option;
            });
            this.$emit('input', ['04']);
          } else { // 否则再根据各字段的要求禁用特定选项
            if (this.fieldConfig.field === 'vehicleReq') {
              // 车辆要求：国6、国5互斥
              this.mutualExclusion(val, '10', '14')
            } else if (this.field === 'packagingReq') {
              this.mutualExclusion(val, '03', '04')
            } else if (this.field === 'disableShipping') {
              this.maxSelectLimit(val, 7);
            } else {
              this.options = this.options.map((option) => {
                return {
                  ...option,
                  disabled: false
                };
              });
            }
            this.$emit('input', val);
          }
        } else {
          this.$emit('input', '0');
        }
      } else {
        this.$emit('input', val);
      }
    }
  }
};
</script>
