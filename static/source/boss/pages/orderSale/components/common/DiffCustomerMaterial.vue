<template>
  <el-dialog
    title="客户物料关系信息确认"
    :visible.sync="dlgVisible"
    width="600px"
    append-to-body
    center
  >
    <p class="m-b font-size">
      当前选中sku及物料关系的客户物料信息与页面输入不一致，是否选择覆盖？
    </p>
    <el-table
      :data="tableData"
      border
      highlight-current-row
      style="width: 100%"
      class="m-b"
    >
      <el-table-column prop="key" label="字段" width="180" align="center">
      </el-table-column>
      <el-table-column
        prop="result"
        label="客户物料关系"
        width="180"
        align="center"
      >
        <template slot-scope="{ row }">
          <span :class="{ red: tryTrim(row.result) !== tryTrim(row.input) }">{{
            row.result
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="input" label="页面输入" align="center">
      </el-table-column>
    </el-table>
    <div class="red">
      <p>
        覆盖：若选择覆盖，则系统会根据选择sku物料关系信息替换当前页面的客户物料信息；
      </p>
      <p>
        不覆盖：若选择不覆盖，则系统会保存当前页面的客户物料信息，并产生一条新的客户物料关系信息；
      </p>
      <p>取消：还没想好，返回重新确认选择sku</p>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button
        type="primary"
        @click="$emit('submit', 'yes', resultMaterialObj)"
        >覆盖</el-button
      >
      <el-button type="primary" @click="$emit('submit', 'no', inputMaterialObj)"
        >不覆盖</el-button
      >
      <el-button type="default" @click="$emit('submit', 'cancel')"
        >取消</el-button
      >
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: ['showDialog', 'resultMaterialObj', 'inputMaterialObj'],
  data() {
    return {};
  },
  computed: {
    dlgVisible: {
      get() {
        return this.showDialog;
      },
      set(val) {
        this.$emit('update:showDialog', val);
      }
    },
    tableData() {
      const keyObj = {
        customerMaterialNo: '客户物料号',
        customerMaterialName: '客户物料名称',
        customerSpecificationModel: '客户规格型号',
        customerMaterialUnit: '客户物料单位'
      };
      let res = [];
      if (this.resultMaterialObj && this.inputMaterialObj) {
        const keys = Object.keys(keyObj);
        for (let key of keys) {
          const result = this.resultMaterialObj[key] || '';
          const input = this.inputMaterialObj[key] || '';
          const item = { key: keyObj[key], result, input };
          res.push(item);
        }
      }
      return res;
    }
  },
  methods: {
    tryTrim(str) {
      return (str && str.trim && str.trim()) || str;
    }
  }
};
</script>
<style scoped lang="scss">
.red {
  color: #ca0b0b;
}

.m-b {
  margin-bottom: 20px;
}

.font-size {
  font-size: 15px;
}
</style>
