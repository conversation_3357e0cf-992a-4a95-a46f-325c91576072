<template>
<el-select
  v-model="contactData"
  :placeholder="`选择${title}`"
  filterable
  remote
  reserve-keyword
  clearable
  style="width:100%"
  value-key="contactId"
  :disabled="disabled"
  :remote-method="remoteMethod"
  :loading="loading"
  @change="changeContact"
>
  <el-option style="color:#ccc" disabled :value="-1" v-show="contactList && contactList.length >=20">
    已展示部分联系人，其他联系人请输入字符进行查询
  </el-option>
  <el-option
    v-for="(item, index) in contactList"
    :key="item.contactId"
    :label="isAddress ? item.address : item.contactName"
    :value="item"
    :disabled="index===0"
  >
    <div
      class="ba-row-start selectClientItem"
      :style="{fontWeight:index===0?'bold':'normal'}"
    >
      <div>{{ item.contactName }}</div>
      <div>{{ item.contactId }}</div>
      <div>{{ item.contactPhone || '--' }}</div>
      <el-tooltip ref="tooltip" :content="item.address" placement="top" :disabled="disabledTooltip" :open-delay="300">
        <div><span @mouseenter="handleMouseEnter($event, index)" @mouseleave="handleMouseLeave">{{ item.address || '--' }}</span></div>
      </el-tooltip>
    </div>
  </el-option>
</el-select>
</template>

<script>
export default {
  props: ['title', 'data', 'disabled', 'contactList', 'loading', 'remoteMethod', 'isAddress'],
  computed: {
    contactData: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  data() {
    return {
      disabledTooltip: true
    }
  },
  methods: {
    changeContact (val) {
      this.$emit('changeContact', val)
    },
    handleMouseEnter(event, index) {
      const e = event.target;
      if (e.offsetWidth > e.parentNode.clientWidth) {
        this.disabledTooltip = false
        this.$refs.tooltip[index].updatePopper()
      } else {
        this.disabledTooltip = true;
      }
    },
    handleMouseLeave(event) {
      this.disabledTooltip = true;
    }
  }
}
</script>

<style scoped lang="scss">
.ba-row-start{
  display: flex;
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 400px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
