<!--
 * @Author: luozhi<PERSON>
 * @Date: 2023-11-23 14:45:59
 * @LastEditors: luozhikai
 * @LastEditTime: 2025-04-14 16:50:58
 * @Description: file content
-->
<template>
  <el-upload
    class="uploadField"
    action="/ali-upload"
    drag
    :show-file-list="true"
    :accept="acceptFileType.soCommonType"
    multiple
    :with-credentials="true"
    :limit="5"
    :data="{ appName: omsAppName }"
    :on-success="handleUploadSucess"
    :on-remove="handleUploadRemove"
    :on-error="handleUploadError"
    :on-exceed="handleUploadExceed"
    :before-upload="handleBeforeUpload"
    :on-preview="onPreview"
    :file-list="files"
    :disabled="disabled || false"
    list-type="text"
  >
    <i class="el-icon-upload"></i>
    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
  </el-upload>
</template>

<script>
import { getFileUrl } from '@/api/orderSale';
import { mapState } from 'vuex';
export default {
  data() {
    return {};
  },
  props: ['fileList', 'disabled'],
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    }),
    omsAppName() {
      return /boss-uat/.test(window.location.href) ? 'ecorp-uat' : 'ecorp-pro';
    },
    files: {
      get() {
        return this.fileList?.map((file) => {
          return {
            ...file,
            name: file.name || file.fileName
          };
        });
      },
      set(val) {
        this.$emit('update:fileList', val);
      }
    }
  },
  methods: {
    handleUpload(fileList) {
      if (fileList.every((file) => file.status === 'success')) {
        const list = fileList.map((file) => {
          const res = file.response;
          return {
            // ...file,
            fileName: file.name || file.fileName || (res && res[0] && res[0].name),
            ossKey: file.ossKey || (res && res[0] && res[0].objectKey),
            bucketName: file.bucketName || (res && res[0] && res[0].bucketName),
            attachmentType: 'general',
            printDirection: 'cross'
          };
        });
        this.files = list;
      }
    },
    handleUploadSucess(_res, _file, fileList) {
      console.log(_file);
      this.handleUpload(fileList);
    },
    handleUploadRemove(_file, fileList) {
      this.handleUpload(fileList);
    },
    handleUploadError(err) {
      this.$message.error(err.message || '上传失败');
    },
    handleUploadExceed() {
      this.$message.error('文件最多上传5个！');
    },
    async onPreview(file) {
      let url = '';
      if (file.url) {
        url = file.url;
      } else {
        // 调接口获取url，然后再跳转
        const ossKey = file?.ossKey || file?.response[0]?.objectKey;
        if (ossKey) {
          const res = await getFileUrl(ossKey);
          console.log('preview', res);
          url = res.data;
        }
      }

      if (url) window.open(url);
      else this.$message.error('无法获取文件url，预览失败');
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file, this.acceptFileType.soCommonType)) return false

      if (this.files.some((item) => item.name === file.name)) {
        this.$message.error({
          message: '文件已存在'
        });
        return false;
      }
      const size = file.size / 1024 / 1024;
      const isGtLimit = size > 10;
      let msg = '';
      let pass = true;
      if (isGtLimit) {
        pass = false;
        msg += `【${file.name}】大小：${size}M，上传文件不能超过10MB！<br/>`;
      }
      if (!pass) {
        this.$message({
          type: 'error',
          dangerouslyUseHTMLString: true,
          message: msg
        });
      }
      return pass;
    }
  }
};
</script>

<style lang="scss">
.uploadField {
  width: 500px;
  display: inline-block;

  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
    }
  }
}
</style>
