<template>
  <el-select
    ref="select"
    class="search-input"
    popper-class="scroll-option"
    v-model="currentSelectSku"
    filterable
    clearable
    remote
    reserve-keyword
    :disabled="disabled"
    :value-key="searchSkuSwitch ? 'index' : 'skuNo'"
    placeholder="请输入商品编号/名称"
    :remote-method="searchSkuList"
    @change="addSkuToTable"
    :loading="isLoading"
    @focus="handleFocus"
  >
    <el-option
      v-for="(item, index) in skuList"
      :key="index + '_' + item.skuNo"
      :label="item.skuNo"
      :value="item"
      :disabled="index===0"
    >
      <div
        class="ba-row-start selectSkuItem"
        :style="{fontWeight:index===0?'bold':'normal'}"
      >
        <div>{{ item.skuNo }}</div>
        <div>{{ `${item.materialDescribe || ''}`}}</div>
        <div>{{ `${item.customerSkuNo || ''}`}}</div>
        <div>{{ `${item.customerSkuName || ''}`}}</div>
        <div>{{ `${item.customerSkuUnitCount || ''}`}}</div>
        <div>{{ `${item.customerSkuUnit || ''}`}}</div>
        <div>{{ `${item.customerSkuSpecification || ''}`}}</div>
        <el-button type="text" @click="toDataSource(item)">{{ `${item.dataSource || ''}`}}</el-button>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { searchSkuListV2 } from '@/api/orderSale'

export default {
  data () {
    return {
      skuList: [],
      isLoading: false,
      currentSelectSku: null,
      searchKeyWord: '',
      defaultOption: {
        skuNo: '商品编号',
        materialDescribe: '商品描述',
        customerSkuNo: '客户物料号',
        customerSkuName: '客户物料名称',
        customerSkuUnitCount: '客户物料数量',
        customerSkuUnit: '客户物料数量单位',
        customerSkuSpecification: '客户物料规格型号',
        dataSource: ''
      }
    }
  },
  props: ['orderData', 'customerNo', 'selectedSku', 'disabled', 'customerMaterialNo'],
  computed: {
    searchSkuSwitch () {
      return this.$store.state.orderCommon.searchSkuSwitch
    }
  },
  mounted() {
    // 传入sku的时候初始化
    if (this.selectedSku && this.selectedSku.skuNo) {
      this.currentSelectSku = this.selectedSku
      this.skuList.push(this.defaultOption, this.selectedSku)
    }
  },
  watch: {
   'selectedSku.skuNo': {
      handler (newVal) {
        if (newVal) {
          this.currentSelectSku = this.selectedSku
          this.skuList = [this.defaultOption, this.selectedSku]
        } else {
          this.currentSelectSku = null;
        }
      }
    }
  },
  methods: {
    searchSkuList (val, customerMaterialNo) {
      this.isLoading = true;
      this.searchKeyWord = val;
      this.currentSelectSku = null;
      if (val) {
        const data = {
          vague: val,
          customerCode: this.customerNo
        }
        searchSkuListV2(data).then(res => {
          if (res.code === 200) {
            if (Array.isArray(res.data) && res.data.length > 0) {
              this.skuList = res.data.map((item, index) => ({
                index: index + 1,
                ...item
              }))
              this.skuList.unshift(this.defaultOption)
              if (customerMaterialNo) {
                this.skuList = this.skuList.filter((item, index) => index === 0 || item.customerSkuNo === customerMaterialNo)
              }
            }
          } else {
            this.skuList = [];
          }
          this.isLoading = false;
        })
      }
    },
    addSkuToTable () {
      this.$emit('handleSelectSku', this.currentSelectSku)
    },
    toDataSource (item) {
      if (item.dataSource) {
        switch (item.dataSource) {
          case 'QTS':
            window.open(`https://qts-uat.zkh360.com/sales/inquiry/detail/${item.referenceNo}`);
            break;
          case 'SO':
            this.$router.jumpToSoOrderDetail({
              query: {
                soNo: item.referenceNo
              }
            });
            break;
          case 'BOSS':
          case 'BOSS_OCR':
            window.open(`/insteadOrder/maintainmentV3?id=${item.id}`)
            break;
        }
      }
    },
    async handleFocus () {
      // 如果存在客户物料号，则基于客户物料号匹配关联的震坤行sku
      if (this.customerMaterialNo && !this.currentSelectSku) {
        this.searchSkuList(this.customerMaterialNo, this.customerMaterialNo)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.search-input {
  width: 100%;
}

.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
    flex-shrink: 0;
  }
  div:nth-child(2) {
    width: 400px;
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
    flex-shrink: 0;
  }
  div:nth-child(3),
  div:nth-child(4),
  div:nth-child(5){
    width: 90px;
    flex-shrink: 0;
    text-overflow:ellipsis;
    overflow:hidden;
  }
  div:nth-child(6){
    width: 120px;
  }
  div:nth-child(7){
    width: 90px;
    text-overflow:ellipsis;
    overflow:hidden;
  }
}
</style>
<style lang="scss">
.scroll-option.el-select-dropdown{
    max-width: 1200px;

    .el-select-dropdown__item {
      width: fit-content !important;
    }
}

</style>
