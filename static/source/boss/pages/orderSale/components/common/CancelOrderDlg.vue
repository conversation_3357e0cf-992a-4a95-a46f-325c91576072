<template>
  <el-dialog :visible.sync="dlgVisible" title="提示" width="500px" @closed="$emit('update:showDialog', false)">
      <el-form ref="cancelFormRef" :model="cancelForm" :rules="rules" label-width="170px">
        <el-form-item label="请选择取消大类原因：" prop="largeReduceReason">
          <el-select
            v-model="cancelForm.largeReduceReason"
            placeholder="请选择"
            style="width: 250px"
            value-key="code"
            clearable
          >
            <el-option
              v-for="item in largeReduceReasonOptions"
              :key="item.code"
              :disabled="item.status ==='stop'"
              :label="item.name"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="请输入取消大类详情：" prop="largeReduceReasonDesc">
          <el-input
            v-model="cancelForm.largeReduceReasonDesc"
            placeholder="原因说明"
            style="width: 250px"
            clearable
          />
        </el-form-item>
        <el-form-item label="请输入替换OA流程：" prop="materialReplaceProcessNo">
          <el-input
            v-model="cancelForm.materialReplaceProcessNo"
            placeholder="替换OA流程"
            style="width: 250px"
            clearable
          />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('update:showDialog', false)">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>
</template>
<script>
export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    },
    largeReduceReasonOptions: {
      required: true,
      type: Array,
      default: () => []
    },
    items: {
      required: false,
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      cancelForm: {
        largeReduceReason: '',
        largeReduceReasonDesc: '',
        materialReplaceProcessNo: ''
      },
      rules: {
        largeReduceReason: [
          { required: true, message: '请选择取消大类原因', trigger: 'blur' }
        ],
        largeReduceReasonDesc: [
          { required: true, message: '请输入取消大类详情', trigger: 'blur' }
        ],
        materialReplaceProcessNo: [{ required: false }]
      }
    }
  },
  computed: {
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  watch: {
    'cancelForm.largeReduceReason': function (val) {
      const isMaterialReplaceProcessNoRequired = this.items?.some(item => item.largeReduceReason !== '17') && this.cancelForm.largeReduceReason?.code === '17'
      if (isMaterialReplaceProcessNoRequired) {
        this.rules.materialReplaceProcessNo = [
            { required: true, message: '请输入替换OA流程', trigger: 'blur' }
          ]
       } else {
        this.rules.materialReplaceProcessNo = [{ required: false }]
       }
       this.$refs.cancelFormRef.clearValidate();
    }
  },
  methods: {
    submit() {
      this.$refs.cancelFormRef.validate(valid => {
        if (valid) {
          if (this.cancelForm.largeReduceReasonDesc.length > 50) {
            return this.$message.error('取消大类详情最多50个字符！')
          }
          this.$emit('submit', this.cancelForm)
        }
      })
    }
  }
}
</script>
