<template>
  <el-dialog
    title="计划行详情"
    :visible.sync="showDlg"
    :show-close="false"
    width="800px"
    @closed="$emit('update:showDialog', false)"
  >
    <el-table border :data="list ? list : []" width="100%">
      <el-table-column prop="deliveryDate" label="交货日期" align="center" width="200" />
      <el-table-column prop="itemQuantity" label="订单数量" align="center" width="180" />
      <el-table-column prop="confirmQuantity" label="确认数量" align="center" />
      <el-table-column prop="salesUnit" label="销售单位" align="center" >
        <template>
          <span>{{ unit ? getQuantityUnitName(unit) : '' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="deliveryQuantity" label="已交货数量" align="center" /> -->
      <el-table-column prop="itemPlanType" label="计划行类别" width="100" align="center" />
    </el-table>

    <div class="ba-row-center btnGroup">
      <el-button @click="$emit('update:showDialog', false)">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getQuantityUnitName } from '@/utils/order'

export default {
  props: ['showDialog', 'list', 'itemRow'],
  data () {
    return {
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    unit () {
      const { unit } = this.itemRow || {}
      return unit || ''
    }
  },
  methods: {
    getQuantityUnitName (val) {
      return getQuantityUnitName(val, this.dictList)
    }
  }
}
</script>

<style scoped lang="scss">
.btnGroup {
  margin-top: 30px;
  text-align: center;
}
.tabelCellError {
  input {
    border: 1px solid #ff4949;
  }
  &:hover {
    input {
      border: 1px solid #ff4949;
    }
  }
}
</style>
<style lang="scss">
.EditOrder-orderNum {
  input {
    text-align: center;
  }
}
</style>
