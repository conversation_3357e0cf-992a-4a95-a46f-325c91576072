<template>
   <iframe
      ref="iframe"
      :height="iframe.height"
      width="100%"
      scrolling="auto"
      :src="`/sr/operation-log?id=${soNo}`"
      frameborder="0"></iframe>
</template>

<script>
export default {
  name: 'soOperationLog',
  data () {
    return {
      container: '',
      iframe: {
        height: 0,
        width: 0
      }
    }
  },
  props: {
    soNo: {
      type: String,
      default: ''
    }
  },
  created () {
    window.onresize = this.throttle(this.resize, 800)
  },
  mounted () {
    this.container = this.$refs.iframe.parentNode
    this.resize()
  },
  methods: {
    resize () {
      try {
        let rect = this.container && this.container.getBoundingClientRect()
        this.iframe.width = rect.width - 40
        this.iframe.height = window.innerHeight - 40 - 50
      } catch (err) {
        console.log(err)
      }
    },
    debounce (fn, delay = 800) {
      let timer
      return function () {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    },
    throttle (fn, delay = 800) {
      let timer
      return function () {
        if (timer) return
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    }
  }
}
</script>
