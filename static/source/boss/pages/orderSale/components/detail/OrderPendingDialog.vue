<template>
  <el-dialog :visible.sync="dlgVisible" :title="title" width="900px">
    <Transfer
      :data="data"
      :titles="['挂起类型 挂起原因', '挂起类型 挂起原因']"
      @change="handleTransferChange"
    />
    <div slot="footer">
      <el-button type="default" @click="dlgVisible = false">取消</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Transfer from '@/components/Transfer'

export default {
  components: { Transfer },
  props: {
    showDialog: { type: Boolean, default: false },
    pendingType: { type: String, default: '' },
    checkedNotAcceptDemandReasons: { type: Array, default: () => [] },
    checkedDnOrderPendingReasons: { type: Array, default: () => [] }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    title () {
      return this.pendingType === 'cancel' ? '取消挂起' : '订单挂起'
    }
  },
  data () {
    return {
      checkedKeys: [],
      data: [],
      notAcceptDemandReasons: [],
      dnOrderPendingReasons: []
    }
  },
  created() {
    this.generateData()
  },
  methods: {
    generateData () {
      const data = [];
      const fields = ['notAcceptDemandReason', 'dnOrderPendingReason'];
      // 订单挂起展示全量挂起原因
      fields.forEach((field) => {
        (this.dictList[field] || []).forEach((item, index) => {
          if (item.status !== 'stop') {
            data.push({
              key: item.id || index, // 唯一标识
              code: item.code,
              typeCode: item.listKey || '', // 挂起类型key
              typeName: item.listName || '', // 挂起类型name
              label: item.name, // 挂起具体原因
              tagType: field === 'notAcceptDemandReason' ? 'success' : '', // 挂起类型el-tag颜色
              checked: false,
              rules: item.rules ? JSON.parse(item.rules) : null
            });
          }
        });
      });
      // 取消挂起展示 选中行已有&支持取消的原因
      if (this.pendingType === 'cancel') {
        this.data = data.filter((item) => {
          return (
            (!item.rules || item.rules.operations?.includes('cancel')) &&
            ((item.typeCode === 'notAcceptDemandReason' &&
              this.checkedNotAcceptDemandReasons.includes(item.code)) ||
            (item.typeCode === 'dnOrderPendingReason' &&
              this.checkedDnOrderPendingReasons.includes(item.code)))
          );
        });
      } else {
        this.data = data.filter(
          (item) => !item.rules || item.rules.operations?.includes('add')
        );
      }
    },
    handleTransferChange (keys) {
      this.checkedKeys = keys;
    },
    submit () {
      this.checkedKeys.forEach((key) => {
      const item = this.data.find((item) => item.key === key);
      switch (item.typeCode) {
        // 不纳入需求挂起
        case 'notAcceptDemandReason':
          this.notAcceptDemandReasons.push(item.code);
          break;
        // 交货挂起
        case 'dnOrderPendingReason':
          this.dnOrderPendingReasons.push(item.code);
          break;
      }
    });
    this.dlgVisible = false;
    this.$emit('submit', this.notAcceptDemandReasons, this.dnOrderPendingReasons);
    }
  }
}

</script>

<style scoped lang="scss">
.red {
  color: #ca0b0b;
}

.m-b {
  margin-bottom: 20px;
}

.font-size {
  font-size: 15px;
}
</style>
