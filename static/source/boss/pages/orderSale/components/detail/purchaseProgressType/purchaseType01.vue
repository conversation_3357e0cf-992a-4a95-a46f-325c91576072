<!-- 未采购 -->
<template>
  <div>
    <el-table
      v-loading="listLoading"
      :data="listData"
      border
      fit
      highlight-current-row
      stripe
      style="width: 100%"
    >
      <el-table-column label="商品编码" align="center" prop="material" />
      <el-table-column label="商品描述" align="center">
        <template slot-scope="{row}">
          <div class="multiText" :title="row.sapMaterialName">{{ row.sapMaterialName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="累计订单需求数量" align="center" prop="requiredQuantity" />
      <el-table-column label="在库数量" align="center" prop="stockQuantity" />
      <el-table-column label="在途数量" align="center" prop="transitQuantity" />
      <el-table-column label="需采数量" align="center" prop="needQuantity" />
      <el-table-column label="需求仓库" align="center" prop="warehouseName" >
        <template slot-scope="{row}">
          {{showCodeAndName(row)}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="供应状态">
        <template slot-scope="scope">
          <span :class="getSupplyStatusClass(scope.row.statusName)">{{ scope.row.statusName }}</span>
        </template>
      </el-table-column>

      <el-table-column label="采购员" align="center" prop="buyer" />

      <!-- <el-table-column align="center" label="详情">
        <template slot-scope="scope">
          <el-link type="primary" @click="viewDetailDialog(scope.row)">查看待采销售订单汇总</el-link>
        </template>
      </el-table-column>-->
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.current"
      :limit.sync="listQuery.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getPurchaseList"
    />

    <el-dialog :visible.sync="dialogStockoutTableVisible" width="1200px">
      <div slot="title">
        <span class="title-highlight">{{ detailProductNo }}</span> 缺货汇总
      </div>
      <el-table
        v-loading="listLoading4"
        :data="list4"
        border
        fit
        highlight-current-row
        height="500"
      >
        <el-table-column label="商品编码" min-width="100px" align="center" prop="productNo" />
        <!-- <el-table-column label="商品描述" min-width="100px" align="center" prop="productName" /> -->
        <el-table-column label="销售/调拨单号" min-width="100px" align="center">
          <template slot-scope="{row}">
            <div :class="{'bold-font':(row.current+'' == 'true')}">
              {{ row.salesOrderNo }}
              <span v-if="row.current+'' == 'true'">（当前）</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="订单行号" min-width="100px" align="center" prop="orderLine" />
        <el-table-column label="订单行数量" min-width="100px" align="center" prop="orderLineQuantity" />
        <el-table-column label="订单创建时间" min-width="100px" align="center" prop="orderDate" />
        <el-table-column label="订单行创建时间" min-width="100px" align="center" prop="orderLineDate" />
        <el-table-column label="缺货数量" min-width="100px" align="center">
          <template slot-scope="{row}">
            <div>{{ row.unpurchasedQuantity }}</div>
          </template>
        </el-table-column>
        <el-table-column label="客服名称" min-width="100px" align="center" prop="customerService" />
        <el-table-column label="客户要求交期" min-width="100px" align="center">
          <template slot-scope="{row}">
            <div>{{ row.customerReqDelivery }}</div>
          </template>
        </el-table-column>
        <el-table-column label="未下单天数" min-width="100px" align="center">
          <template slot-scope="{row}">
            <div :class="getStatusNameClass(row.statusName)">{{ row.statusName }}</div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total4>0"
        :total="total4"
        :page.sync="listQuery.current4"
        :limit.sync="listQuery.pageSize4"
        layout="total, prev, pager, next, jumper"
        @pagination="getList4"
      />
    </el-dialog>
  </div>
</template>
<script>
import Pagination from '@/components/Pagination'

import { getPurchaseDetail, fetchPendingSOList } from '@/api/orderSupplyList'
import { getSupplyStatusClass, getStatusNameClass } from '@/filters/index.js'

export default {
  name: 'PurchaseType01',
  components: {
    Pagination
  },
  // 父组件通过props属性传递进来的数据
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    soNo: {
      type: String,
      required: true,
      default: ''
    },
    type: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      listLoading4: false,
      list4: [],
      total4: 0,
      dialogStockoutTableVisible: false,
      detailProductNo: '',

      listLoading: false,
      soVoucherId: '',
      listQuery: {
        pageSize: 10,
        current: 1,
        current4: 1,
        pageSize4: 10
      },

      total: 0,
      listData: [], // 列表数据
      spanArr: [], // 二维数组，用于存放单元格合并规则（可能存在多列需要计算行合并，所以是二维数组）
      position: 0, // 用于存储相同项的开始index
      hasLoaded: false, // 当前组件是否已加载过
      mounted: false
    }
  },
  watch: {
    // 如果 `type` 发生改变，这个函数就会运行
    type: {
      handler (newName, oldName) {
        if (this.voucherNo && this.mounted && newName === '1') {
          this.getPurchaseList()
        }
      },
      immediate: true
    },
    soNo: {
      handler (newName, oldName) {
        // this.soVoucherId = newName
        if (newName && this.mounted) {
          this.getPurchaseList()
        }
      },
      immediate: true
    }
  },
  created () {},
  mounted () {
    this.mounted = true
    // this.getSpanArr(this.listData)
  },
  methods: {
    showCodeAndName (row) {
      let ret = ''
      if (row.warehouseCode) {
        if (row.warehouseName) {
          ret = row.warehouseCode + ' ' + row.warehouseName
        } else {
          ret = row.warehouseCode
        }
      } else {
        ret = row.warehouseName
      }
      return ret
    },
    getSupplyStatusClass: getSupplyStatusClass,
    getStatusNameClass: getStatusNameClass,
    getPurchaseList () {
      console.log('getPurchaseList...1')
      if (this.soNo || this.voucherNo) {
        this.listLoading = true
        // const param = {
        //   pageSize: this.listQuery.pageSize,
        //   current: this.listQuery.current
        // }
        getPurchaseDetail({ soNo: this.soNo || this.voucherNo, mergeAtp: true })
          .then(response => {
            if (response.code === 200) {
              if (response.data && response.data.notPurchaseItems) {
                this.listData = response.data.notPurchaseItems
                this.total = response.data.notPurchaseItemsTotal
                this.$emit('totalChange', this.total)
                // this.getSpanArr(this.listData)
              }
            } else {
              this.$notify.error(response.msg)
            }
            this.listLoading = false
            this.hasLoaded = true
          })
          .catch(e => {
            this.listLoading = false
            this.hasLoaded = true
          })
      }
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data) {
      const idx = 0 // 此处定义第0列需要计算行合并
      const prop = 'orderNo' // 此处定义第0列的属性名，用于数据比较
      this.spanArr[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1)
          this.position = 0
        } else {
          if (data[index][prop] === data[index - 1][prop]) {
            // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr[idx].push(1)
            this.position = index
          }
        }
      })
      // console.log(this.spanArr)
    },
    // 表格单元格合并
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.spanArr[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        // console.log('第' + rowIndex + '行', '第' + 0 + '列', 'rowspan:' + _row, 'colspan:' + _col)
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    getList4 () {
      this.dialogStockoutTableVisible = true
      this.listLoading4 = true
      const param = {
        sapOrderNo: this.voucherNo,
        pageSize: this.listQuery.pageSize4,
        current: this.listQuery.current4
      }
      fetchPendingSOList(this.detailProductNo, param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list4 = response.data
            this.total4 = response.totalCount || 0
            this.listLoading4 = false
          } else {
            this.$notify.error(response.msg)

            // 报错时清空数据
            this.list4 = []
            this.total4 = 0
            this.listLoading4 = false
          }
        })
        .catch(e => {
          this.listLoading4 = false
        })
    },

    // 点击查看待采销售订单汇总
    viewDetailDialog (row) {
      this.dialogStockoutTableVisible = true
      this.detailProductNo = row.productNo
      this.list4 = []
      this.total4 = 0
      this.listLoading4 = false
      this.getList4()
    }
  }
}
</script>

<style scoped>
/*  物流节点 */
.logistics_con {
  font-size: 12px;
  color: #333333;
  height: 270px;
  background-color: #ffffff;
  border: solid 1px #dfe4ed;
  padding: 0 15px;
}
.logistics_con em {
  font-weight: bold;
  font-style: normal;
}
.logistics_con .main_info {
  margin-top: 12px;
}
.logistics_con .main_info span {
  margin-right: 20px;
}
.logistics_con .dnNo {
  font-size: 12px;
}
.logistics_main {
  margin: 25px 0;
}
.logistics_con .node {
  min-width: 75px;
}
.logistics_con .node * {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #333;
  line-height: 1;
  white-space: nowrap;
}
.logistics_con .node .icon {
  width: 45px;
  height: 45px;
  background: #e2e2e2 url('~@/assets/orderSupplyList/logistics_icons.png')
    no-repeat;
  border-radius: 100%;
  margin: 10px auto;
}
.logistics_con .node.active .icon {
  background-color: #42b983;
  background-position-y: -45px;
}
.logistics_con .node01 .icon {
  background-position: 0 0;
}
.logistics_con .node02 .icon {
  background-position: -45px 0;
}
.logistics_con .node03 .icon {
  background-position: -90px 0;
}
.logistics_con .node04 .icon {
  background-position: -135px 0;
}
.logistics_con .node05 .icon {
  background-position: -180px 0;
}
.logistics_con .node .date {
  color: #666666;
  margin-top: 10px;
}
.logistics_con .arrow {
  min-width: 104px;
  line-height: 96px;
  height: 111px;
}
.detail_info {
  color: #666666;
  line-height: 20px;
}
.detail_info i {
  color: #1890ff;
  font-style: normal;
}
.detail_info em {
  color: #333333;
}
/*  前置单号列表 */
.list_con {
  margin-top: 20px;
}
/* .list_con .el-table table td {
  border-right: 0 none;
} */
</style>
