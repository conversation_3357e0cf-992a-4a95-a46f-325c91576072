<!-- 已采购 -->
<template>
  <div>
    <el-table
      v-loading="listLoading"
      :span-method="objectSpanMethod"
      :data="listData"
      border
      fit
      highlight-current-row
      stripe
      style="width: 100%">
      <el-table-column label="商品编码" align="center" prop="material" />
      <el-table-column label="商品描述" align="center">
        <template slot-scope="{row}">
          <div class="multiText" :title="row.sapMaterialName">{{ row.sapMaterialName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="采购员" align="center" prop="buyer" />
      <el-table-column align="center" label="采购单号">
        <template slot-scope="{row}">
          <el-link type="primary" @click="showProgressDetail(row)">{{ row.poNo }}</el-link>
          <span v-if="row.isTc === 1" style="color: red; display: inline-block; height: 23px;">&ensp;TC</span>
        </template>
      </el-table-column>
      <el-table-column label="采购单行号" align="center" prop="poItemNo" />
      <el-table-column label="到货仓库" align="center" prop="warehouseName" >
        <template slot-scope="{row}">
          {{showCodeAndName(row)}}
        </template>
      </el-table-column>
      <el-table-column label="采购数量" align="center" prop="omsQuantity" />
      <el-table-column label="发货数量" align="center" prop="omsTransferQuantity" />
      <el-table-column label="到货数量" align="center" prop="omsReceivedQuantity" />
      <el-table-column label="下单日期" align="center">
        <template slot-scope="{row}">
          <div>{{ row.orderDate }}</div>
        </template>
      </el-table-column>
      <el-table-column label="预计到货时间" align="center" width="120px">
        <template slot-scope="{row}">
          <div>{{ row.purchaseArrivalDate }}</div>
        </template>
      </el-table-column>
      <el-table-column label="当前状态" align="center">
        <template slot-scope="{row}">
          <div :class="getStatusNameClass(row.statusName)">{{ row.statusName }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="交货明细" align="center" width="140px">
        <template slot-scope="{row}">
          <p v-for="(vc, index) in row.vcList" :key="index">
            {{vc.deliveryNo}}
          </p>
        </template>
      </el-table-column> -->
      <el-table-column label="送货单详情" align="center">
        <template slot-scope="{row}">
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              <div>
                <div v-if="row.dnList && row.dnList.length">
                  <p style="color:black;font-weight:bold" v-if="row.dnList.length || row.vcList.length">SAP单号:</p>
                  <!-- <p>DN:</p> -->
                  <p v-for="(dn, index) in row.dnList" :key="index">
                    <el-button type="text" @click="viewOutboundDetail(dn.deliveryNo, 'dn')" >{{dn.deliveryNo}}</el-button>
                    <el-button type="text" @click="handleCopy(dn.deliveryNo, $event)">复制</el-button>
                  </p>
                </div>
                <div v-if="row.vcList && row.vcList.length">
                  <p style="color:black;font-weight:bold" v-if="row.dnList.length || row.vcList.length">VC单号:</p>
                  <!-- <p>VC:</p> -->
                  <p v-for="(vc, index) in row.vcList" :key="index">
                    <el-button type="text" @click="viewOutboundDetail(vc.deliveryNo, 'vc')" >{{vc.deliveryNo}}</el-button>
                    <el-button type="text" @click="handleCopy(vc.deliveryNo, $event)">复制</el-button>
                  </p>
                </div>
              </div>
              <div v-if="row.omsList && row.omsList.length">
                <p style="color:black;font-weight:bold">OMS单号</p>
                <p v-for="(oms, index) in row.omsList" :key="index">
                  <el-button type="text" @click="viewOutboundDetail(oms.deliveryNo, 'oms')" >{{oms.deliveryNo}}</el-button>
                  <el-button type="text" @click="handleCopy(oms.deliveryNo, $event)">复制</el-button>
                </p>
              </div>
              {{row.buyer}}
            </div>
            <el-button type="text">查看</el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column align="center" label="仓配联系人">
        <template slot-scope="{ row }">
          <div class="flex-p">
          </div>
          <p v-if="row.clerk">{{ row.clerk }} <el-button type="text" @click="handleCopy(row.clerk + ' ' + row.clerkPhone, $event)">复制</el-button></p>
          <p>{{ row.clerkPhone }}</p>
        </template>
      </el-table-column>
      <el-table-column label="当前行备注" align="center" prop="poPlanText" />
      <el-table-column label="采购单备注" align="center" prop="remark" min-width="220px">
        <template slot-scope="{row}">
          <span v-if="row.remark && row.remark.length < 30">{{row.remark}}</span>
          <el-tooltip v-else placement="top" effect="light">
            <div slot="content">
              {{row.remark}}
            </div>
            <span type="text">{{row.remark && row.remark.slice(0,27) + '...'}}</span>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="listQuery.current"
      :limit.sync="listQuery.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getPurchaseList"
    />

    <el-dialog :visible.sync="dialogStockoutTableVisible" width="1400px">
      <div slot="title">
        采购单
        <span class="title-highlight">{{ detailPurchaseNo }}</span> 对应的销售订单汇总
      </div>
      <el-table
        v-loading="listLoading4"
        :data="list4"
        border
        fit
        highlight-current-row
        height="500"
      >
        <el-table-column label="商品编码" min-width="100px" align="center" prop="productNo" />
        <el-table-column label="销售/调拨单号" min-width="100px" align="center">
          <template slot-scope="{row}">
            <div :class="{'bold-font':(row.current+'' == 'true')}">
              {{ row.salesOrderNo }}
              <span>（当前）</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="采购订单号" min-width="100px" align="center" prop="purchaseNo" />
        <el-table-column label="订单行号" min-width="100px" align="center" prop="orderLine" />
        <el-table-column label="订单行数量" min-width="100px" align="center" prop="orderQuantity" />
        <el-table-column label="订单创建时间" min-width="100px" align="center" prop="orderDate" />
        <el-table-column label="行创建时间" min-width="100px" align="center" prop="orderLineDate" />
        <el-table-column label="采购预计到货时间" min-width="120px" align="center" prop="estimatedDeliveryTime"/>
        <el-table-column label="客服" min-width="100px" align="center" prop="customerService" />
        <el-table-column label="销售人员" min-width="100px" align="center" prop="sales" />
        <el-table-column label="到货仓库" min-width="100px" align="center" prop="warehouseName" />
        <el-table-column label="客户要求交期" min-width="100px" align="center">
          <template slot-scope="{row}">
            <div>{{ row.customerReqDelivery }}</div>
          </template>
        </el-table-column>
        <el-table-column label="逾期天数" min-width="100px" align="center">
          <template slot-scope="{row}">
            <div :class="getStatusNameClass(row.statusName)">{{ row.statusName }}</div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total4>0"
        :total="total4"
        :page.sync="listQuery.current4"
        :limit.sync="listQuery.pageSize4"
        layout="total, prev, pager, next, jumper"
        @pagination="getList4"
      />
    </el-dialog>
    <el-dialog :visible.sync="dialogProgressVisible" width="800px">
      <div slot="title">
        PO:
        <span class="title-highlight">{{ progressDetail.purchaseNo }}</span> 当前状态：
        <span class="bold-font">{{ progressDetail.currentNode }}</span> 下一节点：
        <span class="bold-font">{{ progressDetail.nextNode }}</span>
      </div>
      <el-steps :active="progressDetail.activeIndex" finish-status="success">
        <el-step v-for="step in progressDetail.nodeList" :key="step.nodeNo">
          <div slot="title">
            <p>{{ step.warehouseName || '' }}</p>
            <p>{{ step.operateTime || '' }}</p>
          </div>
          <div slot="description">
            <p>{{ (step.operatorRole || '') + ' ' + (step.operator || '') }}</p>
            <p>{{ (step.orderTypeName || '') + ' ' + (step.orderNo || '') }}</p>
            <p>{{ step.nodeName || '' }}</p>
          </div>
        </el-step>
      </el-steps>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { copyToClipboard } from '@/utils/index'

import {
  getPurchaseDetail,
  fetchPurchasedPOSOList,
  fetchPurchasedProgress
} from '@/api/orderSupplyList'
import { getSupplyStatusClass, getStatusNameClass } from '@/filters/index.js'

export default {
  name: 'PurchaseType02',
  components: {
    Pagination
  },
  // 父组件通过props属性传递进来的数据
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    soNo: {
      type: String,
      required: true,
      default: ''
    },
    type: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      listLoading4: false,
      list4: [],
      total4: 0,
      dialogStockoutTableVisible: false,
      detailProductNo: '',
      detailPurchaseNo: '',
      listLoading: false,
      soVoucherId: '',
      listQuery: {
        pageSize: 10,
        current: 1,
        current4: 1,
        pageSize4: 10
      },
      progressDetail: {
        purchaseNo: '',
        currentNode: '',
        nextNode: '',
        nodeList: [],
        activeIndex: 0
      },
      progressLoading: false,
      dialogProgressVisible: false,
      total: 0,
      listData: [], // 列表数据
      spanArr: [], // 二维数组，用于存放单元格合并规则（可能存在多列需要计算行合并，所以是二维数组）
      position: 0, // 用于存储相同项的开始index
      hasLoaded: false, // 当前组件是否已加载过
      mounted: false
    }
  },
  watch: {
    // 如果 `type` 发生改变，这个函数就会运行
    type: {
      handler (newName, oldName) {
        if (this.mounted && this.voucherNo && newName === '2') {
          this.getPurchaseList()
        }
      },
      immediate: true
    },
    soNo: {
      handler (newName, oldName) {
        // this.soVoucherId = newName
        if (newName && this.mounted) {
          this.getPurchaseList()
        }
      },
      immediate: true
    }
  },
  created () {
    this.viewDetailStrategy = {
      dn: (no) => {
        this.$router.push({
          path: '/warehousing/detail/' + no,
          query: {
            type: 0,
            outboundNo: no,
            soNo: this.soNo,
            deliveryNo: no
          }
        })
      },
      oms: (no) => {
        this.$router.push({
          path: '/warehousing/detail/' + no,
          query: {
            type: 0,
            outboundNo: no,
            soNo: this.soNo,
            deliveryNo: no
          }
        })
      },
      vc: (no) => {
        window.open(`/sr/logistics?soNo=${this.soNo}&deliveryNo=${no}`)
      }
    }
  },
  mounted () {
    // this.getSpanArr(this.listData)
    this.mounted = true
  },
  methods: {
    showCodeAndName (row) {
      let ret = ''
      if (row.warehouseCode) {
        if (row.warehouseName) {
          ret = row.warehouseCode + ' ' + row.warehouseName
        } else {
          ret = row.warehouseCode
        }
      } else {
        ret = row.warehouseName
      }
      return ret
    },
    handleCopy (no, event) {
      copyToClipboard.bind(this)(no, event)
    },
    getSupplyStatusClass,
    getStatusNameClass,
    viewOutboundDetail (no, type) {
      this.viewDetailStrategy[type](no)
    },
    getPurchaseList () {
      console.log('getPurchaseList...2')
      if (this.soNo || this.voucherNo) {
        this.listLoading = true
        // const param = {
        //   pageSize: this.listQuery.pageSize,
        //   current: this.listQuery.current
        // }
        getPurchaseDetail({ soNo: this.soNo || this.voucherNo, mergeAtp: true })
          .then(response => {
            if (response.code === 200) {
              if (response.data && response.data.purchaseItems) {
                this.listData = response.data.purchaseItems
                this.listData = this.listData.map(data => {
                  let deliveryInfos = data.deliveryInfos || []
                  let dnList = deliveryInfos.filter(no => no.deliveryNo && no.deliveryNo.indexOf('815') === 0)
                  let vcList = deliveryInfos.filter(no => no.deliveryNo && no.deliveryNo.indexOf('VC-SHD') === 0)
                  let omsList = data.stoDnInfos?.filter(no => no.deliveryNo)
                  return {
                    ...data,
                    dnList,
                    vcList,
                    omsList
                  }
                })
                console.log(this.listData)
                this.total = response.data.purchaseItemsTotal
                this.$emit('totalChange', this.total)
                this.getSpanArr(this.listData)
              }
            } else {
              this.$notify.error(response.msg)
            }
            this.listLoading = false
            this.hasLoaded = true
          })
          .catch(e => {
            this.listLoading = false
            this.hasLoaded = true
          })
      }
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data) {
      const idxs = [0, 1, 2] // 此处定义第0列需要计算行合并
      const props = ['material', 'sapMaterialName', 'buyer'] // 此处定义第0列的属性名，用于数据比较
      idxs.forEach(idx => {
        this.spanArr[idx] = []
      })
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          idxs.forEach(idx => {
            this.spanArr[idx].push(1)
          })

          this.position = 0
        } else {
          let march = true
          props.forEach(prop => {
            march = march && data[index][prop] === data[index - 1][prop]
          })
          if (march) {
            // 有相同项
            idxs.forEach(idx => {
              this.spanArr[idx][this.position] += 1
              this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
            })
            // this.spanArr[idx][this.position] += 1
          } else {
            // 同列的前后两行单元格不相同
            idxs.forEach(idx => {
              this.spanArr[idx].push(1)
            })

            this.position = index
          }
        }
      })
      // console.log(this.spanArr)
    },
    // 表格单元格合并
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if ([0, 1, 2].indexOf(columnIndex) >= 0) {
        const _row = this.spanArr[columnIndex][rowIndex]
        const _col = _row > 0 ? 1 : 0
        // console.log('第' + rowIndex + '行', '第' + 0 + '列', 'rowspan:' + _row, 'colspan:' + _col)
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },

    getList4 () {
      this.dialogStockoutTableVisible = true
      this.listLoading4 = true
      const param = {
        purchaseNo: this.detailPurchaseNo,
        sapOrderNo: this.voucherNo,
        pageSize: this.listQuery.pageSize4,
        current: this.listQuery.current4
      }
      fetchPurchasedPOSOList(this.detailProductNo, param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list4 = response.data
            this.total4 = response.totalCount || 0
            this.listLoading4 = false
          } else {
            this.$notify.error(response.msg)

            // 报错时清空数据
            this.list4 = []
            this.total4 = 0
            this.listLoading4 = false
          }
        })
        .catch(e => {
          this.listLoading4 = false
        })
    },

    // 点击查看待采销售订单汇总
    viewDetailDialog (row) {
      this.dialogStockoutTableVisible = true
      this.detailProductNo = row.productNo
      this.detailPurchaseNo = row.purchaseNo
      this.list4 = []
      this.total4 = 0
      this.listLoading4 = false
      this.getList4()
    },
    showProgressDetail (row) {
      this.dialogProgressVisible = true
      this.progressLoading = true
      this.progressDetail.purchaseNo = row.poNo
      this.progressDetail.activeIndex = 0
      const param = {}
      fetchPurchasedProgress(this.progressDetail.purchaseNo, row.poItemNo, param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.progressDetail = response.data
            if (response.data.nodeList) {
              this.progressDetail.activeIndex = response.data.nodeList.filter(
                item => {
                  return item.nodeReach
                }
              ).length
            }
            this.progressLoading = false
          } else {
            this.$notify.error(response.msg)
            // 报错时清空数据
            this.progressDetail = {
              purchaseNo: '',
              currentNode: '',
              nextNode: '',
              nodeList: [],
              activeIndex: 0
            }

            this.progressLoading = false
          }
        })
        .catch(e => {
          this.progressLoading = false
        })
    }
  }
}
</script>

<style scoped>
/*  物流节点 */
.logistics_con {
  font-size: 12px;
  color: #333333;
  height: 270px;
  background-color: #ffffff;
  border: solid 1px #dfe4ed;
  padding: 0 15px;
}
.logistics_con em {
  font-weight: bold;
  font-style: normal;
}
.logistics_con .main_info {
  margin-top: 12px;
}
.logistics_con .main_info span {
  margin-right: 20px;
}
.logistics_con .dnNo {
  font-size: 12px;
}
.logistics_main {
  margin: 25px 0;
}
.logistics_con .node {
  min-width: 75px;
}
.logistics_con .node * {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #333;
  line-height: 1;
  white-space: nowrap;
}
.logistics_con .node .icon {
  width: 45px;
  height: 45px;
  background: #e2e2e2 url('~@/assets/orderSupplyList/logistics_icons.png')
    no-repeat;
  border-radius: 100%;
  margin: 10px auto;
}
.logistics_con .node.active .icon {
  background-color: #42b983;
  background-position-y: -45px;
}
.logistics_con .node01 .icon {
  background-position: 0 0;
}
.logistics_con .node02 .icon {
  background-position: -45px 0;
}
.logistics_con .node03 .icon {
  background-position: -90px 0;
}
.logistics_con .node04 .icon {
  background-position: -135px 0;
}
.logistics_con .node05 .icon {
  background-position: -180px 0;
}
.logistics_con .node .date {
  color: #666666;
  margin-top: 10px;
}
.logistics_con .arrow {
  min-width: 104px;
  line-height: 96px;
  height: 111px;
}
.detail_info {
  color: #666666;
  line-height: 20px;
}
.detail_info i {
  color: #1890ff;
  font-style: normal;
}
.detail_info em {
  color: #333333;
}
/*  前置单号列表 */
.list_con {
  margin-top: 20px;
}
/* .list_con .el-table table td {
  border-right: 0 none;
} */
</style>
