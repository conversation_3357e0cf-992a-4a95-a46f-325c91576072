<template>
  <div class="supply-status-container">
    <div>
      <el-steps :active="activeNodeIndex" finish-status="success">
        <el-step v-for="step in nodeList" :key="step.nodeNo">
          <div slot="title" class="step-title">
            <p>
              {{ (step.nodeName || '') + ' ' + (step.percentage || '') }}
            </p>
          </div>
          <div slot="description" class="step-description">
            <p>{{ step.operateTime || '' }}</p>
            <p>{{ step.operatorInfo || '' }}</p>
            <p>{{ step.warehouseName || '' }}</p>
          </div>
        </el-step>
      </el-steps>
    </div>

    <el-table
      v-loading="listLoading" :data="supplyList"
      style="width: 100%" fit height="600" :row-class-name="tableRowClassName">
      <el-table-column align="left" width="140px" label="产品名称" prop="skuName">
        <template slot-scope="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.skuName || ''"
            placement="top-start">
            <span class="max-third-row">{{ scope.row.skuName || '' }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="订货编码" align="center" prop="skuNo" />
      <el-table-column width="100px" align="center" label="订单行">
        <template slot-scope="scope">
          <span>{{ scope.row.itemNo }}</span>
        </template>
      </el-table-column>
      <el-table-column width="100px" label="发货仓" align="center" prop="warehouse" >
        <template slot-scope="{row}">
          {{showCodeAndName(row)}}
        </template>
      </el-table-column>
      <el-table-column label="订单数量" width="70px" align="center" prop="orderNum"/>
      <el-table-column label="已占用" width="70px" align="center" prop="preOccupyStockNum"/>
      <el-table-column label="已发货" width="70px" align="center" prop="deliveredNum"/>
      <el-table-column label="已集货" width="70px" align="center" prop="transferInQty">
        <template slot="header">
          <span>已集货</span>
          <el-tooltip effect="dark" content="代表在集货仓收到的数量" placement="top">
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="集货取消" width="100px" align="center" prop="deliveryCancelQty">
        <template slot="header">
          <span>集货取消</span>
          <el-tooltip effect="dark" content="代表取消集货的数量" placement="top">
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <!-- <el-table-column label="盘亏数量" width="70px" align="center" prop="inventoryLossQuantity"/> -->
      <el-table-column label="集货仓已发出" width="125px" align="center" prop="deliveryQty">
        <template slot="header">
          <span>集货仓已发出</span>
          <el-tooltip effect="dark" content="代表从集货仓发货的数量" placement="top">
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="未发货" width="70px" align="center" prop="nonDeliverNum"/>
      <el-table-column label="采购在途" width="70px" align="center" prop="purchasingNum"/>
      <el-table-column label="未采购" width="70px" align="center" prop="unPurchaseNum"/>
      <el-table-column width="80px" align="center" label="发货状态">
        <template slot-scope="scope">
          <span :class="{ 'text-danger': scope.row.deliveryStatus == 4 }">
            {{ scope.row.deliveryStatus | deliveryStatusFilter }}
          </span>
        </template>
      </el-table-column>
      <el-table-column width="120px" align="center" label="供货状态">
        <template slot-scope="{ row }">
          <span v-if="row.matchStatus != 4">
            {{ row.matchStatus | matchStatusFilter | showWareNumber(row) }}
          </span>
          <el-popover v-else placement="top" width="250" trigger="hover">
            <p class="text-danger">异常类别：{{ row.pendingOrderType }}</p>
            <p>{{ row.pendingOrderReason }}</p>
            <p>采购员：{{ row.pendingPurchaseName }}</p>
            <span class="text-danger" slot="reference">
              {{ row.matchStatus | matchStatusFilter }}
            </span>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column align="center" label="采购详情">
        <template slot-scope="{ row }">
          <div v-show="row.matchStatus == 1">
            采购员：{{ row.purchaseName }}
          </div>
          <el-button
            v-if="row.position == '1004' || row.purchasingNum"
            type="text"
            class="detail_link"
            @click="viewMatchResult(row)"
            >查看对应采购单</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" width="150px" label="发货详情">
        <template slot-scope="{ row }">
          <div v-if="row.position && row.position.indexOf('04')==-1">
            <div class="cancel-row-cover">
            <img src="@/assets/images/canceled.png" />
            已取消
            </div>
            <el-popover v-show="row.deliveredNum > 0" placement="top">
              <div v-if="row.dns">
                <el-card v-for="(item,index) in filterCardHeader(CardHeader,row.dns) " :key="index">
                  <div slot="header">
                    <span>{{ item.header }}</span>
                  </div>
                  <DnsTable :tableData="filterDns(row.dns,item)" />
                </el-card>

              </div>
              <el-button type="text" slot="reference">查看对应交货单</el-button>
            </el-popover>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="最新匹配PO记录" :visible.sync="dialogTableVisible">
      <match-Result :list="matchResult" :loading="lastPoLoading" @change-tab="changeTab" />
      <el-button
        v-show="matchResult && matchResult.length > 0"
        type="text"
        :icon="morePoLoading ? 'el-icon-loading' : ''"
        @click="queryMoreResult"
        >查看更多历史记录>></el-button>
      <el-row v-show="viewMoreFlag">
        <el-col
          v-for="(item, index) in allMatchResults"
          :key="index"
          :span="24"
        >
          <p>匹配历史记录：{{ index + 1 }}</p>
          <match-Result :list="item" :changeTab="changeTab" />
        </el-col>
        <el-col
          v-show="allMatchResults && allMatchResults.length == 0"
          :span="24"
        >
          <p>暂无更多匹配历史</p>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import DnsTable from './dnsTable'
import MatchResult from './matchResult'
import {
  fetchSupplyList,
  fetchSupplyNodeList,
  fetchMatchResult,
  fetchAllMatchResult
} from '@/api/orderSupplyList'
export default {
  name: 'SupplyStatus',
  components: {
    MatchResult,
    DnsTable
  },
  filters: {
    showWareNumber (value, row) {
      // row.showWarehousingNum = true
      // row.warehousingNum = 120
      if (row.showWarehousingNum) {
        value += `(${row.warehousingNum}出库中)`
      }
      return value
    },
    matchStatusFilter (status) {
      const statusMap = {
        '1': '未采购', // '库存已占用',
        '2': '未到齐', // '有货',
        '3': '已到齐', // '库存未占用',
        '4': '采购异常' // '缺货已采购',
        // '5': '缺货未采购',
        // '6': '已取消',
        // '7': '已完结'
      }
      return statusMap[status]
    },
    deliveryStatusFilter (status) {
      const statusMap = {
        '1': '未发货',
        '2': '部分发货',
        '3': '全部发货',
        '4': '待采购向供应商下单'
        // '5': '已向供应商下单',
        // '6': '已取消',
        // '7': '已完结'
      }
      return statusMap[status]
    }
  },
  // 父组件通过props属性传递进来的数据
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      CardHeader: [
        {
          header: '发货仓发出的交货单',
          type: '401'
        },
        {
          header: '集货仓收到的交货单',
          type: '402'
        },
        {
          header: '集货仓发出的交货单',
          type: '*'
        },
        {
          header: '集货仓取消的交货单',
          type: '404'
        }
      ],
      supplyList: null,
      nodeList: null,
      activeNodeIndex: 0,
      listLoading: false,
      lastPoLoading: false, // 最新匹配PO记录表loading
      morePoLoading: false,
      listQuery: {
        soVoucherId: this.voucherNo
      },
      matchResultQuery: {
        soVoucherId: this.voucherNo,
        soItemId: null,
        poPlanId: null,
        poVoucherId: null
      },
      dialogTableVisible: false,
      matchResult: null,
      allMatchResults: null,
      viewMoreFlag: false // 展示/隐藏 更多历史记录
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    activeName: {
      handler (newName, oldName) {
        if (!this.hasLoaded && this.voucherNo && newName === 'gy') {
          this.getSupplyList()
        }
      },
      immediate: true
    },
    voucherNo: {
      handler (newName, oldName) {
        if (!this.hasLoaded && newName) {
          this.listQuery.soVoucherId = newName
          this.getSupplyList()
        }
      },
      immediate: true
    },
    dialogTableVisible (newValue) {
      if (!newValue) {
        this.lastPoLoading = false
        this.morePoLoading = false
        this.matchResult = null
        this.allMatchResults = null
      }
    }
  },
  created () {},
  inject: ['detailVM'],
  methods: {
    showCodeAndName (row) {
      let ret = ''
      if (row.position) {
        if (row.warehouse) {
          ret = row.position + ' ' + row.warehouse
        } else {
          ret = row.position
        }
      } else {
        ret = row.warehouse
      }
      return ret
    },
    changeTab (tab) {
      this.dialogTableVisible = false
      this.$emit('change-tab', tab)
      this.$nextTick(() => {
        this.detailVM.$refs.purchaseProgress && (this.detailVM.$refs.purchaseProgress.type = '2')
      })
    },
    // 获取供应状态数据
    getSupplyList () {
      this.listLoading = true
      fetchSupplyList(this.listQuery)
        .then(response => {
          if (response.code === 200) {
            this.supplyList = response.data
            // this.supplyList[0].dns = [
            //   '12xqf415',
            //   '1152415',
            //   '12452115'
            // ]
          } else {
            this.$notify.error(response.msg)
          }
          this.listLoading = false
          this.hasLoaded = true
        })
        .catch(e => {
          this.listLoading = false
          this.hasLoaded = true
        })
      fetchSupplyNodeList(this.voucherNo)
        .then(response => {
          if (response.code === 200) {
            this.nodeList = response.data
            const index = this.nodeList.findIndex(item => {
              return item.nodeReach !== true
            })
            this.activeNodeIndex = index === -1 ? this.nodeList.length : index
          }
        })
        .catch(e => {})
    },
    // 获取最新匹配PO记录
    getMatchResult () {
      this.lastPoLoading = true
      this.allMatchResults = null
      this.viewMoreFlag = false
      fetchMatchResult(this.matchResultQuery)
        .then(response => {
          if (response.code === 200) {
            this.matchResult = response.data
            if (this.matchResult && this.matchResult.length > 0) {
              this.matchResultQuery.soItemId = this.matchResult[0].soItemId
              this.matchResultQuery.poVoucherId = this.matchResult[0].poVoucherId
              this.matchResultQuery.poPlanId = this.matchResult[0].poPlanId
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.lastPoLoading = false
        })
        .catch(e => {
          this.lastPoLoading = false
        })
    },
    // 获取最新5调匹配PO记录
    getAllMatchResult (data) {
      this.morePoLoading = true
      fetchAllMatchResult(this.matchResultQuery)
        .then(response => {
          if (response.code === 200 && response.data) {
            response.data && response.data.splice(0, 1)
            this.allMatchResults = response.data
          } else {
            this.$notify.error(response.msg)
          }
          this.morePoLoading = false
        })
        .catch(e => {
          this.morePoLoading = false
        })
    },
    // 点击查看对应采购明细
    viewMatchResult (row) {
      this.dialogTableVisible = true
      this.matchResultQuery.soItemId = row.sapItemNo //  row.soItemId
      this.matchResultQuery.poPlanId = '' // row.poPlanId
      this.matchResultQuery.poVoucherId = '' // row.poVoucherId
      this.matchResult = null
      this.getMatchResult()
    },
    // 点击请求更多匹配数据（最多5条）
    queryMoreResult () {
      if (!this.viewMoreFlag) {
        // this.loadMoreFlag = true
        this.getAllMatchResult()
      }
      this.viewMoreFlag = !this.viewMoreFlag
    },
    // 针对取消的订单行进行样式设置
    tableRowClassName ({ row, rowIndex }) {
      if (row.cancelFlag) {
        // const r = Math.random()
        // if (r > 0.5) {
        return 'cancel-row'
      }
    },
    filterCardHeader (CardHeader, dns) {
      if (dns.findIndex(value => value.endPosition !== '') !== -1) {
        return CardHeader
      }
      return CardHeader.slice(0, 1)
    },
    filterDns (dns, item) {
      if (!(dns.findIndex(value => value.endPosition !== '') !== -1) && item.header === '发货仓发出的交货单') {
        return dns.filter(val => val.type !== '401' && val.type !== '402' && val.type !== '403' && val.type !== '404' && val.status !== 'CANCEL')
      }
      if (item.header === '发货仓发出的交货单') {
        return dns.filter(val => (val.type === '401' || val.type === '403') && val.status !== 'CANCEL')
      }
      if (item.header === '集货仓收到的交货单') {
        return dns.filter(val => (val.type === '402' || val.type === '403') && val.status !== 'CANCEL')
      }
      if (item.header === '集货仓发出的交货单') {
        return dns.filter(val => val.type !== '401' && val.type !== '402' && val.type !== '403' && val.type !== '404' && val.status !== 'CANCEL')
      }
      return dns.filter(val => val.type === item.type && val.status !== 'CANCEL')
    }
  }
}
</script>

<style lang="scss" scoped>
.text-danger {
  color: #f56c6c;
}
.detail_link {
  padding: 0;
  white-space: unset;
  line-height: 1.35;
}
.step-title {
  p {
    margin: 0;
  }
}
.step-description {
  p {
    margin: 0;
  }
}
</style>
<style lang="scss">
.supply-status-container {
  .el-table {
    .el-table__row {
      .cell {
        position: relative;
        .cancel-row-cover {
          color: rgb(216, 30, 6);
          font-size: 16px;
          padding: 0 20px;
          width: 100%;
          height: 40px;
          display: none;
          justify-content: space-around;
          align-items: center;
          text-align: center;
          transform: rotate(7deg);
          img {
            flex: 0 0 20px;
            display: block;
            height: 20px;
          }
        }
      }
      &.cancel-row {
        background: #cccccc;
        // pointer-events: none;
        .cancel-row-cover {
          display: flex;
        }
      }
    }
  }
  .max-third-row {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }
}
</style>
// 1191000426
