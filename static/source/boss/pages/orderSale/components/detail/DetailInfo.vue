<template>
  <div>
    <!-- 页面的所有弹框组件 -->
    <CustomerDetail
      :show-dialog.sync="showCustomerDetail"
      :customer.sync="customer"
      :so-info.sync="soInfo"
    />
    <MoreInfo
      :show-dialog.sync="showMoreInfo"
      :so-info.sync="soInfo"
      :customer-info.sync="customer"
    />
    <ProductDetail
      :show-dialog.sync="showProductDetail"
      :so-info.sync="soInfo"
      :current-sku.sync="currentSku"
      @next="gotoNext"
      @prev="gotoPrev"
    />
    <ProductAllocate
      :show-dialog.sync="showProductAllocate"
      :item-no-list="orderItemNos"
      :so-info.sync="soInfo"
      @success="refresh"
    />
    <SelectDNPlan
      :show-dialog.sync="showSelectDNPlan"
      :show-add-so-no="false"
      :so-no-info="soNoInfo"
      @submit="handlePlanSubmit"
    />
    <el-dialog :visible.sync="showCancelOrderDialog" title="提示" width="500px">
      <div>
        <span>请选择取消大类原因：&nbsp;&nbsp;</span>
        <el-select
          v-model="largeReduceReason"
          placeholder="请选择"
          style="width: 250px"
          value-key="code"
          clearable
        >
          <el-option
            v-for="item in largeReduceReasonOptions"
            :key="item.code"
            :disabled="item.status ==='stop'"
            :label="item.name"
            :value="item"
          />
        </el-select>
      </div>
      <div style="margin-top: 10px">
        <span>请输入取消大类详情：&nbsp;&nbsp;</span>
        <el-input
          v-model="largeReduceReasonDesc"
          placeholder="原因说明"
          style="width: 250px"
          clearable
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showCancelOrderDialog = false">取 消</el-button>
        <el-button type="primary" @click="cancelOrderHandle">确 定</el-button>
      </span>
    </el-dialog>
    <CancelOrderDlg
      :show-dialog.sync="showCancelOrderPartDialog"
      :largeReduceReasonOptions="largeReduceReasonOptions"
      :items="selectedItems"
      @submit="cancelOrderPartHandle"
    />
    <!-- 基本信息 -->
    <DividerHeader>
      <div class="row-title">
        基本信息
        <RowMore @fold="(v) => handleFold('baseInfo', v)" :fold="fold.baseInfo" />
      </div>
    </DividerHeader>
    <div class="baseInfo" v-show="!fold.baseInfo">
      <div class="ba-row-between title">
        <div>
          <span
            style="font-size: 20px;"
          >{{ soInfo.salesOrganization | dict(dictList,'salesOrganization') }}</span>
          <span
            style="font-size: 20px;margin-left: 15px;"
          >{{ soInfo.orderType | dict(dictList,'soCategory') }}</span>
        </div>
        <div>
          <el-button
            type="primary"
            :disabled="soInfo.orderStatus==='cancel' || soInfo.orderType == 'Z002' || ('Z014' == soInfo.orderType) && soInfo.creator == 'data-center-front'"
            @click="handleCommonPermissionCheck(editOrder)"
          >修改订单
          </el-button>
          <el-button
            type="primary"
            @click="init"
          >刷新订单
          </el-button>
          <el-button
            type="primary"
            :disabled="isPermissionDisabled"
            @click="handleCommonPermissionCheck(batchDeliver)"
          >整单交货
          </el-button>
          <el-button
            type="primary"
            :disabled="isContractDisabled"
            @click="exportContract"
          >导出合同
          </el-button>
          <el-button
            type="primary"
            plain
            :disabled="!isForecast"
            @click="handleCommonPermissionCheck(closeOrder)"
          >关闭订单
          </el-button>
          <el-button
            type="primary"
            plain
            :disabled="soInfo.orderStatus==='cancel'"
            @click="handleCommonPermissionCheck(cancelOrder)"
            v-show="false"
          >取消订单
          </el-button>
          <!--          <el-popconfirm title="确定取消订单吗？" @onConfirm="cancelOrderHandle">-->
          <!--            <el-button slot="reference" type="primary" plain :disabled="soInfo.orderStatus==='cancel'">取消订单</el-button>-->
          <!--          </el-popconfirm>-->
          <!-- 后面再做导出 -->
          <!--          <el-button icon="el-icon-download">导出订单</el-button>-->
        </div>
      </div>
      <el-row :gutter="10">
        <el-col :span="6">OMS订单号：{{ soInfo.soNo }}</el-col>
        <el-col :span="6">
          外围订单号：{{ soInfo.orderNo }}
          <el-tag
            v-if="soInfo.preOrderTag==='01'"
            type="danger"
            effect="plain">
            待客户审批
          </el-tag>
        </el-col>
        <el-col :span="6">订单来源：{{ soInfo.orderSource }}</el-col>
        <el-col :span="6">订单状态：{{ soInfo.orderStatus | orderStatus }}</el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          SAP订单号：{{ soInfo.sapOrderNo }}
          <el-button type="primary" size="mini" plain @click="handleSyncSap">同步SAP</el-button>
        </el-col>
        <el-col :span="6">下发SAP订单状态：
          <span :style="soInfo.requestSapStatus==='fail'?'color:red':''">{{ soInfo.requestSapStatusMsg }}</span>
        </el-col>
        <el-col :span="6">SAP返回结果：
          <span :style="soInfo.requestSapStatus!=='success'?'color:red':''">{{ soInfo.sapRespInfo }}</span>
        </el-col>
        <el-col :span="6">草稿订单完结状态：
          <el-tag v-if="soInfo.sketchOrderCompleted === '0'" type="warning">未完结</el-tag>
          <el-tag v-if="soInfo.sketchOrderCompleted === '1'" type="success">完结</el-tag>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">订单时间：{{ soInfo.orderCreateTime }}</el-col>
        <el-col :span="6">创建者：{{ soInfo.creator }}</el-col>
        <el-col :span="6">
          直/分销渠道：{{ soInfo.distributionChannel | dict(dictList,'distributionChannel') }}
        </el-col>
        <el-col :span="6">
          下单方式：{{ soInfo.orderLabel | dict(dictList,'orderLabel') }}
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">客服：{{ soInfo.customerServiceName }}</el-col>
        <el-col :span="6">客服主管：{{ soInfo.customerServiceSupervisorName }}</el-col>
        <el-col :span="6">销售：{{ soInfo.sellerName }}</el-col>
        <el-col :span="6">销售主管：{{ soInfo.salesManagerName }}</el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">EVM运营：{{ customer.evmOperator }}</el-col>
        <el-col :span="6">
          开票冻结：
          <el-tag v-for="df in freezeBillReasonList"
                  :key="'invoiceFreeze'+df"
                  style="margin-right:10px"
                  type="success"
          >
            {{ df | dict(dictList,'invoiceFreeze') }}
          </el-tag>
          <el-tag v-if="freezeBillReasonList.length===0" type="info">无</el-tag>
          <el-button
            type="primary"
            size="mini"
            v-if="soInfo.billingFreeze && canFreezeBill"
            :disabled="soInfo.orderStatus==='cancel'"
            @click="handleFreezeOrder('bill')"
          >解冻
          </el-button>
        </el-col>
        <el-col :span="6">
          交货冻结：
          <el-tag v-for="df in showFreezeReasonList"
                  :key="'deliveryFreeze'+df"
                  style="margin-right:10px"
                  type="success"
          >
            {{ df | dict(dictList,'deliveryFreeze') }}
          </el-tag>
          <el-tag v-if="showFreezeReasonList.length===0" type="info">无</el-tag>
          <el-button
            type="primary"
            size="mini"
            v-if="showFreezeReasonList && canFreezeDelivery"
            :disabled="soInfo.orderStatus==='cancel'"
            @click="handleFreezeDelivery()"
          >解冻
          </el-button>
        </el-col>
        <el-col :span="6" v-if="soInfo.oaProcessNo">OA流程编号：<el-link :href="getOaUrl" target="_blank" type="primary" style="vertical-align:baseline;">{{ soInfo.oaProcessNo }}</el-link></el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="24">
          <div class="freeze-left">
            信用冻结:
            <div class="credit-freeze">
              <el-tag v-if="!isCreditLimitFreeze && soInfo.overdueFreeze === undefined && (soInfo.timeoutUninvoicedFreeze === undefined || ['00', '01'].includes(soInfo.timeoutUninvoicedFreeze))" type="info">无</el-tag>
              <div class="credit-label" v-if="isCreditLimitFreeze">
                <span>{{ getCreditFreeze(soInfo.creditLimitFreeze) }}</span>
              </div>
              <div class="credit-label" v-if="soInfo.timeoutUninvoicedFreeze !== undefined && !['00', '01'].includes(soInfo.timeoutUninvoicedFreeze)">
                <span>{{ getTimeoutFreeze(soInfo.timeoutUninvoicedFreeze) }}</span>
              </div>
              <div class="credit-label" v-if="soInfo.overdueFreeze !== undefined">
                <span>{{ getOverDueFreeze(soInfo.overdueFreeze) }}</span>
              </div>
              <el-button
                type="primary"
                size="mini"
                class="gap"
                v-if="soInfo.creditLimitFreeze === '30' || (soInfo.overdueFreeze != null && soInfo.overdueFreeze !== '31') || soInfo.timeoutUninvoicedFreeze === '02'"
                @click="handleFreeze(soInfo.creditLimitFreeze, soInfo.overdueFreeze, soInfo.timeoutUninvoicedFreeze)"
              >解冻
              </el-button>
            </div>
            <el-popover
              v-if="isCreditLimitFreeze && soInfo.creditLimitFreeze === '30' "
              placement="top-start"
              width="250"
              trigger="click"
            >
              <span v-loading="leftCreditLimitLoading">当前客户额度为：{{ leftCreditLimit }}</span>
              <el-button slot="reference" @click="checkCreditResult">查看剩余额度</el-button>
            </el-popover>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">订单依据：{{ soInfo.orderBasisName }}</el-col>
        <el-col :span="6">订单依据关联单号：{{ soInfo.orderBasisContent }}</el-col>
        <el-col :span="6">草稿订单号：{{ soInfo.sketchOrderNo }}</el-col>
      </el-row>
    </div>

    <!-- 客户信息 -->
    <DividerHeader>
      <div class="row-title">
        客户信息
        <RowMore @fold="(v) => handleFold('clientInfo', v)" :fold="fold.clientInfo" />
      </div>
    </DividerHeader>
    <div class="clientInfo" v-show="!fold.clientInfo">
      <div class="ba-row-between title">
        <div class="ba-row-start clientSum">
          <span style="font-size: 20px;">{{ soInfo.customerNo || customer.customerNumber }}</span>
          <span style="font-size: 20px;">{{ soInfo.customerName || customer.customerName }}</span>
          <div>
            <el-tag v-if="customer.businessPartnerGroupName" effect="dark" type>{{ customer.businessPartnerGroupName }}</el-tag>
            <el-tag v-if="customer.customerClassificationName" effect="dark" type="success">{{ customer.customerClassificationName }}</el-tag>
            <el-tag v-if="customer.customerNatureName" effect="dark" type="warning">{{ customer.customerNatureName }}</el-tag>
            <el-tag v-if="soInfo.customerTag==='01'" effect="dark" type="danger">新客首单</el-tag>
          </div>
        </div>
        <el-button type="text" @click="showCustomerDetail=true">查看客户详情&gt;&gt;</el-button>
      </div>
      <el-row :gutter="10">
        <el-col :span="12">
          销售范围：{{ `${soInfo.salesOrganization}/${soInfo.distributionChannel}/${soInfo.productGroup}` }}&nbsp;
          {{
            soInfo.salesOrganization | dict(dictList,'salesOrganization')
          }}，{{ soInfo.distributionChannel | dict(dictList,'distributionChannel') }}，{{ soInfo.productGroup | dict(dictList,'productGroup') }}
        </el-col>
        <el-col :span="6">
          客户订单号：{{ soInfo.customerReferenceNo }}
        </el-col>
        <el-col v-if="soInfo.outerBusinessNo" :span="6">
          第三方订单号：{{ soInfo.outerBusinessNo }}
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">收货联系人：{{ soInfo.receiverName }}</el-col>
        <el-col :span="6">收货人电话：
          <safe-phone-num
            v-if="soInfo.receiverPhone"
            :phone="soInfo.receiverPhone"
            :value="
              JSON.stringify({
                user: user.name,
                no: soNo,
                value: coverMobileAndLandline(soInfo.receiverPhone),
                field: '收货人电话',
                dataType: '销售订单',
              })
            "
          ></safe-phone-num>
        </el-col>
        <el-col :span="12">收货地址：{{ soInfo.receiverProvince }}{{ soInfo.receiverAddress }}</el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">收票联系人：{{ soInfo.receivingInvoiceName }}</el-col>
        <el-col :span="6">收票人电话：
          <safe-phone-num
            v-if="soInfo.receivingInvoicePhone"
            :phone="soInfo.receivingInvoicePhone"
            :value="
              JSON.stringify({
                user: user.name,
                no: soNo,
                value: coverMobileAndLandline(soInfo.receivingInvoicePhone),
                field: '收票人电话',
                dataType: '销售订单',
              })
            "
          ></safe-phone-num>
        </el-col>
        <el-col :span="12">收票地址：{{ soInfo.receivingInvoiceProvince }}{{ soInfo.receivingInvoiceAddress }}</el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">订单联系人：{{ soInfo.orderContactName }}</el-col>
        <!-- <el-col :span="6">订单人电话：{{ soInfo.orderContactPhone }}</el-col> -->
        <el-col :span="6">订单人电话：
          <safe-phone-num
            v-if="soInfo.orderContactPhone"
            :phone="soInfo.orderContactPhone"
            :value="
              JSON.stringify({
                user: user.name,
                no: soNo,
                value: coverMobileAndLandline(soInfo.orderContactPhone),
                field: '订单人电话',
                dataType: '销售订单',
              })
            "
          ></safe-phone-num>
        </el-col>
        <el-col :span="6">
          <span v-if="isGBB">客户参考日期：{{ soInfo.customerReferenceDate }}</span>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          <span v-if="soInfo.attributionTitleCode || soInfo.attributionTitleName">实际客户：{{ soInfo.attributionTitleCode + ' ' + soInfo.attributionTitleName }}</span>
        </el-col>
      </el-row>
    </div>

    <!-- 交期要素 -->
    <DividerHeader>
      <div class="row-title">
        交期要素
        <RowMore @fold="(v) => handleFold('deliveryDateInfo', v)" :fold="fold.deliveryDateInfo" />
      </div>
    </DividerHeader>
    <div class="deliveryDateInfo" v-show="!fold.deliveryDateInfo">
      <el-row :gutter="10">
        <el-col :span="6">
          交期条件：{{ soInfo.bidCustomer | dict(dictList,'noDeliveryReason') }}
        </el-col>
        <!-- <el-col :span="6">
          <el-checkbox v-model="checked">整单不接受系统标期</el-checkbox>
        </el-col> -->
        <el-col :span="6">
          <el-checkbox
            v-model="soInfo.receiptTimeCategory"
            true-label="X"
            false-label="Z"
            disabled
          >周末与节假日均可收货</el-checkbox>
        </el-col>
        <el-col :span="6">
          <el-checkbox
            v-model="soInfo.customerAcceptSysDate"
            true-label="X"
            false-label="Z"
            disabled
          >客户接受标期</el-checkbox>
        </el-col>
        <!-- <el-col :span="6">
          客户期望送达日期：{{ "soInfo.receiverName" }}
        </el-col> -->
      </el-row>
      <el-row :gutter="10">
        <el-col :span="6">
          固定送货周期：{{ soInfo.specifiedReceiptDayOfWeek | dayOfWeekFormat }}
        </el-col>
        <el-col :span="6">
          <el-checkbox
            v-model="soInfo.deliverySensitivity"
            true-label="X"
            false-label="Z"
            disabled
          >客户交期敏感</el-checkbox>
        </el-col>
        <el-col :span="6">
          交期敏感区间：{{ soInfo.deliverySensitivityInterval }}
        </el-col>
        <el-col :span="6">
          交期回复时效：{{ soInfo.deliveryReplyTimeliness }}
        </el-col>
      </el-row>
    </div>
    <!-- 交付要素 -->
    <DividerHeader>
      <div class="row-title">
        交付要素
        <RowMore @fold="(v) => handleFold('deliveryInfo', v)" :fold="fold.deliveryInfo" />
      </div>
    </DividerHeader>
    <div class="deliveryInfo" v-show="!fold.deliveryInfo">
      <el-row :gutter="10">
        <el-col :span="6">
          <el-checkbox v-model="soInfo.autoDelivery"  true-label="X" false-label="Z" disabled >自动发货</el-checkbox>
        </el-col>
        <el-col :span="6">
          <el-checkbox v-model="soInfo.autoBatching"  true-label="X" false-label="Z" disabled >允许分批</el-checkbox>
        </el-col>
        <el-col :span="6">
          <el-checkbox
            v-model="soInfo.customerDeliveryConfirmed"
            true-label="X"
            false-label="Z"
            disabled
          >发货需与客户确认/预约</el-checkbox>
        </el-col>
        <el-col :span="6">
          合单发货：{{ soInfo.combinedDelivery | dict(dictList,'CombinedDelivery') }}
        </el-col>
      </el-row>
    </div>
    <!-- 商品信息 -->
      <DividerHeader>
        商品信息
        <el-popover
          placement="right"
          title=""
          width="200"
          trigger="hover"
        >
          <el-tag size="medium" slot="reference" type="danger" style="margin-left: 15px">* 查看打标说明</el-tag>
          <div slot="default">
            <div v-for="(item, index) in soInfo.attributeTagConfig" :key="index">
              <div class="tag-info"><span>{{ index }}</span></div>
              <span>{{ item }}</span>
            </div>
          </div>
        </el-popover>
      </DividerHeader>
    <div class="merchant">
      <div class="ba-row-between merchantInfo">
        <div class="merchantInfo-row">
          <div>
            <span>折前订单含税金额：{{ currencySymbol }}{{ soInfo.taxTotalPrice }}</span>
            <span>折前订单未税金额：{{ currencySymbol }}{{ soInfo.freeTotalPrice }}</span>
          </div>
          <div>
            <span>折后订单含税金额：{{ currencySymbol }}{{ soInfo.discountTotalTaxAmount }}</span>
            <span>折后订单未税金额：{{ currencySymbol }}{{ soInfo.discountFreeTotalPrice }}</span>
          </div>
        </div>
        <div>
          <el-button
           type="primary"
           :disabled="!soInfo.sapOrderNo"
           @click="handleCommonPermissionCheck(jumpToSupplyDetail,true)"
          >供需明细</el-button>
          <!-- <el-button
            v-if="soInfo.canAcceptDemand"
            type="primary"
            :disabled="!selectedItems.length"
            @click="handleCommonPermissionCheck(triggerDemand,true)"
          >触发需求
          </el-button> -->
          <el-tooltip class="item" effect="dark" content="客服提供，非集货场景按钮置灰，集货场景创建401或403交货单，从发货仓发给集货仓" placement="top">
            <el-button
              type="primary"
              :disabled="orderItemNos.length < 1"
              @click="handleCommonPermissionCheck(partialDelivery,true)"
            >集货交货
            </el-button>
          </el-tooltip>
          <el-button
            type="primary"
            :disabled="disabledReplenishDelivery"
            @click="handleCommonPermissionCheck(editOrder,true)"
          >集货补货
          </el-button>
          <el-popover placement="top" width="160" v-model="isShowCreateButton" style="margin:0 10px">
            <div class="align-center">
              <div>
                <el-button type="primary" @click="handleCreate('1')"
                >协同工单
                </el-button
                >
              </div>
              <div class="mt10">
                <el-button type="primary" @click="handleCreate('0')"
                >问题工单
                </el-button
                >
              </div>
            </div>
            <el-button
              type="primary"
              slot="reference"
              :disabled="orderItemNos.length < 1"
            >创建工单
            </el-button>
          </el-popover>
          <el-popover placement="top" v-model="isShowAfterSaleButton" style="margin-right: 10px">
            <div class="align-center">
              <div v-for="item in afterSaleType.filter(item => item.value !== 'CANCEL')" :key="item.value" style="text-align: center" :class="item.value !== 'GOODS' ? 'mt10' : ''">
                <el-button type="primary" @click="handleCreateAfterSale(item.value)" style="width:100px"> {{ item.label }}</el-button>
              </div>
            </div>
            <el-button type="primary" slot="reference" :disabled="orderItemNos.length < 1 || !buttonInfos.list_create">申请售后</el-button>
          </el-popover>
          <el-button
            type="primary"
            :disabled="soInfo.orderStatus==='cancel'"
            @click="triggerAtp"
          >触发占库
          </el-button>
          <el-button
            v-if="showTcWarehouseCode"
            :disabled="!selectedItems.length"
            type="primary"
            @click="cancelTcSupplier"
          >取消TC
          </el-button>
          <el-tooltip class="item" effect="dark" content="客服提供，非集货场景创建交货单从发货仓发给客户，集货场景创建200交货单，从集货仓发给客户" placement="top">
            <el-button
              type="primary"
              :disabled="isCommonPermissionCkeckDisabled"
              @click="handleCommonPermissionCheck(partialDelivery)"
            >批量交货
            </el-button>
          </el-tooltip>
          <el-button
            type="primary"
            plain
            :disabled="soInfo.orderStatus==='cancel' || orderItemNos.length<1"
            @click="handleCommonPermissionCheck(cancelBatch)"
          >批量取消商品行
          </el-button>
          <el-button
            v-if="/z001|z006|z007|z012/gim.test(soInfo.orderType || '')"
            type="primary"
            :disabled="!selectedItems.length"
            @click="handlePendingDialog('pending')"
          >订单挂起
          </el-button>
          <el-button
            v-if="/z001|z006|z007|z012/gim.test(soInfo.orderType || '')"
            type="primary"
            :disabled="!selectedItems.length || !hasPendingReason"
            @click="handlePendingDialog('cancel')"
          >取消挂起
          </el-button>
        </div>
      </div>
      <el-table
        ref="multipleTable"
        border
        :data="itemList"
        style="width: 100%"
        max-height="500"
        @selection-change="handleSelectionChange"
      >
        <el-table-column fixed="left" align="center" type="selection" width="50" />
        <el-table-column fixed="left" align="center" prop="orderItemNo" label="项目行" width="160">
          <template slot-scope="{row}">
            <el-tag v-if="row.referType === '03'">补</el-tag>
            <el-tag v-if="row.acceptDemand === '0'" type="success">不纳入需求</el-tag>
            <el-tag v-if="showDnOrderPendingReasonTag(row)">交货挂起</el-tag>
            <el-tag v-if="row.directionalTag === '01'">定向业务</el-tag>
            <el-tag v-for="item in row.itemAttributeTags" :key="item" style="margin-left: 2px"><span>{{ item }}</span></el-tag>
            <span>
              {{ row.soItemNo || '--' }}
              <el-tooltip content="Bottom center" placement="top" effect="light" v-if="row.referenceOrderNo">
                <div slot="content" style="line-height:1.5;font-size:12px">
                  关联单号: <el-button type="text" @click="handleClickReferNo(row.referenceOrderNo)">{{ row.referenceOrderNo }}</el-button><br />
                  关联行号: {{ row.referenceOrderItemNo }}
                </div>
                <i class="el-icon-info"></i>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column fixed="left" align="center" prop="sapItemNo" label="SAP行号" width="100" />
        <el-table-column fixed="left" align="center" prop="materiel" label="商品编号" width="140">
          <template slot-scope="{ row }">
            <div v-for="item in row.skuAttributeTags" :key="item" class="tag-info"><span>{{ item }}</span></div>
            <div>{{ row.materiel }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="productGroupName" label="产品组" width="100" />
        <el-table-column align="center" prop="materielName" label="商品描述" min-width="300">
          <template slot-scope="{row}">
            <div class="productDesc">
              <div class="img" @click="openGallery(row.materiel)" title="查看图片">
                <i class="el-icon-picture-outline" />
              </div>
              <div>
                <p>{{ row.sapMaterialName }}</p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="quantity" label="数量" width="70">
          <template slot-scope="{row,$index}">
            <el-link
              :underline="false"
              type="primary"
              :style="{textDecoration: 'underline'}"
              @click="handleQuantity($index)"
            >
              {{ row.quantity }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="quantityUnitName" label="单位" width="100" />
        <el-table-column align="center" prop="occupyQty" label="已占用的在库数量" width="150">
          <template slot-scope="{row}">
            {{ row.referType === '03' ? null : row.occupyQty }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="clearedQty" label="已参考数量" width="100" v-if="isForecast" />
        <el-table-column align="center" prop="deliveryQty" label="已交货数量" width="100" v-else>
          <template slot-scope="{row}">
            {{ row.referType === '03' ? null : row.deliveryQty }}
          </template>
        </el-table-column>
        <el-table-column v-if="!isForecast" align="center" prop="readyForDnQty" label="待发数量" width="100">
          <template slot-scope="{row}">{{ row.referType === '03' ? null : row.readyForDnQty }}</template>
        </el-table-column>
        <el-table-column v-if="!isForecast" align="center" prop="replenishNum" label="集货需补货数量" width="130">
          <template slot-scope="{row}">{{ row.referType === '03' ? null : row.replenishNum }}</template>
        </el-table-column>
        <el-table-column align="center" label="客服/销售" width="120">
          <template slot-scope="{row}">{{ row.productServiceName || '--' }}/ {{ row.productSaleName || '--' }}</template>
        </el-table-column>
        <el-table-column align="center" prop="buyerName" label="采购员" width="100" />
        <el-table-column align="center" prop="freeTaxPrice" label="含税单价" width="100">
          <template slot-scope="{row}">{{ (row.taxPrice || 0).toFixed(6) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="freeTaxPrice" label="未税单价" width="100">
          <template slot-scope="{row}">{{ (row.freeTaxPrice || 0).toFixed(6) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="createTime" label="创建时间" width="150">
          <template slot-scope="{row}">{{ row.createTime }}</template>
        </el-table-column>
        <el-table-column align="center" prop="deliveryDate" label="请求发货日期" width="120" />
        <el-table-column align="center" prop="customerDate" label="客户期望送达日期" width="120" />
        <el-table-column align="center" prop="orderPlanArrivalDate" label="订单预计送达日期" width="140" />
        <el-table-column align="center" prop="orderPlanDeliveryDate" label="订单预计发货日期" width="120" />
        <!-- <el-table-column prop="skuArrivalDate" label="标准送达日期" width="120" align="center">
          <template slot-scope="{ row }">
            <el-popover popper-class="custom-popover" placement="top-start" trigger="hover" :content="`原始标准送达日期：${row.originSkuArrivalDate || ''}`">
              <span slot="reference">{{ row.skuArrivalDate }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="sysDeliveryDate" label="标准发货日期" width="120" align="center">
          <template slot-scope="{ row }">
            <el-popover popper-class="custom-popover" placement="top-start" trigger="hover" :content="`原始标准发货日期：${row.originSkuDeliveryDate || ''}`">
              <span slot="reference">{{ row.sysDeliveryDate }}</span>
            </el-popover>
          </template>
        </el-table-column> -->
        <el-table-column align="center" label="商品行类型" width="160">
          <template slot-scope="{row}">{{ getItemType(row.itemType) }}</template>
        </el-table-column>
        <el-table-column align="center" label="工厂" min-width="260">
          <template slot-scope="{row}">{{ getFactory(row.factory) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="position" label="发货仓" width="120">
          <template slot="header">
            <span>发货仓</span>
            <el-tooltip effect="dark" content="非集货场景下，代表货物发向客户的仓位；集货场景下，代表货物发向集货仓的起点仓位；" placement="top">
              <i class="el-icon-info"></i>
            </el-tooltip>
          </template>
          <template slot-scope="{row}">
            {{ getPosition(row.position, row.factory) }}
          </template>
        </el-table-column>
        <el-table-column v-if="showTcWarehouseCodeRow" align="center" prop="tcWarehouseCode" label="TC仓" width="120">
          <template slot-scope="{row}">
            <span v-if="row.tcType === 'CUSTOMER'">{{ (row.tcWarehouseCode || '') + (row.tcWarehouseName || '') }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" prop="deliveryPosition" label="集货仓" width="120">
          <template slot="header">
            <span>集货仓</span>
            <el-tooltip effect="dark" content="代表货物集中合并的仓位，统一集货后发往客户" placement="top">
              <i class="el-icon-info"></i>
            </el-tooltip>
          </template>
          <template slot-scope="{row}">
            {{ getPosition(row.deliveryPosition, row.factory) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="needCollect" label="是否集货" width="80">
          <template slot="header">
            <span>是否集货</span>
            <el-tooltip effect="dark" content="代表货物是否需要集中合并后发给客户" placement="top">
              <i class="el-icon-info"></i>
            </el-tooltip>
          </template>
          <template slot-scope="scope">
            {{ scope.row.needCollect ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="costCenter" label="成本中心" width="120" v-if="isZ007">
          <template slot-scope="{row}" v-if="row.costCenter">
            {{ row.costCenter + '' + row.costCenterDesc }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="generalLedgerAccount" label="总账科目" width="120" v-if="isZ007">
          <template slot-scope="{row}" v-if="row.generalLedgerAccount">
            {{ getGeneralLedger(row.generalLedgerAccount) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="generalLedgerAccount" label="关联订单及行号" width="120">
          <template slot-scope="{row}" v-if="row.orderAndItemRelationInfo">
            {{ formatOrderAndItemRelationInfo(row.orderAndItemRelationInfo) }}
          </template>
        </el-table-column>
        <el-table-column align="center" prop="originSapOrderNo" label="原始销售订单号" width="120" v-if="isSAP814"></el-table-column>
        <el-table-column align="center" prop="referenceOrderNo" label="SAP退货单单号" width="120" v-if="isSAP814"></el-table-column>
        <el-table-column align="center" prop="referenceOrderItemNo" label="退货单行号" width="120" v-if="isSAP814"></el-table-column>
        <el-table-column fixed="right" align="center" label="操作" width="80">
          <template slot-scope="{row}">
            <p>
              <el-button type="text" size="small" @click="openProductDetail(row)">详情</el-button>
            </p>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="page"
        :limit.sync="size"
        layout="total, prev, pager, next, jumper"
      />
      <ProductPlan
        :so-info.sync="soInfo"
        :show-dialog.sync="showProductPlan"
        :list="itemPlanDTOList"
        @updateSuccess="handleUpdateItemPlanSuccess"
      />
      <OrderPendingDialog
        v-if="showOrderPendingDialog"
        :show-dialog.sync="showOrderPendingDialog"
        :pending-type="pendingType"
        :checked-not-accept-demand-reasons="checkedNotAcceptDemandReasons"
        :checked-dn-order-pending-reasons="checkedDnOrderPendingReasons"
        @submit="handlePendingSubmit"
      />
    </div>

    <DividerHeader>其他信息</DividerHeader>
    <div class="extraInfo">
      <p>
        订单原因：{{ soInfo.orderReason | dict(dictList,'orderReason') }}
      </p>
      <p v-if="isShowSapReturnDnNo">
        sap退货交货单：{{ soInfo.sapReturnDnNo || '' }}
      </p>
      <p>
        订单备注：{{ soInfo.orderNote }}
      </p>
      <div class="extraInfo-btn">
        <el-button type="text" @click="showMoreInfo=true">查看更多信息&gt;&gt;</el-button>
      </div>
    </div>
    <el-dialog
      title="请选择要导出的订单合同模板"
      :visible.sync="showContractDialog"
      width="400px"
      center
    >
      <div class="sealRow">
        <el-radio v-model="sealStatus" label="1" border size="medium">订单销售合同（有印章）</el-radio>
      </div>
      <div class="sealRow">
        <el-radio v-model="sealStatus" label="2" border size="medium">订单销售合同（无印章）</el-radio>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showContractDialog = false">取 消</el-button>
        <el-button type="primary" @click="handleDownloadContract">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="deliveryFreezeDlg"
               center
               title="该订单冻结原因如下，是否确认解冻该订单?"
    >
      <el-table :data="soInfo.deliveryFreezeDetailList">
        <el-table-column prop="deliveryFreeze" label="冻结原因" width="200" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.row.deliveryFreeze | dict(dictList,'deliveryFreeze') }}
          </template>
        </el-table-column>
        <el-table-column prop="material" label="冻结商品" align="center"></el-table-column>
        <el-table-column prop="tagReason" label="请选择异常原因">
          <template slot-scope="scope">
            <el-select
              v-model="scope.row.tagReason"
              filterable
              placeholder="请选择异常原因"
              clearable
            >
              <el-option
                v-for="item in getTagReasonOptions(scope.row.deliveryFreeze)"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deliveryFreezeDlg = false">取 消</el-button>
        <el-button type="primary" @click="handleFreezeOrder('delivery')">确 定</el-button>
      </div>
    </el-dialog>
    <div id="lightgallery"></div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import slice from 'lodash/slice'
import startsWith from 'lodash/startsWith'
import endsWith from 'lodash/endsWith'
import { MessageBox } from 'element-ui'
import Pagination from '@/components/Pagination'
import DividerHeader from '../common/DividerHeader'
import CustomerDetail from './CustomerDetail'
import MoreInfo from './MoreInfo'
import ProductDetail from './ProductDetail'
import ProductAllocate from './ProductAllocate'
import SelectDNPlan from '@/components/order/SelectDNPlan'
import OrderPendingDialog from './OrderPendingDialog'
import { requestWithLoading, findNormalItemInDictList, getOrderOptUrl } from '@/pages/orderSale/utils'
import ProductPlan from './ProductPlan'
import {
  cancelOrder, cancelOrderPart, orderDetail, customerInfo, getSkuPic,
  checkPermission, orderDetailPlan, freezeOrder, closeForecastOrder,
  undoAllocate, syncSap, triggerAtp, rebuild, checkCommonPermission,
  unfreezeApi, getCustomerCredit, updateAcceptDemand, soDetailAuthCheck,
  setEditOrder
} from '@/api/orderSale'
import { checkDNV2, checkShippingV2 } from '@/api/orderDelivery'
import { supportedOrderTypeList } from '@/pages/orderSale/constants'
import defaultImage from '@/assets/orderSupplyList/qs_product.jpg'
import { isForecastOrder } from '@/pages/orderSale/utils/orderType'
import { formatAmount } from '@/utils/poPriceCalculate'
import debounce from 'lodash/debounce'
import { deepClone, routeToWorkflow } from '@/utils/index.js'
import 'lightgallery.js'
import 'lightgallery.js/dist/css/lightgallery.css'
import { api } from '@/api/aftersales'
import { getDeliveryNoteTips, checkBTBOrder } from '@/utils/order.js'
import '@boss/web-components';
import { coverMobileAndLandline } from '@/utils/index'
import qs from 'qs';
import RowMore from '@/pages/orderDelivery/components/common/RowMore.vue'
import CancelOrderDlg from '@/pages/orderSale/components/common/CancelOrderDlg.vue'

export default {
  name: 'DetailInfo',
  components: {
    DividerHeader,
    CustomerDetail,
    MoreInfo,
    ProductDetail,
    ProductAllocate,
    SelectDNPlan,
    ProductPlan,
    Pagination,
    RowMore,
    OrderPendingDialog,
    CancelOrderDlg
  },
  props: {
    soNo: {
      type: String,
      default: ''
    },
    voucherNo: {
      type: String,
      required: true,
      default: ''
    }
  },
  filters: {
    dayOfWeekFormat(value) {
      let arr = value && value.split(',')
      let weeks = (arr || []).map((item) => {
        let num = ''
        switch (item) {
          case '01':
            num = '周一'
            break
          case '02':
            num = '周二'
            break
          case '03':
            num = '周三'
            break
          case '04':
            num = '周四'
            break
          case '05':
            num = '周五'
            break
          case '06':
            num = '周六'
            break
          case '07':
            num = '周日'
            break
          default:
            num = ''
        }
        return num
      })
      return weeks?.join(', ')
    }
  },
  data() {
    return {
      coverMobileAndLandline,
      defaultImage,
      showSelectDNPlan: false,
      showProductDetail: false,
      showProductAllocate: false,
      showMoreInfo: false,
      showCustomerDetail: false,
      showCancelOrderDialog: false,
      showCancelOrderPartDialog: false,
      deliveryFreezeDlg: false, // 交货冻结弹框
      showContractDialog: false,
      orderItemNos: [],
      orderSapItemNos: [],
      currentSku: {},
      taxAmount: 0,
      taxAmountDiscount: 0,
      freeAmount: 0,
      freeAmountDiscount: 0,
      soInfo: {},
      skuList: [],
      showProductPlan: false,
      itemPlanDTOList: {},
      selectedItems: [],
      sealStatus: '1',
      page: 1,
      size: 100,
      total: 0,
      customer: {
        saleOrgVO: {
          distributionChannel: '00',
          distributionChannelName: '',
          productGroup: '00',
          productGroupName: '',
          salesOrganization: '0000',
          salesOrganizationName: ''
        }
      },
      isPro: /pro/.test(window.CUR_DATA.env),
      isShowCreateButton: false,
      isShowAfterSaleButton: false,
      leftCreditLimit: '',
      leftCreditLimitLoading: false,
      largeReduceReason: '',
      largeReduceReasonDesc: '',
      afterSaleType: [
        { label: '取消订单', value: 'CANCEL' },
        { label: '退货', value: 'GOODS' },
        { label: '换货', value: 'REPLACE' },
        { label: '仅退款', value: 'REFUND' },
        { label: '仅退票', value: 'INVOICE' },
        { label: '服务类', value: 'SERVICE' },
        { label: '确认售后类型', value: 'CONFIRM' }
      ],
      buttonInfos: {},
      checked: false, // UI Used
      fold: {
        baseInfo: false,
        clientInfo: false,
        deliveryDateInfo: false,
        deliveryInfo: false
      },
      showOrderPendingDialog: false,
      pendingType: ''
    }
  },
  computed: {
    ...mapState([
      'userRole'
    ]),
    user() {
      return window.CUR_DATA.user
    },
    largeReduceReasonOptions() {
      let options = this.dictList['largeReduceReason'] || []
      const typeOptions = options.filter(option => option.parentCode === this.soInfo.orderType)
      if (typeOptions.length) {
        options = typeOptions
      } else {
        options = options.filter(option => option.parentCode === '')
      }
      return options.filter((a) => a.status !== 'stop')
    },
    isZ007() {
      return this.soInfo.orderType === 'Z007'
    },
    isSAP814() {
      return /RECONCILIATION_ADJUSTMENT|MODIFY_INVOICE_RECEIVER/.test(this.soInfo.orderBasis || '')
    },
    isGBB() {
      return this.soInfo.salesOrganization && this.soInfo.salesOrganization.indexOf('13') === 0
    },
    mmDictList() {
      return this.$store.state.orderPurchase.dictList || {}
    },
    isAdmin() {
      return !!~this.userRole.indexOf('boss-售后超管')
    },
    tagReasonOptions() {
      return this.dictList['tagReason'] || []
    },
    itemList() {
      return slice(this.skuList, (this.page - 1) * this.size, (this.page - 1) * this.size + this.size)
    },
    soInfoErrorMsg() {
      return this.$store.state.orderDetail.soInfoErrorMsg
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    soNoInfo() {
      return [
        {
          soNo: this.soInfo.soNo,
          soItemNo: this.orderItemNos,
          closable: false
        }
      ]
    },
    freezeReasonList() {
      return this.soInfo && this.soInfo.deliveryFreezeDetailList
        ? this.soInfo.deliveryFreezeDetailList : []
    },
    showFreezeReasonList() {
      return this.soInfo && this.soInfo.deliveryFreeze
        ? this.soInfo.deliveryFreeze.split(',') : []
    },
    freezeBillReasonList() {
      return this.soInfo && this.soInfo.billingFreeze
        ? this.soInfo.billingFreeze.split(',') : []
    },
    canFreezeDelivery() {
      if (this.showFreezeReasonList && this.showFreezeReasonList.length > 0) {
        return this.showFreezeReasonList.some(item => item !== '05' && item !== '19')
      }
      return false
    },
    canFreezeBill() {
      return this.freezeBillReasonList && this.freezeBillReasonList.length > 0
    },
    isForecast() {
      return this.soInfo && this.soInfo.orderType ? isForecastOrder(this.soInfo.orderType) : false
    },
    currencySymbol() {
      const currency = this.currency
      const symbolMap = {
        USD: '$',
        CNY: '￥',
        EUR: '€',
        MYR: 'RM',
        THB: '฿',
        VND: '₫',
        SGD: 'S$',
        TWD: 'NT$',
        HKD: 'HK$'
      }
      return symbolMap[currency]
    },
    currency() {
      const found = this.skuList.find(item => item.currency)
      return found ? found.currency : ''
    },
    isCreditLimitFreeze() {
      return this.soInfo.creditLimitFreeze !== undefined
    },
    isContractDisabled() {
      const { salesOrganization } = this.soInfo
      return this.soInfo.orderStatus === 'cancel' ||
        startsWith(salesOrganization, '31') ||
        startsWith(salesOrganization, '24')
    },
    hasCreditFreeze() {
      return ['30', '20', '21', '22', '23'].indexOf(this.soInfo.creditLimitFreeze) > -1 || ['30', '20', '21', '22', '23'].indexOf(this.soInfo.overdueFreeze) > -1 || ['02'].indexOf(this.soInfo.timeoutUninvoicedFreeze) > -1
    },
    isPermissionDisabled() {
      return this.soInfo.orderStatus === 'cancel' || this.canFreezeDelivery || this.hasCreditFreeze
    },
    isCommonPermissionCkeckDisabled() {
      return this.soInfo.orderStatus === 'cancel' || this.orderItemNos.length < 1 || this.canFreezeDelivery || this.hasCreditFreeze
    },
    isShowSapReturnDnNo () {
      const skuValid = (this.skuList || []).some(item => item.directDeliverySupplier === '0' && endsWith(item.position, '04'))
      return /Z001|Z006|Z007/gim.test(this.soInfo?.orderType) && this.soInfo?.orderReason === '038' && skuValid
    },
    // 某一商品行存在挂起原因
    hasPendingReason () {
      return (this.selectedItems || []).some((item) => {
        const hasNotAcceptDemandReason = item.notAcceptDemandReasons
          ?.split(',')
          ?.some((code) =>
            findNormalItemInDictList(this.dictList.notAcceptDemandReason, code)
          );
        const hasDnOrderPendingReason = item.dnOrderPendingReasons
          ?.split(',')
          ?.some((code) =>
            findNormalItemInDictList(this.dictList.dnOrderPendingReason, code)
          );
        return hasNotAcceptDemandReason || hasDnOrderPendingReason;
      });
    },
    checkedNotAcceptDemandReasons () {
      const list = (this.selectedItems || []).flatMap(item => item.notAcceptDemandReasons ? item.notAcceptDemandReasons.split(',') : [])
      return Array.from(new Set(list))
    },
    checkedDnOrderPendingReasons () {
      const list = (this.selectedItems || []).flatMap(item => item.dnOrderPendingReasons ? item.dnOrderPendingReasons.split(',') : [])
      return Array.from(new Set(list))
    },
    showTcWarehouseCode () {
      return (this.skuList || []).some(item => item.hasTcSupplier)
    },
    showTcWarehouseCodeRow () {
      return (this.skuList || []).some(item => item.hasTcSupplier && item.tcType === 'CUSTOMER')
    },
    getOaUrl () {
      return window.CUR_DATA.env === 'pro'
        ? `https://oa.zkh360.com/zkh/action/ViewRequest.jsp?requestmark=${this.soInfo.oaProcessNo}`
        : `https://testoa3.zkh360.com/zkh/action/ViewRequest.jsp?requestmark=${this.soInfo.oaProcessNo}`;
    },
    disabledReplenishDelivery () {
      return !(
        this.selectedItems &&
        this.selectedItems[0] &&
        this.selectedItems[0].deliveryPosition &&
        this.selectedItems[0].replenishNum > 0 &&
        this.selectedItems[0].referType !== '03' &&
        this.orderItemNos.length === 1 &&
        /Z001|Z006|Z007|Z008/gim.test(this.soInfo.orderType)
      )
    }
  },
  watch: {
    soInfoErrorMsg(newValue) {
      if (newValue && newValue.msg) {
        this.$notify.error(newValue.msg)
      }
    }
  },
  async created() {
    try {
      await this.soDetailAuthCheck();
      await this.init()
      if (JSON.stringify(this.mmDictList) === '{}' && this.isZ007) {
        await this.$store.dispatch('orderPurchase/queryDictList')
      }
      this.showListButton()
    } catch (error) {
      this.$notify.error(error.message)
    }
  },
  methods: {
    async soDetailAuthCheck () {
      const res = await soDetailAuthCheck({ soNo: this.soNo || this.voucherNo, userDomain: window.CUR_DATA.user?.name });
      if (res.code !== 200) {
        throw new Error(res.msg || '详情查看权限请求失败！')
      } else {
        if (res.data === false) {
          window.location.href = '/unauthorized'
        }
      }
    },
    openGallery(skuNo) {
      const el = document.getElementById('lightgallery')
      if (el && window.lgData && window.lgData[el.getAttribute('lg-uid')]) {
        window.lgData[el.getAttribute('lg-uid')].destroy(true)
      }
      getSkuPic({ skuNo }).then(res => {
        if (res?.data && res?.data.length > 0) {
          const dynamicEl = res.data.map(item => ({
            src: item
          }))
          window.lightGallery(el, {
            dynamic: true,
            dynamicEl
          })
        } else {
          MessageBox.alert('查询图片结果为空！', { type: 'error' })
        }
      })
    },
    handleFreezeDelivery() {
      if (this.freezeReasonList.length > 0) {
        this.deliveryFreezeDlg = true
      } else {
        this.handleFreezeOrder('delivery')
      }
    },
    // 获取冻结原因枚举值
    getTagReasonOptions(parentCode) {
      return this.tagReasonOptions.filter(item => {
        return item.parentCode === parentCode
      })
    },
    applyAfterSale(type) {
      const customerNo = this.customer.customerNumber
      const customerName = this.customer.customerName
      const ids = this.selectedItems.map(item => item.materiel).join(',')
      const sapItemNos = this.selectedItems.map(item => item.sapItemNo).join(',')
      const sapOrderNos = this.selectedItems.map(item => item.sapOrderNo).join(',')
      const rc = this.soInfo.receiverContact
      const rn = this.soInfo.receiverName
      const rp = this.soInfo.receiverPhone
      const ra = window.encodeURIComponent(this.soInfo.receiverAddress)
      const province = window.encodeURIComponent(this.soInfo.receivingInvoiceProvince)
      const city = window.encodeURIComponent(this.soInfo.receivingInvoiceCity)
      const region = window.encodeURIComponent(this.soInfo.receivingInvoiceDistrict)
      const oc = this.soInfo.orderContactName
      const op = this.soInfo.orderContactPhone
      // this.$router.push({
      //   path: `/after-sales/form/${ids}?createType=${type}&type=create&customerName=${customerName}&customerNo=${customerNo}&sapItemNos=${sapItemNos}&sapOrderNos=${sapOrderNos}&rn=${rn}&rp=${rp}&ra=${ra}&oc=${oc}&op=${op}`
      // })
      window.open(`/after-sales/form/${ids}?createType=${type}&type=create&customerName=${customerName}&customerNo=${customerNo}&sapItemNos=${sapItemNos}&sapOrderNos=${sapOrderNos}&rc=${rc}&rn=${rn}&rp=${rp}&ra=${ra}&oc=${oc}&op=${op}&province=${province}&city=${city}&region=${region}`, '_blank')
    },
    async init() {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      try {
        const res = await orderDetail({
          sapOrderNo: this.voucherNo,
          omsNo: this.soNo
        })
        if (res && res.code === 200) {
          this.$emit('init', res.data)
          const { orderType, soNo } = res.data
          if (orderType === 'ZEV2') {
            await rebuild({
              soNo,
              orderType
            })
          }
          this.soInfo = res.data
          this.skuList = res.data.items
          this.total = this.skuList.length
          const {
            customerNo,
            salesOrganization,
            productGroup,
            distributionChannel,
            invoiceReceiver
          } = res.data
          let customerNumber = invoiceReceiver || customerNo
          if (customerNumber) {
            const res1 = await customerInfo({
              customerNumber: customerNumber,
              salesOrganization,
              productGroup,
              distributionChannel
            })
            if (res1 && res1.code === 200) {
              this.customer = res1.data
              if (this.customer && !this.customer.customerNumber) {
                this.$message.error('获取客户信息失败！')
              }
              this.setTotalPrice()
            }
          } else {
            this.$message.error('获取详情客户信息失败！')
          }
        } else if (res && res.code === 500 && res.msg) {
          this.$alert(res.msg, '错误', {
            confirmButtonText: '确定',
            callback: action => {
            }
          })
        }
      } catch (e) {
      } finally {
        loading.close()
      }
    },
    caculateAmount(skus) {
      const { isTax } = this.soInfo
      const result = skus.filter(item => item.referType !== '03').reduce(
        (acc, sku, index) => {
          const {
            taxRate: taxRate = 0,
            discountAmount: discountAmount = 0,
            quantity = 0
          } = sku
          let { freeTaxPrice = 0, taxPrice = 0 } = sku
          let taxTotalPrice = 0
          let freeTotalPrice = 0
          let taxAmount = 0
          if (isTax === '1') {
            taxTotalPrice = formatAmount(taxPrice * quantity, 2)
            taxAmount = formatAmount(taxTotalPrice * taxRate / (1 + taxRate), 2)
            freeTotalPrice = formatAmount(taxTotalPrice - taxAmount, 2)
          } else {
            freeTotalPrice = formatAmount(freeTaxPrice * quantity, 2)
            taxAmount = formatAmount(freeTotalPrice * taxRate, 2)
            taxTotalPrice = formatAmount(freeTotalPrice + taxAmount, 2)
          }

          const dp = parseFloat(discountAmount)
          const newValue = {}
          newValue.taxedTotal = taxTotalPrice + acc.taxedTotal
          newValue.unTaxedTotal = freeTotalPrice + acc.unTaxedTotal
          if (isTax === '0') {
            newValue.taxedDiscountTotal = acc.taxedDiscountTotal + taxTotalPrice - dp * (1 + taxRate)
            newValue.unTaxedDiscountTotal = acc.unTaxedDiscountTotal + freeTotalPrice - dp
          }
          if (isTax === '1') {
            newValue.taxedDiscountTotal = acc.taxedDiscountTotal + taxTotalPrice - dp
            newValue.unTaxedDiscountTotal = acc.unTaxedDiscountTotal + freeTotalPrice - (dp / (1 + taxRate))
          }
          return newValue
        },
        { taxedTotal: 0, taxedDiscountTotal: 0, unTaxedTotal: 0, unTaxedDiscountTotal: 0 }
      )
      return result
    },
    setTotalPrice() {
      const skuList = this.skuList.filter(item => item.quantity !== 0)
      const {
        taxedTotal,
        taxedDiscountTotal,
        unTaxedTotal,
        unTaxedDiscountTotal
      } = this.caculateAmount(skuList)
      this.taxAmount = formatAmount(taxedTotal, 2)
      this.taxAmountDiscount = formatAmount(taxedDiscountTotal, 2)
      this.freeAmount = formatAmount(unTaxedTotal, 2)
      this.freeAmountDiscount = formatAmount(unTaxedDiscountTotal, 2)
    },
    // 获取冻结数据
    getCreditFreeze(creditLimitCode) {
      if (creditLimitCode && this.dictList['creditFreeze']) {
        const p = this.dictList['creditFreeze'].find(item => item.code === creditLimitCode)
        if (p && p.name) {
          return `${p.name}`
        }
      }
      return creditLimitCode
    },
    // 获取超期未开冻结数据
    getTimeoutFreeze(timeoutFreezeCode) {
      if (timeoutFreezeCode && this.dictList['timeoutUninvoicedFreeze']) {
        const p = this.dictList['timeoutUninvoicedFreeze'].find(item => item.code === timeoutFreezeCode)
        if (p && p.name) {
          return `${p.name}`
        }
      }
      return timeoutFreezeCode
    },
    getOverDueFreeze(overdueAccountCode) {
      if (overdueAccountCode && this.dictList['overDueFreeze']) {
        const p = this.dictList['overDueFreeze'].find(item => item.code === overdueAccountCode)
        if (p && p.name) {
          return `${p.name}`
        }
      }
      return overdueAccountCode
    },
    getItemType(itemType) {
      if (itemType && this.dictList['soItemType']) {
        const p = this.dictList['soItemType'].find(item => item.code === itemType)
        if (p && p.name) {
          return `${itemType}${p.name}`
        }
      }
      return itemType
    },
    getFactory(factory) {
      if (this.dictList && this.dictList['factory']) {
        const p = this.dictList['factory'].find(item => item.code === factory)
        if (p && p.name) {
          return `${factory}${p.name}`
        }
      }
      return factory
    },
    getPosition(position, factory) {
      if (this.dictList && this.dictList['position']) {
        const p = this.dictList['position'].find(item => item.code === position && item.parentCode === factory)
        if (p && p.name) {
          return `${position}${p.name}`
        }
      }
      return position
    },
    getGeneralLedger(generalLedgerAccount) {
      if (this.mmDictList && this.mmDictList.generalLedger) {
        return this.mmDictList.generalLedger.find(item => item.value === generalLedgerAccount)?.name
      }
    },
    getDetail(bk) {
      orderDetail({
        sapOrderNo: this.voucherNo,
        omsNo: this.soNo
      }).then(res => {
        if (res && res.code === 200) {
          this.soInfo = res.data
          this.skuList = res.data.items
        }
        if (bk) {
          bk()
        }
      })
    },
    handleCommonPermissionCheck(callback, val) {
      const { salesOrganization } = this.soInfo
      checkCommonPermission(salesOrganization).then(res => {
        if (res && res.code === 200) {
          if (callback) {
            callback(val)
          }
        } else {
          this.$alert(res.data, '提示')
        }
      })
    },
    editOrder(val) {
      checkPermission().then(res => {
        if (res && res.code === 200) {
          if (
            this.soInfo?.orderType &&
            supportedOrderTypeList.indexOf(this.soInfo.orderType) === -1
          ) {
            this.$alert('暂不支持修改该单据类型', '提示')
            return
          }
          const query = {}
          if (val) {
            query['selectedItem'] = this.orderItemNos[0]
          }
          const no = this.soInfo.soNo || this.soNo
          let queryStr = ''
          if (Object.keys(query).length > 0) {
            queryStr = '?' + qs.stringify(query)
          }
          window.location.replace(`/sr/edit/${no}${queryStr}`)
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    },
    batchDeliver() {
      this.gotoDelivery(true, false)
    },
    refresh() {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      this.getDetail(() => {
        loading.close()
      })
    },
    closeOrder() {
      checkPermission().then(res => {
        if (res && res.code === 200) {
          this.$confirm('是否确认关闭该订单？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            requestWithLoading(this, closeForecastOrder({
              soNo: this.soInfo.soNo || this.soNo,
              sapOrderNo: this.soInfo.sapOrderNo
            }), res => {
              this.$message.success({
                dangerouslyUseHTMLString: true,
                message: '订单修改成功'
              })
              this.getDetail()
            })
          })
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    },
    cancelOrder() {
      checkPermission().then(res => {
        if (res && res.code === 200) {
          this.showCancelOrderDialog = true
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    },
    cancelBatch() {
      checkPermission().then(res => {
        if (res && res.code === 200) {
          this.showCancelOrderPartDialog = true
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    },
    async cancelAllocate(params, callback) {
      const unAllocateRes = await undoAllocate(params)
      if (callback && (!unAllocateRes || unAllocateRes.code !== 200 || !unAllocateRes.data)) {
        callback()
        return false
      }
      return true
    },
    async cancelOrderHandle() {
      if (!this.largeReduceReason) {
        return this.$message.error('请选择取消大类原因！')
      }
      if (!this.largeReduceReasonDesc) {
        return this.$message.error('请输入取消大类详情！')
      }
      if (this.largeReduceReasonDesc?.length > 50) {
        return this.$message.error('取消大类详情最多50个字符！')
      }
      this.showCancelOrderDialog = false
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      const params = {
        largeReduceReason: this.largeReduceReason.code,
        largeReduceReasonDesc: this.largeReduceReasonDesc,
        omsNo: this.soNo,
        sapOrderNo: this.soInfo.sapOrderNo
      }
      const allocateRes = await this.cancelAllocate(params, () => {
        loading.close()
      })
      if (!allocateRes) {
        return
      }
      cancelOrder(params).then(res => {
        if (res && res.code === 200) {
          this.$notify.success('订单取消成功！')
          this.getDetail(() => {
            loading.close()
          })
        } else if (res && res.data && Array.isArray(res.data) && res.data.length > 0) {
          this.$alert(res.data.join('<br>'), '错误', {
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
        loading.close()
      }).catch(error => {
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误')
          }
        }
        loading.close()
      })
    },
    async cancelOrderPartHandle(formData) {
      this.showCancelOrderPartDialog = false
      // 判断背靠背订单
      await checkBTBOrder(this, this.selectedItems)
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      const params = {
        largeReduceReason: formData.largeReduceReason.code,
        largeReduceReasonDesc: formData.largeReduceReasonDesc,
        materialReplaceProcessNo: formData.materialReplaceProcessNo,
        omsNo: this.soInfo.soNo,
        sapOrderNo: this.soInfo.sapOrderNo,
        omsItemNoList: this.orderItemNos,
        sapItemNoList: this.orderSapItemNos
      }
      const allocateRes = await this.cancelAllocate(params, () => {
        loading.close()
      })
      if (!allocateRes) {
        return
      }
      cancelOrderPart(params).then(res => {
        if (res && res.code === 200) {
          this.$notify.success('批量取消商品行成功！')
          this.getDetail(() => {
            loading.close()
          })
        } else if (res && res.data && Array.isArray(res.data) && res.data.length > 0) {
          this.$alert(res.data.join('<br>'), '错误', {
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
        loading.close()
      }).catch(error => {
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误')
          }
        }
        loading.close()
      })
    },
    allocateProduct() {
      this.showProductAllocate = true
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach(row => {
          this.$refs.multipleTable.toggleRowSelection(row)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    handleSelectionChange(val) {
      this.selectedItems = val
      this.orderItemNos = val.map(item => item.soItemNo)
      this.orderSapItemNos = val.map(item => {
        if (item && item.sapItemNo) {
          return item.sapItemNo
        }
      })
    },
    openProductDetail(currentSku) {
      this.currentSku = currentSku
      this.$store.dispatch('orderDetail/getSkuInfo', currentSku.materiel)
      this.showProductDetail = true
    },
    openAfterSale(sku) {
      this.$router.push({
        path: `/after-sales/form/${sku.materiel}?type=create&source=${this.soInfo.orderSource || ''}&no=${sku.sapOrderNo}&sapItemNo=${sku.sapItemNo}&pic=${sku.picUrl || ''}&desc=${window.encodeURIComponent(sku.sapMaterialName) || ''}`
      })
    },
    handleQuantity(idx) {
      const index = (this.page - 1) * this.size + idx
      const { sapOrderNo, sapItemNo, soItemNo } = this.skuList[index]
      const soNo = this.soNo
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)',
        lock: true
      })
      orderDetailPlan({ sapOrderNo, sapItemNo, soNo, soItemNo }).then(res => {
        loading.close()
        if (res && res.code === 200) {
          this.showProductPlan = true
          this.itemPlanDTOList = {
            index,
            detail: this.skuList[index],
            data: res.data.sort((a, b) => a.confirmedQtyType > b.confirmedQtyType ? 1 : -1)
          }
        }
      })
    },
    handlePlanSubmit(val) {
      if (val && val.length > 0) {
        const noList = val.map(item => {
          return `${item.referOrderNo}-${item.referOrderItemNo}-${item.referOrderItemDetailNo}`
        })
        const no = noList.join('_')
        this.$router.push({
          path: `/orderDelivery/create/${no}`
        })
      }
    },
    // 信用冻结解冻
    handleFreeze(creditLimitFreeze, overdueFreeze, timeoutUninvoicedFreeze) {
      unfreezeApi(this.soInfo.soNo, creditLimitFreeze, overdueFreeze, timeoutUninvoicedFreeze).then((res) => {
        if (res && res.code === 200) {
          this.$message.success('解冻成功')
          this.refresh()
        } else {
          this.$alert(res.data, '提示')
        }
      })
    },
    // 查看客户信用额度结果
    checkCreditResult: debounce(function () {
      this.leftCreditLimitLoading = true
      const { customerNo, distributionChannel, salesOrganization: saleOrganization, soNo } = this.soInfo
      const queryParams = {
        customerNo,
        distributionChannel,
        saleOrganization,
        soNo
      }
      getCustomerCredit(queryParams).then((res) => {
        this.leftCreditLimitLoading = false
        if (res && res.code === 200) {
          this.leftCreditLimit = res.data.leftCreditLimit
        } else {
          this.$alert(res.msg, '提示')
        }
      })
    }, 1000),
    // 判断异常信息是否填全
    isAllright(deliveryFreezeDetailList) {
      for (let item of deliveryFreezeDetailList) {
        if (!item.tagReason) {
          this.$message.error(item.material + '商品未选择异常原因，无法解冻')
          return false
        }
      }
      return true
    },
    handleFreezeOrder(type) {
      const params = {
        omsNo: this.soInfo.soNo
      }
      if (type === 'delivery') {
        if (!this.isAllright(this.soInfo.deliveryFreezeDetailList)) {
          return
        }
        params.deliveryUnfreezeList = ['05']
        params.unfreezeType = 'DELIVERY'
        params.deliveryFreezeDetailReqVos = this.soInfo.deliveryFreezeDetailList
      } else {
        params.billingUnfreezeList = []
        params.unfreezeType = 'INVOICE'
      }
      const freezeMsg = type === 'delivery' ? '交货' : '开票'
      this.$confirm(`是否确认解冻该订单，清空${freezeMsg}冻结？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        requestWithLoading(this, freezeOrder(params), res => {
          this.refresh()
          if (type === 'delivery') {
            this.deliveryFreezeDlg = false
          }
        })
      }).catch(() => {
      })
    },
    handleUpdateItemPlanSuccess(data) {
      if (data) {
        const { index, data: { itemPlanList } } = data
        if (index != null && itemPlanList && itemPlanList.length > 0) {
          const amount = itemPlanList.reduce((total, current) => {
            total += current.itemQuantity
            return total
          }, 0)
          this.skuList[index].quantity = amount
        }
      }
    },
    async getDeliveryNoteTips (val, isTotal) {
      const { bidCustomer, receiverDistrict, receiverDistrictCode, soNo } = this.soInfo;
      const situationItemList = this.selectedItems.map(item => {
        return {
          deliveryPosition: item.deliveryPosition,
          position: item.position,
          soItemNo: item.soItemNo,
          soNo: item.soNo
        }
      })
      const data = {
        bidCustomer,
        receiverDistrict,
        receiverDistrictCode,
        soNo,
        totalDelivery: !!isTotal,
        // createDnType,
        situationItemList
      }
      // 批量交货增加改字段
      if (val !== true) {
        data.validateDnSketch = true
      }
      try {
        const res = await getDeliveryNoteTips(this, data)
        console.log(res)
        return Promise.resolve(res)
      } catch (err) {
        console.log(err)
        return Promise.reject(err);
      }
    },
    async gotoDelivery (isTotal, val) {
      const no = this.soInfo ? this.soInfo.soNo : this.soNo
      if (no) {
        let formatedNo = no
        const params = {
          soNo: no
        }
        if (isTotal || (this.selectedItems && this.selectedItems.length > 0)) {
          // createDnType：集货交货shippingCreate； 批量交货、整单交货create
          // const createDnType = val === true ? 'shippingCreate' : 'create'
          var tips;
          try {
            tips = await this.getDeliveryNoteTips(val, isTotal)
            console.log(tips)
          } catch (err) {
            return;
          }
          params.soItemNo = []
          const noList = this.selectedItems.map(item => {
            params.soItemNo.push(item.soItemNo)
            return item.soItemNo
          })
          noList.unshift(no)
          formatedNo = noList.join('-')
        }
        if (val) {
          // 集货交货 checkShipping...
          let checkApi = checkShippingV2
          requestWithLoading(this, checkApi(params), () => {
            this.$router.push({
              path: `/orderDelivery/create/${formatedNo}`,
              query: {
                from: 'deliveryCollection',
                epidemicSituationFlag: tips?.epidemicSituationFlag
              }
            })
          })
        } else {
          let checkApi = checkDNV2
          requestWithLoading(this, checkApi(params), () => {
            this.$router.push({
              path: `/orderDelivery/create/${formatedNo}`,
              query: {
                total: isTotal ? 'true' : 'false', // 整单交货
                epidemicSituationFlag: tips?.epidemicSituationFlag
              }
            })
          })
        }
      }
    },
    checkIfSelectionIsUnique() {
      const map = {}
      this.selectedItems.forEach(item => {
        const { demandUser, shippingPosition } = item
        if (map.demandUser == null && demandUser) {
          map.demandUser = demandUser
        } else {
          if (map.demandUser !== demandUser) {
            return false
          }
        }
        if (map.shippingPosition == null && shippingPosition) {
          map.shippingPosition = shippingPosition
        } else {
          if (map.shippingPosition !== shippingPosition) {
            return false
          }
        }
      })
      return true
    },
    jumpToSupplyDetail() {
      const queryObj = {
          orderNo: this.soInfo.sapOrderNo,
          needSafeStock: true,
          pageSize: 10,
          summary: false,
          omsOrder: false,
          open: true
        }
      let query = '?'
      for (let key in queryObj) {
        query += key + '=' + queryObj[key] + '&'
      }
      query = query.slice(0, -1)
      console.log(this.$route, query);

      window.open(window.location.origin + '/inventoryInquiry/supplyDetail' + query, '_blank');
    },
    triggerDemand() {
      console.log(this.selectedItems)
      let directOrder = ''
      this.selectedItems.forEach((item) => {
        if (item.directDeliverySupplier === '1') {
          directOrder += `${item.soItemNo}、`
        }
      })
      if (directOrder) {
        return this.$message.error(`项目行：${directOrder.slice(0, -1)}行 为直发单，直发订单行不允许人工操作纳入需求!`)
      }
      const data = deepClone(this.soInfo)
      this.$confirm('确认将对勾选行触发需求？', '操作提示', {
        confirmButtonText: '触发需求',
        cancelButtonText: '',
        distinguishCancelAndClose: true,
        // closeOnClickModal: false,
        type: 'warning'
        // center: true
      }).then(() => {
        console.log('触发需求')
        data.items.forEach(item => {
          if (this.selectedItems.find(select => select.materiel === item.materiel && select.soItem === item.soItem)) {
            item.acceptDemand = '1'
          }
        })
        requestWithLoading(this, updateAcceptDemand(data), (msg) => {
          this.$message.success(Array.isArray(msg) ? msg.join(',') : msg || '操作成功！')
          setTimeout(() => {
            this.init()
          }, 800)
        })
      }).catch((action) => {
        console.log('关闭需求', action)
        // if (action === 'cancel') {
        //   data.items.forEach(item => {
        //     if (this.selectedItems.find(select => select.materiel === item.materiel && select.soItem === item.soItem)) {
        //       item.acceptDemand = '0'
        //     }
        //   })
        //   requestWithLoading(this, updateAcceptDemand(data), (msg) => {
        //     this.$message.success(Array.isArray(msg) ? msg.join(',') : msg || '操作成功！')
        //     setTimeout(() => {
        //       this.init()
        //     }, 800)
        //   })
        // }
      })
    },
    partialDelivery(val) {
      if (this.selectedItems && this.selectedItems.length > 0) {
        if (!this.checkIfSelectionIsUnique()) {
          this.$alert('您选择的商品行存在不同装运点或不同领用人，或存在不满足交货日期的计划行，无法创建DN，请重新选择！', '错误', {
            type: 'error',
            confirmButtonText: '确定',
            callback: action => {
            }
          })
        } else {
          this.gotoDelivery(false, val)
        }
      }
    },
    exportContract() {
      if (this.soInfo.orderSource === 'GBB' && this.soInfo.orderNo) {
        window.open(`/api-gbb-admin/api/order/downLoadOrderContractPdf/${this.soInfo.orderNo}/2`, '_blank')
      } else if (this.soInfo.salesOrganization === '6001') {
        const no = this.soInfo ? this.soInfo.soNo : this.soNo
        if (no) {
          window.open(`/internal-api/so/exportFormalPdf?status=2&no=${no}`, '_blank')
        }
      } else {
        this.showContractDialog = true
      }
    },
    handleDownloadContract() {
      this.showContractDialog = false
      const no = this.soInfo ? this.soInfo.soNo : this.soNo
      if (no) {
        window.open(`/internal-api/so/exportFormalPdf?status=${this.sealStatus}&no=${no}`, '_blank')
      }
    },
    closeAllocate(row) {
      this.$alert('确定要关闭该商品行所关联的所有匀货记录吗?', '关闭匀货', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            const { orderItemNo, sapItemNo } = row
            const orderItemNos = []
            const orderSapItemNos = []
            if (orderItemNo) {
              orderItemNos.push(orderItemNo)
            }
            if (sapItemNo) {
              orderSapItemNos.push(sapItemNo)
            }
            const params = {
              omsNo: this.soInfo.soNo,
              sapOrderNo: this.soInfo.sapOrderNo,
              omsItemNoList: orderItemNos,
              sapItemNoList: orderSapItemNos
            }
            requestWithLoading(this, undoAllocate(params), data => {
              this.$message({
                message: '关闭匀货成功',
                type: 'success'
              })
            })
          }
        }
      })
    },
    handleSyncSap() {
      const { soNo } = this.soInfo
      if (soNo) {
        requestWithLoading(this, syncSap(soNo), data => {
          if (data && typeof data === 'string') {
            this.$message({
              message: data,
              type: 'warning',
              onClose: () => {
                this.init()
              }
            })
          } else {
            this.init()
          }
        })
      }
    },
    triggerAtp() {
      const { soNo } = this.soInfo
      if (soNo) {
        requestWithLoading(this, triggerAtp(soNo), data => {
          this.$message.success('触发成功！')
        })
      }
    },
    handleClickReferNo(no) {
      this.$router.jumpToSoOrderDetail({
        query: {
          soNo: '',
          sapOrderNo: no,
          id: no,
          refresh: true
        }
      })
    },
    gotoNext(current) {
      const idx = this.itemList.findIndex(item => item.soItemNo === current.soItemNo)
      if (idx > -1 && this.itemList.length > idx + 1) {
        this.currentSku = this.itemList[idx + 1]
        this.$store.dispatch('orderDetail/getSkuInfo', this.currentSku.materiel)
      } else {
        this.$message.error('没有下一行！')
      }
    },
    gotoPrev(current) {
      const idx = this.itemList.findIndex(item => item.soItemNo === current.soItemNo)
      if (idx > 0) {
        this.currentSku = this.itemList[idx - 1]
        this.$store.dispatch('orderDetail/getSkuInfo', this.currentSku.materiel)
      } else {
        this.$message.error('没有上一行！')
      }
    },
    handleCreate(type) {
      routeToWorkflow('/wf/create/0', {
        essence: type,
        soNo: this.soInfo.soNo,
        fromPage: 'orderDetail',
        soItemNos: this.selectedItems.map(item => item.soItemNo).join(',')
      })
    },
    handleCreateAfterSale(type) {
      const isPro = window.CUR_DATA.env === 'pro'
      const omsNo = this.soInfo.soNo
      const customerNo = this.customer.customerNumber
      const skus = this.selectedItems.map(item => item.materiel).join(',')
      const sapItemNos = this.selectedItems.map(item => item.sapItemNo).join(',')
      const sapOrderNos = this.selectedItems.map(item => item.sapOrderNo).join(',')
      const query = `?customerNo=${customerNo}&skus=${skus}&omsNo=${omsNo}&sapItemNos=${sapItemNos}&sapOrderNos=${sapOrderNos}`
      switch (type) {
        case 'CANCEL':
        case 'SERVICE':
          this.applyAfterSale(type)
          break
        case 'GOODS':
          window.open(`/as/return/create${query}`)
          break
        case 'REPLACE':
          window.open(`/as/replace/create${query}`)
          break
        case 'REFUND':
          window.open(`/as/refund/create${query}`)
          break
        case 'INVOICE':
          window.open(`/as/invoice/create${query}`);
          break;
        case 'CONFIRM': {
          routeToWorkflow('/wf/create/0', {
            essence: 0,
            categoryIdAfSales: isPro ? 618 : 960,
            creatSource: 28,
            soNo: this.soInfo.soNo,
            fromPage: 'orderDetail',
            soItemNos: this.selectedItems.map(item => item.soItemNo).join(',')
          })
          break;
        }
      }
    },
    showListButton() {
      api({
        url: '/ticket/common/listButtonInfos',
        complete: (res) => {
          this.buttonInfos = res?.data || {}
        }
      })
    },
    // 是否展示交货挂起tag（’99‘为解除挂起，这种情况不展示）
    showDnOrderPendingReasonTag (row) {
      return (
        (row.dnOrderPendingReasons || '')?.split(',')?.filter((code) => !!code && code !== '99')?.length > 0
      )
    },
    handleFold (key, val) {
      this.fold[key] = val
    },
    handlePendingDialog (type) {
      this.showOrderPendingDialog = true;
      this.pendingType = type;
    },
    handlePendingSubmit (notAcceptDemandReasons, dnOrderPendingReasons) {
      this.selectedItems.forEach((item) => {
        const found = this.skuList?.find((sku) => sku.soItemNo === item.soItemNo);
        if (found) {
          const _notAcceptDemandReasons =
            found.notAcceptDemandReasons?.split(',')?.filter(Boolean) || [];
          const _dnOrderPendingReasons =
            found.dnOrderPendingReasons?.split(',')?.filter(Boolean) || [];
          if (this.pendingType === 'pending') {
            found.notAcceptDemandReasons = Array.from(
              new Set(_notAcceptDemandReasons.concat(notAcceptDemandReasons))
            )?.join(',');
            found.dnOrderPendingReasons = Array.from(
              new Set(_dnOrderPendingReasons.concat(dnOrderPendingReasons))
            )?.join(',');
          } else {
            found.notAcceptDemandReasons = _notAcceptDemandReasons
              .filter((item) => {
                return !notAcceptDemandReasons.includes(item);
              })
              ?.join(',');
            found.dnOrderPendingReasons = _dnOrderPendingReasons
              .filter((item) => {
                return !dnOrderPendingReasons.includes(item);
              })
              ?.join(',');
          }
        }
      });
      const orderUrl = getOrderOptUrl(this.dictList, this.soInfo.orderType, 'update')
      const param = {
        ...this.soInfo,
        items: this.skuList
      }
      if (orderUrl) {
        this.update(orderUrl, param)
      }
    },
    cancelTcSupplier () {
      this.selectedItems.forEach(item => {
        const found = this.skuList?.find((sku) => sku.soItemNo === item.soItemNo);
        if (found) {
          found.hasTcSupplier = false
        }
      })
      const orderUrl = getOrderOptUrl(this.dictList, this.soInfo.orderType, 'update')
      const param = {
        ...this.soInfo,
        items: this.skuList
      }
      if (orderUrl) {
        this.update(orderUrl, param)
      }
    },
    update (orderUrl, param) {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      setEditOrder(orderUrl, param).then(res => {
        const { errMsgList } = res.data || {};
        if (res && res.code === 200) {
          this.$message.success(res.msg || '操作成功')
        } else {
          let message = ''
          if (errMsgList?.length > 0) {
            message += errMsgList.join('<br/>')
          } else {
            message = res.data
          }
          this.$alert(message, '错误提示', {
            type: 'error',
            customClass: 'custom-return-error',
            dangerouslyUseHTMLString: true
          })
        }
      }).catch(err => {
        this.$alert(err.message || err.msg || '操作失败')
      }).finally(() => {
        this.init()
        loading.close()
      })
    },
    formatOrderAndItemRelationInfo(val) {
      return val.split(':')?.slice(0, 2)?.join(':') || ''
    }
  }
}
</script>

<style scoped lang="scss">
  .baseInfo,
  .clientInfo,
  .deliveryDateInfo,
  .deliveryInfo {
    padding: 10px;

    .el-row {
      margin-bottom: 20px;
    }

    .title {
      margin-bottom: 20px;
    }
  }

  .freeze-left {
    display: flex;
    align-items: center;
  }

  .credit-freeze {
    font-size: 14px;
    align-items: center;
    margin-left: 10px;
    display: flex;

    .gap {
      margin-right: 5px
    }

    .credit-label {
      margin: 0 10px 0 0;
      background-color: #f0f9eb;
      border-color: #e1f3d8;
      color: #67c23a;
      display: inline-block;
      height: 32px;
      padding: 0 10px;
      line-height: 30px;
      font-size: 12px;
      border: 1px solid #d9ecff;
      border-radius: 4px;
      box-sizing: border-box;
      white-space: nowrap;
      min-width: 150px;
      // width: 200px;
      text-align: center;
    }

  }

  .refresh-lable {
    cursor: pointer;
    color: rgb(54, 13, 238);
    text-decoration: underline;
    padding-top: 5px;
    padding-left: 10px;
    font-size: 12px;
  }

  .clientInfo {
    .clientSum {
      > span {
        margin-right: 20px;
      }

      .el-tag {
        margin-right: 10px;
      }
    }
  }

  .freezeReason {
    display: flex;
    align-items: center;

    .el-tag {
      margin-right: 10px;
    }
  }

  .merchant {
    padding-bottom: 20px;
  }

  .merchantInfo {
    margin-bottom: 10px;

    .merchantInfo-row {
      margin-left: 10px;
      color: #909399;

      span {
        margin-right: 20px;
      }
    }
  }

  .productDesc {
    display: flex;
    align-items: center;

    .img {
      i {
        font-size: 28px;
        color: #67c23a;
      }

      display: flex;
      align-items: center;
      margin-right: 5px;
      cursor: pointer;
    }

    > div {
      text-align: left;
    }
  }

  .extraInfo {
    padding: 10px;
    position: relative;

    p {
      margin-bottom: 20px;
    }

    &-btn {
      position: absolute;
      top: 0;
      right: 5px;
    }
  }

  .icon-warning {
    position: relative;
    font-size: 26px;
    margin-right: 5px;
    color: #e6a23c;
  }

  .ba-row-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }

  .ba-row-end {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .ba-row-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .ba-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sealRow {
    text-align: center;
    margin-bottom: 10px;
  }

  .row-title {
    display: flex;
    justify-content: space-between;
  }

</style>

<style lang="scss">
.el-message-box__message {
  p {
    word-break: break-word;
  }
}
.pagination-container {
  text-align: right;
}
.align-center {
  text-align: center;
}
.btb-wrapper {
  overflow-y: scroll;
  max-height: 400px;
}
.tag-info {
  display: inline;

  span {
    background-color: #f56c6c;
    border-radius: 11px;
    color: #fff;
  }
}
.custom-popover {
  height: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
}
</style>
