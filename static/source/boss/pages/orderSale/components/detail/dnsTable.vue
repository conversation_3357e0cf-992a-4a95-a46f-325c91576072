<template>
  <div>
    <el-table :data="listData" border>
      <el-table-column width="150" property="omsDnNo" label="OMS交货单号">
        <template slot-scope="scope">
          <el-button
            style="margin-right: 5px; text-align: left"
            type="text"
            @click="viewOutboundDetail(scope.row.omsDnNo)"
            >{{ scope.row.omsDnNo }}</el-button
          >
          <el-button
            v-if="scope.row.omsDnNo"
            type="text"
            @click="handleCopy(scope.row.omsDnNo, $event)"
            >复制</el-button
          >
        </template>
      </el-table-column>
      <el-table-column width="150" property="sapDnNo" label="SAP交货单号">
        <template slot-scope="scope">
          <el-button
            style="margin-right: 5px; text-align: left"
            type="text"
            @click="viewOutboundDetail(scope.row.sapDnNo)"
            >{{ scope.row.sapDnNo }}</el-button
          >
          <el-button
            v-if="scope.row.sapDnNo"
            type="text"
            @click="handleCopy(scope.row.sapDnNo, $event)"
            >复制</el-button
          >
        </template>
      </el-table-column>
      <el-table-column
        width="100"
        property="type"
        label="交货单类型"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          {{ scope.row.type | deliveryType }}
        </template>
      </el-table-column>
      <el-table-column width="80" property="status" label="发货状态">
        <template slot-scope="scope">
          {{ scope.row.status | deliveryStatus }}
        </template>
      </el-table-column>
      <el-table-column width="200" property="startPosition" label="起点">
        <template slot-scope="scope">
          {{
            (scope.row.startPosition || "") +
            (scope.row.startPositionName || "")
          }}
        </template>
      </el-table-column>
      <el-table-column width="200" property="endPosition" label="终点">
        <template slot-scope="scope">
          {{
            (scope.row.endPosition || "") + (scope.row.endPositionName || "")
          }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="float: right; margin-top: 10px"
      @current-change="handleCurrentChange"
      :current-page="table.pageNo"
      :page-size="table.pageSize"
      layout="total, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
</template>

<script>
import { copyToClipboard } from '@/utils/index';
export default {
  data() {
    return {
      table: {
        pageNo: 1,
        pageSize: 5
      }
    };
  },
  props: {
    tableData: {
      type: Array,
      default: () => {}
    }
  },
  computed: {
    listData() {
      return this.tableData.slice(
        (this.table.pageNo - 1) * this.table.pageSize,
        this.table.pageNo * this.table.pageSize
      );
    },
    total() {
      return this.tableData.length;
    }
  },
  filters: {
    deliveryType(type) {
      if (type === '401') {
        return '集货调拨出库单';
      }
      if (type === '402') {
        return '集货调拨入库单';
      }
      if (type === '403') {
        return '集货库存申请单';
      }
      if (type === '404') {
        return '集货库存申请单';
      }
      return '外向交货单';
    },
    deliveryStatus(status) {
      if (status === 'NEW') {
        return '已创建';
      }
      if (status === 'SENT') {
        return '已发货';
      }
      if (status === 'CONFIRM') {
        return '已下发';
      }
      if (status === 'CANCEL') {
        return '已取消';
      }
    }
  },
  methods: {
    handleCurrentChange(pageNo) {
      this.table.pageNo = pageNo;
    },
    viewOutboundDetail (no) {
      console.log(no)
      this.$router.push({
        path: '/warehousing/detail/' + no,
        query: {
          outboundNo: no,
          soNo: this.$route.query.soNo,
          deliveryNo: no
        }
      })
    },
    handleCopy(no, event) {
      copyToClipboard.bind(this)(no, event);
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
