<template>
  <div>
    <el-row class="list_con">
      <el-col :span="24" >
        <div class="brief-count">
          应收金额
          <el-tooltip class="item" effect="dark" content="已开票总金额" placement="top">
            <i class="el-icon-question"/>
          </el-tooltip>
          ：<span style="margin-right: 5px">{{formatMoney('receivableAmount')}}</span>
          订单已核销总额
          <el-tooltip class="item" effect="dark" content="已开票并回款核销的金额" placement="top">
            <i class="el-icon-question"/>
          </el-tooltip>
          ：<span style="margin-right: 5px">{{formatMoney('writtenOffAmount')}}</span>
          订单未收总额
          <el-tooltip class="item" effect="dark" content="应收金额-已核销总额" placement="top">
            <i class="el-icon-question"/>
          </el-tooltip>
          ：<span style="margin-right: 5px">{{formatMoney('uncollectedAmount')}}</span>
          <span :class="{'clickable':clickable}" @click="navToInvoice">红账金额</span>
          <el-tooltip class="item" effect="dark" content="到账期且已逾期的未核销的金额" placement="top">
            <i class="el-icon-question"/>
          </el-tooltip>
          ：<span style="margin-right: 5px">{{formatMoney('redAccountAmount')}}</span>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getreceiveRecord } from '@/api/orderSupplyList'
import { formatCurrencySymbol } from '@/utils/price.js'

export default {
  name: 'ReceiveRecord',
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      receiveRecordData: {}
    }
  },
  computed: {
    clickable () {
      // eslint-disable-next-line
      return true
    }
  },
  methods: {
    navToInvoice () {
      if (this.clickable) {
        this.$emit('change-tab', 'in')
      }
    },
    formatMoney (val) {
      let result = ''
      if (this.receiveRecordData[val] != null) {
        if (val === 'receivableAmount') {
          result = `${formatCurrencySymbol(this.receiveRecordData.receivableAmountCurrency) || this.receiveRecordData.amountSymbol}${this.receiveRecordData[val] || 0}(${this.receiveRecordData.amountSymbol}${this.receiveRecordData.receivableAmountCny || this.receiveRecordData.redAccountAmount})`
        } else {
          result = this.receiveRecordData.amountSymbol + (this.receiveRecordData[val] || 0)
        }
      }
      return result
    },
    getreceiveRecord () {
      getreceiveRecord(this.voucherNo)
        .then(res => {
          if (res && res.status === 200) {
            this.receiveRecordData = res.datas
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error('请求收款记录接口失败！')
        })
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    activeName: {
      handler (newName, oldName) {
        if (!this.hasLoaded && this.voucherNo && newName === 'rc') {
          this.getreceiveRecord()
        }
      },
      immediate: true
    }
  },
  created () {},
  mounted () {}
}
</script>

<style scoped lang="less">
.brief-count{
  margin-top: 8px;
  margin-bottom: 20px;
  span{
    font-weight: bold;
    margin-right: 10px;
  }
  span.clickable{
    margin-right: 5px;
  }
  span.clickable:hover{
    text-decoration: underline;
    cursor: pointer;
  }
}
.dialog-footer{
  display: flex;
  justify-content: space-around;
}
.table{
  margin: 2px;
  margin-top: 6px;
  margin-bottom: 6px;
  width: 756px;
}
.title{
  padding: 5px;
}
.title,.footer{
  p{
    height: 24px;
    line-height: 24px;
  }
}
.tax-price,.header{
  padding: 5px;
  display: flex;
  p{
    flex: 1;
    span{
      font-weight: bold;
    }
  }
}
.header{
  justify-content: space-between;
}
.bordered{
  border: solid 1px grey;
  margin: 2px;
}
.footer{
  display: flex;
  .left,.right{
    flex: 1;
    padding: 5px;
  }
  .right{
    overflow: auto;
  }
}

</style>
