<template>
  <div>
    <el-row class="list_con">
      <el-col :span="24" >
        <div class="brief-count">
          销售订单已交货含税总金额：<span>{{`${formatCurrencySymbol(invoiceStat.currency)}${invoiceStat.deliveredAmount}`}}</span>
          未交货总金额：<span>{{`${formatCurrencySymbol(invoiceStat.currency)}${invoiceStat.undeliveredAmount}`}}</span>
          已开票总金额：<span>{{`${formatCurrencySymbol(invoiceStat.currency)}${invoiceStat.invoicedAmount}`}}</span>
          未开票总金额：<span>{{`${formatCurrencySymbol(invoiceStat.currency)}${invoiceStat.unbilledAmount}`}}</span>
        </div>
      </el-col>
    </el-row>
    <el-row class="list_con">
      <el-col :span="24">
        <el-table
          v-loading="listLoading"
          :data="listData"
          border
          fit
          highlight-current-row
          stripe
          style="width:100%">
          <el-table-column align="center" min-width="120px"  label="SAP开票凭证" prop="voucherId">
            <template slot-scope="scope">
              <span>{{ scope.row.voucherId }}</span>
            </template>
          </el-table-column>

          <el-table-column align="center" min-width="120px" label="开票凭证类型">
            <template slot-scope="scope">
              <span>{{ scope.row.voucherType }}</span>
            </template>
          </el-table-column>

          <el-table-column align="center" min-width="120px" label="会计过审状态">
            <template slot-scope="scope">
              <span>{{ scope.row.postingStatus }}</span>
            </template>
          </el-table-column>

          <el-table-column align="center" label="开票类型">
            <template slot-scope="scope">
              <span>{{ scope.row.invoiceType }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="发票号码" prop="invoiceNo" />
          <el-table-column align="center" label="发票代码" prop="invoiceCode" />
          <el-table-column align="center" label="总含税金额">
            <template slot-scope="scope">
              <span>{{ scope.row.invoiceAmount ? scope.row.amountSymbol+''+scope.row.invoiceAmount: '' }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建日期" prop="createTime" />
          <el-table-column align="center" label="开票日期" prop="billingTime" />
          <el-table-column align="center" label="发票内容">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="detail_link"
                @click="openInvoicePreview(scope.row)">
                详情预览
              </el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="寄票状态">
            <template slot-scope="scope">
              <div v-if="scope.row.logisticsNo">
                <el-popover placement="top" width="400" trigger="hover">
                  <div class="flex-table" >
                    <div>发票运单号</div>
                    <div>物流公司</div>
                    <div>物流详情</div>
                  </div>
                  <div class="flex-table" >
                    <div>{{ scope.row.logisticsNo }}</div>
                    <div>{{ scope.row.logisticsCompany }}</div>
                    <div>
                      <el-button type="text" class="detail_link"
                        @click="viewExpressDetail(scope.row)"> 查看
                      </el-button>
                    </div>
                  </div>
                  <span slot="reference">
                    <span style="text-decoration: underline;color:steelblue">已寄详情</span>
                  </span>
                </el-popover>
              </div>
              <span v-else>未寄</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="账期" prop="accountPeriod" />
          <el-table-column align="center" min-width="120px" label="核销金额" prop="writtenOffAmount">
            <template slot-scope="scope">
              <span>{{ scope.row.writtenOffAmount? scope.row.amountSymbol + scope.row.writtenOffAmount:'' }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" label="发票运单号">
            <template slot-scope="scope">
              <span>{{ scope.row.logisticsNo }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="物流公司">
            <template slot-scope="scope">
              <span>{{ scope.row.logisticsCompany }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="物流详情">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="detail_link"
                @click="viewExpressDetail(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column> -->
        </el-table>
        <!-- <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="listQuery.current"
          :limit.sync="listQuery.pageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="getInvoiceList"
        /> -->
      </el-col>
    </el-row>
    <el-dialog
      title="发票详情预览"
      :visible.sync="invoicePreviewVisible"
      width="800px"
      custom-class="invoice-preview"
      top="10vh">
      <div class="header">
        <p>发票号码：<span>{{invoicePreviewData.invoiceNo}}</span></p>
        <p>发票代码：<span>{{invoicePreviewData.invoiceCode}}</span></p>
      </div>
      <div class="title bordered">
        <p>购买方：<span>{{invoicePreviewData.purchaserName}}</span></p>
        <p>名称：<span>{{invoicePreviewData.purchaserName}}</span></p>
        <p>纳税人识别号：<span>{{invoicePreviewData.purchaserTaxpayerNo}}</span></p>
        <p>地址电话：<span>{{invoicePreviewData.purchaserAddressPhone}}</span></p>
        <p>开户行及账号：<span>{{(invoicePreviewData.purchaserBank || '') + ' '+ (invoicePreviewData.purchaserAccountNo|| '')}}</span></p>
      </div>
      <el-table
        class="table"
        :data="invoicePreviewData.lineList"
        border
        fit
        highlight-current-row
        stripe>
        <el-table-column width="160" align="center" label="货物或应税劳务服务名称" prop="productDesc" />
        <el-table-column align="center" label="规格型号" prop="specificationModel" />
        <el-table-column align="center" label="单位" prop="salesUnit" />
        <el-table-column align="center" label="数量" prop="quantity" />
        <el-table-column align="center" label="单价" prop="unitPrice" />
        <el-table-column align="center" label="金额" prop="amount" />
        <el-table-column align="center" label="税率" prop="taxRate" />
        <el-table-column align="center" label="税额" prop="tax" />
      </el-table>
      <div class="bordered tax-price">
        <p>价税合计（大写）：<span>{{invoicePreviewData.totalPriceAndTaxUpper}}</span></p>
        <p>（小写）：<span>{{invoicePreviewData.totalPriceAndTax}}</span></p>
      </div>
      <div class="footer">
        <div class="left bordered">
          <p>销售方：<span>{{invoicePreviewData.sellerName}}</span></p>
          <p>名称：<span>{{invoicePreviewData.sellerName}}</span></p>
          <p>纳税人识别号：<span>{{invoicePreviewData.sellerTaxpayerNo}}</span></p>
          <p>地址电话：<span>{{(invoicePreviewData.sellerAddress|| '') + ' '+ (invoicePreviewData.sellerPhone|| '')}}</span></p>
          <p>开户行及账号：<span>{{(invoicePreviewData.sellerBank || '')+ ' '+ (invoicePreviewData.sellerAccountNo|| '')}}</span></p>
        </div>
        <div class="right bordered">
          <p>备注：{{invoicePreviewData.remark}}</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="invoicePreviewVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import Pagination from '@/components/Pagination'
import { filterNone } from '@/filters/index'
import { fetchInvoiceProgressList, fetchInvoiceStat, fetchInvoiceDetail } from '@/api/orderSupplyList'
import { formatCurrencySymbol } from '@/utils/price.js'

export default {
  name: 'InvoiceProgress',
  // components: {
  //   Pagination
  // },
  filters: {
    filterNone
  },
  // 父组件通过props属性传递进来的数据
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      invoicePreviewData: {},
      invoicePreviewVisible: false,
      listLoading: false,
      soVoucherId: this.voucherNo,
      listQuery: {
        pageSize: 10,
        current: 1
      },
      invoiceStat: {
        deliveredAmount: '',
        invoicedAmount: '',
        unbilledAmount: '',
        undeliveredAmount: ''
      },
      total: 0,
      listData: [], // 前置单列表数据
      hasLoaded: false // 当前组件是否已加载过
    }
  },
  methods: {
    formatCurrencySymbol,
    // 【查看】跳转到物流详情
    viewExpressDetail (row) {
      console.log(row)
      // let key = (row.logisticsNo || '') + (row.logisticsCompanyNo || '')
      // this.$router.push({
      //   path: '/orderSale/formal/invoice/detail/' + key,
      //   query: {
      //     companyNo: row.logisticsCompanyNo, //  0 非直发 、1 供应商直发
      //     logisticsNo: row.logisticsNo
      //   }
      // })
      window.open(`/sr/logistics?soNo=${this.$route.query.soNo}&logisticsCode=${row.logisticsNo}&logisticsCompanyNo=${row.logisticsCompanyNo}`)
    },
    openInvoicePreview (row) {
      this.invoicePreview = row
      this.invoicePreviewVisible = true
      const loading = this.$loading({
        target: '.invoice-preview'
      })
      fetchInvoiceDetail(row.voucherId)
        .then(response => {
          if (response.status === 200) {
            if (response.datas) {
              this.invoicePreviewData = response.datas
            }
          } else {
            this.$notify.error(response.msg || response.message || '获取发票内容失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '获取发票内容失败！')
        })
        .finally(_ => {
          loading.close()
        })
    },
    getInvoiceStat () {
      fetchInvoiceStat(this.voucherNo)
        .then(response => {
          if (response.status === 200 && response.datas) {
            this.invoiceStat = response.datas
          } else {
            this.$notify.error(response.msg || response.message)
          }
        })
        .catch(e => {
          this.$message.error(e.msg || e.message || '获取发票统计失败！')
        })
        .finally(() => {
        })
    },
    getInvoiceList () {
      this.listLoading = true
      fetchInvoiceProgressList(this.soVoucherId)
        .then(response => {
          if (response.status === 200) {
            if (response.datas) {
              this.listData = response.datas
            }
          } else {
            this.$notify.error(response.msg || response.message)
          }
        })
        .catch(e => {
          this.$message.error(e.msg || e.message || '获取发票进度失败！')
        })
        .finally(() => {
          this.listLoading = false
          this.hasLoaded = true
        })
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    activeName: {
      handler (newName, oldName) {
        if (!this.hasLoaded && this.voucherNo && newName === 'in') {
          this.getInvoiceList()
          this.getInvoiceStat()
        }
      },
      immediate: true
    }
  },
  created () {},
  mounted () {}
}
</script>

<style scoped lang="less">
.brief-count{
  margin-top: 8px;
  margin-bottom: 20px;
  span{
    font-weight: bold;
    margin-right: 20px;
  }
}
.dialog-footer{
  display: flex;
  justify-content: space-around;
}
.table{
  margin: 2px;
  margin-top: 6px;
  margin-bottom: 6px;
  width: 756px;
}
.title{
  padding: 5px;
}
.title,.footer{
  p{
    height: 24px;
    line-height: 24px;
  }
}
.tax-price,.header{
  padding: 5px;
  display: flex;
  p{
    flex: 1;
    span{
      font-weight: bold;
    }
  }
}
.header{
  justify-content: space-between;
}
.bordered{
  border: solid 1px grey;
  margin: 2px;
}
.footer{
  display: flex;
  .left,.right{
    flex: 1;
    padding: 5px;
  }
  .right{
    overflow: auto;
  }
}
.flex-table{
  display: flex;
  justify-content: center;
  div{
    width: 100px;
    margin: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  div:first-child{
    width: 180px;
  }
}
</style>
