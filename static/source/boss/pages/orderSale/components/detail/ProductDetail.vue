<template>
  <el-dialog
    :show-close="false"
    title="商品详情"
    :visible.sync="showDlg"
    top="10px"
    width="800px"
    @closed="$emit('update:showDialog', false)"
  >
    <DividerHeader>商品信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">sku：{{ currentSku.materiel }}</el-col>
      <el-col :span="12">客户物料号：{{ currentSku.customerMaterialNo }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">项目行：{{ currentSku.soItemNo }}</el-col>
      <el-col :span="12">客户行号：{{ currentSku.customerOrderNo }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">制造商型号：{{ sku.manufacturerNo }}</el-col>
      <el-col :span="12">客户规格型号：{{ currentSku.customerSpecificationModel }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">商品描述：{{ sku.materialDescribe }}</el-col>
      <el-col :span="12">客户物料名称：{{ currentSku.customerMaterialName }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">数量：{{ currentSku.quantity }}</el-col>
      <el-col :span="12">客户物料数量：{{ currentSku.customerMaterialQuantity }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">单位：{{ formatQuantityUnit(currentSku.quantityUnit) }}</el-col>
      <el-col :span="12">客户物料单位：{{ currentSku.customerMaterialUnit }}</el-col>
    </el-row>
    <DividerHeader>仓位信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">工厂：{{ formatFactory(currentSku.factory) }}</el-col>
      <el-col :span="12">选择直发：{{ currentSku.directDeliverySupplier | dict(dictList,'directDeliverySupplier') }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">发货仓：{{ formatPosition(currentSku.position) }}</el-col>
      <el-col :span="12">集货仓：{{ formatPosition(currentSku.deliveryPosition) }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        是否集货
        <el-tooltip effect="dark" content="代表货物是否需要集中合并后发给客户" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        ：{{ currentSku.needCollect ? '是' : '否' }}
      </el-col>
      <el-col :span="12">
        是否集齐
        <el-tooltip effect="dark" content="代表对应集货仓下，需要集货的商品是否已全部到达集货仓" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        ：{{ currentSku.isCollectAll ? '是' : '否'  }}
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        发货仓发出数量
        <el-tooltip effect="dark" content="代表从发货仓发出的数量" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        ：{{ currentSku.transferOutQty }}
      </el-col>
      <el-col :span="12">
        发货仓的占库数量
        ：{{ currentSku.occupyQty }}
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        集货仓发出数量
        <el-tooltip effect="dark" content="代表从集货仓发货的数量" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        ：{{ currentSku.deliveryQty }}
      </el-col>
      <el-col :span="12">
        集货仓到货数量
        <el-tooltip effect="dark" content="代表在集货仓收到的数量" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        ：{{ currentSku.transferInQty }}
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        集货取消数量
        <el-tooltip effect="dark" content="代表取消集货的数量" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        ：{{ currentSku.deliveryCancelQty }}
      </el-col>
      <el-col :span="12">
        盘亏数量
        ：{{ currentSku.inventoryLossQuantity }}
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        关联订单行
        <el-tooltip effect="dark" content="代表集货补货是基于哪一行商品行进行补发的" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        ：{{ currentSku.omsReferenceOrderItemNo }}
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">仓库修改原因：{{ formatOrderReason(currentSku.positionModifyReason) }}</el-col>
      <el-col :span="12">仓库修改详情：{{ currentSku.positionModifyDetail }}</el-col>
    </el-row>
    <DividerHeader>价格信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">未税单价：{{ (currentSku.freeTaxPrice|| 0).toFixed(6) }}</el-col>
      <el-col :span="12">含税单价：{{ ( currentSku.taxPrice|| 0).toFixed(6) }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">税率：{{ (currentSku.taxRate === null || currentSku.taxRate === void 0) ? '--' : (currentSku.taxRate*100)+'%' }}</el-col>
      <el-col :span="12">整行税额：{{ currentSku.tax }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">货币：{{ currentSku.currency | dict(dictList,'currency') }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">商品未税总金额：{{ (currentSku.freeTotalPrice || 0).toFixed(6) }}</el-col>
      <el-col :span="12">商品含税总金额：{{ (currentSku.taxTotalPrice || 0).toFixed(6) }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">折扣类型：{{ currentSku.discountConditionType| dict(dictList,'discountConditionType') }}</el-col>
      <el-col :span="12">折扣金额：{{ currentSku.discountAmount }}</el-col>
    </el-row>
    <DividerHeader>其他</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="6">
        <el-checkbox :value="currentSku.needScrapingCode==='X'" :disabled="true">需要刮码</el-checkbox>
      </el-col>
      <el-col :span="6">
        <el-checkbox :value="currentSku.urgent==='X'" :disabled="true">加急</el-checkbox>
      </el-col>
      <el-col :span="6">
        <el-checkbox :value="currentSku.customerDateSensitive==='X'" :disabled="true">客户交期敏感</el-checkbox>
      </el-col>
      <el-col :span="6">
        <el-checkbox :value="currentSku.hasTcSupplier" :disabled="true">TC中转</el-checkbox>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">客户期望送达日期：{{ currentSku.customerDate }}</el-col>
      <el-col :span="12">请求发货日期：{{ currentSku.deliveryDate }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">减少大类原因：{{ currentSku.largeReduceReason | dict(dictList,'largeReduceReason') }}</el-col>
      <el-col :span="12">减少大类详情：{{ currentSku.largeReduceReasonDesc }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">替换OA流程：{{ currentSku.materialReplaceProcessNo }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">领用人：{{ currentSku.demandUser }}</el-col>
      <el-col :span="12">需求部门：{{ currentSku.demandDepartment }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">备注：{{ currentSku.remark }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">采购备注：{{ currentSku.purchaseNote }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">交货挂起原因：{{ currentSku.dnOrderPendingDesc }}</el-col>
      <el-col :span="12">不纳入需求挂起原因：{{ currentSku.notAcceptDemandDesc }}</el-col>
    </el-row>
    <template v-if="showCustom">
      <el-row :gutter="10" v-for="(custom, index) in (currentSku && currentSku.customPropertyList || [])" :key="index">
        <el-col :span="12">
          {{'定制属性' + (index + 1)}}: {{custom.customProperty}}
        </el-col>
        <el-col :span="12">
          {{'定制属性备注' + (index + 1)}}: {{custom.customPropertyRemark}}
        </el-col>
      </el-row>
    </template>
    <el-row :gutter="10" v-if="soInfo&&soInfo.orderType==='Z018'">
      <el-col :span="12">租赁截止日期：{{ currentSku.rentalDueDate }}</el-col>
      <el-col :span="12">固定资产编号：{{ currentSku.fixedAssetsId }}</el-col>
    </el-row>
    <div class="ba-center">
      <el-button type="primary" @click="prev">上一行</el-button>
      <el-button type="primary" @click="next">下一行</el-button>
      <el-button type="primary" plain @click="$emit('update:showDialog', false)">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { formatPrice, isNumber } from '@/utils'
import DividerHeader from '@/components/DividerHeader'

export default {
  props: ['showDialog', 'soInfo', 'currentSku'],
  components: {
    DividerHeader
  },
  computed: {
    showCustom () {
      return (/z001/gim.test(this.soInfo?.orderType) && this.currentSku?.customPropertyList?.length > 0)
    },
    sku () {
      return this.$store.state.orderDetail.sku || { packageInfoList: {} }
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    formatOrderReason (value) {
      if (this.dictList && this.dictList['positionModifyReason']) {
        const founditem = this.dictList['positionModifyReason'].find(item => item.code === value)
        return founditem ? founditem.name : ''
      }
      return value
    },
    formatFactory (value) {
      if (this.dictList && this.dictList['factory']) {
        const founditem = this.dictList['factory'].find(item => item.code === value)
        return founditem ? value + founditem.name : ''
      }
      return value
    },
    formatPosition (value) {
      if (this.dictList && this.dictList['position']) {
        const founditem = this.dictList['position'].find(item => item.code === value)
        return founditem ? value + founditem.name : ''
      }
      return value
    },
    formatPrice (value) {
      return formatPrice(value, 6)
    },
    formatQuantityUnit (value) {
      if (value && isNumber(value)) {
        const unit = this.dictList['quantityUnit'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    formatPendingReason (type, list) {
      if (this.dictList && this.dictList[type]) {
        const res = (list || '').split(',')?.map(code => {
          const foundItem = this.dictList[type].find(item => item.status === 'normal' && item.code === code);
          return foundItem ? foundItem.name : ''
        })
        return res.join(',')
      }
      return list
    },
    next () {
      this.$emit('next', this.currentSku)
    },
    prev () {
      this.$emit('prev', this.currentSku)
    }
  }
}
</script>
<style scoped lang="scss">
.el-row {
  margin-bottom: 20px;
}
.ba-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
