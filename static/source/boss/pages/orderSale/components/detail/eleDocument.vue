<template>
  <div class="ele-document-container">
    <div style="margin-left: 132px; margin-bottom: 10px">
      <el-popover placement="top" width="120" trigger="click" >
        <div class="content" style="display: flex;flex-direction: column;">
        <el-button type="primary" plain size="mini" @click="showUploadDialog({sapOrderNo:voucherNo},'order')">订单附件</el-button> <br />
        <el-button type="primary" plain size="mini" @click="showUploadDialog({sapOrderNo:voucherNo},'other')">其他</el-button>
        </div>
        <el-button :disabled="!orderInfo.sapOrderNo" type="primary" plain size="mini" slot="reference">
          继续上传<i class="el-icon-upload el-icon--right" />
        </el-button>
      </el-popover>
      <el-button :disabled="!orderInfo.sapOrderNo" @click="handleSyncFiles" style="margin-left: 10px;" type="primary" plain size="mini">
        文档同步<i class="el-icon-refresh" />
      </el-button>
    </div>
    <el-tabs v-model="activeTab" tab-position="left" class="document_tab_con">
      <el-tab-pane name="img" :label="`图片(${pageInfo.imgOrder.total + pageInfo.imgOther.total + pageInfo.imgSignTotal})`">
        <el-timeline>
          <el-timeline-item>
            <div class="tab-table-title">订单附件</div>
            <el-table v-loading="loading.imgListOrderLoading" :data="imgListOrder"
              border fit highlight-current-row>
              <el-table-column label="序号" type="index" width="100" align="center" fixed="left"
                :index="i => getIndex(i, pageInfo.imgOrder.pageNo, pageInfo.imgOrder.pageSize)"/>
              <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
              <el-table-column label="类型" min-width="50px" align="center" prop="fileType"/>
              <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
              <el-table-column label="创建时间" min-width="100px" align="center" prop="uploadTime"/>
              <el-table-column label="备注" min-width="100px" align="center" prop="remark" show-overflow-tooltip/>
              <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="remarkEdit(scope.row, 'order')">
                    {{ Boolean(scope.row.remark) ? '编辑备注' : '添加备注' }}
                  </el-button>
                  <el-button type="text" size="mini" @click="preview(scope.row, scope.$index)">预览</el-button>
                  <el-button type="text" size="mini" @click="download(scope.row)">下载</el-button>
                  <el-button type="text" size="mini" @click="del(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="pageInfo.imgOrder.total > pageInfo.imgOrder.pageSize"
              :total="pageInfo.imgOrder.total"
              :page.sync="pageInfo.imgOrder.pageNo"
              :limit.sync="pageInfo.imgOrder.pageSize"
              layout="total, prev, pager, next, jumper"
              @pagination="getImgList('Order')"/>
          </el-timeline-item>
          <el-timeline-item>
            <div class="tab-table-title">签收附件</div>
            <div class="sku-search">
              <el-form
                ref="skuSearchForm"
                :model="skuSearchForm"
                label-width="120px"
                :inline="true"
                label-position="right">
                <el-form-item label="搜索签收商品：" prop="sku1">
                  <el-input v-model.trim="skuSearchForm.sku1" placeholder="请输入SKU" clearable/>
                </el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleSkuFilter('img')">查询</el-button>
              </el-form>
            </div>
            <el-table v-loading="imgSignListLoading" :data="imgSignList"
              :span-method="arraySpanMethod" border fit>
              <el-table-column label="序号" align="center">
                <template slot-scope="scope">
                  <div v-if="!scope.row.expandItem">{{scope.$index + 1}}</div>
                  <el-table v-else :data="scope.row.dnDetail" border fit highlight-current-row>
                    <el-table-column align="center" label="序号" type="index" />
                    <el-table-column align="center" label="行号" prop="lineNum" />
                    <el-table-column align="center" label="客户订单号" prop="customerOrderNo" />
                    <el-table-column align="center" label="客户物料号" prop="customerMaterial" />
                    <el-table-column align="center" label="商品编码" prop="productNumber" />
                    <el-table-column align="center" label="商品名称" min-width="200px" prop="productName" />
                    <el-table-column align="center" label="数量" prop="outboundQuantity" />
                    <el-table-column align="center" label="单位" prop="unit" />
                    <el-table-column align="center" label="出库数量" prop="outboundQuantity" />
                    <el-table-column align="center" label="批次号" prop="supplierBatch" />
                    <el-table-column align="center" label="质量状态" prop="qualityStatus" />
                    <el-table-column align="center" label="首批次" prop="firstBatch" />
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column label="出库单" min-width="100px" align="center" prop="dnNo" />
              <el-table-column label="文件名" min-width="180px" align="center">
                <template slot-scope="{ row }">
                  <div v-for="(img, index) in row.ecorpAttachments" :key="index">
                    <span style="margin-right:10px">{{img.fileName}}</span>
                    <el-button type="text" size="mini" @click="preview(img)">预览</el-button>
                    <el-button type="text" size="mini" @click="download(img)">下载</el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" min-width="100px" align="center" prop="uploadTime"/>
              <el-table-column label="类型" min-width="100px" align="center" prop="orderType">
                <template slot-scope="{ row }">
                    <span v-if="row.orderType == 1">直发</span>
                    <span v-else-if="row.orderType == 0">非直发</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="120px" align="center" prop="remark" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <div>
                    <span style="margin-right: 10px">{{row.ecorpAttachments && row.ecorpAttachments[0].remark}}</span>
                    <el-button type="text" size="mini" @click="remarkEdit(row)">
                      {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <el-button type="text" size="mini" @click="seeDetail(row, 'img')">
                    {{row.expand ? '收起明细': '展开明细'}}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-timeline-item>
          <el-timeline-item>
            <div class="tab-table-title">其他附件</div>
            <el-table v-loading="loading.imgListOtherLoading" :data="imgListOther" border fit highlight-current-row>
              <el-table-column label="序号" type="index" width="100" align="center" fixed="left"
                :index="i => getIndex(i, pageInfo.imgOther.pageNo, pageInfo.imgOther.pageSize)"/>
              <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
              <el-table-column label="类型" min-width="50px" align="center" prop="fileType"/>
              <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
              <el-table-column label="创建时间" min-width="100px" align="center" prop="uploadTime"/>
              <el-table-column label="备注" min-width="100px" align="center" prop="remark" show-overflow-tooltip/>
              <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" @click="remarkEdit(scope.row, 'order')">
                    {{ Boolean(scope.row.remark) ? '编辑备注' : '添加备注' }}
                  </el-button>
                  <el-button type="text" size="mini" @click="preview(scope.row, scope.$index)">预览</el-button>
                  <el-button type="text" size="mini" @click="download(scope.row)">下载</el-button>
                  <el-button type="text" size="mini" @click="del(scope.row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="pageInfo.imgOther.total > pageInfo.imgOther.pageSize"
              :total="pageInfo.imgOther.total"
              :page.sync="pageInfo.imgOther.pageNo"
              :limit.sync="pageInfo.imgOther.pageSize"
              layout="total, prev, pager, next, jumper"
              @pagination="getImgList('Other')"/>
          </el-timeline-item>
        </el-timeline>
      </el-tab-pane>
      <el-tab-pane name="doc" :label="`文档(${pageInfo.docOrder.total + pageInfo.docOther.total + pageInfo.docSignTotal + pageInfo.deliveryAttachmentTotal})`">
        <el-timeline>
          <el-timeline-item>
            <div class="tab-table-title">订单附件</div>
            <el-table v-loading="loading.docListOrderLoading" :data="docListOrder" border fit highlight-current-row>
              <el-table-column label="序号" type="index"
                :index="i => getIndex(i, pageInfo.docOrder.pageNo, pageInfo.docOrder.pageSize)"
                width="100" align="center" fixed="left"/>
              <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
              <el-table-column label="类型" min-width="50px" align="center" prop="fileType"/>
              <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
              <el-table-column label="创建时间" min-width="100px" align="center" prop="uploadTime"/>
              <el-table-column label="备注" min-width="100px" align="center" prop="remark" show-overflow-tooltip/>
              <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <el-button type="text" size="mini" @click="remarkEdit(row, 'order')">
                    {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
                  </el-button>
                  <el-button type="text" size="mini" @click="previewPicOrPdf(row)">预览</el-button>
                  <el-button type="text" size="mini" @click="download(row)">下载</el-button>
                  <el-button type="text" size="mini" @click="del(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="pageInfo.docOrder.total > pageInfo.docOrder.pageSize"
              :total="pageInfo.docOrder.total"
              :page.sync="pageInfo.docOrder.pageNo"
              :limit.sync="pageInfo.docOrder.pageSize"
              layout="total, prev, pager, next, jumper"
              @pagination="getDocList('Order')"
            />
          </el-timeline-item>
          <el-timeline-item>
            <div class="tab-table-title">签收附件</div>
            <div class="sku-search">
              <el-form
                ref="skuSearchForm"
                :model="skuSearchForm"
                label-width="120px"
                :inline="true"
                label-position="right">
                <el-form-item label="搜索签收商品：" prop="sku2">
                  <el-input v-model.trim="skuSearchForm.sku2" placeholder="请输入SKU" clearable/>
                </el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="handleSkuFilter('doc')">查询</el-button>
              </el-form>
            </div>
            <el-table
              v-loading="docSignListLoading"
              :data="docSignList"
              :span-method="arraySpanMethod"
              border
              fit>
              <el-table-column
                label="序号"
                align="center">
                <template slot-scope="scope">
                  <div v-if="!scope.row.expandItem">{{scope.$index + 1}}</div>
                  <el-table v-else :data="scope.row.dnDetail" border fit highlight-current-row>
                    <el-table-column align="center" label="序号" type="index" />
                    <el-table-column align="center" label="行号" prop="lineNum" />
                    <el-table-column align="center" label="客户订单号" prop="customerOrderNo" />
                    <el-table-column align="center" label="客户物料号" prop="customerMaterial" />
                    <el-table-column align="center" label="商品编码" prop="productNumber" />
                    <el-table-column align="center" label="商品名称" min-width="200px" prop="productName" />
                    <el-table-column align="center" label="数量" prop="outboundQuantity" />
                    <el-table-column align="center" label="单位" prop="unit" />
                    <el-table-column align="center" label="出库数量" prop="outboundQuantity" />
                    <el-table-column align="center" label="批次号" prop="supplierBatch" />
                    <el-table-column align="center" label="质量状态" prop="qualityStatus" />
                    <el-table-column align="center" label="首批次" prop="firstBatch" />
                  </el-table>
                </template>
              </el-table-column>
              <el-table-column label="出库单" min-width="100px" align="center" prop="dnNo" />
              <el-table-column label="文件名" min-width="180px" align="center">
                <template slot-scope="{ row }">
                  <div v-for="(doc, index) in row.ecorpAttachments" :key="index">
                    <span style="margin-right:10px">{{doc.fileName}}</span>
                    <el-button type="text" size="mini" @click="previewPicOrPdf(doc)">预览</el-button>
                    <el-button type="text" size="mini" @click="download(doc)">下载</el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="上传时间" min-width="100px" align="center" prop="uploadTime"/>
              <el-table-column label="类型" min-width="100px" align="center" prop="orderType">
                <template slot-scope="{ row }">
                    <span v-if="row.orderType == 1">直发</span>
                    <span v-else-if="row.orderType == 0">非直发</span>
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="120px" align="center" prop="remark" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <div>
                    <span style="margin-right: 10px">{{row.ecorpAttachments && row.ecorpAttachments[0].remark}}</span>
                    <el-button type="text" size="mini" @click="remarkEdit(row)">
                      {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <el-button type="text" size="mini" @click="seeDetail(row, 'doc')">
                    {{row.expand ? '收起明细': '展开明细'}}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-timeline-item>
          <el-timeline-item>
            <div class="tab-table-title">其他附件</div>
            <el-table v-loading="loading.docListOtherLoading" :data="docListOther" border fit highlight-current-row>
              <el-table-column label="序号" type="index"
                :index="i => getIndex(i, pageInfo.docOther.pageNo, pageInfo.docOther.pageSize)"
                width="100" align="center" fixed="left"/>
              <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
              <el-table-column label="类型" min-width="50px" align="center" prop="fileType"/>
              <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
              <el-table-column label="创建时间" min-width="100px" align="center" prop="uploadTime"/>
              <el-table-column label="备注" min-width="100px" align="center" prop="remark" show-overflow-tooltip/>
              <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <el-button type="text" size="mini" @click="remarkEdit(row, 'order')">
                    {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
                  </el-button>
                  <el-button type="text" size="mini" @click="previewFile(row)">预览</el-button>
                  <el-button type="text" size="mini" @click="download(row)">下载</el-button>
                  <el-button type="text" size="mini" @click="del(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="pageInfo.docOther.total > pageInfo.docOther.pageSize"
              :total="pageInfo.docOther.total"
              :page.sync="pageInfo.docOther.pageNo"
              :limit.sync="pageInfo.docOther.pageSize"
              layout="total, prev, pager, next, jumper"
              @pagination="getDocList('Other')"
            />
          </el-timeline-item>
          <el-timeline-item>
            <div class="tab-table-title">送货单附件</div>
            <el-table
              :data="deliveryAttachmentList" border fit highlight-current-row>
              <el-table-column label="序号" type="index" width="100" align="center" fixed="left"/>
              <el-table-column label="采购单" min-width="100px" align="center" prop="voucherNo"/>
              <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
              <el-table-column label="上传时间" min-width="100px" align="center" prop="uploadTime"/>
              <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
              <el-table-column label="备注" min-width="50px" align="center" prop="remark" show-overflow-tooltip/>
              <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
                <template slot-scope="{ row }">
                  <el-button type="text" size="mini" @click="remarkEdit(row, 'order')">
                    {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
                  </el-button>
                  <el-button type="text" size="mini" @click="previewFile(row)">预览</el-button>
                  <el-button type="text" size="mini" @click="download(row)">下载</el-button>
                  <el-button type="text" size="mini" @click="del(row)">删除</el-button>

                </template>
              </el-table-column>
            </el-table>
          </el-timeline-item>
        </el-timeline>
      </el-tab-pane>
      <el-tab-pane name="recycle" :label="`回收站(${pageInfo.recycleTotal})`">
        <el-row>
          <el-col :span="24">
            <div class="recycle_tips">
              <i class="el-icon-warning" />图片和文件在删除前会显示剩余天数，之后将永久删除。
            </div>
          </el-col>
        </el-row>
        <el-table v-loading="recycleListLoading" :data="recycleList" border fit highlight-current-row>
          <el-table-column label="序号" type="index" width="100" align="center" fixed="left"
            :index=" i => getIndex(i,pageInfo.recyclePageNo, pageInfo.recyclePageSize)"/>
          <el-table-column label="文件名" min-width="100px" align="center" prop="fileName" />
          <el-table-column label="类型" min-width="50px" align="center" prop="fileType" />
          <el-table-column label="上传人" min-width="50px" align="center" prop="upLoadName" />
          <el-table-column label="创建时间" min-width="100px" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="resume(row)">恢复</el-button>
              <span v-if="row.remainDay !== null" style="color: red">【{{ row.remainDay }}天后删除】</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="pageInfo.recycleTotal"
          :total="pageInfo.recycleTotal"
          :page.sync="pageInfo.recyclePageNo"
          :limit.sync="pageInfo.recyclePageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="edDustbinList"
        />
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="添加备注" :visible.sync="remarkVisible" width="600px">
      <el-form ref="dataForm" :rules="rules" :model="content" label-position="right" label-width="0px">
        <el-form-item label prop="remark">
          <el-input v-model="content.remark" clearable type="textarea" :autosize="{ minRows: 4, maxRows: 7 }"
            placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="remarkVisible = false">关闭</el-button>
        <el-button type="primary" @click="remarkSave">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="预览" :visible.sync="previewVisible" width="600px" top="10vh">
      <div style="width: 100%; max-height: 400px; overflow: auto; text-align: center; margin-bottom: 10px">
        <el-image v-if="previewImg" :src="previewImg" :preview-src-list="[previewImg]" />
        <i v-else class="el-icon-loading" />
      </div>
      <el-alert style="margin-bottom: 10px" title="点击图片查看原图" type="warning" show-icon close-text="知道了" />
      <el-form ref="dataForm" :rules="rules" :model="content" label-position="right" label-width="0px">
        <el-form-item label prop="remark">
          <el-input v-model="content.remark" clearable type="textarea" :autosize="{ minRows: 4, maxRows: 7 }"
            placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="remarkSave">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="上传附件"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="uploadDialogStatus"
      :before-close="handleUploadClose"
      :show-close="false">
      <div v-loading="uploadLoading" class="dialog-body" style="display:flex;align-items: center;flex-direction:column">
        <!-- <span v-if="uploadType === 'order'" style="margin:10px;color:#597bee">
          可将文件直接拖拽到改区域、或者点击上传按钮，仅支持PDF附件哟
        </span> -->
        <span style="margin:10px;color:#597bee">
          可将文件直接拖拽到该区域，或者点击上传按钮
        </span>
        <el-upload ref="uploadDialog" action="/ali-upload"
          style="display: inline-block;" drag
          :accept="acceptFileType.soCommonType"
          :show-file-list="true" multiple
          :with-credentials="true" :limit="5"
          :data="{appName: omsAppName}"
          :on-success="handleUploadSucess"
          :on-remove="handleUploadRemove"
          :on-error="handleUploadError"
          :before-upload="handleBeforeUpload"
          :on-exceed="handleUploadExceed"
          >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text"><em>点击或拖拽上传</em></div>
        </el-upload>
        <div v-if="uploadType === 'order'" style="display:flex; align-items: center;margin: 10px;">
          <span>打印方向</span>
          <el-select v-model="printDirection" style="width: 120px;margin-left: 10px;">
            <el-option value="cross" label="横向"></el-option>
            <el-option value="vertical" label="纵向"></el-option>
          </el-select>
        </div>
        <span slot="footer" class="dialog-footer" style="align-self:flex-end;margin-top:20px;">
          <el-button @click="cancelUpload">取消</el-button>
          <el-button @click="submitUpload" type="primary">提交</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { mapState } from 'vuex'
import moment from 'moment'
import {
  edDustbinList,
  docList,
  picList,
  upEcorpRemark,
  delEcorpDoc,
  recoverEcorpDoc,
  downloadFile,
  elePicSignAttachment,
  eleDocSignAttachment,
  pagePOItem,
  refreshAttachment
} from '@/api/ecorp'
import * as sellOrder from '@/api/orderSale'

import { saveAs as downloadjs } from 'file-saver'
import { spDebounce } from '@/utils/index.js'

export default {
  name: 'EleDocument',
  components: {
    Pagination
  },
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    },
    orderNo: {
      type: String,
      required: true,
      default: ''
    },
    orderInfo: {
      type: Object,
      required: true,
      default: () => ({})
    }
  },
  data () {
    return {
      omsAppName: window.omsAppName,
      printDirection: 'cross',
      uploadLoading: false,
      uploadType: '',
      uploadListRows: [],
      uploadList: [],
      uploadDialogStatus: false,
      fileSizeLimit: 10, // 上传文件大小限制，单位 MB
      fileName: '', // 上传文件名
      imgSignListLoading: false,
      docSignListLoading: false,
      recycleListLoading: false,
      remarkVisible: false,
      previewVisible: false,
      activeTab: 'img', // img, doc, recycle
      loading: {
        imgListOrderLoading: false,
        imgListOtherLoading: false,
        docListOrderLoading: false,
        docListOtherLoading: false
      },
      imgListOrder: [],
      imgListOther: [],
      docListOrder: [],
      docListOther: [],
      imgSignList: [],
      docSignList: [],
      recycleList: [],
      deliveryAttachmentList: [],
      pageInfo: {
        // 订单图片
        imgOrder: {
          pageNo: 1,
          pageSize: 10,
          total: 0
        },
        imgOther: {
          pageNo: 1,
          pageSize: 10,
          total: 0
        },
        // 签收单图片
        imgSignPageNo: 1,
        imgSignPageSize: 10,
        imgSignTotal: 0,
        // 订单文档
        docOrder: {
          pageNo: 1,
          pageSize: 10,
          total: 0
        },
        docOther: {
          pageNo: 1,
          pageSize: 10,
          total: 0
        },
        // 签收单文档
        docSignPageNo: 1,
        docSignPageSize: 10,
        docSignTotal: 0,
        // 订单回收站
        recyclePageNo: 1,
        recyclePageSize: 10,
        recycleTotal: 0,
        //
        deliveryAttachmentTotal: 0
      },
      content: {
        id: '',
        remark: ''
      },
      previewImg: '',
      rules: {
        remark: [{ max: 200, message: '长度为200字符以内！', trigger: 'blur' }]
      },
      searchForm: {
        id: this.$route.query.id || ''
      },
      hasLoaded: false, // 当前组件是否已加载过
      skuSearchForm: {},
      activeCollapseName: '1',
      syncFileLoading: false
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    activeName: {
      handler (newName, oldName) {
        console.log(newName);
        if (!this.hasLoaded && (this.voucherNo || this.orderNo) && newName === 'dz') {
          // this.getList()
          this.getImgList('Order')
          this.getImgList('Other')
          this.getDocList('Order')
          this.getDocList('Other')
          this.getSignImgList()
          this.getSignDocList()
          this.edDustbinList()
          this.getDeliveryAttachments()
        }
      },
      immediate: true
    },
    activeTab: {
      handler (newName, oldName) {
        if (this.activeName === 'dz') {
          if (newName === 'img') {
            this.getImgList('Order')
            this.getImgList('Other')
            this.getSignImgList()
          } else if (newName === 'doc') {
            this.getDocList('Order')
            this.getDocList('Other')
            this.getSignDocList()
          } else if (newName === 'recycle') {
            this.edDustbinList()
          }
        }
      }
    }
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    })
  },
  created () {
    this.throwCheckMsg = spDebounce(this.throwCheckMsg)
  },
  methods: {
    throwCheckMsg (callback) {
      callback && callback()
    },
    handleBeforeUpload (file) {
      console.log(file, this.uploadList);
      if (this.uploadList.some((item) => item.name === file.name)) {
        this.$message.error({
          message: '文件已存在'
        });
        return false;
      }
      if (!this.$validateFileType(file, this.acceptFileType.soCommonType)) return false

      const size = file.size / 1024 / 1024
      const isGtLimit = size > this.fileSizeLimit
      // const isPDF = /pdf/i.test(file.name)
      let pass = true
      if (isGtLimit) {
        pass = false
        this.returnMsg += `【${file.name}】大小：${size}M，上传文件不能超过` + this.fileSizeLimit + 'MB！<br/>'
      }
      // if (this.uploadType === 'order' && !isPDF) {
      //   pass = false
      //   this.returnMsg += `【${file.name}】不是pdf文件类型，只能上传PDF文件！<br/>`
      // }
      if (!pass) {
        this.throwCheckMsg(() => {
          this.$message({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: this.returnMsg
          })
          this.returnMsg = ''
        })
      }
      return pass
    },
    saveFileInfo (sapOrderNo, docMetaDataList = []) {
      const queryData = {
        source: 'BOSS',
        dimension: 'order',
        docUploadScene: 'existOrder',
        businessId: sapOrderNo,
        docMetaDataList
      }
      return sellOrder.saveDocumentMetaData(queryData)
    },
    resetUploadData () {
      this.uploadDialogStatus = false
      this.uploadListRows = []
      this.uploadList = []
      this.$refs.uploadDialog && this.$refs.uploadDialog.clearFiles()
    },
    cancelUpload () {
      this.resetUploadData()
    },
    async submitUpload () {
      if (!this.uploadList) return
      if (this.uploadList.filter(file => file.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      const uploadListRows = this.uploadListRows
      const uploadList = this.uploadList.map(upload => ({
        bucketName: upload.bucketName,
        fileName: upload.name,
        ossKey: upload.ossKey,
        upUserName: window.CUR_DATA.user && window.CUR_DATA.user.name,
        attachmentType: this.uploadType === 'order' ? 'general' : this.uploadType,
        printDirection: this.uploadType === 'order' ? this.printDirection : '',
        uploadTime: moment(new Date()).format('yyyy-MM-DD HH:mm:ss')
      }))
      if (!uploadList.length) return this.$message.error('请上传文件！')
      this.uploadLoading = true
      for (let row of uploadListRows) {
        const res = await this.saveFileInfo(row.sapOrderNo, uploadList)
        if (res.code === 200) {
          this.$message.success('上传成功！')
        } else {
          this.$message.error(res.msg || '上传失败！')
        }
      }
      this.uploadLoading = false
      this.resetUploadData()
      setTimeout(() => {
        this.getList(true)
      }, 150)
    },
    handleUploadClose (done) {
      this.cancelUpload()
      done && done()
    },
    showUploadDialog (row, type) {
      this.uploadType = type
      this.uploadListRows = Array.isArray(row) ? row : [row]
      this.uploadListRows = this.uploadListRows.filter(row => row.sapOrderNo)
      this.uploadDialogStatus = true
    },
    handleSyncFiles () {
      console.log('handleSyncFiles');
      this.syncFileLoading = true;
      refreshAttachment(this.orderInfo.sapOrderNo).then((res) => {
        if (res.code === 200) {
          this.$message.success(res.msg || '同步成功')
        }
      }).finally(() => {
        this.syncFileLoading = false;
      })
    },
    handleUploadSucess (res, file, fileList) {
      if (fileList.every((file) => file.status === 'success')) {
        this.uploadList = fileList.map(file => ({
          ...file,
          fileName: file.response && file.response[0] && file.response[0].name,
          ossKey: file.response && file.response[0] && file.response[0].objectKey,
          bucketName: file.response && file.response[0] && file.response[0].bucketName
        }))
        console.log(this.uploadList)
      }
    },
    handleUploadRemove (file, fileList) {
      this.uploadList = fileList.map(file => ({
        ...file,
        fileName: file.response && file.response[0] && file.response[0].name,
        ossKey: file.response && file.response[0] && file.response[0].objectKey,
        bucketName: file.response && file.response[0] && file.response[0].bucketName
      }))
    },
    handleUploadError (error) {
      this.$message.error(error.error || error.message || '上传失败！')
    },
    handleUploadExceed (error) {
      console.log(error)
      this.$message.error(error.error || error.message || '文件最多上传5个！')
    },
    fileType (url) {
      let ret = ''
      if (/pdf/.test(url)) { ret = 'pdf' }
      if (/xls(x|m)?/.test(url)) { ret = 'excel' }
      if (/doc(s|x)?/.test(url)) { ret = 'word' }
      return ret
    },
    previewFile (row) {
      const type = this.fileType(row.url)
      if (type === 'pdf') {
        window.open(row.url)
      } else if (type === 'excel' || type === 'word') {
        window.open(`https://view.officeapps.live.com/op/view.aspx?src=${row.url}`)
      }
    },
    arraySpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 && row.expandItem === true) { return [1, 7] }
    },
    seeDetail (row, type) {
      console.log(row)
      console.log(this[`${type}SignList`])
      if (row.expand === true) {
        // 收缩
        this.hideDnDetail(row, type)
      } else {
        // 展开
        this.expandDnDetail(row, type)
      }
    },
    hideDnDetail (row, type) {
      if (row.expand === true) {
        row.expand = false
        const index = this[`${type}SignList`].indexOf(row)
        this[`${type}SignList`].splice(index + 1, 1)
      }
    },
    expandDnDetail (row, type) {
      setTimeout(() => {
        row.expand = true
        const dnDetailList = row.dnDetail || []
        const dnDetail = {}
        dnDetail.expandItem = true
        dnDetail.dnDetail = dnDetailList
        const index = this[`${type}SignList`].indexOf(row)
        this[`${type}SignList`].splice(index + 1, 0, dnDetail)
      })
    },
    resetTableList () {
      this.imgSignList = this.imgSignList.filter(img => img.dnNo !== undefined)
      this.imgSignList.forEach(row => { row.expand = false })
      this.docSignList = this.docSignList.filter(doc => doc.dnNo !== undefined)
      this.docSignList.forEach(row => { row.expand = false })
    },
    handleSkuFilter (type) {
      this.resetTableList()
      let sku = type === 'img' ? this.skuSearchForm.sku1 : this.skuSearchForm.sku2
      if (!sku) return
      let hasResult = false
      this[`${type}SignList`].forEach(row => {
        console.log(row.dnNo)
        if (row.dnNo === undefined || !Array.isArray(row.dnDetail)) return
        let item = row.dnDetail.filter(detail => detail.productNumber.indexOf(sku) !== -1)
        if (item && item[0] && row.dnNo !== undefined) {
          hasResult = true
          this.expandDnDetail(row, type)
        }
      })
      if (!hasResult) { this.$message.success('没有结果 ！') }
    },
    getIndex (rowIndex, pageNo, pageSize) {
      return rowIndex + 1 + (pageNo - 1) * pageSize
    },
    getList (all) {
      if (all) {
        this.getImgList('Order')
        this.getImgList('Other')
        this.getSignImgList()
        this.getDocList('Order')
        this.getDocList('Other')
        this.getSignDocList()
        this.getDeliveryAttachments()
      } else {
        if (this.activeTab === 'img') {
          this.getImgList('Order')
          this.getImgList('Other')
          this.getSignImgList()
        } else if (this.activeTab === 'doc') {
          this.getDocList('Order')
          this.getDocList('Other')
          this.getSignDocList()
          this.getDeliveryAttachments()
        }
      }
    },
    // 查询图片列表
    getImgList (attachmentType = 'Order') {
      if (!this.voucherNo && !this.orderNo) return
      this.loading[`imgList${attachmentType}Loading`] = true
      let lowerType = String.prototype.toLowerCase.call(attachmentType)
      if (lowerType === 'order') { lowerType = 'general' }
      picList(this.voucherNo || this.orderNo, {
        pageNo: this.pageInfo[`img${attachmentType}`].pageNo,
        pageSize: this.pageInfo[`img${attachmentType}`].pageSize,
        attachmentType: lowerType
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this[`imgList${attachmentType}`] = response.data.edList || []
              this.pageInfo[`img${attachmentType}`].total = response.data.total || 0
            } else {
              this[`imgList${attachmentType}`] = []
              this.pageInfo[`img${attachmentType}`].total = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.hasLoaded = true
        })
        .finally(() => {
          this.loading[`imgList${attachmentType}Loading`] = false
        })
    },
    // 查询文档列表
    getDocList (attachmentType = 'Order') {
      if (!this.voucherNo && !this.orderNo) return
      this.loading[`docList${attachmentType}Loading`] = true
      let lowerType = String.prototype.toLowerCase.call(attachmentType)
      if (lowerType === 'order') { lowerType = 'general' }
      docList(this.voucherNo || this.orderNo, {
        pageNo: this.pageInfo[`doc${attachmentType}`].pageNo,
        pageSize: this.pageInfo[`doc${attachmentType}`].pageSize,
        attachmentType: lowerType
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this[`docList${attachmentType}`] = response.data.edList || []
              this.pageInfo[`doc${attachmentType}`].total = response.data.total || 0
              console.log(`doc${attachmentType}`)
              console.log(this.pageInfo[`doc${attachmentType}`].total)
            } else {
              this[`docList${attachmentType}`] = []
              this.pageInfo[`doc${attachmentType}`].total = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.hasLoaded = true
        })
        .finally(() => {
          this.loading[`docList${attachmentType}Loading`] = false
        })
    },
    getDeliveryAttachments () {
      const orderNo = this.orderNo
      this.deliveryAttachmentList = []
      if (orderNo) {
        pagePOItem({
          soNoList: [orderNo],
          isDeleted: 0
        }).then(response => {
          if (response.msg === 'success' && response.data) {
            const reqList = [];
            (response.data.rows || []).forEach(item => {
              const poId = item.poNo
              if (poId) {
                reqList.push(docList(poId, {
                  attachmentType: 'deliveryNote',
                  pageNo: this.pageInfo.docPageNo,
                  pageSize: this.pageInfo.docPageSize
                }))
              }
            })
            Promise.all(reqList).then(resArr => {
              if (resArr.length > 0) {
                const [edList] = resArr.reduce((accumulator, current) => {
                  try {
                    const toPushList = current.data.edList.filter(item => !accumulator[0].find(acc => acc.id === item.id))
                    accumulator[0].push(...toPushList)
                  } catch (err) {}
                  return [accumulator[0], accumulator[1] + (current.data.total || 0)]
                }, [[], 0])
                this.deliveryAttachmentList = edList
                this.pageInfo.deliveryAttachmentTotal = edList.length
              }
            })
          } else {
            this.$notify.error(response.msg || '系统错误！')
          }
        })
      }
    },
    // 查询签收单
    getSignImgList () {
      if (!this.voucherNo) return
      this.imgSignListLoading = true
      elePicSignAttachment(this.voucherNo, {
        current: this.pageInfo.imgSignPageNo,
        pageSize: this.pageInfo.imgSignPageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.imgSignList = response.data || []
              this.pageInfo.imgSignTotal = response.totalCount || 0
            } else {
              this.imgSignList = []
              this.pageInfo.imgSignTotal = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.imgSignListLoading = false
          this.hasLoaded = true
        })
        .catch(e => {
          this.imgSignListLoading = false
          this.hasLoaded = true
        })
    },
    getSignDocList () {
      if (!this.voucherNo) return
      this.docSignListLoading = true
      eleDocSignAttachment(this.voucherNo, {
        current: this.pageInfo.docSignPageNo,
        pageSize: this.pageInfo.docSignPageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.docSignList = response.data || []
              this.pageInfo.docSignTotal = response.totalCount || 0
            } else {
              this.docSignList = []
              this.pageInfo.docSignTotal = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.docSignListLoading = false
          this.hasLoaded = true
        })
        .catch(e => {
          this.docSignListLoading = false
          this.hasLoaded = true
        })
    },

    // 查询回收站列表
    edDustbinList () {
      if (!this.voucherNo) return
      this.recycleList = []
      this.pageInfo.recycleTotal = 0
      edDustbinList(this.voucherNo, {
        pageNo: this.pageInfo.recyclePageNo - 1,
        pageSize: this.pageInfo.recyclePageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.recycleList = response.data.edInDustbinList || []
              this.pageInfo.recycleTotal = response.data.total || 0
              this.pageInfo.recyclePageNo = response.data.current || 1
            }
          } else {
            this.$notify.error(response.msg)
          }
        })
    },
    previewPicOrPdf (row) {
      if (/\.pdf/i.test(row.url)) {
        return window.open(row.url.replace(/https?/, 'https'))
      }
      this.preview(row)
    },
    // 预览
    preview (row) {
      // 回显备注
      const { id, remark } = row
      this.content = { id, remark }
      this.previewVisible = true
      if (row.url) { this.previewImg = row.url } else {
        const param = { ossKeyName: row.ossKey }
        downloadFile(param)
          .then(response => {
            if (response.code === 200) { this.previewImg = response.data } else {
              this.$notify.error(response.msg)
            }
          })
          .catch(e => { this.$message.error('预览失败') })
      }
    },
    // 删除操作
    del (row) {
      this.$confirm(`确定要删除文件【${row.fileName}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => { this.deleteById(row.id) })
        .catch(() => { this.$message.info('已取消') })
    },
    // 确定删除
    deleteById (id) {
      const param = { vNo: this.voucherNo, id, dimension: 'order' }
      delEcorpDoc(param).then(response => {
        if (response.code === 200) {
          this.$notify.success('操作成功！')
          this.getList()
          this.edDustbinList()
        } else {
          this.$notify.error(response.msg)
        }
      })
    },
    // 下载
    download (row) {
      if (row.url) { downloadjs(row.url.replace(/http(s)?/, 'https'), row.fileName) } else {
        const param = { ossKeyName: row.ossKey }
        downloadFile(param).then(response => {
          if (response.code === 200) {
            this.$notify.success('操作成功！')
            this.downloadLink(response.data)
          } else {
            this.$notify.error(response.msg)
          }
        })
      }
    },
    // 自动下载
    downloadLink (href, filaName) {
      try {
        window.open(href.replace(/http(s)?/, 'https'))
      } catch (err) { console.log(err) }
      // const downloadElement = document.createElement('a')
      // downloadElement.href = href
      // downloadElement.target = '_blank'
      // filaName && (downloadElement.download = filaName)
      // document.body.appendChild(downloadElement)
      // downloadElement.click()
      // document.body.removeChild(downloadElement)
    },
    // 恢复文件操作
    resume (row) {
      this.$confirm(`确定要恢复文件【${row.fileName}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => { this.resumeById(row.id) })
        .catch(() => { this.$message.info('已取消') })
    },
    // 确认恢复
    async resumeById (id) {
      const param = { vNo: this.voucherNo }
      recoverEcorpDoc(id, param).then(response => {
        if (response.code === 200) {
          this.$notify.success('操作成功！')
          this.getList()
          this.edDustbinList()
        } else {
          this.$notify.error(response.msg)
        }
      })
    },
    // 编辑备注
    remarkEdit (row, type) {
      const { remark } = row
      let id
      if (type === 'order') { id = row.id } else {
        id = row.ecorpAttachments[0].id
      }
      this.content = { id, remark }
      this.remarkVisible = true
    },
    // 保存备注操作
    remarkSave () {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.saveData()
        } else {
          this.$notify.error('字段校验未通过！')
        }
      })
    },
    // 保存备注
    async saveData () {
      if (!this.content.remark) {
        this.$notify.error('请输入备注！')
        return
      }
      const param = { remark: this.content.remark }
      upEcorpRemark(this.content.id, param)
        .then(response => {
          if (response.code === 200) {
            this.$notify.success('操作成功！')
            this.getList()
          } else {
            this.$notify.error(response.msg)
          }
          this.remarkVisible = false
          this.previewVisible = false
        })
        .catch(e => {
          this.remarkVisible = false
          this.previewVisible = false
        })
    }
  }
}
</script>
<style lang="scss">
.ele-document-container {
  .document_tab_con .el-tabs__item {
    width: 122px;
    height: 32px;
    line-height: 30px;
    background-color: #fff;
    border-radius: 2px;
    border: solid 1px #fff;
    text-align: center !important;
    color: #666666;
    margin-bottom: 10px;
    position: relative;
  }
  .document_tab_con .el-tabs__item:hover {
    border: solid 1px #dfe4ed;
  }
  .document_tab_con .el-tabs__item.is-active {
    border: solid 1px #dfe4ed;
    color: #1890ff;
    font-weight: bold;
  }
  .document_tab_con .el-tabs__item.is-active::after {
    content: '';
    width: 0;
    height: 0;
    border: 5px dashed transparent;
    border-left: 6px solid #b5c9ed;
    position: absolute;
    right: 3px;
    top: 11px;
  }
  .document_tab_con .el-tabs__active-bar {
    display: none;
  }
  .document_tab_con .el-tabs__nav-wrap::after {
    display: none;
  }
  .recycle_tips {
    color: #f23d3d;
    font-size: 12px;
    line-height: 32px;
  }
  .recycle_tips i {
    font-size: 18px;
    margin-right: 3px;
    position: relative;
    top: 2px;
  }
  .visible-h {
    visibility: hidden !important;
  }
  .pagination-container {
    text-align: right;
  }
  .tab-table-title {
    font-size: 16px;
    margin-bottom: 10px;
  }
}
</style>
