<template>
  <div>
    <el-table v-loading="tableLoading" :data="viewList" :span-method="objectSpanMethod" border fit highlight-current-row stripe style="width: 100%" class="matrix">
      <el-table-column align="center" fixed label="外围订单号" prop="orderNo" width="100px"></el-table-column>
      <el-table-column align="center" fixed label="sku" prop="skuNo" width="180px">
        <template slot-scope="scope">
          <div>
            <el-link type="primary" @click="mdmProductDetail(scope.row.skuNo)">{{ scope.row.skuNo }}</el-link>
            <div class="tag">
              <span v-for="item in scope.row.tags" :key="item">
                {{ item }}
              </span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="物料描述" width="120px">
        <template slot-scope="scope">
          <span class="multiText" :title="getSapMaterialName(scope.row.skuNo)">{{ getSapMaterialName(scope.row.skuNo) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="库位策略" width="120px" prop="positionStrategy"></el-table-column>
      <el-table-column align="center" label="订单数量" prop="demandQty"></el-table-column>
      <el-table-column align="center" label="指定发货方式" prop="deliveryMethod" width="140px"></el-table-column>
      <el-table-column align="center" label="指定工厂库位" prop="specifyFactory" width="200px">
        <template slot-scope="scope">
          {{ scope.row.specifyFactory }}/{{ getFactory(scope.row.specifyFactory) }} {{ getPosition(scope.row.specifyPosition, scope.row.specifyFactory) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="供应类型" prop="position" width="200px">
        <template slot-scope="scope">
          {{ (scope.row.index % 2 ? '是否可采' : '可用现货库存') }}
        </template>
      </el-table-column>
      <el-table-column align="center" :label="col.warehouseName||col.warehouseCode" width="200px" v-for="col in warehouseList" :key="col.warehouseCode">
        <template slot-scope="scope" v-if="scope.row[col.warehouseCode]">
          <!-- 仓库维度是否可采 -->
          <p v-if="scope.row.index % 2" :class="getClass(scope.row[col.warehouseCode], col.warehouseCode)">
            <label>{{ scope.row[col.warehouseCode].purchase ? '是' : '否' }}</label>
            <el-tooltip v-if="scope.row[col.warehouseCode].tcCode" placement="top">
              <div slot="content">
                {{ `${scope.row[col.warehouseCode].tcCode} ${scope.row[col.warehouseCode].tcName} ${scope.row[col.warehouseCode].tcWarehouseProvinceName}/${scope.row[col.warehouseCode].tcWarehouseCityName}` }}
                <br/>
                {{ `${scope.row[col.warehouseCode].tcSupplier} ${scope.row[col.warehouseCode].tcSupplierProvinceName}/${scope.row[col.warehouseCode].tcSupplierCityName}` }}
              </div>
              <span class="tag" style="display: block">
                <span>TC</span>
              </span>
            </el-tooltip>
          </p>
          <!-- 仓库维度可用现货库存 -->
          <p v-else :class="getClass(scope.row[col.warehouseCode])">
            <label class="pointer" @click="(e)=>getPositionInventoryInfo(e, scope.row, col.warehouseCode, 'position')">
              {{ scope.row[col.warehouseCode].inventoryQty }}
            </label>
            <span class="tag" style="display: block">
              <span v-for="item in scope.row[col.warehouseCode].tags" :key="item">
                {{ item }}
              </span>
            </span>
          </p>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      style="margin-top: 10px"
      background
      @current-change="handleCurrentChange"
      :current-page="pageInfo.pageNo"
      :page-size="pageInfo.pageSize"
      layout="prev, pager, next, jumper"
      :total="pageInfo.total">
    </el-pagination>
  </div>
</template>
<script>
export default {
  name: 'matrix',
  props: ['loading', 'tableData', 'warehouseList', 'type', 'orderInfo'],
  data() {
    return {
      viewList: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 20,
        total: 0
      }
    }
  },
  computed: {
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    tableLoading: {
      get () {
        return this.loading
      },
      set (val) {
        this.$emit('update:loading', val)
      }
    }
  },
  methods: {
    handleCurrentChange(pageNo) {
      const pageNum = (pageNo - 1) * this.pageInfo.pageSize
      this.viewList = this.tableData?.slice(pageNum, pageNum + this.pageInfo.pageSize)
      this.pageInfo.pageNo = pageNo
    },
    // 表格合并处理
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 7) {
        if (rowIndex % 2 === 0) {
          return {
            rowspan: 2,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    getFactory(factory) {
      if (this.dictList && this.dictList['factory']) {
        const p = this.dictList['factory'].find(item => item.code === factory)
        if (p && p.name) {
          return `${p.name}`
        }
      }
      return factory
    },
    getSapMaterialName(skuNo) {
      let result = ''
      if (this.orderInfo.items) {
        let p = this.orderInfo.items.find(item => item.materiel === skuNo)
        result = p ? p.sapMaterialName : ''
      }
      return result
    },
    getPosition(position, factory) {
      if (this.dictList && this.dictList['position']) {
        const p = this.dictList['position'].find(item => item.code === position && item.parentCode === factory)
        if (p && p.name) {
          return `${position}/${p.name}`
        }
      }
      return position
    },
    getClass(item, code) {
      if (!item) return
      if (this.type === 'available') {
        return item.available ? 'green' : ''
      } else if (this.type === 'ai') {
        return item.selected ? 'red' : ''
      } else if (this.type === 'max') {
        return item.selectRange ? 'green' : item.mainStockWarehouse ? 'blue' : ''
      }
      return 'green'
    },
    // 获取库位信息
    getPositionInventoryInfo(e, item, warehouseCode, type) {
      this.$emit('getPositionInventoryInfo', e, item, warehouseCode, type)
    },
    mdmProductDetail(skuNo) {
      this.$emit('mdmProductDetail', skuNo)
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    tableData: {
      handler(newList) {
        if (newList?.length) {
          this.viewList = newList?.slice(0, this.pageInfo.pageSize)
          this.pageInfo.total = newList.length
          this.tableLoading = false
        }
      },
      immediate: true
    }
  }
}
</script>
<style lang="scss">
</style>
