<template>
  <div>
    <el-row>
      <el-col :span="24" >
        <div class="brief-count">
          订单已开票总金额：<span>{{totalInvoicedAmount}}</span>
        </div>
      </el-col>
    </el-row>
    <el-row class="list_con">
      <el-col :span="24">
        <el-table
          v-loading="listLoading" :data="listData"
          :span-method="objectSpanMethod"
          border fit highlight-current-row stripe style="width: 100%">
          <el-table-column align="center" label="发货仓" prop="warehouseName" width="120px" >
            <template slot-scope="{row}">
              {{row.warehouseName + ' ' + row.warehouseCode}}
            </template>
          </el-table-column>
          <el-table-column align="center" label="交货单号" width="200px">
            <template slot-scope="{row}">
              <div class="flex-p">
                <p @click="viewOutboundDetail(row)">
                  <span style="color:black;font-weight:bold;margin-right:5px">SAP</span>
                  <span class="nav-to-express">{{ row.sapDnNo }}</span>
                </p>
                <el-button type="text" @click="handleCopy(row.sapDnNo, $event)">复制</el-button>
              </div>
              <div class="flex-p">
                <p @click="viewOutboundDetailOMS(row)">
                  <span style="color:black;font-weight:bold;margin-right:5px">OMS</span>
                  <span class="nav-to-express">{{ row.omsDnNo }}</span>
                </p>
                <el-button type="text" @click="handleCopy(row.omsDnNo, $event)">复制</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="dnGmtCreate" />
          <el-table-column align="center" label="运单号">
            <template slot-scope="scope">
              <span :title="scope.row.transportCodeList">{{ scope.row.transportCodeList | listFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="物流公司" prop="carrierName" />
          <el-table-column align="center" label="当前状态" prop="dnStatusName" >
            <template slot-scope="{ row }">
              <el-button
                v-if="row.logisticStatus && (row.logisticStatus.pri || row.logisticStatus.logisticDetailList)"
                @click="showStatus(row)"
                type="text"
                class="detail_link">
                {{ row.logisticStatus.dnStatusName }} <span v-show="row.logisticStatus.pri">({{ row.logisticStatus.pri }})</span>
              </el-button>
              <p v-else>
                {{ row.logisticStatus.dnStatusName }}
              </p>
            </template>
          </el-table-column>
          <el-table-column align="center" label="出库单详情">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="detail_link"
                @click="viewOutboundDetail(scope.row)">
                {{ scope.row.vendorSelfDelivery === false ?'查看':'供应商直发'}}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="签收返回">
            <template slot-scope="{ row }">
              <el-button v-if="row.uploadFileNumber"
                type="text"
                class="detail_link"
                @click="showSignList(row)"
                >查看（{{ row.uploadFileNumber }}/{{ row.lastPrintPageNumber }}）</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="仓配联系人">
            <template slot-scope="{ row }">
              <div class="flex-p">
              </div>
              <p>{{ row.clerk }} <el-button type="text" @click="handleCopy(row.clerk + ' ' + row.clerkPhone, $event)">复制</el-button></p>
              <p>{{ row.clerkPhone }}</p>
            </template>
          </el-table-column>
          <el-table-column align="center" label="开票金额">
            <template slot-scope="scope">
              <div>
                {{scope.row.invoicedAmount ? scope.row.currencySymbol +' ' + scope.row.invoicedAmount:''}}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="已开票数量" prop="invoicedQuantity" />
          <el-table-column align="center" label="未开数量" prop="unInvoicedQuantity" />
          <el-table-column align="center" label="开票状态">
            <template slot-scope="scope">
              <span v-if="scope.row.invoicedQuantity == 0">未开票</span>
              <el-button
                v-else
                type="text"
                class="detail_link"
                @click="changeTab('in')">
                {{
                  scope.row.unInvoicedQuantity == 0 && scope.row.invoicedQuantity > 0 ? '全部开票' : '部分开票'
                }}
                </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-dialog :visible.sync="signListVisible" width="600px">
      <div style="margin-top:-20px">签收单图片</div>
      <el-table
        v-loading="imgSignListLoading"
        :data="imgSignList"
        border
        fit
        highlight-current-row>
        <el-table-column
          label="文件名"
          width="250"
          align="center"
          prop="fileName"
        />
        <el-table-column
          label="上传时间"
          width="100"
          align="center"
          prop="uploadTime"
        />
        <el-table-column
          label="来源系统"
          width="100"
          align="center"
          prop="source"
        />
        <el-table-column
          label="操作"
          align="center"
          min-width="100px"
          class-name="small-padding fixed-width">
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" @click="preview(row)">预览</el-button>
            <el-button type="text" size="mini" @click="download(row)">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        style="margin:0;padding:10px 0"
        small
        v-show="imgSignTotal > listQuery.imgSignPageSize"
        :total="imgSignTotal"
        :page.sync="listQuery.imgSignPageNo"
        :limit.sync="listQuery.imgSignPageSize"
        layout="total, prev, pager, next, jumper"
        @pagination="getSignImgList"
      />
    </el-dialog>

    <el-dialog
      title="预览"
      :visible.sync="previewVisible"
      width="600px"
      top="10vh">
      <div style="width: 100%; max-height: 400px; overflow: auto; text-align: center; margin-bottom: 10px">
        <el-image
          v-if="previewImg"
          :src="previewImg"
          :preview-src-list="[previewImg]"
        />
        <i v-else class="el-icon-loading" />
      </div>
      <el-alert
        style="margin-bottom: 10px"
        title="点击图片查看原图"
        type="warning"
        show-icon
        close-text="知道了"/>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="show.status" custom-class="dialog-status">
      <!-- <div style="margin-top:-20px">当前状态</div> -->
      <el-table :data="dialog.status" fit>
        <el-table-column label="次序" align="center" prop="fileName" >
          <template slot-scope="{ row }">
            <p :class="{'highlight': row.highlight}">第 {{ row.pri }} 段</p>
          </template>
        </el-table-column>
        <el-table-column label="起始地" align="center" prop="fromOrgName">
          <template slot-scope="{ row }">
            <p :class="{'highlight': row.highlight}">{{ row.fromOrgName }}</p>
          </template>
        </el-table-column>
        <el-table-column label="目的地" align="center" prop="toOrgName">
          <template slot-scope="{ row }">
            <p :class="{'highlight': row.highlight}">{{ row.toOrgName }}</p>
          </template>
        </el-table-column>
        <el-table-column label="承运商" align="center" prop="carrierName">
          <template slot-scope="{ row }">
            <p :class="{'highlight': row.highlight}">{{ row.carrierName }}</p>
          </template>
        </el-table-column>
        <el-table-column label="运单号">
          <template slot-scope="{ row }">
            <p :class="{'highlight': row.highlight}">{{ row.transportCodeList && row.transportCodeList.join(', ') }}</p>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

  </div>
</template>

<script>
import Pagination from '@/components/Pagination'

import { fetchOutBoundList } from '@/api/orderSupplyList'
import { listFilter } from '@/filters/index.js'
import { copyToClipboard } from '@/utils/index'
import { docList, picList, downloadFile } from '@/api/ecorp'

export default {
  name: 'WarehousingLogistics',
  components: {
    Pagination
  },
  filters: {
    listFilter: listFilter
  },
  // 父组件通过props属性传递进来的数据
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      deliveryStatusEnum: '',
      totalInvoicedAmount: '',
      signListVisible: false,
      imgSignListLoading: false,
      docSignListLoading: false,
      listLoading: false,
      orderInfo: {}, // 订单信息，包括订单号、总金额等
      soVoucherId: this.voucherNo,
      listQuery: {
        pageSize: 10,
        current: 1,
        imgSignPageSize: 10,
        imgSignPageNo: 1,
        docSignPageSize: 10,
        docSignPageNo: 1
      },
      currentSignKey: {
        voucherNo: '',
        secondLevelBusinessId: ''
      },
      total: 0,
      imgSignTotal: 0,
      docSignTotal: 0,
      listData: [], // 前置单列表数据
      imgSignList: [], // 签收单图片列表
      docSignList: [], // 签收单文件列表
      spanArr: [], // 二维数组，用于存放单元格合并规则（可能存在多列需要计算行合并，所以是二维数组）
      position: 0, // 用于存储相同项的开始index
      hasLoaded: false, // 当前组件是否已加载过
      previewImg: '',
      previewVisible: false,
      dialog: {
        status: []
      },
      show: {
        status: false
      },
      soNo: this.$route.query.soNo
    }
  },
  watch: {
    activeName: {
      handler (newName, oldName) {
        if (this.voucherNo && newName === 'cp') {
          this.getOutboundList()
        }
      },
      immediate: true
    }
  },
  created () {},
  mounted () {},
  methods: {
    changeTab (tab) {
      this.$emit('change-tab', tab)
    },
    getOutboundList () {
      this.listLoading = true
      fetchOutBoundList(this.listQuery, this.soVoucherId)
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              // 合并前先分组排序
              const list = response.data.dnDetails
              const sortProp = 'warehouseName'
              list.sort((a, b) => {
                const i = a[sortProp]
                const j = b[sortProp]
                return i > j ? 1 : i < j ? -1 : 0
              })
              console.log(list)
              this.listData = list
              this.deliveryStatusEnum = response.data.deliveryStatusEnum
              this.totalInvoicedAmount = response.data.totalInvoicedAmount
              this.getSpanArr(this.listData)
            }
          } else {
            this.$notify.error(response.msg)
          }
        })
        .catch(e => {
          console.error(e)
          this.$message.error('获取仓配进度失败！')
        })
        .finally(() => {
          console.log(this.listData)
          this.listLoading = false
          this.hasLoaded = true
        })
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data) {
      const idx = 0 // 此处定义第0列需要计算行合并
      const prop = 'warehouseName' // 此处定义第0列的属性名，用于数据比较
      this.spanArr[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1)
          this.position = 0
        } else {
          if (data[index][prop] === data[index - 1][prop]) {
            // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr[idx].push(1)
            this.position = index
          }
        }
      })
    },
    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const _row = this.spanArr[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return { rowspan: _row, colspan: _col }
      }
    },
    viewOrderDetail (row) {
      this.$emit('change-tab', 'dd')
    },
    viewOutboundDetailOMS (row) {
      this.$router.push({
        path: '/warehousing/detail/' + row.omsDnNo,
        query: {
          outboundNo: row.omsDnNo,
          omsDeliveryNo: row.omsDnNo,
          soNo: this.soNo,
          deliveryNo: row.vendorSelfDelivery === true ? row.vcDeliveryNo : row.omsDnNo // 供应商直发：VC送货单号；ZKH:DN单号
        }
      })
    },
    // 【查看】跳转到出库单详情
    viewOutboundDetail (row) {
      let key = row.sapDnNo || row.omsDnNo || ''
      this.$router.push({
        path: '/warehousing/detail/' + key,
        query: {
          type: row.vendorSelfDelivery === true ? 1 : 0, //  0 非直发 、1 供应商直发
          dnId: row.dnId,
          outboundNo: row.sapDnNo,
          omsDeliveryNo: row.omsDnNo,
          soNo: this.soNo,
          deliveryNo: row.vendorSelfDelivery === true ? row.vcDeliveryNo : row.omsDnNo
        }
      })
    },
    showSignList (row) {
      console.log(row)
      this.signListVisible = true
      this.imgSignList = []
      this.docSignList = []
      let dnNo = row.omsDnNo
      if (row.vendorSelfDelivery && row.vcDeliveryNo) {
        dnNo = row.vcDeliveryNo
      }
      this.currentSignKey = {
        voucherNo: row.soNo,
        dnNo,
        secondLevelBusinessId: row.sapItemNo
      }
      this.listQuery.imgSignPageNo = 1
      this.listQuery.docSignPageNo = 1
      this.getSignImgList()
      this.getSignDocList()
    },
    showStatus (row) {
      this.show.status = true
      this.dialog.status = (row.logisticStatus && row.logisticStatus.logisticDetailList) || []
    },
    getSignImgList () {
      this.imgSignListLoading = true
      picList(this.currentSignKey.dnNo, {
        attachmentType: 'receipt',
        pageNo: this.listQuery.imgSignPageNo,
        pageSize: this.listQuery.imgSignPageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.imgSignList = response.data.edList || []
              this.imgSignTotal = response.data.total || 0
            } else {
              this.imgSignList = []
              this.imgSignTotal = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.imgSignListLoading = false
          this.hasLoaded = true
        })
        .catch(e => {
          this.imgSignListLoading = false
          this.hasLoaded = true
        })
    },
    getSignDocList () {
      this.docSignListLoading = true
      docList(this.currentSignKey.dnNo, {
        attachmentType: 'receipt',
        pageNo: this.listQuery.docSignPageNo,
        pageSize: this.listQuery.docSignPageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.docSignList = response.data.edList || []
              this.docSignTotal = response.data.total || 0
            } else {
              this.docSignList = []
              this.docSignTotal = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.docSignListLoading = false
          this.hasLoaded = true
        })
        .catch(e => {
          this.docSignListLoading = false
          this.hasLoaded = true
        })
    },
    // 预览
    preview (row) {
      // 回显备注
      const { id, remark } = row
      this.content = { id, remark }

      this.previewVisible = true
      if (row.url) {
        this.previewImg = row.url
      } else {
        const param = {
          ossKeyName: row.ossKey
        }
        downloadFile(param)
          .then(response => {
            if (response.code === 200) {
              this.previewImg = response.data
            } else {
              this.$notify.error(response.msg)
            }
          })
          .catch(e => {
            this.$message.error('预览失败')
          })
      }
    },
    // 下载
    download (row) {
      if (row.url) {
        this.downloadLink(row.url)
      } else {
        const param = {
          ossKeyName: row.ossKey
        }
        downloadFile(param).then(response => {
          if (response.code === 200) {
            this.$notify.success('操作成功！')
            this.downloadLink(response.data)
          } else {
            this.$notify.error(response.msg)
          }
        })
      }
    },
    // 自动下载
    downloadLink (href) {
      const downloadElement = document.createElement('a')
      downloadElement.href = href
      // downloadElement.download = 'xxx.xlsx'; //下载后文件名
      document.body.appendChild(downloadElement)
      downloadElement.click() // 点击下载
      document.body.removeChild(downloadElement) // 下载完成移除元素
    },
    handleCopy (no, event) {
      copyToClipboard.bind(this)(no, event)
    }
  }
}
</script>

<style scoped>
/*  物流节点 */
.nav-to-express{
  cursor: pointer;
  color: steelblue;
}
.flex-p {
  display: flex;
}
.flex-p p {
  line-height: 32px;
}
.dialog-status .highlight{
  color: #f00;
  font-weight: bold;
}
.logistics_con {
  font-size: 12px;
  color: #333333;
  height: 270px;
  background-color: #ffffff;
  border: solid 1px #dfe4ed;
  padding: 0 15px;
}
.logistics_con em {
  font-weight: bold;
  font-style: normal;
}
.logistics_con .main_info {
  margin-top: 12px;
}
.logistics_con .main_info span {
  margin-right: 20px;
}
.logistics_con .dnNo {
  font-size: 12px;
}
.logistics_main {
  margin: 25px 0;
}
.logistics_con .node {
  min-width: 75px;
}
.logistics_con .node * {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #333;
  line-height: 1;
  white-space: nowrap;
}
.logistics_con .node .icon {
  width: 45px;
  height: 45px;
  background: #e2e2e2 url('~@/assets/orderSupplyList/logistics_icons.png')
    no-repeat;
  border-radius: 100%;
  margin: 10px auto;
}
.logistics_con .node.active .icon {
  background-color: #42b983;
  background-position-y: -45px;
}
.logistics_con .node01 .icon {
  background-position: 0 0;
}
.logistics_con .node02 .icon {
  background-position: -45px 0;
}
.logistics_con .node03 .icon {
  background-position: -90px 0;
}
.logistics_con .node04 .icon {
  background-position: -135px 0;
}
.logistics_con .node05 .icon {
  background-position: -180px 0;
}
.logistics_con .node .date {
  color: #666666;
  margin-top: 10px;
}
.logistics_con .arrow {
  min-width: 104px;
  line-height: 96px;
  height: 111px;
}
.detail_info {
  color: #666666;
  line-height: 20px;
}
.detail_info i {
  color: #1890ff;
  font-style: normal;
}
.detail_info em {
  color: #333333;
}
/*  前置单号列表 */
.list_con {
  margin-top: 20px;
}
.brief-count{
  margin-top: 8px;
}
.brief-count span {
  font-weight: bold;
  margin-right: 20px;
}
</style>
