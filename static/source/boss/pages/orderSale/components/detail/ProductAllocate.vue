<template>
  <el-dialog
    class="OrderDetail-ProductAllocate"
    :show-close="false"
    title="匀货"
    :visible.sync="showDlg"
    top="10px"
    width="900px"
    @open="openDialog"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form
      ref="allocateForm"
      :model="allocateData"
      :rules="rules"
      label-width="100px"
    >
      <DividerHeader>当前已选择订单</DividerHeader>
      <div class="indentWrapper">
        <el-row :gutter="20">
          <el-col :span="8">OMS订单号：{{allocateData.omsNo}}</el-col>
          <el-col :span="8">SAP订单号：{{allocateData.sapOrderNo}}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">客户编号：{{allocateData.customerNo}}</el-col>
          <el-col :span="8">客户名称：{{allocateData.customerName}}</el-col>
          <el-col :span="8">客服：{{allocateData.serviceName}}</el-col>
        </el-row>
      </div>

      <div class="tip">已选择商品的计划行：<span class="allocate-strong">（每一行匀货数量不能大于可匀货数量）</span></div>
      <el-table :data="allocateData.detailVOList" border style="width: 100%">
        <el-table-column prop="omsItemNo" label="项目行" align="center" />
        <el-table-column prop="omsItemPlanNo" label="计划行编号" align="center" />
        <el-table-column prop="sku" label="商品编号" align="center" width="90" />
        <el-table-column prop="materialDesc" label="商品名称" align="center" width="240"/>
        <el-table-column prop="quantity" label="订单数量" align="center" />
        <!-- <el-table-column prop="clearedQty" label="已交货数量" align="center" /> -->
        <el-table-column prop="confirmQuantity" label="确认数量" align="center" />
        <!-- <el-table-column prop="clearedQty" label="已清数量" align="center" /> -->
        <!-- <el-table-column prop="confirmQtyType" label="确认类型" align="center">
          <template slot-scope="{row}">
            <span v-if="row.confirmQtyType!=='sap'">
              {{row.confirmQtyType | dict(dictList,'confirmedType')}}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column prop="allocateQty" align="center" label="匀货数量" width="180">
          <template slot="header">
            <RequiredColTitle>匀货数量</RequiredColTitle>
          </template>
          <template slot-scope="{row}">
            <el-input-number
              v-model.number="row.allocateQty"
              size="mini"
              :min="0"
              :max="row.confirmQuantity"
              :precision="3"
              :step="1"
              :disabled="row.clearedQty>0||confirmQuantity===0"
            />
          </template>
        </el-table-column>
        <el-table-column prop="factory" align="center" label="工厂"  width="180">
          <template slot-scope="{row}">
            <span>{{row.factory | dict(dictList,'factory')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="position" align="center" label="库位">
          <template slot-scope="{row}">
            <span>{{row.position | dict(dictList,'position')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryDate" align="center" label="交货日期" width="150" />
      </el-table>
      <div class="allocate-row">
        <span class="order-row-text">请输入匀货至目标订单的订单号</span>
        <el-select v-model="allocateOrderType" class="order-row-select">
          <el-option label="OMS订单号" value="1"/>
          <el-option label="SAP订单号" value="2"/>
        </el-select>
        <el-form-item label="" prop="targetOmsNo" label-width="0">
          <el-input clearable class="allocate-row-input" placeholder="请输入单号" v-model="allocateData.targetOmsNo" />
        </el-form-item>
        <el-button class="order-row-btn" type="primary" :disabled="!allocateData.targetOmsNo" @click="handleTargetAllocate">计算匀货结果</el-button>
      </div>
      <DividerHeader>目标订单的匀货结果</DividerHeader>
      <div class="indentWrapper">
        <el-row :gutter="20">
          <el-col :span="8">OMS订单号：{{allocateTargetData.omsNo}}</el-col>
          <el-col :span="8">客户订单号：{{allocateTargetData.sapOrderNo}}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">客户编号：{{allocateTargetData.customerNo}}</el-col>
          <el-col :span="8">客户名称：{{allocateTargetData.customerName}}</el-col>
          <el-col :span="8">客服：{{allocateTargetData.serviceName}}</el-col>
        </el-row>
      </div>
      <div class="tip">该订单对应商品的计划行列表：<span class="allocate-strong">（每一种商品匀货数量总和不得大于未确认到的数量总和）</span></div>
      <el-table :data="allocateTargetData.detailVOList" border style="width: 100%;margin-bottom:10px">
        <el-table-column prop="omsItemNo" label="项目行" align="center" />
        <el-table-column prop="omsItemPlanNo" label="计划行编号" align="center" />
        <el-table-column prop="sku" label="商品编号" align="center" width="90" />
        <el-table-column prop="materialDesc" label="商品名称" align="center" width="240" />
        <el-table-column prop="quantity" label="订单数量" align="center" />
        <el-table-column prop="confirmQuantity" label="确认数量" align="center" />
        <!-- <el-table-column prop="toBeAllocatedQty" label="待占库数量" align="center" /> -->
        <!-- <el-table-column prop="confirmQtyType" label="确认类型" align="center">
          <template slot-scope="{row}">
            <span v-if="row.confirmQtyType!=='sap'">
              {{row.confirmQtyType | dict(dictList,'confirmedType')}}
            </span>
          </template>
        </el-table-column> -->
        <el-table-column prop="allocateQty" align="center" label="获得匀货数量" width="150" />
        <el-table-column prop="factory" align="center" label="工厂"  width="180">
          <template slot-scope="{row}">
            <span>{{row.factory | dict(dictList,'factory')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="position" align="center" label="库位">
          <template slot-scope="{row}">
            <span>{{row.position | dict(dictList,'position')}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryDate" align="center" label="交货日期" />
      </el-table>
      <el-form-item label="匀货原因" prop="allocateReason">
        <el-select
          v-model="allocateData.allocateReason"
          placeholder="请选择匀货原因"
        >
          <el-option
            v-for="item in dictList['allocateReason']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="匀货备注" prop="allocateNote">
        <el-input
          type="textarea"
          v-model="allocateData.allocateNote"
          :rows="2"
          placeholder="请输入匀货备注"
        />
      </el-form-item>
      <div class="ba-row-center btnGroup">
        <el-button type="primary" @click="handleSave">确认保存</el-button>
        <el-button @click="$emit('update:showDialog', false)">取消</el-button>
      </div>
    </el-form>
  </el-dialog>
</template>
<script>
import DividerHeader from '@/components/DividerHeader'
import RequiredColTitle from '@/pages/orderSale/components/common/RequiredColTitle'
import { getSourceAllocateItem, getTargetAllocateInfo, allocate } from '@/api/orderSaleAllocate'
import { requestWithLoading } from '@/pages/orderSale/utils'

export default {
  components: {
    RequiredColTitle,
    DividerHeader
  },
  props: ['showDialog', 'soInfo', 'itemNoList'],
  data () {
    return {
      allocateData: {
        targetOmsNo: '',
        detailVOList: []
      },
      allocateTargetData: {
        detailVOList: []
      },
      allocateOrderType: '1',
      rules: {
        allocateReason: [
          { required: true, message: '请选择匀货原因', trigger: 'blur' }
        ],
        targetOmsNo: [
          { required: true, message: '请输入目标订单号', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    init () {
      if (this.soInfo && this.soInfo.soNo && this.itemNoList && this.itemNoList.length > 0) {
        const loading = this.$loading({
          background: 'rgba(0, 0, 0, 0.8)',
          lock: true
        })
        const { orderType, items } = this.soInfo
        const sourceItemReqs = []
        this.itemNoList.forEach(item => {
          const foundItem = items.find(good => good.soItemNo === item)
          if (foundItem) {
            sourceItemReqs.push({
              omsItemNo: item,
              directDeliverySupplier: foundItem.directDeliverySupplier
            })
          }
        })
        getSourceAllocateItem({
          orderType,
          sourceItemReqs,
          omsNo: this.soInfo.soNo
        }).then(res => {
          loading.close()
          if (res && res.code === 200) {
            res.data.detailVOList.forEach(item => {
              item.allocateQty = 0
            })
            this.allocateData = res.data
          } else if (res && res.msg) {
            this.$alert(res.msg, '错误', {
              close: () => {
                this.showDlg = false
              }
            })
          }
        }).catch(error => {
          loading.close()
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
        })
      }
    },
    handleTargetAllocate () {
      const params = {
        sourceSapOrderNo: this.allocateData.sapOrderNo,
        sourceOmsNo: this.allocateData.omsNo,
        skuPositionList: this.allocateData.detailVOList
      }
      params[this.allocateOrderType === '1' ? 'targetOmsNo' : 'targetSapOrderNo'] = this.allocateData.targetOmsNo
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      getTargetAllocateInfo(params).then(res => {
        loading.close()
        if (res) {
          const { code, msg } = res
          if (code === 200) {
            this.allocateTargetData = res.data
          } else {
            this.$alert(msg || '查询错误！', '错误')
          }
        }
      }).catch(error => {
        loading.close()
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误')
          }
        }
      })
    },
    openDialog () {
      this.init()
      this.allocateData.targetOmsNo = ''
      this.allocateTargetData = {
        detailVOList: [],
        skuPositionList: []
      }
    },
    handleSave () {
      const { omsNo: sourceOmsNo, allocateReason, allocateNote, sapOrderNo, targetOmsNo: targetNo } = this.allocateData
      const targetSapNo = this.allocateOrderType === '2' ? targetNo : ''
      const targetOmsNo = this.allocateOrderType === '1' ? targetNo : ''
      this.$refs['allocateForm'].validate((valid) => {
        if (valid) {
          const allocateList = []
          if (this.allocateTargetData) {
            const { sourceTargetItemNoMap } = this.allocateTargetData
            this.allocateTargetData.detailVOList.forEach(item => {
              const {
                allocateQty,
                confirmQtyType,
                sourceItemAllocateQtyMap,
                omsItemNo,
                omsItemPlanNo,
                sapItemNo: targetSapItemNo,
                sapItemPlanNo: targetSapPlanNo,
                sku,
                factory,
                position,
                deliveryDate,
                confirmQuantity
              } = item
              allocateList.push({
                targetSapNo,
                targetOmsNo,
                targetSapItemNo,
                targetSapPlanNo,
                sourceSapNo: sapOrderNo,
                allocateQuantity: allocateQty,
                confirmQtyType,
                sourceOmsNo,
                sourceItemAllocateQtyMap,
                targetOmsItemNo: omsItemNo,
                targetOmsPlanNo: omsItemPlanNo,
                sku,
                factory,
                position,
                deliveryDate,
                confirmQuantity
              })
            })
            const { detailVOList } = this.allocateData
            requestWithLoading(this, allocate({
              targetSapNo,
              targetOmsNo,
              sourceSapNo: sapOrderNo,
              sourceOmsNo,
              allocateReason,
              allocateNote,
              allocateList,
              sourceTargetItemNoMap,
              skuPositionList: detailVOList
            }), () => {
              this.$message.success('匀货成功')
              this.$emit('update:showDialog', false)
              this.$emit('success')
            })
          } else {
            this.$message.error({
              message: '对应商品的计划行数据不能为空！'
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.el-row {
  margin-bottom: 20px;
}
.tip {
  padding: 10px;
}
.indentWrapper {
  padding: 10px 10px 0;
}
.inputPad {
  margin: 20px 0;
}
.btnGroup {
  margin-top: 30px;
}
.ba-row-center{
  display: flex;
  align-items: center;
  justify-content: center;
}
.allocate-row {
  display: flex;
  align-items: center;
  margin: 20px 0;
  .order-select {
    .el-select {
      .el-input {
        input {
          border: none;
        }
      }
    }
  }
  .allocate-row-input {
    width: 200px;
    margin: 0 20px;
  }
}
.allocate-strong {
  color: #ff0000;
}
</style>
<style lang="scss">
.tabelCellError {
  input {
    border: 1px solid #ff4949;
  }
  &:hover {
    input {
      border: 1px solid #ff4949;
    }
  }
}
.OrderDetail-ProductAllocate {
  .el-dialog__body {
    padding-top: 0;
  }
}
.allocate-row {
  .order-row-text {
    display: inline-block;
    margin-bottom: 15px;
  }
  .order-row-select {
    margin-bottom: 15px;
    input {
      border: none;
      width: 120px;
    }
  }
  .order-row-btn {
    margin-bottom: 15px;
  }
}
</style>
