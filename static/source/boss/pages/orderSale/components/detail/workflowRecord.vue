<template>
  <div class="workflow-list">
    <vxe-grid
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="workflowGrid"
      row-id="id"
      height="740px"
      id="workflow_grid"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      highlight-hover-row
      highlight-current-row
    >
      <template v-slot:workOrderNo_default="{ row }">
        <el-link @click="gotoDetail(row.workOrderNo)"  type="primary" target="_blank">
          {{ row.workOrderNo }}
        </el-link>
      </template>

      <template v-slot:essence_default="{ row }">
        <span>{{ formatLabel(essencePosition, row.essence) }}</span>
      </template>

      <template v-slot:rank_default="{ row }">
        <el-rate
          v-model="row.rank"
          disabled
          style="display: inline-block; vertical-align: middle"
        ></el-rate>
      </template>
    </vxe-grid>
    <el-pagination
        class="pagination"
        v-show="total > 0"
        background
        :total="total"
        :current-page="page"
        @current-change="handleCurrentChange"
        :page-size="pageSize"
        layout="total, prev, pager, next, jumper"
      />
  </div>
</template>

<script>
import { getAllWorkOrder } from '@/api/aftersales'
import { routeToWorkflow } from '@/utils';
import { get, find } from 'lodash';

const columns = [
  {
    type: 'seq',
    title: '序号',
    width: 100
  },
  {
    field: 'workOrderNo',
    title: '工单编号',
    slots: {
      default: 'workOrderNo_default'
    },
    minWidth: 200
  },
  {
    field: 'essence',
    title: '工单定位',
    slots: {
      default: 'essence_default'
    },
    minWidth: 155
  },
  {
    field: 'categoryName',
    title: '工单类型',
    minWidth: 155
  },
  {
    field: 'rank',
    title: '工单等级',
    slots: {
      default: 'rank_default'
    },
    minWidth: 155
  },
  {
    field: 'statusText',
    title: '工单状态',
    minWidth: 100
  },
  {
    field: 'description',
    title: '问题描述',
    minWidth: 100
  },
  {
    field: 'createUserName',
    title: '创建人',
    minWidth: 100
  },
  {
    field: 'createTime',
    title: '创建时间',
    minWidth: 150
  }
]

export default {
  name: 'workflowRecord',
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    soNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      tableCustom: {
        storage: true
      },
      tableLoading: false,
      columns,
      tableData: [],
      essencePosition: [
        {
          label: '问题工单',
          value: 0
        },
        {
          label: '协同工单',
          value: 1
        }
      ],
      page: 1,
      pageSize: 10,
      total: 0
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    activeName: {
      handler (newName, oldName) {
        if ((this.voucherNo || this.soNo) && newName === 'gd') {
          this.getList()
        }
      },
      immediate: true
    }
  },
  methods: {
    gotoDetail(workOrderNo) {
      routeToWorkflow('/wf/detail/' + workOrderNo)
    },
    formatLabel(options, value) {
      return get(
        find(options, (item) => item.value === value),
        'label'
      );
    },
    async getList() {
      this.tableLoading = true;
      const param = {
        page: this.page,
        pageSize: this.pageSize,
        orderType: 1,
        referOrderNo: this.voucherNo || this.soNo
      };
      try {
        const response = await getAllWorkOrder(param);
        if (response.code === 0) {
          this.total = response.data?.workOrderPageEntity?.count;
          if (response.data.workOrderPageEntity.data) {
            this.tableData = response.data.workOrderPageEntity.data || [];
          } else {
            this.tableData = [];
          }
        } else {
          this.$notify.error(response.msg);
        }
        this.tableLoading = false;
      } catch (e) {
        this.tableLoading = false;
      }
    },
    handleCurrentChange (val) {
      this.page = val;
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.pagination{
  margin-top: 20px;
  float:right;
}
</style>
