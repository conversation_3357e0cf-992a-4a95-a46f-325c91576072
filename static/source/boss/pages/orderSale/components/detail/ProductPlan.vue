<template>
  <el-dialog
    title="计划行详情"
    :visible.sync="showDlg"
    :show-close="false"
    width="900px"
    @closed="$emit('update:showDialog', false)"
  >
    <el-table border :data="list ? list.data : []" width="100%">
      <el-table-column prop="deliveryDate" label="交货日期" align="center" width="200" />
      <el-table-column prop="itemQuantity" label="订单数量" align="center" width="180">
        <template slot-scope="{row}" >
          <span v-if="true">{{row.itemQuantity}}</span>
          <el-input-number
            v-else
            v-model="row.itemQuantity"
            style="width:100%"
            size="mini"
            :class="{tabelCellError:!row.itemQuantity>0}"
            :precision="0"
            :min="0"
            :step="1"
          />
        </template>
      </el-table-column>
      <el-table-column prop="confirmQuantity" label="确认数量" align="center" />
      <el-table-column prop="confirmedQtyType" label="确认类型" align="center" >
        <template slot-scope="{row}" >
          <el-tag v-if="row.confirmedQtyType==='onWay'" style="margin-right: 2px;" type="info">在途</el-tag>
          <el-tag v-if="row.confirmedQtyType==='inStock'" style="margin-right: 2px;" type="success">在库</el-tag>
          <el-tag v-if="row.clearedQty >= row.itemQuantity && row.itemQuantity > 0" type="success">已清</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="salesUnit" label="销售单位" align="center" >
        <template>
          <span>{{ list && list.detail ? getQuantityUnitName(list.detail.quantityUnit) : '' }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="deliveryQuantity" label="已交货数量" align="center" /> -->
      <el-table-column prop="itemPlanType" label="计划行类别" width="100" align="center">
        <template slot-scope="{row}">
          <span>{{ row.itemPlanType }}</span>
        </template>
      </el-table-column>
    </el-table>

    <div class="ba-row-center btnGroup">
      <el-button @click="handleSubmit" v-if="false" type="primary">确定</el-button>
      <el-button @click="$emit('update:showDialog', false)">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { requestWithLoading } from '@/pages/orderSale/utils'
import { isForecastOrder } from '@/pages/orderSale/utils/orderType'
import { updateForecastOrderQty } from '@/api/orderSale'
import { getQuantityUnitName } from '@/utils/order'

export default {
  props: ['showDialog', 'list', 'soInfo'],
  data () {
    return {
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    isForecast () {
      return this.soInfo && this.soInfo.orderType ? isForecastOrder(this.soInfo.orderType) : false
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    getQuantityUnitName (val) {
      return getQuantityUnitName(val, this.dictList)
    },
    handleSubmit () {
      if (this.list && this.list.data && this.list.data.length > 0 && this.list.detail) {
        const { soNo, soItemNo, sapOrderNo, sapItemNo } = this.list.data[0]
        const { clearQty, quantity } = this.list.detail
        const itemPlanList = this.list.data.map(item => ({
          confirmQuantity: item.confirmQuantity,
          deliveryDate: item.deliveryDate,
          itemQuantity: item.itemQuantity,
          omsOrderItemPlanNo: item.soItemPlanNo,
          sapOrderItemPlanNo: item.sapItemPlanNo
        }))
        const totalQuantity = this.list.data.reduce((total, item) => {
          total += item.itemQuantity
          return total
        }, 0)
        if (totalQuantity > quantity || totalQuantity < clearQty) {
          this.$alert('商品数量不得小于已参考数量，且不得大于订单数量！', '错误')
          return
        }
        const { orderSource } = this.list.detail
        const data = {
          itemPlanList,
          orderSource,
          sapItemNo,
          sapOrderNo,
          soItemNo,
          soNo
        }
        requestWithLoading(this, updateForecastOrderQty(data), () => {
          this.$emit('update:showDialog', false)
          this.$emit('updateSuccess', {
            index: this.list.index,
            data
          })
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.btnGroup {
  margin-top: 30px;
  text-align: center;
}
.tabelCellError {
  input {
    border: 1px solid #ff4949;
  }
  &:hover {
    input {
      border: 1px solid #ff4949;
    }
  }
}
</style>
<style lang="scss">
.EditOrder-orderNum {
  input {
    text-align: center;
  }
}
</style>
