<template>
  <div>
    <el-row>
      <el-col :span="24">
        <el-radio-group v-model="type" size="medium">
          <el-radio-button label="1">未采购（{{ number1 }}）</el-radio-button>
          <el-radio-button label="2">已采购（{{ number2 }}）</el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>
    <el-row class="list_con">
      <el-col :span="24">
        <purchase-type01
          ref="purchaseType1"
          v-show="type == 1"
          :voucher-no="soVoucherId"
          :soNo="soNo"
          :type="type"
          @totalChange="totalChange($event,1)"
        />
        <purchase-type02
          ref="purchaseType2"
          v-show="type == 2"
          :voucher-no="soVoucherId"
          :soNo="soNo"
          :type="type"
          @totalChange="totalChange($event,2)"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import PurchaseType01 from './purchaseProgressType/purchaseType01'
import PurchaseType02 from './purchaseProgressType/purchaseType02'

export default {
  name: 'PurchaseProgress',
  components: {
    PurchaseType01,
    PurchaseType02
  },
  filters: {},
  // 父组件通过props属性传递进来的数据
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    soNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      number1: 0,
      number2: 0,
      type: '1', // 1-未采购，2-已采购
      soVoucherId: this.voucherNo,
      hasLoaded: false // 当前组件是否已加载过
    }
  },
  watch: {
    // 如果 `activeName` 发生改变，这个函数就会运行
    activeName: {
      handler (newName, oldName) {
        if (!this.hasLoaded && (this.voucherNo || this.soNo) && newName === 'cg') {
          this.initTab('2')
        }
      },
      immediate: true
    }
  },
  created () {

  },
  methods: {
    initTab (tab) {
      this.type = tab
      setTimeout(() => {
        try {
          this.$refs.purchaseType1.getPurchaseList()
          this.$refs.purchaseType2.getPurchaseList()
        } catch (err) { console.log(err) }
      }, 100)
    },
    totalChange (total, type) {
      if (type === 1) {
        this.number1 = total
      } else {
        this.number2 = total
      }
    }
  }
}
</script>

<style scoped>
/*  物流节点 */
.logistics_con {
  font-size: 12px;
  color: #333333;
  height: 270px;
  background-color: #ffffff;
  border: solid 1px #dfe4ed;
  padding: 0 15px;
}
.logistics_con em {
  font-weight: bold;
  font-style: normal;
}
.logistics_con .main_info {
  margin-top: 12px;
}
.logistics_con .main_info span {
  margin-right: 20px;
}
.logistics_con .dnNo {
  font-size: 12px;
}
.logistics_main {
  margin: 25px 0;
}
.logistics_con .node {
  min-width: 75px;
}
.logistics_con .node * {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #333;
  line-height: 1;
  white-space: nowrap;
}
.logistics_con .node .icon {
  width: 45px;
  height: 45px;
  background: #e2e2e2 url("~@/assets/orderSupplyList/logistics_icons.png")
    no-repeat;
  border-radius: 100%;
  margin: 10px auto;
}
.logistics_con .node.active .icon {
  background-color: #42b983;
  background-position-y: -45px;
}
.logistics_con .node01 .icon {
  background-position: 0 0;
}
.logistics_con .node02 .icon {
  background-position: -45px 0;
}
.logistics_con .node03 .icon {
  background-position: -90px 0;
}
.logistics_con .node04 .icon {
  background-position: -135px 0;
}
.logistics_con .node05 .icon {
  background-position: -180px 0;
}
.logistics_con .node .date {
  color: #666666;
  margin-top: 10px;
}
.logistics_con .arrow {
  min-width: 104px;
  line-height: 96px;
  height: 111px;
}
.detail_info {
  color: #666666;
  line-height: 20px;
}
.detail_info i {
  color: #1890ff;
  font-style: normal;
}
.detail_info em {
  color: #333333;
}
/*  前置单号列表 */
.list_con {
  margin-top: 20px;
}
/* .list_con .el-table table td {
  border-right: 0 none;
} */
</style>
