<template>
  <el-dialog title="客户详情"
    :visible.sync="showDlg"
    :show-close="false"
    @closed="$emit('update:showDialog', false)"
  >
    <el-row :gutter="10">
      <el-col :span="12">售达方：{{ soInfo.customerName }}</el-col>
      <el-col :span="12">送达方：{{ soInfo.customerName }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">收票方：{{ soInfo.invoiceReceiverName || soInfo.customerName }}</el-col>
      <el-col :span="12">付款方：{{ soInfo.invoiceReceiverName || soInfo.customerName }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">客户来源：{{ customer.customerSourceName }}</el-col>
      <el-col :span="12">付款条件：{{ soInfo.paymentTerm | dict(dictList,'paymentTerms') }}</el-col>
    </el-row>
       <el-row v-if="soInfo.totalCreditLimit">
        <el-col :span="12">客户总信用额度：{{ soInfo.totalCreditLimit.toFixed(2) }}</el-col>
    </el-row>

    <el-row :gutter="10">
      <el-col :span="12">客户订单号：{{ soInfo.customerReferenceNo }}</el-col>
      <el-col :span="12">客户参考日期：{{ soInfo.customerReferenceDate }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">自营配送：{{ soInfo.serviceCenterSelfTransport | dict(dictList,'serviceCenterSelfTransport') }}</el-col>
      <el-col :span="12">含税/未税：{{ soInfo.isTax==1?'含税':'未税' }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">销售办事处：{{ customer.salesOffice | dictParent(dictList,'salesOffice') }}</el-col>
      <el-col :span="12">销售组：{{ customer.salesGroup | dictParent(dictList,'salesGroup') }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">销售范围：{{ customer && customer.saleOrgVO ? `${customer.saleOrgVO.salesOrganization}/${customer.saleOrgVO.distributionChannel}/${customer.saleOrgVO.productGroup}` : '' }}&nbsp;
        {{ customer && customer.saleOrgVO ? `${customer.saleOrgVO.salesOrganizationName}，${customer.saleOrgVO.distributionChannelName}，${customer.saleOrgVO.productGroupName}` : '' }}
      </el-col>
      <el-col :span="12">信用固定：{{ mapBoolean(soInfo.creditPromise) }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">接受供应商直发：{{ soInfo.acceptSupplierDelivery == 1 ? '是' : '否' }}</el-col>
      <el-col :span="12">项目部：{{ soInfo.projectDepartment }}</el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">个人支付金额：{{ soInfo.individualPaidAmount }}</el-col>
      <el-col :span="12">企业支付金额：{{ soInfo.enterprisePaidAmount }}</el-col>
    </el-row>
    <div class="ba-center">
      <el-button type="primary" plain @click="$emit('update:showDialog', false)">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: ['showDialog', 'customer', 'soInfo'],
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }

  },
  methods: {
    mapBoolean(value) {
      return value == null ? '' : value ? '是' : '否'
    }
  }
}
</script>

<style scoped lang="scss">
.el-row {
  margin-bottom: 20px;
}
.ba-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
