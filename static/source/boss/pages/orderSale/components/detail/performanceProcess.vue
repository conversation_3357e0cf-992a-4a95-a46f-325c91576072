<template>
  <div class="performanceProcess-info" ref="performanceProcess">
    <header>
      <el-row class="version">
        <el-col :span="7">
          <span class="label">版本号：</span>
          <el-select class="select" placeholder="请选择" v-model="version" @change="handleSearch">
            <el-option
              v-for="(item) in versionList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              ></el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-form ref="searchForm" :model="searchForm" label-width="85px" label-position="left">
        <el-row>
          <el-col :span="7">
            <el-form-item label="skuNo号：" prop="skuNoList">
              <el-input v-model="searchForm.skuNoList" placeholder="skuNo号，可多项，分号隔开" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">查询</el-button>
          </el-col>
          <el-col :span="3" :offset="3" style="padding-left:10px;" v-if="basicInfo.splitSource === 'ifc'">
            <img src="@/assets/images/valid.png" style="width: 66px; height: 60px" />
          </el-col>
        </el-row>
      </el-form>
    </header>
    <div class="orderInfo">
      <el-table ref="tableRef" v-loading="loading" :data="[basicInfo]" border fit highlight-current-row stripe style="width: 50%!important">
        <el-table-column align="center" label="外围订单号" prop="orderNo"></el-table-column>
        <el-table-column align="center" label="客户名称">
          <template>{{ orderInfo.customerNo }} {{ orderInfo.customerName }}</template>
        </el-table-column>
        <el-table-column align="center" label="销售范围" prop="salesOrg">
          <template slot-scope="scope">
            {{ scope.row.salesOrg }}/{{ scope.row.distributionChannel }}/{{ orderInfo.productGroup }} {{
              scope.row.salesOrg | dict(dictList,'salesOrganization')
            }}/{{ scope.row.distributionChannel | dict(dictList,'distributionChannel') }}/{{ orderInfo.productGroup | dict(dictList,'productGroup') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="收货联系人">
          <template>{{ orderInfo.receiverName }}</template>
        </el-table-column>
        <el-table-column align="center" label="收货地址">
          <template>
            {{ orderInfo.receiverAddress }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="是否分批" width="100px">
          <template slot-scope="scope">
            {{ scope.row.autoBatching ? '是' : '否' }}
          </template>
        </el-table-column>
      </el-table>
      <div style="display: inline-block; width: 50%">
        <el-table ref="tableRef2" v-loading="loading" :data="viewList" border fit highlight-current-row stripe>
          <el-table-column align="center" label="行号" prop="itemNo"></el-table-column>
          <el-table-column align="center" label="sku" prop="skuNo" width="100px"></el-table-column>
          <el-table-column align="center" label="是否接受直发" width="100px">
            <template slot-scope="scope">
              {{ scope.row.providerDeliver ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否自营配送" width="100px">
            <template slot-scope="scope">
              {{ scope.row.selfDelivery ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="订单指定类型" width="100px">
            <template slot-scope="scope">
              {{ scope.row.positionAssignBy }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="履约规则id" width="100px">
            <template slot-scope="scope">
              {{ scope.row.regulation }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="物料描述" width="120px">
            <template slot-scope="scope">
              {{ getSapMaterialName(scope.row.skuNo) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="订单数量" prop="qty"></el-table-column>
          <el-table-column align="center" label="发货方式" prop="deliveryMethod" width="140px"></el-table-column>
          <el-table-column align="center" label="工厂" prop="factory" width="200px">
            <template slot-scope="scope">
              {{ getFactory(scope.row.factory) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="库位" prop="position" width="200px">
            <template slot-scope="scope">
              {{ getPosition(scope.row.position, scope.row.factory) }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="请求发货日" prop="requestDeliveryDate" width="200px"></el-table-column>
          <el-table-column align="center" label="请求送达日" prop="requestOnsiteDate" width="200px"></el-table-column>
        </el-table>
        <el-pagination
          style="margin-top: 10px"
          background
          @current-change="handleCurrentChange"
          :current-page="pageInfo.pageNo"
          :page-size="pageInfo.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="pageInfo.total">
        </el-pagination>
      </div>
    </div>
    <ul class="networks">
      <li>仓储供应网络</li>
      <li v-for="item in netWorkList" :key="item.ifcOrderNo">
        <el-button :type="choosedNetwork ===item.ifcOrderNo?'primary':'info'" @click="handleChooseNetwork(item.ifcOrderNo)">{{ item.supplyNetName }}+{{ item.paymentTermName }}
        </el-button>
      </li>
    </ul>
    <el-popover
      placement="right"
      title=""
      width="220"
      trigger="hover"
    >
      <el-tag size="medium" slot="reference" type="danger">* 查看打标说明</el-tag>
      <div slot="default">
        <div v-for="(item, idx) in [...flagInfo, ...basicInfo.tags]" style="margin-bottom: 4px" :key="idx">
          <div v-if="item.tagName" class="tag-info"><span>{{ item.tagName }}</span></div>
          <i v-if="item.icon" :style="`color: ${item.color}`" :class="item.icon"></i>
          <span :style="`color: ${item.color}; font-size: 13px`">{{ item.tagDescription }}</span>
        </div>
      </div>
    </el-popover>
    <section>
      <p><span>最大供应矩阵</span><span>展示下单当时所有的履约可能性</span></p>
      <Matrix :tableData="maxMatrix" :orderInfo="orderInfo" @getPositionInventoryInfo="getPositionInventoryInfo" @mdmProductDetail="mdmProductDetail" :loading.sync="tableLoading"
              :warehouseList="matrixWarehouseList" type="max" />
    </section>
    <section>
      <p><span>可供量矩阵</span><span>过滤一些不太合理的履约路径</span></p>
      <Matrix :tableData="availableMatrix" :orderInfo="orderInfo" @getPositionInventoryInfo="getPositionInventoryInfo" @mdmProductDetail="mdmProductDetail" :loading.sync="tableLoading"
              :warehouseList="matrixWarehouseList" type="available" />
    </section>
    <section>
      <p><span>AI挑仓结果</span><span> 1.指定直发货落仓。 2.现货优先使用。3.发货仓最少。4.由近及远。</span></p>
      <Matrix :tableData="aiSelectMatrix" :orderInfo="orderInfo" @getPositionInventoryInfo="getPositionInventoryInfo" @mdmProductDetail="mdmProductDetail" :loading.sync="tableLoading"
              :warehouseList="matrixWarehouseList" type="ai" />
    </section>
    <section>
      <p><span>最终挑仓结果</span><span> 判断是否集货及集货点选择，确定最终履约路径</span></p>
      <el-table v-loading="finalTableLoading" :data="finalViewList" border fit highlight-current-row stripe style="width: 100%"
                v-if="finaResult.length > 0">
        <el-table-column align="center" label="外围订单号" prop="orderNo" width="100px"></el-table-column>
        <el-table-column align="center" label="sku" prop="skuNo">
          <template slot-scope="scope">
            <div>
              <el-link type="primary" @click="mdmProductDetail(scope.row.skuNo)">{{ scope.row.skuNo }}</el-link>
              <div class="tag">
                <span v-for="item in scope.row.tags" :key="item">
                  {{ item }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="物料描述" width="120px">
          <template slot-scope="scope">
            {{ getSapMaterialName(scope.row.skuNo) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="订单数量" prop="demandQty"></el-table-column>
        <el-table-column align="center" label="发货方式" width="140px">
          <template slot-scope="scope">
            {{ scope.row.directDeliverySupply ? '直发' : '震坤行发货' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="工厂" prop="factory" width="200px">
          <template slot-scope="scope">
            {{ getFactory(scope.row.factory) }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="发货仓" prop="warehouseCode" width="140px">
          <template slot-scope="scope">
            {{ getWarehouseName(scope.row.warehouseCode, !scope.row.directDeliverySupply) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="发货库位" prop="position" width="200px">
          <template slot-scope="scope">
            {{ getPosition(scope.row.position, scope.row.factory) }}
            <el-tooltip v-if="scope.row.tcCode" placement="top">
              <div slot="content">
                {{ `${scope.row.tcCode} ${scope.row.tcName} ${scope.row.tcWarehouseProvinceName}/${scope.row.tcWarehouseCityName}` }}
                <br/>
                {{ `${scope.row.tcSupplier} ${scope.row.tcSupplierProvinceName}/${scope.row.tcSupplierCityName}` }}
              </div>
              <span v-if="scope.row.tcType" class="tag" style="display: block">
                <span>{{ scope.row.tcType === 'CUSTOMER' ? '客户TC' : '供应TC' }}</span>
              </span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column v-if="finalViewList.some(item => item.tcCode)" align="center" label="TC库位" prop="tcPosition" width="140px">
          <template slot-scope="scope">{{ getPosition(scope.row.tcPosition, scope.row.factory) }}</template>
        </el-table-column>
        <el-table-column align="center" label="是否集货" prop="needShipping" width="140px">
          <template slot-scope="scope">{{ scope.row.needShipping ? '是' : '否' }}</template>
        </el-table-column>
        <el-table-column align="center" label="集货仓" prop="shippingWarehouse" width="140px">
          <template slot-scope="scope">
            {{ getWarehouseName(scope.row.shippingWarehouse, !scope.row.directDeliverySupply) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="集货库位" prop="shippingPosition" width="140px">
          <template slot-scope="scope">
            <div>{{ getPosition(scope.row.shippingPosition, scope.row.factory) }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="库存类型" prop="supplyType" width="140px"></el-table-column>
      </el-table>
      <el-pagination
        style="margin-top: 10px"
        background
        @current-change="handleFinalPageNo"
        :current-page="finalPageInfo.pageNo"
        :page-size="finalPageInfo.pageSize"
        layout="prev, pager, next, jumper"
        :total="finalPageInfo.total">
      </el-pagination>
    </section>
    <div class="popover" v-if="showPopover.visible" :style="`top:${showPopover.y}px;left:${showPopover.x}px`">
      <span class="close" @click="showPopover.visible=false"><i class="el-dialog__close el-icon el-icon-close"></i></span>
      <el-table :data="gridData">
        <el-table-column :width="col.width" :property="col.prop" :label="col.name" v-for="col in popoverCol" :key="col.code"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { api } from '@/api/boss'
import Matrix from './matrix.vue'
import {
  orderInfo,
  getWarehouseNetwork,
  globalSupplyMatrixInfo,
  availableSupplyMatrixInfo,
  aiSelectSupplyMatrixInfo,
  positionInventoryInfo,
  selectResultInfo,
  mdmProductList,
  getWarehouseVersions
} from '@/api/ifc'

export default {
  name: 'performanceProcess',
  props: ['orderInfo'],
  components: {
    Matrix
  },
  data() {
    return {
      maxMatrix: [],
      flagValue: '',
      flagInfo: [{
        icon: 'el-icon-s-flag',
        color: '#00cc00',
        tagDescription: '可用备货仓颜色打标'
      }, {
        icon: 'el-icon-s-flag',
        color: '#00CCCC',
        tagDescription: '主备货仓颜色打标'
      }, {
        icon: 'el-icon-s-flag',
        color: '#ffbbaf',
        tagDescription: 'AI挑仓结果颜色打标'
      }],
      deliveryMethodMap: {
        'zkh': '震坤行发货',
        'supplier': '供应商直发',
        'system': '系统判断'
      },
      availableMatrix: [],
      aiSelectMatrix: [],
      gridData: [],
      matrixWarehouseList: [],
      skuList: [],
      viewList: [],
      pageInfo: {
        pageNo: 1,
        pageSize: 20,
        total: 0
      },
      finalPageInfo: {
        pageNo: 1,
        pageSize: 20,
        total: 0
      },
      searchForm: {},
      netWorkList: [],
      choosedNetwork: '',
      loading: false,
      tableLoading: false,
      basicInfo: {
        orderNo: '',
        customerNo: '',
        salesOrg: '',
        receiptedContactId: '',
        provinceName: '',
        cityName: '',
        regionName: '',
        autoBatching: false,
        splitSource: '',
        tags: []
      },
      finaResult: [],
      finalViewList: [],
      finalTableLoading: false,
      showPopover: {
        visible: false,
        x: 0,
        y: 0
      },
      popoverCol: [],
      warehouseList: [],
      finaResultLengthArr: [],
      version: '',
      versionList: []
    }
  },
  computed: {
    finaResultRowArr() {
      let total = 0
      let arr = [0]
      this.finaResultLengthArr.forEach(item => {
        total += item
        arr.push(total)
      })
      return arr
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  created() {
    this.finaResultRowIndex = 0
    this.getWarehouseList()
    this.getVersions()
  },
  methods: {
    async getVersions() {
      let res = await getWarehouseVersions({ orderNo: this.orderInfo.originOrderNo || this.orderInfo.orderNo });
      if (res.status === 200) {
        this.version = res.result[0].version;
        this.versionList = res.result.map((a) => {
          return {
            value: a.version,
            label: `V${a.version} ( 生成时间: ${a.gmtCreate.replace('T', '')} )`
          }
        });
      } else {
        this.$message.error(res.msg || '网络异常，请稍后重试')
      }
    },
    handleCurrentChange(pageNo) {
      const pageNum = (pageNo - 1) * this.pageInfo.pageSize
      this.viewList = this.skuList?.slice(pageNum, pageNum + this.pageInfo.pageSize)
      this.pageInfo.pageNo = pageNo
    },
    handleFinalPageNo(pageNo) {
      const pageNum = (pageNo - 1) * this.finalPageInfo.pageSize
      this.finalViewList = this.finaResult?.slice(pageNum, pageNum + this.finalPageInfo.pageSize)
      this.finalPageInfo.pageNo = pageNo
    },
    getWarehouseList() {
      this.finalTableLoading = true
      this.tableLoading = true
      api({
        prefix: '/api-kunhe-mdm',
        url: '/warehouse/list'
      }).then(res => {
        if (res.code === '0000' && res.result) {
          this.warehouseList = res.result
          this.handleSearch()
        }
      })
    },
    // 打开mdm商品详情
    mdmProductDetail(skuNo) {
      let host = 'https://pc-uat.zkh360.com'
      if (window.CUR_DATA.env === 'pro') {
        host = 'https://pc.zkh360.com'
      }
      mdmProductList({ skuCode: skuNo }).then(res => {
        const factory = this.skuList.find(item => item.skuNo === skuNo)?.factory
        if (res.data.skuCode && res.data.itemCode && factory && res.data.name) {
          let url = `${host}/product/skuDetail/${res.data.skuCode}&${res.data.itemCode}&${factory}?tagName=${res.data.name}`
          window.open(url)
        } else {
          this.$message.error('网络异常，请稍后重试')
        }
      })
    },
    // 选择网络
    handleChooseNetwork(ifcOrderNo) {
      this.choosedNetwork = ifcOrderNo
      this.searchMatrix()
    },
    // 查询
    handleSearch() {
      let skuNoList = this.searchForm.skuNoList ? this.searchForm.skuNoList.split(';') : null
      let params = {
        orderNo: this.orderInfo.originOrderNo || this.orderInfo.orderNo,
        skuNoList,
        version: this.version
      }
      orderInfo(params).then(res => {
        let data = res.result || {}
        this.basicInfo = data
        this.skuList = data.items || []
        this.viewList = data.items?.slice(0, this.pageInfo.pageSize)
        this.pageInfo.total = this.skuList?.length
      })
      this.getNetWork()
    },
    // 获取仓储网络
    getNetWork() {
      let params = {
        orderNo: this.orderInfo.originOrderNo || this.orderInfo.orderNo,
        version: this.version
      }
      getWarehouseNetwork(params).then(res => {
        this.netWorkList = res.result
        this.choosedNetwork = res.result.length ? res.result[0].ifcOrderNo : this.choosedNetwork
        this.searchMatrix()
      })
    },
    getFactory(factory) {
      if (this.dictList && this.dictList['factory']) {
        const p = this.dictList['factory'].find(item => item.code === factory)
        if (p && p.name) {
          return `${factory}${p.name}`
        }
      }
      return factory
    },
    /**
     * @description 获取仓库名称
     * @param {*} code 仓库编码
     * @param {*} isZKHSend 是否是zkh发货
     */
    getWarehouseName(code, isZKHSend) {
      let result = ''
      if (this.warehouseList.length && isZKHSend) {
        let p = this.warehouseList.find(item => item.warehouseId === code)
        result = p ? p.warehouseDescr : ''
      }
      return result
    },
    getSapMaterialName(skuNo) {
      let result = ''
      if (this.orderInfo.items) {
        let p = this.orderInfo.items.find(item => item.materiel === skuNo)
        result = p ? p.sapMaterialName : ''
      }
      return result
    },
    getPosition(position, factory) {
      if (this.dictList && this.dictList['position']) {
        const p = this.dictList['position'].find(item => item.code === position && item.parentCode === factory)
        if (p && p.name) {
          return `${position}${p.name}`
        } else {
          return position
        }
      }
      return position
    },
    // 表格合并处理
    resultObjectSpanMethod({ row, column, rowIndex, columnIndex }) {
      let result = {
        rowspan: 0,
        colspan: 0
      }

      if (columnIndex < 4) {
        if (rowIndex === this.finaResultRowArr[this.finaResultRowIndex]) {
          result = {
            rowspan: this.finaResultLengthArr[this.finaResultRowIndex],
            colspan: 1
          }
          if (columnIndex === 3) {
            this.finaResultRowIndex = this.finaResultRowIndex + 1
          }
        }
        return result
      } else if (rowIndex === this.finaResult.length - 1 && columnIndex === Object.keys(row).length) {
        // 条件为最后一行，最后一列
        // 表格处理完一轮之后，需要将finaResultRowIndex放在首位，不然下次接着处理，数据会不对
        this.finaResultRowIndex = 0
      }
    },
    // 查询矩阵
    searchMatrix() {
      let skuNoList = this.searchForm.skuNoList ? this.searchForm.skuNoList.split(';') : null
      let params = {
        orderNo: this.orderInfo.originOrderNo || this.orderInfo.orderNo,
        ifcOrderNo: this.choosedNetwork,
        skuNoList,
        version: this.version
      }

      // 最大供应矩阵
      globalSupplyMatrixInfo(params).then(res => {
        this.dealWithMatrixData(res, 'maxMatrix')
      })
      // 可用矩阵
      availableSupplyMatrixInfo(params).then(res => {
        this.dealWithMatrixData(res, 'availableMatrix')
      })
      // ai挑仓矩阵
      aiSelectSupplyMatrixInfo(params).then(res => {
        this.dealWithMatrixData(res, 'aiSelectMatrix')
      })
      // 最终结果
      selectResultInfo(params).then(res => {
        let data = res.result || []
        let finaResult = []
        let finaResultLengthArr = []
        data.forEach(item => {
          let { warehouses, ...obj } = item
          let length = warehouses.length
          finaResultLengthArr.push(length)
          warehouses.forEach(ware => {
            let rowObj = {
              ...obj,
              ...ware
            }
            finaResult.push(rowObj)
          })
        })
        this.finaResultLengthArr = finaResultLengthArr
        this.finaResult = finaResult
        this.finalPageInfo.total = finaResult.length
        this.finalViewList = this.finaResult.slice(0, this.finalPageInfo.pageSize)
        this.finalTableLoading = false
      })
    },
    // 处理矩阵数据
    dealWithMatrixData(res, name) {
      let data = res.result || []
      let matrix = []
      data.forEach((item, index) => {
        let { warehouseVos } = item
        let currentIndex = index * 2
        item.index = currentIndex
        warehouseVos.forEach(ware => {
          item[ware.warehouseCode] = { ...ware }
        })
        matrix.push(item, { ...item, index: currentIndex + 1 })
        warehouseVos.forEach(p => {
          p.warehouseName = this.getWarehouseName(p.warehouseCode, p.warehouseType)
        })
        this.matrixWarehouseList = [...warehouseVos]
      })
      this[name] = matrix
    },
    // 显示popover框
    showPopoverHandle(e) {
      this.showPopover.visible = true
      let top = document.querySelector('.el-scrollbar__wrap')
      this.showPopover.x = e.pageX - 410
      this.showPopover.y = e.pageY + top.scrollTop - 130
    },
    // 获取库位信息
    getPositionInventoryInfo(e, item, warehouseCode, type) {
      let showSupplier = type === 'supplier'
      if (showSupplier && !item[warehouseCode].purchase) return
      if (!item[warehouseCode].inventoryQty) return
      let params = {
        orderNo: this.orderInfo.originOrderNo || this.orderInfo.orderNo,
        ifcOrderNo: this.choosedNetwork,
        skuNo: item.skuNo,
        warehouseCode: warehouseCode,
        version: this.version
      }

      if (showSupplier) {
        this.popoverCol = [
          { name: '工厂', prop: 'factoryText', width: 150 },
          { name: '供应商', prop: 'supplier', width: 150 }
        ]
      } else {
        this.popoverCol = [
          { name: '工厂', prop: 'factoryText', width: 140 },
          { name: '库位', prop: 'positionText', width: 90 },
          { name: '库存类型', prop: 'positionType', width: 70 },
          { name: '可用量', prop: 'availableQty', width: 70 }
        ]
      }
      this.showPopoverHandle(e)
      positionInventoryInfo(params).then(res => {
        let data = res.result || []
        data.forEach(item => {
          item.factoryText = this.getFactory(item.factory)
          item.positionText = this.getPosition(item.position, item.factory)
          item.positionType = item.escrow ? '代管代发' : '自有'
        })
        this.gridData = data
      })
    }
  }
}
</script>
<style lang="scss">
  .performanceProcess-info {
    position: relative;

    .version {
      margin-bottom: 10px;
      .label {
        width: 85px;
        display: inline-block;
      }
      .select {
        width: calc(100% - 85px);
      }
    }

    .orderInfo {
      .el-table {
        display: inline-block;
        vertical-align: top;
      }
    }

    .networks {
      margin: 20px 0;

      li {
        display: inline-block;
        margin-right: 20px;
      }
    }

    section {
      margin-bottom: 20px;
      padding: 10px;
      border: 1px solid #ccc;
      background-color: #bdbdbd;

      > p {
        margin-bottom: 10px;

        span:nth-child(1) {
          font-size: 18px;
          margin-right: 20px;
        }
      }

      .tag {
        position: absolute;
        top: 0;
        right: 5px;

        span {
          background-color: #f56c6c;
          border-radius: 11px;
          color: #fff;
          display: inline-block;
          font-size: 12px;
          height: 22px;
          line-height: 22px;
          padding: 0 4px;
          text-align: center;
          white-space: nowrap;
          border: 1px solid #fff;
        }
      }

      .matrix {
        td {
          padding: 0;

          .cell {
            padding: 0;
          }

          p {
            color: #4266dd;
            height: 40px;
            line-height: 40px;
            position: relative;

            &.green {
              background-color: #00cc00;
            }

            &.blue {
              background-color: #00CCCC;
            }

            &.red {
              background-color: #ffbbaf;
            }

            .pointer {
              cursor: pointer;
            }
          }
        }
      }
    }

    .popover {
      position: absolute;
      width: 370px;
      background-color: #fff;
      padding-top: 20px;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(20, 20, 20, 0.3);
      z-index: 8;

      .close {
        cursor: pointer;
        position: absolute;
        color: red;
        z-index: 10;
        top: 2px;
        right: 5px;
        color: #666;
      }
    }
  }

  .tag-info {
    display: inline;

    span {
      background-color: #f56c6c;
      border-radius: 11px;
      color: #fff;
      display: inline-block;
      font-size: 12px;
      height: 22px;
      line-height: 22px;
      padding: 0 4px;
      text-align: center;
      white-space: nowrap;
      border: 1px solid #fff;
      margin-right: 6px;
    }
  }
</style>
