<template>
  <el-dialog
    :show-close="false"
    title="查看更多信息"
    :visible.sync="showDlg"
    top="10px"
    width="900px"
    @closed="$emit('update:showDialog', false)"
  >
    <el-tabs v-model="activeName">
      <!-- tab1 -->
      <el-tab-pane label="交货信息" name="delivery">
        <DividerHeader>订单信息</DividerHeader>
        <div class="deliveryInfo">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-checkbox :checked="soInfo.backupOrder=='X'" disabled>后补订单</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox :checked="soInfo.autoBatching=='X'" disabled>允许分批</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox :checked="soInfo.paid=='X'" disabled>已付款</el-checkbox>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-checkbox :checked="soInfo.clearSlackStock=='X'" disabled>清呆滞库存</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox :checked="soInfo.virtualReturn=='X'" disabled>不向客户展示</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox :checked="soInfo.autoDelivery=='X'" disabled>自动发货</el-checkbox>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- <el-col :span="12">未发货条件：{{ soInfo.bidCustomer | dict(dictList,'noDeliveryReason') }}</el-col> -->
            <el-col :span="12">付款流水号: {{ soInfo.tradeNo  }}</el-col>
            <el-col :span="12">预收款金额: {{ soInfo.collectionAmount  }}</el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">项目号：{{ soInfo.projectNo }}</el-col>
            <el-col :span="12">供应商代码：{{ soInfo.supplierAccount }}</el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">付款备注：{{ soInfo.paymentNote }}</el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">合同备注：{{ soInfo.agreementNote }}</el-col>
          </el-row>
        </div>
        <DividerHeader>随货文件要求</DividerHeader>
        <div class="deliveryInfo">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-checkbox :checked="soInfo.attachOrder=='X'" disabled>附订单</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox :checked="soInfo.attachCoa=='X'" disabled>附COA</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox :checked="soInfo.attachMsds=='X'" disabled>附MSDS</el-checkbox>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-checkbox :checked="soInfo.attachTds=='X'" disabled>附TDS</el-checkbox>
            </el-col>
            <el-col :span="6">
              <el-checkbox :checked="soInfo.specifiedDocument=='X'" disabled>其他随货资料</el-checkbox>
            </el-col>
            <!-- <el-col :span="6">
              <el-checkbox :checked="soInfo.referenceStandardShippingReq=='X'" disabled>参考标准客户出货要求文件</el-checkbox>
            </el-col> -->
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">送货单模板：{{ soInfo.deliverySlipTemplate | dict(dictList,'deliverySlipTemplate') }}</el-col>
            <el-col :span="12">打印份数：{{ soInfo.printNum }}</el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.dnPaperReq" field="dnPaperReq" />
            </el-col>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.dnIncidentalWay" field="dnIncidentalWay" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.dnSignatureReq" field="dnSignatureReq" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">标签模板：{{ soInfo.labelTemplate | dict(dictList,'labelTemplate') }}</el-col>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.otherLabelReq" field="otherLabelReq" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.fastenerLabelReq" field="fastenerLabelReq" />
            </el-col>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.labelPasteWay" field="labelPasteWay" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">合格证标识：{{ soInfo.certificateIdentification | dict(dictList,'certificateIdentification') }}</el-col>
            <el-col :span="12">签单返回：{{ soInfo.signingBack | dict(dictList,'signingBack') }}</el-col>
          </el-row>
        </div>
        <DividerHeader>仓储要求</DividerHeader>
        <div class="deliveryInfo">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-checkbox :checked="soInfo.hideLogo==='X'" disabled>隐藏logo</el-checkbox>
            </el-col>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.fastenerDetect" field="fastenerDetect" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">包装要求：{{ showDataLabel(dictList, 'packagingReq', soInfo.packagingReq) }}</el-col>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.fastenerSpecialPackageReq" field="fastenerSpecialPackageReq" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">送货资料需要仓配信息：{{ showDataLabel(dictList, 'deliveryWarehouseInfo', soInfo.deliveryWarehouseInfo) }}</el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">交货其他备注：{{ soInfo.deliveryOtherNote }}</el-col>
          </el-row>
        </div>
        <DividerHeader>运输要求</DividerHeader>
        <div class="deliveryInfo">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-checkbox :checked="soInfo.exportProcessingZone=='X'" disabled>保税区/出口加工区</el-checkbox>
            </el-col>
            <!-- <el-col :span="6">
              <el-checkbox :checked="soInfo.receiptTimeCategory=='X'" disabled>工作日与周末均可收货</el-checkbox>
            </el-col> -->
            <el-col :span="12">预约送货方式：{{ soInfo.scheduleDelivery | dict(dictList,'scheduleDelivery') }}</el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.specifiedReceiptDayOfWeek" field="specifiedReceiptDayOfWeek" />
            </el-col> -->
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.specifiedReceiptTime" field="specifiedReceiptTime" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">装运条件：{{ soInfo.shippingCondition | dict(dictList,'shippingConditions') }}</el-col>
            <el-col :span="12">送卸货要求：{{ soInfo.deliveryUnloadingReq | dict(dictList,'deliveryUnloadingReq') }}</el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.disableShipping" field="disableShipping" />
            </el-col>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.designatedShipping" field="designatedShipping" />
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.vehicleReq" field="vehicleReq" />
            </el-col>
            <el-col :span="12">配送员要求：{{ showDataLabel(dictList, 'deliveryRequirement', soInfo.deliveryRequirements) }}</el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.signingReq" field="signingReq" />
            </el-col>
            <el-col :span="12">
              <SelectOrderService type="detail" v-model="soInfo.forkliftRelated" field="forkliftRelated" />
            </el-col>
          </el-row>
        </div>

        <div class="ba-row-center">
          <el-button type="primary" plain @click="$emit('update:showDialog', false)">关闭</el-button>
        </div>
      </el-tab-pane>

      <!-- tab2 -->
      <el-tab-pane label="发票信息" name="invoice">
        <div style="margin-top:10px" />
        <el-row :gutter="20">
          <el-col :span="24">客户税号：{{ customerInfo.corporationTaxNum }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">开票地址和电话：{{ soInfo.billingAddressAndPhone }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">开户行：{{ customerInfo.bankName }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">账号：{{ customerInfo.bankNumber }}</el-col>
          <el-col :span="12">客户付款账号：{{ soInfo.customerPayAccountTypeName }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">收票联系人：{{ soInfo.receivingInvoiceName }}</el-col>
          <el-col :span="12">收票人电话：{{ soInfo.receivingInvoicePhone }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">收票地址：{{ soInfo.receivingInvoiceAddress }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">寄票备注：{{ soInfo.shippingInfo }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-checkbox :checked="soInfo.invoicingByMail=='X'" disabled>凭邮件开票</el-checkbox>
          </el-col>
          <el-col :span="6">
            <el-checkbox :checked="soInfo.returnOffset=='X'" disabled>退换货抵消</el-checkbox>
          </el-col>
          <el-col :span="6">
            <el-checkbox :checked="soInfo.mergeBilling=='X'" disabled>合并开票</el-checkbox>
          </el-col>
          <el-col :span="6">
            <el-checkbox :checked="soInfo.autoBilling=='X'" disabled>自动开票</el-checkbox>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-checkbox :checked="soInfo.billingRobot=='X'" disabled>开票机器人</el-checkbox>
          </el-col>
          <el-col :span="6">
            <el-checkbox :checked="soInfo.showDiscount=='X'" disabled>显示折扣</el-checkbox>
          </el-col>
          <el-col :span="6">
            <el-checkbox :checked="soInfo.ifDocMailed === 'X'" disabled>附资料邮寄</el-checkbox>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">发票类型：{{ soInfo.invoiceType | dict(dictList,'invoiceType') }}</el-col>
          <el-col :span="12">合并开票要求：{{ soInfo.mergeBillingDemand | dict(dictList,'mergeBillingDemand') }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">快递公司：{{ soInfo.expressCompany | dict(dictList,'expressCompany') }}</el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">发票备注：{{ soInfo.financialNote }}</el-col>
        </el-row>

        <div class="ba-row-center">
          <!-- 更换抬头 后续版本添加此功能 -->
          <!--          <el-button type="primary">更换抬头</el-button>-->
          <el-button type="primary" plain @click="$emit('update:showDialog', false)">关闭</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
import DividerHeader from '../common/DividerHeader'
import SelectOrderService from '@/pages/orderSale/components/common/SelectOrderService'

export default {
  components: {
    DividerHeader,
    SelectOrderService
  },
  props: ['showDialog', 'soInfo', 'customerInfo'],
  data () {
    return {
      activeName: 'delivery',
      trans: {
        // referenceStandardShippingReq: this.soInfo.referenceStandardShippingReq === 'X', // 参考标准客户出货要求文件
        receiptTimeCategory: this.soInfo.receiptTimeCategory === 'X', // 工作日与周末均可收货
        backupOrder: this.soInfo.backupOrder === 'X', // 后补订单
        autoDelivery: this.soInfo.autoDelivery === 'X', // 自动发货
        autoBatching: this.soInfo.autoBatching === 'X', // 允许分批
        paid: this.soInfo.paid === 'X', // 已付款
        attachOrder: this.soInfo.attachOrder === 'X', // 附订单
        specifiedDocument: this.soInfo.specifiedDocument === 'X', // 其他随货资料
        exportProcessingZone: this.soInfo.exportProcessingZone === 'X', // 保税区/出口加工区
        virtualReturn: this.soInfo.virtualReturn === 'X', // 不向客户展示
        bidCustomer: this.soInfo.bidCustomer === 'X', // 叫料客户
        attachCoa: this.soInfo.attachCoa === 'X', // 附COA
        attachMsds: this.soInfo.attachMsds === 'X', // 附MSDS
        attachTds: this.soInfo.attachTds === 'X', // 附TDS
        hideLogo: this.soInfo.hideLogo === 'X' // 隐藏logo
      },
      invoice: {
        invoicingByMail: !!this.soInfo.invoicingByMail
      }
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    showDataLabel(data, category, code) {
      if (!code || !category || !data || !data[category]) {
        return code
      }
      if (data[category] && data[category].length > 0) {
        const codeList = code.split(',');
        return codeList.map(code => (data[category].find(f => f.code === code) || { name: code }).name).join(',');
      }
      return code
    }
  }
}
</script>
<style scoped lang="scss">
.deliveryInfo {
  padding: 10px;
}
.el-row {
  margin-bottom: 20px;
}

.el-dialog {
  &.body {padding: 0 20px 20px;}
}
.el-dialog__body {
  padding: 0 20px 20px !important;
}
.ba-row-center{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
