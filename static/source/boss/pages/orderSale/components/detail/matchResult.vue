<template>
  <el-table :data="list" border fit v-loading="loading">
    <el-table-column label="订货编码">
      <template slot-scope="scope">
        <span>{{ scope.row.skuNo }}</span>
      </template>
    </el-table-column>
    <el-table-column label="采购单号">
      <template slot-scope="scope">
        <span class="nav-to-express" @click="navToExpress(scope.row)">{{ scope.row.poVoucherId }}</span>
      </template>
    </el-table-column>
    <el-table-column label="采购行号" width="80">
      <template slot-scope="scope">
        <span>{{ scope.row.poPlanId }}</span>
      </template>
    </el-table-column>
    <el-table-column label="采购数量" width="80">
      <template slot-scope="scope">
        <span>{{ scope.row.poQuantity }}</span>
      </template>
    </el-table-column>
    <!--     <el-table-column label="匹配上的数量" width="80">
      <template slot-scope="scope">
        <span>{{ scope.row.matchedQty }}</span>
      </template>
    </el-table-column> -->
    <el-table-column label="分配SO数量">
      <template slot-scope="scope">
        <span>{{ scope.row.matchedQty }}</span>
      </template>
    </el-table-column>
    <el-table-column label="预计到货时间">
      <template slot-scope="scope">
        <span>{{ scope.row.expectedArrivalDate }}</span>
      </template>
    </el-table-column>
    <el-table-column label="采购单状态">
      <template slot-scope="scope">
        <!-- <span>{{ scope.row.customerDemandDate }}</span> -->
        <el-tag :type="scope.row | PoStatusFilter">
          {{ scope.row | PoStatusTextFilter }}
        </el-tag>
      </template>
    </el-table-column>
    <el-table-column label="采购员" width="120">
      <template slot-scope="scope">
        <span>{{ scope.row.buyer }}</span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'MatchResult',
  filters: {
    PoStatusFilter (row) {
      const date1 = row.expectedArrivalDate
      const date = new Date()
      const year = date.getFullYear() // 年
      let month = date.getMonth() + 1 // 月
      month = month + ''
      if ((month + '').length === 1) {
        month = '0' + month
      }
      let day = date.getDate() // 日
      if ((day + '').length === 1) {
        day = '0' + day
      }
      const date2 = year + '-' + month + '-' + day
      let type = ''
      if (date1 >= date2) {
        type = 'info'
      } else {
        type = 'danger'
      }
      return type
    },
    PoStatusTextFilter (row) {
      const date1 = row.expectedArrivalDate
      const date = new Date()
      const year = date.getFullYear() // 年
      let month = date.getMonth() + 1 // 月
      month = month + ''
      if ((month + '').length === 1) {
        month = '0' + month
      }
      let day = date.getDate() // 日
      if ((day + '').length === 1) {
        day = '0' + day
      }
      const date2 = year + '-' + month + '-' + day
      let text = ''
      if (date1 >= date2) {
        text = '正常交期'
      } else {
        text = '已逾期'
      }
      return text
    }
  },
  // 父组件通过props属性传递进来的数据
  props: {
    changeTab: {
      type: Function,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => {
        return null
      }
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {}
  },
  created () {},
  methods: {
    navToExpress (row) {
      console.log(row)
      // 跳转到采购进度 -> 已采购
      this.$emit('change-tab', 'cg')
    }
  }
}
</script>

<style scoped>
.nav-to-express{
  cursor: pointer;
  color: steelblue;
}
</style>
