<template>
    <div>
       <el-table
        :data="tableData.list"
        v-loading="tableData.loading"
        border
        highlight-current-row
        style="width: 100%">
          <el-table-column v-for="item in tableData.colums" :key="item.field+activeName" :prop="item.field" :label="item.label"   :width="item.width" show-overflow-tooltip  align="center">
              <template  slot-scope="{row}" >
                  <!-- 工单号 -->
                    <template v-if="item.field==='workNoList'">
                      <template v-if="row[item.field]&&row[item.field].length>0">
                        <template v-for="(it,index) in row[item.field]" >
                           <el-link  type="primary"  @click="toWokNoDeatil(it)" :key="index">
                            {{it}}
                          </el-link>
                          <br :key="it">
                        </template>

                      </template>
                      <template v-else>
                         <el-link type="primary"  @click="createWorkNo(row)">
                        创建工单
                      </el-link>
                      </template>
                    </template>
                  <!-- OMS订单号 点击跳转订单详情-->
                  <el-link v-else-if="item.field==='soNo'" type="primary"  @click="toDtl(row)"> {{  row[item.field] }}</el-link>
                  <span v-else>{{row[item.field]}}</span>
              </template>
          </el-table-column>
      </el-table>
       <el-pagination
       style="float:right;margin-top:10px"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="table.pageNo"
      :page-size="table.pageSize"
      :page-sizes="[10, 20, 30, 50]"
      layout="total, sizes, prev, pager, next, jumper"
      :total="tableData.total||0">
    </el-pagination>
    </div>
</template>
<script>
import { routeToWorkflow } from '@/utils';

export default {
  data() {
    return {
      table: {
        pageNo: 1,
        pageSize: 20
      }

    };
  },
  props: {
    tableData: {
      type: Object,
      default: () => {}
    },
    activeName: {
      type: String,
      default: 'unOrderPurchaseOrder'
    }
  },
  methods: {
    handleSizeChange(pageSize) {
      this.table.pageSize = pageSize
      this.table.activeName = this.activeName
      this.$emit('paginationChange', this.table)
    },
    handleCurrentChange(pageNo) {
      this.table.pageNo = pageNo
      this.table.activeName = this.activeName
      this.$emit('paginationChange', this.table)
    },
    toDtl (row) {
      const query = {
        soNo: row.soNo,
        id: row.id,
        refresh: true,
        sapOrderNo: row.sapOrderNo
      }
      this.$router.jumpToSoOrderDetail({
        query
      })
    },
    toWokNoDeatil(workOrderNo) {
      routeToWorkflow('/wf/detail/' + workOrderNo)
    },
    //  1代表协同工单
    // 0代表问题工单
    createWorkNo(row) {
      routeToWorkflow('/wf/create/0', {
        essence: 1,
        // 协同类型
        categoryId: row.categoryId,
        // 关联单据
        orderType: 1,
        soNo: row.soNo,
        categoryIdChain: row.categoryIdChain,
        fromPage: 'csTODOPage'
      })
    }
  }
};
</script>
