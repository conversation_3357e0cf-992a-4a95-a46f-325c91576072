<template>
  <el-dialog
    :show-close="false"
    title="编辑更多信息"
    :visible.sync="showDlg"
    top="10px"
    width="900px"
    custom-class="CreateOrder-EditMore"
    @open="openDialog"
    @closed="$emit('update:showDialog', false)"
  >
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="交货信息" name="delivery">
        <DeliveryInfo
          ref="deliveryInfo"
          :key="deliveryKey"
          :customer="customer"
          :customer-detail="customerDetail"
          :order-data="orderData"
          :orderType="orderType"
          :companyCode="companyCode"
          v-on="$listeners"
          @close="closeDialog"
          @submit="submitDelivery"
        />
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="invoice">
        <InvoiceInfo
          ref="invoiceInfo"
          :key="invoiceKey"
          :customer="customer"
          :customer-detail="customerDetail"
          :order-data="orderData"
          :orderType="orderType"
          @close="closeDialog"
          @submit="submitInvoice"
        />
      </el-tab-pane>
    </el-tabs>
    <div class="ba-row-center">
      <el-button type="primary" @click="submit">确认保存</el-button>
      <el-button @click="closeDialog">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import * as shortid from 'shortid'
import DeliveryInfo from './EditMoreTab/DeliveryInfo'
import InvoiceInfo from './EditMoreTab/InvoiceInfo'

export default {
  components: {
    DeliveryInfo,
    InvoiceInfo
  },
  props: ['showDialog', 'customer', 'orderData', 'customerDetail', 'orderType', 'companyCode'],
  data () {
    return {
      activeName: 'delivery',
      deliveryKey: shortid.generate(),
      invoiceKey: shortid.generate()
    }
  },
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    openDialog () {
      this.deliveryKey = shortid.generate()
      this.invoiceKey = shortid.generate()
    },
    closeDialog () {
      this.$emit('update:showDialog', false)
      this.$refs.deliveryInfo.$refs['deliveryForm'].resetFields()
      this.$refs.invoiceInfo.$refs['invoiceForm'].resetFields()
      this.$refs.deliveryInfo.$refs['deliveryForm'].clearValidate()
      this.$refs.invoiceInfo.$refs['invoiceForm'].clearValidate()
    },
    handleClick (tab, event) {
      console.log(tab, event)
    },
    submitInvoice (invoice) {
      this.$emit('submitInvoice', invoice)
    },
    submitDelivery (delivery) {
      this.$emit('submitDelivery', delivery)
    },
    submit () {
      this.$refs.deliveryInfo.$refs['deliveryForm'].validate((valid1, items) => {
        if (valid1) {
          this.$refs.invoiceInfo.$refs['invoiceForm'].validate(valid2 => {
            if (valid2) {
              const invoiceRet = this.$refs.invoiceInfo.submit('invoiceForm')
              if (invoiceRet !== false) {
                this.$refs.deliveryInfo.submit('deliveryForm')
                this.$emit('update:showDialog', false)
                this.$emit('getDeliveryTime')
              }
            } else {
              this.$message.error('请检查发票信息')
              return false
            }
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="scss">
.CreateOrder-EditMore {
  .el-dialog__body {
    padding-top: 0;
  }
}
.ba-row-center{
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
