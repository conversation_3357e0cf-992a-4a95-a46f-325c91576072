<template>
  <el-form ref="deliveryForm" label-width="140px" :rules="rules" :model="deliveryData">
    <DividerHeader>订单信息</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="backupOrder">
          <el-checkbox v-model="deliveryData.backupOrder" label="后补订单" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="paid">
          <el-checkbox v-model="deliveryData.paid" label="已付款" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="clearSlackStock">
          <el-checkbox
            v-model="deliveryData.clearSlackStock"
            label="清呆滞库存"
            true-label="X"
            false-label="Z"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="virtualReturn">
          <el-checkbox v-model="deliveryData.virtualReturn" label="不向客户展示" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="autoDelivery">
          <el-checkbox v-model="deliveryData.autoDelivery" label="自动发货" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="项目号" prop="projectNo">
          <el-input  v-model="deliveryData.projectNo" placeholder="请输入项目号" maxlength="40"/>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="预收款金额" prop="collectionAmount">
          <el-input v-model="deliveryData.collectionAmount" label="预收款金额"/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="供应商代码" prop="supplierAccount">
          <el-input v-model="deliveryData.supplierAccount" placeholder="请输入供应商代码" maxlength="10"/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="付款备注" prop="paymentNote">
          <el-input v-model="deliveryData.paymentNote" maxlength="50" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="合同备注" prop="agreementNote">
          <el-input v-model="deliveryData.agreementNote" maxlength="50" />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>随货文件要求</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachOrder">
          <el-checkbox v-model="deliveryData.attachOrder" label="附订单" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachCoa">
          <el-checkbox v-model="deliveryData.attachCoa" label="附COA" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachMsds">
          <el-checkbox v-model="deliveryData.attachMsds" label="附MSDS" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="attachTds">
          <el-checkbox v-model="deliveryData.attachTds" label="附TDS" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="specifiedDocument">
          <el-checkbox v-model="deliveryData.specifiedDocument" label="其他随货资料" />
        </el-form-item>
      </el-col>
      <!-- <el-col :span="6">
        <el-form-item label-width="0" prop="referenceStandardShippingReq">
          <el-checkbox v-model="deliveryData.referenceStandardShippingReq" label="参考标准客户出货要求文件" />
        </el-form-item>
      </el-col> -->
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="送货单模板" prop="deliverySlipTemplate">
          <el-select
            v-model="deliveryData.deliverySlipTemplate"
            placeholder="请选择送货单模板"
          >
            <el-option
              v-for="item in dictList['deliverySlipTemplate']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="打印份数" prop="supplierAccount">
          <el-input-number :min="0" :max="99" :step="1" :precision="0" v-model="deliveryData.printNum"/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.dnPaperReq" field="dnPaperReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService clearable v-model="deliveryData.dnIncidentalWay" field="dnIncidentalWay" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.dnSignatureReq" field="dnSignatureReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="标签模板" prop="labelTemplate">
          <el-select
            v-model="deliveryData.labelTemplate"
            placeholder="请选择标签模板"
          >
            <el-option
              v-for="item in dictList['labelTemplate']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.otherLabelReq" field="otherLabelReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.fastenerLabelReq" field="fastenerLabelReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.labelPasteWay" field="labelPasteWay" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="合格证标识" prop="certificateIdentification">
          <el-select
            v-model="deliveryData.certificateIdentification"
            placeholder="请选合格证标识"
          >
            <el-option
              v-for="item in dictList['certificateIdentification']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="签单返回" prop="signingBack">
          <el-select
            v-model="deliveryData.signingBack"
            placeholder="请选择签单返回"
          >
            <el-option
              v-for="item in dictList['signingBack']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
              :disabled="item.status==='stop'"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>仓储要求</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label-width="0" prop="hideLogo" style="padding-left:20px">
          <el-checkbox v-model="deliveryData.hideLogo" label="隐藏logo" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.fastenerDetect" field="fastenerDetect" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService
          v-model="deliveryData.packagingReq"
          dictName="dictList"
          field="packagingReq"
          :isMultiple="true"
          :isForceReset="true"
          defaultLabel="包装要求"
        />
      </el-col>
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.fastenerSpecialPackageReq" field="fastenerSpecialPackageReq" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="交货其他备注" prop="deliveryOtherNote">
          <el-input v-model="deliveryData.deliveryOtherNote" maxlength="50"/>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>运输要求</DividerHeader>
    <el-row :gutter="10" class="checkBox">
      <el-col :span="6">
        <el-form-item label-width="0" prop="exportProcessingZone">
          <el-checkbox v-model="deliveryData.exportProcessingZone" label="保税区/出口加工区" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="receiptTimeCategory">
          <el-checkbox v-model="deliveryData.receiptTimeCategory" label="工作日与周末均可收货" />
        </el-form-item>
      </el-col>
      <el-col style="margin-left: -10px;" :span="12">
        <el-form-item label="预约送货" prop="scheduleDelivery">
          <el-select
            v-model="deliveryData.scheduleDelivery"
            placeholder="请选择预约送货方式"
            clearable
          >
            <el-option
              v-for="item in dictList['scheduleDelivery']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <!-- <el-col :span="12">
        <SelectOrderService v-model="deliveryData.specifiedReceiptDayOfWeek" field="specifiedReceiptDayOfWeek" />
      </el-col> -->
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.specifiedReceiptTime" field="specifiedReceiptTime" />
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="装运条件" prop="shippingCondition">
          <el-select
            v-model="deliveryData.shippingCondition"
            placeholder="请选择装运条件"
          >
            <el-option
              v-for="item in dictList['shippingConditions']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="送卸货要求" prop="deliveryUnloadingReq">
          <el-select
            v-model="deliveryData.deliveryUnloadingReq"
            placeholder="请选择送卸货要求"
            clearable
          >
            <el-option
              v-for="item in dictList['deliveryUnloadingReq']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService clearable v-model="deliveryData.disableShipping" field="disableShipping" :limit="7" />
      </el-col>
      <!-- <el-col :span="12">
        <SelectOrderService clearable v-model="deliveryData.designatedShipping" field="designatedShipping" />
      </el-col> -->
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.vehicleReq" field="vehicleReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService
          v-model="deliveryData.deliveryRequirements"
          clearable
          dictName="dictList"
          dictKey="deliveryRequirement"
          field="deliveryRequirements"
          :isMultiple="true"
          :isForceReset="true"
          defaultLabel="配送员要求"
        />
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <SelectOrderService v-model="deliveryData.signingReq" field="signingReq" />
      </el-col>
      <el-col :span="12">
        <SelectOrderService clearable v-model="deliveryData.forkliftRelated" field="forkliftRelated" />
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { isEmpty } from 'lodash'
import DividerHeader from '@/components/DividerHeader'
import SelectOrderService from '@/pages/orderSale/components/common/SelectOrderService'
import { isForecastOrder } from '@/pages/orderSale/utils/orderType'
import { unfoldField } from '@/utils/orderService'
const checkboxAttrs = [
  'autoDelivery',
  'paid', 'attachOrder', 'specifiedDocument', 'exportProcessingZone', 'backupOrder',
  'virtualReturn', 'attachCoa', 'attachMsds',
  'attachMsds', 'hideLogo', 'clearSlackStock'
]

const selectAttrs = [
  'shippingCondition', 'signingBack', 'deliverySlipTemplate',
  'deliveryUnloadingReq', 'labelTemplate', 'paymentNote',
  'agreementNote',
  'dnPaperReq',
  'dnIncidentalWay',
  'fastenerDetect',
  'fastenerSpecialPackageReq',
  'deliveryRequirements',
  'signingReq',
  'scheduleDelivery'
]

const orderServiceFields = [
  'deliveryRequirements',
  'packagingReq',
  'dnSignatureReq',
  'otherLabelReq',
  'fastenerLabelReq',
  'labelPasteWay',
  // 'specifiedReceiptDayOfWeek',
  // 'specifiedReceiptTime',
  // 'disableShipping',
  'vehicleReq'
]

const rules = {
  collectionAmount: [
    {
      pattern: /^(0|[1-9]\d{0,12})(\.\d{1,2})?$/, message: '请输入有效的预收款金额', trigger: 'blur'
    }
  ]

}

export default {
  components: {
    DividerHeader,
    SelectOrderService
  },
  props: ['customer', 'orderData', 'customerDetail', 'orderType', 'companyCode'],
  data () {
    const detailData = {}
    checkboxAttrs.forEach(attr => {
      if (this.customerDetail && this.customerDetail[attr]) {
        if (this.customerDetail[attr] === 'Z') {
          detailData[attr] = false
        }
        if (this.customerDetail[attr] === 'X') {
          detailData[attr] = true
        }
      }
    })
    selectAttrs.forEach(attr => {
      if (this.customerDetail && this.customerDetail[attr]) {
        detailData[attr] = this.customerDetail[attr]
      }
    })
    orderServiceFields.forEach(field => {
      if (this.customerDetail && this.customerDetail[field]) {
        detailData[field] = this.customerDetail[field].split(',')
      }
    })
    let packagingReq = ['0']
    if (this.orderData) {
      if (this.orderData.moreDeliveryData && this.orderData.moreDeliveryData.packagingReq) {
        packagingReq = unfoldField(this.orderData.moreDeliveryData.packagingReq)
      } else if (this.orderData.packagingReq) {
        packagingReq = unfoldField(this.orderData.packagingReq)
      }
    }
    const certificateIdentification = this.customerDetail['certificateIdentification'] || '0'
    let deliveryOtherNote = ''
    if (this.orderData) {
      if (this.orderData.moreDeliveryData && this.orderData.moreDeliveryData.deliveryOtherNote !== undefined) {
        deliveryOtherNote = this.orderData.moreDeliveryData.deliveryOtherNote
      } else if (this.orderData.deliveryOtherNote) {
        deliveryOtherNote = this.orderData.deliveryOtherNote
      } else if (this.customerDetail.deliveryOtherNote) {
        deliveryOtherNote = this.customerDetail.deliveryOtherNote
      }
    }
    let printNum = 0
    if (this.orderData) {
      if (this.orderData.moreDeliveryData && this.orderData.moreDeliveryData.printNum) {
        printNum = this.orderData.moreDeliveryData.printNum
      } else if (this.orderData.printNum) {
        printNum = this.orderData.printNum
      }
    }
    let deliveryRequirements = []
    if (this.orderData) {
      if (this.orderData.moreDeliveryData && this.orderData.moreDeliveryData.deliveryRequirements) {
        deliveryRequirements = unfoldField(this.orderData.moreDeliveryData.deliveryRequirements)
      } else if (this.orderData.deliveryRequirements) {
        deliveryRequirements = unfoldField(this.orderData.deliveryRequirements)
      }
    }
    // 先判断moreDeliveryData里是否有值，没有默认值先取联系人，联系人里没有再取销售范围
    let receiptTimeCategory = this?.orderData?.moreDeliveryData?.receiptTimeCategory ||
      this?.orderData?.receiverContact?.isFullTimeReceiving === '1' ||
      this?.customerDetail?.receiptTimeCategory === 'X'
    // if (this?.orderData?.moreDeliveryData?.receiptTimeCategory) {
    //   receiptTimeCategory = this.orderData.moreDeliveryData.receiptTimeCategory
    // } else if (this?.orderData?.receiverContact?.isFullTimeReceiving) {
    //   receiptTimeCategory = this.orderData.receiverContact.isFullTimeReceiving === '1'
    // } else if (this?.customerDetail?.receiptTimeCategory) {
    //   receiptTimeCategory = this.customerDetail.receiptTimeCategory === 'X'
    // }
    // 默认值先取联系人，联系人里没有再取销售范围
    let disableShipping = this?.orderData?.moreDeliveryData?.disableShipping ||
      this?.orderData?.receiverContact?.disableShipping?.split(',').filter(Boolean) ||
      this?.customerDetail?.disableShipping?.split(',').filter(Boolean) || []
    // if (this?.orderData?.moreDeliveryData?.disableShipping) {
    //   disableShipping = this.orderData.moreDeliveryData.disableShipping
    // } else if (this?.orderData?.receiverContact?.disableShipping) {
    //   disableShipping = this.orderData.receiverContact.disableShipping.split(',')
    // } else if (this?.customerDetail?.disableShipping) {
    //   disableShipping = this.customerDetail.disableShipping.split(',')
    // }
    let designatedShipping = this?.orderData?.moreDeliveryData?.designatedShipping ||
      this?.orderData?.receiverContact?.designatedShipping?.split(',').filter(Boolean) ||
      this?.customerDetail?.designatedShipping?.split(',').filter(Boolean) || []
    // if (this?.orderData?.moreDeliveryData?.designatedShipping) {
    //   designatedShipping = this.orderData.moreDeliveryData.designatedShipping
    // } else if (this?.orderData?.receiverContact?.designatedShipping) {
    //   designatedShipping = this.orderData.receiverContact.designatedShipping.split(',')
    // } else if (this?.customerDetail?.designatedShipping) {
    //   designatedShipping = this.customerDetail.designatedShipping.split(',')
    // }

    let signingReq = this?.orderData?.moreDeliveryData?.signingReq ||
      this?.orderData?.receiverContact?.signingRequirements ||
      this?.customerDetail?.signingReq || []
    // if (this?.orderData?.moreDeliveryData?.signingReq) {
    //   signingReq = this.orderData.moreDeliveryData.signingReq
    // } else if (this?.orderData?.receiverContact?.signingRequirements) {
    //   signingReq = this.orderData.receiverContact.signingRequirements
    // } else if (this?.customerDetail?.signingReq) {
    //   signingReq = this.customerDetail.signingReq
    // }
    let specifiedReceiptDayOfWeek = this?.orderData?.moreDeliveryData?.specifiedReceiptDayOfWeek ||
      this?.orderData?.receiverContact?.specifyDeliveryDate?.split(',').filter(Boolean) ||
      this?.customerDetail?.specifiedReceiptDayOfWeek?.split(',').filter(Boolean) || []
    // if (this?.orderData?.moreDeliveryData?.specifiedReceiptDayOfWeek) {
    //   specifiedReceiptDayOfWeek = this.orderData.moreDeliveryData.specifiedReceiptDayOfWeek
    // } else if (this?.orderData?.receiverContact?.specifyDeliveryDate) {
    //   specifiedReceiptDayOfWeek = this.orderData.receiverContact.specifyDeliveryDate.split(',')
    // } else if (this?.customerDetail?.specifiedReceiptDayOfWeek) {
    //   specifiedReceiptDayOfWeek = this.customerDetail.specifiedReceiptDayOfWeek.split(',')
    // }
    let specifiedReceiptTime = this?.orderData?.moreDeliveryData?.specifiedReceiptTime ||
      this?.orderData?.receiverContact?.specifyDeliveryTime?.split(',').filter(Boolean) ||
      this?.customerDetail?.specifiedReceiptTime?.split(',').filter(Boolean) || []
    // if (this?.orderData?.moreDeliveryData?.specifiedReceiptTime) {
    //   specifiedReceiptTime = this.orderData.moreDeliveryData.specifiedReceiptTime
    // } else if (this?.orderData?.receiverContact?.specifyDeliveryTime) {
    //   specifiedReceiptTime = this.orderData.receiverContact.specifyDeliveryTime.split(',')
    // } else if (this?.customerDetail?.specifiedReceiptTime) {
    //   specifiedReceiptTime = this.customerDetail.specifiedReceiptTime.split(',')
    // }
    const forkliftRelated = this?.orderData?.moreDeliveryData?.forkliftRelated || this?.orderData?.receiverContact?.forkliftRelated || ''

    return {
      deliveryData: {
        printNum,
        certificateIdentification,
        ...detailData,
        ...this.orderData.moreDeliveryData,
        packagingReq,
        deliveryOtherNote,
        deliveryRequirements,
        receiptTimeCategory,
        disableShipping,
        designatedShipping,
        signingReq,
        specifiedReceiptDayOfWeek,
        specifiedReceiptTime,
        forkliftRelated
      },
      rules: isForecastOrder(this.orderType) ? { collectionAmount: [
        {
          pattern: /^([1-9]\d{0,12})(\.\d{1,2})?$/, message: '请输入有效的预收款金额', trigger: 'blur'
        }
      ] } : rules
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderServiceDict () {
      return this.$store.state.orderCommon.orderServiceDict || {}
    }
  },
  watch: {
    'deliveryData.disableShipping': {
      handler(newVal) {
        if (!isEmpty(newVal)) {
          this.deliveryData.designatedShipping = []
        }
      }
    },
    'deliveryData.designatedShipping': {
      handler(newVal) {
        if (!isEmpty(newVal)) {
          this.deliveryData.disableShipping = []
        }
      }
    }
  },
  created () {},
  methods: {
    submit (formName) {
      this.$emit('submit', this.deliveryData)
    }
  }
}
</script>

<style scoped lang="scss">
.checkBox {
  margin: 20px;
  padding-left: 20px;
  .el-form-item{
    margin-bottom: 0;
  }
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>
<style lang="scss">
.EditOrderMore-el-select {
  width: 100%;
}
</style>
