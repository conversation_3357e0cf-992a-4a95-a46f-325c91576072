<template>
  <el-form ref="invoiceForm" label-width="140px" :rules="rules" :model="invoiceData">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="客户税号">
          <el-input :value="customerDetail.corporationTaxNum" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开票地址和电话">
          <el-input :value="customerDetail.invoiceAddressTelephone" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="开户行">
          <el-input :value="customerDetail.bankName" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="账号">
          <el-input :value="customerDetail.bankNumber" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户付款账号" prop="customerPayAccountTypeName">
          <el-input v-model="invoiceData.customerPayAccountTypeName" placeholder="请输入客户付款账号" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="收票联系人" prop="receivingInvoiceContact">
          <el-select
            v-model="invoiceData.receivingInvoiceContact"
            placeholder="选择收票联系人"
            style="width:100%"
            filterable
            remote
            reserve-keyword
            value-key="contactId"
            :remote-method="queryInvoiceContactList"
            :loading="loadingInvoiceContactList"
            @change="changeInvoiceContact"
          >
            <el-option
              v-for="(item,index) in invoiceContactList"
              :key="item.contactId"
              :label="item.contactName"
              :value="item"
              :disabled="index===0"
            >
              <div
                class="ba-row-start selectClientItem"
                :style="{fontWeight:index===0?'bold':'normal'}"
              >
                <div>{{ item.contactName }}</div>
                <div>{{ item.contactId }}</div>
                <div>{{ item.contactPhone || '--' }}</div>
                <div>{{ item.address || '--' }}</div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="收票人电话" prop="receivingInvoicePhone">
          <el-input v-model="invoiceData.receivingInvoicePhone" placeholder="选择收票人电话" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="收票地址" prop="receivingInvoiceAddress">
          <el-input v-model="invoiceData.receivingInvoiceAddress" placeholder="选择收票地址" :disabled="true" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="寄票备注">
          <el-input v-model="invoiceData.shippingInfo" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxEmail">
      <el-col :span="6">
        <el-form-item label-width="0" prop="invoicingByMail">
          <el-checkbox v-model="invoiceData.invoicingByMail" label="凭邮件开票" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="returnOffset">
          <el-checkbox v-model="invoiceData.returnOffset" label="退换货抵消" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="mergeBilling">
          <el-checkbox v-model="invoiceData.mergeBilling" label="合并开票" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="autoBilling">
          <el-checkbox v-model="invoiceData.autoBilling" label="自动开票" size="medium" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10" class="checkBoxEmail">
      <el-col :span="6">
        <el-form-item label-width="0" prop="billingRobot">
          <el-checkbox v-model="invoiceData.billingRobot" label="开票机器人" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="showDiscount">
          <el-checkbox v-model="invoiceData.showDiscount" label="显示折扣" size="medium" />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item label-width="0" prop="ifDocMailed">
          <el-checkbox v-model="invoiceData.ifDocMailed" label="附资料邮寄" size="medium" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="发票类型">
          <el-select v-model="invoiceData.invoiceType" placeholder="请选择普票/增票/服务发票">
            <el-option
              v-for="item in dictList['invoiceType']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="合并开票要求">
          <el-select v-model="invoiceData.mergeBillingDemand" placeholder="请选择合并开票要求">
            <el-option
              v-for="item in dictList['mergeBillingDemand']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="快递公司">
          <el-select v-model="invoiceData.expressCompany" clearable placeholder="请选择快递公司">
            <el-option
                v-for="item in dictList['expressCompany']"
                :key="item.code"
                :label="item.name"
                :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="发票备注" prop="financialNote">
          <el-input v-model="invoiceData.financialNote" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import * as createOrder from '@/api/orderSale'
import { isForecastOrder } from '@/pages/orderSale/utils/orderType'
import { contactHeader } from '@/pages/orderSale/constants'

const rules = {
  receivingInvoiceContact: [
    { required: true, message: '请选择收票联系人', trigger: 'blur' }
  ]
}

const checkboxAttrs = ['invoicingByMail', 'autoBilling', 'returnOffset',
  'mergeBilling', 'billingRobot', 'showDiscount', 'ifDocMailed']

const otherAttrs = ['invoiceType', 'shippingInfo', 'mergeBillingDemand', 'financialNote', 'expressCompany']

export default {
  props: ['customer', 'orderData', 'customerDetail', 'orderType'],
  data () {
    let invoiceType
    let expressCompany
    let receivingInvoiceContact
    let receivingInvoicePhone
    let receivingInvoiceAddress
    const detailData = {
      mergeBillingDemand: ''
    }
    checkboxAttrs.forEach(attr => {
      if (this.customerDetail && this.customerDetail[attr]) {
        if (this.customerDetail[attr] === 'Z') {
          detailData[attr] = false
        }
        if (this.customerDetail[attr] === 'X') {
          detailData[attr] = true
        }
      }
    })
    otherAttrs.forEach(attr => {
      if (this.customerDetail && this.customerDetail[attr]) {
        detailData[attr] = this.customerDetail[attr]
      }
    })
    if (this.orderData) {
      invoiceType = (this.orderData.moreInvoiceData && this.orderData.moreInvoiceData.invoiceType)
        ? this.orderData.moreInvoiceData.invoiceType : detailData.invoiceType
    }
    if (this.orderData) {
      expressCompany = (this.orderData.moreInvoiceData && this.orderData.moreInvoiceData.expressCompany)
        ? this.orderData.moreInvoiceData.expressCompany : detailData.expressCompany
    }
    if (this.orderData && this.orderData.receivingInvoiceContact) {
      receivingInvoiceContact = this.orderData.receivingInvoiceContact
      receivingInvoicePhone = this.orderData.receivingInvoiceContact.contactPhone
      receivingInvoiceAddress = this.orderData.receivingInvoiceContact.address || '--'
    }
    return {
      invoiceData: {
        ...detailData,
        ...this.orderData.moreInvoiceData,
        invoiceType: invoiceType || '01', // 默认增票
        expressCompany: expressCompany || '1', // 默认圆通
        receivingInvoiceContact,
        receivingInvoicePhone,
        receivingInvoiceAddress
      },
      loadingInvoiceContactList: false,
      invoiceContactList: [],
      rules: isForecastOrder(this.orderType) ? {} : rules
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  created () {
    if (this.invoiceData.receivingInvoiceContact) {
      this.changeInvoiceContact(this.invoiceData.receivingInvoiceContact)
      this.invoiceContactList = [
        contactHeader,
        this.invoiceData.receivingInvoiceContact
      ]
    }
  },
  methods: {
    handleQueryContactList (contactName, initFn, callbackFn) {
      const { customerNumber } = this.customer
      if (this.customerDetail && this.customerDetail.saleOrgVO) {
        const { saleOrgVO } = this.customerDetail
        if (saleOrgVO) {
          const { salesOrganization, productGroup, distributionChannel } = saleOrgVO
          initFn && initFn()
          createOrder.searchContactListByGroup({
            customerCode: customerNumber,
            contactName,
            distributionChannel,
            productGroup,
            salesOrganization
          }).then(res => {
            callbackFn && callbackFn(res)
          })
        }
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        })
      }
    },
    changeInvoiceContact (contact) {
      const { contactPhone, address } = contact
      this.invoiceData.receivingInvoicePhone = contactPhone
      this.invoiceData.receivingInvoiceAddress = address
    },
    queryInvoiceContactList (contactName) {
      this.handleQueryContactList(contactName, () => {
        this.loadingInvoiceContactList = true
      }, (result) => {
        this.loadingInvoiceContactList = false
        if (result.code === 200) {
          this.invoiceContactList = [
            contactHeader,
            ...result.data.records
          ]
        } else {
          this.$message.error({
            message: result.msg
          })
        }
      })
    },
    submit (formName) {
      this.$emit('submit', this.invoiceData)
    }
  }
}
</script>

<style scoped lang='scss'>
.checkBox {
  margin: 20px;
  padding-left: 20px;
}
.checkBoxEmail {
  padding-left: 140px;
  margin-bottom: 20px;
  .el-form-item {
    margin-bottom: 0;
  }
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 120px;
  }
  div:nth-child(4) {
    width: 300px;
  }
}
.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
</style>

<style lang="scss">
.EditOrderMore-el-select {
  width: 100%;
}
</style>
