<template>
  <div>
    <el-table border :data="tableData" style="width: 100%">
      <el-table-column prop="inquiryDate" label="询价日期" width="150" align="center" />
      <el-table-column prop="will" label="意向" width="100" align="center" />
      <el-table-column prop="taxFlag" label="含税/未税" width="150" align="center" />
      <el-table-column prop="finalOffer" label="最终报价" width="150" align="center" />
      <el-table-column prop="uomIdName" label="单位" width="100" align="center" />
      <el-table-column prop="quoteDate" label="报价日期" width="100" align="center" />
      <el-table-column prop="quotationNo" label="报价单" width="150" align="center" />
    </el-table>
    <div class="ba-row-end" style="margin-top:20px">
      <el-pagination
        :current-page.sync="current"
        background
        layout="prev, pager, next"
        :total="total"
        @current-change="pageChange"
      />
    </div>
    <div class="ba-row-center btnGroup">
      <el-button plain type="primary" @click="$emit('close')">关闭</el-button>
    </div>
  </div>
</template>
<script>
import * as sellOrder from '@/api/orderSale'
export default {
  props: ['skuDetail', 'customerDetail'],
  data () {
    return {
      tableData: [],
      total: 0,
      current: 1
    }
  },
  created () {
    this.pageChange(this.current)
  },
  methods: {
    pageChange (page) {
      sellOrder
        .enquiryList(
          this.customerDetail.customerId,
          this.skuDetail.skuNo,
          this.current
        )
        .then(result => {
          if (result.code === 200) {
            this.total = result.data.total
            this.current = result.data.current
            this.tableData = result.data.records
          }
        })
    }
  }
}
</script>
<style scoped lang="scss">
.btnGroup {
  margin: 20px 0;
}
</style>
