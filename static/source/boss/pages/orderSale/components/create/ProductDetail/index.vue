<template>
  <el-dialog
    :show-close="false"
    title="商品详情"
    :visible.sync="showDlg"
    top="10px"
    width="860px"
    z-index="1999"
    custom-class="CreateOrder-ProductDetail"
    @open="openDlg"
    @closed="$emit('update:showDialog', false)"
  >
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="详细信息" name="detail">
        <Detail :key="key1" :itemKey="itemKey" v-bind="$props" v-on="$listeners" @close="closeDialog" @submit="submit" />
      </el-tab-pane>
      <el-tab-pane label="询价记录" name="enquiry">
        <EnquiryRecord :key="key2" v-bind="$props" @close="closeDialog" />
      </el-tab-pane>
      <el-tab-pane label="订单记录" name="order">
        <OrderRecord :key="key3" v-bind="$props" @close="closeDialog" />
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
import * as shoirtid from 'shortid'
import Detail from './ProductDetailTab/Detail'
import EnquiryRecord from './ProductDetailTab/EnquiryRecord'
import OrderRecord from './ProductDetailTab/OrderRecord'

export default {
  components: {
    Detail,
    EnquiryRecord,
    OrderRecord
  },
  props: [
    'index',
    'itemKey',
    'showDialog',
    'skuDetail',
    'customer',
    'customerDetail',
    'filterStorageLocation',
    'formatPrice',
    'orderData',
    'orderType'
  ],
  data () {
    return {
      key1: shoirtid.generate(),
      key2: shoirtid.generate(),
      key3: shoirtid.generate(),
      activeName: 'detail'
    }
  },
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    openDlg () {
      this.key1 = shoirtid.generate()
      this.key2 = shoirtid.generate()
      this.key3 = shoirtid.generate()
    },
    closeDialog () {
      this.$emit('update:showDialog', false)
    },
    handleClick (tab, event) {
      console.log(tab, event)
    },
    submit() {
      this.$emit('submit')
    }
  }
}
</script>
<style lang="scss">
.CreateOrder-ProductDetail {
  .el-dialog__body {
    padding-top: 0;
  }
}
</style>
