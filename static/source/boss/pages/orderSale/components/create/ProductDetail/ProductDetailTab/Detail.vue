<template>
  <el-form v-loading="loading" ref="goodsDetailForm" :model="sku" :rules="rules" label-width="135px">
    <DividerHeader>商品信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="客户物料号" prop="customerMaterialNo">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户物料号</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-input v-model="sku.customerMaterialNo" clearable placeholder="客户物料号"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户行号" prop="customerOrderNo">
          <el-input v-model="sku.customerOrderNo" placeholder="客户行号"/>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="制造商型号">
          <el-input :value="sku.manufacturerNo" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户规格型号" prop="customerSpecificationModel">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户规格型号</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-input v-model="sku.customerSpecificationModel" placeholder="客户规格型号" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="商品编号">
          <SelectSkus ref="selectSku" :selectedSku="selectedSku" :orderData="orderData" :customerNo="orderData.customerNumber" @handleSelectSku="addSkuToTable" :customerMaterialNo="sku.customerMaterialNo" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="商品描述" prop="materialDescribe">
          <el-input v-model="sku.materialDescribe" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="客户物料名称" prop="customerMaterialName">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户物料名称</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-input v-model="sku.customerMaterialName" maxlength="100" placeholder="客户物料名称" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="数量" prop="quantity">
          <el-input-number
            v-model="sku.quantity"
            style="width:100%"
            :precision="0"
            :step="1"
            :min="0"
            @change="handleQuantityChange"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户物料数量" prop="customerMaterialQuantity">
          <span slot="label">
            <div class="label">
              <span>客户物料数量</span>
              <customerMaterialTable v-if="customerMaterialTableData.length > 1" :tableList="customerMaterialTableData" @change="handleMaterialClick" />
              <el-tooltip
                :content="`当前商品数量与客户物料数量的比例为${rate}`"
                effect="dark"
                placement="top"
                v-if="!!rate"
              >
                <i class="el-icon-info" />
              </el-tooltip>
            </div>
          </span>
          <el-input-number
            v-model="sku.customerMaterialQuantity"
            :precision="6"
            :step="1"
            :min="0"
            style="width:100%"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="单位" prop="quantityUnit">
          <el-select
            v-model="sku.quantityUnit"
            placeholder="请选择单位"
            value-key="unit"
            @change="changeQuantityUnit"
            clearable
          >
            <el-option
              v-for="item in sku.packageInfoList"
              :key="item.skuNo+item.unit"
              :label="item.unitName"
              :value="item.unit"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="客户物料单位" prop="customerMaterialUnit">
          <template slot="label" v-if="customerMaterialTableData.length > 1">
            <div class="label">
              <span>客户物料单位</span>
              <customerMaterialTable :tableList="customerMaterialTableData" @change="handleMaterialClick" />
            </div>
          </template>
          <el-select v-model="sku.customerMaterialUnit" placeholder="请选择"
            clearable filterable allow-create>
            <el-option
              v-for="item in dictList['customerQuantityUnit']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>仓位信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="工厂" prop="factory">
          <el-select
            v-model="sku.factory"
            placeholder="请选择"
            value-key="code"
            @change="changeFactory"
          >
            <el-option
              v-for="item in factoryList"
              :key="item.code"
              :label="(item.code > 0 ? item.code : '')+item.name"
              :value="item"
              :disabled="isFactoryDisable(sku, item.code)"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" v-if="!isForecast">
        <el-form-item label="选择直发" prop="directDeliverySupplier">
          <el-select
            v-model="sku.directDeliverySupplier"
            placeholder="请选择"
            @change="changeDirectDeliverySupplier"
            :disabled="isEnableDirectDeliverySupplier"
          >
            <el-option
              v-for="item in directDeliverySupplierList"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="库位" prop="position">
          <el-select
            v-model="sku.position"
            value-key="code"
            filterable placeholder="请选择" @change="changePosition">
            <div v-if="sku.directDeliverySupplier === '0'" slot="empty" style="text-align:center">
              <p style="margin-top:10px;color:grey">无匹配数据</p>
              <p style="margin:10px 10px 0 10px;color:grey" v-if="morePositionDisabled">所选仓不在仓网内，如有疑问请联系IT</p>
            </div>
            <el-option
              v-for="item in (sku.directDeliverySupplier === '0' || !sku.directDeliverySupplier) ? (sku.simPositionList || []) : sku.positionList"
              :key="item.code"
              :label="(item.code > 0 ? item.code : '')+item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>价格信息</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12" :class="{tax:!isTaxedCustomer}">
        <el-form-item label="未税单价" prop="freeTaxPrice">
          <el-input-number
            v-model="sku.freeTaxPrice"
            :disabled="isTaxedCustomer||isFree"
            style="width:100%"
            :min="0"
            :precision="6"
            :step="1"
            @change="unTaxedChange"
          />
        </el-form-item>

        <el-dropdown v-if="!isTaxedCustomer" trigger="click">
          <el-link class="link" size="mini" :underline="false" type="primary">
            建议价格
            <i class="el-icon-view el-icon--right" />
          </el-link>
          <el-dropdown-menu slot="dropdown" :show-timeout="50">
            <el-dropdown-item style="padding: 0">
              <el-table :data="unTaxedTable" border style="width: 100%" size="mini">
                <el-table-column prop="name" label="价格项" width="150" align="center" />
                <el-table-column prop="price" label="未税单价" width="100" align="center" />
                <el-table-column prop="rate" label="税率" width="100" align="center" />
              </el-table>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
      <el-col :span="12" :class="{tax:isTaxedCustomer}">
        <el-form-item label="含税单价" prop="taxPrice">
          <el-input-number
            v-model="sku.taxPrice"
            :disabled="!isTaxedCustomer||isFree"
            style="width:100%"
            :min="0"
            :precision="6"
            :step="1"
            @change="taxedChange"
          />
        </el-form-item>
        <el-dropdown v-if="isTaxedCustomer" trigger="click">
          <el-link class="link" size="mini" :underline="false" type="primary">
            建议价格
            <i class="el-icon-view el-icon--right" />
          </el-link>
          <el-dropdown-menu slot="dropdown" :show-timeout="50">
            <el-dropdown-item style="padding: 0">
              <el-table border :data="taxedTable" style="width: 100%" size="mini">
                <el-table-column prop="name" label="价格项" width="150" align="center" />
                <el-table-column prop="price" label="含税单价" width="100" align="center" />
                <el-table-column prop="rate" label="税率" width="100" align="center" />
              </el-table>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="税率">
          <el-input :value="sku.taxRate?Math.round(sku.taxRate * 100)+'%':''" disabled />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="税额">
          <el-input disabled :value="formatPrice(sku.taxAmount)" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="商品未税总金额">
          <el-input disabled :value="unTaxedPriceTotal" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="商品含税总金额">
          <el-input disabled :value="taxedPriceTotal" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="折扣类型">
          <el-select v-model="sku.discountConditionType" placeholder="请选择" disabled clearable filterable>
            <el-option
              v-for="item in dictList['discountConditionType']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="折扣金额">
          <el-input-number
            v-model="sku.discountAmount"
            :precision="2"
            :step="1"
            :min="0"
            style="width:100%"
            placeholder="请输入折扣金额"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <DividerHeader>其他</DividerHeader>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="" prop="needScrapingCode">
          <el-checkbox v-model="sku.needScrapingCode">需要刮码</el-checkbox>
        </el-form-item>
      </el-col>
      <el-col :span="6" style="text-align:center">
        <el-form-item>
          <el-checkbox
            v-model="sku.customerDateSensitive"
            true-label="X"
            false-label="Z"
          >
            客户交期敏感
          </el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="客户期望送达日期" prop="customerDate">
          <el-date-picker v-model="sku.customerDate" :picker-options="pickerOptions" @focus="focusDatePicker" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" style="width: 100%" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-form-item label="领用人" prop="demandUser">
          <el-input v-model="sku.demandUser" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="需求部门" prop="demandDepartment">
          <el-input v-model="sku.demandDepartment" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="sku.remark" />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="24">
        <el-form-item label="采购备注" prop="purchaseNote">
          <el-input v-model="sku.purchaseNote" />
        </el-form-item>
      </el-col>
    </el-row>
    <template v-if="showCustom">
      <el-row :gutter="10" v-for="(custom, index) in (sku && sku.customPropertyList || [])" :key="index">
        <el-col :span="12">
          <el-form-item :label="`定制属性${index + 1}`">
            <el-input readonly disabled :value="custom.customProperty" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="`定制属性备注${index + 1}`" >
            <el-input v-model="custom.customPropertyRemark" :placeholder="custom.placeholder" @change="handleCustomChange" maxlength="50" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <el-row :gutter="10" v-if="orderType==='Z018'">
      <el-col :span="12">
        <el-form-item label="租赁截止日期" prop="rentalDueDate">
          <el-date-picker v-model="sku.rentalDueDate" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" style="width: 100%" />
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="固定资产编号" prop="fixedAssetsId">
          <el-input v-model="sku.fixedAssetsId" placeholder="请输入固定资产编号" />
        </el-form-item>
      </el-col>
    </el-row>
    <div class="ba-row-center">
      <el-button type="primary" @click="submit">确认保存</el-button>
      <el-button @click="cancel('goodsDetailForm')">取消</el-button>
    </div>
    <DiffCustomerMaterial :show-dialog.sync="showDiffDialog" :resultMaterialObj="resultMaterialObj" :inputMaterialObj="inputMaterialObj" @submit="handleDiffMaterialRelation" />
  </el-form>
</template>
<script>
import { endsWith } from 'lodash'
import { searchMaterial, getDeliveryWarehouse, searchCustomerMaterialRelation, getClientDetail, accurateQuery } from '@/api/orderSale'
import { formatPrice } from '@/utils'
import { getFactoryList, getDirectDeliverySupplierList, getDisabledDate } from '@/pages/orderSale/utils'
import { isFreeOrder, isServiceOrder, isForecastOrder, isEnableDirectDeliverySupplier } from '@/pages/orderSale/utils/orderType'
import DividerHeader from '@/components/DividerHeader'
import customerMaterialTable from '@/pages/orderSale/components/common/customerMaterialTable.vue';
import SelectSkus from '@/pages/orderSale/components/common/SelectSkus.vue';
import DiffCustomerMaterial from '@/pages/orderSale/components/common/DiffCustomerMaterial.vue';
import { isDiffCustomerRelation } from '@/pages/orderSale/utils/index'

const defaultRules = {
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  directDeliverySupplier: [
    { required: true, message: '请输入直发信息', trigger: 'blur' }
  ],
  position: [
    { required: true, message: '请选择库位', trigger: 'blur' }
  ],
  factory: [
    { required: true, message: '请选择工厂', trigger: 'blur' }
  ]
}
const nullSkuRules = {
  quantity: [
    { required: true, message: '请输入数量', trigger: 'blur' }
  ],
  customerMaterialName: [
    { required: true, message: '请输入客户物料名称', trigger: 'blur' }
  ]
}

export default {
  props: [
    'index',
    'itemKey',
    'skuDetail',
    'customerDetail',
    'formatPrice',
    'orderData',
    'orderType'
  ],
  data () {
    const { needScrapingCode } = this.skuDetail
    const { companycode } = this.$route.params
    return {
      company: companycode,
      sku: {
        discountAmount: 0,
        discountConditionType: 'ZK01',
        rentalDueDate: '',
        fixedAssetsId: '',
        customerSpecificationModel: '',
        customerOrderNo: '',
        customerMaterialName: '',
        customPropertyList: [],
        ...this.skuDetail,
        needScrapingCode: needScrapingCode === 'X'
      },
      customer: this.customerDetail,
      currentQuantityUnit: null,
      materialList: [],
      rate: '',
      rules: {},
      pickerOptions: {
        disabledDate: (time) => {
          const specifiedReceiptDayOfWeek = Array.isArray(this.orderData?.specifiedReceiptDayOfWeek) ? this.orderData?.specifiedReceiptDayOfWeek : this.orderData?.specifiedReceiptDayOfWeek?.split(',')
          const receiptTimeCategory = this.orderData?.receiptTimeCategory
          const check = !['Z002', 'Z014'].includes(this.orderType) && !['8', 'X'].includes(this.orderData.bidCustomer)
          return getDisabledDate(time, specifiedReceiptDayOfWeek, receiptTimeCategory, check)
        }
      },
      customerMaterialTableData: [],
      loading: false,
      showDiffDialog: false,
      oldSku: {}, // 记录没选择sku之前填的信息
      formatedSku: {},
      resultMaterialObj: {}, // 查出来的客户物料关系
      inputMaterialObj: {} // 页面手动输入的客户物料关系
    }
  },
  components: {
    DividerHeader,
    customerMaterialTable,
    SelectSkus,
    DiffCustomerMaterial
  },
  computed: {
    showCustom () {
      return (/z001/gim.test(this.orderType) && this.sku?.customPropertyList?.length > 0)
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    morePositionDisabled () {
      return this.$store.state.orderCommon.morePositionDisabled
    },
    searchSkuSwitch () {
      return this.$store.state.orderCommon.searchSkuSwitch
    },
    factoryList () {
      const { selectedSalesRange } = this.orderData
      if (selectedSalesRange) {
        const { distributionChannel, salesOrganization } = selectedSalesRange
        return getFactoryList(distributionChannel, salesOrganization, this.dictList)
      }
      return []
    },
    salesOrganization () {
      if (this.orderData) {
        const { selectedSalesRange } = this.orderData
        if (selectedSalesRange) {
          return selectedSalesRange.salesOrganization
        }
      }
      return ''
    },
    taxedPriceTotal () {
      return this.sku.taxPrice ? formatPrice(this.sku.quantity * this.sku.taxPrice) : 0
    },
    unTaxedPriceTotal () {
      return this.sku.freeTaxPrice ? formatPrice(this.sku.quantity * this.sku.freeTaxPrice) : 0
    },
    unTaxedTable () {
      const { factoryProductPriceVOMap, factory } = this.sku
      if (factory && factoryProductPriceVOMap) {
        const code = factory.code
        const priceMap = factoryProductPriceVOMap[code]
        if (priceMap) {
          const { dealerMinPrice, suggestPrice, taxRate } = priceMap
          let res = [
            {
              name: '建议销售价',
              price: suggestPrice ? formatPrice(suggestPrice) : '--',
              rate: taxRate ? `${taxRate}%` : '--'
            }
          ]
          const { selectedSalesRange } = this.orderData
          if (selectedSalesRange) {
            const { distributionChannel } = selectedSalesRange
            if (distributionChannel === '02') {
              res.push({
                name: '最低折扣价',
                price: dealerMinPrice ? formatPrice(dealerMinPrice) : '--',
                rate: taxRate ? `${taxRate}%` : '--'
              })
            }
          }
          return res;
        }
      }
      return []
    },
    taxedTable () {
      const { factoryProductPriceVOMap, factory } = this.sku
      if (factory && factoryProductPriceVOMap) {
        const code = factory.code
        const priceMap = factoryProductPriceVOMap[code]
        if (priceMap) {
          const { dealerMinPrice, suggestPrice, taxRate } = priceMap
          let res = [
            {
              name: '建议销售价',
              price: suggestPrice ? formatPrice(suggestPrice) : '--',
              rate: taxRate ? `${taxRate}%` : '--'
            }
          ]
          const { selectedSalesRange } = this.orderData
          if (selectedSalesRange) {
            const { distributionChannel } = selectedSalesRange
            if (distributionChannel === '02') {
              res.push({
                name: '最低折扣价',
                price: dealerMinPrice ? formatPrice(dealerMinPrice) : '--',
                rate: taxRate ? `${taxRate}%` : '--'
              })
            }
          }
          return res;
        }
      }
      return []
    },
    isTaxedCustomer () {
      return this.customerDetail && this.customerDetail.isTax === '1'
    },
    isForecast () {
      return isForecastOrder(this.orderType)
    },
    isFree () {
      return isFreeOrder(this.orderType)
    },
    isEnableDirectDeliverySupplier () {
      return isEnableDirectDeliverySupplier(this.orderType)
    },
    directDeliverySupplierList () {
      return getDirectDeliverySupplierList(this.dictList, this.orderType, this.salesOrganization)
    },
    isNullSku () {
      return !this.sku.skuNo // 没有skuNo时认为此行是无sku行
    },
    skus () {
      const key = this.getKey()
      return this.$store.state.orderGoods.skuList[key] || []
    },
    selectedSku () {
      const { skuNo, materialDescribe, customerMaterialNo, customerMaterialName, customerSkuUnitCount, customerMaterialUnit, customerSpecificationModel } = this.sku
      const sku = {
        skuNo,
        materialDescribe,
        customerSkuNo: customerMaterialNo,
        customerSkuName: customerMaterialName,
        customerSkuUnitCount,
        customerSkuUnit: customerMaterialUnit,
        customerSkuSpecification: customerSpecificationModel,
        index: 1
      }
      return sku
    }
  },
  watch: {
    skuDetail(newVal) {
      if (newVal) {
        this.sku = {
          ...this.sku,
          ...newVal
        }
        this.initRate()
        this.searchCustomerMaterialRelation()
        this.setRules()
      }
    }
  },
  created () {
    this.setRules()
    console.log(this.rules)
    // 灰度，在白名单内走新的查询物料关系接口，否则走老的查询接口
    if (this.searchSkuSwitch) {
      this.initRate()
      this.searchCustomerMaterialRelation()
    } else {
      this.search()
    }
  },
  methods: {
    setRules () {
      // 无sku时，只有商品描述、数量、单价必填
      if (!this.sku.skuNo) {
        this.rules = nullSkuRules
        this.rules.quantity = { required: false }
      } else {
        this.rules = defaultRules;
      }
      if (this.isTaxedCustomer) {
        this.rules = {
          ...this.rules,
          taxPrice: [
            { required: true, message: '请选择含税单价', trigger: 'blur' }
          ]
        }
      } else {
        this.rules = {
          ...this.rules,
          freeTaxPrice: [
            { required: true, message: '请选择未税单价', trigger: 'blur' }
          ]
        }
      }
      if (this.orderType === 'Z018') {
        this.rules = {
          ...this.rules,
          rentalDueDate: [
            { required: true, message: '请选择租赁截止日期', trigger: 'blur' }
          ],
          fixedAssetsId: [
            { required: true, message: '请输入固定资产编号', trigger: 'blur' }
          ]
        }
      }
      if (this.orderType === 'Z014') {
        this.rules = {
          ...this.rules,
          customerDate: [
            { required: true, message: '请选择客户期望送达日期', trigger: 'blur' }
          ]
        }
      }
    },
    getKey () {
      const { companycode, categorycode } = this.$route.params
      return `${companycode}_${categorycode}`
    },
    focusDatePicker () {
      const specifiedReceiptDayOfWeek = Array.isArray(this.orderData?.specifiedReceiptDayOfWeek) ? this.orderData?.specifiedReceiptDayOfWeek?.filter(day => !!day) : this.orderData?.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day)
      const receiptTimeCategory = this.orderData.receiptTimeCategory
      if ((receiptTimeCategory === 'Z' || receiptTimeCategory === false) && specifiedReceiptDayOfWeek?.length && specifiedReceiptDayOfWeek?.every(day => !['0', '01', '02', '03', '04', '05'].includes(day))) {
        this.$message.warning('无可选日期，请修改客户指定日收货或工作日与周末均可收货!')
      }
    },
    handleCustomChange (val) {
      console.log(val)
    },
    changeMaterial (val) {
      const foundItem = this.materialList.find(item => item.customerSkuNo === val)
      if (foundItem) {
        const { customerSkuSpecification, customerSkuName, customerSkuUnit,
          skuUnitCount, customerSkuUnitCount } = foundItem
        this.sku.customerSpecificationModel = customerSkuSpecification
        this.sku.customerMaterialName = customerSkuName
        this.sku.customerMaterialUnit = customerSkuUnit
        this.sku.customerMaterialQuantity = customerSkuUnitCount
        if (customerSkuUnitCount != null && skuUnitCount != null) {
          this.rate = `${skuUnitCount}:${customerSkuUnitCount}`
          const quantity = parseFloat(this.sku.quantity)
          this.sku.customerMaterialQuantity = formatPrice((quantity * customerSkuUnitCount) / skuUnitCount, 6)
        }
      } else {
        this.rate = ''
      }
    },
    async searchCustomerMaterialRelation() {
      try {
        this.loading = true;
        const { skuNo } = this.sku
        const { customerNumber } = this.orderData.customer
        const data = [{
          customerCode: customerNumber,
          zkhSkuNo: skuNo
          }]
        const res = await searchCustomerMaterialRelation({ queryList: data });
        if (res.code === 200) {
          if (Array.isArray(res.data) && res.data.length > 0) {
            this.customerMaterialTableData = res.data
            // if (this.customerMaterialTableData.length === 1) {
            //   const [item] = this.customerMaterialTableData
            //   this.initMaterialInfo(item)
            // }
          } else {
            this.customerMaterialTableData = []
          }
        } else {
          this.customerMaterialTableData = []
        }
      } catch (err) {
        console.log(err)
      } finally {
        this.loading = false;
      }
    },
    search () {
      const { skuNo } = this.sku
      const { customerNumber } = this.orderData.customer
      if (customerNumber) {
        const params = {
          customerNo: customerNumber,
          skuNo: skuNo,
          current: 1,
          size: 20
        }
        searchMaterial(params).then(result => {
          if (result.code === 200) {
            if (Array.isArray(result.data) && result.data.length > 0) {
              this.materialList = result.data
              let data = result.data[0]
              if (this.sku.customerMaterialNo) {
                const foundItem = result.data.find(item =>
                  item.customerSkuNo === this.sku.customerMaterialNo)
                if (foundItem) {
                  data = foundItem
                }
              }
              const {
                customerSkuName, customerSkuNo, customerSkuSpecification,
                customerSkuUnit, customerSkuUnitCount, skuUnitCount
              } = data
              if (!this.sku.customerMaterialNo) {
                this.sku.customerMaterialNo = customerSkuNo
              }
              if (!this.sku.customerMaterialName) {
                this.sku.customerMaterialName = customerSkuName
              }
              if (!this.sku.customerSpecificationModel) {
                this.sku.customerSpecificationModel = customerSkuSpecification
              }
              if (!this.sku.customerMaterialUnit) {
                this.sku.customerMaterialUnit = customerSkuUnit
              }
              if (!this.sku.customerMaterialQuantity && customerSkuUnitCount != null && skuUnitCount != null) {
                this.rate = `${skuUnitCount}:${customerSkuUnitCount}`
                const quantity = parseFloat(this.sku.quantity)
                this.sku.customerMaterialQuantity = formatPrice((quantity * customerSkuUnitCount) / skuUnitCount, 6)
              }
            } else {
              this.materialList = []
            }
          } else {
            this.$message.error({
              message: result.msg
            })
          }
        })
      }
    },
    isFactoryDisable (row, code) {
      if (row && code) {
        const { factoryProductPriceVOMap } = row
        const c = parseInt(code, 10)
        return !(factoryProductPriceVOMap && factoryProductPriceVOMap[c])
      }
      return true
    },
    changePosition (value) {
      // const { code } = value
      // if (this.sku.mtart !== 'Z002' && this.orderType !== 'Z018' && this.orderType !== 'Z008' && this.orderType !== 'Z009') {
      //   this.sku['directDeliverySupplier'] = (code && endsWith(String(code), '04')) ? '1' : '0'
      // }
    },
    changeQuantityUnit (value) {
      const { conversion } = value
      const { quantity } = this.sku
      let oldConversion = 1
      if (this.currentQuantityUnit) {
        oldConversion = this.currentQuantityUnit.conversion
      }
      if (conversion) {
        this.sku.quantity = formatPrice(oldConversion * quantity / conversion, 3)
      }
      this.currentQuantityUnit = value
    },
    changeFactory (value) {
      // 震坤行发货 sim 库位
      this.setSimPosition(this.sku)
      const facCode = String(value.code)
      const positionList = this.dictList['position'].filter(item => {
        if (isServiceOrder(this.orderType) || this.orderType === 'Z018') {
          return item.parentCode === value.code && endsWith(String(item.code), '04')
        }
        return item.parentCode === value.code && item.code !== -1
      })
      if (
        this.orderType !== 'Z018' &&
        this.orderType !== 'Z009' &&
        this.orderType !== 'Z014' &&
        this.orderType !== 'ZEV1' &&
        this.orderType !== 'ZEV2' &&
        this.orderType !== 'ZEV3' &&
        this.orderType !== 'ZEV4' &&
        this.sku.directDeliverySupplier === '2') {
        positionList.unshift({
          code: -1,
          name: '自动挑仓'
        })
        this.sku.position = -1
      } else {
        this.sku.position = ''
      }
      this.sku.positionList = positionList
      let hasTaxRate = true
      if (this.sku.factoryProductPriceVOMap && this.sku.factoryProductPriceVOMap[parseInt(facCode, 10)]) {
        const { taxRate } = this.sku.factoryProductPriceVOMap[facCode]
        let { suggestPrice } = this.sku.factoryProductPriceVOMap[facCode]
        if (!suggestPrice) {
          suggestPrice = 0
        }
        if (taxRate != null) {
          if (this.isFree) {
            this.sku.taxPrice = 0
            this.sku.freeTaxPrice = 0
          } else {
            this.sku.taxPrice = formatPrice(suggestPrice, 6)
            this.sku.freeTaxPrice = formatPrice(suggestPrice / (1 + taxRate * 0.01), 6)
          }
          const { freeTaxPrice, quantity } = this.sku
          // 税率
          this.sku.taxRate = (taxRate * 0.01).toFixed(2)
          this.sku.taxAmount = freeTaxPrice ? formatPrice(freeTaxPrice * quantity * taxRate) : 0
        } else {
          hasTaxRate = false
        }
      } else {
        hasTaxRate = false
        this.$alert('该sku在该工厂没有配置税率，无法下单，请在crm中进行维护或更换其他工厂', '错误')
      }
      if (!hasTaxRate) {
        this.sku.taxedPrice = 0
        this.sku.freeTaxPrice = 0
        this.sku.taxRate = 0
        this.sku.taxAmount = 0
      }
    },
    async setSimPosition(row) {
      const data = {
        skuSet: [row.skuNo],
        factorySet: [row.factory.code],
        positionScope: 1
      }
      const res = await getDeliveryWarehouse(data)
      if (res && res.status === 200 && Array.isArray(res.result)) {
        const posList = []
        const codeMap = {}
        res.result.reduce((prev, next) => {
          prev.push(...next.allPosition)
          return prev;
        }, []).forEach(item => {
          const key = `${item.factory}_${item.code}_${item.name}`
          if (!codeMap[key]) {
            codeMap[key] = true
            posList.push(item)
          }
        })
        console.log(posList)
        // this.sku.simPositionList = posList
        this.$set(this.sku, 'simPositionList', posList)
      }
      console.log(res)
    },
    async changeDirectDeliverySupplier (value) {
      if (value === '1') {
        const positionList = this.sku.positionList.filter(item => endsWith(String(item.code), '04'))
        this.sku.positionList = positionList
        this.sku.position = ''
      } else {
        if (this.sku.factory && this.sku.factory.code) {
          // const facCode = String(this.sku.factory.code)
          const positionList = this.dictList['position'].filter(item => {
            const code = this.sku.factory.code
            if (isServiceOrder(this.orderType) || this.sku.mtart === 'Z002') {
              return item.parentCode === code && endsWith(String(item.code), '04')
            }
            return item.parentCode === code && item.code !== -1
          })
          if (value === '2' &&
            !isForecastOrder(this.orderType) &&
            this.orderType !== 'Z014' &&
            this.orderType !== 'ZEV1' &&
            this.orderType !== 'ZEV2' &&
            this.orderType !== 'ZEV3' &&
            this.orderType !== 'ZEV4') {
            positionList.unshift({
              code: -1,
              name: '自动挑仓'
            })
            this.sku.position = -1
          } else {
            this.sku.position = ''
          }
          this.sku.positionList = positionList
        }
        if (value === '0') {
          if (!this.sku.simPositionList || !this.sku.simPositionList.length) {
            // 震坤行发货 sim 库位
            this.setSimPosition(this.sku)
          }
          this.sku.position = ''
        }
      }
    },
    taxedChange (value) {
      const taxRate = parseFloat(this.sku.taxRate)
      if (!isNaN(taxRate)) {
        this.sku.freeTaxPrice = formatPrice(value / (1 + taxRate))
        this.sku.taxAmount = formatPrice(this.sku.freeTaxPrice * taxRate * this.sku.quantity)
      }
    },
    unTaxedChange (value) {
      const taxRate = parseFloat(this.sku.taxRate)
      if (!isNaN(taxRate)) {
        this.sku.taxPrice = formatPrice(value * (1 + taxRate))
        this.sku.taxAmount = formatPrice(value * taxRate * this.sku.quantity)
      }
    },
    submit () {
      this.$refs['goodsDetailForm'].validate(valid => {
        console.log('valid');
        if (valid) {
          const { skuNo, quantity, customerMaterialQuantity } = this.sku;
          if (!skuNo && (!quantity || parseFloat(quantity) === 0) && (!customerMaterialQuantity || parseFloat(customerMaterialQuantity) === 0)) {
            this.$message({
              message: '数量和客户物料数量不能同时为0！',
              type: 'error'
            })
            return
          }
          // customerMaterialName:客户物料名称
          // customerMaterialNo  客户物料号
          // customerSpecificationModel  客户规格型号
          // customerOrderNo 客户行号
          let { needScrapingCode } = this.sku
          const toHandleArr = ['customerMaterialNo', 'customerMaterialName', 'customerSpecificationModel', 'customerOrderNo']
          toHandleArr.forEach((key) => {
            this.sku[key] = this.sku[key] && this.sku[key].trim().replace(/(?:\r\n|\r|\n)/g, '')
          })
          this.$emit('close')
          this.$store.dispatch('orderGoods/updateGoodsDetail', {
            sku: {
              ...this.sku,
              needScrapingCode: needScrapingCode ? 'X' : 'Z'
            },
            index: this.index,
            key: this.itemKey,
            orderData: this.orderData
          })
          this.changeItemUrgent()
          this.changeItemCustomerDateSensitive()
          this.$emit('submit')
        }
      })
    },
    cancel (form) {
      this.$refs[form].resetFields()
      this.$emit('close')
    },
    changeItemUrgent () {
      this.$emit('changeUrgent', {
        index: this.index,
        data: this.sku.urgent
      })
    },
    changeItemCustomerDateSensitive () {
      this.$emit('changeCustomerDateSensitive', {
        index: this.index,
        data: this.sku.customerDateSensitive
      })
    },
    handleQuantityChange (val) {
      if (this.searchSkuSwitch) {
        if (val !== null && this.rate) {
          this.sku.customerMaterialQuantity = formatPrice(val * this.rate, 6);
        }
      } else {
        this.changeQuantity(val)
      }
      const { freeTaxPrice, quantity, taxRate } = this.sku
      this.sku.taxAmount = freeTaxPrice ? formatPrice(freeTaxPrice * quantity * taxRate, 6) : 0
    },
    changeQuantity (val) {
      if (val != null && this.rate) {
        const arr = this.rate.split(':')
        if (arr && arr.length === 2) {
          const rate1 = parseInt(arr[0], 10)
          const rate2 = parseInt(arr[1], 10)
          if (rate1) {
            this.sku.customerMaterialQuantity = formatPrice(val * rate2 / rate1, 6)
          }
        }
      }
    },
    handleMaterialClick (item) {
      // this.initMaterialInfo(item)
      this.sku.customerMaterialNo = item.customerMaterialNo;
      this.sku.customerSpecificationModel = item.customerMaterialSpecification;
      this.sku.customerMaterialName = item.customerMaterialName;
      this.sku.customerMaterialUnit = item.customerMaterialUnit;
      if (
        item.customerMaterialStandardQuantity &&
        item.zkhSkuStandardQuantity &&
        parseFloat(item.customerMaterialStandardQuantity) !== 0 &&
        parseFloat(item.zkhSkuStandardQuantity) !== 0
      ) {
        this.rate = formatPrice(parseFloat(item.customerMaterialStandardQuantity) / parseFloat(item.zkhSkuStandardQuantity), 6);
        this.sku.customerMaterialQuantity = formatPrice(this.sku.quantity * this.rate, 6);
      } else {
        this.rate = ''
      }
    },
    initMaterialInfo (item) {
      if (!this.sku.customerMaterialNo) {
        this.sku.customerMaterialNo = item.customerMaterialNo;
      }
      if (!this.sku.customerSpecificationModel) {
        this.sku.customerSpecificationModel = item.customerMaterialSpecification;
      }
      if (!this.sku.customerMaterialName) {
        this.sku.customerMaterialName = item.customerMaterialName;
      }
      if (!this.sku.customerMaterialUnit) {
        this.sku.customerMaterialUnit = item.customerMaterialUnit;
      }
      if (
        !this.sku.customerMaterialQuantity &&
        item.customerMaterialStandardQuantity &&
        item.zkhSkuStandardQuantity &&
        parseFloat(item.customerMaterialStandardQuantity) !== 0 &&
        parseFloat(item.zkhSkuStandardQuantity) !== 0
      ) {
        this.rate = formatPrice(parseFloat(item.customerMaterialStandardQuantity) / parseFloat(item.zkhSkuStandardQuantity), 6);
        this.sku.customerMaterialQuantity = formatPrice(this.sku.quantity * this.rate, 6);
      } else {
        this.rate = ''
      }
    },
    initRate () {
      const { skuUnitCount, customerSkuUnitCount } = this.sku;
      if (
        skuUnitCount && customerSkuUnitCount &&
        parseFloat(skuUnitCount) !== 0 &&
        parseFloat(customerSkuUnitCount) !== 0
        ) {
          this.rate = formatPrice(parseFloat(customerSkuUnitCount) / parseFloat(skuUnitCount), 6);
        }
        if (!this.sku.customerMaterialQuantity) {
          this.sku.customerMaterialQuantity = formatPrice(this.sku.quantity * this.rate, 6);
      }
    },
    fetchCustomerBySpart (productGroup, callback) {
      const { customerNumber } = this.orderData
      const { salesOrganization, distributionChannel } = this.orderData.selectedSalesRange || {}
      if (productGroup != null && salesOrganization && distributionChannel && customerNumber) {
        getClientDetail(customerNumber, distributionChannel, productGroup, salesOrganization).then(res => {
          if (res && res.code === 200) {
            this.$emit('updateCustomer', res.data)
            callback && callback()
          }
        })
      }
    },
    async handleDiffMaterialRelation (type, obj) {
      try {
        if (type === 'cancel') {
          this.sku = {
            ...this.sku,
            skuNo: this.oldSku.skuNo || ''
          }
        } else {
          await this.handleSku()
          this.sku.customerMaterialNo = obj.customerMaterialNo;
          this.sku.customerSpecificationModel = obj.customerSpecificationModel;
          this.sku.customerMaterialName = obj.customerMaterialName;
          this.sku.customerMaterialUnit = obj.customerMaterialUnit;
        }
        this.showDiffDialog = false;
      } catch (err) {
        console.log(err)
      }
    },
    isDiffCustomerRelation (result, detail) {
      this.resultMaterialObj = {
        customerMaterialNo: result.customerMaterialNo,
        customerMaterialName: result.customerMaterialName,
        customerMaterialUnit: result.customerMaterialUnit,
        customerSpecificationModel: result.customerSpecificationModel
      }
      this.inputMaterialObj = {
        customerMaterialNo: detail.customerMaterialNo,
        customerMaterialName: detail.customerMaterialName,
        customerMaterialUnit: detail.customerMaterialUnit,
        customerSpecificationModel: detail.customerSpecificationModel
      }
      return isDiffCustomerRelation(this.resultMaterialObj, this.inputMaterialObj)
    },
    handleSku () {
      if (this.formatedSku) {
        this.sku = {
          ...this.sku,
          ...this.formatedSku,
          quantity: this.oldSku.quantity,
          customerMaterialQuantity: this.oldSku.customerMaterialQuantity
        }
        this.initRate()
        this.searchCustomerMaterialRelation()
        this.setRules()
        if (this.skus && this.skus.length === 1 && this.formatedSku.productGroup) {
          this.fetchCustomerBySpart(this.formatedSku.productGroup)
        }
      }
    },
    handleDiff () {
      // 判断查出的客户物料关系和页面手动输入的是否一致，不一致则弹窗提醒
      const isDiff = this.isDiffCustomerRelation(this.formatedSku, this.sku)
      if (isDiff) {
        this.showDiffDialog = true;
      } else {
        this.handleSku();
      }
    },
    addSkuToTable (currentSelectSku) {
      console.log(currentSelectSku)
      if (currentSelectSku) {
        this.loading = true;
        // 将下拉框失焦，避免下拉框展开
        setTimeout(() => {
          this.$refs.selectSku.$refs.select.blur()
        }, 80)
        this.$store.dispatch('orderGoods/addGoods', {
            goods: currentSelectSku,
            salesRange: this.orderData.selectedSalesRange,
            orderData: this.orderData,
            orderType: this.orderType,
            company: this.company,
            factoryList: this.factoryList,
            skus: this.skus,
            key: this.getKey(),
            selectedSearchItem: 1,
            index: this.index
          }).then(async res => {
            const { result, foundItem } = res
            if (result && result.code === 200) {
              if (result.hasTaxRate === false) {
                this.$alert('该sku在该工厂没有对应的税率，无法下单，请在crm中进行维护或更换其他工厂', '错误')
                return
              }
            }
            if (result && result.code !== 200 && result.msg) {
              this.$alert(result.msg, '错误')
              return
            }
            this.oldSku = this.sku
            this.sku = {
              ...this.sku,
              skuNo: currentSelectSku.skuNo
            }
            this.formatedSku = foundItem;
            if (currentSelectSku.dataSource !== '商品中心') {
              this.handleDiff();
            } else {
              // 来源为商品中心，查询是否有客户物料关系，有则比较不同，没有则按不覆盖处理
              const data = {
                sku: currentSelectSku.skuNo,
                customerCode: this.orderData.customer?.customerNumber
              };
              const res = await accurateQuery(data);
              if (res.data.length > 0) {
                this.handleDiff();
              } else {
                await this.handleSku();
                const {
                  customerMaterialNo,
                  customerSpecificationModel,
                  customerMaterialName,
                  customerMaterialUnit
                } = this.oldSku;
                this.sku.customerMaterialNo = customerMaterialNo;
                this.sku.customerSpecificationModel = customerSpecificationModel;
                this.sku.customerMaterialName = customerMaterialName;
                this.sku.customerMaterialUnit = customerMaterialUnit;
              }
            }
          })
          .catch(err => console.log(err))
          .finally(() => {
            this.loading = false;
          })
      } else {
        this.sku.skuNo = ''
      }
    }
  }
}
</script>
<style scoped lang="scss">
.tax {
  display: flex;
  align-items: flex-start;
  .el-link {
    margin-left: 5px;
    white-space: nowrap;
    position: relative;
    top: 10px;
  }
}
.el-select,
.el-date-editor {
  width: 100%;
}
.ba-row-center{
  display: flex;
  align-items: center;
  justify-content: center;
}
.label {
  position: relative;
}
</style>
