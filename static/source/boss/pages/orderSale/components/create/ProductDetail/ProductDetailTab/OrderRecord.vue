<template>
  <div>
    <el-table :data="tableData" style="width: 100%" max-height="600px">
      <el-table-column prop="soNo" label="OMS订单号" width="100" align="center" />
      <el-table-column prop="sapOrderNo" label="SAP订单号" width="100" align="center" />
      <el-table-column prop="quantity" label="该商品数量" width="150" align="center" />
      <el-table-column prop="conditionType" label="含税/未税" width="150" align="center" />
      <el-table-column prop="unitPrice" label="单价" width="100" align="center" />
      <el-table-column prop="seller" label="销售" width="100" align="center" />
      <el-table-column prop="saleTime" label="订单时间" width="100" align="center" />
      <el-table-column prop="type" label="订单类型" width="100" align="center" />
    </el-table>

    <div class="ba-row-end" style="margin-top:20px">
      <el-pagination
        :current-page.sync="current"
        background
        layout="prev, pager, next"
        :total="total"
        @current-change="pageChange"
      />
    </div>

    <div class="ba-row-center btnGroup">
      <el-button plain type="primary" @click="$emit('close')">关闭</el-button>
    </div>
  </div>
</template>
<script>
import * as sellOrder from '@/api/orderSale'
export default {
  props: ['skuDetail', 'customerDetail'],
  data () {
    return {
      tableData: [],
      total: 0,
      current: 1
    }
  },
  created () {
    this.pageChange(this.current)
  },
  methods: {
    pageChange (page) {
      sellOrder
        .orderRecordList(
          this.customerDetail.customerNumber,
          this.skuDetail.skuNo,
          this.current
        )
        .then(result => {
          if (result.code === 200 && result.data) {
            this.total = result.data.total
            this.current = result.data.current
            this.tableData = result.data.records
          }
        })
    }
  }
}
</script>
<style scoped lang="scss">
.btnGroup {
  margin: 20px 0;
}
</style>
