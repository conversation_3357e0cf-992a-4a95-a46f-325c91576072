<template>
  <div class="OrderDetailHeader ba-row-between">
    <p>
      <span>当前创建订单为：</span>
      <span>{{ company }}&nbsp;&nbsp;&nbsp;&nbsp;{{ orderType }}</span>
    </p>
    <el-button type="primary" plain disabled @click="showDialog=true">选择参考预报单</el-button>

    <el-dialog width="600px" title="选择参考预报单" :visible.sync="showDialog" :show-close="false">
      <el-form :model="clientInfo" :inline="false" label-width="0">
        <el-form-item>
          <el-input v-model="spaNo" placeholder="请输入预测型销售订单的SAP订单号" />
        </el-form-item>
      </el-form>
      <div class="DialogFooter">
        <el-button type="primary" @click="confirm">确认创建</el-button>
        <el-button type="info" plain @click="showDialog=false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: ['company', 'orderType'],
  data () {
    return {
      showDialog: false,
      clientInfo: {},
      spaNo: ''
    }
  },
  methods: {
    confirm () {
      this.showDialog = false
      // 获取到数据
      this.$emit('confirm', null)
    }
  }
}
</script>
<style scoped lang="scss">
.ba-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.OrderDetailHeader {
  margin-bottom: 20px;
  p {
    > span:first-child {
      font-size: 14px;
    }
    > span:last-child {
      font-size: 20px;
    }
  }
}

.DialogFooter {
  text-align: center;
}
</style>
