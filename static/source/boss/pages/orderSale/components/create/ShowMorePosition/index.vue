<template>
  <el-dialog
    :visible.sync="showDlg"
    :show-close="false"
    width="420px"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form
      ref="form"
      label-position="left"
      label-width="140px"
    >
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="请选择你要的仓位：">
            <el-select v-model="position" @change="handleChange" filterable default-first-option>
              <el-option
                v-for="item in rowDetail.positionList"
                :key="item.code"
                :label="(item.code !== -1 ? item.code : '')+item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="btnGroup ba-row-center">
      <el-button type="primary" @click="submit">确认选择</el-button>
      <el-button type="primary" plain @click="$emit('update:showDialog', false)">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: ['showDialog', 'rowDetail'],
  data () {
    return {
      position: '',
      currentPosition: null
    }
  },
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    handleChange (code) {
      const find = this.rowDetail.positionList.find(item => item.code === code)
      this.currentPosition = find
    },
    submit () {
      console.log(this.position)
      console.log(this.currentPosition)
      this.rowDetail.position = this.position
      try {
        if (!this.rowDetail.simPositionList.find(item => item.code === this.currentPosition.code)) {
          this.rowDetail.simPositionList.unshift(this.currentPosition)
        }
      } catch (err) {}
      this.$emit('update:showDialog', false)
      this.position = null
    }
  }
}
</script>
