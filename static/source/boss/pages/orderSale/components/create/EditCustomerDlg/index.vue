<template>
  <el-dialog
    title="客户详情"
    :visible.sync="showDlg"
    :show-close="false"
    width="800px"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form
      ref="form"
      :model="validateFormValue"
      :rules="rules"
      label-position="left"
      label-width="110px"
    >
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="售达方">
            <el-input v-model="validateFormValue.customerName" :disabled="!vFlag" maxlength="35"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="送达方" prop="send">
            <el-input v-model="customerDetail.customerName" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="收票方">
            <el-input v-model="customerDetail.customerName" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款方" prop="send">
            <el-input v-model="customerDetail.customerName" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="销售">
            <el-select v-model="validateFormValue.sellerId" placeholder="选择销售">
              <el-option
                v-for="item in sellerList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客服" prop="send">
            <el-select v-model="validateFormValue.customerServiceId" placeholder="选择客服">
              <el-option
                v-for="item in customerList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="客户来源">
            <el-input v-model="customerDetail.customerSourceName" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款条件" prop="paymentTerm">
            <el-select
              v-model="validateFormValue.paymentTerm"
              placeholder="请选择付款条件"
              disabled
            >
              <el-option
                v-for="item in paymentTerms"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="自营配送">
            <el-select v-model="validateFormValue.serviceCenterSelfTransport" placeholder="是否自营配送">
              <el-option
                v-for="item in dictList['serviceCenterSelfTransport']"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="含税/未税">
            <el-input :value="{'1': '含税' , '0': '未税'}[customerDetail.isTax]" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="销售办事处">
            <el-input :value="salesOfficeName" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="销售组">
            <el-input :value="salesGroupName" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="接受供应商直发" prop="acceptSupplierDelivery">
            <el-select v-model="validateFormValue.acceptSupplierDelivery" placeholder="是否接受供应商直发">
              <el-option label="是" :value="1" />
              <el-option label="否" :value="0" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="客户订单号">
            <el-input v-model="validateFormValue.orderNo" placeholder="客户订单号" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12">
          <el-form-item label="客户期望日期">
            <el-date-picker v-model="validateFormValue.customerReferenceDate" value-format="yyyy-MM-dd" type="date" placeholder="选择客户期望日期" />
          </el-form-item>
        </el-col> -->
      </el-row>
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="销售范围" prop="selectedSalesRange">
            <el-select
              v-model="validateFormValue.selectedSalesRange"
              value-key="idx"
              placeholder="请选择销售范围"
            >
              <el-option
                v-for="item in saleOrgList.filter(item => companycode && item.data && item.data.salesOrganization && (item.data.salesOrganization.substring(0,2)===companycode.substring(0, 2)))"
                :key="item.key"
                :label="item.value"
                :value="item.data"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="btnGroup ba-row-center">
      <el-button type="primary" @click="submit">确认保存</el-button>
      <el-button type="primary" plain @click="cancel('form')">取消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { getClientDetail } from '@/api/orderSale'

export default {
  props: ['showDialog', 'orderData', 'customerDetail', 'selectedSalesRange', 'vFlag'],
  data () {
    const { companycode } = this.$route.params
    return {
      companycode,
      serviceCenterSelfTransportOptions: [
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      validateFormValue: {
        // 客户订单号
        orderNo: '',
        // 客户期望日期
        customerReferenceDate: '',
        // 是否自营配送
        serviceCenterSelfTransport: '',
        acceptSupplierDelivery: '',
        selectedSalesRange: null,
        paymentTerm: '',
        customerServiceId: '',
        sellerId: ''
      },
      rules: {
        selectedSalesRange: [
          { required: true, message: '请选择销售范围', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    saleOrgList () {
      const { customer } = this.orderData || {}
      return customer && customer.saleOrgList
        ? customer.saleOrgList.map((item, idx) => {
          const { salesOrganization, productGroup, distributionChannel, salesOrganizationName, distributionChannelName, productGroupName } = item
          return {
            data: {
              idx,
              ...item
            },
            key: `${salesOrganization}-${productGroup}-${distributionChannel}`,
            value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`
          }
        })
        : []
    },
    paymentTerms () {
      const dictList = this.$store.state.orderCommon.dictList
      return dictList['paymentTerms'] || []
    },
    salesGroupName () {
      const dictList = this.$store.state.orderCommon.dictList
      if (this.customerDetail && dictList) {
        const { salesGroup } = this.customerDetail
        if (dictList['salesGroup']) {
          const group = dictList['salesGroup'].find(item => salesGroup === item.parentCode)
          if (group) {
            return group.name
          }
        }
      }
      return ''
    },
    salesOfficeName () {
      const dictList = this.$store.state.orderCommon.dictList
      if (this.customerDetail && dictList) {
        const { salesOffice } = this.customerDetail
        if (dictList['salesOffice']) {
          const office = dictList['salesOffice'].find(item => salesOffice === item.parentCode)
          if (office) {
            return office.name
          }
        }
      }
      return ''
    },
    sellerList () {
      if (this.customerDetail && this.customerDetail.sellerMap) {
        return Object.keys(this.customerDetail.sellerMap).map(k => {
          return {
            code: k,
            name: this.customerDetail.sellerMap[k]
              ? this.customerDetail.sellerMap[k].userName
              : ''
          }
        })
      }
      return []
    },
    customerList () {
      if (this.customerDetail && this.customerDetail.customerServiceMap) {
        return Object.keys(this.customerDetail.customerServiceMap).map(k => {
          return {
            code: k,
            name: this.customerDetail.customerServiceMap[k]
              ? this.customerDetail.customerServiceMap[k].userName
              : ''
          }
        })
      }
      return []
    },
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  watch: {
    customerDetail: function (val) {
      this.init(val)
    }
  },
  created () {
    this.validateFormValue = {
      ...this.orderData
    }
    this.init(this.customerDetail)
  },
  methods: {
    init (customerDetail) {
      let paymentTerm = this.orderData.paymentTerm
      let serviceCenterSelfTransport = this.orderData.serviceCenterSelfTransport
      if (!paymentTerm && customerDetail) {
        paymentTerm = customerDetail.paymentTermCode
      }
      if (!serviceCenterSelfTransport && customerDetail) {
        serviceCenterSelfTransport = customerDetail.serviceCenterSelfTransport
      }
      this.validateFormValue.paymentTerm = paymentTerm
      this.validateFormValue.serviceCenterSelfTransport = serviceCenterSelfTransport
    },
    submit () {
      this.$refs['form'].validate(async valid => {
        if (valid) {
          // 如果销售范围没有变化，不更新客户详情
          const { selectedSalesRange, customerServiceId, sellerId } = this.validateFormValue
          const originalSalesRange = this.selectedSalesRange
          let customerDetail = null
          if (this.orderData.customer && !(originalSalesRange && selectedSalesRange && originalSalesRange.idx === selectedSalesRange.idx)) {
            const { customerNumber } = this.orderData.customer
            const { salesOrganization, productGroup, distributionChannel } = selectedSalesRange
            const res = await getClientDetail(customerNumber, distributionChannel, productGroup, salesOrganization)
            if (res && res.code === 200) {
              customerDetail = res.data
            }
          }
          const { sellerMap, customerServiceMap } = this.customerDetail
          this.$emit('update:showDialog', false)
          const customerData = {
            ...this.validateFormValue,
            customerDetail,
            customerName: this.validateFormValue.customerName,
            customerServiceName: customerServiceId ? customerServiceMap[customerServiceId].userName : '',
            sellerName: sellerId ? sellerMap[sellerId].userName : ''
          }
          this.$emit('submitCustomer', customerData)
          this.$emit('getDeliveryTime')
        } else {
          return false
        }
      })
    },
    cancel (form) {
      this.$emit('update:showDialog', false)
      this.$refs[form].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.el-select,
.el-date-editor {
  width: 100%;
}
</style>
