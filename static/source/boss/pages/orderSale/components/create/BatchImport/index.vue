<template>
  <div class="batch-import">
    <div>
      请在输入框内输入sku编码以及对应数量，用空格隔开。一行对应一种商品，请注意换行。
      <span class="strong">支持直接在Excel中复制粘贴至输入框内</span>，快速导入商品，最多不超过100行。
    </div>
    <div class="batch-import-input-row">
      <el-input
        :autosize="{ minRows: 4, maxRows: 6}"
        class="batch-import-input"
        type="textarea"
        placeholder="例如：sku编码 数量"
        v-model="text"
        resize="none"
        clearable
      >
      </el-input>
    </div>
    <div class="batch-import-btn-row">
      <el-button class="batch-import-btn" type="primary" @click="handleImport">确认导入</el-button>
      <el-button class="batch-import-btn" @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>
<script>
import uniq from 'lodash/uniq'
import { uploadSku } from '@/api/orderSale'
import { requestWithLoading } from '@/pages/orderSale/utils'

export default {
  props: ['orderType', 'salesOrganization', 'salesRange', 'orderData'],
  data () {
    return {
      text: ''
    }
  },
  methods: {
    handleCancel () {
      this.$emit('close')
    },
    handleImport () {
      if (this.text) {
        const uploadSkuInfoVOList = []
        const rows = this.text.split('\n')
        if (rows && rows.length > 0) {
          rows.forEach(row => {
            const strs = row.trim()
            if (strs) {
              const strList = strs.split(/\s+/)
              if (strList && strList.length > 0) {
                const arr = []
                strList.forEach(str => {
                  if (str) {
                    const sr = str.trim()
                    if (sr) {
                      arr.push(sr)
                    }
                  }
                })
                if (arr && arr.length <= 2) {
                  const rowData = {
                    skuNo: arr[0]
                  }
                  const quantity = arr[1] ? arr[1].trim() : ''
                  rowData['quantity'] = quantity.replace(/,/, '') || 0
                  uploadSkuInfoVOList.push(rowData)
                }
              }
            }
          })
        }
        const total = uploadSkuInfoVOList.length
        if (total && this.salesRange && this.orderData) {
          const { salesOrganization, distributionChannel, productGroup } = this.salesRange
          const { customerNumber, orderReason } = this.orderData
          requestWithLoading(this, uploadSku({
            customerNo: customerNumber,
            salesOrganization,
            distributionChannel,
            productGroup,
            orderType: this.orderType,
            uploadSkuInfoVOList
          }), data => {
            const { uploadSkuInfoDetailVOList, failSkuList, failMsg } = data
            const successCallback = () => {
              if (uploadSkuInfoDetailVOList && uploadSkuInfoDetailVOList.length > 0) {
                this.$emit('close')
                this.$emit('import', uploadSkuInfoDetailVOList)
              }
            }
            // 非订单原因：对账差异调整禁止添加客户限售商品
            if (failMsg && orderReason !== '038') {
              this.$alert(failMsg, '提示', {
                type: 'error'
              })
              return
            }
            if (failSkuList && failSkuList.length > 0) {
              const failLine = failSkuList.length
              const successLine = uploadSkuInfoDetailVOList.length
              const skus = uniq(failSkuList).join(',')
              this.$alert(`导入成功商品行数量为${successLine}行，失败数量为${failLine}行。` +
                `其中${skus}为无效商品或未维护建议销售价，无法添加`, '错误', {
                confirmButtonText: '确定',
                callback: action => {
                  successCallback()
                }
              })
            } else {
              this.$message.success(`成功导入${uploadSkuInfoDetailVOList.length}行`)
              successCallback()
            }
          })
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.batch-import {
  &-btn {
    width: 90px;
  }
  .batch-import-input-row {
    margin: 10px 0;
  }
  .batch-import-btn-row {
    margin: 10px auto;
    text-align: center;
  }
  .strong {
    font-weight: bold;
  }
}
</style>
