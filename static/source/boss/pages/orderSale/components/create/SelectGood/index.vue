<template>
  <div>
    <ShowMorePosition
      :show-dialog.sync="ShowMorePosition"
      :rowDetail="rowDetail"
    />
    <el-row :gutter="20">
      <el-col :span="12">
        <el-form-item
          class="search-row"
          required
          label="选择商品"
        >
          <el-select
            v-if="searchSkuSwitch"
            class="search-input"
            popper-class="scroll-option"
            v-model="currentSelectSku"
            filterable
            clearable
            remote
            reserve-keyword
            value-key="index"
            placeholder="请输入商品编号/名称"
            :remote-method="searchSkuList"
            :loading="skuList.isLoading"
          >
            <el-option
              v-for="(item, index) in skuList.options"
              :key="index + '_' + item.skuNo"
              :label="'【'+item.skuNo+'】'+ item.materialDescribe"
              :value="item"
              :disabled="index===0"
            >
              <div
                class="ba-row-start selectMeterialItem"
                :style="{fontWeight:index===0 ? 'bold' : 'normal'}"
              >
                <div>{{ item.skuNo }}</div>
                <div>{{ `${item.materialDescribe || ''}`}}</div>
                <div>{{ `${item.customerSkuNo || ''}`}}</div>
                <div>{{ `${item.customerSkuName || ''}`}}</div>
                <div>{{ `${item.customerSkuUnitCount || ''}`}}</div>
                <div>{{ `${item.customerSkuUnit || ''}`}}</div>
                <div>{{ `${item.customerSkuSpecification || ''}`}}</div>
                <el-button type="text" @click="toDataSource(item)">{{ `${item.dataSource || ''}`}}</el-button>
              </div>
            </el-option>
          </el-select>
          <el-select v-if="!searchSkuSwitch" class="search-item" v-model="selectedSearchItem" @change="handleConditionChange">
            <el-option :value="1" label="商品编号/名称"></el-option>
            <el-option :value="2" label="客户物料号"></el-option>
          </el-select>
          <el-select
            v-if="!searchSkuSwitch"
            class="search-input"
            v-model="currentSelectSku"
            filterable
            clearable
            remote
            reserve-keyword
            value-key="skuNo"
            placeholder="请输入商品编号/名称"
            :remote-method="searchSkuList"
            :loading="skuList.isLoading"
          >
            <el-option
              v-for="(item, index) in skuList.options"
              :key="selectedSearchItem===1?item.skuNo:item.customerSkuNo+'_'+item.skuNo"
              :label="'【'+item.skuNo+'】'+(selectedSearchItem===1?item.materialDescribe:item.skuName)"
              :value="item"
              :disabled="index===0"
            >
              <div
                class="ba-row-start selectSkuItem"
                :style="{fontWeight:index===0?'bold':'normal'}"
                v-if="selectedSearchItem===1"
              >
                <div>{{ item.skuNo }}</div>
                <div>{{ `${item.materialDescribe || ''}`}}</div>
              </div>
              <div
                class="ba-row-start selectCustomerItem"
                :style="{fontWeight:index===0?'bold':'normal'}"
                v-else
              >
                <div>{{ item.customerSkuNo }}</div>
                <div>{{ item.skuNo }}</div>
                <div>{{ `${item.skuName || ''}`}}</div>
              </div>
            </el-option>
          </el-select>
          <el-button type="primary" :disabled="!currentSelectSku" @click="addSkuToTable">确认添加</el-button>
          <el-button type="primary" @click="addNullSkuToTable">新增空行</el-button>
        </el-form-item>
      </el-col>
      <el-col :span="6" v-if="orderType==='Z001'">
        <el-form-item
          class="search-row"
          label="整单折扣金额"
        >
          <el-input-number
            class="discount-input"
            placeholder="整单折扣金额"
            v-model="discount"
            :min="0"
            :max="1000000000"
            :step="1"
            :precision="2"
            style="width: 240px;margin-right:10px"
          ></el-input-number>
          <el-button
            plain
            type="primary"
            style="margin-right:10px"
            :disabled="discount<0"
            @click="handleAssign"
          >
            确认分配
          </el-button>
          <el-tooltip
            effect="dark"
            content="确认分配后，系统将自动把整单折扣金额按照商品行的金额占比进行分配到每一行的折扣金额上"
            placement="top"
          >
            <i class="el-icon-info" />
          </el-tooltip>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="tableInfo">
      <div class="ba-row-between tableHeader">
        <div class="merchantInfo-row">
          <div>
            <span class="amount">折前订单含税金额：
              <span :class="{'currency':company==='2400'}">
                {{orderData.currencySymbol}}
              </span>
              {{ formatPrice(taxedTotalAmount, 6) }}
            </span>

            <span class="amount">折前订单未税金额：
              <span :class="{'currency':company==='2400'}">
                  {{orderData.currencySymbol}}
              </span>
              {{ formatPrice(untaxedTotalAmount, 6) }}
            </span>
          </div>
          <div>
            <span class="amount">折后订单含税金额：
              <span :class="{'currency':company==='2400'}">
                {{orderData.currencySymbol}}
              </span>
              {{ formatPrice(taxedDiscountTotal, 6) }}
            </span>
            <span class="amount">折后订单未税金额：
              <span :class="{'currency':company==='2400'}">
                 {{orderData.currencySymbol}}
              </span>
              {{ formatPrice(unTaxedDiscountTotal, 6) }}
            </span>
          </div>
        </div>
        <div>
          <el-button
            type="danger"
            plain
            :disabled="multipleSelection.length === 0"
            @click="removeMultiSku"
          >
            批量删除商品行
          </el-button>
          <el-button type="primary"
            :disabled="!salesRange"
            @click="showBatchImport=true"
          >
            快速导入商品
          </el-button>
          <el-button
            type="primary"
            style="margin-right:10px"
            @click="downloadTemplate"
          >
            下载导入模板
          </el-button>
          <el-upload
            class="upload"
            ref="upload"
            :action="`/api-opc/v1/excel/batchImportSku${uploadQueryUrl}`"
            :disabled="!salesRange"
            :multiple="false"
            :limit="1"
            :accept="acceptFileType.commonType"
            :show-file-list="true"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
          >
            <el-button size="small" type="primary">上传导入模板</el-button>
          </el-upload>
        </div>
      </div>
      <el-table
        ref="multipleTable"
        v-loading="tableLoading"
        :data="itemList"
        border
        max-height="300"
        @selection-change="tableSelectionChange"
      >
        <el-table-column type="selection" width="40" fixed="left" />
        <el-table-column label="项目行" width="100" align="center" prop="idx" fixed="left" />
        <el-table-column prop="skuNo" label="商品编号" width="100" align="center" fixed="left" />
        <el-table-column prop="materialDescribe" label="商品描述" width="300" align="center" />
        <el-table-column prop="factory" width="260" align="center">
          <template slot="header">
            <span>
              <OptRequiredRowTitle title="工厂" :isRequired="true" style="display:inline-block"/>
              <el-tooltip class="item"
                effect="dark" content="工厂置灰无法选择原因：该商品在该工厂下未配置税率"
                placement="top"
              >
                <i class="el-icon-info" />
              </el-tooltip>
            </span>
          </template>
          <template slot-scope="{row, $index}">
            <el-select
              v-model="row.factory"
              :class="{tabelCellError:!!!row.factory && row.skuNo}"
              value-key="code"
              style="width:100%"
              size="mini"
              :disabled="isFactoryDisabled"
              placeholder="请选择"
              @change="val=>changeRow(val,$index,'factory', row)"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.code"
                :label="(item.code > 0 ? item.code : '')+item.name"
                :value="item"
                :disabled="isFactoryDisable(row, item.code)"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" width="160" align="center">
          <template slot="header">
            <OptRequiredRowTitle title="数量" :isRequired="true" />
          </template>
          <template slot-scope="{row,$index}">
            <el-input-number
              v-model="row.quantity"
              size="mini"
              :class="{tabelCellError:row.quantity===0}"
              :min="0"
              :max="1000000000"
              :step="1"
              :precision="0"
              @change="val=>changeAmount(val,$index, row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="suggestPrice" label="建议含税销售价" width="160" align="center">
          <template slot-scope="{row}">
            <span>{{formatPrice(row.suggestPrice,6)}}</span>
          </template>
        </el-table-column>
        <!-- 含税和未税逻辑 -->
        <template v-if="isTaxedCustomer">
          <el-table-column prop="taxPrice" label="含税单价" width="160" align="center">
            <template slot="header">
              <OptRequiredRowTitle title="含税单价"
                :isRequired="!isFreeOrder(orderType)&&!isForecast"
              />
            </template>
            <template slot-scope="{row, $index}">
              <el-input-number
                v-model="row.taxPrice"
                size="mini"
                style="width:100%"
                :class="{tabelCellError:orderType!=='Z005'&&!row.taxPrice>0}"
                :min="0"
                :precision="6"
                :step="1"
                :disabled="isFreeOrder(orderType)"
                @change="val=>taxedPriceChange(val,$index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="未税单价" prop="freeTaxPrice" width="140" align="center">
            <template slot-scope="{row}">{{ formatPrice(row.freeTaxPrice) }}</template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column prop="taxPrice" label="含税单价" width="140" align="center">
            <template slot-scope="{row}">{{ formatPrice(row.taxPrice) }}</template>
          </el-table-column>
          <el-table-column prop="freeTaxPrice" width="220" align="center">
            <template slot="header">
              <OptRequiredRowTitle
                title="未税单价"
                :isRequired="!isFreeOrder(orderType)&&!isForecast"
              />
            </template>
            <template slot-scope="{row, $index}">
              <el-input-number
                v-model="row.freeTaxPrice"
                style="width:100%"
                size="mini"
                :class="{tabelCellError:orderType!=='Z005'&&!row.freeTaxPrice>0}"
                :min="0"
                :precision="6"
                :step="1"
                :disabled="isFreeOrder(orderType)"
                @change="val=>unTaxedPriceChange(val,$index)"
              />
            </template>
          </el-table-column>
        </template>

        <el-table-column prop="taxRate" label="税率" width="120" align="center" :formatter="formatTaxRate" />
        <el-table-column prop="taxAmount" label="整行税额" width="120" align="center" />
        <el-table-column prop="directDeliverySupplier" width="150" align="center" v-if="!isForecast">
          <template slot="header">
            <OptRequiredRowTitle
              title="选择直发"
              :isRequired="true"
            />
          </template>
          <template slot-scope="{row, $index}">
            <el-select
              v-model="row.directDeliverySupplier"
              :class="{tabelCellError:!!!row.directDeliverySupplier && row.skuNo}"
              size="mini"
              placeholder="请选择"
              @change="val=>changeRow(val,$index,'directDeliverySupplier', row)"
              :disabled="isEnableDirectDeliverySupplier || row.isPositionDisabled"
            >
              <el-option
                v-for="item in directDeliverySupplierList"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="position" width="200" align="center">
          <template slot="header">
            <OptRequiredRowTitle title="库位" :isRequired="true" />
          </template>
          <template slot-scope="{row, $index}">
            <el-select
              v-model="row.position"
              :class="{tabelCellError:!!!row.position && row.skuNo}"
              filterable
              size="mini"
              value-key="code"
              placeholder="请选择"
              :disabled="row.isPositionDisabled"
              @change="val=>changeRow(val,$index,'position')"
            >
              <div v-if="row.directDeliverySupplier === '0'" slot="empty" style="text-align:center">
                <p style="margin-top:10px;color:grey">无匹配数据</p>
                <p style="margin:10px 10px 0 10px;color:grey" v-if="morePositionDisabled">所选仓不在仓网内，如有疑问请联系IT</p>
                <el-button type="text" @click="addPos(row)" v-if="!morePositionDisabled">未找到仓位？点击此处</el-button>
              </div>
              <el-option
                v-for="item in (row.directDeliverySupplier === '0' || !row.directDeliverySupplier) ? (row.simPositionList || []) : row.positionList"
                :key="item.code"
                :label="(item.code !== -1 ? item.code : '')+'  '+item.name"
                :value="item.code"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="sysDeliveryDate" key="sysDeliveryDate" label="标准发货日期" width="120" align="center">
          <template slot-scope="{ row }">
            <el-popover popper-class="custom-popover" placement="top-start" trigger="hover" :content="`原始标准发货日期：${row.originSkuDeliveryDate || ''}`">
              <span slot="reference">{{ row.sysDeliveryDate }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column prop="skuArrivalDate" key="skuArrivalDate" label="标准送达日期" width="120" align="center">
          <template slot="header">
            <el-tooltip class="item" effect="dark" content="点击更新标准送达日期" placement="top">
              <el-button icon="el-icon-refresh" type="text" @click="getDeliveryDate">标准送达日期</el-button>
            </el-tooltip>
          </template>
          <template slot-scope="{ row }">
            <el-popover popper-class="custom-popover" placement="top-start" trigger="hover" :content="`原始标准送达日期：${row.originSkuArrivalDate || ''}`">
              <span slot="reference">{{ row.skuArrivalDate }}</span>
            </el-popover>
          </template>
        </el-table-column>
        <!-- <el-table-column v-if="!isForecast" label="可发货日期" key="deliveryDate" prop="deliveryDate" width="150" align="center">
          <template slot="header" v-if="!isForecast">
            <el-tooltip class="item" effect="dark" content="点击更新可发货日期" placement="top">
              <el-button icon="el-icon-refresh" type="text" @click="getDeliveryDate">可发货日期</el-button>
            </el-tooltip>
          </template>
          <template slot-scope="{row}">
            <span>{{ row.deliveryDate }}</span>
          </template>
        </el-table-column> -->
        <el-table-column  v-if="!isBidCustomer" label="客户期望送达日期" key="customerDate" prop="customerDate" width="150" align="center">
          <template slot="header" v-if="isForecast">
            <OptRequiredRowTitle title="客户期望送达日期" :isRequired="isForecast" />
          </template>
          <template slot-scope="{row, $index}">
            <el-date-picker
              v-model="row.customerDate"
              :disabled="orderData.autoBatching == 'Z'"
              :picker-options="pickerOptions"
              @focus="focusDatePicker"
              :class="{tabelCellError:!!!row.customerDate && isForecast}"
              size="mini"
              type="date"
              style="width:100%"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              @change="val=>changeRow(val,$index,'customerDate')"
            />
          </template>
        </el-table-column>
        <!-- <el-table-column v-if="!isBidCustomer" label="是否接受标期" key="refuseSystemDeliveryDate" prop="refuseSystemDeliveryDate" width="150" align="center">
          <template slot="header" >
            <el-checkbox
              :disabled="orderData.autoBatching != 'X'"
              size="mini"
              class="custom-checkbox"
              style="font-weight: bolder;"
              true-label="X"
              false-label="Z"
              @change="val=>changeColumnCheckbox(val,'refuseSystemDeliveryDate')"
            >是否接受标期
            </el-checkbox>
          </template>
          <template slot-scope="{row, $index}">
            <el-checkbox
              v-model="row.refuseSystemDeliveryDate"
              :disabled="orderData.autoBatching != 'X'"
              size="mini"
              class="custom-checkbox"
              true-label="X"
              false-label="Z"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              @change="val=>changeRow(val,$index,'refuseSystemDeliveryDate')"
            >不接受标期
            </el-checkbox>
          </template>
        </el-table-column> -->
        <el-table-column v-if="isBidCustomer" label="最近客户期望送达日期" key="recentDeliveryDate" prop="recentDeliveryDate" width="150" align="center">
          <template slot="header">
            <OptRequiredRowTitle title="最近客户期望送达日期" :isRequired="true" />
          </template>
          <template slot-scope="{row, $index}">
            <el-date-picker
              v-model="row.recentDeliveryDate"
              :disabled="orderData.bidCustomer == 'Z'"
              size="mini"
              type="date"
              style="width:100%"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              @change="val=>changeRow(val,$index,'recentDeliveryDate')"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="isBidCustomer" label="发货频次" key="deliveryFrequency" prop="deliveryFrequency" width="160" align="center">
          <template slot="header">
            <OptRequiredRowTitle title="发货频次" :isRequired="true" />
          </template>
          <template slot-scope="{row, $index}">
            <el-input-number
              v-model="row.deliveryFrequency"
              :disabled="orderData.bidCustomer == 'Z'"
              :min="0"
              :precision="0"
              placeholder="请输入"
              size="mini"
              @change="val=>changeRow(val,$index,'deliveryFrequency')"
            />
          </template>
        </el-table-column>
        <el-table-column v-if="isBidCustomer" label="发货周期" key="deliveryCycle" prop="deliveryCycle" width="150" align="center">
          <template slot="header">
            <OptRequiredRowTitle title="发货周期" :isRequired="true" />
          </template>
          <template slot-scope="{row, $index}">
            <el-input
              v-model="row.deliveryCycle"
              :disabled="orderData.bidCustomer == 'Z'"
              placeholder="请输入"
              :min="0"
              size="mini"
              @change="val=>changeRow(val,$index,'deliveryCycle')"
            />
          </template>
        </el-table-column>
        <el-table-column prop="costCenter" width="200" align="center" v-if="isZ007">
          <template slot="header">
            <OptRequiredRowTitle title="成本中心" :isRequired="isInnerOrderReason(orderData.orderReason)" />
          </template>
          <template slot-scope="{row, $index}">
            <el-select
              v-model="row.costCenter"
              :class="{tabelCellError:(!!!row.costCenter && isInnerOrderReason(orderData.orderReason))}"
              filterable
              size="mini"
              value-key="costCenter"
              placeholder="请选择"
              @change="val=>changeRow(val,$index,'costCenter')"
            >
              <el-option
              v-for="item in costCenterOptions"
              :key="item.costCenter"
              :label="item.costCenter + ' ' + item.description"
              :value="item.costCenter"
            />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="generalLedgerAccount" width="200" align="center"  v-if="isZ007">
          <template slot="header">
            <OptRequiredRowTitle title="总账科目" :isRequired="isInnerOrderReason(orderData.orderReason)" />
          </template>
          <template slot-scope="{row, $index}">
            <el-select
              v-model="row.generalLedgerAccount"
              :class="{tabelCellError:(!!!row.generalLedgerAccount && isInnerOrderReason(orderData.orderReason))}"
              filterable
              size="mini"
              value-key="value"
              placeholder="请选择"
              @change="val=>changeRow(val,$index,'generalLedgerAccount')"
            >
              <el-option
              v-for="item in mmDictList.generalLedger"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100" align="center">
          <template slot-scope="{$index}">
            <el-button type="text" size="small" @click="showSkuDetail($index)">详情</el-button>
            <el-button type="text" size="small" @click="removeSku($index)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <ProductDetail
      width="800px"
      :itemKey="getKey()"
      :index="skuDetailIndex"
      :sku-detail="skus[skuDetailIndex]"
      :show-dialog.sync="showProductDetail"
      :format-price="formatPrice"
      :customer="customer"
      :customer-detail="customerDetail"
      :order-data="orderData"
      :orderType="orderType"
      @submit="submitDetail"
      v-on="$listeners"
    />
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="page"
      :limit.sync="size"
      layout="total, prev, pager, next, jumper"
    />
    <el-dialog
      title="快速导入商品"
      :visible.sync="showBatchImport"
      :destroy-on-close="true"
      width="600px"
    >
      <BatchImport
        @close="showBatchImport=false"
        @import="handleImport"
        :order-data="orderData"
        :orderType="orderType"
        :salesRange="salesRange"
        :salesOrganization="salesOrganization"
      />
    </el-dialog>
    <el-dialog
      title="操作提示"
      :visible.sync="uploadDialogVisible"
      append-to-body
      center
      width="600px">
      <div class="tips-container" v-html="uploadFailContent"></div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="uploadAgain">继续建单</el-button>
        <el-button type="warning" @click="downloadFailExcelUrl">下载物料关系</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import slice from 'lodash/slice'
import uniq from 'lodash/uniq'
import debounce from 'lodash/debounce'
import Pagination from '@/components/Pagination'
import * as createOrder from '@/api/orderSale'
import { formatPrice } from '@/utils'
import ProductDetail from '../ProductDetail'
import OptRequiredRowTitle from '@/pages/orderSale/components/common/OptRequiredRowTitle'
import BatchImport from '@/pages/orderSale/components/create/BatchImport'
import ShowMorePosition from '@/pages/orderSale/components/create/ShowMorePosition'

import { isFreeOrder, isForecastOrder, isEnableDirectDeliverySupplier, isInnerOrderReason } from '@/pages/orderSale/utils/orderType'
import { getDirectDeliverySupplierList, getDisabledDate } from '@/pages/orderSale/utils'
import { sensors } from '@/utils/index.js'
// import { excelUrls } from '@/pages/orderSale/constants'

export default {
  components: {
    OptRequiredRowTitle,
    ProductDetail,
    BatchImport,
    ShowMorePosition,
    Pagination
  },
  props: [
    'salesRange',
    'orderData',
    'customer',
    'customerDetail',
    'orderType',
    'factoryList',
    'totalDiscount',
    'costCenterOptions'
  ],
  data () {
    const { companycode } = this.$route.params
    return {
      company: companycode,
      tableLoading: false,
      showProductDetail: false,
      showBatchImport: false,
      ShowMorePosition: false,
      rowDetail: {},
      currentSelectSku: null,
      multipleSelection: [],
      totalPrice: {},
      skuList: {},
      loading: null,
      skuDetailIndex: -1,
      selectedSearchItem: 1,
      page: 1,
      size: 20,
      uploadDialogVisible: false,
      uploadFailContent: '',
      needCustomerRelCheck: true, // 批量上传sku，是否校验多物料关系的参数
      pickerOptions: {
        disabledDate: (time) => {
          const specifiedReceiptDayOfWeek = this.orderData?.specifiedReceiptDayOfWeek || this.customerDetail?.specifiedReceiptDayOfWeek?.split(',')
          const receiptTimeCategory = this.orderData?.receiptTimeCategory === '' ? this.customerDetail?.receiptTimeCategory : this.orderData?.receiptTimeCategory
          const check = !['Z002', 'Z014'].includes(this.orderType) && !['8', 'X'].includes(this.orderData.bidCustomer)
          return getDisabledDate(time, specifiedReceiptDayOfWeek, receiptTimeCategory, check)
        }
      },
      searchKeyWord: ''
    }
  },
  computed: {
    isBidCustomer () {
      const bidCustomer = this.orderData.bidCustomer
      return (bidCustomer === '8' || bidCustomer === 'X') && this.isZ001
    },
    total () {
      return this.skus.length
    },
    isZ007 () {
      return this.orderType === 'Z007'
    },
    isZ001 () {
      return /Z001/gim.test(this.orderType)
    },
    mmDictList () {
      return this.$store.state.orderPurchase.dictList || {}
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    acceptFileType () {
      return this.$store.state.orderCommon.acceptFileType || {}
    },
    morePositionDisabled () {
      return this.$store.state.orderCommon.morePositionDisabled
    },
    excelUrls () {
      return this.$store.state.orderCommon.excelUrls
    },
    skus () {
      const key = this.getKey()
      return this.$store.state.orderGoods.skuList[key] || []
    },
    taxedTotalAmount () {
      const key = this.getKey()
      return this.$store.state.orderGoods.taxedTotalAmount[key] || 0
    },
    untaxedTotalAmount () {
      const key = this.getKey()
      return this.$store.state.orderGoods.untaxedTotalAmount[key] || 0
    },
    taxedDiscountTotal () {
      const key = this.getKey()
      return this.$store.state.orderGoods.taxedDiscountTotal[key] || 0
    },
    unTaxedDiscountTotal () {
      const key = this.getKey()
      return this.$store.state.orderGoods.unTaxedDiscountTotal[key] || 0
    },
    isTaxedCustomer () {
      const detail = this.customerDetail || {}
      return detail && detail.isTax === '1'
    },
    isForecast () {
      return isForecastOrder(this.orderType)
    },
    isEnableDirectDeliverySupplier () {
      return isEnableDirectDeliverySupplier(this.orderType)
    },
    salesOrganization () {
      if (this.orderData) {
        const { selectedSalesRange } = this.orderData
        if (selectedSalesRange) {
          return selectedSalesRange.salesOrganization
        }
      }
      return ''
    },

    // 6001 工厂不可选 选择直发只能选择：供应商直发，库位：1004
    isFactoryDisabled () {
      return this.salesOrganization === '6001' && (this.orderData.factoryPriorityList !== undefined && this.orderData.factoryPriorityList[0] !== '1000')
    },
    itemList () {
      let data = slice(this.skus, (this.page - 1) * this.size, (this.page - 1) * this.size + this.size)
      data.forEach(item => {
        item.isPositionDisabled = false
        if (this.salesOrganization === '6001' && item.factory.code === '6000') {
          item.position = '1004'
          item.directDeliverySupplier = '1'
          item.isPositionDisabled = true
        }
        return item
      })
      return data
    },
    directDeliverySupplierList () {
      return getDirectDeliverySupplierList(this.dictList, this.orderType, this.salesOrganization).map(item => {
        if (this.isFactoryDisabled) {
          item.isDisabled = !(item.code === '1')
        }
        return item
      })
    },
    discount: {
      get () {
        return this.totalDiscount
      },
      set (val) {
        this.$emit('update:totalDiscount', val)
      }
    },
    uploadQueryUrl () {
      const { salesOrganization, distributionChannel, productGroup } = this.salesRange || {}
      const { customerNumber } = this.orderData || {}
      const queryStr = []
      let url = ''
      if (salesOrganization && distributionChannel && productGroup && customerNumber) {
        const params = {
          customerNo: customerNumber,
          salesOrganization,
          distributionChannel,
          productGroup,
          orderType: this.orderType,
          needCustomerRelCheck: this.needCustomerRelCheck
        }
        Object.keys(params).forEach(key => {
          queryStr.push(`${key}=${params[key]}`)
        })
        if (queryStr.length > 0) {
          const s = queryStr.join('&')
          url += `?${s}`
        }
      }
      return url
    },
    searchSkuSwitch () {
      return this.$store.state.orderCommon.searchSkuSwitch
    }
  },
  methods: {
    isInnerOrderReason,
    focusDatePicker () {
      const specifiedReceiptDayOfWeek = this.orderData?.specifiedReceiptDayOfWeek?.filter(day => !!day) || this.customerDetail?.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day)
      const receiptTimeCategory = this.orderData?.receiptTimeCategory === '' ? this.customerDetail.receiptTimeCategory : this.orderData?.receiptTimeCategory
      if ((receiptTimeCategory === 'Z' || receiptTimeCategory === false) && specifiedReceiptDayOfWeek?.length && specifiedReceiptDayOfWeek?.every(day => !['0', '01', '02', '03', '04', '05'].includes(day))) {
        this.$message.warning('无可选日期，请修改客户指定日收货或工作日与周末均可收货!')
      }
    },
    addPos (row) {
      this.rowDetail = row
      this.ShowMorePosition = true
    },
    deliveryDateApi (skuDemandQtyList) {
      const { salesOrganization } = this.salesRange || {}
      const { receiverContact, orderContact, autoBatching, bidCustomer, acceptSupplierDelivery } = this.orderData || {}
      let { customerNumber: customer, serviceCenterSelfTransport, specifiedReceiptDayOfWeek, receiptTimeCategory } = this.customerDetail || {}
      let { otherLabelReq, fastenerLabelReq, labelPasteWay, hideLogo, fastenerDetect, packagingReq, fastenerSpecialPackageReq, deliveryOtherNote, dnIncidentalWay } = this.orderData.moreDeliveryData || this.orderData || {}
      if (this.orderData.receiptTimeCategory !== '') {
        receiptTimeCategory = this.orderData?.receiptTimeCategory ? 'X' : 'Z'
      }
      if (this.orderData?.specifiedReceiptDayOfWeek) {
        specifiedReceiptDayOfWeek = (this.orderData?.specifiedReceiptDayOfWeek || []).join(',');
      }
      const {
        province: demandProvinceCode,
        city: demandCityCode,
        region: demandDistrictCode,
        addressId: demandStreetTownCode,
        contactId: receiptContactId
      } = receiverContact || {}
      const { contactId: orderContactId } = orderContact || {}
      const data = {
        customer,
        salesOrganization,
        skuDemandQtyList: skuDemandQtyList.map(item => ({ qty: item.qty, sku: item.sku })),
        demandProvinceCode,
        demandCityCode,
        demandDistrictCode,
        demandStreetTownCode,
        serviceCenterSelfTransport,
        receiptContactId: Number(receiptContactId) || '',
        orderContactId: Number(orderContactId) || '',
        specifiedReceiptDayOfWeek,
        receiptTimeCategory,
        otherLabelReq: Array.isArray(otherLabelReq) ? otherLabelReq.join(',') : otherLabelReq,
        fastenerLabelReq: Array.isArray(fastenerLabelReq) ? fastenerLabelReq.join(',') : fastenerLabelReq,
        labelPasteWay: Array.isArray(labelPasteWay) ? labelPasteWay.join(',') : labelPasteWay,
        hideLogo,
        fastenerDetect,
        packagingReq: Array.isArray(packagingReq) ? packagingReq.join(',') : packagingReq,
        fastenerSpecialPackageReq,
        deliveryOtherNote,
        bidCustomer,
        dnIncidentalWay,
        acceptSupplierDelivery,
        orderType: this.orderType
      }
      console.log(data)
      if (!skuDemandQtyList.length) {
        return
      }
      if (!salesOrganization) {
        return this.$message.error('销售组织不能为空！')
      }
      if (!demandProvinceCode) {
        return this.$message.error('客户收货省份代码不能为空！')
      }
      if (!demandCityCode) {
        return this.$message.error('客户收货城市代码不能为空！')
      }
      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      createOrder.getDeliveryTime(data, { autoBatching })
        .then(res => {
          if (res) {
            console.log(res)
            if (res.code === 200 && res.success && res.data) {
              this.setRowDate(skuDemandQtyList, res.data)
            } else {
              this.$message.error(res.msg || '查询交期出错了，请稍后再试！')
            }
          } else {
            this.$message.error('查询交期出错了，请稍后再试！')
          }
        })
        .finally(() => {
          this.loading && this.loading.close()
        })
    },
    getDeliveryDate () {
      console.log(this.orderData)
      console.log(this.customer)
      console.log(this.customerDetail)
      // 部分销售组织不支持交期查询
      // if (['1000', '1001', '1300'].includes(this.salesOrganization)) {
      //   const skuDemandQtyList = this.skus.slice().map(item => {
      //     if (['1000', '1300'].includes(item.factory?.code)) {
      //       return { qty: item.quantity, sku: item.skuNo, uuid: item.uuid }
      //     }
      //   }).filter(item => item && item.qty && item.sku)
      // 预报单不支持交期查询
      if (!this.isForecast) {
        const skuDemandQtyList = this.skus.slice().map(item => ({ qty: item.quantity, sku: item.skuNo, uuid: item.uuid })).filter(item => item.qty && item.sku)
        this.deliveryDateApi(skuDemandQtyList)
      }
      // }
    },
    getDeliveryDateByQty (row) {
      this.getDeliveryDate()
    },
    setRowDate (sourceList, resList) {
      const { autoBatching } = this.orderData
      let errorMsg = '<div style="max-height: 300px;overflow: auto">'
      let hasError = false
      let fillList = []
      resList.forEach(response => {
        const sourceItem = sourceList.find(source => source.sku === response.material && source.qty === response.quantity)
        sourceList = sourceList.filter(item => item !== sourceItem)
        const { waveDeliveryDate, promoteInfo, skuArrivalDate } = response
        const index = this.skus.slice().findIndex(item => item.uuid === sourceItem.uuid)
        if (!waveDeliveryDate && promoteInfo) {
          hasError = true
          errorMsg += `第${index + 1}行SKU【${sourceItem.sku}】查询交期失败：${promoteInfo}<br />`
        }
        fillList.push({ ...response, waveDeliveryDate, index, skuArrivalDate })
      })
      errorMsg += '</div>'
      // autoBatching 不勾选就是整单
      if (autoBatching !== 'X') {
        try {
          let max = fillList.filter(x => x.waveDeliveryDate).sort((x, y) => new Date(y.waveDeliveryDate) - new Date(x.waveDeliveryDate))[0]
          fillList.forEach(item => {
            item.waveDeliveryDate = max.waveDeliveryDate
          })
        } catch (err) {
          console.log(err)
        }
      }
      console.log(fillList)
      fillList.forEach(({ waveDeliveryDate, index, skuArrivalDate, sysDeliveryDate, originSkuArrivalDate, originSkuDeliveryDate }) => {
        this.changePageRow(waveDeliveryDate, index, 'deliveryDate')
        this.changePageRow(skuArrivalDate, index, 'skuArrivalDate')
        this.changePageRow(sysDeliveryDate, index, 'sysDeliveryDate')
        this.changePageRow(originSkuArrivalDate, index, 'originSkuArrivalDate')
        this.changePageRow(originSkuDeliveryDate, index, 'originSkuDeliveryDate')
      })
      if (hasError) {
        this.$alert(errorMsg, '操作提示', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
    },
    downloadTemplate () {
      // to-do otd4灰度环境匹配的模板，后续要删掉
        // const isGray = window.location.href.includes('boss-gray') || window.location.href.includes('boss-uat-4')
        // const url = isGray ? this.excelUrls.batchImportLineGray : this.excelUrls.batchImportLine
        // window.open(url)
      window.open(this.excelUrls.batchImportLine)
    },
    validCustomerDateByUpload (skuList) {
      let errMsg = '';
      skuList.map(sku => {
        if (sku.customerDate) {
          const day = new Date(sku.customerDate);
          const check = !['Z002', 'Z014'].includes(this.orderType) && !['8', 'X'].includes(this.orderData.bidCustomer)
          const isDisabled = getDisabledDate(day, this.orderData?.specifiedReceiptDayOfWeek, this.orderData?.receiptTimeCategory, check);
          if (isDisabled) {
            errMsg += `第${sku.idx}行客户期望送达日期${sku.customerDate}与客户交期要求冲突(指定收货日期、工作日周末收货)，请重新维护！<br/>`
          }
        }
      })
      if (errMsg) {
        this.$confirm(`<div style="max-height:400px;overflow:auto;">${errMsg}</div>`, '操作提示', {
          type: 'info',
          dangerouslyUseHTMLString: true
        })
      }
    },
    downloadFailExcelUrl () {
      window.open(this.failExcelUrl);
    },
    uploadAgain () {
      try {
        this.needCustomerRelCheck = false;
        console.log('this.$refs.upload.uploadFiles', this.$refs.upload.uploadFiles)
        let action = () => {
          const length = this.$refs.upload.uploadFiles.length - 1
          this.$refs.upload.uploadFiles[length].status = 'ready'
          this.$refs.upload.submit()
        }
        setTimeout(action, 200);
      } catch (err) {
        console.log(err)
      }
    },
    handleUploadSuccess (res, file, fileList) {
      this.loading && this.loading.close()
      if (this.uploadDialogVisible) {
        this.uploadDialogVisible = false;
      }
      this.needCustomerRelCheck = true;
      if (res && res.code === 200) {
        const { failSkuList, skuDetailVOList } = res.data
        // const isEmpty = this.skus && this.skus.length === 0
        const successCallback = () => {
          this.$store.dispatch('orderGoods/importGoods', {
            salesRange: this.salesRange,
            orderData: this.orderData,
            orderType: this.orderType,
            company: this.company,
            key: this.getKey(),
            factoryList: this.factoryList,
            skus: this.skus,
            skuList: skuDetailVOList
          }).then(result => {
            if (result && result.length > 0 && result[0].productGroup) {
              const callback = () => this.getDeliveryDate()
              this.fetchCustomerBySpart(result[0].productGroup, callback)
              this.validCustomerDateByUpload(result)
            }
          })
        }
        // 多物料关系报错提示
        if (res.data?.failExcelUrl) {
          this.uploadDialogVisible = true;
          this.uploadFailContent = '部分数据行存在多条客户物料关系数据，请确认【继续建单】或者【下载物料关系】';
          this.failExcelUrl = res.data?.failExcelUrl;
          return;
        }
        if (failSkuList && failSkuList.length > 0) {
          const failLine = failSkuList.length
          const successLine = skuDetailVOList.length
          const skus = uniq(failSkuList).join(',')
          this.$alert(`导入成功商品行数量为${successLine}行，失败数量为${failLine}行。` +
            `${skus}`, '错误', {
            confirmButtonText: '确定',
            callback: action => {
            }
          })
        } else {
          successCallback()
        }
      } else if (res && res.code !== 200 && res.msg) {
        if (res?.data?.failExcelUrl) {
            this.$confirm(res.msg, '操作提示', {
              confirmButtonText: '下载报错文档',
              cancelButtonText: '确定',
              type: 'error'
            }).then(() => {
              window.open(res.data.failExcelUrl)
            })
        } else {
          this.$alert(res.msg, '错误', {
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
      }
    },
    handleConditionChange (val) {
      this.skuList = {
        options: [
          val === 1 ? {
            skuNo: '商品编号',
            materialDescribe: '商品描述'
          } : {
            customerSkuNo: '客户物料号',
            skuNo: '商品编号',
            skuName: '商品描述'
          }
        ]
      }
      this.currentSelectSku = null
    },
    isFactoryDisable (row, code) {
      if (row && code) {
        const { factoryProductPriceVOMap } = row
        const c = parseInt(code, 10)
        return !(factoryProductPriceVOMap && factoryProductPriceVOMap[c] && factoryProductPriceVOMap[c].taxRate != null)
      }
      return true
    },
    isFreeOrder (type) {
      return isFreeOrder(type)
    },
    getKey () {
      const { companycode, categorycode } = this.$route.params
      return `${companycode}_${categorycode}`
    },
    handleImport (data) {
      // const isEmpty = this.skus && this.skus.length === 0
      this.$store.dispatch('orderGoods/importGoods', {
        salesRange: this.salesRange,
        orderData: this.orderData,
        orderType: this.orderType,
        company: this.company,
        factoryList: this.factoryList,
        skus: this.skus,
        key: this.getKey(),
        skuList: data
      }).then(result => {
        if (result && result.length > 0 && result[0].productGroup) {
          const callback = () => this.getDeliveryDate()
          this.fetchCustomerBySpart(result[0].productGroup, callback)
        }
      })
    },
    searchSkuList: debounce(function(search, cb) {
      this.searchKeyWord = search;
      this.skuList = { isLoading: true }
      if (this.searchSkuSwitch) {
        if (search) {
          const data = {
            vague: search,
            customerCode: this.orderData?.customerNumber
          }
          createOrder.searchSkuListV2(data).then(result => {
            if (result.code === 200) {
              if (Array.isArray(result.data) && result.data.length > 0) {
                cb && cb(result.data)
                let options = result.data.map((item, index) => {
                  return {
                    index: index + 1,
                    ...item
                  }
                })
                options.unshift({
                  skuNo: '商品编号',
                  materialDescribe: '商品描述',
                  customerSkuNo: '客户物料号',
                  customerSkuName: '客户物料名称',
                  customerSkuUnitCount: '客户物料数量',
                  customerSkuUnit: '客户物料数量单位',
                  customerSkuSpecification: '客户物料规格型号',
                  dataSource: ''
                })
                this.skuList = {
                  options
                }
                console.log(this.skuList)
              }
            } else {
              cb && cb()
              this.$message.error({
                message: result.msg
              })
            }
            this.skuList.isLoading = false
          })
        }
      } else {
        let searchFn = this.selectedSearchItem === 1 ? 'searchSkuList' : 'searchMaterial'
        let params = search
        if (this.selectedSearchItem !== 1) {
          const { customerNumber } = this.orderData
          params = {
            customerNo: customerNumber,
            customerSkuNo: search,
            current: 1,
            size: 20
          }
        }
        createOrder[searchFn](params).then(result => {
          if (result.code === 200) {
            if (Array.isArray(result.data) && result.data.length > 0) {
              cb && cb(result.data)
              this.skuList = {
                options: [
                  this.selectedSearchItem === 1 ? {
                    skuNo: '商品编号',
                    materialDescribe: '商品描述'
                  } : {
                    customerSkuNo: '客户物料号',
                    skuNo: '商品编号',
                    skuName: '商品描述'
                  },
                  ...result.data
                ]
              }
              console.log(this.skuList)
            }
          } else {
            cb && cb()
            this.$message.error({
              message: result.msg
            })
          }
          this.skuList.isLoading = false
        })
      }
    }, 1000),
    toDataSource (item) {
      if (item.dataSource) {
        switch (item.dataSource) {
          case 'QTS':
            window.open(`https://qts-uat.zkh360.com/sales/inquiry/detail/${item.referenceNo}`);
            break;
          case 'SO':
            this.$router.jumpToSoOrderDetail({
              query: {
                soNo: item.referenceNo
              }
            })
            break;
          case 'BOSS':
          case 'BOSS_OCR':
            window.open(`/insteadOrder/maintainmentV3?id=${item.id}`)
            break;
        }
      }
    },
    formatPrice (value) {
      return formatPrice(value)
    },
    fetchCustomerBySpart (productGroup, callback) {
      const { customerNumber } = this.orderData
      const { salesOrganization, distributionChannel } = this.salesRange || {}
      if (productGroup != null && salesOrganization && distributionChannel && customerNumber) {
        createOrder.getClientDetail(customerNumber, distributionChannel, productGroup, salesOrganization).then(res => {
          if (res && res.code === 200) {
            this.$emit('updateCustomer', res.data)
            callback && callback()
          }
        })
      }
    },
    addSkuToTable () {
      if (this.salesRange) {
        const { skuNo, materialDescribe, customerSkuNo, customerSkuName, customerSkuUnitCount, customerSkuUnit, customerSkuSpecification, dataSource, matchField } = this.currentSelectSku;
        const data = {
          key_word: this.searchKeyWord,
          sku_no: skuNo,
          product_description: materialDescribe,
          customer_materiel_no: customerSkuNo,
          customer_materiel_name: customerSkuName,
          customer_materiel_quantity: customerSkuUnitCount,
          customer_materiel_quantity_unit: customerSkuUnit,
          customer_materiel_specifications_no: customerSkuSpecification,
          data_source: dataSource,
          match_route: matchField
        }
        this.searchSkuSwitch && sensors('SoCreateSelectProductConfirmButtonClick', data) // 点击确认添加埋点
        this.tableLoading = true
        this.$store.dispatch('orderGoods/addGoods', {
          goods: this.currentSelectSku,
          salesRange: this.salesRange,
          orderData: this.orderData,
          orderType: this.orderType,
          company: this.company,
          factoryList: this.factoryList,
          skus: this.skus,
          key: this.getKey(),
          selectedSearchItem: this.selectedSearchItem
        }).then(res => {
          const { result } = res;
          this.tableLoading = false
          if (result && result.code === 200) {
            if (result.hasTaxRate === false) {
              this.$alert('该sku在该工厂没有对应的税率，无法下单，请在crm中进行维护或更换其他工厂', '错误')
              return
            }
          }
          if (result && result.code !== 200 && result.msg) {
            this.$alert(result.msg, '错误')
            return
          }
          const { productGroup } = (result || {}).data
          if (this.skus && this.skus.length === 1 && productGroup) {
            this.fetchCustomerBySpart(productGroup)
          }
        })
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        })
      }
    },
    addNullSkuToTable() {
      if (this.salesRange) {
        this.$store.dispatch('orderGoods/addNullGoods', {
          skus: this.skus,
          key: this.getKey(),
          orderData: this.orderData,
          orderType: this.orderType
        }).then(() => {
          this.showSkuDetail(this.skus.length - 1)
        })
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        })
      }
    },
    submitDetail() {
      this.getDeliveryDate()
    },
    changeColumnCheckbox (value, type) {
      console.log(value, type)
      this.skus.forEach((sku, index) => {
        this.changeRow(value, index, type)
      })
    },
    changePageRow (value, index, type) {
      let page = 1;
      while (index > 19) {
        if (index / 20 >= 1) {
          page++
          index -= 20
        }
      }
      this.$store.dispatch('orderGoods/changeItem', {
        orderData: this.orderData,
        key: this.getKey(),
        orderType: this.orderType,
        page,
        size: this.size,
        type,
        index,
        value
      })
    },
    async setSimPosition(row, index) {
      index = (this.page - 1) * this.size + index;
      const data = {
        skuSet: [row.skuNo],
        factorySet: [row.factory.code],
        positionScope: 1
      }
      const res = await createOrder.getDeliveryWarehouse(data)
      if (res && res.status === 200 && Array.isArray(res.result)) {
        const posList = []
        const codeMap = {}
        res.result.reduce((prev, next) => {
          prev.push(...next.allPosition)
          return prev;
        }, []).forEach(item => {
          const key = `${item.factory}_${item.code}_${item.name}`
          if (!codeMap[key]) {
            codeMap[key] = true
            posList.push(item)
          }
        })
        console.log(posList)
        this.skus[index].simPositionList = posList
      }
      console.log(res)
    },
    async changeRow (value, index, type, row) {
      if (type === 'directDeliverySupplier') {
        if (!row.simPositionList || !row.simPositionList.length) {
          if (value === '0') {
              // 震坤行发货 sim 库位
              await this.setSimPosition(row, index)
            }
        }
      }
      if (type === 'factory' && value && value.code) {
        const isDisabled = this.isFactoryDisable(this.skus[index], value.code)
        if (isDisabled) {
          this.$alert('该sku在该工厂没有对应的建议销售价，无法下单，请在crm中进行维护或更换其他工厂', '错误')
        } else {
          await this.setSimPosition(row, index)
        }
      }
      // if (type === 'deliveryDate' && value) {
      //   row.deliveryDate = value;
      // }
      if (type === 'directDeliverySupplier') {
        row.position = ''
      }
      this.$store.dispatch('orderGoods/changeItem', {
        orderData: this.orderData,
        key: this.getKey(),
        orderType: this.orderType,
        page: this.page,
        size: this.size,
        type,
        index,
        value
      })
    },
    changeAmount (value, index, row) {
      this.$store.dispatch('orderGoods/changeGoodsAmount', {
        orderData: this.orderData,
        key: this.getKey(),
        page: this.page,
        size: this.size,
        index,
        value
      })
      if (value) {
        this.getDeliveryDateByQty(row)
      }
    },
    taxedPriceChange (value, index) {
      this.$store.dispatch('orderGoods/changeGoodsTaxedPrice', {
        orderData: this.orderData,
        key: this.getKey(),
        page: this.page,
        size: this.size,
        value,
        index
      })
    },
    unTaxedPriceChange (value, index) {
      this.$store.dispatch('orderGoods/changeGoodsUntaxedPrice', {
        orderData: this.orderData,
        key: this.getKey(),
        page: this.page,
        size: this.size,
        value,
        index
      })
    },
    handleAssign () {
      this.$store.dispatch('orderGoods/assignDiscount', {
        orderData: this.orderData,
        key: this.getKey(),
        totalDiscount: this.discount
      }).then(result => {
        if (result) {
          this.discount = 0
        } else {
          this.$message.error('分配整单折扣金额失败！')
        }
      })
    },
    removeSku (index) {
      this.$confirm('此操作将删除所选的商品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('orderGoods/removeGoods', {
          orderData: this.orderData,
          key: this.getKey(),
          page: this.page,
          size: this.size,
          index
        }).then(() => {
          if (index === 0 && this.skus.length > 0) {
            const goodsItem = this.skus[0]
            if (goodsItem) {
              this.fetchCustomerBySpart(goodsItem.productGroup, () => {
                this.getDeliveryDate()
              })
            }
          } else {
            this.getDeliveryDate()
          }
        })
      })
    },
    tableSelectionChange (val) {
      this.multipleSelection = val
    },
    removeMultiSku () {
      const firstItem = this.skus[0]
      const hasRemoveFirstLine = this.multipleSelection &&
        this.multipleSelection.length > 0 &&
        this.multipleSelection.find(item => item.idx === firstItem.idx)
      this.$confirm('此操作将删除所选的商品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('orderGoods/removeMultiGoods', {
          key: this.getKey(),
          page: this.page,
          size: this.size,
          value: this.multipleSelection,
          orderData: this.orderData
        }).then(() => {
          if (hasRemoveFirstLine && this.skus.length > 0) {
            const goodsItem = this.skus[0]
            if (goodsItem) {
              this.fetchCustomerBySpart(goodsItem.productGroup, () => {
                this.getDeliveryDate()
              })
            }
          } else {
            this.getDeliveryDate()
          }
        })
      })
    },
    showSkuDetail (index) {
      console.log(index)
      this.skuDetailIndex = (this.page - 1) * this.size + index
      this.showProductDetail = true
    },
    formatTaxRate (row, column, cellValue, index) {
      if (row && row.taxRate) {
        return `${row.taxRate * 100}%`
      }
      return 0
    }
  }
}
</script>

<style lang="scss">
.el-message-box__message {
  p {
    word-break: break-word;
  }
}
.pagination-container {
  text-align: right;
}
</style>

<style lang="scss" scoped>
.upload {
  display: inline;
}
.tableInfo {
  padding: 0 10px;
}

.tableHeader {
  margin-bottom: 10px;
  .merchantInfo-row {
    color: #909399;
    .amount {
      margin-right: 20px;
    }
    .currency {
      color: #ff4949;
    }
  }
}

.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
  }
}
.selectMeterialItem {
  div:nth-child(1) {
    width: 90px;
    flex-shrink: 0;
  }
  div:nth-child(2) {
    width: 400px;
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
    flex-shrink: 0;
  }
  div:nth-child(3),
  div:nth-child(4),
  div:nth-child(5){
    width: 90px;
    flex-shrink: 0;
    text-overflow:ellipsis;
    overflow:hidden;
  }
  div:nth-child(6){
    width: 120px;
  }
  div:nth-child(7){
    width: 90px;
    text-overflow:ellipsis;
    overflow:hidden;
  }
}

.selectCustomerItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 90px;
  }
  div:nth-child(3) {
    width: 500px;
    white-space:nowrap;
    text-overflow:ellipsis;
    overflow:hidden;
  }
}

.selectClientItem {
  div:nth-child(1) {
    width: 80px;
  }
  div:nth-child(2) {
    width: 300px;
  }
}

.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.ba-row-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.discount-tips {
  font-size: 13px;
  color: #909399;
}
</style>

<style lang="scss">
.tabelCellError {
  input {
    border: 1px solid #ff4949;
  }
  &:hover {
    input {
      border: 1px solid #ff4949;
    }
  }
}

.search-row {
  .el-form-item__content {
    display: flex;
    align-items: center;
  }
  .discount-input {
    .el-input__inner {
      width: 240px;
    }
  }
}
.search-item {
  width: 130px;
  flex: none;
  .el-input {
    input {
      border: 0;
    }
  }
}

.search-input {
  flex: 1;
  margin-right: 10px;
}
.custom-popover {
  height: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 10px;
}
.custom-checkbox .el-checkbox__label {
  font-size: 12px;
}
.custom-checkbox .el-checkbox__inner {
  width: 12px;
  height: 12px;
}
</style>
<style lang="scss">
.scroll-option.el-select-dropdown{
    max-width: 1200px;

    .el-select-dropdown__item {
      width: fit-content !important;
    }
}
</style>
