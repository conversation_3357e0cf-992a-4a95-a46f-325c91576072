<template>
<div class="sto">
  <div class="sto-query">
    <el-form :inline="true" ref="stoForm" :model="stoQueryData"
      label-width="145px" class="sto-form" :rules="rules"
    >
      <el-form-item label="调拨单号" class="sto-form-item">
        <el-input
          type="textarea"
          v-model="stoQueryData.sapStoNos"
          placeholder="最大同时支持100个单号"
          class="form-item"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item label="采购组" prop="purchaseGroup" class="sto-form-item">
        <el-select v-model="stoQueryData.purchaseGroup" placeholder="采购组" class="form-item" filterable clearable>
          <el-option
            v-for="item in dictList['purchaseGroup']"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="商品编码" class="sto-form-item">
        <el-input v-model="stoQueryData.sku" placeholder="商品编码" class="form-item" clearable></el-input>
      </el-form-item>
      <el-form-item label="工厂" class="sto-form-item">
        <el-select v-model="stoQueryData.factory" placeholder="工厂" class="form-item" filterable clearable>
          <el-option
            v-for="item in factoryList"
            :key="item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="发货库位" class="sto-form-item">
        <el-select v-model="stoQueryData.deliveryPosition" placeholder="发货库位" class="form-item"
          filterable
          clearable
        >
          <el-option
            v-for="item in getPositionList(stoQueryData.factory)"
            :key="item.code"
            :label="item.code+item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="接收库位" class="sto-form-item">
        <el-select v-model="stoQueryData.receiptPosition" placeholder="接收库位" class="form-item"
          filterable
          clearable
        >
          <el-option
            v-for="item in getPositionList(stoQueryData.factory)"
            :key="item.code"
            :label="item.code+item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime" class="sto-form-item">
        <el-date-picker
          v-model="stoQueryData.createTime"
          type="daterange"
          start-placeholder="创建开始时间"
          end-placeholder="创建结束时间"
          value-format="yyyy-MM-dd"
          :default-time="['00:00:00', '23:59:59']">
        </el-date-picker>
      </el-form-item>
      <el-form-item class="sto-form-item btn-row">
        <el-button class="sto-btn" type="primary" @click="onSubmit('stoForm')">查询</el-button>
        <el-button class="sto-btn" @click="onReset('stoForm')">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
  <div class="sto-row">
    <span>请勾选您要创建交货的调拨单商品行，并点击右侧创建交货按钮进行创建</span>
    <el-button type="primary" @click="onCreateDN" :disabled="!tableSelection||tableSelection.length===0">创建交货</el-button>
  </div>
  <vxe-table
    border
    highlight-hover-row
    height="500"
    align="center"
    :data="stoListData"
    :checkbox-config="{checkMethod: checCheckbox}"
    @checkbox-change="onTableSelectionChange"
    @checkbox-all="onTableSelectionAllChange"
  >
    <vxe-table-column type="checkbox" title="" fixed="left" align="center"></vxe-table-column>
    <vxe-table-column field="sapStoNo" title="调拨单号" width="240" align="center">
      <template v-slot="{row}">
        {{ row.sapStoNo }}
        <el-button type="primary" plain @click="triggerAtp(row.sapStoNo)" size="mini">触发占库</el-button>
      </template>
    </vxe-table-column>
    <vxe-table-column field="sku" title="商品编码" width="110" align="center"></vxe-table-column>
    <vxe-table-column field="sapStoItemNo" title="项目行号" width="80" align="center"></vxe-table-column>
    <vxe-table-column
      align="center"
      :width="80"
      field="quantity"
      title="订单数量"
    >
      <template v-slot="{row}">
        <el-link
          :underline="false"
          type="primary"
          :style="{textDecoration: 'underline'}"
          @click="handleQuantity(row)"
        >
        {{ row.quantity }}
        </el-link>
      </template>
    </vxe-table-column>
    <vxe-table-column
      v-for="col in stoColumnPart3"
      align="center"
      :width="col.width"
      :key="col.prop"
      :field="col.prop"
      :title="col.label">
    </vxe-table-column>
    <vxe-table-column
      align="center"
      :width="160"
      field="deliveryQty"
      title="交货数量"
    >
      <template slot-scope="{row}">
        <el-input-number
          v-model="row.deliveryQty"
          style="width:100%"
          :min="0"
          :max="row.confirmInStockQty"
          :step="1"
        />
      </template>
    </vxe-table-column>
    <vxe-table-column
      v-for="col in stoColumnPart4"
      align="center"
      :width="col.width"
      :key="col.prop"
      :field="col.prop"
      :title="col.label">
    </vxe-table-column>
    <vxe-table-column
      align="center"
      width="80"
      field="purchaseGroup"
      title="采购组"
    >
      <template v-slot="{row}">
        <span>{{getDictName("purchaseGroup", row.purchaseGroup)}}</span>
      </template>
    </vxe-table-column>
    <vxe-table-column
      v-for="col in stoColumnPart2"
      align="center"
      :width="col.width"
      :key="col.prop"
      :field="col.prop"
      :title="col.label"
    >
    </vxe-table-column>
    <vxe-table-column field="skuDesc" title="商品描述" width="230" align="center" show-overflow></vxe-table-column>
    <vxe-table-column
      align="center"
      :width="230"
      field="factory"
      title="工厂"
      show-overflow
    >
      <template v-slot="{row}">
        <span>{{row.factory}}{{getDictName("factory", row.factory)}}</span>
      </template>
    </vxe-table-column>
    <vxe-table-column
      align="center"
      :width="130"
      field="receiptPosition"
      title="接收库位"
      show-overflow
    >
      <template v-slot="{row}">
        <span>{{row.receiptPosition}}{{getDictName("position", row.receiptPosition)}}</span>
      </template>
    </vxe-table-column>
    <vxe-table-column
      align="center"
      :width="130"
      field="deliveryPosition"
      title="发货库位"
      show-overflow
    >
      <template v-slot="{row}">
        <span>{{row.deliveryPosition}}{{getDictName("position", row.deliveryPosition)}}</span>
      </template>
    </vxe-table-column>
    <vxe-table-column
      v-for="col in stoColumnPart1"
      align="center"
      :width="col.width"
      :key="col.prop"
      :field="col.prop"
      :title="col.label">
    </vxe-table-column>
    <vxe-table-column
      :width="160"
      fixed="right"
      align="center"
      field="deliveryNo"
      title="交货单号"
    >
      <template slot-scope="{row}">
        <el-link type="primary" @click="toDtl(row)" v-if="row.deliveryNo">
          {{row.deliveryNo}}
        </el-link>
      </template>
    </vxe-table-column>
  </vxe-table>
  <div class="pagination-container">
    <pagination
      v-show="stoTotal > 0"
      :total="stoTotal"
      :page.sync="stoQueryData.page"
      :limit.sync="stoQueryData.size"
      :page-sizes="[50, 100, 150, 200, 250, 300]"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="queryStoData"
    />
  </div>
  <ProductPlan
    :show-dialog.sync="showProductPlan"
    :list="itemPlanDTOList"
    :itemRow="itemRow"
  />
</div>
</template>

<script>
import moment from 'moment'
import { list, create, stoFactoryList } from '@/api/sto'
import { stoOrderDetailPlan, triggerStoAtp } from '@/api/orderSale'
import Pagination from '@/components/Pagination'
import { requestWithLoading } from './utils'
import ProductPlan from './components/sto/ProductPlan'
import { getDeliveryNoteTips } from '@/utils/order.js'

export default {
  components: {
    ProductPlan,
    Pagination
  },
  data () {
    return {
      stoQueryData: this.getDefaultQueryParams(),
      stoListData: [],
      stoTotal: 0,
      tableSelection: [],
      showProductPlan: false,
      itemPlanDTOList: [],
      itemRow: null,
      stoColumnPart1: [
        { prop: 'creator', label: '创建人', width: '90' },
        { prop: 'createDate', label: '创建日期', width: '180' }
      ],
      stoColumnPart2: [
        { prop: 'deliveryDate', label: '交货日期', width: '110' }
      ],
      stoColumnPart3: [
        { prop: 'confirmInStockQty', label: '确认在库数量', width: '110' },
        { prop: 'unDeliveryQty', label: '未清数量', width: '80' }
      ],
      stoColumnPart4: [
        { prop: 'unit', label: '单位', width: '60' }
      ],
      isLoading: false,
      filterFactoryList: [],
      rules: {
        createTime: [
          { required: true, message: '请输入交货时间', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    factoryList () {
      const facList = (this.dictList && this.dictList['factory']) ? this.dictList['factory']
        .filter(item => {
          return item.parentCode === ''
        }) : []
      const results = []
      this.filterFactoryList.forEach(item => {
        const foundItem = facList.find(fac => fac.code === item)
        if (foundItem && foundItem.code) {
          results.push(foundItem)
        }
      })
      return results
    }
  },
  mounted () {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    stoFactoryList().then(res => {
      if (res && res.code === 200) {
        this.filterFactoryList = res.data
      }
    })
    if (this.$route?.query?.orderNo && !this.stoQueryData.sapStoNos) {
      this.$set(this.stoQueryData, 'sapStoNos', this.$route?.query?.orderNo)
    }
    this.queryStoData()
  },
  methods: {
    checCheckbox ({ row }) {
      const { deliveryQty } = row
      return deliveryQty > 0
    },
    getDefaultQueryParams () {
      const startDate = moment().subtract(7, 'days').format('yyyy-MM-DD')
      const endDate = moment().format('yyyy-MM-DD')
      return {
        page: 1,
        size: 200,
        createTime: [startDate, endDate]
      }
    },
    toDtl (row) {
      this.$router.push({
        path: `/orderDelivery/stoDetail/${row.deliveryNo}`
      })
    },
    getDictName (type, code) {
      const group = (code && this.dictList && this.dictList[type]) ? this.dictList[type].find(item => {
        return item.code === code
      }) : ''
      return group ? group.name : ''
    },
    getPositionList (factoryCode) {
      return (factoryCode && this.dictList) ? this.dictList['position'].filter(item => {
        return item.parentCode === factoryCode && item.code !== -1
      }) : []
    },
    queryStoData (callback) {
      const { sapStoNos, createTime, page, size } = this.stoQueryData
      const params = {
        ...this.stoQueryData,
        current: page,
        size: size,
        sapStoNos: sapStoNos ? sapStoNos.split(/\s+|,|，|\r?\n|\t/).join(',') : ''
      }
      delete params.createTime
      if (createTime && createTime.length === 2) {
        const m1 = moment(createTime[1])
        const m2 = moment(createTime[0])
        const d = m1.diff(m2, 'months', true)
        if (!sapStoNos && d > 3) {
          this.$alert('只允许查询三个月内的数据！', '错误', {
            type: 'error'
          })
          return
        }
        params['createBeginTime'] = createTime[0]
        params['createEndTime'] = createTime[1]
      }
      requestWithLoading(this, list(params), data => {
        data.records = data.records || []
        this.stoListData = data.records.map(item => {
          const { confirmInStockQty } = item
          return {
            ...item,
            deliveryQty: confirmInStockQty
          }
        })
        this.stoTotal = data.total
        this.tableSelection = []
        if (callback) {
          callback()
        }
      })
    },
    onSubmit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.queryStoData()
        } else {
          return false
        }
      })
    },
    onReset (formName) {
      this.$refs[formName].resetFields()
      this.stoQueryData = this.getDefaultQueryParams()
      this.stoListData = []
      this.queryStoData()
    },
    async getDeliveryNoteTips () {
      const situationItemList = this.tableSelection.map(item => {
        return {
          position: item.deliveryPosition,
          deliveryPosition: item.receiptPosition,
          soItemNo: item.sapStoItemNo,
          soNo: item.sapStoNo
        }
      })
      const data = {
        createDnType: 'stoCreate',
        situationItemList
      }
      try {
        const res = await getDeliveryNoteTips(this, data)
        return Promise.resolve(res)
      } catch (err) {
        return Promise.reject(err);
      }
    },
    onCreateDN () {
      if (this.tableSelection && this.tableSelection.length > 0) {
        this.$confirm('确认对已勾选的调拨单行进行交货吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          var tips;
          try {
            tips = await this.getDeliveryNoteTips()
          } catch (err) {
            return;
          }
          const referOrderInfos = this.tableSelection.map(item => {
            const {
              factory, deliveryPosition, receiptPosition, sapStoItemPlanNo,
              sapStoItemNo, deliveryQty, sapStoNo, shippingPosition, shippingCondition, sku
            } = item
            return {
              factory,
              referOrderItemDetailNo: sapStoItemPlanNo,
              shippingCondition,
              fromPosition: deliveryPosition,
              toPosition: receiptPosition,
              referOrderItemNo: sapStoItemNo,
              lfimg: deliveryQty,
              referOrderNo: sapStoNo,
              shippingPoint: shippingPosition,
              sku,
              epidemicSituationFlag: tips?.epidemicSituationFlag || 'n'
            }
          })
          requestWithLoading(this, create({
            referOrderInfos
          }), data => {
            const { dnInfos } = data
            if (dnInfos && dnInfos.length > 0) {
              const failRow = dnInfos.filter(item => !item.success)
              const failCount = failRow.length
              const successCount = dnInfos.length - failRow.length
              let msg = `交货成功${successCount}行，失败${failCount}行。`
              if (failCount > 0) {
                msg += '失败原因如下：<br>'
                failRow.forEach((failItem, idx) => {
                  const { referOrderNo, referOrderItemNo, errorMessage } = failItem
                  msg += `${idx + 1}: ${referOrderNo}调拨单${referOrderItemNo}的失败原因：${errorMessage}<br>`
                })
              }
              this.$alert(msg, '创建完成', {
                confirmButtonText: '确定',
                dangerouslyUseHTMLString: true,
                callback: action => {
                  const updateDnNoCallback = () => {
                    dnInfos.forEach(dnItem => {
                      const { dnNo, referOrderNo, referOrderItemNo } = dnItem
                      if (dnNo) {
                        const idx = this.stoListData.findIndex(item =>
                          item.sapStoNo === referOrderNo && referOrderItemNo === item.sapStoItemNo)
                        if (idx >= 0) {
                          this.$set(this.stoListData, idx, {
                            ...this.stoListData[idx],
                            deliveryNo: dnNo
                          })
                        }
                      }
                    })
                  }
                  this.queryStoData(updateDnNoCallback)
                }
              })
            }
          })
        }).catch(() => {
        })
      }
    },
    onTableSelectionChange ({ records }) {
      this.tableSelection = records
    },
    onTableSelectionAllChange ({ records }) {
      this.tableSelection = records
    },
    handleQuantity (item) {
      const { sapStoNo, sapStoItemNo } = item
      this.itemRow = item
      requestWithLoading(this, stoOrderDetailPlan({
        sapStoNo,
        sapStoItemNo
      }), data => {
        this.showProductPlan = true
        this.itemPlanDTOList = data
      })
    },
    triggerAtp (id) {
      requestWithLoading(this, triggerStoAtp(id), data => {
        this.$message.success('触发成功')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sto {
 &-query {
   padding: 10px 0;
   .form-item {
     width: 192px;
   }
   .form-item-long {
     width: 540px;
   }
 }
 .btn-row {
  margin-left: 20px;
 }
 &-btn {
   width: 90px;
 }
 &-row {
   font-size: 14px;
   color: #909399;
   padding: 20px;
   display: flex;
   align-items: center;
   justify-content: space-between;
 }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}
</style>
