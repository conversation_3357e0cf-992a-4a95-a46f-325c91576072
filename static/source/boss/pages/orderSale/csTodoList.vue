<template>
    <el-tabs v-model="activeName" @tab-click="handleClick">

      <el-tab-pane label="待对账数据" name="basisList">
        <BasisList :diableInitialFetchList="true" :condition="{invoicingStatus:['A','B'],status:['2','3'],daysLeftFlag:true, daysLeftOrder:0, idOrder:''}" :visible="false"/>
      </el-tab-pane>

      <el-tab-pane label="待确认数据" name="statementList">
        <StatementList :diableInitialFetchList="true" :condition="{status:'2',checkTotalAmountTaxOrder:1, idOrder:''}" :visible="false"/>
      </el-tab-pane>
      <el-tab-pane label="待开票数据" name="pendingInvoiceList">
        <PendingInvoiceList :diableInitialFetchList="true" :condition="{checkAmountTaxOrder:1, idOrder:'', fkstk:'A,B'}" :visible="false"/>
      </el-tab-pane>

    </el-tabs>
</template>
<script>
import BasisList from '@/pages/accountStatement/basisList'
import StatementList from '@/pages/accountStatement/statementList'
import PendingInvoiceList from '@/pages/accountStatement/pendingInvoiceList'

import { getUnOrderPurchaseOrder as unOrderPurchaseOrderList,
  getSupplierUnconfirmOrder as supplierUnconfirmOrderList,
  getUnmetDeliveryOrder as unmetDeliveryOrderList,
  getReadyForDnOrder as readyForDnOrderList,
  getDirectDeliverySupplierAckDnOrder as directDeliverySupplierAckDnOrderList,
  getunCheckSignBackOrder as unCheckSignBackOrderList,
  getUnOverDueOrder as UnOverDueOrderList,
  getAlmostOverdueUndeliveryOrder as almostOverdueUndeliveryOrderList,
  getAlmostOverdueDeliveryOrder as almostOverdueDeliveryOrderList,
  getAlmostOverdueInDaysOrder as almostOverdueInDaysOrderList
} from '@/api/csToList'

let colums = [
  {
    field: 'customerNo',
    label: '客户编号',
    width: 100
  },
  {
    field: 'customerName',
    label: '客户名称',
    width: 200
  },
  {
    field: 'soNo',
    label: 'OMS订单号',
    width: 200
  },
  {
    field: 'soItemNo',
    label: 'OMS行号',
    width: 100
  },
  {
    field: 'materiel',
    label: '商品编码',
    width: 100
  },
  {
    field: 'materielName',
    label: '物料描述',
    width: 250
  },
  {
    field: 'needCollect',
    label: '是否集货',
    width: 80
  },
  {
    field: 'quantity',
    label: '订单数量',
    width: 80
  },
  {
    field: 'unClearedQty',
    label: '未交货数量',
    minWidth: 80
  },
  {
    field: 'warehouseUnClearedQty',
    label: '集货仓未交数量',
    minWidth: 110
  },
  {
    field: 'basicUnitName',
    label: '单位',
    width: 80
  },
  {
    field: 'soCreateDate',
    label: '销售订单日期',
    width: 100

  },
  {
    field: 'deliveryDate',
    label: '请求发货日期',
    width: 100
  },
  {
    field: 'unPurchaseReason',
    label: '未下采购单说明',
    width: 200
  },
  {
    field: 'planDeliveryDate',
    label: '预计到货日期',
    width: 100
  },
  {
    field: 'arrivalDate',
    label: '预计送达日期',
    width: 100
  },
  {
    field: 'purchaseRemarks',
    label: '采购文本',
    width: 200
  },
  {
    field: 'vcDeliveryDate',
    label: '供应商发货日期',
    width: 110
  },
  {
    field: 'purchaseLogisticInfo',
    label: '采购物流信息',
    width: 200
  },
  {
    field: 'workNoList',
    label: '对应工单号',
    width: 250
  }
]

export default {
  name: 'csTodoList',
  data() {
    return {
      colums,
      tabName: 'important',
      tableData: {
        total: '',
        list: [],
        colums: [],
        loading: false
      },
      importantTabs: [
        {
          label: '未下采购单',
          name: 'unOrderPurchaseOrder'
        },
        {
          label: '供应商未确认',
          name: 'supplierUnconfirm'
        },
        {
          label: '交期无法满足',
          name: 'unmetDelivery'
        },
        {
          label: '待创建交货单',
          name: 'readyForDn'
        },
        // {
        //   label: '直发可确认收货',
        //   name: 'directDeliverySupplierAckDn'
        // },
        {
          label: '待检查签单情况',
          name: 'unCheckSignBack'
        },
        {
          label: '订单已逾期',
          name: 'UnOverDue'
        },
        {
          label: '临近三天逾期且供应商未发货',
          name: 'almostOverdueUndelivery'
        }
      ],
      secondaryTabs: [
        {
          label: '临近三天逾期且供应商已发货',
          name: 'almostOverdueDelivery'
        },
        {
          label: '临近七天可能逾期',
          name: 'almostOverdueInDays'
        }
      ],
      activeName: 'basisList'
    };
  },
  created() {
    this.getList(this.activeName, { current: 1, size: 20 })
  },
  methods: {
    tabClick (tab) {
      const { name } = tab
      if (name === 'important') {
        this.activeName = 'unOrderPurchaseOrder'
        this.getList(this.activeName, { current: 1, size: 20 })
      }
      if (name === 'secondary') {
        this.activeName = 'almostOverdueDelivery'
        this.getList(this.activeName, { current: 1, size: 20 })
      }
    },
    handleClick(tab) {
      if (tab.name === 'overdue') {
        const isUat = /test|local/gim.test(location.href)
        const path = '/so/csWorkbench?open=true&tab=overdue'
        const url = isUat ? 'https://boss-uat-4.zkh360.com' : 'https://boss.zkh360.com'
        return window.open(`${url}${path}`)
      }
      this.getList(this.activeName, { current: 1, size: 20 })
    },
    paginationChange(tablePagination) {
      const { activeName } = tablePagination
      const queryParams = {}
      Object.assign(queryParams, { current: tablePagination.pageNo, size: tablePagination.pageSize })
      this.getList(activeName, queryParams)
    },
    getList(activeName, params) {
      this.tableData.loading = true
      let searchApi = ''
      const list = this.colums.filter(item => item.field !== 'unPurchaseReason' && item.field !== 'planDeliveryDate' && item.field !== 'purchaseRemarks' && item.field !== 'vcDeliveryDate' && item.field !== 'purchaseLogisticInfo' && item.field !== 'needCollect' && item.field !== 'warehouseUnClearedQty' && item.field !== 'arrivalDate');
      switch (activeName) {
        case 'unOrderPurchaseOrder':
          searchApi = unOrderPurchaseOrderList;
          this.tableData.colums = this.colums.filter(item => item.field !== 'planDeliveryDate' && item.field !== 'purchaseRemarks' && item.field !== 'vcDeliveryDate' && item.field !== 'purchaseLogisticInfo' && item.field !== 'needCollect' && item.field !== 'warehouseUnClearedQty' && item.field !== 'arrivalDate');
          break;
        case 'supplierUnconfirm':
          searchApi = supplierUnconfirmOrderList;
          this.tableData.colums = list;
          break;
        case 'unmetDelivery':
          searchApi = unmetDeliveryOrderList;
          this.tableData.colums = this.colums.filter(item => item.field !== 'unPurchaseReason' && item.field !== 'purchaseRemarks' && item.field !== 'vcDeliveryDate' && item.field !== 'purchaseLogisticInfo' && item.field !== 'needCollect' && item.field !== 'warehouseUnClearedQty' && item.field !== 'arrivalDate');
          break;
        case 'readyForDn':
          searchApi = readyForDnOrderList;
          this.tableData.colums = list;
          break;
        case 'directDeliverySupplierAckDn':
          searchApi = directDeliverySupplierAckDnOrderList;
          this.tableData.colums = list;
          break;
        case 'unCheckSignBack':
          searchApi = unCheckSignBackOrderList;
          this.tableData.colums = list;
          break;
        case 'UnOverDue':
          searchApi = UnOverDueOrderList;
          this.tableData.colums = this.colums.filter(item => item.field !== 'unPurchaseReason');
          break;
        case 'almostOverdueUndelivery':
          searchApi = almostOverdueUndeliveryOrderList;
          this.tableData.colums = this.colums.filter(item => item.field !== 'unPurchaseReason' && item.field !== 'vcDeliveryDate' && item.field !== 'purchaseLogisticInfo' && item.field !== 'needCollect' && item.field !== 'warehouseUnClearedQty' && item.field !== 'arrivalDate');
          break;
        case 'almostOverdueDelivery':
          searchApi = almostOverdueDeliveryOrderList;
          this.tableData.colums = list;
          break;
        case 'almostOverdueInDays':
          searchApi = almostOverdueInDaysOrderList;
          this.tableData.colums = list;
          break;
        // default:
        //   searchApi = unOrderPurchaseOrderList;
        //   this.tableData.colums = this.colums.filter(item => item.field !== 'planDeliveryDate' && item.field !== 'purchaseRemarks' && item.field !== 'vcDeliveryDate' && item.field !== 'purchaseLogisticInfo' && item.field !== 'needCollect' && item.field !== 'warehouseUnClearedQty' && item.field !== 'arrivalDate');
      }
      if (searchApi === '') return
      return searchApi(params)
        .then(res => {
          this.tableData.loading = false
          if (res && res.code === 200) {
            this.tableData.total = res.data.total
            this.tableData.list = res.data.records
          } else {
            var errorMsg = res.msg
            if (res && res.data) {
              errorMsg = `${res.msg}
              ${res.data}
              `
            }
            this.$message.error(errorMsg);
          }
        })
    }
  },
  components: {
    BasisList,
    StatementList,
    PendingInvoiceList
  }
};
</script>

<style scoped lang="scss">
.box-card {
  margin-top: 10px;
}

</style>
