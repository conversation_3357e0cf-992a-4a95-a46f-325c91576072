export function isServiceOrder (type) {
  return type === 'Z005' || type === 'Z013'
}

export function isFreeOrder (type) {
  return type === 'Z006' || type === 'Z007'
}

export function isForecastOrder (type) {
  return type === 'Z002' || type === 'Z014'
}

export function isEnableDirectDeliverySupplier (orderType) {
  return orderType === 'Z018' || orderType === 'Z009' || orderType === 'Z014' || orderType === 'ZEV1' ||
    orderType === 'ZEV2' || orderType === 'ZEV3' || orderType === 'ZEV4'
}
export function isInnerOrderReason (orderReason) {
  return orderReason === '050'
}
