import * as utils from '@/utils'

export function getFactoryList (distributionChannel, salesOrganization, dictList) {
  if (distributionChannel && salesOrganization) {
    const f = dictList['factory']
      .filter(item => {
        return item.parentCode === `${salesOrganization}-${distributionChannel}`
      })
    return f
  }
  return []
}

export function getFactorySpecialList (factoryCode, dictList) {
  if (factoryCode) {
    const f = dictList['factory']
      .filter(item => {
        return item.code === factoryCode
      })
    return f[0]
  }
  return {}
}

export function calculateTaxPrice (freeTaxPrice, taxRate) {
  return utils.formatPrice(freeTaxPrice * (1 + taxRate))
}

export function calculateFreeTaxPrice (taxPrice, taxRate) {
  return utils.formatPrice(taxPrice / (1 + taxRate))
}

export function calculatePriceTotalAmount (skuList) {
  const result = skuList.reduce((acc, sku) => {
    const newValue = {}
    newValue.taxedTotal = sku.taxPrice * sku.quantity + acc.taxedTotal
    newValue.unTaxedTotal = sku.freeTaxPrice * sku.quantity + acc.unTaxedTotal
    return newValue
  },
  { taxedTotal: 0, unTaxedTotal: 0 })
  return result
}

// 通过字典获得当前请求url
export function getOrderOptUrl (dictList, orderType, optType) {
  const orderOptUrls = dictList['orderOptUrl']
  if (orderOptUrls && orderOptUrls.length > 0) {
    const foundDict = orderOptUrls.find(dict => {
      if (dict.parentCode) {
        const itemInfo = dict.parentCode.split('-')
        if (itemInfo && itemInfo.length > 0) {
          return (itemInfo[0] === orderType && itemInfo[1] === optType)
        }
      }
      return false
    })
    if (foundDict) {
      return foundDict.code
    }
  }
  return ''
}

export function requestWithLoading (el, promiseFun, successCb) {
  const loading = el.$loading({
    background: 'rgba(0, 0, 0, 0.8)',
    lock: true
  })
  promiseFun.then(res => {
    loading.close()
    if (res && /20\d/.test(res.code)) {
      if (successCb) {
        successCb(res.data)
      }
    } else if (res && (res.data || res.msg)) {
      const content = res.data || res.msg
      const templ = `<pre>${content}</pre>`
      el.$alert(templ, '错误', {
        type: 'error',
        dangerouslyUseHTMLString: true
      })
    }
  }).catch(error => {
    loading.close()
    if (error.response && error.response.data) {
      const { message } = error.response.data
      if (message) {
        el.$alert(`<pre>${message}</pre>`, '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    }
  })
}

export function getDirectDeliverySupplierList (dictList, orderType, salesOrganization) {
  if (orderType && salesOrganization) {
    const code = `${orderType}_${salesOrganization}`
    const dict = dictList['orderTypeOfDirectDeliverySupplierList'].find(item => {
      return item.parentCode === code
    })
    if (dict && dict.code) {
      return dictList['directDeliverySupplier'].filter(item =>
        dict.code.split(',').indexOf(item.code) > -1)
    } else {
      return dictList['directDeliverySupplier'].filter(item =>
        parseInt(item.code, 10) < 2)
    }
  } else {
    return dictList['directDeliverySupplier'].filter(item =>
      parseInt(item.code, 10) < 2)
  }
}
export function getDisabledDate (time, specifiedReceiptDayOfWeek, receiptTimeCategory, check = true) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  // 今天及以前的日期禁用
  if (time.getTime() <= today.getTime()) return true;
  // Z002/Z014/叫料订单不卡控周期性发货
  if (check) {
    let specifiedDays = [];
    const newSpecifiedReceiptDayOfWeek = specifiedReceiptDayOfWeek?.filter(day => !!day)
    if (newSpecifiedReceiptDayOfWeek?.includes('0') || !newSpecifiedReceiptDayOfWeek?.length) {
      specifiedDays = ['01', '02', '03', '04', '05', '06', '07']
    } else {
      specifiedDays = newSpecifiedReceiptDayOfWeek;
    }
    // 勾选了工作日与周末均可收货，表示所有日期都可选
    if (receiptTimeCategory === 'X' || receiptTimeCategory === true) {
      specifiedDays = specifiedDays?.filter(day => day !== '0').map(day => parseInt(day))
    } else {
      specifiedDays = specifiedDays?.filter(day => !['0', '06', '07'].includes(day)).map(day => parseInt(day))
    }
    // 指定收货日
    if (specifiedDays) {
      // 禁用非指定收货日的日期
      if (time.getDay() !== 0 && !specifiedDays.includes(time.getDay())) {
        return true;
      }
      // 对于星期日，只有在指定的收货日期中才是可选的
      if (time.getDay() === 0 && !specifiedDays.includes(7)) {
        return true;
      }
    }
  }
  return false;
}

function tryTrim(str) {
  return (str && str.trim && str.trim()) || str
}
// 比较客户物料关系的字段是否相同
export function isDiffCustomerRelation (obj1, obj2) {
  try {
    const keys1 = Object.keys(obj1);
    for (let key of keys1) {
      const value1 = obj1[key]
      const value2 = obj2[key]
      // 输入为空时不比较
      if (value2 && tryTrim(value1) !== tryTrim(value2)) {
        return true;
      }
    }
    return false;
  } catch (err) {
    console.log(err)
  }
}

export function findNormalItemInDictList(dictList, code) {
  return dictList?.find(
    (item) => item.status === 'normal' && item.code === code
  );
}
