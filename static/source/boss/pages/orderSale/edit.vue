<template>
  <div class="app-container">
    <!-- 页面的所有弹框组件 -->
    <ShowMorePosition
      :show-dialog.sync="ShowMorePosition"
      :rowDetail="rowDetail"
    />
    <EditCustomer
      :show-dialog.sync="showEditCustomer"
      :orderDetail="orderDetail"
      :clientDetail="clientDetail"
      :invoiceReceiverList="invoiceReceiverList"
      @onSubmit="submitCustomer"
      @getDeliveryDate="getDeliveryDate"
    />
    <ProductDetail
      :sku-detail="skuDetail"
      :client-detail="clientDetail"
      :show-dialog.sync="showProductDetail"
      :positionList="positionList"
      :orderDetail="orderDetail"
      :formatSKU="formatSKU"
      @submitProductDetail="submitProductDetail"
      @getWarehouseCodeList="getWarehouseCodeList"
    />
    <ProductPlan
      :skuDetail="skuDetail"
      :quantityList="quantityList"
      :show-dialog.sync="showProductPlan"
      @submitProductPlan="submitProductPlan"
    />

    <EditOrderMore
      :orderDetail="orderDetail"
      :client-detail="clientDetail"
      :show-dialog.sync="showEditOrderMore"
      :goodsList="goodsList"
      @changeGoodsList="changeGoodsList"
      @submitInvoice="submitInvoice"
      @submitDelivery="submitDelivery"
      @getDeliveryDate="getDeliveryDate"
    />
    <el-form ref="orderForm" label-width="120px" :rules="rules" :model="orderDetail">
      <!-- 基本信息 -->
      <DividerHeader>基本信息</DividerHeader>
      <div class="baseInfo">
        <div class="title">
          <span
            style="font-size: 20px;"
          >{{ orderDetail.salesOrganization | dict(dictList,'salesOrganization') }}</span>
          <span
            style="font-size: 20px;margin-left: 15px;"
          >{{ orderTypeName }}</span>
        </div>
        <el-row :gutter="10" v-for="(item, idx) in baseInfo" :key="'baseInfo-row'+idx">
          <el-col :span="children.span" v-for="(children, childIdx) in item" :key="'baseInfo-row'+childIdx">
            <span v-if="children.prop==='orderStatus'">
              {{ children.label }}: {{ orderDetail[children.prop] | orderStatus }}
            </span>
            <span :label="children.label" v-else-if="children.prop==='saleOrgVO'">
              {{ children.label }}: {{ (clientDetail.saleOrgVO || {}).distributionChannelName }}
            </span>
            <span :label="children.label" v-else>
              {{ children.label }}: {{ orderDetail[children.prop] }}
            </span>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">
            EVM运营: {{ clientDetail.evmOperator }}
          </el-col>
          <el-col :span="6">
            开票冻结：
            <el-tag v-for="df in freezeBillReasonList"
                    :key="'invoiceFreeze'+df"
                    style="margin-right:10px"
                    type="success"
            >
              {{ df | dict(dictList,'invoiceFreeze') }}
            </el-tag>
            <el-tag v-if="freezeBillReasonList.length===0" type="info">无</el-tag>
          </el-col>
          <el-col :span="12">
            交货冻结:
            <el-tag v-for="df in freezeReasonList"
                    :key="'deliveryFreeze'+df"
                    style="margin-right:10px"
                    type="success"
            >
              {{ df | dict(dictList,'deliveryFreeze') }}
            </el-tag>
            <el-tag v-if="freezeReasonList.length===0" type="info">无</el-tag>
          </el-col>
        </el-row>
      </div>
      <DividerHeader>订单信息</DividerHeader>
      <div class="orderInfo">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="外围订单号" prop="orderNo">
              <el-input
                placeholder="外围订单号"
                v-model="orderDetail.orderNo"
                maxlength="50"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户订单号">
              <el-input
                placeholder="客户订单号"
                v-model="orderDetail.customerReferenceNo"
                maxlength="35"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="订单来源" prop="orderSource">
              <el-select
                v-model="orderDetail.orderSource"
                :disabled="orderDetail.orderSource === 'HQZY'"
                placeholder="请选择"
                style="width:100%"
                clearable
              >
                <el-option
                  v-for="item in (dictList['orderSource'] || [])"
                  :disabled="item.code === 'HQZY'"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <SelectOrderService v-if="JSON.stringify(this.orderServiceDict) !== '{}'" v-model="orderDetail.specifiedReceiptDayOfWeek" field="specifiedReceiptDayOfWeek"  :selectStyle="`width:100%`" />
          </el-col>
          <el-col :span="4" style="text-align:center;margin-top: 5px;">
            <el-checkbox
              v-model="orderDetail.receiptTimeCategory"
              label="工作日与周末均可收货"
              true-label="X"
              false-label="Z"
            />
          </el-col>
        </el-row>
      </div>
      <!-- 客户信息 -->
      <DividerHeader>客户信息</DividerHeader>
      <div class="clientInfo">
        <div class="ba-row-between title">
          <div class="ba-row-start clientSum">
            <span style="font-size: 20px;">{{ orderDetail.customerNo || clientDetail.customerNumber }}</span>
            <span style="font-size: 20px;">{{ orderDetail.customerName || clientDetail.customerName }}</span>
            <div>
              <el-tag v-if="clientDetail && clientDetail.businessPartnerGroupName" effect="dark" type="">
                {{ clientDetail.businessPartnerGroupName }}
              </el-tag>
              <el-tag v-if="clientDetail && clientDetail.customerClassificationName" effect="dark" type="success">
                {{ clientDetail.customerClassificationName }}
              </el-tag>
              <el-tag v-if="clientDetail && clientDetail.customerNatureName" effect="dark" type="warning">
                {{ clientDetail.customerNatureName }}
              </el-tag>
            </div>
          </div>
          <el-button type="text" @click="showEditCustomer=true">修改客户详情&gt;&gt;</el-button>
        </div>
        <el-row :gutter="10">
          <el-col :span="12">
            销售范围：{{ `${orderDetail.salesOrganization}/${orderDetail.distributionChannel}/${orderDetail.productGroup}` }}&nbsp;
            {{
              orderDetail.salesOrganization | dict(dictList,'salesOrganization')
            }}，{{ orderDetail.distributionChannel | dict(dictList,'distributionChannel') }}，{{ orderDetail.productGroup | dict(dictList,'productGroup') }}
          </el-col>
          <el-col :span="12">
            客户订单号：{{ orderDetail.customerReferenceNo }}
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="6">客户来源：{{ clientDetail.customerSourceName }}</el-col>
          <el-col :span="6">付款条件：{{ paymentTermName }}</el-col>
          <el-col :span="4">含税/未税：{{ orderDetail.isTax === '0' ? '未税' : '含税' }}</el-col>
          <el-col :span="8" style="margin-top: -5px" v-if="isGBB">
            <el-form-item prop="customerReferenceDate" label="客户参考日期">
              <el-date-picker
                v-model="orderDetail.customerReferenceDate"
                clearable
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 商品信息 -->
      <DividerHeader>商品信息</DividerHeader>
      <SelectGoods @selectedSku="handleAdd" @addNullSku="addNullSku" :orderData="orderDetail" v-if="isShow" />
      <div class="merchant">
        <div class="ba-row-between merchantInfo">
          <div class="merchantInfo-row">
            <div>
              <span>折前订单含税金额：{{ currencySymbol }}{{ allTaxTotalPrice }}</span>
              <span>折前订单未税金额：{{ currencySymbol }}{{ allFreeTotalPrice }}</span>
            </div>
            <div>
              <span>折后订单含税金额：{{ currencySymbol }}{{ allTaxTotalDiscountPrice }}</span>
              <span>折后订单未税金额：{{ currencySymbol }}{{ allFreeTotalDiscountPrice }}</span>
            </div>
          </div>
          <div class="ba-row-between">
            <!-- <el-button type="primary" :disabled="!multipleSelection.length">保存草稿</el-button> -->
            <div v-if="orderType==='Z001'&&orderDetail.updateByExcelSwitch">
              <el-button
                type="primary"
                plain
                style="margin:0 10px"
                @click="downloadMultiSku"
              >
                下载批量修改模板
              </el-button>
              <el-upload
                class="upload"
                :action="`/api-opc/v1/excel/batchUpdateSku${uploadQueryUrl}`"
                :disabled="false"
                :multiple="false"
                :limit="1"
                :show-file-list="true"
                :accept="acceptFileType.commonType"
                :before-upload="handleBeforeUpload"
                :on-success="handleUploadSuccess"
              >
                <el-button size="small" type="primary">上传批量修改模板</el-button>
              </el-upload>
            </div>

          </div>
        </div>
        <el-table :data="itemList" border>
          <!-- <el-table-column type="selection"></el-table-column> -->
          <el-table-column label="项目行" width="100" align="center" fixed="left">
            <template slot-scope="{row}">
              {{ row.soItemNo || row.idx || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="materiel" label="商品编号" width="100" align="center" fixed="left" />
          <el-table-column prop="sapMaterialName" label="商品描述" width="300" align="center">
            <template slot-scope="{row}">
              <div v-if="row.sapMaterialName" class="productDesc">
                <img :src="row.picUrl||defaultImage">
                <div>
                  <p>{{ row.sapMaterialName }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="factory" width="280" align="center">
            <template slot="header">
              <RequiredColTitle>工厂</RequiredColTitle>
            </template>
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.factory"
                size="mini"
                placeholder="请选择"
                style="width:100%"
                :disabled="isFactoryDisabled"
                @change="val => changeFactory(val, scope.$index)">
                <el-option
                  v-for="item in factoryList"
                  :key="item.code"
                  :label="(item.code > 0 ? item.code : '')+item.name"
                  :value="item.code"
                  :disabled="isFactoryDisable(scope.row, item.code)"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="total" width="200" align="center">
            <template slot="header">
              <RequiredColTitle>数量</RequiredColTitle>
            </template>
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row.quantity"
                size="mini"
                :min="0"
                :max="scope.row.referType === '03' ? scope.row.replenishNum : 1000000000"
                :step="1"
                :precision="0"
                @change="val=>changeAmount(val,scope.$index, scope.row)"
              />

            </template>
          </el-table-column>
          <el-table-column align="center" prop="clearedQty" label="已参考数量" width="100" v-if="isForecast" />
          <el-table-column align="center" prop="clearedQty" label="已交货数量" width="100" v-else>
            <template slot-scope="{row}">{{ row.needCollect ? row.deliveryQty : row.clearedQty }}</template>
          </el-table-column>
          <el-table-column prop="taxPrice" width="200" align="center">
            <template slot="header">
              <OptRequiredRowTitle
                title="含税单价"
                :isRequired="isPriceRequired"
              />
            </template>
            <template slot-scope="scope">
              <el-input-number
                v-if="orderDetail.isTax === '1'"
                v-model="scope.row.taxPrice"
                style="width:100%"
                :min="0"
                :precision="6"
                :step="1"
                :disabled="isFree"
                @change="changeGoodsPrice('tax', scope.$index)"
              />
              <span v-else>{{ scope.row.taxPrice.toFixed(6) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="freeTaxPrice" width="200" align="center">
            <template slot="header">
              <OptRequiredRowTitle
                title="未税单价"
                :isRequired="isPriceRequired"
              />
            </template>
            <template slot-scope="scope">
              <el-input-number
                v-if="orderDetail.isTax === '0'"
                v-model="scope.row.freeTaxPrice"
                style="width:100%"
                :min="0"
                :precision="6"
                :step="1"
                :disabled="isFree"
                @change="changeGoodsPrice('freeTax', scope.$index)"
              />
              <span v-else>{{ scope.row.freeTaxPrice.toFixed(6) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="taxRate" label="税率" width="60" align="center">
            <template slot-scope="scope">{{ scope.row.taxRate != null ? `${Math.round(scope.row.taxRate * 100)}%` : '--' }}</template>
          </el-table-column>
          <el-table-column prop="taxAmount" label="整行税额" width="120" align="center" />
          <el-table-column prop="currency" label="货币" width="80" align="center" />
          <el-table-column prop="directDeliverySupplier" width="180" align="center" v-if="!isForecast">
            <template slot="header">
              <RequiredColTitle>选择直发</RequiredColTitle>
            </template>
            <template slot-scope="scope">
              <el-select
                :disabled="isService||isEnableDirectDeliverySupplier||orderType==='Z008' || scope.row.isPositionDisabled"
                v-model="scope.row.directDeliverySupplier"
                size="mini"
                placeholder="请选择"
                @change="val => changeDirectDeliverySupplier(val, scope.$index)"
              >
                <el-option
                  v-for="item in directDeliverySupplierList(scope.row)"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                  :disabled="item.isDisabled"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="position" width="200" align="center">
            <template slot="header">
              <RequiredColTitle>发货仓</RequiredColTitle>
            </template>
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.position"
                filterable
                size="mini"
                placeholder="请选择"
                :disabled="scope.row.isPositionDisabled"
                @change="val => changePosition(val, scope.row)"
              >
                <div v-if="scope.row.directDeliverySupplier === '0'" slot="empty" style="text-align:center">
                  <p style="margin-top:10px;color:grey">无匹配数据</p>
                  <p style="margin:10px 10px 0 10px;color:grey" v-if="morePositionDisabled">所选仓不在仓网内，如有疑问请联系IT</p>
                  <el-button type="text" @click="addPos(scope.row)" v-if="!morePositionDisabled">未找到仓位？点击此处</el-button>
                </div>
                <el-option
                  v-for="item in (scope.row.directDeliverySupplier === '0' ? scope.row.simPositionList : scope.row.positionList)"
                  :key="`${scope.$index}_${item.code}_${item.name}_${item.factory || item.parentCode}`"
                  :label="(item.code !== -1 ? item.code : '')+' '+item.name"
                  :value="item.code"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="deliveryPosition" width="200" align="center" label="集货仓">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.deliveryPosition"
                filterable
                clearable
                size="mini"
                placeholder="请选择"
                :loading="scope.row.isDeliveryPositionLoading"
                :disabled="scope.row.clearedQty > 0 || !orderDetail.isCollectWhiteList || scope.row.referType === '03' || scope.row.directDeliverySupplier == 1"
                @focus="() => changedeliveryPosition(scope.row)"
                @change="() => selectdeliveryWarehouseCode(scope.row)"
              >
                <el-option
                  v-for="item in deliveryPositionList"
                  :key="item.code + item.factory"
                  :label="item.code+' '+item.name"
                  :value="item.code"
                />
              </el-select>
            </template>
          </el-table-column>
          <!-- <el-table-column v-if="!isForecast" prop="deliveryTime" width="180" align="center">
            <template slot="header">
              <el-tooltip class="item" effect="dark" content="点击更新可发货日期" placement="top">
                <RequiredColTitle>
                  <el-button icon="el-icon-refresh" type="text" @click="getDeliveryDate">请求发货日期</el-button>
                </RequiredColTitle>
              </el-tooltip>
            </template>
            <template slot-scope="scope">
              <el-date-picker v-model="scope.row.deliveryDate"
                :disabled="scope.row.addType === '1'"
                size="mini"
                type="date"
                style="width:100%"
                placeholder="选择日期"
                value-format="yyyy-MM-dd" />
            </template>
          </el-table-column> -->
          <el-table-column label="请求发货日期" key="deliveryDate" prop="deliveryDate" width="150" align="center"></el-table-column>
          <el-table-column label="客户期望送达日期" key="customerDate" prop="customerDate" width="150" align="center">
            <template v-if="isForecast" slot="header">
              <RequiredColTitle>客户期望送达日期</RequiredColTitle>
            </template>
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.customerDate"
                :picker-options="getPickerOptions(scope.row)"
                @focus="focusDatePicker"
                @change="changeCustomerDate(scope.row)"
                size="mini"
                type="date"
                style="width:100%"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              />
            </template>
          </el-table-column>
          <el-table-column label="订单预计送达日期" key="orderPlanArrivalDate" prop="orderPlanArrivalDate" width="150" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.orderPlanArrivalDate"
                disabled
                size="mini"
                type="date"
                style="width:100%"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              />
            </template>
          </el-table-column>
          <el-table-column label="订单预计发货日期" key="orderPlanDeliveryDate" prop="orderPlanDeliveryDate" width="150" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.orderPlanDeliveryDate"
                disabled
                size="mini"
                type="date"
                style="width:100%"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              />
            </template>
          </el-table-column>
          <el-table-column label="物流时效" key="leadTime" prop="leadTime" width="100" align="center"></el-table-column>
          <!-- <el-table-column label="是否接受标期" key="refuseSystemDeliveryDate" prop="refuseSystemDeliveryDate" width="150" align="center">
            <template #header>
              <el-checkbox
                size="mini"
                true-label="X"
                false-label="Z"
                :disabled="disabledAllRefuseSystemDeliveryDate"
                @change="val => changeColumnCheckbox(val)"
              >是否接受标期
              </el-checkbox>
            </template>
            <template slot-scope="scope">
              <el-checkbox
                v-model="scope.row.refuseSystemDeliveryDate"
                :disabled="disabledRefuseSystemDeliveryDate(scope.row)"
                size="mini"
                true-label="X"
                false-label="Z"
                placeholder="选择日期"
                value-format="yyyy-MM-dd"
              >不接受标期
              </el-checkbox>
            </template>
          </el-table-column> -->
          <el-table-column prop="costCenter" width="200" align="center" v-if="isZ007">
            <template slot="header">
              <OptRequiredRowTitle title="成本中心" :isRequired="isInnerOrderReason(orderDetail.orderReason)" />
            </template>
            <template slot-scope="{row}">
              <el-select
                v-model="row.costCenter"
                :class="{tabelCellError:(!!!row.costCenter && isInnerOrderReason(orderDetail.orderReason))}"
                filterable
                size="mini"
                value-key="costCenter"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in costCenterOptions"
                  :key="item.costCenter"
                  :label="item.costCenter + ' ' + item.description"
                  :value="item.costCenter"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="generalLedgerAccount" width="200" align="center" v-if="isZ007">
            <template slot="header">
              <OptRequiredRowTitle title="总账科目" :isRequired="isInnerOrderReason(orderDetail.orderReason)" />
            </template>
            <template slot-scope="{row}">
              <el-select
                v-model="row.generalLedgerAccount"
                :class="{tabelCellError:(!!!row.generalLedgerAccount && isInnerOrderReason(orderDetail.orderReason))}"
                filterable
                size="mini"
                value-key="value"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in mmDictList.generalLedger"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="handleDetail(scope.$index)">详情</el-button>
              <el-button type="text" size="small" @click="handleDelete(scope.$index)" v-if="scope.row.addType==='1'">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="page"
          :limit.sync="size"
          layout="total, prev, pager, next, jumper"
          @pagination="pageList"
        />
      </div>

      <DividerHeader>其他信息</DividerHeader>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="收货联系人" prop="receiverContact">
            <el-select
              v-model="orderDetail.receiverContact"
              placeholder="选择收货联系人"
              filterable
              remote
              reserve-keyword
              clearable
              style="width:100%"
              value-key="contactId"
              :remote-method="queryReceivedContactList"
              :loading="loadingContact"
              @change="changeReceivedContact"
              @clear="clearReceivedContact"
            >
              <el-option
                v-for="(item,index) in receivedContactList"
                :key="item.contactId"
                :label="item.contactName"
                :value="item.contactId"
                :disabled="index===0"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{fontWeight:index===0?'bold':'normal'}"
                >
                  <div>{{ item.contactName }}</div>
                  <div>{{ item.contactId }}</div>
                  <div>{{ item.contactPhone || '--' }}</div>
                  <div>{{ item.address || '--' }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收货人电话">
            <el-input
              placeholder="收货人电话"
              :value="orderDetail.receiverPhone"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="收货地址" class="longField">
            <el-input
              placeholder="收货地址"
              :value="orderDetail.receiverAddress || '--'"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="收票联系人" prop="receivingInvoiceContact">
            <el-select
              v-model="orderDetail.receivingInvoiceContact"
              placeholder="选择收票联系人"
              filterable
              remote
              reserve-keyword
              clearable
              style="width:100%"
              value-key="contactId"
              :remote-method="queryInvoiceContactList"
              :loading="loadingContact"
              @change="changeInvoiceContact"
              @clear="clearInvoiceContact"
            >
              <el-option
                v-for="(item,index) in invoiceContactList"
                :key="item.contactId"
                :label="item.contactName"
                :value="item.contactId"
                :disabled="index===0"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{fontWeight:index===0?'bold':'normal'}"
                >
                  <div>{{ item.contactName }}</div>
                  <div>{{ item.contactId }}</div>
                  <div>{{ item.contactPhone || '--' }}</div>
                  <div>{{ item.address || '--' }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收票人电话">
            <el-input
              placeholder="收票人电话"
              :value="orderDetail.receivingInvoicePhone"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="收票地址" class="longField">
            <el-input
              placeholder="收票地址"
              :value="orderDetail.receivingInvoiceAddress || '--'"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单联系人" prop="orderContact">
            <el-select
              v-model="orderDetail.orderContact"
              placeholder="选择订单联系人"
              filterable
              remote
              reserve-keyword
              clearable
              style="width:100%"
              :remote-method="queryOrderContactList"
              :loading="loadingContact"
              @change="changeOrderContact"
              @clear="clearOrderContact"
            >
              <el-option
                v-for="(item,index) in orderContactList"
                :key="item.contactId"
                :label="item.contactName"
                :value="item.contactId"
                :disabled="index===0"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{fontWeight:index===0?'bold':'normal'}"
                >
                  <div>{{ item.contactName }}</div>
                  <div>{{ item.contactId }}</div>
                  <div>{{ item.contactPhone || '--' }}</div>
                  <div>{{ item.address || '--' }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单人电话">
            <el-input
              placeholder="订单人电话"
              :value="orderDetail.orderContactPhone || '--'"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单原因" class="orderReason" prop="orderReason">
            <el-select
              clearable
              v-model="orderDetail.orderReason"
              placeholder="请选择订单原因"
              style="width:100%"
            >
              <el-option
                v-for="item in orderReasonList"
                :key="`${item.code}-${item.label}-${item.parentCode}`"
                :disabled="disabledHistory(item)"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item v-if="isShowSapReturnDnNo" label="sap退货交货单" prop="sapReturnDnNo">
            <el-input
              v-model="orderDetail.sapReturnDnNo"
              placeholder="请输入814单号"
              :disabled="!orderDetail.sapReturnDnNoCanModify"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="订单备注" class="longField">
            <el-input v-model="orderDetail.orderNote" placeholder="订单备注" type="textarea" maxlength="500" show-word-limit />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="ba-row-center btnGroup">
      <el-button type="primary" plain @click="showEditOrderMore=true">编辑更多</el-button>
      <el-button type="primary" @click="submit('orderForm')" :disabled="isSubmitting">确认保存</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
    <CustomerDateConfirmDlg :show-dialog.sync="showCustomerDateConfirmDlg" :fail-content="conflictMessage" @submit="submitEditAgain" />
  </div>
</template>

<script>
import * as shortid from 'shortid'
import moment from 'moment'
import slice from 'lodash/slice'
import endsWith from 'lodash/endsWith'
import debounce from 'lodash/debounce'
import DividerHeader from './components/common/DividerHeader'
import OptRequiredRowTitle from '@/pages/orderSale/components/common/OptRequiredRowTitle'
import Pagination from '@/components/Pagination'
import RequiredColTitle from './components/common/RequiredColTitle'
import EditCustomer from './components/edit/EditCustomer'
import ShowMorePosition from './components/edit/ShowMorePosition'
import ProductDetail from './components/edit/ProductDetail'
import EditOrderMore from './components/edit/EditOrderMore'
import ProductPlan from './components/edit/ProductPlan'
import SelectGoods from './components/edit/SelectGoods'
import CustomerDateConfirmDlg from './components/edit/CustomerDateConfirmDlg'
import * as utils from '@/utils'
import * as editOrder from '@/api/orderSale'
import { getCostCenter } from '@/api/mm'
import defaultImage from '@/assets/orderSupplyList/qs_product.jpg'
import {
  getFactoryList,
  getOrderOptUrl,
  requestWithLoading
} from './utils'
import {
  isServiceOrder,
  isForecastOrder,
  isFreeOrder,
  isEnableDirectDeliverySupplier,
  isInnerOrderReason
} from '@/pages/orderSale/utils/orderType'
import { contactHeader } from '@/pages/orderSale/constants'
import { foldFields, orderServiceFields } from '@/utils/orderService'
import { buildSoDetailLink, deepClone } from '@/utils/index.js'
import { checkBTBOrder } from '@/utils/order'
import { getDisabledDate } from '@/pages/orderSale/utils'
import SelectOrderService from '@/pages/orderSale/components/common/SelectOrderService'

const rules = {
  receiverContact: [
    { required: true, message: '请选择收货联系人', trigger: 'blur' }
  ],
  orderContact: [
    { required: true, message: '请选择订单联系人', trigger: 'blur' }
  ],
  receivingInvoiceContact: [
    { required: true, message: '请选择收票联系人', trigger: 'blur' }
  ]
  // sapReturnDnNo: [
  //   { required: true, message: '请输入sap退货交货单', trigger: 'blur' }
  // ]
}

const ruleOrderReason = {
  orderReason: [
    { required: true, message: '请选择订单原因', trigger: 'blur' }
  ]
}

export default {
  components: {
    DividerHeader,
    OptRequiredRowTitle,
    RequiredColTitle,
    EditCustomer,
    ShowMorePosition,
    EditOrderMore,
    ProductDetail,
    ProductPlan,
    SelectGoods,
    Pagination,
    SelectOrderService,
    CustomerDateConfirmDlg
  },
  data() {
    const { omsNo, sapOrderNo } = this.$route.query
    return {
      baseInfo: [
        [
          {
            label: 'OMS订单号', prop: 'soNo', span: 6
          },
          {
            label: 'SAP订单号', prop: 'sapOrderNo', span: 6
          },
          {
            label: '订单时间', prop: 'orderCreateTime', span: 6
          }
        ],
        [
          {
            label: '订单状态', prop: 'orderStatus', span: 6
          },
          {
            label: '直/分销渠道', prop: 'saleOrgVO', span: 6
          },
          {
            label: '创建者', prop: 'creator', span: 6
          }
        ],
        [
          {
            label: '客服', prop: 'customerServiceName', span: 6
          },
          {
            label: '客服主管', prop: 'customerServiceSupervisorName', span: 6
          },
          {
            label: '销售', prop: 'sellerName', span: 6
          },
          {
            label: '销售主管', prop: 'salesManagerName', span: 6
          }
        ]
      ],
      defaultImage,
      showProductPlan: false,
      showEditCustomer: false,
      showEditOrderMore: false,
      showProductDetail: false,
      loadingContact: false,
      multipleSelection: [],
      freezeOptionValue: [],
      allFreeTotalPrice: 0, // 订单总未税金额
      allTaxTotalPrice: 0, // 订单总含税金额
      allFreeTotalDiscountPrice: 0, // 订单总未税折扣金额
      allTaxTotalDiscountPrice: 0, // 订单总含税折扣金额
      omsNo,
      sapOrderNo,
      orderDetail: {
        receiverContact: null,
        orderContact: null,
        receivingInvoiceContact: null,
        sapReturnDnNo: '',
        attachmentList: []
      },
      clientDetail: {},
      skuDetail: {},
      goodsList: [],
      positionList: [],
      deliveryPositionList: [],
      quantityList: [],
      receivedContactList: [],
      orderContactList: [],
      invoiceContactList: [],
      total: 0,
      page: 1,
      size: 20,
      itemList: [],
      invoiceReceiver: '',
      invoiceReceiverList: [],
      updateWay: '',
      isShow: true,
      costCenterOptions: [],
      isSubmitting: false,
      rowDetail: {},
      ShowMorePosition: false,
      pickerOptions: {
        disabledDate: (time) => {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return time.getTime() < today.getTime();
        }
      },
      showBTBOrder: false, // 背靠背信息提醒弹窗仅显示一次
      num: 1, // 新加商品行项目行id
      showCustomerDateConfirmDlg: false, // 确认交期的二次弹窗
      conflictMessage: '',
      newItem: []
    }
  },
  computed: {
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    acceptFileType () {
      return this.$store.state.orderCommon.acceptFileType || {}
    },
    morePositionDisabled() {
      return this.$store.state.orderCommon.morePositionDisabled
    },
    orderServiceDict() {
      return this.$store.state.orderCommon.orderServiceDict || {}
    },
    mmDictList() {
      return this.$store.state.orderPurchase.dictList || {}
    },
    sapReturnOrderValidator () {
      return this.$store.state.orderCommon.sapReturnOrderValidator
    },
    isZ007() {
      return this.orderDetail.orderType === 'Z007'
    },
    isGBB() {
      return this.orderDetail.salesOrganization && this.orderDetail.salesOrganization.indexOf('13') === 0
    },
    orderTypeName() {
      if (this.dictList && this.dictList['soCategory'] && Array.isArray(this.dictList['soCategory']) && this.orderDetail.orderType) {
        return (this.dictList['soCategory'].filter(item => item.code === this.orderDetail.orderType)[0] || {}).name
      } else {
        return ''
      }
    },
    orderType() {
      return this.orderDetail ? this.orderDetail.orderType : ''
    },
    factoryList() {
      const { distributionChannel, salesOrganization } = this.orderDetail
      if (distributionChannel && salesOrganization) {
        return getFactoryList(distributionChannel, salesOrganization, this.dictList)
      }
      return []
    },
    // 6001 工厂不可选 选择直发只能选择：供应商直发，库位：1004
    isFactoryDisabled() {
      return this.orderDetail.salesOrganization === '6001' && (this.orderDetail.factoryPriorityList !== undefined && this.orderDetail.factoryPriorityList[0] !== '1000')
    },
    isService() {
      return isServiceOrder(this.orderDetail.orderType)
    },
    isForecast() {
      return this.orderDetail && this.orderDetail.orderType
        ? isForecastOrder(this.orderDetail.orderType)
        : false
    },
    isFree() {
      return isFreeOrder(this.orderDetail.orderType)
    },
    isEnableDirectDeliverySupplier() {
      return isEnableDirectDeliverySupplier(this.orderDetail.orderType)
    },
    isPriceRequired() {
      // todo,table的头上prop设置没有效果，需要后续调查
      return this.orderDetail && this.orderDetail.orderType &&
        !isForecastOrder(this.orderDetail.orderType) &&
        !isFreeOrder(this.orderDetail.orderType)
    },
    orderReasonList() {
      const orList = this.dictList['orderReason'] || []
      let options = orList
      if (this.orderType) {
        const typeOptions = orList.filter(or => or.parentCode === this.orderType)
        if (typeOptions.length) {
          options = typeOptions
        } else {
          options = orList.filter(or => or.parentCode === '')
        }
      }
      // Z007 兼容历史数据
      if (orList && this.orderType === 'Z007') {
        const finalList = []
        for (let item of orList) {
          if (!finalList.find(fItem => fItem.code === item.code)) {
            finalList.push(item)
          } else {
            const findIndex = finalList.findIndex(fItem => fItem.code === item.code)
            if (item.parentCode === 'Z007') {
              finalList.splice(findIndex, 1)
              finalList.push(item)
            }
          }
        }
        return [...finalList.filter(e => e.status !== 'stop'), ...finalList.filter(e => e.status === 'stop')]
      }
      return options
    },
    rules() {
      let result = {}
      if (this.orderDetail) {
        const { orderType, sapReturnDnNoCanModify } = this.orderDetail
        if (orderType) {
          if (!isForecastOrder(orderType)) {
            result = rules
          }
          if (!(orderType === 'Z001') &&
            !(orderType === 'Z002') &&
            !(orderType === 'Z012') &&
            !(orderType === 'Z014') &&
            !(orderType === 'ZEV1') &&
            !(orderType === 'ZEV3')
          ) {
            result = {
              ...ruleOrderReason,
              ...result
            }
          }
        }
        if (this.isShowSapReturnDnNo && !this.sapReturnOrderValidator && sapReturnDnNoCanModify) {
          result = {
            ...result,
            sapReturnDnNo: [
              { required: true, message: '请输入sap退货交货单', trigger: 'blur' }
            ]
          }
        }
      }
      return result
    },
    currencySymbol() {
      const currency = this.currency
      const symbolMap = {
        USD: '$',
        CNY: '￥',
        EUR: '€',
        MYR: 'RM',
        THB: '฿',
        VND: '₫',
        SGD: 'S$',
        TWD: 'NT$',
        HKD: 'HK$'
      }
      return symbolMap[currency]
    },
    currency() {
      const found = this.goodsList.find(item => item.currency)
      return found ? found.currency : ''
    },
    paymentTermName() {
      if (this.orderDetail && this.dictList && this.dictList['paymentTerms']) {
        const { paymentTerm } = this.orderDetail
        const found = this.dictList['paymentTerms'].find(item => item.code === paymentTerm)
        if (found) {
          return found.name
        }
      }
      return ''
    },
    freezeReasonList() {
      return this.orderDetail && this.orderDetail.deliveryFreeze
        ? this.orderDetail.deliveryFreeze.split(',') : []
    },
    freezeBillReasonList() {
      return this.orderDetail && this.orderDetail.billingFreeze
        ? this.orderDetail.billingFreeze.split(',') : []
    },
    isTaxedCustomer() {
      return this.orderDetail && this.orderDetail && this.orderDetail.isTax === '1'
    },
    uploadQueryUrl() {
      const { salesOrganization, distributionChannel, productGroup } = this.orderDetail || {}
      const { customerNo } = this.orderDetail || {}
      const queryStr = []
      let url = ''
      if (salesOrganization && distributionChannel && productGroup && customerNo) {
        const params = {
          customerNo: customerNo,
          salesOrganization,
          distributionChannel,
          productGroup,
          orderType: this.orderType,
          soNo: this.omsNo
        }
        Object.keys(params).forEach(key => {
          queryStr.push(`${key}=${params[key]}`)
        })
        if (queryStr.length > 0) {
          const s = queryStr.join('&')
          url += `?${s}`
        }
      }
      return url
    },
    disabledAllRefuseSystemDeliveryDate() {
      return this.itemList.every(item => item.originRefuseSystemDeliveryDate === 'X')
    },
    isShowSapReturnDnNo () {
      const skuValid = (this.goodsList || []).some(item => item.directDeliverySupplier === '0' && endsWith(item.position, '04'))
      return /Z001|Z006|Z007/gim.test(this.orderDetail?.orderType) && this.orderDetail?.orderReason === '038' && skuValid
    }
  },
  async created() {
    const loading = this.$loading({
      background: 'rgba(0, 0, 0, 0.5)',
      lock: true
    })
    if (JSON.stringify(this.dictList) === '{}') {
      await this.$store.dispatch('orderCommon/queryDictList')
    }
    if (JSON.stringify(this.orderServiceDict) === '{}') {
      this.$store.dispatch('orderCommon/getOrderServiceDict')
    }
    if (JSON.stringify(this.mmDictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    this.$store.dispatch('orderCommon/getSearchSkuSwitch')
    const res1 = await editOrder.orderDetail({
      sapOrderNo: this.sapOrderNo,
      omsNo: this.omsNo,
      wantedFields: 'skuFactoryPriceMap'
    })
    if (res1 && res1.code === 200 && res1.data && res1.data.items) {
      this.orderDetail = {
        ...res1.data,
        acceptSupplierDeliveryOld: res1.data.acceptSupplierDelivery,
        specifiedReceiptDayOfWeek: res1.data.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day)
      }
      const {
        customerNo, distributionChannel, productGroup, salesOrganization,
        orderContact, orderContactName, orderContactAddress, orderContactPhone,
        receiverContact, receiverName, receiverPhone, receiverAddress,
        receivingInvoiceContact, receivingInvoiceName, receivingInvoicePhone,
        receivingInvoiceAddress, invoiceReceiver
      } = res1.data
      this.invoiceReceiver = invoiceReceiver
      const res2 = await editOrder.getClientDetail(customerNo, distributionChannel, productGroup, salesOrganization)
      if (res2 && res2.code === 200) {
        this.clientDetail = res2.data
      }
      this.initGoodsList(res1.data.items || [])
      const contactRes = await editOrder.searchContactListByGroup({
        customerCode: customerNo,
        contactName: '',
        distributionChannel,
        productGroup,
        salesOrganization
      })
      let contactList = []
      if (contactRes && contactRes.code === 200) {
        contactList = contactRes.data.records
      }
      this.orderContactList = [
        contactHeader,
        { contactId: orderContact, contactName: orderContactName, address: orderContactAddress, contactPhone: orderContactPhone },
        ...(contactList.filter(item => Number(item.contactId) !== orderContact))
      ]
      this.receivedContactList = [
        contactHeader,
        { contactId: receiverContact, contactName: receiverName, address: receiverAddress, contactPhone: receiverPhone },
        ...(contactList.filter(item => Number(item.contactId) !== receiverContact))
      ]
      this.invoiceContactList = [
        contactHeader,
        { contactId: receivingInvoiceContact, contactName: receivingInvoiceName, address: receivingInvoiceAddress, contactPhone: receivingInvoicePhone },
        ...(contactList.filter(item => Number(item.contactId) !== receivingInvoiceContact))
      ]
      const param = {
        orderType: this.orderDetail.orderType,
        orderSource: this.orderDetail.orderSource
      }
      if (this.isZ007) {
        this.remoteMethod('')
      }
      editOrder.isAddSkuBtnDisplay(param).then(res => {
        if (res && res.code === 200) {
          this.isShow = res.data
        }
      })
    } else {
      this.$message.error('获取订单信息失败！')
    }
    loading.close()
  },
  methods: {
    getPickerOptions (row) {
      const disabledDate = (time) => {
        const specifiedReceiptDayOfWeek = typeof this.orderDetail?.specifiedReceiptDayOfWeek === 'string' ? this.orderDetail?.specifiedReceiptDayOfWeek?.split(',') : this.orderDetail?.specifiedReceiptDayOfWeek
        const receiptTimeCategory = this.orderDetail?.receiptTimeCategory
        const check = !['Z002', 'Z014'].includes(this.orderType) && !['8', 'X'].includes(this.orderDetail.bidCustomer)
        return getDisabledDate(time, specifiedReceiptDayOfWeek, receiptTimeCategory, check)
      }
      return {
        disabledDate
      }
    },
    focusDatePicker () {
      const specifiedReceiptDayOfWeek = typeof this.orderDetail?.specifiedReceiptDayOfWeek === 'string' ? this.orderDetail?.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day) : this.orderDetail?.specifiedReceiptDayOfWeek?.filter(day => !!day)
      const receiptTimeCategory = this.orderDetail?.receiptTimeCategory
      if ((receiptTimeCategory === 'Z' || receiptTimeCategory === false) && specifiedReceiptDayOfWeek?.length && specifiedReceiptDayOfWeek?.every(day => !['0', '01', '02', '03', '04', '05'].includes(day))) {
        this.$message.warning('无可选日期，请修改客户指定日收货或工作日与周末均可收货!')
      }
    },
    isInnerOrderReason,
    disabledHistory(item) {
      if (this.orderType === 'Z006') {
        return item.parentCode !== 'Z006' || item.status === 'stop'
      }
      if (this.orderType === 'Z007') {
        return item.parentCode !== 'Z007' || item.status === 'stop'
      }
      return item.status === 'stop'
    },
    addPos(row) {
      this.rowDetail = row
      this.ShowMorePosition = true
    },
    remoteMethod(val) {
      getCostCenter({
        companyCode: this.orderDetail.salesOrganization,
        costCenter: val
      }).then(data => {
        if (data) {
          this.costCenterOptions = data
        }
      })
    },
    async pageList() {
      this.total = this.goodsList.length
      const tempList = slice(this.goodsList, (this.page - 1) * this.size, (this.page - 1) * this.size + this.size)
      for (let idx = 0; idx < tempList.length; idx++) {
        const goods = tempList[idx]
        goods.isPositionDisabled = false
        if (this.orderDetail.salesOrganization === '6001' && goods.factory === '6000') {
          goods.position = '1004'
          goods.directDeliverySupplier = '1'
          goods.isPositionDisabled = true
        }
      }
      await this.setPosition(tempList)
      this.itemList = tempList
    },
    // handleSelectChange(items) {
    //   this.multipleSelection = items;
    // },
    directDeliverySupplierList(row) {
      return this.dictList['directDeliverySupplier'].filter(item => {
          if (row.addType !== '1') {
            return parseInt(item.code, 10) < 2
          } else {
            return true
          }
        }).map(item => {
        if (this.isFactoryDisabled) {
          item.isDisabled = !(item.code === '1')
        }
        return item
      })
    },
    isFactoryDisable(row, code) {
      if (row && code) {
        const { skuFactoryPriceMap } = row
        if (skuFactoryPriceMap) {
          const c = parseInt(code, 10)
          return !(skuFactoryPriceMap[c] && skuFactoryPriceMap[c].taxRate != null)
        }
      }
      return true
    },
    formatQuantityUnit(value) {
      if (value && utils.isNumber(value)) {
        const unit = this.dictList['quantityUnit'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    async setPosition(tempList) {
      const factoryList = tempList.map(item => item.factory).filter(Boolean)
      const skuNoList = tempList.map(item => item.materiel).filter(Boolean)
      const simPositionList = await this.getSimPosition(factoryList, skuNoList)
      console.log(simPositionList)
      tempList.forEach(item => {
        item.simPositionList = simPositionList.filter(simItem => simItem.sku === item.materiel)
        item.positionList = this.getPositionList(item)
        if (!item.simPositionList.includes(item.position)) {
          const find = item.positionList.find(item => item.code === item.position)
          console.log(item, find)
          if (find) {
            item.simPositionList.unshift(find)
          }
        }
      })
    },
    async getSimPosition(factory, skuNo) {
      // 震坤行发货 sim 库位
      let positionList = []
      const data = {
        skuSet: Array.isArray(skuNo) ? skuNo : [skuNo],
        factorySet: Array.isArray(factory) ? factory : [factory],
        positionScope: 1
      }
      const res = await editOrder.getDeliveryWarehouse(data)
      if (res && res.status === 200 && Array.isArray(res.result)) {
        const codeMap = {}
        res.result.reduce((prev, next) => {
          prev.push(...next.allPosition.map(item => ({ ...item, sku: next.sku, warehouseCode: next.warehouseCode })))
          return prev
        }, []).forEach(item => {
          const key = `${item.factory}_${item.code}_${item.name}_${item.sku}`
          if (!codeMap[key]) {
            codeMap[key] = true
            positionList.push(item)
          }
        })
      }
      return positionList
    },
    getPositionList(goods) {
      const { directDeliverySupplier, factory } = goods
      let positionList = factory
        ? this.dictList['position'].filter(item => item.parentCode === factory)
        : []
      if (this.orderDetail &&
        (isServiceOrder(this.orderDetail.orderType) ||
          directDeliverySupplier === '1' ||
          this.orderType === 'Z018')) {
        return positionList.filter(item => endsWith(item.code, '04'))
      }
      if (directDeliverySupplier === '2') {
        positionList.unshift({
          code: -1,
          name: '自动挑仓'
        })
      }
      return positionList
    },
    submitCustomer(data, invoiceReceiverList) {
      this.orderDetail.customerReferenceNo = data.customerReferenceNo && data.customerReferenceNo.trim().replace(/(?:\r\n|\r|\n)/g, '')
      this.orderDetail.customerReferenceDate = data.customerReferenceDate === null ? '' : data.customerReferenceDate
      this.orderDetail.serviceCenterSelfTransport = data.serviceCenterSelfTransport
      this.orderDetail.acceptSupplierDelivery = data.acceptSupplierDelivery
      this.orderDetail.paymentTerm = data.paymentTerm
      this.invoiceReceiverList = invoiceReceiverList

      const { customerNo, distributionChannel, productGroup, salesOrganization, invoiceReceiver, invoiceReceiverName } = this.orderDetail
      if (data.invoiceReceiver !== invoiceReceiver || data.invoiceReceiverName !== invoiceReceiverName) {
        this.orderDetail.invoiceReceiver = data.invoiceReceiver
        this.orderDetail.invoiceReceiverName = data.invoiceReceiverName
        this.orderDetail.receivingInvoiceContact = ''
        this.orderDetail.receivingInvoicePhone = ''
        this.orderDetail.receivingInvoiceAddress = ''
        editOrder.searchContactListByGroup({
          customerCode: data.invoiceReceiver || customerNo,
          contactName: '',
          distributionChannel,
          productGroup,
          salesOrganization
        }).then(contactRes => {
          let contactList = []
          if (contactRes && contactRes.code === 200) {
            contactList = contactRes.data.records
          }
          this.invoiceContactList = [
            contactHeader,
            ...contactList
          ]
        })
        editOrder.getClientDetail(data.invoiceReceiver,
          distributionChannel,
          productGroup,
          salesOrganization
        ).then(res => {
          if (res && res.code === 200 && res.data) {
            const {
              corporationTaxNum, billingAddressAndPhone,
              bankName, bankNumber, customerPayAccountTypeName,
              shippingInfo, invoiceType, mergeBillingDemand,
              autoBilling, financialNote
            } = res.data
            const {
              customerSourceName, businessPartnerGroupName,
              customerClassificationName, customerNatureName, evmOperator
            } = this.clientDetail
            this.clientDetail = {
              ...res.data,
              customerSourceName,
              businessPartnerGroupName,
              customerClassificationName,
              customerNatureName,
              evmOperator
            }
            this.orderDetail = {
              ...this.orderDetail,
              corporationTaxNum,
              billingAddressAndPhone,
              bankName,
              bankNumber,
              customerPayAccountTypeName,
              shippingInfo,
              invoiceType,
              mergeBillingDemand,
              autoBilling,
              financialNote
            }
          }
        })
      }
    },
    async setSimPosition(row) {
      const data = {
        skuSet: [row.materiel],
        factorySet: [row.factory],
        positionScope: 1
      }
      const res = await editOrder.getDeliveryWarehouse(data)
      let posList = []
      if (res && res.status === 200 && Array.isArray(res.result)) {
        const codeMap = {}
        res.result.reduce((prev, next) => {
          prev.push(...next.allPosition)
          return prev
        }, []).forEach(item => {
          const key = `${item.factory}_${item.code}_${item.name}`
          if (!codeMap[key]) {
            codeMap[key] = true
            posList.push(item)
          }
        })
        console.log(posList)
      }
      return posList
    },
    // TODO, 将来需要支持夸公司售卖
    async changeFactory (value, idx) {
      const index = (this.page - 1) * this.size + idx
      const positionList = this.dictList['position'].filter(item => {
        if (this.orderDetail && this.isService) {
          return item.parentCode === value && endsWith(item.code, '04')
        }
        return item.parentCode === value
      })
      this.goodsList[index].factory = value
      this.goodsList[index].position = ''
      this.goodsList[index].positionList = positionList
      this.goodsList[index].deliveryPosition = ''
      const simPositionList = await this.setSimPosition(this.goodsList[index], index)
      const tempItem = { ...this.itemList[idx], simPositionList }
      this.$set(this.itemList, idx, tempItem)
      this.$set(this.goodsList, index, tempItem)
    },
    changePosition(value, row) {
      console.log(value, row)
      delete row.lastConfirmedPosition
      this.queryFinalLT(row)
    },
    queryFinalLT(row) {
      try {
        // 非直发才去查询物流LT
        if (row.directDeliverySupplier === '0') {
          const positionWarehouseCode = (row.simPositionList.find(item => item.code === row.position) || {}).warehouseCode || ''
          const { receiverProvince, receiverCity, receiverDistrict } = this.orderDetail
          const data = {
            address: {
              province: receiverProvince,
              city: receiverCity,
              district: receiverDistrict
            },
            skuInfos: [{ qty: row.quantity, sku: row.materiel }],
            wmsIds: [row.deliveryWarehouseCode || positionWarehouseCode]
          }
          editOrder.queryFinalLT(data).then(res => {
            if (res.data && Array.isArray(res.data) && res.data.length) {
              row.leadTime = res.data[0].transportAgingTime
            }
          })
        }
      } catch (err) {
        console.log(err)
      }
    },
    async changedeliveryPosition(row) {
      this.deliveryPositionList = []
      try {
        row.isDeliveryPositionLoading = true;
        const data = {
          skuSet: [row.materiel],
          factorySet: [row.factory],
          positionScope: 3
        }
        const res = await editOrder.getDeliveryWarehouse(data)
        if (res.status === 200) {
          let deliveryPositionList = res.result.filter(item => item.allPosition && item.allPosition.length > 0).flatMap(item => {
            item.allPosition.map(val => {
              val.warehouseCode = item.warehouseCode
              return val
            })
            return item.allPosition
          })
          this.deliveryPositionList = deliveryPositionList
        } else {
          this.deliveryPositionList = []
        }
      } catch (error) {
        console.log(error)
      } finally {
        row.isDeliveryPositionLoading = false;
      }
    },
    selectdeliveryWarehouseCode(row) {
      row.deliveryWarehouseCode = (this.deliveryPositionList.find(item => item.code === row.deliveryPosition) || {}).warehouseCode || ''
      this.queryFinalLT(row)
    },
    getWarehouseCodeList(val) {
      this.deliveryPositionList = val
    },
    changeDirectDeliverySupplier(value, index) {
      console.log(value, index)
      const idx = (this.page - 1) * this.size + index
      const { factory, mtart } = this.goodsList[idx]
      let positionList = factory
        ? this.dictList['position'].filter(item => item.parentCode === factory)
        : []
      if (value === '1' || this.isService || mtart === 'Z002') {
        positionList = positionList.filter(item => endsWith(item.code, '04'))
        this.goodsList[idx].deliveryPosition = ''
      }
      if (value === '2') {
        positionList.unshift({
          code: -1,
          name: '自动挑仓'
        })
      }
      this.goodsList[idx].positionList = positionList
      this.goodsList[idx].position = ''
    },
    handleQuantity(index) {
      const idx = (this.page - 1) * this.size + index
      const { sapOrderNo, sapItemNo, soItemNo } = this.goodsList[idx]
      const soNo = this.omsNo
      if (this.goodsList[idx] && !this.goodsList[idx].itemPlanDTOList) {
        const loading = this.$loading({
          background: 'rgba(0, 0, 0, 0.5)',
          lock: true
        })
        editOrder.orderDetailPlan({ sapOrderNo, sapItemNo, soNo, soItemNo }).then(res => {
          loading.close()
          if (res && res.code === 200) {
            this.showProductPlan = true
            this.goodsList[idx].itemPlanDTOList =
              (res.data || []).sort((a, b) => a.confirmedQtyType > b.confirmedQtyType ? 1 : -1)
            this.skuDetail = {
              index: idx,
              data: this.goodsList[idx]
            }
          }
        })
      } else {
        this.skuDetail = {
          index: idx,
          data: this.goodsList[idx]
        }
        this.showProductPlan = true
      }
    },
    handleDelete(index) {
      const idx = (this.page - 1) * this.size + index
      this.goodsList.splice(idx, 1)
      this.pageList()
      this.getDeliveryDate()
    },
    handleDetail(index) {
      const idx = (this.page - 1) * this.size + index
      const sku = this.goodsList[idx].materiel
      const { customerNo, salesOrganization, distributionChannel, productGroup } = this.orderDetail || {}
      if (sku) {
        const loading = this.$loading({
          background: 'rgba(0, 0, 0, 0.5)',
          lock: true
        })
        editOrder.getSkuDetail(sku, {
          customerNo,
          salesOrganization,
          distributionChannel,
          productGroup,
          orderType: this.orderType
        }).then(res => {
          loading.close()
          if (res && res.code === 200) {
            this.showProductDetail = true
            this.skuDetail = {
              index: idx,
              sku: res.data,
              data: this.goodsList[idx]
            }
          }
        }).catch(error => {
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
          loading.close()
        })
      } else {
        this.showProductDetail = true
        this.skuDetail = {
          index: idx,
          sku: {},
          data: this.goodsList[idx]
        }
      }
    },
    changeGoodsPrice(type, idx) {
      const index = (this.page - 1) * this.size + idx
      if (!this.goodsList[index]) {
        return
      }
      const { freeTaxPrice = 0, taxPrice = 0, taxRate = 0, quantity = 0 } = this.goodsList[index] || {}
      let taxAmount = 0
      if (type === 'tax') {
        this.goodsList[index].freeTaxPrice = taxPrice / (1 + taxRate)
        taxAmount = this.goodsList[index].freeTaxPrice * quantity * taxRate
      } else {
        this.goodsList[index].taxPrice = freeTaxPrice * (1 + taxRate)
        taxAmount = freeTaxPrice * quantity * taxRate
      }
      this.goodsList[index].taxAmount = utils.formatPrice(taxAmount)
      if (Number(this.goodsList[index].taxTotalPrice) !== this.goodsList[index].taxPrice * Number(quantity)) {
        this.goodsList[index].taxTotalPrice = this.goodsList[index].taxPrice * quantity
        this.goodsList[index].freeTotalPrice = this.goodsList[index].freeTaxPrice * quantity
        this.setTotalPrice()
      }
    },
    calSkuAmount(skuList) {
      const { isTax } = this.orderDetail
      const result = skuList.reduce(
        (acc, sku) => {
          const {
            taxRate: taxRate = 0,
            discountAmount: discountAmount = 0,
            quantity,
            taxPrice,
            freeTaxPrice
          } = sku
          const taxTotalPrice = taxPrice * quantity;
          const freeTotalPrice = freeTaxPrice * quantity;
          const dp = parseFloat(discountAmount)
          const newValue = {}
          newValue.taxedTotal = parseFloat(taxTotalPrice) + acc.taxedTotal
          newValue.unTaxedTotal = parseFloat(freeTotalPrice) + acc.unTaxedTotal
          if (isTax === '0') {
            newValue.taxedDiscountTotal = acc.taxedDiscountTotal + parseFloat(taxTotalPrice) - dp * (1 + taxRate)
            newValue.unTaxedDiscountTotal = acc.unTaxedDiscountTotal + parseFloat(freeTotalPrice) - dp
          }
          if (isTax === '1') {
            newValue.taxedDiscountTotal = acc.taxedDiscountTotal + parseFloat(taxTotalPrice) - dp
            newValue.unTaxedDiscountTotal = acc.unTaxedDiscountTotal + parseFloat(freeTotalPrice) - (dp / (1 + taxRate))
          }
          return newValue
        },
        { taxedTotal: 0, taxedDiscountTotal: 0, unTaxedTotal: 0, unTaxedDiscountTotal: 0 }
      )
      return result
    },
    setTotalPrice() {
      const {
        taxedTotal,
        taxedDiscountTotal,
        unTaxedTotal,
        unTaxedDiscountTotal
      } = this.calSkuAmount(this.goodsList)
      this.allTaxTotalPrice = utils.formatPrice(taxedTotal)
      this.allTaxTotalDiscountPrice = utils.formatPrice(taxedDiscountTotal)
      this.allFreeTotalPrice = utils.formatPrice(unTaxedTotal)
      this.allFreeTotalDiscountPrice = utils.formatPrice(unTaxedDiscountTotal)
    },
    changeOrderContact(val) {
      const orderContactData = this.orderContactList.find(item => item.contactId === val)
      if (orderContactData) {
        const { contactName, mobilephone, telephone, address } = orderContactData
        // 订单联系人 姓名
        this.orderDetail.orderContactName = contactName
        // 电话
        this.orderDetail.orderContactPhone = mobilephone || telephone || '--'
        // 地址
        this.orderDetail.orderContactAddress = address || '--'
      }
    },
    clearOrderContact() {
      // 订单联系人 姓名
      this.orderDetail.orderContactName = ''
      // 电话
      this.orderDetail.orderContactPhone = ''
      // 地址
      this.orderDetail.orderContactAddress = ''
    },
    changeReceivedContact(val) {
      const receivedContactData = this.receivedContactList.find(item => item.contactId === val)
      if (receivedContactData) {
        const { contactName, mobilephone, telephone, address, province, provinceName, city, cityName, region, regionName } = receivedContactData
        // 收货联系人 姓名
        this.orderDetail.receiverName = contactName
        // 电话
        this.orderDetail.receiverPhone = mobilephone || telephone || '--'
        // 地址
        this.orderDetail.receiverAddress = address || '--'
        // 省市区代码
        this.orderDetail.receiverProvinceCode = province || ''
        this.orderDetail.receiverCityCode = city || ''
        this.orderDetail.receiverDistrictCode = region || ''
        this.orderDetail.receiverProvince = provinceName || ''
        this.orderDetail.receiverCity = cityName || ''
        this.orderDetail.receiverDistrict = regionName || ''
        this.getDeliveryDate()
      }
    },
    clearReceivedContact() {
      // 收货联系人 姓名
      this.orderDetail.receiverName = ''
      // 电话
      this.orderDetail.receiverPhone = ''
      // 地址
      this.orderDetail.receiverAddress = ''
    },
    clearInvoiceContact() {
      // 收货联系人 姓名
      this.orderDetail.receivingInvoiceName = ''
      // 电话
      this.orderDetail.receivingInvoicePhone = ''
      // 地址
      this.orderDetail.receivingInvoiceAddress = ''
    },
    changeInvoiceContact(val) {
      const invoiceContactData = this.invoiceContactList.find(item => item.contactId === val)
      if (invoiceContactData) {
        const { contactName, mobilephone, telephone, address } = invoiceContactData
        // 收货联系人 姓名
        this.orderDetail.receivingInvoiceName = contactName
        // 电话
        this.orderDetail.receivingInvoicePhone = mobilephone || telephone || '--'
        // 地址
        this.orderDetail.receivingInvoiceAddress = address || '--'
      }
    },
    queryContactListByCustomer(contact, type, customerNumber) {
      this.loadingContact = true
      const { salesOrganization, productGroup, distributionChannel } = this.orderDetail
      const param = {
        salesOrganization,
        productGroup,
        distributionChannel,
        contactName: contact,
        current: 1,
        size: 20,
        customerCode: customerNumber
      }
      editOrder.getContactList(param).then(res => {
        this.loadingContact = false
        if (res && res.code === 200) {
          const contactList = [
            contactHeader,
            ...res.data.records
          ]
          switch (type) {
            case 'order':
              this.orderContactList = contactList
              break
            case 'received':
              this.receivedContactList = contactList
              break
            case 'invoice':
              this.invoiceContactList = contactList
              break
          }
        }
      })
    },
    queryContactList(contact, type) {
      const { customerNumber } = this.clientDetail
      this.queryContactListByCustomer(contact, type, customerNumber)
    },
    queryOrderContactList(contact) {
      this.queryContactList(contact, 'order')
    },
    queryReceivedContactList(contact) {
      this.queryContactList(contact, 'received')
    },
    queryInvoiceContactList(contact) {
      const { invoiceReceiver } = this.orderDetail
      const { customerNumber } = this.clientDetail
      const num = invoiceReceiver || customerNumber
      this.queryContactListByCustomer(contact, 'invoice', num)
    },
    formatPriceMsg(msg) {
      msg += ''
      return msg.replace(/\]|\[|\s+/gmi, '').replace(/,/gmi, '\n')
    },
    async createWorkOrder(data, response) {
      const { orderType } = data
      if (orderType !== 'Z001') return true
      const { bizCode, msg, data: resData } = response
      console.log(data, bizCode, msg, resData)
      if (bizCode === 500001) {
        let confirmMsg = ''
        if (Array.isArray(resData)) {
          confirmMsg = this.formatPriceMsg(resData.join('\n'))
          if (confirmMsg) {
            this.$alert(`<div style="max-height:400px;overflow:auto;"><pre>${confirmMsg}</pre></div>`, '操作提示', {
              type: 'warning',
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确认'
              // cancelButtonText: '确认'
            })
              // .then(action => {
              //   console.log(action);
              //   const loading = this.$loading({
              //     background: 'rgba(0, 0, 0, 0.8)',
              //     lock: true
              //   })
              //   editOrder.createWorkOrderApi(data)
              //     .then(res => {
              //       if (res.code === 200) {
              //         this.$message.success(res.msg || '提交成功！');
              //       } else {
              //         this.$message.error(res.msg || '操作失败！');
              //       }
              //     })
              //     .finally(() => {
              //       loading.close();
              //     })
              // })
              .catch(action => {
                console.log(action)
              })
          }
        }
      } else {
        this.$confirm(`<pre style="max-height:400px;overflow:auto;">${msg}</pre>`, '操作提示', {
          type: 'error',
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认'
        })
          .then(action => {
            console.log(action)
          })
          .catch(action => {
            console.log(action)
          })
      }
    },
    changeGoodsList (value, field) {
      this.goodsList.forEach((item, index) => {
        this.$set(item, field, value)
      })
    },
    submitDelivery(orderData) {
      Object.keys(orderData).forEach(name => {
        if (typeof orderData[name] === 'boolean') {
          orderData[name] = orderData[name] ? 'X' : 'Z'
        }
      })
      const {
        orderContact, orderContactName, orderContactAddress, orderContactPhone,
        receiverContact, receiverName, receiverPhone, receiverAddress, deliveryRequirements
      } = orderData
      this.orderContactList = [
        contactHeader,
        { contactId: orderContact, contactName: orderContactName, address: orderContactAddress, mobilephone: orderContactPhone }
      ]
      this.receivedContactList = [
        contactHeader,
        { contactId: receiverContact, contactName: receiverName, address: receiverAddress, mobilephone: receiverPhone }
      ]
      this.orderDetail = {
        ...this.orderDetail,
        ...orderData,
        deliveryRequirements: (deliveryRequirements && deliveryRequirements.length > 0)
          ? deliveryRequirements.join(',') : ''
      }
    },
    submitInvoice(invoiceData) {
      const { orderData } = invoiceData
      Object.keys(orderData).forEach(name => {
        if (typeof orderData[name] === 'boolean') {
          orderData[name] = orderData[name] ? 'X' : 'Z'
        }
      })
      const {
        receivingInvoiceContact, receivingInvoiceName, receivingInvoicePhone, receivingInvoiceAddress
      } = orderData
      this.invoiceContactList = [
        contactHeader,
        { contactId: receivingInvoiceContact, contactName: receivingInvoiceName, address: receivingInvoiceAddress, mobilephone: receivingInvoicePhone }
      ]
      this.orderDetail = {
        ...this.orderDetail,
        ...orderData
      }
    },
    submitProductDetail(value) {
      const { index, data } = value
      if (data && data.position && data.position === -1) {
        if (data.positionList?.[0]?.code !== -1) {
          (data.positionList || []).unshift({
            code: -1,
            name: '自动挑仓'
          })
        }
      }
      this.$set(this.goodsList, index, data)
      this.setTotalPrice()
      this.pageList()
      this.getDeliveryDate()
    },
    submitProductPlan(value) {
      const { index, data } = value
      this.goodsList[index] = data
      this.setTotalPrice()
    },
    isSkuValid() {
      if (this.goodsList) {
        const errList = []
        let quantityError = false
        let positionError = false
        let factoryError = false
        let directDeliverySupplierError = false
        let customerDateError = false
        let taxedPriceError = false
        let freeTaxPriceError = false
        let costCenterError = false
        let generalLedgerError = false
        let customerMaterialNameError = false
        this.goodsList.forEach(sku => {
          // if (!quantityError && !sku.quantity) {
          //   errList.push('商品信息商品数量不能为空！')
          //   quantityError = true
          // }
          if (
            !sku.materiel &&
            !quantityError &&
            (!sku.quantity || Number.parseFloat(sku.quantity) === 0) &&
            (!sku.customerMaterialQuantity ||
              Number.parseFloat(sku.customerMaterialQuantity) === 0)
          ) {
            errList.push('商品信息商品数量和客户物料数量不能同时为空！');
            quantityError = true;
          }
          if (!positionError && !sku.position && sku.materiel) {
            errList.push('商品信息库位信息不能为空！')
            positionError = true
          }
          if (!factoryError && !sku.factory && sku.materiel) {
            errList.push('商品信息工厂信息不能为空！')
            factoryError = true
          }
          if (!customerMaterialNameError && !sku.customerMaterialName && !sku.materiel) {
            errList.push('商品客户物料名称信息不能为空！')
            customerMaterialNameError = true
          }
          if (!this.isForecast) {
            if (!directDeliverySupplierError && !sku.directDeliverySupplier && sku.materiel) {
              errList.push('商品信息直送供应商不能为空！')
              directDeliverySupplierError = true;
            }
          }
          if (this.isForecast) {
            if (!customerDateError && !sku.customerDate) {
              errList.push('商品信息客户期望送达日期不能为空！')
              customerDateError = true
            }
          }
        if (!isServiceOrder(this.orderDetail.orderType) && !isForecastOrder(this.orderDetail.orderType) && !isFreeOrder(this.orderDetail.orderType)) {
            if (!taxedPriceError && !sku.taxPrice && this.isTaxedCustomer) {
                errList.push('商品信息含税价格不能为空！')
                taxedPriceError = true
            }
            if (!freeTaxPriceError && !sku.freeTaxPrice && !this.isTaxedCustomer) {
              errList.push('商品信息未税价格不能为空！')
              freeTaxPriceError = true
            }
          }
          let costCenterDesc = this.costCenterOptions.find(item => item.costCenter === sku.costCenter)?.description
          if (this.isZ007 && (!costCenterError && (!sku.costCenter || !costCenterDesc) && isInnerOrderReason(this.orderDetail.orderReason))) {
            errList.push('成本中心/描述不能为空！')
            costCenterError = true
          }
          if (this.isZ007 && !generalLedgerError && !sku.generalLedgerAccount && isInnerOrderReason(this.orderDetail.orderReason)) {
            errList.push('总账科目不能为空！')
            generalLedgerError = true
          }
        })
        const skuError = !quantityError && !positionError &&
          !factoryError && !customerDateError && !directDeliverySupplierError && !taxedPriceError && !freeTaxPriceError && !costCenterError && !generalLedgerError && !customerMaterialNameError
        if (!skuError) {
          this.$message.error({
            dangerouslyUseHTMLString: true,
            message: errList.join('<br>')
          })
          return skuError
        }
      }
      return true;
    },
    submit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const isSkuValid = this.isSkuValid();
          if (!isSkuValid) return;
          const { orderReason } = this.orderDetail
          if (!orderReason &&
            this.orderType !== 'Z001' &&
            this.orderType !== 'Z002' &&
            this.orderType !== 'Z012' &&
            this.orderType !== 'Z014' &&
            this.orderType !== 'ZEV1' &&
            this.orderType !== 'ZEV3'
          ) {
            return this.$message.error({
              dangerouslyUseHTMLString: true,
              message: '订单原因不能为空！'
            })
          }
          if (this.orderDetail.invoiceReceiver !== this.invoiceReceiver) {
            this.$confirm('您修改了收票方，请在更多信息内确认相关的开票信息，若确认无误则点击保存即可完成修改', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
              center: true
            }).then(() => {
              // this.setEdit()
              this.submitOrderFn()
            })
          } else {
            // this.setEdit()
            this.submitOrderFn()
          }
        }
      })
    },
    trimPropertyList(item) {
      if (item && Array.isArray(item.customPropertyList)) {
        item.customPropertyList.forEach(property => {
          if (property.placeholder !== undefined) {
            delete property.placeholder
          }
        })
      }
    },
    changeCustomerDate (row) {
      row.isChangeCustomerDate = true; // 是否修改了客户期望送达日期的标识，修改了客户期望送达日期在提交的时候才去提示
    },
    submitOrderFn() {
      let list = []
      if (this.orderDetail.acceptSupplierDelivery === 0) {
        this.goodsList.forEach((a) => {
          if (a.quantity > a.clearedQty && a.directDeliverySupplier === '1') {
            if (this.orderDetail.acceptSupplierDeliveryOld === 1) {
              // 客户接受直发由是改为否的话只要含有直发行就弹提示
              list.push(`行号${a.soItemNo || a.idx}`)
            } else if (a.soItemNo) {
              // 商品行由非直发改为直发
              let item = this.orderDetail.items.find((b) => a.soItemNo === b.soItemNo && b.directDeliverySupplier !== '1')
              item && list.push(`行号${item.soItemNo}`)
            } else {
              // 没有soItemNo是新增行
              list.push(`行号${a.idx}`)
            }
          }
        })
      }
      if (list.length) {
        this.$confirm(`存在直发冲突冻结行：${list.join('，')}，是否修改客户接受供应商直发=是`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.orderDetail.acceptSupplierDelivery = 1;
          this.setEdit()
        }).catch(() => {
          // this.setEdit()
        })
      } else {
        this.setEdit()
      }
    },
    formatEditParam(newItem) {
      // customerMaterialName:客户物料名称
      // customerMaterialNo  客户物料号
      // customerSpecificationModel  客户规格型号
      // customerOrderNo 客户行号
      const orderServiceFieldsList = orderServiceFields.map(obj => obj.field)
      this.orderDetail = foldFields(this.orderDetail, orderServiceFieldsList.concat('packagingReq'))
      const {
        // soNo,
        // sapOrderNo,
        orderNote,
        supplierAccount,
        disableShipping,
        deliveryOtherNote,
        shippingInfo,
        financialNote,
        agreementNote,
        paymentNote,
        customerReferenceNo,
        collectionAmount,
        projectNo,
        dnIncidentalWay,
        designatedShipping
      } = this.orderDetail
      const param = {
        ...this.orderDetail,
        customerReferenceNo: customerReferenceNo ? customerReferenceNo.trim() : '',
        orderNote: orderNote || '*',
        supplierAccount: supplierAccount || '*',
        disableShipping: disableShipping || '*',
        deliveryOtherNote: deliveryOtherNote || '*',
        shippingInfo: shippingInfo || '*',
        financialNote: financialNote || '*',
        agreementNote: agreementNote || '*',
        paymentNote: paymentNote || '*',
        dnIncidentalWay: dnIncidentalWay || '*',
        designatedShipping: designatedShipping || '*',
        // 开票模式 Todo，支持未来业务
        invoiceMode: '',
        // updateWay: this.updateWay,
        requestId: shortid.generate(),
        items: newItem,
        collectionAmount,
        projectNo,
        specifiedReceiptDayOfWeek: Array.isArray(this.orderDetail.specifiedReceiptDayOfWeek) ? this.orderDetail.specifiedReceiptDayOfWeek.join(',') : this.orderDetail.specifiedReceiptDayOfWeek,
        currency: newItem[newItem.length - 1]?.currency
      }
      param.sapReturnDnNo = this.isShowSapReturnDnNo ? param.sapReturnDnNo : ''
      param.customerReferenceDate = param.customerReferenceDate ? param.customerReferenceDate : ''
      delete param.wholeCustomerReferenceDate;
      return param
    },
    updateConfirm (param, query) {
      const orderUrl = getOrderOptUrl(this.dictList, this.orderDetail.orderType, 'update')
      if (orderUrl) {
        this.handleUpdateResponse1(orderUrl, param, query)
      }
    },
    saveDraft(data, createWorkList) {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)',
        lock: true
      })
      this.isSubmitting = true
      editOrder.createOrAddSketch(data, createWorkList).then(res => {
        if (res.code === 200) {
          let msgList;
          if (res.data.msgList && Array.isArray(res.data.msgList)) {
            msgList = res.data.msgList.join(';');
          }
          const str = `<div style="max-height:400px;max-width:350px;overflow:auto;white-space: break-spaces;">${msgList}</div>`
          this.$alert(str, '操作提示', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            callback: action => {
              window.location.replace(`/sr/draft/list?voucherNoList=${res.data.sketchOrderNo}`)
            }
          })
        } else {
          this.$alert(res.message || res.msg || '操作失败！', '错误', { type: 'error' })
        }
      })
      .catch(err => {
        console.log(err)
        this.$alert(err.message || err.msg || '操作失败')
      })
      .finally(() => {
        loading.close()
        this.isSubmitting = false
      })
    },
    handleUpdateResponse1(orderUrl, param, query) {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)',
        lock: true
      })
      this.isSubmitting = true
      editOrder.setEditOrder(orderUrl, param).then(res => {
        const { errMsgList, remindList, headerResultList, itemResultList } = res.data || {};
        if (res && res.code === 200) {
          const str = '订单修改成功!'
          const msg = remindList && remindList.length > 0
            ? str + ';' + `${remindList.join(' ')}`
            : str
          this.$alert(msg, '订单修改成功', {
            confirmButtonText: '确定',
            callback: action => {
              if (localStorage.getItem('open-in-new-tab') !== 'boss') {
                const hash = this.$route.hash
                const url = buildSoDetailLink({ query, hash })
                location.href = url
              } else {
                this.$closeTag(this.$route.path)
                this.$router.jumpToSoOrderDetail({
                  query
                })
              }
            }
          })
        } else {
          const isAddType = itemResultList?.findIndex(item => item.addType && item.addType === '1') > -1;
          if (errMsgList && Array.isArray(errMsgList)) {
            let message = ''
            message += errMsgList.join('<br/>')
            // 只有新增行报错时才需要报错草稿，已有行报错不允许保存草稿
            if (isAddType) {
              const saveDraftData = deepClone(param);
              saveDraftData.items = saveDraftData.items.filter(item => item.addType === '1').map(item => {
                const curItem = itemResultList.find(i => i.validateItemNo === item.validateItemNo);
                if (curItem) {
                  return {
                    ...item,
                    validatorResultDTOList: curItem.itemValidatorResultList,
                    orderItemNo: curItem.orderItemNo
                  }
                }
                return item
              });
              // 触发协议价审批的条件
              const negotiatedDiscountValidator =
                headerResultList?.length === 0 &&
                itemResultList?.every(
                  (item) =>
                    item?.itemValidatorResultList?.length === 1 &&
                    [
                      'negotiatedDiscountValidator',
                      'negotiatedDiscountValidatorV2'
                    ].includes(item.itemValidatorResultList[0].validateCode)
                );
              const confirmButtonText = negotiatedDiscountValidator ? '一键提交价格审批并保存草稿' : '保存草稿';
              const createWorkList = !!negotiatedDiscountValidator;
              this.$confirm(message, '错误提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText,
                cancelButtonText: '取消',
                type: 'warning'
              })
              .then(() => {
                saveDraftData.validatorResultDTOList = headerResultList;
                saveDraftData.sketchOrderScene = 'manualItemUpdateFailToSketch'
                this.saveDraft(saveDraftData, createWorkList)
              })
              .catch(_err => {
                saveDraftData.validatorResultAllDTO = {
                  headerResultList,
                  itemResultList
                }
                saveDraftData.actionSource = '2'
                editOrder.transformSoFailDataPoint(saveDraftData)
              })
            } else {
              this.$alert(message, '错误提示', {
                type: 'error',
                customClass: 'custom-return-error',
                dangerouslyUseHTMLString: true
              })
            }
          } else {
            this.$alert(res.msg, '错误', {
              type: 'error',
              customClass: 'custom-return-error',
              dangerouslyUseHTMLString: true
            })
          }
        }
      })
      .catch(err => {
        console.log(err)
        this.$alert(err.message || err.msg || '操作失败')
      })
      .finally(() => {
        loading.close()
        this.isSubmitting = false
      })
    },
    handleUpdateResponse(orderUrl, param, query) {
      const loading = this.$loading({
          background: 'rgba(0, 0, 0, 0.5)',
          lock: true
        })
        this.isSubmitting = true
        editOrder.setEditOrder(orderUrl, param).then(res => {
          loading.close()
          this.isSubmitting = false
          const successCB = () => {
            if (localStorage.getItem('open-in-new-tab') !== 'boss') {
              const hash = this.$route.hash
              const url = buildSoDetailLink({ query, hash })
              location.href = url
            } else {
              this.$closeTag(this.$route.path)
              this.$router.jumpToSoOrderDetail({
                query
              })
            }
          }
          if (res) {
            const { code, data, msg, bizCode } = res
            if (code !== 200 && bizCode) {
              this.createWorkOrder(param, res)
            } else if (code === 4000666) {
              const m = data && data.length > 0
                ? data[0]
                : ''
              this.$confirm(m, '错误', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                try {
                  const d = JSON.parse(msg)
                  editOrder.undoAllocate(d).then(res => {
                    this.setEdit()
                  })
                } catch (e) {
                  console.error(e)
                }
              }).catch(() => {
              })
            } else if (code === 201) {
              const str = '订单修改成功!'
              const msg = data && data.length > 0
                ? str + ';' + `${data.join(' ')}`
                : str
              this.$alert(msg, '订单修改成功', {
                type: 'success',
                confirmButtonText: '确定',
                callback: action => {
                  successCB()
                }
              })
            } else if (code === 200) {
              this.$message({
                message: msg || '修改成功',
                type: 'success',
                onClose: () => {
                  successCB()
                }
              })
            } else {
              let message = '修改失败'
              if (data && typeof data === 'string') {
                message = data
              } else if (data && Array.isArray(data) && data.length > 0 && data[0]) {
                message = data.join('<br>')
              } else if (msg) {
                message = msg
              }
              let contentMsg = `<div style="max-height: 300px;overflow: auto">${message}</div>`
              this.$alert(contentMsg, '错误', {
                type: 'error',
                dangerouslyUseHTMLString: true
              })
            }
          }
        }).catch(error => {
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
          loading.close()
          this.isSubmitting = false
        }).finally(() => {
          this.isSubmitting = false
        })
    },
    async setEdit() {
      const validateUnitArr = []
      const InnerOrderReason = isInnerOrderReason(this.orderDetail.orderReason)
      let canEdit = true
      const newList = deepClone(this.goodsList)
      const newItem = newList.map((e, index) => {
        const {
          materiel,
          soItemNo,
          itemPlanDTOList,
          customerMaterialUnit,
          purchaseNote,
          remark,
          customerDate,
          costCenter,
          generalLedgerAccount
        } = e
        if (this.isZ007 && (!costCenter || !generalLedgerAccount || !this.costCenterOptions.find(item => item.costCenter === costCenter)?.description)) {
          canEdit = false
        }
        const toHandleArr = ['customerMaterialNo', 'customerMaterialName', 'customerSpecificationModel', 'customerOrderNo']
        toHandleArr.forEach((key) => {
          e[key] = e[key] && e[key].trim().replace(/(?:\r\n|\r|\n)/g, '')
        })

        if (customerMaterialUnit && customerMaterialUnit.length > 10) {
          validateUnitArr.push({
            soItemNo,
            materiel
          })
        }
        const itemPlanList = itemPlanDTOList ? itemPlanDTOList.map(item => ({
          ...item,
          soItemPlanNo: item.soItemPlanNo
        })) : []
        const data = {
          ...e,
          costCenterDesc: this.costCenterOptions.find(item => item.costCenter === costCenter)?.description,
          remark: remark || '*',
          purchaseNote: purchaseNote || '*',
          customerDate: customerDate || '*',
          itemPlanList
        }
        this.trimPropertyList(data)
        if (data.addType === '1') {
          data.validateItemNo = '800' + (index + Math.floor(1000 + Math.random() * 9000)).toString()
        }
        data.waveDeliveryDate = data.deliveryDate || ''
        delete data.positionList
        delete data.simPositionList
        delete data.deliveryPositionList
        return data
      })
      // 记录newItem，交期判断的二次提交时使用
      this.newItem = newItem;
      // 校验背靠背订单
      if (this.showBTBOrder === false) {
        await checkBTBOrder(this, newItem, true);
        this.showBTBOrder = true;
      }
      if (validateUnitArr && validateUnitArr.length > 0) {
        const msg = validateUnitArr.map(({ soItemNo, materiel }) => `行${soItemNo}-sku为${materiel}`).join(',')
        let contentMsg =
          `<div style="max-height: 300px;overflow: auto">
          【${msg}】的客户物料单位大于10位
          </div>
          `
        this.$alert(contentMsg, '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
        return
      }
      if (!canEdit && InnerOrderReason) {
        this.$alert('成本中心代码/描述、总账科目不能为空', '错误', {
          type: 'error'
        })
        return
      }
      const {
        soNo,
        sapOrderNo
      } = this.orderDetail
      const param = this.formatEditParam(newItem);
      const shipping = this.orderDetail.disableShipping
      if (shipping) {
        if (shipping.length > 300 || shipping.split(',').length > 7) {
          this.$message.error('禁用货运字段最多选择7项且字符串长度不能大于300！')
          return
        }
      }
      let hasConflictFields = false
      // let showCustomerDateConfirmDlg = false
      let confirmButtonText = '确认'
      let conflictMessage = '交货信息中存在冲突字段，系统已自动修正：<br />'
      if (this.orderDetail.hideLogo === 'X' && (this.orderDetail.dnSignatureReq?.indexOf('01') > -1 || this.orderDetail.dnSignatureReq?.indexOf('05') > -1)) {
        conflictMessage += '因隐藏logo=是，故修正送货单签章要求不等于盖红章或每页盖章，紧固件特殊包装要求等于无要求；<br />'
        hasConflictFields = true
      }

      if (this.orderDetail.receiptTimeCategory === 'Z' && (param.specifiedReceiptDayOfWeek?.indexOf('06') > -1 || param.specifiedReceiptDayOfWeek?.indexOf('07') > -1)) {
        conflictMessage += '因工作日和周末均可收=否，故取消指定收货日期=周六/周日选项；'
        hasConflictFields = true
      }
      // newItem.forEach((sku, index) => {
      //   if (sku.isChangeCustomerDate && sku.addType && sku.refuseSystemDeliveryDate !== 'X' && sku.customerDate && sku.originSkuArrivalDate && new Date(sku.customerDate) < new Date(sku.originSkuArrivalDate)) {
      //     conflictMessage += `第${index + 1}行SKU:${sku.skuNo}【客户期望送达日期】${sku.customerDate}小于【原始标准送达日期】${sku.originSkuArrivalDate}，请与客户确认是否接受标期<br />`
      //     hasConflictFields = true
      //     confirmButtonText = '已确认接受标期，直接创建订单'
      //     showCustomerDateConfirmDlg = true
      //   }
      //   if (sku.isChangeCustomerDate && sku.refuseSystemDeliveryDate !== 'X' && sku.customerDate && sku.supplierPlanArrivalDate && new Date(sku.customerDate) < new Date(sku.supplierPlanArrivalDate)) {
      //     conflictMessage += `第${index + 1}行SKU:${sku.skuNo || sku.materiel}【客户期望送达日期】${sku.customerDate}小于【供应侧最新预计送达日期】${sku.supplierPlanArrivalDate}，请与客户确认是否接受标期<br />`
      //     hasConflictFields = true
      //     confirmButtonText = '已确认接受标期，直接创建订单'
      //     showCustomerDateConfirmDlg = true
      //   }
      // })
      // // 交期二次确认弹窗
      // if (showCustomerDateConfirmDlg) {
      //   this.conflictMessage = conflictMessage;
      //   this.showCustomerDateConfirmDlg = true;
      //   return;
      // }

      // if (!param.items.find(item => item.fastenerLogo) && (this.orderDetail.fastenerLabelReq !== '0' || this.orderDetail.fastenerDetect !== '0' || this.orderDetail.fastenerSpecialPackageReq !== '0')) {
      //   conflictMessage += '因不含oem紧固件商品行，故紧固件标签要求/紧固件检测/紧固件特殊包装要求=无要求；';
      //   hasConflictFields = true;
      // }

      if (hasConflictFields) {
        await this.$confirm(conflictMessage, {
          dangerouslyUseHTMLString: true,
          confirmButtonText: confirmButtonText,
          cancelButtonText: '取消',
          showClose: false,
          closeOnClickModal: false
        })
      }
      const query = {
        soNo,
        sapOrderNo,
        id: soNo || sapOrderNo,
        refresh: true
      }
      this.updateConfirm(param, query)
    },
    submitEditAgain(type) {
      const param = this.formatEditParam(this.newItem);
      // 不接受标期并提交
      if (type === 'refuse') {
        param.items.forEach(sku => {
          if (sku.isChangeCustomerDate && sku.addType && sku.refuseSystemDeliveryDate !== 'X' && sku.customerDate && sku.originSkuArrivalDate && new Date(sku.customerDate) < new Date(sku.originSkuArrivalDate)) {
            sku.refuseSystemDeliveryDate = 'X'
          }
          if (sku.isChangeCustomerDate && sku.refuseSystemDeliveryDate !== 'X' && sku.customerDate && sku.supplierPlanArrivalDate && new Date(sku.customerDate) < new Date(sku.supplierPlanArrivalDate)) {
            sku.refuseSystemDeliveryDate = 'X'
          }
        })
      }
      const {
        soNo,
        sapOrderNo
      } = this.orderDetail
      const query = {
        soNo,
        sapOrderNo,
        id: soNo || sapOrderNo,
        refresh: true
      }
      this.showCustomerDateConfirmDlg = false;
      this.updateConfirm(param, query)
    },
    handleCancel() {
      const { soNo, sapOrderNo } = this.orderDetail
      if (localStorage.getItem('open-in-new-tab') !== 'boss') {
        const query = {
          soNo,
          sapOrderNo,
          id: soNo || sapOrderNo,
          refresh: true
        }
        const hash = this.$route.hash
        const url = buildSoDetailLink({ query, hash })
        location.href = url
      } else {
        this.$closeTag(this.$route.path)
        this.$router.jumpToSoOrderDetail({
          query: {
            soNo,
            sapOrderNo: sapOrderNo,
            id: soNo || sapOrderNo,
            refresh: true
          }
        })
      }
    },
    getDefaultDirectDeliverySupplier (salesOrganization, orderType, dictList) {
      if (orderType && salesOrganization) {
        const code = `${orderType}_${salesOrganization}`
        const dict = dictList['orderTypeOfDirectDeliverySupplierList'].find(item => {
          return item.parentCode === code
        })
        if (dict) {
          return '2'
        }
      }
      return '0'
    },
    handleAdd: debounce(function (sku) {
      const { skuNo } = sku
      const { customerNo, salesOrganization, distributionChannel, productGroup } = this.orderDetail || {}
      requestWithLoading(this, editOrder.getSkuDetail(skuNo, {
        customerNo,
        salesOrganization,
        distributionChannel,
        productGroup,
        orderType: this.orderType
      }), detail => {
        const result = this.formatSKU(sku, detail)
        result.addType = '1'
        result.directDeliverySupplier = this.getDefaultDirectDeliverySupplier(salesOrganization, this.orderType, this.dictList)
        result.position = result.directDeliverySupplier === '2' ? -1 : ''
        this.goodsList.unshift(result)
        this.pageList()
      })
    }, 1000),
    addNullSku () {
      const { items, autoBatching, entireOrderRefuseSDD, salesOrganization } = this.orderDetail
      const { currency, refuseSystemDeliveryDate } = items[items.length - 1]
      const sku = {};
      sku.addType = '1';
      sku.uuid = shortid.generate();
      // sku.validateItemNo = sku.uuid;
      sku.quantity = 0
      sku.freeTaxPrice = 0
      sku.taxPrice = 0
      sku.position = ''
      sku.leadTime = ''
      sku.factory = '1000'
      sku.directDeliverySupplier = this.getDefaultDirectDeliverySupplier(salesOrganization, this.orderType, this.dictList)
      sku.position = sku.directDeliverySupplier === '2' ? -1 : ''
      sku.currency = currency
      // 整单时新增行 默认为 原整单是否接受标期标识
      if (autoBatching !== 'X') {
        sku.refuseSystemDeliveryDate = entireOrderRefuseSDD || refuseSystemDeliveryDate
        sku.originRefuseSystemDeliveryDate = sku.refuseSystemDeliveryDate
        const lastCustomerDate = this.goodsList[this.goodsList.length - 1].customerDate;
        if (lastCustomerDate) {
          sku.customerDate = lastCustomerDate
        }
      }
      this.goodsList.unshift(sku)
      this.pageList()
      this.setTotalPrice()
      this.showProductDetail = true
      this.skuDetail = {
        index: 0,
        sku,
        data: this.goodsList[0]
      }
    },
    formatSKU(item, detail) {
      const { salesOrganization, items, autoBatching, entireOrderRefuseSDD } = this.orderDetail
      console.log(autoBatching)
      const { taxType } = this.clientDetail
      const { currency, refuseSystemDeliveryDate } = items[items.length - 1]
      const { skuNo, materialDescribe, customerSkuNo, customerSkuName, customerSkuUnitCount, customerSkuUnit, customerSkuSpecification, skuUnitCount } = item
      const { factoryProductPriceVOMap, customerPriceTax, customerPrice } = detail
      const result = {
        ...detail
      }
      // result.addType = '1'
      result.uuid = shortid.generate()
      // result.validateItemNo = result.uuid;
      result.skuFactoryPriceMap = factoryProductPriceVOMap
      result.materiel = skuNo
      result.sapMaterialName = materialDescribe
      result.quantity = 0
      result.freeTaxPrice = 0
      result.taxPrice = 0
      // result.position = ''
      result.leadTime = ''
      result.deliveryDate = moment().format('yyyy-MM-DD')
      // 客户物料关系相关字段赋值
      result.customerMaterialNo = customerSkuNo;
      result.customerMaterialName = customerSkuName;
      result.customerMaterialUnit = customerSkuUnit;
      result.customerSpecificationModel = customerSkuSpecification;
      result.customerSkuUnitCount = customerSkuUnitCount || 0;
      result.skuUnitCount = skuUnitCount || 0;
      // 整单时新增行 默认为 原整单是否接受标期标识
      if (autoBatching !== 'X') {
        result.refuseSystemDeliveryDate = entireOrderRefuseSDD || refuseSystemDeliveryDate
        result.originRefuseSystemDeliveryDate = result.refuseSystemDeliveryDate
        const lastCustomerDate = this.goodsList[this.goodsList.length - 1].customerDate;
        if (lastCustomerDate) {
          result.customerDate = lastCustomerDate
        }
      }
      if (result && Array.isArray(result.customPropertyList)) {
        if (result?.fillDefaultCustomProperty !== 'Z') {
          result.customPropertyList = result.customPropertyList.map(property => ({
            ...property,
            placeholder: property.customPropertyRemark,
            customPropertyRemark: ''
          }))
        }
      }
      // 处理单位
      if (detail) {
        const { unitCode, unitName } = detail
        if (unitCode && unitName) {
          detail.packageInfoList = [{
            skuNo,
            unitName,
            ruleDes: `1${unitName}/${unitName}`,
            conversion: 1,
            unit: unitCode
          }]
        }
      }
      const { unitCode } = detail
      result.packageInfoList = detail.packageInfoList
      result.quantityUnit = unitCode
      if (currency) {
        result.currency = currency
      }
      if (salesOrganization && salesOrganization.length >= 2) {
        const factory = this.factoryList.find(item => {
          const { code } = item
          if (salesOrganization === '6001') {
            return result.factoryPriorityList[0] === code
          }
          if (salesOrganization !== '6001' && code && code.length >= 2) {
            return code[0] === salesOrganization[0] && code[1] === salesOrganization[1]
          }
          return false
        })
        if (factory) {
          const { code } = factory
          let c = parseInt(code, 10)
          // 如果工邦邦，优先选择1000工厂
          if (c === 1300) {
            c = 1000
          }
          const priceMap = factoryProductPriceVOMap[c]
          if (priceMap && priceMap.taxRateInPoint) {
            result.taxRate = taxType === '0' ? 0 : priceMap.taxRateInPoint
            if (customerPriceTax != null) {
              if (customerPriceTax === '0' || customerPriceTax === 0) {
                result.freeTaxPrice = customerPrice
                result.taxPrice = customerPrice / (1 + result.taxRate)
              } else {
                result.taxPrice = customerPrice
                result.freeTaxPrice = customerPrice * (1 + result.taxRate)
              }
            }
          }
          result.factory = c.toString()
        }
      }
      result.idx = `new-${this.num}`
      this.num += 1
      return result
    },
    changeColumnCheckbox(val) {
      this.itemList.forEach(item => {
        if (!this.disabledRefuseSystemDeliveryDate(item)) {
          this.$set(item, 'refuseSystemDeliveryDate', val)
        }
      })
    },
    disabledRefuseSystemDeliveryDate (row) {
      return row.originRefuseSystemDeliveryDate === 'X'
    },
    changeAmount(value, idx, row) {
      // const index = (this.page - 1) * this.size + idx
      this.setTotalPrice()
      this.getDeliveryDate()
    },
    // 部分销售组织不支持交期查询
    // getDeliveryDate () {
    //   if (['1000', '1001', '1300'].includes(this.orderDetail?.salesOrganization)) {
    //     const skuDemandQtyList = this.goodsList.slice().filter(item => item.addType === '1' && ['1000', '1300'].includes(item.factory)).map((item) => ({ qty: item.quantity, sku: item.skuNo, uuid: item.uuid })).filter(item => item && item.qty && item.sku)
    //     if (skuDemandQtyList.length) {
    //       this.deliveryDateApi(skuDemandQtyList)
    //     }
    //   }
    // },
    getDeliveryDate () {
      const skuDemandQtyList = this.goodsList.slice().filter(item => item.addType === '1').map((item) => ({ qty: item.quantity, sku: item.skuNo, uuid: item.uuid })).filter(item => item.qty && item.sku)
      if (skuDemandQtyList.length && !isForecastOrder(this.orderDetail.orderType)) {
        this.deliveryDateApi(skuDemandQtyList)
      }
    },
    deliveryDateApi (skuDemandQtyList) {
      console.log(this.orderDetail)
      const { salesOrganization, receiverProvinceCode, receiverCityCode, receiverDistrictCode, customerNo, serviceCenterSelfTransport, autoBatching, orderContact, receiverContact, receiptTimeCategory, specifiedReceiptDayOfWeek,
      otherLabelReq, fastenerLabelReq, labelPasteWay, hideLogo, fastenerDetect, packagingReq, fastenerSpecialPackageReq, deliveryOtherNote, bidCustomer, dnIncidentalWay, acceptSupplierDelivery } = this.orderDetail
      const data = {
        customer: customerNo,
        salesOrganization,
        skuDemandQtyList: skuDemandQtyList.map(item => ({ qty: item.qty, sku: item.sku })),
        demandProvinceCode: receiverProvinceCode,
        demandCityCode: receiverCityCode,
        demandDistrictCode: receiverDistrictCode,
        serviceCenterSelfTransport,
        orderContactId: orderContact,
        receiptContactId: receiverContact,
        receiptTimeCategory,
        specifiedReceiptDayOfWeek: Array.isArray(specifiedReceiptDayOfWeek) ? specifiedReceiptDayOfWeek.join(',') : specifiedReceiptDayOfWeek,
        otherLabelReq: Array.isArray(otherLabelReq) ? otherLabelReq.join(',') : otherLabelReq,
        fastenerLabelReq: Array.isArray(fastenerLabelReq) ? fastenerLabelReq.join(',') : fastenerLabelReq,
        labelPasteWay: Array.isArray(labelPasteWay) ? labelPasteWay.join(',') : labelPasteWay,
        hideLogo,
        fastenerDetect,
        packagingReq: Array.isArray(packagingReq) ? packagingReq.join(',') : packagingReq,
        fastenerSpecialPackageReq,
        deliveryOtherNote,
        bidCustomer,
        dnIncidentalWay,
        acceptSupplierDelivery,
        orderType: this.orderType
        // demandStreetTownCode: '8540040'
      }
      if (!skuDemandQtyList.length) {
        return
      }
      if (!salesOrganization) {
        return this.$message.error('销售组织不能为空！')
      }
      if (!receiverProvinceCode) {
        return this.$message.error('客户收货省份代码不能为空！')
      }
      if (!receiverCityCode) {
        return this.$message.error('客户收货城市代码不能为空！')
      }
      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      editOrder.getDeliveryTime(data, { autoBatching })
        .then(res => {
          if (res) {
            if (res.code === 200 && res.success && res.data) {
              this.setRowDate(skuDemandQtyList, res.data)
            } else {
              this.$message.error(res.msg || '查询交期出错了，请稍后再试！')
            }
          } else {
            this.$message.error('查询交期出错了，请稍后再试！')
          }
        })
        .finally(() => {
          this.loading && this.loading.close()
        })
    },
    setRowDate(sourceList, resList) {
      const { autoBatching } = this.orderDetail
      let errorMsg = '<div style="max-height: 300px;overflow: auto">'
      let hasError = false
      let fillList = []
      resList.forEach((response) => {
        const sourceItem = sourceList.find(source => source.sku === response.material && source.qty === response.quantity)
        sourceList = sourceList.filter(item => item !== sourceItem)
        const { waveDeliveryDate, promoteInfo, material, skuArrivalDate } = response
        const index = this.goodsList.slice().findIndex((item) => item.uuid === sourceItem.uuid)
        if (!waveDeliveryDate && promoteInfo) {
          hasError = true
          errorMsg += `第${index + 1}行SKU【${material}】查询交期失败：${promoteInfo}<br />`
        }
        fillList.push({ ...response, waveDeliveryDate, index, skuArrivalDate })
        // this.goodsList[index].
      })
      errorMsg += '</div>'
      // autoBatching 不勾选就是整单
      if (autoBatching !== 'X') {
        try {
          let max = fillList.filter(x => x.waveDeliveryDate).sort((x, y) => new Date(y.waveDeliveryDate) - new Date(x.waveDeliveryDate))[0]
          fillList.forEach(item => {
            item.waveDeliveryDate = max.waveDeliveryDate
          })
        } catch (err) {
          console.log(err)
        }
      }
      console.log(fillList)
      fillList.forEach(({ waveDeliveryDate, index, skuArrivalDate, originSkuArrivalDate }) => {
        this.goodsList[index].deliveryDate = waveDeliveryDate
        this.goodsList[index].skuArrivalDate = skuArrivalDate
        this.goodsList[index].originSkuArrivalDate = originSkuArrivalDate
        // this.changePageRow(waveDeliveryDate, index, 'deliveryDate')
      })
      if (hasError) {
        this.$alert(errorMsg, '操作提示', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }
    },
    initGoodsList(data) {
      if (!data) {
        return
      }
      if (this.$route.query.selectedItem) {
        const item = data.find(item => item.soItemNo === this.$route.query.selectedItem)
        const newItem = deepClone(item)
        newItem.referType = '03'
        newItem.omsReferenceOrderItemNo = newItem.soItemNo
        newItem.omsReferenceOrderNo = this.orderDetail.soNo
        newItem.quantity = newItem.replenishNum
        newItem.skuNo = newItem.materiel
        newItem.addType = '1'
        newItem.uuid = shortid.generate()
        newItem.soItemNo = ''
        newItem.sapItemNo = ''
        newItem.clearedQty = 0
        data.unshift(newItem)
      }
      this.goodsList = data.map(item => {
        const oldItem = this.goodsList.find(row => row.soItemNo === item.soItemNo)
        if (item && Array.isArray(item.customPropertyList)) {
          if (item?.fillDefaultCustomProperty !== 'Z') {
            item.customPropertyList = item.customPropertyList.map(property => ({
              ...property,
              placeholder: property.customPropertyRemark,
              customPropertyRemark: property.customPropertyRemark || ''
            }))
          }
        }
        if (item && oldItem && oldItem.customerDate !== item.customerDate) {
          item.isChangeCustomerDate = true
        }
        return {
          ...oldItem,
          ...item,
          quantityUnit: this.formatQuantityUnit(item.quantityUnit),
          itemPlanDTOList: null,
          simPositionList: [],
          originRefuseSystemDeliveryDate: item.refuseSystemDeliveryDate,
          leadTime: (item.deliveryPosition ? item.deliveryReceiverLeadTime : item.transferLeadTime) || oldItem?.leadTime
        }
      })
      this.total = this.goodsList.length
      this.pageList()
      this.getDeliveryDate()
      if (this.total > 0) {
        this.positionList = this.goodsList.map(item => item.position)
        this.quantityList = this.goodsList.map(item => item.quantity)
      }
      this.setTotalPrice()
    },
    downloadMultiSku() {
      const { soNo } = this.orderDetail
      if (soNo) {
        window.open(`/api-opc/v1/excel/batchDownloadSku?soNo=${soNo}`, '_blank')
      }
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
    },
    handleUploadSuccess(res, file, fileList) {
      this.loading && this.loading.close()
      if (res && res.code === 200) {
        const { updateWay, itemVOS } = res.data
        this.initGoodsList(itemVOS)
        this.updateWay = updateWay
      } else if (res && res.code !== 200 && res.msg) {
        this.$alert(res.msg, '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    }
  }
}
</script>

<style lang="scss">
  .el-message-box__message {
    p {
      word-break: break-word;
    }
  }

  .pagination-container {
    text-align: right;
  }

  .upload {
    display: inline;
  }
</style>

<style lang="scss" scoped>
  .baseInfo,
  .clientInfo {
    padding: 10px;

    .el-row {
      margin-bottom: 20px;
    }

    .title {
      margin-bottom: 20px;
    }
  }

  .clientInfo {
    .clientSum {
      > span {
        margin-right: 20px;
      }

      .el-tag {
        margin-right: 10px;
      }
    }
  }

  .freezeReason {
    display: flex;
    align-items: center;

    .el-tag {
      margin-right: 10px;
    }
  }

  .merchant {
    padding-bottom: 20px;
  }

  .merchantInfo {
    margin-bottom: 10px;

    .merchantInfo-row {
      margin-left: 10px;
      color: #909399;

      span {
        margin-right: 20px;
      }
    }
  }

  .extraInfo {
    padding: 10px;
  }

  .btnGroup {
    margin: 20px 0 40px;
  }

  .productDesc {
    display: flex;
    align-items: center;

    img {
      width: 60px;
      height: 60px;
      margin-right: 5px;
    }

    > div {
      text-align: left;
    }
  }

  .selectClientItem {
    div:nth-child(1) {
      width: 120px;
    }

    div:nth-child(2) {
      width: 120px;
    }

    div:nth-child(3) {
      width: 120px;
    }

    div:nth-child(4) {
      width: 300px;
      overflow: auto;
    }
  }

  .ba-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ba-row-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ba-row-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .ba-row-start {
    display: flex;
  }
</style>
<style lang="scss">
  .btb-wrapper {
    overflow-y: scroll;
    max-height: 400px;
  }
</style>
