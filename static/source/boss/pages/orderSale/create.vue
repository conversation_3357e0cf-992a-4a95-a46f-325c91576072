<template>
  <div class="app-container">
    <Header
      :company="customerInfo.companyName"
      :order-type="customerInfo.categoryName"
    />
    <el-form
      ref="orderForm"
      label-width="130px"
      :rules="rules"
      :model="orderData"
    >
      <DividerHeader>客户信息</DividerHeader>
      <el-row :gutter="20">
        <el-col :span="12" class="customer-select">
          <el-form-item label="选择客户" prop="customer">
            <el-select
              ref="customer"
              v-model="orderData.customer"
              filterable
              clearable
              remote
              reserve-keyword
              placeholder="请输入客户编号/名称"
              style="flex:1"
              value-key="customerNumber"
              :remote-method="queryCustomerList"
              :loading="loadingCustomer"
              @change="changeCustomer"
            >
              <el-option
                v-for="(item, index) in customerList"
                :key="item.customerId"
                :label="item.customerName"
                :value="item"
                :disabled="index === 0 || !isCustomerAvailable(item)"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
                >
                  <div>{{ item.customerNumber }}</div>
                  <div>{{ item.cityName }}</div>
                  <div>{{ item.customerName }}</div>
                </div>
              </el-option>
            </el-select>
            <el-link
              type="primary"
              :underline="false"
              class="viewCustomer"
              @click="viewCustomerDetail"
            >
              查看客户详情
              <i class="el-icon-view el-icon--right" />
            </el-link>
            <div v-if="vFlag" class="customer-info">
              请在客户详情中修改售达方与送达方信息
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="直/分销渠道" prop="distributionChannel">
            <el-select
              v-model="orderData.distributionChannel"
              style="width:100%"
              @change="changeDistributionChannel"
            >
              <el-option
                v-for="item in saleMap"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户订单号" prop="orderNo">
            <el-input
              v-model="orderData.orderNo"
              clearable
              maxlength="35"
              placeholder="客户订单号"
              style="width:100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="货币" prop="currency">
            <div class="currency-row">
              <el-select
                v-model="orderData.currency"
                size="mini"
                placeholder="请选择"
                @change="handleCurrencyChange"
                clearable
                style="width:240px"
              >
                <el-option
                  style="margin-bottom:5px"
                  v-for="item in dictList['currencySymbol']"
                  :key="item.parentCode"
                  :label="item.name"
                  :value="item.parentCode"
                >
                  <div class="current-option">
                    <img
                      :src="prefix + item.parentCode + '.png'"
                      class="flag"
                    />
                    <span>{{ item.name }}</span>
                  </div>
                </el-option>
              </el-select>
              <img
                :src="prefix + orderData.currency + '.png'"
                class="currency-flag"
                v-if="prefix + orderData.currency + '.png'"
              />
            </div>
            <div
              class="strong"
              v-if="companycode === '2400' || companycode === '3100'"
            >
              *请确认币种是否正确，创建订单后将无法修改币种
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="销售范围" prop="selectedSalesRange">
            <el-select
              v-model="orderData.selectedSalesRange"
              style="width:100%"
              value-key="idx"
              placeholder="请选择销售范围"
              @change="changeSalesRange"
            >
              <el-option
                v-for="item in saleOrgList.filter(
                  (so) =>
                    companycode &&
                    so.data &&
                    so.data.salesOrganization &&
                    so.data.salesOrganization.substring(0, 2) ===
                      companycode.substring(0, 2)
                )"
                :key="item.key"
                :label="item.value"
                :value="item.data"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" style="text-align:center;margin-top: 5px;">
          <el-checkbox
            :indeterminate="customerDateSensitiveIndeterminate"
            v-model="orderData.customerDateSensitive"
            true-label="X"
            false-label="Z"
            @change="handleCustomerDateSensitiveChange"
          >
            客户对整单交期敏感
          </el-checkbox>
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="6" style="text-align:center">
          <el-checkbox
            :indeterminate="urgentIndeterminate"
            v-model="orderData.urgent"
            true-label="X"
            false-label="Z"
            @change="handleUrgentChange"
          >
            商品整单加急
          </el-checkbox>
        </el-col> -->
      </el-row>
      <DividerHeader>交货信息</DividerHeader>
      <el-row :gutter="20">
        <el-col :span="6" class="customer-select">
          <el-form-item label="未发货条件" prop="bidCustomer">
            <el-select
              v-model="orderData.bidCustomer"
              @change="handleChangeBidCustomer"
              placeholder="请选择未发货条件"
            >
              <el-option
                v-for="item in dictList['noDeliveryReason']"
                :key="item.code"
                :disabled="item.status === 'stop'"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4" class="customer-select" style="margin-top: 5px;">
          <el-checkbox
            v-model="orderData.autoBatching"
            true-label="X"
            false-label="Z"
            @change="allowAutoBatching"
            >
            允许分批
          </el-checkbox>
        </el-col>
        <!-- <el-col :span="8" class="customer-select" style="margin-top: 5px;">
          <el-checkbox
            v-model="orderData.entireOrderRefuseSDD"
            :disabled="orderData.autoBatching == 'X' || isBidCustomer"
            @change="handleRefSDD"
            true-label="X"
            false-label="Z"
            >
            整单不接受标准送达日期
          </el-checkbox>
        </el-col> -->
        <el-col :span="6">
          <el-form-item label-width="0" prop="receiptTimeCategory">
            <el-checkbox v-model="orderData.receiptTimeCategory" label="工作日与周末均可收货" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户期望送达日期" prop="customerReferenceDate">
            <el-date-picker
              v-model="orderData.customerReferenceDate"
              :disabled="isBidCustomer"
              :picker-options="pickerOptions"
              @focus="focusDatePicker"
              clearable
              type="date"
              value-format="yyyy-MM-dd"
              @change="handleRefDate"
              placeholder="选择日期"
              style="width:100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <SelectOrderService v-if="JSON.stringify(this.orderServiceDict) !== '{}'" v-model="orderData.specifiedReceiptDayOfWeek" field="specifiedReceiptDayOfWeek" :selectStyle="`width:100%`" />
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="收货联系人" prop="receiverContact">
            <SelectContact
              ref="receiverContactRef"
              title="收货联系人"
              :data.sync="orderData.receiverContact"
              :disabled="!orderData.customer"
              :contactList="contactList"
              :loading="loadingContact"
              :remoteMethod="queryContactList"
              @changeContact="changeContact"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收货人电话" prop="receiverPhone">
            <el-input
              :value="orderData.receiverPhone || '--'"
              placeholder="联系电话"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="收货地址"
            class="longField"
            prop="receiverAddress"
          >
            <el-input
              :value="orderData.receiverAddress || '--'"
              placeholder="收货地址"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <DividerHeader>商品信息</DividerHeader>
      <SelectGood
        ref="selectGood"
        :orderType="orderType"
        :sales-range="orderData.selectedSalesRange"
        :order-data="orderData"
        :customer="orderData.customer"
        :customer-detail="cusDetail"
        :factoryList="factoryList"
        :totalDiscount.sync="totalDiscount"
        :costCenterOptions="costCenterOptions"
        v-on="$listeners"
        @updateCustomer="updateCustomer"
        @changeUrgent="handleItemUrgentChange"
        @changeCustomerDateSensitive="handleItemCustomerDateSensitiveChange"
      />
      <DividerHeader>其他信息</DividerHeader>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="收票联系人" prop="receivingInvoiceContact">
            <SelectContact
              ref="receivingInvoiceContactRef"
              title="收票联系人"
              :data.sync="orderData.receivingInvoiceContact"
              :disabled="!orderData.customer"
              :contactList="invoiceContactList"
              :loading="loadingInvoiceContact"
              :remoteMethod="queryReceivingInvoiceContactList"
              @changeContact="changeReceivingInvoiceContact"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收票人电话" prop="invoicePhone">
            <el-input
              :value="orderData.invoicePhone || '--'"
              placeholder="收票人电话"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item
            label="收票地址"
            class="longField"
            prop="invoiceAddress"
          >
            <el-input
              :value="orderData.invoiceAddress || '--'"
              placeholder="收票地址"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单联系人" prop="orderContact">
            <SelectContact
              ref="orderContactRef"
              title="订单联系人"
              :data.sync="orderData.orderContact"
              :disabled="!orderData.customer"
              :contactList="orderContactList"
              :loading="loadingOrderContact"
              :remoteMethod="queryOrderContactList"
              @changeContact="changeOrderContact"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="订单人电话" prop="orderContactPhone">
            <el-input
              :value="orderData.orderContactPhone || '--'"
              placeholder="订单人电话"
              :disabled="true"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="订单原因" prop="orderReason">
            <el-select
              clearable
              filterable
              v-model="orderData.orderReason"
              placeholder="请选择订单原因"
              style="width:100%"
              @change="handleOrderReasonChange"
            >
              <el-option
                v-for="item in orderReasonList"
                :key="`${item.code}${item.label}${item.parentCode}`"
                :disabled="item.status === 'stop'"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item v-if="isShowSapReturnDnNo" label="sap退货交货单" prop="sapReturnDnNo">
            <el-input
              v-model="orderData.sapReturnDnNo"
              placeholder="请输入814单号"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="订单备注" class="longField">
            <el-input
              v-model="orderData.orderNote"
              placeholder="订单备注"
              type="textarea"
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="订单附件">
            <UploadAttachFiles :file-list.sync="orderData.attachmentList" />
          </el-form-item>
        </el-col>
      </el-row>
      <div class="ba-row-center btnGroup">
        <el-button type="primary" plain @click="viewEditMore"
          >编辑更多</el-button
        >
        <el-button type="primary" @click="submitOrder('orderForm')">确认创建</el-button>
        <el-button type="primary" @click="submitOrderDraft()">保存草稿</el-button>
      </div>
    </el-form>
    <EditCustomer
      :key="detailKey"
      :show-dialog.sync="showCustomerEdit"
      :selected-sales-range="orderData.selectedSalesRange"
      :order-data="orderData"
      :customer-detail.sync="cusDetail"
      :vFlag="vFlag"
      width="800px"
      @submitCustomer="submitCustomer"
      @getDeliveryTime="getDeliveryTime"
    />
    <EditOrderMore
      :customer="orderData.customer"
      :show-dialog.sync="showEditMore"
      :order-data="orderData"
      :customer-detail.sync="cusDetail"
      :order-type="orderType"
      :company-code="companycode"
      width="800px"
      @submitInvoice="submitInvoice"
      @submitDelivery="submitDelivery"
      @getDeliveryTime="getDeliveryTime"
    />
  </div>
</template>

<script>
import * as shortid from 'shortid'
import flatten from 'lodash/flatten'
import endsWith from 'lodash/endsWith'
import get from 'lodash/get'
import * as id from '@boss/id'
import debounce from 'lodash/debounce'
import DividerHeader from './components/common/DividerHeader'
import Header from './components/create/Header'
import EditCustomer from './components/create/EditCustomerDlg'
import EditOrderMore from './components/create/EditOrderMore'
import SelectGood from './components/create/SelectGood'
import SelectContact from './components/common/SelectContact'
import UploadAttachFiles from './components/common/UploadAttach.vue'
import { getCostCenter } from '@/api/mm';
import {
  createOrder, searchClients,
  getClientDetail, searchContactListByGroup,
  exchange, saveDraft as saveDraftApi,
  getContactById, uploadSku, transformSoFailDataPoint
} from '@/api/orderSale'
import { contactHeader } from '@/pages/orderSale/constants'
import { getOrderOptUrl, getFactoryList, requestWithLoading, getDisabledDate } from '@/pages/orderSale/utils'
import { isServiceOrder, isFreeOrder, isForecastOrder, isInnerOrderReason } from '@/pages/orderSale/utils/orderType'
import { deepClone } from '@/utils/index'
import { foldFields, orderServiceFields } from '@/utils/orderService'
import uniq from 'lodash/uniq'
import SelectOrderService from '@/pages/orderSale/components/common/SelectOrderService'

export default {
  name: 'OrderCreate',
  components: {
    Header,
    DividerHeader,
    EditCustomer,
    SelectGood,
    SelectContact,
    EditOrderMore,
    SelectOrderService,
    UploadAttachFiles
  },
  data () {
    const { companycode, categorycode } = this.$route.params
    let rules = {
      customer: [
        { required: true, message: '请选择客户', trigger: 'blur' }
      ],
      distributionChannel: [
        { required: true, message: '请选择直/分销渠道', trigger: 'blur' }
      ],
      selectedSalesRange: [
        { required: true, message: '请选择销售范围', trigger: 'blur' }
      ]
    }
    if (!isForecastOrder(categorycode) && categorycode !== 'Z014') {
      rules = {
        ...rules,
        receiverContact: [
          { required: true, message: '请选择收货联系人', trigger: ['blur', 'change'] }
        ],
        orderContact: [
          { required: true, message: '请选择订单联系人', trigger: ['blur', 'change'] }
        ],
        receivingInvoiceContact: [
          { required: true, message: '请选择收票联系人', trigger: ['blur', 'change'] }
        ]
      }
    }
    if (!(categorycode === 'Z001') &&
      !(categorycode === 'Z002') &&
      !(categorycode === 'Z012') &&
      !(categorycode === 'Z014') &&
      !(categorycode === 'ZEV1') &&
      !(categorycode === 'ZEV3')
    ) {
      rules = {
        ...rules,
        orderReason: [
          { required: true, message: '请选择订单原因', trigger: 'blur' }
        ]
      }
    }
    return {
      bossID: id(),
      companycode,
      orderType: categorycode ? categorycode.toUpperCase() : '',
      customerList: [],
      cusDetail: {},
      loadingCustomer: false,
      loadingContact: false,
      loadingOrderContact: false,
      loadingInvoiceContact: false,
      urgentIndeterminate: false,
      customerDateSensitiveIndeterminate: false,
      orderData: {
        customerDateSensitive: 'Z',
        urgent: 'Z',
        selectedSalesRange: null,
        packagingReq: ['0'],
        receiverContact: null,
        orderContact: null,
        receivingInvoiceContact: null,
        currency: (companycode === '2400' || companycode === '3100') ? 'USD' : 'CNY',
        exchangeRate: 0,
        printNum: 0,
        currencySymbol: '￥',
        specifiedReceiptDayOfWeek: '',
        receiptTimeCategory: '',
        autoBatching: '',
        entireOrderRefuseSDD: '',
        customer: {
          saleMap: {},
          saleOrgList: []
        },
        orderSource: '',
        sapReturnDnNo: '',
        attachmentList: []
      },
      addedSkuList: [],
      showCustomerEdit: false,
      showEditMore: false,
      priviousCustomer: null,
      detailKey: '',
      receiverPassedContactList: [],
      reqContactList: [],
      orderPassedContactList: [],
      orderReqContactList: [],
      invoicePassedContactList: [],
      invoiceReqContactList: [],
      totalDiscount: 0,
      rules,
      prefix: 'https://files.zkh360.com/so/currency/',
      num: 0,
      page: 1,
      size: 20,
      costCenterOptions: [],
      prechecks: {
        A: false,
        B: false
      },
      pickerOptions: {
        disabledDate: (time) => {
          const specifiedReceiptDayOfWeek = this.orderData?.specifiedReceiptDayOfWeek || this.cusDetail?.specifiedReceiptDayOfWeek?.split(',')
          const receiptTimeCategory = this.orderData?.receiptTimeCategory === '' ? this.cusDetail?.receiptTimeCategory : this.orderData?.receiptTimeCategory
          const check = !['Z002', 'Z014'].includes(this.orderType) && !['8', 'X'].includes(this.orderData.bidCustomer)
          return getDisabledDate(time, specifiedReceiptDayOfWeek, receiptTimeCategory, check)
        }
      }
    }
  },
  computed: {
    isBidCustomer () {
      const bidCustomer = this.orderData.bidCustomer
      return (bidCustomer === '8' || bidCustomer === 'X') && this.isZ001
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderServiceDict () {
      return this.$store.state.orderCommon.orderServiceDict || {}
    },
    sapReturnOrderValidator () {
      return this.$store.state.orderCommon.sapReturnOrderValidator
    },
    mmDictList () {
      return this.$store.state.orderPurchase.dictList || {}
    },
    isZ007() {
      return this.orderType === 'Z007'
    },
    isZ001 () {
      return /Z001/gim.test(this.orderType)
    },
    saleMap () {
      const { customer } = this.orderData || {}
      console.log('saleMap...', customer)
      return customer && customer.saleMap ? Object.keys(customer.saleMap).map(key => {
        const value = customer.saleMap[key]
        return {
          key,
          value
        }
      }) : []
    },
    saleOrgList () {
      const { customer } = this.orderData || {}
      console.log('saleOrgList...', customer)
      return customer && customer.saleOrgList
        ? customer.saleOrgList.map((item, idx) => {
          const { salesOrganization, productGroup, distributionChannel, salesOrganizationName, distributionChannelName, productGroupName } = item
          return {
            data: {
              idx,
              ...item
            },
            key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
            value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`
          }
        })
        : []
    },
    customerInfo () {
      const { companycode, categorycode } = this.$route.params
      const dict = this.dictList
      let companyName = ''
      let categoryName = ''
      if (companycode && categorycode) {
        if (dict && dict['soCategory'] && Array.isArray(dict['soCategory'])) {
          const dictItem = dict['soCategory'].find(item => item.code === categorycode)
          if (dictItem) {
            categoryName = dictItem.name
          }
        }
        if (dict && dict['companyScope'] && Array.isArray(dict['companyScope'])) {
          const dictItem = dict['companyScope'].find(item => item.code === companycode)
          if (dictItem) {
            companyName = dictItem.name
          }
        }
      }
      return {
        companyName,
        categoryName
      }
    },
    contactList () {
      let data = []
      if (this.receiverPassedContactList && this.receiverPassedContactList.length > 0) {
        data = this.receiverPassedContactList
      } else {
        data = this.reqContactList || []
      }
      return [
        contactHeader,
        ...data
      ]
    },
    orderContactList () {
      let data = []
      if (this.orderPassedContactList && this.orderPassedContactList.length > 0) {
        data = this.orderPassedContactList
      } else {
        data = this.orderReqContactList || []
      }
      return [
        contactHeader,
        ...data
      ]
    },
    invoiceContactList () {
      let data = []
      if (this.invoicePassedContactList && this.invoicePassedContactList.length > 0) {
        data = this.invoicePassedContactList
      } else {
        data = this.invoiceReqContactList || []
      }
      return [
        contactHeader,
        ...data
      ]
    },
    skuList () {
      const key = this.getKey()
      return this.$store.state.orderGoods.skuList[key]
    },
    orderReasonList () {
      // 有配置取配置，没配置默认取""对应枚举
      const orList = this.dictList['orderReason'] || []
      let options = orList;
      if (this.orderType) {
        const typeOptions = orList.filter((or) => or.parentCode === this.orderType);
        if (typeOptions.length) {
          options = typeOptions;
        } else {
          options = orList.filter((or) => or.parentCode === '');
        }
      }
      // 未禁用的在上边
      const normal = options.filter(option => option.status !== 'stop')
      // const stop = options.filter(option => option.status === 'stop')
      return normal
    },
    factoryList () {
      const { selectedSalesRange } = this.orderData
      if (selectedSalesRange && this.dictList) {
        const { distributionChannel, salesOrganization } = selectedSalesRange
        return getFactoryList(distributionChannel, salesOrganization, this.dictList)
      }
      return []
    },
    vFlag () {
      return this.cusDetail ? !!this.cusDetail.vflag : false
    },
    isShowSapReturnDnNo () {
      const skuValid = (this.skuList || []).some(item => item.directDeliverySupplier === '0' && endsWith(item.position, '04'))
      return /Z001|Z006|Z007/gim.test(this.orderType) && this.orderData.orderReason === '038' && skuValid
    },
    isTaxedCustomer () {
      const detail = this.cusDetail || {}
      return detail && detail.isTax === '1'
    }
  },
  created () {
    this.$store.dispatch('orderGoods/clearGoods', {
      key: this.getKey()
    })
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    if (JSON.stringify(this.orderServiceDict) === '{}') {
      this.$store.dispatch('orderCommon/getOrderServiceDict')
    }
    if (JSON.stringify(this.mmDictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    this.$store.dispatch('orderCommon/getSearchSkuSwitch')
    // exchange({
    //   sourceCurrency: findOne.parentCode,
    //   targetCurrency: 'CNY'
    // }).then(res => {
    //   if (res && res.code === 200 && res.data) {
    //     const { rate } = res.data
    //     this.orderData.exchangeRate = rate
    //   }
    // })
    this.setDocTitle()
  },
  mounted () {
    this.initByQuery()
    window.vm = this
  },
  watch: {
    'orderData.selectedSalesRange.salesOrganization': function(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.resetRowDate()
      }
    },
    'orderData.receiverContact': function(newVal, oldVal) {
      if (!newVal || !oldVal) {
        return this.resetRowDate()
      }
      let n1 = ''
      let n2 = ''
      const props = ['province', 'city', 'region', 'addressId']
      props.forEach(prop => { n1 += newVal[prop] })
      props.forEach(prop => { n2 += oldVal[prop] })
      if (n1 !== n2) {
        console.log('n1,n2:', n1, n2)
        this.resetRowDate()
      }
    }
  },
  methods: {
    focusDatePicker () {
      const specifiedReceiptDayOfWeek = this.orderData?.specifiedReceiptDayOfWeek?.filter(day => !!day) || this.cusDetail?.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day)
      const receiptTimeCategory = this.orderData?.receiptTimeCategory === '' ? this.cusDetail.receiptTimeCategory : this.orderData?.receiptTimeCategory
      if ((receiptTimeCategory === 'Z' || receiptTimeCategory === false) && specifiedReceiptDayOfWeek?.length && specifiedReceiptDayOfWeek?.every(day => !['0', '01', '02', '03', '04', '05'].includes(day))) {
        this.$message.warning('无可选日期，请修改客户指定日收货或工作日与周末均可收货!')
      }
    },
    handleOrderReasonChange (val) {
      if (this.isShowSapReturnDnNo && !this.sapReturnOrderValidator) {
        this.rules = {
          ...this.rules,
          sapReturnDnNo: [
            { required: true, message: '请输入sap退货交货单', trigger: 'blur' }
          ]
        }
      }
    },
    handleChangeBidCustomer (val) {
      if (this.isZ001 && (val === 'X' || val === '8')) {
        console.log('reset customerData and refuse....')
        this.orderData.customerReferenceDate = ''
        this.handleRefDate('')
        this.orderData.entireOrderRefuseSDD = 'Z'
      }
    },
    async initByQuery () {
      const {
        customerNo, // 客户编码
        salesOrganization, // 销售范围
        distributionChannel, // 分销渠道
        productGroup, // 产品组
        receiverContact, // 收货联系人
        receivingInvoiceContact, // 收票联系人
        orderContact, // 订单联系人
        skuInfoList, // sku编码可能有多个？
        quantity,
        directSupplierType, // 直发类型
        position,
        omsNo, // 外围系统订单号
        customerReferenceNo, // 客户订单号
        orderSource
      } = this.$route.query || {};
      if (orderSource !== 'SH') return
      this.orderData.orderSource = 'SH'
      console.log(distributionChannel, customerNo, receivingInvoiceContact, receiverContact, orderContact, skuInfoList, quantity, directSupplierType, position, omsNo, orderSource,)
      const queryCallback = async () => {
        const findCustomer = this.customerList.find(customer => customer.customerNumber === customerNo)
        if (findCustomer) {
          this.changeCustomer(findCustomer)
        }
        if (customerReferenceNo) {
          this.orderData.orderNo = customerReferenceNo
        }
        if (distributionChannel) {
          this.orderData.distributionChannel = distributionChannel
          this.changeDistributionChannel()
        }
        const key = `${salesOrganization}_${productGroup}_${distributionChannel}`
        const findRange = this.saleOrgList.find(item => item.key === key)
        console.log(key, findRange, this.saleOrgList)
        if (findRange) {
          this.orderData.selectedSalesRange = findRange.data
          await this.changeSalesRange(findRange.data)
        }
        // contact setup
        if (receiverContact) {
          const a1 = async () => {
            const contactList = this.contactList
            let findContact = contactList.find(contact => contact.contactId === receiverContact)
            if (!findContact) {
              const res = await getContactById({ contactId: receiverContact })
              if (res?.code === 200 && res?.data) {
                findContact = res.data
              }
              this.reqContactList.unshift(findContact)
              console.log(res)
            }
            if (findContact) {
              this.orderData.receiverContact = findContact
              this.changeContact(findContact)
            }
            console.log(receiverContact, contactList, findContact)
          }
          await a1();
        }
        if (receivingInvoiceContact) {
          const a2 = async () => {
            const contactList = this.orderContactList
            let findContact = contactList.find(contact => contact.contactId === receivingInvoiceContact)
            if (!findContact) {
              const res = await getContactById({ contactId: receivingInvoiceContact })
              if (res?.code === 200 && res?.data) {
                findContact = res.data
              }
              this.invoiceReqContactList.unshift(findContact)
              console.log(res)
            }
            if (findContact) {
              this.orderData.receivingInvoiceContact = findContact
              this.changeReceivingInvoiceContact(findContact)
            }
            console.log(receivingInvoiceContact, contactList, findContact)
          }
          await a2();
        }
        if (orderContact) {
          const a3 = async () => {
            const contactList = this.invoiceContactList
            let findContact = contactList.find(contact => contact.contactId === orderContact)
            if (!findContact) {
              const res = await getContactById({ contactId: orderContact })
              if (res?.code === 200 && res?.data) {
                findContact = res.data
              }
              this.orderReqContactList.unshift(findContact)
              console.log(res)
            }
            if (findContact) {
              this.orderData.orderContact = findContact
              this.changeOrderContact(findContact)
            }
            console.log(orderContact, contactList, findContact)
          }
          await a3();
        }
        // const afterSearchSkuCallback = async (skuResList) => {
        //   skuResList.forEach(sku => {
        //     sku.quantity = 1
        //     sku.position = '1004'
        //     if (directSupplierType) {
        //       sku.directDeliverySupplier = directSupplierType
        //     }
        //   })
        //   console.log(skuResList)
        //   setTimeout(() => {
        //     if (this.$refs.selectGood.skuList.options.length > 1) {
        //       const sku = this.$refs.selectGood.skuList.options[1]
        //       sku.createdBySH = true
        //       this.$refs.selectGood.currentSelectSku = sku
        //       console.log(this.$refs.selectGood.skuList.options[1].quantity)
        //       this.$refs.selectGood.addSkuToTable()
        //     }
        //   }, 100)
        // }
        if (skuInfoList) {
          this.batchImportSku()
          // this.$refs.selectGood.searchSkuList(skuNo, afterSearchSkuCallback)
        }
      }
      this.queryCustomerList(customerNo, queryCallback)
    },
    batchImportSku() {
      const {
        customerNo, // 客户编码
        salesOrganization, // 销售范围
        distributionChannel, // 分销渠道
        productGroup, // 产品组
        skuInfoList // sku
      } = this.$route.query || {};
      requestWithLoading(this, uploadSku({
        customerNo,
        salesOrganization,
        distributionChannel,
        productGroup,
        orderType: this.orderType,
        // uploadSkuInfoVOList: [{ 'skuNo': 'AF8012', 'quantity': '1' }, { 'skuNo': 'AF8012', 'quantity': '2' }]
        uploadSkuInfoVOList: JSON.parse(skuInfoList)
      }), data => {
        const { uploadSkuInfoDetailVOList, failSkuList, failMsg } = data
        const successCallback = () => {
          if (uploadSkuInfoDetailVOList && uploadSkuInfoDetailVOList.length > 0) {
            this.$refs.selectGood.handleImport(uploadSkuInfoDetailVOList)
          }
        }
        // 非订单原因：对账差异调整禁止添加客户限售商品
        if (failMsg && this.orderData.orderReason !== '038') {
          this.$alert(failMsg, '提示', {
            type: 'error'
          })
          return
        }
        if (failSkuList && failSkuList.length > 0) {
          const failLine = failSkuList.length
          const successLine = uploadSkuInfoDetailVOList.length
          const skus = uniq(failSkuList).join(',')
          this.$alert(`导入成功商品行数量为${successLine}行，失败数量为${failLine}行。` +
            `其中${skus}为无效商品或未维护建议销售价，无法添加`, '错误', {
            confirmButtonText: '确定',
            callback: action => {
              successCallback()
            }
          })
        } else {
          successCallback()
        }
      })
    },
    resetRowDate (val = '') {
      // salesOrganization,
      // skuDemandQtyList: skuDemandQtyList.map(item => ({ qty: item.qty, sku: item.sku })),
      // demandProvinceCode,
      // demandCityCode,
      // demandDistrictCode,
      // demandStreetTownCode
      this.skuList.forEach((item, index) => {
        this.changeRow(val, index, 'deliveryDate')
      })
    },
    changeRow (value, index, type) {
      this.$store.dispatch('orderGoods/changeItem', {
        orderData: this.orderData,
        key: this.getKey(),
        orderType: this.orderType,
        page: this.page,
        size: this.size,
        type,
        index,
        value
      })
    },
    handleRefDate (value) {
      console.log(value)
      this.skuList.forEach((item, index) => {
        this.changeRow(value, index, 'customerDate')
      })
    },
    allowAutoBatching () {
      try {
        this.$refs.selectGood.getDeliveryDate()
      } catch (err) {
        console.error(err)
      }
      this.orderData.entireOrderRefuseSDD = 'Z'
      this.valCustomerDate()
    },
    valCustomerDate () {
      if (this.orderData.autoBatching === 'Z') {
        if (this.skuList.length > 0 && this.skuList.some(item => item.customerDate !== this.skuList[0].customerDate)) {
          this.$confirm('订单发货方式修改为整单发货，请检查和确认是否清空订单行客户期望送达日期并重新维护', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.skuList.forEach((item, index) => {
              this.changeRow('', index, 'customerDate')
            })
          }).catch(() => {
            this.orderData.autoBatching = 'X'
          })
        }
      } else {
        if (this.orderData.customerReferenceDate) {
          this.$confirm('订单发货方式修改为分批发货，请检查和确认客户期望送达日期', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.skuList.forEach((item, index) => {
                this.changeRow(this.orderData.customerReferenceDate, index, 'customerDate')
              })
            }).catch(() => {
              this.orderData.autoBatching = 'Z'
            })
        }
      }
    },
    handleRefSDD (value) {
      this.skuList.forEach((item, index) => {
        this.changeRow(value, index, 'refuseSystemDeliveryDate')
      })
    },
    setDocTitle () {
      let originTitle = document.title
      try {
        document.title = '创建订单'
      } catch (err) {
        document.title = originTitle
      }
    },
    getKey () {
      const { companycode, categorycode } = this.$route.params
      return `${companycode}_${categorycode}`
    },
    isCustomerAvailable (customer) {
      const { companycode } = this.$route.params
      if (customer && companycode && companycode.length > 2) {
        const { saleOrgList } = customer
        return saleOrgList.find(item =>
          item.salesOrganization &&
          item.salesOrganization.length >= 2 &&
          item.salesOrganization.substring(0, 2) === companycode.substring(0, 2))
      }
      return false
    },
    queryCustomerList: debounce(async function(customer, cb) {
      this.loadingCustomer = true
      this.num++;
      let a = this.num;
      try {
        let result = await searchClients(customer)
        this.loadingCustomer = false
        if (a === this.num) {
          if (result && result.code === 200) {
            this.customerList = [
              {
                customerNumber: '客户编码',
                customerName: '客户名称',
                cityName: '城市'
              },
              ...result.data
            ]
          }
        }
        cb && cb()
      } catch (err) {
        console.log(err);
      }
    }, 1000),
    handleQueryContactList (contactName, initFn, callbackFn) {
      const { customerNumber } = this.orderData.customer
      if (this.cusDetail && this.cusDetail.saleOrgVO) {
        const { saleOrgVO } = this.cusDetail
        if (saleOrgVO) {
          const { salesOrganization, productGroup, distributionChannel } = saleOrgVO
          initFn && initFn()
          searchContactListByGroup({
            customerCode: customerNumber,
            contactName,
            distributionChannel,
            productGroup,
            salesOrganization
          }).then(res => {
            callbackFn && callbackFn(res)
          })
        }
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        })
      }
    },
    queryOrderContactList (contactName) {
      this.handleQueryContactList(contactName, () => {
        this.loadingOrderContact = true
        this.orderPassedContactList = []
      }, (res) => {
        this.loadingOrderContact = false
        if (res && res.code === 200) {
          this.orderReqContactList = res.data.records
        }
      })
    },
    queryContactList (contactName) {
      this.handleQueryContactList(contactName, () => {
        this.loadingContact = true
        this.receiverPassedContactList = []
      }, (res) => {
        this.loadingContact = false
        if (res && res.code === 200) {
          this.reqContactList = res.data.records
        }
      })
    },
    queryReceivingInvoiceContactList (contactName) {
      this.handleQueryContactList(contactName, () => {
        this.loadingInvoiceContact = true
        this.invoicePassedContactList = []
      }, (res) => {
        this.loadingInvoiceContact = false
        if (res && res.code === 200) {
          this.invoiceReqContactList = res.data.records
        }
      })
    },
    changeCustomer (customer) {
      const dispatchChangeCustomer = () => {
        // this.$store.dispatch('orderCreate/selectCustomer', customer)
        this.$store.dispatch('orderGoods/clearGoods', {
          key: this.getKey()
        })
        console.log(this)
        console.log(this.$store)
        this.orderData.customer = customer
        this.cusDetail = {}
        this.orderData.selectedSalesRange = null
        this.orderData.distributionChannel = null
        this.orderData.serviceCenterSelfTransport = null
        this.orderData.paymentTerm = null
        this.orderData.moreInvoiceData = null
        this.orderData.moreDeliveryData = null
        this.orderData.receiverContact = null
        this.orderData.receivingInvoiceContact = null
        this.orderData.receivingInvoicePhone = null
        this.orderData.receiverPhone = ''
        this.orderData.receiverAddress = null
        this.orderData.orderContact = null
        this.orderData.orderContactPhone = null
        this.receiverContactList = []
        this.$refs['orderForm'].clearValidate()

        this.receiverPassedContactList = []
        this.orderPassedContactList = []
        this.invoicePassedContactList = []
        this.reqContactList = []
        this.orderReqContactList = []
        this.invoiceReqContactList = []
      }
      if (this.skuList && this.skuList.length > 0) {
        this.$confirm('此操作将删除所选的商品, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.orderDate = {}
          dispatchChangeCustomer()
          this.$refs.customer.blur()
        }).catch(() => {
          this.$refs.customer.blur()
          this.orderData.customer = this.priviousCustomer
        })
      } else {
        this.priviousCustomer = customer
        dispatchChangeCustomer()
      }
    },
    remoteMethod (val) {
      getCostCenter({
        companyCode: this.orderData.selectedSalesRange.salesOrganization,
        costCenter: val
      }).then(data => {
        if (data) {
          this.costCenterOptions = data
        }
      })
    },
    async changeSalesRange (range) {
      if (range) {
        let { salesOrganization, productGroup, distributionChannel } = range
        if (this.skuList && this.skuList[0] && this.skuList[0].productGroup) {
          productGroup = this.skuList[0].productGroup
        }
        this.orderData.distributionChannel = distributionChannel
        if (this.orderData.customer) {
          const { customerNumber } = this.orderData.customer
          const loading = this.$loading({
            background: 'rgba(0, 0, 0, 0.8)',
            lock: true
          })
          await getClientDetail(customerNumber, distributionChannel, productGroup, salesOrganization).then(res => {
            loading.close()
            if (res) {
              if (res.code === 200) {
                this.cusDetail = res.data
                this.orderData = {
                  ...this.orderData,
                  ...res.data,
                  specifiedReceiptDayOfWeek: res.data.specifiedReceiptDayOfWeek?.split(',')?.filter(day => !!day),
                  receiptTimeCategory: res.data.receiptTimeCategory === 'X'
                }
                // mock autoBatching
                // this.orderData.autoBatching = 'X'
                if (this.orderData.autoBatching !== 'X') {
                  this.setRowCustomerDate()
                }
                if (this.isZ007) {
                  this.remoteMethod('')
                }
              } else if (res.data || res.msg) {
                this.$message.error(res.data || res.msg)
              }
            }
          })
          await searchContactListByGroup({
            customerCode: customerNumber,
            contactName: '',
            distributionChannel,
            productGroup,
            salesOrganization
          }).then(res => {
            if (res && res.code === 200) {
              this.reqContactList = res.data.records
              this.orderReqContactList = res.data.records
              this.invoiceReqContactList = res.data.records
            }
          })
        }
      }
    },
    /**
     * 禁用客户编辑提示
     */
    customerEditForbidden () {
      this.$message.warning({
        message: '您已经添加了商品，客户信息不允许修改'
      })
    },
    viewCustomerDetail () {
      if (this.addedSkuList.length > 0) {
        this.customerEditForbidden()
        return
      }
      if (!this.orderData.customer) {
        this.$message.warning({
          message: '请选择客户'
        })
        return
      }
      this.detailKey = shortid.generate()
      this.showCustomerEdit = true
    },
    submitCustomer (detail) {
      const fields = [
        'serviceCenterSelfTransport', 'orderNo', 'customerReferenceDate', 'selectedSalesRange',
        'paymentTerm', 'customerServiceId', 'sellerId', 'customerServiceName',
        'sellerName', 'acceptSupplierDelivery'
      ]
      const data = {}
      fields.forEach(field => {
        if (detail[field] != null) {
          data[field] = detail[field]
        }
      })
      if (this.vFlag) {
        data['customerName'] = detail['customerName']
      }
      if (detail.customerDetail) {
        this.cusDetail = detail.customerDetail
      }
      this.orderData = {
        ...this.orderData,
        ...data
      }
    },
    submitInvoice (invoice) {
      const { receivingInvoiceContact } = invoice
      if (receivingInvoiceContact) {
        this.orderData.receivingInvoiceContact = receivingInvoiceContact
        this.invoicePassedContactList = [receivingInvoiceContact]
        const { contactPhone, address } = receivingInvoiceContact
        this.orderData.invoicePhone = contactPhone
        this.orderData.invoiceAddress = address
      }
      this.orderData.moreInvoiceData = invoice
    },
    submitDelivery (delivery) {
      const { orderContact, receiverContact } = delivery
      if (orderContact) {
        this.orderData.orderContact = orderContact
        this.orderPassedContactList = [orderContact]
        const { contactPhone } = orderContact
        this.orderData.orderContactPhone = contactPhone
      }
      if (receiverContact) {
        this.orderData.receiverContact = receiverContact
        this.receiverPassedContactList = [receiverContact]
        const { contactPhone, address } = receiverContact
        this.orderData.receiverPhone = contactPhone
        this.orderData.receiverAddress = address
      }
      this.orderData.moreDeliveryData = delivery
    },
    getDeliveryTime() {
      setTimeout(() => {
       this.$refs.selectGood.getDeliveryDate()
      }, 500)
    },
    changeContact (contact) {
      if (contact) {
        console.log(contact)
        const { address, contactPhone } = contact
        this.orderData.receiverPhone = contactPhone
        this.orderData.receiverAddress = address
        try {
          this.$refs.selectGood.getDeliveryDate()
        } catch (err) {
          console.error(err)
        }
      }
    },
    changeReceivingInvoiceContact (contact) {
      if (contact) {
        const { address, contactPhone } = contact
        this.orderData.invoicePhone = contactPhone
        this.orderData.invoiceAddress = address
      }
    },
    changeOrderContact (contact) {
      if (contact) {
        const { contactPhone } = contact
        this.orderData.orderContactPhone = contactPhone
      }
    },
    changeDistributionChannel () {
      this.orderData = {
        ...this.orderData,
        selectedSalesRange: {}
      }
    },
    viewEditMore () {
      if (!this.orderData.customer) {
        this.$message.warning({
          message: '请选择客户'
        })
        return
      }
      this.showEditMore = true
    },
    precheckA () {
      // 允许分批 当客户期望日期有值，且小于系统标准发货日期
      let contentMsg = '<div style="max-height: 300px;overflow: auto">'
      let hasError = false
      this.skuList.forEach((sku, index) => {
        console.log(sku.customerDate, sku.customerDate, sku.deliveryDate)
        if (sku.refuseSystemDeliveryDate !== 'X' && sku.customerDate && sku.originSkuArrivalDate && new Date(sku.customerDate) < new Date(sku.originSkuArrivalDate)) {
          hasError = true
          contentMsg += `第${index + 1}行SKU:${sku.skuNo}【客户期望送达日期】${sku.customerDate}小于【原始标准送达日期】${sku.originSkuArrivalDate}，请与客户确认是否接受标期<br />`
        }
      })
      contentMsg += '</div>'
      if (this.prechecks['A'] || !hasError) {
        return true
      }
      console.log(contentMsg)
      this.$confirm(contentMsg, '操作提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '已确认接受标期，直接创建订单',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.prechecks['A'] = true
        this.submitOrderData(false)
      }).catch(console.log)
      return false
    },
    precheckB () {
      // 整单交货 当客户期望日期有值，且存在小于某一行的系统标准发货日期
      let contentMsg = ''
      this.skuList.forEach((sku, index) => {
        if (sku.customerDate && new Date(sku.customerDate) < new Date(sku.deliveryDate)) {
          contentMsg += `第${index + 1}行的${sku.skuNo}sku的客户期望日期${sku.customerDate}小于系统标准日期${sku.deliveryDate}，请与客户确认是否接收标期<br />`
        }
      })
      if (this.prechecks['A']) {
        return true
      }
      this.$confirm(contentMsg, '操作提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '已确认，直接创建订单',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.prechecks['A'] = true
        this.submitOrderData(false)
      }).catch(console.log)
      return false
    },
    validateDeliveryDateRules () {
      // const { autoBatching } = this.orderData
      let pass = true
      pass = pass && this.precheckA()
      this.prechecks['A'] = false
      return pass
    },
    isOrderDataValid () {
      if (this.orderData) {
        if (!isForecastOrder(this.orderType) &&
          !this.orderData.receivingInvoiceContact &&
          this.orderType !== 'Z014'
        ) {
          this.$message.error({
            message: '收票联系人不能为空！'
          })
          return false
        }
      }
      if (!this.skuList || this.skuList.length === 0) {
        this.$message.error({
          message: '商品信息不能为空！'
        })
        return false
      }
      if (this.skuList) {
        let quantityError = false
        let positionError = false
        let factoryError = false
        // let deliveryDateError = false
        let customerDateError = false
        let taxedPriceError = false
        let freeTaxPriceError = false
        let costCenterError = false
        let generalLedgerError = false
        let customerMaterialNameError = false
        const errList = []
        this.skuList.forEach(sku => {
          if (sku.skuNo && !quantityError && sku.quantity === 0) {
            errList.push('商品信息商品数量不能为空！');
            quantityError = true;
          }
          if (
            !sku.skuNo &&
            !quantityError &&
            (!sku.quantity || Number.parseFloat(sku.quantity) === 0) &&
            (!sku.customerMaterialQuantity ||
              Number.parseFloat(sku.customerMaterialQuantity) === 0)
          ) {
            errList.push('商品信息商品数量和客户物料数量不能同时为空！');
            quantityError = true;
          }
          if (!positionError && !sku.position && sku.skuNo) {
            errList.push('商品信息库位信息不能为空！')
            positionError = true
          }
          if (!factoryError && !sku.factory && sku.skuNo) {
            errList.push('商品信息工厂信息不能为空！')
            factoryError = true
          }
          if (!customerMaterialNameError && !sku.customerMaterialName && !sku.skuNo) {
            errList.push('商品客户物料名称信息不能为空！')
            customerMaterialNameError = true
          }
          let costCenterDesc = this.costCenterOptions.find(item => item.costCenter === sku.costCenter)?.description
          console.log('costCenterDesc', !costCenterDesc, 'costCenterDesc', costCenterDesc)
          if (this.isZ007 && (!costCenterError && (!sku.costCenter || !costCenterDesc) && isInnerOrderReason(this.orderData.orderReason))) {
            errList.push('成本中心/描述不能为空！')
            costCenterError = true
          }
          if (this.isZ007 && !generalLedgerError && !sku.generalLedgerAccount && isInnerOrderReason(this.orderData.orderReason)) {
            errList.push('总账科目不能为空！')
            generalLedgerError = true
          }
          // if (!['1000', '1001', '1300'].includes(this.orderData?.selectedSalesRange?.salesOrganization)) {
          //   if (!deliveryDateError && !sku.deliveryDate && !['1000', '1300'].includes(sku.factory)) {
          //     errList.push('商品信息交期不能为空！')
          //     deliveryDateError = true
          //   }
          // }
          if (!isServiceOrder(this.orderType) && !isForecastOrder(this.orderType) && !isFreeOrder(this.orderType)) {
            if (!taxedPriceError && !sku.taxPrice && this.isTaxedCustomer) {
                errList.push('商品信息含税价格不能为空！')
                taxedPriceError = true
            }
            if (!freeTaxPriceError && !sku.freeTaxPrice && !this.isTaxedCustomer) {
              errList.push('商品信息未税价格不能为空！')
              freeTaxPriceError = true
            }
          }
          if (isForecastOrder(this.orderType)) {
            if (!customerDateError && !sku.customerDate) {
              errList.push('商品信息客户期望送达日期不能为空！')
              customerDateError = true
            }
          }
        })
        const skuError = !quantityError && !positionError &&
          !factoryError && !customerDateError && !taxedPriceError && !freeTaxPriceError && !costCenterError && !generalLedgerError && !customerMaterialNameError
        if (!skuError) {
          this.$message.error({
            dangerouslyUseHTMLString: true,
            message: errList.join('<br>')
          })
          return skuError
        }
      }
      console.log(this.orderData.bidCustomer)
      if (this.isBidCustomer) {
        // 客户叫料商品行 最近发货日期，发货频次，发货周期必填
        if (this.skuList && this.skuList.length) {
          if (this.skuList.some(sku => !sku.recentDeliveryDate || !sku.deliveryFrequency || !sku.deliveryCycle)) {
            this.$message.error('未发货条件为 客户叫料/特殊客户叫料，商品行 最近发货日期、发货频次，发货周期必填！')
            return false
          }
        }
      }
      // if (!this.validateDeliveryDateRules()) return false
      return true
    },
    formatPriceMsg (msg) {
      msg += '';
      return msg.replace(/\]|\[|\s+/gmi, '').replace(/,/gmi, '\n');
    },
    async createWorkOrder (data, response) {
      const { orderType } = data
      if (orderType !== 'Z001') return true
      const { bizCode, msg, data: resData } = response
      console.log(data, bizCode, msg, resData);
      if (bizCode === 500001) {
        let confirmMsg = '';
        if (Array.isArray(resData)) {
          confirmMsg = this.formatPriceMsg(resData.join('\n'));
          if (confirmMsg) {
            this.$confirm(`<pre style="max-height:400px;overflow:auto;">${confirmMsg}</pre>`, '操作提示', {
              type: 'warning',
              dangerouslyUseHTMLString: true,
              confirmButtonText: '一键提交价格审批并保存草稿',
              cancelButtonText: '确认'
            })
              .then(action => {
                console.log(action);
                // const loading = this.$loading({
                //   background: 'rgba(0, 0, 0, 0.8)',
                //   lock: true
                // })
                this.submitOrderDraft(true)
                // createWorkOrderApi(data)
                //   .then(res => {
                //     if (res.code === 200) {
                //       this.$message.success(res.msg || '提交成功！');
                //     } else {
                //       this.$message.error(res.msg || '操作失败！');
                //     }
                //   })
                //   .finally(() => {
                //     loading.close();
                //   })
              })
              .catch(action => {
                console.log(action)
              })
          }
        }
      } else {
        this.$confirm(`<div style="max-height:400px;overflow:auto;">${msg}</div>`, '操作提示', {
          type: 'error',
          dangerouslyUseHTMLString: true,
          confirmButtonText: '确认'
        })
          .then(console.log)
          .catch(console.log)
      }
    },
    trimPropertyList (item) {
      if (item && Array.isArray(item.customPropertyList)) {
        item.customPropertyList.forEach(property => {
          if (property.placeholder !== undefined) {
            delete property.placeholder
          }
        })
      }
    },
    async submitOrderData (isDraft, createWorkList) {
      if (!isDraft) {
        const isValid = this.isOrderDataValid()
        if (!isValid) { return }
      }
      const {
        selectedSalesRange: { distributionChannel, productGroup, salesOrganization },
        customer: { customerNumber },
        orderNote,
        orderNo,
        customerReferenceDate,
        moreDeliveryData: rawMoreDeliveryData,
        moreInvoiceData,
        paymentTerm,
        serviceCenterSelfTransport,
        customerServiceId,
        sellerId,
        customerServiceName,
        sellerName
      } = deepClone(this.orderData)
      const orderServiceFieldsList = orderServiceFields.map(obj => obj.field)
      const moreDeliveryData = foldFields(rawMoreDeliveryData, orderServiceFieldsList.concat('packagingReq'))
      console.log(moreDeliveryData);
      const receiverContactId = get(this.orderData, 'receiverContact.contactId')
      const invoiceContactId = get(this.orderData, 'receivingInvoiceContact.contactId')
      const orderContactId = get(this.orderData, 'orderContact.contactId')
      const {
        customerServiceEmail,
        sellerEmail,
        isTax,
        paymentTermCode
      } = deepClone(this.cusDetail)
      const items = []
      const validateUnitArr = []
      this.skuList.forEach((item, idx) => {
        const {
          unitCode,
          skuNo,
          materialDescribe,
          taxPrice,
          freeTaxPrice,
          currency,
          position,
          customerMaterialUnit,
          costCenter,
          factory: { code: factoryCode }
        } = item
        if (customerMaterialUnit && customerMaterialUnit.length > 10) {
          validateUnitArr.push({
            idx,
            skuNo
          })
        }
        const newItem = {
          ...deepClone(item),
          costCenterDesc: this.costCenterOptions.find(item => item.costCenter === costCenter)?.description,
          orderItemNo: (idx + 1) * 10,
          materiel: skuNo,
          sapMaterialName: materialDescribe,
          taxPrice: taxPrice == null ? 0 : taxPrice,
          freeTaxPrice: freeTaxPrice == null ? 0 : freeTaxPrice,
          conditionalUnit: unitCode, // todo
          quantityUnit: unitCode,
          conditionalPricingUnit: 1,
          currency,
          factory: factoryCode,
          position
        }
        this.trimPropertyList(newItem)
        if (this.orderData.autoBatching === 'Z' && this.orderData.customerReferenceDate) {
          newItem.customerDate = this.orderData.customerReferenceDate
        }
        if (this.orderData.autoBatching === 'Z' && this.orderData.entireOrderRefuseSDD === 'X') {
          newItem.refuseSystemDeliveryDate = 'X'
        }
        newItem.waveDeliveryDate = newItem.deliveryDate || ''
        delete newItem.positionList
        if (this.isBidCustomer) {
          newItem.customerDate = newItem.recentDeliveryDate
        }
        items.push(newItem)
      })

      if (validateUnitArr && validateUnitArr.length > 0) {
        const msg = validateUnitArr.map(({ idx, skuNo }) => `行${idx + 1}-sku为${skuNo}`).join(',')
        this.$alert(`【${msg}】的客户物料单位大于10位`, '错误', {
          type: 'error'
        })
        return
      }
      const orderData = {
        ...this.orderData,
        items,
        isTax,
        customerReferenceNo: orderNo ? orderNo.trim().replace(/(?:\r\n|\r|\n)/g, '') : '',
        orderNo: this.bossID,
        customerServiceId,
        customerServiceName,
        customerServiceEmail,
        sellerId,
        sellerName,
        sellerEmail,
        orderNote,
        distributionChannel,
        productGroup,
        salesOrganization,
        customerReferenceDate: customerReferenceDate,
        receivingInvoiceContact: invoiceContactId,
        orderSource: this.orderData.orderSource || 'BOSS',
        orderType: this.orderType,
        customerNo: customerNumber,
        paymentTerm: paymentTerm || paymentTermCode,
        serviceCenterSelfTransport,
        receiverContact: receiverContactId,
        orderContact: orderContactId,
        requestId: shortid.generate(),
        collectionAmount: this.orderData.moreDeliveryData && this.orderData.moreDeliveryData.collectionAmount,
        projectNo: this.orderData.moreDeliveryData && this.orderData.moreDeliveryData.projectNo
      }
      if (isDraft) {
        orderData.sketchOnly = 'X'
      }
      // 处理交货信息字段
      const deliveryCheckboxAttrs = [
        'referenceStandardShippingReq', 'autoDelivery',
        'paid', 'attachOrder', 'specifiedDocument', 'exportProcessingZone',
        'virtualReturn', 'attachCoa', 'attachMsds', 'backupOrder',
        'attachTds', 'hideLogo', 'clearSlackStock'
      ]
      const deliveryOtherAttr = [
        'disableShipping', 'deliveryOtherNote', 'supplierAccount', 'clearSlackStock',
        'packagingReq', 'shippingCondition', 'signingBack', 'deliverySlipTemplate',
        'deliveryUnloadingReq', 'labelTemplate', 'certificateIdentification', 'printNum', 'scheduleDelivery',
        ...orderServiceFieldsList
      ]
      console.log(deliveryOtherAttr);
      const deliverySelectAttr = [
        'agreementNote', 'paymentNote'
      ]
      if (Object.keys(moreDeliveryData).length) {
        deliveryCheckboxAttrs.forEach(attr => {
          orderData[attr] = moreDeliveryData[attr] ? 'X' : 'Z'
        })
        deliveryOtherAttr.forEach(attr => {
          if (moreDeliveryData[attr] != null) {
            orderData[attr] = moreDeliveryData[attr]
          }
        })
        deliverySelectAttr.forEach(attr => {
          if (moreDeliveryData[attr] != null) {
            orderData[attr] = moreDeliveryData[attr]
          }
        })
        if (Array.isArray(moreDeliveryData.deliveryRequirements) && moreDeliveryData.deliveryRequirements.length > 0) {
          orderData['deliveryRequirements'] = moreDeliveryData.deliveryRequirements.join(',')
        }
      } else {
        // 给合格证设置默认值
        orderData['certificateIdentification'] = '0'
        deliveryCheckboxAttrs.forEach(attr => {
          orderData[attr] = this.orderData[attr]
        })
        deliveryOtherAttr.forEach(attr => {
          if (this.orderData[attr] != null) {
            orderData[attr] = this.orderData[attr]
          }
        })
        deliverySelectAttr.forEach(attr => {
          if (this.orderData[attr] != null) {
            orderData[attr] = this.orderData[attr]
          }
        })

        if (Array.isArray(orderData.packagingReq) && orderData.packagingReq.length > 0) {
          orderData['packagingReq'] = orderData.packagingReq.join(',')
        }
      }

      // 如果两者都有值，则清空禁用货运
      if (orderData.disableShipping && orderData.disableShipping) {
        orderData.disableShipping = '*'
      }
      // 创建时若为空，转成*
      orderData.disableShipping = orderData.disableShipping || '*'
      orderData.designatedShipping = orderData.designatedShipping || '*'

      // 处理发票信息
      const invoiceCheckboxAttrs = ['invoicingByMail', 'autoBilling', 'returnOffset',
        'mergeBilling', 'billingRobot', 'showDiscount', 'ifDocMailed']
      const invoiceOtherAttr = ['shippingInfo', 'financialNote',
        'customerPayAccountTypeName', 'invoiceType', 'mergeBillingDemand', 'expressCompany']
      if (moreInvoiceData) {
        invoiceCheckboxAttrs.forEach(attr => {
          orderData[attr] = moreInvoiceData[attr] ? 'X' : 'Z'
        })
        invoiceOtherAttr.forEach(attr => {
          if (moreInvoiceData[attr] != null) {
            orderData[attr] = moreInvoiceData[attr]
          }
        })
      } else {
        invoiceCheckboxAttrs.forEach(attr => {
          orderData[attr] = this.orderData[attr]
        })
        invoiceOtherAttr.forEach(attr => {
          if (this.orderData[attr] != null) {
            orderData[attr] = this.orderData[attr]
          }
        })
      }
      const { omsNo, orderSource } = this.$route.query || {};
      if (orderSource === 'SH' && omsNo) {
        orderData.orderNo = omsNo
      }
      orderData.specifiedReceiptDayOfWeek = this.orderData.specifiedReceiptDayOfWeek?.join(',')
      orderData.receiptTimeCategory = this.orderData.receiptTimeCategory ? 'X' : 'Z'

      let hasConflictFields = false;
      let conflictMessage = '交货信息中存在冲突字段，系统已自动修正：<br />';
      if (moreDeliveryData.hideLogo && (moreDeliveryData.dnSignatureReq?.indexOf('01') > -1 || moreDeliveryData.dnSignatureReq?.indexOf('05') > -1)) {
        conflictMessage += '因隐藏logo=是，故修正送货单签章要求不等于盖红章或每页盖章，紧固件特殊包装要求等于无要求；<br />';
        hasConflictFields = true;
      }
      const shipping = moreDeliveryData.disableShipping
      if (shipping) {
        if (shipping.length > 300 || shipping.split(',').length > 7) {
          this.$message.error('禁用货运字段最多选择7项且字符串长度不能大于300！')
          return;
        }
      }
      if (orderData.receiptTimeCategory === 'Z' && (orderData.specifiedReceiptDayOfWeek?.indexOf('06') > -1 || orderData.specifiedReceiptDayOfWeek?.indexOf('07') > -1)) {
        conflictMessage += '因工作日和周末均可收=否，故取消指定收货日期=周六/周日选项；';
        hasConflictFields = true;
      }

      // if (!orderData.items.find(item => item.fastenerLogo) && (moreDeliveryData.fastenerLabelReq !== '0' || moreDeliveryData.fastenerDetect !== '0' || moreDeliveryData.fastenerSpecialPackageReq !== '0')) {
      //   conflictMessage += '因不含oem紧固件商品行，故紧固件标签要求/紧固件检测/紧固件特殊包装要求=无要求；';
      //   hasConflictFields = true;
      // }

      if (hasConflictFields) {
        await this.$confirm(conflictMessage, {
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          showClose: false,
          closeOnClickModal: false
        });
      }

      if (isDraft) {
        orderData.items.forEach(item => {
          delete item.simPositionList
        })
        orderData.sketchOrderScene = 'manualCreateSketch'
        return this.saveDraft(orderData, createWorkList);
      }
      const orderUrl = getOrderOptUrl(this.dictList, this.orderType, 'create')
      if (orderUrl) {
        orderData.items.forEach(item => {
          delete item.simPositionList
        })
        console.log(orderData);
        // this.createOrderApi(isDraft, orderUrl, orderData)
        this.createOrderApi2(isDraft, orderUrl, orderData)
      }
    },
    saveDraft (data, createWorkList, afterCreateValidateError) {
      console.log(data);
      const loading = this.$loading({ background: 'rgba(0, 0, 0, 0.8)', lock: true })
      saveDraftApi(data, createWorkList, afterCreateValidateError)
        .then(res => {
          console.log(res)
          if (res.code === 200) {
            let msgList;
            if (Array.isArray(res.data.msgList)) {
              msgList = res.data.msgList.join(';');
            }
            const str = `<div style="max-height:400px;max-width:350px;overflow:auto;white-space: break-spaces;">${msgList}</div>`
            this.$alert(str, '操作提示', {
              confirmButtonText: '确定',
              dangerouslyUseHTMLString: true,
              callback: action => {
                window.location.replace(`/sr/draft/list?voucherNoList=${res.data.sketchOrderNo}`)
              }
            })
          } else {
            this.$alert(res.message || res.msg || '操作失败！', '错误', { type: 'error' })
          }
        })
        .catch(error => {
          console.log(error)
          this.$alert(error.message || error.msg || '操作失败！', '错误', { type: 'error' })
        })
        .finally(() => {
          loading.close()
        })
    },
    createOrderApi2 (isDraft, orderUrl, orderData, params) {
      const loading = this.$loading({ background: 'rgba(0, 0, 0, 0.8)', lock: true })
      createOrder(orderUrl, orderData, params)
        .then(res => {
          const { errMsgList, remindList, headerResultList, itemResultList } = res.data || {};
          const saveDraftData = deepClone(orderData);
          saveDraftData.validatorResultAllDTO = {
            headerResultList,
            itemResultList
          }
          // 触发协议价审批的条件
          const negotiatedDiscountValidator =
            headerResultList?.length === 0 &&
            itemResultList?.every(
              (item) =>
                item?.itemValidatorResultList?.length === 1 &&
                [
                  'negotiatedDiscountValidator',
                  'negotiatedDiscountValidatorV2'
                ].includes(item.itemValidatorResultList[0].validateCode)
            );
          if (res.code === 200) {
            const str = `订单创建成功！对应的外围订单号为：${orderData.orderNo}`
              const msg = remindList && remindList.length > 0
                  ? str + ';' + `${remindList.join(' ')}`
                  : str
              this.$alert(msg, '订单创建成功', {
                confirmButtonText: '确定',
                callback: action => {
                  try {
                    this.$closeTag(this.$route.path)
                    this.$closeTag('/orderSale/periphery')
                  } catch (err) {
                    console.log(err)
                  }
                  this.$router.push({
                    path: '/orderSale/periphery'
                  })
                }
              })
          } else if (res.code === 500 && negotiatedDiscountValidator) {
            this.$confirm(`<div style="max-height:400px;overflow:auto;white-space: break-spaces;">${errMsgList?.join('<br/>')}</div>`, '操作提示', {
              type: 'warning',
              dangerouslyUseHTMLString: true,
              confirmButtonText: '一键提交价格审批并保存草稿',
              cancelButtonText: '确认'
            })
            .then(() => {
              saveDraftData.sketchOrderScene = 'manualCreateOrderFailToSketch'
              this.saveDraft(saveDraftData, true, false);
            })
            .catch(_err => {
              saveDraftData.actionSource = '1'
              transformSoFailDataPoint(saveDraftData)
            })
          } else {
            if (errMsgList && Array.isArray(errMsgList)) {
              let message = ''
              message += errMsgList.join('<br/>')
              this.$confirm(`<div style="max-height:400px;overflow:auto;white-space: break-spaces;">${message}</div>`, '错误提示', {
                dangerouslyUseHTMLString: true,
                confirmButtonText: '尝试创建订单并保存草稿',
                cancelButtonText: '取消',
                type: 'error'
              })
              .then(() => {
                saveDraftData.sketchOrderScene = 'manualCreateOrderFailToSketch'
                this.saveDraft(saveDraftData, false, true)
              })
              .catch(_err => {
                saveDraftData.actionSource = '1'
                transformSoFailDataPoint(saveDraftData)
              })
            } else {
              this.$alert(res.msg, '错误', {
                type: 'error',
                customClass: 'custom-return-error',
                dangerouslyUseHTMLString: true
              })
            }
          }
        })
        .catch(err => console.log(err))
        .finally(() => loading.close())
    },
    createOrderApi (isDraft, orderUrl, orderData, params) {
      const loading = this.$loading({ background: 'rgba(0, 0, 0, 0.8)', lock: true })
      createOrder(orderUrl, orderData, params)
        .then(async (res) => {
          loading.close()
          if (res) {
            const { code, data, bizCode } = res
            console.log(code, data, bizCode);
            if (code === 200 || code === 201) {
              const str = `订单创建成功！对应的外围订单号为：${orderData.orderNo}`
              const msg = {
                '200': str,
                '201': data && data.length > 0
                  ? str + ';' + `${data.join(' ')}`
                  : str
              }
              this.$alert(msg[code], '订单创建成功', {
                confirmButtonText: '确定',
                callback: action => {
                  try {
                    this.$closeTag(this.$route.path)
                    this.$closeTag('/orderSale/periphery')
                  } catch (err) {
                    console.log(err)
                  }
                  this.$router.push({
                    path: isDraft ? '/orderSale/draft' : '/orderSale/periphery'
                  })
                }
              })
            } else if (code !== 200 && bizCode) {
              // 负毛利卡控
              this.createWorkOrder(orderData, res)
            } else {
              let message = ''
              if (res.data) {
                if (Array.isArray(res.data)) {
                  const arr = res.data.map(item => {
                    if (typeof (item) === 'string') {
                      return item.split(';').filter(s => s)
                    }
                    return item
                  })
                  message = flatten(arr).join(';<br>')
                } else {
                  message = res.data
                }
              } else if (res.msg) {
                message = res.msg
              }
              this.$alert(message, '错误', {
                type: 'error',
                customClass: 'custom-return-error',
                dangerouslyUseHTMLString: true
              })
            }
          }
        })
        .catch(error => {
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
          loading.close()
        })
    },
    submitOrder (formName) {
      this.$refs[formName].validate((valid) => {
        console.log(valid, this.orderData, this.skuList)
        if (valid) {
          const { orderReason } = this.orderData
          if (this.orderType === 'Z001' && this.totalDiscount) {
            this.$message.error('整单折扣金额待分配')
            return false
          }
          if (!orderReason && (this.orderType !== 'Z001' && this.orderType !== 'Z002')) {
            this.$confirm('还未选择订单原因，是否继续提交?', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.submitOrderFn()
              // this.submitOrderData(false)
            }).catch(() => {
              return false
            })
          } else {
            this.submitOrderFn()
            // this.submitOrderData(false)
          }
        } else {
          return false
        }
      })
    },
    submitOrderFn() {
      let list = this.orderData.acceptSupplierDelivery === 1 ? [] : this.skuList.filter((a) => a.directDeliverySupplier === '1').map((a) => `行号${a.idx}`)
      if (list.length) {
        this.$confirm(`存在直发冲突冻结行：${list.join('，')}，是否修改客户接受供应商直发=是`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.orderData.acceptSupplierDelivery = 1;
          this.submitOrderData(false)
        }).catch(() => {
          // this.submitOrderData(false)
        })
      } else {
        this.submitOrderData(false)
      }
    },
    validateDraft () {
      let ret = true
      const { customer, selectedSalesRange } = this.orderData;
      if (!customer) {
        this.$message.error('请选择客户！');
        ret = false;
        return ret;
      }
      if (!selectedSalesRange) {
        this.$message.error('请选择销售范围！');
        ret = false;
        return ret;
      }
      // if (!this.skuList || this.skuList.length === 0) {
      //   this.$message.error({
      //     message: '商品信息不能为空！'
      //   })
      //   ret = false;
      //   return ret;
      // }
      return ret
    },
    submitOrderDraft (createWorkList) {
      if (!this.validateDraft()) return
      this.submitOrderData(true, createWorkList)
    },
    handleCustomerDateSensitiveChange () {
      this.$store.dispatch('orderGoods/customerDateSensitiveTotal', {
        key: this.getKey(),
        data: this.orderData.customerDateSensitive
      })
      this.customerDateSensitiveIndeterminate = false
    },
    handleUrgentChange () {
      this.$store.dispatch('orderGoods/urgentTotal', {
        key: this.getKey(),
        data: this.orderData.urgent
      })
      this.urgentIndeterminate = false
    },
    handleItemUrgentChange (data) {
      if (this.skuList.length > 0) {
        const urgent = this.skuList[0].urgent
        const isSame = this.skuList.every(item => item.urgent === urgent)
        if (isSame) {
          this.orderData.urgent = urgent
          this.urgentIndeterminate = false
        } else {
          this.urgentIndeterminate = true
        }
      }
    },
    handleItemCustomerDateSensitiveChange (data) {
      if (this.skuList.length > 0) {
        const customerDateSensitive = this.skuList[0].customerDateSensitive
        const isSame = this.skuList.every(item => item.customerDateSensitive === customerDateSensitive)
        if (isSame) {
          this.orderData.customerDateSensitive = customerDateSensitive
          this.customerDateSensitiveIndeterminate = false
        } else {
          this.customerDateSensitiveIndeterminate = true
        }
      }
    },
    async handleCurrencyChange (currency) {
      const findOne = this.dictList['currencySymbol'].find(item => item.parentCode === currency)
      let rate = 1
      if (findOne && findOne.parentCode !== 'CNY') {
        const loading = this.$loading({
          background: 'rgba(0, 0, 0, 0.8)',
          lock: true
        })
        const res = await exchange({
          sourceCurrency: findOne.parentCode,
          targetCurrency: 'CNY'
        })
        loading.close()
        if (res && res.code === 200 && res.data) {
          rate = res.data.rate
        } else {
          this.$message.error(res.msg);
          return
        }
      }
      this.orderData.exchangeRate = rate
      this.orderData.currencySymbol = findOne.code
      this.$store.dispatch('orderGoods/exchangeCurrency', {
        orderData: this.orderData,
        key: this.getKey(),
        exchangeRate: this.orderData.exchangeRate,
        currency
      })
    },
    setRowCustomerDate () {
      // 特殊情况整单设置项目行日期, 第一行没有日期才走这个逻辑
      const { customerReferenceDate } = this.orderData
      if (this.skuList && this.skuList.length) {
        console.log('特殊情况整单设置项目行日期')
        this.handleRefDate(customerReferenceDate || '')
      }
    },
    updateCustomer (data) {
      const { acceptSupplierDelivery, autoDelivery, autoBatching, bidCustomer } = data
      if (autoDelivery != null) {
        this.$set(this.cusDetail, 'autoDelivery', autoDelivery)
        this.$set(this.orderData, 'autoDelivery', autoDelivery)
      }
      if (autoBatching != null) {
        this.$set(this.cusDetail, 'autoBatching', autoBatching)
        this.$set(this.orderData, 'autoBatching', autoBatching)
      }
      if (autoBatching !== 'X') {
        this.setRowCustomerDate()
      }
      this.$set(this.orderData, 'bidCustomer', bidCustomer)
      this.$set(this.orderData, 'acceptSupplierDelivery', acceptSupplierDelivery)
    }
  }
}
</script>

<style lang="scss">
.el-message-box__message {
  p {
    text-align: left;
  }
}
.customer-select {
  .el-form-item__content {
    display: flex;
  }
}
.custom-return-error{
  white-space: break-spaces;
}
</style>

<style scoped lang="scss">
.currency-row {
  display: flex;
  align-items: center;
  .currency-flag {
    height: 32px;
    width: 40px;
    margin-left: 20px;
  }
}
.strong {
  color: #ff4949;
  font-size: 12px;
}
.current-option {
  display: flex;
  align-items: center;
  .flag {
    display: inline-block;
    height: 40px;
    width: 40px;
    margin-right: 5px;
  }
}
.viewCustomer {
  margin-left: 10px;
  width: 110px;
}
.customer-info {
  color: #ff4949;
}
.selectClientItem,
.selectSkuItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
    overflow: auto;
  }
}

.CreateOrderTabelCellError {
  input {
    border: 1px solid #ff4949;
  }
  &:hover {
    input {
      border: 1px solid #ff4949;
    }
  }
}

.ba-row-start {
  display: flex;
}

.ba-row-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
