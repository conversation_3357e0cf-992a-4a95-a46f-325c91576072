<template>
  <div class="app-container"  v-loading="pageLoading">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="订单号检索" prop="orderNo">
              <el-input
                v-model="searchForm.orderNo"
                placeholder="请输入外围订单号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户检索" prop="customerNo">
              <el-select
                ref="customer"
                v-model="searchForm.customerNo"
                filterable
                clearable
                remote
                reserve-keyword
                placeholder="请输入客户编号/名称"
                style="width:100%"
                :remote-method="queryCustomerList"
                :loading="loadingCustomer"
              >
                <el-option
                  v-for="(item, index) in customerList"
                  :key="'zkunnr'+item.customerId"
                  :label="item.customerName"
                  :value="item.customerNumber"
                  :disabled="index===0"
                >
                  <div
                    class="ba-row-start selectClientItem"
                    :style="{fontWeight:index===0?'bold':'normal'}"
                  >
                    <div>{{ item.customerNumber }}</div>
                    <div>{{ item.cityName }}</div>
                    <div>{{ item.customerName }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单创建日期" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                align="right"
                unlink-panels
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                size="small"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="状态" prop="orderStatus">
              <el-select
                    clearable
                    v-model="searchForm.orderStatus"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in orderStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="协议价审批状态" prop="salePriceApprovalStatus">
              <el-select
                clearable
                v-model="searchForm.salePriceApprovalStatus"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in salePriceApprovalStatus"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button
              class="filterBtn"
              type="primary"
              icon="el-icon-search"
              @click="handleFilter"
              >查询
            </el-button>
            <el-button
              class="filterBtn"
              @click="handleReset"
              >重置
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row type="flex" justify="space-between" align="middle" class="orderListTop">
      <span>列表内订单总数：{{ total }}</span>
      <el-button type="primary" @click="handleOpenCreateDlg">创建订单</el-button>
    </el-row>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      style="width:100%"
      height="500"
    >
      <el-table-column
        label="外围订单号"
        width="180"
        align="center"
        prop="orderNo"
      >
          <template slot-scope="{row}">
              <el-link type="primary" :href="`/orderSale/performanceProcessTool?orderNo=${row.orderNo}`" target="_blank">{{ row.orderNo }}</el-link>
          </template>
      </el-table-column>
      <el-table-column
        label="客户名称"
        width="300"
        align="center"
        prop="customerName"
      >
      </el-table-column>
      <el-table-column
        label="订单来源"
        width="100"
        align="center"
        prop="orderSource"
      >
      </el-table-column>
      <el-table-column
        label="协议价审批状态"
        width="120"
        align="center"
        prop="salePriceApprovalStatusDesc"
      >
        <template slot-scope="{row}">
          <el-popover
            v-if="row.salePriceApprovalErrorMessage"
            :content="row.salePriceApprovalErrorMessage"
            placement="top"
            title=""
            width="200"
            trigger="hover"
          >
            <span slot="reference">
              <span style="color:red">{{row.salePriceApprovalStatusDesc}}</span>
            </span>
          </el-popover>
          <span v-else>
            {{row.salePriceApprovalStatusDesc}}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="审批单号"
        width="240"
        align="center"
        prop="unifyApprovalNo"
      >
        <template slot-scope="{row}">
          <span class="link-to-detail" @click="jumpToAuditDetail(row)">
            {{row.unifyApprovalNo}}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="销售"
        width="110"
        align="center"
        prop="sellerName"
      >
      </el-table-column>
      <el-table-column
        label="客服"
        width="110"
        align="center"
        prop="customerServiceName"
      >
      </el-table-column>
      <el-table-column
        label="创建时间"
        width="150"
        align="center"
        prop="gmtCreate"
      >
      </el-table-column>
      <el-table-column
        label="状态"
        width="100"
        align="center"
        prop="ofcStatusDesc"
      >
      </el-table-column>
      <el-table-column
        label="备注"
        min-width="340"
        align="center"
        prop="statusMessage"
      >
        <template slot-scope="{row}">
          <div class="remark" :title="row.statusMessage">
            {{ row.statusMessage }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="销售订单明细"
        width="210"
        align="center"
        prop="operation"
      >
        <template slot-scope="{row}">
          <div v-for="item in row.soNos.split(',')" :key="item">
            <el-button type="text" size="mini" @click="toDtl(item,row)">{{getNo(item)}}</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        fixed="right"
        label="操作"
        width="120"
        align="center"
        prop="operation"
      >
        <template slot-scope="{row}">
          <el-button size="mini" type="text" v-if="row.status==='sketch'" @click="editDraft(row)">编辑</el-button>
          <el-button size="mini" type="text" v-if="row.ofcStatusDesc==='失败'" @click="repush(row)">重推</el-button>
          <el-button size="mini" type="text" v-if="row.status==='sketch'" @click="deleteDraft(row)">删除</el-button>
        </template>
      </el-table-column>

    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
    <!-- 创建订单的选择公司类型弹框 -->
    <CreateOrderDialog
      :show-dialog.sync="createOrder"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { omsOrderList, checkPermission, searchClients, repushFailuredSplitOrder, deleteDraft } from '@/api/orderSale'
import CreateOrderDialog from './components/list/CreateOrderDlg'
import moment from 'moment'

const defaultSearchParams = {
  orderNo: '',
  customerNo: '',
  orderStatus: '',
  salePriceApprovalStatus: '',
  dateRange: ''
}
export default {
  name: 'PeripheryOrderList',
  components: {
    Pagination,
    CreateOrderDialog
  },
  data () {
    return {
      componentsName: 'peripheryOrderList',
      listLoading: false,
      loadingCustomer: false,
      searchForm: {
        ...defaultSearchParams
      },
      customerList: [],
      list: [],
      total: 0,
      listQuery: {
        page: 1,
        size: 20
      },
      // 模态框
      createOrder: false,
      orderStatus: [
        { value: 1, label: '成功' },
        { value: 2, label: '处理中' },
        { value: 3, label: '失败' },
        { value: 4, label: '草稿' }
      ],
      // 审批中”: 0 “审批通过” 1 “审批驳回” 2 “提交审批失败” 3"

      salePriceApprovalStatus: [
        { value: 0, label: '审批中' },
        { value: 1, label: '审批通过' },
        { value: 2, label: '审批驳回' },
        { value: 3, label: '提交审批失败' }
      ],
      pageLoading: false
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  created () {
    this.componentsName = this.$route.name
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    this.initDate()
    this.getList()
  },
  methods: {
    jumpToAuditDetail(row) {
      // window.location.href = `/pa/priceAudit/audit/${row.unifyApprovalNo}`
      window.open(`/priceAudit/audit/${row.unifyApprovalNo}`)
    },
    mapCodeToMsg(code, options) {
      const option = options.find(item => item.value === code);
      return (option && option.label) || code;
    },
    initDate () {
      const startDate = moment().subtract(7, 'days').format('yyyy-MM-DD')
      const endDate = moment().format('yyyy-MM-DD')
      this.searchForm.dateRange = [startDate, endDate]
    },
    editDraft (row) {
      let isPro = false;
      try {
        isPro = window.CUR_DATA.env === 'pro'
      } catch (err) {}
      const url = `/so/draft/${row.orderNo}`
      console.log(isPro, url);
      if (/localhost|local/.test(location.href)) {
        window.open(`http://local.zkh360.com:9004${url}`)
        return
      }
      window.open(url)
    },
    deleteDraft (row) {
      const params = {
        orderNo: row.orderNo
      }
      this.$confirm('是否需要将该草稿订单记录删除？删除后将无法找回', '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageLoading = true
        deleteDraft(params).then((res) => {
          console.log(res)
          if (res.code === 200) {
            this.getList()
            this.$message.success(`删除${res.msg}`)
          } else {
            this.$message.error(`删除${res.msg}`)
          }
        }).finally(() => {
          this.pageLoading = false
        })
      }).catch(() => {});
    },
    repush(row) {
      const params = {
        bizKeys: `${row.orderSource}/${row.orderNo}`,
        bizLine: 'oms.so.create'
      }
      this.$confirm('确认是否要重推该订单进行创建？?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.pageLoading = true
        repushFailuredSplitOrder(params).then((res) => {
          this.pageLoading = false
          console.log(res)
          if (res.code === 200) {
            this.pageLoading =
            this.getList()
            this.$message.success(`重推${res.msg}`)
          } else {
            this.$message.error(res.data || `重推${res.msg}`)
          }
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消重推'
        });
      });
    },
    getNo (nos) {
      const items = nos.split('_')
      return items.length > 0 ? items[0] : nos
    },
    queryCustomerList (str) {
      this.loadingCustomer = true
      searchClients(str).then(res => {
        this.loadingCustomer = false
        if (res && res.code === 200) {
          this.customerList = [
            {
              customerNumber: '客户编码',
              customerName: '客户名称',
              cityName: '城市'
            },
            ...res.data
          ]
        }
      })
    },
    clean (obj) {
      for (var propName in obj) {
        if (obj[propName] === '' || obj[propName] === null || obj[propName] === undefined) {
          delete obj[propName]
        }
      }
    },
    getList () {
      this.listLoading = true
      const params = {
        ...this.searchForm,
        current: this.listQuery.page,
        size: this.listQuery.size
      }
      if (this.searchForm.dateRange) {
        const startDate = this.searchForm.dateRange[0]
        const endDate = this.searchForm.dateRange[1]
        if (startDate && endDate) {
          params.createStart = startDate
          params.createEnd = endDate
        }
        delete params.dateRange
      }
      this.clean(params)
      omsOrderList(params).then(response => {
        if (response.code === 200 && response.data) {
          this.list = response.data.records
          this.total = response.data.total || 0
        } else {
          this.$notify.error(response.msg)
          // 报错时清空数据
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(e => {
        this.listLoading = false
      })
    },
    handleFilter () {
      this.listQuery.page = 1
      this.getList()
    },
    handleReset () {
      this.searchForm = {
        ...defaultSearchParams
      }
      this.listQuery.page = 1
      this.getList()
    },
    // 跳转到订单详情
    toDtl (item, row) {
      const items = item.split('_')
      if (items.length > 0) {
        const soNo = items[0]
        const sapNo = items[1] === 'null' ? '' : items[1]
        const query = {
          soNo,
          id: row.id,
          refresh: true
        }
        if (sapNo) {
          query['sapOrderNo'] = sapNo
        }
        this.$router.jumpToSoOrderDetail({
          query
        })
      }
    },
    handleOpenCreateDlg () {
      checkPermission().then(res => {
        if (res && res.code === 200) {
          this.createOrder = true
        } else if (res && res.data) {
          this.$alert(res.data, '提示')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.orderListTop {
  margin: 10px 0 10px;
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}

.el-autocomplete,
.el-select {
  width: 100%;
}

.filterBtn {
  width: 100px;
  margin-left: 20px;
}

.selectClientItem {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
  }
}

.remark {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.link-to-detail{
  color: #597bee;
  cursor: pointer;
}
</style>
