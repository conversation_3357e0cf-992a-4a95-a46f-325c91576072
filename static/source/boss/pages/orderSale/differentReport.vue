<template>
  <div class="list-container">
    <el-form :model="searchForm" ref="searchFrom" :rules="rules" style="width: 100%" label-suffix=":" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="dn单号">
            <el-input
              v-model="searchForm.dnNo"
              filterable
              clearable
              style="width: 100%"
              placeholder="请输入dn单号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="9">
          <el-form-item label="SKU" prop="skuNos" required>
            <el-input
              v-model="searchForm.skuNos"
              filterable
              clearable
              style="width: 100%"
              placeholder="最多支持50个SKU按空格隔开搜索"
            />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="销售单号">
            <el-input
              v-model="searchForm.soNo"
              filterable
              clearable
              style="width: 100%"
              placeholder="请输入销售单号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="2" :style="{display: 'flex', justifyContent: 'flex-end'}">
          <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <vxe-grid
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="differentListGrid"
      row-id="id"
      height="740px"
      id="different_grid"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'manual', mode: 'row', autoClear: false, showIcon: false, showStatus: true}"
      highlight-hover-row
    >
      <template v-slot:toolbar_tools>
        <el-button type="primary" size="mini" @click="handleDownExcel">全部下载</el-button>
      </template>

      <template #outDnNo_default="{ row }">
        <el-button type="text" @click="toDtl(row.outDnNo)">{{ row.outDnNo }}</el-button>
      </template>

      <template #inDnNo_default="{ row }">
        <el-button type="text" @click="toDtl(row.inDnNo)">{{ row.inDnNo }}</el-button>
      </template>

      <template #soNo_default="{ row }">
        <el-button type="text" @click="jumpToDetail(row)">{{ row.soNo }}</el-button>
      </template>

    </vxe-grid>
  </div>
</template>

<script>
import { safeRun } from '@/utils/index'
import { queryDifferenceReport } from '@/api/orderSale'
import { writeFile } from '@boss/excel'
import moment from 'moment'
import * as shortid from 'shortid'

const columns = [
  {
    field: 'outDnNo',
    title: '出库DN单号',
    minWidth: 120,
    titleHelp: {
      message: '402单号，基于虚拟仓'
    },
    slots: {
      default: 'outDnNo_default'
    }
  },
  {
    field: 'outDnItemNo',
    title: '出库DN行号',
    minWidth: 100
  },
  {
    field: 'inDnNo',
    title: '入库DN单号',
    minWidth: 120,
    titleHelp: {
      message: '401单号，基于虚拟仓'
    },
    slots: {
      default: 'inDnNo_default'
    }
  },
  {
    field: 'inDnItemNo',
    title: '入库DN行号',
    minWidth: 100
  },
  {
    field: 'inNum',
    title: '入库数量',
    minWidth: 80
  },
  {
    field: 'outNum',
    title: '出库数量',
    minWidth: 80
  },
  {
    field: 'gapNum',
    title: '差异数量',
    minWidth: 80
  },
  {
    field: 'soNo',
    title: 'SO单号',
    minWidth: 120,
    slots: {
      default: 'soNo_default'
    }
  },
  {
    field: 'soItemNo',
    title: 'SO行号',
    minWidth: 80
  },
  {
    field: 'companyCode',
    title: '公司代码',
    minWidth: 100
  },
  {
    field: 'factory',
    title: '工厂',
    minWidth: 100
  },
  {
    field: 'outPosition',
    title: '出货库存地点',
    minWidth: 120
  },
  {
    field: 'inPosition',
    title: '入库库存地点',
    minWidth: 120
  },
  {
    field: 'skuNo',
    title: '物料',
    minWidth: 100
  },
  {
    field: 'inBatchNos',
    title: '入库批次',
    minWidth: 100
  },
  {
    field: 'outBatchNos',
    title: '出库批次',
    minWidth: 100
  },
  {
    field: 'kf',
    title: '客服',
    minWidth: 80,
    titleHelp: {
      message: '差异处理负责人'
    }
  }
]

export default {
  name: 'defferentReport',
  data () {
    return {
      searchForm: {},
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          tools: 'toolbar_tools'
        }
      },
      columns,
      tableData: [],
      rules: {
        skuNos: [
          { required: true, message: 'SKU不能为空！', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  methods: {
    handleSearch () {
      this.$refs.searchFrom.validate((valid) => {
        if (valid) {
          this.getDifferenceReport()
        } else {
          return false;
        }
      })
    },
    formatParams(params) {
      let form = { ...params };
      form.skuNos = safeRun(() =>
        form.skuNos
          .split(/\s/).filter((e) => e)
      );
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.skuNos.length > 50) {
          ret = false
          this.$message.error('最多支持50个SKU按空格隔开搜索！')
        }
      })
      return ret
    },
    async getDifferenceReport () {
      try {
        const data = this.formatParams(this.searchForm);
        if (!this.validate(data)) return
        this.tableLoading = true
        this.searchLoading = true
        const res = await queryDifferenceReport(data)
        if (res.code === 200) {
          this.tableData = res.data
        } else {
          this.$message.error(res.msg)
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        console.log(error);
        this.tableLoading = false
        this.searchLoading = false
      }
    },
    handleDownExcel () {
      const mapping = {
        outDnNo: '出库DN单号',
        outDnItemNo: '出库DN行号',
        inDnNo: '入库DN单号',
        inDnItemNo: '入库DN行号',
        inNum: '入库数量',
        outNum: '出库数量',
        gapNum: '差异数量',
        soNo: 'SO单号',
        soItemNo: 'SO行号',
        companyCode: '公司代码',
        factory: '工厂',
        outPosition: '出货库存地点',
        inPosition: '入库库存地点',
        skuNo: '物料',
        inBatchNos: '入库批次',
        outBatchNos: '出库批次',
        kf: '客服'
      }

      const allList = this.tableData.map(item => {
        const data = { ...item }
        Object.keys(data).forEach(key => {
          if (mapping[key]) {
            data[mapping[key]] = data[key];
          }
          delete data[key]
        })
        console.log(data);
        return data
      })

      writeFile(allList, `在途虚拟仓差异报表 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: Object.values(mapping) })
    },
    toDtl (id) {
      this.$router.push({
        path: `/orderDelivery/detail/${id}`
      })
    },
    jumpToDetail (row) {
      this.$router.jumpToSoOrderDetail({
        query: { soNo: row.soNo, id: shortid.generate(), refresh: true }
      })
    }
  }
}
</script>

<style>
.list-container {
  padding: 20px;
}
</style>
