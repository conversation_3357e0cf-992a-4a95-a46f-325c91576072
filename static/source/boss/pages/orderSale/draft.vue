<template>
  <div class="app-container order-list-components">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="订单号检索" prop="voucherNo">
              <el-input
                v-model.trim="searchForm.voucherNo"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户检索" prop="customer">
              <el-input
                v-model.trim="searchForm.customer"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单类型" prop="orderTypes">
              <el-select
                v-model="searchForm.orderTypes"
                multiple
                :multiple-limit="multipleLimit"
                placeholder="请选择"
                @change="handleFilter"
              >
                <el-option
                  v-for="item in orderTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="订单创建日期" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                align="right"
                unlink-panels
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              />
              <el-button
                class="filterBtn"
                type="primary"
                icon="el-icon-search"
                @click="handleFilter"
                >查询
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row type="flex" justify="space-between" class="orderListTop">
      <el-col :span="12">
        <!-- <el-button
          type="primary"
          plain
          icon="el-icon-download"
          @click="exportList"
          >列表导出</el-button
        > -->
        <span>列表内订单总数：{{ total }}</span>
      </el-col>
      <el-col :span="6">
        <!-- <el-button type="primary" plain>虚拟调账</el-button> -->
        <el-button type="primary" @click="createOrder = true"
          >创建订单</el-button
        >
      </el-col>
    </el-row>
    <el-table
      v-loading="listLoading"
      :data="list"
      border
      fit
      highlight-current-row
      height="500"
    >
      <el-table-column
        label="OMS订单号"
        min-width="100px"
        align="center"
        prop="soNo"
      />
      <el-table-column
        label="前端订单号"
        min-width="100px"
        align="center"
        prop="orderNo"
      />
      <el-table-column
        label="SAP订单号"
        min-width="100px"
        align="center"
        prop="sapOrderNo"
      />
      <el-table-column
        label="订单类型"
        width="100px"
        align="center"
        prop="type"
      />
      <el-table-column
        label="客户名称"
        min-width="100px"
        align="center"
        prop="customerName"
      />
      <el-table-column
        label="客户订单号"
        min-width="100px"
        align="center"
        prop="customerReferenceNo"
      />
      <el-table-column
        label="订单来源"
        width="100px"
        align="center"
        prop="orderSource"
      />
      <el-table-column
        label="销售"
        width="80px"
        align="center"
        prop="sellerName"
      />
      <el-table-column
        label="客服"
        width="80px"
        align="center"
        prop="customerServiceName"
      />
      <el-table-column
        label="创建时间"
        width="100px"
        align="center"
        prop="sapAddDate"
      />
      <el-table-column
        label="操作"
        align="center"
        width="130"
        fixed="right"
        class-name="small-padding"
      >
        <template slot-scope="{ row }">
          <el-button type="text" size="mini" @click="toDtl(row)"
            >查看详情</el-button
          >
          <br />
          <el-button type="text" size="small" :disabled="!row.sapOrderNo"
            >导出订单</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
    <!-- 创建订单的选择公司类型弹框 -->
    <CreateOrderDialog
      :show-dialog.sync="createOrder"
      :order-types="orderTypeOption"
      @cancel="createOrder = false"
      @confirm="createOrderDialogConfirm"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { orderList } from '@/api/ecorp'
import * as sellOrder from '@/api/orderSale'
import { downloadFile } from '@/utility/request.js'
import CreateOrderDialog from './components/list/CreateOrderDlg'

import moment from 'moment'

const prefixFront = '/oms-new'
const upStatusOption = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '已上传附件',
    value: '1'
  },
  {
    label: '未上传附件',
    value: '0'
  }
]

export default {
  name: 'OrderList',
  components: {
    Pagination,
    CreateOrderDialog
  },
  filters: {
    uploadTxtFilter (val) {
      if (val === '1') {
        return '继续上传'
      } else {
        return '上传文档'
      }
    }
  },
  data () {
    var checkDateRange = (rule, value, callback) => {
      const createTimeFrom = value[0]
      const createTimeTo = value[1]
      if (
        moment(createTimeTo)
          .subtract(3, 'month')
          .isAfter(createTimeFrom)
      ) {
        this.validFlag = false
        callback(new Error('时间段只能支持3个月内，请重新选择'))
      } else {
        this.validFlag = true
        callback()
      }
    }
    return {
      // 组件所在的路由名字
      componentsName: 'fomalOrder',
      fileSizeLimit: 10, // 上传文件大小限制，单位 MB
      fileName: '', // 上传文件名
      listLoading: false,
      searchForm: {
        voucherNo: '',
        customer: '',
        upStatus: '',
        status: 'sketch', // 写死成sketch
        orderTypes: [],
        dateRange: '', // 日期区间（数组）
        startDate: '',
        endDate: ''
        // pageNo: ''
      },
      formParam: {},
      validFlag: true, // 表单验证 true-通过，false-未通过
      list: [],
      total: 0,
      attachmentTotal: 0,
      noAttachmentTotal: 0,

      // hasDocCnt: 0,
      // noDocCnt: 0,
      listQuery: {
        page: 1,
        size: 20
      },
      upStatusOption: upStatusOption,
      orderTypeOption: [],
      rules: {
        dateRange: [
          {
            validator: checkDateRange,
            trigger: 'blur'
          }
        ]
      },
      multipleLimit: 5,
      // 选择公司表单的数据
      selectCompany: {
        name: '',
        type: ''
        // order: this.componentsName
      },
      // 检查规则

      // 模态框
      createOrder: false,
      createOrderData: null
    }
  },
  created () {
    this.componentsName = this.$route.name
    this.getOrderTypes()
    this.getList()
  },
  methods: {
    /**
     * 获取订单类型列表
     */
    getOrderTypes () {
      sellOrder.getOptionTypes().then(result => {
        if (result.code === 200) {
          this.orderTypeOption = result.data.records.map(item => {
            return { label: item.name, value: item.code }
          })
        } else {
          this.$notify(result.msg)
        }
      })
    },

    createOrderDialogConfirm (event) {
      this.createOrderData = event
    },
    getList () {
      this.listLoading = true
      this.dealFormParam()
      const param = {
        ...this.formParam,
        current: this.listQuery.page - 1,
        pageSize: this.listQuery.size
      }
      orderList(param)
        .then(response => {
          if (response.code === 200 && response.data) {
            this.list = response.data
            this.total = response.totalCount || 0
            // this.hasDocCnt = response.data.hasDocCnt || 0
            // this.noDocCnt = response.data.noDocCnt || 0
          } else {
            this.$notify.error(response.msg)
            // 报错时清空数据
            this.list = []
            this.total = 0
            // this.hasDocCnt = 0
            // this.noDocCnt = 0
          }
          this.listLoading = false
        })
        .catch(e => {
          this.listLoading = false
        })
    },
    handleFilter () {
      if (this.validFlag) {
        this.listQuery.page = 1
        this.getList()
      }
    },
    // 处理搜索表单数据（去除日期区间dateRange，避免后台接收参数时报错）
    dealFormParam () {
      // 1.去除日期区间dateRange，避免后台接收参数时报错
      if (this.searchForm.dateRange === null) {
        this.searchForm.dateRange = ''
      }
      this.searchForm.startDate = this.searchForm.dateRange[0]
      this.searchForm.endDate = this.searchForm.dateRange[1]
      this.formParam = JSON.parse(JSON.stringify(this.searchForm))
      delete this.formParam.dateRange

      // 2.多选订单类型orderTypes数组改为逗号分隔
      this.formParam.orderTypes =
        this.searchForm.orderTypes && this.searchForm.orderTypes.join(',')
    },
    // 跳转到订单详情
    toDtl (row) {
      // this.$router.push({
      //   path: '/sale/detail',
      //   query: {
      //     soNo: row.soNo,
      //     id: row.id,
      //     opCode: this.searchForm.opCode,
      //     opName: this.searchForm.opName,
      //     refresh: true
      //   }
      // })

      this.$router.push({
        path: '/sale/edit',
        query: {
          omsNo: row.soNo
        }
      })
    },
    toConfirm (voucherNo, id) {
      this.$confirm('可进入订单详情管理电子文档', '上传成功', {
        confirmButtonText: '文档管理',
        cancelButtonText: '了解',
        type: 'warning'
      })
        .then(() => {
          this.$router.jumpToSoOrderDetail({
            // path: `/orderSale/formal/detail?soVoucherId=${voucherNo}&id=${id}&refresh=true#point=dz`
            query: {
              sapOrderNo: voucherNo,
              id: id,
              // point: 'dz',
              refresh: true
            },
            hash: {
              point: 'dz'
            }
          })
        })
        .catch(() => {})
    },
    // 列表导出
    exportList () {
      this.dealFormParam()
      const param = { ...this.formParam }

      let exportUrl = `${prefixFront}/orderList/export/excel?`
      for (var key in param) {
        exportUrl += key + '=' + param[key] + '&'
      }
      downloadFile(exportUrl)
    }
  }
}
</script>
<style lang="scss" scoped>
.orderListTop {
  margin: 20px 0 10px;

  .el-col:nth-child(1) {
    display: flex;
    align-items: center;

    span {
      margin: 0 10px;
      color: #909399;
    }
  }

  .el-col:nth-child(2) {
    text-align: right;
  }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}

.el-autocomplete,
.el-select {
  width: 100%;
}

.filterBtn {
  margin-left: 20px;
}
</style>
