<template>
  <div class="app-container">
    <PerformanceProcess :orderInfo="orderInfo" v-if="orderInfo.orderNo"/>
  </div>
</template>

<script>
import PerformanceProcess from './components/detail/performanceProcess'

export default {
  name: 'OrderDetail',
  components: {
    PerformanceProcess
  },
  data () {
    return {
      orderInfo: {
        orderNo: ''
      }
    }
  },
  created () {
    let orderNo = this.$route.query.orderNo
    this.orderInfo.orderNo = orderNo
  }
}
</script>

<style scoped>

</style>
