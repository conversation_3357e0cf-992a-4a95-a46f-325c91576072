<template>
  <div class="app-container" v-loading="loading">
    <el-row class="logistics_detail_con">
      <h3>
        该订单被<em>【{{ logisticsName }}】</em>承运，运单号：<em>{{ waybillInfo.waybillList }}</em>
      </h3>
      <el-col :span="24">
        <ul>
          <li
            v-for="(log, index) in logisticsList"
            :key="index"
            :class="index === 0 ? 'active' : ''"
          >
            <em class="time">{{ log.trackTime }}</em>
            <div class="remark" :class="{ bold: log.bold }">
              <span v-show="log.type == 1">【{{ log.address }}】</span
              >{{ log.remark }}
            </div>
          </li>
        </ul>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  fetchInvoiceLogisticsDetail
} from '@/api/orderSupplyList'

export default {
  name: 'invoiceProgressDetail',
  filters: {},
  data () {
    return {
      loading: true,
      waybillInfo: {}, // 运单
      companyNo: this.$route.query.companyNo,
      logisticsNo: this.$route.query.logisticsNo,
      logisticsName: this.$route.query.logisticsName,
      logisticsList: [] // 物流轨迹详情
    }
  },
  created () {
    this.getLogisticsDetail()
  },
  methods: {
    // 查询物流轨迹详情
    getLogisticsDetail () {
      if (!this.companyNo || !this.logisticsNo) {
        return this.$notify.error('缺少必要参数companyNo, logisticsNo！')
      }
      this.loading = true
      fetchInvoiceLogisticsDetail(this.companyNo, this.logisticsNo).then(response => {
        if (response.code === 200) {
          const { carrierName, arrivalTime, packageQuantity, waybillList } = {
            ...response.data
          }
          this.waybillInfo = {
            carrierName,
            arrivalTime,
            packageQuantity,
            waybillList
          }

          this.logisticsList = response.data && response.data.contList
        } else {
          this.$notify.error(response.msg)
        }
      })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>
/*  物流节点 */
.logistics_con {
  font-size: 12px;
  color: #333333;
  height: 220px;
  background-color: #ffffff;
  border: solid 1px #dfe4ed;
  padding: 0 15px;
}
.logistics_con em {
  font-weight: bold;
  font-style: normal;
}
.logistics_con .main_info {
  margin-top: 12px;
}
.logistics_con .main_info span {
  margin-right: 20px;
}
.logistics_con .dnNo {
  font-size: 12px;
}
.logistics_main {
  margin: 25px 0;
}
.logistics_con .node {
  min-width: 75px;
}
.logistics_con .node * {
  display: block;
  text-align: center;
  font-size: 12px;
  color: #333;
  line-height: 1;
  white-space: nowrap;
}
.logistics_con .node .icon {
  width: 45px;
  height: 45px;
  background: #e2e2e2 url('~@/assets/orderSupplyList/logistics_icons.png')
    no-repeat;
  border-radius: 100%;
  margin: 10px auto;
}
.logistics_con .node.active .icon {
  background-color: #42b983;
  background-position-y: -45px;
}
.logistics_con .node01 .icon {
  background-position: 0 0;
}
.logistics_con .node02 .icon {
  background-position: -45px 0;
}
.logistics_con .node03 .icon {
  background-position: -90px 0;
}
.logistics_con .node04 .icon {
  background-position: -135px 0;
}
.logistics_con .node05 .icon {
  background-position: -180px 0;
}
.logistics_con .node .date {
  color: #666666;
  margin-top: 10px;
}
.logistics_con .arrow {
  min-width: 104px;
  line-height: 96px;
  height: 111px;
}
.detail_info {
  color: #666666;
  line-height: 20px;
}
.detail_info i {
  color: #1890ff;
  font-style: normal;
}
.detail_info em {
  color: #333333;
}
/* 前置单号列表 */
.list_con {
  margin-top: 5px;
}
.list_con h2 {
  font-size: 16px;
  color: #666666;
  font-weight: bold;
}
/* 物流轨迹详情 */
.logistics_detail_con {
  /* height: 360px; */
  background-color: #ffffff;
  border: solid 1px #e2e2e2;
  margin-top: 20px;
}
.logistics_detail_con h3 {
  background-color: #f4f4f4;
  font-size: 12px;
  color: #666666;
  padding: 13px 10px;
  font-weight: normal;
  margin: 0;
  line-height: 1.25;
}
.logistics_detail_con h3 em {
  color: #333333;
  font-style: normal;
  font-weight: bold;
}
.logistics_detail_con ul {
  max-height: 400px;
  overflow-y: auto;
  margin: 0;
  padding: 10px 20px;
}
.logistics_detail_con li {
  font-size: 12px;
  color: #333333;
  position: relative;
  padding-left: 150px;
}
.logistics_detail_con li .time {
  color: #666666;
  font-style: normal;
  position: absolute;
  top: 13px;
  left: 0;
}
.logistics_detail_con li .remark {
  border-left: 1px dashed #bdbdbd;
  padding: 13px 10px 13px 50px;
}
.logistics_detail_con li .bold {
  font-weight: bold;
}
.logistics_detail_con li .remark::before {
  content: '';
  position: absolute;
  top: 15px;
  left: 146px;
  width: 9px;
  height: 9px;
  background-color: #cecece;
  border-radius: 100%;
}
.logistics_detail_con li.active .remark::before {
  background-color: #1890ff;
}
</style>
