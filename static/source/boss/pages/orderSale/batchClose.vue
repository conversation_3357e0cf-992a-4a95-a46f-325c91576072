<template>
  <div class="close">
    <div class="close-query">
      <el-form ref="closeForm" :model="closeQueryData"
               label-width="110px" class="close-form" :rules="rules"
      >
        <el-row>
          <el-col :span="14">
            <el-form-item label="订单号：" prop="orderNo">
              <el-input
                v-model="closeQueryData.orderNo"
                placeholder="外围\OMS\SAP 均支持搜索，同时支持10个单号，以空格分隔"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="客户名称：" prop="customerNo">
              <el-select
                v-model="closeQueryData.customerNo"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="customerNoLoading"
                style="width:100%"
                clearable>
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="订单类型：" prop="orderTypeSet">
              <el-select
                clearable
                v-model="closeQueryData.orderTypeSet"
                multiple
                :multiple-limit="multipleLimit"
                placeholder="请选择"
                style="width:100%"
              >
                <el-option
                  v-for="item in orderTypeOption"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="商品名称：" prop="materiel">
              <el-select
                v-model="closeQueryData.materiel"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteSkuMethod"
                :loading="skuNoLoading"
                style="width:100%"
                clearable>
                <el-option
                  style="width:500px"
                  v-for="item in skuNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="创建时间" prop="createTime" class="close-form-item">
              <el-date-picker
                v-model="closeQueryData.createTime"
                type="daterange"
                start-placeholder="创建开始时间"
                end-placeholder="创建结束时间"
                value-format="yyyy-MM-dd"
                :default-time="['00:00:00', '23:59:59']"
                style="width:100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-form-item class="close-form-item btn-row">
            <el-button class="close-btn" type="primary" @click="onSubmit('closeForm')">查询</el-button>
            <el-button class="close-btn" @click="onReset('closeForm')">重置</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div class="close-row">
      <span>请勾选您要关闭的商品行，并点击右侧关闭订单按钮进行关闭，关闭后商品行的订单数量将更新为已交货数量</span>
      <el-button type="primary" @click="handleCloseOrder" :disabled="!tableSelection||tableSelection.length===0">关闭订单</el-button>
    </div>
    <vxe-table
      border
      highlight-hover-row
      height="500"
      align="center"
      size="small"
      :scroll-y="{gt: -1}"
      :data="closeListData"
      :checkbox-config="{checkMethod: checCheckbox}"
      @checkbox-change="onTableSelectionChange"
      @checkbox-all="onTableSelectionAllChange"
    >
      <vxe-table-column type="checkbox" title="" fixed="left" align="center"></vxe-table-column>
      <vxe-table-column
        v-for="col in closeColumn"
        align="center"
        :width="col.width"
        :key="col.prop"
        :field="col.prop"
        :title="col.label"
      >
        <template slot-scope="{row}">
        <span v-if="col.format">
          {{ col.format(row) }}
        </span>
          <span v-else>{{ row[col.prop] }}</span>
        </template>
      </vxe-table-column>
    </vxe-table>
    <div class="pagination-container">
      <pagination
        v-show="closeTotal > 0"
        :total="closeTotal"
        :page.sync="closeQueryData.page"
        :limit.sync="closeQueryData.size"
        :page-sizes="[200, 250, 300]"
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="querycloseData"
      />
    </div>
    <CancelOrderDlg
      :show-dialog.sync="showCloseDialog"
      :largeReduceReasonOptions="largeReduceReasonOptions"
      :items="tableSelection"
      @submit="handleClose"
    />
  </div>
</template>

<script>
import moment from 'moment'
import { list, close } from '@/api/orderBatchClose'
import { searchSkuList } from '@/api/orderSale'
import {
  goodsCustomerListSearch
} from '@/api/insteadOrder'
import Pagination from '@/components/Pagination'
import { requestWithLoading } from './utils'
import { checkBTBOrder } from '@/utils/order'
import CancelOrderDlg from '@/pages/orderSale/components/common/CancelOrderDlg.vue'

export default {
  components: {
    Pagination,
    CancelOrderDlg
  },
  data() {
    return {
      closeQueryData: this.getDefaultQueryParams(),
      closeListData: [],
      closeTotal: 0,
      tableSelection: [],
      multipleLimit: 5,
      skuNoLoading: false,
      skuNoOptions: [],
      customerNoLoading: false,
      customerNoOptions: [],
      closeColumn: [
        { prop: 'soNo', label: 'OMS单号', width: '200' },
        { prop: 'orderItemNo', label: 'OMS行号', width: '80' },
        { prop: 'sapOrderNo', label: 'SAP单号', width: '120' },
        { prop: 'sapItemNo', label: 'SAP行号', width: '120' },
        { prop: 'gmtCreate', label: '创建时间', width: '180' },
        { prop: 'customerNo', label: '客户编码', width: '100' },
        { prop: 'customerName', label: '客户名称', width: '200' },
        { prop: 'materiel', label: '商品编码', width: '100' },
        { prop: 'sapMaterialName', label: '商品描述', width: '240' },
        { prop: 'quantity', label: '订单数量', width: '90' },
        { prop: 'clearedQty', label: '已交货数量', width: '90' },
        {
          prop: 'directDeliverySupplier',
          label: '直发类型',
          width: '100',
          format: row => {
            return this.getDirectDeliverySupplier(row)
          }
        },
        { prop: 'orderType', label: '订单类型', width: '90' },
        {
          prop: 'factory',
          label: '工厂',
          width: '90'
        },
        {
          prop: 'position',
          label: '仓位',
          width: '90'
        }
      ],
      isLoading: false,
      filterFactoryList: [],
      rules: {
        createTime: [
          { required: true, message: '请输入交货时间', trigger: 'blur' }
        ]
      },
      largeReduceReason: null,
      largeReduceReasonDesc: null,
      showCloseDialog: false
    }
  },
  computed: {
    largeReduceReasonOptions() {
      const typeList = [...new Set(this.tableSelection.map(order => order.orderType).filter(Boolean))]
      let originOptions = this.dictList['largeReduceReason'] || []
      let options = originOptions
      const optionsMap = {}
      typeList.forEach(type => {
        const typeOptions = originOptions.filter(option => option.parentCode === type)
        if (typeOptions.length) {
          options = typeOptions
        } else {
          options = originOptions.filter(option => option.parentCode === '')
        }
        optionsMap[type] = options
      })
      console.log(optionsMap)
      // options取交集法
      if (Object.keys(optionsMap).length > 1) {
        options = Object.keys(optionsMap).reduce((prev, next) => {
          return (optionsMap[prev] || []).filter(prevItem => optionsMap[next].some(nextItem => nextItem === prevItem.code))
        })
      }
      console.log(options)
      return options.filter((a) => a.status !== 'stop')
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderTypeOption() {
      return this.dictList && this.dictList['soCategory'] ? this.dictList['soCategory'].map(item => {
        return { label: item.name, value: item.code }
      }) : []
    },
    factoryList() {
      const facList = (this.dictList && this.dictList['factory']) ? this.dictList['factory']
        .filter(item => {
          return item.parentCode === ''
        }) : []
      const results = []
      this.filterFactoryList.forEach(item => {
        const foundItem = facList.find(fac => fac.code === item)
        if (foundItem && foundItem.code) {
          results.push(foundItem)
        }
      })
      return results
    }
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    // this.querycloseData()
  },
  methods: {
    getDirectDeliverySupplier(row) {
      const { directDeliverySupplier } = row
      if (directDeliverySupplier != null && this.dictList['directDeliverySupplier']) {
        const found = this.dictList['directDeliverySupplier'].find(item => item.code === directDeliverySupplier)
        if (found) {
          return found.name
        }
      }
      return ''
    },
    // 远程查找客户
    remoteCustomerMethod(query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        goodsCustomerListSearch({
          customer_name: key,
          pageSize: 100
        }).then(res => {
          this.customerNoLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.customerNoOptions = res.data.contents.map(item => {
                return {
                  value: item.customerNo,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      }
      this.customerNoOptions = []
    },
    // 远程查找客户
    remoteSkuMethod(query) {
      const key = query.trim()
      if (key !== '') {
        this.skuNoLoading = true
        searchSkuList(key).then(res => {
          console.log(res)
          this.skuNoLoading = false
          if (res.code === 200 && res.data) {
            if (res.data.length > 0) {
              this.skuNoOptions = res.data.map(item => {
                return {
                  value: item.skuNo,
                  label: item.materialDescribe
                }
              })
            } else {
              this.skuNoOptions = []
            }
          } else {
            this.skuNoOptions = []
          }
        })
      } else {
        this.skuNoOptions = []
      }
    },
    checCheckbox({ row }) {
      const { quantity, clearedQty } = row
      return quantity > clearedQty
    },
    getDefaultQueryParams() {
      const startDate = moment().subtract(6, 'days').format('yyyy-MM-DD')
      const endDate = moment().format('yyyy-MM-DD')
      return {
        page: 1,
        size: 200,
        createTime: [startDate, endDate]
      }
    },
    querycloseData(callback) {
      const { orderNo, createTime, page, size, orderTypeSet } = this.closeQueryData
      const params = {
        ...this.closeQueryData,
        current: page,
        size: size
      }
      if (orderNo) {
        let on = orderNo.split(' ').filter(item => !!(item.trim())).map(item => item.trim())
        if (on.length > 10) {
          this.$alert('只支持10个单号查询', '错误', {
            type: 'error'
          })
          return
        }
        params.orderNo = on.length > 0 ? on.join(',') : ''
      }
      if (!params.orderNo) {
        delete params.orderNo
      }
      delete params.createTime
      if (createTime && createTime.length === 2) {
        const m1 = moment(createTime[1])
        const m2 = moment(createTime[0])
        const d = m1.diff(m2, 'months', true)
        if (!orderNo && d > 1) {
          this.$alert('只允许查询一个月内的数据！', '错误', {
            type: 'error'
          })
          return
        }
        params['orderBeginDate'] = createTime[0]
        params['orderEndDate'] = createTime[1]
      }
      if (orderTypeSet && orderTypeSet.length !== 0) {
        params.orderTypeSet = orderTypeSet.join(',')
      } else {
        delete params.orderTypeSet
      }
      requestWithLoading(this, list(params), data => {
        data.records = data.records || []
        this.closeListData = data.records.map(item => {
          const { confirmIncloseckQty } = item
          return {
            ...item,
            deliveryQty: confirmIncloseckQty
          }
        })
        this.closeTotal = data.total
        this.tableSelection = []
        if (callback) {
          callback()
        }
      })
    },
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.querycloseData()
        } else {
          return false
        }
      })
    },
    onReset(formName) {
      this.$refs[formName].resetFields()
      this.closeQueryData = this.getDefaultQueryParams()
      this.closeListData = []
      this.querycloseData()
    },
    resetReason() {
      this.largeReduceReason = null
      this.largeReduceReasonDesc = null
    },
    closeDialog() {
      this.showCloseDialog = false
      this.resetReason()
    },
    handleCloseOrder() {
      this.showCloseDialog = true
    },
    handleClose(formData) {
      if (this.tableSelection && this.tableSelection.length > 0) {
        this.$confirm('确认对已勾选的行进行关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const closeItems = this.tableSelection.map(item => {
            const {
              soNo, orderItemNo, sapOrderNo, sapItemNo
            } = item
            return {
              ...item,
              largeReduceReason: formData.largeReduceReason.code,
              largeReduceReasonDesc: formData.largeReduceReasonDesc,
              materialReplaceProcessNo: formData.materialReplaceProcessNo,
              sapOrderNo,
              sapOrderItemNo: sapItemNo,
              soItemNo: orderItemNo,
              soNo
            }
          })
          this.closeDialog()
          // 判断背靠背订单
          await checkBTBOrder(this, closeItems)
          requestWithLoading(this, close({
            closeItems
          }), data => {
            if (data && typeof data === 'string') {
              this.$alert(data, '关闭完成', {
                confirmButtonText: '确定',
                dangerouslyUseHTMLString: true,
                callback: action => {
                  this.querycloseData()
                }
              })
            }
          })
        }).catch(() => {
        })
      }
    },
    onTableSelectionChange({ records }) {
      this.tableSelection = records
    },
    onTableSelectionAllChange({ records }) {
      this.tableSelection = records
    }
  }
}
</script>

<style lang="scss" scoped>
  .close {
    margin: 0 5px;

    &-query {
      padding: 10px;

      .form-item {
        width: 192px;
      }

      .form-item-long {
        width: 540px;
      }
    }

    .btn-row {
      margin-left: 20px;
    }

    &-btn {
      width: 90px;
    }

    &-row {
      font-size: 14px;
      color: #909399;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .pagination-container {
    margin-top: 0;
    padding: 16px 0;
    text-align: right;
  }
</style>
<style lang="scss">
  .btb-wrapper {
    overflow-y: scroll;
    max-height: 400px;
  }
</style>
