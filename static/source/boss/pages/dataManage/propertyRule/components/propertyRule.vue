<template>
  <div class="data-manage-rule-container">
    <el-button v-if="addPropertyButton" size="small" @click="openAddPropertyRule" type="primary" class="rightBtn">+添加属性及规则</el-button>
    <div class="body-container">
        <div class="data-manage-rule-center">
          <div class="rulelist propertyRule">
            <el-table v-loading="loading.table" row-key="id" :data="templateList" border fit highlight-current-row ref="propertyRuleTable" :height="'570px'">
              <el-table-column label="属性名称" align="center" prop="name" />
              <el-table-column label="开启检测" align="center" prop="state">
                <template slot-scope="scope">
                  {{ scope.row.state ? '已开启' : '未开启' }}
                </template>
              </el-table-column>
              <el-table-column label="属性类型" align="center" prop="type">
                <template slot-scope="scope">
                  {{
                    scope.row.type == 'MAIN'
                      ? '关键属性'
                      : scope.row.type == 'SALE'
                      ? '销售属性'
                      : scope.row.type == 'NORMAL'
                      ? '一般属性'
                      : scope.row.type == 'BLANK'
                      ? '空'
                      : '-'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="字段类型" align="center" prop="dataType">
                <template slot-scope="scope">
                  {{
                    scope.row.dataType == 'TEXT'
                      ? '纯文本'
                      : ['ENUM_TYPE','SINGLE'].includes(scope.row.dataType)
                      ? '单选枚举值'
                      : scope.row.dataType == 'MULTI'
                      ? '多选枚举值'
                      : scope.row.dataType == 'NUMBER'
                      ? '数字'
                      : scope.row.dataType == 'NO_LIMIT'
                      ? '不校验'
                      : '不校验'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="属性排序" align="center" prop="seq">
              </el-table-column>
              <el-table-column label="是否必填" align="center" prop="required">
                <template slot-scope="scope">
                  {{
                     (scope.row.required||false) ? '是' : '否'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="是否选型" align="center" prop="selection">
                <template slot-scope="scope">
                  {{
                     (scope.row.selection||false) ? '是' : '否'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="是否展示" align="center" prop="show">
                <template slot-scope="scope">
                  {{
                     (scope.row.show||false) ? '是' : '否'
                  }}
                </template>
              </el-table-column>
              <el-table-column label="是否父标品聚合属性" align="center" prop="ifFbp">
                <template slot-scope="scope">
                  {{
                     (scope.row.ifFbp||false) | booleanFormat
                  }}
                </template>
              </el-table-column>
              <el-table-column label="是否子标品定品属性" align="center" prop="ifZbp">
                <template slot-scope="scope">
                  {{
                     (scope.row.ifZbp||false) | booleanFormat
                  }}
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="120px" align="center" prop="example">
                <template slot-scope="scope">
                  {{ scope.row.example ? scope.row.example : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="规则解析问题" min-width="100px" align="center" prop="error">
                <template slot-scope="scope">
                  {{ scope.row.error ? scope.row.error : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="120px">
                <template slot-scope="scope">
                  <el-button type="text" @click="showRuleDetail(scope.$index, scope.row)">详情</el-button>
                  <el-button type="text" v-if="editPropertyButton" @click="editPropertyRule(scope.$index, scope.row)">编辑</el-button>
                  <el-popconfirm confirm-button-text="确定" cancel-button-text="不用了" icon="el-icon-info" icon-color="red" title="确定删除吗？" @confirm="getRuleDel(scope.$index, scope.row)">
                    <el-button type="text" v-if="deletePropertyButton" slot="reference" style="margin-left: 10px">删除</el-button>
                    <!--<el-button slot="reference" type="text" @click="getRuleDel(scope.$index, scope.row)">删除</el-button>-->
                  </el-popconfirm>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="排序" align="center" min-width="120" :class-name="'move-td'">
                <template>
                  <span v-if="templateList.length > 1">
                    <i class="sort-icon"></i>
                    <span style="color:#999">拖拽调整顺序</span>
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <pagination v-show="total > 0" :total="total" align="right" :page.sync="listQueryInfo.current" :limit.sync="listQueryInfo.pageSize" layout=" total, prev, pager, next, jumper" @pagination="pageClick" />
      <!-- </div> -->
      <!--右边添加属性规则 end-->
    </div>
    <!--添加修改属性规则-->
    <el-dialog width="900px" :title="
        isModifyState === '1'
          ? '修改属性及规则'
          : isModifyState === '2'
          ? '添加属性及规则'
          : '属性及规则'
      " class="dialogClass" :visible.sync="showRuleDialog" :close-on-click-modal="false">
      <div class="ruleset">
        <div class="rulesetcont">
          <el-row :span="24">
            <el-col :span="4" class="dialogTitle">目录：</el-col>
            <el-col :span="20">
              <div class="color-grey-300" v-if="currCategory" style="line-height: 32px">
                {{ currCategory.fulePath | categoriesFormat }}
              </div>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="4" class="dialogTitle">属性名称：</el-col>
            <el-col :span="8">
              <el-input :disabled="isModifyState === '1'" v-model.trim="propertyRuleInfo.name" placeholder="请输入属性名称" />
            </el-col>
            <el-col :span="4" class="dialogTitle">开启检测：</el-col>
            <el-col :span="8" style="padding: 4px 0">
              <el-switch v-model="isOpenValue"> </el-switch>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="4" class="dialogTitle">字段类型：</el-col>
            <el-col :span="8">
              <el-select style="width: 100%" v-model="fieldTypeValue" placeholder="请选择字段类型">
                <el-option v-for="item in fieldTypeOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" class="dialogTitle">属性类型：</el-col>
            <el-col :span="8">
              <el-select v-model="propertyTypeValue" placeholder="请选择属性类型">
                <el-option v-for="item in propertyTypeOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col v-show="
                fieldTypeValue !== 'NUMBER' && !isEnumType(fieldTypeValue)
              " :span="4" class="dialogTitle">是否必填：</el-col>
            <el-col v-show="
                fieldTypeValue !== 'NUMBER' && !isEnumType(fieldTypeValue)
              " :span="8" style="padding: 4px 0">
              <el-switch v-model="ismustValue" :disabled="isMustDisabled"> </el-switch>
            </el-col>
            <!-- <el-col v-show="fieldTypeValue === 'ENUM_TYPE'" :span="4" class="dialogTitle">是否多选：</el-col>
            <el-col v-show="fieldTypeValue === 'ENUM_TYPE'" :span="8" style="padding: 4px 0">
              <el-switch v-model="isMoreValue"> </el-switch>
            </el-col> -->
            <el-col :span="4" class="dialogTitle">备 &nbsp; &nbsp; &nbsp; 注：</el-col>
            <el-col :span="8">
              <el-input v-model.trim="propertyRuleInfo.demo" class="input96" v-bind:class="{ input100: fieldTypeValue === 'NUMBER' }" placeholder="请输入备注" />
            </el-col>
            <el-col :span="4" class="dialogTitle">是否选型：</el-col>
            <el-col :span="8" style="padding: 8px 0">
              <el-radio v-model="propertyRuleInfo.selection" :label="true">是</el-radio>
              <el-radio v-model="propertyRuleInfo.selection" :label="false">否</el-radio>
            </el-col>
            <el-col :span="4" class="dialogTitle">是否展示：</el-col>
            <el-col :span="8" style="padding: 8px 0">
              <el-radio v-model="propertyRuleInfo.show" :label="true">是</el-radio>
              <el-radio v-model="propertyRuleInfo.show" :label="false">否</el-radio>
            </el-col>
            <el-col :span="4" class="dialogTitle">是否父标品聚合属性：</el-col>
            <el-col :span="8" style="padding: 8px 0">
              <el-radio v-model="propertyRuleInfo.ifFbp" :label="true">是</el-radio>
              <el-radio v-model="propertyRuleInfo.ifFbp" :label="false">否</el-radio>
            </el-col>
            <el-col :span="4" class="dialogTitle">是否子标品定品属性：</el-col>
            <el-col :span="8" style="padding: 8px 0">
              <el-radio v-model="propertyRuleInfo.ifZbp" :label="true">是</el-radio>
              <el-radio v-model="propertyRuleInfo.ifZbp" :label="false">否</el-radio>
            </el-col>
            <el-col :span="4" class="dialogTitle">是否sourcing选型：</el-col>
            <el-col :span="8" style="padding: 8px 0">
              <el-radio v-model="propertyRuleInfo.sourcing" :label="true">是</el-radio>
              <el-radio v-model="propertyRuleInfo.sourcing" :label="false">否</el-radio>
            </el-col>
          </el-row>
          <!--前置条件-非数字、非枚举-->
          <div v-show="
              fieldTypeValue !== 'NUMBER' &&
              !isEnumType(fieldTypeValue) &&
              !(
                (fieldTypeValue === 'NO_LIMIT' || fieldTypeValue === 'TEXT') &&
                !ismustValue
              )
            ">
            <el-row :span="24">
              <el-col :span="4" class="dialogTitle">前置属性：</el-col>
              <el-col :span="8">
                <el-select v-model="propertyRuleInfo.propertyId" @change="prePropertyChange" placeholder="请选择前置属性">
                  <el-option v-for="item in prePropertyOptions" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="4" class="dialogTitle">判断条件：</el-col>
              <el-col :span="8">
                <el-select v-model="propertyRuleInfo.conditionType" :disabled="!propertyRuleInfo.propertyId" placeholder="请选择判断条件">
                  <el-option v-for="item in judgeOptions" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :span="24" v-show="propertyRuleInfo.conditionType !== 'EMPTY'">
              <el-col :span="4" class="dialogTitle">条件设置：</el-col>
              <el-col :span="20">
                <el-input style="width: 98%" v-model.trim="propertyRuleInfo.value" :disabled="!propertyRuleInfo.propertyId" @input="modifyValue1" placeholder="请输入条件设置，使用,隔开" />
              </el-col>
            </el-row>
          </div>
          <!--前置条件-非数字、非枚举 end-->
          <!--备注-->
          <el-row :span="24" v-show="
              fieldTypeValue !== 'NUMBER' && !isEnumType(fieldTypeValue)
            ">
            <el-col :span="4" class="dialogTitle">属性示例：</el-col>
            <el-col :span="20">
              <el-input v-model.trim="propertyRuleInfo.remark" style="width: 98%" placeholder="请输入属性示例" />
            </el-col>
          </el-row>
          <!--备注 end-->
          <el-row :span="24">
            <el-col :span="4" class="dialogTitle">填写说明：</el-col>
            <el-col :span="20">
              <el-input v-model.trim="propertyRuleInfo.writeDesc" style="width: 98%" placeholder="请输入填写说明" />
            </el-col>
          </el-row>
          <!--字段类型-枚举-->
          <div v-show="isEnumType(fieldTypeValue)">
            <!--添加的枚举规则列表-->
            <div class="numberRuleTab" style="padding-bottom: 5px">
              <el-radio-group v-model="radioIndex_block_enumeration" size="small" @change="tabEnumerationRule_block">
                <el-radio-button v-for="(
                    item, index
                  ) in propertyRuleInfo.enumerationRuleParams" :item="item" :key="index" :label="index">规则{{ index | numberFormat }}</el-radio-button>
                <el-button type="primary" @click="openAddEnumerationRule_block" size="mini" icon="el-icon-plus" style="
                    margin-left: 10px;
                    position: relative;
                    top: 5px;
                    display: inline-block;
                    border-radius: 2px;
                  "></el-button>
              </el-radio-group>
            </div>
            <!-- 当前选中枚举规则的详情 -->
            <div class="numberRuleDetail" v-if="propertyRuleInfo.enumerationRuleDetailTemp_block">
              <!--前置条件-枚举-->
              <div style="width: 94%; margin: 15px auto">
                <el-row :span="24">
                  <el-col :span="4" class="dialogTitle">前置属性：</el-col>
                  <el-col :span="8">
                    <el-select v-model="
                        propertyRuleInfo.enumerationRuleDetailTemp_block
                          .propertyId
                      " placeholder="请选择前置属性">
                      <el-option v-for="item in prePropertyOptions" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="5" class="dialogTitle">判断条件：</el-col>
                  <el-col :span="7">
                    <el-select v-model="
                        propertyRuleInfo.enumerationRuleDetailTemp_block
                          .conditionType
                      " :disabled="
                        !propertyRuleInfo.enumerationRuleDetailTemp_block
                          .propertyId
                      " placeholder="请选择判断条件">
                      <el-option v-for="item in judgeOptions" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row :span="24" v-show="
                    propertyRuleInfo.enumerationRuleDetailTemp_block
                      .conditionType !== 'EMPTY'
                  ">
                  <el-col :span="4" class="dialogTitle">条件设置：</el-col>
                  <el-col :span="20">
                    <el-input style="width: 100%" v-model.trim="
                        propertyRuleInfo.enumerationRuleDetailTemp_block.value
                      " @input="modifyValue3" :disabled="
                        !propertyRuleInfo.enumerationRuleDetailTemp_block
                          .propertyId
                      " placeholder="请输入条件设置，使用,隔开" />
                  </el-col>
                </el-row>
                <!--前置条件-枚举 end-->
                <!--枚举-其他-->
                <el-row :span="24" v-show="
                    propertyRuleInfo.enumerationRuleDetailTemp_block
                      .requiredType !== 'BLANK'
                  ">
                  <el-col :span="4" class="dialogTitle">属性示例：</el-col>
                  <el-col :span="20">
                    <el-input v-model.trim="
                        propertyRuleInfo.enumerationRuleDetailTemp_block.remark
                      " style="width: 100%" placeholder="请输入属性示例" />
                  </el-col>
                </el-row>
                <el-row :span="24">
                  <el-col :span="4" class="dialogTitle">是否必填：</el-col>
                  <el-col :span="8">
                    <el-select v-model="
                        propertyRuleInfo.enumerationRuleDetailTemp_block
                          .requiredType
                      " placeholder="请选择" :disabled="isMustDisabled">
                      <el-option v-for="item in requiredTypeOptions" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <!--枚举-其他 end-->
                <!--枚举值-->
                <div v-show="
                    propertyRuleInfo.enumerationRuleDetailTemp_block
                      .requiredType !== 'BLANK'
                  ">
                  <el-row :span="24">
                    <el-col :span="4" class="dialogTitle">枚举值：</el-col>
                    <el-col :span="20" v-if="
                        propertyRuleInfo.enumerationRuleDetailTemp_block
                          .enumInfo
                      ">
                      <el-button size="small" style="margin-bottom: 8px" class="item-btn" v-for="(
                          item, index
                        ) in propertyRuleInfo.enumerationRuleDetailTemp_block.enumInfo.split(
                          ','
                        )" :item="item" :key="index">{{ item }}
                        <i @click="delEnumerationItem(index)" class="item-close el-icon-error"></i>
                      </el-button>
                      <el-button size="small" type="primary" @click="addEnumeration" icon="el-icon-plus"></el-button>
                    </el-col>
                    <el-col :span="20" v-else>
                      <el-button size="small" type="primary" @click="addEnumeration" icon="el-icon-plus"></el-button>
                    </el-col>
                  </el-row>
                  <el-row :span="24" v-show="isAddEnumerationValue">
                    <el-col :span="4" class="dialogTitle">添加枚举值：</el-col>
                    <el-col :span="10">
                      <el-input v-model.trim="enumerationValue" placeholder="请输入枚举值" />
                    </el-col>
                    <el-col :span="8" style="margin-left: 20px">
                      <el-button size="small" @click="addEnumerationCancel">取消</el-button>
                      <el-button size="small" @click="addEnumerationSure" type="primary">确认</el-button>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <!--枚举值 end-->
              <el-row :span="24">
                <el-col :span="24" style="text-align: right">
                  <el-button size="medium" @click="delEnumerationRule_block">删除</el-button>
                  <el-button size="medium" @click="addEnumerationRule_block" style="margin-left: 20px; margin-right: 20px" type="primary">{{
                      radioIndex_block_enumeration !== '' ? '保存' : '添加'
                    }}</el-button>
                </el-col>
              </el-row>
            </div>
          </div>
          <!--字段类型-枚举 end-->

          <!--字段类型-数字-->
          <div v-show="fieldTypeValue == 'NUMBER'" class="numberRuleList">
            <!--添加的数字规则列表-->
            <div class="numberRuleTab" style="padding-bottom: 5px">
              <el-radio-group v-model="radioIndex_block" size="small" @change="tabNumberRule_block">
                <el-radio-button v-for="(item, index) in propertyRuleInfo.numberRuleParams" :item="item" :key="index" :label="index">规则{{ index | numberFormat }}</el-radio-button>
                <el-button type="primary" @click="openAddNumberRule_block" size="mini" icon="el-icon-plus" style="
                    margin-left: 10px;
                    position: relative;
                    top: 5px;
                    display: inline-block;
                    border-radius: 2px;
                  "></el-button>
              </el-radio-group>
            </div>
            <!-- 当前选中规则的详情 -->
            <div class="numberRuleDetail" v-if="propertyRuleInfo.numberRuleDetailTemp_block">
              <!--前置条件-数字-->
              <div style="width: 90%; margin: 15px auto">
                <el-row :span="24">
                  <el-col :span="4" class="dialogTitle">前置属性：</el-col>
                  <el-col :span="8">
                    <el-select v-model="
                        propertyRuleInfo.numberRuleDetailTemp_block.propertyId
                      " placeholder="请选择前置属性">
                      <el-option v-for="item in prePropertyOptions" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="5" class="dialogTitle">判断条件：</el-col>
                  <el-col :span="7">
                    <el-select v-model="
                        propertyRuleInfo.numberRuleDetailTemp_block
                          .conditionType
                      " :disabled="
                        !propertyRuleInfo.numberRuleDetailTemp_block.propertyId
                      " placeholder="请选择判断条件">
                      <el-option v-for="item in judgeOptions" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
                <el-row :span="24" v-show="
                    propertyRuleInfo.numberRuleDetailTemp_block
                      .conditionType !== 'EMPTY'
                  ">
                  <el-col :span="4" class="dialogTitle">条件设置：</el-col>
                  <el-col :span="20">
                    <el-input style="width: 100%" v-model.trim="
                        propertyRuleInfo.numberRuleDetailTemp_block.value
                      " @input="modifyValue2" :disabled="
                        !propertyRuleInfo.numberRuleDetailTemp_block.propertyId
                      " placeholder="请输入条件设置，使用,隔开" />
                  </el-col>
                </el-row>
                <el-row :span="24" v-show="
                    propertyRuleInfo.numberRuleDetailTemp_block.requiredType !==
                    'BLANK'
                  ">
                  <el-col :span="4" class="dialogTitle">属性示例：</el-col>
                  <el-col :span="20">
                    <el-input v-model.trim="
                        propertyRuleInfo.numberRuleDetailTemp_block.remark
                      " placeholder="请输入属性示例" />
                  </el-col>
                </el-row>
                <el-row :span="24">
                  <el-col :span="4" class="dialogTitle">是否必填：</el-col>
                  <el-col :span="8">
                    <el-select v-model="
                        propertyRuleInfo.numberRuleDetailTemp_block.requiredType
                      " placeholder="请选择" :disabled="isMustDisabled">
                      <el-option v-for="item in requiredTypeOptions" :key="item.id" :label="item.name" :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                </el-row>
              </div>
              <!--前置条件-数字 end-->
              <!--必空时不需要填写 -->
              <div v-show="
                  propertyRuleInfo.numberRuleDetailTemp_block.requiredType !==
                  'BLANK'
                ">
                <el-table :data="
                    propertyRuleInfo.numberRuleDetailTemp_block.numberRuleList
                  " border fit highlight-current-row style="width: 90%; margin: 15px auto">
                  <el-table-column label="数字类型" align="center" prop="type">
                    <template slot-scope="scope">
                      {{ scope.row.numberType }}
                    </template>
                  </el-table-column>
                  <el-table-column label="数字精度" align="center" prop="accuracy">
                    <template slot-scope="scope">
                      {{
                        scope.row.numericalPrecision === '0'
                          ? '整数'
                          : scope.row.numericalPrecision == 1
                          ? '一位小数'
                          : scope.row.numericalPrecision == 2
                          ? '二位小数'
                          : scope.row.numericalPrecision == 3
                          ? '三位小数'
                          : scope.row.numericalPrecision == 4
                          ? '四位小数'
                          : scope.row.numericalPrecision == 5
                          ? '五位小数'
                          : '不限制'
                      }}
                    </template>
                  </el-table-column>
                  <el-table-column label="数字合理范围" align="center" prop="numberRange"></el-table-column>
                  <el-table-column label="前置单位" align="center" prop="prefixUnit">
                    <template slot-scope="scope">
                      {{ scope.row.prefixUnit }}
                    </template>
                  </el-table-column>
                  <el-table-column label="后置单位" align="center" prop="suffixUnit">
                    <template slot-scope="scope">
                      {{ scope.row.suffixUnit }}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                      <el-button type="text" @click="editNumberRule_row(scope.$index, scope.row)">编辑</el-button>
                      <el-button type="text" @click="delNumberRule_row(scope.$index, scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--添加的数字规则列表 end-->
                <el-row :span="24">
                  <el-col :span="24" class="dialogTitle" style="
                      text-align: left;
                      box-sizing: border-box;
                      padding-left: 40px;
                    ">
                    <el-button @click="openAddNumberRule_row" type="primary" icon="el-icon-plus" circle></el-button>
                  </el-col>
                </el-row>
                <!--组间连接符添加-->
                <div class="joinbox">
                  <el-row :span="24">
                    <el-col :span="4" :offset="1" class="dialogTitle">组间连接符：</el-col>
                    <el-col :span="18">
                      <el-button size="small" style="margin-bottom: 8px" class="item-btn" v-for="(item, index) in propertyRuleInfo
                          .numberRuleDetailTemp_block.numberSeparator &&
                        propertyRuleInfo.numberRuleDetailTemp_block.numberSeparator.split(
                          '|'
                        )" :item="item" :key="index">{{ item }}
                        <i @click="delJoinMarkItem(index)" class="item-close el-icon-error"></i>
                      </el-button>
                      <el-button size="mini" type="primary" @click="openJoinMark" icon="el-icon-plus" style="border-radius: 2px"></el-button>
                    </el-col>
                  </el-row>
                  <el-row :span="24" v-show="isAddJoinMarkValue">
                    <el-col :span="6" class="dialogTitle">添加连接符：</el-col>
                    <el-col :span="6">
                      <el-input v-model.trim="joinMarkValue" placeholder="请输入连接符" />
                    </el-col>
                    <el-col :span="6" style="margin-left: 20px">
                      <el-button size="small" @click="addJoinMarkCancel">取消</el-button>
                      <el-button size="small" @click="addJoinMarkSure" type="primary">确认</el-button>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <!--必空时不需要填写 end-->
              <!--级差设置-->
              <div style="width: 90%; margin: 0 auto">
                <el-row :span="24">
                  <el-col :span="4" class="dialogTitle" style="text-align: center">级差开关：</el-col>
                  <el-col :span="8" style="padding: 8px 0">
                    <el-radio v-model="
                        propertyRuleInfo.numberRuleDetailTemp_block.levelOnFlag
                      " :label="true">开启</el-radio>
                    <el-radio v-model="
                        propertyRuleInfo.numberRuleDetailTemp_block.levelOnFlag
                      " :label="false">关闭</el-radio>
                  </el-col>
                </el-row>
                <div v-if="propertyRuleInfo.numberRuleDetailTemp_block.levelOnFlag" style="
                    background: #f0f3fa;
                    padding: 10px;
                    padding-bottom: 0;
                    margin-bottom: 10px;
                  ">
                  <el-row style="margin-bottom: 0" :span="24" v-for="(item, index) in propertyRuleInfo
                      .numberRuleDetailTemp_block.levelConfigDTOS" :item="item" :key="index">
                    <el-col :span="7">
                      <el-row :span="24">
                        <el-col :span="12" class="dialogTitle" style="font-size: 12px">低级单位：</el-col>
                        <el-col :span="12">
                          <el-input v-model="item.lowUnit" type="text" />
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="7">
                      <el-row :span="24">
                        <el-col :span="12" class="dialogTitle" style="font-size: 12px">高级单位：</el-col>
                        <el-col :span="12">
                          <el-input v-model="item.highUnit" type="text" />
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="7">
                      <el-row :span="24">
                        <el-col :span="10" class="dialogTitle" style="font-size: 12px">级差：</el-col>
                        <el-col :span="14">
                          <el-select clearable style="width: 100%" v-model="item.stageEnum" placeholder="请选择">
                            <el-option v-for="item in levelOptions" :key="item.code" :label="item.name" :value="item.code">
                            </el-option>
                          </el-select>
                        </el-col>
                      </el-row>
                    </el-col>
                    <el-col :span="2" :offset="1">
                      <el-button @click="deleteLevel(index)" v-if="
                          index !==
                          propertyRuleInfo.numberRuleDetailTemp_block
                            .levelConfigDTOS.length -
                            1
                        " icon="el-icon-delete" type="danger"></el-button>
                      <el-button @click="addLevel(index)" v-else icon="el-icon-plus" type="primary"></el-button>
                    </el-col>
                  </el-row>
                </div>
              </div>
              <!--级差设置 end-->
              <el-row :span="24">
                <el-col :span="24" style="text-align: right">
                  <el-button size="medium" @click="delNumberRule_block">删除</el-button>
                  <el-button size="medium" @click="addNumberRule_block" style="margin-left: 20px; margin-right: 20px" type="primary">{{ radioIndex_block !== '' ? '保存' : '添加' }}</el-button>
                </el-col>
              </el-row>
            </div>
          </div>
          <!--字段类型-数字 end-->
        </div>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right">
            <el-button size="medium" @click="closeAddPropertyRule" style="margin-left: 20px">关闭</el-button>
            <el-button size="medium" @click="storeAddPropertyRule" type="primary">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--添加修改属性规则 end-->
    <!--给数字规则段添加行数字规则-->
    <el-dialog width="780px" title="添加数字规则" class="dialogClass" :visible.sync="showNumberDialog" :close-on-click-modal="false">
      <div class="numberUnit">
        <div class="numberUnitForm">
          <el-row :span="24" :gutter="15">
            <el-col :span="4" class="dialogTitle">数字类型：</el-col>
            <el-col :span="7">
              <el-select v-model="numberTypeValue" placeholder="请选择数字类型" style="width:100%">
                <el-option v-for="item in numberTypeOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4" class="dialogTitle">数字精度：</el-col>
            <el-col :span="7">
              <el-select v-model="numberAccuracyValue" placeholder="请选择数字精度" style="width:100%">
                <el-option v-for="item in numberAccuracyOptions" :key="item.value" :label="item.label" :value="item.value">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
          <el-row :span="24"  :gutter="15">
            <el-col :span="4" class="dialogTitle">数字合理范围：</el-col>
            <el-col :span="3">
              <number-input  v-model="rangeLeft"></number-input>
            </el-col>
            <el-col :span="1" style="height:32px;display:flex;align-items:center;justify-content:center;">~</el-col>
            <el-col :span="3">
              <number-input  v-model="rangeRight"></number-input>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="4" class="dialogTitle">前置单位：</el-col>
            <el-col :span="18">
              <el-button size="small" class="item-btn" v-for="(item, index) in propertyRuleInfo
                  .numberRuleDetailTemp_row.prefixUnit &&
                propertyRuleInfo.numberRuleDetailTemp_row.prefixUnit.split(',')" :item="item" :key="index">{{ item }}
                <i @click="delPrefixUnit(index)" class="item-close el-icon-error"></i>
              </el-button>
              <el-button size="small" type="primary" @click="openNumberBeforeUnits" icon="el-icon-plus"></el-button>
            </el-col>
          </el-row>
          <el-row :span="24" v-show="isAddBeforeUnitsValue">
            <el-col :span="4" class="dialogTitle">添加单位：</el-col>
            <el-col :span="6">
              <el-input v-model.trim="numberUnitsValue" placeholder="请输入前置单位" />
            </el-col>
            <el-col :span="6" style="margin-left: 20px">
              <el-button size="small" @click="closeAddNumberBeforeUnits">取消</el-button>
              <el-button size="small" @click="storeAddNumberBeforeUnits" type="primary">确认</el-button>
            </el-col>
          </el-row>
          <el-row :span="24">
            <el-col :span="4" class="dialogTitle">后置单位：</el-col>
            <el-col :span="18">
              <el-button size="small" class="item-btn" v-for="(item, index) in propertyRuleInfo
                  .numberRuleDetailTemp_row.suffixUnit &&
                propertyRuleInfo.numberRuleDetailTemp_row.suffixUnit.split(',')" :item="item" :key="index">{{ item }}
                <i @click="delSuffixUnit(index)" class="item-close el-icon-error"></i>
              </el-button>
              <el-button size="small" type="primary" @click="openAddNumberAfterUnits" icon="el-icon-plus"></el-button>
            </el-col>
          </el-row>
          <el-row :span="24" v-show="isAddAfterUnitsValue">
            <el-col :span="4" class="dialogTitle">添加单位：</el-col>
            <el-col :span="6">
              <el-input v-model.trim="numberUnitsValue" placeholder="请输入后置单位" />
            </el-col>
            <el-col :span="5" style="margin-left: 20px">
              <el-button size="small" @click="closeAddNumberAfterUnits">取消</el-button>
              <el-button size="small" @click="storeAddNumberAfterUnits" type="primary">确认</el-button>
            </el-col>
          </el-row>
        </div>
        <el-row :span="24">
          <el-col :span="24" class="dialogTitle">
            <el-button size="small" @click="closeAddNumberRule_row">取消</el-button>
            <el-button size="small" @click="storeAddNumberRule_row" type="primary">保存</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--给数字规则段添加行数字规则 end-->
    <!--属性规则描述弹出框-->
    <PropertyRuleDetail ref="PropertyRuleDetail" :propertyList="templateList"></PropertyRuleDetail>
    <!--属性规则描述弹出框 end-->
  </div>
</template>

<script>
import Sortable from 'sortablejs'
import {
  getPropertiesRuleList,
  addProperties,
  delProperties,
  getPropertyRule,
  modifyPropertyRule,
  createFinalDraft,
  adjustPropertiesOrder
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
// import Tree from './common/tree'
import PropertyRuleDetail from '../../common/propertyRuleDetail'
import { mapState } from 'vuex'
import { findButton } from '../../common/untils/tools'

let numberRuleIndexRow = '' // 当前修改的是规则段里面的规则行的index,为空表示是新增规则行
export default {
  inject: ['category'],
  name: 'propertyRule',
  props: {
    brandId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: {
        // page: true,
        table: true
      },
      listQueryInfo: {
        current: 1,
        pageSize: 200
      },
      // treeKind: 'propertyRule',
      // currCategory: null, // 当前选中类目信息
      currPropertyid: null, // 当前修改的属性id
      currPropertyidIndex: null, // 当前修改的属性id的index
      currPropertyerror: null, // 当前修改的属性的error
      templateList: [], // 属性规则列表
      total: 0,
      ruleLoading: false,
      showRuleDialog: false,
      showNumberDialog: false,
      ismustValue: true, // 是否必填
      isOpenValue: true, // 是否开启检测
      isMoreValue: true, // 枚举值是否多选
      enumerationValue: '', // 添加的枚举值（临时存储）
      joinMarkValue: '', // 添加的组间连接符（临时存储）
      numberUnitsValue: '', // 添加的数字单位（临时存储）
      numberBreakValue: '*', // 组间分隔符（固定不变的）
      isAddEnumerationValue: false, // 是否添加枚举值
      isAddJoinMarkValue: false, // 是否添加组间连接符
      isAddBeforeUnitsValue: false, // 是否添加数字前置单位
      isAddAfterUnitsValue: false, // 是否添加数字后置单位
      ruleState: '1', // 1 添加属性规则 2 编辑属性规则
      isModifyState: 0, // 0 默认 1 修改属性 2 添加属性
      propertyTypeOptions: [
        {
          value: 'MAIN',
          label: '关键属性'
        },
        {
          value: 'SALE',
          label: '销售属性'
        },
        {
          value: 'NORMAL',
          label: '一般属性'
        },
        {
          value: 'BLANK',
          label: '空'
        }
      ],
      propertyTypeValue: '',
      fieldTypeOptions: [
        // {
        //   value: 'NO_LIMIT',
        //   label: '不校验'
        // },
        {
          value: 'TEXT',
          label: '纯文本'
        },
        {
          value: 'SINGLE',
          label: '单选枚举值'
        },
        {
          value: 'MULTI',
          label: '多选枚举值'
        },
        {
          value: 'NUMBER',
          label: '数字'
        }
      ],
      fieldTypeValue: '',
      numberTypeOptions: [
        {
          // 数字类型 1 数字 2 分数 3 数字范围 4 比较数字 // key是汉字是因为接口需要兼容excel导入，excel导入导入的都是汉字；所以为了统一接口
          value: '全部',
          label: '全部'
        },
        {
          value: '数字',
          label: '数字'
        },
        {
          value: '分数',
          label: '分数'
        },
        {
          value: '数字范围',
          label: '数字范围'
        },
        {
          value: '比较数字',
          label: '比较数字'
        },
        {
          value: '数学公式',
          label: '数学公式'
        }
      ],
      numberTypeValue: '',
      numberAccuracyOptions: [
        {
          value: '',
          label: '不限制'
        },
        {
          value: '0',
          label: '整数'
        },
        {
          value: '1',
          label: '一位小数'
        },
        {
          value: '2',
          label: '两位小数'
        },
        {
          value: '3',
          label: '三位小数'
        },
        {
          value: '4',
          label: '四位小数'
        },
        {
          value: '5',
          label: '五位小数'
        }
      ],
      numberAccuracyValue: '',
      numberUnitsTemp: ['cm', 'mm', 'km'], // 临时储存的单位
      prePropertyOptions: [], // 属性选项，编辑的时候要排除掉当前属性值
      judgeOptions: [
        // 判断条件选项
        {
          id: 'COMPVALUE',
          name: '值大小'
        },
        {
          id: 'ENUM',
          name: '枚举'
        },
        {
          id: 'CONTAINS',
          name: '包含'
        },
        {
          id: 'EMPTY',
          name: '空值判断'
        },
        {
          id: 'NEGATION',
          name: '非'
        }
      ],
      requiredTypeOptions: [
        // 是否必填选项
        {
          id: 'NOVALIDATE',
          name: '否'
        },
        {
          id: 'REQUIRED',
          name: '是'
        }
      ],
      radioIndex_block: '', // 数字规则列表中选择的值的index;'' 一个都没有选择;其他值则是对应规则的index
      radioIndex_block_enumeration: '', // 枚举规则列表中选择的值的index;'' 一个都没有选择;其他值则是对应规则的index
      propertyRuleInfo: {
        // 当前属性规则信息
        name: '颜色', // 属性名称
        ismust: '1', // 1 是 2 否
        propertyType: 'MAIN', // 属性类型
        fieldType: '', // 字段类型
        demo: '1~2cm', // 属性示例
        show: false, // 是否展示
        ifFbp: false,
        ifZbp: false,
        writeDesc: '',
        sourcing: false, // 是否sourcing选型
        selection: true, // 是否选型
        remark: '', // 属性备注
        propertyId: null, // 前置条件属性id
        conditionType: '', // 前置条件类型
        value: '', // 前置条件值
        enumeration: {
          // 枚举字段
          multiple: '1', // 是否多选  1 多选 2 单选
          items: ['红', '橙', '黄', '绿'] // 枚举值
        },
        currNumberRule: 0, // 段位置  按照段index往下走
        numberRuleDetailTemp_row: {
          // 当前显示的数字规则段里面的一条规则行的信息（临时存储,添加修改使用）
          numberType: '',
          numericalPrecision: '',
          prefixUnit: '',
          suffixUnit: ''
        },
        numberRuleDetailTemp_block: null, // 当前显示的数字规则段详情（临时存储，添加修改使用）
        numberRuleDetailTemp_block_demo: {
          // 数字一条规则模型
          numberSeparator: '', // 数字规则链接符
          remark: '', // 添加的数字规则备注
          propertyId: null, // 前置属性id
          conditionType: '', // 前置条件类型
          value: '', // 前置条件值
          requiredType: '', // 是否必填
          numberRuleList: [] // 数字规则的多条规则列表
        },
        numberRuleParams: [], // 字段类型为“数字”时的规则列表
        enumerationRuleDetailTemp_block: null, // 当前显示的枚举规则项详情（临时存储，添加修改使用）
        enumerationRuleDetailTemp_block_demo: {
          // 枚举一条规则模型
          propertyId: null, // 前置属性id
          conditionType: '', // 前置属性判断条件
          value: '', // 前置属性值
          enumInfo: '', // 枚举值
          requiredType: '', // 是否必填
          remark: '' // 备注
        },
        enumerationRuleParams: [] // 字段类型为“枚举”时的规则列表
      },
      // categories: [],
      defaultProps: {
        children: 'catalogueTreeVOList',
        code: 'code',
        id: 'id',
        label: 'name',
        level: 'level'
      },
      levelOptions: [
        {
          name: '千级',
          code: 'THOUSAND'
        },
        {
          name: '百级',
          code: 'HUANDRED'
        },
        {
          name: '十级',
          code: 'TEN'
        }
      ],
      rangeLeft: null,
      rangeRight: null
    }
  },
  components: {
    Pagination,
    // Tree,
    PropertyRuleDetail,
    NumberInput: () => import('../../compontents/numberInput.vue')
  },
  filters: {
    categoriesFormat(value) {
      return value.split('/').join(' / ')
    },
    numberFormat(value) {
      let num = ''
      switch (value) {
        case 0:
          num = '一'
          break
        case 1:
          num = '二'
          break
        case 2:
          num = '三'
          break
        case 3:
          num = '四'
          break
        case 4:
          num = '五'
          break
        case 5:
          num = '六'
          break
        case 6:
          num = '七'
          break
        case 7:
          num = '八'
          break
        case 8:
          num = '九'
          break
        case 9:
          num = '十'
          break
        default:
          num = value + 1
      }
      return num
    },
    booleanFormat(value) {
      return value ? '是' : '否'
    }
  },
  watch: {
    modelIsMust: function () {
      this.reWriteIsMust()
    },
    brandId: {
      handler() {
        this.getPropertiesRuleList()
      },
      immediate: true
    }
  },
  created() { },
  computed: {
    ...mapState(['menu']),
    isEnumType() {
      return function (type) {
        // ENUM_TYPE旧枚举类型，兼容旧数据
        return ['SINGLE', 'MULTI', 'ENUM_TYPE'].includes(type)
      }
    },
    isMustDisabled() {
      const { selection = false, ifFbp = false, ifZbp } = this.propertyRuleInfo
      return selection || ifFbp || ifZbp
    },
    modelIsMust() {
      const { selection = false, ifFbp = false, ifZbp = false } = this.propertyRuleInfo
      return selection || ifFbp || ifZbp
    },
    currCategory() {
      return this.category?.currCategory || {}
    },
    addPropertyButton() {
      return findButton(
      this.menu,
      '/dataManage/propertyRule',
      '添加属性及规则'
    )
    },
    createFinalDraftButton() {
      return findButton(
      this.menu,
      '/dataManage/propertyRule',
      '生成定稿'
    )
    },
    editPropertyButton() {
      return findButton(
      this.menu,
      '/dataManage/propertyRule',
      '属性编辑'
    )
    },
    deletePropertyButton() {
      return findButton(
      this.menu,
      '/dataManage/propertyRule',
      '属性删除'
    )
    }
    // modelCurrCategory() {
    //   return JSON.stringify(this.category?.currCategory)
    // }
  },
  mounted() {
  },
  methods: {
    reWriteIsMust() {
      let newVal = this.modelIsMust
      const fetch = {
        'TEXT': () => {
          this.ismustValue = newVal || this.ismustValue
        },
        'NUMBER': () => {
          if (!newVal) return
          this.propertyRuleInfo.numberRuleParams && (this.propertyRuleInfo.numberRuleParams = this.propertyRuleInfo.numberRuleParams.map(item => ({
            ...item,
            requiredType: 'REQUIRED'
          })))
          this.propertyRuleInfo.numberRuleDetailTemp_block && (this.propertyRuleInfo.numberRuleDetailTemp_block.requiredType = 'REQUIRED')
        },
        'ENUM': () => {
          if (!newVal) return
          this.propertyRuleInfo.enumerationRuleParams && (this.propertyRuleInfo.enumerationRuleParams = this.propertyRuleInfo.enumerationRuleParams.map(item => ({
            ...item,
            requiredType: 'REQUIRED'
          })))
          this.propertyRuleInfo.enumerationRuleDetailTemp_block && (this.propertyRuleInfo.enumerationRuleDetailTemp_block.requiredType = 'REQUIRED')
        }
      }
      let fetchType = ['SINGLE', 'MULTI'].includes(this.fieldTypeValue) ? 'ENUM' : this.fieldTypeValue
      fetch[fetchType] && fetch[fetchType]()
    },
    deleteLevel(index) {
      // 删除
      let levelConfigDTOS = JSON.parse(
        JSON.stringify(
          this.propertyRuleInfo.numberRuleDetailTemp_block.levelConfigDTOS
        )
      )
      levelConfigDTOS.splice(index, 1)
      this.propertyRuleInfo.numberRuleDetailTemp_block.levelConfigDTOS =
        levelConfigDTOS
      // this.$set(this.propertyRuleInfo.numberRuleDetailTemp_block, 'levelConfigDTOS', levelConfigDTOS)
      this.$forceUpdate()
    },
    addLevel(index) {
      // 添加
      let level = {
        lowUnit: '',
        highUnit: '',
        stageEnum: ''
      }
      let levelConfigDTOS = JSON.parse(
        JSON.stringify(
          this.propertyRuleInfo.numberRuleDetailTemp_block.levelConfigDTOS
        )
      )
      levelConfigDTOS.push(level)
      this.propertyRuleInfo.numberRuleDetailTemp_block.levelConfigDTOS =
        levelConfigDTOS
      this.$forceUpdate()
    },
    // 行拖拽
    rowDrop() {
      // 此时找到的元素是要拖拽元素的父容器
      // 首先获取需要拖拽的dom节点
      let el = this.$el && this.$el
        .querySelectorAll('.el-table__fixed-right')[0]
      if (!el) {
        el = this.$el
          .querySelectorAll('.el-table__body-wrapper')[0]
      }
      if (!el) {
        return
      }
      el = el
        .querySelectorAll('table > tbody')[0];
      if (!el) {
        return
      }
      const _this = this
      Sortable.create(el, {
        //  指定父元素下可被拖拽的子元素
        disabled: false, // 是否开启拖拽
        ghostClass: 'sortable-ghost', // 拖拽样式
        animation: 150, // 拖拽延时，效果更好看
        group: {
          // 是否开启跨表拖拽
          pull: false,
          put: false
        },
        onEnd({ newIndex, oldIndex }) {
          console.log('newIndex=' + newIndex)
          console.log('oldIndex=' + oldIndex)
          const currRow = _this.templateList.splice(oldIndex, 1)[0]
          _this.templateList.splice(newIndex, 0, currRow)
          // let obj = {}
          // _this.templateList.forEach((item, index) => {
          //   obj[item.id] = index
          // })
          // console.log(obj, _this.templateList)
          adjustPropertiesOrder({
            attrIds: _this.templateList.map(item => item.id || ''),
            categoryId: _this.currCategory?.id || '',
            brandId: _this.brandId
          }).then((res) => {
            console.log(res)
            const { success = false, msg = '' } = res
            if (success) {
              _this.$message.success('修改成功')
            } else {
              _this.$message.error(msg || '修改失败')
            }
          }).finally(() => {
            _this.getPropertiesRuleList()
          })
        }
      })
    },
    // treeLoad() {
    //   this.$refs.tree.reload()
    // },
    prePropertyChange(e) {
      if (!e) {
        this.propertyRuleInfo.conditionType = ''
        this.propertyRuleInfo.value = ''
      }
    },
    modifyValue3() {
      /**
       * 为了兼容中英文逗号(，,)全部转为英文,
       * 为了兼容输入法的大于（＞）小于（＜）和键盘的大于（>）小于（<）;全部转换为输入法的大于（＞）小于（＜）
       * **/
      this.propertyRuleInfo.enumerationRuleDetailTemp_block.value =
        this.propertyRuleInfo.enumerationRuleDetailTemp_block.value.replace(
          /，/g,
          ','
        )
      this.propertyRuleInfo.enumerationRuleDetailTemp_block.value =
        this.propertyRuleInfo.enumerationRuleDetailTemp_block.value.replace(
          />/g,
          '＞'
        )
      this.propertyRuleInfo.enumerationRuleDetailTemp_block.value =
        this.propertyRuleInfo.enumerationRuleDetailTemp_block.value.replace(
          /</g,
          '＜'
        )
    },
    modifyValue2() {
      /**
       * 为了兼容中英文逗号(，,)全部转为英文,
       * 为了兼容输入法的大于（＞）小于（＜）和键盘的大于（>）小于（<）;全部转换为输入法的大于（＞）小于（＜）
       * **/
      this.propertyRuleInfo.numberRuleDetailTemp_block.value =
        this.propertyRuleInfo.numberRuleDetailTemp_block.value.replace(
          /，/g,
          ','
        )
      this.propertyRuleInfo.numberRuleDetailTemp_block.value =
        this.propertyRuleInfo.numberRuleDetailTemp_block.value.replace(
          />/g,
          '＞'
        )
      this.propertyRuleInfo.numberRuleDetailTemp_block.value =
        this.propertyRuleInfo.numberRuleDetailTemp_block.value.replace(
          /</g,
          '＜'
        )
    },
    modifyValue1() {
      /**
       * 为了兼容中英文逗号(，,)全部转为英文,
       * 为了兼容输入法的大于（＞）小于（＜）和键盘的大于（>）小于（<）;全部转换为输入法的大于（＞）小于（＜）
       * **/
      this.propertyRuleInfo.value = this.propertyRuleInfo.value.replace(
        /，/g,
        ','
      )
      this.propertyRuleInfo.value = this.propertyRuleInfo.value.replace(
        />/g,
        '＞'
      )
      this.propertyRuleInfo.value = this.propertyRuleInfo.value.replace(
        /</g,
        '＜'
      )
    },
    pageClick(page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.getPropertiesRuleList()
    },
    openAddPropertyRule() {
      // 【打开】添加属性规则form
      let arr = {
        id: null,
        name: '无'
      }
      let templateList = JSON.parse(JSON.stringify(this.templateList))
      templateList.unshift(arr)
      this.prePropertyOptions = templateList
      this.showRuleDialog = true
      this.isModifyState = '2'
      this.dataInit()
    },
    closeAddPropertyRule() {
      // 【关闭】属性添加form
      this.showRuleDialog = false
    },
    storeAddPropertyRule() {
      // 【保存】属性添加
      let This = this
      if (!this.propertyRuleInfo.name) {
        this.$message.error('请添加属性名称')
        return false
      }
      if (!this.propertyTypeValue) {
        this.$message.error('请选择属性类型')
        return false
      }
      if (!this.fieldTypeValue) {
        this.$message.error('请选择字段类型')
        return false
      }
      let ismustValue = this.ismustValue
      // 前置条件逻辑判断
      let propertyRuleInfo = this.propertyRuleInfo
      let detail = JSON.parse(JSON.stringify(propertyRuleInfo))
      if (
        this.fieldTypeValue === 'NO_LIMIT' ||
        this.fieldTypeValue === 'TEXT'
      ) {
        // 纯文本 不限制是做这些判断
        if (!detail.propertyId) {
          // 前置属性为无时
          detail.propertyId = null
          detail.value = ''
          detail.conditionType = null // 应接口要求，当前值条件选项为空时，给传一个默认值null
        } else {
          if (!detail.conditionType) {
            // 没有选择判断条件的时候
            this.$message.error('请选择判断条件')
            return false
          } else {
            if (detail.conditionType === 'EMPTY') {
              // 判断条件是“空值判断”时，条件设置值必须为空
              detail.value = ''
            } else {
              // 判断条件存在且是非空值判断时，条件设置必须有值
              if (!detail.value) {
                this.$message.error('请输入条件设置')
                return false
              } else {
                if (detail.conditionType === 'COMPVALUE') {
                  // 判断条件是"值大小"时，输入条件必须满足以下正则
                  let judgeValueArr = detail.value.split(',')
                  let isAllTrue = true // 所有项都满足正则条件
                  let reg = /^[＞＜≥≤]-?\d+.*$/
                  judgeValueArr.forEach((item, index) => {
                    if (!reg.test(item)) {
                      isAllTrue = false
                    }
                  })
                  if (!isAllTrue) {
                    this.$message.error('条件设置格式不正确')
                    return false
                  }
                } else if (
                  detail.conditionType === 'CONTAINS' ||
                  detail.conditionType === 'ENUM' ||
                  detail.conditionType === 'NEGATION'
                ) {
                  // 判断条件是"枚举"、"包含"时，输入条件必须满足以下正则
                  let judgeValue = detail.value
                  let reg = /^.*[^,]$/
                  if (!reg.test(judgeValue)) {
                    this.$message.error('条件设置格式不正确')
                    return false
                  }
                }
              }
            }
          }
        }
      }
      let numberRuleList = this.propertyRuleInfo.numberRuleParams
      if (!this.currCategory.id) {
        this.$message.error('请选择类目')
      }
      let remark = this.propertyRuleInfo.remark
      if (this.fieldTypeValue === 'NUMBER') {
        // 当字段类型是数字的时候，必须有一条数字规则
        if (numberRuleList.length === 0) {
          this.$message.error('请至少添加一条数字规则')
          return false
        }
        // 当字段类型是数字的时候，属性规则是没有(备注/前置条件)的，但是数字类型下面的每一条规则有一个对应的(备注/前置条件)
        remark = ''
        detail.propertyId = null
        detail.conditionType = null // 应接口要求，当前值条件选项为空时，给传一个默认值null
        detail.value = ''
        ismustValue = false // 应接口要求，当“枚举”“数字”时，是否外层的是否必填置为false
      } else {
        numberRuleList = []
      }
      let enumerationRuleList = this.propertyRuleInfo.enumerationRuleParams
      if (this.isEnumType(this.fieldTypeValue)) {
        // 当字段类型是枚举的时候，必须有一条枚举规则
        if (enumerationRuleList.length === 0) {
          this.$message.error('请至少添加一条枚举规则')
          return false
        }
        // 当字段类型是数字的时候，属性规则是没有(备注/前置条件)的，但是数字类型下面的每一条规则有一个对应的(备注/前置条件)
        remark = '' // 备注
        detail.propertyId = null // 前置属性id
        detail.conditionType = null // 应接口要求，当前值条件选项为空时，给传一个默认值null
        detail.value = '' // 前置属性条件设置值
        ismustValue = false // 应接口要求，当“枚举”“数字”时，是否外层的是否必填置为false
      } else {
        enumerationRuleList = []
      }
      let param = {
        categoryId: this.currCategory.id,
        brandId: this.brandId,
        dataType: this.fieldTypeValue,
        enumInfo: this.propertyRuleInfo.enumeration.items.join(','),
        example: this.propertyRuleInfo.demo,
        selection: this.propertyRuleInfo.selection,
        ifFbp: this.propertyRuleInfo.ifFbp,
        ifZbp: this.propertyRuleInfo.ifZbp,
        multipleChoiceFlag: this.isMoreValue,
        name: this.propertyRuleInfo.name,
        multipleEnumInfoDTOList: enumerationRuleList,
        multipleNumberRuleList: numberRuleList,
        required: ismustValue,
        state: this.isOpenValue,
        type: this.propertyTypeValue,
        remark: remark,
        conditionType: detail.conditionType, // 前置条件类型
        propertyId: detail.propertyId, // 前置条件属性id
        value: detail.value, // 前置条件值
        show: this.propertyRuleInfo.show,
        sourcing: this.propertyRuleInfo.sourcing,
        writeDesc: this.propertyRuleInfo.writeDesc
      }
      if (This.isModifyState === '1') {
        // 修改属性规则
        let propertyid = This.currPropertyid
        param.error = This.currPropertyerror
        modifyPropertyRule(propertyid, param).then((res) => {
          console.log(res)
          if (res.code === 0) {
            // 本地模拟修改
            let modifyItem = This.templateList[This.currPropertyidIndex]
            modifyItem.name = This.propertyRuleInfo.name // 属性名字
            modifyItem.state = This.isOpenValue // 是否开启检测
            modifyItem.required = ismustValue // 是否必填
            modifyItem.type = This.propertyTypeValue // 属性类型
            modifyItem.dataType = This.fieldTypeValue // 字段类型
            modifyItem.example = This.propertyRuleInfo.demo // 示例
            modifyItem.selection = This.propertyRuleInfo.selection // 是否选型
            modifyItem.remark = This.propertyRuleInfo.remark // 备注
            modifyItem.propertyId = This.propertyRuleInfo.propertyId // 前置属性id
            modifyItem.conditionType = This.propertyRuleInfo.conditionType // 前置条件类型
            modifyItem.value = This.propertyRuleInfo.value // 前置条件值
            This.templateList.splice(This.currPropertyidIndex, 1, modifyItem)
            This.showRuleDialog = false
            This.getPropertiesRuleList()
          } else {
            This.$message.error(res.msg)
          }
        })
      } else {
        // 新增属性规则
        addProperties(param).then((res) => {
          if (res.code === 0) {
            // 本地模拟添加； 不能模拟添加，会有一系列问题，修改详情没有id,删除后页面不正确
            /***
            let narr = {
              name: This.propertyRuleInfo.name, // 属性名字
              state: This.isOpenValue, // 是否开启检测
              required: This.ismustValue, // 是否必填
              type: This.propertyTypeValue, // 属性类型
              dataType: This.fieldTypeValue, // 字段类型
              example: This.propertyRuleInfo.demo // 示例
            }
            This.templateList.unshift(narr)
             **/
            This.listQueryInfo.current = 1
            This.getPropertiesRuleList()
            This.showRuleDialog = false
          } else {
            This.$message.error(res.msg)
          }
        })
      }
    },
    showRuleDetail(index, row) {
      // 【查看】属性规则详情
      this.currPropertyid = row.id
      this.currPropertyidIndex = index
      this.$refs.PropertyRuleDetail.getPropertyRuleDetail(row.id)
    },
    editPropertyRule(index, row) {
      // 【编辑】属性规则
      console.log(row)
      // 编辑时候前置属性不能选取和当前属性作为前置属性
      let arr = {
        id: null,
        name: '无'
      }
      let templateList = JSON.parse(JSON.stringify(this.templateList))
      // 删除自己（后来说可以选择自己，故先注释了）
      // templateList.splice(index, 1)
      templateList.unshift(arr)
      this.prePropertyOptions = templateList
      this.currPropertyid = row.id
      this.currPropertyerror = row.error
      this.currPropertyidIndex = index
      this.isModifyState = '1'
      this.getPropertyRuleInfo(row.id)
    },
    getPropertyRuleInfo(propertyid) {
      let This = this
      getPropertyRule(propertyid).then((res) => {
        if (res.code === 0) {
          console.log(res.data)
          This.dataInit()
          // 获取详情后的赋值
          this.propertyRuleInfo.numberRuleParams =
            res.data.multipleNumberRuleList
          if (
            this.propertyRuleInfo.numberRuleParams &&
            this.propertyRuleInfo.numberRuleParams.length > 0
          ) {
            // 添加级差默认值
            let numberRuleDetailTempBlock = JSON.parse(
              JSON.stringify(this.propertyRuleInfo.numberRuleParams[0])
            )
            if (
              !numberRuleDetailTempBlock.levelConfigDTOS ||
              (numberRuleDetailTempBlock.levelConfigDTOS &&
                numberRuleDetailTempBlock.levelConfigDTOS.length <= 0)
            ) {
              numberRuleDetailTempBlock.levelConfigDTOS = [
                {
                  lowUnit: '',
                  highUnit: '',
                  stageEnum: ''
                }
              ]
              numberRuleDetailTempBlock.levelOnFlag = false
            }
            this.propertyRuleInfo.numberRuleDetailTemp_block =
              numberRuleDetailTempBlock
            this.radioIndex_block = 0
          }
          this.propertyRuleInfo.enumerationRuleParams =
            res.data.multipleEnumInfoDTOList
          if (
            this.propertyRuleInfo.enumerationRuleParams &&
            this.propertyRuleInfo.enumerationRuleParams.length > 0
          ) {
            this.propertyRuleInfo.enumerationRuleDetailTemp_block =
              this.propertyRuleInfo.enumerationRuleParams[0]
            this.radioIndex_block_enumeration = 0
          }
          if (res.data.dataType) {
            this.fieldTypeValue = res.data.dataType
          }
          if (res.data.enumInfo) {
            this.propertyRuleInfo.enumeration.items =
              res.data.enumInfo.split(',')
          }
          if (res.data.show) {
            this.propertyRuleInfo.show = res.data.show
          }
          if (res.data.sourcing) {
            this.propertyRuleInfo.sourcing = res.data.sourcing
          }
          this.propertyRuleInfo.demo = res.data.example
          this.propertyRuleInfo.selection = res.data.selection
          this.propertyRuleInfo.remark = res.data.remark
          this.propertyRuleInfo.propertyId = res.data.propertyId
          this.propertyRuleInfo.conditionType = res.data.conditionType
          this.propertyRuleInfo.value = res.data.value
          this.propertyRuleInfo.ifFbp = res.data?.ifFbp || false
          this.propertyRuleInfo.ifZbp = res.data?.ifZbp || false
          this.propertyRuleInfo.writeDesc = res.data?.writeDesc || ''
          this.isMoreValue = res.data.multipleChoiceFlag
          this.propertyRuleInfo.name = res.data.name
          this.ismustValue = res.data.required
          this.isOpenValue = res.data.state
          if (res.data.type) {
            this.propertyTypeValue = res.data.type
          }
          this.reWriteIsMust()
          this.showRuleDialog = true
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    dataInit() {
      // 属性添加、编辑之前，先初始化数据，恢复为默认状态
      this.propertyRuleInfo.numberRuleDetailTemp_block = null
      this.propertyRuleInfo.numberRuleParams = []
      this.radioIndex_block = ''
      this.propertyRuleInfo.enumerationRuleDetailTemp_block = null
      this.propertyRuleInfo.enumerationRuleParams = []
      this.radioIndex_block_enumeration = ''
      this.fieldTypeValue = ''
      this.propertyRuleInfo.enumeration.items = []
      this.propertyRuleInfo.demo = ''
      this.propertyRuleInfo.writeDesc = ''
      this.propertyRuleInfo.selection = true
      this.propertyRuleInfo.remark = ''
      this.propertyRuleInfo.propertyId = null
      this.propertyRuleInfo.conditionType = ''
      this.propertyRuleInfo.value = ''
      this.propertyRuleInfo.show = false
      this.propertyRuleInfo.sourcing = false
      this.propertyRuleInfo.ifFbp = false
      this.propertyRuleInfo.ifZbp = false
      this.isMoreValue = true
      this.propertyRuleInfo.name = ''
      this.ismustValue = true
      this.isOpenValue = true
      this.propertyTypeValue = ''
      this.numberUnitsTemp = []
    },
    getRuleDel(index, row) {
      console.log(index, row)
      let This = this
      let id = row.id
      delProperties(id).then((res) => {
        if (res.code === 0) {
          This.templateList.splice(index, 1)
          This.total -= 1
          if (This.templateList.length < 1) {
            This.listQueryInfo.current = 1
            This.getPropertiesRuleList()
          }
          This.$message.success('删除成功')
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    delEnumerationItem(index) {
      let propertyRuleInfo = this.propertyRuleInfo
      let enumInfo =
        propertyRuleInfo.enumerationRuleDetailTemp_block.enumInfo.split(',')
      enumInfo.splice(index, 1)
      propertyRuleInfo.enumerationRuleDetailTemp_block.enumInfo =
        enumInfo.join(',')
      this.propertyRuleInfo = propertyRuleInfo
    },
    delPrefixUnit(index) {
      // 删除前缀
      let prefixUnit = this.propertyRuleInfo.numberRuleDetailTemp_row.prefixUnit
      if (prefixUnit) {
        prefixUnit = prefixUnit.split(',')
        prefixUnit.splice(index, 1)
        prefixUnit = prefixUnit.join(',')
        this.propertyRuleInfo.numberRuleDetailTemp_row.prefixUnit = prefixUnit
      }
    },
    delSuffixUnit(index) {
      // 刪除后缀
      let suffixUnit = this.propertyRuleInfo.numberRuleDetailTemp_row.suffixUnit
      if (suffixUnit) {
        suffixUnit = suffixUnit.split(',')
        suffixUnit.splice(index, 1)
        suffixUnit = suffixUnit.join(',')
        this.propertyRuleInfo.numberRuleDetailTemp_row.suffixUnit = suffixUnit
      }
    },
    addEnumeration() {
      this.isAddEnumerationValue = !this.isAddEnumerationValue
    },
    addEnumerationCancel() {
      this.isAddEnumerationValue = false
    },
    addEnumerationSure() {
      let propertyRuleInfo = this.propertyRuleInfo
      if (this.enumerationValue) {
        if (propertyRuleInfo.enumerationRuleDetailTemp_block.enumInfo) {
          propertyRuleInfo.enumerationRuleDetailTemp_block.enumInfo +=
            ',' + this.enumerationValue
        } else {
          propertyRuleInfo.enumerationRuleDetailTemp_block.enumInfo =
            this.enumerationValue
        }
        this.propertyRuleInfo = propertyRuleInfo
        this.enumerationValue = ''
      }
    },
    openJoinMark() {
      // 调起连接符添加form
      this.isAddJoinMarkValue = !this.isAddJoinMarkValue
      this.joinMarkValue = ''
    },
    addJoinMarkCancel() {
      this.isAddJoinMarkValue = false
      this.joinMarkValue = ''
    },
    addJoinMarkSure() {
      // 添加连接符
      if (this.joinMarkValue) {
        if (this.propertyRuleInfo.numberRuleDetailTemp_block.numberSeparator) {
          this.propertyRuleInfo.numberRuleDetailTemp_block.numberSeparator +=
            '|' + this.joinMarkValue
        } else {
          this.propertyRuleInfo.numberRuleDetailTemp_block.numberSeparator =
            this.joinMarkValue
        }
        this.joinMarkValue = ''
      }
    },
    delJoinMarkItem(index) {
      let numberSeparator = JSON.parse(
        JSON.stringify(
          this.propertyRuleInfo.numberRuleDetailTemp_block.numberSeparator
        )
      )
      if (numberSeparator) {
        numberSeparator = numberSeparator.split('|')
      }
      numberSeparator.splice(index, 1)
      numberSeparator = numberSeparator.join('|')
      this.propertyRuleInfo.numberRuleDetailTemp_block.numberSeparator =
        numberSeparator
    },
    tabNumberRule_block(index) {
      // 【切换显示-段】
      // 深复制
      let numberRuleDetailTempBlock = JSON.parse(
        JSON.stringify(this.propertyRuleInfo.numberRuleParams[index])
      )
      // 添加级差默认值
      if (
        !numberRuleDetailTempBlock.levelConfigDTOS ||
        (numberRuleDetailTempBlock.levelConfigDTOS &&
          numberRuleDetailTempBlock.levelConfigDTOS.length <= 0)
      ) {
        numberRuleDetailTempBlock.levelConfigDTOS = [
          {
            lowUnit: '',
            highUnit: '',
            stageEnum: ''
          }
        ]
        numberRuleDetailTempBlock.levelOnFlag = false
      }
      this.propertyRuleInfo.numberRuleDetailTemp_block =
        numberRuleDetailTempBlock
      this.radioIndex_block = index
      this.isAddJoinMarkValue = false
    },
    openAddNumberRule_block() {
      // 【打开添加-段】新的数字规则段的form
      this.propertyRuleInfo.numberRuleDetailTemp_block = {
        numberSeparator: '', // 数字规则链接符
        remark: '', // 添加的数字规则备注
        propertyId: null, // 前置属性id
        conditionType: '', // 前置条件类型
        value: '', // 前置条件值
        requiredType: 'REQUIRED', // 是否必填
        numberRuleList: [], // 数字规则的多条规则列表
        levelOnFlag: false, // 是否设置级差
        levelConfigDTOS: [
          // 级差list
          {
            lowUnit: '',
            highUnit: '',
            stageEnum: ''
          }
        ]
      }
      this.radioIndex_block = ''
      this.isAddJoinMarkValue = false
    },
    delNumberRule_block() {
      // 【删除-段】一条数字规则段
      let radioIndex = this.radioIndex_block
      let numberRuleParams = this.propertyRuleInfo.numberRuleParams
      if (numberRuleParams[radioIndex]) {
        // 如果数据里有这一条规则段则删除
        this.propertyRuleInfo.numberRuleParams.splice(radioIndex, 1)
        this.radioIndex_block = ''
        this.propertyRuleInfo.numberRuleDetailTemp_block = null
      } else {
        // 如果没有（则证明是新加的的规则段还没有添加到数据里）直接清空即可
        this.propertyRuleInfo.numberRuleDetailTemp_block = null
        this.radioIndex_block = ''
      }
    },
    addNumberRule_block() {
      // 【添加】一条数字规则段
      const detail = JSON.parse(
        JSON.stringify(this.propertyRuleInfo.numberRuleDetailTemp_block)
      )
      let numberRuleParams = this.propertyRuleInfo.numberRuleParams
      let currIndex = this.radioIndex_block
      // 前置条件逻辑判断
      if (!detail.propertyId) {
        // 前置属性为无时
        detail.propertyId = null
        detail.value = ''
        detail.conditionType = null // 应接口要求，当前值条件选项为空时，给传一个默认值null
      } else {
        if (!detail.conditionType) {
          // 没有选择判断条件的时候
          this.$message.error('请选择判断条件')
          return false
        } else {
          if (detail.conditionType === 'EMPTY') {
            // 判断条件是“空值判断”时，条件设置值必须为空
            detail.value = ''
          } else {
            // 判断条件存在且是非空值判断时，条件设置必须有值
            if (!detail.value) {
              this.$message.error('请输入条件设置')
              return false
            } else {
              if (detail.conditionType === 'COMPVALUE') {
                // 判断条件是"值大小"时，输入条件必须满足以下正则
                let judgeValueArr = detail.value.split(',')
                let isAllTrue = true // 所有项都满足正则条件
                let reg = /^[＞＜≥≤]-?\d+.*$/
                judgeValueArr.forEach((item, index) => {
                  if (!reg.test(item)) {
                    isAllTrue = false
                  }
                })
                if (!isAllTrue) {
                  this.$message.error('条件设置格式不正确')
                  return false
                }
              } else if (
                detail.conditionType === 'CONTAINS' ||
                detail.conditionType === 'ENUM' ||
                detail.conditionType === 'NEGATION'
              ) {
                // 判断条件是"枚举"、"包含"时，输入条件必须满足以下正则
                let judgeValue = detail.value
                let reg = /^.*[^,]$/
                if (!reg.test(judgeValue)) {
                  this.$message.error('条件设置格式不正确')
                  return false
                }
              }
            }
          }
        }
      }
      // 必填时 规则必须有值
      if (detail.requiredType === 'REQUIRED') {
        if (detail.numberRuleList.length <= 0) {
          // 如果有规则才能添加
          this.$message.error('请至少添加一条规则')
          return false
        }
        let len = 0
        if (detail.numberSeparator) {
          len = detail.numberSeparator.split('|').length
        }
        if (detail.numberRuleList.length !== len + 1) {
          // 每个组之间都要分配一个组间连接符
          this.$message.error('每两组规则之间必须设置一个(只能一个)组间连接符')
          return false
        }
      }
      // 级差选择处理
      if (detail.levelOnFlag) {
        let levelConfigDTOS = detail.levelConfigDTOS
        let isAllSet = true
        levelConfigDTOS.forEach((item, index) => {
          if (!item.lowUnit || !item.highUnit || !item.stageEnum) {
            isAllSet = false
          }
        })
        if (!isAllSet) {
          this.$message.error('请将级差信息设置完整')
          return false
        }
      } else {
        detail.levelConfigDTOS = []
      }
      if (currIndex !== '') {
        // 修改
        numberRuleParams.splice(currIndex, 1, detail)
      } else {
        // 新增
        numberRuleParams.push(detail)
      }
      this.propertyRuleInfo.numberRuleDetailTemp_block = null
      this.radioIndex_block = ''
    },
    openNumberBeforeUnits() {
      // 打开添加前置单位form
      this.isAddBeforeUnitsValue = !this.isAddBeforeUnitsValue
      this.isAddAfterUnitsValue = false
      this.numberUnitsValue = ''
    },
    closeAddNumberBeforeUnits() {
      // 取消添加前置单位
      this.isAddBeforeUnitsValue = false
    },
    storeAddNumberBeforeUnits() {
      // 确认添加前置单位
      if (this.numberUnitsValue) {
        if (this.propertyRuleInfo.numberRuleDetailTemp_row.prefixUnit) {
          this.propertyRuleInfo.numberRuleDetailTemp_row.prefixUnit +=
            ',' + this.numberUnitsValue
        } else {
          this.propertyRuleInfo.numberRuleDetailTemp_row.prefixUnit =
            this.numberUnitsValue
        }
        this.numberUnitsValue = ''
      }
    },
    openAddNumberAfterUnits() {
      // 打开添加后置单位form
      this.isAddAfterUnitsValue = !this.isAddAfterUnitsValue
      this.isAddBeforeUnitsValue = false
      this.numberUnitsValue = ''
    },
    closeAddNumberAfterUnits() {
      // 取消添加后置单位
      this.isAddAfterUnitsValue = false
    },
    storeAddNumberAfterUnits() {
      // 确认添加后置单位
      if (this.numberUnitsValue) {
        if (this.propertyRuleInfo.numberRuleDetailTemp_row.suffixUnit) {
          this.propertyRuleInfo.numberRuleDetailTemp_row.suffixUnit +=
            ',' + this.numberUnitsValue
        } else {
          this.propertyRuleInfo.numberRuleDetailTemp_row.suffixUnit =
            this.numberUnitsValue
        }
        this.numberUnitsValue = ''
      }
    },
    delNumberRule_row(index) {
      console.log('【删除-行】此行=' + index)
      this.propertyRuleInfo.numberRuleDetailTemp_block.numberRuleList.splice(
        index,
        1
      )
    },
    editNumberRule_row(index) {
      console.log('【编辑打开-行】数字规则段里面的一个数字规则=' + index)
      numberRuleIndexRow = index
      let rowDetail = JSON.parse(
        JSON.stringify(
          this.propertyRuleInfo.numberRuleDetailTemp_block.numberRuleList[index]
        )
      )
      // 编辑
      this.detailInit_row(rowDetail)
    },
    openAddNumberRule_row() {
      console.log('【新增打开-行】-显示数字规则添加form_行')
      // 打开时初始化form
      let rowDetail = {
        numberType: '',
        numericalPrecision: '',
        prefixUnit: '',
        suffixUnit: ''
      }
      numberRuleIndexRow = ''
      // 新增
      this.detailInit_row(rowDetail)
    },
    detailInit_row(rowDetail) {
      // 【初始化-行】每次打开对规则行form按照新增，编辑进行一次初始化
      this.propertyRuleInfo.numberRuleDetailTemp_row = rowDetail
      this.numberTypeValue = rowDetail.numberType
      this.numberAccuracyValue =
        rowDetail.numericalPrecision === '6' ? '' : rowDetail.numericalPrecision
      this.isAddBeforeUnitsValue = false
      this.isAddAfterUnitsValue = false
      this.numberUnitsValue = ''
      this.rangeLeft = !isNaN(parseFloat(rowDetail.rangeLeft)) && isFinite(rowDetail.rangeLeft) ? +rowDetail.rangeLeft : null
      this.rangeRight = !isNaN(parseFloat(rowDetail.rangeRight)) && isFinite(rowDetail.rangeRight) ? +rowDetail.rangeRight : null
      this.showNumberDialog = true
    },
    storeAddNumberRule_row() {
      console.log('【保存-行】新增/修改数字规则_行')
      if (!this.numberTypeValue) {
        this.$message.error('请选择数字类型')
        return false
      }
      this.propertyRuleInfo.numberRuleDetailTemp_row.numberType =
        this.numberTypeValue
      this.propertyRuleInfo.numberRuleDetailTemp_row.numericalPrecision =
        this.numberAccuracyValue
      this.propertyRuleInfo.numberRuleDetailTemp_row.rangeLeft = this.rangeLeft
      this.propertyRuleInfo.numberRuleDetailTemp_row.rangeRight = this.rangeRight
      this.propertyRuleInfo.numberRuleDetailTemp_row.numberRange = `${this.rangeLeft ?? ''} ~ ${this.rangeRight ?? ''}`
      if (numberRuleIndexRow === '') {
        // 新增行规则
        this.propertyRuleInfo.numberRuleDetailTemp_block.numberRuleList.push(
          this.propertyRuleInfo.numberRuleDetailTemp_row
        )
      } else {
        // 修改行规则
        this.propertyRuleInfo.numberRuleDetailTemp_block.numberRuleList.splice(
          numberRuleIndexRow,
          1,
          this.propertyRuleInfo.numberRuleDetailTemp_row
        )
      }
      this.showNumberDialog = false
    },
    closeAddNumberRule_row() {
      console.log('【关闭-行】新增数字规则_行')
      this.showNumberDialog = false
    },
    getPropertiesRuleList() {
      let This = this
      This.loading.table = true
      let currCategory = this.currCategory
      let param = {
        page: This.listQueryInfo.current - 1,
        size: This.listQueryInfo.pageSize,
        categoryId: currCategory.id,
        brandId: This.brandId
      }
      let sort1 = 'order,asc' // id的倒序 asc时正序
      let sort2 = 'id,asc' // id的倒序 asc时正序
      getPropertiesRuleList(param, sort1, sort2).then((res) => {
        console.log(res)
        if (res.code === 0) {
          This.templateList = res.data.content
          This.total = res.data.totalElements
          This.rowDrop()
        } else {
          This.$message.error(res.msg)
        }
      }).finally(() => {
        This.loading.table = false
      })
    },
    goCreateFinalDraft() {
      let This = this
      let currCategory = this.currCategory
      createFinalDraft(currCategory.id).then((res) => {
        console.log(res)
        if (res.code === 0) {
          this.$message.success('操作成功')
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    // ==============枚举规则列表================
    tabEnumerationRule_block(index) {
      // 【切换显示-段】
      // 深复制
      let enumerationRuleParams = JSON.parse(
        JSON.stringify(this.propertyRuleInfo.enumerationRuleParams[index])
      )
      this.propertyRuleInfo.enumerationRuleDetailTemp_block =
        enumerationRuleParams
      this.radioIndex_block_enumeration = index
      this.isAddEnumerationValue = false
    },
    openAddEnumerationRule_block() {
      // 【打开添加-段】新的数字规则段的form
      this.propertyRuleInfo.enumerationRuleDetailTemp_block = {
        propertyId: null, // 前置属性id
        conditionType: '', // 前置属性判断条件
        value: '', // 前置属性值
        enumInfo: '', // 枚举值
        requiredType: 'REQUIRED', // 是否必填
        remark: '' // 备注
      }
      this.radioIndex_block_enumeration = ''
    },
    delEnumerationRule_block() {
      // 【删除-段】一条数字规则段
      let radioIndex = this.radioIndex_block_enumeration
      let enumerationRuleParams = this.propertyRuleInfo.enumerationRuleParams
      if (enumerationRuleParams[radioIndex]) {
        // 如果数据里有这一条规则段则删除
        this.propertyRuleInfo.enumerationRuleParams.splice(radioIndex, 1)
        this.radioIndex_block_enumeration = ''
        this.propertyRuleInfo.enumerationRuleDetailTemp_block = null
      } else {
        // 如果没有（则证明是新加的的规则段还没有添加到数据里）直接清空即可
        this.propertyRuleInfo.enumerationRuleDetailTemp_block = null
        this.radioIndex_block_enumeration = ''
      }
    },
    addEnumerationRule_block() {
      // 【添加】一条枚举规则段
      const detail = this.propertyRuleInfo.enumerationRuleDetailTemp_block
      let enumerationRuleParams = this.propertyRuleInfo.enumerationRuleParams
      let currIndex = this.radioIndex_block_enumeration
      // 前置条件逻辑判断
      if (!detail.propertyId) {
        // 前置属性为无时
        detail.propertyId = null
        detail.value = ''
        detail.conditionType = null // 应接口要求，当前值条件选项为空时，给传一个默认值null
      } else {
        if (!detail.conditionType) {
          // 没有选择判断条件的时候
          this.$message.error('请选择判断条件')
          return false
        } else {
          if (detail.conditionType === 'EMPTY') {
            // 判断条件是“空值判断”时，条件设置值必须为空
            detail.value = ''
          } else {
            // 判断条件存在且是非空值判断时，条件设置必须有值
            if (!detail.value) {
              this.$message.error('请输入条件设置')
              return false
            } else {
              if (detail.conditionType === 'COMPVALUE') {
                // 判断条件是"值大小"时，输入条件必须满足以下正则
                let judgeValueArr = detail.value.split(',')
                let isAllTrue = true // 所有项都满足正则条件
                let reg = /^[＞＜≥≤]-?\d+.*$/
                judgeValueArr.forEach((item, index) => {
                  if (!reg.test(item)) {
                    isAllTrue = false
                  }
                })
                if (!isAllTrue) {
                  this.$message.error('条件设置格式不正确')
                  return false
                }
              } else if (
                detail.conditionType === 'CONTAINS' ||
                detail.conditionType === 'ENUM' ||
                detail.conditionType === 'NEGATION'
              ) {
                // 判断条件是"枚举"、"包含"时，输入条件必须满足以下正则
                let judgeValue = detail.value
                let reg = /^.*[^,]$/
                if (!reg.test(judgeValue)) {
                  this.$message.error('条件设置格式不正确')
                  return false
                }
              }
            }
          }
        }
      }
      // 必填时 枚举值必须有值
      if (detail.requiredType === 'REQUIRED') {
        if (!detail.enumInfo) {
          this.$message.error('请添加枚举值')
          return false
        }
      }
      if (currIndex !== '') {
        // 修改
        enumerationRuleParams.splice(currIndex, 1, detail)
      } else {
        // 新增
        enumerationRuleParams.push(detail)
      }
      this.propertyRuleInfo.enumerationRuleDetailTemp_block = null
      this.radioIndex_block_enumeration = ''
    },
    doLayout() {
      if (this.$refs['propertyRuleTable']) {
        this.$refs['propertyRuleTable'].doLayout()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.data-manage-rule-container {
  display: flex;
  flex-direction: column;
  min-height: 500px;

  .input96 {
    width: 96%;
  }
  .input100 {
    width: 100%;
  }
  .rule-left {
    float: left;
    width:270px;
    .tree_box {
      width: 100%;
      border: 1px solid #eee;
      box-sizing: border-box;
      padding: 20px 10px;
      overflow: auto;
      position: relative;
      .reload {
        position: absolute;
        right: 5px;
        top: 5px;
        font-size: 20px;
        color: #ccc;
        cursor: pointer;
        visibility: visible;
        &:hover {
          color: #b0b0b0;
        }
      }
    }
  }
  .rule-right {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
    .ellipse2 {
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      cursor: default;
      max-height:46px;
    }
  }
  .dialogTitle {
    line-height: 32px;
    text-align: right;
    box-sizing: border-box;
    padding-right: 2px;
  }
  .ruleset {
    .rulesetcont {
      min-height: 400px;
      margin-bottom: 10px;
    }
    .el-row {
      margin-bottom: 10px;
      .item-btn {
        position: relative;
        .item-close {
          position: absolute;
          right:-8px;
          top:-8px;
          font-size: 16px;
        }
      }
    }
  }
  .el-tree-node__content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .numberRuleTab {
    width: 90%;
    min-height: 35px;
    margin: 0 auto;
  }
  .numberRuleDetail {
    border: 1px solid #eee;
    width: 90%;
    margin: 0 auto;
  }
  .numberUnit {
    border: 1px solid #ddd;
    padding:10px;
    margin-bottom: 10px;
    .numberUnitForm {
      min-height: 150px;
      .el-row {
        margin-bottom: 15px;
        .item-btn {
          position: relative;
          .item-close {
            position: absolute;
            right:-8px;
            top:-8px;
            font-size: 16px;
          }
        }
      }
    }
  }
  .rightBtn{
    align-self: flex-end;
  }
  .body-container{
    overflow: hidden;
    margin-top: 10px;
  .data-manage-rule-center {
    border: 1px solid #eee;
    // padding-top: 10px;
    .data-manage-rule-add {
      padding: 0 20px 10px;
      overflow: hidden;
      .ruletitle {
        font-size: 18px;
        line-height: 32px;
      }
      .rulebtn {
        text-align: right;
      }
      .ruleInfo {
        margin-top: 10px;
      }
    }
  }
  }
  ::v-deep{
    .page{
      padding: 0!important;
    }
  }
}
</style>
<style lang="scss">
  .dialogClass {
    .el-dialog__body {
      padding:10px 20px !important;
    }
  }
  .numberRuleList {
    td {
      padding: 2px 0;
    }
  }
  .rulelist.propertyRule {
    td {
      padding: 2px 0;
      cursor: default;
    }
    .move-td{
      cursor: move;
    }
    // ::v-deep{
      .sort-icon{
        display: inline-block;
        width: 16px;
        height: 16px;
        vertical-align: middle;
        object-fit:contain;
        margin-right: 10px;
        background: url('~@/assets/images/yidong.png') no-repeat;
      }
    // }
  }
  .el-radio-button__inner {
    border-left: 1px solid #eee;
  }
</style>
