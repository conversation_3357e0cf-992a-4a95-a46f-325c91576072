
import Vue from 'vue/types/umd';
<template>
  <div class="propertyRule-container">
    <div class="left-container">
      <div class="tree_box">
          <Tree ref="tree" @treeClick="treeClick" :editable="false" :treeKind="'propertyRule'"></Tree>
        </div>
    </div>
    <div class="right-container" v-loading="loading">
      <div class="noCategories" v-if="!currCategory">
          请选择类目
      </div>
      <div v-else>
        <div class="catalogInfo">
          <div class="ruleTitle">属性规则</div>
          <div class="displayFlex">
            <label>
            目录：
            <span class="catalogTxt"> {{currCategory.fulePath || ''}}</span>
          </label>
          <el-button v-if="addPropertyButton" size="small" @click="handleClickBrand" type="primary" class="rightBtn">新增品牌维度</el-button>
          </div>

        </div>
        <el-tabs v-model="activeName" class="content-container" type="card">
          <el-tab-pane v-for="(tab, index) in tabs" :label="tab.label" :name="tab.id" :key="`${currCategory.id||''}${tab.label}${index}`">
            <property-rule :brandId="tab.id" :ref="`${currCategory.id||''}${tab.id}PropertyRule`"></property-rule>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <el-dialog title="新增品牌维度配置"  :visible.sync="brandDialogVisible" width="400px" v-if="brandDialogVisible">
      <span>当前类目：{{currCategory.fulePath || ''}}</span>
      <el-form :model="brandFormData" label-width="100px" :rules="brandRules" ref="brandForm" v-if="brandDialogVisible">
        <el-form-item label="选择品牌" prop="brandId">
          <el-select filterable clearable remote :remote-method="(name) => getEntityList('brand', name)" v-model="brandFormData.brandId" @focus="getEntityList('brand')">
            <el-option v-for="(item) in option.brand" :key="item.id" :label="item.name" :value="item.id" placeholder="请选择品牌">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" size="mini" @click="handleAddBrand">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getCCEntityTypes, qryBrandInfoByCategory } from '@/api/dataManage'
import { mapState } from 'vuex'
import { findButton } from '../common/untils/tools'
export default {
  provide() {
    return {
      category: this
    }
  },
  components: {
    Tree: () => import('../common/tree.vue'),
    propertyRule: () => import('./components/propertyRule.vue')
  },
  data() {
    return {
      loading: false,
      currCategory: null,
      treeKind: 'propertyRule',
      activeName: '',
      // defaultTab: [{
      //   id: 'default',
      //   label: '默认'
      // }],
      tabs: [],
      option: {
        brand: []
      },
      brandDialogVisible: false,
      brandFormData: {
        brandId: ''
      },
      brandRules: {
        brandId: [
          { required: true, message: '请选择品牌', trigger: ['blur', 'change'] }
        ]
      }
    }
  },
  computed: {
    ...mapState(['menu']),
    addPropertyButton() {
      return findButton(
      this.menu,
      '/dataManage/propertyRule',
      '添加属性及规则'
    )
    }
  },
  watch: {
    activeName: {
      handler() {
        this.doLayout()
      },
      immediate: false
    }
  },
  methods: {
    treeLoad() {
      this.$refs.tree.reload()
    },
    treeClick(data) {
      this.currCategory = data
      this.qryBrandInfo()
    },
    async qryBrandInfo() {
      this.loading = true
      const { id = '' } = this.currCategory
      const { data = { 0: '' }, message = '', code = null } = await qryBrandInfoByCategory({
        fourCategoryId: id
      }).finally(() => {
        this.loading = false
      })

      this.tabs = Object.entries(data).reduce((pre, cur, index) => {
        const [key, value] = cur
        if (index === 0) this.activeName = key
        pre.push({
          id: key,
          label: value || '默认'
        })
        return pre
      }, [])
      if (code !== 0) {
        this.$message.error(message || '操作失败')
      }
    },
    handleClickBrand() {
      this.brandFormData = {
        brandId: ''
      }
      this.option.brand = []
      this.brandDialogVisible = true
    },
    async getEntityList(type = '', name = '') {
      const fetchApi = {
        brand: getCCEntityTypes
      }
      const fetchParams = {
        brand: () => {
          return {
            entityType: 'entity.brand',
            name
          }
        }
      }
      let params = (fetchParams[type] && fetchParams[type]()) || {}
      let { data = [] } = await fetchApi[type](params)
      this.option[type] = data
    },
    async handleAddBrand() {
      let res = await this.$refs['brandForm'].validate().catch((error) => {
        console.warn(error)
      })
      if (!res) return
      const { id = '', name = '' } = this.option.brand.filter(opt => opt.id === this.brandFormData.brandId)?.[0] || {}
      if (!this.tabs.map(tab => tab.id).includes(id)) {
        this.tabs.push({
        id,
        label: name
      })
        this.$message.success('新增品牌维度成功，请添加对应类目属性。')
      } else {
        this.$message.warning('新增品牌已存在，请添加对应类目属性。')
      }
      this.brandDialogVisible = false
      this.$nextTick(() => {
        this.activeName = id
      })
    },
    doLayout() {
      this.$nextTick(() => {
        if (this.$refs[`${this.currCategory.id}${this.activeName}PropertyRule`] && this.$refs[`${this.currCategory.id}${this.activeName}PropertyRule`][0]) {
          this.$refs[`${this.currCategory.id}${this.activeName}PropertyRule`][0].doLayout()
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.propertyRule-container{
  min-height: 500px;
  padding: 20px;
  overflow: hidden;
  display: flex;
  .left-container{
    width: 270px;
    border: 1px solid #eee;
    .tree_box{
      width: 100%;
      box-sizing: border-box;
      padding: 20px 10px;
      overflow: auto;
      position: relative;
    }
  }
  .right-container{
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
    border: 1px solid #eee;
    .noCategories{
      text-align: center;
      font-size: 20px;
      padding: 200px 0
    }
    .catalogInfo{
      padding: 10px 10px 0 20px;
      overflow: hidden;
      .ruleTitle{
        font-size: 18px;
        line-height: 32px;
      }
      .displayFlex{
        display: flex;
        padding: 10px 0;
        justify-content: space-between;
        .catalogTxt{
          color: #a8abc9;
        }

      }
    }
    .content-container{
      padding: 0 10px;
    }
  }
  ::v-deep{
    .el-dialog__header{
      border-bottom: 1px solid #eee;
    }
    .el-form{
      margin-top: 15px;
    }
    .el-dialog__footer{
      text-align: center;
    }
  }
}
</style>
