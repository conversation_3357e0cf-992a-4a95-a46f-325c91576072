<template>
   <el-container class="taskInfo-wrapper">
    <h3 class="header">任务名称：{{ taskInfo.taskName || '' }}</h3>
    <el-card class="wrapper-head box-card">
      <el-row>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>产品名称</label>
            </el-col>
            <el-col :span="18">
              <el-input
                style="width: 100%"
                placeholder="请输入产品名称"
                v-model="searchFormData['productName']"
                clearable
              >
              </el-input>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>条码</label>
            </el-col>
            <el-col :span="18">
              <el-input
                style="width: 100%"
                placeholder="请输入条码"
                v-model="searchFormData['barcode']"
                clearable
              >
              </el-input>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>规格</label>
            </el-col>
            <el-col :span="18">
              <el-input
                style="width: 100%"
                placeholder="请输入规格"
                v-model="searchFormData['specification']"
                clearable
              >
              </el-input>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>匹配sku编码</label>
            </el-col>
            <el-col :span="18">
              <el-input
                v-model="searchFormData['matchSkuCode']"
                clearable
                placeholder="请输入匹配sku编码"
              />
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>匹配sku数量</label>
            </el-col>
            <el-col :span="18">
              <numberInput v-model="searchFormData.matchSkuCnt" :positive="true" :integer="true">
              </numberInput>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>人工确认</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['confirmStatus']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in selects.ackStatusOptions"
                  :key="`confirmStatus-${item.value}`"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>型号是否匹配</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['ifModelMatch']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in selects.booleanSelects"
                  :key="`ifModelMatch-${item.value}`"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>品牌是否匹配</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['ifBrandMatch']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in selects.booleanSelects"
                  :key="`ifBrandMatch-${item.value}`"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>匹配度</label>
            </el-col>
            <el-col :span="18" class="display-flex">
              <numberInput v-model="searchFormData['matchedValMin']"></numberInput>
              <span>~</span>
              <numberInput v-model="searchFormData['matchedValMax']"></numberInput>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>是否切换正式标品</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['ifSwitchBp']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in selects.booleanSelects"
                  :key="`ifSwitchBp-${item.value}`"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" style="text-align: right" class="left-auto">
          <el-button size="mini" @click="handleResetSearch">重置</el-button>
          <el-button size="mini" @click="handleSearch" type="primary"
            >搜索</el-button
          >
        </el-col>
      </el-row>
    </el-card>
    <el-card class="wrapper-body box-card">
      <div class="btn-container btn-r">
        <el-button class="btn-label" size="mini" @click="handleBatchUpdate" :loading="loading.update">批量修改</el-button>
        <el-button class="btn-label" size="mini" @click="handleExport" :loading="loading.export">导出数据</el-button>
      </div>
      <el-table
        v-loading="loading.tableLoading"
        :data="tableData"
        border
        fit
        highlight-current-row
        :span-method="objectSpanMethod"
        @selection-change="handleSelectionChange"
      >
        <template slot="empty">
          <div class="empty">
            <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
          </div>
        </template>
        <template
          v-for="column in tableColumns"
        >
          <el-table-column align="center" type="selection" width="50" v-if="column.type ==='selected'" :key="column.prop"/>
          <el-table-column
            v-else
            align="center"
            :prop="column.prop"
            :label="column.label"
            :key="column.prop"
            :min-width="column.minWidth"
          >
            <template slot-scope="scope">
              <span v-if="column.prop === 'barcode'" class="flex-row">
                <div class="flex-left">
                  <ImgCol :imgUrl="scope.row.picUrl || ''" />
                </div>
                <div class="flex-right">
                  <span>{{scope.row.productName || ''}}</span>
                  <span class="font-12">条码：{{scope.row.barcode || ''}}</span>
                  <span class="font-12">规格：{{scope.row.specification || ''}}</span>
                  <span class="font-12">品牌：{{scope.row.otherBrandName || ''}}</span>
                </div>
              </span>
              <template v-else-if="column.prop === 'skuCode'">
                <el-link
                  v-if="scope.row.skuCode"
                  type="primary"
                  target="_blank"
                  class="underline_link"
                  :href="webUrl(scope.row)"
                  :underline="false"
                  >{{ scope.row.skuCode }}
                </el-link>
                <span v-else>--</span>
              </template>
              <span v-else>{{ scope.row[column.prop] || '--' }}</span>
            </template>
          </el-table-column>
        </template>

        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.confirmStatus === '1'">
              <el-button type="text" @click="handleConfirm(scope.row, 'update')" >确认修改</el-button>
              <el-divider direction="vertical" class="color-blue"></el-divider>
              <el-button type="text" @click="handleConfirm(scope.row, 'ignore')">无需修改</el-button>
            </span>
            <span v-else :class="modelLabelClass(scope.row)">{{modelConfirmStatusName(scope.row)}}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 50, 100, 200, 300, 400]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </el-card>
   </el-container>
</template>

<script>
import { cloneDeep } from 'lodash'
import { taskInfoColumns, ackStatusSelectsList as ackStatusSelects, ackStatusLabel, booleanSelects } from '../helper'
import { removeProperty, getOtherSystemUrl } from '@/utils/dataManage.js'
import { qryBarCodeTaskDetail, updateSkuBarCode, ignoreSkuBarCode, updateBarCodeTaskDetail, exportBarCodeTaskDetail } from '@/api/dataManage'
export default {
  components: {
    numberInput: () => import('./numberInput.vue'),
    ImgCol: () => import('./ImgCol.vue')
  },
  data() {
    return {
      taskInfo: {},
      tableColumns: cloneDeep(taskInfoColumns),
      searchFormData: {
        name: '',
        barCode: '',
        specification: '',
        skuCode: '',
        skuNum: '',
        ifModelMatch: '',
        ifSwitchBp: '',
        matchedValMin: '',
        matchedValMax: ''
      },
      selects: {
        ackStatusOptions: [
          ...ackStatusSelects
        ],
        booleanSelects
      },
      pageSize: 10,
      currentPage: 1,
      total: 0,
      loading: {
        tableLoading: false,
        update: false,
        export: false
      },
      tableData: [],
      multipleSelection: []

    }
  },
  computed: {
    modelConfirmStatusName() {
      return function ({ confirmStatus = '' }) {
        const { label = '' } = ackStatusLabel.filter(item => item.value === (confirmStatus + ''))?.[0] || {}
        return label
      }
    },
    modelLabelClass() {
      return function({ confirmStatus = '' }) {
        const fetch = {
          '2': 'color-green'
        }
        return fetch[confirmStatus + ''] || 'color-grey'
      }
    },
    webUrl() {
      return function (data) {
        const { skuCode = '' } = data
        let preUrl = getOtherSystemUrl('cc')
        return `${preUrl}product/skuDetail/${skuCode}?tagName=${skuCode}详情`
      }
    }
  },
  created() {
    const { taskId = '' } = this.$route.params
    const { taskName = '' } = this.$route.query
    this.taskInfo = {
      taskId,
      taskName
    }
    this.getCurrentList()
  },
  methods: {
    handleResetSearch() {
      this.searchFormData = {}
      this.getCurrentList()
    },
    async getCurrentList() {
      this.loading.tableLoading = true
      const { success = false, errDesc = '', data = {} } = await qryBarCodeTaskDetail({
        taskId: this.taskInfo?.taskId || '',
        ...this.searchFormData,
        pageNum: this.currentPage,
        pageSize: this.pageSize
      }).finally(() => {
        this.loading.tableLoading = false
      })
      if (!success) {
        this.$message.error(errDesc || '操作失败')
        return
      }
      const { content = [], totalElements = 0 } = data
      this.tableData = content.reduce((pre, cur) => {
        let { skus = [] } = cur
        skus = skus.length ? skus : [{
          ...cur
        }]
        pre = pre.concat(skus.map((sku, index) => ({
          ...sku,
          ...cur,
          matchedValue: `${sku.matchedValue || '0'}分`,
          rowspan: (!index && skus.length) || 0,
          confirmStatus: (sku?.confirmStatus || '') + ''
        })))
        return pre
      }, [])
      this.total = totalElements
    },
    handleSearch() {
      this.currentPage = 1
      this.searchFun()
    },
    searchFun() {
      this.searchFormData = {
        ...removeProperty(this.searchFormData)
      }
      this.getCurrentList()
    },
    handleSizeChange(val) {
      if ((this.currentPage - 1) * val <= this.total) {
        this.pageSize = val
        this.searchFun()
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.searchFun()
    },
    async handleConfirm({ taskSkuRelId = '' }, type = '') {
      const fetch = {
        update: updateSkuBarCode,
        ignore: ignoreSkuBarCode
      }
      this.loading.tableLoading = true
      const { success = false, errDesc = '' } = await fetch[type]({ taskSkuRelIds: [taskSkuRelId] }).finally(() => {
        this.loading.tableLoading = false
      })
      if (!success) {
        this.$message.error(errDesc)
        return
      }
      this.getCurrentList()
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      const { rowspan = 0 } = row
      const { property = '' } = column
      if (columnIndex === 0 && property === 'barcode') {
        if (rowspan) {
          return {
            rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleBatchUpdate() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要修改的数据')
        return
      }
      this.$confirm('您确认修改所有选中sku的条形码吗？', '提示', {
        confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
      }).then(() => {
        this.updateTaskDetail()
      }).catch(() => {})
    },
    async updateTaskDetail() {
      this.loading.update = true
      const { success = false, errDesc = '' } = await updateBarCodeTaskDetail({
        taskSkuRelIds: this.multipleSelection.map(item => item.taskSkuRelId).filter(item => item)
      }).finally(() => {
        this.loading.update = false
      })
      this.$message({
        message: success ? '修改成功' : errDesc,
        type: success ? 'success' : 'error'
      })
      success && this.getCurrentList()
    },
    async handleExport() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要导出的数据')
        return
      }
      this.loading.export = true
      const { success = false, errDesc = '' } = await exportBarCodeTaskDetail({
        taskSkuRelIds: this.multipleSelection.map(item => item.taskSkuRelId).filter(item => item)
      }).finally(() => {
        this.loading.export = false
      })
      this.$message({
        message: success ? '操作成功' : errDesc,
        type: success ? 'success' : 'error'
      })
    }
  }
}
</script>

<style  lang="less" scoped>
.taskInfo-wrapper{
  margin: 0;
  padding: 10px;
  width: 100%;
  display: flex;
  flex-direction: column;
  .header{
    margin-bottom: 10px;
    font-weight: 300;
  }
  .wrapper-head{
    width: 100%;
    .wrapper-head-label{
      text-align: right;
      padding-right: 10px;
    }
    .display-flex{
      display: flex;
      align-items: center;
      span{
        margin: 0 2px;
      }
    }
    &::v-deep {
      .el-select, .el-date-editor{
        width: 100%;
      }

      .el-row{
        margin-bottom: 16px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
      }
    }
    .left-auto{
      margin-top: 10px;
      float: right;
    }
  }
  .wrapper-body{
    margin-top: 10px;
    .btn-container{
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }
    .btn-label{
      color: #5098ff;
    }
    .btn-r{
      justify-content: end;
    }
    .flex-row{
      display: flex;
      flex-direction: row;
      align-items: center;
      .flex-left{

      }
      .flex-right{
        text-align: left;
        padding-left: 10px;
        span {
          font-size: 13px;
          font-family: PingFang SC;
          font-weight: 400;
          color: #606266;
          line-height: 22px;
          display: inline-block;
          width: 100%;
        }
        .font-12{
          font-size: 12px;
        }
      }
    }
    .color-grey{
      color: #b4bccc;
    }

    .color-green{
      color: #67c23a;
    }

    .underline_link{
      text-decoration: underline;
    }
  }
  &::v-deep{
    .el-dialog__header{
      border-bottom: 1px solid #EBEEF5;
      display: flex;
    }
    .el-pagination{
      text-align: right;
    }
    .el-upload__input{
      color:#DCDFE6;
      font-size: 12px;
    }
    .el-divider{
      background-color: #597bee;
    }
    .el-button--text{
      color: #597bee;
    }
  }
}
</style>
