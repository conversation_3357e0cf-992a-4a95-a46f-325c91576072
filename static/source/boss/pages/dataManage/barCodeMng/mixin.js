import { getCCEntityTypes, queryUserList } from '@/api/dataManage.js'
const mixin = {
  methods: {
    async getEntityList(selectsObj, type = '', name = '',) {
      const fetchApi = {
        brand: getCCEntityTypes,
        creatorId: queryUserList
      }
      const fetchParams = {
        brand: () => {
          return {
            entityType: 'entity.brand',
            name
          }
        },
        creatorId: () => {
          return {
            page: 0,
            size: 10,
            nickName: name
          }
        }
      }
      let params = (fetchParams[type] && fetchParams[type]()) || {}
      let { data = [] } = await fetchApi[type](params)

      const handleFetch = {
        brand: () => {
          selectsObj[type] = data
        },
        creatorId: () => {
          selectsObj[type] = data?.content || []
        }
      }

      handleFetch[type]()
    }
  }
}

export default mixin
