export const uploadUrl = '/fe-upload/api/upload/'
export const tableColums = [
  {
    label: '任务名称',
    prop: 'taskName'
  },
  {
    label: 'ZKH品牌',
    prop: 'brandName'
  },
  {
    label: '公司名称',
    prop: 'companyName'
  },
  {
    label: '条码数量',
    prop: 'barcodeCnt'
  },
  {
    label: 'AI匹配状态',
    prop: 'aiMatchStatusName'
  },
  {
    label: '人工确认状态',
    prop: 'confirmStatusName'
  },
  {
    label: '创建人',
    prop: 'createdName'
  }
]

export const taskInfoColumns = [
  {
    label: '条码信息',
    prop: 'barcode',
    minWidth: '150px'
  },
  {
    prop: 'selection',
    type: 'selected'
  },
  {
    label: '匹配sku',
    prop: 'skuCode'
  },
  {
    label: '是否切换正式标品',
    prop: 'ifSwitchBpName'
  },
  {
    label: '物料描述',
    prop: 'materialDesc'
  },
  {
    label: '匹配度',
    prop: 'matchedValue'
  },
  {
    label: '匹配说明',
    prop: 'matchedDesc'
  },
  {
    label: '型号是否匹配',
    prop: 'ifXinghaoMatchedName'
  },
  {
    label: '品牌是否匹配',
    prop: 'ifBrandMatchedName'
  },
  {
    label: '当前条码',
    prop: 'currBarcode'
  }
]

export const ackStatusSelectsList = [{
  label: '匹配中',
  value: '0'
}, {
  label: '确认中',
  value: '1'
}, {
  label: '已确认',
  value: '2'
}]

export const ackStatusLabel = [{
  label: '匹配中',
  value: '0'
}, {
  label: '确认中',
  value: '1'
}, {
  label: '已修改',
  value: '2'
}, {
  label: '未修改',
  value: '3'
}]

export const booleanSelects = [{
  label: '是',
  value: '1'
}, {
  label: '否',
  value: '0'
}]
