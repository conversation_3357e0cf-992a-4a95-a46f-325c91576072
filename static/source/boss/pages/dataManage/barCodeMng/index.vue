<template>
  <el-container class="barCodeMng-wrapper">
    <h3 class="header">任务列表</h3>
    <el-card class="wrapper-head box-card">
      <el-row>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>任务名称</label>
            </el-col>
            <el-col :span="18">
              <el-input
                style="width: 100%"
                placeholder="请输入任务名称"
                v-model="searchFormData['taskName']"
                clearable
              >
              </el-input>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>品牌</label>
            </el-col>
            <el-col :span="18">
              <el-select filterable clearable remote :remote-method="(name) => getEntityList(selects, 'brand', name)" v-model="searchFormData.brandId" @focus="getEntityList(selects, 'brand')">
                <el-option v-for="(item) in selects.brand" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>公司名称</label>
            </el-col>
            <el-col :span="18">
              <el-input
                style="width: 100%"
                placeholder="请输入公司名称"
                v-model="searchFormData['companyName']"
                clearable
              >
              </el-input>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>创建人</label>
            </el-col>
            <el-col :span="18">
              <el-select filterable clearable remote :remote-method="(name) => getEntityList(selects, 'creatorId', name)" v-model="searchFormData.createdId" @focus="getEntityList(selects, 'creatorId')">
                <el-option v-for="(item) in selects.creatorId" :key="item.id" :label="item.nickName" :value="item.id">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>AI匹配状态</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['aiMatchStatus']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in selects.aiStatusOptions"
                  :key="`aiStatus-${item.value}`"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>人工确认</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['confirmStatus']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in selects.ackStatusOptions"
                  :key="`ackStatus-${item.value}`"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" style="text-align: right" class="left-auto">
          <el-button size="mini" @click="handleResetSearch">重置</el-button>
          <el-button size="mini" @click="handleSearch" type="primary"
            >搜索</el-button
          >
        </el-col>
      </el-row>
    </el-card>
    <el-card class="wrapper-body box-card">
      <div class="btn-container">
        <span>
          <el-button size="mini" @click="handleAdd" type="primary"
            >新增任务</el-button
          >
        </span>
        <span>
        </span>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        fit
        highlight-current-row
      >
        <template slot="empty">
          <div class="empty">
            <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
          </div>
        </template>
        <el-table-column
          v-for="column in tableColumns"
          align="center"
          :prop="column.prop"
          :label="column.label"
          :key="column.prop"
        >
          <template slot-scope="scope">
            <span v-if="['aiMatchStatusName', 'confirmStatusName'].includes(column.prop)">
              <el-popover
                v-if="modelPopover(scope.row, column.prop)"
                placement="right"
                width="300"
                @show="handleShowPop(scope.row, column.prop)"
              >
                <div class="flex-row">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="18"
                    :percentage="percent"
                    :format="formatPregress"
                  >
                </el-progress>
                </div>

                <a slot="reference" style="color: #409eff; cursor: pointer" class="link-btn">{{
                  scope.row[column.prop] || '--'
                }}</a>
              </el-popover>
              <span v-else>{{ scope.row[column.prop] || '--' }}</span>
            </span>
            <span v-else>{{ scope.row[column.prop] || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleCheck(scope.row)" >查看</el-button>
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" @click="hancleExport(scope.row)">导出</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 50, 100, 200, 300, 400]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </el-card>
    <create-dialog v-if="createDialogVisible" :visible.sync="createDialogVisible" :callback="searchFun"></create-dialog>
  </el-container>
</template>

<script>
import { qryBarCodeList, qryAIMatchProgress, exportBarCodeTask } from '@/api/dataManage.js'
import { cloneDeep } from 'lodash'
import { tableColums, ackStatusSelectsList as ackStatusSelects } from './helper.js'
import mixin from './mixin.js'
import { removeProperty } from '@/utils/dataManage.js'
export default {
  mixins: [mixin],
  components: {
    createDialog: () => import('./components/createDialog.vue')
  },
  data() {
    return {
      tableColumns: cloneDeep(tableColums),
      pageSize: 10,
      currentPage: 1,
      total: 0,
      searchFormData: {
        taskName: '',
        brandId: '',
        companyName: '',
        createdId: '',
        aiMatchStatus: '',
        confirmStatus: ''
      },
      selects: {
        brand: [],
        creatorId: [],
        aiStatusOptions: [{
          label: '匹配中',
          value: '0'
        }, {
          label: '已完成',
          value: '1'
        }],
        ackStatusOptions: [
          ...ackStatusSelects
        ]
      },
      loading: false,
      tableData: [],
      // multipleSelection: [],
      create_dialog: false,
      checkedTotal: 0,
      barCodeTotal: 1,
      percent: 0,
      createDialogVisible: false
    }
  },
  computed: {
    modelPopover() {
      return function(rowData, prop) {
        const fetch = {
          aiMatchStatusName: () => {
            const { aiMatchStatus = '' } = rowData
            return (aiMatchStatus + '') === '0'
          },
          confirmStatusName: () => {
            const { confirmStatus = '' } = rowData
            return (confirmStatus + '') === '1'
          }
        }
        return (fetch[prop] && fetch[prop]()) || false
      }
    }
  },
  created() {
    this.getCurrentList()
  },
  methods: {
    // handleSelectionChange(val) {
    //   this.multipleSelection = val
    // },
    handleResetSearch() {
      this.searchFormData = {}
      this.getCurrentList()
    },
    async getCurrentList() {
      this.loading = true
      const { success = false, errDesc = '', data = {} } = await qryBarCodeList({
        ...this.searchFormData,
        pageNum: this.currentPage,
        pageSize: this.pageSize
      }).finally(() => {
        this.loading = false
      })
      if (!success) {
        this.$message.error(errDesc || '操作失败')
        return
      }
      const { content = [], totalElements = 0 } = data
      this.tableData = content
      this.total = totalElements
    },
    handleSearch() {
      this.createDialogVisible = false
      this.currentPage = 1
      this.searchFun()
    },
    searchFun() {
      this.searchFormData = {
        ...removeProperty(this.searchFormData)
      }
      this.getCurrentList()
    },
    handleAdd() {
      this.createDialogVisible = true
    },
    // 查看诊断中任务进度
    handleShowPop({ taskId }, columnName = '') {
      this.percent = 0
      this.checkedTotal = 0
      this.barCodeTotal = 0
      this.pollingTaskProgress({ taskId }, columnName)
    },
    async pollingTaskProgress(params = {}, columnName) {
      const { data = {}, success = false, errDesc = '' } = await qryAIMatchProgress(params)
      if (!success) {
        this.$message.error(errDesc)
        return
      }
      const { barcodeCnt = 0, matchedBarcodeCnt = 0, confirmBarcodeCnt = 0 } = data
      const fetch = {
        aiMatchStatusName: () => {
          this.checkedTotal = matchedBarcodeCnt
        },
        confirmStatusName: () => {
          this.checkedTotal = confirmBarcodeCnt
        }
      }
      this.barCodeTotal = barcodeCnt
      fetch[columnName] && fetch[columnName]()
      this.calculatePercent()
    },
    calculatePercent() {
      this.percent = (this.barCodeTotal && (this.checkedTotal / this.barCodeTotal).toFixed(2) * 100) || 0
    },
    handleCheck({ taskId = '', taskName = '', brandName = '' }) {
      this.$router.push({
          path: `barCodeMng/taskInfo/${taskId}?taskName=${taskName}（品牌：${brandName}）`
        })
    },
    async hancleExport({ taskId }) {
      this.loading = true
      const { success = false, errDesc = '' } = await exportBarCodeTask({
        taskId
      }).finally(() => {
        this.loading = false
      })
      if (!success) {
        this.$message.error(errDesc)
        return
      }
      this.$message.success('导出成功，请至商品中心【文件下载列表】中查看')
    },
    handleSizeChange(val) {
      if ((this.currentPage - 1) * val <= this.total) {
        this.pageSize = val
        this.searchFun()
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.searchFun()
    },
    formatPregress() {
      return `${this.checkedTotal}/${this.barCodeTotal}`
    }
  }
}
</script>

<style lang="less" scoped>
.barCodeMng-wrapper{
  margin: 0;
  padding: 10px;
  width: 100%;
  display: flex;
  flex-direction: column;
  .header{
    margin-bottom: 10px;
    font-weight: 300;
  }
  .wrapper-head{
    width: 100%;
    .wrapper-head-label{
      text-align: right;
      padding-right: 10px;
    }
    &::v-deep {
      .el-select, .el-date-editor{
        width: 100%;
      }
      .el-row{
        margin-bottom: 16px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
      }
    }
    .left-auto{
      margin-top: 10px;
      float: right;
    }
  }
  .wrapper-body{
    margin-top: 10px;
    .btn-container{
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }
    .btn-label{
      color: #5098ff;
    }
    .link-btn{
      text-decoration: underline;
    }
    .flex-row{
      display: flex;
      flex-direction: row;
    }
  }
  &::v-deep{
    .el-dialog__header{
      border-bottom: 1px solid #EBEEF5;
      display: flex;
    }
    .el-pagination{
      text-align: right;
    }
    .el-upload__input{
      color:#DCDFE6;
      font-size: 12px;
    }
    .el-divider{
      background-color: #597bee;
    }
    .el-button--text{
      color: #597bee;
    }
  }
}
</style>
