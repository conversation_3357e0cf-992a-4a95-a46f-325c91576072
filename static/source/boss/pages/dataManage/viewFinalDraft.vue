<template>
  <div class="page data-manage-rule-container" v-loading="loading.page">
    <div style="overflow: hidden; display: flex;" v-show="!loading.page">
      <!--左边树形结构-->
      <div class="rule-left">
        <!--<div class="reload el-icon-refresh" @click="treeLoad()"></div>-->
        <Tree ref="tree" @treeClick="treeClick" @treeLoading="treeLoading" :treeKind="treeKind" :treeType="1"></Tree>
      </div>
      <!--左边树形结构 end-->
      <!--右边添加属性规则-->
      <div class="rule-right">
        <div class="noCategories" style="text-align: center; font-size: 20px; padding:200px 0;" v-show="!currCategory">请选择类目</div>
        <div class="data-manage-rule-center" v-show="currCategory">
          <div class="data-manage-rule-add">
            <div class="ruletitle">定稿管理</div>
            <div class="ruleInfo">
              目录：<span class="color-grey-300" v-if="currCategory" style="margin-right: 50px; min-width: 300px; display: inline-block; text-align: left; text-indent: 0;">{{currCategory.fulePath | categoriesFormat}}</span>
              规则版本：<span class="color-grey-300">{{ruleVersion}}</span>
            </div>
            <div class="repairTab" style="overflow: hidden; border-bottom: 1px solid #eee;">
              <div style="float: left; position: relative; bottom: -2px;">
                <el-menu
                  :default-active="activeIndex"
                  class="el-menu-demo"
                  mode="horizontal"
                  active-text-color="#597bee"
                  @select="handleSelect"
                >
                  <el-menu-item index="1">属性</el-menu-item>
                  <el-menu-item index="2">数据</el-menu-item>
                </el-menu>
              </div>
              <div style="float: right; margin-top: 15px; padding:9px 30px;">
                <el-button :loading="loading.exportRule" v-if="exportFinalDraftButton" @click="exportFinalDraft" size="small" type="primary">导出定稿规则</el-button>
                <el-button :loading="loading.copyData" v-if="dataCopyToCRMButton" @click="openCopyToCRMDialog" size="small" type="primary">数据同步至CRM</el-button>
              </div>
            </div>
          </div>
          <!--定稿属性-->
          <div v-show="activeIndex === '1'">
            <FinalDraftProperty ref="finalDraftProperty"></FinalDraftProperty>
          </div>
          <!--定稿属性 end-->
          <!--定稿数据-->
          <div v-show="activeIndex === '2'">
            <FinalDraftData ref="finalDraftData"></FinalDraftData>
          </div>
          <!--定稿数据 end-->

        </div>
      </div>
      <!--右边添加属性规则 end-->
    </div>
    <a ref="download" download="" :href="filePath"></a>
    <!--同步到CRM-->
    <el-dialog
      width="780px"
      title="数据同步至CRM"
      :visible.sync="showCopyToCRMDialog"
      :close-on-click-modal="false">
      <div class="importset">
        <el-row :span="24" :gutter="4">
          <el-col :span="4" class="dialogTitle">导出类目：</el-col>
          <el-col :span="5">
            <el-select
              v-model="copyToCRMForm.categoryLevel1Value"
              :disabled="!copyToCRMForm.categoryListLevel1 || copyToCRMForm.categoryListLevel1.length < 0"
              @change="categorySelect(1)"
              placeholder="请选择">
              <el-option
                v-for="item in copyToCRMForm.categoryListLevel1"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select
              v-model="copyToCRMForm.categoryLevel2Value"
              :disabled="!copyToCRMForm.categoryListLevel2 || copyToCRMForm.categoryListLevel2.length < 0"
              @change="categorySelect(2)"
              placeholder="请选择">
              <el-option
                v-for="item in copyToCRMForm.categoryListLevel2"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select
              v-model="copyToCRMForm.categoryLevel3Value"
              :disabled="!copyToCRMForm.categoryListLevel3 || copyToCRMForm.categoryListLevel3.length < 0"
              @change="categorySelect(3)"
              placeholder="请选择">
              <el-option
                v-for="item in copyToCRMForm.categoryListLevel3"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select
              v-model="copyToCRMForm.categoryLevel4Value"
              :disabled="!copyToCRMForm.categoryListLevel4 || copyToCRMForm.categoryListLevel4.length < 0"
              @change="categorySelect(4)"
              placeholder="请选择">
              <el-option
                v-for="item in copyToCRMForm.categoryListLevel4"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <!--<el-row :span="24">-->
          <!--<el-col :span="4" class="dialogTitle">导出规则：</el-col>-->
          <!--<el-col :span="16">-->
            <!--<el-select v-model="copyToCRMForm.importRuleType" placeholder="请选择">-->
              <!--<el-option-->
                <!--v-for="item in importRuleOptions"-->
                <!--:key="item.value"-->
                <!--:label="item.label"-->
                <!--:value="item.value">-->
              <!--</el-option>-->
            <!--</el-select>-->
          <!--</el-col>-->
        <!--</el-row>-->
        <el-row :span="24">
          <el-col :span="4" class="dialogTitle">备注：</el-col>
          <el-col :span="16">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请输入内容"
              v-model.trim="copyToCRMForm.remarks">
            </el-input>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right;">
            <el-button size="medium" @click="closeCopyToCRMDialog">关闭</el-button>
            <el-button size="medium" @click="sureCopyToCRM" type="primary" style="margin-left: 20px;">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--同步到 end-->
  </div>
</template>

<script>
import {
  getCategoriesVersion,
  getFinalizeExportData,
  getCRMcategory,
  exportToCRM
} from '@/api/dataManage'
import { mapState } from 'vuex'
import Tree from './common/tree'
import FinalDraftProperty from './compontents/finalDraftProperty'
import FinalDraftData from './compontents/finalDraftData'
import { findButton } from './common/untils/tools'

export default {
  name: 'propertyRule',
  data () {
    return {
      loading: {
        page: true,
        table: true,
        exportRule: false,
        exportData: false,
        copyData: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 200
      },
      treeKind: 'viewFinalDraft',
      ruleVersion: 'V5', // 规则版本
      activeIndex: '1', //  1 属性 2 数据
      currCategory: null, // 当前选中类目信息
      templateList: [], // 属性规则列表
      total: 0,
      categories: [],
      filePath: '',
      exportFinalDraftButton: false, // 是否显示导出定稿button
      dataCopyToCRMButton: false, // 是否显示数据同步button
      importRuleOptions: [{
        value: 'COVER',
        label: '覆盖冲突数据'
      }, {
        value: 'INHERENT',
        label: '保留原有数据'
      }, {
        value: 'IMPORT_AFTER_EMPTYING',
        label: '清空原有数据后导入'
      }],
      showCopyToCRMDialog: false,
      copyToCRMForm: {
        remarks: '', // 备注
        importRuleType: '', // 导入规则
        categoryList: null, // 目录树列表数据
        categoryListLevel1: null, // 目录树第一级列表数据
        categoryListLevel2: null, // 目录树第二级列表数据
        categoryListLevel3: null, // 目录树第三级列表数据
        categoryListLevel4: null, // 目录树第四级列表数据
        categoryLevel1Value: null, // 目录树第一级选中结果
        categoryLevel2Value: null, // 目录树第二级选中结果
        categoryLevel3Value: null, // 目录树第三级选中结果
        categoryLevel4Value: null // 目录树第四级选中结果
      }
    }
  },
  components: {
    FinalDraftProperty,
    FinalDraftData,
    Tree
  },
  computed: {
    ...mapState(['menu'])
  },
  filters: {
    categoriesFormat (value) {
      return value.split('/').join(' / ')
    },
    numberFormat (value) {
      let num = ''
      switch (value) {
        case 0:
          num = '一'
          break;
        case 1:
          num = '二'
          break;
        case 2:
          num = '三'
          break;
        case 3:
          num = '四'
          break;
        case 4:
          num = '五'
          break;
        case 5:
          num = '六'
          break;
        case 6:
          num = '七'
          break;
        case 7:
          num = '八'
          break;
        case 8:
          num = '九'
          break;
        case 9:
          num = '十'
          break;
        default:
          num = value + 1;
      }
      return num
    }
  },
  created () {
  },
  mounted () {
    let menu = this.menu
    this.exportFinalDraftButton = findButton(menu, '/dataManage/viewFinalDraft', '导出定稿')
    this.dataCopyToCRMButton = findButton(menu, '/dataManage/viewFinalDraft', '数据同步至CRM')
  },
  methods: {
    goGetCRMcategory (level, pid) {
      let data = {
        level: level,
        pid: pid || ''
      }
      getCRMcategory(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          this.importCRMForm.CRMcategoryListLevel1 = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    categorySelect (level) {
      // 树形目录变化时触发
      console.log('level=' + level)
      let This = this
      if (level === 1) {
        let plist = this.copyToCRMForm.categoryListLevel1
        let id = this.copyToCRMForm.categoryLevel1Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.copyToCRMForm.categoryListLevel2 = plist[i].catalogueTreeVOList
            This.copyToCRMForm.categoryListLevel3 = null
            This.copyToCRMForm.categoryListLevel4 = null
            This.copyToCRMForm.categoryLevel2Value = ''
            This.copyToCRMForm.categoryLevel3Value = ''
            This.copyToCRMForm.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 2) {
        let plist = this.copyToCRMForm.categoryListLevel2
        let id = this.copyToCRMForm.categoryLevel2Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.copyToCRMForm.categoryListLevel3 = plist[i].catalogueTreeVOList
            This.copyToCRMForm.categoryListLevel4 = null
            This.copyToCRMForm.categoryLevel3Value = ''
            This.copyToCRMForm.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 3) {
        let plist = this.copyToCRMForm.categoryListLevel3
        let id = this.copyToCRMForm.categoryLevel3Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.copyToCRMForm.categoryListLevel4 = plist[i].catalogueTreeVOList
            This.copyToCRMForm.categoryLevel4Value = ''
            return false
          }
        }
      }
    },
    handleSelect (key, keyPath) {
      // 切换tab
      this.activeIndex = key
      this.listQueryInfo.current = 1
      if (this.activeIndex === '1') {
        console.log('获取属性规则列表')
      } else {
        console.log('获取数据列表')
      }
    },
    treeLoad () {
      this.$refs.tree.reload();
    },
    getCategoriesVersion () {
      // 获取属性规则版本号
      let This = this
      let categoryId = This.currCategory.id
      getCategoriesVersion(categoryId).then(res => {
        if (res.code === 0) {
          This.ruleVersion = res.data.version
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    treeClick (data) {
      console.log('tree传递过来的')
      // console.log(data)
      this.listQueryInfo.current = 1
      let currCategory = data
      this.currCategory = currCategory
      this.$refs.finalDraftProperty.goGetFinalDraftList(currCategory)
      this.$refs.finalDraftData.goGetTableStructure(currCategory)
      this.getCategoriesVersion()
    },
    treeLoading (data) {
      this.loading.page = false
    },
    exportFinalDraft () {
      // 导出定稿规则
      let This = this
      let categoryId = This.currCategory.id
      This.loading.exportRule = true
      getFinalizeExportData(categoryId).then(res => {
        if (res.code === 0) {
          This.filePath = res.data.url
          let timer = setTimeout(function () {
            This.$refs.download.click()
            clearTimeout(timer)
          }, 300)
        } else {
          This.$message.error(res.msg)
        }
        This.loading.exportRule = false
      }).catch(() => {
        This.loading.exportRule = false
      })
    },
    sureCopyToCRM () {
      // 数据同步至CRM
      let This = this
      let copyToCRMForm = this.copyToCRMForm
      let categoryId = This.currCategory.id
      let data = {
        categoryId: categoryId, // 当前类目id
        productCategoryId: copyToCRMForm.categoryLevel4Value, // crm类目id
        // type: copyToCRMForm.importRuleType,
        remarks: copyToCRMForm.remarks
      }
      if (!data.categoryLevel4Value) {
        this.$message.error('请选择完整类目')
        return false
      }
      This.loading.copyData = true
      exportToCRM(data).then(res => {
        if (res.code === 0) {
          This.$message.success('同步成功')
        } else {
          This.$message.error(res.msg)
        }
        This.loading.copyData = false
        This.loading.showCopyToCRMDialog = false
      }).catch(() => {
        This.loading.showCopyToCRMDialog = false
      })
    },
    openCopyToCRMDialog () {
      this.showCopyToCRMDialog = true
      this.goGetCRMcategory(1)
    },
    closeCopyToCRMDialog () {
      this.showCopyToCRMDialog = false
    }
  }
}
</script>
<style lang="scss" scoped>
.data-manage-rule-container {
  min-height: 500px;
  .data-manage-rule-center {
    padding-top: 10px;
    .data-manage-rule-add {
      padding: 0 0 10px;
      overflow: hidden;
      .ruletitle {
        font-size: 18px;
        line-height: 32px;
      }
      .ruleInfo {
        padding: 20px 0 10px;
      }
    }
  }
  .rule-left {
    float: left;
    width:270px;
    border: 1px solid #eee;
    height: 800px;
    box-sizing: border-box;
    padding: 20px 10px;
    overflow: auto;
    position: relative;
    .reload {
      position: absolute;
      right: 5px;
      top: 5px;
      font-size: 20px;
      color: #ccc;
      cursor: pointer;
      visibility: visible;
      &:hover {
        color: #b0b0b0;
      }
    }
  }
  .rule-right {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
  }
  .dialogTitle {
    line-height: 32px;
    text-align: right;
    box-sizing: border-box;
    padding-right: 12px;
  }
  .ruleset {
    .rulesetcont {
      min-height: 400px;
      margin-bottom: 10px;
    }
    .el-row {
      margin-bottom: 10px;
      .item-btn {
        position: relative;
        .item-close {
          position: absolute;
          right:-8px;
          top:-8px;
          font-size: 16px;
        }
      }
    }
  }
  .el-tree-node__content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .numberRuleTab {
    width: 90%;
    height: 35px;
    margin: 0 auto;
  }
  .numberRuleDetail {
    border: 1px solid #eee;
    width: 90%;
    margin: 0 auto;
  }
  .numberUnit {
    border: 1px solid #ddd;
    padding:10px;
    margin-bottom: 10px;
    .numberUnitForm {
      min-height: 150px;
      .el-row {
        margin-bottom: 15px;
        .item-btn {
          position: relative;
          .item-close {
            position: absolute;
            right:-8px;
            top:-8px;
            font-size: 16px;
          }
        }
      }
    }
  }
}
.el-menu.el-menu--horizontal {
  border-bottom: none;
}
</style>
<style lang="scss">
  .dialogClass {
    .el-dialog__body {
      padding:10px 20px !important;
    }
  }
  .rulelist.viewDraft {
    td {
      padding: 8px 0;
    }
  }
</style>
