<template>
  <el-container class="taskInfo-wrapper">
   <h3 class="header">条形码列表</h3>
   <el-card class="wrapper-head box-card">
     <el-row>
      <el-col :span="6">
         <el-row>
           <el-col :span="6" class="wrapper-head-label">
             <label>条形码</label>
           </el-col>
           <el-col :span="18">
             <el-input
               style="width: 100%"
               placeholder="请输入条码，多个可用空格隔开"
               v-model="searchFormData['barcode']"
               clearable
             >
             </el-input>
           </el-col>
         </el-row>
       </el-col>
       <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>品牌</label>
            </el-col>
            <el-col :span="18">
              <el-select filterable clearable remote :remote-method="(name) => getEntityList(selects, 'brand', name)" v-model="searchFormData.brandId" @focus="getEntityList(selects, 'brand')" placeholder="全部">
                <el-option v-for="(item) in selects.brand" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
       <el-col :span="6">
         <el-row>
           <el-col :span="6" class="wrapper-head-label">
             <label>产品名称</label>
           </el-col>
           <el-col :span="18">
             <el-input
               style="width: 100%"
               placeholder="请输入产品名称"
               v-model="searchFormData['productName']"
               clearable
             >
             </el-input>
           </el-col>
         </el-row>
       </el-col>
       <el-col :span="6">
         <el-row>
           <el-col :span="6" class="wrapper-head-label">
             <label>规格</label>
           </el-col>
           <el-col :span="18">
             <el-input
               style="width: 100%"
               placeholder="请输入规格"
               v-model="searchFormData['specification']"
               clearable
             >
             </el-input>
           </el-col>
         </el-row>
       </el-col>
       <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>公司名称</label>
            </el-col>
            <el-col :span="18">
              <el-input
                style="width: 100%"
                placeholder="请输入公司名称"
                v-model="searchFormData['companyName']"
                clearable
              >
              </el-input>
            </el-col>
          </el-row>
        </el-col>
       <el-col :span="6">
         <el-row>
           <el-col :span="6" class="wrapper-head-label">
             <label>sku编码</label>
           </el-col>
           <el-col :span="18">
             <el-input
               v-model="searchFormData['skuCode']"
               clearable
               placeholder="请输入sku编码"
             />
           </el-col>
         </el-row>
       </el-col>
       <el-col :span="6" style="text-align: right" class="left-auto">
         <el-button size="mini" @click="handleResetSearch">重置</el-button>
         <el-button size="mini" @click="handleSearch" type="primary"
           >搜索</el-button
         >
       </el-col>
     </el-row>
   </el-card>
   <el-card class="wrapper-body box-card">
    <el-button class="mg-b-10" type="primary" @click="handleImport">导入条码</el-button>
     <el-table
       v-loading="loading"
       :data="tableData"
       border
       fit
       highlight-current-row
       :span-method="objectSpanMethod"
     >
       <template slot="empty">
         <div class="empty">
           <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
         </div>
       </template>
       <el-table-column
         v-for="column in tableColumns"
         align="center"
         :prop="column.prop"
         :label="column.label"
         :key="column.prop"
       >
         <template slot-scope="scope">
           <span v-if="column.prop === 'barcode'" class="flex-row">
             <div class="flex-left">
               <ImgCol :imgUrl="scope.row.picUrl || ''" />
             </div>
             <div class="flex-right">
               <span>{{scope.row.productName || ''}}</span>
               <span class="font-12">条码：{{scope.row.barcode || ''}}</span>
               <span class="font-12">规格：{{scope.row.specification || ''}}</span>
               <span class="font-12">品牌：{{scope.row.otherBrandName || ''}}</span>
             </div>
           </span>
           <template v-else-if="column.prop === 'skuCode'">
             <el-link
               v-if="scope.row[column.prop]"
               type="primary"
               target="_blank"
               class="underline_link"
               :href="webUrl(scope.row)"
               :underline="false"
               >{{ scope.row.skuCode}}
             </el-link>
             <span v-else>--</span>
           </template>
           <span v-else>{{ scope.row[column.prop] || '--' }}</span>
         </template>
       </el-table-column>
     </el-table>
     <el-pagination
       @size-change="handleSizeChange"
       @current-change="handleCurrentChange"
       :current-page="currentPage"
       :page-sizes="[10, 50, 100, 200, 300, 400]"
       :page-size="pageSize"
       layout="total, sizes, prev, pager, next, jumper"
       :total="total"
     >
     </el-pagination>
   </el-card>
   <uploadDialog v-if="uploadDialogVisible" :visible.sync="uploadDialogVisible" :callback="getCurrentList"></uploadDialog>
  </el-container>
</template>

<script>
import { cloneDeep } from 'lodash'
import { tableColumns } from './helper'
import { removeProperty, getOtherSystemUrl, delProperty } from '@/utils/dataManage.js'
import { qryBarCodeMetaList } from '@/api/dataManage'
import mixin from './mixin.js'
export default {
  mixins: [mixin],
 components: {
   ImgCol: () => import('./components/ImgCol.vue'),
   uploadDialog: () => import('./components/uploadDialog.vue')
 },
 data() {
   return {
     tableColumns: cloneDeep(tableColumns),
     searchFormData: {
       name: '',
       barCode: '',
       specification: '',
       skuCode: '',
       skuNum: ''
     },
     selects: {
      brand: []
     },
     pageSize: 10,
     currentPage: 1,
     total: 0,
     loading: false,
     tableData: [],
     uploadDialogVisible: false
   }
 },
 computed: {
   webUrl() {
     return function (data) {
       const { skuCode = '' } = data
       if (!skuCode) {
        return ''
       }
       let preUrl = getOtherSystemUrl('cc')
       return `${preUrl}product/skuDetail/${skuCode}?tagName=${skuCode}详情`
     }
   }
 },
 created() {
   this.getCurrentList()
 },
 methods: {
   handleResetSearch() {
     this.searchFormData = {}
     this.getCurrentList()
   },
   async getCurrentList() {
     this.loading = true
     const { success = false, errDesc = '', data = {} } = await qryBarCodeMetaList({
       ...delProperty(this.searchFormData, 'barcode'),
       pageNum: this.currentPage,
       pageSize: this.pageSize
     }).finally(() => {
       this.loading = false
     })
     if (!success) {
       this.$message.error(errDesc || '操作失败')
       return
     }
     const { content = [], totalElements = 0 } = data
     this.tableData = content.reduce((pre, cur) => {
       const { skus = [{}] } = cur
       pre = pre.concat(skus.map((sku, index) => ({
         ...sku,
         ...cur,
         rowspan: (!index && skus.length) || 0
       })))
       return pre
     }, [])
     this.total = totalElements
   },
   handleSearch() {
     this.currentPage = 1
     this.searchFun()
   },
   searchFun() {
    const { barcode = '' } = this.searchFormData
     this.searchFormData = {
       ...removeProperty(this.searchFormData),
       barcodes: barcode.trim()
          .replace(/[\s,，]/g, ' ')
          .replace(/[\s]+/g, ',').split(',')
          .filter(item => item)
     }
     this.getCurrentList()
   },
   handleSizeChange(val) {
     if ((this.currentPage - 1) * val <= this.total) {
       this.pageSize = val
       this.searchFun()
     }
   },
   handleCurrentChange(val) {
     this.currentPage = val
     this.searchFun()
   },
   objectSpanMethod({ row, column, rowIndex, columnIndex }) {
     const { rowspan = 0 } = row
     const { property = '' } = column
     if (columnIndex === 0 && property === 'barcode') {
       if (rowspan) {
         return {
           rowspan,
           colspan: 1
         }
       } else {
         return {
           rowspan: 0,
           colspan: 0
         }
       }
     }
   },
   handleImport() {
    this.uploadDialogVisible = true
   }
 }
}
</script>

<style  lang="less" scoped>
.taskInfo-wrapper{
 margin: 0;
 padding: 10px;
 width: 100%;
 display: flex;
 flex-direction: column;
 .header{
   margin-bottom: 10px;
   font-weight: 300;
 }
 .wrapper-head{
   width: 100%;
   .wrapper-head-label{
     text-align: right;
     padding-right: 10px;
     height:32px;
     line-height: 32px;
     margin-bottom:16px;
   }
   &::v-deep {
     .el-select, .el-date-editor{
       width: 100%;
     }
   }
   .left-auto{
     margin-top: 10px;
     float: right;
   }
 }
 .wrapper-body{
   margin-top: 10px;
   .btn-container{
     margin-bottom: 10px;
     display: flex;
     justify-content: space-between;
   }
   .btn-label{
     color: #5098ff;
   }
   .flex-row{
     display: flex;
     flex-direction: row;
     .flex-right{
       text-align: left;
       padding-left: 10px;
       span {
         width: 100%;
         font-size: 13px;
         font-family: PingFang SC;
         font-weight: 400;
         color: #606266;
         line-height: 22px;
         display: inline-block;
       }
       .font-12{
         font-size: 12px;
       }
     }
   }
   .color-grey{
     color: #b4bccc;
   }

   .color-green{
     color: #67c23a;
   }

   .underline_link{
     text-decoration: underline;
   }
   .mg-b-10{
    margin-bottom: 10px;
   }
 }
 &::v-deep{
   .el-dialog__header{
     border-bottom: 1px solid #EBEEF5;
     display: flex;
   }
   .el-pagination{
     text-align: right;
   }
   .el-upload__input{
     color:#DCDFE6;
     font-size: 12px;
   }
   .el-divider{
     background-color: #597bee;
   }
   .el-button--text{
     color: #597bee;
   }
 }
}
</style>
