<template>
   <el-dialog title="条码数据导入" :visible.sync="modelVisible" width="600px" center :custom-class="'new-dialog'" :close-on-click-modal="false" v-loading.fullscreen.lock="fullscreenLoading">
      <el-form ref="newForm" :model="formData" label-width="120px" :rules="rules">
        <el-form-item label="上传文件" class="mg-b-0">
          <el-upload class="upload-demo" drag :action="uploadUrl" :on-success="onUploadSuccess" :before-upload="beforeUpload" :on-error="onUploadError" :on-remove="handleRemove" accept=".xlsx,.xls" name="file" :limit="1">
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">条码导入清单</div>
                <div class="el-upload__tip">仅支持.xlsx文件</div>
              </el-upload>
        </el-form-item>
        <el-form-item class="line-height-24">
          <label class="href-btn" @click="downloadTemplate">数据模版.xlsx</label>
        </el-form-item>
      </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="handleAdd" :loading="runLoading">确认</el-button>
    </span>
   </el-dialog>
</template>

<script>
import { uploadUrl } from '../helper'
import { uploadBarCodeMetaData, downLoadBarCodeTemp } from '@/api/dataManage'
import { asyncDownload } from '@/utils/dataManage'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    callback: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      uploadUrl,
      runLoading: false,
      fullscreenLoading: false,
      formData: {},
      rules: {
        taskName: [
          { required: true, message: '请输入任务名称' }
        ]

      }
    }
  },
  computed: {
    modelVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    async handleAdd() {
      const { filePath = '' } = this.formData
      let errMsg = !filePath ? '请上传文件' : ''
      if (errMsg) {
        errMsg && this.$message.warning(errMsg)
        return
      }
      this.runLoading = true
      const { success = false, errDesc = '' } = await uploadBarCodeMetaData({
        ossKey: filePath
      }).finally(() => {
        this.runLoading = false
      })
      if (!success) {
        this.$message.error(errDesc)
        return
      }
      this.$message.success('操作成功')
      this.callback && this.callback()
      this.modelVisible = false
    },
    beforeUpload(file) {
      const { name = '', size = '' } = file
      const isLt = size / 1024 / 1024 < 50
      let notExcel =
        '.xlsx,.xls'.indexOf(
          name.substring(name.lastIndexOf('.')).toLowerCase()
        ) === -1
      let msg =
        (this.formData.filePath && '目前只能上传1个文件') ||
        (notExcel && '上传文件类型与要求不符，只允许上传excel文件') ||
        (!isLt && '上传文件大小不能超过 50MB!')
      if (msg) {
        this.$message.error(msg)
        return false
      }
      this.formData.fileName = name
    },
    onUploadSuccess(response = {}) {
      const { link = '', status = 0 } = response?.data || {}
      if (status === 200) {
        // this.$message.success(response?.message || '导入成功！')
        this.formData.filePath = link
      } else {
        this.$message.error(response?.message || '导入失败！')
      }
    },
    handleRemove() {
      this.formData.fileName = ''
      this.formData.filePath = ''
    },
    onUploadError(error = {}) {
      this.$message.error(error?.msg || error?.message || '上传失败')
    },
    async downloadTemplate() {
      this.fullscreenLoading = true
      const { success = false, errDesc = '', data = '' } = await downLoadBarCodeTemp({
         template: 'BarcodeDicImportExcelDTO'
      }).finally(() => {
        this.fullscreenLoading = false
      })
      if (!success) {
        this.$message.error(errDesc || '操作失败')
        return
      }
      asyncDownload(data)
    }
  }
}
</script>

<style lang="less" scoped>
  .href-btn{
    color: #597bee;
    text-decoration: underline;
    text-decoration-color: #597bee;
  }
  .mg-b-0{
    margin-bottom: 0px;
  }
  .line-height-24{
    &::v-deep{
      .el-form-item__content{
        line-height: 24px;
      }
    }
  }
  &::v-deep{
    .el-upload-dragger{
      width: 430px;
      border: 1px solid #d9d9d9;
    }
  }
</style>
