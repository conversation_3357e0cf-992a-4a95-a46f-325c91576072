import { getCCEntityTypes } from '@/api/dataManage.js'
const mixin = {
  methods: {
    async getEntityList(selectsObj, type = '', name = '',) {
      const fetchApi = {
        brand: getCCEntityTypes
      }
      const fetchParams = {
        brand: () => {
          return {
            entityType: 'entity.brand',
            name
          }
        }
      }
      let params = (fetchParams[type] && fetchParams[type]()) || {}
      let { data = [] } = await fetchApi[type](params)

      const handleFetch = {
        brand: () => {
          selectsObj[type] = data
        }
      }

      handleFetch[type]()
    }
  }
}

export default mixin
