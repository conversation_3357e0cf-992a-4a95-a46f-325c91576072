<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="fifter searchBox">
        <el-row :gutter="10">
          <el-col :span="3" class="dialogTitle">任务名称：</el-col>
          <el-col :span="4">
            <el-select
              style="width: 100%"
              clearable
              filterable
              v-model="taskName"
              placeholder="请选择任务名称"
            >
              <el-option
                v-for="(item, idx) in taskNameList"
                :key="idx"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="3" class="dialogTitle">规则类型：</el-col>
          <el-col :span="4">
            <el-select
              style="width: 100%"
              clearable
              v-model="governRuleId"
              placeholder="请选择规则类型"
            >
              <el-option
                v-for="item in ruleTypeList"
                :key="item.id"
                :label="item.ruleName"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :span="24" :gutter="10">
          <el-col :span="3" class="dialogTitle">选择类目：</el-col>
          <el-col :span="4">
            <el-select
              style="width: 100%"
              v-model="categoryLevel1Value"
              :disabled="!categoryListLevel1 || categoryListLevel1.length < 0"
              clearable
              filterable
              @clear="categorySelectClear(1)"
              @change="categorySelect(1)"
              placeholder="请选择"
            >
              <el-option
                v-for="item in categoryListLevel1"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              style="width: 100%"
              v-model="categoryLevel2Value"
              clearable
              filterable
              :disabled="!categoryListLevel2 || categoryListLevel2.length < 0"
              @clear="categorySelectClear(2)"
              @change="categorySelect(2)"
              placeholder="全部"
            >
              <el-option
                v-for="item in categoryListLevel2"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              style="width: 100%"
              v-model="categoryLevel3Value"
              clearable
              filterable
              :disabled="!categoryListLevel3 || categoryListLevel3.length < 0"
              @clear="categorySelectClear(3)"
              @change="categorySelect(3)"
              placeholder="全部"
            >
              <el-option
                v-for="item in categoryListLevel3"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4">
            <el-select
              v-model="categoryLevel4Value"
              clearable
              filterable
              :disabled="!categoryListLevel4 || categoryListLevel4.length < 0"
              @clear="categorySelectClear(4)"
              @change="categorySelect(4)"
              placeholder="全部"
            >
              <el-option
                v-for="item in categoryListLevel4"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="4" style="text-align: right">
            <el-button size="medium" @click="queryData" type="primary"
              >查询</el-button
            >
            <el-button size="medium" @click="resetSearch">重置</el-button>
            <el-button size="medium" @click="handleExport" type="primary"
              >导出</el-button
            >
          </el-col>
        </el-row>
      </div>
      <div class="c_tables">
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column
            align="center"
            prop="categoryId"
            label="类目id"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="categoryName"
            label="类目名称"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="level"
            label="类目级别"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="governRuleName"
            label="规则类型"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="governTotal"
            label="类目下总数"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="needFixCount"
            label="待修复数"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="finishFixCount"
            label="已修复数"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="noNeedFixCount"
            label="无需修复数"
          ></el-table-column>
          <!-- <el-table-column
            align="center"
            prop="syncSuccessCount"
            label="同步成功SKU数"
          ></el-table-column>
          <el-table-column
            align="center"
            prop="noProblemCount"
            label="无需修复SKU数"
          ></el-table-column> -->
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  getCategoriesTree,
  qryRuleList,
  getAllTaskName,
  getNewCategoryTree,
  getGovernStatistic,
  exportGovernStatistics
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
export default {
  data() {
    return {
      loading: {
        page: false,
        table: true,
        account: false,
        create: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      search: {
        // 搜索值
        name: '', // 品名
        alias: '' // 别名
      },
      total: 0,
      tableData: [],
      categoryList: null, // 目录树列表数据
      categoryListLevel1: null, // 目录树第一级列表数据
      categoryListLevel2: null, // 目录树第二级列表数据
      categoryListLevel3: null, // 目录树第三级列表数据
      categoryListLevel4: null, // 目录树第四级列表数据
      categoryLevel1Value: null, // 目录树第一级选中结果
      categoryLevel2Value: null, // 目录树第二级选中结果
      categoryLevel3Value: null, // 目录树第三级选中结果
      categoryLevel4Value: null, // 目录树第四级选中结果
      taskName: '',
      governRuleId: null,
      currAppInfo: null, // 当前app信息,修改时候传递给接口使用
      taskNameList: [],
      ruleTypeList: [],
      currentLevel: -1,
      currCategoryNameMap: {
        '-1': { name: '' },
        1: { name: '' },
        2: { name: '' },
        3: { name: '' },
        4: { name: '' }
      }
    }
  },
  components: {
    Pagination
  },
  created() {
    // this.getCategories()
    this.goGetApps()
    this.getRuleTypeList()
    this.getTaskNameList()
    this.getCategoryTree()
  },

  methods: {
    async getRuleTypeList() {
      try {
        const res = await qryRuleList()
        if (res?.success) {
          this.ruleTypeList = res.data || []
        }
      } catch (err) {}
    },
    async getTaskNameList() {
      try {
        const res = await getAllTaskName()
        if (res?.success) {
          this.taskNameList = res.data || []
        }
      } catch (err) {}
    },
    async getCategoryTree() {
      try {
        const res = await getNewCategoryTree()
        if (res?.success) {
          this.categoryList = res.data || []
          this.categoryListLevel1 = res.data || []
          // this.taskNameList = res.data || []
        }
      } catch (err) {
      } finally {
        // this.loading.page = false
      }
    },
    queryData() {
      this.goGetApps()
    },
    pageClick(page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    getCategories() {
      let data = {
        finalizeState: 0
      }
      getCategoriesTree(data).then((res) => {
        if (res.code === 0) {
          this.categoryList = res.data
          this.categoryListLevel1 = res.data
        } else {
          this.$message.error(res.msg)
        }
        this.loading.page = false
      })
    },
    resetSearch() {
      let This = this
      This.categoryListLevel2 = null
      This.categoryListLevel3 = null
      This.categoryListLevel4 = null
      This.categoryLevel1Value = ''
      This.categoryLevel2Value = ''
      This.categoryLevel3Value = ''
      This.categoryLevel4Value = ''
      // This.tableData = []
      This.listQueryInfo.current = 1
      This.taskName = ''
      This.governRuleId = null
      This.currentLevel = -1
      This.currCategoryNameMap = {
        '-1': { name: '' },
        1: { name: '' },
        2: { name: '' },
        3: { name: '' },
        4: { name: '' }
      }
      This.goGetApps()
    },
    categorySelectClear(level) {
      // 清空选择
      let This = this
      if (level === 1) {
        This.categoryListLevel2 = null
        This.categoryListLevel3 = null
        This.categoryListLevel4 = null
        This.categoryLevel1Value = ''
        This.categoryLevel2Value = ''
        This.categoryLevel3Value = ''
        This.categoryLevel4Value = ''
        This.currentLevel = -1
      } else if (level === 2) {
        This.categoryListLevel3 = null
        This.categoryListLevel4 = null
        This.categoryLevel2Value = ''
        This.categoryLevel3Value = ''
        This.categoryLevel4Value = ''
        This.currentLevel = 1
      } else if (level === 3) {
        This.categoryListLevel4 = null
        This.categoryLevel3Value = ''
        This.categoryLevel4Value = ''
        This.currentLevel = 2
      } else if (level === 4) {
        This.categoryLevel4Value = ''
        This.currentLevel = 3
      }
    },
    categorySelect(level) {
      this.currentLevel = level
      // 树形目录变化时触发
      let This = this
      if (level === 1) {
        let plist = this.categoryListLevel1
        let id = this.categoryLevel1Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.currCategoryNameMap[level].name = plist[i].name
            This.categoryListLevel2 = plist[i].children
            This.categoryListLevel3 = null
            This.categoryListLevel4 = null
            This.categoryLevel2Value = ''
            This.categoryLevel3Value = ''
            This.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 2) {
        let plist = this.categoryListLevel2
        let id = this.categoryLevel2Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.currCategoryNameMap[level].name = plist[i].name
            This.categoryListLevel3 = plist[i].children
            This.categoryListLevel4 = null
            This.categoryLevel3Value = ''
            This.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 3) {
        let plist = this.categoryListLevel3
        let id = this.categoryLevel3Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.currCategoryNameMap[level].name = plist[i].name
            This.categoryListLevel4 = plist[i].children
            This.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 4) {
        let plist = this.categoryListLevel4
        let id = this.categoryLevel4Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.currCategoryNameMap[level].name = plist[i].name
            return false
          }
        }
      }
    },
    goGetApps() {
      // 获取数据列表
      let This = this
      let categoryId =
        This.categoryLevel4Value ||
        This.categoryLevel3Value ||
        This.categoryLevel2Value ||
        This.categoryLevel1Value ||
        -1
      if (categoryId) {
        const { currCategoryNameMap, currentLevel, governRuleId, taskName } =
          This
        This.loading.table = true
        getGovernStatistic({
          categoryId,
          taskName: taskName,
          governRuleId: governRuleId,
          categoryName: currCategoryNameMap[currentLevel].name,
          level: currentLevel
        })
          .then((res) => {
            if (res?.success) {
              let result = res.data || []
              This.tableData = result
            } else {
              This.$message.error(res.msg)
            }
            This.loading.table = false
            This.loading.page = false
          })
          .catch(() => {
            This.loading.table = false
            This.loading.page = false
          })
      } else {
        this.$message.warning('请先选择类目')
      }
    },
    async handleExport() {
      const This = this
      const categoryId =
        This.categoryLevel4Value ||
        This.categoryLevel3Value ||
        This.categoryLevel2Value ||
        This.categoryLevel1Value ||
        -1
      try {
        const { currCategoryNameMap, currentLevel, governRuleId, taskName } =
          This
        const res = await exportGovernStatistics({
          categoryId,
          categoryName: currCategoryNameMap[currentLevel].name,
          governRuleId: governRuleId,
          level: currentLevel,
          taskName: taskName
        })
        if (res?.success) {
          this.$message.success('导出成功！')
        }
      } catch (error) {}
    }
  }
}
</script>

<style lang="scss" scoped>
  .zZindex {
    z-index:99999 !important;
  }
  .formts {
    p.ts {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }
  }
  .dialogTitle {
    text-align: right;
    box-sizing: border-box;
    padding-right: 10px;
    line-height: 32px;
    i {
      color: red;
      position: relative;
      top: 2px;
      right: 4px;
    }
  }
  .rulesetcont {
    padding-bottom: 15px;
    .el-row {
      margin-bottom: 10px;
    }
  }
  .v-modal {
    z-index: 1998;
  }
  .page_center {
    .searchBox {
      padding: 20px 0;
      border: 1px solid #eee;
      margin: 20px 0;
    }
    .dialogTitle {
      text-align: right;
      box-sizing: border-box;
      padding-right: 2px;
      line-height: 32px;
      i {
        color: red;
        position: relative;
        top:3px;
        right: 4px;
      }
    }
    .c_pagination {
      text-align: right;
      padding: 20px;
    }
    .el-row {
      margin-bottom: 10px;
      .item-btn {
        position: relative;
        .item-close {
          position: absolute;
          right:-8px;
          top:-8px;
          font-size: 16px;
        }
      }
    }
  }
  .empty {
    padding: 150px 0;
    text-align: center;
    p {
      font-size: 16px;
      color: #999;
    }
  }
  .selectLoading {
    font-size: 12px;
    color: #ccc;
    text-align: center;
    height: 14px;
    line-height: 14px;
  }
</style>
