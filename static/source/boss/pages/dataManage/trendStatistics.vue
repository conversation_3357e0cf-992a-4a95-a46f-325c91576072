<template>
  <div class="page trend-statistics-container" v-loading="loading.page">
    <!--顶部数据统计-->
    <div class="trend-statistics-number" v-if="topDatas">
      <el-row :span="24" :gutter="20">
        <el-col :span="6">
          <div class="item">
            <div class="title">总检出数</div>
            <div class="total">{{topDatas.detectionTotal | numberFormat}}</div>
            <el-row :span="24" class="trend">
              <el-col :span="12">周环比 {{topDatas.compareWeek}}% <Triangle :trendState="topDatas.compareWeek"></Triangle></el-col>
              <el-col :span="12">日环比 {{topDatas.compareDay}}% <Triangle :trendState="topDatas.compareDay"></Triangle></el-col>
            </el-row>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="item" style="padding: 20px 0;">
            <div class="title" style="padding:0 20px;">检测规则数</div>
            <el-row :span="24" class="rule">
              <el-col :span="8" class="circle_progress">
                <el-progress type="circle"
                             :percentage="topDatas.propertyRuleRatio"
                             :stroke-width="12"
                             stroke-linecap="butt"
                             :width="80"
                             color="#02A7F0"
                ></el-progress>
               <div class="circle_txt">
                 <div>
                   <p>总规则</p>
                   <p>{{topDatas.ruleTotal | numberFormat}}</p>
                 </div>
               </div>
              </el-col>
              <el-col :span="16" class="text">
                <p><i class="dot1"></i><span class="span1">属性规则</span><span class="span2"><i>|</i>{{topDatas.propertyRuleRatio}}%</span><span class="span3">{{topDatas.propertyRuleTotal | numberFormat}}</span></p>
                <p><i class="dot2"></i><span class="span1">算法规则</span><span class="span2"><i>|</i>{{topDatas.algorithmRuleRatio}}%</span><span class="span3">{{topDatas.algorithmRuleTotal  | numberFormat}}</span></p>
              </el-col>
            </el-row>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="item">
            <div class="title">违规率</div>
            <div class="total">{{topDatas.illegalRatio}}%</div>
            <el-row :span="24" class="trend">
              <el-col :span="12">周环比 {{topDatas.compareWeek}}% <Triangle :trendState="topDatas.compareWeek"></Triangle></el-col>
              <el-col :span="12">日环比 {{topDatas.compareDay}}% <Triangle :trendState="topDatas.compareDay"></Triangle></el-col>
            </el-row>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="item">
            <div class="title">检测范围</div>
            <div class="total">{{topDatas.monitorScope | numberFormat}}</div>
            <div class="nums">
              <el-row :span="24" :gutter="10">
                <el-col :span="16"><div class="pro pro1" :style="'width:'+getpercent(topDatas.algorithmRuleCheckTotal,topDatas.monitorScope)+'%;'">{{topDatas.algorithmRuleCheckTotal | numberFormat}}</div></el-col>
                <el-col :span="8"><i class="pro1i"></i>算法检测</el-col>
              </el-row>
              <el-row :span="24" :gutter="10">
                <el-col :span="16"><div class="pro pro2" :style="'width:'+getpercent(topDatas.propertyRuleCheckTotal,topDatas.monitorScope)+'%; color:'+(getpercent(topDatas.propertyRuleCheckTotal,topDatas.monitorScope)>5 ? '#fff' : '#333')+';'">{{topDatas.propertyRuleCheckTotal | numberFormat}}</div></el-col>
                <el-col :span="8"><i class="pro2i"></i>属性检测</el-col>
              </el-row>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!--顶部数据统计 end-->
    <div class="trend-statistics-content">
      <el-tabs v-model="dataType" @tab-click="dataTypeClick">
        <el-tab-pane label="检 出" name="1"></el-tab-pane>
        <el-tab-pane label="修 复" name="2"></el-tab-pane>
        <el-tab-pane label="未修复" name="3"></el-tab-pane>
      </el-tabs>
      <!--中间echarts折线图部分-->
      <el-row class="trend-statistics-ecahrts" :span="24">
        <el-col :span="14" style="position: relative;">
          <div class="tabs">
            <el-radio-group v-model="echartCont" @change="echartContTab">
              <el-radio-button label="number">数量</el-radio-button>
              <el-radio-button label="proportion">比例</el-radio-button>
            </el-radio-group>
          </div>
          <div id="trend-echart" v-loading="loading.echart" style="width: 100%;height:400px;"></div>
        </el-col>
        <el-col :span="10">
          <div class="filters-box">
            <el-row :span="24">
              <el-col :span="8">
                <el-tabs v-model="dateTabValue" @tab-click="dateTabClick" style=" text-align: center;">
                  <el-tab-pane style="display: inline-block;" label="本 周" name="week"></el-tab-pane>
                  <el-tab-pane style="display: inline-block;" label="本 月" name="month"></el-tab-pane>
                </el-tabs>
              </el-col>
              <el-col :span="16" style="padding-top: 5px;">
                <el-date-picker
                  :picker-options="pickerOptions"
                  @change="dateRangeChange"
                  style="max-width:100%;"
                  v-model="dateValue"
                  type="daterange"
                  range-separator="~"
                  :clearable="false"
                  :default-value="defaultValue"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期">
                </el-date-picker>
              </el-col>
            </el-row>
            <el-row>
              <div class="dimension">
                <div class="dimension-title">维度选择</div>
                <div class="dimension-selects">
                  <el-radio-group size="medium" v-model="dimensionTabValue" @change="dimensionTab">
                    <el-radio-button label="material">物料组</el-radio-button>
                    <el-radio-button label="rule">规 则</el-radio-button>
                    <el-radio-button label="category">类 目</el-radio-button>
                  </el-radio-group>
                  <div style="text-align: center; margin-top: 40px;">
                    <el-row :span="24" v-show="dimensionTabValue === 'material'">
                      <el-col :span="24">
                        <el-select
                          @change="materialChange"
                          v-model="materialValue"
                          filterable
                          placeholder="请选择">
                          <el-option
                            v-for="item in materialOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-row>
                    <el-row :span="24" :gutter="20" v-show="dimensionTabValue === 'rule'">
                      <el-col :span="8" :offset="4">
                        <el-select  @change="ruleChange1" v-model="ruleValue1" placeholder="请选择">
                          <el-option
                            v-for="item in ruleOptions1"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-col>
                      <el-col :span="8">
                        <el-select @change="ruleChange2" v-model="ruleValue2" placeholder="请选择">
                          <el-option
                            v-for="item in ruleOptions2"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-row>
                    <el-row :span="24" v-show="dimensionTabValue === 'category'">
                      <el-col :span="24">
                        <el-select @change="categoryChange" v-model="categoryValue" placeholder="请选择">
                          <el-option
                            v-for="item in categoryOptions"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id">
                          </el-option>
                        </el-select>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </el-row>
          </div>
        </el-col>
      </el-row>
      <!--中间echarts折线图部分 end-->
      <!--底部排行-->
      <el-row class="trend-statistics-ranks" v-if="rankData" :span="24" :gutter="20">
        <!--排行榜-->
        <el-col class="item" v-loading="loading.rank" :span="8">
          <DimensionRank :numberData="rankData.productGroupRank.amount" :proportionData="rankData.productGroupRank.ratio" dimensionType="material"></DimensionRank>
        </el-col>
        <el-col class="item" v-loading="loading.rank" :span="8">
          <DimensionRank :numberData="rankData.ruleRank.amount" :proportionData="rankData.ruleRank.ratio" dimensionType="rule"></DimensionRank>
        </el-col>
        <el-col class="item" v-loading="loading.rank" :span="8">
          <DimensionRank :numberData="rankData.categoryRank.amount" :proportionData="rankData.categoryRank.ratio" dimensionType="category"></DimensionRank>
        </el-col>
        <!--排行榜 end-->
      </el-row>
      <!--底部排行 end-->
    </div>
  </div>
</template>
<script>
import {
  getCategoriesTree,
  getProductGroup,
  getFactRule,
  getTrendEchartData,
  getTrendRankData,
  getTrendDatas
} from '@/api/dataManage'
import Triangle from './common/triangle'
import DimensionRank from './common/dimensionRank'
const echarts = window.echarts
export default {
  name: 'trendStatistics',
  data () {
    return {
      loading: {
        page: true,
        rank: false,
        echart: false
      },
      dataType: '1', // 1 检出  2 修复 3 未修复
      echartCont: 'number', // 统计类型  number 数量 proportion 比例
      dimensionTabValue: 'material', // 维度  material:物料；rule:规则；类目:category
      dateTabValue: 'week', // 统计类型  week:本周  month:月份
      dateValue: '', // 选择日期阶段
      currDateValue: '', // 日期阶段改变前的上一次选择结果（用于此次选择不合理后，恢复上次的值用）
      pickerOptions: {
        onPick: (obj) => {
          console.log('onPick')
          console.log(obj)
          this.pickerMinDate = new Date(obj.minDate).getTime()
          console.log(this.pickerMinDate)
        },
        disabledDate: (time) => {
          const day1 = 1 * 24 * 3600 * 1000 // 一天的毫秒数
          if (this.pickerMinDate) {
            // 选中一个最小日期后，最大选择范围是90天（前后90天且不能大于当前日期）
            let maxTime = this.pickerMinDate + day1 * 90
            let minTime = this.pickerMinDate - day1 * 90
            return time.getTime() > maxTime || time.getTime() < minTime || time.getTime() > Date.now() - day1
          } else {
            return time.getTime() > Date.now() - day1
          }
        }
      },
      defaultValue: '', // 日期默认显示的月份
      materialOptions: [], // 物料选择
      materialValue: '',
      ruleOptions1: [{ // 规则选择1
        id: '0',
        name: '全部'
      }, {
        id: '1',
        name: '属性规范'
      }, {
        id: '2',
        name: '算法规范'
      }],
      ruleValue1: '0',
      ruleOptions2: [], // 规则选择2
      ruleValue2: '',
      categoryOptions: [], // 类目选择
      categoryValue: '',
      trendData: null, // 趋势图总数据（从接口取出来的）
      rankData: null, // 排行榜总数据（从接口取出来的）
      topDatas: null // 顶部数据大集合（从接口取出来的）
    }
  },
  created () {
    this.getCategories()
    this.getdefaultValue()
    this.getNowDateSlot()
    this.goGetProductGroup()
    this.goGetFactRule()
    this.dataTypeClick()
    this.goGetTrendDatas()
    this.initChart()
  },
  components: {
    Triangle,
    DimensionRank
  },
  filters: {
    numberFormat (num) {
      // 数字格式化：每三位以“,”分割
      let result = ''
      let counter = 0
      num = (num || 0).toString()
      for (let i = num.length - 1; i >= 0; i--) {
        counter++
        result = num.charAt(i) + result
        if (!(counter % 3) && i !== 0) { result = ',' + result; }
      }
      return result
    }
  },
  mounted () {
    /***
    let xdata = ['2021-05-27', '2021-05-28', '2021-05-29', '2021-05-30', '2021-05-30', '2021-05-31', '2021-06-01', '2021-05-30', '2021-05-31', '2021-06-01'
    ]
    let ydata = [
      [ // 全部数据
        300, 264, 301, 258, 190, 360, 510, 150, 262, 301
      ],
      [ // 动销数据
        150, 232, 201, 154, 190, 330, 410, 150, 232, 201
      ],
      [ // 非动销数据
        130, 32, 2, 104, 0, 30, 100, 0, 30, 100
      ]
    ]
    this.setChart(xdata, ydata)**/
  },
  methods: {
    getdefaultValue () {
      // 设置日历显示的默认日期是上个月
      const date = new Date()
      let year = date.getFullYear() // 年
      let month = date.getMonth() // 月
      month = month + ''
      if (month === '0') {
        month = '12'
        year = year - 1
      }
      if ((month + '').length === 1) {
        month = '0' + month
      }
      let day = date.getDate() // 日
      if ((day + '').length === 1) {
        day = '0' + day
      }
      let defaultValue = year + '-' + month + '-01'
      // console.log('默认当前日期')
      // console.log(defaultValue)
      this.defaultValue = defaultValue
    },
    getDateBefore (n) {
      /**
       * 获取n天前的年月日
       * */
      let nowstr = new Date().getTime() // 当前日期的毫秒数
      let nstr = n * 1 * 24 * 60 * 60 * 1000 // n天的毫秒数
      let ndate = new Date(nowstr - nstr) // n天之前的时间
      let year = ndate.getFullYear() // 年
      let month = ndate.getMonth() + 1 // 月
      if ((month + '').length === 1) {
        month = '0' + month
      }
      let day = ndate.getDate() // 日
      if ((day + '').length === 1) {
        day = '0' + day
      }
      return year + '-' + month + '-' + day
    },
    getNowDateSlot (type) {
      /**
       * 获取本周，本月的起止日期
       * 1.实质上查的时本周一到昨天的、本月1号到昨天的起止日期（因为是T+1做的统计，所以今天的统计不到）
       * 2.如果当天是周一或者1号，name时间段就是上周一周的或上个月一月的
       * **/
      /**
       * 获取本周、本月、上月的开始日期、结束日期
       */
      let now = new Date() // 当前日期
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1) // 获取昨天的日期
      let nowDayOfWeek = now.getDay() // 今天本周的第几天
      let nowDay = now.getDate() // 当前日
      let nowMonth = now.getMonth() // 当前月
      let nowYear = now.getYear() // 当前年
      nowYear += (nowYear < 2000) ? 1900 : 0
      let lastMonthDate = new Date() // 上月日期
      lastMonthDate.setDate(1)
      lastMonthDate.setMonth(lastMonthDate.getMonth() - 1)
      let lastMonth = lastMonthDate.getMonth()
      // 格式化日期：yyyy-MM-dd
      function formatDate(date) {
        let myyear = date.getFullYear()
        let mymonth = date.getMonth() + 1
        let myweekday = date.getDate()
        if (mymonth < 10) {
          mymonth = '0' + mymonth
        }
        if (myweekday < 10) {
          myweekday = '0' + myweekday
        }
        return (myyear + '-' + mymonth + '-' + myweekday)
      }
      // 获得某月的天数
      function getMonthDays (myMonth) {
        let monthStartDate = new Date(nowYear, myMonth, 1)
        let monthEndDate = new Date(nowYear, myMonth + 1, 1)
        let days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24)
        return days;
      }
      // 获得本月的开始日期
      function getMonthStartDate () {
        let monthStartDate = new Date(nowYear, nowMonth, 1)
        return formatDate(monthStartDate)
      }
      // 获得上月开始时间
      function getLastMonthStartDate () {
        if (nowDay === 1 && nowMonth === 0) {
          // 1月1号
          nowYear = nowYear - 1
        }
        let lastMonthStartDate = new Date(nowYear, lastMonth, 1)
        return formatDate(lastMonthStartDate)
      }

      // 获得上月结束时间
      function getLastMonthEndDate () {
        let lastMonthEndDate = new Date(nowYear, lastMonth, getMonthDays(lastMonth));
        return formatDate(lastMonthEndDate)
      }
      // console.log('本周起止日期')
      // console.log(getWeekStartDate() + '***' + getWeekEndDate())
      // console.log('本月起止日期')
      // console.log(getMonthStartDate() + '***' + getMonthEndDate())
      // console.log('上月起止日期')
      // console.log(getLastMonthStartDate() + '***' + getLastMonthEndDate())
      // 获取上周起始时间结束时间、下周起始时间结束时间开始时间和本周起始时间结束时间;（西方）
      function getWeeksDate (n) {
        let now = new Date()
        let year = now.getFullYear()
        // 因为月份是从0开始的,所以获取这个月的月份数要加1才行
        let month = now.getMonth() + 1
        let date = now.getDate()
        let day = now.getDay()
        // 判断是否为周日,如果不是的话,就让今天的day-1(例如星期二就是2-1)
        if (day !== 0) {
          n = n + (day - 1)
        } else {
          n = n + day
        }
        if (day) {
          // 这个判断是为了解决跨年的问题
          if (month > 1) {
          } else { // 这个判断是为了解决跨年的问题,月份是从0开始的
            year = year - 1
            month = 12
          }
        }
        now.setDate(now.getDate() - n)
        year = now.getFullYear()
        month = now.getMonth() + 1
        date = now.getDate()
        // console.log(n);
        let s = year + '-' + (month < 10 ? ('0' + month) : month) + '-' + (date < 10 ? ('0' + date) : date)
        return s
      }
      // 参数都是以周一为基准的
      // 上周开始日期 getWeeksDate(7)
      // 上周结束日期 getWeeksDate(1)
      // 本周开始日期 getWeekStartDate()
      // 昨天日期 formatDate(yesterday)
      // 本月开始日期 getMonthStartDate()
      // 上月开始日期 getLastMonthStartDate()
      // 上月结束日期 getLastMonthEndDate()
      let weekStartDate = '' // 周起始日期
      let weekEndDate = '' // 周结束日期
      let monthStartDate = '' // 月起始日期
      let monthEndDate = '' // 月结束日期
      if (nowDayOfWeek === 1) {
        // console.log('是周一')
        // 如果是周一
        weekStartDate = getWeeksDate(7) // 上周开始日期
        weekEndDate = getWeeksDate(1) // 上周结束日期
      } else {
        // console.log('不是周一')
        // 兼容西方 周日是一周开始的问题
        if (nowDayOfWeek === 0) {
          weekStartDate = getWeeksDate(6) // 本周开始日期
        } else {
          weekStartDate = getWeeksDate(0) // 本周开始日期
        }
        weekEndDate = formatDate(yesterday) // 本周结束日期
      }
      // console.log('周起始日期', weekStartDate, weekEndDate)
      if (nowDay === 1) {
        // console.log('是1号')
        // 如果是1号
        monthStartDate = getLastMonthStartDate() // 上月起始日期
        monthEndDate = getLastMonthEndDate() // 上月结束日期
      } else {
        // console.log('不是1号')
        monthStartDate = getMonthStartDate() // 本月起始日期
        monthEndDate = formatDate(yesterday) // 本月结束日期
      }
      // console.log('月起始日期', monthStartDate, monthEndDate)
      if (type === 'week') {
        // 返回周起始日期
        return [weekStartDate, weekEndDate]
      } else if (type === 'month') {
        // 返回月起始日期
        return [monthStartDate, monthEndDate]
      }
    },
    getpercent (num1, num2) {
      // 根据数值，总数；百分比
      let result = 0
      if (num2 !== 0) {
        result = (num1 / num2 * 100).toFixed(2)
      }
      return parseFloat(result)
    },
    dataTypeClick(tab, event) {
      // 问题类型切换（检出、修复、未修复）
      console.log('问题类型切换')
      console.log(this.dataType)
      this.initChart()
      let checkType = ''
      if (this.dataType === '1') {
        checkType = '检出'
      } else if (this.dataType === '2') {
        checkType = '修复'
      } else if (this.dataType === '3') {
        checkType = '未修复'
      } else {
        console.log('啥也不是')
      }
      this.goGetTrendRankData(checkType)
    },
    dateTabClick (tab, event) {
      // 日期切换
      console.log('切换——日期')
      console.log(this.dateTabValue)
      this.dateValue = ''
      this.initChart()
    },
    echartContTab (e) {
      console.log('切换——数量、比例')
      console.log(e)
      if (this.trendData) {
        let xdata = [] // 日期
        let ydata = [[], [], []] // 数值
        for (let item of this.trendData) {
          if (e === 'number') {
            ydata[0].push(item.amount)
          } else if (e === 'proportion') {
            ydata[0].push(item.ratio)
          }
          xdata.push(item.time.replace(/-/g, '.'))
        }
        console.log('xy值')
        console.log(xdata)
        console.log(ydata)
        this.setChart(xdata, ydata)
      }
    },
    dimensionTab (e) {
      console.log('切换-维度')
      console.log(e)
      this.initChart()
    },
    dateRangeChange (val) {
      console.log('日期修改')
      console.log(val)
      let startTime = val[0].getTime()
      let endTime = val[1].getTime()
      if (startTime === endTime) {
        this.$message.error('开始日期和结束日期不能为同一天')
        this.dateValue = this.currDateValue
        return false
      }
      this.currDateValue = this.dateValue
      this.dateTabValue = ''
      this.initChart()
    },
    materialChange () {
      console.log('切换物料选项')
      console.log(this.materialValue)
      this.initChart()
    },
    ruleChange1 () {
      console.log('切换规则1选项')
      console.log(this.ruleValue1)
      this.ruleValue2 = ''
      let name = ''
      if (this.ruleValue1 === '0') {
        name = ''
      } else if (this.ruleValue1 === '1') {
        name = '属性规范'
      } else if (this.ruleValue1 === '2') {
        name = '算法规范'
      }
      this.goGetFactRule(name)
      this.initChart()
    },
    ruleChange2 () {
      console.log('切换规则2选项')
      console.log(this.ruleValue2)
      this.initChart()
    },
    categoryChange () {
      console.log('切换类目选项')
      console.log(this.categoryValue)
      this.initChart()
    },
    initChart () {
      function timeFormat (datestr) {
        console.log('时间格式化之前')
        console.log(datestr)
        const date = new Date(datestr)
        let year = date.getFullYear() // 年
        let month = date.getMonth() // 月
        month = month + 1 + ''
        if (month === '0') {
          month = '12'
          year = year - 1
        }
        if ((month + '').length === 1) {
          month = '0' + month
        }
        let day = date.getDate() // 日
        if ((day + '').length === 1) {
          day = '0' + day
        }
        return year + '-' + month + '-' + day
      }
      /**
       * 筛选条件
       *（1）检出 修复 未检出
       *（2）数量 比列
       *（3）本周 本月 时间段
       *（4）维度：物料组 规则 类目
       *
       */
      console.log('筛选条件')
      console.log('(1)=' + this.dataType) // 1 检出  2 修复 3 未修复
      console.log('(2)=' + this.echartCont) // 统计类型  number 数量 proportion 比例
      console.log('(3-1)=' + this.dateTabValue)// 统计类型  week:本周  month:月份
      console.log('(3-2)=' + this.dateValue)
      let echartCont = this.echartCont
      let startDate = ''
      let endDate = ''
      let dateTabValue = this.dateTabValue
      if (dateTabValue) {
        // 本周或本月
        /**
         * 最早真正的本周和本月
         * **/
        /**
        let dateArray = this.getNowDateSlot(this.dateTabValue)
        startDate = dateArray[0]
        endDate = dateArray[1]**/
        // 本周为 最近7天的，本月为最近30天的
        if (dateTabValue === 'week') {
          startDate = this.getDateBefore(7)
        } else if (dateTabValue === 'month') {
          startDate = this.getDateBefore(30)
        }
        endDate = this.getDateBefore(1)
      } else if (this.dateValue) {
        // 固定时间段
        let dateArray = this.dateValue
        startDate = timeFormat(dateArray[0])
        endDate = timeFormat(dateArray[1])
      }
      console.log('开始时间=' + startDate, '结束时间=' + endDate)
      let dimensionTabValue = this.dimensionTabValue // material:物料；rule:规则；类目:category
      console.log('(4)=' + dimensionTabValue)

      let checkType = ''
      if (this.dataType === '1') {
        checkType = '检出'
      } else if (this.dataType === '2') {
        checkType = '修复'
      } else if (this.dataType === '3') {
        checkType = '未修复'
      } else {
        console.log('啥也不是')
      }
      let productGroupId = '' // 选中物料id
      let ruleId = '' // 选中规则id
      let level1id = '' // 一级类目id
      let level1name = '' // 一级类目名字
      // 维度
      if (dimensionTabValue === 'material') {
        // 物料
        console.log('物料值=' + this.materialValue)
        productGroupId = this.materialValue ? this.materialValue : ''
        ruleId = ''
        level1id = ''
      } else if (dimensionTabValue === 'rule') {
        // 规则
        console.log('规则值1=' + this.ruleValue1)
        console.log('规则值2=' + this.ruleValue2)
        productGroupId = ''
        ruleId = this.ruleValue2
        level1id = ''
      } else if (dimensionTabValue === 'category') {
        // 类目
        console.log('类目=' + this.categoryValue)
        productGroupId = ''
        ruleId = ''
        level1id = this.categoryValue ? this.categoryValue : ''
      }
      let param = {
        startTime: startDate,
        endTime: endDate,
        level1id: level1id,
        level1name: level1name,
        productGroupId: productGroupId,
        ruleId: ruleId,
        checkType: checkType
      }
      console.log('趋势图参数集合')
      console.log(param)
      this.loading.echart = true
      getTrendEchartData(param).then(res => {
        console.log(res);
        this.loading.echart = false
        if (res.code === 0) {
          console.log('获取到的数据')
          console.log(res.data)
          let xdata = [] // 日期
          let ydata = [[], [], []] // 数值
          if (res.data.allData) {
            this.trendData = res.data.allData
            for (let item of res.data.allData) {
              if (echartCont === 'number') {
                ydata[0].push(item.amount)
              } else if (echartCont === 'proportion') {
                ydata[0].push(item.ratio)
              }
              xdata.push(item.time.replace(/-/g, '.'))
            }
          }
          console.log('xy值')
          console.log(xdata)
          console.log(ydata)
          this.setChart(xdata, ydata)
          this.loading.page = false
        } else {
          this.$message.error(res.msg)
        }
      })
      // 根据筛选条件获取数据，格式化成表格需要的格式，初始化echart
      /**
       * 下面数据是模拟使用的

      let xdata2 = [
        '2021-05-27', '2021-05-28', '2021-05-29', '2021-05-30', '2021-05-30', '2021-05-31', '2021-06-01', '2021-05-30', '2021-05-31', '2021-06-01',
        '2021-05-27', '2021-05-28', '2021-05-29', '2021-05-30', '2021-05-30', '2021-05-31', '2021-06-01', '2021-05-30', '2021-05-31', '2021-06-01'
      ]
      let ydata2 = [
        [ // 全部
          20, 200, 191, 50, 190, 300, 310, 190, 300, 310,
          20, 200, 191, 50, 190, 300, 310, 190, 300, 310
        ],
        [ // 动销数据
          150, 232, 201, 154, 190, 330, 410, 150, 232, 201,
          150, 232, 201, 154, 190, 330, 410, 150, 232, 201
        ],

        [ // 非动销数据
          130, 32, 2, 104, 0, 30, 100, 0, 30, 100,
          130, 32, 2, 104, 0, 30, 100, 0, 30, 100
        ]
      ]
      this.setChart(xdata, ydata)* */
    },
    setChart (xdata, ydata) {
      const myChart = echarts.init(document.getElementById('trend-echart'))
      // 指定图表的配置项和数据
      const option = {
        title: {
          text: '检出趋势'
        },
        tooltip: {
          formatter: function (a, b, c, d) {
            console.log(a)
            console.log(b)
            console.log(c)
            console.log(d)
            let str = a.seriesName + '：' + a.value + '<br />日期：' + a.name
            return str
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          right: 5,
          top: 0,
          bottom: 20,
          itemHeight: 10, // 改变圆圈大小
          itemGap: 10, // 改变item间距
          icon: 'circle',
          data: ['全部数据', '动销数据', '非动销数据']
        },
        grid: {
          top: '20%',
          left: '1%',
          right: '4.5%  ',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xdata // ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        series: [
          {
            name: '全部数据',
            type: 'line',
            symbol: 'circle',
            stack: '全部',
            data: ydata[0], // [120, 132, 101, 134, 90, 230, 210],
            color: '#F59A23', // 改变折线点的颜色
            lineStyle: {
              color: '#F59A23' // 改变折线颜色
            }
          }
        ],
        series2: [
          // 有动销、非动销数据时候使用，暂时只有总数据，所以先隐藏
          {
            name: '全部数据',
            type: 'line',
            symbol: 'circle',
            stack: '全部',
            data: ydata[0], // [120, 132, 101, 134, 90, 230, 210],
            color: '#F59A23', // 改变折线点的颜色
            lineStyle: {
              color: '#F59A23' // 改变折线颜色
            }
          },
          {
            name: '动销数据',
            type: 'line',
            symbol: 'circle',
            stack: '动销数据',
            data: ydata[1], // [220, 182, 191, 234, 290, 330, 310],
            color: '#02A7F0',
            lineStyle: {
              color: '#02A7F0'
            }
          },
          {
            name: '非动销数据',
            type: 'line',
            symbol: 'circle',
            stack: '非动销数据',
            data: ydata[2], // [150, 232, 201, 154, 190, 330, 410],
            color: '#D9001B',
            lineStyle: {
              color: '#D9001B'
            }
          }
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
    },
    getCategories () {
      this.loading.tree = true
      let data = {
        finalizeState: 0 // 0 默认值获取全部类目  1 获取定稿类目
      }
      getCategoriesTree(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          let arr = {
            id: 0,
            name: '全部',
            number: ''
          }
          let categoryOptions = res.data
          categoryOptions.unshift(arr)
          this.categoryOptions = categoryOptions
          this.categoryValue = 0
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goGetProductGroup () {
      let data = {}
      getProductGroup(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          let arr = {
            id: 0,
            name: '全部',
            number: ''
          }
          let materialOptions = res.data
          materialOptions.unshift(arr)
          this.materialOptions = materialOptions
          this.materialValue = 0
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goGetFactRule (name) {
      let data = {
        ruleSceneType: name
      }
      getFactRule(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          this.ruleOptions2 = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goGetTrendRankData (name) {
      // 排行榜数据
      let data = {
        checkType: name
      }
      this.loading.rank = true
      getTrendRankData(data).then(res => {
        if (res.code === 0) {
          this.rankData = res.data
          /***
          if (!this.rankData) {
            this.rankData = res.data
          } else {
            this.$set(this.rankData.categoryRank, 'amount', res.data.categoryRank.categoryRank.amount)
            this.$set(this.rankData.productGroupRank, 'amount', res.data.productGroupRank.productGroupRank.amount)
            this.$set(this.rankData.ruleRank, 'amount', res.data.ruleRank.ruleRank.amount)
          }**/
          this.loading.rank = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goGetTrendDatas () {
      // 顶部数据集合
      getTrendDatas().then(res => {
        console.log(res);
        if (res.code === 0) {
          this.topDatas = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../style/colors.scss";
.trend-statistics-container{
  background: #f0f0f0;
  min-height: 800px;
  .trend-statistics-number {
    .item {
      background: #fff;
      height: 180px;
      box-sizing: border-box;
      padding: 20px 10px;
      .title {
        font-size: 14px;
        color: #666;
      }
      .total {
        font-size: 20px;
        color: #333;
        font-weight: bold;
        padding: 20px 30px 10px;
      }
      .trend {
        text-align: center;
        padding: 10px 0 20px;
        font-size: 14px;
        white-space: nowrap;
      }
      .nums {
        padding-top: 10px;
        .el-row {
          padding-bottom: 5px;
        }
        .pro {
          text-align: left;
          text-indent: 4px;
          box-sizing: border-box;
          font-size: 12px;
          padding:2px 0;
        }
        .pro1 {
          width: 100%;
          background: #fff;
          border: 1px solid #333;
          color: #333;
        }
        .pro2 {
          width: 40%;
          background: $color-blue-200;
          border: 1px solid #333;
          color: #333;
        }
        .pro1i {
          width: 12px;
          height: 12px;
          border: 1px solid #333;
          border-radius: 100%;
          display: inline-block;
          margin-right: 4px;
        }
        .pro2i {
          width: 12px;
          height: 12px;
          background: $color-blue-200;
          border: 1px solid #333;
          border-radius: 100%;
          display: inline-block;
          margin-right: 4px;
        }
      }
      .rule {
        padding-top: 20px;
        .dot1 {
          width: 12px;
          height: 12px;
          background: #02A7F0;
          border: 1px solid #333;
          border-radius: 100%;
          display: inline-block;
          margin-right: 4px;
        }
        .dot2 {
          width: 12px;
          height: 12px;
          background: #8400FF;
          border: 1px solid #333;
          border-radius: 100%;
          display: inline-block;
          margin-right: 4px;
        }
        .span1 {

        }
        .span2 {
          color: #999;
          padding: 0 4px;
          i {
            position: relative;
            top: -1px;
            margin: 0 4px;
          }
        }
        .span3 {

        }
        .text {
          padding-top: 16px;
          p {
            padding-bottom: 6px;
            white-space: nowrap;
          }
        }
      }
    }
  }
  .trend-statistics-content {
    background: #fff;
    margin-top: 20px;
    padding: 20px;
    .trend-statistics-ecahrts {
      border: 1px solid #ccc;
      padding:20px 10px 10px;
      .tabs {
        position: absolute;
        left: calc(50% - 50px);
        top: 0;
        z-index: 10;
      }
      .filters-box {
        padding:0 30px;
        .dimension {
          .dimension-title {
            text-align: left;
            font-size: 16px;
            position: relative;
            margin-bottom: 15px;
            margin-top: 15px;
            &::before {
              content: "";
              position: absolute;
              left: 80px;
              top: 12px;
              height: 1px;
              width: calc(100% - 80px);
              background: #ccc;
            }
          }
          .dimension-selects {
            text-align: center;
          }

        }
      }
    }
    .trend-statistics-ranks {
      margin-top: 20px;
    }
  }
}
</style>
<style lang="scss">
  .circle_progress {
    text-align: center;
    position: relative;
    svg path:first-child {
      stroke: #8400FF;
    }
    .circle_txt {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      div {
        display: inline-block;
        p {
          font-size: 12px;
          line-height: 14px;
          white-space: nowrap;
        }
      }
    }
    .el-progress__text {
      display: none;
    }
  }
  .filters-box {
    .el-tabs__nav-wrap::after {
      display: none;
    }
    .el-tabs__nav {
      position: relative;
      right: -20px;
    }
  }
  .el-tabs__content {
    display: none;
  }
  .trend-statistics-ranks {
    margin-top: 20px;
    .trend-up,.trend-down {
      top: 0;
    }
  }
</style>
