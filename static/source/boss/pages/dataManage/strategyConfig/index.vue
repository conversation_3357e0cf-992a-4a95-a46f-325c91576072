<template>
  <div class="strategy-container" v-loading="!isReady">
    <h3>商品发布策略</h3>
    <el-button type="primary" plain class="absolute-top-btn" size="mini" @click="handleExport">导出规则</el-button>
    <el-tabs type="border-card" class="mg-t-10 height-full" v-model="activeTab" v-if="isReady">
      <el-tab-pane v-for="tab in tabs" :label="tab.label" :key="tab.key" :name="tab.key" >
        <component
              :is="tab.component"
              :tabName="tab.key"
              :ref="`${tab.key}-component`"
              :key="`${tab.key}-component}`"
        />
      </el-tab-pane>
    </el-tabs>
    <exportDialog v-if="exportDialog.visible" ref="exportDialog" :visible.sync="exportDialog.visible" :exportData.sync="exportDialog.data"/>
  </div>
</template>

<script>
import { getCategoriesTree, getProductGroup } from '@/api/dataManage'
export default {
  components: {
    exportDialog: () => import('./components/exportDialog')
  },
  provide() {
    return {
      strategyConf: this
    }
  },
  data() {
    return {
      activeTab: 'CC',
      tabs: [{
        key: 'CC',
        label: '新商品中心（CC）',
        component: () => import('./components/tabContent')
      }, {
        key: 'VC',
        label: '供应商平台（VC）',
        component: () => import('./components/tabContent')
      }, {
        key: 'GBBVC',
        label: '工邦邦平台（GBB-VC）',
        component: () => import('./components/tabContent')
      }, {
        key: 'QTS',
        label: '询报价系统（QTS）',
        component: () => import('./components/tabContent')
      }],
      exportDialog: {
        visible: false
      },
      categoryList: [],
      materialGroupList: [],
      isReady: false
    }
  },
  created() {
    this.getTreeData()
  },
  methods: {
    async getTreeData() {
      const [resCategories = {}, resProductGroup = {}] = await Promise.all([
      getCategoriesTree({
        finalizeState: 0
      }),
      getProductGroup()
      ]).finally(() => {
        this.isReady = true
      })
      const { data = [] } = resCategories
      const { data: productGroup = [] } = resProductGroup
      this.categoryList = data
      this.materialGroupList = productGroup
    },
    handleExport() {
      this.exportDialog = {
        visible: true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.strategy-container{
  height: calc(100vh - 130px);
  padding: 15px 15px 0 15px;
  .absolute-top-btn{
    position: absolute;
    right: 15px;
    top: 10px;
  }
  .mg-t-10{
    margin-top: 10px;
  }
  .height-full{
    height: calc(100% - 35px);
    display: flex;
    flex-direction: column;
    .el-tabs__content{
      flex: 1;
    }
  }
  &::v-deep{
    .el-collapse-item__header{
      height: 32px;
      line-height: 32px;
      background-color: #f5f7fa;
      padding-left: 10px;
    }
    .el-tabs__content{
      flex: 1;
    }
    .right-container{
      max-height: calc(100vh - 330px);
      overflow-y: auto;
      .el-collapse{
        margin-bottom: 50px;
      }
    }
  }
}
</style>
