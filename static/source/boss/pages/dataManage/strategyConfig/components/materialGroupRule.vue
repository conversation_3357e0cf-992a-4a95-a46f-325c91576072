<template>
  <div>
    <div class="material-group-title">
      <label class="left-title">
        请选择物料组
      </label>
      <label class="right-title">
        您当前选择的物料组是：{{materialGroup.name || ''}}
      </label>
    </div>
    <div class="material-group-rule">
      <el-aside class="left-container border-eee">
        <tree class="tree-height" :dataType="'materialGroup'" :clickCallBack="qryRuleData" v-bind="$attrs"></tree>
      </el-aside>
      <div class="right-container" v-loading="loading" v-show="hasClicked">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="默认规则" name="default">
            <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="150px" class="mg-t-15">
              <el-form-item label="产品组" prop="productGroup">
                <el-select filterable clearable v-model="formData.productGroup" class="width-60">
                  <el-option v-for="item in options.productGroup" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="质检分类" prop="qualityInspection">
                <el-select filterable clearable v-model="formData.qualityInspection" class="width-60">
                  <el-option v-for="item in options.qualityInspection" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="关联一级类目" name="category">
            <el-form :model="formDataCategory" :rules="rulesCategory" ref="ruleFormCategory" label-width="150px" class="mg-t-15">
              <el-form-item label="关联一级类目" prop="category">
                <el-select filterable clearable v-model="formDataCategory.category" multiple class="width-60">
                  <el-option v-for="item in categoryListLevel1" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="品牌黑名单" name="excludeBrands">
            <el-form :model="formDataBlackBrand"  ref="ruleFormExcludeBrand" label-width="150px" class="mg-t-15">
              <el-form-item label="品牌黑名单" prop="blackBrandIds">
                <el-select filterable clearable remote multiple class="width-60" :remote-method="(name) => getEntityList('brand', name)" v-model="formDataBlackBrand.blackBrandIds" @focus="getEntityList('brand')">
                <el-option v-for="(item) in options.brand" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>

              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="物料描述拼接规则" name="jointRule" v-show="materialGroupUsable">
            <span class="span-inline-block">
              物料描述可选择拼接字段：{{materialGroupJointProps}}。
              <span class="red-font">(说明：拼接字段不能重复。)</span>
            </span>
            <span v-for="prop in materialGroupJointOpts.length" :key="`jointOpt${prop}`" class="mg-t-10">
              <el-select  v-model="jointFormData[prop]" clearable>
                <el-option v-for="(item) in materialGroupJointOpts"  :value="item.id" :label="item.name" :key="`jointOpt${prop}${item.id}`"></el-option>
              </el-select>
              <i class="el-icon-plus pd-10 font-blue" v-show="prop != materialGroupJointOpts.length"></i>
            </span>
          </el-collapse-item>
          <el-collapse-item title="商品标题拼接规则" name="titleJointRule" v-show="titleUsable">
            <span class="span-inline-block">
              商品标题可选择拼接字段：{{titleJointProps}}。
              <span class="red-font">(说明：拼接字段不能重复。)</span>
            </span>
            <span v-for="prop in titleJointOpts.length" :key="`titleOpt${prop}`" class="mg-t-10">
              <el-select  v-model="titleJointFormData[prop]" clearable>
                <el-option v-for="(item) in titleJointOpts"  :value="item.id" :label="item.name" :key="`titleOpt${prop}${item.id}`"></el-option>
              </el-select>
              <i class="el-icon-plus pd-10 font-blue" v-show="prop != titleJointOpts.length"></i>
            </span>
          </el-collapse-item>
        </el-collapse>
        <label class="absolute-footer-row">
          <el-button type="primary" size="mini" @click="handleSave" :loading="saveLoading">保存</el-button>
        </label>
      </div>
  </div>
  </div>

</template>

<script>
import { queryEnum, qryRuleData, saveRuleData, getCCEntityTypes } from '@/api/dataManage'
export default {
  components: {
    tree: () => import('./leftTree')
  },
  inject: ['strategyConf'],
  data() {
    return {
      loading: false,
      activeNames: ['default', 'category', 'excludeBrands', 'jointRule', 'titleJointRule'],
      materialGroup: {},
      formData: {
        productGroup: '',
        qualityInspection: ''
      },
      formDataCategory: {
        category: []
      },
      formDataBlackBrand: {
        blackBrandIds: []
      },
      rules: {
        productGroup: [
          { required: false, message: '请选择产品组' }
        ],
        qualityInspection: [
          { required: false, message: '请选择质检分类' }
        ]
      },
      rulesCategory: {
        category: [
          { required: false, message: '请选择一级类目' }
        ]
      },
      options: {
        productGroup: [],
        qualityInspection: [],
        brand: []
      },
      saveLoading: false,
      materialGroupJointOpts: [],
      jointFormData: {},
      titleJointOpts: [],
      titleJointFormData: {}
    }
  },
  crearted() {
  },
  computed: {
    hasClicked() {
      return Object.keys(this.materialGroup).length
    },
    categoryListLevel1() {
      return this.strategyConf.categoryList
    },
    isActiveTab() {
      return this.$attrs.tabKey === this.strategyConf.activeTab
    },
    materialGroupJointProps() {
      return this.materialGroupJointOpts.map(item => item.name).join('、')
    },
    titleJointProps() {
      return this.titleJointOpts.map(item => item.name).join('、')
    },
    materialGroupUsable() {
      return ['CC'].includes(this.$attrs.tabKey)
    },
    titleUsable() {
      return ['CC'].includes(this.$attrs.tabKey)
    }
  },
  watch: {
    isActiveTab: {
      handler(val) {
        val && this.initSelectOptions()
      },
      immediate: true
    }
  },
  methods: {
    async initSelectOptions() {
      const [res1 = {}, res2 = {}, res3 = [], res4 = []] = await Promise.all([
        queryEnum({
          typeCodes: 'spart'
        }),
        queryEnum({
          typeCodes: 'QualityInspection'
        }),
        queryEnum({
          typeCodes: 'materialDescriptionJoinField'
        }),
        queryEnum({
          typeCodes: 'sellNameJoinField'
        })
      ])
      this.options.productGroup = res1?.data?.spart || []
      this.options.qualityInspection = res2?.data?.qualityinspection || []
      this.materialGroupJointOpts = res3?.data?.materialdescriptionjoinfield || []
      this.titleJointOpts = res4?.data?.sellnamejoinfield || []
    },
    qryRuleData(formData = {}) {
      if (formData.id === this.materialGroup.id) return
      this.materialGroup = {
        ...formData
      }
      this.fetchRuleData()
    },
    async fetchRuleData() {
      this.loading = true
      const { data = {} } = await qryRuleData({
        platformCode: this.strategyConf.activeTab,
        materialGroup: this.materialGroup.id
      }).finally(() => {
        this.loading = false
      })
      const { productGroup = '', qualityInspection = '', oneCatalogIds = [], blackBrandIds = [], blackBrandNames = {}, materialDescriptionJoinFields = [], sellNameJoinFields = [] } = data
      this.formData = {
        productGroup: (productGroup ?? '') + '',
        qualityInspection: qualityInspection ?? ''
      }
      this.formDataCategory.category = oneCatalogIds || []
      this.formDataBlackBrand.blackBrandIds = blackBrandIds
      this.options.brand = []
      this.jointFormData = this.materialGroupJointOpts.reduce((pre, cur, index) => {
        pre[index + 1] = materialDescriptionJoinFields?.[index] || ''
        return pre
      }, {})
      this.titleJointFormData = this.titleJointOpts.reduce((pre, cur, index) => {
        pre[index + 1] = sellNameJoinFields?.[index] || ''
        return pre
      }, {})
      if (blackBrandIds.length) {
        this.options.brand = blackBrandIds.reduce((pre, cur, index) => {
          pre.push({
            id: cur,
            name: blackBrandNames[cur] || ''
          })
          return pre
        }, [])
      }
    },
    validate() {
      return new Promise((resolve, reject) => {
        let res1 = false
        let res2 = false
        let res3 = false
        let res4 = false
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            res1 = true
          } else {
            res1 = false
          }
        })
        this.$refs.ruleFormCategory.validate(valid => {
          if (valid) {
            res2 = true
          } else {
            res2 = false
          }
        })
        let jointOpts = Object.values(this.jointFormData).filter(item => item)
        res3 = jointOpts.length === [...new Set(jointOpts)].length
        let titleJointOpts = Object.values(this.titleJointFormData).filter(item => item)
        res4 = titleJointOpts.length === [...new Set(titleJointOpts)].length
        resolve({
          res: res1 && res2 && res3 && res4,
          msg: (res3 && res4) ? '' : `${!res3 ? '<span style="margin-bottom:10px;display:inline-block;">物料组拼接字段不能重复</span><br>' : ''}${!res4 ? '商品标题拼接字段不能重复' : ''}`
        })
      })
    },
    async handleSave() {
      let { res = false, msg = '' } = await this.validate()
      if (!res) {
        msg && this.$message({
          dangerouslyUseHTMLString: true,
          type: 'warning',
          message: msg
        })
        return
      }
      this.saveLoading = true
      const { productGroup = '', qualityInspection = '' } = this.formData
      const { category = [] } = this.formDataCategory
      const { success = false, errDesc = '' } = await saveRuleData({
        ruleType: '1',
        platformCode: this.strategyConf.activeTab,
        productGroup,
        qualityInspection,
        oneCatalogIds: category,
        materialGroup: this.materialGroup.id,
        blackBrandIds: this.formDataBlackBrand?.blackBrandIds || [],
        materialDescriptionJoinFields: this.materialGroupUsable ? Object.values(this.jointFormData).filter(item => item) : [],
        sellNameJoinFields: this.titleUsable ? Object.values(this.titleJointFormData).filter(item => item) : []
      }).finally(() => {
        this.saveLoading = false
      })
      this.$message[success ? 'success' : 'error'](success ? '操作成功' : errDesc || '操作失败')
      if (!success) return
      this.fetchRuleData()
    },
    async getEntityList(type = '', name = '') {
      const fetchApi = {
        brand: getCCEntityTypes
      }
      const fetchParams = {
        brand: () => {
          return {
            entityType: 'entity.brand',
            name
          }
        }
      }
      if ((this.options?.[type] || []).length && !name) {
        return
      }
      let params = (fetchParams[type] && fetchParams[type]()) || {}
      const { data = [] } = await fetchApi[type](params)
      const handleFetch = {
        brand: () => {
          this.options[type] = data
        }
      }

      handleFetch[type] && handleFetch[type]()
    }
  }
}
</script>

<style lang="less" scoped>
.material-group-title{
  .left-title{
    width: 330px;
    display: inline-block;
  }
  .right-title{
    margin-left: 10px;
    display: inline-block;
  }
}
.material-group-rule{
  display: flex;
  .left-container{
    display: flex;
    flex-direction: column;
    width: 255px;
    max-height: 100%;
    overflow: auto;
  }
  .border-eee{
    border: 1px solid #eee;
    border-radius: 4px;
  }
  .tree-height{
    height: calc(100vh - 320px);
  }
  .right-container{
    flex: 1;
    margin-left: 10px;
    padding-left: 10px;
    border-left: 1px solid #eee;
    .width-60{
      width:60%;
    }
    .absolute-footer-row{
      width: -webkit-fill-available;
      text-align: center;
      display: inline-block;
      position: absolute;
      bottom: 0px;
    }
    .span-inline-block{
      display: inline-block;
      width: 100%;
    }
    .red-font{
      color: red;
    }
    .pd-10{
      padding: 0 10px;
    }
    .font-blue{
      color: #409eff;
      font-weight: bolder;
    }
    .mg-t-10{
      display: inline-block;
      margin-top: 10px;
    }
    &::v-deep{
      .el-collapse-item__content{
        margin-top: 10px;
      }
      .el-collapse{
        max-height: calc(100% - 50px);
        overflow: hidden;
        overflow-y: auto;
      }
    }
  }
}
</style>
