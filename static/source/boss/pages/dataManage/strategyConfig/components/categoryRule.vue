<template>
   <div>
    <div class="category-title">
      <label class="left-title">
        请选择类目
      </label>
      <label class="right-title">
        您当前选择的类目是：{{category.fulePath || ''}}
      </label>
    </div>
    <div class="category-rule">
      <el-aside class="left-container">
        <tree class="border-eee tree-height" :dataType="'category'" :clickCallBack="qryRuleData" v-bind="$attrs"></tree>
      </el-aside>
      <div class="right-container" v-loading="loading" v-show="hasClicked">
        <el-collapse v-model="activeNames">
          <el-collapse-item title="默认规则" name="default" v-show="showCollapse('default')">
            <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="150px" class="mg-t-15">
              <tempalte v-if="isLevel4">
                <el-form-item label="是否化学品" prop="ifChemical">
                  <el-radio-group v-model="formData.ifChemical">
                    <el-radio v-for="(opt, id) in radioOpts"  :label="opt.value" :key="`radio${id}`">{{
                      opt.name
                    }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="是否成品油" prop="ifProductOil">
                  <el-radio-group v-model="formData.ifProductOil">
                    <el-radio v-for="(opt, id) in radioOpts"  :label="opt.value" :key="`radio${id}`">{{
                      opt.name
                    }}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="是否医疗器械" prop="ifMedicalDevice">
                  <el-radio-group v-model="formData.ifMedicalDevice">
                    <el-radio v-for="(opt, id) in radioOpts"  :label="opt.value" :key="`radio${id}`">{{
                      opt.name
                    }}</el-radio>
                  </el-radio-group>
                </el-form-item>
              </tempalte>
              <tempalate v-if="isLevel1">
                <el-form-item label="专享折扣" prop="exclusiveDiscount">
                  <el-col :span="6">
                    <numberInput v-model="formData.exclusiveDiscount" :positive="true" :integer="false" :precision="2">
                    </numberInput>
                  </el-col>
                </el-form-item>
              </tempalate>
              <tempalate v-if="isLevel2">
                <el-form-item label="标准佣金" prop="standardCommission">
                  <el-col :span="6">
                    <numberInput v-model="formData.standardCommission" :positive="true" :integer="false" :precision="3">
                    </numberInput>
                  </el-col>
                </el-form-item>
              </tempalate>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="字段范围" name="fields" v-show="showCollapse('fields')">
            <el-form :model="formDataFields" :rules="fieldsRules" ref="ruleFormFields" label-width="150px" class="mg-t-15">
              <el-form-item label="存储消防等级" prop="storageFireRating">
                <el-select filterable clearable v-model="formDataFields.storageFireRating" multiple class="width-60">
                  <el-option v-for="item in selects.storageFireRating" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="关联物料组" name="materialGroup" v-show="showCollapse('materialGroup')">
            <el-form :model="formDataMaterial"  ref="materialFields" label-width="150px" class="mg-t-15">
              <el-form-item label="关联物料组" prop="storageFireRating">
                <el-select filterable clearable v-model="formDataMaterial.materialGroupIds" multiple class="width-60">
                  <el-option v-for="item in selects.materialGroup" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </el-collapse-item>
          <el-collapse-item title="类目管理策略" name="validateForm" v-show="showCollapse('validateForm')">
            <el-form :model="validateFormData"  ref="validateFields" label-width="150px" class="mg-t-15">
              <el-form-item label="属性规则校验" prop="ruleCenterCheck">
                <el-radio-group v-model="validateFormData.ruleCenterCheck">
                  <el-radio v-for="(opt, id) in radioOpts"  :label="opt.value" :key="`radio${id}`">{{
                    opt.name
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="水效标识必填" prop="weImageRequired">
                <el-radio-group v-model="validateFormData.weImageRequired">
                  <el-radio v-for="(opt, id) in radioOpts"  :label="opt.value" :key="`radio${id}`">{{
                    opt.name
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="能效标识必填" prop="egImageRequired">
                <el-radio-group v-model="validateFormData.egImageRequired">
                  <el-radio v-for="(opt, id) in radioOpts"  :label="opt.value" :key="`radio${id}`">{{
                    opt.name
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="isLevel4 && isVCTab" label="是否支持VC发品" prop="ifVcCanPublish">
                <el-radio-group v-model="validateFormData.ifVcCanPublish">
                  <el-radio v-for="(opt, id) in radioOpts"  :label="opt.value" :key="`radio${id}`">{{
                    opt.name
                  }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-collapse-item>
        </el-collapse>
        <label class="absolute-footer-row" v-show="showCollapse('default')">
          <el-button type="primary" size="mini" @click="handleSave" :loading="saveLoading">保存</el-button>
        </label>
      </div>
  </div>
  </div>
</template>

<script>
import { qryRuleData, saveRuleData } from '@/api/dataManage'
import { delProperty } from '@/utils/dataManage.js'
export default {
  components: {
    tree: () => import('./leftTree'),
    NumberInput: () => import('../../compontents/numberInput.vue')
  },
  inject: ['strategyConf'],
  data() {
    return {
      loading: false,
      radioOpts: [{
        name: '是',
        value: 1
      }, {
        name: '否',
        value: 0
      }],
      formData: {
        // ifChemical: 0,
        // ifProductOil: 0,
        // ifMedicalDevice: 0
      },
      rules: {},
      category: {},
      saveLoading: false,
      formDataFields: {},
      formDataMaterial: {},
      validateFormData: {
        ruleCenterCheck: 0,
        weImageRequired: 0,
        egImageRequired: 0,
        ifVcCanPublish: 1
      },
      selects: {
        storageFireRating: [
              {
                label: '甲',
                value: '1'
              },
              {
                label: '乙',
                value: '2'
              },
              {
                label: '丙一',
                value: '3'
              },
              {
                label: '丙二',
                value: '4'
              },
              {
                label: '丁戊',
                value: '5'
              }
        ],
        materialGroup: []
      },
      fieldsRules: {
        storageFireRating: [
          { required: true, message: '存储消防等级' }
        ]
      },
      catalogFetch: {
        4: 'fourCatalogId',
        3: 'threeCatalogId',
        2: 'twoCatalogId',
        1: 'oneCatalogId'
      }
    }
  },
  computed: {
    hasClicked() {
      return Object.keys(this.category).length
    },
    activeNames() {
      const fetch = {
        4: ['default', 'fields', 'materialGroup', 'validateForm'],
        1: ['default'],
        2: ['default']
      }
      return fetch[(this.category?.level || '') + ''] || []
    },
    showCollapse() {
      return function(name) {
        return this.activeNames.includes(name)
      }
    },
    isLevel4() {
      return (+this.category?.level) === 4
    },
    isLevel1() {
      return (+this.category?.level) === 1
    },
    isLevel2() {
      return (+this.category?.level) === 2
    },
    isVCTab() {
      return this.strategyConf?.activeTab === 'VC'
    }

  },
  created() {
    this.selects.materialGroup = this.strategyConf?.materialGroupList || []
  },
  methods: {
    qryRuleData(formData = {}) {
      const { id = '', level = 0 } = formData
      if (id === this.category.id) return
      this.category = {
        ...formData
      }
      if (level === 3) return
      this.fetchRuleData()
    },
    async fetchRuleData() {
      this.loading = true
      const { id = '', level = 0 } = this.category
      const { data = {} } = await qryRuleData({
        platformCode: this.strategyConf.activeTab,
        [this.catalogFetch[level + '']]: id
      }).finally(() => {
        this.loading = false
      })
      this.formData = {
        ifChemical: data?.ifChemical || 0,
        ifProductOil: data?.ifProductOil || 0,
        ifMedicalDevice: data?.ifMedicalDevice || 0,
        exclusiveDiscount: data?.exclusiveDiscount ?? '',
        standardCommission: data?.standardCommission ?? ''
      }
      this.formDataFields = {
        storageFireRating: data?.storageFireRating || ['1', '2', '3', '4', '5']
      }
      this.formDataMaterial = {
        materialGroupIds: data?.materialGroupIds || []
      }
      this.validateFormData = {
        ruleCenterCheck: data?.ruleCenterCheck || 0,
        weImageRequired: data?.weImageRequired || 0,
        egImageRequired: data?.egImageRequired || 0,
        validateFormData: data?.ifVcCanPublish ?? 1
      }
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleFormFields.validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    },
    async handleSave() {
      let res = await this.validate()
      if (!res) return
      const ruleTypeFetch = {
        // 四级类目
        4: '2',
        1: '3',
        2: '4',
        3: '5'
      }
      this.saveLoading = true
      const { level = '' } = this.category
      const { success = false, errDesc = '' } = await saveRuleData({
        ruleType: ruleTypeFetch[level + ''] || '',
        platformCode: this.strategyConf.activeTab,
        catalogId: this.category.id,
        ...this.getSubmitParams()
      }).finally(() => {
        this.saveLoading = false
      })
      this.$message[success ? 'success' : 'error'](success ? '操作成功' : errDesc || '操作失败')
      if (!success) return
      this.fetchRuleData()
    },
    getSubmitParams() {
      const { level = '' } = this.category
      const { ifChemical = '', ifProductOil = '', ifMedicalDevice = 0, exclusiveDiscount = '', standardCommission = '' } = this.formData
      const fetch = {
        1: () => ({
          exclusiveDiscount
        }),
        2: () => ({
          standardCommission
        }),
        4: () => ({
          ifChemical,
          ifProductOil,
          ifMedicalDevice,
          storageFireRating: this.formDataFields.storageFireRating,
          ...this.formDataMaterial,
          ...this.isVCTab ? this.validateFormData : delProperty(this.validateFormData, 'ifVcCanPublish')
        })
      }
      return (fetch[level + ''] && fetch[level + '']()) || {}
    }
  }
}
</script>

<style lang="less" scoped>
.category-title{
  .left-title{
    width: 330px;
    display: inline-block;
  }
  .right-title{
    margin-left: 10px;
    display: inline-block;
  }
}
.category-rule{
  display: flex;
  .left-container{
    display: flex;
    flex-direction: column;
    width: 255px;
    max-height: 100%;
    overflow: auto;
  }
  .border-eee{
    border: 1px solid #eee;
    border-radius: 4px;
  }
  .tree-height{
    height: calc(100vh - 320px);
    overflow: auto;
  }
  .right-container{
    flex: 1;
    margin-left: 10px;
    padding-left: 10px;
    border-left: 1px solid #eee;
    .absolute-footer-row{
      width: -webkit-fill-available;
      text-align: center;
      display: inline-block;
      position: absolute;
      bottom: 0px;
    }
    &::v-deep{
      .el-collapse-item__content{
        margin-top: 10px;
      }
      .el-collapse{
        max-height: calc(100% - 50px);
        overflow: hidden;
        overflow-y: auto;
      }
    }
  }
}
</style>
