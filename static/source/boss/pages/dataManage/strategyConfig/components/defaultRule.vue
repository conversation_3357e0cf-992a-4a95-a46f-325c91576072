<template>
  <div class="default-rule-container">
    <el-collapse v-model="activeNames" v-loading="loading">
      <el-collapse-item title="默认规则" name="default">
        <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="100px" class="mg-t-15">
          <el-form-item label="商品定位" prop="productPosition">
            <el-select filterable clearable v-model="formData.productPosition" >
              <el-option v-for="item in productPositionOpts" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-collapse-item>
    </el-collapse>
    <label class="absolute-footer-row">
      <el-button type="primary" size="mini" @click="handleSave" :loading="saveLoading">保存</el-button>
    </label>
  </div>
</template>

<script>
import { qryRuleData, saveRuleData, queryEnum } from '@/api/dataManage'
export default {
  inject: ['strategyConf'],
  props: {
    tabKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeNames: ['default'],
      formData: {},
      rules: {
        productPosition: [
          { required: true, message: '请选择商品定位' }
        ]
      },
      productPositionOpts: [],
      loading: false,
      saveLoading: false
    }
  },
  computed: {
    isActiveTab() {
      return this.tabKey === this.strategyConf.activeTab
    }
  },
  watch: {
    isActiveTab: {
      handler(val) {
        if (val) {
          this.getProductPositionOptions()
          this.qryDeafultRule()
        }
      },
      immediate: false
    }
  },
  created() {
    if (this.tabKey === this.strategyConf.activeTab) {
      this.getProductPositionOptions()
      this.qryDeafultRule()
    }
  },
  methods: {
    async getProductPositionOptions() {
      const { data = {} } = await queryEnum({
          typeCodes: 'ProductPositioning'
        })
      this.productPositionOpts = data?.productpositioning || []
    },
    async qryDeafultRule() {
      this.loading = true
      const { data = {} } = await qryRuleData({
        platformCode: this.strategyConf.activeTab
      }).finally(() => {
        this.loading = false
      })
      this.formData = {
        productPosition: (data?.productPositioning ?? '') + ''
      }
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    },
    async handleSave() {
      let res = await this.validate()
      if (!res) return
      this.saveLoading = true
      const { success = false, errDesc = '' } = await saveRuleData({
        ruleType: '0',
        platformCode: this.strategyConf.activeTab,
        productPositioning: this.formData.productPosition
      }).finally(() => {
        this.saveLoading = false
      })
      this.$message[success ? 'success' : 'error'](success ? '操作成功' : errDesc || '操作失败')
      if (!success) return
      this.qryDeafultRule()
    }
  }
}
</script>

<style lang="less" scoped>
.default-rule-container{
  height: calc(100vh - 290px);
  overflow: auto;
  .mg-t-15{
    margin-top: 15px;
  }
  .absolute-footer-row{
    height: 30px;
    width: 100%;
    border-color: #fff;
    position: absolute;
    bottom: 10px;
    text-align: center;
  }
}

</style>
