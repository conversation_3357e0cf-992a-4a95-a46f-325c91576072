<template>
  <el-dialog v-if="dialogVisible" :visible.sync="dialogVisible"  width="900" title="导出策略" :close-on-click-modal="false">
    <el-form :model="modelFormData" :rules="rules" ref="ruleForm" label-width="200px">
      <el-form-item label="选择本次导出的平台：" prop="platformCode">
        <el-row :span="24" :gutter="10">
          <el-checkbox-group v-model="modelFormData.platformCode">
            <el-checkbox v-for="opt in platOpts" :label="opt.value" :key="opt.value">{{ opt.label }}</el-checkbox>
          </el-checkbox-group>
        </el-row>
      </el-form-item>
      <el-form-item label="选择本次导出的维度：" prop="exportType">
        <el-row :span="24" :gutter="10">
            <el-radio v-model="modelFormData.exportType" v-for="opt in typeOpts" :label="opt.value" :key="opt.value">{{ opt.label }}</el-radio>
        </el-row>
      </el-form-item>
      <template v-if="isCatalogExport">
        <el-form-item label="选择类目层级：" prop="ruleType" >
        <el-row :span="24" :gutter="10">
            <el-radio v-model="modelFormData.ruleType" v-for="opt in levelOpts" :label="opt.value" :key="opt.value">{{ opt.label }}</el-radio>
        </el-row>
      </el-form-item>
      <el-form-item label="选择本次导出的类目：" prop="catalog4">
        <el-row  :span="24" :gutter="10">
          <el-col :span="20" class="pd-0">
            <el-input type="textarea"  v-model="modelFormData.catelog4" rows="4" placeholder="请输入需要导出类目的id，多个用英文逗号隔开。"></el-input>
          </el-col>
        </el-row>
      </el-form-item>
      </template>
    </el-form>
    <span slot="footer" class="footer-btn">
      <el-button type="primary" @click="handleExport" :loading="exportLoading">确定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { exportRuleData } from '@/api/dataManage.js'
export default {
  inject: ['strategyConf'],
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rules: {},
      platOpts: [{
        label: 'CC',
        value: 'CC'
      }, {
        label: 'VC',
        value: 'VC'
      }, {
        label: 'GBB-VC',
        value: 'GBBVC'
      }, {
        label: 'QTS',
        value: 'QTS'
      }],
      typeOpts: [{
        label: '物料组',
        value: '1'
      }, {
        label: '类目',
        value: '2'
      }],
      levelOpts: [{
        label: '四级',
        value: '2'
      }, {
        label: '三级',
        value: '5'
      }, {
        label: '二级',
        value: '4'
      }, {
        label: '一级',
        value: '3'
      }],
      modelFormData: {
        catelog4: '',
        platformCode: [],
        exportType: '2',
        ruleType: '2'
      },
      exportLoading: false,
      categoryListLevel4: []
    }
  },
  created() {
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    isCatalogExport() {
      const { exportType = '' } = this.modelFormData
      return exportType === '2'
    }
  },
  methods: {
    async handleExport() {
      const { platformCode = [], catelog4 = '', ruleType = '' } = this.modelFormData
      if (!platformCode.length) {
        this.$message.warning('请选择本次导出的平台')
        return
      }
      this.exportLoading = true
      const { success = false, errDesc = '' } = await exportRuleData({
        platformCode: platformCode.join(','),
        ruleType: this.isCatalogExport ? ruleType : this.modelFormData.exportType,
        catalogIds: this.isCatalogExport ? catelog4.trim()
          .replace(/[\s,，]/g, ' ')
          .replace(/[\s]+/g, ',').split(',').filter(item => item) : []
      }).finally(() => {
        this.exportLoading = false
      })
      this.$message[success ? 'success' : 'error'](success ? '操作成功' : errDesc || '操作失败')
      if (!success) return
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
.footer-btn{
  width: 100%;
  display: inline-block;
  text-align: center;
}
.width-100{
  width: 100%;
}
.pd-0{
  padding: 0!important;
}
</style>
