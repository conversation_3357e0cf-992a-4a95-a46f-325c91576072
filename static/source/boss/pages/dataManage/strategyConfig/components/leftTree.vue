<template>
  <div class="tree-container" v-loading="loading">
    <el-input
      class="full-width position-absolute"
      placeholder="请输入搜索内容"
      prefix-icon="el-icon-search"
       @keyup.enter.native="findNode()"
      v-model="searchVal">
    </el-input>
    <el-tree
      :data="treeData"
      :props="defaultProps"
      @node-click="handleNodeClick"
      node-key="id"
      auto-expand-parent
      expand-on-click-node
       :default-expanded-keys="expandedKeys"
      id="treeList"
      class="filter-tree"
      ref="tree">
      <div class="custom-tree-node" slot-scope="{ node }">
        <span class="name">
          <span :title="node.label">{{ node.label }}</span>
        </span>
      </div>
    </el-tree>
  </div>
</template>

<script>

export default {
  inject: ['strategyConf'],
  props: {
    dataType: {
      type: String,
      default: ''
    },
    clickCallBack: {
      type: Function,
      default: null
    },
    tabKey: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      searchVal: '',
      treeData: [],
      fifterNodes: [],
      fifterIndex: -1,
      expandedKeys: [],
      searchValCP: ''
    }
  },
  computed: {
    defaultProps() {
      const fetch = {
        category: {
          children: 'catalogueTreeVOList'
        }
      }
      return {
        code: 'code',
        id: 'id',
        label: 'name',
        level: 'level',
        ...fetch[this.dataType] || {}
      }
    },
    isActiveTab() {
      return this.tabKey === this.strategyConf.activeTab
    }
  },
  watch: {
    isActiveTab: {
      handler(val) {
        val && this.initTreeData()
      },
      immediate: false
    }
  },
  created() {
    this.tabKey === this.strategyConf.activeTab && this.initTreeData()
  },
  methods: {
    async initTreeData() {
      const fetch = {
        category: () => {
          this.treeData = this.strategyConf.categoryList
        },
        materialGroup: () => {
          this.treeData = this.strategyConf.materialGroupList
        }
      }
      fetch[this.dataType] && fetch[this.dataType]()
    },
    qryParams() {
      const fetch = {
        category: {
          finalizeState: 0
        }
      }
      return (fetch[this.dataType] && fetch[this.dataType]) || {}
    },
    findNode() {
      if (!this.searchVal) return
      if (this.searchValCP !== this.searchVal) {
        this.searchValCP = this.searchVal
        this.fifterNodes = []
        this.fifterIndex = -1
        this.searchNode()
      } else {
        this.setCurrentKey()
      }
    },
    searchNode() {
      let that = this
      let nodes = this.treeData
      let narrSame = [] // 模糊搜索到的值
      let narrEqual = [] // 精确搜索到的值
      forarr(nodes)
      // 遍历找出所有包含关键字的节点
      function forarr(arr) {
        for (let item of arr) {
          // console.log(item)
          if ((item.name || '').indexOf(that.searchVal) > -1) {
            if (item.name === that.searchVal) {
              // 把名字完全相等的放在最前面
              narrEqual.push(item)
            } else {
              narrSame.push(item)
            }
          }
          if (item.catalogueTreeVOList && item.catalogueTreeVOList.length > 0) {
            forarr(item.catalogueTreeVOList)
          }
        }
      }
      this.fifterNodes = narrEqual.concat(narrSame)
      this.setCurrentKey()
    },
    setCurrentKey() {
      if (this.fifterNodes.length < 1) {
        this.$message.error('未找到对应内容')
        return false
      }
      // 搜索出来的节点之间的切换（通过索引值切换）// 切换到最后一个后 就回到初始位置
      this.fifterIndex++
      if (this.fifterIndex > this.fifterNodes.length - 1) {
        this.fifterIndex = 0
      }
      // 设定当前节点
      this.$refs.tree.setCurrentKey(this.fifterNodes[this.fifterIndex].id)

      // 设定自动展开的节点（第一级别时不展开，其他级别展开）
      if (this.fifterNodes[this.fifterIndex].level !== 1) {
        this.expandedKeys.push(this.fifterNodes[this.fifterIndex].id)
      }

      // 当前节点滚动到父级可见区域
      setTimeout(() => {
        let element = this.$refs['tree'].$el.querySelector('.is-current')
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          })
      }, 300)
    },
    handleNodeClick(data) {
      this.clickCallBack && this.clickCallBack({
        ...data
      })
    }
  }
}
</script>

<style lang="less" scoped>
.tree-container{
  padding-top: 10px;
  .custom-tree-node {
    display: block;
    width:calc(100% - 20px);
    .name {
      float: left;
      display: block;
      width: calc(100% - 40px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 14px;
      i {
        font-style: normal;
      }
      i.new {
        color: #0000ff;
      }
    }
    .edit {
      float: right;
      display: none;
      padding:2px 10px;
    }
    &:hover {
      .edit {
        display: block;
      }
    }
  }
  .full-width{
    width: 100%;
    box-sizing: border-box;
    margin-top: 1px;
    &::v-deep{
      .el-input__inner{
        border-radius: 0;
        border: unset;
        border-bottom: 1px solid #DCDFE6;
        border-top: 1px solid #eee;
        border-radius: 4px 4px 0 0;
      }

    }
  }
  .position-absolute{
    position: absolute;
    top: 19px;
    z-index: 10;
    width: 298px;
  }
  &::v-deep{
    .el-tree-node.is-current>.el-tree-node__content {
        color: #fff;
      .is-leaf {
        opacity: 0;
      }
      .el-tree-node__label{
        color: #fff;
        background: #409eff;
        padding:0 6px;
        border-radius: 2px;
      }
      span.name{
        span {
          color: #fff;
          background: #409eff;
          padding:0 6px;
          border-radius: 2px;
        }
      }
    }
    .el-tree{
      padding-top: 25px;
    }
  }
}
</style>
