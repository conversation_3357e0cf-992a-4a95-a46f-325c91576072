<template>
  <div class="strategy-tab-container">
    <el-tabs>
      <el-tab-pane  v-for="tab in tabs" :key="tab.key" :label="tab.label">
        <component
          :is="tab.component"
          :key="`${tabName}-${tab.key}-component}`"
          :ref="`${tabName}-${tab.key}-component}`"
          :tabKey="tabName"
        ></component>
      </el-tab-pane>
    </el-tabs>
  </div>

</template>

<script>
export default {
  props: {
    tabName: {
       type: String,
       default: ''
    }
  },
  data() {
    return {
      tabs: [
        {
          key: 'all',
          label: '全局规则',
          component: () => import('./defaultRule.vue')
        },
        {
          key: 'materialGroup',
          label: '物料组规则',
          component: () => import('./materialGroupRule.vue')
        },
        {
          key: 'category',
          label: '类目规则',
          component: () => import('./categoryRule.vue')
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>
.strategy-tab-container{
  padding:0 10px;
}
</style>
