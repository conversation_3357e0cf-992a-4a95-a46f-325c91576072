<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="fifter">
        <el-row :span="24" class="searchBox">
          <el-col :span="2" class="dialogTitle">渠道名称：</el-col>
          <el-col :span="4" >
            <el-input
            v-model="search.channelName"
            />
          </el-col>
          <el-col :span="2" class="dialogTitle">渠道编码：</el-col>
          <el-col :span="4" >
            <el-input
              v-model="search.channelCode"
            />
          </el-col>
          <el-col :span="6" style="padding-left: 10px" >
            <el-button @click="searchList" icon="el-icon-search" size="small" type="primary">查询</el-button>
            <el-button @click="searchReset" icon="el-icon-close" size="small" type="info">清空</el-button>
          </el-col>
          <el-col :span="6" style="text-align: right;">
            <el-button style="margin-right: 20px;"  icon="el-icon-plus" size="medium" @click="openRuleDialog" type="primary">添加渠道</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="c_tables">
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column align="center" prop="id" width="100" label="编号"></el-table-column>
          <el-table-column align="center" prop="channelName" label="规则渠道名称"></el-table-column>
          <el-table-column align="center" prop="channelCode" label="规则渠道编码"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button @click="modifyApp(scope.row, scope.$index)" type="text" size="small">编辑</el-button>
              <el-button @click="deleteApp(scope.row, scope.$index)" type="text" size="small">删除</el-button>
              <el-button @click="sceneManage(scope.row, scope.$index)" type="text" size="small">场景管理</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
      <!--添加弹出框-->
      <el-dialog
        width="580px"
        title="渠道信息"
        class="dialogClass"
        :visible.sync="showAddDialog"
        :close-on-click-modal="false">
        <div class="ruleset">
          <div class="rulesetcont">
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>规则渠道名称：</el-col>
              <el-col :span="16">
                <el-input
                  v-model="appForm.channelName"
                />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>规则渠道编码：</el-col>
              <el-col :span="16">
                <el-input
                  v-model="appForm.channelCode"
                />
              </el-col>
            </el-row>
          </div>
          <el-row :span="24">
            <el-col :span="24" style="text-align: right;">
              <el-button size="medium" @click="closeAddDialog" style="margin-left: 20px;">关闭</el-button>
              <el-button size="medium" :loading="loading.create" @click="storeAddDialog" type="primary">确认</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>
      <!--添加弹出框 end-->
    </div>
  </div>
</template>

<script>
import {
  getChannel,
  createChannel,
  updateChannel,
  deleteChannel
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        account: false,
        create: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      search: { // 搜索值
        channelName: '',
        channelCode: ''
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        channelName: '',
        channelCode: ''
      },
      total: 0,
      tableData: [],
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetApps()
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.channelName || This.search.channelCode) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  methods: {
    openRuleDialog () {
      // 打开添加数据窗口
      this.appForm = { // 初始化form应用的内容
        id: '',
        channelName: '',
        channelCode: ''
      }
      this.currAppInfo = null
      this.showAddDialog = true
    },
    closeAddDialog () {
      this.showAddDialog = false
    },
    storeAddDialog () {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp (row, index) {
      console.log('修改数据')
      this.appForm = row
      this.currAppInfo = row
      this.showAddDialog = true
    },
    deleteApp (row, index) {
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteChannel(row.id).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(() => {
        console.log('取消删除')
      })
    },
    sceneManage (row, index) {
      console.log('停用')
      this.appForm = row
      this.$router.push(`sceneManage/${row.channelCode}`)
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    goGetApps () {
      // 获取数据列表
      let This = this
      let data = {
        page: This.listQueryInfo.current - 1,
        size: This.listQueryInfo.pageSize,
        channelName: This.search.channelName,
        channelCode: This.search.channelCode
      }
      getChannel(data).then(res => {
        if (res.code === 0) {
          let result = res.data.content
          if (result) {
            This.tableData = result
            This.total = res.data.totalElements
          } else {
            This.tableData = []
          }
        } else {
          This.$message.error(res.message)
        }
        This.loading.table = false
        This.loading.page = false
      }).catch(() => {
        This.loading.table = false
        This.loading.page = false
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = {
        role: '',
        property: ''
      }
      console.log(state)
      if (state !== 'create') {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    gocreateApps (state) {
      // 新建、编辑数据
      let This = this
      let data = this.appForm
      This.searchReset('create')
      if (!data.channelName || !data.channelCode) {
        this.$message.error('请将信息填写完整')
        return false
      }
      let appId = this.appForm.id // 修改数据id
      this.loading.create = true
      if (appId) {
        data.id = appId
        // 修改数据
        updateChannel(data, appId).then(res => {
          if (res.code === 0) {
            This.$message.success('修改成功')
            // 修改成功更新数据列表
            This.goGetApps()
            This.showAddDialog = false
          } else {
            This.$message.error(res.msg)
          }
          This.loading.create = false
        })
      } else {
        // 添加数据
        createChannel(data).then(res => {
          if (res.code === 0) {
            This.$message.success('创建成功')
            // 新增成功创建数据列表
            This.goGetApps()
            This.showAddDialog = false
          } else {
            This.$message.error(res.msg)
          }
          This.loading.create = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .zZindex {
    z-index:99999 !important;
  }
  .formts {
    p.ts {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }
  }
  .dialogTitle {
    text-align: right;
    box-sizing: border-box;
    padding-right: 10px;
    line-height: 32px;
    i {
      color: red;
      position: relative;
      top: 2px;
      right: 4px;
    }
  }
  .rulesetcont {
    padding-bottom: 15px;
    .el-row {
      margin-bottom: 10px;
    }
  }
  .v-modal {
    z-index: 1998;
  }
  .page_center {
    .searchBox {
      padding: 20px 0;
      border: 1px solid #eee;
      margin: 20px 0;
    }
    .dialogTitle {
      text-align: right;
      box-sizing: border-box;
      padding-right: 2px;
      line-height: 32px;
      i {
        color: red;
        position: relative;
        top:3px;
        right: 4px;
      }
    }
    .c_pagination {
      text-align: right;
      padding: 20px;
    }
    .el-row {
      margin-bottom: 10px;
      .item-btn {
        position: relative;
        .item-close {
          position: absolute;
          right:-8px;
          top:-8px;
          font-size: 16px;
        }
      }
    }
  }
  .empty {
    padding: 150px 0;
    text-align: center;
    p {
      font-size: 16px;
      color: #999;
    }
  }
  .selectLoading {
    font-size: 12px;
    color: #ccc;
    text-align: center;
    height: 14px;
    line-height: 14px;
  }
</style>
