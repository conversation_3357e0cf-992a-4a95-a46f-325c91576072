<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right;"><el-button @click="openRuleDialog()" icon="el-icon-plus" type="primary">规则因子管理</el-button></el-col>
        </el-row>
        <div style="border: 1px solid #ccc; padding:20px;">
          <div style="margin:0 auto; width:80%; padding:20px;">
            <el-row :span="24" v-for="(itemp, indexp) in ruleList" :item="itemp" :key="indexp">
              <el-col :span="2" style="text-align: right; padding-right:20px;">规则{{indexp + 1}}</el-col>
              <el-col :span="22" style="border: 1px solid #ccc; padding:20px; padding-bottom: 10px;">
                <!--一个规则因子-->
                <el-row :span="24" :gutter="20" v-for="(item, index) in itemp" :item="item" :key="index">
                  <el-col :span="6">
                    <el-select style="width: 100%;" v-model="item.ruleName" placeholder="请选择">
                      <el-option
                        v-for="item in ruleOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-select style="width: 100%;" v-model="item.ruleSymbol" placeholder="请选择">
                      <el-option
                        v-for="item in ruleOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                      </el-option>
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-input style="width: 100%;" v-model="item.ruleValue" />
                  </el-col>
                  <el-col :span="6">
                    <div v-if="index < (itemp.length - 1)">
                      <el-select style="width: 100%;" v-model="item.ruleCondition"  placeholder="请选择">
                        <el-option
                          v-for="item in ruleOptions"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id">
                        </el-option>
                      </el-select>
                    </div>
                    <div v-else>
                      <el-button @click="addItem(indexp)" icon="el-icon-plus" type="primary"></el-button>
                    </div>
                  </el-col>
                </el-row>
                <!--一个规则因子 end-->
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="24" style="text-align: center;"><el-button @click="addItemP" icon="el-icon-plus" type="primary">添加规则</el-button></el-col>
            </el-row>
          </div>
        </div>

      </div>
      <!--添加弹出框-->
      <el-dialog
        width="580px"
        title="渠道信息"
        class="dialogClass"
        :visible.sync="showAddDialog"
        :close-on-click-modal="false">
        <div class="ruleset">
          <div class="rulesetcont">
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>因子名称：</el-col>
              <el-col :span="16">
                <el-input v-model="appForm.name" />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>因子编码：</el-col>
              <el-col :span="16">
                <el-input v-model="appForm.code" />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>因子业务范围：</el-col>
              <el-col :span="16">
                <el-input v-model="appForm.range" />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>取值配置：</el-col>
              <el-col :span="16">
                <div style="padding-top: 8px; padding-left: 6px;">
                  <el-radio v-model="appForm.valueSet" :label="true">是</el-radio>
                  <el-radio v-model="appForm.valueSet" :label="false">否</el-radio>
                </div>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>取值方式：</el-col>
              <el-col :span="16">
                <div style="padding-top: 8px; padding-left: 6px;">
                  <el-radio v-model="appForm.valueStyle" label="PORT">接口</el-radio>
                  <el-radio v-model="appForm.valueStyle" label="PROPERTY">属性绑定</el-radio>
                </div>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>属性：</el-col>
              <el-col :span="16">
                <el-input v-model="appForm.property" />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>值域配置：</el-col>
              <el-col :span="16">
                <div style="padding-top: 8px; padding-left: 6px;">
                  <el-radio v-model="appForm.valueRange" :label="true">是</el-radio>
                  <el-radio v-model="appForm.valueRange" :label="false">否</el-radio>
                </div>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="21" :offset="3">
                <el-row :gutter="10" v-for="(item, index) in appForm.valueSetList" :item="item" :key="index" style="margin-bottom: 0;">
                  <el-col :span="8">
                    <el-row :span="24">
                      <el-col :span="12" class="dialogTitle">值域关系: </el-col>
                      <el-col :span="12"><el-input v-model="item.symbol"  /></el-col>
                    </el-row>
                  </el-col>
                  <el-col :span="8">
                    <el-row :span="24">
                      <el-col :span="12" class="dialogTitle">值域值: </el-col>
                      <el-col :span="12"><el-input v-model="item.value" /></el-col>
                    </el-row>
                  </el-col>
                  <el-col :span="4">
                    <el-input v-if="index < (appForm.valueSetList.length - 1)" v-model="item.condition" />
                    <el-button v-else  @click="addValueItem()" icon="el-icon-plus" type="primary"></el-button>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>
          <el-row :span="24">
            <el-col :span="24" style="text-align: right;">
              <el-button size="medium" @click="closeAddDialog" style="margin-left: 20px;">关闭</el-button>
              <el-button size="medium" :loading="loading.create" @click="storeAddDialog" type="primary">确认</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>
      <!--添加弹出框 end-->
    </div>
  </div>
</template>

<script>
import {
  getServiceCenterArea
} from '@/api/customerDelivery'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true,
        account: false,
        create: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      ruleOptions: [{ // 规则选择1
        id: '1',
        name: '规则因子1'
      }, {
        id: '2',
        name: '规则因子2'
      }, {
        id: '3',
        name: '规则因子3'
      }],
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        name: '',
        code: '',
        range: '',
        valueSet: true,
        valueStyle: 'PORT', // PORT 接口  PROPERTY 属性
        property: '',
        valueRange: true,
        valueSetList: [
          {
            symbol: '',
            value: '',
            condition: ''
          }
        ]
      },
      total: 0,
      ruleList: [
        [
          {
            ruleName: '',
            ruleSymbol: '',
            ruleValue: '',
            ruleCondition: ''
          }
        ]
      ],
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
  },
  created () {
    this.goGetApps()
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.channelName || This.search.sceneName || This.search.sceneCode) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  methods: {
    addValueItem () {
      let arr = {
        symbol: '',
        value: '',
        condition: ''
      }
      this.appForm.valueSetList.push(arr)
    },
    addItem (index) {
      let arr = {
        ruleName: '',
        ruleSymbol: '',
        ruleValue: '',
        ruleCondition: ''
      }
      this.ruleList[index].push(arr)
    },
    addItemP (index) {
      let arr = [
        {
          ruleName: '',
          ruleSymbol: '',
          ruleValue: '',
          ruleCondition: ''
        }
      ]
      this.ruleList.push(arr)
    },
    openRuleDialog () {
      // 打开添加数据窗口
      this.appForm = { // 初始化form应用的内容
        id: '',
        name: '',
        code: '',
        range: '',
        valueSet: true,
        valueStyle: 'PORT', // PORT 接口  PROPERTY 属性
        property: '',
        valueRange: true,
        valueSetList: [
          {
            symbol: '',
            value: '',
            condition: ''
          }
        ]
      }
      this.currAppInfo = null
      this.showAddDialog = true
    },
    closeAddDialog () {
      this.showAddDialog = false
    },
    storeAddDialog () {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp (row, index) {
      console.log('修改数据')
      this.appForm = row
      this.currAppInfo = row
      this.showAddDialog = true
    },
    deleteApp (row, index) {
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

        /**
        let data = {
          aggregationId: row.id, // 数据id
          id: row.id // 数据id
        }
        deleteServiceCenterArea(data).then(res => {
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.tableData.splice(index, 1)
          } else {
            this.$message.error(res.message)
          }
        })**/
      }).catch(() => {
        console.log('取消删除')
      })
    },
    sceneManage (row, index) {
      console.log('停用')
      this.appForm = row
      this.$router.push(`sceneManage/${row.id}`)
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    goGetApps () {
      // 获取数据列表
      let This = this
      let data = {
        pageNum: This.listQueryInfo.current,
        pageSize: This.listQueryInfo.pageSize,
        code: 'service_center_area_batch',
        aggregationCode: 'service_center_area',
        aggregationType: 'service_center_area'
      }
      getServiceCenterArea(data).then(res => {
        if (res.code === 0) {
          let result = res.data.result
          if (result) {
            This.tableData = result
            This.total = res.data.total
          } else {
            This.tableData = []
          }
        } else {
          This.$message.error(res.message)
        }
        This.loading.table = false
        This.loading.page = false
      }).catch(() => {
        This.loading.table = false
        This.loading.page = false
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = {
        role: '',
        property: ''
      }
      if (!state) {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    gocreateApps (state) {
      // 新建、编辑数据
      let This = this
      let data = this.appForm
      This.searchReset('create')
      if (!data.channelName || !data.sceneName || !data.sceneCode) {
        this.$message.error('请将信息填写完整')
        return false
      }
      let appId = this.appForm.id // 修改数据id
      this.loading.create = true
      data.aggregationId = appId
      data.id = appId
      /**
      if (appId) {
        // 修改数据
        updateServiceCenterArea(data, appId).then(res => {
          if (res.code === 0) {
            This.$message.success('修改成功')
            // 修改成功更新数据列表
            This.goGetApps()
            this.showAddDialog = false
          } else {
            This.$message.error(res.message)
          }
          this.loading.create = false
        })
      } else {
        // 添加数据
        createServiceCenterArea(data).then(res => {
          if (res.code === 0) {
            This.$message.success('创建成功')
            // 新增成功创建数据列表
            this.tableData.push(res.data)
            this.showAddDialog = false
          } else {
            This.$message.error(res.message)
          }
          this.loading.create = false
        })
      }**/
    }
  }
}
</script>

<style lang="scss" scoped>
  .zZindex {
    z-index:99999 !important;
  }
  .formts {
    p.ts {
      font-size: 12px;
      color: #999;
      margin-top: 5px;
    }
  }
  .dialogTitle {
    text-align: right;
    box-sizing: border-box;
    padding-right: 10px;
    line-height: 32px;
    i {
      color: red;
      position: relative;
      top: 2px;
      right: 4px;
    }
  }
  .rulesetcont {
    padding-bottom: 15px;
    .el-row {
      margin-bottom: 10px;
    }
  }
  .v-modal {
    z-index: 1998;
  }
  .page_center {
    .searchBox {
      padding: 20px 0;
      border: 1px solid #eee;
      margin: 20px 0;
    }
    .dialogTitle {
      text-align: right;
      box-sizing: border-box;
      padding-right: 2px;
      line-height: 32px;
      i {
        color: red;
        position: relative;
        top:3px;
        right: 4px;
      }
    }
    .c_pagination {
      text-align: right;
      padding: 20px;
    }
    .el-row {
      margin-bottom: 10px;
      .item-btn {
        position: relative;
        .item-close {
          position: absolute;
          right:-8px;
          top:-8px;
          font-size: 16px;
        }
      }
    }
  }
  .empty {
    padding: 150px 0;
    text-align: center;
    p {
      font-size: 16px;
      color: #999;
    }
  }
  .selectLoading {
    font-size: 12px;
    color: #ccc;
    text-align: center;
    height: 14px;
    line-height: 14px;
  }
</style>
