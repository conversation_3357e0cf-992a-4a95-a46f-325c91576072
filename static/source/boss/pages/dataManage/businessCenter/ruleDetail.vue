<template>
  <div class="page data-manage-import-container" v-loading="loading.page">
    <div class="data-manage-import-center" v-show="!loading.page">
      <div class="data-manage-import-add">
        <el-row :span="24">
          <el-col :span="2" class="dialogTitle">属性值：</el-col>
          <el-col :span="6">
            <el-input
              v-model="search.propertyName"
              placeholder="请输入属性值"
            />
          </el-col>
          <el-col :span="4" style="padding-left: 10px;">
            <el-button @click="startSearch()" type="primary">查询</el-button>
            <el-button @click="resetSearch()">重置</el-button>
          </el-col>
          <el-col :span="12" style="text-align: right;" v-if="pageParams.type === '2'" >
            <el-button size="small" @click="openAddPropertyRule" type="primary">添加属性</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="importlist">
        <el-table
          v-loading="loading.table"
          v-if="templateList.length > 0"
          :data="templateList"
          row-key="id"
          :tree-props="{children: 'sideConditionList', hasChildren: 'hasChildren'}"
          border
          fit
          highlight-current-row
          :max-height="screenHeight"
          :default-expand-all="expandAll"
          :style="{ maxHeight: screenHeight}"
        >
          <el-table-column width="100" label="属性类型" align="left" prop="propertyRuleVO.dimensionType">
            <template slot-scope="scope">
              {{(scope.row.dimensionType === 'PRODUCT' ? '商品' : (scope.row.dimensionType === 'ONLINENEGATIVEDEMAND' ? '在线负需求' : (scope.row.dimensionType === 'BUYPRICE' ? '采购价格' : (scope.row.dimensionType === 'SKU' ? 'SKU' : (scope.row.dimensionType === 'OTHER' ? '其他' : (scope.row.dimensionType === 'NEGATIVEDEMAND' ? '负需求' : scope.row.dimensionType))))))}}
            </template>
          </el-table-column>
          <el-table-column width="120" label="属性名称" align="center" prop="name" />
          <el-table-column width="80" label="属性状态" align="center" prop="state">
            <template slot-scope="scope">
              {{(scope.row.state ? '已开启' : '已关闭')}}
            </template>
          </el-table-column>
          <el-table-column label="属性值" align="center" width="300px" >
            <template slot-scope="scope">
              <div v-if="scope.row.blackListRuleList[0]" class="ellipse2" :title="scope.row.blackListRuleList.filter((item)=>{return (item.name==='黑名单' || item.name==='白名单')})[0].value || '-' ">
                {{scope.row.blackListRuleList.filter((item)=>{return (item.name==='黑名单' || item.name==='白名单')})[0].value || '-' }}
              </div>
              <div v-else>
                -
              </div>
            </template>
          </el-table-column>
          <el-table-column width="100" label="筛选规则" align="center" prop="propertyRuleVO.constraintDTOList[0].ruleName">
            <template slot-scope="scope">
              {{scope.row.ruleName}}
            </template>
          </el-table-column>
          <el-table-column width="80" label="规则期限" align="center" prop="term">
            <template slot-scope="scope">
              {{scope.row.timeType === 0 ? '临时' : '长期'}}
            </template>
          </el-table-column>
          <el-table-column width="140" label="开始时间" align="center" prop="propertyRuleVO.constraintDTOList[0].startTime">
            <template slot-scope="scope">
              {{scope.row.startTime || '-'}}
            </template>
          </el-table-column>
          <el-table-column width="140" label="结束时间" align="center" prop="propertyRuleVO.constraintDTOList[0].endTime">
            <template slot-scope="scope">
              {{scope.row.endTime || '-'}}
            </template>
          </el-table-column>
          <el-table-column min-width="160" label="客户" align="center">
            <template slot-scope="scope">
              {{(scope.row.dimensionType === 'SKU' && scope.row.blackListRuleList.filter((item)=>{return item.name==='客户'})[0]) ? (scope.row.blackListRuleList.filter((item)=>{return item.name==='客户'})[0].value || '-') : '-'}}
            </template>
          </el-table-column>
          <el-table-column min-width="160" label="原因" align="center">
            <template slot-scope="scope">
              {{(scope.row.dimensionType === 'SKU' && scope.row.blackListRuleList.filter((item)=>{return item.name==='原因'})[0]) ? (scope.row.blackListRuleList.filter((item)=>{return item.name==='原因'})[0].value || '-') : '-'}}
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="100" label="操作" align="center" v-if="pageParams.type === '2'" >
            <template slot-scope="scope">
              <div v-if="!scope.row.ifSide">
                <!--只有主条件可以删除-->
                <el-button type="text" @click="goEditRule(scope.$index, scope.row)">编辑</el-button>
                <el-button v-if="scope.row.dimensionType !== 'OTHER'" type="text" @click="goRuleDel(scope.$index, scope.row)" style="margin-left: 10px;">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout=" total, prev, pager, next, jumper"
      @pagination="pageClick"
    />
    <!--添加修改属性规则-->
    <el-dialog
      width="680px"
      :title="isModifyState === '1' ? '修改属性规则' : (isModifyState === '2' ? '添加属性规则' : '属性及规则')"
      class="dialogClass"
      :visible.sync="showRuleDialog"
      :close-on-click-modal="false">
      <div class="ruleset">
        <div class="rulesetcont">
          <!--公用导入组件-->
          <div style="display: none;">
            <el-upload class="btn-upload"
                       ref="uploadBox"
                       :action="uploadUrl"
                       :show-file-list="false"
                       :on-success="onUploadSuccess"
                       :before-upload="beforeUpload"
                       :on-error="onUploadError"
                       accept=".xlsx,.xls"
                       name="file">
              <el-button :loading="propertyRuleInfo.loading" size="small" type="primary">导入sku</el-button>
            </el-upload>
          </div>
          <!--公用导入组件 end-->
          <!--主条件-->
          <div class="rule_main">
            <div class="rule_li">
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">属性维度：</el-col>
                <el-col :span="14">
                  <el-select
                    style="width: 100%;"
                    v-model="propertyRuleInfo.dimensionValue"
                    @change="dimensionChange('main')"
                    :disabled="propertyRuleInfo.dimensionValue === 'OTHER'"
                    placeholder="请选择">
                    <el-option
                      v-for="item in dimensionOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.value === 'OTHER'"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">属性名称：</el-col>
                <el-col :span="14">
                  <el-select
                    style="width: 100%;"
                    v-model="propertyRuleInfo.name"
                    @change="propertiesChange"
                    :disabled="propertyRuleInfo.dimensionValue === 'OTHER'"
                    placeholder="请选择">
                    <el-option
                      v-for="item in propertyRuleInfo.propertiesOptions"
                      :key="item.name"
                      :label="item.name"
                      :value="item.name">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24" v-if="propertyRuleInfo.dimensionValue !== 'OTHER'">
                <el-col :span="4" class="dialogTitle">属性值：</el-col>
                <el-col :span="14">
                  <el-input
                    type="textarea"
                    :rows="3"
                    placeholder="请输入内容"
                    v-model="propertyRuleInfo.value">
                  </el-input>
                  <p class="ts">所有文本属性都是按英文逗号隔开</p>
                </el-col>
                <el-col :span="4" class="dialogTitle downTemplate" v-show="propertyRuleInfo.dimensionValue === 'SKU'">
                  <p><a :href="propertyValueTemplate"><el-button size="small" type="primary">下载模板</el-button></a></p>
                  <p>
                    <el-button :loading="propertyRuleInfo.loading" size="small" @click="daorufile('main')" type="primary">导入sku</el-button>
                  </p>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">规则期限：</el-col>
                <el-col :span="7">
                  <el-select
                    v-model="propertyRuleInfo.termValue"
                    @change="termChange"
                    placeholder="请选择">
                    <el-option
                      v-for="item in termOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4" class="dialogTitle">筛选规则：</el-col>
                <el-col :span="7">
                  <el-select
                    v-model="propertyRuleInfo.filterValue"
                    @change="filterChange"
                    placeholder="请选择">
                    <el-option
                      v-for="item in filterOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24" v-if="propertyRuleInfo.termValue === 0">
                <!--期限临时需要选择开始时间、结束时间-->
                <el-col :span="4" class="dialogTitle">开始时间：</el-col>
                <el-col :span="7">
                  <el-date-picker
                    @blur="startTimeChange('main')"
                    style="width: 100%;"
                    v-model="propertyRuleInfo.startTime"
                    type="date"
                    placeholder="选择开始时间"
                    :picker-options="startPickerOptions"
                  >
                  </el-date-picker>
                </el-col>
                <el-col :span="4" class="dialogTitle">结束时间：</el-col>
                <el-col :span="7">
                  <el-date-picker
                    @blur="endTimeChange"
                    style="width: 100%;"
                    v-model="propertyRuleInfo.endTime"
                    type="date"
                    placeholder="选择结束时间"
                    :picker-options="endPickerOptions"
                  >
                  </el-date-picker>
                </el-col>
              </el-row>
              <el-row :span="24" v-show="propertyRuleInfo.dimensionValue === 'SKU'">
                <el-col :span="4" class="dialogTitle">客户：</el-col>
                <el-col :span="7" style="padding: 4px 0;">
                  <div>
                    <el-select
                      style="width: 100%;"
                      v-model="propertyRuleInfo.customerName"
                      filterable
                      remote
                      reserve-keyword
                      placeholder="请输入关键词"
                      :remote-method="searchCustomer"
                      @change="customerChange"
                      ref="searchselect"
                      :loading="loading.search">
                      <el-option
                        v-for="(item, index) in customerOptions"
                        :key="index"
                        :label="item.customerNumber+' '+item.customerName"
                        :value="item.customerName">
                      </el-option>
                    </el-select>
                  </div>
                </el-col>
                <el-col :span="4" class="dialogTitle">原因：</el-col>
                <el-col :span="7" style="padding: 4px 0;">
                  <el-input
                    style="width: 100%;"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入原因"
                    v-model="propertyRuleInfo.customerSeason" />
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">状态：</el-col>
                <el-col :span="7" style="padding: 4px 0;">
                  <el-switch
                    v-model="propertyRuleInfo.isOpenValue"
                  >
                  </el-switch>
                </el-col>
              </el-row>
            </div>
          </div>
          <!--主条件 end-->
          <!--副条件-->
          <div class="rule_gap" v-if="propertyRuleInfo.list.length > 0">
            <span class="span1"></span>
            <span class="span2">AND关系，属性之间默认是“且”的逻辑，此条规则需要同时满足前后多个条件</span>
            <span class="span1"></span>
          </div>
          <div class="rule_others" v-if="propertyRuleInfo.list.length > 0">
            <div class="rule_li" v-for="(itemp, indexp) in propertyRuleInfo.list" :item="itemp" :key="indexp">
              <div class="del_li" @click="deleteNewProperty(indexp)">
                <i class="el-icon-remove"></i>
              </div>
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">属性维度：</el-col>
                <el-col :span="14">
                  <el-select
                    style="width: 100%;"
                    v-model="itemp.dimensionValue"
                    @change="dimensionChange(indexp)"
                    :disabled="itemp.dimensionValue === 'OTHER'"
                    placeholder="请选择">
                    <el-option
                      v-for="item in dimensionOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.value === 'OTHER'"
                    >
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">属性名称：</el-col>
                <el-col :span="14">
                  <el-select
                    style="width: 100%;"
                    v-model="itemp.name"
                    @change="propertiesChange"
                    :disabled="itemp.dimensionValue === 'OTHER'"
                    placeholder="请选择">
                    <el-option
                      v-for="item in itemp.propertiesOptions"
                      :key="item.name"
                      :label="item.name"
                      :value="item.name">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24" v-if="itemp.dimensionValue !== 'OTHER'">
                <el-col :span="4" class="dialogTitle">属性值：</el-col>
                <el-col :span="14">
                  <el-input
                    type="textarea"
                    :rows="3"
                    placeholder="请输入内容"
                    v-model="itemp.value">
                  </el-input>
                  <p class="ts">所有文本属性都是按英文逗号隔开</p>
                </el-col>
                <el-col :span="4" class="dialogTitle downTemplate" v-show="itemp.dimensionValue === 'SKU'">
                  <p><a :href="propertyValueTemplate"><el-button size="small" type="primary">下载模板</el-button></a></p>
                  <p>
                    <el-button :loading="itemp.loading" size="small" @click="daorufile(indexp)" type="primary">导入sku</el-button>
                  </p>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">规则期限：</el-col>
                <el-col :span="7">
                  <el-select
                    v-model="itemp.termValue"
                    @change="termChange"
                    placeholder="请选择">
                    <el-option
                      v-for="item in termOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="4" class="dialogTitle">筛选规则：</el-col>
                <el-col :span="7" style="line-height: 32px;">{{ propertyRuleInfo.filterValue === '1' ? '黑名单' : '白名单'}}</el-col>
              </el-row>
              <el-row :span="24" v-if="itemp.termValue === 0">
                <!--期限临时需要选择开始时间、结束时间-->
                <el-col :span="4" class="dialogTitle">开始时间：</el-col>
                <el-col :span="7">
                  <el-date-picker
                    @blur="startTimeChange(indexp)"
                    style="width: 100%;"
                    v-model="itemp.startTime"
                    type="date"
                    placeholder="选择开始时间"
                    :picker-options="startPickerOptions"
                  >
                  </el-date-picker>
                </el-col>
                <el-col :span="4" class="dialogTitle">结束时间：</el-col>
                <el-col :span="7">
                  <el-date-picker
                    @blur="endTimeChange"
                    style="width: 100%;"
                    v-model="itemp.endTime"
                    type="date"
                    placeholder="选择结束时间"
                    :picker-options="{
                      disabledDate: (time) => {
                        return time <= itemp.startTime
                      }
                    }"
                  >
                  </el-date-picker>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="4" class="dialogTitle">状态：</el-col>
                <el-col :span="7" style="padding: 4px 0;">
                  <el-switch
                    v-model="itemp.isOpenValue"
                  >
                  </el-switch>
                </el-col>
              </el-row>
            </div>
          </div>
          <!--副条件 end-->
          <div class="rule_add" v-show="propertyRuleInfo.list.length < 5">
            <span @click="addNewProperty">添加属性+</span>
          </div>
        </div>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right;">
            <el-button size="medium" @click="closeAddPropertyRule" style="margin-left: 20px;">取消</el-button>
            <el-button size="medium" @click="storeAddPropertyRule" type="primary">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--添加修改属性规则 end-->
    <!--删除、修改确认-->
    <el-dialog
      width="500px"
      title="提示"
      class="dialogClass"
      :visible.sync="showHandleDialog"
      :close-on-click-modal="false">
      <div style="padding-bottom: 20px;">
        <p v-if="handleId === '1'">取消后将不保存之前对属性的修改操作，是否确认取消？</p>
        <p v-else>是否确认删除当前属性数据，删除后不可恢复请谨慎操作。</p>
      </div>
      <div class="ruleset">
        <el-row :span="24">
          <el-col :span="24" style="text-align: right;">
            <el-button size="medium" @click="closeDelDialog" style="margin-left: 20px;">取消</el-button>
            <el-button size="medium" @click="storeDelDialog" type="primary">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--删除、修改确认 end-->
  </div>
</template>

<script>
import {
  getbusinessPropertyList,
  getbusinessPropertyNameList,
  addbusinessProperty,
  updatebusinessProperty,
  deletebusinessProperty,
  getbusinessPropertyValueTemplate,
  getbusinessPropertyDetail,
  getbusinessCustomer
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
import moment from 'moment'
export default {
  name: 'businessCenterDetail',
  data () {
    return {
      pageParams: this.$route.params, // {id:'1',type:'1'} id:数据id type 1 详情 2 编辑
      loading: {
        table: true,
        import: false,
        page: true,
        search: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 100
      },
      search: {
        propertyName: ''
      },
      prevKeywords: '', // 上次关键词，用来做对比，判断是关键字的分页加载还是，新的关键字的第一页加载
      currKeywords: '', // 上次关键词，用来做对比，判断是关键字的分页加载还是，新的关键字的第一页加载
      isLoading: true, // 加载中 true 加载中不能重新发送请求， false 可以发送加载请求
      total: 0,
      uploadUrl: '/api-rulecenter/sku/import/sku-value',
      filePath: '', // 导入成功返回的文件地址
      createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
      createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      templateList: [],
      showRuleDialog: false,
      showHandleDialog: false,
      handleId: '1', // 1 修改操作  2 删除操作
      currConstraintId: null, // 当前删除、修改的属性规则id
      isModifyState: '0', // 0 默认 1 修改属性 2 添加属性
      propertyRuleInfo: { // 当前属性规则信息
        value: '', // 属性值
        name: '', // 选中的属性名称
        isOpenValue: true, // 是否开启检测
        startTime: '', // 开始时间
        endTime: '', // 结束时间
        termValue: 1, // 规则期限选择的值
        dimensionValue: '', // 规则维度选择的值
        propertiesOptions: [], // 当前使用的properties库
        filterValue: '1', // 筛选规则选择的值
        customerName: '', // 维度是sku中选中的客户名字
        customerNumber: '', // 维度是sku中选中的客户编码
        customerSeason: '', // 维度是sku中客户原因
        loading: false, // 上传skuloading
        list: [
          {
            value: '',
            name: '',
            isOpenValue: true,
            startTime: '',
            endTime: '',
            termValue: 1,
            dimensionValue: '',
            propertiesOptions: [],
            filterValue: '1',
            customerName: '',
            customerNumber: '',
            customerSeason: '',
            loading: false
          }
        ]
      },
      customerOptions: [],
      dimensionOptions: [{ // 规则维度
        value: 'PRODUCT',
        label: '商品'
      }, {
        value: 'NEGATIVEDEMAND',
        label: '负需求'
      }, {
        value: 'ONLINENEGATIVEDEMAND',
        label: '在线负需求'
      }, {
        value: 'BUYPRICE',
        label: '采购价格'
      }, {
        value: 'SKU',
        label: 'SKU'
      }, {
        value: 'OTHER',
        label: '其他'
      }, {
        value: 'SDI',
        label: '供货地图'
      }],
      termOptions: [{ // 规则期限
        value: 1,
        label: '长期'
      }, {
        value: 0,
        label: '临时'
      }],
      filterOptions: [{ // 筛选规则
        value: '1',
        label: '黑名单'
      }, {
        value: '2',
        label: '黑-白名单'
      }],
      startPickerOptions: {
        disabledDate(time) {
          // 开始时间可选日期控制
          return time.getTime() < (Date.now() - 24 * 60 * 60 * 1000) // 从今天起
        }
      },
      properties: [
        {
          id: '1',
          name: '商品',
          value: ['品牌', '物料组', '商品来源', 'SKU编码', '商品名称', '物料号', '商品定位', '一级分组', '二级分组', '三级分组', '四级分组', '行业专属', '推广关键字', '采购员', '推荐行业', '备货类型', '产品组', '商品经理', '商品备注', '是否官网', 'CAS编号', '核心规格', '制造商订货号', '制造商型号', '箱规']
        },
        {
          id: '2-1',
          name: '在线负需求(直发)',
          value: ['客户指定', '销售订单号', '销售订单行', '分配供应商', '物料组', '物料组描述', '采购组', '采购组描述', '采购申请号', '采购申请行', '工厂', '物料类型', '客服', '客户编码', '客户名称', '收货联系人', '对应总仓', '默认供应商', '默认供应商名称', '免费', '客户需求备注', '供应商送达仓库', '是否有交付主管', '收货人是否为交付主管', '供应商物料号', '签单返回']
        },
        {
          id: '2-2',
          name: '在线负需求(非直发)',
          value: ['工厂', '物料编码', '物料组', '物料组描述', 'MRP区域', 'MRP区域描述', '售完即止', '客户需求备注', 'MRP控制者', 'MRP控制者描述', '默认供应商', '默认供应商描述', '默认运营路线', '交期', '招投标', '含运费', '产品类型', '商品经理', '供应商送达仓库']
        },
        {
          id: '3',
          name: '采购价格',
          value: ['是否招投标', '信息类别', '工厂', '区域总仓', '供应商物料号', '采购价格备注', '是否有货', '是否推优', '启用对接库存']
        },
        {
          id: '4',
          name: 'SKU',
          value: []
        },
        {
          id: '5',
          name: '其他',
          value: []
        }
      ],
      propertyValueTemplate: '', // 导出属性值模板连接
      screenHeight: '600',
      dimensionType: '', // 属性维度
      expandAll: false // 是否默认展开
    }
  },
  components: {
    Pagination
  },
  computed: {
    endPickerOptions () {
      let This = this
      return {
        disabledDate(time) {
          // 结束时间可选日期控制
          let value = null
          console.log('时间')
          console.log(This.propertyRuleInfo.startTime)
          if (This.propertyRuleInfo.startTime) {
            value = time.getTime() < This.propertyRuleInfo.startTime.getTime() + 24 * 60 * 60 * 1000
          } else {
            value = time.getTime() < (Date.now() + 24 * 60 * 60 * 1000) // 从明天起
          }
          console.log('结束时间')
          console.log(value)
          return value
        }
      }
    }
  },
  created () {
    console.log('参数')
    console.log(this.pageParams)
    this.dimensionType = this.pageParams.scene
    if (this.dimensionType === 'AUTOINDIRECTLY' || this.dimensionType === 'AUTODIRECTLY') {
      this.dimensionOptions = [{ // 规则维度
        value: 'PRODUCT',
        label: '商品'
      }, {
        value: 'ONLINENEGATIVEDEMAND',
        label: '在线负需求'
      }, {
        value: 'BUYPRICE',
        label: '采购价格'
      }, {
        value: 'SDI',
        label: '供货地图'
      }, {
        value: 'SKU',
        label: 'SKU'
      }, {
        value: 'OTHER',
        label: '其他'
      }]
    } else {
      this.dimensionOptions = [{ // 规则维度
        value: 'PRODUCT',
        label: '商品'
      }, {
        value: 'NEGATIVEDEMAND',
        label: '负需求'
      }, {
        value: 'BUYPRICE',
        label: '采购价格'
      }, {
        value: 'SDI',
        label: '供货地图'
      }, {
        value: 'SKU',
        label: 'SKU'
      }, {
        value: 'OTHER',
        label: '其他'
      }]
    }
    this.goGetbusinessPropertyList()
    this.goGetbusinessPropertyValueTemplate()
  },
  mounted () {
    // 设置表格高度为屏幕高度，使其能完全显示在屏幕内
    if (this.pageParams.type === '2') {
      // 编辑
      this.screenHeight = window.screen.height - 340
    } else {
      // 详情
      this.screenHeight = window.screen.height - 280
    }
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showRuleDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.storeAddPropertyRule()
        } else if (This.search.propertyName) {
          This.goGetbusinessPropertyList()
        }
      }
    })
  },
  methods: {
    startSearch () {
      this.goGetbusinessPropertyList()
    },
    resetSearch () {
      this.search.propertyName = ''
      this.goGetbusinessPropertyList()
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
    },
    beforeUpload (file) {
      console.log('beforeUpload')
      console.log(file);
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false;
      }
      if (!this.$validateFileType(file)) return false

      this.fileName = file.name;
      let This = this
      let uploadIndex = this.uploadIndex
      if (uploadIndex === 'main') {
        // 证明是主条件的上传
        This.propertyRuleInfo.loading = true
      } else {
        // 证明是副条件的上传
        This.propertyRuleInfo.list[uploadIndex].loading = true
      }
    },
    onUploadSuccess (response) {
      console.log('success')
      console.log(response)
      let This = this
      let uploadIndex = this.uploadIndex
      if (uploadIndex === 'main') {
        // 证明是主条件的上传
        This.propertyRuleInfo.loading = false
      } else {
        // 证明是副条件的上传
        This.propertyRuleInfo.list[uploadIndex].loading = false
      }
      if (response && response.code === 0) {
        this.$message.success(response.message || '导入成功！')
        if (uploadIndex === 'main') {
          // 证明是主条件的上传
          This.propertyRuleInfo.value = response.data.join(',')
        } else {
          // 证明是副条件的上传
          This.propertyRuleInfo.list[uploadIndex].value = response.data.join(',')
        }
      } else {
        this.$message.error((response && response.message) || '导入失败！')
      }
    },
    onUploadError (error) {
      console.log('error')
      console.log(error)
      this.propertyRuleInfo.loading = false
      this.$message.error((error && error.msg) || (error && error.message) || '导入失败')
    },
    dimensionChange (index) {
      // 切换完维度，初始化对应属性的值
      this.goGetbusinessPropertyNameList('', index)
    },
    termChange () {
    },
    filterChange () {
    },
    propertiesChange () {
    },
    startTimeChange (index) {
      let This = this
      if (index === 'main') {
        // 证明是主条件的上传
        let startTime = new Date(This.propertyRuleInfo.startTime)
        let endTime = new Date(This.propertyRuleInfo.endTime)
        console.log(endTime)
        if (startTime && endTime) {
          if (startTime.getTime() >= endTime.getTime()) {
            This.propertyRuleInfo.endTime = ''
          }
        }
      } else {
        // 证明是副条件的上传
        let startTime = new Date(This.propertyRuleInfo.list[index].startTime)
        let endTime = new Date(This.propertyRuleInfo.list[index].endTime)
        if (startTime && endTime) {
          if (startTime.getTime() >= endTime.getTime()) {
            This.propertyRuleInfo.list[index].endTime = ''
          }
        }
      }
    },
    endTimeChange () {
    },
    customerChange (e) {
    },
    goEditRule (index, row) {
      // 打开编辑弹窗
      let id = row.id
      // 由于添加折叠之后table的index不是列表的真实index,所以用id去获取数据在列表中的真实index，用来后续的假删除
      let templateList = this.templateList
      let nindex = templateList.findIndex((item) => {
        return item.id === id
      })
      index = nindex
      this.handleId = '1'
      this.currConstraintId = id
      this.currConstraintIndex = index
      this.isModifyState = '1'
      let data = {
        constraintId: id
      }
      console.log('打开编辑')
      getbusinessPropertyDetail(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          // 数据回显
          let mainData = {
            value: res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '黑名单' })[0] && res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '黑名单' })[0].value, // 属性值
            name: res.data.constraintDTOList[0].propertyName, // 属性名字
            isOpenValue: res.data.constraintDTOList[0].state, // 是否开启检测
            startTime: res.data.constraintDTOList[0].startTime, // 开始时间
            endTime: res.data.constraintDTOList[0].endTime, // 结束时间
            termValue: res.data.constraintDTOList[0].timeType, // 规则期限选择的值
            dimensionValue: res.data.dimensionType, // 规则维度选择的值
            propertiesOptions: [], // 维度筛选项
            filterValue: res.data.constraintDTOList[0].screenType === 'BLACKLIST' ? '1' : '2', // 筛选规则选择的值
            customerName: res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '客户' })[0] && res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '客户' })[0].value, // 选中的客户名字
            customerNumber: res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '客户编码' })[0] && res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '客户编码' })[0].value, // 选中的客户number
            customerSeason: res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '原因' })[0] && res.data.constraintDTOList[0].blackListRuleList.filter((item) => { return item.name === '原因' })[0].value, // 客户原因
            loading: false, // 导入loading
            list: [] // 副条件列表
          }
          this.propertyRuleInfo = mainData
          // 获取客户列表
          this.searchCustomer(this.propertyRuleInfo.customerName)
          this.goGetbusinessPropertyNameList(res.data.constraintDTOList[0].propertyName, 'main')
          let sideCondition = res.data.constraintDTOList[0].sideCondition
          if (sideCondition && sideCondition.length > 0) {
            sideCondition.forEach((itemp, indexp) => {
              let unit = {
                value: itemp.blackListRuleList.filter((item) => { return (item.name === '黑名单' || item.name === '白名单') })[0] && itemp.blackListRuleList.filter((item) => { return (item.name === '黑名单' || item.name === '白名单') })[0].value, // 属性值
                name: itemp.propertyName, // 属性名字
                isOpenValue: itemp.state, // 是否开启检测
                startTime: itemp.startTime, // 开始时间
                endTime: itemp.endTime, // 结束时间
                termValue: itemp.timeType, // 规则期限选择的值
                dimensionValue: itemp.dimensionType, // 规则维度选择的值
                propertiesOptions: [], // 维度筛选项
                filterValue: itemp.screenType === 'BLACKLIST' ? '1' : '2', // 筛选规则选择的值
                customerName: itemp.blackListRuleList.filter((item) => { return item.name === '客户' })[0] && itemp.blackListRuleList.filter((item) => { return item.name === '客户' })[0].value, // 选中的客户名字
                customerNumber: itemp.blackListRuleList.filter((item) => { return item.name === '客户编码' })[0] && itemp.blackListRuleList.filter((item) => { return item.name === '客户编码' })[0].value, // 选中的客户number
                customerSeason: itemp.blackListRuleList.filter((item) => { return item.name === '原因' })[0] && itemp.blackListRuleList.filter((item) => { return item.name === '原因' })[0].value, // 客户原因
                loading: false // 导入loading
              }
              this.propertyRuleInfo.list.push(unit)
              // 获取属性名称列表
              this.goGetbusinessPropertyNameList(itemp.propertyName, indexp)
            })
          }
          console.log('回显')
          console.log(this.propertyRuleInfo)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goRuleDel (index, row) {
      // 打开删除弹窗
      let id = row.id
      // 由于添加折叠之后table的index不是列表的真实index,所以用id去获取数据在列表中的真实index，用来后续的假删除
      let templateList = this.templateList
      let nindex = templateList.findIndex((item) => {
        return item.id === id
      })
      index = nindex
      this.handleId = '2'
      this.currConstraintId = id
      this.currConstraintIndex = index
      this.showHandleDialog = true
    },
    closeDelDialog () {
      this.showHandleDialog = false
    },
    storeDelDialog () {
      if (this.handleId === '1') {
        // 修改取消==“确认”
        console.log('修改属性，id=' + this.currConstraintId, 'index=' + this.currConstraintIndex)
      } else {
        // 删除确认
        console.log('删除属性，id=' + this.currConstraintId, 'index=' + this.currConstraintIndex)
        deletebusinessProperty(this.currConstraintId).then(res => {
          console.log(res);
          if (res.code === 0) {
            this.$message.success('删除成功')
            this.templateList.splice(this.currConstraintIndex, 1)
          } else {
            this.$message.error(res.msg)
          }
        })
      }
      this.showHandleDialog = false
      this.showRuleDialog = false
    },
    openAddPropertyRule () {
      // 【打开】添加属性规则form；
      this.showRuleDialog = true
      this.isModifyState = '2'
      // 初始化表单
      this.propertyRuleInfo = {
        value: '',
        name: '',
        isOpenValue: true,
        startTime: '',
        endTime: '',
        termValue: 1,
        dimensionValue: '',
        propertiesOptions: [],
        filterValue: '1',
        customerName: '',
        customerNumber: '',
        customerSeason: '',
        loading: false,
        list: []
      }
      this.customerOptions = []
    },
    closeAddPropertyRule () {
      // 【关闭】属性添加form
      let This = this;
      if (This.isModifyState === '1') {
        console.log('修改属性规则，需要确认是否取消修改')
        this.showHandleDialog = true
        this.handleId = '1'
      } else {
        // 添加
        console.log('关闭添加属性规则')
        this.showRuleDialog = false
      }
    },
    storeAddPropertyRule () {
      // 【保存】属性添加
      let This = this;
      // 组装主条件数据
      let mainData = getMainData()
      // 主条件数据
      function getMainData () {
        let dimensionValue = This.propertyRuleInfo.dimensionValue
        if (!dimensionValue) {
          // 属性名必选
          This.$message.error('请选择属性维度')
          return false
        }
        if (!This.propertyRuleInfo.name) {
          // 属性名必选
          This.$message.error('请选择属性名称')
          return false
        }
        if (!This.propertyRuleInfo.value) {
          // 属性值必填
          if (dimensionValue === 'SKU') {
            This.$message.error('请填写属性值或导入sku')
            return false
          } else {
            if (dimensionValue !== 'OTHER') {
              This.$message.error('请填写属性值')
              return false
            }
          }
        }
        if (dimensionValue === 'SKU') {
          // 维度是sku是，原因必填
          if (!This.propertyRuleInfo.customerSeason) {
            This.$message.error('请填写原因')
            return false
          }
        }
        if (This.propertyRuleInfo.termValue === 0) {
          // 期限为临时时，需要有时间段
          if (!This.propertyRuleInfo.startTime || !This.propertyRuleInfo.endTime) {
            This.$message.error('请选择开始/结束时间')
            return false
          }
        } else {
          // 长期不需要传递时间
          This.propertyRuleInfo.startTime = ''
          This.propertyRuleInfo.endTime = ''
        }
        let currItem = null
        // 获取当前选中的属性项
        This.propertyRuleInfo.propertiesOptions.forEach((item, index) => {
          if (item.name === This.propertyRuleInfo.name) {
            currItem = item
          }
        })
        let startTime = This.propertyRuleInfo.startTime
        if (This.propertyRuleInfo.startTime) {
          startTime = This.formatDate(startTime)
        }
        let endTime = This.propertyRuleInfo.endTime
        if (endTime) {
          endTime = This.formatDate(endTime)
        }
        let customerOptions = This.customerOptions
        let customerName = This.propertyRuleInfo.customerName
        let customerNumber = This.propertyRuleInfo.customerNumber
        let customerSeason = This.propertyRuleInfo.customerSeason
        if (dimensionValue !== 'SKU') {
          // 这三个值，只有维度是sku时，才需要填写
          customerName = ''
          customerNumber = ''
          customerSeason = ''
        } else {
          // console.log('客户编号1=' + customerNumber)
          // console.log('客户名字1=' + customerNumber)
          // console.log(customerOptions)
          if (customerName) {
            customerOptions.forEach((item) => {
              if (item.customerName === customerName) {
                customerNumber = item.customerNumber
              }
            })
          }
          // console.log('客户编号2=' + customerNumber)
          // console.log('客户名字2=' + customerNumber)
        }
        let data = {
          'categoryId': This.pageParams.id,
          'constraintDTOList': [{
            'blackListRuleList': [
              {
                'name': '黑名单',
                'value': This.propertyRuleInfo.value
              },
              {
                'name': '客户',
                'value': customerName
              },
              {
                'name': '客户编码',
                'value': customerNumber
              },
              {
                'name': '原因',
                'value': customerSeason
              }
            ],
            'rule': {
              'code': 'blackList'
            },
            'startTime': startTime,
            'endTime': endTime,
            'timeType': This.propertyRuleInfo.termValue, // 0 临时 1 长期
            'state': This.propertyRuleInfo.isOpenValue,
            'propertyId': currItem.id,
            'screenType': This.propertyRuleInfo.filterValue === '1' ? 'BLACKLIST' : 'BLACKANDWHITELIST', // 1 黑名单 2 黑-白名单(主条件的黑-白名单编码：BLACKANDWHITELIST)
            'sideCondition': []
          }],
          'dimensionType': dimensionValue,
          'name': currItem.name,
          'propertyId': currItem.id,
          'source': 'BUSINESS',
          'state': This.propertyRuleInfo.isOpenValue
        }
        return data
      }
      // 主条件数据 end
      if (!mainData) {
        return false
      }
      // 组装副条件数据列表
      let sideData = getSideData()
      // 副条件数据
      function getSideData () {
        let newlist = []
        let data = This.propertyRuleInfo.list
        if (data.length > 0) {
          let isAllOk = true // 是不是循环里面的数据都没有问题
          data.forEach((item, index) => {
            let dimensionValue = item.dimensionValue
            if (!dimensionValue) {
              // 属性名必选
              This.$message.error('请给第' + (index + 1) + '副条件选择属性维度')
              isAllOk = false
              return false
            }
            if (!item.name) {
              // 属性名必选
              This.$message.error('请给第' + (index + 1) + '副条件选择属性名称')
              isAllOk = false
              return false
            }
            if (!item.value) {
              // 属性值必填
              if (dimensionValue === 'SKU') {
                This.$message.error('请给第' + (index + 1) + '副条件填写属性值或导入sku')
                isAllOk = false
                return false
              } else {
                if (dimensionValue !== 'OTHER') {
                  This.$message.error('请给第' + (index + 1) + '副条件填写属性值')
                  isAllOk = false
                  return false
                }
              }
            }
            let startTime = item.startTime
            if (item.startTime) {
              startTime = This.formatDate(startTime)
            }
            let endTime = item.endTime
            if (endTime) {
              endTime = This.formatDate(endTime)
            }
            if (item.termValue === 0) {
              // 期限为临时时，需要有时间段
              if (!item.startTime || !item.endTime) {
                This.$message.error('请给第' + (index + 1) + '副条件选择开始/结束时间')
                isAllOk = false
                return false
              }
            } else {
              // 长期不需要传递时间
              This.propertyRuleInfo.list[index].startTime = ''
              This.propertyRuleInfo.list[index].endTime = ''
              startTime = ''
              endTime = ''
            }
            let currItem = null
            // 获取当前选中的属性项
            item.propertiesOptions.forEach((itemp, index) => {
              if (itemp.name === item.name) {
                currItem = itemp
              }
            })
            let itemData = {
              'blackListRuleList': [
                {
                  'name': This.propertyRuleInfo.filterValue === '1' ? '黑名单' : '白名单',
                  'value': item.value
                },
                {
                  'name': '客户',
                  'value': ''
                },
                {
                  'name': '客户编码',
                  'value': ''
                },
                {
                  'name': '原因',
                  'value': ''
                }
              ],
              'rule': {
                'code': 'blackList'
              },
              'startTime': startTime,
              'endTime': endTime,
              'timeType': item.termValue, // 0 临时 1 长期
              'state': item.isOpenValue,
              'propertyId': currItem.id,
              'screenType': This.propertyRuleInfo.filterValue === '1' ? 'BLACKLIST' : 'WHITELIST' // 1 黑名单 2 白名单(副条件的白名单编码：WHITELIST)
            }
            newlist.push(itemData)
          })
          return isAllOk ? newlist : isAllOk
        } else {
          return newlist
        }
      }
      if (!sideData) {
        return false
      }
      if (This.propertyRuleInfo.filterValue === '2') {
        // 黑白名单
        if (sideData && sideData.length < 1) {
          This.$message.error('主条件是黑-白名单时，必须有副条件(白名单)')
          return false
        }
      }
      // 副条件数据列表 end
      mainData.constraintDTOList[0].sideCondition = sideData
      // console.log('提交总数据：合并后的主条件')
      // console.log(mainData)
      if (This.isModifyState === '1') {
        let constraintId = This.currConstraintId
        updatebusinessProperty(constraintId, mainData).then(res => {
          // console.log(res);
          if (res.code === 0) {
            // console.log('修改属性规则-保存')
            this.$message.success('修改成功')
            This.goGetbusinessPropertyList()
            this.showRuleDialog = false
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        // 添加
        addbusinessProperty(mainData).then(res => {
          console.log(res);
          if (res.code === 0) {
            // console.log('添加属性规则-保存')
            This.goGetbusinessPropertyList()
            this.$message.success('添加成功')
            this.showRuleDialog = false
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    formatDate (date) {
      let ndate = new Date(date)
      let year = ndate.getFullYear()
      let month = ndate.getMonth() + 1
      let day = ndate.getDate()
      return year + '-' + this.formatTen(month) + '-' + this.formatTen(day)
    },
    formatTen (num) {
      return num > 9 ? (num + '') : ('0' + num)
    },
    goGetbusinessPropertyList () {
      let This = this
      // 获取规则属性列表
      let data = {
        categoryId: this.pageParams.id,
        paramValue: this.search.propertyName
      }
      this.loading.table = true
      getbusinessPropertyList(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          console.log(res.data)
          if (this.search.propertyName) {
            // console.log('打开拓展')
            this.templateList = []
            this.expandAll = true
          } else {
            // console.log('关闭拓展')
            this.templateList = []
            this.expandAll = false
          }
          setTimeout(function () {
            This.templateList = res.data
            This.loading.table = false
          }, 200)
          this.loading.page = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goGetbusinessPropertyNameList (propertiesValue, index) {
      // 根据属性维度获取属性选项列表
      let dimensionValue = ''
      if (index === 'main' || (!index && index !== 0)) {
        // 主条件
        this.propertyRuleInfo.name = propertiesValue || ''
        dimensionValue = this.propertyRuleInfo.dimensionValue
      } else {
        // 副条件
        this.propertyRuleInfo.list[index].name = propertiesValue || ''
        dimensionValue = this.propertyRuleInfo.list[index].dimensionValue
      }
      let dimensionText = '商品'
      if (dimensionValue === 'PRODUCT') {
        // 商品
        dimensionText = '商品'
      } else if (dimensionValue === 'NEGATIVEDEMAND') {
        // 负需求
        dimensionText = '负需求'
      } else if (dimensionValue === 'ONLINENEGATIVEDEMAND') {
        // 在线负需求
        dimensionText = '在线负需求'
      } else if (dimensionValue === 'BUYPRICE') {
        // 采购价格
        dimensionText = '采购价格'
      } else if (dimensionValue === 'SKU') {
        // SKU
        dimensionText = 'SKU'
      } else if (dimensionValue === 'OTHER') {
        // 其他
        dimensionText = '其他'
      } else if (dimensionValue === 'SDI') {
        // 供货地图
        dimensionText = '供货地图'
      }
      let data = {
        categoryId: this.pageParams.id,
        dimensionType: dimensionText
      }
      getbusinessPropertyNameList(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          console.log(res.data)
          if (index === 'main' || (!index && index !== 0)) {
            // 主条件
            this.propertyRuleInfo.propertiesOptions = res.data
          } else {
            // 副条件
            this.propertyRuleInfo.list[index].propertiesOptions = res.data
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goGetbusinessPropertyValueTemplate () {
      let data = {
        excelTemplateType: 'SKU_IMPORT'
      }
      getbusinessPropertyValueTemplate(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          this.propertyValueTemplate = res.data[0].path
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    searchCustomer(keywords) {
      console.log('搜索值=' + keywords)
      let This = this
      if (keywords) {
        This.isLoading = true
        This.listQueryInfo.current = 1
        // 首次搜索加载
        this.loading.search = true
        This.prevKeywords = keywords
        let param = {
          nameOrNumberLike: keywords,
          page: (This.listQueryInfo.current - 1),
          size: This.listQueryInfo.pageSize,
          sort: ''
        }
        getbusinessCustomer(param).then(res => {
          if (res.code === 0) {
            This.loading.search = false;
            This.customerOptions = res.data.result.rows
            This.showRuleDialog = true
            if (res.data.result.rows.length > 0) {
              This.listQueryInfo.current++
            }
            This.isLoading = false
            if (This.listQueryInfo.current === 2) {
              // 仅在首次
              This.$nextTick(() => {
                console.log('首次渲染')
                /**
                 * vue中通过$refs获取元素的方法的使用
                 * **/
                // console.log(this.$refs.searchselect.$children[1])
                // console.log(this.$refs.searchselect.$children[1].$el)
                // console.log(this.$refs.searchselect.$children[1].$el.offsetHeight)
                let container = this.$refs.searchselect.$children[1].$el.getElementsByClassName('el-select-dropdown__wrap')[0]
                let list = this.$refs.searchselect.$children[1].$el.getElementsByClassName('el-select-dropdown__list')[0]
                container.addEventListener('scroll', function (e) {
                  let containerHeight = container.offsetHeight
                  let listHeight = list.offsetHeight
                  // console.log(e)
                  // console.log('滚动高度=' + container.scrollTop)
                  // console.log('父级高度=' + containerHeight)
                  // console.log('子级高度=' + listHeight)
                  if (container.scrollTop >= listHeight - containerHeight - 200) {
                    // 当滚动到底部200px时触发加载下一页
                    if (!This.isLoading) {
                      This.searchCustomerPage()
                    }
                  }
                }, true)
              })
            }
          } else {
            This.$message.error(res.msg)
            This.isLoading = false
          }
        })
      } else {
        this.customerOptions = []
        this.showRuleDialog = true
      }
    },
    searchCustomerPage() {
      console.log('分页搜索')
      let This = this
      if (!This.isLoading) {
        let keywords = This.prevKeywords
        This.isLoading = true
        // 分页搜索加载
        let param = {
          nameOrNumberLike: keywords,
          page: (This.listQueryInfo.current - 1),
          size: This.listQueryInfo.pageSize,
          sort: ''
        }
        getbusinessCustomer(param).then(res => {
          if (res.code === 0) {
            This.loading.search = false
            console.log('客户1')
            console.log(This.customerOptions)
            let customerOptions = This.customerOptions
            customerOptions = customerOptions.concat(res.data.result.rows)
            console.log(customerOptions)
            console.log('客户2')
            This.customerOptions = customerOptions
            console.log(This.customerOptions)
            This.showRuleDialog = true
            if (res.data.result.rows.length > 0) {
              This.listQueryInfo.current++
            }
            This.isLoading = false
          } else {
            This.$message.error(res.msg)
            This.isLoading = false
          }
        })
      }
    },
    addNewProperty () {
      if (this.propertyRuleInfo.list.length >= 5) {
        return false;
      }
      let unitarr = {
        value: '',
        name: '',
        isOpenValue: true,
        startTime: '',
        endTime: '',
        termValue: 1,
        dimensionValue: '',
        propertiesOptions: [],
        filterValue: '1',
        customerName: '',
        customerNumber: '',
        customerSeason: '',
        loading: false
      }
      this.propertyRuleInfo.list.push(unitarr)
    },
    deleteNewProperty (index) {
      console.log(index)
      let This = this
      This.$confirm('确认要删除这条规则吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        This.propertyRuleInfo.list.splice(index, 1)
      }).catch(() => {
        console.log('取消删除')
      })
    },
    daorufile (index) {
      this.uploadIndex = index
      this.$refs.uploadBox.$children[0].$refs.input.click();
      console.log(this.$refs)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../../../style/colors.scss";
.data-manage-import-container{
  min-height: 500px;
  .data-manage-import-center{
    border: 1px solid #eee;
    border-bottom: none;
    padding-top: 10px;
    .data-manage-import-add{
      padding: 10px 20px 20px;
      overflow: hidden;
      .importtitle{
        float: left;
        font-size: 18px;
        line-height: 32px;
      }
      .importbtn{
        float: right;
      }
    }
  }
  .dialogTitle{
    line-height: 32px;
    text-align: right;
  }
  .downTemplate {
    text-align: left;
    padding-left: 10px;
    p {
      margin-bottom: 5px;
    }
    button {
      width: 90px;
    }
    a {
      color: #fff;
      display: block;
    }
  }
  .rulesetcont {
    min-height: 300px;
    margin-bottom: 10px;
    .el-row{
      margin-bottom: 10px;
      .ts {
        color: #e10000;
        font-size: 12px;
        padding-top: 5px;
      }
    }
    .rule_li {
      background: #f5f5f5;
      padding:20px 20px 5px 20px;
    }
    .rule_gap {
      text-align: center;
      color: $color-blue-200;
      font-size: 12px;
      padding:15px 0;
      display: flex;
      .span1{
        flex: 1;
        background: $color-blue-200;
        height: 1px;
        margin-top: 8px;
      }
      .span2 {
        padding:0 6px;
      }
    }
    .rule_others {
      .rule_li {
        margin-bottom: 10px;
        position: relative;
        .del_li {
          display: inline-block;
          font-size: 26px;
          color: $color-blue-200;
          position: absolute;
          right: 20px;
          top: 50%;
          cursor: pointer;
          z-index: 10;
          opacity: .8;
          &:hover {
            opacity: 1;
          }
        }
      }
    }
    .rule_add {
      font-size: 14px;
      color: $color-blue-200;
      cursor: pointer;
      span {
        display: inline-block;
        padding: 10px 20px;
        &:hover {
          opacity: .8;
        }
      }
    }
  }
  .ellipse2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    cursor: default;
    max-height:46px;
  }
}
</style>
<style lang="scss" >
.importlist {
  .el-table {
    max-height: none !important;
    height: auto !important;
    .el-table__placeholder {
      display: none !important;
    }
  }
  /*修改展开按钮的样式 start*/
  /*1.取消原本展开的旋转动效*/
  .el-table__expand-icon{
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  /*2.展开按钮未点击的样式是加号带边框*/
  .el-table__expand-icon .el-icon-arrow-right:before{
    content: "\e6d9";
    border: 1px solid #ccc;
    padding: 2px;
    font-size: 8px;
  }
  /*3.展开按钮点击后的样式是减号带边框*/
  .el-table__expand-icon--expanded .el-icon-arrow-right:before{
    content: "\e6d8";
  }
  /*修改展开按钮的样式 end*/
  .el-table__row.el-table__row--level-1 {
    background: #f6f6f6;
  }
}
</style>
