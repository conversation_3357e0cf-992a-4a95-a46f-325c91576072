<template>
  <div class="page data-manage-import-container" v-loading="loading.page">
    <div class="data-manage-import-center" v-show="!loading.page">
      <div class="data-manage-search">
        <div class="search-form">
          <span class="dialogTitle">规则名称：</span>
          <span>
            <el-input
              v-model="search.name"
            />
          </span>
          <span class="dialogTitle">规则状态：</span>
          <span>
            <el-select v-model="search.state" placeholder="请选择">
              <el-option
                v-for="item in ruleStateOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </span>
          <span class="dialogTitle">规则场景：</span>
          <span>
            <el-select v-model="search.scene" placeholder="请选择">
              <el-option
                v-for="item in ruleSceneOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </span>
          <span class="btns">
                <el-button @click="startSearch()" type="primary">查询</el-button>
                <el-button @click="resetSearch()">重置</el-button>
              </span>
        </div>
      </div>
      <div class="data-manage-import-add" style="display: none;">
        <p class="importtitle">商机规则管理</p>
        <div class="importbtn">
          <el-button size="small" type="primary">新建规则</el-button>
        </div>
      </div>
      <div class="importlist">
        <el-table
          v-loading="loading.table"
          :data="templateList"
          border
          fit
          highlight-current-row
          :max-height="screenHeight"
        >
          <el-table-column label="规则名称" align="center" prop="name" />
          <el-table-column label="规则场景" align="center" prop="categoryScene">
            <template slot-scope="scope">
              {{scope.row.categoryScene === 'DIRECTLY' ? '直发' : (scope.row.categoryScene === 'INDIRECTLY' ? '非直发' : (scope.row.categoryScene === 'SELFINDIRECTLY' ? '自研非直发' : (scope.row.categoryScene === 'SELFDIRECTLY' ? '自研直发' : (scope.row.categoryScene === 'AUTODIRECTLY' ? '自动释放直发' : (scope.row.categoryScene === 'AUTOINDIRECTLY' ? '自动释放非直发' : '')))))}}
            </template>
          </el-table-column>
          <el-table-column label="规则类型" align="center">公共</el-table-column>
          <el-table-column label="创建时间" align="center" prop="createdTime" />
          <el-table-column label="更新时间" align="center" prop="lastModifiedTime" />
          <el-table-column label="操作" align="center" >
            <template slot-scope="scope">
              <el-button type="text" @click="goRuleDetail(scope.$index, scope.row)">详情</el-button>
              <el-button type="text" @click="goEditRule(scope.$index, scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout=" total, prev, pager, next, jumper"
      @pagination="pageClick"
    />
  </div>
</template>

<script>
import {
  getbusinessRuleList
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
import moment from 'moment'
export default {
  name: 'businessCenterRule',
  data () {
    return {
      loading: {
        table: true,
        page: true
      },
      search: {
        name: '',
        state: '',
        scene: ''
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      total: 0,
      createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
      createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      templateList: [], // 列表
      ruleStateOptions: [{ // 规则状态
        value: '0',
        label: '已关闭'
      }, {
        value: '1',
        label: '已开启'
      }],
      ruleSceneOptions: [{ // 规则场景
        value: '1',
        label: '非直发'
      }, {
        value: '2',
        label: '直发'
      }, {
        value: '3',
        label: '自研非直发'
      }, {
        value: '4',
        label: ' 自研直发'
      }, {
        value: '5',
        label: '自动释放非直发'
      }, {
        value: '6',
        label: '自动释放直发'
      }],
      screenHeight: '600'
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetbusinessRuleList()
  },
  mounted () {
    // 设置表格高度为屏幕高度，使其能完全显示在屏幕内
    this.screenHeight = window.screen.height - 300
    let This = this
    document.addEventListener('keydown', function (e) {
      // console.log('点击了=' + window.event.keyCode)
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showRuleDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.storeAddPropertyRule()
        } else if (This.search.name || This.search.state || This.search.scene) {
          // 如果搜索里有值，则触发搜索列表方法
          This.startSearch()
        }
      }
    })
  },
  methods: {
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
    },
    getRuleDel (index, row) {
      console.log(index, row)
      let This = this
      let id = row.id
      console.log('id=' + id)
      This.$message.success('删除成功')
      This.templateList.splice(index, 1)
    },
    resetSearch () {
      this.search = {
        name: '',
        state: '',
        scene: ''
      }
      this.startSearch()
    },
    startSearch () {
      this.goGetbusinessRuleList()
    },
    goRuleDetail (index, row) {
      console.log('跳转详情页面')
      console.log(row)
      let id = row.id
      let scene = row.categoryScene
      this.$router.push(`./ruleDetail/1/${id}/${scene}`);
    },
    goEditRule (index, row) {
      console.log('跳转编辑页面')
      let id = row.id
      let scene = row.categoryScene
      this.$router.push(`./ruleDetail/2/${id}/${scene}`);
    },
    goGetbusinessRuleList () {
      this.loading.table = true
      let data = {
        name: this.search.name,
        state: this.search.state,
        scene: this.search.scene
      }
      getbusinessRuleList(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          console.log(res.data)
          this.templateList = res.data
          this.loading.page = false
          this.loading.table = false
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.data-manage-import-container{
  min-height: 500px;
  .data-manage-import-center{
    border: 1px solid #eee;
    border-bottom: none;
    padding-top: 10px;
    .data-manage-import-add{
      padding: 0 20px 10px;
      overflow: hidden;
      .importtitle{
        float: left;
        font-size: 18px;
        line-height: 32px;
      }
      .importbtn{
        float: right;
      }
    }
  }
  .data-manage-search {
    border-bottom: 1px solid #eee;
    padding: 12px 0 20px;
    margin-bottom: 20px;
    .search-meau {
      padding-left: 60px;
      padding-bottom: 10px;
    }
    .search-form {
      padding-top: 10px;
      padding-left: 60px;
      span {
        display: inline-block;
        margin-right: 15px;
        input {
          width: 140px;
        }
      }
      .dialogTitle {
        margin-right: 0;
        padding-right: 0;
      }
      .btns {
        span {
          margin: 0;
        }
      }
    }
  }
}
.dialogTitle{
  line-height: 32px;
}
.importset{
  .el-row{
    margin-bottom: 15px;
  }
}
</style>
