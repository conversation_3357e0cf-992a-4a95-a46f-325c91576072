<template>
  <div class="page" v-loading="loading.page">
    <div class="page_center">
      <div class="fifter">
        <el-row :span="24" class="searchBox">
          <el-col :span="2" class="dialogTitle">账号：</el-col>
          <el-col :span="4" >
            <el-input
              placeholder="账号"
              v-model="search.account"
            />
          </el-col>
          <el-col :span="2" class="dialogTitle">物料组：</el-col>
          <el-col :span="4" >
            <el-select
              style="width: 100%;"
              v-model="search.productGroupId"
              filterable
              placeholder="请选择">
              <el-option
                v-for="item in materialOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5" style="padding-left: 10px" >
            <el-button @click="searchList" icon="el-icon-search" size="small" type="primary">查询</el-button>
            <el-button @click="searchReset" icon="el-icon-close" size="small" type="info">清空</el-button>
          </el-col>
          <el-col :span="7" style="text-align: right;">
            <el-button style="margin-right: 20px;"  icon="el-icon-plus" size="medium" @click="openRuleDialog" type="primary">新增</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="c_tables">
        <el-table
          v-loading="loading.table"
          :data="tableData"
          border
          fit
          highlight-current-row
        >
          <template slot="empty">
            <div class="empty">
              <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
            </div>
          </template>
          <el-table-column align="center" width="80" label="序号">
            <template slot-scope="scope">
              {{scope.$index + 1}}
            </template>
          </el-table-column>
          <!--<el-table-column align="center" prop="id" width="100" label="id"></el-table-column>-->
          <el-table-column align="center" prop="productGroupName" label="物料组"></el-table-column>
          <el-table-column align="center" prop="userName" label="用户"></el-table-column>
          <el-table-column align="center" prop="account" label="账户"></el-table-column>
          <el-table-column align="center" prop="createdTime" label="创建时间"></el-table-column>
          <el-table-column align="center" label="操作">
            <template slot-scope="scope">
              <el-button @click="deleteApp(scope.row, scope.$index)" type="text" size="small">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="c_pagination">
        <pagination
          v-show="total > 0"
          :total="total"
          align="right"
          :page.sync="listQueryInfo.current"
          :limit.sync="listQueryInfo.pageSize"
          layout=" total, prev, pager, next, jumper"
          @pagination="pageClick"
        />
      </div>
      <!--添加弹出框-->
      <el-dialog
        width="580px"
        title="配置信息"
        class="dialogClass"
        :visible.sync="showAddDialog"
        :close-on-click-modal="false">
        <div class="ruleset">
          <div class="rulesetcont">
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>用户名：</el-col>
              <el-col :span="16">
                <el-input
                  v-model="appForm.userName"
                  placeholder="用户名"
                />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>账号：</el-col>
              <el-col :span="16">
                <el-input
                  v-model="appForm.account"
                  placeholder="账号"
                />
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="6" class="dialogTitle"><i>*</i>物料组：</el-col>
              <el-col :span="16">
                <el-select
                  style="width: 100%;"
                  v-model="appForm.productGroupId"
                  filterable
                  placeholder="请选择">
                  <el-option
                    v-for="item in materialOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id">
                  </el-option>
                </el-select>
              </el-col>
            </el-row>
          </div>
          <el-row :span="24">
            <el-col :span="24" style="text-align: right;">
              <el-button size="medium" @click="closeAddDialog" style="margin-left: 20px;">关闭</el-button>
              <el-button size="medium" @click="storeAddDialog" type="primary">确认</el-button>
            </el-col>
          </el-row>
        </div>
      </el-dialog>
      <!--添加弹出框 end-->
    </div>
  </div>
</template>

<script>
import {
  getUserProductList,
  getProductGroup,
  createUserProduct,
  deleteUserProduct
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
export default {
  data () {
    return {
      loading: {
        page: true,
        table: true
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showAddDialog: false,
      search: { // 搜索值
        account: '', // 账号
        productGroupId: '' // 物料组
      },
      appForm: { // 添加form应用的内容
        id: '', // 修改时候的appid
        account: '',
        userName: '',
        productGroupId: ''
      },
      total: 0,
      tableData: [],
      materialOptions: [], // 物料选择
      currAppInfo: null // 当前app信息,修改时候传递给接口使用
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetApps()
    this.goGetProductGroup()
  },
  mounted () {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.showAddDialog) {
          // 如果当前显示的是新建/修改的弹出框，触发新建/修改方法
          This.gocreateApps()
        } else if (This.search.account || This.search.productGroupId) {
          // 如果搜索里有值，则触发搜索列表方法
          This.searchList()
        }
      }
    })
  },
  methods: {
    goGetProductGroup () {
      // 获取物料组列表
      let data = {}
      getProductGroup(data).then(res => {
        if (res.code === 0) {
          let materialOptions = res.data
          this.materialOptions = materialOptions
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    openRuleDialog () {
      // 打开添加数据窗口
      this.appForm = { // 初始化form应用的内容
        id: '',
        postName: '',
        roleName: ''
      }
      this.currAppInfo = null
      this.showAddDialog = true
    },
    closeAddDialog () {
      this.showAddDialog = false
    },
    storeAddDialog () {
      // 提交 修改、添加的操作
      this.gocreateApps()
    },
    modifyApp (row, index) {
      console.log('修改数据')
      this.appForm = {
        id: row.id, // 修改时候的appid
        postName: row.postName,
        roleName: row.roleName
      }
      this.currAppInfo = row
      this.showAddDialog = true
    },
    deleteApp (row, index) {
      this.$confirm('您确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          id: row.id
        }
        deleteUserProduct(params).then(res => {
          this.$message.success('删除成功')
          this.tableData.splice(index, 1)
        })
      }).catch(() => {
        console.log('取消删除')
      })
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetApps()
    },
    goGetApps () {
      // 【1】获取数据列表
      let This = this
      let params = {
        page: This.listQueryInfo.current - 1,
        size: This.listQueryInfo.pageSize,
        sort: 'createdTime,desc'
      }
      let data = {
        account: This.search.account,
        productGroupId: This.search.productGroupId
      }
      getUserProductList(params, data).then(res => {
        if (res.code === 0) {
          This.tableData = res.data.content
          This.total = res.data.totalElements
        } else {
          This.$message.error(res.msg)
        }
        This.loading.table = false
        This.loading.page = false
      })
    },
    searchList () {
      this.loading.table = true
      this.listQueryInfo.current = 1
      this.goGetApps()
    },
    searchReset (state) {
      // 搜索条件清空
      this.search = {
        account: '',
        productGroupId: ''
      }
      if (!state) {
        // 不是添加时候的重置数据
        this.goGetApps()
      }
    },
    gocreateApps () {
      // 新建、编辑数据
      let This = this
      let account = this.appForm.account
      let userName = this.appForm.userName
      let productGroupId = this.appForm.productGroupId
      let productGroupName = this.appForm.productGroupName
      let materialOptions = This.materialOptions
      materialOptions.forEach((item) => {
        if (item.id === productGroupId) {
          productGroupName = item.name
        }
      })
      This.searchReset('create')
      if (!account) {
        this.$message.error('请输入账号')
        return false
      }
      if (!userName) {
        this.$message.error('请输入用户')
        return false
      }
      if (!productGroupId) {
        this.$message.error('请选择物料组')
        return false
      }
      let param = {
        account: account,
        userName: userName,
        productGroupName: productGroupName,
        productGroupId: productGroupId
      }
      // 添加数据
      createUserProduct(param).then(res => {
        if (res) {
          This.$message.success('创建成功')
          // 新增成功创建数据列表
          This.goGetApps()
          this.showAddDialog = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .searchBox {
    padding: 20px 0;
    border: 1px solid #eee;
    margin: 20px 0;
  }
  .dialogTitle {
    text-align: right;
    box-sizing: border-box;
    padding-right: 10px;
    line-height: 32px;
    i {
      color: red;
      position: relative;
      top: 2px;
      right: 4px;
    }
  }
  .el-row {
    margin-bottom: 10px;
    .item-btn {
      position: relative;
      .item-close {
        position: absolute;
        right:-8px;
        top:-8px;
        font-size: 16px;
      }
    }
  }
</style>
