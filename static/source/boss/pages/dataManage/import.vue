<template>
  <div class="page data-manage-import-container" v-loading="loading.page">
    <div class="data-manage-import-center" v-show="!loading.page">
      <div class="data-manage-import-add">
        <p class="importtitle">导入记录</p>
        <div class="importbtn">
          <el-button size="small" v-if="hasDelAuth" @click="importToDeleteCatalog" type="primary">删除属性规则</el-button>
          <!-- 需求变更 功能下线 <el-button size="small" @click="importToRepairData" type="primary">导入待修复数据</el-button> -->
          <el-button size="small" @click="downloadTemplate" type="primary">下载属性规则模板</el-button>
          <el-button size="small" v-if="hasImportAuth" @click="openImportDialog" type="primary">导入属性规则</el-button>
          <el-button size="small" v-if="exportCRMDataButton" @click="openImportCRMDialog" type="primary">导入CRM数据</el-button>
        </div>
      </div>
      <div class="importlist">
        <el-table v-loading="loading.table" :data="templateList" border fit highlight-current-row height="500">
          <el-table-column width="200" label="导入文件名" align="center" prop="name" />
          <el-table-column label="导入时间" align="center" prop="time">
            <template slot-scope="scope">
              {{ scope.row.time ? scope.row.time : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="数据条数" align="center" prop="count">
            <template slot-scope="scope">
              {{ scope.row.count ? scope.row.count : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="导入规则" align="center" prop="type">
            <template slot-scope="scope">
              {{ importRuleMap[scope.row.type]}}
            </template>
          </el-table-column>
          <el-table-column label="导入人员" align="center" prop="staff">
            <template slot-scope="scope">
              {{ scope.row.staff ? scope.row.staff : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center" prop="remark">
            <template slot-scope="scope">
              {{ scope.row.remark ? scope.row.remark : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="导入问题" align="center" prop="remark">
            <template slot-scope="scope">
              <div v-if="scope.row.message">
                <el-popover placement="top-start" width="500" trigger="hover" :content="scope.row.message">
                  <div class="ellipse2" slot="reference">
                    {{ scope.row.message ? scope.row.message : '-' }}
                  </div>
                </el-popover>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="导入状态" align="center" prop="state">
            <template slot-scope="scope">
              {{
                scope.row.state == 'SUCCESS'
                  ? '导入成功'
                  : scope.row.state == 'WORKING'
                  ? '导入中'
                  : scope.row.state == 'FAIL'
                  ? '导入失败'
                  : scope.row.state == 'QUEUING'
                  ? '排队中'
                  : ''
              }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <pagination v-show="total > 0" :total="total" align="right" :page.sync="listQueryInfo.current" :limit.sync="listQueryInfo.pageSize" layout=" total, prev, pager, next, jumper" @pagination="pageClick" />
    <!--导入excel-->
    <el-dialog width="680px" v-if="showImportDialog" title="导入配置" :visible.sync="showImportDialog" :close-on-click-modal="false">
      <div class="importset">
        <el-row :span="24">
          <el-col :span="4" class="dialogTitle">文件选择：</el-col>
          <el-col :span="16">
            <el-input :disabled="true" v-model="filePath" />
          </el-col>
          <el-col :span="4">
            <el-upload class="btn-upload" :action="uploadUrl" :show-file-list="false" :on-success="onUploadSuccess" :before-upload="beforeUpload" :on-error="onUploadError" accept=".xlsx,.xls" name="file">
              <el-button :loading="loading.import" size="small" type="primary" style="margin-left: 10px">选择</el-button>
            </el-upload>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4" class="dialogTitle">备注：</el-col>
          <el-col :span="16">
            <el-input type="textarea" :rows="3" placeholder="请输入内容" v-model.trim="remarks">
            </el-input>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right">
            <el-button size="medium" @click="closeImportDialog">关闭</el-button>
            <el-button size="medium" @click="sureImport" type="primary" style="margin-left: 20px">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--导入excel end-->
    <!--导入CRM-->
    <el-dialog width="780px" title="导入CRM数据" :visible.sync="showImportCRMDialog" :close-on-click-modal="false">
      <div class="importset">
        <el-row :span="24" :gutter="2">
          <el-col :span="4" class="dialogTitle">CRM类目：</el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.CRMcategoryLevel1Value" :disabled="
                !importCRMForm.CRMcategoryListLevel1 ||
                importCRMForm.CRMcategoryListLevel1.length < 0
              " @change="categorySelectCRM(1)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.CRMcategoryListLevel1" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.CRMcategoryLevel2Value" :disabled="
                !importCRMForm.CRMcategoryListLevel2 ||
                importCRMForm.CRMcategoryListLevel2.length < 0
              " @change="categorySelectCRM(2)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.CRMcategoryListLevel2" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.CRMcategoryLevel3Value" :disabled="
                !importCRMForm.CRMcategoryListLevel3 ||
                importCRMForm.CRMcategoryListLevel3.length < 0
              " @change="categorySelectCRM(3)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.CRMcategoryListLevel3" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.CRMcategoryLevel4Value" :disabled="
                !importCRMForm.CRMcategoryListLevel4 ||
                importCRMForm.CRMcategoryListLevel4.length < 0
              " @change="categorySelectCRM(4)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.CRMcategoryListLevel4" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :span="24" :gutter="2">
          <el-col :span="4" class="dialogTitle">治理平台类目：</el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.categoryLevel1Value" :disabled="
                !importCRMForm.categoryListLevel1 ||
                importCRMForm.categoryListLevel1.length < 0
              " @change="categorySelect(1)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.categoryListLevel1" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.categoryLevel2Value" :disabled="
                !importCRMForm.categoryListLevel2 ||
                importCRMForm.categoryListLevel2.length < 0
              " @change="categorySelect(2)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.categoryListLevel2" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.categoryLevel3Value" :disabled="
                !importCRMForm.categoryListLevel3 ||
                importCRMForm.categoryListLevel3.length < 0
              " @change="categorySelect(3)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.categoryListLevel3" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select v-model="importCRMForm.categoryLevel4Value" :disabled="
                !importCRMForm.categoryListLevel4 ||
                importCRMForm.categoryListLevel4.length < 0
              " @change="categorySelect(4)" placeholder="请选择">
              <el-option v-for="item in importCRMForm.categoryListLevel4" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4" class="dialogTitle">导入规则：</el-col>
          <el-col :span="16">
            <el-select v-model="importCRMForm.importRuleType" placeholder="请选择">
              <el-option v-for="item in importRuleOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <!--<el-row :span="24">-->
        <!--<el-col :span="4" class="dialogTitle">备注：</el-col>-->
        <!--<el-col :span="16">-->
        <!--<el-input-->
        <!--type="textarea"-->
        <!--:rows="3"-->
        <!--placeholder="请输入内容"-->
        <!--v-model.trim="importCRMForm.remarks">-->
        <!--</el-input>-->
        <!--</el-col>-->
        <!--</el-row>-->
        <el-row :span="24">
          <el-col :span="24" style="text-align: right">
            <el-button size="medium" @click="closeImportCRMDialog">关闭</el-button>
            <el-button size="medium" @click="sureImportCRMData" type="primary" style="margin-left: 20px">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--导入CRM end-->

    <!-- 待修复数据导入 -->
    <el-dialog width="500px" :visible.sync="toRepairVisible" title="待修复数据导入">
      <el-upload class="repair_upload" :action="uploadUrl" drag :file-list="fileList" accept=".xlsx, .xls" :before-upload="handleCheckFile" :on-success="handleSucceed">
        <i style="font--size: 14px" class="el-icon-upload"></i>
        <div class="el-upload__text">
          仅支持.xlsx文件，支持拖拽上传
        </div>
      </el-upload>

      <span slot="footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="tobeRepairLoading">确定</el-button>
      </span>
    </el-dialog>
    <!-- 删除属性规则 -->
    <el-dialog width="500px" v-if="toDelCatalogShow" :visible.sync="toDelCatalogShow" title="删除属性规则" :close-on-click-modal="false">
      <el-upload class="delete_upload" :action="uploadUrl" drag accept=".xlsx, .xls" :before-upload="handleCheckDelFile" :on-success="handleDelFileSucceed" :ref="'elUpload'" :on-remove="handleRemove">
        <i style="font--size: 14px" class="el-icon-upload"></i>
        <div class="el-upload__text">导入删除属性</div>
        <div class="el-upload__tip">仅支持.xlsx文件</div>
      </el-upload>
      <a class="link-btn" @click="downloadDelTemplate">删除属性规则模版.xlsx</a>
      <span slot="footer">
        <el-button type="primary" @click="handleSubmitDelFile" :loading="delLoading">确定</el-button>
        <el-button @click="() => toDelCatalogShow=false">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getExcelImport,
  getExcelImportHistory,
  getCategoriesTree,
  importCRMData,
  getCRMcategory,
  getUploadTemplate,
  tobeRepairImport,
  getDelTemplate,
  uploadDelTemplate
} from '@/api/dataManage'
import { mapState } from 'vuex'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { findButton, importRuleMap } from './common/untils/tools'
export default {
  name: 'directOrderList',
  data() {
    return {
      loading: {
        table: false,
        import: false,
        page: true
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      total: 0,
      rules: {},
      createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
      createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      templateList: [], // 文件列表
      showImportDialog: false,
      showImportCRMDialog: false,
      uploadUrl: '/fe-upload/api/upload/',
      remarks: '', // 备注信息
      filePath: '', // 传oss后生成的文件路径
      fileName: '', // 文件在本地的名字
      tobeRepairFilePath: '',
      tobeRepirFileName: '',
      importRuleOptions: [
        {
          value: 'COVER',
          label: '覆盖冲突数据'
        },
        {
          value: 'INHERENT',
          label: '保留原有数据'
        },
        {
          value: 'IMPORT_AFTER_EMPTYING',
          label: '清空原有数据后导入'
        }
        // {
        //   value: 'COVER_NEW',
        //   label·: '覆盖冲突数据新版'
        // }
      ],
      ruleValue: '',
      timer: null,
      exportCRMDataButton: false, // 是否显示导入crm数据按钮
      importCRMForm: {
        remarks: '', // 备注
        importRuleType: '', // 导入规则
        categoryList: null, // 目录树列表数据
        categoryListLevel1: null, // 目录树第一级列表数据
        categoryListLevel2: null, // 目录树第二级列表数据
        categoryListLevel3: null, // 目录树第三级列表数据
        categoryListLevel4: null, // 目录树第四级列表数据
        categoryLevel1Value: null, // 目录树第一级选中结果
        categoryLevel2Value: null, // 目录树第二级选中结果
        categoryLevel3Value: null, // 目录树第三级选中结果
        categoryLevel4Value: null, // 目录树第四级选中结果
        CRMcategoryListLevel1: null, // 目录树第一级列表数据
        CRMcategoryListLevel2: null, // 目录树第二级列表数据
        CRMcategoryListLevel3: null, // 目录树第三级列表数据
        CRMcategoryListLevel4: null, // 目录树第四级列表数据
        CRMcategoryLevel1Value: null, // 目录树第一级选中结果
        CRMcategoryLevel2Value: null, // 目录树第二级选中结果
        CRMcategoryLevel3Value: null, // 目录树第三级选中结果
        CRMcategoryLevel4Value: null // 目录树第四级选中结果
      },
      activatedState: true, // 当前路由的激活状态
      toRepairVisible: false,
      radioVal: 1,
      fileList: [],
      tobeRepairLoading: false,
      importRuleMap,
      toDelCatalogShow: false,
      delFile: {
        name: '',
        path: ''
      },
      delLoading: false
    }
  },
  components: {
    Pagination
  },
  created() {
    this.getExcelImportHistory()
    this.getCategories()
  },
  activated() {
    // console.log('我激活了')
    let This = this
    This.timer = setInterval(function () {
      This.getExcelImportHistory()
    }, 1000 * 10)
    This.activatedState = true
  },
  deactivated() {
    // console.log('我退出了')
    let This = this
    if (This.timer) {
      clearInterval(This.timer)
    }
    This.activatedState = false
  },
  beforeDestroy() {
    // console.log('我销毁了')
    let This = this
    if (This.timer) {
      clearInterval(This.timer)
    }
    This.activatedState = false
  },
  computed: {
    ...mapState(['menu']),
    hasDelAuth() {
      return findButton(
        this.menu,
        '/dataManage/import',
        '删除属性规则'
      )
    },
    hasImportAuth() {
      return findButton(
        this.menu,
        '/dataManage/import',
        '导入属性规则'
      )
    }
  },
  mounted() {
    let menu = this.menu
    this.exportCRMDataButton = findButton(
      menu,
      '/dataManage/import',
      '导入CRM数据'
    )
    let This = this
    document.addEventListener('visibilitychange', function () {
      let string = document.visibilityState
      if (string === 'hidden') {
        // 当页面由前端运行在后端时，出发此代码
        // console.log('我被隐藏了')
        if (This.timer) {
          clearInterval(This.timer)
        }
      }
      if (string === 'visible') {
        // 当页面由隐藏至显示时
        // console.log('欢迎回来')
        if (This.activatedState) {
          // console.log('定时器开启=' + This.activatedState)
          This.timer = setInterval(function () {
            This.getExcelImportHistory()
          }, 1000 * 10)
        }
      }
    })
  },
  methods: {
    getCategories() {
      let data = {
        finalizeState: 0
      }
      getCategoriesTree(data).then((res) => {
        console.log(res)
        if (res.code === 0) {
          this.importCRMForm.categoryList = res.data
          this.importCRMForm.categoryListLevel1 = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    categorySelect(level) {
      // 树形目录变化时触发
      console.log('level=' + level)
      let This = this
      if (level === 1) {
        let plist = this.importCRMForm.categoryListLevel1
        let id = this.importCRMForm.categoryLevel1Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.importCRMForm.categoryListLevel2 = plist[i].catalogueTreeVOList
            This.importCRMForm.categoryListLevel3 = null
            This.importCRMForm.categoryListLevel4 = null
            This.importCRMForm.categoryLevel2Value = ''
            This.importCRMForm.categoryLevel3Value = ''
            This.importCRMForm.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 2) {
        let plist = this.importCRMForm.categoryListLevel2
        let id = this.importCRMForm.categoryLevel2Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.importCRMForm.categoryListLevel3 = plist[i].catalogueTreeVOList
            This.importCRMForm.categoryListLevel4 = null
            This.importCRMForm.categoryLevel3Value = ''
            This.importCRMForm.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 3) {
        let plist = this.importCRMForm.categoryListLevel3
        let id = this.importCRMForm.categoryLevel3Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.importCRMForm.categoryListLevel4 = plist[i].catalogueTreeVOList
            This.importCRMForm.categoryLevel4Value = ''
            return false
          }
        }
      }
    },
    goGetCRMcategory(level, pid) {
      let data = {
        level: level,
        pid: pid || ''
      }
      getCRMcategory(data).then((res) => {
        console.log(res)
        if (res.code === 0) {
          this.importCRMForm.CRMcategoryListLevel1 = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    categorySelectCRM(level) {
      // 树形目录变化时触发
      console.log('level=' + level)
      let This = this
      if (level === 1) {
        let plist = this.importCRMForm.CRMcategoryListLevel1
        let id = this.importCRMForm.CRMcategoryLevel1Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.importCRMForm.CRMcategoryListLevel2 =
              plist[i].catalogueTreeVOList
            This.importCRMForm.CRMcategoryListLevel3 = null
            This.importCRMForm.CRMcategoryListLevel4 = null
            This.importCRMForm.CRMcategoryLevel2Value = ''
            This.importCRMForm.CRMcategoryLevel3Value = ''
            This.importCRMForm.CRMcategoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 2) {
        let plist = this.importCRMForm.CRMcategoryListLevel2
        let id = this.importCRMForm.CRMcategoryLevel2Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.importCRMForm.CRMcategoryListLevel3 =
              plist[i].catalogueTreeVOList
            This.importCRMForm.CRMcategoryListLevel4 = null
            This.importCRMForm.CRMcategoryLevel3Value = ''
            This.importCRMForm.CRMcategoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 3) {
        let plist = this.importCRMForm.CRMcategoryListLevel3
        let id = this.importCRMForm.CRMcategoryLevel3Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.importCRMForm.CRMcategoryListLevel4 =
              plist[i].catalogueTreeVOList
            This.importCRMForm.CRMcategoryLevel4Value = ''
            return false
          }
        }
      }
    },
    sureImportCRMData() {
      // 导入crm数据
      let This = this
      let importCRMForm = this.importCRMForm
      if (
        !importCRMForm.CRMcategoryLevel4Value ||
        !importCRMForm.categoryLevel4Value
      ) {
        this.$message.error('请选择完整类目')
        return false
      }
      let sourceCategoryName = ''
      let targetCategoryName = ''
      importCRMForm.CRMcategoryListLevel4.forEach((item) => {
        if (item.id === importCRMForm.CRMcategoryLevel4Value) {
          sourceCategoryName = item.name
          return false
        }
      })
      importCRMForm.categoryListLevel4.forEach((item) => {
        if (item.id === importCRMForm.categoryLevel4Value) {
          targetCategoryName = item.name
          return false
        }
      })
      let data = {
        sourceCategoryId: importCRMForm.CRMcategoryLevel4Value,
        sourceCategoryName: sourceCategoryName,
        targetCategoryId: importCRMForm.categoryLevel4Value,
        targetCategoryName: targetCategoryName,
        uploadType: importCRMForm.importRuleType
      }
      importCRMData(data).then((res) => {
        console.log(res)
        if (res.code === 0) {
          This.showImportCRMDialog = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    openImportCRMDialog() {
      this.goGetCRMcategory(1)
      this.showImportCRMDialog = true
    },
    closeImportCRMDialog() {
      this.showImportCRMDialog = false
    },
    pageClick(page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.getExcelImportHistory()
    },
    beforeUpload(file) {
      console.log('beforeUpload')
      console.log(file)
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      this.fileName = file.name
      this.loading.import = true
    },
    onUploadSuccess(response) {
      console.log('success')
      console.log(response)
      this.loading.import = false
      if (response && response.data.status === 200) {
        this.$message.success(response.message || '导入成功！')
        this.filePath = response.data.link
      } else {
        this.$message.error((response && response.message) || '导入失败！')
      }
    },
    onUploadError(error) {
      console.log('error')
      console.log(error)
      this.loading.import = false
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      )
    },
    sureImport() {
      // 提交上传数据信息
      if (!this.filePath) {
        this.$message.error('请上传excel文件！')
        return false
      }
      let narr = {
        name: this.fileName,
        time: moment().format('YYYY-MM-DD HH:mm:ss'),
        count: '-',
        type: 'COVER',
        staff: this.createUser,
        remark: this.remarks,
        state: 'QUEUING'
      }
      let This = this
      // excel数据导入接口
      let param = {
        name: This.fileName, // 文件名
        path: This.filePath,
        remark: This.remarks,
        uploadName: This.createUser // 上传人
      }
      getExcelImport(param).then((res) => {
        console.log(res)
        if (res.code === 0) {
          res = res.data
          console.log('导入结果')
          console.log(res)
          // 数据提交完成之后，清空上一个数据
          This.templateList.unshift(narr)
          This.showImportDialog = false
          This.fileName = ''
          This.filePath = ''
          This.remarks = ''
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    openImportDialog() {
      this.showImportDialog = true
    },
    closeImportDialog() {
      this.showImportDialog = false
    },
    getExcelImportHistory() {
      let This = this
      let param = {
        page: This.listQueryInfo.current - 1,
        size: This.listQueryInfo.pageSize,
        sort: 'id,desc' // id的倒序 asc时正序
      }
      getExcelImportHistory(param).then((res) => {
        console.log(res)
        if (res.code === 0) {
          This.loading.table = false
          This.loading.page = false
          This.templateList = res.data.content
          This.total = res.data.totalElements
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    handleCancel() {
      this.toRepairVisible = false
    },
    handleSubmit() {
      // 提交上传数据信息
      if (!this.tobeRepairFilePath) {
        this.$message.error('请上传excel文件！')
        return false
      }
      this.tobeRepairLoading = true
      let narr = {
        name: this.tobeRepirFileName,
        time: moment().format('YYYY-MM-DD HH:mm:ss'),
        count: '-',
        type: 'COVER',
        staff: this.createUser,
        state: 'QUEUING'
      }
      let This = this
      // excel数据导入接口
      let param = {
        name: This.tobeRepirFileName, // 文件名
        path: This.tobeRepairFilePath,
        uploadName: This.createUser // 上传人
      }
      tobeRepairImport(param).then((res) => {
        if (res.code === 0) {
          res = res.data
          This.tobeRepairLoading = false
          // 数据提交完成之后，清空上一个数据
          This.templateList.unshift(narr)
          This.$message.success('数据导入成功！')
          This.toRepairVisible = false;
          This.tobeRepirFileName = ''
          This.tobeRepairFilePath = ''
          This.fileList = []
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    importToRepairData() {
      this.toRepairVisible = true
    },
    handleRadioChange(label) {
      this.radioVal = label
    },
    handleCheckFile(file) {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        this.$message.error('上传文件大小不能超过 10MB!')
        return false
      }
      if (!this.$validateFileType(file)) return false
      this.tobeRepirFileName = file.name
      this.fileList = [file]
      console.log(this.fileList, 'fileList')
    },
    handleSucceed(response) {
      if (response && response.data.status === 200) {
        this.$message.success(response.message || '数据导入成功！')
        this.tobeRepairFilePath = response.data.link
      } else {
        this.$message.error((response && response.message) || '数据导入失败！')
      }
    },
    downloadTemplate() {
      getUploadTemplate().then((res) => {
        if (res.code === 0 && res.data && res.data.path) {
          window.open(res.data.path)
        } else {
          this.$message.error(res.msg || '下载失败')
        }
      })
    },
    importToDeleteCatalog() {
      this.toDelCatalogShow = true
      this.handleRemove()
    },
    handleRemove() {
      this.delFile = {}
    },
    handleCheckDelFile(file) {
      if (file.size / 1024 / 1024 > 10) {
        this.$message.error('上传文件不能超过10MB!')
        return false
      }
      if (!this.$validateFileType(file)) return false
      if (this.$refs.elUpload.uploadFiles.length > 1) {
        this.$message({
          message: '目前只能上传1个文件',
          type: 'warning'
        })
        return false
      }
      this.delFile.name = file.name
      this.delLoading = true
      return true
    },
    handleDelFileSucceed(res) {
      const { code = -1, data = {} } = res
      if (code === 0) {
        this.delFile.path = data?.link || ''
      } else {
        console.log('上传附件异常', res?.message || '')
      }
      this.delLoading = false
    },
    async downloadDelTemplate() {
      const { data = {}, msg = '', success = false } = await getDelTemplate()
      if (!success) {
        this.$message.warning(msg || '下载删除类目模版失败')
      }
      data.path && window.open(data.path)
    },
    async handleSubmitDelFile() {
      const { name = '', path = '' } = this.delFile
      if (!name || !path) {
        this.$message.warning('请上传excel文件！')
        return
      }
      this.delLoading = true
      const { success = false, msg = '' } = await uploadDelTemplate({
        name: this.delFile.name,
        path: this.delFile.path,
        uploadName: window?.CUR_DATA?.user?.name || ''
      }).finally(() => {
        this.delLoading = false
      })
      if (!success) {
        this.$message.warning(msg || '上传删除类目附件失败')
        return
      }
      this.$message.success('操作成功')
      this.toDelCatalogShow = false
      this.getExcelImportHistory()
    }
  }
}
</script>
<style lang="scss" scoped>
.data-manage-import-container{
  min-height: 500px;
  .data-manage-import-center{
    border: 1px solid #eee;
    padding-top: 10px;
    .data-manage-import-add{
      padding: 0 20px 10px;
      overflow: hidden;
      .importtitle{
        float: left;
        font-size: 18px;
        line-height: 32px;
      }
      .importbtn{
        float: right;
      }
    }
    .ellipse2 {
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
      cursor: default;
      max-height:46px;
    }
  }
  .link-btn{
      color: #597bee;
      cursor: pointer;
      font-size: 12px;
    }
}
.dialogTitle{
  line-height: 32px;
}
.importset{
  .el-row{
    margin-bottom: 15px;
  }
}
.repair_upload{
  text-align: center;
}
.delete_upload{
  width: 100%;
  ::v-deep{
    .el-upload, .el-upload-dragger{
      width: 100%;
    }
    .el-upload__tip{
      color: #DCDfd6;
    }
    .el-dialog__header{
      border-bottom: 1px solid #d9d9d9;
    }
    .el-dialog__footer{
      text-align: center;
    }
  }
}

.repair_upload ::v-deep .el-dialog {
}
</style>
