<template>
  <span style="height: 22px; display: inline-block;">
    <i v-if="trendState > 0" class="trend-up el-icon-caret-top"></i>
    <i v-else-if="trendState < 0" class="trend-down el-icon-caret-bottom"></i>
    <i v-else>--</i>
  </span>
</template>

<script>
export default {
  name: 'triangle',
  props: {
    trendState: { // >0 升; <0 降; 0 平
      type: Number,
      default: 0
    }
  },
  data () {
    return {
    }
  },
  methods: {
  }
}
</script>
<style lang="scss" scoped>
  .trend-up {
    color: #D9001B;
    font-size: 20px;
    position: relative;
    top: 2px;
    margin-left: 2px;
  }
  .trend-down {
    color: #70B603;
    font-size: 20px;
    position: relative;
    top: 1px;
    margin-left: 2px;
  }
</style>
