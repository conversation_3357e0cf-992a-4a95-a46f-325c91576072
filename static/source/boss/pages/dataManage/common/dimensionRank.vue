<template>
  <div class="item" v-loading="loading.rank">
    <el-row :span="24">
      <el-col :span="11" :offset="1" style="font-size: 16px; line-height: 32px;">{{dimensionType === 'material' ? '物料组' : (dimensionType === 'rule' ? '规则' : '类目')}}排行</el-col>
      <el-col :span="11" style="text-align: right;">
        <el-radio-group size="small" v-model="rankMaterialValue" @change="rankMaterialTab">
          <el-radio-button label="number">数量</el-radio-button>
          <el-radio-button label="proportion">比例</el-radio-button>
        </el-radio-group>
      </el-col>
    </el-row>
    <el-divider></el-divider>
    <el-row :span="24" style="min-height: 330px;">
      <el-row :span="24" :gutter="10" v-for="(item, index) in currData" :item="item" :key="index" style="margin-bottom: 10px; font-size: 14px;">
        <el-col :span="2" :offset="2" ><span class="rank">{{index + 1}}</span></el-col>
        <el-col :span="8"><div class="ellipsis" :title="item.name">{{item.name}}</div></el-col>
        <el-col :span="8"><div style="padding-top: 2px;">{{item.num}}<span v-if="rankMaterialValue === 'proportion'">%</span></div></el-col>
        <el-col :span="2" style="text-align: center;"><Trignale :trendState="Number(item.trend)"></Trignale></el-col>
      </el-row>
      <el-row v-show="this.currData.length <= 0">
        <div class="zanno">暂无数据</div>
      </el-row>
    </el-row>
  </div>
</template>

<script>
import Trignale from './triangle.vue'
export default {
  name: 'dimensionRank',
  props: {
    dimensionType: {
      type: String,
      default: 'material'
    }, // 维度  material:物料；rule:规则；类目:category
    numberData: {
      type: Array,
      default () { // 数量数据
        return [
          {
            id: '1',
            name: '物料组1xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '2',
            name: '物料组2xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '3',
            name: '物料组3xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '4',
            name: '物料组4xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '5',
            name: '物料组5xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '6',
            name: '物料组5xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '7',
            name: '物料组5xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '8',
            name: '物料组5xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '9',
            name: '物料组5xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          },
          {
            id: '10',
            name: '物料组5xxx',
            num: '10',
            trend: 0 // >0 升; <0 降; 0 平
          }
        ]
      }
    },
    proportionData: {
      type: Array,
      default () { // 比例数据
        return [
          {
            id: '1',
            name: '物料组1xxx',
            value: 'XXXXXXXXX',
            state: '1' // 1 升 2 降 3 平
          },
          {
            id: '2',
            name: '物料组2xxx',
            value: 'XXXXXXXXX',
            state: '2' // 1 升 2 降 3 平
          },
          {
            id: '3',
            name: '物料组3xxx',
            value: 'XXXXXXXXX',
            state: '3' // 1 升 2 降 3 平
          },
          {
            id: '4',
            name: '物料组4xxx',
            value: 'XXXXXXXXX',
            state: '1' // 1 升 2 降 3 平
          },
          {
            id: '5',
            name: '物料组5xxx',
            value: 'XXXXXXXXX',
            state: '2' // 1 升 2 降 3 平
          },
          {
            id: '6',
            name: '物料组5xxx',
            value: 'XXXXXXXXX',
            state: '3' // 1 升 2 降 3 平
          }
        ]
      }
    }
  },
  data () {
    return {
      loading: {
        rank: false
      },
      rankMaterialValue: 'number' // number 数量 proportion 比列
    }
  },
  watch: {
    numberData: {
      handler (nval, oval) {
        console.log('数据变化了1')
        this.numberData = nval
        this.rankMaterialTab()
      },
      deep: true
    },
    proportionData: {
      handler (nval, oval) {
        console.log('数据变化了2')
        this.proportionData = nval
        this.rankMaterialTab()
      },
      deep: true
    }
  },
  created () {
    console.log('维度')
    console.log(this.dimensionType)
    this.currData = this.numberData
  },
  components: {
    Trignale
  },
  methods: {
    rankMaterialTab (e) {
      this.loading.rank = true
      console.log('排行切换=' + e)
      console.log(this.rankMaterialValue)
      if (this.rankMaterialValue === 'number') {
        // 数量数据
        this.currData = this.numberData
      } else {
        // 比列数据
        this.currData = this.proportionData
      }
      this.$forceUpdate()
      this.loading.rank = false
    }
  }
}
</script>
<style lang="scss" scoped>
  .item {
    border: 1px solid #ccc;
    padding:20px 10px 10px;
    .rank {
      display: inline-block;
      width: 18px;
      height: 18px;
      text-align: center;
      line-height: 18px;
      border-radius: 100%;
      font-size: 12px;
      border: 1px solid #999;
    }
    .zanno {
      text-align: center;
      font-size: 14px;
      color: #999;
      padding: 50px 0;
    }
    .ellipsis {
      width: 100%;
      overflow: hidden;  /*超出部分隐藏*/
      white-space: nowrap;  /*禁止换行*/
      text-overflow: ellipsis; /*省略号*/
      cursor: default;
    }
  }
</style>
