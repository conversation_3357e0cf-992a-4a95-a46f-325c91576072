/**
 * 确定某个button是否有权限看到
 * meau： 原始菜单库（返回的用户权限菜单）
 * 根据返回的用户权限菜单，通过button的父页面链接（parentLink）来查找其子集是否包含所需要确定button的惟一名字（buttonUniqueName），包含则显示，不包含则隐藏
 *
 * **/
export function findButton(meau, parentLink, buttonUniqueName) {
  let viewFinalMeau = null;
  let isHave = false;
  function meanEach(list) {
    list.forEach((item, index) => {
      if (!viewFinalMeau && item.link && item.link.indexOf(parentLink) > 0) {
        // console.log('父级菜单')
        // console.log(item)
        viewFinalMeau = item;
        if (item.children && item.children.length > 0) {
          let siblingsMeau = item.children;
          siblingsMeau.forEach((itemi, indexi) => {
            if (itemi.name === buttonUniqueName) {
              // 通过button惟一名字找到了button
              isHave = true;
            }
          });
        }
      } else {
        if (!viewFinalMeau && item.children && item.children.length > 0) {
          meanEach(item.children);
        }
      }
    });
  }
  meanEach(meau);
  return isHave;
}
export const nonEditableAttrMapping = [
  '品牌',
  '商品名称',
  '制造商型号',
  '销售单位',
  '箱规',
  '产品名称',
  '核心规格',
  '计价单位',
  '制造商订货号'
];

export const pageMappingListTitle = {
  propertyRepair: '属性问题列表',
  purchaseMaintenance: '采购价格问题列表',
  salesMaintenance: '销售价格问题列表'
};
export const commonShowControl = [
  'propertyRepair',
  'purchaseMaintenance',
  'salesMaintenance'
];

export const importRuleMap = {
  COVER: '覆盖冲突数据',
  INHERENT: '保留原有数据',
  COVER_NEW: '覆盖冲突数据新版',
  IMPORT_AFTER_EMPTYING: '清空原有数据后导入',
  PROPERTY_DEL: '删除类目属性'
};
