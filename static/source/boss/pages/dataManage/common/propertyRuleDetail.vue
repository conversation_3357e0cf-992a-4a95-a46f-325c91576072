<template>
  <div class="propertyRuleDetail">
    <div v-if="detailPageId === 'repair'">
      <!--可拖拽规则描述弹出框-->
      <div id="drag" v-drag v-if="showThDialog">
        <div class="el-dialog__header">
          <span class="el-dialog__title">属性规则详情</span>
          <button @click="closeThDialog" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button>
        </div>
        <div class="el-dialog__body">
          <div class="importset importset2">
            <div>
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">属性名称：</el-col>
                <el-col :span="8">{{currRuleSetDetail.name}}</el-col>
                <el-col :span="3" class="dialogTitle">开启检测：</el-col>
                <el-col :span="8">{{currRuleSetDetail.state ? '是' : '否'}}</el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">字段类型：</el-col>
                <el-col :span="8">{{(currRuleSetDetail.dataType == 'TEXT' ? '纯文本' : (isEnumType(currRuleSetDetail.dataType)  ? getEnumName(currRuleSetDetail.dataType) : (currRuleSetDetail.dataType == 'NUMBER' ? '数字' : '不校验')))}}</el-col>
                <el-col :span="3" class="dialogTitle">属性类型：</el-col>
                <el-col :span="8">{{(currRuleSetDetail.type == 'MAIN' ? '关键属性' : (currRuleSetDetail.type == 'SALE' ? '销售属性' : (currRuleSetDetail.type == 'NORMAL' ? '一般属性' : (currRuleSetDetail.type == 'BLANK' ? '空' : '-'))))}}</el-col>
              </el-row>
              <el-row :span="24">
                <el-col v-show="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType)" :span="3" class="dialogTitle">是否必填：</el-col>
                <el-col v-show="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType)" :span="8">{{currRuleSetDetail.required ? '是' : '否'}}</el-col>
                <el-col :span="3" class="dialogTitle">备&emsp;&emsp;注：</el-col>
                <el-col :span="8">
                  <div class="ellipse" :title="currRuleSetDetail.example">{{(currRuleSetDetail.example || '-')}}</div>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">是否选型：</el-col>
                <el-col :span="8">{{currRuleSetDetail.selection ? '是' : '否'}}</el-col>
                <el-col :span="3" class="dialogTitle">是否展示：</el-col>
                <el-col :span="8">{{currRuleSetDetail.show ? '是' : '否'}}</el-col>
              </el-row>
              <el-row :span="24" v-if="currRuleSetDetail.sourcing != null">
                <el-col :span="3" class="dialogTitle">是否sourcing选型：</el-col>
                <el-col :span="8">{{currRuleSetDetail.sourcing ? '是' : '否'}}</el-col>
              </el-row>
              <!--非数字-前置属性-->
              <div v-show="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType) && !((currRuleSetDetail.dataType === 'NO_LIMIT' || currRuleSetDetail.dataType === 'TEXT') && !currRuleSetDetail.required)">
                <el-row :span="24">
                  <el-col :span="3" class="dialogTitle">前置属性：</el-col>
                  <el-col :span="8">
                    <div class="ellipse">{{currRuleSetDetail.propertyId | getPropertyName(propertyList)}}</div>
                  </el-col>
                  <el-col :span="3" class="dialogTitle">判断条件：</el-col>
                  <el-col :span="8">{{(currRuleSetDetail.conditionType == 'COMPVALUE' ? '值大小' : (currRuleSetDetail.conditionType == 'ENUM' ? '枚举值' : (currRuleSetDetail.conditionType == 'CONTAINS' ? '包含' : (currRuleSetDetail.conditionType == 'EMPTY' ? '空值判断' : (currRuleSetDetail.conditionType == 'NEGATION' ? '非' : '-')))))}}</el-col>
                </el-row>
                <el-row :span="24">
                  <el-col :span="3" class="dialogTitle">条件设置：</el-col>
                  <el-col :span="19">{{currRuleSetDetail.value || '-'}}</el-col>
                </el-row>
              </div>
              <!--非数字-前置属性-->
              <!--枚举 PI27去掉是否多选配置-->
              <!-- <div v-if="isEnumType(currRuleSetDetail.dataType)">
                <el-row :span="24">
                  <el-col :span="3" class="dialogTitle">是否多选：</el-col>
                  <el-col :span="21">{{currRuleSetDetail.multipleChoiceFlag ? '是' : '否'}}</el-col>
                </el-row>
              </div> -->
              <!--枚举 end-->
              <!--非数字备注-->
              <el-row :span="24" v-if="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType)">
                <el-col :span="3" class="dialogTitle" style="line-height: 22px;">属性示例：</el-col>
                <el-col :span="16">
                  <div class="ellipse2" style="line-height: 22px;" :title="currRuleSetDetail.remark">{{currRuleSetDetail.remark || '-'}}</div>
                </el-col>
              </el-row>
              <!--非数字备注 end-->
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle" style="line-height: 22px;">填写说明：</el-col>
                <el-col :span="16">
                  <div class="ellipse2" style="line-height: 22px;" :title="currRuleSetDetail.writeDesc">{{currRuleSetDetail.writeDesc || ''}}</div>
                </el-col>
              </el-row>
            </div>
            <div>
              <!--枚举规则列表-->
              <el-row class="numbers_box" v-if="isEnumType(currRuleSetDetail.dataType) && currRuleSetDetail.multipleEnumInfoDTOList">
                <el-row class="numberRuleTab" style="margin-bottom: 0;">
                  <el-radio-group v-model="currEnumInfoRuleIndex" size="small" @change="tabEnumInfoRule_block">
                    <el-radio-button v-for="(item, index) in currRuleSetDetail.multipleEnumInfoDTOList" :item="item" :key="index" :label="index">规则{{index | numberFormat}}</el-radio-button>
                  </el-radio-group>
                </el-row>
                <el-row style="border:1px solid #eee; padding-top:10px; padding-bottom: 10px; line-height: 20px;">
                  <div style="width: 96%; margin:0 auto;">
                    <div style="border: 1px solid #eee; padding: 10px 10px 1px 10px;">
                      <!--数字-前置属性-->
                      <el-row :span="24">
                        <el-col :span="3">前置属性:</el-col>
                        <el-col :span="8">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].propertyId | getPropertyName(propertyList)}}</el-col>
                        <el-col :span="3">判断条件:</el-col>
                        <el-col :span="8">{{(currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'COMPVALUE' ? '值大小' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'ENUM' ? '枚举值' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'CONTAINS' ? '包含' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'EMPTY' ? '空值判断' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'NEGATION' ? '非' : '-')))))}}</el-col>
                      </el-row>
                      <el-row :span="24">
                        <el-col :span="3">条件设置:</el-col>
                        <el-col :span="20">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].value || '-'}}</el-col>
                      </el-row>
                      <!--数字-前置属性-->
                      <el-row :span="24" v-show="currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].requiredType !== 'BLANK'">
                        <el-col :span="3">属性示例:</el-col>
                        <el-col :span="20">
                          <div class="ellipse2" :title="currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].remark">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].remark || '-'}}</div>
                        </el-col>
                      </el-row>
                      <el-row :span="24">
                        <el-col :span="3">是否必填:</el-col>
                        <el-col :span="20">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].requiredType | formatRequiredType}}</el-col>
                      </el-row>
                      <el-row :span="24" v-show="currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].requiredType !== 'BLANK'">
                        <el-col :span="3">&nbsp;枚&nbsp;举&nbsp;值:</el-col>
                        <el-col :span="20">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].enumInfo && currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].enumInfo.replace(/,/g, '、')}}</el-col>
                      </el-row>
                    </div>
                  </div>
                </el-row>
              </el-row>
              <!--枚举规则列表 end-->
              <!--数字规则列表-->
              <el-row class="numbers_box" v-if="currRuleSetDetail.dataType == 'NUMBER' && currRuleSetDetail.multipleNumberRuleList[0]">
                <el-row class="numberRuleTab" style="margin-bottom: 0;">
                  <el-radio-group v-model="currNumberRuleIndex" size="small" @change="tabNumberRule_block">
                    <el-radio-button v-for="(item, index) in currRuleSetDetail.multipleNumberRuleList" :item="item" :key="index" :label="index">规则{{index | numberFormat}}</el-radio-button>
                  </el-radio-group>
                </el-row>
                <el-row style="border:1px solid #eee; padding-top:10px; padding-bottom: 10px; line-height: 20px;">
                  <div style="width: 96%; margin:0 auto;">
                    <div style="border: 1px solid #eee; padding: 10px 10px 1px 10px;">
                      <!--数字-前置属性-->
                      <el-row :span="24">
                        <el-col :span="3">前置属性:</el-col>
                        <el-col :span="8">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].propertyId | getPropertyName(propertyList)}}</el-col>
                        <el-col :span="3">判断条件:</el-col>
                        <el-col :span="8">{{(currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'COMPVALUE' ? '值大小' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'ENUM' ? '枚举值' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'CONTAINS' ? '包含' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'EMPTY' ? '空值判断' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'NEGATION' ? '非' : '-')))))}}</el-col>
                      </el-row>
                      <el-row :span="24">
                        <el-col :span="3">条件设置:</el-col>
                        <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].value || '-'}}</el-col>
                      </el-row>
                      <!--数字-前置属性-->
                      <el-row :span="24">
                        <el-col :span="3">属性示例:</el-col>
                        <el-col :span="20">
                          <div class="ellipse2" :title="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].remark">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].remark || '-'}}</div>
                        </el-col>
                      </el-row>
                      <el-row :span="24">
                        <el-col :span="3">是否必填:</el-col>
                        <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].requiredType | formatRequiredType}}</el-col>
                      </el-row>
                    </div>
                    <el-table v-show="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].requiredType !== 'BLANK'" :data="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].numberRuleList" border fit highlight-current-row style="margin:5px 0;">
                      <el-table-column label="数字类型" align="center" prop="type">
                        <template slot-scope="scope">
                          {{(scope.row.numberType)}}
                        </template>
                      </el-table-column>
                      <el-table-column label="数字精度" align="center" prop="accuracy">
                        <template slot-scope="scope">
                          {{(scope.row.numericalPrecision === '0' ? '整数' : (scope.row.numericalPrecision == 1 ? '一位小数' : (scope.row.numericalPrecision == 2 ? '二位小数' :  (scope.row.numericalPrecision == 3 ? '三位小数' : (scope.row.numericalPrecision == 4 ? '四位小数' : (scope.row.numericalPrecision == 5 ? '五位小数' : '不限制'))))))}}
                        </template>
                      </el-table-column>
                       <el-table-column label="数字合理范围" align="center" prop="numberRange">
                        <template slot-scope="scope">
                          {{scope.row.numberRange}}
                        </template>
                      </el-table-column>
                      <el-table-column label="前置单位" align="center" prop="prefixUnit">
                        <template slot-scope="scope">
                          {{scope.row.prefixUnit}}
                        </template>
                      </el-table-column>
                      <el-table-column label="后置单位" align="center" prop="suffixUnit">
                        <template slot-scope="scope">
                          {{scope.row.suffixUnit}}
                        </template>
                      </el-table-column>
                    </el-table>
                    <div style="border: 1px solid #eee; padding: 10px 10px 1px 10px; margin-bottom: 10px;">
                      <el-row :span="24" v-show="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].requiredType !== 'BLANK'">
                        <el-col :span="3">组连接符:</el-col>
                        <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].numberSeparator || '-'}}</el-col>
                      </el-row>
                      <el-row :span="24">
                        <el-col :span="3">级差开关:</el-col>
                        <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelOnFlag ? '开启' : '关闭'}}</el-col>
                      </el-row>
                      <el-row :span="24" v-if="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelOnFlag">
                        <el-col :span="3">级差配置:</el-col>
                        <el-col :span="20" v-if="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelConfigDTOS && currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelConfigDTOS.length > 0">
                          <el-table :data="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelConfigDTOS" border fit highlight-current-row style="margin:5px 0;">
                            <el-table-column label="低级单位" align="center" prop="lowUnit">
                              <template slot-scope="scope">
                                {{scope.row.lowUnit}}
                              </template>
                            </el-table-column>
                            <el-table-column label="高级单位" align="center" prop="highUnit">
                              <template slot-scope="scope">
                                {{scope.row.highUnit}}
                              </template>
                            </el-table-column>
                            <el-table-column label="级差" align="center" prop="stageEnum">
                              <template slot-scope="scope">
                                {{scope.row.stageEnum === 'THOUSAND' ? '千级' : (scope.row.stageEnum === 'HUANDRED' ? '百级' : (scope.row.stageEnum === 'TEN' ? '十级' : ''))}}
                              </template>
                            </el-table-column>
                          </el-table>
                        </el-col>
                      </el-row>
                    </div>
                  </div>
                </el-row>
              </el-row>
              <!--数字规则列表 end-->
              <el-row :span="24" style="text-align: right;">
                <el-col :span="24">
                  <el-button size="medium" @click="closeThDialog">关闭</el-button>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
      <!--可拖拽规则描述弹出框 end-->
    </div>
    <div v-else>
      <!--属性规则描述弹出框-->
      <el-dialog width="680px" title="属性规则详情" :visible.sync="showThDialog" v-if="currRuleSetDetail" :close-on-click-modal="false">
        <div class="importset importset2">
          <div>
            <el-row :span="24">
              <el-col :span="3" class="dialogTitle">属性名称：</el-col>
              <el-col :span="8">{{currRuleSetDetail.name}}</el-col>
              <el-col :span="3" class="dialogTitle">开启检测：</el-col>
              <el-col :span="8">{{currRuleSetDetail.state ? '是' : '否'}}</el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="3" class="dialogTitle">字段类型：</el-col>
              <el-col :span="8">{{(currRuleSetDetail.dataType == 'TEXT' ? '纯文本' : (isEnumType(currRuleSetDetail.dataType)  ? getEnumName(currRuleSetDetail.dataType) : (currRuleSetDetail.dataType == 'NUMBER' ? '数字' : '不校验')))}}</el-col>
              <el-col :span="3" class="dialogTitle">属性类型：</el-col>
              <el-col :span="8">{{(currRuleSetDetail.type == 'MAIN' ? '关键属性' : (currRuleSetDetail.type == 'SALE' ? '销售属性' : (currRuleSetDetail.type == 'NORMAL' ? '一般属性' : (currRuleSetDetail.type == 'BLANK' ? '空' : '-'))))}}</el-col>
            </el-row>
            <el-row :span="24">
              <el-col v-show="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType)" :span="3" class="dialogTitle">是否必填：</el-col>
              <el-col v-show="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType)" :span="8">{{currRuleSetDetail.required ? '是' : '否'}}</el-col>
              <el-col :span="3" class="dialogTitle">备&emsp;&emsp;注：</el-col>
              <el-col :span="8">
                <div class="ellipse" :title="currRuleSetDetail.example">{{(currRuleSetDetail.example || '-')}}</div>
              </el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="3" class="dialogTitle">是否选型：</el-col>
              <el-col :span="8">{{currRuleSetDetail.selection ? '是' : '否'}}</el-col>
              <el-col :span="3" class="dialogTitle">是否展示：</el-col>
              <el-col :span="8">{{currRuleSetDetail.show ? '是' : '否'}}</el-col>
            </el-row>
             <el-row :span="24">
                <el-col :span="6" class="dialogTitle">是否父标品聚合属性：</el-col>
                <el-col :span="5">{{currRuleSetDetail.ifFbp ? '是' : '否'}}</el-col>
                <el-col :span="6" class="dialogTitle">是否子标品定品属性：</el-col>
                <el-col :span="5">{{currRuleSetDetail.ifZbp ? '是' : '否'}}</el-col>
              </el-row>
            <el-row :span="24" v-if="currRuleSetDetail.sourcing != null">
              <el-col :span="5" class="dialogTitle">是否sourcing选型：</el-col>
              <el-col :span="8">{{currRuleSetDetail.sourcing ? '是' : '否'}}</el-col>
            </el-row>
            <!--非数字-前置属性-->
            <div v-show="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType) && !((currRuleSetDetail.dataType === 'NO_LIMIT' || currRuleSetDetail.dataType === 'TEXT') && !currRuleSetDetail.required)">
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">前置属性：</el-col>
                <el-col :span="8">
                  <div class="ellipse">{{currRuleSetDetail.propertyId | getPropertyName(propertyList)}}</div>
                </el-col>
                <el-col :span="3" class="dialogTitle">判断条件：</el-col>
                <el-col :span="8">{{(currRuleSetDetail.conditionType == 'COMPVALUE' ? '值大小' : (currRuleSetDetail.conditionType == 'ENUM' ? '枚举值' : (currRuleSetDetail.conditionType == 'CONTAINS' ? '包含' : (currRuleSetDetail.conditionType == 'EMPTY' ? '空值判断' : (currRuleSetDetail.conditionType == 'NEGATION' ? '非' : '-')))))}}</el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">条件设置：</el-col>
                <el-col :span="20">{{currRuleSetDetail.value || '-'}}</el-col>
              </el-row>
            </div>
            <!--非数字-前置属性-->
            <!--枚举 PI27去掉是否多选配置-->
            <!-- <div v-if="isEnumType(currRuleSetDetail.dataType)">
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">是否多选：</el-col>
                <el-col :span="21">{{currRuleSetDetail.multipleChoiceFlag ? '是' : '否'}}</el-col>
              </el-row>
            </div> -->
            <!--枚举 end-->
            <!--非数字备注-->
            <el-row :span="24" v-if="currRuleSetDetail.dataType !== 'NUMBER' && !isEnumType(currRuleSetDetail.dataType)">
              <el-col :span="3" class="dialogTitle" style="line-height: 22px;">属性示例：</el-col>
              <el-col :span="16">
                <div class="ellipse2" style="line-height: 22px;" :title="currRuleSetDetail.remark">{{currRuleSetDetail.remark}}</div>
              </el-col>
            </el-row>
            <!--非数字备注 end-->
            <el-row :span="24">
              <el-col :span="3" class="dialogTitle" style="line-height: 22px;">填写说明：</el-col>
              <el-col :span="16">
                <div class="ellipse2" style="line-height: 22px;" :title="currRuleSetDetail.writeDesc">{{currRuleSetDetail.writeDesc||''}}</div>
              </el-col>
            </el-row>
          </div>
          <div>
            <!--枚举规则列表-->
            <el-row class="numbers_box" v-if="isEnumType(currRuleSetDetail.dataType) && currRuleSetDetail.multipleEnumInfoDTOList">
              <el-row class="numberRuleTab" style="margin-bottom: 0;">
                <el-radio-group v-model="currEnumInfoRuleIndex" size="small" @change="tabEnumInfoRule_block">
                  <el-radio-button v-for="(item, index) in currRuleSetDetail.multipleEnumInfoDTOList" :item="item" :key="index" :label="index">规则{{index | numberFormat}}</el-radio-button>
                </el-radio-group>
              </el-row>
              <el-row style="border:1px solid #eee; padding-top:10px; padding-bottom: 10px; line-height: 20px;">
                <div style="width: 96%; margin:0 auto;">
                  <div style="border: 1px solid #eee; padding: 10px 10px 1px 10px;">
                    <!--数字-前置属性-->
                    <el-row :span="24">
                      <el-col :span="3">前置属性:</el-col>
                      <el-col :span="8">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].propertyId | getPropertyName(propertyList)}}</el-col>
                      <el-col :span="3">判断条件:</el-col>
                      <el-col :span="8">{{(currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'COMPVALUE' ? '值大小' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'ENUM' ? '枚举值' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'CONTAINS' ? '包含' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'EMPTY' ? '空值判断' : (currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].conditionType == 'NEGATION' ? '非' : '-')))))}}</el-col>
                    </el-row>
                    <el-row :span="24">
                      <el-col :span="3">条件设置:</el-col>
                      <el-col :span="20">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].value || '-'}}</el-col>
                    </el-row>
                    <!--数字-前置属性-->
                    <el-row :span="24" v-show="currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].requiredType !== 'BLANK'">
                      <el-col :span="3">属性示例:</el-col>
                      <el-col :span="20">
                        <div class="ellipse2" :title="currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].remark">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].remark || '-'}}</div>
                      </el-col>
                    </el-row>
                    <el-row :span="24">
                      <el-col :span="3">是否必填:</el-col>
                      <el-col :span="20">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].requiredType | formatRequiredType}}</el-col>
                    </el-row>
                    <el-row :span="24" v-show="currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].requiredType !== 'BLANK'">
                      <el-col :span="3">&nbsp;枚&nbsp;举&nbsp;值:</el-col>
                      <el-col :span="20">{{currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].enumInfo && currRuleSetDetail.multipleEnumInfoDTOList[currEnumInfoRuleIndex].enumInfo.replace(/,/g, '、')}}</el-col>
                    </el-row>
                  </div>
                </div>
              </el-row>
            </el-row>
            <!--枚举规则列表 end-->
            <!--数字规则列表-->
            <el-row class="numbers_box" v-if="currRuleSetDetail.dataType == 'NUMBER' && currRuleSetDetail.multipleNumberRuleList[0]">
              <el-row class="numberRuleTab" style="margin-bottom: 0;">
                <el-radio-group v-model="currNumberRuleIndex" size="small" @change="tabNumberRule_block">
                  <el-radio-button v-for="(item, index) in currRuleSetDetail.multipleNumberRuleList" :item="item" :key="index" :label="index">规则{{index | numberFormat}}</el-radio-button>
                </el-radio-group>
              </el-row>
              <el-row style="border:1px solid #eee; padding-top:10px; padding-bottom: 10px; line-height: 20px;">
                <div style="width: 96%; margin:0 auto;">
                  <div style="border: 1px solid #eee; padding: 10px 10px 1px 10px;">
                    <!--数字-前置属性-->
                    <el-row :span="24">
                      <el-col :span="3">前置属性:</el-col>
                      <el-col :span="8">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].propertyId | getPropertyName(propertyList)}}</el-col>
                      <el-col :span="3">判断条件:</el-col>
                      <el-col :span="8">{{(currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'COMPVALUE' ? '值大小' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'ENUM' ? '枚举值' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'CONTAINS' ? '包含' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'EMPTY' ? '空值判断' : (currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].conditionType == 'NEGATION' ? '非' : '-')))))}}</el-col>
                    </el-row>
                    <el-row :span="24">
                      <el-col :span="3">条件设置:</el-col>
                      <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].value || '-'}}</el-col>
                    </el-row>
                    <!--数字-前置属性-->
                    <el-row :span="24">
                      <el-col :span="3">属性示例:</el-col>
                      <el-col :span="20">
                        <div class="ellipse2" :title="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].remark">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].remark || '-'}}</div>
                      </el-col>
                    </el-row>
                    <el-row :span="24">
                      <el-col :span="3">是否必填:</el-col>
                      <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].requiredType | formatRequiredType}}</el-col>
                    </el-row>
                  </div>
                  <el-table v-show="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].requiredType !== 'BLANK'" :data="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].numberRuleList" border fit highlight-current-row style="margin:5px 0;">
                    <el-table-column label="数字类型" align="center" prop="type">
                      <template slot-scope="scope">
                        {{(scope.row.numberType)}}
                      </template>
                    </el-table-column>
                    <el-table-column label="数字精度" align="center" prop="accuracy">
                      <template slot-scope="scope">
                        {{(scope.row.numericalPrecision === '0' ? '整数' : (scope.row.numericalPrecision == 1 ? '一位小数' : (scope.row.numericalPrecision == 2 ? '二位小数' :  (scope.row.numericalPrecision == 3 ? '三位小数' : (scope.row.numericalPrecision == 4 ? '四位小数' : (scope.row.numericalPrecision == 5 ? '五位小数' : '不限制'))))))}}
                      </template>
                    </el-table-column>
                     <el-table-column label="数字合理范围" align="center" prop="numberRange">
                        <template slot-scope="scope">
                          {{scope.row.numberRange}}
                        </template>
                      </el-table-column>
                    <el-table-column label="前置单位" align="center" prop="prefixUnit">
                      <template slot-scope="scope">
                        {{scope.row.prefixUnit}}
                      </template>
                    </el-table-column>
                    <el-table-column label="后置单位" align="center" prop="suffixUnit">
                      <template slot-scope="scope">
                        {{scope.row.suffixUnit}}
                      </template>
                    </el-table-column>
                  </el-table>
                  <div style="border: 1px solid #eee; padding: 10px 10px 1px 10px; margin-bottom: 10px;">
                    <el-row :span="24" v-show="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].requiredType !== 'BLANK'">
                      <el-col :span="3">组连接符:</el-col>
                      <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].numberSeparator || '-'}}</el-col>
                    </el-row>
                    <el-row :span="24">
                      <el-col :span="3">级差开关:</el-col>
                      <el-col :span="20">{{currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelOnFlag ? '开启' : '关闭'}}</el-col>
                    </el-row>
                    <el-row :span="24" v-if="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelOnFlag">
                      <el-col :span="3">级差配置:</el-col>
                      <el-col :span="20" v-if="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelConfigDTOS && currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelConfigDTOS.length > 0">
                        <el-table :data="currRuleSetDetail.multipleNumberRuleList[currNumberRuleIndex].levelConfigDTOS" border fit highlight-current-row style="margin:5px 0;">
                          <el-table-column label="低级单位" align="center" prop="lowUnit">
                            <template slot-scope="scope">
                              {{scope.row.lowUnit}}
                            </template>
                          </el-table-column>
                          <el-table-column label="高级单位" align="center" prop="highUnit">
                            <template slot-scope="scope">
                              {{scope.row.highUnit}}
                            </template>
                          </el-table-column>
                          <el-table-column label="级差" align="center" prop="stageEnum">
                            <template slot-scope="scope">
                              {{scope.row.stageEnum === 'THOUSAND' ? '千级' : (scope.row.stageEnum === 'HUANDRED' ? '百级' : (scope.row.stageEnum === 'TEN' ? '十级' : ''))}}
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </el-row>
            </el-row>
            <!--数字规则列表 end-->
            <el-row :span="24" style="text-align: right;">
              <el-col :span="24">
                <el-button size="medium" @click="closeThDialog">关闭</el-button>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-dialog>
      <!--属性规则描述弹出框 end-->
    </div>
  </div>
</template>

<script>
/***
 * 属性规则详情弹出框，使用页面有：属性规则页面点击详情、属性问题页面点击表头
 * **/
import {
  getPropertyRule
} from '@/api/dataManage'
export default {
  name: 'propertyRuleDetail',
  props: {
    detailPageId: {
      type: String,
      default: ''
    },
    propertyList: { // '此类目下面的所有属性列表，方便"前置属性"回显时候使用'
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      currNumberRuleIndex: 0, // 当前选择的数字规则段的index
      currEnumInfoRuleIndex: 0, // 当前选择的枚举规则段的index
      currRuleSetDetail: null,
      showThDialog: null
    }
  },
  filters: {
    numberFormat(value) {
      let num = ''
      switch (value) {
        case 0:
          num = '一'
          break;
        case 1:
          num = '二'
          break;
        case 2:
          num = '三'
          break;
        case 3:
          num = '四'
          break;
        case 4:
          num = '五'
          break;
        case 5:
          num = '六'
          break;
        case 6:
          num = '七'
          break;
        case 7:
          num = '八'
          break;
        case 8:
          num = '九'
          break;
        case 9:
          num = '十'
          break;
        default:
          num = value + 1;
      }
      return num
    },
    getPropertyName(propertyId, propertyList) {
      let propertyName = '-'
      propertyList.forEach((item, index) => {
        if (item.id === propertyId) {
          propertyName = item.name
          return false
        }
      })
      return propertyName
    },
    formatRequiredType(type = '') {
      const fetch = {
        REQUIRED: '是',
        NOVALIDATE: '否'
      }
      return fetch[type] || ''
    }
  },
  created() {
  },
  computed: {
    isEnumType() {
      return function (type) {
        // ENUM_TYPE旧枚举类型，兼容旧数据
        return ['SINGLE', 'MULTI', 'ENUM_TYPE'].includes(type)
      }
    },
    getEnumName() {
      return function (type) {
        const typeFetch = {
          SINGLE: '单选枚举值',
          MULTI: '多选枚举值',
          ENUM_TYPE: '单选枚举值'
        }
        return typeFetch[type] || ''
      }
    }
  },
  // 注册局部组件指令
  directives: {
    drag: function (el) {
      let dragBox = el; // 获取当前元素
      dragBox.onmousedown = e => {
        // 算出鼠标相对元素的位置
        let disX = e.clientX - dragBox.offsetLeft;
        let disY = e.clientY - dragBox.offsetTop;
        document.onmousemove = e => {
          // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
          let left = e.clientX - disX;
          let top = e.clientY - disY;
          // 移动当前元素
          dragBox.style.left = left + 'px';
          dragBox.style.top = top + 'px';
        };
        document.onmouseup = e => {
          // 鼠标弹起来的时候不再移动
          document.onmousemove = null;
          // 预防鼠标弹起来后还会循环（即预防鼠标放上去的时候还会移动）
          document.onmouseup = null;
        };
      };
    }
  },
  methods: {
    closeThDialog() {
      this.showThDialog = false
    },
    tabNumberRule_block(index) {
      // 【切换显示-段】
      this.currNumberRuleIndex = index
    },
    tabEnumInfoRule_block(index) {
      // 【切换显示-段】
      this.currEnumInfoRuleIndex = index
    },
    getPropertyRuleDetail(propertyId) {
      let This = this
      getPropertyRule(propertyId).then(res => {
        if (res.code === 0) {
          This.currRuleSetDetail = res.data
          This.showThDialog = true
          This.currNumberRuleIndex = 0
          this.currEnumInfoRuleIndex = 0
        } else {
          This.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  .ellipse2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    cursor: default;
    max-height:46px;
  }
</style>
<style lang="scss">
.propertyRuleDetail {
  .el-dialog__body {
    padding:10px 40px;
  }
  .ellipse {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: default;
    max-height:46px;
  }
}
.importset2{
  .el-row{
    margin-bottom: 10px;
    line-height: 24px;
  }
  .dialogTitle {
    line-height: 24px;
  }
}
.data-manage-rule-container {
  .el-progress__text {
    margin-left: 4px;
  }
}
#drag {
  width: 680px;
  position: fixed;
  left: calc(50% - 340px);
  top: calc(50% - 300px);
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  z-index: 2000;
  cursor: move;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Chrome/Safari/Opera */
  -khtml-user-select: none; /* Konqueror */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently not supported by any browser */
}
</style>
