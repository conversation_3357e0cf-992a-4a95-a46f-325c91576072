<template>
  <div class="page data-manage-rule-container" v-loading="loading.page">
    <div style="display: flex" v-show="!loading.page">
      <!--左边树形结构-->
      <div class="rule-left" :class="{ fold: !isTreeShow }">
        <div class="switch_tab" @click="tabTree()" :class="{
            'el-icon-s-fold': isTreeShow,
            'el-icon-s-unfold': !isTreeShow
          }"></div>
        <div class="treewrap">
          <div class="tree">
            <Tree ref="tree" @treeClick="treeClick" @treeSync="treeSync" @treeList="treeList" @treeLoading="treeLoading" :treeKind="pageId"></Tree>
          </div>
        </div>
      </div>
      <!--左边树形结构 end-->
      <!--右边添加属性规则-->
      <div class="rule-right">
        <div class="noCategories" style="text-align: center; font-size: 20px; padding: 200px 0" v-if="!currCategory">
          请选择类目
        </div>
        <div class="data-manage-rule-center" v-else>
          <div class="data-manage-search">
            <div class="search-meau">
              目录：<span class="color-grey-300" v-if="currCategory" style="margin-right: 50px">{{ currCategory.fulePath | categoriesFormat }}</span>
            </div>
            <div v-if="currCategory.level === 4" class="search-box" style="padding-top: 10px">
              <el-row :span="24" style="margin-bottom: 10px">
                <el-col :span="2" class="dialogTitle">品牌:</el-col>
                <el-col :span="4">
                  <el-input placeholder="" v-model.trim="search.brand" />
                </el-col>
                <el-col :span="2" class="dialogTitle">产品名称:</el-col>
                <el-col :span="4">
                  <el-input placeholder="" v-model.trim="search.productName" />
                </el-col>
                <el-col :span="2" class="dialogTitle">修改负责人:</el-col>
                <el-col :span="4">
                  <el-input v-model.trim="search.division" />
                </el-col>
                <el-col :span="2" class="dialogTitle">制造商型号:</el-col>
                <el-col :span="4">
                  <el-input v-model.trim="search.manufacturerMaterialNo" />
                </el-col>
              </el-row>
              <el-row :span="24" style="margin-bottom: 10px">
                <el-col :span="2" class="dialogTitle">商品来源:</el-col>
                <el-col :span="4">
                  <el-select clearable v-model="search.skuSourceName" placeholder="请选择" style="width: 100%">
                    <el-option v-for="item in sourceOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="2" class="dialogTitle">是否备注:</el-col>
                <el-col :span="4">
                  <el-select clearable v-model="search.ifRemark" placeholder="请选择" style="width: 100%">
                    <el-option v-for="item in remarksOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="2" class="dialogTitle">skuID:</el-col>
                <el-col :span="4">
                  <el-input v-model="search.skuid" />
                </el-col>
                <el-col :span="2" class="dialogTitle">物料组:</el-col>
                <el-col :span="4">
                  <el-select clearable filterable remote style="width: 100%" :loading="loading.materialGroup" :remote-method="remoteMaterialGroup" v-model="search.productGroupId">
                    <el-option v-for="item in materialGroupOptions" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="2" class="dialogTitle">产品定位:</el-col>
                <el-col :span="4">
                  <el-select style="width: 100%" filterable clearable v-model="search.productPositioning">
                    <el-option v-for="item in productPositioningOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="4" style="text-align: right">
                  <el-button @click="startSearch()" type="primary">查询</el-button>
                  <el-button @click="resetSearch()">重置</el-button>
                </el-col>
              </el-row>
            </div>
          </div>
          <div v-if="currCategory.level === 4" class="data-manage-rule-add">
            <div class="ruletitle" style="padding-top: 20px">
              {{ pageMappingListTitle[pageId]
              }}<span style="color: red">{{
                ischecking ? '（校验中）' : ''
              }}</span>
            </div>
            <div class="repairTab" style="overflow: hidden; border-bottom: 1px solid #eee">
              <div style="float: left">
                <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" active-text-color="#597bee" @select="handleSelect" style="border: none">
                  <el-menu-item index="">待修复</el-menu-item>
                  <el-menu-item index="6">类目待调整</el-menu-item>
                  <el-menu-item index="5">已修复待同步</el-menu-item>
                  <el-menu-item index="4">已同步</el-menu-item>
                  <!--<el-menu-item index="">所有问题</el-menu-item>-->
                  <!--<el-menu-item v-if="pageId == 'propertyRepair'" index="0">未修复</el-menu-item>-->
                  <!--<el-menu-item v-if="pageId == 'propertyRepair'" index="1">已修复</el-menu-item>-->
                  <!--<el-menu-item index="2">已备注</el-menu-item>-->
                  <!--<el-menu-item index="3">所有数据</el-menu-item>-->
                </el-menu>
              </div>
              <div class="ruleInfo">
                <!--待修复-->
                <div v-show="activeIndex === ''">
                  <!-- <el-button
                    type="primary"
                    :loading="!syncStatus"
                    v-if="dataSkuSyncButton"
                    @click="skuSync"
                    >{{ syncStatus ? '同步并校验' : '同步中...' }}</el-button
                  >
                  <el-button
                    v-if="pageId === 'propertyRepair'"
                    type="primary"
                    @click="openColumnEdit"
                    >批量编辑</el-button
                  >
                  <el-button type="primary" @click="openModifyCategory"
                    >调整类目</el-button
                  > -->
                  <el-button type="primary" @click="handleCatalogAdjust">类目待调整</el-button>
                  <el-button v-if="pageId === 'propertyRepair'" type="primary" @click="openModifyPerson">设置修改负责人</el-button>
                  <el-button type="primary" @click="checkList">规则校验</el-button>
                  <!-- <el-button type="primary" @click="deleteSkuFn"
                    >删除</el-button
                  > -->
                </div>
                <!--已修复待同步-->
                <div v-show="activeIndex === '5'">
                  <el-button type="primary" @click="chooseSkuSync">推送</el-button>
                  <!--<el-button type="primary" :loading="!syncStatus" v-if="dataSkuSyncButton" @click="skuSync">{{syncStatus ? 'sku同步' : '同步中...' }}</el-button>-->
                  <el-button type="primary" :loading="!reflowStatus" v-if="dataSkuSyncToProductCenterButton" @click="skuSyncToProductCenter">{{ reflowStatus ? '全部推送' : '推送中...' }}</el-button>
                  <el-button type="primary" @click="refresh()">刷新</el-button>
                </div>
                <!--已同步-->
                <div v-show="activeIndex === '4'">
                  <el-button type="primary" @click="refresh()">刷新</el-button>
                </div>
                <div v-show="activeIndex === '6'">
                  <el-button type="primary" @click="handleRepair">移动至待修复</el-button>
                </div>
                <!--<el-button type="primary" @click="deleteColumn">批量删除</el-button>-->
                <!--<el-button @click="checkList" type="primary">{{pageId === 'propertyRepair' ? '检测当前目录' : (pageId === 'uniqueRepair' ? '检测唯一性问题' : (pageId === 'simpleRepair' ? '检测一般属性问题' : ''))}}</el-button>-->
                <!--<el-button type="primary" v-if="dataFinalizeButton" @click="toFinalData">数据定稿</el-button>-->
              </div>
            </div>
          </div>
          <div v-if="currCategory.level === 4" class="rulelist">
            <!--检测中进度条-->
            <div class="progressWrap" v-show="ischecking">
              <div v-show="ischecking" style="width: 400px; margin: 0 auto">
                <!--当数据为空，且percentage<100的时候 才显示进度条-->
                <p class="tit">数据校验中...</p>
                <el-progress :stroke-width="16" :percentage="percentage" :format="percentFormat"></el-progress>
              </div>
            </div>
            <!--检测中进度条 end-->
            <el-table ref="multipleTable" v-loading="loading.table" :data="templateList" border fit highlight-current-row :max-height="screenHeight" :style="{ maxHeight: screenHeight }" style="max-width: 100%" :cell-class-name="setCellClassName" @selection-change="handleSelectionChange">
              <template slot="empty">
                <div class="progress">
                  <p class="tit">暂无数据</p>
                </div>
              </template>
              <el-table-column type="selection" width="40"> </el-table-column>
              <el-table-column min-width="120px" label="备注" align="center" prop="number" fixed>
                <template slot-scope="scope">
                  <div>{{ scope.row.errorRemark.value }}</div>
                </template>
                <template slot-scope="scope">
                  <div class="prodiv" :title="scope.row.errorRemark.value" v-show="
                      (scope.row.errorRemark &&
                        scope.row.errorRemark.inputState) == 1
                    " @dblclick="addRemark(scope.$index, scope.row)">
                    {{ scope.row.errorRemark.value }}
                  </div>
                  <textarea v-if="scope.row.errorRemark" type="text" v-model="scope.row.errorRemark.value" v-show="
                      (scope.row.errorRemark &&
                        scope.row.errorRemark.inputState) == 2
                    " v-focus="focusState" placeholder="请输入" class="tsinput" @blur="addRemarkBlur(scope.$index, scope.row)"></textarea>
                </template>
              </el-table-column>
              <el-table-column v-if="pageId != 'propertyRepair'" min-width="120px" label="sku" align="center" prop="number" fixed />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="100px" label="品牌" align="center" prop="brand" fixed />
              <el-table-column min-width="100px" label="产品名称" align="center" prop="productName" fixed />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="80px" label="制造商型号" align="center" prop="manufacturerMaterialNo" fixed />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="120px" label="核心规格" align="center" prop="coreSpecification" fixed />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="100px" label="箱规" align="center" prop="boxSpecification" fixed />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="72px" label="销售单位" align="center" prop="priceUnit" fixed />
              <el-table-column min-width="100px" label="商品来源" align="center" prop="skuSourceName" fixed />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="100px" label="sku" align="center" prop="number" fixed />
              <el-table-column v-if="['4', '5'].includes(activeIndex)" label="是否同步成功" width="100" align="center" prop="isSuccess">
                <template slot-scope="scope">
                  <div>
                    {{
                      scope.row.isSuccess === true
                        ? '是'
                        : scope.row.isSuccess === false
                        ? '否'
                        : ''
                    }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column v-if="['4', '5'].includes(activeIndex)" label="同步信息" width="200" align="center" prop="errorMessage">
                <template slot-scope="scope">
                  <div>{{ scope.row.errorMessage }}</div>
                </template>
              </el-table-column>
              <el-table-column v-if="['4', '5'].includes(activeIndex)" label="同步时间" width="90" align="center" prop="reflowDate" />
              <el-table-column min-width="120px" label="修改负责人" align="center" prop="division" />
              <el-table-column min-width="120px" label="修改人" align="center" prop="lastModifier" />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="120px" label="物料组" align="center" prop="productGroupName" />
              <el-table-column v-if="commonShowItem.includes(pageId)" min-width="120px" label="产品定位" align="center" prop="productPositioningName" />
              <!--灵活添加的属性表头-->
              <el-table-column min-width="120px" :label="item.name" v-for="(item, index) in tableThList" :item="item" :key="index + Math.random()" align="center" :prop="item.keyname" scoped-slot>
                <template slot="header">
                  <div @click="openThDialog(item)">
                    <p class="property">{{ item.name }}</p>
                    <p class="property" v-if="pageId != 'propertyRepair'">
                      （{{ item.typeName }}）
                    </p>
                  </div>
                </template>
                <template slot-scope="scope">
                  <!--没问题的td-->
                  <div v-if="
                      scope.row.propertyResultVOMap[item.name] &&
                      scope.row.propertyResultVOMap[item.name].checkStatus !=
                        'true' &&
                      scope.row.propertyResultVOMap[item.name].checkStatus !=
                        '1' &&
                      scope.row.propertyResultVOMap[item.name] != true
                    ">
                    <div class="prodiv prodiv1" :title="
                        scope.row.propertyResultVOMap[item.name] &&
                        scope.row.propertyResultVOMap[item.name].value
                      " v-show="
                        (scope.row.propertyResultVOMap[item.name] &&
                          scope.row.propertyResultVOMap[item.name]
                            .inputState) == 1
                      " @dblclick="
                        modifyTdValue(
                          scope.$index,
                          item.name,
                          index,
                          scope.row.propertyResultVOMap[item.name].state,
                          scope.row.propertyResultVOMap[item.name].checkStatus,
                          scope.row.propertyResultVOMap[item.name].message
                        )
                      ">
                      {{
                        scope.row.propertyResultVOMap[item.name] &&
                        scope.row.propertyResultVOMap[item.name].value
                      }}
                    </div>
                  </div>
                  <!--没有问题的td end-->
                  <!--有问题的td-->
                  <div v-else>
                    <div class="prodiv prodiv2" :title="
                        scope.row.propertyResultVOMap[item.name] &&
                        scope.row.propertyResultVOMap[item.name].value
                      " v-show="
                        (scope.row.propertyResultVOMap[item.name] &&
                          scope.row.propertyResultVOMap[item.name]
                            .inputState) == 1
                      " @click="
                        openTdDialog(
                          scope.$index,
                          item.name,
                          index,
                          scope.row.propertyResultVOMap[item.name].state,
                          scope.row.propertyResultVOMap[item.name].checkStatus,
                          scope.row.propertyResultVOMap[item.name].message
                        )
                      ">
                      {{
                        scope.row.propertyResultVOMap[item.name] &&
                        scope.row.propertyResultVOMap[item.name].value
                      }}
                    </div>
                  </div>
                  <!--有问题的td end-->
                  <textarea v-if="scope.row.propertyResultVOMap[item.name]" type="text" v-model="scope.row.propertyResultVOMap[item.name].value" v-show="
                      (scope.row.propertyResultVOMap[item.name] &&
                        scope.row.propertyResultVOMap[item.name].inputState) ==
                      2
                    " v-focus="focusState" placeholder="请输入" class="tsinput" @blur="
                      tableTdBlur(
                        scope.$index,
                        item.name,
                        index,
                        scope.row.propertyResultVOMap[item.name].checkStatus
                      )
                    "></textarea>
                </template>
              </el-table-column>
              <!--灵活添加的属性表头 end-->
              <el-table-column min-width="120px" label="未匹配" align="center" prop="misMatched">
                <template slot-scope="scope">
                  <div>
                    {{ scope.row.misMatched ? scope.row.misMatched : '' }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div v-if="currCategory.level !== 4" class="not-forth-category">
            <!-- <el-button type="primary" v-if="dataSkuSyncButton" @click="skuSync"
              >同步并校验</el-button
            > -->
            <el-button type="primary" @click="checkList">规则校验</el-button>
            <el-button type="primary" v-if="dataSkuSyncToProductCenterButton" @click="skuSyncToProductCenter">全部推送</el-button>
            <!-- 一二三级类目规则校验时无法验证校验状态 -->
            <!-- <div class="progressWrap" v-show="ischecking">
              <div style="width: 400px; margin: 0 auto">
                <p class="tit">数据校验中...</p>
                <el-progress
                  :stroke-width="16"
                  :percentage="percentage"
                  :format="percentFormat"
                ></el-progress>
              </div>
            </div> -->
          </div>
        </div>
        <pagination v-show="total > 0 && currCategory.level === 4" :total="total" align="right" :page.sync="listQueryInfo.current" :limit.sync="listQueryInfo.pageSize" :pageSizes="[20, 50, 100, 200, 500]" layout=" total, sizes, prev, pager, next, jumper" @pagination="pageClick" />
      </div>
      <!--右边添加属性规则 end-->
    </div>
    <!--属性规则描述弹出框-->
    <PropertyRuleDetail ref="PropertyRuleDetail" :detailPageId="detailPageId" :propertyList="tableThList"></PropertyRuleDetail>
    <!--属性规则描述弹出框 end-->
    <!--属性值错误原因弹出框-->
    <el-dialog width="380px" title="提示" :visible.sync="showTdDialog" :close-on-click-modal="false">
      <div class="propertyTs">
        {{ tdTsTxt }}
      </div>
    </el-dialog>
    <!--属性值错误原因弹出框 end-->
    <!--批量修改负责人-->
    <el-dialog width="450px" title="批量设置修改负责人" :visible.sync="modifyPersonState" :close-on-click-modal="false">
      <div>
        <el-row :span="24">
          <el-col :span="6" class="dialogTitle" style="line-height: 30px">修改负责人：</el-col>
          <el-col :span="17">
            <el-input v-model.trim="modifyPersonValue" />
          </el-col>
        </el-row>
        <el-row :span="24" style="padding-top: 20px">
          <el-col :span="24" class="dialogTitle">
            <el-button size="small" @click="closeAddperson">取消</el-button>
            <el-button size="small" @click="storeAddperson" type="primary">确定</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--批量修改负责人 end-->
    <!--批量修改类目-->
    <el-dialog width="500px" title="批量调整类目" :visible.sync="modifyCategoryState" :close-on-click-modal="false">
      <el-row :span="24">
        <el-col :span="24" style="font-size: 16px; padding-bottom: 20px">选择类目：</el-col>
      </el-row>
      <el-row :span="24" type="flex" justify="space-around" style="padding-bottom: 10px">
        <el-col :span="11">
          <el-select v-model="categoryLevel1Value" :disabled="!categoryListLevel1 || categoryListLevel1.length < 0" @change="categorySelect(1)" placeholder="请选择一级类目">
            <el-option v-for="item in categoryListLevel1" :key="item.name" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="11">
          <el-select v-model="categoryLevel2Value" :disabled="!categoryListLevel2 || categoryListLevel2.length < 0" @change="categorySelect(2)" placeholder="请选择二级类目">
            <el-option v-for="item in categoryListLevel2" :key="item.name" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row :span="24" type="flex" justify="space-around">
        <el-col :span="11">
          <el-select v-model="categoryLevel3Value" :disabled="!categoryListLevel3 || categoryListLevel3.length < 0" @change="categorySelect(3)" placeholder="请选择三级类目">
            <el-option v-for="item in categoryListLevel3" :key="item.name" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="11">
          <el-select v-model="categoryLevel4Value" :disabled="!categoryListLevel4 || categoryListLevel4.length < 0" @change="categorySelect(4)" placeholder="请选择四级类目">
            <el-option v-for="item in categoryListLevel4" :key="item.name" :label="item.name" :value="item.name">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row :span="24" style="padding-top: 20px">
        <el-col :span="24" class="dialogTitle">
          <el-button size="small" @click="closeModifyCategory">取消</el-button>
          <el-button size="small" @click="storeModifyCategory" type="primary">确定</el-button>
        </el-col>
      </el-row>
    </el-dialog>
    <!--批量修改类目 end-->
    <!--批量编辑某一列-->
    <el-dialog width="450px" title="批量编辑" :visible.sync="modifyColumnState" :close-on-click-modal="false">
      <div>
        <el-row :span="24" style="margin-bottom: 10px">
          <el-col :span="6" class="dialogTitle" style="line-height: 30px">选 择 列：</el-col>
          <el-col :span="17">
            <el-select style="width: 100%" v-model="modifyColumnName" placeholder="请选择列">
              <el-option v-for="item in tableEditList" :key="item.name" :label="item.name" :value="item.name">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="6" class="dialogTitle" style="line-height: 30px">编辑内容：</el-col>
          <el-col :span="17">
            <el-input v-model.trim="modifyColumnValue" />
          </el-col>
        </el-row>
        <el-row :span="24" style="padding-top: 20px">
          <el-col :span="24" class="dialogTitle">
            <el-button size="small" @click="closeColumnEdit">取消</el-button>
            <el-button size="small" @click="storeColumnEdit" type="primary">确定</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--批量编辑某一列 end-->
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getCategoriesProgress,
  getTableStructure,
  getProblemList,
  getUniqueList,
  getSimpleList,
  updatePropertiesValue,
  propertiesCheck,
  uniqueCheck,
  simpleCheck,
  adjustCategory,
  deleteSku,
  toFinalData,
  skuSync,
  skuSyncToProductCenter,
  getSkuSyncStatus,
  getAssignSkuToProductCenter,
  getAsyncAndValidate,
  getCCMaterialGroup,
  labelAdjustCategorySku,
  cancelAdjustCategorySku
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
import Tree from './tree'
import PropertyRuleDetail from './propertyRuleDetail'
import {
  findButton,
  nonEditableAttrMapping,
  pageMappingListTitle,
  commonShowControl
} from './untils/tools'
import { getProductPositioningAndTradeOptions } from '@/api/pricePolicyConfigList.js'
export default {
  name: 'directOrderList',
  props: {
    pageId: {
      type: String,
      default: 'propertyRepair'
    }
  },
  data() {
    return {
      loading: {
        table: true,
        page: true,
        materialGroup: false
      },
      search: {
        brand: '',
        division: '',
        productName: '',
        manufacturerMaterialNo: '',
        skuSourceName: '',
        skuid: '',
        ifRemark: '',
        productPositioning: '',
        productGroupId: ''
      },
      detailPageId: 'repair',
      modifyPersonState: false,
      modifyCategoryState: false,
      isTreeShow: true, // 折叠打开树状菜单
      modifyPersonValue: '',
      currRuleSetDetail: null, // 当前点击的属性的规则详情
      currNumberRuleIndex: 0, // 当前选择规则段的index
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      showThDialog: false,
      showTdDialog: false,
      clicknum: 0, // 点击次数  区别双击还是单击使用
      timer: null, // 次数定时器
      timerProgress: null, // 百分比定时器
      percentage: 0, // 虚拟进度百分比
      ischecking: false, // true检测中(不能重复提交检测，不能请求列表数据) false 检测完成
      truePercent: 0, // 实际进度百分比
      tdTsTxt: '当前属性限定为：纯数字；当前值为：字符串；',
      currCategory: null, // 当前选中类目信息
      ruleVersion: 'V5', // 规则版本
      activeIndex: '', // ''全部问题 0 未修复 1 已修复 2 已备注 3 所有数据
      templateList: [], // table数据
      multipleSelection: [],
      fixedNum: 14, // 固定属性长度
      tableThList: [],
      tableEditList: [],
      total: 0,
      categories: [],
      defaultProps: {
        children: 'catalogueTreeVOList',
        code: 'code',
        id: 'id',
        label: 'name',
        level: 'level'
      },
      focusState: false, // 是不是 聚焦
      tdTempValue: '', // 临时储存的上一个td值，以便于修改后与新值做对比，判断是否进行了修改
      categoryList: null, // 目录树列表数据
      categoryListLevel1: null, // 目录树第一级列表数据
      categoryListLevel2: null, // 目录树第二级列表数据
      categoryListLevel3: null, // 目录树第三级列表数据
      categoryListLevel4: null, // 目录树第四级列表数据
      categoryLevel1Value: null, // 目录树第一级选中结果
      categoryLevel2Value: null, // 目录树第二级选中结果
      categoryLevel3Value: null, // 目录树第三级选中结果
      categoryLevel4Value: null, // 目录树第四级选中结果
      screenHeight: '600',
      modifyColumnName: '',
      modifyColumnValue: '',
      modifyColumnState: false,
      selfClickProcess: false, // 是不是点击检测类目之后触发的请求列表，如果是，同时还要更新类目
      dataFinalizeButton: false, // 是否显示数据定稿button
      sourceOptions: [
        {
          label: 'ZKH',
          value: 1
        },
        {
          label: 'VPI佣金',
          value: 2
        },
        {
          label: 'VPI112(废弃)',
          value: 3
        },
        {
          label: '数据大赛',
          value: 4
        },
        {
          label: '企数采',
          value: 5
        },
        {
          label: 'VC差价',
          value: 6
        },
        {
          label: 'VC佣金',
          value: 7
        },
        {
          label: 'VPI差价',
          value: 8
        },
        {
          label: 'FA',
          value: 9
        },
        {
          label: 'FA虚拟',
          value: 10
        },
        {
          label: '建筑行业',
          value: 11
        },
        {
          label: 'ZKH佣金',
          value: 13
        }
      ],
      dataSkuSyncButton: false, // 是否显示sku同步button
      dataSkuSyncToProductCenterButton: false, // 是否显示sku回写商品中心button
      reflowStatus: true, // false 同步中 true 同步完成
      syncStatus: true, // false 同步中 true 同步完成
      syncTimer: null, // 数据同步的定时去
      remarksOptions: [
        {
          value: '',
          label: '全部'
        },
        {
          value: true,
          label: '已备注'
        },
        {
          value: false,
          label: '未备注'
        }
      ],
      productPositioningOptions: [], // 产品定位options
      materialGroupOptions: [], // 物料组
      pageMappingListTitle: pageMappingListTitle,
      commonShowItem: commonShowControl
    }
  },
  components: {
    Pagination,
    Tree,
    PropertyRuleDetail
  },
  filters: {
    categoriesFormat: function (value) {
      return value.split('/').join(' / ')
    },
    numberFormat(value) {
      let num = ''
      switch (value) {
        case 0:
          num = '一'
          break
        case 1:
          num = '二'
          break
        case 2:
          num = '三'
          break
        case 3:
          num = '四'
          break
        case 4:
          num = '五'
          break
        case 5:
          num = '六'
          break
        case 6:
          num = '七'
          break
        case 7:
          num = '八'
          break
        case 8:
          num = '九'
          break
        case 9:
          num = '十'
          break
        default:
          num = value + 1
      }
      return num
    }
  },
  created() {
    this.getProductPositionOptions()
  },
  beforeUpdate() {
    // 关键--解决表格数据修改导致表头闪动问题
    this.$nextTick(() => {
      // 在数据加载完，重新渲染表格
      if (this.$refs['multipleTable']) {
        this.$refs['multipleTable'].doLayout()
      }
    })
  },
  directives: {
    focus: {
      update: function (el, { value }) {
        if (value) {
          el.focus()
        }
      }
    }
  },
  computed: {
    ...mapState(['menu'])
  },
  mounted() {
    // 设置表格高度为屏幕高度，使其能完全显示在屏幕内
    this.screenHeight = window.screen.height - 300
    let menu = this.menu
    let This = this
    if (This.pageId === 'propertyRepair') {
      // 属性问题维护
      This.dataFinalizeButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        '数据定稿'
      )
      This.dataSkuSyncButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        'sku同步'
      )
      This.dataSkuSyncToProductCenterButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        'sku回写商品中心'
      )
    } else if (This.pageId === 'uniqueRepair') {
      // 唯一性问题维护
      This.dataFinalizeButton = findButton(
        menu,
        '/dataManage/uniqueRepair',
        '数据定稿'
      )
      This.dataSkuSyncButton = findButton(
        menu,
        '/dataManage/uniqueRepair',
        'sku同步'
      )
      This.dataSkuSyncToProductCenterButton = findButton(
        menu,
        '/dataManage/uniqueRepair',
        'sku回写商品中心'
      )
    } else if (This.pageId === 'simpleRepair') {
      // 一般属性问题维护
      This.dataFinalizeButton = findButton(
        menu,
        '/dataManage/simpleRepair',
        '数据定稿'
      )
      This.dataSkuSyncButton = findButton(
        menu,
        '/dataManage/simpleRepair',
        'sku同步'
      )
      This.dataSkuSyncToProductCenterButton = findButton(
        menu,
        '/dataManage/simpleRepair',
        'sku回写商品中心'
      )
    } else if (This.pageId === 'purchaseMaintenance') {
      // 采购价格问题维护
      This.dataFinalizeButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        '数据定稿'
      )
      This.dataSkuSyncButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        'sku同步'
      )
      This.dataSkuSyncToProductCenterButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        'sku回写商品中心'
      )
    } else if (This.pageId === 'salesMaintenance') {
      // 销售价格维护
      This.dataFinalizeButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        '数据定稿'
      )
      This.dataSkuSyncButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        'sku同步'
      )
      This.dataSkuSyncToProductCenterButton = findButton(
        menu,
        '/dataManage/propertyRepair',
        'sku回写商品中心'
      )
    }
  },
  beforeDestory() {
    let This = this
    if (This.timerProgress) {
      clearInterval(This.timerProgress)
    }
  },
  methods: {
    tabTree() {
      this.isTreeShow = !this.isTreeShow
    },
    resetSearch() {
      this.search = {
        brand: '',
        division: '',
        productName: '',
        manufacturerMaterialNo: '',
        skuSourceName: '',
        skuid: '',
        ifRemark: ''
      }
      this.getCategoriesProgress()
    },
    startSearch() {
      this.getCategoriesProgress()
    },
    openColumnEdit() {
      let selectList = this.multipleSelection
      if (selectList.length > 0) {
        this.modifyColumnName = ''
        this.modifyColumnValue = ''
        this.modifyColumnState = true
      } else {
        this.$message.error('请先选择要修改的数据')
      }
    },
    closeColumnEdit() {
      this.modifyColumnState = false
    },
    storeColumnEdit() {
      // 批量修改列 和 备注
      let This = this
      let modifyColumnName = This.modifyColumnName
      if (!modifyColumnName) {
        This.$message.error('请选择列')
        return false
      }
      let modifyColumnValue = This.modifyColumnValue
      This.modifyColumnState = false
      for (let i = 0; i < This.multipleSelection.length; i++) {
        This.updateProperties(
          This.multipleSelection[i].id,
          modifyColumnName,
          modifyColumnValue
        )
        for (let j = 0; j < This.templateList.length; j++) {
          if (This.multipleSelection[i].id === This.templateList[j].id) {
            console.log('index=' + j)
            if (modifyColumnName === '备注') {
              This.$set(
                This.templateList[j].errorRemark,
                'value',
                modifyColumnValue
              )
            } else {
              This.$set(
                This.templateList[j].propertyResultVOMap[modifyColumnName],
                'value',
                modifyColumnValue
              )
              This.$set(
                this.templateList[j].propertyResultVOMap[modifyColumnName],
                'state',
                true
              )
            }
            break
          }
        }
      }
    },
    deleteColumn() {
      let This = this
      let selectList = this.multipleSelection
      let len = selectList.length
      if (selectList.length > 0) {
        This.$confirm('确认要删除这' + len + '条数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let skuid = []
            for (let i = 0; i < selectList.length; i++) {
              skuid.push(selectList[i].id)
              for (let j = 0; j < This.templateList.length; j++) {
                if (selectList[i].id === This.templateList[j].id) {
                  This.templateList.splice(j, 1)
                  break
                }
              }
            }
            let data = {
              skuIdStr: skuid.join(',')
            }
            deleteSku(data).then((res) => {
              if (res.code === 0) {
                This.$message.success('删除成功')
              } else {
                This.$message.error(res.msg)
              }
            })
          })
          .catch(() => {
            console.log('取消删除')
          })
      } else {
        this.$message.error('请先选择要删除的数据')
      }
    },
    openModifyPerson() {
      let selectList = this.multipleSelection
      if (selectList.length > 0) {
        this.modifyPersonValue = ''
        this.modifyPersonState = true
      } else {
        this.$message.error('请先选择要修改的数据')
      }
    },
    closeAddperson() {
      this.modifyPersonState = false
    },
    storeAddperson() {
      let This = this
      This.modifyPersonState = false
      for (let i = 0; i < This.multipleSelection.length; i++) {
        This.updateProperties(
          This.multipleSelection[i].id,
          '修改负责人',
          This.modifyPersonValue
        )
        for (let j = 0; j < This.templateList.length; j++) {
          if (This.multipleSelection[i].id === This.templateList[j].id) {
            console.log('index=' + j)
            This.$set(This.templateList[j], 'division', This.modifyPersonValue)
            break
          }
        }
      }
    },
    openModifyCategory() {
      // 【显示】调整类目弹窗
      let selectList = this.multipleSelection
      if (selectList.length > 0) {
        this.modifyCategoryState = true
      } else {
        this.$message.error('请先选择要调整的数据')
      }
    },
    closeModifyCategory() {
      // 【关闭】调整类目弹窗
      this.modifyCategoryState = false
    },
    storeModifyCategory() {
      // 【保存】调整类目弹窗
      let This = this
      let plist = this.categoryListLevel4
      let name = this.categoryLevel4Value
      if (!name) {
        This.$message.error('请选择四级类目')
        return false
      }
      for (let i = 0; i < plist.length; i++) {
        if (plist[i].name === name) {
          let selectList = this.multipleSelection // 选择的sku列表
          console.log('选中数据')
          console.log(selectList)
          let categoryId = plist[i].id // 选中的四级类目的categoryId
          if (categoryId !== This.currCategory.id) {
            let skuIdList = selectList.map((item, _index) => {
              return item.id
            })
            this.modifyCategoryState = false
            // 要调整到的类目和当前所在类目不是同一个类目时才触发调整
            let data = {
              categoryId: categoryId,
              skuIdList: skuIdList
            }
            adjustCategory(data).then((res) => {
              console.log(res)
              if (res.code === 0) {
                This.$message.success('调整完成')
                selectList.forEach((item0) => {
                  let index = This.templateList.findIndex((item1) => {
                    return item1.id === item0.id
                  })
                  console.log('index=' + index)
                  This.templateList.splice(index, 1)
                })
              } else {
                This.$message.error(res.msg)
              }
            })
          } else {
            This.$message.error('所选类目和当前类目相同')
          }
        }
      }
    },
    tabNumberRule_block(index) {
      // 【切换显示-段】
      this.currNumberRuleIndex = index
    },
    percentFormat(percentage) {
      return `${parseInt(percentage)}%`
    },
    pageClick(page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.getCategoriesProgress()
    },
    treeSync(data) {
      let This = this
      This.$confirm('确认同步【商品中心】数据至【数据治理平台】吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let categoryId = data.id
        getAsyncAndValidate(categoryId).then((res) => {
          console.log(res)
          if (res.code === 0) {
            This.$message.success('开始同步【商品中心】数据至【数据治理平台】')
          } else {
            This.$message.error(res.msg)
          }
        })
      })
    },
    treeClick(data) {
      console.log('tree传递过来的')
      console.log(data)
      this.selfClickProcess = false
      this.currCategory = data
      this.templateList = []
      this.percentage = 0
      this.ischecking = false
      if (this.timerProgress) {
        clearInterval(this.timerProgress)
      }
      if (data.level === 4) {
        this.getTableStructure()
        this.getCategoriesProgress()
        this.goGetSkuSyncStatus()
      }
    },
    treeList(data) {
      console.log('tree传递过来的category数据列表')
      console.log(data)
      this.categoryList = data
      this.categoryListLevel1 = data
      console.log(this.categoryListLevel1)
    },
    treeLoading() {
      this.loading.page = false
    },
    categorySelect(level) {
      // 树形目录变化时触发
      console.log('level=' + level)
      let This = this
      if (level === 1) {
        let plist = this.categoryListLevel1
        let name = this.categoryLevel1Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].name === name) {
            This.categoryListLevel2 = plist[i].catalogueTreeVOList
            This.categoryListLevel3 = null
            This.categoryListLevel4 = null
            This.categoryLevel2Value = ''
            This.categoryLevel3Value = ''
            This.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 2) {
        let plist = this.categoryListLevel2
        let name = this.categoryLevel2Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].name === name) {
            This.categoryListLevel3 = plist[i].catalogueTreeVOList
            This.categoryListLevel4 = null
            This.categoryLevel3Value = ''
            This.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 3) {
        let plist = this.categoryListLevel3
        let name = this.categoryLevel3Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].name === name) {
            This.categoryListLevel4 = plist[i].catalogueTreeVOList
            This.categoryLevel4Value = ''
            return false
          }
        }
      }
    },
    getCategoriesProgress() {
      let This = this
      if (This.ischecking === false) {
        // 校验中进行的查询是无痕查询不loading
        This.loading.table = true
      }
      let categoryId = this.currCategory.id
      let resultType = ''
      switch (This.pageId) {
        case 'propertyRepair':
          // 属性修复检测
          resultType = 'FIELD'
          break
        case 'uniqueRepair':
          // 唯一性修复检测
          resultType = 'UNIQUE'
          break
        case 'simpleRepair':
          // 一般属性问题修复检测
          resultType = 'NORMAL'
          break
        case 'purchaseMaintenance':
          // 采购价格问题修复检测
          resultType = 'NORMAL'
          break
        case 'salesMaintenance':
          // 销售问题修复检测
          resultType = 'NORMAL'
          break
        default:
          break
      }

      getCategoriesProgress(categoryId, resultType).then((res) => {
        console.log(res)
        if (res.code === 0) {
          if (res.data.status) {
            // 检测完成进度100%
            This.percentage = 100
            This.truepercentage = 100
            let timetemp = setTimeout(function () {
              This.ischecking = false
              clearTimeout(timetemp)
            }, 800)
            // 请求数据列表
            This.getDataList()
            if (This.timerProgress) {
              clearInterval(This.timerProgress)
            }
          } else {
            if (This.ischecking) {
              // 检测中 计算完成进度
              This.truepercentage = (res.data.current / res.data.total).toFixed(
                2
              )
            } else {
              // 还没有置位检测状态， 则初始化去检测，置位检测状态（显示进度条）
              This.truepercentage = (res.data.current / res.data.total).toFixed(
                2
              )
              This.loading.table = false
              This.ischecking = true
              let timeSecond = 0
              if (This.timerProgress) {
                clearInterval(This.timerProgress)
              }
              This.timerProgress = setInterval(function () {
                let truePercent = This.truepercentage
                console.log('truePercent1=' + truePercent)
                if (truePercent <= 100) {
                  console.log('小于')
                  timeSecond = timeSecond + 0.5
                  if (timeSecond >= 5) {
                    // 每10秒去请求一次实际进度
                    timeSecond = 0
                    This.getCategoriesProgress()
                  }
                  console.log('percentage4=' + This.percentage)
                  if (This.percentage < 95) {
                    if (truePercent > This.percentage) {
                      // 当实际进度 > 当前虚拟进度时，虚拟进度直接置位 实际进度
                      This.percentage = truePercent
                    } else {
                      // 当实际进度 > 当前虚拟进度时，虚拟进度直接置位 实际进度
                      This.percentage = Number(This.percentage) + 0.5
                    }
                    console.log('percentage2=' + This.percentage)
                  }
                } else {
                  console.log('大于')
                  This.percentage = 100
                  This.truepercentage = 100
                  console.log('percentage3=' + This.percentage)
                  // 请求数据列表
                  This.getDataList()
                  clearInterval(This.timerProgress)
                }
              }, 500)
            }
          }
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getTableStructure() {
      let This = this
      let categoryId = this.currCategory.id
      getTableStructure(categoryId).then((res) => {
        console.log(res)
        if (res.code === 0) {
          console.log('表结构')
          console.log(res.data)
          This.tableThList = res.data
          let tableEditList = JSON.parse(JSON.stringify(res.data))
          let remarkUnit = {
            name: '备注'
          }
          tableEditList = tableEditList.filter(
            (it) => !nonEditableAttrMapping.includes(it.name)
          )
          tableEditList.unshift(remarkUnit)
          This.tableEditList = tableEditList
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('选中的值')
      console.log(val)
    },
    tableTdModify(pindex, keyName, _index, _state, _checkStatus) {
      // 修改值
      this.$set(
        this.templateList[pindex].propertyResultVOMap[keyName],
        'inputState',
        '2'
      )
      this.focusState = true
      this.tdTempValue =
        this.templateList[pindex].propertyResultVOMap[keyName].value
    },
    tableTdBlur(pindex, keyName, index, _checkStatus) {
      console.log('pindex=' + pindex)
      console.log('keyName=' + keyName)
      console.log('index=' + index)
      console.log(this.templateList)
      console.log(this.templateList[pindex])
      let skuid = this.templateList[pindex].id
      console.log(this.templateList[pindex].propertyResultVOMap)
      console.log(this.templateList[pindex].propertyResultVOMap[keyName])
      this.$set(
        this.templateList[pindex].propertyResultVOMap[keyName],
        'inputState',
        '1'
      )
      this.focusState = false
      let newTdValue =
        this.templateList[pindex].propertyResultVOMap[keyName].value
      console.log('此处去验证修改后的值是否正确')
      console.log('修改后的值：' + newTdValue)
      if (newTdValue === this.tdTempValue) {
        console.log('未做修改')
      } else {
        // 已修改
        console.log('已修改')
        /***
        if (checkStatus === true || checkStatus === '1' || checkStatus === 'true') {
          // 有问题的数据的修改，要变为黄色
          this.$set(this.templateList[pindex].propertyResultVOMap[keyName], 'state', true)
        }**/
        this.$set(
          this.templateList[pindex].propertyResultVOMap[keyName],
          'state',
          true
        )
        this.updateProperties(skuid, keyName, newTdValue)
      }
    },
    setCellClassName({ row, column, rowIndex, columnIndex }) {
      let tableThList = this.tableThList
      if (this.commonShowItem.includes(this.pageId)) {
        this.fixedNum = 14
      } else {
        this.fixedNum = 10
      }
      if (columnIndex > this.fixedNum - 1) {
        // 从自定义属性开始
        let nIndex = columnIndex - this.fixedNum
        if (nIndex >= tableThList.length) {
          // 只有在动态属性之中才判断对错，显示状态，前后都不显示
          return ''
        }
        let keyname = tableThList[nIndex].name
        // console.log('keyname=' + keyname)
        let item = row.propertyResultVOMap[keyname]
        // console.log(item)
        if (item) {
          if (
            item.state === true ||
            item.state === 'true' ||
            item.state === '1'
          ) {
            // 修改过的数据
            if (item.inputState === 2 || item.inputState === '2') {
              return 'tsclass3 focus'
            } else {
              return 'tsclass3'
            }
          } else {
            if (
              item.checkStatus === true ||
              item.checkStatus === 'true' ||
              item.checkStatus === '1'
            ) {
              // 有问题的样式
              if (item.inputState === 2 || item.inputState === '2') {
                return 'tsclass2 focus'
              } else {
                return 'tsclass2'
              }
            } else {
              return ''
            }
          }
        } else {
          return ''
        }
      }
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath)
      this.activeIndex = key
      this.listQueryInfo.current = 1
      /**
      this.search = {
        brand: '',
        division: '',
        productName: '',
        skuid: ''
      }**/
      this.getCategoriesProgress()
    },
    refresh() {
      // 刷新
      this.listQueryInfo.current = 1
      this.getCategoriesProgress()
    },
    chooseSkuSync() {
      // 选中sku推送至商品中心
      let This = this
      let selectList = this.multipleSelection
      console.log('已选中sku', selectList)
      let categoryId = this.currCategory.id
      console.log('categoryId', categoryId)
      if (selectList.length > 0) {
        This.$confirm('确认推送选中数据至【商品中心】吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            let skuid = []
            for (let i = 0; i < selectList.length; i++) {
              skuid.push(selectList[i].id)
              /**
            for (let j = 0; j < This.templateList.length; j++) {
              if (selectList[i].id === This.templateList[j].id) {
                This.templateList.splice(j, 1)
                break
              }
            }**/
            }
            let param = {
              skuIds: skuid.join(',')
            }
            getAssignSkuToProductCenter(categoryId, param).then((res) => {
              console.log(res)
              if (res.code === 0) {
                This.$message.success('开始推送推送选中数据至【商品中心】')
              } else {
                This.$message.error(res.msg)
              }
            })
          })
          .catch(() => {
            console.log('取消')
          })
      } else {
        This.$message.error('请先选择要同步的数据')
      }
    },
    addRemark(pindex, item) {
      // 【显示输入框】
      console.log(pindex)
      console.log(item)
      if (this.activeIndex === '') {
        // 只有待修复状态下数据可以修改编辑
        this.$set(this.templateList[pindex].errorRemark, 'inputState', '2')
        this.focusState = true
        this.tdTempValue = this.templateList[pindex].errorRemark.value
      }
    },
    addRemarkBlur(pindex, _item) {
      console.log('pindex=' + pindex)
      console.log(this.templateList)
      console.log(this.templateList[pindex])
      let skuid = this.templateList[pindex].id
      this.$set(this.templateList[pindex].errorRemark, 'inputState', '1')
      this.focusState = false
      let newTdValue = this.templateList[pindex].errorRemark.value
      console.log('此处去验证修改后的值是否正确')
      console.log('修改后的值：' + newTdValue)
      if (newTdValue === this.tdTempValue) {
        console.log('未做修改')
      } else {
        // 已修改
        console.log('已修改')
        this.updateProperties(skuid, '备注', newTdValue)
      }
    },
    openThDialog(item) {
      let This = this
      This.$refs.PropertyRuleDetail.getPropertyRuleDetail(item.id)
    },
    closeThDialog() {
      this.showThDialog = false
    },
    openTdDialog(pindex, keyName, index, state, checkStatus, msg) {
      let This = this
      This.clicknum = This.clicknum + 1
      console.log('次数=' + This.clicknum)
      if (This.clicknum === 1) {
        This.timer = setTimeout(function () {
          // 单击
          console.log('到这里了吧')
          console.log(state)
          console.log(checkStatus)
          if (state === 'true' || state === '1' || state === true) {
            // 已修改 不提示
          } else {
            if (
              checkStatus === 'true' ||
              checkStatus === '1' ||
              checkStatus === true
            ) {
              // 有问题的且未修改的
              This.showTdDialog = true
              This.tdTsTxt = msg
            }
          }
          This.clicknum = 0
        }, 300)
      } else {
        // 双击
        clearTimeout(this.timer)
        if (state === 'true' || state === '1' || state === true) {
          // 已修改
          if (!nonEditableAttrMapping.includes(keyName)) {
            This.tableTdModify(pindex, keyName, index, state, checkStatus)
          }
        } else {
          if (
            checkStatus === 'true' ||
            checkStatus === '1' ||
            checkStatus === true
          ) {
            // 有问题的且未修改的
            if (!nonEditableAttrMapping.includes(keyName)) {
              This.tableTdModify(pindex, keyName, index, state, checkStatus)
            }
          }
        }
        This.clicknum = 0
      }
    },
    modifyTdValue(pindex, keyName, index, state, checkStatus) {
      console.log(pindex, keyName, index, state, checkStatus, '参数')
      if (
        this.activeIndex === '' &&
        !nonEditableAttrMapping.includes(keyName)
      ) {
        // 只有待修复状态下数据可以修改编辑
        console.log('双击----')
        this.tableTdModify(pindex, keyName, index, state, checkStatus)
      }
    },
    checkList() {
      let This = this
      console.log('检测状态=' + This.ischecking)
      if (!This.ischecking) {
        This.selfClickProcess = true
        This.loading.table = true
        This.ischecking = true
        // 非检测状态时，才能重复检测（检测中，不能重复检测）
        let categoryId = this.currCategory.id
        let param = {
          categoryId: categoryId
        }
        this.templateList = []
        this.percentage = 0
        if (This.pageId === 'propertyRepair') {
          // 属性修复检测
          propertiesCheck(param).then((res) => {
            console.log(res)
            // 四级类目才去验证状态
            if (res.code === 0) {
              // 四级类目时验证校验状态
              if (This.currCategory.level === 4) {
                This.timerPro()
              } else {
                This.$message.success('规则校验成功！')
              }
            } else {
              this.$message.error(res.msg)
            }
          })
        } else if (This.pageId === 'uniqueRepair') {
          // 唯一性修复检测
          uniqueCheck(param).then((res) => {
            console.log(res)
            if (res.code === 0) {
              This.timerPro()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else if (This.pageId === 'simpleRepair') {
          // 一般属性问题修复检测
          simpleCheck(param).then((res) => {
            console.log(res)
            if (res.code === 0) {
              This.timerPro()
            } else {
              this.$message.error(res.msg)
            }
          })
        } else if (This.pageId === 'purchaseMaintenance') {
        } else if (This.pageId === 'salesMaintenance') {
        }
      }
    },
    timerPro() {
      // 惟一性/属性修复检测结果的初始化
      let This = this
      // 开启检测
      this.ischecking = true
      this.templateList = []
      this.total = 0
      this.listQueryInfo.current = 1
      this.loading.table = false
      This.truepercentage = 0
      This.percentage = 0
      let timeSecond = 0
      if (This.timerProgress) {
        clearInterval(This.timerProgress)
      }
      This.timerProgress = setInterval(function () {
        let truePercent = This.truepercentage
        if (truePercent <= 100) {
          timeSecond = timeSecond + 0.5
          if (timeSecond >= 5) {
            // 每10秒去请求一次实际进度
            timeSecond = 0
            This.getCategoriesProgress()
          }
          if (This.percentage < 95) {
            if (truePercent > This.percentage) {
              // 当实际进度 > 当前虚拟进度时，虚拟进度直接置位 实际进度
              This.percentage = truePercent
            } else {
              // 当实际进度 > 当前虚拟进度时，虚拟进度直接置位 实际进度
              This.percentage = Number(This.percentage) + 0.5
            }
          }
        } else {
          This.percentage = 100
          This.truepercentage = 100
          // 请求数据列表
          This.getDataList()
          clearInterval(This.timerProgress)
        }
      }, 500)
    },
    getDataList() {
      // 当前类目检测进度完成，请求数据列表
      let This = this
      if (This.selfClickProcess) {
        // 更新类目
        console.log('检测完成，更新目录')
        This.$refs.tree.reload('selfClickProcess')
      }
      let categoryId = this.currCategory.id
      let activeIndex = This.activeIndex
      let brand = ''
      if (This.search.brand) {
        brand = This.search.brand
      }
      let productName = ''
      if (This.search.productName) {
        productName = This.search.productName
      }
      let division = ''
      if (This.search.division) {
        division = This.search.division
      }
      let manufacturerMaterialNo = ''
      if (This.search.manufacturerMaterialNo) {
        manufacturerMaterialNo = This.search.manufacturerMaterialNo
      }
      let ifRemark = This.search.ifRemark
      let skuSourceName = ''
      if (This.search.skuSourceName) {
        skuSourceName = This.search.skuSourceName
      }
      let skuid = ''
      if (This.search.skuid) {
        skuid = This.search.skuid
        skuid = skuid.replace(/[\s|,|，]+/g, ',')
      }
      let param = {
        page: This.listQueryInfo.current - 1,
        size: This.listQueryInfo.pageSize
      }
      let data = {
        categoryId: categoryId,
        status: activeIndex, // 是否修复 0 未修复 1 已修复, 2 已备注 3 所有数据 ''全部问题不传值
        brand: brand, // 搜索项“品牌”
        productName: productName, // 搜索项“产品名称”
        division: division, // 搜索项“修改负责人”
        manufacturerMaterialNo: manufacturerMaterialNo, // 搜索项“制造商型号”
        skuSourceName: skuSourceName, // 搜索项“商品来源”
        skuStr: skuid,
        ifRemark: ifRemark,
        productPositioning: This.search.productPositioning,
        productGroupId: This.search.productGroupId
      }
      // Todo 采购销售处理
      if (This.pageId === 'propertyRepair') {
        // 属性修复列表
        if (This.currCategory.level === 4) {
          let sort1 = 'uploadTime,desc'
          // let sort1 = 'lastModifiedTime,desc'
          let sort2 = 'rowNum,asc'
          getProblemList(param, data, sort1, sort2).then((res) => {
            if (res.code === 0) {
              console.log(res.data)
              This.listDeal(res.data)
            } else {
              this.$message.error(res.msg)
            }
          })
        } else {
          // 非四级类目校验完成后操作
        }
      } else if (This.pageId === 'uniqueRepair') {
        // 唯一性修复列表
        getUniqueList(param, data).then((res) => {
          if (res.code === 0) {
            This.listDeal(res.data)
          } else {
            this.$message.error(res.msg)
          }
        })
      } else if (This.pageId === 'simpleRepair') {
        // 一般属性修复列表修复列表
        getSimpleList(param, data).then((res) => {
          if (res.code === 0) {
            This.listDeal(res.data)
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    listDeal(data) {
      // 问题列表接口返回数据的处理
      let This = this
      This.templateList = data.content
      This.total = data.totalElements
      This.loading.table = false
      // 获取到数据列表后，检测状态置为检测完成，进度条隐藏
      This.ischecking = false
    },
    updateProperties(skuid, keyName, newTdValue) {
      let categoryId = this.currCategory.id
      let validationType = 'FIELD'
      if (this.pageId === 'propertyRepair') {
        // 属性修复
        validationType = 'FIELD'
      } else if (this.pageId === 'uniqueRepair') {
        // 唯一性修复
        validationType = 'UNIQUE'
      } else if (this.pageId === 'simpleRepair') {
        // 一般属性性修复
        validationType = 'NORMAL'
      } else if (this.pageId === 'purchaseMaintenance') {
        // 采购价格修复
        validationType = 'NORMAL'
      } else if (this.pageId === 'salesMaintenance') {
        // 销售价格修复
        validationType = 'NORMAL'
      }
      let param = {
        categoryId: categoryId,
        skuId: skuid,
        propertyName: keyName,
        propertyValue: newTdValue,
        validationType: validationType
      }
      updatePropertiesValue(skuid, param).then((res) => {
        console.log(res)
        if (res.code === 0) {
          console.log('修改成功')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    toFinalData() {
      let This = this
      let categoryId = this.currCategory.id
      toFinalData(categoryId).then((res) => {
        if (res.code === 0) {
          This.$message.success('数据定稿成功')
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    // 一二三或四级类目同步并校验
    skuSync() {
      let This = this
      This.$confirm(
        '确认【商品中心】同步SKU数据至【数据治理平台】吗?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          let categoryId = this.currCategory.id
          skuSync(categoryId).then((res) => {
            if (res.code === 0) {
              This.$message.success(
                '【商品中心】开始同步SKU数据至【数据治理平台】'
              )
              // 同步sku状态只支持四级类目
              if (!This.syncTimer && This.currCategory.level === 4) {
                This.syncTimer = setInterval(function () {
                  This.goGetSkuSyncStatus()
                }, 10000)
              } else {
                console.log('定時器=' + This.syncTimer)
              }
              This.syncStatus = false
            } else {
              This.$message.error(res.msg)
            }
          })
        })
        .catch(() => {
          console.log('取消')
        })
    },
    skuSyncToProductCenter() {
      let This = this
      This.$confirm(
        '确认【数据治理平台】同步SKU属性修改至【商品中心】吗?',
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(() => {
          let categoryId = this.currCategory.id
          let param = {
            categoryId: categoryId
          }
          skuSyncToProductCenter(param).then((res) => {
            if (res.code === 0) {
              This.$message.success(
                '【数据治理平台】开始同步SKU属性修改至【商品中心】'
              )
              if (!This.syncTimer && This.currCategory.level === 4) {
                This.syncTimer = setInterval(function () {
                  This.goGetSkuSyncStatus()
                }, 10000)
              }
              This.reflowStatus = false
            } else {
              This.$message.error(res.msg)
            }
          })
        })
        .catch(() => {
          console.log('取消')
        })
    },
    goGetSkuSyncStatus() {
      // 获取sku同步状态
      let This = this
      let param = {
        categoryId: This.currCategory.id
      }
      getSkuSyncStatus(param).then((res) => {
        if (res.code === 0) {
          This.reflowStatus = res.data.reflowStatus
          This.syncStatus = res.data.syncStatus
          if (This.reflowStatus && This.syncStatus) {
            if (This.syncTimer) {
              clearInterval(This.syncTimer)
              This.syncTimer = null
              This.getCategoriesProgress()
              // 四级类目才获取相应表结构
              This.currCategory.level === 4 && This.getTableStructure()
            }
          }
        }
      })
    },
    getProductPositionOptions() {
      getProductPositioningAndTradeOptions().then((res) => {
        this.productPositioningOptions = res?.data?.ProductPositioning
      })
    },
    remoteMaterialGroup(query) {
      if (query !== '') {
        this.loading.materialGroup = true
        const params = {
          name: query,
          entityType: 'entity.productgroup'
        }
        getCCMaterialGroup(params).then((res) => {
          this.loading.materialGroup = false
          if (res.success && res?.data?.length) {
            const { data } = res
            this.materialGroupOptions = data.filter(
              (it) => it.name.toLowerCase().indexOf(query.toLowerCase()) > -1
            )
          }
        })
      } else {
        this.materialGroupOptions = []
      }
    },
    deleteSkuFn() {
      const self = this
      const selectedRows = self.multipleSelection
      if (!selectedRows.length) {
        self.$message.warning('请先选择要删除的数据！')
        return false
      }
      self
        .$confirm('所勾选数据还未进行治理，是否确认从治理平台移除？', '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          const params = selectedRows.map((it) => it.id).join(',')
          deleteSku({ skuIdStr: params })
            .then((_res) => {
              if (_res?.code === 0) {
                self.getTableStructure()
                self.getCategoriesProgress()
                self.goGetSkuSyncStatus()
                self.$message({
                  type: 'success',
                  message: '删除成功!'
                })
                self.$refs.multipleTable.clearSelection()
              }
            })
            .catch((_err) => { })
        })
        .catch(() => {
          self.$refs.multipleTable.clearSelection()
          self.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleCatalogAdjust() {
      if (!this.multipleSelection.length) {
        this.$message.error('请先选择要修改的数据')
        return
      }
      this.$prompt('待移入四级类目', '你确认将数据移入类目待调整？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPlaceholder: '非必填',
        customClass: 'promt-dialog',
        closeOnClickModal: false
      }).then(async ({ value = '' }) => {
        const { success = false, msg = '' } = await labelAdjustCategorySku({
          categoryName: value,
          categoryId: this.currCategory?.id || '',
          skuIdList: this.multipleSelection.map(item => item?.id || '')
        })
        if (!success) {
          this.$message.error(msg || '操作失败')
          return
        }
        this.$message.success('已将所选商品移动至类目待调整标签下')
        this.getCategoriesProgress()
      }).catch(() => {

      })
    },
    handleRepair() {
      if (!this.multipleSelection.length) {
        this.$message.error('请先选择要修改的数据')
        return
      }
      this.$confirm('你确认将数据恢复至待修复状态吗?', '确认信息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'promt-dialog inline-message',
        closeOnClickModal: false
      }).then(async () => {
        const { success = false, msg = '' } = await cancelAdjustCategorySku({
          categoryId: this.currCategory?.id || '',
          skuIdList: this.multipleSelection.map(item => item?.id || '')
        })
        if (!success) {
          this.$message.error(msg || '操作失败')
          return
        }
        this.$message.success(msg || '操作成功')
        this.getCategoriesProgress()
      }).catch(() => { })
    }
  }
}
</script>
<style lang="scss" scoped>
.dialogTitle2 {
  display: inline-block;
  width: 90px;
  text-align: right;
}
.data-manage-rule-container {
  min-height: 500px;
  .data-manage-rule-center {
    border: 1px solid #eee;
    padding-top: 10px;
    margin-right: 20px;
    .data-manage-rule-add {
      padding: 0 20px 10px;
      overflow: hidden;
      .ruletitle {
        font-size: 18px;
        line-height: 32px;
      }
      .ruleInfo {
        padding-top: 15px;
        text-align:  right;
        float: right;
      }
    }
  }
  .rule-left {
    position: relative;
    transition: width 0.2s ease;
    width:270px;
    .treewrap {
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
      height: auto;
      max-height: 1570px;
    }
    .tree {
      width: 100%;
      overflow:hidden;
      padding: 20px 10px;
      border: 1px solid #eee;
    }
    .switch_tab {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 20px;
      color: #ccc;
      cursor: pointer;
      visibility: visible;
      &:hover {
        color: #b0b0b0;
      }
    }
  }
  .rule-left.fold {
    width: 0;
    .switch_tab {
      right: -20px;
    }
  }
  .rule-right {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
  }
  .dialogTitle {
    text-align: right;
    box-sizing: border-box;
    padding-right: 6px;
    font-size: 13px;
  }
  .ruleset {
    .rulesetcont {
      min-height: 400px;
    }
    .el-row {
      margin-bottom: 10px;
      .item-btn {
        position: relative;
        .item-close {
          position: absolute;
          right:-8px;
          top:-8px;
          font-size: 16px;
        }
      }
    }
  }
  .el-tree-node__content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .progress {
    padding-top: 200px;
    padding-bottom: 200px;
    min-height: 300px;
    max-width: 1200px;
    .tit {
      font-size: 16px;
    }
  }
}

</style>
<style lang="scss">
.rulelist {
  position: relative;
  .progressWrap {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding-top: 200px;
    min-height: 500px;
    z-index: 10;
    background: #fff;
    .tit {
      font-size: 16px;
      text-align: center;
      padding-bottom: 10px;
    }
  }
  .cell {
    overflow: visible;
    span {
      user-select: none;
    }
    .property {
      color: blue;
      cursor: pointer;
      line-height: 16px;
    }
    .prodiv {
      color: #333;
      cursor: pointer;
      width: 100%;
      padding: 10px 0;
      text-align: center;
    }
  }
  .tsinput {
    width: 100% !important;
    height: 100px !important;
    overflow: auto;
    padding: 2px 0;
    text-align: left;
    box-sizing: border-box;
    border:none;
    outline: none;
  }
  .tsclass2 {
    background: #ec808d !important;
    color: #333;
    span {
      background: #ec808d;
      color: #333;
      cursor: pointer;
      position: absolute;
      width: 100%;
      height: 100%;
      left:0;
      top:0;
      line-height: 36px;
      padding: 2px 0;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .prodiv {
      background: #ec808d;
    }
    &:hover {
      background: #ec808d;
    }
  }
  .tsclass2.focus {
    background: #fff !important;
  }
  .tsclass3 {
    color: #333;
    background: yellow !important;
    span {
      background: yellow;
      color: #333;
      cursor: pointer;
      position: absolute;
      width: 100%;
      height: 100%;
      left:0;
      top:0;
      line-height: 36px;
      padding: 2px 0;
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .prodiv {
      background: yellow;
    }
    &:hover {
      background: yellow;
    }
  }
  .tsclass3.focus {
    background: #fff !important;
  }
  .el-table__empty-text {
    width: 100%;
  }
}
.not-forth-category{
  padding: 24px;
}
.importset{
  .el-row{
    margin-bottom: 10px;
  }
}
.data-manage-rule-container {
  .el-progress__text {
    margin-left: 4px;
  }
}
.data-manage-search {
  border-bottom: 1px solid #eee;
  padding: 12px 20px 20px;
  .search-meau {
    padding-bottom: 10px;
  }
  .search-form {
    padding-top: 10px;
    span {
      display: inline-block;
      margin-right: 15px;
      input {
        width: 120px;
      }
    }
    span.el-input__suffix, span.el-input__suffix-inner {
      margin-right: 0;
    }
    .skuIdInput {
      input {
        width: 432px;
      }
    }
    .dialogTitle {
      margin-right: 0;
      padding-right: 0;
    }
    .btns {
      span {
        margin: 0;
      }
    }
  }
}
.promt-dialog{
  color: #ec808d;
  .el-message-box__header{
    border-bottom: 1px solid #EBEEF5;
  }
  .el-message-box__content{
    display: flex;
    align-content: center;
    justify-content: center;
  }
  .el-message-box__container{
    width: 100px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .el-message-box__input{
    width: 250px;
  }
  .el-message-box__btns{
    text-align: center;
  }
}
.inline-message{
  .el-message-box__container{
    width: 100%;
  }
}
</style>
