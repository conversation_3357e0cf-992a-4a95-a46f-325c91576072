<template>
  <div>
    <!--刷新-->
    <div
      class="reload el-icon-refresh"
      :class="{
        propertyRule:
          treeKind === 'propertyRule' || treeKind === 'viewFinalDraft'
      }"
      @click="reload()"
    ></div>
    <!--刷新 end-->
    <!--搜索-->
    <div class="search_box">
      <el-input
        style="width: 170px"
        placeholder="输入关键字"
        v-model.trim="filterText"
      >
      </el-input>
      <el-button style="float: right" type="primary" @click="setCurrentKey"
        >定位</el-button
      >
    </div>
    <!--搜索 end-->
    <div v-loading="loading.tree">
      <div
        id="treeWrapBox"
        :style="{ height: screenHeight }"
        style="min-height: 500px; overflow-y: auto"
      >
        <!--数组件-->
        <el-tree
          :data="categories"
          :props="defaultProps"
          @node-click="handleNodeClick"
          node-key="id"
          :auto-expand-parent="istrue"
          :expand-on-click-node="!commonShowControl.includes(treeKind)"
          :default-expanded-keys="expandedKeys"
          id="treeList"
          class="filter-tree"
          ref="tree"
          @node-drop="handleDrop"
          draggable
          :allow-drop="allowDrop"
          :allow-drag="allowDrag"
        >
          <div
            v-if="commonShowControl.includes(treeKind)"
            class="custom-tree-node"
            slot-scope="{ node, data }"
          >
            <!--属性问题修复-->
            <span v-if="!data.resultTotal" class="name"
              ><span :title="node.label">{{ node.label }}</span></span
            >
            <span
              v-if="data.resultTotal"
              class="name"
              :class="{ notSame: data.finalizeStatus === 0 }"
            >
              <span :title="getNumberTitle(node.label, data.resultTotal)">
                {{ node.label }}
                <i :class="{ new: getNumberClass(data.resultTotal) }"
                  >({{ data.resultTotal | getNumberValue(This) }})</i
                >
              </span>
            </span>
            <!-- <span title="同步并检测" class="edit" @click.stop="syncAndCheck(node, data)"><i class="el-icon-sort"></i></span> -->
          </div>
          <div v-else class="custom-tree-node" slot-scope="{ node, data }">
            <span
              v-if="treeKind === 'propertyRule' || !data.resultTotal"
              class="name"
              :class="{ notSame: data.finalizeStatus === 0 }"
              ><span :title="node.label">{{ node.label }}</span></span
            >
            <span
              v-else
              class="name"
              :class="{ notSame: data.finalizeStatus === 0 }"
              style="width: 100%"
            >
              <span :title="getNumberTitle(node.label, data.resultTotal)">
                {{ node.label }}
                <i :class="{ new: getNumberClass(data.resultTotal) }"
                  >({{ data.resultTotal | getNumberValue(This) }})</i
                >
              </span>
            </span>
            <span
              v-if="treeKind === 'propertyRule' && editable"
              class="edit"
              @click.stop="editCategories(node, data)"
              ><i class="el-icon-edit"></i
            ></span>
          </div>
        </el-tree>
        <!--数组件 end-->
      </div>
    </div>
    <el-dialog
      width="600px"
      title="类目修改"
      :visible.sync="showDialog"
      :close-on-click-modal="false"
    >
      <div class="importset" v-if="editNodeData">
        <el-row :span="24">
          <el-col :span="4" class="dialogTitle" style="text-align: right"
            >所在类目：</el-col
          >
          <el-col :span="20" style="line-height: 32px; color: #a8abc9">{{
            propertyFulePath.split('/').join(' / ')
          }}</el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="4" class="dialogTitle" style="text-align: right"
            >类目名称：</el-col
          >
          <el-col :span="20">
            <el-input v-model="propertyName" />
          </el-col>
        </el-row>
        <el-row :span="24" style="padding-top: 20px">
          <el-col :span="24" style="text-align: right">
            <el-button size="medium" type="danger" @click="deleteDialog"
              >删除</el-button
            >
            <el-button size="medium" @click="closeDialog">关闭</el-button>
            <el-button size="medium" @click="storeModify" type="primary"
              >确认</el-button
            >
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCategoriesTree,
  modifyCategoryName,
  modifyCategoryClass,
  deleteCategory
} from '@/api/dataManage'
import { commonShowControl } from './untils/tools'
export default {
  name: 'catalogTree',
  props: {
    treeType: {
      // 0 默认值获取全部类目  1 获取定稿类目
      type: Number,
      default: 0
    },
    treeKind: {
      type: String, //  默认为 ''; 'propertyRule':属性规则; viewFinalDraft:查看定稿; 'propertyRepair': '属性问题'; 'simpleRepair':一般属性问题; 'uniqueRepair': 唯一性问题;
      default: ''
    },
    editable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: {
        tree: true
      },
      screenHeight: 'none',
      This: this,
      categories: [],
      defaultProps: {
        children: 'catalogueTreeVOList',
        code: 'code',
        id: 'id',
        label: 'name',
        level: 'level'
      },
      istrue: true,
      filterText: '',
      fifterNodes: [], // 筛选出来的节点
      fifterIndex: -1,
      expandedKeys: [], // 需要展开的的节点id
      showDialog: false,
      propertyName: '',
      propertyFulePath: '',
      treeClickCount: 0, // 点击计数（用来判断是不是双击）
      timeCount: null, // 点击计时器
      idCount: null, // 上次点击 的id
      editNodeData: null, // 当前编辑节点信息
      currTreeData: null, // 当前显示的tree节点数据
      commonShowControl
    }
  },
  created() {
    this.getCategories()
  },
  filters: {
    getNumberValue(data, vue) {
      let treeKind = vue.treeKind
      let treeKindType = ''
      if (treeKind === 'propertyRepair') {
        // 属性问题列表
        treeKindType = 'FIELD'
      } else if (treeKind === 'uniqueRepair') {
        // 唯一性问题列表
        treeKindType = 'UNIQUE'
      } else if (treeKind === 'simpleRepair') {
        // 一般属性问题列表
        treeKindType = 'NORMAL'
      } else if (treeKind === 'purchaseMaintenance') {
      } else if (treeKind === 'salesMaintenance') {
      }
      let number = 0
      data.forEach((item, index) => {
        if (item.resultType === treeKindType) {
          number = item.total
        }
      })
      return number
    }
  },
  watch: {
    filterText(val) {
      if (val) {
        this.fifterNodes = []
        this.fifterIndex = -1
        this.findNode(val)
      }
    }
  },
  mounted() {
    let This = this
    document.addEventListener('keydown', function (e) {
      // 在页面中点击enter触发事件
      if (window.event.keyCode === 13) {
        if (This.filterText) {
          // 当tree搜索有值的时候，点击enter触发定位
          This.setCurrentKey()
        }
      }
    })
    // 设置树形菜单高度为屏幕高度，使其能完全显示在屏幕内
    this.screenHeight = window.screen.height - 380 + 'px'
  },
  methods: {
    getNumberTitle(name, data) {
      let treeKind = this.treeKind
      let treeKindType = ''
      if (treeKind === 'propertyRepair') {
        // 属性问题列表
        treeKindType = 'FIELD'
      } else if (treeKind === 'uniqueRepair') {
        // 唯一性问题列表
        treeKindType = 'UNIQUE'
      } else if (treeKind === 'simpleRepair') {
        // 一般属性问题列表
        treeKindType = 'NORMAL'
      } else if (treeKind === 'purchaseMaintenance') {
      } else if (treeKind === 'salesMaintenance') {
      }
      let number = 0
      data.forEach((item, index) => {
        if (item.resultType === treeKindType) {
          number = item.total
        }
      })
      return name + '(' + number + ')'
    },
    getNumberClass(data) {
      let treeKind = this.treeKind
      let treeKindType = ''
      if (treeKind === 'propertyRepair') {
        // 属性问题列表
        treeKindType = 'FIELD'
      } else if (treeKind === 'uniqueRepair') {
        // 唯一性问题列表
        treeKindType = 'UNIQUE'
      } else if (treeKind === 'simpleRepair') {
        // 一般属性问题列表
        treeKindType = 'NORMAL'
      } else if (treeKind === 'purchaseMaintenance') {
      } else if (treeKind === 'salesMaintenance') {
      }
      let status = 0
      data.forEach((item, index) => {
        if (item.resultType === treeKindType) {
          status = item.status
        }
      })
      return status
    },
    handleDrop(draggingNode, dropNode, dropType, ev) {
      // console.log('拖拽完成')
      // console.log('tree drop: ', dropNode.label, dropType);
      // console.log(draggingNode)
      // console.log(dropNode)
      let This = this
      let categoryId = draggingNode.data.id
      let parentId = null
      if (dropType === 'inner') {
        parentId = dropNode.data.id
      } else {
        parentId = dropNode.data.parentId
      }
      // console.log('拖拽的categoryId=' + categoryId)
      // console.log('移入的parentId=' + parentId)
      modifyCategoryClass(categoryId, parentId).then((res) => {
        if (res.code === 0) {
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    allowDrop(draggingNode, dropNode, type) {
      /**
      console.log('拖拽的level=' + draggingNode.data.level)
      console.log(draggingNode)
      console.log('放的level=' + dropNode.data.level)
      console.log('移动的节点id=' + dropNode.data.id)
      console.log(dropNode)
      console.log('type=' + type)
      console.log('值1=' + Number(draggingNode.data.level - 1))
      console.log('值2=' + Number(dropNode.data.level)) **/
      /**
       * 只有两种移动方式可以移动
       * 1.移入到比和自己父级同级别(level-1=level)的菜单内部（type=inner）
       * 2.移入到和自己同级别(level=level)的上面或者下面(type=next||type=prev)
       * 3.由于此项目没有排序功能，所以2其实是1的其他表现形式
       * **/
      if (
        (draggingNode.data.level - 1 === dropNode.data.level &&
          type === 'inner') ||
        (draggingNode.data.level === dropNode.data.level && type !== 'inner')
      ) {
        return true
        /**
        if (draggingNode.data.parent.id !== dropNode.data.parent.id) {
          // 排除掉自己兄弟之间的移动（因为暂时不支持排序）
          console.log('可以移动')
          return true
        } else {
          return false
        } **/
      } else {
        console.log('不可以移动')
        return false
      }
    },
    allowDrag(draggingNode) {
      // 是否允许被拖拽
      if (this.treeKind === 'propertyRule') {
        /**
        console.log('菜单级别=' + draggingNode.data.level)
        if (draggingNode.data.level === 1) {
          this.$message.error('一级菜单不能移动')
        } */
        return draggingNode.data.level !== 1
      } else {
        return false
      }
    },
    editCategories(node, data) {
      this.editNodeData = data
      this.propertyName = data.name
      this.propertyFulePath = data.fulePath
      this.showDialog = true
    },
    syncAndCheck(node, data) {
      // 同步并检测
      this.$emit('treeSync', data)
    },
    reload(state) {
      this.getCategories(state)
    },
    findNode(val) {
      // 找出含有关键字的node
      let This = this
      let nodes = This.categories
      let narrSame = [] // 模糊搜索到的值
      let narrEqual = [] // 精确搜索到的值
      forarr(nodes)
      // 遍历找出所有包含关键字的节点
      function forarr(arr) {
        for (let item of arr) {
          // console.log(item)
          if (item.name.indexOf(val) > -1) {
            if (item.name === val) {
              // 把名字完全相等的放在最前面
              narrEqual.push(item)
            } else {
              narrSame.push(item)
            }
          }
          if (item.catalogueTreeVOList && item.catalogueTreeVOList.length > 0) {
            forarr(item.catalogueTreeVOList)
          }
        }
      }
      this.fifterNodes = narrEqual.concat(narrSame)
    },
    handleNodeClick(data, node) {
      let This = this
      let idCount = data.id // 上次点击的id与这次的对比，相等则次数相加，不相等则次数置为1
      This.currTreeData = data
      if (This.idCount === idCount) {
        this.treeClickCount++
      } else {
        This.idCount = idCount
        This.treeClickCount = 1
      }
      clearTimeout(this.timer)
      this.timer = setTimeout(function () {
        if (This.treeClickCount > 0) {
          if (This.treeClickCount === 1) {
            // 单次点击
            // console.log('单击')
            if (This.commonShowControl.includes(This.treeKind)) {
              This.$emit('treeClick', data)
            } else if (data.level === 4) {
              // 四级目录点击，请求列表数据
              This.$emit('treeClick', data)
            }
          } else {
            // 多次点击
            // console.log('双击')
          }
          This.treeClickCount = 0
          This.idCount = 0
        }
      }, 300)
    },
    getCategories(state) {
      let This = this
      this.loading.tree = true
      if (state === 'selfClickProcess') {
        // 检测后触发的更新类目（去掉loading，无感刷新）
        this.loading.tree = false
      }
      let data = {
        finalizeState: this.treeType // 0 默认值获取全部类目  1 获取定稿类目
      }
      getCategoriesTree(data).then((res) => {
        if (res.code === 0) {
          console.log(res.data, 'xxx')
          this.categories = res.data
          this.$emit('treeLoading', true)
          this.$emit('treeList', res.data)
          this.loading.tree = false
          This.setCurrentKeyAfterLoading()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    setCurrentKey() {
      let This = this
      // 搜索结果为空时，提示
      if (!This.filterText) {
        This.$message.error('请输入关键字进行搜索')
        return false
      }
      if (This.fifterNodes.length < 1) {
        This.$message.error('未找到对应内容')
        return false
      }

      // 搜索出来的节点之间的切换（通过索引值切换）// 切换到最后一个后 就回到初始位置
      This.fifterIndex++
      if (This.fifterIndex > This.fifterNodes.length - 1) {
        This.fifterIndex = 0
      }

      // 设定当前节点
      This.$refs.tree.setCurrentKey(This.fifterNodes[This.fifterIndex].id)

      // 设定自动展开的节点（第一级别时不展开，其他级别展开）
      if (This.fifterNodes[This.fifterIndex].level !== 1) {
        This.expandedKeys.push(This.fifterNodes[This.fifterIndex].id)
      }

      // 当前节点滚动到父级可见区域
      setTimeout(function () {
        let element = document.getElementsByClassName('is-current')[0]
        // console.log('元素距离顶部高度=' + element.offsetTop)
        let parentDiv = document.getElementById('treeWrapBox')
        // console.log('父级高度=' + parentDiv.offsetHeight)
        // console.log('已滚动高度=' + parentDiv.scrollTop)
        if (element.offsetTop >= parentDiv.offsetHeight) {
          // 当元素超过父级区域，则需要使元素滚动父级可见区域内
          let stp =
            element.offsetTop -
            parentDiv.offsetHeight +
            parentDiv.offsetHeight / 2
          // console.log('滚动高度=' + stp)
          parentDiv.scrollTop = stp
        } else {
          let stp = 0
          if (element.offsetTop < parentDiv.offsetHeight / 2) {
            stp = 0
          } else {
            stp = element.offsetTop - parentDiv.offsetHeight / 2
          }
          // console.log('滚动高度=' + stp)
          parentDiv.scrollTop = stp
        }
        // element.scrollIntoView({ block: 'center' })
      }, 300)
      // 满足条件则去请求列表数据
      if (This.fifterNodes[This.fifterIndex].level === 4) {
        // console.log('去请求数据id=' + This.fifterNodes[This.fifterIndex].id)
        // 只有四级的时候才去请求数据
        This.$emit('treeClick', This.fifterNodes[This.fifterIndex])
      }
    },
    setCurrentKeyAfterLoading() {
      // 刷新后的类目定位
      let This = this
      let currTreeData = This.currTreeData
      if (!currTreeData) {
        return false
      }
      // This.expandedKeys = []
      This.$nextTick(function () {
        // console.log('设置节点id', currTreeData.id)
        This.$refs.tree.setCurrentKey(currTreeData.id)
        // console.log('获取当前节点id', This.$refs.tree.getCurrentKey())
        // 设定自动展开的节点（第一级别时不展开，其他级别展开）
        if (currTreeData.level !== 1) {
          This.expandedKeys.push(currTreeData.id)
          // 满足条件则去请求列表数据
          if (currTreeData.level === 4) {
            // 只有四级的时候才去请求数据(只有用户自己点击类目的时候再去请求数据)
            // This.$emit('treeClick', currTreeData)
          }
        }
        // 当前节点滚动到父级可见区域
        setTimeout(function () {
          let element = document.getElementsByClassName('is-current')[0]
          // console.log('元素距离顶部高度=' + element.offsetTop)
          let parentDiv = document.getElementById('treeWrapBox')
          // console.log('父级高度=' + parentDiv.offsetHeight)
          // console.log('已滚动高度=' + parentDiv.scrollTop)
          if (element.offsetTop >= parentDiv.offsetHeight) {
            // 当元素超过父级区域，则需要使元素滚动父级可见区域内
            let stp =
              element.offsetTop -
              parentDiv.offsetHeight +
              parentDiv.offsetHeight / 2
            // console.log('滚动高度=' + stp)
            parentDiv.scrollTop = stp
          } else {
            let stp = 0
            if (element.offsetTop < parentDiv.offsetHeight / 2) {
              stp = 0
            } else {
              stp = element.offsetTop - parentDiv.offsetHeight / 2
            }
            // console.log('滚动高度=' + stp)
            parentDiv.scrollTop = stp
          }
        }, 300)
      })
    },
    closeDialog() {
      this.showDialog = false
    },
    storeModify() {
      let This = this
      let propertyName = this.propertyName
      if (propertyName !== this.editNodeData.name) {
        // console.log('新修改名字=' + propertyName)
        let categoryId = this.editNodeData.id
        let data = {
          name: propertyName
        }
        modifyCategoryName(categoryId, data).then((res) => {
          if (res.code === 0) {
            This.editNodeData.name = propertyName
            This.showDialog = false
            This.$message.success('修改成功')
          } else {
            This.$message.error(res.msg)
          }
        })
      }
    },
    deleteDialog() {
      let This = this
      this.$confirm('确认要删除此类目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          This.$refs.tree.remove(This.editNodeData)
          this.showDialog = false
          let categoryId = This.editNodeData.id
          // console.log(This.editNodeData)
          deleteCategory(categoryId).then((res) => {
            if (res.code === 0) {
              // console.log('删除成功')
              This.$message.success('删除成功')
            } else {
              This.$message.error(res.msg)
            }
          })
        })
        .catch(() => {
          console.log('取消删除')
        })
    }
  }
}
</script>
<style lang="scss" scoped>
  #treeList {
    overflow: auto;
  }
  .search_box {
    padding: 10px 0;
    white-space: nowrap;
    width: 240px;
  }
  .reload {
    position: absolute;
    right: 30px;
    top: 1px;
    font-size: 20px;
    color: #ccc;
    cursor: pointer;
    visibility: visible;
    &:hover {
      color: #b0b0b0;
    }
  }
  .reload.propertyRule {
    right: 5px;
    top: 5px;
  }
</style>
<style lang="scss">
  .el-tree-node.is-current>.el-tree-node__content {
    span {
      color: #409eff;
    }
    .is-leaf {
      opacity: 0;
    }
    .el-tree-node__label{
      color: #fff;
      background: #409eff;
      padding:0 6px;
      border-radius: 2px;
    }
    span.name{
      span {
        color: #fff;
        background: #409eff;
        padding:0 6px;
        border-radius: 2px;
      }
    }
  }
  .dialogTitle{
    line-height: 32px;
  }
  .importset{
    .el-row{
      margin-bottom: 15px;
    }
  }
  .custom-tree-node {
    display: block;
    width:calc(100% - 20px);
    .name {
      float: left;
      display: block;
      width: calc(100% - 40px);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      font-size: 14px;
      i {
        font-style: normal;
      }
      i.new {
        color: #0000ff;
      }
    }
    .edit {
      float: right;
      display: none;
      padding:2px 10px;
    }
    &:hover {
      .edit {
        display: block;
      }
    }
  }
  .el-tree-node__expand-icon {
    font-size: 18px;
    padding: 3px !important;
  }
  span.notSame {
    /*定稿数据和当前数据不一致*/
    color: #0000ff !important;
    span {
      color: #0000ff !important;
      i {
        color: #0000ff !important;
      }
    }
  }
</style>
