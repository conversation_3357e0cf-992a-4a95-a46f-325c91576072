<template>
  <div class="page data-manage-import-container" v-loading="loading.page">
    <div class="data-manage-import-center" v-show="!loading.page">
      <div class="data-manage-search">
        <div class="search-form" style="padding:10px 20px 20px; border-bottom: 1px solid #eee; margin-bottom: 20px;">
          <span class="dialogTitle">规则场景：</span>
          <span>
            <el-select v-model="search.type" placeholder="请选择">
              <el-option
                v-for="item in ruleSceneOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </span>
          <span class="btns" style="margin-left: 10px;">
                <el-button @click="startSearch()" type="primary">查询</el-button>
                <el-button @click="resetSearch()">重置</el-button>
              </span>
        </div>
      </div>
      <div class="data-manage-import-add">
        <p class="importtitle">审批列表</p>
      </div>
      <div class="importlist">
        <el-table
          v-loading="loading.table"
          :data="templateList"
          border
          fit
          highlight-current-row
          height="500"
        >
          <el-table-column label="导出时间" align="center" prop="time">
            <template slot-scope="scope">
              {{(scope.row.time ? scope.row.time : '-' )}}
            </template>
          </el-table-column>
          <el-table-column width="100" label="数据条数" align="center" prop="count" >
            <template slot-scope="scope">
              {{(scope.row.count ? scope.row.count : '-' )}}
            </template>
          </el-table-column>
          <el-table-column width="200" label="导出目录" align="center" prop="categoryStr">
            <template slot-scope="scope">
              {{scope.row.categoryStr}}
            </template>
          </el-table-column>
          <el-table-column label="导出内容" align="center" prop="status" >
            <template slot-scope="scope">
              {{scope.row.status == 0 ? '未修复数据' : (scope.row.status == 2 ? '已备注数据' : (scope.row.status == 3 ? '规则导出' : (scope.row.status == 4 ? '导出定稿数据' : '')))}}
            </template>
          </el-table-column>
          <el-table-column label="导出人员" align="center" prop="staff" >
            <template slot-scope="scope">
              {{(scope.row.staff ? scope.row.staff : '-' )}}
            </template>
          </el-table-column>
          <el-table-column label="备注" align="center" prop="remark" >
            <template slot-scope="scope">
              {{(scope.row.remark ? scope.row.remark : '-' )}}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" prop="state">
            <template slot-scope="scope">
              <div>
                <div v-if="scope.row.auditType === 'AUDITING'">
                  <el-button type="text" @click="agree(scope.row, scope.$index)">通过</el-button>
                  <el-button type="text" @click="refuse(scope.row, scope.$index)" style="margin-left: 10px;">拒绝</el-button>
                </div>
                <div v-else-if="scope.row.auditType === 'PASS'">已通过</div>
                <div v-else-if="scope.row.auditType === 'DENY'">已拒绝</div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout=" total, prev, pager, next, jumper"
      @pagination="pageClick"
    />
  </div>
</template>

<script>
import {
  getExportAuditList,
  getExportAudit
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
export default {
  name: 'directOrderList',
  data () {
    return {
      loading: {
        table: false,
        page: true
      },
      search: {
        type: ''
      },
      createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      total: 0,
      templateList: [],
      ruleSceneOptions: [{ // 规则场景
        value: 'AUDITING',
        label: '审批中'
      }, {
        value: 'PASS',
        label: '已通过'
      }, {
        value: 'DENY',
        label: ' 已拒绝'
      }]
    }
  },
  components: {
    Pagination
  },
  created () {
    this.goGetExportList()
  },
  methods: {
    startSearch () {
      this.goGetExportList()
    },
    resetSearch () {
      this.search.type = ''
      this.goGetExportList()
    },
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetExportList()
    },
    goGetExportList () {
      let This = this;
      let param = {
        page: (This.listQueryInfo.current - 1),
        size: This.listQueryInfo.pageSize,
        name: This.createUser,
        exportAuditType: This.search.type || '', // AUDITING 审核中 PASS 通过 DENY 拒绝
        sort: 'id,desc' // id的倒序 asc时正序
      }
      getExportAuditList(param).then(res => {
        console.log(res);
        if (res.code === 0) {
          This.loading.table = false
          This.loading.page = false
          This.templateList = res.data.content
          This.total = res.data.totalElements
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    agree (row, index) {
      // 同意
      let This = this
      console.log(row)
      let param = {
        exportId: row.id,
        auditType: 'PASS' // AUDITING 审核中 PASS 通过 DENY 拒绝
      }
      This.$confirm('确定要同意吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getExportAudit(param).then(res => {
          if (res.code === 0) {
            This.$set(This.templateList[index], 'auditType', 'PASS')
            This.$message.success('已通过')
          } else {
            This.$message.error(res.msg)
          }
        })
      }).catch(() => {
        console.log('已取消')
      })
    },
    refuse (row, index) {
      // 拒绝
      let This = this
      let param = {
        exportId: row.id,
        auditType: 'DENY' // AUDITING 审核中 PASS 通过 DENY 拒绝
      }
      This.$confirm('确定要拒绝吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getExportAudit(param).then(res => {
          if (res.code === 0) {
            This.$set(This.templateList[index], 'auditType', 'DENY')
            This.$message.success('已拒绝')
          } else {
            This.$message.error(res.msg)
          }
        })
      }).catch(() => {
        console.log('已取消')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.data-manage-import-container{
  min-height: 500px;
  .data-manage-import-center{
    border: 1px solid #eee;
    padding-top: 10px;
    .data-manage-import-add{
      padding: 0 20px 10px;
      overflow: hidden;
      .importtitle{
        float: left;
        font-size: 18px;
        line-height: 32px;
      }
      .importbtn{
        float: right;
      }
      .ellipse2 {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        cursor: default;
        max-height:46px;
      }
    }
  }
}
.dialogTitle{
  line-height: 32px;
}
.importset{
  .el-row{
    margin-bottom: 15px;
  }
}
</style>
