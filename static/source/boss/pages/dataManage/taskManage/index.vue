<template>
  <el-container class="task-wrapper">
    <el-card class="wrapper-head box-card">
      <el-row>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>任务名称</label>
            </el-col>
            <el-col :span="18">
              <el-input
                style="width: 100%"
                placeholder="请输入任务名称"
                v-model="searchFormData['taskName']"
                clearable
              >
              </el-input>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>规则类型</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['governRuleId']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in ruleType"
                  :key="`governRuleId-${item.value}`"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>任务状态</label>
            </el-col>
            <el-col :span="18">
              <el-select
                v-model="searchFormData['taskStatus']"
                placeholder="全部"
                clearable
              >
                <el-option
                  v-for="item in statusType"
                  :key="`taskStatus-${item.code || 'status'}`"
                  :label="item.name"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>创建时间</label>
            </el-col>
            <el-col :span="18">
              <el-date-picker
                style="width: 100%"
                v-model="searchFormData['createTime']"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :value-format="'yyyy-MM-dd'"
                :format="'yyyy-MM-dd'"
              >
              </el-date-picker>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6">
          <el-row>
            <el-col :span="6" class="wrapper-head-label">
              <label>创建人</label>
            </el-col>
            <el-col :span="18">
              <el-input
                v-model="searchFormData['creatorName']"
                clearable
                placeholder="请输入创建人"
              />
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="6" style="text-align: right" class="left-auto">
          <el-button size="mini" @click="handleResetSearch">重置</el-button>
          <el-button size="mini" @click="handleSearch" type="primary"
            >搜索</el-button
          >
        </el-col>
      </el-row>
    </el-card>
    <el-card class="wrapper-body box-card">
      <div class="btn-container">
        <span>
          <el-button size="mini" @click="handleAdd" type="primary"
            >新建治理任务</el-button
          >
          <el-button size="mini" @click="handleDelete">删除</el-button>
        </span>
        <span>
          <el-button size="mini" @click="handleLog('export')"
            >导出记录</el-button
          >
          <el-button size="mini" @click="handleLog('import')"
            >导入记录</el-button
          >
        </span>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        @selection-change="handleSelectionChange"
        border
        fit
        highlight-current-row
      >
        <template slot="empty">
          <div class="empty">
            <p class="tit">没有查询到任何数据，请修改查询条件并重试</p>
          </div>
        </template>
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column
          v-for="column in tableColumns"
          align="center"
          :prop="column.prop"
          :label="column.label"
          :key="column.prop"
        >
          <template slot-scope="scope">
            <span v-if="['taskStatus', 'aiFinishedStatus','aiFixFinishedStatus'].includes(column.prop)">
              <el-popover
                v-if="(scope.row[column.prop]||'').includes('诊断')"
                placement="right"
                width="300"
                @show="handleShowPop(scope.row, column.prop)"
              >
                <el-progress
                  :text-inside="true"
                  :stroke-width="18"
                  :percentage="modelPercent(column.prop)"
                  :format="( ) =>formatPregress(column.prop)"
                ></el-progress>
                <a slot="reference" style="color: #409eff; cursor: pointer">{{
                  scope.row[column.prop] || ''
                }}</a>
              </el-popover>
              <span v-else>{{ scope.row[column.prop] || '' }}</span>
            </span>
            <span v-else>{{ scope.row[column.prop] | defautStr('--') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template slot-scope="scope">
            <el-button type="text" :class="{'btn-label': !isPicHealthRule(scope.row)}" @click="handleCheckDtl(scope.row)" :disabled="isPicHealthRule(scope.row)"
              >查看</el-button
            >
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" :class="{'btn-label': !isPicHealthRule(scope.row)}" @click="hancleUpload(scope.row, 'export')" :disabled="isPicHealthRule(scope.row, 'export')"
              >导出</el-button
            >
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" :class="{'btn-label': !isPicHealthRule(scope.row)}" @click="hancleUpload(scope.row, 'import')" :disabled="isPicHealthRule(scope.row)"
              >导入</el-button
            >
            <el-divider direction="vertical"></el-divider>
            <el-button type="text" :class="{'btn-label': !isPicHealthRule(scope.row)}" @click="handleReCheck(scope.row)" :disabled="isPicHealthRule(scope.row)"
              >重新检测</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 50, 100, 200, 300, 400]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </el-pagination>
    </el-card>
    <createDialog
      v-if="create_dialog"
      :dialogVisible.sync="create_dialog"
      :taskFormData.sync="formData"
      :onAddSuccess="handleSearch"
      :ruleType="ruleType"
    ></createDialog>
    <uploadDialog
      v-if="import_dialog"
      :dialogVisible.sync="import_dialog"
      :formData="importFormData"
      :uploadType="uploadType"
    ></uploadDialog>
    <logDialog
      v-if="log_dialog"
      :dialogVisible.sync="log_dialog"
      :logType="logType"
    ></logDialog>
    <detailDialog
      v-if="detail_dialog"
      :dialogVisible.sync="detail_dialog"
      :formData="detailFormData"
    ></detailDialog>
  </el-container>
</template>

<script>
import {
  qryRuleList,
  qryTaskStatus,
  qryTaskList,
  deleteGovernTask,
  reCheckGovernTask,
  getGovernTaskInfo
} from '@/api/dataManage.js'
import { tableColums } from './helper.js'
import _ from 'lodash'
import { mixin } from './mixin.js'
export default {
  mixins: [mixin],
  components: {
    createDialog: () => import('./components/createDialog.vue'),
    uploadDialog: () => import('./components/uploadDialog.vue'),
    logDialog: () => import('./components/logDialog.vue'),
    detailDialog: () => import('./components/detailDialog.vue')
  },
  data() {
    return {
      tableColumns: _.cloneDeep(tableColums),
      formData: {
        taskName: '',
        operatorName: '',
        taskStatus: '',
        createTime: '',
        fileName: '',
        filePath: '',
        allSee: false,
        checkRuleList: []
      },
      pageSize: 10,
      currentPage: 1,
      total: 0,
      ruleType: [],
      statusType: [],
      loading: false,
      tableData: [],
      multipleSelection: [],
      create_dialog: false,
      checkRuleList: [],
      import_dialog: false,
      detail_dialog: false,
      importFormData: {
        taskName: '',
        governRule: '',
        governRuleId: '',
        catalog1: '',
        catalog2: '',
        catalog3: '',
        catalog4: '',
        fileName: '',
        filePath: ''
      },
      searchFormData: {
        taskName: '',
        taskStatus: '',
        createTime: '',
        governRuleId: ''
      },
      detailFormData: {},
      uploadType: '',
      logType: '',
      log_dialog: false,
      checkedTotal: 0,
      governTotal: 0,
      percent: 0,
      percentAIFinished: 0,
      AIFinishedCount: 0,
      percentAIFixed: 0,
      AIFixedCount: 0,
      timerId: null
    }
  },
  computed: {
    isPicHealthRule() {
      return function (rowData = {}, editType = '') {
        const { governRuleId = '' } = rowData
        /*
          图片高度检测所有编辑按钮都不可用，id:6
          69码对比检测id:7&类目异常检测:8除导出按钮外均不可用
        */
        return (+governRuleId) === 6 || ([7, 8].includes(+governRuleId) && !editType)
      }
    },
    modelPercent() {
      return function(prop) {
        const fetch = {
          taskStatus: () => {
            return this.percent
          },
          aiFinishedStatus: () => {
            return this.percentAIFinished
          },
          aiFixFinishedStatus: () => {
            return this.percentAIFixed
          }
        }
        return (fetch[prop] && fetch[prop]()) || 0
      }
    }
  },
  created() {
    this.qryRuleList()
    this.qryTaskStatus()
    this.qryTaskList()
  },
  methods: {
    async qryRuleList() {
      const { data = [] } = await qryRuleList()
      this.ruleType = data.map((rule) => ({
        value: rule.id,
        label: rule.ruleName
      }))
      // this.checkRuleList = (this.ruleType.slice(0, 1) || []).map(
      //   (rule) => rule.value
      // )
      this.formData.formData = _.cloneDeep(this.checkRuleList)
    },
    handleSearch() {
      this.create_dialog = false
      this.currentPage = 1
      this.searchFun()
    },
    searchFun() {
      const [createTimeFrom = '', createTimeTo = ''] =
        this.searchFormData?.createTime || ''
      const {
        taskName = '',
        taskStatus = '',
        governRuleId = '',
        creatorName = ''
      } = this.searchFormData
      this.qryTaskList(
        {
          page: this.currentPage - 1,
          size: this.pageSize
        },
        {
          taskName,
          taskStatus,
          governRuleId,
          createTimeFrom,
          createTimeTo,
          creatorName
        }
      )
    },
    resetFormData(formData) {
      return Object.keys(formData).reduce((pre, cur) => {
        if (cur === 'checkRuleList') {
          pre[cur] = _.cloneDeep(this.checkRuleList)
        } else {
          pre[cur] = ''
        }
        return pre
      }, {})
    },
    handleResetSearch() {
      this.searchFormData = this.resetFormData({
        ...this.searchFormData
      })
      this.getCurrentList()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    async qryTaskStatus() {
      const { data = [] } = await qryTaskStatus()
      this.statusType = data.map((item) => ({
        ...item,
        value: item.value + ''
      }))
    },
    // 获取任务列表
    qryTaskList(params = {}, data = {}) {
      this.loading = true
      qryTaskList(params, data)
        .then((res) => {
          const { success = false, msg = '', data = {} } = res
          if (!success) {
            this.$message.warning(msg || '获取任务列表失败')
            return
          }
          this.tableData = data?.content || []
          this.total = data?.totalElements || 0
        })
        .catch((err) => {
          console.error(`queryTaskByPage:${err}`)
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleAdd() {
      this.activeStep = 0
      this.formData = this.resetFormData({
        ...this.formData
      })
      this.$nextTick(() => {
        this.create_dialog = true
      })
    },
    handleDelete() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      this.$confirm('数据删除后将无法恢复，确认删除吗？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      })
        .then(() => {
          deleteGovernTask(this.multipleSelection.map((rule) => rule.taskId))
            .then((res) => {
              const { success = false, msg = '' } = res
              if (!success) {
                this.$message.warning(msg || '删除任务失败')
                return
              }
              this.$message.success('删除任务成功')
              this.handleSearch()
            })
            .catch((err) => {
              console.error(`删除任务异常：${err}`)
            })
        })
        .catch(() => { })
    },
    handleSizeChange(val) {
      if ((this.currentPage - 1) * val <= this.total) {
        this.pageSize = val
        this.searchFun()
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.searchFun()
    },
    hancleUpload(data = {}, type = '') {
      const { taskName = '', governRule = '', governRuleId = '', taskType = '' } = data
      this.uploadType = type
      Object.assign(this.importFormData, {
        taskName,
        governRule,
        governRuleId,
        taskType
      })
      this.import_dialog = true
    },
    handleLog(type = '') {
      this.logType = type
      this.$nextTick(() => {
        this.log_dialog = true
      })
    },
    handleCheckDtl(data = {}) {
      const { taskName = '', governRule = '', governRuleId = '', taskType = '' } = data
      Object.assign(this.detailFormData, {
        taskName,
        governRule,
        governRuleId,
        taskType
      })
      this.detail_dialog = true
    },
    // 获取当前列表
    getCurrentList() {
      const { currentPage, pageSize } = this
      const [createTimeFrom = '', createTimeTo = ''] =
        this.searchFormData?.createTime || ''
      const {
        taskName = '',
        taskStatus = '',
        governRuleId = '',
        creatorName = ''
      } = this.searchFormData
      this.qryTaskList(
        {
          page: currentPage - 1,
          size: pageSize
        },
        {
          taskName,
          taskStatus,
          governRuleId,
          createTimeFrom,
          createTimeTo,
          creatorName
        }
      )
    },
    // 重新检测
    async handleReCheck({ taskName, governRuleId }) {
      try {
        const { success = false, msg = '' } = await reCheckGovernTask({ taskName, governRuleId })
        if (!success) {
          this.$message.warning(msg || '重新检测失败')
          return
        }
        this.getCurrentList()
      } catch (error) {
        console.log(`重新检测失败：${error}`)
      }
    },
    formatPregress(prop) {
      const fetch = {
        taskStatus: `${this.checkedTotal}/${this.governTotal}`,
        aiFinishedStatus: `${this.AIFinishedCount}/${this.governTotal}`,
        aiFixFinishedStatus: `${this.AIFixedCount}/${this.governTotal}`
      }
      return (fetch[prop] && fetch[prop]) || ''
    },
    // 轮询任务进度（暂不轮询）
    async pollingTaskProgress(taskName, governRuleId, prop) {
      const That = this
      if (That.timerId) {
        clearInterval(That.timerId)
      }
      // That.timerId = setInterval(async () => {
      try {
        const res = await getGovernTaskInfo({
          governTaskName: taskName,
          governRuleId
        })
        if (res?.success && res?.data) {
          That.percent =
            (res.data?.checkedTotal / res.data?.governTotal).toFixed(2) * 100
          That.checkedTotal = res.data?.checkedTotal
          That.governTotal = res.data?.governTotal
          const { aiFinishedCount = 0, aiFixFinishedCount = 0, governTotal = 0, checkedTotal = 0 } = res?.data || {}
          That.AIFinishedCount = aiFinishedCount
          That.AIFixedCount = aiFixFinishedCount
          That.percentAIFinished = (aiFinishedCount / governTotal).toFixed(2) * 100
          That.percentAIFixed = (aiFixFinishedCount / governTotal).toFixed(2) * 100

          const fetch = {
              taskStatus: () => {
                return checkedTotal === governTotal
              },
              aiFinishedStatus: () => {
                return aiFinishedCount === governTotal
              },
              aiFixFinishedStatus: () => {
                return aiFixFinishedCount === governTotal
              }

          }
          if (fetch[prop] && fetch[prop]()) {
            // clearInterval(That.timerId)
            That.getCurrentList()
          }
        }
      } catch (error) { }
      // }, 1000)
    },
    // 查看诊断中任务进度
    handleShowPop({ taskName, governRuleId }, prop) {
      this.percent = 0
      this.checkedTotal = 0
      this.governTotal = 0
      this.percentAIFinished = 0
      this.AIFinishedCount = 0
      this.percentAIFixed = 0
      this.AIFixedCount = 0
      this.pollingTaskProgress(taskName, governRuleId, prop)
    }
  },
  beforeDestroy() {
    if (this.timerId) {
      clearInterval(this.timerId)
    }
  }
}
</script>

<style lang="scss" scoped>
.task-wrapper{
  margin: 0;
  padding: 10px;
  width: 100%;
  // height: 100%;
  display: flex;
  flex-direction: column;
  .wrapper-head{
    width: 100%;
    .wrapper-head-label{
      text-align: right;
      padding-right: 10px;
      height:32px;
      line-height: 32px;
      margin-bottom:16px;
    }
    ::v-deep {
      .el-select, .el-date-editor{
        width: 100%;
      }
    }
    .left-auto{
      margin-top: 10px;
      float: right;
    }
  }
  .wrapper-body{
    margin-top: 10px;
    .btn-container{
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }
    .btn-label{
      color: #5098ff;
    }
  }
  ::v-deep{
    .el-dialog__header{
      border-bottom: 1px solid #EBEEF5;
      display: flex;
    }
    .el-step__head.is-process,
    .el-step__head.is-success{
      border-color: #597bee;
      color: #597bee;
    }
    .el-step__title.is-success{
      font-weight: bold;
      color: #303133;
    }
    .el-pagination{
      text-align: right;
    }
    .el-upload__input{
      color:#DCDFE6;
      font-size: 12px;
    }
    .el-step__head.is-process{
      .el-step__icon.is-text{
        background: #597bee;
        color: #fff;
      }
    }
  }
}
</style>
