export const uploadUrl = '/fe-upload/api/upload/'

export const tableColums = [
  {
    label: '任务名称',
    prop: 'taskName'
  },
  {
    label: '规则类型',
    prop: 'governRule'
  },
  {
    label: '任务状态',
    prop: 'taskStatus'
  },
  {
    label: 'AI识别状态',
    prop: 'aiFinishedStatus'
  },
  {
    label: 'AI修正状态',
    prop: 'aiFixFinishedStatus'
  },
  {
    label: '治理总数',
    prop: 'governTotal'
  },
  {
    label: '待修复数',
    prop: 'toFixCount'
  },
  {
    label: '已修复数',
    prop: 'finishFixCount'
  },
  {
    label: '无需治理数',
    prop: 'noNeedFixCount'
  },
  {
    label: '创建人',
    prop: 'creatorName'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  }
]

export const importLogTableColumns = [
  {
    label: '文件名称',
    prop: 'name'
  },
  {
    label: '导入时间',
    prop: 'time'
  },
  {
    label: '数据条数',
    prop: 'count'
  },
  {
    label: '任务名称',
    prop: 'governTaskName'
  },
  {
    label: '规则类型',
    prop: 'governRuleName'
  },
  {
    label: '导入人员',
    prop: 'staff'
  },
  {
    label: '导入状态',
    prop: 'state'
  },
  {
    label: '导入详情',
    prop: 'importDetail'
  }
]
export const exportLogTableColumns = [
  {
    label: '导出时间',
    prop: 'time'
  },
  {
    label: '数据条数',
    prop: 'count'
  },
  {
    label: '任务名称',
    prop: 'governTaskName'
  },
  {
    label: '规则类型',
    prop: 'governRuleName'
  },
  {
    label: '导出人员',
    prop: 'staff'
  },
  {
    label: '导出状态',
    prop: 'state'
  },
  {
    label: '操作',
    prop: 'handle'
  }
]

export const governRuleMap = {
  // 属性规则
  propertyRule: 1,
  // 交期规则
  deliveryRule: 2,
  // 关键词规则
  keywordRule: 3,
  // 标品属性规则
  bpRule: 9
}

export const deliveryTableColumns = [
  {
    label: 'sku编码',
    prop: 'skuCode'
  },
  {
    label: '供应商编码',
    prop: 'providerCode'
  },
  {
    label: '供应商名称',
    prop: 'providerName'
  },
  {
    label: '商家仓',
    prop: 'supplierWarehouseName'
  },
  {
    label: '现货发出日',
    prop: 'deliverDayInStock'
  },
  {
    label: '缺货发出日',
    prop: 'deliverDayOutStock'
  }
]

export const switchOPts = [{
  name: '是',
  value: 1
}, {
  name: '否',
  value: 0
}]
