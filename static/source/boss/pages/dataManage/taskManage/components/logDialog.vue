<template>
  <el-dialog v-if="modelVisible" :title="dialog_title[logType] || ''" :visible.sync="modelVisible" width="1000px" center :custom-class="'log-dialog'" :close-on-click-modal="false" @close="onCancel">
    <el-table v-loading="loading" :data="tableData" border fit highlight-current-row :height="'550px'">
      <el-table-column v-for="column in tableColumns" align="center" :prop="column.prop" :label="column.label" :key="column.prop">
        <template slot-scope="scope">
          <span v-if="column.prop === 'state'">
            {{computedState(scope.row)}}
          </span>
          <div v-else-if="column.prop === 'handle'">
            <el-button v-if="scope.row.state==='FAIL'" type="text" @click="onCancel">重试</el-button>
            <el-button v-else-if="scope.row.path && scope.row.state==='SUCCESS'" type="text" @click="downLoad(scope.row.path)">下载</el-button>
          </div>
          <span v-else>
            {{scope.row[column.prop] | defautStr('--')}}
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage" :page-sizes="[10, 50, 100, 200, 300, 400]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total">
    </el-pagination>
  </el-dialog>
</template>

<script>
import { importLogTableColumns, exportLogTableColumns } from '../helper.js'
import _ from 'lodash'
import { getExcelImportHistory, getExportList } from '@/api/dataManage.js'
import { mixin } from '../mixin.js'
export default {
  mixins: [mixin],
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    logType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialog_title: {
        import: '导入记录',
        export: '导出记录'
      },
      loading: false,
      tableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0
    }
  },
  computed: {
    tableColumns() {
      const fetch = {
        'import': _.cloneDeep(importLogTableColumns),
        'export': _.cloneDeep(exportLogTableColumns)
      }
      return fetch[this.logType] || []
    },
    modelVisible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    computedState() {
      return function (rowData = {}) {
        const { state = '' } = rowData
        const fetch = {
          import: {
            SUCCESS: '导入成功',
            WORKING: '导入中',
            FAIL: '导入失败',
            QUEUING: '排队中',
            'SYSTEM_WORKING': '系统录入中'
          },
          export: {
            SUCCESS: '导出成功',
            WORKING: '正在导出',
            FAIL: '导出失败',
            AUDITING: '审批中'
          }
        }
        return (fetch?.[this.logType]?.[state.toUpperCase()]) || ''
      }
    }
  },
  created() {
    this.qryLogList()
  },
  watch: {
    pageSize: {
      handler() {
        this.qryLogList()
      },
      immediate: false
    },
    currentPage: {
      handler() {
        this.qryLogList()
      },
      immediate: false
    }
  },
  methods: {
    getParams() {
      let params = {
        page: this.currentPage - 1,
        size: this.pageSize
        // sort: 'id, desc'
      }
      const fetchMap = {
        import: () => {
          return {
            ...params,
            sort: 'id,desc'
          }
        },
        export: () => {
          return {
            ...params,
            name: window.CUR_DATA?.user?.name || ''
          }
        }
      }
      return (fetchMap[this.logType] && fetchMap[this.logType]()) || {}
    },
    qryLogList() {
      const fetchMap = {
        import: getExcelImportHistory,
        export: getExportList
      }
      if (fetchMap[this.logType]) {
        this.loading = true;
        fetchMap[this.logType]({
          ...this.getParams()
        }).then(res => {
          const { data = {}, success = false, msg = '' } = res
          if (!success) {
            this.$message.warning(msg)
            return
          }
          const { content = [], totalElements = 0 } = data
          this.tableData = content
          this.total = totalElements
        }).finally(() => {
          this.loading = false
        })
      }
    },
    handleSizeChange(val) {
      if ((this.currentPage - 1) * val <= this.total) {
        this.pageSize = val
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
    },
    onCancel() {
      this.$emit('update:dialogVisible', false)
    },
    downLoad(url = '') {
      if (!url) {
        console.error('下载链接不能为空')
        return
      }
      let aLink = document.createElement('a')
      aLink.setAttribute('href', url)
      aLink.setAttribute('id', 'camnpr')
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    }
  }
}
</script>

<style>

</style>
