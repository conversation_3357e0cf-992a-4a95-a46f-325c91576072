<template>
  <el-dialog
    v-if="dialogVisible"
    :title="'任务详情'"
    :visible.sync="modelVisible"
    width="1200px"
    center
    :custom-class="'detail-dialog'"
    :close-on-click-modal="false"
    @close="onCancel"
  >
    <label class="border-bottom">
      <span>任务名称：</span>
      <span class="label-content">{{ formData.taskName }}</span>
      <span>规则类型：</span>
      <span class="label-content">{{ formData.governRule }}</span>
    </label>
    <div class="detail-body">
      <el-aside width="250px" class="height-100">
        <category-tree
          :taskName="formData.taskName || ''"
          :ruleId="formData.governRuleId || ''"
          @itemClick="handleItemClick"
        ></category-tree>
      </el-aside>
      <div
        class="select-tip"
        v-if="!Object.keys(treeItemData).length && formData.governRuleId == 1"
      >
        <i
          class="el-icon-folder-opened"
          style="font-size: 48px; margin-bottom: 12px"
        ></i>
        请选择类目
      </div>
      <el-container class="body-wrapper" v-else>
        <div
          v-if="
            treeItemData &&
            treeItemData.level != 4 &&
            formData.governRuleId == 1
          "
        >
          <div class="body-header">
            <span class="category-left"
              >目录：
              <span class="font-normal">{{ treeItemData.fullPath || '' }}</span>
            </span>
            <el-button type="primary" @click="handleOperate('category')"
              >导出待修复</el-button
            >
            <!-- 暂时隐藏，后期迭代 -->
            <!-- <el-button type="primary">批量合成关键词</el-button> -->
          </div>
          <div class="select-tip">
            <i
              class="el-icon-folder-opened"
              style="font-size: 48px; margin-bottom: 12px"
            ></i>
            请选择四级类目查看任务详情
          </div>
        </div>
        <div class="table-wrapper" v-else>
          <div class="category-path">
            <span class="category-left"
              >目录：
              <span style="color: #ff5500">{{
                treeItemData.fullPath || ''
              }}</span>
            </span>
            <!-- 暂时隐藏，后期迭代 -->
            <!-- <el-button type="primary">批量合成关键词</el-button> -->
          </div>
          <!-- <el-row style="display: flex; align-items: center">
            <el-col :span="1"> 品牌 </el-col>
            <el-col :span="10">
              <el-input placeholder="请输入品牌" />
            </el-col>
            <el-col :span="4">
              <el-button>重置</el-button>
              <el-button type="primary">搜索</el-button>
            </el-col>
          </el-row> -->
          <el-button
            class="export-tobeFixed"
            style="z-index: 9999"
            type="primary"
            @click="
              handleOperate(formData.governRuleId === 3 ? 'keyword' : 'category')
            "
            >{{
              formData.governRuleId === 3 ? '批量合成关键词' : '导出待修复'
            }}</el-button
          >
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane
              v-for="(tab, index) in tabs"
              :label="`${tab.label}（${totalData[tab.key] || 0}）`"
              :name="tab.key"
              :key="`${tab.key}-${index}`"
            >
              <el-table
                :data="tableData[tab.key]"
                :cell-class-name="handleCellClassName"
                v-loading="loading"
                max-height="460px"
                :border="true"
              >
                <el-table-column
                  :label="column.label"
                  v-for="(column, index) in tableColumns"
                  :prop="column.prop + ''"
                  :key="index + ''"
                  :show-overflow-tooltip="true"
                  min-width="120"
                  align="center"
                >
                  <template slot-scope="scope">
                      <el-popover
                        placement="top"
                        title="错误原因："
                        width="200"
                        trigger="click"
                        :disabled="!isShowErrMsg(scope.row, column.prop).errMsg"
                        :content="isShowErrMsg(scope.row, column.prop).errMsg"
                      >
                        <span
                          style="display: inline-block; width: 100%;"
                          slot="reference"
                          :class="{'cursor-pointer': isShowErrMsg(scope.row, column.prop).errMsg}"
                        >
                          {{ scope.row | formatTableDisp(column.prop) }}
                        </span>
                      </el-popover>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
          <el-pagination
            v-if="totalData[activeName] > 0"
            style="margin-top: 12px"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="pageInfo.page"
            :page-size="pageInfo.pageSize"
            :total="totalData[activeName]"
            layout="total, prev,pager,next,jumper"
          ></el-pagination>
        </div>
      </el-container>
    </div>
  </el-dialog>
</template>

<script>
import {
  getTaskTableColumns,
  getTaskCategoryData,
  exportTobeFixedData,
  batchBuildKeyword
} from '@/api/dataManage'
import { governRuleMap } from '../helper'

export default {
  components: {
    categoryTree: () => import('./categoryTree.vue')
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      pageInfo: {
        page: 1,
        pageSize: 10
      },
      tabs: [],
      tableData: {},
      totalData: {},
      tableColumns: [],
      activeName: '',
      treeItemData: {}
    }
  },
  filters: {
    formatTableDisp(row, prop) {
      let disText = ''
      const filterByProp = row.find((it) => it?.propertyId === prop)
      if (filterByProp) {
        disText = filterByProp?.value || '—'
      }
      return disText
    }
  },
  computed: {
    modelVisible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    }
  },
  mounted() {
    if ([2, 3].includes(this.formData.governRuleId)) {
      this.initTableConfig()
    }
  },
  methods: {
    onCancel() {
      this.$emit('update:dialogVisible', false)
    },
    handleItemClick({ data, node }) {
      this.treeItemData = data
      const { level } = data
      console.log(level, 'level')
      if (level === 4 || [2, 3].includes(this.formData.governRuleId)) {
        this.initTableConfig()
      }
    },
    async getList(tabPane) {
      try {
        const {
          tableData,
          totalData,
          formData: { governRuleId },
          pageInfo: { page, pageSize },
          treeItemData: { categoryId = null, level = 0 }
        } = this
        this.loading = true
        const res = await getTaskCategoryData(
          { page: page - 1, size: pageSize },
          {
            categoryId,
            level,
            taskName: this.formData.taskName,
            governRuleId,
            governStatus: tabPane === 'needFix' ? 0 : 1
          }
        )
        if (res?.success) {
          this.$set(totalData, tabPane, res?.data?.totalElements || 0)
          this.$set(tableData, tabPane, res?.data?.content ?? [])
        }
      } catch (err) {
        console.log(`获取类目数据出错了：${err}`)
      } finally {
        this.loading = false
      }
    },
    initTabsConfig() {
      const commonTabs = [
        {
          label: '待修复',
          key: 'needFix'
        },
        {
          label: '已修复',
          key: 'finishFix'
        }
      ]
      const fetch = {
        [governRuleMap.propertyRule]: () => {
          return commonTabs
        },
        [governRuleMap.deliveryRule]: () => {
          return commonTabs
        },
        [governRuleMap.keywordRule]: () => {
          return commonTabs
        },
        [governRuleMap.bpRule]: () => {
          return commonTabs
        }
      }
      const { governRuleId = '' } = this.formData
      return (fetch[governRuleId] && fetch[governRuleId]()) || []
    },
    async initTableColumns() {
      let columns = []
      try {
        const { governRuleId } = this.formData
        const { categoryId } = this.treeItemData
        const res = await getTaskTableColumns({
          governRuleId,
          fourthCategoryId: categoryId || ''
        })
        if (res?.success) {
          columns = (res?.data ?? []).map((it) => ({
            label: it.name,
            prop: it.propertyId
          }))
        }
      } catch (err) {
        console.error(`获取表格列出错：${err}`)
      }
      return columns
    },
    async initTableConfig() {
      this.tabs = this.initTabsConfig()
      const [firstTab = {}] = this.tabs
      this.activeName = firstTab?.key || ''
      const columns = await this.initTableColumns()
      this.tableColumns = columns
      this.getList(this.activeName)
      this.getList('finishFix')
    },
    handleClick(tab, e) {
      this.activeName = tab.name
      this.getList(this.activeName)
    },
    handleCurrentChange(val) {
      this.pageInfo.page = val
      this.getList(this.activeName)
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.getList(this.activeName)
    },
    async handleOperate(type) {
      const typeMapInterface = {
        category: exportTobeFixedData,
        keyword: batchBuildKeyword
      }
      try {
        const { governRuleId, taskName, taskType = '' } = this.formData
        const { categoryId, level } = this.treeItemData
        const res = await typeMapInterface[type]({
          categoryId: categoryId || -1,
          governRuleId,
          level: level || -1,
          taskName,
          taskType
        })
        if (res?.success) {
          this.$message.success('操作成功！')
        } else {
          this.$message.warning('出错了，请重试！')
        }
      } catch (err) { }
    },
    handleCellClassName({ row, column, rowIndex, columnIndex }) {
      let cellClassName = ''
      const { redFlag = false, greenFlag = false } = row[columnIndex] || {}
      cellClassName = (redFlag && 'highlight-red') || (greenFlag && 'highlight-green') || ''
      return cellClassName
    },
    isShowErrMsg(row, prop) {
      const filterByProp = row.find((it) => it?.propertyId === prop)

      return {
        showFlag: filterByProp?.redFlag || false,
        errMsg: filterByProp?.errMsg
      }
    }
  }
}
</script>

<style lang="scss">
.detail-dialog{
  .border-bottom{
    margin-bottom: 12px;
    width: 100%;
    border-bottom: 1px solid #EBEEF5;
    display: block;
    padding-bottom: 12px;
  }
  .label-content{
    min-width: 100px;
    display: inline-block;
    margin-right: 10px;
  }
  .detail-body{
   height:600px;
   display:flex;
    .height-100{
      height: 100%;
    }
    .select-tip{
      width:100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      }
    .body-wrapper{
      padding: 0 10px;
      display: flex;
      flex-direction: column;
      .body-header{
        display: flex;
        padding-bottom: 12px;
        justify-content: space-between;
        border-bottom: 1px solid #CCCCCC;
        .category-left{
          display: inline-block;
          width: 600px;
          height:20px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .font-normal{
          color: #FF5500;
        }
      }
      .table-wrapper{
        position:relative;
        height:100%;
        .category-path{
          display: flex;
          justify-content: space-between;
          padding-bottom: 12px;
          .category-left{
            display: inline-block;
            width: 600px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
        }
        .export-tobeFixed{
          position:absolute;
          top:30px;
          right: 0;
        }
        ::v-deep{
          .el-popover__reference{
            display: inline-block;
            width: 100%;
          }
        }
      }
    }
  }

}
.highlight-red{
  background: red!important;
}
.highlight-green{
  background: #22b066!important;
}
.cursor-pointer{
  cursor: pointer;
}
</style>
