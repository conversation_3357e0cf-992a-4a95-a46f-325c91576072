<template>
  <el-dialog v-if="dialogVisible" :title="import_dialog_title[uploadType] || ''" :visible.sync="modelVisible" width="600px" center :custom-class="'import-dialog'" :close-on-click-modal="false" @close="onCancel">
    <label class="border-bottom">
      <span>任务名称：</span>
      <span class="label-content">{{formData.taskName}}</span>
      <span>规则类型：</span>
      <span class="label-content">{{formData.governRule}}</span>
    </label>
    <div v-if="uploadType === 'import'">
      <el-upload class="upload-demo" drag :action="uploadUrl" :on-success="onUploadSuccess" :before-upload="beforeUpload" :on-error="onUploadError" accept=".xlsx,.xls" name="file" :limit="1" :auto-upload="true">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">待修复数据上传</div>
        <div class="el-upload__tip">仅支持.xlsx文件</div>
      </el-upload>
    </div>
    <div v-if="uploadType === 'export'">
      <el-row :span="24" :gutter="10">
        <el-col :span="4" class="dialogTitle">选择类目：</el-col>
        <el-col :span="5">
          <el-select style="width: 100%" v-model="modelFormData.catelog1" clearable filterable @clear="categorySelectClear(1)" @change="categorySelect(1)" placeholder="请选择">
            <el-option v-for="item in categoryListLevel1" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-select style="width: 100%" v-model="modelFormData.catelog2" clearable filterable :disabled="!modelFormData.catelog1" @clear="categorySelectClear(2)" @change="categorySelect(2)" placeholder="全部">
            <el-option v-for="item in categoryListLevel2" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-select style="width: 100%" v-model="modelFormData.catelog3" clearable filterable :disabled="!modelFormData.catelog2" @clear="categorySelectClear(3)" @change="categorySelect(3)" placeholder="全部">
            <el-option v-for="item in categoryListLevel3" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-select v-model="modelFormData.catelog4" clearable filterable :disabled="!modelFormData.catelog3" @clear="categorySelectClear(4)" @change="categorySelect(4)" placeholder="全部">
            <el-option v-for="item in categoryListLevel4" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row :span="24" :gutter="10">
        <el-col :span="20" :offset="4">
          <span class="tip-text">说明： 若不选择类目，则默认导出该任务下所有数据。</span>
        </el-col>
      </el-row>
    </div>
    <div class="dialog-footer">
      <el-button type="primary" @click="handleConfirm" :loading="uploading">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { uploadUrl } from '../helper.js'
import { qryAllCategoryTree, importAndFix, exportTask } from '@/api/dataManage.js'
import _ from 'lodash'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    },
    uploadType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      import_dialog_title: {
        import: '数据导入',
        export: '导出待修复数据'
      },
      uploadUrl,
      modelFormData: {
        catelog1: '',
        catelog2: '',
        catelog3: '',
        catelog4: '',
        file: ''
      },
      catalogData: [],
      uploading: false
    }
  },
  computed: {
    categoryListLevel1() {
      return this.catalogData
    },
    categoryListLevel2() {
      return this.modelFormData.catelog1 ? this.categoryListLevel1.filter(catalog => catalog.id === this.modelFormData.catelog1)?.[0]?.children || [] : []
    },
    categoryListLevel3() {
      return this.modelFormData.catelog2 ? this.categoryListLevel2.filter(catalog => catalog.id === this.modelFormData.catelog2)?.[0]?.children || [] : []
    },
    categoryListLevel4() {
      return this.modelFormData.catelog3 ? this.categoryListLevel3.filter(catalog => catalog.id === this.modelFormData.catelog3)?.[0]?.children || [] : []
    },
    modelVisible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    }
  },
  created() {
    this.qryAllCategoryTree()
  },
  methods: {
    async qryAllCategoryTree() {
      const { data = [], msg = '', success = false } = await qryAllCategoryTree()
      if (!success) {
        this.$message.warning(msg || '获取类目树失败')
      }
      this.catalogData = _.cloneDeep(data)
    },
    onCancel() {
      this.$emit('update:dialogVisible', false)
    },
    beforeUpload(file) {
      const { name = '', size = '' } = file
      const isLt10M = size / 1024 / 1024 < 10
      let notExcel = '.xlsx,.xls'.indexOf(name.substring(name.lastIndexOf('.'))) === -1
      let msg = (this.modelFormData.file && '目前只能上传1个文件') || (notExcel && '上传文件类型与要求不符') || (!isLt10M && '上传文件大小不能超过 10MB!')
      if (msg) {
        this.$message.error(msg)
        return false
      }
      this.modelFormData.file = file
    },
    onUploadSuccess(response = {}) {
      const { status = 0 } = response?.data || {}
      if (status !== 200) {
        this.$message.error(response?.message || '导入失败！')
      }
    },
    onUploadError(error = {}) {
      this.$message.error(
        error?.msg || error?.message || '上传失败'
      )
    },
    handleConfirm() {
      const fetch = {
        import: importAndFix,
        export: exportTask
      }
      this.uploading = true
      fetch[this.uploadType] && fetch[this.uploadType](this.getUploadParams()).then(res => {
        const { success = false, msg = '' } = res
        if (!success) {
          this.$message.warning(msg || '导入失败')
          return
        }
        this.$message.success(msg)
        this.onCancel()
      }).finally(() => {
        this.uploading = false
      })
    },
    getUploadParams() {
      const fetch = {
        import: () => {
          const { taskName = '', governRuleId = '' } = this.formData
          const { file = '' } = this.modelFormData
          const formData = new FormData()
          formData.append('file', file)
          return {
            params: {
              taskName,
              governRuleId
            },
            data: formData
          }
        },
        export: () => {
          const { taskName = '', governRuleId = '', taskType = '' } = this.formData
          const { name = '' } = window?.CUR_DATA?.user || ''
          const { catelog1 = '', catelog2 = '', catelog3 = '', catelog4 = '' } = this.modelFormData
          return {
            params: {
              name
            },
            data: {
              categoryId: catelog4 || catelog3 || catelog2 || catelog1 || '',
              level: this.findLastIndex([catelog1, catelog2, catelog3, catelog4], opt => opt) + 1,
              taskName,
              governRuleId,
              operatorName: name,
              taskType
            }
          }
        }
      }
      return (fetch[this.uploadType] && fetch[this.uploadType]()) || {}
    },
    findLastIndex(arr, callback, thisArg) {
      for (let index = arr.length - 1; index >= 0; index--) {
        const val = arr[index]
        if (callback.call(thisArg, val, index, arr)) return index
      }
      return -1
    }
  }
}
</script>

<style lang="scss" scoped>
 .import-dialog{
    .border-bottom{
      margin-bottom: 20px;
      width: 100%;
      border-bottom: 1px solid #EBEEF5;
      display: block;
      padding-bottom: 10px;
    }
    .label-content{
      min-width: 100px;
      display: inline-block;
      margin-right: 10px;
    }
    .dialog-footer{
      text-align: center;
      margin-top: 10px;
    }
    .tip-text{
      font-size: 12px;
      color: #5098ff;
      margin-top: 5px;
      display: block;
    }
    .upload-demo{
        text-align: center;
      }
  }
</style>
