<template>
  <el-dialog title="新建诊断任务" :visible.sync="modelVisible" width="800px" center :custom-class="'task-dialog'" :close-on-click-modal="false">
    <el-steps :active="activeStep" finish-status="success">
      <el-step title="确定数据范围"></el-step>
      <el-step title="选择治理规则"></el-step>
      <el-step title="执行治理规则"></el-step>
    </el-steps>
    <!-- <el-scrollbar> -->
    <div v-show="activeStep === 0" class="step-container width-100">
      <el-tabs v-model="activeName" type="border-card" v-loading.lock="dataLoading" @tab-click="handleTabClick">
        <el-tab-pane name="skuTab" label="sku维度">
          <el-tabs v-model="activeTab.skuTab">
            <el-tab-pane label="按照条件筛选" name="selectTab">
              <el-row>
                <el-col :span="span[0]">
                  <label>任务名称</label>
                </el-col>
                <el-col :span="span[1]" :style="{ 'max-width': '361px' }">
                  <el-input placeholder="请输入任务名称" v-model="formData['taskName']" clearable>
                  </el-input>
                </el-col>
              </el-row>
              <el-row :gutter="5">
                <el-col :span="4" class="dialogTitle">选择类目</el-col>
                <el-col :span="5">
                  <el-select style="width: 100%" v-model="formData.firstCategory" clearable filterable placeholder="请选择" @change="categorySelectClear(1)">
                    <el-option v-for="item in categoryListLevel1" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select style="width: 100%" v-model="formData.secondCategory" clearable filterable :disabled="!formData.firstCategory" @change="categorySelectClear(2)" placeholder="全部">
                    <el-option v-for="item in categoryListLevel2" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select style="width: 100%" v-model="formData.thirdCategory" clearable filterable :disabled="!formData.secondCategory" @change="categorySelectClear(3)" placeholder="全部">
                    <el-option v-for="item in categoryListLevel3" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select v-model="formData.fourthCategory" clearable filterable :disabled="!formData.thirdCategory" placeholder="全部">
                    <el-option v-for="item in categoryListLevel4" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="span[0]">
                  <label>选择物料组</label>
                </el-col>
                <el-col :span="span[1]" :style="{ 'width': '361px' }">
                  <el-select filterable clearable remote :remote-method="(name) => getEntityList('materialGroup', name)" v-model="formData.materialGroup" @focus="getEntityList('materialGroup')">
                    <el-option v-for="(item) in option.materialGroup" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="span[0]">
                  <label>选择品牌</label>
                </el-col>
                <el-col :span="span[1]" :style="{ 'max-width': '361px' }">
                  <el-select filterable clearable remote :remote-method="(name) => getEntityList('brand', name)" v-model="formData.brandId" @focus="getEntityList('brand')">
                    <el-option v-for="(item) in option.brand" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="span[0]">
                  <label>商品标签</label>
                </el-col>
                <el-col :span="span[1]" :style="{ 'max-width': '361px' }">
                  <el-select filterable clearable remote :remote-method="(name) => getEntityList('tag', name)" v-model="formData.tag" @focus="getEntityList('tag')">
                    <el-option v-for="(item) in option.tag" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="span[0]">
                  <label>是否上官网</label>
                </el-col>
                <el-col :span="span[1]">
                  <el-radio-group v-model="formData.ifWebProduct">
                    <el-radio v-for="(opt, idx) in switchOPts" :key="idx" :label="opt.value">{{opt.name}}</el-radio>
                  </el-radio-group>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="上传sku列表" name='uploadTab'>
              <el-row :gutter="2">
                <el-col :span="6" class="wrapper-head-label">
                  <label>任务名称</label>
                </el-col>
                <el-col :span="18" :style="{ 'max-width': '361px' }">
                  <el-input placeholder="请输入任务名称" v-model="formData['taskName']" clearable>
                  </el-input>
                </el-col>
              </el-row>
              <el-row :gutter="2">
                <el-col :span="6" class="wrapper-head-label">
                  <label>上传文件</label>
                </el-col>
                <el-col :span="18">
                  <el-upload class="upload-demo" drag :action="uploadUrl" :on-success="onUploadSuccess" :before-upload="beforeUpload" :on-error="onUploadError" :on-remove="handleRemove" accept=".xlsx,.xls" name="file" :limit="1" ref="uploadSKU">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">商品sku列表</div>
                    <div class="el-upload__tip">仅支持.xlsx文件</div>
                  </el-upload>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
        <el-tab-pane label="标品维度" name="bpTab">
          <el-tabs v-model="activeTab.bpTab">
            <el-tab-pane label="按照条件筛选" name="selectTab">
              <el-row>
                <el-col :span="span[0]">
                  <label>任务名称</label>
                </el-col>
                <el-col :span="span[1]" :style="{ 'max-width': '361px' }">
                  <el-input placeholder="请输入任务名称" v-model="formData['taskName']" clearable>
                  </el-input>
                </el-col>
              </el-row>
              <el-row :gutter="5">
                <el-col :span="4" class="dialogTitle">选择类目</el-col>
                <el-col :span="5">
                  <el-select style="width: 100%" v-model="formData.firstCategory" clearable filterable placeholder="请选择" @change="categorySelectClear(1)">
                    <el-option v-for="item in categoryListLevel1" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select style="width: 100%" v-model="formData.secondCategory" clearable filterable :disabled="!formData.firstCategory" @change="categorySelectClear(2)" placeholder="全部">
                    <el-option v-for="item in categoryListLevel2" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select style="width: 100%" v-model="formData.thirdCategory" clearable filterable :disabled="!formData.secondCategory" @change="categorySelectClear(3)" placeholder="全部">
                    <el-option v-for="item in categoryListLevel3" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select v-model="formData.fourthCategory" clearable filterable :disabled="!formData.thirdCategory" placeholder="全部">
                    <el-option v-for="item in categoryListLevel4" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="span[0]">
                  <label>选择品牌</label>
                </el-col>
                <el-col :span="span[1]" :style="{ 'max-width': '361px' }">
                  <el-select filterable clearable remote :remote-method="(name) => getEntityList('brandBp', name)" v-model="formData.brandId" @focus="getEntityList('brandBp')">
                    <el-option v-for="(item) in option.brandBp" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="span[0]">
                  <label>待治理状态</label>
                </el-col>
                <el-col :span="span[1]" :style="{ 'max-width': '361px' }">
                  <el-select filterable clearable  v-model="formData.bpZhiLiStatus">
                    <el-option v-for="(item) in option.status" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
            </el-tab-pane>
            <el-tab-pane label="上传标品清单" name='uploadTab'>
              <el-row :gutter="2">
                <el-col :span="6" class="wrapper-head-label">
                  <label>任务名称</label>
                </el-col>
                <el-col :span="18" :style="{ 'max-width': '361px' }">
                  <el-input placeholder="请输入任务名称" v-model="formData['taskName']" clearable>
                  </el-input>
                </el-col>
              </el-row>
              <el-row :gutter="2">
                <el-col :span="6" class="wrapper-head-label">
                  <label>上传文件</label>
                </el-col>
                <el-col :span="18">
                  <el-upload class="upload-demo" drag :action="uploadUrl" :on-success="onUploadSuccess" :before-upload="beforeUpload" :on-error="onUploadError" :on-remove="handleRemove" accept=".xlsx,.xls" name="file" :limit="1" ref="uploadBP">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">上传标品清单</div>
                    <div class="el-upload__tip">仅支持.xlsx文件</div>
                  </el-upload>
                </el-col>
              </el-row>
            </el-tab-pane>
          </el-tabs>
        </el-tab-pane>
      </el-tabs>

    </div>
    <div v-show="activeStep === 1" class="step-container">
      <div class="container-body">
        <el-row>
          <el-col :span="10" class="text-right">选择所需治理规则:</el-col>
          <el-col :span="12" :offset="1">
            <el-checkbox-group v-model="formData.checkRuleList">
              <el-checkbox class="text-left inline-span" v-for="rule in modelRuleType" :label="rule.value" :key="`ruleType-${rule.value}`">{{ rule.label }}</el-checkbox>
            </el-checkbox-group>
          </el-col>
        </el-row>
      </div>
    </div>
    <div v-show="activeStep === 2" class="step-container">
      <div class="container-body margin-top-20">
        <span class="font-bold step-title">确定针对上传数据按照对应数据治理规则进行诊断？</span>
        <el-row>
          <el-col :span="10" class="text-right"><span class="font-bold">数据治理范围：</span></el-col>
          <el-col :span="12" :offset="1">
            <span class="inline-span text-left">{{ modelDataRange }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="10" class="text-right"><span class="font-bold">数据治理规则：</span></el-col>
          <el-col :span="12" :offset="1">
            <span class="inline-span text-left" v-for="rule in formData.checkRuleList" :key="`governRule-${rule}`">{{ ruleLabel(rule) }}</span>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- </el-scrollbar> -->
    <span slot="footer" class="dialog-footer" v-show="!dataLoading">
      <el-button v-show="ifShowBack" @click="handleLastStep">上一步</el-button>
      <el-button v-show="ifShowNext" type="primary" @click="handleNextStep" :loading="checkLoading">下一步</el-button>
      <el-button v-show="ifShowRun" type="primary" @click="handleAddTask" :loading="runLoading">确定执行</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { uploadUrl, switchOPts } from '../helper.js'
import { checkGovernTask, createGovernTask, qryAllCategoryTree, getCCEntityTypes, getCCOptionSet, checkGovernTaskCondition } from '@/api/dataManage.js'
import _ from 'lodash'
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    taskFormData: {
      type: Object,
      default: () => ({})
    },
    onAddSuccess: {
      type: Function,
      default: null
    },
    ruleType: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeStep: 0,
      uploadUrl,
      runLoading: false,
      catalogData: [],
      // modelFormData: {},
      span: [4, 18],
      option: {
        materialGroup: [],
        brand: [],
        tag: [],
        status: [
          {
            name: '定品属性缺失',
            id: 4
          },
          {
            name: '聚合属性缺失',
            id: 5
          },
          {
            name: '其他属性不一致',
            id: 2
          },
          {
            name: '聚合属性不一致',
            id: 3
          },
          {
            name: '无需治理',
            id: 1
          }
        ],
        brandBp: []
      },
      switchOPts,
      activeName: 'skuTab',
      dataLoading: true,
      checkLoading: false,
      activeTab: {
        skuTab: 'selectTab',
        bpTab: 'selectTab'
      }
    }
  },
  computed: {
    modelVisible: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    modelCheckRuleList() {
      return {
        //  sku维度默认选中属性规则
        checkRuleList: (this.isBpTab && [9]) || [1]
      }
    },
    formData: {
      get() {
        return Object.assign(this.taskFormData, {
          allSee: false,
          ...this.modelCheckRuleList
        })
      },
      set(val) {
        this.$emit('update:taskFormData', val)
      }
    },
    ifShowBack() {
      return [1, 2].includes(this.activeStep)
    },
    ifShowNext() {
      return [0, 1].includes(this.activeStep)
    },
    ifShowRun() {
      return [2].includes(this.activeStep)
    },
    ruleLabel() {
      return function (ruleId = '') {
        const [{ label = '' }] = this.ruleType.filter(
          (rule) => rule.value === ruleId
        )
        return label
      }
    },
    categoryListLevel1() {
      return this.catalogData
    },
    categoryListLevel2() {
      return this.formData.firstCategory ? this.categoryListLevel1.filter(catalog => catalog.id === this.formData.firstCategory)?.[0]?.children || [] : []
    },
    categoryListLevel3() {
      return this.formData.secondCategory ? this.categoryListLevel2.filter(catalog => catalog.id === this.formData.secondCategory)?.[0]?.children || [] : []
    },
    categoryListLevel4() {
      return this.formData.thirdCategory ? this.categoryListLevel3.filter(catalog => catalog.id === this.formData.thirdCategory)?.[0]?.children || [] : []
    },
    isBpTab() {
      return this.activeName === 'bpTab'
    },
    modelRuleType() {
      const fetch = {
        skuTab: () => {
          return this.ruleType.filter(item => (+item.value) !== 9)
        },
        bpTab: () => {
          return this.ruleType.filter(item => (+item.value) === 9)
        }
      }
      return fetch[this.activeName]() || []
    },
    modelDataRange() {
      return this.isBpTab ? '已上传标品' : '已上传sku'
    }
  },
  created() {
    this.qryAllCategoryTree()
  },
  methods: {
    beforeUpload(file) {
      const { name = '', size = '' } = file
      const isLt10M = size / 1024 / 1024 < 10
      let notExcel =
        '.xlsx,.xls'.indexOf(
          name.substring(name.lastIndexOf('.')).toLowerCase()
        ) === -1
      let msg =
        (this.formData.filePath && '目前只能上传1个文件') ||
        (notExcel && '上传文件类型与要求不符') ||
        (!isLt10M && '上传文件大小不能超过 10MB!')
      if (msg) {
        this.$message.error(msg)
        return false
      }
      this.formData.fileName = name
    },
    onUploadSuccess(response = {}) {
      const { link = '', status = 0 } = response?.data || {}
      if (status === 200) {
        // this.$message.success(response?.message || '导入成功！')
        this.formData.filePath = link
      } else {
        this.$message.error(response?.message || '导入失败！')
      }
    },
    handleRemove() {
      this.formData.fileName = ''
      this.formData.filePath = ''
    },
    onUploadError(error = {}) {
      this.$message.error(error?.msg || error?.message || '上传失败')
    },
    handleLastStep() {
      this.activeStep--
    },
    async handleNextStep() {
      let valideTask = await this.validateFormData()
      if (valideTask) return
      this.activeStep++
    },
    validateFormData() {
      return new Promise(async (resolve) => {
        if (this.activeStep === 0) {
          if ((this.activeTab[this.activeName] || '') === 'selectTab') {
            if (!this.formData.taskName) {
              this.$message.warning('请输入任务名称')
              resolve(true)
              return
            }
            this.checkLoading = true
            const { brandId = '', ifWebProduct = '', materialGroup = '', tag = '', firstCategory = '', secondCategory = '', thirdCategory = '', fourthCategory = '' } = this.formData
            const { success = false, msg = '' } = await checkGovernTaskCondition({
              brandId,
              ifWebProduct,
              materialGroup,
              tag,
              firstCategory,
              secondCategory,
              thirdCategory,
              fourthCategory,
              taskType: (this.isBpTab && '2') || ''
            }).finally(() => {
              this.checkLoading = false
            })
            if (!success) {
              resolve(true)
              this.$message.warning(msg)
            }
          } else {
            let msg =
              (!this.formData.taskName && '请输入任务名称') ||
              (!this.formData.filePath && '请上传excel文件！') ||
              ''
            if (msg) {
              this.$message.warning(msg)
              resolve(true)
            }
          }

          resolve(false)
        } else if (this.activeStep === 1) {
          if (!this.formData.checkRuleList.length) {
            this.$message.warning('请选择治理规则')
            resolve(true)
          } else {
            this.checkTaskName().then((res) => resolve(res))
          }
        } else {
          resolve(false)
        }
      })
    },
    checkTaskName() {
      return new Promise((resolve) => {
        checkGovernTask({
          taskName: this.formData.taskName,
          governRuleIds: this.formData.checkRuleList
        })
          .then((res) => {
            // data : true 存在  false 不存在
            const { data = true, msg = '', success = false } = res
            if (!success) {
              this.$message.warning(msg || '校验任务名称接口异常')
              return
            }
            data &&
              this.$message.warning('任务已存在,请确保任务名称+治理规则唯一！')
            resolve(data)
          })
          .catch((err) => {
            console.error(`校验任务名称异常：${err}`)
          })
      })
    },
    handleAddTask() {
      this.runLoading = true
      const { brandId = '', ifWebProduct = '', materialGroup = '', tag = '', firstCategory = '', secondCategory = '', thirdCategory = '', fourthCategory = '', bpZhiLiStatus = '' } = this.formData
      createGovernTask({
        taskName: this.formData.taskName,
        // 0: 全员可见 1: 仅自己可见
        allSee: +!this.formData.allSee,
        governRuleIds: this.formData.checkRuleList,
        name: window?.CUR_DATA?.user?.name || '',
        conditionParam: {
          brandId,
          ifWebProduct,
          materialGroup,
          tag,
          firstCategory,
          secondCategory,
          thirdCategory,
          fourthCategory,
          bpZhiLiStatus,
          taskType: (this.isBpTab && '2') || ''
        },
        uploadDTO: {
          name: this.formData.fileName,
          path: this.formData.filePath,
          uploadName: window?.CUR_DATA?.user?.name || ''
        }
      })
        .then((res) => {
          const { msg = '', success = false } = res
          if (!success) {
            this.$message.warning(msg || '新建治理任务失败')
            return
          }
          this.$message({
            message: '任务创建成功，数据正在诊断中，请稍后在任务列表中查看。',
            type: 'success'
          })
          this.onAddSuccess && this.onAddSuccess()
          // this.$nextTick(() => {
          //   this.$emit('update:dialogVisible', false)
          // })
        })
        .finally(() => {
          this.runLoading = false
        })
    },
    async qryAllCategoryTree() {
      const { data = [], msg = '', success = false } = await qryAllCategoryTree().finally(() => {
        this.dataLoading = false
      })
      if (!success) {
        this.$message.warning(msg || '获取类目树失败')
      }
      this.catalogData = _.cloneDeep(data)
    },
    async getEntityList(type = '', name = '') {
      const fetchApi = {
        materialGroup: getCCEntityTypes,
        brand: getCCEntityTypes,
        tag: getCCOptionSet,
        brandBp: getCCEntityTypes
      }
      const fetchParams = {
        materialGroup: () => {
          return {
            entityType: 'entity.productgroup',
            name
          }
        },
        brand: () => {
          return {
            entityType: 'entity.brand',
            name
          }
        },
        tag: () => {
          return {
            typeCode: 'Tag'
          }
        },
        brandBp: () => {
          return {
            entityType: 'entity.brand',
            name
          }
        }
      }
      if ((this.option?.[type] || []).length && !name) return
      let params = (fetchParams[type] && fetchParams[type]()) || {}
      let { data = [] } = await fetchApi[type](params)
      if (type === 'tag' && name) {
        data = data.filter((item) => !!~item.name.indexOf(name)) || []
      }
      this.option[type] = data
      if (type === 'brand' && !name && !(this.option?.['brandBp'] || []).length) {
        this.option.brandBp = data
      }
    },
    categorySelectClear(level) {
      // 清空选择
      const fetch = {
        1: {
          secondCategory: '',
          thirdCategory: '',
          fourthCategory: ''
        },
        2: {
          thirdCategory: '',
          fourthCategory: ''
        },
        3: {
          fourthCategory: ''
        }
      }
      this.formData = {
        ...this.formData,
        ...fetch[level] || {}
      }
    },
    handleTabClick() {
      this.formData = {
        allSee: false,
        ...this.modelCheckRuleList
      }
      this.$refs.uploadSKU && this.$refs.uploadSKU.clearFiles()
      this.$refs.uploadBP && this.$refs.uploadBP.clearFiles()
    }
  }
}
</script>

<style lang="scss" scoped>
.task-dialog{
    .step-container{
      margin: auto;
      width: 70%;
      .wrapper-head-label{
        text-align: right;
        padding-right: 10px!important;
      }
      .container-body{
        .row-label{
          margin-right: 10px;
        }
        width: 100%;
        text-align: center;
        .text-right{
          text-align: right;
        }
        .text-left{
          text-align: left;
        }
        .inline-span{
          display: block;
        }
      }
      .margin-top-20{
        margin-top: 20px;
      }
      .step-title{
        font-size: 18px;
      }
      .font-bold{
        font-weight: bold;
        color: #303133;
      }
      .step-3-row{
        line-height: 36px;
      }
      ::v-deep{
        .el-row{
          margin-top: 10px;
        }
        .el-checkbox-group{
          display: contents;
        }
        .el-select {
          width: 100%;
          .el-select__caret:first-child::before {
            content: "\e6e1";
          }
          .is-focus {
            .el-select__caret:first-child {
              transform: rotateZ(0deg);
            }
          }
        }
      }
    }
    .width-100{
      width: 100%;
    }
    ::v-deep{
      .el-upload__tip{
        color: #DCDFE6;
      }
    }
  }
</style>
