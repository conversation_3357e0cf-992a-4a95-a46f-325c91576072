<template>
  <div class="tree-wrapper" v-loading="loadingTree">
    <div class="search_box">
      <el-input
        style="width: 100%"
        placeholder="请输入搜索内容"
        prefix-icon="el-icon-search"
        v-model.trim="filterText"
      >
      </el-input>
      <el-card class="height-tree" shadow="always">
        <el-tree
          :data="categories"
          :props="defaultProps"
          @node-click="handleNodeClick"
          :expand-on-click-node="false"
          :highlight-current="true"
          :filter-node-method="handleFilterNode"
          node-key="id"
          id="treeList"
          class="filter-tree"
          ref="tree"
        >
          <template slot-scope="{ node, data }">
            <span>{{ `${node.label}（${data.needFixCount || 0}）` }}</span>
          </template>
        </el-tree>
      </el-card>
    </div>
  </div>
</template>

<script>
import { qryTaskCategoryTree } from '@/api/dataManage.js'
export default {
  props: {
    taskName: {
      type: String,
      default: ''
    },
    ruleId: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loadingTree: false,
      filterText: '',
      categories: [],
      defaultProps: {
        children: 'catalogueTreeVOList',
        id: 'categoryId',
        label: 'categoryName',
        level: 'level',
        needFixCount: 'needFixCount'
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.qryTaskCategoryTree()
  },
  methods: {
    qryTaskCategoryTree() {
      this.loadingTree = true
      qryTaskCategoryTree({
        taskName: this.taskName,
        governRuleId: this.ruleId
      })
        .then((res) => {
          const { data = [], success = false, msg = '' } = res
          if (!success) {
            this.$message.warning(msg || '获取任务 类目树失败')
            return
          }
          this.categories = data
        })
        .finally(() => {
          this.loadingTree = false
        })
    },
    handleFilterNode(value, data) {
      if (!value) return true
      return data.categoryName.indexOf(value) !== -1
    },
    handleNodeClick(data, node) {
      this.$emit('itemClick', { data, node })
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-wrapper{
  width: 100%;
  height: 100%;
  padding: 0 10px 10px 10px;
  margin: 0;
  .search_box{
    height: inherit;
  }
  .height-tree{
    height: calc(100% - 32px);
    overflow: auto;
    .filter-tree{
      overflow: auto;
      span{
        overflow: hidden;
      }
    }
  }
}
.el-tree-node.is-current>.el-tree-node__content {
    span {
      color: #409eff;
    }
    .is-leaf {
      opacity: 0;
    }
    .el-tree-node__label{
      color: #fff;
      background: #409eff;
      padding:0 6px;
      border-radius: 2px;
    }
    span.name{
      span {
        color: #fff;
        background: #409eff;
        padding:0 6px;
        border-radius: 2px;
      }
    }
  }
  ::v-deep.el-card__body{
      display:inline-block;
  }
</style>
