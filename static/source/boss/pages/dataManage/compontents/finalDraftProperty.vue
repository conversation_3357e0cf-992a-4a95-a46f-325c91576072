<template>
  <div>
    <div class="rulelist viewDraft" v-if="currCategory">
      <el-table
        v-loading="loading.table"
        :data="templateList"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="属性名称" align="center" prop="name" />
        <el-table-column label="开启检测" align="center" prop="state" >
          <template slot-scope="scope">
            {{(scope.row.state ? '已开启' : '未开启' )}}
          </template>
        </el-table-column>
        <el-table-column label="是否必填" align="center" prop="required" >
          <template slot-scope="scope">
            {{(scope.row.required ? '是' : '否' )}}
          </template>
        </el-table-column>
        <el-table-column label="属性类型" align="center" prop="type" >
          <template slot-scope="scope">
            {{(scope.row.type == 'MAIN' ? '关键属性' : (scope.row.type == 'SALE' ? '销售属性' : (scope.row.type == 'NORMAL' ? '一般属性' : (scope.row.type == 'BLANK' ? '空' : '-' ) ) ) )}}
          </template>
        </el-table-column>
        <el-table-column label="字段类型" align="center" prop="dataType" >
          <template slot-scope="scope">
            {{(scope.row.dataType == 'TEXT' ? '纯文本' : (scope.row.dataType == 'ENUM_TYPE' ? '枚举值' : (scope.row.dataType == 'NUMBER' ? '数字' : (scope.row.dataType == 'NO_LIMIT' ? '不校验' : '不校验' ) ) ) )}}
          </template>
        </el-table-column>
        <el-table-column label="属性示例" min-width="120px" align="center" prop="example">
          <template slot-scope="scope">
            {{(scope.row.example ? scope.row.example : '-')}}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout=" total, prev, pager, next, jumper"
      @pagination="pageClick"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {
  getFinalDraftList
} from '@/api/dataManage'

export default {
  name: 'propertyRule',
  data() {
    return {
      loading: {
        page: true,
        table: true
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      templateList: null,
      total: 0,
      currCategory: null // 当前选中类目信息
    }
  },
  components: {
    Pagination
  },
  created() {
  },
  methods: {
    pageClick(page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetFinalDraftList()
    },
    goGetFinalDraftList(currCategory) {
      // 获取属性规则列表
      console.log('我进来啦')
      let This = this;
      This.loading.table = true
      if (currCategory) {
        this.currCategory = currCategory
      } else {
        currCategory = this.currCategory
      }
      let param = {
        page: (This.listQueryInfo.current - 1),
        size: This.listQueryInfo.pageSize,
        categoryId: currCategory.id,
        sort: 'order,asc' // id的倒序 asc时正序
      }
      getFinalDraftList(param).then(res => {
        console.log(res);
        if (res.code === 0) {
          This.loading.table = false
          This.templateList = res.data.content
          This.total = res.data.totalElements
        } else {
          This.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.data-manage-rule-container {
  min-height: 500px;
  .data-manage-rule-center {
    padding-top: 10px;
    .data-manage-rule-add {
      padding: 0 0 10px;
      overflow: hidden;
      .ruletitle {
        font-size: 18px;
        line-height: 32px;
      }
      .ruleInfo {
        padding: 20px 0 10px;
      }
    }
  }
  .rule-left {
    float: left;
    width:270px;
    border: 1px solid #eee;
    height: 800px;
    box-sizing: border-box;
    padding: 20px 10px;
    overflow: auto;
    position: relative;
    .reload {
      position: absolute;
      right: 5px;
      top: 5px;
      font-size: 20px;
      color: #ccc;
      cursor: pointer;
      visibility: visible;
      &:hover {
        color: #b0b0b0;
      }
    }
  }
  .rule-right {
    flex: 1;
    box-sizing: border-box;
    overflow: hidden;
    margin-left: 20px;
  }
  .dialogTitle {
    line-height: 32px;
    text-align: right;
    box-sizing: border-box;
    padding-right: 12px;
  }
  .ruleset {
    .rulesetcont {
      min-height: 400px;
      margin-bottom: 10px;
    }
    .el-row {
      margin-bottom: 10px;
      .item-btn {
        position: relative;
        .item-close {
          position: absolute;
          right:-8px;
          top:-8px;
          font-size: 16px;
        }
      }
    }
  }
  .el-tree-node__content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .numberRuleTab {
    width: 90%;
    height: 35px;
    margin: 0 auto;
  }
  .numberRuleDetail {
    border: 1px solid #eee;
    width: 90%;
    margin: 0 auto;
  }
  .numberUnit {
    border: 1px solid #ddd;
    padding:10px;
    margin-bottom: 10px;
    .numberUnitForm {
      min-height: 150px;
      .el-row {
        margin-bottom: 15px;
        .item-btn {
          position: relative;
          .item-close {
            position: absolute;
            right:-8px;
            top:-8px;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
  .dialogClass {
    .el-dialog__body {
      padding:10px 20px !important;
    }
  }
  .rulelist.viewDraft {
    td {
      padding: 8px 0;
    }
  }
</style>
