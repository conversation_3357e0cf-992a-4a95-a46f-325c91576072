<template>
  <div>
    <div v-if="currCategory && tableThList">
      <el-table
        ref="multipleTable"
        v-loading="loading.table"
        :data="templateList"
        border
        fit
        highlight-current-row
        :max-height="screenHeight"
        :style="{ maxHeight: screenHeight}"
        style="max-width: 100%;"
        @selection-change="handleSelectionChange"
      >
        <template slot="empty">
          <div class="progress">
            <p class="tit" >暂无数据</p>
          </div>
        </template>
        <el-table-column
          type="selection"
          width="40">
        </el-table-column>
        <el-table-column min-width="100px" label="品牌" align="center" prop="brand" />
        <el-table-column min-width="100px" label="产品名称" align="center" prop="productName" />
        <el-table-column min-width="80px" label="制造商型号" align="center" prop="manufacturerMaterialNo" />
        <el-table-column min-width="120px" label="核心规格" align="center" prop="coreSpecification" />
        <el-table-column min-width="100px" label="箱规" align="center" prop="boxSpecification" />
        <el-table-column min-width="72px" label="计价单位" align="center" prop="priceUnit" />
        <el-table-column min-width="100px" label="sku" align="center" prop="number" />
        <el-table-column min-width="120px" label="修改负责人" align="center" prop="division" />
        <el-table-column min-width="120px" label="修改人" align="center" prop="lastModifier" />
        <!--灵活添加的属性表头-->
        <el-table-column min-width="120px" :label="item.name" v-for="(item, index) in tableThList" :item="item" :key="index+Math.random()" align="center" :prop="item.keyname" scoped-slot >
          <template slot="header">
            <div>
              <p class="property">{{item.name}}</p>
            </div>
          </template>
          <template slot-scope="scope">
            <!--没问题的td-->
            <div>
              <div :title="scope.row.propertyResultVOMap[item.name] && scope.row.propertyResultVOMap[item.name].value">{{scope.row.propertyResultVOMap[item.name] && scope.row.propertyResultVOMap[item.name].value}}</div>
            </div>
            <!--没有问题的td end-->
          </template>
        </el-table-column>
        <!--灵活添加的属性表头 end-->
        <el-table-column min-width="120px" label="未匹配" align="center" prop="misMatched">
          <template slot-scope="scope">
            <div>{{scope.row.misMatched ? scope.row.misMatched : ''}}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout=" total, prev, pager, next, jumper"
      @pagination="pageClick"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import {
  getTableStructure,
  getFinalDraftSku
} from '@/api/dataManage'

export default {
  name: 'propertyRule',
  data () {
    return {
      loading: {
        table: true
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      tableThList: [],
      templateList: [],
      total: 0,
      screenHeight: '600',
      currCategory: null // 当前选中类目信息
    }
  },
  components: {
    Pagination
  },
  created () {
  },
  mounted () {
    // 设置表格高度为屏幕高度，使其能完全显示在屏幕内
    this.screenHeight = window.screen.height - 300
  },
  methods: {
    pageClick (page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetDataList()
    },
    goGetTableStructure (currCategory) {
      // 获取表结构
      let This = this
      if (currCategory) {
        this.currCategory = currCategory
      }
      let categoryId = this.currCategory.id
      This.loading.table = true
      getTableStructure(categoryId).then(res => {
        if (res.code === 0) {
          This.tableThList = res.data
        } else {
          This.$message.error(res.msg)
        }
      })
      This.goGetDataList()
    },
    goGetDataList () {
      // 获取数据列表
      let This = this
      let categoryId = this.currCategory.id
      let param = {
        page: (This.listQueryInfo.current - 1),
        size: This.listQueryInfo.pageSize,
        categoryId: categoryId,
        sort: ''
      }
      // 属性修复列表
      getFinalDraftSku(param).then(res => {
        console.log(res);
        if (res.code === 0) {
          This.templateList = res.data.content
          This.total = res.data.totalElements
        } else {
          This.$message.error(res.msg)
        }
        This.loading.table = false
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log('选中的值')
      console.log(val)
    }
  }
}
</script>
<style lang="scss" scoped>
  .property {
    color: blue;
    cursor: pointer;
    line-height: 16px;
  }
</style>
