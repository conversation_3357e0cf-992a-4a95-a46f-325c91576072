<template>
  <div class="page data-manage-import-container" v-loading="loading.page">
    <div class="data-manage-import-center" v-show="!loading.page">
      <div class="data-manage-import-add">
        <p class="importtitle">导出记录</p>
        <div class="importbtn">
          <el-button size="small" v-if="hasExportAuth" @click="openImportDialog('rule')" type="primary">导出属性规则</el-button>
          <!-- 需求变更 功能隐藏 <el-button size="small" @click="openImportDialog('data')" type="primary">导出数据</el-button>
          <el-button size="small" @click="openImportDialog('catalog')" type="primary">导出类目待调整</el-button>
          <el-button size="small" v-if="exportFinalDraftDataButton" @click="openExportFinalDataDialog()" type="primary">导出定稿数据</el-button> -->
        </div>
      </div>
      <div class="importlist">
        <el-table v-loading="loading.table" :data="templateList" border fit highlight-current-row height="500">
          <el-table-column label="导出时间" align="center" prop="time">
            <template slot-scope="scope">
              {{ scope.row.time ? scope.row.time : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="数据条数" align="center" prop="count">
            <template slot-scope="scope">
              {{ scope.row.count ? scope.row.count : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="导出目录" align="center" prop="categoryStr">
            <template slot-scope="scope">
              {{ scope.row.categoryStr }}
            </template>
          </el-table-column>
          <el-table-column label="导出内容" align="center" prop="status">
            <template slot-scope="scope">
              {{
                scope.row.status == 0
                  ? '未修复数据'
                  : scope.row.status == 2
                  ? '已备注数据'
                  : scope.row.status == 3
                  ? '规则导出'
                  : scope.row.status == 4
                  ? '导出定稿数据'
                  : scope.row.status == 5
                  ? '类目待调整'
                  :''
              }}
            </template>
          </el-table-column>
          <el-table-column label="导出人员" align="center" prop="staff">
            <template slot-scope="scope">
              {{ scope.row.staff ? scope.row.staff : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="审批人" align="center" prop="auditName">
          </el-table-column>
          <el-table-column label="备注" align="center" prop="remark">
            <template slot-scope="scope">
              {{ scope.row.remark ? scope.row.remark : '-' }}
            </template>
          </el-table-column>
          <el-table-column label="导出问题" align="center" prop="remark">
            <template slot-scope="scope">
              <div v-if="scope.row.message">
                <el-popover placement="top-start" width="500" trigger="hover" :content="scope.row.message">
                  <div class="ellipse2" slot="reference">
                    {{ scope.row.message ? scope.row.message : '-' }}
                  </div>
                </el-popover>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="导出状态" align="center" prop="state">
            <template slot-scope="scope">
              {{
                scope.row.state == 'SUCCESS'
                  ? '导出成功'
                  : scope.row.state == 'WORKING'
                  ? '正在导出'
                  : scope.row.state == 'FAIL'
                  ? '导出失败'
                  : scope.row.state == 'AUDITING'
                  ? '审批中'
                  : '-'
              }}
              <span style="color: #597bee; margin-left: 5px; cursor: pointer" v-if="scope.row.state == 'SUCCESS'">
                <a :href="scope.row.path">下载</a>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <pagination v-show="total > 0" :total="total" align="right" :page.sync="listQueryInfo.current" :limit.sync="listQueryInfo.pageSize" layout=" total, prev, pager, next, jumper" @pagination="pageClick" />
    <!--导出规则、数据弹出框-->
    <el-dialog width="780px" :title="exportTitle" :visible.sync="showImportDialog" :close-on-click-modal="false" v-if="showImportDialog">
      <div class="importset">
        <el-tabs  v-if="exportState === 'rule'">
          <el-tab-pane label="同一级类目导出">
            <template>
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">品类定位</el-col>
                <el-select v-model="positionValue" placeholder="全部">
                    <el-option v-for="item in positionOpts" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
              </el-row>
              <el-row :span="24" :gutter="10">
                <el-col :span="3" class="dialogTitle">导出类目：</el-col>
                <el-col :span="5">
                  <el-select filterable clearable v-model="categoryLevel1Value" :disabled="(!categoryListLevel1 || categoryListLevel1.length < 0)" @change="categorySelect(1)" placeholder="请选择">
                    <el-option v-for="item in categoryListLevel1" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select filterable clearable v-model="categoryLevel2Value" :disabled="!categoryListLevel2 || categoryListLevel2.length < 0" @change="categorySelect(2)" placeholder="全部">
                    <el-option v-for="item in categoryListLevel2" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select filterable clearable v-model="categoryLevel3Value" :disabled="!categoryListLevel3 || categoryListLevel3.length < 0" @change="categorySelect(3)" placeholder="全部">
                    <el-option v-for="item in categoryListLevel3" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="5">
                  <el-select filterable clearable v-model="categoryLevel4Value" :disabled="!categoryListLevel4 || categoryListLevel4.length < 0" @change="categorySelect(4)" placeholder="全部">
                    <el-option v-for="item in categoryListLevel4" :key="item.id" :label="item.name" :value="item.id">
                    </el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">品牌：</el-col>
                <el-select filterable clearable remote :remote-method="(name) => getEntityList('brand', name)" v-model="brandId" @focus="getEntityList('brand')">
                  <el-option v-for="(item) in option.brand" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-row>
              <el-row :span="24">
                <el-col :span="3" class="dialogTitle">备注：</el-col>
                <el-col :span="16">
                  <el-input type="textarea" :rows="3" placeholder="请输入内容" v-model.trim="remarks">
                  </el-input>
                </el-col>
              </el-row>
            </template>
          </el-tab-pane>
          <el-tab-pane label="跨一级类目导出">
            <span class="mg-b-10">输入需要导出的四级类目名称或类目id，用英文逗号或空格隔开（最多支持200个）</span>
            <el-row :span="24">
              <el-col :span="23">
                  <el-input type="textarea" :rows="8" placeholder="请输入内容" v-model="exportCategory">
                  </el-input>
                </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right">
            <el-button size="medium" @click="closeImportDialog">关闭</el-button>
             <el-button size="medium" @click="handleSave" type="primary" style="margin-left: 20px">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--导出规则、数据弹出框 end-->
    <!--导出定稿弹出框-->
    <el-dialog width="780px" title="导出定稿数据" :visible.sync="showExportFinalDataDialog" :close-on-click-modal="false">
      <div class="importset">
        <el-row :span="24" :gutter="10">
          <el-col :span="3" class="dialogTitle">导出类目：</el-col>
          <el-col :span="5">
            <el-select filterable clearable v-model="categoryLevel1ValueFinal" :disabled="
                !categoryListLevel1Final || categoryListLevel1Final.length < 0
              " @change="categorySelectFinal(1)" placeholder="请选择">
              <el-option v-for="item in categoryListLevel1Final" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select filterable clearable v-model="categoryLevel2ValueFinal" :disabled="
                !categoryListLevel2Final || categoryListLevel2Final.length < 0
              " @change="categorySelectFinal(2)" placeholder="全部">
              <el-option v-for="item in categoryListLevel2Final" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select filterable clearable v-model="categoryLevel3ValueFinal" :disabled="
                !categoryListLevel3Final || categoryListLevel3Final.length < 0
              " @change="categorySelectFinal(3)" placeholder="全部">
              <el-option v-for="item in categoryListLevel3Final" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="5">
            <el-select filterable clearable v-model="categoryLevel4ValueFinal" :disabled="
                !categoryListLevel4Final || categoryListLevel4Final.length < 0
              " @change="categorySelectFinal(4)" placeholder="全部">
              <el-option v-for="item in categoryListLevel4Final" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="3" class="dialogTitle">物料组：</el-col>
          <el-col :span="16">
            <el-select v-model="materialValue" filterable placeholder="请选择">
              <el-option v-for="item in materialOptions" :key="item.id" :label="item.name" :value="item.id">
              </el-option>
            </el-select>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="3" class="dialogTitle">备注：</el-col>
          <el-col :span="16">
            <el-input type="textarea" :rows="3" placeholder="请输入内容" v-model.trim="remarks">
            </el-input>
          </el-col>
        </el-row>
        <el-row :span="24">
          <el-col :span="24" style="text-align: right">
            <el-button size="medium" @click="closeExportFinalDataDialog">关闭</el-button>
            <el-button size="medium" :loading="loading.submitFinal" @click="sureExportFinalData" type="primary" style="margin-left: 20px">确认</el-button>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
    <!--导出定稿弹出框 end-->
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getCategoriesTree,
  getExportList,
  exportData,
  exportRule,
  exportFinalData,
  getProductGroup,
  exportCategoryAdjustSku,
  queryEnum,
  getCCEntityTypes
} from '@/api/dataManage'
import Pagination from '@/components/Pagination'
import moment from 'moment'
import { findButton } from './common/untils/tools'
export default {
  name: 'directOrderList',
  data() {
    return {
      loading: {
        table: false,
        page: true,
        submitFinal: false
      },
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      total: 0,
      rules: {},
      createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
      createTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      templateList: [], // 文件列表
      showImportDialog: false,
      showExportFinalDataDialog: false,
      uploadUrl: '/fe-upload/api/upload/',
      remarks: '', // 备注信息
      filePath: '', // 传oss后生成的文件路径
      fileName: '', // 文件在本地的名字
      exportCategory: '', // 导出类目
      exportOptions: [
        {
          value: '0',
          label: '未修复数据'
        },
        {
          value: '2',
          label: '已备注数据'
        }
      ],
      exportValue: '',
      checkList: [],
      categoryList: null, // 目录树列表数据
      categoryListLevel1: null, // 目录树第一级列表数据
      categoryListLevel2: null, // 目录树第二级列表数据
      categoryListLevel3: null, // 目录树第三级列表数据
      categoryListLevel4: null, // 目录树第四级列表数据
      categoryLevel1Value: null, // 目录树第一级选中结果
      categoryLevel2Value: null, // 目录树第二级选中结果
      categoryLevel3Value: null, // 目录树第三级选中结果
      categoryLevel4Value: null, // 目录树第四级选中结果
      exportState: 'data', // 导出类型： data 数据  rule 规则
      categoryListFinal: null, // 目录树列表数据(定稿)
      categoryListLevel1Final: null, // 目录树第一级列表数据(定稿)
      categoryListLevel2Final: null, // 目录树第二级列表数据(定稿)
      categoryListLevel3Final: null, // 目录树第三级列表数据(定稿)
      categoryListLevel4Final: null, // 目录树第四级列表数据(定稿)
      categoryLevel1ValueFinal: null, // 目录树第一级选中结果(定稿)
      categoryLevel2ValueFinal: null, // 目录树第二级选中结果(定稿)
      categoryLevel3ValueFinal: null, // 目录树第三级选中结果(定稿)
      categoryLevel4ValueFinal: null, // 目录树第四级选中结果(定稿)
      materialOptions: [], // 物料选择
      materialValue: '',
      activatedState: true, // 当前路由的激活状态
      exportFinalDraftDataButton: false, // 是否显示导出定稿数据button
      separateFlag: false, // 是否按照四级目录拆分
      positionValue: '',
      positionOpts: [],
      brandId: '',
      option: {
        brand: []
      }
    }
  },
  components: {
    Pagination
  },
  created() {
    this.goGetExportList()
    this.getCategories()
    this.getCategoriesFinal()
    this.goGetProductGroup()
    this.qryPositionOpts()
  },
  activated() {
    // console.log('我激活了')
    let This = this
    This.timer = setInterval(function () {
      This.goGetExportList()
    }, 1000 * 10)
    This.activatedState = true
  },
  deactivated() {
    // console.log('我退出了')
    let This = this
    if (This.timer) {
      clearInterval(This.timer)
    }
    This.activatedState = false
  },
  beforeDestroy() {
    // console.log('我销毁了')
    let This = this
    if (This.timer) {
      clearInterval(This.timer)
    }
    This.activatedState = false
  },
  computed: {
    ...mapState(['menu']),
    exportTitle() {
      const titleFetch = {
        'rule': '导出规则',
        'data': '导出数据',
        'catalog': '导出类目待调整'
      }
      return titleFetch[this.exportState] || ''
    },
    hasExportAuth() {
      return findButton(
        this.menu,
        '/dataManage/export',
        '导出属性规则'
      )
    }
  },
  mounted() {
    let menu = this.menu
    this.exportFinalDraftDataButton = findButton(
      menu,
      '/dataManage/export',
      '导出定稿数据'
    )
    let This = this
    document.addEventListener('visibilitychange', function () {
      let string = document.visibilityState
      if (string === 'hidden') {
        // 当页面由前端运行在后端时，出发此代码
        // console.log('我被隐藏了')
        if (This.timer) {
          clearInterval(This.timer)
        }
      }
      if (string === 'visible') {
        // 当页面由隐藏至显示时
        // console.log('欢迎回来')
        if (This.activatedState) {
          // console.log('定时器开启=' + This.activatedState)
          This.timer = setInterval(function () {
            This.goGetExportList()
          }, 1000 * 10)
        }
      }
    })
  },
  methods: {
    goGetProductGroup() {
      // 获取物料组列表
      let data = {}
      getProductGroup(data).then((res) => {
        console.log(res)
        if (res.code === 0) {
          let arr = {
            id: '',
            name: '全部'
          }
          let materialOptions = res.data
          materialOptions.unshift(arr)
          this.materialOptions = materialOptions
          this.materialValue = ''
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    pageClick(page) {
      this.listQueryInfo.current = page.page
      this.loading.table = true
      this.goGetExportList()
    },
    getCategories() {
      let data = {
        finalizeState: 0
      }
      getCategoriesTree(data).then((res) => {
        console.log(res)
        if (res.code === 0) {
          this.categoryList = res.data
          this.categoryListLevel1 = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    getCategoriesFinal() {
      // 获取定稿类目
      let data = {
        finalizeState: 1
      }
      getCategoriesTree(data).then((res) => {
        console.log(res)
        if (res.code === 0) {
          this.categoryListFinal = res.data
          this.categoryListLevel1Final = res.data
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    sureImportData() {
      // 提交上传数据信息
      let This = this
      // excel数据导入接口
      let param = {
        exportName: This.createUser, // 导出人
        remark: This.remarks,
        separateFlag: This.separateFlag
      }
      if (this.checkList.length <= 0) {
        This.$message.error('请选择导出分类')
        return false
      }
      let flagArray = this.checkList.join(',')
      let status = this.exportValue
      if (status === '') {
        This.$message.error('请选择导出内容')
        return false
      }
      let categoryId = '' // 只需要最后一级类目
      let categoryName = [] // 当前选中分类名字
      if (This.categoryLevel1Value) {
        categoryName.push(
          This.categoryListLevel1.filter(function (item) {
            return item.id === This.categoryLevel1Value
          })
        )
      }
      if (This.categoryLevel2Value) {
        categoryName.push(
          This.categoryListLevel2.filter(function (item) {
            return item.id === This.categoryLevel2Value
          })
        )
      }
      if (This.categoryLevel3Value) {
        categoryName.push(
          This.categoryListLevel3.filter(function (item) {
            return item.id === This.categoryLevel3Value
          })
        )
      }
      if (This.categoryLevel4Value) {
        categoryName.push(
          This.categoryListLevel4.filter(function (item) {
            return item.id === This.categoryLevel4Value
          })
        )
      }
      let categoryNameStr = categoryName.map((item, index) => {
        return item[0].name
      })
      categoryNameStr = categoryNameStr.join('/')
      if (This.categoryLevel4Value) {
        categoryId = This.categoryLevel4Value
      } else if (This.categoryLevel3Value) {
        categoryId = This.categoryLevel3Value
      } else if (This.categoryLevel2Value) {
        categoryId = This.categoryLevel2Value
      } else if (This.categoryLevel1Value) {
        categoryId = This.categoryLevel1Value
      } else {
        This.$message.error('请选择类目')
        return false
      }
      let querystr =
        '?flagArray=' +
        flagArray +
        '&categoryId=' +
        categoryId +
        '&status=' +
        status
      exportData(querystr, param).then((res) => {
        console.log(res)
        if (res.code === 0) {
          console.log('导出请求发送成功')
          let narr = {
            time: moment().format('YYYY-MM-DD HH:mm:ss'),
            count: '-',
            staff: this.createUser,
            remark: this.remarks,
            categoryStr: categoryNameStr,
            status: this.exportValue,
            state: 'WORKING'
          }
          This.templateList.unshift(narr)
          This.showImportDialog = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    sureImportRule() {
      // 提交上传数据信息
      let This = this
      // excel数据导入接口
      let param = {
        exportName: This.createUser, // 导出人
        remark: This.remarks,
        fourCatalogs: this.exportCategory.trim().replace(/[\s,，]/g, ' ').replace(/[\s]+/g, ',')
      }
      let categoryId = '' // 只需要最后一级类目
      let categoryName = [] // 当前选中分类名字
      if (This.categoryLevel1Value) {
        categoryName.push(
          This.categoryListLevel1.filter(function (item) {
            return item.id === This.categoryLevel1Value
          })
        )
      }
      if (This.categoryLevel2Value) {
        categoryName.push(
          This.categoryListLevel2.filter(function (item) {
            return item.id === This.categoryLevel2Value
          })
        )
      }
      if (This.categoryLevel3Value) {
        categoryName.push(
          This.categoryListLevel3.filter(function (item) {
            return item.id === This.categoryLevel3Value
          })
        )
      }
      if (This.categoryLevel4Value) {
        categoryName.push(
          This.categoryListLevel4.filter(function (item) {
            return item.id === This.categoryLevel4Value
          })
        )
      }
      let categoryNameStr = categoryName.map((item, index) => {
        return item[0].name
      })
      categoryNameStr = categoryNameStr.join('/')
      categoryId = This.categoryLevel4Value || This.categoryLevel3Value || This.categoryLevel2Value || This.categoryLevel1Value || ''
      if (!categoryId && !this.exportCategory) {
        This.$message.error('请选择类目')
        return false
      }
      let querystr = `?categoryId=${categoryId}&position=${this.positionValue}&brandId=${this.brandId}`
      exportRule(querystr, param).then((res) => {
        console.log(res)
        if (res.code === 0) {
          console.log('导出请求发送成功')
          let narr = {
            time: moment().format('YYYY-MM-DD HH:mm:ss'),
            count: '-',
            staff: this.createUser,
            remark: this.remarks,
            categoryStr: categoryNameStr,
            status: 3,
            state: 'WORKING'
          }
          This.templateList.unshift(narr)
          This.showImportDialog = false
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    sureExportFinalData() {
      // 提交上传数据信息--导出定稿数据
      let This = this
      // excel数据导入接口
      let data = {
        exportName: This.createUser, // 导出人
        remark: This.remarks
      }
      let categoryId = '' // 只需要最后一级类目
      let categoryName = [] // 当前选中分类名字
      if (This.categoryLevel1ValueFinal) {
        categoryName.push(
          This.categoryListLevel1Final.filter(function (item) {
            return item.id === This.categoryLevel1ValueFinal
          })
        )
      }
      if (This.categoryLevel2ValueFinal) {
        categoryName.push(
          This.categoryListLevel2Final.filter(function (item) {
            return item.id === This.categoryLevel2ValueFinal
          })
        )
      }
      if (This.categoryLevel3ValueFinal) {
        categoryName.push(
          This.categoryListLevel3Final.filter(function (item) {
            return item.id === This.categoryLevel3ValueFinal
          })
        )
      }
      if (This.categoryLevel4ValueFinal) {
        categoryName.push(
          This.categoryListLevel4Final.filter(function (item) {
            return item.id === This.categoryLevel4ValueFinal
          })
        )
      }
      if (This.categoryLevel4ValueFinal) {
        categoryId = This.categoryLevel4ValueFinal
      } else if (This.categoryLevel3ValueFinal) {
        categoryId = This.categoryLevel3ValueFinal
      } else if (This.categoryLevel2ValueFinal) {
        categoryId = This.categoryLevel2ValueFinal
      } else if (This.categoryLevel1ValueFinal) {
        categoryId = This.categoryLevel1ValueFinal
      } else {
        This.$message.error('请选择类目')
        return false
      }
      let productGroupId = This.materialValue
      let params = {
        categoryId: categoryId,
        productGroupId: productGroupId
      }
      This.loading.submitFinal = true
      exportFinalData(params, data)
        .then((res) => {
          console.log(res)
          This.loading.submitFinal = false
          if (res.code === 0) {
            This.showExportFinalDataDialog = false
          } else {
            this.$message.error(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
          This.loading.submitFinal = false
        })
    },
    openImportDialog(state) {
      this.exportState = state
      this.checkList = []
      this.separateFlag = false
      this.exportValue = ''
      this.categoryLevel1Value = null
      this.categoryLevel2Value = null
      this.categoryLevel3Value = null
      this.categoryLevel4Value = null
      this.remarks = ''
      this.positionValue = ''
      this.brandId = ''
      this.exportCategory = ''
      this.$nextTick(() => {
        this.showImportDialog = true
      })
    },
    closeImportDialog() {
      this.showImportDialog = false
    },
    openExportFinalDataDialog(state) {
      this.showExportFinalDataDialog = true
    },
    closeExportFinalDataDialog() {
      this.showExportFinalDataDialog = false
    },
    exportClassifyChoose(value) {
      // 获取复选框问题分类的值
      console.log('复选值')
      console.log(this.checkList)
    },
    goGetExportList() {
      let This = this
      let param = {
        page: This.listQueryInfo.current - 1,
        size: This.listQueryInfo.pageSize,
        name: This.createUser,
        sort: 'id,desc' // id的倒序 asc时正序
      }
      getExportList(param).then((res) => {
        console.log(res)
        if (res.code === 0) {
          This.loading.table = false
          This.loading.page = false
          This.templateList = res.data.content
          This.total = res.data.totalElements
        } else {
          This.$message.error(res.msg)
        }
      })
    },
    categorySelect(level) {
      // 树形目录变化时触发
      console.log('level=' + level)
      let This = this
      if (level === 1) {
        let plist = this.categoryListLevel1
        let id = this.categoryLevel1Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.categoryListLevel2 = plist[i].catalogueTreeVOList
            This.categoryListLevel3 = null
            This.categoryListLevel4 = null
            This.categoryLevel2Value = ''
            This.categoryLevel3Value = ''
            This.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 2) {
        let plist = this.categoryListLevel2
        let id = this.categoryLevel2Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.categoryListLevel3 = plist[i].catalogueTreeVOList
            This.categoryListLevel4 = null
            This.categoryLevel3Value = ''
            This.categoryLevel4Value = ''
            return false
          }
        }
      } else if (level === 3) {
        let plist = this.categoryListLevel3
        let id = this.categoryLevel3Value
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.categoryListLevel4 = plist[i].catalogueTreeVOList
            This.categoryLevel4Value = ''
            return false
          }
        }
      }
    },
    categorySelectFinal(level) {
      // 树形目录变化时触发(定稿)
      console.log('level=' + level)
      let This = this
      if (level === 1) {
        let plist = this.categoryListLevel1Final
        let id = this.categoryLevel1ValueFinal
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.categoryListLevel2Final = plist[i].catalogueTreeVOList
            This.categoryListLevel3Final = null
            This.categoryListLevel4Final = null
            This.categoryLevel2ValueFinal = ''
            This.categoryLevel3ValueFinal = ''
            This.categoryLevel4ValueFinal = ''
            return false
          }
        }
      } else if (level === 2) {
        let plist = this.categoryListLevel2Final
        let id = this.categoryLevel2ValueFinal
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.categoryListLevel3Final = plist[i].catalogueTreeVOList
            This.categoryListLevel4Final = null
            This.categoryLevel3ValueFinal = ''
            This.categoryLevel4ValueFinal = ''
            return false
          }
        }
      } else if (level === 3) {
        let plist = this.categoryListLevel3Final
        let id = this.categoryLevel3ValueFinal
        for (let i = 0; i < plist.length; i++) {
          if (plist[i].id === id) {
            This.categoryListLevel4Final = plist[i].catalogueTreeVOList
            This.categoryLevel4ValueFinal = ''
            return false
          }
        }
      }
    },
    sureImportCatalog() {
      exportCategoryAdjustSku({
        exportName: window.CUR_DATA.user?.name || ''
      }, {
        categoryId: this.categoryLevel4Value || this.categoryLevel3Value || this.categoryLevel2Value || this.categoryLevel1Value || ''
      }).then(res => {
        const { success = false, msg = '' } = res
        if (!success) {
          this.$message.error(msg || '操作失败')
          return
        }
        this.goGetExportList()
        this.closeImportDialog()
      }).catch(e => {
        console.warn(`导出调整类目数据异常：${e}`)
      })
    },
    handleSave() {
      const fetch = {
        data: () => {
          this.sureImportData()
        },
        rule: () => {
          this.sureImportRule()
        },
        catalog: () => {
          this.sureImportCatalog()
        }
      }
      fetch[this.exportState] && fetch[this.exportState]()
    },
    async qryPositionOpts() {
      const { data = {} } = await queryEnum({
        typeCodes: 'catalogPosition'
      })
      this.positionOpts = data?.catalogposition || []
    },
    async getEntityList(type = '', name = '') {
      const fetchApi = {
        brand: getCCEntityTypes
      }
      const fetchParams = {
        brand: () => {
          return {
            entityType: 'entity.brand',
            name
          }
        }
      }
      let params = (fetchParams[type] && fetchParams[type]()) || {}
      let { data = [] } = await fetchApi[type](params)
      this.option[type] = data
    }
  }
}
</script>
<style lang="scss" scoped>
.data-manage-import-container{
  min-height: 500px;
  .data-manage-import-center{
    border: 1px solid #eee;
    padding-top: 10px;
    .data-manage-import-add{
      padding: 0 20px 10px;
      overflow: hidden;
      .importtitle{
        float: left;
        font-size: 18px;
        line-height: 32px;
      }
      .importbtn{
        float: right;
      }
      .ellipse2 {
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        cursor: default;
        max-height:46px;
      }
    }
  }
}
.dialogTitle{
  line-height: 32px;
}
.importset{
  .el-row{
    margin-bottom: 15px;
  }
  .mg-b-10{
    display: inline-block;
    margin-bottom: 10px;
  }
}
</style>
