<template>
  <div class="app-container stockpile-strategy-history">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row type="flex" justify="space-between">
          <el-col>
            <el-col :span="8">
              <el-form-item label="品牌：">
                <el-select
                  v-model="searchForm.brandNoList"
                  filterable
                  clearable
                  multiple
                  placeholder="请输入关键词"
                >
                  <el-option
                    v-for="item in brandOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="物料组：">
                <el-select
                  v-model="searchForm.materialGroupNoList"
                  filterable
                  clearable
                  multiple
                  placeholder="请输入关键词"
                >
                  <el-option
                    v-for="item in materialGroupOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="search-row" label="SKU：">
                <el-input
                  v-model="searchForm.skuNoList"
                  placeholder="最多支持200个，以空格分隔"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-col>
          <el-col class="align-right" :span="6">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
          </el-col>
        </el-row>
        <el-row v-if="isStockStaff">
          <el-col class="mb-10 align-right">
            <el-button type="primary" @click="handleDelete">批量删除</el-button>
            <el-button
              type="primary"
              @click="handleDownload"
              :loading="exportLoading"
              >下载</el-button
            >
            <el-button type="primary" @click="openBlacklistDialog"
              >新建黑名单</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <el-table
        ref="multipleTable"
        v-loading="basisListLoading"
        :data="basisList"
        @selection-change="tableSelectionChange"
        border
        fit
        height="500"
      >
        <el-table-column type="selection" width="40" fixed="left" />
        <el-table-column
          prop="brandName"
          min-width="120"
          :show-overflow-tooltip="true"
          label="产品经理备货品牌"
          align="center"
        />
        <el-table-column
          prop="materialGroup"
          min-width="80"
          label="物料组"
          align="center"
        />
        <el-table-column
          prop="skuNo"
          label="SKU"
          min-width="80"
          align="center"
        />
        <el-table-column
          prop="skuDesc"
          label="SKU描述"
          min-width="120"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          prop="remark"
          min-width="80"
          label="备注"
          align="center"
        />
        <el-table-column
          prop="updateTimeStr"
          min-width="80"
          label="保存日期"
          align="center"
        />
        <el-table-column
          v-if="isStockStaff"
          label="操作"
          align="center"
          fixed="right"
          class-name="small-padding"
        >
          <template slot-scope="{ row }">
            <el-button type="text" size="mini" @click="deleteItem(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
    <el-dialog
      width="800px"
      title="新建备货物料黑名单"
      class="blacklist-dialog"
      :visible.sync="isOpenBlacklistDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="closeBlacklistDialog"
    >
      <el-form
        ref="blacklistForm"
        :model="blacklistForm"
        :rules="blacklistFormRules"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-form-item label="品牌：" prop="brandNo">
            <RemoteBrand ref="remoteBrand" v-model="blacklistForm.brandNo" />
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item label="物料组：" prop="materialGroupNo">
            <MaterialGroup
              ref="materialGroup"
              v-model="blacklistForm.materialGroupNo"
          /></el-form-item>
        </el-row>
        <el-row>
          <el-form-item class="search-row" label="SKU：" prop="skuNo">
            <el-select
              class="search-input"
              v-model="blacklistForm.skuNo"
              filterable
              clearable
              remote
              value-key="skuNo"
              placeholder="请输入商品编号/名称"
              :remote-method="searchSkuList"
              :loading="skuList.isLoading"
            >
              <el-option
                v-for="(item, index) in skuList.options"
                :key="item.skuNo"
                :label="'【' + item.skuNo + '】' + item.materialDescribe"
                :value="item.skuNo"
                :disabled="index === 0"
              >
                <div
                  class="ba-row-start selectSkuItem"
                  :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
                >
                  <div>{{ item.skuNo }}</div>
                  <div>{{ `${item.materialDescribe || ""}` }}</div>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item class="search-row" label="备注：" prop="remark">
            <el-input
              type="textarea"
              :rows="3"
              v-model="blacklistForm.remark"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <footer class="align-right">
        <el-button @click="closeBlacklistDialog">取消</el-button>
        <el-button @click="submitData" type="primary">确定</el-button>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import RemoteBrand from '@/components/SearchFields/brand';
import MaterialGroup from '@/components/SearchFields/materialGroup';
import Pagination from '@/components/Pagination';
import { searchSkuList } from '@/api/orderSale';
import { get } from 'lodash';
import {
  addBlacklist,
  deleteRow,
  deleteBatched,
  exportBlacklistBasisApi,
  getBlacklistBasisList,
  getBlacklistBrandOptions,
  getBlacklistMaterialGroupOptions
} from '@/api/stockpile';

export default {
  name: 'Blacklist',
  data() {
    return {
      basisListLoading: false,
      isOpenBlacklistDialog: false,
      exportLoading: false,
      isSubmitted: false,
      basisList: [],
      brandOptions: [],
      materialGroupOptions: [],
      multipleSelection: [],
      blacklistForm: {
        brandNo: '',
        materialGroupNo: '',
        skuNo: '',
        remark: ''
      },
      searchForm: {
        brandNoList: [],
        materialGroupNoList: [],
        skuNoList: ''
      },
      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      total: 0,
      skuList: {}
    };
  },
  components: {
    Pagination,
    RemoteBrand,
    MaterialGroup
  },
  created() {
    Promise.all([
      getBlacklistBrandOptions().then((data) => {
        this.brandOptions = data;
      }),
      getBlacklistMaterialGroupOptions().then((data) => {
        this.materialGroupOptions = data;
      })
    ]);
    this.handleFilter();
  },
  computed: {
    ...mapState(['userRole']),
    isStockStaff() {
      return !!~this.userRole.indexOf('data-库存策略');
    },
    blacklistFormRules() {
      if (!this.isSubmitted) {
        return null;
      }
      return {
        brandNo: [
          { required: true, message: '品牌不能为空！', trigger: 'change' }
        ],
        materialGroupNo: [
          { required: true, message: '物料组不能为空！', trigger: 'change' }
        ]
      };
    }
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    prepareSearchParam() {
      let param = { ...this.searchForm };
      if (param.skuNoList.split) {
        param.skuNoList = param.skuNoList.split(/\s+|,|，/).filter(Boolean);
      }
      return param;
    },
    async handleDelete() {
      if (!this.multipleSelection.length) {
        return;
      }
      try {
        const res = await deleteBatched(
          this.multipleSelection.map((item) => item.id)
        );
        if (res.code === 200) {
          this.$message.success(res.msg || '删除成功！');
          this.handleFilter();
        } else {
          this.$message.error({
            message: res.msg || res.message || '删除失败！',
            duration: 6000
          });
        }
      } catch (err) {
        this.$message.error({
          message: err.msg || err.message || '删除失败！',
          duration: 6000
        });
      }
    },
    handleDownload() {
      let param = this.prepareSearchParam();
      this.exportLoading = true;
      exportBlacklistBasisApi({ ...param })
        .catch((err) => {
          this.$message.error({
            message: err.msg || err.message || '下载失败！',
            duration: 6000
          });
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    searchSkuList(search) {
      this.skuList = { isLoading: true };
      let params = search;
      searchSkuList(params).then((result) => {
        if (result.code === 200) {
          if (Array.isArray(result.data) && result.data.length > 0) {
            this.skuList = {
              options: [
                {
                  skuNo: '商品编号',
                  materialDescribe: '商品描述'
                },
                ...result.data
              ]
            };
          }
        } else {
          this.$message.error({
            message: result.msg,
            duration: 6000
          });
        }
        this.skuList.isLoading = false;
      });
    },
    getBasisList() {
      let param = this.prepareSearchParam();
      param.current = this.listQueryInfo.current;
      param.pageSize = this.listQueryInfo.pageSize;
      this.basisList = [];
      this.basisListLoading = true;
      getBlacklistBasisList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              const data = res.data;
              this.total = res.totalCount;
              this.basisList = data;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    tableSelectionChange(val) {
      this.multipleSelection = val;
    },
    deleteItem(row) {
      deleteRow(row.id).then((res) => {
        if (res.code === 200) {
          this.$message.success(res.msg || '删除成功！');
          this.handleFilter();
        } else {
          this.$message.error({
            message: res.msg || res.message || '删除失败！',
            duration: 6000
          });
        }
      });
    },
    openBlacklistDialog() {
      this.isOpenBlacklistDialog = true;
    },
    closeBlacklistDialog() {
      this.isOpenBlacklistDialog = false;
      this.isSubmitted = false;
      this.$refs['blacklistForm'].clearValidate();
    },
    submitData() {
      this.isSubmitted = true;
      this.$nextTick(() => {
        let param = { ...this.blacklistForm };
        this.$refs['blacklistForm'].validate((valid) => {
          if (valid) {
            const brandName = get(
              this.$refs['remoteBrand'].brandIdOptions.find(
                (item) => item.value === param.brandNo
              ),
              'label'
            );
            param.brandName = brandName;

            const materialGroup = get(
              this.$refs['materialGroup'].productGroupIdOptions.find(
                (item) => item.value === param.materialGroupNo
              ),
              'label'
            );
            param.materialGroup = materialGroup;

            const skuDesc = get(
              (this.skuList.options || []).find(
                (item) => item.skuNo === param.skuNo
              ),
              'materialDescribe'
            );
            param.skuDesc = skuDesc;
            addBlacklist(param)
              .then((res) => {
                if (res.code === 200) {
                  this.$message.success(res.msg || '添加成功！');
                  this.closeBlacklistDialog();
                  this.handleFilter();
                } else {
                  this.$message.error({
                    message: res.msg || res.message || '添加失败！',
                    duration: 6000
                  });
                }
              })
              .finally(() => {
                this.isSubmitted = false;
                this.$refs['blacklistForm'].clearValidate();
              });
          }
        });
      });
    }
  }
};
</script>

<style lang="scss">
.stockpile-strategy-history {
  .tabelCellError {
    input {
      border: 1px solid #ff4949;
    }
    &:hover {
      input {
        border: 1px solid #ff4949;
      }
    }
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }
  .justify-between {
    justify-content: space-between;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .fw-500 {
    font-weight: 500;
  }
  .primary-blue {
    color: #7a95f1;
  }
  .align-right {
    text-align: right;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
}
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
