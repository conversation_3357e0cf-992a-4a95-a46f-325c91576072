<template>
  <div class="list-container">
    <el-form :model="searchForm" :rules="rules" ref="ruleForm" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="物料组"
            prop="materialGroupId"
            required>
            <el-select v-model="searchForm.materialGroupId"
              filterable
              default-first-option
              clearable
              style="width:100%">
              <el-option v-for="item in productList"
                :key="item.productGroupNum+Math.random()"
                :label="item.productGroupNum+ ' '+item.productGroupName"
                :value="item.productGroupId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="品牌" prop="brandId">
            <SelectBrand clearable
              :data.sync="searchForm.brands"
              @change="handleChange('brands', $event)" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品来源" prop="commoditySourceTypeName">
            <el-select
              v-model="searchForm.commoditySourceTypeName"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in commoditySourceTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="SKU编码" prop="skuNos">
            <el-input
              v-model="searchForm.skuNos"
              placeholder="最多支持100个SKU按空格隔开搜索"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品定位" prop="productPositioningName">
            <el-select
              v-model="searchForm.productPositioningName"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in productPositioningNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :offset="16" :span="8" :style="{display: 'flex', justifyContent: 'flex-end'}">
          <el-button type="primary"
            :loading="searchLoading"
            @click="handleSearch">
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <vxe-grid
      border
      stripe
      resizable
      auto-resize
      keep-source
      show-overflow
      ref="approvalGrid"
      height="640"
      id="approval_grid"
      row-id="id"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="listData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}"
      :checkbox-config="{trigger: 'row', highlight: true}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
      highlight-hover-row
      header-cell-class-name="headerCellClass">

      <template v-slot:toolbar_tools>
        <el-dropdown style="margin-right: 10px;">
          <el-button size="mini" type="default">
            模板下载<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-button type="text" size="mini"  @click="handleDownloadTemplateHeader('ab')">
                导入备货类型模版下载
              </el-button>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-button type="text" size="mini"  @click="handleDownloadTemplateHeader('dock')">
                导入库存对接模版下载
              </el-button>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown style="margin-right: 10px;">
          <el-button size="mini" type="primary">
            导入清单<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-upload
                ref="upload"
                style="display:inline-block;margin:0 10px"
                accept=".xlsx"
                :before-upload="$validateFileType"
                action="/api-ab/sku-inventory/operating-tool/business/operator/inventory-parameter/sku-list"
                :auto-upload="false"
                :file-list="fileList"
                :on-change="(file, fleList) => handleImportChange(file, fleList, 'ab')"
                :show-file-list="false"
                :multiple="false"
              >
                <el-button type="text" size="mini">导入需修改备货类型</el-button>
              </el-upload>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-upload
                ref="upload"
                style="display:inline-block;margin:0 10px"
                accept=".xlsx"
                :before-upload="$validateFileType"
                action=""
                :auto-upload="false"
                :file-list="fileList"
                :on-change="(file, fleList) => handleImportChange(file, fleList, 'dock')"
                :show-file-list="false"
                :multiple="false"
              >
                <el-button type="text" size="mini">导入需修改库存对接清单</el-button>
              </el-upload>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button type="primary" size="mini" plain  @click="handleDownExcel" :disabled="listData.length === 0">
          导出需审核清单
        </el-button>

        <el-upload
          ref="upload"
          style="display:inline-block;margin:0 10px"
          accept=".xlsx"
          action="/api-ab/sku-inventory/operating-tool/business/operator/inventory-parameter/sku-list"
          :before-upload="$validateFileType"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="handleImport"
          :show-file-list="false"
          :multiple="false"
        >
          <el-button type="primary" size="mini">导入审核结果</el-button>
        </el-upload>

        <el-button type="primary" size="mini" @click="handleAllCount" :disabled="!disabled">
          参数全量计算
        </el-button>
        <el-button type="primary" size="mini" @click="handleCount" :disabled="disabled">
          参数计算
        </el-button>
        <el-button type="primary" size="mini" @click="handleAudit" :disabled="disabled">
          审核完成
        </el-button>
      </template>

      <template v-slot:dockedStatus_default="{ row }">
        {{ row.dockedStatus ? '是' : '否' }}
      </template>

      <template #originalDecisionSource_default="{ row }">
        {{ (DESICION_SOURCE.find(item => item.value === row.originalDecisionSource) || {}).label }}
      </template>

      <template #originalInventoryStatus_default="{ row }">
        {{ row.originalInventoryStatus ? '备货' : '不备货' }}
      </template>

      <template #currentDecisionSource_default="{ row }">
        {{ (DESICION_SOURCE.find(item => item.value === row.currentDecisionSource) || {}).label }}
      </template>

      <template #calculatedInventoryStatus_default="{ row }">
        {{ row.calculatedInventoryStatus ? '备货' : '不备货' }}
      </template>

      <template #currentInventoryStatus_default="{ row }">
        <el-select
          v-model="row.currentInventoryStatus"
          :disabled="!row.inventoryStatusEditable"
          >
          <el-option label="备货" :value="true"></el-option>
          <el-option label="不备货" :value="false"></el-option>
        </el-select>
      </template>

      <template #currentRop_default="{ row }">
        <el-input-number
          :disabled="!row.reorderParameterEditable"
          v-model="row.currentRop"
          :min="0"
          style="width: 100%"
          >
        </el-input-number>
      </template>

      <template #currentRoq_default="{ row }">
        <el-input-number
          :disabled="!row.reorderParameterEditable"
          v-model="row.currentRoq"
          :min="0"
          style="width: 100%"
          >
        </el-input-number>
      </template>

    </vxe-grid>

    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="table.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="table.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="table.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectBrand from '@/pages/mrp/components/selectBrand'
import { safeRun } from '@/utils/index'
import { COMMODITY_SOURCETYPE, DESICION_SOURCE, productPositioningNameList } from './utils'
import { getABStockList, getDownloadTemplate, handelCheck, ImportSkuList, getAllDownload, handelSkuList, handelCalculate, completedAudit } from '@/api/ab-configuration'

const columns = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left'
  },
  {
    title: 'sku基础信息',
    children: [
      {
        field: 'factory',
        title: '工厂',
        minWidth: 80
      },
      {
        field: 'productGroupName',
        title: '物料组',
        minWidth: 80
      },
      {
        field: 'brandName',
        title: '品牌',
        minWidth: 80
      },
      {
        field: 'manufacturerMaterialNo',
        title: '制造商型号',
        minWidth: 90
      },
      {
        field: 'commoditySourceTypeName',
        title: '商品来源',
        minWidth: 80
      },
      {
        field: 'productPosition',
        title: '产品定位',
        minWidth: 80
      },
      {
        field: 'dockedStatus',
        title: '是否对接库存',
        minWidth: 100,
        slots: {
          default: 'dockedStatus_default'
        }
      },
      {
        field: 'sku',
        title: 'sku编码',
        minWidth: 80
      },
      {
        field: 'productName',
        title: '产品名称',
        minWidth: 80
      },
      {
        field: 'supplyNetWorkRuleName',
        title: '供应网络',
        minWidth: 80
      },
      {
        field: 'khWarehouseCode',
        title: '仓库编码',
        minWidth: 80
      },
      {
        field: 'khWarehouseName',
        title: '仓库名称',
        minWidth: 80
      },
      {
        field: 'mrpScope',
        title: 'mrp编码',
        minWidth: 80
      }
    ]
  },
  {
    title: '原有A类配置信息(不允许编辑)',
    children: [
      {
        field: 'originalGmtModified',
        title: '最近更新日期',
        minWidth: 100
      },
      {
        field: 'originalDecisionSource',
        title: '备货决策来源',
        minWidth: 100,
        slots: {
          default: 'originalDecisionSource_default'
        }
      },
      {
        field: 'originalInventoryStatus',
        title: '备货状态',
        minWidth: 80,
        slots: {
          default: 'originalInventoryStatus_default'
        }
      },
      {
        field: 'originalInventoryType',
        title: 'sku归类',
        minWidth: 80
      },
      {
        field: 'originalRop',
        title: 'ROP',
        minWidth: 80
      },
      {
        field: 'originalRoq',
        title: 'ROQ',
        minWidth: 80
      }
    ]
  },
  {
    title: '修改建议(不允许编辑)',
    children: [
      {
        field: 'currentGmtCreate',
        title: '发起修改日期',
        minWidth: 100
      },
      {
        field: 'currentRoleCreate',
        title: '发起修改用户',
        minWidth: 100
      },
      {
        field: 'currentDecisionSource',
        title: '备货决策来源',
        minWidth: 100,
        slots: {
          default: 'currentDecisionSource_default'
        }
      },
      {
        field: 'calculatedInventoryStatus',
        title: '备货状态',
        minWidth: 80,
        slots: {
          default: 'calculatedInventoryStatus_default'
        }
      },
      {
        field: 'calculatedRop',
        title: 'ROP',
        minWidth: 80
      },
      {
        field: 'calculatedRoq',
        title: 'ROQ',
        minWidth: 80
      }
    ]
  },
  {
    title: '产线审核(允许编辑)',
    children: [
      {
        field: 'currentInventoryStatus',
        title: '备货状态',
        minWidth: 120,
        slots: {
          default: 'currentInventoryStatus_default'
        }
      },
      {
        field: 'currentRop',
        title: 'ROP',
        minWidth: 130,
        slots: {
          default: 'currentRop_default'
        }
      },
      {
        field: 'currentRoq',
        title: 'ROQ',
        minWidth: 150,
        slots: {
          default: 'currentRoq_default'
        }
      }
    ]
  }
]

export default {
  name: 'approvalList',
  components: {
    SelectBrand
  },
  data () {
    return {
      commoditySourceTypeList: COMMODITY_SOURCETYPE,
      productPositioningNameList,
      DESICION_SOURCE,
      searchForm: {
        materialGroupId: '',
        brandId: '',
        brands: {},
        commoditySourceTypeName: '',
        productPositioningName: '',
        skuNos: ''
      },
      table: {
        pageNo: 1,
        total: 0,
        pageSize: 10
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          tools: 'toolbar_tools'
        }
      },
      columns,
      listData: [],
      loading: null,
      fileList: [],
      file: '',
      selectList: [],
      disabled: true,
      rules: {
        materialGroupId: [
          { required: true, message: '物料组不能为空！', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  async created () {
    const pList = []
    if (this.productList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryProductGroup'))
    }
    await Promise.all(pList)
  },
  computed: {
    ...mapState({
      productList: state => state.mrp.productList
    })
  },
  methods: {
    validSearch (callback) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          callback && callback()
        }
      })
    },
    handleSizeChange (size) {
      this.table.pageSize = size
      this.validSearch(this.getABStockAuditList)
    },
    handleCurrentChange (pageNo) {
      this.table.pageNo = pageNo
      this.validSearch(this.getABStockAuditList)
    },
    handleChange (type, event) {
      if (type === 'brands') {
        this.searchForm.brandId = event.brandId
      }
    },
    formatParams(params) {
      let form = { ...params };
      form.skuNos = safeRun(() =>
        form.skuNos
          .split(/\s+|,|，/).filter((e) => e)
      );
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.skuNos.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
      })
      return ret
    },
    async getABStockAuditList () {
      try {
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return
        this.tableLoading = true
        this.searchLoading = true
        if (Array.isArray(params.skuNos)) {
          params.skuNos = params.skuNos.join(',')
        }
        params = {
          ...params,
          pageNo: this.table.pageNo
        }
        const res = await getABStockList(params)
        if (res.code === 200) {
          this.listData = res.data
          this.table.total = res.total
          if (this.listData.length === 0) {
            this.$message.info('没有符合条件的AB配置审批')
          }
        } else {
          this.$message.error(res.msg || res.message || '请求失败！')
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
        this.$message.error(error.msg || error.message || '请求失败！')
      }
    },
    handleSearch (key) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (key !== 'handleCount') {
            this.table.pageNo = 1
          }
          this.getABStockAuditList()
          this.selectList = []
          this.disable()
        } else {
          return false;
        }
      })
    },
    async handleDownloadTemplateHeader (type) {
      try {
        const res = await getDownloadTemplate(type)
        window.open(res)
      } catch (error) {
        console.log(error);
      }
    },
    handleImportChange (file, fileList, type) {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      this.fileList = [fileList[fileList.length - 1]]; // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file.raw;
      let reader = new FileReader()
      reader.readAsArrayBuffer(this.file)
      reader.onload = () => {
        let buffer = reader.result
        let bytes = new Uint8Array(buffer)
        let length = bytes.byteLength
        let binary = ''
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        let XLSX = require('xlsx')
        let wb = XLSX.read(binary, {
          type: 'binary'
        })
        let outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        console.log(outdata)
        if (Array.isArray(outdata) && Object.keys(outdata[0]).length > 3) {
          this.loading.close()
          return this.$message.error('请下载新模板上传！')
        }
        outdata.splice(0, 1)
        this.importSkuError(outdata, type)
      }
    },
    importSkuError (outdata, type) {
      outdata = outdata.filter(item =>
        !(item.factory === undefined && item.sku === undefined && item.inventoryType === undefined && item.dockedStatus === undefined))

      let alertFactoryArr = []
      let alertInventoryTypeArr = []
      let alertDockedStatusArr = []
      for (let i = 0; i < outdata.length; i++) {
        if (type === 'ab') {
          if (!(outdata[i].factory && outdata[i].sku && outdata[i].inventoryType)) {
            this.loading.close()
            return this.$message.error('备货类型模板必填项不能为空')
          }
          if (!(outdata[i].inventoryType === 'A1' || outdata[i].inventoryType === 'A2' || outdata[i].inventoryType === 'B')) {
            alertInventoryTypeArr.push(outdata[i].sku)
          }
        }
        if (type === 'dock') {
          if (!(outdata[i].factory && outdata[i].sku && outdata[i].dockedStatus)) {
            this.loading.close()
            return this.$message.error('库存对接模板必填项不能为空')
          }
          if (!(outdata[i].dockedStatus === '是' || outdata[i].dockedStatus === '否')) {
            alertDockedStatusArr.push(outdata[i].sku)
          }
        }
        if (outdata[i].factory !== 1000 && outdata[i].factory !== '1000') {
          alertFactoryArr.push(outdata[i].sku)
        }
        if (outdata[i].dockedStatus === '是') {
          outdata[i].dockedStatus = true
        }
        if (outdata[i].dockedStatus === '否') {
          outdata[i].dockedStatus = false
        }
      }
      if (alertFactoryArr.length > 0 || alertInventoryTypeArr.length > 0 || alertDockedStatusArr.length > 0) {
        this.loading.close()
        const content = {
          message: `${alertFactoryArr.length > 0 ? `工厂填写错误，请确认，SKU清单：${alertFactoryArr.join(' ')}` : ''}
            ${alertInventoryTypeArr.length > 0 ? `备货类型写法错误，请按照标准写法：A1,A2,B，SKU清单：${alertInventoryTypeArr.join(' ')}` : ''}
            ${alertDockedStatusArr.length > 0 ? `是否库存对接写法错误，请按照标准写法：是,否；sku清单：${alertDockedStatusArr.join(' ')}` : ''}`
        }
        return this.$confirm(content.message, '操作提示', {
          type: 'error',
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          showClose: true
        })
      }
      this.preCheck(outdata, type)
    },
    async abPreCheck (outdata, type) {
      try {
        const data = {
          skuChangedInventoryTypeList: outdata
        };
        const res = await handelCheck(data, type)
        const withoutCategoryRuleOrMdmInfoResult = (res.withoutCategoryRuleOrMdmInfoResult && res.withoutCategoryRuleOrMdmInfoResult.results) || []
        const productGroupWithoutAuthResult = (res.productGroupWithoutAuthResult && res.productGroupWithoutAuthResult.results) || []
        const notNeedOperateAndSetToNotStockResult = (res.notNeedOperateAndSetToNotStockResult && res.notNeedOperateAndSetToNotStockResult.results) || []
        if ((notNeedOperateAndSetToNotStockResult && notNeedOperateAndSetToNotStockResult.length > 0) || (withoutCategoryRuleOrMdmInfoResult && withoutCategoryRuleOrMdmInfoResult.length > 0) || (productGroupWithoutAuthResult && productGroupWithoutAuthResult.length > 0)) {
          const content = {
            message: `${notNeedOperateAndSetToNotStockResult.length > 0 ? `${res.notNeedOperateAndSetToNotStockResult.msg}：${notNeedOperateAndSetToNotStockResult.join(' ')}` : ''}
            ${withoutCategoryRuleOrMdmInfoResult.length > 0 ? `${res.withoutCategoryRuleOrMdmInfoResult.msg}：${withoutCategoryRuleOrMdmInfoResult.join(' ')}` : ''}
            ${productGroupWithoutAuthResult.length > 0 ? `${res.productGroupWithoutAuthResult.msg}：${productGroupWithoutAuthResult.join(' ')}` : ''}`
          }
          return this.$confirm(content.message, '操作提示', {
            type: 'error',
            dangerouslyUseHTMLString: true,
            showCancelButton: false
          })
        }
        this.ImportSkuListOperator(data, type)
      } catch (error) {
        console.log(error);
      } finally {
        this.loading.close();
      }
    },
    async dockPreCheck (outdata, type) {
      try {
        const data = [...outdata];
        const res = await handelCheck(data, type)
        const notNeedOperateAndSetToNotStockResult = (res.notNeedOperateAndSetToNotStockResult && res.notNeedOperateAndSetToNotStockResult.results) || []
        const notConfirmedSkuResult = (res.notConfirmedSkuResult && res.notConfirmedSkuResult.results) || []
        const withoutCategoryRuleOrMdmInfoResult = (res.withoutCategoryRuleOrMdmInfoResult && res.withoutCategoryRuleOrMdmInfoResult.results) || []
        const productGroupWithoutAuthResult = (res.productGroupWithoutAuthResult && res.productGroupWithoutAuthResult.results) || []
        if ((notNeedOperateAndSetToNotStockResult && notNeedOperateAndSetToNotStockResult.length > 0) || (notConfirmedSkuResult && notConfirmedSkuResult.length > 0) || (withoutCategoryRuleOrMdmInfoResult && withoutCategoryRuleOrMdmInfoResult.length > 0) || (productGroupWithoutAuthResult && productGroupWithoutAuthResult.length > 0)) {
          this.loading.close()
          const content = {
            message: `${notNeedOperateAndSetToNotStockResult.length > 0 ? `${res.notNeedOperateAndSetToNotStockResult.msg}：${notNeedOperateAndSetToNotStockResult.join(' ')}` : ''}
            ${notConfirmedSkuResult.length > 0 ? `${res.notConfirmedSkuResult.msg}：${notConfirmedSkuResult.join(' ')}` : ''} 
            ${withoutCategoryRuleOrMdmInfoResult.length > 0 ? `${res.withoutCategoryRuleOrMdmInfoResult.msg}：${withoutCategoryRuleOrMdmInfoResult.join(' ')}` : ''}
            ${productGroupWithoutAuthResult.length > 0 ? `${res.productGroupWithoutAuthResult.msg}：${productGroupWithoutAuthResult.join(' ')}` : ''}`
          }
          return this.$confirm(content.message, '操作提示', {
            type: 'error',
            dangerouslyUseHTMLString: true,
            showCancelButton: false
          })
        }
        this.ImportSkuListOperator(data, type)
      } catch (error) {
        console.log(error);
      } finally {
        this.loading.close();
      }
    },
    async preCheck (outdata, type) {
      switch (type) {
        case 'ab':
          this.abPreCheck(outdata, type); break;
        case 'dock':
          this.dockPreCheck(outdata, type); break;
        default:
      }
    },
    async ImportSkuListOperator (data, type) {
      try {
        const res = await ImportSkuList(data, type)
        this.$message.success(res)
      } catch (error) {
        console.log(error);
      }
    },
    async handleDownExcel () {
      try {
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return
        this.loading = this.$loading({
          background: 'rgba(0, 0, 0, 0.5)'
        })
        if (Array.isArray(params.skuNos)) {
          params.skuNos = params.skuNos.join(',')
        }
        params = {
          ...params,
          pageNo: this.table.pageNo
        }
        const res = await getAllDownload(params)
        window.open(res)
        this.loading.close()
      } catch (error) {
        console.log(error);
        this.loading.close()
      }
    },
    handleImport (file, fileList) {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      this.fileList = [fileList[fileList.length - 1]]; // 只能上传一个Excel，重复上传会覆盖之前的
      this.file = file.raw;
      let reader = new FileReader()
      reader.readAsArrayBuffer(this.file)
      reader.onload = async () => {
        let buffer = reader.result
        let bytes = new Uint8Array(buffer)
        let length = bytes.byteLength
        let binary = ''
        for (let i = 0; i < length; i++) {
          binary += String.fromCharCode(bytes[i])
        }
        let XLSX = require('xlsx')
        let wb = XLSX.read(binary, {
          type: 'binary'
        })
        let outdata = XLSX.utils.sheet_to_json(wb.Sheets[wb.SheetNames[0]])
        outdata.splice(0, 1)

        try {
          outdata = outdata.map(item => {
            item.modifiedInventoryStatus = !!(item.currentInventoryStatus === '是')
            item.modifiedRop = item.currentRop
            item.modifiedRoq = item.currentRoq
            return item
          })
          const res = await handelSkuList(outdata)
          this.$message.success(res)
          this.getABStockAuditList()
          this.loading.close()
        } catch (error) {
          this.loading.close()
          console.log(error);
        }
      }
    },
    selectChange ({ checked, row }) {
      if (!checked) {
        this.$refs.approvalGrid.setCheckboxRow(this.selectList.filter(item => item.factory === row.factory && item.sku === row.sku && item.supplyNetWorkRuleName === row.supplyNetWorkRuleName), checked);
        this.reelected(checked, row)
      } else {
        this.reelected(checked, row)
        this.$refs.approvalGrid.setCheckboxRow(this.selectList, checked)
      }
    },
    reelected (checked, row) {
      if (checked) {
        this.selectList.push(...this.listData.filter(item => item.factory === row.factory && item.sku === row.sku && item.supplyNetWorkRuleName === row.supplyNetWorkRuleName))
      } else if (!checked && row) {
        this.selectList = this.selectList.filter(item => !(item.factory === row.factory && item.sku === row.sku && item.supplyNetWorkRuleName === row.supplyNetWorkRuleName))
      }
      this.disable()
    },
    selectAll ({ records }) {
      this.selectList = records
      this.disable()
    },
    disable () {
      if (this.selectList.length > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    async handleCount () {
      try {
        const data = {
          skuInventoryBasicParameterList: this.selectList.map(item => {
            item.modifiedInventoryStatus = item.currentInventoryStatus
            item.modifiedRop = item.currentRop
            item.modifiedRoq = item.currentRoq
            return item
          }),
          triggerTotal: false
        }
        const res = await handelCalculate(data)
        this.$message.success(res)
        this.handleSearch('handleCount')
      } catch (error) {
        console.log(error);
      }
    },
    async handleAllCount () {
      try {
        const data = {
          skuInventoryBasicParameterList: [],
          triggerTotal: true
        }
        const res = await handelCalculate(data)
        this.$message.success(res)
        this.handleSearch()
      } catch (error) {
        console.log(error);
      }
    },
    async handleAudit () {
      try {
        const data = {
          skuInventoryBasicParameterList: this.selectList.map(item => {
            item.modifiedInventoryStatus = item.currentInventoryStatus
            item.modifiedRop = item.currentRop
            item.modifiedRoq = item.currentRoq
            return item
          }),
          triggerTotal: false
        }
        const res = await completedAudit(data)
        this.completedMessage(res)
      } catch (error) {
        console.log(error);
      }
    },
    completedMessage (res) {
      const notCalculatedResult = Array.from(new Set([...(res?.notCalculatedResult?.results || [])]))
      const reorderParamErrorResult = Array.from(new Set([...(res?.reorderParamErrorResult?.results || [])]))
      const withoutPurchasePriceResult = Array.from(new Set([...(res?.withoutPurchasePriceResult?.results || [])]))
      const reorderParamOutOfLimitResult = Array.from(new Set([...(res?.reorderParamOutOfLimitResult?.results || [])]))
      const errorMessages = []
      if (res?.notCalculatedResult?.results?.length > 0) {
        errorMessages.push(`${res?.notCalculatedResult?.msg}：${notCalculatedResult.join(' ')}`)
      }
      if (res?.reorderParamErrorResult?.results?.length > 0) {
        errorMessages.push(`${res?.reorderParamErrorResult?.msg}：${reorderParamErrorResult.join(' ')}`)
      }
      if (res?.withoutPurchasePriceResult?.results?.length > 0) {
        errorMessages.push(`${res?.withoutPurchasePriceResult?.msg}：${withoutPurchasePriceResult.join(' ')}`)
      }
      if (res?.reorderParamOutOfLimitResult?.results?.length > 0) {
        errorMessages.push(`${res?.reorderParamOutOfLimitResult?.msg}：${reorderParamOutOfLimitResult.join(' ')}`)
      }
      if (errorMessages.length) {
        const message = errorMessages.map((item, index) => {
          return `提示${index + 1}、${item}`
        }).join('<br/><br/>')
        return this.$message({
          showClose: true,
          dangerouslyUseHTMLString: true,
          type: 'error',
          message
        })
      }
      this.$message.success('审核通过')
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
.list-container {
  padding: 20px;
  margin-right: 15px;
}
.pagination{
  margin-top: 20px;
  float:right;
}
</style>
<style lang="scss">
.headerCellClass {
  border: 1px solid rgb(215, 230, 243);
}
</style>
