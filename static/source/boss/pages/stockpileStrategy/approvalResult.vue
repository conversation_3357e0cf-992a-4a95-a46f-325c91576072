<template>
  <div class="list-container">
    <el-form :model="searchForm" :rules="rules" ref="ruleForm" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="物料组"
              prop="materialGroupId"
              required>
            <el-select v-model="searchForm.materialGroupId"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option v-for="item in productList"
                :key="item.productGroupNum+Math.random()"
                :label="item.productGroupNum+ ' '+item.productGroupName"
                :value="item.productGroupId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="品牌" prop="brandId">
            <SelectBrand clearable
              :data.sync="searchForm.brands"
              @change="handleChange('brands', $event)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品来源" prop="commoditySourceTypeName">
            <el-select
              v-model="searchForm.commoditySourceTypeName"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in commoditySourceTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="SKU编码" prop="skuNos">
            <el-input
              v-model="searchForm.skuNos"
              placeholder="最多支持100个SKU按空格隔开搜索"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="产品定位" prop="productPositioningName">
            <el-select
              v-model="searchForm.productPositioningName"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in productPositioningNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :offset="16" :span="8" :style="{display: 'flex', justifyContent: 'flex-end'}">
          <el-button v-if="userRole.includes('备货管理员')" type="primary" @click="handleTransfer">转移备货参数</el-button>
          <el-button type="primary"
              :loading="searchLoading"
              @click="handleSearch">
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <vxe-grid
      border
      stripe
      resizable
      auto-resize
      keep-source
      show-overflow
      ref="approvalResultGrid"
      height="640"
      id="approvalResult_grid"
      row-id="id"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="listData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}"
      highlight-hover-row
      highlight-current-row
      @checkbox-change="checkboxChangeEvent"
      @checkbox-all="checkboxChangeEvent"
    >
      <template v-slot:dockedStatus_default="{ row }">
        {{ row.dockedStatus ? '是' : '否' }}
      </template>

      <template #currentInventoryStatus_default="{ row }">
        {{ row.currentInventoryStatus ? '备货' : '不备货' }}
      </template>

      <template #currentDecisionSource_default="{ row }">
        {{ (DESICION_SOURCE.find(item => item.value === row.currentDecisionSource) || {}).label }}
      </template>

      <template #changeTypeName_default="{ row }">
        {{ row.originalInventoryType + '->' + row.currentInventoryType }}
      </template>
    </vxe-grid>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="table.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="table.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="table.total">
      </el-pagination>
    </div>
    <TransferDlg v-if="visible" :visible="visible" :selectRecords="selectRecords" :cancel="cancel" />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectBrand from '@/pages/mrp/components/selectBrand'
import { COMMODITY_SOURCETYPE, DESICION_SOURCE, productPositioningNameList } from './utils'
import { safeRun } from '@/utils/index'
import { getABResultList } from '@/api/ab-configuration'
import TransferDlg from './components/transferDlg.vue'

const columns = [
  {
    type: 'checkbox',
    fixed: 'left',
    width: 60
  },
  {
    field: 'factory',
    title: '工厂',
    minWidth: 80
  },
  {
    field: 'productGroupName',
    title: '物料组',
    minWidth: 80
  },
  {
    field: 'brandName',
    title: '品牌',
    minWidth: 80
  },
  {
    field: 'manufacturerMaterialNo',
    title: '制造商型号',
    minWidth: 90
  },
  {
    field: 'commoditySourceTypeName',
    title: '商品来源',
    minWidth: 80
  },
  {
    field: 'productPosition',
    title: '产品定位',
    minWidth: 80
  },
  {
    field: 'dockedStatus',
    title: '是否对接库存',
    minWidth: 100,
    slots: {
      default: 'dockedStatus_default'
    }
  },
  {
    field: 'sku',
    title: 'sku编码',
    minWidth: 80
  },
  {
    field: 'productName',
    title: '产品名称',
    minWidth: 80
  },
  {
    field: 'supplyNetWorkRuleName',
    title: '供应网络',
    minWidth: 80
  },
  {
    field: 'khWarehouseCode',
    title: '仓库编码',
    minWidth: 80
  },
  {
    field: 'khWarehouseName',
    title: '仓库名称',
    minWidth: 80
  },
  {
    field: 'mrpScope',
    title: 'mrp编码',
    minWidth: 80
  },
  {
    field: 'currentInventoryStatus',
    title: '备货状态',
    minWidth: 80,
    slots: {
      default: 'currentInventoryStatus_default'
    }
  },
  {
    field: 'currentDecisionSource',
    title: '备货决策来源',
    minWidth: 100,
    slots: {
      default: 'currentDecisionSource_default'
    }
  },
  {
    field: 'currentInventoryType',
    title: 'SKU分类',
    minWidth: 80
  },
  {
    field: 'currentRop',
    title: 'ROP',
    minWidth: 80
  },
  {
    field: 'currentRoq',
    title: 'ROQ',
    minWidth: 80
  },
  {
    field: 'originalGmtModified',
    title: '最近更新日期',
    minWidth: 100
  },
  {
    field: 'changeTypeName',
    title: '更新类型',
    slots: {
      default: 'changeTypeName_default'
    },
    minWidth: 120
  }
]

export default {
  name: 'approvalResult',
  components: {
    SelectBrand,
    TransferDlg
  },
  data () {
    return {
      commoditySourceTypeList: COMMODITY_SOURCETYPE,
      productPositioningNameList,
      DESICION_SOURCE,
      searchForm: {
        materialGroupId: '',
        brandId: '',
        brands: {},
        commoditySourceTypeName: '',
        skuNos: ''
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true
      },
      table: {
        pageNo: 1,
        total: 0,
        pageSize: 10
      },
      columns,
      listData: [],
      rules: {
        materialGroupId: [
          { required: true, message: '物料组不能为空！', trigger: ['change', 'blur'] }
        ]
      },
      selectRecords: [],
      visible: false
    }
  },
  async created () {
    const pList = []
    if (this.productList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryProductGroup'))
    }
    await Promise.all(pList)
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole,
      productList: state => state.mrp.productList
    })
  },
  methods: {
    validSearch (callback) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          callback && callback()
        }
      })
    },
    handleSizeChange (size) {
      this.table.pageSize = size
      this.validSearch(this.getABStockResultList)
    },
    handleCurrentChange (pageNo) {
      this.table.pageNo = pageNo
      this.validSearch(this.getABStockResultList)
    },
    handleChange (type, event) {
      if (type === 'brands') {
        this.searchForm.brandId = event.brandId
      }
    },
    formatParams(params) {
      let form = { ...params };
      form.skuNos = safeRun(() =>
        form.skuNos
          .split(/\s+|,|，/).filter((e) => e)
      );
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.skuNos.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
      })
      return ret
    },
    async getABStockResultList () {
      try {
        if (this.searchForm.modificationDate === null) {
          this.searchForm.modificationDate = []
        }
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return

        if (Array.isArray(params.skuNos)) {
          params.skuNos = params.skuNos.join(',')
        }
        this.tableLoading = true
        this.searchLoading = true
        params = {
          ...params,
          pageNo: this.table.pageNo
        }
        const res = await getABResultList(params)
        if (res.code === 200) {
          this.listData = res.data
          this.table.total = res.total;
          if (this.listData.length === 0) {
            this.$message.info('没有符合条件的AB分类审批结果')
          }
        } else {
          this.$message.error(res.msg || res.message || '请求失败！')
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
        this.$message.error(error.msg || error.message || '请求失败！')
      }
    },
    handleSearch () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.table.pageNo = 1
          this.getABStockResultList()
        } else {
          return false;
        }
      })
    },
    checkboxChangeEvent ({ records }) {
      this.selectRecords = records
    },
    handleTransfer() {
      if (!this.selectRecords.length) {
        this.$message.info('请先选中数据！')
        return
      }
      this.visible = true
    },
    cancel(flag) {
      this.visible = false
      if (flag) {
        this.handleSearch();
        this.selectRecords = [];
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.list-container {
  padding: 20px;
  margin-right: 15px;
}
.pagination{
  margin-top: 20px;
  float:right;
}
</style>
