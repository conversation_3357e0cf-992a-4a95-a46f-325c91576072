<template>
  <div class="list-container">
    <el-form :model="searchForm" :rules="rules" ref="ruleForm" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="物料组"
                        prop="materialGroupId"
                        required>
            <el-select v-model="searchForm.materialGroupId"
                       filterable
                       default-first-option
                       clearable
                       style="width:100%">
              <el-option v-for="item in productList"
                         :key="item.productGroupNum+Math.random()"
                         :label="item.productGroupNum+ ' '+item.productGroupName"
                         :value="item.productGroupId">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="品牌"
                        prop="brandId">
            <SelectBrand clearable
                         :data.sync="searchForm.brands"
                         @change="handleChange('brands', $event)" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="商品来源"
                        prop="commoditySourceTypeName">
            <el-select
              v-model="searchForm.commoditySourceTypeName"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in commoditySourceTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="SKU编码" prop="sku">
            <el-input
              v-model="searchForm.sku"
              placeholder="最多支持100个SKU按空格隔开搜索"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="发起修改日期" prop="modificationDate">
            <el-date-picker v-model="searchForm.modificationDate"
                            type="daterange"
                            value-format="yyyy-MM-dd"
                            range-separator="-"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            clearable
                            style="width:100%">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备货类型" prop="inventoryType">
            <el-select
              v-model="searchForm.inventoryType"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option value="A1"></el-option>
              <el-option value="A2"></el-option>
              <el-option value="B"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="产品定位" prop="productPositioningName">
            <el-select
              v-model="searchForm.productPositioningName"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in productPositioningNameList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :offset="8" :span="8" :style="{display: 'flex', justifyContent: 'flex-end'}">
          <el-button type="primary"
            :loading="searchLoading"
            @click="handleSearch">
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <vxe-grid border
              stripe
              resizable
              auto-resize
              keep-source
              show-overflow
              ref="approvalHistoryGrid"
              height="640"
              id="approvalHistory_grid"
              row-id="id"
              align="center"
              :loading="tableLoading"
              :custom-config="tableCustom"
              :data="listData"
              :columns="columns"
              :toolbar-config="tableToolbar"
              :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}"
              highlight-hover-row
              highlight-current-row
              header-cell-class-name="headerCellClass">
      <template v-slot:dockedStatus_default="{ row }">
        {{ row.dockedStatus ? '是' : '否' }}
      </template>

      <template #originalInventoryStatus_default="{ row }">
        {{ row.originalInventoryStatus ? '备货' : '不备货' }}
      </template>

      <template #originalDecisionSource_default="{ row}">
        {{ (DESICION_SOURCE.find(item => item.value === row.originalDecisionSource) || {}).label }}
      </template>

      <template v-slot:isEffective_default="{ row }">
        {{ (IS_EFFECTIVE.find(item => item.value === row.isEffective) || {}).label }}
      </template>
    </vxe-grid>

    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="table.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="table.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="table.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectBrand from '@/pages/mrp/components/selectBrand'
import { safeRun } from '@/utils/index'
import { COMMODITY_SOURCETYPE, DESICION_SOURCE, IS_EFFECTIVE, productPositioningNameList } from './utils'
import { getABHistoryList } from '@/api/ab-configuration'

const columns = [
  {
    title: 'sku基础信息',
    children: [
      {
        field: 'factory',
        title: '工厂',
        minWidth: 50
      },
      {
        field: 'productGroupName',
        title: '物料组',
        minWidth: 80
      },
      {
        field: 'brandName',
        title: '品牌',
        minWidth: 80
      },
      {
        field: 'manufacturerMaterialNo',
        title: '制造商型号',
        minWidth: 90
      },
      {
        field: 'commoditySourceTypeName',
        title: '商品来源',
        minWidth: 80
      },
      {
        field: 'productPosition',
        title: '产品定位',
        minWidth: 80
      },
      {
        field: 'dockedStatus',
        title: '是否对接库存',
        minWidth: 100,
        slots: {
          default: 'dockedStatus_default'
        }
      },
      {
        field: 'sku',
        title: 'sku编码',
        minWidth: 80
      },
      {
        field: 'productName',
        title: '产品名称',
        minWidth: 80
      },
      {
        field: 'supplyNetWorkRuleName',
        title: '供应网络',
        minWidth: 80
      },
      {
        field: 'khWarehouseCode',
        title: '仓库编码',
        minWidth: 80
      },
      {
        field: 'khWarehouseName',
        title: '仓库名称',
        minWidth: 80
      },
      {
        field: 'mrpScope',
        title: 'mrp编码',
        minWidth: 80
      }
    ]
  },
  {
    title: '产线审核',
    children: [
      {
        field: 'originalInventoryStatus',
        title: '备货状态',
        minWidth: 80,
        slots: {
          default: 'originalInventoryStatus_default'
        }
      },
      {
        field: 'originalRop',
        title: 'ROP',
        minWidth: 80
      },
      {
        field: 'originalRoq',
        title: 'ROQ',
        minWidth: 80
      }
    ]
  },
  {
    title: '其他关键信息',
    children: [
      {
        field: 'originalGmtModified',
        title: '审批完成日期',
        minWidth: 100
      },
      {
        field: 'originalDecisionSource',
        title: '备货决策来源',
        minWidth: 100,
        slots: {
          default: 'originalDecisionSource_default'
        }
      },
      {
        field: 'originalInventoryType',
        title: 'SKU归类',
        minWidth: 80
      }
    ]
  },
  {
    title: '生效时间',
    children: [
      {
        field: 'isEffective',
        title: '本行是否生效',
        minWidth: 100,
        slots: {
          default: 'isEffective_default'
        }
      },
      {
        field: 'gmtEffectStart',
        title: '生效开始日期',
        minWidth: 100
      },
      {
        field: 'gmtEffectEnd',
        title: '生效结束日期',
        minWidth: 100
      }
    ]
  }
]

export default {
  name: 'approvalHistory',
  components: {
    SelectBrand
  },
  data () {
    return {
      commoditySourceTypeList: COMMODITY_SOURCETYPE,
      productPositioningNameList,
      DESICION_SOURCE,
      IS_EFFECTIVE,
      searchForm: {
        materialGroupId: '',
        brandId: '',
        brands: {},
        commoditySourceTypeName: '',
        sku: '',
        inventoryType: '',
        productPositioningName: '',
        modificationDate: []
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true
      },
      table: {
        pageNo: 1,
        total: 0,
        pageSize: 10
      },
      columns,
      listData: [],
      rules: {
        materialGroupId: [
          { required: true, message: '物料组不能为空！', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  async created () {
    const pList = []
    if (this.productList.length === 0) {
      pList.push(this.$store.dispatch('mrp/queryProductGroup'))
    }
    await Promise.all(pList)
  },
  computed: {
    ...mapState({
      productList: state => state.mrp.productList
    })
  },
  methods: {
    validSearch (callback) {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          callback && callback()
        }
      })
    },
    handleSizeChange (size) {
      this.table.pageSize = size
      this.validSearch(this.getABStockHistoryList)
    },
    handleCurrentChange (pageNo) {
      this.table.pageNo = pageNo
      this.validSearch(this.getABStockHistoryList)
    },
    handleChange (type, event) {
      if (type === 'brands') {
        this.searchForm.brandId = event.brandId
      }
    },
    formatParams(params) {
      let form = { ...params };
      form.sku = safeRun(() =>
        form.sku
          .split(/\s+|,|，/).filter((e) => e)
      );
      delete form.modificationDate
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.sku.length > 100) {
          ret = false
          this.$message.error('最多支持100个SKU按空格隔开搜索！')
        }
      })
      return ret
    },
    async getABStockHistoryList () {
      try {
        if (this.searchForm.modificationDate === null) {
          this.searchForm.modificationDate = []
        }
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return

        if (Array.isArray(params.sku)) {
          params.sku = params.sku.join(',')
        }
        this.tableLoading = true
        this.searchLoading = true
        params = {
          ...params,
          pageNo: this.table.pageNo,
          gmtModifiedStart: this.searchForm.modificationDate[0],
          gmtModifiedEnd: this.searchForm.modificationDate[1]
        }
        const res = await getABHistoryList(params)
        if (res.code === 200) {
          this.listData = res.data
          this.table.total = res.total;
          if (this.listData.length === 0) {
            this.$message.info('没有符合条件的AB分类审批历史')
          }
        } else {
          this.$message.error(res.msg || res.message || '请求失败！')
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
        this.$message.error(error.msg || error.message || '请求失败！')
      }
    },
    handleSearch () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.table.pageNo = 1
          this.getABStockHistoryList()
        } else {
          return false;
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.list-container {
  padding: 20px;
  margin-right: 15px;
}
.pagination{
  margin-top: 20px;
  float:right;
}
</style>
<style lang="scss">
.headerCellClass {
  border: 1px solid rgb(215, 230, 243);
}
</style>
