<template>
  <div class="app-container stockpile-strategy-history">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row type="flex" justify="space-between">
          <el-col>
            <el-col :span="6">
              <el-form-item label="版本：">
                <el-select
                  v-model="searchForm.versionDateStrList"
                  filterable
                  clearable
                  multiple
                  value-key="id"
                  placeholder="请输入"
                >
                  <el-option
                    v-for="item in versionOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="采购员：">
                <el-select
                  v-model="searchForm.productMangerIdList"
                  filterable
                  clearable
                  multiple
                  value-key="id"
                  placeholder="请输入关键词"
                >
                  <el-option
                    v-for="item in productManagerOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="物料组：">
                <el-select
                  v-model="searchForm.materialGroupNoList"
                  filterable
                  clearable
                  multiple
                  value-key="id"
                  placeholder="请输入关键词"
                >
                  <el-option
                    v-for="item in materialGroupOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="search-row" label="SKU：">
                <el-input
                  v-model="searchForm.skuNoList"
                  placeholder="最多支持200个，以空格分隔"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-col>
          <el-col style="text-align: right" :span="6">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button @click="handleDownload" :loading="exportLoading"
              >下载</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <el-table
        ref="multipleTable"
        v-loading="basisListLoading"
        :data="basisList"
        border
        height="500"
      >
        <el-table-column
          prop="versionDateStr"
          label="版本"
          width="90"
          align="center"
        />
        <el-table-column prop="skuNo" label="SKU" width="80" align="center" />
        <el-table-column
          prop="skuDesc"
          label="SKU描述"
          width="100"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          prop="materialGroup"
          label="物料组"
          width="100"
          align="center"
        />
        <el-table-column
          prop="brandName"
          label="品牌名称"
          width="80"
          align="center"
        />
        <el-table-column
          prop="productManager"
          label="产品经理"
          width="80"
          align="center"
        />
        <el-table-column
          prop="position"
          label="备货仓位"
          width="100"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          prop="rop"
          width="120"
          align="center"
          label="补货点（ROP）"
        />
        <el-table-column
          prop="roq"
          width="120"
          align="center"
          label="再订货量（ROQ）"
        />
        <el-table-column
          prop="stockMark"
          width="100"
          label="备货标识"
          align="center"
        >
          <template slot-scope="{ row }">
            <span>{{ transStockCodeToLabel(row.stockMark) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="remark"
          width="160"
          align="center"
          :show-overflow-tooltip="true"
          label="审核备注"
        />
        <el-table-column
          prop="statusName"
          label="行状态"
          width="80"
          align="center"
        />
        <el-table-column
          prop="monthQuantity"
          label="月用量"
        />
        <el-table-column prop="salesTrendStatus" label="销售趋势判断" min-width="160">
        </el-table-column>
        <el-table-column
          prop="changeRatio"
          label="变化比例"
        />
        <el-table-column
          prop="salesFeedbackRemark"
          min-width="160"
          label="销售反馈备注"
        />
        <el-table-column
          prop="bigStoneCustomerCode"
          min-width="120"
          label="大石头客户代码"
        />
        <el-table-column
          prop="bigStoneCustomer"
          min-width="160"
          label="大石头客户"
        />
        <el-table-column
          prop="seller"
          label="销售"
        />
        <el-table-column
          prop="operateLog"
          label="操作记录"
          width="160"
          :show-overflow-tooltip="true"
          align="center"
        />
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination';
import {
  exportHistory,
  getHistoryBasisList,
  getHistoryVersionOptions,
  getStockMarkOptions,
  getHistoryProductManagerOptions,
  getHistoryMaterialGroupOptions
} from '@/api/stockpile';
export default {
  name: 'Historylist',
  data() {
    return {
      basisListLoading: false,
      exportLoading: false,
      versionOptions: [],
      productManagerOptions: [],
      materialGroupOptions: [],
      stockMarkOptions: [],
      basisList: [],
      searchForm: {
        versionDateStrList: [],
        productMangerIdList: [],
        materialGroupNoList: [],
        skuNoList: ''
      },
      skuList: {
        isLoading: false,
        options: []
      },
      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      total: 0
    };
  },
  components: {
    Pagination
  },
  created() {
    Promise.all([
      getHistoryVersionOptions().then((data) => {
        this.versionOptions = data;
      }),
      getHistoryProductManagerOptions().then((data) => {
        this.productManagerOptions = data;
      }),
      getHistoryMaterialGroupOptions().then((data) => {
        this.materialGroupOptions = data;
      }),
      getStockMarkOptions().then((data) => {
        this.stockMarkOptions = data;
      })
    ]);
    this.handleFilter();
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    prepareSearchParam() {
      let param = { ...this.searchForm };
      if (param.skuNoList.split) {
        param.skuNoList = param.skuNoList.split(/\s+|,|，/).filter(Boolean);
      }
      return param;
    },
    transStockCodeToLabel(code) {
      const opt = this.stockMarkOptions.find((item) => item.value === code);
      if (opt) {
        return opt.label;
      }
      return '';
    },
    handleDownload() {
      if (!this.searchForm.versionDateStrList.length) {
        this.$message.error({ message: '请先选择版本！', duration: 6000 });
        return;
      }
      let param = this.prepareSearchParam();
      this.exportLoading = true;
      exportHistory({ ...param })
        .catch((err) => {
          this.$message.error({
            message: err.msg || err.message || '下载失败！',
            duration: 6000
          });
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    getBasisList() {
      let param = this.prepareSearchParam();
      param.current = this.listQueryInfo.current;
      param.pageSize = this.listQueryInfo.pageSize;
      this.basisList = [];
      this.basisListLoading = true;
      getHistoryBasisList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              const data = res.data;
              this.total = res.totalCount;
              this.basisList = data;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    }
  }
};
</script>

<style lang="scss">
.stockpile-strategy-history {
  .tabelCellError {
    input {
      border: 1px solid #ff4949;
    }
    &:hover {
      input {
        border: 1px solid #ff4949;
      }
    }
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }
  .justify-between {
    justify-content: space-between;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .fw-500 {
    font-weight: 500;
  }
  .primary-blue {
    color: #7a95f1;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
}
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
