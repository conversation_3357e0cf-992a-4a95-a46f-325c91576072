export const COMMODITY_SOURCETYPE = [
  { value: 'ZKH', label: 'ZKH' },
  { value: 'VPI_COMMISSION', label: 'VPI佣金' },
  { value: 'VPI_DIFFERENCE', label: 'VPI差价' }
]
export const productPositioningNameList = [
  { value: '1', label: '行家精选' },
  { value: '2', label: '规格商品' },
  { value: '3', label: '主推商品' },
  { value: '4', label: '询价商品' },
  { value: '5', label: '一般商品' }
]

export const DESICION_SOURCE = [
  { value: 'PRODUCTION_DECISION', label: '产线备货' },
  { value: 'AI_DECISION', label: '模型备货' },
  { value: 'STATUS_CHANGE_DECISION', label: '商品状态变更' }
]

export const IS_EFFECTIVE = [
  { value: 'N', label: '未生效' },
  { value: 'Y', label: '已生效' },
  { value: 'NA', label: '已废弃' }
]

export function removeDuplicatesByKey(arr, key = 'warehouseCode') {
  return arr.filter((item, index) => {
    return arr.findIndex(t => t[key] === item[key]) === index
  })
}
