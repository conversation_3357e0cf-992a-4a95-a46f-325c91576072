<template>
  <div class="app-container stockpile-strategy-checklist">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="采购员：" prop="productMangerIdList">
              <el-select
                v-model="searchForm.productMangerIdList"
                filterable
                clearable
                multiple
                placeholder="请输入关键词"
              >
                <el-option
                  v-for="item in productManagerOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组：" prop="materialGroupNoList">
              <el-select
                v-model="searchForm.materialGroupNoList"
                filterable
                clearable
                multiple
                placeholder="请输入关键词"
              >
                <el-option
                  v-for="item in materialGroupOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌：" prop="brandNoList">
              <el-select
                v-model="searchForm.brandNoList"
                filterable
                clearable
                multiple
                placeholder="请输入关键词"
              >
                <el-option
                  v-for="item in brandOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item class="search-row" label="SKU：" prop="skuNoList">
              <el-input
                v-model="searchForm.skuNoList"
                placeholder="最多支持200个，以空格分隔"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row type="flex" justify="space-between">
          <el-col :span="6">
            <el-form-item label="备货标识：" prop="stockMark">
              <el-select clearable v-model="searchForm.stockMark">
                <el-option
                  v-for="item in stockMarkOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核备注：" prop="remarkMark">
              <el-select clearable v-model="searchForm.remarkMark">
                <el-option
                  v-for="item in remarkMarkOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="审核状态：" prop="statusCode">
              <el-select clearable v-model="searchForm.statusCode">
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货仓位：" prop="stockPositionCodeList">
              <el-select
                v-model="searchForm.stockPositionCodeList"
                filterable
                clearable
                multiple
                placeholder="请输入关键词"
              >
                <el-option
                  v-for="item in stockPositionCodeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container">
      <el-row>
        <DividerHeader>
          <span class="flex justify-between pr-10">
            <span>审核信息 <span class="color-blue ml-10">重点打标商品，当前库存对接质量低，建议选择自备或寄售，请重点关注！</span>
            </span>
            <span class="primary-blue fw-500">当前版本：{{ version }}</span>
          </span>
        </DividerHeader>
      </el-row>
      <el-row type="flex" class="result-operation" justify="space-between">
        <el-col class="flex" style="align-items: flex-start" :span="8">
          <el-button
            type="success"
            :loading="operateLoading.acceptBatched"
            @click="() => handleOperateSelected('accept')"
            >批量通过</el-button
          >
          <el-button
            v-if="isStockStaff"
            type="danger"
            @click="() => handleOperateSelected('reject')"
            >批量驳回</el-button
          >
          <el-button
            v-if="isStockStaff"
            type="primary"
            @click="handleOperateAll('stockStaffCheckAll')"
            >全部审核</el-button
          >
          <el-button
            v-if="isStockStaff"
            type="primary"
            @click="handleOperateAll('stockStaffReIssue')"
            >重新下发</el-button
          >
          <el-button
            :loading="operateLoading.approveBatched"
            type="primary"
            @click="() => handleOperateSelected('approve')"
            >批量提交</el-button
          >
          <el-button
            :loading="operateLoading.readjustBatched"
            v-if="isLineStaff"
            type="primary"
            @click="() => handleOperateSelected('readjust')"
            >重新调整</el-button
          >
        </el-col>
        <el-col
          style="
            text-align: right;
            justify-content: flex-end;
            align-items: start;
          "
        >
          <el-button
            type="success"
            :loading="operateLoading.acceptAll"
            @click="() => handleOperateAll('accept')"
            >全部通过</el-button
          >
          <el-button
            v-if="isStockStaff"
            :loading="operateLoading.rejectAll"
            type="danger"
            @click="() => handleOperateAll('reject')"
            >全部驳回</el-button
          >
          <el-button
            :loading="operateLoading.approveAll"
            type="primary"
            :class="!isSalesStaff ? '' : 'mr-10'"
            @click="() => handleOperateAll('approve')"
            >全部提交</el-button
          >
          <el-button
            v-if="!isSalesStaff"
            type="primary"
            class="mr-10"
            @click="handleExportAll"
            :loading="exportLoading"
            >下载</el-button
          >
          <el-upload
            class="upload mr-10"
            action="/data-center-front/stockup/stock/audit/import/excel"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            :multiple="false"
            :limit="1"
            ref="upload"
            :show-file-list="true"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
          >
            <el-button :loading="uploadLoading" size="small" type="default"
              >上传</el-button
            >
          </el-upload>
        </el-col>
      </el-row>
      <el-table
        ref="multipleTable"
        v-loading="basisListLoading"
        :data="basisList"
        border
        fit
        height="500"
        @selection-change="tableSelectionChange"
      >
        <el-table-column type="selection" min-width="40" fixed="left" />
        <el-table-column
          prop="skuNo"
          label="SKU"
          min-width="120"
          align="center"
        >
          <template slot-scope="{ row }">
            <span :class="{ 'mr-24': row.skuTagSet && row.skuTagSet.length > 0 }">{{ row.skuNo }}</span>
            <span v-if="row.skuTagSet && row.skuTagSet.length > 0" class="sku-tag">
              <el-tag v-for="tag in row.skuTagSet" size="mini" :key="tag.value" type="danger" :content="tag.name">{{ tag.name }}</el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="skuDesc"
          label="SKU描述"
          min-width="100"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          prop="materialGroup"
          label="物料组"
          min-width="100"
          align="center"
        />
        <el-table-column
          prop="brandName"
          label="品牌名称"
          min-width="80"
          align="center"
        />
        <el-table-column
          prop="productManager"
          label="产品经理"
          min-width="80"
          align="center"
        />

        <el-table-column
          prop="position"
          label="备货仓位"
          min-width="100"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          v-if="!isSalesStaff"
          prop="originalRop"
          class-name="text-no-overflow"
          min-width="120"
          align="center"
          label="原始ROP"
        />
        <el-table-column
          v-if="!isSalesStaff"
          prop="rop"
          class-name="text-no-overflow"
          min-width="120"
          align="center"
          label="补货点（ROP）"
        >
          <template slot-scope="{ row }">
            <el-input-number
              v-if="calcEditable(row, 'rop')"
              class="input-number--micro"
              v-model="row.rop"
              size="mini"
              :controls="false"
              :step="1"
              :precision="0"
              @blur="(event) => changeRow(row, event.target.value, 'rop')"
            />
            <span v-else>{{ row.rop }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="!isSalesStaff"
          prop="originalRoq"
          class-name="text-no-overflow"
          min-width="120"
          align="center"
          label="原始ROQ"
        />
        <el-table-column
          v-if="!isSalesStaff"
          prop="roq"
          class-name="text-no-overflow"
          min-width="120"
          align="center"
          label="再订货量（ROQ）"
        >
          <template slot-scope="{ row }">
            <el-input-number
              v-if="calcEditable(row, 'roq')"
              class="input-number--micro"
              v-model="row.roq"
              size="mini"
              :controls="false"
              :step="1"
              :precision="0"
              @blur="(event) => changeRow(row, event.target.value, 'roq')"
            />
            <span v-else>{{ row.roq }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="是否系统对接"
          prop="isInventoryDocking"
          min-width="80"
          align="center"
        >
        <template slot-scope="{ row }"> {{row.isInventoryDocking === null ? '--' : row.isInventoryDocking == 1 ? '是' : '否'}} </template>
        </el-table-column>
         <el-table-column
          label="近三个月对客总有货率"
          prop="spotGoodsDnLineRate90d"
          min-width="100"
          align="center"
        >
          <template slot-scope="{ row }"> {{row.spotGoodsDnLineRate90d === null ? '--' : row.spotGoodsDnLineRate90d}} </template>
        </el-table-column>
         <el-table-column
          label="近三个月对客展示有货交货行占比"
          prop="displaySpotGoodsDnLineRate90d"
          min-width="120"
          align="center"
        >
          <template slot-scope="{ row }"> {{row.displaySpotGoodsDnLineRate90d === null ? '--' : row.displaySpotGoodsDnLineRate90d}} </template>
        </el-table-column>
        <el-table-column
          v-if="!isSalesStaff"
          prop="stockMark"
          min-width="120"
          align="center"
        >
          <template slot="header">
            <OptRequiredRowTitle title="备货标识" :isRequired="true" />
          </template>
          <template slot-scope="{ row }">
            <el-select
              v-if="calcEditable(row, 'stockMark')"
              v-model="row.stockMark"
              size="mini"
              placeholder="请选择"
              @change="(val) => changeRow(row, val, 'stockMark')"
            >
              <el-option
                v-for="item in stockMarkOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span v-else>{{ transStockCodeToLabel(row.stockMark) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="!isSalesStaff"
          label="预计采购单量"
          prop="estimatePurchase"
          min-width="100"
        ></el-table-column>
        <el-table-column
          label="级库存模式"
          prop="isClassificationWhs"
          min-width="80"
        ></el-table-column>
        <el-table-column
          label="级库存上级仓"
          prop="upWarehouse"
          min-width="120"
        ></el-table-column>
        <el-table-column
          v-if="!isSalesStaff"
          prop="remark"
          min-width="160"
          :show-overflow-tooltip="true"
          align="center"
          label="审核备注"
        >
          <template slot-scope="{ row }">
            <el-input
              type="textarea"
              v-if="calcEditable(row, 'remark')"
              autosize
              v-model="row.remark"
              @blur="(event) => changeRow(row, event.target.value, 'remark')"
            />
            <span v-else>{{ row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="statusName"
          label="行状态"
          min-width="80"
          align="center"
        />
        <el-table-column prop="monthQuantity" label="月用量参考" />
        <el-table-column
          prop="salesTrendStatus"
          label="销售趋势判断"
          min-width="160"
        >
          <template slot-scope="{ row, $index }">
            <el-select
              size="mini"
              placeholder="请选择"
              v-model="row.salesTrendStatus"
              @change="(val) => changeRow(row, val, 'salesTrendStatus', $index)"
              v-if="isSalesStaff && !isNeedSalesFeedback(row)"
            >
              <el-option
                v-for="item in trendStatusOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span v-else>{{ row.salesTrendStatusName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="changeRatio" min-width="120" label="变化比例">
          <template slot-scope="{ row }">
            <el-select
              size="mini"
              placeholder="请选择"
              v-model="row.changeRatio"
              @change="(val) => changeRow(row, val, 'changeRatio')"
              v-if="
                isSalesStaff &&
                !(
                  isNeedSalesFeedback(row) &&
                  (isTrendStatusNotChanged(row) ||
                    isTrendStatusNotNeedStock(row))
                )
              "
            >
              <el-option
                v-for="item in ratioOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <span v-else>{{ row.changeRatio }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="salesFeedbackRemark"
          min-width="160"
          label="销售反馈备注"
        >
          <template slot-scope="{ row }">
            <el-input
              type="textarea"
              autosize
              v-model="row.salesFeedbackRemark"
              v-if="
                isSalesStaff &&
                !(isTrendStatusNotChanged(row) && isNeedSalesFeedback(row))
              "
              @blur="
                (event) =>
                  changeRow(row, event.target.value, 'salesFeedbackRemark')
              "
            />
            <span v-else>{{ row.salesFeedbackRemark }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="bigStoneCustomerCode"
          min-width="120"
          label="大石头客户代码"
        />
        <el-table-column
          prop="bigStoneCustomer"
          min-width="160"
          label="大石头客户"
        />
        <el-table-column prop="seller" label="销售" />
        <el-table-column
          prop="operateLog"
          label="操作记录"
          min-width="160"
          :show-overflow-tooltip="true"
          align="center"
        />
        <el-table-column
          fixed="right"
          v-if="isSalesStaff"
          label="操作"
          align="center"
        >
          <template slot-scope="{ row }">
            <el-button
              v-if="isNeedSalesFeedback(row)"
              type="text"
              @click="openTrendDialog(row)"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
    <el-dialog
      width="800px"
      title="批量驳回"
      class="reject-dialog"
      :visible.sync="isOpenRejectDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="closeRejectDialog"
    >
      <el-form
        ref="rejectForm"
        :model="rejectForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-form-item class="search-row" label="驳回原因：" prop="remark">
            <el-input type="textarea" :rows="3" v-model="rejectForm.remark" />
          </el-form-item>
        </el-row>
      </el-form>
      <footer class="align-right">
        <el-button @click="closeRejectDialog">取消</el-button>
        <el-button @click="submitData" type="primary">确定</el-button>
      </footer>
    </el-dialog>
    <el-dialog
      width="800px"
      title="填写不备货备注"
      class="remark-dialog"
      :visible.sync="isOpenRemarkDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="closeRemarkDialog"
    >
      <el-form
        ref="remarkForm"
        :model="remarkForm"
        label-width="120px"
        label-position="right"
        :rules="remarkFormRules"
      >
        <el-row>
          <el-form-item class="search-row" label="备注：" prop="remark">
            <el-input type="textarea" :rows="3" v-model="remarkForm.remark" />
          </el-form-item>
        </el-row>
      </el-form>
      <footer class="align-right">
        <el-button @click="closeRemarkDialog">取消</el-button>
        <el-button @click="submitRemarkData" type="primary">确定</el-button>
      </footer>
    </el-dialog>
    <el-dialog
      width="400px"
      title="销售趋势反馈"
      class="trend-dialog"
      :visible.sync="isOpenTrendDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      @close="closeTrendDialog"
    >
      <el-form
        ref="trendForm"
        :model="trendForm"
        :rules="trendFormRules"
        :validate-on-rule-change="false"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-form-item
            class="search-row"
            label="销售趋势判断："
            prop="salesTrendStatus"
          >
            <el-select
              size="mini"
              placeholder="请选择"
              v-model="trendForm.salesTrendStatus"
            >
              <el-option
                v-for="item in trendStatusOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item
            class="search-row"
            label="变化比例："
            prop="changeRatio"
          >
            <el-select
              size="mini"
              placeholder="请选择"
              v-model="trendForm.changeRatio"
              :disabled="
                isNeedSalesFeedback(trendForm) &&
                (isTrendStatusNotChanged(trendForm) ||
                  isTrendStatusNotNeedStock(trendForm))
              "
            >
              <el-option
                v-for="item in ratioOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item
            class="search-row"
            label="销售反馈备注："
            prop="salesFeedbackRemark"
          >
            <el-input
              type="textarea"
              :disabled="
                isTrendStatusNotChanged(trendForm) &&
                isNeedSalesFeedback(trendForm)
              "
              :rows="3"
              v-model="trendForm.salesFeedbackRemark"
            />
          </el-form-item>
        </el-row>
      </el-form>
      <footer class="align-right">
        <el-button @click="closeTrendDialog">取消</el-button>
        <el-button @click="submitTrendData" type="primary">确定</el-button>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Pagination from '@/components/Pagination';
import OptRequiredRowTitle from '@/pages/orderSale/components/common/OptRequiredRowTitle';
import DividerHeader from '@/components/DividerHeader';
import {
  acceptAll,
  acceptBatched,
  approveAll,
  rejectAll,
  approveBatched,
  rejectBatched,
  readjustBatched,
  exportBasisApi,
  getBasisList,
  getBrandOptions,
  getMaterialGroupOptions,
  getProductManagerOptions,
  getStockPositionCodeOptions,
  getRemarkMarkOptions,
  getStatusOptions,
  getStockMarkOptions,
  updateField,
  getCurrentVersion,
  stockStaffCheckAll,
  stockStaffReIssue
} from '@/api/stockpile';
import { cloneDeep } from 'lodash';

const initailOperateLoading = {
  acceptBatched: false,
  approveBatched: false,
  readjustBatched: false,
  acceptAll: false,
  approveAll: false,
  rejectAll: false,
  stockStaffCheckAll: false,
  stockStaffReIssue: false
};

const trendStatusOption = [
  { value: 0, label: '不变' },
  { value: 1, label: '上升' },
  { value: -1, label: '下降' },
  { value: 2, label: '无需备货' }
];

const ratioOption = [
  { value: '25%', label: '25%' },
  { value: '50%', label: '50%' },
  { value: '75%', label: '75%' },
  { value: '100%', label: '100%' }
];

export default {
  name: 'Checklist',
  data() {
    return {
      version: '',
      basisListLoading: false,
      brandOptions: [],
      materialGroupOptions: [],
      productManagerOptions: [],
      stockPositionCodeOptions: [],
      trendStatusOption,
      ratioOption,
      remarkMarkOptions: [],
      statusOptions: [],
      stockMarkOptions: [],
      exportLoading: false,
      uploadLoading: false,
      basisList: [],
      basisListCopy: [],
      multipleSelection: [],
      searchForm: {
        brandNoList: [],
        materialGroupNoList: [],
        productMangerIdList: [],
        stockPositionCodeList: [],
        skuNoList: '',
        remarkMark: '',
        statusCode: '',
        stockMark: ''
      },
      skuList: {
        isLoading: false,
        options: []
      },
      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      total: 0,
      isOpenRejectDialog: false,
      rejectForm: {
        remark: ''
      },
      isOpenRemarkDialog: false,
      remarkForm: {
        remark: ''
      },
      selectedRow: null,
      operateLoading: { ...initailOperateLoading },
      remarkFormRules: {
        remark: [
          { required: true, message: '备注不能为空！', trigger: 'blur' }
        ]
      },
      isOpenTrendDialog: false,
      trendForm: {}
    };
  },
  components: {
    Pagination,
    OptRequiredRowTitle,
    DividerHeader
  },
  created() {
    Promise.all([
      getBrandOptions().then((data) => {
        this.brandOptions = data;
      }),
      getMaterialGroupOptions().then((data) => {
        this.materialGroupOptions = data;
      }),
      getProductManagerOptions().then((data) => {
        this.productManagerOptions = data;
      }),
      getStockPositionCodeOptions().then((data) => {
        this.stockPositionCodeOptions = data;
      }),
      getRemarkMarkOptions().then((data) => {
        this.remarkMarkOptions = data;
      }),
      getStatusOptions().then((data) => {
        const clone = JSON.parse(JSON.stringify(data));
        const excludes = ['下发中', '下发SAP成功', '下发SAP失败'];
        excludes.forEach((item) => {
          clone.splice(
            clone.findIndex((it) => it.label === item),
            1
          );
        });
        this.statusOptions = clone;
      }),
      getStockMarkOptions().then((data) => {
        this.stockMarkOptions = data;
      }),
      getCurrentVersion().then((res) => {
        this.version = res.data;
      })
    ]);
    this.handleFilter();
  },
  computed: {
    trendFormRules() {
      if (this.isTrendStatusUpOrDown(this.trendForm)) {
        return {
          changeRatio: [
            {
              required: true,
              trigger: 'blur',
              validator(_, value) {
                return new Promise((resolve, reject) => {
                  if (value === '0%' || value === '0.0%' || !value) {
                    return reject(new Error('变化比例不能为空！'));
                  }
                  resolve();
                });
              }
            }
          ],
          salesFeedbackRemark: [
            {
              required: true,
              message: '销售反馈备注不能为空！',
              trigger: 'blur'
            }
          ]
        };
      }
      if (
        this.isTrendStatusUpOrDown(this.trendForm) ||
        this.isTrendStatusNotNeedStock(this.trendForm)
      ) {
        return {
          salesFeedbackRemark: [
            {
              required: true,
              message: '销售反馈备注不能为空！',
              trigger: 'blur'
            }
          ]
        };
      }

      return {};
    },
    ...mapState(['userRole']),
    isLineStaff() {
      return (
        !!~this.userRole.indexOf('data-产线审核') ||
        !!~this.userRole.indexOf('商品中心-备货审核')
      );
    },
    isStockStaff() {
      return !!~this.userRole.indexOf('data-库存策略');
    },
    isSalesStaff() {
      return !!~this.userRole.indexOf('data-销售');
    }
  },
  methods: {
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.handleFilter();
    },
    async handleOperateSelected(type) {
      if (!this.multipleSelection.length) {
        this.$message.warning('请勾选需要需要处理的行');
        return;
      }
      let remoteRequest = Promise.resolve();
      switch (type) {
        case 'accept':
        case 'reject':
        case 'approve':
          let arr = this.multipleSelection.filter((a) => a.riskWarning && (type === 'reject' || !a.roq || a.stockMark !== 1))
          let str = ''
          arr.forEach((item) => {
            str += `${item.skuNo}(${item.skuDesc}) 存在风险项：${item.riskWarning}\n`
          })
          if (str) {
            this.$confirm(`<div style="white-space: pre-line; max-height: 400px; overflow-y: scroll;">${str}影响对客有货率，请三思！</div>`, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              closeOnClickModal: false,
              type: 'warning',
              dangerouslyUseHTMLString: true,
              width: '1000px'
            }).then(() => {
              if (type === 'accept') {
                this.operateLoading.acceptBatched = true;
                remoteRequest = acceptBatched;
                this.handleOperateSelectedFn(remoteRequest)
              }
              if (type === 'reject') {
                this.isOpenRejectDialog = true;
              }
              if (type === 'approve') {
                this.operateLoading.approveBatched = true;
                remoteRequest = approveBatched;
                this.handleOperateSelectedFn(remoteRequest)
              }
            })
            return
          } else {
            if (type === 'accept') {
              this.operateLoading.acceptBatched = true;
              remoteRequest = acceptBatched;
              this.handleOperateSelectedFn(remoteRequest)
            }
            if (type === 'reject') {
              this.isOpenRejectDialog = true;
            }
            if (type === 'approve') {
              this.operateLoading.approveBatched = true;
              remoteRequest = approveBatched;
              this.handleOperateSelectedFn(remoteRequest)
            }
            return
          }
        case 'readjust':
          this.operateLoading.readjustBatched = true;
          remoteRequest = readjustBatched;
          break;
      }
      this.handleOperateSelectedFn(remoteRequest)
    },
    async handleOperateSelectedFn(remoteRequest) {
      try {
        const res = await remoteRequest(
          this.multipleSelection.map((item) => item.id)
        );
        if (res.code === 200) {
          this.$message.success(res.msg || '操作成功！');
          this.handleFilter();
        } else {
          this.$message.error({
            message: res.msg || res.message || '操作失败！',
            duration: 6000
          });
        }
      } catch (err) {
        this.$message.error({
          message: err.msg || err.message || '操作失败！',
          duration: 6000
        });
      } finally {
        this.operateLoading = { ...initailOperateLoading };
      }
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.uploadLoading = true;
    },
    handleUploadSuccess(response) {
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
      if (response && response.code === 200) {
        this.$message.success(response.message || response.msg || '上传成功！');
        this.handleFilter();
      } else {
        this.$message.error({
          message:
            (response && (response.message || response.msg)) || '上传失败！',
          duration: 6000
        });
      }
    },
    handleUploadError(error) {
      this.uploadLoading = false;
      this.$message.error({
        message:
          (error && error.msg) || (error && error.message) || '上传失败!',
        duration: 6000
      });
    },
    prepareSearchParam() {
      let param = { ...this.searchForm };
      if (param.skuNoList.split) {
        param.skuNoList = param.skuNoList.split(/\s+|,|，/).filter(Boolean);
      }
      return param;
    },
    handleExportAll() {
      let param = this.prepareSearchParam();
      this.exportLoading = true;
      exportBasisApi({ ...param })
        .catch((err) => {
          this.$message.error({
            message: err.msg || err.message || '下载失败！',
            duration: 6000
          });
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    async handleOperateAll(type) {
      let param = this.prepareSearchParam();
      let remoteRequest = Promise.resolve();
      switch (type) {
        case 'accept':
          this.operateLoading.acceptAll = true;
          remoteRequest = acceptAll;
          param.mode = 0
          break;
        case 'approve':
          this.operateLoading.approveAll = true;
          remoteRequest = approveAll;
          param.mode = 0
          break;
        case 'reject':
          this.operateLoading.rejectAll = true;
          remoteRequest = rejectAll;
          param.mode = 0
          break;
        case 'stockStaffCheckAll':
          this.operateLoading.stockStaffCheckAll = true;
          remoteRequest = stockStaffCheckAll;
          break;
        case 'stockStaffReIssue':
          this.operateLoading.stockStaffReIssue = true;
          remoteRequest = stockStaffReIssue;
          break;
      }
      try {
        let res = await remoteRequest({ ...param });
        if (res.code === 200) {
          this.handleOperateAllFn(param, remoteRequest)
        } else if (res.code === 600) {
          this.$confirm(`<div style="white-space: pre-line; max-height: 400px; overflow-y: scroll;">${res.msg}</div>`, {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            closeOnClickModal: false,
            type: 'warning',
            dangerouslyUseHTMLString: true,
            width: '1000px'
          }).then(async () => {
            this.operateLoading[`${type}All`] = true;
            this.handleOperateAllFn(param, remoteRequest)
          })
        } else {
          this.$message.error({
            message: res.msg || res.message || '操作失败！',
            duration: 6000
          });
        }
      } catch (err) {
        this.$message.error({
          message: err.msg || err.message || '操作失败！',
          duration: 6000
        });
      } finally {
        this.operateLoading = { ...initailOperateLoading };
      }
    },
    async handleOperateAllFn (param, remoteRequest) {
      delete param.mode
      try {
        const result = await remoteRequest({ ...param });
        if (result.code === 200) {
          this.$message.success(result.msg || '操作成功！');
          this.handleFilter();
        } else {
          this.$message.error({
            message: result.msg || result.message || '操作失败！',
            duration: 6000
          });
        }
      } catch (err) {
        this.$message.error({
          message: err.msg || err.message || '操作失败！',
          duration: 6000
        });
      } finally {
        this.operateLoading = { ...initailOperateLoading };
      }
    },
    getBasisList() {
      let param = this.prepareSearchParam();
      param.current = this.listQueryInfo.current;
      param.pageSize = this.listQueryInfo.pageSize;
      this.basisList = [];
      this.basisListLoading = true;
      getBasisList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              const data = res.data;
              this.total = res.totalCount;
              this.basisList = data;
              this.basisListCopy = cloneDeep(data);
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    tableSelectionChange(val) {
      this.multipleSelection = val;
    },
    changeRow(row, value, field, index) {
      // if (field === 'stockMark') {
      //   // 不备货强制填写备注
      //   if (value === 0 && this.isLineStaff) {
      //     this.isOpenRemarkDialog = true;
      //     this.selectedRow = row;
      //     this.$set(this.remarkForm, 'remark', row.remark);
      //     return;
      //   }
      // }
      // 1.待销售反馈状态
      // 1.2 销售趋势判断如果为上升或下降，则必须填写比例>0%，否则提交时候需要有报错信息“##行， 未填写比例或者反馈备注”
      // 销售选择上升、下降和无需备货时，销售反馈备注列为必填项
      if (field === 'salesTrendStatus') {
        if (
          (this.isNeedSalesFeedback(row) &&
            this.isTrendStatusUpOrDown(row) &&
            !this.isChangeRatioGtZero(row)) ||
          (this.isNeedSalesFeedback(row) &&
            (this.isTrendStatusUpOrDown(row) ||
              this.isTrendStatusNotNeedStock(row)) &&
            !row['salesFeedbackRemark'])
        ) {
          this.$message.error({
            message: `第${index + 1}行， 未填写比例或者反馈备注`,
            duration: 6000
          });
          this.basisList = cloneDeep(this.basisListCopy);
          return;
        }
      }
      updateField({
        id: row.id,
        [field]: value
      }).then((res) => {
        if (res.code === 200) {
          this.basisListCopy = cloneDeep(this.basisList);
          this.$message.success(res.msg || '更新成功！');
          if (field === 'stockMark') { // 备货标识更改后重新请求这一条数据
            getBasisList({ skuNoList: [row.skuNo] }).then((res) => {
              this.basisListLoading = false;
              if (res.code === 200) {
                if (res.data) {
                  let temp = res.data.find((a) => a.id === row.id);
                  this.basisList = this.basisList.map((item) => {
                    item.id === row.id && temp && (item = temp)
                    return item
                  })
                  this.basisListCopy = cloneDeep(this.basisList);
                }
              } else {
                this.$message.error({ message: res.msg, duration: 6000 });
              }
            }).catch(() => {
              this.basisListLoading = false;
            });
          }
        } else {
          this.basisList = cloneDeep(this.basisListCopy);
          this.$message.error({ message: res.msg, duration: 6000 });
        }
      });
    },
    transStockCodeToLabel(code) {
      const opt = this.stockMarkOptions.find((item) => item.value === code);
      if (opt) {
        return opt.label;
      }
      return '';
    },
    // 销售趋势判断选择不变
    isTrendStatusNotChanged(row) {
      return row.salesTrendStatus === 0;
    },
    // 销售趋势判断选择无需备货
    isTrendStatusNotNeedStock(row) {
      return row.salesTrendStatus === 2;
    },
    // 销售趋势判断为上升或下降
    isTrendStatusUpOrDown(row) {
      return row.salesTrendStatus === 1 || row.salesTrendStatus === -1;
    },
    // 变化比例 > 0%
    isChangeRatioGtZero(row) {
      return (
        !!row.changeRatio &&
        row.changeRatio !== '0%' &&
        row.changeRatio !== '0.0%'
      );
    },
    // 待销售反馈状态
    isNeedSalesFeedback(row) {
      return row.statusCode === 9;
    },
    canStockStaffEdit(row) {
      return (
        row.statusCode === 1 || row.statusCode === 4 || row.statusCode === 5
      );
    },
    canLineStaffEdit(row) {
      return (
        row.statusCode === 0 || row.statusCode === 2 || row.statusCode === 4 || row.statusCode === 12
      );
    },
    closeRejectDialog() {
      this.isOpenRejectDialog = false;
      this.$refs['rejectForm'].clearValidate();
    },
    closeRemarkDialog() {
      this.isOpenRemarkDialog = false;
      this.basisList = cloneDeep(this.basisListCopy);
      this.$refs['remarkForm'].clearValidate();
    },
    submitRemarkData() {
      this.$refs['remarkForm'].validate(async (valid) => {
        if (valid) {
          try {
            const res = await updateField({
              id: this.selectedRow.id,
              remark: this.remarkForm.remark,
              stockMark: 0
            });
            if (res.code === 200) {
              this.getBasisList();
              this.closeRemarkDialog();
              this.$message.success(res.msg || '更新成功！');
            } else {
              this.$message.error({
                message: res.msg || res.message || '操作失败！',
                duration: 6000
              });
            }
          } catch (err) {
            this.$message.error({
              message: err.msg || err.message || '操作失败！',
              duration: 6000
            });
          }
        }
      });
    },
    async submitData() {
      try {
        const res = await rejectBatched({
          idList: this.multipleSelection.map((item) => item.id),
          remark: this.rejectForm.remark
        });
        if (res.code === 200) {
          this.$message.success(res.msg || '操作成功！');
          this.closeRejectDialog();
          this.handleFilter();
        } else {
          this.$message.error({
            message: res.msg || res.message || '操作失败！',
            duration: 6000
          });
        }
      } catch (err) {
        this.$message.error({
          message: err.msg || err.message || '操作失败！',
          duration: 6000
        });
      }
    },
    openTrendDialog(row) {
      this.isOpenTrendDialog = true;
      this.basisList = cloneDeep(this.basisListCopy);
      this.trendForm = { ...row };
    },
    closeTrendDialog() {
      this.isOpenTrendDialog = false;
      this.$refs['trendForm'].clearValidate();
    },
    submitTrendData() {
      this.$refs['trendForm'].validate(async (valid) => {
        if (valid) {
          try {
            const res = await updateField({
              id: this.trendForm.id,
              salesTrendStatus: this.trendForm.salesTrendStatus,
              changeRatio: this.trendForm.changeRatio,
              salesFeedbackRemark: this.trendForm.salesFeedbackRemark
            });
            if (res.code === 200) {
              this.getBasisList();
              this.closeTrendDialog();
              this.$message.success(res.msg || '更新成功！');
            } else {
              this.$message.error({
                message: res.msg || res.message || '操作失败！',
                duration: 6000
              });
            }
          } catch (err) {
            this.$message.error({
              message: err.msg || err.message || '操作失败！',
              duration: 6000
            });
          }
        }
      });
    },
    calcEditable(row, field) {
      // 审核状态：0 待送审、1 待审核、2 已驳回、3 下发中、4 下发SAP成功、 5 下发SAP失败、 12 待重审
      // 产线角色只能修改待送审、已驳回状态数据、下发SAP成功、待重审 && 产线角色可以修改补货点（ROP）、再订货量（ROQ）、备货标识、审核备注 && 待重审状态下产线角色可以修改补货点（ROP）、再订货量（ROQ）
      // 库存角色只能修改待审核、下发SAP成功、下发SAP失败状态数据 && 库存角色可以修改补货点（ROP）、再订货量（ROQ）、备货标识、审核备注
      switch (field) {
        case 'rop':
        case 'roq':
          return (
            (this.isStockStaff && this.canStockStaffEdit(row)) ||
            // 2. 反馈待处理状态下
            // 2.1 库存策略角色，可以对ROP、ROQ进行修改
            (this.isStockStaff && row.statusCode === 10) ||
            (this.isLineStaff && this.canLineStaffEdit(row))
          );
        case 'stockMark':
          return (
            (this.isStockStaff && this.canStockStaffEdit(row)) ||
            (this.isLineStaff && this.canLineStaffEdit(row))
          );
        case 'remark':
          return (
            (this.isStockStaff && this.canStockStaffEdit(row)) ||
            (this.isLineStaff && this.canLineStaffEdit(row))
          );
        default:
          return false;
      }
    }
  }
};
</script>

<style lang="scss">
.stockpile-strategy-checklist {
  .tabelCellError {
    input {
      border: 1px solid #ff4949;
    }
    &:hover {
      input {
        border: 1px solid #ff4949;
      }
    }
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }
  .justify-between {
    justify-content: space-between;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .fw-500 {
    font-weight: 500;
  }
  .color-blue {
    color: blue;
  }
  .primary-blue {
    color: #7a95f1;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mr-24 {
    margin-right: 24px;
  }
  .ml-10 {
    margin-left: 10px;
  }
  .input-number--micro {
    width: 100px;
  }
  .text-no-overflow {
    .cell {
      text-overflow: unset;
    }
  }
  .align-right {
    text-align: right;
  }
  .upload {
    display: inline;
  }
}
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.sku-tag {
  position: absolute;
  right: 2px;
  top: 0px;
}
</style>
