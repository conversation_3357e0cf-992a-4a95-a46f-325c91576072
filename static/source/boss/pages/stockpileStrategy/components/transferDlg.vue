<template>
  <el-dialog
    title="转移至"
    :visible.sync="visible"
    :destroy-on-close="true"
    width="500px"
    :show-close="false"
  >
    <div class="item">
      <span class="label">MRP范围：</span>
      <el-select
        v-model="mrpArea"
        filterable
        remote
        reserve-keyword
        style="width: 100%"
        value-key="mrpArea"
        :remote-method="remoteMethod"
        :loading="loading"
      >
        <el-option
          v-for="(item, index) in mrpAreaList"
          :key="item.id"
          :label="item.code + ' ' + item.description"
          :value="item.code"
          :disabled="index === 0"
        >
          <div
            class="selectItem"
            :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
          >
            <div>{{ item.code || "--" }}</div>
            <div>{{ item.description || "--" }}</div>
          </div>
        </el-option>
      </el-select>
    </div>
    <div class="footer">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button @click="cancel()">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getMrpArea } from '@/api/mrp';
import { skuInventoryParamsTransfer } from '@/api/ab-configuration'

export default {
  props: ['visible', 'selectRecords', 'cancel'],
  data() {
    return {
      loading: false,
      mrpAreaList: [],
      mrpArea: null
    };
  },
  methods: {
    remoteMethod(val) {
      if (val) {
        this.loading = true;
        getMrpArea({ mrpArea: val }).then((data) => {
          this.loading = false;
          let result = [];
          let obj = {};
          for (let i = 0; i < data.length; i++) {
            if (!obj[data[i].code]) {
              result.push(data[i]);
              obj[data[i].code] = true;
            }
          }
          this.mrpAreaList = [
            {
              code: 'MRP区域编码',
              description: 'MRP描述'
            },
            ...result
          ];
        });
      } else {
        this.mrpAreaList = [
          {
            code: 'MRP区域编码',
            description: 'MRP描述'
          }
        ];
      }
    },
    async confirm() {
      if (!this.mrpArea) {
        this.$message.info('请先选择MRP范围！')
        return
      }
      let params = {
        inventoryParamsTransferList: this.selectRecords.map((a) => {
          return {
            factory: a.factory,
            khWarehouseCode: a.khWarehouseCode,
            mrpScope: a.mrpScope,
            sku: a.sku
          }
        }),
        targetMrpScope: this.mrpArea
      }
      let res = await skuInventoryParamsTransfer(params)
      if (res.code === 200) {
        this.$message.success('备货参数转移成功！');
        this.cancel(true)
      } else {
        this.$message.error(res.msg || res.message || '操作失败！');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.item {
  display: flex;
  align-items: center;
  .label {
    width: 200px;
    text-align: right;
  }
}
.footer {
  text-align: center;
  margin-top: 20px;
}
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
  }
}
</style>
