<template>
  <el-select
    :value="value"
    placeholder="选择SKU"
    filterable
    remote
    clearable
    multiple
    :remote-method="remoteMethod"
    :loading="loading"
    @change="handleSelectChange"
  >
    <el-option
      v-for="(item, index) in skuList"
      :key="item.skuNo"
      :label="item.skuNo"
      :value="item.skuNo"
      :disabled="index === 0"
    >
      <div
        class="selectItem"
        :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
      >
        <div>{{ item.skuNo || "--" }}</div>
        <div :title="item.materialDescription">
          {{ item.materialDescription || "--" }}
        </div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { getSkuOptions } from '@/api/stockpile';
export default {
  props: ['value', 'type'],
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      loading: false,
      skuList: []
    };
  },
  methods: {
    handleSelectChange(val) {
      this.$emit('change', val);
    },
    remoteMethod(val) {
      const key = val.trim();
      if (key !== '') {
        this.loading = true;
        getSkuOptions(val, this.type).then((data) => {
          this.loading = false;
          if (data && data.length) {
            this.skuList = [
              {
                skuNo: '商品编号',
                materialDescription: '商品描述'
              },
              ...data
            ];
          }
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
