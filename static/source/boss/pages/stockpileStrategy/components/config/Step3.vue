<template>
  <el-card>
    <div class="chart-wrap">
      <p style="font-size: 16px;margin: 30px 0;"><i class="el-icon-success" style="color: #67C23A;"></i>您本次完成<b style="color: #f00;">{{ step3Info.skuCount }}</b>个商品备货策略配置，5-20号模型会按照您的策略计算当月的备货参数！</p>
      <Chart chart-id="chart2" :data="step3Info.chartData" style="width: 500px; height: 500px;" />
    </div>
  </el-card>
</template>

<script>
import Chart from './Chart'
export default {
  props: {
    step3Info: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    Chart
  }
}
</script>

<style scoped>
.chart-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
</style>
