<template>
  <div>
    <el-card>
      <el-form ref="searchForm" :model="searchForm" label-width="100px"  label-position="right">
        <el-row>
          <el-col :span="6">
            <el-form-item label="物料组">
              <MaterialGroup v-model="searchForm.materialGroup" multiple style="width: 100%" placeholder="输入关键词" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="sku编码">
              <el-input style="width: 100%;" v-model="searchForm.sku" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌">
              <Brand v-model="searchForm.brandId" multiple style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品经理">
              <SelectSecurityUser v-model="searchForm.pmId" style="width: 100%" :options="userOptions" :username="userName" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="类目">
              <Catalog v-model="searchForm.category" multiple style="width: 100%" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="产品定位">
              <el-select
                v-model="searchForm.productPositioning"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in productPositioningNameList"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货月份">
              <el-date-picker
                clearable
                v-model="searchForm.batchDate"
                type="month"
                value-format="yyyy-MM"
                style="width: 100%"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="searchForm.status"
                filterable
                clearable
                style="width: 100%"
                placeholder="请选择状态"
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.key"
                  :value="item.key"
                  :label="item.label"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="expand">
          <el-col :span="6">
            <el-form-item label="策略1-库存周转天数">
              <el-input-number v-model="searchForm.strategy1MinDays" style="width: 45%;" :min="0" :controls="false" placeholder="最小值" />
              <span style="display: inline-block; width: 10%; text-align: center">-</span>
              <el-input-number v-model="searchForm.strategy1MaxDays" style="width: 45%;" :min="0" :controls="false" placeholder="最大值" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="策略2-库存周转天数">
              <el-input-number v-model="searchForm.strategy2MinDays" style="width: 45%;" :min="0" :controls="false" placeholder="最小值" />
              <span style="display: inline-block; width: 10%; text-align: center">-</span>
              <el-input-number v-model="searchForm.strategy2MaxDays" style="width: 45%;" :min="0" :controls="false" placeholder="最大值" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="策略3-库存周转天数">
              <el-input-number v-model="searchForm.strategy3MinDays" style="width: 45%;" :min="0" :controls="false" placeholder="最小值" />
              <span style="display: inline-block; width: 10%; text-align: center">-</span>
              <el-input-number v-model="searchForm.strategy3MaxDays" style="width: 45%;" :min="0" :controls="false" placeholder="最大值" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item style="width: 100%;" label="策略4-库存周转天数" prop="status">
              <el-input-number v-model="searchForm.strategy4MinDays" style="width: 45%;" :min="0" :controls="false" placeholder="最小值" />
              <span style="display: inline-block; width: 10%; text-align: center">-</span>
              <el-input-number v-model="searchForm.strategy4MaxDays" style="width: 45%;" :min="0" :controls="false" placeholder="最大值" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" style="text-align: right">
            <i v-if="!expand" @click="expand = !expand" class="el-icon-arrow-down cursor-pointer"></i>
            <i v-else @click="expand = !expand" class="el-icon-arrow-up cursor-pointer"></i>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button type="primary" :loading="loading" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset" :loading="loading">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card style="margin-top: 10px">
      <vxe-toolbar ref="toolbarRef" custom>
        <template #buttons>
          <div>您还有<b style="color: #f00">{{ unConfigSkuNum }}</b>个商品未设定备货策略，请在本月19号前完成设定</div>
        </template>
        <template #tools>
          <el-upload
            action="/api-ab/skuInventoryStrategy/importExcel"
            :show-file-list="false"
            :on-success="onUploadSuccess"
            :on-error="onUploadError"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls,.xlsm,application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            style="display: inline-block; margin-right: 10px"
            name="excelFile"
          >
            <el-button
              type="primary"
              style="margin-right: 5px"
              :loading="importLoading"
              >导入</el-button
            >
          </el-upload>
          <el-button type="primary" :loading="exportLoading" @click="handleExport">导出</el-button>
        </template>
      </vxe-toolbar>
      <vxe-table
        border
        resizable
        :data="tableData"
        height="560"
        :loading="loading"
      >
        <vxe-table-column width="268" field="sku" title="SKU">
          <template v-slot="{ row }">
            <div class="flex">
              <el-image
                :src="row.skuPicture"
                style="width: 60px; height: 68px; flex-shrink: 0;"
              />
              <div style="width: 172px;margin-left: 10px">
                <div class="ellipsis" style="font-size:11px;color: #409EFF">{{ row.materialDescription }}</div>
                <div>sku:{{ row.sku }}</div>
                <div v-if="row.skuTagsDesc">
                  <el-tag style="margin-right: 2px" v-for="item in row.skuTagsDesc.split(' ')" :key="item" size="mini">{{ item }}</el-tag>
                </div>
              </div>
            </div>
          </template>
        </vxe-table-column>
        <vxe-table-column width="10%" field="factory" title="工厂">
          <template v-slot="{ row }">
            <span>{{ row.factory + ' ' + row.factoryName }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column width="10%" field="lastStrategy" title="上月策略">
          <template v-slot="{ row }">
            <span>{{ row.lastMonthStrategyStockOwner ? (stockTypeOptions.find(it => it.value === row.lastMonthStrategyStockOwner) || {}).label : '' }}</span><br /><span>{{ row.lastMonthStrategyTargetAging }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column width="220" field="strategy1" title="本月策略1">
          <template #header>
            <el-tooltip effect="dark" content="备货方+目标履约天数" placement="top">
              <span class="required">本月策略1<i class="el-icon-question"></i></span>
            </el-tooltip>
          </template>
          <template v-slot="{ row }">
            <el-select :disabled="row.status === '已完成'" @change="handleStockTypeChange(row, 1)" style="width: 98px; margin-right: 12px;" v-model="row.thisMonthStrategy1StockOwner" placeholder="请选择">
              <el-option v-for="item in stockTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-select :disabled="row.status === '已完成'" @change="handleChange(row, 1)" style="width: 88px" v-model="row.thisMonthStrategy1TargetAging" placeholder="请选择">
              <el-option v-for="item in numberList" :label="item.label" :value="item.value" :key="item.value"></el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column width="220" field="strategy2" title="本月策略2">
          <template v-slot="{ row }">
            <el-select :disabled="row.status === '已完成'" @change="handleStockTypeChange(row, 2)" style="width: 98px; margin-right: 12px;" v-model="row.thisMonthStrategy2StockOwner" placeholder="请选择">
              <el-option v-for="item in stockTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-select :disabled="row.status === '已完成'" @change="handleChange(row, 2)" style="width: 88px" v-model="row.thisMonthStrategy2TargetAging" placeholder="请选择">
              <el-option v-for="item in numberList" :label="item.label" :value="item.value" :key="item.value"></el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column width="220" field="strategy3" title="本月策略3">
          <template v-slot="{ row }">
            <el-select :disabled="row.status === '已完成'" @change="handleStockTypeChange(row, 3)" style="width: 98px; margin-right: 12px;" v-model="row.thisMonthStrategy3StockOwner" placeholder="请选择">
              <el-option v-for="item in stockTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-select :disabled="row.status === '已完成'" @change="handleChange(row, 3)" style="width: 88px" v-model="row.thisMonthStrategy3TargetAging" placeholder="请选择">
              <el-option v-for="item in numberList" :label="item.label" :value="item.value" :key="item.value"></el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column width="220" field="strategy4" title="本月策略4">
          <template v-slot="{ row }">
            <el-select :disabled="row.status === '已完成'" @change="handleStockTypeChange(row, 4)" style="width: 98px; margin-right: 12px;" v-model="row.thisMonthStrategy4StockOwner" placeholder="请选择">
              <el-option v-for="item in stockTypeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
            <el-select :disabled="row.status === '已完成'" @change="handleChange(row, 4)" style="width: 88px" v-model="row.thisMonthStrategy4TargetAging" placeholder="请选择">
              <el-option v-for="item in numberList" :label="item.label" :value="item.value" :key="item.value"></el-option>
            </el-select>
          </template>
        </vxe-table-column>
        <vxe-table-column width="10%" field="strategy" title="最终策略">
          <template v-slot="{ row }">
            <span>{{ row.thisMonthFinalStrategyStockOwner ? (stockTypeOptions.find(it => it.value === row.thisMonthFinalStrategyStockOwner) || {}).label : '' }}</span><br /><span>{{ row.thisMonthFinalStrategyTargetAging }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column width="6%" field="status" title="状态">
          <template v-slot="{ row }">
            <span :style="{ color: row.status === '已完成' ? 'green' : 'red'}">{{ row.status }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column width="6%" field="month" title="备货月份"></vxe-table-column>
        <vxe-colgroup title="策略1">
          <vxe-column width="80" field="strategy1InventoryHoldCost" title="持有成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy1FulfillmentCost" title="履约成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy1FulfillmentAging" title="履约天数" :visible="false"></vxe-column>
          <vxe-column width="100" field="strategy1StockTurnOverDays" title="库存周转天数" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy1AvailabilityRate" title="有货率" :visible="false"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="策略2">
          <vxe-column width="80" field="strategy2InventoryHoldCost" title="持有成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy2FulfillmentCost" title="履约成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy2FulfillmentAging" title="履约天数" :visible="false"></vxe-column>
          <vxe-column width="100" field="strategy2StockTurnOverDays" title="库存周转天数" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy2AvailabilityRate" title="有货率" :visible="false"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="策略3">
          <vxe-column width="80" field="strategy3InventoryHoldCost" title="持有成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy3FulfillmentCost" title="履约成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy3FulfillmentAging" title="履约天数" :visible="false"></vxe-column>
          <vxe-column width="100" field="strategy3StockTurnOverDays" title="库存周转天数" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy3AvailabilityRate" title="有货率" :visible="false"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="策略4">
          <vxe-column width="80" field="strategy4InventoryHoldCost" title="持有成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy4FulfillmentCost" title="履约成本" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy4FulfillmentAging" title="履约天数" :visible="false"></vxe-column>
          <vxe-column width="100" field="strategy4StockTurnOverDays" title="库存周转天数" :visible="false"></vxe-column>
          <vxe-column width="80" field="strategy4AvailabilityRate" title="有货率" :visible="false"></vxe-column>
        </vxe-colgroup>
        <vxe-column width="100" field="skuSourceTypeName" title="商品来源" :visible="false"></vxe-column>
        <vxe-column width="100" field="pmName" title="商品经理" :visible="false"></vxe-column>
        <vxe-column width="100" field="materialGroupName" title="物料组" :visible="false"></vxe-column>
        <vxe-column width="100" field="brandName" title="品牌" :visible="false"></vxe-column>
        <vxe-column width="100" field="productPositioningName" title="产品定位" :visible="false"></vxe-column>
        <vxe-column width="120" field="lastThreeMonthsAvailabilityRate" title="近三个月有货率" :visible="false"></vxe-column>
        <vxe-column width="100" field="lt" title="LT" :visible="false"></vxe-column>
        <vxe-column width="100" field="bigStoneRatio" title="大石头比例" :visible="false"></vxe-column>
        <vxe-column width="100" field="bigStoneCustomer" title="大石头客户" :visible="false">
          <template v-slot="{ row }">
            【{{row.bigStoneCustomerCode}}】{{ row.bigStoneCustomer }}
          </template>
        </vxe-column>
        <vxe-colgroup title="90天">
          <vxe-column width="80" field="salesVolume90Days" title="销量" :visible="false"></vxe-column>
          <vxe-column width="80" field="salesFrequency90Days" title="动销次数" :visible="false"></vxe-column>
          <vxe-column width="80" field="differentCustomers90Days" title="客户数" :visible="false"></vxe-column>
          <vxe-column width="80" field="quoteInquiries90Days" title="询价次数" :visible="false"></vxe-column>
          <vxe-column width="80" field="quoteVolume90Days" title="询价数量" :visible="false"></vxe-column>
        </vxe-colgroup>
        <vxe-colgroup title="90-180天">
          <vxe-column width="80" field="salesVolume180Days" title="销量" :visible="false"></vxe-column>
          <vxe-column width="80" field="salesFrequency180Days" title="动销次数" :visible="false"></vxe-column>
          <vxe-column width="80" field="differentCustomers180Days" title="客户数" :visible="false"></vxe-column>
          <vxe-column width="80" field="quoteInquiries180Days" title="询价次数" :visible="false"></vxe-column>
          <vxe-column width="80" field="quoteVolume180Days" title="询价数量" :visible="false"></vxe-column>
        </vxe-colgroup>
        <template #empty>
          <el-empty description="暂无数据"></el-empty>
        </template>
      </vxe-table>
      <vxe-pager
        background
        size="medium"
        :current-page="pageInfo.pageNum"
        :page-size="pageInfo.pageSize"
        :total="pageInfo.total"
        :layouts="['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total']"
        @page-change="handlePageChange">
      </vxe-pager>
    </el-card>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'
import Catalog from '@/components/SearchFields/allDirectoryCascader';
import MaterialGroup from '@/components/SearchFields/materialGroup';
import Brand from '@/components/SearchFields/brand';
import SelectSecurityUser from '@/components/SearchFields/selectSecurityUser.vue';
import { productPositioningNameList } from '../../utils/index';
import { exportStockConfig, getStockConfigList, updateStockConfigItem, getUnConfigSkuNum } from '@/api/stockpileNew'
import { getUserInfo } from '@/api/mrp'
import { stockConfigTypeOptions } from '../../newModelStock/constant'
export default {
  components: {
    Catalog,
    MaterialGroup,
    Brand,
    SelectSecurityUser
  },
  computed: {
    ...mapState(['userRole']),
    ...mapState(['user']),
    isStockStaff() {
      return !!~this.userRole.indexOf('data-模型备货策略');
    },
    isStockCheckStaff() {
      return !!~this.userRole.indexOf('data-模型备货审核');
    }
  },
  data() {
    return {
      searchForm: {
        materialGroup: [],
        brandId: [],
        category: [],
        status: '未完成',
        batchDate: moment().format('YYYY-MM'),
        pmId: '',
        sku: '',
        productPositioning: '',
        strategy1MaxDays: undefined,
        strategy1MinDays: undefined,
        strategy2MaxDays: undefined,
        strategy2MinDays: undefined,
        strategy3MaxDays: undefined,
        strategy3MinDays: undefined,
        strategy4MaxDays: undefined,
        strategy4MinDays: undefined
      },
      expand: false,
      tableData: [],
      pageInfo: {
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      importLoading: false,
      exportLoading: false,
      productPositioningNameList,
      userName: '',
      unConfigSkuNum: 0,
      userOptions: [],
      numberList: [{
        label: '1',
        value: 1
      }, {
        label: '3',
        value: 3
      }, {
        label: '5',
        value: 5
      }, {
        label: '8',
        value: 8
      }],
      stockTypeOptions: stockConfigTypeOptions,
      statusOptions: [{
        key: '',
        label: '全部'
      }, {
        key: '未完成',
        label: '未完成'
      }, {
        key: '已完成',
        label: '已完成'
      }]
    }
  },
  methods: {
    prepareSearchParam() {
      const category = !this.searchForm.category ? undefined : this.searchForm.category.map(item => item[3]);
      const sku = !this.searchForm.sku ? undefined : this.searchForm.sku?.trim()
        .split(/[\n\r\s;；, ]/g)
        .filter(Boolean);
      if (this.searchForm.strategy1MinDays >= 0 && this.searchForm.strategy1MaxDays >= 0 && this.searchForm.strategy1MinDays > this.searchForm.strategy1MaxDays) {
        this.$message.error({ message: '最小值不能大于最大值！' });
        return false
      }
      if (this.searchForm.strategy2MinDays >= 0 && this.searchForm.strategy2MaxDays >= 0 && this.searchForm.strategy2MinDays > this.searchForm.strategy2MaxDays) {
        this.$message.error({ message: '最小值不能大于最大值！' });
        return false
      }
      if (this.searchForm.strategy3MinDays >= 0 && this.searchForm.strategy3MaxDays >= 0 && this.searchForm.strategy3MinDays > this.searchForm.strategy3MaxDays) {
        this.$message.error({ message: '最小值不能大于最大值！' });
        return false
      }
      if (this.searchForm.strategy4MinDays >= 0 && this.searchForm.strategy4MaxDays >= 0 && this.searchForm.strategy4MinDays > this.searchForm.strategy4MaxDays) {
        this.$message.error({ message: '最小值不能大于最大值！' });
        return false
      }
      return { ...this.searchForm, pageNum: this.pageInfo.pageNum, pageSize: this.pageInfo.pageSize, category, sku };
    },
    async getList() {
      const param = this.prepareSearchParam();
      if (!param) {
        return
      }
      this.loading = true
      try {
        const res = await getStockConfigList(param)
        if (res.code === 200) {
          this.tableData = (res.data || [])
          this.pageInfo.total = res.total
        } else {
          this.$message.error({
            message:
              (res && (res.message || res.msg)),
            duration: 6000
          })
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    handleSearch() {
      this.pageNum = 1
      this.getList()
    },
    handleReset() {
      this.searchForm.strategy1MaxDays = undefined
      this.searchForm.strategy1MinDays = undefined
      this.searchForm.strategy2MaxDays = undefined
      this.searchForm.strategy2MinDays = undefined
      this.searchForm.strategy3MaxDays = undefined
      this.searchForm.strategy3MinDays = undefined
      this.searchForm.strategy4MaxDays = undefined
      this.searchForm.strategy4MinDays = undefined
      this.searchForm.materialGroup = []
      this.searchForm.brandId = []
      this.searchForm.category = []
      this.searchForm.status = '未完成'
      this.searchForm.batchDate = moment().format('YYYY-MM')
      this.searchForm.pmId = this.user.userID
      this.searchForm.sku = ''
      this.searchForm.productPositioning = ''
      this.userOptions = [{
        value: this.user.userID,
        label: this.userName
      }]
      this.handleSearch()
    },
    handlePageChange({ currentPage, pageSize }) {
      this.pageInfo.pageNum = currentPage
      this.pageInfo.pageSize = pageSize
      this.getList()
    },
    async handleChange(row, num) {
      const val = row[`thisMonthStrategy${num}TargetAging`]
      this.$set(row, `thisMonthStrategy${num}TargetAging`, val)
      if (val > 0) {
        const {
          thisMonthStrategy1TargetAging,
          thisMonthStrategy2TargetAging,
          thisMonthStrategy3TargetAging,
          thisMonthStrategy4TargetAging,
          thisMonthStrategy1StockOwner,
          thisMonthStrategy2StockOwner,
          thisMonthStrategy3StockOwner,
          thisMonthStrategy4StockOwner,
          id
        } = row
        // 调用行更新
        const res = await updateStockConfigItem({
          thisMonthStrategy1TargetAging,
          thisMonthStrategy2TargetAging,
          thisMonthStrategy3TargetAging,
          thisMonthStrategy4TargetAging,
          thisMonthStrategy1StockOwner,
          thisMonthStrategy2StockOwner,
          thisMonthStrategy3StockOwner,
          thisMonthStrategy4StockOwner,
          id
        })
        if (res.code !== 200) {
          this.$message.error({
            message: res.msg,
            duration: 1500
          })
        }
      } else {
        this.$message.error({
          message: `请选择策略${num}目标履约天数`,
          duration: 1500
        })
      }
    },
    handleStockTypeChange(row, num) {
      this.$set(row, `thisMonthStrategy${num}TargetAging`, '')
      this.handleChange(row, num)
    },
    async handleSave() {
      try {
        if (!this.tableData.length) {
          this.$message.error({
            message: '配置SKU列表不能为空',
            duration: 1500
          })
          return false
        }
        const queryParams = this.prepareSearchParam();
        if (queryParams) {
          return queryParams
        } else {
          return false
        }
      } catch (error) {
        this.$message.error({
          message: error.message,
          duration: 6000
        });
        return false
      }
    },
    handleExport() {
      const param = this.prepareSearchParam();
      if (!param) {
        return
      }
      this.exportLoading = true;
      exportStockConfig({ ...param })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success(res.data || res.msg || '导出成功！');
          } else {
            this.$message.error({
              message:
                (res && (res.message || res.msg)) || '导出失败！',
              duration: 6000
            })
          }
        })
        .catch((err) => {
          this.$message.error({
            message: err.msg || err.message || '导出失败！',
            duration: 6000
          });
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    async getUser() {
      const res = await getUserInfo({ username: window.CUR_DATA.user && window.CUR_DATA.user.name })
      this.userName = res.nickname;
    },
    async getNum() {
      const res = await getUnConfigSkuNum()
      this.unConfigSkuNum = res.data || 0
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.importLoading = true;
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.code === 200) {
        this.$message.success(response.data || '导入成功！');
        this.handleSearch();
      } else {
        let msg = ((response && response.msg) || '导入失败！').replace(/\n/g, '<br/>')
        this.$message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          showClose: true,
          duration: 3000
        })
      }
    },
    onUploadError(error) {
      this.importLoading = false;
      let msg = ((error && error.msg) || (error && error.message) || '上传失败').replace(/\n/g, '<br/>')
        this.$message({
          message: msg,
          type: 'error',
          dangerouslyUseHTMLString: true,
          showClose: true,
          duration: 3000
        })
    }
  },
  async created() {
    this.searchForm.pmId = this.user.userID
    this.getUser()
  },
  activated() {
    this.getNum()
    this.getList()
  }
}
</script>

<style>
.cursor-pointer {
  cursor: pointer;
}
.flex {
  display: flex;
}
.ellipsis {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.required::before {
  content: '*';
  color: #ff7268;
  margin-right: 4px;
}
.required::after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
</style>
