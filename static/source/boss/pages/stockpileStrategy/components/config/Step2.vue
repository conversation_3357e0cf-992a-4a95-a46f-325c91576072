
<template>
  <el-card :body-style="{display: 'flex'}">
    <Chart chart-id="chart1" :data="chartData" style="width: 500px; height: 500px;flex-shrink: 0;" />
    <div class="table">
      <vxe-table border :data="tableData" @radio-change="radioChange">
        <vxe-column align="center" width="60" type="radio" title="请选择" />
        <vxe-column align="center" width="80" title="策略" field="strategyName" />
        <vxe-column align="center" width="15%" title="备货SKU量" field="skuCount" />
        <vxe-column align="center" width="15%" title="库存持有成本" field="inventoryHoldCost" />
        <vxe-column align="center" width="15%" title="履约成本" field="fulfillmentCost" />
        <vxe-column align="center" width="15%" title="平均履约时效" field="fulfillmentAging" />
        <vxe-column align="center" width="15%" title="库存周转天数" field="strategy1StockTurnOverDays" />
        <vxe-column align="center" title="有货率" field="strategy1AvailabilityRate" />
      </vxe-table>
    </div>
  </el-card>
</template>

<script>
import Chart from './Chart.vue'
import { submitCalStrategy, getCalStrategyList } from '@/api/stockpileNew'
export default {
  props: {
    queryParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: {
    Chart
  },
  computed: {
    chartData: function() {
      return this.tableData?.map(item => {
        return {
          name: item.strategyName,
          value: [item.skuCount, item.inventoryHoldCost, item.fulfillmentCost, item.fulfillmentAging, item.strategy1StockTurnOverDays, item.strategy1AvailabilityRate]
        }
      })
    }
  },
  activated () {
    this.getData()
  },
  data () {
    return {
      tableData: [],
      selectRow: null
    }
  },
  methods: {
    async handleSave () {
      if (!this.selectRow) {
        this.$message.error('请选择本月备货策略')
        return false
      }
      return new Promise((resolve) => {
        this.$confirm('请您再次确认选择策略', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await this.saveRequest()
          resolve(res)
        }).catch(() => {
          resolve(false);
        })
      })
    },
    async saveRequest () {
      try {
        const res = await submitCalStrategy({ ...this.queryParams, ...this.selectRow });
        if (res.code === 200) {
          return {
            skuCount: this.selectRow.skuCount,
            chartData: this.chartData
          }
        } else {
          this.$message.error({
            message:
              (res && (res.data || res.msg)),
            duration: 1000
          })
          return false
        }
      } catch (error) {
        this.$message.error({
          message: error.message,
          duration: 1000
        });
        return false
      }
    },
    radioChange ({ row }) {
      this.selectRow = row
    },
    async getData () {
      try {
        const res = await getCalStrategyList(this.queryParams)
        if (res.code === 200) {
          this.tableData = res.data || []
        } else {
          this.$message.error(res.msg)
        }
      } catch (error) {
        this.$message.error(error.message)
      }
    }
  }
}
</script>

<style scoped>
.table {
  width: calc(100% - 500px);
}
</style>
