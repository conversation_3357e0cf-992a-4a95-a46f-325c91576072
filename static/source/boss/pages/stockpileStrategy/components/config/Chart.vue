<template>
  <div :id="chartId" style="width: 500px; height: 500px;flex-shrink: 0;"></div>
</template>
<script>
  const echart = window.echarts
  const findMax = (arr, key) => {
    let max = 0
    arr.forEach(item => {
      max = Math.max(max, item.value[key])
    })
    return max
  }
  export default {
    props: {
      chartId: {
        type: String,
        default: 'chart'
      },
      data: {
        type: Array,
        default: () => []
      }
    },
    watch: {
      data () {
        if (!this.myChart) {
          setTimeout(() => {
            this.setOption(this.myChart)
          }, 600)
          return
        }
        this.setOption(this.myChart)
      }
    },
    mounted () {
      this.myChart = echart.init(document.getElementById(this.chartId))
      this.setOption(this.myChart)
    },
    methods: {
      setOption (myChart) {
        myChart.setOption({
          title: {
            text: '策略数据预测图',
            left: 'center'
          },
          legend: {
            data: this.data.map(item => item.name),
            bottom: '0',
            left: 'center'
          },
          radar: {
            // shape: 'circle',
            indicator: [
              { name: '备货SKU量', max: findMax(this.data, 0) },
              { name: '库存持有成本', max: findMax(this.data, 1) },
              { name: '履约成本', max: findMax(this.data, 2) },
              { name: '平均履约时效', max: findMax(this.data, 3) },
              { name: '库存周转天数', max: findMax(this.data, 4) },
              { name: '有货率', max: findMax(this.data, 5) }
            ]
          },
          series: [
            {
              name: '',
              type: 'radar',
              label: {
                show: true,
                formatter: '{c}'
              },
              data: this.data
            }
          ]
        })
      }
    }
  }
</script>
