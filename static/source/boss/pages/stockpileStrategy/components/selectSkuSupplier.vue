<template>
  <el-select
    :value="value"
    placeholder="选择供应商"
    filterable
    default-first-option
    reserve-keyword
    style="width:100%"
    :disabled="disabled"
    :loading="loading"
    @change="handleChange"
  >
    <el-option
      v-for="(item, index) in supplierList"
      :key="item.providerNo"
      :label="item.providerName"
      :value="item.providerNo"
      :disabled="index===0"
    >
      <div
        class="selectItem"
        :style="{fontWeight:index===0?'bold':'normal'}"
      >
        <div>{{ item.providerNo || '--' }}</div>
        <div>{{ item.providerName || '--' }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { getSkuSupplier } from '@/api/mrp'
import { removeDuplicatesByKey } from '../utils/index'
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    list: {
      type: Array
    },
    value: {
      type: String
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    skuNo: {
      type: String
    },
    factory: {
      type: String
    }
  },
  data() {
    return {
      loading: false,
      supplierList: this.list || []
    }
  },
  watch: {
    'skuNo': {
      handler: function(val) {
        if (val) {
          getSkuSupplier({
            skuCodes: val,
            factoryCodes: this.factory,
            pageNum: 1,
            pageSize: 500
          }).then(res => {
            if (res && res.data) {
              const temp = res.data.map(item => ({
                providerNo: item.supplierCode,
                providerName: item.supplierName.split(' ')[1],
                supplierName: item.supplierName
              }))
              this.supplierList = [
                {
                  providerNo: '供应商编码',
                  providerName: '供应商名称'
                },
                ...removeDuplicatesByKey(temp, 'providerNo')
              ]
            }
          })
        }
      },
      immediate: true
    },
    'factory': {
      handler: function(val) {
        if (val && this.skuNo) {
          getSkuSupplier({
            skuCodes: this.skuNo,
            factoryCodes: val,
            pageNum: 1,
            pageSize: 500
          }).then(res => {
            if (res && res.data) {
              const temp = res.data.map(item => ({
                providerNo: item.supplierCode,
                providerName: item.supplierName.split(' ')[1],
                supplierName: item.supplierName
              }))
              this.supplierList = [
                {
                  providerNo: '供应商编码',
                  providerName: '供应商名称'
                },
                ...temp
              ]
            }
          })
        }
      }
    }
  },
  methods: {
    handleChange (val) {
      this.$emit('change', val, this.supplierList)
    }
  }
}
</script>

<style scoped>
.selectItem {
  display: flex;
}
.selectItem div:nth-child(1) {
  width: 100px;
}
.selectItem div:nth-child(2) {
  overflow: auto;
  width: 220px;
}
</style>
