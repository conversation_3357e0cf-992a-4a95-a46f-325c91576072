<template>
  <div class="app-container">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="140px"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="SKU" prop="skuSetStr">
              <el-input
                v-model="searchForm.skuSetStr"
                type="text"
                placeholder="支持多个SKU，用逗号或空格分隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工厂" prop="factory">
              <el-select
                v-model="searchForm.factorySetStr"
                placeholder="请选择"
                filterable
                collapse-tags
                multiple
                clearable
              >
                <el-option
                  v-for="item in factoryOptions"
                  :key="item.factory"
                  :label="item.factory + ' ' + item.factoryName"
                  :value="item.factory"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货仓" prop="warehouse">
              <el-select
                v-model="searchForm.warehouseSetStr"
                filterable
                clearable
                collapse-tags
                multiple
                value-key="warehouseCode"
                placeholder="请选择"
                @change="changeWarehouseCode"
              >
                <el-option
                  v-for="(item,index) in warehouseOptions"
                  :key="item.warehouseCode+index"
                  :label="item.warehouseName"
                  :value="item"

                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货库位" prop="position">
              <el-select
                v-model="searchForm.positionSetStr"
                filterable
                clearable
                collapse-tags
                multiple
                placeholder="请选择"
              >
                <el-option
                  v-for="(item, index) in designatedPosition"
                  :key="item.code + item.name + index"
                  :label="(item.code !== -1 ? item.code : '') + ' '+ item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="近三月预测准确率" prop="last3MPreviewRate">
              <el-input-number
                v-model="searchForm.accuracyLastThreeMonthsStart"
                clearable
                placeholder="最小值0"
                style="width: 45%"
                :controls="false"
                :min="0"
                :max="100"
              ></el-input-number>
              <span style="display: inline-block; width: 10%; text-align: center">-</span>
              <el-input-number
                v-model="searchForm.accuracyLastThreeMonthsEnd"
                clearable
                placeholder="最大值100"
                style="width: 45%"
                :controls="false"
                :min="0"
                :max="100"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组" prop="materialGroupSetStr">
              <MaterialGroup v-model="searchForm.materialGroupSetStr" collapse-tags multiple style="width: 100%" placeholder="输入关键词，支持多个" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brandId">
              <Brand v-model="searchForm.brandIdSetStr" collapse-tags multiple style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类目" prop="categorySetStr">
              <AllCatalog @change="changeCatalog" collapse-tags v-model="searchForm.categorySetStr" style="width: 100%" :checkStrictly="true" placeholder="支持多个" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="查看历史版本" prop="tagHistory">
              <el-switch
                v-model="searchForm.tagHistory"
                active-text="是"
                inactive-text="否"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="版本号" prop="versionNumber">
              <el-input
                v-model="searchForm.versionNumber"
                :disabled="!searchForm.tagHistory"
                type="text"
                placeholder="支持模糊搜索，示例：202401"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="searchForm.status"
                filterable
                clearable
              >
                <el-option
                  v-for="item in statusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否EVM备货" prop="tagEvm">
              <el-select
                v-model="searchForm.tagEvm"
                filterable
                clearable
              >
                <el-option
                  v-for="item in tagBoolOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="上月备货标识" prop="tagFinalStockLastMonth">
              <el-select
                v-model="searchForm.tagFinalStockLastMonth"
                filterable
                clearable
              >
                <el-option
                  v-for="item in tagBoolIncludeAllOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="本月备货标识" prop="tagFinalStock">
              <el-select
                v-model="searchForm.tagFinalStock"
                filterable
                clearable
              >
                <el-option
                  v-for="item in tagBoolIncludeAllOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container" style="margin-top: 20px;">
      <el-row type="flex" class="result-operation" justify="space-between">
        <el-col style="margin-top: 12px;" :span="4">查询结果</el-col>
        <el-col
          :span="20"
          style="
            text-align: right;
            justify-content: flex-end;
            align-items: start;
          "
        >
          <el-button
            type="primary"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            @click="handleBatchSubmit"
          >
            批量提交
          </el-button>
          <el-button
            v-if="isStockCheckStaff"
            :loading="operateLoading.approveAll"
            type="primary"
            @click="handleBatchCheck"
          >
            批量审核
          </el-button>
          <el-button
            v-if="isStockCheckStaff"
            type="primary"
            @click="handleBatchReject"
          >
            批量驳回
          </el-button>
          <el-button
            v-if="isStockCheckStaff"
            type="primary"
            @click="handleBatchClose"
          >
            批量关闭备货
          </el-button>
          <el-button
            type="primary"
            class="mr-10"
            @click="handleExportAll"
            :loading="exportLoading"
          >
            导出
          </el-button>
          <el-upload
            class="upload mr-10"
            action="/api-ab/inventoryStrategyCheck/import"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            :multiple="false"
            :limit="1"
            ref="upload"
            :show-file-list="true"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
          >
            <el-button :loading="uploadLoading" size="small" type="default">导入</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <zkh-table
        ref="approvalTable"
        :loading="basisListLoading"
        :data="basisList"
        :selectable="true"
        :checkSelectable="(row) => ['DRAFT', 'REJECT', 'CHECKING'].includes(row.status)"
        height="420"
        :columns="stockModelCheckColunms"
        :row-key="row => row.id"
      >
        <template #lastThreeMonthsAccuracy="scope">
          <a class="primary-blue mr-4px cursor-pointer" @click="handlePreview(scope.row)">{{ scope.row.lastThreeMonthsAccuracy }}</a>
        </template>
        <template #action="scope">
          <template v-if="scope.row.status === 'DRAFT' || scope.row.status === 'REJECT'">
            <a class="primary-blue mr-4px cursor-pointer" @click="handleEdit(scope.row)">编辑</a>
            <a class="primary-blue mr-4px cursor-pointer" @click="handleSubmit([scope.row])">提交</a>
            <a v-if="isStockCheckStaff" class="primary-blue cursor-pointer" @click="handleClose([scope.row])">关闭备货</a>
            <a v-if="scope.row.source !== 'AI'" class="primary-blue cursor-pointer" @click="handleDelete(scope.row)">删除</a>
          </template>
          <template v-if="scope.row.status === 'CHECKING'">
            <a v-if="isStockCheckStaff" class="primary-blue mr-4px cursor-pointer" @click="handleCheck([scope.row])">审核</a>
            <a v-if="isStockCheckStaff" class="primary-blue cursor-pointer" @click="handleReject([scope.row])">驳回</a>
          </template>
        </template>
      </zkh-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10, 20, 50, 100]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
    <AddDialog
      v-if="addDialogVisible"
      :visible="addDialogVisible"
      :factoryOptions="factoryOptions"
      :stockTypeOptions="stockTypeOptions"
      :sourceOptions="sourceOptions"
      :detail="detail"
      :actionType="actionType"
      :loading="loading"
      @submit="handleCreate"
      @update:visible="(value) => addDialogVisible = value"
    />
    <PreviewDialog
      v-if="previewDialogVisible"
      :visible="previewDialogVisible"
      :previewList="previewList"
      @update:visible="(value) => previewDialogVisible = value"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Pagination from '@/components/Pagination';
import {
  getStockApprovalList,
  addStockApproval,
  updateStockApproval,
  exportStockApproval,
  closeStockApproval,
  checkStockApproval,
  submitStockApproval,
  rejectStockApproval,
  deleteStockApproval,
  previewStockApprovalDetail,
  getWarehouseCode,
  getAllFactory,
  getAllRuleCode
} from '@/api/stockpileNew';
import { stockModelCheckColunms, initailOperateLoading, sourceOptions, tagBoolOptions, stockTypeOptions, statusOptions, tagBoolIncludeAllOptions } from './constant';
import AllCatalog from '@/components/SearchFields/allDirectoryCascader';
import MaterialGroup from '@/components/SearchFields/materialGroup';
import Brand from '@/components/SearchFields/brand';
import AddDialog from './approvalAddDialog';
import PreviewDialog from './approvalPreviewDialog';
import { removeDuplicatesByKey } from '../utils/index';

export default {
  name: 'ApprovalChecklist',
  data() {
    return {
      basisListLoading: false,
      tagBoolOptions,
      tagBoolIncludeAllOptions,
      stockTypeOptions,
      statusOptions,
      warehouseOptions: [],
      designatedPosition: [],
      factoryOptions: [],
      sourceOptions,
      exportLoading: false,
      uploadLoading: false,
      loading: false,
      basisList: [],
      searchForm: {
        brandIdSetStr: '',
        categorySetStr: '',
        materialGroupSetStr: '',
        skuSetStr: '',
        factorySetStr: '',
        warehouseSetStr: '',
        positionSetStr: '',
        status: '',
        tagHistory: false,
        versionNumber: '',
        tagEvm: ''
      },
      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      total: 0,
      operateLoading: { ...initailOperateLoading },
      addDialogVisible: false,
      stockModelCheckColunms,
      detail: {},
      actionType: '',
      previewDialogVisible: false,
      previewList: [],
      categorySetStrLabels: []
    };
  },
  components: {
    Pagination,
    AddDialog,
    PreviewDialog,
    AllCatalog,
    MaterialGroup,
    Brand
  },
  async created() {
    // 支持URL代入查询参数
    const {
      factory,
      tagEvm,
      position,
      sku,
      warehouse,
      warehouseName
    } = this.$route.query
    if (factory || sku) {
      this.searchForm.factorySetStr = factory ? [factory] : undefined
      this.searchForm.tagEvm = Number(tagEvm) ?? ''
      this.searchForm.positionSetStr = position ? [position] : undefined
      this.searchForm.skuSetStr = sku
      this.searchForm.warehouseSetStr = warehouse ? [{
        warehouseCode: warehouse,
        warehouseName: warehouseName
      }] : ''
    }
    this.handleFilter();
    let ruleCodeSet = JSON.parse(localStorage.getItem('ruleCodeSet') || '[]');
    if (ruleCodeSet.length === 0) {
      const res = await getAllRuleCode();
      ruleCodeSet = res.result.map(item => item.code);
      localStorage.setItem('ruleCodeSet', JSON.stringify(ruleCodeSet));
    }
    Promise.all([
      getWarehouseCode({ ruleCodeSet, positionScope: 1 }).then((data) => {
        this.warehouseOptions = removeDuplicatesByKey(data.result.filter(item => item.ifWarehouseSupportStockupIn === '1'));
        // URL参数 能够回显库位和备货仓
        if (warehouse && position) {
          const curPosition = this.warehouseOptions.find(item => item.warehouseCode === warehouse);
          this.designatedPosition = curPosition?.allPosition
          this.searchForm.warehouseSetStr[0].allPosition = curPosition?.allPosition
        }
      }),
      getAllFactory().then((data) => {
        this.factoryOptions = data.result;
      })
    ]);
  },
  computed: {
    ...mapState(['userRole']),
    isStockStaff() {
      return !!~this.userRole.indexOf('data-模型备货策略');
    },
    isStockCheckStaff() {
      return !!~this.userRole.indexOf('data-模型备货审核');
    }
  },
  methods: {
    changeCatalog(val, labels) {
      if (val) {
        this.categorySetStrLabels = labels
      } else {
        this.categorySetStrLabels = []
      }
    },
    changeWarehouseCode(selectedWareHouseCode) {
      const curPosition = selectedWareHouseCode.reduce((pre, cur) => pre?.concat(cur?.allPosition), [])
      this.designatedPosition = curPosition
    },
    getSelctedRows() {
      const selectableRows = this.$refs.approvalTable.getSelections()
      return selectableRows
    },
    // 删除按钮
    handleDelete(row) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteStockApproval([row.id])
        if (res.code !== 200) {
          this.$message.error(res.message || res.msg)
          return
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.handleFilter()
      })
    },
    // 新增按钮
    handleAdd() {
      this.actionType = 'add'
      this.addDialogVisible = true
    },
    // 编辑按钮
    handleEdit(row) {
      this.actionType = 'edit'
      this.detail = row
      this.addDialogVisible = true
    },
    // 关闭按钮
    async handleClose(rows) {
      const res = await closeStockApproval(rows.map(item => item.id))
      if (res.code !== 200) {
        this.$message.error(res.message || res.msg)
        return
      }
      this.$message({
        type: 'success',
        message: '关闭成功!'
      });
      this.handleFilter();
    },
    // 批量关闭
    handleBatchClose() {
      const selectableRows = this.getSelctedRows()
      if (selectableRows.length === 0) {
        this.$message.warning('请选择要关闭的记录!')
        return
      }
      this.$confirm('确认关闭吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.handleClose(selectableRows)
      })
    },
    // 审核按钮
    async handleCheck(rows) {
      const res = await checkStockApproval(rows.map(item => item.id))
      if (res.code !== 200) {
        this.$message.error(res.message || res.msg)
        return
      }
      this.$message({
        type: 'success',
        message: '审核成功!'
      });
      this.handleFilter();
    },
    // 批量审核
    handleBatchCheck() {
      const selectableRows = this.getSelctedRows()
      if (selectableRows.length === 0) {
        this.$message.warning('请选择要审核的记录!')
        return
      }
      this.handleCheck(selectableRows)
    },
    // 驳回
    async handleReject(rows) {
      this.$prompt('驳回原因:', '确认驳回', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea'
      }).then(async ({ value }) => {
        const res = await rejectStockApproval(rows.map(item => item.id), value)
        if (res.code !== 200) {
          this.$message.error(res.message || res.msg)
          return
        }
        this.$message({
          type: 'success',
          message: '驳回成功!'
        });
        this.handleFilter();
      })
    },
    // 批量驳回
    handleBatchReject() {
      const selectableRows = this.getSelctedRows()
      if (selectableRows.length === 0) {
        this.$message.warning('请选择要驳回的记录!')
        return
      }
      this.handleReject(selectableRows)
    },
    // 提交
    async handleSubmit(rows) {
      const res = await submitStockApproval(rows.map(item => item.id))
      if (res.code !== 200) {
        this.$message.error(res.message || res.msg)
        return
      }
      this.$message({
        type: 'success',
        message: '提交成功!'
      });
      this.handleFilter();
    },
    // 批量提交
    handleBatchSubmit() {
      const selectableRows = this.getSelctedRows()
      if (selectableRows.length === 0) {
        this.$message.warning('请选择要提交的记录!')
        return
      }
      this.handleSubmit(selectableRows)
    },
    // 预测准备率弹窗
    async handlePreview(row) {
      const res = await previewStockApprovalDetail(row)
      if (res.code !== 200) {
        this.$message.error(res.message || res.msg)
        return
      }
      this.previewList = res.result
      this.previewDialogVisible = true
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.searchForm.accuracyLastThreeMonthsStart = undefined
      this.searchForm.accuracyLastThreeMonthsEnd = undefined
      this.categorySetStrLabels = []
      this.searchForm.categorySetStr = []
      this.searchForm.materialGroupSetStr = ''
      this.searchForm.brandIdSetStr = ''
      this.searchForm.warehouseSetStr = ''
      this.searchForm.factorySetStr = ''
      this.searchForm.positionSetStr = ''
      this.handleFilter();
    },
    // 创建配置
    async handleCreate(formFields) {
      try {
        this.loading = true
        const res = this.actionType === 'edit' ? await updateStockApproval([{ ...formFields }]) : await addStockApproval([{ ...formFields }])
        if (res.code === 200) {
          this.$message.success(this.actionType === 'edit' ? '更新成功！' : '新增成功')
          this.addDialogVisible = false
          this.handleFilter()
        } else {
          this.$message.error({
            message:
              (res && (res.message || res.msg)) || '更新失败！',
            duration: 6000
          });
        }
      } catch (error) {
        this.$message.error('更新失败！')
      } finally {
        this.loading = false
      }
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.uploadLoading = true;
    },
    handleUploadSuccess(response) {
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
      if (response && response.code === 200) {
        this.$message.success(response.message || response.msg || '上传成功！');
        this.handleFilter();
      } else {
        this.$message.error({
          message:
            (response && (response.message || response.msg)) || '上传失败！',
          duration: 6000
        });
      }
    },
    handleUploadError(error) {
      this.uploadLoading = false;
      this.$message.error({
        message:
          (error && error.msg) || (error && error.message) || '上传失败!',
        duration: 6000
      });
    },
    prepareSearchParam() {
      if (this.searchForm.accuracyLastThreeMonthsStart >= 0 && this.searchForm.accuracyLastThreeMonthsEnd >= 0 && this.searchForm.accuracyLastThreeMonthsStart > this.searchForm.accuracyLastThreeMonthsEnd) {
        this.$message.error({ message: '预测准确率最小值不能大于最大值！' });
        return false
      }
      const categorySet = !this.searchForm.categorySetStr ? undefined : this.categorySetStrLabels.map(item => (item || []).join('/'));
      const materialGroupSet = !this.searchForm.materialGroupSetStr ? undefined : this.searchForm.materialGroupSetStr
      const brandIdSet = !this.searchForm.brandIdSetStr ? undefined : this.searchForm.brandIdSetStr
      const factorySet = !this.searchForm.factorySetStr ? undefined : this.searchForm.factorySetStr
      const warehouseSet = this.searchForm.warehouseSetStr?.length ? this.searchForm.warehouseSetStr.map(i => i.warehouseCode) : undefined
      const positionSet = !this.searchForm.positionSetStr ? undefined : this.searchForm.positionSetStr
      const versionNumber = this.searchForm.tagHistory ? this.searchForm.versionNumber : undefined
      const tagHistory = this.searchForm.tagHistory ? 1 : 0
      const skuSet = !this.searchForm.skuSetStr ? undefined : this.searchForm.skuSetStr.trim().split(/[\n\r\s;；, ]/g).filter(Boolean);
      return { ...this.searchForm, pageNum: this.listQueryInfo.current, pageSize: this.listQueryInfo.pageSize, categorySet, materialGroupSet, warehouseSet, versionNumber, tagHistory, factorySet, brandIdSet, positionSet, skuSet };
    },
    handleExportAll() {
      const param = this.prepareSearchParam();
      if (!param) {
        return
      }
      this.exportLoading = true;
      exportStockApproval({ ...param })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success(res.data || res.msg || '导出成功！');
          } else {
            this.$message.error({
              message:
                (res && (res.message || res.msg)) || '导出失败！',
              duration: 6000
            })
          }
        })
        .catch((err) => {
          this.$message.error({
            message: err.msg || err.message || '导出失败！',
            duration: 6000
          });
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    getBasisList() {
      const param = this.prepareSearchParam();
      if (!param) {
        return
      }
      this.basisList = [];
      this.basisListLoading = true;
      getStockApprovalList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              const data = res.data;
              this.total = res.total;
              this.basisList = data;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    }
  }
};
</script>

<style lang="scss">
.app-container {
  .tabelCellError {
    input {
      border: 1px solid #ff4949;
    }
    &:hover {
      input {
        border: 1px solid #ff4949;
      }
    }
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }
  .justify-between {
    justify-content: space-between;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .fw-500 {
    font-weight: 500;
  }
  .primary-blue {
    color: #7a95f1;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .input-number--micro {
    width: 100px;
  }
  .text-no-overflow {
    .cell {
      text-overflow: unset;
    }
  }
  .align-right {
    text-align: right;
  }
}
.upload {
  display: inline;
}
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.mr-4px {
  margin-right: 4px;
}
.cursor-pointer {
  cursor: pointer;
}
</style>
