<template>
  <div class="app-container stockpile-strategy-checklist">
    <div>
      <el-form
        ref="searchForm"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="6">
            <el-form-item label="来源" prop="source">
              <el-select
                v-model="searchForm.source"
                placeholder="请选择"
                filterable
                clearable
              >
                <el-option
                  v-for="item in sourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="SKU" prop="skuSetStr">
              <el-input
                v-model="searchForm.skuSetStr"
                type="text"
                placeholder="支持多个SKU，用逗号或空格分隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="工厂" prop="factory">
              <el-select
                v-model="searchForm.factorySetStr"
                placeholder="请选择"
                filterable
                collapse-tags
                :multiple="true"
                clearable
              >
                <el-option
                  v-for="item in factoryOptions"
                  :key="item.factory"
                  :label="item.factory + ' ' + item.factoryName"
                  :value="item.factory"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="物料组" prop="materialGroupSetStr">
              <MaterialGroup v-model="searchForm.materialGroupSetStr" collapse-tags multiple style="width: 100%" placeholder="输入关键词，支持多个" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="版本" prop="version">
              <el-input
                v-model="searchForm.version"
                type="text"
                placeholder="示例：202401"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类目" prop="categorySetStr">
              <AllCatalog @change="changeCatalog" v-model="searchForm.categorySetStr" collapse-tags :multiple="true" :checkStrictly="true" style="width: 100%" placeholder="支持多个" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌" prop="brandId">
              <Brand v-model="searchForm.brandIdSetStr" collapse-tags :multiple="true" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备货方" prop="stockType">
              <el-select
                v-model="searchForm.stockType"
                filterable
                clearable
              >
                <el-option
                  v-for="item in stockTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item label="备货仓" prop="warehouse">
              <el-select
                v-model="searchForm.warehouseSetStr"
                filterable
                clearable
                :multiple="true"
                collapse-tags
                style="width: 100%"
                value-key="warehouseCode"
                placeholder="请选择"
              >
                <el-option
                  v-for="(item,index) in warehouseOptions"
                  :key="item.warehouseCode+index"
                  :label="item.warehouseName"
                  :value="item"

                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="是否EVM备货" prop="tagEvm">
              <el-select
                v-model="searchForm.tagEvm"
                filterable
                clearable
              >
                <el-option
                  v-for="item in tagBoolOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" style="text-align: right">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result-container" style="margin-top: 20px;">
      <el-row>
        <el-col style="margin-top: 12px;" :span="4">查询结果</el-col>
        <el-col
          :span="20"
          style="
            text-align: right;
            justify-content: flex-end;
            align-items: center;
          "
        >
          <el-button
            :loading="operateLoading.approveAll"
            type="primary"
            :class="isStockStaff ? '' : 'mr-10'"
            @click="handleAdd"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            class="mr-10"
            @click="handleExportAll"
            :loading="exportLoading"
          >
            导出
          </el-button>
          <el-upload
            class="upload mr-10"
            action="/api-ab/inventoryStrategyConfig/import"
            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            :multiple="false"
            :limit="1"
            ref="upload"
            :show-file-list="true"
            :before-upload="handleBeforeUpload"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
          >
            <el-button :loading="uploadLoading" size="small" type="default">导入</el-button>
          </el-upload>
        </el-col>
      </el-row>
      <zkh-table
        :loading="basisListLoading"
        :data="basisList"
        height="420"
        :columns="stockModelListColunms"
        :row-key="row => row.id"
      >
        <template #action="scope">
          <a class="primary-blue mr-4px cursor-pointer" @click="handleEdit(scope.row)">编辑</a>
          <a class="primary-blue mr-4px cursor-pointer" @click="handleDelete(scope.row)">删除</a>
          <a v-if="scope.row.stockType === 'ZKH'" class="primary-blue cursor-pointer" @click="handleCalculate(scope.row)">试算</a>
        </template>
      </zkh-table>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10, 20, 50, 100]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
    <AddDialog
      v-if="addDialogVisible"
      :visible="addDialogVisible"
      :factoryOptions="factoryOptions"
      :stockTypeOptions="stockTypeOptions"
      :sourceOptions="sourceOptions"
      :detail="detail"
      :actionType="actionType"
      :loading="loading"
      @submit="handleCreate"
      @update:visible="(value) => addDialogVisible = value"
    />
    <PreviewDialog
      v-if="previewDialogVisible"
      :visible="previewDialogVisible"
      :result="result"
      @update:visible="(value) => previewDialogVisible = value"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Pagination from '@/components/Pagination';
import {
  getStockModeList,
  addStockMode,
  updateStockMode,
  deleteStockMode,
  previewStockModeDetail,
  exportStockMode,
  getWarehouseCode,
  getAllFactory,
  getAllRuleCode
} from '@/api/stockpileNew';
import { stockModelListColunms, initailOperateLoading, sourceOptions, tagBoolOptions, stockTypeOptions } from './constant';
import AllCatalog from '@/components/SearchFields/allDirectoryCascader';
import MaterialGroup from '@/components/SearchFields/materialGroup';
import Brand from '@/components/SearchFields/brand';
import AddDialog from './listAddDialog';
import PreviewDialog from './listCaclDialog';
import { removeDuplicatesByKey } from '../utils/index';

export default {
  name: 'configlist',
  data() {
    return {
      basisListLoading: false,
      tagBoolOptions,
      stockTypeOptions,
      warehouseOptions: [],
      factoryOptions: [],
      sourceOptions,
      exportLoading: false,
      uploadLoading: false,
      loading: false,
      basisList: [],
      searchForm: {
        brandIdSetStr: '',
        categorySetStr: '',
        materialGroupSetStr: '',
        skuSetStr: '',
        factorySetStr: '',
        warehouseSetStr: '',
        stockType: '',
        source: '',
        version: '',
        tagEvm: ''
      },
      listQueryInfo: {
        current: 1,
        pageSize: 10
      },
      total: 0,
      operateLoading: { ...initailOperateLoading },
      addDialogVisible: false,
      stockModelListColunms,
      detail: {},
      actionType: '',
      previewDialogVisible: false,
      result: {},
      categorySetStrLabels: []
    };
  },
  components: {
    Pagination,
    AddDialog,
    PreviewDialog,
    AllCatalog,
    MaterialGroup,
    Brand
  },
  async created() {
    this.handleFilter();
    let ruleCodeSet = JSON.parse(localStorage.getItem('ruleCodeSet') || '[]');
    if (ruleCodeSet.length === 0) {
      const res = await getAllRuleCode();
      ruleCodeSet = res.result.map(item => item.code);
      localStorage.setItem('ruleCodeSet', JSON.stringify(ruleCodeSet));
    }
    Promise.all([
      getWarehouseCode({ ruleCodeSet }).then((data) => {
        this.warehouseOptions = removeDuplicatesByKey(data.result.filter(item => item.ifWarehouseSupportStockupIn === '1'));
      }),
      getAllFactory().then((data) => {
        this.factoryOptions = data.result;
      })
    ]);
  },
  computed: {
    ...mapState(['userRole']),
    isStockStaff() {
      return !!~this.userRole.indexOf('data-模型备货策略');
    },
    isStockCheckStaff() {
      return !!~this.userRole.indexOf('data-模型备货审核');
    }
  },
  methods: {
    prepareSearchParam() {
      const categorySet = !this.searchForm.categorySetStr ? undefined : this.categorySetStrLabels.map(item => (item || []).join('/'));
      const materialGroupSet = !this.searchForm.materialGroupSetStr ? undefined : this.searchForm.materialGroupSetStr
      const brandIdSet = !this.searchForm.brandIdSetStr ? undefined : this.searchForm.brandIdSetStr
      const factorySet = !this.searchForm.factorySetStr ? undefined : this.searchForm.factorySetStr
      const warehouseSet = this.searchForm.warehouseSetStr?.length ? this.searchForm.warehouseSetStr.map(i => i.warehouseCode) : undefined
      const skuSet = !this.searchForm.skuSetStr ? undefined : this.searchForm.skuSetStr.trim().split(/[\n\r\s;；, ]/g).filter(Boolean);
      return { ...this.searchForm, pageNum: this.listQueryInfo.current, pageSize: this.listQueryInfo.pageSize, categorySet, materialGroupSet, warehouseSet, brandIdSet, factorySet, skuSet };
    },
    changeCatalog(val, labels) {
      if (val) {
        this.categorySetStrLabels = labels
      } else {
        this.categorySetStrLabels = []
      }
    },
    // 新增按钮
    handleAdd() {
      this.addDialogVisible = true
      this.actionType = 'add'
    },
    // 删除按钮
    handleDelete(row) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteStockMode([row.id])
        if (res.code !== 200) {
          this.$message.error(res.message || res.msg)
          return
        }
        this.$message({
          type: 'success',
          message: '删除成功!'
        });
        this.handleFilter()
      })
    },
    // 编辑按钮
    handleEdit(row) {
      this.detail = row
      this.actionType = 'edit'
      this.addDialogVisible = true
    },
    // 试算按钮
    async handleCalculate(row) {
      const data = {
        factory: row.factory,
        position: row.position,
        sku: row.sku,
        warehouse: row.warehouse,
        tagEvm: row.tagEvm,
        tagLevelStock: row.tagLevelStock,
        parentWarehouse: row.parentWarehouse,
        cityServerSet: row.cityServerCodeStr?.split(',')
      }
      const res = await previewStockModeDetail(data)
      if (![200, 300].includes(res.code)) {
        this.$message.error(res.message || res.msg)
        return
      }
      this.result = {
        previewList: res.data,
        previewMsg: res.code === 300 ? res.msg : '',
        code: res.code
      }
      this.previewDialogVisible = true
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    // 重置按钮
    handleReset() {
      this.$refs['searchForm'].resetFields();
      this.categorySetStrLabels = []
      this.searchForm.categorySetStr = []
      this.searchForm.materialGroupSetStr = ''
      this.searchForm.brandIdSetStr = ''
      this.searchForm.warehouseSetStr = ''
      this.searchForm.factorySetStr = ''
      this.handleFilter();
    },
    // 创建配置
    async handleCreate(formFields) {
      try {
        this.loading = true
        const res = this.actionType === 'edit' ? await updateStockMode([{ ...formFields }]) : await addStockMode([{ ...formFields }])
        if (res.code === 200) {
          this.$message.success(this.actionType === 'edit' ? '更新成功！' : '新增成功')
          this.addDialogVisible = false
          this.handleFilter()
        } else {
          this.$message.error({
            message:
              (res && (res.message || res.msg)) || '更新失败！',
            duration: 6000
          });
        }
      } catch (error) {
        this.$message.error('更新失败！')
      } finally {
        this.loading = false
      }
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.uploadLoading = true;
    },
    handleUploadSuccess(response) {
      this.uploadLoading = false;
      this.$refs.upload.clearFiles();
      if (response && response.code === 200) {
        this.$message.success(response.message || response.msg || '上传成功！');
        this.handleFilter();
      } else {
        this.$message.error({
          message:
            (response && (response.message || response.msg)) || '上传失败！',
          duration: 6000
        });
      }
    },
    handleUploadError(error) {
      this.uploadLoading = false;
      this.$message.error({
        message:
          (error && error.msg) || (error && error.message) || '上传失败!',
        duration: 6000
      });
    },
    async handleExportAll() {
      const param = this.prepareSearchParam();
      this.exportLoading = true;
      try {
        const res = await exportStockMode({ ...param });
        if (res.code === 200) {
          this.$message.success({
            message: res.data || '导出成功！',
            duration: 6000
          });
        } else {
          this.$message.error({
            message:
              (res && (res.data || res.msg)) || '导出失败！',
            duration: 6000
          })
        }
      } catch (error) {
        this.$message.error({
          message: '导出失败！',
          duration: 6000
        });
      } finally {
        this.exportLoading = false;
      }
    },
    getBasisList() {
      const param = this.prepareSearchParam();
      this.basisList = [];
      this.basisListLoading = true;
      getStockModeList(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              const data = res.data;
              this.total = res.total;
              this.basisList = data;
            }
          } else {
            this.$message.error({ message: res.msg, duration: 6000 });
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    }
  }
};
</script>

<style lang="scss">
.stockpile-strategy-checklist {
  .tabelCellError {
    input {
      border: 1px solid #ff4949;
    }
    &:hover {
      input {
        border: 1px solid #ff4949;
      }
    }
  }

  .el-select {
    width: 100%;
  }

  .flex {
    display: flex;
  }
  .justify-between {
    justify-content: space-between;
  }
  .pr-10 {
    padding-right: 10px;
  }
  .fw-500 {
    font-weight: 500;
  }
  .primary-blue {
    color: #7a95f1;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .input-number--micro {
    width: 100px;
  }
  .text-no-overflow {
    .cell {
      text-overflow: unset;
    }
  }
  .align-right {
    text-align: right;
  }
}
.upload {
  display: inline;
}
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.mr-4px {
  margin-right: 4px;
}
.cursor-pointer {
  cursor: pointer;
}
</style>
