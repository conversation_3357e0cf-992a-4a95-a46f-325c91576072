export const stockModelListColunms = [
  {
    label: '来源',
    prop: 'source',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === 'AI' ? 'AI' : '人工',
    fixed: 'left',
    type: ''
  },
  {
    label: '版本',
    prop: 'version',
    minWidth: 100,
    align: 'center',
    fixed: 'left',
    type: ''
  },
  {
    label: 'sku',
    prop: 'sku',
    minWidth: 100,
    align: 'center',
    fixed: 'left',
    type: ''
  },
  {
    label: '物料描述',
    prop: 'skuDesc',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    fixed: 'left',
    type: ''
  },
  {
    label: '工厂',
    prop: 'factoryName',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    fixed: 'left',
    type: ''
  },
  {
    label: '备货方',
    prop: 'stockType',
    minWidth: 120,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === 'ZKH' ? '震坤行' : '商家',
    type: ''
  },
  {
    label: '备货仓',
    prop: 'warehouseName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => ceilValue ? row.warehouse + ' ' + ceilValue : '',
    type: ''
  },
  {
    label: '备货库位',
    prop: 'positionName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => ceilValue ? row.position + ' ' + ceilValue : '',
    type: ''
  },
  {
    label: '供应商',
    prop: 'supplierName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '是否系统对接',
    prop: 'tagSystemApi',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : '否',
    type: ''
  },
  {
    label: '近3月商家有货率',
    prop: 'last3MInStockRateStr',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '服务范围',
    prop: 'cityServerNameStr',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '是否级库存',
    prop: 'tagLevelStock',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : '否',
    type: ''
  },
  {
    label: '是否EVM备货',
    prop: 'tagEvm',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : '否',
    type: ''
  },
  {
    label: '上级仓',
    prop: 'parentWarehouseName',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '物料组',
    prop: 'materialGroupName',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '类目',
    prop: 'category',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '品牌',
    prop: 'brandName',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '核心规格',
    prop: 'coreSpecification',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '制造商型号',
    prop: 'manufacturerMaterialNo',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '创建人',
    prop: 'creator',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '操作',
    prop: 'operate',
    minWidth: 140,
    align: 'center',
    fixed: 'right',
    type: 'custom',
    columnSlot: 'action'
  }
]
export const initailOperateLoading = {
  acceptBatched: false,
  approveBatched: false,
  readjustBatched: false,
  acceptAll: false,
  approveAll: false,
  rejectAll: false,
  stockStaffCheckAll: false,
  stockStaffReIssue: false
};
export const sourceOptions = [
  {
    value: 'AI',
    label: 'AI'
  },
  {
    value: 'MANUAL',
    label: '人工'
  }
];
export const tagEvmOptions = [
  {
    value: 1,
    label: '是'
  },
  {
    value: 0,
    label: '否'
  }
];
export const stockTypeOptions = [
  {
    value: 'ZKH',
    label: '震坤行'
  },
  {
    value: 'SUPPLIER',
    label: '商家'
  }
];
export const statusOptions = [
  {
    value: 'DRAFT',
    label: '草稿'
  },
  {
    value: 'CHECKING',
    label: '待审核'
  },
  {
    value: 'CHECKED',
    label: '已审核'
  },
  {
    value: 'REJECT',
    label: '已驳回'
  },
  {
    value: 'CLOSED',
    label: '已关闭'
  },
  {
    value: 'EXPIRED',
    label: '已失效'
  }
];
export const tagBoolOptions = [
  { value: 1, label: '是' },
  { value: 0, label: '否' }
];
export const tagStockOptions = [
  { value: 1, label: '备货' },
  { value: 0, label: '不备货' }
];
export const tagBoolIncludeAllOptions = [
  { value: 1, label: '备货' },
  { value: 0, label: '不备货' }
];
export const tagStockReasonOptions = [
  { value: 1, label: '原因1' },
  { value: 0, label: '原因2' }
];
export const stockModelCheckColunms = [
  {
    label: '来源',
    prop: 'source',
    minWidth: 100,
    align: 'center',
    type: '',
    labelFormat: (ceilValue) => ceilValue === 'AI' ? 'AI' : '人工',
    fixed: 'left'
  },
  {
    label: '版本号',
    prop: 'version',
    minWidth: 100,
    align: 'center',
    type: '',
    fixed: 'left'
  },
  {
    label: 'sku',
    prop: 'sku',
    minWidth: 100,
    align: 'center',
    type: '',
    fixed: 'left'
  },
  {
    label: '物料描述',
    prop: 'skuDesc',
    minWidth: 120,
    align: 'center',
    type: '',
    showOverflowTooltip: true,
    fixed: 'left'
  },
  {
    label: '工厂',
    prop: 'factoryName',
    minWidth: 120,
    align: 'center',
    type: '',
    showOverflowTooltip: true,
    fixed: 'left'
  },
  {
    label: '商品来源',
    prop: 'sourceTypeName',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '产品经理',
    prop: 'pmName',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '备货仓',
    prop: 'warehouseName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => ceilValue ? row.warehouse + ' ' + ceilValue : '',
    type: ''
  },
  {
    label: '备货库位',
    prop: 'positionName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => ceilValue ? row.position + ' ' + ceilValue : '',
    type: ''
  },
  {
    label: '服务范围',
    prop: 'serverCityNameSetStr',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '是否级库存',
    prop: 'tagLevelStock',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : '否',
    type: ''
  },
  {
    label: '上级仓',
    prop: 'parentWarehouseName',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '是否EVM备货',
    prop: 'tagEvm',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : '否',
    type: ''
  },
  {
    label: '预计下月销量',
    prop: 'expectedNextMonthSales',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  // {
  //   label: '近三月预测准确率',
  //   prop: 'lastThreeMonthsAccuracy',
  //   minWidth: 100,
  //   align: 'center',
  //   type: 'custom',
  //   columnSlot: 'lastThreeMonthsAccuracy'
  // },
  {
    label: '预计滚动备用资金',
    prop: 'expectedRollingInventoryAmount',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '系统建议是否备货',
    prop: 'systemSuggestedStocking',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : (ceilValue ? '否' : ''),
    type: ''
  },
  {
    label: '系统建议ROP',
    prop: 'systemSuggestedRop',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '系统建议ROQ',
    prop: 'systemSuggestedRoq',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '上月ROP',
    prop: 'finalRopLastMonth',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '上月ROQ',
    prop: 'finalRoqLastMonth',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '上月备货标识',
    prop: 'tagFinalStockLastMonth',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '备货' : (ceilValue ? '不备货' : ''),
    type: ''
  },
  {
    label: '本月备货标识',
    prop: 'tagFinalStock',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '备货' : (ceilValue ? '不备货' : ''),
    type: ''
  },
  {
    label: '不备原因分类',
    prop: 'notStockingReason',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '审核备注',
    prop: 'approvalRemarks',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '最终ROP',
    prop: 'finalRop',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '最终ROQ',
    prop: 'finalRoq',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '物料组',
    prop: 'materialGroupName',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '类目',
    prop: 'category',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '品牌',
    prop: 'brandName',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '核心规格',
    prop: 'coreSpecification',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '制造商型号',
    prop: 'manufacturerMaterialNo',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '产品定位',
    prop: 'productPositioning',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '是否系统库存对接',
    prop: 'tagSystemApi',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: 'ROQ天数',
    prop: 'roqDays',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '近三个月对客有货率',
    prop: 'lastThreeMonthsAvailabilityRate',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '审核备注',
    prop: 'approvalRemarks',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '平均Leadtime',
    prop: 'averageLeadtime',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '平均单价',
    prop: 'averageUnitPrice',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '90天销量',
    prop: 'salesVolume90Days',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '91至180天销售次数',
    prop: 'salesFrequency91To180Days',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '90天销售次数',
    prop: 'salesFrequency90Days',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '91至180天客户数',
    prop: 'customers91To180Days',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '90天不同客户数',
    prop: 'differentCustomers90Days',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '90天询报价次数',
    prop: 'quoteInquiries90Days',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '90天询报价量',
    prop: 'quoteVolume90Days',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '大石头比例',
    prop: 'bigStoneRatio',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '大石头客户代码',
    prop: 'bigStoneCustomerCode',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '大石头客户',
    prop: 'bigStoneCustomer',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '预计平均在库数量',
    prop: 'estimatedAverageInStockQuantity',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '预计平均在库金额',
    prop: 'estimatedAverageInStockAmount',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '寄售标签',
    prop: 'consignmentLabel',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '预估采购量',
    prop: 'estimatedPurchaseQuantity',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '月用量参考',
    prop: 'monthlyUsageReference',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '创建人',
    prop: 'creator',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '审核人',
    prop: 'approver',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '状态',
    prop: 'status',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => {
      return statusOptions.find((item) => {
        return item.value === ceilValue
      }).label
    },
    type: ''
  },
  {
    label: '驳回原因',
    prop: 'rejectReason',
    minWidth: 120,
    align: 'center',
    type: ''
  },
  {
    label: '操作',
    prop: 'action',
    minWidth: 80,
    align: 'center',
    type: 'custom',
    columnSlot: 'action',
    fixed: 'right'
  }
]

export const stockModelPreviewColunms = [
  {
    label: 'sku',
    prop: 'sku',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '物料描述',
    prop: 'skuDesc',
    minWidth: 120,
    align: 'center',
    type: '',
    showOverflowTooltip: true
  },
  {
    label: '工厂',
    prop: 'factoryName',
    minWidth: 120,
    align: 'center',
    type: '',
    showOverflowTooltip: true
  },
  {
    label: '备货仓',
    prop: 'warehouseName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => ceilValue ? row.warehouse + ' ' + ceilValue : '',
    type: ''
  },
  {
    label: '备货库位',
    prop: 'positionName',
    minWidth: 140,
    align: 'center',
    showOverflowTooltip: true,
    labelFormat: (ceilValue, row) => ceilValue ? row.position + ' ' + ceilValue : '',
    type: ''
  },
  {
    label: '服务范围',
    prop: 'cityServerNameStr',
    minWidth: 120,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '是否级库存',
    prop: 'tagLevelStock',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : '否',
    type: ''
  },
  {
    label: '上级仓',
    prop: 'parentWarehouseName',
    minWidth: 100,
    align: 'center',
    showOverflowTooltip: true,
    type: ''
  },
  {
    label: '系统建议是否备货',
    prop: 'systemSuggestedStocking',
    minWidth: 100,
    align: 'center',
    labelFormat: (ceilValue) => ceilValue === '1' ? '是' : (ceilValue ? '否' : ''),
    type: ''
  },
  {
    label: '系统建议ROP',
    prop: 'systemSuggestedRop',
    minWidth: 100,
    align: 'center',
    type: ''
  },
  {
    label: '系统建议ROQ',
    prop: 'systemSuggestedRoq',
    minWidth: 100,
    align: 'center',
    type: ''
  }
]

export const stockConfigTypeOptions = [
  {
    value: 'ZKH',
    label: 'ZKH备货'
  },
  {
    value: 'SUPPLIER',
    label: '商家备货'
  }
];
