<template>
  <el-dialog
    width="55%"
    title="试算结果"
    center
    destroy-on-close
    :visible.sync="showDlg"
    @closed="$emit('update:visible', false)"
  >
    <zkh-table
      :data="result.previewList"
      :columns="stockModelPreviewColunms"
      :emptyText="result.previewMsg"
      :row-key="row => row.id"
    />
    <footer slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">关闭</el-button>
      <el-button v-if="result.code === 200" :loading="loading" @click="submit" type="primary">转备货审核</el-button>
    </footer>
  </el-dialog>
</template>
<script>
import { stockModelPreviewColunms } from './constant';
import { convert2CheckStock } from '@/api/stockpileNew';
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    result: {
      type: Object,
      default: () => {}
    }
  },
  data: function() {
    return {
      stockModelPreviewColunms,
      loading: false
    }
  },
  computed: {
    showDlg: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    async submit() {
      this.loading = true
      const res = await convert2CheckStock(this.result.previewList).catch(e => {
        this.loading = false
        this.$message.error(e.msg)
      })
      this.loading = false
      if (res.code === 200) {
        this.$message.success('操作成功')
        const row = this.result.previewList[0]
        const path = `/stockpile-strategy/newModelStockApproval?factory=${row.factory}&sku=${row.sku}&position=${row.position}&tagEvm=0&warehouse=${row.warehouse}&warehouseName=${row.warehouseName}`
        this.$emit('update:visible', false)
        setTimeout(() => {
          this.$router.push({
            path
          })
        }, 10)
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>
