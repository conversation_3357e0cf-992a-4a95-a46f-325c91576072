<template>
  <div class="app-container" style="padding-bottom: 50px;">
    <el-card>
      <el-steps :active="currentStep" align-center finish-status="success">
        <el-step title="备货策略设定" description="配置当月备货商品备货方、目标履约时效等，可同时设置多个目标" />
        <el-step title="模拟对比" description="依据步骤1模拟计算：备货成本，履约时效，DIO等，对比多个策略投入产出及风险收益" />
        <el-step title="确认备货策略" description="综合对比确认当月的备货策略" />
      </el-steps>
    </el-card>
    <div style="margin-top: 18px;">
      <KeepAlive>
        <component :ref="`stepRef${currentStep}`" :is="currentComponent" :query-params="queryParams" :step3-info="step3Info" />
      </KeepAlive>
    </div>
    <div class="strategy-fixed-btn-wrap">
      <el-button v-if="currentStep > 0" @click="handlePrev">返回</el-button>
      <el-button type="primary" :loading="loading" @click="handleNext">{{ currentStep === 2 ? '完成' : '下一步' }}</el-button>
    </div>
  </div>
</template>

<script>
import Step1 from '../components/config/Step1.vue'
import Step2 from '../components/config/Step2.vue'
import Step3 from '../components/config/Step3.vue'
export default {
  components: {
    Step1,
    Step2,
    Step3
  },
  data() {
    return {
      currentStep: 0,
      loading: false,
      queryParams: null,
      step3Info: {}
    }
  },
  computed: {
    currentComponent: function() {
      return `Step${this.currentStep + 1}`
    }
  },
  methods: {
    handlePrev() {
      this.currentStep--
    },
    async handleNext() {
      const comp = this.$refs[`stepRef${this.currentStep}`]
      if (comp?.handleSave) {
        this.loading = true
        const res = await comp.handleSave()
        this.loading = false
        if (res) {
          if (this.currentStep === 0) {
            this.queryParams = res
          } else if (this.currentStep === 1) {
            this.step3Info = res
          }
          this.currentStep++
        }
      } else {
        this.currentStep = 0
      }
    }
  }
}
</script>

<style>
.el-step.is-simple:not(:last-of-type) .el-step__title {
  max-width: 65%;
}
.strategy-fixed-btn-wrap {
  position: fixed;
  width: calc(100% - 264px);
  z-index: 99;
  bottom: 0;
  padding: 12px 24px;
  background: #fff;
  display: flex;
  justify-content: center;
  box-shadow: rgb(25 26 35 / 20%) 0px -1px 20px 0px;
}
</style>
