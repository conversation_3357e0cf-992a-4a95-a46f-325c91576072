<template>
  <el-dialog
    width="55%"
    :title="actionType === 'add' ? '新增' : '编辑'"
    class="trend-dialog"
    :visible.sync="showDlg"
    @closed="$emit('update:visible', false)"
  >
    <el-form
      ref="form"
      :model="formField"
      :rules="formRules"
      label-width="140px"
      label-position="right"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="SKU" prop="sku">
            <SelectSku
              v-model="formField.sku"
              :disabled="actionType === 'edit'"
              @selectChange="skuChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料描述" prop="skuDesc">
            <el-input type="textarea" v-model="formField.skuDesc" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源" prop="source">
            <el-select v-model="formField.source" :disabled="true">
              <el-option
                v-for="item in sourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="工厂" prop="factory">
            <el-select v-model="formField.factory" :disabled="actionType === 'edit'">
              <el-option
                v-for="item in factoryOptions"
                :key="item.factory"
                :label="item.factory + ' ' + item.factoryName"
                :value="item.factory"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formField.status" :disabled="true">
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="版本" prop="version">
            <el-input type="input" v-model="formField.version" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否EVM备货" prop="tagEvm">
            <el-select v-model="formField.tagEvm" disabled>
              <el-option
                v-for="item in tagBoolOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备货仓" prop="warehouse" required>
            <el-select
              v-model="formField.warehouse"
              filterable
              clearable
              style="width: 100%"
              :disabled="actionType === 'edit'"
              value-key="warehouseCode"
              placeholder="请选择"
              @change="handleWarehouseChange"
            >
              <el-option
                v-for="(item,index) in warehouseCodeList"
                :key="item.warehouseCode+index"
                :label="item.warehouseName"
                :value="item"

              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备货库位" prop="position" required>
            <el-select
              v-model="formField.position"
              filterable
              clearable
              style="width: 100%"
              :disabled="actionType === 'edit'"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in designatedPosition"
                :key="item.code + item.name + index"
                :label="(item.code !== -1 ? item.code : '') + ' '+ item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否级库存" prop="tagLevelStock">
            <el-select v-model="formField.tagLevelStock" :disabled="!canChangeLevelStock">
              <el-option
                v-for="item in tagBoolOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上级仓" prop="parentWarehouse">
            <el-select
              v-model="formField.parentWarehouse"
              filterable
              clearable
              style="width: 100%"
              :disabled="formField.tagLevelStock === 0 || !canChangeLevelStock"
              placeholder="请选择"
            >
              <el-option
                v-for="(item,index) in warehouseCodeList"
                :key="item.warehouseCode+index"
                :label="item.warehouseName"
                :value="item.warehouseCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="!canChangeLevelStock" :span="24" style="color: red; padding-left: 140px; margin-top: -10px;">如需修改级库存关系，请修改商品库位策略，或联系@张可修改。</el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="系统建议是否备货" prop="systemSuggestedStocking">
            <el-select v-model="formField.systemSuggestedStocking" :disabled="true">
              <el-option
                v-for="item in tagBoolOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="系统建议ROP" prop="systemROP">
            <el-input style="width: 100%;" v-model="formField.systemROP" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="系统建议ROQ" prop="systemROQ">
            <el-input style="width: 100%;" v-model="formField.systemROQ" :disabled="true" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="本月备货标识" prop="tagFinalStock">
            <el-select v-model="formField.tagFinalStock" placeholder="">
              <el-option
                v-for="item in tagStockOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="不备原因分类" prop="notStockingReason">
            <el-cascader :options="tagStockReasonOptions" v-model="formField.notStockingReason" @change="handleStockingReasonChange" :disabled="formField.tagFinalStock === 1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" prop="approvalRemarks" label-width="20px">
            <el-input placeholder="请输入审核备注" :disabled="formField.tagFinalStock === 1" style="width: 100%;" v-model="formField.approvalRemarks" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最终ROP" prop="finalRop">
            <el-input-number :min="0" style="width: 100%;" :disabled="formField.tagFinalStock === 0" v-model="formField.finalRop" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="最终ROQ" prop="finalRoq">
            <el-input-number :min="0" style="width: 100%;" :disabled="formField.tagFinalStock === 0" v-model="formField.finalRoq" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="服务范围" prop="cityServerSet">
            <Province style="width: 100%;" v-model="formField.cityServerSet" :maxDepth="2" :multiple="true" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <footer class="align-right">
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button @click="submit" :loading="loading" type="primary">保存</el-button>
    </footer>
  </el-dialog>
</template>
<script>
import Province from '@/components/SearchFields/provinceCascaderSync';
import SelectSku from '@/components/SearchFields/selectSku';
import { getWarehouseCode, getAllParentWarehouseCode, getNoStockReasonEnum, getStockLevel } from '@/api/stockpileNew';
import { statusOptions, tagBoolOptions, tagStockOptions } from './constant';
function transformData(data) {
  const transformedData = []
  for (const key in data) {
    const options = data[key].map(item => {
      return {
        value: item,
        label: item
      }
    })
    transformedData.push({
      value: key,
      label: key,
      children: options.length ? options : null
    })
  }
  return transformedData
}
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    factoryOptions: {
      type: Array,
      default: () => []
    },
    sourceOptions: {
      type: Array,
      default: () => []
    },
    detail: {
      type: Object,
      default: () => {}
    },
    actionType: {
      type: String,
      default: 'add'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Province,
    SelectSku
  },
  watch: {
    'formField.sku': {
      handler: function(val) {
        if (val) {
          getWarehouseCode({ skuSet: [val], positionScope: 1, factorySet: this.formField.factory ? [this.formField.factory] : [] }).then(res => {
            this.warehouseCodeList = res.result.filter(item => item.ifWarehouseSupportStockupIn === '1')
            if (this.formField.warehouse?.warehouseCode) {
              this.changeWarehouseCode(this.warehouseCodeList.find(item => item.warehouseCode === this.formField.warehouse.warehouseCode))
            }
          })
          if (this.formField.warehouse?.warehouseCode && this.formField.factory) {
            this.queryStockInfo(this.formField.factory, this.formField.sku, this.formField.warehouse.warehouseCode)
          }
        }
      },
      immediate: true
    },
    'formField.factory': {
      handler: function(val) {
        if (val && this.formField.sku) {
          getWarehouseCode({ skuSet: [this.formField.sku], positionScope: 1, factorySet: [val] }).then(res => {
            this.warehouseCodeList = res.result.filter(item => item.ifWarehouseSupportStockupIn === '1')
            if (this.formField.warehouse?.warehouseCode) {
              this.changeWarehouseCode(this.warehouseCodeList.find(item => item.warehouseCode === this.formField.warehouse.warehouseCode))
            }
          })
          if (this.formField.warehouse?.warehouseCode) {
            this.queryStockInfo(this.formField.factory, this.formField.sku, this.formField.warehouse.warehouseCode)
          }
        }
      },
      immediate: true
    },
    'formField.tagLevelStock': {
      handler: function(val) {
        if (val === 1) {
          if (this.parentWarehouseList.length === 0) {
            getAllParentWarehouseCode().then(res => {
              this.parentWarehouseList = res.result
            })
          }
          this.formRules.parentWarehouse[0].required = true
        } else if (val === 0) {
          this.formField.parentWarehouse = ''
          this.formRules.parentWarehouse[0].required = false
        }
      },
      immediate: true
    },
    'formField.tagFinalStock': function(val) {
      if (val === 0) {
        this.formField.finalRop = 0
        this.formField.finalRoq = 0
        this.formRules.notStockingReason[0].required = true
      } else {
        this.formField.finalRop = this.actionType === 'add' ? undefined : this.detail.finalRop
        this.formField.finalRoq = this.actionType === 'add' ? undefined : this.detail.finalRoq
        this.formRules.notStockingReason[0].required = false
        this.formField.notStockingReason = []
        this.formField.approvalRemarks = ''
      }
    },
    'formField.warehouse': {
      handler: function(val) {
        if (val.warehouseCode && this.formField.sku && this.formField.factory) {
          this.queryStockInfo(this.formField.factory, this.formField.sku, val.warehouseCode)
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    if (this.actionType === 'edit') {
      this.formField.cityServerSet = this.detail.serverCitySet ? this.detail.serverCitySet.map(item => [item.parentCode + '', item.code + '']) : []
      this.formField.parentWarehouse = this.detail.parentWarehouse
      this.formField.tagLevelStock = Number(this.detail.tagLevelStock)
      this.formField.tagEvm = Number(this.detail.tagEvm)
      this.formField.last3MInStockRateStr = this.detail.last3MInStockRateStr
      this.formField.sku = this.detail.sku
      this.formField.skuDesc = this.detail.skuDesc
      this.formField.status = this.detail.status
      this.formField.systemSuggestedStocking = this.detail.systemSuggestedStocking
      this.formField.systemROP = this.detail.systemSuggestedRop
      this.formField.systemROQ = this.detail.systemSuggestedRoq
      this.formField.version = this.detail.version
      this.formField.position = this.detail.position
      this.formField.stockType = this.detail.stockType
      this.formField.warehouse = {
        warehouseCode: this.detail.warehouse,
        warehouseName: this.detail.warehouseName
      }
      this.formField.source = this.detail.source
      this.formField.factory = this.detail.factory
      this.formField.tagFinalStock = Number(this.detail.tagFinalStock)
      this.formField.finalRop = this.detail.finalRop
      this.formField.finalRoq = this.detail.finalRoq
      this.formField.notStockingReason = this.detail.notStockingReason?.split(':')
      this.formField.approvalRemarks = this.detail.approvalRemarks
      this.canChangeLevelStock = this.detail.canChangeLevelStock
    }
    getNoStockReasonEnum().then(res => {
      this.tagStockReasonOptions = transformData(res.data)
    })
  },
  data: function() {
    return {
      formField: {
        source: 'MANUAL',
        tagEvm: 0,
        tagLevelStock: 0,
        warehouse: {},
        position: '',
        parentWarehouse: '',
        cityServerSet: [],
        sku: '',
        status: 'DRAFT',
        tagFinalStock: '',
        systemROQ: '',
        systemROP: '',
        notStockingReason: '',
        approvalRemarks: '',
        finalRop: undefined,
        finalRoq: undefined
      },
      formRules: {
        sku: [
          { required: true, message: '请输入SKU', trigger: 'change' }
        ],
        source: [
          { required: true, message: '请选择来源', trigger: 'change' }
        ],
        factory: [
          { required: true, message: '请选择厂家', trigger: 'change' }
        ],
        tagEvm: [
          { required: true, message: '请选择是否EVM', trigger: 'change' }
        ],
        tagLevelStock: [
          { required: true, message: '请选择是否级库存', trigger: 'change' }
        ],
        warehouse: [
          { required: true, message: '请选择备货仓', trigger: 'change' }
        ],
        parentWarehouse: [
          { required: false, message: '请选择上级仓', trigger: 'change' }
        ],
        position: [
          { required: true, message: '请选择库位', trigger: 'change' }
        ],
        tagFinalStock: [
          { required: true, message: '请选择最终是否备货', trigger: 'change' }
        ],
        finalRop: [
          { required: true, message: '请输入最终ROP', trigger: 'blur' }
        ],
        finalRoq: [
          { required: true, message: '请输入最终ROQ', trigger: 'blur' }
        ],
        notStockingReason: [
          { required: false, message: '请选择不备原因', trigger: 'change' }
        ],
        approvalRemarks: [
          { required: false, message: '请输入审核备注', trigger: 'blur' }
        ]
      },
      tagBoolOptions,
      statusOptions,
      tagStockOptions,
      tagStockReasonOptions: [],
      warehouseCodeList: [],
      designatedPosition: [],
      parentWarehouseList: [],
      canChangeLevelStock: true
    }
  },
  computed: {
    showDlg: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const submitFiled = { ...this.formField }
          submitFiled.warehouse = this.formField.warehouse.warehouseCode
          submitFiled.serviceCitySetStr = this.formField.cityServerSet.map(item => item[1]).join(',')
          submitFiled.id = this.actionType === 'edit' ? this.detail.id : undefined
          submitFiled.notStockingReason = this.formField.notStockingReason.join(':')
          this.$emit('submit', submitFiled)
        } else {
          return false
        }
      })
    },
    queryStockInfo(factory, sku, warehouseCode) {
      if (this.actionType === 'edit') {
        return
      }
      getStockLevel({ factory, sku, warehouseCode }).then(res => {
        this.canChangeLevelStock = res.data.canChangeLevelStock ?? false
        this.formField.tagLevelStock = Number(res.data.tagLevelStock ?? 0)
        if (this.formField.tagLevelStock === 1) {
          this.formField.parentWarehouse = res.data.parentWarehouse ?? ''
        }
      })
    },
    handleStockingReasonChange(val) {
      if (val[0] === '其他原因') {
        this.formRules.approvalRemarks[0].required = true
      } else {
        this.formRules.approvalRemarks[0].required = false
        this.formField.approvalRemarks = ''
      }
    },
    handleWarehouseChange(selectedWareHouseCode) {
      this.formField.position = ''
      this.changeWarehouseCode(selectedWareHouseCode)
    },
    changeWarehouseCode(selectedWareHouseCode) {
      this.designatedPosition = selectedWareHouseCode.allPosition
    },
    skuChange(val) {
      this.formField.skuDesc = val?.materialDescribe || ''
    }
  }
}
</script>
