<template>
  <el-dialog
    width="55%"
    style="max-height: 800px;"
    :title="actionType === 'add' ? '新增' : '编辑'"
    :visible.sync="showDlg"
    @closed="$emit('update:visible', false)"
  >
    <el-form
      ref="form"
      :model="formField"
      :rules="formRules"
      :validate-on-rule-change="false"
      label-width="120px"
      label-position="right"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="SKU" prop="sku">
            <SelectSku
              v-model="formField.sku"
              :disabled="actionType === 'edit'"
              @selectChange="skuChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料描述" prop="skuDesc">
            <el-input type="textarea" disabled v-model="formField.skuDesc" style="font-size: 12px;line-height: 16px;color: #666;" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源" prop="source">
            <el-select v-model="formField.source" :disabled="true">
              <el-option
                v-for="item in sourceOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="8">
          <el-form-item label="工厂" prop="factory">
            <el-select v-model="formField.factory" :disabled="actionType === 'edit'">
              <el-option
                v-for="item in factoryOptions"
                :key="item.factory"
                :label="item.factory + ' ' + item.factoryName"
                :value="item.factory"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备货方" prop="stockType">
            <el-select v-model="formField.stockType">
              <el-option
                v-for="item in stockTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="版本" prop="version">
            <el-input type="input" v-model="formField.version" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否EVM备货" prop="tagEvm">
            <el-select v-model="formField.tagEvm" disabled>
              <el-option
                v-for="item in tagBoolOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备货仓" prop="warehouse">
            <el-select
              v-model="formField.warehouse"
              filterable
              clearable
              style="width: 100%"
              :disabled="formField.stockType === 'SUPPLIER'"
              value-key="warehouseCode"
              placeholder="请选择"
              @change="handleWarehouseChange"
            >
              <el-option
                v-for="(item,index) in warehouseCodeList"
                :key="item.warehouseCode+index"
                :label="item.warehouseName"
                :value="item"

              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备货库位" prop="position">
            <el-select
              v-model="formField.position"
              filterable
              clearable
              style="width: 100%"
              :disabled="formField.stockType === 'SUPPLIER'"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, index) in designatedPosition"
                :key="item.code + item.name + index"
                :label="(item.code !== -1 ? item.code : '') + ' '+ item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商" prop="supplier">
            <Supplier v-model="formField.supplier" :sku-no="formField.sku" :factory="formField.factory" :disabled="formField.stockType === 'ZKH'" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否系统对接" prop="tagSystemApi">
            <el-select disabled v-model="formField.tagSystemApi">
              <el-option
                v-for="item in tagBoolOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="近三月商家总有货率" prop="last3MInStockRateStr" label-width="160px">
            <el-input type="input" disabled v-model="formField.last3MInStockRateStr" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="是否级库存" prop="tagLevelStock">
            <el-select v-model="formField.tagLevelStock" :disabled="formField.stockType === 'SUPPLIER' || !canChangeLevelStock">
              <el-option
                v-for="item in tagBoolOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="上级仓" prop="parentWarehouse">
            <el-select
              v-model="formField.parentWarehouse"
              filterable
              clearable
              style="width: 100%"
              :disabled="formField.tagLevelStock === 0 || formField.stockType === 'SUPPLIER' || !canChangeLevelStock"
              placeholder="请选择"
            >
              <el-option
                v-for="(item,index) in warehouseCodeList"
                :key="item.warehouseCode+index"
                :label="item.warehouseName"
                :value="item.warehouseCode"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col v-if="!canChangeLevelStock" :span="24" style="color: red; padding-left: 120px; margin-top: -10px;">如需修改级库存关系，请修改商品库位策略，或联系@张可修改。</el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="服务范围" prop="cityServerSet" required>
            <Province style="width: 100%;" v-model="formField.cityServerSet" :multiple="true" :maxDepth="2" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <footer class="align-right">
      <el-button @click="$emit('update:visible', false)">取消</el-button>
      <el-button :loading="loading" @click="submit" type="primary">确定</el-button>
    </footer>
  </el-dialog>
</template>
<script>
import Supplier from '../components/selectSkuSupplier.vue';
import Province from '@/components/SearchFields/provinceCascaderSync';
import SelectSku from '@/components/SearchFields/selectSku';
import { getWarehouseCode, getAllParentWarehouseCode, queryBiLast3MonthSupplierStockRate, querySupplierStock, getStockLevel } from '@/api/stockpileNew';
import { tagBoolOptions } from './constant'
const getDate = () => {
  const date = new Date();
  if (date.getMonth() + 1 < 10) {
    return date.getFullYear() + '0' + (date.getMonth() + 1)
  }
  return date.getFullYear() + '' + (date.getMonth() + 1)
}
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    factoryOptions: {
      type: Array,
      default: () => []
    },
    sourceOptions: {
      type: Array,
      default: () => []
    },
    stockTypeOptions: {
      type: Array,
      default: () => []
    },
    detail: {
      type: Object,
      default: () => {}
    },
    actionType: {
      type: String,
      default: 'add'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  components: {
    Supplier,
    Province,
    SelectSku
  },
  watch: {
    'formField.stockType': {
      handler: function(val) {
        if (val === 'SUPPLIER') {
          // 清空仓库和库位
          this.formField.warehouse = ''
          this.formField.position = ''
          this.formField.tagLevelStock = ''
          this.formField.parentWarehouse = ''
          this.formField.tagEvm = 0
          // 校验规则也改变
          this.formRules.warehouse[0].required = false
          this.formRules.position[0].required = false
          this.formRules.supplier[0].required = true
          this.formRules.tagLevelStock[0].required = false
        } else if (val === 'ZKH') {
          // 清空供应商
          this.formField.supplier = ''
          this.formField.tagSystemApi = 0
          this.formField.last3MInStockRateStr = ''
          // 校验规则也改变
          this.formRules.warehouse[0].required = true
          this.formRules.position[0].required = true
          this.formRules.tagLevelStock[0].required = true
          this.formRules.supplier[0].required = false
        }
      },
      immediate: true
    },
    'formField.sku': {
      handler: function(val) {
        if (val) {
          getWarehouseCode({ skuSet: [val], positionScope: 1, factorySet: this.formField.factory ? [this.formField.factory] : [] }).then(res => {
            this.warehouseCodeList = res.result.filter(item => item.ifWarehouseSupportStockupIn === '1')
            if (this.formField.warehouse?.warehouseCode) {
              this.changeWarehouseCode(this.warehouseCodeList.find(item => item.warehouseCode === this.formField.warehouse.warehouseCode))
            }
          })
          if (this.actionType === 'add') {
            this.formField.supplier = ''
          }
          if (this.formField.warehouse?.warehouseCode && this.formField.factory) {
            this.queryStockInfo(this.formField.factory, this.formField.sku, this.formField.warehouse.warehouseCode)
          }
        }
      },
      immediate: true
    },
    'formField.factory': {
      handler: function(val) {
        if (val && this.formField.sku) {
          getWarehouseCode({ skuSet: [this.formField.sku], positionScope: 1, factorySet: [val] }).then(res => {
            this.warehouseCodeList = res.result.filter(item => item.ifWarehouseSupportStockupIn === '1')
            if (this.formField.warehouse?.warehouseCode) {
              this.changeWarehouseCode(this.warehouseCodeList.find(item => item.warehouseCode === this.formField.warehouse.warehouseCode))
            }
          })
          if (this.actionType === 'add') {
            this.formField.supplier = ''
          }
          if (this.formField.warehouse?.warehouseCode) {
            this.queryStockInfo(this.formField.factory, this.formField.sku, this.formField.warehouse.warehouseCode)
          }
        }
      },
      immediate: true
    },
    'formField.tagLevelStock': {
      handler: function(val) {
        if (val === 1 && this.parentWarehouseList.length === 0) {
          getAllParentWarehouseCode().then(res => {
            this.parentWarehouseList = res.result
          })
        } else if (val === 0) {
          this.formField.parentWarehouse = ''
        }
      },
      immediate: true
    },
    'formField.supplier': function(val) {
      if (val) {
        // 通过供应商获取是否系统对接和总有货率
        queryBiLast3MonthSupplierStockRate({ supplier: val, factory: this.formField.factory, sku: this.formField.sku }).then(res => {
          this.formField.last3MInStockRateStr = res.data ?? 0
        })
        querySupplierStock({ factory: this.formField.factory, sku: this.formField.sku }).then(res => {
          this.formField.tagSystemApi = res.data > 0 ? 1 : 0
        })
      }
    },
    'formField.warehouse': {
      handler: function(val) {
        if (val.warehouseCode && this.formField.sku && this.formField.factory) {
          this.queryStockInfo(this.formField.factory, this.formField.sku, val.warehouseCode)
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    if (this.actionType === 'edit') {
      this.formField.cityServerSet = this.detail.cityServerSet ? this.detail.cityServerSet.map(item => [item.parentCode + '', item.code + '']) : []
      this.formField.parentWarehouse = this.detail.parentWarehouse
      this.formField.tagLevelStock = Number(this.detail.tagLevelStock)
      this.formField.tagEvm = Number(this.detail.tagEvm)
      this.formField.tagSystemApi = Number(this.detail.tagSystemApi)
      this.formField.last3MInStockRateStr = this.detail.last3MInStockRateStr
      this.formField.sku = this.detail.sku
      this.formField.version = this.detail.version
      this.formField.position = this.detail.position
      this.formField.supplier = this.detail.supplier
      this.formField.stockType = this.detail.stockType
      this.formField.warehouse = {
        warehouseCode: this.detail.warehouse,
        warehouseName: this.detail.warehouseName
      }
      this.formField.source = this.detail.source
      this.formField.factory = this.detail.factory
      this.formField.skuDesc = this.detail.skuDesc
      this.supplierName = this.detail.supplierName
      this.canChangeLevelStock = this.detail.canChangeLevelStock
    }
  },
  data: function() {
    return {
      formField: {
        source: 'MANUAL',
        stockType: '',
        version: getDate(),
        tagEvm: 0,
        tagLevelStock: 0,
        tagSystemApi: '',
        warehouse: {},
        position: '',
        supplier: '',
        parentWarehouse: '',
        cityServerSet: [],
        sku: ''
      },
      formRules: {
        sku: [
          { required: true, message: '请输入SKU', trigger: 'change' }
        ],
        source: [
          { required: true, message: '请选择来源', trigger: 'change' }
        ],
        factory: [
          { required: true, message: '请选择厂家', trigger: 'change' }
        ],
        stockType: [
          { required: true, message: '请选择备货方', trigger: 'change' }
        ],
        tagEvm: [
          { required: true, message: '请选择是否EVM', trigger: 'change' }
        ],
        tagLevelStock: [
          { required: false, message: '请选择是否级库存', trigger: 'change' }
        ],
        supplier: [
          { required: true, message: '请选择供应商', trigger: 'change' }
        ],
        warehouse: [
          { required: false, message: '请选择仓库', trigger: 'change' }
        ],
        position: [
          { required: false, message: '请选择库位', trigger: 'change' }
        ],
        cityServerSet: [
          { required: true, message: '请选择服务城市', trigger: 'change' }
        ]
      },
      tagBoolOptions,
      warehouseCodeList: [],
      designatedPosition: [],
      parentWarehouseList: [],
      supplierName: '',
      canChangeLevelStock: true
    }
  },
  computed: {
    showDlg: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const submitFiled = { ...this.formField }
          submitFiled.position = this.formField.position
          submitFiled.warehouse = this.formField.warehouse.warehouseCode
          submitFiled.parentWarehouse = this.formField.parentWarehouse
          submitFiled.cityServerSet = this.formField.cityServerSet.map(item => item[1])
          submitFiled.id = this.actionType === 'edit' ? this.detail.id : undefined
          this.$emit('submit', submitFiled)
        } else {
          return false
        }
      })
    },
    queryStockInfo(factory, sku, warehouseCode) {
      if (this.actionType === 'edit') {
        return
      }
      getStockLevel({ factory, sku, warehouseCode }).then(res => {
        this.canChangeLevelStock = res.data.canChangeLevelStock ?? false
        this.formField.tagLevelStock = Number(res.data.tagLevelStock ?? 0)
        if (this.formField.tagLevelStock === 1) {
          this.formField.parentWarehouse = res.data.parentWarehouse ?? ''
        }
      })
    },
    handleWarehouseChange(selectedWareHouseCode) {
      this.formField.position = ''
      this.changeWarehouseCode(selectedWareHouseCode)
    },
    changeWarehouseCode(selectedWareHouseCode) {
      this.designatedPosition = selectedWareHouseCode.allPosition
    },
    skuChange(val) {
      this.formField.skuDesc = val?.materialDescribe || ''
    }
  }
}
</script>
