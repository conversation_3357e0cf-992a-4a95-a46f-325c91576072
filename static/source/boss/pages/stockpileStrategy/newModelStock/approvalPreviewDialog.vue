<template>
  <el-dialog
    width="55%"
    title="预测数据"
    :visible.sync="showDlg"
    @closed="$emit('update:visible', false)"
  >
    <div id="main" style="height: 500px; width: 100%;"></div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    previewList: {
      type: Object,
      default: () => []
    }
  },
  created() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  data: function() {
    return {
      total: 0
    }
  },
  computed: {
    showDlg: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    initChart() {
      const chartDom = document.getElementById('main');
      const myChart = window.echarts.init(chartDom);
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999'
            }
          }
        },
        toolbox: {
          feature: {
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] },
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        legend: {
          data: ['Evaporation', 'Precipitation', 'Temperature', 'test']
        },
        xAxis: [
          {
            type: 'category',
            data: ['Mon', 'Tue', 'Wed', 'Thu'],
            axisPointer: {
              type: 'shadow'
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            name: '预测数据',
            min: 0,
            max: 250,
            interval: 50,
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '百分比',
            min: 0,
            max: 100,
            interval: 20,
            axisLabel: {
              formatter: '{value} %'
            }
          }
        ],
        series: [
          {
            name: '系统预测',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value;
              }
            },
            data: [
              20, 49, 70, 232
            ],
            label: {
              normal: {
                show: true,
                position: 'top'
              }
            }
          },
          {
            name: '实际销量',
            type: 'bar',
            tooltip: {
              valueFormatter: function (value) {
                return value;
              }
            },
            data: [
              26, 59, 90, 214
            ],
            label: {
              normal: {
                show: true,
                position: 'top'
              }
            }
          },
          {
            name: '预测准确率',
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value + ' %';
              }
            },
            data: [55, 22, 89, 46],
            label: {
              normal: {
                show: true,
                position: 'top',
                formatter: '{c}%'
              }
            }
          },
          {
            name: 'ROP',
            type: 'line',
            yAxisIndex: 1,
            tooltip: {
              valueFormatter: function (value) {
                return value;
              }
            },
            data: [65, 72, 83, 69],
            label: {
              normal: {
                show: true,
                position: 'top'
              }
            }
          }
        ]
      };
      myChart.setOption(option);
    }
  }
}
</script>
