export const supplyStatusOptions = [
  {
    label: '采购异常',
    value: '0'
  },
  {
    label: '长交期',
    value: '4'
  },
  {
    label: '未采购',
    value: '1'
  },
  {
    label: '未到齐',
    value: '2'
  },
  {
    label: '已到齐',
    value: '3'
  }
]
export const paiddescOptions = [
  {
    label: '已付款',
    value: '已付款'
  },
  {
    label: '未付款',
    value: '未付款'
  }
]
export const orderArriStatusOptions = [
  {
    label: '未到齐',
    value: '0'
  },
  {
    label: '已到齐',
    value: '1'
  }
]

export const autoDeliveryOptions = [
  {
    label: '是',
    value: '是'
  },
  {
    label: '否',
    value: '否'
  }
]

export const orderSourceOptions = [
  { label: '1688', value: '1688' },
  { label: 'BOSS', value: 'BOSS' },
  { label: 'ESP', value: 'ESP' },
  { label: 'EVM', value: 'EVM' },
  { label: 'GBB', value: 'GBB' },
  { label: 'GW', value: 'GW' }
]
export const creditFreezeOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]
export const ifNeedDeliveryOptions = [
  { label: '是', value: 'Y' },
  { label: '否', value: 'N' }
]
// 页面搜索条件初始化
export function getInitSearchParams () {
  return {
    customerId: '', // 客户id
    sapOrderNo: '', // 销售订单号
    orderArriStatus: '', // 整单到货状态 0:未到齐 1:已到齐 支持多选 逗号分割，// 两种状态，传空就是全选
    supplyStatus: '', // 0:采购异常 1:未采购 2:未到齐 3:已到齐
    paiddesc: '', // 付款状态
    productCustomerName: '', // 产线客服
    customerServiceDirector: '', // 客服经理
    sapMaterialName: '', // 物料描述
    productGroupId: '', // 物料组id
    sapCreateTimeStart: '', // 销售订单日期开始
    sapCreateTimeEnd: '', // 物料描述
    brandId: '', // 品牌id
    productSaleName: '', // 销售员
    salesManager: '', // 销售经理
    skuNo: '', // skuno
    orderTypes: '', // 订单类型,支持多个 逗号分割
    itemTypes: '', // 订单行类型,支持多个 逗号分割
    requestedDeliveryDateStart: '', // 首个交期开始
    requestedDeliveryDateEnd: '', // 首个交期结束
    customerDateStart: '', // 客户期望送达日期开始
    customerDateEnd: '', // 客户期望送达日期结束
    voucherNos: '', // 首个交期结束
    orderSource: '', // 订单来源
    ifNeedDelivery: '', // 是否应发未收
    bidCustomer: '', // 未发货原因
    autoDelivery: '', // 自动交货
    paymentTermsName: '', // 账期
    autoBatching: '' // 订单是否分批
  }
}
