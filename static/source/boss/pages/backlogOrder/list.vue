<template>
  <div class="app-container backlog-search-container" v-loading="loading.pageLoading">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="140px"
        label-position="right">
        <el-row>
          <el-col :span="7">
            <el-form-item label="客户名称：" prop="customerId">
              <el-select
                v-model="searchForm.customerId"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="loading.customerNoLoading"
                clearable
              >
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="订单号：" prop="voucherNos">
              <el-input
                v-model="searchForm.voucherNos"
                placeholder="外围、OMS、SAP、客户参考号"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="整单到货状态：" prop="orderArriStatus">
              <el-select
                v-model="searchForm.orderArriStatus"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in orderArriStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <!-- <el-form-item> -->
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="loading.backlogLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="单笔供货状态：" prop="supplyStatus">
              <el-select
                v-model="searchForm.supplyStatus"
                clearable
                placeholder="请选择"
              >
                <el-option
                  v-for="item in supplyStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客服：" prop="productCustomerName">
              <el-input
                v-model.trim="searchForm.productCustomerName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客服经理：" prop="customerServiceDirector">
              <el-input
                v-model.trim="searchForm.customerServiceDirector"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
            <el-button
              type="primary"
              circle
              :icon="caretIcon"
              @click="searchSonditionOpen = !searchSonditionOpen"
            />
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="物料描述：" prop="sapMaterialName">
              <el-input
                v-model.trim="searchForm.sapMaterialName"
                placeholder="可按名称、规格、箱规筛选"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="物料组：" prop="productGroupId">
              <el-select
                v-model="searchForm.productGroupId"
                filterable
                clearable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteProductGroupIdMethod"
                :loading="loading.productGroupIdLoading"
              >
                <el-option
                  v-for="item in productGroupIdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="7">
            <el-form-item label="品牌：" prop="brandId">
              <el-select
                v-model="searchForm.brandId"
                filterable
                remote
                clearable
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="loading.brandIdLoading"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="销售：" prop="productSaleName">
              <el-input
                v-model.trim="searchForm.productSaleName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;" />

          <el-col :span="7">
            <el-form-item label="销售经理：" prop="salesManager">
              <el-input
                v-model.trim="searchForm.salesManager"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="SKU编号：" prop="skuNo">
              <el-input
                v-model.trim="searchForm.skuNo"
                placeholder="多个可用空格隔开"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-show="searchSonditionOpen">
          <el-col :span="14">
            <el-form-item label="订单日期：">
              <el-col :span="11">
                <el-form-item prop="sapCreateTimeStart">
                  <el-date-picker
                    clearable
                    v-model="searchForm.sapCreateTimeStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="sapCreateTimeEnd">
                  <el-date-picker
                    clearable
                    v-model="searchForm.sapCreateTimeEnd"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="付款状态：" prop="paiddesc">
              <el-select
                v-model="searchForm.paiddesc"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in paiddescOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="请求发货日期：">
              <el-col :span="11">
                <el-form-item prop="requestedDeliveryDateStart">
                  <el-date-picker
                    clearable
                    v-model="searchForm.requestedDeliveryDateStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="requestedDeliveryDateEnd">
                  <el-date-picker
                    clearable
                    v-model="searchForm.requestedDeliveryDateEnd"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="订单来源：" prop="orderSource">
              <el-select
                v-model="searchForm.orderSource"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in orderSourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="14">
            <el-form-item label="客户期望送达日期：">
              <el-col :span="11">
                <el-form-item prop="customerDateStart">
                  <el-date-picker
                    clearable
                    v-model="searchForm.customerDateStart"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="customerDateEnd">
                  <el-date-picker
                    clearable
                    v-model="searchForm.customerDateEnd"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="订单是否分批：">
              <el-select
                v-model="searchForm.autoBatching"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in autoDeliveryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="14">
            <el-form-item label="订单类型：" prop="orderTypes">
              <el-select
                :value="getQueryOrderType"
                @change="setQueryOrderType"
                placeholder="请选择"
                clearable
                multiple
              >
                <el-option
                  v-for="item in orderTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="是否应发未发：" prop="ifNeedDelivery">
              <el-select
                v-model="searchForm.ifNeedDelivery"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in ifNeedDeliveryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="订单行类型：" prop="itemTypes">
              <el-select
                :value="getQueryItemType"
                @change="setQueryItemType"
                placeholder="请选择"
                clearable
                multiple
              >
                <el-option
                  v-for="item in itemTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="预订单：" prop="orderTag">
              <el-select
                v-model="searchForm.orderTag"
                placeholder="请选择"
                clearable
                multiple
              >
                <el-option
                  v-for="item in orderTagOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="3" style="padding-left:10px;" /> -->
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="所属集团名称：" prop="parentName">
              <el-input
                v-model="searchForm.parentName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
           <el-col :span="7">
            <el-form-item label="订单原因：" prop="orderReasonCode">
              <el-select
                v-model="searchForm.orderReasonCode"
                placeholder="请选择"
                clearable
              >
              <el-option
                v-for="item in orderReasonList"
                :key="`${item.code}${item.label}${item.parentCode}`"
                :label="item.name"
                :value="item.code"
              />
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="7">
            <el-form-item label="订单联系人：" prop="orderContact">
              <el-input
                v-model="searchForm.orderContact"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="信用冻结：" prop="creditFreeze">
              <el-select
                v-model="searchForm.creditFreeze"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in creditFreezeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="7">
            <el-form-item label="信用超额冻结：" prop="ifCreditFreeze">
              <el-select
                v-model="searchForm.ifCreditFreeze"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in ifNeedDeliveryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="7">
            <el-form-item label="信用逾期冻结：" prop="ifOverDueFreeze">
              <el-select
                v-model="searchForm.ifOverDueFreeze"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in ifNeedDeliveryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="未发货原因：" prop="bidCustomer">
              <el-select
                v-model="searchForm.bidCustomer"
                placeholder="请选择"
                clearable
              >
              <el-option
                v-for="item in bidCustomerList"
                :key="`${item.code}${item.label}`"
                :label="item.name"
                :value="item.code"
              />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="集货仓：" prop="deliveryPositions">
              <el-select
                v-model="searchForm.deliveryPositions"
                placeholder="请选择"
                clearable
                filterable
                multiple
              >
              <el-option
                v-for="item in buildOptions('warehouseLocation')"
                :key="item.value"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="发货仓：" prop="positionCodes">
              <el-select
                v-model="searchForm.positionCodes"
                placeholder="请选择"
                clearable
                filterable
                multiple
              >
              <el-option
                v-for="item in buildOptions('warehouseLocation')"
                :key="item.value"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="自动交货：">
              <el-select
                v-model="searchForm.autoDelivery"
                placeholder="请选择"
                clearable
              >
                <el-option

                  v-for="item in autoDeliveryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="账期：">
              <el-select
                v-model="searchForm.paymentTermsName"
                placeholder="请选择"
                clearable
              >
                <el-option
                  v-for="item in paymentTerms"
                  :key="`${item.code}${item.name}`"
                  :label="item.name"
                  :value="item.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="超期未开冻结：" prop="ifTimeoutUninvoicedFreeze">
              <el-select
                v-model="searchForm.ifTimeoutUninvoicedFreeze"
                >
                <el-option
                  v-for="item in ifNeedDeliveryOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="backlog-search-result-container">
      <el-row type="flex" class="result-title" justify="space-between">
        <el-col :span="8">未交订单跟踪</el-col>
        <el-col :span="16" style="text-align:right;">
          <el-button type="primary" icon="el-icon-download"
            @click="exportExcel" :loading="loading.exportLoading"
            >导出</el-button>
          <div style="display: inline-block;margin-right:10px;margin-left:10px">
            <el-upload class="btn-upload" action="/oms-opc-front/backlog/readExcel" :show-file-list="false"
              :on-success="onUploadSuccess"
              :before-upload="beforeUpload"
              :on-error="onUploadError"
              :disabled="loading.importLoading"
              :accept="acceptFileType.commonType"
              name="file">
              <el-button :loading="loading.importLoading" size="small" type="primary">导入跟单备注</el-button>
            </el-upload>
          </div>
          <el-button type="default" icon="el-icon-edit-outline" @click="downloadTpl" >下载导入模板</el-button>
          <el-button icon="el-icon-s-unfold" @click="showMoreSearch" circle></el-button>
        </el-col>
      </el-row>
      <zkh-table
        :loading="loading.backlogLoading"
        v-loading="loading.initConfigLoading"
        :data="backlogOrderList"
        :columns="columns"
        :headerDragend="headerDragend"
        :height="600"
        :columnEditable="true"
        :visible.sync="columnsSetVisible"
        :columnsConfig="columnsConfigDeepCopy"
        :defaultColumnsConfig="defaultColumnsConfig"
        @columnSet="columnSet"
        @inner-input-change="innerInputChange"
      ></zkh-table>
    </div>
    <pagination
      v-show="canShowTotal > 0"
      :total="canShowTotal"
      align="right"
      :page.sync="listQueryInfo.current"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBacklogOrderList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import getColumns, { orderTagOptions } from './columns.js'
import {
  createdConfigByColumns,
  formatByConfig
} from '@/components/ZkhTable/columnConfigTransformation.js'
import { downloadFile } from '@/utility/request.js'
import {
  goodsBrandListSearch,
  goodsCustomerListSearch,
  goodsGroupListSearch
} from '@/api/insteadOrder.js'

import {
  getBacklogOrders,
  getOrderTypeOptions,
  getItemTypeOptions,
  fetchConfigFromServer,
  saveConfig2Server,
  updateRemark
} from '@/api/backlogOrder.js'

import {
  orderArriStatusOptions,
  supplyStatusOptions,
  paiddescOptions,
  getInitSearchParams,
  creditFreezeOptions,
  ifNeedDeliveryOptions,
  autoDeliveryOptions
} from './constants.js'
import { mapState } from 'vuex'
import { buildOptions } from '@/utils/mm'
export default {
  name: 'BacklogOrderList',
  data () {
    return {
      orderTagOptions, // 预订单
      orderArriStatusOptions, // 整单到货状态 下拉选项
      supplyStatusOptions, // 单笔供货状态 下拉选项
      paiddescOptions, // 付款状态
      ifNeedDeliveryOptions,
      autoDeliveryOptions,
      creditFreezeOptions,
      orderTypeOptions: [], // 订单类型下拉选项
      itemTypeOptions: [],
      customerNoOptions: [],
      brandIdOptions: [],
      productGroupIdOptions: [],
      searchForm: {
        orderArriStatus: '',
        supplyStatus: '',
        paiddesc: ''
      }, // 绑定搜索参数
      columnsSetVisible: false, // 列设置弹框显隐
      defaultColumnsConfig: [],
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      rules: {},
      backlogOrderList: [],
      total: 0,
      searchSonditionOpen: false,
      columns: [],
      columnsConfig: [],
      columnsConfigDeepCopy: [],
      loading: {
        initConfigLoading: false,
        pageLoading: false,
        customerNoLoading: false,
        brandIdLoading: false,
        productGroupIdLoading: false,
        backlogLoading: false, // 查询按钮loading
        exportLoading: false, // 导出按钮 loading
        importLoading: false
      },
      tableServerConfig: null,
      buildOptions
    }
  },
  components: {
    Pagination
  },
  async created () {
    this.loading.initConfigLoading = true
    let tableConfigResponse = await fetchConfigFromServer()
    if (tableConfigResponse && tableConfigResponse.length === 2) {
      this.tableServerConfig = tableConfigResponse
    }
    setTimeout(() => {
      this.loading.initConfigLoading = false
    }, 800)
    this.initColumns()
    // this.searchForm = getInitSearchParams()
    const query = this.$route.query
    this.searchForm = {
      ...getInitSearchParams(),
      ...query,
      orderTag: query.orderTag ? query.orderTag.split(',').map(tag => Number(tag)) : [],
      creditFreeze: query.creditFreeze ? Number(query.creditFreeze) : ''
    }
    // console.log(this.searchForm);
    let orderTypeOptions = await getOrderTypeOptions()
    this.orderTypeOptions = orderTypeOptions.map(item => {
      return {
        label: `${item.orderTypeCode} - ${item.orderTypeName}`,
        value: item.orderTypeCode
      }
    })
    let itemTypeOptions = await getItemTypeOptions()
    this.itemTypeOptions = itemTypeOptions.map(item => {
      return {
        label: `${item.orderTypeCode} - ${item.orderTypeName}`,
        value: item.orderTypeCode
      }
    })
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    // this.initQuery()
    this.handleFilter()
  },
  computed: {
    ...mapState({
      dictList: state => state.orderCommon.dictList,
      orderReasonList: state => state.orderCommon.dictList?.orderReason,
      bidCustomerList: state => state.orderCommon.dictList?.noDeliveryReason,
      paymentTerms: state => state.orderCommon.dictList?.paymentTerms,
      warehouseList: state => state.orderPurchase.warehouseList,
      acceptFileType: state => state.orderCommon.acceptFileType
    }),
    canShowTotal () {
      return this.total
    },
    caretIcon () {
      if (this.searchSonditionOpen) {
        return 'el-icon-caret-top'
      } else {
        return 'el-icon-caret-bottom'
      }
    },
    getQueryOrderType () {
      return this.searchForm.orderTypes ? this.searchForm.orderTypes.split(',') : []
    },
    getQueryItemType () {
      return this.searchForm.itemTypes ? this.searchForm.itemTypes.split(',') : []
    },
    orderSourceOptions () {
      return this.dictList.orderSource?.filter(item => item.status === 'normal' && item.supportQuery === '1')?.map(item => {
        return {
          ...item,
          label: item.name,
          value: item.code
        }
      }) || []
    }
  },
  methods: {
    // initQuery () {
    //   const query = this.$route.query
    //   for (let prop in query) {
    //     this.searchForm[prop] = query[prop]
    //   }
    // },
    innerInputChange (row, prop, val) {
      console.log(row, prop, val)
      const data = {
        remark: val,
        sapItemNo: row.sapItemNo,
        sapOrderNo: row.sapOrderNo
      }
      updateRemark(data)
        .then(res => {
          if (res.code === 200) {
            this.$message.success(res.msg || '保存成功！')
          } else {
            this.$message.error(res.msg || res.message || '保存失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '保存失败！')
        })
    },
    headerDragend (newWidth, oldWidth, column, event) {
      const label = column.label
      newWidth = Math.trunc(newWidth) // 列宽只能允许整数
      console.log(`修改: ${label} width ${oldWidth} -> ${newWidth}`)
      this.columnsConfig.forEach(config => {
        config.children.forEach(child => {
          if (child.label === label) {
            child.minWidth = newWidth + 'px'
          }
        })
      })
      this.defaultColumnsConfig = JSON.parse(JSON.stringify(this.columnsConfig))
      // cachedConfig('backlogOrderColumnsConfig', this.columnsConfig)
      console.log('保存table配置到服务器')
      saveConfig2Server(this.defaultColumnsConfig)
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '保存配置失败！')
        })
    },
    columnSet (newConfig) {
      if (newConfig) {
        this.columnsConfig = newConfig
        this.columns = formatByConfig(this.columnsConfig, getColumns())
        // cachedConfig('backlogOrderColumnsConfig', newConfig)
      }
      this.columnsSetVisible = false
      console.log('保存列设置')
      console.log(newConfig)
      saveConfig2Server(newConfig)
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '保存配置失败！')
        })
    },
    showMoreSearch () {
      this.columnsConfigDeepCopy = JSON.parse(
        JSON.stringify(this.columnsConfig)
      )

      this.columnsSetVisible = true
    },
    changeKeys (serverConfig, defaultConfig) {
      let changed = false
      // 顺序更换
      try {
        if (serverConfig[0].label !== defaultConfig[0].label) {
          defaultConfig = defaultConfig.reverse()
        }
      } catch (err) {
        console.log(err)
      }
      if (
        serverConfig[0].children.length !== defaultConfig[0].children.length ||
        serverConfig[1].children.length !== defaultConfig[1].children.length
      ) changed = true
      serverConfig[0].children = serverConfig[0].children.filter(sConf => {
        // server删除属性
        return !defaultConfig[0].children.every(config => config.label !== sConf.label)
      })
      defaultConfig[0].children.forEach((config, index) => {
        // server增加属性
        if (!serverConfig[0].children.some(sConf => sConf.label === config.label)) {
          changed = true
          serverConfig[0].children.splice(index, 0, config)
        }
      })
      serverConfig[1].children = serverConfig[1].children.filter(sConf => {
        // server删除属性
        return !defaultConfig[1].children.every(config => config.label !== sConf.label)
      })
      defaultConfig[1].children.forEach((config, index) => {
        // server增加属性
        if (!serverConfig[1].children.some(sConf => sConf.label === config.label)) {
          changed = true
          serverConfig[1].children.splice(index, 0, config)
        }
      })
      // 属性字段对应取本地值
      serverConfig[0].children.forEach(child => {
        let dConf = defaultConfig[0].children.filter(d => d.label === child.label)
        if (dConf && dConf[0] && child.prop !== dConf[0].prop) {
          child.prop = dConf[0].prop
          changed = true
        }
      })
      serverConfig[1].children.forEach(child => {
        let dConf = defaultConfig[1].children.filter(d => d.label === child.label)
        if (dConf && dConf[0] && child.prop !== dConf[0].prop) {
          child.prop = dConf[0].prop
          changed = true
        }
      })
      return changed
    },
    checkColumnsConfig (defaultConfig, serverConfig) {
      // 新旧配置更改，配置属性、顺序使用服务端属性、字段删除新增使用本地属性，字段对应prop不一致使用本地属性
      defaultConfig = JSON.parse(JSON.stringify(defaultConfig))
      serverConfig = JSON.parse(JSON.stringify(serverConfig))
      let changed = false
      try {
        changed = this.changeKeys(serverConfig, defaultConfig)
      } catch (err) {
        console.log(err)
        serverConfig = defaultConfig
        changed = true
      }
      return {
        config: serverConfig,
        changed
      }
    },
    initColumns () {
      try {
        const defaultColumns = getColumns()
        this.defaultColumnsConfig = createdConfigByColumns(defaultColumns)
        this.setDefaultColFromCache(getColumns())
        if (this.tableServerConfig) {
          const checkResult = this.checkColumnsConfig(
            this.defaultColumnsConfig,
            this.tableServerConfig
          )
          let columnsConfig = checkResult.config
          if (checkResult.changed) {
            console.log('对比本地不一致, 上传table配置至服务器！')
            saveConfig2Server(columnsConfig)
          }
          console.log('从服务端获取table配置并应用！')
          this.columnsConfig = columnsConfig
          this.columns = formatByConfig(this.columnsConfig, getColumns())
        } else {
          console.log('上传table配置至服务器！')
          saveConfig2Server(this.defaultColumnsConfig)
        }
      } catch (error) {
        this.setDefaultColFromCache(getColumns())
      }
    },
    setDefaultColFromCache (columns) {
      const config = createdConfigByColumns(columns)
      this.columnsConfig = config
      this.columns = columns
    },
    setQueryOrderType (value) {
      this.searchForm.orderTypes = value.join(',')
    },
    setQueryItemType (value) {
      this.searchForm.itemTypes = value.join(',')
    },
    getFormattedParams () {
      let param = { ...this.searchForm }
      try {
        param.voucherNos = param.voucherNos.trim().split(/\s+/).filter(e => e)
      } catch (err) { param.voucherNos = [] }
      return param
    },
    formatOriginBidCustomer (data) {
      const list = this.dictList['noDeliveryReason']
      data.forEach(item => {
        const find = list.find(li => li.code === item.origionBidCustomer)
        if (find && find.name) {
          item.origionBidCustomer = find.name
        }
      })
    },
    getBacklogOrderList () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.loading.backlogLoading = true
          let param = this.getFormattedParams()
          param.current = this.listQueryInfo.current
          param.pageSize = this.listQueryInfo.pageSize
          this.backlogOrderList = []
          getBacklogOrders(param)
            .then(res => {
              this.loading.backlogLoading = false
              if (res.code === 200) {
                this.total = res.totalCount
                if (res.data) {
                  this.formatOriginBidCustomer(res.data)
                  this.backlogOrderList = res.data
                }
              } else {
                this.$message.error(res.msg)
              }
            })
            .catch(() => {
              this.loading.backlogLoading = false
            })
        }
      })
    },
    downloadTpl () {
      window.open('//static.zkh360.com/file/resource/files/%E6%9C%AA%E4%BA%A4_%E5%AF%BC%E5%85%A5%E8%B7%9F%E5%8D%95%E5%A4%87%E6%B3%A8_%E6%A8%A1%E6%9D%BF.xlsx')
    },
    beforeUpload (file) {
      this.loading.importLoading = true
      return this.$validateFileType(file)
    },
    onUploadSuccess (res) {
      this.loading.importLoading = false
      if (res && res.code === 200) {
        const responseMsg = res.msg && res.totalCount && (`上传成功 ${res.totalCount} 条备注！`)
        this.$message.success(responseMsg || '上传完成！')
        setTimeout(() => {
          this.getBacklogOrderList()
        }, 500)
      } else {
        this.$message.error(res.msg || res.message || '上传失败！')
      }
    },
    onUploadError (err) {
      this.loading.importLoading = false
      console.log(err)
      this.$message.error(err.msg || err.message || '上传失败！')
    },
    // 导出按钮
    exportExcel () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.loading.exportLoading = true
          let param = this.getFormattedParams()
          let url = '/oms-opc-front/backlog/export'
          downloadFile(url, param, { method: 'post', ignoreEmpty: true })
            .finally(() => {
              this.loading.exportLoading = false
            })
        }
      })
    },
    // 查询按钮
    handleFilter () {
      this.listQueryInfo.current = 1
      this.getBacklogOrderList()
    },
    // 重置按钮
    handleReset () {
      this.$refs['searchForm'].resetFields()
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading.customerNoLoading = true
        goodsCustomerListSearch({
          customer_name: key
        }).then(res => {
          this.loading.customerNoLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.customerNoOptions = res.data.contents.map(item => {
                return {
                  value: item.customerId,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading.brandIdLoading = true
        goodsBrandListSearch({
          brand_name: key
        }).then(res => {
          this.loading.brandIdLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.brandIdOptions = res.data.contents.map(item => {
                return {
                  value: item.brandId,
                  label: item.brandName
                }
              })
            } else {
              this.brandIdOptions = []
            }
          } else {
            this.brandIdOptions = []
          }
        })
      } else {
        this.brandIdOptions = []
      }
    },
    // 远程查找物料组
    remoteProductGroupIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading.productGroupIdLoading = true
        goodsGroupListSearch({
          group_name: key
        }).then(res => {
          this.loading.productGroupIdLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.productGroupIdOptions = res.data.contents.map(item => {
                return {
                  value: item.productGroupId,
                  label: item.productGroupName
                }
              })
            } else {
              this.productGroupIdOptions = []
            }
          } else {
            this.productGroupIdOptions = []
          }
        })
      } else {
        this.productGroupIdOptions = []
      }
    }
  }
}
</script>

<style lang="scss" scope>
.backlog-search-container .el-loading-mask{
  background-color: rgba(255, 255, 255, 1);
}
</style>
<style lang="scss">
.backlog-search-container {
  .filter-container {
    padding-top: 18px;
    background-color: #f4f4f4;
  }
  .tag-container {
    padding-left: 10px;
    padding-bottom: 5px;
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .customer-tips {
    background: #ffffff;
    margin-top: 15px;
    margin-bottom: 15px;
    padding-left: 0;
    font-size: 14px;
    span {
      text-align: center;
      color: red;
      font-size: 14px;
    }
  }
  .backlog-search-result-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin: 10px 0;
    & > {
      .result-title {
        height: 50px;
        padding: 0 10px 0 20px;
        line-height: 50px;
        font-size: 16px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}

</style>
