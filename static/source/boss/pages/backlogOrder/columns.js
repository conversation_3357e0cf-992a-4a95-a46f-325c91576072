import {
  orderArriStatusFilter, pastStatus<PERSON>ilter,
  SOlinkFilter, supplyStatusFilter,
  YNFilter, dateFormat, hideZero, ifNeedDeliveryFilter, creditFreezeFilter
} from '@/filters/index.js'
import { get, find } from 'lodash'
import { deepClone } from '@/utils/index.js'

// 时间戳转字符串
const timestampToString = function (timestamp) {
  if (!timestamp || timestamp - 0 === 0) {
    return ''
  } else {
    const date = new Date(timestamp - 0)
    // eslint-disable-next-line no-useless-call
    return dateFormat.call(null, 'yyyy-MM-dd', date)
  }
}

export const orderTagOptions = [
  {
    label: '否',
    value: 0
  },
  {
    label: '审批中',
    value: 1
  },
  {
    label: '审批通过',
    value: 2
  }
]

const optionLabelFormat = (options, value) => {
  return get(find(options, (item) => item.value === value), 'label')
}

const SOcolumns = [
  { label: '跟单备注', prop: 'remark', type: 'innerInput', visible: true, minWidth: '200px' },
  { label: '客户规格型号', prop: 'customerSpecificationModel', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '预订单', prop: 'orderTag', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return optionLabelFormat(orderTagOptions, val) } },
  { label: '应发未发', prop: 'ifNeedDelivery', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return ifNeedDeliveryFilter(val) } },
  { label: '客户代码', prop: 'customerNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客户名称', prop: 'customerName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '150px' },
  { label: '订单联系人', prop: 'orderContact', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '150px' },
  { label: '领料人', prop: 'demandUser', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '150px' },
  { label: '客服', prop: 'productCustomerName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客服主管', prop: 'customerServiceSup', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '销售员', prop: 'productSaleName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: 'SAP订单号', prop: 'sapOrderNo', type: 'link', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', valueFormat (val, row) { return SOlinkFilter(val, row) } },
  { label: 'SAP订单行号', prop: 'sapItemNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: 'OMS订单号', prop: 'soNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '信用冻结', prop: 'creditFreeze', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return creditFreezeFilter(val) } },
  { label: '信用超额冻结', prop: 'ifCreditFreeze', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return YNFilter(val) } },
  { label: '信用逾期冻结', prop: 'ifOverDueFreeze', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return YNFilter(val) } },
  { label: '超期未开冻结', prop: 'ifTimeoutUninvoicedFreeze', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return YNFilter(val) } },
  { label: 'OMS行号', prop: 'soItemNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: 'skuno', prop: 'skuNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '物料描述', prop: 'sapMaterialName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '180px' },
  { label: '未税单价', prop: 'freeTaxPrice', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '180px' },
  { label: '含税单价', prop: 'taxPrice', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '180px' },
  { label: '订单数量', prop: 'baseUnitQty', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '单位', prop: 'basicUnitName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: 'VPI物料', prop: 'vpiMaterial', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '成品油', prop: 'ifoil', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '已交货数量', prop: 'clearedQty', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '未交货数量', prop: 'backLogQty', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '开票数量', prop: 'invoiceQuantity', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '销售订单日期', prop: 'sapCreateTime', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return timestampToString(val) } },
  { label: '客户期望送达日期', prop: 'customerDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  // { label: '首个交期', prop: 'requestedDeliveryDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return timestampToString(val) } },
  { label: '订单预计送达日期', prop: 'orderPlanArrivalDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  { label: '订单预计发货日期', prop: 'orderPlanDeliveryDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  { label: '供应侧最新预计送达日期', prop: 'supplierPlanArrivalDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '180px', labelFormat (val) { return timestampToString(val) } },
  { label: '供应侧最新预计发货日期', prop: 'supplierPlanDeliveryDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '180px', labelFormat (val) { return timestampToString(val) } },
  { label: '标准送达日期', prop: 'skuArrivalDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return timestampToString(val) } },
  { label: '标准发货日期', prop: 'sysDeliveryDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  { label: '请求送达日期', prop: 'arrivalDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  { label: '请求发货日期', prop: 'requestedDeliveryDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  { label: '对客承诺送达日期', prop: 'modifiedFirstArrivalDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  { label: '对客承诺发货日期', prop: 'modifiedFirstDeliveryDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  { label: '预订单审批通过时间', prop: 'preorderApprovedDate', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px', labelFormat (val) { return timestampToString(val) } },
  // { label: '发货日期', prop: 'deliveryTime', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return timestampToString(val) } },
  { label: '是否超期', prop: 'overdue', type: 'default', visible: true, minWidth: '100px' },
  { label: '销售订单上的仓库', prop: 'position', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '120px' },
  { label: '整单到货状态', prop: 'orderArriStatus', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return orderArriStatusFilter(val) } },
  { label: '当前供货状态', prop: 'supplyStatus', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return supplyStatusFilter(val) } },
  { label: '付款状态', prop: 'paiddesc', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '在库量', prop: 'inStokQuantity', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '在途量', prop: 'onRouteQuantity', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '未采量', prop: 'unPurchaseQuantity', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '收货省', prop: 'receiverProvince', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '收货人', prop: 'receiverName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '需求部门', prop: 'sapDemandDepartment', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '订单类型', prop: 'orderType', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '销售订单行类型', prop: 'itemType', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '120px' },
  { label: '销售订单行类型描述', prop: 'itemTypeDesc', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '140px' },
  { label: '客户交期敏感', prop: 'customerDateSensitive', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: 'AA加急', prop: 'urgent', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '交货冻结', prop: 'deliveryFreeze', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '自动交货', prop: 'autoDelivery', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '自动拆分', prop: 'autoBatching', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  // { label: '是否叫料', prop: 'bidCustomer', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return YNFilter(val) } },
  { label: '未发货条件', prop: 'origionBidCustomer', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '后补订单', prop: 'backupOrder', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px', labelFormat (val) { return YNFilter(val) } },
  { label: '客户物料号', prop: 'customerMaterialNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客户订单号', prop: 'customerOrderNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客户行号', prop: 'customerLineNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客户物料描述', prop: 'customerMaterialName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客户物料数量', prop: 'customerMaterialQuantity', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客户物料单位', prop: 'customerMaterialUnit', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '展示交期', prop: 'deliveryDay', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '外围系统来源', prop: 'orderSource', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '外围系统来源订单号', prop: 'orderNo', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '135px' },
  { label: '所属集团名称', prop: 'parentName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '销售大区', prop: 'vkgrpKeyParent', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '账期', prop: 'zterm', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '客服经理', prop: 'customerServiceDirector', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '销售经理', prop: 'salesManager', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '销售总监', prop: 'salesDirector', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '销售负责人', prop: 'salesSuperintendent', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '服务中心', prop: 'serviceName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '物料组', prop: 'productGroup', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '品牌', prop: 'brandName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '120px' },
  { label: '商品经理', prop: 'ownerName', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '100px' },
  { label: '订单备注', prop: 'orderNote', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '200px' },
  { label: 'BD销售', prop: 'bdSales', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '200px' },
  { label: '预收款金额', prop: 'collectionAmount', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '200px' },
  { label: '是否集货', prop: 'needCollect', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '200px' },
  { label: '集货仓未交数量', prop: 'collectUnDeliveryQty', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '200px' },
  { label: '订单原因', prop: 'orderReason', type: 'default', mergeable: true, mergeByKey: ['sapOrderNo', 'sapItemNo'], visible: true, minWidth: '200px' }

]
const POcolumns = [
  { label: '未采购说明', prop: 'unpurchaseExplain', type: 'default', visible: true, minWidth: '100px' },
  { label: '采购员', prop: 'buyer', type: 'default', visible: true, minWidth: '100px' },
  { label: '采购单日期', prop: 'createAt', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return timestampToString(val) } },
  { label: '采购单号', prop: 'purchaseOrder', type: 'default', visible: true, minWidth: '100px' },
  { label: '采购订单单据类型', prop: 'purchaseOrderType', type: 'default', visible: true, minWidth: '120px' },
  { label: '采购单行号', prop: 'purchaseItermNo', type: 'default', visible: true, minWidth: '100px' },
  { label: '预计到货时间', prop: 'providerInPlan', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return timestampToString(val) } },
  { label: '采购确认状态', prop: 'purchaseStatus', type: 'default', visible: true, minWidth: '100px' },
  { label: 'SAP确认交期', prop: 'sap_delivery_date', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return timestampToString(val) } },
  { label: '采购数量', prop: 'purchaseQuantity', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return hideZero(val) } },
  { label: '采购单位', prop: 'purchaseUnit', type: 'default', visible: true, minWidth: '100px' },
  { label: '当前占用量', prop: 'atpPurchaseQuantity', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return hideZero(val) } },
  { label: '采购订单备注', prop: 'purchaseDesc', type: 'default', visible: true, minWidth: '200px' },
  { label: '项目文本', prop: 'projectText', type: 'default', visible: true, minWidth: '100px' },
  { label: '到货仓', prop: 'arrivalStock', type: 'default', visible: true, minWidth: '100px' },
  { label: '集货仓', prop: 'deliveryPositionName', type: 'default', visible: true, minWidth: '100px' },
  { label: '集货仓到货数量', prop: 'transferInQty', type: 'default', visible: true, minWidth: '100px' },
  // { label: '待发数量', prop: 'inStockNum', type: 'default', visible: true, minWidth: '100px' },
  { label: '逾期状态', prop: 'pastStatus', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return pastStatusFilter(val) } },
  { label: '逾期天数', prop: 'overdueDays', type: 'default', visible: true, minWidth: '100px', labelFormat (val) { return pastStatusFilter(val) } },
  { label: '采购物流信息', prop: 'straightLogistics', type: 'default', visible: true, minWidth: '120px' },
  { label: '签收单', prop: 'vcReceipt', type: 'default', visible: true, minWidth: '120px' },
  { label: '采购单行文本', prop: 'lineText', type: 'default', visible: true, minWidth: '120px' },
  { label: '是否回传签单', prop: 'signingBackAccepted', type: 'default', visible: true, minWidth: '120px' }

]
const columns = [
  {
    label: '销售订单信息',
    prop: 'SOInfo',
    visible: true,
    type: 'parent',
    children: SOcolumns
  },
  {
    label: '采购信息',
    prop: 'POInfo',
    visible: true,
    type: 'parent',
    children: POcolumns
  }
]

export default function getColumns () {
  return deepClone(columns)
}
