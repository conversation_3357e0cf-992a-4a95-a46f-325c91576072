<template>
  <div class="delivery-container">
    <el-form :inline="true" :model="deliveryQueryData" ref="deliveryQueryForm" class="delivery-form-inline" label-width="145px">
      <el-form-item label="调拨单号" prop="orderNo">
        <el-input v-model="deliveryQueryData.orderNo" placeholder="请输入调拨单号" style="width:192px"></el-input>
      </el-form-item>
      <el-form-item label="" prop="deliveryType">
        <el-select v-model="deliveryQueryData.deliveryType" placeholder="交货单查询" class="delivery-select" style="width: 135px">
          <el-option
            v-for="item in deliveryOptions"
            :key="'orderNo'+item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="deliveryNo">
        <el-input v-model="deliveryQueryData.deliveryNo" placeholder="请输入交货单ID" style="width:192px"></el-input>
      </el-form-item>
      <el-form-item label="交货单状态" prop="status">
        <el-select
          style="width:192px"
          v-model="deliveryQueryData.status"
          placeholder="请选择交货单状态"
        >
          <el-option label="全部" value="" />
          <el-option label="异常" value="EXCEPTION" />
          <el-option :label="item.label" :value="item.value" v-for="item in deliveryStateList" :key="item.value" />
        </el-select>
      </el-form-item>
      <el-row>
        <el-form-item label="创建时间" prop="createDate">
          <el-date-picker
            v-model="deliveryQueryData.createDate"
            type="daterange"
            start-placeholder="创建开始时间"
            end-placeholder="创建结束时间"
            value-format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']">
          </el-date-picker>
        </el-form-item>
        <el-form-item style="margin-Left: 20px">
          <el-button class="dn-normal-bn" type="primary" @click="handleSubmit">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button class="dn-normal-bn" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <div class="notice">友情提醒：STO调拨单自动转DN交货的时间点为每天10点、11点30、14点</div>
    <div style="margin-bottom: 20px" v-show="false">
      <el-button @click="handleRefresh" type="primary">刷新</el-button>
      <el-button @click="handleExport">导出</el-button>
      <el-button @click="handleBatchChangeReason">批量修改未开票原因</el-button>
    </div>
    <el-table
      border
      fit
      highlight-current-row
      v-loading="isLoading"
      :data="deliveryListData"
      style="width: 100%">
      <el-table-column
        align="center"
        type="selection"
        width="40">
      </el-table-column>
      <el-table-column
        align="center"
        type="index"
        prop="rowNo"
        label="序号">
      </el-table-column>
      <el-table-column
        align="center"
        prop="deliveryNo"
        label="OMS交货单号"
        width="180">
          <template slot-scope="{row}">
            <el-tooltip placement="top" effect="light">
              <span>
                {{ row.deliveryNo || "--" }}
                <i class="el-icon-info" style="color:#999"></i>
              </span>
              <TooltipNo
                slot="content"
                :noList="getDeliveryNoTooltip(row)"
              />
            </el-tooltip>
          </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="wbstk"
        label="交货单状态">
        <template slot-scope="{row}">
          <span>{{row.wbstk}}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="lfart"
        label="交货单类型" width="100">
      </el-table-column>
      <el-table-column
        align="center"
        prop="referOrderNo"
        label="调拨单号"
        width="100">
        <template slot-scope="{row}">
          <span>{{ row.referOrderNo || "--" }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        prop="history"
        label="操作历史">
        <template slot-scope="{ row }">
          <el-button type="text" size="mini">操作历史</el-button>
        </template>
      </el-table-column> -->
      <el-table-column
        align="center"
        prop="sapSyncStatusMsg"
        label="SAP同步状态" width="150">
      </el-table-column>
      <el-table-column
        align="center"
        prop="exceptionMessage"
        label="坤合同步状态" width="150">
      </el-table-column>
      <el-table-column
        align="center"
        prop="umwrk"
        label="工厂" width="230">
      </el-table-column>
      <el-table-column
        align="center"
        prop="lgort"
        label="发货库位" width="110">
      </el-table-column>
      <el-table-column
        align="center"
        prop="zkunnr"
        label="客户名称" width="210">
      </el-table-column>
      <el-table-column
        align="center"
        prop="zcont"
        label="采购" width="100">
      </el-table-column>
      <el-table-column
        align="center"
        prop="lfdat"
        label="计划发货" width="180">
      </el-table-column>
      <el-table-column
        align="center"
        prop="gmtCreate"
        label="创建时间" width="180">
      </el-table-column>
      <el-table-column
        align="center"
        fixed="right"
        prop="operation"
        label="操作">
        <template slot-scope="{ row }">
          <el-button type="text" size="mini" @click="toDtl(row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <pagination
        v-show="deliveryListDataTotal > 0"
        :total="deliveryListDataTotal"
        :page.sync="deliveryPaginationQuery.page"
        :limit.sync="deliveryPaginationQuery.size"
        layout="total, prev, pager, next, jumper"
        @pagination="queryDNOrder"
      />
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import * as shortid from 'shortid'
import Pagination from '@/components/Pagination'
import { listStoDN } from '@/api/sto'
import TooltipNo from './components/TooltipNo'
import { deliveryState } from './constants'

export default {
  name: 'OrderDelivery',
  components: {
    Pagination,
    TooltipNo
  },
  data () {
    return {
      deliveryOptions: [
        {
          value: 'SAP',
          label: 'SAP交货单号'
        },
        {
          value: 'OMS',
          label: 'OMS交货单号'
        }
      ],
      orderOptions: [
        {
          value: 'OMS',
          label: 'OMS订单号'
        },
        {
          value: 'SAP',
          label: 'SAP订单号'
        },
        {
          value: 'CUS',
          label: '客户订单号'
        },
        {
          value: 'OUT',
          label: '外围订单号'
        }
      ],
      deliveryQueryData: this.getDefaultQueryParams(),
      showMore: false,
      isLoading: false,
      deliveryListData: [],
      deliveryListDataTotal: 0,
      deliveryPaginationQuery: {
        page: 1,
        size: 50
      },
      customerList: [],
      loadingCustomer: false
    }
  },
  created () {
    this.queryDNOrder()
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    deliveryStateList () {
      return Object.values(deliveryState)
    }
  },
  methods: {
    handleRouter (param) {
      const { type, no } = param
      const soNo = type === 'oms' ? no : ''
      const sapOrderNo = type === 'sap' ? no : ''
      this.$router.jumpToSoOrderDetail({
        query: {
          soNo: soNo,
          sapOrderNo: sapOrderNo,
          id: shortid.generate(),
          refresh: true
        }
      })
    },
    getOrderNoTooltip (item) {
      return item ? [
        { title: 'OMS订单号', no: item.referOrderNo ? item.referOrderNo.split(',') : [''], type: 'oms' },
        { title: 'SAP订单号', no: item.vgbel ? item.vgbel.split(',') : [''], type: 'sap' },
        { title: '客户订单号', no: item.customerReferenceNo ? item.customerReferenceNo.split(',') : [''] },
        { title: '外围订单号', no: item.peripheryReferenceNo ? item.peripheryReferenceNo.split(',') : [''] }
      ] : []
    },
    getDeliveryNoTooltip (item) {
      return item ? [
        { title: 'OMS交货单号', no: item.deliveryNo ? item.deliveryNo.split(',') : [''] },
        { title: 'SAP交货单号', no: item.sapDeliveryNo ? item.sapDeliveryNo.split(',') : [''] }
      ] : []
    },
    getStatus (status) {
      if (status) {
        const s = status.trim().toUpperCase()
        if (s && (s === 'ALL' || s === 'EXCEPTION')) {
          return ''
        }
        return s
      }
      return ''
    },
    getOrderNo () {
      const result = {}
      const { orderType } = this.deliveryQueryData
      if (orderType === 'OMS') {
        result.referOrderNo = this.deliveryQueryData.orderNo
      } else if (orderType === 'SAP') {
        result.vgbel = this.deliveryQueryData.orderNo
      } else if (orderType === 'CUS') {
        result.customerReferenceNo = this.deliveryQueryData.orderNo
      } else if (orderType === 'OUT') {
        result.peripheryReferenceNo = this.deliveryQueryData.orderNo
      }
      return result
    },
    getDeliveryNo () {
      const result = {}
      const { deliveryType } = this.deliveryQueryData
      if (deliveryType === 'OMS') {
        result.sapDeliveryNo = ''
        result.deliveryNo = this.deliveryQueryData.deliveryNo
      } else if (deliveryType === 'SAP') {
        result.deliveryNo = ''
        result.sapDeliveryNo = this.deliveryQueryData.deliveryNo
      }
      return result
    },
    queryDNOrder () {
      const orderNoObj = this.getOrderNo()
      const deliveryNoObj = this.getDeliveryNo()
      const { createDate, status } = this.deliveryQueryData
      const param = {
        ...this.deliveryQueryData,
        ...orderNoObj,
        ...deliveryNoObj,
        exceptionFlag: status && status.trim() === 'EXCEPTION',
        status: this.getStatus(status),
        current: this.deliveryPaginationQuery.page,
        size: this.deliveryPaginationQuery.size
      }
      if (createDate && createDate.length === 2) {
        const m1 = moment(createDate[1])
        const m2 = moment(createDate[0])
        const d = m1.diff(m2, 'months', true)
        if (d > 2) {
          this.$alert('只允许查询两个月内的DN订单！', '错误', {
            type: 'error'
          })
          return
        }
        param.createStartDate = createDate[0]
        param.createEndDate = createDate[1]
      }
      delete param.createDate
      delete param.deliveryType
      delete param.orderNo
      delete param.orderType
      this.clean(param)
      this.isLoading = true
      listStoDN(param).then(res => {
        this.isLoading = false
        if (res && res.code === 200) {
          this.deliveryListDataTotal = res.data.total
          this.deliveryListData = res.data.records
        }
      })
    },
    clean (obj) {
      for (var propName in obj) {
        if (!obj[propName]) {
          delete obj[propName]
        }
      }
    },
    handleReset () {
      // this.$refs['deliveryQueryForm'].resetFields()
      this.deliveryQueryData = this.getDefaultQueryParams()
      this.queryDNOrder()
    },
    handleSubmit () {
      this.queryDNOrder()
    },
    handleMore () {
      this.showMore = !this.showMore
    },
    toDtl (row) {
      this.$router.push({
        path: `/orderDelivery/stoDetail/${row.id}`
      })
    },
    getDefaultQueryParams () {
      const startDate = moment().subtract(7, 'days').format('yyyy-MM-DD')
      const endDate = moment().format('yyyy-MM-DD')
      return {
        orderType: 'OMS',
        deliveryType: 'OMS',
        status: '',
        createDate: [startDate, endDate]
      }
    },
    handleRefresh () {
    },
    handleExport () {
    },
    handleBatchChangeReason () {

    }
  }
}
</script>

<style lang="scss" scoped>
@import "./styles/common.scss";

.delivery-form-inline {
  font-size: 12px;
}
.delivery-container {
  padding: 0 20px;
  .notice {
    line-height: 16px;
    font-size: 12px;
    margin-bottom: 5px;
  }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}
.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.selectClientItem {
  div:nth-child(1) {
    width: 80px;
  }
  div:nth-child(2) {
    width: 80px;
  }
  div:nth-child(3) {
    width: 300px;
  }
}
</style>

<style lang="scss">
.delivery-container {
  .delivery-select {
    .el-input {
      input {
        border: 0;
        text-align: right;
      }
    }
  }
}
</style>
