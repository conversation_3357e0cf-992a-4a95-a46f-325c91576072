<template>
  <el-tabs v-model="activeName">
    <el-tab-pane name="detail">
      <span slot="label"><i class="el-icon-tickets" /> 交货单详情</span>
      <Detail />
    </el-tab-pane>
    <el-tab-pane name="elecDoc">
      <span slot="label"><i class="el-icon-folder-opened" /> 电子文档</span>
      <ElecDoc :activeName="activeName" :voucherNo="no"/>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import Detail from './detail'
import ElecDoc from './ElecDoc'
export default {
  name: 'DetailTab',
  components: { ElecDoc, Detail },
  data () {
    return {
      id: this.$route.params.id,
      no: this.$route.params.id,
      activeName: 'detail'
    }
  },
  created () {
    this.setActiveTab()
  },
  methods: {
    setActiveTab () {
      console.log(this.$route)
      let { hash } = this.$route
      hash = hash.replace('#', '')
      console.log(hash)
      if (/detail|elecDoc/.test(hash)) {
        this.activeName = hash
      }
    }
  }
}
</script>
