<template>
  <div class="dn-edit-container">
    <div class="dn-edit-header">
      <span class="dn-title">编号{{id}}交货单明细{{dnData.statusDesc?`-${dnData.statusDesc}`:''}}</span>
      <div>
        <el-button
          class="dn-normal-bn"
          type="primary"
          @click="refresh"
        >
          刷新
        </el-button>
        <el-button
          class="dn-normal-bn"
          type="warning"
          @click="cancelVisible=true"
          v-if="fsm&&fsm.canGoTo('CANCEL')"
        >
          取消交货单
        </el-button>
        <el-button
          class="dn-normal-bn"
          type="primary"
          @click="handleTransferAccount"
          :disabled="isFreeze"
          v-if="isSupportedAutoAccount"
        >
          一键过账
        </el-button>
        <el-button
          class="dn-normal-bn"
          type="primary"
          @click="handleSubmit"
          :disabled="isFreeze||dnData.systemStatusCode==='10'"
          v-if="dnData.status==='NEW'"
        >
          提交
        </el-button>
      </div>
    </div>
    <div class="dn-row-title">
      基础信息
    </div>
    <BaseInfo
      :data="dnData.basicInfo||{}"
      :type="'detail'"
      @afterAsync="handleAfterAsync"
      :sto="true"
    />
    <div class="dn-row-title">
      交货信息
      <RowMore @fold="handleDeliveryFold" :fold="foldDelivery" />
    </div>
    <Delivery
      :data="dnData.deliveryInfo||{}"
      :basicAndDeliveryInfo="dnData.deliveryInfo || {}"
      :dnData="dnData"
      :fold="foldDelivery"
      :id="id"
      @freezeShipment="freezeShipment"
      @freezeDelivery="freezeDelivery"
    />
    <div class="dn-row-title">
      财务信息
      <RowMore @fold="handleFinanceFold" />
    </div>
    <Finance
      :data="dnData.financeInfo||{}"
      :fold="foldFinance"
      :id="id"
      @freeze="freezeFinance"
      @updateUnbillReason="updateUnbillReason"
    />
    <DividerHeader>行项目明细</DividerHeader>
    <DetailTable
      :data="deliveryNoteListData"
      :isLoading="isLoading"
      :disabled="disabled"
      :status="dnData.status"
      :type="'detail'"
      :sto="true"
    />
    <div class="pagination-container" v-show="false">
      <pagination
        v-show="deliveryNoteListDataTotal > 0"
        :total="deliveryNoteListDataTotal"
        :page.sync="deliveryPaginationQuery.page"
        :limit.sync="deliveryPaginationQuery.size"
        layout="total, prev, pager, next, jumper"
        @pagination="queryDNNoteOrder"
      />
    </div>
    <el-dialog
      title="取消交货单"
      :visible.sync="cancelVisible"
      width="600px"
      :destroy-on-close="true"
    >
      <el-form ref="cancelForm" :model="cancelData" label-width="120px" :rules="rules">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-select v-model="cancelData.cancelReason" placeholder="请选择取消原因" style="width:100%">
            <el-option
              v-for="item in dictList['cancelReason']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="取消原因备注">
          <el-input type="textarea" :rows="3" maxlength="20"
            v-model="cancelData.cancelReasonDesc"
            show-word-limit></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleCancelOrder">确定</el-button>
          <el-button @click="cancelVisible=false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      title="确认商品的批次信息"
      width="900px"
      :visible.sync="batchVisible"
      :destroy-on-close="true"
    >
      <Batch
        :batchData="batchData"
        :listData="deliveryNoteListData"
        @cancel="handleBatchCancel"
        @success="handleBatchSuccess"
      />
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import DividerHeader from '@/components/DividerHeader'
import { getContactDetail, getDNNoteDetail, initNonKhDnBatch,
  postOutBoundDnWithServiceMaterial, unFreezeShipment, unFreezeInvoice,
  unfreezeDelivery, updateDN
} from '@/api/orderDelivery'
import { getDN, cancelDN, submitDN } from '@/api/sto'

import BaseInfo from './components/common/BaseInfo'
import Delivery from './components/detail/Delivery'
import Finance from './components/detail/Finance'
import Batch from './components/detail/Batch'
import DetailTable from './components/common/DetailTable'
import RowMore from './components/common/RowMore'
import { requestWithLoading, isSupportedAutoAccount } from './utils'
import { DeliveryFSM } from './utils/state'

export default {
  name: 'OrderDeliveryEdit',
  components: {
    BaseInfo, Delivery, Finance, Batch, DetailTable, Pagination, DividerHeader, RowMore
  },
  data () {
    const { id } = this.$route.params
    return {
      id,
      dnData: {},
      cancelData: {},
      isLoading: false,
      disabled: true,
      batchVisible: false,
      cancelVisible: false,
      deliveryNoteListData: [],
      deliveryNoteListDataTotal: 0,
      deliveryPaginationQuery: {
        page: 1,
        size: 1000
      },
      fsm: null,
      batchData: [],
      foldDelivery: false,
      foldFinance: true,
      rules: {
        cancelReason: [
          { required: true, message: '请选择取消原因', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList').then(() => {
        this.init()
      })
    } else {
      this.init()
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    isFreeze () {
      return !!(this.dnData &&
        this.dnData.deliveryInfo &&
        this.dnData.deliveryInfo.shipmentFreeze &&
        this.dnData.deliveryInfo.shipmentFreeze !== 'C')
    },
    isSupportedAutoAccount () {
      return isSupportedAutoAccount(this.dnData)
    }
  },
  methods: {
    init () {
      const { id } = this.$route.params
      if (id) {
        this.isLoading = true
        const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
        Promise.all([getDN(id), getDNNoteDetail({
          id,
          size: this.deliveryPaginationQuery.size,
          current: this.deliveryPaginationQuery.page
        })]).then(response => {
          this.isLoading = false
          loading.close()
          const [res1, res2] = response
          if (res2 && res2.code === 200) {
            this.deliveryNoteListData = res2.data.records
            this.deliveryNoteListDataTotal = res2.data.total
          }
          if (res1 && res1.code === 200) {
            const {
              basicInfo: {
                hideLogo,
                requireDepartment
              },
              deliveryInfo: {
                contactId,
                contactInfo,
                receiptTimeCategory,
                attachCoa,
                attachMsds,
                attachOrder,
                attachTds,
                exportProcessingZone,
                referenceStandardShippingReq,
                specifiedDocument,
                serviceCenterSelfTransport,
                shipmentFreeze
              },
              financeInfo: { invoicingByMail },
              status,
              statusDesc,
              sapSyncStatus,
              sapSyncStatusCode,
              positionOwner,
              systemStatus,
              warehouseRuleCode
            } = res1.data
            this.fsm = new DeliveryFSM(res1.data)
            res1.data.deliveryInfo = {
              ...res1.data.deliveryInfo,
              requireDepartment,
              serviceCenterSelfTransport: serviceCenterSelfTransport === '001',
              receiptTimeCategory: receiptTimeCategory === 'X',
              attachCoa: attachCoa === 'X',
              attachMsds: attachMsds === 'X',
              attachOrder: attachOrder === 'X',
              attachTds: attachTds === 'X',
              hideLogo: hideLogo === 'X',
              exportProcessingZone: exportProcessingZone === 'X',
              referenceStandardShippingReq: referenceStandardShippingReq === 'X',
              specifiedDocument: specifiedDocument === 'X',
              shipmentFreeze: !shipmentFreeze || shipmentFreeze === 'C' ? '' : shipmentFreeze
            }
            res1.data.financeInfo = {
              ...res1.data.financeInfo,
              invoicingByMail: invoicingByMail === 'X'
            }
            res1.data.basicInfo['statusDesc'] = statusDesc
            res1.data.basicInfo['positionOwner'] = positionOwner
            res1.data.basicInfo['systemStatus'] = systemStatus
            res1.data.basicInfo['sapSyncStatus'] = sapSyncStatus
            res1.data.basicInfo['sapSyncStatusCode'] = sapSyncStatusCode
            res1.data.basicInfo['warehouseRuleCode'] = warehouseRuleCode
            res1.data.status = status
            this.dnData = res1.data
            if (contactInfo) {
              this.dnData.deliveryInfo = {
                ...this.dnData.deliveryInfo,
                ...contactInfo,
                mobilephone: contactInfo.contactPhone
              }
            } else if (contactId) {
              getContactDetail(contactId).then(res2 => {
                if (res2 && res2.code === 200) {
                  this.dnData.deliveryInfo = {
                    ...this.dnData.deliveryInfo,
                    ...res2.data
                  }
                }
              })
            }
          } else if (res1 && res1.msg) {
            this.$alert(res1.msg, '错误')
          }
        }).catch(error => {
          this.isLoading = false
          loading.close()
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
        })
      }
    },
    handleCancelOrder () {
      this.$refs['cancelForm'].validate((valid, items) => {
        if (valid) {
          const { cancelReason, cancelReasonDesc } = this.cancelData
          const loading = this.$loading({
            lock: true,
            background: 'rgba(0, 0, 0, 0.5)'
          })
          cancelDN({
            cancelReason,
            cancelReasonDesc,
            deliveryNoteNo: this.id,
            status: this.dnData.status
          }).then(res => {
            loading.close()
            if (res && res.code === 200) {
              this.$notify.success('取消成功！')
              this.cancelVisible = false
              this.init()
            } else if (res.msg) {
              this.$alert(res.msg, '错误')
            }
          }).catch(error => {
            loading.close()
            if (error.response && error.response.data) {
              const { message } = error.response.data
              if (message) {
                this.$alert(message, '错误')
              }
            }
          })
        }
      })
    },
    handleTransferAccount () {
      const { id } = this.$route.params
      const { basicInfo } = this.dnData
      if (basicInfo && basicInfo.referOrderType && basicInfo.serviceMaterial) {
        requestWithLoading(this, postOutBoundDnWithServiceMaterial({
          dnNo: id
        }), data => {
          this.$message({
            type: 'success',
            message: '过账成功！'
          })
          this.init()
        })
      } else {
        requestWithLoading(this, initNonKhDnBatch({
          dnNo: id
        }), data => {
          this.batchData = data
          this.batchVisible = true
        })
      }
    },
    handleEdit () {
      const { id } = this.$route.params
      this.$router.push({
        path: `/orderDelivery/edit/${id}`
      })
    },
    handleSubmit () {
      const { id } = this.$route.params
      this.$confirm('是否确认将DN交货单提交，下发至仓库?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
        submitDN({
          deliveryNoteNo: id,
          status: this.dnData.status
        }).then(res => {
          loading.close()
          if (res) {
            if (res.code === 200) {
              this.init()
            } else if (res.msg) {
              this.$alert(res.msg, '错误')
            }
          }
        }).catch(error => {
          loading.close()
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
        })
      }).catch(() => {})
    },
    queryDNNoteOrder () {
      this.isLoading = true
      const { id } = this.$route.params
      const params = {
        id,
        size: this.deliveryPaginationQuery.size,
        current: this.deliveryPaginationQuery.page
      }
      getDNNoteDetail(params).then(res => {
        this.isLoading = false
        if (res && res.code === 200) {
          this.deliveryNoteListData = res.data.records
          this.deliveryNoteListDataTotal = res.data.total
        }
      }).catch(error => {
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误')
          }
        }
        this.isLoading = false
      })
    },
    handleBatchCancel () {
      this.batchVisible = false
    },
    handleBatchSuccess () {
      this.batchVisible = false
      this.init()
    },
    handleDeliveryFold (val) {
      this.foldDelivery = val
    },
    handleFinanceFold (val) {
      this.foldFinance = val
    },
    freezeShipment () {
      requestWithLoading(this, unFreezeShipment(this.id), () => {
        this.init()
      })
    },
    freezeDelivery () {
      requestWithLoading(this, unfreezeDelivery(this.id), () => {
        this.init()
      })
    },
    freezeFinance () {
      requestWithLoading(this, unFreezeInvoice(this.id), () => {
        this.init()
      })
    },
    updateUnbillReason (val) {
      requestWithLoading(this, updateDN({
        deliveryNoteNo: this.id,
        status: this.dnData.status,
        unbillReason: val || '*'
      }), () => {
        this.dnData.financeInfo.unbillReason = val
      })
    },
    handleAfterAsync () {
      this.init()
    },
    refresh () {
      this.init()
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./styles/common.scss";
.dn-edit-container {
  padding: 15px;
}

.dn-edit-header {
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: space-between;
  .dn-title {
    font-size: 18px;
    font-weight: bold;
  }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}
.dn-row-btn-list {
  display: flex;
  margin: 10px 0;
  align-items: center;
  justify-content: space-between;
}
</style>
