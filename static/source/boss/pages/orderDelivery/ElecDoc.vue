<template>
  <div class="ele-document-container">
    <div style="margin-left: 132px; margin-bottom: 10px">
      <el-popover placement="top" width="120" trigger="click" >
        <div class="content" style="display: flex;flex-direction: column;">
        <el-button type="primary" plain size="mini" @click="showUploadDialog({deliveryNo:voucherNo},'deliveryNote')">交货单附件</el-button> <br />
        <el-button type="primary" plain size="mini" @click="showUploadDialog({deliveryNo:voucherNo},'other')">其他随货资料</el-button> <br />
        <el-button type="primary" plain size="mini" @click="showUploadDialog({deliveryNo:voucherNo},'label')">标签附件</el-button>
        </div>
        <el-button type="primary" plain size="mini" slot="reference">
          继续上传<i class="el-icon-upload el-icon--right" />
        </el-button>
      </el-popover>
    </div>
    <el-tabs v-model="activeTab" tab-position="left" class="document_tab_con">
      <el-tab-pane name="doc" :label="`文档(${pageInfo.docTotal + pageInfo.docSignTotal + pageInfo.otherDocTotal + pageInfo.labelDocTotal})`">
        <div class="tab-table-title">交货单附件</div>
        <el-table v-loading="docListLoading" :data="docList" border fit highlight-current-row>
          <el-table-column label="序号" type="index"
            :index="i => getIndex(i, pageInfo.docPageNo, pageInfo.docPageSize)"
            width="100" align="center" fixed="left"/>
          <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
          <el-table-column label="类型" min-width="50px" align="center" prop="fileType"/>
          <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
          <el-table-column label="创建时间" min-width="100px" align="center" prop="uploadTime"/>
          <el-table-column label="备注" min-width="100px" align="center" prop="remark" show-overflow-tooltip/>
          <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="remarkEdit(row, 'order')">
                {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
              </el-button>
              <el-button type="text" size="mini" @click="previewFile(row)">预览</el-button>
              <el-button type="text" size="mini" @click="download(row)">下载</el-button>
              <el-button type="text" size="mini" @click="del(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="pageInfo.docTotal > pageInfo.docPageSize"
          :total="pageInfo.docTotal"
          :page.sync="pageInfo.docPageNo"
          :limit.sync="pageInfo.docPageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="getDocList('deliveryNote', 'docList')"
        />
        <div class="tab-table-title" style="margin-top: 30px;">其他随货资料</div>
        <el-table v-loading="docListLoading" :data="otherDocList" border fit highlight-current-row>
          <el-table-column label="序号" type="index"
            :index="i => getIndex(i, pageInfo.otherDocPageNo, pageInfo.otherDocPageSize)"
            width="100" align="center" fixed="left"/>
          <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
          <el-table-column label="类型" min-width="50px" align="center" prop="fileType"/>
          <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
          <el-table-column label="创建时间" min-width="100px" align="center" prop="uploadTime"/>
          <el-table-column label="备注" min-width="100px" align="center" prop="remark" show-overflow-tooltip/>
          <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="remarkEdit(row, 'order')">
                {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
              </el-button>
              <el-button type="text" size="mini" @click="previewFile(row)">预览</el-button>
              <el-button type="text" size="mini" @click="download(row)">下载</el-button>
              <el-button type="text" size="mini" @click="del(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="pageInfo.otherDocTotal > pageInfo.docPageSize"
          :total="pageInfo.otherDocTotal"
          :page.sync="pageInfo.otherDocPageNo"
          :limit.sync="pageInfo.otherDocPageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="getDocList('other', 'otherDocList')"
        />
        <div class="tab-table-title" style="margin-top: 30px;">标签附件</div>
        <el-table v-loading="docListLoading" :data="labelDocList" border fit highlight-current-row>
          <el-table-column label="序号" type="index"
            :index="i => getIndex(i, pageInfo.labelDocPageNo, pageInfo.labelDocPageSize)"
            width="100" align="center" fixed="left"/>
          <el-table-column label="文件名" min-width="100px" align="center" prop="fileName"/>
          <el-table-column label="类型" min-width="50px" align="center" prop="fileType"/>
          <el-table-column label="上传人" min-width="50px" align="center" prop="upUserName"/>
          <el-table-column label="创建时间" min-width="100px" align="center" prop="uploadTime"/>
          <el-table-column label="备注" min-width="100px" align="center" prop="remark" show-overflow-tooltip/>
          <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="remarkEdit(row, 'order')">
                {{ Boolean(row.remark) ? '编辑备注' : '添加备注' }}
              </el-button>
              <el-button type="text" size="mini" @click="previewFile(row)">预览</el-button>
              <el-button type="text" size="mini" @click="download(row)">下载</el-button>
              <el-button type="text" size="mini" @click="del(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="pageInfo.labelDocTotal > pageInfo.docPageSize"
          :total="pageInfo.labelDocTotal"
          :page.sync="pageInfo.labelDocPageNo"
          :limit.sync="pageInfo.labelDocPageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="getDocList('label', 'labelDocList')"
        />
      </el-tab-pane>
      <el-tab-pane name="recycle" :label="`回收站(${pageInfo.recycleTotal})`">
        <el-row>
          <el-col :span="24">
            <div class="recycle_tips">
              <i class="el-icon-warning" />图片和文件在删除前会显示剩余天数，之后将永久删除。
            </div>
          </el-col>
        </el-row>
        <el-table v-loading="recycleListLoading" :data="recycleList" border fit highlight-current-row>
          <el-table-column label="序号" type="index" fixed="left" width="100" align="center"
            :index=" i => getIndex(i,pageInfo.recyclePageNo, pageInfo.recyclePageSize)"
          />
          <el-table-column label="文件名" min-width="100px" align="center" prop="fileName" />
          <el-table-column label="类型" min-width="50px" align="center" prop="fileType" />
          <el-table-column label="上传人" min-width="50px" align="center" prop="upLoadName" />
          <el-table-column label="创建时间" min-width="100px" align="center" prop="createTime" />
          <el-table-column label="操作" align="center" width="300" fixed="right" class-name="small-padding fixed-width">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="resume(row)">恢复</el-button>
              <span v-if="row.remainDay !== null" style="color: red">【{{ row.remainDay }}天后删除】</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="pageInfo.recycleTotal"
          :total="pageInfo.recycleTotal"
          :page.sync="pageInfo.recyclePageNo"
          :limit.sync="pageInfo.recyclePageSize"
          layout="total, prev, pager, next, jumper"
          @pagination="edDustbinList"
        />
      </el-tab-pane>
    </el-tabs>
    <el-dialog title="添加备注" :visible.sync="remarkVisible" width="600px">
      <el-form ref="dataForm" :rules="rules"
        :model="content" label-position="right" label-width="0px">
        <el-form-item label prop="remark">
          <el-input v-model="content.remark" clearable
            type="textarea" :autosize="{ minRows: 4, maxRows: 7 }" placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="remarkVisible = false">关闭</el-button>
        <el-button type="primary" @click="remarkSave">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="预览" :visible.sync="previewVisible"
      width="600px" top="10vh">
      <div style="width: 100%; max-height: 400px; overflow: auto; text-align: center; margin-bottom: 10px">
        <el-image v-if="previewImg" :src="previewImg" :preview-src-list="[previewImg]" />
        <i v-else class="el-icon-loading" />
      </div>
      <el-alert style="margin-bottom: 10px" title="点击图片查看原图"
        type="warning" show-icon close-text="知道了"/>
      <el-form ref="dataForm" :rules="rules"
        :model="content" label-position="right" label-width="0px">
        <el-form-item label prop="remark">
          <el-input v-model="content.remark" clearable type="textarea"
            :autosize="{ minRows: 4, maxRows: 7 }" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="remarkSave">保存</el-button>
      </div>
    </el-dialog>
    <el-dialog title="上传附件" :visible.sync="uploadDialogStatus" :before-close="handleUploadClose" :show-close="false">
      <div v-loading="uploadLoading" class="dialog-body" style="display:flex;align-items: center;flex-direction:column">
        <span  style="margin:10px;color:#597bee">
          {{'可将文件直接拖拽到改区域、或者点击上传按钮，仅支持PDF' + (uploadType === 'label' ? '/EXCEL' : '') + '附件哟'}}
        </span>
        <el-upload ref="uploadDialog" action="/ali-upload"
          style="display: inline-block;" drag
          :show-file-list="true" multiple
          :with-credentials="true" :limit="5"
          :data="{appName: omsAppName}"
          :on-success="handleUploadSucess"
          :on-remove="handleUploadRemove"
          :on-error="handleUploadError"
          :before-upload="handleBeforeUpload"
          :on-exceed="handleUploadExceed"
          :accept="uploadType === 'label' ? '.xls, .xlsx, .xlsm, .csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/pdf ,.pdf': 'application/pdf,.pdf'"
          >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text"><em>点击或拖拽上传</em></div>
        </el-upload>
        <div style="display:flex; align-items: center;margin: 10px;">
          <span>打印方向</span>
          <el-select v-model="printDirection" style="width: 120px;margin-left: 10px;">
            <el-option value="cross" label="横向"></el-option>
            <el-option value="vertical" label="纵向"></el-option>
          </el-select>
        </div>
        <span slot="footer" class="dialog-footer" style="align-self:flex-end;margin-top:20px;">
          <el-button @click="cancelUpload">取消</el-button>
          <el-button @click="submitUpload" type="primary">提交</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import moment from 'moment'
import {
  edDustbinList,
  docList,
  upEcorpRemark,
  delEcorpDoc,
  recoverEcorpDoc,
  downloadFile,
  saveOssFileInfo,
  elePicSignAttachment,
  eleDocSignAttachment
} from '@/api/ecorp'

export default {
  name: 'EleDocument',
  components: {
    Pagination
  },
  props: {
    voucherNo: {
      type: String,
      required: true,
      default: ''
    },
    activeName: {
      type: String,
      required: true,
      default: ''
    }
  },
  data () {
    return {
      omsAppName: window.omsAppName,
      printDirection: 'cross',
      uploadLoading: false,
      uploadType: '',
      uploadListRows: [],
      uploadDialogStatus: false,
      fileSizeLimit: 10,
      fileName: '',
      imgListLoading: false,
      imgSignListLoading: false,
      docListLoading: false,
      docSignListLoading: false,
      recycleListLoading: false,
      remarkVisible: false,
      previewVisible: false,
      activeTab: 'doc', // doc, recycle
      imgList: [],
      docList: [],
      otherDocList: [],
      labelDocList: [],
      imgSignList: [],
      docSignList: [],
      recycleList: [],
      pageInfo: {
        // 订单图片
        imgPageNo: 1,
        imgPageSize: 10,
        imgTotal: 0,
        // 签收单图片
        imgSignPageNo: 1,
        imgSignPageSize: 10,
        imgSignTotal: 0,
        // 订单文档
        docPageNo: 1,
        docPageSize: 10,
        docTotal: 0,
        // 其他随货单文档
        otherDocPageNo: 1,
        otherDocPageSize: 10,
        otherDocTotal: 0,
        // 标签附件
        labelDocPageNo: 1,
        labelDocPageSize: 10,
        labelDocTotal: 0,
        // 签收单文档
        docSignPageNo: 1,
        docSignPageSize: 10,
        docSignTotal: 0,
        // 订单回收站
        recyclePageNo: 1,
        recyclePageSize: 10,
        recycleTotal: 0
      },
      content: {
        id: '',
        remark: ''
      },
      previewImg: '',
      rules: {
        remark: [{ max: 200, message: '长度为200字符以内！', trigger: 'blur' }]
      },
      searchForm: {
        id: this.$route.query.id || ''
      },
      hasLoaded: false,
      skuSearchForm: {},
      activeCollapseName: '1'
    }
  },
  watch: {
    activeName: {
      handler (newName, oldName) {
        if (!this.hasLoaded && this.voucherNo && newName === 'elecDoc') {
          this.getList()
        }
      },
      immediate: true
    },
    activeTab: {
      handler (newName, oldName) {
        if (this.activeName === 'elecDoc') {
          if (newName === 'doc') {
            this.getDocList('deliveryNote', 'docList')
            this.getDocList('other', 'otherDocList')
            this.getDocList('label', 'labelDocList')
          } else if (newName === 'recycle' && this.recycleList.length === 0) {
            this.edDustbinList()
          }
        }
      }
    }
  },
  created () {},
  methods: {
    throwCheckMsg (callback) {
      callback && callback()
    },
    handleBeforeUpload (file) {
      const size = file.size / 1024 / 1024
      const isGtLimit = size > this.fileSizeLimit
      const isPDF = /pdf/i.test(file.name)
      let pass = true
      if (!this.$validateFileType(file)) return false

      if (isGtLimit) {
        pass = false
        this.returnMsg += `【${file.name}】大小：${size}M，上传文件不能超过` + this.fileSizeLimit + 'MB！<br/>'
      }
      if (this.uploadType === 'order' && !isPDF) {
        pass = false
        this.returnMsg += `【${file.name}】不是pdf文件类型，只能上传PDF文件！<br/>`
      }
      if (!pass) {
        this.throwCheckMsg(() => {
          this.$message({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: this.returnMsg
          })
          this.returnMsg = ''
        })
      }
      return pass
    },
    saveFileInfo (deliveryNo, docMetaDataList = []) {
      const queryData = {
        source: 'BOSS',
        dimension: 'dn',
        businessId: deliveryNo,
        docMetaDataList
      }
      return saveOssFileInfo(queryData)
    },
    resetUploadData () {
      this.uploadDialogStatus = false
      this.uploadListRows = []
      this.uploadList = []
      this.$refs.uploadDialog && this.$refs.uploadDialog.clearFiles()
    },
    cancelUpload () {
      this.resetUploadData()
    },
    async submitUpload () {
      if (this.uploadList.filter(file => file.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      const uploadListRows = this.uploadListRows
      const uploadList = this.uploadList.map(upload => ({
        bucketName: upload.bucketName,
        fileName: upload.name,
        ossKey: upload.ossKey,
        printDirection: this.printDirection,
        upUserName: window.CUR_DATA.user && window.CUR_DATA.user.name,
        attachmentType: this.uploadType ? this.uploadType : 'deliveryNote',
        uploadTime: moment(new Date()).format('yyyy-MM-DD HH:mm:ss')
      }))
      let responseList = []
      if (!uploadList.length) return this.$message.error('请上传文件！')
      this.uploadLoading = true
      for (let row of uploadListRows) {
        const res = await this.saveFileInfo(row.deliveryNo, uploadList)
        res.businessId = row.deliveryNo
        responseList.push(res)
        if (res.code === 200) {
          this.$message.success('上传成功！')
        } else {
          this.$message.error(res.msg)
        }
      }
      this.uploadLoading = false
      this.getList()
      this.resetUploadData()
    },
    handleUploadClose (done) {
      this.cancelUpload()
      done && done()
    },
    showUploadDialog (row, type) {
      this.uploadType = type || this.uploadType
      this.uploadListRows = Array.isArray(row) ? row : [row]
      this.uploadListRows = this.uploadListRows.filter(row => row.deliveryNo)
      this.uploadDialogStatus = true
    },
    handleUploadSucess (res, file, fileList) {
      this.uploadList = fileList.map(file => ({
        ...file,
        fileName: file.response && file.response[0] && file.response[0].name,
        ossKey: file.response && file.response[0] && file.response[0].objectKey,
        bucketName: file.response && file.response[0] && file.response[0].bucketName
      }))
    },
    handleUploadRemove (file, fileList) {
      this.uploadList = fileList.map(file => ({
        ...file,
        fileName: file.response && file.response[0] && file.response[0].name,
        ossKey: file.response && file.response[0] && file.response[0].objectKey,
        bucketName: file.response && file.response[0] && file.response[0].bucketName
      }))
    },
    handleUploadError (error) {
      this.$message.error(error.error || error.message || '上传失败！')
    },
    handleUploadExceed (error) {
      this.$message.error(error.error || error.message || '文件最多上传5个！')
    },
    fileType (url) {
      let ret = ''
      if (/pdf/.test(url)) { ret = 'pdf' }
      if (/xls(x|m)?/.test(url)) { ret = 'excel' }
      if (/doc(s|x)?/.test(url)) { ret = 'word' }
      return ret
    },
    previewFile (row) {
      const type = this.fileType(row.url)
      if (type === 'pdf') { window.open(row.url) } else if (type === 'excel' || type === 'word') {
        window.open(`https://view.officeapps.live.com/op/view.aspx?src=${row.url}`)
      }
    },
    arraySpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0 && row.expandItem === true) { return [1, 7] }
    },
    seeDetail (row) {
      if (row.expand === true) {
        this.hideDnDetail(row)
      } else { this.expandDnDetail(row) }
    },
    hideDnDetail (row) {
      if (row.expand === true) {
        row.expand = false
        const index = this.imgSignList.indexOf(row)
        this.imgSignList.splice(index + 1, 1)
      }
    },
    expandDnDetail (row) {
      setTimeout(() => {
        row.expand = true
        const dnDetailList = row.dnDetail || []
        const dnDetail = {}
        dnDetail.expandItem = true
        dnDetail.dnDetail = dnDetailList
        const index = this.imgSignList.indexOf(row)
        this.imgSignList.splice(index + 1, 0, dnDetail)
      })
    },
    resetTableList () {
      this.imgSignList = this.imgSignList.filter(img => img.dnNo !== undefined)
      this.imgSignList.forEach(row => { row.expand = false })
    },
    handleSkuFilter () {
      this.resetTableList()
      if (!this.skuSearchForm.sku) return
      let hasResult = false
      this.imgSignList.forEach(row => {
        if (row.dnNo === undefined || !Array.isArray(row.dnDetail)) return
        let item = row.dnDetail.filter(detail => detail.productNumber.indexOf(this.skuSearchForm.sku) !== -1)
        if (item && item[0] && row.dnNo !== undefined) {
          hasResult = true
          this.expandDnDetail(row)
        }
      })
      if (!hasResult) {
        this.$message.success('没有结果 ！')
      }
    },
    getIndex (rowIndex, pageNo, pageSize) {
      return rowIndex + 1 + (pageNo - 1) * pageSize
    },
    getList () {
      this.getDocList('deliveryNote', 'docList')
      this.getDocList('other', 'otherDocList')
      this.getDocList('label', 'labelDocList')
      this.edDustbinList()
    },
    getDocList (attachmentType, docLists) {
      this.docListLoading = true
      let totalName
      let pageNo
      let pageSize
      switch (attachmentType) {
        case 'deliveryNote':
          totalName = 'docTotal';
          pageNo = this.pageInfo.docPageNo;
          pageSize = this.pageInfo.docPageSize;
          break;
        case 'other':
          totalName = 'otherDocTotal';
          pageNo = this.pageInfo.otherDocPageNo;
          pageSize = this.pageInfo.otherDocPageSize;
          break;
        case 'label':
          totalName = 'labelDocTotal';
          pageNo = this.pageInfo.labelDocPageNo;
          pageSize = this.pageInfo.labelDocPageSize;
          break;
      }

      docList(this.voucherNo, {
        pageNo,
        pageSize,
        attachmentType
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this[docLists] = response.data.edList || []
              this.pageInfo[totalName] = response.data.total || 0
            } else {
              this[docLists] = []
              this.pageInfo[totalName] = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.docListLoading = false
          this.hasLoaded = true
        })
        .catch(err => {
          this.$notify.error(err.msg || err.message || '操作失败！')
          this.docListLoading = false
          this.hasLoaded = true
        })
    },
    // 查询签收单
    getSignImgList () {
      this.imgSignListLoading = true
      elePicSignAttachment(this.voucherNo, {
        current: this.pageInfo.imgSignPageNo,
        pageSize: this.pageInfo.imgSignPageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.imgSignList = response.data || []
              this.pageInfo.imgSignTotal = response.totalCount || 0
            } else {
              this.imgSignList = []
              this.pageInfo.imgSignTotal = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.imgSignListLoading = false
          this.hasLoaded = true
        })
        .catch(e => {
          this.imgSignListLoading = false
          this.hasLoaded = true
        })
    },
    getSignDocList () {
      this.docSignListLoading = true
      eleDocSignAttachment(this.voucherNo, {
        current: this.pageInfo.docSignPageNo,
        pageSize: this.pageInfo.docSignPageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.docSignList = response.data || []
              this.pageInfo.docSignTotal = response.totalCount || 0
            } else {
              this.docSignList = []
              this.pageInfo.docSignTotal = 0
            }
          } else {
            this.$notify.error(response.msg)
          }
          this.docSignListLoading = false
          this.hasLoaded = true
        })
        .catch(e => {
          this.docSignListLoading = false
          this.hasLoaded = true
        })
    },

    // 查询回收站列表
    edDustbinList () {
      this.recycleList = []
      this.pageInfo.recycleTotal = 0
      // this.listLoading = true
      edDustbinList(this.voucherNo, {
        pageNo: this.pageInfo.recyclePageNo - 1,
        pageSize: this.pageInfo.recyclePageSize
      })
        .then(response => {
          if (response.code === 200) {
            if (response.data) {
              this.recycleList = response.data.edInDustbinList || []
              this.pageInfo.recycleTotal = response.data.total || 0
              this.pageInfo.recyclePageNo = response.data.current + 1 || 1
            }
          } else {
            this.$notify.error(response.msg)
          }
        })
        .catch(e => {})
    },
    preview (row) {
      // 回显备注
      const { id, remark } = row
      this.content = { id, remark }
      this.previewVisible = true
      if (row.url) {
        this.previewImg = row.url
      } else {
        const param = {
          ossKeyName: row.ossKey
        }
        downloadFile(param)
          .then(response => {
            if (response.code === 200) {
              this.previewImg = response.data
            } else {
              this.$notify.error(response.msg)
            }
          })
          .catch(e => {
            this.$message.error('预览失败')
          })
      }
    },
    del (row) {
      this.$confirm(`确定要删除文件【${row.fileName}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteById(row.id)
        })
        .catch(() => {
          this.$message.info('已取消')
        })
    },
    deleteById (id) {
      const param = {
        // vId: this.searchForm.id,
        vNo: this.voucherNo,
        id: id,
        dimension: 'dn'
      }
      delEcorpDoc(param).then(response => {
        if (response.code === 200) {
          this.$notify.success('操作成功！')
          this.getList()
        } else {
          this.$notify.error(response.msg)
        }
      })
    },
    download (row) {
      if (row.url) {
        this.downloadLink(row.url, row.fileName)
      } else {
        const param = {
          ossKeyName: row.ossKey
        }
        downloadFile(param).then(response => {
          if (response.code === 200) {
            this.$notify.success('操作成功！')
            this.downloadLink(response.data)
          } else {
            this.$notify.error(response.msg)
          }
        })
      }
    },
    downloadLink (href, filaName) {
      const downloadElement = document.createElement('a')
      downloadElement.href = href
      downloadElement.target = '_blank'
      filaName && (downloadElement.download = filaName)
      document.body.appendChild(downloadElement)
      downloadElement.click()
      document.body.removeChild(downloadElement)
    },
    // 恢复文件操作
    resume (row) {
      this.$confirm(`确定要恢复文件【${row.fileName}】?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.resumeById(row.id)
        })
        .catch(() => {
          this.$message.info('已取消')
        })
    },
    // 确认恢复
    async resumeById (id) {
      const param = {
        // vId: this.searchForm.id,
        vNo: this.voucherNo
      }
      recoverEcorpDoc(id, param).then(response => {
        if (response.code === 200) {
          this.$notify.success('操作成功！')
          this.getList()
        } else {
          this.$notify.error(response.msg)
        }
      })
    },
    remarkEdit (row, type) {
      const { remark } = row
      let id
      if (type === 'order') {
        id = row.id
      } else {
        id = row.ecorpAttachments[0].id
      }
      this.content = { id, remark }
      this.remarkVisible = true
    },
    remarkSave () {
      this.$refs.dataForm.validate(valid => {
        if (valid) {
          this.saveData()
        } else {
          this.$notify.error('字段校验未通过！')
        }
      })
    },
    async saveData () {
      if (!this.content.remark) {
        return this.$notify.error('请输入备注！')
      }
      const param = { remark: this.content.remark }
      upEcorpRemark(this.content.id, param)
        .then(response => {
          if (response.code === 200) {
            this.$notify.success('操作成功！')
            this.getList()
          } else {
            this.$notify.error(response.msg)
          }
        })
        .catch(err => {
          this.$notify.error(err.msg || err.message || '操作失败！')
        })
        .finally(() => {
          this.remarkVisible = false
          this.previewVisible = false
        })
    }
  }
}
</script>
<style lang="scss">
.ele-document-container {
  .document_tab_con .el-tabs__item {
    width: 122px;
    height: 32px;
    line-height: 30px;
    background-color: #fff;
    border-radius: 2px;
    border: solid 1px #fff;
    text-align: center !important;
    color: #666666;
    margin-bottom: 10px;
    position: relative;
  }
  .document_tab_con .el-tabs__item:hover {
    border: solid 1px #dfe4ed;
  }
  .document_tab_con .el-tabs__item.is-active {
    border: solid 1px #dfe4ed;
    color: #1890ff;
    font-weight: bold;
  }
  .document_tab_con .el-tabs__item.is-active::after {
    content: '';
    width: 0;
    height: 0;
    border: 5px dashed transparent;
    border-left: 6px solid #b5c9ed;
    position: absolute;
    right: 3px;
    top: 11px;
  }
  .document_tab_con .el-tabs__active-bar {
    display: none;
  }
  .document_tab_con .el-tabs__nav-wrap::after {
    display: none;
  }
  .recycle_tips {
    color: #f23d3d;
    font-size: 12px;
    line-height: 32px;
  }
  .recycle_tips i {
    font-size: 18px;
    margin-right: 3px;
    position: relative;
    top: 2px;
  }
  .visible-h {
    visibility: hidden !important;
  }
  .pagination-container {
    text-align: right;
  }
  .tab-table-title {
    font-size: 16px;
    margin-bottom: 10px;
  }
}
</style>
