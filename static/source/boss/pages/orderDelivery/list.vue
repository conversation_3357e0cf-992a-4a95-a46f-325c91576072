<template>
  <div class="delivery-container">
    <el-form :inline="true" :model="deliveryQueryData" ref="deliveryQueryForm" class="delivery-form-inline" label-width="145px">
      <el-form-item label="" prop="orderType">
        <el-select v-model="deliveryQueryData.orderType" placeholder="订单查询" class="delivery-select" style="width: 135px">
          <el-option
            v-for="item in orderOptions"
            :key="'orderType'+item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="orderNo">
        <el-input v-model="deliveryQueryData.orderNo" placeholder="请输入订单ID" style="width:192px" clearable></el-input>
      </el-form-item>
      <el-form-item label="" prop="deliveryType">
        <el-select v-model="deliveryQueryData.deliveryType" placeholder="交货单查询" class="delivery-select" style="width: 135px">
          <el-option
            v-for="item in deliveryOptions"
            :key="'orderNo'+item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="" prop="deliveryNo">
        <el-input v-model="deliveryQueryData.deliveryNo" placeholder="请输入交货单ID" style="width:192px" clearable></el-input>
      </el-form-item>
      <el-form-item label="交货单状态" prop="status">
        <el-select
          style="width:192px"
          v-model="deliveryQueryData.status"
          placeholder="请选择交货单状态"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option label="异常" value="EXCEPTION" />
          <el-option :label="item.label" :value="item.value" v-for="item in deliveryStateList" :key="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="交货冻结原因" prop="lifsk">
        <el-select
          style="width:192px"
          v-model="deliveryQueryData.lifsk"
          filterable
          placeholder="请选择交货冻结原因"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in dictList['deliveryFreeze']"
            :key="'lifsk'+item.code"
            :label="item.name"
            :value="item.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="客户名称" prop="zkunnr">
        <el-select
          ref="customer"
          v-model="deliveryQueryData.zkunnr"
          filterable
          clearable
          remote
          reserve-keyword
          placeholder="请输入客户编号/名称"
          style="width:192px"
          :remote-method="queryCustomerList"
          :loading="loadingCustomer"
        >
          <el-option
            v-for="(item, index) in customerList"
            :key="'zkunnr'+item.customerId"
            :label="item.customerName"
            :value="item.customerNumber"
            :disabled="index===0"
          >
            <div
              class="ba-row-start selectClientItem"
              :style="{fontWeight:index===0?'bold':'normal'}"
            >
              <div>{{ item.customerNumber }}</div>
              <div>{{ item.cityName }}</div>
              <div>{{ item.customerName }}</div>
            </div>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客服" prop="zcont">
        <el-input style="width:192px" v-model="deliveryQueryData.zcont" placeholder="客服" clearable></el-input>
      </el-form-item>
      <el-row>
        <el-form-item label="创建时间" prop="createDate">
          <el-date-picker
            v-model="deliveryQueryData.createDate"
            type="daterange"
            start-placeholder="创建开始时间"
            end-placeholder="创建结束时间"
            value-format="yyyy-MM-dd"
            :default-time="['00:00:00', '23:59:59']">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button @click="handleMore">更多<i :class='showMore ? "el-icon-arrow-up" : "el-icon-arrow-down"'></i></el-button>
        </el-form-item>
      </el-row>
      <div v-show="showMore">
        <el-form-item label="交货类型" prop="lfart">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.lfart"
            placeholder="请选择交货类型"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in dnOrderTypeOptions"
              :key="item.parentCode+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="销售凭证类型" prop="referOrderType">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.referOrderType"
            placeholder="请选择销售凭证类型"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in dictList['soCategory']"
              :key="'referOrderType'+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="销售组织" prop="vkorg">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.vkorg"
            placeholder="请选择销售组织"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in dictList['salesGroup']"
              :key="'vkorg'+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="装运冻结原因" prop="trspg">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.trspg"
            placeholder="请选择装运冻结原因"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in dictList['shippingFreeze']"
              :key="'trspg'+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建者" prop="creator">
          <el-input style="width:192px" v-model="deliveryQueryData.creator" placeholder="创建者" clearable></el-input>
        </el-form-item>
        <el-form-item label="计划发货日期" prop="khdat">
          <el-date-picker
            style="width:192px"
            v-model="deliveryQueryData.khdat"
            type="date"
            align="right"
            unlink-panels
            value-format="yyyy-MM-dd"
            placeholder="请输入发货日期"
          />
        </el-form-item>
        <el-form-item label="交货日期" prop="lfdat">
          <el-date-picker
            style="width:192px"
            v-model="deliveryQueryData.lfdat"
            type="date"
            align="right"
            unlink-panels
            value-format="yyyy-MM-dd"
            placeholder="请输入交货日期"
          />
        </el-form-item>
        <el-form-item label="发票提交状态" prop="invoiceStatus">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.invoiceStatus"
            placeholder="请选择发票提交状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in dictList['commonStatus']"
              :key="'invoiceStatus'+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="过账状态" prop="wbstk">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.wbstk"
            placeholder="请选择过账状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in dictList['commonStatus']"
              :key="'wbstk'+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="库管类别" prop="positionOwner">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.positionOwner"
            placeholder="请选择库管类别"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in dictList['positionOwner']"
              :key="'positionOwner'+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="EVM运营" prop="evmOperator">
          <el-input style="width:192px" v-model="deliveryQueryData.evmOperator" placeholder="EVM运营" clearable></el-input>
        </el-form-item>
        <el-form-item label="业务类型" prop="businessType">
          <el-select
            style="width:192px"
            v-model="deliveryQueryData.businessType"
            placeholder="请选择业务类型"
            clearable
          >
            <el-option
              v-for="item in dictList['dnBusinessType']"
              :key="item.parentCode+item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
      </div>
      <el-row type="flex" justify="center">
        <el-form-item>
          <el-button class="dn-normal-bn" type="primary" @click="handleSubmit">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button class="dn-normal-bn" @click="handleReset">重置</el-button>
        </el-form-item>
      </el-row>
      <div class="notice">友情提醒：销售订单自动转DN交货的创建时间点为整点，创建后会立即下发坤合</div>
    </el-form>
    <div style="margin-bottom: 20px" v-show="false">
      <el-button @click="handleRefresh" type="primary">刷新</el-button>
      <el-button @click="handleExport">导出</el-button>
      <el-button @click="handleBatchChangeReason">批量修改未开票原因</el-button>
    </div>
    <el-table
      border
      fit
      highlight-current-row
      v-loading="isLoading"
      :data="deliveryListData"
      style="width: 100%">
      <el-table-column
        align="center"
        type="selection"
        width="40">
      </el-table-column>
      <el-table-column
        align="center"
        type="index"
        prop="rowNo"
        label="序号">
      </el-table-column>
      <el-table-column
        align="center"
        prop="deliveryNo"
        label="OMS交货单号"
        width="180">
          <template slot-scope="{row}">
            <MultiLineTooltip
              :title="row.deliveryNo"
              :lineList="getDeliveryNoTooltip(row)"
              :click="handleRouter"
              :copy="handleCopy"
            />
          </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="wbstk"
        label="交货单状态">
        <template slot-scope="{row}">
          <span>{{row.wbstk}}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="businessDesc"
        label="业务类型" width="120">
      </el-table-column>
      <el-table-column
        align="center"
        prop="lfart"
        label="交货单类型" width="150">
      </el-table-column>
      <el-table-column
        align="center"
        prop="referOrderNo"
        label="OMS销售订单号"
        width="180">
        <template slot-scope="{row}">
          <MultiLineTooltip
            :title="row.referOrderNo"
            :lineList="getOrderNoTooltip(row)"
            :copy="handleCopy"
            :click="handleRouter"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column
        align="center"
        prop="history"
        label="操作历史">
        <template slot-scope="{ row }">
          <el-button type="text" size="mini">操作历史</el-button>
        </template>
      </el-table-column> -->
      <el-table-column
        align="center"
        prop="sapSyncStatusMsg"
        label="SAP同步状态" width="150">
      </el-table-column>
      <el-table-column
        align="center"
        prop="exceptionMessage"
        label="坤合同步状态" width="150">
      </el-table-column>
      <el-table-column align="center" label="交货冻结原因" prop="deliveryFreezeDesc" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="包装信息" prop="supplierBatch">
        <template slot-scope="{ row }">
          <el-button @click="handleClickPackage(row)" type="primary" size="mini" round>查看</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" label="调度信息" prop="supplierBatch">
        <template slot-scope="{ row }">
          <el-button @click="handleClickTrans(row)" type="primary" size="mini" round>查看</el-button>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="umwrk"
        label="工厂" width="230">
      </el-table-column>
      <el-table-column
        align="center"
        prop="lgort"
        label="库存点" width="110">
      </el-table-column>
      <el-table-column
        align="center"
        prop="zkunnr"
        label="客户" width="230">
      </el-table-column>
      <el-table-column
        align="center"
        prop="zcont"
        label="客服" width="100">
      </el-table-column>
      <el-table-column
        align="center"
        prop="lfdat"
        label="计划发货" width="180">
      </el-table-column>
      <el-table-column
        align="center"
        prop="gmtCreate"
        label="创建时间" width="180">
      </el-table-column>
      <el-table-column
        align="center"
        fixed="right"
        prop="operation"
        width="140"
        label="操作">
        <template slot-scope="{ row }">
          <el-button type="text" size="mini" @click="toDtl(row)">查看</el-button>
          <el-button type="text" size="mini" @click="toTaskList(row)">去处理</el-button> <br />
          <el-popover placement="top" width="120" trigger="click" >
            <div class="content" style="display: flex;flex-direction: column;">
            <el-button type="primary" plain size="mini" @click="showUploadDialog(row,'deliveryNote')">交货单附件</el-button> <br />
            <el-button type="primary" plain size="mini" @click="showUploadDialog(row,'other')">其他随货资料</el-button> <br />
            <el-button type="primary" plain size="mini" @click="showUploadDialog(row,'label')">标签附件</el-button>
            </div>
            <el-button type="primary" plain size="mini"  slot="reference">
              上传文档
              <i class="el-icon-upload el-icon--right" />
            </el-button>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <pagination
        v-show="deliveryListDataTotal > 0"
        :total="deliveryListDataTotal"
        :page.sync="deliveryPaginationQuery.page"
        :limit.sync="deliveryPaginationQuery.size"
        layout="total, prev, pager, next, jumper"
        @pagination="queryDNOrder"
      />
    </div>
    <el-dialog title="上传附件" :visible.sync="uploadDialogStatus" :before-close="handleUploadClose" :show-close="false">
      <div v-loading="uploadLoading" class="dialog-body" style="display:flex;align-items: center;flex-direction:column">
        <span style="margin:10px;color:#597bee">
          {{'可将文件直接拖拽到改区域、或者点击上传按钮，仅支持PDF' + (uploadType === 'label' ? '/EXCEL' : '') + '附件哟'}}
        </span>
        <el-upload ref="uploadDialog" action="/ali-upload"
          style="display: inline-block;" drag
          :show-file-list="true" multiple
          :with-credentials="true" :limit="5"
          :data="{appName: omsAppName}"
          :on-success="handleUploadSucess"
          :on-remove="handleUploadRemove"
          :on-error="handleUploadError"
          :before-upload="handleBeforeUpload"
          :on-exceed="handleUploadExceed"
          :accept="uploadType === 'label' ? '.xls, .xlsx, .xlsm, .csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/pdf ,.pdf': 'application/pdf,.pdf'"
          >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text"><em>点击或拖拽上传</em></div>
        </el-upload>
        <div style="display:flex; align-items: center;margin: 10px;">
          <span>打印方向</span>
          <el-select v-model="printDirection" style="width: 120px;margin-left: 10px;">
            <el-option value="cross" label="横向"></el-option>
            <el-option value="vertical" label="纵向"></el-option>
          </el-select>
        </div>
        <span slot="footer" class="dialog-footer" style="align-self:flex-end;margin-top:20px;">
          <el-button @click="cancelUpload">取消</el-button>
          <el-button @click="submitUpload" type="primary">提交</el-button>
        </span>
      </div>
    </el-dialog>
    <TransDetailDialog :dialogVisible.sync="showTransDlgVisible" :tableData="transData" />
    <PackagesDialog :dialogVisible.sync="showPakageDlgVisible" :tableData="packagesData" />
  </div>
</template>

<script>
import moment from 'moment'
import * as shortid from 'shortid'
import clip from '@/utils/clipboard'
import MultiLineTooltip from '@/components/MultiLineTooltip'
import Pagination from '@/components/Pagination'
import { listDN, searchClients, getTransportInfo } from '@/api/orderDelivery'
import { deliveryState } from './constants'
import { spDebounce } from '@/utils/index.js'
import { saveOssFileInfo } from '@/api/ecorp'
import TransDetailDialog from './components/list/TransDetailDialog.vue'
import PackagesDialog from './components/list/PackagesDialog.vue'

export default {
  name: 'OrderDelivery',
  components: {
    Pagination,
    MultiLineTooltip,
    TransDetailDialog,
    PackagesDialog
  },
  data () {
    return {
      omsAppName: window.omsAppName,
      uploadType: '',
      printDirection: 'cross',
      uploadLoading: false,
      uploadDialogStatus: false,
      uploadListRows: [],
      uploadList: [],
      deliveryOptions: [
        { value: 'SAP', label: 'SAP交货单号' },
        { value: 'OMS', label: 'OMS交货单号' }
      ],
      orderOptions: [
        { value: 'OMS', label: 'OMS订单号' },
        { value: 'SAP', label: 'SAP订单号' },
        { value: 'CUS', label: '客户订单号' },
        { value: 'OUT', label: '外围订单号' }
      ],
      deliveryQueryData: this.getDefaultQueryParams(),
      showMore: false,
      isLoading: false,
      deliveryListData: [],
      deliveryListDataTotal: 0,
      deliveryPaginationQuery: {
        page: 1,
        size: 50
      },
      customerList: [],
      loadingCustomer: false,
      showTransDlgVisible: false,
      transData: [], // 调度信息
      showPakageDlgVisible: false,
      packagesData: [] // 包装信息
    }
  },
  created () {
    // this.queryDNOrder()
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    this.throwCheckMsg = spDebounce(this.throwCheckMsg)
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    deliveryStateList () {
      return Object.values(deliveryState)
    },
    dnOrderTypeOptions () {
      const list = (this.dictList['dnOrderType'] || []).reduce((pre, cur) => {
        const found = pre.find(item => item.code === cur.code)
        if (!found) {
          pre.push(cur)
        }
        return pre;
      }, [])
      return list
    }
  },
  methods: {
    saveFileInfo (deliveryNo, docMetaDataList = []) {
      const queryData = {
        source: 'BOSS',
        dimension: 'dn',
        businessId: deliveryNo,
        docMetaDataList
      }
      return saveOssFileInfo(queryData)
    },
    throwCheckMsg (callback) {
      callback && callback()
    },
    handleBeforeUpload (file) {
      const size = file.size / 1024 / 1024
      const isGtLimit = size > this.fileSizeLimit
      const isPDF = /pdf/i.test(file.name)
      let pass = true
      if (!this.$validateFileType(file)) return false

      if (isGtLimit) {
        pass = false
        this.returnMsg += `【${file.name}】大小：${size}M，上传文件不能超过` + this.fileSizeLimit + 'MB！<br/>'
      }
      if (this.uploadType === 'order' && !isPDF) {
        pass = false
        this.returnMsg += `【${file.name}】不是pdf文件类型，只能上传PDF文件！<br/>`
      }
      if (!pass) {
        this.throwCheckMsg(() => {
          this.$message({
            type: 'error',
            dangerouslyUseHTMLString: true,
            message: this.returnMsg
          })
          this.returnMsg = ''
        })
      }
      return pass
    },
    async submitUpload () {
      if (this.uploadList.filter(file => file.status !== 'success').length) {
        return this.$message.error('请等待文件上传完成！')
      }
      const uploadListRows = this.uploadListRows
      const uploadList = this.uploadList.map(upload => ({
        bucketName: upload.bucketName,
        fileName: upload.name,
        ossKey: upload.ossKey,
        printDirection: this.printDirection,
        upUserName: window.CUR_DATA.user && window.CUR_DATA.user.name,
        attachmentType: this.uploadType ? this.uploadType : 'deliveryNote',
        uploadTime: moment(new Date()).format('yyyy-MM-DD HH:mm:ss')
      }))
      if (!uploadList.length) return this.$message.error('请上传文件！')
      this.uploadLoading = true
      try {
        const res = await this.saveFileInfo(uploadListRows[0].deliveryNo, uploadList)
        if (res && res.code === 200) {
          this.$message.success(res.msg || '操作成功！')
        } else {
          this.$message.error(res.msg || '操作失败！')
        }
      } catch (err) {
        this.$message.error(err.msg || '操作失败！')
      } finally {
        this.uploadLoading = false
        this.resetUploadData()
      }
    },
    handleUploadSucess (res, file, fileList) {
      this.uploadList = fileList.map(file => ({
        ...file,
        fileName: file.response && file.response[0] && file.response[0].name,
        ossKey: file.response && file.response[0] && file.response[0].objectKey,
        bucketName: file.response && file.response[0] && file.response[0].bucketName
      }))
    },
    handleUploadRemove (file, fileList) {
      this.uploadList = fileList.map(file => ({
        ...file,
        fileName: file.response && file.response[0] && file.response[0].name,
        ossKey: file.response && file.response[0] && file.response[0].objectKey,
        bucketName: file.response && file.response[0] && file.response[0].bucketName
      }))
    },
    handleUploadError (error) {
      this.$message.error(error.error || error.message || '上传失败！')
    },
    handleUploadExceed (error) {
      this.$message.error(error.error || error.message || '文件最多上传5个！')
    },
    resetUploadData () {
      this.uploadDialogStatus = false
      this.uploadListRows = []
      this.uploadList = []
      this.$refs.uploadDialog && this.$refs.uploadDialog.clearFiles()
    },
    cancelUpload () {
      this.resetUploadData()
    },
    handleUploadClose (done) {
      this.cancelUpload()
      done && done()
    },
    showUploadDialog (row, type) {
      this.uploadType = type || this.uploadType
      this.uploadListRows = Array.isArray(row) ? row : [row]
      this.uploadDialogStatus = true
    },
    handleRouter (type, no, row) {
      const soNo = type === 'oms' ? no : ''
      const sapOrderNo = type === 'sap' ? no : ''
      this.$router.jumpToSoOrderDetail({
        query: {
          soNo: soNo || row?.referOrderNo,
          sapOrderNo: sapOrderNo || row?.vgbel,
          id: shortid.generate(),
          refresh: true
        }
      })
    },
    getOrderNoTooltip (item) {
      return item ? [
        { title: 'OMS订单号', children: item.referOrderNo ? item.referOrderNo.split(',') : [''], type: 'oms', row: item },
        { title: 'SAP订单号', children: item.vgbel ? item.vgbel.split(',') : [''], type: 'sap', row: item },
        { title: '客户订单号', children: item.customerReferenceNo ? item.customerReferenceNo.split(',') : [''] },
        { title: '外围订单号', children: item.peripheryReferenceNo ? item.peripheryReferenceNo.split(',') : [''] }
      ] : []
    },
    getDeliveryNoTooltip (item) {
      return item ? [
        { title: 'OMS交货单号', children: item.deliveryNo ? item.deliveryNo.split(',') : [''] },
        { title: 'SAP交货单号', children: item.sapDeliveryNo ? item.sapDeliveryNo.split(',') : [''] }
      ] : []
    },
    getStatus (status) {
      if (status) {
        const s = status.trim().toUpperCase()
        if (s && (s === 'ALL' || s === 'EXCEPTION')) {
          return ''
        }
        return s
      }
      return ''
    },
    queryCustomerList (str) {
      this.loadingCustomer = true
      searchClients(str).then(res => {
        this.loadingCustomer = false
        if (res && res.code === 200) {
          this.customerList = [
            {
              customerNumber: '客户编码',
              customerName: '客户名称',
              cityName: '城市'
            },
            ...res.data
          ]
        }
      })
    },
    getOrderNo () {
      const result = {}
      const { orderType } = this.deliveryQueryData
      if (orderType === 'OMS') {
        result.referOrderNo = this.deliveryQueryData.orderNo
      } else if (orderType === 'SAP') {
        result.vgbel = this.deliveryQueryData.orderNo
      } else if (orderType === 'CUS') {
        result.customerReferenceNo = this.deliveryQueryData.orderNo
      } else if (orderType === 'OUT') {
        result.peripheryReferenceNo = this.deliveryQueryData.orderNo
      }
      return result
    },
    getDeliveryNo () {
      const result = {}
      const { deliveryType } = this.deliveryQueryData
      if (deliveryType === 'OMS') {
        result.sapDeliveryNo = ''
        result.deliveryNo = this.deliveryQueryData.deliveryNo
      } else if (deliveryType === 'SAP') {
        result.deliveryNo = ''
        result.sapDeliveryNo = this.deliveryQueryData.deliveryNo
      }
      return result
    },
    queryDNOrder () {
      const orderNoObj = this.getOrderNo()
      const deliveryNoObj = this.getDeliveryNo()
      const { createDate, status } = this.deliveryQueryData
      const param = {
        ...this.deliveryQueryData,
        ...orderNoObj,
        ...deliveryNoObj,
        exceptionFlag: status && status.trim() === 'EXCEPTION',
        status: this.getStatus(status),
        current: this.deliveryPaginationQuery.page,
        size: this.deliveryPaginationQuery.size
      }
      if (createDate && createDate.length === 2) {
        const m1 = moment(createDate[1])
        const m2 = moment(createDate[0])
        const d = m1.diff(m2, 'months', true)
        if (d > 2) {
          this.$alert('只允许查询两个月内的DN订单！', '错误', {
            type: 'error'
          })
          return
        }
        param.createStartDate = createDate[0]
        param.createEndDate = createDate[1]
      }
      delete param.createDate
      delete param.deliveryType
      delete param.orderNo
      delete param.orderType
      this.clean(param)
      this.isLoading = true
      listDN(param).then(res => {
        this.isLoading = false
        if (res && res.code === 200) {
          this.deliveryListDataTotal = res.data.total
          this.deliveryListData = res.data.records
        } else {
          this.$message.error(res.msg || '查询失败')
        }
      })
    },
    clean (obj) {
      for (var propName in obj) {
        if (!obj[propName]) {
          delete obj[propName]
        }
      }
    },
    handleReset () {
      // this.$refs['deliveryQueryForm'].resetFields()
      this.deliveryQueryData = this.getDefaultQueryParams()
      this.queryDNOrder()
    },
    handleSubmit () {
      this.queryDNOrder()
    },
    handleMore () {
      this.showMore = !this.showMore
    },
    toDtl (row) {
      this.$router.push({
        path: `/orderDelivery/detail/${row.id}?no=${row.vgbel}`
      })
    },
    toTaskList (row) {
      console.log(row)
      window.open(`/wb/so-task/list?relatedNoType=delivery_no&relatedNo=${row.deliveryNo}`)
    },
    getDefaultQueryParams () {
      const startDate = moment().subtract(3, 'days').format('yyyy-MM-DD')
      const endDate = moment().format('yyyy-MM-DD')
      return {
        orderType: 'OMS',
        deliveryType: 'OMS',
        status: '',
        createDate: [startDate, endDate]
      }
    },
    handleRefresh () {

    },
    handleExport () {

    },
    handleBatchChangeReason () {
    },
    handleCopy (no) {
      clip(no, event, () => {
        const content = '复制成功'
        this.$message({
          message: content,
          type: 'success'
        })
      })
    },
    handleClickPackage (row) {
      getTransportInfo(row.deliveryNo).then(res => {
        this.showPakageDlgVisible = true;
        if (res.code === '0000') {
          this.packagesData = res.result?.tskInfos?.find(item => item.pri === item.maxPri)?.packageInfos;
        } else {
          this.packagesData = []
        }
      })
    },
    handleClickTrans (row) {
      getTransportInfo(row.deliveryNo).then(res => {
        this.showTransDlgVisible = true;
        if (res.code === '0000') {
          this.transData = res.result?.tskInfos?.filter(item => item.pri === item.maxPri)
        } else {
          this.transData = []
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./styles/common.scss";

.delivery-form-inline {
  font-size: 12px;
}
.delivery-container {
  padding: 0 20px;
  .notice {
    line-height: 16px;
    font-size: 12px;
    margin-bottom: 5px;
  }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}
.ba-row-start{
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.selectClientItem {
  div:nth-child(1) {
    width: 80px;
  }
  div:nth-child(2) {
    width: 80px;
  }
  div:nth-child(3) {
    width: 300px;
  }
}
</style>

<style lang="scss">
.delivery-container {
  .delivery-select {
    .el-input {
      input {
        border: 0;
        text-align: right;
      }
    }
  }
}
</style>
