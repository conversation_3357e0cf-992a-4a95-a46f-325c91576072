<template>
  <div class="dn-edit-container">
    <div class="dn-edit-header">
      <span class="dn-title">编辑编号{{id}}交货单—{{dnData.statusDesc}}</span>
      <div>
        <el-button class="dn-normal-bn" type="primary" @click="handleEdit">保存</el-button>
      </div>
    </div>
    <div class="dn-row-title">基础信息</div>
    <BaseInfo
      ref="baseInfo"
      :data="dnData.basicInfo||{}"
    />
    <div class="dn-row-title">交货信息<RowMore @fold="handleDeliveryFold" /></div>
    <Delivery
      ref="deliveryInfo"
      :data="dnData.deliveryInfo||{}"
      :customerNo="customerNo"
      :contact="contact"
      :disabledContact="dnData.status!=='NEW'"
      :fields="deliveryFields"
      :fold="foldDelivery"
      @submit="submitDelivery"
    />
    <div class="dn-row-title">财务信息<RowMore @fold="handleFinanceFold" /></div>
    <Finance
      ref="financeInfo"
      :data="dnData.financeInfo"
      :fields="financeFields"
      :fold="foldFinance"
      @submit="submitFinance"
    />
    <div class="dn-row-title">行项目明细</div>
    <div class="dn-row-btn-list">
      <div>
        <el-button
          class="dn-mini-bn"
          size="mini"
          type="primary"
          @click="addDlg=true"
          v-if="dnData.status==='NEW'"
        >
          新增
        </el-button>
      </div>
    </div>
    <DetailTable
      :data="items.filter(d => d.operationType !== 3)"
      :isLoading="isLoading"
      :status="dnData.status"
      @selectionChange="handleSelectionChange"
      :type="'edit'"
    />
    <SelectDNPlan
      :show-dialog.sync="showSelectDNPlan"
      :show-add-so-no="true"
      :so-no-info="[]"
      :exclude="items.filter(d => d.operationType !== 3)"
      @submit="handlePlanSubmit"
    />
    <el-dialog
      title="添加其他订单"
      width="600px"
      :visible.sync="addDlg"
      :destroy-on-close="true"
    >
      <AttachOrder :data="items" @submit="handleAdd" @cancel="addDlg=false" />
    </el-dialog>
  </div>
</template>

<script>
import {
  getDN, getContactDetail, getDNNoteDetail,
  updateDN, queryDnBySoPlan
} from '@/api/orderDelivery'
import SelectDNPlan from '@/components/order/SelectDNPlan'
import BaseInfo from './components/common/BaseInfo'
import Delivery from './components/common/Delivery'
import Finance from './components/common/Finance'
import DetailTable from './components/common/DetailTable'
import RowMore from './components/common/RowMore'
import AttachOrder from './components/common/AttachOrder'
import { getDeliveryFields, getFinanceFields } from './utils'

export default {
  name: 'OrderDeliveryEdit',
  components: {
    BaseInfo, Delivery, Finance, DetailTable, SelectDNPlan, RowMore, AttachOrder
  },
  data () {
    const { id } = this.$route.params
    return {
      id,
      dnData: {},
      items: [],
      contact: {},
      isLoading: false,
      deliveryFields: [],
      financeFields: [],
      selectedItemList: [],
      submitFlag: 0,
      foldDelivery: true,
      foldFinance: true,
      showSelectDNPlan: false,
      addDlg: false
    }
  },
  created () {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList').then(() => {
        this.init()
      })
    } else {
      this.init()
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    customerNo () {
      return this.dnData.basicInfo ? this.dnData.basicInfo.customerNo : ''
    }
  },
  methods: {
    init () {
      const { id } = this.$route.params
      if (id) {
        this.isLoading = true
        const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
        Promise.all([getDN(id), getDNNoteDetail({
          id,
          size: 1000,
          current: 1
        })]).then(([res, res1]) => {
          this.isLoading = false
          loading.close()
          if (res && res.code === 200) {
            const {
              basicInfo: { distributionChannel, productGroup, salesOrganization },
              deliveryInfo: {
                contactId,
                receiptTimeCategory,
                attachCoa,
                attachMsds,
                attachOrder,
                attachTds,
                exportProcessingZone,
                referenceStandardShippingReq,
                specifiedDocument,
                serviceCenterSelfTransport,
                shipmentFreeze
              },
              statusDesc,
              positionOwner,
              systemStatus
            } = res.data
            res.data.basicInfo = {
              statusDesc,
              positionOwner,
              systemStatus,
              ...res.data.basicInfo
            }
            res.data.deliveryInfo = {
              distributionChannel,
              productGroup,
              salesOrganization,
              ...res.data.deliveryInfo,
              serviceCenterSelfTransport: serviceCenterSelfTransport === '001',
              receiptTimeCategory: receiptTimeCategory === 'X',
              attachCoa: attachCoa === 'X',
              attachMsds: attachMsds === 'X',
              attachOrder: attachOrder === 'X',
              attachTds: attachTds === 'X',
              exportProcessingZone: exportProcessingZone === 'X',
              referenceStandardShippingReq: referenceStandardShippingReq === 'X',
              specifiedDocument: specifiedDocument === 'X',
              shipmentFreeze: shipmentFreeze === 'C' ? '' : shipmentFreeze
            }
            this.dnData = res.data
            this.deliveryFields = getDeliveryFields(`edit_${res.data.status}`)
            this.financeFields = getFinanceFields(`edit_${res.data.status}`)
            if (contactId) {
              getContactDetail(contactId).then(res1 => {
                if (res1 && res1.code === 200) {
                  this.contact = res1.data
                }
              })
            }
          } else if (res && res.msg) {
            this.$alert(res.msg, '错误')
          }
          if (res1 && res1.code === 200) {
            this.items = res1.data.records
          } else if (res1 && res1.msg) {
            this.$alert(res1.msg, '错误')
          }
        })
      }
    },
    handlePlanSubmit (val) {
      if (val && val.length > 0) {
        const params = val.map(v => {
          const { referOrderItemDetailNo, referOrderItemNo, referOrderNo } = v
          return { referOrderItemDetailNo, referOrderItemNo, referOrderNo }
        })
        queryDnBySoPlan(params).then(res => {
          if (res && res.code === 200 && res.data && res.data.length > 0) {
            res.data.forEach(d => {
              this.items.push({
                ...d,
                operationType: 1
              })
            })
          }
        })
      }
    },
    submitFinance (data) {
      this.dnData.financeInfo = data
      this.submitFlag = this.submitFlag + 1
      if (this.submitFlag === 2) {
        this.handleSubmit()
      }
    },
    submitDelivery (data) {
      this.dnData.deliveryInfo = data
      this.submitFlag = this.submitFlag + 1
      if (this.submitFlag === 2) {
        this.handleSubmit()
      }
    },
    handleSelectionChange (val) {
      this.selectedItemList = val
    },
    handleEdit () {
      this.submitFlag = 0
      this.$refs.deliveryInfo.$refs['deliveryBaseForm'].validate((valid1, items) => {
        if (valid1) {
          this.$refs.deliveryInfo.submit('deliveryBaseForm')
        } else {
          return false
        }
      })
      this.$refs.financeInfo.$refs['financeBaseForm'].validate(valid2 => {
        if (valid2) {
          this.$refs.financeInfo.submit('financeBaseForm')
        } else {
          return false
        }
      })
    },
    handleSubmit () {
      const {
        deliveryInfo: {
          contactId, deliveryRequireTime,
          deliverySlipTemplate, demandUser,
          deliveryUnloadingReq, packagingReq,
          signingBack, deliveryTime,
          receiptTimeCategory,
          attachCoa,
          attachMsds,
          attachOrder,
          attachTds,
          exportProcessingZone,
          referenceStandardShippingReq,
          specifiedDocument,
          serviceCenterSelfTransport,
          disableShipping, deliveryNote,
          shipmentFreeze,
          orderNote
        },
        financeInfo: {
          invoicingByMail, invoiceType, unbillReason, unbillReasonDesc, shippingInfo, financeRedReason, financialNote
        },
        status
      } = this.dnData
      const batchInfos = []
      if (this.items && this.items.length > 0) {
        this.items.forEach(bi => {
          if (bi && bi.batchInfos) {
            bi.batchInfos.forEach(b => {
              const bd = {
                ...b,
                skuBatch: b.wmsBatch,
                lfimg: b.deliveryAmount
              }
              delete bd.signStatus
              delete bd.wmsBatch
              delete bd.deliveryAmount
              batchInfos.push(bd)
            })
          }
          if (bi.operationType == null) {
            bi.operationType = 2
          }
        })
      }
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      updateDN({
        status,
        deliveryNoteNo: this.id,
        deliveryInfo: {
          contactId,
          demandUser,
          deliveryRequireTime,
          deliveryTime,
          deliverySlipTemplate,
          deliveryUnloadingReq,
          packagingReq,
          signingBack,
          receiptTimeCategory: receiptTimeCategory ? 'X' : 'Z',
          attachCoa: attachCoa ? 'X' : 'Z',
          attachMsds: attachMsds ? 'X' : 'Z',
          attachOrder: attachOrder ? 'X' : 'Z',
          attachTds: attachTds ? 'X' : 'Z',
          exportProcessingZone: exportProcessingZone ? 'X' : 'Z',
          referenceStandardShippingReq: referenceStandardShippingReq ? 'X' : 'Z',
          specifiedDocument: specifiedDocument ? 'X' : 'Z',
          serviceCenterSelfTransport: serviceCenterSelfTransport ? '001' : '002',
          disableShipping,
          deliveryNote,
          shipmentFreeze,
          orderNote
        },
        financeInfo: {
          invoiceType,
          invoicingByMail: invoicingByMail ? 'X' : 'Z',
          unbillReason,
          unbillReasonDesc,
          shippingInfo,
          financeRedReason,
          financialNote
        },
        batchInfos,
        items: this.items
      }).then(res => {
        loading.close()
        if (res && res.code !== 200 && res.msg) {
          this.$alert(res.msg, '错误', {
            type: 'error'
          })
        }
        if (res && res.code === 200) {
          this.$message({
            message: '修改成功',
            type: 'success',
            onClose: () => {
              this.$closeTag(this.$route.path)
              this.$closeTag(`/orderDelivery/detail/${this.id}`)
              this.$router.push('/orderDelivery/list')
            }
          })
        }
      }).catch(error => {
        loading.close()
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误')
          }
        }
      })
    },
    handleAdd (data) {
      this.addDlg = false
      data && data.forEach(g => {
        this.items.push(g)
      })
    },
    handleDeliveryFold (val) {
      this.foldDelivery = val
    },
    handleFinanceFold (val) {
      this.foldFinance = val
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./styles/common.scss";
.dn-edit-container {
  padding: 15px;
}

.dn-edit-header {
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: space-between;
  .dn-title {
    font-size: 18px;
    font-weight: bold;
  }
}
.dn-row-title {
  background-color: #F2F6FC;
  line-height: 3;
  padding-left: 10px;
  margin: 10px 0;
}
.dn-row-btn-list {
  display: flex;
  margin: 10px 0;
  align-items: center;
  justify-content: space-between;
}
</style>
