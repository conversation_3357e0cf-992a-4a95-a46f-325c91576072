<template>
  <div class="dn-edit-container" :key="key">
    <div class="dn-edit-header">
      <div>
        <span class="dn-title" v-if="editKey">编号{{id}}交货单明细-修改交货信息</span>
        <span class="dn-title" v-else>编号{{id}}交货单明细{{ dnData.statusDesc ? `-${dnData.statusDesc}` : ''}}</span>
      </div>
      <div>
        <template v-if="editKey">
          <el-button @click="cancelEdit">
            取消
          </el-button>
          <el-button type="primary" @click="updateEdit">
            确认保存
          </el-button>
        </template>
        <span v-else>
          <el-button v-if="showChangeDeliveryButton" type="primary" @click="changeEditKey('changeDeliveryInfo')">修改交货信息</el-button>
          <el-button v-if="showChangeContactButton" type="primary" @click="changeEditKey('changeContactInfo')">修改交货单</el-button>
          <el-button class="dn-normal-bn" type="primary" @click="refresh" >
            刷新
          </el-button>
          <el-button
            class="dn-normal-bn"
            type="warning"
            @click="cancelVisible=true"
            v-if="fsm&&fsm.canGoTo('CANCEL')&&!isZRE3&&not402"
          >
            取消交货单
          </el-button>
          <el-button
            class="dn-normal-bn"
            type="primary"
            @click="handleTransferAccount"
            :disabled="isFreeze"
            v-if="isSupportedAutoAccount&&!isZRE3"
          >
            一键过账
          </el-button>
          <el-button
            class="dn-normal-bn"
            type="primary"
            @click="showUploadDialog"
            v-if="dnData.showUploadSign"
          >
            上传签单
          </el-button>
          <el-button
            class="dn-normal-bn"
            type="primary"
            @click="handleSubmit"
            :disabled="isFreeze || dnData.systemStatusCode==='10' || !hasDeliveryFreeze"
            v-if="dnData.status==='NEW'&&!isZRE3"
          >
            提交
          </el-button>
        </span>
      </div>
    </div>
    <div class="dn-row-title">
      基础信息
    </div>
    <BaseInfo
      :data="dnData.basicInfo||{}"
      :sapBtnFlg="dnData.sourceSystem"
      :type="'detail'"
      @afterAsync="handleAfterAsync"
    />
    <div class="dn-row-title">
      订单信息
      <RowMore @fold="(v) => handleFold('foldOrderInfo', v)" :fold="!foldOrderInfo" />
    </div>
    <Delivery
      :fields="OrderDeliveryDetailOrderInfoFields"
      :basicAndDeliveryInfo="basicAndDeliveryInfo"
      :data="{...dnData.basicInfo, ...dnData.deliveryInfo}||{}"
      :dnData="dnData||{}"
      :fold="!foldOrderInfo"
      :id="id"
      :editKey="editKey"
      :attachOrder="attachOrder"
      :referenceStandardShippingReq="referenceStandardShippingReq"
      :specifiedDocument="specifiedDocument"
      :attachCoa="attachCoa"
      :attachMsds="attachMsds"
      :attachTds="attachTds"
      :certificateIdentification="certificateIdentification"
      :deliverySlipTemplate="deliverySlipTemplate"
      :labelTemplate="labelTemplate"
      @freezeShipment="freezeShipment"
      @freezeDelivery="freezeDelivery"
    />
    <div class="dn-row-title">
      仓储信息-随货文件要求
      <RowMore @fold="(v) => handleFold('foldCargoFile', v)" :fold="foldCargoFile" />
    </div>
    <Delivery
      :fields="OrderDeliveryDetailCargoFileFields"
      :basicAndDeliveryInfo="basicAndDeliveryInfo"
      :data="{...dnData.basicInfo, ...dnData.deliveryInfo}||{}"
      :dnData="dnData||{}"
      :fold="foldCargoFile"
      :id="id"
      :editKey="editKey"
      :attachOrder="attachOrder"
      :referenceStandardShippingReq="referenceStandardShippingReq"
      :specifiedDocument="specifiedDocument"
      :attachCoa="attachCoa"
      :attachMsds="attachMsds"
      :attachTds="attachTds"
      :certificateIdentification="certificateIdentification"
      :deliverySlipTemplate="deliverySlipTemplate"
      :labelTemplate="labelTemplate"
      @freezeShipment="freezeShipment"
      @freezeDelivery="freezeDelivery"
    />
    <div class="dn-row-title">
      仓储信息-其他要求
      <RowMore @fold="(v) => handleFold('foldOtherReq', v)" :fold="foldOtherReq" />
    </div>
    <Delivery
      :fields="OrderDeliveryDetailOtherReqFields"
      :basicAndDeliveryInfo="basicAndDeliveryInfo"
      :data="{...dnData.basicInfo, ...dnData.deliveryInfo}||{}"
      :dnData="dnData||{}"
      :fold="foldOtherReq"
      :id="id"
      :editKey="editKey"
      :attachOrder="attachOrder"
      :referenceStandardShippingReq="referenceStandardShippingReq"
      :specifiedDocument="specifiedDocument"
      :attachCoa="attachCoa"
      :attachMsds="attachMsds"
      :attachTds="attachTds"
      :certificateIdentification="certificateIdentification"
      :deliverySlipTemplate="deliverySlipTemplate"
      :labelTemplate="labelTemplate"
      @freezeShipment="freezeShipment"
      @freezeDelivery="freezeDelivery"
    />
    <div class="dn-row-title">
      运输信息
      <RowMore @fold="(v) => handleFold('foldTransport', v)" :fold="foldTransport" />
    </div>
    <Delivery
      :fields="OrderDeliveryDetailTransportFields"
      :basicAndDeliveryInfo="basicAndDeliveryInfo"
      :data="{...dnData.basicInfo, ...dnData.deliveryInfo}||{}"
      :dnData="dnData||{}"
      :fold="foldTransport"
      :id="id"
      :editKey="editKey"
      :attachOrder="attachOrder"
      :referenceStandardShippingReq="referenceStandardShippingReq"
      :specifiedDocument="specifiedDocument"
      :attachCoa="attachCoa"
      :attachMsds="attachMsds"
      :attachTds="attachTds"
      :certificateIdentification="certificateIdentification"
      :deliverySlipTemplate="deliverySlipTemplate"
      :labelTemplate="labelTemplate"
      @freezeShipment="freezeShipment"
      @freezeDelivery="freezeDelivery"
    />
    <div class="dn-row-title">
      财务信息
      <RowMore @fold="handleFinanceFold" />
    </div>
    <Finance
      :data="dnData.financeInfo||{}"
      :fold="foldFinance"
      :id="id"
      @freeze="freezeFinance"
      @updateUnbillReason="updateUnbillReason"
    />
    <DividerHeader>行项目明细</DividerHeader>
    <DetailTable
      :data="deliveryNoteListData"
      :isLoading="isLoading"
      :disabled="disabled"
      :status="dnData.status"
      :type="'detail'"
    />
    <div class="pagination-container" v-show="false">
      <pagination
        v-show="deliveryNoteListDataTotal > 0"
        :total="deliveryNoteListDataTotal"
        :page.sync="deliveryPaginationQuery.page"
        :limit.sync="deliveryPaginationQuery.size"
        layout="total, prev, pager, next, jumper"
        @pagination="queryDNNoteOrder"
      />
    </div>
    <el-dialog
      title="取消交货单"
      :visible.sync="cancelVisible"
      width="600px"
      :destroy-on-close="true"
    >
      <el-form ref="cancelForm" :model="cancelData" label-width="120px" :rules="rules">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-select v-model="cancelData.cancelReason" placeholder="请选择取消原因" style="width:100%">
            <el-option
              v-for="item in dictList['cancelReason']"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="取消原因备注">
          <el-input type="textarea" :rows="3" maxlength="20"
            v-model="cancelData.cancelReasonDesc"
            show-word-limit></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleCancelOrder">确定</el-button>
          <el-button @click="cancelVisible=false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog
      title="确认商品的批次信息"
      width="900px"
      :visible.sync="batchVisible"
      :destroy-on-close="true"
    >
      <Batch
        :batchData="batchData"
        :listData="deliveryNoteListData"
        @cancel="handleBatchCancel"
        @success="handleBatchSuccess"
      />
    </el-dialog>
    <AttachmentUpload :deliveryNo="$route.params.id" :visible="showUpload" :closeDialog="closeUploadDialog" @submit="submitAttachment" />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import DividerHeader from '@/components/DividerHeader'
import { updateDelivery, updateDeliveryNote, getContactDetail, getDNNoteDetail, initNonKhDnBatch,
  postOutBoundDnWithServiceMaterial, unFreezeShipment, unFreezeInvoice,
  unfreezeDelivery, updateDN, getDN, cancelDN, submitDN, getOrderStatus, signReceipt, dnDetailAuthCheck } from '@/api/orderDelivery'

import BaseInfo from './components/common/BaseInfo'
import Delivery from './components/detail/Delivery'
import Finance from './components/detail/Finance'
import Batch from './components/detail/Batch'
import DetailTable from './components/common/DetailTable'
import RowMore from './components/common/RowMore'
import AttachmentUpload from './components/detail/AttachmentUpload.vue'
import { requestWithLoading, isSupportedAutoAccount } from './utils'
import { DeliveryFSM } from './utils/state'
import { OrderDeliveryDetailOrderInfoFields, OrderDeliveryDetailCargoFileFields, OrderDeliveryDetailOtherReqFields, OrderDeliveryDetailTransportFields } from './constants'
import moment from 'moment'

export default {
  name: 'OrderDeliveryEdit',
  components: {
    BaseInfo, Delivery, Finance, Batch, DetailTable, Pagination, DividerHeader, RowMore, AttachmentUpload
  },
  data () {
    const { id } = this.$route.params
    return {
      id,
      key: '',
      dnData: {},
      cancelData: {},
      isLoading: false,
      disabled: true,
      batchVisible: false,
      cancelVisible: false,
      deliveryNoteListData: [],
      deliveryNoteListDataTotal: 0,
      deliveryPaginationQuery: {
        page: 1,
        size: 10000
      },
      fsm: null,
      batchData: [],
      foldDelivery: true,
      foldFinance: true,
      editKey: '',
      attachOrder: false,
      referenceStandardShippingReq: false,
      specifiedDocument: false,
      attachCoa: '',
      attachMsds: '',
      attachTds: '',
      certificateIdentification: '',
      deliverySlipTemplate: '',
      labelTemplate: '',
      rules: {
        cancelReason: [
          { required: true, message: '请选择取消原因', trigger: 'blur' }
        ]
      },
      showUpload: false,
      foldOrderInfo: true,
      foldCargoFile: true,
      foldOtherReq: true,
      foldTransport: true,
      OrderDeliveryDetailOrderInfoFields,
      OrderDeliveryDetailCargoFileFields,
      OrderDeliveryDetailOtherReqFields,
      OrderDeliveryDetailTransportFields,
      basicAndDeliveryInfo: {}
    }
  },
  async created () {
    try {
      await this.dnDetailAuthCheck();
      if (JSON.stringify(this.orderServiceDict) === '{}') {
        this.$store.dispatch('orderCommon/getOrderServiceDict')
      }
      if (JSON.stringify(this.dictList) === '{}') {
        this.$store.dispatch('orderCommon/queryDictList').then(() => {
          this.init()
        })
      } else {
        this.init()
      }
    } catch (error) {
      this.$message.error(error.message)
    }
  },
  computed: {
    not402 () {
      return this.dnData.businessType !== '402'
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderServiceDict () {
      return this.$store.state.orderCommon.orderServiceDict || {}
    },
    isFreeze () {
      return !!(this.dnData &&
        this.dnData.deliveryInfo &&
        this.dnData.deliveryInfo.shipmentFreeze &&
        this.dnData.deliveryInfo.shipmentFreeze !== 'C')
    },
    hasDeliveryFreeze () {
      return !this.dnData.deliveryInfo.deliveryFreeze || this.dnData.deliveryInfo.deliveryFreeze === '05'
    },
    isSupportedAutoAccount () {
      return isSupportedAutoAccount(this.dnData)
    },
    isZRE3 () {
      return this.dnData.referOrderType === 'ZRE3'
    },
    showChangeDeliveryButton () {
      return this.dnData.status === 'CONFIRM' &&
      (this.dnData.deliveryInfo.attachCoa === 'X' ||
      this.dnData.deliveryInfo.attachMsds === 'X' ||
      this.dnData.deliveryInfo.attachTds === 'X' ||
      this.dnData.deliveryInfo.certificateIdentification === '1' ||
      this.dnData.deliveryInfo.deliverySlipTemplate === '1' ||
      this.dnData.deliveryInfo.deliverySlipTemplate === '4' ||
      this.dnData.deliveryInfo.labelTemplate === '1' ||
      this.dnData.deliveryInfo.attachOrder ||
      this.dnData.deliveryInfo.specifiedDocument ||
      this.dnData.deliveryInfo.referenceStandardShippingReq)
    },
    showChangeContactButton () {
      return this.dnData.status === 'NEW' && /z001|z006|z007|z008/gi.test(this.dnData.referOrderType)
    }
  },
  methods: {
    async dnDetailAuthCheck () {
      const res = await dnDetailAuthCheck({ dnNo: this.$route.params?.id, userDomain: window.CUR_DATA.user?.name });
      if (res.code !== 200) {
        throw new Error(res.msg || '详情查看权限请求失败！')
      } else {
        if (res.data === false) {
          window.location.href = '/unauthorized'
        }
      }
    },
    init () {
      const { id } = this.$route.params
      if (id) {
        this.isLoading = true
        const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
        Promise.all([getDN(id), getDNNoteDetail({
          id,
          size: this.deliveryPaginationQuery.size,
          current: this.deliveryPaginationQuery.page
        }), getOrderStatus(id)]).then(response => {
          this.isLoading = false
          loading.close()
          const [res1, res2, res4] = response
          if (res2 && res2.code === 200) {
            this.deliveryNoteListData = res2.data.records
            this.deliveryNoteListDataTotal = res2.data.total
          }
          if (res1 && res1.code === 200) {
            const {
              basicInfo: {
                referOrderType,
                hideLogo,
                requireDepartment
              },
              deliveryInfo: {
                contactId,
                contactInfo,
                receiptTimeCategory,
                attachCoa,
                attachMsds,
                attachOrder,
                attachTds,
                exportProcessingZone,
                referenceStandardShippingReq,
                specifiedDocument,
                serviceCenterSelfTransport,
                shipmentFreeze,
                customerDeliveryConfirmed
              },
              financeInfo: { invoicingByMail },
              status,
              statusDesc,
              sapSyncStatus,
              sapSyncStatusCode,
              positionOwner,
              showUploadSign,
              systemStatus,
              warehouseRuleCode
            } = res1.data
            this.fsm = new DeliveryFSM(res1.data)
            res1.data.deliveryInfo = {
              ...res1.data.deliveryInfo,
              requireDepartment,
              serviceCenterSelfTransport: serviceCenterSelfTransport === '001',
              receiptTimeCategory: receiptTimeCategory === 'X',
              attachCoa,
              attachMsds,
              attachOrder: attachOrder === 'X',
              attachTds,
              hideLogo: hideLogo === 'X',
              exportProcessingZone: exportProcessingZone === 'X',
              referenceStandardShippingReq: referenceStandardShippingReq === 'X',
              specifiedDocument: specifiedDocument === 'X',
              shipmentFreeze: !shipmentFreeze || shipmentFreeze === 'C' ? '' : shipmentFreeze,
              customerDeliveryConfirmed: customerDeliveryConfirmed === 'X'
            }
            res1.data.financeInfo = {
              ...res1.data.financeInfo,
              invoicingByMail: invoicingByMail === 'X'
            }
            res1.data.basicInfo['statusDesc'] = statusDesc
            res1.data.basicInfo['positionOwner'] = positionOwner
            res1.data.basicInfo['systemStatus'] = systemStatus
            res1.data.basicInfo['sapSyncStatus'] = sapSyncStatus
            res1.data.basicInfo['sapSyncStatusCode'] = sapSyncStatusCode
            res1.data.basicInfo['warehouseRuleCode'] = warehouseRuleCode
            if (res1.data.basicInfo['positionOwner'] === 'kh') {
              if (res4 && res4.code === '0000') {
                res1.data.basicInfo['desc'] = res4.result?.desc
              }
            }
            res1.data.status = status
            res1.data.referOrderType = referOrderType
            res1.data.showUploadSign = showUploadSign
            this.dnData = res1.data
            try {
              this.dnData.deliveryInfo._shippingCondition = this.dnData.deliveryInfo.shippingCondition
            } catch (err) {}
            this.attachOrder = this.dnData.deliveryInfo.attachOrder
            this.referenceStandardShippingReq = this.dnData.deliveryInfo.referenceStandardShippingReq
            this.specifiedDocument = this.dnData.deliveryInfo.specifiedDocument
            this.attachCoa = this.dnData.deliveryInfo.attachCoa
            this.attachMsds = this.dnData.deliveryInfo.attachMsds
            this.attachTds = this.dnData.deliveryInfo.attachTds
            this.certificateIdentification = this.dnData.deliveryInfo.certificateIdentification
            this.deliverySlipTemplate = this.dnData.deliveryInfo.deliverySlipTemplate
            this.labelTemplate = this.dnData.deliveryInfo.labelTemplate
            if (contactInfo) {
              this.dnData.deliveryInfo = {
                ...this.dnData.deliveryInfo,
                ...contactInfo,
                mobilephone: contactInfo.contactPhone
              }
            } else if (contactId) {
              getContactDetail(contactId).then(res2 => {
                if (res2 && res2.code === 200) {
                  this.dnData.deliveryInfo = {
                    ...this.dnData.deliveryInfo,
                    ...res2.data
                  }
                  this.basicAndDeliveryInfo = this.dnData.deliveryInfo
                }
              })
            }
            this.basicAndDeliveryInfo = this.dnData.deliveryInfo
          } else if (res1 && res1.msg) {
            this.$alert(res1.msg, '错误')
          }
        }).catch(error => {
          this.isLoading = false
          loading.close()
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
        })
      }
    },
    handleCancelOrder () {
      this.$refs['cancelForm'].validate((valid, items) => {
        if (valid) {
          const { cancelReason, cancelReasonDesc } = this.cancelData
          const loading = this.$loading({
            lock: true,
            background: 'rgba(0, 0, 0, 0.5)'
          })
          cancelDN({
            cancelReason,
            cancelReasonDesc,
            deliveryNoteNo: this.id,
            status: this.dnData.status
          }).then(res => {
            loading.close()
            if (res && res.code === 200) {
              this.$notify.success('取消成功！')
              this.cancelVisible = false
              this.init()
            } else if (res.msg) {
              this.$alert(res.msg, '错误')
            }
          }).catch(error => {
            loading.close()
            if (error.response && error.response.data) {
              const { message } = error.response.data
              if (message) {
                this.$alert(message, '错误')
              }
            }
          })
        }
      })
    },
    submitAttachment (fileList) {
      console.log(fileList)
      if (!fileList.length) return this.$message.error('请上传签单！')
      const { basicInfo } = this.dnData
      const { deliveryNo } = basicInfo || {}
      const data = {
        items: fileList.map(file => ({
          uploadPath: file.url,
          uploadTime: file.timestamp
        })),
        receiptOrDeliveryNo: deliveryNo,
        receivedDate: moment().format('YYYY-MM-DD HH:mm:ss'),
        receivedSource: 'BOSS',
        receivedStatus: '1',
        sysReceivedSource: 'BOSS'
      }
      const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
      signReceipt(data)
        .then(res => {
          console.log(res)
          if (res && res.code === 200) {
            this.$message.success(res?.data || '操作成功！')
            this.closeUploadDialog()
          } else {
            this.$message.error(res?.msg || res?.message || '操作失败！')
          }
        })
        .finally(() => {
          loading.close();
        })
    },
    showUploadDialog () {
      this.showUpload = true
    },
    closeUploadDialog () {
      this.showUpload = false
    },
    handleTransferAccount () {
      const { id } = this.$route.params
      const { basicInfo } = this.dnData
      if (basicInfo && basicInfo.referOrderType && basicInfo.serviceMaterial) {
        requestWithLoading(this, postOutBoundDnWithServiceMaterial({
          dnNo: id
        }), data => {
          this.$message({
            type: 'success',
            message: '过账成功！'
          })
          this.init()
        })
      } else {
        requestWithLoading(this, initNonKhDnBatch({
          dnNo: id
        }), data => {
          this.batchData = data
          this.batchVisible = true
        })
      }
    },
    handleEdit () {
      const { id } = this.$route.params
      this.$router.push({
        path: `/orderDelivery/edit/${id}`
      })
    },
    handleSubmit () {
      const { id } = this.$route.params
      this.$confirm('是否确认将DN交货单提交，下发至仓库?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
        submitDN({
          deliveryNoteNo: id,
          status: this.dnData.status
        }).then(res => {
          loading.close()
          if (res) {
            if (res.code === 200) {
              this.init()
            } else if (res.msg) {
              this.$alert(res.msg, '错误')
            }
          }
        }).catch(error => {
          loading.close()
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
        })
      }).catch(() => {})
    },
    queryDNNoteOrder () {
      this.isLoading = true
      const { id } = this.$route.params
      const params = {
        id,
        size: this.deliveryPaginationQuery.size,
        current: this.deliveryPaginationQuery.page
      }
      getDNNoteDetail(params).then(res => {
        this.isLoading = false
        if (res && res.code === 200) {
          this.deliveryNoteListData = res.data.records
          this.deliveryNoteListDataTotal = res.data.total
        }
      }).catch(error => {
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误')
          }
        }
        this.isLoading = false
      })
    },
    handleBatchCancel () {
      this.batchVisible = false
    },
    handleBatchSuccess () {
      this.batchVisible = false
      this.init()
    },
    handleFold(type, val) {
      this[type] = val;
    },
    handleDeliveryFold (val) {
      this.foldDelivery = val
    },
    handleFinanceFold (val) {
      this.foldFinance = val
    },
    freezeShipment () {
      requestWithLoading(this, unFreezeShipment(this.id), () => {
        this.init()
      })
    },
    freezeDelivery (submit) {
      requestWithLoading(this, unfreezeDelivery(this.id), () => {
        if (submit) {
          const callback = () => {
            const loading = this.$loading({
              lock: true,
              background: 'rgba(0, 0, 0, 0.5)'
            })
            submitDN({
              deliveryNoteNo: this.$route.params.id,
              status: this.dnData.status
            }).then(res => {
              loading.close()
              if (res) {
                if (res.code === 200) {
                  this.init()
                } else if (res.msg) {
                  this.$alert(res.msg, '错误')
                }
              }
            }).catch(error => {
              loading.close()
              if (error.response && error.response.data) {
                const { message } = error.response.data
                if (message) {
                  this.$alert(message, '错误')
                }
              }
            })
          }
          setTimeout(callback, 100)
        }
      })
    },
    freezeFinance () {
      requestWithLoading(this, unFreezeInvoice(this.id), () => {
        this.init()
      })
    },
    updateUnbillReason (val) {
      requestWithLoading(this, updateDN({
        deliveryNoteNo: this.id,
        status: this.dnData.status,
        unbillReason: val || '*'
      }), () => {
        this.dnData.financeInfo.unbillReason = val
      })
    },
    handleAfterAsync () {
      this.init()
    },
    refresh () {
      this.init()
    },
    changeEditKey(key) {
      this.editKey = key;
    },
    cancelEdit () {
      this.editKey = '';
      this.init()
    },
    updateEdit () {
      switch (this.editKey) {
        case 'changeDeliveryInfo':
          this.updateCustomer();
          break;
        case 'changeContactInfo':
          this.updateContact();
          break;
      }
    },
    updateContact () {
      const { contactId, deliveryRequireTime } = this.dnData.deliveryInfo || {}
      const { deliveryNo } = this.dnData.basicInfo || {}
      const data = {
        deliveryNo,
        receiverContact: contactId,
        deliveryRequireTime
      }
      updateDeliveryNote(data).then(res => {
        if (res.code === 200) {
          this.$message.success(res.msg || '操作成功')
          this.cancelEdit()
        } else {
          this.$message.error(res.msg || '操作失败')
        }
      })
      .catch(error => {
        console.log(error)
      })
    },
    async updateCustomer () {
      try {
        const data = {
          orderNo: this.id,
          attachOrder: this.dnData.deliveryInfo.attachOrder ? 'X' : 'Z',
          referenceStandardShippingReq: this.dnData.deliveryInfo.referenceStandardShippingReq ? 'X' : 'Z',
          specifiedDocument: this.dnData.deliveryInfo.specifiedDocument ? 'X' : 'Z',
          attachCoa: this.dnData.deliveryInfo.attachCoa,
          attachMsds: this.dnData.deliveryInfo.attachMsds,
          attachTds: this.dnData.deliveryInfo.attachTds,
          certificateIdentification: this.dnData.deliveryInfo.certificateIdentification,
          deliverySlipTemplate: this.dnData.deliveryInfo.deliverySlipTemplate,
          labelTemplate: this.dnData.deliveryInfo.labelTemplate
        }
        const res = await updateDelivery(data)
        if (res.code === 200) {
          this.$message.success(res.msg)
          this.cancelEdit()
        } else {
          this.$message.error(res.msg)
        }
        console.log(res);
      } catch (error) {
        console.log(error);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import "./styles/common.scss";
.dn-edit-container {
  padding: 15px;
}

.dn-edit-header {
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: space-between;
  .dn-title {
    font-size: 18px;
    font-weight: bold;
  }
}
.pagination-container {
  margin-top: 0;
  padding: 16px 0;
  text-align: right;
}
.dn-row-btn-list {
  display: flex;
  margin: 10px 0;
  align-items: center;
  justify-content: space-between;
}
</style>
