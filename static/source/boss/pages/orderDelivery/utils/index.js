import {
  deliveryFields1, deliveryFields2, deliveryFields3,
  financeFields1, financeFields2, financeFields3, financeFields4,
  OrderDeliveryCreateOrderInfoFields,
  OrderDeliveryCreateCargoFileFields,
  OrderDeliveryCreateOtherReqFields,
  OrderDeliveryCreateTransportFields
} from '../constants'

export function getDeliveryFields (key) {
  const fields = {
    create: deliveryFields1,
    edit_NEW: deliveryFields1,
    edit_CONFIRM: deliveryFields2,
    edit_SENT: deliveryFields3,
    edit_CANCEL: deliveryFields3
  }
  return fields[key]
}

export const getDnCreateFields = (key) => {
  const fields = {
    OrderInfo: OrderDeliveryCreateOrderInfoFields,
    CargoFile: OrderDeliveryCreateCargoFileFields,
    OtherReq: OrderDeliveryCreateOtherReqFields,
    Transport: OrderDeliveryCreateTransportFields
  }

  return fields[key]
}

export function getFinanceFields (key) {
  const fields = {
    create: financeFields1,
    edit_NEW: financeFields1,
    edit_CONFIRM: financeFields2,
    edit_SENT: financeFields3,
    edit_CANCEL: financeFields4,
    edit: financeFields4
  }
  return fields[key]
}

export function requestWithLoading (el, promiseFun, successCb) {
  const loading = el.$loading({
    background: 'rgba(0, 0, 0, 0.8)',
    lock: true
  })
  promiseFun.then(res => {
    loading.close()
    if (res && res.code === 200) {
      if (successCb) {
        successCb(res.data)
      }
    } else if (res && res.msg) {
      el.$alert(res.msg, '错误', {
        type: 'error',
        dangerouslyUseHTMLString: true
      })
    }
  }).catch(error => {
    loading.close()
    if (error.response && error.response.data) {
      const { message } = error.response.data
      if (message) {
        el.$alert(message, '错误', {
          type: 'error'
        })
      }
    }
  })
}

export function isSupportedAutoAccount (dnData) {
  if (dnData && dnData.status === 'CONFIRM' && dnData.basicInfo) {
    const referOrderType = dnData.basicInfo.referOrderType
    if (dnData.basicInfo.positionOwner === 'fw') {
      return false
    }
    if (dnData.basicInfo.serviceMaterial || referOrderType === 'ZEV3' || referOrderType === 'ZEV4') {
      return true
    }
    if (referOrderType === 'ZEV2') {
      return false
    }
    if (((referOrderType === 'Z009') || (dnData['positionOwner'] !== 'kh'))) {
      return true
    }
  }
  return false
}
