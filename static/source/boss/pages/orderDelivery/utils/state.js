import { deliveryState } from '../constants'

export class DeliveryFSM {
  constructor (data) {
    const { status, positionOwner } = data
    if (status != null && positionOwner != null) {
      this.state = deliveryState[status]
      this.positionOwner = positionOwner
      this.transitions = [
        { name: 'cancel', from: deliveryState.NEW, to: deliveryState.CANCEL },
        { name: 'cancel', from: deliveryState.CONFIRM, to: deliveryState.CANCEL },
        {
          name: 'edit',
          from: deliveryState.NEW,
          to: deliveryState.NEW
        },
        {
          name: 'edit',
          from: deliveryState.CONFIRM,
          to: deliveryState.CONFIRM,
          condition: () => this.positionOwner !== 'kh'
        },
        { name: 'submit', from: deliveryState.NEW, to: deliveryState.CONFIRM }
      ]
    }
  }

  canGoTo (state) {
    const foundItem = this.transitions ? this.transitions.find(item => {
      const { to: toState, from: fromState, condition } = item
      const isConditionFullfill = item.condition ? condition() : true
      const result = toState && toState.value === state &&
        fromState && this.state && fromState.value === this.state.value &&
        isConditionFullfill
      return result
    }) : null
    return !!foundItem
  }
}
