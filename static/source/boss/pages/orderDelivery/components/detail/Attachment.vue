<template>
  <el-table
    border
    :data="data"
    style="margin-top: 10px"
  >
    <el-table-column
      type="index"
      label="序号"
      align="center"
      width="50">
    </el-table-column>
    <el-table-column
      prop="name"
      label="附件名称"
      align="center"
      width="500"
    >
      <template slot-scope="{row}">
        <a :href="row.attachmentUrl" target="_blank">{{row.name}}</a>
      </template>
    </el-table-column>
    <el-table-column
      prop="tag"
      align="center"
      label="标签">
    </el-table-column>
  </el-table>
</template>
<script>

export default {
  props: ['data'],
  methods: {
  }
}
</script>
<style scoped lang="scss">
.attachment-list {
  li {
    span:first-child {
      display: inline-block;
      width: 300px;
    }
  }
}
</style>
