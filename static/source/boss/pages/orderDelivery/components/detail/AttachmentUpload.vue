<template>
  <el-dialog
    title="上传签单"
    :visible.sync="visible"
    width="666px"
    :destroy-on-close="true"
    class="delivery-order-upload"
    append-to-body
    :before-close="handleCancel"
  >
    <p class="tips">上传签收单截图支持JPEG、JPG、GIF、PNG、PDF等格式</p>
    <el-upload
      accept=".png,.jpg,.jpeg,.gif,.pdf"
      action=""
      ref="upload"
      multiple
      show-file-list
      :limit="fileSizeLimit"
      list-type="picture-card"
      :on-remove="handleRemove"
      :on-exceed="handleExceed"
      :on-preview="handlePreview"
      :http-request="httpRequestHandle"
      :before-upload="beforeUpload"
      :file-list="fileList"
    >
      <i class="el-icon-plus"></i>
      <div slot="file" slot-scope="{ file }">
        <img v-if="isPic(file.url)" class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
        <img v-else class="el-upload-list__item-thumbnail" :src="pdfPic" alt="" />
        <span class="el-upload-list__item-actions">
          <span
            class="el-upload-list__item-preview"
            @click="handlePreview(file)"
          >
            <i class="el-icon-zoom-in"></i>
          </span>
          <span
            class="el-upload-list__item-delete"
            @click="handleRemove(file)"
          >
            <i class="el-icon-delete"></i>
          </span>
        </span>
      </div>
    </el-upload>
    <div class="button-group">
      <el-button @click="handleCancel" size="mini">取消</el-button>
      <el-button type="primary" @click="handleSubmit" size="mini"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { upload } from '@/utils/upload';
import pdfPic from '@/assets/images/file.png'
import moment from 'moment'
export default {
  props: ['visible', 'closeDialog', 'deliveryNo'],
  data() {
    return {
      fileList: [],
      fileSizeLimit: 10,
      pdfPic,
      numberLimit: 10
    };
  },
  methods: {
    isPic(url) {
      return /jp(e?)g|png|gif/.test(url)
    },
    handleExceed () {
      this.$message.error(`最多上传${this.numberLimit}个附件！`)
    },
    handleClose() {
      this.$refs.upload.clearFiles();
      this.fileList = [];
      this.closeDialog();
    },
    handleCancel() {
      this.handleClose();
    },
    handleSubmit() {
      this.$emit('submit', this.fileList)
    },
    handleRemove(file, fileList) {
      if (!fileList) fileList = this.fileList
      fileList = fileList.filter((item) => item.url !== file.url);
      this.fileList = fileList.map((item) => {
        return {
          ...item,
          name: item.name,
          url: item.url
        };
      });
    },
    handlePreview(file) {
      if (file.url || file.path) {
        window.open(file.url || file.path);
      }
    },
    async httpRequestHandle(file) {
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.9)'
      });
      const response = await upload(
        `orderDelivery/detail/signAttachment/${this.deliveryNo}`,
        file.file
      );
      if (response?.url) {
        this.fileList.push({
          name: file.file.name,
          url: response.url,
          timestamp: moment().valueOf()
        });
      } else {
        // this.$message.error('上传失败！');
      }
      setTimeout(() => {
        loading.close();
      }, 600)
    },
    beforeUpload(file) {
      // 校验大小 文件类型
      const isGtLimit = file.size / 1024 / 1024 < this.fileSizeLimit;
      if (!isGtLimit) {
        this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!');
      }
      const typeReg = /jp(e?)g|png|gif|pdf/
      const isTypeLimit = typeReg.test(file.name);
      if (!isTypeLimit) {
        this.$message.error('只支持JPEG、JPG、GIF、PNG、PDF等格式');
      }
      return isGtLimit && isTypeLimit;
    }
  }
};
</script>

<style scoped lang="scss">
.delivery-order-upload {
  .tips {
    color: red;
    margin: -40px 0px 20px 0px;
  }
  .button-group {
    text-align: right;
  }
}
</style>
