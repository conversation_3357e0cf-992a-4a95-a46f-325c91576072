<template>
<div class="batch">
  <el-table
    border
    fit
    highlight-current-row
    v-loading="isLoading"
    :data="listData"
    style="width: 100%"
  >
    <el-table-column align="center" type="index" prop="rowNo" label="序号"></el-table-column>
    <el-table-column align="center" prop="sku" label="SKU编号" width="100"></el-table-column>
    <el-table-column align="center" prop="skuDesc" label="物料描述" width="300"></el-table-column>
    <el-table-column align="center" prop="salesUnit" label="单位">
      <template slot-scope="{row}">
        <span>{{formatQuantityUnit(row.salesUnit)}}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="lfimg" label="交货数量" width="100">
    </el-table-column>
    <el-table-column align="center" label="发货详情" width="100">
      <template slot-scope="{row}">
        <el-button @click="handleEdit(row)" type="primary" size="mini" round>
          {{ specialStock === 'E' ? '查看' : '编辑' }}
        </el-button>
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="referOrderNo"
      label="OMS销售订单号"
      width="200">
      <template slot-scope="{row}">
        <el-tooltip placement="top" effect="light">
          <span class="order-no-list">
            {{ row.referOrderNo || "--" }}
            <i class="el-icon-info" />
          </span>
          <TooltipNo
            slot="content"
            :noList="getOrderNoTooltip(row)"
          />
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="warehouseStatus" label="仓库状态"></el-table-column>
    <el-table-column align="center" prop="transferStatus" label="发货状态" width="100"></el-table-column>
    <el-table-column align="center" prop="deliveryStatus" label="签收状态" width="180"></el-table-column>

    <el-table-column align="center" prop="factory" label="工厂" width="240"></el-table-column>
    <el-table-column align="center" prop="position" label="库存地点" width="120"></el-table-column>
    <el-table-column align="center" prop="roughWeight" label="毛重" width="180"></el-table-column>

    <el-table-column align="center" prop="roughUnit" label="毛重单位"></el-table-column>
    <el-table-column align="center" prop="volume" label="容量" width="100"></el-table-column>
    <el-table-column align="center" prop="volumeUnit" label="容量单位" width="180"></el-table-column>

    <el-table-column align="center" prop="memo" label="跟单备注" width="180">
      <template slot-scope="{ row }">
        <span>{{row.memo}}</span>
      </template>
    </el-table-column>
  </el-table>
  <div class="btn-row">
    <el-button type="primary" @click="handleBatch" size="mini">确认过账</el-button>
    <el-button @click="handleBatchCancel" size="mini">取消</el-button>
  </div>
  <el-dialog
    title="编辑发货详情"
    :visible.sync="showEditDeliveryDetail"
    width="700px"
    :destroy-on-close="true"
    append-to-body
  >
    <el-table
      :data="batchTableList"
      style="width: 100%"
    >
      <el-table-column align="center" type="index" prop="rowNo" label="序号"></el-table-column>
      <el-table-column align="center" prop="firstBatch" label="批次号">
        <template slot-scope="{row}">
          <el-select v-model="row.firstBatch" clearable placeholder="请选择" :disabled="specialStock==='E'">
            <el-option
              v-for="item in batchList.filter(t=>selectedTableList.indexOf(t.firstBatch)===-1)"
              :key="item.firstBatch"
              :label="item.firstBatch"
              :value="item.firstBatch"
            >
            </el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="postQty" label="实际交货数量">
        <template slot-scope="{row}">
          <el-input-number
            v-model.number="row.postQty"
            size="mini"
            :style="{width: '150px'}"
            :min="0"
            :precision="3"
            :step="1"
            :disabled="specialStock==='E'"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" prop="operation" label="操作" v-if="specialStock !== 'E'">
        <template slot-scope="{$index}">
          <el-button v-if="true" type="text" size="mini" @click="handleDel($index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="btn-row" v-if="specialStock !== 'E'">
      <el-button v-if="selectedTableList&&batchList&&selectedTableList.length<batchList.length" type="primary" @click="handleAdd" size="mini">新增批次</el-button>
      <el-button v-if="specialStock !== 'E'" type="primary" @click="handleOk" size="mini">确定保存</el-button>
      <el-button @click="handleCancel" size="mini">取消</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import { isNumber } from '@/utils'
import { postOutBoundDn, queryAvailableWithSku } from '@/api/orderDelivery'
import { requestWithLoading } from '../../utils'
import TooltipNo from '../TooltipNo'

export default {
  props: ['batchData', 'listData'],
  components: {
    TooltipNo
  },
  data () {
    return {
      batchList: [],
      firstBatchList: {},
      batches: {},
      editRow: '',
      batchTableList: [],
      showEditDeliveryDetail: false,
      isLoading: false
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    selectedTableList () {
      return this.batchTableList.map(item => item.firstBatch)
    },
    specialStock () {
      const foundItem = this.listData.find(item => !!item.specialStock)
      return foundItem ? foundItem.specialStock : ''
    }
  },
  methods: {
    formatQuantityUnit (value) {
      if (value && isNumber(value) && this.dictList) {
        const unit = this.dictList['quantityUnit'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    handleAdd () {
      this.batchTableList.push({
        firstBatch: '',
        postQty: ''
      })
    },
    handleOk () {
      const { dnItemNo, dnNo, lfimg } = this.editRow
      const selectedKey = `${dnNo}_${dnItemNo}`
      const foundEmpty = this.batchTableList.some(item => !(item.firstBatch && item.postQty))
      if (foundEmpty) {
        this.$message.error('批次号，或者实际交货数量不能为空')
        return
      }
      const sum = this.batchTableList.reduce((x, y) => {
        return x + y.postQty
      }, 0)
      if (sum > lfimg) {
        this.$message.error('所有批次的交货数量总和不得大于该商品行的交货数量')
        return
      }
      this.showEditDeliveryDetail = false
      this.batchTableList.forEach((item, idx) => {
        const { firstBatch } = item
        if (firstBatch) {
          const found = this.batchList.find(b => b.firstBatch === firstBatch)
          if (found) {
            // const { factory, position, qty, sku, inventoryId } = found
            this.$set(this.batchTableList, idx, {
              ...item,
              ...found
              // factory,
              // position,
              // qty,
              // sku,
              // inventoryId
            })
          }
        }
      })
      if (this.batches[selectedKey]) {
        this.batches[selectedKey].batchList = this.batchTableList
      }
    },
    handleEdit (row) {
      const { dnItemNo, dnNo } = row
      const selectedKey = `${dnNo}_${dnItemNo}`
      this.editRow = row
      if (this.batches[selectedKey]) {
        const { batchList } = this.batches[selectedKey]
        this.batchTableList = [...batchList]
        this.batchList = this.firstBatchList[selectedKey] || []
        this.showEditDeliveryDetail = true
      } else {
        requestWithLoading(this, queryAvailableWithSku({
          dnNo,
          dnItemNo
        }), data => {
          if (data && data.firstBatchList) {
            this.batchList = data.firstBatchList
            const item = this.batchData.find(item => item.itemNo === dnItemNo)
            if (item) {
              const { batchList } = item
              this.batches[selectedKey] = item
              this.firstBatchList[selectedKey] = data.firstBatchList
              this.batchTableList = [...batchList]
              this.showEditDeliveryDetail = true
            }
          }
        })
      }
    },
    handleDel (idx) {
      this.batchTableList.splice(idx, 1)
    },
    getOrderNoTooltip (item) {
      return item ? [
        { title: 'OMS订单号', no: item.referOrderNo ? item.referOrderNo.split(',') : [''] },
        { title: 'SAP订单号', no: item.sapOrderNo ? item.sapOrderNo.split(',') : [''] },
        { title: '客户订单号', no: item.customerReferenceNo ? item.customerReferenceNo.split(',') : [''] },
        { title: '外围订单号', no: item.orderNo ? item.orderNo.split(',') : [''] }
      ] : []
    },
    handleBatch () {
      this.$confirm('确认需要将该DN交货单过账？过账后所有信息将无法修改', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const values = []
        let no = ''
        this.listData.forEach(row => {
          const { dnNo, dnItemNo } = row
          if (!no) {
            no = dnNo
          }
          const k = `${dnNo}_${dnItemNo}`
          if (this.batches[k]) {
            values.push(this.batches[k])
          } else {
            const batchOne = this.batchData.find(item => item.itemNo === dnItemNo)
            if (batchOne) {
              values.push(batchOne)
            }
          }
        })
        if (values.length > 0) {
          requestWithLoading(this, postOutBoundDn({
            reqId: no,
            itemList: values
          }), data => {
            this.$message.success('一键过账成功')
            this.$emit('success')
          })
        }
      }).catch(() => {})
    },
    handleBatchCancel () {
      this.$emit('cancel')
    },
    handleCancel () {
      this.showEditDeliveryDetail = false
    }
  }
}
</script>

<style scoped lang="scss">
.btn-row {
  margin: 20px 0;
  text-align: center;
}
</style>
