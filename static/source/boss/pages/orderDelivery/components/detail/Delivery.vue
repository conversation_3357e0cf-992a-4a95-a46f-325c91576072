<template>
<div class="field-container" :class="{ padding: type==='edit' }" v-show="!fold">
  <el-row :gutter="20" v-for="(fieldList,rowIdx) in fieldsList" :key="'row'+rowIdx">
    <el-col
      v-for="(field,itemIdx) in fieldList"
      :key="'item'+itemIdx"
      :span="field.span"
    >
      <span class="field-label" v-if="field.type!=='checkbox'">
        <el-tooltip v-if="field.prop === 'labelPasteWay'" effect="dark" content="仅适用于非OEM紧固件" placement="top">
          <i class="el-icon-info"></i>
        </el-tooltip>
        {{field.title}}：
      </span>
      <span class="field-value">
        <template v-if="editKey === 'changeDeliveryInfo' && (field.prop ==='attachCoa' && attachCoa === 'X' || field.prop ==='attachMsds' && attachMsds === 'X' || field.prop ==='attachTds' && attachTds === 'X' || field.prop ==='certificateIdentification' && certificateIdentification === '1' || field.prop ==='deliverySlipTemplate' && (deliverySlipTemplate === '1' || deliverySlipTemplate === '4') || field.prop ==='labelTemplate' && labelTemplate === '1')">
          <el-select
            v-model="basicAndDeliveryInfo[field.prop]"
            :placeholder="'请选择'+field.title"
          >
            <el-option
              v-for="item in dictList[field.dictKey]"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
        </template>
        <template v-else-if="field.prop === 'contactName' && editKey === 'changeContactInfo'">
          <el-select
            v-model="basicAndDeliveryInfo.contactId"
            placeholder="选择收货联系人"
            filterable
            remote
            reserve-keyword
            :remote-method="queryContactList"
            :loading="loadingContact"
            @change="changeContact"
          >
            <el-option
              v-for="(item, index) in contactList"
              :key="item.contactId"
              :label="item.contactName"
              :value="item.contactId"
              :disabled="index===0"
            >
              <div
                class="ba-row-start selectClientItem"
                :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
              >
                <div>{{ item.contactName }}</div>
                <div>{{ item.mobilephone || item.telephone || item.contactPhone || '--' }}</div>
                <div>{{ item.address || '--' }}</div>
              </div>
            </el-option>
            <el-option style="color:#ccc" disabled :value="-1" v-show="contactList && contactList.length===0">
              无数据
            </el-option>
            <el-option style="color:#ccc" disabled :value="-1" v-show="contactList && contactList.length >=20">
              已展示部分联系人，其他联系人请输入字符进行查询
            </el-option>
          </el-select>
        </template>
        <template v-else-if="field.prop === 'deliveryRequireTime' && editKey === 'changeContactInfo'">
          <el-date-picker v-model="basicAndDeliveryInfo.deliveryRequireTime" placeholder="请选择计划发货日期" value-format="yyyy-MM-dd"></el-date-picker>
        </template>
        <template v-else-if="field.prop ==='shippingCondition'">
          <span v-if="showText(field)">{{ getDictName(data[field.prop], field.dictKey) }}</span>
          <el-select
            v-else
            v-model="basicAndDeliveryInfo.shippingCondition"
            :disabled="!editable"
            :placeholder="'请选择'+field.title"
            @change="handleShipChange"
            size="mini"
            style="width: 120px;"
          >
            <el-option
              v-for="item in dictList[field.dictKey]"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            />
          </el-select>
          <i v-if="!showText(field)" style="margin-left: 2px;cursor: pointer;" class="el-icon-edit-outline" @click="editable = !editable" />
        </template>
        <template v-else-if="field.prop==='mobilephone'">
          <safe-phone-num
            v-if="data['mobilephone']"
            :phone="data['mobilephone']"
            :value="
              JSON.stringify({
                user: user.name,
                no: deliveryNo,
                value: coverMobileAndLandline(data['mobilephone']),
                field: '收货电话',
                dataType: '交货单',
              })
            "
          ></safe-phone-num>
        </template>
        <template v-else-if="field.type==='text' || field.type==='input'">
          {{ data ? data[field.prop] : '' }}
        </template>
        <template v-else-if="field.type==='select'">
          <span v-if="field.style==='tag'&& data[field.prop]">
            <el-tag type="success" v-for="item in data[field.prop].split(',')" :key="item" style="margin-right: 5px">
              {{ getDictName(item, field.dictKey) }}
            </el-tag>
          </span>
          <el-tag v-else-if="field.style==='tag'">无</el-tag>
          <span v-else-if="field.multiple">{{ getMultiSelectLabel(field.dictName, field.dictKey, data[field.prop]) }}</span>
          <span v-else>{{ getDictName(data[field.prop], field.dictKey, field.dictName) }}</span>
          <el-button
            type="primary"
            size="mini"
            style="margin-left: 20px"
            v-if="field.prop==='shipmentFreeze'&&data[field.prop]"
            @click="handleFreezeShipment"
          >解冻</el-button>
          <el-button
            type="primary"
            size="mini"
            style="margin-left: 20px"
            v-if="field.prop==='deliveryFreeze'&&data[field.prop]&&data[field.prop].includes('98')"
            @click="handleFreezeDelivery(data[field.prop])"
          >含易制毒品解冻</el-button>
        </template>
      </span>
      <el-checkbox v-if="field.type==='checkbox'" :label="field.title" v-model="basicAndDeliveryInfo[field.prop]" :disabled="!(editKey === 'changeDeliveryInfo' && (field.prop === 'attachOrder' && attachOrder || field.prop === 'specifiedDocument' && specifiedDocument || field.prop === 'referenceStandardShippingReq' && referenceStandardShippingReq))">
      </el-checkbox>
    </el-col>
  </el-row>
</div>
</template>

<script>
import { DeliveryTextFields } from '../../constants'
import { requestWithLoading } from '../../utils'
import { isCurrentUserLeader, shippingCondition } from '@/api/orderDelivery'
import '@boss/web-components';
import { coverMobileAndLandline } from '@/utils/index'
import { searchContactListByGroup } from '@/api/orderSale'
import { contactHeader } from '@/pages/orderSale/constants'

export default {
  props: ['data', 'dnData', 'contact', 'type', 'fold', 'id', 'editKey', 'attachOrder', 'referenceStandardShippingReq', 'specifiedDocument', 'attachCoa', 'attachMsds', 'attachTds',
    'certificateIdentification', 'deliverySlipTemplate', 'labelTemplate', 'fields', 'basicAndDeliveryInfo'
  ],
  data () {
    return {
      coverMobileAndLandline,
      fieldsList: this.fields ? this.fields : DeliveryTextFields,
      shippingCondition: '',
      editable: false,
      loadingContact: false,
      contactList: []
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderServiceDict () {
      return this.$store.state.orderCommon.orderServiceDict || {}
    },
    user() {
      return window.CUR_DATA.user
    },
    deliveryNo () {
      return this.dnData?.basicInfo?.deliveryNo
    }
  },
  watch: {
    'dnData.deliveryInfo'(value) {
      const { contactId } = value
      const { contactName, contactPhone, address } = value.contactInfo;
      if (!value) return;
      this.contactList = [
          contactHeader,
          { contactName, mobilephone: contactPhone, address, contactId }
        ]
    }
  },
  methods: {
    showText(field) {
      console.log(this.data[field.prop] === '06', this.dnData.businessType === '402', (this.dnData.status !== 'CONFIRM'))
      return this.data[field.prop] === '06' || this.dnData.businessType === '402' || (this.dnData.status !== 'CONFIRM') || /sto/.test(location.href)
    },
    handleShipChange (val) {
      console.log(val);
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const data = {
        shippingCondition: val,
        orderNo: this.dnData?.basicInfo?.deliveryNo
      }
      shippingCondition(data)
        .then(res => {
          console.log(res)
          if (res.code === 200 && res.success === true) {
            this.$message.success('修改成功！');
          } else {
            if (this.dnData?.deliveryInfo?._shippingCondition) {
              this.dnData.deliveryInfo.shippingCondition = this.dnData.deliveryInfo._shippingCondition
            }
            console.log(this.dnData.deliveryInfo.shippingCondition);
            this.$message.error(res.msg || '修改失败！');
          }
        })
        .finally(() => {
          loading.close()
        })
    },
    getMultiSelectLabel (dictName, dictKey, val) {
      if (val && val.length) {
        const codes = (typeof val === 'string' && val) ? val.split(',') : val
        return codes.map((code) => {
          const foundItem = (this[dictName]?.[dictKey]?.['options'] || this[dictName]?.[dictKey] || []).find(item => item.code === code)
          if (foundItem) {
            return foundItem.name
          }
          return code
        }).join(',')
      }
    },
    getDictName (code, dictKey, dictName) {
      if (code) {
        const foundItem = (this[dictName || 'dictList']?.[dictKey]?.['options'] || this[dictName || 'dictList']?.[dictKey] || []).find(item => item.code === code)
        if (foundItem) {
          return foundItem.name
        }
      }
      return ''
    },
    handleFreezeShipment () {
      if (this.id) {
        requestWithLoading(this, isCurrentUserLeader(this.id), res => {
          if (res) {
            this.$confirm('是否确认解冻该订单，清空装运冻结？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.$emit('freezeShipment')
            }).catch(() => {
            })
          } else {
            this.$alert('您暂无权限操作，请联系对应客服主管进行操作', '错误', {
              type: 'error',
              dangerouslyUseHTMLString: true
            })
          }
        })
      }
    },
    handleFreezeDelivery (deliveryFreeze) {
      let msg = '是否确认解冻该订单，清空交货冻结？'
      if (deliveryFreeze.includes('98')) {
        msg = '确认EHS已备案，解除含易制毒品交货冻结？'
      }
      if (this.id) {
        this.$confirm(msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$emit('freezeDelivery', true)
        }).catch(() => {
        })
      }
    },
    queryContactList (contactName) {
      this.loadingContact = true
      const { distributionChannel, productGroup, salesOrganization, customerNo } = this.dnData?.basicInfo
      if (customerNo) {
        searchContactListByGroup({
          distributionChannel,
          productGroup,
          salesOrganization,
          contactName,
          customerCode: customerNo
        }).then(res => {
          this.loadingContact = false
          if (res && res.code === 200) {
            const { records } = res.data
            this.contactList = records && records.length > 0 ? [
              contactHeader,
              ...records
            ] : []
          }
        })
      }
    },
    changeContact (val) {
      const contact = this.contactList.find(item => item.contactId === val)
      if (contact) {
        const { contactName, telephone, mobilephone, contactPhone, address } = contact
        // 电话
        this.dnData.deliveryInfo.contactName = contactName;
        this.dnData.deliveryInfo.mobilephone = mobilephone || contactPhone || telephone
        this.dnData.deliveryInfo.contactPhone = mobilephone || contactPhone || telephone
        // 地址
        this.dnData.deliveryInfo.address = address
      }
    }
  }
}
</script>

<style scoped lang="scss">
.field-container {
  font-size: 14px;
  color: rgb(96, 98, 102);
  padding: 0 10px;
  .el-row {
    margin-bottom: 20px;
  }
  .field-label {
    color: #8c8c8c;
  }
  .field-value {
    color: #262626;
  }
}
.padding {
  padding: 0 40px;
}
.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 150px;
  }
  div:nth-child(3) {
    width: 300px;
  }
}
</style>
