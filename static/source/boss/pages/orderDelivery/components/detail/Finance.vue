<template>
<div class="field-container" :class="{ padding: type==='edit' }" v-show="!fold">
  <el-row :gutter="20" v-for="(fieldList,rowIdx) in fields" :key="'row'+rowIdx">
    <el-col
      v-for="(field,itemIdx) in fieldList"
      :key="'item'+itemIdx"
      :span="field.span"
    >
      <span class="field-label" v-if="field.type!=='checkbox'">{{field.title}}：</span>
      <span class="field-value">
        <template v-if="field.type==='text'">
          {{ data ? data[field.prop] : '' }}
        </template>
        <template v-if="field.type==='select'">
          <el-tag type="success" v-if="field.style==='tag'&&data[field.prop]">
            {{ getDictName(data[field.prop], field.dictKey) }}
          </el-tag>
          <el-tag v-else-if="field.style==='tag'">无</el-tag>
          <span v-else>{{ getDictName(data[field.prop], field.dictKey) }}</span>
          <el-button
            type="primary"
            size="mini"
            style="margin-left: 20px"
            v-if="field.prop==='unbillReason'&&data['invoiceStatus']==='A'"
            @click="handleUnbillReason"
          >修改</el-button>
          <el-button
            type="primary"
            size="mini"
            style="margin-left: 20px"
            v-if="field.prop==='invoiceFreeze'&&data[field.prop]"
            @click="handleFreezeOrder"
          >解冻</el-button>
        </template>
      </span>
      <el-checkbox v-if="field.type==='checkbox'" :label="field.title" :value="data ? data[field.prop] : false" disabled>
      </el-checkbox>
    </el-col>
  </el-row>
  <el-dialog
    title="修改"
    :visible.sync="unbillReasonVisible"
    @open="openDlg"
    width="300px"
    center
  >
    <el-select
      v-model="unbillReason"
      style="width:100%"
      placeholder="请选择未开票原因"
    >
      <el-option
        v-for="item in dictList['unInvoiceReason']"
        :key="item.code"
        :label="item.name"
        :value="item.code"
      />
    </el-select>
    <span slot="footer" class="dialog-footer">
      <el-button @click="unbillReasonVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleUpdateUnbillReason">确 定</el-button>
    </span>
  </el-dialog>
</div>
</template>

<script>
import { FinanceTextFields } from '../../constants'
import { requestWithLoading } from '../../utils'
import { isCurrentUserLeader } from '@/api/orderDelivery'

export default {
  props: ['data', 'type', 'fold', 'id'],
  data () {
    return {
      unbillReason: '',
      unbillReasonVisible: false,
      fields: FinanceTextFields
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  methods: {
    openDlg () {
      this.unbillReason = this.data['unbillReason']
    },
    handleUpdateUnbillReason () {
      this.unbillReasonVisible = false
      this.$emit('updateUnbillReason', this.unbillReason)
    },
    handleUnbillReason () {
      this.unbillReasonVisible = true
    },
    getDictName (code, dictKey) {
      if (code) {
        const foundItem = this.dictList[dictKey].find(item => item.code === code)
        if (foundItem) {
          return foundItem.name
        }
      }
      return ''
    },
    handleFreezeOrder () {
      if (this.id) {
        requestWithLoading(this, isCurrentUserLeader(this.id), res => {
          if (res) {
            this.$confirm('是否确认解冻该订单，清空交货冻结？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              this.$emit('freeze')
            }).catch(() => {
            })
          } else {
            this.$alert('无权操作', '错误', {
              type: 'error',
              dangerouslyUseHTMLString: true
            })
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.field-container {
  font-size: 14px;
  color: rgb(96, 98, 102);
  padding: 0 10px;
  .el-row {
    margin-bottom: 20px;
  }
  .field-label {
    color: #8c8c8c;
  }
  .field-value {
    color: #262626;
  }
}
.padding {
  padding: 0 40px;
}
</style>
