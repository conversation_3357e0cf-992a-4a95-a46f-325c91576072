<template>
  <ul class="tooltip-no">
    <li v-bind:key="item.title" v-for="(item) in noList">
      <div class="title">{{item.title}}</div>
      <div v-for="(n,idx) in item.no" :key="`${n}_${idx}`" class="rowNo">
        <div>
          <el-button type="text" v-if="item.type" @click="handleLink(item.type, n, item.row)">{{n}}</el-button>
          <span v-else>{{n}}</span>
          <span v-if="item.itemNo">/{{item.itemNo}}</span>
          <span v-if="item.itemDetailNo">/{{item.itemDetailNo}}</span>
        </div>
        <el-button type="text" @click="handleCopy(n, $event)">复制</el-button>
      </div>
    </li>
  </ul>
</template>
<script>
import clip from '@/utils/clipboard'

export default {
  props: ['noList'],
  methods: {
    handleCopy (no) {
      clip(no, event, () => {
        const content = '复制成功'
        this.$message({
          message: content,
          type: 'success'
        })
      })
    },
    handleLink (type, no, row) {
      this.$emit('router', { type, no, row })
    }
  }
}
</script>
<style scoped lang="scss">
.tooltip-no {
  width: 300px;
  .title {
    font-weight: bold;
  }
  .rowNo {
    display: flex;
    justify-content: space-between;
    line-height: 40px;
    color: #409eff;
  }
  .copy {
    cursor: pointer;
  }
}
</style>
