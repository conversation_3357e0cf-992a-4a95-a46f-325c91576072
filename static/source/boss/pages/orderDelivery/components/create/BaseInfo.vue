<template>
<div class="field-container">
  <el-row :gutter="20" v-for="(fieldList,rowIdx) in fields" :key="'row'+rowIdx">
    <el-col
      v-for="(field,itemIdx) in fieldList"
      :key="'item'+itemIdx"
      :span="field.span"
    >
      <span class="field-label">{{field.title}}：</span>
      <span class="field-value">
        <template v-if="field.type==='text'">
          {{ data ? data[field.prop] : '' }}
        </template>
        <el-checkbox v-if="field.type==='checkbox'" label="" :value="data ? data[field.prop] : false" disabled>
        </el-checkbox>
        <template v-if="field.type==='select'">
          {{ getDictName(data[field.prop], field.dictKey) }}
        </template>
      </span>
    </el-col>
  </el-row>
</div>
</template>

<script>
import { BaseinfoCreateFields, BaseinfoStoCreateFields } from '../../constants'
export default {
  props: ['data', 'statusDesc', 'sto'],
  computed: {
    fields() {
      return this.sto ? BaseinfoStoCreateFields : BaseinfoCreateFields
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  methods: {
    getDictName (code, dictKey) {
      if (code) {
        const foundItem = this.dictList[dictKey].find(item => item.code === code)
        if (foundItem) {
          return foundItem.name
        }
      }
      return ''
    }
  }
}
</script>

<style scoped lang="scss">
.field-container {
  font-size: 14px;
  color: rgb(96, 98, 102);
  .el-row {
    margin-bottom: 10px;
  }
  .field-label {
    width: 110px;
    display: inline-block;
    text-align: right;
    color: #8c8c8c;
  }
  .field-value {
    color: #262626;
  }
}
</style>
