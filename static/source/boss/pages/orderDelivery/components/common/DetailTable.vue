<template>
<div>
  <el-table
    border
    fit
    highlight-current-row
    v-loading="isLoading"
    :data="data"
    @selection-change="handleSelectionChange"
    style="width: 100%"
  >
    <el-table-column align="center" type="selection" width="40" v-if="type!=='detail'"></el-table-column>
    <el-table-column align="center" type="index" prop="rowNo" label="序号"></el-table-column>
    <el-table-column align="center" prop="sku" label="SKU编号" width="100"></el-table-column>
    <el-table-column align="center" prop="skuDesc" label="物料描述" width="300"></el-table-column>
    <el-table-column align="center" prop="salesUnit" label="单位">
      <template slot-scope="{row}">
        <span>{{formatQuantityUnit(row.salesUnit)}}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="lfimg" label="交货数量" width="150">
      <template slot-scope="{row, $index}">
        <el-input-number
          v-model="row.lfimg"
          size="mini"
          style="width:100%"
          :min="0"
          :step="1"
          :precision="3"
          :disabled="type==='detail'||(status==='CONFIRM'&&type==='edit')"
          @change="val=>quantityChange(val,$index)"
        />
      </template>
    </el-table-column>
    <el-table-column align="center" prop="quantity" label="整行订单数量" width="100" v-if="type=='init'"></el-table-column>
    <el-table-column align="center" prop="needScrapingCode" label="需要刮码">
      <template slot-scope="{row}">
        <el-checkbox
          :disabled="!!status"
          v-model="row.needScrapingCode"
          true-label="X"
        ></el-checkbox>
      </template>
    </el-table-column>
    <el-table-column
      align="center"
      prop="referOrderNo"
      :label="sto?'调拨单号':'OMS销售订单号'"
      width="200"
    >
      <template slot-scope="{row}">
        <span v-if="sto">{{row.referOrderNo}}</span>
        <el-tooltip v-else placement="top" effect="light">
          <span class="order-no-list">
            {{ row.referOrderNo || "--" }}
            <i class="el-icon-info" />
          </span>
          <TooltipNo
            slot="content"
            :noList="getOrderNoTooltip(row)"
            @router="handleRouter"
          />
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column align="center" label="发货详情" width="100" v-if="status&&status!=='NEW'">
      <template slot-scope="{row}">
        <el-button @click="handleEdit(row)" type="primary" size="mini" round v-if="type==='edit'">编辑</el-button>
        <el-button @click="handleClick(row)" type="primary" size="mini" round v-else>查看</el-button>
      </template>
    </el-table-column>
    <el-table-column align="center" label="SKU尺寸" width="100">
      <template slot-scope="{row}">
        <el-button @click="showSkuDialog(row)" type="primary" size="mini" round>查看</el-button>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="warehouseStatus" label="仓库状态" v-if="type!=='init'"></el-table-column>
    <el-table-column align="center" prop="transferStatus" label="发货状态" width="100" v-if="type!=='init'"></el-table-column>
    <el-table-column align="center" prop="deliveryStatus" label="签收状态" width="180" v-if="type!=='init'"></el-table-column>
    <el-table-column align="center" prop="factory" label="工厂" width="240">
      <template slot-scope="{row}">
        <span>{{row.factoryCode}}{{row.factory}}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="customerMaterialNo" label="客户物料号" width="100" v-if="!sto"></el-table-column>
    <el-table-column align="center" prop="customerOrderLineNo" label="客户行号" width="100" v-if="!sto"></el-table-column>
    <el-table-column align="center" prop="customerMaterialName" label="客户物料名称" width="100" v-if="!sto"></el-table-column>
    <el-table-column align="center" prop="customerMaterialQuantity" label="客户物料数量" width="100" v-if="!sto"></el-table-column>
    <el-table-column align="center" prop="customerMaterialUnit" label="客户物料单位" width="100" v-if="!sto"></el-table-column>
    <el-table-column align="center" prop="position" label="库存地点" width="150">
      <template slot-scope="{row}">
        <span>{{row.positionCode}}{{row.position}}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="roughWeight" label="毛重" width="180"></el-table-column>

    <el-table-column align="center" prop="roughUnit" label="毛重单位"></el-table-column>
    <el-table-column align="center" prop="volume" label="容量" width="100"></el-table-column>
    <el-table-column align="center" prop="volumeUnit" label="容量单位" width="180"></el-table-column>
    <el-table-column align="center" prop="appointSapBatch" label="SAP批次" width="180"></el-table-column>

    <el-table-column align="center" prop="memo" label="跟单备注" width="180">
      <template slot-scope="{ row }">
        <el-input size="mini" v-model="row.memo" v-if="type!=='detail'&&status!=='CONFIRM'" plcaeholder="请输入备注信息" />
        <span v-else>{{row.memo}}</span>
      </template>
    </el-table-column>
  </el-table>
  <el-dialog
    title="编辑发货详情"
    :visible.sync="showEditDeliveryDetail"
    width="600px"
    :destroy-on-close="true"
  >
    <el-table
      :data="detailData.data.filter(item=>item.operationType!==3)"
      style="width: 100%"
    >
      <el-table-column align="center" type="index" prop="rowNo" label="序号"></el-table-column>
      <el-table-column align="center" prop="wmsBatch" label="SKU批次号">
        <template slot-scope="{row}">
          <el-input
            v-model="row.wmsBatch"
            size="mini"
            :style="{width: '150px'}"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" prop="deliveryAmount" label="交货数量">
        <template slot-scope="{row}">
          <el-input-number
            v-model.number="row.deliveryAmount"
            size="mini"
            :style="{width: '150px'}"
            :min="0"
            :precision="3"
            :step="1"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" prop="operation" label="操作">
        <template slot-scope="{$index}">
          <el-button v-if="true" type="text" size="mini" @click="handleDel($index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="dn-row">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleAdd">新增</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
  <el-dialog
    title="SKU尺寸"
    :visible.sync="showSKUDetail"
    width="600px"
    :destroy-on-close="true"
  >
    <el-table
      :data="skuDetailData"
      style="width: 100%"
    >
      <el-table-column align="center" prop="weight" label="每件净重(Kg)"></el-table-column>
      <el-table-column align="center" prop="sellGrossWeight" label="每件毛重(Kg)"></el-table-column>
      <el-table-column align="center" prop="length" label="长(CM)"></el-table-column>
      <el-table-column align="center" prop="width" label="宽(CM)"></el-table-column>
      <el-table-column align="center" prop="height" label="高(CM)"></el-table-column>
    </el-table>
  </el-dialog>
  <DeliveryDetailDlg :dialogVisible.sync="showDeliveryDetail" :tableData="detailData.data" />
</div>
</template>

<script>
import * as shortid from 'shortid'
import { getDNBackDetail } from '@/api/orderDelivery'
import { isNumber } from '@/utils'
import TooltipNo from '../TooltipNo'
import { mdmProductList } from '@/api/ifc.js';
import DeliveryDetailDlg from './DeliveryDetailDlg.vue'

export default {
  props: ['data', 'isLoading', 'disabled', 'type', 'status', 'sto', 'setFastenerLogo'],
  components: {
    TooltipNo,
    DeliveryDetailDlg
  },
  data () {
    return {
      detailData: {
        data: []
      },
      expandRow: [],
      showDeliveryDetail: false,
      showEditDeliveryDetail: false,
      isDetailLoading: false,
      showSKUDetail: false,
      skuDetailData: []
    }
  },
  watch: {
    data (newVal, oldVal) {
      if (newVal.every(item => item.fastenerLogo === true)) {
        this.setFastenerLogo && this.setFastenerLogo('ALL')
      } else if (newVal.every(item => item.fastenerLogo !== true)) {
        this.setFastenerLogo && this.setFastenerLogo('NOPE')
      } else {
        this.setFastenerLogo && this.setFastenerLogo('SOME')
      }
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  methods: {
    handleRouter (param) {
      const { type, no, row } = param
      const soNo = type === 'oms' ? no : ''
      const sapOrderNo = type === 'sap' ? no : ''
      this.$router.jumpToSoOrderDetail({
        query: {
          soNo: soNo || row?.referOrderNo,
          sapOrderNo: sapOrderNo || row?.sapOrderNo,
          id: shortid.generate(),
          refresh: true
        }
      })
    },
    formatQuantityUnit (value) {
      if (value && isNumber(value)) {
        const unit = this.dictList['quantityUnit'].find(item => ('' + item.code) === value)
        if (unit && unit.name) {
          return unit.name
        }
      }
      return value
    },
    getOrderNoTooltip (item) {
      return item ? [
        {
          title: 'OMS订单号',
          no: item.referOrderNo ? item.referOrderNo.split(',') : [''],
          itemNo: item.referOrderItemNo,
          itemDetailNo: item.referOrderItemDetailNo,
          type: 'oms',
          row: item
        },
        {
          title: 'SAP订单号',
          no: item.sapOrderNo ? item.sapOrderNo.split(',') : [''],
          itemNo: item.sapOrderItemNo,
          type: 'sap',
          row: item
        },
        { title: '客户订单号', no: item.customerReferenceNo ? item.customerReferenceNo.split(',') : [''] },
        { title: '外围订单号', no: item.orderNo ? item.orderNo.split(',') : [''] }
      ] : []
    },
    handleCancel () {
      this.showEditDeliveryDetail = false
    },
    handleAdd () {
      this.detailData.data.push({
        operationType: 1
      })
    },
    handleDel (idx) {
      const item = this.detailData.data[idx]
      if (item.operationType === 1) {
        this.$delete(this.detailData.data, idx)
      } else {
        this.$set(this.detailData.data, idx, {
          ...item,
          operationType: 3
        })
      }
    },
    handleOk () {
      const { dnNo, dnItemNo, lfimg } = this.detailData.detail
      const t = this.detailData && this.detailData.data ? this.detailData.data.reduce((total, item) => {
        return item.operationType !== 3 ? total + item.deliveryAmount : total
      }, 0) : 0
      if (!this.detailData.data || this.detailData.data.length === 0) {
        this.$alert('交货数量不能为空！', '错误')
      } else if (t !== lfimg) {
        this.$alert('交货数量不一致！', '错误')
      } else {
        const batchInfos = this.detailData.data.map(item => {
          return {
            dnNo,
            dnItemNo,
            ...item
          }
        })
        this.detailData.detail.batchInfos = batchInfos
        this.showEditDeliveryDetail = false
      }
    },
    async getDNBackDetail (row, callback) {
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      this.detailData = {
        detail: {},
        data: []
      }
      const res = await getDNBackDetail({
        dnItemNo: row.dnItemNo,
        dnNo: row.dnNo
      })
      loading.close()
      if (res.code !== 200 && res.msg) {
        this.$alert(res.msg, '错误')
      } else if (res.code === 200 && callback) {
        callback(res.data)
      }
    },
    handleEdit (row) {
      if (!row.batchInfos || row.batchInfos.length === 0) {
        this.getDNBackDetail(row, (data) => {
          this.showEditDeliveryDetail = true
          this.detailData = {
            detail: row,
            data: data.map(item => Object.assign({ operationType: 2 }, item))
          }
        })
      } else {
        this.showEditDeliveryDetail = true
        this.detailData = {
          detail: row,
          data: row.batchInfos
        }
      }
    },
    async handleClick (row) {
      this.getDNBackDetail(row, (data) => {
        this.showDeliveryDetail = true
        this.detailData = {
          detail: row,
          data: data.map(item => Object.assign({}, item, { operationType: 2 }))
        }
      })
    },
    showSkuDialog (row) {
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const params = {
        skuCode: row.sku,
        ifAttr: true
      }
      mdmProductList(params).then(res => {
        this.showSKUDetail = true;
        if (res.data) {
          this.skuDetailData = [res.data]
        } else {
          this.skuDetailData = []
        }
      }).finally(() => {
        loading.close()
      })
    },
    handleSelectionChange (val) {
      this.$emit('selectionChange', val)
    },
    quantityChange (val, idx) {
      this.$emit('quantityChange', val, idx)
    }
  }
}
</script>

<style scoped lang="scss">
.dn-row {
  margin: 10px 0;
  text-align: center;
}
</style>
