<template>
  <div class="upload-container">
    <div>
      <el-button class="dn-mini-bn" size="mini" type="primary" :disabled="disabled" @click="dialogVisible=true">
        添加文件
      </el-button>
      <span class="el-upload__tip" style="margin-left: 10px">{{notice}}</span>
    </div>
    <el-table
      border
      fit
      :data="attachments"
      style="width: 100%;margin-top: 10px"
    >
      <el-table-column
        type="index"
        label="序号"
        align="center"
        width="50">
      </el-table-column>
      <el-table-column
        prop="name"
        label="附件名称"
        align="center"
        width="500"
      >
        <template slot-scope="{row}">
          <a :href="row.attachmentUrl" target="_blank">{{row.name}}</a>
        </template>
      </el-table-column>
      <el-table-column
        prop="tag"
        align="center"
        label="标签">
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
      >
        <template slot-scope="{row, $index}">
          <el-button v-if="true" type="text" size="mini" @click="handleRemove($index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      title="添加附件"
      :visible.sync="dialogVisible"
      width="600px"
      :before-close="handleClose"
      :destroy-on-close="true"
    >
      <el-form ref="form" :model="uploadData" label-width="80px" size="mini">
        <el-form-item label="上传文件">
          <el-upload
            ref="upload"
            action="/ali/upload"
            accept=".doc, .docx, .ppt, .pptx, .xls, .xlsx, .pdf, image/jpg, image/jpeg, image/png"
            :before-upload="$validateFileType"
            style="display: inline-block"
            drag
            :limit="1"
            :show-file-list="true"
            :multiple="false"
            :disabled="disabled"
            :http-request="httpRequestHandle"
            :on-remove="handleRemoveUpload"
            :on-exceed="handleExceed"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">{{notice}}</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="附件名称">
          <el-input v-model="uploadData.name" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <el-input v-model="uploadData.tag" :disabled="disabled"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSubmit">确认</el-button>
          <el-button @click="handleCancel">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { uploadFile } from '@/api/ecorp'
const fileSizeLimit = 10

export default {
  props: ['data', 'disabled'],
  data () {
    return {
      fileSizeLimit, // 上传文件大小限制，单位 MB
      notice: `只能上传doc/docx/ppt/pptx/xls/xlsx/pdf/jpg/jpeg/png文件，且不超过${fileSizeLimit}MB`,
      uploadData: {
        tag: '',
        name: ''
      },
      activeIndex: -1,
      attachments: this.data || [],
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  methods: {
    mouseOver (idx) {
      this.activeIndex = idx
    },
    // 移出
    mouseLeave (idx) {
      this.activeIndex = -1
    },
    handleRemove (idx) {
      this.attachments.splice(idx, 1)
    },
    handleRemoveUpload () {
      this.uploadData.name = ''
    },
    handleExceed () {
      this.$message.warning({
        message: '一次只能上传一个文件'
      })
    },
    handleClose () {
      this.dialogVisible = false
      this.uploadData = {
        name: '',
        tag: '',
        attachmentUrl: ''
      }
    },
    handleCancel () {
      this.dialogVisible = false
      this.uploadData = {
        name: '',
        tag: '',
        attachmentUrl: ''
      }
    },
    handleSubmit () {
      this.dialogVisible = false
      const { name, tag, attachmentUrl } = this.uploadData
      this.attachments.push({ attachmentUrl, name, tag })
      this.$emit('upload', this.attachments)
      this.uploadData = {
        name: '',
        tag: '',
        attachmentUrl: ''
      }
    },
    handlePictureCardPreview (file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    // 上传文件
    httpRequestHandle (file) {
      // 校验大小
      const isGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit
      if (isGtLimit) {
        this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
        return
      }

      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      this.uploadData.name = file.file.name
      const formData = new FormData()
      formData.append('file', file.file)
      uploadFile(formData)
        .then(response => {
          if (response && response.length > 0) {
            response.forEach(item => {
              if (item.bucketName && item.objectKey) {
                this.uploadData.attachmentUrl = `http://${item.bucketName}.oss-cn-hangzhou.aliyuncs.com/${item.objectKey}`
              }
            })
          }
          loading.close()
        })
        .catch(e => {
          this.$message.error('文件上传失败，请重新尝试')
          loading.close()
        })
    }
  },
  watch: {
    data (val) {
      this.attachments = val || []
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../styles/common.scss";

.upload-container {
  .upload-row {
    display: flex;
    height: 44px;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    .delete-icon {
      cursor: pointer;
      width: 40px;
      visibility: hidden;
    }
  }
  .upload-active {
    background: #e2e2e2;
    .delete-icon {
      visibility: visible;
    }
  }
}
</style>
