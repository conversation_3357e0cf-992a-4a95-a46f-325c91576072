<template>
<div class="field-container">
  <el-form
    :model="deliveryInfo"
    :rules="rules"
    size="mini"
    label-position="right"
    ref="deliveryBaseForm"
    class="delivery-form-inline"
    label-width="160px"
  >
    <template v-if="!isHideContactFields">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="收货联系人" prop="contactId">
            <el-select
              v-model="deliveryInfo.contactId"
              placeholder="选择收货联系人"
              filterable
              remote
              reserve-keyword
              style="width:100%"
              :remote-method="queryContactList"
              :loading="loadingContact"
              :disabled="disabledContact"
              @change="changeContact"
            >
              <el-option
                v-for="(item, index) in contactList"
                :key="item.contactId"
                :label="item.contactName"
                :value="item.contactId"
                :disabled="index===0"
              >
                <div
                  class="ba-row-start selectClientItem"
                  :style="{fontWeight:index===0?'bold':'normal'}"
                >
                  <div>{{ item.contactName }}</div>
                  <div>{{ item.mobilephone || item.telephone || '--' }}</div>
                  <div>{{ item.address || '--' }}</div>
                </div>
              </el-option>
              <el-option style="color:#ccc" disabled :value="-1" v-show="contactList && contactList.length===0">
                无数据
              </el-option>
              <el-option style="color:#ccc" disabled :value="-1" v-show="contactList && contactList.length >=20">
                已展示部分联系人，其他联系人请输入字符进行查询
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="收货电话" prop="phone">
            <el-input :value="deliveryInfo.phone||'--'" placeholder="请输入收货电话" style="width:100%" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="领用人" prop="demandUser">
            <el-input v-model="deliveryInfo.demandUser" placeholder="请输入领用人" style="width:100%" :disabled="disabledContact" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="收货地址" prop="address">
            <el-input :value="deliveryInfo.address||'--'" placeholder="请输入收货地址" style="width:100%" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label=" ">
            <el-checkbox v-model="deliveryInfo.customerDeliveryConfirmed" true-label="X" false-label="Z" disabled>发货需与客户确认/预约</el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <el-row :gutter="20" v-for="(fieldList,rowIdx) in fields" :key="'row'+rowIdx" v-show="!fold">
      <el-col
        v-for="(field,itemIdx) in fieldList"
        :key="'item'+itemIdx"
        :span="field.span"
      >
        <el-form-item v-if="field.type==='checkbox'" label=" ">
          <el-checkbox
            v-model="deliveryInfo[field.prop]"
            :disabled="field.disabled===true"
          >
            {{field.title}}
          </el-checkbox>
        </el-form-item>
        <template v-else-if="field.category==='order-service'">
          <SelectOrderService
            :dictName="field.dictName"
            :disabledSelect="fastenerLogoDisabledList && fastenerLogoDisabledList[field.prop] || field.disabled"
            :selectStyle="`width:100%`"
            v-model="deliveryInfo[field.prop]"
            :defaultLabel="field.title"
            :isForceReset="field.forceReset"
            :isMultiple="field.multiple"
            :field="field.prop"
            :limit="field.limit"
            :clearable="field.clearable"
          />

        </template>
        <el-form-item :label="field.title" :prop="field.prop" v-else>
          <template v-if="field.type==='input'">
            <el-input v-model="deliveryInfo[field.prop]"
              :placeholder="'请输入'+field.title"
              :disabled="field.disabled===true"
              style="width:100%"
            />
          </template>
          <template v-if="field.type==='textarea'">
            <el-input type="textarea"
              style="width:100%"
              :rows="3"
              v-model="deliveryInfo[field.prop]"
              :disabled="field.disabled===true"
              :placeholder="'请输入'+field.title"
            />
          </template>
          <template v-if="field.type==='date'">
            <el-date-picker v-model="deliveryInfo[field.prop]"
              value-format="yyyy-MM-dd"
              type="date"
              :placeholder="'请选择'+field.title"
              :disabled="field.disabled===true"
              style="width:100%"
            />
          </template>
          <template v-if="field.type==='select'">
            <div v-if="field.disabled && field.prop==='deliveryRequirements'">
              {{ deliveryInfo[field.prop] }}
            </div>
            <el-select
              v-else
              v-model="deliveryInfo[field.prop]"
              :placeholder="'请选择'+field.title"
              :disabled="field.disabled===true"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in dictList[field.dictKey]"
                :key="item.code"
                :label="item.name"
                :value="item.code"
                :disabled="item.status==='stop'"
              />
            </el-select>
          </template>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</div>
</template>

<script>
import { isEmpty } from 'lodash'
import { searchContactListByGroup } from '@/api/orderSale'
import { contactHeader } from '@/pages/orderSale/constants'
import SelectOrderService from '@/pages/orderSale/components/common/SelectOrderService'

export default {
  components: {
    SelectOrderService
  },
  props: [
    'data', 'contact', 'fields', 'disabledContact', 'customerNo',
    'fold', 'isHideContactFields', 'isGenerateDataByFields', 'fastenerLogoDisabledList'
  ],
  data () {
    return {
      deliveryInfo: {},
      loadingContact: false,
      contactList: [],
      rules: {
        contactName: [
          { required: true, message: '请选择客户', trigger: 'blur' }
        ],
        deliveryRequireTime: [
          { required: true, message: '请选择计划发货时间', trigger: 'blur' }
        ],
        deliveryTime: [
          { required: true, message: '请选择实际发货日期', trigger: 'blur' }
        ],
        deliveryUnloadingReq: [
          { required: false, message: '请选择送货卸货要求', trigger: 'blur' }
        ],
        packagingReq: [
          { required: true, message: '请选择包装要求', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    if (!Object.keys(this.dictList).length) {
      this.$store.dispatch('orderCommon/queryDictList')
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    }
  },
  watch: {
    data (val) {
      if (this.isGenerateDataByFields) {
        if (this.fields.length) {
          const result = {};
          const fields = [];
          this.fields.forEach((rows) => {
            rows.forEach(row => {
              fields.push(row.prop);
            })
          });
          (fields || []).forEach(field => {
            result[field] = val[field]
          })
          this.deliveryInfo = {
            ...this.deliveryInfo,
            ...result,
            demandUser: val?.demandUser,
            customerDeliveryConfirmed: val?.customerDeliveryConfirmed
          }
        }
      } else {
        this.deliveryInfo = val
      }
      console.log(this.deliveryInfo)
    },
    contact (value) {
      console.log(value);
      if (!value) {
        return
      }
      const { contactName, contactPhone, mobilephone, telephone, address, contactId } = value
      if (contactName) {
        this.contactList = [
          contactHeader,
          { contactName, mobilephone, address, contactId }
        ]
        this.deliveryInfo = {
          ...this.deliveryInfo,
          contactName,
          address,
          contactId,
          phone: contactPhone || mobilephone || telephone
        }
      }
    },
    'deliveryInfo.disableShipping' (val) {
      if (!isEmpty(val)) {
        this.deliveryInfo.designatedShipping = []
      }
    },
    'deliveryInfo.designatedShipping' (val) {
      if (!isEmpty(val)) {
        this.deliveryInfo.disableShipping = []
      }
    }
  },
  methods: {
    getDictName (code, dictKey) {
      if (code) {
        const foundItem = this.dictList[dictKey].find(item => item.code === code)
        if (foundItem) {
          return foundItem.name
        }
      }
      return ''
    },
    changeContact (val) {
      const contact = this.contactList.find(item => item.contactId === val)
      if (contact) {
        const { telephone, mobilephone, address } = contact
        // 电话
        this.deliveryInfo.phone = mobilephone || telephone
        // 地址
        this.deliveryInfo.address = address
      }
    },
    queryContactList (contactName) {
      this.loadingContact = true
      const { distributionChannel, productGroup, salesOrganization } = this.data
      if (this.customerNo) {
        searchContactListByGroup({
          distributionChannel,
          productGroup,
          salesOrganization,
          contactName,
          customerCode: this.customerNo
        }).then(res => {
          this.loadingContact = false
          if (res && res.code === 200) {
            const { records } = res.data
            this.contactList = records && records.length > 0 ? [
              contactHeader,
              ...records
            ] : []
          }
        })
      }
    },
    submit (formName, isDraft, addSubmitFlag) {
      this.$emit('submit', this.deliveryInfo, isDraft, addSubmitFlag)
    }
  }
}
</script>

<style scoped lang="scss">
.field-container {
  padding: 0 10px;
  font-size: 14px;
  color: rgb(96, 98, 102);
  .el-row {
    margin-bottom: 10px;
  }
  .field-label {
    color: #8c8c8c;
  }
  .field-value {
    color: #262626;
  }
}
.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
  }
}
</style>
