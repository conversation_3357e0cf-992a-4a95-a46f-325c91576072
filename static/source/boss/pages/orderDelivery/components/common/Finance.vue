<template>
<div class="field-container">
  <el-form
    :model="financeInfo"
    :rules="rules"
    size="mini"
    label-position="right"
    ref="financeBaseForm"
    class="delivery-form-inline"
    label-width="110px"
  >
    <el-row :gutter="20" v-for="(fieldList,rowIdx) in fields" :key="'row'+rowIdx" v-show="rowIdx<2||!fold">
      <el-col
        v-for="(field,itemIdx) in fieldList"
        :key="'item'+itemIdx"
        :span="field.span"
      >
        <el-form-item :prop="field.prop" v-if="field.type==='checkbox'" label=" ">
          <el-checkbox
            v-model="financeInfo[field.prop]"
            :disabled="field.disabled===true"
          >
            {{field.title}}
          </el-checkbox>
        </el-form-item>
        <el-form-item :label="field.title" :prop="field.prop" v-else>
          <template v-if="field.type==='input'">
            <el-input v-model="financeInfo[field.prop]"
              :placeholder="'请输入'+field.title"
              :disabled="field.disabled===true"
              style="width:100%"
            />
          </template>
          <template v-if="field.type==='textarea'">
            <el-input type="textarea"
              style="width:100%"
              :rows="3"
              v-model="financeInfo[field.prop]"
              :placeholder="'请输入'+field.title"
              :disabled="field.disabled===true"
            />
          </template>
          <template v-if="field.type==='date'">
            <el-date-picker v-model="financeInfo[field.prop]"
              :disabled="field.disabled===true"
              value-format="yyyy-MM-dd"
              type="date"
              :placeholder="'请选择'+field.title"
              style="width:100%"
            />
          </template>
          <template v-if="field.type==='select'">
            <el-select
              :disabled="field.disabled===true"
              v-model="financeInfo[field.prop]"
              :placeholder="'请选择'+field.title"
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in dictList[field.dictKey]"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </template>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</div>
</template>

<script>
import { searchContactList } from '@/api/orderSale'
import { contactHeader } from '@/pages/orderSale/constants'

const defaultRules = {
}

export default {
  props: ['data', 'contact', 'fields', 'fold', 'orderType'],
  data () {
    return {
      financeInfo: this.data || {},
      loadingContact: false,
      contactList: []
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    rules () {
      return (this.orderType === 'Z008' || this.orderType === 'Z009')
        ? {} : defaultRules
    }
  },
  watch: {
    data (val) {
      this.financeInfo = val
    },
    contact (value) {
      if (!value) {
        return
      }
      const { contactName, mobilephone, telephone, address, contactId } = value
      if (contactName) {
        this.contactList = [
          contactHeader,
          { contactName, mobilephone, address, contactId }
        ]
        this.financeInfo = {
          ...this.financeInfo,
          contactName,
          address: address || '--',
          phone: mobilephone || telephone || '--'
        }
      }
    }
  },
  methods: {
    getDictName (code, dictKey) {
      if (code) {
        const foundItem = this.dictList[dictKey].find(item => item.code === code)
        if (foundItem) {
          return foundItem.name
        }
      }
      return ''
    },
    changeContact (val) {
      const { telephone, address } = val
      // 电话
      this.financeInfo.phone = telephone || '--'
      // 地址
      this.financeInfo.address = address || '--'
    },
    queryContactList (contactName) {
      this.loadingContact = true
      if (this.customerNo) {
        searchContactList(this.customerNo, contactName).then(res => {
          this.loadingContact = false
          if (res && res.code === 200) {
            this.contactList = [
              contactHeader,
              ...res.data.records
            ]
          }
        })
      }
    },
    submit (formName, isDraft) {
      this.$emit('submit', this.financeInfo, isDraft)
    }
  }
}
</script>

<style scoped lang="scss">
.field-container {
  padding: 0 10px;
  font-size: 14px;
  color: rgb(96, 98, 102);
  .el-row {
    margin-bottom: 10px;
  }
  .field-label {
    color: #8c8c8c;
  }
  .field-value {
    color: #262626;
  }
}
</style>
