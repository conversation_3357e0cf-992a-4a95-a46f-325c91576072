<template>
<div class="field-container" :class="{ padding: type==='edit' }">
  <el-row :gutter="20" v-for="(fieldList,rowIdx) in formatFieldlist(fields)" :key="'row'+rowIdx">
    <el-col
      v-for="(field,itemIdx) in fieldList"
      :key="'item'+itemIdx"
      :span="field.span"
    >
      <span class="field-label">{{field.title}}：</span>
      <span class="field-value">
        <template v-if="field.type==='text'">
          {{ data ? data[field.prop] : '' }}
        </template>
        <template v-if="field.prop==='isSpecialOrder'">
          <el-tag v-if="isSpecialOrder" class="special-order" @click="showSpOrder">是</el-tag>
        </template>
        <el-button
          v-if="field.prop==='sapDeliveryNo'&&data&&data['sapSyncStatusCode']!=='00'&&!isZRE3&&sapBtnFlg!=='sap'"
          type="primary" size="mini"
          @click="handleSync" plain
        >
          同步SAP
        </el-button>
        <el-checkbox v-if="field.type==='checkbox'" label="" :value="data ? data[field.prop] : false" disabled>
        </el-checkbox>
        <template v-if="field.type==='dict'">
          {{ getDictName(data[field.prop], field.dictKey) }}
        </template>
      </span>
    </el-col>
  </el-row>
  <SpecialOrder
    :visible.sync="visible"
    :source="source"
    />
</div>
</template>

<script>
import { BaseinfoFields, BaseinfoStoFields } from '../../constants'
import { compensateToSap } from '@/api/orderDelivery'
import SpecialOrder from '@/pages/directOrder/specialOrder'
import { requestWithLoading } from '../../utils'
import { queryDeliveryNos } from '@/api/directOrder.js'

export default {
  props: ['data', 'statusDesc', 'type', 'sto', 'onSync', 'sapBtnFlg'],
  components: { SpecialOrder },
  data () {
    return {
      isSpecialOrder: false,
      visible: false,
      source: {},
      detail: null
    }
  },
  computed: {
    fields() {
      return this.sto ? BaseinfoStoFields : BaseinfoFields
    },
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    isZRE3 () {
      return this.data.referOrderType === 'ZRE3'
    }
  },
  created () {
    queryDeliveryNos({
      deliveryNos: [this.$route.params.id],
      type: 'DN'
    }).then(res => {
      console.log(res)
      if (res.code === 200 && res.success === true && Array.isArray(res.data) && res.data.length) {
        const data = res.data[0]
        console.log(data)
        if (data.isSpecialDeliveryOrder) {
          this.isSpecialOrder = true
          this.detail = data
        }
      }
    })
  },
  methods: {
    showSpOrder () {
      this.source = this.detail || {}
      const { isSpecialOrder, isSpecialCustomer, isSpecialProduct } = this.detail || {}
      if (isSpecialOrder || isSpecialCustomer || isSpecialProduct) {
        this.visible = true
      }
    },
    formatFieldlist (fields) {
      if (this.data['positionOwner'] === 'kh') {
        return fields
      }
      const filteredFields = fields.map(item => item.filter(p => p.prop !== 'desc'));
      return filteredFields
    },
    getDictName (code, dictKey) {
      if (code && this.dictList && this.dictList[dictKey]) {
        const foundItem = this.dictList[dictKey].find(item => item.code === code)
        if (foundItem) {
          return foundItem.name
        }
      }
      return ''
    },
    handleSync () {
      const { deliveryNo } = this.data
      if (deliveryNo) {
        requestWithLoading(this, compensateToSap(deliveryNo), result => {
          this.$emit('afterAsync')
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.field-container {
  font-size: 14px;
  color: rgb(96, 98, 102);
  padding: 0 10px;
  .el-row {
    margin-bottom: 20px;
  }
  .field-label {
    color: #8c8c8c;
  }
  .field-value {
    color: #262626;
  }
  .special-order{
    cursor: pointer;
  }
}
.padding {
  padding: 0 40px;
}
</style>
