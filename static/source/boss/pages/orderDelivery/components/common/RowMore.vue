<template>
<div class="row-more" @click="handleToggle">
  查看更多
  <i :class="foldStatus?'el-icon-arrow-down':'el-icon-arrow-up'" />
</div>
</template>

<script>

export default {
  props: ['fold'],
  components: {
  },
  data () {
    return {
      foldStatus: this.fold == null ? true : this.fold
    }
  },
  computed: {
  },
  methods: {
    handleToggle () {
      this.foldStatus = !this.foldStatus
      this.$emit('fold', this.foldStatus)
    }
  }
}
</script>

<style scoped lang="scss">
.row-more {
  margin-right: 10px;
  color: #597bee;
  cursor: pointer;
}
</style>
