<template>
<el-form ref="form" :model="soAddData" :rules="rules" label-width="100px">
  <el-form-item label="OMS订单号" prop="soNo">
    <el-input v-model="soAddData.soNo" placeholder="请输入OMS订单号"></el-input>
  </el-form-item>
  <div style="margin-bottom: 10px">请输入有效OMS订单，输入后会直接将对应订单内所有和当前DN相同装运点、领用人，并且可做DN交货的计划行添加至行项目列表中</div>
  <el-form-item>
    <el-button type="primary" @click="handleAdd">立即创建</el-button>
    <el-button @click="handleCancel">取消</el-button>
  </el-form-item>
</el-form>
</template>

<script>
import { queryDNByGroupV2 } from '@/api/orderDelivery'
import { requestWithLoading } from '../../utils'

export default {
  props: ['data', 'dnData', 'deliveryType'],
  components: {
  },
  data () {
    return {
      soAddData: {
        soNo: ''
      },
      rules: {
        soNo: [
          { required: true, message: 'OMS订单号不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    async handleAdd () {
      const { soNo } = this.soAddData
      const basicInfo = this.dnData ? this.dnData.basicInfo : {}
      const { referOrderType = '', soBusinessType = '', hideLogo = '', orderBasis = '' } = basicInfo
      let { disableShipping = '', designatedShipping = '' } = this.dnData?.deliveryInfo || {}
      try {
        disableShipping = Array.isArray(disableShipping) ? disableShipping.join(',') : disableShipping
        designatedShipping = Array.isArray(designatedShipping) ? designatedShipping.join(',') : designatedShipping
      } catch (error) {
        console.log(error);
      }
      if (soNo && this.data && this.data.length > 0) {
        let { deliveryShippingLocation, deliveryWarehouseCode, demandUser, shippingLocation, factoryCode, itemType, warehouseRuleCode, referOrderNo } = this.data[0]
        demandUser = demandUser || ''
        shippingLocation = shippingLocation || ''
        const params = {
          deliveryType: this.deliveryType,
          deliveryShippingLocation,
          deliveryWarehouseCode,
          itemType,
          factoryCode,
          demandUser,
          shippingLocation,
          referOrderType,
          soBusinessType,
          hideLogo,
          soNo,
          warehouseRuleCode,
          disableShipping,
          designatedShipping,
          orderBasis,
          oldSoNo: referOrderNo
        }
        let queryApi = queryDNByGroupV2
        requestWithLoading(this, queryApi(params), async (item) => {
          const { groupedDnItems, epidemicSituationFlag, msg } = item
          if (!groupedDnItems || Object.keys(groupedDnItems).length === 0) {
            this.$alert(msg || '该订单暂无可合并创建的计划行哦~', '错误', {
              type: 'error'
            })
          } else {
            // 创建交货单前的提示
            if (msg) {
              try {
                await this.$confirm(msg, '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
              } catch (err) {
                console.log(err)
                return;
              }
            }
            // const groups = groupedDnItems[`${factoryCode2Group}_${shippingLocation}_${itemType}_${demandUser || ''}_${deliveryShippingLocation || ''}_${warehouseRuleCode || ''}`]
            // if (groups && groups.length > 0) {
            //   this.$emit('submit', groups, epidemicSituationFlag)
            // } else {
            //   this.$alert('对应订单内不存在和当前DN相同装运点、领用人的计划行哦~', '错误', {
            //     type: 'error'
            //   })
            // }
            // 新增行的时候后端已经过滤过一遍，所以前端不用过滤了 2023.5.18
            const groups = [].concat(...Object.values(groupedDnItems))
            if (groups && groups.length > 0) {
              this.$emit('submit', groups, epidemicSituationFlag)
            }
          }
        })
      }
    },
    handleCancel () {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped lang="scss">
</style>
