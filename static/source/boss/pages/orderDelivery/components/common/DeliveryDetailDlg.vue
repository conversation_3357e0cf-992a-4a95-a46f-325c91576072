<template>
  <el-dialog
    title="发货详情"
    :visible.sync="showDialog"
    width="800px"
    :destroy-on-close="true"
  >
    <el-table
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column align="center" type="index" prop="rowNo" label="序号"></el-table-column>
      <el-table-column align="center" prop="wmsBatch" label="SKU批次号"></el-table-column>
      <el-table-column align="center" prop="supplierBatchList" label="供应商批次号">
        <template slot-scope="{row}">
          {{ (row.supplierBatchList || []).join(',') }}
        </template>"
      </el-table-column>
      <el-table-column align="center" prop="deliveryAmount" label="交货数量"></el-table-column>
      <el-table-column align="center" prop="gmtModify" label="执行时间"></el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    }
  }
}
</script>

<style>

</style>
