<template>
  <el-dialog
    title="包装信息"
    :visible.sync="showDialog"
    width="800px"
    :destroy-on-close="true"
  >
    <el-table
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column align="center" prop="traceType" label="包装类型"></el-table-column>
      <el-table-column align="center" prop="weight" label="包裹重量(Kg)"></el-table-column>
      <el-table-column align="center" prop="cubic" label="包裹体积(m³)"></el-table-column>
      <el-table-column align="center" prop="length" label="包裹-长(cm)"></el-table-column>
      <el-table-column align="center" prop="width" label="包裹-宽(cm)"></el-table-column>
      <el-table-column align="center" prop="height" label="包裹-高(cm)"></el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    showDialog: {
      get() {
        console.log(this.tableData)
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    }
  }
}
</script>

<style>

</style>
