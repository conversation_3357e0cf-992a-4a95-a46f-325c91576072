<template>
  <el-dialog
    title="调度信息"
    :visible.sync="showDialog"
    width="800px"
    :destroy-on-close="true"
  >
    <el-table
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column align="center" prop="carrierType" label="配送类型">
        <template slot-scope="scope">
          <p v-if="scope.row.carrierType">{{ scope.row.carrierType === 'Y' ? '自营配送' :  '第三方'}}</p>
        </template>
      </el-table-column>
      <el-table-column
        v-for="column in columns"
        align="center"
        :prop="column.prop"
        :label="column.label"
        :width="column.width"
        :key="column.prop"
      >
        <template slot-scope="scope">
          <span v-if="column.prop === 'packageNo'">{{ formatPackageInfos(scope.row) }}</span>
          <span v-else>{{ scope.row[column.prop] }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
const columns1 = [
  { label: '承运商', prop: 'carrierName' },
  { label: '快递单号', prop: 'packageNo' }
]
const columns2 = [
  { label: '自营车辆车牌号', prop: 'vehicleId', width: '150px' },
  { label: '车型', prop: 'vehicleType', width: '150px' },
  { label: '司机姓名', prop: 'driverName', width: '150px' },
  { label: '司机手机号', prop: 'driverTel', width: '150px' }
]

export default {
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.dialogVisible
      },
      set(val) {
        this.$emit('update:dialogVisible', val)
      }
    },
    columns () {
      if (this.tableData.length > 0) {
        return this.tableData[0].carrierType === 'Y' ? columns2 : columns1
      }
      return columns1
    }
  },
  methods: {
    formatPackageInfos (row) {
      return (row.packageInfos || []).map(pa => pa.traceId)?.join(',')
    }
  }
}
</script>

<style>

</style>
