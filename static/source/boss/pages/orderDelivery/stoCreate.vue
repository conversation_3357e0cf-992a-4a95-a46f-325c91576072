<template>
  <div class="dn-edit-container">
    <div class="dn-edit-header">
      <span class="dn-title">创建交货单</span>
      <div>
        <el-button class="dn-normal-bn" type="primary" @click="handleSubmit">确认创建</el-button>
      </div>
    </div>
    <div class="dn-row-title">
      基础信息
    </div>
    <div class="dn-container">
      <BaseInfo
        ref="baseInfo"
        :sto="true"
        :data="dnData.basicInfo||{}"
      />
    </div>
    <div class="dn-row-title">
      交货信息
      <RowMore @fold="handleDeliveryFold" />
    </div>
    <Delivery
      ref="deliveryInfo"
      :data="dnData.deliveryInfo"
      :customerNo="customerNo"
      :contact="contact"
      :disabledContact="false"
      :fields="deliveryFields"
      :fold="foldDelivery"
      @submit="submitDelivery"
    />
    <div class="dn-row-title">
      财务信息
      <RowMore @fold="handleFinanceFold" />
    </div>
    <Finance
      ref="financeInfo"
      :data="dnData.financeInfo"
      :fields="financeFields"
      :fold="foldFinance"
      :orderType="dnData.basicInfo?dnData.basicInfo.referOrderType:''"
      @submit="submitFinance"
    />
    <div class="dn-row-title">行项目明细</div>
    <div class="dn-row-btn-list">
      <div>
        <el-button class="dn-mini-bn" type="primary" @click="handleOpenCreateDlg" size="mini">新增</el-button>
        <el-button class="dn-mini-bn" @click="handleBatchDelete" :disabled="selectedItemList.length===0" size="mini">批量删除</el-button>
      </div>
    </div>
    <DetailTable
      type="init"
      :data="dnData.items"
      :isLoading="isLoading"
      :status="dnData.status"
      @selectionChange="handleSelectionChange"
      @quantityChange="handleQuantityChange"
    />
    <div class="pagination-container">
      <pagination
        v-show="deliveryDetailListDataTotal > 0"
        :total="deliveryDetailListDataTotal"
        :page.sync="deliveryPaginationQuery.page"
        :limit.sync="deliveryPaginationQuery.size"
        layout="total, prev, pager, next, jumper"
        @pagination="queryDNOrder"
      />
    </div>
    <el-dialog
      title="添加其他订单"
      width="600px"
      :visible.sync="addDlg"
      :destroy-on-close="true"
    >
      <AttachOrder :data="dnGroup[groupIndex]" @submit="handleAdd" @cancel="addDlg=false" />
    </el-dialog>
    <el-dialog
      title="是否直接将DN交货单创建并提交下发至仓库？"
      :visible.sync="submitDlg"
      width="600px"
    >
      <div>若您选择的是整单交货，且存在剩余计划行可交货，可选择是否继续创建DN</div>
      <div class="dlg-btn-row">
        <el-button
          type="primary"
          @click="handleSubmitAndContinue"
          :disabled="groupIndex>=dnGroup.length-1"
        >
          确定并继续创建下一笔
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmitAndStop"
          :disabled="groupIndex>dnGroup.length"
        >
          确定并停止继续创建
        </el-button>
        <el-button @click="submitDlg=false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import endsWith from 'lodash/endsWith'
import Pagination from '@/components/Pagination'
import { getContactDetail, createDN, submitDN, initDN, queryDNByGroup } from '@/api/orderDelivery'
import BaseInfo from './components/create/BaseInfo'
import Delivery from './components/common/Delivery'
import Finance from './components/common/Finance'
import DetailTable from './components/common/DetailTable'
import RowMore from './components/common/RowMore'
import AttachOrder from './components/common/AttachOrder'

import { getDeliveryFields, getFinanceFields } from './utils'
import { getGroupErrorTips } from '@/utils/order.js'

export default {
  name: 'OrderDeliveryCreate',
  components: {
    BaseInfo, Delivery, Finance, DetailTable, Pagination, RowMore, AttachOrder
  },
  data () {
    const { id } = this.$route.params
    return {
      id,
      dnData: {},
      dnGroup: [],
      groupIndex: 0,
      contact: {},
      isLoading: false,
      deliveryDetailListDataTotal: 0,
      showSelectDNPlan: false,
      orderItemNos: [],
      deliveryFields: getDeliveryFields('create'),
      financeFields: getFinanceFields('create'),
      deliveryPaginationQuery: {
        page: 1,
        size: 20
      },
      soAddData: {
        soNo: ''
      },
      submitFlag: 0,
      submitDlg: false,
      addDlg: false,
      foldDelivery: true,
      foldFinance: true,
      selectedItemList: [],
      rules: {
        soNo: [
          { required: true, message: 'OMS订单号不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created () {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList').then(() => {
        this.init()
      })
    } else {
      this.init()
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    customerNo () {
      return this.dnData.basicInfo ? this.dnData.basicInfo.customerNo : ''
    }
  },
  methods: {
    setShipmentFreeze () {
      const hasShipmentFreeze = this.dnGroup[this.groupIndex].some(item => {
        const result = endsWith(item.positionCode, '02')
        return result
      })
      if (hasShipmentFreeze) {
        this.dnData.deliveryInfo.shipmentFreeze = '01'
      }
    },
    setDemandUser (groupItemList) {
      const groupItem = groupItemList.find(item => !!item.demandUser)
      if (groupItem) {
        // this.dnData.deliveryInfo.demandUser = groupItem.demandUser
        this.dnData.deliveryInfo = { ...this.dnData.deliveryInfo, demandUser: groupItem.demandUser }
      }
    },
    init () {
      const { id } = this.$route.params
      const ids = id.split('-')
      if (id && ids && ids.length > 0) {
        this.isLoading = true
        const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
        const params = {}
        params.soNo = ids[0]
        if (ids.length > 0) {
          params.soItemNo = []
          ids.slice(1).forEach(d => {
            params.soItemNo.push(d)
          })
        }
        if (params && params.soNo) {
          Promise.all([initDN({ referOrderNo: params.soNo }), queryDNByGroup(params)]).then(async ([res, res1]) => {
            this.isLoading = false
            loading.close()
            if (res && res.code === 200) {
              const {
                basicInfo: {
                  distributionChannel,
                  productGroup,
                  salesOrganization,
                  hideLogo
                },
                deliveryInfo: {
                  contactId, receiptTimeCategory,
                  attachCoa,
                  attachMsds,
                  attachOrder,
                  attachTds,
                  exportProcessingZone,
                  referenceStandardShippingReq,
                  specifiedDocument,
                  serviceCenterSelfTransport
                },
                financeInfo: { invoicingByMail }
              } = res.data
              res.data.deliveryInfo = {
                distributionChannel,
                productGroup,
                salesOrganization,
                ...res.data.deliveryInfo,
                receiptTimeCategory: receiptTimeCategory === 'X',
                attachCoa: attachCoa === 'X',
                attachMsds: attachMsds === 'X',
                attachOrder: attachOrder === 'X',
                attachTds: attachTds === 'X',
                hideLogo: hideLogo === 'X',
                exportProcessingZone: exportProcessingZone === 'X',
                referenceStandardShippingReq: referenceStandardShippingReq === 'X',
                specifiedDocument: specifiedDocument === 'X',
                serviceCenterSelfTransport: serviceCenterSelfTransport === '001'
              }
              res.data.financeInfo = {
                ...res.data.financeInfo,
                invoicingByMail: invoicingByMail === 'X'
              }
              this.dnData = res.data
              if (contactId) {
                getContactDetail(contactId).then(res2 => {
                  if (res2 && res2.code === 200) {
                    this.contact = res2.data
                  }
                })
              }
            } else if (res && res.msg) {
              this.$alert(res.msg, '错误', {
                type: 'error',
                dangerouslyUseHTMLString: true
              })
            }
            if (res1 && res1.code === 200) {
              const { groupedDnItems } = res1.data
              if (!groupedDnItems || Object.keys(groupedDnItems).length === 0) {
                this.$alert('该订单当前不存在可交货的计划行，无法交货', '错误', {
                  type: 'error'
                })
              } else {
                await getGroupErrorTips(this, res1.data)
                this.groupIndex = 0
                Object.keys(groupedDnItems).forEach(item => {
                  const items = item.split('_')
                  if (groupedDnItems[item]) {
                    const group = groupedDnItems[item].map(g => ({
                      shippingPosition: items[1],
                      demandUser: items[2] || '',
                      ...g
                    }))
                    this.dnGroup.push(group)
                  }
                })
                this.setShipmentFreeze()
                this.dnData.items = this.dnGroup[0] || []
                this.setDemandUser(this.dnData.items)
              }
            } else if (res1 && res1.msg) {
              this.$alert(res1.msg, '错误')
            }
          }).catch(error => {
            loading.close()
            this.isLoading = false
            if (error.response && error.response.data) {
              const { message } = error.response.data
              if (message) {
                this.$alert(message, '错误')
              }
            }
          })
        }
      }
    },
    handleSelectionChange (val) {
      this.selectedItemList = val
    },
    handleBatchDelete () {
      if (this.selectedItemList && this.selectedItemList.length > 0) {
        this.$confirm('此操作将删除所选的商品, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          for (let i = this.dnData.items.length - 1; i >= 0; i--) {
            const foundItem = this.selectedItemList.find(item => {
              const {
                referOrderItemDetailNo: n1,
                referOrderItemNo: n2,
                referOrderNo: n3
              } = item
              const {
                referOrderItemDetailNo: m1,
                referOrderItemNo: m2,
                referOrderNo: m3
              } = this.dnData.items[i]
              return n1 === m1 && n2 === m2 && n3 === m3
            })
            if (foundItem) {
              this.dnData.items.splice(i, 1)
            }
          }
        })
      }
    },
    queryDNOrder () {
    },
    handleQuantityChange (val, idx) {
      const item = {
        ...this.dnData.items[idx],
        lfimg: val
      }
      this.$set(this.dnData.items, idx, item)
    },
    submitFinance (data, isDraft) {
      this.dnData.financeInfo = data
      this.submitFlag = this.submitFlag + 1
      if (this.submitFlag === 2) {
        this.handleCreate(isDraft)
      }
    },
    submitDelivery (data, isDraft) {
      this.dnData.deliveryInfo = data
      this.submitFlag = this.submitFlag + 1
      if (this.submitFlag === 2) {
        this.handleCreate(isDraft)
      }
    },
    handleSubmit () {
      if (this.dnGroup.length === 1) {
        this.handleSave(false)
      } else {
        this.submitDlg = true
      }
    },
    getFormPromise (form) {
      return new Promise(resolve => {
        form.validate(res => {
          resolve(res)
        })
      })
    },
    async handleSave (isDraft) {
      this.submitFlag = 0
      const deliveryForm = this.$refs.deliveryInfo.$refs['deliveryBaseForm']
      const financeForm = this.$refs.financeInfo.$refs['financeBaseForm']
      const res = await Promise.all([deliveryForm, financeForm].map(this.getFormPromise))
      const validateResult = res.every(item => !!item)
      if (validateResult) {
        this.$refs.deliveryInfo.submit('deliveryBaseForm', isDraft)
        this.$refs.financeInfo.submit('financeBaseForm', isDraft)
        return true
      }
      return false
    },
    handleCreate (isDraft) {
      const {
        basicInfo: {
          customerNo,
          referOrderType
        },
        deliveryInfo: {
          contactId, deliveryRequireTime, deliverySlipTemplate, demandUser,
          deliveryUnloadingReq, packagingReq, signingBack,
          receiptTimeCategory,
          attachCoa,
          attachMsds,
          attachOrder,
          attachTds,
          hideLogo,
          exportProcessingZone,
          referenceStandardShippingReq,
          specifiedDocument,
          serviceCenterSelfTransport,
          disableShipping, deliveryNote,
          shipmentFreeze,
          shippingCondition,
          orderNote
        },
        financeInfo: {
          invoiceType, invoicingByMail, unbillReason, unbillReasonDesc, shippingInfo, financeRedReason, financialNote
        },
        items
      } = this.dnData
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const itemList = items.map(item => Object.assign({}, item, {
        operationType: 1
      }))
      const hasShipmentFreeze = items.some(item => endsWith(item.positionCode, '02'))
      let shippingFreezeCode = ''
      if (hasShipmentFreeze && this.dictList['shippingFreeze']) {
        shippingFreezeCode = this.dictList['shippingFreeze'][0].code
      }
      const params = {
        customerNo,
        referOrderType,
        basicInfo: {
          hideLogo: hideLogo ? 'X' : 'Z'
        },
        deliveryInfo: {
          contactId,
          demandUser,
          deliveryRequireTime,
          deliverySlipTemplate,
          deliveryUnloadingReq,
          packagingReq,
          signingBack,
          receiptTimeCategory: receiptTimeCategory ? 'X' : 'Z',
          attachCoa: attachCoa ? 'X' : 'Z',
          attachMsds: attachMsds ? 'X' : 'Z',
          attachOrder: attachOrder ? 'X' : 'Z',
          attachTds: attachTds ? 'X' : 'Z',
          exportProcessingZone: exportProcessingZone ? 'X' : 'Z',
          referenceStandardShippingReq: referenceStandardShippingReq ? 'X' : 'Z',
          specifiedDocument: specifiedDocument ? 'X' : 'Z',
          serviceCenterSelfTransport: serviceCenterSelfTransport ? '001' : '002',
          disableShipping,
          deliveryNote,
          shipmentFreeze: shipmentFreeze || shippingFreezeCode,
          shippingCondition,
          orderNote
        },
        financeInfo: {
          invoiceType,
          invoicingByMail: invoicingByMail ? 'X' : 'Z',
          unbillReason,
          unbillReasonDesc,
          shippingInfo,
          financeRedReason,
          financialNote
        },
        items: itemList
      }
      const successCb = (isDraft, id) => {
        const str = `${isDraft ? 'DN创建成功' : 'DN创建成功且提交成功'}！对应的OMS交货单号为：${id}`
        this.$alert(str, 'DN创建成功', {
          confirmButtonText: '确定',
          callback: action => {
            if (this.groupIndex === this.dnGroup.length - 1) {
              this.$closeTag(this.$route.path)
              this.$router.push('/orderDelivery/list')
            }
          }
        })
      }
      createDN(params).then(res => {
        loading.close()
        if (res && res.code !== 200 && res.msg) {
          this.$alert(res.msg, '错误', {
            type: 'error'
          })
        }
        if (res && res.code === 200) {
          if (isDraft && res.data) {
            successCb(isDraft, res.data)
          } else if (!isDraft && res.data && typeof res.data === 'string') {
            submitDN({
              status: 'NEW',
              deliveryNoteNo: res.data
            }).then(res1 => {
              if (res1) {
                const { code, msg } = res1
                if (code !== 200 && msg) {
                  this.$alert(msg, code === 420 ? '创建成功' : '错误', {
                    type: code === 420 ? 'info' : 'error',
                    callback: () => {
                      if (code === 420 && this.groupIndex === this.dnGroup.length - 1) {
                        this.$closeTag(this.$route.path)
                        this.$router.push('/orderDelivery/list')
                      }
                    }
                  })
                }
                if (code === 200) {
                  successCb(isDraft, res.data)
                }
              }
            }).catch(error => {
              if (error.response && error.response.data) {
                const { message } = error.response.data
                if (message) {
                  this.$alert(message, '错误', {
                    type: 'error',
                    dangerouslyUseHTMLString: true
                  })
                }
              }
            })
          } else {
            this.$router.push('/orderDelivery/list')
          }
        }
      }).catch(error => {
        loading.close()
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误', {
              type: 'error',
              dangerouslyUseHTMLString: true
            })
          }
        }
      })
    },
    handleOpenCreateDlg () {
      this.addDlg = true
    },
    async handleSubmitAndContinue () {
      const ret = await this.handleSave(false)
      this.submitDlg = false
      if (ret) {
        this.groupIndex = this.groupIndex + 1
        this.dnData.items = this.dnGroup[this.groupIndex]
        this.setDemandUser(this.dnData.items)
      }
    },
    handleSubmitAndStop () {
      this.handleSave(false)
      this.submitDlg = false
    },
    handleAdd (data) {
      this.addDlg = false
      data && data.forEach(g => {
        this.dnData.items.push(g)
      })
    },
    handleDeliveryFold (val) {
      this.foldDelivery = val
    },
    handleFinanceFold (val) {
      this.foldFinance = val
    }
  }
}
</script>
<style lang="scss">
.el-message-box__message {
  p {
    word-break: break-word;
  }
}
</style>
<style lang="scss" scoped>
@import "./styles/common.scss";

.dn-edit-container {
  padding: 15px;
  .dn-container {
    padding: 10px;
  }
}

.dn-edit-header {
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: space-between;
  .dn-title {
    font-size: 18px;
    font-weight: bold;
  }
}

.dn-row-btn-list {
  display: flex;
  margin: 10px 0;
  align-items: center;
  justify-content: space-between;
}
.dlg-btn-row {
  margin-top: 15px;
  text-align: center;
}
</style>
