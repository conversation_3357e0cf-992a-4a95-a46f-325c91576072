<template>
  <div class="dn-edit-container">
    <div class="dn-edit-header">
      <span class="dn-title">创建交货单</span>
      <div>
        <el-button class="dn-normal-bn" type="primary" @click="handleSubmit">确认创建</el-button>
      </div>
    </div>
    <div class="dn-row-title">
      订单信息
    </div>
    <div class="dn-container">
      <BaseInfo
        ref="baseInfo"
        :data="dnData.basicInfo||{}"
      />
      <Delivery
        ref="deliveryOrderInfo"
        :isGenerateDataByFields="true"
        :data="dnData.deliveryInfo"
        :customerNo="customerNo"
        :contact="contact"
        :disabledContact="false"
        :fields="getDnCreateFields('OrderInfo')"
        @submit="submitDelivery"
      />
    </div>
    <div class="dn-row-title">
      随货文件要求
      <RowMore @fold="(v) => handleFold('foldCargoFile', v)" :fold="foldCargoFile" />
    </div>
    <Delivery
      ref="deliveryCargoInfo"
      :isHideContactFields="true"
      :isGenerateDataByFields="true"
      :data="dnData.deliveryInfo"
      :customerNo="customerNo"
      :fields="getDnCreateFields('CargoFile')"
      :fold="foldCargoFile"
      :fastenerLogoDisabledList="fastenerLogoDisabledList"
      @submit="submitDelivery"
    />
    <div class="dn-row-title">
      仓储要求
      <RowMore @fold="(v) => handleFold('foldOtherReq', v)" :fold="foldOtherReq" />
    </div>
    <Delivery
      ref="deliveryOtherInfo"
      :isHideContactFields="true"
      :isGenerateDataByFields="true"
      :data="dnData.deliveryInfo"
      :customerNo="customerNo"
      :fields="getDnCreateFields('OtherReq')"
      :fastenerLogoDisabledList="fastenerLogoDisabledList"
      :fold="foldOtherReq"
      @submit="submitDelivery"
    />
    <div class="dn-row-title">
      运输要求
      <RowMore @fold="(v) => handleFold('foldTransport', v)" :fold="foldTransport" />
    </div>
    <Delivery
      ref="deliveryTransInfo"
      :isHideContactFields="true"
      :isGenerateDataByFields="true"
      :data="dnData.deliveryInfo"
      :customerNo="customerNo"
      :fields="getDnCreateFields('Transport')"
      :fold="foldTransport"
      @submit="submitDelivery"
    />
    <div class="dn-row-title">
      财务信息
      <RowMore @fold="handleFinanceFold" />
    </div>
    <Finance
      ref="financeInfo"
      :data="dnData.financeInfo"
      :fields="financeFields"
      :fold="foldFinance"
      :orderType="dnData.basicInfo?dnData.basicInfo.referOrderType:''"
      @submit="submitFinance"
    />
    <div class="dn-row-title">行项目明细</div>
    <div class="dn-row-btn-list">
      <div>
        <el-button class="dn-mini-bn" v-if="isAddButtonShow()" type="primary" @click="handleOpenCreateDlg" size="mini">新增</el-button>
        <el-button class="dn-mini-bn" @click="handleBatchDelete" :disabled="selectedItemList.length===0" size="mini">批量删除</el-button>
      </div>
    </div>
    <DetailTable
      type="init"
      :data="dnData.items"
      :isLoading="isLoading"
      :status="dnData.status"
      :setFastenerLogo="setFastenerLogo"
      @selectionChange="handleSelectionChange"
      @quantityChange="handleQuantityChange"
    />
    <div class="pagination-container">
      <pagination
        v-show="deliveryDetailListDataTotal > 0"
        :total="deliveryDetailListDataTotal"
        :page.sync="deliveryPaginationQuery.page"
        :limit.sync="deliveryPaginationQuery.size"
        layout="total, prev, pager, next, jumper"
        @pagination="queryDNOrder"
      />
    </div>
    <el-dialog
      title="添加其他订单"
      width="600px"
      :visible.sync="addDlg"
      :destroy-on-close="true"
    >
      <AttachOrder :deliveryType="deliveryTypeData()" :dnData="dnData" :data="dnGroup[groupIndex]" @submit="handleAdd" @cancel="addDlg=false" />
    </el-dialog>
    <el-dialog
      title="是否直接将DN交货单创建并提交下发至仓库？"
      :visible.sync="submitDlg"
      width="600px"
    >
      <div>若您选择的是整单交货，且存在剩余计划行可交货，可选择是否继续创建DN</div>
      <div class="dlg-btn-row">
        <el-button
          type="primary"
          @click="handleSubmitAndContinue"
          :disabled="groupIndex>=dnGroup.length-1"
        >
          确定并继续创建下一笔
        </el-button>
        <el-button
          type="primary"
          @click="handleSubmitAndStop"
          :disabled="groupIndex>dnGroup.length"
        >
          确定并停止继续创建
        </el-button>
        <el-button @click="submitDlg=false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Pagination from '@/components/Pagination'
import {
  getContactDetail,
  createDN,
  createShippingDNV2,
  dncCreateDN,
  submitDN,
  initDN,
  queryDNByGroupV2,
  queryShippingByGroupV2,
  updateWorkflowNDStatus
} from '@/api/orderDelivery'
import BaseInfo from './components/create/BaseInfo'
import Delivery from './components/common/Delivery'
import Finance from './components/common/Finance'
import DetailTable from './components/common/DetailTable'
import RowMore from './components/common/RowMore'
import AttachOrder from './components/common/AttachOrder'
import { foldFields, unfoldFields, orderServiceFields } from '@/utils/orderService'
import { getDeliveryFields, getFinanceFields, getDnCreateFields } from './utils'
import { deepClone } from '@/utils/index'
import { getGroupErrorTips } from '@/utils/order.js'
import { isEqual } from 'lodash'

export default {
  name: 'OrderDeliveryCreate',
  components: {
    BaseInfo, Delivery, Finance, DetailTable, Pagination, RowMore, AttachOrder
  },
  data () {
    const { id } = this.$route.params
    return {
      id,
      dnData: {},
      dnGroup: [],
      groupIndex: 0,
      contact: {},
      isLoading: false,
      deliveryDetailListDataTotal: 0,
      showSelectDNPlan: false,
      orderItemNos: [],
      deliveryFields: getDeliveryFields('create'),
      financeFields: getFinanceFields('create'),
      deliveryPaginationQuery: {
        page: 1,
        size: 20
      },
      soAddData: {
        soNo: ''
      },
      submitFlag: 0,
      submitDlg: false,
      addDlg: false,
      foldDelivery: false,
      foldFinance: true,
      foldOrderInfo: true,
      foldCargoFile: true,
      foldOtherReq: true,
      foldTransport: true,
      selectedItemList: [],
      rules: {
        soNo: [
          { required: true, message: 'OMS订单号不能为空', trigger: 'blur' }
        ]
      },
      fastenerLogoStatus: '',
      fastenerLogoDisabledList: {},
      epidemicSituationFlag: '', // 疫情标识
      itemDetailData: {}
    }
  },
  async created () {
    if (JSON.stringify(this.dictList) === '{}') {
      await this.$store.dispatch('orderCommon/queryDictList')
    }
    if (JSON.stringify(this.orderServiceDict) === '{}') {
      this.$store.dispatch('orderCommon/getOrderServiceDict')
    }
    window.setFastenerLogo = this.setFastenerLogo
    await this.init()
  },
  activated() {
    this.getDeliveryNoteTips()
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderServiceDict () {
      return this.$store.state.orderCommon.orderServiceDict || {}
    },
    customerNo () {
      return this.dnData.basicInfo ? this.dnData.basicInfo.customerNo : ''
    }
  },
  methods: {
    getDnCreateFields,
    setFastenerLogo (status) {
      console.log(status)
      this.fastenerLogoStatus = status
      this.fastenerLogoDisabledList = {}
      if (status !== 'ALL') {
        // 1、紧固件标签要求、紧固件检测、紧固件特殊包装要求：
        // 从so继承到dn维度时的处理规则变更，原本是任意行oem紧固件就继承，
        // 现逻辑改为所有行oem紧固件才继承，否则默认无要求，不可改；
        // 紧固件标签要求: fastenerLabelReq
        // 紧固件检测: fastenerDetect
        // 紧固件特殊包装要求: fastenerSpecialPackageReq
        this.fastenerLogoDisabledList = {
          fastenerLabelReq: true,
          fastenerDetect: true,
          fastenerSpecialPackageReq: true
        }
        try {
          this.$refs.deliveryCargoInfo.deliveryInfo.fastenerLabelReq = ['0']
        } catch (err) {}
        try {
          this.$refs.deliveryOtherInfo.deliveryInfo.fastenerDetect = '0'
        } catch (err) {}
        try {
          this.$refs.deliveryOtherInfo.deliveryInfo.fastenerSpecialPackageReq = '0'
        } catch (err) {}
      }
      if (status === 'ALL') {
        // 2、标签张贴方式：新增so继承到dn规则，无oem紧固件行才继承，否则默认无要求，不可改；
        // 改为只要有非oem紧固件都可以改
        this.fastenerLogoDisabledList.labelPasteWay = true
        try {
          this.$refs.deliveryCargoInfo.deliveryInfo.labelPasteWay = ['0']
        } catch (err) {}
      }
    },
    handleFold(type, val) {
      this[type] = val;
    },
    deliveryTypeData () {
      let ret = ''
      try {
        ret = this.dnData.items[0].deliveryType
      } catch (err) {}
      return ret;
    },
    isAddButtonShow () {
      return this.deliveryTypeData() !== 'shippingDelivery' && this.deliveryTypeData() !== 'transfer'
    },
    setDemandUser (groupItemList) {
      const groupItem = groupItemList.find(item => !!item.demandUser)
      if (groupItem) {
        // this.dnData.deliveryInfo.demandUser = groupItem.demandUser
        this.dnData.deliveryInfo = { ...this.dnData.deliveryInfo, demandUser: groupItem.demandUser }
      }
    },
    getDeliveryNoteTips() {
      if (this.$route.query.total === 'true' && this.itemDetailData?.msg) {
        this.epidemicSituationFlag = this.itemDetailData?.epidemicSituationFlag
        this.$confirm(this.itemDetailData?.msg, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
    },
    async init () {
      this.epidemicSituationFlag = this.$route?.query?.epidemicSituationFlag
      const { id } = this.$route.params
      const ids = id.split('-')
      if (id && ids && ids.length > 0) {
        this.isLoading = true
        const loading = this.$loading({
          lock: true,
          background: 'rgba(0, 0, 0, 0.5)'
        })
        const params = {}
        params.soNo = ids[0]
        if (ids.length > 0) {
          params.soItemNo = []
          ids.slice(1).forEach(d => {
            params.soItemNo.push(d)
          })
        }
        try {
          let res
          let res1
          if (this.$route.query.from === 'deliveryCollection' && params && params.soNo) {
            let queryApi = queryShippingByGroupV2;
            [res, res1] = await Promise.all([initDN({ referOrderNo: params.soNo, dnType: '1' }), queryApi(params)])
          } else if (params && params.soNo) {
            let queryApi = queryDNByGroupV2;
            res = await initDN({ referOrderNo: params.soNo, dnType: '0' });
            if (res && res.code === 200) {
              // 两个都有值，清空禁用货运
              if ((res.data.deliveryInfo.disableShipping && res.data.deliveryInfo.disableShipping !== '0') && (res.data.deliveryInfo.designatedShipping && res.data.deliveryInfo.designatedShipping !== '0')) {
                res.data.deliveryInfo.disableShipping = ''
              }
              // 用于后端校验指定货运、禁用货运一致性
              params.disableShipping = res.data.deliveryInfo.disableShipping
              params.designatedShipping = res.data.deliveryInfo.designatedShipping
              params.orderBasis = res.data.basicInfo.orderBasis;
            }
            res1 = await queryApi(params);
            // [res, res1] = await Promise.all([initDN({ referOrderNo: params.soNo, dnType: '0' }), queryApi(params)])
          }
          this.isLoading = false
          loading.close()
          if (res && res.code === 200) {
            const {
              basicInfo: {
                distributionChannel,
                productGroup,
                salesOrganization,
                hideLogo,
                requireDepartment
              },
              deliveryInfo: {
                contactId,
                receiptTimeCategory,
                attachCoa,
                attachMsds,
                attachOrder,
                attachTds,
                exportProcessingZone,
                referenceStandardShippingReq,
                specifiedDocument,
                serviceCenterSelfTransport
              },
              financeInfo: { invoicingByMail }
            } = res.data
            this.preHandleDisableShipping(res.data.deliveryInfo)
            res.data.deliveryInfo = {
              distributionChannel,
              productGroup,
              salesOrganization,
              requireDepartment,
              ...res.data.deliveryInfo,
              ...unfoldFields(res.data.deliveryInfo, [
                'dnSignatureReq',
                'otherLabelReq',
                'fastenerLabelReq',
                'labelPasteWay',
                'specifiedReceiptDayOfWeek',
                'specifiedReceiptTime',
                'disableShipping',
                'vehicleReq',
                'designatedShipping',
                'deliveryWarehouseInfo'
              ]),
              packagingReq: res.data.deliveryInfo.packagingReq ? (res.data.deliveryInfo.packagingReq || '').split(',').filter(Boolean) : ['0'],
              receiptTimeCategory: receiptTimeCategory === 'X',
              attachCoa: attachCoa === '1' ? 'X' : attachCoa === '0' ? 'Z' : attachCoa,
              attachMsds: attachMsds === '1' ? 'X' : attachMsds === '0' ? 'Z' : attachMsds,
              attachOrder: attachOrder === 'X',
              attachTds: attachTds === '1' ? 'X' : attachTds === '0' ? 'Z' : attachTds,
              hideLogo: hideLogo === 'X',
              exportProcessingZone: exportProcessingZone === 'X',
              referenceStandardShippingReq: referenceStandardShippingReq === 'X',
              specifiedDocument: specifiedDocument === 'X',
              serviceCenterSelfTransport: serviceCenterSelfTransport === '001',
              deliveryRequireTime: moment().format('yyyy-MM-DD')
            }
            res.data.financeInfo = {
              ...res.data.financeInfo,
              invoicingByMail: invoicingByMail === 'X'
            }

            this.dnData = res.data
            if (contactId) {
              getContactDetail(contactId).then(res2 => {
                if (res2 && res2.code === 200) {
                  this.contact = res2.data
                }
              })
            }
          } else if (res && res.msg) {
            this.$alert(res.msg, '错误', {
              type: 'error',
              dangerouslyUseHTMLString: true
            })
          }
          if (res1 && res1.code === 200) {
            const { groupedDnItems, groupErrMsg } = res1.data
            this.itemDetailData = res1.data
            if (!groupedDnItems || Object.keys(groupedDnItems).length === 0) {
              this.$alert(groupErrMsg || '该订单当前不存在可交货的计划行，无法交货', '错误', {
                type: 'error'
              })
            } else {
              await getGroupErrorTips(this, this.itemDetailData)
              this.getDeliveryNoteTips()
              this.groupIndex = 0
              Object.keys(groupedDnItems).forEach(item => {
                const items = item.split('_')
                if (groupedDnItems[item]) {
                  const group = groupedDnItems[item].map(g => ({
                    shippingPosition: items[1],
                    demandUser: items[3] || '',
                    ...g
                  }))
                  this.dnGroup.push(group)
                }
              })
              this.dnData.items = this.dnGroup[0] || []
              this.setDemandUser(this.dnData.items)
            }
          } else if (res1 && res1.msg) {
            this.$alert(res1.msg, '错误')
          }
        } catch (error) {
          loading.close()
          this.isLoading = false
          if (error.response && error.response.data) {
            const { message } = error.response.data
            if (message) {
              this.$alert(message, '错误')
            }
          }
        }
      }
    },
    handleSelectionChange (val) {
      this.selectedItemList = val
    },
    handleBatchDelete () {
      if (this.selectedItemList && this.selectedItemList.length > 0) {
        this.$confirm('此操作将删除所选的商品, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          for (let i = this.dnData.items.length - 1; i >= 0; i--) {
            const foundItem = this.selectedItemList.find(item => {
              const {
                referOrderItemDetailNo: n1,
                referOrderItemNo: n2,
                referOrderNo: n3
              } = item
              const {
                referOrderItemDetailNo: m1,
                referOrderItemNo: m2,
                referOrderNo: m3
              } = this.dnData.items[i]
              return n1 === m1 && n2 === m2 && n3 === m3
            })
            if (foundItem) {
              this.dnData.items.splice(i, 1)
            }
          }
        })
      }
    },
    queryDNOrder () {
    },
    handleQuantityChange (val, idx) {
      const item = {
        ...this.dnData.items[idx],
        lfimg: val
      }
      this.$set(this.dnData.items, idx, item)
    },
    submitFinance (data, isDraft) {
      this.dnData.financeInfo = data
      this.submitFlag = this.submitFlag + 1
      if (this.submitFlag === 5) {
        this.handleCreate(isDraft)
      }
    },
    submitDelivery (data, isDraft, addSubmitFlag = true) {
      this.dnData.deliveryInfo = { ...this.dnData.deliveryInfo, ...data }
      if (addSubmitFlag) {
        this.submitFlag = this.submitFlag + 1
      }
      if (this.submitFlag === 5) {
        this.handleCreate(isDraft)
      }
    },
    handleSubmit () {
      if (this.dnGroup.length === 1) {
        this.handleSave(false)
      } else {
        this.submitDlg = true
      }
    },
    getFormPromise (form) {
      return new Promise(resolve => {
        form.validate(res => {
          resolve(res)
        })
      })
    },
    async handleSave (isDraft) {
      this.submitFlag = 0
      const deliveryOrderForm = this.$refs.deliveryOrderInfo.$refs['deliveryBaseForm']
      const deliveryCargoForm = this.$refs.deliveryCargoInfo.$refs['deliveryBaseForm']
      const deliveryOtherForm = this.$refs.deliveryOtherInfo.$refs['deliveryBaseForm']
      const deliveryTransForm = this.$refs.deliveryTransInfo.$refs['deliveryBaseForm']
      const financeForm = this.$refs.financeInfo.$refs['financeBaseForm']
      const res = await Promise.all([
        deliveryOrderForm,
        deliveryCargoForm,
        deliveryOtherForm,
        deliveryTransForm,
        financeForm
      ].map(this.getFormPromise))
      const validateResult = res.every(item => !!item)
      if (validateResult) {
        this.$refs.deliveryOrderInfo.submit('deliveryBaseForm', isDraft)
        this.$refs.deliveryCargoInfo.submit('deliveryBaseForm', isDraft)
        this.$refs.deliveryOtherInfo.submit('deliveryBaseForm', isDraft)
        this.$refs.deliveryTransInfo.submit('deliveryBaseForm', isDraft)
        this.$refs.financeInfo.submit('financeBaseForm', isDraft)
        return true
      }
      return false
    },
    handleWorkbenchCB() {
      const entityId = this.$route.query['entity-id']
      if (entityId) {
        const data = {
          entityId: [entityId],
          status: 'doing'
        }
        updateWorkflowNDStatus(data)
        .then(console.log)
      }
    },
    isInOptions(code, options) {
      return options.find(option => option.code === code);
    },
    isDisableShippingNew (deliveryInfo) {
      try {
        if (!deliveryInfo.disableShipping) return true
        const values = deliveryInfo.disableShipping.split(',')
        const options = this.orderServiceDict.disableShipping.options
        if (!values.every(value => this.isInOptions(value, options))) {
          return false;
        }
      } catch (err) {
        console.log(err)
      }
      return true
    },
    preHandleDisableShipping(deliveryInfo) {
      try {
        if (deliveryInfo?.disableShipping && !this.isDisableShippingNew(deliveryInfo)) {
          console.log('is old disableShipping...')
          deliveryInfo._disableShipping = deliveryInfo?.disableShipping
          deliveryInfo.disableShipping = []
        }
      } catch (err) {
        console.log(err)
      }
    },
    handleDisableShipping (params) {
      try {
        if (params.deliveryInfo._disableShipping && !params.deliveryInfo.disableShipping) {
          params.deliveryInfo.disableShipping = params.deliveryInfo._disableShipping
        }
        delete params.deliveryInfo._disableShipping
      } catch (err) {
        console.log(err)
      }
    },
    async handleCreate (isDraft) {
      const orderServiceFieldsList = orderServiceFields.map(obj => obj.field)
      const deliveryInfo = foldFields(deepClone(this.dnData.deliveryInfo), orderServiceFieldsList.concat('packagingReq', 'deliveryWarehouseInfo'))
      const {
        contactId, deliveryRequireTime, deliverySlipTemplate, demandUser,
        deliveryUnloadingReq, packagingReq, signingBack, labelTemplate,
        receiptTimeCategory,
        attachCoa,
        attachMsds,
        attachOrder,
        attachTds,
        hideLogo,
        exportProcessingZone,
        referenceStandardShippingReq,
        specifiedDocument,
        serviceCenterSelfTransport,
        disableShipping,
        deliveryNote,
        shippingCondition,
        orderNote,
        requireDepartment,
        certificateIdentification,
        printNum
      } = deliveryInfo
      const uselessDeliveryFields = ['distributionChannel', 'productGroup', 'salesOrganization', 'requireDepartment', 'contactInfo', 'hideLogo', 'contactName', 'address', 'phone'];
      uselessDeliveryFields.forEach(field => {
        delete deliveryInfo[field]
      })
      const {
        basicInfo: {
          customerNo,
          referOrderType,
          orderBasis
        },
        financeInfo: {
          invoiceType, invoicingByMail, unbillReason, unbillReasonDesc, shippingInfo, financeRedReason, financialNote
        },
        items
      } = deepClone(this.dnData)
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      const itemList = items.map(item => Object.assign({}, item, {
        operationType: 1
      }))
      const params = {
        deliveryType: (itemList && itemList[0] && itemList[0].deliveryType) || '',
        customerNo,
        referOrderType,
        basicInfo: {
          requireDepartment,
          hideLogo: hideLogo ? 'X' : 'Z',
          orderBasis
        },
        deliveryInfo: {
          ...deliveryInfo,
          contactId,
          demandUser,
          deliveryRequireTime,
          deliverySlipTemplate,
          deliveryUnloadingReq,
          packagingReq,
          signingBack,
          labelTemplate,
          receiptTimeCategory: receiptTimeCategory ? 'X' : 'Z',
          attachCoa,
          attachMsds,
          attachOrder: attachOrder ? 'X' : 'Z',
          attachTds,
          exportProcessingZone: exportProcessingZone ? 'X' : 'Z',
          referenceStandardShippingReq: referenceStandardShippingReq ? 'X' : 'Z',
          specifiedDocument: specifiedDocument ? 'X' : 'Z',
          serviceCenterSelfTransport: serviceCenterSelfTransport ? '001' : '002',
          disableShipping,
          deliveryNote,
          shippingCondition,
          orderNote,
          certificateIdentification,
          printNum
        },
        financeInfo: {
          invoiceType,
          invoicingByMail: invoicingByMail ? 'X' : 'Z',
          unbillReason,
          unbillReasonDesc,
          shippingInfo,
          financeRedReason,
          financialNote
        },
        epidemicSituationFlag: this.epidemicSituationFlag || 'n',
        items: itemList
      }
      this.handleDisableShipping(params)
      const shipping = params?.deliveryInfo?.disableShipping
      if (shipping) {
        if (shipping.length > 300 || shipping.split(',').length > 7) {
          this.$message.error('禁用货运字段最多选择7项且字符串长度不能大于300！')
          if (loading) {
            loading.close()
          }
          return;
        }
      }
      if (this.fastenerLogoStatus !== 'ALL') {
        params.deliveryInfo.fastenerLabelReq = '0'
        params.deliveryInfo.fastenerDetect = '0'
        params.deliveryInfo.fastenerSpecialPackageReq = '0'
      }
      const successCb = (isDraft, res) => {
        const { data: { dnNo, message, freezeMessage } } = res
        if (dnNo) {
          let str = ''
          if (freezeMessage) {
            str = `DN创建成功但存在交货冻结，冻结原因：${freezeMessage}，对应的OMS交货单号为：${dnNo}`
          } else {
            str = `${isDraft ? 'DN创建成功' : 'DN创建成功且提交成功'}！对应的OMS交货单号为：${dnNo}`
          }
          if (message) {
            str = `${str}<br>${message}`
          }
          this.handleWorkbenchCB()
          this.$alert(str, 'DN创建成功', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            callback: action => {
              if (this.groupIndex === this.dnGroup.length - 1) {
                this.$closeTag(this.$route.path)
                this.$router.push('/orderDelivery/list')
              } else {
                this.groupIndex = this.groupIndex + 1
                this.dnData.items = this.dnGroup[this.groupIndex]
                this.setDemandUser(this.dnData.items)
              }
            }
          })
        }
      }
      const catchErr = error => {
        if (error.response && error.response.data) {
          const { message } = error.response.data
          if (message) {
            this.$alert(message, '错误', {
              type: 'error',
              dangerouslyUseHTMLString: true
            })
          }
        }
      }
      const createCallback = res => {
        if (isDraft) {
          successCb(isDraft, res)
        } else {
          submitDN({
            status: 'NEW',
            deliveryNoteNo: res.data.dnNo
          }).then(res1 => {
            if (res1) {
              const { code, msg } = res1
              if (code !== 200 && msg) {
                this.$alert(msg, code === 420 ? '创建成功' : '错误', {
                  type: code === 420 ? 'info' : 'error',
                  callback: () => {
                    if (code === 420 && this.groupIndex === this.dnGroup.length - 1) {
                      this.$closeTag(this.$route.path)
                      this.$router.push('/orderDelivery/list')
                    }
                  }
                })
              }
              if (code === 200) {
                successCb(isDraft, res)
              }
            }
          }).catch(error => {
            catchErr(error)
          })
        }
      }
      let hasConflictFields = false;
      let conflictMessage = '交货信息中存在冲突字段，系统已自动修正：<br />';

      if (hideLogo && (deliveryInfo.dnSignatureReq?.indexOf('01') > -1 || deliveryInfo.dnSignatureReq?.indexOf('05') > -1)) {
        conflictMessage += '因隐藏logo=是，故修正送货单签章要求不等于盖红章或每页盖章，紧固件特殊包装要求等于无要求；<br />';
        hasConflictFields = true;
      }

      if (!receiptTimeCategory && (deliveryInfo.specifiedReceiptDayOfWeek?.indexOf('06') > -1 || deliveryInfo.specifiedReceiptDayOfWeek?.indexOf('07') > -1)) {
        conflictMessage += '因工作日和周末均可收=否，故取消指定收货日期=周六/周日选项；<br />';
        hasConflictFields = true;
      }

      if (!items.find(item => item.fastenerLogo) && (deliveryInfo.fastenerLabelReq !== '0' || deliveryInfo.fastenerDetect !== '0' || deliveryInfo.fastenerSpecialPackageReq !== '0')) {
        conflictMessage += '因不含oem紧固件商品行，故紧固件标签要求/紧固件检测/紧固件特殊包装要求=无要求；';
        hasConflictFields = true;
      }
      if (items.every(item => item.fastenerLogo) && (deliveryInfo.labelPasteWay !== '0')) {
        conflictMessage += '所有sku产品组都为OEM紧固件，故标签张贴方式=无要求；';
        hasConflictFields = true;
      }

      if (hasConflictFields) {
        await this.$confirm(conflictMessage, {
          dangerouslyUseHTMLString: true,
          showCancelButton: false,
          showClose: false,
          closeOnClickModal: false
        });
      }

      const create = async () => {
        try {
          let res
          if (this.dnData.basicInfo.orderBasis === 'STOCK_CLEARANCE') {
            res = await dncCreateDN(params)
          } else if (this.dnData.basicInfo.dnType === '1') {
            let createApi = createShippingDNV2
            res = await createApi(params)
          } else {
            res = await createDN(params)
          }
          loading.close()
          if (res && res.code !== 200 && res.msg) {
            this.$alert(res.msg, '错误', {
              type: 'error'
            })
          }
          if (res && res.code === 200) {
            if (res.data && res.data.dnNo) {
              createCallback(res)
            }
          }
        } catch (error) {
          loading.close()
          catchErr(error)
        }
      }

      let orders = []
      items.forEach(item => {
        if (!this.isEqualDesignatedShipping(item.designatedShipping, params.deliveryInfo.designatedShipping)) {
          orders.push(item.referOrderNo)
        }
      })
      // 指定货运时校验DN头的指定货运和所有so行一致，不一致则弹窗提示(401/403不判断)
      if (orders.length > 0 && params.deliveryType !== 'transfer') {
        this.$confirm(`SO：${orders.join(',')}指定货运与DN要求不一致，确定创建？`, {
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false
        }).then(() => {
          create()
        }).catch(err => {
          loading.close();
          console.log(err);
        })
      } else {
        create()
      }
    },
    isEqualDesignatedShipping (str1 = '', str2 = '') {
      try {
        str1 = typeof str1 === 'string' ? str1.split(',') : str1
        str2 = typeof str2 === 'string' ? str2.split(',') : str2
        const a1 = str1?.filter(item => !!item && item !== '0')
        const a2 = str2?.filter(item => !!item && item !== '0')
        return isEqual(a1, a2)
      } catch (err) {
        console.log(err)
      }
    },
    handleOpenCreateDlg () {
      this.addDlg = true
      // 新增行需要使用禁用货运和指定货运的最新值，所以把deliveryInfo全都更新一下
      this.$refs.deliveryOrderInfo.submit('deliveryBaseForm', false, false)
      this.$refs.deliveryCargoInfo.submit('deliveryBaseForm', false, false)
      this.$refs.deliveryOtherInfo.submit('deliveryBaseForm', false, false)
      this.$refs.deliveryTransInfo.submit('deliveryBaseForm', false, false)
      // this.$refs.financeInfo.submit('deliveryBaseForm', false, false)
    },
    async handleSubmitAndContinue () {
      const ret = await this.handleSave(false)
      if (!ret) {
        this.$message.warning({
          message: '必填项校验失败！'
        })
      }
    },
    handleSubmitAndStop () {
      this.handleSave(false)
      this.submitDlg = false
    },
    handleAdd (data, epidemicSituationFlag) {
      this.addDlg = false
      if (epidemicSituationFlag) {
        this.epidemicSituationFlag = epidemicSituationFlag;
      }
      data && data.forEach(g => {
        if (g) {
          const { referOrderItemDetailNo, referOrderItemNo, referOrderNo } = g
          const found = this.dnData.items.find(item => {
            return item.referOrderItemDetailNo === referOrderItemDetailNo &&
              item.referOrderItemNo === referOrderItemNo &&
              item.referOrderNo === referOrderNo
          })
          if (!found) {
            this.dnData.items.push(g)
          }
        }
      })
    },
    handleDeliveryFold (val) {
      this.foldDelivery = val
    },
    handleFinanceFold (val) {
      this.foldFinance = val
    }
  }
}
</script>
<style lang="scss">
.el-message-box__message {
  p {
    word-break: break-word;
  }
}
</style>
<style lang="scss" scoped>
@import "./styles/common.scss";

.dn-edit-container {
  padding: 15px;
  .dn-container {
    padding: 10px;
  }
}

.dn-edit-header {
  display: flex;
  height: 40px;
  align-items: center;
  justify-content: space-between;
  .dn-title {
    font-size: 18px;
    font-weight: bold;
  }
}

.dn-row-btn-list {
  display: flex;
  margin: 10px 0;
  align-items: center;
  justify-content: space-between;
}
.dlg-btn-row {
  margin-top: 15px;
  text-align: center;
}
</style>
