export const BaseinfoFields = [
  [
    { title: 'OMS交货单号', prop: 'deliveryNo', span: 6, type: 'text' },
    { title: 'SAP交货单号', prop: 'sapDeliveryNo', span: 6, type: 'text' },
    { title: '状态', prop: 'statusDesc', span: 6, type: 'text' },
    { title: '交货类型', prop: 'deliveryTypeDesc', span: 6, type: 'text' }
  ],
  [
    { title: '创建日期', prop: 'gmtCreate', span: 6, type: 'text' },
    { title: '创建者', prop: 'creator', span: 6, type: 'text' },
    { title: '仓位类型', prop: 'positionOwner', span: 6, type: 'dict', dictKey: 'positionOwner' },
    { title: '特殊交付订单', prop: 'isSpecialOrder', span: 6 }
  ],
  [
    { title: '系统处理结果', prop: 'systemStatus', span: 12, type: 'text' },
    { title: '业务类型', prop: 'dnBusinessTypeDesc', span: 12, type: 'text' }
  ],
  [
    { title: '坤合当前状态', prop: 'desc', span: 6, type: 'text' },
    { title: '下发SAP结果', prop: 'sapSyncStatus', span: 12, type: 'text' },
    { title: '仓网类型', prop: 'warehouseRuleCode', span: 6, type: 'text' }
  ],
  [
    { title: '客服', prop: 'customerServiceName', span: 6, type: 'text' },
    { title: '客户主管', prop: 'customerServiceSupervisorName', span: 6, type: 'text' },
    { title: '销售', prop: 'salesName', span: 6, type: 'text' },
    { title: '销售主管', prop: 'salesSupervisorName', span: 6, type: 'text' }
  ]
]

export const BaseinfoStoFields = [
  [
    { title: 'OMS交货单号', prop: 'deliveryNo', span: 6, type: 'text' },
    { title: 'SAP交货单号', prop: 'sapDeliveryNo', span: 6, type: 'text' },
    { title: '状态', prop: 'statusDesc', span: 6, type: 'text' },
    { title: '交货类型', prop: 'deliveryTypeDesc', span: 6, type: 'text' }
  ],
  [
    { title: '创建日期', prop: 'gmtCreate', span: 6, type: 'text' },
    { title: '创建者', prop: 'creator', span: 6, type: 'text' },
    { title: '仓位类型', prop: 'positionOwner', span: 6, type: 'dict', dictKey: 'positionOwner' },
    { title: '特殊交付订单', prop: 'isSpecialOrder', span: 6 }
  ],
  [
    { title: '下发坤合结果', prop: 'systemStatus', span: 12, type: 'text' },
    { title: '下发SAP结果', prop: 'sapSyncStatus', span: 6, type: 'text' },
    { title: '仓网类型', prop: 'warehouseRuleCode', span: 6, type: 'text' }
  ],
  [
    { title: '采购', prop: 'customerServiceName', span: 6, type: 'text' }
  ],
  [
    { title: '客户名称', prop: 'customerName', span: 6, type: 'text' },
    { title: '直/分销渠道', prop: 'distributionChannel', span: 6, type: 'dict', dictKey: 'distributionChannel' },
    { title: '销售组织', prop: 'company', span: 8, type: 'text' }
  ]
]

const CustomerCreateFields = [
  { title: '客户名称', prop: 'customerName', span: 8, type: 'text' },
  { title: '销售组织', prop: 'company', span: 10, type: 'text' },
  { title: '直/分销渠道', prop: 'distributionChannel', span: 6, type: 'select', dictKey: 'distributionChannel' }
]

export const BaseinfoStoCreateFields = [CustomerCreateFields];

// 交货信息
const deliveryRequireTime = { title: '计划发货日期', prop: 'deliveryRequireTime', type: 'date' }
const deliveryTime = { title: '实际发货日期', prop: 'deliveryTime', type: 'date' }
const deliverySlipTemplate = { title: '送货单模板', prop: 'deliverySlipTemplate', type: 'select', dictKey: 'deliverySlipTemplate' }
const deliveryUnloadingReq = { title: '送货卸货要求', prop: 'deliveryUnloadingReq', type: 'select', dictKey: 'deliveryUnloadingReq' }
const shipmentFreeze = { title: '装运冻结原因', prop: 'shipmentFreeze', type: 'select', dictKey: 'shippingFreeze', style: 'tag' }
const deliveryFreeze = { title: '交货冻结原因', prop: 'deliveryFreeze', type: 'select', dictKey: 'deliveryFreeze', style: 'tag' }
const signingBack = { title: '签单返回', prop: 'signingBack', type: 'select', dictKey: 'signingBack' }
const labelTemplate = { title: '标签模板', prop: 'labelTemplate', type: 'select', dictKey: 'labelTemplate' }
const receiptTimeCategory = { title: '周末与节假日均可收货', prop: 'receiptTimeCategory', type: 'checkbox' }
const serviceCenterSelfTransport = { title: '是否自营配送', prop: 'serviceCenterSelfTransport', type: 'checkbox' }
const disableShipping = { title: '禁用货运', prop: 'disableShipping', type: 'input' }
const deliveryNote = { title: '交货单备注', prop: 'deliveryNote', type: 'input' }
const referenceStandardShippingReq = { title: '参考标准客户出货要求文件', prop: 'referenceStandardShippingReq', type: 'checkbox' }
const specifiedDocument = { title: '其他随货资料', prop: 'specifiedDocument', type: 'checkbox' }
const exportProcessingZone = { title: '保税区/出口加工区', prop: 'exportProcessingZone', type: 'checkbox' }
const attachMsds = { title: '附MSDS', prop: 'attachMsds', type: 'select', dictKey: 'attachMsds' }
const attachTds = { title: '附TDS', prop: 'attachTds', type: 'select', dictKey: 'attachTds' }
const attachCoa = { title: '附COA', prop: 'attachCoa', type: 'select', dictKey: 'attachCoa' }
const attachOrder = { title: '附订单', prop: 'attachOrder', type: 'checkbox' }
const hideLogo = { title: '隐藏logo', prop: 'hideLogo', type: 'checkbox' }
const orderNote = { title: '订单备注', prop: 'orderNote', type: 'input' }
const shippingCondition = { title: '装运条件', prop: 'shippingCondition', type: 'select', dictKey: 'shippingConditions' }
const requireDepartment = { title: '需求部门', prop: 'requireDepartment', type: 'input' }
const certificateIdentification = { title: '合格证标识', prop: 'certificateIdentification', type: 'select', dictKey: 'certificateIdentification' }
const printNum = { title: '送货单份数', prop: 'printNum', type: 'input' }
const dnPaperReq = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'dnPaperReq', prop: 'dnPaperReq', title: '送货单纸张要求', multiple: false, type: 'select' }
const dnSignatureReq = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'dnSignatureReq', prop: 'dnSignatureReq', title: '送货单签章要求', type: 'select', multiple: true }
const dnIncidentalWay = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'dnIncidentalWay', prop: 'dnIncidentalWay', title: '送货单附带方式', type: 'select', multiple: false }
const otherLabelReq = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'otherLabelReq', prop: 'otherLabelReq', title: '其他标签要求', type: 'select', multiple: true, forceReset: true }
const fastenerLabelReq = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'fastenerLabelReq', prop: 'fastenerLabelReq', title: '紧固件标签要求', type: 'select', multiple: true, forceReset: true }
const labelPasteWay = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'labelPasteWay', prop: 'labelPasteWay', title: '标签张贴方式', type: 'select', multiple: true, forceReset: true }
const fastenerDetect = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'fastenerDetect', prop: 'fastenerDetect', title: '紧固件检测', type: 'select', multiple: false }
const fastenerSpecialPackageReq = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'fastenerSpecialPackageReq', prop: 'fastenerSpecialPackageReq', title: '紧固件特殊包装要求', type: 'select', multiple: false }
const specifiedReceiptDayOfWeek = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'specifiedReceiptDayOfWeek', prop: 'specifiedReceiptDayOfWeek', title: '固定送货周期', type: 'select', multiple: true, forceReset: true }
const specifiedReceiptTime = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'specifiedReceiptTime', prop: 'specifiedReceiptTime', title: '指定收货时间', type: 'select', multiple: true, forceReset: true }
const disableShippingNew = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'disableShipping', prop: 'disableShipping', title: '禁用货运', type: 'select', multiple: true, forceReset: true, limit: 7, clearable: true }
const designatedShipping = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'designatedShipping', prop: 'designatedShipping', title: '指定货运', type: 'select', multiple: true, forceReset: true, clearable: true }
const packagingReq = { title: '包装要求', prop: 'packagingReq', type: 'select', dictName: 'dictList', dictKey: 'packagingReq', multiple: true, forceReset: true, category: 'order-service' }
const vehicleReq = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'vehicleReq', prop: 'vehicleReq', title: '车辆要求', type: 'select', multiple: true, forceReset: true }
const signingReq = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'signingReq', prop: 'signingReq', title: '签收要求', type: 'select', multiple: false }
const deliveryRequirements = { dictName: 'dictList', dictKey: 'deliveryRequirement', prop: 'deliveryRequirements', title: '配送员要求', type: 'select', multiple: true }
const scheduleDelivery = { dictName: 'dictList', dictKey: 'scheduleDelivery', prop: 'scheduleDelivery', title: '预约送货方式', type: 'select', multiple: false }
const forkliftRelated = { category: 'order-service', dictName: 'orderServiceDict', dictKey: 'forkliftRelated', prop: 'forkliftRelated', title: '叉车相关', type: 'select', multiple: false }
const customerDeliveryConfirmed = { title: '发货需与客户确认/预约', prop: 'customerDeliveryConfirmed', type: 'checkbox' }
const deliveryWarehouseInfo = { category: 'order-service', title: '送货资料需要仓配信息', prop: 'deliveryWarehouseInfo', type: 'select', dictName: 'dictList', dictKey: 'deliveryWarehouseInfo', multiple: true }

// 财务信息
const paymentTerm = { title: '付款条件', prop: 'paymentTerm', type: 'select', dictKey: 'paymentTerms' }
const invoiceStatus = { title: '发票提交状态', prop: 'invoiceStatus', type: 'select', dictKey: 'invoiceStatus' }
const invoiceType = { title: '发票类型', prop: 'invoiceType', type: 'select', dictKey: 'invoiceType' }
const deliveryStatus = { title: '过账状态', prop: 'deliveryStatus', type: 'select', dictKey: 'deliveryStatus' }
const invoiceFreeze = { title: '开票冻结原因', prop: 'invoiceFreeze', type: 'select', dictKey: 'invoiceFreeze', style: 'tag' }
const invoicingByMail = { title: '凭邮件开票', prop: 'invoicingByMail', type: 'checkbox' }
const unbillReason = { title: '未开票原因', prop: 'unbillReason', type: 'select', dictKey: 'unInvoiceReason' }
const unbillReasonDesc = { title: '详细未开票原因', prop: 'unbillReasonDesc', type: 'input' }
const shippingInfo = { title: '寄票备注', prop: 'shippingInfo', type: 'input' }
const financeRedReason = { title: '红账原因', prop: 'financeRedReason', type: 'input' }
const financialNote = { title: '发票备注', prop: 'financialNote', type: 'input' }

export const DeliveryTextFields = [
  [
    { title: '收货联系人', prop: 'contactName', span: 6, type: 'text' },
    { title: '收货电话', prop: 'mobilephone', span: 6, type: 'text' },
    { title: '领用人', prop: 'demandUser', span: 6, type: 'text' },
    { title: '需求部门', prop: 'requireDepartment', span: 6, type: 'text' }
  ],
  [
    { title: '收货地址', prop: 'address', span: 24, type: 'text' }
  ],
  [
    { ...deliveryRequireTime, span: 6, type: 'text' },
    { ...deliveryTime, span: 6, type: 'text' }
  ],
  [
    { ...receiptTimeCategory, span: 6 },
    { ...serviceCenterSelfTransport, span: 6 },
    { ...exportProcessingZone, span: 6 },
    { ...hideLogo, span: 6 }
  ],
  [
    { ...specifiedDocument, span: 6 },
    { ...referenceStandardShippingReq, span: 6 },
    { ...attachOrder, span: 6 }
  ],
  [
    { ...attachMsds, span: 6 },
    { ...attachTds, span: 6 },
    { ...attachCoa, span: 6 },
    { ...certificateIdentification, span: 6 }
  ],
  [
    { ...deliverySlipTemplate, span: 6 },
    { ...printNum, span: 6 },
    { ...labelTemplate, span: 6 }
  ],
  [
    { ...deliveryUnloadingReq, span: 6 },
    { ...packagingReq, span: 6 },
    { ...signingBack, span: 6 },
    { ...shippingCondition, span: 6 }
  ],
  [
    { ...shipmentFreeze, span: 6 },
    { ...deliveryFreeze, span: 6 },
    { ...disableShipping, span: 6, type: 'text' }
  ],
  [
    { ...orderNote, span: 12, type: 'text' },
    { ...deliveryNote, span: 12, type: 'text' }
  ]
]

export const FinanceTextFields = [
  [
    { ...paymentTerm, span: 6 },
    { ...invoiceStatus, span: 6 },
    { ...invoiceType, span: 6 }
  ],
  [
    { ...deliveryStatus, span: 6 },
    { ...invoicingByMail, span: 6 },
    { ...unbillReason, span: 6 }
  ],
  [
    { ...invoiceFreeze, span: 6 }
  ],
  [
    { ...unbillReasonDesc, span: 12, type: 'text' },
    { ...shippingInfo, span: 12, type: 'text' }
  ],
  [
    { ...financeRedReason, span: 12, type: 'text' },
    { ...financialNote, span: 12, type: 'text' }
  ]
]

export const deliveryFields1 = [
  [
    { ...deliveryRequireTime, span: 8 },
    { ...deliveryUnloadingReq, span: 8 },
    { ...packagingReq, span: 8 }
  ],
  [
    { ...receiptTimeCategory, span: 6 },
    { ...serviceCenterSelfTransport, span: 6 },
    { ...exportProcessingZone, span: 6 },
    { ...hideLogo, span: 6 }
  ],
  [
    { ...specifiedDocument, span: 6 },
    { ...referenceStandardShippingReq, span: 6 },
    { ...attachOrder, span: 6 }
  ],
  [
    { ...attachMsds, span: 8 },
    { ...attachTds, span: 8 },
    { ...attachCoa, span: 8 }
  ],
  [
    { ...deliverySlipTemplate, span: 8 },
    { ...labelTemplate, span: 8 },
    { ...certificateIdentification, span: 8 }
  ],
  [
    { ...printNum, span: 8 },
    { ...shipmentFreeze, span: 8 },
    { ...deliveryFreeze, span: 8 }
  ],
  [
    { ...signingBack, span: 8 },
    { ...requireDepartment, span: 8 },
    { ...shippingCondition, span: 8 }
  ],
  [
    { ...disableShipping, span: 8 }
  ],
  [
    { ...orderNote, span: 12 },
    { ...deliveryNote, span: 12 }
  ]
]

export const deliveryFields2 = [
  [
    { ...deliveryRequireTime, span: 8, disabled: true },
    { ...deliveryTime, span: 8, disabled: false }
  ],
  [
    { ...receiptTimeCategory, span: 8, disabled: true },
    { ...serviceCenterSelfTransport, span: 8, disabled: true }
  ],
  [
    { ...deliverySlipTemplate, span: 8, disabled: true },
    { ...deliveryUnloadingReq, span: 8, disabled: true }
  ],
  [
    { ...packagingReq, span: 8, disabled: true },
    { ...shipmentFreeze, span: 8, disabled: true },
    { ...deliveryFreeze, span: 8, disabled: true },
    { ...signingBack, span: 8, disabled: true }
  ],
  [
    { ...disableShipping, span: 12, disabled: true },
    { ...deliveryNote, span: 12, disabled: true }
  ],
  [
    { ...orderNote, span: 24, disabled: true }
  ]
]
export const deliveryFields3 = [
  [
    { ...deliveryRequireTime, span: 8, disabled: true },
    { ...deliveryTime, span: 8, disabled: true }
  ],
  [
    { ...receiptTimeCategory, span: 8, disabled: true },
    { ...serviceCenterSelfTransport, span: 8, disabled: true }
  ],
  [
    { ...deliverySlipTemplate, span: 8, disabled: true },
    { ...deliveryUnloadingReq, span: 8, disabled: true }
  ],
  [
    { ...packagingReq, span: 8, disabled: true },
    { ...shipmentFreeze, span: 8, disabled: true },
    { ...deliveryFreeze, span: 8, disabled: true },
    { ...signingBack, span: 8, disabled: true }
  ],
  [
    { ...disableShipping, span: 12, disabled: true },
    { ...deliveryNote, span: 12, disabled: true }
  ],
  [
    { ...orderNote, span: 24, disabled: true }
  ]
]

export const financeFields1 = [
  [
    { ...paymentTerm, span: 8, disabled: true },
    { ...invoiceType, span: 8, disabled: false },
    { ...invoicingByMail, span: 8, disabled: false }
  ],
  [
    { ...unbillReason, span: 8, disabled: false }
  ],
  [
    { ...unbillReasonDesc, span: 12, disabled: false },
    { ...shippingInfo, span: 12, disabled: false }
  ],
  [
    { ...financeRedReason, span: 12, disabled: false },
    { ...financialNote, span: 12, disabled: false }
  ]
]

export const financeFields2 = [
  [
    { ...paymentTerm, span: 8, disabled: true },
    { ...invoiceStatus, span: 8, disabled: true },
    { ...invoiceType, span: 8, disabled: false }
  ],
  [
    { ...deliveryStatus, span: 8, disabled: true },
    { ...invoiceFreeze, span: 8, disabled: true }
  ],
  [
    { ...unbillReason, span: 8, disabled: false },
    { ...invoicingByMail, span: 8, disabled: false }
  ],
  [
    { ...unbillReasonDesc, span: 12, disabled: false },
    { ...shippingInfo, span: 12, disabled: false }
  ],
  [
    { ...financeRedReason, span: 12, disabled: false },
    { ...financialNote, span: 12, disabled: false }
  ]
]

export const financeFields3 = [
  [
    { ...paymentTerm, span: 8, disabled: true },
    { ...invoiceStatus, span: 8, disabled: true },
    { ...invoiceType, span: 8, disabled: false }
  ],
  [
    { ...deliveryStatus, span: 8, disabled: true },
    { ...invoiceFreeze, span: 8, disabled: true }
  ],
  [
    { ...unbillReason, span: 8, disabled: false },
    { ...invoicingByMail, span: 8, disabled: false }
  ],
  [
    { ...unbillReasonDesc, span: 12, disabled: false },
    { ...shippingInfo, span: 12, disabled: false }
  ],
  [
    { ...financeRedReason, span: 12, disabled: false },
    { ...financialNote, span: 12, disabled: false }
  ]
]

export const financeFields4 = [
  [
    { ...paymentTerm, span: 8, disabled: true },
    { ...invoiceStatus, span: 8, disabled: true },
    { ...invoiceType, span: 8, disabled: true }
  ],
  [
    { ...deliveryStatus, span: 8, disabled: true },
    { ...invoiceFreeze, span: 8, disabled: true }
  ],
  [
    { ...unbillReason, span: 8, disabled: true },
    { ...invoicingByMail, span: 8, disabled: true }
  ],
  [
    { ...unbillReasonDesc, span: 12, disabled: true },
    { ...shippingInfo, span: 12, disabled: true }
  ],
  [
    { ...financeRedReason, span: 12, disabled: true },
    { ...financialNote, span: 12, disabled: true }
  ]
]

export const deliveryState = {
  NEW: { label: '已创建', value: 'NEW' },
  CONFIRM: { label: '已下发', value: 'CONFIRM' },
  SENT: { label: '已发货', value: 'SENT' },
  CANCEL: { label: '已取消', value: 'CANCEL' }
}

// so详情 订单信息
export const OrderDeliveryDetailOrderInfoFields = [
  [
    { title: '客户名称', prop: 'customerName', span: 6, type: 'text' },
    { title: '销售组织', prop: 'company', span: 8, type: 'text' },
    { title: '直/分销渠道', prop: 'distributionChannel', span: 6, type: 'dict', dictKey: 'distributionChannel' }
  ],
  [
    { title: '收货联系人', prop: 'contactName', span: 6, type: 'text' },
    { title: '收货电话', prop: 'mobilephone', span: 6, type: 'text' },
    { ...customerDeliveryConfirmed, span: 6, disabled: true }
  ],
  [
    { title: '收货地址', prop: 'address', span: 24, type: 'text' }
  ],
  [
    { title: '需求部门', prop: 'requireDepartment', span: 6, type: 'text' },
    { title: '领用人', prop: 'demandUser', span: 6, type: 'text' },
    { ...deliveryRequireTime, span: 6, type: 'text' },
    { ...deliveryTime, span: 6, type: 'text' }
  ],
  [
    { ...shipmentFreeze, span: 12 },
    { ...deliveryFreeze, span: 12 }
  ]
];
// so详情 仓储信息-随货文件要求
export const OrderDeliveryDetailCargoFileFields = [
  [
    { ...attachOrder, span: 6 },
    { ...attachCoa, span: 6 },
    { ...attachMsds, span: 6 }
  ],
  [
    { ...specifiedDocument, span: 6 },
    // { ...referenceStandardShippingReq, span: 6 },
    { ...attachTds, span: 6 }
  ],
  [
    { ...deliverySlipTemplate, span: 6 },
    { ...printNum, span: 6 },
    { ...dnPaperReq, span: 6 }
  ],
  [
    { ...dnSignatureReq, span: 6 },
    { ...dnIncidentalWay, span: 6 }
  ],
  [
    { ...labelTemplate, span: 6 },
    { ...otherLabelReq, span: 6 },
    { ...fastenerLabelReq, span: 6 }
  ],
  [
    { ...labelPasteWay, span: 6 },
    { ...certificateIdentification, span: 6 },
    { ...signingBack, span: 6 }
  ]
];
// so详情 仓储信息-其他要求
export const OrderDeliveryDetailOtherReqFields = [
  [
    { ...hideLogo, span: 6 },
    { ...fastenerDetect, span: 6 }
  ],
  [
    { ...packagingReq, span: 6 },
    { ...fastenerSpecialPackageReq, span: 6 },
    { ...deliveryWarehouseInfo, span: 6 }
  ],
  [
    { ...orderNote, span: 12, type: 'text' },
    { ...deliveryNote, span: 12, type: 'text' }
  ]
];
// so详情 运输信息
export const OrderDeliveryDetailTransportFields = [
  [
    { ...receiptTimeCategory, span: 6 },
    { ...serviceCenterSelfTransport, span: 6 },
    { ...exportProcessingZone, span: 6 }
  ],
  [
    { ...specifiedReceiptDayOfWeek, span: 6 },
    { ...specifiedReceiptTime, span: 6 },
    { ...shippingCondition, span: 6 }
  ],
  [
    { ...deliveryUnloadingReq, span: 6 },
    { ...disableShippingNew, span: 6 },
    { ...designatedShipping, span: 6 }
  ],
  [
    { ...vehicleReq, span: 6 },
    { ...deliveryRequirements, span: 6 },
    { ...signingReq, span: 6 }
  ],
  [
    { ...scheduleDelivery, span: 6 },
    { ...forkliftRelated, span: 6 }
  ]
];

// so创建 订单信息
// TODO: 暂不调整，sto创建改版时一起修改
export const BaseinfoCreateFields = [
  CustomerCreateFields
];

export const OrderDeliveryCreateOrderInfoFields = [
  [
    { ...requireDepartment, span: 8 },
    { ...deliveryRequireTime, span: 8 },
    { ...shipmentFreeze, span: 8 }
  ]
];
export const OrderDeliveryCreateCargoFileFields = [
  [
    { ...attachOrder, span: 8 },
    { ...attachCoa, span: 8 },
    { ...attachMsds, span: 8 }
  ],
  [
    { ...specifiedDocument, span: 8 },
    // { ...referenceStandardShippingReq, span: 8 },
    { ...attachTds, span: 8 }
  ],
  [
    { ...deliverySlipTemplate, span: 8 },
    { ...printNum, span: 8 },
    { ...dnPaperReq, span: 8 }
  ],
  [
    { ...dnSignatureReq, span: 8 },
    { ...dnIncidentalWay, span: 8, clearable: true }
  ],
  [
    { ...labelTemplate, span: 8 },
    { ...otherLabelReq, span: 8 },
    { ...fastenerLabelReq, span: 8 }
  ],
  [
    { ...labelPasteWay, span: 8 },
    { ...certificateIdentification, span: 8 },
    { ...signingBack, span: 8 }
  ]
];
export const OrderDeliveryCreateOtherReqFields = [
  [
    { ...hideLogo, span: 8 },
    { ...fastenerDetect, span: 8 }
  ],
  [
    { ...packagingReq, span: 8 },
    { ...fastenerSpecialPackageReq, span: 8 },
    { ...deliveryWarehouseInfo, span: 8, forceReset: true }
  ],
  [
    { ...orderNote, span: 24 },
    { ...deliveryNote, span: 24 }
  ]
];
export const OrderDeliveryCreateTransportFields = [
  [
    { ...receiptTimeCategory, span: 8 },
    { ...serviceCenterSelfTransport, span: 8 },
    { ...exportProcessingZone, span: 8 }
  ],
  [
    { ...specifiedReceiptDayOfWeek, span: 8 },
    { ...specifiedReceiptTime, span: 8 },
    { ...shippingCondition, span: 8 }
  ],
  [
    { ...deliveryUnloadingReq, span: 8 },
    { ...disableShippingNew, span: 8 },
    { ...designatedShipping, span: 8 }
  ],
  [
    { ...vehicleReq, span: 8 },
    { ...deliveryRequirements, forceReset: true, span: 8, disabled: true },
    { ...signingReq, span: 8 }
  ],
  [
    { ...scheduleDelivery, span: 8, disabled: true },
    { ...forkliftRelated, span: 8, disabled: true }
  ]
];
