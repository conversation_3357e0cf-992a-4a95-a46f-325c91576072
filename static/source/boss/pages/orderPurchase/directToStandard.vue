<template>
  <div class="list-container">
    <el-form ref="ruleForm" :model="searchForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="公司代码" prop="companyCode">
            <el-select
              v-model="searchForm.companyCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in companyFactoryList"
                :key="item.companyCode"
                :label="item.companyCode+' '+item.companyName"
                :value="item.companyCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购姓名" prop="purchaseGroup">
            <el-select
              v-model="searchForm.purchaseGroup"
              filterable
              default-first-option
              clearable
              style="width:100%"
              placeholder="选择采购员"
            >
              <el-option
                v-for="item in purchaseList"
                :key="item.groupCode"
                :label="item.groupCode+' '+item.userName"
                :value="item.groupCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商" prop="supplierNo">
            <SelectSupplier
              clearable
              :data.sync="searchForm.supplier"
              @change="handleChange('supplier', $event)"
              />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="采购单号" prop="poNoList">
            <el-input
              v-model="searchForm.poNoList"
              placeholder="BOSS或SAP单号，最多20个采购订单同时查询，用空格或换行隔开"
              filterable
              clearable
              @input="changePoNo"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建日期" prop="createTime">
              <el-date-picker
                style="width: 100%"
                v-model="searchForm.createTime"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                clearable
              >
              </el-date-picker>
            </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      ref="aggreementGrid"
      height="710"
      id="aggreement_grid"
      row-id="poNo"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="listData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
      :checkbox-config="{checkMethod:checkMethod}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
    >
      <template v-slot:toolbar_font>
        <span :style="{color: 'red'}" v-show="selectList && selectList.length > 1">批量转单所有商品行删除原因将保持一致</span>
      </template>
      <template v-slot:toolbar_buttons>
        <el-form :inline="true" :model="transferForm" class="demo-form-inline">
          <el-form-item>
            <el-select
              v-model="transferForm.warehouseLocationCode"
              filterable
              default-first-option
              clearable
              style="width: 200px"
              placeholder="请选择仓库地点"
              @change="disable()"
            >
              <template v-if="warehouseListFilterByCompany.length > 0">
                <el-option
                  v-for="(item,index) in warehouseListFilterByCompany"
                  :key="index"
                  :label="`【工厂${item.factoryCode}】${item.warehouseLocationCode} ${item.warehouseLocationName}`"
                  :value="`${item.factoryCode}_${item.warehouseLocationCode}`">
                </el-option>
              </template>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" v-if="getButtonAuth('采购管理', '一键转标准')" :disabled="disabled" @click="toStandard">一键转标准</el-button>
          </el-form-item>
        </el-form>
      </template>
      <template v-slot:status_default="{ row }">
        <span :style="{color: 'green'}" v-if="row.status === 1">成功</span>
        <span :style="{color: 'red'}" v-else-if="row.status === -1">失败</span>
      </template>
      <template v-slot:supplierName_default="{ row }">
        {{row.supplierNo + ' ' + row.supplierName}}
      </template>
      <template v-slot:purchaseGroup_default="{ row }">
        {{row.purchaseGroup + ' ' + (purchaseList.find(item => item.groupCode === row.purchaseGroup) || {}).userName}}
      </template>
      <template v-slot:customerNo_default="{ row }">
      {{ row.customerNo + ' '+ row.customerName }}
    </template>
    </vxe-grid>
    <delete-dialog
      :show-dialog.sync="showDeleteDialog"
      :fileList=[]
      @getAttachmentList="convert"
      />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { safeRun } from '@/utils/index'
import { convertiblePO, convertPO, getFactoryWarehouse } from '@/api/mm'
import { getButtonAuth } from '@/utils/auth'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { getUserCompany } from '@/utils/mm'
import DeleteDialog from '@/pages/orderPurchase/components/common/DeleteDialog'

const columns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'status',
    title: '结果',
    width: 80,
    slots: {
      default: 'status_default'
    }
  },
  {
    field: 'errorMsg',
    title: '消息',
    width: 120
  },
  {
    field: 'poNo',
    title: '采购订单号',
    width: 120
  },
  {
    field: 'itemNo',
    title: '采购订单行',
    width: 120
  },
  {
    field: 'factoryCode',
    title: '工厂',
    width: 110
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    width: 80
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    width: 200
  },
  {
    field: 'itemQuantity',
    title: '订单数量',
    width: 80
  },
  {
    field: 'itemUnit',
    title: '订单单位',
    width: 80
  },
  {
    field: 'supplierName',
    title: '供应商',
    width: 200,
    slots: {
      default: 'supplierName_default'
    }
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    width: 130,
    slots: {
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'soNo',
    title: '关联销售单号',
    width: 120
  },
  {
    field: 'customerName',
    title: '客户',
    width: 80,
    slots: {
      default: 'customerNo_default'
    }
  },
  {
    field: 'supplierMaterialNo',
    title: '供应商物料号',
    width: 120
  }
]

export default {
  name: 'directToStandard',
  components: { SelectSupplier, DeleteDialog },
  data () {
    return {
      disabled: true,
      selectList: [],
      showDeleteDialog: false,
      searchForm: {
        companyCode: '',
        purchaseGroup: '',
        supplier: {},
        supplierNo: '',
        poNoList: '',
        createTime: []
      },
      transferForm: {
        warehouseLocationCode: ''
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          tools: 'toolbar_buttons',
          buttons: 'toolbar_font'
        }
      },
      columns,
      listData: [],
      convertList: [],
      warehouseList: [],
      factoryList: [],
      warehouseListFilterByCompany: [],
      rules: {
        companyCode: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ],
        purchaseGroup: [
          { required: true, message: '请选择采购员', trigger: 'change' }
        ]
      }
    }
  },
  created () {
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    if (!Object.keys(this.dictList).length) {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (this.purchaseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    getFactoryWarehouse({ warehouseType: 1 }).then(data => {
      this.warehouseList = data
    })
    this.getUserCompany()
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {},
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      purchaseList: state => state.orderPurchase.purchaseList
    })
  },
  watch: {
    'searchForm.companyCode': function (oldVal, newVal) {
      this.factoryList = (this.companyFactoryList.find(item => item.companyCode === this.searchForm.companyCode) || {}).factoryList || []
      console.log(this.factoryList);
    },
    factoryList: {
      handler: function (newVal) {
        if (this.warehouseListFilterByCompany.length !== 0) {
          this.warehouseListFilterByCompany = []
          this.transferForm.warehouseLocationCode = ''
        }
        for (let i = 0; i < newVal.length; i++) {
          for (let j = 0; j < this.warehouseList.length; j++) {
            if (newVal[i].factoryCode === this.warehouseList[j].factoryCode) {
              this.warehouseListFilterByCompany.push(this.warehouseList[j])
            }
          }
        }
      },
      deep: true,
      immediate: true
    },
    warehouseList: {
      handler: function (newVal) {
        if (newVal) {
          this.warehouseListFilterByCompany = newVal
        }
      },
      deep: true
    }
  },
  methods: {
    async getUserCompany() {
      const defaultCompany = await getUserCompany();
      this.searchForm.companyCode = defaultCompany;
    },
    getButtonAuth,
    changePoNo () {
      if (this.searchForm.poNoList) {
        this.rules = {
          companyCode: [
            { required: false }
          ],
          purchaseGroup: [
            { required: false }
          ]
        }
        this.warehouseListFilterByCompany = this.warehouseList
      } else {
        this.rules = {
          companyCode: [
            { required: true, message: '请选择公司', trigger: 'change' }
          ],
          purchaseGroup: [
            { required: true, message: '请选择采购员', trigger: 'change' }
          ]
        }
      }
    },
    handleSearch () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.getOrderList()
          this.selectList = []
        } else {
          return false;
        }
      })
    },
    formatParams(params) {
      let form = { ...params };
      form.poNoList = safeRun(() =>
        form.poNoList
          .split(/\s+|,|，/).filter((e) => e)
      );
      delete form.supplier
      delete form.createTime
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.poNoList.length > 20) {
          ret = false
          this.$message.error('采购订单号不能超过20个！')
        }
      })
      return ret
    },
    async getOrderList () {
      try {
        if (this.searchForm.createTime === null) {
          this.searchForm.createTime = []
        }
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return
        params = {
          ...params,
          type: 2,
          orderCreateDateStart: this.searchForm.createTime[0],
          orderCreateDateEnd: this.searchForm.createTime[1]
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await convertiblePO(params)
        this.listData = res.filter(item => item.isDeleted === 0)
        this.convertList = res
        this.tableLoading = false
        this.searchLoading = false
        if (this.listData.length === 0) {
          this.$message.info('无符合条件的采购订单')
        }
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
      }
    },
    selectChange ({ checked, records, row }) {
      if (!checked) {
        this.$refs.aggreementGrid.setCheckboxRow(this.selectList.filter(item => item.poNo === row.poNo), checked);
        this.reelected(checked, records, row)
      } else {
        this.reelected(checked, records, row)
        this.$refs.aggreementGrid.setCheckboxRow(this.selectList, checked)
      }
    },
    selectAll ({ checked, records }) {
      if (checked) {
        this.selectList = this.convertList
      } else {
        this.selectList = []
      }
      this.disable()
    },
    reelected (checked, records, row) {
      if (checked) {
        this.selectList.push(...this.convertList.filter(item => item.poNo === row.poNo))
      } else if (!checked && row) {
        this.selectList = this.selectList.filter(item => item.poNo !== row.poNo)
      }
      this.disable()
    },
    disable () {
      if (this.selectList.length > 0 && this.transferForm.warehouseLocationCode !== '') {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    toStandard () {
      let repeatArr = this.selectList.map(item => item.poNo)
      repeatArr = Array.from(new Set(repeatArr))
      if (repeatArr.length > 1) {
        return this.$confirm('<div>当前勾选了多张采购订单，是否确认均转为相同库存地点的标准订单？</div><div>（批量转单所有商品行删除原因将保持一致）</div>', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        }).then(() => {
          // this.convert()
          this.showDeleteDialog = true
        }).catch(() => {
        });
      } else if (repeatArr.length === 1) {
        this.showDeleteDialog = true
      }
    },
    async convert (deleteForm) {
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      console.log('deleteForm', deleteForm)
      this.selectList = this.selectList.filter(item => item.status !== 1)
      let { warehouseLocationCode = '' } = this.transferForm
      warehouseLocationCode = warehouseLocationCode.split('_')
      if (warehouseLocationCode && warehouseLocationCode.length === 2) {
        warehouseLocationCode = warehouseLocationCode[1]
      }
      const itemList = this.selectList.map(item => {
        return {
          ...item,
          deleteReason: deleteForm.deleteReason,
          attachmentList: deleteForm.attachmentList
        }
      })
      const data = {
        itemList,
        updateUser: '',
        type: 2,
        warehouseLocation: warehouseLocationCode
      }
      data.updateUser = window.CUR_DATA.user && window.CUR_DATA.user.name
      try {
        const res = await convertPO(data)
        this.listData = this.listData.map(value => {
          let idx = this.selectList.findIndex(item => item.poNo === value.poNo && item.itemNo === value.itemNo
          )
          if (idx !== -1) {
            value.status = res[idx].status
            value.errorMsg = res[idx].errorMsg
          }
          return value
        })
        this.selectList = this.selectList.map(value => {
          let idx = this.selectList.findIndex(item => item.poNo === value.poNo && item.itemNo === value.itemNo
          )
          if (idx !== -1) {
            value.status = res[idx].status
            value.errorMsg = res[idx].errorMsg
          }
          return value
        })
        this.$refs.aggreementGrid.setCheckboxRow(this.selectList, false);
        this.selectList = []
        this.disable()
        loading.close()
      } catch (error) {
        loading.close()
        console.log(error);
      }
    },
    checkMethod ({ row }) {
      if (row.status === 1) {
        return false
      } else {
        return true
      }
    },
    handleChange (type, event) {
      if (type === 'supplier') {
        this.searchForm.supplierNo = event.supplierNo
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.list-container {
  margin: 10px 20px;
}
.demo-form-inline {
  height: 34px;
}
</style>
