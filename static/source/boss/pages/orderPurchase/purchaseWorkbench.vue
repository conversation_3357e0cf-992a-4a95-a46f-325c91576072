<template>
  <div class="purchase-workbench" v-loading="loading.pageLoading" >
    <div class="search-filter">
      <el-tabs v-model="activeName" type="card" @tab-click="handleTabChange">
        <el-tab-pane
          v-for="(tab, index) in tabsList"
          :key="index"
          :label="tab.name"
          :name="tab.value"></el-tab-pane>
      </el-tabs>
      <el-form
        v-show="showFilter=='el-icon-arrow-up'"
        :model="searchFormList[activeName]"
        :rules="rules"
        ref="searchForm"
        style="width: 100%"
        label-suffix=":"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col v-for="(item,index) in renderFilter()" :key="index+item.prop" :span="item.span || 8" style="height: 52px;">
            <el-form-item :label="item.label" :prop="item.prop">
              <el-input
                v-if="item.type === 'input'"
                v-model="searchFormList[activeName][item.prop]"
                clearable
                :placeholder="item.placeholder"
              />
              <template v-if="item.type ==='select'">
                <!-- 未下采购单采购组特殊逻辑 -->
                <el-select
                  v-if="isAbWithPower(item.prop, true)"
                  v-model="searchFormList[activeName][item.prop]"
                  allow-create
                  filterable
                  collapse-tags
                  default-first-option
                  clearable
                  multiple
                  style="width: 100%"
                  placeholder="请选择采购员"
                >
                  <el-option
                    v-for="option in buildOptions(item.enums)"
                    :key="option.value"
                    :label="item.showCode ? option.value+' '+option.name : option.name"
                    :value="option.value">
                  </el-option>
                </el-select>
                <SelectPurchaseGroup
                  v-else-if="isAbWithPower(item.prop, false)"
                  :data.sync="searchFormList[activeName][item.prop]"
                  @change="handelPurchaseGroup"
                  v-on="$listeners"
                />
                <el-select
                  v-else
                  v-model="searchFormList[activeName][item.prop]"
                  style="width: 100%"
                  filterable
                  collapse-tags
                  :multiple="item.multiple"
                  :clearable="item.clearable"
                  default-first-option
                >
                  <el-option
                    v-for="option in buildOptions(item.enums)"
                    :key="option.value"
                    :label="item.showCode ? option.value+' '+option.name : option.name"
                    :value="option.value">
                  </el-option>
                </el-select>
              </template>
              <el-date-picker
                v-if="item.type === 'date'"
                v-model="searchFormList[activeName][item.prop]"
                style="width:100%"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始"
                end-placeholder="结束"
              >
              </el-date-picker>
              <el-select
                 v-if="item.type === 'ifVpi'"
                 v-model="searchFormList[activeName][item.prop]"
                clearable
                style="width: 100%"
                placeholder="请选择"
              >
                <el-option :key="1" label="是" :value="1" />
                <el-option :key="0" label="否" :value="0" />
              </el-select>
            <el-select
              v-if="item.type === 'ifUrgent'"
              v-model="searchFormList[activeName][item.prop]"
              style="width: 100%"
              placeholder="请选择"
            >
              <el-option :key="0" label="全量显示" :value="null" />
              <el-option :key="1" label="仅显示急单" :value="1" />
              <el-option :key="2" label="仅显示非急单" :value="0" />
            </el-select>
              <SelectSupplier
                v-if="item.type === 'supplier'"
                :multiple="item.multiple"
                :data.sync="searchFormList[activeName][item.prop]"
                collapse-tags
              />
              <SelectBrand
                  v-if="item.type === 'brand'"
                  clearable
                  style="width:100%"
                  :data.sync="searchFormList[activeName].brands"
                  @change="handleChange('brandId', $event)" />
              <RemoteCustomer
                style="width:100%"
                v-if="item.type === 'customer'"
                v-model="searchFormList[activeName].customerNo"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="search-button-group">
        <el-button type="primary" :loading="loading.searchLoading" @click="() => handleSearch(false, true)">查询</el-button>
        <el-button type="default" @click="resetFields">重置</el-button>
        <el-button type="default" :icon="showFilter" @click="toggleFilter">{{showFilter=='el-icon-arrow-up'?'收起':'展开'}}</el-button>
      </div>
    </div>
    <div class="search-result">
      <div class="result-container">
        <WorkbenchGrid
          ref="grid"
          v-loading="!loading.pageLoading && loading.tableLoading"
          element-loading-text="加载中，请不要刷新浏览器"
          :activeName="activeName"
          :isAbnormalTab="isAbnormalTab"
          :orderType="orderType"
          :dataList="dataList"
          :rawDataList="rawDataList"
          :tableInfo="table"
          :tableLoading="loading.tableLoading"
          :oneProductAuth="oneProductAuth"
          @changeTableLoading="changeTableLoading"
          :reasonOptions="reasonOptions"
          @refresh="() =>handleSearch(false, true)"
          @sortTable="handleSortChange"
          @selection-change="selectionChange"
        >
        <template slot="button-group">
          <span class="tip">
            <el-popover
              v-if="renderTip()"
              placement="right"
              title=""
              width="400"
              trigger="hover"
              >
              <span>
                {{renderTip()}}
              </span>
              <span slot="reference">
                待办说明 <i class="el-icon-question"></i>
              </span>
            </el-popover>
          </span>
          <span class="button-group">
            <el-button v-if="isAbnormalTab" :disabled="!selections.length" type="success" @click="showAbnormal">未采购说明原因</el-button>
            <el-button v-if="isAbnormalTab" :disabled="!dataList.length" type="primary" @click="exportAbnormalDetail" style="margin-right: 10px; ">导出明细</el-button>
            <el-dropdown style="margin-right: 10px; " @command="submitUpload" v-if="isAbnormalTab">
              <el-button v-if="isAbnormalTab"  type="primary" >
                批量更新未下单原因
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="download">导出批量创建模板</el-dropdown-item>
                <el-dropdown-item command="upload">
                  <el-upload
                    style="display:inline-block;"
                    accept=".xlsx"
                    :action="uploadAction"
                    :show-file-list="false"
                    :on-success="handleSuccess"
                    :on-error="handleError"
                    :before-upload="handleBeforeUpload"
                  >
                    <span>上传批量创建模板</span>
                  </el-upload>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button v-if="!isAbnormalTab && getButtonAuth('跟单报表', '保存修改')" type="success" @click="saveChange">保存修改</el-button>
            <el-button v-if="!isAbnormalTab && getButtonAuth('跟单报表', '导出明细')" type="primary" :loading="loading.exportLoading" @click="exportDetail">导出明细</el-button>
            <el-button v-if="getButtonAuth('跟单报表', '批量更新跟单') && ['2', '3', '5', '6', '12'].includes(activeName)" type="primary" @click="updateVisible('batchUpdateVisible', true)">批量更新跟单</el-button>
          </span>
        </template>
        </WorkbenchGrid>
      </div>
      <div class="pagination" >
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="table.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="table.total">
        </el-pagination>
      </div>
    </div>
    <ResultDialog
      :dataResult="dataResult"
      :updateVisible="updateVisible"
      :resultVisible="resultVisible"
    />
    <AbnormalReason
      :dataResult="dataResult"
      :updateVisible="updateVisible"
      :visible="showAbnormalReason"
      :reasonOptions="reasonOptions"
      @submitReason="submitReason"
    />
    <BatchUpdate
      @uploadSuccess="() =>handleSearch(false, true)"
      :updateVisible="updateVisible"
      :type="activeName"
      :batchUpdateVisible="batchUpdateVisible"
    />
  </div>
</template>
<script>
import { pagePOWorkbench, getPendingOrderList, exportPOWorkbench, batchEditTrackingOrder, saveReason, saveWorkOrderNo, getMMOneProductAuth } from '@/api/mm'
import { getButtonAuth } from '@/utils/auth'
import { getAllDictList, buildOptions } from '@/utils/mm'
import { deepClone, safeRun, sensors } from '@/utils/index'
import RemoteCustomer from '@/components/SearchFields/consCustomer'
import BatchUpdate from './components/trackingOrder/BatchUpdateDialog'
import SelectPurchaseGroup from '@/pages/mrp/components/selectPurchaseGroup.vue'
import * as shortid from 'shortid'
import * as moment from 'moment'
import { mapState } from 'vuex'
import WorkbenchGrid from './components/purchaseWorkbench/grid.vue'
import AbnormalReason from './components/purchaseWorkbench/AbnormalReason'
import ResultDialog from './components/trackingOrder/ResultDialog'
import SelectSupplier from './components/common/SelectSupplier'
import { tabTips, tabsLists, workbenchFilters, abnormalFilters, searchFormList, toBeReceivedFilters } from './constants/index'
import { setPower } from '@/pages/mrp/utils'
import { writeFile } from '@boss/excel'
import { getDictionaryList } from '@/api/purchaseList.js'
import SelectBrand from '@/pages/mrp/components/selectBrand.vue'
import { abnormalPurchase } from '@/pages/orderPurchase/components/purchaseWorkbench/grid.js'
import {
  getReasonTypeOneList,
  getReasonTypeTwoList
} from '@/filters/index.js'

export default {
  name: 'purchaseWorkbench',
  components: {
    SelectSupplier, RemoteCustomer, WorkbenchGrid, ResultDialog, AbnormalReason, SelectPurchaseGroup, BatchUpdate, SelectBrand
  },
  provide () {
    return {
      setDataResult: this.setDataResult
    }
  },
  data() {
    return {
      oneProductAuth: false,
      uploadLoading: null,
      abnormalPurchase,
      showFilter: 'el-icon-arrow-up',
      tabTips,
      showAbnormalReason: false,
      batchUpdateVisible: false,
      tabCache: {
        formCache: {},
        dataCache: {}
      },
      selections: [],
      searchFormList: searchFormList,
      omsFilterProps: [
        'customerNo',
        'skuNo',
        'workOrderNo',
        'soNo',
        'deliveryDate',
        'soCreateDate',
        'approvalDate'
      ],
      pmsFilterProps: [
        'supplierNoList',
        'skuNo',
        'workOrderNo',
        'poNo',
        'orderDate',
        'openPoStatus'
      ],
      tabsList: getButtonAuth('采购工作台', '未下采购订单') ? tabsLists : tabsLists.slice(1, 15),
      activeName: getButtonAuth('采购工作台', '未下采购订单') ? '1' : '2',
      loading: {
        searchLoading: false,
        exportLoading: false,
        tableLoading: false,
        pageLoading: false
      },
      dataList: [],
      rawDataList: [],
      table: {
        pageNo: 1,
        pageSize: 50,
        total: 1
      },
      sortInfo: {
        orderByField: '',
        orderDirection: ''
      },
      tabStack: [],
      dataResult: {},
      resultVisible: false,
      reasonOptions: []
    };
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole
    }),
    refsForm () {
      return this.$refs.searchForm
    },
    refsGrid () {
      return this.$refs.grid
    },
    refsTable() {
      return this.$refs.grid.$refs.purchaseWorkbenchGrid
    },
    orderType () {
      return this.activeName
    },
    isAbnormalTab () {
      return this.activeName === '1'
    },
    rules () {
      const rule = {
        purchaseGroupList: [{ required: true, message: '请选择采购组', trigger: ['blur', 'change'] }]
      }
      return this.isAbnormalTab ? rule : {}
    },
    uploadAction () {
      return '/internal-api/mm/uploadPendingReasonExcel'
    },
    // 调拨订单
    isPickingAreaPurchaseTab () {
      return this.activeName === '8'
    },
    // 供应商未确认
    isNotConfirm() {
      return this.activeName === '2'
    },
    // 保供订单
    isGuaranteedStatus() {
      return this.activeName === '14'
    },
    isToBeReceiveTab () {
      return this.activeName === '13'
    }
    },
  async created() {
    await getAllDictList(this)
    await this.getOneProductAuth()
    window.demo = this
    getDictionaryList({ includeKey: 'pendingReason' }).then(data => {
      this.reasonOptions = data
    })
    this.findDefaultPurchaseGroup()
    if (this.$route.query.tab) {
      this.activeName = this.$route.query.tab
    }
    this.initSearch(false, false)
    setTimeout(() => {
      const label = this.tabsList.find(item => item.value === this.activeName)
      sensors('PurchaseWorkbenchLabelPageView', { label_name: label.name || '' })
    }, 200)
  },
  methods: {
    getButtonAuth,
    setPower,
    handleChange(type, event) {
      if (type === 'brandId') {
        this.searchFormList[this.activeName].brandId = event.brandId
      }
    },
    async getOneProductAuth () {
      await getMMOneProductAuth()
        .then((data) => {
          if (data) {
            this.oneProductAuth = data.enable
          } else {
            this.oneProductAuth = false
          }
        })
    },
    handleSuccess (res) {
      if (this.uploadLoading) {
        this.uploadLoading.close()
      }
      if (res && res.code === 200) {
        this.$message({
          message: res.data,
          type: 'success'
        });
        this.initSearch(false, true)
      }
      if (res && res.code !== 200 && res.msg) {
        this.$alert(res.msg, '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.uploadLoading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleError () {
      if (this.uploadLoading) {
        this.uploadLoading.close()
      }
    },
    handelPurchaseGroup (list) {
      this.searchFormList[this.activeName].purchaseGroupList = list
    },
    isAbWithPower (prop, auth) {
      const pow = (this.isAbnormalTab && /purchaseGroup/.test(prop) && (auth ? setPower(this.userRole) : !setPower(this.userRole)))
      return pow
    },
    findDefaultPurchaseGroup () {
      const options = this.buildOptions('purchaseGroupList')
      const username = window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
      const purchaseGroup = options.find(option => option.securityUsername === username)
      this.initApprovalDate(purchaseGroup)
      if (purchaseGroup && purchaseGroup.value) {
        this.searchFormList[this.activeName].purchaseGroupList = [purchaseGroup.value]
      }
    },
    initApprovalDate (purchaseGroup) {
      const now = moment().format('YYYY-MM-DD')
      // const after7 = moment().add(7, 'days').format('YYYY-MM-DD')
      // const after30 = moment().add(30, 'days').format('YYYY-MM-DD')
      // if (purchaseGroup && purchaseGroup.value.indexOf('X') !== -1) {
      //   this.searchFormList[this.activeName].approvalDate = [now, after7]
      // }
      // this.searchFormList[this.activeName].approvalDate = [now, after30]
      this.searchFormList[this.activeName].approvalDate = ['2022-01-01', now]
    },
    showAbnormal () {
      if (this.selections.some(item => item.detailPendingReason !== 0)) {
        return this.$confirm('勾选行存在未下单原因，是否要更新？', '操作提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.showAbnormalReason = true
          })
      }
      this.showAbnormalReason = true
    },
    renderTip () {
      return this.tabTips[this.activeName]
    },
    submitReason (data) {
      const submitData = {
        ...data,
        soList: this.selections.map(i => ({
          soNo: i.soNo,
          soItemNo: i.soItemNo
        }))
      }
      this.loading.pageLoading = true
      saveReason(submitData)
        .then(res => {
          this.initSearch(false, true)
        })
        .finally(() => {
          this.loading.pageLoading = false
        })
    },
    saveWorkOrderNo () {
      console.log(saveWorkOrderNo)
    },
    toggleFilter () {
      this.showFilter = this.showFilter === 'el-icon-arrow-down' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
      safeRun(() => {
        this.refsGrid.calcLeftHeight()
      })
    },
    updateVisible (type, visible) {
      this[type] = visible
      if (type === 'visible' && !visible) {
        this.dataResult = {
          failCount: 0,
          successCount: 0,
          detailList: []
        }
      }
    },
    selectionChange (val) {
      this.selections = val
    },
    handleSortChange(order, property) {
      if (order === null) {
        this.sortInfo = {}
        this.initSearch(false, true)
        return
      }
      this.sortInfo.orderDirection = order.toUpperCase()
      switch (property) {
        case 'createDate':
          this.sortInfo.orderByField = 'poCreateTime'; break;
        case 'planDeliveryDate':
          this.sortInfo.orderByField = 'planDeliveryDate'; break;
        case 'approvalDate':
          this.sortInfo.orderByField = 'approvalDate'; break;
        case 'deliveryDate':
          this.sortInfo.orderByField = 'deliveryDate'; break;
        case 'soCreateDate':
          this.sortInfo.orderByField = 'soCreateDate'; break;
        default:
          this.sortInfo = {}
      }
      this.initSearch(false, true)
    },
    formatChange (list) {
      let data = []
      const props = [ 'planDeliveryDate', 'tag', 'isConfirmed', 'remark', 'isCustomerAccept' ]
      list.forEach(item => {
        const findItem = this.rawDataList.find(raw => raw.shortid === item.shortid)
        const tmp = {}
        props.forEach(prop => {
          if (item[prop] !== findItem[prop]) {
            tmp[prop] = item[prop]
          }
        })
        if (tmp.planDeliveryDate) {
          tmp.planDeliveryDate = tmp.planDeliveryDate.replace(/\//gmi, '-')
        }
        tmp.poNo = item.poNo
        tmp.itemNo = item.itemNo
        tmp.planNo = item.planNo
        data.push(tmp)
      })
      return data
    },
    abnormalReason () {

    },
    saveChange () {
      const changedList = this.dataList.filter(item => item.changed)
      if (!changedList.length) {
        return this.$message.info('没有需要修改的行！')
      }
      this.$confirm('确认保存修改行吗？', '操作提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const submitData = {
          updateUser: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name,
          rows: this.formatChange(changedList),
          type: Number(this.orderType || 1)
        }
        this.loading.pageLoading = true
        batchEditTrackingOrder(submitData)
          .then(res => {
            if (!res) return this.$message.error('保存出错！')
            if (typeof res === 'string') {
              this.$alert(res, '操作提示', {
                confirmButtonText: '确认',
                type: 'info'
              }).then(() => {
                this.handleSearch(false, true)
              })
              return
            }
            this.dataResult = res
            this.resultVisible = true
          })
          .finally(() => {
            this.loading.pageLoading = false
            this.initSearch(false, true)
          })
      })
    },
    exportDetail () {
      const form = this.formatForm(this.searchFormList[this.activeName])
      const data = { ...form }
      this.loading.exportLoading = true
      exportPOWorkbench(data)
        .then(res => {
          if (res === null) {
            this.$message.success('导出成功，请在下载专区查看导出内容！')
            setTimeout(() => {
              this.$router.push({
                path: '/purchaseReport/downLoadList'
              })
            }, 600)
          }
          console.log(res)
        })
        .finally(() => {
          this.loading.exportLoading = false
        })
      const label = this.tabsList.find(item => item.value === this.activeName)
      sensors('PurchaseWorkbenchExportOrderLine', { label_name: label.name || '' })
    },
    reasonType (row) {
      let list = getReasonTypeOneList(this.reasonOptions) || []
      const find = list.find(item => item.value === row.pendingReason)
      return (find && find.label) || ''
    },
    reasonTypeDetail (row) {
      let list = getReasonTypeTwoList(this.reasonOptions, row.pendingReason) || []
      const find = list.find(item => item.value === row.detailPendingReason)
      return (find && find.label) || ''
    },
    exportAbnormalDetail () {
      const form = this.formatOmsForm(this.searchFormList[this.activeName])
      if (form.purchaseGroup) {
        const data = { pageNo: 1, pageSize: -1, ...form }
        getPendingOrderList(data)
          .then(data => {
            if (!data) return
            if (Array.isArray(data.records)) {
              const showAbnormalPurchase = this.abnormalPurchase.slice(1, this.abnormalPurchase.length - 1)
              const mapping = { id: 'ID' }
              showAbnormalPurchase.map(item => { mapping[item.field] = item.title })
              const allList = data.records.map(item => {
                const itemCopy = { ...item }
                Object.keys(itemCopy).forEach((key) => {
                  if (mapping[key]) {
                    if (mapping[key] === '未采购说明') {
                      itemCopy[mapping[key]] = this.reasonType(item) + ' ' + this.reasonTypeDetail(item) + ' ' + itemCopy[key]
                    } else if (mapping[key] === 'ID') {
                      itemCopy[mapping[key]] = String(itemCopy[key])
                    } else {
                      itemCopy[mapping[key]] = itemCopy[key]
                    }
                  }
                  delete itemCopy[key]
                })
                itemCopy['未采数量'] = (parseFloat(item.quantity) - parseFloat(item.clearedQuantity)).toFixed(2)
                return itemCopy
              })
              writeFile(allList, `未下采购单 ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: Object.values(mapping) })
            } else {
              this.$message.error(data.msg)
            }
          })
          const label = this.tabsList.find(item => item.value === this.activeName)
          sensors('PurchaseWorkbenchExportOrderLine', { label_name: label.name || '' })
      } else {
        this.$message.error('至少选择一个采购组！')
      }
    },
    submitUpload (command) {
      switch (command) {
        // 导出模版
        case 'download':
          const env = window.CUR_DATA.env.toLowerCase() || 'pro'
          const url = `${env !== 'pro' ? 'https://files-uat.zkh360.com' : 'https://files.zkh360.com'}/mrp/%E5%BC%82%E5%B8%B8%E6%9C%AA%E4%B8%8B%E5%8D%95.xlsx`
          window.open(url)
          break;
      }
    },
    resetForm (onlyDiff) {
      if (onlyDiff) {
        this.searchFormList[this.activeName].supplierNoList = []
        this.searchFormList[this.activeName].customerNo = ''
      } else {
        this.searchFormList[this.activeName] = {
          supplierNoList: [],
          skuNo: '',
          workOrderNo: '',
          poNo: '',
          orderDate: [],
          workOrderStatus: '',
          purchaseGroupList: [],
          openPoStatus: '',

          soNo: '',
          customerNo: '',
          deliveryDate: [],
          approvalDate: [],
          soCreateDate: [],
          approveStep: '',
          orderTypeList: [],
          warehouseLocationList: []
        }
      }
      if (this.isGuaranteedStatus) {
        this.searchFormList[this.activeName].guaranteedStatusList = []
      }
    },
    resetFields () {
      this.refsForm.resetFields()
      this.resetForm()
    },
    buildOptions (enums) {
      let options = buildOptions(enums)
      if (enums === 'Boolean') {
        options = [
          { value: 1, name: '是' },
          { value: 0, name: '否' }
        ]
      }
      if (enums === 'workOrderStatus') {
        options = [
          { value: 1, name: '已创建工单' },
          { value: 2, name: '未创建工单' }
        ]
        if (this.isAbnormalTab) {
          options = [
            { value: 1, name: '已创建工单' },
            { value: 0, name: '未创建工单' }
          ]
        }
      }
      return options
    },
    renderFilter () {
      if (this.isAbnormalTab) {
        return abnormalFilters
      }
      let finalFilter = this.isAbnormalTab ? abnormalFilters : this.isToBeReceiveTab ? toBeReceivedFilters : workbenchFilters
      if (this.isGuaranteedStatus) {
        // finalFilter.slice(0, 12).splice(10, 1)
        return finalFilter.slice(0, 10).concat(finalFilter[11])
      }
      return this.isPickingAreaPurchaseTab ? finalFilter.slice(0, 9) : (this.isNotConfirm ? finalFilter.slice(0, 11) : this.isToBeReceiveTab ? finalFilter : finalFilter.slice(0, 10))
    },
    pushTabStack (tab) {
      if (this.tabStack.length === 11) {
        this.tabStack.shift()
      }
      this.tabStack.push(tab)
    },
    tabStackPeek () {
      return this.tabStack[this.tabStack.length - 1]
    },
    handleTabChange (tab) {
      const last = this.tabStackPeek()
      this.pushTabStack(tab.paneName)
      // if (this.isToBeReceiveTab) {
      //   this.searchFormList[this.activeName] = toBeReceivedFormList
      // }
      if (tab.paneName !== last) {
        if (tab.paneName === '1' || last === '1') {
          // 切换未下采购tab 搜索条件与其他tab不一致
          this.resetForm('onlyDiff')
        }
        if (tab.paneName === '1') {
          // 如果没有采购组 清空结果, 不触发查询
          if (!this.searchFormList[this.activeName].purchaseGroupList.length) {
            this.dataList = []
            this.rawDataList = []
            return
          }
        }
        if (this.searchFormList[this.activeName].purchaseGroupList.length === 0) {
          this.findDefaultPurchaseGroup()
        }
        setTimeout(() => this.handleSearch(true, false), 30)
      }
      const label = this.tabsList.find(item => item.value === this.activeName)
      sensors('PurchaseWorkbenchLabelPageView', { label_name: label.name || '' })
    },
    formatForm(form) {
      const data = deepClone(form)
      if (Array.isArray(data.orderDate) && data.orderDate.length) {
        data.poCreateDateStart = data.orderDate[0]
        data.poCreateDateEnd = data.orderDate[1]
      }
      const { orderByField, orderDirection } = this.sortInfo
      if (orderByField && orderDirection) {
        data.orderByField = orderByField
        data.orderDirection = orderDirection
      } else {
        data.orderByField = 'poCreateTime'
        data.orderDirection = 'DESC'
      }
      delete data.orderDate
      data.type = this.orderType
      if (data.approveStep) {
        data.approveStep = Number(data.approveStep)
      }
      if (data.skuNo) {
        data.skuNoList = data.skuNo.split(/\s+|\n|,|，/gmi).filter(Boolean)
      }
      if (data.poNo) {
        data.poNoList = data.poNo.split(/\s+|\n|,|，/gmi).filter(Boolean)
      }
      if (Array.isArray(data.supplierNoList)) {
        data.supplierNoList = data.supplierNoList.map(item => item.supplierNo)
      }
      if (Array.isArray(data.guaranteedStatusList)) {
        data.guaranteedStatusList = data.guaranteedStatusList.map(item => Number(item))
      }
      for (let prop in data) {
        if (typeof data[prop] === 'string') {
          data[prop] = data[prop].trim()
        }
      }
      delete data.approvalDate
      return data
    },
    formatOmsForm (form) {
      const data = deepClone(form)
      const keepProps = [
        'customerNo', 'skuNo', 'workOrder', 'soNo', 'deliveryDate', 'soCreateDate', 'approvalDate', 'purchaseGroupList', 'workOrderStatus', 'brandId', 'ifVpi', 'ifUrgent'
      ]
      for (let prop in data) {
        if (!keepProps.find(item => item === prop)) {
          delete data[prop]
        }
      }
      data.workOrder = data.workOrderNo
      delete data.workOrderNo

      data.purchaseGroup = data.purchaseGroupList.join(' ')
      delete data.purchaseGroupList

      data.ifWorkOrderUsable = data.workOrderStatus
      delete data.workOrderStatus

      const { orderByField, orderDirection } = this.sortInfo
      switch (orderByField) {
        case 'approvalDate':
          data.approvalDateSort = orderDirection === 'ASC' ? 1 : 0; break;
        case 'deliveryDate':
          data.deliveryDateSort = orderDirection === 'ASC' ? 1 : 0; break;
        case 'soCreateDate':
          data.soCreateDateSort = orderDirection === 'ASC' ? 1 : 0; break;
        default:
      }

      if (Array.isArray(data.deliveryDate) && data.deliveryDate.length) {
        data.deliveryDateStartDate = data.deliveryDate[0]
        data.deliveryDateEndDate = data.deliveryDate[1]
      }
      if (Array.isArray(data.soCreateDate) && data.soCreateDate.length) {
        data.soCreateDateStartDate = data.soCreateDate[0]
        data.soCreateDateEndDate = data.soCreateDate[1]
      }
      if (Array.isArray(data.approvalDate) && data.approvalDate.length) {
        data.approvalDateStartDate = data.approvalDate[0]
        data.approvalDateEndDate = data.approvalDate[1]
      }
      delete data.deliveryDate
      delete data.soCreateDate
      delete data.approvalDate
      for (let prop in data) {
        if (typeof data[prop] === 'string') {
          data[prop] = data[prop].trim()
        }
      }
      return data
    },
    tagWithId (row) {
      row.shortid = shortid.generate()
    },
    formatRecords (row) {
      safeRun(() => {
        if (!row.workOrderNoList) {
          row.workOrderNoList = row.workOrder.split(',').filter(Boolean)
        }
      })
    },
    changeTableLoading(loading) {
      this.loading.tableLoading = loading
    },
    async initOmsSearch (pageLoading) {
      const { pageNo, pageSize } = this.table
      const form = this.formatOmsForm(this.searchFormList[this.activeName])
      if (form.purchaseGroup) {
        const data = { pageNo, pageSize, ...form }
        if (pageLoading) {
          this.loading.pageLoading = true
        }
        this.loading.searchLoading = true
        this.loading.tableLoading = true
        this.dataList = []
        this.rawDataList = []
        // this.table.total = 0
        getPendingOrderList(data)
          .then(data => {
            if (!data) return
            this.table.total = data.total || 0
            if (Array.isArray(data.records)) {
              data.records.forEach(this.tagWithId)
              data.records.forEach(this.formatRecords)
              this.dataList = data.records
              this.rawDataList = deepClone(this.dataList)
            } else {
              this.$message.error(data.msg)
            }
          })
          .finally(() => {
            this.loading.searchLoading = false
            this.loading.tableLoading = false
            if (pageLoading) {
              this.loading.pageLoading = false
            }
          })
      } else {
        this.$message.error('至少选择一个采购组！')
      }
    },
    formIsEmpty(form) {
      const keepProps = [
        'supplierNoList', 'skuNo', 'workOrderNo', 'poNo', 'orderDate', 'purchaseGroupList', 'workOrderStatus', 'orderType', 'warehouseLocationList', 'openPoStatus', 'approveStep', 'orderTypeList'
      ]
      let isEmpty = true
      Object.keys(form).forEach(key => {
        if (keepProps.includes(key) && ((Array.isArray(form[key]) && form[key].length > 0) || (!Array.isArray(form[key]) && form[key] !== ''))) {
          isEmpty = false
        }
      })
      return isEmpty
    },
    async initSearch (pageLoading, canSearch) {
      if (this.isAbnormalTab) {
        return this.initOmsSearch(pageLoading)
      }
      const isEmpty = this.formIsEmpty(this.searchFormList[this.activeName])
      if (!isEmpty || canSearch) {
        const { pageNo, pageSize } = this.table
        const form = this.formatForm(this.searchFormList[this.activeName])
        const data = { pageNo, pageSize, ...form }
        if (pageLoading) {
          this.loading.pageLoading = true
        }
        this.loading.searchLoading = true
        this.loading.tableLoading = true
        this.dataList = []
        this.rawDataList = []
        // this.table.total = 0
        pagePOWorkbench(data)
          .then(res => {
            if (res) {
              if (Array.isArray(res.rows)) {
                res.rows.forEach(this.tagWithId)
                res.rows.map(item => {
                  item.workOrderNoList = item.workOrderNoList ? item.workOrderNoList : []
                  return item
                })
                this.dataList = res.rows
                this.rawDataList = deepClone(this.dataList)
              }
              this.table.total = res.total || 0
            }
          })
          .finally(() => {
            this.loading.searchLoading = false
            this.loading.tableLoading = false
            if (pageLoading) {
              this.loading.pageLoading = false
            }
          })
      } else if (isEmpty) {
        this.dataList = []
        this.rawDataList = []
      }
    },
    setDataResult (data) {
      this.dataResult = data
      this.resultVisible = true
    },
    maxLengthRule () {},
    validateSearch () {
      const form = this.formatForm(this.searchFormList[this.activeName])
      if (form.skuNo) {
        form.skuNoList = form.skuNo.split(/\s+|\n|,|，/gmi).filter(Boolean)
        if (form.skuNoList.length > 200) {
          this.$message.error('最多输入200个SKU！')
          return false
        }
      }
      if (form.poNo) {
        form.poNoList = form.poNo.split(/\s+|\n|,|，/gmi).filter(Boolean)
        if (form.poNoList.length > 200) {
          this.$message.error('最多输入200个采购单号！')
          return false
        }
      }
      if (form.soNo) {
        form.soNoList = form.soNo.split(/\s+|\n|,|，/gmi).filter(Boolean)
        if (form.soNoList.length > 200) {
          this.$message.error('最多输入200个销售单号！')
          return false
        }
      }
      return true
    },
    handleSearch (pageLoading, canEmpty) {
      this.$refs.searchForm.validate((valid) => {
        if (!valid) return
        if (!this.validateSearch()) return
        this.table.pageNo = 1
        this.sortInfo = {}
        this.refsTable.clearSort()
        this.refsTable.clearSelected()
        this.selections = []
        this.initSearch(pageLoading, canEmpty)
        const label = this.tabsList.find(item => item.value === this.activeName)
        sensors('PurchaseWorkbenchQueryClick', { label_name: label.name || '' })
      })
    },
    handleSizeChange (val) {
      this.table.pageNo = 1
      this.table.pageSize = val
      this.initSearch(false, true)
    },
    handleCurrentChange (val) {
      this.table.pageNo = val
      this.initSearch(false, true)
    }
  }
};
</script>
<style lang="scss" scoped>
.purchase-workbench {
  padding: 10px;
  margin-right: 15px;
  .search-filter {
    overflow: hidden;
    .search-button-group{
      float: right;
    }
  }
  .button-group{
    margin-right: 5px;
    float: right;
  }
  .tip{
      align-self: center;
      font-weight: bold;
    }
  .pagination{
    margin-top: 20px;
    float:right;
  }
}
</style>
