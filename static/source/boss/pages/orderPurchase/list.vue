<template>
  <div class="purchase-search-list" v-loading="loading.pageLoading">
    <div class="search-filter">
      <el-form :model="searchForm" ref="searchFrom" style="width: 100%" label-suffix=":" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form-item label="单号检索" prop="orderNos">
              <el-input
                v-model="searchForm.orderNos"
                clearable
                placeholder="采购单号 或 SAP订单号 或 协议单号 均支持搜索，同时支持200个单号，以空格分隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商" prop="supplierNo">
              <SelectSupplier :data.sync="searchForm.supplierNo" :isCanDisabled="false"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="工厂代码" prop="factoryCodeList">
              <el-select v-model="searchForm.factoryCodeList" clearable placeholder="请选择" multiple collapse-tags filterable>
                <el-option
                  v-for="item in factoryList"
                  :key="item.factoryCode+item.factoryName"
                  :label="item.factoryCode+' '+item.factoryName"
                  :value="item.factoryCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仓库地点" prop="warehouseLocationList">
              <AllowCreateSelect :data.sync="searchForm.warehouseLocationList" @change="val=>searchForm.warehouseLocationList=val" :optionLists="setwarehouseList" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="采购姓名" prop="purchaseGroup">
            <AllowCreateSelect :data.sync="searchForm.purchaseGroup" @change="val=>searchForm.purchaseGroup=val" :optionLists="purchaseGroupLists"  />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-show="showFilter">
          <el-col :span="8">
            <el-form-item label="SKU编码" prop="skuNos">
              <el-input
                v-model="searchForm.skuNos"
                clearable
                placeholder="同时支持500个单号，以空格分隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单类型" prop="orderType">
              <el-select v-model="searchForm.orderType" placeholder="请选择订单类型" clearable>
                <el-option
                  v-for="item in orderTypeList"
                  :key="item.value"
                  :label="item.value+' '+item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建日期" clearable prop="createTime">
              <el-date-picker
                v-model="searchForm.createTime"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width:100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-show="showFilter">
          <el-col :span="8">
            <el-form-item label="到货状态" prop="deliveryStatusList">
              <el-select v-model="searchForm.deliveryStatusList" clearable multiple placeholder="请选择到货状态">
                <el-option
                  v-for="item in (dictList||{})['deliveryStatus']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="SAP状态" prop="sapStatus">
              <el-select v-model="searchForm.sapStatus" clearable placeholder="请选择下发SAP状态">
                <el-option
                  v-for="item in (dictList||{})['sapStatus']"
                  :key="item.value"
                  :label="item.value+' '+item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交货日期" clearable prop="deliveryTime">
              <el-date-picker
                v-model="searchForm.deliveryTime"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width:100%"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-show="showFilter">
          <el-col :span="8">
            <el-form-item label="审批状态" prop="approveStep">
              <el-select v-model="searchForm.approveStep" clearable placeholder="请选择金额审批状态">
                <el-option
                  v-for="item in (dictList||{})['approveStatus']"
                  :key="item.value"
                  :label="item.value+' '+item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="交期确认" prop="isDeliveryTimeConfirm">
              <el-select v-model="searchForm.isDeliveryTimeConfirm" filterable clearable placeholder="请选择">
                <el-option
                  v-for="item in (dictList||{})['poIsDeliveryTimeConfirm']"
                  :key="item.value"
                  :label="item.value+' '+item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :gutter="20" :span="8">
            <el-form-item label="是否删除" prop="isDeleted">
              <el-select v-model="searchForm.isDeleted" filterable clearable placeholder="请选择">
                <el-option
                  v-for="item in (dictList||{})['isDeleted']"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-show="showFilter">
          <el-col :span="8">
            <el-form-item label="代管代发" prop="isEscrow">
              <el-select v-model="searchForm.isEscrow" filterable clearable placeholder="请选择">
                <el-option
                  v-for="item in isEscrowOptions"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="付款条件" prop="paymentTermCode">
              <el-select v-model="searchForm.paymentTermCode" filterable clearable placeholder="请选择">
                <el-option
                  v-for="item in (dictList||{})['paymentTermCode']"
                  :key="item.value"
                  :label="item.value + ' ' + item.name"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row >
          <el-col :offset="16" :span="8" style="text-align:right;margin-bottom:10px;">
            <el-button :loading="loading.searchLoading" @click="handleSearch" type="primary">
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
            <el-button type="default" :icon="showFilter?'el-icon-arrow-up':'el-icon-arrow-down'" @click="handleFold">{{showFilter ? '收起' : '展开'}}</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result">
      <el-row type="flex" justify="space-between">
        <el-col :span="6">
          <h3>采购订单列表</h3>
        </el-col>
        <el-col :span="18" style="text-align:right;">
          <el-popover
            trigger="click"
            placement="bottom"
            v-model="showCreateTaskCascader"
            @hide="hideCreateTaskCascader">
            <el-cascader-panel v-model="taskId" :options="cascaderOptions" @change="cascaderChange"></el-cascader-panel>
            <el-button v-if="getButtonAuth('采购订单列表', '创建任务')" slot="reference" type="primary" size="mini" style="margin-right: 10px;">创建任务</el-button>
          </el-popover>
          <el-dropdown :disabled="!showOaAditButton" style="margin-right: 10px;" @command="submitAduit">
            <el-button size="mini" type="primary" :loading="oaLoading">
              提交OA审批
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="item.value" v-for="item in dictList['oaType']" :key="item.value">{{item.name}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <IDOCreateBtn v-if="getButtonAuth('采购管理', '创建内向交货单')" :selections="selections" />
          <el-button v-if="getButtonAuth('采购管理', '批量修改订单')" type="primary" @click="handleOpenBatchEditDlg" plain>批量修改订单</el-button>
          <el-button v-if="getButtonAuth('采购管理', '批量创建直发订单')" type="primary" @click="handleOpenBatchCreateDlg('direct')" plain>批量创建直发订单</el-button>
          <el-button v-if="getButtonAuth('采购管理', '批量创建标准订单')" type="primary" @click="handleOpenBatchCreateDlg" plain>批量创建标准订单</el-button>
        </el-col>
      </el-row>
      <el-table
        :data="table.tableData"
        tooltip-effect="dark"
        highlight-hover-row
        highlight-current-row
        v-loading="loading.tableLoading"
        style="width: 100%"
        @row-dblclick="rowDbclick"
        @selection-change="handleSelectionChange"
        :cell-style="headStyle"
        :header-row-style="headStyle"
        :header-cell-style="headStyle"
      >
        <el-table-column type="selection" />
        <template v-for="item in fields.tableList">
          <el-table-column v-if="item.prop === 'orderNo'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              <el-link @click="toDetail(row)" type="primary">{{row[item.prop]}}</el-link>
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.prop === 'orderType'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              {{row[item.prop] + ' ' + readNameFromDic(item.prop, row[item.prop])}}
            </template>
          </el-table-column>

          <el-table-column v-else-if="item.prop === 'approveStep'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              {{readNameFromDic('approveStatus', row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.prop === 'deliveryStatus'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              {{readNameFromDic('deliveryStatus', row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.prop === 'isDeliveryTimeConfirm'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              {{readNameFromDic('poIsDeliveryTimeConfirm', row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.prop === 'isDeleted'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              {{readNameFromDic('isDeleted', row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column v-else-if="item.prop === 'purchaseGroup'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              {{readNameFromDic(item.prop, row[item.prop])}}
            </template>
          </el-table-column>
          <el-table-column show-overflow-tooltip v-else-if="item.prop === 'supplierName'" :label="item.label" :key="item.prop" >
            <template slot-scope="{ row }">
              {{row.supplierNo+' ' +row.supplierName }}
            </template>
          </el-table-column>
          <el-table-column
            v-else
            show-overflow-tooltip
            :label="item.label"
            :prop="item.prop"
            :key="item.prop"
            :type="item.type"
          >
            <template slot-scope="{ row }">
              <div v-if="item.format">
                {{item.format(row[item.prop])}}
              </div>
              <div v-if="item.enum">
                {{mapToProps(row[item.prop], 'groupCode', 'userName', purchaseGroupList)}}
              </div>
              <span v-else>
                {{ row && row[item.prop] }}
              </span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button @click="toDetail(row)" type="text">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagi" v-if="table.total > 10">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="table.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="table.total">
        </el-pagination>
      </div>
    </div>
    <CreateBatchOrderDialog
      :show-dialog.sync="createBatchOrder"
      :isDirect="isDirect"
      @uploadSuccess="handleSuccess"
    />
    <EditBatchOrderDialog
      :show-dialog.sync="editBatchOrder"
      @uploadSuccess="handleSuccess"
    />
    <ResultDlg
      :resultVisible="resultVisible"
      :resultData="resultData"
      :closeResultDlg="closeResultDlg"
    />
    <CreateTaskDlg
      :show-dialog.sync="createTaskDlgVisible"
      :tableData="createTaskData"
    />
    <ModifyPaymentDlg
      v-if="modifyPaymentDlgVisible"
      :show-dialog.sync="modifyPaymentDlgVisible"
      :tableData="modifyPaymentData"
    />
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { ListFactory } from './utils/field/list/list.factory'
import { getPurchaseOrderList, oaAdvancePayment, oaContract, queryEvent, queryPaymentTerm, queryApproveStatus } from '@/api/mm'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import AllowCreateSelect from './components/common/MultipleSelect'
import CreateBatchOrderDialog from './components/list/BatchCreateDlg'
import EditBatchOrderDialog from './components/list/BatchEditDlg'
import CreateTaskDlg from './components/list/CreateTaskDlg.vue'
import ModifyPaymentDlg from './components/list/ModifyPaymentDlg.vue'
import ResultDlg from './components/list/ResultDlg'
import IDOCreateBtn from './components/common/IDOCreateBtn'
import { safeRun, deepClone } from '@/utils/index'
import { mapToProps, readNameFromDic, getAllDictList, buildOptions } from '@/utils/mm'
import { getButtonAuth } from '@/utils/auth'

const factory = new ListFactory();
const fields = factory.getFields();

export default {
  name: 'purchaseOrderList',
  components: {
    CreateBatchOrderDialog,
    EditBatchOrderDialog,
    IDOCreateBtn,
    SelectSupplier,
    ResultDlg,
    AllowCreateSelect,
    CreateTaskDlg,
    ModifyPaymentDlg
  },
  data() {
    return {
      fields,
      oaLoading: false,
      isDirect: false,
      selections: [],
      showFilter: true,
      loading: {
        searchLoading: false,
        tableLoading: false,
        pageLoading: false
      },
      isEscrowOptions: [
        { name: '是', value: 1 },
        { name: '否', value: 0 }
      ],
      searchForm: {
        skuNos: '',
        orderNos: '',
        purchaseGroup: [],
        supplierNo: '',
        sapStatus: '',
        approveStep: '',
        orderType: '',
        msgSendStatus: '',
        deliveryStatusList: [],
        createTime: [],
        deliveryTime: [],
        factoryCodeList: [],
        warehouseLocationList: [],
        isDeliveryTimeConfirm: '',
        isDeleted: '',
        isEscrow: '',
        paymentTermCode: ''
      },
      table: {
        pageNo: 1,
        current: 1,
        pageSize: 10,
        total: 0,
        tableData: []
      },
      createBatchOrder: false,
      editBatchOrder: false,
      setwarehouseList: [],
      resultData: {},
      resultVisible: false,
      createTaskDlgVisible: false,
      createTaskData: [],
      showCreateTaskCascader: false,
      taskId: [],
      modifyPaymentDlgVisible: false,
      modifyPaymentData: []
    };
  },
  async created() {
    this.loading.pageLoading = true
    await getAllDictList(this)
    this.findDefaultPurchaseGroup()
    await this.setObjectArray()
    this.loading.pageLoading = false
    await this.onSubmit('initSearch')
    this.initQuery()
    // const defaultCompany = await getUserCompany()
    // if (defaultCompany) {
    //   this.searchForm.factoryCodeList = [defaultCompany]
    // }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      purchaseGroupList: state => state.orderPurchase.purchaseList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      warehouseList: state => state.orderPurchase.warehouseList
    }),
    purchaseGroupLists () {
      return this.purchaseGroupList.map(item => {
        return {
          value: item.groupCode,
          name: item.userName
        }
      })
    },
    orderTypeList () {
      return this.dictList['orderType']
    },
    factoryList: {
      get () {
        let factories = []
        for (let i = 0; i < this.companyFactoryList.length; i++) {
          factories.push(...this.companyFactoryList[i].factoryList)
        }
        return factories
      }
    },
    showOaAditButton () {
      return this.selections.length
    },
    cascaderOptions () {
      const processTaskFirst = this.dictList['processTaskFirst'];
      const processTaskSecond = this.dictList['processTaskSecond'];
      if (processTaskFirst) {
        processTaskFirst.map(first => {
          first.children = [...processTaskSecond.filter(second => first.value === second.value.substr(0, 2))].map(item => {
            return {
              value: item.value,
              label: item.name
            }
          })
        })
        return processTaskFirst.map(item => {
          const newItem = {
            value: item.value,
            label: item.name
          }
          if (item.children.length) {
            newItem.children = item.children
          }
          return newItem
        })
      }
      return []
    }
  },
  methods: {
    buildOptions,
    initQuery() {
      const { orderNo } = this.$route.query;
      if (orderNo) {
        this.searchForm.orderNos = orderNo;
      }
    },
    async submitAduit (command) {
      const data = {
        operateUser: window.CUR_DATA.user.name,
        poNoList: this.selections.map(sec => sec.orderNo)
      }
      switch (command) {
        // 预付申请单
        case 'PREPAYMENT':
          this.oaloading = true
          const res = await queryApproveStatus(this.selections.map(sec => sec.orderNo))
          const invalidNos = res?.filter(item => item.approveStatus)?.map(item => item.poNo).join(',') || ''
          if (invalidNos) {
            this.oaloading = false
            this.$message.error(`订单${invalidNos}审批中，请完成审批后递交预付款申请`)
            return
          }
          oaAdvancePayment(data)
            .then(res => {
              if (res && res.oaNo && res.url) {
                this.$message.success('提交成功，即将跳转OA页面！')
                setTimeout(() => {
                  window.open(res.url)
                }, 600)
              } else {
                // this.$message.error(res || 'OA流程提交失败！')
              }
            })
            .finally(() => {
              this.oaloading = false
            })
          break;
        // 合同单
        case 'CONTRACT':
          this.oaloading = true
          oaContract(data)
            .then(res => {
              if (res && res.oaNo && res.url) {
                this.$message.success('已成功提交OA预付款申请，即将跳转OA页面！')
                setTimeout(() => {
                  window.open(res.url)
                }, 600)
              } else {
                // this.$message.error(res || 'OA流程提交失败！')
              }
            })
            .finally(() => {
              this.oaloading = false
            })
          break;
      }
    },
    handleFold () {
      this.showFilter = !this.showFilter
    },
    closeResultDlg () {
      this.resultData = {}
      this.resultVisible = false
    },
    updateResultVisible (visible) {
      this.resultVisible = visible
    },
    findDefaultPurchaseGroup () {
      const options = this.buildOptions('purchaseGroupList')
      const username = window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
      const purchaseGroup = options.find(option => option.securityUsername === username)
      if (purchaseGroup && purchaseGroup.value) {
        this.searchForm.purchaseGroup = [purchaseGroup.value]
      }
    },
    setObjectArray () {
      let obj = {}
      for (let i = 0; i < this.warehouseList.length; i++) {
        if (!obj[this.warehouseList[i].warehouseLocationCode]) {
          this.setwarehouseList.push({
            value: this.warehouseList[i].warehouseLocationCode,
            name: this.warehouseList[i].warehouseLocationName
          })
          obj[this.warehouseList[i].warehouseLocationCode] = true
        }
      }
    },
    mapToProps,
    getButtonAuth,
    readNameFromDic,
    toDetail (row) {
      this.$router.push({
        path: `/orderPurchase/detail/${row.orderNo}`,
        query: { tagName: `${row.orderNo}详情` }
      })
    },
    handleSizeChange (val) {
      this.table.pageSize = val
      this.onSubmit()
    },
    handleCurrentChange (val) {
      this.table.pageNo = val
      this.onSubmit()
    },
    rowDbclick(row, col, event) {
      console.log(row, col, event);
      row.editable = !row.editable;
    },
    formatParams(params) {
      let form = { ...params };
      form.poNoList = safeRun(() =>
        form.orderNos
          .split(/\s+|,|，/).filter((e) => e)
      );
      form.skuNoList = safeRun(() =>
        form.skuNos
          .split(/\s+|,|，/).filter((e) => e)
      );
      if (form.createTime && form.createTime.length === 2) {
        form.createTimeBegin = form.createTime[0] + ' 00:00:00'
        form.createTimeEnd = form.createTime[1] + ' 23:59:59'
        delete form.createTime
      }
      if (form.deliveryTime && form.deliveryTime.length === 2) {
        form.deliveryDateBegin = form.deliveryTime[0]
        form.deliveryDateEnd = form.deliveryTime[1]
        delete form.deliveryTime
      }
      form.purchaseGroupList = form.purchaseGroup
      if (Array.isArray(form.supplierNo)) {
        form.supplierNoList = form.supplierNo.map(item => item.supplierNo)
      } else if (form?.supplierNo?.supplierNo) {
        form.supplierNoList = [form.supplierNo.supplierNo]
      }
      form.orderTypeList = [form.orderType].filter(Boolean)
      form.sapStatusList = [form.sapStatus].filter(Boolean)
      form.approveStepList = [form.approveStep].filter(Boolean)
      delete form.purchaseGroup
      delete form.supplierNo
      delete form.orderType
      delete form.sapStatus
      delete form.skuNos
      delete form.approveStep
      delete form.orderNos
      return form;
    },
    parse2Array(input) {
      if (Array.isArray(input)) {
        // 不过滤0
        return input.filter(item => !item && (item !== 0))
      }
      if (input && input.split) {
        return input.split(/,|，|\s+/).filter(item => !item && (item !== 0))
      }
    },
    handleSearch () {
      this.table.pageNo = 1;
      this.onSubmit()
    },
    handleReset () {
      this.$refs.searchFrom.resetFields()
      this.findDefaultPurchaseGroup()
      this.handleSearch()
    },
    validate (params) {
      let ret = true
      try {
        if (params.orderNos.length > 200) {
          ret = false
          this.$message.error('订单号不能超过200个！')
        }
        if (params.skuNos.length > 500) {
          ret = false
          this.$message.error('SKU数目不能超过500个！')
        }
      } catch (err) {}
      return ret
    },
    async onSubmit(initSearch) {
      let params = this.formatParams(deepClone(this.searchForm));
      console.log('params', params)
      if (initSearch === 'initSearch') {
        if (!params.purchaseGroupList || !params.purchaseGroupList.length) return
      }
      if (!this.validate(params)) return
      this.loading.searchLoading = true;
      this.loading.tableLoading = true;
      params = {
        pageNo: this.table.pageNo,
        pageSize: this.table.pageSize,
        ...params
      }
      return getPurchaseOrderList(params)
        .then((data) => {
          if (data) {
            try {
              this.table.tableData = data.rows
              this.table.total = data.total
            } catch (err) { console.log(err) }
          }
        })
        .finally(() => {
          this.loading.searchLoading = false;
          this.loading.tableLoading = false;
        })
    },
    handleSelectionChange(rows) {
      this.selections = rows;
    },
    handleOpenBatchCreateDlg (type) {
      this.isDirect = false
      if (type === 'direct') {
        this.isDirect = true
      }
      this.createBatchOrder = true
    },
    handleOpenBatchEditDlg () {
      this.editBatchOrder = true
    },
    handleSuccess (resultData) {
      if (resultData) {
        this.updateResultVisible(true)
        this.resultData = resultData
      }
      this.handleSearch()
    },
    headStyle() {
      return 'text-align:center'
    },
    hideCreateTaskCascader () {
      this.taskId = [];
    },
    async createTask () {
      if (this.selections.length === 0) {
        this.$message.warning('请选择需要处理的PO单');
        return;
      }
      if (this.selections.length > 50) {
        this.$message.warning('单次最多提交50个，请减少勾选后重新提交');
        return;
      }
      this.showCreateTaskCascader = false;
      const poIdList = this.selections.map(item => item.id);
      const data = {
        poIdList
      }
      const res = await queryEvent(data);
      if (res && res.code === 0 && res.data) {
        this.createTaskDlgVisible = true;
        this.createTaskData = res.data;
      } else if (res?.code === 1004) {
        this.$confirm(`<div style="max-height:400px;overflow:auto;white-space: break-spaces;">${res?.msg || '查询失败'}</div>`, '提醒', {
          dangerouslyUseHTMLString: true,
          confirmButtonText: '继续创建',
          cancelButtonText: '取消创建'
        })
          .then(() => {
            this.createTaskDlgVisible = true;
            this.createTaskData = res.data || [];
          })
          .catch(action => {
            console.log(action)
          })
      } else {
        this.$alert(`<div style="max-height:400px;overflow:auto;white-space: break-spaces;">${res?.msg || '查询失败'}</div>`, '提醒', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true
        })
      }
    },
    async modifyPaymentTerm () {
      if (this.selections.length === 0) {
        this.$message.warning('请选择需要处理的PO单');
        return;
      }
      if (this.selections.length > 1) {
        this.$message.warning('仅支持逐单修改为KA01款期，请勾选唯一采购单！');
        return;
      }
      if (!getButtonAuth('采购订单列表', '创建任务_修改为KA01款期')) {
        this.$message.warning('您没有权限进行此操作！');
        return;
      }
      this.showCreateTaskCascader = false;
      const poNo = this.selections.map(item => item.orderNo);
      const data = {
        poNo
      }
      const res = await queryPaymentTerm(data);
      if (res && res.code === 0) {
        this.modifyPaymentDlgVisible = true;
        this.modifyPaymentData = res.data || [];
      } else {
        this.$alert(`<div style="max-height:400px;overflow:auto;white-space: break-spaces;">${res?.msg || '查询失败'}</div>`, '提醒', {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true
        })
      }
    },
    cascaderChange (val) {
      console.log('选择创建任务', val)
      const second = val.at(-1);
      switch (second) {
        // 创建任务-背靠背付款状态认定
        case '1010':
          this.createTask();
          break;
        // 修改为KA01款期
        case '20':
          this.modifyPaymentTerm();
          break;
        }
    }
  }
};
</script>
<style lang="scss" scoped>
.purchase-search-list {
  margin-right: 15px;
  padding: 20px;
  .search-result {
    h3 {
      margin: 10px 0px;
    }
  }
  .pagi{
    margin-top: 20px;
    float:right;
  }
  .el-select {
    width: 100%;
  }
}
</style>
