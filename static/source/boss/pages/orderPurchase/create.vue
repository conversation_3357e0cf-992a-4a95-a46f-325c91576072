<template>
  <div class="purchase-create" v-loading="pageLoading">
    <el-form :model="purchaseData" ref="orderForm" label-width="140px" label-suffix=":" :rules="rules">
      <div class="card-wrapper">
        <div class="tab">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="基本信息" name="base"></el-tab-pane>
            <el-tab-pane label="备注文本" name="remark"></el-tab-pane>
            <el-tab-pane label="通讯信息" name="communication"></el-tab-pane>
            <el-tab-pane v-if="showCharge" label="价费信息" name="charge"></el-tab-pane>
          </el-tabs>
          <el-button class="toggle-button" circle :icon="showHeaderIcon" @click="toggleButton" />
        </div>
        <div class="card-container" v-show="showHeader">
          <div class="card-title">
            <div v-show="activeTab==='base'">
              <el-row :gutter="20" class="flex-row-wrapper">
                <el-col :span="item.span || 8" v-for="item in getFieldsByCategory('baseInfo')" :key="item.name">
                  <el-form-item :label="item.name" :prop="item.prop">
                    <SelectSupplier
                      ref="supplier"
                      v-if="item.prop==='supplier'&&item.type==='custom'"
                      :data.sync="purchaseData[item.prop]"
                      :purchaseData="purchaseData"
                      @change="handleSupplierChange"
                    />
                    <el-select
                      ref="customerName"
                      v-else-if="item.prop==='customerName'&&item.type==='custom'"
                      style="width:100%"
                      filterable
                      placeholder="请选择"
                      v-model="purchaseData[item.prop]"
                      value-key="value"
                      @change="handleCustomerChange"
                    >
                      <el-option
                        v-for="item in customerNameOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item">
                      </el-option>
                    </el-select>
                    <!-- <FestoCustomer
                      ref="customerName"
                      v-else-if="item.prop==='customerName'&&item.type==='custom'"
                      width="100%"
                      headName="客户"
                      :value="purchaseData[item.prop]"
                      :purchaseData="purchaseData"
                      @change="handleCustomerChange"
                    /> -->
                    <el-input
                      v-else-if="item.type==='input'"
                      v-model="purchaseData[item.prop]"
                      clearable
                      maxlength="35"
                      :placeholder="`请输入${item.name}`"
                      :disabled="item.disabled"
                    />
                    <el-input-number
                      v-else-if="item.type==='number'"
                      v-model="purchaseData[item.prop]"
                      clearable
                      :placeholder="`请输入${item.label || item.name}`"
                      :disabled="item.disabled"
                      :precision="item.precision||0"
                      @change="(val)=>handleNumberChange(val,item.prop)"
                      :min="0"
                      :max="100"
                      style="width:100%"
                    />
                    <el-checkbox
                      v-else-if="item.type==='checkbox'"
                      :indeterminate="isIndeterminate(item.prop)"
                      v-model="purchaseData[item.prop]"
                      :true-label="1"
                      :false-label="0"
                      @change="(val) => handleCheckboxChange(val, item.prop)"
                    />
                    <el-date-picker
                      v-else-if="item.type==='dateTime'"
                      v-model="purchaseData[item.prop]"
                      :disabled="item.disabled"
                      style="width:100%"
                      clearable type="date" placeholder="选择日期" />
                    <el-select
                      v-else-if="item.type==='select'&&item.prop==='companyCode'"
                      v-model="purchaseData[item.prop]"
                      :disabled="item.disabled"
                      style="width:100%"
                      filterable
                      default-first-option
                      @change="handleChangeCompany"
                      ref="company"
                    >
                      <el-option
                        v-for="item in companyFactoryList"
                        :key="item.companyCode"
                        :label="item.companyCode+' '+item.companyName"
                        :value="item.companyCode">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="item.type==='select'&&item.prop==='purchaseGroup'"
                      v-model="purchaseData[item.prop]"
                      :disabled="hideByPurchaseGroup(item.prop)"
                      filterable
                      default-first-option
                      @change="handlePurchaseGroupChange"
                      style="width:100%"
                    >
                      <el-option
                        v-for="item in purchaseList"
                        :key="item.groupCode"
                        :label="item.groupCode+' '+item.userName"
                        :value="item.groupCode">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="item.type==='select'&&item.prop==='supplierDict'"
                      v-model="purchaseData[item.prop]"
                      :disabled="item.disabled"
                      style="width:100%"
                      clearable
                      filterable
                      default-first-option
                      @change="val => handleBaseItemSelectChange(val, item.prop)"
                    >
                      <el-option
                        v-for="item in transferSupplierList"
                        :key="item.value"
                        :label="item.value+' '+item.name"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="item.type==='select'"
                      v-model="purchaseData[item.prop]"
                      :disabled="item.disabled || hideByPurchaseGroup(item.prop)"
                      style="width:100%"
                      default-first-option
                      clearable
                      filterable
                      @change="val => handleBaseItemSelectChange(val, item.prop)"
                    >
                      <el-option
                        v-for="opt in dictList[item.enums]"
                        :key="opt.value"
                        :label="['receiptReturnWay', 'drawbackWay', 'returnWay'].includes(item.prop) ? opt.name : opt.value+' '+opt.name"
                        :value="opt.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" :offset="2" v-if="['Z007', 'Z008'].includes(purchaseData.orderType)">
                  <el-form-item label="OA流程编号">
                    <el-input v-model="commonOaNumber" clearable placeholder="请输入OA流程编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="2" v-if="['Z007', 'Z008'].includes(purchaseData.orderType)">
                  <el-tooltip content="注意：批量设置OA流程编号会把当前输入的OA流程编号设置到所有行中" placement="top">
                    <el-button type="primary" @click="handleOaNumber">批量设置OA流程编号</el-button>
                  </el-tooltip>
                </el-col>
              </el-row>
            </div>
            <div v-show="activeTab==='remark'">
              <el-row v-for="item in getFieldsByCategory('remark')" :key="item.name" :gutter="20">
                <el-form-item :label="item.name" :prop="item.prop">
                  <el-input
                    type="textarea"
                    :rows="2"
                    v-model="purchaseData[item.prop]"
                    :maxlength="item.length"
                    show-word-limit
                    :placeholder="item.name"
                    clearable
                  />
                </el-form-item>
              </el-row>
            </div>
            <div v-show="activeTab==='communication'">
              <el-row :gutter="20">
                <el-col v-for="childItem in getFieldsByCategory('comm')" :key="childItem.name" :span="childItem.span">
                  <el-form-item :label="childItem.name" :prop="childItem.prop">
                    <el-input
                      v-if="childItem.type==='input'"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      :disabled="childItem.disabled"
                      :placeholder="childItem.name"
                    />
                    <el-select
                      v-else-if="childItem.type==='select'&&childItem.prop==='supplierAddress'"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      filterable
                      default-first-option
                      @change="handleChangeSupplierAddress"
                      style="width:100%"
                    >
                      <el-option
                        v-for="(item,idx) in supplierAddressList"
                        :key="idx+item.detail"
                        :label="fullDetailAddress(item)"
                        :value="fullDetailAddress(item)">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="childItem.type==='select'&&childItem.prop==='supplierContactName'"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      filterable
                      default-first-option
                      @change="handleChangeSupplierContactName"
                      style="width:100%"
                    >
                      <el-option
                        v-for="(item,idx) in supplierContactList"
                        :key="idx+item.phone"
                        :label="item.name"
                        :value="item.name">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="childItem.type==='select'&&childItem.prop==='receiptContactName'"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      allow-create
                      filterable
                      default-first-option
                      style="width:100%"
                      @change="handleChangeReceiptContactName"
                    >
                      <el-option
                        v-for="(item, index) in receiptContactList"
                        :key="`${index}${item.phone}`"
                        :label="item.name"
                        :value="item.name">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="childItem.type==='select'&&childItem.prop==='receiptAddress'"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      filterable
                      default-first-option
                      style="width:100%"
                      @change="handleChangeReceiptAddress"
                    >
                      <el-option
                        v-for="(item,idx) in receiptAddressList"
                        :key="idx+item.detail"
                        :label="fullDetailAddress(item)"
                        :value="fullDetailAddress(item)">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="childItem.type==='select'&&childItem.prop==='receiveContactName'"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      allow-create
                      filterable
                      default-first-option
                      style="width:100%"
                      @change="handleChangeReceiveContactName"
                    >
                      <el-option
                        v-for="(item, idx) in receiveContactList"
                        :key="idx+item.phone"
                        :label="item.name"
                        :value="item.name">
                      </el-option>
                    </el-select>
                    <el-select
                      v-else-if="childItem.type==='select'&&childItem.prop==='receiveAddress'"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      filterable
                      default-first-option
                      style="width:100%"
                      @change="handleChangeReceiveAddress"
                    >
                      <el-option
                        v-for="(item,idx) in receiveAddressList"
                        :key="idx+item.detail"
                        :label="fullDetailAddress(item)"
                        :value="fullDetailAddress(item)">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div v-show="activeTab==='charge'">
              <el-row class="charge-row">可输入一个或多个待分配金额，点击
                <el-button size="mini" type="primary" class="charge-row-btn" @click="handleOneAssign">一键分摊</el-button>
              将金额分配到未收货且未删除的商品行上。其中分摊运费做叠加处理。</el-row>
              <el-row :gutter="20">
                <el-col v-for="childItem in fieldAllocationContent" :key="childItem.name" :span="childItem.span || 12">
                  <span v-if="childItem.type!=='checkbox'" style="display: inline-block;margin-bottom: 5px">
                    <span class="charge-row-item">
                      <span>{{childItem.name}}</span>:
                      <span class="charge-row-item-highlight">
                        {{purchaseData[childItem.prop+'Amount'] && purchaseData[childItem.prop + 'Type'] ? getItemType(childItem.prop + 'Type') : ''}}
                        {{purchaseData[childItem.prop+'Amount'] ? Number(purchaseData[childItem.prop+'Amount']).toFixed(2) : ''}}
                      </span>
                      <span class="charge-row-item-highlight">{{purchaseData[childItem.prop+'Amount'] ? purchaseData[childItem.prop+'Currency'] : ''}}</span>
                    </span>
                    <el-select
                      size="mini"
                      :disabled="disableCharge(childItem.prop)"
                      v-if="purchaseData.orderType==='Z002' && childItem.prop==='customsFee'"
                      v-model="purchaseData[childItem.prop+'TypeSelect']"
                      placeholder="请选择报关杂费类别"
                      clearable
                      style="width:220px;margin-right:10px"
                    >
                      <el-option
                        v-for="item in dictList['customsFeeType']"
                        :key="item.value"
                        :label="item.value+' '+item.name"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <el-select
                      size="mini"
                      :disabled="disableCharge(childItem.prop)"
                      v-if="purchaseData.orderType==='Z002' && childItem.prop==='intlShipping'"
                      v-model="purchaseData[childItem.prop+'TypeSelect']"
                      placeholder="请选择国际运费类别"
                      clearable
                      style="width:220px;margin-right:10px"
                    >
                      <el-option
                        v-for="item in dictList['intlShippingType']"
                        :key="item.value"
                        :label="item.value+' '+item.name"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <el-input-number
                      size="mini"
                      :disabled="disableCharge(childItem.prop)"
                      v-model="purchaseData[childItem.prop]"
                      clearable
                      :placeholder="childItem.name"
                      :precision="2"
                      style="width:120px;margin-right:10px"
                    />
                    <el-select
                      size="mini"
                      :disabled="disableCharge(childItem.prop)"
                      v-if="childItem.prop.indexOf('share')===-1"
                      v-model="purchaseData[childItem.prop+'CurrencyInput']"
                      clearable
                      filterable
                      default-first-option
                      placeholder="币别"
                      style="width:90px;margin-right:10px"
                    >
                      <el-option
                        v-for="item in dictList['sapCurrency']"
                        :key="item.value"
                        :label="item.value+' '+item.name"
                        :value="item.value">
                      </el-option>
                    </el-select>
                    <SelectSupplierNo
                      size="mini"
                      :disabled="disableCharge(childItem.prop)"
                      v-if="childItem.prop.indexOf('share')===-1"
                      v-model="purchaseData[childItem.prop+'SupplierNo']"
                      @change="(item,itemList)=>handleChangeSupplier(childItem.prop+'SupplierNo',item,itemList)"
                      style="width:120px;"
                    />
                  </span>
                </el-col>
                <el-col :span="24">
                  <span style="display: flex;align-items: center;margin-bottom: 5px">
                    <span class="charge-row-item">
                      <span>杂费总计:</span>
                      <span class="charge-row-item-highlight">
                        {{purchaseData['sundryAmount'] ? Number(purchaseData['sundryAmount']).toFixed(2) : ''}}
                      </span>
                    </span>
                    <el-input-number
                      size="mini"
                      v-model="purchaseData['sundry']"
                      clearable
                      placeholder="杂费"
                      :precision="2"
                      style="width:120px;margin-right:10px"
                    />
                    <el-select multiple style="width: 200px;" v-model="purchaseData['sundryReason']" placeholder="请选择杂费原因">
                      <el-option v-for="item in dictList.sundryReason" :key="item.value" :label="item.name" :value="item.value"></el-option>
                    </el-select>
                    <el-tooltip placement="top">
                      <div slot="content">
                        1、客户特殊需求：如现场需要上楼，指定快递送货；<br/>
                        2、超出包邮区域：如订单价格只江浙沪包邮，但实际订单地址在新疆；<br/>
                        3、客户需求变更：如收货地址变更、二次派送、货物滞留费。
                      </div>
                      <i class="el-icon-question" style="margin-left: 5px;margin-right: 10px;"></i>
                    </el-tooltip>
                    <el-input v-model="purchaseData['sundryReasonDetail']" type="textarea" :rows="1" show-word-limit maxlength="30" style="width: 300px;" placeholder="请输入杂费原因备注"></el-input>
                  </span>
                </el-col>
                <el-col :span="12" v-if="purchaseData.orderType==='Z001'">
                  <span style="display: inline-block;margin-bottom: 5px">
                    <span class="charge-row-item">
                      <span>分摊返利总计:</span>
                      <span class="charge-row-item-highlight">
                        {{purchaseData['shareRebateAmount'] ? Number(purchaseData['shareRebateAmount']).toFixed(2) : ''}}
                      </span>
                    </span>
                    <SelectRebateDialog :purchaseData="purchaseData" />
                  </span>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="3">
                  <el-form-item label="异常延期付款">
                    <el-checkbox
                      v-model="purchaseData.poExtend.isDelayedPayment"
                      :true-label="1"
                      :false-label="0"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-for="childItem in fieldAllocationCheckbox" :key="childItem.name" :span="childItem.span || 12">
                  <el-form-item v-if="childItem.type==='checkbox'" :label="childItem.name" :prop="childItem.prop">
                    <el-checkbox
                      v-model="purchaseData[childItem.prop]"
                      @change="value=>handleChangeChargeCheckbox(childItem.prop, value)"
                      :true-label="1"
                      :false-label="0"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" >
                <el-col :span="12" style="display: flex; align-items: flex-start" v-if="purchaseData.orderType === 'Z001'" >
                <span>附件凭证：</span>
                  <el-upload
                    ref="upload"
                    action="/upload"
                    :on-remove="handleRemove"
                    :accept="acceptFileType.poCommonType"
                    :show-file-list="true"
                    :multiple="false"
                    :http-request="httpRequestHandle"
                    :before-upload="(file) => $validateFileType(file, acceptFileType.poCommonType)"
                  >
                    <el-button size="small" type="primary">点击上传</el-button>
                  </el-upload>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="card-container">
          <div class="card-table">
            <div class="table-header">
              <span>商品信息</span>
              <span>
                <el-button
                  type="danger"
                  plain
                  :disabled="purchaseData.itemList.length<=1"
                  @click="handleItemDelete"
                >
                  删除商品行
                </el-button>
                <el-button
                  type="primary"
                  :disabled="isDisabledAddItem"
                  @click="showBatchImport=true"
                >
                  快速导入商品
                </el-button>
                <el-button
                  v-if="isFesto"
                  type="primary"
                  @click="showPunchImport=true"
                >
                  Punch导入商品
                </el-button>
              </span>
            </div>
            <el-row type="flex" justify="space-between" style="padding: 15px 20px;">
              <el-col :span="24" style="font-size: 16px">
                <span class="amount" v-show="purchaseData.orderType !== 'Z003'">未税总额：
                  <span :class="{'currency':purchaseData.currency!=='CNY'}">
                    {{purchaseData.currency}}
                  </span>
                  {{totalAmount.unTaxedTotal.toFixed(2)}}
                </span>
                <span class="amount" v-show="purchaseData.orderType !== 'Z003'">含税总额：
                  <span :class="{'currency':purchaseData.currency!=='CNY'}">
                    {{purchaseData.currency}}
                  </span>
                  {{totalAmount.taxedTotal.toFixed(2)}}
                </span>
                <span class="amount" v-show="purchaseData.orderType !== 'Z003'">税费总额：
                  <span :class="{'currency':purchaseData.currency!=='CNY'}">
                    {{purchaseData.currency}}
                  </span>
                  {{((totalAmount.taxedTotal - totalAmount.unTaxedTotal)||0).toFixed(2)}}
                </span>
                <span class="amount" v-show="isGrossMarginOrderType">毛利率:
                  {{![undefined, 'undefined', '', null, NaN].includes(totalAmount.grossMarginTotal) ? `${ totalAmount.grossMarginTotal }%` : '' }}
                </span>
              </el-col>
            </el-row>
            <ItemTable
              class="table-body"
              ref="itemTable"
              :data="purchaseData"
              :factoryList="factoryList"
              :field="getFieldsByCategory('table')"
              :records.sync="records"
              :disabled="isDisabledAddItem"
              :costCenterList="costCenterList"
              :getCostCenter="getCostCenter"
              :setTitleCheckbox="setTitleCheckbox"
              :diffDays="diffDays"
              :reCalcCompQuantity="reCalcCompQuantity"
              :setRowMPQ="setRowMPQ"
              @showDetail="handleShowDetail"
              @updateItemDeliveryDate="updateItemDeliveryDate"
              @updateTransferTime="updateTransferTime"
              @updateFactory="updateFactory"
              @updateCompWarehouse="updateCompWarehouse"
              @updateCompWarehouseByWarehouse="updateCompWarehouseByWarehouse"
              @reactiveSetItem="reactiveSetItem"
              @updateWarehouseLocation="updateWarehouseLocation"
              @updateDeliveryDaysByWarehouseLocation="updateDeliveryDaysByWarehouseLocation"
              @updateMaterialDescription="handleUpdateMaterialDescription"
              @updateCustomerService="handleUpdateCustomerService"
              @addItemEmptyLine="addItemEmptyLine"
              @addItem="addItem"
            />
          </div>
        </div>
      </div>
      <div class="fixed-create" ref="fixedCreate">
        <div class="btn-group">
          <el-button type="primary" @click="handleSubmit('orderForm')" style="width:100px">创建</el-button>
        </div>
      </div>
    </el-form>
    <ItemDetail
      :purchase-data="purchaseData"
      :data="itemDetail"
      :showDialog.sync="showDetail"
      :itemField="fieldList"
      :showCharge="showCharge"
      :KHWarehouseList="KHWarehouseList"
      @updateItem="handleUpdateItem"
    />
    <el-dialog
      title="快速导入商品"
      :visible.sync="showBatchImport"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
    >
      <BatchImport
        @close="showBatchImport=false"
        @import="handleImport"
        :purchase-data="purchaseData"
      />
    </el-dialog>
    <el-dialog
      title="Punch导入商品"
      :visible.sync="showPunchImport"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="860px"
    >
      <PunchImport
        :visible="showPunchImport"
        @close="showPunchImport=false"
        @import="handlePunchImport"
        :finishDataApi="finishDataApi"
        :purchase-data="purchaseData"
      />
    </el-dialog>
    <el-dialog
      title="操作成功"
      width="360px"
      :visible.sync="submitDialog"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose">
      <div style="text-align:center;margin-bottom:-20px;margin-top:-10px;">
        <el-button type="success" icon="el-icon-check" circle size="mini" style="padding: 3px;margin-right:3px;"></el-button>订单{{submitOrderNo}}创建成功！</div>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="purchaseData.orderType === 'Z003'" @click="jumpToSto">调拨单交货</el-button>
        <el-button @click="continueCreate">继续创建</el-button>
        <el-button type="primary" @click="ensureSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import padStart from 'lodash/padStart'
import uniq from 'lodash/uniq'
import merge from 'lodash/merge'
import isEmpty from 'lodash/isEmpty'
import moment from 'moment'
import { mapState } from 'vuex'
import * as shortid from 'shortid'
import { formatAmount, getD02ComponentWarehouseLocation, getDefaultWarehouseLocation, getKHpurchaseGroup } from './utils'
import { getAssignInfo, isAssigned, calculateTotal as calculatePOTotal, rowEditChange } from '@/utils/poPriceCalculate'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import SelectSupplierNo from '@/pages/orderPurchase/components/common/SelectSupplierNo'
import SelectRebateDialog from '@/pages/orderPurchase/components/common/SelectRebateDialog'
import ItemDetail from '@/pages/orderPurchase/components/create/itemDetail'
import ItemTable from '@/pages/orderPurchase/components/create/itemTable'
import BatchImport from '@/pages/orderPurchase/components/create/BatchImport'
import PunchImport from '@/pages/orderPurchase/components/create/PunchImport'
import { filterInProp, registerReCalcStyle, unRegisterReCalcStyle, getAllDictList, submitErrorHandler, getUserCompany, getSplitOrderTips } from '@/utils/mm'
import { safeRun, deepClone } from '@/utils/index'
import { findTopRole, getButtonAuth } from '@/utils/auth'
import { remove } from 'lodash'
import { upload } from '@/utils/upload'
import {
  createPO, getSupplierInfo, getProduct, getProductPrice, getCostCenter as getCostCenterApi,
  getBom, getInventory, listCommOptions, getCommDefault, getTransferTime, listKHWarehouseUsingPOST
  , assign, assignToggle, assignByQuantity, calcUnTaxedPrice
} from '@/api/mm'
import { getFestoCustomer, finishData as finishDataApi } from '@/api/honeycomb'

export default {
  name: 'purchaseOrderCreate',
  components: {
    SelectSupplier, SelectSupplierNo, ItemDetail, ItemTable, BatchImport, PunchImport, SelectRebateDialog
  },
  data() {
    const { id } = this.$route.params
    let currency = null
    let paymentTermCode
    let supplier = null
    let supplierDict
    const companyCode = ''
    if (id === 'Z003') {
      paymentTermCode = 'KY00'
      currency = 'CNY'
      supplierDict = `V${companyCode}`
    }
    let projectCategory = ''
    let isReturn = 0
    let receivedQuantity = 0
    if (id === 'Z004') {
      isReturn = 1
    }
    if (id === 'Z003') {
      projectCategory = 'U'
    }
    if (id === 'Z005') {
      projectCategory = 'K'
    }
    if (id === 'Z006') {
      projectCategory = 'L'
    }
    if (id === 'Z010') {
      projectCategory = 'S'
    }
    return {
      festoSupplierInfo: {
        providerId: 218812,
        supplierNo: 'V45696',
        supplierName: '费斯托（中国）有限公司'
      },
      customerNameOptions: [],
      submitFestoList: [],
      isFesto: false,
      showHeader: true,
      submitDialog: false,
      submitOrderNo: '',
      KHWarehouseList: [],
      purchaseData: {
        customerName: '',
        customerCode: '',
        companyCode,
        supplier,
        supplierNo: '',
        supplierDict,
        previousCompany: '',
        purchaseGroup: '',
        orderType: id,
        createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
        createTime: moment().format('YYYY-MM-DD'),
        paymentTermCode: paymentTermCode || '',
        currency: currency || '',
        receiptContactPhone: '',
        receiptContactName: '',
        receiptAddress: '',
        receiveContactName: '',
        receiveContactPhone: '',
        receiveAddress: '',
        previousSupplier: null,
        supplierContactName: '',
        supplierContactPhone: '',
        supplierAddress: '',

        tariff: undefined,
        tariffCurrency: '',
        tariffCurrencyInput: '',
        tariffSupplierNo: '',
        saleTax: undefined,
        saleTaxCurrency: '',
        saleTaxCurrencyInput: '',
        saleTaxSupplierNo: '',
        latePayment: undefined,
        latePaymentCurrency: '',
        latePaymentCurrencyInput: '',
        latePaymentSupplierNo: '',
        premium: undefined,
        premiumCurrency: '',
        premiumCurrencyInput: '',
        premiumSupplierNo: '',
        intlShippingTypeSelect: '',
        intlShippingType: '',
        intlShipping: undefined,
        intlShippingCurrency: '',
        intlShippingCurrencyInput: '',
        intlShippingSupplierNo: '',
        customsFeeTypeSelect: '',
        customsFeeType: '',
        customsFee: undefined,
        customsFeeCurrency: '',
        customsFeeCurrencyInput: '',
        customsFeeSupplierNo: '',
        other: undefined,
        otherCurrency: '',
        otherCurrencyInput: '',
        otherSupplierNo: '',
        itemList: [{
          itemQuantity: 0,
          itemNo: '00010',
          uuid: shortid.generate(),
          isEmptyLine: true,
          receivedQuantity,
          projectCategory,
          isReturn,
          materialDescription: '',
          soNo: '',
          soItemNo: '',
          oaItemNo: '',
          oaNo: '',
          oaType: '',
          supplierMaterialNo: '',
          trackNo: '',
          unit: '',
          itemDeliveryDate: '',
          taxedPrice: '',
          priceTimes: '',
          generalLedgerAccount: '',
          costCenter: '',
          inputTax: '',
          materialGroup: ''
        }],
        poExtend: {
          isDelayedPayment: 0
        }
      },
      activeTab: 'base',
      showDetail: false,
      itemDetail: null,
      supplierAddressList: [],
      supplierContactList: [],
      billingAddressList: [],
      receiptAddressList: [],
      receiptContactList: [],
      receiveContactList: [],
      receiveAddressList: [],
      shippingAddressList: [],
      records: [],
      showBatchImport: false,
      showPunchImport: false,
      costCenterList: [],
      keepSupplier: null,
      pageLoading: false,
      needSplit: false, // 是否需要拆单
      commonOaNumber: ''
    }
  },
  async created() {
    this.pageLoading = true
    await this.initFields()
    this.clearValidate()
    await getAllDictList(this)
    const defaultCompany = await getUserCompany()
    this.purchaseData.companyCode = defaultCompany
    const createUser = window.CUR_DATA.user && window.CUR_DATA.user.name
    const user = this.purchaseList.find(item => item.securityUsername === createUser)
    if (user) this.purchaseData.purchaseGroup = user.groupCode
    if (this.purchaseData.orderType === 'Z003') {
      this.purchaseData.supplier = this.getTransferSupplier()
    }
    this.setDefaultCompanyCode(this.companyFactoryList, this.purchaseData.companyCode)
    if (this.purchaseData.orderType === 'Z008') {
      await this.getCostCenter(this.purchaseData.companyCode)
    }
    if (this.purchaseData.orderType === 'Z006') {
      if (this.defaultWarehouseConfigList && this.defaultWarehouseConfigList.length === 0) {
        await this.$store.dispatch('orderPurchase/queryWarehouseConfigList')
      }
    }
    await this.initFesto()
    this.clearValidate()
    this.pageLoading = false
  },
  mounted () {
    this.reflow()
    this.listener = () => {
      setTimeout(this.reflow, 200)
    }
    registerReCalcStyle(this.listener)
    this.$nextTick(() => {
      window.addEventListener('resize', this.setTableHeight, false)
    })
  },
  watch: {
    'purchaseData.companyCode': function (newVal) {
      if (newVal) {
        const isKHGroup = getKHpurchaseGroup(this.purchaseData.purchaseGroup, this.dictList['componentWarehouse'])
        if (isKHGroup) {
          this.getKHWarehouseUsing()
        }
      }
    },
    'purchaseData.purchaseGroup': function(newVal) {
      if (this.purchaseData.companyCode && newVal) {
        this.getKHWarehouseUsing()
      }
    }
  },
  beforeDestroy () {
    unRegisterReCalcStyle(this.listener)
    window.removeEventListener('resize', this.setTableHeight, false)
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole,
      dictList: state => state.orderPurchase.dictList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      disableCompanyCodeList: state => state.orderPurchase.disableCompanyCodeList,
      warehouseList: state => state.orderPurchase.warehouseList,
      purchaseList: state => state.orderPurchase.purchaseList,
      defaultWarehouseConfigList: state => state.orderPurchase.defaultWarehouseConfigList,
      createFields: state => state.orderPurchase.create,
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    }),
    isGrossMarginOrderType() {
      const { orderType } = this.purchaseData
      const disList = [ 'Z001', 'Z010' ]
      return disList.includes(orderType)
    },
    showHeaderIcon () {
      return this.showHeader ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
    },
    showCharge() {
      const { orderType } = this.purchaseData
      const disList = [ 'Z003', 'Z005' ]
      return !disList.includes(orderType)
    },
    supplierId () {
      const supplier = (this.purchaseData.supplier || {})
      return supplier.providerId || supplier.supplierNo
    },
    factoryList () {
      const { companyCode, supplierNo } = this.purchaseData
      if (companyCode) {
        if (companyCode === '1000' && supplierNo === 'V00004') {
          const list = ['1000', '1100', '1600', '1700', '1800', '2300']
          const factoryList = this.companyFactoryList
            .map(item => item.factoryList)
            .reduce((prev, next) => {
              prev.push(...next)
              return prev
            }, [])
          return factoryList.filter(fac => list.includes(fac.factoryCode))
        } else {
          const currentCompany = this.companyFactoryList.find(item => item.companyCode === companyCode)
          if (currentCompany) {
            return currentCompany.factoryList
          }
        }
      }
      return []
    },
    // 默认工厂取首行
    factoryCode () {
      const { itemList } = this.purchaseData
      if (itemList.length > 1) {
        const firstItem = itemList[0]
        if (firstItem && firstItem.factoryCode) {
          return firstItem.factoryCode
        }
      }
      return this.factoryList && this.factoryList.length > 0 ? this.factoryList[0].factoryCode : ''
    },
    // 默认仓库取首行
    warehouseLocation () {
      const { itemList } = this.purchaseData
      if (itemList.length > 1) {
        const firstItem = itemList[0]
        if (firstItem && firstItem.warehouseLocation) {
          return firstItem.warehouseLocation
        }
      }
      return ''
    },
    totalAmount () {
      console.log('totalAmount in')
      if (this.purchaseData.orderType === 'Z003') {
        return {
          taxedTotal: 0,
          unTaxedTotal: 0
        }
      }
      return calculatePOTotal(this.purchaseData)
    },
    transferSupplierList () {
      const companyCode = this.purchaseData.companyCode
      if (companyCode && this.dictList) {
        return (this.dictList['transferSupplier'] || [])
          .filter(item => item.value.substring(1, 3) === companyCode.substring(0, 2))
      }
      return []
    },
    isDisabledAddItem () {
      // 供应商可能模糊搜索，也可能支持下拉选择
      return !((this.supplierId || this.purchaseData.supplierDict) && this.purchaseData.purchaseGroup)
    },
    fieldList () {
      const { orderType } = this.purchaseData
      const fields = deepClone(this.createFields[orderType]) || []
      if (this.isFesto && !fields.find(item => item.prop === 'customerName')) {
        const item = {
          category: 'baseInfo',
          disabled: false,
          name: '客户名称',
          prop: 'customerName',
          type: 'custom',
          required: true,
          sequence: 17,
          span: 8
        }
        const idx = fields.findIndex(item => item.type === 'checkbox')
        if (idx > -1) {
          fields.splice(idx - 1, 0, item)
        } else {
          fields.push(item)
        }
      }
      return fields
    },
    fieldAllocationContent () {
      const fieldList = this.getFieldsByCategory('allocation')
      return fieldList.filter(item => item.type !== 'checkbox')
    },
    fieldAllocationCheckbox () {
      const fieldList = this.getFieldsByCategory('allocation')
      return fieldList.filter(item => item.type === 'checkbox')
    },
    rules () {
      const requiredItems = this.fieldList.filter(item => item.required === true)
      const rules = {}
      requiredItems.forEach(item => {
        const trigger = ~['custom', 'select', 'input'].indexOf(item.type) ? ['change', 'blur'] : 'blur'
        rules[item.prop] = [
          { required: true, message: `${item.name}必填`, trigger }
        ]
      })
      return rules
    }
  },
  methods: {
    getButtonAuth,
    finishDataApi,
    /**
     * 批量设置OA流程编号
     */
     handleOaNumber() {
      if (!this.commonOaNumber) {
        this.$message.error('请输入OA流程编号后再操作')
        return
      }
      try {
        const { itemList = [] } = this.purchaseData
        if (itemList.filter(item => !!item.skuNo || !!item.materialDescription).length === 0) {
          this.$message.error('请先添加商品后再操作')
          return
        }
        itemList.forEach(item => {
          item.oaNo = this.commonOaNumber
        })
        this.$message.success('批量设置OA流程编号成功')
      } catch (error) {
        this.$message.error('批量设置OA流程编号失败，请重试')
      }
    },
    getItemType(prop) {
      return this.dictList[prop].find((item) => Number(item.value) === Number(this.purchaseData[prop]))?.name || ''
    },
    getKHWarehouseUsing() {
      const isKHGroup = getKHpurchaseGroup(this.purchaseData?.purchaseGroup, this.dictList['componentWarehouse'])
      if (this.purchaseData?.companyCode && isKHGroup && this.purchaseData.orderType === 'Z006') {
        listKHWarehouseUsingPOST({ factoryCode: this.purchaseData.companyCode }).then((data) => {
          if (data && data.length > 0) {
            this.KHWarehouseList = data
          } else {
            this.KHWarehouseList = []
          }
        })
      }
    },
    isFixedAssets(orderType, projectCategory) {
      return orderType === 'Z004' && projectCategory === 'A'
    },
     // 上传文件
    async httpRequestHandle (file) {
      // 校验大小
      // const i//sGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit
      // if (isGtLimit) {
      // this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
      // return
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      const response = await upload('mm/attachment/shippingAttachment', file.file);
      if (response?.url) {
        if (!this.purchaseData?.initialShippingAmountAttachmentList) this.purchaseData.initialShippingAmountAttachmentList = []
        this.purchaseData.initialShippingAmountAttachmentList.push({
          uid: file.file.uid,
          fileName: file.file.name,
          fileUrl: response.url,
          attachmentType: 3
        })
      } else {
        // this.$message.error('上传失败！')
      }
      loading.close()
    },
    // 删除文件
    handleRemove(file, fileList) {
      remove(this.purchaseData?.initialShippingAmountAttachmentList, function (item) {
        return item.uid === file.uid
      });
    },
    async initFesto () {
      const isFesto = window.isFesto
      if (!isFesto) return
      this.isFesto = true
      this.setFestoSupplier()
      delete window.isFesto
    },
    setFestoSupplier () {
      const { supplierName, supplierNo, providerId } = this.festoSupplierInfo
      if (supplierName && supplierNo) {
        if (this.$refs.supplier && this.$refs.supplier[0] && this.$refs.supplier[0].supplierList) {
          this.$refs.supplier[0].supplierList.push(...[
            { supplierNo: '供应商编码', supplierName: '供应商名称' },
            { supplierNo, supplierName, providerId }
          ])
        }
        this.purchaseData.supplier = { supplierNo, supplierName, providerId }
        this.handleSupplierChange({ supplierNo, supplierName })
        this.setCustomerOptions(supplierNo)
      }
    },
    setCustomerOptions (supplierNo) {
      getFestoCustomer({ supplierNo })
        .then(data => {
          if (data && data.length > 0) {
            this.customerNameOptions = data.map(item => ({
              value: item.supplierCustomCode,
              label: item.customName
            }))
          } else {
            this.customerNameOptions = []
          }
        })
    },
    jumpToSto () {
      this.submitDialog = false
      this.$closeTag(this.$route.path)
      const orderNo = this.submitOrderNo
      setTimeout(() => {
        this.$router.push({
          path: '/orderSale/stoList',
          query: { orderNo }
        })
      }, 300)
    },
    ensureSubmit () {
      this.submitDialog = false
      this.$closeTag(this.$route.path)
      const orderNo = this.submitOrderNo
      if (this.needSplit) {
        this.$closeTag('/purchaseReport/trackingOrder')
        setTimeout(() => {
          this.$router.push({
            path: '/purchaseReport/trackingOrder',
            query: { orderNo }
          })
        }, 300)
      } else {
        setTimeout(() => {
          this.$router.push({
            path: `/orderPurchase/detail/${orderNo}`,
            query: { tagName: `${orderNo}详情` }
          })
        }, 300)
      }
    },
    continueCreate () {
      this.submitDialog = false
      const { orderType } = this.purchaseData
      this.$closeTag(this.$route.path)
      setTimeout(() => {
        this.$router.push({
          path: `/orderPurchase/create/${orderType}`
        })
      }, 200)
    },
    handleClose (done) {
      done && done()
    },
    toggleButton () {
      this.showHeader = !this.showHeader
      this.setTableHeight()
    },
    handleTabClick (tab) {
      if (tab.name) {
        this.activeTab = tab.name
      }
      this.showHeader = true
      this.setTableHeight()
    },
    setTableHeight () {
      const grid = this.$refs.itemTable
      setTimeout(() => {
        grid.tableHeight = window.innerHeight - grid.$el.offsetTop - 200
        if (grid.tableHeight < 355) {
          grid.tableHeight = 355
        }
      }, 100)
    },
    hideByPurchaseGroup (prop) {
      // 采购员禁用付款条件，采购姓名
      if (prop === 'purchaseGroup' || prop === 'paymentTermCode') {
        return findTopRole(this.userRole) === 'PMS采购员'
      }
    },
    disableCharge(prop) {
      if (this.purchaseData.isOverchargeFree === 1) return true
      if (/tariff/.test(prop)) {
        if (this.purchaseData.isZeroTariff) return true
      }
      if (/intlShipping/.test(prop)) {
        if (this.purchaseData.isZeroIntlShipping) return true
      }
      if (/customsFee/.test(prop)) {
        if (this.purchaseData.isZeroCustomsFee) return true
      }
    },
    resetChargeItem (type) {
      this.purchaseData[`${type}`] = undefined
      this.purchaseData[`${type}Amount`] = undefined
      this.purchaseData[`${type}Currency`] = null
      this.purchaseData[`${type}CurrencyInput`] = null
      this.purchaseData[`${type}SupplierNo`] = ''
      if (type === 'intlShipping' || type === 'customsFee') {
        this.purchaseData[`${type}Type`] = ''
        this.purchaseData[`${type}TypeSelect`] = ''
      }
      const { itemList } = this.purchaseData
      itemList.forEach(item => {
        item[`${type}`] = undefined
        item[`${type}Amount`] = undefined
        item[`${type}Currency`] = null
        item[`${type}CurrencyInput`] = null
        item[`${type}SupplierNo`] = ''
      })
      this.$set(this.purchaseData.itemList, itemList)
    },
    handleChangeChargeCheckbox (prop, value) {
      // const chargeProps = ['tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment']
      const checkboxProps = ['isZeroTariff', 'isZeroIntlShipping', 'isZeroCustomsFee']
      // eslint-disable-next-line eqeqeq
      if (checkboxProps.includes(prop) && value == 1) {
        switch (prop) {
          case 'isZeroTariff':
            this.resetChargeItem('tariff'); break;
          case 'isZeroIntlShipping':
            this.resetChargeItem('intlShipping'); break;
          case 'isZeroCustomsFee':
            this.resetChargeItem('customsFee'); break;
        }
      }
    },
    disabledCode (code) {
      // const ableList = [ '1100', '1600', '1700', '1800', '1900', '2300' ]
      // return !ableList.includes(code)
      if (Array.isArray(this.disableCompanyCodeList)) {
        return !this.disableCompanyCodeList.includes(code)
      }
    },
    reactiveSetItem (row) {
      const idx = this.purchaseData.itemList.findIndex(item => item.itemNo === row.itemNo)
      if (idx > -1) {
        console.log('reactiveSetItem...')
        this.$set(this.purchaseData.itemList, idx, row)
      }
    },
    isIndeterminate (prop) {
      // 交期确认中间状态
      if (prop === 'isUrgent') {
        let ret = false
        const itemList = this.purchaseData.itemList.filter(item => !item.isEmptyLine && !item.isDeleted)
        if (!itemList.every(item => item.isUrgent) &&
          !itemList.every(item => !item.isUrgent)
        ) {
          ret = true
        }
        return ret
      }
      return false
    },
    setTitleCheckbox (prop) {
      const { itemList } = this.purchaseData
      if (prop === 'isUrgent') {
        const filterList = itemList.filter(item => !item.isEmptyLine)
        if (filterList.length && filterList.every(item => item.isUrgent)) {
          this.$set(this.purchaseData, 'isUrgent', 1)
        } else {
          this.$set(this.purchaseData, 'isUrgent', 0)
        }
      }
    },
    clearValidate () {
      this.$refs['orderForm'] &&
        this.$refs['orderForm'].clearValidate &&
          this.$refs['orderForm'].clearValidate()
    },
    async initFields() {
      const { id } = this.$route.params
      if (this.fieldList && this.fieldList.length === 0) {
        await this.$store.dispatch('orderPurchase/getPoFields', {
          orderType: id,
          routerType: 'create'
        })
      }
    },
    getFieldsByCategory (category) {
      const fieldList = this.fieldList.filter(item => item.category === category && (!item.status || /create/.test(item.status)))
      return fieldList
    },
    reflow () {
      safeRun(() => {
        this.$refs.fixedCreate.style.width = `calc(100% - ${parseInt(document.querySelector('.main-side').clientWidth) + 10}px)`
      })
    },
    async getCostCenter(code, keyword = '') {
      getCostCenterApi({
        companyCode: code,
        costCenter: keyword
      }).then(data => {
        this.costCenterList = data
      })
    },
    fullDetailAddress (item) {
      let ret = ''
      safeRun(() => {
        ret = (item.provinceText || '') +
                (item.cityText || '') +
                (item.regionText || '') +
                (item.streetText || '') +
                (item.detail || '')
      })
      return ret
    },
    getTransferSupplier () {
      const companyCode = this.purchaseData.companyCode
      if (companyCode && this.dictList) {
        const val = `V${companyCode}`
        this.purchaseData.supplierDict = val
        const currentSupplier = (this.dictList['transferSupplier'] || []).find(item => item.value === val)
        return {
          supplierNo: val,
          supplierId: val,
          providerId: val,
          supplierName: currentSupplier ? currentSupplier.name : ''
        }
      }
      return null
    },
    getInputTax (val) {
      const taxRate = Number(((this.dictList['inputTax'] || []).find(item => item.value === val) || {}).description) || 0
      return taxRate
    },
    handleCheckboxChange (val, prop) {
      const { itemList } = this.purchaseData
      if (prop === 'isOverchargeFree') {
        if (val === 1) {
          const inlineList = [ 'tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment' ]
          const titleList = ['shareShipping', 'shareDiscount', ...inlineList]
          safeRun(() => {
            titleList.forEach(charge => {
              this.purchaseData[`${charge}`] = undefined
              this.purchaseData[`${charge}Amount`] = null
              this.purchaseData[`${charge}Currency`] = null
              this.purchaseData[`${charge}CurrencyInput`] = undefined
            })
            itemList.filter(item => !item.isEmptyLine).forEach(item => {
              // item.isFree = 1
              // item.taxedPrice = 0
              // item.untaxedPrice = ''
              // item.taxedTotalAmount = ''
              // item.untaxedTotalAmount = ''
              // item.taxTotalAmount = ''
              titleList.forEach(charge => {
                item[`${charge}Amount`] = undefined
                item[`${charge}Currency`] = null
              })
            })
          })
          safeRun(() => {
            this.purchaseData.shippingAmount = undefined
            this.purchaseData.discountAmount = undefined
          })
        }
      }
      if (prop === 'isUrgent') {
        safeRun(() => {
          itemList.filter(item => !item.isEmptyLine).forEach(item => {
            // 不修改已删除的行
            if (item.isDeleted === 1) return
            // eslint-disable-next-line eqeqeq
            if (val == 1) {
              item.isUrgent = 1
            } else {
              item.isUrgent = 0
            }
          })
        })
      }
      if (prop === 'ownerTransfer') {
        if (!this.purchaseData.taglibIdList) {
          this.purchaseData.taglibIdList = []
        }
        if (val === 1) {
          this.purchaseData.taglibIdList.push(102)
        } else {
          const index = this.purchaseData.taglibIdList.indexOf(102)
          this.purchaseData.taglibIdList.splice(index, 1)
        }
      }
      if (prop === 'strategicStock') {
        if (!this.purchaseData.taglibIdList) {
          this.purchaseData.taglibIdList = []
        }
        if (val === 1) {
          this.purchaseData.taglibIdList.push(101)
        } else {
          const index = this.purchaseData.taglibIdList.indexOf(101)
          this.purchaseData.taglibIdList.splice(index, 1)
        }
      }
      this.purchaseData.itemList = itemList
    },
    handleNumberChange (lossRatio, type) {
      if (type === 'lossRatio') {
        lossRatio = lossRatio === undefined ? 0 : lossRatio
        let items = []
        safeRun(() => {
          items = this.purchaseData.itemList.filter(item => Array.isArray(item.componentList) && item.componentList.length > 0)
        })
        safeRun(() => {
          items.forEach(item => {
            let { itemQuantity, inventoryUnitMole = 1, inventoryUnitDeno = 1 } = item
            if (!inventoryUnitMole) inventoryUnitMole = 1
            if (!inventoryUnitDeno) inventoryUnitDeno = 1
            item.componentList.forEach(comp => {
              const { materialGroupNum } = comp
              // const number = Number((comp._quantity * (1 + lossRatio * 0.01)).toFixed(3))
              if (!itemQuantity) itemQuantity = 0
              let number = Number((itemQuantity * inventoryUnitMole / inventoryUnitDeno * (comp._quantity / comp.bomQuantity) * (1 + lossRatio * 0.01)).toFixed(3))
              if (materialGroupNum === 430) {
                number = Math.ceil(number)
              }
              comp.componentRequiredQuantity = number
            })
          })
        })
      }
    },
    inWarehouse(code) {
      let ret = false
      safeRun(() => {
        ret = this.warehouseList.filter(item => item.warehouseLocationCode === code).length
      })
      return ret
    },
    inCostCenter(val) {
      let ret = false
      safeRun(() => {
        ret = this.costCenterList.filter(item => item.costCenter === val).length
      })
      return ret
    },
    inGeneralLedger(val) {
      let ret = false
      safeRun(() => {
        ret = this.dictList.generalLedger.filter(item => item.value === val).length
      })
      return ret
    },
    setDefaultCompanyCode (data, companyCode) {
      if (data && data.length > 0) {
        if (companyCode) {
          const currentCompany = data.find(item => item.companyCode === companyCode)
          if (currentCompany) {
            const factoryList = currentCompany.factoryList
            if (factoryList && factoryList.length > 0) {
              const factoryCode = factoryList[0].factoryCode
              if (factoryCode) {
                this.purchaseData.itemList[0].factoryCode = factoryCode
              }
            }
          }
        }
      }
    },
    startLoading () {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      return loading
    },
    endLoading (loading) {
      if (loading) {
        loading.close()
      }
    },
    validateRelateOrder (params) {
      let ret = true
      let errorText = ''
      if (params && Array.isArray(params.itemList)) {
        params.itemList.forEach((item, index) => {
          if ((item.soNo && !item.soItemNo) || (item.soItemNo && !item.soNo)) {
            ret = false
            errorText += index + 1 + ' '
          }
        })
      }
      if (!ret) {
        this.$message.error(`第${errorText}行关联销售单号、行号必须同时填写！`)
      }
      return ret
    },
    validateInventory (params) {
      // 可用库存均满足需求数量
      safeRun(() => {
        if (params.itemList.every(item =>
          item.componentList && item.componentList.every(comp => comp.componentRequiredQuantity <= comp.availableQty)
        )) {
          this.$confirm('委外组件可用库存已满足需求数量，请尽快安排委外发料。', '操作提示', {
            confirmButtonText: '委外发料',
            cancelButtonText: '返回',
            type: 'warning'
          }).then(() => {
            this.$message({ type: 'success', message: '委外发料!' })
          }).catch(() => {
            this.$message({ type: 'success', message: '返回!' })
          })
        }
      })
    },
    validatePrice (params) {
      let ret = true
      const priceCheckList = [ 'Z001', 'Z002', 'Z006', 'Z013', 'Z005' ]
      // run with catch error
      safeRun(() => {
        if (~priceCheckList.indexOf(params.orderType)) {
          // eslint-disable-next-line eqeqeq
          let item = params.itemList.filter(item => item.noMaintainPrice && item.isFree != 1)
          if (item.length) {
            ret = false
            this.$message.error(`sku:${item.map(i => i.skuNo).join(',')} 没有价格信息且没有勾选免费！`)
          }
        }
        params.itemList.forEach(item => delete item.noMaintainPrice)
      })
      return ret
    },
    validateSo (item) {
      let ret = true
      safeRun(() => {
        const ret1 = item.itemList.some(it => {
          // 关联销售单, 关联销售单行号
          const { soNo, soItemNo } = it
          return soItemNo && !soNo
        })
        if (ret1) {
          ret = false
          this.$message.error('填写关联销售单行号，则关联销售单号必填！')
          return
        }
        const ret2 = item.itemList.some(it => {
          // 关联销售单, 关联销售单行号, 关联销售单计划行号
          const { soNo, soItemNo, soPlanNo } = it
          return soPlanNo && (!soNo || !soItemNo)
        })
        if (ret2) {
          ret = false
          this.$message.error('填写关联销售单计划行，则关联销售单行号、关联销售单号必填！')
        }
        const ret3 = item.itemList.some(it => {
          const { projectCategory, materialDescription } = it
          return !materialDescription && (['A', 'S'].includes(projectCategory))
        })
        const ret4 = item.itemList.some(it => {
          const { projectCategory, fixedAssetsCardNo } = it
          return !fixedAssetsCardNo && (projectCategory === 'A')
        })
        if (item.orderType === 'Z004' && ret3) {
          ret = false
          this.$message.error('请填写物料描述')
        } else if (item.orderType === 'Z004' && ret4) {
          ret = false
          this.$message.error('请填写资产卡片号')
        }
        const ret5 = item.itemList.some(it => {
          const { inputTax, skuNo } = it
          return skuNo && !inputTax
        })
        if (ret5 && item.orderType !== 'Z003') {
          ret = false
          this.$message.error('请选择进项税！')
        }
        // const ret6 = item.itemList.some(it => {
        //   const { specialSupplySo, specialSupplySoItem, projectCategory } = it
        //   return projectCategory === 'S' && (!specialSupplySo || !specialSupplySoItem)
        // })
        // if (ret6 && item.orderType === 'Z004' && item.isDirect === 1) {
        //   ret = false
        //   this.$message.error('专供销售单号、专供销售单行号必填!')
        // }
      })
      return ret
    },
    handleItemPlanNoSubmit (item) {
      if (item && item.itemNo) {
        item.itemNo = null
      }
      item && item.planList && item.planList.forEach(plan => {
        if (plan.planNo) {
          plan.planNo = null
        }
      })
    },
    filterValid (item) {
      const { orderType } = this.purchaseData
      if (item.isEmptyLine) return false
      const validDesc = [
        'Z007', 'Z008', 'Z010', 'Z004'
      ]
      if (validDesc.includes(orderType)) {
        return item.materialDescription
      }
      return item.skuNo
    },
    parseDate(date) {
      var result = new Date(date);
      result.setMinutes(result.getMinutes() - result.getTimezoneOffset());
      return result;
    },
    diffDays(second, first) {
      return Math.abs(Math.round((this.parseDate(second) - this.parseDate(first)) / (1000 * 60 * 60 * 24)));
    },
    formatSupplierNo (params) {
      const emptyStringList = [
        'tariffSupplierNo',
        'saleTaxSupplierNo',
        'intlShippingSupplierNo',
        'latePaymentSupplierNo',
        'customsFeeSupplierNo'
      ]
      params.itemList.forEach(item => {
        emptyStringList.forEach(prop => (!item[prop] && item[prop] !== undefined) ? (item[prop] = '') : null)
      })
    },
    festoSubmit () {
      if (this.isFesto && this.submitFestoList.length) {
        const { supplierNo } = this.purchaseData
        finishDataApi({ supplierNo: supplierNo, ids: this.submitFestoList.map(item => item.id).join(',') })
          .then(res => {
            console.log(res)
          })
      }
    },
    formatData (params) {
      // TODO
      safeRun(() => {
        params.itemList.forEach(item => {
          delete item.oaType
        })
      })
    },
    handleSubmit (formName) {
      const materialList = [ 'Z007', 'Z008' ]
      this.$refs[formName].validate(async (valid, validteItem) => {
        const flag = Object.keys(validteItem).every(v => /receive|receipt/.test(v))
        if (Object.keys(validteItem).length > 0 && flag) {
          this.activeTab = 'communication'
        }
        let itemList = deepClone(this.purchaseData.itemList)
        if (valid) {
          if (!this.validateSo(this.purchaseData)) return
          const { supplier, orderType } = this.purchaseData

          // 行上打标战略备货
          const processOrderChannel = (item) => {
            if (this.purchaseData.strategicStock) {
              if (isEmpty(item?.poItemExtend?.orderChannelList)) {
                return [3]
              } else if (item?.poItemExtend?.orderChannelList?.includes(3)) {
                return item.poItemExtend.orderChannelList
              } else {
                return [...item?.poItemExtend?.orderChannelList, 3]
              }
            } else {
              return item?.poItemExtend?.orderChannelList
            }
          }
          itemList = itemList.filter(this.filterValid).map(item => {
            this.handleItemPlanNoSubmit(item)
            const { itemQuantity, taxedPrice, materialGroupNum, unit, taxedTotalAmount, newOrderReason, orderReason } = item
            const result = {
              ...item,
              itemQuantity: itemQuantity ? itemQuantity.toFixed(3) : 0,
              taxedPrice: taxedPrice ? taxedPrice.toFixed(2) : taxedPrice === '' ? taxedPrice : 0,
              taxedTotalAmount: formatAmount(taxedTotalAmount || 0, 2),
              materialGroupId: materialGroupNum,
              orderReason: newOrderReason && orderReason === '其他' ? newOrderReason : orderReason,
              priceUnit: unit
            }
            // 非进口订单的国际运费类型和报关杂费类型也要有个默认值
            if (item.intlShippingAmount && !item.intlShippingType) result.intlShippingType = 4
            if (item.customsFeeAmount && !item.customsFeeType) result.customsFeeType = 5

            result.itemQuantity = Number(result.itemQuantity)
            if ((result.warehouseLocation ?? '') === '') {
              result.warehouseLocation = null
            }
            result.poItemExtend = {
              ...item.poItemExtend,
              initGrossMargin: item?.poItemExtend?.lastGrossMargin,
              skuNo: item.skuNo,
              untaxedSalePrice: item.untaxedSalePrice,
              sundryAmount: item.sundryAmount,
              rebateAmount: item.rebateAmount,
              orderChannelList: processOrderChannel(item)
            }
            if (materialList.includes(orderType)) {
              delete result.warehouseLocation
              delete result.warehouseLocationCode
              delete result.warehouseLocationName
            }
            if (orderType === 'Z010') {
              result.generalLedgerAccount = '********'
            }
            delete result.uuid
            if (orderType !== 'Z007' && (orderType === 'Z004' && result.projectCategory !== 'A' && (result.fixedAssetsCardNo ?? '') === '')) {
              delete result.fixedAssetsList
            }
            // if (orderType === 'Z003') {
            //   result.isManualReason = 1
            // }
            delete result.newOrderReason
            // 删除固定资产组件
            // delete result.fixedAssetsList
            return result
          })
          const params = {
            ...this.purchaseData,
            itemList
          }
          // 非进口订单的国际运费类型和报关杂费类型也要有个默认值
          if (params.intlShippingAmount && !params.intlShippingType) params.intlShippingType = 4
          if (params.customsFeeAmount && !params.customsFeeType) params.customsFeeType = 5

          delete params.createTime
          if (supplier) {
            const { supplierNo, supplierName } = supplier
            params.supplierNo = supplierNo
            params.supplierName = supplierName
            delete params.supplier
          }
          if (!this.validatePrice(params)) return
          // if (orderType === 'Z006') {
          //   this.validateInventory(params)
          // }
          if (orderType === 'Z010') {
            if (!this.validateRelateOrder(params)) return
          }
          // this.formatSupplierNo(params)
          if (typeof params.customerName === 'object') {
            params.customerCode = params.customerName.value
            params.customerName = params.customerName.label
          }
          params.poExtend = {
            ...params.poExtend,
            lastGrossMargin: this.totalAmount.grossMarginTotal,
            initGrossMargin: this.totalAmount.grossMarginTotal,
            shareSundryAmount: params.sundryAmount,
            sundryReason: params.sundryReason?.join(',') || '',
            sundryReasonDetail: params.sundryReasonDetail
          }
          params.initialShippingAmount = params.shareInitialShippingAmount
          this.formatData(params)
          const splitOrderData = itemList.map(item => {
            const data = {
              factoryCode: item.factoryCode,
              warehouseLocationCode: item.warehouseLocation,
              skuNo: item.skuNo
            }
            if (orderType === 'Z003') {
              return { ...data, shipWarehouseLocationCode: item.shipWarehouseLocation }
            }
            return data
          })
          this.needSplit = await getSplitOrderTips(this, splitOrderData);
          const loading = this.startLoading()
          createPO(params).then(res => {
            this.endLoading(loading)
            if (res) {
              const { code, data, msg } = res
              if (code === 0 && data) {
                const { orderNo } = data
                if (orderNo) {
                  this.submitDialog = true
                  this.submitOrderNo = orderNo
                  this.festoSubmit()
                  // this.$confirm(`订单${orderNo}创建成功！`, '成功', {
                  //   confirmButtonText: '确定',
                  //   cancelButtonText: '继续创建',
                  //   type: 'success'
                  // }).then(() => {
                  //   this.$closeTag(this.$route.path)
                  //   setTimeout(() => {
                  //     this.$router.push({
                  //       path: `/orderPurchase/detail/${orderNo}`,
                  //       query: { tagName: `${orderNo}详情` }
                  //     })
                  //   }, 300)
                  // }).catch(() => {
                  //   this.$closeTag(this.$route.path)
                  //   setTimeout(() => {
                  //     this.$router.push({
                  //       path: `/orderPurchase/create/${orderType}`
                  //     })
                  //   }, 200)
                  // })
                }
              } else if (code !== 0) {
                let errMsg = '订单创建失败！<br>'
                submitErrorHandler(errMsg, data, msg)
              }
            }
          })
        }
      })
    },
    getInfoGroup (idx) {
      const baseItem = this.field.find(item => item.name === 'base')
      if (baseItem && baseItem.children) {
        const baseInfoItem = baseItem.children.find(item => item.name === 'info')
        if (baseInfoItem && baseInfoItem.children) {
          const groupInfoItem = baseInfoItem.children.find(item => item.name === `line${idx}`)
          if (groupInfoItem && groupInfoItem.children) {
            return groupInfoItem.children
          }
        }
      }
      return []
    },
    getFieldGroup (name) {
      const baseItem = this.field.find(item => item.name === name)
      if (baseItem && baseItem.children) {
        return baseItem.children
      }
      return []
    },
    getChargeGroup () {
      const baseItem = this.field.find(item => item.name === 'charge')
      if (baseItem && baseItem.children) {
        return baseItem.children
      }
      return []
    },
    getCommunicationGroup () {
      const baseItem = this.field.find(item => item.name === 'communication')
      if (baseItem && baseItem.children) {
        return baseItem.children
      }
      return []
    },
    getRemarkGroup () {
      const baseItem = this.field.find(item => item.name === 'remark')
      if (baseItem && baseItem.children) {
        return baseItem.children
      }
      return []
    },
    handlePurchaseGroupChange (group) {
      const index = this.getFirstLineUpdateDefaultAddress()
      console.log(index)
      if (index !== null) {
        this.keepSupplier = true
        this.resetDefaultAddress(index)
      }
      this.setCompWarehouseLocation()
    },
    // 修改仓库点更改委外组件仓库地点
    updateCompWarehouseByWarehouse (warehouseLocation, index, row) {
      console.log('updateCompWarehouseByWarehouse')
      if (Array.isArray(row.componentList)) {
        // if (row.componentList.some(comp => comp._defaultConfigWarehouse)) {
        //   console.log('comp._defaultConfigWarehouse')
        // } else {
        row.componentList.forEach(comp => {
          comp.componentWarehouseLocation = getD02ComponentWarehouseLocation(this.purchaseData.orderType, this.purchaseData.purchaseGroup, this.KHWarehouseList, warehouseLocation, warehouseLocation, this.dictList['componentWarehouse'])
          // comp.componentWarehouseLocation = warehouseLocation
        })
        // }
      }
      this.$set(this.purchaseData.itemList, index, row)
    },
    // 修改工厂更改委外组件仓库地点
    updateCompWarehouse (factoryCode, index, row) {
      const { purchaseGroup, orderType } = this.purchaseData
      const configWarehouse = getDefaultWarehouseLocation(this.defaultWarehouseConfigList, purchaseGroup, factoryCode)
      row.componentList.forEach(comp => {
        comp.componentWarehouseLocation = null
        if (configWarehouse) {
          comp.componentWarehouseLocation = configWarehouse
          comp._defaultConfigWarehouse = true
        } else {
          comp._defaultConfigWarehouse = false
        }
        if (!comp.componentWarehouseLocation) {
          comp.componentWarehouseLocation = row.warehouseLocation || row.warehouseLocationCode
        }
        comp.componentWarehouseLocation = getD02ComponentWarehouseLocation(orderType, purchaseGroup, this.KHWarehouseList, row.warehouseLocation || row.warehouseLocationCode, comp.componentWarehouseLocation, this.dictList['componentWarehouse'])
      })
      this.$set(this.purchaseData.itemList, index, row)
    },
    setCompWarehouseLocation () {
      const { orderType, itemList, purchaseGroup } = this.purchaseData
      if (orderType === 'Z006') {
        itemList.forEach(item => {
          if (Array.isArray(item.componentList)) {
            const configWarehouse = getDefaultWarehouseLocation(this.defaultWarehouseConfigList, purchaseGroup, item.factoryCode)
            item.componentList.forEach(comp => {
              comp.componentWarehouseLocation = null
              if (configWarehouse) {
                comp.componentWarehouseLocation = configWarehouse
                comp._defaultConfigWarehouse = true
              } else {
                comp._defaultConfigWarehouse = false
              }
              if (!comp.componentWarehouseLocation) {
                comp.componentWarehouseLocation = item.warehouseLocation || item.warehouseLocationCode
              }
              comp.componentWarehouseLocation = getD02ComponentWarehouseLocation(orderType, purchaseGroup, this.KHWarehouseList, item.warehouseLocation || item.warehouseLocationCode, comp.componentWarehouseLocation, this.dictList['componentWarehouse'])
            })
          }
        })
        this.$set(this.purchaseData, 'itemList', itemList)
      }
    },
    resetSupplier (companyCode) {
      if (this.purchaseData.orderType !== 'Z003') {
        this.setSupplier('')
      } else {
        this.purchaseData.supplier = this.getTransferSupplier()
        this.purchaseData.supplierDict = `V${companyCode}`
      }
    },
    handleChangeCompany (val) {
      if (this.purchaseData.supplier || this.purchaseData.itemList.length > 1) {
        this.$confirm('此操作将删除所选的数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.resetCommunication()
          this.purchaseData.itemList = []
          this.addItemEmptyLine()
          this.resetCharge()
          this.resetSupplier(val)
          this.setDefaultCompanyCode(this.companyFactoryList, val)
          this.previousCompany = val
        }).catch(() => {
          this.purchaseData.companyCode = this.previousCompany || '1000'
        }).finally(() => {
          setTimeout(() => {
            this.clearValidate()
            safeRun(() => {
              this.$refs.company[0].blur()
            })
          }, 80)
        })
      } else {
        this.previousCompany = val
        this.setDefaultCompanyCode(this.companyFactoryList, val)
        this.resetSupplier(val)
        setTimeout(() => {
          this.clearValidate()
        }, 80)
      }
      if (this.purchaseData.orderType === 'Z008') {
        this.getCostCenter(val)
      }
    },
    resetCharge () {
      const inlineList = [ 'tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment', 'other', 'premium' ]
      const titleList = ['shareShipping', 'shareDiscount', ...inlineList]
      safeRun(() => {
        titleList.forEach(charge => {
          this.purchaseData[`${charge}`] = undefined
          this.purchaseData[`${charge}Amount`] = null
          this.purchaseData[`${charge}Currency`] = null
          this.purchaseData[`${charge}CurrencyInput`] = undefined
          if (charge === 'intlShipping' || charge === 'customsFee') {
            this.purchaseData[`${charge}Type`] = ''
            this.purchaseData[`${charge}TypeSelect`] = ''
          }
        })
        this.purchaseData.sundryAmount = null
        this.purchaseData.sundryReason = null
        this.purchaseData.sundryReasonDetail = null
        this.purchaseData.usedRebaseVoucherList = []
        this.purchaseData.shareRebateAmount = null
      })
    },
    handleCustomerChange (val, customer) {
      console.log(val, customer)
      if (typeof customer !== 'object') customer = {}
      // this.purchaseData.customerName = customer.customName
      // this.purchaseData.customerCode = customer.supplierCustomCode
    },
    handleSupplierChange (val) {
      const { itemList = [] } = this.purchaseData
      this.resetCommunication()
      if (itemList.length > 1) {
        this.$confirm('此操作将删除所选的商品, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.purchaseData.itemList = []
          this.purchaseData.paymentTermCode = ''
          this.purchaseData.currency = ''
          this.resetCharge()
          // this.purchaseData.purchaseGroup = ''
          this.addItemEmptyLine()
          this.setSupplier(val)
          this.clearValidate()
          this.previousSupplier = val
        }).catch(() => {
          this.purchaseData.supplier = this.previousSupplier
        }).finally(() => {
          console.log(this.$refs)
          console.log(this.$refs.supplier)
          setTimeout(() => {
            safeRun(() => {
              this.$refs.supplier[0].$refs.supplier.blur()
            })
          }, 80)
        })
      } else {
        this.setSupplier(val)
        setTimeout(() => {
          this.clearValidate()
        }, 80)
      }
    },
    setFestoBySupplierNo (supplierNo) {
      console.log(supplierNo)
      this.isFesto = false
      if (/v45696/gmi.test(supplierNo)) {
        this.isFesto = true
        this.setCustomerOptions(supplierNo)
      }
    },
    setSupplier (val) {
      const { supplierNo } = val
      this.setFestoBySupplierNo(supplierNo)
      const { companyCode } = this.purchaseData
      this.previousSupplier = val
      if (supplierNo) {
        const loading = this.startLoading()
        getSupplierInfo({
          supplierNo,
          factoryCode: companyCode
        }).then(data => {
          this.endLoading(loading)
          if (data) {
            const {
              sapCurrency, paymentTermCode
            } = data
            if (paymentTermCode) {
              if (this.purchaseData.orderType !== 'Z003') {
                this.purchaseData.paymentTermCode = paymentTermCode
              }
            }
            if (sapCurrency) {
              if (this.purchaseData.orderType !== 'Z003') {
                this.purchaseData.currency = sapCurrency
              }
            }
          }
        })
        this.purchaseData.supplierNo = supplierNo
        this.queryAddressList(supplierNo, this.factoryCode)
      } else {
        this.purchaseData.supplier = ''
        this.supplierAddressList = []
        this.supplierContactList = []
        this.purchaseData.supplierContactName = ''
        this.purchaseData.supplierContactPhone = ''
        if (this.purchaseData.orderType !== 'Z003') {
          this.purchaseData.paymentTermCode = ''
          this.purchaseData.currency = ''
        }
      }
    },
    handleChangeSupplierContactName (val) {
      if (val) {
        const supplierContact = this.supplierContactList.find(item => item.name === val)
        if (supplierContact) {
          this.purchaseData.supplierContactPhone = supplierContact.phone
        }
      } else {
        this.purchaseData.supplierContactPhone = ''
      }
    },
    handleChangeSupplierAddress (val) {
      if (val) {
        const supplierAddress = this.supplierAddressList.find(item => val === this.fullDetailAddress(item))
        if (supplierAddress) {
          this.setAddress('supplier', supplierAddress)
        }
      } else {
        this.setAddress('supplier')
      }
    },
    handleChangeReceiptContactName (val) {
      if (val) {
        const contact = this.receiptContactList.find(item => item.name === val)
        if (contact) {
          this.purchaseData.receiptContactPhone = contact.phone
        }
      } else {
        this.purchaseData.receiptContactPhone = ''
      }
    },
    handleChangeReceiptAddress (val) {
      if (val !== undefined) {
        const address = this.receiptAddressList.find(item => val === this.fullDetailAddress(item))
        if (address) {
          this.setAddress('receipt', address)
        }
      } else {
        this.setAddress('receipt')
      }
    },
    handleChangeReceiveContactName (val) {
      if (val) {
        const contact = this.receiveContactList.find(item => item.name === val)
        if (contact) {
          this.purchaseData.receiveContactPhone = contact.phone
        }
      } else {
        this.purchaseData.receiveContactPhone = ''
      }
    },
    handleChangeReceiveAddress (val) {
      if (val) {
        const address = this.receiveAddressList.find(item => this.fullDetailAddress(item) === val)
        if (address) {
          this.setAddress('receive', address)
          this.purchaseData.itemList.map(item => {
            if (item.skuNo && this.purchaseData.orderType !== 'Z003') {
              this.updateDeliveryDaysByWarehouseLocation(item, 2)
            }
          })
        }
      } else {
        this.setAddress('receive')
      }
    },
    handleBaseItemSelectChange (val, prop) {
      if (prop === 'purchaseGroup' && this.factoryCode) {
        // 更新错误以及切换提示
        this.$forceUpdate()
        const { supplier = {}, itemList } = this.purchaseData
        if (supplier.supplierNo) {
          const item = itemList[0] || {}
          const { soNo } = item
          this.queryDefaultAddress(supplier.supplierNo, val, this.factoryCode, this.warehouseLocation, soNo)
        } else {
          this.$message.error('请先选择供应商！')
        }
      }
      if (prop === 'supplierDict') {
        const currentSupplier = (this.dictList['transferSupplier'] || [])
          .find(item => item.value === val)
        this.purchaseData.supplier = {
          supplierNo: val,
          supplierId: val,
          providerId: val,
          supplierName: currentSupplier ? currentSupplier.name : ''
        }
      }
      if (prop === 'paymentTermCode') {
        console.log('change paymentTermCode...')
        // if (this.purchaseData.itemList.filter(item => !item.isEmptyLine && !item.isDeleted).length > 0) {
        //   const { supplierNo } = this.purchaseData.supplier
        //   const { purchaseGroup } = this.purchaseData
        //   const { factoryCode, warehouseLocation, soNo } = this.purchaseData.itemList[0]
        //   if (factoryCode && purchaseGroup && supplierNo) {
        //     this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo)
        //   }
        // }
      }
    },
    reCalcCompQuantity (oldData, rowItem, ignoreCompare) {
      let ret = false
      if (!ignoreCompare) {
        if (oldData.itemQuantity !== rowItem.itemQuantity || oldData.planList.length !== rowItem.planList.length) {
          ret = true
        }
      }
      let { lossRatio } = this.purchaseData
      let { itemQuantity, inventoryUnitMole = 1, inventoryUnitDeno = 1 } = rowItem
      if (!inventoryUnitMole) inventoryUnitMole = 1
      if (!inventoryUnitDeno) inventoryUnitDeno = 1
      if (!lossRatio) lossRatio = 0
      // console.log('materialGroupNum:', materialGroupNum)
      if (!rowItem || !Array.isArray(rowItem.componentList)) return
      rowItem.componentList.forEach(comp => {
        const { materialGroupNum } = comp
        if (!itemQuantity) itemQuantity = 0
        let number = Number((itemQuantity * inventoryUnitMole / inventoryUnitDeno * (comp._quantity / comp.bomQuantity) * (1 + lossRatio * 0.01)).toFixed(3))
        // console.log('materialGroupNum:', materialGroupNum)
        if (materialGroupNum === 430) {
          number = Math.ceil(number)
        }
        comp.componentRequiredQuantity = number
      })
      return ret
    },
    // 商品行详情修改数据
    async handleUpdateItem (val) {
      const { purchaseData, data, updateTypes = [] } = val
      console.log(data);
      const { orderType } = this.purchaseData
      if (purchaseData && purchaseData.uuid) {
        const idx = this.purchaseData.itemList.findIndex(item => item.uuid === purchaseData.uuid)
        if (idx > -1) {
          // let detailData = {}
          // let planData = {}
          // let priceData = {}

          let newData = this.purchaseData.itemList[idx]
          if (updateTypes.includes('detail')) {
            const {
              itemRemark = '', materialDescription = '', isLastInvoice,
              supplierOrderNo = '', specialSupplySo = '', specialSupplySoItem = '',
              oaItemNo = '', oaNo = '', oaType = ''
            } = data.detailData
            newData = {
              ...newData,
              supplierOrderNo,
              itemRemark,
              materialDescription,
              isLastInvoice,
              specialSupplySo,
              specialSupplySoItem,
              oaItemNo,
              oaNo,
              oaType
            }
          }
          if (updateTypes.includes('price')) {
            // 返利和杂费在商品行详情无法修改，因此这里不需要重新计算头上的返利和杂费金额
            const {
              tariffAmount, tariffSupplierNo, tariffCurrency,
              saleTaxAmount, saleTaxSupplierNo, saleTaxCurrency,
              intlShippingAmount, intlShippingSupplierNo, intlShippingCurrency, intlShippingType,
              latePaymentAmount, latePaymentSupplierNo, latePaymentCurrency,
              customsFeeAmount, customsFeeSupplierNo, customsFeeCurrency, customsFeeType,
              premiumAmount, premiumSupplierNo, premiumCurrency,
              otherAmount, otherSupplierNo, otherCurrency
            } = data.priceData
            newData = {
              ...newData,
              tariffAmount,
              tariffSupplierNo,
              tariffCurrency,
              saleTaxAmount,
              saleTaxCurrency,
              saleTaxSupplierNo,
              intlShippingAmount,
              intlShippingCurrency,
              intlShippingSupplierNo,
              intlShippingType,
              latePaymentAmount,
              latePaymentCurrency,
              latePaymentSupplierNo,
              customsFeeAmount,
              customsFeeCurrency,
              customsFeeSupplierNo,
              customsFeeType,
              premiumAmount,
              premiumSupplierNo,
              premiumCurrency,
              otherAmount,
              otherSupplierNo,
              otherCurrency
            }
            // this.handleResetAssign()
          }
          if (updateTypes.includes('entrust')) {
            const componentList = data.entrustData || []
            if (componentList.length > 0) {
              // const itemQuantity = componentList.reduce((acc, item) => {
              //   return item.fixedAssetsCardQuantity + acc
              // }, 0)
              // if (!planData.componentList) {
              newData = {
                ...newData,
                componentList
              }
              // }
            }
          }
          if (updateTypes.includes('assets') && ['Z007', 'Z004'].includes(orderType)) {
            let fixedAssetsList = data.assetsData || []
            fixedAssetsList = fixedAssetsList.slice(0, 1)
            if (fixedAssetsList.length > 0) {
              const itemQuantity = fixedAssetsList.reduce((acc, item) => {
                return (item.fixedAssetsCardQuantity || 0) + acc
              }, 0)
              newData = {
                ...newData,
                fixedAssetsList,
                itemQuantity
              }
            }
          }
          if (updateTypes.includes('plan')) {
            const planList = data.planData || []
            if (planList.length > 0) {
              const itemQuantity = planList.reduce((acc, item) => {
                return item.deliveryQuantity + acc
              }, 0)
              const { itemNo } = this.purchaseData.itemList[idx]
              const currentItemQuantity = (this.purchaseData.itemList[idx] || {}).itemQuantity || 0
              const hasAssign = isAssigned(this.purchaseData.itemList[idx])
              if (hasAssign && itemQuantity > currentItemQuantity) {
                this.$message.error(`项目行${itemNo}已分摊运费或折扣或返利，不允许将订单数量改大。`)
                return
              }
              // 如果数量减少，重新计算分摊
              if (hasAssign && itemQuantity < currentItemQuantity) {
                await assignByQuantity(this.purchaseData, this.purchaseData.itemList[idx], currentItemQuantity, itemQuantity).then(data => {
                  if (data) {
                    this.purchaseData = merge(this.purchaseData, data.data)
                  }
                })
              }
              // const taxRate = this.getInputTax(inputTax)
              // const taxedTotalAmount = formatAmount(taxedPrice ? taxedPrice * itemQuantity / priceTimes : 0, 2)
              // const untaxedTotalAmount = formatAmount(taxedPrice ? (taxedPrice / (1 + taxRate * 0.01)) * itemQuantity / priceTimes : 0, 2)
              // const taxTotalAmount = formatAmount(taxedTotalAmount - untaxedTotalAmount, 2)
              const newPlanList = planList.filter(item => item.deliveryDate && item.deliveryQuantity).sort((a, b) => {
                return (new Date(a.deliveryDate)).getTime() - (new Date(b.deliveryDate)).getTime()
              })
              const itemDeliveryDate = newPlanList[0] ? moment(newPlanList[0].deliveryDate || Date.now()).format('YYYY-MM-DD') : ''
              newData = {
                ...newData,
                planList: newPlanList,
                itemQuantity,
                itemDeliveryDate
              }
              await calcUnTaxedPrice(newData, this.dictList).then(data => {
                if (data) newData = merge(newData, rowEditChange(data))
              })
              // planData.taxedTotalAmount = newData.taxedTotalAmount
              // planData.untaxedTotalAmount = newData.untaxedTotalAmount
              // planData.taxTotalAmount = newData.taxTotalAmount
              // planData.untaxedPrice = newData.untaxedPrice
              // const needDelay = this.reCalcCompQuantity(this.purchaseData.itemList[idx], newData)
              // if (needDelay) {
              //   setTimeout(() => {
              //     this.$set(this.purchaseData.itemList, idx, newData)
              //   }, 200)
              // }
            }
          }
          // const newData = {
          //   ...this.purchaseData.itemList[idx],
          //   ...detailData,
          //   ...planData,
          //   ...priceData,
          //   ...entrustData,
          //   ...assetsData
          // }
          this.$set(this.purchaseData.itemList, idx, newData)
          this.$nextTick(() => {
            this.handleResetAssign()
          })
        }
      }
    },
    getWarehouseList (factoryCode) {
      if (factoryCode) {
        return this.warehouseList.filter(item => item.factoryCode === factoryCode)
      }
      return []
    },
    handleItemDelete () {
      const assignMsg = getAssignInfo(this.records, this.purchaseData.orderType)
      this.$confirm(assignMsg || '此操作将删除所选的商品, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(async () => {
        const size = this.purchaseData.itemList.length
        let isFirstItemDeleted = false
        for (let i = size - 1; i >= 0; i--) {
          const current = this.purchaseData.itemList[i]
          const found = this.records.find(item => item.uuid === current.uuid)
          if (found) {
            this.purchaseData.itemList.splice(i, 1)
            if (i === 0) {
              isFirstItemDeleted = true
            }
          }
        }
        if (isFirstItemDeleted && this.purchaseData.itemList.length > 1) {
          const { supplierNo } = this.purchaseData.supplier
          const { purchaseGroup } = this.purchaseData
          const { factoryCode, warehouseLocation, soNo } = this.purchaseData.itemList[0]
          if (factoryCode) {
            this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo)
          }
        }
        // assignToggle(this.purchaseData, this.records, true)
        await assignToggle(this.purchaseData, this.records, true).then(data => {
          if (data?.data) {
            const { itemList, ...others } = data.data
            this.purchaseData = merge(this.purchaseData, others)
          }
          console.log(this.purchaseData);
        })
        // 删除所有商品行清空通讯信息
        if (this.records.length === size - 1) {
          this.resetCommunication()
          setTimeout(() => {
            this.clearValidate()
          }, 100)
        }
      })
    },
    handleResetAssign () {
      const { itemList } = this.purchaseData
      console.log('itemlist', itemList);
      if (itemList && itemList.length > 0) {
        const chargeProps = ['tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment', 'premium', 'other']
        const sum = {}
        itemList.forEach(item => {
          chargeProps.forEach(prop => {
            const amount = prop + 'Amount'
            const currency = prop + 'Currency'
            const type = prop + 'Type'
            if (!sum[prop]) sum[prop] = {}
            if (item[amount] != null) {
              sum[prop][amount] = (sum[prop][amount] || 0) + item[amount]
              if (item[currency]) {
                sum[prop][currency] = item[currency]
              }
              if (item[type] && this.purchaseData.orderType === 'Z002' && (prop === 'customsFee' || prop === 'intlShipping')) {
                sum[prop][type] = item[type]
              }
            }
          })
        })
        safeRun(() => { window._console.red('handleResetAssign sum:', sum) })

        Object.keys(sum).forEach(key => {
          const amount = key + 'Amount'
          const currency = key + 'Currency'
          const type = key + 'Type'
          this.purchaseData[amount] = sum[key][amount]
          this.purchaseData[currency] = sum[key][currency]
          if ((key === 'customsFee' || key === 'intlShipping') && this.purchaseData.orderType === 'Z002') {
            this.purchaseData[type] = this.purchaseData[type] || sum[key][type]
          }
        })
        this.$forceUpdate()
      }
    },
    handleOneAssign () {
      try {
        console.log('before assign', this.purchaseData)
        if (this.purchaseData.sundry) {
          if (isEmpty(this.purchaseData.sundryReason)) {
            this.$message.error('杂费原因必填')
            return
          } else if (this.purchaseData.sundryReason.includes('09') && !this.purchaseData.sundryReasonDetail) {
            this.$message.error('杂费原因包含其他时，杂费原因备注必填')
            return
          }
        }
        const loading = this.startLoading()
        assign(this.purchaseData).then((data) => {
          if (data) {
            this.purchaseData = merge(this.purchaseData, data)
            this.purchaseData.itemList = this.purchaseData.itemList.map(item => {
              const updatedShareRebatePOItemList = data.itemList.find(i => i.itemNo === item.itemNo)?.shareRebatePOItemList || []
              return {
                ...item,
                editChange: true,
                shareRebatePOItemList: updatedShareRebatePOItemList
              }
            })
          }
        }).finally(() => {
          this.endLoading(loading)
        })
      } catch (e) {
        this.$message.error(e.message)
      }
    },
    handleChangeSupplier (prop, val) {
      this.$set(this.purchaseData, prop, val)
    },
    updateItemDeliveryDate (row) {
      const index = this.purchaseData.itemList.findIndex(item => item === row)
      if (index > -1) {
        this.$set(this.purchaseData.itemList, index, row)
      }
    },
    handleShowDetail (val, flag) {
      this.showDetail = true
      this.itemDetail = val
    },
    setRowDeliveryDate (row, standardLeadTime, deliveryDate) {
      // const { createTime } = this.purchaseData
      const { planList } = row
      // const date = moment(createTime).add(data, 'days').format('YYYY-MM-DD')
      row.itemDeliveryDate = deliveryDate
      row.standardLeadTime = standardLeadTime
      if (Array.isArray(planList) && planList.length) {
        planList.forEach(plan => {
          plan.deliveryDate = deliveryDate
        })
      }
    },
    async updateTransferTime (factoryCode, warehouseLocation, shipWarehouseLocation, row) {
      const { skuNo } = row
      const params = { factoryCode, warehouseLocation, shipWarehouseLocation, skuNo }
      this.pageLoading = true
      return getTransferTime(params)
        .then(res => {
          const { code, data, msg } = res
          if (code === 0 && data) {
            const { f0: standardLeadTime, f1: deliveryDate } = data
            this.setRowDeliveryDate(row, standardLeadTime, deliveryDate)
          } else {
            submitErrorHandler('', data, msg)
          }
        })
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    updateFactory (factoryCode, warehouseLocation, soNo) {
      if (factoryCode) {
        const { supplierNo } = this.purchaseData.supplier
        const { purchaseGroup } = this.purchaseData
        if (supplierNo) {
          this.queryAddressList(supplierNo, factoryCode, warehouseLocation)
          this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo)
        }
      }
    },
    async getPriceData (factoryCode, supplierId, skuNo, orderType, row, queryType = 0) {
      let { warehouseLocation, soNo, soItemNo, itemQuantity, discountAmount, inventoryUnitDeno = 1, inventoryUnitMole = 1, priceTimes, shippingAmount, taxedPrice, sundryAmount, rebateAmount } = row
      const { receiveProvince, receiveCity, supplier: { supplierNo } } = this.purchaseData
      const body = {
        factoryCode,
        supplierId,
        supplierNo,
        skuNos: [skuNo],
        skuQueryList: [
          { skuNo, warehouseLocation, soNo, soItemNo, itemQuantity, discountAmount, inventoryUnitDeno, inventoryUnitMole, priceTimes, shippingAmount, taxedPrice, sundryAmount, rebateAmount }
        ],
        orderType,
        receiveProvince,
        receiveCity,
        queryType
      }
      this.pageLoading = true
      const response = await getProductPrice(body)
      if (response && response.code === 0 && Array.isArray(response.data)) {
        const { deliveryDays, cloud, taxedPriceTimes = 0, taxedPrice = 0, priceTimes: responsePriceTimes } = response.data[0] || {}
        const { createTime } = this.purchaseData
        if ([2, 0].includes(queryType)) {
          const date = moment(createTime).add(deliveryDays, 'days').format('YYYY-MM-DD')
          row.standardLeadTime = deliveryDays
          row.itemDeliveryDate = date
          row.oldDeliveryDate = date
          row.oldCloud = cloud
          row.cloud = cloud
          if (row.planList && row.planList.length === 1) {
            row.planList[0].deliveryDate = date
          }
        }
        if (this.purchaseData.orderType !== 'Z004' && row.isFree !== 1 && [1, 0].includes(queryType)) {
          row.taxedPrice = taxedPriceTimes
          row.taxedPriceOrigin = taxedPrice
          row.priceTimes = responsePriceTimes
          row.taxedTotalAmount = formatAmount(taxedPrice * row.itemQuantity / responsePriceTimes, 2) || 0
          row.untaxedTotalAmount = formatAmount(taxedPrice / (1 + row.taxRate * 0.01) * row.itemQuantity / responsePriceTimes, 2) || 0
          row.untaxedPrice = formatAmount(row.untaxedTotalAmount / row.itemQuantity * responsePriceTimes, 2) || 0
          await calcUnTaxedPrice(row, this.dictList).then(data => {
            if (data) row = merge(row, rowEditChange(data))
            console.log(row, data);
          })
        }
      }
      this.pageLoading = false
    },
    updateDeliveryDaysByWarehouseLocation (row, queryType = 0) {
      const { factoryCode, skuNo } = row
      const { orderType, supplier: { providerId } } = this.purchaseData
      if (orderType !== 'Z003') {
        this.getPriceData(factoryCode, providerId, skuNo, orderType, row, queryType)
      }
    },
    updateWarehouseLocation (factoryCode, warehouseLocation, soNo) {
      // 1201仓有2个地址，其中一个只有特殊采购组别的时候才展示出来 https://wiki.zkh360.com/confluence/pages/viewpage.action?pageId=*********
      if (this.purchaseData.orderType === 'Z001' && warehouseLocation === '1201') {
        const { supplierNo } = this.purchaseData.supplier || {}
        this.queryAddressList(supplierNo, factoryCode, warehouseLocation)
      }
      this.handleUpdateDefaultAddress(factoryCode, warehouseLocation, soNo)
    },
    handleUpdateDefaultAddress (factoryCode, warehouseLocation, soNo) {
      if (factoryCode) {
        const { supplierNo } = this.purchaseData.supplier
        const { purchaseGroup } = this.purchaseData
        if (supplierNo) {
          this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo)
        }
      }
    },
    async fetchItemListInfo (skuNos, providerId, projectCategory = '', row = {}) {
      const { orderType } = this.purchaseData
      const factoryCode = row.factoryCode || this.factoryCode
      let { itemQuantity } = row
      const { receiveProvince, receiveCity, supplier: { supplierNo } } = this.purchaseData
      const productList = await getProduct({
        factoryCode,
        skuNos,
        supplierNo
      })
      if (!productList) return []
      let priceList = []
      if (orderType !== 'Z003') {
        const body = {
          factoryCode,
          supplierId: providerId,
          skuNos,
          supplierNo,
          skuQueryList: skuNos.map(sku => {
            const skuInfoList = skuNos.skuInfoList
            const loc0 = (skuInfoList && skuInfoList.find(item => item.skuNo === sku)) || {}
            const loc1 = (productList && productList.find(item => item.skuNo === sku)) || {}
            itemQuantity = itemQuantity || loc0.quantity || 0
            return {
              skuNo: sku,
              warehouseLocation: loc0.warehouseLocation || loc1.warehouseLocationCode,
              itemQuantity
            }
          }),
          orderType,
          receiveProvince,
          receiveCity,
          queryType: 0
        }
        if (orderType === 'Z004') {
          body.projectCategory = projectCategory
        }
        const response = await getProductPrice(body)
        if (response.code === 0 && response.data) {
          priceList = response.data
        }
      }
      return [productList, priceList]
    },
    async addDom (row, priceData) {
      let { inventoryUnitMole = 1, inventoryUnitDeno = 1 } = priceData || {}
      let { factoryCode, skuNo, itemQuantity } = row
      if (!inventoryUnitMole) inventoryUnitMole = 1
      if (!inventoryUnitDeno) inventoryUnitDeno = 1
      const newComponentList = []
      // 都请求bom
      if (factoryCode && skuNo) {
        const { factoryCode, skuNo, warehouseLocation: rowWarehouseLocation } = row
        let { lossRatio } = this.purchaseData
        const bomData = await getBom({
          factoryCode,
          skuNo
        })
        if (bomData && bomData.bomItemList && bomData.bomItemList.length > 0) {
          const categoryList = {}
          bomData.bomItemList.forEach(bom => {
            if (bom && bom.warehouseLocation) {
              const { skuNo, warehouseLocation } = bom
              if (!categoryList[warehouseLocation]) {
                categoryList[warehouseLocation] = []
              }
              categoryList[warehouseLocation].push(skuNo)
            }
          })
          let inventoryList = []
          if (Object.keys(categoryList).length > 0) {
            inventoryList = await Promise.all(Object.keys(categoryList).map(async (warehouseLocation) => {
              const skuNos = categoryList[warehouseLocation].join(',')
              if (skuNos) {
                return getInventory({
                  factoryCode,
                  skuNos,
                  warehouseLocation
                })
              }
              return Promise.resolve([])
            }))
          }
          bomData.bomItemList.forEach(bom => {
            const { skuNo, materialDescribe, materialGroupNum, warehouseLocation, quantity, uomIdName } = bom
            let componentRequiredQuantity = quantity
            // 组件数量计算
            if (!lossRatio) lossRatio = 0
            if (!itemQuantity) itemQuantity = 0
            componentRequiredQuantity = Number((itemQuantity * inventoryUnitMole / inventoryUnitDeno * (quantity / bomData.quantity) * (1 + lossRatio * 0.01)).toFixed(3))
            if (materialGroupNum === 430) {
              componentRequiredQuantity = Math.ceil(componentRequiredQuantity)
            }
            const configWarehouse = getDefaultWarehouseLocation(this.defaultWarehouseConfigList, this.purchaseData.purchaseGroup, factoryCode)
            const component = {
              materialGroupNum,
              bomQuantity: bomData.quantity,
              _quantity: materialGroupNum === 430 ? Math.ceil(quantity) : quantity,
              componentMaterialDescription: materialDescribe,
              componentSkuNo: skuNo,
              componentRequiredQuantity,
              componentInventoryUnit: uomIdName,
              componentInventoryUnitName: uomIdName,
              componentWarehouseLocation: configWarehouse,
              _configWarehouse: configWarehouse
            }
            if (configWarehouse) {
              component._defaultConfigWarehouse = true
            }
            if (!component.componentWarehouseLocation) {
              component.componentWarehouseLocation = rowWarehouseLocation
            }
            component.componentWarehouseLocation = getD02ComponentWarehouseLocation(this.purchaseData.orderType, this.purchaseData.purchaseGroup, this.KHWarehouseList, rowWarehouseLocation, component.componentWarehouseLocation, this.dictList['componentWarehouse'])
            if (bom && bom.warehouseLocation) {
              const inv = inventoryList.find(inventory => inventory.sku === skuNo && inventory.position === warehouseLocation)
              if (inv && inv.availableQty) {
                component.availableQty = inv.availableQty
              }
            }
            newComponentList.push(component)
          })
        }
      }
      return newComponentList
    },
    addCateRetQuantity () {
      const { orderType } = this.purchaseData
      let projectCategory = ''
      let isReturn = 0
      let receivedQuantity = 0
      if (orderType === 'Z004') {
        isReturn = 1
      }
      if (orderType === 'Z003') {
        projectCategory = 'U'
      }
      if (orderType === 'Z004') {
        projectCategory = ''
      }
      if (orderType === 'Z005') {
        projectCategory = 'K'
      }
      if (orderType === 'Z006') {
        projectCategory = 'L'
      }
      if (orderType === 'Z010') {
        projectCategory = 'S'
      }
      return {
        projectCategory,
        isReturn,
        receivedQuantity
      }
    },
    findUnitName (value) {
      const unitList = this.dictList['orderUnit']
      if (Array.isArray(unitList) && unitList.length) {
        const item = unitList.find(item => item.value === value)
        if (item && item.name) {
          value = item.name
        }
      }
      return value
    },
    setRowMPQTip(row, itemQuantity) {
      const { itemNo, bstrf } = row
      // 商品行**存在MPQ(*)，订单数量已更新为建议数量：*
      setTimeout(() => {
        this.$message.info(`商品行${itemNo}存在MPQ(${bstrf})，订单数量已更新为建议数量：${itemQuantity}`)
      })
    },
    setRowMPQ (rowItem) {
      // 没有计算过MPQ且有商品数量、SKU的MPQ数量大于0，则修改商品数量
      if (!rowItem.calculatedMPQ && rowItem.itemQuantity && rowItem.bstrf > 0) {
        const calcRes = Math.ceil(rowItem.itemQuantity / rowItem.bstrf) * rowItem.bstrf
        if (rowItem.itemQuantity !== calcRes) {
          this.setRowMPQTip(rowItem, calcRes)
        }
        rowItem.itemQuantity = calcRes
        rowItem.calculatedMPQ = true
      }
      return rowItem.itemQuantity
    },
    addItem (sku, row) {
      const { supplier: { providerId, supplierNo }, createTime, purchaseGroup, itemList, orderType, isUrgent } = this.purchaseData
      const oldProjectCategory = row?.projectCategory
      if (sku && providerId) {
        const loading = this.startLoading()
        this.fetchItemListInfo([sku], providerId, oldProjectCategory, row).then(async ([productList, priceList = []]) => {
          loading.close()
          // 去除没有价格信息不让添加新行的卡控
          if (productList && productList.length > 0) {
            const productData = productList[0]
            const priceData = priceList[0] || { noMaintainPrice: true }

            if (orderType === 'Z004' && (!priceList.length || !priceData.taxedPrice)) {
              this.$message.warning(`商品[${sku}]未查询到该供应渠道有效采购价，请核实！`)
            }
            if (!['Z003', 'Z004'].includes(orderType)) {
              if (productData?.chemicalQualification === 0 && productData?.hazmat === 1) {
                this.$message.error(`该供应商${this.purchaseData?.supplier?.supplierNo}的SKU：${productData?.skuNo}危化品经营许可证已失效，不能下单`)
                row.skuNo = ''
                return
              }
              if (priceData?.ifBrandQualification === 0) {
                this.$message.error(`该供应商${this.purchaseData?.supplier?.supplierNo}的SKU：${productData?.skuNo}的${productData?.brandName}品牌资质已失效，不能下单`)
                row.skuNo = ''
                return
              }
            }
            delete row.isEmptyLine
            const idx = itemList.findIndex(item => item.uuid === row.uuid)
            const len = itemList.length
            if (supplierNo && idx === 0) {
              if (productData.warehouseLocationCode === '1201' && orderType === 'Z001') {
                this.queryAddressList(supplierNo, this.factoryCode, '1201')
              }
              await this.queryDefaultAddress(supplierNo, purchaseGroup, this.factoryCode, productData.warehouseLocationCode, productData.soNo)
            }
            // Z006,需要额外获取委外信息
            let componentList
            if (orderType === 'Z006') {
              console.log('await this.addDom(row)')
              row.warehouseLocation = productData.warehouseLocationCode
              componentList = await this.addDom(row, priceData)
            }
            const { deliveryDays = 0, deliveryDate, taxedPrice = 0, taxedPriceTimes = 0, discountAmount, priceTimes = 1, unitCode } = priceData
            if (discountAmount) {
              delete priceData.discountAmount
            }
            const { materialGroup, materialGroupNum, materialGroupId, warehouseLocationCode = '', bstrf } = productData
            console.log('bstrf:', bstrf)
            if (idx > -1) {
              const date = priceData ? deliveryDate : moment(createTime).add(deliveryDays, 'days').format('YYYY-MM-DD')
              let { projectCategory, isReturn, receivedQuantity } = this.addCateRetQuantity()
              if (oldProjectCategory) projectCategory = oldProjectCategory
              const { factoryCode } = itemList[idx]
              let finalWarehouseLocation = warehouseLocationCode
              if (this.isFixedAssets(orderType, projectCategory)) {
                finalWarehouseLocation = null
              }
              let params = {
                oaItemNo: '',
                oaNo: '',
                oaType: '',
                soNo: '',
                soItemNo: '',
                supplierMaterialNo: '',
                trackNo: '',
                bstrf, /* SKU 对应MPQ */
                ...itemList[idx],
                ...productData,
                ...priceData,
                oldCloud: priceData.cloud,
                oldDeliveryDate: date,
                factoryCode,
                priceTimes,
                taxedPriceOrigin: taxedPrice,
                taxedPrice: taxedPriceTimes,
                // untaxedPrice: Number((taxedPriceTimes / (1 + taxRate * 0.01)).toFixed(2)),
                untaxedPrice: '',
                standardLeadTime: deliveryDays,
                materialGroupName: materialGroup,
                materialGroupNum: materialGroupNum,
                materialGroupId: materialGroupId,
                warehouseLocation: finalWarehouseLocation,
                itemDeliveryDate: date,
                inventoryUnit: productData.unitCode,
                unit: unitCode,
                unitName: this.findUnitName(unitCode),
                planList: [{
                  deliveryQuantity: itemList[idx].itemQuantity || 0,
                  deliveryDate: date,
                  planNo: '0001'
                }],
                projectCategory,
                isReturn,
                receivedQuantity
              }
              await calcUnTaxedPrice(params, this.dictList).then(data => {
                if (data) params = merge(params, rowEditChange(data))
              })
              if (priceList[0]) {
                params.noMaintainPrice = false
              }
              if (['Z007', 'Z004'].includes(orderType)) {
                if (!params.fixedAssetsList || params.fixedAssetsList.length === 0) {
                  params.fixedAssetsList = [{
                    fixedAssetsNo: null
                  }]
                }
              }
              if (componentList && componentList.length > 0) {
                params.componentList = componentList
              }
              // if (orderType === 'Z010') {
              //   delete params.warehouseLocation
              // }
              if (orderType === 'Z004') {
                params.trackNo = null
                params.batchNo = null
                params.refundableAmount = null
              }
              // if (orderType === 'Z010') {
              //   delete params.warehouseLocation
              // }
              if (orderType === 'Z003') {
                params.shipWarehouseLocation = warehouseLocationCode
                // 对于Z003订单，单位去主数据sku接口 unitCode
                params.unit = productData.unitCode
              }
              if (params.taxedTotalAmount === undefined) {
                params.taxedTotalAmount = ''
              }
              if (params.untaxedTotalAmount === undefined) {
                params.untaxedTotalAmount = ''
              }
              if (params.taxTotalAmount === undefined) {
                params.taxTotalAmount = ''
              }
              if (isUrgent === 1) {
                params.isUrgent = 1
              }
              if (orderType === 'Z003') {
                const { factoryCode, warehouseLocation, shipWarehouseLocation } = params
                await this.updateTransferTime(factoryCode, warehouseLocation, shipWarehouseLocation, params)
              }
              if (orderType !== 'Z010') {
                this.setRowMPQ(params)
              }
              this.$set(this.purchaseData.itemList, idx, params)
              if (len === idx + 1) {
                this.addItemEmptyLine()
              }
            }
          }
        })
      } else if (this.isFixedAssets(orderType, oldProjectCategory)) {
        const idx = itemList.findIndex(item => item === row)
        const len = itemList.length
        delete row.isEmptyLine
        // this.$set(this.purchaseData.itemList, idx, row)
        if (len === idx + 1) {
          this.addItemEmptyLine()
        }
      }
    },
    /**
     * return first line index which was valid for updating default address
     */
    getFirstLineUpdateDefaultAddress () {
      const { itemList } = this.purchaseData
      let retIndex = null
      for (let index = 0; index < itemList.length; index++) {
        const item = itemList[index]
        if (item && !item.isEmptyLine && !item.isDeleted) {
          retIndex = index; break;
        }
      }
      return retIndex
    },
    resetDefaultAddress (idx = 0) {
      console.log('查询通讯信息默认值')
      let { supplierNo } = this.purchaseData
      let { supplier } = this.purchaseData
      if (!supplierNo) supplierNo = supplier.supplierNo
      const { purchaseGroup } = this.purchaseData
      const { factoryCode, warehouseLocation, soNo } = this.purchaseData.itemList[idx];
      this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo)
    },
    addItemList (skuInfoList) {
      const total = skuInfoList.length
      if (!this.purchaseData.supplier) return this.$message.error('请先选择供应商！')
      const { supplier: { providerId }, orderType, isUrgent } = this.purchaseData
      if (total && providerId) {
        const skuNos = uniq(skuInfoList.map(item => item.skuNo))
        const loading = this.startLoading()
        skuNos.skuInfoList = skuInfoList
        this.fetchItemListInfo(skuNos, providerId).then(async ([productList, priceList]) => {
          if (!productList) return
          const skuInfo = {}
          const priceInfo = {}
          if (productList && productList.length > 0) {
            productList.forEach(product => {
              if (!skuInfo[product.skuNo]) {
                skuInfo[product.skuNo] = product
              }
            })
            priceList.forEach(price => {
              if (!priceInfo[price.skuNo]) {
                priceInfo[price.skuNo] = price
              }
            })
          }
          this.purchaseData.itemList.pop()
          let needQueryAddress = false
          if (!this.purchaseData.itemList.length) {
            needQueryAddress = true
          }
          const filterList = skuInfoList.filter(filterInProp(productList, 'skuNo'))
          for (let index = 0; index < filterList.length; index++) {
            let item = filterList[index]
            let {
              skuNo, quantity, warehouseLocation, shipWarehouseLocation, returnReason, trackNo, fixedAssetsCardNo, generalLedgerAccount, costCenter
            } = item
            if (skuNo) {
              let itemData = {
                ...skuInfo[skuNo],
                itemNo: this.getItemNo()
              }
              if (quantity) {
                itemData.itemQuantity = quantity
              }
              console.log('addItemList before: ', itemData.itemQuantity)
              if (orderType !== 'Z010') {
                this.setRowMPQ(itemData)
                quantity = itemData.itemQuantity
              }
              console.log('addItemList after: ', itemData.itemQuantity)
              if (item.inSystemMaterial) {
                itemData.inSystemMaterial = item.inSystemMaterial
              }
              if (fixedAssetsCardNo) {
                itemData.fixedAssetsCardNo = fixedAssetsCardNo
              }
              if (generalLedgerAccount && this.inGeneralLedger(generalLedgerAccount)) {
                itemData.generalLedgerAccount = generalLedgerAccount
              }
              if (costCenter && this.inCostCenter(costCenter)) {
                itemData.costCenter = costCenter
              }
              if (shipWarehouseLocation && this.inWarehouse(shipWarehouseLocation)) {
                itemData.shipWarehouseLocation = shipWarehouseLocation
              }
              if (returnReason) {
                itemData.returnReason = returnReason
              }
              if (trackNo) {
                itemData.trackNo = trackNo
              }
              if (skuInfo[skuNo]) {
                const { materialGroup, warehouseLocationCode = '' } = skuInfo[skuNo]
                let finalWarehouseLocation = warehouseLocation || warehouseLocationCode
                if (!this.inWarehouse(finalWarehouseLocation)) {
                  finalWarehouseLocation = ''
                }
                const { createTime } = this.purchaseData
                const { unitCode, deliveryDate } = priceInfo[skuNo] || {}
                const date = priceInfo[skuNo] ? deliveryDate : moment(createTime).format('YYYY-MM-DD')
                const { projectCategory, isReturn, receivedQuantity } = this.addCateRetQuantity()
                itemData = {
                  oaItemNo: '',
                  oaNo: '',
                  oaType: '',
                  soNo: '',
                  soItemNo: '',
                  supplierMaterialNo: '',
                  trackNo: '',
                  uuid: shortid.generate(),
                  ...itemData,
                  ...skuInfo[skuNo],
                  ...priceInfo[skuNo],
                  unit: unitCode,
                  inventoryUnit: skuInfo[skuNo].unitCode,
                  unitName: this.findUnitName(unitCode),
                  itemNo: this.getItemNo(),
                  warehouseLocation: finalWarehouseLocation,
                  taxedPriceOrigin: '',
                  untaxedPrice: '',
                  taxedTotalAmount: '',
                  untaxedTotalAmount: '',
                  standardLeadTime: '',
                  noMaintainPrice: true,
                  materialGroupName: materialGroup,
                  itemDeliveryDate: date,
                  factoryCode: this.factoryCode,
                  planList: [{
                    deliveryQuantity: quantity || 0,
                    planNo: '0001',
                    deliveryDate: date
                  }],
                  projectCategory,
                  isReturn,
                  receivedQuantity
                }
                let componentList
                if (orderType === 'Z006') {
                  console.log('addItemList this.addDom(row)')
                  // itemData.warehouseLocation = priceInfo[skuNo].warehouseLocationCode
                  componentList = await this.addDom(itemData, priceInfo[skuNo])
                }
                if (componentList && componentList.length) {
                  itemData.componentList = componentList
                }
                if (['Z007', 'Z004'].includes(orderType)) {
                  if (!itemData.fixedAssetsList || itemData.fixedAssetsList.length === 0) {
                    itemData.fixedAssetsList = [{
                      fixedAssetsNo: null,
                      fixedAssetsCardQuantity: itemData.itemQuantity,
                      fixedAssetsCardNo: itemData.fixedAssetsCardNo
                    }]
                  }
                }
                if (priceInfo[skuNo]) {
                  const { deliveryDays = 0, taxedPrice = 0, taxedPriceTimes = 0, taxRate = 0, discountAmount } = priceInfo[skuNo]
                  if (discountAmount) {
                    delete priceInfo[skuNo].discountAmount
                  }
                  const date = moment(createTime).add(deliveryDays, 'days').format('YYYY-MM-DD')
                  itemData.taxedPrice = taxedPriceTimes
                  itemData.taxedPriceOrigin = taxedPrice
                  // itemData.untaxedPrice = Number((taxedPriceTimes / (1 + taxRate * 0.01)).toFixed(2))
                  itemData.taxedTotalAmount = Number((taxedPrice * (quantity || 0)).toFixed(2))
                  itemData.untaxedTotalAmount = Number(((quantity || 0) * taxedPrice / (1 + taxRate * 0.01)).toFixed(2))
                  itemData.taxTotalAmount = itemData.taxedTotalAmount - itemData.untaxedTotalAmount
                  itemData.standardLeadTime = deliveryDays
                  itemData.itemDeliveryDate = date
                  itemData.planList[0].deliveryDate = date
                  await calcUnTaxedPrice(itemData, this.dictList).then(data => {
                    if (data) itemData = merge(itemData, rowEditChange(data))
                  })
                  delete itemData.noMaintainPrice
                }
              }
              // if (isOverchargeFree === 1) {
              //   itemData.isFree = 1
              //   itemData.taxedPrice = 0
              //   itemData.untaxedPrice = ''
              //   itemData.taxedTotalAmount = ''
              //   itemData.untaxedTotalAmount = ''
              //   itemData.taxTotalAmount = ''
              // }
              if (isUrgent === 1) {
                itemData.isUrgent = 1
              }
              if (orderType === 'Z003') {
                // 对于Z003订单，单位去主数据sku接口 unitCode
                itemData.unit = skuInfo[skuNo].unitCode
                const { factoryCode, warehouseLocation, shipWarehouseLocation } = itemData
                await this.updateTransferTime(factoryCode, warehouseLocation, shipWarehouseLocation, itemData)
              }
              this.purchaseData.itemList.push(itemData)
              if (index === 0 && itemData.warehouseLocation === '1201' && this.purchaseData.orderType === 'Z001') {
                this.queryAddressList(this.purchaseData.supplierNo, this.factoryCode, '1201')
              }
              if (needQueryAddress && (index === filterList.length - 1)) {
                const idx = this.getFirstLineUpdateDefaultAddress()
                console.log(idx)
                if (idx !== null) {
                  this.resetDefaultAddress(idx)
                }
              }
            }
          }
          this.addItemEmptyLine()
        }).finally(() => {
          loading.close()
        })
      }
    },
    handleImport (uploadSkuInfoVOList) {
      this.showBatchImport = false
      this.addItemList(uploadSkuInfoVOList)
    },
    handlePunchImport (uploadSkuInfoVOList) {
      this.showPunchImport = false
      console.log(uploadSkuInfoVOList)
      this.addFestoItemList(uploadSkuInfoVOList)
    },
    addFestoItemList (skuInfoList) {
      this.submitFestoList = skuInfoList
      const total = skuInfoList.length
      // festo 导入需要覆盖PMS订单创建的字段
      const coverFields = skuInfoList.map(item => ({
        punchId: item.id,
        quantity: item.supplierOrderAmount,
        itemDeliveryDate: item.leadDate,
        // taxedPrice: item.zkhTaxPrice,
        // taxRate: item.zkhTaxRate,
        deliveryDays: item.leadTime
        // unitName: item.zkhUnit
      }))
      if (!this.purchaseData.supplier) return this.$message.error('请先选择供应商！')
      const { supplier: { providerId }, orderType, isUrgent } = this.purchaseData
      if (total && providerId) {
        const skuNos = uniq(skuInfoList.map(item => item.skuNo))
        const loading = this.startLoading()
        this.fetchItemListInfo(skuNos, providerId).then(async ([productList, priceList]) => {
          if (!productList) return
          const skuInfo = {}
          const priceInfo = {}
          if (productList && productList.length > 0) {
            productList.forEach(product => {
              if (!skuInfo[product.skuNo]) {
                skuInfo[product.skuNo] = product
              }
            })
            priceList.forEach(price => {
              if (!priceInfo[price.skuNo]) {
                priceInfo[price.skuNo] = price
              }
            })
          }
          this.purchaseData.itemList.pop()
          let needQueryAddress = false
          if (!this.purchaseData.itemList.length) {
            needQueryAddress = true
          }
          const filterList = skuInfoList.filter(filterInProp(productList, 'skuNo'))
          console.log(coverFields, skuInfo, priceInfo, filterList)
          for (let index = 0; index < filterList.length; index++) {
            let item = filterList[index]
            let coverItem = coverFields[index]
            let {
              skuNo, warehouseLocation, shipWarehouseLocation, returnReason, trackNo, fixedAssetsCardNo, generalLedgerAccount, costCenter
            } = item
            let { quantity } = coverItem
            if (skuNo) {
              let itemData = {
                ...skuInfo[skuNo],
                itemNo: this.getItemNo()
              }
              if (quantity) {
                itemData.itemQuantity = quantity
              }
              console.log('addItemList before: ', itemData.itemQuantity)
              if (orderType !== 'Z010') {
                this.setRowMPQ(itemData)
                quantity = itemData.itemQuantity
              }
              console.log('addItemList after: ', itemData.itemQuantity)
              if (item.inSystemMaterial) {
                itemData.inSystemMaterial = item.inSystemMaterial
              }
              if (fixedAssetsCardNo) {
                itemData.fixedAssetsCardNo = fixedAssetsCardNo
              }
              if (generalLedgerAccount && this.inGeneralLedger(generalLedgerAccount)) {
                itemData.generalLedgerAccount = generalLedgerAccount
              }
              if (costCenter && this.inCostCenter(costCenter)) {
                itemData.costCenter = costCenter
              }
              if (shipWarehouseLocation && this.inWarehouse(shipWarehouseLocation)) {
                itemData.shipWarehouseLocation = shipWarehouseLocation
              }
              if (returnReason) {
                itemData.returnReason = returnReason
              }
              if (trackNo) {
                itemData.trackNo = trackNo
              }
              if (skuInfo[skuNo]) {
                const { materialGroup, warehouseLocationCode = '' } = skuInfo[skuNo]
                let { itemDeliveryDate } = coverItem
                let finalWarehouseLocation = warehouseLocation || warehouseLocationCode
                if (!this.inWarehouse(finalWarehouseLocation)) {
                  finalWarehouseLocation = ''
                }
                const { createTime } = this.purchaseData
                const deliveryDate = itemDeliveryDate || moment(createTime).format('YYYY-MM-DD')
                const { unitCode } = priceInfo[skuNo] || {}
                const { projectCategory, isReturn, receivedQuantity } = this.addCateRetQuantity()
                itemData = {
                  oaItemNo: '',
                  oaNo: '',
                  oaType: '',
                  soNo: '',
                  soItemNo: '',
                  supplierMaterialNo: '',
                  trackNo: '',
                  uuid: shortid.generate(),
                  ...itemData,
                  ...skuInfo[skuNo],
                  ...priceInfo[skuNo],
                  unit: unitCode,
                  inventoryUnit: skuInfo[skuNo].unitCode,
                  unitName: this.findUnitName(unitCode),
                  itemNo: this.getItemNo(),
                  warehouseLocation: finalWarehouseLocation,
                  taxedPriceOrigin: '',
                  untaxedPrice: '',
                  taxedTotalAmount: '',
                  untaxedTotalAmount: '',
                  standardLeadTime: '',
                  noMaintainPrice: true,
                  materialGroupName: materialGroup,
                  itemDeliveryDate: deliveryDate,
                  factoryCode: this.factoryCode,
                  planList: [{
                    deliveryQuantity: quantity || 0,
                    planNo: '0001',
                    deliveryDate
                  }],
                  projectCategory,
                  isReturn,
                  receivedQuantity,
                  ...coverItem // festo属性覆盖
                }
                let componentList
                if (orderType === 'Z006') {
                  console.log('addItemList this.addDom(row)')
                  // itemData.warehouseLocation = priceInfo[skuNo].warehouseLocationCode
                  componentList = await this.addDom(itemData, priceInfo[skuNo])
                }
                if (componentList && componentList.length) {
                  itemData.componentList = componentList
                }
                if (orderType === 'Z007' || ['Z007', 'Z004'].includes(orderType)) {
                  if (!itemData.fixedAssetsList || itemData.fixedAssetsList.length === 0) {
                    itemData.fixedAssetsList = [{
                      fixedAssetsNo: null,
                      fixedAssetsCardQuantity: itemData.itemQuantity,
                      fixedAssetsCardNo: itemData.fixedAssetsCardNo
                    }]
                  }
                }
                if (priceInfo[skuNo]) {
                  const { discountAmount, taxRate = 0, taxedPrice = 0, taxedPriceTimes = 0 } = priceInfo[skuNo]
                  const deliveryDays = coverItem.deliveryDays || (priceInfo[skuNo].deliveryDays || 0)
                  if (discountAmount) {
                    delete priceInfo[skuNo].discountAmount
                  }
                  // const date = moment(createTime).add(deliveryDays, 'days').format('YYYY-MM-DD')
                  itemData.taxedPrice = taxedPriceTimes
                  itemData.taxedPriceOrigin = taxedPrice
                  // itemData.untaxedPrice = Number((taxedPriceTimes / (1 + taxRate * 0.01)).toFixed(2))
                  itemData.taxedTotalAmount = Number((taxedPrice * (quantity || 0)).toFixed(2))
                  itemData.untaxedTotalAmount = Number(((quantity || 0) * taxedPrice / (1 + taxRate * 0.01)).toFixed(2))
                  itemData.taxTotalAmount = itemData.taxedTotalAmount - itemData.untaxedTotalAmount
                  itemData.standardLeadTime = deliveryDays
                  // itemData.itemDeliveryDate = date
                  // itemData.planList[0].deliveryDate = date

                  await calcUnTaxedPrice(itemData, this.dictList).then(data => {
                    if (data) itemData = merge(itemData, rowEditChange(data))
                  })
                  delete itemData.noMaintainPrice
                }
              }
              if (isUrgent === 1) {
                itemData.isUrgent = 1
              }
              if (orderType === 'Z003') {
                // 对于Z003订单，单位去主数据sku接口 unitCode
                itemData.unit = skuInfo[skuNo].unitCode
                const { factoryCode, warehouseLocation, shipWarehouseLocation } = itemData
                await this.updateTransferTime(factoryCode, warehouseLocation, shipWarehouseLocation, itemData)
              }
              this.purchaseData.itemList.push(itemData)
              if (needQueryAddress && (index === filterList.length - 1)) {
                const idx = this.getFirstLineUpdateDefaultAddress()
                if (idx !== null) {
                  this.resetDefaultAddress(idx)
                }
              }
            }
          }
          this.addItemEmptyLine()
        }).finally(() => {
          loading.close()
        })
      }
    },
    addItemEmptyLine () {
      this.purchaseData.itemList.push({
        itemQuantity: 0,
        itemNo: this.getItemNo(),
        factoryCode: this.factoryCode,
        uuid: shortid.generate(),
        isEmptyLine: true,
        materialDescription: '',
        oaItemNo: '',
        oaNo: '',
        oaType: '',
        soNo: '',
        soItemNo: '',
        supplierMaterialNo: '',
        trackNo: '',
        unit: '',
        itemDeliveryDate: '',
        taxedPrice: '',
        priceTimes: '',
        generalLedgerAccount: '',
        costCenter: '',
        inputTax: '',
        materialGroup: ''
      })
    },
    getItemNo () {
      const { itemList } = this.purchaseData
      const len = itemList.length
      if (len > 0) {
        const { itemNo } = itemList[len - 1]
        if (itemNo) {
          const numItemNo = Number(itemNo) + 10
          return padStart(numItemNo, 5, '0')
        }
      }
      return '00010'
    },
    initAddress () {
      this.purchaseData.receiptAddress = this.purchaseData.receiptAddressDetail
      if (this.purchaseData.itemList.length > 0) {
        console.log('edit run this.queryDefaultAddress')
        const { supplierNo } = this.purchaseData.supplier || {}
        const { purchaseGroup } = this.purchaseData
        const { factoryCode, warehouseLocation, soNo } = this.purchaseData.itemList[0]
        if (factoryCode) {
          this.queryAddressList(supplierNo, factoryCode, warehouseLocation)
          this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo)
        }
      }
    },
    handleUpdateMaterialDescription (row) {
      let { projectCategory, receivedQuantity } = this.addCateRetQuantity()
      const { itemList } = this.purchaseData
      row.projectCategory = projectCategory
      row.receivedQuantity = receivedQuantity
      const len = itemList.length
      if (itemList[0] === row) {
        // 如果是第一行，调用默认查询通讯信息接口
        this.initAddress()
      }
      if (itemList[len - 1].uuid === row.uuid) {
        this.addItemEmptyLine()
      }
    },
    handleUpdateCustomerService (val, row) {
      const { itemList } = this.purchaseData
      const idx = itemList.findIndex(el => el.uuid === row.uuid)
      if (idx > -1) {
        let { customerServiceName, soNo } = val
        if (soNo) soNo = String(soNo).trim()
        if (val === 'clear') {
          this.$set(itemList, idx, { ...itemList[idx], customerService: '' })
        } else {
          if (customerServiceName) {
            this.$set(itemList, idx, {
              ...itemList[idx],
              customerService: customerServiceName
            })
          }
        }
        if (idx === 0) {
          const { supplierNo } = this.purchaseData.supplier
          const { purchaseGroup } = this.purchaseData
          const { factoryCode, warehouseLocation } = this.purchaseData.itemList[0]
          this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo)
        }
      }
    },
    queryAddressList (supplierNo, factoryCode, warehouseLocation) {
      if (supplierNo && factoryCode) {
        let params = {
          supplierNo,
          factoryCode
        }
        if (warehouseLocation) {
          params = {
            ...params,
            warehouseLocation
          }
        }
        listCommOptions(params).then(data => {
          if (data) {
            const { receipt, receive, supplier } = data
            if (receipt) {
              const { addressOptions = [], contactOptions = [] } = receipt
              this.receiptAddressList = addressOptions
              this.receiptContactList = contactOptions
            }
            if (receive) {
              const { addressOptions = [], contactOptions = [] } = receive
              this.receiveAddressList = addressOptions
              this.receiveContactList = contactOptions
            }
            if (supplier) {
              const { addressOptions = [], contactOptions = [] } = supplier
              this.supplierAddressList = addressOptions
              this.supplierContactList = contactOptions
            }
          }
        })
      }
    },
    async queryDefaultAddress (supplierNo, purchaseGroupCode, factoryCode, warehouseLocation = '', soNo = '') {
      const { orderType, paymentTermCode } = this.purchaseData
      // 无仓库地点
      if (orderType === 'Z007' || orderType === 'Z008' || orderType === 'Z011') {
        warehouseLocation = ''
      }
      this.pageLoading = true
      if (supplierNo && purchaseGroupCode && factoryCode) {
        const data = await getCommDefault({
          supplierNo,
          purchaseGroupCode,
          factoryCode,
          warehouseLocation: warehouseLocation || '',
          soNo: soNo || '',
          paymentTermCode: paymentTermCode || ''
        })
        const { receipt, receive, supplier } = data
        this.resetCommunication('keepSupplier')
        if (receipt) {
          const { address = '', contact = '' } = receipt
          if (address) {
            this.purchaseData.receiptAddress = this.formatAddress(address)
            this.setAddress('receipt', address)
          }
          if (contact) {
            const { name, phone } = contact
            this.purchaseData.receiptContactName = name
            this.purchaseData.receiptContactPhone = phone
          }
        }
        if (receive) {
          const { address = '', contact = '' } = receive
          if (address) {
            this.purchaseData.receiveAddress = this.formatAddress(address)
            this.setAddress('receive', address)
            setTimeout(() => {
              this.purchaseData.itemList.map(item => {
                if (item.skuNo && this.purchaseData.orderType !== 'Z003') {
                  this.updateDeliveryDaysByWarehouseLocation(item, 2)
                }
              })
            }, 100)
          }
          if (contact) {
            const { name, phone } = contact
            this.purchaseData.receiveContactName = name
            this.purchaseData.receiveContactPhone = phone
          }
        }
        if (supplier) {
          const { address = '', contact = '' } = supplier
          if (address && address.detail) {
            if (!this.purchaseData.supplierAddress) {
              this.purchaseData.supplierAddress = this.formatAddress(address)
              this.setAddress('supplier', address)
            }
          }
          if (contact) {
            const { name, phone } = contact
            if (!this.purchaseData.supplierContactName) {
              this.purchaseData.supplierContactName = name
            }
            if (!this.purchaseData.supplierContactPhone) {
              this.purchaseData.supplierContactPhone = phone
            }
          }
        }
      }
      this.pageLoading = false
    },
    setAddress (type, address = {}) {
      const {
        detail = '', city = '', province = '',
        region = '', street = '', postCode = ''
      } = address || {}
      this.purchaseData[`${type}AddressDetail`] = detail
      this.purchaseData[`${type}City`] = city
      this.purchaseData[`${type}Province`] = province
      this.purchaseData[`${type}Region`] = region
      this.purchaseData[`${type}Street`] = street
      this.purchaseData[`${type}AddressPostCode`] = postCode
    },
    resetCommunication (keepSupplier) {
      if (this.keepSupplier !== true && keepSupplier !== 'keepSupplier') {
        this.setAddress('supplier')
        this.purchaseData.supplierAddress = ''
        this.purchaseData.supplierContactName = ''
        this.purchaseData.supplierContactPhone = ''
      }
      this.keepSupplier = null
      this.setAddress('receive')
      this.purchaseData.receiveAddress = ''
      this.purchaseData.receiveContactName = ''
      this.purchaseData.receiveContactPhone = ''
      this.setAddress('receipt')
      this.purchaseData.receiptAddress = ''
      this.purchaseData.receiptContactName = ''
      this.purchaseData.receiptContactPhone = ''
    },
    formatAddress (address) {
      const { provinceText, cityText, regionText, streetText, detail } = address
      return [provinceText, cityText, regionText, streetText, detail].filter(item => !!item).join('')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .checkbox-column .vxe-cell.c--tooltip  {
  padding: 2px 0px;
  margin-left: -8px;
}
.purchase-create {
  @import './styles/card-layout.scss';
  .amount {
    margin-right: 30px;
  }
  .flex-row-wrapper{
    display: flex;
    flex-wrap: wrap;
  }
  .tab{
    position: relative;
    .toggle-button{
      position: absolute;
      top: 25px;
      right: 20px;
    }
  }
  .fixed-create{
    margin-top: 10px;
    position: fixed;
    bottom: 0px;
    z-index: 1000;
    padding: 10px;
    width: 100%;
    border-top: solid 2px #bfb2b23d;
    background-color: white;
    .btn-group{
      display: flex;
      margin-right: 30px;
      justify-content: flex-end;
    }
  }
}
.btn-row {
  margin: 10px 0;
}
.charge-row {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  &-item {
    display: inline-block;
    min-width: 180px;
    &-highlight {
      margin-right: 5px;
      font-weight: bold;
    }
  }
  &-btn {
    margin: 0 3px;
  }
}
</style>
