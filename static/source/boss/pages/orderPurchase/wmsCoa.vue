<template>
<div class="page page-home page-container">
    <Loading v-if="!loaded" absolute />
    <iframe
      ref="iframe"
      :height="height"
      width="100%"
      scrolling="auto"
      @load="onload"
      :src="url"
      frameborder="0"
    >
    </iframe>
  </div>
</template>

<script>
import Loading from '@/components/loading';
export default {
  data () {
    return {
      host: 'https://w-uat.kunhe-sc.com',
      url: '',
      loaded: false,
      height: '900px'
    }
  },
  watch: {
    $route: {
      handler: function (newVal, oldVal) {
        this.loaded = false
      },
      immediate: true
    }
  },
  components: {
    Loading
  },
  created () {
    let page = this.$route.params.type
    let pageMap = {
      all: '/fileManagement/list',
      supply: '/supplyFileManagement/list',
      rf: '/rfFileManagement/list'
    }
    if (process.env.NODE_ENV === 'production') {
      this.host = 'https://w.kunhe-sc.com'
    }
    this.url = `${this.host}/#${pageMap[page]}?user=${window.CUR_DATA.user.name}`
  },
  mounted() {
    setTimeout(() => {
      let tag = document.getElementById('tab-/orderPurchase/wmsCoa/supply')
      if (tag) {
        tag.childNodes[0].data = '出库补充上传COA合格证'
      }
    }, 0)
  },
  methods: {
    onload() {
      this.loaded = true;
    }
  }

}
</script>

<style lang="scss" scoped>
.page-container {
  padding: 0px;
  overflow: hidden;
  position: relative;
}
</style>
