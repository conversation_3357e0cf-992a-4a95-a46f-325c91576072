<template>
  <div class="list-container">
    <el-form ref="ruleForm" :model="searchForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="公司代码" prop="companyCode">
            <el-select
              v-model="searchForm.companyCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="item in companyFactoryList"
                :key="item.companyCode"
                :label="item.companyCode+' '+item.companyName"
                :value="item.companyCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购姓名" prop="purchaseGroup">
            <el-select
              v-model="searchForm.purchaseGroup"
              filterable
              default-first-option
              clearable
              style="width:100%"
              placeholder="选择采购员"
            >
              <el-option
                v-for="item in purchaseList"
                :key="item.groupCode"
                :label="item.groupCode+' '+item.userName"
                :value="item.groupCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商" prop="supplierNo">
            <SelectSupplier
              clearable
              :data.sync="searchForm.supplier"
              @change="handleChange('supplier', $event)"
              />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="采购单号" prop="poNoList">
            <el-input
              v-model="searchForm.poNoList"
              placeholder="BOSS或SAP单号，最多20个采购订单同时查询，用空格或换行隔开"
              filterable
              clearable
              @input="changePoNo"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="创建日期" prop="createTime">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.createTime"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              clearable
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="2">
          <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="handleSearch">
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      ref="aggreementGrid"
      height="710"
      id="aggreement_grid"
      row-id="poNo"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="listData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
      :checkbox-config="{checkMethod:checkMethod}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
    >
      <template v-slot:toolbar_font>
        列表说明：系统自动匹配 <span :style="{color: 'blue'}"> 完全未清的、类型为直发、销售订单数量=采购订单数量</span> 的销售订单行数据。
        <span :style="{color: 'red'}" v-show="selectList && selectList.length > 1">(批量转单所有商品行删除原因将保持一致)</span>
      </template>
      <template v-slot:toolbar_buttons>
        <el-button type="primary" v-if="getButtonAuth('采购管理', '一键转直发')" :disabled="disabled" @click="() => showDeleteDialog = true">一键转直发</el-button>
      </template>
      <template v-slot:status_default="{ row }">
        <span :style="{color: 'green'}" v-if="row.status === 1">成功</span>
        <span :style="{color: 'red'}" v-else-if="row.status === -1">失败</span>
      </template>
      <template v-slot:sapSoNo_default="{ row }">
        {{  getSapSoNo(row) }}
      </template>
      <template v-slot:sapSoItemNo_default="{ row }">
        {{ getSapSoItemNo(row) }}
        <el-button v-if="row.soItemOptions.length > 1 && row.status !== 1" type="primary" plain size="mini" @click="showMore(row)">更多</el-button>
      </template>
      <template v-slot:soNo_default="{ row }">
        {{  getSoNo(row) }}
      </template>
      <template v-slot:soItemNo_default="{ row }">
        {{ getSoItemNo(row) }}
      </template>
      <template v-slot:soItemQuantity_default="{ row }">
        {{ getSoItemQuantity(row) }}
      </template>
      <template v-slot:supplierName_default="{ row }">
        {{row.supplierNo + ' ' + row.supplierName}}
      </template>
      <template v-slot:purchaseGroup_default="{ row }">
        {{row.purchaseGroup + ' ' + (purchaseList.find(item => item.groupCode === row.purchaseGroup) || {}).userName}}
      </template>
      <template v-slot:customerName_default="{ row }">
        {{ getCustomerName(row) }}
      </template>
      <template v-slot:deliveryManagerName_default="{ row }">
        {{ getDeliveryManagerName(row) }}
      </template>
      <template v-slot:signingBack_default="{ row }">
        {{ getSigningBack(row) }}
      </template>
      <template v-slot:receiveAddress_default="{ row }">
        {{ getReceiveAddress(row) }}
      </template>
    </vxe-grid>

    <el-dialog
      :visible.sync="dialogVisible"
      width="70%"
      >
      <span slot="title" class="dialog-title">
        <p :style="{fontSize: '18px', paddingBottom: '10px'}">销售订单</p>
      以下数据为销售订单中，<span :style="{color: 'blue'}"> 完全未清的、类型为直发</span>的销售订单行数据。若销售订单数量不等于采购订单数量，则不支持选择。
      </span>
      <vxe-grid
      border
      auto-resize
      resizable
      keep-source
      show-overflow="ellipsis"
      id="dialog_grid"
      max-height="400"
      align="center"
      :custom-config="tableCustom"
      :data="dialogData.options"
      :columns="dialogcolumns"
      >
        <template v-slot:radio_default="{ row }">
          <el-radio v-model="dialogData.selectItem.check" :label='row.soNo+row.soItemNo' :name="row.soNo" :disabled='row.soItemQuantity !== itemQuantity'>{{ '' }}</el-radio>
        </template>
        <template v-slot:customerNo_default="{ row }">
          {{ row.customerNo + ' '+ row.customerName }}
        </template>
        <template v-slot:Address_default="{ row }">
          {{ (row.receiveProvinceText || '') + (row.receiveCityText || '') + (row.receiveRegionText || '') + (row.receiveStreetText || '') + (row.receiveAddress || '')}}
        </template>
      </vxe-grid>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="modifySelect">确 认</el-button>
        <el-button type="info" @click="dialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>
    <delete-dialog
      :show-dialog.sync="showDeleteDialog"
      :fileList=[]
      showTip
      @getAttachmentList="toDirect"
      />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { safeRun } from '@/utils/index'
import { convertiblePO, convertPO } from '@/api/mm'
import { getButtonAuth } from '@/utils/auth'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { getUserCompany } from '@/utils/mm'
import DeleteDialog from '@/pages/orderPurchase/components/common/DeleteDialog'
const dialogcolumns = [
  {
    title: '选择',
    slots: {
      default: 'radio_default'
    },
    width: 50
  },
  {
    field: 'sapSoNo',
    title: 'SAP销售订单',
    width: 150
  },
  {
    field: 'sapSoItemNo',
    title: 'SAP销售订单行',
    width: 170
  },
  {
    field: 'soItemQuantity',
    title: '销售订单行数量',
    width: 170
  },
  {
    field: 'customerService',
    title: '客服',
    width: 150
  },
  {
    field: 'customerNo',
    title: '客户',
    slots: {
      default: 'customerNo_default'
    },
    width: 230
  },
  {
    field: 'receiveAddress',
    title: '收货地址',
    slots: {
      default: 'Address_default'
    },
    width: 230
  },
  {
    field: 'soNo',
    title: 'OMS销售订单',
    width: 180
  },
  {
    field: 'soItemNo',
    title: 'OMS销售订单行',
    width: 160
  }
]
const columns = [
  {
    type: 'checkbox',
    width: 50
  },
  {
    field: 'status',
    title: '结果',
    width: 80,
    slots: {
      default: 'status_default'
    }
  },
  {
    field: 'errorMsg',
    title: '消息',
    width: 120
  },
  {
    field: 'poNo',
    title: '采购订单号',
    width: 120
  },
  {
    field: 'itemNo',
    title: '采购订单行',
    width: 120
  },
  {
    field: 'soItemOptions[0].sapSoNo',
    title: 'SAP销售订单',
    width: 120,
    slots: {
      default: 'sapSoNo_default'
    }
  },
  {
    field: 'soItemOptions[0].sapSoItemNo',
    title: 'SAP销售订单行',
    width: 120,
    slots: {
      default: 'sapSoItemNo_default'
    }
  },
  {
    field: 'soItemOptions[0].soItemQuantity',
    title: '销售订单行数量',
    minWidth: 130,
    slots: {
      default: 'soItemQuantity_default'
    }
  },
  {
    field: 'factoryCode',
    title: '工厂',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    minWidth: 80
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    width: 200
  },
  {
    field: 'itemQuantity',
    title: '订单数量',
    width: 80
  },
  {
    field: 'itemUnit',
    title: '订单单位',
    width: 80
  },
  {
    field: 'supplierName',
    title: '供应商',
    width: 200,
    slots: {
      // 使用插槽模板渲染
      default: 'supplierName_default'
    }
  },
  {
    field: 'taxedPrice',
    title: '含税采购价格',
    width: 150
  },
  {
    field: 'currency',
    title: '币别',
    width: 100
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    width: 130,
    slots: {
      // 使用插槽模板渲染
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'soItemOptions[0].customerName',
    title: '客户',
    width: 80,
    slots: {
      default: 'customerName_default'
    }
  },
  {
    field: 'soItemOptions[0].deliveryManagerName',
    title: '交付主管',
    width: 80,
    slots: {
      default: 'deliveryManagerName_default'
    }
  },
  {
    field: 'soItemOptions[0].signingBack',
    title: '签单返回',
    width: 120,
    slots: {
      default: 'signingBack_default'
    }
  },
  {
    field: 'soItemOptions[0].receiveAddress',
    title: '收货地址',
    width: 120,
    slots: {
      default: 'receiveAddress_default'
    }
  },
  {
    field: 'soItemOptions[0].soNo',
    title: 'OMS销售订单',
    width: 180,
    slots: {
      default: 'soNo_default'
    }
  },
  {
    field: 'soItemOptions[0].soItemNo',
    title: 'OMS销售订单行',
    width: 120,
    slots: {
      default: 'soItemNo_default'
    }
  }
]

export default {
  name: 'standardToDirect',
  components: { SelectSupplier, DeleteDialog },
  data () {
    return {
      disabled: true,
      selectList: [],
      itemQuantity: '',
      dialogVisible: false,
      showDeleteDialog: false,
      dialogcolumns,
      dialogData: {
        options: [],
        selectItem: {
          check: ''
        }
      },
      searchForm: {
        companyCode: '',
        purchaseGroup: '',
        supplier: {},
        supplierNo: '',
        poNoList: '',
        createTime: []
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          tools: 'toolbar_buttons',
          buttons: 'toolbar_font'
        }
      },
      columns,
      listData: [],
      convertList: [],
      rules: {
        companyCode: [
          { required: true, message: '请选择公司', trigger: 'change' }
        ],
        purchaseGroup: [
          { required: true, message: '请选择采购员', trigger: 'change' }
        ]
      }
    }
  },
  created () {
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    if (!Object.keys(this.dictList).length) {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    if (this.purchaseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    this.getUserCompany()
  },
  mounted() {
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {},
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      warehouseList: state => state.orderPurchase.warehouseList,
      purchaseList: state => state.orderPurchase.purchaseList
    }),
    signingBack() {
      return this.dictList['signingBack']
    }

  },
  methods: {
    async getUserCompany() {
      const defaultCompany = await getUserCompany();
      this.searchForm.companyCode = defaultCompany;
    },
    getButtonAuth,
    changePoNo () {
      if (this.searchForm.poNoList) {
        this.rules = {
          companyCode: [
            { required: false }
          ],
          purchaseGroup: [
            { required: false }
          ]
        }
      } else {
        this.rules = {
          companyCode: [
            { required: true, message: '请选择公司', trigger: 'change' }
          ],
          purchaseGroup: [
            { required: true, message: '请选择采购员', trigger: 'change' }
          ]
        }
      }
    },
    handleSearch () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.getOrderList()
        } else {
          return false;
        }
      })
    },
    formatParams(params) {
      let form = { ...params };
      form.poNoList = safeRun(() =>
        form.poNoList
          .split(/\s+|,|，/).filter((e) => e)
      );
      delete form.supplier
      delete form.createTime
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.poNoList.length > 20) {
          ret = false
          this.$message.error('采购订单号不能超过20个！')
        }
      })
      return ret
    },
    async getOrderList () {
      try {
        if (this.searchForm.createTime === null) {
          this.searchForm.createTime = []
        }
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return
        params = {
          ...params,
          type: 1,
          orderCreateDateStart: this.searchForm.createTime[0],
          orderCreateDateEnd: this.searchForm.createTime[1]
        }
        this.tableLoading = true
        this.searchLoading = true
        const res = await convertiblePO(params)
        this.listData = res.filter(item => item.isDeleted === 0)
        this.convertList = res
        this.listData = this.listData.map(item => {
          const selectItem = item.soItemOptions.find(po => po.soItemQuantity === item.itemQuantity) || {}
          if (selectItem) {
            item.selectItem = selectItem
          }
          return item
        })
        this.tableLoading = false
        this.searchLoading = false
        if (this.listData.length === 0) {
          this.$message.info('无符合条件的采购订单')
        }
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
      }
    },
    showMore (row) {
      this.dialogVisible = true
      this.dialogData.options = row.soItemOptions
      this.dialogData.selectItem = {
        ...row.selectItem,
        check: row.selectItem.soNo + row.selectItem.soItemNo
      }
      this.itemQuantity = row.itemQuantity
    },
    modifySelect () {
      let row = this.listData.find(item => item.soItemOptions === this.dialogData.options)
      row.selectItem = this.dialogData.options.find(item => {
        const check = item.soNo + item.soItemNo
        return check === this.dialogData.selectItem.check
      })
      this.dialogVisible = false
    },
    getSapSoNo (row) {
      return row.selectItem.sapSoNo
    },
    getSapSoItemNo (row) {
      return row.selectItem.sapSoItemNo
    },
    getSoNo (row) {
      return row.selectItem.soNo
    },
    getSoItemNo (row) {
      return row.selectItem.soItemNo
    },
    getSoItemQuantity (row) {
      return row.selectItem.soItemQuantity
    },
    getCustomerName (row) {
      return (row.selectItem.customerNo || '') + ' ' + (row.selectItem.customerName || '')
    },
    getDeliveryManagerName (row) {
      return row.selectItem.deliveryManagerName
    },
    getSigningBack (row) {
      return (this.signingBack.find(item =>
        row.selectItem.signingBack === item.value
      ) || {}).name
    },
    getReceiveAddress (row) {
      return (row.selectItem.receiveProvinceText || '') + (row.selectItem.receiveCityText || '') + (row.selectItem.receiveRegionText || '') + (row.selectItem.receiveStreetText || '') + (row.selectItem.receiveAddress || '')
    },
    selectChange ({ checked, records, row }) {
      if (!checked) {
        this.$refs.aggreementGrid.setCheckboxRow(this.selectList.filter(item => item.poNo === row.poNo), checked);
        this.reelected(checked, records, row)
      } else {
        this.reelected(checked, records, row)
        this.$refs.aggreementGrid.setCheckboxRow(this.selectList, checked)
      }
    },
    selectAll ({ checked, records }) {
      if (checked) {
        this.selectList = this.convertList
      } else {
        this.selectList = []
      }
      this.disable()
    },
    reelected (checked, records, row) {
      if (checked) {
        this.selectList.push(...this.convertList.filter(item => item.poNo === row.poNo))
      } else if (!checked && row) {
        this.selectList = this.selectList.filter(item => item.poNo !== row.poNo)
      }
      this.disable()
    },
    disable () {
      if (this.selectList.length > 0) {
        this.disabled = false
      } else {
        this.disabled = true
      }
    },
    async toDirect (deleteForm) {
      const loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      this.selectList = this.selectList.filter(item => item.status !== 1)
      let list = this.selectList.map(value => {
        let item = {
          ...value.selectItem,
          currency: value.currency,
          factoryCode: value.factoryCode,
          inventoryUnitDeno: value.inventoryUnitDeno,
          inventoryUnitMole: value.inventoryUnitMole,
          isDeleted: value.isDeleted,
          itemNo: value.itemNo,
          itemQuantity: value.itemQuantity,
          itemUnit: value.itemUnit,
          materialDescription: value.materialDescription,
          poNo: value.poNo,
          purchaseGroup: value.purchaseGroup,
          skuNo: value.skuNo,
          supplierId: value.supplierId,
          supplierMaterialNo: value.supplierMaterialNo,
          supplierName: value.supplierName,
          supplierNo: value.supplierNo,
          taxedPrice: value.taxedPrice,
          warehouseLocation: value.warehouseLocation,
          deleteReason: deleteForm.deleteReason,
          attachmentList: deleteForm.attachmentList
        }
        return item
      })
      try {
        const data = {
          itemList: list,
          updateUser: '',
          type: 1
        }
        data.updateUser = window.CUR_DATA.user && window.CUR_DATA.user.name
        const res = await convertPO(data)
        if (Array.isArray(res)) {
          this.listData = this.listData.map(value => {
            let idx = this.selectList.findIndex(item => item.poNo === value.poNo && item.itemNo === value.itemNo
            )
            if (idx !== -1) {
              value.status = res[idx].status
              value.errorMsg = res[idx].errorMsg
            }
            return value
          })
          this.selectList = this.selectList.map(value => {
            let idx = this.selectList.findIndex(item => item.poNo === value.poNo && item.itemNo === value.itemNo
            )
            if (idx !== -1) {
              value.status = res[idx].status
              value.errorMsg = res[idx].errorMsg
            }
            return value
          })
          this.$refs.aggreementGrid.setCheckboxRow(this.selectList, false);
        }
        this.selectList = []
        this.disable()
        loading.close()
      } catch (error) {
        loading.close()
        console.log(error);
      }
    },
    checkMethod ({ row }) {
      if (row.status === 1) {
        return false
      } else {
        return true
      }
    },
    handleChange (type, event) {
      if (type === 'supplier') {
        this.searchForm.supplierNo = event.supplierNo
      }
    }
  }
}
</script>

<style lang="scss" scoped>

.list-container {
  margin: 10px 20px;
}
.el-button--mini {
  float: right;
}
.el-dialog__footer {
  position: absolute;
  bottom: 10px;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
