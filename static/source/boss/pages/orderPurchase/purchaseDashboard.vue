<template>
  <div class="purchase-dashboard" v-loading="loading">
    <div class="po-overview overview" v-loading="dashboardLoading.overviewLoading">
      <ItemHeader showDetail="overview" titleName="订单概况" @handleDetail="handleOverviewDetail"></ItemHeader>
      <Situation :finalFields="poOverviewFields" :orderData="poOverviewData"></Situation>
    </div>
    <div class="overview" v-loading="dashboardLoading.toDoLoading">
      <ItemHeader showDetail="todo" titleName="我的待办" @handleDetail="handleShowMore"></ItemHeader>
      <div class="todo-list" :class="{'one-line': !showMore}">
        <div class="todo-item" v-for="tab in tabsList" :key="tab.value">
          <div class="type">
            {{tab.name}}
          </div>
          <div class="type-count" @click="handleClick(tab)">
            {{tab.count}}
          </div>
        </div>
      </div>
    </div>
    <div class="overview" v-loading="dashboardLoading.advancePaymentLoading">
      <ItemHeader showDetail="payment" titleName="预付款情况" @handleDetail="handlePaymentDetail"></ItemHeader>
      <Situation :finalFields="paymentFields" :orderData="paymentData"></Situation>
    </div>
    <div class="overview warning" v-loading="dashboardLoading.warningLoading">
      <ItemHeader showDetail="warning" titleName="跟单预警" @handleDetail="handleWarningDetail" :updateTime="updateTime"></ItemHeader>
      <div class="flex-between">
        <div  class="tables" style="width: 68%">
          <WarningTable :finalFields="redWaringTableField" :tableData="redWarningTable" type="red" :renderTips="renderTips"></WarningTable>
        </div>
        <div  class="tables" style="width: 30%">
          <WarningTable :finalFields="greenWaringTableField" :tableData="greenWarningTable" type="green" :renderTips="renderTips"></WarningTable>
        </div>
      </div>
      <div class="flex-between">
        <div class="tables" style="width: 68%">
          <WarningTable :finalFields="yellowWaringTableField" :tableData="yellowWarningTable" type="yellow" :renderTips="renderTips"></WarningTable>
        </div>
        <div class="warning-charts tables" style="width: 30%">
          <div id="trend-echart" style="width: 90%;height:150%">
          </div>
        </div>
      </div>
      <div class="overview" v-loading="dashboardLoading.overdueLoading">
        <ItemHeader titleName="履约状况" ></ItemHeader>
        <div class="overdue flex-between">
          <div class="overdue-chart">
            <div class="overdue-chart-title">
              逾期原因分布统计
            </div>
            <div id="overdue-echart" style="width: 98%;height:240px;background: #FAFAFA;margin: 8px;"></div>
          </div>
          <div class="overdue-table">
            <div class="overdue-table-title flex-between">
              <div>逾期类型分布统计</div>
              <div class="overdue-type-detail detail" @click="handleOverdueTypeDetail"> 查看详情</div>
            </div>
            <OverdueType :finalFields="OverdueTypeField" :tableData="OverdueTypeTable" @showOverdueTypeDetail="showOverdueTypeDetail" canShowDetail="true"/>
          </div>
        </div>
        <div class="overdue-reason">
          <div class="overdue-table">
            <div class="overdue-table-title flex-between">
              <div>原因分布统计</div>
              <div class="overdue-type-detail detail" @click="handleOverdueReasonTable"> 查看详情</div>
            </div>
            <OverdueType :finalFields="overdueReasonFields" :tableData="overdueReasonTable" />
          </div>
        </div>
      </div>
    </div>
    <DashboardDialog :show-dialog.sync="showDashboardDialog" :titleName="titleName" :dataList="dataList" :detailData="detailData"
      :columns="columns" :percentFiled="percentFiled" :showSum="showSum" :isExport="isExport"/>
  </div>
</template>
<script>
import ItemHeader from '@/pages/orderPurchase/components/purchaseDashboard/ItemHeader.vue'
import Situation from '@/pages/orderPurchase/components/purchaseDashboard/Situation.vue'
import DashboardDialog from '@/pages/orderPurchase/components/purchaseDashboard/DashboardDialog'
import WarningTable from '@/pages/orderPurchase/components/purchaseDashboard/WarningTable'
import OverdueType from '@/pages/orderPurchase/components/purchaseDashboard/OverdueType'
import { tabsLists } from './constants/index'
import { overviewDetailField, overviewDetailPercentField,
poOverviewFields, paymentFields, paymentPercentField, paymentField, redWaringTableField, yellowWaringTableField,
greenWaringTableField, warningDetailField, WaringDetailPercentField, redWarningTableRow1, redWarningTableRow2, yellowTableRow1, yellowTableRow2,
renderTips, greenTableRow1, greenTableRow2, OverdueTypeField, OverdueTypDetailField, overdueReasonFields, overdueReasonPercentField, OverdueReasonDetailField } from '@/pages/orderPurchase/constants/dashboard'
import { poOverview, poOverviewDetail, advancePaymentDetail, statisticsWarning, statisticsWarningDetail, overdueDetail, getPoTodo, getPoPendingOrderTodo } from '@/api/mm'
import { thousands } from '@/pages/orderPurchase/utils/index'
import { buildOptions } from '@/utils/mm'
import { getButtonAuth } from '@/utils/auth'
import { mapState } from 'vuex'
const echarts = window.echarts
export default {
  name: 'purchaseDashboard',
  components: {
    DashboardDialog, ItemHeader, Situation, WarningTable, OverdueType
  },
  data() {
    return {
      dashboardLoading: {
        overviewLoading: true,
        warningLoading: true,
        overdueLoading: true,
        advancePaymentLoading: true,
        toDoLoading: true
      },
      tabsList: getButtonAuth('采购工作台', '未下采购订单') ? tabsLists : tabsLists.slice(1, 10),
      poOverviewData: {},
      showMore: false,
      paymentData: {},
      poTodoList: [],
      updateTime: '',
      poOverviewFields,
      paymentFields,
      redWaringTableField,
      yellowWaringTableField,
      greenWaringTableField,
      OverdueTypeField,
      OverdueTypeTable: [],
      overdueData: {},
      redWarningTable: [],
      statisticsWarning: {},
      yellowWarningTable: [],
      greenWarningTable: [],
      loading: false,
      showDashboardDialog: false,
      titleName: '',
      detailData: {},
      dataList: [],
      columns: [],
      percentFiled: [],
      renderTips,
      showSum: false,
      isExport: false,
      overdueReasonFields,
      overdueReasonTable: []
    };
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {},
      purchaseList: state => state.orderPurchase.purchaseList,
      email: (state) => state.user.email

    })
  },
  async created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (this.purchaseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    this.loading = true
    poOverview().then((poOverviewRes) => {
      this.loading = false
      this.dashboardLoading.overviewLoading = false
      this.poOverviewData = poOverviewRes
    })
    advancePaymentDetail().then((advancePaymentDetailRes) => {
      this.loading = false
      this.dashboardLoading.advancePaymentLoading = false
      this.paymentData = advancePaymentDetailRes
      this.paymentData.submitCountRatio = (advancePaymentDetailRes.submitCountRatio * 100).toFixed(2)
    })
    this.getWaringData()
    overdueDetail().then((overdueDetailRes) => {
      this.loading = false
      this.dashboardLoading.overdueLoading = false
      this.handleOverdue(overdueDetailRes)
    })
    this.getToDOList()
  },
  methods: {
    thousands,
    async getToDOList() {
      const todoListRes = await getPoTodo()
      const pendingRes = await getPoPendingOrderTodo({ purchaseGroup: todoListRes.pgCodes.join(' ') })
      this.loading = false
      this.dashboardLoading.toDoLoading = false
      this.tabsList.map(tab => {
        if (tab.value === '1') {
          tab.count = this.handleThousands(pendingRes)
        }
        todoListRes.detailList.map(item => {
          if (item.type === Number(tab.value)) {
            tab.count = this.handleThousands(item.typeCount)
          }
        })
      })
    },
    handleClick(tab) {
      try {
        this.$closeTag('/orderPurchase/purchaseWorkbench');
      } catch {}
      this.$router.push({
        path: '/orderPurchase/purchaseWorkbench',
        query: {
          tab: tab.value
        }
      })
    },
    // 查看更多待办
    handleShowMore() {
      this.showMore = !this.showMore
    },
    getWaringData() {
      this.dashboardLoading.warningLoading = true
      statisticsWarning().then(res => {
        this.loading = false
        this.updateTime = res.refreshDate.split(' ')[1]
        this.handleStatisticsWarning(res)
        this.dashboardLoading.warningLoading = false
      })
    },
    changeShowDashboardDialog(visible, titleName, isShowSum, isExport) {
      this.showDashboardDialog = visible
      this.titleName = titleName
      this.showSum = isShowSum
      this.isExport = isExport
    },
    handleThousands (data) {
      return this.thousands(data) || 0
    },
    handleCnt(count) {
      return (count / 10000).toFixed(3) + '万'
    },
    // 处理跟单预警三个table
    handleStatisticsWarning(statisticsWarningRes) {
      Object.keys(statisticsWarningRes).map((key) => {
        if (/Ratio/.test(key)) {
          this.statisticsWarning[key] = (statisticsWarningRes[key] * 100).toFixed(2)
        } else {
          this.statisticsWarning[key] = statisticsWarningRes[key]
        }
      })
      this.setChart()
      const initTable = [{
        type: '数量'
      }, {
        type: '占比'
      }]
      this.redWarningTable = initTable
      this.yellowWarningTable = initTable
      this.greenWarningTable = initTable
      Object.keys(this.statisticsWarning).map(key => {
        if (redWarningTableRow1.includes(key)) {
          this.redWarningTable[0][key] = this.handleThousands(this.statisticsWarning[key])
        } else if (redWarningTableRow2.includes(key)) {
          let index = redWarningTableRow2.indexOf(key)
          this.redWarningTable[1][redWarningTableRow1[index]] = this.addPercent(this.statisticsWarning[key])
        } else if (yellowTableRow1.includes(key)) {
          this.yellowWarningTable[0][key] = this.handleThousands(this.statisticsWarning[key])
        } else if (yellowTableRow2.includes(key)) {
          let index = yellowTableRow2.indexOf(key)
          this.yellowWarningTable[1][yellowTableRow1[index]] = this.addPercent(this.statisticsWarning[key])
        } else if (greenTableRow1.includes(key)) {
          this.greenWarningTable[0][key] = this.handleThousands(this.statisticsWarning[key])
        } else if (greenTableRow2.includes(key)) {
          let index = greenTableRow2.indexOf(key)
          this.greenWarningTable[1][greenTableRow1[index]] = this.addPercent(this.statisticsWarning[key])
        }
      })
    },
    addPercent(data) {
      return (data || 0) + '%'
    },
    // 订单概况详情
    async handleOverviewDetail() {
      const data = {
        pageNo: 1,
        pageSize: -1
      }
      this.loading = true
      const response = await poOverviewDetail(data)
      if (response) {
        this.detailData = response
        this.dataList = response?.pageResult?.rows || []
      } else {
        this.$message.error('获取订单概况详情失败！')
      }
      this.loading = false
      this.columns = overviewDetailField
      this.percentFiled = overviewDetailPercentField
      this.changeShowDashboardDialog(true, '订单概况', true, true)
    },
    // 预付款详情
    handlePaymentDetail() {
      const total = {
        ...this.paymentData,
        parentPgCode: this.paymentData.superPurchaseGroup,
        pgCode: this.paymentData.purchaseGroup
      }
      const dataList = this.paymentData.detailList.map(item => {
        return {
          ...item,
          submitCountRatio: (item.submitCountRatio * 100).toFixed(2),
          parentPgCode: item.superPurchaseGroup,
          pgCode: item.purchaseGroup
        }
      })
      this.detailData = {
        total,
        dataList
      }
      this.dataList = dataList
      this.columns = paymentField
      this.percentFiled = paymentPercentField
      this.changeShowDashboardDialog(true, '预付款情况', true, true)
    },
    // 跟单预警详情
    async handleWarningDetail() {
      const data = {
        pageNo: 1,
        pageSize: -1
      }
      this.loading = true
      const response = await statisticsWarningDetail(data)
      if (response) {
        this.dataList = response?.rows || []
        this.detailData = {
          total: this.statisticsWarning,
          dataList: this.dataList
        }
      } else {
        this.$message.error('获取跟单预警情失败！')
      }
      this.loading = false
      this.columns = warningDetailField
      this.percentFiled = WaringDetailPercentField
      this.changeShowDashboardDialog(true, '跟单预警', true, true)
    },
    // 跟单预警饼图
    setChart () {
      const myChart = echarts.init(document.getElementById('trend-echart'))
      // 指定图表的配置项和数据
      const option = {
        tooltip: {
          trigger: 'item',
          formatter(params) {
            let mySeries = option.series[0].data
            return mySeries[params.dataIndex].name + '  ' + mySeries[params.dataIndex].count + '<br/>' + mySeries[params.dataIndex].value + '%'
          }
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: [
              { value: this.statisticsWarning.redTypeOrderRatio, name: '红灯', count: this.handleCnt(this.statisticsWarning.redTypeOrderCnt) },
              { value: this.statisticsWarning.yellowTypeOrderRatio, name: '黄灯', count: this.handleCnt(this.statisticsWarning.yellowTypeOrderCnt) },
              { value: this.statisticsWarning.greenTypeOrderRatio, name: '绿灯', count: this.handleCnt(this.statisticsWarning.greenTypeOrderCnt) }
            ],
            label: {
              position: 'outside',
              alignTo: 'labelLine',
              overflow: 'truncate',
              margin: 0,
              formatter(params) {
                let mySeries = option.series[0].data
                return mySeries[params.dataIndex].name + '  ' + mySeries[params.dataIndex].count + '\n' + mySeries[params.dataIndex].value + '%'
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ],
        color: [
          '#F5222D', '#F58F22', '#14DAB0'
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
      myChart.resize()
    },
    emptyValue (value) {
      return value ? value + '' : ''
    },
    mapValue (prop, value, withCode) {
      const options = buildOptions(prop)
      if (Array.isArray(options)) {
        // eslint-disable-next-line eqeqeq
        const item = buildOptions(prop).find(item => item.value == value)
        if (item) {
          return withCode ? item.value + ' ' + this.emptyValue(item.name) : item.name
        } else if (prop === 'overdueReasonFirst') {
          return '未填写原因'
        }
      }
    },
    // 履约状况饼图
    setOverdueChart () {
      const data = this.overdueData.overdueFirstReasonDistributeList.map(item => {
        return {
          value: (item.ratio * 100).toFixed(2),
          name: this.mapValue('overdueReasonFirst', item.overdueFirstReason, false)
        }
      })
      const myChart = echarts.init(document.getElementById('overdue-echart'))
      // 指定图表的配置项和数据
      const option = {
        tooltip: {
          trigger: 'item',
          formatter(params) {
            let mySeries = option.series[0].data
            return mySeries[params.dataIndex].name + '<br/>' + mySeries[params.dataIndex].value + '%'
          }
        },
        series: [
          {
            type: 'pie',
            radius: '50%',
            data: data,
            label: {
              position: 'outside',
              alignTo: 'labelLine',
              overflow: 'truncate',
              margin: 0,
              formatter(params) {
                let mySeries = option.series[0].data
                return mySeries[params.dataIndex].name + '   ' + mySeries[params.dataIndex].value + '%'
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ],
        color: [
          '#729FF9', '#A184D1', '#45A8A7', '#82CFEE', '#7483A1', '#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'
        ]
      }
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)
      myChart.resize()
    },
    handleOverdueTypeDetail() {
      this.dataList = this.overdueData.overdueTypeDetailList.map(item => {
        return {
          ...item,
          parentPgCode: item.superPurchaseGroup,
          pgCode: item.purchaseGroup
        }
      })
      this.columns = OverdueTypDetailField
      this.changeShowDashboardDialog(true, '逾期类型明细', false, true)
    },
    handleOverdueReasonTable() {
      this.dataList = this.overdueData.overdueReasonDetailList.map(item => {
        return {
          ...item,
          parentPgCode: item.superPurchaseGroup,
          pgCode: item.purchaseGroup
        }
      })
      this.columns = OverdueReasonDetailField
      this.changeShowDashboardDialog(true, '逾期原因明细', false, true)
    },
    // 履约状况
    handleOverdue(data) {
      this.overdueData = data
      this.overdueReasonTable = data.overdueSecondReasonDistributeList || []
      this.setOverdueChart()
      this.OverdueTypeTable = data.overdueTypeDistributeList || []
    },
    showOverdueTypeDetail(data) {
      this.columns = overdueReasonFields
      this.percentFiled = overdueReasonPercentField
      this.dataList = data
      this.changeShowDashboardDialog(true, '', false, false)
    }
  }
};
</script>
<style lang="scss" scoped>
.purchase-dashboard{
  width: 100%;
  height: 100%;
  background: #f3f3f3;
  padding: 10px;
}
.overview {
  margin: 5px;
  padding: 10px;
  background: #fff;
  border-radius: 2px;
}
.flex-between {
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  .warning-charts {
    width: 30%;
    background: #FAFAFA;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.overdue {
  .overdue-chart, .overdue-table{
    width: 49%;
    margin: 8px;
  }
}
.detail {
  font-weight: 400;
  color: #5098FF;
  line-height: 20px;
  cursor: pointer;
}
.overdue {
  align-items: flex-start;
}
.overdue-reason {
  width: 99%;
  margin: 8px;
}
.tables {
  margin: 8px;
}
.one-line {
  height: 115px;
  overflow: hidden;
}
.todo-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  z-index: 0;
  .todo-item {
    width: 145px;
    height: 100px;
    margin: 8px;
    padding: 10px;
    background: #F0F2F5;
    border-radius: 2px;
    display: flex;
    align-content: space-between;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    .type{
      font-weight: 400;
      color: rgba(25, 26, 35, 0.55);
      font-size: 12px;
      line-height: 22px;
    }
    .type-count {
      font-size: 28px;
      line-height: 38px;
      color: #0072F5;
      cursor: pointer;
    }
  }
}
</style>
