export let updateFields = [
  'companyCode',
  'createTime',
  'createUser',
  'currency',
  'customsFeeAmount',
  'customerCode',
  'customerName',
  'customsFeeCurrency',
  'customsFeeType',
  'otherAmount',
  'otherCurrency',
  'premiumAmount',
  'premiumCurrency',
  'deliveryManagerName',
  'deliveryManagerPhone',
  'deliveryStatus',
  'intlShippingAmount',
  'intlShippingCurrency',
  'intlShippingType',
  'isAttachmentUpload',
  'isDeliveryManager',
  'isDeliveryTimeConfirm',
  'isOverchargeFree',
  'isUrgent',
  'isZeroCustomsFee',
  'isZeroIntlShipping',
  'isZeroTariff',
  'itemList',
  'latePaymentAmount',
  'latePaymentCurrency',
  'lossRatio',
  'orderNo',
  'orderRemark',
  'orderStatus',
  'orderType',
  'paymentTermCode',
  'pickupType',
  'printRemark',
  'purchaseGroup',
  'receiptAddressDetail',
  'receiptAddressPostCode',
  'receiptCity',
  'receiptContactName',
  'receiptContactPhone',
  'receiptProvince',
  'receiptRegion',
  'receiptStreet',
  'receiveAddressDetail',
  'receiveAddressPostCode',
  'receiveCity',
  'receiveContactName',
  'receiveContactPhone',
  'receiveProvince',
  'receiveRegion',
  'receiveStreet',
  'saleTaxAmount',
  'saleTaxCurrency',
  'shareDiscountAmount',
  'shareShippingAmount',
  'shipStatus',
  'source',
  'sourceOrderNo',
  'supplierAddressDetail',
  'supplierAddressPostCode',
  'supplierCity',
  'supplierContactName',
  'supplierContactPhone',
  'supplierNo',
  'supplierProvince',
  'supplierRegion',
  'supplierStreet',
  'tariffAmount',
  'tariffCurrency',
  'transactionId',
  'updateUser',
  'deleteAttachmentList',
  'receiptReturnWay',
  'drawbackWay',
  'returnWay',
  'supplierContractAttachmentList',
  'taglibIdList',
  'delTaglibIdList',
  'isZkhPickUp',
  'initialShippingAmount',
  'initialShippingAmountAttachmentList',
  'poExtend',
  'usedRebaseVoucherList',
  'deliveryChangeReason'
]

export const itemListFields = [
  'batchNo',
  'oaItemNo',
  'oaNo',
  'oaType',
  'componentList',
  'costCenter',
  'customerService',
  'customsFeeAmount',
  'customsFeeCurrency',
  'customsFeeSupplierNo',
  'customsFeeType',
  'deliveryStatus',
  'discountAmount',
  'factoryCode',
  'fixedAssetsList',
  'generalLedgerAccount',
  'inputTax',
  'intlShippingAmount',
  'intlShippingCurrency',
  'intlShippingSupplierNo',
  'intlShippingType',
  'inventoryUnit',
  'inventoryUnitDeno',
  'inventoryUnitMole',
  'isConfirmed',
  'isDeleted',
  'isDeliveryDone',
  'isUrgent',
  'isEscrow',
  'isFree',
  'isLastInvoice',
  'itemDeliveryDate',
  'itemNo',
  'itemQuantity',
  'itemRemark',
  'specialSupplySo',
  'specialSupplySoItem',
  'latePaymentAmount',
  'latePaymentCurrency',
  'latePaymentSupplierNo',
  'materialDescription',
  'materialGroupName',
  'materialGroupNum',
  'otherAmount',
  'otherCurrency',
  'otherSupplierNo',
  'planList',
  'premiumAmount',
  'premiumCurrency',
  'premiumSupplierNo',
  'priceTimes',
  'priceUnit',
  'priceUnitDeno',
  'priceUnitMole',
  'receivedQuantity',
  'returnReason',
  'sapOrderNo',
  'sapItemNo',
  'saleTaxAmount',
  'saleTaxCurrency',
  'saleTaxSupplierNo',
  'shipStatus',
  'shipWarehouseLocation',
  'shippedQuantity',
  'shippingAmount',
  'skuNo',
  'soItemNo',
  'soNo',
  'soPlanNo',
  'standardLeadTime',
  'supplierMaterialNo',
  'supplierOrderNo',
  'tariffAmount',
  'tariffCurrency',
  'tariffSupplierNo',
  'taxedPrice',
  'taxedTotalAmount',
  'untaxedTotalAmount',
  'taxTotalAmount',
  'thousandWeight',
  'trackNo',
  'unit',
  'warehouseLocation',
  'receivedQuantity',
  'projectCategory',
  'isReturn',
  'orderReason',
  'deleteReason',
  'refundableAmount',
  'customInstructions',
  'cloud',
  'transferReason',
  'isRealOrder',
  'initialItemShippingAmount',
  'poItemExtend',
  'shareRebatePOItemList',
  'receiveQuantity',
  'deliveryChangeReason'
]

export const compFields = [
  'componentInventoryUnit',
  'componentMaterialDescription',
  'componentNo',
  'componentRequiredQuantity',
  'componentShippedQuantity',
  'componentSkuNo',
  'componentWarehouseLocation',
  'isDeleted'
]

export const assetsFields = [
  'fixedAssetsCardNo',
  'fixedAssetsCardQuantity',
  'fixedAssetsCardSubNo',
  'fixedAssetsNo',
  'isDeleted'
]

export const planFields = [
  'deliveryDate',
  'deliveryQuantity',
  'isDeleted',
  'planNo',
  'idnQty',
  'odnQty',
  'prOrderItemNo',
  'prOrderNo',
  'receivedQuantity',
  'deliveryChangeReason'
]
