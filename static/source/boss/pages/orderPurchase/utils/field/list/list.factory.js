// const NodeList = {
//   selections: [],
//   loading: {
//     searchLoading: false,
//     tableLoading: false
//   },
//   searchForm: {
//     orderNos: '',
//     purchaseGroup: ''
//   },
//   table: {
//     pageNo: 1,
//     current: 1,
//     pageSize: 10,
//     total: 0,
//     tableData: []
//   }
// }
// field 定义
const nodeFields = {
  query: [
    { label: '订单号检索', prop: 'orderNos', placeholder: '采购单号或SAP订单号', type: 'input', clearable: true, span: 12 },
    { label: '采购姓名：', prop: 'purchaseGroup', placeholder: '请输入', type: 'component', componentName: 'AfterSaleStaff' },
    { type: 'button', text: '查询', buttonType: 'primary' }
  ],
  tableList: [
    { label: '采购单号', prop: 'orderNo', 'show-overflow-tooltip': true },
    { label: 'SAP订单号', prop: 'sapOrderNo', 'show-overflow-tooltip': true },
    { label: '订单类型', prop: 'orderType', 'show-overflow-tooltip': true },
    { label: '审核状态', prop: 'approveStep', 'show-overflow-tooltip': true },
    { label: '到货状态', prop: 'deliveryStatus', 'show-overflow-tooltip': true },
    { label: '交期确认', prop: 'isDeliveryTimeConfirm', 'show-overflow-tooltip': true },
    { label: '删除状态', prop: 'isDeleted', 'show-overflow-tooltip': true },
    { label: '采购姓名', prop: 'purchaseGroup', 'show-overflow-tooltip': true },
    { label: '供应商', prop: 'supplierName', 'show-overflow-tooltip': true },
    { label: '创建日期', prop: 'createTime', 'show-overflow-tooltip': true }
  ]
}

export class ListFactory {
  getFields () {
    return nodeFields
  }
}
