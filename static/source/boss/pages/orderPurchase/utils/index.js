import { displaceSku, getInfoBySpecialSupplySo } from '@/api/mm'

export function formatAgreementData (data) {
  const { atpInfoList = [] } = data
  const results = []
  if (atpInfoList && atpInfoList.length > 0) {
    atpInfoList.forEach(atp => {
      const { position } = atp
      delete atp.position
      const result = {
        ...data,
        ...atp,
        soPosition: position
      }
      results.push(result)
    })
  } else {
    results.push(data)
  }
  return results
}

export function formatLogisticsData (data) {
  const { deliveryInfoVOList = [], transportInfoVOList = [], warehouseInfoVOList = [] } = data
  let results = []
  if (deliveryInfoVOList.length > 0) {
    let deliveryTime = ''
    let number = ''
    let deliveryWayText = ''
    let logisticsName = ''
    let logisticsCode = ''
    let tcLogisticsName = ''
    let tcLogisticsCode = ''
    let statusText = ''
    const gt1 = deliveryInfoVOList.length > 1
    for (let i = 0; i < deliveryInfoVOList.length; i++) {
      deliveryTime += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].deliveryTime || ''} <br>`
      number += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].number || ''} <br>`
      deliveryWayText += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].deliveryWayText || ''} <br>`
      logisticsName += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].logisticsName || ''} <br>`
      logisticsCode += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].logisticsCode || ''} `
      tcLogisticsName += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].tcLogisticsName || ''} <br>`
      tcLogisticsCode += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].tcLogisticsCode || ''} <br>`
      statusText += `${gt1 ? `No${i + 1}:` : ''}${deliveryInfoVOList[i].statusText || ''} <br>`
      deliveryInfoVOList[i]._logisticsCode = deliveryInfoVOList[i].logisticsCode
      deliveryInfoVOList[i]._tcLogisticsCode = deliveryInfoVOList[i].tcLogisticsCode
    }
    deliveryInfoVOList[0].deliveryTime = deliveryTime
    deliveryInfoVOList[0].number = number
    deliveryInfoVOList[0].deliveryWayText = deliveryWayText
    deliveryInfoVOList[0].logisticsName = logisticsName
    deliveryInfoVOList[0].logisticsCode = logisticsCode
    deliveryInfoVOList[0].tcLogisticsName = tcLogisticsName
    deliveryInfoVOList[0].tcLogisticsCode = tcLogisticsCode
    deliveryInfoVOList[0].statusText = statusText
    results.push({
      ...data,
      ...deliveryInfoVOList[0]
    })
  } else {
    results.push(data)
  }
  if (transportInfoVOList.length > 0) {
    let results1 = []
    transportInfoVOList.forEach(transport => {
      results.forEach(result => {
        results1.push({
          ...result,
          ...transport
        })
      })
    })
    results = results1
  }
  if (warehouseInfoVOList.length > 0) {
    let results2 = []
    warehouseInfoVOList.forEach(warehouse => {
      const { taskInfoVOList } = warehouse
      results.forEach(result => {
        results2.push({
          ...result,
          ...warehouse,
          driver: (taskInfoVOList || []).map(v => `${v.driverName}/${v.driverTel}`).join(',')
        })
      })
    })
    results = results2
  }
  return results
}

export function isValidSoNo (soNo) {
  return soNo && soNo.length === 20
}

export function formatAmount (amount, p) {
  return Number(amount.toFixed(p));
}

export function getDefaultWarehouseLocation(warehouseConfigList, purchaseGroup, factoryCode) {
  let warehouseLocation
  if (warehouseConfigList) {
    const found = warehouseConfigList.find(item => {
      return item.purchaseGroup === purchaseGroup && item.factory.toString() === factoryCode
    })
    if (found) {
      warehouseLocation = found.warehouseLocation.toString()
    }
  }
  return warehouseLocation
}
export function getD02ComponentWarehouseLocation(orderType, purchaseGroup, KHWarehouseList, warehouseLocation, componentWarehouseLocation, componentWarehouseList) {
  const isKHGroup = getKHpurchaseGroup(purchaseGroup, componentWarehouseList)
  if (orderType === 'Z006' && isKHGroup && KHWarehouseList) {
    const KHWarehouseListFilter = KHWarehouseList.find(item => item.warehouseLocationCode === warehouseLocation)
    if (KHWarehouseListFilter) {
      const warehouseCodeFilter = KHWarehouseList.find(item => item.warehouseCode === KHWarehouseListFilter.warehouseCode && item.qualityStatus === '13')
      if (warehouseCodeFilter) {
        return warehouseCodeFilter.warehouseLocationCode
      }
    }
  }
  if (componentWarehouseLocation) {
    return componentWarehouseLocation
  }
  return warehouseLocation
}
export function getKHpurchaseGroup(purchaseGroup, componentWarehouseList) {
  if (componentWarehouseList && componentWarehouseList.length > 0) {
    const findGroup = componentWarehouseList.find(item => item.value === purchaseGroup)
    return findGroup?.value
  }
  return false
}
export function thousands(num) {
  return num ? num.toLocaleString() : '';
}
export async function handleDisplaceSku(item) {
  const { itemNo, poNo, skuNo } = item
  const data = {
    poNo,
    skuNo,
    poItemNo: itemNo,
    updateUser: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
  }
  let response = false
  response = await displaceSku(data).then(res => {
    if (res) {
      return res
    }
    return false
  })
  return response
}
export async function handleGetInfoBySpecialSupplySo(row, dictList) {
  const { specialSupplySo, specialSupplySoItem } = row
  const data = await getInfoBySpecialSupplySo({
    specialSupplySo, specialSupplySoItem
  }).then(res => {
    console.log('handleGetInfoBySpecialSupplySo', res)
    if (res && res.skuNo) {
      return handlePrice(res, row, dictList)
    }
    return row
  })
  return data
}
export function getInputTax (val, dictList) {
  const taxRate = Number((dictList['inputTax'].find(item => item.value === val) || {}).description) || 0
  return taxRate
}
export function handlePrice(data, row, dictList) {
  row = {
    ...row,
    ...data
  }
  return row
}
