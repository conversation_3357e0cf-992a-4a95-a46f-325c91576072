<template>
  <div class="app-container">
    <InventoryForm ref="inventoryForm"  :inventoryTypes="inventoryTypes">
      <template #default>
        <el-button type="primary" @click="search">查询</el-button>
          <el-button  @click="reset">重置</el-button>
      </template>
    </InventoryForm>
      <InventoryGrid :list="list" :inventoryTypes="inventoryTypes" :loading="loading">
        <template #default>
          <div class="inventory-upload-wrapper">
            <span style="font-size:13px">说明：导入呆滞原因的模板与导出明细EXCEL相同。</span>
            <div class="action">
              <el-upload class="inventory-upload"
                name="serviceFile"
                accept=".xlsx"
                :show-file-list="false"
                style="display:inline-block"
                :before-upload="handleBeforeUpload"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                action="/api-sim/sluggishReason/import/excel"
              >
                <el-button v-if="getButtonAuth('实时库存', '导入呆滞原因')" size="small" type="primary">导入呆滞原因</el-button>
              </el-upload>
              <el-button v-if="getButtonAuth('实时库存', '导出明细')" size="small" @click="exportDull" style="margin-left:20px" :disabled="list.length<=0" >导出明细</el-button>
            </div>
          </div>
        </template>
      </InventoryGrid>
      <Pagination
        v-show="total > 0"
        :total="total"
        align="right"
        :page.sync="listQueryInfo.pageNo"
        :limit.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getPaginationInventoryApllyList"
      />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getInventoryApllyList, exportDullInventoryList } from '@/api/mm'
import InventoryForm from './components/inventory/form'
import InventoryGrid from './components/inventory/grid'
import Pagination from '@/components/Pagination'
import { getButtonAuth } from '@/utils/auth'

export default {
  data () {
    return {
      list: '',
      listQueryInfo: {
        pageNo: 1,
        pageSize: 50
      },
      uploadLoading: null,
      loading: false,
      total: 0

    }
  },
  created() {
    if (this.inventoryTypes.length === 0) {
      this.$store.dispatch('orderPurchase/getInventoryTypes')
    }
  },
  components: { InventoryForm, InventoryGrid, Pagination },
  computed: {
    ...mapState({
      inventoryTypes: state => state.orderPurchase.inventoryTypes
    })
  },
  methods: {
    getButtonAuth,
    getInventoryApllyList() {
      this.loading = true
      const queryParams = {}
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 50
      queryParams.current = this.listQueryInfo.pageNo
      queryParams.size = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getInventoryApllyList(queryParams).then((res) => {
        if (res.code === 0) {
          this.list = res.data.records
          this.total = res.data.total
        } else if (res.status !== 200 || res.status !== 0) {
          this.$message.error(res.msg);
        }
        this.loading = false
      }).catch((err) => {
        console.log(err);
      })
    },
    getPaginationInventoryApllyList() {
      this.loading = true
      const queryParams = {}
      queryParams.current = this.listQueryInfo.pageNo
      queryParams.size = this.listQueryInfo.pageSize
      Object.assign(queryParams, { ...this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm })
      getInventoryApllyList(queryParams).then((res) => {
        if (res.code === 0) {
          this.list = res.data.records
          this.total = res.data.total
        } else if (res.status !== 200 || res.status !== 0) {
          this.$message.error(res.msg);
        }
        this.loading = false
      }).catch((err) => {
        console.log(err);
      })
    },
    search() {
      this.$refs.inventoryForm.$refs['searchForm'].validate((valid) => {
        let dayValidator = this.$refs.inventoryForm.leftDayStartErr || this.$refs.inventoryForm.leftDayEndErr || this.$refs.inventoryForm.stockAgeStartErr || this.$refs.inventoryForm.stockAgeEndErr
        if (dayValidator) {
          return false
        }
        if (valid) {
          this.getInventoryApllyList()
        } else {
          return false;
        }
      });
    },
    reset() {
      // this.$refs.inventoryForm.$refs['searchForm'].resetFields()
      for (var k in this.$refs.inventoryForm.searchForm) {
        if (this.$refs.inventoryForm.searchForm.hasOwnProperty(k)) {
          if (k === 'factory') {
            this.$refs.inventoryForm.searchForm[k] = ['1000']
          } else if (k === 'leftStartDays' || k === 'leftEndDays' || k === 'startStockAge' || k === 'endStockAge') {
            this.$refs.inventoryForm.searchForm[k] = undefined
          } else {
            this.$refs.inventoryForm.searchForm[k] = ''
          }
        }
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.uploadLoading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleUploadError (error) {
      console.log(error)
      this.importLoading = false
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      )
    },
    // 导入呆滞原因
    handleUploadSuccess(res) {
      if (this.uploadLoading) {
        this.uploadLoading.close()
      }
      let msg = ''
      if (res && res.status === 200) {
        this.$message.success(msg || '导入呆滞原因成功！')
      } else {
        this.$alert(res.msg.replace(/\n/, '</br>') || '导入呆滞原因失败', '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    // 导出明细
    exportDull() {
      this.$confirm('此操作导出所有数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        exportDullInventoryList(this.$refs.inventoryForm && this.$refs.inventoryForm.copySearchForm).then((res) => {
          if (res.status === 200) {
            this.$router.push({ name: 'downLoadList' })
          } else {
            this.$alert(res.msg)
          }
          console.log(res);
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    }

  }
}
</script>

<style lang="scss" scoped>
.inventory-upload-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .inventory-upload {
    display: flex;
  }
}
</style>
