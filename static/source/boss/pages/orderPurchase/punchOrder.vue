<template>
  <div class="punch-order-list" v-loading="loading.pageLoading">
    <div class="search-filter">
      <el-form :model="searchForm" ref="searchFrom" style="width: 100%" label-suffix=":" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="供应商" prop="supplierNo">
              <el-select
                default-first-option
                placeholder="请选择"
                v-model="searchForm.supplierNo"
              >
                <el-option
                  v-for="item in supplierOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button>取消</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result">
      <div class="result-container">
        <div class="item" v-for="(data, index) in dataList" :key="index">
          <div class="logo">
            <img :src="data.logo" alt="logo">
          </div>
          <div class="content">
            <span class="no">【{{data.supplierNo}}】</span>
            <span class="name">{{data.supplierName}}</span>
            <div class="button-group">
              <el-button type="primary" @click="jumpToZ001" :disabled="!getButtonAuth('创建采购单', 'Festo购物车选品')">购物车选品</el-button>
              <el-button @click="openWindow(data.manual)">操作手册</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="pagi" v-if="table.total > 10">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="table.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="table.total">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { getPunchList } from '@/api/honeycomb'
import { getButtonAuth } from '@/utils/auth'
const data = {
  logo: 'https://zkh-vpi.oss-cn-hangzhou.aliyuncs.com/honeycomb/festo_logo.png',
  manual: 'https://zkh360-honeycomb.oss-cn-hangzhou.aliyuncs.com/%E8%B4%B9%E6%96%AF%E6%89%98%E8%AE%A2%E5%8D%95%E5%AF%B9%E6%8E%A5%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8C.pdf',
  supplierNo: 'V45696',
  supplierName: '费斯托（中国）有限公司'
}
export default {
  name: 'punchOrder',
  data() {
    return {
      supplierOptions: [
        { label: '费斯托（中国）有限公司', value: 'V45696' }
      ],
      searchForm: {
        supplierNo: 'V45696'
      },
      loading: {
        pageLoading: false
      },
      dataList: [],
      table: {
        pageNo: 1,
        pageSize: 10,
        total: 1
      },
      shopcart: 'https://www.festo.com.cn/jsl/en-cn_cn/FORMDS/ocilogin?l_user=OCI_ZKH360_GCN_EN&j_password=OCI_zkh360_2020_en&HOOK_URL=https://honeycomb.zkh360.com/openOci/festo/oci'
    };
  },
  async created() {
    this.loading.pageLoading = true
    this.initSearch()
    this.loading.pageLoading = false
  },
  computed: {
    randomImg () {
      return `http://iph.href.lu/${Math.trunc(Math.random() * 200) + 300}x${Math.trunc(Math.random() * 200) + 300}`
    }
  },
  methods: {
    getButtonAuth,
    openWindow () {
      window.open(data.manual)
    },
    async initSearch () {
      const { pageNo, pageSize } = this.table
      const { supplierNo } = this.searchForm
      const params = { supplierNo, pageNo, pageSize, ...data }
      return getPunchList(params)
        .then(res => {
          if (Array.isArray(res)) {
            this.dataList = res
          }
        })
    },
    handleSupplierChange (val, list) {
      const supplierInfo = list.find(item => item.supplierNo === val)
      this.searchForm.supplierNo = val
      this.supplier = supplierInfo || {}
    },
    jumpToZ001 () {
      window.isFesto = true
      this.$router.push('/orderPurchase/create/Z001')
      window.open(this.shopcart)
    },
    handleSearch () {
      console.log(this.table, this.searchForm)
      this.initSearch()
    },
    handleSizeChange () {},
    handleCurrentChange () {}
  }
};
</script>
<style lang="scss" scoped>
.punch-order-list {
  padding: 10px;
  .search-result {
    padding-left: 20px;
    .result-container{
      display: flex;
      /* justify-content: space-around; */
      flex-wrap: wrap;
      min-width: 920px;
      max-width: 1400px;
      .item{
        display: flex;
        align-items: center;
        padding: 10px;
        height: 120px;
        width: 45%;
        min-width: 400px;
        max-width: 500px;
        border: solid 1px lightgrey;
        margin: 10px;
        .logo{
          height: 100px;
          width: 160px;
          text-align: center;
          line-height: 100px;
          img{
            max-width: 100%;
            object-fit: contain;
            max-height: 100%;
          }
        }
        .content{
          height: 100px;
          width: calc(100% - 160px);
          /* border: solid 1px lightgrey; */
          margin-left: 50px;
          padding: 10px;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          .no{
            color: grey
          }
          .button-group{
            margin-top: 15px;
          }
        }
      }
    }
  }
  .pagi{
    margin-top: 20px;
    float:right;
  }
  .el-select {
    width: 100%;
  }
}
</style>
