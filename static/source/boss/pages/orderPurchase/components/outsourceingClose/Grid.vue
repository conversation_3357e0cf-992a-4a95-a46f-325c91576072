<template>
    <vxe-grid
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="aggreementGrid"
      height="710"
      id="aggreement_grid"
      row-id
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="listData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
      :checkbox-config="{checkMethod:checkMethod,reserve: true}"
      @checkbox-all="selectAll"
      @checkbox-change="selectChange"
    >
    <template v-slot:toolbar_buttons>
      <el-button  v-if="getButtonAuth('批量关单', '批量关单')" type="primary" :disabled="disable" @click="toClose">批量关单</el-button>
    </template>

    <template v-slot:companyCode_default="{ row }">
      {{ row.companyCode + ' ' + (companyFactoryList.find(item => item.companyCode === row.companyCode) || {}).companyName }}
    </template>

    <template v-slot:supplierName_default="{ row }">
      {{ row.supplierNo + ' ' + row.supplierName }}
    </template>

    <template v-slot:purchaseGroup_default="{ row }">
      {{ row.purchaseGroup + ' ' + (purchaseList.find(item => item.groupCode === row.purchaseGroup) || {}).userName }}
    </template>

    <template v-slot:receivedRatio_default="{ row }">
      {{ formattingData(row) }}
    </template>

    <template v-slot:factoryCode_default="{ row }">
      {{ row.factoryCode + ' '+ (factoryList.find(item => item.factoryCode === row.factoryCode) || {}).factoryName }}
    </template>

    <template #pager>
      <vxe-pager
        :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
        :page-sizes="[100,200,300,400,500]"
        :border="true"
        :current-page.sync="tablePage.currentPage"
        :page-size.sync="tablePage.pageSize"
        :total="tablePage.total"
        @page-change="handlePageChange">
      </vxe-pager>
    </template>
    </vxe-grid>
</template>

<script>
import { getButtonAuth } from '@/utils/auth'
import { formatAmount } from '../../utils/index'
import { mapState } from 'vuex'
const columns = [
  {
    type: 'checkbox',
    minWidth: 50
  },
  {
    field: 'orderNo',
    title: '采购订单',
    minWidth: 120
  },
  {
    field: 'companyCode',
    title: '公司',
    minWidth: 120,
    slots: {
      default: 'companyCode_default'
    }
  },
  {
    field: 'supplierName',
    title: '供应商',
    minWidth: 120,
    slots: {
      // 使用插槽模板渲染
      default: 'supplierName_default'
    }
  },
  {
    field: 'purchaseGroup',
    title: '采购员',
    minWidth: 130,
    slots: {
      // 使用插槽模板渲染
      default: 'purchaseGroup_default'
    }
  },
  {
    field: 'itemNo',
    title: '项目行',
    minWidth: 100
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    minWidth: 100
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    minWidth: 200
  },
  {
    field: 'itemQuantity',
    title: '订单数量',
    minWidth: 100
  },
  {
    field: 'receivedQuantity',
    title: '收货数量',
    minWidth: 100
  },
  {
    field: 'receivedRatio',
    title: '收货比例',
    minWidth: 100,
    slots: {
      default: 'receivedRatio_default'
    }
  },
  {
    field: 'factoryCode',
    title: '工厂',
    minWidth: 100,
    slots: {
      default: 'factoryCode_default'
    }
  }
]

export default {
  props: {
    listData: {
      type: Array,
      default: () => []
    },
    tableLoading: {
      type: Boolean,
      default: false
    },
    tablePage: {
      type: Object,
      default: () => ({
        total: 0,
        currentPage: 1,
        pageSize: 200
      })
    },
    selectList: {
      type: Array,
      default: () => ([])
    }

  },
  data() {
    return {
      columns,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          tools: 'toolbar_buttons'
        }
      }
    };
  },
  computed: {
    ...mapState({
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      purchaseList: state => state.orderPurchase.purchaseList
    }),
    factoryList: {
      get () {
        let factories = []
        for (let i = 0; i < this.companyFactoryList.length; i++) {
          factories.push(...this.companyFactoryList[i].factoryList)
        }
        return factories
      }
    },
    disable () {
      return this.selectList.length <= 0
    }

  },
  methods: {
    getButtonAuth,
    handlePageChange({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.$emit('handlePageChange', this.tablePage)
    },
    checkMethod ({ row }) {
      if (row.receivedQuantity === 0) {
        return false
      } else {
        return true
      }
    },
    formattingData (row) {
      return formatAmount(row.receivedRatio * 100, 2) + '%'
    },
    selectAll ({ checked, reserves, records }) {
      this.$emit('update:selectList', records)
    },
    selectChange ({ checked, records, reserves, row }) {
      this.$emit('update:selectList', records)
    },
    toClose() {
      this.$emit('toClose')
    }

  },
  components: {

  }
};
</script>

<style scoped lang="scss">

</style>
