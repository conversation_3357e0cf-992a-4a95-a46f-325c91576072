<template>
  <div class="overdue-type">
    <vxe-table
      border
      resizable
      class="overdue-table"
      :scroll-y="{enabled: false}"
      :cell-class-name="cellClassName"
      max-height="300"
      :data="tableData">
       <vxe-table-column
        v-for="col in finalFields"
        align="center"
        :width="col.width"
        :key="col.field"
        :field="col.field"
        :title="col.title"
    >
        <template slot-scope="{row}">
          <span v-if="col.enums" @click="handleDetail(row)">
            {{mapValue(col.enums, row[col.field], false)}}
           </span>
           <span v-else-if="col.isPercent" >
            {{(row[col.field] * 100).toFixed(2)}} %
           </span>
          <span v-else>
            {{row[col.field]}}
          </span>
          </template>
        </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script>
import { buildOptions } from '@/utils/mm'
export default {
  name: 'OverdueType',
  props: [ 'finalFields',
    'tableData', 'canShowDetail'],
  data () {
    return {
    }
  },
  methods: {
    emptyValue (value) {
      return value ? value + '' : ''
    },
    mapValue (prop, value, withCode) {
      const options = buildOptions(prop)
      if (Array.isArray(options)) {
        // eslint-disable-next-line eqeqeq
        const item = buildOptions(prop).find(item => item.value == value)
        if (item) {
          return withCode ? item.value + ' ' + this.emptyValue(item.name) : item.name
        } else if (prop === 'overdueReasonFirst') {
          return '未填写原因'
        }
      }
    },
    cellClassName({ row, column }) {
      if (column.property === 'poFollowTagType') {
        return 'row-blue'
      }
      if (column.property === 'overdueFirstReason' && !row.overdueFirstReason) {
        return 'row-red'
      }
      return null
    },
    handleDetail(row) {
      if (!this.canShowDetail) {
        return
      }
      const data = row.reasonDistributeList.map(item => {
        return {
          ...item,
          ratio: (item.ratio * 100).toFixed(2)
        }
      })
      this.$emit('showOverdueTypeDetail', data)
    }
  }
}
</script>
<style scoped lang="scss">
.overdue-type {
  margin: 8px;
}
</style>
<style lang="less">
.overdue-table .vxe-body--column.row-blue {
  color: #5098FF;
  cursor: pointer;
}
.overdue-table .vxe-body--column.row-red {
  color: red;
}
</style>
