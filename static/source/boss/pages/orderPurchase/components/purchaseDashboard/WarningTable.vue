<template>
  <div class="WarningTable">
    <div :class="['title', type ]">
      <el-popover
        placement="right"
        title=""
        trigger="hover"
        >
        <span>
          {{renderTips[type]}}
        </span>
        <span slot="reference">
          <span @click="goToTraceOrder">{{type === 'red' ? '红灯' :( type === 'green' ? '绿灯' : '黄灯' ) }} <i class="el-icon-question"></i></span>
        </span>
      </el-popover>
    </div>
    <vxe-table
      border
      resizable
      class="warning-table"
      :scroll-y="{enabled: false}"
      :data="tableData">
       <vxe-table-column
        v-for="col in finalFields"
        align="center"
        :width="col.width"
        :key="col.field"
        :field="col.field"
        :title="col.title"
        :fixed="col.fixed"
        show-header-overflow
        show-overflow="title"
    >
        <template slot-scope="{row}">
          <span >
            {{row[col.field]}}
          </span>
          </template>
        </vxe-table-column>
    </vxe-table>
  </div>
</template>
<script>
export default {
  name: 'WarningTable',
  props: [ 'finalFields',
    'tableData', 'type', 'renderTips' ],
  data () {
    return {
      lightStatusList: {
        green: 1,
        yellow: 2,
        red: 3
      }
    }
  },
  methods: {
    goToTraceOrder() {
      try {
        this.$closeTag('/purchaseReport/trackingOrder');
      } catch {}
      this.$router.push({
        path: '/purchaseReport/trackingOrder',
        query: {
          reportLightStatus: this.lightStatusList[this.type]
        }
      })
    }
  }
}
</script>
<style lang="less">
.warning-table {
  width: 100%;
}
</style>
<style scoped lang="scss">
.WarningTable {
  display: flex;
  align-items: center;
  .title {
    width: 20px;
    height: 144px;
    border-radius: 5px 0;
    color: #fff !important ;
    -webkit-writing-mode: vertical-rl;
    writing-mode: vertical-rl;
    cursor: pointer;
    span:first-child{
      margin-top: 12%;
      margin-right: 5px;
    }
  }
  .red {
    background-color: red !important ;
  }
  .green {
    background-color: #14DAB0 !important ;
  }
  .yellow {
    background: #F58F22;
  }
}
</style>
