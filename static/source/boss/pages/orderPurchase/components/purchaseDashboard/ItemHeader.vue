<template>
  <div class="item-header">
    <div class="title">
      <div class="title-name">{{titleName}}
        <span class="warning-tips" v-if="showDetail =='warning'">
          上次更新时间{{updateTime}},每30分钟更新
        </span>
      </div>
      <div class="show-detail" v-if="showDetail == 'todo'" @click="showMore">
        {{`${isShowMore ? '收起' : '展开更多'}`}}
        <i class="el-icon-arrow-down" v-if="!isShowMore"/>
        <i class="el-icon-arrow-up" v-if="isShowMore"/>
        </div>
      <div class="show-detail" v-else-if="showDetail" @click="handleClick">查看详情</div>
    </div>
    <div class="line"></div>
  </div>
</template>
<script>
export default {
  name: 'itemHeader',
  props: {
    titleName: String,
    showDetail: String,
    updateTime: String
  },
  data () {
    return {
      isShowMore: false
    }
  },
  mounted() {
  },
  methods: {
    handleClick() {
      this.$emit('handleDetail')
    },
    showMore() {
      this.isShowMore = !this.isShowMore
      this.$emit('handleDetail')
    }
  }
}
</script>

<style lang="scss" scoped>
.item-header{
  padding: 0 8px;
  .title{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title-name {
      font-weight: 500;
      color: #191A23;
      line-height: 22px;
      .warning-tips{
        font-weight: 400;
        color: #F5222D;
        line-height: 22px;
        margin-left: 40px;
      }
    }
    .show-detail {
      font-weight: 400;
      color: #5098FF;
      line-height: 20px;
      cursor: pointer;
    }
  }
  .line {
    width: 100%;
    border: 1px solid #EEEEEE;
    margin-top: 8px;
  }
}
</style>
