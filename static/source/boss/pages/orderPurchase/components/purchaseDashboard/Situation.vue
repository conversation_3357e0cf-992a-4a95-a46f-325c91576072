<template>
  <div class="Situation">
    <el-row v-for="(row, index) in finalFields" :key="index">
      <el-col :span="24/row.length" class="col" v-for="(item, itemIndex) in row" :key="itemIndex">
        <div class="item">
          <div class="item-title">
            {{item.title}}
          </div>
          <div class="item-content">
            <span>{{thousands(orderData[item.propsFirst]) || 0}}
              <span v-if="item.percent">%</span>
            </span>
            <span v-if="item.propsSecond">/ {{thousands(orderData[item.propsSecond]) || 0}}</span>
            <span v-if="item.propsSecondPercent">%</span>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { thousands } from '@/pages/orderPurchase/utils/index'
export default {
  name: 'Situation',
  props: [ 'finalFields',
    'orderData' ],
  data () {
    return {
    }
  },
  mounted() {
  },
  methods: {
    thousands,
    handleClick() {
      this.$emit('handleDetail')
    }
  }
}
</script>

<style lang="scss" scoped>
.Situation{
  padding: 15px 20px;
  .item {
    margin-bottom: 20px;
  }
  .item-title {
    font-weight: 400;
    color: rgba(25, 26, 35, 0.55);
    line-height: 22px;
    margin: 8px 0;
  }
  .item-content {
    font-weight: bold;
    color: #191A23;
    line-height: 38px;
    font-size: 20px;
  }
}
</style>
