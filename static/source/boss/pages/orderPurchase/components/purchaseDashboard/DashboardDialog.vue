<template>
  <el-dialog
    :visible="showDialogVisible"
    :title="titleName"
    :before-close="closeDialog">
    <div class="container">
      <div class="body">
        <vxe-table
          resizable
          border
          show-overflow
          highlight-hover-row
          highlight-current-row
          show-footer-overflow
          show-footer
          height="400"
          class="dashboard-table"
          :cell-class-name="cellClassName"
          ref="detailTable"
          align="center"
          size="small"
          :span-method="mergeRowMethod"
          :scroll-y="{gt: -1}"
          :sort-config="{trigger: 'cell'}"
          :data="dataList || []"
          :footer-method="footerMethod"
          >
          <vxe-table-column
            v-for="col in columns"
            align="center"
            :width="col.width"
            :key="col.field"
            :field="col.field"
            :title="col.title"
        >
            <template slot-scope="{row}">
               <span v-if="col.isPercent" >
                 {{row[col.field] || 0}}%
               </span>
               <span v-else-if="col.enums" >
                {{mapValue(col.enums, row[col.field], col.enums === 'purchaseGroup' ? true : false)}}
               </span>
               <span v-else >
                 {{row[col.field] || 0}}
               </span>
            </template>
        </vxe-table-column>
        </vxe-table>
      </div>
      <span slot="footer" class="dialog-footer" v-if="isExport">
        <el-button @click="exportDetail" type="primary" :loading="btnLoading">导  出</el-button>
      </span>
    </div>
  </el-dialog>
</template>
<script>
import { thousands } from '@/pages/orderPurchase/utils/index'
import { buildOptions } from '@/utils/mm'
import * as moment from 'moment'
import { writeFile } from '@boss/excel'

export default {
  name: 'DashboardDialog',
  props: {
    showDialog: Boolean,
    titleName: String,
    dataList: Array,
    detailData: Object,
    columns: Array,
    percentFiled: Array,
    showSum: Boolean,
    isExport: Boolean
  },
  computed: {
    showDialogVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  data () {
    return {
      btnLoading: false,
      mergeCells: []
    }
  },
  methods: {
    thousands,
    emptyValue (value) {
      return value ? value + '' : ''
    },
    mergeRowMethod ({ row, _rowIndex, column, visibleData }) {
      const fields = ['parentPgCode']
      const cellValue = row[column.property]
      if (fields.includes(column.property)) {
        const prevRow = visibleData[_rowIndex - 1]
        let nextRow = visibleData[_rowIndex + 1]
        if (prevRow && prevRow[column.property] === cellValue) {
          return { rowspan: 0, colspan: 0 }
        } else {
          let countRowspan = 1
          while (nextRow && nextRow[column.property] === cellValue) {
            nextRow = visibleData[++countRowspan + _rowIndex]
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 }
          }
        }
      }
    },
    mapValue (prop, value, withCode) {
      const options = buildOptions(prop)
      if (Array.isArray(options)) {
        // eslint-disable-next-line eqeqeq
        const item = buildOptions(prop).find(item => item.value == value)
        if (item) {
          return withCode ? item.value + ' ' + this.emptyValue(item.name) : item.name
        } else if (prop === 'overdueReasonFirst') {
          return '未填写原因'
        } else if (prop === 'warningType') {
          return value === 2 ? '逾期已发货' : '逾期未发货'
        }
      }
    },
    closeDialog () {
      this.showDialogVisible = false
    },
    cellClassName({ row, column }) {
      if (column.property === 'overdueFirstReason' && !row.overdueFirstReason) {
        return 'row-red'
      }
      return null
    },
    footerMethod({ columns }) {
      if (!this.showSum) {
        return []
      }
      return [
        columns.map((column, columnIndex) => {
          if (columnIndex === 0) {
            return ''
          }
          if (columnIndex === 1) {
            return '合计'
          }
          const num = this.thousands(this.detailData?.total[column.property] || '') || 0
          const percent = this.percentFiled.includes(column.property) ? '%' : ''
          return (num + percent)
        })
      ]
    },
    async exportDetail () {
      const mapping = {}
      this.btnLoading = true
      this.columns.map(item => { mapping[item.field] = item.title })
      const enumsFields = {
        'pgCode': 'purchaseGroup',
        'parentPgCode': 'purchaseGroup',
        'poFollowTagType': 'poFollowReportTag',
        'overdueFirstReason': 'overdueReasonFirst',
        'overdueSecondReason': 'overdueReasonSecond'
      }
      // console.log('this.columns', this.columns)
      // console.log('mapping', mapping, this.dataList)
      let allList = this.dataList.map(item => {
        const itemCopy = { ...item }
        Object.keys(itemCopy).forEach((key) => {
          if (mapping[key]) {
            // console.log('key', key, itemCopy[key], mapping[key], this.percentFiled.includes(key))
            if (this.percentFiled.includes(key)) {
              itemCopy[mapping[key]] = (itemCopy[key] + '%')
            } else if (enumsFields[key]) {
              itemCopy[mapping[key]] = this.mapValue(enumsFields[key], itemCopy[key], enumsFields[key] === 'purchaseGroup')
            } else if (['warningType'].includes(key)) {
              itemCopy[mapping[key]] = itemCopy[key] === 2 ? '逾期已发货' : '逾期未发货'
            } else {
              itemCopy[mapping[key]] = itemCopy[key]
            }
          }
          delete itemCopy[key]
        })
        return itemCopy
      })
      console.log('allList', allList)
      const totalItem = { ...this.detailData?.total }
      Object.keys(totalItem).forEach((key) => {
        if (mapping[key]) {
          if (this.percentFiled.includes(key)) {
            totalItem[mapping[key]] = (totalItem[key] + '%')
          } else if (['pgCode'].includes(key) && this.showSum) {
            totalItem[mapping[key]] = '合计'
          } else {
            totalItem[mapping[key]] = totalItem[key]
          }
        }
        delete totalItem[key]
      })
      allList.push(totalItem)
      await writeFile(allList, `${this.titleName} ${moment(new Date()).format('YYYY-MM-DD HH-mm-ss')}.xlsx`, { header: Object.values(mapping) })
      this.btnLoading = false
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer{
  margin: 10px 0px 0px;
  display: flex;
  justify-content: flex-end;
}
</style>
<style lang="less">
.dashboard-table .vxe-body--column.row-blue {
  color: #5098FF;
  cursor: pointer;
}
.dashboard-table .vxe-body--column.row-red {
  color: red;
}
</style>
