<template>
   <vxe-grid
    border
    resizable
    keep-source
    show-overflow
    ref="inventoryGrid"
    id="inventory_grid"
    align="center"
    height="500"
    :loading="loading"
    :custom-config="tableCustom"
    :data="gridList"
    :columns="columns"
    :toolbar-config="tableToolbar"
    :edit-config="{trigger: 'click', mode: 'row', showStatus: true}">
    <template v-slot:toolbar_buttons>
      <slot></slot>
    </template>
    <template v-slot:original_batch_post_time="{ row }">
        {{ formatTime(row.originalBatchPostTime) }}
    </template>
    <template v-slot:po_no="{ row }">
      <el-link type="primary" @click="toDetail(row)">{{ row.poNo }}</el-link>
    </template>
  </vxe-grid>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'

const columns = [
  {

    field: 'factory',
    title: '工厂',
    width: 250
  },
  {
    field: 'sku',
    title: 'SKU编码',
    width: 100
  },
  {
    field: 'materielName',
    title: '物料描述',
    width: 250
  },
  {
    field: 'inventoryId',
    title: '库存类型',
    width: 150
  },
  {
    field: 'position',
    title: '仓库地点',
    width: 150
  },
  {
    field: 'frozenPosition',
    title: '冻结仓库地点',
    width: 150
  },
  {
    field: 'firstBatch',
    title: 'SAP批次',
    width: 100

  },
  {
    field: 'batch',
    title: '坤合批次',
    width: 100

  },
  {
    field: 'supplierBatch',
    title: '供应商批次',
    width: 80
  },
  {
    field: 'supplierCode',
    title: '供应商编号',
    width: 80
  },
  {
    field: 'supplierName',
    title: '供应商名称',
    width: 200
  },
  {
    field: 'poNo',
    title: '采购订单号',
    width: 100,
    slots: {
      default: 'po_no'
    }
  },
  {
    field: 'poItemNo',
    title: '订单行',
    width: 80
  },
  {
    field: 'taxedPrice',
    title: '含税采购价格',
    width: 100
  },
  {
    field: 'priceTimes',
    title: '价格倍数',
    width: 100
  },
  {
    field: 'inputTax',
    title: '进项税',
    width: 100
  },
  {
    field: 'qty',
    title: '批次数量',
    width: 80
  },
  {
    field: 'frozenQty',
    title: '冻结数量',
    width: 80
  },
  {
    field: 'unit',
    title: '单位',
    width: 80
  },

  {
    field: 'inventoryAvailableQty',
    title: '在库可用',
    width: 120,
    titleHelp: { message: `E库存：在库-DN占用
W库存：在库(sku+工厂+客户)-DN占用-ATP占用
K库存或自营库存：在库(sku+工厂+仓位)-DN占用-申请单占用-ATP占用` }
  },
  {
    field: 'inTransitQty',
    title: '在途可用',
    width: 120,
    titleHelp: { message: `在途可用提示：
K库存或自营库存：在途(sku+工厂+仓位)-ATP占用` }

  },

  {

    field: 'inventoryValue',
    title: '库存价值',
    width: 80
  },
  {

    field: 'currency',
    title: '币别',
    width: 80
  },
  {

    field: 'discount',
    title: '折扣率',
    width: 80
  },
  {

    field: 'discountValue',
    title: '折扣价值',
    width: 80
  },
  {
    field: 'produceTime',
    title: '生产日期',
    width: 120
  },
  {
    field: 'invalidTime',
    title: '失效日期',
    width: 120

  },
  {
    field: 'warehousingTime',
    title: '入库日期',
    width: 120

  },
  {
    field: 'effectLeftDays',
    title: '剩余天数',
    width: 80

  },
  {

    field: 'warehusingDays',
    title: '库龄天数',
    width: 80
  },
  {

    field: 'qualityDescCode',
    title: '质量描述',
    width: 80
  },
  {

    field: 'slowMovingReason',
    title: '呆滞原因',
    width: 80
  },

  {
    field: 'supplierName',
    title: '供应商',
    width: 200
  },
  {
    field: 'consignmentSupplierName',
    title: '寄售供应商',
    width: 200
  },
  {
    field: 'consignmentCustomerName',
    title: '寄售客户',
    width: 100
  },
  {
    field: 'demandNo',
    title: '销售订单号',
    width: 80
  },
  {
    field: 'demandItem',
    title: '销售单行号',
    width: 80
  },
  {
    field: 'statusMsg',
    title: '启用状态',
    width: 80
  },
  {

    field: 'mstaeStateMsg',
    title: '售完即止',
    width: 80
  },
  {

    field: 'materialGroupName',
    title: '物料组',
    width: 150

  },
  {
    field: 'buyerName',
    title: '采购员',
    width: 80
  },

  {
    field: 'commoditySourceTypeMsg',
    title: 'VPI物料',
    width: 80
  },
  {
    field: 'onGwMsg',
    title: '是否上官网',
    width: 80
  },
  {
    field: 'brandName',
    title: '品牌',
    width: 150
  },
  {
    field: 'originalBatch',
    title: '最早原始批次',
    width: 150
  },
  {
    field: 'originalBatchPostTime',
    title: '原始批次入库时间',
    width: 150,
    slots: {
      default: 'original_batch_post_time'
    }
  },
  {
    field: 'originalBatchWarehousingDays',
    title: '原始批次库龄',
    width: 150
  },
  {
    field: 'originalBatchPoNo',
    title: '原始批次PO',
    width: 150
  },
  {
    field: 'originalBatchPoIfStrategy',
    title: '原始批次PO是否战略',
    width: 150
  },
  {
    field: 'originalBatchDetailInfo',
    title: '原始批次详情',
    width: 150
  }
]
export default {
  data () {
    return {
      columns,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      time: moment().format('YYYY-MM-DD'),
      gridList: []
    }
  },
  props: {
    list: {
      default: () => []
    },
    inventoryTypes: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    list: {
      deep: true,
      handler(val) {
        if (val) {
          this.gridList = val
        }
      }
    }

  },
  computed: {
    ...mapState({
      dictList: (state) => state.orderPurchase.dictList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      purchaseGroupList: (state) => state.orderPurchase.purchaseList,
      searchMaterialGroupList: (state) => state.orderPurchase.searchMaterialGroupList

    })
  },
  methods: {
    formatTime(time) {
      return time ? moment(time).format('YYYY-MM-DD') : ''
    },
    toDetail(row) {
      this.$router.push(`/orderPurchase/detail/${row.poNo}`)
    }
  },
  mounted() {
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    if (!this.purchaseGroupList || this.purchaseGroupList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    if (!this.searchMaterialGroupList || this.searchMaterialGroupList.length === 0) {
      this.$store.dispatch('orderPurchase/getSearchMaterialGroup')
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
