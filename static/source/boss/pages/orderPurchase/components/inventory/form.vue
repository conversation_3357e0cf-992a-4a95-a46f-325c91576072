<template>
  <div class="inventory-form">
    <el-form
      :model="searchForm"
      ref="searchForm"
      style="width: 100%"
      label-suffix=":"
      :rules="rules"
      label-width="120px"
    >
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="SKU编码" prop="skuNos">
            <el-input
              v-model="searchForm.skuNos"
              clearable
              placeholder="多个sku用空格隔开，最多500个"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工厂" prop="factory">
            <el-select
              v-model="searchForm.factory"
              filterable
              multiple
              collapse-tags
              default-first-option
              clearable
              style="width:100%"
            >
              <el-option
                v-for="(item,index) in factoryList"
                :key="index"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库地点" prop="position">
            <AllowCreateSelect :data.sync="searchForm.position" @change="val=>searchForm.position=val" :optionLists="storeWarehouseListFilterByFactory"  type="warehouse"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="库存类型" prop="inventoryId">
            <el-select
              multiple
              collapse-tags
              v-model="searchForm.inventoryId"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in inventoryTypes"
                :key="item.key"
                :label="item.value"
                :value="item.key"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SAP批次" prop="firstBatch">
            <el-input
              v-model="searchForm.firstBatch"
              clearable
              placeholder="SAP批次"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商批次" prop="supplierBatch">
            <el-input
              v-model="searchForm.supplierBatch"
              clearable
              placeholder="供应商批次"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
         <el-col :span="8">
          <el-form-item label="寄售供应商" prop="consignmentSupplier">
            <SelectSupplier
              collapse-tags
              multiple
              :data.sync="searchForm.consignmentSupplier"
              @change="(val) => (searchForm.consignmentSupplier = val)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购供应商" prop="poSupplierCode ">
            <SelectSupplier
              collapse-tags
              multiple
              :data.sync="searchForm.poSupplierCode"
              @change="(val) => (searchForm.poSupplierCode = val)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生产日期" clearable prop="productionTime">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.productionTime"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始"
              end-placeholder="结束"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
         <el-col :span="8">
          <el-form-item label="失效日期" clearable prop="invalidTime">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.invalidTime"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始"
              end-placeholder="结束"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="入库日期" clearable prop="inStockTime">
            <el-date-picker
              style="width:100%"
              v-model="searchForm.inStockTime"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始"
              end-placeholder="结束"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="寄售客户" prop="consignmentCustomer">
            <RemoteCustomer
              collapse-tags
              multiple
              v-model="searchForm.consignmentCustomer"
              :style="{ width: '100%' }"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="采购姓名" prop="buyerEmailList">
            <el-select
              collapse-tags
              multiple
              filterable
              default-first-option
              v-model="searchForm.buyerEmailList"
              placeholder="请选择"
              style="width:100%"
              clearable
            >
              <el-option
                v-for="item in purchaseGroupList"
                :key="item.id"
                :label="item.groupCode + ' ' + item.userName"
                :value="item.email"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料组" prop="materialGroup">
            <MaterialGroup
              collapse-tags
              multiple
              v-model="searchForm.materialGroup"
              :style="{ width: '100%' }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="品牌" prop="brandId">
            <RemoteBrand
              collapse-tags
              multiple
              v-model="searchForm.brandId"
              :style="{ width: '100%' }"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
          <el-col :span="8">
          <el-row>
            <el-col :span="8">
              <el-form-item label="剩余天数" prop="leftStartDays" :class="{'is-error':leftDayStartErr}" >
                <el-input-number
                   size="mini"
                    style="width:120px"
                     v-model="searchForm.leftStartDays"
                  @blur="leftStartDaysBlur"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="leftEndDays" :class="{'is-error':leftDayEndErr}">
                <el-input-number
                 size="mini"
                  style="width:120px"
                  v-model="searchForm.leftEndDays"
                  @blur="leftEndDaysBlur"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
        <span v-if="leftDayStartErr||leftDayEndErr" class="el-form-item__error" style="position:absolute;left:120px;top:32px">结束剩余天数应大于开始剩余天数</span>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8">
              <el-form-item label="库龄天数" prop="startStockAge">
                <el-input-number
                  size="mini"
                  style="width:120px"
                  v-model="searchForm.startStockAge"
                  @blur="startStockAgeBlur"
                  :min="0"
                  label="描述文字"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="endStockAge">
                <el-input-number
                  size="mini"
                  style="width:120px"
                  v-model="searchForm.endStockAge"
                  @blur="endStockAgeBlur"
                  :min="0"
                ></el-input-number>
              </el-form-item>
            </el-col>
           <span v-if="stockAgeStartErr||stockAgeEndErr" class="el-form-item__error" style="position:absolute;left:120px;top:32px">结束剩余天数应大于开始剩余天数</span>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-form-item label="物料描述" prop="materielName">
            <el-input
              v-model="searchForm.materielName"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="冻结仓库地点" prop="frozenPositions">
            <AllowCreateSelect :data.sync="searchForm.frozenPositions" @change="val=>searchForm.frozenPositions=val" :optionLists="storeWarehouseListFilterByFactory"  type="warehouse"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="战略备货" prop="poStrategy">
            <el-select
              v-model="searchForm.poStrategy"
              placeholder="请选择"
              style="width:100%"
              clearable
            >
              <el-option :value="true" label="战略备货"></el-option>
              <el-option :value="false" label="非战略备货"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <div style="display:inline-block;margin-left:120px">
          <slot></slot>
        </div>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import RemoteCustomer from '@/components/SearchFields/customer';
import RemoteBrand from '@/components/SearchFields/brand';

import MaterialGroup from '@/components/SearchFields/materialGroup'
import { deepClone } from '@/utils/index.js'
import { getUserCompany } from '@/utils/mm'
import AllowCreateSelect from '@/pages/orderPurchase/components/common/MultipleSelect'
export default {
  data() {
    return {
      searchForm: {
        skuNos: '',
        factory: [],
        frozenPositions: '',
        position: '',
        batch: '',
        // 库存类型
        inventoryId: null,
        supplierBatch: '',
        consignmentSupplier: '',
        poSupplierCode: '',
        productionStartDate: '',
        productionEndDate: '',
        productionTime: '',
        invalidStartDate: '',
        invalidEndDate: '',
        invalidTime: '',
        inStockStartDate: '',
        inStockEndDate: '',
        inStockTime: '',
        // 寄售客户
        consignmentCustomer: '',
        // 采购姓名 purchaseGroup
        buyerEmailList: '',
        materialGroup: '',
        brandId: '',
        leftStartDays: undefined,
        leftEndDays: undefined,
        // 库龄天数
        startStockAge: undefined,
        endStockAge: undefined,
        materielName: ''

      },
      rules: {
        factory: [
          {
            required: true,
            message: '请输入',
            trigger: 'change'
          }
        ]
      },
      warehouseListFilterByFactory: [],
      leftDayStartErr: false,
      leftDayEndErr: false,
      stockAgeStartErr: false,
      stockAgeEndErr: false
    }
  },
  props: {
    inventoryTypes: {
      type: Array,
      default: () => []
    }
  },
  components: { SelectSupplier, RemoteCustomer, MaterialGroup, RemoteBrand, AllowCreateSelect },
  computed: {
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      factoryList: (state) => state.orderPurchase.factoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList,
      purchaseGroupList: (state) => state.orderPurchase.purchaseList
      // storeWarehouseListFilterByFactory: (state) => state.orderPurchase.storeWarehouseListFilterByFactory
    }),
    copySearchForm() {
      this.clean(this.searchForm)
      const cloneSearchForm = deepClone(this.searchForm)
      let processdSearchForm = {}
      for (var k in cloneSearchForm) {
        if (k === 'skuNos') {
          processdSearchForm[k] = cloneSearchForm[k]
            .split(/\s|,|;|，|；/)
            .filter(function(s) {
              return s && s.trim()
            })
        } else if (k === 'productionTime') {
          processdSearchForm.productionStartDate =
            cloneSearchForm.productionTime[0]
          processdSearchForm.productionEndDate =
            cloneSearchForm.productionTime[1]
          delete cloneSearchForm.productionTime
        } else if (k === 'invalidTime') {
          processdSearchForm.invalidStartDate = cloneSearchForm.invalidTime[0]
          processdSearchForm.invalidEndDate = cloneSearchForm.invalidTime[1]
          delete cloneSearchForm.invalidDate
        } else if (k === 'inStockTime') {
          processdSearchForm.inStockStartDate = cloneSearchForm.inStockTime[0]
          processdSearchForm.inStockEndDate = cloneSearchForm.inStockTime[1]
          delete cloneSearchForm.inStockTime
        } else if (k === 'consignmentSupplier') {
          let supplierNoArr = []
          cloneSearchForm[k].forEach(item => supplierNoArr.push(item.supplierNo))
          processdSearchForm[k] = supplierNoArr
        } else if (k === 'poSupplierCode') {
          let supplierNoArr = []
          cloneSearchForm[k].forEach(item => supplierNoArr.push(item.supplierNo))
          processdSearchForm[k] = supplierNoArr
        } else if (k === 'position' || k === 'frozenPositions') {
          // let uniquePositionId = []
          // uniquePositionId = cloneSearchForm[k].map(item => { item = item.split('_')[1]; return item })
          processdSearchForm[k] = [...new Set(cloneSearchForm[k])]
        } else {
          processdSearchForm[k] = cloneSearchForm[k]
        }
      }
      return processdSearchForm
    },
    storeWarehouseListFilterByFactory() {
      const factoryCodeArr = this.searchForm.factory
      let list = [];
      if (factoryCodeArr) {
        let tmpWarehouseListFilterByFactory = []
        factoryCodeArr.forEach(factoryCode => {
          tmpWarehouseListFilterByFactory.push(...this.warehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCode))
        })
        list = tmpWarehouseListFilterByFactory
        // this.$store.commit({ type: 'orderPurchase/SET_WAREHOUSE_FILTERBY_COMPANYCODE', warehouseListFilterByFactory: this.warehouseListFilterByFactory })
      }
      return list.map(item => {
        return {
          ...item,
          value: item.warehouseLocationCode,
          name: `【工厂${item.factoryCode}】${item.warehouseLocationCode} ${item.warehouseLocationName}`
        }
      })
    }
  },
  watch: {
    'searchForm.factory': {
      deep: true,
      handler(factoryCodeArr) {
        if (factoryCodeArr) {
          let tmpWarehouseListFilterByFactory = []
          factoryCodeArr.forEach(factoryCode => {
            tmpWarehouseListFilterByFactory.push(...this.warehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCode))
          })
          this.warehouseListFilterByFactory = tmpWarehouseListFilterByFactory
          // this.$store.commit({ type: 'orderPurchase/SET_WAREHOUSE_FILTERBY_COMPANYCODE', warehouseListFilterByFactory: this.warehouseListFilterByFactory })
        }
      }

    },
    // 初始化 其他页面初始化之后 不会走此方法
    warehouseList: {
      deep: true,
      handler(adWarehouseList) {
        if (adWarehouseList && adWarehouseList.length > 0) {
          this.searchForm.factory.forEach(factoryCode => {
            this.warehouseListFilterByFactory = [...adWarehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCode)]
            // this.$store.commit({ type: 'orderPurchase/SET_WAREHOUSE_FILTERBY_COMPANYCODE', warehouseListFilterByFactory: this.warehouseListFilterByFactory })
          })
        }
      }
    }
  },
  created() {
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    if (!this.purchaseGroupList || this.purchaseGroupList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    this.getUserCompany()
  },
  mounted() {

  },
  methods: {
    async getUserCompany() {
      const defaultCompany = await getUserCompany();
      if (defaultCompany) {
        this.searchForm.factory = [defaultCompany];
      }
    },
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    },
    leftStartDaysBlur(evt) {
      if (evt.target.value && this.searchForm.leftEndDays) {
        if (evt.target.value > this.searchForm.leftEndDays) {
          this.leftDayStartErr = true
        } else {
          this.leftDayStartErr = false
          this.leftDayEndErr = false
        }
      }
    },
    leftEndDaysBlur(evt) {
      if (evt.target.value && this.searchForm.leftStartDays) {
        if (evt.target.value < this.searchForm.leftStartDays) {
          this.leftDayEndErr = true
        } else {
          this.leftDayStartErr = false
          this.leftDayEndErr = false
        }
      }
    },
    startStockAgeBlur(evt) {
      if (evt.target.value && this.searchForm.endStockAge) {
        if (evt.target.value > this.searchForm.endStockAge) {
          this.stockAgeStartErr = true
        } else {
          this.stockAgeStartErr = false
          this.stockAgeEndErr = false
        }
      }
    },
    endStockAgeBlur(evt) {
      if (evt.target.value && this.searchForm.startStockAge) {
        if (evt.target.value < this.searchForm.startStockAge) {
          this.stockAgeEndErr = true
        } else {
          this.stockAgeStartErr = false
          this.stockAgeEndErr = false
        }
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.inline-block {
  position: absolute;
  // float: left;
  // display: inline-block;
  width: 100px;
}
.posotion {
  left: 100px;
  margin-left: 20px;
}
</style>
