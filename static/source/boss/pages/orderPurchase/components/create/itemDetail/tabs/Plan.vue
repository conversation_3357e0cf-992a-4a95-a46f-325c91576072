<template>
  <el-form ref="purchasePlan">
    <el-table
      :data="planData"
      style="width: 100%"
    >
      <el-table-column
        v-for="item in field"
        :key="item.prop"
        :label="item.name"
        width="180"
        align="center"
      >
        <template slot="header">
          <span v-bind:class="{required: item.required===true}" >{{item.name}}</span>
        </template>
        <template slot-scope="{row}">
          <el-date-picker
            v-if="item.type=='date'"
            v-model="row[item.prop]"
            size="mini"
            type="date"
            style="width:100%"
            :picker-options="dateOptions"
            placeholder="选择日期"
            value-format="yyyy-MM-dd"
          />
          <el-input-number
            v-else-if="item.prop==='deliveryQuantity'"
            v-model="row[item.prop]"
            style="width:100%"
            size="mini"
            :placeholder="item.name"
            :min="0"
            :precision="3"
            :step="1"
          />
          <el-input
            v-else-if="item.type==='input'"
            v-model="row[item.prop]"
            :placeholder="item.name"
            style="width:100%"
            size="mini"
          />
          <span v-else>{{row[item.prop]}}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        fixed="right"
      >
        <template slot-scope="{$index}">
          <el-button
            v-show="planData.length > 1"
            type="text" size="mini" @click="handleRemove($index)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="center">
      <el-button
        size="mini"
        style="width:100%;margin:5px 0"
        v-if="showAddLine"
        @click="handleAdd"
      >
        <i class="el-icon-circle-plus" />新增计划行
      </el-button>
    </el-row>
  </el-form>
</template>

<script>
import moment from 'moment'
import padStart from 'lodash/padStart'

export default {
  name: 'purchaseOrderItemPlan',
  data () {
    let planList = (this.data || {}).planList || []
    if (!planList || planList.length === 0) {
      const { itemDeliveryDate, itemQuantity } = this.data
      planList = [
        {
          deliveryDate: itemDeliveryDate,
          deliveryQuantity: itemQuantity,
          planNo: this.getItemNo()
        }
      ]
    }
    planList = JSON.parse(JSON.stringify(planList))
    const { createTime } = this.purchaseData
    const newCreateTime = moment(createTime).subtract(1, 'days').format('YYYY-MM-DD')
    return {
      planData: planList || [],
      dateOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(newCreateTime)).getTime()
        }
      }
    }
  },
  props: [
    'field', 'data', 'purchaseData'
  ],
  computed: {
    showAddLine() {
      const { orderType } = this.purchaseData
      const hideList = ['Z006', 'Z007']
      if (hideList.includes(orderType)) return false
      return true
    }
  },
  methods: {
    handleAdd () {
      if (!this.planData) {
        this.planData = []
      }
      this.planData.push({
        planNo: this.getItemNo()
      })
    },
    handleRemove (idx) {
      this.planData.splice(idx, 1)
    },
    getItemNo () {
      const len = (this.planData || []).length
      let no = 0
      if (len > 0) {
        const { planNo = 0 } = this.planData[len - 1]
        if (planNo) {
          no = planNo
        }
      }
      const numItemNo = Number(no) + 1
      return padStart(numItemNo, 4, '0')
    },
    submit (formName) {
      return {
        planData: this.planData
      }
      // this.$emit('submit', this.planData)
    }
  }
}
</script>

<style lang="scss" scoped>
.required {
  &:before {
    content: '*';
    color: #ff7268;
    margin-right: 4px;
  }
  &::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>
