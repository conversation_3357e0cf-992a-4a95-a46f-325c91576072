<template>
  <el-dialog
    title="商品详情"
    top="10px"
    :width="purchaseData.orderType === 'Z002' ? '1200px' : '860px'"
    :visible.sync="showDlg"
    :show-close="false"
    @open="openDialog"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane label="详细信息" name="detail">
        <Detail :data="data" :key="key1" :field="fieldDetail" ref="detail"/>
      </el-tab-pane>
      <el-tab-pane label="计划行" name="plan">
        <Plan :data="data" :key="key2" :field="fieldPlan" ref="plan" :purchaseData="purchaseData"/>
      </el-tab-pane>
      <el-tab-pane label="价费信息" name="price" v-if="fieldPrice.length>0 && showCharge">
        <Price :data="data" :purchaseData="purchaseData" :key="key3" :field="fieldPrice" ref="price"/>
      </el-tab-pane>
      <el-tab-pane label="委外组件" name="entrust" v-if="fieldEntrust.length>0">
        <Entrust :data="data" :key="key4" :field="fieldEntrust" ref="entrust" :purchaseData="purchaseData" :KHWarehouseList="KHWarehouseList"/>
      </el-tab-pane>
      <el-tab-pane label="固定资产详情" name="assets" v-if="fieldAssets.length>0">
        <Assets :data="data" :key="key5" :field="fieldAssets" ref="assets"/>
      </el-tab-pane>
    </el-tabs>
    <el-row type="flex" justify="center" class="btn-row">
      <el-button type="primary" @click="handleSubmit">确定</el-button>
      <el-button @click="closeDialog">取消</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import * as shoirtid from 'shortid'
import Detail from './tabs/Detail'
import Plan from './tabs/Plan'
import Entrust from './tabs/Entrust'
import Price from './tabs/Price'
import Assets from './tabs/Assets'
import { safeRun } from '@/utils/index'
import { sameTimeValid } from '@/utils/poPriceCalculate'

export default {
  data () {
    return {
      purchaseDetail: {},
      key1: shoirtid.generate(),
      key2: shoirtid.generate(),
      key3: shoirtid.generate(),
      key4: shoirtid.generate(),
      key5: shoirtid.generate(),
      activeName: 'detail',
      rules: {}
    }
  },
  components: { Detail, Plan, Entrust, Price, Assets },
  props: [
    'purchaseData', 'data', 'showDialog', 'itemField', 'showCharge', 'KHWarehouseList'
  ],
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    fieldDetail () {
      return this.getFieldsByCategory('detailInfo')
    },
    fieldPlan () {
      return this.getFieldsByCategory('detailPlan')
    },
    fieldPrice () {
      const priceFields = this.getFieldsByCategory('detailPrice') || []
      return priceFields
    },
    fieldEntrust () {
      const entrustFields = this.getFieldsByCategory('detailEntrust') || []
      const entrustTableFields = this.getFieldsByCategory('detailEntrustTable') || []
      return [...entrustFields, ...entrustTableFields]
    },
    fieldAssets () {
      const category = this.purchaseData.orderType === 'Z004' ? 'detailAsset' : 'detailAssets'
      const assetsFields = this.getFieldsByCategory(category) || []
      return assetsFields
    }
  },
  methods: {
    getFieldsByCategory (category) {
      const fieldList = this.itemField.filter(item => item.category === category && (!item.status || /create/.test(item.status)))
      return fieldList || []
    },
    openDialog () {
      this.key1 = shoirtid.generate()
      this.key2 = shoirtid.generate()
      this.key3 = shoirtid.generate()
      this.key4 = shoirtid.generate()
      this.key5 = shoirtid.generate()
    },
    getFormPromise (form) {
      return new Promise(resolve => {
        form.validate(res => {
          resolve(res)
        })
      })
    },
    validateRefsForm (detail, plan) {
      let ret = true
      detail.validate((valid) => {
        if (!valid) {
          ret = false
          return
        }
        plan.validate((valid) => {
          if (!valid) {
            ret = false
          }
        })
      })
      return ret
    },
    validateSubmit (comp, type) {
      let ret = true
      safeRun(() => {
        if (type === 'plan' && comp.planData.some(plan => !plan.prOrderNo && plan.prOrderItemNo)) {
          ret = false
          this.$message.error('当填写采购申请行号时，采购申请单号不能为空！')
        }
      })
      safeRun(() => {
        if (type === 'comp' && comp.componentList.some(comp => !comp.componentSkuNo || !comp.componentRequiredQuantity || !comp.componentWarehouseLocation)) {
          ret = false
          this.$message.error('委外组件SKU编码、组件数量、库存地点不能为空！')
        }
      })
      return ret
    },
    validatePlanAndAsset (plan, assets) {
      let ret = true
      safeRun(() => {
        const planNum = plan.planData.reduce((x, y) => Number(x) + Number(y.deliveryQuantity), 0)
        const assetsNum = assets.assetsData.reduce((x, y) => Number(x) + Number(y.fixedAssetsCardQuantity), 0)
        console.log(planNum, assetsNum)
        if (assetsNum !== planNum) {
          ret = false
          return this.$message.error('固定资产详情数量之和应该等于计划行数量之和！')
        }
        if (assets.assetsData.some(asset => !asset.fixedAssetsCardNo)) {
          ret = false
          return this.$message.error('请输入资产卡片号！')
        }
      }, null, true)
      return ret
    },
    validateQuantity () {
      const { orderType, fixedAssetsList = [], planList = [] } = this.purchaseData
      let ret = true
      if (orderType === 'Z007') {
        if (fixedAssetsList && planList) {
          const count1 = fixedAssetsList.reduce((total, item) => total + item.fixedAssetsCardQuantity, 0)
          const count2 = planList.reduce((total, item) => total + item.deliveryQuantity, 0)
          if (count1 !== count2) {
            this.$message.error('计划行计划数量之和应等于固定资产行数量之和！')
            ret = false
          }
        }
      }
      return ret
    },
    validateDeliveryDate (comp) {
      if (!comp) return false
      const planList = comp.planData
      let ret = true
      if (planList.length) {
        if (planList.some(plan => (!plan.deliveryDate || !plan.deliveryQuantity) && (plan.deliveryQuantity !== 0))) {
          this.$message.error('交货日期和计划行数量必填！')
          ret = false
        }
      }
      return ret
    },
    mixedValue(rest) {
      let ret = false
      if (rest.some(item => item == null || item === '') && rest.some(item => item)) {
        ret = true
      }
      return ret
    },
    validateSpecial (comp) {
      let ret = true
      const detail = comp.purchaseDetailData || {}
      if (!sameTimeValid(detail, 'specialSupplySo', 'specialSupplySoItem')) {
        ret = false
        this.$message.error('专供销售单和专供销售单行号要么均填写，要么均不填写！')
      }
      return ret
    },
    validateOA (comp) {
      let ret = true
      // FYW-WZ：物资      FYW-FW：服务
      // 服务的 行号可以不填，物资的行号校验必填就好
      const detail = comp.purchaseDetailData || {}
      if (detail.oaNo && detail.oaNo.indexOf('FYW-WZ') === 0 && !sameTimeValid(detail, 'oaNo', 'oaItemNo')) {
        ret = false
        this.$message.error('OA流程编号、OA行号要么均填写，要么均不填写！')
      }
      return ret
    },
    validatePrice (comp) {
      const priceData = comp.purchasePrice
      const chargeProps = ['tariff', 'saleTax', 'latePayment', 'other', 'premium']
      const chargePropsWithType = ['customsFee', 'intlShipping']

      if (!chargeProps.every(item => {
        // 税目,币别,供应商应该同时存在或同时不存在
        const valueList = [item + 'Amount', item + 'Currency', item + 'SupplierNo']
        console.log(valueList)
        return sameTimeValid(priceData, ...valueList)
      }
      )) {
        this.$message.error('金额和币别类型、供应商必须同时输入！')
        return false
      }
      if (!chargePropsWithType.every(item => {
        // 税目,币别,供应商应该同时存在或同时不存在
        const valueList = [item + 'Amount', item + 'Currency', item + 'SupplierNo']
        if (this.purchaseData.orderType === 'Z002') {
          valueList.push(item + 'Type')
        }
        console.log(valueList)
        return sameTimeValid(priceData, ...valueList)
      }
      )) {
        this.$message.error('费用类别、金额、币别类型、供应商必须同时输入！')
        return false
      }

      if ([...chargeProps, ...chargePropsWithType].some(item => {
        const currency = this.purchaseData[item + 'Currency']
        return currency && currency !== priceData[item + 'Currency'] &&
          (priceData[item + 'Currency'] && priceData[item + 'Amount'] != null && priceData[item + 'SupplierNo'])
      }
      )) {
        this.$message.error('订单头上币别类型必须和行上币别类型保持一致！')
        return false
      }
      return true
    },
    async handleSubmit () {
      const { orderType } = this.purchaseData
      if (!this.validateRefsForm(this.$refs.detail.$refs['purchaseDetail'], this.$refs.plan.$refs['purchasePlan'])) {
        // 校验详情，计划行必填项
        return this.$message.error('请填写必填项！')
      }
      if (orderType === 'Z007') {
        // 固定资产详情数量之和应该等于计划行数量之和！
        if (!this.validatePlanAndAsset(this.$refs.plan, this.$refs.assets)) return
      }
      if (!this.validateSubmit(this.$refs.plan, 'plan')) return
      // 计划行当填写采购申请行号时，采购申请单号不能为空！
      if (orderType === 'Z006') {
        // 委外组件SKU编码、组件数量、库存地点不能为空！
        if (!this.validateSubmit(this.$refs.entrust, 'comp')) return
      }
      if (!this.validateQuantity()) return
      // Z007 计划行计划数量之和应等于固定资产行数量之和！

      if (!this.validateSpecial(this.$refs.detail)) return
      // 专供销售单号和专供销售单行号要么均填写，要么均不填写

      if (!this.validateOA(this.$refs.detail)) return
      // OA相关要么均填写，要么均不填写

      if (!this.validateDeliveryDate(this.$refs.plan)) return
      // 计划行交货日期和计划行数量必填！
      if (orderType !== 'Z003' && this.$refs.price) {
        if (!this.validatePrice(this.$refs.price)) return
        // 订单头上币别类型必须和行上币别类型保持一致！
        // 金额和币别类型、供应商必须同时输入！
      }
      this.$emit('update:showDialog', false)
      const pArr = []
      if (this.$refs.detail) {
        const detailForm = this.$refs.detail.$refs['purchaseDetail']
        pArr.push(detailForm)
      }
      if (this.$refs.price) {
        const priceForm = this.$refs.price.$refs['purchasePrice']
        pArr.push(priceForm)
      }
      const res = await Promise.all(pArr.map(this.getFormPromise))
      const validateResult = res.every(item => !!item) || !res.length
      if (validateResult) {
        let res = {}
        let typeArr = ['detail']
        res = {
          ...res,
          ...this.$refs.detail.submit('purchaseDetail')
        }
        if (this.$refs.price) {
          res = {
            ...res,
            ...this.$refs.price.submit('purchasePrice')
          }
          typeArr.push('price')
        }
        if (this.$refs.assets) {
          res = {
            ...res,
            ...this.$refs.assets.submit('purchaseAssets')
          }
          typeArr.push('assets')
        }
        if (this.$refs.entrust) {
          res = {
            ...res,
            ...this.$refs.entrust.submit('purchaseEntrust')
          }
          typeArr.push('entrust')
        }
        // if (this.$refs.customInstruction) {
        //   res = {
        //     ...res,
        //     ...this.$refs.customInstruction.submit('customInstruction')
        //   }
        //   typeArr.push('customInstructions')
        // }
        res = {
          ...res,
          ...this.$refs.plan.submit('purchasePlan')
        }
        typeArr.push('plan')
        this.$emit('updateItem', {
          purchaseData: this.data,
          data: res,
          updateTypes: typeArr
        })
        return true
      }
    },
    closeDialog () {
      this.$emit('update:showDialog', false)
      if (this.$refs.detail) {
        this.$refs.detail.$refs['purchaseDetail'].resetFields()
      }
      if (this.$refs.plan) {
        this.$refs.plan.$refs['purchasePlan'].resetFields()
      }
      if (this.$refs.price) {
        this.$refs.price.$refs['purchasePrice'].resetFields()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-row{
  margin-top: 20px;
}
</style>
