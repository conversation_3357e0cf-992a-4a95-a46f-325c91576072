<template>
  <el-form :model="purchasePrice" ref="purchasePrice" label-width="120px" label-suffix=":" :rules="rules">
    <el-row :gutter="20" type="flex">
      <el-col v-for="childItem in field" :key="childItem.name" :span="childItem.span || 8">
        <el-form-item :label="childItem.name" :label-width="childItem.width ? childItem.width + 'px' : '120px'" :prop="childItem.prop">
          <el-input-number
            :disabled="disableCharge"
            v-if="childItem.type==='number'"
            v-model="purchasePrice[childItem.prop]"
            clearable
            :placeholder="childItem.name"
            :precision="2"
            :min="0"
            style="width:100%"
          />
          <el-select
            :disabled="disableCharge"
            v-if="childItem.type==='select'"
            v-model="purchasePrice[childItem.prop]"
            style="width:100%"
            clearable
            filterable
          >
            <el-option
              v-for="item in dictList[childItem.enums]"
              :key="item.value"
              :label="item.value+' '+item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <SelectSupplierNo
            :disabled="disableCharge"
            v-if="childItem.type==='custom'"
            v-model="purchasePrice[childItem.prop]"
            @change="(item,itemList)=>handleChangeSupplier(childItem.prop,item,itemList)"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import SelectSupplierNo from '@/pages/orderPurchase/components/common/SelectSupplierNo'

export default {
  name: 'purchaseOrderItemPrice',
  data () {
    let data = { ...this.data }
    data = JSON.parse(JSON.stringify(data))
    let props = [ 'tariffAmount', 'saleTaxAmount', 'intlShippingAmount', 'latePaymentAmount', 'customsFeeAmount', 'otherAmount', 'premiumAmount' ]
    for (let prop of props) {
      if (!data[prop]) {
        data[prop] = undefined
      }
    }
    if (data.intlShippingType) {
      data.intlShippingType = Number(data.intlShippingType)
    }
    if (data.customsFeeType) {
      data.customsFeeType = Number(data.customsFeeType)
    }
    return {
      rules: {},
      purchasePrice: data
    }
  },
  components: {
    SelectSupplierNo
  },
  props: [
    'field', 'data', 'purchaseData'
  ],
  computed: {
    disableCharge() {
      return this.purchaseData.isOverchargeFree === 1
    },
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    })
  },
  methods: {
    // parseVal(value) {
    //   if (!isNaN(value)) return Number(value)
    //   return value
    // },
    submit (formName) {
      console.log('this.purchasePrice', this.purchasePrice);
      return {
        priceData: this.purchasePrice
      }
      // this.$emit('submit', this.purchasePrice)
    },
    handleChangeSupplier (prop, val, valList) {
      this.purchasePrice[prop] = val
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-row--flex{
  flex-wrap: wrap;
}
</style>
