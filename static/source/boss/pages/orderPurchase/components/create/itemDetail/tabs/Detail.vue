<template>
  <el-form :model="purchaseDetailData" ref="purchaseDetail" label-width="125px" label-suffix=":" :rules="rules">
    <el-row :gutter="20">
      <el-col v-for="childItem in computedField" :key="childItem.name" :span="childItem.span || 12">
        <el-form-item :label="childItem.name" :prop="childItem.prop">
          <el-input v-if="childItem.type === 'input'" v-model="purchaseDetailData[childItem.prop]" clearable
            :disabled="disabledBySpecial(childItem.prop)" :maxlength="childItem.length || 35" :placeholder="childItem.name"
            style="width:100%" />
          <el-input v-else-if="childItem.type === 'textarea'" type="textarea" :rows="3" placeholder="请输入内容"
            :maxlength="childItem.length || 200" v-model="purchaseDetailData[childItem.prop]" style="width:100%"
            show-word-limit />
          <el-input-number v-else-if="childItem.type === 'number'" v-model="purchaseDetailData[childItem.prop]" clearable
            :placeholder="childItem.name" :precision="0" :min="0" :max="999" style="width:100%" />
          <el-checkbox v-else-if="childItem.type === 'checkbox'" v-model="purchaseDetailData[childItem.prop]" true-label="1"
            false-label="0" />
          <el-date-picker v-else-if="childItem.type === 'date'" v-model="purchaseDetailData[childItem.prop]" type="date" />
          <el-select v-else-if="childItem.type === 'select'" style="width:100%;max-height: 50px;max-width: 275px;"
            v-model="purchaseDetailData[childItem.prop]" filterable default-first-option clearable>
            <el-option v-for="item in dictList[childItem.enums]" :key="item.value" :label="item.name" :value="item.value">
            </el-option>
          </el-select>
          <span v-else>{{ purchaseDetailData[childItem.prop] }}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <el-form-item label="订单单位 — 价格单位" label-width=“200px”>
          <el-input placeholder="订单单位" :value="1" style="width:200px" disabled>
            <template slot="append">{{ purchaseDetailData.unitName }}</template>
          </el-input>
          -
          <el-input placeholder="价格单位" :value="1" style="width:200px" disabled>
            <template slot="append">{{ purchaseDetailData.unitName }}</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <el-form-item label="库存单位 — 订单单位" label-width=“200px”>
          <el-input placeholder="库存单位" :value="purchaseDetailData.inventoryUnitMole || 1" style="width:200px" disabled>
            <template slot="append">{{ getUnitName(purchaseDetailData.inventoryUnitCode) }}</template>
          </el-input>
          -
          <el-input placeholder="订单单位" :value="purchaseDetailData.inventoryUnitDeno || 1" style="width:200px" disabled>
            <template slot="append">{{ purchaseDetailData.unitName }}</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'purchaseOrderItemDetail',
  data() {
    return {
      purchaseDetailData: { ...this.data },
      rules: {
        materialDescription: [
          { required: true, message: '物料描述必填', trigger: 'blur' }
        ]
      }
    }
  },
  props: [
    'field', 'data'
  ],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList || {}
    }),
    computedField() {
      // 不想每个类型添加一遍field，所以在这里直接concat了
      return this.field
        .concat([
          { 'id': 6061, 'name': '商品行杂费', 'prop': 'sundryAmount', 'type': 'text', 'width': 0, 'span': 0, 'category': 'detailInfo', 'precision': null, 'length': null, 'required': true, 'enums': null, 'disabled': false, 'sequence': 55, 'status': null },
          { 'id': 6062, 'name': '商品行返利', 'prop': 'rebateAmount', 'type': 'text', 'width': 0, 'span': 0, 'category': 'detailInfo', 'precision': null, 'length': null, 'required': true, 'enums': null, 'disabled': false, 'sequence': 56, 'status': null }
      ])
        .sort((a, b) => a.sequence - b.sequence)
    }
  },
  methods: {
    disabledBySpecial(prop) {
      const propList = ['specialSupplySo', 'specialSupplySoItem']
      const { soNo, soItemNo } = this.purchaseDetailData
      if (propList.find(item => item === prop)) {
        if (soNo || soItemNo) {
          return true
        }
      }
    },
    submit(formName) {
      return {
        detailData: this.purchaseDetailData
      }
      // this.$emit('submit', this.purchaseDetailData)
    },
    getUnitName(code) {
      const unit = (this.dictList['orderUnit'] || []).find(item => item.value === code)
      return unit ? unit.name : ''
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-col-12 {
  max-height: 50px;
}
</style>
