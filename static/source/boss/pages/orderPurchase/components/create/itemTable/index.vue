<template>
<vxe-table
  resizable
  border
  show-overflow
  highlight-hover-row
  :height="tableHeight"
  align="center"
  size="small"
  :scroll-y="{gt: 0}"
  :data="data.itemList"
  :checkbox-config="{checkMethod: checCheckbox}"
  @checkbox-change="onTableSelectionChange"
  @checkbox-all="onTableSelectionAllChange"
>
  <vxe-table-column class-name="checkbox-column" type="checkbox" title="" fixed="left" align="center"></vxe-table-column>
  <vxe-table-column
    v-for="col in columns"
    align="center"
    :width="col.width"
    :key="col.name"
    :field="col.name"
    :title="col.name"
  >
    <template v-slot:header>
      <span v-bind:class="{required: col.required===true}" >{{col.name}}</span>
    </template>
    <template slot-scope="{row,rowIndex}">
      <span v-if="col.prop==='skuNo'" style="display:flex;">
        <SelectSku
          :data.sync="row[col.prop]"
          :disabled="disabled || disabledReturnS(col, row)"
          @change="val=>handleSelectSku(val,row)"
        />
        <span v-if="row[col.prop]"
          @click="handleCopy(row[col.prop],$event)"
          style="display:inline-block;cursor:pointer;margin:0 5px;"
          title="点击复制SKU"
        >
          <i class="el-icon-document-copy"></i>
        </span>
      </span>
      <el-input-number
        v-else-if="col.type==='number'&&col.prop==='standardLeadTime'"
        :disabled="disabled"
        v-model="row[col.prop]"
        size="mini"
        :placeholder="col.name"
        :precision="0"
        :min="0"
        :max="999"
        @change="val=>handleStandardLeadTime(val,row)"
      />
      <el-input-number
        v-else-if="col.type==='number'&&col.prop==='priceTimes'"
        v-model="row[col.prop]"
        size="mini"
        :placeholder="col.name"
        :min="1"
        :disabled="disabled || disabledByFree(row) || disabledByOverCharge(col) || disabledReturnS(col, row)"
        :precision="0"
        @change="val=>handlePriceTimesChange(val,row)"
      />
      <template v-else-if="col.type==='number'">
        <el-input-number
          v-model="row[col.prop]"
          size="mini"
          :placeholder="col.name"
          :min="0"
          :max="(col.prop==='itemQuantity'&&isAssigned(row))?row[col.prop]: (col.prop === 'refundableAmount' && row.taxedTotalAmount ? row.taxedTotalAmount: Number.MAX_SAFE_INTEGER)"
          :disabled="
            disabled ||
            (disabledByFree(row) && col.prop!=='itemQuantity') ||
            disabledByOverCharge(col) ||
            disableByPlanList(row, col) ||
            disableByRefundableAmount(row, col) || disabledReturnS(col, row)"
          :precision="col.prop==='itemQuantity'?3:2"
          @change="(val, old)=>handleItemNumChange(val,old,col.prop,row)"
        />
        <el-tooltip v-if="showItemTip(row, col)" class="item" effect="dark" content="订单数量不满足MPQ整数倍" placement="top">
          <i style="margin-left: 3px;" class="el-icon-warning"></i>
        </el-tooltip>
      </template>
      <el-input
        v-else-if="col.prop==='materialDescription'||(col.type==='input'&&orderType === 'Z007')"
        :disabled="disabled"
        v-model="row[col.prop]"
        size="mini"
        clearable
        :placeholder="col.name"
        :maxlength="col.length||80"
        @change="val=>handleChangeInput(val,col.prop,row)"
      />
      <el-input
        v-else-if="col.type==='input'"
        v-model="row[col.prop]"
        size="mini"
        clearable
        :placeholder="col.name"
        :maxlength="smLength(col) || col.maxlength||30"
        :disabled="disabled || disabledInput(col.prop, row) || disabledBySpecial(col.prop, row) || disabledReturnS(col, row)"
        @change="val=>handleChangeInput(val,col.prop,row)"
      />
      <el-checkbox
        v-else-if="col.type==='checkbox'"
        v-model="row[col.prop]"
        :true-label="1"
        :false-label="0"
        @change="val=>handleChangeCheckbox(val,col.prop,row)"
      />
      <el-date-picker
        v-else-if="col.type==='date'"
        v-model="row[col.prop]"
        clearable
        size="mini"
        type="date"
        value-format="yyyy-MM-dd"
        placeholder="选择日期"
        style="width:100%"
        :picker-options="dateOptions"
        :disabled="disabled || disableByPlanList(row, col)"
        @change="val=>handleChangeItemDate(val,row,col)"
      />
      <el-select
        v-else-if="col.type==='select'&&(col.prop==='warehouseLocation'||col.prop==='shipWarehouseLocation')"
        v-model="row[col.prop]"
        :disabled="disabled || disabledReturnS(col, row)"
        filterable
        clearable
        default-first-option
        size="mini"
        @change="val=>changeWarehouseLocation(col.prop,val,rowIndex,row)"
      >
        <el-option
          v-for="item in getWarehouseList(row.factoryCode)"
          :key="item.warehouseLocationCode"
          :label="item.warehouseLocationCode+' '+item.warehouseLocationName"
          :value="item.warehouseLocationCode">
        </el-option>
      </el-select>
      <el-select
        v-else-if="col.prop==='factoryCode'"
        v-model="row[col.prop]"
        :disabled="col.disabled||disabled"
        size="mini"
        filterable
        default-first-option
        @change="val=>handleChangeItemFactory(val,rowIndex,row)"
      >
        <el-option
          v-for="item in factoryList"
          :key="item.factoryCode"
          :label="item.factoryCode+' '+item.factoryName"
          :value="item.factoryCode">
        </el-option>
      </el-select>
      <el-select
        v-else-if="col.prop==='projectCategory' && col.type === 'select'"
        v-model="row[col.prop]"
        :disabled="Boolean(col.disabled||disabled)"
        clearable
          filterable
        default-first-option
        size="mini"
        @change="value=>handleCategoryChange(col.prop, value, row)"
      >
        <el-option
          v-for="item in dictList['poProjectCategory']"
          :key="item.value"
          :label="item.name"
          :value="item.value">
        </el-option>
      </el-select>
      <el-select
        v-else-if="col.prop==='costCenter'"
        v-model="row[col.prop]"
        :disabled="col.disabled||disabled"
        filterable
        default-first-option
        clearable
        size="mini"
      >
        <el-option
          v-for="item in costCenterList"
          :key="item.costCenter"
          :label="item.costCenter+' '+item.description"
          :value="item.costCenter">
        </el-option>
      </el-select>
      <el-select
        v-else-if="col.type==='select'&&col.prop==='inputTax'"
        v-model="row[col.prop]"
        :disabled="col.disabled||disabled || disableByInSystemMaterial(col.prop, row) || disabledReturnS(col, row)"
        size="mini"
        filterable
        default-first-option
        clearable
        @change="val=>handleChangeItemInputTax(val,row,col.prop)"
      >
        <el-option
          v-for="item in dictList[col.enums]"
          :key="item.value"
          :label="col.enums==='orderUnit'?item.name:item.value + ' ' + item.name"
          :value="item.value"
          :disabled="item.value==='J1'||item.value==='J3'"
        >
        </el-option>
      </el-select>
      <div v-else-if="col.type==='select' && col.prop === 'orderReason'">
        <el-select
          v-model="row[col.prop]"
          :disabled="col.disabled || disabled || disableByInSystemMaterial(row, col)"
          size="mini"
          filterable
          clearable
          :class="{ 'orderReason-select': row[col.prop]=== '其他' }"
        >
          <el-option
            v-for="item in dictList[col.enums]"
            :key="item.value"
            :label="item.name"
            :value="item.name">
          </el-option>
        </el-select>
        <el-input
          v-if="row[col.prop] === '其他'"
          v-model="row.newOrderReason"
          size="mini"
          style="width: 100px"
          :placeholder="col.name"
          :maxlength="col.maxlength||30"
          :disabled="disabled || disabledInput(col.prop, row) || disabledBySpecial(col.prop, row)"
        />
      </div>
      <el-select
        v-else-if="col.type==='select'"
        v-model="row[col.prop]"
        :disabled="col.disabled||disabled || disableByInSystemMaterial(col.prop, row) || disabledReturnS(col, row)"
        size="mini"
        filterable
        default-first-option
        clearable
      >
        <el-option
          v-for="item in dictList[col.enums]"
          :key="item.value"
          :label="['orderUnit', 'orderReason'].includes(col.enums)?item.name:item.value + ' ' + item.name"
          :value="item.value">
        </el-option>
      </el-select>
      <SelectMaterialGroup
        v-else-if="col.prop==='materialGroup'&&!row.skuNo"
        :data.sync="row[col.prop]"
        :disabled="disabled"
        :materialList="materialList"
        @change="val=>handleChangeMaterialGroup(val,row)"
      />
      <span v-else-if="col.prop ==='lastGrossMargin'">
        {{ row.poItemExtend && !['undefined', undefined, '', null, NaN].includes(row.poItemExtend.lastGrossMargin) ? `${row.poItemExtend.lastGrossMargin}%` : ''}}
      </span>
      <span v-else-if="['taxedTotalAmount','untaxedTotalAmount','taxTotalAmount'].indexOf(col.prop)>-1">
        {{row[col.prop]?Number(row[col.prop]).toFixed(col.precision||2):''}}
      </span>
      <span v-else-if="col.prop ==='materialGroup'">{{row.materialGroupNum ? row.materialGroupNum + ' ' + row.materialGroupName: row.materialGroupName}}</span>
      <span v-else-if="col.prop === 'unitName'">{{renderUnitName(row[col.prop])}}</span>
      <span v-else-if="col.prop === 'cloud'">
        {{row.cloud ? '是' : (row.cloud === false ? '否' : '')}}
      </span>
      <span v-else-if="col.prop === 'orderChannelList'">
          {{ row.poItemExtend ? mapKeyToValue('orderChannel', row.poItemExtend.orderChannelList, dictList) : '' }}
        </span>
      <span v-else>{{row[col.prop]}}</span>
    </template>
  </vxe-table-column>
  <vxe-table-column
    :width="90"
    fixed="right"
    align="center"
    field="option"
    title="操作"
  >
    <template slot-scope="{ row }">
      <el-link type="primary" style="padding:2px 0px;" @click="toDtl(row)">
        详情
      </el-link>
    </template>
  </vxe-table-column>
</vxe-table>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import SelectSku from '@/pages/orderPurchase/components/common/SelectSku'
import SelectMaterialGroup from '@/pages/orderPurchase/components/common/SelectMaterialGroup'
import { getSO, searchMaterialGroup, getRefundableAmountApi, querySkuBatchPrice, getPOHead, assignByQuantity, calcUnTaxedPrice } from '@/api/mm'
import { safeRun, copyToClipboard } from '@/utils/index'
import { mapKeyToValue } from '@/utils/mm'
import { formatAmount, handleGetInfoBySpecialSupplySo } from '@/pages/orderPurchase/utils'
import { isAssigned, rowEditChange } from '@/utils/poPriceCalculate'
import { merge } from 'lodash'

export default {
  name: 'purchaseOrderTable',
  data () {
    const { createTime, orderType } = this.data
    const newCreateTime = moment(createTime).subtract(1, 'days').format('YYYY-MM-DD')
    return {
      orderType,
      materialList: [],
      projectCategoryList: [
        { label: 'K', value: 'K' }
      ],
      dateOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(newCreateTime)).getTime()
        }
      },
      tableHeight: 355,
      refundableAmount: 0
    }
  },
  props: [
    'factoryList', 'data', 'disabled',
    'field', 'costCenterList', 'setTitleCheckbox',
    'diffDays', 'reCalcCompQuantity', 'setRowMPQ'
  ],
  components: {
    SelectSku, SelectMaterialGroup
  },
  created () {
    // 需要添加条件
    searchMaterialGroup('').then(data => {
      if (data) {
        this.materialList = data
      }
    })
  },
  methods: {
    isAssigned,
    mapKeyToValue,
    smLength (col) {
      if (col.prop === 'supplierMaterialNo') return 100
    },
    disabledBySpecial (prop, row) {
      const propList = ['soNo', 'soItemNo']
      if (propList.find(item => item === prop)) {
        return true
        // if (row.specialSupplySo) {
        //   return true
        // }
      }
    },
    disabledInput(prop, row) {
      if (prop === 'innerOrderNo') {
        if (row.factoryCode === '1500') {
          return false;
        }
        return true
      }
    },
    disabledReturnS(col, row) {
      const { prop } = col
      // 选择项目类别为s后，【sku编码】、【价格】、【税率】、【仓库地点】、【跟踪单号】置灰，不可手动填写，由后端查询返回。
      // 数量也置灰 2022.11.22
      if (this.isReturnOrderType && ['skuNo', 'warehouseLocation', 'taxedPrice', 'priceTimes', 'inputTax', 'trackNo', 'itemQuantity'].includes(prop) && row.projectCategory === 'S') {
        return true
      }
      return false
    },
    showItemTip(row, col) {
      const { prop } = col
      const { calculatedMPQ, itemQuantity, bstrf } = row
      if (prop === 'itemQuantity' && calculatedMPQ && itemQuantity > 0 && (itemQuantity % bstrf !== 0)) {
        return true
      }
    },
    renderUnitName (value) {
      const { orderType } = this.data
      const unitList = [
        'Z010', 'Z008', 'Z007', 'Z004'
      ]
      if (!unitList.includes(orderType)) {
        // eslint-disable-next-line eqeqeq
        const opt = this.dictList && this.dictList['orderUnit'] && this.dictList['orderUnit'].find(item => item.value == value)
        if (opt) {
          return opt.name
        }
      }
      return value
    },
    handleCopy (no, event) {
      copyToClipboard.bind(this)(no, event)
    },
    disableByPlanList (row, col) {
      const list = [
        'itemDeliveryDate', 'itemQuantity'
      ]
      let ret = false
      safeRun(() => {
        if (list.includes(col.prop) && row.planList.length > 1) {
          ret = true
        }
      })
      return ret
    },
    disableByRefundableAmount(row, col) {
      if (this.isReturnOrderType && col.prop === 'refundableAmount' && row.projectCategory === 'K') {
        return true
      }
      return false
    },
    disableByInSystemMaterial (prop, row) {
      if (prop === 'unit' && row.inSystemMaterial) {
        return true
      }
      return false
    },
    disabledByOverCharge (col) {
      // return col.prop === 'taxedPrice' && (this.data.isOverchargeFree === 1)
    },
    disabledByFree (row) {
      let ret = false
      // eslint-disable-next-line
      if (row.isFree == 1) {
        ret = true
      }
      return ret
    },
    startLoading () {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      return loading
    },
    endLoading (loading) {
      if (loading) {
        loading.close()
      }
    },
    checCheckbox ({ row }) {
      const { skuNo, materialDescription } = row
      return !!skuNo || !!materialDescription
    },
    getWarehouseList (factoryCode) {
      if (factoryCode) {
        return this.warehouseList.filter(item => item.factoryCode === factoryCode)
      }
      return []
    },
    toDtl (row) {
      if (!row.skuNo && !row.materialDescription) {
        this.$message.warning({
          message: '请选择SKU'
        })
        return
      }
      this.$emit('showDetail', row)
    },
    onTableSelectionChange ({ records }) {
      this.$emit('update:records', records)
    },
    onTableSelectionAllChange ({ records }) {
      this.$emit('update:records', records)
    },
    handleCategoryChange (prop, value, row) {
      if (prop === 'projectCategory') {
        row.itemQuantity = 0
        row.isFree = 0
        if (value !== 'A') {
          delete row.fixedAssetsList
          row.fixedAssetsCardNo = ''
        }
        if (this.isReturnOrderType) {
          if (value === 'A') {
            row.warehouseLocation = null
          } else if (value === 'K') {
            row.refundableAmount = 0
          } else if (value === 'S' && this.isReturnProjectCategory(row)) {
            this.handleGetSku(row)
          }
        }
        delete row.calculatedMPQ
        this.$emit('addItem', row.skuNo, row)
      }
    },
    handleSelectSku (val, row) {
      if (val) {
        row.inSystemMaterial = true
        row.itemQuantity = 0
        row.isFree = 0
        delete row.calculatedMPQ
        this.$emit('addItem', val, row)
      } else {
        row.inSystemMaterial = false
      }
    },
    getTrackOrderHead(data) {
      const { trackNo } = data
      if (this.data.orderType === 'Z004' && trackNo) {
        getPOHead({ orderNo: trackNo }).then(res => {
            if (res && res.supplierNo !== this.data.supplierNo) {
              this.$message.warning('退货单供应商与跟踪单号供应商不一致，请核实是否有误！')
            }
        })
      }
    },
    getRefundableAmount(data) {
      const { factoryCode, itemQuantity, skuNo, taxedTotalAmount, trackNo, projectCategory } = data
      if (factoryCode && itemQuantity && skuNo && taxedTotalAmount && trackNo && projectCategory !== 'K') {
        const params = [{
          factoryCode,
          quantity: itemQuantity,
          skuNo,
          taxedTotalAmount,
          trackNo
        }]
        getRefundableAmountApi(params).then(res => {
          const resData = res[`${trackNo + '-' + skuNo + '-' + factoryCode}`]
          const { refundAmount, taxedPrice, priceTimes, inputTax, untaxedPrice } = resData || {}
          if (refundAmount) {
            data.taxedPrice = taxedPrice
            data.priceTimes = priceTimes
            data.untaxedPrice = untaxedPrice
            const taxRate = this.getInputTax(inputTax)
            data.inputTax = inputTax
            data.taxedTotalAmount = formatAmount(taxedPrice * itemQuantity / priceTimes, 2)
            data.untaxedTotalAmount = formatAmount(taxedPrice / (1 + taxRate * 0.01) * itemQuantity / priceTimes, 2)
            data.taxTotalAmount = formatAmount(data.taxedTotalAmount - data.untaxedTotalAmount, 2)
          }
          data.refundableAmount = refundAmount || data.taxedTotalAmount
        })
      }
      if (projectCategory === 'K') {
        data.refundableAmount = 0
      }
    },
    async handleItemNumChange (val, old, prop, row) {
      const { priceTimes = 1 } = row
      if (prop === 'itemQuantity') {
        const { planList, fixedAssetsList } = row
        // 计划行和固定资产行都只有一行的时候联动
        if (planList && planList.length === 1) {
          planList[0].deliveryQuantity = row.itemQuantity
        }
        if (fixedAssetsList && fixedAssetsList.length === 1) {
          fixedAssetsList[0].fixedAssetsCardQuantity = row.itemQuantity
        }
        await assignByQuantity(this.data, row, old).then(async (res) => {
            if (res.data) {
              this.data = merge(this.data, res.data)
              row = merge(row, res.row)
              console.log(row, this.data);
            }
          })
        await this.$emit('updateDeliveryDaysByWarehouseLocation', row, 0)
      }
      // 如果转储订单，不进行价格计算
      if (this.data.orderType !== 'Z003') {
        const taxRate = this.getInputTax(row.inputTax)
        if (prop === 'taxedPrice') {
          const taxedPrice = val || 0
          row.taxedTotalAmount = formatAmount(taxedPrice * row.itemQuantity / priceTimes, 2)
          row.untaxedTotalAmount = formatAmount(taxedPrice / (1 + taxRate * 0.01) * row.itemQuantity / priceTimes, 2)
          row.untaxedPrice = formatAmount(row.untaxedTotalAmount / row.itemQuantity * priceTimes, 2)
        }
        if (prop === 'itemQuantity') {
          if (this.data.orderType !== 'Z010' && this.data.orderType !== 'Z003') {
            this.setRowMPQ(row)
          }
          safeRun(() => {
            let { lossRatio } = this.data
            let { itemQuantity } = row
            if (!lossRatio) lossRatio = 0
            if (!itemQuantity) itemQuantity = 0
            row.componentList.forEach(comp => {
              let { inventoryUnitMole = 1, inventoryUnitDeno = 1 } = row
              const { materialGroupNum } = comp
              let number = Number((itemQuantity * inventoryUnitMole / inventoryUnitDeno * (comp._quantity / comp.bomQuantity) * (1 + lossRatio * 0.01)).toFixed(3))
              if (materialGroupNum === 430) {
                number = Math.ceil(number)
              }
              comp.componentRequiredQuantity = number
            })
          })
        }
        row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
        await calcUnTaxedPrice(row, this.dictList).then(data => {
          if (data) row = merge(row, rowEditChange(data))
          console.log(row, data);
        })
      }
      if (this.isReturnOrderType) {
        if (prop === 'taxedPrice' || prop === 'itemQuantity') {
          row.refundableAmount = row.taxedTotalAmount
        }
        if (row.projectCategory === 'K') {
          row.refundableAmount = 0
        }
      }
      this.$emit('reactiveSetItem', row)
    },
    handleChangeItemDate (date, row, col) {
      const materialList = [ 'Z007', 'Z008', 'Z010' ]
      const { orderType, createTime } = this.data
      const { planList } = row
      safeRun(() => {
        if (planList && planList.length === 1) {
          planList[0].deliveryDate = date
        }
      })
      if (col.prop === 'itemDeliveryDate') {
        if (row.oldDeliveryDate && date === row.oldDeliveryDate.split(' ')[0]) {
          row.cloud = row.oldCloud
        } else {
          row.cloud = null
        }
        if (materialList.includes(orderType)) {
          if (!row.inSystemMaterial && row.itemDeliveryDate) {
            row.standardLeadTime = this.diffDays(row.itemDeliveryDate, createTime)
            this.$emit('updateItemDeliveryDate', row)
          }
        }
      }
    },
    async handleChangeItemInputTax (val, row, prop) {
      if (val === 'J0') {
        this.$message.warning('进项税已选择0%，请确认是否有误！')
      }
      if (prop === 'inputTax') {
        const { priceTimes = 1 } = row
        const taxRate = this.getInputTax(val)
        // row.untaxedPrice = Number((row.taxedPrice / (1 + 0.01 * taxRate)).toFixed(2))
        row.taxedTotalAmount = formatAmount(row.taxedPrice * (row.itemQuantity || 0) / priceTimes, 2)
        row.untaxedTotalAmount = formatAmount((row.taxedPrice / (1 + taxRate * 0.01)) * (row.itemQuantity || 0) / priceTimes, 2)
        row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
        await calcUnTaxedPrice(row, this.dictList).then(data => {
          if (data) row = merge(row, rowEditChange(data))
        })
        this.$emit('reactiveSetItem', row)
      }
    },
    handleChangeItemFactory (val, index, row) {
      const { soNo, warehouseLocation } = row
      if (index === 0) {
        this.$emit('updateFactory', val, warehouseLocation, soNo)
      }
      if (this.data.orderType === 'Z006') {
        this.$emit('updateCompWarehouse', val, index, row)
      }
      row.itemQuantity = 0
      row.isFree = 0
      if (this.isReturnOrderType) {
        this.getRefundableAmount(row)
      }
      delete row.calculatedMPQ
      this.$emit('addItem', row.skuNo, row)
    },
    handleChangeInput (val, prop, row) {
      if (prop === 'fixedAssetsCardNo') {
        if (!row.fixedAssetsList || !row.fixedAssetsList.length) {
          row.fixedAssetsList = [{
            fixedAssetsNo: null
          }]
        }
        row.fixedAssetsList[0].fixedAssetsCardNo = val
        const idx = this.data.itemList.indexOf(row)
        this.$set(this.data.itemList, idx, {
          ...row,
          fixedAssetsCardNo: val,
          fixedAssetsList: [ ...row.fixedAssetsList ]
        })
      }
      if (prop === 'materialDescription') {
        const inSystemMaterialList = [ 'Z007', 'Z008', 'Z010', 'Z004' ]
        if (inSystemMaterialList.includes(this.data.orderType)) {
          // 如果没有选择SKU，并修改了物料描述
          if (!row.skuNo) {
            row.inSystemMaterial = false
          }
          delete row.isEmptyLine
          this.$emit('updateMaterialDescription', row)
        }
        if (!row.planList) {
          row.planList = [{
            deliveryQuantity: 0,
            deliveryDate: moment().format('YYYY-MM-DD'),
            planNo: '0001'
          }]
        }
        if (!row.fixedAssetsList && (['Z007', 'Z004'].includes(this.data.orderType))) {
          row.fixedAssetsList = [{
            fixedAssetsNo: '01'
          }]
        }
      }
      if (prop === 'soNo') {
        this.$emit('updateDeliveryDaysByWarehouseLocation', row, 2)
        val = val.trim()
        if (val) {
          const loading = this.startLoading()
          getSO({ soNo: val }).then(data => {
            this.endLoading(loading)
            this.$emit('updateCustomerService', { ...data }, row)
          })
        } else {
          this.$emit('updateCustomerService', 'clear', row)
        }
      }
      if (prop === 'soItemNo') {
        this.$emit('updateDeliveryDaysByWarehouseLocation', row, 2)
      }
      if (prop === 'trackNo' && this.isReturnOrderType) {
        this.getRefundableAmount(row)
        this.getTrackOrderHead(row)
      }
      if (prop === 'batchNo' && this.isReturnOrderType) {
        const { skuNo, factoryCode, projectCategory } = row
        const { supplierNo } = this.data
        if (supplierNo && factoryCode && skuNo && val) {
          const params = {
            factoryCode,
            supplierNo,
            itemList: [
              {
                batchNo: val,
                skuNo
              }
            ]
          }
          querySkuBatchPrice(params).then(res => {
            if (res?.code === 0) {
              const { data } = res
              if (data.length === 0) {
                this.$message.error('未查找到批次对应的采购价，请手工填写采购价！')
              } else if (data.length === 1) {
                row.trackNo = data[0].poNo
                if (projectCategory === 'K') {
                  row.refundableAmount = 0
                  this.$message.warning('跟踪单号已自动填充!')
                } else {
                  row.taxedPrice = data[0].taxedPrice
                  row.priceTimes = data[0].priceTimes
                  const taxRate = this.getInputTax(row.inputTax)
                  row.untaxedPrice = Number((row.taxedPrice / (1 + 0.01 * taxRate)).toFixed(2))
                  row.taxedTotalAmount = formatAmount(row.taxedPrice * row.itemQuantity / row.priceTimes, 2)
                  row.untaxedTotalAmount = formatAmount(row.taxedPrice / (1 + taxRate * 0.01) * row.itemQuantity / row.priceTimes, 2)
                  this.$message.warning('价格和跟踪单号已自动填充!')
                }
                this.getRefundableAmount(row)
              } else if (data.length > 1) {
                this.$message.error('查找到多条采购价，请手工填写采购价！')
              }
            }
          })
        }
      }
      if (this.isReturnProjectCategory(row) && ['specialSupplySo', 'specialSupplySoItem'].includes(prop)) {
        // 向后端获取sku编码，采购价格，仓库地点，跟踪单号等信息
        this.handleGetSku(row)
      }
    },
    // 向后端获取sku编码，采购价格，仓库地点，跟踪单号等信息
    async handleGetSku(row) {
      if (this.isReturnProjectCategory(row)) {
        const idx = this.data.itemList.indexOf(row)
        const data = await handleGetInfoBySpecialSupplySo(row, this.dictList)
        if (data.skuNo) {
          const { taxedPrice, itemQuantity, priceTimes, inputTax } = data
          const taxRate = this.getInputTax(inputTax)
          const taxedTotalAmount = formatAmount(taxedPrice * itemQuantity / priceTimes, 2)
          const untaxedTotalAmount = formatAmount(taxedPrice / (1 + taxRate * 0.01) * itemQuantity / priceTimes, 2)
          const taxTotalAmount = formatAmount(taxedTotalAmount - untaxedTotalAmount, 2)
          // const refundableAmount = refundAmount || data.taxedTotalAmount
          const newData = {
            ...row,
            ...data,
            taxedTotalAmount,
            untaxedTotalAmount,
            taxTotalAmount
          }
          delete newData.isEmptyLine
          console.log('newData.planList start', newData.planList)
          if (!newData.planList || (newData.planList.length === 1 && newData.planList[0].deliveryQuantity === 0)) {
            console.log('in')
            newData.planList = [{
              deliveryQuantity: newData.itemQuantity,
              // deliveryDate: moment().format('YYYY-MM-DD'),
              planNo: '0001'
            }]
          }
          console.log('newData.planList end', newData.planList)
          this.$set(this.data.itemList, idx, newData)
          const { itemList } = this.data
          const len = itemList.length
          if (itemList[len - 1] === row || (itemList[len - 1] && itemList[len - 1].uuid) === (row && row.uuid)) {
            this.$emit('addItemEmptyLine')
          }
        }
      }
    },
    // 退货订单，专供销售单号，专供销售行号，项目类别为S
    isReturnProjectCategory(row) {
      return this.isReturnOrderType && (row?.specialSupplySo ?? '') !== '' && (row?.specialSupplySoItem ?? '') !== '' && row?.projectCategory === 'S'
    },
    handleChangeMaterialGroup (val, row) {
      if (val) {
        const { materialGroupName, materialGroupNum } = val
        if (materialGroupName && materialGroupNum) {
          row.materialGroupName = materialGroupName
          row.materialGroupNum = materialGroupNum
        }
      }
    },
    getInputTax (val) {
      const taxRate = Number((this.dictList['inputTax'].find(item => item.value === val) || {}).description) || 0
      return taxRate
    },
    handleChangeCheckbox (val, prop, row) {
      // eslint-disable-next-line
      if (val == 1 && prop === 'isFree') {
        row.taxedPrice = 0
        row.untaxedPrice = ''
        row.taxedTotalAmount = ''
        row.untaxedTotalAmount = ''
        row.taxTotalAmount = ''
        row.poItemExtend.lastGrossMargin = 100
        row.editChange = true
      }
      if (prop === 'isUrgent') {
        this.setTitleCheckbox(prop)
      }
      // eslint-disable-next-line
      if (prop === 'isDeliveryDone' && val == 1 && row.receivedQuantity !== 0) {
        row.itemQuantity = row.receivedQuantity
        this.reCalcCompQuantity(null, row, true)
        safeRun(() => {
          row.planList.forEach(plan => {
            plan.deliveryQuantity = row.receivedQuantity
          })
        })
      }
    },
    handleStandardLeadTime (val, row) {
      if (val != null) {
        const { createTime } = this.data
        if (createTime) {
          row.itemDeliveryDate = moment(createTime).add(val, 'days').format('YYYY-MM-DD')
        }
      }
    },
    changeWarehouseLocation (prop, val, idx, row) {
      const { factoryCode, soNo, warehouseLocation, shipWarehouseLocation } = row
      const { orderType } = this.data
      if (prop === 'warehouseLocation' && idx === 0) {
        this.$emit('updateWarehouseLocation', factoryCode, val, soNo)
      }
      if (orderType === 'Z003') {
        if (factoryCode && warehouseLocation && shipWarehouseLocation) {
          this.$emit('updateTransferTime', factoryCode, warehouseLocation, shipWarehouseLocation, row)
        }
      } else {
        this.$emit('updateDeliveryDaysByWarehouseLocation', row, 2)
      }
      if (orderType === 'Z006') {
        this.$emit('updateCompWarehouseByWarehouse', val, idx, row)
      }
    },
    async handlePriceTimesChange (val, row) {
      const { inputTax, priceTimes } = row
      const taxRate = this.getInputTax(inputTax)
      // row.untaxedPrice = Number((row.taxedPrice / (1 + 0.01 * taxRate)).toFixed(2))
      row.taxedTotalAmount = formatAmount(row.taxedPrice * (row.itemQuantity || 0) / priceTimes, 2)
      row.untaxedTotalAmount = formatAmount(row.taxedPrice / (1 + taxRate * 0.01) * (row.itemQuantity || 0) / priceTimes, 2)
      row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
      await calcUnTaxedPrice(row, this.dictList).then(data => {
        if (data) row = merge(row, rowEditChange(data))
      })
      if (this.isReturnOrderType) {
        row.refundableAmount = row.taxedTotalAmount
      }
      this.$emit('reactiveSetItem', row)
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      warehouseList: state => state.orderPurchase.warehouseList
    }),
    columns () {
      let ret = this.field || []
      const unitList = [
        'Z010', 'Z008', 'Z007', 'Z004'
      ]
      const { orderType } = this.data
      const ableSubCateList = [ 'Z004', 'Z005' ]
      ret.forEach(field => {
        if (field.prop === 'unitName' && unitList.includes(orderType)) {
          field.prop = 'unit'
          field.type = 'select'
          field.enums = 'orderUnit'
        }
        if (field.prop === 'projectCategory' && !ableSubCateList.includes(orderType)) {
          field.disabled = true
          field.type = 'text'
        }
      })
      return ret
    },
    supplierId () {
      return (this.data.supplier || {}).providerId
    },
    isReturnOrderType() {
      return this.data?.orderType === 'Z004'
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .checkbox-column .vxe-cell.c--tooltip {
  padding: 2px 0px;
  margin-left: -8px;
}
.orderReason-select {
  width: 100px !important;
}
.required {
  &:before {
    content: '*';
    color: #ff7268;
    margin-right: 4px;
  }
  &::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>
