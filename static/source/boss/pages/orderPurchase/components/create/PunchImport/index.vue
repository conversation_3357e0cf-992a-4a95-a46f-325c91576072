<template>
  <div class="punch-import" v-loading="loading.pageLoading">
    <p class="red">以下为购物车商品，如需调整请在商家购物车操作或导入后在创建页面调整。</p>
    <div class="title">
      <span>供应商: {{title.supplierName}}</span>
      <span>订货未税总额: {{totalUnTaxPrice.toFixed(2)}}</span>
      <span>订货含税总额: {{totalTaxPrice.toFixed(2)}}</span>
      <span>税费总额: {{totalTax}}</span>
    </div>
    <div class="table">
      <el-table
        height="300"
        tooltip-effect="dark"
        border
        :cell-style="cellStyle"
        :data="calcDataList"
        v-loading="loading.tableLoading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column align="center" fixed="left" type="selection" />
        <el-table-column show-overflow-tooltip align="center" type="index" label="序号" />
        <el-table-column align="center" prop="skuNo" label="震坤行SKU" width="120px">
          <template slot-scope="{row}">
            <el-select
              v-if="Array.isArray(row.duplicateZkhSkuInfos) && row.duplicateZkhSkuInfos.length"
              style="width:100%"
              filterable
              placeholder="请选择"
              value-key="skuNo"
              v-model="row.skuNo"
              @change="(value) => handleSkuChange(row, value)"
            >
              <el-option
                v-for="item in row.duplicateZkhSkuInfos"
                :key="item.skuNo"
                :label="item.skuNo"
                :value="item">
              </el-option>
            </el-select>
            <span v-else>{{row.skuNo}}</span>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="supplierOrderId" label="商家订货号" width="100px"/>
        <el-table-column align="center" prop="supplierSkuDesc" label="订货商品描述"  width="200px" />
        <el-table-column show-overflow-tooltip align="center" prop="supplierOrderAmount" label="订货数量" />
        <!-- <el-table-column show-overflow-tooltip align="center" prop="" label="转换震坤行数量"  width="110px" /> -->
        <el-table-column show-overflow-tooltip align="center" prop="supplierUnTaxPrice" label="订货未税单价" width="100px" />
        <el-table-column show-overflow-tooltip align="center" prop="supplierTaxPrice" label="订货含税单价"  width="100px" />
        <el-table-column show-overflow-tooltip align="center" prop="zkhTaxPrice" label="震坤行含税单价"  width="110px" />
        <el-table-column show-overflow-tooltip align="center" prop="zkhTaxRate" label="税率">
          <template slot-scope="{row}">
            {{row.zkhTaxRate ? (isNaN(parseFloat(row.zkhTaxRate) * 100) ? '' : row.zkhTaxRate * 100 + '%') : row.zkhTaxRate}}
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" prop="leadDate" label="预计交货日期"  width="100px"/>
        <el-table-column show-overflow-tooltip align="center" prop="leadTime" label="leadtime" />
        <el-table-column show-overflow-tooltip align="center" prop="supplierUnit" label="订货单位" />
        <el-table-column show-overflow-tooltip align="center" prop="zkhUnit" label="震坤行单位" />
        <el-table-column show-overflow-tooltip fixed="right" align="center" prop="manage" label="操作">
          <template slot-scope="{row}">
            <el-button type="text" @click="deleteRow(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagi">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="table.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="table.total">
        </el-pagination>
      </div>
    </div>
    <div class="button-group">
      <el-button class="punch-import-btn" :disabled="!selectedItems.length" type="primary" @click="handleImport">
        <el-tooltip v-if="!selectedItems.length" class="item" effect="dark" content="请勾选要导入的商品！" placement="top">
          <span>确认导入</span>
        </el-tooltip>
        <span v-else>确认导入</span>
      </el-button>
      <el-button class="punch-import-btn" @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>
<script>
import { getPunchImportDataList } from '@/api/honeycomb'
function isLocalDebug () {
  return /fetest|300/.test(location.href)
}
isLocalDebug()
export default {
  props: ['purchaseData', 'visible', 'finishDataApi'],
  data () {
    return {
      dataList: [],
      title: {},
      table: {
        pageNo: 1,
        pageSize: 10,
        total: 0
      },
      loading: {
        pageLoading: false,
        tableLoading: false
      },
      selectedItems: []
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(n, o) {
        if (n === true) {
          this.initSearch()
        }
      }
    }
  },
  computed: {
    calcDataList () {
      const { pageNo, pageSize } = this.table
      return this.dataList.slice((pageNo - 1) * pageSize, pageNo * pageSize)
    },
    totalUnTaxPrice () {
      const unTaxTotal = this.dataList.reduce((prev, next) => {
        const sum = (parseFloat(next.supplierUnTaxPrice) || 0) * (parseFloat(next.supplierOrderAmount) || 0)
        if (isLocalDebug()) {
          console.log(`supplierUnTaxPrice: ${next.supplierUnTaxPrice}, supplierOrderAmount: ${next.supplierOrderAmount}`)
        }
        return prev + sum
      }, 0) || 0
      return unTaxTotal
    },
    totalTaxPrice () {
      const taxTotal = this.dataList.reduce((prev, next) => {
        const sum = (parseFloat(next.supplierTaxPrice) || 0) * (parseFloat(next.supplierOrderAmount) || 0)
        if (isLocalDebug()) {
          console.log(`supplierTaxPrice: ${next.supplierTaxPrice}, supplierOrderAmount: ${next.supplierOrderAmount}`)
        }
        return prev + sum
      }, 0) || 0
      return taxTotal
    },
    totalTax () {
      return (this.totalTaxPrice - this.totalUnTaxPrice).toFixed(2)
    }
  },
  methods: {
    handleSelectionChange(val, b, c, d) {
      console.log(val, this.selectedItems)
      this.selectedItems = val
    },
    handleSkuChange (row, sku) {
      const idx = this.dataList.findIndex(item => item === row)
      const item = { ...row, ...sku }
      console.log(item)
      this.$set(this.dataList, idx, item)
    },
    deleteRow (row) {
      this.$confirm('确认删除此行？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const { supplierNo } = this.purchaseData
        this.loading.pageLoading = true
        this.finishDataApi({ supplierNo, ids: row.id })
          .then(res => {
            if (res === null) {
              this.$message.success('删除成功！')
              this.initSearch()
            }
          })
      })
    },
    async initSearch () {
      this.loading.pageLoading = true
      const params = {
        supplierNo: 'V45696'
      }
      const response = await getPunchImportDataList(params)
      // response.list = []
      this.dataList = []
      if (response && response.list) {
        let { list, supplierName, totalTax, totalTaxPrice, totalUnTaxPrice } = response
        // list.push(...[{}, {}])
        // list.forEach(item => {
        //   item.skuNo = 'AC5963'
        //   item.supplierTaxPrice = 5 + (Math.random() * 10).toFixed(2) * 0.1
        //   item.zkhTaxPrice = Math.random() > 0.5 ? item.supplierTaxPrice : 5 + (Math.random() * 10).toFixed(2) * 0.1
        // })
        // if (isLocalDebug()) {
        //   list.forEach(item => {
        //     item.skuNo = item.skuNo ? item.skuNo : 'AC5963'
        //   })
        //   list = list.slice()
        // }
        this.dataList = list
        this.title = { supplierName, totalTax, totalTaxPrice, totalUnTaxPrice }
        this.table.total = this.dataList.length
      }
      this.loading.pageLoading = false
    },
    cellStyle ({ row, column, rowIndex, columnIndex }) {
      const { property } = column || {}
      if (property === 'zkhTaxPrice' || property === 'supplierTaxPrice') {
        if ((row.supplierTaxPrice || row.zkhTaxPrice) && (parseFloat(row.supplierTaxPrice) !== parseFloat(row.zkhTaxPrice))) {
          return {
            backgroundColor: '#ff000057'
          }
        }
      }
    },
    handleSizeChange (pageSize) {
      this.table.pageSize = pageSize
      this.table.pageNo = 1
    },
    handleCurrentChange (pageNo) {
      this.table.pageNo = pageNo
    },
    handleCancel () {
      this.$emit('close')
    },
    validateData (data) {
      const lines = []
      data.forEach((item, index) => {
        if (!item.skuNo) {
          lines.push(index + 1)
        }
      })
      if (lines.length) {
        throw new Error(`${lines.join(',')}项目行SKU编码为空，不能导入！`)
      }
    },
    handleImport () {
      const importData = this.selectedItems
      try {
        this.validateData(importData)
        console.log('validate success...')
        console.log('import starting...', importData)
        setTimeout(() => {
          this.$emit('import', importData)
        }, 200)
      } catch (err) {
        this.$message.error(err.message)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.punch-import {
  .red {
    margin-top: -10px;
    color: red;
  }
  .title{
    margin: 10px 0px;
    span {
      margin-right: 10px;
    }
  }
  &-btn {
    width: 90px;
  }
  .table {
    overflow: hidden;
    .pagi{
      margin-top: 10px;
      float:right;
    }
  }
  .button-group{
    margin-top: 20px;
    text-align: right;
  }
}
</style>
