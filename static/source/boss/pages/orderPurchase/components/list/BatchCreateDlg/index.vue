<template>
  <el-dialog
    title="批量创建订单"
    :visible.sync="dlgVisible"
    width="600px"
    @open="openDlg"
    @closed="$emit('update:showDialog', false)"
  >
    <el-form v-if="!isDirect" :model="batchData" :rules="rules" label-width="150px">
      <el-form-item label="非直发订单类型" prop="orderType">
        <el-select v-model="batchData.orderType" placeholder="请选择订单类型">
          <el-option
            v-for="item in calcOrderTypeList"
            :key="item.value"
            :label="item.value+' '+item.name"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="notice">*批量模板在上传后，将会直接生成订单，请确认信息无误后进行上传！上传后请在订单列表搜索客户来查询对应订单即可</div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:showDialog', false)">取 消</el-button>
      <el-button @click="handleDownload" type="primary" plain>下载批量创建模板</el-button>
      <el-upload
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        :action="uploadAction"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button type="primary" >上传批量创建模板</el-button>
      </el-upload>
    </div>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    },
    isDirect: Boolean
  },
  data () {
    return {
      loading: null,
      resultData: {
        title: '',
        list: []
      },
      batchData: {
        orderType: 'Z001'
      },
      rules: {
        orderType: [
          { required: true, message: '请选择订单类型', trigger: 'change' }
        ]
      }
    }
  },
  watch: {
    isDirect (val, newVal) {
      if (this.isDirect) {
        this.batchData.orderType = 'Z010'
      }
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      poExcelUrls: state => state.orderCommon.poExcelUrls || {}
    }),
    orderTypeList () {
      return this.dictList['orderType']
    },
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    uploadAction () {
      return `/internal-api/mm/uploadExcel?orderType=${this.batchData.orderType}`
    },
    calcOrderTypeList () {
      const orderTypeList = this.orderTypeList || []
      orderTypeList.forEach(this.z010ChangeName)
      return orderTypeList
    }
  },
  methods: {
    z010ChangeName(order) {
      if (order.value === 'Z010') {
        // order.name = '直发订单/服务订单'
      }
    },
    openDlg () {
      if (this.$refs['batchData']) {
        this.$refs['batchData'].resetFields()
      }
    },
    getBatchExcelUrl (orderType) {
      const { Z004Template, Z003Template, Z007Z008Template, Z010Template, standardUrl } = this.poExcelUrls
      let url = ''
      switch (orderType) {
        case 'Z004':
          url = Z004Template
          break
        case 'Z003':
          url = Z003Template
          break
        case 'Z007':
        case 'Z008':
          url = Z007Z008Template
          break
        case 'Z010':
          url = Z010Template
          break
        default:
          url = standardUrl
          break
      }
      return url
    },
    handleDownload () {
      const url = this.getBatchExcelUrl(this.batchData.orderType)
      if (url) {
        window.open(url)
      }
    },
    processErrMsg (res) {
      const { data } = res
      if (data) {
        const { totalCount, failCount, successCount, importResultList } = data
        let title = `结果信息如下：总共${totalCount}单，其中成功${successCount}单，失败${failCount}单。`
        this.resultData.title = title
        if (!Array.isArray(importResultList)) return
        importResultList.forEach(ipRes => {
          if (Array.isArray(ipRes.failReasonList)) {
            ipRes.failReasonList = ipRes.failReasonList.map(item => ({
              ...item,
              ...ipRes
            }))
          }
        })
        this.resultData.list = importResultList.reduce((prev, next) => {
          if (next && Array.isArray(next.failReasonList)) {
            prev.push(...next.failReasonList)
          } else {
            prev.push(next)
          }
          return prev
        }, [])
      }
    },
    handleSuccess (res) {
      if (this.loading) {
        this.loading.close()
      }
      if (res && res.code === 0) {
        this.processErrMsg(res)
        this.$emit('update:showDialog', false)
        this.$emit('uploadSuccess', this.resultData)
      }
      if (res && res.code !== 0 && res.msg) {
        this.$alert(res.msg, '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleError () {
      if (this.loading) {
        this.loading.close()
      }
    }
  }
}
</script>

<style lang="scss">
.uploadMsg .el-message-box__message {
  max-height: 500px;
  overflow: auto;
}
</style>
