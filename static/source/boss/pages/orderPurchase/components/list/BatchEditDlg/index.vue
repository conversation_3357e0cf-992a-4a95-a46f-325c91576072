<template>
  <el-dialog
    title="批量修改订单"
    :visible.sync="dlgVisible"
    width="600px"
    @open="openDlg"
    @closed="$emit('update:showDialog', false)"
  >
    <el-row>
      <div class="desc"><span class="strong">修改抬头字段：</span>包括采购员、是否急单标识、付款条件</div>
    </el-row>
    <el-row style="margin-bottom:10px">
      <el-button class="batch-btn" @click="handleDownloadHeader" type="primary" plain>下载模板</el-button>
      <el-upload
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        action="/internal-api/mm/batchEditByExcel?type=header"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button class="batch-btn" type="primary" >上传模板</el-button>
      </el-upload>
    </el-row>
    <el-row>
      <div class="desc"><span class="strong">修改商品行字段：</span>包括仓库地点、税率、含税单价、价格倍数、行交期确认标识、计划行交货日期、订单数量、交货完成标识、不收发票标识、删除商品行、委外组件仓库地点</div>
    </el-row>
    <el-row>
      <el-button class="batch-btn" @click="handleDownloadBody" type="primary" plain>下载模板</el-button>
      <el-upload
        style="display:inline-block;margin-left:10px"
        accept=".xlsx"
        action="/internal-api/mm/batchEditByExcel?type=body"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="handleBeforeUpload"
      >
        <el-button class="batch-btn" type="primary" >上传模板</el-button>
      </el-upload>
    </el-row>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
// import { batchEditHeaderUrl, batchEditBodyUrl } from '@/pages/orderPurchase/constants'

export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      poExcelUrls: state => state.orderCommon.poExcelUrls || {}
    }),
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    uploadAction (type) {
      return `/internal-api/mm/batchEditByExcel?type=${type}`
    }
  },
  methods: {
    openDlg () {
      if (this.$refs['batchData']) {
        this.$refs['batchData'].resetFields()
      }
    },
    handleDownloadHeader () {
      if (this.poExcelUrls.batchEditHeaderUrl) {
        window.open(this.poExcelUrls.batchEditHeaderUrl)
      }
    },
    handleDownloadBody () {
      if (this.poExcelUrls.batchEditBodyUrl) {
        window.open(this.poExcelUrls.batchEditBodyUrl)
      }
    },
    processErrMsg (res) {
      const { data } = res
      if (data) {
        const { totalCount, failCount, successCount, updateResultList } = data
        let msg = `结果信息如下：<br>总共${totalCount}单，其中成功${successCount}单，失败${failCount}单。<br>`
        updateResultList.forEach(d => {
          const { failReasonList, orderNo } = d
          if (failReasonList && failReasonList.length > 0) {
            failReasonList.forEach(f => (msg += `失败原因：${f.index}-${f.reason}<br>`))
          } else if (orderNo) {
            msg += `订单号：${orderNo}修改成功<br>`
          }
        })
        return msg
      }
      return ''
    },
    handleSuccess (res) {
      if (this.loading) {
        this.loading.close()
      }
      let msg = ''
      if (res && res.code === 0) {
        msg = this.processErrMsg(res)
        this.$alert(msg, '结果', {
          customClass: 'uploadMsg',
          dangerouslyUseHTMLString: true
        })
        this.$emit('update:showDialog', false)
        this.$emit('uploadSuccess')
      } else {
        this.$alert(res ? res.msg : '操作失败！', '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleError () {
      if (this.loading) {
        this.loading.close()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.desc {
  margin: 10px 0;
  .strong {
    font-weight: bold;
  }
}
.batch-btn {
  width: 120px;
}
</style>
