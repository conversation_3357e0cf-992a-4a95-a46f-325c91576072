<template>
  <el-dialog
    v-loading="loading"
    title="协同信息确认"
    :visible.sync="dlgVisible"
    width="1000px"
    append-to-body
    @closed="handleClose"
  >
    <div style="max-height: 600px; overflow-y: auto;">
      <vxe-grid :data="tableData" v-bind="gridOptions" @checkbox-change="selectChangeEvent" @checkbox-all="selectAllChange">
        <template #purchaseLastGrossMargin_header><span style="color: #ff7268; margin-right: 5px;">*</span>采购复核毛利率</template>
        <template #purchaseLastGrossMargin="{ row }">
          <el-input v-model="row.purchaseLastGrossMargin"></el-input>
        </template>
      </vxe-grid>
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" label-suffix="：" style="margin-top: 20px;">
        <el-form-item label="原因说明" prop="reason">
          <el-input type="textarea" v-model="formData.reason" placeholder="请输入原因说明"></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="attachmentList">
          <el-upload
            ref="upload"
            action="/upload"
            :accept="acceptFileType.poCommonType"
            :before-upload="(file) => $validateFileType(file, acceptFileType.poCommonType)"
            :on-remove="handleRemove"
            :show-file-list="true"
            :multiple="true"
            :limit="numberLimit"
            :on-exceed="handleUploadExceed"
            :http-request="httpRequestHandle"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('update:showDialog', false)">取消创建</el-button>
        <el-button @click="submit" type="primary">确认提交</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { submitPaymentTerm } from '@/api/mm'
import { upload } from '@/utils/upload'
import { mapState } from 'vuex'

export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    },
    tableData: {
      required: true,
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      gridOptions: {
        ref: 'xGrid',
        border: true,
        height: 300,
        align: 'center',
        keepSource: true,
        checkboxConfig: {
          reserve: true
        },
        columns: [
          { field: 'poNo', title: 'PO单号', fixed: 'left' },
          { field: 'soNo', title: 'SO单号', fixed: 'left' },
          { field: 'customerServiceName', title: '客服' },
          { field: 'purchaseName', title: '采购' },
          { field: 'supplierNo', title: '供应商编码' },
          { field: 'supplierName', title: '供应商名称' },
          { field: 'purchaseLastGrossMargin', title: '采购复核毛利率', slots: { default: 'purchaseLastGrossMargin', header: 'purchaseLastGrossMargin_header' } }
        ]
      },
      loading: false,
      currentPage: 1,
      pageSize: 10,
      fileSizeLimit: 5,
      numberLimit: 10,
      formData: {
        reason: '',
        attachmentList: []
      },
      formRules: {
        reason: [{ required: true, message: '请输入原因说明', trigger: 'blur' }],
        attachmentList: [{ required: true, message: '请上传附件', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    }),
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  methods: {
    handleClose () {
      this.$emit('update:showDialog', false);
      this.filteredData = [];
    },
    selectChangeEvent ({ checked, row }) {
      row.checked = checked;
    },
    selectAllChange ({ checked, records }) {
      records.forEach((item) => {
        item.checked = checked;
      })
    },
    handleUploadExceed () {
      this.$message.error(`最多上传${this.numberLimit}个附件！`)
    },
    async httpRequestHandle (file) {
      // 校验大小
      const isGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit
      if (isGtLimit) {
        this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      const response = await upload('mm/attachment/modifyPaymentAttachment', file.file);
      if (response?.url) {
        this.formData.attachmentList.push({
          uid: file.file.uid,
          attachmentName: file.file.name,
          attachmentUrl: response.url
        })
        this.$refs.formRef.clearValidate('attachmentList')
      } else {
        // this.$message.error('上传失败！')
      }
      loading.close()
    },
    // 删除文件
    handleRemove(file, fileList) {
      const idx = this.formData.attachmentList.findIndex(item => item.uid === file.uid)
      if (idx >= 0) {
        this.formData.attachmentList.splice(idx, 1)
      }
    },
    // handlePreview(file) {
    //   console.log(file)
    //   const fileItem = this.formData.attachmentList.find(item => item.uid === file.uid)
    //   if (fileItem) {
    //     window.open(fileItem.fileUrl)
    //   }
    // },
    async submit () {
      try {
        this.$refs.formRef.validate(async (valid) => {
          if (!valid) return
          if (this.tableData.some(row => !row.purchaseLastGrossMargin)) return this.$message.warning('请输入采购复核毛利率！')
          this.loading = true;
          const data = {
            updateUser: window.CUR_DATA.user?.name,
            row: this.tableData,
            reason: this.formData.reason,
            attachmentList: this.formData.attachmentList
          }
          const res = await submitPaymentTerm(data);
          this.loading = false;
          if (res?.code === 0) {
            this.$message.success('提交成功！');
            this.dlgVisible = false;
          } else {
            this.$message.$confirm(res?.msg || '提交失败！', '提示', {
              confirmButtonText: '确认',
              cancelButtonText: '取消',
              type: 'error'
            })
            }
          })
      } catch (err) {
        console.log(err);
      }
    }
  }
}
</script>

<style lang="scss">
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
