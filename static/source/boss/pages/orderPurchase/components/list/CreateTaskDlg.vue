<template>
  <el-dialog
    title="协同信息确认"
    :visible.sync="dlgVisible"
    width="1200px"
    append-to-body
    @closed="handleClose"
  >
    <div style="height: 460px;">
      <el-form>
        <el-form-item label="SKU编号">
          <div style="display: flex; width: 50%;">
            <el-input v-model="skuNos" placeholder="多个SKU以空格分隔" clearable @change="changeSkuNos"></el-input>
            <el-button type="primary" @click="search" style="margin-left: 10px;">搜索</el-button>
          </div>
        </el-form-item>
      </el-form>
      <vxe-grid :data="computedData" v-bind="gridOptions" @checkbox-change="selectChangeEvent" @checkbox-all="selectAllChange">
        <template #soItemConfirmQty="{ row }">
          <el-input-number v-model="row.soItemConfirmQty" :min="0" :step="1"></el-input-number>
        </template>
      </vxe-grid>
      <el-pagination
        class="pagination"
        :total="total"
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange" />
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="$emit('update:showDialog', false)">取消创建</el-button>
        <el-button @click="submit" type="primary">确认提交</el-button>
      </div>
    <el-dialog title="提醒" :visible.sync="submitWarningVisible" width="1200px" append-to-body>
      <div style="height: 460px;">
        <div>提交失败！请根据失败原因跳转勾选项后重新尝试提交~</div>
        <el-link v-if="failUrl" type="primary" :href="failUrl" target="_blank">查看失败详情></el-link>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="submitWarningVisible = false">确认</el-button>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { submitEvent } from '@/api/mm'

export default {
  props: {
    showDialog: {
      required: true,
      type: Boolean,
      default: false
    },
    tableData: {
      required: true,
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      gridOptions: {
        ref: 'xGrid',
        border: true,
        height: 400,
        align: 'center',
        keepSource: true,
        checkboxConfig: {
          reserve: true
        },
        columns: [
          { type: 'checkbox', width: 50, fixed: 'left' },
          { field: 'poNo', title: 'PO单号', width: 120, fixed: 'left' },
          { field: 'poItemNo', title: 'PO行号', width: 80, fixed: 'left' },
          { field: 'soNo', title: 'SO单号', width: 150, fixed: 'left' },
          { field: 'soItemNo', title: 'SO行号', width: 80, fixed: 'left' },
          { field: 'skuNo', title: 'SKU编码', width: 120, fixed: 'left' },
          { field: 'materialDescription', title: '物料描述', width: 200, showOverflow: true },
          { field: 'soItemQty', title: 'SO行数量', width: 120 },
          { field: 'soItemConfirmQty', title: '确认数量', width: 150, slots: { default: 'soItemConfirmQty' } },
          { field: 'salesUnit', title: '销售单位', width: 120 },
          { field: 'customerServiceName', title: '客服', width: 120 },
          { field: 'purchaseName', title: '采购员', width: 120 },
          { field: 'supplierNo', title: '供应商编码', width: 120 },
          { field: 'supplierName', title: '供应商名称', width: 120 }
        ]
      },
      // total: 0,
      currentPage: 1,
      pageSize: 10,
      skuNos: '',
      filteredData: [],
      submitWarningVisible: false,
      failUrl: '',
      isFiltered: false // 是否展示过滤过的数据
    }
  },
  computed: {
    dlgVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    computedData () {
      let data = this.tableData;
      if (this.isFiltered) {
        data = this.filteredData
      }
      return data.slice((this.currentPage - 1) * this.pageSize, (this.currentPage - 1) * this.pageSize + this.pageSize)
    },
    total () {
      if (this.filteredData?.length) {
        return this.filteredData.length
      }
      return this.tableData.length || 0
    }
  },
  methods: {
    handleClose () {
      this.$emit('update:showDialog', false);
      this.skuNos = '';
      this.filteredData = [];
    },
    handleSizeChange (val) {
      this.pageSize = val;
    },
    handleCurrentChange (val) {
      this.currentPage = val;
    },
    selectChangeEvent ({ checked, row }) {
      row.checked = checked;
    },
    selectAllChange ({ checked, records }) {
      records.forEach((item) => {
        item.checked = checked;
      })
    },
    search () {
      const skuList = (this.skuNos || '').split(/\s+|,|，/).filter((i) => i);
      this.filteredData = this.tableData.filter(item => skuList.includes(item.skuNo));
      this.isFiltered = true;
    },
    changeSkuNos () {
      if (!this.skuNos) {
        this.isFiltered = false;
      }
    },
    async submit () {
      try {
        const rows = this.tableData.filter(item => item.checked)
        if (!rows.length) return this.$message.warning('请勾选需要提交的行！')
        if (rows.some(row => !row.soItemConfirmQty || row.soItemConfirmQty <= 0 || row.soItemConfirmQty > row.soItemQty)) return this.$message.warning('确认数量必须符合”0＜确认数量≤SO行数量”！')
        const data = {
          updateUser: window.CUR_DATA.user?.name,
          rows
        }
        const res = await submitEvent(data);
        if (res?.data?.failCount === 0) {
          this.$message.success('提交成功！');
          this.dlgVisible = false;
        } else {
          this.submitWarningVisible = true;
          this.failUrl = res?.data?.failUrl;
        }
      } catch (err) {
        console.log(err);
      }
    }
  }
}
</script>

<style lang="scss">
.pagination {
  text-align: right;
}
</style>
