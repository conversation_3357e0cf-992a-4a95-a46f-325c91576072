<template>
<el-select
  ref="supplier"
  v-model="supplier"
  placeholder="选择供应商"
  filterable
  remote
  reserve-keyword
  style="width:100%"
  value-key="supplierNo"
  :disabled="disabled"
  :clearable="clearable"
  :multiple="multiple"
  :collapseTags="collapseTags"
  :remote-method="remoteMethod"
  :loading="loading"
  default-first-option
  @change="handleChange"
>
  <el-option
    v-for="(item, index) in supplierList"
    :key="item.supplierNo"
    :label="item.supplierName"
    :value="item"
    :disabled="index === 0 || (isCanDisabled && item.isValid === 0)"
  >
    <div
      class="supplier-select-item"
      :style="supplierSelectItem(index)"
    >
      <div style="width: 100px;">{{ item.supplierNo || '--' }}</div>
      <div style="width: 200px;">{{ item.supplierName || '--' }}</div>
    </div>
  </el-option>
</el-select>
</template>

<script>
import { getSupplierOptions } from '@/api/mm';
export default {
  name: 'SelectSupplier',
  props: {
    data: [Object, String, Array],
    disabled: Boolean,
    defaultValue: Boolean,
    clearable: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    collapseTags: {
      type: Boolean,
      default: false
    },
    isCanDisabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      loading: false,
      supplierList: []
    }
  },
  created () {
    if (this.defaultValue) {
      this.supplierList = [
        {
          supplierNo: '供应商编码',
          supplierName: '供应商名称'
        }
      ]
      if (this.data && this.data.supplierNo) {
        this.supplierList.push({
          supplierNo: this.data.supplierNo,
          supplierName: this.data.supplierName
        })
      }
    }
  },
  computed: {
    supplier: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    supplierSelectItem (index) {
      return {
        fontWeight: index === 0 ? 'bold' : 'normal',
        display: 'flex'
      }
    },
    remoteMethod (val) {
      if (val) {
        this.loading = true
        getSupplierOptions({
          supplierNoOrName: val
        }).then(data => {
          if (!data) return
          this.supplierList = [
            {
              supplierNo: '供应商编码',
              supplierName: '供应商名称'
            },
            ...data
          ]
        }).finally(() => {
          this.loading = false
        })
      } else {
        this.supplierList = [
          {
            supplierNo: '供应商编码',
            supplierName: '供应商名称'
          }
        ]
      }
    },
    handleChange (val) {
      this.$emit('change', val)
    }
  }
}
</script>

<style lang="scss" scoped>
.supplier-select-item {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
  }
}
</style>
