<template>
  <el-dialog
    :visible="showDialog"
    title="订单删除" width="600px"
    :before-close="closeDialog">
    <div class="container">
      <div class="body">
        <el-form :model="deleteForm" :rules="rules" label-width="100px" ref="deleteForm">
          <el-form-item label="请选择原因" prop="deleteReasonCode" >
            <el-cascader
              v-model="deleteForm.deleteReasonCode"
              :options="deleteReasonOptions"
              @change="handleChange"
              ></el-cascader>
          </el-form-item>
          <el-form-item label="上传文件" prop="upload" >
          <el-upload
            ref="upload"
            action="/upload"
            style="display: inline-block"
            drag
            :accept="acceptFileType.poCommonType"
            :before-upload="(file) => $validateFileType(file, acceptFileType.poCommonType)"
            :on-remove="handleRemove"
            :show-file-list="true"
            :multiple="false"
            :file-list="deleteForm.attachmentList"
            :http-request="httpRequestHandle"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
        </el-form-item>
        </el-form>
        <div v-if="showTip" class="remind">注意：如PO对应SO修改，请在销售订单修改成功并产生负需求后再操作标准转直发！</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit('deleteForm')">确 定</el-button>
        <el-button @click="closeDialog">取 消</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { mapState } from 'vuex'
import { remove } from 'lodash'
import { upload } from '@/utils/upload'
import * as shortid from 'shortid'

export default {
  name: 'DeleteDialog',
  props: {
    showDialog: Boolean,
    fileList: Array,
    showTip: Boolean
  },
  computed: {
    ...mapState({
      deleteReasonOptions: state => state.orderPurchase.deleteReasonOptions,
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    }),
    showDialogVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    showFileList() {
      const fileList = this.fileList || []
      return fileList.map(item => {
        return {
          uid: shortid.generate(),
          url: item.fileUrl,
          name: item.fileName
        }
      })
    }
  },
  watch: {
    showDialog (val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.deleteForm.clearValidate();
        })
        this.deleteForm = {
          attachmentList: [],
          deleteReasonCode: '',
          deleteReason: ''
        }
      }
    }
  },
  data () {
    return {
      deleteForm: {
        attachmentList: [],
        deleteReason: '',
        deleteReasonCode: ''
      },
      rules: {
        deleteReasonCode: [
          { required: true, message: '请选择原因', trigger: 'change' }
        ]
      }
    }
  },
  created () {
    this.$store.dispatch('orderPurchase/getDeleteReasonOptions')
  },
  methods: {
    handleChange(val) {
      this.deleteForm.deleteReason = val[0] + '/' + val[1]
    },
    // 删除文件
    handleRemove(file, fileList) {
      remove(this.deleteForm.attachmentList, function (item) {
        return item.uid === file.uid
      });
    },
    // 上传文件
    async httpRequestHandle (file) {
      // 校验大小
      // const i//sGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit
      // if (isGtLimit) {
      // this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
      // return
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      const response = await upload('mm/attachment/deleteAttachment', file.file);
      if (response?.url) {
        this.deleteForm.attachmentList.push({
          uid: file.file.uid,
          fileName: file.file.name,
          fileUrl: response.url,
          url: response.url,
          name: file.file.name
        })
      } else {
        // this.$message.error('上传失败！')
      }
      loading.close()
    },
    submit (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.showDialogVisible = false
          this.deleteForm.attachmentList = this.deleteForm.attachmentList.map(item => {
            return {
              fileName: item.fileName,
              fileUrl: item.fileUrl
            }
          })
          this.$emit('getAttachmentList', this.deleteForm)
        } else {
          return false;
        }
      });
    },
    handleClick () {},
    closeDialog () {
      this.showDialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
.remind{
  color: red;
  font-weight: bold;
  text-align: center;
}
.dialog-footer{
  margin: 20px 0px 0px;
  display: flex;
  justify-content: center;
}
</style>
