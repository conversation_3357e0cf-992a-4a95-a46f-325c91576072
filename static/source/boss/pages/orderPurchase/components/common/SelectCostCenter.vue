<template>
<el-select
  v-model="costCenter"
  placeholder="选择成本中心"
  filterable
  remote
  reserve-keyword
  clearable
  style="width:100%"
  size="mini"
  :disabled="disabled"
  :remote-method="remoteMethod"
  :loading="loading"
  @change="handleSelectChange"
>
  <el-option
    v-for="item in options"
    :key="item.costCenter"
    :label="item.costCenter + ' ' + item.description"
    :value="item.costCenter">
  </el-option>
</el-select>
</template>

<script>
import { getCostCenter } from '@/api/mm';
export default {
  name: 'SelectCostCenter',
  props: ['row', 'data', 'disabled', 'companyCode'],
  data() {
    return {
      loading: false,
      options: []
    }
  },
  computed: {
    costCenter: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      if (val) {
        this.$emit('change', val, this.row)
      }
    },
    remoteMethod (val) {
      if (val) {
        this.loading = true
        getCostCenter({
          companyCode: this.companyCode,
          costCenter: val
        }).then(data => {
          this.loading = false
          if (data) {
            this.options = data
          }
        })
      } else {
        this.options = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
  }
}
</style>
