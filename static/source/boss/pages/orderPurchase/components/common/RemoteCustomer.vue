<template>
  <el-select
    :value="value"
    filterable
    remote
    default-first-option
    clearable
    :placeholder="placeholder"
    :remote-method="remoteCustomerMethod"
    :loading="customerNoLoading"
    @change="handleSelectChange"
  >
    <el-option
      v-for="item in customerNoOptions"
      :key="item.value"
      :label="item.label"
      :value="getLabel? item.label: item.value"
    />
  </el-select>
</template>
<script>
import { getCustomerList } from './api/remoteSearchFields'
export default {
  name: 'RemoteCustomer',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入关键词'
    },
    value: [ String, Number ],
    getLabel: Boolean
  },
  data () {
    return {
      customerNoOptions: [],
      customerNoLoading: false
    }
  },
  methods: {
    handleSelectChange (val) {
      this.$emit('change', val)
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        getCustomerList({ customer_name: key }).then(res => {
          if (res && res.status == 200 && (res = res.data) && res.code === 200 && res.data.contents && res.data.contents.length > 0) {
            this.customerNoOptions = res.data.contents.map(item => ({
              value: item.customerNo,
              label: item.customerName
            }))
          } else {
            this.customerNoOptions = []
          }
        })
          .catch(err => {
            console.log(err)
          })
          .finally(() => {
            this.customerNoLoading = false
          })
      } else {
        this.customerNoOptions = []
      }
    }
  }
}
</script>
