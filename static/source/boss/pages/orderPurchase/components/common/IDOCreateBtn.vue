<template>
  <el-button size="mini" type="primary" @click="handleCreateIDO" :disabled="disabled">创建内向交货单</el-button>
</template>

<script>
import { idoEnter } from '@/api/mm'
export default {
  name: 'IDOCreateBtn',
  props: ['selections', 'disabled'],
  methods: {
    handleCreateIDO () {
      if (this.selections && this.selections.length > 0) {
        const id = this.selections.map(item => item.orderNo)
        idoEnter(id).then(data => {
          if (data) {
            const ids = id.join('_')
            this.$router.push({
              path: `/inboundDeliveryOrder/create/${ids}`
            })
          }
        })
      } else {
        this.$message.warning('请选择订单！')
      }
    }
  }
}
</script>
