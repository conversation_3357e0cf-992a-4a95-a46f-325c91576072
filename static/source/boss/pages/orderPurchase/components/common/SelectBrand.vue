<template>
  <el-select
    v-model="skuNo"
    placeholder="请输入品牌"
    filterable
    remote
    reserve-keyword
    clearable
    default-first-option
    style="width:100%"
    :disabled="disabled"
    :multiple="multiple"
    :remote-method="remoteMethod"
    :loading="loading"
    :collapseTags="collapseTags"
    @change="handleSelectChange"
  >
    <el-option
      v-for="item in resList"
      :key="item"
      :label="item"
      :value="item">
    </el-option>
  </el-select>
</template>

<script>
import { getBrandLike } from '@/api/mm';
import { spDebounce } from '@/utils/index';
export default {
  name: 'SelectBrand',
  props: ['data', 'disabled', 'multiple', 'collapseTags'],
  data() {
    return {
      loading: false,
      resList: []
    }
  },
  created () {
    this.remoteMethod = spDebounce(this.remoteMethod, 800, this)
  },
  computed: {
    skuNo: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      this.$emit('change', val)
    },
    remoteMethod (val) {
      if (val) {
        this.loading = true;
        getBrandLike({
          brandNameByLike: val,
          count: 10
        }).then(data => {
          this.loading = false
          if (data) {
            this.resList = [
              ...data
            ]
          }
        })
      } else {
        this.resList = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
