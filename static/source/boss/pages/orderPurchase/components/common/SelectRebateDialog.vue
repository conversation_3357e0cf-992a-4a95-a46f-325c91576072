<!--
 * @Author: l<PERSON>zhi<PERSON>
 * @Date: 2024-02-29 15:52:07
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-03-29 10:12:31
 * @Description: 选择返利单弹窗
-->
<template>
  <span>
    <el-button
      type="primary"
      @click="openRebateDialog"
      >{{ isReadonly ? '查看详情' : '选择返利单' }}</el-button
    >

    <el-dialog
      width="70%"
      :title="isReadonly ? '查看详情' : '选择返利单'"
      append-to-body
      :visible.sync="visible"
      @closed="clearSelected"
    >
      <vxe-table
        ref="rebateTableEdit"
        resizable
        border
        highlight-hover-row
        show-overflow
        :loading="tableLoading"
        :max-height="600"
        align="center"
        size="small"
        :scroll-y="{ gt: 0 }"
        row-id="id"
        :data="rebateList"
      >
        <vxe-column
          v-if="!isReadonly"
          fixed="left"
          :width="60"
          align="center"
          :show-overflow="false"
          class-name="checkbox-column"
        >
          <template slot-scope="{ row }">
            <el-checkbox :key="row.id" v-model="row.isChecked" :disabled="row.unchangableRebate > 0"></el-checkbox>
          </template>
        </vxe-column>
        <vxe-column field="orderNo" title="返利单" :width="180"></vxe-column>
        <vxe-column
          field="toVerificationAmount"
          title="待核销金额(含税)"
        ></vxe-column>
        <vxe-column field="usedRebateAmount" title="本次核销金额" :width="180">
          <template slot-scope="{ row }">
            <span v-if="isReadonly">{{ row['usedRebateAmount'] }}</span>
            <el-input-number
              v-else
              v-model="row['toUseRebate']"
              size="mini"
              :precision="2"
              :disabled="!row.isChecked"
              :min="0"
              :max="row['toVerificationAmount']"
              @change="(val) => validateInput(val, row.toVerificationAmount)"
            />
          </template>
        </vxe-column>
        <vxe-column field="unchangableRebate" title="收货/免费行占用金额"></vxe-column>
        <vxe-column field="rebateFormText" title="返利形式"></vxe-column>
        <vxe-column field="materialGroupName" title="物料组"></vxe-column>
        <vxe-column field="validDate" title="返利有效期">
          <template slot-scope="{ row }">
            <span>{{
              row.validDateStart
                ? `${row.validDateStart} ~ ${row.validDateEnd}`
                : ""
            }}</span>
          </template>
        </vxe-column>
        <vxe-column
          field="supplierApproveTime"
          title="供应商确认时间"
        ></vxe-column>
        <vxe-column
          field="singleOrderMaxOff"
          title="单笔订单返利抵扣上限(%)"
        ></vxe-column>
        <vxe-column
          field="orderAmountExceedsDeductionLimit"
          title="订单金额超过可抵扣(元)"
        ></vxe-column>
        <vxe-column field="rebateUseCondition" title="返利使用条件">
          <template slot-scope="{ row }">
            <el-popover
              placement="top"
              :title="row.orderNo + '使用条件'"
              width="270"
              trigger="hover"
              :content="row.rebateUseCondition"
            >
              <el-button slot="reference" type="text"> 查看 </el-button>
            </el-popover>
          </template>
        </vxe-column>
      </vxe-table>
      <!-- <vxe-pager
        v-if="!isReadonly"
        :layouts="[
          'Sizes',
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'FullJump',
          'Total',
        ]"
        :border="true"
        :current-page.sync="tablePage.currentPage"
        :page-size.sync="tablePage.pageSize"
        :total="tablePage.total"
        @page-change="handlePageChange"
      >
      </vxe-pager> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </span>
    </el-dialog>
  </span>
</template>
<script>
import { uniq, isEmpty } from 'lodash';
import { getRebateList } from '@/api/mm';

export default {
  props: ['isEditDelayedPaymentPo', 'purchaseData', 'isDetailDialog'],
  data() {
    return {
      visible: false,
      rebateList: [],
      // tablePage: {
      //   total: 0,
      //   currentPage: 1,
      //   pageSize: 10
      // },
      tableLoading: false,
      selectedRebates: []
    };
  },
  computed: {
    isReadonly() {
      return this.isEditDelayedPaymentPo || this.isDetailDialog
    }
  },
  methods: {
    validateInput(val, max) {
      if (val && max && val > max) {
        this.$message.error('本次核销金额不可大于待核销金额，请修改！');
      }
    },
    clearSelected() {
      console.log('rebate dialog closed');
      // this.selectedRebates = []
    },
    openRebateDialog() {
      this.visible = true;

      this.tableLoading = true;
      let data = {
          poNo: this.purchaseData.orderNo,
          factory: this.purchaseData.companyCode,
          materialGroupIdList: uniq(
            this.purchaseData.itemList
              .filter(item => !item.isDeleted)
              .map((item) => item.materialGroupId)
              .filter(Boolean)
          ),
          page: 1,
          pageSize: 10000, // 不做分页处理
          supplierNo: this.purchaseData.supplierNo
      }
      // readonly时只获取已分摊过的返利单
      const selectedIds = this.purchaseData.usedRebaseVoucherList?.map(item => item.rebateVoucherId) || []
      if (this.isDetailDialog && isEmpty(selectedIds)) {
        this.tableLoading = false
        return
      }
      if (this.isReadonly) {
        data = {
          ...data,
          idList: selectedIds
        }
      }
      // 不分页，一次获取全部返利单
      getRebateList(data).then(res => {
          // 已选展示在最上面
          const sel = res.data.filter(item => selectedIds.includes(item.id)).map(item => {
            const usedRebateOnHeader = this.purchaseData.usedRebaseVoucherList?.find(i => i.rebateVoucherId === item.id)
            if (usedRebateOnHeader) {
              return {
                ...item,
                toUseRebate: usedRebateOnHeader.toUseRebate,
                usedRebateAmount: usedRebateOnHeader.usedRebateAmount,
                isChecked: true
              }
            } else {
              return item
            }
          })
          const unSelected = res.data.filter(item => !selectedIds.includes(item.id)).map(item => {
            return {
              ...item,
              isChecked: false
            }
          })
          // 详情弹窗时，只需要展示已选的返利单
          // this.selectedRebates = [...sel]
          this.rebateList = [...sel, ...unSelected];
          console.log(this.purchaseData.usedRebaseVoucherList, sel);
          // this.$refs['rebateTableEdit'].setCheckboxRow(
          //   sel,
          //   true
          // );
          // this.tableLoading = false
      }).finally(() => {
        this.tableLoading = false
      })
    },
    getRebateData() {
      this.tableLoading = true;
      getRebateList({
        poNo: this.purchaseData.orderNo,
        factory: this.purchaseData.companyCode,
        materialGroupIdList: uniq(
          this.purchaseData.itemList
            .map((item) => item.materialGroupId)
            .filter(Boolean)
        ),
        page: 1,
        pageSize: 10000,
        supplierNo: this.purchaseData.supplierNo
      })
        .then((res) => {
          console.log(res);
          this.rebateList = res.data.map((item) => {
            const selectedRow = this.selectedRebates.find(
              (sel) => sel.id === item.id
            );
            if (selectedRow) return selectedRow;
            return item;
          });
          // this.tablePage = {
          //   currentPage: res.currPage,
          //   pageSize: res.pageSize,
          //   total: res.count
          // };
          this.$refs['rebateTableEdit'].setCheckboxRow(
            this.selectedRebates,
            true
          );
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // handlePageChange({ currentPage, pageSize, e }) {
    //   console.log('page cchange', e);

    //   this.tablePage.currentPage = currentPage;
    //   this.tablePage.pageSize = pageSize;
    //   this.getRebateData();
    // },
    handleOk() {
      if (this.isReadonly) {
        this.visible = false
        return
      }
      // if (this.selectedRebates.some((item) => !item.toUseRebate)) {
      //   return this.$message.error('请输入本次核销金额');
      // }
      const selectedRebates = this.rebateList.filter(item => item.isChecked)
      console.log(selectedRebates);
      if (
        selectedRebates.some(
          (item) => item.toUseRebate > item.toVerificationAmount
        )
      ) {
        return this.$message.error('核销金额大于待核销金额，请检查');
      }
      const usedRebaseVoucherList = selectedRebates.map((item) => {
        return {
          poNo: this.purchaseData.orderNo || '',
          rebateVoucherId: item.id,
          rebateVoucherNo: item.orderNo,
          toUseRebate: item.toUseRebate,
          usedRebateAmount: item.usedRebateAmount
        };
      });
      this.$set(
        this.purchaseData,
        'usedRebaseVoucherList',
        usedRebaseVoucherList
      );
      this.visible = false;
    },
    onTableSelectionChange({ checked, records, row }) {
      // console.log(data);
      this.selectedRebates = records
      row.isChecked = checked
      // this.selectedRebates = [ ...records, ...reserves ]
      console.log((this.selectedRebates.map(i => i.orderNo)));
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep *{
  .vxe-cell{
    &.c--ellipsis{
      padding: 2px 0px;
      margin-left: -8px;
    }
    &.c--tooltip{
      padding: 2px 0px;
      margin-left: -8px;
    }
  }

}
</style>
