<template>
  <div class="change-channel">
    <el-dialog
      :visible="showDialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="换渠道" width="90%"
      :before-close="closeDialog">
      <div class="container" v-loading="loading.dialogLoading">
        <div class="body">
          <div class="sku" v-if="changeChannelData &&changeChannelData.currentSku">
            <div class="sku-title">当前SKU</div>
            <div class="current-sku">
              <div>
                {{changeChannelData.currentSku.skuCode}} &nbsp;&nbsp;&nbsp;&nbsp;
                {{changeChannelData.currentSku.name}}&nbsp;&nbsp;&nbsp;&nbsp;
                商品来源：{{changeChannelData.currentSku.sourceTypeName}}&nbsp;&nbsp;&nbsp;&nbsp;
                当前PO供应商编码：{{changeChannelData.supplierNo || ''}}&nbsp;&nbsp;&nbsp;&nbsp;
                物料描述：{{changeChannelData.currentSku.materialDescribe}}
              </div>
              <div class="add-sku">
                <el-button type="text" @click="handleAddSku">添加其他可用SKU</el-button>
              </div>
            </div>
            <div class="sku-table">
              <SkuTable
                :data="changeChannelData.diffSkuPriceList"
                :currentSkuSupplierCode="changeChannelData.supplierNo"
                :sourceType="changeChannelData.currentSku.sourceType"
                :sdiDict="sdiDictData"
                type="1"
                @changeSupplier="changeSupplier"
              />
            </div>
          </div>
          <div class="sku" v-if="changeChannelData && (changeChannelData.commissionSkuList && changeChannelData.commissionSkuList.length > 0) || (changeChannelData.similarDiffSkuList && changeChannelData.similarDiffSkuList.length > 0)">
            <div class="sku-title commission-title">相同SKU可用供应商</div>
            <div class="commission-sku">
              <div v-for="(item, index) in changeChannelData.commissionSkuList" :key="index + (item.commissionSkuInfo.skuCode || '') " class="commission-item">
                <div class="current-sku" v-if="item.commissionSkuInfo">
                  <div>
                    {{item.commissionSkuInfo.skuCode}} &nbsp;&nbsp;&nbsp;&nbsp;
                    {{item.commissionSkuInfo.name}}&nbsp;&nbsp;&nbsp;&nbsp;
                    商品来源：{{item.commissionSkuInfo.sourceTypeName}}&nbsp;&nbsp;&nbsp;&nbsp;
                    物料描述：{{item.commissionSkuInfo.materialDescribe}}
                  </div>
                </div>
                <div class="sku-table">
                  <SkuTable
                    :data="[item.commissionSkuPrice]"
                    :diffSkuPriceList="changeChannelData.diffSkuPriceList"
                    :currentSkuSupplierCode="changeChannelData.supplierNo"
                    :sourceType="item.commissionSkuInfo.sourceType"
                    :sdiDict="sdiDictData"
                    type="2"
                    @changeSupplier="changeSupplier"
                  />
                </div>
              </div>
              <div v-for="(item, index) in changeChannelData.similarDiffSkuList" :key="index + (item.diffSkuInfo.skuCode || '')" class="commission-item">
                <div class="current-sku" v-if="item.diffSkuInfo">
                  <div>
                    {{item.diffSkuInfo.skuCode}} &nbsp;&nbsp;&nbsp;&nbsp;
                    {{item.diffSkuInfo.name}}&nbsp;&nbsp;&nbsp;&nbsp;
                    商品来源：{{item.diffSkuInfo.sourceTypeName}}&nbsp;&nbsp;&nbsp;&nbsp;
                    物料描述：{{item.diffSkuInfo.materialDescribe}}
                  </div>
                </div>
                <div class="sku-table">
                  <SkuTable
                    :data="item.diffSkuPrice"
                    :diffSkuPriceList="changeChannelData.diffSkuPriceList"
                    currentSkuSupplierCode=""
                    :sourceType="item.diffSkuInfo.sourceType"
                    :sdiDict="sdiDictData"
                    type="2"
                    @changeSupplier="changeSupplier"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="sku" v-if="addSkuList && addSkuList.length > 0">
            <div class="sku-title commission-title">采购添加SKU</div>
            <div class="commission-sku">
              <div v-for="(item, index) in addSkuList" :key="index + (item.addedSkuInfo.skuCode || '')" class="commission-item">
                <div class="current-sku" v-if="item.addedSkuInfo">
                  <div>
                    {{item.addedSkuInfo.skuCode}} &nbsp;&nbsp;&nbsp;&nbsp;
                    {{item.addedSkuInfo.name}}&nbsp;&nbsp;&nbsp;&nbsp;
                    商品来源：{{item.addedSkuInfo.sourceTypeName}}&nbsp;&nbsp;&nbsp;&nbsp;
                    物料描述：{{item.addedSkuInfo.materialDescribe}}
                  </div>
                </div>
                <div class="sku-table">
                  <SkuTable
                    :data="item.addedSkuPriceList"
                    :diffSkuPriceList="changeChannelData.diffSkuPriceList"
                    currentSkuSupplierCode=""
                    :sourceType="item.addedSkuInfo.sourceType"
                    :sdiDict="sdiDictData"
                    type="2"
                    @changeSupplier="changeSupplier"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeDialog">关  闭</el-button>
        </div>
      </div>
      <el-dialog
        width="30%"
        title="添加可用SKU"
        :visible.sync="skuDialog"
        append-to-body>
        <el-form :model="addSkuForm" :rules="rules" label-width="100px" ref="addSkuForm" v-loading="loading.addSkuLoading">
          <el-form-item label="SKU编码:" prop="skuNo">
            <el-input v-model="addSkuForm.skuNo" clearable></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="skuDialog = false">取 消</el-button>
          <el-button type="primary" @click="addSku('addSkuForm')">确 定</el-button>
        </div>
      </el-dialog>
      <el-dialog
        width="30%"
        title="切换供应商"
        :visible.sync="approveSupplierDialog"
        append-to-body>
        <el-form :model="approveSupplierForm"  label-width="150px" ref="approveSupplier" v-loading="loading.approveLoading">
          <el-form-item label="更新采购价格:">
            <el-input-number
              v-model="approveSupplierForm.unitPriceNew"
              size="mini"
              :min="0"
              :max="Number.MAX_SAFE_INTEGER"
              :precision="2"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="approveSupplierDialog = false">取 消</el-button>
          <el-button type="primary" @click="approveSupplier('approveSupplier')">确 定</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import SkuTable from './SkuTable.vue'
import { addDisplaceSku, replaceSupplier, displaceApprove, sdiDict, displaceVpiPrice } from '@/api/mm'

export default {
  name: 'ChangeChannelDialog',
  props: {
    showDialog: Boolean,
    changeChannelData: Object
  },
  components: { SkuTable },
   data () {
    return {
      addSkuList: [],
      skuDialog: false,
      approveData: {},
      approveSupplierDialog: false,
      loading: {
        addSkuLoading: false,
        approveLoading: false,
        dialogLoading: false
      },
      addSkuForm: {
        skuNo: ''
      },
      approveSupplierForm: {
        unitPriceNew: ''
      },
      sdiDictData: {
        recommendingLevel: {},
        operateType: {},
        supplierFactory: {}
      },
      rules: {
        skuNo: [
          { required: true, message: '请输入SKU编码', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      deleteReasonOptions: state => state.orderPurchase.deleteReasonOptions
    }),
    showDialogVisible: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  watch: {
    showDialog (val) {
      if (val) {
        this.addSkuList = []
        this.addSkuForm.skuNo = ''
        this.approveSupplierForm.unitPriceNew = ''
      }
    }
  },
  async created () {
    await sdiDict({ dictType: 'recommendingLevel' }).then(res => {
      this.sdiDictData.recommendingLevel = res
    })
    await sdiDict({ dictType: 'operateType' }).then(res => {
      this.sdiDictData.operateType = res
    })
  },
  methods: {
    closeDialog () {
      this.showDialogVisible = false
    },
    handleAddSku() {
      this.skuDialog = true
      // if (this.$refs.addSkuForm) {
      //   this.$refs.addSkuForm.resetFields()
      // }
    },
    async changeSupplier(type, skuNo, row, sourceType) {
      const { poNo, poItemNo } = this.changeChannelData
      const { skuCode, supplierMode } = this.changeChannelData.currentSku
      const data = {
        poNo,
        poItemNo,
        factoryCode: this.changeChannelData.factoryCode || '',
        factoryCodeNew: row.factoryCode,
        skuCodeNew: skuNo,
        skuCode,
        supplierCode: this.changeChannelData.supplierNo || '',
        supplierMode,
        supplierCodeNew: row.supplierCode,
        updateUser: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
      }
      if (type === '1') {
        this.$alert('确定切换此供应商？', '提示', {
          confirmButtonText: '确定',
          callback: async action => {
            if (action === 'confirm') {
              this.loading.dialogLoading = true
              const res = await replaceSupplier(data)
              this.loading.dialogLoading = false
              if (res?.code === 0) {
                this.closeDialog()
                // 刷新
                this.$emit('refresh')
                this.$message.success('切换供应商成功')
              } else {
                this.$message.error(res.msg || '切换供应商失败')
              }
            }
          }
        });
      } else {
        if (sourceType === '2') {
          const params = {
            factoryCode: row.factoryCode,
            skuNo,
            supplierNo: row.supplierCode
          }
          const displaceVpiPriceRes = await displaceVpiPrice(params)
          if (displaceVpiPriceRes?.code === 0 && displaceVpiPriceRes?.data?.purchasePrice) {
            this.approveSupplierForm.unitPriceNew = displaceVpiPriceRes.data.purchasePrice
          } else {
            this.approveSupplierForm.unitPriceNew = 0
          }
        } else {
          this.approveSupplierForm.unitPriceNew = 0
        }
        this.approveSupplierDialog = true
        this.approveData = row
      }
    },
    async addSku(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          const findSku = this.addSkuList.find(item => item?.addedSkuInfo?.skuCode === this.addSkuForm.skuNo) || this.changeChannelData.diffSkuPriceList.find(item => item?.skuCode === this.addSkuForm.skuNo) || this.changeChannelData.commissionSkuList.find(item => item?.commissionSkuInfo?.skuCode === this.addSkuForm.skuNo) || this.changeChannelData.similarDiffSkuList.find(item => item?.diffSkuInfo?.skuCode === this.addSkuForm.skuNo)
          if (findSku) {
            this.$message.error('SKU已添加！')
            return
          }
          const { poNo, poItemNo } = this.changeChannelData
          const data = {
            poNo,
            skuNo: this.addSkuForm.skuNo,
            poItemNo,
            updateUser: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
          }
          this.loading.addSkuLoading = true
          const res = await addDisplaceSku(data)
          this.loading.addSkuLoading = false
          if (res?.code === 0) {
            this.addSkuList.push(res.data)
            this.$message.success('获取sku成功')
            this.skuDialog = false
          } else {
            this.$message.error(res.msg || '获取sku失败')
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    async approveSupplier() {
      const { poNo, poItemNo } = this.changeChannelData
      const { skuCode, supplierMode } = this.changeChannelData.currentSku
      const data = {
        poNo,
        poItemNo,
        factoryCode: this.changeChannelData.factoryCode || '',
        factoryCodeNew: this.approveData.factoryCode,
        skuCodeNew: this.approveData.skuCode,
        skuCode,
        supplierCode: this.changeChannelData.supplierNo || '',
        supplierMode,
        supplierCodeNew: this.approveData.supplierCode,
        updateUser: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
      }
      if (this.approveSupplierForm.unitPriceNew) {
        data.unitPriceNew = this.approveSupplierForm.unitPriceNew
      }
      this.loading.approveLoading = true
      const res = await displaceApprove(data)
      this.loading.approveLoading = false
      this.approveSupplierDialog = false
      if (res?.code === 0) {
        this.$message.success('提交审批成功')
        this.closeDialog()
        // 刷新
        this.$emit('refresh')
      } else {
        this.$message.error(res.msg || '提交审批失败')
      }
    }
  }
}
</script>
<style lang="scss">
  .vxe-table--tooltip-wrapper {
    z-index: 10000 !important;
  }
</style>
<style lang="scss" scoped>
.dialog-footer{
  margin: 20px 0px 0px;
  display: flex;
  justify-content: center;
}
.sku-title {
  font-size: 16px;
  font-weight: 600;
  margin: 5px;
}
.current-sku {
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 5px;
}
.commission-sku {
  margin: 15px 0;
  overflow: auto;
  max-height: 240px;
  .commission-item {
    margin: 10px 0;
  }
}
.commission-title {
  margin-top: 20px;
}
</style>
