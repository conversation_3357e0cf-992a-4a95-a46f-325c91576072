<template>
  <el-select
    clearable
    filterable
    allow-create
    default-first-option
    placeholder="请选择"
    v-model="modelData"
    style="width:100%"
    multiple
    collapse-tags
    @change="handleChange"
  >
    <el-option
      v-for="item in optionLists"
      :key="`${type === 'warehouse' ? (item.id ? item.id : item.name ): '' + item.value + item.name }`"
      :label="`${type === 'warehouse' ? '' : item.value} ${item.name}`"
      :value="item.value"
    />
  </el-select>
</template>

<script>
export default {
  name: 'AllowCreateSelect',
  props: {
    data: [Object, String, Array],
    disabled: {
      type: Boolean,
      default: false
    },
    optionLists: [Array],
    type: [String]
  },
  data() {
    return {
    }
  },
  created () {
  },
  computed: {
    modelData: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    handleChange (val) {
      let values = []
      val.map(item => {
        let items = item.split(' ')
        items.map(innerItem => {
          values.push(innerItem)
        })
      })
      this.$emit('change', [...new Set(values)])
    }
  }
}
</script>
