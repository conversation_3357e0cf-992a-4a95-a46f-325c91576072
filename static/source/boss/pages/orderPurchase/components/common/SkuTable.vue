<template>
  <div>
     <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      highlight-hover-row
      show-header-overflow
      show-footer-overflow
      size="small"
      align="center"
      :scroll-x="{gt: -1}"
      :scroll-y="{gt: -1}"
      :max-height="gridHeight"
      id="sku-grid"
      :data="data"
      :columns="getColumns"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, enabled: true}">
      <template v-slot:toolbar_buttons>
        <div class="title-group">
          <slot name="button-group"></slot>
        </div>
      </template>
      <template #supplierCode="{ row }">
        <span> {{row.supplierCode}} {{row.supplierName}} </span>
      </template>
      <template #operateType="{ row }">
        {{mapValue('operateType', row.operateType, sdiDict)}}
      </template>
      <template #recommendingLevel="{ row }">
        {{mapValue('recommendingLevel', row.recommendingLevel, sdiDict)}}
      </template>
      <template #purchaseStatus="{ row }">
        <span :class="{'red-class': row.purchaseStatus === 4}"> {{value2Label('guaranteedStatus', row.purchaseStatus, statusOptions)}} </span>
      </template>
      <template #operation="{ row }">
        <el-button type="text" @click="changeSupplier(row)" :disabled="row.status !== 1" v-if="!(type === '1' && currentSkuSupplierCode === row.supplierCode) && row.purchaseStatus !== 4">切换此供应商</el-button>
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { oneProductColumns } from '@/pages/orderPurchase/constants'
export default {
  name: 'SkuTable',
  props: ['data', 'type', 'sdiDict', 'currentSkuSupplierCode', 'diffSkuPriceList', 'sourceType'],
  data () {
    return {
      gridHeight: 150,
      oneProductColumns,
      tableCustom: {
        storage: true
      },
      statusOptions: [{
        value: 1,
        label: '启用'
      }, {
        value: 4,
        label: '停用'
      }],
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      }
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    getColumns () {
      const columns = oneProductColumns
      columns.forEach(column => {
        if (column.enums) {
          column.slots = { default: column.enums }
        }
        if (column.isBoolean || column.slot) {
          column.slots = { default: column.field }
        }
        if (column.field === 'supplierCode') {
          column.slots = { default: column.field }
        }
        if (column.field === 'purchaseStatus') {
          column.slots = { default: column.field }
        }
        if (column.field === 'operation') {
          column.slots = { default: column.field }
        }
      })
      return columns
    }
  },
  methods: {
    changeSupplier(row) {
      // type 1--当前sku切换 2-其他审批
      if (this.diffSkuPriceList && this.diffSkuPriceList.length > 0 && this.type !== '1') {
        const supplier = this.diffSkuPriceList.find(item => item.supplierCode === row.supplierCode)
        if (supplier) {
          this.$emit('changeSupplier', '1', row.skuCode, row, this.sourceType)
        } else {
          this.$emit('changeSupplier', this.type, row.skuCode, row, this.sourceType)
        }
      } else {
        this.$emit('changeSupplier', this.type, row.skuCode, row, this.sourceType)
      }
    },
    mapDictValue (prop, value, dictData) {
      return dictData[prop] ? dictData[prop].find(item => item.value === String(value))?.name || '' : ''
    },
    value2Label(prop, value, options) {
      return options.find(item => String(item.value) === String(value))?.label || ''
    },
    mapValue (prop, value, dictData) {
      return dictData[prop][Number(value)]
    }
  }
}
</script>
<style scoped>
.red-class {
  color: brown;
}
</style>
