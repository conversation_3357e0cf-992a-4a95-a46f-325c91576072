<template>
<el-select
  v-model="skuNo"
  placeholder="选择SKU"
  filterable
  remote
  reserve-keyword
  clearable
  default-first-option
  style="width:100%"
  size="mini"
  :disabled="disabled"
  :remote-method="remoteMethod"
  :loading="loading"
  @change="handleSelectChange"
>
  <el-option
    v-for="(item, index) in skuList"
    :key="item.skuNo"
    :label="item.skuNo"
    :value="item.skuNo"
    :disabled="index===0"
  >
    <div
      class="selectItem"
      :style="{fontWeight:index===0?'bold':'normal', display: 'flex'}"
    >
      <div style="width: 100px;">{{ item.skuNo || '--' }}</div>
      <div style="width: 220px; overflow: auto;">{{ item.materialDescription || '--' }}</div>
    </div>
  </el-option>
</el-select>
</template>

<script>
import { getProductSkuLike } from '@/api/mm';
import debounce from 'lodash/debounce'

export default {
  name: 'SelectSku',
  props: ['data', 'disabled', 'extraQuery'],
  data() {
    return {
      loading: false,
      skuList: []
    }
  },
  created () {
    this.remoteMethod()
  },
  computed: {
    skuNo: {
      get () {
        return this.data
      },
      set (val) {
        this.$emit('update:data', val)
      }
    }
  },
  methods: {
    handleSelectChange (val) {
      let materialDescription = ''
      if (val) {
        const found = this.skuList.find(item => item.skuNo === val)
        if (found) {
          materialDescription = found.materialDescription
        }
      }
      this.$emit('change', val, materialDescription)
    },
    remoteMethod: debounce(function (val) {
      if (val) {
        this.loading = true
        let query = {
          vague: val
        }
        if (this.extraQuery && typeof this.extraQuery === 'object' && this.extraQuery !== null) {
          query = {
            ...query,
            ...this.extraQuery
          }
        }
        getProductSkuLike(query).then(data => {
          this.loading = false
          if (data) {
            this.skuList = [
              {
                skuNo: 'skuNo',
                materialDescription: '物料描述'
              },
              ...data
            ]
          }
        })
      } else {
        this.skuList = [
          {
            skuNo: 'skuNo',
            materialDescription: '物料描述'
          }
        ]
      }
    }, 1000)
  }
}
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    overflow: auto;
    width: 220px;
  }
}
</style>
