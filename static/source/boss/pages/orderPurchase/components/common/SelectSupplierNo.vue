<template>
<el-select
  :value="value"
  :size="size||'medium'"
  placeholder="选择供应商"
  filterable
  remote
  reserve-keyword
  clearable
  style="width:100%"
  :disabled="disabled"
  :remote-method="remoteMethod"
  :loading="loading"
  @change="handleChange"
>
  <el-option
    v-for="(item, index) in supplierList"
    :key="item.supplierNo"
    :label="item.supplierNo+' '+item.supplierName"
    :value="item.supplierNo"
    :disabled="index===0||item.isValid===0"
  >
    <div
      class="selectItem"
      :style="{fontWeight:index===0?'bold':'normal'}"
    >
      <div>{{ item.supplierNo || '--' }}</div>
      <div>{{ item.supplierName || '--' }}</div>
    </div>
  </el-option>
</el-select>
</template>

<script>
import { getSupplierOptions } from '@/api/mm';
export default {
  name: 'SelectSupplierNo',
  props: ['list', 'disabled', 'value', 'size'],
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      loading: false,
      supplierList: this.list || []
    }
  },
  methods: {
    remoteMethod (val) {
      if (val) {
        this.loading = true
        getSupplierOptions({
          supplierNoOrName: val
        }).then(data => {
          this.loading = false
          this.supplierList = [
            {
              supplierNo: '供应商编码',
              supplierName: '供应商名称'
            },
            ...data
          ]
        })
      } else {
        this.supplierList = [
          {
            supplierNo: '供应商编码',
            supplierName: '供应商名称'
          }
        ]
      }
    },
    handleChange (val) {
      this.$emit('change', val, this.supplierList)
    }
  }
}
</script>

<style lang="scss" scoped>
.selectItem {
  display: flex;
  div:nth-child(1) {
    width: 100px;
  }
  div:nth-child(2) {
    width: 220px;
  }
}
</style>
