<template>
  <el-form :model="purchasePrice" ref="purchasePrice" label-width="120px" label-suffix=":" :rules="rules">
    <el-row :gutter="20">
      <el-col v-for="item in field" :key="item.name" :span="item.span || 8">
        <el-form-item :label="item.name" :label-width="item.width ? item.width + 'px' : '120px'" :prop="item.prop">
          <el-input
            v-if="item.type==='input'"
            :disabled="disableCharge(item.prop) || disableByDeliveryStatus(item.prop)"
            v-model="purchasePrice[item.prop]"
            clearable maxlength="10"
            :placeholder="item.name"
          />
          <el-input-number
            v-if="item.type==='number'"
            :disabled="disableCharge(item.prop) || disableByDeliveryStatus(item.prop)"
            v-model="purchasePrice[item.prop]"
            clearable
            :placeholder="item.name"
            :precision="2"
            :min="0"
            style="width:100%"
          />
          <el-select
            v-if="item.type==='select'"
            :disabled="disableCharge(item.prop) || disableByDeliveryStatus(item.prop)"
            v-model="purchasePrice[item.prop]"
            @change="value => handleSelectChange(item.prop,value)"
            style="width:100%"
            clearable
            filterable
          >
            <el-option
              v-for="item in dictList[item.enums]"
              :key="item.value"
              :label="item.value+' '+item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <SelectSupplier
            v-if="item.type==='custom'"
            clearable
            defaultValue
            :disabled="disableCharge(item.prop) || disableByDeliveryStatus(item.prop)"
            :data.sync="purchasePrice[item.prop]"
            @change="(value) => handleSupplierChange(item.prop, value)"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'

export default {
  name: 'purchaseOrderItemPrice',
  data () {
    let data = { ...this.data }
    data = JSON.parse(JSON.stringify(data))
    let props = [ 'tariffAmount', 'saleTaxAmount', 'intlShippingAmount', 'latePaymentAmount', 'customsFeeAmount', 'otherAmount', 'premiumAmount' ]
    for (let prop of props) {
      if (!data[prop]) {
        data[prop] = undefined
      }
    }
    if (data.intlShippingType) {
      data.intlShippingType = Number(data.intlShippingType)
    }
    if (data.customsFeeType) {
      data.customsFeeType = Number(data.customsFeeType)
    }
    return {
      rules: {},
      purchasePrice: data
    }
  },
  components: {
    SelectSupplier
  },
  props: [
    'field', 'data', 'disableByDeliveryStatus', 'purchaseData'
  ],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    })
  },
  methods: {
    // parseVal(value) {
    //   if (!isNaN(value)) return Number(value)
    //   return value
    // },
    disableCharge(prop) {
      if (this.purchaseData.isOverchargeFree === 1) return true
      if (/tariff/.test(prop)) {
        if (this.purchaseData.isZeroTariff) return true
      }
      if (/intlShipping/.test(prop)) {
        if (this.purchaseData.isZeroIntlShipping) return true
      }
      if (/customsFee/.test(prop)) {
        if (this.purchaseData.isZeroCustomsFee) return true
      }
    },
    submit (formName) {
      return {
        priceData: this.purchasePrice
      }
      // this.$emit('submit', this.purchasePrice)
    },
    handleChangeSupplier (prop, val, valList) {
      console.log(prop, val, valList)
      this.purchasePrice[prop] = val
    },
    handleSelectChange (prop, value) {
      console.log(prop, value)
    },
    handleSupplierChange (prop, value) {
      if (!value) {
        value = { supplierNo: '', supplierName: '' }
        this.purchasePrice[prop] = ''
      } else {
        this.purchasePrice[prop] = value
      }
      console.log(prop, value)
      const { supplierNo, supplierName } = value
      this.purchasePrice[prop + 'No'] = supplierNo
      this.purchasePrice[prop + 'Name'] = supplierName
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
