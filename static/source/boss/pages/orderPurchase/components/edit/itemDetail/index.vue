<template>
  <el-dialog
    title="商品详情"
    top="10px"
    :width="purchaseData.orderType === 'Z002' ? '1200px' : '860px'"
    :visible.sync="showDlg"
    :show-close="false"
    @open="openDialog"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane label="详细信息" name="detail">
        <Detail :disableByDeliveryStatus="disableByDeliveryStatus" :data="rowDetail" :key="key1" :field="fieldDetail" ref="detail"/>
      </el-tab-pane>
      <el-tab-pane label="计划行" name="plan">
        <Plan :disableByDeliveryStatus="disableByDeliveryStatus" :data="rowDetail" :purchaseData="purchaseData" :key="key2" :field="fieldPlan" ref="plan" @handleChangeDeliveryDate="handleChangeDeliveryDate"/>
      </el-tab-pane>
      <el-tab-pane label="价费信息" name="price" v-if="fieldPrice.length && showCharge">
        <Price :disableByDeliveryStatus="disableByDeliveryStatus" :data="rowDetail" :purchaseData="purchaseData" :key="key3" :field="fieldPrice" ref="price"/>
      </el-tab-pane>
      <el-tab-pane label="委外组件" name="entrust" v-if="fieldEntrust.length">
        <Entrust :disableByDeliveryStatus="disableByDeliveryStatus" :data="rowDetail" :purchaseData="purchaseData" :key="key4" :field="fieldEntrust" :KHWarehouseList="KHWarehouseList" ref="entrust"/>
      </el-tab-pane>
      <el-tab-pane label="固定资产详情" name="assets" v-if="fieldAssets.length">
        <Assets :disableByDeliveryStatus="disableByDeliveryStatus" :data="rowDetail" :key="key5" :field="fieldAssets" ref="assets"/>
      </el-tab-pane>
      <el-tab-pane label="定制说明" name="customInstruction"  v-if="rowDetail && rowDetail.customInstructions">
        <CustomInstruction :disableByDeliveryStatus="disableByDeliveryStatus" :data="rowDetail" :key="key7" ref="customInstruction"/>
      </el-tab-pane>
    </el-tabs>
    <el-row type="flex" justify="center" class="btn-row">
      <el-button type="primary" :disabled="disableByDeliveryStatus('999')" @click="handleSubmit">确定</el-button>
      <el-button @click="closeDialog">取消</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
import * as shoirtid from 'shortid'
import Detail from './tabs/Detail'
import Plan from './tabs/Plan'
import Entrust from './tabs/Entrust'
import Price from './tabs/Price'
import Assets from './tabs/Assets'
import CustomInstruction from './tabs/CustomInstruction'
import { safeRun } from '@/utils/index'
import { sameTimeValid } from '@/utils/poPriceCalculate'

export default {
  data () {
    return {
      purchaseDetail: {},
      key1: shoirtid.generate(),
      key2: shoirtid.generate(),
      key3: shoirtid.generate(),
      key4: shoirtid.generate(),
      key5: shoirtid.generate(),
      key6: shoirtid.generate(),
      key7: shoirtid.generate(),
      activeName: 'detail',
      rules: {}
    }
  },
  components: { Detail, Plan, Entrust, Price, Assets, CustomInstruction },
  props: [
    'purchaseData', 'rowDetail', 'showDialog', 'itemField', 'showCharge', 'KHWarehouseList', 'isEditDelayedPaymentPo'
  ],
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    },
    fieldDetail () {
      return this.itemField.filter(item => item.category === 'detailInfo')
    },
    fieldPlan () {
      return this.itemField.filter(item => item.category === 'detailPlan')
    },
    fieldPrice () {
      const fields = this.itemField.filter(item => item.category === 'detailPrice')
      fields.forEach(field => {
        if (field.prop === 'tariffSupplierNo') field.prop = 'tariffSupplier'
        if (field.prop === 'saleTaxSupplierNo') field.prop = 'saleTaxSupplier'
        if (field.prop === 'intlShippingSupplierNo') field.prop = 'intlShippingSupplier'
        if (field.prop === 'latePaymentSupplierNo') field.prop = 'latePaymentSupplier'
        if (field.prop === 'customsFeeSupplierNo') field.prop = 'customsFeeSupplier'
        if (field.prop === 'premiumSupplierNo') field.prop = 'premiumSupplier'
        if (field.prop === 'otherSupplierNo') field.prop = 'otherSupplier'
      })
      return fields
    },
    fieldEntrust () {
      return this.itemField.filter(item => /detailEntrust/gi.test(item.category))
    },
    fieldAssets () {
      return this.itemField.filter(item => item.category === 'detailAssets' || item.category === 'detailAsset')
    }
  },
  methods: {
    disableByDeliveryStatus (prop) {
      const rowDetail = this.rowDetail || {}
      const purchaseData = this.purchaseData

      if (prop === 'disabled') return true

      // 已开票未付款订单编辑时禁用
      if (this.isEditDelayedPaymentPo) return true

      // 状态为已删除, 则禁用
      if (rowDetail.isDeleted) return true
      // deliveryStatus: 0 未到货
      // deliveryStatus: 1 到货完成
      // deliveryStatus: 2 部分到货

      // 商品行不存在收货记录可以修改，部分到货、到货完成则禁用
      const status0 = [
        'materialDescription', 'prOrderNo', 'prOrderItemNo', 'fixedAssetsCardQuantity',
        'fixedAssetsCardNo', 'fixedAssetsCardSubNo', 'specialSupplySo', 'specialSupplySoItem', 'oaNo', 'oaItemNo'
      ]
      if (status0.includes(prop) && !rowDetail.deliveryStatus) {
        return false
      }
      // 商品行收货完结前可以修改，则到货完成禁用
      const status1 = [
        '1',
        'deliveryDate', 'customInstructions'
      ]
      if (status1.includes(prop) && (rowDetail.deliveryStatus !== 1 || rowDetail.deliveryStatus === undefined)) {
        return false
      }
      // 订单收货完结前，可修改
      const status2 = [
        'supplierOrderNo', 'itemRemark', 'deliveryQuantity'
      ]
      if (status2.includes(prop) && purchaseData.deliveryStatus !== 1) {
        return false
      }
      const disList = [
        'shippingAmount', 'discountAmount'
      ]
      const ableList = [
        '999'
      ]
      if (ableList.includes(prop)) {
        return false
      }
      if (disList.includes(prop)) {
        return true
      }
      if (/tariff|saleTax|intlShipping|latePayment|customsFee|other|premium/.test(prop) && (rowDetail.deliveryStatus === 0 || rowDetail.deliveryStatus === undefined)) {
        // 有收货记录(1、2)则禁用
        return false
      }
      if (prop === 'isLastInvoice') {
        return false
      }
      return true
    },
    openDialog () {
      this.key1 = shoirtid.generate()
      this.key2 = shoirtid.generate()
      this.key3 = shoirtid.generate()
      this.key4 = shoirtid.generate()
      this.key5 = shoirtid.generate()
    },
    getFormPromise (form) {
      return new Promise(resolve => {
        form.validate(res => {
          resolve(res)
        })
      })
    },
    validateRefsForm (detail, plan) {
      let ret = true
      detail.validate((valid) => {
        if (!valid) {
          ret = false
          return
        }
        plan.validate((valid) => {
          if (!valid) {
            ret = false
          }
        })
      })
      return ret
    },
    validateSubmit (comp, type) {
      let ret = true
      safeRun(() => {
        if (type === 'plan' && comp.planData.some(plan => !plan.prOrderNo && plan.prOrderItemNo)) {
          ret = false
          this.$message.error('当填写采购申请行号时，采购申请单号不能为空！')
        }
      })
      safeRun(() => {
        if (type === 'comp' && comp.componentList.some(comp => !comp.componentSkuNo || !comp.componentRequiredQuantity || !comp.componentWarehouseLocation)) {
          ret = false
          this.$message.error('委外组件SKU编码、组件数量、库存地点不能为空！')
        }
      })
      return ret
    },
    validatePlanAndAsset (plan, assets) {
      let ret = true
      safeRun(() => {
        const planNum = plan.planData.reduce((x, y) => Number(x) + Number(y.deliveryQuantity), 0)
        const assetsNum = assets.assetsData.reduce((x, y) => Number(x) + Number(y.fixedAssetsCardQuantity), 0)
        if (assetsNum !== planNum) {
          ret = false
          this.$message.error('固定资产详情数量之和应该等于计划行数量之和！')
        }
        if (assets.assetsData.some(asset => !asset.fixedAssetsCardNo)) {
          ret = false
          this.$message.error('请输入资产卡片号！')
        }
      }, null, true)
      return ret
    },
    validateQuantity () {
      const { orderType, fixedAssetsList = [], planList = [] } = this.rowDetail
      let ret = true
      if (orderType === 'Z007') {
        if (fixedAssetsList && planList) {
          const count1 = fixedAssetsList.reduce((total, item) => total + item.fixedAssetsCardQuantity, 0)
          const count2 = planList.reduce((total, item) => total + item.deliveryQuantity, 0)
          if (count1 !== count2) {
            this.$message.error('计划行计划数量之和应等于固定资产行数量之和！')
            ret = false
          }
        }
      }
      return ret
    },
    validateDeliveryDate (comp) {
      if (!comp) return false
      const planList = comp.planData
      let ret = true
      if (planList.length) {
        if (planList.some(plan => (!plan.deliveryDate || !plan.deliveryQuantity) && (plan.deliveryQuantity !== 0))) {
          this.$message.error('交货日期和计划行数量必填！')
          ret = false
        }
      }
      return ret
    },
    validateSpecial (comp) {
      let ret = true
      const detail = comp.purchaseDetailData || {}
      if (!sameTimeValid(detail, 'specialSupplySo', 'specialSupplySoItem')) {
        ret = false
        this.$message.error('专供销售单和专供销售单行号要么均填写，要么均不填写！')
      }
      return ret
    },
    validateOA (comp) {
      let ret = true
      // FYW-WZ：物资      FYW-FW：服务
      // 服务的 行号可以不填，物资的行号校验必填就好
      const detail = comp.purchaseDetailData || {}
      if (detail.oaNo && detail.oaNo.indexOf('FYW-WZ') === 0 && !sameTimeValid(detail, 'oaNo', 'oaItemNo')) {
        ret = false
        this.$message.error('OA流程编号、OA行号要么均填写，要么均不填写！')
      }
      return ret
    },
    validatePrice (comp) {
      const priceData = comp.purchasePrice
      const chargeProps = ['tariff', 'saleTax', 'latePayment', 'premium', 'intlShipping', 'customsFee', 'other']
      const chargeAttrs = {
        'shareShipping': '运费',
        'shareDiscount': '折扣',
        'tariff': '关税',
        'customsFee': '报关杂费',
        'intlShipping': '国际运费',
        'saleTax': '消费税',
        'latePayment': '滞报金',
        'premium': '保险费',
        'other': '其他'
      }
      let errorMsg = ''
      if (chargeProps.some(item => {
        // 税目,币别,供应商应该同时存在或同时不存在
        const valueList = [item + 'Amount', item + 'Currency', item + 'SupplierNo']
        if (this.purchaseData.orderType === 'Z002' && (item === 'intlShipping' || item === 'customsFee')) {
          valueList.push(item + 'Type')
          errorMsg = `${chargeAttrs[item]}类型、金额、币别类型、供应商必须同时输入！`
          return !sameTimeValid(priceData, ...valueList)
        } else {
          errorMsg = `${chargeAttrs[item]}金额、币别类型、供应商必须同时输入！`
          return !sameTimeValid(priceData, ...valueList)
        }
      }
      )) {
        this.$message.error(errorMsg)
        return false
      }

      if (chargeProps.some(item => {
        const currency = this.purchaseData[item + 'Currency']
        return currency && currency !== priceData[item + 'Currency'] &&
          (priceData[item + 'Currency'] && priceData[item + 'Amount'] != null && priceData[item + 'SupplierNo'])
      }
      )) {
        this.$message.error('订单头上币别类型必须和行上币别类型保持一致！')
        return false
      }
      return true
    },
    async handleSubmit () {
      const { orderType } = this.purchaseData
      if (!this.validateRefsForm(this.$refs.detail.$refs['purchaseDetail'], this.$refs.plan.$refs['purchasePlan'])) {
        return this.$message.error('请填写必填项！')
      }
      if (orderType === 'Z007') {
        if (!this.validatePlanAndAsset(this.$refs.plan, this.$refs.assets)) return
      }
      if (!this.validateSpecial(this.$refs.detail)) return
      // 专供销售单号和专供销售单行号要么均填写，要么均不填写

      if (!this.validateOA(this.$refs.detail)) return
      // OA相关要么均填写，要么均不填写

      if (!this.validateSubmit(this.$refs.plan, 'plan')) return
      if (orderType === 'Z006') {
        if (!this.validateSubmit(this.$refs.entrust, 'comp')) return
      }
      if (!this.validateQuantity()) return
      if (!this.validateDeliveryDate(this.$refs.plan)) return
      if (orderType !== 'Z003' && this.$refs.price) {
        if (!this.validatePrice(this.$refs.price)) return
      }
      this.$emit('update:showDialog', false)
      const pArr = []
      if (this.$refs.detail) {
        const detailForm = this.$refs.detail.$refs['purchaseDetail']
        pArr.push(detailForm)
      }
      if (this.$refs.price) {
        const priceForm = this.$refs.price.$refs['purchasePrice']
        pArr.push(priceForm)
      }
      const res = await Promise.all(pArr.map(this.getFormPromise))
      const validateResult = res.every(item => !!item) || !res.length
      if (validateResult) {
        let res = {}
        let typeArr = ['detail']
        console.log(this.rowDetail);
        res = {
          ...res,
          ...this.$refs.detail.submit('purchaseDetail')
        }
        if (this.$refs.price) {
          res = {
            ...res,
            ...this.$refs.price.submit('purchasePrice')
          }
          typeArr.push('price')
        }
        if (this.$refs.assets) {
          res = {
            ...res,
            ...this.$refs.assets.submit('purchaseAssets')
          }
          typeArr.push('assets')
        }
        if (this.$refs.entrust) {
          res = {
            ...res,
            ...this.$refs.entrust.submit('purchaseEntrust')
          }
          typeArr.push('entrust')
        }
        if (this.$refs.customInstruction) {
          res = {
            ...res,
            ...this.$refs.customInstruction.submit('customInstruction')
          }
          typeArr.push('customInstructions')
        }
        res = {
          ...res,
          ...this.$refs.plan.submit('purchasePlan')
        }
        typeArr.push('plan')
        this.$emit('updateItem', {
          rowDetail: this.rowDetail,
          data: res,
          updateTypes: typeArr
        })
        return true
      }
    },
    closeDialog () {
      this.$emit('update:showDialog', false)
      if (this.$refs.detail) {
        this.$refs.detail.$refs['purchaseDetail'].resetFields()
      }
      if (this.$refs.plan) {
        this.$refs.plan.$refs['purchasePlan'].resetFields()
      }
      if (this.$refs.price) {
        this.$refs.price.$refs['purchasePrice'].resetFields()
      }
    },
    handleChangeDeliveryDate() {
      this.$emit('handleChangeDeliveryDate')
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-row{
  margin-top: 20px;
}
</style>
