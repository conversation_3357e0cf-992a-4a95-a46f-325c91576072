<template>
  <el-form :model="rowDetail.customInstructionData" ref="customInstruction" label-width="120px" label-suffix=":" :rules="rules">
    <el-row :gutter="20">
      <el-col v-for="item in rowDetail.customInstructionList" :key="item.key" :span="24">
        <el-form-item :label="item.customProperty" :prop="item.customPropertyRemark">
          <el-input
            :disabled="isDisabled"
            type="textarea"
            v-model="rowDetail.customInstructionData[item.key]"
            clearable maxlength="512"
            :autosize="{ minRows: 2, maxRows: 4 }"
            :placeholder="item.name"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>

export default {
  name: 'customInstructions',
  data () {
    let data = { ...this.data }
    return {
      rules: {},
      rowDetail: data
    }
  },
  props: [
    'data', 'disableByDeliveryStatus', 'purchaseData'
  ],
  computed: {
    isDisabled() {
      console.log('this.rowDetail', this.rowDetail)
      return this.data?.isDeliveryDone === 1
    }
  },
  watch: {
    'data.customInstructions'(newVal, oldVal) {
      this.rowDetail.customInstructionData = this.data.customInstructionData
      this.rowDetail.customInstructionList = this.data.customInstructionList
    }
  },
  methods: {
    submit (formName) {
      const customInstructions = this.rowDetail.customInstructionList.map(item => {
        return {
          customProperty: item.customProperty,
          customPropertyRemark: this.rowDetail.customInstructionData[item.key]
        }
      })
      return {
        customInstructionData: customInstructions
      }
      // this.$emit('submit', customInstructions)
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
