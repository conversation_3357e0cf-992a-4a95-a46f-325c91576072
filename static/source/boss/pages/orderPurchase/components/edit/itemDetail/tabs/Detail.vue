<template>
  <el-form :model="purchaseDetailData" ref="purchaseDetail" label-width="125px" label-suffix=":" :rules="rules">
    <el-row :gutter="20">
      <el-col v-for="item in computedField" :key="item.name" :span="item.span || 12">
        <el-form-item :label="item.name" :prop="item.prop">
          <el-input
            v-if="item.type==='input'"
            v-model="purchaseDetailData[item.prop]"
            :disabled="disableByDeliveryStatus(item.prop) || disabledBySpecial(item.prop)"
            clearable
            :maxlength="item.maxlength||35"
            :placeholder="item.name"
            style="width:100%"
          />
          <el-input
            v-else-if="item.type==='textarea'"
            :disabled="disableByDeliveryStatus(item.prop)"
            type="textarea"
            :rows="2"
            placeholder="请输入内容"
            show-word-limit
            :maxlength="item.length||200"
            v-model="purchaseDetailData[item.prop]"
            style="width:100%"
          />
          <el-input-number
            v-else-if="item.type==='number'"
            v-model="purchaseDetailData[item.prop]"
            :disabled="disableByDeliveryStatus(item.prop)"
            clearable
            :placeholder="item.name"
            :precision="0"
            :min="0"
            :max="100"
            style="width:100%"
          />
          <el-checkbox
            v-else-if="item.type==='checkbox'"
            v-model="purchaseDetailData[item.prop]"
            :disabled="disableByDeliveryStatus(item.prop)"
            :true-label="1"
            :false-label="0"
          />
          <el-date-picker
            v-else-if="item.type==='date'"
            v-model="purchaseDetailData[item.prop]"
            :disabled="disableByDeliveryStatus(item.prop)"
            type="date"
          />
          <el-select
            v-else-if="item.type==='select'"
            style="width:100%;max-height: 50px;max-width: 275px;"
            v-model="purchaseDetailData[item.prop]"
            :disabled="disableByDeliveryStatus(item.prop)"
            filterable
            default-first-option
            clearable
          >
            <el-option
              v-for="item in dictList[item.enums]"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <span v-else>{{purchaseDetailData[item.prop]}}</span>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <el-form-item label="订单单位 — 价格单位" label-width=“200px”>
          <el-input placeholder="订单单位" :value="purchaseDetailData.priceUnitMole || 1" style="width:200px" disabled>
            <template slot="append">{{this.findUnitName(purchaseDetailData.priceUnit)}}</template>
          </el-input>
          -
          <el-input placeholder="价格单位" :value="purchaseDetailData.priceUnitDeno || 1" style="width:200px" disabled>
            <template slot="append">{{this.findUnitName(purchaseDetailData.priceUnit)}}</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col>
        <el-form-item label="库存单位 — 订单单位" label-width=“200px”>
          <el-input placeholder="库存单位" :value="purchaseDetailData.inventoryUnitMole || 1" style="width:200px" disabled>
            <template slot="append">{{this.findUnitName(purchaseDetailData.inventoryUnit)}}</template>
          </el-input>
          -
          <el-input placeholder="订单单位" :value="purchaseDetailData.inventoryUnitDeno  || 1" style="width:200px" disabled>
            <template slot="append">{{this.findUnitName(purchaseDetailData.unit)}}</template>
          </el-input>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'purchaseOrderItemDetail',
  data () {
    return {
      purchaseDetailData: { ...this.data },
      rules: {
        materialDescription: [
          { required: true, message: '物料描述必填', trigger: 'blur' }
        ],
        standardLeadTime: [
          { required: true, message: '商品行交期必填', trigger: 'blur' }
        ]
      }
    }
  },
  props: [
    'field', 'data', 'disableByDeliveryStatus'
  ],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    computedField() {
      // 不想每个类型添加一遍field，所以在这里直接concat了
      return this.field
        .concat([
          { 'id': 6061, 'name': '商品行杂费', 'prop': 'sundryAmount', 'type': 'text', 'width': 0, 'span': 0, 'category': 'detailInfo', 'precision': null, 'length': null, 'required': true, 'enums': null, 'disabled': false, 'sequence': 55, 'status': null },
          { 'id': 6062, 'name': '商品行返利', 'prop': 'rebateAmount', 'type': 'text', 'width': 0, 'span': 0, 'category': 'detailInfo', 'precision': null, 'length': null, 'required': true, 'enums': null, 'disabled': false, 'sequence': 56, 'status': null }
      ])
        .sort((a, b) => a.sequence - b.sequence)
    }
  },
  methods: {
    disabledBySpecial (prop) {
      const propList = ['specialSupplySo', 'specialSupplySoItem']
      const { soNo, soItemNo } = this.purchaseDetailData
      if (propList.find(item => item === prop)) {
        if (soNo || soItemNo) {
          return true
        }
      }
    },
    disableByOA (prop) {
      const propList = ['oaNo', 'oaItemNo', 'oaType']
      if (propList.find(item => item === prop)) {
        return true
      }
    },
    submit (formName) {
      return {
        detailData: this.purchaseDetailData
      }
      // this.$emit('submit', this.purchaseDetailData)
    },
    findUnitName (value) {
      const unitList = this.dictList['orderUnit']
      if (Array.isArray(unitList) && unitList.length) {
        const item = unitList.find(item => item.value === value)
        if (item && item.name) {
          value = item.name
        }
      }
      return value
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
