<template>
  <el-form ref="purchaseAssets">
    <el-table :data="assetsData" style="width: 100%">
      <el-table-column
        v-for="item in field"
        :key="item.prop"
        :label="item.name"
        align="center">
        <template slot="header">
          <span v-bind:class="{required: item.required===true}" >{{item.name}}</span>
        </template>
        <template slot-scope="{row}">
          <el-input-number
            v-if="item.type==='number'"
            :disabled="disableByDeliveryStatus(item.prop)"
            v-model="row[item.prop]"
            style="width:100%"
            size="mini"
            :placeholder="item.name"
            :min="0"
            :precision="3"
            :step="1"
          />
          <el-input
            v-else-if="item.type==='input'"
            :disabled="disableByDeliveryStatus(item.prop)"
            v-model="row[item.prop]"
            :placeholder="item.name"
            style="width:100%"
            size="mini"
            @change="val=>handleChangeInput(val, item.prop, row)"
          />
          <span v-else>{{row[item.prop]}}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-form>
</template>

<script>
import padStart from 'lodash/padStart'
import { deepClone } from '@/utils/index'

export default {
  name: 'purchaseOrderItemAssets',
  data () {
    let fixedAssetsList = this.data.fixedAssetsList || []
    if (!fixedAssetsList || fixedAssetsList.length === 0) {
      fixedAssetsList = [{ fixedAssetsNo: this.getItemNo() }]
    }
    fixedAssetsList = deepClone(fixedAssetsList)
    return {
      assetsData: fixedAssetsList
    }
  },
  props: [
    'field', 'data', 'disableByDeliveryStatus'
  ],
  methods: {
    handleChangeInput (val, prop, row) {
      if (prop === 'fixedAssetsCardNo') {
        this.data.fixedAssetsCardNo = val
        this.$forceUpdate()
      }
    },
    getItemNo () {
      const len = (this.assetsData || []).length
      let no = 0
      if (len > 0) {
        const { fixedAssetsNo = 0 } = this.assetsData[len - 1]
        if (fixedAssetsNo) {
          no = fixedAssetsNo
        }
      }
      const numItemNo = Number(no) + 1
      return padStart(numItemNo, 4, '0')
    },
    submit (formName) {
      return {
       assetsData: this.assetsData
      }
      // this.$emit('submit', this.assetsData)
    }
  }
}
</script>

<style lang="scss" scoped>
.required {
  &:before {
    content: '*';
    color: #ff7268;
    margin-right: 4px;
  }
  &::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}</style>
