<template>
  <div>
    <el-form :model="purchaseEntrust" ref="purchaseEntrust" label-width="110px" label-suffix=":">
      <el-row :gutter="20">
        <el-col v-for="item in formField" :key="item.name" :span="item.span || 12">
          <el-form-item :label="item.name" :prop="item.prop">
            <el-input
              v-if="item.type==='input'"
              v-model="purchaseEntrust[item.prop]"
              clearable maxlength="35"
              :placeholder="item.name"
            />
            <span v-if="item.type==='text'">{{purchaseEntrust[item.prop]}}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="componentList" style="width: 100%">
      <el-table-column
        v-for="col in formTable"
        :key="col.prop"
        :label="col.name"
        align="center"
        width="180"
      >
        <template slot="header">
          <span v-bind:class="{required: Boolean(col.required)}" >{{col.name}}</span>
        </template>
        <template slot-scope="{row}">
          <SelectSku
            v-if="col.prop==='componentSkuNo'"
            :row="row"
            :data.sync="row[col.prop]"
            :disabled="disabled || disabledByEditCompSku(row) || disabledByPlanLineRecvQty"
            @change="(val,desc)=>handleSelectSku(val,desc,row)"
          />
          <el-select
            v-else-if="col.type==='select'&&(col.prop==='componentWarehouseLocation')"
            v-model="row[col.prop]"
            :disabled="disabled || disabledByPlanLineRecvQty"
            filterable
            clearable
            size="mini"
            @change="val=>handleChangeWarehouse(val,row)"
          >
            <el-option
              v-for="item in getWarehouseList(data.factoryCode)"
              :key="item.warehouseLocationCode"
              :label="item.warehouseLocationCode+' '+item.warehouseLocationName"
              :value="item.warehouseLocationCode">
            </el-option>
          </el-select>
          <el-input-number
            v-else-if="col.type==='number'"
            v-model="row[col.prop]"
            :disabled="disabledByPlanLineRecvQty"
            size="mini"
            :placeholder="col.name"
            :precision="3"
            :min="0"
          />
          <el-input
            v-else-if="col.type==='input'"
            v-model="row[col.prop]"
            :disabled="disabledByPlanLineRecvQty"
            :placeholder="col.name"
            style="width:100%"
            size="mini"
          />
          <span v-else-if="col.format">{{col.format(row)}}</span>
          <span v-else>{{row[col.prop]}}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        fixed="right"
        label="操作">
        <template slot-scope="{$index}">
          <el-button @click="handleRemove($index)"
            :disabled="disabledByPlanLineRecvQty"
            type="text"
            size="small"
            v-show="componentList.length > 1"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-row type="flex" justify="center">
      <el-button
        size="mini"
        style="width:100%;margin:5px 0"
        @click="handleAdd"
        :disabled="disabledByPlanLineRecvQty"
      >
        <i class="el-icon-circle-plus" />新增组件行
      </el-button>
    </el-row>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import SelectSku from '@/pages/orderPurchase/components/common/SelectSku'
import {
  getProduct, getInventory
} from '@/api/mm'
import padStart from 'lodash/padStart'
import { getDefaultWarehouseLocation, getD02ComponentWarehouseLocation } from '@/pages/orderPurchase/utils'

export default {
  name: 'purchaseOrderItemEntrust',
  data () {
    let componentList = (this.data || {}).componentList || []
    const { skuNo, materialDescription, itemQuantity } = this.data
    componentList = JSON.parse(JSON.stringify(componentList))
    return {
      purchaseEntrust: {
        skuNo, materialDescription, itemQuantity
      },
      componentList
    }
  },
  components: {
    SelectSku
  },
  props: [
    'field', 'data', 'disabled', 'purchaseData', 'KHWarehouseList'
  ],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      warehouseList: state => state.orderPurchase.warehouseList,
      defaultWarehouseConfigList: state => state.orderPurchase.defaultWarehouseConfigList
    }),
    formField () {
      const itemList = [
        'skuNo', 'itemQuantity', 'materialDescription'
      ]
      return this.field.filter(item => itemList.includes(item.prop))
    },
    formTable () {
      const itemList = [
        'skuNo', 'itemQuantity', 'materialDescription'
      ]
      return this.field.filter(item => !itemList.includes(item.prop))
    },
    disabledByPlanLineRecvQty () {
      let ret = false
      if (this.data && this.data.planList && this.data.planList[0]) {
        ret = this.data.planList[0].receivedQuantity > 0
      }
      return ret
    }
  },
  methods: {
    disabledByEditCompSku (row) {
      return !row.userAdded
    },
    getItemNo () {
      const len = (this.componentList || []).length
      let no = 0
      if (len > 0) {
        const { componentNo = 0 } = this.componentList[len - 1]
        if (componentNo) {
          no = componentNo
        }
      }
      const numItemNo = Number(no) + 1
      return padStart(numItemNo, 4, '0')
    },
    getWarehouseList (factoryCode) {
      if (factoryCode) {
        return this.warehouseList.filter(item => item.factoryCode === factoryCode)
      }
      return []
    },
    startLoading () {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      return loading
    },
    endLoading (loading) {
      if (loading) {
        loading.close()
      }
    },
    handleChangeWarehouse (val, row) {
      const { factoryCode } = this.data
      const { componentSkuNo } = row
      if (factoryCode && componentSkuNo) {
        if (val) {
          const loading = this.startLoading()
          getInventory({
            factoryCode,
            skuNos: componentSkuNo,
            warehouseLocation: val
          }).then(data => {
            this.endLoading(loading)
            if (data && Array.isArray(data) && data.length > 0) {
              const inventory = data[0]
              if (inventory != null) {
                this.$set(row, 'availableQty', inventory.availableQty)
              }
            }
          })
        } else {
          row.availableQty = ''
        }
      }
    },
    handleSelectSku (val, desc, row) {
      if (val) {
        row.componentMaterialDescription = desc
        const { factoryCode } = this.data
        const loading = this.startLoading()
        const { supplier: { supplierNo } } = this.purchaseData
        getProduct({
          factoryCode: factoryCode,
          skuNos: [val],
          supplierNo
        }).then(data => {
          this.endLoading(loading)
          if (data && Array.isArray(data) && data.length > 0) {
            const product = data[0]
            if (product && product.unitId) {
              row.componentInventoryUnit = product.unitName === '米' ? 'M' : product.unitName
              row.componentInventoryUnitName = product.unitName === '米' ? 'M' : product.unitName
            }
            if (row._configWarehouse) {
              row.componentWarehouseLocation = row._configWarehouse
            }
          }
        })
      } else {
        row.componentMaterialDescription = ''
        row.componentInventoryUnit = ''
        row.componentInventoryUnitName = ''
        row.availableQty = ''
        row.componentWarehouseLocation = ''
      }
    },
    handleAdd () {
      const { factoryCode, warehouseLocation, warehouseLocationCode } = this.data
      if (!this.componentList) {
        this.componentList = []
      }
      let _configWarehouse =
        getDefaultWarehouseLocation(this.defaultWarehouseConfigList, this.purchaseData.purchaseGroup, factoryCode) || ''
      let componentWarehouseLocation = _configWarehouse
      if (!componentWarehouseLocation) {
        componentWarehouseLocation = warehouseLocation || warehouseLocationCode
      }
      componentWarehouseLocation = getD02ComponentWarehouseLocation(this.purchaseData.orderType, this.purchaseData.purchaseGroup, this.KHWarehouseList, warehouseLocation || warehouseLocationCode, componentWarehouseLocation, this.dictList['componentWarehouse'])
      this.componentList.push({
        componentNo: this.getItemNo(),
        componentSkuNo: '',
        userAdded: true,
        componentRequiredQuantity: 0,
        componentInventoryUnit: '',
        componentInventoryUnitName: '',
        componentWarehouseLocation,
        _configWarehouse
      })
    },
    handleRemove (idx) {
      this.componentList.splice(idx, 1)
    },
    submit () {
      console.log(this.componentList);
      return {
        entrustData: this.componentList
      }
      // this.$emit('submit', this.componentList)
    }
  }
}
</script>

<style lang="scss" scoped>
.required {
  &:before {
    content: '*';
    color: #ff7268;
    margin-right: 4px;
  }
  &::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>
