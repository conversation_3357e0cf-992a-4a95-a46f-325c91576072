<template>
  <vxe-table
    resizable
    border
    show-overflow
    highlight-hover-row
    highlight-current-row
    :height="tableHeight"
    ref="detailTable"
    align="center"
    size="small"
    :scroll-y="{ gt: 0 }"
    :data="data.itemList"
    :checkbox-config="{ checkMethod: checCheckbox }"
    @checkbox-change="onTableSelectionChange"
    @checkbox-all="onTableSelectionAllChange"
  >
    <vxe-table-column
      class-name="checkbox-column"
      type="checkbox"
      title=""
      fixed="left"
      align="center"
    ></vxe-table-column>
    <vxe-table-column
      v-for="col in getColumns"
      align="center"
      :width="col.width"
      :key="col.name"
      :field="col.name"
      :title="col.name"
    >
      <template v-slot:header>
        <span v-bind:class="{ required: col.required === true }">{{
          col.name
        }}</span>
      </template>
      <template slot-scope="{ row, rowIndex }">
        <span v-if="col.prop === 'skuNo'" style="display: flex">
          <SelectSku
            v-if="col.prop === 'skuNo'"
            :data.sync="row[col.prop]"
            :disabled="
              disabled ||
              disableByDeliveryStatus(row, col) ||
              disabledReturnS(col, row)
            "
            @change="(val) => handleSelectSku(val, row)"
          />
          <span
            v-if="row[col.prop]"
            @click="handleCopy(row[col.prop], $event)"
            style="display: inline-block; cursor: pointer; margin: 0 5px"
            title="点击复制SKU"
          >
            <i class="el-icon-document-copy"></i>
          </span>
        </span>
        <el-input-number
          v-else-if="col.type === 'number' && col.prop === 'standardLeadTime'"
          :disabled="disabled || disableByDeliveryStatus(row, col)"
          v-model="row[col.prop]"
          size="mini"
          :placeholder="col.name"
          :precision="0"
          :min="0"
          :max="999"
          @change="(val) => handleStandardLeadTime(val, row)"
        />
        <el-input-number
          v-else-if="col.type === 'number' && col.prop === 'priceTimes'"
          v-model="row[col.prop]"
          size="mini"
          :placeholder="col.name"
          :min="1"
          :disabled="
            disabled ||
            disableByFree(row, col) ||
            disableByOverCharge(row, col) ||
            disableByDeliveryStatus(row, col) ||
            disabledReturnS(col, row)
          "
          :precision="0"
          @change="(val) => handlePriceTimesChange(val, row)"
        />
        <el-input-number
          v-else-if="col.type === 'number'"
          v-model="row[col.prop]"
          size="mini"
          :placeholder="col.name"
          :min="0"
          :max="numberMax(col.prop, row)"
          :disabled="
            disabled ||
            disableByFree(row, col) ||
            disableByOverCharge(row, col) ||
            disableByDeliveryStatus(row, col) ||
            disableByPlanList(row, col) ||
            disableByRefundableAmount(row, col) ||
            disabledReturnS(col, row)
          "
          :precision="col.prop === 'itemQuantity' ? 3 : 2"
          @change="(val, old) => handleItemNumChange(val, old, col.prop, row, rowIndex)"
        />
        <el-input
          v-else-if="
            col.prop === 'materialDescription' ||
            (col.type === 'input' && ['Z007', 'Z004'].includes(orderType))
          "
          :disabled="
            disabled ||
            disableByDeliveryStatus(row, col) ||
            disabledReturnS(col, row)
          "
          v-model="row[col.prop]"
          size="mini"
          clearable
          :placeholder="col.name"
          :maxlength="col.maxlength || 80"
          @change="(val) => handleChangeInput(val, col.prop, row)"
        />
        <el-input
          v-else-if="col.type === 'input'"
          v-model="row[col.prop]"
          size="mini"
          clearable
          :placeholder="col.name"
          :maxlength="smLength(col) || col.maxlength || 30"
          :disabled="
            disabled ||
            disableByDeliveryStatus(row, col) ||
            disabledInput(col.prop, row) ||
            disabledBySpecial(col.prop, row) ||
            disabledReturnS(col, row)
          "
          @change="(val) => handleChangeInput(val, col.prop, row)"
        />
        <el-checkbox
          v-else-if="col.type === 'checkbox'"
          :disabled="
            disabled ||
            disableByDeliveryStatus(row, col) ||
            disableIsDeliveryDone(row, col)
          "
          v-model="row[col.prop]"
          :true-label="1"
          :false-label="0"
          @change="(val) => handleChangeCheckbox(val, col.prop, row)"
        />
        <el-date-picker
          v-else-if="col.type === 'date'"
          v-model="row[col.prop]"
          clearable
          size="mini"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择日期"
          style="width: 100%"
          :picker-options="dateOptions"
          :disabled="
            disabled ||
            disableByDeliveryStatus(row, col) ||
            disableByPlanList(row, col)
          "
          @change="(val) => handleChangeItemDate(val, row, col)"
        />
        <el-select
          v-else-if="
            col.type === 'select' &&
            (col.prop === 'warehouseLocation' ||
              col.prop === 'shipWarehouseLocation')
          "
          v-model="row[col.prop]"
          :disabled="
            disabled ||
            disableByDeliveryStatus(row, col) ||
            disabledReturnS(col, row)
          "
          filterable
          clearable
          default-first-option
          size="mini"
          @change="
            (val) => changeWarehouseLocation(col.prop, val, rowIndex, row)
          "
        >
          <el-option
            v-for="item in getWarehouseList(row.factoryCode)"
            :key="item.warehouseLocationCode"
            :label="
              item.warehouseLocationCode + ' ' + item.warehouseLocationName
            "
            :value="item.warehouseLocationCode"
          >
          </el-option>
        </el-select>
        <el-select
          v-else-if="col.prop === 'factoryCode'"
          v-model="row[col.prop]"
          :disabled="
            col.disabled || disabled || disableByDeliveryStatus(row, col)
          "
          size="mini"
          @change="(val) => handleChangeItemFactory(val, rowIndex, row)"
        >
          <el-option
            v-for="item in factoryList"
            :key="item.factoryCode"
            :label="item.factoryCode + ' ' + item.factoryName"
            :value="item.factoryCode"
          >
          </el-option>
        </el-select>
        <el-select
          v-else-if="col.prop === 'projectCategory' && col.type === 'select'"
          v-model="row[col.prop]"
          :disabled="Boolean(col.disabled || disabled)"
          clearable
          size="mini"
          @change="(value) => handleCategoryChange(col.prop, value, row)"
        >
          <el-option
            v-for="item in dictList['poProjectCategory']"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          v-else-if="col.prop === 'costCenter'"
          v-model="row[col.prop]"
          :disabled="
            col.disabled || disabled || disableByDeliveryStatus(row, col)
          "
          filterable
          clearable
          size="mini"
        >
          <el-option
            v-for="item in costCenterList"
            :key="item.costCenter"
            :label="item.costCenter + ' ' + item.description"
            :value="item.costCenter"
          >
          </el-option>
        </el-select>
        <el-select
          v-else-if="col.type === 'select' && col.prop === 'inputTax'"
          v-model="row[col.prop]"
          size="mini"
          filterable
          clearable
          :disabled="disabledReturnS(col, row) || row.receivedQuantity > 0"
          @change="(val) => handleChangeItemInputTax(val, row, col.prop)"
        >
          <el-option
            v-for="item in dictList[col.enums]"
            :key="item.value"
            :label="
              col.enums === 'orderUnit'
                ? item.name
                : item.value + ' ' + item.name
            "
            :value="item.value"
            :disabled="item.value === 'J1' || item.value === 'J3'"
          >
          </el-option>
        </el-select>
        <div v-else-if="col.type === 'select' && col.prop === 'orderReason'">
          <el-select
            v-model="row[col.prop]"
            :disabled="
              col.disabled ||
              disabled ||
              disableByDeliveryStatus(row, col) ||
              disableByInSystemMaterial(row, col)
            "
            size="mini"
            filterable
            clearable
            :class="{ 'orderReason-select': row[col.prop] === '其他' }"
          >
            <el-option
              v-for="item in dictList[col.enums]"
              :key="item.value"
              :label="item.name"
              :value="item.name"
            >
            </el-option>
          </el-select>
          <el-input
            v-if="row[col.prop] === '其他'"
            v-model="row.newOrderReason"
            size="mini"
            style="width: 100px"
            :placeholder="col.name"
            :maxlength="col.maxlength || 30"
            @change="(val) => handleOderReasonInput(val, col.prop, row)"
            :disabled="
              disabled ||
              disableByDeliveryStatus(row, col) ||
              disabledInput(col.prop, row) ||
              disabledBySpecial(col.prop, row)
            "
          />
        </div>
        <el-select
          v-else-if="col.type === 'select'"
          v-model="row[col.prop]"
          :disabled="
            col.disabled ||
            disabled ||
            disableByDeliveryStatus(row, col) ||
            disableByInSystemMaterial(row, col) ||
            disabledReturnS(col, row)
          "
          size="mini"
          filterable
          clearable
        >
          <el-option
            v-for="item in dictList[col.enums]"
            :key="item.value"
            :label="
              ['orderUnit', 'yesOrNo', 'transferReason', 'deliveryChangeReason'].includes(col.enums)
                ? item.name
                : item.value + ' ' + item.name
            "
            :value="
              ['yesOrNo'].includes(col.enums)
                ? parseInt(item.value)
                : item.value
            "
          >
          </el-option>
        </el-select>
        <SelectMaterialGroup
          v-else-if="col.prop === 'materialGroup' && !row.skuNo"
          :data.sync="row.materialGroup"
          :materialList="materialList"
          :disabled="disabled || disableByDeliveryStatus(row, col)"
          @change="(val) => handleChangeMaterialGroup(val, row)"
        />
        <span v-else-if="col.prop ==='lastGrossMargin'">
          {{ row.poItemExtend && ![undefined, 'undefined', '', null, NaN].includes(row.poItemExtend.lastGrossMargin) ? `${row.poItemExtend.lastGrossMargin}%` : ''}}
        </span>
        <span v-else-if="col.prop === 'isDeleted'" style="margin-right: 10px">
          <span v-if="row[col.prop] === 1">已删除</span>
          <span v-else-if="row.approveTriggerDetail">
            <el-tooltip
              :content="row.approveTriggerDetail"
              placement="bottom"
              effect="light"
            >
              <img src="@/assets/images/warning.png" />
            </el-tooltip>
          </span>
        </span>
        <span
          v-else-if="
            [
              'taxedTotalAmount',
              'untaxedTotalAmount',
              'taxTotalAmount',
            ].indexOf(col.prop) > -1
          "
        >
          {{
            row[col.prop]
              ? Number(row[col.prop]).toFixed(col.precision || 2)
              : ''
          }}
        </span>
        <span v-else-if="col.format">
          {{ col.format(row) }}
        </span>
        <span v-else-if="col.prop === 'unitName'">{{
          renderUnitName(row.unit)
        }}</span>
        <span v-else-if="col.prop === 'cloud'">
          {{ row.cloud ? '是' : row.cloud === false ? '否' : '' }}
        </span>
        <span v-else-if="col.prop === 'orderChannelList'">
          {{ row.poItemExtend ? mapKeyToValue('orderChannel', row.poItemExtend.orderChannelList, dictList) : '' }}
        </span>
        <span v-else>{{ row[col.prop] }}</span>
      </template>
    </vxe-table-column>
    <vxe-table-column
      :width="90"
      fixed="right"
      align="center"
      field="option"
      title="操作"
    >
      <template slot-scope="{ row }">
        <el-link type="primary" style="padding: 2px 0px" @click="toDtl(row)">
          详情
        </el-link>
      </template>
    </vxe-table-column>
  </vxe-table>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex'
import SelectSku from '@/pages/orderPurchase/components/common/SelectSku'
import SelectMaterialGroup from '@/pages/orderPurchase/components/common/SelectMaterialGroup'
import { getSO, getRefundableAmountApi, querySkuBatchPrice, getPOHead, assignByQuantity, calcUnTaxedPrice } from '@/api/mm'
import { safeRun, copyToClipboard } from '@/utils/index'
import { readNameFromDic, mapKeyToValue } from '@/utils/mm'
import { formatAmount, handleGetInfoBySpecialSupplySo } from '@/pages/orderPurchase/utils'
import { isAssigned, rowEditChange } from '@/utils/poPriceCalculate'
import { merge } from 'lodash'

export default {
  name: 'purchaseOrderTable',
  data () {
    return {
      tableHeight: 400
    }
  },
  props: [
    'factoryList', 'finalFields', 'data', 'disabled', 'costCenterList', 'materialList',
    'getFirstLineUpdateDefaultAddress', 'setTitleCheckbox', 'reCalcCompQuantity', 'diffDays', 'isEditDelayedPaymentPo'
  ],
  components: {
    SelectSku, SelectMaterialGroup
  },
  methods: {
    isAssigned,
    readNameFromDic,
    mapKeyToValue,
    smLength (col) {
      if (col.prop === 'supplierMaterialNo') return 100
    },
    isReferPa(row) {
      return row?.paNo
    },
    numberMax(prop, row) {
      return (prop === 'itemQuantity' && (isAssigned(row) || this.isReferPa(row))) ? row[prop] : (prop === 'refundableAmount' && row.taxedTotalAmount ? row.taxedTotalAmount : Number.MAX_SAFE_INTEGER)
    },
    disabledBySpecial (prop, row) {
      const propList = ['soNo', 'soItemNo']
      if (propList.find(item => item === prop)) {
        return true
        // const { specialSupplySo, specialSupplySoItem } = row
        // if (specialSupplySo || specialSupplySoItem) {
        //   return true
        // }
      }
    },
    disabledInput(prop, row) {
      if (prop === 'innerOrderNo') {
        return true
      }
    },
    renderUnitName (value) {
      const { orderType } = this.data
      const unitList = [
        'Z010', 'Z008', 'Z007', 'Z004'
      ]
      if (!unitList.includes(orderType)) {
        // eslint-disable-next-line eqeqeq
        const opt = this.dictList && this.dictList['orderUnit'] && this.dictList['orderUnit'].find(item => item.value == value)
        if (opt) {
          return opt.name
        }
      }
      return value
    },
    handleCopy (no, event) {
      copyToClipboard.bind(this)(no, event)
    },
    disableByInSystemMaterial (row, col) {
      if (col.prop === 'unit' && row.inSystemMaterial) {
        return true
      }
      return false
    },
    disableByOverCharge(row, col) {
      // return col.prop === 'taxedPrice' && (this.data.isOverchargeFree === 1) && !row.isEmptyLine
    },
    disableByFree(row, col) {
      // eslint-disable-next-line
      if (row.isFree == 1 && col.prop!=='itemQuantity') {
        return true
      }
    },
    emptyLine (row) {
      return row.isEmptyLine
    },
    disableByPlanList(row, col) {
      const list = [
        'itemDeliveryDate', 'itemQuantity'
      ]
      let ret = false
      safeRun(() => {
        if (list.includes(col.prop) && row.planList.length > 1) {
          ret = true
        }
      })
      return ret
    },
    disableByRefundableAmount(row, col) {
      if (this.isReturnOrderType && col.prop === 'refundableAmount' && row.projectCategory === 'K') {
        return true
      }
      return false
    },
    disableByDeliveryStatus(row, col) {
      // this.data.deliveryStatus 订单收货状态
      // row.deliveryStatus 商品行收货状态

      // deliveryStatus: 0 未到货
      // deliveryStatus: 1 到货完成
      // deliveryStatus: 2 部分到货
      if (row.isDeleted) return true
      if (row.isEmptyLine) return false
      // 商品行不存在收货记录时可修改
      const status1 = [
        'skuNo', 'materialDescription', 'warehouseLocation', 'shipWarehouseLocation',
        'taxedPrice', 'priceTimes', 'inputTax', 'isFree', 'soNo', 'soItemNo',
        'soPlanNo', 'supplierMaterialNo', 'trackNo', 'factoryCode', 'orderReason'
      ]
      if (status1.includes(col.prop)) {
        if (row.deliveryStatus === 0 || !row.deliveryStatus) {
          return false
        }
        if (col.prop === 'inputTax' && row.isInvoiced === 0) {
          // 进项税有收货记录,没有发票的情况下，不禁用
          return false
        }
        return true
      }
      // 商品行收货完结前可修改
      const status2 = [
        'itemDeliveryDate', 'isConfirmed', 'isUrgent', 'isDeliveryDone', 'itemQuantity'
      ]
      if (status2.includes(col.prop)) {
        if (row.deliveryStatus !== 1) {
          return false
        }
        return true
      }
      // 订单收货完结前可修改
      // const status3 = [
      //   'itemQuantity'
      // ]
      // if (status3.includes(col.prop)) {
      //   if (this.data.deliveryStatus !== 1) {
      //     return false
      //   }
      //   return true
      // }
      // 完全收货后不可修改
      const status4 = [
        'transferReason'
      ]
      if (status4.includes(col.prop)) {
        if (this.data.deliveryStatus !== 2) {
          return false
        }
        return true
      }
    },
    startLoading () {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      return loading
    },
    endLoading (loading) {
      if (loading) {
        loading.close()
      }
    },
    checCheckbox ({ row }) {
      if (this.isEditDelayedPaymentPo) return false
      const { recovery } = this.$route.query
      const { skuNo, materialDescription } = row
      return (!!skuNo || !!materialDescription) && (recovery !== 'true')
    },
    getWarehouseList (factoryCode) {
      if (factoryCode) {
        return this.warehouseList.filter(item => item.factoryCode === factoryCode)
      }
      return []
    },
    toDtl (row) {
      if (!row.skuNo && !row.materialDescription) {
        this.$message.warning({
          message: '请选择SKU'
        })
        return
      }
      this.$emit('showDetail', row)
    },
    onTableSelectionChange ({ records }) {
      this.$emit('update:records', records)
    },
    onTableSelectionAllChange ({ records }) {
      this.$emit('update:records', records)
    },
    disabledReturnS(col, row) {
      const { prop } = col
      // 选择项目类别为s后，【sku编码】、【价格】、【税率】、【仓库地点】、【跟踪单号】置灰，不可手动填写，由后端查询返回。
      // 数量也置灰 2022.11.22
      if (this.isReturnOrderType && ['skuNo', 'warehouseLocation', 'taxedPrice', 'priceTimes', 'inputTax', 'trackNo', 'itemQuantity'].includes(prop) && row.projectCategory === 'S') {
        return true
      }
      return false
    },
    handleCategoryChange (prop, value, row) {
      if (prop === 'projectCategory') {
        row.itemQuantity = 0
        row.isFree = 0
        if (value !== 'A') {
          delete row.fixedAssetsList
          row.fixedAssetsCardNo = ''
        }
        if (this.isReturnOrderType) {
          if (value === 'A') {
            row.warehouseLocation = null
          } else if (value === 'K') {
            row.refundableAmount = 0
          } else if (value === 'S' && this.isReturnProjectCategory(row)) {
            this.handleGetSku(row)
          }
        }
        this.$emit('addItem', row.skuNo, row)
      }
    },
    handleSelectSku (val, row) {
      if (val) {
        row.inSystemMaterial = true
        row.itemQuantity = 0
        row.isFree = 0
        this.$emit('addItem', val, row)
      } else {
        row.inSystemMaterial = false
      }
    },
    getTrackOrderHead(data) {
      const { trackNo } = data
      if (this.data.orderType === 'Z004' && trackNo) {
        getPOHead({ orderNo: trackNo }).then(res => {
            if (res && res.supplierNo !== this.data.supplierNo) {
              this.$message.warning('退货单供应商与跟踪单号供应商不一致，请核实是否有误！')
            }
        })
      }
    },
    getRefundableAmount(data) {
      const { factoryCode, itemQuantity, skuNo, taxedTotalAmount, trackNo, projectCategory } = data
      if (factoryCode && itemQuantity && skuNo && taxedTotalAmount && trackNo && projectCategory !== 'K') {
        const params = [{
          factoryCode,
          quantity: itemQuantity,
          skuNo,
          taxedTotalAmount,
          trackNo
        }]
        getRefundableAmountApi(params).then(res => {
          const resData = res[`${trackNo + '-' + skuNo + '-' + factoryCode}`]
          const { refundAmount, taxedPrice, priceTimes, inputTax, untaxedPrice } = resData || {}
          if (refundAmount) {
            data.taxedPrice = taxedPrice
            data.priceTimes = priceTimes
            data.untaxedPrice = untaxedPrice
            const taxRate = this.getInputTax(inputTax)
            data.inputTax = inputTax
            data.taxedTotalAmount = formatAmount(taxedPrice * itemQuantity / priceTimes, 2)
            data.untaxedTotalAmount = formatAmount(taxedPrice / (1 + taxRate * 0.01) * itemQuantity / priceTimes, 2)
            data.taxTotalAmount = formatAmount(data.taxedTotalAmount - data.untaxedTotalAmount, 2)
          }
          data.refundableAmount = refundAmount || data.taxedTotalAmount
        })
      }
      if (projectCategory === 'K') {
        data.refundableAmount = 0
      }
    },
    async handleItemNumChange (val, old, prop, row, idx) {
      console.log('item number change');
      // let row = deepClone(changedRow)
      const { priceTimes = 1 } = row
      if (prop === 'itemQuantity') {
        const { planList, fixedAssetsList } = row
        // 计划行和固定资产行都只有一行的时候联动
        if (planList && planList.length === 1) {
          planList[0].deliveryQuantity = val
        }
        if (fixedAssetsList && fixedAssetsList.length === 1) {
          fixedAssetsList[0].fixedAssetsCardQuantity = val
        }
        await assignByQuantity(this.data, row, old).then(async (res) => {
          if (res.data) {
            this.data = merge(this.data, res.data)
            row = merge(row, res.row)
            console.log(row, this.data);
            // this.$set(this.data.itemList, idx, data.row)
          }
        })
        const queryType = row.deliveryStatus === '2' ? 2 : 0
        await this.$emit('updateDeliveryDaysByWarehouseLocation', row, row.userAdded, queryType)
      }
      // 如果转储订单，不进行价格计算
      if (this.data.orderType !== 'Z003') {
        const taxRate = this.getInputTax(row.inputTax)
        if (prop === 'taxedPrice') {
          const taxedPrice = val || 0
          // row.untaxedPrice = (taxedPrice / (1 + taxRate * 0.01)).toFixed(2)
          row.taxedTotalAmount = formatAmount(taxedPrice * row.itemQuantity / priceTimes, 2)
          row.untaxedTotalAmount = formatAmount(taxedPrice / (1 + taxRate * 0.01) * row.itemQuantity / priceTimes, 2)
          row.untaxedPrice = formatAmount(row.untaxedTotalAmount / row.itemQuantity * priceTimes, 2)
        }
        if (prop === 'itemQuantity') {
          safeRun(() => {
            let { lossRatio } = this.data
            let { itemQuantity } = row
            if (!lossRatio) lossRatio = 0
            if (!itemQuantity) itemQuantity = 0
            row.componentList.forEach(comp => {
              const { materialGroupNum } = comp
              let number = Number((comp.numCalScale * itemQuantity).toFixed(3))
              if (materialGroupNum === 430) {
                number = Math.ceil(number)
              }
              comp.componentRequiredQuantity = number
            })
          })
        }
        row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
        await calcUnTaxedPrice(row, this.dictList).then(data => {
          if (data) row = merge(row, rowEditChange(data))
          console.log(row, data);
        })
      }
      if (this.isReturnOrderType) {
        if (prop === 'taxedPrice' || prop === 'itemQuantity') {
          row.refundableAmount = row.taxedTotalAmount
        }
        if (row.projectCategory === 'K') {
          row.refundableAmount = 0
        }
      }
      this.$emit('reactiveSetItem', row)
    },
    handleChangeItemDate (date, row, col) {
      const materialList = [ 'Z007', 'Z008', 'Z010' ]
      const { orderType, createTime } = this.data
      const { planList } = row
      safeRun(() => {
        if (planList && planList.length === 1) {
          planList[0].deliveryDate = date
        }
      })
      if (col.prop === 'itemDeliveryDate') {
        row.isConfirmed = 1
        if (materialList.includes(orderType)) {
          if (!row.inSystemMaterial && row.itemDeliveryDate) {
            row.standardLeadTime = this.diffDays(row.itemDeliveryDate, createTime)
          }
        }
        this.$emit('updateItemDeliveryDate', row)
      }
    },
    async handleChangeItemInputTax (val, row, prop) {
      if (val === 'J0') {
        this.$message.warning('进项税已选择0%，请确认是否有误！')
      }
      if (prop === 'inputTax') {
        const { priceTimes = 1 } = row
        const taxRate = this.getInputTax(val)
        // row.untaxedPrice = Number((row.taxedPrice / (1 + 0.01 * taxRate)).toFixed(2))
        row.taxedTotalAmount = formatAmount(row.taxedPrice * (row.itemQuantity || 0) / priceTimes, 2)
        row.untaxedTotalAmount = formatAmount(row.taxedPrice / (1 + taxRate * 0.01) * (row.itemQuantity || 0) / priceTimes, 2)
        row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
        await calcUnTaxedPrice(row, this.dictList).then(data => {
          if (data) row = merge(row, rowEditChange(data))
        })
      }
      this.$emit('reactiveSetItem', row)
    },
    handleChangeItemFactory (val, index, row) {
      const { warehouseLocation, soNo, skuNo } = row
      if (index === this.getFirstLineUpdateDefaultAddress()) {
        this.$emit('updateFactory', val, warehouseLocation, soNo, skuNo)
      }
      row.itemQuantity = 0
      row.isFree = 0
      if (this.isReturnOrderType) {
        this.getRefundableAmount(row)
      }
      this.$emit('addItem', row.skuNo, row)
    },
    handleOderReasonInput(val) {
      console.log(val)
    },
    handleChangeInput (val, prop, row) {
      if (prop === 'fixedAssetsCardNo') {
        if (!row.fixedAssetsList || !row.fixedAssetsList.length) {
          row.fixedAssetsList = [{
            fixedAssetsNo: '01'
          }]
        }
        row.fixedAssetsList[0].fixedAssetsCardNo = val
        const idx = this.data.itemList.indexOf(row)
        this.$set(this.data.itemList, idx, {
          ...row,
          fixedAssetsCardNo: val,
          fixedAssetsList: [ ...row.fixedAssetsList ]
        })
      }
      if (prop === 'materialDescription') {
        const inSystemMaterialList = [ 'Z007', 'Z008', 'Z010' ]
        if (inSystemMaterialList.includes(this.data.orderType)) {
          // 如果没有选择SKU，并修改了物料描述
          if (!row.skuNo) {
            row.inSystemMaterial = false
          }
          this.$emit('updateMaterialDescription', row)
        }
        if (!row.planList) {
          row.planList = [{
            deliveryQuantity: 0,
            deliveryDate: moment().format('YYYY-MM-DD'),
            planNo: '0001'
          }]
        }
        if (!row.fixedAssetsList && ['Z004', 'Z007'].includes(this.data.orderType)) {
          row.fixedAssetsList = [{
            fixedAssetsNo: '01'
          }]
        }
      }
      if (prop === 'soNo') {
        this.$emit('updateDeliveryDaysByWarehouseLocation', row, row.userAdded, 2)
        val = val.trim()
        if (val) {
          const loading = this.startLoading()
          getSO({ soNo: val }).then(data => {
            this.endLoading(loading)
            this.$emit('updateCustomerService', { ...data }, row)
          })
        } else {
          this.$emit('updateCustomerService', 'clear', row)
        }
      }
      if (prop === 'soItemNo') {
        this.$emit('updateDeliveryDaysByWarehouseLocation', row, row.userAdded, 2)
      }
      if (prop === 'trackNo' && this.isReturnOrderType) {
        this.getRefundableAmount(row)
        this.getTrackOrderHead(row)
      }
      if (prop === 'batchNo' && this.isReturnOrderType) {
        const { skuNo, factoryCode, projectCategory } = row
        const { supplierNo } = this.data
        if (supplierNo && factoryCode && skuNo && val) {
          const params = {
            factoryCode,
            supplierNo,
            itemList: [
              {
                batchNo: val,
                skuNo
              }
            ]
          }
          querySkuBatchPrice(params).then(res => {
            if (res?.code === 0) {
              const { data } = res
              if (data.length === 0) {
                this.$message.error('未查找到批次对应的采购价，请手工填写采购价！')
              } else if (data.length === 1) {
                row.trackNo = data[0].poNo
                if (projectCategory === 'K') {
                  row.refundableAmount = 0
                  this.$message.warning('跟踪单号已自动填充!')
                } else {
                  row.taxedPrice = data[0].taxedPrice
                  row.priceTimes = data[0].priceTimes
                  const taxRate = this.getInputTax(row.inputTax)
                  row.untaxedPrice = Number((row.taxedPrice / (1 + 0.01 * taxRate)).toFixed(2))
                  row.taxedTotalAmount = formatAmount(row.taxedPrice * row.itemQuantity / row.priceTimes, 2)
                  row.untaxedTotalAmount = formatAmount(row.taxedPrice / (1 + taxRate * 0.01) * row.itemQuantity / row.priceTimes, 2)
                  this.$message.warning('价格和跟踪单号已自动填充!')
                }
                this.getRefundableAmount(row)
              } else if (data.length > 1) {
                this.$message.error('查找到多条采购价，请手工填写采购价！')
              }
            }
          })
        }
      }
      if (this.isReturnProjectCategory(row) && ['specialSupplySo', 'specialSupplySoItem'].includes(prop)) {
        // 向后端获取sku编码，采购价格，仓库地点，跟踪单号等信息
        this.handleGetSku(row)
      }
    },
    // 向后端获取sku编码，采购价格，仓库地点，跟踪单号等信息
    async handleGetSku(row) {
      if (this.isReturnProjectCategory(row)) {
        const idx = this.data.itemList.indexOf(row)
        const data = await handleGetInfoBySpecialSupplySo(row, this.dictList)
        if (data.skuNo) {
          const { taxedPrice, itemQuantity, priceTimes, inputTax } = data
          const taxRate = this.getInputTax(inputTax)
          const taxedTotalAmount = formatAmount(taxedPrice * itemQuantity / priceTimes, 2)
          const untaxedTotalAmount = formatAmount(taxedPrice / (1 + taxRate * 0.01) * itemQuantity / priceTimes, 2)
          const taxTotalAmount = formatAmount(taxedTotalAmount - untaxedTotalAmount, 2)
          // const refundableAmount = refundAmount || data.taxedTotalAmount
          const newData = {
            ...row,
            ...data,
            taxedTotalAmount,
            untaxedTotalAmount,
            taxTotalAmount
          }
          delete newData.isEmptyLine
          if (!newData.planList) {
            newData.planList = [{
              deliveryQuantity: newData.itemQuantity,
              // deliveryDate: moment().format('YYYY-MM-DD'),
              planNo: '0001'
            }]
          }
          this.$set(this.data.itemList, idx, newData)
          const { itemList } = this.data
          const len = itemList.length
          if (itemList[len - 1] === row || (itemList[len - 1] && itemList[len - 1].uuid) === (row && row.uuid)) {
            this.$emit('addItemEmptyLine')
          }
        }
      }
    },
    // 退货订单，专供销售单号，专供销售行号，项目类别为S
    isReturnProjectCategory(row) {
      return this.isReturnOrderType && (row?.specialSupplySo ?? '') !== '' && (row?.specialSupplySoItem ?? '') !== '' && row?.projectCategory === 'S'
    },
    handleChangeMaterialGroup (val, row) {
      if (!val) {
        val = { materialGroupName: '', materialGroupNum: '' }
        row.materialGroup = {}
      }
      const { materialGroupName, materialGroupNum } = val
      row.materialGroupName = materialGroupName
      row.materialGroupNum = materialGroupNum
      const { itemList } = this.data
      const idx = itemList.findIndex(item => item === row)
      const newData = {
        ...itemList[idx],
        materialGroupName,
        materialGroupNum
      }
      if (materialGroupNum) {
        newData.materialGroup = {
          materialGroupName,
          materialGroupNum
        }
      }
      this.$set(this.data.itemList, idx, newData)
    },
    getInputTax (val) {
      const taxRate = Number((this.dictList['inputTax'].find(item => item.value === val) || {}).description) || 0
      return taxRate
    },
    disableIsDeliveryDone (row, col) {
      if (col.prop === 'isDeliveryDone') {
        if (row.receivedQuantity === 0) {
          return true
        }
        if (row.receivedQuantity === row.itemQuantity) {
          return true
        }
      }
    },
    async handleChangeCheckbox (val, prop, row) {
      const taxRate = this.getInputTax(row.inputTax)
      // eslint-disable-next-line
      if (val == 1 && prop === 'isFree') {
        row.taxedPrice = 0
        row.untaxedPrice = ''
        row.taxedTotalAmount = ''
        row.untaxedTotalAmount = ''
        row.taxTotalAmount = ''
        row.poItemExtend.lastGrossMargin = 100
        row.editChange = true
      }
      // eslint-disable-next-line
      if (prop === 'isDeliveryDone' && val == 1 && row.receivedQuantity > 0) {
        // 部分收货: 商品行数量 = 商品上已收货数量, 计划行 = 计划行已收数量
        // 全部收货: 商品已收货数量 = 商品订单数量
        row.itemQuantity = row.receivedQuantity
        this.reCalcCompQuantity(null, row, true)
        safeRun(() => {
          row.planList.forEach(plan => {
            plan.deliveryQuantity = plan.receivedQuantity
          })
        })
        // row.untaxedPrice = Number((row.taxedPrice / (1 + 0.01 * taxRate)).toFixed(2))
        // row.taxedTotalAmount = formatAmount(row.taxedPrice * (row.itemQuantity || 0) / priceTimes, 2)
        // row.untaxedTotalAmount = formatAmount(row.taxedPrice / (1 + taxRate * 0.01) * (row.itemQuantity || 0) / priceTimes, 2)
        // row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
        const { taxedPrice, untaxedPrice, itemQuantity = 0, priceTimes = 1 } = row
        row.taxedTotalAmount = formatAmount(taxedPrice ? taxedPrice * itemQuantity / priceTimes : 0, 2)
        row.untaxedTotalAmount = formatAmount(untaxedPrice ? taxedPrice / (1 + taxRate * 0.01) * itemQuantity / priceTimes : 0, 2)
        row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
        await calcUnTaxedPrice(row, this.dictList).then(data => {
          if (data) row = merge(row, rowEditChange(data))
        })
      }
      if (prop === 'isConfirmed') {
        this.setTitleCheckbox(prop)
      }
      if (prop === 'isUrgent') {
        this.setTitleCheckbox(prop)
      }
    },
    handleStandardLeadTime (val, row) {
      if (val != null) {
        const { createTime } = this.data
        if (createTime) {
          row.itemDeliveryDate = moment(createTime).add(val, 'days').format('YYYY-MM-DD')
        }
      }
    },
    isFirstValidWarehouse (index, row) {
      if (row.isEmptyLine) return false
      if (index === 0 && !row.isDeleted) return true
      const { itemList } = this.data
      let ret = true
      // 第一个非删除/新增行
      for (let idx = 0; idx < index; idx++) {
        if (itemList[idx] && !itemList[idx].isDeleted) {
          ret = false
        }
      }
      return ret
    },
    changeWarehouseLocation (prop, val, index, row) {
      const { factoryCode, soNo, warehouseLocation, shipWarehouseLocation, skuNo } = row
      const { orderType } = this.data
      if (prop === 'warehouseLocation' && index === this.getFirstLineUpdateDefaultAddress()) {
        this.$emit('updateWarehouseLocation', factoryCode, val, soNo, skuNo)
      }
      if (orderType === 'Z003') {
        if (factoryCode && warehouseLocation && shipWarehouseLocation) {
          this.$emit('updateTransferTime', factoryCode, warehouseLocation, shipWarehouseLocation, row)
        }
      } else {
        this.$emit('updateDeliveryDaysByWarehouseLocation', row, row.userAdded, 2)
      }
      if (orderType === 'Z006') {
        this.$emit('updateCompWarehouseByWarehouse', val, index, row)
      }
    },
    async handlePriceTimesChange (val, row) {
      const { priceTimes, inputTax } = row
      const taxRate = this.getInputTax(inputTax)
      // row.untaxedPrice = Number((row.taxedPrice / (1 + 0.01 * taxRate)).toFixed(2))
      row.taxedTotalAmount = formatAmount(row.taxedPrice * (row.itemQuantity || 0) / priceTimes, 2)
      row.untaxedTotalAmount = formatAmount(row.taxedPrice / (1 + taxRate * 0.01) * (row.itemQuantity || 0) / priceTimes, 2)
      row.taxTotalAmount = formatAmount(row.taxedTotalAmount - row.untaxedTotalAmount, 2)
      await calcUnTaxedPrice(row, this.dictList).then(data => {
        if (data) row = merge(row, rowEditChange(data))
      })
      if (this.isReturnOrderType) {
        if (row.projectCategory !== 'K') {
          row.refundableAmount = row.taxedTotalAmount
        } else {
          row.refundableAmount = 0
        }
      }
      this.$emit('reactiveSetItem', row)
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      warehouseList: state => state.orderPurchase.warehouseList
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.status !== 'hide')
          .filter(item => item.category === 'table' && (/edit/.test(item.status) || !item.status))
        const unitList = [ 'Z010', 'Z008', 'Z007', 'Z004' ]
        const ableSubCateList = [ 'Z004', 'Z005' ]
        ret.forEach(field => {
          if (field.prop === 'materialGroup') {
            field.format = row => row.materialGroupNum ? (row.materialGroupNum || ' ') + ' ' + (row.materialGroup || row.materialGroupName || '') : ''
          }
          if (field.prop === 'unitName' && unitList.includes(this.data.orderType)) {
            field.prop = 'unit'
            field.type = 'select'
            field.enums = 'orderUnit'
          }
          if (field.prop === 'projectCategory' && !ableSubCateList.includes(this.data.orderType)) {
            field.disabled = true
            field.type = 'text'
          }
        })
        // 交期变更原因
      if (!ret.find(item => item.prop === 'deliveryChangeReason')) {
        const item = {
          category: 'table',
          disabled: 0,
          name: '交期变更原因',
          prop: 'deliveryChangeReason',
          status: 'detail',
          type: 'select',
          enums: 'deliveryChangeReason',
          sequence: 261,
          width: 200
        }
        const idx = ret.findIndex(item => item.prop === 'factoryCode')
        if (idx > -1) {
          ret.splice(idx + 1, 0, item)
        } else {
          ret.push(item)
        }
      }
        // if (!this.data?.itemList.find(item => item.approveTriggerDetail)) {
        //   ret = ret.filter(item => !(item.prop === 'orderReason'))
        // }
      })
      console.log(ret);
      return ret
    },
    supplierId () {
      return (this.data.supplier || {}).providerId
    },
    dateOptions () {
      // 商品行修改不能修改为当前日期之前的日期
      const newCreateTime = moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD')
      return {
        disabledDate: (time) => {
          return time.getTime() < (new Date(newCreateTime)).getTime()
        }
      }
    },
    orderType () {
      const { orderType } = this.data
      return orderType
    },
    isReturnOrderType() {
      return this.orderType === 'Z004'
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .checkbox-column .vxe-cell.c--tooltip  {
  padding: 2px 0px;
  margin-left: -8px;
}
.orderReason-select {
  width: 100px !important;
}
.required {
  &:before {
    content: '*';
    color: #ff7268;
    margin-right: 4px;
  }
  &::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
  }
}
</style>
