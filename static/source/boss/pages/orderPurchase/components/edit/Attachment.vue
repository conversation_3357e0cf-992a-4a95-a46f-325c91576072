<template>
  <div class="tele-info">
    <div class="supplier-attachmentList">
      <div style="margin-top: 5px">
        确认订单相关附件：
      </div>
      <el-upload
        ref="upload"
        style="display: inline-block"
        action="/upload"
        :accept="acceptFileType.poCommonType"
        :on-remove="handleRemove"
        :show-file-list="true"
        :multiple="false"
        :http-request="httpRequestHandle"
        :before-upload="beforeUpload"
        :file-list="fileList"
        :disabled="isEditDelayedPaymentPo"
      >
        <el-button :disabled="isEditDelayedPaymentPo" size="small" type="primary">点击上传</el-button>
      </el-upload>
    </div>
    <div class="tips">
      *临时供应商无有效年框且未签署授权书时，需上传确认订单相关附件
    </div>
  </div>
</template>
<script>
import { upload } from '@/utils/upload'
import { mapState } from 'vuex'
import { remove } from 'lodash'
export default {
  name: 'Attachment',
  props: ['purchaseData', 'isEditDelayedPaymentPo'],
  data () {
    return {
      fileSizeLimit: 50
    }
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    }),
    fileList () {
      return (this.purchaseData?.supplierContractAttachmentList || []).map(item => {
        return {
          name: item.fileName,
          url: item.fileUrl,
          uid: item.id
        }
      }) || []
    }
  },
  methods: {
    // 删除文件
    handleRemove(file, fileList) {
      remove(fileList, function (item) {
        return item.uid === file.uid
      });
      this.purchaseData.supplierContractAttachmentList = fileList.map(item => {
        return {
          attachmentType: 2,
          createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
          poNo: this.purchaseData.orderNo,
          fileName: item.name,
          fileUrl: item.url
        }
      })
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false
      // 校验大小
      const isGtLimit = file.size / 1024 / 1024 < this.fileSizeLimit
      if (!isGtLimit) {
        this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
      }

      return isGtLimit;
    },
    async httpRequestHandle (file) {
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      const response = await upload(`mm/attachment/supplierAttachment/${this.purchaseData.orderNo}`, file.file);
      if (response?.url) {
        this.purchaseData.supplierContractAttachmentList.push({
          attachmentType: 2,
          createUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
          poNo: this.purchaseData.orderNo,
          fileName: file.file.name,
          fileUrl: response.url
        })
      } else {
        // this.$message.error('上传失败！')
      }
      loading.close()
    },
    beforeAvatarUpload(file) {
      const isLt50M = file.size / 1024 / 1024 < 50;
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!');
      }
      return isLt50M;
    },
    openPicUrl(path) {
      let url = path
      try {
        if (url.indexOf('https') === -1) {
          url = 'https' + url.replace(/http(s)?/, '')
        }
      } catch (err) {
        url = path
      }
      window.open(url);
    }
  },
  created () {}
}
</script>
<style lang="scss" scoped >
.supplier-attachmentList{
  display: flex;
}
.pic-preview-card {
  display: flex;
  flex-direction: column;
  width: 110px;
  height: 110px;
  margin-right: 5px;
  img {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    border: solid 1px lightgrey;
    padding: 5px;
    margin: 5px;
  }
}
.tips {
  color: red;
  font-size: 10px;
}
</style>
