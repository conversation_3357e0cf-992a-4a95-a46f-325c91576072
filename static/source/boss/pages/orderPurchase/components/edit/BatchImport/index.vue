<template>
  <div class="batch-import">
    <div>
      请在输入框内输入{{placeholder('trim')}}，用空格隔开。一行对应一种商品，请注意换行。
      <span class="strong">支持直接在Excel中复制粘贴至输入框内</span>，快速导入商品，最多不超过{{maxLine}}行。
      <div class="strong warning">注意：若数量修改影响价格，导入后请在界面手动更新含税采购价格和价格倍数</div>
    </div>
    <div class="batch-import-input-row">
      <el-input
        :autosize="{ minRows: 4, maxRows: 6}"
        class="batch-import-input"
        type="textarea"
        :placeholder="placeholder()"
        v-model="text"
        resize="none"
        clearable
      >
      </el-input>
    </div>
    <div class="batch-import-btn-row">
      <el-button class="batch-import-btn" type="primary" @click="handleImport">确认导入</el-button>
      <el-button class="batch-import-btn" @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>
<script>

export default {
  props: ['purchaseData'],
  data () {
    return {
      maxLine: 50,
      text: ''
    }
  },
  computed: {
  },
  methods: {
    placeholder (trim) {
      const { orderType } = this.purchaseData
      let ret = '例如：SKU编码、数量、仓库地点'
      if (orderType === 'Z003') {
        ret = '例如：SKU编码、数量、发货仓库地点、仓库地点'
      }
      if (orderType === 'Z004') {
        ret = '例如：SKU编码、数量、仓库地点、退货原因、跟踪单号'
      }
      if (orderType === 'Z007') {
        ret = '例如：SKU编码、数量、资产卡片号'
      }
      if (orderType === 'Z008') {
        ret = '例如：SKU编码、数量、总账科目、成本中心'
      }
      if (trim) {
        ret = ret.replace('例如：', '')
      }
      return ret
    },
    handleCancel () {
      this.$emit('close')
    },
    handleImport () {
      if (this.text) {
        const uploadSkuInfoVOList = []
        let rows = this.text.split('\n')
        if (rows && rows.length > this.maxLine) {
          rows = rows.slice(0, this.maxLine)
          this.$message.error(`本次输入行数大于${this.maxLine}行，超出部分已自动截断！`)
        }
        if (rows && rows.length > 0) {
          rows.filter(row => row).map(row => row.trim()).forEach(row => {
            const strList = row.split(/\s+|,|，/).map(str => str.trim()).filter(row => row)
            if (!strList.length) return
            const rowData = { skuNo: strList[0] }
            rowData['quantity'] = strList[1] || 0
            rowData['warehouseLocation'] = strList[2] || ''
            switch (this.purchaseData.orderType) {
              case 'Z003':
                rowData['shipWarehouseLocation'] = strList[2] || ''
                rowData['warehouseLocation'] = strList[3] || ''
                break;
              case 'Z004':
                rowData['returnReason'] = strList[3] || ''
                rowData['trackNo'] = strList[4] || ''
                break;
              case 'Z007':
                delete rowData['warehouseLocation']
                rowData['fixedAssetsCardNo'] = strList[2] || ''
                break;
              case 'Z008':
                delete rowData['warehouseLocation']
                rowData['generalLedgerAccount'] = strList[2] || ''
                rowData['costCenter'] = strList[3] || ''
                break;
            }
            rowData.quantity = parseFloat(rowData.quantity)
            if (rowData.skuNo) {
              rowData.inSystemMaterial = true
            }
            uploadSkuInfoVOList.push(rowData)
          })
          setTimeout(() => {
            this.$emit('import', uploadSkuInfoVOList)
          }, 200)
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.batch-import {
  &-btn {
    width: 90px;
  }
  .batch-import-input-row {
    margin: 10px 0;
  }
  .batch-import-btn-row {
    margin: 10px auto;
    text-align: center;
  }
  .strong {
    font-weight: bold;
  }

  .warning{
    color: red;
  }
}
</style>
