<template>
  <div>
    <el-row v-for="item in getColumns" :key="item.name" :gutter="20">
      <el-form-item :label="item.name" :prop="item.prop">
        <el-input
          type="textarea"
          :rows="2"
          v-model="purchaseData[item.prop]"
          :maxlength="item.length"
          :placeholder="item.name"
          show-word-limit
          :disabled="isEditDelayedPaymentPo"
          clearable
        />
      </el-form-item>
    </el-row>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
export default {
  name: 'EditRemarkText',
  props: ['finalFields', 'purchaseData', 'isEditDelayedPaymentPo'],
  computed: {
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(item => item.category === 'remark' && (/edit/.test(item.status) || !item.status))
      })
      return ret
    }
  },
  data () {
    return {}
  },
  methods: {},
  created () {}
}
</script>
