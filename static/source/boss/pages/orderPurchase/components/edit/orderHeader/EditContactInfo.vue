<template>
  <div>
    <el-row :gutter="20">
      <el-col v-for="item in getColumns" :key="item.name" :span="item.span">
        <el-form-item :label="item.name" :prop="item.prop">
          <el-input
            v-if="item.type==='input'"
            v-model="purchaseData[item.prop]"
            clearable
            :disabled="item.disabled===true || isEditDelayedPaymentPo"
            :placeholder="item.name"
          />
          <el-select
            v-else-if="item.type==='select'&&item.prop==='supplierContactName'"
            v-model="purchaseData[item.prop]"
            clearable
            filterable
            :disabled="isEditDelayedPaymentPo"
            @change="handleChangeSupplierContactName"
            style="width:100%"
          >
            <el-option
              v-for="(item,idx) in supplierContactList"
              :key="idx+item.phone"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'&&item.prop==='supplierAddress'"
            v-model="purchaseData[item.prop]"
            :disabled="isEditDelayedPaymentPo"
            clearable
            filterable
            @change="handleChangeSupplierAddress"
            style="width:100%"
          >
            <el-option
              v-for="(item,idx) in supplierAddressList"
              :key="idx+item.detail"
              :label="fullDetailAddress(item)"
              :value="fullDetailAddress(item)">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'&&item.prop==='receiptContactName'"
            v-model="purchaseData[item.prop]"
            clearable
            allow-create
            :disabled="isEditDelayedPaymentPo"
            filterable
            style="width:100%"
            @change="handleChangeReceiptContactName"
          >
            <el-option
              v-for="(item, index) in receiptContactList"
              :key="`${index}${item.phone}`"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'&&item.prop==='receiptAddress'"
            v-model="purchaseData[item.prop]"
            clearable
            filterable
            :disabled="isEditDelayedPaymentPo"
            style="width:100%"
            @change="handleChangeReceiptAddress"
          >
            <el-option
              v-for="(item,idx) in receiptAddressList"
              :key="idx+item.detail"
              :label="fullDetailAddress(item)"
              :value="fullDetailAddress(item)">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'&&item.prop==='receiveContactName'"
            v-model="purchaseData[item.prop]"
            clearable
            allow-create
            :disabled="isEditDelayedPaymentPo"
            filterable
            style="width:100%"
            @change="handleChangeReceiveContactName"
          >
            <el-option
              v-for="(item, idx) in receiveContactList"
              :key="idx+item.phone"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'&&item.prop==='receiveAddress'"
            v-model="purchaseData[item.prop]"
            clearable
            filterable
            :disabled="isEditDelayedPaymentPo"
            style="width:100%"
            @change="handleChangeReceiveAddress"
          >
            <el-option
              v-for="(item,idx) in receiveAddressList"
              :key="idx+item.detail"
              :label="fullDetailAddress(item)"
              :value="fullDetailAddress(item)">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
export default {
  name: 'EditContactInfo',
  props: [
    'finalFields',
    'purchaseData',
    'supplierAddressList',
    'supplierContactList',
    'receiptContactList',
    'receiptAddressList',
    'receiveContactList',
    'receiveAddressList',
    'isEditDelayedPaymentPo'
  ],
  computed: {
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(item => item.category === 'comm' && (/edit/.test(item.status) || !item.status))
      })
      return ret
    }
  },
  data () {
    return {}
  },
  methods: {
    fullDetailAddress (item) {
      let ret = ''
      safeRun(() => {
        ret = (item.provinceText || '') +
                (item.cityText || '') +
                (item.regionText || '') +
                (item.streetText || '') +
                (item.detail || '')
      })
      return ret
    },
    handleChangeSupplierAddress (val) {
      if (val) {
        const supplierAddress = this.supplierAddressList.find(item => val === this.fullDetailAddress(item))
        if (supplierAddress) {
          this.$emit('setAddress', 'supplier', supplierAddress)
        }
      } else {
        this.$emit('setAddress', 'supplier')
      }
    },
    handleChangeSupplierContactName (val) {
      if (val) {
        const supplierContact = this.supplierContactList.find(item => item.name === val)
        if (supplierContact) {
          this.purchaseData.supplierContactPhone = supplierContact.phone
        }
      } else {
        this.purchaseData.supplierContactPhone = ''
      }
    },
    handleChangeReceiptContactName (val) {
      if (val) {
        const contact = this.receiptContactList.find(item => item.name === val)
        if (contact) {
          this.purchaseData.receiptContactPhone = contact.phone
        }
      } else {
        this.purchaseData.receiptContactPhone = ''
      }
    },
    handleChangeReceiptAddress (val) {
      if (val) {
        const address = this.receiptAddressList.find(item => val === this.fullDetailAddress(item))
        if (address) {
          this.$emit('setAddress', 'receipt', address)
        }
      } else {
        this.$emit('setAddress', 'receipt')
      }
    },
    handleChangeReceiveContactName (val) {
      if (val) {
        const contact = this.receiveContactList.find(item => item.name === val)
        if (contact) {
          this.purchaseData.receiveContactPhone = contact.phone
        }
      } else {
        this.purchaseData.receiveContactPhone = ''
      }
    },
    handleChangeReceiveAddress (val) {
      if (val) {
        const address = this.receiveAddressList.find(item => this.fullDetailAddress(item) === val)
        if (address) {
          this.$emit('setAddress', 'receive', address)
        }
      } else {
        this.$emit('setAddress', 'receive')
      }
    }
  },
  created () {}
}
</script>
