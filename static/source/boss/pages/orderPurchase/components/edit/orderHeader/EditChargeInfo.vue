<template>
  <div class="charge-info">
    <el-form label-width="110px">
      <el-row class="charge-row">可输入一个或多个待分配金额，点击
        <el-button :disabled="isEditDelayedPaymentPo" size="mini" type="primary" class="charge-row-btn" @click="handleOneAssign">一键分摊</el-button>
      将金额分配到未收货且未删除的商品行上。其中分摊运费做叠加处理。
      </el-row>
      <el-row :gutter="20">
        <el-col v-for="childItem in fieldAllocationContent" :key="childItem.name" :span="childItem.span || 12">
          <span style="display: inline-block;margin-bottom: 5px">
            <span class="charge-row-item">
              <span>{{childItem.name}}</span>:
              <span class="charge-row-item-highlight">
                {{purchaseData[childItem.prop+'Amount'] && purchaseData[childItem.prop + 'Type'] ? getItemType(childItem.prop + 'Type') : ''}}
                {{purchaseData[childItem.prop+'Amount'] ? Number(purchaseData[childItem.prop+'Amount']).toFixed(2) : ''}}
              </span>
              <span class="charge-row-item-highlight">{{purchaseData[childItem.prop+'Amount'] ? purchaseData[childItem.prop+'Currency'] : ''}}</span>
            </span>
            <el-select
              size="mini"
              :disabled="disableCharge(childItem.prop) || isEditDelayedPaymentPo"
              v-if="purchaseData.orderType==='Z002' && childItem.prop==='customsFee'"
              v-model="purchaseData[childItem.prop+'TypeSelect']"
              placeholder="请选择报关杂费类别"
              clearable
              style="width:220px;margin-right:10px"
            >
              <el-option
                v-for="item in dictList['customsFeeType']"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
            <el-select
              size="mini"
              :disabled="disableCharge(childItem.prop) || isEditDelayedPaymentPo"
              v-if="purchaseData.orderType==='Z002' && childItem.prop==='intlShipping'"
              v-model="purchaseData[childItem.prop+'TypeSelect']"
              placeholder="请选择国际运费类别"
              @change="handleSc"
              clearable
              style="width:220px;margin-right:10px"
            >
              <el-option
                v-for="item in dictList['intlShippingType']"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
            <el-input-number
              size="mini"
              :disabled="disableCharge(childItem.prop) || isEditDelayedPaymentPo"
              v-model="purchaseData[childItem.prop]"
              clearable
              :placeholder="childItem.name"
              :precision="2"
              style="width:120px;margin-right:10px"
            />
            <el-select
              size="mini"
              :disabled="disableCharge(childItem.prop) || isEditDelayedPaymentPo"
              v-if="childItem.prop.indexOf('share')===-1"
              v-model="purchaseData[childItem.prop+'CurrencyInput']"
              clearable
              filterable
              default-first-option
              placeholder="币别"
              style="width:90px;margin-right:10px"
            >
              <el-option
                v-for="item in dictList['sapCurrency']"
                :key="item.value"
                :label="item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
            <SelectSupplierNo
              size="mini"
              :disabled="disableCharge(childItem.prop) || isEditDelayedPaymentPo"
              v-if="childItem.prop.indexOf('share')===-1"
              v-model="purchaseData[childItem.prop+'SupplierNo']"
              @change="(item,itemList)=>handleChangeSupplier(childItem.prop+'SupplierNo',item,itemList)"
              style="width:120px;"
            />
          </span>
        </el-col>
        <el-col :span="24">
          <span style="display: flex;align-items: center;margin-bottom: 5px">
            <span class="charge-row-item">
              <span>杂费总计:</span>
              <span class="charge-row-item-highlight">
                {{purchaseData['sundryAmount'] ? Number(purchaseData['sundryAmount']).toFixed(2) : ''}}
              </span>
            </span>
            <el-input-number
              size="mini"
              :disabled="isEditDelayedPaymentPo"
              v-model="purchaseData['sundry']"
              clearable
              placeholder="杂费"
              :precision="2"
              style="width:120px;margin-right:10px"
            />
            <el-select multiple style="width: 200px;" v-model="purchaseData['sundryReason']" placeholder="请选择杂费原因">
              <el-option v-for="item in dictList.sundryReason" :key="item.value" :label="item.name" :value="item.value"></el-option>
            </el-select>
            <el-tooltip placement="top">
              <div slot="content">
                1、客户特殊需求：如现场需要上楼，指定快递送货；<br/>
                2、超出包邮区域：如订单价格只江浙沪包邮，但实际订单地址在新疆；<br/>
                3、客户需求变更：如收货地址变更、二次派送、货物滞留费。
              </div>
              <i class="el-icon-question" style="margin-left: 5px;margin-right: 10px;"></i>
            </el-tooltip>
            <el-input v-model="purchaseData['sundryReasonDetail']" type="textarea" :rows="1" show-word-limit maxlength="30" style="width: 300px;" placeholder="请输入杂费原因备注"></el-input>
          </span>
        </el-col>
        <el-col :span="12" v-if="purchaseData.orderType==='Z001'">
          <span style="display: inline-block;margin-bottom: 5px">
            <span class="charge-row-item">
              <span>分摊返利总计:</span>
              <span class="charge-row-item-highlight">
                {{purchaseData['shareRebateAmount'] ? Number(purchaseData['shareRebateAmount']).toFixed(2) : ''}}
              </span>
            </span>
            <SelectRebateDialog :purchaseData="purchaseData" :isEditDelayedPaymentPo="isEditDelayedPaymentPo" />
          </span>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="3">
          <el-form-item label="异常延期付款">
            <el-checkbox
              v-model="purchaseData.poExtend.isDelayedPayment"
              :true-label="1"
              :false-label="0"
            />
          </el-form-item>
        </el-col>
        <el-col v-for="childItem in fieldAllocationCheckbox" :key="childItem.name" :span="childItem.span || 12">
          <el-form-item v-if="childItem.type==='checkbox'" :label="childItem.name" :prop="childItem.prop">
            <el-checkbox
              v-model="purchaseData[childItem.prop]"
              @change="value=>handleChangeChargeCheckbox(childItem.prop, value)"
              :true-label="1"
              :false-label="0"
              :disabled="isEditDelayedPaymentPo"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20" >
          <el-col :span="12" style="display: flex; align-items: flex-start" v-if="purchaseData.orderType === 'Z001'">
          <span>附件凭证：</span>
            <el-upload
              ref="upload"
              action="/upload"
              :accept="acceptFileType.poCommonType"
              :before-upload="(file) => $validateFileType(file, acceptFileType.poCommonType)"
              :on-remove="handleRemove"
              :show-file-list="true"
              :multiple="false"
              :http-request="httpRequestHandle"
              :file-list="fileList"
              :disabled="isEditDelayedPaymentPo"
              >
              <el-button :disabled="isEditDelayedPaymentPo" size="small" type="primary">点击上传</el-button>
            </el-upload>
          </el-col>
        </el-row>
    </el-form>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import SelectSupplierNo from '@/pages/orderPurchase/components/common/SelectSupplierNo'
import SelectRebateDialog from '@/pages/orderPurchase/components/common/SelectRebateDialog'
import { remove } from 'lodash'
import { upload } from '@/utils/upload'
import * as shortid from 'shortid'
import { mapState } from 'vuex'

export default {
  name: 'EditChargeInfo',
  props: ['finalFields', 'purchaseData', 'dictList', 'disableCharge', 'handleChangeChargeCheckbox', 'isEditDelayedPaymentPo'],
  components: {
    SelectSupplierNo,
    SelectRebateDialog
  },
  data () {
    return {
      itemSupplier: {},
      fileList: []
    }
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType || {}
    }),
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(item => item.category === 'allocation' && (/edit/.test(item.status) || !item.status))
      })
      return ret
    },
    fieldAllocationContent () {
      return this.getColumns.filter(item => item.type !== 'checkbox')
    },
    fieldAllocationCheckbox () {
      return this.getColumns.filter(item => item.type === 'checkbox')
    }
  },
  watch: {
    'purchaseData.initialShippingAmountAttachmentList'(newVal) {
       this.fileList = newVal.map(item => {
        return {
          name: item.fileName,
          url: item.fileUrl,
          uid: item.uid ?? shortid.generate()
        }
      })
    }
  },
  methods: {
    handleSc() {
      console.log(this.purchaseData);
    },
    getItemType(prop) {
      return this.dictList[prop].find((item) => Number(item.value) === Number(this.purchaseData[prop]))?.name || ''
    },
    handleOneAssign () {
      this.$emit('handleOneAssign')
    },
    handleChangeSupplier (prop, item, itemList) {
      console.log(prop, item, itemList)
      const supplier = itemList.find(_item => _item.supplierNo === item)
      this.$emit('setItemSupplier', prop, supplier)
      this.$set(this.purchaseData, prop, item)
    },
      // 上传文件
    async httpRequestHandle (file) {
      // 校验大小
      // const i//sGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit
      // if (isGtLimit) {
      // this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
      // return
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      const response = await upload('mm/attachment/shippingAttachment', file.file);
      if (response?.url) {
        this.fileList.push({
          uid: file.file.uid,
          name: file.file.name,
          url: response.url
        })
        this.$emit('handleAttachmentList', this.fileList)
      } else {
        // this.$message.error('上传失败！')
      }
      loading.close()
    },
    // 删除文件
    handleRemove(file, fileList) {
      remove(this.fileList, function (item) {
        return item.uid === file.uid
      });
      this.$emit('handleAttachmentList', this.fileList)
    }
  },
  mounted () {
    // this.fileList = this.purchaseData.initialShippingAmountAttachmentList.map(item => {
    //   return {
    //     name: item.fileName,
    //     url: item.fileUrl,
    //     uid: shortid.generate()
    //   }
    // })
  }
}
</script>
<style lang="scss" scoped>
::v-deep .checkbox-column .vxe-cell.c--tooltip  {
  padding: 2px 0px;
  margin-left: -8px;
}
.charge-info{
  .charge-row {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
  }
  .charge-row-item {
    display: inline-block;
    min-width: 180px;
    .charge-row-item-highlight{
      margin-right: 5px;
      font-weight: bold;
    }
  }
  .row-item{
    margin-bottom: 10px;
  }
  .dialog{
    padding: 20px;
  }
  .content{
    padding: 0 35px;
    display: flex;
    align-items: center;
    .label{
      width: 80px;
    }
  }
  .tips{
    padding: 0 35px;
    display: flex;
    flex-direction: column;
    justify-content: left;
    align-items: left;
  }
}
</style>
<style lang="scss">
.clickable{
  margin-left: 10px;
  cursor: pointer;
  &:hover{
    opacity: 0.7;
  }
  &:active{
    opacity: 0.9;
  }
}
</style>
