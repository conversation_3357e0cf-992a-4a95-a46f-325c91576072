<template>
  <div>
    <el-row :gutter="20" class="edit-base-info">
      <el-col :span="item.span || 8" v-for="item in getColumns" :key="item.name">
        <el-form-item :label="item.name" :prop="item.prop">
          <SelectSupplier
            ref="supplier"
            disabled
            defaultValue
            v-if="item.prop==='supplier'&&item.type==='custom'"
            :data.sync="purchaseData[item.prop]"
            :purchaseData="purchaseData"
            @change="handleSupplierChange"
          />
          <el-select
            ref="customerName"
            v-else-if="item.prop==='customerName'&&item.type==='custom'"
            style="width:100%"
            placeholder="请选择"
            v-model="purchaseData[item.prop]"
            value-key="value"
          >
            <el-option
              v-for="item in customerNameOptions"
              :key="item.value"
              :label="item.label"
              :value="item">
            </el-option>
          </el-select>
          <el-input
            v-else-if="item.type==='input'"
            v-model="purchaseData[item.prop]"
            clearable
            maxlength="35"
            :placeholder="`请输入${item.name}`"
            :disabled="Boolean(item.disabled)"
          />
          <el-input-number
            v-else-if="item.type==='number'"
            v-model="purchaseData[item.prop]"
            clearable
            :placeholder="`请输入${item.name === '损耗比' ? (item.name + '%') : item.name}`"
            :disabled="Boolean(item.disabled)"
            :precision="item.precision||0"
            @change="(val)=>handleNumberChange(val,item.prop)"
            :min="0"
            :max="100"
            style="width:100%"
          />
          <el-checkbox
            v-else-if="item.type==='checkbox'"
            :indeterminate="isIndeterminate(item.prop)"
            :disabled="Boolean(item.disabled) || (disableByDeliveryStatus(item.prop) && disableByIsZkhPickUp(item.prop))"
            v-model="purchaseData[item.prop]"
            :true-label="1"
            :false-label="0"
            @change="(val) => handleCheckboxChange(val, item.prop)"
          />
          <el-date-picker
            v-else-if="/date/.test(item.type)"
            v-model="purchaseData[item.prop]"
            :disabled="Boolean(item.disabled)"
            style="width:100%"
            clearable type="date" placeholder="选择日期" />
          <el-select
            v-else-if="item.type==='select'&&item.prop==='companyCode'"
            v-model="purchaseData[item.prop]"
            disabled
            style="width:100%"
            filterable
            @change="handleChangeCompany"
            ref="company"
          >
            <el-option
              v-for="item in companyFactoryList"
              :key="item.companyCode"
              :label="item.companyCode+' '+item.companyName"
              :value="item.companyCode">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'&&item.prop==='purchaseGroup'"
            v-model="purchaseData[item.prop]"
            :disabled="isEditDelayedPaymentPo"
            filterable
            @change="val => handleBaseItemSelectChange(val, item.prop)"
            style="width:100%"
          >
            <el-option
              v-for="item in purchaseList"
              :key="item.groupCode"
              :label="item.groupCode+' '+item.userName"
              :value="item.groupCode">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'&&item.prop==='supplierDict'"
            v-model="purchaseData[item.prop]"
            :disabled="Boolean(item.disabled)"
            style="width:100%"
            clearable
            filterable
            @change="val => handleBaseItemSelectChange(val, item.prop)"
          >
            <el-option
              v-for="item in transferSupplierList"
              :key="item.value"
              :label="item.value+' '+item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <el-select
            v-else-if="item.type==='select'"
            v-model="purchaseData[item.prop]"
            :disabled="Boolean(item.disabled) || disableByDeliveryStatus(item.prop) || isEditDelayedPaymentPo"
            style="width:100%"
            clearable
            filterable
            @change="val => handleBaseItemSelectChange(val, item.prop)"
          >
            <el-option
              v-for="opt in dictList[item.enums]"
              :key="opt.value"
              :label="['receiptReturnWay', 'drawbackWay', 'returnWay'].includes(item.prop) ? opt.name : opt.value+' '+opt.name"
              :disabled="disabledCode(item.prop, opt.value)"
              :value="['receiptReturnWay', 'drawbackWay', 'returnWay'].includes(item.prop) ? Number(opt.value) : opt.value">
            </el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8" v-if="['Z007', 'Z008'].includes(purchaseData.orderType)">
        <el-form-item label="OA流程编号">
          <el-input v-model="commonOaNumber" clearable placeholder="请输入OA流程编号" />
        </el-form-item>
      </el-col>
      <el-col :span="2" v-if="['Z007', 'Z008'].includes(purchaseData.orderType)">
        <el-tooltip content="注意：批量设置OA流程编号会把当前输入的OA流程编号设置到所有行中" placement="top">
          <el-button type="primary" @click="handleOaNumber">批量设置OA流程编号</el-button>
        </el-tooltip>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { mapKeyToValue, labelColon, formatString } from '@/utils/mm'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { safeRun } from '@/utils/index'
import { getSupplierInfo } from '@/api/mm'
export default {
  name: 'EditBaseInfo',
  props: ['purchaseData', 'factoryCode', 'finalFields', 'getFirstLineUpdateDefaultAddress', 'isFesto', 'customerNameOptions', 'isEditDelayedPaymentPo'],
  components: {
    SelectSupplier
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole,
      dictList: state => state.orderPurchase.dictList,
      purchaseList: state => state.orderPurchase.purchaseList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList
    }),
    transferSupplierList () {
      const companyCode = this.purchaseData.companyCode
      if (companyCode && this.dictList) {
        const ret = (this.dictList['transferSupplier'] || [])
          .filter(item => item.value.substring(1, 3) === companyCode.substring(0, 2))
        return ret
      }
      return []
    },
    getColumns () {
      let ret = []
      const { orderType } = this.purchaseData
      safeRun(() => {
        ret = this.finalFields.filter(item => item.category === 'baseInfo' && (/edit/.test(item.status) || !item.status))
        ret.forEach(item => {
          if (item.prop === 'supplierDict' && orderType === 'Z003') {
            item.disabled = 1
          }
        })
      })
      return ret
    }
  },
  data () {
    return {
      commonOaNumber: ''
    }
  },
  methods: {
    formatString,
    mapKeyToValue,
    labelColon,
    /**
     * 批量设置OA流程编号
     */
    handleOaNumber() {
      if (!this.commonOaNumber) {
        this.$message.error('请输入OA流程编号后再操作')
        return
      }
      try {
        const { itemList = [] } = this.purchaseData
        if (itemList.filter(item => !!item.skuNo || !!item.materialDescription).length === 0) {
          this.$message.error('请先添加商品后再操作')
          return
        }
        itemList.forEach(item => {
          item.oaNo = this.commonOaNumber
        })
        this.$message.success('批量设置OA流程编号成功')
      } catch (error) {
        this.$message.error('批量设置OA流程编号失败,请重试')
      }
    },
    disabledCode (prop, code) {
      const ableList = [ '1100', '1600', '1700', '1800', '1900', '2300' ]
      return prop === 'companyCode' && !ableList.includes(code)
    },
    isIndeterminate (prop) {
      // 交期确认中间状态
      if (prop === 'isDeliveryTimeConfirm') {
        let ret = false
        const itemList = this.purchaseData.itemList.filter(item => !item.isEmptyLine && !item.isDeleted)
        if (!itemList.every(item => item.isConfirmed) &&
          !itemList.every(item => !item.isConfirmed)
        ) {
          ret = true
        }
        return ret
      }
      // 交期确认中间状态
      if (prop === 'isUrgent') {
        let ret = false
        const itemList = this.purchaseData.itemList.filter(item => !item.isEmptyLine && !item.isDeleted)
        if (!itemList.every(item => item.isUrgent) &&
          !itemList.every(item => !item.isUrgent)
        ) {
          ret = true
        }
        return ret
      }
      return false
    },
    disableByDeliveryStatus (prop) {
      const { deliveryStatus, orderType } = this.purchaseData
      if (prop === 'paymentTermCode' && orderType === 'Z003') {
         return true
      } else if (prop === 'paymentTermCode') {
        return false
      }
      const ableList = [
        'pickupType',
        'purchaseGroup',
        'isOverchargeFree',
        'isUrgent',
        'isDeliveryTimeConfirm',
        'returnWay',
        'drawbackWay',
        'receiptReturnWay',
        'strategicStock'
      ]
      if (ableList.includes(prop) && deliveryStatus !== 1) {
        return false
      }
      return true
    },
    disableByIsZkhPickUp (prop) {
      if (prop === 'isZkhPickUp' && this.userRole.find(item => ['PMS采购经理', 'PMS采购总监', 'PMS管理员'].indexOf(item) > -1) && this.purchaseData.isDirect === 1) {
        return false
      }
      return true
    },
    handleSupplierChange (val) {
      const { itemList = [] } = this.purchaseData
      // this.resetCommunication()
      this.$emit('resetCommunication')
      if (itemList.length > 1) {
        this.$confirm('此操作将删除所选的商品, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.purchaseData.itemList = []
          this.purchaseData.paymentTermCode = ''
          this.purchaseData.currency = ''
          // this.resetCharge()
          this.$emit('resetCharge')
          // this.addItemEmptyLine()
          this.$emit('addItemEmptyLine')
          // this.setSupplier(val)
          this.$emit('setSupplier', val)
          // this.$refs['orderForm'].clearValidate()
          this.$emit('clearValidate')
          this.previousSupplier = val
        }).catch(() => {
          this.purchaseData.supplier = this.previousSupplier
        }).finally(() => {
          setTimeout(() => {
            safeRun(() => {
              this.$refs.supplier[0].$refs.supplier.blur()
            })
          }, 80)
        })
      } else {
        // this.setSupplier(val)
        this.$emit('setSupplier', val)
        setTimeout(() => {
          // this.$refs['orderForm'].clearValidate()
          this.$emit('clearValidate')
        }, 80)
      }
    },
    handleNumberChange (lossRatio, type) {
      // 组件数量以实际比例为主，不和损耗比关联
      // if (type === 'lossRatio') {
      //   if (!lossRatio) lossRatio = 0
      //   let items = []
      //   safeRun(() => {
      //     items = this.purchaseData.itemList.filter(item => Array.isArray(item.componentList) && item.componentList.length > 0)
      //     items.forEach(item => {
      //       let { itemQuantity } = item
      //       item.componentList.forEach(comp => {
      //         const { materialGroupNum } = comp
      //         if (!itemQuantity) itemQuantity = 0
      //         if (!comp.bomQuantity) comp.bomQuantity = 1
      //         if (!comp._quantity) comp._quantity = 1
      //         let { inventoryUnitMole = 1, inventoryUnitDeno = 1 } = item
      //         if (!inventoryUnitMole) inventoryUnitMole = 1
      //         if (!inventoryUnitDeno) inventoryUnitDeno = 1
      //         let number = Number((comp.numCalScale * itemQuantity).toFixed(3))
      //         console.log(number)
      //         if (materialGroupNum === 430) {
      //           number = Math.ceil(number)
      //         }
      //         comp.componentRequiredQuantity = number
      //       })
      //     })
      //   })
      // }
    },
    handleCheckboxChange (val, prop) {
      let { itemList } = this.purchaseData
      if (prop === 'isOverchargeFree') {
        // 勾选免费超收订单:
        // row.taxedPrice = 0
        // row.untaxedPrice = 0
        // row.taxedTotalAmount = 0
        // row.untaxedTotalAmount = 0
        // row.taxTotalAmount = 0
        const inlineList = [ 'tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment' ]
        const titleList = ['shareShipping', 'shareDiscount', ...inlineList]
        safeRun(() => {
          titleList.forEach(charge => {
            this.purchaseData[`${charge}`] = undefined
            this.purchaseData[`${charge}Amount`] = null
            this.purchaseData[`${charge}Currency`] = null
            this.purchaseData[`${charge}CurrencyInput`] = undefined
          })
          itemList.filter(item => !item.isEmptyLine).forEach(item => {
            // 不修改已删除的行
            if (item.isDeleted === 1) return
            // item.isFree = 1
            // item.taxedPrice = 0
            // item.untaxedPrice = ''
            // item.taxedTotalAmount = ''
            // item.untaxedTotalAmount = ''
            // item.taxTotalAmount = ''
            // eslint-disable-next-line eqeqeq
            if (val == 1) {
              titleList.forEach(charge => {
                item[`${charge}Amount`] = undefined
                item[`${charge}Currency`] = null
              })
            }
          })
        })
        safeRun(() => {
          this.purchaseData.shippingAmount = undefined
          this.purchaseData.discountAmount = undefined
        })
      }
      if (prop === 'isDeliveryTimeConfirm') {
        safeRun(() => {
          itemList.filter(item => !item.isEmptyLine).forEach(item => {
            // 不修改已删除的行
            if (item.isDeleted === 1) return
            // eslint-disable-next-line eqeqeq
            if (val == 1) {
              item.isConfirmed = 1
            } else {
              item.isConfirmed = 0
            }
          })
        })
      }
      if (prop === 'isUrgent') {
        safeRun(() => {
          itemList.filter(item => !item.isEmptyLine).forEach(item => {
            // 不修改已删除的行
            if (item.isDeleted === 1) return
            // eslint-disable-next-line eqeqeq
            if (val == 1) {
              item.isUrgent = 1
            } else {
              item.isUrgent = 0
            }
          })
        })
      }
      if (prop === 'ownerTransfer') {
        if (!this.purchaseData.taglibIdList) {
          this.purchaseData.taglibIdList = []
        }
        // if (!this.purchaseData.delTaglibIdList) {
        //   this.purchaseData.delTaglibIdList = []
        // }
        if (val === 1) {
          this.purchaseData.taglibIdList.push(102)
        } else {
          const index = this.purchaseData.taglibIdList.indexOf(102)
          this.purchaseData.taglibIdList.splice(index, 1)
          // this.purchaseData.delTaglibIdList.push(102)
        }
        this.purchaseData.ownerTransfer = val
        this.$emit('setTitleCheckbox', 'ownerTransfer')
      }
      if (prop === 'strategicStock') {
        if (!this.purchaseData.taglibIdList) {
          this.purchaseData.taglibIdList = []
        }
        if (!this.purchaseData.delTaglibIdList) {
          this.purchaseData.delTaglibIdList = []
        }
        if (val === 1) {
          this.purchaseData.taglibIdList.push(101)
        } else {
          const index = this.purchaseData.taglibIdList.indexOf(101)
          this.purchaseData.taglibIdList.splice(index, 1)
          this.purchaseData.delTaglibIdList.push(101)
        }
        this.purchaseData.strategicStock = val
        this.$emit('setTitleCheckbox', 'strategicStock')
      }
      console.log('this.purchaseData.strategicStock--', this.purchaseData.strategicStock)
      this.purchaseData.itemList = itemList
    },
    handleChangeCompany (val) {
      // this.resetCommunication()
      this.$emit('resetCommunication')
      if (this.purchaseData.supplier || this.purchaseData.itemList.length > 1) {
        this.$confirm('此操作将删除所选的数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.purchaseData.itemList = []
          // this.addItemEmptyLine()
          this.$emit('addItemEmptyLine')
          // this.resetCharge()
          this.$emit('resetCharge')
          // this.resetSupplier(val)
          this.$emit('resetSupplier', val)
          this.setDefaultCompanyCode(this.companyFactoryList, val)
          this.previousCompany = val
        }).catch(() => {
          this.purchaseData.companyCode = this.previousCompany || '1000'
        }).finally(() => {
          setTimeout(() => {
            // this.$refs['orderForm'].clearValidate()
            this.$emit('clearValidate')
            safeRun(() => {
              this.$refs.company[0].blur()
            })
          }, 80)
        })
      } else {
        this.previousCompany = val
        this.setDefaultCompanyCode(this.companyFactoryList, val)
        // this.resetSupplier(val)
        this.$emit('resetSupplier', val)
      }
    },
    startLoading () {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      return loading
    },
    endLoading (loading) {
      if (loading) {
        loading.close()
      }
    },
    setSupplier (val) {
      const { supplierNo } = val
      const { companyCode } = this.purchaseData
      this.previousSupplier = val
      if (supplierNo) {
        const loading = this.startLoading()
        getSupplierInfo({
          supplierNo,
          factoryCode: companyCode
        }).then(data => {
          this.endLoading(loading)
          if (!data) return
          const { sapCurrency, paymentTermCode } = data
          if (paymentTermCode) {
            if (this.purchaseData.orderType !== 'Z003') {
              this.purchaseData.paymentTermCode = paymentTermCode
            }
          }
          if (sapCurrency) {
            if (this.purchaseData.orderType !== 'Z003') {
              this.purchaseData.currency = sapCurrency
            }
          }
        })
        this.queryAddressList(supplierNo, this.factoryCode)
        this.$emit('queryAddressList', supplierNo, this.factoryCode)
      } else {
        this.purchaseData.supplier = ''
        this.supplierAddressList = []
        this.supplierContactList = []
        this.purchaseData.supplierContactName = ''
        this.purchaseData.supplierContactPhone = ''
        if (this.purchaseData.orderType !== 'Z003') {
          this.purchaseData.paymentTermCode = ''
          this.purchaseData.currency = ''
        }
      }
    },
    setDefaultCompanyCode (data, companyCode) {
      if (data && data.length > 0) {
        if (companyCode) {
          const currentCompany = data.find(item => item.companyCode === companyCode)
          if (currentCompany) {
            const factoryList = currentCompany.factoryList
            if (factoryList && factoryList.length > 0) {
              const factoryCode = factoryList[0].factoryCode
              if (factoryCode) {
                this.purchaseData.itemList[0].factoryCode = factoryCode
              }
            }
          }
        }
      }
    },
    handleBaseItemSelectChange (val, prop) {
      if (prop === 'purchaseGroup' && this.factoryCode) {
        // 更新错误以及切换提示
        this.$forceUpdate()
        const { supplier = {}, itemList } = this.purchaseData
        if (supplier.supplierNo) {
          const idx = this.getFirstLineUpdateDefaultAddress()
          let soNo = ''
          let warehouseLocation = ''
          let skuNo = ''
          if (idx !== false) {
            soNo = itemList[idx].soNo
            warehouseLocation = itemList[idx].warehouseLocation
            skuNo = itemList[idx].skuNo
          }
          this.$emit('updateKeepSupplier', true)
          this.$emit('queryDefaultAddress', supplier.supplierNo, val, this.factoryCode, warehouseLocation, soNo, skuNo)
        } else {
          this.$message.error('请先选择供应商！')
        }
      }
      if (prop === 'supplierDict') {
        const currentSupplier = (this.dictList['transferSupplier'] || [])
          .find(item => item.value === val)
        this.purchaseData.supplier = {
          supplierNo: val,
          supplierId: val,
          providerId: val,
          supplierName: currentSupplier ? currentSupplier.name : ''
        }
      }
      if (prop === 'paymentTermCode') {
        console.log('change paymentTermCode...')
      }
      if (['receiptReturnWay', 'drawbackWay', 'returnWay'].includes(prop)) {
        this.$emit('setReturnKey', val, prop)
      }
    }
  },
  created () {}
}
</script>
<style lang="scss">
.edit-base-info{
  // .el-row {
    display: flex;
    flex-wrap: wrap;
  // }
}
</style>
