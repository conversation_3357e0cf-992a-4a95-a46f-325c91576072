<template>
  <el-dialog
    title="未采购说明原因选择"
    width="600px"
    :visible="visible"
    :before-close="beforeClose"
  >
    <el-form :model="submitForm" ref="submitForm" :rules="rules" class="form-inline" label-width="120px" style="padding-right: 10px;">
      <el-row>
        <el-col :span="12">
          <el-form-item label="汇总原因" prop="pendingReason">
            <el-select style="width:100%" @change="handleChange" v-model="submitForm.pendingReason" placeholder="汇总原因">
              <el-option
                v-for="(item, index) in reasonTypeOneList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="汇总原因明细" prop="detailPendingReason">
            <el-select style="width:100%" v-model="submitForm.detailPendingReason" placeholder="汇总原因明细">
              <el-option
                v-for="(item, index) in reasonTypeTwoList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="未下采购说明">
            <el-input
              type="textarea"
              :rows="3"
              v-model="submitForm.pendingReasonDesc"
              placeholder="请输入"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item style="text-align:right">
          <el-button type="default" @click="cancel">取消</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-form-item>
      </el-row>
    </el-form>
  </el-dialog>
</template>

<script>
import {
  getReasonTypeOneList,
  getReasonTypeTwoList
} from '@/filters/index.js'
export default {
  props: {
    visible: {
      required: true,
      type: Boolean,
      default: false
    },
    reasonOptions: {
      type: Array,
      default: () => []
    },
    updateVisible: {
      type: Function,
      default: () => {}
    },
    dataResult: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    reasonTypeOneList () {
      return getReasonTypeOneList(this.reasonOptions)
    },
    reasonTypeTwoList () {
      return getReasonTypeTwoList(this.reasonOptions, this.submitForm.pendingReason)
    }
  },
  data() {
    return {
      rules: {
        pendingReason: [
          { required: true, message: '请选择汇总原因', trigger: ['blur', 'change'] }
        ],
        detailPendingReason: [
          { required: true, message: '请选择汇总原因明细', trigger: ['blur', 'change'] }
        ]
      },
      submitForm: {}
    };
  },
  methods: {
    handleChange (val) {
      if (this.submitForm.detailPendingReason) {
        this.submitForm.detailPendingReason = ''
      }
    },
    submit() {
      this.$refs.submitForm.validate(valid => {
        if (valid) {
          console.log(this.submitForm)
          this.$emit('submitReason', this.submitForm)
          this.beforeClose()
        }
      });
    },
    cancel() {
      this.beforeClose()
    },
    beforeClose(done) {
      this.$refs.submitForm.clearValidate()
      this.submitForm = {}
      done && done();
      this.updateVisible('showAbnormalReason', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.desc {
  margin: 10px 0;
  .strong {
    font-weight: bold;
  }
}
.batch-btn {
  width: 120px;
}
</style>
