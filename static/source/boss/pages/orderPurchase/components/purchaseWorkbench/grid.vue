<template>
  <div class="purchase-workbench-grid-table">
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      highlight-hover-row
      show-header-overflow
      size="small"
      align="center"
      ref="purchaseWorkbenchGrid"
      id="purchase-workbench-grid"
      :scroll-x="{gt: -1}"
      :scroll-y="{gt: -1}"
      :height="gridHeight"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="dataList"
      :columns="getColumns"
      :toolbar-config="tableToolbar"
      @sort-change="handleSortChange"
      @checkbox-change="selectionChange"
      @checkbox-all="selectionChange"
      @cell-dblclick="cellClick"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, enabled: true}">
      <template v-slot:toolbar_buttons>
        <div class="title-group">
          <slot name="button-group"></slot>
        </div>
      </template>
      <template #openPoStatusText="{ column }">
        <el-tooltip class="item" effect="dark" content="供应商在VC是否开通了PO订单功能" placement="top">
          <span>
          {{column.title}}<i class="el-icon-question"></i>
          </span>
        </el-tooltip>
      </template>
      <template #operationReminderText="{ row }">
        <span style="color: red">{{row.operationReminderText}}</span>
      </template>
      <template #poNo="{ row }">
        <span class="link-to-detail" @click="toDetail(row.poNo)" type="primary">{{row.poNo}}</span>
      </template>
      <template #soNo="{ row }">
        <span class="link-to-detail" @click="toSoDetail(row)" type="primary">{{row.soNo}}</span>
      </template>
      <template #shipWarehouseLocation="{ row }">
        {{(row.shipWarehouseLocation || '') + (row.shipWarehouseLocationText || '')}}
      </template>
      <template #warehouseLocation="{ row }">
        {{(row.warehouseLocation || '') + (row.warehouseLocationText || '')}}
      </template>
      <template #planDeliveryDate="{ row }">
        <el-date-picker
          v-model="row.planDeliveryDate"
          @change="val => handleChange(val, 'planDeliveryDate', row)"
          :disabled="disabledByStatus(row)"
          :picker-options="dateOptions"
          size="mini"
          value-format="yyyy/MM/dd"
          style="width: 90%"
          type="date"
          placeholder="选择日期">
        </el-date-picker>
      </template>
      <template #tag="{ row }">
        <el-select v-model="row.tag" clearable :disabled="disabledByStatus(row)" @change="val => handleChange(val, 'tag', row)" placeholder="请选择">
          <el-option
            v-for="item in buildOptions('poFollowReportTag')"
            :disabled="item.value !== '8' && item.value !== '10'"
            :key="item.value"
            :label="item.name"
            :value="item.value">
          </el-option>
        </el-select>
      </template>
      <template #pendingReasonDesc="{ row }">
        <span>
          {{reasonType(row)}}
          {{reasonTypeDetail(row)}}
          {{row.pendingReasonDesc}}
        </span>
      </template>
      <template #unPurchaseQuantity="{ row }">
        <span>
          {{(parseFloat(row.quantity) - parseFloat(row.clearedQuantity)).toFixed(2)}}
        </span>
      </template>
      <template #isConfirmed="{ row }">
        <el-checkbox :disabled="disabledByStatus(row)" @change="val => handleChange(val, 'isConfirmed', row)" :true-label="1" :false-label="0" v-model="row.isConfirmed"></el-checkbox>
      </template>
      <template #isCustomerAccept="{ row }">
        <el-checkbox :disabled="disabledByStatus(row)" @change="val => handleChange(val, 'isCustomerAccept', row)" :true-label="1" :false-label="0" v-model="row.isCustomerAccept"></el-checkbox>
      </template>
      <template #remark="{ row }">
        <el-input
          placeholder="请输入内容"
          @change="val => handleChange(val, 'remark', row)"
          :disabled="disabledByStatus(row)"
          maxlength="3000"
          v-model="row.remark">
        </el-input>
      </template>
      <template #purchaseGroup="{ row }"> {{mapValue('purchaseGroup', row.purchaseGroup, true)}} </template>
      <template #materialGroupName="{ row }"> {{mapValue('materialGroupName', row)}} </template>
      <template v-slot:deliveryCode="{ row }">
        <el-popover
          placement="top"
          trigger="hover"
          popper-class="align">
          <span v-if="row.deliveryCode && row.deliveryCode.length > 0">
            <div v-for="(item, index) in row.deliveryCode" :key="item" class="link-to-detail">
              <el-button class="link-to-detail" @click="toVcDetail(item)" type="text">{{`${row.deliveryCode.length > 1 ? 'No'+ (index + 1) + ':' : ''}`}}{{item}}</el-button>
            </div>
        </span>
          <span class="link-to-detail"  slot="reference" style="overflow: hidden;width: 180px; display: inline-block;text-align: left; vertical-align: middle;">
            {{mapDeliveryList(row.deliveryCode)}}
          </span>
        </el-popover>
      </template>
       <template #deliveryDateList="{ row }">
        <span v-html="mapDeliveryList(row.deliveryDateList)"></span>
      </template>
      <template #logisticsNameList="{ row }">
        <span v-html="mapDeliveryList(row.logisticsNameList)"></span>
      </template>
      <template #logisticsCodeList="{ row }">
        <span v-html="mapDeliveryList(row.logisticsCodeList)"></span>
      </template>
      <template #deliveryVehicleNumberList="{ row }">
        <span v-html="mapDeliveryList(row.deliveryVehicleNumberList)"></span>
      </template>
      <template #driverNameList="{ row }">
        <span v-html="mapDeliveryList(row.driverNameList)"></span>
      </template>
      <template #driverPhoneList="{ row }">
        <span v-html="mapDeliveryList(row.driverPhoneList)"></span>
      </template>
      <template #isDeliveryOrderSignedList="{ row }">
        <span v-html="mapDeliveryList(row.isDeliveryOrderSignedList, 'isDeliveryOrderSigned')"></span>
      </template>
      <template #isSigningBackAccepted="{ row }">
        <el-checkbox disabled @change="val => handleChange(val, 'isSigningBackAccepted', row)" :true-label="1" false-label="0" v-model="row.isSigningBackAccepted"></el-checkbox>
      </template>
      <template #isCustomerReceived="{ row }">
        <el-checkbox disabled @change="val => handleChange(val, 'isCustomerReceived', row)" :true-label="1" false-label="0" v-model="row.isCustomerReceived"></el-checkbox>
      </template>
      <template #signingBack="{ row }"> {{mapValue('signingBack', row.soSigningBack)}} </template>
      <template #deliveryOrderQtyList="{ row }">
        <span v-html="mapDeliveryList(row.deliveryOrderQtyList)"></span>
      </template>
      <template #deliveryOrderStatusList="{ row }">
        <span v-html="mapDeliveryList(row.deliveryOrderStatusList, 'deliveryOrderStatus')"></span>
      </template>
      <template #deliveryWayList="{ row }">
        <span v-html="mapDeliveryList(row.deliveryWayList, 'deliveryWay')"></span>
      </template>
      <template #guaranteedPoNo="{ row }">
        <span  class="link-to-detail" @click="toDetail(row.guaranteedPoNo)" type="primary"> {{row.guaranteedPoNo}}</span>
      </template>
      <template #inputTax="{ row }"> {{mapValue('inputTax', row.inputTax, true)}} </template>
      <template #WONoList="{ row }">
        <p
          v-for="(order, index) in row.workOrderNoList || []"
          :key="index"
          type="primary"
          class="link-to-detail"
          @click="toWorkflowDetail(order)"
          >
          {{order}}
        </p>
      </template>
      <template #operation="{ row }">
        <el-button @click="createWorkOrder(row)" type="text" >发工单</el-button>
        <el-button  @click="handleChangeChannel(row)" type="text" v-if="row.soShipStatus !== 1 && row.orderType === 'Z001' && row.isDeleted !== 1 && oneProductAuth" >换渠道</el-button>
      </template>
      <template #workOrderNoList="{ row }">
        <el-tooltip effect="dark" content="请先查明未下单原因并维护到未采购说明，再发对应工单" placement="top-start"  v-if="activeName === '1' && !row.pendingReason && !row.detailPendingReason">
          <el-button type="text" style="color: #C0C4CC !important;">创建工单</el-button>
        </el-tooltip>
        <el-button v-else-if="isShowCreateWorkOrder(row)" @click="createWorkOrder(row)" type="text" >创建工单</el-button><br/>
        <p
          v-for="(order, index) in row.workOrderNoList || []"
          :key="index"
          type="primary"
          class="link-to-detail"
          @click="toWorkflowDetail(order)"
          >
          {{order}}
        </p>
      </template>
    </vxe-grid>
     <ChangeChannelDialog
      :show-dialog.sync="showChangeChannelDialog"
      :changeChannelData="changeChannelData"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import { columnsMap } from './grid.js'
import ChangeChannelDialog from '@/pages/orderPurchase/components/common/ChangeChannelDialog'
import { buildOptions } from '@/utils/mm'
import { routeToWorkflow, safeRun, throttle } from '@/utils/index'
import { handleDisplaceSku } from '@/pages/orderPurchase/utils/index'
import { getButtonAuth } from '@/utils/auth'
import clip from '@/utils/clipboard'
import {
  getReasonTypeOneList,
  getReasonTypeTwoList
} from '@/filters/index.js'
import moment from 'moment'

export default {
  name: 'purchaseWorkbenchGird',
  components: { ChangeChannelDialog },
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    rawDataList: {
      type: Array,
      default: () => []
    },
    reasonOptions: {
      type: Array,
      default: () => []
    },
    activeName: String,
    orderType: [String, Number],
    tableLoading: Boolean,
    isAbnormalTab: Boolean,
    oneProductAuth: Boolean
  },
  data () {
    const newCreateTime = moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD')
    return {
      showChangeChannelDialog: false,
      changeChannelData: {},
      gridHeight: 450,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      dateOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(newCreateTime)).getTime()
        }
      }
    }
  },
  computed: {
    getColumns () {
      const columns = columnsMap[this.activeName]
      columns.forEach(column => {
        if (column.enums) {
          column.slots = { default: column.enums }
        }
        if (column.isBoolean || column.slot) {
          column.slots = { default: column.field }
        }
        if (column.field === 'materialGroupName') {
          column.slots = { default: column.field }
        }
        if (column.field === 'lightStatus') {
          column.slots = { default: column.field }
        }
        if (column.field === 'openPoStatusText') {
          column.slots = { header: column.field }
        }
      })
      return columns
    }
  },
  mounted() {
    setTimeout(this.calcLeftHeight, 100)
    window.addEventListener('resize', this.calcLeftHeight, false)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.calcLeftHeight, false)
  },
  methods: {
    buildOptions,
    getButtonAuth,
    refresh() {
      this.$emit('refresh')
    },
    async handleChangeChannel (row) {
      this.$emit('changeTableLoading', true)
      const res = await handleDisplaceSku(row)
      this.$emit('changeTableLoading', false)
      if (res) {
        this.showChangeChannelDialog = true
        this.changeChannelData = res
        this.changeChannelData.poNo = row.poNo
        this.changeChannelData.poItemNo = row.itemNo
        this.changeChannelData.supplierNo = row.supplierNo
        this.changeChannelData.factoryCode = row.factoryCode
      }
    },
    isShowCreateWorkOrder(row) {
      if (this.activeName === '1' && !row.pendingReason && !row.detailPendingReason) {
        return false
      }
      if ((row.workOrderNoList && row.workOrderNoList.length < 10) || !row.workOrderNoList) {
        return true
      }
      return false
    },
    reasonType (row) {
      let list = getReasonTypeOneList(this.reasonOptions) || []
      const find = list.find(item => Number(item.value) === Number(row.pendingReason))
      return (find && find.label) || ''
    },
    reasonTypeDetail (row) {
      let list = getReasonTypeTwoList(this.reasonOptions, row.pendingReason) || []
      const find = list.find(item => Number(item.value) === Number(row.detailPendingReason))
      return (find && find.label) || ''
    },
    cellClick (config) {
      try {
        const { cell } = config
        let content = cell.innerText
        clip(content, event, () => {
          this.$message({
            message: '复制成功',
            type: 'success'
          })
        })
      } catch (err) {
        console.log(err)
      }
    },
    calcLeftHeight: throttle(function calcLeftHeight () {
      safeRun(() => {
        console.log('calcLeftHeight....')
        const form = document.querySelector('.purchase-workbench .search-filter')
        const table = document.querySelector('.purchase-workbench-grid-table')
        const innerHeight = window.innerHeight - 130 - 60
        const existHeight = form.offsetHeight + table.offsetHeight
        const paddingHeight = innerHeight - existHeight
        const floorHeight = Math.floor(paddingHeight * 400 / 452)
        if (floorHeight > 5 || floorHeight < -5) {
          this.gridHeight += floorHeight
          if (this.gridHeight <= 400) {
            this.gridHeight = 400
          }
        }
      })
    }, 600),
    createWorkOrder (row) {
      let query = {}
      if (this.isAbnormalTab) {
        query = {
          essence: 1,
          soNo: row.soNo,
          soItemNo: row.soItemNo,
          fromPage: 'omsWorkbench'
        }
      } else {
        query = {
          essence: 1,
          poNo: row.poNo,
          poType: this.orderType,
          soNo: row.soNo,
          customerService: row.customerService,
          fromPage: 'purchaseWorkbench',
          type: this.activeName,
          skuNo: row.skuNo
        }
      }
      routeToWorkflow('/wf/create/0', query)
    },
    showCreateWorkOrder (row) {
      return !row.workOrderStatus || row.workOrderStatus === 6
    },
    selectionChange ({ records }) {
      this.selections = records
      this.$emit('selection-change', records)
    },
    handleSortChange ({ column, property, order, sortBy, sortList, $event }) {
      if (['soNo', 'brandName', 'productServiceName', 'productSaleName'].includes(property)) {
        if (order) {
          this.$refs.purchaseWorkbenchGrid.sort(property, order)
        }
      } else {
        this.$emit('sortTable', order, property)
      }
    },
    disabledByStatus (row) {
      const roleDisabled = !this.getButtonAuth('跟单报表', '保存修改')
      // eslint-disable-next-line eqeqeq
      const statusDisabled = row.isPlanDeliveryDone == 1 || Boolean(row.isDeleted)
      return statusDisabled || roleDisabled
    },
    disabledIsCustomerReceived (row) {
      if (row.isDirect !== 1) {
        return true
      }
      return false
    },
    disabledIsSigningBackAccepted (row) {
      if (row.isPlanDeliveryDone === 1) {
        return true
      }
      return false
    },
    toWorkflowDetail (workOrderNo) {
      routeToWorkflow(`/wf/detail/${workOrderNo}`)
    },
    toDetail (no) {
      this.$router.push({
        path: `/orderPurchase/detail/${no}`,
        query: { tagName: `${no}详情` }
      })
    },
    toSoDetail (row) {
      const { soNo, id } = row
      this.$router.jumpToSoOrderDetail({
        query: {
          soNo: soNo,
          sapOrderNo: soNo,
          id: id,
          refresh: true,
          tagName: `${row.soNo}详情`
        }
      })
    },
    toVcDetail (deliveryCode) {
      const url = window.CUR_DATA.env === 'pro' ? 'https://biw.zkh360.com/deliveryDetail/' : 'https://vcadmin-uat.zkh360.com/deliveryDetail/'
      const deliveryUrl = `${url}${deliveryCode}`
      window.open(deliveryUrl, '_blank')
    },
    isOrigin (row, prop, value) {
      const rawItem = this.rawDataList.find(item => item.shortid === row.shortid)

      return rawItem[prop] === value
    },
    mapDeliveryList (list, enums) {
      let ret = ''
      if (Array.isArray(list)) {
        list = list.map((item, index) => {
          if (enums) {
            let opts = buildOptions(enums) || []
            if (enums === 'isDeliveryOrderSigned') {
              opts = [
                { value: 1, name: '是' },
                { value: 0, name: '否' }
              ]
            }
            // eslint-disable-next-line eqeqeq
            const findItem = opts.find(_item => _item.value == item)
            if (findItem) {
              item = findItem.name
            }
          }
          return list.length > 1 ? `No${index + 1}:${item}` : item
        })
        ret = list.join('\n')
      }
      return ret
    },
    handleChange (value, prop, row) {
      if (this.isOrigin(row, prop, value)) {
        row.changed = false
      } else {
        row.changed = true
      }
    },
    emptyValue (value) {
      return value ? value + '' : ''
    },
    mapValue (prop, value, withCode) {
      if (prop === 'materialGroupName') {
        return this.emptyValue(value.materialGroupNum) + ' ' + this.emptyValue(value.materialGroupName)
      }
      if (prop === 'shipWarehouseLocationText') {
        return this.emptyValue(value.shipWarehouseLocation) + ' ' + this.emptyValue(value.shipWarehouseLocationText)
      }
      if (prop === 'warehouseLocationText') {
        return this.emptyValue(value.warehouseLocation) + ' ' + this.emptyValue(value.warehouseLocationText)
      }
      const options = buildOptions(prop)
      if (Array.isArray(options)) {
        // eslint-disable-next-line eqeqeq
        const item = buildOptions(prop).find(item => item.value == value)
        if (item) {
          return withCode ? item.value + ' ' + this.emptyValue(item.name) : item.name
        }
      }
    },
    mapBoolean (value) {
      return value ? '是' : '否'
    },
    validateDeliveryDate (data) {
      let ret = true
      let dateMsg = ''
      let textMsg = ''
      data.forEach((item, index) => {
        if (!item.planDeliveryDate) {
          ret = false; dateMsg += index + 1 + ' '
        }
      })
      data.forEach((item, index) => {
        if (item.remark && item.remark.length > 200) {
          ret = false; textMsg += index + 1 + ' '
        }
      })
      if (dateMsg) {
        dateMsg = `第${dateMsg}行计划行交货日期不能为空！`
      }
      if (textMsg) {
        textMsg = `第${textMsg}行备注文本不能大于200个字符！`
      }
      let err = dateMsg ? dateMsg + '<br />' + textMsg : textMsg
      this.$alert(err, '操作提示', {
        type: 'error',
        dangerouslyUseHTMLString: true
      })
      return ret
    },
    batchUpdate () {
      this.$emit('showEditDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
.purchase-workbench-grid-table {
  overflow: hidden;
  .title-group{
    display: flex;
    justify-content: space-between;
    span{
      line-height: 32px;
      height: 33px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .button-group {
      min-width: 300px;
      display: inline-block;
      text-align: right;
    }
  }
  .link-to-detail{
    color: #597bee;
    cursor: pointer;
  }
}
</style>
