const allColumns = [
  { prop: 'accumulateRemark', comment: '累计跟单文本' },
  { prop: 'approveStep', comment: '审批状态' },
  { prop: 'brandName', comment: '品牌名称' },
  { prop: 'coopMode', comment: '合作模式' },
  { prop: 'costCenter', comment: '成本中心' },
  { prop: 'costCenterText', comment: '成本中心描述' },
  { prop: 'createDate', comment: '订单创建日期' },
  { prop: 'createUser', comment: '创建用户' },
  { prop: 'customerService', comment: '客服' },
  { prop: 'deliveryDateList', comment: '发货日期' },
  { prop: 'deliveryOrderQtyListoptional', comment: '送货单已发数量' },
  { prop: 'deliveryOrderStatusList', comment: '发货状态' },
  { prop: 'deliveryVehicleNumberListoptional', comment: '送货车牌号' },
  { prop: 'deliveryWayList', comment: '发货方式' },
  { prop: 'demanderCustomerService', comment: '销售订单（客服）' },
  { prop: 'diffLeadDays', comment: '交期天数差异' },
  { prop: 'discountAmount', comment: '商品行折扣' },
  { prop: 'dnNo', comment: '外向交货单号' },
  { prop: 'driverNameListoptional', comment: '司机姓名' },
  { prop: 'driverPhoneListoptional', comment: '司机联系方式' },
  { prop: 'expectedLeadDays', comment: '预计到货天数' },
  { prop: 'factoryCode', comment: '工厂' },
  { prop: 'firstDeliveryDate', comment: '首次承诺日期' },
  { prop: 'followSignBack', comment: '跟进回传' },
  { prop: 'generalLedgerAccount', comment: '总账科目' },
  { prop: 'ifSignFrameworkAgreement', comment: '是否签订框架协议' },
  { prop: 'inputTax', comment: '税率' },
  { prop: 'invoiceAmount', comment: '已开票金额' },
  { prop: 'invoiceQuantity', comment: '已开票数量' },
  { prop: 'isConfirmed', comment: '交期确认标识' },
  { prop: 'isConfirmedText', comment: '交期确认文本' },
  { prop: 'isCustomerAccept', comment: '是否客户接收交期' },
  { prop: 'isCustomerReceived', comment: '是否客户已收货' },
  { prop: 'isDeleted', comment: '是否删除' },
  { prop: 'isDeliveryOrderSignedListoptional', comment: '是否已签收' },
  { prop: 'isDirect', comment: '是否直发单' },
  { prop: 'isEscrow', comment: '是否代管代发' },
  { prop: 'isPlanDeliveryDone', comment: '是否交货完成' },
  { prop: 'isSigningBackAccepted', comment: '是否接受签单返回' },
  { prop: 'isUrgent', comment: '是否急单' },
  { prop: 'itemNo', comment: '商品行号' },
  { prop: 'itemQty', comment: '商品行数量' },
  { prop: 'itemReceivedQty', comment: '商品行已收货数量' },
  { prop: 'itemUnreceivedAmount', comment: '商品行未收货金额' },
  { prop: 'itemUnreceivedQty', comment: '商品行未收货数量' },
  { prop: 'lastDeliveryDate', comment: '最新承诺日期' },
  { prop: 'leadProcessRate', comment: '交货进度，百分制' },
  { prop: 'leftLeadDays', comment: '剩余交货天数' },
  { prop: 'lightReason', comment: '红黄灯原因' },
  { prop: 'lightStatus', comment: '红绿灯状态' },
  { prop: 'logisticsCodeList', comment: '物流单号' },
  { prop: 'logisticsNameList', comment: '物流公司名称' },
  { prop: 'materialDescription', comment: '物料描述' },
  { prop: 'materialGroupName', comment: '物料组名称' },
  { prop: 'materialGroupNum', comment: '物料组编号' },
  { prop: 'notInvoiceAmount', comment: '未开票金额' },
  { prop: 'notInvoiceQuantity', comment: '未开票数量' },
  { prop: 'openPoStatusText', comment: '开通Po状态文本' },
  { prop: 'openVcStatus', comment: 'VC开通状态' },
  { prop: 'orderRemark', comment: '订单备注' },
  { prop: 'orderType', comment: '订单类型' },
  { prop: 'paymentTermCode', comment: '付款条件代码' },
  { prop: 'planDeliveryDate', comment: '计划行交货日期' },
  { prop: 'planDeliveryQty', comment: '计划行数量' },
  { prop: 'planNo', comment: '计划行号' },
  { prop: 'poNo', comment: '采购订单号' },
  { prop: 'printRemark', comment: '打印备注' },
  { prop: 'productManager', comment: '商品经理' },
  { prop: 'productPosition', comment: '产品定位' },
  { prop: 'promiseUpdateFrequency', comment: '承诺变更次数' },
  { prop: 'purchaseGroup', comment: '采购组' },
  { prop: 'requestDeliveryDate', comment: '请求交货日期' },
  { prop: 'returnReason', comment: '退货原因' },
  { prop: 'sapOrderNo', comment: 'SAP订单号' },
  { prop: 'shipWarehouseLocation', comment: '发货仓库地点' },
  { prop: 'shipWarehouseLocationText', comment: '发货仓库地点文本' },
  { prop: 'shippingAmount', comment: '商品行运费' },
  { prop: 'skuNo', comment: 'SKU编码' },
  { prop: 'soDeliveryDate', comment: 'SO需求交期' },
  { prop: 'soDeliveryManager', comment: '交付主管' },
  { prop: 'soItemNo', comment: '关联的销售单销售单行号' },
  { prop: 'soItemQty', comment: '销售订单数量' },
  { prop: 'soNo', comment: '关联销售单号' },
  { prop: 'soSellerName', comment: '销售姓名' },
  { prop: 'soShipStatus', comment: 'SO发货状态' },
  { prop: 'soSigningBack', comment: '签单返回' },
  { prop: 'source', comment: '订单来源' },
  { prop: 'specialSupplySo', comment: '专料专供销售单号' },
  { prop: 'specialSupplySoItem', comment: '专料专供销售单行号' },
  { prop: 'standardDeliveryDate', comment: '标准交货日期' },
  { prop: 'standardLeadDays', comment: '行标准交期（下单时）' },
  { prop: 'stoDnClearedQty', comment: '转储已发货数量' },
  { prop: 'stoDnOnWayQty', comment: '转储在途数量' },
  { prop: 'stoDnPostDate', comment: '外向交货单过账日期' },
  { prop: 'stoDnQty', comment: '转储已制单数量' },
  { prop: 'stoDnUnclearedQty', comment: '转储已制单未发货' },
  { prop: 'stoNotDnQty', comment: '转储未制单数量' },
  { prop: 'supplierClassify', comment: '供应商分类' },
  { prop: 'supplierMaterialNo', comment: '供应商物料号' },
  { prop: 'supplierName', comment: '供应商名称' },
  { prop: 'supplierNo', comment: '供应商代码' },
  { prop: 'supplierOrderNo', comment: '供应商订单号' },
  { prop: 'tag', comment: '标签' },
  { prop: 'taxTotalAmount', comment: '税额' },
  { prop: 'taxedPrice', comment: '含税单价' },
  { prop: 'taxedTotalAmount', comment: '行含税总额' },
  { prop: 'trackNo', comment: '跟踪单号' },
  { prop: 'unit', comment: '订单单位' },
  { prop: 'untaxedPrice', comment: '未税单价' },
  { prop: 'untaxedTotalAmount', comment: '行未税总额' },
  { prop: 'warehouseLocation', comment: '仓库地点' },
  { prop: 'warehouseLocationText', comment: '仓库地点文本' },
  { prop: 'warningType', comment: '预警状态' },
  { prop: 'workOrderNoList', type: 'html', showOverflow: false, comment: '工单号' }
]

const abnormalPurchase = [
  { title: '', type: 'checkbox', width: 50 },
  { title: '客户编号', field: 'customerNo', width: 100 },
  { title: '客户名称', field: 'customerName', width: 160 },
  { title: '客服', field: 'productServiceName', sortable: true, width: 100 },
  { title: '销售订单日期', field: 'soCreateDate', sortable: true, width: 140 },
  { title: '销售单号', field: 'soNo', sortable: true, width: 100 },
  { title: '销售订单行号', field: 'soItemNo', width: 100 },
  { title: '未采购说明', field: 'pendingReasonDesc', slot: true, width: 160 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'skuDesc', width: 200 },
  { title: '订单数量', field: 'quantity', width: 100 },
  { title: '未采数量', field: 'unPurchaseQuantity', slot: true, width: 100 },
  { title: '单位', field: 'quantityUnitName', width: 100 },
  { title: '仓位', field: 'position', width: 100 },
  { title: '请求发货日期', field: 'deliveryDate', sortable: true, width: 180 },
  { title: '首次应下采购日期', field: 'firstApprovalDate', sortable: true, width: 180 },
  { title: '应下采购单日期', field: 'approvalDate', width: 180 },
  { title: '品牌', field: 'brandName', sortable: true, width: 100 },
  { title: '采购员', field: 'purchaseGroup', sortable: true, enums: 'purchaseGroup', width: 100 },
  { title: '物料组', field: 'productGroupName', width: 100 },
  { title: '产品经理', field: 'productManagerName', width: 100 },
  { title: '销售', field: 'productSaleName', sortable: true, width: 100 },
  { title: 'EVM专员', field: 'evmOperatorName', width: 100 },
  { title: '工单', field: 'workOrderNoList', type: 'html', showOverflow: false, fixed: 'right', slot: true, width: 160 }
]

const notConfirm = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },
  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '交期确认标识', field: 'isConfirmed', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },
  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', width: 120, enums: 'inputTax' },
  { title: '工单', field: 'WONoList', type: 'html', showOverflow: false, slot: true, width: 160 },
  { title: '操作', field: 'operation', fixed: 'right', slot: true, width: 160 }
]
const firstNotSatisfied = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },
  { title: '客户接受交货日期', field: 'customerAcceptDate', width: 160 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '更新客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },
  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },
  { title: '工单', field: 'WONoList', type: 'html', showOverflow: false, slot: true, width: 160 },
  { title: '操作', field: 'operation', fixed: 'right', slot: true, width: 160 }
]
const dateNotSatisfied = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '客户接受交货日期', field: 'customerAcceptDate', width: 160 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '更新客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 160 },
  { title: '发货数量', field: 'deliveryOrderQtyList', slot: true, width: 160 },
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 160 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },
  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },

  { title: '未收货数量', field: 'itemUnreceivedQty', width: 120 },
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 160 },
  { title: '物流状态', field: 'deliveryOrderStatusList', slot: true, width: 120 },
  { title: '签收单', field: 'isDeliveryOrderSignedList', slot: true, width: 120 },
  { title: '交付主管', field: 'soDeliveryManager', width: 120 },
  { title: '签单返回', field: 'soSigningBack', enums: 'signingBack', width: 120 },
  { title: '直发签收单能否返回', field: 'isSigningBackAccepted', slot: true, width: 120 },
  { title: '直发已送达', field: 'isCustomerReceived', slot: true, width: 120 },

  { title: '工单', field: 'WONoList', type: 'html', showOverflow: false, slot: true, width: 160 },
  { title: '操作', field: 'operation', fixed: 'right', slot: true, width: 160 }
]
const orderOutDated = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '客户接受交货日期', field: 'customerAcceptDate', width: 160 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '更新客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 160 },
  { title: '发货数量', field: 'deliveryOrderQtyList', slot: true, width: 160 },
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 160 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },

  { title: '未收货数量', field: 'itemUnreceivedQty', width: 120 },
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 160 },
  { title: '物流状态', field: 'deliveryOrderStatusList', slot: true, width: 120 },
  { title: '签收单', field: 'isDeliveryOrderSignedList', slot: true, width: 120 },
  { title: '交付主管', field: 'soDeliveryManager', width: 120 },
  { title: '签单返回', field: 'soSigningBack', enums: 'signingBack', width: 120 },
  { title: '直发签收单能否返回', field: 'isSigningBackAccepted', slot: true, width: 120 },
  { title: '直发已送达', field: 'isCustomerReceived', slot: true, width: 120 },

  { title: '工单', field: 'WONoList', type: 'html', showOverflow: false, slot: true, width: 160 },
  { title: '操作', field: 'operation', fixed: 'right', slot: true, width: 160 }
]
const followThan7Days = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '客户接受交货日期', field: 'customerAcceptDate', width: 160 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '更新客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },

  { title: '工单', field: 'WONoList', type: 'html', showOverflow: false, slot: true, width: 160 },
  { title: '操作', field: 'operation', fixed: 'right', slot: true, width: 160 }
]
const close3DaysNotDelivery = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '请求交货日期', field: 'requestDeliveryDate', width: 100 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '更新客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },

  { title: '工单', field: 'WONoList', type: 'html', showOverflow: false, slot: true, width: 160 },
  { title: '操作', field: 'operation', fixed: 'right', slot: true, width: 160 }
]
const pickingAreaPurchase = [
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '发货仓位', field: 'shipWarehouseLocation', slot: true, width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '转储未制单数量', field: 'stoNotDnQty', width: 160 },
  { title: '转储已制单数量', field: 'stoDnQty', width: 160 },
  { title: '转储已制单未发货', field: 'stoDnUnclearedQty', width: 160 },
  { title: '转储已发货数量', field: 'stoDnClearedQty', width: 160 },
  { title: '外向交货单号', field: 'dnNo', width: 160 },
  { title: '外向交货单过账日期', field: 'stoDnPostDate', width: 160 },
  { title: '转储在途数量', field: 'stoDnOnWayQty', width: 160 },
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 160 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },
  { title: '工单', field: 'workOrderNoList', type: 'html', showOverflow: false, fixed: 'right', slot: true, width: 160 }
]
const preparingOrder = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '请求交货日期', field: 'requestDeliveryDate', width: 100 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 160 },
  { title: '发货数量', field: 'deliveryOrderQtyList', slot: true, width: 160 },
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 160 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },

  { title: '工单', field: 'WONoList', type: 'html', showOverflow: false, slot: true, width: 160 },
  { title: '操作', field: 'operation', fixed: 'right', slot: true, width: 160 }
]
const moreThan2DaysNoDeliveryNo = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '请求交货日期', field: 'requestDeliveryDate', width: 100 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 160 },
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 160 },
  { title: '送货方式', field: 'deliveryWayList', slot: true, width: 160 },
  { title: '物流公司', field: 'logisticsNameList', slot: true, width: 160 },
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 160 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },
  { title: '工单', field: 'workOrderNoList', type: 'html', showOverflow: false, fixed: 'right', slot: true, width: 160 }
]
const moreThan5DaysNoReceive = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '请求交货日期', field: 'requestDeliveryDate', width: 100 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 160 },
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 160 },
  { title: '送货方式', field: 'deliveryWayList', slot: true, width: 160 },
  { title: '物流公司', field: 'logisticsNameList', slot: true, width: 160 },
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 160 },
  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },

  { title: '工单', field: 'workOrderNoList', type: 'html', showOverflow: false, fixed: 'right', slot: true, width: 160 }
]
const toBeReceive = [
  { title: '操作提醒', field: 'operationReminderText', slot: true, width: 100 },
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'vc送货单号', field: 'deliveryCode', slot: true, showOverflow: 'ellipsis', width: 100 }, // 未联调
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 100 },
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 100 },
  { title: '发货时长', field: 'deliveryDuration', width: 100 }, // 未联调
  { title: '直发已送达', field: 'isCustomerReceived', slot: true, width: 100 },
  { title: '直发签单能否回传', field: 'isSigningBackAccepted', slot: true, width: 100 },
  { title: '签单返回', field: 'soSigningBack', enums: 'signingBack', width: 120 },
  { title: '签收单', field: 'isDeliveryOrderSignedList', slot: true, width: 80 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '物料组', field: 'materialGroupName', width: 100 },
  { title: '品牌', field: 'brandName', sortable: true, width: 100 },
  { title: '计划行数量', field: 'planDeliveryQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '已收货数量', field: 'itemReceivedQty', width: 100 },
  { title: '未收货数量', field: 'itemUnreceivedQty', width: 120 },
  { title: '送货方式', field: 'deliveryWayList', slot: true, width: 160 },
  { title: '物流公司', field: 'logisticsNameList', slot: true, width: 160 },
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 160 },
  { title: '送货车牌号', field: 'deliveryVehicleNumberList', slot: true, width: 120 },
  { title: '司机姓名', field: 'driverNameList', slot: true, width: 120 },
  { title: '电话号码', field: 'driverPhoneList', slot: true, width: 120 },
  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },
  { title: '采购', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '交付主管', field: 'soDeliveryManager', width: 120 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },

  { title: '工单', field: 'workOrderNoList', type: 'html', showOverflow: false, fixed: 'right', slot: true, width: 160 }
]
const ensuringSupply = [
  { title: '是否开通PO', field: 'openPoStatusText', slot: true, width: 100 },
  { title: '供应商名称', field: 'supplierName', width: 160 },
  { title: '供应商编码', field: 'supplierNo', width: 100 },
  { title: '采购订单日期', field: 'createDate', sortable: true, width: 140 },
  { title: '采购订单号', field: 'poNo', slot: true, width: 100 },
  { title: '采购订单行号', field: 'itemNo', width: 100 },
  { title: '计划行号', field: 'planNo', width: 100 },
  { title: 'SKU', field: 'skuNo', width: 100 },
  { title: '物料描述', field: 'materialDescription', width: 200 },
  { title: '保供状态', field: 'guaranteedStatusText', width: 120 },
  { title: '新PO', field: 'guaranteedPoNo', slot: true, width: 120 },
  { title: '保供信息', field: 'guaranteedMsg', width: 120 },
  { title: '采购订单数量', field: 'itemQty', width: 100 },
  { title: '单位', field: 'unit', width: 100 },
  { title: '仓位', field: 'warehouseLocation', slot: true, width: 100 },
  { title: '计划行交货日期', field: 'planDeliveryDate', slot: true, sortable: true, width: 180 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 100 },
  { title: '客户接受交货日期', field: 'customerAcceptDate', width: 160 },
  { title: '标准交货日期', field: 'standardDeliveryDate', width: 100 },

  { title: '标签', field: 'tag', slot: true, width: 160 },
  { title: '更新客户接受交期', field: 'isCustomerAccept', slot: true, width: 100 },
  { title: '文本备注', field: 'remark', slot: true, width: 200 },
  { title: '累计跟单文本', field: 'accumulateRemark', width: 160 },
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 160 },
  { title: '发货数量', field: 'deliveryOrderQtyList', slot: true, width: 160 },
  { title: '发货状态', field: 'deliveryOrderStatusList', slot: true, width: 160 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '客服', field: 'customerService', width: 100 },
  { title: '销售', field: 'soSellerName', width: 100 },

  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 100 },
  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 160 },
  { title: '专料专供销售单号', field: 'specialSupplySo', width: 160 },
  { title: '含税单价', field: 'taxedPrice', width: 120 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 120 },
  { title: '未税单价', field: 'untaxedPrice', width: 120 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 120 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 120 },

  { title: '未收货数量', field: 'itemUnreceivedQty', width: 120 },
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 160 },
  { title: '物流状态', field: 'deliveryOrderStatusList', slot: true, width: 120 },
  { title: '签收单', field: 'isDeliveryOrderSignedList', slot: true, width: 120 },
  { title: '交付主管', field: 'soDeliveryManager', width: 120 },
  { title: '签单返回', field: 'soSigningBack', enums: 'signingBack', width: 120 },
  { title: '直发签收单能否返回', field: 'isSigningBackAccepted', slot: true, width: 120 },
  { title: '直发客户已收货', field: 'isCustomerReceived', slot: true, width: 120 },

  { title: '工单', field: 'workOrderNoList', type: 'html', showOverflow: false, fixed: 'right', slot: true, width: 160 }
]
// 1: '未下采购订单'
// 2: '供应商未确认'
// 3: '首次承诺不满足'
// 4: '交期不满足'
// 5: '订单已逾期'
// 6: '临近三天逾期且供应商未发货'
// 12: '跟单超7天'
// type=8  '转储采购单'
// type=9  '备货订单'
// type=10  '发货2天没有物流单号'
// type=11  '发货5天未收货'
// type= 14 保供订单
// 2 3 4 5 6 12 9
// 13= 直发待收货和签单
const columnsMap = {
  '1': abnormalPurchase,
  '2': notConfirm,
  '3': firstNotSatisfied,
  '4': dateNotSatisfied,
  '5': orderOutDated,
  '6': close3DaysNotDelivery,
  '12': followThan7Days,
  '8': pickingAreaPurchase,
  '9': preparingOrder,
  '10': moreThan2DaysNoDeliveryNo,
  '11': moreThan5DaysNoReceive,
  '14': ensuringSupply,
  '13': toBeReceive
}
export {
  allColumns,
  columnsMap,
  abnormalPurchase
}
