<template>
  <div>
    <el-form :model="searchForm" style="width: 100%" label-width="120px">
      <el-row>
        <el-col :span="14">
          <el-form-item label="SKU编码">
            <el-input
              v-model="searchForm.skuNo"
              clearable
              placeholder="SKU编码，多个SKU按空格隔开"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :gutter="20">
          <el-form-item label="履约状态">
            <el-select filterable v-model="searchForm.performanceStatus" placeholder="请选择" clearable>
              <el-option
                v-for="item in dictList['performanceStatus']"
                :key="item.value"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2" style="text-align:right;">
          <el-button
            type="primary"
            :loading="loading.searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      ref="aggreementGrid"
      height="400"
      id="aggreement_grid"
      align="center"
      :loading="loading.tableLoading || tabLoading"
      :custom-config="tableCustom"
      :data="agreementInfo"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
      :span-method="mergeRowMethod">
      <template v-slot:toolbar_buttons>
      </template>
      <template v-slot:warehouseLocation_default="{ row }">
        {{
          row.warehouseLocation
            ? row.warehouseLocation
              + ' '
              + ((warehouseList||[]).find(item => item.warehouseLocationCode === row.warehouseLocation) || {}).warehouseLocationName
            : ''
        }}
      </template>
      <template v-slot:soPosition_default="{ row }">
        {{
          row.soPosition
            ? row.soPosition
              + ' '
              + ((warehouseList||[]).find(item => item.warehouseLocationCode === row.soPosition) || {}).warehouseLocationName
            : ''
        }}
      </template>
      <template v-slot:performanceStatus_default="{ row }">
        {{row.performanceStatus ? row.performanceStatus + ' ' + ((dictList.performanceStatus||[]).find(item => item.value === row.performanceStatus) || {}).name : ''}}
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getAgreement } from '@/api/mm'
import { formatAgreementData } from '../../utils'

const columns = [
  {
    field: 'itemNo',
    title: '商品行',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    width: 80
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    width: 180
  },
  {
    field: 'warehouseLocation',
    title: '仓库地点',
    width: 100,
    slots: {
      default: 'warehouseLocation_default'
    }
  },
  {
    field: 'deliveryDate',
    title: '交货日期',
    width: 120
  },
  {
    field: 'deliveryQuantity',
    title: '订单数量',
    width: 80
  },
  {
    field: 'outstandingQuantity',
    title: '未清数量',
    width: 80
  },
  {
    field: 'performanceStatus',
    title: '履约状态',
    width: 120,
    slots: {
      default: 'performanceStatus_default'
    }
  },
  {
    field: 'poSoDeliveryDiff',
    title: 'PO-SO交期差',
    width: 110
  },
  {
    field: 'soNo',
    title: '销售订单',
    width: 120
  },
  {
    field: 'soItemNo',
    title: 'SO项目行',
    width: 90
  },
  {
    field: 'soPosition',
    title: '需求仓库',
    width: 120,
    slots: {
      default: 'soPosition_default'
    }
  },
  {
    field: 'soMatching',
    title: 'SO需求数量',
    width: 100
  },
  {
    field: 'soDeliveryDate',
    title: 'SO要求交期',
    width: 120
  },
  {
    field: 'stoNo',
    title: '转储订单',
    width: 100
  },
  {
    field: 'stoItemNo',
    title: 'STO项目行',
    width: 100
  },
  {
    field: 'stoDeliveryDate',
    title: 'STO交货日期',
    width: 110
  },
  {
    field: 'autoBatching',
    title: '是否允许分批',
    width: 110
  },
  {
    field: 'serviceName',
    title: '客服',
    width: 80
  },
  {
    field: 'saleName',
    title: '销售',
    width: 80
  }
]
export default {
  data () {
    return {
      columns,
      searchForm: {
        skuNo: '',
        performanceStatus: ''
      },
      loading: {
        searchLoading: false,
        tableLoading: false
      },
      agreementInfo: [],
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      }
    }
  },
  props: ['initAgreementInfo', 'id', 'tabLoading', 'spanArr'],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      warehouseList: state => state.orderPurchase.warehouseList
    })
  },
  methods: {
    mergeRowMethod ({ row, column, rowIndex, columnIndex }) {
      if ([0, 1, 2, 3, 4, 5, 6].indexOf(columnIndex) !== -1) {
        const _row = this.spanArr[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    handleSearch () {
      this.loading.searchLoading = true
      this.loading.tableLoading = true
      const { skuNo, performanceStatus } = this.searchForm
      const params = {
        poNo: this.id
      }
      if (skuNo) {
        const formatedSkuNo = skuNo.split(' ').filter(item => !!item).join(',')
        if (formatedSkuNo) {
          params.skuNo = formatedSkuNo
        }
      }
      if (performanceStatus) {
        params.performanceStatus = performanceStatus
      }
      getAgreement(params)
        .then((data) => {
          if (data) {
            this.agreementInfo = data.itemList.reduce((p, c) => {
              const formatedList = formatAgreementData(c)
              return p.concat(formatedList)
            }, [])
          } else {
            this.agreementInfo = []
          }
        })
        .finally(() => {
          this.loading.searchLoading = false
          this.loading.tableLoading = false
        })
    }
  },
  watch: {
    initAgreementInfo (val) {
      this.agreementInfo = val
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
