<template>
  <div>
    <el-form :model="searchForm" style="width: 100%" label-width="120px">
      <el-row>
        <el-col :span="14">
          <el-form-item label="SKU编码">
            <el-input
              v-model="searchForm.skuNo"
              clearable
              placeholder="SKU编码，多个SKU按空格隔开"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" :gutter="20">
          <el-form-item label="提货类型">
            <el-select filterable v-model="searchForm.pickupType" placeholder="请选择" clearable>
              <el-option
                v-for="item in dictList['pickupType']"
                :key="item.value"
                :label="item.value + ' ' + item.name"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="2" style="text-align:right;">
          <el-button
            type="primary"
            :loading="loading.searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </el-col>
      </el-row>
    </el-form>
    <vxe-grid
      border
      resizable
      keep-source
      ref="aggreementGrid"
      height="400"
      id="aggreement_grid"
      align="center"
      :loading="loading.tableLoading || tabLoading"
      :custom-config="tableCustom"
      :data="logisticsInfo"
      :columns="columns"
      :toolbar-config="tableToolbar"
      :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
      :span-method="mergeRowMethod">
      <template v-slot:asnNo_default="{ row }">
        <el-popover
          placement="top-start"
          title="入库信息"
          width="300"
          trigger="hover"
        >
          <el-row v-show="row.actualArriveTime">{{row.actualArriveTime}}到货登记</el-row>
          <el-row v-show="row.putAllTime">{{row.putAllTime}}订单开始上架</el-row>
          <el-row v-show="row.receivedAllTime">{{row.receivedAllTime}}入库完成</el-row>
          <el-row v-show="!row.actualArriveTime&&!row.putAllTime&&!row.receivedAllTime">暂无信息</el-row>
          <span slot="reference" style="color:#409eff;margin-right:5px">{{row.asnNo}}</span>
        </el-popover>
      </template>
      <template v-slot:warehouseLocation_default="{ row }">
          {{
          row.warehouseLocation
            ? row.warehouseLocation
              + ' '
              + ((warehouseList||[]).find(item => item.warehouseLocationCode === row.warehouseLocation) || {}).warehouseLocationName
            : ''
        }}
      </template>
      <template v-slot:pickupType_default="{ row }">
        {{row.pickupType ? row.pickupType + ' ' + ((dictList.pickupType||[]).find(item => item.value === row.pickupType) || {}).name : ''}}
      </template>
      <template v-slot:logisticsCode_default="{ row }">
        <div v-for="(logisticsInfo, index) in row.deliveryInfoVOList" :key="index" style="display: inline-flex;">
          <span style="min-width: 30px">{{row.deliveryInfoVOList && row.deliveryInfoVOList.length > 1 ? `No${index+1}: ` : ''}}</span>
          <div>
            <el-popover
              v-for="(logisticsCode, idx) in (logisticsInfo._logisticsCode || '').split(/\s+|,|，/gi)"
              :key="logisticsCode+ idx"
              placement="right-end"
              :title="`单号${logisticsCode}的物流信息`"
              width="600"
              trigger="hover"
              popper-class="logistics-popper"
              @show="()=>handleFetchDeliveryInfo(logisticsCode ,logisticsInfo.expressCode, row)"
            >
              <p v-if="loading.logisticsLoading">加载中……</p>
              <p v-else-if="logisticsList.length===0">物流信息为空！</p>
              <el-timeline v-else>
                <el-timeline-item
                  v-for="(logistics, index) in logisticsList"
                  :key="index"
                  :timestamp="logistics.trackTime">
                  <p>{{logistics.address}}</p>
                  <p>{{logistics.remark}}</p>
                  <p>{{logistics.status}}</p>
                </el-timeline-item>
              </el-timeline>
              <div slot="reference" style="color:#409eff;">{{logisticsCode}}</div>
            </el-popover>
          </div>
        </div>
      </template>
      <template v-slot:toolbar_buttons>
        <vxe-button @click="toRep">跳转跟单报表</vxe-button>
      </template>
    </vxe-grid>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getLogistics, getLogisticsTrack, getTcLogisticsTrack } from '@/api/mm'
import { formatLogisticsData } from '../../utils'

const columns = [
  {
    field: 'itemNo',
    title: '商品行',
    width: 80
  },
  {
    field: 'skuNo',
    title: 'SKU编码',
    width: 80
  },
  {
    field: 'materialDescription',
    title: '物料描述',
    width: 180,
    showOverflow: 'tooltip'
  },
  {
    field: 'warehouseLocation',
    title: '仓库地点',
    width: 120,
    slots: {
      default: 'warehouseLocation_default'
    }
  },
  {
    field: 'itemQuantity',
    title: '订单数量',
    width: 80
  },
  {
    field: 'outstandingQuantity',
    title: '未清数量',
    width: 80
  },
  {
    field: 'deliveryTime',
    title: '发货日期',
    minWidth: 120,
    type: 'html'
  },
  {
    field: 'number',
    title: '发货数量',
    minWidth: 120,
    type: 'html'
  },
  {
    field: 'deliveryWayText',
    title: '送货方式',
    minWidth: 130,
    type: 'html'
  },
  {
    field: 'logisticsName',
    title: '商家物流公司',
    minWidth: 100,
    type: 'html'
  },
  {
    field: 'logisticsCode',
    title: '商家物流单号',
    minWidth: 170,
    showOverflow: false,
    slots: {
      // 使用插槽模板渲染
      default: 'logisticsCode_default'
    },
    type: 'html'
  },
  {
    field: 'tcLogisticsName',
    title: 'TC物流公司',
    minWidth: 100,
    type: 'html'
  },
  {
    field: 'tcLogisticsCode',
    title: 'TC物流单号',
    minWidth: 160,
    showOverflow: false,
    type: 'html'
  },
  {
    field: 'statusText',
    title: '送货单状态',
    minWidth: 100,
    type: 'html'
  },
  {
    field: 'actualArriveTime',
    title: '到货登记时间',
    width: 180
  },
  {
    field: 'asnStatus',
    title: '入库状态',
    width: 100
  },
  {
    field: 'asnNo',
    title: '入库单号',
    width: 180,
    slots: {
      // 使用插槽模板渲染
      default: 'asnNo_default'
    }
  },
  {
    field: 'receivedQty',
    title: '收货数量',
    width: 80
  },
  {
    field: 'pickupType',
    title: '提货类型',
    width: 100,
    slots: {
      default: 'pickupType_default'
    }
  },
  {
    field: 'deliveryNo',
    title: '内向交货单',
    width: 180
  },
  {
    field: 'driver',
    title: '提货司机/电话',
    width: 180,
    showOverflow: false
  }
]
export default {
  data () {
    return {
      columns,
      sequenceCode: '',
      searchForm: {
        skuNo: '',
        pickupType: ''
      },
      loading: {
        searchLoading: false,
        tableLoading: false,
        logisticsLoading: false
      },
      logisticsInfo: [],
      logisticsList: [],
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      }
    }
  },
  props: ['initLogisticsInfo', 'id', 'tabLoading', 'spanArr', 'isTc'],
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      warehouseList: state => state.orderPurchase.warehouseList
    })
  },
  methods: {
    mergeRowMethod ({ row, column, rowIndex, columnIndex }) {
      if ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].indexOf(columnIndex) !== -1) {
        const _row = this.spanArr[columnIndex][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },
    handleSearch () {
      this.loading.searchLoading = true
      this.loading.tableLoading = true
      const { skuNo, pickupType } = this.searchForm
      const params = {
        poNo: this.id
      }
      if (skuNo) {
        const formatedSkuNo = skuNo.split(' ').filter(item => !!item).join(',')
        if (formatedSkuNo) {
          params.skuNo = formatedSkuNo
        }
      }
      if (pickupType) {
        params.pickupType = pickupType
      }
      getLogistics(params)
        .then((data) => {
          if (data) {
            this.logisticsInfo = data.itemList.reduce((p, c) => {
              const formatedList = formatLogisticsData(c)
              return p.concat(formatedList)
            }, [])
          } else {
            this.logisticsInfo = []
          }
        })
        .finally(() => {
          console.log(this.logisticsInfo)
          this.loading.searchLoading = false
          this.loading.tableLoading = false
        })
    },
    async handleFetchDeliveryInfo (logisticsCode, expressCode, row) {
      this.sequenceCode = logisticsCode
      const { deliveryInfoVOList } = row
      let receiverPhone = null
      const info = deliveryInfoVOList.find(item => item._logisticsCode && item._logisticsCode.indexOf(logisticsCode) > -1)
      if (info && info.receiverPhone) {
        receiverPhone = info.receiverPhone
      }
      this.logisticsList = []
      if (logisticsCode && logisticsCode.trim) {
        logisticsCode = logisticsCode.trim()
        const submitData = { logisticsCode }
        if (expressCode) submitData.expressCode = expressCode
        if (receiverPhone) submitData.receiverPhone = receiverPhone
        this.loading.logisticsLoading = true
        let res = this.isTc === 1 ? await getTcLogisticsTrack({ logisticsCode, deliveryCode: info.deliveryCode }) : await getLogisticsTrack(submitData)
        this.loading.logisticsLoading = false
        const data = this.isTc === 1 ? res?.data?.logisticsContent : res?.data?.logisticsInfo?.logisticsContent
        if (Array.isArray(data) && this.sequenceCode === logisticsCode) {
          this.logisticsList = data
        }
        const { code, msg } = res || {}
        if (code !== 0) {
          this.$message.error(msg || '查询失败！')
        }
      }
    },
    toRep () {
      this.$router.push({ path: '/purchaseReport/trackingOrder' })
      window.logistics_orderNo = this.id
    }
  },
  watch: {
    initLogisticsInfo (val) {
      this.logisticsInfo = val
    },
    isTc (val) {
      if (val === 1) {
        this.columns = columns
      } else {
        this.columns = columns.filter((a) => !['tcLogisticsName', 'tcLogisticsCode'].includes(a.field))
      }
    }
  }
}
</script>

<style lang="scss" scped>
.logistics-popper {
  max-height: 400px;
  overflow: auto;
}

</style>
