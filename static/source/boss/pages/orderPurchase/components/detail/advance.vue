<template>
  <vxe-grid
    border
    height="400"
    resizable
    keep-source
    show-overflow
    align="center"
    :columns="columns"
    :toolbar-config="tableToolbar"
    :data="advanceInfo"
    :loading="tableLoading"
    :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
  >
    <template v-slot:toolbar_buttons>
    </template>
    <template v-slot:isUrgent_default="{ row }">
      {{ row.isUrgent === 'X' ? '是' : '否' }}
    </template>
    <template v-slot:paymentTermCode_default="{ row }">
      {{ row.paymentTermCode + ' ' + row.paymentTermText}}
    </template>
  </vxe-grid>
</template>

<script>
import { listAdvancePayment } from '@/api/mm'

const columns = [
  {
    field: 'paymentApplyStatusText',
    title: '状态',
    width: 100
  },
  {
    field: 'paymentApplyNo',
    title: '付款申请单号',
    width: 120
  },
  {
    field: 'applyAmount',
    title: '申请金额',
    width: 80
  },
  {
    field: 'currency',
    title: '币别',
    width: 80
  },
  {
    field: 'historyPaymentAmount',
    title: '历史预付金额',
    width: 100
  },
  {
    field: 'poTaxedAmount',
    title: '订单含税总金额',
    width: 110
  },
  {
    field: 'paymentTermCode',
    title: '付款条件',
    width: 80,
    slots: {
      default: 'paymentTermCode_default'
    }
  },
  {
    field: 'paymentWayText',
    title: '付款方式',
    width: 100
  },
  {
    field: 'poAttachment',
    title: '采购订单附件',
    width: 100
  },
  {
    field: 'bank',
    title: '银行信息',
    width: 200
  },
  {
    field: 'bankAccount',
    title: '银行账号',
    width: 80
  },
  {
    field: 'remark',
    title: '备注',
    width: 80
  },
  {
    field: 'paymentRemark',
    title: '付款交易附言',
    width: 100
  },
  {
    field: 'applicant',
    title: '申请人',
    width: 80
  },
  {
    field: 'applyTime',
    title: '申请时间',
    width: 140
  },
  {
    field: 'approvePerson',
    title: '初审人',
    width: 80
  },
  {
    field: 'approveTime',
    title: '初审时间',
    width: 140
  },
  {
    field: 'reviewPerson',
    title: '复审人',
    width: 80
  },
  {
    field: 'reviewTime',
    title: '复审时间',
    width: 140
  },
  {
    field: 'rejectionDate',
    title: '拒绝日期',
    width: 80
  },
  {
    field: 'rejectionReason',
    title: '审批拒绝原因',
    width: 100
  },
  {
    field: 'submitBankTime',
    title: '递交银行时间',
    width: 140
  },
  // {
  //   field: '',
  //   title: '付款完成时间',
  //   width: 100
  // },
  {
    field: 'purchaseGroupName',
    title: '采购员',
    width: 80
  },
  {
    field: 'isUrgent',
    title: '是否紧急',
    width: 80,
    slots: {
      default: 'isUrgent_default'
    }
  },
  {
    field: 'companyCode',
    title: '公司',
    width: 80
  }
]
export default {
  data () {
    return {
      columns,
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      advanceInfo: [],
      tableLoading: false
    }
  },
  props: ['id', 'activeTab'],
  watch: {
    activeTab (newValue, prevValue) {
      if (newValue === 'fifth' && !this.mounted) {
        this.listAdvancePayment()
        this.mounted = true
      }
    }
  },
  methods: {
    listAdvancePayment() {
      this.tableLoading = true
      const params = {
        orderNo: this.id
      }
      listAdvancePayment(params)
        .then((data) => {
          this.advanceInfo = data
        })
        .finally(() => {
          this.tableLoading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
