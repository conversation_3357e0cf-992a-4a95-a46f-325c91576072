<template>
  <div class="fragement">
      <div class="basic-infor" style="display:flex;min-width:690px">
          <!--
    含税总金额 - 已收货含税总金额 = 未收货含税总金额
    发票含税总金额含=》税总金额 - 已过账发票含税总金额 = 未过账发票含税总金额 -->
      <div class="first" style="margin-right:8px">
        <span v-if="this.productInfo.orderType==='Z004'">已发货含税总金额：</span>
        <span v-else>已收货含税总金额：</span>
        <span>{{
          receiptAndInvoiceInfor &&
            receiptAndInvoiceInfor.receivedTaxedTotalAmount
        }}</span>
      </div>
      <div class="second" style="margin-right:8px">
        <span v-if="this.productInfo.orderType==='Z004'">未发货含税总金额：</span>
        <span v-else>未收货含税总金额：</span>
        <span>{{formatReceiveTotal(receiptAndInvoiceInfor)}}</span>
      </div>
      <div class="third" style="margin-right:8px">
        <span>已过账发票含税总金额：</span>
        <span>{{
          receiptAndInvoiceInfor &&
            receiptAndInvoiceInfor.postedReceiptTaxedTotalAmount
        }}</span>
      </div>
      <div class="firth" style="margin-right:8px">
        <span>未过账发票含税总金额：</span>
        <span>{{formatReceiptTotal(receiptAndInvoiceInfor)}}</span>
      </div>
    </div>
    <el-form ref="searchForm" :model="searchForm" style="margin: 10px 0;">
      <el-row>
        <div
          class="float-rigth"
          style="float: right;
          margin-right: 10px;"
        >
          <el-button type="primary" @click="search('searchForm')"
            >查询</el-button
          >
          <el-button type="primary" @click="refresh('searchForm')"
            >刷新</el-button
          >
        </div>
        <el-input
          style="width: 260px;  float: right; margin-right: 10px;"
          class="float-rigth"
          placeholder="SKU编码，多个SKU按空格隔开"
          v-model="searchForm.skuNos"
          clearable
        />
      </el-row>
    </el-form>
    <vxe-table
    :expand-config="{ expandAll: true }"
      border
      :data="
        (receiptAndInvoiceInfor &&
          receiptAndInvoiceInfor.pageItems &&
          receiptAndInvoiceInfor.pageItems.rows) ||
          []
      "
      :auto-resize="true"
      show-overflow
      align="center"
      ref="outerTable"
      :loading="loading"
    >
      <vxe-table-column  type="expand"  width="50">
        <template #content="{ row }">
          <vxe-grid
            ref="innerTable"
            border
            :data="row.records"
            :auto-resize="true"
            align="center"
            :columns="innerColumns"
            :expand-config="{ expandAll: true }"
          >
          <template v-slot:type_default="{row}">
            <span v-if="row.type === 1">出库</span>
              <span v-else-if="row.type === 2">入库</span>
            <span v-else-if="row.type === 3">发票</span>
          </template>
          </vxe-grid>
        </template>
      </vxe-table-column>
      <vxe-table-column
        v-for="item in outerColumns"
        :key="item.field"
        :field="item.field"
        :title="item.title"
      ></vxe-table-column>
    </vxe-table>
    <Pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getReceiptAndInvoiceList"
    />
  </div>
</template>

<script>
import { getReceiptAndInvoiceList } from '@/api/mm'
import Pagination from '@/components/Pagination'

const outerColumns = [
  {
    field: 'itemNo',
    title: '商品行号'
    // width:
  },
  {
    field: 'skuNo',
    title: 'SKU编码'
    // width:
  },
  {
    field: 'materialDescription',
    title: '物料描述'
  },
  {
    field: 'itemQuantity',
    title: '商品行数量'
  },
  {
    field: 'receivedQuantity',
    title: '已过账数量'
  },
  {
    field: 'receiptQuantity',
    title: '已发票数量'
  },
  {
    field: 'postPercent',
    title: '过账比例'
  },
  {
    field: 'receiptPercent',
    title: '发票比例'
  }
]
const innerColumns = [
  {
    field: 'type',
    title: '业务类型',
    width: 80,
    slots: {
      default: 'type_default'
    }
  },
  {
    field: 'recordNo',
    title: '单号'
  },
  {
    field: 'itemNo',
    title: '行号',
    width: 80
  },
  {
    field: 'createDate',
    title: '单据日期',
    width: 100
  },
  {
    field: 'postDate',
    title: '过账日期',
    width: 100
  },
  {
    field: 'orderQuantity',
    title: '单据数量',
    width: 80
  },
  {
    field: 'postQuantity',
    title: '过账数量',
    width: 80
  },
  {
    field: 'unit',
    title: '订单单位',
    width: 80
  },
  {
    field: 'postAmount',
    title: '金额',
    width: 80
  },
  {
    field: 'currency',
    title: '币别',
    width: 80
  },
  {
    field: 'referNo',
    title: '参考单号'
  },
  {
    field: 'referItemNo',
    title: '参考单行号'
  }
]

export default {
  props: ['id', 'activeTab'],
  inject: ['getProductInfo'],
  data() {
    return {
      outerColumns,
      innerColumns,
      receiptAndInvoiceInfor: {},
      searchForm: {
        skuNos: ''
      },
      listQueryInfo: {
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      loading: false,
      mounted: false
    }
  },
  computed: {
    copySearchForm() {
      const { skuNos } = this.searchForm
      let params = {}
      console.log(skuNos)
      var fomatterSkuNos = []
      if (skuNos) {
        fomatterSkuNos = skuNos.split(/\s|,|;|，|；/).filter(function(s) {
          return s && s.trim()
        })
      }
      params.skuNos = fomatterSkuNos
      return params
    },
    productInfo () {
      return this.getProductInfo()
    }
  },
  watch: {
    receiptAndInvoiceInfor: {
      handler(newVal) {
        this.receiptAndInvoiceInfor = newVal
      },
      deep: true
    },
    activeTab (newValue, prevValue) {
      if (newValue === 'fourth' && !this.mounted) {
        this.getReceiptAndInvoiceList()
        this.mounted = true
      }
    }
  },
  methods: {
    formatReceiveTotal (total) {
      const { taxedTotalAmount, receivedTaxedTotalAmount } = total
      let ret = (parseFloat(taxedTotalAmount) || 0) - (parseFloat(receivedTaxedTotalAmount) || 0)
      ret = ret.toFixed(2)
      if (isNaN(ret)) ret = null
      return ret
    },
    formatReceiptTotal (total) {
      const { taxedTotalAmount, postedReceiptTaxedTotalAmount } = total
      let ret = (parseFloat(taxedTotalAmount) || 0) - Math.abs((parseFloat(postedReceiptTaxedTotalAmount)) || 0)
      ret = ret.toFixed(2)
      if (isNaN(ret)) ret = null
      return ret
    },
    search() {
      this.listQueryInfo.pageNo = 1
      this.listQueryInfo.pageSize = 10
      this.getReceiptAndInvoiceList()
    },
    getReceiptAndInvoiceList() {
      this.loading = true
      let query = {
        pageNo: this.listQueryInfo.pageNo,
        pageSize: this.listQueryInfo.pageSize
      }
      const params = Object.assign(
        { poNo: this.id, ...query },
        this.copySearchForm
      )
      getReceiptAndInvoiceList(params)
        .then((res) => {
          if (res) {
            this.receiptAndInvoiceInfor = res
            this.total = (res && res.pageItems.total) || 0
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    refresh() {
      this.getReceiptAndInvoiceList()
    }
  },
  components: {
    Pagination
  }
}
</script>
<style lang="scss" scoped>
.fragement {
  ::v-deep .vxe-table--expanded {
    font-size: 2em;
  }
}
// .fragement {
//   .basic-infor {
//     display: -moz-box; /*firefox*/
//     display: -ms-flexbox; /*IE10*/
//     display: -webkit-box; /*Safari*/
//     display: -webkit-flex; /*Chrome*/
//     display: box;
//     display: flexbox;
//     display: flex;
//     min-width: 690px;
//     div {
//       margin-right: 8px;
//     }
//   }

//   ::v-deep .el-form {
//     margin: 10px 0;
//     .el-input {
//       width: 260px;
//     }
//     .float-rigth {
//       float: right;
//       margin-right: 10px;
//     }
//   }
// }
</style>
