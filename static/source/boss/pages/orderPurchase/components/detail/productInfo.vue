<template>
  <div class="product-info" ref="productInfo">
    <div class="result">
      <vxe-grid border
        resizable
        column-key
        keep-source
        show-overflow
        highlight-hover-row
        highlight-current-row
        ref="detailGrid"
        :height="tableHeight"
        id="detail_grid"
        align="center"
        :scroll-y="{gt: 0}"
        :custom-config="tableCustom"
        :data="itemListData"
        :columns="columns"
        :toolbar-config="tableToolbar"
        :edit-config="{trigger: 'click', mode: 'row', showStatus: true}"
        @cell-dblclick="handleDblclick"
      >
        <template v-slot:toolbar_buttons>
          <div class="text" v-if="showTotalCharge">
            <span>未税总额：{{formatString(3, productInfo.currency,productInfo.untaxedTotalAmount)}}</span>
            <span>含税总额：{{formatString(3, productInfo.currency,productInfo.taxedTotalAmount)}}</span>
            <span>税费总额：{{formatString(3, productInfo.currency,productInfo.taxTotalAmount)}}</span>
            <span v-if="['Z001', 'Z010'].includes(productInfo.orderType)">
              毛利率：{{productInfo.poExtend && ![undefined, 'undefined', '', null, NaN].includes(productInfo.poExtend.lastGrossMargin)? `${productInfo.poExtend.lastGrossMargin}%` : ''}}</span>
            <template v-if="productInfo.orderType === 'Z002'">
              <span>下单时汇率：{{ productInfo.poExtend.exchangeRate || '' }}</span>
              <!-- <span>含税总额参考：{{productInfo.poExtend.taxedCnyAmount ? formatString(3, 'CNY',productInfo.poExtend.taxedCnyAmount) : ''}}</span> -->
              <span>未税总额参考：{{productInfo.poExtend.untaxedCnyAmount ? formatString(3, 'CNY',productInfo.poExtend.untaxedCnyAmount) : ''}}</span>
            </template>
            <span></span>
          </div>
        </template>
        <template v-slot:checkbox_default="{row, column}">
          <el-checkbox disabled :true-label="1" :false-label="0" v-model="row[column.property]"></el-checkbox>
        </template>
        <template v-slot:warehouseLocation_default="{row}">
          {{mapLocation(row.warehouseLocation, row.factoryCode)}}
        </template>
         <template v-slot:tcWarehouse_default="{row}">
          {{ row.poItemExtend.tcWarehouseCode ? formatString(3, row.poItemExtend.tcWarehouseCode,row.poItemExtend.tcWarehouseName) : '' }}
        </template>
        <!-- <template v-slot:taxedCnyAmount_default="{row}">
          {{ row.poItemExtend.taxedCnyAmount }}
        </template> -->
        <template v-slot:untaxedCnyAmount_default="{row}">
          {{ row.poItemExtend.untaxedCnyAmount }}
        </template>
        <template v-slot:isDeleted_default="{row}">
          <span v-if="row.isDeleted">已删除</span>
          <span v-else-if="row.approveTriggerDetail">
            <el-tooltip :content="row.approveTriggerDetail" placement="bottom" effect="light">
             <img src="@/assets/images/warning.png"/>
            </el-tooltip>
          </span>
        </template>
        <template v-slot:factoryCode_default="{row}">
          {{mapFactoryCode(row.factoryCode)}}
        </template>
        <template v-slot:shipWarehouseLocation_default="{row}">
          {{mapLocation(row.shipWarehouseLocation, row.factoryCode)}}
        </template>
        <template v-slot:materialGroupName_default="{row}">
          {{formatString(3, row.materialGroupNum, row.materialGroupName)}}
        </template>
        <template v-slot:projectCategory_default="{row}">
          <!-- {{ row.projectCategory}} -->
          {{mapKeyToValue('poProjectCategory', row.projectCategory || '', dictList)}}
        </template>
        <template v-slot:cloud_default="{row}">
          {{row.cloud ? '是' : (row.cloud === null ? '' : '否')}}
        </template>
        <template v-slot:lastGrossMargin_default="{row}">
          {{ row.poItemExtend && ![undefined, 'undefined', '', null, NaN].includes(row.poItemExtend.lastGrossMargin) ? `${row.poItemExtend.lastGrossMargin}%` : ''}}
        </template>
        <template v-slot:orderChannelList_default="{row}">
          {{ row.poItemExtend ? mapKeyToValue('orderChannel', row.poItemExtend.orderChannelList, dictList) : '' }}
        </template>
        <template v-slot:designatedChannelCustomer_default="{row}">
          {{ row.poItemExtend && row.poItemExtend.designatedChannelCustomerCode ? row.poItemExtend.designatedChannelCustomerCode + ' ' + row.poItemExtend.designatedChannelCustomerName : '' }}
        </template>
        <template v-slot:operation="{ row }">
          <vxe-button type="text" status="primary" @click="toDetail(row)">详情</vxe-button>
          <vxe-button type="text" status="primary" @click="handleChangeChannel(row)" v-if="showChangeChannel(row)">换渠道</vxe-button>
        </template>
      </vxe-grid>
    </div>
    <div class="detailDialog">
      <DetailDialog
        v-if="detailDialogShow"
        @close-dialog="closeDialog"
        :finalFields="finalFields"
        :showCharge="showCharge"
        :rowDetail="rowDetail"/>
    </div>
    <ChangeChannelDialog
      :show-dialog.sync="showChangeChannelDialog"
      :changeChannelData="changeChannelData"
      @refresh="refresh"
    />
  </div>
</template>
<script>
import DetailDialog from './components/DetailDialog'
import ChangeChannelDialog from '@/pages/orderPurchase/components/common/ChangeChannelDialog'
import { mapKeyToValue, formatString } from '@/utils/mm'
import { safeRun } from '@/utils/index'
import { toFixedByRadix } from '@/utils/price'
import { handleClipboardV2 as clip } from '@/utils/clipboard';
import { handleDisplaceSku } from '@/pages/orderPurchase/utils/index'
import { mapState } from 'vuex'
import Sortable from 'sortablejs'

export default {
  name: 'productInfo',
  components: { DetailDialog, ChangeChannelDialog },
  inject: ['getProductInfo'],
  props: {
    itemList: Array,
    showCharge: Boolean,
    updateLoading: {
      type: Function
    },
    finalFields: {
      type: Array,
      default: () => []
    },
    oneProductAuth: Boolean
  },
  data () {
    return {
      tableCustom: {
        storage: true
      },
      showChangeChannelDialog: false,
      changeChannelData: {},
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      detailDialogShow: false,
      rowDetail: {},
      tableHeight: 355
    }
  },
  created() {
    this.columnDrop2()
  },
  computed: {
    showTotalCharge () {
      // Z003-转储订单 不显示价费信息
      return this.productInfo.orderType !== 'Z003'
    },
    productInfo () {
      return this.getProductInfo()
    },
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'table' && (/detail/.test(item.status) || !item.status))
        // if (!this.productInfo?.itemList.find(item => item.approveTriggerDetail)) {
        //   ret = ret.filter(item => !(item.prop === 'orderReason'))
        // }
      })
      return ret
    },
    columns () {
      const cols = this.getColumns.map(item => {
        const { prop, name, width, format, enums } = item
        const newItem = {
          field: prop,
          title: name,
          width
        }
        if (['isFree', 'isConfirmed', 'isUrgent', 'isDeliveryDone'].indexOf(prop) > -1) {
          newItem.slots = {
            default: 'checkbox_default'
          }
        } else if (prop === 'isDeleted') {
          newItem.slots = {
            default: 'isDeleted_default'
          }
        } else if (prop === 'warehouseLocation') {
          newItem.slots = {
            default: 'warehouseLocation_default'
          }
        } else if (prop === 'shipWarehouseLocation') {
          newItem.slots = {
            default: 'shipWarehouseLocation_default'
          }
        } else if (prop === 'factoryCode') {
          newItem.slots = {
            default: 'factoryCode_default'
          }
        } else if (prop === 'materialGroupName') {
          newItem.slots = {
            default: 'materialGroupName_default'
          }
        } else if (prop === 'projectCategory') {
          newItem.slots = {
            default: 'projectCategory_default'
          }
        } else if (prop === 'cloud') {
          newItem.slots = {
            default: 'cloud_default'
          }
        } else if (prop === 'lastGrossMargin') {
          newItem.slots = {
            default: 'lastGrossMargin_default'
          }
        } else if (prop === 'untaxedCnyAmount') {
          newItem.slots = {
            default: 'untaxedCnyAmount_default'
          }
        } else if (prop === 'orderChannelList') {
          newItem.slots = {
            default: 'orderChannelList_default'
          }
        } else if (prop === 'designatedChannelCustomer') {
          newItem.slots = {
            default: 'designatedChannelCustomer_default'
          }
        }
        if (enums) {
          newItem.formatter = ({ cellValue }) => this.mapKeyToValue(enums, cellValue, this.dictList)
        } else if (format) {
          newItem.formatter = format
        }
        return newItem
      })
      cols.push({ title: '操作', width: 150, fixed: 'right', slots: { default: 'operation' } })
      if (this.productInfo?.poExtend?.isTc === 1) {
        cols.splice(8, 0, {
          title: 'TC仓',
          field: 'tcWarehouse',
          width: 120,
          slots: { default: 'tcWarehouse_default' }
        })
      }
      return cols
    },
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      factoryList: state => state.orderPurchase.factoryList,
      warehouseList: state => state.orderPurchase.warehouseList
    }),
    itemListData () {
      return this.itemList.map(item => {
        let fit = { ...item }
        safeRun(() => {
          fit.fixedAssetsCardNo = item.fixedAssetsList[0].fixedAssetsCardNo;
        })
        fit.deliveryChangeReason = item.planList?.[0]?.deliveryChangeReason ? item.planList?.[0]?.deliveryChangeReason + '' : ''
        return fit
      })
    }
  },
  methods: {
    refresh() {
      this.$emit('refresh')
    },
    showChangeChannel(row) {
      return this.productInfo.orderType === 'Z001' && row.isDeleted !== 1 && row.shipStatus !== 1 && this.productInfo.isDeleted !== 1 && this.oneProductAuth
    },
    formatString,
    mapKeyToValue,
    toFixedByRadix,
    filterSkuNo ({ option, row }) {
      // eslint-disable-next-line
      return row.skuNo == option.data
    },
    beforeDestroy() {
      if (this.sortable2) {
        this.sortable2.destroy()
      }
    },
    mapLocation (location, code) {
      let ret = location
      ret = safeRun(() => {
        return this.warehouseList.filter(item => item.factoryCode === code)
          .filter(item => item.warehouseLocationCode === location)[0].warehouseLocationName
      })
      if (location && ret) {
        ret = location + ' ' + ret
      }
      return ret
    },
    mapFactoryCode (code) {
      let ret = code
      ret = safeRun(() => {
        // eslint-disable-next-line eqeqeq
        return this.factoryList.filter(fac => fac.value == code)[0].name
      })
      return code + ' ' + ret
    },
    closeDialog () {
      this.detailDialogShow = false
    },
    async handleChangeChannel (row) {
      this.updateLoading(true)
      const res = await handleDisplaceSku(row)
      this.updateLoading(false)
      if (res) {
        this.showChangeChannelDialog = true
        this.changeChannelData = res
        this.changeChannelData.poNo = row.poNo
        this.changeChannelData.poItemNo = row.itemNo
        this.changeChannelData.factoryCode = this.itemList && this.itemList.length > 0 ? this.itemList[0].factoryCode : ''
        this.changeChannelData.supplierNo = this.productInfo.supplierNo || ''
      }
    },
    toDetail (row) {
      this.rowDetail = row
      this.detailDialogShow = true
    },
    handleClick () {},
    handleDblclick(event) {
      try {
        if (event?.column?.field === '_options') {
          return;
        }
        const { cell } = event;
        let content = cell.innerText;
        if (content) {
          clip(content);
        }
      } catch (err) {
        console.log(err);
      }
    },
    columnDrop2() {
      this.$nextTick(() => {
        const $table = this.$refs.detailGrid
        this.sortable2 = Sortable.create(
          $table.$el.querySelector(
            '.body--wrapper>.vxe-table--header .vxe-header--row'
          ),
          {
            handle: '.vxe-header--column:not(.col--fixed)',
            onEnd: ({ item, newIndex, oldIndex }) => {
              console.log(item, newIndex, oldIndex)
              const {
                fullColumn,
                tableColumn

              } = $table.getTableColumn()

              // 目标表头
              const targetThElem = item
              const wrapperElem = targetThElem.parentNode
              console.dir('wrapperElem' + wrapperElem);
              // 被移到位置的数据
              const newColumn = fullColumn[newIndex]
              if (newColumn.fixed) {
                // 错误的移动
                if (newIndex > oldIndex) {
                  wrapperElem.insertBefore(
                    targetThElem,
                    wrapperElem.children[oldIndex]
                  )
                } else {
                  // （被移到位置的数据）前插入 （要移动的数据）
                  wrapperElem.insertBefore(
                    // 要移动的数据
                    wrapperElem.children[oldIndex],
                    // 《===被移到位置的数据
                    targetThElem
                  )
                }
                return this.$message.error('固定列不允许拖动！')
              }
              // 转换真实索引
              const oldColumnIndex = $table.getColumnIndex(
                // 找到数据
                tableColumn[oldIndex]
              )
              const newColumnIndex = $table.getColumnIndex(
                tableColumn[newIndex]
              )
              // 移动到目标列
              const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
              fullColumn.splice(newColumnIndex, 0, currRow)
              $table.loadColumn(fullColumn)
            }
          }
        )
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.product-info{
  .text span {
    margin-right: 20px;
  }
  .my-input {
    margin: 10px;
    width: 140px;
    height: 32px;
  }
}
</style>
