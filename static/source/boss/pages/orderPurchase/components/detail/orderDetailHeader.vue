<template>
  <div class="order-detail">
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="基本信息" name="BaseInfo" />
      <el-tab-pane label="备注文本" name="RemarkText" />
      <el-tab-pane label="通讯信息" name="ContactInfo" />
      <el-tab-pane v-if="showCharge && !hideByAuth" label="价费信息" name="ChargeInfo" />
      <el-tab-pane label="相关附件" name="Attachment" />
    </el-tabs>
    <el-button class="toggle-button" circle :icon="showHeaderIcon" @click="handleFold" />
    <TitleAndButtonGroup v-show="showHeader" v-on="$listeners" />
    <component v-show="showHeader" :is="activeName" :finalFields="finalFields" :detailLoading="detailLoading" :updateLoading="updateLoading"></component>
    <DividerHeader>商品信息</DividerHeader>
  </div>
</template>
<script>
import BaseInfo from './components/baseInfo'
import RemarkText from './components/remarkText'
import ContactInfo from './components/contactInfo'
import ChargeInfo from './components/chargeInfo'
import Attachment from './components/Attachment'
import TitleAndButtonGroup from './components/titleAndButtonGroup'
import DividerHeader from '@/components/DividerHeader'
import { findTopRole } from '@/utils/auth'
import { mapState } from 'vuex'
import { safeRun } from '@/utils/index'

// 订单详情
export default {
  name: 'orderDetail',
  inject: ['getProductInfo'],
  props: ['finalFields', 'detailLoading', 'updateLoading', 'showCharge'],
  components: {
    BaseInfo,
    RemarkText,
    ContactInfo,
    ChargeInfo,
    Attachment,
    TitleAndButtonGroup,
    DividerHeader
  },
  data () {
    return {
      activeName: 'BaseInfo',
      showHeader: true
    }
  },
  computed: {
    productInfo () {
      return this.getProductInfo()
    },
    ...mapState({
      userRole: state => state.userRole
    }),
    hideByAuth () {
      return findTopRole(this.userRole) === 'PMS仓管'
    },
    showHeaderIcon () {
      return this.showHeader ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
    }
  },
  mounted () {
    this.$nextTick(() => {
      window.addEventListener('resize', this.setTableHeight, false)
    })
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.setTableHeight, false)
  },
  methods: {
    setTableHeight () {
      const grid = this.findGrid()
      setTimeout(() => {
        safeRun(() => {
          grid.tableHeight = window.innerHeight - grid.$refs.detailGrid.$el.offsetTop - 200
          if (grid.tableHeight < 355) {
            grid.tableHeight = 355
          }
        })
      }, 100)
    },
    handleTabClick () {
      this.showHeader = true
      this.setTableHeight()
    },
    handleFold () {
      this.showHeader = !this.showHeader
      this.setTableHeight()
    },
    findGrid () {
      let ret = {}
      safeRun(() => {
        ret = this.$parent.$children.find(child => child.$options.name === 'productInfo')
      })
      return ret
    }
  }
}
</script>
<style lang="less" scoped>
.order-detail{
  position: relative;
  .toggle-button{
    position: absolute;
    top: 0px;
    right: 20px;
  }
}
</style>
