<template>
  <div>
    <el-form label-width="150px">
      <el-row>
        <el-col class="row-item" v-for="item in getTitle" :key="item.name" :span="item.span || 8">
          <span>{{labelColon(item.name)}}</span>
          <span>{{rowDetail[item.prop]}}</span>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="compData">
      <el-table-column v-for="col in getTable" :key="col.name" :prop="col.prop" :label="col.name">
        <template slot-scope="{ row }">
          <span v-if="col.prop == 'componentWarehouseLocation'">
            {{row.componentWarehouseLocation}}
            {{readNameFromDic('warehouseLocation', row.componentWarehouseLocation)}}
          </span>
          <span v-else>{{row[col.prop]}}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import { labelColon, readNameFromDic } from '@/utils/mm'
import { safeRun } from '@/utils/index'
export default {
  name: 'PDComp',
  props: {
    rowDetail: Object,
    finalFields: Array
  },
  data () {
    return {
      titleList: [ 'skuNo', 'itemQuantity', 'materialDescription' ]
    }
  },
  computed: {
    getTitle () {
      return this.getColumns.filter(item => this.titleList.includes(item.prop))
    },
    getTable () {
      return this.getColumns.filter(item => !this.titleList.includes(item.prop))
    },
    compData () {
      return this.rowDetail.componentList || []
    },
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'detailEntrust' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    }
  },
  methods: {
    labelColon,
    readNameFromDic
  }
}
</script>
<style scoped>
.row-item{
  margin-bottom: 10px;
}
</style>
