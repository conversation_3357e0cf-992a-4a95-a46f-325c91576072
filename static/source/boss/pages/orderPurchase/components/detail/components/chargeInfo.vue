<template>
  <div class="charge-info">
    <el-form label-width="110px">
      <el-row>
        <el-col class="row-item" v-for="item in getText" :key="item.name" :span="item.span || 8">
          <span>{{item.name}}：</span>
          <span>
            <span>{{getItemType(item.prop)}}</span>
            <span>{{productInfo[item.prop]}}</span>
            <span v-if="/Amount/.test(item.prop)">
              {{productInfo[item.prop]?formatString(1, productInfo[item.prop.replace('Amount', 'Currency')]):''}}
            </span>
          </span>
        </el-col>
        <el-col class="row-item" :span="24">
          <span style="display: flex;align-items: center;">
            <span>
              <span>杂费总计:</span>
              <span style="margin-right: 10px;">
                {{productInfo.poExtend['shareSundryAmount'] ? Number(productInfo.poExtend['shareSundryAmount']).toFixed(2) : ''}}
              </span>
            </span>
            <el-select multiple disabled style="width: 200px;margin-right: 10px;"  v-model="sundryReason" placeholder="请选择杂费原因">
              <el-option v-for="item in dictList.sundryReason" :key="item.value" :label="item.name" :value="item.value"></el-option>
            </el-select>
            <el-input disabled v-model="productInfo.poExtend['sundryReasonDetail']" type="textarea" :rows="1" show-word-limit maxlength="30" style="width: 300px;" placeholder="请输入杂费原因备注"></el-input>
          </span>
        </el-col>
        <el-col class="row-item" :span="12" v-if="productInfo.orderType==='Z001'">
          <span style="display: inline-block;margin-right: 10px;">
            <span>
              <span>分摊返利总计:</span>
              <span>
                {{productInfo['shareRebateAmount'] ? Number(productInfo['shareRebateAmount']).toFixed(2) : ''}}
              </span>
            </span>
            <SelectRebateDialog :isDetailDialog="true" :purchaseData="productInfo" />
          </span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="3">
          <el-checkbox
            v-model="productInfo.poExtend.isDelayedPayment"
            :true-label="1"
            :false-label="0"
            disabled
          />
          异常延期付款
        </el-col>
        <el-col :span="3">
          <el-checkbox
            v-model="productInfo.poExtend.isForceSignReconciliation"
            :true-label="1"
            :false-label="0"
            disabled
          />
          强制签单对账
        </el-col>
        <el-col v-for="item in getCheckbox" :span="item.span || 3" :key="item.name">
          <el-checkbox v-if="item.type==='checkbox'" disabled :value="Boolean(productInfo[item.prop])" />
          {{item.name}}
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12"  style="display: flex; align-items: flex-start" v-if="productInfo.orderType === 'Z001'">
          <div>附件凭证：</div>
          <div>
            <div v-for="item in productInfo.initialShippingAmountAttachmentList" :key="item.fileUrl">
              <el-link :href="item.fileUrl" target="_blank" type="primary">{{item.fileName}}</el-link>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { formatString } from '@/utils/mm'
import { mapState } from 'vuex'
import SelectRebateDialog from '@/pages/orderPurchase/components/common/SelectRebateDialog'

export default {
  name: 'chargeInfo',
  inject: ['getProductInfo'],
  props: ['finalFields'],
  components: {
    SelectRebateDialog
  },
  computed: {
    productInfo () {
      return this.getProductInfo()
    },
    sundryReason() {
      return this.productInfo?.poExtend['sundryReason']?.split(',').filter(Boolean) || []
    },
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'charge' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    },
    getText() {
      return this.getColumns.filter(item => item.type !== 'checkbox')
    },
    getCheckbox() {
      return this.getColumns.filter(item => item.type === 'checkbox')
    },
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    })
  },
  methods: {
    formatString,
    // 获取国际运费、报关杂费类型
    getItemType(prop) {
      if (!this.productInfo[prop]) return ''
      if (prop === 'intlShippingAmount') {
        return this.dictList.intlShippingType.find((item) => Number(item.value) === Number(this.productInfo.intlShippingType))?.name || ''
      }
      if (prop === 'customsFeeAmount') {
        return this.dictList.customsFeeType.find((item) => Number(item.value) === Number(this.productInfo.customsFeeType))?.name || ''
      }
      return ''
    }
  }
}
</script>
<style lang="scss" scoped>
.charge-info{
  .row-item{
    margin-bottom: 10px;
  }
  .dialog{
    padding: 20px;
  }
  .content{
    padding: 0 35px;
    display: flex;
    align-items: center;
    .label{
      width: 80px;
    }
  }
  .tips{
    padding: 0 35px;
    display: flex;
    flex-direction: column;
    justify-content: left;
    align-items: left;
  }
}
</style>
<style lang="scss">
.clickable{
  margin-left: 10px;
  cursor: pointer;
  &:hover{
    opacity: 0.7;
  }
  &:active{
    opacity: 0.9;
  }
}
</style>
