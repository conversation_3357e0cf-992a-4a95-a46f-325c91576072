<template>
  <div class="tele-info">
    <el-form label-width="160px">
      <el-row>
        <el-col v-if="productInfo.poExtend.isTc === 1" class="row-item" span="12">
          <span>TC仓联系人：</span>
          <span>{{productInfo.poExtend.tcReceiverName}}</span>
        </el-col>
        <el-col v-if="productInfo.poExtend.isTc === 1" class="row-item" span="12">
          <span>TC仓联系人电话：</span>
          <span>{{productInfo.poExtend.tcReceiverPhone}}</span>
        </el-col>
        <el-col v-if="productInfo.poExtend.isTc === 1" class="row-item" span="24">
          <span>TC仓地址：</span>
          <span>{{`${productInfo.poExtend.tcReceiverProvinceText || ''} ${productInfo.poExtend.tcReceiverCityText || ''} ${productInfo.poExtend.tcReceiverRegionText || ''} ${productInfo.poExtend.tcReceiverAddressDetail || ''}`}}</span>
        </el-col>
        <el-col class="row-item" v-for="item in getColumns" :key="item.name" :span="item.span">
          <span>{{item.name}}：</span>
          <span v-if="item.prop === 'receiveAddressDetail'" :label="item.name" :prop="item.prop">
            <span>{{renderAddress('receive')}}</span>
          </span>
          <span v-else-if="item.prop === 'receiptAddressDetail'" :label="item.name" :prop="item.prop">
            <span>{{renderAddress('receipt')}}</span>
          </span>
          <span v-else-if="item.prop === 'supplierAddressDetail'" :label="item.name" :prop="item.prop">
            <span>{{renderAddress('supplier')}}</span>
          </span>
          <span v-else :label="item.name" :prop="item.prop">
            <span>{{productInfo[item.prop]}}</span>
          </span>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
export default {
  name: 'contactInfo',
  inject: ['getProductInfo'],
  props: ['finalFields'],
  computed: {
    productInfo () {
      return this.getProductInfo()
    },
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'comm' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    }
  },
  data () {
    return {}
  },
  methods: {
    handleClick () {},
    addressWithSpace (address) {
      return address ? address + ' ' : address
    },
    renderAddress (address) {
      let ret = ''
      try {
        ret = (this.addressWithSpace(this.productInfo[`${address}ProvinceText`]) || '') +
          (this.addressWithSpace(this.productInfo[`${address}CityText`]) || '') +
          (this.addressWithSpace(this.productInfo[`${address}RegionText`]) || '') +
          (this.addressWithSpace(this.productInfo[`${address}StreetText`]) || '') +
          (this.addressWithSpace(this.productInfo[`${address}AddressDetail`]) || '')
      } catch (err) {}
      return ret
    }
  },
  created () {}
}
</script>
<style scoped >
.row-item{
  margin-bottom: 10px;
}
</style>
