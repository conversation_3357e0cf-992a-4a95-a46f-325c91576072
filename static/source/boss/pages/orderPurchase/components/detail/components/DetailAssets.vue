<template>
  <el-table :data="rowDetail.fixedAssetsList">
    <el-table-column
      v-for="col in getColumns"
      align="center"
      :key="col.name"
      :prop="col.prop"
      :label="col.name"
    />
  </el-table>
</template>
<script>
import { safeRun } from '@/utils/index'
export default {
  name: 'PDAsset',
  props: {
    rowDetail: Object,
    finalFields: Array
  },
  computed: {
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields.filter(item => item.category === 'detailAssets' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    }
  },
  data () {
    return {}
  }
}
</script>
