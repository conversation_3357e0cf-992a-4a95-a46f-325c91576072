<template>
  <el-form label-width="150px">
    <el-row>
      <el-col class="row-item" v-for="item in getColumns" :key="item.name" :span="item.span || 8">
        <span>
          <span>{{labelColon(item.name)}}</span>
          <span v-if="/Name/.test(item.prop)">
            {{rowDetail[item.prop.replace('Name', 'No')]}}
          </span>
          <span v-if="/Amount/.test(item.prop)">{{getItemType(item.prop)}}</span>
          <span>{{rowDetail[item.prop]}}</span>
          <span v-if="/Amount/.test(item.prop)">
            {{formatString(1, rowDetail[item.prop.replace('Amount', 'Currency')])}}
          </span>
        </span>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import { labelColon, formatString } from '@/utils/mm'
import { safeRun } from '@/utils/index'
import { mapState } from 'vuex'

export default {
  name: 'PDCharge',
  props: {
    rowDetail: Object,
    finalFields: Array
  },
  computed: {
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'detailCharge' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    },
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    })
  },
  data () {
    return {}
  },
  methods: {
    labelColon,
    formatString,
    getItemType(prop) {
      if (!this.rowDetail[prop]) return ''
      if (prop === 'intlShippingAmount') {
        return this.dictList.intlShippingType.find((item) => Number(item.value) === Number(this.rowDetail.intlShippingType))?.name || ''
      }
      if (prop === 'customsFeeAmount') {
        return this.dictList.customsFeeType.find((item) => Number(item.value) === Number(this.rowDetail.customsFeeType))?.name || ''
      }
      return ''
    },
    notSupplier (name) {
      return !/供应商/.test(name)
    }
  }
}
</script>
<style scoped>
.row-item{
  margin-bottom: 10px;
}
</style>
