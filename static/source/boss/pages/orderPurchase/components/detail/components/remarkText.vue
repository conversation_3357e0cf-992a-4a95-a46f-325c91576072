<template>
  <div class="remark-text">
    <el-form label-width="110px" label-suffix=":">
      <el-row v-for="item in getColumns" :key="item.name">
        <el-form-item :label="item.name" prop="orderNo">
          <el-input type="textarea" :auto-size="{minRows:4, maxRows: 10}"
            :value="productInfo[item.prop]" disabled/>
        </el-form-item>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
export default {
  name: 'remarkText',
  inject: ['getProductInfo'],
  props: ['finalFields'],
  computed: {
    productInfo () {
      return this.getProductInfo()
    },
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'remark' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    }
  },
  data () {
    return {}
  },
  methods: {
    handleClick () {}
  },
  created () {}
}
</script>
