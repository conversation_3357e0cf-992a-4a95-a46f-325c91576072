<template>
  <el-form label-width="120px">
    <el-row>
      <el-col class="row-item" v-for="item in getColumns" :key="item.name" :span="item.span || 12">
        <span :label="labelColon(item.name)" :prop="item.prop">
          <span>{{labelColon(item.name)}}</span>
          <span v-if="item.prop == 'materialGroupName'">{{formatString(3, rowDetail.materialGroupNum, rowDetail[item.prop])}}</span>
          <span v-else-if="item.prop==='sundryAmount' || item.prop==='rebateAmount'">{{ rowDetail.poItemExtend[item.prop] }}</span>
          <span v-else>{{rowDetail[item.prop]}}</span>
        </span>
      </el-col>
    </el-row>
    <el-row>
      <div style="margin: 0px;">
        <span>订单单位 - 价格单位：</span>
        <span>
          <el-input disabled size="mini" style="width: 160px" type="text" v-model="rowDetail.priceUnitMole">
            <template slot="append" >
              <span class="append-span-text">{{this.findUnitName(rowDetail.priceUnit)}}</span>
            </template>
          </el-input>
          <span style="margin-left: 10px;margin-right: 10px;"> - </span>
          <el-input disabled size="mini" style="width: 160px" type="text" v-model="rowDetail.priceUnitDeno">
            <template slot="append" >
              <span class="append-span-text">{{this.findUnitName(rowDetail.priceUnit)}}</span>
            </template>
          </el-input>
        </span>
      </div>
      <div style="margin-top: 10px;">
        <span>库存单位 - 订单单位：</span>
        <span>
          <el-input disabled size="mini" style="width: 160px" type="text" v-model="rowDetail.inventoryUnitMole">
            <template slot="append" >
              <span class="append-span-text">{{this.findUnitName(rowDetail.inventoryUnit)}}</span>
            </template>
          </el-input>
          <span style="margin-left: 10px;margin-right: 10px;"> - </span>
          <el-input disabled size="mini" style="width: 160px" type="text" v-model="rowDetail.inventoryUnitDeno">
            <template slot="append" >
              <span class="append-span-text">{{this.findUnitName(rowDetail.unit)}}</span>
            </template>
          </el-input>
        </span>
      </div>
    </el-row>
    <el-row style="margin-top:10px">
      <el-col v-if="showInvoice" :offset="4" :span="8" >
        <el-checkbox :value="Boolean(rowDetail.isLastInvoice)" disabled>
        </el-checkbox>
        不收发票
      </el-col>
      <el-col v-if="productInfo.orderType === 'Z004'" class="row-item" :span="12">
        <span>
          <span>{{ labelColon('OA流程编号') }}</span>
          <span>{{ rowDetail.oaNo || '' }}</span>
        </span>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import { labelColon, formatString } from '@/utils/mm'
import { safeRun } from '@/utils/index'
import { mapState } from 'vuex'
export default {
  name: 'PDAsset',
  props: {
    rowDetail: Object,
    productInfo: Object,
    finalFields: Array
  },
  data () {
    return {}
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    showInvoice () {
      return this.productInfo.orderType !== 'Z003'
    },
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'detailInfo' && (/detail/.test(item.status) || !item.status))
          .concat([
              { 'id': 6061, 'name': '商品行杂费', 'prop': 'sundryAmount', 'type': 'text', 'width': 0, 'span': 0, 'category': 'detailInfo', 'precision': null, 'length': null, 'required': true, 'enums': null, 'disabled': false, 'sequence': 322, 'status': null },
              { 'id': 6062, 'name': '商品行返利', 'prop': 'rebateAmount', 'type': 'text', 'width': 0, 'span': 0, 'category': 'detailInfo', 'precision': null, 'length': null, 'required': true, 'enums': null, 'disabled': false, 'sequence': 324, 'status': null }
          ])
          .sort((a, b) => a.sequence - b.sequence)
      })
      return ret
    }
  },
  methods: {
    labelColon,
    formatString,
    findUnitName (value) {
      const unitList = this.dictList['orderUnit']
      if (Array.isArray(unitList) && unitList.length) {
        const item = unitList.find(item => item.value === value)
        if (item && item.name) {
          value = item.name
        }
      }
      return value
    }
  }
}
</script>
<style lang="scss" scoped>
.row-item{
  margin-bottom: 10px;
}
.append-span-text{
  width: 20px;
  display:inline-block;
  text-align:center;
}

</style>
