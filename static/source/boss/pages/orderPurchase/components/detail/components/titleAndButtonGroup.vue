<template>
  <div class="title-button-group">
    <div class="left">
      <span style="font-size: 20px;vertical-align: middle;" class="cut-one-line">
        {{productInfo.companyName ? productInfo.companyName : '' + ' '}}  {{(productInfo.orderType ?'   '+productInfo.orderType + '-':'')}}{{mapKeyToValue('orderType', productInfo.orderType, dictList)}}
      </span>
      <el-tag effect="dark" v-if="productInfo.sapStatus === 0" type="default">待推送</el-tag>
      <el-tag effect="dark" v-if="productInfo.sapStatus === 1" type="success">推送中</el-tag>
      <el-tag effect="dark" v-if="productInfo.sapStatus === 2" type="danger">推送失败</el-tag>
      <el-tag effect="dark" v-if="productInfo.sapStatus === 3" type="success">推送成功</el-tag>
      <el-tag effect="dark" v-if="productInfo.isDirect" type="warning">直发</el-tag>
      <el-tag effect="dark" v-if="isTitleUrgent" type="danger">加急</el-tag>
      <el-tag effect="dark" v-if="productInfo.poExtend && productInfo.poExtend.isTc === 1 && productInfo.poExtend.tcType === 'CUSTOMER'" type="warning">客户TC</el-tag>
      <el-tag effect="dark" v-if="productInfo.poExtend && productInfo.poExtend.isTc === 1 && productInfo.poExtend.tcType === 'SUPPLIER'" type="warning">供应TC</el-tag>

      <el-tooltip v-if="productInfo.showRpaTag && productInfo.message" class="item" effect="dark" placement="top">
        <div slot="content">
          <div v-html="productInfo.message"></div>
        </div>
        <el-tag effect="dark" :type="tagsMap[productInfo.rapTagType]">{{tagsTextMap[productInfo.rapTagType]}}</el-tag>
      </el-tooltip>
      <el-tag v-if="productInfo.showRpaTag && !productInfo.message" effect="dark" :type="tagsMap[productInfo.rapTagType]">{{tagsTextMap[productInfo.rapTagType]}}</el-tag>

      <el-tag effect="dark" v-if="productInfo.punchStatus === 1" type="success">Punch已确认</el-tag>
      <el-tag effect="dark" v-if="productInfo.punchStatus === 0" type="warning">Punch待确认</el-tag>
      <el-tag effect="dark" v-if="productInfo.punchStatus === 4" type="warning">Punch已发货</el-tag>
      <el-tooltip v-if="((productInfo.punchStatus !== null) && (productInfo.punchStatus !== 1) && (productInfo.punchStatus !== 0) && (productInfo.punchStatus !== 4))" class="item" effect="dark" placement="top">
        <div slot="content">
          <div v-html="productInfo.punchResult"></div>
        </div>
        <el-tag effect="dark"  type="danger">Punch失败</el-tag>
      </el-tooltip>
      <el-tooltip v-if="omsStatus && productInfo.omsStatus !== 3" class="item" effect="dark" placement="top">
        <div slot="content" >
          <div v-html="productInfo.omsResult"></div>
        </div>
          <el-tag effect="dark"  type='danger'>货主转移</el-tag>
      </el-tooltip>
      <el-tag  v-if="omsStatus && productInfo.omsStatus === 3" effect="dark"  type='success'>货主转移</el-tag>
      <el-tag effect="dark" v-if="productInfo.escrow" type="default">代管代发</el-tag>
      <el-tag  v-if="productInfo && productInfo.isZkhPickUp === 1 && ['Z001', 'Z005', 'Z006', 'Z013'].includes(productInfo.orderType)" effect="dark"  type='success'>震坤行提货</el-tag>
    </div>
    <div class="right">
      <el-button
        size="mini"
        v-if="productInfo.poExtend && productInfo.poExtend.isTc === 1 && productInfo.poExtend.tcType === 'SUPPLIER'"
        type="danger"
        @click="handleCancelTc"
        :loading="cancelLoading"
      >取消TC中转</el-button>
      <el-dropdown v-if="showOaAditButton || showOAContract" @command="submitAduit">
        <el-button size="mini" type="primary" :loading="loading">
          提交OA审批
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="item.value" v-for="item in dictList['oaType']" :key="item.value">
            <span v-if="item.value === 'PREPAYMENT' && showOaAditButton">{{item.name}}</span>
            <span v-else-if="item.value === 'CONTRACT' && showOAContract">{{item.name}}</span>
            </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <span v-if="getButtonAuth('采购订单列表', 'PO详情_创建内向交货单')">
      <el-tooltip content="直发采购单不可手动创建内向交货单，需在vc上创建送货单并确认收货自动过账" v-if="productInfo && productInfo.isDirect">
        <div>
        <IDOCreateBtn
        :selections="selections"
        disabled />
        </div>
      </el-tooltip>
       <IDOCreateBtn
        v-else
        :selections="selections"
        :disabled="(orderIsDeleted || productInfo.orderType==='Z004' || (productInfo.approveStep !== -1 && productInfo.approveStep !== 100) ||( productInfo && productInfo.isDirect)) ? true : false" />
      </span>
      <el-button size="mini" type="primary" @click="handleRefreshContract">刷新</el-button>
      <el-button size="mini" v-if="getButtonAuth('采购订单列表', 'PO详情_修改')" type="primary" @click="modifyOrder(false)" >修改</el-button>
      <!-- <el-button size="mini" v-if="orderIsDeleted" type="warning" @click="modifyOrder('recovery')">恢复</el-button> -->
      <el-dropdown v-if="getButtonAuth('采购订单列表', 'PO详情_打印')" @command="handleExportContract" :disabled="orderIsDeleted">
        <el-button size="mini" type="primary" :disabled="printDisabled" :loading="printLoading">
          打印
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="a">合同</el-dropdown-item>
          <el-dropdown-item command="b">送货单</el-dropdown-item>
          <el-dropdown-item v-if="!productInfo.isDirect" command="c">合同与送货单</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button size="mini" v-if="getButtonAuth('采购订单列表', 'PO详情_删除')" type="danger" :loading="delLoading" @click="handleDelete" :disabled="orderIsDeleted">删除</el-button>
    </div>
    <delete-dialog
      :show-dialog.sync="showDeleteDialog"
      :poNo='productInfo.orderNo'
      :fileList='productInfo.deleteAttachmentList'
      @getAttachmentList="getAttachmentList"
      />
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { mapKeyToValue, submitErrorHandler } from '@/utils/mm'
import { getButtonAuth } from '@/utils/auth'
import IDOCreateBtn from '@/pages/orderPurchase/components/common/IDOCreateBtn'
import {
  deletePO,
  forceDeletePo,
  deliveryPrint,
  oaAdvancePayment,
  oaContract,
  batchQueryDocs,
  cancelTc
} from '@/api/mm'
import DeleteDialog from '@/pages/orderPurchase/components/common/DeleteDialog'

export default {
  name: 'titleAndButtonGroup',
  inject: ['getProductInfo', 'getApproveStatus'],
  components: { IDOCreateBtn, DeleteDialog },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      orderType: state => state.orderPurchase.dictList.orderType
    }),
    productInfo () {
      return this.getProductInfo()
    },
    approveStatus () {
      return this.getApproveStatus()
    },
    omsStatus() {
      return !!(this.productInfo?.taglibMap?.company_transfer || []).find(item => item.id === 102)
    },
    printDisabled () {
      // 在订单处于整单已删除或一审待审批或二审待审批或审批驳回状态下，打印按钮置灰
      return this.productInfo.isDeleted === 1 || this.productInfo.approveStep === 10 || this.productInfo.approveStep === 20 || this.productInfo.approveStep === 11 || this.productInfo.approveStep === 21
    },
    orderIsDeleted () {
      return this.productInfo.isDeleted === 1
    },
    allLineIsInvoiced () {
      // 所有非删除行都已开票
      const itemList = this.productInfo.itemList.filter(item => !item.isDeleted)
      return itemList.every(item => item.isInvoiced)
    },
    isTitleUrgent () {
      const { itemList } = this.productInfo
      return itemList.filter(item => !item.isDeleted).some(item => item.isUrgent)
    },
    // 展示oa合同审批
    showOAContract() {
      return this.productInfo?.isAttachmentUpload === 1 && this.productInfo?.poAnnexList && this.productInfo?.poAnnexList.length
    },
    showOaAditButton () {
      const paymentList = [
        'KY00', 'KY04', 'KY05', 'KY07', 'KY08', 'KY09', 'KY10', 'KY11', 'KY12'
      ]
      const hasCode = paymentList.find(code => code === this.productInfo.paymentTermCode)
      const moreThan2 = this.productInfo.taxedTotalAmount >= 20000
      if (hasCode && moreThan2) {
        return true
      }
      return false
    }
  },
  data () {
    const selections = []
    const id = this.$route.params.id
    if (id) {
      selections.push({
        orderNo: id
      })
    }
    return {
      loading: false,
      printLoading: false,
      cancelLoading: false,
      delLoading: false,
      selections,
      showDeleteDialog: false,
      tagsMap: {
        '-1': '',
        '0': 'warning',
        '1': 'success',
        '2': 'danger'
      },
      deleteReason: {},
      tagsTextMap: {
        '-1': '商家系统待推送',
        '0': '商家系统下单中',
        '1': '商家系统下单成功',
        '2': '商家系统下单失败'
      }
    }
  },
  methods: {
    mapKeyToValue,
    getButtonAuth,
    getAttachmentList(data) {
      this.deleteReason = data
      const params = {
        orderNo: this.productInfo?.orderNo,
        updateUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
        deleteReason: data.deleteReason,
        attachmentList: data.attachmentList
      }
      this.delLoading = true
      deletePO(params).then(res => {
        if (res) {
          const { code, data, msg } = res
          if (code === 0 && data) {
            const { orderNo } = data
            if (orderNo) {
              this.$message.success('订单删除成功!')
              this.$emit('refresh')
            }
          } else if (code !== 0) {
            if (code === 30301) {
              this.$confirm('<div><p>供应商代管仓已发货完成，不允许取消。</p><p><span style="color: #f00">如果已经线下和坤合协商一致</span>，由坤合强制拦截货物，入供应商代管仓。请点击‘强制修改’即可删除 PO</p></div>', '修改失败', {
                confirmButtonText: '确定',
                distinguishCancelAndClose: true,
                dangerouslyUseHTMLString: true,
                cancelButtonText: '强制修改',
                type: 'warning'
              }).then(() => {}).catch(this.handleForceDelete)
            } else {
              let errMsg = '订单删除失败！<br>'
              submitErrorHandler(errMsg, data, msg)
            }
          }
        }
      }).finally(() => {
        this.delLoading = false
      })
    },
    modifyOrder (recovery) {
      const path = this.$route.path
      const query = { tagName: `${this.$route.params.id}修改` }
      if (recovery === 'recovery') {
        query.recovery = true
      }
      this.$router.push({
        path: `/orderPurchase/edit/${this.$route.params.id}`,
        query
      })
      this.$closeTag(path)
    },
    submitAduit (command) {
      console.log(command)
      const data = {
        operateUser: window.CUR_DATA.user.name,
        poNoList: [ this.productInfo.orderNo ]
      }
      switch (command) {
        // 预付申请单
        case 'PREPAYMENT':
          if (this.approveStatus) {
            this.$message.error('订单审批中，请完成审批后递交预付款申请')
            return
          }
          this.loading = true
          oaAdvancePayment(data)
            .then(res => {
              console.log(res)
              if (res && res.oaNo && res.url) {
                this.$message.success('提交成功，即将跳转OA页面！')
                this.$emit('refresh')
                setTimeout(() => {
                  window.open(res.url)
                }, 600)
              } else {
                // this.$message.error(res || 'OA流程提交失败！')
              }
            })
            .finally(() => {
              this.loading = false
            })
          break;
        // 合同单
        case 'CONTRACT':
          this.loading = true
          oaContract(data)
            .then(res => {
              console.log(res)
              if (res && res.oaNo && res.url) {
                this.$message.success('已成功提交OA预付款申请，即将跳转OA页面！')
                this.$emit('refresh')
                setTimeout(() => {
                  window.open(res.url)
                }, 600)
              } else {
                // this.$message.error(res || 'OA流程提交失败！')
              }
            })
            .finally(() => {
              this.loading = false
            })
          break;
      }
    },
    handleBatchQueryDocs (no) {
      const data = {
        docQueryReq: [{
          attachmentType: 'deliveryNote',
          businessId: no,
          fileType: 'doc'
        }]
      }
      batchQueryDocs(data)
        .then(res => {
          console.log(res)
          if (res?.edList && res?.edList.length > 0) {
            if (res.matchResultUrl) {
              window.open(res.matchResultUrl)
            } else {
              this.$message.error('无对应匹配送货单!')
            }
            res.edList.map(item => {
              window.open(item.url)
            })
          } else {
            this.$message.error('打印出错！')
          }
        })
    },
    handleDeliveryPrint (no) {
      const data = {
        modelType: 4,
        orderNo: no,
        pdf: 1
      }
      deliveryPrint(data)
        .then(res => {
          console.log(res)
          if (res) {
            if (res.matchResultUrl) {
              window.open(res.matchResultUrl)
            } else {
              this.$message.error('无对应匹配送货单，请至模板中心维护对应送货单模板或联系管理员!')
            }
          } else {
            this.$message.error('打印出错！')
          }
        })
    },
    handleExportContract (command) {
      const no = this.productInfo ? this.productInfo.orderNo : ''
      if (command === 'b' && this.productInfo.isDirect) {
        this.printLoading = true
        let msg = ''
        if (this.productInfo.poExtend.tcType === 'CUSTOMER') {
          msg += '客户TC的采购订单，在PMS仅能打印配送至客户实际收货地址的送货单，如需打印给供应商到TC仓的送货单，请至VC打印。<br/>请注意区别使用，避免给供应商提供错误的送货单。<br/>'
        }
        if (this.productInfo?.printNum) {
          msg += `送货时，请按要求附带送货单：此单要求送货单打印份数：${Number(this.productInfo.printNum) + 1}份`
        }
        if (msg) {
          this.$confirm(msg, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            dangerouslyUseHTMLString: true,
            type: 'warning'
          }).then(() => {
            this.productInfo?.deliverySlipTemplate === '1' ? this.handleBatchQueryDocs(no) : this.handleDeliveryPrint(no)
          }).catch(() => {
          });
        } else {
          this.productInfo?.deliverySlipTemplate === '1' ? this.handleBatchQueryDocs(no) : this.handleDeliveryPrint(no)
        }
        this.printLoading = false
        return
      }
      if ((command === 'a' || command === 'c') && this.approveStatus) {
        this.$message.error('订单审批中，请完成审批后打印合同单')
        return
      }
      if (no) {
        window.open(`/internal-api/mm/exportPdf?status=1&type=${command}&no=${no}`, '_blank')
      }
    },
    handleRefreshContract () {
      this.$emit('refresh')
    },
    // 强制删除
    handleForceDelete(action) {
      if (action === 'close') return
      const params = {
        orderNo: this.productInfo?.orderNo,
        updateUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
        deleteReason: this.deleteReason.deleteReason,
        attachmentList: this.deleteReason.attachmentList
      }
      forceDeletePo(params).then(res => {
        if (res) {
          const { code, data, msg } = res
          if (code === 0 && data) {
            const { orderNo } = data
            if (orderNo) {
              this.$message.success('订单删除成功!')
              this.$emit('refresh')
            }
          } else if (code !== 0) {
            let errMsg = '订单删除失败！<br>'
            submitErrorHandler(errMsg, data, msg)
          }
        }
      })
    },
    handleDelete (id) {
      this.showDeleteDialog = true
    },
    async handleCancelTc () {
      this.cancelLoading = true
      let res = await cancelTc({
        orderNo: this.productInfo?.orderNo,
        updateUser: window.CUR_DATA.user && window.CUR_DATA.user.name
      })
      this.cancelLoading = false
      if (res.code === 0) {
        this.$message.success('TC中转取消成功!')
        this.$emit('refresh')
      } else {
        this.$message.error(res.msg)
      }
    }
  },
  created () {}
}
</script>
<style lang="scss" scoped>
.title-button-group{
  margin-top:10px;
  margin-bottom:10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    span {
      margin-right: 5px;
    }
  }
  .right {
    min-width: 450px;
    display: flex;
    justify-content: flex-end;
    button{
      margin: 0 2px;
    }
  }
  .cut-one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
