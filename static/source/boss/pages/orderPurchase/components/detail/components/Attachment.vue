<template>
  <div class="tele-info">
    <div class="supplier-attachmentList">
      <div>
        确认订单相关附件：
      </div>
      <span style="margin-left: 10px;"  key="poAnnexList" v-if="productInfo.supplierContractAttachmentList.length > 0">
        <el-popover
          placement="right"
          title="附件"
          width="400"
          trigger="hover">
            <p
              v-for="(pdf, index) in productInfo.supplierContractAttachmentList"
              :key="index"
              class="pdf-name"
              @click="openUrl(pdf.fileUrl)"
            >
              {{pdf.fileName}}
          </p>
          <span class="link-to-detail" slot="reference">查看附件</span>
        </el-popover>
      </span>
    </div>
    <div class="tips">
      *临时供应商无有效年框且未签署授权书时，需上传确认订单相关附件
    </div>
  </div>
</template>
<script>
export default {
  name: 'Attachment',
  inject: ['getProductInfo'],
  props: ['finalFields'],
  computed: {
    productInfo () {
      return this.getProductInfo()
    }
  },
  data () {
    return {}
  },
  methods: {
    openUrl(path) {
      let url = path
      try {
        if (url.indexOf('https') === -1) {
          url = 'https' + url.replace(/http(s)?/, '')
        }
      } catch (err) {
        url = path
      }
      window.open(url);
    },
    attachmentIsPic(path) {
      const reg = /\.(bmp|jpg|gif|jpeg|png)\b/i;
      let ret = false;
      if (reg.test(path)) {
        ret = true;
      }
      return ret;
    }
  },
  created () {}
}
</script>
<style lang="scss" scoped >
.supplier-attachmentList{
  display: flex;
}
.link-to-detail{
  color: #597bee;
  cursor: pointer;
}
.pdf-name {
  color: #597bee;cursor: pointer;margin-bottom: 5px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
}
.tips {
  color: red;
  font-size: 10px;
}
</style>
