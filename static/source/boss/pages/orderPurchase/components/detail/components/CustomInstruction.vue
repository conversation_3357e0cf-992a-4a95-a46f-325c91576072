<template>
  <div class="custom-instruction">
    <el-row v-for="(item, index) in customInstructions" :key="index">
      <el-col :span="24">
        <span class="property">{{item.customProperty}}:</span>
        <span class="mark">{{item.customPropertyRemark}}</span>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import { safeRun } from '@/utils/index'
import { formatString } from '@/utils/mm'

export default {
  name: 'CustomInstruction',
   props: {
    rowDetail: Object
  },
  computed: {
    customInstructions() {
      let customInstructions = []
      safeRun(() => {
        customInstructions = JSON.parse(this.rowDetail?.customInstructions)
      })
      return customInstructions
    }
  },
  methods: {
    formatString
  }
}
</script>
<style lang="scss" scoped>
.custom-instruction{
  line-height: 30px;
  .mark {
    padding-left: 10px;
  }
  .property {
    font-weight: 600;
  }
}
</style>
<style lang="scss">
.clickable{
  margin-left: 10px;
  cursor: pointer;
  &:hover{
    opacity: 0.7;
  }
  &:active{
    opacity: 0.9;
  }
}
</style>
