<template>
  <el-table :data="rowDetail.planList" border>
    <el-table-column v-for="col in getColumns" align="center"
      :key="col.name" :prop="col.prop" :label="col.name" :width="col.width">
      <template slot-scope="{ row }">
        <el-button v-if="col.name === '操作'" type="text" @click="delRow(row)">
          {{detailStatus() === 'readonly' ? '' : '删除'}}
        </el-button>
        <span v-else>{{row[col.prop]}}</span>
      </template>
    </el-table-column>
  </el-table>
</template>
<script>
import { detailStatus } from '@/utils/mm'
import { safeRun } from '@/utils/index'
export default {
  name: 'PDPlan',
  props: {
    rowDetail: Object,
    finalFields: Array
  },
  computed: {
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'detailPlan' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    }
  },
  data () {
    return {}
  },
  methods: {
    detailStatus,
    delRow (row) {
      console.log(row)
    }
  }
}
</script>
