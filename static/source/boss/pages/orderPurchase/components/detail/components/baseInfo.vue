<template>
  <div class="base-info">
    <el-form label-width="130px" label-position="right" label-suffix=":">
      <el-row v-for="(item, index) in [1,2,3,4,5,6,7]" :key="index" :gutter="10">
        <el-col class="row-item cut-one-line" v-for="item in renderLine(item)" :span="item.span || 8" :key="item.name">
          <span class="label" v-if="item.prop === 'oaList' && productInfo[item.prop]">{{item.name}}：</span>
          <span class="label" v-else-if="item.prop !== 'oaList'">{{item.name}}：</span>
          <span class="content">
            <span v-if="item.prop === 'supplierName'" :title="formatString(3, productInfo.supplierNo, productInfo.supplierName)"  style="white-space: normal">
              <el-tooltip class="item" effect="dark" :content="supplierTooltip" placement="top" v-if="supplierTooltip">
                <span style="color:#409eff;" class="cut-one-line">{{formatString(3, productInfo.supplierNo, productInfo.supplierName)}}</span>
              </el-tooltip>
              <span v-else>{{formatString(3, productInfo.supplierNo, productInfo.supplierName)}}</span>
              <el-button v-if="productInfo.showRpaTag" class="mini-button" size="mini" @click="pushRpaData" type="primary">
              RPA推送
              </el-button>
            </span>
            <span v-else-if="item.prop === 'customerName'">
              {{((productInfo.customerCode || '') + ' ' + (productInfo.customerName || ''))}}
            </span>
            <span v-else-if="item.prop === 'purchaseGroup'">
              {{handlePurchaseGroup(productInfo[item.prop])}}
            </span>
            <span v-else-if="item.prop === 'lossRatio'">
              {{productInfo.lossRatio && (productInfo.lossRatio + ' %')}}
            </span>
            <span v-else-if="item.prop === 'sapOrderNo'">
              {{productInfo[item.prop]}}
              <el-button class="mini-button" size="mini" @click="resendSap" type="primary">
              推送S4/坤合
              </el-button>
            </span>
            <span v-else-if="item.prop === 'taglibIdList' && productInfo.taglibMap && productInfo.taglibMap['require_type']">
              <span v-for="item in productInfo.taglibMap['require_type']" :key="item.id">
                <span >{{item.tagDesc}} &nbsp;&nbsp;</span>
              </span>
            </span>
            <span v-else-if="item.prop === 'strategicStock' && productInfo.taglibMap && productInfo.taglibMap['require_type']">
              <span v-for="item in productInfo.taglibMap['require_type']" :key="item.id">
                <span >{{item.tagDesc}} &nbsp;&nbsp;</span>
              </span>
            </span>
            <span v-else-if="item.prop === 'oaList' && productInfo[item.prop]">
                <span class="content">
                  <span v-if="productInfo.oaList.length > 0 && productInfo.oaList[0].oaNo" class="oaNo">
                    <a target="_blank" :href="productInfo.oaList[0].oaUrl">{{productInfo.oaList[0].oaNo || '点击跳转'}}</a>
                  </span>
                  <span v-if="productInfo.oaList.length > 1 && productInfo.oaList[1].oaNo" class="oaNo">
                    <a target="_blank" :href="productInfo.oaList[1].oaUrl">{{productInfo.oaList[1].oaNo || '点击跳转'}}</a>
                  </span>
                  <el-popover
                  placement="right"
                  title="OA单号"
                  width="400"
                  trigger="hover">
                  <el-table :data="productInfo.oaList" >
                    <el-table-column label="流程类型" align="center" >
                      <template slot-scope="scope">
                        {{((dictList.oaType||[]).find(item => item.value === scope.row.oaType) || {}).name}}
                      </template>
                    </el-table-column>
                    <el-table-column label="OA单号" align="center">
                      <template slot-scope="scope">
                        <a target="_blank" :href="scope.row.oaUrl">{{scope.row.oaNo || '点击跳转'}}</a>
                      </template>
                    </el-table-column>
                  </el-table>
                  <span class="link-to-detail" slot="reference">查看OA</span>
                </el-popover>
                </span>
            </span>
            <CutParagraph v-else-if="item.prop === 'sapResult'" splitSapMsg :content="productInfo.sapResult" :status="productInfo.sapStatus" :maxLen="100" />
            <span v-else-if="item.prop === 'approveStep'">
            <el-tooltip class="item" effect="dark"  placement="top" v-if="productInfo.approveReason">
               <div slot="content">
                 <span v-html="`审批原因： ${productInfo.approveReason}`"></span></div>
                <span > {{mapKeyToValue(item.enums, productInfo[item.prop], dictList)}} </span>
              </el-tooltip>
            <span v-else> {{mapKeyToValue(item.enums, productInfo[item.prop], dictList)}} </span>
            </span>
            <span v-else-if="item.prop === 'paymentTermCode'">
              {{ mapKeyToValue(item.enums, productInfo[item.prop], dictList) }}
              <el-tag v-if="productInfo.isPrepayment">预付款订单</el-tag>
              <el-tag style="margin-left: 2px" v-if="productInfo.poExtend.paymentTermCodeSourceName">{{ productInfo.poExtend.paymentTermCodeSourceName }}</el-tag>
            </span>
            <span v-else>
              {{
                item.enums ?
                mapKeyToValue(item.enums, productInfo[item.prop], dictList) :
                productInfo[item.prop]
              }}
            </span>
            <el-button v-if="showAuditBtn(item)" size="mini" @click="showAuditDialog" type="primary">
            {{item.name === '金额审批' ? '审批金额' : '审批'}}
            </el-button>
          </span>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" v-if="renderCheckbox().length">
          <el-row>
            <el-col v-for="item in renderCheckbox()" :span="hasAttach(item, productInfo) ? item.span + 2 : item.span || 3" :key="item.name">
              <el-checkbox :indeterminate="isIndeterminate(item.prop)" disabled :value="Boolean(handleCheckboxProp(item.prop, productInfo[item.prop]))">
                {{item.name}}
              </el-checkbox>
              <span style="margin-left: 10px;" v-if="hasAttach(item, productInfo)" key="poAnnexList">
                <el-popover
                  placement="right"
                  title="附件"
                  width="200"
                  trigger="hover">
                  <div>
                    <p
                      v-for="(pdf, index) in productInfo[`${item.prop === 'isAttachmentUpload' ? 'poAnnexList' : 'deleteAttachmentList'}`]"
                      :key="index"
                      style="color: #597bee;cursor: pointer;"
                      @click="openUrl(item.prop === 'isAttachmentUpload' ? pdf.annexUrl : pdf.fileUrl)"
                    >
                      {{item.prop === 'isAttachmentUpload' ? pdf.purchaseOrderId + "." + pdf.suffix : pdf.fileName}}
                    </p>
                  </div>
                  <span class="link-to-detail" slot="reference">查看附件</span>
                </el-popover>
              </span>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-form>
    <el-dialog :visible.sync="showAudit" title="审核订单" width="400px" :before-close="handleClose" :close-on-click-modal="false">
      <div class="container">
        <div class="body">
          <div style="width: 90%;text-laign:left; margin-bottom: 20px; margin-top: -10px;">
            <p>供应商：<span style="font-weight: bold">{{productInfo.supplierName}}</span></p>
            <p style="margin-top: 5px;" v-if="productInfo.approveReason && productInfo.approveReason.indexOf('金额') > -1">
              <span>审批金额：<span style="font-weight: bold">{{productInfo.approveAmount}}</span></span>
              <span style="margin-left: 30px;">订单含税总额：<span style="font-weight: bold">{{productInfo.taxedTotalAmount}}</span></span>
            </p>
          </div>
          <el-radio-group v-model="auditStatus">
            <el-radio label="1">审批通过</el-radio>
            <el-radio label="0">审批不通过</el-radio>
          </el-radio-group>
          <div class="text">
            <span>备注：</span>
            <el-input type="textarea" placeholder="若审批不通过，请备注说明" v-model="approveRemark" :autosize="{ minRows: 2, maxRows: 6}"/>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="mini" type="primary" @click="submitAudit">确认</el-button>
          <el-button size="mini" @click="showAudit = false">取 消</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { mapKeyToValue, formatString, buildOptions } from '@/utils/mm'
import { resendSap, resendMQ, approve } from '@/api/mm'
import { pushRpaData } from '@/api/honeycomb'
import CutParagraph from '@/components/mm/CutParagraph'
import { safeRun } from '@/utils/index'
import { getButtonAuth } from '@/utils/auth'
export default {
  name: 'baseInfo',
  inject: ['getProductInfo', 'updateDetail'],
  props: ['finalFields', 'updateLoading', 'detailLoading'],
  components: {
    CutParagraph
  },
  data () {
    return {
      showAudit: false,
      auditStatus: '1',
      approveRemark: ''
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      purchaseList: state => state.orderPurchase.purchaseList
    }),
    productInfo () {
      return this.getProductInfo()
    },
    getColumns () {
      let ret = []
      safeRun(() => {
        ret = this.finalFields
          .filter(item => item.category === 'baseInfo' && (/detail/.test(item.status) || !item.status))
      })
      return ret
    },
    supplierTooltip () {
      const { supplierClassify, ifSignFrameworkAgreement } = this.productInfo.supplier || {}
      const supplierOptions = buildOptions('supplierClassify')
      const yesOrNoOptions = buildOptions('yesOrNo')
      // eslint-disable-next-line eqeqeq
      const findSupplier = supplierOptions.find(item => item.value == supplierClassify) || {}
      // eslint-disable-next-line eqeqeq
      const findIfSign = yesOrNoOptions.find(item => item.value == ifSignFrameworkAgreement) || {}
      let str = ''
      if (findSupplier && findSupplier.name) {
        str = `供应商分类:${findSupplier.name} `
      }
      if (findIfSign && findIfSign.name) {
        str += `签订框架协议:${findIfSign.name}`
      }
      return str
    },
    formatOaAdvancePayment () {
      let ret = []
      const { oaAdvancePaymentNo, oaAdvancePaymentUrl } = this.productInfo
      if (oaAdvancePaymentNo && oaAdvancePaymentUrl) {
        const nos = oaAdvancePaymentNo.split(',');
        const urls = oaAdvancePaymentUrl.split(',');
        nos.forEach((no, index) => {
          ret.push({
            orderNo: nos[index],
            url: urls[index]
          })
        })
      }
      return ret
    }
  },
  methods: {
    formatString,
    getButtonAuth,
    mapKeyToValue,
    openUrl(url) {
      window.open(url)
    },
    hasAttach(item, productInfo) {
      // eslint-disable-next-line eqeqeq
      const isAttachmentUpload = item.prop === 'isAttachmentUpload' && productInfo[item.prop] == 1 && productInfo.poAnnexList && productInfo.poAnnexList.length
      const deleteAttachmentList = item.prop === 'deleteAttachmentList' && productInfo?.deleteAttachmentList && productInfo?.deleteAttachmentList?.length
      return isAttachmentUpload || deleteAttachmentList
    },
    handleCheckboxProp (prop, value) {
      if (prop === 'deleteAttachmentList') return value?.length > 0
      if (prop === 'ownerTransfer') return !!(this.productInfo?.taglibMap?.company_transfer || []).find(item => item.id === 102)
      if (prop === 'strategicStock') return !!(this.productInfo?.taglibMap?.require_type || []).find(item => item.id === 101)
      if (prop === 'isDesignatedChannel') return !!this.productInfo?.poExtend?.isDesignatedChannel
      if (prop !== 'followSignBack') return value
      const { supplierClassify, ifSignFrameworkAgreement } = this.productInfo.supplier || {}
      // 临时供应商 且 签订框架协议为否
      // eslint-disable-next-line eqeqeq
      if (supplierClassify == 3 && ifSignFrameworkAgreement == 0) {
        return true
      }
      return false
    },
    findValueInOptions (value, options) {},
    showAuditBtn (item) {
      return (item.name === '审批状态' || item.name === '金额审批') &&
        (this.productInfo[item.prop] === 10 || this.productInfo[item.prop] === 20) &&
          getButtonAuth('采购订单列表', 'PO详情_审批')
    },
    renderLine(idx) {
      return this.getColumns.filter(col => col.type !== 'checkbox').slice((idx - 1) * 3, idx * 3)
    },
    renderCheckbox() {
      return this.getColumns.filter(col => col.type === 'checkbox')
    },
    isIndeterminate (prop) {
      // 交期确认中间状态
      if (prop === 'isDeliveryTimeConfirm') {
        return this.productInfo.isDeliveryTimeConfirm === 2
      }
      // 是否急单确认中间状态
      if (prop === 'isUrgent') {
        let ret = false
        let { itemList } = this.productInfo
        itemList = itemList.filter(item => !item.isDeleted)
        if (!itemList.every(item => item.isUrgent) &&
          !itemList.every(item => !item.isUrgent)
        ) {
          ret = true
        }
        return ret
      }
      return false
    },
    handlePurchaseGroup (val) {
      let ret = val
      let item = this.purchaseList.filter(item => item.groupCode === val)
      if (item && item[0] && item[0].userName) {
        ret += '  ' + item[0].userName
      }
      return ret
    },
    async pushRpaData () {
      this.updateLoading(true)
      const data = {
        supplierNo: this.productInfo.supplierNo,
        orderId: this.productInfo.orderNo
      }
      const response = await pushRpaData(data)
      console.log(response)
      this.updateDetail('noOtherTab')
    },
    async resendSap () {
      this.updateLoading(true)
      const data = new FormData()
      safeRun(() => {
        data.append('orderNo', this.productInfo.orderNo)
        data.append('updateUser', window.CUR_DATA.user.name)
      })
      await Promise.all([resendSap(data), resendMQ(data)])
      this.updateDetail('noOtherTab')
    },
    submitAudit () {
      if (this.auditStatus === '0' && !this.approveRemark) {
        return this.$message.error('审批不通过请输入备注！')
      }
      const data = {
        approveRemark: this.approveRemark,
        action: this.auditStatus === '1' ? 'P' : 'R',
        orderNo: this.productInfo.orderNo,
        approveUser: window.CUR_DATA.user.name,
        currentStep: this.productInfo.approveStep
      }
      this.handleClose()
      approve(data)
        .then(res => {
          const { code, data, msg } = res
          let message = msg
          if (code === 0 && data === null) {
            message = '审批成功！'
          }
          this.$alert(message, '操作提示', {
            type: code === 0 ? 'success' : 'error',
            confirmButtonText: '确定',
            callback: action => {
              this.updateDetail()
            }
          })
        })
        .catch(err => {
          this.$alert(err.message || err.msg || '审批失败！', '操作提示', {
            type: 'error',
            confirmButtonText: '确定',
            callback: action => {
              this.updateDetail()
            }
          })
        })
    },
    handleClose (done) {
      done && done()
      this.auditStatus = '1'
      this.approveRemark = ''
      this.showAudit = false
    },
    showAuditDialog () {
      this.showAudit = true
    },
    handleClick () {}
  }
}
</script>
<style lang="scss" scoped>
.base-info{
  .mini-button{
    padding: 1px 5px;
    height: 22px;
  }
  .row-item{
    margin-bottom: 10px;
  }
  .oaNo{
    padding-right: 10px;
  }
  .cut-two-line {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .cut-one-line {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .word-no-wrap {
    white-space: nowrap;
  }
  .container{
    .body{
      display: flex;
      align-items: center;
      flex-direction: column;
    }
    .text{
      display: flex;
      margin: 10px;
      width: 90%;
      span{
        white-space:nowrap
      }
    }
    .dialog-footer{
      display: flex;
      justify-content: flex-end;
    }
  }
  .link-to-detail{
    color: #597bee;
    cursor: pointer;
  }
}
</style>
