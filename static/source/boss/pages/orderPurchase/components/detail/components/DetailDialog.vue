<template>
  <el-dialog
    :visible="true"
    title="商品详情" width="800px"
    :before-close="handleClose">
    <div class="container">
      <div class="body">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="详细信息" name="DetailInfo" />
          <el-tab-pane label="计划行" name="DetailPlan" />
          <el-tab-pane v-if="showCharge && !hideByAuth" label="价费信息" name="DetailCharge" />
          <!-- Z001 没有委外组件与固定资产详情 -->
          <el-tab-pane v-if="showComp" label="委外组件" name="DetailEntrust" />
          <el-tab-pane v-if="showAsset" label="固定资产详情" name="DetailAssets" />
          <el-tab-pane label="定制说明" name="CustomInstruction" v-if="rowDetail && rowDetail.customInstructions" />
        </el-tabs>
        <component :is="activeName" :finalFields="finalFields" :rowDetail="rowDetail" :productInfo="productInfo"></component>
      </div>
      <div slot="footer" class="dialog-footer">
        <div v-if="detailStatus() === 'readonly'">
          <el-button @click="closeDialog">关闭</el-button>
        </div>
        <div v-if="detailStatus() === 'edit'">
          <el-button type="primary" @click="submitDetail">确认保存</el-button>
          <el-button @click="closeDialog">取 消</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
// DetailDialog
import { detailStatus } from '@/utils/mm'
import { findTopRole } from '@/utils/auth'
import { mapState } from 'vuex'

import DetailInfo from './DetailInfo'
import DetailPlan from './DetailPlan'
import DetailCharge from './DetailCharge'
import DetailEntrust from './DetailEntrust'
import DetailAssets from './DetailAssets'
import CustomInstruction from './CustomInstruction'
export default {
  name: 'DetailDialog',
  components: {
    DetailInfo,
    DetailPlan,
    DetailCharge,
    DetailEntrust,
    DetailAssets,
    CustomInstruction
  },
  props: {
    detailDialogShow: Boolean,
    showCharge: Boolean,
    rowDetail: Object,
    finalFields: Array
  },
  inject: ['getProductInfo'],
  computed: {
    productInfo () {
      return this.getProductInfo()
    },
    showComp () {
      // Z006-委外采购订单 显示委外组件
      return this.productInfo.orderType === 'Z006'
    },
    showAsset () {
      // Z007-固定资产采购订单 显示固定资产组件
      return this.productInfo.orderType === 'Z007' || this.productInfo.orderType === 'Z004'
    },
    ...mapState({
      userRole: state => state.userRole
    }),
    hideByAuth () {
      return findTopRole(this.userRole) === 'PMS仓管'
    }
  },
  data () {
    return {
      activeName: 'DetailInfo'
    }
  },
  created () {},
  methods: {
    detailStatus,
    handleClose (done) {
      this.$emit('close-dialog')
      done && done()
    },
    submitDetail () {},
    handleClick () {},
    closeDialog () {
      this.handleClose()
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer{
  margin: 20px 0px 0px;
  display: flex;
  justify-content: center;
}
</style>
