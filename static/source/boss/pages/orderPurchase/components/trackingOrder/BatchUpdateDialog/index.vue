<template>
  <el-dialog
    title="批量更新跟单"
    :visible="batchUpdateVisible"
    width="600px"
    :before-close="beforeClose"
  >
    <el-row>
      <div class="desc">
        <span class="strong">可修改字段：</span>
        包括采购订单号、项目行、计划行、计划交货日期、订单交期确认标志、急单标识、客户接受供应商交期、备注文本、申请下单原因、指定渠道打标、无法确认订单原因、交期变更原因
      </div>
    </el-row>
    <el-row style="margin-bottom:10px;display:flex;justify-content:space-around;">
      <el-button class="batch-btn" @click="handleDownloadTempl" type="primary" plain>下载模板</el-button>
      <Upload @uploadSuccess="uploadSuccess" :updateVisible="updateVisible" :type="type"/>
    </el-row>
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'
import Upload from './upload'

export default {
  props: {
    batchUpdateVisible: {
      required: true,
      type: Boolean,
      default: false
    },
    updateVisible: {
      type: Function,
      default: () => {}
    },
    type: {
      type: String,
      default: () => '0'
    }
  },
  components: {
    Upload
  },
  data () {
    return {
      // templateUrl: 'https://static.zkh360.com/file/2024-07-11/0710-%E6%89%B9%E9%87%8F%E8%B7%9F%E5%8D%95%E6%A8%A1%E6%9D%BFV22-1720681521050.xlsx'
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      poExcelUrls: state => state.orderCommon.poExcelUrls || {}
    })
  },
  methods: {
    uploadSuccess () {
      this.updateVisible('batchUpdateVisible', false)
      this.$emit('uploadSuccess')
    },
    beforeClose (done) {
      this.updateVisible('batchUpdateVisible', false)
      done()
    },
    handleDownloadTempl () {
      window.open(this.poExcelUrls.trackOrderBatchTemplate)
    }
  }
}
</script>

<style lang="scss" scoped>
.desc {
  margin: 10px 0;
  .strong {
    font-weight: bold;
  }
}
.batch-btn {
  width: 120px;
}
</style>
