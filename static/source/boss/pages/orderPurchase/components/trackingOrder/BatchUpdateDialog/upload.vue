<template>
  <el-upload
    style="display:inline-block;margin-left: 20px;"
    accept=".xlsx,.xls,.xlsm"
    :action="uploadAction"
    :show-file-list="false"
    :on-success="handleSuccess"
    :on-error="handleError"
    :before-upload="handleBeforeUpload"
  >
    <el-button class="batch-btn" type="primary" >批量更新跟单</el-button>
  </el-upload>
</template>

<script>
import { submitErrorHandler } from '@/utils/mm'
export default {
  inject: ['setDataResult'],
  props: ['updateVisible', 'type'],
  computed: {
    uploadAction () {
      console.log('this.type', this.type)
      return `/internal-api/mm/batchEditTrackingOrder?type=${this.type}`
    }
  },
  methods: {
    handleSuccess (res) {
      console.log(res)
      // res = {
      //   code: 0,
      //   msg: '123',
      //   data: {
      //     successCount: 1,
      //     failCount: 0,
      //     detailList: [
      //       {
      //         poNo: '12312',
      //         itemNo: '12314',
      //         planNo: '阿士大夫全文',
      //         message: '艾杜纱所多恶趣味若'
      //       }
      //     ]
      //   }
      // }
      if (this.loading) {
        this.loading.close()
      }
      this.updateVisible('batchUpdateVisible', false)
      let { code, data, msg } = res
      if (code === 0 && data) {
        if (typeof data === 'string') {
          this.$alert(data, '操作提示', {
            confirmButtonText: '确认',
            type: 'success'
          }).then(() => {
            this.$emit('uploadSuccess')
          })
          return
        }
        this.setDataResult(data)
      } else {
        this.$emit('uploadSuccess')
        submitErrorHandler('', data, msg)
      }
    },
    handleBeforeUpload (file) {
      if (!this.$validateFileType(file)) return false

      this.loading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleError () {
      if (this.loading) {
        this.loading.close()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.desc {
  margin: 10px 0;
  .strong {
    font-weight: bold;
  }
}
.batch-btn {
  width: 120px;
}
</style>
