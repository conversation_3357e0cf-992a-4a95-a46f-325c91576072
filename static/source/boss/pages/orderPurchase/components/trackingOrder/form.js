const forms = [
  { name: '工厂', prop: 'factoryCodeList', type: 'select', enums: 'factoryCodeList', multiple: true, required: true, span: 12 },
  { name: '采购员', prop: 'purchaseGroupList', type: 'select', enums: 'purchaseGroupList', multiple: true, required: false, span: 6 },
  { name: '订单创建日期', prop: 'orderDate', type: 'date-picker', required: false, span: 6 },

  { name: '采购订单', prop: 'orderNoList', type: 'input', placeholder: '采购单号 或 SAP订单号 或 协议单号 均支持搜索，同时支持200个单号，按空格或换行分隔', required: false, span: 12 },
  { name: '供应商订单号', prop: 'supplierOrderNoList', type: 'input', placeholder: '同时支持200个单号，按空格或换行分隔', required: false, span: 6 },
  { name: '订单类型', prop: 'orderTypeList', type: 'select', enums: 'orderType', multiple: true, required: false, span: 6 },
  { name: '供应商', prop: 'supplierNoList', type: 'custom', multiple: true, required: false, span: 6 },
  { name: '审批状态', prop: 'approveStep', type: 'select', enums: 'approveStatus', clearable: true, hideCode: true, required: false, span: 6 },
  { name: 'SKU编码', prop: 'skuNoList', type: 'input', placeholder: '多个SKU编码按空格或换行分隔', required: false, span: 6 },
  { name: '仓库地点', prop: 'warehouseLocationList', type: 'select', enums: 'warehouseLocationList', multiple: true, required: false, span: 6 },
  { name: '发货仓库地点', prop: 'shipWarehouseLocationList', type: 'select', enums: 'warehouseLocationList', multiple: true, required: false, span: 6 },

  { name: '跟踪单号', prop: 'trackNo', type: 'input', required: false, span: 6 },
  { name: '供应商物料号', prop: 'supplierMaterialNo', type: 'input', required: false, span: 6 },
  { name: '物料组', prop: 'materialGroupNumList', type: 'custom', enums: 'materialGroupNumList', multiple: true, required: false, span: 6 },
  { name: '订单范围', prop: 'orderScope', type: 'select', enums: 'reportOrderRange', clearable: true, hideCode: true, required: false, span: 6 },

  { name: '品牌', prop: 'brandNameList', type: 'custom', multiple: true, required: false, span: 6 },
  { name: '付款条件', prop: 'paymentTermCodeList', type: 'select', enums: 'paymentTermCode', multiple: true, required: false, span: 6 },
  { name: '已删除', prop: 'isDeleted', type: 'select', enums: 'isDeleted', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '展示SO(客服)', prop: 'isQueryDemanderCustomerService', type: 'select', enums: 'isQueryDemanderCustomerService', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '红黄绿标识', prop: 'lightStatusList', type: 'select', enums: 'reportLightStatus', hideCode: true, clearable: true, multiple: true, required: false, span: 6 },
  { name: '非第一推优', prop: 'notFirstSupplier', type: 'select', enums: 'isDeleted', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '代管代发', prop: 'isEscrow', type: 'select', enums: 'isDeleted', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '商品定位', prop: 'productPosition', type: 'select', enums: 'productPosition', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '展示预付款', prop: 'prePayment', type: 'select', enums: 'prePayment', hideCode: true, clearable: true, required: false, span: 6 },
  { name: 'TC中转', prop: 'isTc', type: 'select', enums: 'isDeleted', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '交期变更原因', prop: 'deliveryChangeReason', type: 'select', enums: 'deliveryChangeReason', hideCode: true, clearable: true, required: false, span: 6 },
  { name: '无法确认订单原因', prop: 'poUnconfirmedReason', type: 'select', enums: 'poUnconfirmedReason', hideCode: true, clearable: true, required: false, span: 6 },
  { name: 'OA流程编号', prop: 'oaNoList', type: 'input', placeholder: '同时支持20个单号，按空格或换行分隔', required: false, span: 6 }
]

/**
 * 生成form校验rules
 * @param {*} forms
 * @returns form rules
 */
const fillRules = (forms) => {
  let rules = {}
  forms.forEach(rule => {
    if (rule.required) {
      rules[rule.prop] = { required: true, message: `请选择${rule.name}！`, trigger: ['change', 'blur'] }
    }
  })
  return rules
}
const defaultSpan = 6
const arraySumEqual = (array, val) => {
  let acc = array.reduce((prev, next) => {
    if (next) { prev += parseInt(next.span || defaultSpan) }
    return prev
  }, 0)
  return acc >= val
}

const fillArray = (array, data) => {
  const len = array.length ? array.length - 1 : 0
  if (!Array.isArray(array[len])) array[len] = []
  if (!arraySumEqual(array[len], 24)) {
    array[len].push(data)
  } else {
    array[len + 1] = [data]
  }
}

const sortRows = (forms) => {
  let array = []
  for (let form of forms) {
    fillArray(array, form)
  }
  window._console.red(array)
  return array
}

export {
  forms,
  fillRules,
  sortRows
}
