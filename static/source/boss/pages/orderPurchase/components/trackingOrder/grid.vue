<template>
  <div class="tracking-order-grid-table" ref="girdTable">
    <vxe-grid
      border
      resizable
      keep-source
      show-overflow
      column-key
      :height="gridHeight"
      align="center"
      highlight-hover-row
      ref="trackingOrderGrid"
      show-header-overflow
      size="small"
      id="tracking-order-grid"
      :scroll-x="{gt: -1}"
      :scroll-y="{gt: -1}"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="listData"
      :columns="tableColumns"
      :toolbar-config="tableToolbar"
      @sort-change="handleSortChange"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true, enabled: true}">
      <template v-slot:toolbar_buttons>
        <div class="title-group">
          <span>  </span>
          <span class="button-group">
            <el-button v-if="getButtonAuth('跟单报表', '保存修改')" type="success" @click="saveData">保存修改</el-button>
            <el-button v-if="getButtonAuth('跟单报表', '导出明细')" type="primary" @click="exportDetail">导出明细</el-button>
            <el-button v-if="getButtonAuth('跟单报表', '批量更新跟单')" type="primary" @click="updateVisible('batchUpdateVisible', true)">批量更新跟单</el-button>
          </span>
        </div>
      </template>
      <template #poUnconfirmedReason="{ row }">
        <el-select v-model="row.poUnconfirmedReason" clearable filterable :disabled="disabledByStatus(row) || row.orderType == 'Z003' || row.isConfirmed" @change="val => handleChange(val, 'poUnconfirmedReason', row)" placeholder="请选择">
          <el-option
            v-for="item in buildOptions('poUnconfirmedReason')"
            :key="item.value"
            :label="item.name"
            :value="Number(item.value)">
          </el-option>
        </el-select>
      </template>
      <template #deliveryChangeReason="{ row }">
        <el-select v-model="row.deliveryChangeReason" clearable filterable :disabled="disabledByStatus(row)" @change="val => handleChange(val, 'deliveryChangeReason', row)" placeholder="请选择">
          <el-option
            v-for="item in buildOptions('deliveryChangeReason')"
            :key="item.value"
            :label="item.name"
            :value="Number(item.value)">
          </el-option>
        </el-select>
      </template>
      <template #poUnpassedReason="{ row }">
        <div v-if="row.changeDiff && row.changeDiff.length">
          <el-tooltip trigger="hover" effect="dark">
            <template #content>
              <div v-for="item in row.changeDiff" :key="item.property" style="line-height: 18px;">
                {{ item.desc }}
              </div>
            </template>
            <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ row.changeDiff[0] ? row.changeDiff[0].desc : '' }}
            </div>
          </el-tooltip>
        </div>
      </template>
      <template #attachmentName="{ row }">
        <el-popover trigger="hover">
          <template #reference>
            <div style="word-wrap: break-word;">
              {{ row.attachmentList && row.attachmentList[0].attachmentName }}
            </div>
          </template>
          <div
            v-for="(item, index) in row.attachmentList || []"
            :key="index"
          >
            {{ item.attachmentName }}
          </div>
        </el-popover>
      </template>
      <template #attachmentUrl="{ row }">
        <el-popover trigger="hover">
          <template #reference>
            <el-link
              :href="row.attachmentList && row.attachmentList[0].attachmentUrl"
              :download="row.attachmentList && row.attachmentList[0].attachmentUrl"
              type="primary"
              target="_blank">
              {{ row.attachmentList && row.attachmentList[0].attachmentUrl }}
            </el-link>
          </template>
          <div v-for="(item, index) in row.attachmentList || []" :key="index" class="attachment-name">
            <el-link
              :href="item.attachmentUrl"
              :download="item.attachmentUrl"
              type="primary"
              target="_blank">
              {{ item.attachmentUrl }}
            </el-link>
          </div>
        </el-popover>
      </template>
      <template #earlyWarnPo="{ row }"> {{mapBoolean(row.earlyWarnPo)}} </template>
      <template #poNo="{ row }">
        <el-link @click="toDetail(row)" type="primary">{{row.poNo}}</el-link>
      </template>
      <template #poFollowReportWarn="{ row }"> {{mapValue('poFollowReportWarn', row.warningType)}} </template>
      <template #orderType="{ row }"> {{mapValue('orderType', row.orderType, true)}} </template>

      <template #supplierClassify="{ row }"> {{mapValue('supplierClassify', row.supplierClassify)}} </template>
      <template #yesOrNo="{ row }"> {{mapValue('yesOrNo', row.ifSignFrameworkAgreement)}} </template>
      <template #cloud="{ row }"> {{row.cloud ? '是' : row.cloud === null ? '' : '否'}} </template>
      <template #purchaseGroup="{ row }"> {{mapValue('purchaseGroup', row.purchaseGroup, true)}} </template>
      <template #openVcStatus="{ row }"> {{mapValue('openVcStatus', row.openVcStatus)}} </template>
      <template #cooperateMode="{ row }"> {{mapValue('cooperateMode', row.coopMode)}} </template>
      <template #poContractBack="{ row }"> {{mapValue('poContractBack', row.followContractBack)}} </template>
      <template #paymentTermCode="{ row }"> {{mapValue('paymentTermCode', row.paymentTermCode, true)}} </template>
      <template #approveStatus="{ row }"> {{mapValue('approveStatus', row.approveStep)}} </template>
      <template #orderUnit="{ row }"> {{mapValue('orderUnit', row.unit)}} </template>
      <template #materialGroupName="{ row }"> {{mapValue('materialGroupName', row)}} </template>
      <template #factoryCode="{ row }"> {{mapValue('factoryCode', row.factoryCode, true)}} </template>
      <template #inputTax="{ row }"> {{mapValue('inputTax', row.inputTax, true)}} </template>
      <template #returnReason="{ row }"> {{mapValue('returnReason', row.returnReason, true)}} </template>
      <template #soShipStatus="{ row }"> {{mapValue('soShipStatus', row.soShipStatus, true)}} </template>
      <template #signingBack="{ row }"> {{mapValue('signingBack', row.soSigningBack)}} </template>
      <template #generalLedger="{ row }"> {{mapValue('generalLedger', row.generalLedgerAccount)}} </template>
      <template #orderChannelList="{ row }"> {{mapValue('orderChannel', row.orderChannelList)}} </template>
      <template #designatedChannelCustomerCode="{ row }"> {{ `${row.designatedChannelCustomerCode || ''} ${row.designatedChannelCustomerName || ''}` }} </template>
      <template #designatedChannel="{ row }">
        <span v-if="row.designatedChannel===4||row.designatedChannel===5||row.designatedChannel===6">
          {{mapValue('orderChannel', row.designatedChannel)}}
        </span>
        <el-select
          v-else
          v-model="row.designatedChannel"
          :disabled="row.orderChannelList && (row.orderChannelList.includes(4)||row.orderChannelList.includes(5)||row.orderChannelList.includes(6))"
          placeholder="请选择"
        >
          <el-option v-for="item in getDesignatedChannelOption()" :key="item.value" :label="item.name"
            :value="item.value"></el-option>
        </el-select>
      </template>

      <template #shipWarehouseLocationText="{ row }"> {{mapValue('shipWarehouseLocationText', row)}} </template>
      <template #warehouseLocationText="{ row }"> {{mapValue('warehouseLocationText', row)}} </template>
      <template #tcWarehouseName="{ row }"> {{ row.tcWarehouseCode ? row.tcWarehouseCode + ' ' + row.tcWarehouseName : '' }} </template>
      <template #tcType="{ row }"> {{ row.tcType ? row.tcType === 'CUSTOMER' ? '客户TC' : '供应TC' : '' }} </template>
      <template #lightStatus="{ row }">
        <span :class="['circle-tag', statusMapColor(mapValue('reportLightStatus', row.lightStatus))]"></span>
      </template>

      <template #deliveryDateList="{row}">
        <span v-html="mapDeliveryList(row.deliveryDateList)"></span>
      </template>
      <template #logisticsNameList="{row}">
        <span v-html="mapDeliveryList(row.logisticsNameList)"></span>
      </template>
      <template #logisticsCodeList="{row}">
        <span v-html="mapDeliveryList(row.logisticsCodeList)"></span>
      </template>
      <template #signInfo="{row}">
        <span v-html="mapDeliveryList(row.signInfo)"></span>
      </template>
      <template #tcLogisticsNameList="{row}">
        <span v-html="mapDeliveryList(row.tcLogisticsNameList)"></span>
      </template>
      <template #tcLogisticsCodeList="{row}">
        <span v-html="mapDeliveryList(row.tcLogisticsCodeList)"></span>
      </template>
      <template #tcSignInfo="{row}">
        <span v-html="mapDeliveryList(row.tcSignInfo)"></span>
      </template>
      <template #deliveryVehicleNumberList="{row}">
        <span v-html="mapDeliveryList(row.deliveryVehicleNumberList)"></span>
      </template>
      <template #driverNameList="{row}">
        <span v-html="mapDeliveryList(row.driverNameList)"></span>
      </template>
      <template #driverPhoneList="{row}">
        <span v-html="mapDeliveryList(row.driverPhoneList)"></span>
      </template>
      <template #isDeliveryOrderSignedList="{row}">
        <span v-html="mapDeliveryList(row.isDeliveryOrderSignedList, 'isDeliveryOrderSigned')"></span>
      </template>
      <template #deliveryOrderQtyList="{row}">
        <span v-html="mapDeliveryList(row.deliveryOrderQtyList)"></span>
      </template>

      <template #deliveryOrderStatusList="{row}">
        <span v-html="mapDeliveryList(row.deliveryOrderStatusList, 'deliveryOrderStatus')"></span>
      </template>
      <template #deliveryWayList="{row}">
        <span v-html="mapDeliveryList(row.deliveryWayList, 'deliveryWay')"></span>
      </template>
      <template #idoNoList="{row}">
        <span v-if="row.idoNoList && row.idoNoList.length > 0">
          <span v-for="ido in row.idoNoList" :key="ido">
            {{ido}};
          </span>
        </span>
      </template>
      <template #leadProcessRate="{row}">
        <span>{{row.leadProcessRate != null ? row.leadProcessRate + '%': row.leadProcessRate}}</span>
      </template>

      <template #isPlanDeliveryDone="{ row }"> {{mapBoolean(row.isPlanDeliveryDone)}} </template>
      <template #isDeliveryOrderSigned="{ row }"> {{mapBoolean(row.isDeliveryOrderSigned)}} </template>
      <template #isEscrow="{ row }"> {{mapBoolean(row.isEscrow)}} </template>
      <template #isAutoDeliveryOrder="{ row }"> {{mapBoolean(row.isAutoDeliveryOrder)}} </template>

      <template #poFollowReportTag="{ row }">
        <el-select v-model="row.tag" clearable disabled @change="val => handleChange(val, 'poFollowReportTag', row)" placeholder="请选择">
          <el-option
            v-for="item in buildOptions('poFollowReportTag')"
            disabled
            :key="item.value"
            :label="item.name"
            :value="item.value">
          </el-option>
        </el-select>
      </template>
       <template #transferReason="{ row }">
        <el-select v-model="row.transferReason" clearable :disabled="disabledByStatus(row) || row.orderType !== 'Z003'" @change="val => handleChange(val, 'transferReason', row)" placeholder="请选择">
          <el-option
            v-for="item in buildOptions('transferReason')"
            :key="item.value"
            :label="item.name"
            :value="item.value">
          </el-option>
        </el-select>
      </template>
      <template #isUrgent="{ row }">
        <el-checkbox :disabled="disabledByStatus(row)" @change="val => handleChange(val, 'isUrgent', row)" :true-label="1" false-label="0" v-model="row.isUrgent"></el-checkbox>
      </template>
      <template #isConfirmed="{ row }">
        <el-checkbox :disabled="disabledByStatus(row)" @change="val => handleChange(val, 'isConfirmed', row)" :true-label="1" false-label="0" v-model="row.isConfirmed"></el-checkbox>
      </template>
      <template #isSpot="{ row }">
        <el-checkbox :disabled="disabledByStatusSpot(row)" @change="val => handleChange(val, 'isSpot', row)" :true-label="1" false-label="0" v-model="row.isSpot"></el-checkbox>
      </template>
      <template #isCustomerAccept="{ row }">
        <el-checkbox :disabled="disabledByStatus(row)" @change="val => handleChange(val, 'isCustomerAccept', row)" :true-label="1" false-label="0" v-model="row.isCustomerAccept"></el-checkbox>
      </template>
      <template #isSigningBackAccepted="{ row }">
        <el-checkbox disabled @change="val => handleChange(val, 'isSigningBackAccepted', row)" :true-label="1" false-label="0" v-model="row.isSigningBackAccepted"></el-checkbox>
      </template>
      <template #isCustomerReceived="{ row }">
        <el-checkbox disabled @change="val => handleChange(val, 'isCustomerReceived', row)" :true-label="1" false-label="0" v-model="row.isCustomerReceived"></el-checkbox>
      </template>
      <template #customerDeliveryDate="{ row }">
        <el-date-picker
          v-model="row.customerDeliveryDate"
          @change="val => handleChange(val, 'customerDeliveryDate', row)"
          :disabled="disabledByStatus(row)"
          size="mini"
          value-format="yyyy-MM-dd"
          style="width: 90%"
          type="date"
          placeholder="选择日期">
        </el-date-picker>
      </template>
      <template #planDeliveryDate="{ row }">
        <el-date-picker
          v-model="row.planDeliveryDate"
          @change="val => handleChange(val, 'planDeliveryDate', row)"
          :disabled="disabledByStatus(row)"
          :picker-options="dateOptions"
          size="mini"
          value-format="yyyy/MM/dd"
          style="width: 90%"
          type="date"
          placeholder="选择日期">
        </el-date-picker>
      </template>
      <template #remark="{ row }">
        <el-input
          placeholder="请输入内容"
          @change="val => handleChange(val, 'remark', row)"
          :disabled="disabledByStatus(row)"
          v-model="row.remark">
        </el-input>
      </template>
      <template #overdueReason="{ row }">
        <el-cascader
          v-model="row.overdueReason"
          :options="overdueReasonOptions"
          clearable
          disabled
          @change="(val) => handleChangeOverdueReason(val, row)">
        </el-cascader>
      </template>
      <template #isRealOrder="{ row }">
        {{mapValue('yesOrNo', row.isRealOrder)}}
      </template>
      <template #deleteReason="{ row }">
        {{row.deleteReason}}
      </template>
      <template #poItemLastGrossMargin="{ row }">
        <span>{{![undefined, 'undefined', '', null, NaN].includes(row.poItemLastGrossMargin) ? row.poItemLastGrossMargin + '%': row.poItemLastGrossMargin}}</span>
      </template>
      <template #receiveContactPhoneEncryption="{ row }">
        <span>
          {{row.receiveContactPhoneEncryption}}
        <el-popover
          width="200"
          trigger="click"
          :content="row.receiveContactPhone"
          v-if="row.receiveContactPhone"
          >
          <el-button slot="reference" type="text">查看</el-button>
        </el-popover>
      </span>
    </template>
    </vxe-grid>
    <div class="pagination" >
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tableInfo.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="tableInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableInfo.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { columns, z002Columns } from './grid.js'
import { buildOptions } from '@/utils/mm'
import { getButtonAuth } from '@/utils/auth'
import { throttle, safeRun, deepClone, Storage } from '@/utils/index'
import Sortable from 'sortablejs'
import moment from 'moment'

export default {
  name: 'trackingOrderGird',
  props: {
    listData: {
      type: Array,
      default: () => []
    },
    tableInfo: {
      type: Object,
      default: () => ({})
    },
    tableLoading: Boolean,
    updateLoading: Function,
    updateVisible: Function
  },
  data () {
    const newCreateTime = moment(new Date()).subtract(1, 'days').format('YYYY-MM-DD')
    console.log(new Date(newCreateTime))
    return {
      columnSequence: null,
      z002ColumnSequence: null,
      gridHeight: 400,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      dateOptions: {
        disabledDate: (time) => {
          return time.getTime() < (new Date(newCreateTime)).getTime()
        }
      },
      tableColumns: []
    }
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList
    }),
    hasZ002Order () {
      return this.listData.some(d => d.orderType === 'Z002')
    },
    // getColumns () {
    //   // 查询结果有进口订单时才展示币种、行未税人民币总额、行含税人民币总额
    //   console.log(this.hasZ002Order);
    //   const cols = this.hasZ002Order ? deepClone(z002Columns) : deepClone(columns)

    //   cols.forEach(column => {
    //     if (column.enums) {
    //       column.slots = { default: column.enums }
    //     }
    //     if (column.isBoolean || column.slot) {
    //       column.slots = { default: column.field }
    //     }
    //     if (column.field === 'materialGroupName') {
    //       column.slots = { default: column.field }
    //     }
    //     if (column.field === 'lightStatus') {
    //       column.slots = { default: column.field }
    //     }
    //   })
    //   console.log(z002Columns);
    //   this.sortByDefault(cols)
    //   console.log(cols);
    //   return cols
    // },
    overdueReasonOptions() {
      const overdueReasonFirst = this.dictList['overdueReasonFirst']
      const overdueReasonSecond = this.dictList['overdueReasonSecond']
      if (overdueReasonFirst) {
        overdueReasonFirst.map(first => {
          first.children = [...overdueReasonSecond.filter(second => first.value === second.value.substr(0, 2))].map(item => {
            return {
              value: item.value,
              label: item.name
            }
          })
        })
        return overdueReasonFirst.map(item => {
          return {
            value: item.value,
            label: item.name,
            children: item.children
          }
        })
      }
      return []
    }
  },
  watch: {
    hasZ002Order: function(newVal, oldVal) {
      console.log(newVal, oldVal);
      if (newVal !== oldVal) {
        const cols = newVal ? deepClone(z002Columns) : deepClone(columns)
        this.tableColumns = this.getColumns(cols)
      }
    }
  },
  methods: {
    buildOptions,
    getButtonAuth,
    getDesignatedChannelOption() {
      const options = buildOptions('orderChannel').filter(item => item.value === 7 || item.value === 8 || item.value === 9)
      return [{
        name: '清空',
        value: 0
      }, ...options]
    },
    getColumns(cols) {
      cols.forEach(column => {
        if (column.enums) {
          column.slots = { default: column.enums }
        }
        if (column.isBoolean || column.slot) {
          column.slots = { default: column.field }
        }
        if (column.field === 'materialGroupName') {
          column.slots = { default: column.field }
        }
        if (column.field === 'lightStatus') {
          column.slots = { default: column.field }
        }
      })
      this.sortByDefault(cols)
      console.log(cols);
      return cols
    },
    handleChangeOverdueReason(val, row) {
      const data = this.listData
      data.forEach(item => {
        if (item.poNo === row.poNo && item.itemNo === row.itemNo) {
          item.overdueReasonFirst = val.length === 0 ? '' : val[0]
          item.overdueReasonSecond = val.length === 0 ? '' : val[1]
          if (val.length === 0) {
            item.overdueReason = ''
          }
        }
      })
    },
    sortByDefault (columns) {
      const cloneColumns = deepClone(columns)
      const cs = this.hasZ002Order ? this.z002ColumnSequence : this.columnSequence
      if (Array.isArray(cs) && cs.length) {
        console.log('sort getColumns...');
        for (let conSeq = 0; conSeq < cs.length; conSeq++) {
          const item = cloneColumns.find(item => item.field === cs[conSeq]);
          if (columns[conSeq].field !== cs[conSeq] && item) {
            columns[conSeq] = item
          }
        }
      }
    },
    columnDrop() {
      this.$nextTick(() => {
        const $table = this.$refs.trackingOrderGrid
        this.sortable2 = Sortable.create(
          $table.$el.querySelector(
            '.body--wrapper>.vxe-table--header .vxe-header--row'
          ),
          {
            handle: '.vxe-header--column:not(.col--fixed)',
            onEnd: ({ item, newIndex, oldIndex }) => {
              console.log(item, newIndex, oldIndex)
              const { fullColumn, tableColumn } = $table.getTableColumn()
              // 目标表头
              const targetThElem = item
              const wrapperElem = targetThElem.parentNode
              // 被移到位置的数据
              const newColumn = fullColumn[newIndex]
              if (newColumn.fixed) {
                // 错误的移动
                if (newIndex > oldIndex) {
                  wrapperElem.insertBefore(
                    targetThElem,
                    wrapperElem.children[oldIndex]
                  )
                } else {
                  // （被移到位置的数据）前插入 （要移动的数据）
                  wrapperElem.insertBefore(
                    // 要移动的数据
                    wrapperElem.children[oldIndex],
                    // 《===被移到位置的数据
                    targetThElem
                  )
                }
                return this.$message.error('固定列不允许拖动！')
              }
              // 转换真实索引
              const oldColumnIndex = $table.getColumnIndex(
                // 找到数据
                tableColumn[oldIndex]
              )
              const newColumnIndex = $table.getColumnIndex(
                tableColumn[newIndex]
              )
              // 移动到目标列
              const currRow = fullColumn.splice(oldColumnIndex, 1)[0]
              fullColumn.splice(newColumnIndex, 0, currRow)
              Storage.setItem(this.hasZ002Order ? 'z002TrackingOrderGridSequence' : 'trackingOrderGridSequence', Array.from(new Set(fullColumn.map(i => i.property))))
              this.updateSeq()
              $table.loadColumn(fullColumn)
            }
          }
        )
      })
    },
    calcLeftHeight: throttle(function calcLeftHeight() {
      safeRun(() => {
        const form = document.querySelector('.tracking-order-grid-form')
        const table = document.querySelector('.tracking-order-grid-table')
        const innerHeight = window.innerHeight - 130
        // 跟单报表页面container总高度
        const existHeight = form.offsetHeight + table.offsetHeight
        // 已占据高度
        // console.log(`existHeight: ${existHeight}, innerHeight: ${innerHeight}`)
        const paddingHeight = innerHeight - existHeight
        const floorHeight = Math.floor(paddingHeight * 400 / 452)
        // console.log(`floorHeight: ${floorHeight}, gridHeight: ${this.gridHeight}`)
        if (floorHeight > 5 || floorHeight < -5) {
          this.gridHeight += floorHeight
          if (this.gridHeight <= 400) {
            this.gridHeight = 400
          }
        }
      })
    }),
    handleSortChange ({ column, property, order, sortBy, sortList, $event }) {
      this.$emit('sortTable', order, property)
    },
    disabledByStatus (row) {
      const roleDisabled = !this.getButtonAuth('跟单报表', '保存修改')
      // eslint-disable-next-line eqeqeq
      const statusDisabled = row.isPlanDeliveryDone == 1 || Boolean(row.isDeleted)
      return statusDisabled || roleDisabled
    },
    disabledByStatusSpot (row) {
      const statusDisabled = row.deliveryChangeReason !== 11
      return this.disabledByStatus(row) || statusDisabled
    },
    disabledIsCustomerReceived (row) {
      if (row.isDirect !== 1) {
        return true
      }
      return false
    },
    disabledIsSigningBackAccepted (row) {
      if (row.isPlanDeliveryDone === 1) {
        return true
      }
      return false
    },
    toDetail (row) {
      this.$router.push({
        path: `/orderPurchase/detail/${row.poNo}`,
        query: { tagName: `${row.poNo}详情` }
      })
    },
    mapDeliveryList (list, enums) {
      let ret = ''
      if (Array.isArray(list)) {
        list = list.map((item, index) => {
          if (enums) {
            let opts = buildOptions(enums) || []
            if (enums === 'isDeliveryOrderSigned') {
              opts = [
                { value: 1, name: '是' },
                { value: 0, name: '否' }
              ]
            }
            // eslint-disable-next-line eqeqeq
            const findItem = opts.find(_item => _item.value == item)
            if (findItem) {
              item = findItem.name
            }
          }
          return list.length > 1 ? `No${index + 1}:${item}` : item
        })
        ret = list.join('\n')
      }
      return ret
    },
    handleChange (val, prop, row) {
      row.changed = true
      if (prop === 'isUrgent' || prop === 'isConfirmed' || prop === 'isSigningBackAccepted' || prop === 'isCustomerReceived' || prop === 'isSpot') {
        const data = this.listData
        data.forEach(item => {
          if (item.poNo === row.poNo && item.itemNo === row.itemNo) {
            item[prop] = val
          }
        })
      }
      if (prop === 'planDeliveryDate' && val) {
        row.isConfirmed = 1
      }
    },
    statusMapColor (status) {
      let color = ''
      switch (status) {
        case '绿灯':
          color = 'success'; break;
        case '黄灯':
          color = 'warning'; break;
        case '红灯':
          color = 'danger'; break;
      }
      return color
    },
    emptyValue (value) {
      return value ? value + '' : ''
    },
    mapValue (prop, value, withCode) {
      if (prop === 'materialGroupName') {
        return this.emptyValue(value.materialGroupNum) + ' ' + this.emptyValue(value.materialGroupName)
      }
      if (prop === 'shipWarehouseLocationText') {
        return this.emptyValue(value.shipWarehouseLocation) + ' ' + this.emptyValue(value.shipWarehouseLocationText)
      }
      if (prop === 'warehouseLocationText') {
        return this.emptyValue(value.warehouseLocation) + ' ' + this.emptyValue(value.warehouseLocationText)
      }
      const options = buildOptions(prop)
      if (Array.isArray(options)) {
        if (Array.isArray(value)) {
          let ret = []
          safeRun(() => {
            value.forEach(value => {
              // eslint-disable-next-line
              const item = this.dictList[prop].find(item => item.value == value)
              ret.push(item.name)
            })
          })
          return ret.join('、')
        }
        // eslint-disable-next-line eqeqeq
        const item = options.find(item => item.value == value)
        if (item) {
          return withCode ? item.value + ' ' + this.emptyValue(item.name) : item.name
        }
      }
    },
    mapBoolean (value) {
      return value ? '是' : '否'
    },
    handleChangedProp (tmp, item) {
      const needProp = [ 'poNo', 'planNo', 'itemNo', 'deliveryChangeReason' ]
      for (let prop in tmp) {
        // eslint-disable-next-line eqeqeq
        if ((tmp[prop] == item._originData[prop]) && !needProp.find(n => n == prop)) {
          delete tmp[prop]
        }
      }
    },
    formatData (list) {
      let data = list.filter(item => item.poNo && item.itemNo && item.planNo).filter(this.lineChanged)
      data = data.map(item => {
        let tmp = {}
        const allProps = [ 'poNo', 'itemNo', 'planNo', 'tag', 'isUrgent', 'isCustomerAccept', 'isConfirmed', 'planDeliveryDate', 'isSpot', 'remark', 'isSigningBackAccepted', 'isCustomerReceived', 'overdueReasonFirst', 'overdueReasonSecond', 'transferReason', 'designatedChannel', 'poUnconfirmedReason', 'deliveryChangeReason' ]
        const numberProps = ['isUrgent', 'isCustomerAccept', 'isConfirmed', 'isSpot', 'isSigningBackAccepted', 'isCustomerReceived']
        allProps.forEach(prop => {
          tmp[prop] = item[prop]
          if (numberProps.find(p => p === prop)) {
            tmp[prop] = Number(tmp[prop])
          }
        })
        this.handleChangedProp(tmp, item)
        return tmp
      })
      return data
    },
    lineChanged (item) {
      const tmp = {}
      const allProps = [ 'poNo', 'itemNo', 'planNo', 'tag', 'isUrgent', 'isCustomerAccept', 'isConfirmed', 'planDeliveryDate', 'isSpot', 'remark', 'isSigningBackAccepted', 'isCustomerReceived', 'transferReason', 'designatedChannel', 'poUnconfirmedReason', 'deliveryChangeReason' ]
      const needProp = [ 'poNo', 'planNo', 'itemNo' ]
      allProps.forEach(prop => {
        tmp[prop] = item[prop]
      })
      for (let prop in tmp) {
        // eslint-disable-next-line eqeqeq
        if ((tmp[prop] == item._originData[prop]) && !needProp.find(n => n == prop)) {
          delete tmp[prop]
        }
      }
      if (Object.keys(tmp).length === 3) {
        return false
      }
      return true
    },
    validateChanged (data) {
      let ret = true
      const fData = data.filter(this.lineChanged)
      if (!fData.length) {
        this.$message.info('没有修改行！')
        ret = false
      }
      return ret
    },
    validateDeliveryDate (data) {
      let ret = true
      let dateMsg = ''
      let textMsg = ''
      let dataChangeMsg = ''
      data.forEach((item, index) => {
        if (!item.planDeliveryDate) {
          ret = false; dateMsg += index + 1 + ' '
        } else {
          console.log(item.standardDeliveryDate < item.planDeliveryDate)
          if ((item._originData.planDeliveryDate < item.planDeliveryDate) && !item.deliveryChangeReason) {
            ret = false; dataChangeMsg += index + 1 + ' '
          }
        }
      })
      data.forEach((item, index) => {
        if (item.remark && item.remark.length > 200) {
          ret = false; textMsg += index + 1 + ' '
        }
      })
      if (dateMsg) {
        dateMsg = `第${dateMsg}行计划行交货日期不能为空！`
      }
      if (dataChangeMsg) {
        dataChangeMsg = `第${dataChangeMsg}行计划交货日期小于交货日期，交期变更原因必填！`
      }
      if (textMsg) {
        textMsg = `第${textMsg}行备注文本不能大于200个字符！`
      }
      let err = (dateMsg || dataChangeMsg) ? (dateMsg || dataChangeMsg) + '<br />' + textMsg : textMsg
      this.$alert(err, '操作提示', {
        type: 'error',
        dangerouslyUseHTMLString: true
      })
      return ret
    },
    formatDate (data) {
      const tmpData = deepClone(data)
      tmpData && tmpData.forEach && tmpData.forEach(item => {
        if (item.planDeliveryDate) {
          item.planDeliveryDate = item.planDeliveryDate.replace(/\//ig, '-')
        }
      })
      return tmpData
    },
    saveData () {
      let data = this.listData
      if (!this.validateChanged(data)) return
      if (!this.validateDeliveryDate(data)) return
      data = this.formatData(data)
      // data = this.formatDate(data)
      // console.log(this.listData)
      this.$confirm('确认保存修改行吗？', '操作提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const submitData = {
          updateUser: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name,
          rows: data,
          type: 0
        }
        this.$emit('saveData', submitData)
      })
    },
    exportDetail () {
      this.$emit('exportDetail')
    },
    batchUpdate () {
      this.$emit('showEditDialog')
    },
    handleSizeChange (val) {
      this.$emit('handleTableInfo', { pageSize: val })
    },
    handleCurrentChange (val) {
      this.$emit('handleTableInfo', { pageNo: val })
    },
    updateSeq () {
      const columnSequence = Storage.getItem('trackingOrderGridSequence')
      const z002ColumnSequence = Storage.getItem('z002TrackingOrderGridSequence')
      if (columnSequence) {
        console.log('mounted find columnSequence...')
        this.columnSequence = columnSequence
      }
      if (z002ColumnSequence) {
        console.log('mounted find z002columnSequence...')
        this.z002ColumnSequence = z002ColumnSequence
      }
    },
    initVersion() {
      const newVersion = '20241014'
      const versionKey = 'poFollowVersionKey'
      let version = Storage.getItem(versionKey) + ''
      if (newVersion !== version) {
        console.log('%cPoFollow version not match!', 'color:pink;')
        console.log('newVersion, version', newVersion, version)
        console.log('call Storage.clear()')
        Storage.removeItem(versionKey)
        Storage.removeItem('trackingOrderGridSequence')
        Storage.removeItem('z002TrackingOrderGridSequence')
        Storage.removeItem('VXE_TABLE_CUSTOM_COLUMN_VISIBLE')
        Storage.setItem(versionKey, newVersion)
      }
    }
  },
  mounted() {
    setTimeout(this.calcLeftHeight, 100)
    window.addEventListener('resize', this.calcLeftHeight, false)
  },
  created() {
    this.initVersion()
    this.columnDrop()
    this.updateSeq()
    this.tableColumns = this.getColumns(deepClone(columns))
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.calcLeftHeight, false)
    if (this.sortable2) {
      this.sortable2.destroy()
    }
  }
}
</script>

<style lang="scss" scoped>
.tracking-order-grid-table {
  overflow: hidden;
  .title-group{
    display: flex;
    justify-content: space-between;
    span{
      line-height: 32px;
      height: 33px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .button-group {
      min-width: 300px;
      display: inline-block;
      text-align: right;
    }
  }
  .circle-tag{
    height: 20px;
    width: 20px;
    border-radius: 50%;
    display: inline-block;
    &.success {
      background-color: green;
    }
    &.warning {
      background-color: orange;
    }
    &.danger {
      background-color: red;
    }
  }
  .pagination{
    margin-top: 20px;
    float:right;
  }
}
</style>
