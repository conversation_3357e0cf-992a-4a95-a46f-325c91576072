const columns = [
  { title: '待审核原因', field: 'poUnpassedReason', slot: true, width: 160, showOverflow: false, align: 'left' },
  { title: '状态', field: 'lightStatus', enums: 'reportLightStatus', width: 50 },
  { title: '警示原因', field: 'lightReason', width: 220 },
  { title: '是否供应商预警订单', field: 'earlyWarnPo', isBoolean: true, width: 140 },
  { title: '采购订单', field: 'poNo', slot: true, width: 100 },
  { title: 'SAP订单号', field: 'sapOrderNo', width: 100 },
  { title: '商品行', field: 'itemNo', width: 80 },
  { title: '计划行', field: 'planNo', width: 80 },
  { title: '无法确认订单原因', field: 'poUnconfirmedReason', slot: true, width: 160 },
  { title: '无法确认原因备注', field: 'reasonRemark', width: 160 },
  { title: '附件名称', field: 'attachmentName', slot: true, width: 160, showOverflow: 'ellipsis' },
  { title: '附件地址', field: 'attachmentUrl', slot: true, width: 160, showOverflow: 'ellipsis' },
  { title: 'PO逾期', field: 'warningType', enums: 'poFollowReportWarn', width: 120 },
  { title: '采购单类型标签', field: 'tag', enums: 'poFollowReportTag', width: 120 },
  { title: '跟单分类', field: 'overdueReason', slot: true, width: 160 },
  { title: '是否急单', field: 'isUrgent', isBoolean: true, width: 80 },
  { title: '更新接受交期', field: 'isCustomerAccept', isBoolean: true, width: 120 }, //
  { title: '接受交货日期', field: 'customerAcceptDate', width: 120 }, //
  { title: '交期确认标识', field: 'isConfirmed', isBoolean: true, width: 120 },
  { title: '是否现货', field: 'isSpot', isBoolean: true, width: 120 },
  { title: '交期变更原因', field: 'deliveryChangeReason', slot: true, width: 160 },
  { title: '直发签单能否回传', field: 'isSigningBackAccepted', isBoolean: true, width: 120 },
  { title: '直发已送达', field: 'isCustomerReceived', isBoolean: true, width: 120 },
  { title: '标准交货日期', field: 'standardDeliveryDate', sortable: true, width: 120 },
  { title: '可信云仓(下单时)', field: 'cloud', enums: 'cloud', width: 120 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 120 },
  { title: '计划行交货日期', field: 'planDeliveryDate', sortable: true, slot: true, width: 160 },
  { title: '备注文本', field: 'remark', slot: true, width: 200 }, //
  { title: '累计跟单文本', field: 'accumulateRemark', width: 120 },
  { title: '是否回传合同', field: 'followContractBack', enums: 'poContractBack', width: 120 },
  { title: '订单类型', field: 'orderType', enums: 'orderType', width: 120 },
  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 120 },
  { title: 'VC标识', field: 'openVcStatus', enums: 'openVcStatus', width: 80 },
  { title: '供应商代码', field: 'supplierNo', width: 100 },
  { title: '供应商', field: 'supplierName', width: 120 },
  { title: '供应商分类', field: 'supplierClassify', enums: 'supplierClassify', width: 120 },
  { title: '签订框架协议', field: 'ifSignFrameworkAgreement', enums: 'yesOrNo', width: 120 },
  { title: '合作模式', field: 'coopMode', enums: 'cooperateMode', width: 120 },
  { title: '付款条件', field: 'paymentTermCode', enums: 'paymentTermCode', width: 120 },
  { title: '创建人', field: 'createUser', width: 120 },
  { title: '创建日期', field: 'createDate', width: 120 },
  { title: '审批状态', field: 'approveStep', enums: 'approveStatus', width: 120 },
  { title: 'SKU编码', field: 'skuNo', width: 120 },
  { title: '物料描述', field: 'materialDescription', width: 120 },
  { title: '交货完成标识', field: 'isPlanDeliveryDone', isBoolean: true, width: 100 },
  { title: '商品行数量', field: 'itemQty', width: 100 },
  { title: '订单单位', field: 'unit', enums: 'orderUnit', width: 80 },
  { title: '已收货数量', field: 'itemReceivedQty', width: 100 },
  { title: '未收货数量', field: 'itemUnreceivedQty', width: 100 },
  { title: '未收货金额', field: 'itemUnreceivedAmount', width: 120 },
  { title: '仓库地点', field: 'warehouseLocationText', slot: true, width: 120 }, //
  { title: 'TC仓', field: 'tcWarehouseName', slot: true, width: 120 }, //
  { title: 'TC类型', field: 'tcType', slot: true, width: 120 }, //
  { title: '发货仓库地点', field: 'shipWarehouseLocationText', slot: true, width: 120 }, //
  { title: '含税单价', field: 'taxedPrice', width: 80 },
  { title: '未税单价', field: 'untaxedPrice', width: 80 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 60 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 100 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 100 },
  { title: '商品行运费', field: 'shippingAmount', width: 100 },
  { title: '商品行折扣', field: 'discountAmount', width: 100 },
  { title: '订单行毛利率', field: 'poItemLastGrossMargin', slot: true, width: 100 },
  { title: '退货原因', field: 'returnReason', enums: 'returnReason', width: 120 },
  { title: '供应商订单号', field: 'supplierOrderNo', width: 120 },
  { title: '代管代发', field: 'isEscrow', isBoolean: true, width: 80 },
  { title: '供应商物料号', field: 'supplierMaterialNo', width: 120 },
  { title: '工厂', field: 'factoryCode', enums: 'factoryCode', width: 120 },
  { title: '计划行数量', field: 'planDeliveryQty', width: 100 },
  { title: '商品行交期(下单时）', field: 'standardLeadDays', width: 140 },
  { title: '预计到货天数', field: 'expectedLeadDays', sortable: true, width: 120 },
  { title: '交期天数差异', field: 'diffLeadDays', sortable: true, width: 120 },
  { title: '交货进度', field: 'leadProcessRate', slot: true, width: 120 },
  { title: '剩余交货天数', field: 'leftLeadDays', sortable: true, width: 120 },
  { title: '交货单', field: 'idoNoList', slot: true, width: 120 },

  { title: '转储未制单数量', field: 'stoNotDnQty', width: 120 },
  { title: '转储已制单数量', field: 'stoDnQty', width: 120 },
  { title: '转储已制单未发货', field: 'stoDnUnclearedQty', width: 120 },
  { title: '转储已发货数量', field: 'stoDnClearedQty', width: 120 },
  { title: '外向交货单号', field: 'dnNo', width: 120 },
  { title: '外向交货单过账日期', field: 'stoDnPostDate', width: 120 },
  { title: '转储在途数量', field: 'stoDnOnWayQty', width: 100 },

  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 120 },
  { title: '发货状态', field: 'deliveryOrderStatusList', enums: 'deliveryOrderStatus', slot: true, width: 80 }, // deliveryStatusList 改为list，加字典
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 120 },
  { title: '送货方式', field: 'deliveryWayList', enums: 'deliveryWay', slot: true, width: 120 }, // deliveryWayList 改为list

  { title: '物流公司', field: 'logisticsNameList', slot: true, width: 120 }, // deliveryWayList 改为list
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 120 },
  { title: '签收信息', field: 'signInfo', slot: true, width: 120 },
  { title: 'TC物流公司', field: 'tcLogisticsNameList', slot: true, width: 120 }, // deliveryWayList 改为list
  { title: 'TC物流单号', field: 'tcLogisticsCodeList', slot: true, width: 120 },
  { title: '收货方签收信息', field: 'tcSignInfo', slot: true, width: 120 },
  { title: '送货车牌号', field: 'deliveryVehicleNumberList', slot: true, width: 120 },
  { title: '司机姓名', field: 'driverNameList', slot: true, width: 120 },
  { title: '电话号码', field: 'driverPhoneList', slot: true, width: 120 },
  { title: '签收单', field: 'isDeliveryOrderSignedList', slot: true, isBoolean: true, width: 80 },
  { title: '送货单已发数量', field: 'deliveryOrderQtyList', slot: true, width: 120 }, // deliveryWayList 改为list
  { title: '订单渠道', field: 'orderChannelList', slot: true, width: 120 },
  { title: '指定渠道客户', field: 'designatedChannelCustomerCode', slot: true, width: 120 },
  { title: '指定渠道打标', field: 'designatedChannel', slot: true, width: 120 },

  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '专供销售单号', field: 'specialSupplySo', width: 120 },
  { title: '专供销售单行号', field: 'specialSupplySoItem', width: 120 },
  { title: '客服', field: 'customerService', width: 120 },
  { title: '销售', field: 'soSellerName', width: 120 },
  { title: '销售订单数量', field: 'soItemQty', width: 120 },
  { title: 'SO发货状态', field: 'soShipStatus', enums: 'soShipStatus', width: 120 }, // dicList
  { title: '要求交期', field: 'soDeliveryDate', width: 120 },
  { title: '签单返回', field: 'soSigningBack', enums: 'signingBack', width: 120 },
  { title: '交付主管', field: 'soDeliveryManager', width: 120 },
  { title: '物料组', field: 'materialGroupName', width: 120 },
  { title: '品牌', field: 'brandName', width: 120 },
  { title: '产品定位', field: 'productPosition', width: 120 },
  { title: '商品经理', field: 'productManager', width: 120 },
  { title: '订单备注', field: 'orderRemark', width: 120 },
  { title: '打印备注', field: 'printRemark', width: 120 },
  { title: '已开票数量', field: 'invoiceQuantity', width: 100 },
  { title: '已开票金额', field: 'invoiceAmount', width: 100 },
  { title: '未开票数量', field: 'notInvoiceQuantity', width: 100 },
  { title: '未开票金额', field: 'notInvoiceAmount', width: 100 },
  { title: '历史预付总金额', field: 'historyPaymentAmount', width: 120 },
  { title: '递交银行付款时间', field: 'submitBankTime', width: 120 },
  { title: '承诺变更次数', field: 'promiseUpdateFrequency', width: 120 },
  { title: '订单来源', field: 'source', width: 120 },
  { title: '成本中心', field: 'costCenterText', width: 120 },
  { title: '成本中心代码', field: 'costCenter', width: 120 },
  { title: '总账科目', field: 'generalLedgerAccount', width: 120 },
  { title: '总账科目描述', field: 'generalLedgerAccountText', enums: 'generalLedger', width: 120 },
  { title: '触发审批内容', field: 'approveTriggerDetail', width: 120 },
  { title: '申请下单原因', field: 'orderReason', width: 120 },
  { title: '调拨原因', field: 'transferReason', enums: 'transferReason', width: 150 },
  { title: '是否实单', field: 'isRealOrder', slot: true, width: 100 },
  { title: '删除原因', field: 'deleteReason', enums: 'deleteReason', width: 120 },
  { title: '收货联系人', field: 'receiveContactName', width: 120 },
  { title: '收货联系电话', field: 'receiveContactPhoneEncryption', slot: true, width: 120 },
  { title: '收货联系地址', field: 'receiveAddressDetail', width: 120 },
  { title: '自动生成送货单', field: 'isAutoDeliveryOrder', enums: 'isAutoDeliveryOrder', width: 120 },
  { title: 'OA流程编号', field: 'oaNo', width: 100 }
]

const z002Columns = [
  { title: '待审核原因', field: 'poUnpassedReason', slot: true, width: 160, showOverflow: false, align: 'left' },
  { title: '状态', field: 'lightStatus', enums: 'reportLightStatus', width: 50 },
  { title: '警示原因', field: 'lightReason', width: 220 },
  { title: '是否供应商预警订单', field: 'earlyWarnPo', isBoolean: true, width: 140 },
  { title: '采购订单', field: 'poNo', slot: true, width: 100 },
  { title: 'SAP订单号', field: 'sapOrderNo', width: 100 },
  { title: '商品行', field: 'itemNo', width: 80 },
  { title: '计划行', field: 'planNo', width: 80 },
  { title: '无法确认订单原因', field: 'poUnconfirmedReason', slot: true, width: 160 },
  { title: '无法确认原因备注', field: 'reasonRemark', width: 160 },
  { title: '附件名称', field: 'attachmentName', slot: true, width: 160, showOverflow: 'ellipsis' },
  { title: '附件地址', field: 'attachmentUrl', slot: true, width: 160, showOverflow: 'ellipsis' },
  { title: 'PO逾期', field: 'warningType', enums: 'poFollowReportWarn', width: 120 },
  { title: '采购单类型标签', field: 'tag', enums: 'poFollowReportTag', width: 120 },
  { title: '跟单分类', field: 'overdueReason', slot: true, width: 160 },
  { title: '是否急单', field: 'isUrgent', isBoolean: true, width: 80 },
  { title: '更新接受交期', field: 'isCustomerAccept', isBoolean: true, width: 120 }, //
  { title: '接受交货日期', field: 'customerAcceptDate', width: 120 }, //
  { title: '交期确认标识', field: 'isConfirmed', isBoolean: true, width: 120 },
  { title: '是否现货', field: 'isSpot', isBoolean: true, width: 120 },
  { title: '交期变更原因', field: 'deliveryChangeReason', slot: true, width: 160 },
  { title: '直发签单能否回传', field: 'isSigningBackAccepted', isBoolean: true, width: 120 },
  { title: '直发已送达', field: 'isCustomerReceived', isBoolean: true, width: 120 },
  { title: '标准交货日期', field: 'standardDeliveryDate', sortable: true, width: 120 },
  { title: '可信云仓(下单时)', field: 'cloud', enums: 'cloud', width: 120 },
  { title: '首次承诺日期', field: 'firstDeliveryDate', width: 120 },
  { title: '计划行交货日期', field: 'planDeliveryDate', sortable: true, slot: true, width: 160 },
  { title: '备注文本', field: 'remark', slot: true, width: 200 }, //
  { title: '累计跟单文本', field: 'accumulateRemark', width: 120 },
  { title: '是否回传合同', field: 'followContractBack', enums: 'poContractBack', width: 120 },
  { title: '订单类型', field: 'orderType', enums: 'orderType', width: 120 },
  { title: '采购员', field: 'purchaseGroup', enums: 'purchaseGroup', width: 120 },
  { title: 'VC标识', field: 'openVcStatus', enums: 'openVcStatus', width: 80 },
  { title: '供应商代码', field: 'supplierNo', width: 100 },
  { title: '供应商', field: 'supplierName', width: 120 },
  { title: '供应商分类', field: 'supplierClassify', enums: 'supplierClassify', width: 120 },
  { title: '签订框架协议', field: 'ifSignFrameworkAgreement', enums: 'yesOrNo', width: 120 },
  { title: '合作模式', field: 'coopMode', enums: 'cooperateMode', width: 120 },
  { title: '付款条件', field: 'paymentTermCode', enums: 'paymentTermCode', width: 120 },
  { title: '创建人', field: 'createUser', width: 120 },
  { title: '创建日期', field: 'createDate', width: 120 },
  { title: '审批状态', field: 'approveStep', enums: 'approveStatus', width: 120 },
  { title: 'SKU编码', field: 'skuNo', width: 120 },
  { title: '物料描述', field: 'materialDescription', width: 120 },
  { title: '交货完成标识', field: 'isPlanDeliveryDone', isBoolean: true, width: 100 },
  { title: '商品行数量', field: 'itemQty', width: 100 },
  { title: '订单单位', field: 'unit', enums: 'orderUnit', width: 80 },
  { title: '已收货数量', field: 'itemReceivedQty', width: 100 },
  { title: '未收货数量', field: 'itemUnreceivedQty', width: 100 },
  { title: '未收货金额', field: 'itemUnreceivedAmount', width: 120 },
  { title: '仓库地点', field: 'warehouseLocationText', slot: true, width: 120 }, //
  { title: '发货仓库地点', field: 'shipWarehouseLocationText', slot: true, width: 120 }, //
  { title: '含税单价', field: 'taxedPrice', width: 80 },
  { title: '未税单价', field: 'untaxedPrice', width: 80 },
  { title: '税率', field: 'inputTax', enums: 'inputTax', width: 60 },
  { title: '行含税总额', field: 'taxedTotalAmount', width: 100 },
  { title: '行未税总额', field: 'untaxedTotalAmount', width: 100 },
  { title: '币种', field: 'currency', width: 100 },
  // { title: '行含税人民币总额（参考）', field: 'taxedCnyAmount', width: 170 },
  { title: '行未税人民币总额（参考）', field: 'untaxedCnyAmount', width: 170 },
  { title: '商品行运费', field: 'shippingAmount', width: 100 },
  { title: '商品行折扣', field: 'discountAmount', width: 100 },
  { title: '订单行毛利率', field: 'poItemLastGrossMargin', slot: true, width: 100 },
  { title: '退货原因', field: 'returnReason', enums: 'returnReason', width: 120 },
  { title: '供应商订单号', field: 'supplierOrderNo', width: 120 },
  { title: '代管代发', field: 'isEscrow', isBoolean: true, width: 80 },
  { title: '供应商物料号', field: 'supplierMaterialNo', width: 120 },
  { title: '工厂', field: 'factoryCode', enums: 'factoryCode', width: 120 },
  { title: '计划行数量', field: 'planDeliveryQty', width: 100 },
  { title: '商品行交期(下单时）', field: 'standardLeadDays', width: 140 },
  { title: '预计到货天数', field: 'expectedLeadDays', sortable: true, width: 120 },
  { title: '交期天数差异', field: 'diffLeadDays', sortable: true, width: 120 },
  { title: '交货进度', field: 'leadProcessRate', slot: true, width: 120 },
  { title: '剩余交货天数', field: 'leftLeadDays', sortable: true, width: 120 },
  { title: '交货单', field: 'idoNoList', slot: true, width: 120 },

  { title: '转储未制单数量', field: 'stoNotDnQty', width: 120 },
  { title: '转储已制单数量', field: 'stoDnQty', width: 120 },
  { title: '转储已制单未发货', field: 'stoDnUnclearedQty', width: 120 },
  { title: '转储已发货数量', field: 'stoDnClearedQty', width: 120 },
  { title: '外向交货单号', field: 'dnNo', width: 120 },
  { title: '外向交货单过账日期', field: 'stoDnPostDate', width: 120 },
  { title: '转储在途数量', field: 'stoDnOnWayQty', width: 100 },

  { title: '销售订单（客服）', field: 'demanderCustomerService', width: 120 },
  { title: '发货状态', field: 'deliveryOrderStatusList', enums: 'deliveryOrderStatus', slot: true, width: 80 }, // deliveryStatusList 改为list，加字典
  { title: '发货日期', field: 'deliveryDateList', slot: true, width: 120 },
  { title: '送货方式', field: 'deliveryWayList', enums: 'deliveryWay', slot: true, width: 120 }, // deliveryWayList 改为list

  { title: '物流公司', field: 'logisticsNameList', slot: true, width: 120 }, // deliveryWayList 改为list
  { title: '物流单号', field: 'logisticsCodeList', slot: true, width: 120 },
  { title: '签收信息', field: 'signInfo', slot: true, width: 120 },
  { title: '送货车牌号', field: 'deliveryVehicleNumberList', slot: true, width: 120 },
  { title: '司机姓名', field: 'driverNameList', slot: true, width: 120 },
  { title: '电话号码', field: 'driverPhoneList', slot: true, width: 120 },
  { title: '签收单', field: 'isDeliveryOrderSignedList', slot: true, isBoolean: true, width: 80 },
  { title: '送货单已发数量', field: 'deliveryOrderQtyList', slot: true, width: 120 }, // deliveryWayList 改为list

  { title: '订单渠道', field: 'orderChannelList', slot: true, width: 120 },
  { title: '指定渠道客户', field: 'designatedChannelCustomerCode', slot: true, width: 120 },
  { title: '指定渠道打标', field: 'designatedChannel', slot: true, width: 120 },
  { title: '跟踪单号', field: 'trackNo', width: 120 },
  { title: '专供销售单号', field: 'specialSupplySo', width: 120 },
  { title: '专供销售单行号', field: 'specialSupplySoItem', width: 120 },
  { title: '客服', field: 'customerService', width: 120 },
  { title: '销售', field: 'soSellerName', width: 120 },
  { title: '销售订单数量', field: 'soItemQty', width: 120 },
  { title: 'SO发货状态', field: 'soShipStatus', enums: 'soShipStatus', width: 120 }, // dicList
  { title: '要求交期', field: 'soDeliveryDate', width: 120 },
  { title: '签单返回', field: 'soSigningBack', enums: 'signingBack', width: 120 },
  { title: '交付主管', field: 'soDeliveryManager', width: 120 },
  { title: '物料组', field: 'materialGroupName', width: 120 },
  { title: '品牌', field: 'brandName', width: 120 },
  { title: '产品定位', field: 'productPosition', width: 120 },
  { title: '商品经理', field: 'productManager', width: 120 },
  { title: '订单备注', field: 'orderRemark', width: 120 },
  { title: '打印备注', field: 'printRemark', width: 120 },
  { title: '已开票数量', field: 'invoiceQuantity', width: 100 },
  { title: '已开票金额', field: 'invoiceAmount', width: 100 },
  { title: '未开票数量', field: 'notInvoiceQuantity', width: 100 },
  { title: '未开票金额', field: 'notInvoiceAmount', width: 100 },
  { title: '历史预付总金额', field: 'historyPaymentAmount', width: 120 },
  { title: '递交银行付款时间', field: 'submitBankTime', width: 120 },
  { title: '承诺变更次数', field: 'promiseUpdateFrequency', width: 120 },
  { title: '订单来源', field: 'source', width: 120 },
  { title: '成本中心', field: 'costCenterText', width: 120 },
  { title: '成本中心代码', field: 'costCenter', width: 120 },
  { title: '总账科目', field: 'generalLedgerAccount', width: 120 },
  { title: '总账科目描述', field: 'generalLedgerAccountText', enums: 'generalLedger', width: 120 },
  { title: '触发审批内容', field: 'approveTriggerDetail', width: 120 },
  { title: '申请下单原因', field: 'orderReason', width: 120 },
  { title: '调拨原因', field: 'transferReason', enums: 'transferReason', width: 150 },
  { title: '是否实单', field: 'isRealOrder', slot: true, width: 100 },
  { title: '删除原因', field: 'deleteReason', enums: 'deleteReason', width: 120 },
  { title: '收货联系人', field: 'receiveContactName', width: 120 },
  { title: '收货联系电话', field: 'receiveContactPhoneEncryption', slot: true, width: 120 },
  { title: '收货联系地址', field: 'receiveAddressDetail', width: 120 },
  { title: '自动生成送货单', field: 'isAutoDeliveryOrder', enums: 'isAutoDeliveryOrder', width: 120 },
  { title: 'OA流程编号', field: 'oaNo', width: 100 }
]

export {
  columns,
  z002Columns
}
