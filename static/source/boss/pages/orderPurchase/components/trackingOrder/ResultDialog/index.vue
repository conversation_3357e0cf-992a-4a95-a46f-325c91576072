<template>
  <el-dialog
    title="更新结果"
    :visible="resultVisible"
    :width="dataResult.detailList && dataResult.detailList.length ? '800px': '400px'"
    :before-close="beforeClose"
  >
    <el-row style="margin-top: -10px; margin-bottom: 10px">
      <span style="margin-right: 20px">成功行数：{{dataResult.successCount}}</span>
      <span>失败行数：{{dataResult.failCount}}</span>
    </el-row>
    <el-table v-if="dataResult.detailList && dataResult.detailList.length" :data="dataResult.detailList || []" style="width: 100%">
      <el-table-column prop="poNo" label="采购单号" width="180" />
      <el-table-column prop="itemNo" label="项目行" width="180" />
      <el-table-column prop="planNo" label="计划行" width="180" />
      <el-table-column prop="message" label="失败描述" />
    </el-table>
  </el-dialog>
</template>

<script>

export default {
  props: {
    resultVisible: {
      required: true,
      type: Boolean,
      default: false
    },
    updateVisible: {
      type: Function,
      default: () => {}
    },
    dataResult: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    beforeClose (done) {
      this.updateVisible('resultVisible', false)
      done()
    }
  }
}
</script>

<style lang="scss" scoped>
.desc {
  margin: 10px 0;
  .strong {
    font-weight: bold;
  }
}
.batch-btn {
  width: 120px;
}
</style>
