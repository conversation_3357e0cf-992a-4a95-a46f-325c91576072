<template>
  <el-form class="tracking-order-grid-form" :model="searchForm" ref="searchForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="140px">
    <el-row v-for="(row, rowIndex) in getRows" :gutter="10" :key="rowIndex">
      <template v-if="showFilter ? true: rowIndex < 1">
        <el-col v-for="(column, index) in row" :key="index" :span="column.span">
          <el-form-item :label="column.name" :prop="column.prop">
            <AllowCreateSelect v-if="column.type === 'select' && isAllowCreateSelect(column.prop)" :data.sync="searchForm[column.prop]" @change="val=>searchForm[column.prop]=val" :optionLists="buildOptions(column.enums)" />
            <el-select
              v-else-if="column.type === 'select'"
              v-model="searchForm[column.prop]"
              :allow-create="column.prop==='purchaseGroupList'"
              style="width: 100%"
              collapse-tags
              :multiple="column.multiple"
              filterable
              :clearable="column.clearable"
              default-first-option
            >
              <el-option
                v-for="item in buildOptions(column.enums)"
                :key="item.value"
                :label="column.hideCode ? item.name : item.value+' '+item.name"
                :value="item.value">
              </el-option>
            </el-select>
            <SelectSupplier
              v-if="column.type === 'custom' && column.prop === 'supplierNoList'"
              :multiple="column.multiple"
              @change="handleSupplierChange"
              :data.sync="searchForm[column.prop]"
              :isCanDisabled="false"
              collapse-tags
            />
            <SelectBrand
              v-if="column.type === 'custom' && column.prop === 'brandNameList'"
              :data.sync="searchForm[column.prop]"
              :multiple="column.multiple"
              collapse-tags
            />
            <SelectMaterialGroup
              v-if="column.type === 'custom' && column.prop === 'materialGroupNumList'"
              :data.sync="searchForm[column.prop]"
              :multiple="column.multiple"
              collapse-tags
            />
            <el-date-picker
              v-if="column.type === 'date-picker'"
              v-model="searchForm.orderDate"
              style="width:100%"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="-"
              start-placeholder="开始"
              end-placeholder="结束"
            >
            </el-date-picker>
            <el-input
              v-if="column.type === 'input'"
              v-model="searchForm[column.prop]"
              clearable
              @change="(value)=>handleInputChange(column.prop, value)"
              :placeholder="column.placeholder || column.name"
            />
          </el-form-item>
        </el-col>
      </template>
      <div v-if="rowIndex === 7" class="button-group">
        <el-button type="primary" :loading="searchLoading" @click="validateSearch">查询</el-button>
        <el-button type="default" @click="resetFields">重置</el-button>
        <el-button type="default" :icon="showFilter?'el-icon-arrow-up':'el-icon-arrow-down'" @click="handleFold">{{showFilter ? '收起' : '展开'}}</el-button>
      </div>
    </el-row>
  </el-form>
</template>
<script>
import { mapState } from 'vuex'
import SelectBrand from '../common/SelectBrand'
import SelectSupplier from '../common/SelectSupplier'
import SelectMaterialGroup from '../common/SelectMaterialGroup'
import AllowCreateSelect from '../common/MultipleSelect'
import { getAllDictList, buildOptions, getUserCompany } from '@/utils/mm'
import { safeRun, sensors } from '@/utils/index'
import { fillRules, forms, sortRows } from './form'
// const orderDateRule = {
//   message: '请选择订单创建日期！',
//   required: true,
//   trigger: [ 'blur', 'change' ]
// }
export default {
  name: 'trackingOrderForm',
  data () {
    const rules = fillRules(forms)
    return {
      rules,
      searchLoading: false,
      showFilter: true,
      searchForm: {
        factoryCodeList: [],
        purchaseGroupList: [],
        orderTypeList: '',
        orderNoList: '',
        orderDate: '',
        supplierOrderNoList: '',
        supplierNoList: [],
        skuNoList: '',
        warehouseLocationList: '',
        shipWarehouseLocationList: '',
        trackNo: '',
        supplierMaterialNo: '',
        materialGroupNumList: '',
        isQueryDemanderCustomerService: null,
        orderScope: '',
        isDeleted: null,
        isEscrow: 0,
        lightStatusList: [],
        brandNameList: '',
        isTc: '',
        productPosition: '',
        prePayment: false,
        deliveryChangeReason: '',
        poUnconfirmedReason: ''
      }
    }
  },
  async mounted () {
    await getAllDictList(this)
    this.initSearchFields()
  },
  components: { SelectSupplier, SelectBrand, SelectMaterialGroup, AllowCreateSelect },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      purchaseGroupList: state => state.orderPurchase.purchaseList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      productPositionOptions: state => state.orderPurchase.productPositionOptions
    }),
    getRows () {
      return sortRows(forms)
    }
  },
  beforeDestroy () {
    window._console.black('beforeDestroy delete window.logistics_orderNo')
    delete window.logistics_orderNo
  },
  activated () {
    window._console.black('activated', window.logistics_orderNo)
    if (window.logistics_orderNo) {
      this.searchForm.factoryCodeList = []
      this.searchForm.isDeleted = null
      this.searchForm.orderScope = ''
      this.searchForm.orderDate = ''
      this.searchForm.purchaseGroupList = []
      this.searchForm.orderNoList = window.logistics_orderNo
      setTimeout(() => {
        this.$refs.searchForm.clearValidate()
      }, 100)
    }
  },
  methods: {
    findGrid () {
      let ret = {}
      safeRun(() => {
        ret = this.$parent.$children.find(child => child.$options.name === 'trackingOrderGird')
      })
      return ret
    },
    isAllowCreateSelect(prop) {
      return ['purchaseGroupList', 'warehouseLocationList', 'shipWarehouseLocationList'].includes(prop)
    },
    handleFold () {
      this.showFilter = !this.showFilter
      const grid = this.findGrid()
      setTimeout(() => {
        safeRun(() => {
          grid.calcLeftHeight()
        })
      }, 100)
    },
    handleInputChange (prop, value) {
      console.log(prop, value, this.rules)
    },
    async initSearchFields () {
      window._console.black('initSearchFields', window.logistics_orderNo)
      const { poNo, reportLightStatus, orderNo } = this.$route.query
      if (orderNo) {
        this.searchForm.orderNoList = orderNo;
      }
      if (window.logistics_orderNo) return
      if (poNo) return this.initPoNo()
      const defaultCompany = await getUserCompany()
      if (defaultCompany) {
        this.searchForm.factoryCodeList = [defaultCompany]
      }
      // this.searchForm.factoryCodeList = ['1000']
      this.searchForm.isDeleted = 0
      this.searchForm.isEscrow = 0
      this.searchForm.orderScope = '0'
      this.searchForm.isQueryDemanderCustomerService = 0
      if (reportLightStatus) {
        this.searchForm.lightStatusList = [Number(reportLightStatus)]
        setTimeout(this.validateSearch, 200)
      }
      // const dateNow = new Date()
      // const dateBefore30Days = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      // this.searchForm.orderDate = [dateBefore30Days, dateNow]
      this.findDefaultPurchaseGroup()
      this.initPoNo()
    },
    initPoNo () {
      const { poNo } = this.$route.query
      if (poNo) {
        this.searchForm.orderNoList = poNo
        setTimeout(this.validateSearch, 200)
      }
    },
    findDefaultPurchaseGroup () {
      const options = this.buildOptions('purchaseGroupList')
      const username = window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
      const purchaseGroup = options.find(option => option.securityUsername === username)
      if (purchaseGroup && purchaseGroup.value) {
        this.searchForm.purchaseGroupList = [purchaseGroup.value]
      }
    },
    handleSupplierChange (val) {
      console.log(val)
      // this.searchForm.supplierNoList = val.map(item => item.supplierNo)
      // if (Array.isArray(val) && val.length) {
      //   this.rules.orderDate = null
      // } else if (!this.rules.orderDate) {
      //   this.rules.orderDate = orderDateRule
      // }
    },
    string2Array (string) {
      if (Array.isArray(string)) return string
      let ret = []
      if (string && string.split) {
        ret = string.split(/\s+|，|,/).filter(Boolean)
      }
      return ret
    },
    formatSubmit (form) {
      const data = JSON.parse(JSON.stringify(form))
      if (data.orderDate) {
        data.orderDateBegin = data.orderDate[0]
        data.orderDateEnd = data.orderDate[1]
        delete data.orderDate
      }
      // data.productPosition = data.productPosition ? parseInt(data.productPosition) : ''
      data.orderNoList = this.string2Array(data.orderNoList)
      data.supplierOrderNoList = this.string2Array(data.supplierOrderNoList)
      data.orderTypeList = this.string2Array(data.orderTypeList)
      data.warehouseLocationList = this.string2Array(data.warehouseLocationList)
      data.shipWarehouseLocationList = this.string2Array(data.shipWarehouseLocationList)
      data.materialGroupNumList = this.string2Array(data.materialGroupNumList)
      data.brandNameList = this.string2Array(data.brandNameList)
      data.skuNoList = this.string2Array(data.skuNoList)
      data.oaNoList = this.string2Array(data.oaNoList)
      data.prePayment = data.prePayment === '' ? false : data.prePayment

      if (Array.isArray(data.materialGroupNumList)) {
        data.materialGroupNumList = data.materialGroupNumList.map(item => item.materialGroupNum ? item.materialGroupNum : item)
      }
      if (Array.isArray(data.supplierNoList)) {
        data.supplierNoList = data.supplierNoList.map(item => item.supplierNo)
      }
      delete data.supplier
      for (let item in data) {
        if (typeof data[item] === 'string' && data[item].trim) {
          data[item] = data[item].trim()
        }
      }
      return data
    },
    trimEmpty(data) {
      for (let prop in data) {
        if (!data[prop]) {
          delete data[prop]
        }
        if (Array.isArray(data[prop]) && data[prop].length === 0) {
          delete data[prop]
        }
      }
    },
    validateListLength (data) {
      let ret = true
      if (data.supplierOrderNoList && data.supplierOrderNoList.length > 200) {
        this.$message.error('供应商订单号最多200个！')
        ret = false
      }
      if (data.orderNoList && data.orderNoList.length > 200) {
        this.$message.error('采购订单号最多200个！')
        ret = false
      }
      return ret
    },
    validateSearch () {
      let data = this.formatSubmit(this.searchForm)
      if (!this.validateListLength(data)) return
      if (Array.isArray(data.orderNoList) && data.orderNoList.length) {
        this.$refs.searchForm.clearValidate()
        this.handleSearch('initPageNo')
      } else {
        this.$refs.searchForm.validate(async (valid) => {
          if (valid) {
            this.handleSearch('initPageNo')
          }
        })
      }
      sensors('PurchaseWorkbenchQueryClick', { label_name: '跟单报表' })
    },
    setParentForm () {
      let data = this.formatSubmit(this.searchForm)
      this.$emit('setSearchForm', data)
    },
    handleSearch (initPageNo) {
      console.log('handle Search...')
      let data = this.formatSubmit(this.searchForm)
      this.$emit('setSearchForm', data, true, initPageNo)
    },
    buildOptions (prop) {
      if (prop === 'isDeleted') {
        return [
          { name: '是', value: 1 },
          { name: '否', value: 0 }
        ]
      }
      if (prop === 'isQueryDemanderCustomerService') {
        return [
          { name: '是', value: 1 },
          { name: '否', value: 0 }
        ]
      }
      if (prop === 'reportLightStatus') {
        return [
          { name: '绿', value: 1 },
          { name: '黄', value: 2 },
          { name: '红', value: 3 }
        ]
      }
      if (prop === 'productPosition') {
        return this.productPositionOptions
      }
      if (prop === 'prePayment') {
        return [
          { name: '是', value: true },
          { name: '否', value: false }
        ]
      }
      return buildOptions(prop)
    },
    resetFields () {
      this.$refs.searchForm.resetFields()
      // this.searchForm.orderDate = ''
      // if (!this.rules.orderDate) {
      //   this.rules.orderDate = orderDateRule
      // }
      this.searchForm.supplierNoList = []
    }
  }
}
</script>
<style scoped lang="scss">
.button-group{
  margin-right: 5px;
  float: right;
}
</style>
