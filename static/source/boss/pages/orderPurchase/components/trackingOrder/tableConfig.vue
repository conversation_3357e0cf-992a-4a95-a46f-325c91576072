<template>
  <el-dialog title="表头配置">
  </el-dialog>
</template>

<script>
import { mapState } from 'vuex'

export default {
  props: {
    tableConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
    return {}
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      purchaseGroupList: state => state.orderPurchase.purchaseList,
      factoryList: state => state.orderPurchase.companyFactoryList
    })
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
</style>
