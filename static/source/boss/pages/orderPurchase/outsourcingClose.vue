<template>
  <div class="list-container">
    <el-form ref="ruleForm" :model="searchForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="工厂" prop="factoryCode">
            <el-select
              v-model="searchForm.factoryCode"
              filterable
              default-first-option
              clearable
              style="width:100%"
              placeholder="请选择工厂"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.factoryCode+item.factoryName"
                :label="item.factoryCode+' '+item.factoryName"
                :value="item.factoryCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="供应商" prop="supplierNo">
            <SelectSupplier
              clearable
              :data.sync="searchForm.supplier"
              @change="handleChange('supplier', $event)"
              />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="采购员" prop="purchaseGroup">
            <el-select
              v-model="searchForm.purchaseGroup"
              filterable
              default-first-option
              clearable
              style="width:100%"
              placeholder="选择采购员"
            >
              <el-option
                v-for="item in purchaseList"
                :key="item.groupCode"
                :label="item.groupCode+' '+item.userName"
                :value="item.groupCode">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="16">
          <el-form-item label="订单号检索" prop="orderNos">
            <el-input
              v-model="searchForm.orderNos"
              placeholder="采购单号 或 SAP订单号 均支持搜索，同时支持10个单号，以空格或换行符分隔"
              filterable
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="SKU编码" prop="skuNos">
            <el-input
              v-model="searchForm.skuNos"
              filterable
              clearable
              style="width:100%"
              placeholder="最多支持10个SKU按空格隔开搜索"
              />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="8">
          <el-form-item label="收货比例%" prop="receivedRadioMin">
              <el-col :span="10">
                <el-input-number
                  v-model="searchForm.receivedRadioMin"
                  style="width:100%"
                  :precision="0"
                  :min="0"
                  :max="100"
                  :controls="false"
                  placeholder="请输入"
                  />
              </el-col>
              <el-col style="text-align:center" :span="4">—</el-col>
              <el-col :span="10">
                <el-input-number
                  v-model="searchForm.receivedRadioMax"
                  style="width:100%"
                  :precision="0"
                  :min="searchForm.receivedRadioMin"
                  :max="100"
                  :controls="false"
                  placeholder="请输入"
                  />
              </el-col>
          </el-form-item>
        </el-col>
        <el-col :span="12" style="text-align: left;padding-left: 130px;">
          <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="handleSearch">
            查询
          </el-button>
          <el-button style="width: 80px" @click="resetForm" >重置</el-button>
        </el-col>
      </el-row>
    </el-form>
     <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane v-for="(item,index) in tabs" :key="index" :label="item.label" :name="item.name">
            <Grid :listData="listData" :tableLoading="tableLoading" :tablePage="tablePage" @handlePageChange="handlePageChange"  @toClose="toClose"  :selectList.sync="selectList"></Grid>
        </el-tab-pane>
   </el-tabs>
    <el-dialog title="关单结果" :visible.sync="dialogTableVisible">
      <div>关单成功{{result.successCount}}行，失败{{result.failCount}}行。</div>
      <el-table :data="result.failReasonList">
        <el-table-column property="orderNo" label="采购订单" ></el-table-column>
        <el-table-column property="itemNo" label="项目行"></el-table-column>
        <el-table-column property="reason" label="失败描述" ></el-table-column>
      </el-table>
    </el-dialog>

  </div>
</template>

<script>
import { mapState } from 'vuex'
import { safeRun } from '@/utils/index'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { pageOutSourceClosePo, batchCloseOutSourcePo, pageClosePo, batchClosePo } from '@/api/mm'
import Grid from '@/pages/orderPurchase/components/outsourceingClose/Grid'

const tabs = [
  {
    label: '委外关单',
    name: 'outSource'
  },
  {
    label: '标准关单',
    name: 'stardardClose'
  }
]
export default {
  name: 'outsourcingClose',
  components: { SelectSupplier, Grid },
  data () {
    return {
      disabled: true,
      selectList: [],
      searchForm: {
        factoryCode: '1000',
        supplier: {},
        supplierNo: '',
        purchaseGroup: '',
        orderNos: '',
        skuNos: '',
        receivedRadioMin: 97,
        receivedRadioMax: 100
      },
      searchLoading: false,
      tableLoading: false,
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 200
      },
      listData: [],
      outSourceListData: [],
      stardardCloseListData: [],
      rules: {
        factoryCode: [
          { required: true, message: '请选择工厂', trigger: 'change' }
        ]
      },
      dialogTableVisible: false,
      result: {},
      tabs,
      activeName: 'outSource',
      listSearchApi: pageOutSourceClosePo,
      closeApi: batchCloseOutSourcePo
    }
  },
  created () {
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    if (this.purchaseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
  },
  mounted() {
    console.log(this.$refs.grid)
  },
  computed: {
    ...mapState({
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      purchaseList: state => state.orderPurchase.purchaseList
    }),
    factoryList: {
      get () {
        let factories = []
        for (let i = 0; i < this.companyFactoryList.length; i++) {
          factories.push(...this.companyFactoryList[i].factoryList)
        }
        return factories
      }
    }
  },
  methods: {
    handleChange (type, event) {
      if (type === 'supplier') {
        this.searchForm.supplierNo = event.supplierNo
      }
    },
    resetForm () {
      this.$refs.ruleForm.resetFields()
      this.searchForm.supplier = {}
      this.searchForm.receivedRadioMin = 97
      this.searchForm.receivedRadioMax = 100
      this.getOutsourcingList()
    },
    formatParams(params) {
      let form = { ...params };
      form.orderNos = safeRun(() =>
        form.orderNos
          .split(/\s+|,|，/).filter((e) => e)
      );
      form.skuNos = safeRun(() =>
        form.skuNos
          .split(/\s+|,|，/).filter((e) => e)
      );
      delete form.supplier
      return form;
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.orderNos.length > 200) {
          ret = false
          this.$message.error('订单号不能超过200个！')
        }
      })
      if (!ret) return ret
      safeRun(() => {
        if (params.skuNos.length > 500) {
          ret = false
          this.$message.error('SKU编码不能超过500个！')
        }
      })
      return ret
    },
    handleClick(tab) {
      const { name } = tab
      if (name === 'outSource') {
        this.listSearchApi = pageOutSourceClosePo;
        this.closeApi = batchCloseOutSourcePo
      }
      if (name === 'stardardClose') {
        this.listSearchApi = pageClosePo;
        this.closeApi = batchClosePo
      }
      this.getOutsourcingList()
    },
    async getOutsourcingList () {
      this.selectList = []
      try {
        let params = this.formatParams(this.searchForm);
        if (!this.validate(params)) return
        this.tableLoading = true
        this.searchLoading = true
        params = {
          ...params,
          pageNo: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize
        }
        const res = await this.listSearchApi(params)
        // if (this.activeName === 'outSource') {
        //   this.outSourceListData = res.rows
        // }
        // if (this.activeName === 'stardardClose') {
        //   this.stardardCloseListData = res.rows
        // }
        this.listData = res.rows
        this.tablePage.total = res.total
        if (this.listData.length === 0) {
          this.$message.info('无符合条件的采购订单')
        }
        this.tableLoading = false
        this.searchLoading = false
      } catch (error) {
        this.tableLoading = false
        this.searchLoading = false
        console.log(error);
      }
    },
    handleSearch () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.tablePage.currentPage = 1
          this.getOutsourcingList()
        } else {
          return false;
        }
      })
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.getOutsourcingList()
        } else {
          return false;
        }
      })
    },
    async toClose () {
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      try {
        const itemList = this.selectList.map(item => {
          let value = {
            itemNo: item.itemNo,
            orderNo: item.orderNo
          }
          return value
        })
        const res = await this.closeApi({
          itemList,
          operateUser: window.CUR_DATA.user && window.CUR_DATA.user.name
        })

        if (res.failCount) {
          this.dialogTableVisible = true
          this.result = res
        } else {
          this.$message.success(`关单成功${res.successCount}行`)
        }
        this.tablePage.currentPage = 1
        this.getOutsourcingList()
        this.loading.close()
      } catch (error) {
        this.loading.close()
        console.log(error);
      }
    }

  }
}
</script>

<style lang="scss" scoped>
.list-container {
  margin: 10px 20px;
}
</style>
