<template>
  <div class="tracking-order" v-loading="pageLoading">
    <TrackingOrderForm
      ref="searchForm"
      :tableInfo="tableInfo"
      @setSearchForm="setSearchForm"
      @handleTableInfo="handleTableInfo"
    />
    <TrackingOrderGrid
      ref="grid"
      :tableLoading="tableLoading"
      :listData="listData"
      :tableInfo="tableInfo"
      :updateLoading="updateLoading"
      :updateVisible="updateVisible"
      @exportDetail="validExportDetail"
      @sortTable="sortTable"
      @handleTableInfo="handleTableInfo"
      @showEditDialog="showEditDialog"
      @saveData="saveData"
      @handleSearch="handleSearch"
    />
    <BatchUpdate
      @uploadSuccess="validSearch"
      type="0"
      :updateVisible="updateVisible"
      :batchUpdateVisible="batchUpdateVisible"
    />
    <ResultDialog
      :dataResult="dataResult"
      :updateVisible="updateVisible"
      :resultVisible="resultVisible"
    />
  </div>
</template>

<script>
import TrackingOrderForm from './components/trackingOrder/form.vue'
import TrackingOrderGrid from './components/trackingOrder/grid.vue'
import ResultDialog from './components/trackingOrder/ResultDialog'
import BatchUpdate from './components/trackingOrder/BatchUpdateDialog'
import { buildOptions, getAllDictList } from '@/utils/mm'
import { deepClone, safeRun, Storage, sensors } from '@/utils/index.js'
import { getTrackingOrderList, exportTrackingOrderDetail, exportTrackingOrderDetailAsync, batchEditTrackingOrder } from '@/api/mm'
import { writeFile } from '@boss/excel'
import { columns } from './components/trackingOrder/grid.js'
import { mapState } from 'vuex'

export default {
  name: 'trackingOrder',
  provide () {
    return {
      setDataResult: this.setDataResult
    }
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole,
      trackingOrderConfig: state => state.orderPurchase.trackingOrderConfig
    })
  },
  components: { TrackingOrderForm, TrackingOrderGrid, ResultDialog, BatchUpdate },
  data () {
    return {
      tableInfo: {
        total: 0,
        pageNo: 1,
        pageSize: 20
      },
      tableLoading: false,
      pageLoading: false,
      editBatchOrder: false,
      searchForm: {},
      listData: [],
      sortInfo: {},
      resultVisible: false,
      batchUpdateVisible: false,
      dataResult: {
        failCount: 0,
        successCount: 0,
        detailList: []
      }
    }
  },
  async created () {
    await getAllDictList(this)
    this.$store.dispatch('orderPurchase/getTrackingOrderConfig')
    this.$store.dispatch('orderPurchase/getProductPositionOptions')
    sensors('PurchaseWorkbenchLabelPageView', { label_name: '跟单报表' })
  },
  methods: {
    parsePurchaseGroup(data) {
      if (Array.isArray(data.purchaseGroupList) && data.purchaseGroupList.length) {
        const pgOptions = buildOptions('purchaseGroupList')
        const groups = []
        const matchedGroups = data.purchaseGroupList.filter(group => /\*/gi.test(group)).map(item => item.toUpperCase())
        for (let group of matchedGroups) {
          const rp = group.replace(/\*/gi, '')
          groups.push(...pgOptions.filter(item => item.value.indexOf(rp) > -1).map(item => item.value))
        }
        const newList = data.purchaseGroupList.filter(group => !/\*/gi.test(group))
        newList.push(...groups)
        data.purchaseGroupList = newList
      }
    },
    betterWriteFile (res) {
      const fileName = new Date().toLocaleDateString().replace(/\//g, '-') + '跟单报表导出明细.xlsx'
      const data = this.formateJsonToExcel(res)
      writeFile(data, fileName, { raw: false })
    },
    emptyValue (value) {
      return value ? value + '' : ''
    },
    mapValueToName (prop, value) {
      let ret = value
      const props = [
        { prop: 'warningType', enums: 'poFollowReportWarn' },
        { prop: 'tag', enums: 'poFollowReportTag' },
        { prop: 'approveStep', enums: 'approveStatus' },
        { prop: 'lightStatus', enums: 'reportLightStatus' },
        { prop: 'soSigningBack', enums: 'signingBack' },
        { prop: 'openVcStatus', enums: 'openVcStatus' },
        { prop: 'soShipStatus', enums: 'soShipStatus' },
        { prop: 'coopMode', enums: 'cooperateMode' },
        { prop: 'followContractBack', enums: 'poContractBack' },
        'orderType',
        'purchaseGroup',
        'paymentTermCode',
        'approveStep',
        'isPlanDeliveryDone',
        'isDeliveryOrderSigned',
        'isDeleted',
        'isEscrow',
        'isUrgent',
        'isConfirmed',
        'isCustomerAccept',
        'isSigningBackAccepted',
        'isCustomerReceived'
      ]
      const boolList = [
        'isPlanDeliveryDone', 'isDeliveryOrderSigned',
        'isEscrow', 'isUrgent', 'isConfirmed', 'isCustomerAccept',
        'isDeleted', 'isSigningBackAccepted', 'isCustomerReceived'
      ]
      // eslint-disable-next-line eqeqeq
      const findProp = props.find(item => item && item.prop == prop)
      if (props.includes(prop) || findProp) {
        let optProp = prop
        if (findProp) {
          optProp = findProp.enums
        }

        let options = buildOptions(optProp)
        if (boolList.includes(optProp)) {
          options = [
            { value: 1, name: '是' },
            { value: 0, name: '否' }
          ]
        }
        if (optProp === 'lightStatus') {
          options = [
            { value: 1, name: '绿色' },
            { value: 2, name: '黄色' },
            { value: 3, name: '红色' }
          ]
        }
        if (Array.isArray(options)) {
          // eslint-disable-next-line eqeqeq
          const item = options.find(item => item.value == value)
          if (item) {
            ret = this.emptyValue(item.name)
          }
        }
      }
      return ret
    },
    // list按规则转string
    mapDeliveryList (list, enums) {
      let ret = ''
      if (Array.isArray(list)) {
        list = list.map((item, index) => {
          if (enums) {
            let opts = buildOptions(enums)
            if (enums === 'isDeliveryOrderSigned') {
              opts = [ { value: 1, name: '是' }, { value: 0, name: '否' } ]
            }
            // eslint-disable-next-line eqeqeq
            const findItem = opts.find(_item => _item.value == item)
            if (findItem) {
              item = findItem.name
            }
          }
          return list.length > 1 ? `No${index + 1}:${item}` : item
        })
        ret = list.join('\n')
      }
      return ret
    },
    getHiddenFields () {
      let hiddenProps = []
      safeRun(() => {
        if (localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE) {
          hiddenProps = JSON.parse(localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE)['tracking-order-grid'].split(',')
        }
      })
      return hiddenProps
    },
    filterHiddenFields (columns) {
      // 导出过滤隐藏的列
      let hiddenProps = []
      safeRun(() => {
        if (localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE) {
          hiddenProps = JSON.parse(localStorage.VXE_TABLE_CUSTOM_COLUMN_VISIBLE)['tracking-order-grid'].split(',')
        }
      })
      if (hiddenProps.length) {
        columns = columns.filter(colProp => {
          return !hiddenProps.find(prop => colProp.field === prop)
        })
      }
      return columns
    },
    formateDateProps (data, title) {
      if (data[title] && /日期/.test(title) && data[title].length && typeof data[title] === 'string') {
        safeRun(() => {
          const oldDate = data[title]
          data[title] = new Date(data[title])
          safeRun(() => {
            // xlsx date format +8 time 43s diff
            // https://github.com/SheetJS/sheetjs/issues/1470#issuecomment-501108554
            const isChrome = !!~window.navigator.userAgent.toLowerCase().indexOf('chrome')
            const plus8Zone = new Date().getTimezoneOffset()
            if (plus8Zone === -480 && isChrome) {
              data[title] = new Date(new Date(data[title]).getTime() - 43 * 1000)
            }
          })
          if (data[title].toString && data[title].toString() === 'Invalid Date') {
            data[title] = oldDate
          }
        })
      }
    },
    formateJsonToExcel (res) {
      const mapList = [
        'deliveryDateList', 'logisticsNameList', 'logisticsCodeList',
        'deliveryVehicleNumberList', 'driverNameList', 'driverPhoneList',
        'deliveryOrderQtyList'
      ]
      const enumList = [
        { prop: 'deliveryOrderStatusList', enums: 'deliveryOrderStatus' },
        { prop: 'deliveryWayList', enums: 'deliveryWay' },
        { prop: 'isDeliveryOrderSignedList', enums: 'isDeliveryOrderSigned' }
      ]
      let extendColumns = [{ title: '是否删除', field: 'isDeleted' }, ...columns]
      extendColumns.splice(3, 0, { title: 'SAP订单号', field: 'sapOrderNo' })
      extendColumns = this.filterHiddenFields(extendColumns)
      console.log('after filter columns:', extendColumns)
      let data = res.map(item => {
        let tmp = {}
        for (let prop of extendColumns) {
          tmp[prop.title] = this.mapValueToName(prop.field, item[prop.field])
          this.formateDateProps(tmp, prop.title)
          if (mapList.includes(prop.field)) {
            tmp[prop.title] = this.mapDeliveryList(tmp[prop.title])
          }
          const prop2 = enumList.find(item => item.prop === prop.field)
          if (prop2) {
            tmp[prop.title] = this.mapDeliveryList(tmp[prop.title], prop2.enums)
          }
        }
        return tmp
      })
      return data
    },
    showEditDialog () {
      this.editBatchOrder = true
    },
    updateLoading (loading) {
      this.pageLoading = loading
    },
    validSearch () {
      if (this.$refs.searchForm.searchForm.orderNoList) {
        this.$refs.searchForm.$refs.searchForm.clearValidate()
        this.$refs.searchForm.setParentForm()
        this.handleSearch()
        return
      }
      this.$refs.searchForm.$refs.searchForm.validate(valid => {
        if (valid) {
          this.$refs.searchForm.setParentForm()
          this.handleSearch()
        }
      })
    },
    validExportDetail () {
      if (this.$refs.searchForm.searchForm.orderNoList) {
        this.$refs.searchForm.setParentForm()
        this.$refs.searchForm.$refs.searchForm.clearValidate()
        this.exportDetail()
        return
      }
      this.$refs.searchForm.$refs.searchForm.validate(valid => {
        if (valid) {
          this.$refs.searchForm.setParentForm()
          this.exportDetail()
        }
      })
    },
    sortTable (order, property) {
      if (!this.listData.length) return
      this.tableInfo.pageNo = 1
      if (this.$refs.searchForm.searchForm.orderNoList) {
        this.$refs.searchForm.$refs.searchForm.clearValidate()
        this.$refs.searchForm.setParentForm()
        this.sortInfo = { order, property }
        this.handleSearch()
        return
      }
      this.$refs.searchForm.$refs.searchForm.validate(valid => {
        if (valid) {
          this.$refs.searchForm.setParentForm()
          this.sortInfo = { order, property }
          this.handleSearch()
        }
      })
    },
    filterFields (data) {
      const fields = this.getHiddenFields()
      if (Array.isArray(fields) && fields.length) {
        data.excludeFields = fields
      }
    },
    setDataShowFields (data) {
      safeRun(() => {
        let columnSequence = Storage.getItem('trackingOrderGridSequence')
        const visibleColumn = Storage.getItem('VXE_TABLE_CUSTOM_COLUMN_VISIBLE')
        if (Array.isArray(columnSequence)) {
          if (visibleColumn && visibleColumn['tracking-order-grid']) {
            const viColumns = visibleColumn['tracking-order-grid'].split(',').filter(Boolean)
            columnSequence = columnSequence.filter(item => !viColumns.some(vi => vi === item))
          }
          data.showFields = columnSequence.filter(item => item !== 'remark')
        }
      })
    },
    exportDetail () {
      const data = deepClone(this.searchForm)
      this.pageLoading = true
      this.parsePurchaseGroup(data)
      this.filterFields(data)
      this.setDataShowFields(data)

      console.log(this.trackingOrderConfig, data)
      let useAsync = false
      if (this.trackingOrderConfig && this.trackingOrderConfig.async) {
        useAsync = true
      }
      let exportApi = exportTrackingOrderDetail
      if (useAsync) {
        exportApi = exportTrackingOrderDetailAsync
      }
      exportApi(data)
        .then(res => {
          if (useAsync && res === null) {
            this.$message.success('导出成功，请在下载专区查看导出内容！')
            setTimeout(() => {
              this.$router.push({
                path: '/purchaseReport/downLoadList'
              })
            }, 600)
            return
          }
          if (res) {
            let data = []
            if (Array.isArray(res) && res.length) {
              data = res
            }
            if (Array.isArray(res.rows) && res.rows.length) {
              data = res.rows
            }
            if (data && data.length) {
              this.betterWriteFile(data)
            } else {
              this.$message.info('没有数据！')
            }
          }
        })
        .finally(() => {
          this.pageLoading = false
        })
      sensors('PurchaseWorkbenchExportOrderLine', { label_name: '跟单报表' })
    },
    setSearchForm (data, refresh, initPageNo) {
      this.searchForm = data
      if (initPageNo) {
        this.tableInfo.pageNo = 1
        this.sortInfo = null
        this.$refs.grid.$refs.trackingOrderGrid.clearSort()
      }
      if (refresh) {
        this.handleSearch()
      }
    },
    paddingArrayRes (resArray) {
      let ret = ''
      resArray.forEach((data, index) => {
        ret += `第${index + 1}行: ${data} <br />`
      })
      return ret
    },
    setDataResult (data) {
      this.dataResult = data
      this.resultVisible = true
    },
    updateVisible (type, visible) {
      this[type] = visible
      if (type === 'resultVisible' && !visible) {
        this.dataResult = {
          failCount: 0,
          successCount: 0,
          detailList: []
        }
        this.validSearch()
      }
    },
    async saveData (data) {
      this.pageLoading = true
      if (data && data.rows && data.rows.length) {
        data.rows.forEach(row => {
          if (row.planDeliveryDate) {
            row.planDeliveryDate = row.planDeliveryDate.replace(/\//gmi, '-')
          }
          if (row.poUnconfirmedReason === '') {
            row.poUnconfirmedReason = 0
          }
          if (row.deliveryChangeReason === '') {
            row.deliveryChangeReason = 0
          }
        })
      }
      let res = await batchEditTrackingOrder(data)
      // res = '成功1行，失败0行'
      // console.log(res)
      this.pageLoading = false
      if (!res) return
      if (typeof res === 'string') {
        this.$alert(res, '操作提示', {
          confirmButtonText: '确认',
          type: 'info'
        }).then(() => {
          this.handleSearch()
        })
        return
      }
      this.dataResult = res
      this.resultVisible = true
    },
    validateDateRange (data) {
      let ret = true
      const { orderDateBegin, orderDateEnd } = data
      if (orderDateBegin && orderDateEnd) {
        const dayRange = Math.abs(new Date(orderDateBegin).getTime() - new Date(orderDateEnd).getTime()) / (1e3 * 3600 * 24)
        // if (dayRange > 365) {
        //   ret = false
        //   this.$message.error('订单创建日期跨度最长12个月！')
        // }
        console.log(dayRange)
      }
      return ret
    },
    handleSearch () {
      const data = deepClone(this.searchForm)
      this.filterFields(data)
      this.setDataShowFields(data)
      data.pageNo = this.tableInfo.pageNo
      data.pageSize = this.tableInfo.pageSize
      // 有供应商不校验日期
      if (!data.supplierNoList.length) {
        if (!this.validateDateRange(data)) return
      }
      if (this.sortInfo && this.sortInfo.order) {
        data.orderDirection = this.sortInfo.order.toUpperCase()
        data.orderByField = this.sortInfo.property
      }
      this.pageLoading = true
      this.parsePurchaseGroup(data)
      getTrackingOrderList(data)
        .then(res => {
          if (res) this.setDataList(res)
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    handleTableInfo (data) {
      this.tableInfo = { ...this.tableInfo, ...data }
      this.handleSearch()
    },
    saveOriginData(rows) {
      const reg = /(\d{3})\d{4}(\d{4})/ // 正则表达式
      const originProps = [ 'poNo', 'itemNo', 'planNo', 'tag', 'isUrgent', 'isCustomerAccept', 'isConfirmed', 'isSpot', 'planDeliveryDate', 'remark', 'isSigningBackAccepted', 'isCustomerReceived', 'overdueReason', 'transferReason', 'designatedChannel', 'poUnconfirmedReason', 'deliveryChangeReason' ]
      rows.forEach(row => {
        row._originData = {}
        row.overdueReason = [row?.overdueReasonFirst, row?.overdueReasonSecond]
        row.receiveContactPhoneEncryption = row.receiveContactPhone ? row.receiveContactPhone.replace(reg, '$1****$2') : ''
        originProps.forEach(prop => {
          row._originData[prop] = row[prop]
        })
      })
    },
    setDataList (response) {
      // console.log(response)
      const { total, pageNo, rows } = response
      const data = rows.map(it => {
        const diffList = it.changeDiff ? JSON.parse(it.changeDiff) : ''
        let deliveryChangeReasonDesc = ''
        let isSpotDesc = ''
        if (diffList) {
          const deliveryChangeReason = diffList.find(item => item.property === 'deliveryChangeReasonDesc')
          const isSpot = diffList.find(item => item.property === 'isSpotDesc')
          if (deliveryChangeReason) {
            deliveryChangeReasonDesc = `,原因从${ deliveryChangeReason.oldValue }修改为${ deliveryChangeReason.newValue }`
          }
          if (isSpot) {
            isSpotDesc = `,是否现货从${ isSpot.oldValue }修改为${ isSpot.newValue }`
          }
        }
        console.log('diffList', diffList)
        return {
          ...it,
          poUnconfirmedReason: it.poUnconfirmedReason ? it.poUnconfirmedReason : '',
          deliveryChangeReason: it.deliveryChangeReason ? it.deliveryChangeReason : '',
          changeDiff: diffList ? diffList.map(item => {
            let desc = ''
            if (item.property === 'shippingAmount') {
              desc = `修改运费(从${ item.oldValue || 0 }修改为${ item.newValue })`
            } else if (item.property === 'sundryAmount') {
              desc = `修改杂费(从${ item.oldValue || 0 }修改为${ item.newValue })`
            } else if (item.property === 'itemQuantity') {
              desc = `数量减少(从${ item.oldValue || 0 }修改为${ item.newValue })`
            } else if (item.property === 'itemDeliveryDate') {
              desc = `修改交期(从${ item.oldValue || 0 }修改为${ item.newValue }${ deliveryChangeReasonDesc }${ isSpotDesc })`
            }
            return {
              ...item,
              desc
            }
          }).filter(i => ['shippingAmount', 'sundryAmount', 'itemQuantity', 'itemDeliveryDate'].includes(i.property)) : null
        }
      })
      this.saveOriginData(data)
      this.listData = data
      this.tableInfo.total = total
      this.tableInfo.pageNo = pageNo
    }
  }
}
</script>

<style lang="scss" scoped>
.tracking-order {
  padding: 10px 0;
  margin-right: 20px;
}
</style>
