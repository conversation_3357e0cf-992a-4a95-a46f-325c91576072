<template>
  <div class="purchase-detail" v-loading="detailLoading">
    <el-tabs v-model="activeTab" @tab-click="clickTab" type="border-card">
      <el-tab-pane label="商品详情" name="first">
        <OrderDetailHeader :finalFields="finalFields" :showCharge="showCharge" @refresh="handleRefresh" :updateLoading="updateLoading" :detailLoading="detailLoading"/>
        <ProductInfo :finalFields="finalFields" :itemList="productInfo.itemList" :showCharge="showCharge" @refresh="handleRefresh" :updateLoading="updateLoading" :oneProductAuth="oneProductAuth"/>
      </el-tab-pane>
      <el-tab-pane label="履约状况" name="second">
        <Agreement :tabLoading="tabLoading" :initAgreementInfo="agreementInfo" :id="detailId" :spanArr="rowspanArr" />
      </el-tab-pane>
      <el-tab-pane label="物流信息" name="third">
        <Logistics :tabLoading="tabLoading" :initLogisticsInfo="logisticsInfo" :id="detailId" :spanArr="spanArr" :isTc="productInfo.poExtend && productInfo.poExtend.isTc"/>
      </el-tab-pane>
      <el-tab-pane v-if="getButtonAuth('采购订单列表', 'PO详情_交货与发票')" label="收货与发票" name="fourth">
        <ReceiptAndInvoice :activeTab="activeTab" :id="detailId"></ReceiptAndInvoice>
      </el-tab-pane>
      <el-tab-pane label="预付款信息" name="fifth">
        <Advance :id="detailId" :activeTab="activeTab"></Advance>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
// 采购订单详情页
import OrderDetailHeader from './components/detail/orderDetailHeader'
import ProductInfo from './components/detail/productInfo'
import Agreement from './components/detail/agreement'
import Logistics from './components/detail/logistics'
import Advance from './components/detail/advance.vue'
import ReceiptAndInvoice from './components/detail/receiptAndInvoice'
import { getOrderDetail, getAgreement, getLogistics, getMMOneProductAuth, queryApproveStatus } from '@/api/mm'
import { formatAgreementData, formatLogisticsData } from './utils'
import { getAllDictList } from '@/utils/mm'
import { getRpaDetail } from '@/api/honeycomb'
import { deepClone } from '@/utils/index'
import { getButtonAuth } from '@/utils/auth'
import { mapState } from 'vuex'

export default {
  name: 'purchaseOrderDetail',
  components: {
    OrderDetailHeader,
    ProductInfo,
    Agreement,
    Logistics,
    ReceiptAndInvoice,
    Advance
  },
  provide () {
    return {
      getProductInfo: () => this.productInfo,
      updateDetail: this.getDetail,
      getApproveStatus: () => this.approveStatus
    }
  },
  computed: {
    ...mapState({
      fields: state => state.orderPurchase.detail,
      userRole: state => state.userRole
    }),
    showCharge() {
      const { orderType } = this.productInfo
      const disList = [ 'Z003', 'Z005' ]
      return !disList.includes(orderType)
    },
    finalFields() {
      let ret = []
      if (Array.isArray(this.fields[this.productInfo.orderType])) {
        ret = deepClone(this.fields[this.productInfo.orderType])
      }
      if (this.isFesto && !ret.find(item => item.prop === 'customerName')) {
        const item = {
          category: 'baseInfo',
          disabled: false,
          name: '客户名称',
          prop: 'customerName',
          status: 'detail',
          type: 'input',
          sequence: 23,
          span: 16
        }
        const idx = ret.findIndex(item => item.prop === 'sapResult')
        if (idx > -1) {
          ret.splice(idx, 0, item)
        } else {
          ret.push(item)
        }
      }
      // 交期变更原因
      if (!ret.find(item => item.prop === 'deliveryChangeReason')) {
        const item = {
          category: 'table',
          disabled: 0,
          name: '交期变更原因',
          prop: 'deliveryChangeReason',
          enums: 'deliveryChangeReason',
          status: 'detail',
          type: 'select',
          sequence: 261,
          width: 200
        }
        const idx = ret.findIndex(item => item.prop === 'factoryCode')
        if (idx > -1) {
          ret.splice(idx + 1, 0, item)
        } else {
          ret.push(item)
        }
      }
      window._console.black('detail1', this.fields, ret)
      return ret
    }
  },
  data() {
    return {
      isFesto: false,
      rowspanArr: [],
      local: 0,
      spanArr: [],
      position: 0,
      detailLoading: false,
      tabLoading: false,
      detailId: this.$route.params.id,
      activeTab: 'first',
      productInfo: {
        showRpaTag: false,
        rapTagType: '',
        itemList: []
      },
      agreementInfo: [],
      logisticsInfo: [],
      // 表格数据加载
      loading: false,
      oneProductAuth: false,
      approveStatus: false
    }
  },
  async created() {
    this.detailLoading = true
    await getAllDictList(this)
    await this.getDetail()
    await this.getDetailFields()
    await this.initFesto()
    await this.getOneProductAuth()
    this.detailLoading = false
  },
  methods: {
    getButtonAuth,
    async getApproveStatus () {
      const data = await queryApproveStatus([ this.detailId ])
      if (data) {
        this.approveStatus = data?.[0]?.approveStatus || false
      }
    },
    async getOneProductAuth () {
      await getMMOneProductAuth()
        .then((data) => {
          if (data) {
            this.oneProductAuth = data.enable
          } else {
            this.oneProductAuth = false
          }
        })
    },
    async initFesto () {
      const { supplierNo } = this.productInfo
      if (/v45696/gim.test(supplierNo)) {
        this.isFesto = true
        console.log('is festo...')
      }
    },
    setTitleCheckbox (prop) {
      const { itemList } = this.productInfo
      if (prop === 'isUrgent') {
        const filterList = itemList.filter(item => !item.isDeleted)
        if (filterList.length && filterList.every(item => item.isUrgent)) {
          this.$set(this.productInfo, 'isUrgent', 1)
        } else {
          this.$set(this.productInfo, 'isUrgent', 0)
        }
      }
    },
    updateLoading (loading) {
      this.detailLoading = loading
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data, idx, prop) {
      this.spanArr[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1)
          this.position = 0
        } else {
          if (data[index].itemNo === data[index - 1].itemNo && data[index][prop] === data[index - 1][prop] && (data[0][prop] === '' || data[0][prop] === null) && idx >= 12) {
          // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else if (data[index].itemNo === data[index - 1].itemNo && idx < 12) {
            // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr[idx].push(1)
            this.position = index
          }
        }
      })
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr1 (data, idx, prop) {
      this.rowspanArr[idx] = []
      this.local = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.rowspanArr[idx].push(1)
          this.local = 0
        } else {
          if (data[index].itemNo === data[index - 1].itemNo) {
            // 有相同项
            this.rowspanArr[idx][this.local] += 1
            this.rowspanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.rowspanArr[idx].push(1)
            this.local = index
          }
        }
      })
    },
    clickTab (tab) {
      const { name } = tab
      if (name === 'second' && !tab.tabMounted) {
        this.getAgreementDetail()
        tab.tabMounted = true
      }
      if (name === 'third' && !tab.tabMounted) {
        this.getLogisticsDetail()
        tab.tabMounted = true
      }
    },
    async getAgreementDetail () {
      this.tabLoading = true
      return getAgreement({ poNo: this.detailId }).then((res) => {
        if (res) {
          const { itemList = [] } = res
          this.agreementInfo = itemList.reduce((p, c) => {
            const formatedList = formatAgreementData(c)
            return p.concat(formatedList)
          }, [])
          this.getSpanArr1(this.agreementInfo, 0, 'itemNo')
          this.getSpanArr1(this.agreementInfo, 1, 'skuNo')
          this.getSpanArr1(this.agreementInfo, 2, 'materialDescription')
          this.getSpanArr1(this.agreementInfo, 3, 'warehouseLocation')
          this.getSpanArr1(this.agreementInfo, 4, 'deliveryDate')
          this.getSpanArr1(this.agreementInfo, 5, 'deliveryQuantity')
          this.getSpanArr1(this.agreementInfo, 6, 'outstandingQuantity')
        }
      }).finally(() => {
        this.tabLoading = false
      })
    },
    async getLogisticsDetail () {
      this.tabLoading = true
      return getLogistics({ poNo: this.detailId }).then((res) => {
        if (res) {
          const { itemList = [] } = res
          this.logisticsInfo = itemList.reduce((p, c) => {
            const formatedList = formatLogisticsData(c)
            return p.concat(formatedList)
          }, [])
          this.getSpanArr(this.logisticsInfo, 0, 'itemNo')
          this.getSpanArr(this.logisticsInfo, 1, 'skuNo')
          this.getSpanArr(this.logisticsInfo, 2, 'materialDescription')
          this.getSpanArr(this.logisticsInfo, 3, 'warehouseLocation')
          this.getSpanArr(this.logisticsInfo, 4, 'itemQuantity')
          this.getSpanArr(this.logisticsInfo, 5, 'outstandingQuantity')
          this.getSpanArr(this.logisticsInfo, 6, 'deliveryTime')
          this.getSpanArr(this.logisticsInfo, 7, 'number')
          this.getSpanArr(this.logisticsInfo, 8, 'deliveryWayText')
          this.getSpanArr(this.logisticsInfo, 9, 'logisticsName')
          this.getSpanArr(this.logisticsInfo, 10, 'logisticsCode')
          this.getSpanArr(this.logisticsInfo, 11, 'statusText')
        }
      }).finally(() => {
        this.tabLoading = false
      })
    },
    otherTabReq () {
      this.tabLoading = true
      const reqList = [
        getAgreement({ poNo: this.detailId }),
        getLogistics({ poNo: this.detailId })
      ]
      Promise.all(reqList).then(([data2, data3]) => {
        if (data2) {
          const { itemList = [] } = data2
          this.agreementInfo = itemList.reduce((p, c) => {
            const formatedList = formatAgreementData(c)
            return p.concat(formatedList)
          }, [])
          this.getSpanArr1(this.agreementInfo, 0, 'itemNo')
          this.getSpanArr1(this.agreementInfo, 1, 'skuNo')
          this.getSpanArr1(this.agreementInfo, 2, 'materialDescription')
          this.getSpanArr1(this.agreementInfo, 3, 'warehouseLocation')
          this.getSpanArr1(this.agreementInfo, 4, 'deliveryDate')
          this.getSpanArr1(this.agreementInfo, 5, 'deliveryQuantity')
          this.getSpanArr1(this.agreementInfo, 6, 'outstandingQuantity')
        }
        if (data3) {
          const { itemList = [] } = data3
          this.logisticsInfo = itemList.reduce((p, c) => {
            const formatedList = formatLogisticsData(c)
            return p.concat(formatedList)
          }, [])
          this.getSpanArr(this.logisticsInfo, 0, 'itemNo')
          this.getSpanArr(this.logisticsInfo, 1, 'skuNo')
          this.getSpanArr(this.logisticsInfo, 2, 'materialDescription')
          this.getSpanArr(this.logisticsInfo, 3, 'warehouseLocation')
          this.getSpanArr(this.logisticsInfo, 4, 'itemQuantity')
          this.getSpanArr(this.logisticsInfo, 5, 'outstandingQuantity')
          this.getSpanArr(this.logisticsInfo, 6, 'deliveryTime')
          this.getSpanArr(this.logisticsInfo, 7, 'number')
          this.getSpanArr(this.logisticsInfo, 8, 'deliveryWayText')
          this.getSpanArr(this.logisticsInfo, 9, 'logisticsName')
          this.getSpanArr(this.logisticsInfo, 10, 'logisticsCode')
          this.getSpanArr(this.logisticsInfo, 11, 'statusText')
        }
      }).finally(() => {
        console.log(this.agreementInfo)
        console.log(this.logisticsInfo)
        this.tabLoading = false
      })
    },
    async getDetailFields () {
      this.detailLoading = true
      const { orderType } = this.productInfo
      if (!this.fields[orderType] || !this.fields[orderType].length) {
        await this.$store.dispatch('orderPurchase/getPoFields', {
          orderType,
          routerType: 'detail'
        })
      }
      this.detailLoading = false
    },
    async getRpaDetail() {
      const { supplierNo, orderNo } = this.productInfo
      return getRpaDetail({ supplierNo, orderId: orderNo })
        .then(res => {
          // showRpaTag rapTagType
          if (res && res.isRpaSupplier) {
            const { orderPushStatus, message } = res
            this.productInfo.message = message
            this.productInfo.showRpaTag = true
            this.productInfo.rapTagType = orderPushStatus
          }
        })
    },
    async getDetail (tab) {
      this.detailLoading = true
      this.getApproveStatus()
      await getOrderDetail({ orderNo: this.detailId })
        .then((data) => {
          if (data) {
            data.ownerTransfer = (data?.taglibMap?.company_transfer || []).find(item => item.id === 102) ? 1 : 0
            this.productInfo = {
              ...data,
              showRpaTag: false,
              rapTagType: '',
              poExtend: data.poExtend ? data.poExtend : {
                isDelayedPayment: 0
              }
            }
            this.productInfo.poExtend.isDelayedPayment = this.productInfo?.poExtend?.isDelayedPayment || 0
          }
        }).finally(() => {
          this.detailLoading = false
          this.setTitleCheckbox('isUrgent')
        })
      await this.getRpaDetail()
    },
    handleRefresh () {
      this.getDetail('noOtherTab')
    }

  }
}
</script>

<style lang="scss" scoped>
.purchase-detail {
  padding: 5px 0;
  min-height: 500px;
}
</style>
