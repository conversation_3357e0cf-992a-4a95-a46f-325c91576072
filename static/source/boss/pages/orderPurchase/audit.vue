<template>
  <div class="purchase-audit">
    <div class="search-filter">
      <el-form ref="searchFrom" :model="searchForm" style="width: 100%" label-width="120px">
        <el-row>
          <el-col :span="16">
            <el-form-item label="单号检索：" prop="orderNos">
              <el-input
                v-model="searchForm.orderNos"
                clearable
                placeholder="采购单号 或 SAP订单号 均支持搜索，同时支持10个单号，以空格或换行符分隔"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="供应商：" prop="supplierNo">
              <SelectSupplier
                :data.sync="searchForm.supplier"
                @change="handleChange('supplier', $event)"
                />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="审批状态：" prop="approveStep">
              <el-select style="width:100%" filterable v-model="searchForm.approveStep" placeholder="请选择"  default-first-option clearable>
                <el-option
                  v-for="item in approveStepOptions.slice(1, approveStepOptions.length)"
                  :key="item.code"
                  :label="item.label"
                  :value="item.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="采购姓名：" prop="purchaseGroups">
            <AllowCreateSelect  :data.sync="searchForm.purchaseGroups" @change="val=>searchForm.purchaseGroups=val" :optionLists="purchaseGroupLists" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="工厂：" prop="factoryCode">
              <el-select style="width:100%" v-model="searchForm.factoryCode" clearable filterable default-first-option>
                <!--   :key="item.factoryCode" -->
                <el-option
                  v-for="(item,index) in factoryList"
                  :key="index"
                  :label="item.factoryCode+' '+item.factoryName"
                  :value="item.factoryCode">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="审批人：" prop="approveUser">
              <el-input
                v-model="searchForm.approveUser"
                clearable
                placeholder="请输入审批人域账号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
             <el-form-item>
              <el-button type="primary" @click="handleCurrentChange(1)">查询</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="search-result">
      <el-table
        :data="table.tableData"
        tooltip-effect="dark"
        v-loading="loading.tableLoading"
        style="width: 100%"
        @row-dblclick="rowDbclick"
        @selection-change="handleSelectionChange"
      >
        <template v-for="item in tableConfig">
          <el-table-column
            show-overflow-tooltip
            align="center"
            :label="item.label"
            :prop="item.prop"
            :key="item.prop"
            :width="item.width"
          >
            <template slot-scope="{ row }">
              <div v-if="item.format">
                {{item.format(row[item.prop])}}
              </div>
              <div v-else-if="item.link">
                <el-link @click="toDetail(row)" type="primary">{{row[item.prop]}}</el-link>
              </div>
              <div v-else-if="item.enum">
                {{row[item.prop] + '  ' + mapToProps(row[item.prop], 'groupCode', 'userName', purchaseGroupList)}}
              </div>
              <div v-else-if="item.prop === 'supplierName'">
                {{row.supplierNo + '  ' + row.supplierName}}
              </div>
              <div v-else-if="item.prop === 'approveStep'">
                {{mapStepList(row[item.prop])}}
              </div>
              <span v-else>
                {{ row && row[item.prop] }}
              </span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" align="center">
          <template slot-scope="{row}" v-if="showAuditColumn(row)">
            <el-button type="text" v-if="showAuditButton(row, 'P')" @click="auditAction(row, 'P')">通过</el-button>
            <el-button type="text" v-if="showAuditButton(row, 'R')" @click="auditAction(row, 'R')">驳回</el-button>
            <el-button type="text" v-if="showAuditButton(row, 'C')" @click="auditAction(row, 'C')">撤回</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagi" v-if="table.total > 10">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="table.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="table.total">
        </el-pagination>
      </div>
    </div>
    <el-dialog class="reject-dialog" :visible.sync="showReject" title="审批驳回" width="400px" :before-close="handleClose" :close-on-click-modal="false">
      <div class="body">
        <span>审批备注：</span>
        <el-input
          type="textarea"
          placeholder="驳回订单需填写审批备注"
          v-model="auditInfo.auditRemark"
          :autosize="{ minRows: 2, maxRows: 6}
        "/>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" type="primary" @click="auditReject">确认</el-button>
        <el-button size="mini" @click="showReject = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { getAuditList, auditOrder } from '@/api/mm';
import { safeRun } from '@/utils/index'
import { mapToProps } from '@/utils/mm'
import SelectSupplier from '@/pages/orderPurchase/components/common/SelectSupplier'
import { getButtonAuth } from '@/utils/auth'
import AllowCreateSelect from './components/common/MultipleSelect'

export default {
  name: 'purchaseOrderAudit',
  components: { SelectSupplier, AllowCreateSelect },
  data() {
    return {
      approveStepOptions: [
        // { code: '-1', label: '无需审批' },
        { code: '0', label: '审批流初始化中' },
        { code: '10', label: '一审待审批' },
        { code: '11', label: '一审驳回' },
        { code: '20', label: '二审待审批' },
        { code: '21', label: '二审驳回' },
        { code: '100', label: '审批通过' }
      ],
      selections: [],
      tableConfig: [
        { label: '采购单号', prop: 'orderNo', link: true },
        { label: '采购姓名', prop: 'purchaseGroup', enum: 'purchaseGroup' },
        { label: '供应商', prop: 'supplierName', width: 200 },
        { label: '审批金额', prop: 'approveAmount' },
        { label: '含税总额', prop: 'taxedTotalAmount' },
        { label: '未税总额', prop: 'untaxedTotalAmount' },
        { label: '审批状态', prop: 'approveStep' },
        { label: '审批人', prop: 'approveUser' }
      ],
      loading: {
        searchLoading: false,
        tableLoading: false
      },
      searchForm: {
        orderNos: '',
        supplier: {},
        supplierNo: '',
        purchaseGroups: [],
        approveStep: '',
        factoryCode: '',
        approveUser: ''
      },
      showReject: false,
      auditInfo: {
        row: {},
        action: '',
        auditRemark: '',
        approveUser: '',
        currentStep: ''
      },
      table: {
        pageNo: 1,
        current: 1,
        pageSize: 10,
        total: 0,
        tableData: []
      }
    };
  },
  created() {
    if (this.purchaseGroupList.length === 0) {
      this.$store.dispatch('orderPurchase/queryPurchaseGroup')
    }
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    // this.onSubmit()
  },
  computed: {
    ...mapState({
      dictList: state => state.orderPurchase.dictList,
      purchaseGroupList: state => state.orderPurchase.purchaseList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList
    }),
    purchaseGroupLists () {
      return this.purchaseGroupList.map(item => {
        return {
          value: item.groupCode,
          name: item.userName
        }
      })
    },
    factoryList () {
      let list = []
      safeRun(() => {
        list = this.companyFactoryList.reduce((x, y) => { return x.concat(y.factoryList || []) }, [])
      })
      return list
    }
  },
  methods: {
    getButtonAuth,
    mapToProps,
    showAuditColumn (row) {
      return row.approveStep !== 0 && row.approveStep !== -1
    },
    showAuditButton (row, type) {
      const showPass = [10, 20]
      const showReject = [10, 20]
      const showCancel = [11, 20, 21, 100]
      const buttonMap = {
        P: showPass,
        R: showReject,
        C: showCancel
      }
      return buttonMap[type].includes(row.approveStep)
    },
    mapStepList (code) {
      let item = {
        label: ''
      }
      safeRun(() => {
        // eslint-disable-next-line
        const tmp = this.approveStepOptions.find(item => item.code == code)
        tmp && (item = tmp)
      })
      return item.label
    },
    confirmAction (title) {
      return this.$confirm(title, '操作提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
    },
    handleClose (done) {
      done && done()
      this.showReject = false
      this.auditInfo = {
        row: {},
        action: '',
        auditRemark: '',
        approveUser: '',
        currentStep: ''
      }
    },
    showRejectDialog () {
      this.showReject = true
    },
    toDetail (row) {
      this.$router.push(`/orderPurchase/detail/${row.orderNo}`)
    },
    handleChange (type, event) {
      if (type === 'supplier') {
        this.searchForm.supplierNo = event.supplierNo
      }
    },
    resetForm () {
      this.$refs.searchFrom.resetFields()
      this.searchForm.supplier = {}
    },
    toggleDetail (row) {
      row.expand = !row.expand
      this.table.tableData.splice(this.table.tableData.indexOf(row), 1, row)
    },
    auditReject () {
      if (!this.auditInfo.auditRemark) {
        return this.$message.error('驳回订单需填写审批备注')
      }
      this.showReject = false
      this.auditOrder()
    },
    auditAction (row, action) {
      this.auditInfo.row = row
      this.auditInfo.action = action
      if (action === 'R') {
        return this.showRejectDialog()
      }
      this.auditOrder()
    },
    auditOrderApi (data) {
      auditOrder(data)
        .then(res => {
          if (res === null) {
            this.$message.success('操作成功！')
            setTimeout(() => {
              this.onSubmit()
            }, 100)
          }
        })
        .finally(() => {
          this.handleClose()
        })
    },
    async auditOrder () {
      const data = {
        action: this.auditInfo.action,
        approveRemark: this.auditInfo.auditRemark,
        approveUser: window.CUR_DATA.user && window.CUR_DATA.user.name,
        currentStep: this.auditInfo.row.approveStep,
        orderNo: this.auditInfo.row.orderNo
      }
      let title = ''
      if (data.action === 'R') {
        return this.auditOrderApi(data)
      }
      if (data.action === 'P') {
        title = '审核通过，确认提交'
      }
      if (data.action === 'C') {
        title = '撤销审核，确认提交'
      }
      this.confirmAction(title)
        .then(() => {
          this.auditOrderApi(data)
        })
    },
    handleSizeChange (val) {
      this.table.pageSize = val
      this.onSubmit()
    },
    handleCurrentChange (val) {
      this.table.pageNo = val
      this.onSubmit()
    },
    rowDbclick(row, col, event) {
      console.log(row, col, event);
      row.editable = !row.editable;
    },
    formatParams(params) {
      let form = { ...params };
      form.orderNos = safeRun(() =>
        form.orderNos
          .split(/\s+|,|，/).filter((e) => e)
      );
      delete form.supplier
      return form;
    },
    handleSearch () {
      this.table.pageNo = 1;
      this.onSubmit()
    },
    validate (params) {
      let ret = true
      safeRun(() => {
        if (params.orderNos.length > 10) {
          ret = false
          this.$message.error('订单号不能超过10个！')
        }
      })
      return ret
    },
    onSubmit() {
      let params = this.formatParams(this.searchForm);
      if (!this.validate(params)) return
      this.loading.searchLoading = true;
      this.loading.tableLoading = true;
      params = {
        pageNo: this.table.pageNo,
        pageSize: this.table.pageSize,
        ...params
      }
      console.log(params);
      getAuditList(params)
        .then((data) => {
          if (data) {
            safeRun(() => {
              this.table.tableData = data.rows
              this.table.total = data.total
              this.table.tableData && this.table.tableData.forEach(row => (row.expand = false))
            })
          }
        })
        .finally(() => {
          this.loading.searchLoading = false;
          this.loading.tableLoading = false;
        })
    },
    handleSelectionChange(rows) {
      this.selections = rows;
    },
    handleOpenBatchCreateDlg () {
      this.createBatchOrder = true
    },
    handleOpenBatchEditDlg () {
      this.editBatchOrder = true
    },
    handleSuccess () {
      this.handleSearch()
    }
  }
};
</script>
<style lang="scss" scoped>
.purchase-audit {
  padding: 20px;
  .search-filter {
    padding: 0 10px;
  }
  .search-result {
    h3 {
      margin: 10px 0px;
    }
  }
  .reject-dialog {
    .body {
      display: flex;
      div {
        flex: 1
      }
    }
  }
  .pagi{
    margin-top: 20px;
    float:right;
  }
}
</style>
