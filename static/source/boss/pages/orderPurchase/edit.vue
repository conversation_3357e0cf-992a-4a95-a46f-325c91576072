<template>
  <div class="purchase-edit" v-loading="pageLoading">
    <el-form :model="purchaseData" ref="orderForm" label-width="140px" label-suffix=":" :rules="rules">
      <div class="card-wrapper">
        <div class="tab">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="基本信息" name="base"></el-tab-pane>
            <el-tab-pane label="备注文本" name="remark"></el-tab-pane>
            <el-tab-pane label="通讯信息" name="communication"></el-tab-pane>
            <el-tab-pane v-if="showCharge" label="价费信息" name="charge"></el-tab-pane>
            <el-tab-pane label="相关附件" name="attachment"></el-tab-pane>
          </el-tabs>
          <el-button class="toggle-button" circle :icon="showHeaderIcon" @click="handleFold" />
        </div>
        <div class="card-container"  v-show="showHeader">
          <div class="card-title">
            <EditBaseInfo
              v-show="activeTab==='base'"
              :purchaseData="purchaseData"
              :finalFields="finalFields"
              :factoryCode="factoryCode"
              :isFesto="isFesto"
              :customerNameOptions="customerNameOptions"
              :createEditStatus="createEditStatus"
              :getFirstLineUpdateDefaultAddress="getFirstLineUpdateDefaultAddress"
              :isEditDelayedPaymentPo="isEditDelayedPaymentPo"
              @resetCommunication="resetCommunication"
              @resetCharge="resetCharge"
              @addItemEmptyLine="addItemEmptyLine"
              @setSupplier="setSupplier"
              @resetSupplier="resetSupplier"
              @setReturnKey="setReturnKey"
              @clearValidate="clearValidate"
              @addItem="addItem"
              @initAddress="initAddress"
              @queryAddressList="queryAddressList"
              @queryDefaultAddress="queryDefaultAddress"
              @updateKeepSupplier="updateKeepSupplier"
              @setTitleCheckbox="setTitleCheckbox"
            />
            <EditRemarkText
              v-show="activeTab==='remark'"
              :createEditStatus="createEditStatus"
              :finalFields="finalFields"
              :purchaseData="purchaseData"
              :isEditDelayedPaymentPo="isEditDelayedPaymentPo"
            />
            <EditContactInfo
              v-show="activeTab==='communication'"
              :finalFields="finalFields"
              :createEditStatus="createEditStatus"
              :purchaseData="purchaseData"
              :supplierAddressList="supplierAddressList"
              :supplierContactList="supplierContactList"
              :receiptAddressList="receiptAddressList"
              :receiptContactList="receiptContactList"
              :receiveContactList="receiveContactList"
              :receiveAddressList="receiveAddressList"
              :isEditDelayedPaymentPo="isEditDelayedPaymentPo"
              @setAddress="setAddress"
            />
            <EditChargeInfo
              v-show="activeTab==='charge'"
              :finalFields="finalFields"
              :createEditStatus="createEditStatus"
              :purchaseData="purchaseData"
              :disableCharge="disableCharge"
              :handleChangeChargeCheckbox="handleChangeChargeCheckbox"
              :dictList="dictList"
              :isEditDelayedPaymentPo="isEditDelayedPaymentPo"
              @handleOneAssign="handleOneAssign"
              @handleAttachmentList="handleAttachmentList"
              @setItemSupplier="setItemSupplier"
            />
            <Attachment
              v-show="activeTab==='attachment'"
              :finalFields="finalFields"
              :purchaseData="purchaseData"
              :isEditDelayedPaymentPo="isEditDelayedPaymentPo"
            />
          </div>
        </div>
        <div class="card-container">
          <div class="card-table">
            <div class="table-header">
              <span>商品信息</span>
              <span>
                <el-button type="danger" :disabled="isEditDelayedPaymentPo" plain @click="handleItemToggle">
                  删除/恢复商品行
                </el-button>
                <!-- <el-button type="primary" @click="batchConfirm">
                  批量交期确认
                </el-button> -->
                <el-button
                  type="primary"
                  :disabled="isDisabledAddItem || isEditDelayedPaymentPo"
                  @click="showBatchImport=true"
                >
                  快速导入商品
                </el-button>
                <el-button
                  v-if="isFesto"
                  :disabled="isEditDelayedPaymentPo"
                  type="primary"
                  @click="showPunchImport=true"
                >
                  Punch导入商品
                </el-button>
              </span>
            </div>
            <el-row type="flex" justify="space-between" style="padding: 15px 20px;">
              <el-col :span="24" style="font-size: 16px">
                <span class="amount" v-show="purchaseData.orderType !== 'Z003'">未税总额：
                  <span :class="{'currency':purchaseData.currency!=='CNY'}">
                    {{purchaseData.currency}}
                  </span>
                  {{totalAmount.unTaxedTotal.toFixed(2)}}
                </span>
                <span class="amount" v-show="purchaseData.orderType !== 'Z003'">含税总额：
                  <span :class="{'currency':purchaseData.currency!=='CNY'}">
                    {{purchaseData.currency}}
                  </span>
                  {{totalAmount.taxedTotal.toFixed(2)}}
                </span>
                <span class="amount" v-show="purchaseData.orderType !== 'Z003'">税费总额：
                  <span :class="{'currency':purchaseData.currency!=='CNY'}">
                    {{purchaseData.currency}}
                  </span>
                  {{((totalAmount.taxedTotal - totalAmount.unTaxedTotal)||0).toFixed(2)}}
                </span>
                <span class="amount" v-show="isGrossMarginOrderType">毛利率:
                  {{![undefined, 'undefined', '', null, NaN].includes(totalAmount.grossMarginTotal) ? `${ totalAmount.grossMarginTotal }%` : '' }}
                </span>
              </el-col>
            </el-row>
            <ItemTable
              class="table-body"
              ref="itemTable"
              :data="purchaseData"
              :factoryList="factoryList"
              :finalFields="finalFields"
              :createEditStatus="createEditStatus"
              :materialList="materialList"
              :records.sync="records"
              :disabled="isDisabledAddItem || isEditDelayedPaymentPo"
              :isEditDelayedPaymentPo="isEditDelayedPaymentPo"
              :diffDays="diffDays"
              :costCenterList="costCenterList"
              :getCostCenter="getCostCenter"
              :setTitleCheckbox="setTitleCheckbox"
              :reCalcCompQuantity="reCalcCompQuantity"
              :getFirstLineUpdateDefaultAddress="getFirstLineUpdateDefaultAddress"
              @showDetail="handleShowDetail"
              @updateFactory="updateFactory"
              @reactiveSetItem="reactiveSetItem"
              @updateItemDeliveryDate="updateItemDeliveryDate"
              @updateTransferTime="updateTransferTime"
              @updateWarehouseLocation="updateWarehouseLocation"
              @updateDeliveryDaysByWarehouseLocation="updateDeliveryDaysByWarehouseLocation"
              @updateMaterialDescription="handleUpdateMaterialDescription"
              @updateCustomerService="handleUpdateCustomerService"
              @addItem="addItem"
              @addItemEmptyLine="addItemEmptyLine"
              @updateCompWarehouseByWarehouse="updateCompWarehouseByWarehouse"
            />
          </div>
        </div>
      </div>
      <div class="fixed-edit" ref="fixedEdit">
        <div class="btn-group">
          <el-button type="default" @click="cancelEdit('orderForm')" size="large" style="width:100px">取消修改</el-button>
          <el-button type="primary" @click="handleSubmit('orderForm')" size="large" style="width:100px">保存修改</el-button>
        </div>
      </div>
    </el-form>
    <ItemDetail
      :purchase-data="purchaseData"
      :rowDetail="itemDetail"
      :showDialog.sync="showDetail"
      :itemField="itemField"
      :showCharge="showCharge"
      :isEditDelayedPaymentPo="isEditDelayedPaymentPo"
      :KHWarehouseList="KHWarehouseList"
      @handleChangeDeliveryDate="handleChangeDeliveryDate"
      @updateItem="handleUpdateItem"
    />
    <el-dialog
      title="快速导入商品"
      :visible.sync="showBatchImport"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="600px"
    >
      <BatchImport
        :purchase-data="purchaseData"
        @close="showBatchImport=false"
        @import="handleImport"
      />
    </el-dialog>
    <el-dialog
      title="Punch导入商品"
      :visible.sync="showPunchImport"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="860px"
    >
      <PunchImport
        :visible="showPunchImport"
        @close="showPunchImport=false"
        @import="handlePunchImport"
        :purchase-data="purchaseData"
      />
    </el-dialog>
    <delete-dialog
      :show-dialog.sync="showDeleteDialog"
      :fileList='purchaseData.deleteAttachmentList'
      @getAttachmentList="getAttachmentList"
    />
  </div>
</template>

<script>
import padStart from 'lodash/padStart'
import uniq from 'lodash/uniq'
import merge from 'lodash/merge'
import isEmpty from 'lodash/isEmpty'
import moment from 'moment'
import { mapState } from 'vuex'
import * as shortid from 'shortid'
import { getAssignInfo, isAssigned, calculateTotal as calculatePOTotal, rowEditChange } from '@/utils/poPriceCalculate'
import { updateFields, itemListFields, compFields, assetsFields, planFields } from './utils/field/edit/updateFields.js'
import { filterInProp, registerReCalcStyle, unRegisterReCalcStyle, getAllDictList, submitErrorHandler } from '@/utils/mm'
import { safeRun, deepClone } from '@/utils/index'
import {
  updatePO, getSupplierInfo, getOrderDetail, getCostCenter as getCostCenterApi,
  getProduct, getProductPrice, getBom, getInventory, listCommOptions, getCommDefault,
  getTransferTime, listKHWarehouseUsingPOST, assign, assignToggle, assignByQuantity, calcUnTaxedPrice
} from '@/api/mm'
import { getFestoCustomer, finishData } from '@/api/honeycomb'
import { formatAmount, getDefaultWarehouseLocation, getD02ComponentWarehouseLocation, getKHpurchaseGroup } from './utils'
import { findTopRole } from '@/utils/auth'

import ItemDetail from '@/pages/orderPurchase/components/edit/itemDetail'
import ItemTable from '@/pages/orderPurchase/components/edit/itemTable/index.vue'
import BatchImport from '@/pages/orderPurchase/components/edit/BatchImport'
import PunchImport from '@/pages/orderPurchase/components/create/PunchImport'
import EditBaseInfo from '@/pages/orderPurchase/components/edit/orderHeader/EditBaseInfo.vue'
import EditRemarkText from '@/pages/orderPurchase/components/edit/orderHeader/EditRemarkText.vue'
import EditContactInfo from '@/pages/orderPurchase/components/edit/orderHeader/EditContactInfo'
import EditChargeInfo from '@/pages/orderPurchase/components/edit/orderHeader/EditChargeInfo'
import Attachment from '@/pages/orderPurchase/components/edit/Attachment'
import DeleteDialog from '@/pages/orderPurchase/components/common/DeleteDialog'

// import {chargeProps} from './constants/index'

function getRequiedFieldList (root, requiredList) {
  if (root) {
    root.forEach(child => {
      if (child && child.required && child.prop && child.name) {
        const trigger = ~['custom', 'select', 'input'].indexOf(child.type) ? ['change', 'blur'] : 'blur'
        requiredList[child.prop] = [
          { required: true, message: `${child.name}必填`, trigger }
        ]
      }
    })
  }
}

export default {
  name: 'purchaseOrderEdit',
  components: {
    ItemDetail, ItemTable, BatchImport, EditBaseInfo, EditRemarkText, EditContactInfo, EditChargeInfo, PunchImport, DeleteDialog, Attachment
  },
  data() {
    return {
      showDeleteDialog: false,
      attachmentList: [],
      isFesto: false,
      showPunchImport: false,
      festoSupplierInfo: {
        providerId: 218812,
        supplierNo: 'V45696',
        supplierName: '费斯托（中国）有限公司'
      },
      customerNameOptions: [],
      KHWarehouseList: [],
      submitFestoList: [],
      showHeader: true,
      materialList: [],
      pageLoading: false,
      orderDetail: {},
      deleteReason: {},
      purchaseData: {
        companyCode: '',
        previousCompany: '',
        purchaseGroup: '',
        orderType: '',
        createUser: '',
        createTime: '',
        paymentTermCode: '',
        currency: '',
        receiptContactPhone: '',
        receiptContactName: '',
        receiptAddress: '',
        receiveContactName: '',
        receiveContactPhone: '',
        receiveAddress: '',
        previousSupplier: null,
        supplierContactName: '',
        supplierContactPhone: '',
        supplierAddress: '',

        tariff: undefined,
        tariffCurrency: '',
        tariffCurrencyInput: '',
        tariffSupplierNo: '',
        saleTax: undefined,
        saleTaxCurrency: '',
        saleTaxCurrencyInput: '',
        saleTaxSupplierNo: '',
        latePayment: undefined,
        latePaymentCurrency: '',
        latePaymentCurrencyInput: '',
        latePaymentSupplierNo: '',
        premium: undefined,
        premiumCurrency: '',
        premiumCurrencyInput: '',
        premiumSupplierNo: '',
        intlShippingTypeSelect: '',
        intlShippingType: '',
        intlShipping: undefined,
        intlShippingCurrency: '',
        intlShippingCurrencyInput: '',
        intlShippingSupplierNo: '',
        customsFeeTypeSelect: '',
        customsFeeType: '',
        customsFee: undefined,
        customsFeeCurrency: '',
        customsFeeCurrencyInput: '',
        customsFeeSupplierNo: '',
        other: undefined,
        otherCurrency: '',
        otherCurrencyInput: '',
        otherSupplierNo: '',
        itemList: [],
        supplier: {},
        supplierDict: '',
        poExtend: {
          isDelayedPayment: 0
        }
      },
      activeTab: 'base',
      itemField: [],
      showDetail: false,
      itemDetail: null,
      supplierAddressList: [],
      supplierContactList: [],
      billingAddressList: [],
      receiptAddressList: [],
      receiptContactList: [],
      receiveContactList: [],
      receiveAddressList: [],
      shippingAddressList: [],
      records: [],
      showBatchImport: false,
      costCenterList: [],
      rules: {},
      createEditStatus: /create/.test(location.href) ? 'create' : 'edit',
      keepSupplier: null,
      recoveryMode: false
    }
  },
  watch: {
    'purchaseData.companyCode': function (newVal) {
      if (newVal) {
        const isKHGroup = getKHpurchaseGroup(this.purchaseData.purchaseGroup, this.dictList['componentWarehouse'])
        if (isKHGroup) {
          this.getKHWarehouseUsing()
        }
      }
    },
    'purchaseData.purchaseGroup': function(newVal) {
      if (this.purchaseData.companyCode && newVal) {
        this.getKHWarehouseUsing()
      }
    }
  },
  async created() {
    this.pageLoading = true
    // 初始化枚举、字典
    await getAllDictList(this)
    await this.getDetail()
    const { orderType } = this.purchaseData
    if (!this.fields[orderType] || !this.fields[orderType].length) {
      await this.$store.dispatch('orderPurchase/getPoFields', {
        orderType,
        routerType: 'edit'
      })
    }
    if (orderType === 'Z006') {
      if (this.defaultWarehouseConfigList && this.defaultWarehouseConfigList.length === 0) {
        await this.$store.dispatch('orderPurchase/queryWarehouseConfigList')
      }
    }
    await this.initFesto()
    this.populateDataWithOrderType(orderType)
    this.initRecovery()
    this.pageLoading = false
  },
  mounted () {
    this.reflow()
    this.listener = () => {
      setTimeout(this.reflow, 200)
    }
    registerReCalcStyle(this.listener)
    this.$nextTick(() => {
      window.addEventListener('resize', this.setTableHeight, false)
    })
  },
  beforeDestroy () {
    unRegisterReCalcStyle(this.listener)
    window.removeEventListener('resize', this.setTableHeight, false)
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole,
      dictList: state => state.orderPurchase.dictList,
      companyFactoryList: state => state.orderPurchase.companyFactoryList,
      warehouseList: state => state.orderPurchase.warehouseList,
      purchaseList: state => state.orderPurchase.purchaseList,
      defaultWarehouseConfigList: state => state.orderPurchase.defaultWarehouseConfigList,
      fields: state => state.orderPurchase.edit
    }),
    isGrossMarginOrderType() {
      const { orderType } = this.purchaseData
      const disList = [ 'Z001', 'Z010' ]
      return disList.includes(orderType)
    },
    showHeaderIcon () {
      return this.showHeader ? 'el-icon-caret-bottom' : 'el-icon-caret-top'
    },
    hideByPurchaseGroup () {
      // 采购员禁用付款条件，采购姓名
      return findTopRole(this.userRole) === 'PMS采购员'
    },
    showCharge() {
      const { orderType } = this.purchaseData
      const disList = [ 'Z003', 'Z005' ]
      return !disList.includes(orderType)
    },
    finalFields() {
      let ret = []
      if (Array.isArray(this.fields[this.purchaseData.orderType])) {
        ret = deepClone(this.fields[this.purchaseData.orderType])
      }
      if (this.isFesto && !ret.find(item => item.prop === 'customerName')) {
        const item = {
          category: 'baseInfo',
          disabled: false,
          name: '客户名称',
          prop: 'customerName',
          type: 'custom',
          required: true,
          sequence: 18,
          span: 8
        }
        const idx = ret.findIndex(item => item.type === 'checkbox')
        if (idx > -1) {
          ret.splice(idx, 0, item)
        } else {
          ret.push(item)
        }
      }
      window._console.black('edit1', this.fields, ret)
      return ret
    },
    supplierId () {
      const supplier = (this.purchaseData.supplier || {})
      return supplier.providerId || supplier.supplierNo
    },
    factoryList () {
      const { companyCode, supplierNo } = this.purchaseData
      if (companyCode) {
        if (companyCode === '1000' && supplierNo === 'V00004') {
          const list = ['1000', '1100', '1600', '1700', '1800', '2300']
          const factoryList = this.companyFactoryList
            .map(item => item.factoryList)
            .reduce((prev, next) => {
              prev.push(...next)
              return prev
            }, [])
          return factoryList.filter(fac => list.includes(fac.factoryCode))
        } else {
          const currentCompany = this.companyFactoryList.find(item => item.companyCode === companyCode)
          if (currentCompany) {
            return currentCompany.factoryList
          }
        }
      }
      return []
    },
    // 默认工厂取首行
    factoryCode () {
      const { itemList } = this.purchaseData
      if (itemList.length > 1) {
        const firstItem = itemList[0]
        if (firstItem && firstItem.factoryCode) {
          return firstItem.factoryCode
        }
      }
      return this.factoryList && this.factoryList.length > 0 ? this.factoryList[0].factoryCode : ''
    },
    // 默认仓库取首行
    warehouseLocation () {
      const { itemList } = this.purchaseData
      if (itemList.length > 1) {
        const firstItem = itemList[0]
        if (firstItem && firstItem.warehouseLocation) {
          return firstItem.warehouseLocation
        }
      }
      return ''
    },
    totalAmount () {
      if (this.purchaseData.orderType === 'Z003') {
        return {
          taxedTotal: 0,
          unTaxedTotal: 0
        }
      }
      return calculatePOTotal(this.purchaseData)
    },
    transferSupplierList () {
      const companyCode = this.purchaseData.companyCode
      if (companyCode && this.dictList) {
        return (this.dictList['transferSupplier'] || [])
          .filter(item => item.value.substring(1, 3) === companyCode.substring(0, 2))
      }
      return []
    },
    isDisabledAddItem () {
      // 供应商可能模糊搜索，也可能支持下拉选择
      return !((this.supplierId || this.purchaseData.supplierDict) && this.purchaseData.purchaseGroup)
    },
    // 是否是货主转移
    ownerTransfer() {
      return (this.purchaseData?.taglibMap?.company_transfer || []).find(item => item.id === 102)
    },
    strategicStock() {
      return (this.purchaseData?.taglibMap?.require_type || []).find(item => item.id === 101)
    },
    // 是否正在编辑已开票未付款的订单，此情况下只能修改异常延期付款
    isEditDelayedPaymentPo() {
      const allLineIsInvoiced = this.purchaseData.itemList.filter(item => item.isDeleted === 0).every(item => item.isInvoiced)
      return this.purchaseData.isDeleted === 1 || (this.purchaseData.deliveryStatus === 1 && allLineIsInvoiced)
    }
  },
  methods: {
    getKHWarehouseUsing() {
      const isKHGroup = getKHpurchaseGroup(this.purchaseData?.purchaseGroup, this.dictList['componentWarehouse'])
      if (this.purchaseData.orderType === 'Z006' && this.purchaseData?.companyCode && isKHGroup) {
        listKHWarehouseUsingPOST({ factoryCode: this.purchaseData.companyCode }).then((data) => {
          if (data && data.length > 0) {
            this.KHWarehouseList = data
          } else {
            this.KHWarehouseList = []
          }
        })
      }
    },
    isFixedAssets(orderType, projectCategory) {
      return orderType === 'Z004' && projectCategory === 'A'
    },
    async initFesto () {
      const { supplierNo } = this.purchaseData
      if (/v45696/gim.test(supplierNo)) {
        this.isFesto = true
        console.log('is festo...')
        await this.setCustomerOptions(supplierNo)
      }
    },
    async setCustomerOptions (supplierNo) {
      return getFestoCustomer({ supplierNo })
        .then(data => {
          if (data && data.length > 0) {
            this.customerNameOptions = data.map(item => ({
              value: item.supplierCustomCode,
              label: item.customName
            }))
          } else {
            this.customerNameOptions = []
          }
        })
    },
    initRecovery () {
      const { recovery } = this.$route.query
      if (recovery === 'true') {
        this.recoveryMode = true
        this.recoveryItemList()
      }
    },
    recoveryItemList () {
      const prevItem = this.getItemSnapshot()
      const itemList = this.purchaseData.itemList
      safeRun(async() => {
        await assignToggle(this.purchaseData, itemList).then(data => {
          if (data?.data) {
            const { itemList, ...others } = data.data
            this.purchaseData = merge(this.purchaseData, others)
          }
          console.log(this.purchaseData);
        })
      })
      // 恢复已经删除的商品行
      itemList.forEach(item => {
        if (item.isDeleted) (item.isDeleted = 0)
      })
      this.purchaseData.itemList = itemList

      // 判断第一个有效商品行，并查询默认信息
      const noValidLine = this.compareWithPrevItem(prevItem)
      if (noValidLine) {
        // 清空所有通讯信息
        // this.resetCommunication()
        setTimeout(() => {
          this.$refs['orderForm'].clearValidate()
        }, 100)
      }

      this.setTitleCheckbox('isConfirmed')
      this.setTitleCheckbox('isUrgent')
    },
    disableCharge(prop) {
      if (this.purchaseData.isOverchargeFree === 1) return true
      if (/tariff/.test(prop)) {
        if (this.purchaseData.isZeroTariff) return true
      }
      if (/intlShipping/.test(prop)) {
        if (this.purchaseData.isZeroIntlShipping) return true
      }
      if (/customsFee/.test(prop)) {
        if (this.purchaseData.isZeroCustomsFee) return true
      }
    },
    resetChargeItem (type) {
      this.purchaseData[`${type}`] = undefined
      this.purchaseData[`${type}Amount`] = undefined
      this.purchaseData[`${type}Currency`] = null
      this.purchaseData[`${type}CurrencyInput`] = null
      this.purchaseData[`${type}SupplierNo`] = ''
      const { itemList } = this.purchaseData

      if (type === 'intlShipping' || type === 'customsFee') {
        this.purchaseData[`${type}Type`] = ''
        this.purchaseData[`${type}TypeSelect`] = ''
        itemList.forEach(item => {
          item[`${type}Type`] = ''
        })
      }
      itemList.forEach(item => {
        item[`${type}`] = undefined
        item[`${type}Amount`] = undefined
        item[`${type}Currency`] = null
        item[`${type}CurrencyInput`] = null
        item[`${type}SupplierNo`] = ''
        item[`${type}Supplier`] = ''
      })
      this.$set(this.purchaseData.itemList, itemList)
    },
    handleChangeChargeCheckbox (prop, value) {
      // const chargeProps = ['tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment']
      const checkboxProps = ['isZeroTariff', 'isZeroIntlShipping', 'isZeroCustomsFee']
      // eslint-disable-next-line eqeqeq
      if (checkboxProps.includes(prop) && value == 1) {
        switch (prop) {
          case 'isZeroTariff':
            this.resetChargeItem('tariff'); break;
          case 'isZeroIntlShipping':
            this.resetChargeItem('intlShipping'); break;
          case 'isZeroCustomsFee':
            this.resetChargeItem('customsFee'); break;
        }
      }
    },
    reactiveSetItem (row) {
      const idx = this.purchaseData.itemList.findIndex(item => item.itemNo === row.itemNo)
      if (idx > -1) {
        this.$set(this.purchaseData.itemList, idx, row)
      }
    },
    setSupplierByTitle (supplierNo, item, supplierObj) {
      const supplierName = supplierNo.replace('No', 'Name')
      const supplier = supplierNo.replace('No', '')
      if (supplierObj) {
        item[supplier] = supplierObj
        item[supplierNo] = supplierObj.supplierNo
        item[supplierName] = supplierObj.supplierName
      } else {
        item[supplier] = ''
        item[supplierNo] = null
        item[supplierName] = ''
      }
    },
    setItemSupplier (prop, supplier) {
      const { itemList } = this.purchaseData
      itemList
        .filter(item => !item.isEmptyLine && !item.isDeleted)
        .filter(item => !item.deliveryStatus || item.deliveryStatus === 0)
        .forEach(item => {
          this.setSupplierByTitle(prop, item, supplier)
        })
      this.purchaseData.itemList = itemList
    },
    setTitleCheckbox (prop) {
      const { itemList, taglibIdList } = this.purchaseData
      if (prop === 'isConfirmed') {
        const filterList = itemList.filter(item => !item.isEmptyLine).filter(item => !item.isDeleted)
        if (filterList.length && filterList.every(item => item.isConfirmed)) {
          this.purchaseData.isDeliveryTimeConfirm = 1
        } else {
          this.purchaseData.isDeliveryTimeConfirm = 0
        }
      }
      if (prop === 'isUrgent') {
        const filterList = itemList.filter(item => !item.isEmptyLine).filter(item => !item.isDeleted)
        if (filterList.length && filterList.every(item => item.isUrgent)) {
          this.$set(this.purchaseData, 'isUrgent', 1)
        } else {
          this.$set(this.purchaseData, 'isUrgent', 0)
        }
      }
      if (prop === 'strategicStock') {
        if (taglibIdList.includes(101)) {
          this.$set(this.purchaseData, 'strategicStock', 1)
        } else {
          this.$set(this.purchaseData, 'strategicStock', 0)
        }
      }
      if (prop === 'ownerTransfer') {
         if (taglibIdList.includes(102)) {
          this.$set(this.purchaseData, 'ownerTransfer', 1)
        } else {
          this.$set(this.purchaseData, 'ownerTransfer', 0)
        }
      }
    },
    reflow () {
      safeRun(() => {
        this.$refs.fixedEdit.style.width = `calc(100% - ${parseInt(document.querySelector('.main-side').clientWidth) + 10}px)`
      })
    },
    onlyProps (obj, props) {
      let ret = {}
      for (let prop of props) {
        ret[prop] = obj[prop]
      }
      return ret
    },
    traverseFormat(obj, item, props) {
      // O(n2)
      if (obj[item] && Array.isArray(obj[item])) {
        obj[item] = obj[item].map(item => {
          const data = {}
          for (let prop in props) {
            data[prop] = item[prop]
          }
          return data
        })
      }
    },
    getCostCenter(code, keyword = '') {
      getCostCenterApi({
        companyCode: code,
        costCenter: keyword
      }).then(data => {
        this.costCenterList = data
      })
    },
    formatParams (params) {
      // const formatPropList = [
      //   'tariff', 'saleTax', 'intlShipping', 'latePayment', 'customsFee'
      // ]
      params = JSON.parse(JSON.stringify(params))
      let data = {}
      // format 商品行
      for (let field of updateFields) {
        data[field] = params[field]
      }
      // format itemList
      if (data.itemList) {
        data.itemList = data.itemList.map(item => this.onlyProps(item, itemListFields))
        // data.itemList.forEach(item => {
        //   // 数额, 币别, 供应商必须同为真值
        //   formatPropList.forEach(prop => !item[`${prop}Amount`] ? this.setRowChargeFields(item, prop) : null)
        // })
      }
      // compFields, assetsFields, planFields
      const timeStart = performance.now()
      // format 委外组件
      safeRun(() => {
        data.itemList.forEach(item => {
          if (item.componentList) {
            item.componentList = item.componentList.map(comp => this.onlyProps(comp, compFields))
          }
        })
      })
      // format 计划行
      safeRun(() => {
        data.itemList.forEach(item => {
          if (item.planList) {
            item.planList = item.planList.map(comp => this.onlyProps(comp, planFields))
          }
        })
      })
      // format 固定资产
      safeRun(() => {
        data.itemList.forEach(item => {
          if (item.fixedAssetsList) {
            item.fixedAssetsList = item.fixedAssetsList.map(comp => this.onlyProps(comp, assetsFields))
          }
        })
      })
      if (this.recoveryMode) {
        data.recoverAction = true
      }
      const timeEnd = performance.now()
      safeRun(() => {
        window._console.green(`O(n3) format time cost: ${(timeEnd - timeStart)}ms`)
        window._console.red('update formatted data:', data)
      })
      safeRun(() => {
        data.updateUser = window.CUR_DATA.user.name
      })
      return data
    },
    fullDetailAddress (type) {
      let ret = ''
      safeRun(() => {
        ret = (this.purchaseData[`${type}ProvinceText`] || '') +
                (this.purchaseData[`${type}CityText`] || '') +
                (this.purchaseData[`${type}RegionText`] || '') +
                (this.purchaseData[`${type}StreetText`] || '') +
                (this.purchaseData[`${type}AddressDetail`] || '')
      })
      return ret
    },
    compareWithPrevItem (prevItem) {
      // 判断第一个有效商品行，并查询默认信息
      const index = this.getFirstLineUpdateDefaultAddress()
      if (index !== null) {
        const thisItem = this.getItemSnapshot()
        if (prevItem !== thisItem) {
          console.log('prevItem is not thisItem, query default address')
          this.resetDefaultAddress(index)
        }
      } else {
        return index === null
      }
    },
    getItemSnapshot () {
      const idx = this.getFirstLineUpdateDefaultAddress()
      let item = null
      if (idx !== null) {
        item = this.purchaseData.itemList[idx]
      }
      return item
    },
    /**
     * return first line index which was valid for updating default address
     */
    getFirstLineUpdateDefaultAddress () {
      const { itemList } = this.purchaseData
      let retIndex = null
      for (let index = 0; index < itemList.length; index++) {
        const item = itemList[index]
        if (item && !item.isEmptyLine && !item.isDeleted) {
          retIndex = index; break;
        }
      }
      return retIndex
    },
    initContactAddress () {
      const { supplierAddressDetail, receiptAddressDetail, receiveAddressDetail } = this.purchaseData
      if (supplierAddressDetail) {
        this.purchaseData.supplierAddress = this.fullDetailAddress('supplier')
      }
      if (receiptAddressDetail) {
        this.purchaseData.receiptAddress = this.fullDetailAddress('receipt')
      }
      if (receiveAddressDetail) {
        this.purchaseData.receiveAddress = this.fullDetailAddress('receive')
      }
    },
    initAddress (withoutDefaultAddress) {
      this.purchaseData.receiptAddress = this.purchaseData.receiptAddressDetail
      if (this.purchaseData.itemList.length > 0) {
        console.log('edit run this.queryDefaultAddress')
        const { supplierNo } = this.purchaseData
        const { purchaseGroup } = this.purchaseData
        const { factoryCode, warehouseLocation, soNo, skuNo } = this.purchaseData.itemList[0]
        if (factoryCode) {
          this.queryAddressList(supplierNo, factoryCode, warehouseLocation)
          if (!withoutDefaultAddress) {
            this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo, skuNo)
          }
        }
      }
    },
    populateDataWithOrderType (orderType) {
      window._console.red(`populate edit data by order type: ${orderType}`)
      const fields = this.finalFields || []
      const baseItem = fields.filter(item => item.category === 'baseInfo' && (/edit/.test(item.status) || !item.status))
      // add rules for baseItem
      const requiredList = {}
      getRequiedFieldList(baseItem, requiredList)
      const communicationItem = fields.filter(item => item.category === 'comm' && (/edit/.test(item.status) || !item.status))
      if (orderType === 'Z003') {
        this.purchaseData.supplier = this.getTransferSupplier()
        this.purchaseData.supplierDict = this.purchaseData.supplierNo
        communicationItem.filter(item => {
          item.required = 0
        })
      }
      // add rules for communicationItem
      const requiredListCommunicationItem = {}
      getRequiedFieldList(communicationItem, requiredListCommunicationItem)

      this.rules = {
        ...requiredList, ...requiredListCommunicationItem
      }
      this.purchaseData.orderType = orderType
      const baseItem2 = fields.filter(item => /detail(Info|Plan|Price|Assets|Entrust|Asset)/gi.test(item.category) && (/edit/.test(item.status) || !item.status))
      this.itemField = baseItem2
      if (orderType === 'Z008') {
        this.getCostCenter(this.purchaseData.companyCode)
      }
    },
    setSupplierDetail () {
      this.purchaseData.supplier = {
        isValid: 1,
        supplierName: this.purchaseData.supplierName,
        supplierNo: this.purchaseData.supplierNo,
        supplierId: this.purchaseData.supplierId
      }
    },
    populdatePriceSupplier (prop, item) {
      const name = prop.replace('No', 'Name')
      const supplier = prop.replace('No', '')
      if (!item[prop]) {
        item[supplier] = ''
        item[prop] = ''
        item[name] = ''
      } else {
        item[supplier] = {
          supplierNo: item[prop],
          supplierName: item[name]
        }
      }
    },
    // 修改仓库点更改委外组件仓库地点
    updateCompWarehouseByWarehouse (warehouseLocation, index, row) {
      // if (Array.isArray(row.componentList)) {
      //   row.componentList.forEach(comp => {
      //     if (comp._defaultConfigWarehouse) {
      //       comp.componentWarehouseLocation = getD02ComponentWarehouseLocation(this.purchaseData.orderType, this.purchaseData.purchaseGroup, this.KHWarehouseList, warehouseLocation)
      //     }
      //   })
      // }
      // this.$set(this.purchaseData.itemList, index, row)
    },
    initItemList () {
      const { itemList, orderType } = this.purchaseData
      if (!Array.isArray(itemList)) return
      itemList.forEach(item => {
        if (item.skuNo) {
          // 系统内物料
          item.inSystemMaterial = true
          item.initialShippingAmount = item.initialItemShippingAmount
        } else {
          if (item.materialGroupNum) {
            item.materialGroup = {
              materialGroupNum: item.materialGroupNum,
              materialGroupName: item.materialGroupName
            }
            item.initialShippingAmount = item.initialItemShippingAmount
          }
        }
        item.uuid = shortid.generate()
        // chargeProps.forEach(charge => {
        //   this.populdatePriceSupplier(`${charge}SupplierNo`, item)
        // })
        this.populdatePriceSupplier('tariffSupplierNo', item)
        this.populdatePriceSupplier('saleTaxSupplierNo', item)
        this.populdatePriceSupplier('intlShippingSupplierNo', item)
        this.populdatePriceSupplier('latePaymentSupplierNo', item)
        this.populdatePriceSupplier('customsFeeSupplierNo', item)
        if (this.purchaseData.orderType === 'Z002') {
          this.populdatePriceSupplier('otherSupplierNo', item)
          this.populdatePriceSupplier('premiumSupplierNo', item)
        }
        // 标记服务端传的计划行
        this.handleServerNoInit(item)
      })
      // 委外组件详情每一行都获取一下bom
      if (orderType === 'Z006') {
        itemList.forEach(item => {
          if (item.componentList) {
            item.componentList.forEach(component => {
              // 记录下数量和组件数量的比例
              component.numCalScale = component.componentRequiredQuantity / item.itemQuantity
              component.materialGroupNum = item.materialGroupNum
            })
          }
        })
      }
      let tmpMap = {}
      let tmpArray = []
      itemList.forEach((item, index) => {
        safeRun(() => {
          // 固定资产卡片号 取固定资产组件行第一行 固定资产卡片号
          if (item.fixedAssetsList && item.fixedAssetsList.length && item.fixedAssetsList[0].fixedAssetsCardNo) {
            this.$set(this.purchaseData.itemList, index, {
              ...item,
              fixedAssetsCardNo: item.fixedAssetsList[0].fixedAssetsCardNo
            })
          }
          if (orderType === 'Z004') {
            if (!item.fixedAssetsList || item.fixedAssetsList.length === 0) {
              const fixedAssetsList = [{
                fixedAssetsNo: null,
                fixedAssetsCardQuantity: item.itemQuantity,
                fixedAssetsCardNo: item.fixedAssetsCardNo
              }]
              this.$set(this.purchaseData.itemList, index, {
                ...item,
                fixedAssetsList: fixedAssetsList
              })
            }
          }
          // 计划行排序
          if (item.planList) {
            item.planList = item.planList.sort((a, b) => a.planNo - b.planNo)
          }
        })
        if (!tmpMap[item.materialGroupNum]) {
          tmpMap[item.materialGroupNum] = true
          tmpArray.push({
            materialGroupNum: item.materialGroupNum,
            materialGroupName: item.materialGroupName
          })
        }
      })
      // 物料组初始下拉
      this.materialList = tmpArray
    },
    async getDetail () {
      return getOrderDetail({ orderNo: this.$route.params.id })
        .then(data => {
          if (data) {
            const { orderType } = data
            if (orderType === 'Z006') {
              // lossRatio 如果为null input-number 组件将初始化为0,则提交报错
              if (data.lossRatio == null) {
                data.lossRatio = undefined
              }
            }
            data.itemList.map(item => {
              if (item.approveTriggerDetail) {
                item.newOrderReason = ''
              }
              item.sundryAmount = item?.poItemExtend?.sundryAmount || 0
              item.rebateAmount = item?.poItemExtend?.rebateAmount || 0
              // 增加交期变更原因
              item.deliveryChangeReason = item.planList?.[0]?.deliveryChangeReason ? item.planList?.[0]?.deliveryChangeReason + '' : ''
              item.__originalDeliveryDate = item.planList?.[0]?.deliveryDate || item.deliveryDate
              return item
            })
            this.orderDetail = data
            this.purchaseData = {
              ...this.purchaseData,
              ...data,
              strategicStock: 0,
              ownerTransfer: 0,
              shareInitialShippingAmount: data.initialShippingAmount,
              sundryAmount: data?.poExtend?.shareSundryAmount || 0,
              sundryReason: data?.poExtend?.sundryReason ? data?.poExtend?.sundryReason.split(',') : [],
              sundryReasonDetail: data.poExtend?.sundryReasonDetail || '',
              poExtend: data.poExtend ? data.poExtend : {
                isDelayedPayment: 0
              }
            }
            if (this.ownerTransfer) {
              this.purchaseData.ownerTransfer = 1
            }
            if (this.strategicStock) {
              this.purchaseData.strategicStock = 1
            }
            this.initItemList()
            // 修改页面并不需要重新查询地址
            this.initAddress(true)
            this.initContactAddress()
            this.addItemEmptyLine()
            this.setSupplierDetail()
            this.setTitleCheckbox('isUrgent')
          }
        })
    },
    getTransferSupplier () {
      const companyCode = this.purchaseData.companyCode
      if (companyCode && this.dictList) {
        const val = `V${companyCode}`
        const currentSupplier = (this.dictList['transferSupplier'] || []).find(item => item.value === val)
        return {
          supplierNo: val,
          supplierId: val,
          providerId: val,
          supplierName: currentSupplier ? currentSupplier.name : ''
        }
      }
      return null
    },
    getInputTax (val) {
      const taxRate = Number(((this.dictList['inputTax'] || []).find(item => item.value === val) || {}).description) || 0
      return taxRate
    },
    inWarehouse(code) {
      let ret = false
      safeRun(() => {
        ret = this.warehouseList.filter(item => item.warehouseLocationCode === code).length
      })
      return ret
    },
    inCostCenter(val) {
      let ret = false
      safeRun(() => {
        ret = this.costCenterList.filter(item => item.costCenter === val).length
      })
      return ret
    },
    inGeneralLedger(val) {
      let ret = false
      safeRun(() => {
        ret = this.dictList.generalLedger.filter(item => item.value === val).length
      })
      return ret
    },
    setDefaultCompanyCode (data, companyCode) {
      if (data && data.length > 0) {
        if (companyCode) {
          const currentCompany = data.find(item => item.companyCode === companyCode)
          if (currentCompany) {
            const factoryList = currentCompany.factoryList
            if (factoryList && factoryList.length > 0) {
              const factoryCode = factoryList[0].factoryCode
              if (factoryCode) {
                this.purchaseData.itemList[0].factoryCode = factoryCode
              }
            }
          }
        }
      }
    },
    handleTabClick (tab) {
      if (tab.name) {
        this.activeTab = tab.name
      }
      this.showHeader = true
      this.setTableHeight()
    },
    handleFold () {
      this.showHeader = !this.showHeader
      this.setTableHeight()
    },
    setTableHeight () {
      const grid = this.$refs.itemTable
      setTimeout(() => {
        grid.tableHeight = window.innerHeight - grid.$el.offsetTop - 200
        if (grid.tableHeight < 355) {
          grid.tableHeight = 355
        }
      }, 100)
    },
    startLoading () {
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)',
        lock: true
      })
      return loading
    },
    endLoading (loading) {
      if (loading) {
        loading.close()
      }
    },
    validateInventory (params) {
      // 可用库存均满足需求数量
      safeRun(() => {
        if (params.itemList.every(item =>
          item.componentList && item.componentList.every(comp => comp.componentRequiredQuantity <= comp.availableQty)
        )) {
          this.$confirm('委外组件可用库存已满足需求数量，请尽快安排委外发料。', '操作提示', {
            confirmButtonText: '委外发料',
            cancelButtonText: '返回',
            type: 'warning'
          }).then(() => {
            this.$message({ type: 'success', message: '委外发料!' })
          }).catch(() => {
            this.$message({ type: 'success', message: '返回!' })
          })
        }
      })
    },
    validatePrice (params) {
      let ret = true
      const priceCheckList = [ 'Z001', 'Z002', 'Z006', 'Z013', 'Z005' ]
      // run with catch error
      safeRun(() => {
        if (~priceCheckList.indexOf(params.orderType)) {
          // eslint-disable-next-line
          let item = params.itemList.filter(item => item.noMaintainPrice && item.isFree != 1)
          if (item.length) {
            ret = false
            this.$message.error(`sku:${item.map(i => i.skuNo).join(',')} 没有价格信息且没有勾选免费！`)
          }
        }
        params.itemList.forEach(item => delete item.noMaintainPrice)
      })
      return ret
    },
    validateSo (item) {
      let ret = true
      safeRun(() => {
        const ret1 = item.itemList.some(it => {
          // 关联销售单, 关联销售单行号
          const { soNo, soItemNo } = it
          return soItemNo && !soNo
        })
        if (ret1) {
          ret = false
          this.$message.error('填写关联销售单行号，则关联销售单号必填！')
          return
        }
        const ret2 = item.itemList.some(it => {
          // 关联销售单, 关联销售单行号, 关联销售单计划行号
          const { soNo, soItemNo, soPlanNo } = it
          return soPlanNo && (!soNo || !soItemNo)
        })
        if (ret2) {
          ret = false
          this.$message.error('填写关联销售单计划行，则关联销售单行号、关联销售单号必填！')
        }
        const ret3 = item.itemList.some(it => {
          const { projectCategory, materialDescription } = it
          return !materialDescription && (projectCategory === 'A')
        })
        const ret4 = item.itemList.some(it => {
          const { projectCategory, fixedAssetsCardNo } = it
          return !fixedAssetsCardNo && (projectCategory === 'A')
        })
        if (item.orderType === 'Z004' && ret3) {
          ret = false
          this.$message.error('请填写物料描述')
        } else if (item.orderType === 'Z004' && ret4) {
          ret = false
          this.$message.error('请填写资产卡片号')
        }
        const ret5 = item.itemList.some(it => {
          const { inputTax, skuNo } = it
          return skuNo && !inputTax
        })
        if (ret5 && item.orderType !== 'Z003') {
          ret = false
          this.$message.error('请选择进项税！')
        }
        const ret6 = item.itemList.some(it => {
          const { specialSupplySo, specialSupplySoItem, projectCategory } = it
          return projectCategory === 'S' && (!specialSupplySo || !specialSupplySoItem)
        })
        if (ret6 && item.orderType === 'Z004' && item.isDirect === 1) {
          ret = false
          this.$message.error('专供销售单号、专供销售单行号必填!')
        }
      })
      return ret
    },
    cancelEdit () {
      const id = this.$route.params.id
      this.$closeTag(this.$route.path)
      setTimeout(() => {
        this.$router.push({
          path: `/orderPurchase/detail/${id}`,
          query: { tagName: `${id}详情` }
        })
      })
    },
    handleServerNoSubmit (item, orderType) {
      item = JSON.parse(JSON.stringify(item))
      // 服务端商品行，计划行标记
      if (item && !item.__itemNo) {
        // delete item.__itemNo
        item.itemNo = null
      }
      item && item.planList && item.planList.forEach(plan => {
        if (!plan.__planNo) {
          // delete plan.__planNo
          plan.planNo = null
        }
      })
      safeRun(() => {
        item && item.componentList && item.componentList.forEach(item => {
          if (!item.__componentNo) {
            item.componentNo = null
          }
        })
      })
      safeRun(() => {
        item && item.fixedAssetsList && item.fixedAssetsList.forEach(item => {
          if (!item.__fixedAssetsNo) {
            item.fixedAssetsNo = null
          }
        })
      })
      return item
    },
    handleServerNoInit (item) {
      // 服务端商品行，计划行提交删除
      if (item && item.itemNo) {
        item.__itemNo = true
      }
      safeRun(() => {
        item && item.planList && item.planList.forEach(item => {
          item.__planNo = true
        })
      })
      safeRun(() => {
        item && item.componentList && item.componentList.forEach(item => {
          item.__componentNo = true
        })
      })
      safeRun(() => {
        item && ((item.fixedAssetsList ?? '') !== '') && item.fixedAssetsList.forEach(item => {
          item.__fixedAssetsNo = true
        })
      })
    },
    filterValid (item) {
      const { orderType } = this.purchaseData
      if (item.isEmptyLine) return false
      const validDesc = [
        'Z007', 'Z008', 'Z010', 'Z004'
      ]
      if (validDesc.includes(orderType)) {
        return item.materialDescription
      }
      return item.skuNo
    },
    validateRelateOrder (params) {
      let ret = true
      let errorText = ''
      if (params && Array.isArray(params.itemList)) {
        params.itemList.forEach((item, index) => {
          if ((item.soNo && !item.soItemNo) || (item.soItemNo && !item.soNo)) {
            ret = false
            errorText += index + 1 + ' '
          }
        })
      }
      if (!ret) {
        this.$message.error(`第${errorText}行关联销售单号、行号必须同时填写！`)
      }
      return ret
    },
    parseDate(date) {
      var result = new Date(date);
      result.setMinutes(result.getMinutes() - result.getTimezoneOffset());
      return result;
    },
    diffDays(second, first) {
      return Math.abs(Math.round((this.parseDate(second) - this.parseDate(first)) / (1000 * 60 * 60 * 24)));
    },
    festoSubmit () {
      if (this.isFesto && this.submitFestoList.length) {
        const { supplierNo } = this.purchaseData
        finishData({ supplierNo: supplierNo, ids: this.submitFestoList.map(item => item.id).join(',') })
          .then(res => {
            console.log(res)
          })
      }
    },
    handleSubmit (formName) {
      const materialList = [ 'Z007', 'Z008' ]
      this.$refs[formName].validate((valid, validteItem) => {
        const flag = Object.keys(validteItem).every(v => /receive|receipt/.test(v))
        if (Object.keys(validteItem).length > 0 && flag) {
          this.activeTab = 'communication'
        }
        // eslint-disable-next-line no-unused-vars
        let itemList = JSON.parse(JSON.stringify(this.purchaseData.itemList))
        if (valid) {
          if (!this.validateSo(this.purchaseData)) return
          // if (this.ownerTransfer) {
          //   this.$alert('如果修改订单数量，采购价格，仓库地点，交货完成按钮时，请务必通知对应的客服更新销售订单！', '提示', {
          //     type: 'warning'
          //   })
          // }
          const { supplier, orderType } = this.purchaseData
          const processOrderChannel = (item) => {
            // console.log(this.strategicStock, this.purchaseData.strategicStock);
            if (this.purchaseData.strategicStock) {
              if (isEmpty(item?.poItemExtend?.orderChannelList)) {
                return [3]
              } else if (item?.poItemExtend?.orderChannelList?.includes(3)) {
                return item.poItemExtend.orderChannelList
              } else {
                return [...item?.poItemExtend?.orderChannelList, 3]
              }
            } else {
              return item?.poItemExtend?.orderChannelList
            }
          }
          let errMsg = ''
          itemList = itemList.filter(this.filterValid).map((item, key) => {
            // 处理计划行，商品行行号
            item = this.handleServerNoSubmit(item, orderType)
            // 如果计划交期大于原交期，交期变更原因必填
            if (item.__originalDeliveryDate && item.__originalDeliveryDate < item.planList[0].deliveryDate && !item.deliveryChangeReason) {
              errMsg = `第${key + 1}行计划交货日期小于交货日期，交期变更原因必填！`
            }
            let { itemQuantity, taxedPrice, materialGroupNum, taxedTotalAmount, unit, orderReason, newOrderReason, deleteReason } = item
            itemQuantity = parseFloat(itemQuantity)
            taxedPrice = parseFloat(taxedPrice)
            const newPlanList = item.planList.map(plan => {
              return {
                ...plan,
                deliveryChangeReason: item.deliveryChangeReason || '0'
              }
            })
            const result = {
              ...item,
              planList: newPlanList,
              itemQuantity: itemQuantity.toFixed(3),
              taxedPrice: taxedPrice ? taxedPrice.toFixed(2) : taxedPrice === '' ? taxedPrice : 0,
              materialGroupId: materialGroupNum,
              taxedTotalAmount: formatAmount(taxedTotalAmount || 0, 2),
              priceUnit: unit,
              orderReason: newOrderReason && orderReason === '其他' ? newOrderReason : orderReason,
              deleteReason: item.isDeleted ? deleteReason : ''
            }
            // 非进口订单的国际运费类型和报关杂费类型也要有个默认值
            if (item.intlShippingAmount && !item.intlShippingType) result.intlShippingType = 4
            if (item.customsFeeAmount && !item.customsFeeType) result.customsFeeType = 5

            result.itemQuantity = Number(result.itemQuantity)
            if (orderType === 'Z004' && result.projectCategory !== 'A' && (result.fixedAssetsCardNo ?? '') === '' && result.fixedAssetsList) {
              delete result.fixedAssetsList
            }
            if ((result.warehouseLocation ?? '') === '') {
              result.warehouseLocation = null
            }
            result.poItemExtend = {
              ...item.poItemExtend,
              initGrossMargin: item?.poItemExtend?.lastGrossMargin,
              skuNo: item.skuNo,
              untaxedSalePrice: item.untaxedSalePrice,
              sundryAmount: item.sundryAmount,
              rebateAmount: item.rebateAmount,
              orderChannelList: processOrderChannel(item)
            }
            if (materialList.includes(orderType)) {
              delete result.warehouseLocation
              delete result.warehouseLocationCode
              delete result.warehouseLocationName
            }
            delete result.newOrderReason
            delete result.uuid
            // 删除商品行不再清空分摊信息
            // if (isDeleted === 1) {
            //   clearItemAssign(result)
            //   console.log(result)
            // }
            if (orderType === 'Z003') {
              result.taxedPrice = 0
              result.taxedTotalAmount = 0
              result.taxedTotalAmount = 0
              result.untaxedTotalAmount = 0
              result.priceTimes = 1
            }
            return result
          })
          if (errMsg) {
            this.$message.error(errMsg)
            return
          }
          if (this.purchaseData?.taglibMap && this.purchaseData?.taglibMap['require_type'] && this.purchaseData?.taglibMap['require_type']?.id === 101) {
            this.purchaseData.taglibIdList.push(101)
          }
          const params = {
            ...this.purchaseData,
            itemList,
            deleteAttachmentList: this.attachmentList,
            supplierContractAttachmentList: this.purchaseData.supplierContractAttachmentList,
            initialShippingAmount: this.purchaseData.shareInitialShippingAmount
          }

          // 非进口订单的国际运费类型和报关杂费类型也要有个默认值
          if (params.intlShippingAmount && !params.intlShippingType) params.intlShippingType = 4
          if (params.customsFeeAmount && !params.customsFeeType) params.customsFeeType = 5

          // delete params.createTime
          if (supplier) {
            const { supplierNo, supplierName } = supplier
            params.supplierNo = supplierNo
            params.supplierName = supplierName
            delete params.supplier
          }
          if (this.attachmentList.length === 0) {
            delete params.deleteAttachmentList
          }
          if (!this.validatePrice(params)) return
          // if (orderType === 'Z006') {
          //   this.validateInventory(params)
          // }
          if (orderType === 'Z010') {
            if (!this.validateRelateOrder(params)) return
          }
          if (typeof params.customerName === 'object') {
            params.customerCode = params.customerName.value
            params.customerName = params.customerName.label
          }
          params.poExtend = {
            ...params.poExtend,
            lastGrossMargin: this.totalAmount.grossMarginTotal,
            initGrossMargin: this.totalAmount.grossMarginTotal,
            shareSundryAmount: params.sundryAmount,
            sundryReason: params.sundryReason.join(','),
            sundryReasonDetail: params.sundryReasonDetail
          }
          params.initialShippingAmount = params.shareInitialShippingAmount
          let data = this.formatParams(params)
          console.log(data);
          const loading = this.startLoading()
          updatePO(data).then(res => {
            this.endLoading(loading)
            if (res) {
              const { code, data, msg } = res
              if (code === 0 && data) {
                const { orderNo } = data
                this.$alert(`${orderNo ? '订单' + orderNo : ''}修改成功！`, '成功', {
                  confirmButtonText: '确定',
                  type: 'success',
                  callback: action => {
                    const id = this.$route.params.id
                    this.$closeTag(this.$route.path)
                    this.festoSubmit()
                    setTimeout(() => {
                      this.$router.push({
                        path: `/orderPurchase/detail/${id}`,
                        query: { tagName: `${id}详情` }
                      })
                    })
                  }
                })
              } else if (code !== 0) {
                let errMsg = '订单修改失败！<br>'
                submitErrorHandler(errMsg, data, msg)
              }
            }
          })
        }
      })
    },
    resetSupplier (companyCode) {
      if (this.purchaseData.orderType !== 'Z003') {
        this.setSupplier('')
      } else {
        this.purchaseData.supplier = this.getTransferSupplier()
        this.purchaseData.supplierDict = `V${companyCode}`
      }
    },
    setReturnKey(val, prop) {
      if (this.purchaseData.orderType === 'Z004') {
        this.purchaseData[prop] = val
      }
    },
    resetCharge () {
      const inlineList = [ 'tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment', 'other', 'premium' ]
      const titleList = ['shareShipping', 'shareDiscount', ...inlineList]
      safeRun(() => {
        titleList.forEach(charge => {
          this.purchaseData[`${charge}`] = undefined
          this.purchaseData[`${charge}Amount`] = ''
          this.purchaseData[`${charge}Currency`] = ''
          this.purchaseData[`${charge}CurrencyInput`] = ''
          if (charge === 'intlShipping' || charge === 'customsFee') {
            this.purchaseData[`${charge}Type`] = ''
            this.purchaseData[`${charge}TypeSelect`] = ''
          }
          this.purchaseData.sundryAmount = ''
          this.purchaseData.sundryReason = ''
          this.purchaseData.sundryReasonDetail = ''
          this.purchaseData.usedRebaseVoucherList = []
          this.purchaseData.shareRebateAmount = ''
        })
      })
    },
    setSupplier (val) {
      const { supplierNo } = val
      const { companyCode } = this.purchaseData
      this.previousSupplier = val
      if (supplierNo) {
        const loading = this.startLoading()
        getSupplierInfo({
          supplierNo,
          factoryCode: companyCode
        }).then(data => {
          this.endLoading(loading)
          if (data) {
            const {
              sapCurrency, paymentTermCode
            } = data
            if (paymentTermCode) {
              if (this.purchaseData.orderType !== 'Z003') {
                this.purchaseData.paymentTermCode = paymentTermCode
              }
            }
            if (sapCurrency) {
              if (this.purchaseData.orderType !== 'Z003') {
                this.purchaseData.currency = sapCurrency
              }
            }
          }
        })
        this.queryAddressList(supplierNo, this.factoryCode)
      } else {
        this.purchaseData.supplier = ''
        this.supplierAddressList = []
        this.supplierContactList = []
        this.purchaseData.supplierContactName = ''
        this.purchaseData.supplierContactPhone = ''
        if (this.purchaseData.orderType !== 'Z003') {
          this.purchaseData.paymentTermCode = ''
          this.purchaseData.currency = ''
        }
      }
    },
    // reCalcUntaxedPrice (item) {
    //   const { inputTax = 0, taxedPrice = 1 } = item
    //   const taxRate = this.getInputTax(inputTax)
    //   return Number((taxedPrice / (1 + 0.01 * taxRate)))
    // },
    reCalcCompQuantity (oldData, rowItem, ignoreCompare) {
      let ret = false
      if (!ignoreCompare) {
        if (oldData.itemQuantity !== rowItem.itemQuantity || oldData.planList.length !== rowItem.planList.length) {
          ret = true
        }
      }
      let { lossRatio } = this.purchaseData
      let { itemQuantity } = rowItem
      if (!lossRatio) lossRatio = 0
      if (!rowItem || !Array.isArray(rowItem.componentList)) return
      rowItem.componentList.forEach(comp => {
        const { materialGroupNum } = comp
        if (!itemQuantity) itemQuantity = 0
        let number = Number((comp.numCalScale * itemQuantity).toFixed(3))
        if (materialGroupNum === 430) {
          number = Math.ceil(number)
        }
        comp.componentRequiredQuantity = number
      })
      return ret
    },
    async handleUpdateItem (val) {
      console.log(val);
      const { rowDetail, data, updateTypes = [] } = val
      if (rowDetail && rowDetail.uuid) {
        const idx = this.purchaseData.itemList.findIndex(item => item.uuid === rowDetail.uuid)
        if (idx > -1) {
          let newData = this.purchaseData.itemList[idx]
          if (updateTypes.includes('detail')) {
            const {
              itemRemark = '', materialDescription = '', isLastInvoice,
              supplierOrderNo = '', specialSupplySo = '', specialSupplySoItem = '',
              oaItemNo = '', oaNo = '', oaType = ''
            } = data.detailData
            newData = {
              ...newData,
              supplierOrderNo,
              itemRemark,
              materialDescription,
              isLastInvoice,
              specialSupplySo,
              specialSupplySoItem,
              oaItemNo,
              oaNo,
              oaType
            }
            // this.$set(this.purchaseData.itemList, idx, newData)
          }
          if (updateTypes.includes('price')) {
            // 返利和杂费在商品行详情无法修改，因此这里不需要重新计算头上的返利和杂费金额
            const {
              tariffAmount, tariffSupplierNo, tariffSupplier, tariffCurrency,
              saleTaxAmount, saleTaxSupplierNo, saleTaxSupplier, saleTaxCurrency,
              intlShippingAmount, intlShippingSupplierNo, intlShippingSupplier, intlShippingCurrency, intlShippingType,
              latePaymentAmount, latePaymentSupplierNo, latePaymentSupplier, latePaymentCurrency,
              customsFeeAmount, customsFeeSupplierNo, customsFeeSupplier, customsFeeCurrency, customsFeeType,
              premiumAmount, premiumSupplierNo, premiumSupplier, premiumCurrency,
              otherAmount, otherSupplierNo, otherSupplier, otherCurrency
            } = data.priceData
            newData = {
              ...newData,
              tariffAmount,
              tariffSupplierNo,
              tariffSupplier,
              tariffCurrency,
              saleTaxAmount,
              saleTaxCurrency,
              saleTaxSupplierNo,
              saleTaxSupplier,
              intlShippingAmount,
              intlShippingCurrency,
              intlShippingSupplierNo,
              intlShippingSupplier,
              intlShippingType,
              latePaymentAmount,
              latePaymentCurrency,
              latePaymentSupplierNo,
              latePaymentSupplier,
              customsFeeAmount,
              customsFeeCurrency,
              customsFeeSupplierNo,
              customsFeeSupplier,
              customsFeeType,
              premiumAmount,
              premiumSupplierNo,
              premiumSupplier,
              premiumCurrency,
              otherAmount,
              otherSupplierNo,
              otherSupplier,
              otherCurrency
            }
          }
          if (updateTypes.includes('entrust')) {
            const componentList = data.entrustData || []
            if (componentList.length > 0) {
              // const itemQuantity = componentList.reduce((acc, item) => {
              //   return item.fixedAssetsCardQuantity + acc
              // }, 0)
              newData = {
                ...newData,
                componentList
                // itemQuantity
              }
              // this.$set(this.purchaseData.itemList, idx, newData)
            }
          }
          if (updateTypes.includes('assets')) {
            const fixedAssetsList = data.assetsData || []
            if (fixedAssetsList.length > 0) {
              const itemQuantity = fixedAssetsList.reduce((acc, item) => {
                return (item.fixedAssetsCardQuantity || this.purchaseData.itemList[idx].itemQuantity) + acc
              }, 0)
              newData = {
                ...newData,
                fixedAssetsList,
                itemQuantity
              }
              // this.$set(this.purchaseData.itemList, idx, newData)
            }
          }
          if (updateTypes.includes('customInstructions')) {
            newData = {
              ...newData,
              customInstructions: JSON.stringify(data.customInstructionData)
            }
            // this.$set(this.purchaseData.itemList, idx, newData)
          }
          if (updateTypes.includes('plan')) {
            const planList = data.planData || []
            if (planList.length > 0) {
              const itemQuantity = planList.reduce((acc, item) => {
                return item.deliveryQuantity + acc
              }, 0)
              const { inputTax = 0, itemNo, taxedPrice, priceTimes = 1 } = this.purchaseData.itemList[idx]
              // const untaxedPrice = this.reCalcUntaxedPrice(this.purchaseData.itemList[idx])
              // window._console.blue('untaxedPrice:', untaxedPrice)
              const hasAssign = isAssigned(this.purchaseData.itemList[idx])
              const currentItemQuantity = (this.purchaseData.itemList[idx] || {}).itemQuantity || 0
              if (hasAssign && itemQuantity > currentItemQuantity) {
                this.$message.error(`项目行${itemNo}已分摊运费或折扣或返利，不允许将订单数量改大。`)
                return
              }
              // 如果数量减少，重新计算分摊
              if (hasAssign && itemQuantity < currentItemQuantity) {
                await assignByQuantity(this.purchaseData, this.purchaseData.itemList[idx], currentItemQuantity, itemQuantity).then(data => {
                  if (data) {
                    this.purchaseData = merge(this.purchaseData, data.data)
                    // this.$set(this.purchaseData.itemList, idx, merge())
                  }
                })
              }
              const taxRate = this.getInputTax(inputTax)
              const taxedTotalAmount = formatAmount(taxedPrice ? taxedPrice * itemQuantity / priceTimes : 0, 2)
              // const untaxedTotalAmount = formatAmount(untaxedPrice ? untaxedPrice * itemQuantity / priceTimes : 0, 2)
              const untaxedTotalAmount = formatAmount(((itemQuantity || 0) * taxedPrice / (1 + taxRate * 0.01)), 2)
              const taxTotalAmount = formatAmount(taxedTotalAmount - untaxedTotalAmount, 2)
              const newPlanList = planList.filter(item => item.deliveryDate && item.deliveryQuantity).sort((a, b) => {
                return (new Date(a.deliveryDate)).getTime() - (new Date(b.deliveryDate)).getTime()
              })
              const itemDeliveryDate = newPlanList[0] ? moment(newPlanList[0].deliveryDate || Date.now()).format('YYYY-MM-DD') : ''
              newData = {
                ...newData,
                taxedTotalAmount,
                untaxedTotalAmount,
                taxTotalAmount,
                planList: newPlanList,
                itemQuantity,
                itemDeliveryDate
              }
              console.log(newData);
              await calcUnTaxedPrice(newData, this.dictList).then(data => {
                    if (data) newData = merge(newData, rowEditChange(data))
                  })
              // const needDelay = this.reCalcCompQuantity(this.purchaseData.itemList[idx], newData)
              // if (needDelay) {
              //   setTimeout(() => {
              //     this.$set(this.purchaseData.itemList, idx, newData)
              //   }, 200)
              // } else {
              // }
            }
          }
          this.$set(this.purchaseData.itemList, idx, newData)
          this.$nextTick(() => {
            this.handleResetAssign()
          })
        }
      }
    },
    resetDefaultAddress (idx = 0) {
      console.log('删除/恢复商品行重置通讯信息默认值')
      const { supplierNo } = this.purchaseData
      const { purchaseGroup } = this.purchaseData
      const { factoryCode, warehouseLocation, soNo, skuNo } = this.purchaseData.itemList[idx];
      this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo, skuNo)
    },
    batchConfirm () {
      if (!this.records.length) return;
      this.records.forEach(record => {
        if (record.deliveryStatus !== 1) {
          record.isConfirmed = 1
        }
      })
      this.setTitleCheckbox('isConfirmed')
      this.setTitleCheckbox('isUrgent')
    },
    setRowChargeFields (rowItem, prop) {
      rowItem[`${prop}Amount`] = 0
      rowItem[`${prop}Currency`] = ''
      rowItem[`${prop}SupplierNo`] = ''
    },
    getAttachmentList(data) {
      this.deleteReason = data
      this.attachmentList.push.apply(this.attachmentList, this.deleteReason.attachmentList)
      this.deleteOrRecover(true)
    },
    async deleteOrRecover (status) {
      const prevItem = this.getItemSnapshot()
      // 删除手动添加行
      const handList = this.records.filter(record => record.userAdded)
      this.purchaseData.itemList = this.purchaseData.itemList.filter(item => !handList.includes(item))
      await assignToggle(this.purchaseData, this.records, status).then(data => {
        if (data?.data) {
          const { itemList, ...others } = data.data
          this.purchaseData = merge(this.purchaseData, others)
        }
        console.log('shanchuhuifu', this.purchaseData);
      })
      // 根据状态删除/恢复其余商品行
      this.purchaseData.itemList.forEach((item, index) => {
        if (this.records.some(record => record === item)) {
          item.isDeleted = status ? 1 : 0
          item.editChange = true
          item.deleteReason = status ? this.deleteReason.deleteReason : ''
        }
      })
      // 判断第一个有效商品行，并查询默认信息
      const noValidLine = this.compareWithPrevItem(prevItem)
      if (noValidLine) {
        // 清空所有通讯信息
        // this.resetCommunication()
        setTimeout(() => {
          this.$refs['orderForm'].clearValidate()
        }, 100)
      }
      // 清除复选框以及records
      safeRun(() => {
        this.records = []
        this.$refs.itemTable.$refs.detailTable.clearCheckboxRow()
      })
      this.setTitleCheckbox('isConfirmed')
      this.setTitleCheckbox('isUrgent')
    },
    handleItemToggle () {
      if (!this.records.length) return
      const notLocal = this.records.filter(record => !record.userAdded)
      if (notLocal.some(item => item.isDeleted === 0) && notLocal.some(item => item.isDeleted === 1)) {
        return this.$message.error('不能同时勾选已删除和未删除的商品行进行操作！')
      }
      const status = notLocal[0] && notLocal[0].isDeleted === 0
      const statusType = status ? '删除' : '恢复'
      let content = `此操作将删除手动添加行，${statusType}其余商品行, 是否继续?`
      if (this.records.every(item => item.userAdded)) {
        content = '此操作将删除所选商品行, 是否继续?'
      }
      if (this.records.every(item => !item.userAdded)) {
        content = `此操作将${statusType}所选商品行, 是否继续?`
      }
      if (status) {
        this.showDeleteDialog = true
        return
      }
      const assignMsg = status ? getAssignInfo(this.records, this.purchaseData.orderType) : ''
      this.$confirm(`${assignMsg}${content}`, '操作提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.deleteOrRecover(status)
      })
    },
    resetContactUnnecessary () {
      const { orderType } = this.purchaseData
      if (orderType === 'Z001') {
        // Z001 清空除收票之外的信息
        this.setAddress('supplier')
        this.purchaseData.supplierAddress = ''
        this.purchaseData.supplierContactName = ''
        this.purchaseData.supplierContactPhone = ''
        this.setAddress('receive')
        this.purchaseData.receiveAddress = ''
        this.purchaseData.receiveContactName = ''
        this.purchaseData.receiveContactPhone = ''
      }
    },
    handleResetAssign () {
      const { itemList } = this.purchaseData
      if (itemList && itemList.length > 0) {
        const chargeProps = ['tariff', 'customsFee', 'intlShipping', 'saleTax', 'latePayment', 'other', 'premium']
        const sum = {}
        itemList.forEach(item => {
          chargeProps.forEach(prop => {
            const amount = prop + 'Amount'
            const currency = prop + 'Currency'
            const type = prop + 'Type'
            if (!sum[prop]) sum[prop] = {}
            if (item[amount] != null) {
              sum[prop][amount] = (sum[prop][amount] || 0) + item[amount]
              if (item[currency]) {
                sum[prop][currency] = item[currency]
              }
              if (item[type] && this.purchaseData.orderType === 'Z002' && (prop === 'customsFee' || prop === 'intlShipping')) {
                sum[prop][type] = item[type]
              }
            }
          })
        })
        safeRun(() => { window._console.red('handleResetAssign sum:', sum) })

        Object.keys(sum).forEach(key => {
          const amount = key + 'Amount'
          const currency = key + 'Currency'
          const type = key + 'Type'
          this.purchaseData[amount] = sum[key][amount]
          this.purchaseData[currency] = sum[key][currency]
          if ((key === 'customsFee' || key === 'intlShipping') && this.purchaseData.orderType === 'Z002') {
            this.purchaseData[type] = this.purchaseData[type] || sum[key][type]
          }
        })
        this.$forceUpdate()
      }
    },
    handleOneAssign () {
      try {
        // console.log(this.purchaseData);
        // assign(this.purchaseData, this.dictList)
        console.log('before assign', this.purchaseData)
        if (this.purchaseData.sundry) {
          if (isEmpty(this.purchaseData.sundryReason)) {
            this.$message.error('杂费原因必填')
            return
          } else if (this.purchaseData.sundryReason.includes('09') && !this.purchaseData.sundryReasonDetail) {
            this.$message.error('杂费原因包含其他时，杂费原因备注必填')
            return
          }
        }
        const loading = this.startLoading()
        // const params = {...this.purchaseData, itemList: this.purchaseData.itemList.filter(item => !!item.skuNo)}
        assign(this.purchaseData).then((data) => {
          console.log('assignByInterface', merge(this.purchaseData, data));
          if (data) {
            this.purchaseData = merge(this.purchaseData, data)
            this.purchaseData.itemList = this.purchaseData.itemList.map(item => {
              console.log(item.itemNo)
              const updatedShareRebatePOItemList = data.itemList.find(i => i.itemNo === item.itemNo)?.shareRebatePOItemList || []
              return {
                ...item,
                editChange: true,
                shareRebatePOItemList: updatedShareRebatePOItemList
              }
            })
          }

          // console.log('merged purchasedata', this.purchaseData);
        }).finally(() => {
          this.endLoading(loading)
        })
      } catch (e) {
        this.$message.error(e.message)
      }
    },
    handleAttachmentList(list) {
      this.purchaseData.initialShippingAmountAttachmentList = list.map(item => {
        return {
          fileName: item.name,
          fileUrl: item.url,
          attachmentType: 3,
          poNo: this.purchaseData.orderNo
        }
      })
    },
    handleChangeDeliveryDate () {
      const index = this.purchaseData.itemList.findIndex(item => item === this.itemDetail)
      if (index > -1) {
        this.itemDetail.isConfirmed = 1
        this.setTitleCheckbox('isConfirmed')
        this.$set(this.purchaseData.itemList, index, this.itemDetail)
      }
    },
    handleShowDetail (val, flag) {
      val.customInstructionList = []
      safeRun(() => {
        val.customInstructionList = (JSON.parse(val?.customInstructions) || []).map((item, index) => {
          return {
            ...item,
            key: 'customProperty' + index
          }
        });
      })
      val.customInstructionData = {};
      (val.customInstructionList || []).map((item) => {
        val.customInstructionData[item.key] = item.customPropertyRemark
      })
      this.itemDetail = val
      setTimeout(() => {
        this.showDetail = true
      }, 500)
    },
    setRowDeliveryDate (row, standardLeadTime, deliveryDate) {
      // const { createTime } = this.purchaseData
      const { planList } = row
      // const date = moment(createTime).add(data, 'days').format('YYYY-MM-DD')
      row.itemDeliveryDate = deliveryDate
      row.standardLeadTime = standardLeadTime
      if (Array.isArray(planList) && planList.length) {
        planList.forEach(plan => {
          plan.deliveryDate = deliveryDate
        })
      }
    },
    async updateTransferTime (factoryCode, warehouseLocation, shipWarehouseLocation, row) {
      const { skuNo } = row
      const params = { factoryCode, warehouseLocation, shipWarehouseLocation, skuNo }
      this.pageLoading = true
      return getTransferTime(params)
        .then(res => {
          const { code, data, msg } = res
          if (code === 0 && data) {
            const { f0: standardLeadTime, f1: deliveryDate } = data
            this.setRowDeliveryDate(row, standardLeadTime, deliveryDate)
          } else {
            submitErrorHandler('', data, msg)
          }
        })
        .catch(err => {
          console.log(err)
        })
        .finally(() => {
          this.pageLoading = false
        })
    },
    updateItemDeliveryDate (row) {
      const index = this.purchaseData.itemList.findIndex(item => item === row)
      if (index > -1) {
        this.setTitleCheckbox('isConfirmed')
        this.$set(this.purchaseData.itemList, index, row)
      }
    },
    updateFactory (factoryCode, warehouseLocation, soNo, skuNo) {
      if (factoryCode) {
        const { supplierNo } = this.purchaseData.supplier
        const { purchaseGroup } = this.purchaseData
        if (supplierNo) {
          this.queryAddressList(supplierNo, factoryCode, warehouseLocation)
          this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo, skuNo)
        }
      }
    },
    async getPriceData (factoryCode, supplierId, skuNo, orderType, row, userAdded, queryType = 0) {
      let { warehouseLocation, soNo, soItemNo, itemQuantity, discountAmount, inventoryUnitDeno = 1, inventoryUnitMole = 1, priceTimes, shippingAmount, taxedPrice, sundryAmount, rebateAmount } = row
      const { receiveProvince, receiveCity, supplier: { supplierNo } } = this.purchaseData
      const body = {
        factoryCode,
        supplierId,
        supplierNo,
        skuNos: [skuNo],
        skuQueryList: [
          { skuNo, warehouseLocation, soNo, soItemNo, itemQuantity, discountAmount, inventoryUnitDeno, inventoryUnitMole, priceTimes, shippingAmount, taxedPrice: userAdded ? '' : taxedPrice, sundryAmount, rebateAmount }
        ],
        orderType,
        receiveProvince,
        receiveCity,
        queryType
      }
      this.pageLoading = true
      const response = await getProductPrice(body)
      if (response && response.code === 0 && Array.isArray(response.data)) {
        const { deliveryDays, taxedPriceTimes = 0, taxedPrice = 0, priceTimes: responsePriceTimes } = response.data[0] || {}
        if (!userAdded && [2, 0].includes(queryType)) {
          const date = moment().add(deliveryDays, 'days').format('YYYY-MM-DD')
          row.standardLeadTime = deliveryDays
          row.itemDeliveryDate = date
          if (row.planList && row.planList.length === 1) {
            row.planList[0].deliveryDate = date
          }
          // 修改仓库时交期会变化，更新默认的交期为修改后的
          row.__originalDeliveryDate = date
        }
        if (this.purchaseData.orderType !== 'Z004' && (!row.deliveryStatus || row.deliveryStatus === 0) && row.isFree !== 1 && [1, 0].includes(queryType)) {
          row.taxedPrice = taxedPriceTimes
          row.taxedPriceOrigin = taxedPrice
          row.priceTimes = responsePriceTimes
          console.log('priceTimes', responsePriceTimes)
          row.taxedTotalAmount = formatAmount(taxedPrice * row.itemQuantity / responsePriceTimes, 2) || 0
          row.untaxedTotalAmount = formatAmount(taxedPrice / (1 + row.taxRate * 0.01) * row.itemQuantity / responsePriceTimes, 2) || 0
          row.untaxedPrice = formatAmount(row.untaxedTotalAmount / row.itemQuantity * responsePriceTimes, 2) || 0
          await calcUnTaxedPrice(row, this.dictList).then(data => {
            if (data) row = merge(row, rowEditChange(data))
            console.log(row, data);
          })
        }
      }
      this.pageLoading = false
    },
    updateWarehouseLocation (factoryCode, warehouseLocation, soNo, skuNo) {
      // 1201仓有2个地址，其中一个只有特殊采购组别的时候才展示出来 https://wiki.zkh360.com/confluence/pages/viewpage.action?pageId=*********
      if (this.purchaseData.orderType === 'Z001' && warehouseLocation === '1201') {
        const { supplierNo } = this.purchaseData.supplier || {}
        this.queryAddressList(supplierNo, factoryCode, warehouseLocation)
      }
      this.handleUpdateDefaultAddress(factoryCode, warehouseLocation, soNo, skuNo)
    },
    updateDeliveryDaysByWarehouseLocation (row, userAdded, queryType = 0) {
      // if (!row.userAdded) return
      const { factoryCode, skuNo } = row
      const { orderType, supplierId } = this.purchaseData
      if (orderType !== 'Z003') {
        this.getPriceData(factoryCode, supplierId, skuNo, orderType, row, userAdded, queryType)
      }
    },
    handleUpdateDefaultAddress (factoryCode, warehouseLocation, soNo, skuNo, callback) {
      if (factoryCode) {
        const { supplierNo } = this.purchaseData.supplier
        const { purchaseGroup } = this.purchaseData
        if (supplierNo) {
          this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, soNo, skuNo).then(() => {
            if (callback) callback()
          })
        }
      }
    },
    async fetchItemListInfo (skuNos, providerId, projectCategory = '', row = {}) {
      const { orderType } = this.purchaseData
      const factoryCode = row.factoryCode || this.factoryCode
      let { itemQuantity } = row
      const { receiveProvince, receiveCity, supplier: { supplierNo } } = this.purchaseData
      const productList = await getProduct({
        factoryCode,
        skuNos,
        supplierNo
      })
      if (!productList) return []
      let priceList = []
      if (orderType !== 'Z003') {
        const body = {
          factoryCode,
          supplierId: providerId,
          skuNos,
          supplierNo,
          skuQueryList: skuNos.map(sku => {
            const skuInfoList = skuNos.skuInfoList
            const loc0 = (skuInfoList && skuInfoList.find(item => item.skuNo === sku)) || {}
            const loc1 = (productList && productList.find(item => item.skuNo === sku)) || {}
            itemQuantity = itemQuantity || loc0.quantity || 0
            return {
              skuNo: sku,
              warehouseLocation: loc0.warehouseLocation || loc1.warehouseLocationCode,
              itemQuantity
            }
          }),
          orderType,
          receiveProvince,
          receiveCity,
          queryType: 0
        }
        if (orderType === 'Z004') {
          body.projectCategory = projectCategory
        }
        const response = await getProductPrice(body)
        if (response.code === 0 && response.data) {
          priceList = response.data
        }
      }
      return [productList, priceList]
    },
    async addDom (row, priceData = {}) {
      let { inventoryUnitMole = 1, inventoryUnitDeno = 1 } = priceData || {}
      if (!inventoryUnitMole) inventoryUnitMole = 1
      if (!inventoryUnitDeno) inventoryUnitDeno = 1
      let { factoryCode, skuNo, itemQuantity } = row
      const newComponentList = []
      // 都请求bom
      if (factoryCode && skuNo) {
        const { factoryCode, skuNo, warehouseLocation: rowWarehouseLocation } = row
        let { lossRatio } = this.purchaseData
        const bomData = await getBom({
          factoryCode,
          skuNo
        })
        if (bomData && bomData.bomItemList && bomData.bomItemList.length > 0) {
          const categoryList = {}
          bomData.bomItemList.forEach(bom => {
            if (bom && bom.warehouseLocation) {
              const { skuNo, warehouseLocation } = bom
              if (!categoryList[warehouseLocation]) {
                categoryList[warehouseLocation] = []
              }
              categoryList[warehouseLocation].push(skuNo)
            }
          })
          let inventoryList = []
          if (Object.keys(categoryList).length > 0) {
            inventoryList = await Promise.all(Object.keys(categoryList).map(async (warehouseLocation) => {
              const skuNos = categoryList[warehouseLocation].join(',')
              if (skuNos) {
                return getInventory({
                  factoryCode,
                  skuNos,
                  warehouseLocation
                })
              }
              return Promise.resolve([])
            }))
          }
          bomData.bomItemList.forEach(bom => {
            const { skuNo, materialDescribe, materialGroupNum, warehouseLocation, quantity, uomIdName } = bom
            let componentRequiredQuantity = quantity
            // 组件数量计算
            if (!lossRatio) lossRatio = 0
            if (!itemQuantity) itemQuantity = 0
            componentRequiredQuantity = Number((itemQuantity * inventoryUnitMole / inventoryUnitDeno * (quantity / bomData.quantity) * (1 + lossRatio * 0.01)).toFixed(3))
            if (materialGroupNum === 430) {
              componentRequiredQuantity = Math.ceil(componentRequiredQuantity)
            }
            let configWarehouse = getDefaultWarehouseLocation(this.defaultWarehouseConfigList, this.purchaseData.purchaseGroup, factoryCode)
            let numCalScale = inventoryUnitMole / inventoryUnitDeno * (quantity / bomData.quantity) * (1 + lossRatio * 0.01)
            const component = {
              materialGroupNum,
              bomQuantity: bomData.quantity,
              _quantity: materialGroupNum === 430 ? Math.ceil(quantity) : quantity,
              componentMaterialDescription: materialDescribe,
              componentSkuNo: skuNo,
              componentRequiredQuantity,
              componentInventoryUnit: uomIdName,
              componentInventoryUnitName: uomIdName,
              componentWarehouseLocation: configWarehouse || warehouseLocation,
              numCalScale,
              _configWarehouse: configWarehouse
            }
            if (!component.componentWarehouseLocation) {
              component.componentWarehouseLocation = rowWarehouseLocation
            }
            component.componentWarehouseLocation = getD02ComponentWarehouseLocation(this.purchaseData.orderType, this.purchaseData.purchaseGroup, this.KHWarehouseList, rowWarehouseLocation, component.componentWarehouseLocation, this.dictList['componentWarehouse'])
            if (bom && bom.warehouseLocation) {
              const inv = inventoryList.find(inventory => inventory.sku === skuNo && inventory.position === warehouseLocation)
              if (inv && inv.availableQty) {
                component.availableQty = inv.availableQty
              }
            }
            newComponentList.push(component)
          })
        }
      }
      return newComponentList
    },
    addCateRetQuantity () {
      const { orderType } = this.purchaseData
      let projectCategory = ''
      let isReturn = 0
      let receivedQuantity = 0
      if (orderType === 'Z004') {
        isReturn = 1
      }
      if (orderType === 'Z003') {
        projectCategory = 'U'
      }
      if (orderType === 'Z004') {
        projectCategory = ''
      }
      if (orderType === 'Z005') {
        projectCategory = 'K'
      }
      if (orderType === 'Z006') {
        projectCategory = 'L'
      }
      if (orderType === 'Z010') {
        projectCategory = 'S'
      }
      return {
        projectCategory,
        isReturn,
        receivedQuantity
      }
    },
    addItem (sku, row) {
      let { supplierId, itemList, orderType, isDeliveryTimeConfirm, isUrgent } = this.purchaseData
      const oldProjectCategory = row.projectCategory
      // isOverchargeFree 如果为 1 新增行
      if (sku && (supplierId != null)) {
        const loading = this.startLoading()
        const prevItem = this.getItemSnapshot()
        this.fetchItemListInfo([sku], supplierId, oldProjectCategory, row).then(async ([productList, priceList = []]) => {
          loading.close()
          // 去除没有价格信息不让添加新行的卡控
          if (productList && productList.length > 0) {
            delete row.isEmptyLine
            const productData = productList[0]
            const priceData = priceList[0] || { noMaintainPrice: true }
            const idx = itemList.findIndex(item => item === row)
            const len = itemList.length
            // if (supplierNo && idx === 0) {
            //   this.queryDefaultAddress(supplierNo, purchaseGroup, this.factoryCode, productData.warehouseLocationCode)
            // }

            if (orderType === 'Z004' && (!priceList.length || !priceData.taxedPrice)) {
              this.$message.warning(`商品[${sku}]未查询到该供应渠道有效采购价，请核实！`)
            }
            // Z006,需要额外获取委外信息
            let componentList
            if (orderType === 'Z006') {
              row.warehouseLocation = productData.warehouseLocationCode
              componentList = await this.addDom(row, priceData)
            }
            const { deliveryDays = 0, deliveryDate, taxedPrice = 0, taxedPriceTimes = 0, discountAmount, priceTimes = 1, unitCode } = priceData
            if (discountAmount) {
              delete priceData.discountAmount
            }
            const { materialGroup, materialGroupNum, materialGroupId, warehouseLocationCode = '' } = productData
            if (idx > -1) {
              const date = priceData ? deliveryDate : moment().add(deliveryDays, 'days').format('YYYY-MM-DD')
              let { projectCategory, isReturn, receivedQuantity } = this.addCateRetQuantity()
              if (oldProjectCategory) projectCategory = oldProjectCategory
              let finalWarehouseLocation = warehouseLocationCode
              if (this.isFixedAssets(orderType, projectCategory)) {
                finalWarehouseLocation = null
              }
              const { factoryCode } = itemList[idx]
              let params = {
                soNo: '',
                soItemNo: '',
                supplierMaterialNo: '',
                trackNo: '',
                ...itemList[idx],
                ...productData,
                ...priceData,
                factoryCode,
                priceTimes,
                taxedPriceOrigin: taxedPrice,
                taxedPrice: taxedPriceTimes,
                // untaxedPrice: Number((taxedPriceTimes / (1 + taxRate * 0.01)).toFixed(2)),
                untaxedPrice: '',
                standardLeadTime: deliveryDays,
                materialGroupName: materialGroup,
                materialGroupNum: materialGroupNum,
                materialGroupId: materialGroupId,
                warehouseLocation: finalWarehouseLocation,
                itemDeliveryDate: date,
                userAdded: 1,
                unit: unitCode,
                unitName: this.findUnitName(unitCode),
                priceUnit: unitCode,
                inventoryUnit: productData.unitCode,
                planList: [{
                  deliveryQuantity: itemList[idx].itemQuantity || 0,
                  deliveryDate: date,
                  planNo: '0001'
                }],
                projectCategory,
                isReturn,
                receivedQuantity
              }
              await calcUnTaxedPrice(params, this.dictList).then(data => {
                if (data) params = merge(params, rowEditChange(data))
              })
              if (['Z007', 'Z004'].includes(orderType)) {
                if (!params.fixedAssetsList || params.fixedAssetsList.length === 0) {
                  params.fixedAssetsList = [{
                    fixedAssetsNo: null
                  }]
                }
              }
              if (orderType === 'Z004') {
                params.trackNo = null
                params.batchNo = null
                params.refundableAmount = null
              }
              if (componentList && componentList.length > 0) {
                params.componentList = componentList
              }
              // if (orderType === 'Z010') {
              //   delete params.warehouseLocation
              // }
              if (orderType === 'Z003') {
                params.shipWarehouseLocation = warehouseLocationCode
                // 对于Z003订单，单位去主数据sku接口 unitCode
                params.unit = productData.unitCode
              }
              if (params.taxedTotalAmount === undefined) {
                params.taxedTotalAmount = ''
              }
              if (params.untaxedTotalAmount === undefined) {
                params.untaxedTotalAmount = ''
              }
              if (params.taxTotalAmount === undefined) {
                params.taxTotalAmount = ''
              }
              // if (isOverchargeFree === 1) {
              //   params.isFree = 1
              //   params.taxedPrice = 0
              //   params.untaxedPrice = ''
              //   params.taxedTotalAmount = ''
              //   params.untaxedTotalAmount = ''
              //   params.taxTotalAmount = ''
              // }
              if (isDeliveryTimeConfirm === 1) {
                params.isConfirmed = 1
              }
              if (isUrgent === 1) {
                params.isUrgent = 1
              }
              if (orderType === 'Z003') {
                const { factoryCode, warehouseLocation, shipWarehouseLocation } = params
                await this.updateTransferTime(factoryCode, warehouseLocation, shipWarehouseLocation, params)
              }
              this.$set(this.purchaseData.itemList, idx, params)
              if (len === idx + 1) {
                this.addItemEmptyLine()
              }
            }
            this.compareWithPrevItem(prevItem)
          }
          this.setTitleCheckbox('isConfirmed')
          this.setTitleCheckbox('isUrgent')
        })
      } else if (this.isFixedAssets(orderType, oldProjectCategory)) {
        const idx = itemList.findIndex(item => item === row)
        const len = itemList.length
        delete row.isEmptyLine
        // this.$set(this.purchaseData.itemList, idx, row)
        if (len === idx + 1) {
          this.addItemEmptyLine()
        }
      }
    },
    findUnitName (value) {
      const unitList = this.dictList['orderUnit']
      if (Array.isArray(unitList) && unitList.length) {
        const item = unitList.find(item => item.value === value)
        if (item && item.name) {
          value = item.name
        }
      }
      return value
    },
    addItemList (skuInfoList) {
      const total = skuInfoList.length
      if (!this.purchaseData.supplier) return this.$message.error('请先选择供应商！')
      const { supplier: { supplierId }, orderType, isDeliveryTimeConfirm, isUrgent } = this.purchaseData
      if (total && supplierId) {
        const skuNos = uniq(skuInfoList.map(item => item.skuNo))
        const loading = this.startLoading()
        const prevItem = this.getItemSnapshot()
        skuNos.skuInfoList = skuInfoList
        this.fetchItemListInfo(skuNos, supplierId).then(async ([productList, priceList]) => {
          if (!productList) return
          const skuInfo = {}
          const priceInfo = {}
          if (productList && productList.length > 0) {
            productList.forEach(product => {
              if (!skuInfo[product.skuNo]) {
                skuInfo[product.skuNo] = product
              }
            })
            priceList.forEach(price => {
              if (!priceInfo[price.skuNo]) {
                priceInfo[price.skuNo] = price
              }
            })
          }
          this.purchaseData.itemList.pop()
          // let needQueryAddress = false
          // if (!this.purchaseData.itemList.length) {
          //   needQueryAddress = true
          // }
          const filterList = skuInfoList.filter(filterInProp(productList, 'skuNo'))
          for (let index = 0; index < filterList.length; index++) {
            let item = filterList[index]
            const {
              skuNo, quantity, warehouseLocation, shipWarehouseLocation, returnReason, trackNo, fixedAssetsCardNo, generalLedgerAccount, costCenter
            } = item
            if (skuNo) {
              let itemData = {}
              if (quantity) {
                itemData.itemQuantity = quantity
              }
              if (item.inSystemMaterial) {
                itemData.inSystemMaterial = item.inSystemMaterial
              }
              if (fixedAssetsCardNo) {
                itemData.fixedAssetsCardNo = fixedAssetsCardNo
              }
              if (generalLedgerAccount && this.inGeneralLedger(generalLedgerAccount)) {
                itemData.generalLedgerAccount = generalLedgerAccount
              }
              if (costCenter && this.inCostCenter(costCenter)) {
                itemData.costCenter = costCenter
              }
              if (shipWarehouseLocation && this.inWarehouse(shipWarehouseLocation)) {
                itemData.shipWarehouseLocation = shipWarehouseLocation
              }
              if (returnReason) {
                itemData.returnReason = returnReason
              }
              if (trackNo) {
                itemData.trackNo = trackNo
              }
              if (skuInfo[skuNo]) {
                const { materialGroup, warehouseLocationCode = '' } = skuInfo[skuNo]
                let finalWarehouseLocation = warehouseLocation || warehouseLocationCode
                if (!this.inWarehouse(finalWarehouseLocation)) {
                  finalWarehouseLocation = ''
                }
                // if (index === 0 && finalWarehouseLocation) {
                //   const { supplierNo } = this.purchaseData.supplier
                //   const { purchaseGroup } = this.purchaseData
                //   this.queryDefaultAddress(supplierNo, purchaseGroup, this.factoryCode, finalWarehouseLocation)
                // }
                const { unitCode, deliveryDate, deliveryDays = 0 } = priceInfo[skuNo] || {}
                const date = priceInfo[skuNo] ? deliveryDate : moment().add(deliveryDays, 'days').format('YYYY-MM-DD')
                const { projectCategory, isReturn, receivedQuantity } = this.addCateRetQuantity()
                itemData = {
                  soNo: '',
                  soItemNo: '',
                  supplierMaterialNo: '',
                  trackNo: '',
                  uuid: shortid.generate(),
                  ...itemData,
                  ...skuInfo[skuNo],
                  ...priceInfo[skuNo],
                  unit: unitCode,
                  unitName: this.findUnitName(unitCode),
                  priceUnit: unitCode,
                  inventoryUnit: unitCode,
                  itemNo: this.getItemNo(),
                  warehouseLocation: finalWarehouseLocation,
                  taxedPriceOrigin: '',
                  untaxedPrice: '',
                  taxedTotalAmount: '',
                  untaxedTotalAmount: '',
                  standardLeadTime: '',
                  noMaintainPrice: true,
                  materialGroupName: materialGroup,
                  itemDeliveryDate: date,
                  factoryCode: this.factoryCode,
                  userAdded: 1,
                  planList: [{
                    deliveryQuantity: quantity || 0,
                    planNo: '0001',
                    deliveryDate: date
                  }],
                  projectCategory,
                  isReturn,
                  receivedQuantity
                }
                let componentList
                if (orderType === 'Z006') {
                  // itemData.warehouseLocation = priceInfo[skuNo].warehouseLocationCode
                  componentList = await this.addDom(itemData, priceInfo[skuNo])
                }
                if (componentList && componentList.length) {
                  itemData.componentList = componentList
                }
                if (['Z007', 'Z004'].includes(orderType)) {
                  if (!itemData.fixedAssetsList || itemData.fixedAssetsList.length === 0) {
                    itemData.fixedAssetsList = [{
                      fixedAssetsNo: null,
                      fixedAssetsCardQuantity: itemData.itemQuantity,
                      fixedAssetsCardNo: itemData.fixedAssetsCardNo
                    }]
                  }
                }
                if (priceInfo[skuNo]) {
                  const { deliveryDays = 0, deliveryDate, taxedPrice = 0, taxedPriceTimes = 0, taxRate = 0, discountAmount } = priceInfo[skuNo]
                  const date = priceInfo[skuNo] ? deliveryDate : moment().add(deliveryDays, 'days').format('YYYY-MM-DD')
                  if (discountAmount) {
                    delete priceInfo[skuNo].discountAmount
                  }
                  itemData.taxedPrice = taxedPriceTimes
                  itemData.taxedPriceOrigin = taxedPrice
                  // itemData.untaxedPrice = formatAmount(taxedPriceTimes / (1 + taxRate * 0.01), 2)
                  itemData.taxedTotalAmount = formatAmount(taxedPrice * (quantity || 0), 2)
                  itemData.untaxedTotalAmount = formatAmount((quantity || 0) * taxedPrice / (1 + taxRate * 0.01), 2)
                  itemData.taxTotalAmount = formatAmount(itemData.taxedTotalAmount - itemData.untaxedTotalAmount, 2)
                  itemData.standardLeadTime = deliveryDays
                  itemData.itemDeliveryDate = date
                  itemData.planList[0].deliveryDate = date
                  await calcUnTaxedPrice(itemData, this.dictList).then(data => {
                    if (data) itemData = merge(itemData, rowEditChange(data))
                  })
                  delete itemData.noMaintainPrice
                }
              }
              // if (isOverchargeFree === 1) {
              //   itemData.isFree = 1
              //   itemData.taxedPrice = 0
              //   itemData.untaxedPrice = ''
              //   itemData.taxedTotalAmount = ''
              //   itemData.untaxedTotalAmount = ''
              //   itemData.taxTotalAmount = ''
              // }
              if (isDeliveryTimeConfirm === 1) {
                itemData.isConfirmed = 1
              }
              if (isUrgent === 1) {
                itemData.isUrgent = 1
              }
              if (orderType === 'Z003') {
                // 对于Z003订单，单位去主数据sku接口 unitCode
                itemData.unit = skuInfo[skuNo].unitCode
                const { factoryCode, warehouseLocation, shipWarehouseLocation } = itemData
                await this.updateTransferTime(factoryCode, warehouseLocation, shipWarehouseLocation, itemData)
              }
              this.purchaseData.itemList.push(itemData)
              if (index === 0 && itemData.warehouseLocation === '1201' && this.purchaseData.orderType === 'Z001') {
                this.queryAddressList(this.purchaseData.supplierNo, this.factoryCode, '1201')
              }
              // if (needQueryAddress && (index === skuInfoList.length - 1)) {
              //   const index = this.getFirstLineUpdateDefaultAddress()
              //   if (index !== null) {
              //     this.resetDefaultAddress(index)
              //   }
              // }
            }
          }
          this.compareWithPrevItem(prevItem)
          this.addItemEmptyLine()
        }).finally(() => {
          loading.close()
          this.setTitleCheckbox('isConfirmed')
          this.setTitleCheckbox('isUrgent')
        })
      }
    },
    handleImport (uploadSkuInfoVOList) {
      this.showBatchImport = false
      this.addItemList(uploadSkuInfoVOList)
    },
    handlePunchImport (uploadSkuInfoVOList) {
      this.showPunchImport = false
      this.addFestoItemList(uploadSkuInfoVOList)
    },
    addFestoItemList (skuInfoList) {
      this.submitFestoList = skuInfoList
      const total = skuInfoList.length
      // festo 导入需要覆盖PMS订单创建的字段
      const coverFields = skuInfoList.map(item => ({
        punchId: item.id,
        quantity: item.supplierOrderAmount,
        itemDeliveryDate: item.leadDate,
        // taxedPrice: item.zkhTaxPrice,
        // taxRate: item.zkhTaxRate,
        deliveryDays: item.leadTime
        // unitName: item.zkhUnit
      }))
      if (!this.purchaseData.supplier) return this.$message.error('请先选择供应商！')
      const { supplier: { supplierId }, orderType, isUrgent } = this.purchaseData
      if (total && supplierId) {
        const skuNos = uniq(skuInfoList.map(item => item.skuNo))
        const loading = this.startLoading()
        this.fetchItemListInfo(skuNos, supplierId).then(async ([productList, priceList]) => {
          if (!productList) return
          const skuInfo = {}
          const priceInfo = {}
          if (productList && productList.length > 0) {
            productList.forEach(product => {
              if (!skuInfo[product.skuNo]) {
                skuInfo[product.skuNo] = product
              }
            })
            priceList.forEach(price => {
              if (!priceInfo[price.skuNo]) {
                priceInfo[price.skuNo] = price
              }
            })
          }
          this.purchaseData.itemList.pop()
          let needQueryAddress = false
          if (!this.purchaseData.itemList.length) {
            needQueryAddress = true
          }
          const filterList = skuInfoList.filter(filterInProp(productList, 'skuNo'))
          for (let index = 0; index < filterList.length; index++) {
            let item = filterList[index]
            let coverItem = coverFields[index]
            let {
              skuNo, warehouseLocation, shipWarehouseLocation, returnReason, trackNo, fixedAssetsCardNo, generalLedgerAccount, costCenter
            } = item
            let { quantity } = coverItem
            if (skuNo) {
              let itemData = {
                ...skuInfo[skuNo],
                itemNo: this.getItemNo()
              }
              if (quantity) {
                itemData.itemQuantity = quantity
              }
              if (item.inSystemMaterial) {
                itemData.inSystemMaterial = item.inSystemMaterial
              }
              if (fixedAssetsCardNo) {
                itemData.fixedAssetsCardNo = fixedAssetsCardNo
              }
              if (generalLedgerAccount && this.inGeneralLedger(generalLedgerAccount)) {
                itemData.generalLedgerAccount = generalLedgerAccount
              }
              if (costCenter && this.inCostCenter(costCenter)) {
                itemData.costCenter = costCenter
              }
              if (shipWarehouseLocation && this.inWarehouse(shipWarehouseLocation)) {
                itemData.shipWarehouseLocation = shipWarehouseLocation
              }
              if (returnReason) {
                itemData.returnReason = returnReason
              }
              if (trackNo) {
                itemData.trackNo = trackNo
              }
              if (skuInfo[skuNo]) {
                const { materialGroup, warehouseLocationCode = '' } = skuInfo[skuNo]
                let { itemDeliveryDate } = coverItem
                let finalWarehouseLocation = warehouseLocation || warehouseLocationCode
                if (!this.inWarehouse(finalWarehouseLocation)) {
                  finalWarehouseLocation = ''
                }
                const { createTime } = this.purchaseData
                const deliveryDate = itemDeliveryDate || moment(createTime).format('YYYY-MM-DD')
                const { unitCode } = priceInfo[skuNo] || {}
                const { projectCategory, isReturn, receivedQuantity } = this.addCateRetQuantity()
                itemData = {
                  soNo: '',
                  soItemNo: '',
                  supplierMaterialNo: '',
                  trackNo: '',
                  uuid: shortid.generate(),
                  ...itemData,
                  ...skuInfo[skuNo],
                  ...priceInfo[skuNo],
                  unit: unitCode,
                  inventoryUnit: skuInfo[skuNo].unitCode,
                  unitName: this.findUnitName(unitCode),
                  itemNo: this.getItemNo(),
                  warehouseLocation: finalWarehouseLocation,
                  taxedPriceOrigin: '',
                  untaxedPrice: '',
                  taxedTotalAmount: '',
                  untaxedTotalAmount: '',
                  standardLeadTime: '',
                  noMaintainPrice: true,
                  materialGroupName: materialGroup,
                  itemDeliveryDate: deliveryDate,
                  factoryCode: this.factoryCode,
                  planList: [{
                    deliveryQuantity: quantity || 0,
                    planNo: '0001',
                    deliveryDate
                  }],
                  projectCategory,
                  isReturn,
                  receivedQuantity,
                  ...coverItem // festo属性覆盖
                }
                let componentList
                if (orderType === 'Z006') {
                  // itemData.warehouseLocation = priceInfo[skuNo].warehouseLocationCode
                  componentList = await this.addDom(itemData, priceInfo[skuNo])
                }
                if (componentList && componentList.length) {
                  itemData.componentList = componentList
                }
                if (['Z007', 'Z004'].includes(orderType)) {
                  if (!itemData.fixedAssetsList || itemData.fixedAssetsList.length === 0) {
                    itemData.fixedAssetsList = [{
                      fixedAssetsNo: null,
                      fixedAssetsCardQuantity: itemData.itemQuantity,
                      fixedAssetsCardNo: itemData.fixedAssetsCardNo
                    }]
                  }
                }
                if (priceInfo[skuNo]) {
                  const { discountAmount, taxRate = 0, taxedPrice = 0 } = priceInfo[skuNo]
                  const deliveryDays = coverItem.deliveryDays || (priceInfo[skuNo].deliveryDays || 0)
                  if (discountAmount) {
                    delete priceInfo[skuNo].discountAmount
                  }
                  const date = moment(createTime).add(deliveryDays, 'days').format('YYYY-MM-DD')
                  itemData.taxedPrice = taxedPrice
                  itemData.taxedPriceOrigin = taxedPrice
                  // itemData.untaxedPrice = Number((taxedPriceTimes / (1 + taxRate * 0.01)).toFixed(2))
                  itemData.taxedTotalAmount = Number((taxedPrice * (quantity || 0)).toFixed(2))
                  itemData.untaxedTotalAmount = Number(((quantity || 0) * taxedPrice / (1 + taxRate * 0.01)).toFixed(2))
                  itemData.taxTotalAmount = itemData.taxedTotalAmount - itemData.untaxedTotalAmount
                  itemData.standardLeadTime = deliveryDays
                  itemData.itemDeliveryDate = date
                  itemData.planList[0].deliveryDate = date

                  await calcUnTaxedPrice(itemData, this.dictList).then(data => {
                    if (data) itemData = merge(itemData, rowEditChange(data))
                  })
                  delete itemData.noMaintainPrice
                }
              }
              if (isUrgent === 1) {
                itemData.isUrgent = 1
              }
              if (orderType === 'Z003') {
                // 对于Z003订单，单位去主数据sku接口 unitCode
                itemData.unit = skuInfo[skuNo].unitCode
                const { factoryCode, warehouseLocation, shipWarehouseLocation } = itemData
                await this.updateTransferTime(factoryCode, warehouseLocation, shipWarehouseLocation, itemData)
              }
              this.purchaseData.itemList.push(itemData)
              if (needQueryAddress && (index === filterList.length - 1)) {
                const index = this.getFirstLineUpdateDefaultAddress()
                if (index !== null) {
                  this.resetDefaultAddress(index)
                }
              }
            }
          }
          this.addItemEmptyLine()
        }).finally(() => {
          loading.close()
        })
      }
    },
    addItemEmptyLine () {
      if (!this.ownerTransfer) {
        this.purchaseData.itemList.push({
          itemQuantity: 0,
          itemNo: this.getItemNo(),
          factoryCode: this.factoryCode,
          uuid: shortid.generate(),
          isEmptyLine: true,
          materialDescription: '',
          soNo: '',
          soItemNo: '',
          supplierMaterialNo: '',
          trackNo: '',
          unit: '',
          itemDeliveryDate: '',
          taxedPrice: '',
          priceTimes: '',
          generalLedgerAccount: '',
          costCenter: '',
          inputTax: '',
          materialGroup: ''
        })
      }
    },
    getItemNo () {
      const { itemList } = this.purchaseData
      const len = itemList.length
      if (len > 0) {
        const { itemNo } = itemList[len - 1]
        if (itemNo) {
          const numItemNo = Number(itemNo) + 10
          return padStart(numItemNo, 5, '0')
        }
      }
      return '00010'
    },
    handleUpdateMaterialDescription (row) {
      const prevItem = this.getItemSnapshot()
      let { projectCategory, receivedQuantity } = this.addCateRetQuantity()
      delete row.isEmptyLine
      row.userAdded = 1
      row.projectCategory = projectCategory
      row.receivedQuantity = receivedQuantity

      const { itemList } = this.purchaseData
      const len = itemList.length
      if (itemList[len - 1] === row || (itemList[len - 1] && itemList[len - 1].uuid) === (row && row.uuid)) {
        this.addItemEmptyLine()
      }
      this.compareWithPrevItem(prevItem)
    },
    handleUpdateCustomerService (val, row) {
      const { itemList } = this.purchaseData
      const idx = itemList.findIndex(el => el.uuid === row.uuid)
      let _soNo = ''
      if (idx > -1) {
        if (val === 'clear') {
          this.$set(itemList, idx, { ...itemList[idx], customerService: '' })
        } else {
          const { customerServiceName, soNo } = val
          if (soNo) _soNo = String(soNo).trim()
          if (customerServiceName) {
            this.$set(itemList, idx, {
              ...itemList[idx],
              customerService: customerServiceName
            })
          }
        }
        if (idx === this.getFirstLineUpdateDefaultAddress()) {
          const { supplierNo } = this.purchaseData.supplier
          const { purchaseGroup } = this.purchaseData
          const { factoryCode, warehouseLocation, skuNo } = this.purchaseData.itemList[idx]
          this.queryDefaultAddress(supplierNo, purchaseGroup, factoryCode, warehouseLocation, _soNo, skuNo)
        }
      }
    },
    queryAddressList (supplierNo, factoryCode, warehouseLocation) {
      if (supplierNo && factoryCode) {
        let params = {
          supplierNo,
          factoryCode
        }
        if (warehouseLocation) {
          params = {
            ...params,
            warehouseLocation
          }
        }
        listCommOptions(params).then(data => {
          if (data) {
            const { receipt, receive, supplier } = data
            if (receipt) {
              const { addressOptions = [], contactOptions = [] } = receipt
              // const addressMap = {}
              // const addrOptions = []
              // addressOptions.forEach(address => {
              //   const id = (address.province || '') + (address.city || '') + (address.region || '') + (address.street || '') + (address.detail || '')
              //   if (!addressMap[id]) {
              //     addressMap[id] = true
              //     addrOptions.push(address)
              //   }
              // })
              this.receiptAddressList = addressOptions
              this.receiptContactList = contactOptions
            }
            if (receive) {
              const { addressOptions = [], contactOptions = [] } = receive
              this.receiveAddressList = addressOptions
              this.receiveContactList = contactOptions
            }
            if (supplier) {
              const { addressOptions = [], contactOptions = [] } = supplier
              this.supplierAddressList = addressOptions
              this.supplierContactList = contactOptions
            }
          }
        })
      }
    },
    updateKeepSupplier (keep) {
      this.keepSupplier = keep
    },
    async queryDefaultAddress (supplierNo, purchaseGroupCode, factoryCode, warehouseLocation = '', soNo = '', skuNo = '') {
      if (!soNo) {
        const { itemList } = this.purchaseData
        const item = itemList.filter(item => !item.isDeleted)[0]
        if (item && item.soNo) {
          soNo = item.soNo
        }
      }
      const { orderType, paymentTermCode } = this.purchaseData
      // 无仓库地点
      if (orderType === 'Z007' || orderType === 'Z008' || orderType === 'Z011') {
        warehouseLocation = ''
      }
      this.pageLoading = true
      if (supplierNo && purchaseGroupCode && factoryCode) {
        await getCommDefault({
          supplierNo,
          purchaseGroupCode,
          factoryCode,
          warehouseLocation: warehouseLocation || '',
          soNo: soNo || '',
          paymentTermCode: paymentTermCode || '',
          skuNo: skuNo || ''
        }).then(data => {
          const { receipt, receive, supplier } = data
          this.resetCommunication('keepSupplier')
          if (receipt) {
            const { address = '', contact = '' } = receipt
            if (address) {
              this.purchaseData.receiptAddress = this.formatAddress(address)
              this.setAddress('receipt', address)
            }
            if (contact) {
              const { name, phone } = contact
              this.purchaseData.receiptContactName = name
              this.purchaseData.receiptContactPhone = phone
            }
          }
          if (receive) {
            const { address = '', contact = '' } = receive
            if (address) {
              this.purchaseData.receiveAddress = this.formatAddress(address)
              this.setAddress('receive', address, 0)
            }
            if (contact) {
              const { name, phone } = contact
              this.purchaseData.receiveContactName = name
              this.purchaseData.receiveContactPhone = phone
            }
          }
          if (supplier) {
            const { address = '', contact = '' } = supplier
            if (address && address.detail) {
              if (!this.purchaseData.supplierAddress) {
                this.purchaseData.supplierAddress = this.formatAddress(address)
                this.setAddress('supplier', address)
              }
            }
            if (contact) {
              const { name, phone } = contact
              if (!this.purchaseData.supplierContactName) {
                this.purchaseData.supplierContactName = name
              }
              if (!this.purchaseData.supplierContactPhone) {
                this.purchaseData.supplierContactPhone = phone
              }
            }
          }
        })
      }
      this.pageLoading = false
    },
    setAddress (type, address = {}, upDate = 1) {
      const {
        detail = '', city = '', province = '',
        region = '', street = '', postCode = ''
      } = address || {}
      this.purchaseData[`${type}AddressDetail`] = detail
      this.purchaseData[`${type}City`] = city
      this.purchaseData[`${type}Province`] = province
      this.purchaseData[`${type}Region`] = region
      this.purchaseData[`${type}Street`] = street
      this.purchaseData[`${type}AddressPostCode`] = postCode
      // 修改采购订单收货地址，采购价格不变，价格倍数不变。（现状：修改采购订单收货地址，采购价格变更，价格倍数不变。） 20230506
      // if (type === 'receive') {
      //   this.purchaseData.itemList.map(item => {
      //     if (item.skuNo && this.purchaseData.orderType !== 'Z003') {
      //       upDate ? this.updateDeliveryDaysByWarehouseLocation(item, false, 2) : this.reactiveSetItem(item)
      //     }
      //   })
      // }
    },
    resetCommunication (keepSupplier) {
      const { orderType } = this.purchaseData
      const addressList = ['Z007', 'Z008', 'Z010']
      if (this.keepSupplier !== true && keepSupplier !== 'keepSupplier') {
        this.setAddress('supplier')
        this.purchaseData.supplierAddress = ''
        this.purchaseData.supplierContactName = ''
        this.purchaseData.supplierContactPhone = ''
      }
      this.keepSupplier = null
      this.setAddress('receive', {}, 0)
      if (!addressList.includes(orderType)) {
        this.purchaseData.receiveAddress = ''
        this.purchaseData.receiveContactName = ''
        this.purchaseData.receiveContactPhone = ''
      }
      this.setAddress('receipt')
      this.purchaseData.receiptAddress = ''
      this.purchaseData.receiptContactName = ''
      this.purchaseData.receiptContactPhone = ''
    },
    formatAddress (address) {
      const { provinceText, cityText, regionText, streetText, detail } = address
      return [provinceText, cityText, regionText, streetText, detail].filter(item => !!item).join('')
    },
    clearValidate () {
      this.$refs['orderForm'].clearValidate()
    }
  }
}
</script>

<style lang="scss" scoped>
.purchase-edit {
  @import './styles/card-layout.scss';
  .amount {
    margin-right: 30px;
  }
  .tab{
    position: relative;
    .toggle-button{
      position: absolute;
      top: 25px;
      right: 20px;
    }
  }
  .fixed-edit{
    margin-top: 10px;
    position: fixed;
    bottom: 0px;
    z-index: 1000;
    padding: 10px;
    width: 100%;
    border-top: solid 2px #bfb2b23d;
    background-color: white;
    .btn-group{
      display: flex;
      margin-right: 30px;
      justify-content: flex-end;
    }
  }
}
.btn-row {
  margin: 10px 0;
}
.charge-row {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
  &-item {
    display: inline-block;
    width: 180px;
    &-highlight {
      font-weight: bold;
    }
  }
  &-btn {
    margin: 0 3px;
  }
}
</style>
