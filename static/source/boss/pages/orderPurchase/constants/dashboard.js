export const poOverviewFields = [
  [{
      title: '未清订单数/行数',
      propsFirst: 'unDeliverDonePOCnt',
      propsSecond: 'unDeliverDonePOItemCnt',
      percent: ''
    },
    {
      title: '未清实单（销售订单拉动）数/行数',
      propsFirst: 'unDeliverDoneSalePOCnt',
      propsSecond: 'unDeliverDoneSalePOItemCnt',
      percent: ''
    },
    {
      title: '未清紧急订单数/行数',
      propsFirst: 'unDeliverDoneUrgentPOCnt',
      propsSecond: 'unDeliverDoneUrgentPOItemCnt',
      percent: ''
    },
    {
      title: '未清未税采购额（元）',
      propsFirst: 'unDeliverDoneUnTaxedTotalAmount',
      propsSecond: '',
      percent: ''
    }
  ],
  [{
      title: '未清供应商数量',
      propsFirst: 'unDeliverDoneSupplierCnt',
      propsSecond: '',
      percent: ''
    },
    {
      title: '未清直发订单占比（按行）',
      propsFirst: 'unDeliverDoneDirectItemRatio',
      propsSecond: '',
      percent: true
    },
    {
      title: '未清直发订单占比（按金额）',
      propsFirst: 'unDeliverDoneDirectAmountRatio',
      propsSecond: '',
      percent: true
    },
    {
      title: '未清紧急订单占比（按行）',
      propsFirst: 'unDeliverDoneUrgentItemRatio',
      propsSecond: '',
      percent: true
    }
  ]
]

export const paymentFields = [
  [{
      title: '未清订单已提交预付款单数 /占比',
      propsFirst: 'submitCount',
      propsSecond: 'submitCountRatio',
      percent: '',
      propsSecondPercent: true
    },
    {
      title: '两天内预付款未提交单数 / 含税金额（元）',
      propsFirst: 'unSubmitCountInTwoDays',
      propsSecond: 'unSubmitMoneyInTwoDays',
      percent: ''
    },
    {
      title: '超两天预付款未提交单数 / 含税金额（元）',
      propsFirst: 'unSubmitCountOutTwoDays',
      propsSecond: 'unSubmitMoneyOutTwoDays',
      percent: ''
    }
  ]
]

export const overviewDetailField = [{
    title: '采购主管',
    field: 'parentPgCode',
    width: '120',
    enums: 'purchaseGroup'
  },
  {
    title: '采购员',
    field: 'pgCode',
    width: '120',
    enums: 'purchaseGroup'
  },
  {
    title: '未清订单行数',
    field: 'unDeliverDonePOItemCnt',
    width: '100'
  },
  {
    title: '未清订单数',
    field: 'unDeliverDonePOCnt',
    width: '100'
  },
  {
    title: '未清未税金额',
    field: 'unDeliverDoneUnTaxedTotalAmount',
    width: '150'
  },
  {
    title: '未清实单行数',
    field: 'unDeliverDoneSalePOItemCnt',
    width: '100'
  },
  {
    title: '未清实单单数',
    field: 'unDeliverDoneSalePOCnt',
    width: '100'
  },
  {
    title: '未清紧急订单行数',
    field: 'unDeliverDoneUrgentPOItemCnt',
    width: '150'
  },
  {
    title: '未清紧急订单单数',
    field: 'unDeliverDoneUrgentPOCnt',
    width: '150'
  },
  {
    title: '未清紧急订单行占比',
    field: 'unDeliverDoneUrgentItemRatio',
    width: '150',
    isPercent: true
  },
  {
    title: '未清直发行占比',
    field: 'unDeliverDoneDirectItemRatio',
    width: '100',
    isPercent: true
  },
  {
    title: '未清直发金额占比',
    field: 'unDeliverDoneDirectAmountRatio',
    width: '100',
    isPercent: true
  },
  {
    title: '未清供应商数量',
    field: 'unDeliverDoneSupplierCnt',
    width: '100'
  }
]
export const warningDetailField = [{
    title: '采购主管',
    field: 'parentPgCode',
    width: '120',
    enums: 'purchaseGroup'
  },
  {
    title: '采购员',
    field: 'pgCode',
    width: '120',
    enums: 'purchaseGroup'
  },
  {
    title: '超期未承诺',
    field: 'redTypeExceedNoPromiseCnt',
    width: '100'
  },
  {
    title: '超期未承诺占比',
    field: 'redTypeExceedNoPromiseRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '首次承诺过晚',
    field: 'redTypeFirstPromiseLaterCnt',
    width: '80'
  },
  {
    title: '首次承诺过晚占比',
    field: 'redTypeFirstPromiseLaterRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '交期无法满足',
    field: 'redTypeDeliveryDateNotMetCnt',
    width: '80'
  },
  {
    title: '交期无法满足占比',
    field: 'redTypeDeliveryDateNotMetRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '上次跟单超8天',
    field: 'redTypeEightDayNotFollowCnt',
    width: '80'
  },
  {
    title: '上次跟单超8天占比',
    field: 'redTypeEightDayNotFollowRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '逾期',
    field: 'redTypeOverdueCnt',
    width: '100'
  },
  {
    title: '逾期占比',
    field: 'redTypeOverdueRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '距离上次跟单超七天',
    field: 'yellowTypeSevenDayNotFollowCnt',
    width: '100'
  },
  {
    title: '距离上次跟单超七天占比',
    field: 'yellowTypeSevenDayNotFollowRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '当前日期距离交货日期剩余3天',
    field: 'yellowTypeDeliveryDateLessThanThreeDayCnt',
    width: '150'
  },
  {
    title: '当前日期距离交货日期剩余3天占比',
    field: 'yellowTypeDeliveryDateLessThanThreeDayRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '发货日期+2天无物流单号',
    field: 'yellowTypeDeliveryTwoDayNoLogisticsCodeCnt',
    width: '100'
  },
  {
    title: '发货日期+2天无物流单号占比',
    field: 'yellowTypeDeliveryTwoDayNoLogisticsCodeRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '发货日期+5天未收货完成',
    field: 'yellowTypeDeliveryFiveDayNotDeliveryDoneCnt',
    width: '150'
  },
  {
    title: '发货日期+5天未收货完成占比',
    field: 'yellowTypeDeliveryFiveDayNotDeliveryDoneRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '直发订单并且需要签单返回，但7天后无签单',
    field: 'yellowTypeDirectSevenDayNoSignBackCnt',
    width: '150'
  },
  {
    title: '直发订单并且需要签单返回，但7天后无签单占比',
    field: 'yellowTypeDirectSevenDayNoSignBackRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '发货状态已签收但无签收单',
    field: 'yellowTypeDeliveryDoneNoSignBackCnt',
    width: '100'
  },
  {
    title: '发货状态已签收但无签收单占比',
    field: 'yellowTypeDeliveryDoneNoSignBackRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '签收单VC后台已上传',
    field: 'yellowTypeSignOrderVcUploadedCnt',
    width: '80'
  },
  {
    title: '签收单VC后台已上传占比',
    field: 'yellowTypeSignOrderVcUploadedRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '发货状态已签收且客服不要求签收单',
    field: 'yellowTypeDeliveryDoneNotNeedSignBackCnt',
    width: '100'
  },
  {
    title: '发货状态已签收且客服不要求签收单占比',
    field: 'yellowTypeDeliveryDoneNotNeedSignBackRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '直发客户已收货',
    field: 'yellowTypeDirectCustomerReceivedCnt',
    width: '80'
  },
  {
    title: '直发客户已收货占比',
    field: 'yellowTypeDirectCustomerReceivedRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '等客户通知送货但七天未跟单',
    field: 'yellowTypeCustomerNotifySevenDayNotFollowCnt',
    width: '150'
  },
  {
    title: '等客户通知送货但七天未跟单占比',
    field: 'yellowTypeCustomerNotifySevenDayNotFollowRatio',
    width: '80',
    isPercent: true
  },
  {
    title: '未清无需跟进',
    field: 'greenTypeOrderCnt',
    width: '100'
  },
  {
    title: '未清无需跟进占比',
    field: 'greenTypeOrderRatio',
    width: '80',
    isPercent: true
  }
]
export const WaringDetailPercentField = ['greenTypeOrderRatio', 'yellowTypeCustomerNotifySevenDayNotFollowRatio', 'yellowTypeDirectCustomerReceivedRatio',
  'yellowTypeDeliveryDoneNotNeedSignBackRatio', 'yellowTypeSignOrderVcUploadedRatio', 'yellowTypeDeliveryDoneNoSignBackRatio',
  'yellowTypeDirectSevenDayNoSignBackRatio', 'yellowTypeDeliveryFiveDayNotDeliveryDoneRatio', 'yellowTypeDeliveryTwoDayNoLogisticsCodeRatio',
  'yellowTypeDeliveryDateLessThanThreeDayRatio', 'yellowTypeSevenDayNotFollowRatio', 'redTypeOverdueRatio', 'redTypeEightDayNotFollowRatio',
  'redTypeDeliveryDateNotMetRatio', 'redTypeFirstPromiseLaterRatio', 'redTypeExceedNoPromiseRatio'
]

export const paymentField = [{
    title: '采购主管',
    field: 'parentPgCode',
    width: '120',
    enums: 'purchaseGroup'
  },
  {
    title: '采购员',
    field: 'pgCode',
    width: '120',
    enums: 'purchaseGroup'
  },
  {
    title: '全部需提交单量',
    field: 'totalCount',
    width: '100'
  },
  {
    title: '已提交单数',
    field: 'submitCount',
    width: '100'
  },
  {
    title: '已提交比例',
    field: 'submitCountRatio',
    width: '150',
    isPercent: true
  },
  {
    title: '未提交单数',
    field: 'unSubmitCount',
    width: '100'
  },
  {
    title: '两天内未提交单数',
    field: 'unSubmitCountInTwoDays',
    width: '100'
  },
  {
    title: '两天内未提交含税金额（元）',
    field: 'unSubmitMoneyInTwoDays',
    width: '150'
  },
  {
    title: '超两天未提交单数',
    field: 'unSubmitCountOutTwoDays',
    width: '150'
  },
  {
    title: '超两天未提交含税金额（元）',
    field: 'unSubmitMoneyOutTwoDays',
    width: '150'
  }
]

export const overviewDetailPercentField = [
  'unDeliverDoneDirectItemRatio', 'unDeliverDoneDirectAmountRatio', 'unDeliverDoneUrgentItemRatio'
]
export const paymentPercentField = [
  'submitCountRatio'
]

export const redWaringTableField = [{
    title: '',
    field: 'type',
    fixed: 'left',
    width: '100'
  },
  {
    title: '超期未承诺',
    field: 'redTypeExceedNoPromiseCnt'
  },
  {
    title: '首次承诺过晚',
    field: 'redTypeFirstPromiseLaterCnt'
  },
  {
    title: '交期无法满足',
    field: 'redTypeDeliveryDateNotMetCnt'
  },
  {
    title: '上次跟单超8天',
    field: 'redTypeEightDayNotFollowCnt'
  },
  {
    title: '逾期',
    field: 'redTypeOverdueCnt'
  }
]
export const yellowWaringTableField = [{
    title: '',
    field: 'type',
    width: '100',
    fixed: 'left'
  },
  {
    title: '距离上次跟单超七天',
    field: 'yellowTypeSevenDayNotFollowCnt'
  },
  {
    title: '当前日期距离交货日期剩余3天',
    field: 'yellowTypeDeliveryDateLessThanThreeDayCnt'
  },
  {
    title: '发货日期+2天无物流单号',
    field: 'yellowTypeDeliveryTwoDayNoLogisticsCodeCnt'
  },
  {
    title: '发货日期+5天未收货完成',
    field: 'yellowTypeDeliveryFiveDayNotDeliveryDoneCnt'
  },
  {
    title: '直发订单并且需要签单返回，但7天后无签单',
    field: 'yellowTypeDirectSevenDayNoSignBackCnt'
  },
  {
    title: '发货状态已签收但无签收单',
    field: 'yellowTypeDeliveryDoneNoSignBackCnt'
  },
  {
    title: '签收单VC后台已上传',
    field: 'yellowTypeSignOrderVcUploadedCnt'
  },
  {
    title: '发货状态已签收且客服不要求签收单',
    field: 'yellowTypeDeliveryDoneNotNeedSignBackCnt'
  },
  {
    title: '直发客户已收货',
    field: 'yellowTypeDirectCustomerReceivedCnt'
  },
  {
    title: '等客户通知送货但七天未跟单',
    field: 'yellowTypeCustomerNotifySevenDayNotFollowCnt'
  }
]

export const greenWaringTableField = [{
    title: '',
    field: 'type',
    fixed: 'left',
    width: '100'
  },
  {
    title: '未清无需跟进',
    field: 'greenTypeOrderCnt'
  }
]
export const redWarningTableRow1 = ['redTypeExceedNoPromiseCnt', 'redTypeFirstPromiseLaterCnt', 'redTypeDeliveryDateNotMetCnt', 'redTypeEightDayNotFollowCnt', 'redTypeOverdueCnt']
export const redWarningTableRow2 = ['redTypeExceedNoPromiseRatio', 'redTypeFirstPromiseLaterRatio', 'redTypeDeliveryDateNotMetRatio', 'redTypeEightDayNotFollowRatio', 'redTypeOverdueRatio']
export const yellowTableRow1 = ['yellowTypeSevenDayNotFollowCnt', 'yellowTypeDeliveryDateLessThanThreeDayCnt', 'yellowTypeDeliveryTwoDayNoLogisticsCodeCnt',
  'yellowTypeDeliveryFiveDayNotDeliveryDoneCnt', 'yellowTypeDeliveryDoneNoSignBackCnt', 'yellowTypeDirectSevenDayNoSignBackCnt', 'yellowTypeSignOrderVcUploadedCnt',
  'yellowTypeDeliveryDoneNotNeedSignBackCnt', 'yellowTypeDirectCustomerReceivedCnt', 'yellowTypeCustomerNotifySevenDayNotFollowCnt'
]
export const yellowTableRow2 = ['yellowTypeSevenDayNotFollowRatio', 'yellowTypeDeliveryDateLessThanThreeDayRatio', 'yellowTypeDeliveryTwoDayNoLogisticsCodeRatio',
  'yellowTypeDeliveryFiveDayNotDeliveryDoneRatio', 'yellowTypeDeliveryDoneNoSignBackRatio', 'yellowTypeDirectSevenDayNoSignBackRatio', 'yellowTypeSignOrderVcUploadedRatio',
  'yellowTypeDeliveryDoneNotNeedSignBackRatio', 'yellowTypeDirectCustomerReceivedRatio', 'yellowTypeCustomerNotifySevenDayNotFollowRatio'
]

export const greenTableRow1 = ['greenTypeOrderCnt']
export const greenTableRow2 = ['greenTypeOrderRatio']

export const renderTips = {
  'red': '目前最紧急需要处理事项',
  'yellow': '目前出现问题，需要处理以阻止问题进一步严重',
  'green': '目前订单没有问题，非常好，请保持'
}

export const OverdueTypeField = [{
    title: '类型',
    field: 'poFollowTagType',
    enums: 'poFollowReportTag'
  },
  {
    title: '正常货期',
    field: 'poOverdueNormalCount'
  },
  {
    title: '逾期-已发货',
    field: 'poOverdueShippedCount'
  },
  {
    title: '逾期-未发货',
    field: 'poOverdueNotShippedCount'
  }
]

export const OverdueTypDetailField = [{
    title: '采购主管',
    field: 'parentPgCode',
    enums: 'purchaseGroup',
    width: 100
  },
  {
    title: '采购员',
    field: 'pgCode',
    enums: 'purchaseGroup',
    width: 100
  },
  {
    title: '类型',
    field: 'poFollowTagType',
    enums: 'poFollowReportTag'
  },
  {
    title: '正常货期',
    field: 'poOverdueNormalCount'
  },
  {
    title: '逾期-已发货',
    field: 'poOverdueShippedCount'
  },
  {
    title: '逾期-未发货',
    field: 'poOverdueNotShippedCount'
  }
]

export const overdueReasonFields = [{
    title: '一级原因',
    field: 'overdueFirstReason',
    enums: 'overdueReasonFirst'
  },
  {
    title: '二级原因',
    field: 'overdueSecondReason',
    enums: 'overdueReasonSecond'
  },
  {
    title: '数量',
    field: 'count'
  },
  {
    title: '占比',
    field: 'ratio',
    isPercent: true
  }
]

export const overdueReasonPercentField = [
  'ratio'
]
export const OverdueReasonDetailField = [{
    title: '采购主管',
    field: 'parentPgCode',
    enums: 'purchaseGroup',
    width: 100
  },
  {
    title: '采购员',
    field: 'pgCode',
    enums: 'purchaseGroup',
    width: 100
  },
  {
    title: '类型',
    field: 'warningType',
    enums: 'warningType'
  },
  {
    title: '逾期类型',
    field: 'poFollowTagType',
    enums: 'poFollowReportTag'
  },
  {
    title: '一级原因',
    field: 'overdueFirstReason',
    enums: 'overdueReasonFirst'
  },
  {
    title: '二级原因',
    field: 'overdueSecondReason',
    enums: 'overdueReasonSecond'
  },
  {
    title: '数量',
    field: 'count'
  }
]
