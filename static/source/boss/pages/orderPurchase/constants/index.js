import { deepClone } from '@/utils/index'

// 静态资源上传地址 https://api-fe.zkh360.com/cdn/image ，上传完成后替换下面的链接即可
/**
 * 已废弃下面模板url，改为从mse获取
 */
export const Z004Template = 'https://static.zkh360.com/file/2024-06-05/0604-%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA-%E9%80%80%E8%B4%A7%E9%87%87%E8%B4%AD%E8%AE%A2%E5%8D%95V11-1717554886775.xlsx'
export const Z003Template = 'https://static.zkh360.com/file/2024-02-04/%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA-%E8%BD%AC%E5%82%A8%E9%87%87%E8%B4%AD%E8%AE%A2%E5%8D%95%20V4-1707015716932.xlsx'
export const Z007Z008Template = 'https://static.zkh360.com/file/resource/orderPurchase/%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA-%E8%A1%8C%E6%94%BF%E7%B1%BB%E9%87%87%E8%B4%AD%E8%AE%A2%E5%8D%95V4.xlsx'
export const Z010Template = 'https://files.zkh360.com/mm/create/%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA-%E7%9B%B4%E5%8F%91%E6%88%96%E6%9C%8D%E5%8A%A1%E9%87%87%E8%B4%AD%E8%AE%A2%E5%8D%95.xlsx'
export const standardUrl = 'https://static.zkh360.com/file/2024-02-04/%E6%89%B9%E9%87%8F%E5%88%9B%E5%BB%BA-%E6%A0%87%E5%87%86%E9%87%87%E8%B4%AD%E8%AE%A2%E5%8D%95V4-1707015513102.xlsx'

export const batchEditHeaderUrl = 'https://static.zkh360.com/all/file/2022-06-23/批量修改-采购订单抬头-00866b.xlsx'
export const batchEditBodyUrl = 'https://static.zkh360.com/file/2023-12-13/%E6%89%B9%E9%87%8F%E4%BF%AE%E6%94%B9-%E9%87%87%E8%B4%AD%E8%AE%A2%E5%8D%95%E9%A1%B9%E7%9B%AE%E8%A1%8C-6a2cf8-1688096316716%20-1702432000180.xlsx'

export const tabTips = {
  '2': '供应商未确认：未清订单（不包含Z003、Z004、货权转移自动生成的Z001、免费超收）里，前一天12点前 供应商未确认的采购订单',
  '3': '首次承诺不满足：未清订单（不包含Z003、Z004、货权转移自动生成的Z001、免费超收）中，“交期确认标识”勾选 且“客户接受交期”未勾 且 客户接受交货日期为空 且 “供应商首次承诺日期” >“标准交货日期” 的采购订单',
  '4': `交期不满足：未清订单（不包含Z003、Z004、货权转移自动生成的Z001、免费超收）且供应商已确认的采购单。
1）当“客户接受交期”未勾，计划行交货日期 > 标准交货日期 ，认定交期无法满足
2）当“客户接受交期”未勾，计划行交货日期 > 客户接受交货日期 ，认定交期无法满足`,
  '5': '订单已逾期：未清订单（不包含Z003、Z004、货权转移自动生成的Z001、免费超收）中，供应商已确认 且 计划行交期已小于等于当天的采购订单',
  '6': '临近三天逾期且供应商未发货：未清订单（不包含Z003、Z004、货权转移自动生成的Z001、免费超收）中，交货日期-当前日期≤4天 并且 发货日期为空的采购订单',
  '12': '跟单超7天：未清订单（不包含Z003、Z004、货权转移自动生成的Z001、免费超收）中，首次交期确认后，距离上次跟单超过7天的采购订单',
  '8': '调拨订单：未完成收货的Z003调拨订单',
  '9': '备货订单：未完成收获的备货订单（无对应跟踪单号+销售订单（客服）+专料专供单号），不包含Z003调拨订单、货权转移自动生成的Z001、免费超收、Z004、所有已清即已完成收货的',
  '10': '发货2天无物流单号：当天 - 发货日期 ≥ 2 天 且 物流单号字段为空（不含自营车辆自主配送），不包含Z003调拨订单、货权转移自动生成的Z001、免费超收、Z004、所有已清即已完成收货的',
  '11': '发货5天未收货：当天 - 发货日期 ≥ 5 天 且 未完成全部收货，不包含Z003调拨订单、货权转移自动生成的Z001、免费超收、Z004、所有已清即已完成收货的',
  '13': '直发待收货和签单: 直发需要在vc上确认收货及上传签单的数据，参考操作提示'
}
export const tabsLists = [
  { name: '未下采购订单', value: '1' },
  { name: '供应商未确认', value: '2' },
  { name: '首次承诺不满足', value: '3' },
  { name: '交期不满足', value: '4' },
  { name: '订单已逾期', value: '5' },
  { name: '临近三天逾期且供应商未发货', value: '6' },
  { name: '跟单超7天', value: '12' },
  { name: '调拨订单', value: '8' },
  { name: '备货订单', value: '9' },
  { name: '发货2天没有物流单号', value: '10' },
  { name: '发货5天未收货', value: '11' },
  { name: '直发待收货和签单', value: '13' },
  { name: '保供订单', value: '14' }
]
export const searchFrom = {
  supplierNoList: [],
  skuNo: '',
  workOrderNo: '',
  poNo: '',
  orderDate: [],
  workOrderStatus: '',
  purchaseGroupList: [],
  openPoStatus: '',

  soNo: '',
  customerNo: '',
  deliveryDate: [],
  approvalDate: [],
  soCreateDate: [],
  orderTypeList: [],
  approveStep: ''
}
export const toBeReceivedFormList = {
  supplierNoList: [],
  workOrderNo: '',
  poNo: '',
  orderScope: '',
  orderDate: [],
  workOrderStatus: '',
  orderTypeList: [],
  directReceipt: '',
  directReturn: '',
  isDeliveryOrderSignedList: '',
  deliveryOrderStatusList: '',
  openPoStatus: '',
  operationReminder: '',
  soSigningBackList: [],
  deliveryDate: [],
  approvalDate: [],
  soCreateDate: [],
  purchaseGroupList: [],
  approveStep: ''
}
export const searchFormList = {
  1: { supplierNoList: [],
    skuNo: '',
    workOrderNo: '',
    workOrderStatus: '',
    purchaseGroupList: [],

    soNo: '',
    customerNo: '',
    deliveryDate: [],
    approvalDate: [],
    soCreateDate: [],
    brandId: '',
    ifVpi: '',
    ifUrgent: null,
    brands: []
  },
  2: deepClone(searchFrom),
  3: deepClone(searchFrom),
  4: deepClone(searchFrom),
  5: deepClone(searchFrom),
  6: deepClone(searchFrom),
  12: deepClone(searchFrom),
  8: deepClone(searchFrom),
  9: deepClone(searchFrom),
  10: deepClone(searchFrom),
  11: deepClone(searchFrom),
  13: deepClone(toBeReceivedFormList),
  14: {
    ...deepClone(searchFrom),
    guaranteedStatusList: []
  }
}
export const workbenchFilters = [
  { type: 'supplier', label: '供应商名称', placeholder: '请输入供应商名称', prop: 'supplierNoList', multiple: true, span: 8 },
  { type: 'input', label: 'SKU', placeholder: '请输入SKU，同时支持200个单号，按空格或换行符分隔', prop: 'skuNo', span: 8 },
  { type: 'input', label: '工单号', placeholder: '请输入工单号', prop: 'workOrderNo', span: 8 },
  { type: 'input', label: '采购订单', placeholder: '请输入采购订单，同时支持200个单号，按空格或换行符分隔', prop: 'poNo', span: 8 },
  { type: 'date', label: '采购订单日期', placeholder: '请输入采购订单', prop: 'orderDate', span: 8 },
  { type: 'select', label: '采购组', enums: 'purchaseGroupList', clearable: true, multiple: true, placeholder: '请选择采购组', prop: 'purchaseGroupList', span: 8, showCode: true },
  { type: 'select', label: '工单状态', enums: 'workOrderStatus', clearable: true, placeholder: '工单状态', prop: 'workOrderStatus', span: 8 },
  { label: '订单类型', prop: 'orderTypeList', type: 'select', enums: 'orderType', multiple: true, required: false, span: 8 },
  { label: '仓库地点', prop: 'warehouseLocationList', type: 'select', enums: 'warehouseLocationList', multiple: true, required: false, span: 8, showCode: true },
  { type: 'select', label: '是否开通PO', enums: 'Boolean', clearable: true, placeholder: '是否开通PO', prop: 'openPoStatus', span: 8 },
  { label: '审批状态', prop: 'approveStep', type: 'select', enums: 'approveStatus', clearable: true, required: false, span: 8 },
  { label: '保供状态', prop: 'guaranteedStatusList', type: 'select', enums: 'guaranteedStatus', multiple: true, clearable: true, required: false, span: 8 }
]
export const toBeReceivedFilters = [
  { type: 'supplier', label: '供应商名称', placeholder: '请输入供应商名称', prop: 'supplierNoList', multiple: true, span: 8 },
  { type: 'input', label: '采购订单', placeholder: '请输入采购订单，同时支持200个单号，按空格或换行符分隔', prop: 'poNo', span: 8 },
  { type: 'input', label: '工单号', placeholder: '请输入工单号', prop: 'workOrderNo', span: 8 },
  { type: 'select', label: '订单范围', enums: 'reportOrderRange', clearable: true, placeholder: '订单范围', prop: 'orderScope', span: 8 },
  { type: 'date', label: '采购订单日期', placeholder: '请输入采购订单', prop: 'orderDate', span: 8 },
  { type: 'select', label: '采购组', enums: 'purchaseGroupList', clearable: true, multiple: true, placeholder: '请选择采购组', prop: 'purchaseGroupList', span: 8, showCode: true },
  { type: 'select', label: '直发已送达', enums: 'Boolean', clearable: true, placeholder: '直发已送达', prop: 'directReceipt', span: 8 },
  { type: 'select', label: '直发签单能否回传', enums: 'Boolean', clearable: true, placeholder: '直发签单能否回传', prop: 'directReturn', span: 8 },
  { type: 'select', label: '签收单', enums: 'Boolean', clearable: true, placeholder: '是否上传签收单', prop: 'isDeliveryOrderSignedList', span: 8 },
  { type: 'select', label: '是否开通PO', enums: 'Boolean', clearable: true, placeholder: '是否开通PO', prop: 'openPoStatus', span: 8 },
  { type: 'select', label: '签单返回', enums: 'signingBack', clearable: true, placeholder: '签单返回', multiple: true, prop: 'soSigningBackList', span: 8 },
  { type: 'select', label: '发货状态', enums: 'deliveryOrderStatus', clearable: true, placeholder: '发货状态', prop: 'deliveryOrderStatusList', span: 8 },
  { type: 'select', label: '工单状态', enums: 'workOrderStatus', clearable: true, placeholder: '工单状态', prop: 'workOrderStatus', span: 8 },
  { type: 'select', label: '操作提醒', enums: 'operationReminder', clearable: true, placeholder: '操作提醒', prop: 'operationReminder', span: 8 }
]
export const abnormalFilters = [
  { type: 'customer', label: '客户名称', placeholder: '客户名称', prop: 'customer', span: 8 },
  { type: 'input', label: 'SKU', placeholder: '请输入SKU，同时支持200个单号，按空格或换行符分隔', prop: 'skuNo', span: 8 },
  { type: 'input', label: '工单号', placeholder: '请输入工单号', prop: 'workOrderNo', span: 8 },
  { type: 'input', label: '销售订单', placeholder: '请输入销售订单，同时支持200个单号，按空格或换行符分隔', prop: 'soNo', span: 8 },
  { type: 'date', label: '销售订单日期', placeholder: '请输入', prop: 'soCreateDate', span: 8 },
  { type: 'date', label: '请求发货日期', placeholder: '请输入', prop: 'deliveryDate', span: 8 },
  { type: 'date', label: '应下采购日期', placeholder: '请输入', prop: 'approvalDate', span: 8 },
  { type: 'select', label: '采购组', enums: 'purchaseGroupList', clearable: true, multiple: true, placeholder: '请选择采购组', prop: 'purchaseGroupList', span: 8, showCode: true },
  { type: 'select', label: '工单状态', enums: 'workOrderStatus', clearable: true, placeholder: '工单状态', prop: 'workOrderStatus', span: 8 },
  { type: 'brand', label: '品牌', placeholder: '请输入品牌', prop: 'brandId', span: 8 },
  { type: 'ifVpi', label: 'VPI物料', placeholder: '请选择', prop: 'ifVpi', span: 8 },
  { type: 'ifUrgent', label: '急单显示', placeholder: '请选择', prop: 'ifUrgent', span: 8 }
]
// supplierFactory-工厂；bidLevel-是否招投标；recommendingLevel-推优等级；operateType-信息类别；shippingfeeRateOrigin-运费费率来源
export const oneProductColumns = [
  { title: '状态', field: 'purchaseStatus', width: 100 }, // 字典取哪个
  { title: '工厂', field: 'factoryName' },
  { title: '供应商', field: 'supplierCode' }, // 是否返回供应商名称
  { title: '区域总仓', field: 'mrpAreaName', width: 120 },
  { title: '信息类别', field: 'operateType', enums: 'operateType', width: 100 },
  { title: '采购价格', field: 'unitPrice', width: 100 },
  { title: '币种', field: 'currencyName', width: 100 },
  { title: '优先级别', field: 'recommendingLevel', enums: 'recommendingLevel', width: 120 },
  { title: '现货交期', field: 'leadTimeInStock', width: 100 },
  { title: '供应商采购MOQ', field: 'purchaseMoq', width: 120 },
  { title: '采购MPQ', field: 'purchaseMpq', width: 120 },

  { title: '操作', field: 'operation', width: 160 }
]

export const chargeProps = ['tariff', 'saleTax', 'latePayment', 'premium', 'intlShipping', 'customsFee', 'other']
