<template>
  <div class="resource-management">
    <el-row :span="24" :gutter="20">
      <el-col :span="6">
        <div class="rolesPowerLeft"
              :style="{ height: screenHeight + 'px'}"
        >
          <div class="ctitle">资源管理</div>
          <div style="text-align: right; margin-bottom: 10px"><el-button  v-if="currAppId" @click="openAddDialog()" type="primary" icon="el-icon-plus">新建资源</el-button></div>
          <div class="treeBox">
            <el-tree
              v-if="appPowersData"
              :data="appPowersData"
              node-key="id"
              ref="tree"
              highlight-current
              @node-click="handleNodeClick"
              :default-expanded-keys="defaultExpandedKeys"
              :props="defaultProps">
            </el-tree>
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="rolesPowerRight" :style="{ height: screenHeight + 'px'}">
          <div class="ctitle">菜单信息</div>
          <div class="info" v-if="currTreeNode">
            <el-row :span="24">
              <el-col :span="4" class="dialogTitle">名称：</el-col>
              <el-col :span="20">{{currTreeNode && currTreeNode.name}}</el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="4" class="dialogTitle">链接：</el-col>
              <el-col :span="20">{{currTreeNode && currTreeNode.link}}</el-col>
            </el-row>
            <el-row :span="24">
              <el-col :span="4" class="dialogTitle">图标样式：</el-col>
              <el-col :span="20">{{currTreeNode && currTreeNode.icon}}</el-col>
            </el-row>
            <el-row :span="24" :style="{display: 'flex', justifyContent: 'center', paddingTop: '30px'}">
              <el-button icon="el-icon-top" @click="goUpResource(currTreeNode)" type="primary">上移</el-button>
              <el-button icon="el-icon-bottom" @click="goDownResource(currTreeNode)" type="primary">下移</el-button>
              <el-button icon="el-icon-edit" @click="openAddDialog(currTreeNode)" type="warning">修改</el-button>
              <el-button icon="el-icon-delete" @click="goDeleteResource(currTreeNode)" type="danger">删除</el-button>
            </el-row>
          </div>
        </div>
      </el-col>
    </el-row>
    <!--添加弹出框-->
    <el-dialog
      width="40%"
      title="资源信息"
      class="dialogClass"
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <el-form ref="dialogForm" :model="dialogForm" :rules="rules" style="width: 100%" label-suffix=":" label-width="80px" :hide-required-asterisk="false">
        <el-form-item label="名称" prop="name" required>
          <el-input
            v-model="dialogForm.name"
            filterable
            clearable
            style="width:100%"
            placeholder="名称"
          />
        </el-form-item>
        <el-form-item label="类型" prop="type" required>
          <el-select
            v-model="dialogForm.type"
            filterable
            style="width: 100%;"
            placeholder="请选择类型">
            <el-option
              v-for="item in resourcesTypeOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="链接" prop="link">
          <el-input
            v-model="dialogForm.link"
            filterable
            clearable
            style="width:100%"
            placeholder="链接"
          />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input
            v-model="dialogForm.icon"
            filterable
            clearable
            style="width:100%"
            placeholder="图标样式"
          />
        </el-form-item>
        <el-row :gutter="20"  v-for="(item, index) in dialogForm.urls" :item="item" :key="index">
          <el-col :span="12">
            <el-form-item label="权限URL" prop="urls">
              <el-input
                v-model="item.urls"
                filterable
                clearable
                style="width:100%"
                placeholder="权限URL"
                :disabled="index < dialogForm.urls.length - 1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-select
                v-model="item.methodType"
                filterable
                clearable
                style="width: 100%"
                :disabled="index < dialogForm.urls.length - 1"
                placeholder="请选择">
                <el-option
                  v-for="item in requestOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4" :style="{display: 'flex', justifyContent: 'flex-end'}">
            <el-button @click="deletePowerUrl(index)" v-if="index < dialogForm.urls.length - 1" type="warning" icon="el-icon-minus"></el-button>
            <el-button @click="addPowerUrl" v-else type="primary" icon="el-icon-plus" :disabled="!item.methodType"></el-button>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" :style="{display: 'flex', justifyContent: 'center'}">
        <el-button size="medium" @click="storeAddDialog" type="primary">保存</el-button>
        <el-button size="medium" @click="closeAddDialog" >取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAppResources,
  setAppResources,
  modifyResources,
  deleteResources,
  upResources,
  downResources
} from '@/api/authoritySystem'

export default {
  name: 'resourceManagement',
  data () {
    return {
      loading: null,
      dialogForm: {
        id: '',
        name: '', // 名称
        type: 'MENU', // 类型
        link: '', // 链接
        icon: '', // 图标
        urls: [
          {
            urls: '',
            methodType: ''
          }
        ] // 权限链接
      },
      rules: {
        name: [
          { required: true, message: '名称不能为空！', trigger: ['change', 'blur'] }
        ],
        type: [
          { required: true, message: '类型不能为空！', trigger: ['change', 'blur'] }
        ]
      },
      requestOptions: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS', 'ALL'], // 请求方式
      dialogVisible: false,
      currAppId: 138, // 当前选中的应用的信息id
      currTreeNode: null, // 当前选中的tree的信息
      screenHeight: '450',
      appPowersData: null, // 当前应用下面的所有资源
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      resourcesTypeOptions: [
        {
          id: 'MENU',
          name: '菜单'
        },
        {
          id: 'BUTTON',
          name: '按钮'
        },
        {
          id: 'SERVICE',
          name: '服务'
        },
        {
          id: 'DASHBOARD',
          name: '看板'
        }
      ],
      defaultExpandedKeys: []
    }
  },
  created () {
    if (process.env.NODE_ENV === 'development') {
      this.currAppId = 277
    }
    this.goGetAppResources()
  },
  methods: {
    addPowerUrl () {
      this.dialogForm.urls.push({
        url: '',
        methodType: ''
      })
    },
    deletePowerUrl (index) {
      this.dialogForm.urls.splice(index, 1)
    },
    handleNodeClick (item) {
      this.currTreeNode = item
    },
    goGetAppResources () {
      // 获取应用下面的所有权限
      this.loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.5)'
      })
      let data = {
        appId: this.currAppId
      }
      getAppResources(data).then(res => {
        this.appPowersData = res.children
      }).finally(() => {
        this.loading.close()
      })
    },
    openAddDialog (currTreeNode) {
      // 打开添加数据窗口
      if (currTreeNode) {
        // 修改
        this.dialogForm = JSON.parse(JSON.stringify(currTreeNode))
      } else {
        // 新增
        this.dialogForm = {
          id: '',
          name: '', // 名称
          type: 'MENU', // 类型
          link: '', // 链接
          icon: '', // 图标
          urls: [
            {
              urls: '',
              methodType: ''
            }
          ] // 权限链接
        }
      }
      this.dialogVisible = true
      setTimeout(() => {
        this.$refs.dialogForm.clearValidate()
      }, 0);
    },
    closeAddDialog () {
      this.dialogVisible = false
    },
    storeAddDialog () {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          // 提交修改、添加的操作
          let This = this
          let name = this.dialogForm.name
          let type = this.dialogForm.type
          let link = this.dialogForm.link
          let icon = this.dialogForm.icon
          let urls = this.dialogForm.urls
          let resourceId = this.dialogForm.id
          if (resourceId) {
            // 修改资源
            let param = this.dialogForm
            modifyResources(resourceId, param).then(res => {
              This.$message.success('修改成功')
              This.currTreeNode.name = res.name
              This.currTreeNode.link = res.link
              This.currTreeNode.icon = res.icon
              This.currTreeNode.type = res.type
              This.currTreeNode.urls = res.urls
            }).catch((error) => {
              console.log(error);
            })
          } else {
            // 添加资源
            let appId = this.currAppId
            let parentId = this.currTreeNode ? this.currTreeNode.id : ''
            let param = {
              appId: appId,
              name: name,
              icon: icon,
              parentId: parentId || '',
              type: type,
              urls: urls,
              link: link
            }
            setAppResources(param).then(res => {
              if (res) {
                This.$message.success('添加成功')
                // 添加成功后，把新数据添加到树状结构中，使其保持现在打开状态不变，且能够添加成功
                // 添加时候parentId不存在说明是加在了根节点上
                if (!res.parentId || res.parentId === 1) {
                  // 添加到根节点上
                  This.appPowersData.push(res)
                } else {
                  // 添加到子节点上
                  // tree 添加节点
                  This.$refs['tree'].append(res, this.currTreeNode)
                  // tree 默认选中
                  // This.$refs['tree'].setCurrentKey(this.currTreeNode.id)
                  // tree 默认展开
                  This.defaultExpandedKeys = [res.id]
                }
              }
            }).catch((error) => {
              console.log(error);
            })
          }
          this.dialogVisible = false
        } else {
          return false
        }
      })
    },
    goDeleteResource (currTreeNode) {
      let This = this
      this.$confirm('确认要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteResources(currTreeNode.id).then(res => {
          if (res) {
            this.$message.error('删除失败')
          } else {
            This.$message({
              type: 'success',
              message: '删除成功'
            })
            This.$refs.tree.remove(This.currTreeNode)
            This.currTreeNode = null
          }
        })
      }).catch(() => {
      })
    },
    goUpResource (currTreeNode) {
      let This = this
      upResources(currTreeNode.id).then(res => {
        let treeList = This.appPowersData
        forData(treeList, [])
        function forData (data) {
          data.forEach((item, index) => {
            if (currTreeNode.id === item.id) {
              if (index !== 0) {
                // 节点前移
                This.$refs.tree.remove(currTreeNode)
                This.$refs.tree.insertBefore(currTreeNode, data[index - 1])
                // tree 默认选中
                This.$refs['tree'].setCurrentKey(currTreeNode.id)
                // tree 默认展开
                This.defaultExpandedKeys = [currTreeNode.id]
              }
            } else {
              if (item.children && item.children.length > 0) {
                forData(item.children)
              }
            }
          })
        }
      })
    },
    goDownResource (currTreeNode) {
      let This = this
      downResources(currTreeNode.id).then(res => {
        let treeList = This.appPowersData
        forData(treeList, [])
        function forData (data) {
          for (let i = 0; i < data.length; i++) {
            let item = data[i]
            let index = i
            if (currTreeNode.id === item.id) {
              if (index < data.length - 1) {
                // 节点前移
                let toNode = data[index + 1]
                This.$refs.tree.remove(currTreeNode)
                This.$refs.tree.insertAfter(currTreeNode, toNode)
                // tree 默认选中
                This.$refs['tree'].setCurrentKey(currTreeNode.id)
                // tree 默认展开
                This.defaultExpandedKeys = [currTreeNode.id]
                return false
              }
            } else {
              if (item.children && item.children.length > 0) {
                forData(item.children)
              }
            }
          }
        }
      })
    },
    modifyTreeData: function (ndata, state) {
      // 添加、删除、修改、排序等操作树状菜单的方法都在这里处理
      // state 1 添加； 2 删除； 3 修改； 4 排序
      let This = this
      let treeList = This.appPowersData
      // (2)其他情形
      forData(treeList, ndata)
      function forData (treeList, ndata) {
        treeList.forEach((item, index) => {
          if (ndata.parentId === item.id) {
            // tree 添加节点
            This.$refs['tree'].append(ndata, item)
            // tree 默认选中
            This.$refs['tree'].setCurrentKey(item.id)
            // tree 默认展开
            This.defaultExpandedKeys = [item.id]
          } else {
            if (item.children && item.children.length > 0) {
              forData(item.children, ndata)
            }
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-management {
  padding: 10px;
}
.ctitle {
    padding:14px 20px;
    color: #fff;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background: #252b4a;
    margin-bottom: 10px;
  }
  .treeBox {
    height: 100%;
    overflow-y: auto;
  }
  .tree {
    padding:10px 20px;
  }
  .rolesPowerRight {
    .el-row {
      padding-top: 20px;
      .el-col {
        line-height: 32px;
        word-wrap: break-word;
      }
    }
  }
.dialogTitle {
  text-align: right;
  box-sizing: border-box;
  padding-right: 10px;
  line-height: 32px;
}
.el-tree-node__expand-icon.is-leaf {
  color: red;
}
</style>
