<template>
  <div class="related-system">
    <div class="marginpadding">
      您的工号：{{ workCode }}
    </div>
    <vxe-grid
      border
      auto-resize
      resizable
      show-overflow
      keep-source
      ref="systemGrid"
      row-id
      height="550"
      id="system_grid"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      :toolbar-config="tableToolbar"
      highlight-hover-row
    >

    <template v-slot:toolbar_buttons>
      可以访问的系统见下表
    </template>

    <template v-slot:validateTime_default="{ row }">
      {{ row.validateTime && row.validateTime.slice(0,19) }}
    </template>
    </vxe-grid>
  </div>
</template>

<script>
import { getUserInfo, getUserSystem } from '@/api/authoritySystem'

const columns = [
  {
    field: 'userName',
    title: '账号名',
    minWidth: 100
  },
  {
    field: 'nickname',
    title: '姓名',
    minWidth: 100
  },
  {
    field: 'systemName',
    title: '系统',
    minWidth: 100
  },
  {
    field: 'validateTime',
    title: '数据统计时间',
    minWidth: 100,
    sortable: true,
    slots: {
      default: 'validateTime_default'
    }
  }
]

export default {
  name: 'relatedSystem',
  data () {
    return {
      workCode: '',
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      tableToolbar: {
        custom: true,
        slots: {
          buttons: 'toolbar_buttons'
        }
      },
      columns,
      tableData: []
    }
  },
  async created () {
    await this.getUserWorkCode()
    this.getSystemList()
  },
  methods: {
    async getUserWorkCode () {
      try {
        const res = await getUserInfo({
          username: window.CUR_DATA.user && window.CUR_DATA.user.name
        })
        this.workCode = res.owerWorkCode
      } catch (error) {
        console.log(error);
      }
    },
    async getSystemList () {
      try {
        this.tableLoading = true
        const res = await getUserSystem(this.workCode)
        console.log(res);
        this.tableData = res.filter(item => item.status === true)
        this.tableLoading = false
      } catch (error) {
        this.tableLoading = false
        console.log(error);
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.related-system {
  margin: 20px;
}
</style>
