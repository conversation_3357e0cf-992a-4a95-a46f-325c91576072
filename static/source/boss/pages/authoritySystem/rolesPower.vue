<template>
  <div class="roles-power">
    <el-form @submit.native.prevent :model="searchForm" ref="searchFrom" style="width: 100%" label-suffix=":" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="9">
          <el-form-item label="角色名称">
            <el-input
              v-model="searchForm.name"
              @keyup.native.enter="handleSearch"
              clearable
              suffix-icon="el-icon-search"
              placeholder="请输入关键字">
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4" :offset="2" :style="{display: 'flex', justifyContent: 'space-between'}">
          <el-button
            type="primary"
            style="width: 80px"
            :loading="searchLoading"
            @click="handleSearch"
          >
            查询
          </el-button>
          <el-button
            style="width: 80px"
            @click="resetForm"
          >
            清空
          </el-button>
        </el-col>
      </el-row>
    </el-form>

    <vxe-grid
      border
      auto-resize
      resizable
      keep-source
      show-overflow
      ref="mrpListGrid"
      row-id="id"
      height="525px"
      id="mrp_grid"
      align="center"
      :loading="tableLoading"
      :custom-config="tableCustom"
      :data="tableData"
      :columns="columns"
      highlight-hover-row
    >

    <template v-slot:operation="{ row }">
      <el-button type="text" @click="editRow(row)" size="medium">授权</el-button>
    </template>

    <template #pager>
      <vxe-pager
        :layouts="['Sizes', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'FullJump', 'Total']"
        :border="true"
        :current-page.sync="tablePage.currentPage"
        :page-size.sync="tablePage.pageSize"
        :total="tablePage.total"
        @page-change="handlePageChange">
      </vxe-pager>
    </template>
    </vxe-grid>

    <el-drawer
      :show-close="false"
      :close-on-press-escape="false"
      :wrapperClosable="false"
      :visible.sync="dialog"
      direction="rtl"
      ref="drawer"
      >
      <div slot="title">
        <h5>角色赋权</h5>
        <h6>角色名称：{{this.role.name}}</h6>
      </div>
      <el-card shadow="never" class="demo-drawer__content">
        <el-tree
          :data="appPowersData"
          show-checkbox
          :default-expand-all="true"
          :default-checked-keys="defaultCheckedKeys"
          :check-strictly="true"
          node-key="id"
          ref="tree"
          highlight-current
          :props="defaultProps">
        </el-tree>
      </el-card>
      <el-card shadow="never" class="drawer_footer">
        <el-button type="primary" @click="storeDialog" :loading="loading" style="width: 120px">{{ loading ? '提交中 ...' : '保 存' }}</el-button>
        <el-button @click="cancel" style="width: 120px">取 消</el-button>
      </el-card>
    </el-drawer>
  </div>
</template>

<script>
import { getAppRoles, getAppResources, getRolesAllPower, setRolesPower } from '@/api/authoritySystem'

const columns = [
  {
    field: 'name',
    title: '角色名称',
    minWidth: 120,
    fixed: 'left'
  },
  {
    field: 'creator',
    title: '创建人',
    minWidth: 80
  },
  {
    field: 'createDate',
    title: '创建时间',
    minWidth: 120
  },
  {
    field: 'explain',
    title: '说明',
    minWidth: 120
  },
  {
    title: '操作',
    slots: {
      default: 'operation'
    },
    minWidth: 150,
    fixed: 'right'
  }
]

export default {
  name: 'rolesPower',
  data () {
    return {
      searchForm: {
        appId: 138,
        name: ''
      },
      searchLoading: false,
      tableLoading: false,
      tableCustom: {
        storage: true
      },
      columns,
      tableData: [],
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10
      },
      dialog: false,
      appPowersData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      defaultCheckedKeys: [],
      loading: false,
      role: {}
    }
  },
  created () {
    if (process.env.NODE_ENV === 'development') {
      this.searchForm.appId = 277
    }
    this.getAppRolesList()
    this.goGetAppResources()
  },
  methods: {
    handleSearch () {
      this.tablePage.currentPage = 1
      this.getAppRolesList()
    },
    handlePageChange ({ currentPage, pageSize }) {
      this.tablePage.currentPage = currentPage
      this.tablePage.pageSize = pageSize
      this.getAppRolesList()
    },
    resetForm () {
      this.searchForm.name = ''
      this.getAppRolesList()
    },
    async getAppRolesList () {
      try {
        this.searchLoading = true
        this.tableLoading = true
        const params = { ...this.searchForm }
        const res = await getAppRoles(params)
        this.tableData = res.slice((this.tablePage.currentPage - 1) * this.tablePage.pageSize, this.tablePage.currentPage * this.tablePage.pageSize)
        this.tablePage.total = res.length
        this.searchLoading = false
        this.tableLoading = false
      } catch (error) {
        this.searchLoading = false
        this.tableLoading = false
        console.log(error);
      }
    },
    async goGetAppResources () {
      // 获取应用下面的所有权限
      try {
        let params = {
          appId: this.searchForm.appId
        }
        const res = await getAppResources(params)
        this.appPowersData = res.children
      } catch (error) {
        console.log(error);
      }
    },
    editRow (row) {
      this.goGetRolesAllPower(row.id)
      this.dialog = true
      this.role = row
    },
    async goGetRolesAllPower (id) {
      try {
        const res = await getRolesAllPower(id)
        this.defaultCheckedKeys = res.map(item => Number(item))
        this.$refs.tree.setCheckedKeys(this.defaultCheckedKeys)
      } catch (error) {
        console.log(error);
      }
    },
    async storeDialog () {
      await this.createRolePower()
      this.$refs.drawer.closeDrawer()
    },
    cancel () {
      this.dialog = false;
    },
    async createRolePower () {
      try {
        this.loading = true
        await setRolesPower(this.role.id, this.$refs.tree.getCheckedKeys().join(','))
        this.loading = false
      } catch (error) {
        this.loading = false
        console.log(error);
      }
    }
  }
}
</script>

<style lang="scss">
.roles-power {
  padding: 20px;
}
.demo-drawer__content {
  height: 550px;
  overflow-y: auto;
}
.drawer_footer {
  display: flex;
  justify-content: center;
}
</style>
