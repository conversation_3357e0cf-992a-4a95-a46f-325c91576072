export const supplyStatusOptions = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '采购异常',
    value: '0'
  },
  {
    label: '未采购',
    value: '1'
  },
  {
    label: '未到齐',
    value: '2'
  },
  {
    label: '已到齐',
    value: '3'
  }
]

export const orderArriStatusOptions = [
  {
    label: '全部',
    value: ''
  },
  {
    label: '未到齐',
    value: '0'
  },
  {
    label: '已到齐',
    value: '1'
  }
]
// 页面搜索条件初始化
export function getInitSearchParams () {
  return {
    // customerName: '', // 客户名称
    customerNo: '', // 客户id
    // orderArriStatus: '', // 整单到货状态 0:未到齐 1:已到齐 支持多选 逗号分割，// 两种状态，传空就是全选
    // supplyStatus: '', // 0:采购异常 1:未采购 2:未到齐 3:已到齐
    customerServiceName: '', // 产线客服
    customerServiceSupervisorName: '', // 客服经理
    sapMaterialName: '', // 物料描述
    productGroup: '', // 物料组
    // productGroupId: '', // 物料组id
    deliveryStatus: '', // 交货状态
    customerMaterialNo: '', // 客户物料号
    customerMaterialName: '', // 客户物料描述
    startDate: '', // 销售订单日期开始
    endDate: '', // 物料描述
    brand: '', // 品牌
    // brandId: '', // 品牌id
    sellerName: '', // 销售员
    salesManagerName: '', // 销售经理
    materiel: '', // skuno
    orderTypes: '', // 订单类型,支持多个 逗号分割
    // requestedDeliveryDateStart: '', // 首个交期开始
    // requestedDeliveryDateEnd: '', // 首个交期结束
    voucherNos: '', // 订单号,
    // signStatus: '',
    invoiceStatus: ''
  }
}
