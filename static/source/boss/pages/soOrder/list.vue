<template>
  <div class="app-container backlog-search-container" v-loading="loading.pageLoading">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="130px"
        label-position="right">
        <el-row>
          <el-col :span="7">
            <el-form-item label="客户名称：" prop="customerNo">
              <el-select
                v-model="searchForm.customerNo"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="loading.customerNoLoading"
                clearable
              >
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="订单号：" prop="voucherNos">
              <el-input
                v-model="searchForm.voucherNos"
                placeholder="支持外围、OMS、SAP、客户参考订单号搜索"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="SKU编号：" prop="materiel">
              <el-input
                v-model="searchForm.materiel"
                placeholder="多个可用空格隔开"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="loading.backlogLoading"
              @click="handleFilter"
              >查询</el-button>
          </el-col>
          <el-col :span="7">
            <el-form-item label="品牌：" prop="brand">
              <RemoteBrand
                getLabel
                v-model="searchForm.brand"
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="物料组：" prop="productGroup">
              <el-select
                v-model="searchForm.productGroup"
                filterable
                clearable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteProductGroupIdMethod"
                :loading="loading.productGroupIdLoading">
                <el-option
                  v-for="item in productGroupIdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <!-- <el-select
                v-model="searchForm.productGroup"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in productGroupOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select> -->
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="物料描述：" prop="sapMaterialName">
              <el-input
                v-model.trim="searchForm.sapMaterialName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button>
            <el-button
              type="primary"
              circle
              :icon="caretIcon"
              @click="searchSonditionOpen = !searchSonditionOpen"/>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="交货状态：" prop="deliveryStatus">
              <el-select
                v-model="searchForm.deliveryStatus"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in deliveryStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户物料号：" prop="customerMaterialNo">
              <el-input
                v-model="searchForm.customerMaterialNo"
                placeholder="多个可用空格隔开"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户物料描述：" prop="customerMaterialName">
              <el-input
                v-model.trim="searchForm.customerMaterialName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="订单类型：" prop="orderTypes">
              <el-select
                :value="getQueryOrderType"
                @change="setQueryOrderType"
                placeholder="请选择"
                clearable
                multiple>
                <el-option
                  v-for="item in orderTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客服：" prop="customerServiceName">
              <el-input
                v-model.trim="searchForm.customerServiceName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="销售：" prop="sellerName">
              <el-input
                v-model.trim="searchForm.sellerName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;" />
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="客服主管：" prop="customerServiceSupervisorName">
              <el-input
                v-model.trim="searchForm.customerServiceSupervisorName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="销售经理：" prop="salesManagerName">
              <el-input
                v-model.trim="searchForm.salesManagerName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="开票状态：" prop="invoiceStatus">
              <el-select
                v-model="searchForm.invoiceStatus"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in invoiceStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <!-- <el-col :span="7">
            <el-form-item label="签单状态：" prop="signStatus">
              <el-select
                v-model="searchForm.signStatus"
                clearable
                placeholder="请选择">
                <el-option
                  v-for="item in signStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="14">
              <el-form-item label="订单日期：">
                <el-col :span="11">
                  <el-form-item prop="startDate">
                    <el-date-picker
                      clearable
                      v-model="searchForm.startDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="起"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
                <el-col class="line" :span="2">-</el-col>
                <el-col :span="11">
                  <el-form-item prop="endDate">
                    <el-date-picker
                      clearable
                      v-model="searchForm.endDate"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="止"
                      style="width: 100%;"
                    />
                  </el-form-item>
                </el-col>
              </el-form-item>
          </el-col>
            <el-col :span="7">
            <el-form-item label="订单原因：" prop="reason">
              <el-select
                v-model="searchForm.reason"
                placeholder="请选择"
                clearable
                @change="changeReason()"
              >
              <el-option
                v-for="item in orderReasonList"
                :key="`${item.code}${item.label}${item.parentCode}`"
                :label="`${item.code} ${item.name}`"
                :value="item.code"
              />
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>
        <el-row v-show="searchSonditionOpen">
           <el-col :span="7">
             <el-form-item label="收货联系人姓名：" prop="receiverName">
              <el-input
                v-model="searchForm.receiverName"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
           </el-col>
           <el-col :span="7">
             <el-form-item label="订单备注：" prop="orderNote">
              <el-input
                v-model="searchForm.orderNote"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
           </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="backlog-search-result-container">
      <el-row type="flex" class="result-title" justify="space-between">
        <el-col :span="16">销售订单明细表</el-col>
        <el-col :span="8" style="text-align:right;">
          <el-button
            v-if="!getButtonAuth('销售跟单', '不允许导出') && !showColums"
            type="primary"
            icon="el-icon-download"
            @click="exportExcel"
            :loading="loading.exportLoading"
            >导出</el-button>
          <el-button icon="el-icon-s-unfold" @click="showMoreSearch" circle></el-button>
        </el-col>
      </el-row>
      <zkh-table
        :loading="loading.backlogLoading"
        v-loading="loading.initConfigLoading"
        :data="backlogOrderList"
        :columns="newColumns"
        :headerDragend="headerDragend"
        :height="600"
        :columnEditable="true"
        :visible.sync="columnsSetVisible"
        :columnsConfig="columnsConfigDeepCopy"
        :defaultColumnsConfig="defaultColumnsConfig"
        @columnSet="columnSet"
      ></zkh-table>
    </div>
    <pagination
      v-show="canShowTotal > 0"
      :total="canShowTotal"
      align="right"
      :page.sync="listQueryInfo.pageNo"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBacklogOrderList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import getColumns from './columns.js'
import RemoteBrand from '@/components/SearchFields/brand'
import {
  createdConfigByColumns,
  formatByConfig
} from '@/components/ZkhTable/columnConfigTransformation.js'
import {
  goodsCustomerListSearch,
  goodsGroupListSearch
} from '@/api/insteadOrder.js'
import { getButtonAuth } from '@/utils/auth'

import {
  getSoOrders,
  fetchConfigFromServer,
  saveConfig2Server,
  exportSoOrders
  // getProductGroupOptions
} from '@/api/soOrder.js'
import {
  orderArriStatusOptions,
  supplyStatusOptions,
  getInitSearchParams
} from './constants.js'

export default {
  name: 'soOrderList',
  data () {
    return {
      orderArriStatusOptions, // 整单到货状态 下拉选项
      supplyStatusOptions, // 单笔供货状态 下拉选项
      // orderTypeOptions: [], // 订单类型下拉选项
      customerNoOptions: [],
      brandIdOptions: [],
      productGroupIdOptions: [],
      // productGroupOptions: [],
      invoiceStatusOptions: [
        { label: '未开票', value: 0 },
        { label: '部分开票', value: 1 },
        { label: '全部开票', value: 2 }
      ],
      deliveryStatusOptions: [
        { label: '未清', value: 0 },
        { label: '已清', value: 1 }
      ],
      // signStatusOptions: [
      //   { label: '否', value: 0 },
      //   { label: '是', value: 1 }
      // ],
      searchForm: {}, // 绑定搜索参数
      columnsSetVisible: false, // 列设置弹框显隐
      defaultColumnsConfig: [],
      listQueryInfo: {
        pageNo: 1,
        pageSize: 20
      },
      rules: {},
      backlogOrderList: [],
      total: 0,
      searchSonditionOpen: false,
      columns: [],
      columnsConfig: [],
      columnsConfigDeepCopy: [],
      loading: {
        initConfigLoading: false,
        pageLoading: false,
        customerNoLoading: false,
        brandIdLoading: false,
        productGroupIdLoading: false,
        backlogLoading: false, // 查询按钮loading
        exportLoading: false // 导出按钮 loading
      },
      tableServerConfig: null
    }
  },
  components: {
    Pagination,
    RemoteBrand
  },
  async created () {
    this.loading.initConfigLoading = true
    try {
      let tableConfigResponse = await fetchConfigFromServer()
      console.log('tableConfigResponse')
      console.log(tableConfigResponse)
      if (tableConfigResponse && tableConfigResponse.length > 1) {
        this.tableServerConfig = tableConfigResponse
      } else {
        console.log('服务端获取配置失败！')
      }
      setTimeout(() => {
        this.loading.initConfigLoading = false
      }, 800)
    } catch (err) {
      console.log(err)
      this.$message.error('服务端获取配置失败！')
    }
    this.initColumns()

    this.searchForm = getInitSearchParams()
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
  },
  computed: {
    dictList () {
      return this.$store.state.orderCommon.dictList || {}
    },
    orderReasonList() {
      return this.$store.state.orderCommon.dictList.orderReason
    },
    orderTypeOptions () {
      return this.dictList && this.dictList['soCategory'] ? this.dictList['soCategory'].map(item => {
        return { label: item.name, value: item.code }
      }) : []
    },
    canShowTotal () {
      return this.total
    },
    caretIcon () {
      if (this.searchSonditionOpen) {
        return 'el-icon-caret-top'
      } else {
        return 'el-icon-caret-bottom'
      }
    },
    getQueryOrderType () {
      if (!this.searchForm.orderTypes) {
        return []
      } else {
        return this.searchForm.orderTypes.split(',')
      }
    },
    showColums () {
      return !!~this.$store.state.userRole.indexOf('boss-产品营销');
    },
    newColumns () {
      this.showColumsVisible(this.columns)
      return this.columns
    }
  },
  methods: {
    changeReason() {
      this.$forceUpdate()
    },
    getButtonAuth,
    headerDragend (newWidth, oldWidth, column, event) {
      const label = column.label
      console.log(`修改: ${label} width ${oldWidth} -> ${newWidth}`)
      this.columnsConfig.forEach(config => {
        if (config.label === label) {
          config.minWidth = newWidth + 'px'
        }
      })
      this.defaultColumnsConfig = JSON.parse(JSON.stringify(this.columnsConfig))
      console.log('保存table配置到服务器')
      saveConfig2Server(this.defaultColumnsConfig)
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '保存配置失败！')
        })
    },
    columnSet (newConfig) {
      if (newConfig) {
        this.columnsConfig = newConfig
        this.columns = formatByConfig(this.columnsConfig, getColumns())
      }
      this.columnsSetVisible = false
      console.log('保存列设置')
      console.log(newConfig)
      saveConfig2Server(newConfig)
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '保存配置失败！')
        })
    },
    showColumsVisible (columns) {
      if (this.showColums) {
        columns.forEach(item => {
            const flag = ['orderNote', 'orderContactName', 'orderContactPhone', 'sapDemandDepartment', 'receiverContact', 'receiverName', 'receiverPhone', 'receivingInvoiceContact', 'receivingInvoiceName', 'receivingInvoicePhone'].includes(item.prop)
            if (flag) {
              item.visible = false;
              item.visibleSwitch = false;
            }
          })
      }
    },
    showMoreSearch () {
      this.showColumsVisible(this.columnsConfig)
      this.columnsConfigDeepCopy = JSON.parse(
        JSON.stringify(this.columnsConfig)
      )

      this.columnsSetVisible = true
    },
    changeKeys (serverConfig, defaultConfig) {
      let changed = false
      if (serverConfig.length !== defaultConfig.length) changed = true
      // 过滤本地不存在的属性
      serverConfig = serverConfig.filter(sConf => {
        return defaultConfig.some(config => config.label === sConf.label)
      })
      defaultConfig.forEach((config, index) => {
        if (!serverConfig.some(sConf => sConf.label === config.label)) {
          console.log('服务端缺少属性, changed = true', config.label)
          changed = true
          serverConfig.splice(index, 0, config)
        }
      })
      // 属性字段对应取本地值
      serverConfig.forEach(child => {
        let dConf = defaultConfig.filter(d => d.label === child.label)
        if (dConf && dConf[0] && child.prop !== dConf[0].prop) {
          console.log('服务端缺少prop不一致, changed = true')
          child.prop = dConf[0].prop
          changed = true
        }
      })
      return {
        changed,
        serverConfig
      }
    },
    checkColumnsConfig (defaultConfig, serverConfig) {
      // 新旧配置更改，配置属性、顺序使用服务端属性、字段删除新增使用本地属性
      defaultConfig = JSON.parse(JSON.stringify(defaultConfig))
      serverConfig = JSON.parse(JSON.stringify(serverConfig))
      let changed = false
      try {
        let result = this.changeKeys(serverConfig, defaultConfig)
        changed = result.changed
        serverConfig = result.serverConfig
      } catch (err) {
        console.error(err)
        serverConfig = defaultConfig
        changed = true
      }
      return {
        config: serverConfig,
        changed
      }
    },
    initColumns () {
      try {
        // if (Math.random() > 0.000001) throw new Error('123')
        const defaultColumns = getColumns()
        this.defaultColumnsConfig = createdConfigByColumns(defaultColumns)
        this.setDefaultColFromCache(getColumns())
        if (this.tableServerConfig) {
          const checkResult = this.checkColumnsConfig(
            this.defaultColumnsConfig,
            this.tableServerConfig
          )
          let columnsConfig = checkResult.config
          if (checkResult.changed) {
            console.log('对比本地不一致, 上传table配置至服务器！')
            saveConfig2Server(columnsConfig)
          }
          console.log('从服务端获取table配置并应用！')
          this.columnsConfig = columnsConfig
          this.columns = formatByConfig(this.columnsConfig, getColumns())
        } else {
          console.log('上传table配置至服务器！')
          saveConfig2Server(this.defaultColumnsConfig)
        }
      } catch (error) {
        this.setDefaultColFromCache(getColumns())
      }
    },
    setDefaultColFromCache (columns) {
      const config = createdConfigByColumns(columns)
      this.columnsConfig = config
      this.columns = columns
    },
    setQueryOrderType (value) {
      this.searchForm.orderTypes = value.join(',')
    },
    getBacklogOrderList () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.loading.backlogLoading = true

          let param = { ...this.searchForm }
          param.pageNo = this.listQueryInfo.pageNo
          param.pageSize = this.listQueryInfo.pageSize
          this.backlogOrderList = []
          try {
            param.voucherNos = param.voucherNos.trim().split(/\s+/).filter(e => e)
          } catch (err) {
            param.voucherNos = []
          }
          try {
            param.materiel = param.materiel.split(/\s+/).filter(e => e)
          } catch (err) {
            param.materiel = []
          }
          try {
            param.customerMaterialNo = param.customerMaterialNo.split(/\s+/).filter(e => e)
          } catch (err) {
            param.customerMaterialNo = []
          }
          try {
            param.orderTypes = param.orderTypes.split(',').filter(e => e)
          } catch (err) {
            param.orderTypes = []
          }
          console.log(param)
          getSoOrders(param)
            .then(res => {
              this.loading.backlogLoading = false
              if (res.code === 200) {
                this.total = res.totalCount
                if (res.data) {
                  this.backlogOrderList = res.data
                }
              } else {
                this.$message.error(res.msg)
              }
            })
            .catch(() => {
              this.loading.backlogLoading = false
            })
        }
      })
    },
    // 导出按钮
    exportExcel () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.loading.exportLoading = true
          let param = { ...this.searchForm }
          try {
            param.voucherNos = param.voucherNos.trim().split(/\s+/).filter(e => e)
          } catch (err) {
            param.voucherNos = []
          }
          try {
            param.materiel = param.materiel.split(/\s+/).filter(e => e)
          } catch (err) {
            param.materiel = []
          }
          try {
            param.customerMaterialNo = param.customerMaterialNo.split(/\s+/).filter(e => e)
          } catch (err) {
            param.customerMaterialNo = []
          }
          try {
            param.orderTypes = param.orderTypes.split(',').filter(e => e)
          } catch (err) {
            param.orderTypes = []
          }
          exportSoOrders(param)
            .then(res => {
              if (res && res.code === 200) {
                this.$message.success(res.msg || res.data || '操作成功！')
              } else {
                this.$message.error(res.msg || res.message || '导出失败!')
              }
            })
            .catch(err => {
              this.$message.error(err.msg || err.message || '导出失败！')
            })
            .finally(res => {
              this.loading.exportLoading = false
            })
        }
      })
    },
    // 查询按钮
    handleFilter () {
      this.listQueryInfo.pageNo = 1
      console.log(this.searchForm)
      this.getBacklogOrderList()
    },
    // 重置按钮
    handleReset () {
      this.$refs['searchForm'].resetFields()
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading.customerNoLoading = true
        goodsCustomerListSearch({
          customer_name: key
        }).then(res => {
          this.loading.customerNoLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.customerNoOptions = res.data.contents.map(item => {
                return {
                  value: item.customerNo,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    },
    // 远程查找物料组
    remoteProductGroupIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.loading.productGroupIdLoading = true
        goodsGroupListSearch({
          group_name: key
        }).then(res => {
          this.loading.productGroupIdLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.productGroupIdOptions = res.data.contents.map(item => {
                return {
                  value: item.productGroupId,
                  label: item.productGroupName
                }
              })
            } else {
              this.productGroupIdOptions = []
            }
          } else {
            this.productGroupIdOptions = []
          }
        })
      } else {
        this.productGroupIdOptions = []
      }
    }
  }
}
</script>

<style lang="scss" scope>
.backlog-search-container .el-loading-mask{
  background-color: rgba(255, 255, 255, 1);
}
</style>
<style lang="scss">
.backlog-search-container {
  .filter-container {
    padding-top: 18px;
    background-color: #f4f4f4;
  }
  .tag-container {
    padding-left: 10px;
    padding-bottom: 5px;
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .customer-tips {
    background: #ffffff;
    margin-top: 15px;
    margin-bottom: 15px;
    padding-left: 0;
    font-size: 14px;
    span {
      text-align: center;
      color: red;
      font-size: 14px;
    }
  }
  .backlog-search-result-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin: 10px 0;
    & > {
      .result-title {
        height: 50px;
        padding: 0 10px 0 20px;
        line-height: 50px;
        font-size: 16px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}

</style>
