import {
  orderLabelFilter
} from '@/filters/index.js'
import { deepClone } from '@/utils/index.js'

const columns = [
  { label: '销售组织', prop: 'salesOrganization', type: 'default', visible: true, minWidth: '120px' },
  { label: '工厂名称', prop: 'factoryName', type: 'default', visible: true, minWidth: '120px' },
  { label: '分销渠道', prop: 'distributionChannelName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单类型', prop: 'orderType', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售订单号', prop: 'sapOrderNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售订单行号', prop: 'sapItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '行项目类别', prop: 'itemType', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户编码', prop: 'customerNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户名称', prop: 'customerName', type: 'default', visible: true, minWidth: '120px' },
  { label: 'SKU', prop: 'materiel', type: 'default', visible: true, minWidth: '120px' },
  { label: '物料描述', prop: 'sapMaterialName', type: 'default', visible: true, minWidth: '120px' },
  { label: '规格型号', prop: 'snapshotSpecificationModel', type: 'default', visible: true, minWidth: '120px' },
  { label: '物料组', prop: 'productGroupName', type: 'default', visible: true, minWidth: '120px' },
  { label: '商品经理', prop: 'productOwner', type: 'default', visible: true, minWidth: '120px' },
  { label: '品牌', prop: 'brand', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单备注', prop: 'orderNote', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单行备注', prop: 'remark', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单日期', prop: 'sapAddDate', type: 'default', visible: true, minWidth: '120px' },
  { label: '创建者', prop: 'creator', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单原因', prop: 'reason', type: 'default', visible: true, minWidth: '120px' },
  { label: '外围系统来源订单号', prop: 'orderNo', type: 'default', visible: true, minWidth: '160px' },
  { label: '外围系统来源', prop: 'orderSource', type: 'default', visible: true, minWidth: '130px' },
  { label: '所属集团编码', prop: 'companyCode', type: 'default', visible: true, minWidth: '120px' },
  { label: '所属集团名称', prop: 'companyName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单联系人', prop: 'orderContact', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单联系人姓名', prop: 'orderContactName', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单联系人电话', prop: 'orderContactPhone', type: 'phone', visible: true, minWidth: '120px' },
  { label: '订单省', prop: 'orderContactProvince', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单市', prop: 'orderContactCity', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单区', prop: 'orderContactDistrict', type: 'default', visible: true, minWidth: '120px' },
  { label: '需求部门', prop: 'sapDemandDepartment', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货联系人', prop: 'receiverContact', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货联系人姓名', prop: 'receiverName', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货联系人电话', prop: 'receiverPhone', type: 'phone', visible: true, minWidth: '120px' },
  { label: '收货省', prop: 'receiverProvince', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货市', prop: 'receiverCity', type: 'default', visible: true, minWidth: '120px' },
  { label: '收货区', prop: 'receiverDistrict', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票联系人', prop: 'receivingInvoiceContact', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票联系人姓名', prop: 'receivingInvoiceName', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票联系人电话', prop: 'receivingInvoicePhone', type: 'phone', visible: true, minWidth: '120px' },
  { label: '收票省', prop: 'receivingInvoiceProvince', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票市', prop: 'receivingInvoiceCity', type: 'default', visible: true, minWidth: '120px' },
  { label: '收票区', prop: 'receivingInvoiceDistrict', type: 'default', visible: true, minWidth: '120px' },
  { label: '含税未税标识文本', prop: 'conditionType', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单数量', prop: 'quantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售单位', prop: 'basicUnitName', type: 'default', visible: true, minWidth: '120px' },
  { label: '箱规', prop: 'productPackage', type: 'default', visible: true, minWidth: '120px' },
  { label: '含税单价', prop: 'taxIncludedUnitPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '未税单价', prop: 'untaxedUnitPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '含税总价', prop: 'taxTotalPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '未税总价', prop: 'totalUntaxedPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '税额', prop: 'tax', type: 'default', visible: true, minWidth: '120px' },
  { label: '货币', prop: 'currency', type: 'default', visible: true, minWidth: '120px' },
  { label: '订单描述', prop: 'headerText', type: 'default', visible: true, minWidth: '120px' },
  { label: '首次预计送达日期', prop: 'deliveryDate', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户期望送达日期', prop: 'customerReferenceDate', type: 'default', visible: true, minWidth: '120px' },
  // { label: 'Lead Time', prop: 'plannedDeliveryDays', type: 'default', visible: true, minWidth: '150px' },
  { label: '交货状态', prop: 'deliveryStatus', type: 'default', visible: true, minWidth: '120px' },
  { label: '已交货数量', prop: 'clearedQty', type: 'default', visible: true, minWidth: '120px' },
  { label: '已交货金额', prop: 'deliveryPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '未交货数量', prop: 'nonClearedQty', type: 'default', visible: true, minWidth: '120px' },
  { label: '未交货金额', prop: 'nonDeliveryPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '开票状态', prop: 'invoiceStatus', type: 'default', visible: true, minWidth: '120px' },
  { label: '已开票数量', prop: 'invoicedNum', type: 'default', visible: true, minWidth: '120px' },
  { label: '已开票金额', prop: 'invoicedPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '未开票数量', prop: 'nonInvoicedNum', type: 'default', visible: true, minWidth: '120px' },
  { label: '未开票金额', prop: 'nonInvoicedPrice', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服', prop: 'customerServiceName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服主管', prop: 'customerServiceSupervisorName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客服经理', prop: 'customerServiceManagerName', type: 'default', visible: true, minWidth: '120px' },
  // { label: '客服总监', prop: 'customerServiceDirectorName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售', prop: 'sellerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售经理', prop: 'salesManagerName', type: 'default', visible: true, minWidth: '120px' },
  { label: '销售总监', prop: 'salesDirectorName', type: 'default', visible: true, minWidth: '120px' },
  { label: '参考销售订单号', prop: 'referenceOrderNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '参考订单行项目号', prop: 'referenceOrderItem', type: 'default', visible: true, minWidth: '130px' },
  { label: '客户订单号', prop: 'customerReferenceNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户订单序列号', prop: 'customerItemNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料号', prop: 'customerMaterialNo', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料描述', prop: 'customerMaterialName', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料数量', prop: 'customerMaterialQuantity', type: 'default', visible: true, minWidth: '120px' },
  { label: '客户物料单位', prop: 'customerMaterialUnit', type: 'default', visible: true, minWidth: '120px' },
  { label: '库存地点', prop: 'position', type: 'default', visible: true, minWidth: '120px' },
  // { label: '库存地点名称', prop: 'positionName', type: 'default', visible: true, minWidth: '120px' },
  { label: '当前库存', prop: 'currentNum', type: 'default', visible: true, minWidth: '120px' },
  { label: '仓库修改原因', prop: 'positionModifyReason', type: 'default', visible: true, minWidth: '120px' },
  { label: '仓库修改详情', prop: 'positionModifyDetail', type: 'default', visible: true, minWidth: '120px' },
  { label: '责任人', prop: 'responsiblePerson', type: 'default', visible: true, minWidth: '120px' },
  { label: 'VPI物料', prop: 'vpiMateriel', type: 'default', visible: true, minWidth: '120px' },
  { label: '成品油', prop: 'ifOil', type: 'default', visible: true, minWidth: '120px' },
  { label: 'EVM运营', prop: 'evmOperationSpecialist', type: 'default', visible: true, minWidth: '120px' },
  { label: '叫料客户', prop: 'bidCustomer', type: 'default', visible: true, minWidth: '120px' },
  { label: '后补订单', prop: 'backupOrder', type: 'default', visible: true, minWidth: '120px' },
  { label: '自动发货', prop: 'autoDelivery', type: 'default', visible: true, minWidth: '120px' },
  { label: '自动分批', prop: 'autoBatching', type: 'default', visible: true, minWidth: '120px' },
  { label: '自动开票', prop: 'autoBilling', type: 'default', visible: true, minWidth: '120px' },
  { label: '自主或代理下单标签', prop: 'orderLabel', type: 'default', visible: true, minWidth: '150px', labelFormat (val) { return orderLabelFilter(val) } },
  { label: '退货原因', prop: 'returnReasonType', type: 'default', visible: true, minWidth: '120px' },
  { label: '退货详情', prop: 'returnReason', type: 'default', visible: true, minWidth: '120px' }
]

// [
//   {
//     label: '销售订单信息',
//     prop: 'SOInfo',
//     visible: true,
//     type: 'parent',
//     children: SOcolumns
//   },
//   {
//     label: '采购信息',
//     prop: 'POInfo',
//     visible: true,
//     type: 'parent',
//     children: POcolumns
//   }
// ]

export default function getColumns () {
  return deepClone(columns)
}
