<template>
  <div class="order-sale-log-detail">
    <div class="search-part">
      <el-form ref="searchForm" :rules="rules"
        :model="searchForm" label-width="120px" label-position="right">
        <el-row>
          <el-col :span="14">
            <el-form-item label="变更时间：">
              <el-col :span="11">
                <el-form-item style="margin-bottom:0px" size="small" prop="startTime">
                  <el-date-picker
                    v-model="searchForm.startTime"
                    type="date" clearable value-format="timestamp"
                    placeholder="开始时间" style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2" style="text-align:center">-</el-col>
              <el-col :span="11">
                <el-form-item style="margin-bottom:0px" size="small" prop="endTime">
                  <el-date-picker
                    v-model="searchForm.endTime"
                    value-format="timestamp" clearable type="date"
                    placeholder="结束时间" style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="单据类型：" prop="orderType">
              <el-select
                v-model="searchForm.orderType" style="width: 100%;">
                <el-option label="销售订单" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="订单号：" prop="orderNos">
              <el-input
                v-model="searchForm.orderNos"
                placeholder="OMS/SAP点单号均支持搜索，最多同时支持10个单号，以空格分隔" clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户名称：" prop="customerNo">
              <Client width="100%" v-model="searchForm.customerNo" />
            </el-form-item>
          </el-col>
          <el-col :span="14">
            <el-form-item label="商品编码：" prop="skuNos">
              <el-input
                v-model="searchForm.skuNos"
                placeholder="最多同时支持30个商品编码，以空格分隔"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客服人员：" prop="customerServiceName">
              <el-input clearable v-model="searchForm.customerServiceName" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="变更人：" prop="changeName">
              <el-input clearable v-model="searchForm.changeName" placeholder="请输入" />
              <!-- <Customer width="100%" :getLabel="true" v-model="searchForm.changeName" /> -->
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6" :offset="10" style="text-align:center;margin-bottom: 20px">
            <el-button :loading="loading.searchLoading" icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
            <el-button icon="el-icon-refresh-left" @click="handleReset">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="table-list">
      <el-table
        border
        v-loading="loading.searchLoading"
        :data="listData"
        :span-method="arraySpanMethod"
        >
        <el-table-column
          v-for="item in columnList"
          :key="item.prop" :label="item.label"
          :prop="item.prop" align="center" :width="item.width" show-overflow-tooltip />
        <el-table-column label="变更前" prop="beforeData" width="140" align="center" >
          <template slot-scope="scope">
            <el-popover
              v-if="hideLongColumn(scope.row.beforeData, 10)"
              placement="top" width="200" trigger="hover"
              :content="scope.row.beforeData">
              <div slot="reference">{{shorten(scope.row.beforeData, 10)}}</div>
            </el-popover>
            <span v-else>
              {{ scope.row.beforeData }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="变更后" prop="afterData" width="140" align="center" >
          <template slot-scope="scope">
            <el-popover
              v-if="hideLongColumn(scope.row.afterData, 10)"
              placement="top" width="200" trigger="hover"
              :content="scope.row.afterData">
              <div slot="reference">{{shorten(scope.row.afterData, 10)}}</div>
            </el-popover>
            <span v-else>
              {{ scope.row.afterData }}
            </span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="listQueryInfo.total > 0"
        :total="listQueryInfo.total"
        align="right"
        :page.sync="listQueryInfo.current"
        :pageSizes="[10,20,30,50,100]"
        :limit.sync="listQueryInfo.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList"
      />
    </div>
  </div>
</template>
<script>
import { getOrderSaleLogDetail } from '@/api/logDetail'
// import Customer from '@/components/SearchFields/customer'
import Client from '@/components/SearchFields/client'
import Pagination from '@/components/Pagination'
const columnList = [
  { label: 'SAP单号', prop: 'sapOrderNo', width: '120px' },
  { label: 'OMS单号', prop: 'omsOrderNo', width: '160px' },
  { label: '客户名称', prop: 'customerName', width: '120px' },
  { label: '客服', prop: 'customerServiceName' },
  { label: '销售', prop: 'saleName' },
  { label: 'OMS行号', prop: 'omsItemNo' },
  { label: '商品编码', prop: 'skuNo' },
  { label: '商品名称', prop: 'skuName', width: '260px' },
  { label: '变更时间', prop: 'modifyTime', width: '160px' },
  { label: '变更人', prop: 'userName', width: '200px' },
  { label: '触发操作', prop: 'operName', width: '200px' },
  { label: '变更字段', prop: 'columnName', width: '160px' }
]
export default {
  name: 'orderSaleLogDetail',
  components: {
    Pagination,
    // Customer,
    Client
  },
  data () {
    return {
      listData: [],
      rules: {},
      searchForm: {
        orderType: '0',
        orderNos: '',
        skuNos: '',
        startTime: '',
        endTime: '',
        changeName: '',
        customerServiceName: '',
        customerNo: ''
      },
      loading: {
        searchLoading: false
      },
      spanArr: [],
      position: '',
      listQueryInfo: {
        total: 0,
        current: 1,
        pageSize: 20
      },
      columnList
    }
  },
  mounted () {
    this.initDate()
  },
  methods: {
    initDate () {
      const zeroDate = new Date().setHours(0, 0, 0, 0)
      console.log(zeroDate)
      this.searchForm.startTime = zeroDate - 1000 * 60 * 60 * 24 * 365
      this.searchForm.endTime = zeroDate
    },
    shorten (str, end = 10) {
      return str.slice(0, end)
    },
    hideLongColumn (term, length = 10) {
      if (term && term.length > length) {
        return true
      }
      return false
    },
    // 处理表格数据，得到需合并的规则
    getSpanArr (data, idx, prop) {
      this.spanArr[idx] = []
      this.position = 0
      data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr[idx].push(1)
          this.position = 0
        } else {
          if (data[index][prop] === data[index - 1][prop] &&
            data[index].identityCode === data[index - 1].identityCode
          ) {
            // 有相同项
            this.spanArr[idx][this.position] += 1
            this.spanArr[idx].push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr[idx].push(1)
            this.position = index
          }
        }
      })
    },
    // 表格单元格合并
    arraySpanMethod ({ row, column, rowIndex, columnIndex }) {
      if ([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].indexOf(columnIndex) !== -1) {
        const _row = this.spanArr[0][rowIndex]
        const _col = _row > 0 ? 1 : 0
        return { rowspan: _row, colspan: _col }
      }
    },
    formatListData () {
      const result = []
      this.listData.forEach(data => {
        const identityCode = Math.random()
        if (data.soModifyColumnBOS && data.soModifyColumnBOS.length) {
          data.soModifyColumnBOS = data.soModifyColumnBOS.map(column => ({
            ...column, ...data, identityCode
          }))
          result.push(...data.soModifyColumnBOS)
        }
      })
      this.listData = result
    },
    handleSearch () {
      this.listQueryInfo.current = 1
      this.getList()
    },
    validateParams () {
      // 校验
      let result = true
      if (!this.searchForm.orderNos && !this.searchForm.skuNos) {
        this.$message.error('订单号或商品编码至少填写一项！')
        return false
      }
      if (!this.searchForm.startTime || !this.searchForm.endTime) {
        this.$message.error('变更起止时间必填！')
        return false
      }
      try {
        const timeDiff = Math.abs(this.searchForm.endTime - this.searchForm.startTime)
        const dayDiff = Math.floor(timeDiff / 1000 / 60 / 60 / 24)
        console.log(timeDiff, dayDiff)
        if (dayDiff > 365) {
          this.$message.error('变更时间查询范围最多为365天！')
          return false
        }
      } catch (err) { console.log(err) }
      try {
        const tmp = this.searchForm.orderNos.split(/\s+|,|，|;|；/).filter(e => e)
        if (tmp.length > 10) {
          this.$message.error('最多输入10个订单号')
          return false
        }
      } catch (err) { console.log(err) }
      try {
        const tmp = this.searchForm.skuNos.split(/\s+|,|，|;|；/).filter(e => e)
        if (tmp.length > 30) {
          this.$message.error('最多输入30个商品编码')
          return false
        }
      } catch (err) { console.log(err) }
      return result
    },
    formatParams () {
      let result = { ...this.searchForm }
      try {
        result.orderNos = result.orderNos.split(/\s+|,|，|;|；/).filter(e => e).join(',')
      } catch (err) { console.log(err) }
      try {
        result.skuNos = result.skuNos.split(/\s+|,|，|;|；/).filter(e => e).join(',')
      } catch (err) { console.log(err) }
      try {
        if (result.startTime) {
          result.startTime = Number(String(result.startTime).slice(0, -3))
        }
      } catch (err) { console.log(err) }
      try {
        if (result.endTime) {
          result.endTime += 1000 * 60 * 60 * 23 + 1000 * 60 * 59 + 1000 * 59
          result.endTime = Number(String(result.endTime).slice(0, -3))
        }
      } catch (err) { console.log(err) }
      if (!result.startTime) {
        result.startTime = 0
      }
      if (!result.endTime) {
        result.endTime = 0
      }
      return result
    },
    getList () {
      if (!this.validateParams()) return
      this.loading.searchLoading = true
      const data = {
        ...this.formatParams(),
        current: this.listQueryInfo.current,
        pageSize: this.listQueryInfo.pageSize
      }
      getOrderSaleLogDetail(data)
        .then(res => {
          if (res.code === 200 && res.data) {
            this.listData = res.data
            this.formatListData()
            this.listQueryInfo.total = res.totalCount
            this.getSpanArr(this.listData, 0, 'sapOrderNo')
          } else {
            this.$message.error(res.msg || res.message || '操作失败！')
          }
        })
        .catch(err => {
          console.log(err)
          this.$message.error(err.msg || err.message || '操作失败！')
        })
        .finally(() => {
          this.loading.searchLoading = false
        })
    },
    handleReset () {
      this.$refs.searchForm.resetFields()
      this.initDate()
    }
  }
}
</script>

<style scoped>
.order-sale-log-detail{
  padding-top: 10px;
  width: 95%;
  margin: auto;
}
</style>
