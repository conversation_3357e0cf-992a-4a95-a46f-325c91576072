<template>
    <div class="app-container order-list-components">
      <div class="filter-container">
        <el-form
          ref="searchForm"
          :rules="rules"
          :model="searchForm"
          label-width="120px"
          label-suffix=":"
        >
          <el-row>
            <el-col :span="8">
              <el-form-item label="任务类型" prop="taskType">
                <el-select
                  v-model="searchForm.taskType"
                  filterable
                  clearable
                  style="width:100%"
                >
                  <el-option
                    v-for="item in taskTypeList"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="关键字" prop="entryId">
                <el-input
                  v-model="searchForm.entryId"
                  placeholder="关键字"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="任务状态" prop="taskStatus">
                <el-select
                  v-model="searchForm.taskStatus"
                  filterable
                  clearable
                  style="width:100%"
                >
                  <el-option
                    v-for="item in taskStatusList"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="searchForm.createTime"
                  type="daterange"
                  value-format="yyyy-MM-dd"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>

            </el-col>
            <el-col :span="16">
              <el-form-item class="min-width">
                <el-button type="primary" @click="search('searchForm')">查询</el-button>
                <el-button type="default" @click="reset('searchForm')">重置</el-button>
                <el-button type="danger" :loading="batchLoading" :disabled="!retryList.length" @click="batchRetry">批量重试</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-table
        style="width: 100%"
        :data="taskList"
        height="450"
        border
        v-loading="loading"
        @selection-change="handleSelectionChange"
        :header-cell-style="{ 'text-align': 'center' }"
      >
      <el-table-column
        fixed="left"
        width="50"
        type="selection"
        align="center"
      />
      <el-table-column
          fixed="left"
          width="80"
          label="操作"
          align="center"
        >
        <template slot-scope="{row}">
          <el-button type="text" @click="retryTask(row.id)">重试</el-button>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        width="160"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="taskType"
        label="任务类型"
        width="120"
        align="center"
      >
      <template slot-scope="{row}">
        {{

        (
          (dictList.taskType || []).find(
            (item) => item.value === row.taskType
          ) || {}
        ).name
    }}
      </template>

    </el-table-column>
        <el-table-column
          prop="entityId"
          label="关键字"
          align="center"
          width="80"
        >
        </el-table-column>

        <el-table-column
          prop="extraInfo"
          label="请求报文"
          align="center"
          width="880"
        >
        </el-table-column>
        <el-table-column
          width="80"
          prop="status"
          label="任务状态"
          align="center"
        >
          <template slot-scope="{row}">
            {{

            (
              (dictList.taskStatus || []).find(
                (item) => item.value == row.status
              ) || {}
            ).name
        }}
          </template>
        </el-table-column>
        <el-table-column
          prop="failMsg"
          label="任务描述"
          align="center"
          width="480"
        >
        </el-table-column>
      </el-table>
      <Pagination
        v-show="total > 0"
        :total="total"
        align="right"
        :page.sync="listQueryInfo.pageNo"
        :limit.sync="listQueryInfo.pageSize"
        layout="total, sizes,prev, pager, next, jumper"
        @pagination="getTaskRecordsList"
      />
    </div>
</template>

<script>
import { getTaskRecordsList, retryTask, batchRetryTask } from '@/api/mm'
import Pagination from '@/components/Pagination'
import { mapState } from 'vuex'

export default {
  props: {},
  data() {
    return {
      batchLoading: false,
      retryList: [],
      searchForm: {
        // 任务类型
        taskType: 'WMS_POST_PO_IN',
        entryId: '',
        // 任务状态
        taskStatus: '',
        // 日志状态
        createTime: '',
        createTimeBegin: '',
        createTimeEnd: ''

      },
      rules: {
        orderNo: [
          {
            required: true,
            message: '请输入购单号 或 SAP订单号',
            trigger: 'blur'
          }
        ]
      },
      listQueryInfo: {
        pageNo: 1,
        pageSize: 20
      },
      total: 0,
      // 货权转移列表
      taskList: [],
      // 表格数据加载
      loading: false
      // 物料组
    }
  },
  computed: {
    ...mapState({
      dictList: (state) => state.orderPurchase.dictList
    }),
    copySearchForm() {
      this.clean(this.searchForm)
      let processdSearchForm = {}
      for (var k in this.searchForm) {
        if (k === 'createTime') {
          processdSearchForm.createTimeBegin = this.searchForm.createTime[0]
          processdSearchForm.createTimeEnd = this.searchForm.createTime[1]
        } else {
          processdSearchForm[k] = this.searchForm[k]
        }
      }
      return processdSearchForm
    },
    taskTypeList() {
      return this.dictList['taskType']
    },
    taskStatusList() {
      return this.dictList['taskStatus']
    }
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderPurchase/queryDictList')
    }
    // this.getTaskRecordsList()
  },
  // taskType 任务类型 taskStatus 状态
  mounted() {},
  watch: {},
  methods: {
    handleSelectionChange(val) {
      this.retryList = val;
    },
    batchRetry () {
      const data = this.retryList.map(row => row.id);
      this.batchLoading = true
      batchRetryTask(data).then(res => {
        if (res) {
          res = res.replace(/;/gi, '<br />')
          res = `<div style="overflow:auto;">${res}</div>`
          return this.$alert(res, '操作结果', {
            confirmButtonText: '确定',
            type: 'success',
            dangerouslyUseHTMLString: true
          })
        } else if (res === null) {
          this.$message.success('批量重试成功！')
        }
        this.search('searchForm')
      }).finally(() => {
        this.batchLoading = false
      })
    },
    search(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.listQueryInfo.pageNo = 1
          this.getTaskRecordsList()
        } else {
          return false
        }
      })
    },
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
      delete this.searchForm.materialGroupNumTmp
    },
    // 清楚所有查询参数
    reset(formName) {
      // this.$refs[formName].resetFields()
      for (var k in this.searchForm) {
        this.searchForm[k] = ''
      }
    },
    getTaskRecordsList() {
      this.loading = true
      let queryParams = {}

      Object.assign(queryParams, { ...this.listQueryInfo, ...this.copySearchForm })
      getTaskRecordsList(queryParams).then((res) => {
        this.taskList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    retryTask(taskId) {
      console.log(taskId)
      retryTask({ updateUser: window.CUR_DATA.user.name, taskId }).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg);
        } else {
          this.$message.error((res && res.msg) || '重试失败！');
        }
      })
    }
  },
  components: {
    Pagination
  }
}
</script>

<style scoped lang="scss">
.notify {
  line-height: 16px;
  font-size: 12px;
  margin-bottom: 5px;
}
.el-date-editor--daterange.el-input,
.el-date-editor--daterange.el-input__inner,
.el-date-editor--timerange.el-input,
.el-date-editor--timerange.el-input__inner {
  width: 100%;
}
.min-width {
  min-width: 130px;
}
.hide {
  visibility: hidden;
}
</style>
