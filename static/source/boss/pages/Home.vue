<template>
  <div class="page page-home">
    <div class="welcome" v-if="!showWorkbench">
      <div class="welcome-img">
        <img src="@/assets/home/<USER>" />
      </div>
      <h1>欢迎使用员工一站式系统</h1>
    </div>
    <iframe
      v-else
      :src="src"
      frameborder="0"
      width="100%"
      height="100%"
      style="display:block;"
    />
  </div>
</template>

<script>
import { sensors, getCurBizRole } from '@/utils';
import request from '@/utility/request';

export default {
  data () {
    return {
      showWorkbench: false,
      src: '/wb/',
      curBizRole: {}
    }
  },
  methods: {
    trackOrigin(label) {
      sensors('WorkbenchView', {
          current_security_role_name: this.curBizRole?.bizRoleName ? [this.curBizRole.bizRoleName] : [],
          first_menu_name: '我的工作台',
          label_name: label,
          platform_type: 'BOSS-工作台',
          current_page_type: this.curBizRole?.workbenchRoleName || '',
          operation_type: '菜单'
        })
    }
  },
  mounted () {
    if (window.CUR_DATA) {
      if (window.CUR_DATA.env === 'local') {
        this.src = 'http://local.zkh360.com:5173/wb/#/'
      }
    request({
      url: '/api-opc-csc/api/workbench/item/list',
      method: 'POST'
    }).then((res) => {
      const roleList = res?.data?.personParam?.bizRoleList
      const newWorkbenchAuth = roleList && roleList?.length > 0 && roleList.some(role => role.workbenchAvailable)
      const oldWorkbenchAuth = res && res.total
      if (newWorkbenchAuth || oldWorkbenchAuth) {
        this.showWorkbench = true;
        const bizRole = getCurBizRole();
        this.curBizRole = bizRole || roleList[0]
        // sessionStorage.setItem('boss-current-bizrole', JSON.stringify(roleList[0]))
        // this.itemViewType = res.data?.personParam?.itemViewType
        this.trackOrigin('首页')

        newWorkbenchAuth && window.open(this.src, '_self')
      }
    })
    }
  },
  activated() {
    // 可能存在的问题：切换账号后没有更新showWorkbench
    if (this.showWorkbench) {
      // if (openType === 'sidemenu') {
      //   this.trackOrigin('我的工作台')
      // } else {
      this.trackOrigin('首页')
      // }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-home {
  height: calc(100vh - 100px);
}
.welcome {
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  text-align: center;
  &-img {
    margin: 70px 0 20px 0;
  }
}
</style>
