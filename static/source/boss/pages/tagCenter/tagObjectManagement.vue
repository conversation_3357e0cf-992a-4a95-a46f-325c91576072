<template>
  <div class="tagObject-search-list">
    <div class="search-filter">
      <el-form
        :model="searchForm"
        ref="searchFrom"
        style="width: 100%"
        label-suffix=":"
        label-width="60px"
      >
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item label="名称" prop="name">
              <el-input
                v-model="searchForm.name"
                clearable
                placeholder="请输入名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="编码" prop="code">
              <el-input
                v-model="searchForm.code"
                clearable
                placeholder="请输入编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="searchForm.status"
                clearable
                collapse-tags
                filterable
              >
                <el-option
                  v-for="item in option.status"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :offset="6" :span="2" style="text-align: right; margin-bottom: 10px">
            <el-button @click="handleSearch" type="primary">查询 </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-col :offset="21" :span="2" style="text-align: right; margin-bottom: 10px">
      <el-button @click="createShow = true" type="primary">创建</el-button>
    </el-col>
    <el-dialog title="创建标签对象" :visible.sync="createShow">
      <el-form
        ref="createForm"
        :model="createForm"
        :rules="createRules"
        label-suffix=":"
        label-width="80px"
      >
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="createForm.name"
            autocomplete="off"
            style="width: 60%"
            placeholder="名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input
            v-model="createForm.code"
            autocomplete="off"
            style="width: 60%"
            placeholder="编码"
          ></el-input>
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-radio-group v-model="createForm.status">
            <el-radio
              v-for="item in option.status"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            type="textarea"
            v-model="createForm.remark"
            :autosize="{ minRows: 4, maxRows: 10 }"
          ></el-input>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
        :style="{ display: 'flex', justifyContent: 'center' }"
      >
        <el-button type="primary" @click="handleCreate">确 定</el-button>
        <el-button @click="cancelCreate">取 消</el-button>
      </div>
    </el-dialog>
    <div class="search-result">
      <el-table
        :data="table.tableData"
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
        border
        v-loading="loading.tableLoading"
      >
        <el-table-column label="id">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="名称">
          <template slot-scope="scope">
            <el-input
              placeholder="请输入内容"
              v-show="scope.row.showName"
              v-model="scope.row.editName"
              @focus="scope.row.nameFocus = true"
              @blur="handleEditBlurName(scope.row)"
              :autofocus="scope.row.nameFocus"
            ></el-input>
            <span
              v-show="!scope.row.showName"
              @click="handleEditClickName(scope.row)"
              >{{ scope.row.name }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="编码">
          <template slot-scope="scope">
            <el-input
              placeholder="请输入内容"
              v-show="scope.row.showCode"
              v-model="scope.row.editCode"
              @focus="scope.row.codeFocus = true"
              @blur="handleEditBlurCode(scope.row)"
              :autofocus="scope.row.codeFocus"
            ></el-input>
            <span
              v-show="!scope.row.showCode"
              @click="handleEditClickCode(scope.row)"
              >{{ scope.row.code }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span :style="{color: scope.row.status ? '#4caf50' : '#ff7268'}">{{ scope.row.status ? "启用" : "停用" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="描述">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人">
          <template slot-scope="scope">
            <span>{{ scope.row.creator }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createdTime | afterDateformat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              :type=" scope.row.status ? 'danger':'success'"
              @click="handleStatus(scope.row.id, scope.row.status)"
              >{{ scope.row.status ? "停用" : "启用" }}</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagi" v-if="table.total > 10">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="table.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="table.total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getTagObject,
  updateTagObjectStatus,
  updateTagObject,
  createTagObject
} from '@/api/tagCenter';

export default {
  name: 'tagManagement',
  components: {},
  data() {
    return {
      searchForm: {
        name: '',
        code: '',
        status: ''
      },
      table: {
        pageNum: 1,
        current: 1,
        pageSize: 10,
        total: 0,
        tableData: []
      },
      option: {
        status: [
          {
            value: true,
            label: '启用'
          },
          {
            value: false,
            label: '停用'
          }
        ]
      },
      loading: {
        searchLoading: false,
        tableLoading: false,
        pageLoading: false
      },
      createForm: {
        name: '',
        code: '',
        status: false,
        remark: ''
      },
      createRules: {
        code: [
          {
            pattern: /^[a-zA-Z]*$/,
            message: '编码只能输入英文字母',
            trigger: 'blur'
          },
          { required: true, message: '请输入编码', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      createShow: false
    };
  },
  async created() {
    await this.onSubmit('initSearch');
  },
  computed: {},
  filters: {
    /**
     * @param {string} date
     * 格式化后台返回的此种格式的时间
     * 2021-07-28T01:53:53.000+0000
     */
    afterDateformat: function (date) {
      if (date && date.indexOf('T')) {
        const dateee = new Date(date).toJSON()
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      } else {
        return ''
      }
    }
  },
  methods: {
    async onSubmit(initSearch) {
      let params = this.removeEmpty(this.searchForm);
      this.loading.searchLoading = true;
      this.loading.tableLoading = true;
      params = {
        ...params,
        pageNum: this.table.pageNum - 1,
        pageSize: this.table.pageSize
      };
      return getTagObject(params)
        .then((data) => {
          if (data.content) {
            try {
              this.table.total = data.totalElements;
              this.table.tableData = data.content.map((item) => {
                return {
                  ...item,
                  showName: false,
                  showCode: false,
                  nameFocus: true,
                  codeFocus: true,
                  editName: item.name,
                  editCode: item.code
                };
              });
            } catch (err) {
              console.log(err);
            }
          }
        })
        .catch((err) => {
          this.$message.warning(err.msg || err.message || '请求失败！');
        })
        .finally(() => {
          this.loading.searchLoading = false;
          this.loading.tableLoading = false;
        });
    },
    removeEmpty(obj) {
      let newObj = {};
      for (var attr in obj) {
        if (
          obj.hasOwnProperty(attr) &&
          obj[attr] !== '' &&
          obj[attr] !== null &&
          obj[attr] !== undefined
        ) {
          newObj[attr] = obj[attr];
        }
      }
      return newObj;
    },
    handleSizeChange(val) {
      this.table.pageSize = val;
      this.onSubmit();
    },
    handleCurrentChange(val) {
      this.table.pageNum = val;
      this.onSubmit();
    },
    handleSearch() {
      this.table.pageNum = 1;
      this.onSubmit();
    },
    // 启用/禁用标签对象
    handleStatus(id, status) {
      let params = {
        id: id,
        status: !status
      };
      const loading = this.$loading({
        lock: true,
        text: '操作中......',
        background: 'rgba(0, 0, 0, 0.8)'
      });
      updateTagObjectStatus(params)
        .then((res) => {
          this.onSubmit();
        })
        .catch((err) => {
          this.$message.warning(err.msg || err.message || '请求失败！');
        })
        .finally(() => {
          loading.close()
        });
    },
    handleEditClickName(row) {
      row.showName = true;
      row.showCode = true;
      row.nameFocus = true;
      row.codeFocus = false;
    },
    handleEditClickCode(row) {
      row.showCode = true;
      row.showName = true;
      row.codeFocus = true;
      row.nameFocus = false;
    },
    handleEditBlurName(row) {
      row.nameFocus = false;
      setTimeout(() => {
        if (!(row.nameFocus || row.codeFocus)) {
          row.showCode = false;
          row.showName = false;
        }
        this.handleEdit(row);
      }, 220);
      // 编辑
    },
    handleEditBlurCode(row) {
      row.codeFocus = false;
      setTimeout(() => {
        if (!(row.nameFocus || row.codeFocus)) {
          row.showCode = false;
          row.showName = false;
        }
        // 编辑
        this.handleEdit(row);
      }, 220);
    },
    // 编辑标签对象
    handleEdit(row) {
      if (
        row.editName &&
        row.editCode &&
        (row.editName !== row.name || row.editCode !== row.code)
      ) {
        let reg = /^[a-zA-Z]*$/;
        if (reg.test(row.editCode)) {
          let params = {
            id: row.id,
            name: row.editName,
            code: row.editCode
          };
          updateTagObject(params)
            .then((res) => {
              this.onSubmit();
            })
            .catch((err) => {
              this.$message.warning(err.msg || err.message || '请求失败！');
            });
        } else {
          row.editName = row.name;
          row.editCode = row.code;
          this.$message.warning('编码只能是英文字母');
        }
      } else {
        row.editName = row.name;
        row.editCode = row.code;
        this.$message.warning('名称和编码必填');
      }
    },
    // 创建标签对象
    handleCreate() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          let params = this.createForm;
          createTagObject(params)
            .then((res) => {
              if (res) {
                this.$message.success('创建成功');
                this.onSubmit('initSearch');
              }
              this.cancelCreate();
            })
            .catch((err) => {
              this.$message.warning(err.msg || err.message || '请求失败！');
            });
        }
      });
    },
    cancelCreate() {
      this.$refs.createForm.resetFields();
      this.createShow = false;
      this.createForm = {
        name: '',
        code: '',
        status: false,
        remark: ''
      };
    }
  }
};
</script>
<style lang="scss" scoped>
.tagObject-search-list {
  .search-filter {
    border-radius: 10px;
    border: 1px solid #eee;
    margin: 20px 0;
    padding: 10px;
  }
}
</style>
