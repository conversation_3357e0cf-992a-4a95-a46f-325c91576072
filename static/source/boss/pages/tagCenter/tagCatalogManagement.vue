<template>
  <div class="tagCatalog">
    <div class="tagCatalogManage">
      <div class="catalog-title">
        <span>类目管理</span>
      </div>
      <div class="catalog-search">
        <el-col :span=8>
          <el-select
            v-model="tagObjectId"
            clearable
            remote
            :loading='loading.optionsLoading'
            :remote-method="getObject"
            collapse-tags
            placeholder="请输入关键字搜索"
            filterable
            loading-text="加载中..."
            @change="onSubmit"
          >
            <el-option
              v-for="item in tagOption"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-col>
      </div>
      <div class="tree-list">
        <div class="create-node">
          <el-button
            v-show="treeData.length && !currTreeNode"
            type="primary"
            size="mini"
            @click="() => append()">
            创建类目
          </el-button>
        </div>
        <el-tree
          v-if="treeData"
          :data="treeData"
          :props="defaultProps"
          node-key="id"
          @node-click="handleNodeClick"
          ref="tree"
          highlight-current
          >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <span class="node-text">{{ node.label }}</span>
            <span v-if='node.isCurrent && currTreeNode'>
              <el-button
                type="text"
                size="mini"
                @click="() => append(node, data)">
                创建子类目
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="() => edit(node, data)">
                编辑
              </el-button>
            </span>
          </span>
        </el-tree>
      </div>
    </div>
    <div class="tagCatalogDetail">
      <div class="detail-title">
        <span>菜单信息</span>
      </div>
      <div class="catalog-detail" v-if="currTreeNode">
        <span class="tag-name">名称：{{this.currTreeNode.name}}</span>
        <span class="tag-code">code：{{this.currTreeNode.code}}</span>
        <span class="tag-remark">描述：{{this.currTreeNode.remark}}</span>
      </div>
      <div class="detail-empty" v-if="!currTreeNode">
        请先选择节点
      </div>
    </div>
    <el-dialog
      width="40%"
      title="新建类目"
      class="dialogClass"
      :visible.sync="dialogVisible"
      :show-close="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <el-form ref="dialogForm" :model="dialogForm" :rules="createRules" style="width: 100%" label-suffix=":" label-width="80px" :hide-required-asterisk="false">
        <el-form-item label="对象" prop="tagObjectId">
          <el-select
            v-model="dialogForm.tagObjectId"
            filterable
            style="width: 100%;"
            placeholder="请选择对象"
            disabled>
            <el-option
              v-for="item in tagObjectOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="父节点" prop="parentId">
          <span>{{parentList}}</span>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="dialogForm.name"
            filterable
            clearable
            style="width:100%"
            placeholder="名称"
          />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input
            v-model="dialogForm.code"
            filterable
            clearable
            style="width:100%"
            placeholder="编码"
          />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            v-model="dialogForm.remark"
            filterable
            clearable
            style="width:100%"
            placeholder="描述"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" :style="{display: 'flex', justifyContent: 'center'}">
        <el-button size="medium" @click="storeAddDialog" type="primary">保存</el-button>
        <el-button size="medium" @click="closeAddDialog" >取消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      width="40%"
      title="编辑类目"
      class="editClass"
      :visible.sync="editVisible"
      :show-close="false"
      :close-on-press-escape="false"
      :close-on-click-modal="false">
      <el-form ref="editForm" :model="editForm" :rules="editRules" style="width: 100%" label-suffix=":" label-width="80px" :hide-required-asterisk="false">
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="editForm.name"
            clearable
            style="width:100%"
            placeholder="名称"
          />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input
            v-model="editForm.code"
            clearable
            style="width:100%"
            placeholder="编码"
          />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            v-model="editForm.remark"
            clearable
            style="width:100%"
            placeholder="描述"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" :style="{display: 'flex', justifyContent: 'center'}">
        <el-button size="medium" @click="storeEditDialog" type="primary">保存</el-button>
        <el-button size="medium" @click="closeEditDialog" >取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>

import { getTagObject, getCatagory, createCatagory, updateCatagory } from '@/api/tagCenter'

export default {
  name: 'tagCatalogManagement',
  components: {},
  data() {
    return {
      loading: {
        optionsLoading: false
      },
      tagObjectId: '',
      defaultProps: {
        children: 'childs',
        label: 'name'
      },
      tagOption: [],
      treeData: [],
      currTreeNode: null,
      parentList: '',
      dialogVisible: false,
      editVisible: false,
      tagObjectOptions: [],
      dialogForm: {
        parentId: '',
        tagObjectId: '',
        name: '',
        code: '',
        remark: ''
      },
      editForm: {},
      createRules: {
        tagObjectId: [{ required: true, message: '请选择对象', trigger: 'blur' }],
        code: [
          { pattern: /^[a-zA-Z]*$/, message: '编码只能输入英文字母', trigger: 'blur' },
          { required: true, message: '请输入编码', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      editRules: {
        code: [
          { pattern: /^[a-zA-Z]*$/, message: '编码只能输入英文字母', trigger: 'blur' },
          { required: true, message: '请输入编码', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      }
    };
  },
  methods: {
    async onSubmit(initSearch) {
      if (this.tagObjectId) {
        const loading = this.$loading({
          lock: true,
          text: '查询中......',
          background: 'rgba(0, 0, 0, 0.8)'
        });
        let catalogParams = {
          tagObjectId: this.tagObjectId,
          level: 2
        }
        getCatagory(catalogParams)
          .then((data) => {
            if (data) {
              loading.close();
              this.treeData = data.content
            }
          })
      }
    },
    handleNodeClick(data) {
      if (this.currTreeNode && (this.currTreeNode.fullId === data.fullId)) {
        this.$refs.tree.setCheckedKeys([])
        this.currTreeNode = null
      } else {
        this.currTreeNode = data
      }
    },
    append(node) {
      this.dialogVisible = true
      this.dialogForm.tagObjectId = this.tagObjectId
      let params = {
        pageNum: 0,
        pageSize: 10
      };
      getTagObject(params).then((res) => {
        if (res) {
          this.tagObjectOptions = res.content
        }
      })
      if (node) {
        this.dialogForm.parentId = node.data.id
        this.getParent(node)
      } else {
        this.dialogForm.parentId = 1
        this.parentList = '/'
      }
    },
    edit(node, data) {
      this.editVisible = true
      this.editForm = {
        status: data.status,
        id: data.id,
        code: data.code,
        parentId: node.parent.data.id,
        name: data.name,
        remark: data.remark || '',
        tagObjectId: data.tagObjectId
      }
    },
    getParent(node) {
      if (node.parent) {
        this.parentList = `${node.data.name}/${this.parentList}`
        this.getParent(node.parent)
      }
    },
    storeAddDialog() {
      this.$refs.dialogForm.validate((valid) => {
        if (valid) {
          let params = this.dialogForm
          createCatagory(params)
            .then((data) => {
              if (data.status) {
                this.closeAddDialog()
                this.$message('创建成功')
                this.onSubmit('')
              }
            })
            .catch((err) => {
              this.$message.warning(err.msg || err.message || '请求失败！');
            })
        }
      })
    },
    closeAddDialog() {
      this.$refs.dialogForm.resetFields()
      this.parentList = ''
      this.dialogForm = {
        parentId: '',
        tagObjectId: '',
        name: '',
        code: '',
        remark: ''
      }
      this.dialogVisible = false
    },
    storeEditDialog() {
      this.$refs.editForm.validate((valid) => {
        if (valid) {
          let params = this.editForm
          updateCatagory(params)
            .then((data) => {
              if (data.status) {
                this.closeEditDialog()
                this.$message.success('编辑成功')
                this.onSubmit('')
              }
            })
            .catch((err) => {
              this.$message.warning(err.msg || err.message || '请求失败！');
            })
        }
      })
    },
    closeEditDialog() {
      this.$refs.editForm.resetFields()
      this.editForm = {}
      this.editVisible = false
    },
    getObject(query) {
      if (query) {
        this.loading.optionsLoading = true
        let params = {
          name: query,
          pageNum: 0,
          pageSize: 10
        };
        getTagObject(params).then((res) => {
          if (res) {
            this.loading.optionsLoading = false
            this.tagOption = res.content
          }
        })
      } else {
        this.tagOption = []
      }
    }
  }
};
</script>
<style lang="scss" scoped>
  .tagCatalog {
    display: flex;
    justify-content: space-between;
    background: #f5f5f5;
    height: 700px;
  }
  .tagCatalogManage{
    width: 49%;
    background: #fff;
    border-radius: 4px;
    overflow: auto;
    .catalog-title {
      display: flex;
      width: 100%;
      height: 28px;
      background: #141934;
      color: #c5c7df;
      font-size: 12px;
      padding-left: 10px;
      align-items: center;
    }
    .catalog-search {
      display: flex;
      justify-content: center;
      margin-top: 10px;
      padding: 10px 0;
      border-radius: 2px;
      border: 1px solid #eee;
    }
    .tree-list {
      margin: 20px;
      .create-node {
        display: flex;
        justify-content: flex-end;
        height: 30px;
        margin-bottom: 20px;
      }
      .custom-tree-node {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
  .tagCatalogDetail {
    width: 49%;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    .detail-title {
      display: flex;
      width: 100%;
      height: 28px;
      background: #141934;
      color: #c5c7df;
      font-size: 12px;
      padding-left: 10px;
      align-items: center;
    }
    .catalog-detail {
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: flex-start;
      padding: 80px 0 0 100px;
      span {
        margin: 10px 0;
      }
    }
    .detail-empty {
      margin: 80px 40%;
      color: #909399;
    }
  }
</style>
<style  lang="scss">
  .tree-list {
    .el-tree-node.is-current>.el-tree-node__content {
      span {
        color: #409eff;
      }
      .is-leaf {
        opacity: 0;
      }
      .el-tree-node__label{
        color: #fff;
        background: #409eff;
        padding:0 6px;
        border-radius: 2px;
      }
      span.name{
        span {
          color: #fff;
          background: #409eff;
          padding:0 6px;
          border-radius: 2px;
        }
      }
    }
  }
</style>
