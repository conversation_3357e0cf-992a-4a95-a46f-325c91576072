<template>
  <div class="tagManagement-search-list">
    <div class="search-filter">
      <el-form
        :model="searchForm"
        ref="searchFrom"
        style="width: 98%"
        label-suffix=":"
        label-width="80px"
      >
        <el-row :gutter="20">
          <el-col :span="4">
            <el-form-item label="所属对象" prop="tagObject">
              <el-select
                v-model="table.tagObjectId"
                clearable
                remote
                :remote-method="getSearchObjectOption"
                collapse-tags
                filterable
                @change="getSearchCatalogOption"
              >
                <el-option
                  v-for="item in option.tagObject"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所属类目" prop="tagCatalog">
              <el-cascader
                :props="catagoryListProps"
                :options="option.tagCatalog"
                @change="$refs.cascader.dropDownVisible = false"
                clearable
                filterable
                :placeholder="table.tagObjectId ? (loading.catalogLoading ? '获取类目中': '请选择类目') : '请先选择对象'"
                v-model="searchForm.catagoryId"
                ref="cascader"
                separator="/"
              >
                <template slot-scope="{data}">
                  <span class="tag-cascader-label" @click="() => handleCatagory(data)">{{data.name}}</span>
                </template>
              </el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="名称" prop="name">
              <el-input
                v-model="searchForm.name"
                clearable
                placeholder="请输入名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="searchForm.status"
                clearable
                collapse-tags
              >
                <el-option
                  v-for="item in option.status"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :offset="1" :span="2" style="text-align: center; margin-bottom: 10px">
            <el-button @click="handleSearch" type="primary">查询 </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row :gutter="20"
      style="display: flex; justify-content: end;">
      <el-col
        :span="2"
        style="text-align: right; margin-bottom: 10px"
      >
        <el-button @click="handleCreate" type="primary">创建</el-button>
      </el-col>
      <el-col :span="3" style="text-align: center; margin: 0 10px 10px 0">
        <el-button @click="batchShow = true" type="primary">批量打标</el-button>
      </el-col>
    </el-row>
    <el-dialog
      :visible="batchShow"
      title="批量打标"
      width="550px"
      :before-close="closeDialog"
      :close-on-click-modal="false"
    >
      <div>
        <div class="upload-create">
          <el-upload
            ref="upload"
            action
            style="display: inline-block; width: 90%"
            drag
            :on-remove="handleRemove"
            :show-file-list="true"
            :multiple="false"
            :limit="1"
            :http-request="httpRequestHandle"
            :accept="'.xlsx'"
            :before-upload="$validateFileType"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处上传，或<em>点击上传</em>
            </div>
          </el-upload>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer"
        :style="{ display: 'flex', justifyContent: 'right' }"
      >
        <el-col :span="16" style="text-align: left; padding-top: 10px; ">
          <i style="font-size: 12px; color: red; position: relative; top: 2px; margin-right:6px;">*</i>支持上传.xlsx文件
        </el-col>
        <el-link
          class="tagTemplate"
          type="info"
          :href="tagTemplate"
          target="_blank"
          >
          下载打标模板
        </el-link>
        <el-button type="primary" @click="handleBatchTag">上传</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="createOrEdit ? '创建标签' : '编辑标签'"
      :visible.sync="createShow"
      :before-close="cancelCreate"
      :close-on-click-modal="false"
    >
      <el-form
        ref="createForm"
        :model="createForm"
        :rules="createRules"
        label-suffix=":"
        label-width="100px"
      >
        <el-form-item label="对象" prop="tagObjectId">
          <el-select
            v-model="createForm.tagObjectId"
            filterable
            style="width: 60%"
            placeholder="请选择对象"
            v-if="createOrEdit"
            @change="getCreateCatalogOption"
            remote
            :remote-method="getCreateObjectOption"
          >
            <el-option
              v-for="item in option.createObject"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-input
            v-if="!createOrEdit"
            v-model="createForm.tagObjectName"
            style="width: 60%"
            :disabled="true"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="类目"
          prop="catagoryId"
        >
          <el-cascader
            :disabled="!createForm.tagObjectId || !createOrEdit"
            :props="catagoryListProps"
            :options="option.createCatalog"
            @change="$refs.createCascader.dropDownVisible = false"
            style="width: 60%"
            clearable
            filterable
            :placeholder="createForm.tagObjectId ? '请选择类目':'请先选择对象'"
            v-model="createForm.catagoryId"
            ref="createCascader"
            separator="/"
          >
            <template slot-scope="{data}">
              <span class="tag-cascader-label" @click="() => handleCreateCatagory(data)">{{data.name}}</span>
            </template>
          </el-cascader>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input
            v-model="createForm.name"
            autocomplete="off"
            style="width: 60%"
            placeholder="名称"
            :disabled="!createForm.tagObjectId"
          ></el-input>
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input
            v-model="createForm.code"
            autocomplete="off"
            style="width: 60%"
            placeholder="编码"
            :disabled="!createForm.tagObjectId || !createOrEdit"
          ></el-input>
        </el-form-item>
        <el-form-item label="值字典" prop="value">
          <el-input
            v-model="createForm.value"
            autocomplete="off"
            style="width: 60%"
            placeholder="值字典"
            :disabled="!createForm.tagObjectId"
          ></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            type="textarea"
            v-model="createForm.remark"
            :autosize="{ minRows: 4, maxRows: 10 }"
            :disabled="!createForm.tagObjectId"
          ></el-input>
        </el-form-item>
        <el-form-item label="所属业务" prop="businessName">
          <el-select
            v-model="createForm.businessName"
            filterable
            style="width: 60%"
            placeholder="请选择业务组"
            :disabled="!createForm.tagObjectId"
          >
            <el-option
              v-for="item in option.businessName"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否启用" prop="status">
          <el-radio-group
            v-model="createForm.status"
            :disabled="!createForm.tagObjectId"
          >
            <el-radio
              v-for="item in option.status"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否允许人工编辑" prop="manualEditFlag">
          <el-radio-group
            v-model="createForm.manualEditFlag"
            :disabled="!createForm.tagObjectId"
          >
            <el-radio
              v-for="item in option.booleanOption"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="前台是否展示" prop="frontShowFlag">
          <el-radio-group
            v-model="createForm.frontShowFlag"
            :disabled="!createForm.tagObjectId"
          >
            <el-radio
              v-for="item in option.booleanOption"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="业务含义描述" prop="businessDescribe">
          <el-input
            type="textarea"
            v-model="createForm.businessDescribe"
            :autosize="{ minRows: 4, maxRows: 10 }"
            :disabled="!createForm.tagObjectId"
          ></el-input>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
        :style="{ display: 'flex', justifyContent: 'center' }"
      >
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
        <el-button @click="cancelCreate">取 消</el-button>
      </div>
    </el-dialog>
    <div class="search-result">
      <el-table
        :data="table.tableData"
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
        border
        size="mini"
        v-loading="loading.tableLoading"
      >
        <el-table-column label="id">
          <template slot-scope="scope">
            <span>{{ scope.row.id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属对象">
          <template slot-scope="scope">
            <span>{{ scope.row.tagObjectName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所属类目">
          <template slot-scope="scope">
            <span>{{ scope.row.catagoryName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="编码">
          <template slot-scope="scope">
            <span>{{ scope.row.code }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <span :style="{color: scope.row.status ? '#4caf50' : '#ff7268'}">{{ scope.row.status ? "启用" : "停用" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="描述">
          <template slot-scope="scope">
            <span>{{ scope.row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人">
          <template slot-scope="scope">
            <span>{{ scope.row.creator }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createdTime | afterDateformat }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          width="180">
          <template slot-scope="scope">
            <el-button
              @click="handleStatus(scope.row.id, scope.row.status)"
              type="text"
              :style="{color : scope.row.status ? 'red':'#4caf50'}"
              >{{ scope.row.status ? "停用" : "启用" }}</el-button
            >
            <el-button
              @click="handleGoTagMark(scope.row.code, scope.row.tagObjectCode)"
              type="text"
              >打标情况</el-button
            >
            <el-button
              @click="handleEdit(scope.row)"
              type="text"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagi" v-if="table.total > 10">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="table.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="table.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="table.total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getTagObject,
  getTag,
  createTag,
  updateTag,
  getCatagory,
  updateTagStatus,
  createObjectTag,
  getBusinessName
} from '@/api/tagCenter';
import { remove } from 'lodash';
import { upload } from '@/utils/upload';

export default {
  name: 'tagManagement',
  components: {},
  data() {
    return {
      searchForm: {
        catagoryId: '',
        name: '',
        status: ''
      },
      table: {
        tagObjectId: '',
        pageNum: 1,
        current: 1,
        pageSize: 10,
        total: 0,
        tableData: []
      },
      option: {
        tagObject: [],
        tagCatalog: [],
        createObject: [],
        createCatalog: [],
        businessName: [],
        status: [
          {
            value: true,
            label: '启用'
          },
          {
            value: false,
            label: '停用'
          }
        ],
        booleanOption: [
          {
            value: true,
            label: '是'
          },
          {
            value: false,
            label: '否'
          }
        ]
      },
      catagoryListProps: {
        value: 'id',
        children: 'childs',
        label: 'name',
        expandTrigger: 'hover',
        checkStrictly: true,
        emitPath: false
      },
      loading: {
        catalogLoading: false,
        tableLoading: false,
        pageLoading: false
      },
      createForm: {
        id: '',
        catagoryName: '',
        tagObjectName: '',
        tagObjectId: '',
        catagoryId: '',
        name: '',
        code: '',
        value: '',
        status: false,
        businessName: '',
        businessDescribe: '',
        manualEditFlag: false,
        remark: '',
        frontShowFlag: false
      },
      createRules: {
        code: [
          {
            pattern: /^[a-zA-Z]*$/,
            message: '编码只能输入英文字母',
            trigger: 'blur'
          },
          { required: true, message: '请输入编码', trigger: 'blur' }
        ],
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        tagObjectId: [{ required: true, message: '请选择对象', trigger: 'blur' }],
        catagoryId: [{ required: true, message: '请选择类目', trigger: 'blur' }]
      },
      batchShow: false,
      createShow: false,
      isEdit: false,
      tagFileList: [],
      createOrEdit: 1,
      tagTemplate:
        '//static.zkh360.com/file/2022-03-24/%E6%A0%87%E7%AD%BE%E6%A8%A1%E6%9D%BF-1648105512525.xlsx'
    };
  },
  async created() {
    await this.onSubmit('initSearch');
  },
  filters: {
    /**
     * @param {string} date
     * 格式化后台返回的此种格式的时间
     * 2021-07-28T01:53:53.000+0000
     */
    afterDateformat: function (date) {
      if (date && date.indexOf('T')) {
        const dateee = new Date(date).toJSON()
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      } else {
        return ''
      }
    }
  },
  methods: {
    async onSubmit(initSearch) {
      let params = this.removeEmpty(this.searchForm);
      this.loading.tableLoading = true;
      if (initSearch) {
        getBusinessName().then((res) => {
          this.option.businessName = res;
        });
      }
      params = {
        ...params,
        page: this.table.pageNum - 1,
        size: this.table.pageSize
      };
      return getTag(params)
        .then((data) => {
          if (data.content) {
            this.table.tableData = data.content;
            this.table.total = data.totalElements;
          }
        })
        .catch((err) => {
          this.$message(err.msg || err.message || '请求失败！');
        })
        .finally(() => {
          this.loading.tableLoading = false;
        });
    },
    removeEmpty(obj) {
      let newObj = {};
      for (var attr in obj) {
        if (
          obj.hasOwnProperty(attr) &&
          obj[attr] !== '' &&
          obj[attr] !== null &&
          obj[attr] !== undefined
        ) {
          newObj[attr] = obj[attr];
        }
      }
      return newObj;
    },
    handleSizeChange(val) {
      this.table.pageSize = val;
      this.onSubmit();
    },
    handleCurrentChange(val) {
      this.table.pageNum = val;
      this.onSubmit();
    },
    handleClose() {
      this.batchShow = false;
    },
    // 移除上传文件
    handleRemove(file, fileList) {
      remove(this.tagFileList, function (item) {
        return item.uid === file.uid;
      });
    },
    async httpRequestHandle(file) {
      // 校验大小
      // const i//sGtLimit = file.file.size / 1024 / 1024 > this.fileSizeLimit
      // if (isGtLimit) {
      // this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
      // return
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      });
      const response = await upload('', file.file, 'zkh360-tagcenter', 'oss-cn-hangzhou');
      if (response?.name) {
        this.tagFileList.push({
          uid: file.file.uid,
          fileName: response.name,
          bucketName: response.bucketName
        });
      } else {
        // this.$message.error('上传失败！');
      }
      loading.close();
    },
    handleBatchTag() {
      if (this.tagFileList) {
        let params = this.tagFileList[0];
        createObjectTag(params)
          .then((res) => {
            this.$message.success('打标上传成功，请稍后查看');
          })
          .catch((err) => {
            this.$message(err.msg || err.message || '请求失败！');
          });
      } else {
        this.$message.warning({
          message: '请先上传打标信息'
        });
      }
      this.closeDialog();
    },
    closeDialog() {
      this.$refs.upload.clearFiles();
      this.tagFileList = [];
      this.batchShow = false;
    },
    // 远程搜索
    getSearchObjectOption(query) {
      if (query) {
        let params = {
          name: query,
          pageNum: 0,
          pageSize: 10
        };
        getTagObject(params).then((res) => {
          if (res) {
            this.option.tagObject = res.content;
          }
        });
      } else {
        this.option.tagObject = [];
      }
    },
    getSearchCatalogOption() {
      if (this.table.tagObjectId) {
        let catalogParams = {
          tagObjectId: this.table.tagObjectId,
          level: 2
        };
        this.loading.catalogLoading = true
        getCatagory(catalogParams)
          .then((data) => {
            if (data) {
              this.option.tagCatalog = this.getCascaderData(data.content)
            }
          })
          .catch((err) => {
          this.$message(err.msg || err.message || '请求失败！');
          })
          .finally(() => {
            this.loading.catalogLoading = false;
          });
      } else {
        this.option.tagCatalog = []
      }
    },
    // 级联数据处理
    getCascaderData(data) {
      let newData = []
      newData = data.map((item) => {
        if (item.childs?.length < 1) {
          item.childs = undefined
        } else {
          this.getCascaderData(item.childs)
        }
        return item
      })
      return newData
    },
    // 获取对象列表
    getCreateObjectOption(query) {
      if (query) {
        let params = {
          name: query,
          pageNum: 0,
          pageSize: 10
        };
        getTagObject(params).then((res) => {
          if (res) {
            this.option.createObject = res.content;
          }
        });
      } else {
        this.option.createObject = [];
      }
    },
    getCreateCatalogOption() {
      if (this.createForm.tagObjectId) {
        let catalogParams = {
          tagObjectId: this.createForm.tagObjectId,
          level: 2
        };
        getCatagory(catalogParams).then((data) => {
          if (data) {
            this.option.createCatalog = this.getCascaderData(data.content)
          }
        });
      } else {
        this.option.createCatalog = []
      }
    },
    // 选择类目
    handleCatagory(data) {
      this.searchForm.catagoryId = data.id;
      this.$refs.cascader.dropDownVisible = false;
    },
    handleCreateCatagory(data) {
      this.createForm.catagoryId = data.id;
      this.$refs.createCascader.dropDownVisible = false;
    },
    handleSearch() {
      this.table.pageNum = 1;
      this.onSubmit();
    },
    // 启用/禁用标签
    handleStatus(id, status) {
      let params = {
        id: id,
        status: !status
      };
      const loading = this.$loading({
        lock: true,
        text: '操作中......',
        background: 'rgba(0, 0, 0, 0.8)'
      });
      updateTagStatus(params).then((res) => {
        this.onSubmit();
      })
      .finally(() => {
        loading.close();
      });
    },
    handleCreate() {
      this.createOrEdit = 1;
      this.createShow = true;
    },
    // 跳转打标情况
    handleGoTagMark(code, tagObjectCode) {
      try {
        this.$closeTag('/tagCenter/tagMark');
      } catch {}
      setTimeout(() => {
        this.$router.push({
          name: 'tagMark',
          params: {
            tagCode: code,
            tagObjectCode: tagObjectCode
          }
        })
      }, 0)
    },
    // 标签编辑
    handleEdit(row) {
      this.createForm = {
        id: row.id,
        catagoryName: row.catagoryName,
        tagObjectName: row.tagObjectName,
        tagObjectId: row.tagObjectId,
        catagoryId: row.catagoryId,
        name: row.name,
        code: row.code,
        value: row.value,
        status: row.status,
        businessName: row.businessName,
        businessDescribe: row.businessDescribe,
        manualEditFlag: row.manualEditFlag,
        remark: row.remark,
        frontShowFlag: row.frontShowFlag
      };
      this.option.createCatalog = [
        {
          name: row.catagoryName,
          id: row.catagoryId
        }
      ];
      this.getCreateCatalogOption();
      this.createOrEdit = 0;
      this.createShow = true;
    },
    handleSubmit() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          let params = {
            id: this.createForm.id,
            catagoryId: this.createForm.catagoryId,
            name: this.createForm.name,
            code: this.createForm.code,
            value: this.createForm.value,
            status: this.createForm.status,
            businessName: this.createForm.businessName,
            manualEditFlag: this.createForm.manualEditFlag,
            remark: this.createForm.remark,
            frontShowFlag: this.createForm.frontShowFlag
          };
          params = this.removeEmpty(params);
          if (this.createOrEdit) {
            // 创建
            const loading = this.$loading({
              lock: true,
              text: '创建中......',
              background: 'rgba(0, 0, 0, 0.8)'
            });
            createTag(params).then((data) => {
              this.createShow = false;
              this.$message.success('创建成功');
              this.onSubmit();
              this.cancelCreate();
            })
            .catch((err) => {
              this.$message(err.msg || err.message || '请求失败！');
            })
            .finally(() => {
              loading.close();
            });
          } else {
            // 编辑
            const loading = this.$loading({
              lock: true,
              text: '编辑中......',
              background: 'rgba(0, 0, 0, 0.8)'
            });
            updateTag(params).then((data) => {
              this.createShow = false;
              this.$message.success('编辑成功');
              this.onSubmit();
              this.cancelCreate();
            })
            .catch((err) => {
              this.$message(err.msg || err.message || '请求失败！');
            })
            .finally(() => {
              loading.close();
            });
          }
        }
      });
    },
    cancelCreate() {
      this.$refs.createForm.resetFields()
      this.createShow = false;
      this.createForm = {
        catagoryName: '',
        tagObjectName: '',
        tagObjectId: '',
        catagoryId: '',
        name: '',
        code: '',
        value: '',
        status: false,
        businessName: '',
        businessDescribe: '',
        manualEditFlag: false,
        remark: '',
        frontShowFlag: false
      };
    }
  }
};
</script>
<style lang="scss" scoped>
.tagManagement-search-list {
  .search-filter {
    border-radius: 10px;
    border: 1px solid #eee;
    margin: 20px 0;
    padding: 10px;
  }
  .upload-create {
    display: flex;
    justify-content: center;
  }
  .dialog-footer {
    .tagTemplate {
      margin-right: 20px;
    }
    .download-modal {
      font-size: 14px;
      color: #909399;
    }
    .download-modal:hover {
      text-decoration: underline;
    }
  }
}
</style>
<style lang="scss">
.tagManagement-search-list {
  .upload-create {
    .el-upload--text {
      width: 100%;
      .el-upload-dragger {
        width: 100%;
      }
    }
  }
}
.tag-cascader-label {
  display: inline-block;
  width: 100%;
  height: 100%;
}
</style>
