<template>
  <div class="tagMark-search-list">
    <div class="search-filter">
      <el-form
        :model="searchForm"
        ref="searchFrom"
        style="width: 100%"
        label-suffix=":"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="标签对象" prop="name" label-width="80px">
              <el-input
                v-model="searchForm.tagMedata"
                clearable
                placeholder="请输入标签对象"
              />
            </el-form-item>
          </el-col>
          <el-col :offset="2" :span="10">
            <el-form-item label="精准搜索标签对象" prop="code" label-width="140px">
              <el-input
                v-model="searchForm.tagMedatas"
                clearable
                placeholder="请输入标签对象，用空格或英文逗号分隔，最多1000个"
              />
            </el-form-item>
          </el-col>
          <el-col :offset="1" :span="2" style="text-align: right; margin-bottom: 10px">
            <el-button @click="handleSearch" type="primary">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div>
      <span :v-if="total > 0">总共{{total}}个对象</span>
    </div>
    <div>
      <el-table
        :data="tableData"
        :span-method="setSpan"
        :header-cell-style="{ 'text-align': 'center' }"
        :cell-style="{ 'text-align': 'center' }"
        border
        v-loading="tableLoading"
      >
        <el-table-column label="对象">
          <template slot-scope="scope">
            <span>{{ scope.row.tagObject }}</span>
          </template>
        </el-table-column>
        <el-table-column label="标签值">
          <template slot-scope="scope">
            <span>{{ scope.row.tagValue}}</span>
          </template>
        </el-table-column>
        <el-table-column label="打标时间">
          <template slot-scope="scope">
            <span>{{ scope.row.createTime | afterDateformat }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleDelect(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagi" v-if="total > 10">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="searchForm.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { fetchTagMarkData, deleteTagMark } from '@/api/tagCenter'

export default {
  name: 'tagMark',
  data() {
    return {
      tableLoading: false,
      searchForm: {
        pageNum: 1,
        pageSize: 10,
        tagCode: this.$route.params.tagCode || '',
        tagObject: this.$route.params.tagObjectCode || '',
        tagMedata: '',
        tagMedatas: ''
      },
      current: 1,
      total: 0,
      tableData: [],
      mergeSpan: [],
      mergeSpanIndex: ''
    }
  },
  filters: {
    /**
     * @param {string} date
     * 格式化后台返回的此种格式的时间
     * 2021-07-28T01:53:53.000+0000
     */
    afterDateformat: function (date) {
      if (date && date.indexOf('T')) {
        const dateee = new Date(date).toJSON()
        return new Date(+new Date(dateee) + 8 * 3600 * 1000).toISOString().replace(/T/g, ' ').replace(/\.[\d]{3}Z/, '')
      } else {
        return ''
      }
    }
  },
  async created() {
    this.fetchMarkObject();
  },
  methods: {
    async fetchMarkObject() {
      let params = this.dataModified(this.searchForm)
      if (params?.tagMedatas?.length > 1000) {
        this.$message.warning('输入标签对象超过1000条')
        return
      }
      this.tableLoading = true
      fetchTagMarkData(params)
        .then((res) => {
          if (res) {
            this.tableData = [];
            this.mergeSpan = [];
            this.mergeSpanIndex = '';
            for (let i = 0; i < res.data.length; i++) {
              for (let j = 0; j < res.data[i][`label_${this.searchForm.tagCode}`].length; j++) {
                this.tableData.push({
                  tagObject: res.data[i].tag_meta_data,
                  tagValue: res.data[i][`label_${this.searchForm.tagCode}`][j].tagValue,
                  createTime: res.data[i][`label_${this.searchForm.tagCode}`][j].createTime,
                  tagId: res.data[i][`label_${this.searchForm.tagCode}`][j].id
                })
              }
            }
            this.total = res.totalCount;
            this.tableMerge(this.tableData);
          }
        })
        .catch((err) => {
          this.$message(err.msg || err.message || '请求失败！');
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    handleSearch() {
      this.searchForm.pageNum = 1;
      this.searchForm.pageSize = 10;
      this.fetchMarkObject();
    },
    tableMerge(data) {
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.mergeSpan.push(1);
          this.mergeSpanIndex = 0;
        } else {
          if (data[i].tagObject === data[i - 1].tagObject) {
            this.mergeSpan[this.mergeSpanIndex] += 1;
            this.mergeSpan.push(0);
          } else {
            this.mergeSpan.push(1);
            this.mergeSpanIndex = 1;
          }
        }
      }
    },
    setSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        const rowSpan = this.mergeSpan[rowIndex]
        const columnSpan = rowSpan > 0 ? 1 : 0
        return {
          rowspan: rowSpan,
          colspan: columnSpan
        }
      }
    },
    handleDelect(row) {
      let params = {
        tagId: row.tagId,
        tagMetaData: row.tagObject,
        tagValue: row.tagValue
      }
      const loading = this.$loading({
        lock: true,
        text: '操作中......',
        background: 'rgba(0, 0, 0, 0.8)'
      });
      deleteTagMark(params)
        .then((res) => {
          loading.close();
          this.$message.success('删除成功');
          this.fetchMarkObject();
        })
        .catch((err) => {
          this.$message.warning(err.msg || err.message || '请求失败！');
          loading.close();
        })
    },
    dataModified(data) {
      let obj = data
      let newObj = {};
      if (obj?.tagMedatas) {
        obj.tagMedatas = obj.tagMedatas.split(/\s+|\s+$|,/);
      }
      for (var attr in obj) {
        if (
          obj.hasOwnProperty(attr) &&
          obj[attr] !== '' &&
          obj[attr] !== null &&
          obj[attr] !== undefined
        ) {
          newObj[attr] = obj[attr];
        }
      }
      return newObj;
    },
    handleSizeChange(val) {
      this.searchForm.pageSize = val;
      this.fetchMarkObject();
    },
    handleCurrentChange(val) {
      this.searchForm.pageNum = val;
      this.fetchMarkObject();
    }
  }
}
</script>

<style lang="scss" scoped>
.tagMark-search-list {
  .search-filter {
    border-radius: 10px;
    border: 1px solid #eee;
    margin: 20px 0;
    padding: 10px;
  }
}
</style>
