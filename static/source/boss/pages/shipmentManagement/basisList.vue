<template>
  <div class='app-container shipment-management-container'>
    <div class='filter-container'>
      <el-form
        ref='searchForm'
        :model='searchForm'
        label-suffix=":"
        label-width='120px'
        label-position='right'
      >
        <el-row>
          <el-col :span=6>
            <el-form-item label="指定渠道ID" prop="supplyChannelNo">
              <el-input v-model="searchForm.supplyChannelNo" placeholder="请输入指定渠道ID"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span='6'>
            <el-form-item label='客户名称' prop='customerNo'>
              <SelectCustomer v-model='searchForm.customerNo' />
            </el-form-item>
          </el-col>
          <el-col :span='6'>
            <el-form-item label='创建人' prop='securityUserIds'>
              <SelectSecurityUser v-model='searchForm.securityUserIds' />
            </el-form-item>
          </el-col>
          <el-col :span='6'>
            <el-form-item label='物料组' prop='materialGroupNames'>
              <MaterialGroup
                ref="materialGroup"
                v-model="searchForm.materialGroupNames"
                multiple
                getLabel
              />
            </el-form-item>
          </el-col>
          <el-col :span='6'>
            <el-form-item label='状态' prop='approval'>
              <el-select v-model="searchForm.approval" clearable placeholder="请选择审核状态">
                <el-option
                  v-for="item in ApprovalStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
             <el-form-item label='指定分类' prop='delivery_mode'>
                <el-select v-model="searchForm.deliveryMode" clearable placeholder="请选择指定分类">
                  <el-option
                    v-for="item in deliveryModeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label='集团客户' prop='containSubsidiary'>
              <el-select v-model="searchForm.containSubsidiary" clearable placeholder="请选择类型">
                <el-option
                  v-for="item in boolMap"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
           <div style="float:right;min-width:150px">
                 <el-button
              type='primary'
              icon='el-icon-search'
              :loading='basisListLoading'
              @click='handleFilter'
              >查询</el-button
            >
            <el-button type='primary' @click='handleReset'>重置</el-button>
          </div>
        </el-row>
      </el-form>
    </div>
    <div class='create-shipment-container'>
      <el-row type='flex' class='result-title' justify='space-between'>
        <el-col :span='8'>指定供货渠道/集货仓列表</el-col>
        <el-col  style='text-align: right'  v-if="hasPermission('新增')">
           <el-popover placement="bottom" width="160">
            <div class="align-center">
              <div>
                <el-button type="primary" v-if="hasPermission('新增指定供货渠道')" @click="addNewShipment('SUPPLY_TO_CUSTOMER')"
                  >新增指定供货渠道</el-button
                >
              </div>
              <div class="mt10">
                <el-button type="primary" v-if="hasPermission('新增指定集货仓')" @click="addNewShipment('ZKH_TO_CUSTOMER')"
                  >新增指定集货仓</el-button
                >
              </div>
              <div class="mt10">
                <el-button type="primary" v-if="hasPermission('新增指定发货仓')" @click="addNewShipment('SPECIFY_WAREHOUSE')"
                >新增指定发货仓</el-button
                >
              </div>
              <div class="mt10">
                <el-button type="primary" v-if="hasPermission('新增报备供货渠道')" @click="addNewShipment('REPORT_SUPPLY_TO_CUSTOMER')"
                >新增报备供货渠道</el-button
                >
              </div>
            </div>
            <el-button type="primary" slot="reference"
              >新增指定</el-button
            >
            </el-popover>
        </el-col>

      </el-row>
      <el-table
        v-loading='basisListLoading'
        :data='basisList'
        border
        fit
        highlight-current-row
        height='500'
      >
        <el-table-column
          label='指定渠道ID'
          align='center'
          prop='supplyChannelNo'
        ></el-table-column>
        <el-table-column
          label='客户编码'
          align='center'
          prop='customerNo'
        ></el-table-column>
        <el-table-column
          label='客户名称'
          align='center'
          prop='customerName'
        ></el-table-column>
        <el-table-column
          label='销售组织'
          align='center'
          prop='salesOrganizationName'
        ></el-table-column>
        <el-table-column
          label='指定客户维度'
          align='center'
          prop='customerChooseType'
        >
          <template slot-scope='{ row }'>
            {{
              getLabelByValue(
                'CustomerChooseTypeOptions',
                row.customerChooseType
              )
            }}
          </template>
        </el-table-column>
        <el-table-column
          label='指定分类'
          align='center'
          prop='deliveryMode'
        >
          <template slot-scope='{ row }'>
            {{
              getLabelByValue(
                'deliveryModeList',
                row.deliveryMode
              )
            }}
          </template>
        </el-table-column>

        <el-table-column
          label='创建人'
          align='center'
          prop='createUserName'
        ></el-table-column>
        <el-table-column
          label='创建日期'
          align='center'
          prop='createTime'
        >
          <template slot-scope='{ row }'>
            {{ row.createTime | formatDate }}
          </template>
        </el-table-column>
        <el-table-column label='状态' align='center' prop='approval'>
          <template slot-scope='{ row }'>
            {{ getLabelByValue('ApprovalStatusOptions', row.approval) }}
          </template>
        </el-table-column>
        <el-table-column label='集团客户' align='center' prop='containSubsidiary'>
          <template slot-scope='{ row }'>
            {{ row.containSubsidiary === '1' ? '是' : row.containSubsidiary === '0' ? '否' : '' }}
          </template>
        </el-table-column>
        <el-table-column label="附件" align='center'  prop='attachmentList' width="250">
          <template slot-scope='{ row }'>
            <div v-for="(it) in row.attachmentList || []" :key="it.path"  >
               <el-link type="primary" :href="getFileUrl(it.path)" target="_blank" >
                {{it.name}}
              </el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column label='操作' align='center'>
          <template slot-scope='{ row }'>
            <el-button type='text' @click='viewDetail(row.supplyChannelNo, row.deliveryMode)'
              >查看详情</el-button
            >
            <!-- 区分指定供货渠道和其他类型 -->
            <template v-if="row.deliveryMode !== 'SUPPLY_TO_CUSTOMER'">
              <el-button type='text' @click='handleSubmit(row.supplyChannelNo, "APPROVAL")' v-if="hasPermission('审批', row.approval)"
                >审批</el-button
              >
              <el-button type='text'  @click="handleSubmit(row.supplyChannelNo, 'SUBMIT')" v-if="hasPermission('提交', row.approval)">提交</el-button  >
              <el-button type='text' @click='openConfirmDialog(row.supplyChannelNo, "REJECT")' v-if="hasPermission('驳回', row.approval)"
                >驳回</el-button
              >
              <el-button type='text' @click="handleSubmit(row.supplyChannelNo, 'DRAFT')" v-if="hasPermission('取消审核', row.approval)"
                >取消审核</el-button
              >
              <el-button type='text' @click='deleteItem(row.supplyChannelNo)' v-if="hasPermission('删除', row.approval)"
                >删除</el-button
              >
            </template>
            <el-button type='text' @click='openConfirmDialog(row.supplyChannelNo, "DRAFT")' v-if="row.deliveryMode === 'SUPPLY_TO_CUSTOMER' && row.buttonSet && row.buttonSet.includes('取消审核')"
              >取消审核</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <pagination
      v-show='total > 0'
      :total='total'
      align='right'
      :page.sync='listQueryInfo.current'
      :pageSizes='[10, 20, 30, 50, 100]'
      :limit.sync='listQueryInfo.pageSize'
      layout='total, sizes, prev, pager, next, jumper'
      @pagination='getListData'
    />
    <ConfirmDialog :visible.sync='confirmDialogVisible' @confirm='handleConfirm' :confirmType='confirmType' />
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Pagination from '@/components/Pagination';
import {
  getCustomerPositions,
  approveCustomerPosition,
  deleteCustomer
} from '@/api/shipmentManagement.js';
import SelectCustomer from './components/SelectCustomer';
import SelectSecurityUser from './components/SelectSecurityUser';
import ConfirmDialog from './components/supplyToCustomer/ConfirmDialog';
import { getLabelByValue, formatDate, ApprovalStatusOptions } from './helper';
import MaterialGroup from '@/components/SearchFields/materialGroup'

const initialSearchForm = {
  customerNo: '',
  securityUserIds: [],
  productManagerWorkCodes: [],
  approval: '',
  supplyChannelNo: '',
  containSubsidiary: ''
};

export default {
  name: 'BasisList',
  data() {
    return {
      searchForm: { ...initialSearchForm },
      ApprovalStatusOptions,
      basisListLoading: false, // 查询按钮loading
      customerList: [],
      loadingCustomer: false,
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      total: 0,
      basisList: [],
      confirmDialogVisible: false,
      confirmId: '',
      confirmType: '',
      deliveryMode: '',
      deliveryModeList: [
        {
          value: 'SUPPLY_TO_CUSTOMER',
          label: '指定供货渠道'
        },
        {
          value: 'ZKH_TO_CUSTOMER',
          label: '指定集货仓'
        },
        {
          value: 'SPECIFY_WAREHOUSE',
          label: '指定发货仓'
        },
        {
          value: 'REPORT_SUPPLY_TO_CUSTOMER',
          label: '报备供货渠道'
        }
      ],
      boolMap: [
        {
          value: 1,
          label: '是'
        },
        {
          value: 0,
          label: '否'
        }
      ]
    };
  },
  filters: {
    formatDate
  },
  components: {
    Pagination,
    SelectCustomer,
    SelectSecurityUser,
    ConfirmDialog,
    MaterialGroup
  },
  created() {
    this.handleFilter()
  },
  computed: {
    ...mapState(['menu', 'userRole']),
    canApprove() {
      const currentMenu = this.findCurrentMenu(
        this.menu,
        this.$route.meta.breadcrumb
      );
      return (
        currentMenu &&
        currentMenu.children &&
        (currentMenu.children || []).some(
          (config) => config.name === '审批' && config.type === 'BUTTON'
        )
      );
    },
    // 获取操作权限
    getPermissions () {
      return this.menu.filter(menu => menu.name === '基础配置')[0].children[0].children
    },
    // 同时具有以下两个角色时才能审批
    // https://wiki.zkh360.com/confluence/pages/viewpage.action?pageId=345669975
    approvalPermission() {
      return this.userRole.includes('data-客服总监') && this.userRole.includes('boss-指定集货仓审核')
    }
  },
  methods: {
    getLabelByValue,
    // 审批流程权限相关
    hasPermission(permissionName, approval) {
      const ownerPermission = this.getPermissions.filter(permission => permission.name === permissionName).length > 0
      // 草稿|审批驳回 显示<提交??><编辑><删除>
      if (ownerPermission && ~['提交', '编辑', '删除'].indexOf(permissionName) && (approval === 'DRAFT' || approval === 'REJECT')) {
        return true
      }
      // 审核中 <审批>
      if (this.approvalPermission && ~['审批'].indexOf(permissionName) && approval === 'SUBMIT') {
        return true
      }
      // 审核中 <驳回>
      if (ownerPermission && ~['驳回'].indexOf(permissionName) && approval === 'SUBMIT') {
        return true
      }
      // 审核通过 显示<取消审核>
      if (ownerPermission && permissionName === '取消审核' && approval === 'APPROVAL') {
        return true
      }
      // 非审核流程按钮权限
      if (ownerPermission && !approval) {
        return true
      }
      return false
    },
    handleReset() {
      this.searchForm = { ...initialSearchForm };
      this.handleFilter();
    },
    viewDetail(id, type) {
      if (type === 'SUPPLY_TO_CUSTOMER') {
        this.$router.push({
          path: `/shipmentManagement/detailStc/${id}?type=${type}`
        });
      } else {
        this.$router.push({
          path: `/shipmentManagement/detail/${id}?type=${type}`
        });
      }
    },
    findCurrentMenu(menus, path) {
      for (let i = 0; i < path.length; i++) {
        let menu = menus.find((item) => item.name === path[i]);
        if (menu && menu.children.length && path.length > 1) {
          return this.findCurrentMenu(menu.children, path.slice(i + 1));
        } else {
          return menu;
        }
      }
    },
    addNewShipment(type) {
      this.$closeTag('/shipmentManagement/create/0');
      if (type === 'SUPPLY_TO_CUSTOMER') {
        this.$router.push({
          path: '/shipmentManagement/createStc/0',
          query: {
            type
          }
        });
      } else {
        this.$router.push({
          path: '/shipmentManagement/create/0',
          query: {
            type
          }
        });
      }
    },
    handleSubmit(id, approval) {
      const data = {
        approval: approval,
        approvalReason: ''
      };
      this.handleApproval(id, data);
    },
    openConfirmDialog(id, type) {
      this.confirmId = id
      this.confirmType = type
      this.confirmDialogVisible = true
    },
    handleConfirm(reason) {
      const data = {
        approval: this.confirmType,
        approvalReason: reason
      }
      this.handleApproval(this.confirmId, data)
    },
    deleteItem(id) {
      deleteCustomer(id).then((res) => {
        this.$message.success('删除成功')
        this.getListData();
      });
    },
    handleApproval(id, data) {
      approveCustomerPosition(id, data).then((res) => {
        if (res.code === 200) {
          this.$message.success('操作成功');
          this.getListData();
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    getListData() {
      this.searchForm.supplyChannelNo = this.searchForm.supplyChannelNo.trim()
      const param = { ...this.searchForm };
      param.materialGroupNames = param.materialGroupNames?.join(',') || ''
      param.securityUserIds = param.securityUserIds?.join(',') || ''
      param.pageNo = this.listQueryInfo.current;
      param.pageSize = this.listQueryInfo.pageSize;
      this.basisList = [];
      this.basisListLoading = true;
      getCustomerPositions(param)
        .then((res) => {
          this.basisListLoading = false;
          if (res.code === 200) {
            if (res.data) {
              const data = res.data;
              this.total = data.total;
              this.basisList = data.data;
            }
          } else {
            this.$message.error(res.msg || res.message);
          }
        })
        .catch(() => {
          this.basisListLoading = false;
        });
    },
    // 查询按钮
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getListData();
    },
    getFileUrl(path) {
      let url = path
      try {
        if (url.indexOf('https') === -1) {
          url = 'https' + url.replace(/http(s)?/, '')
        }
      } catch (err) {
        url = path
      }
      return url
    }
  }
};
</script>

<style lang='scss'>
.shipment-management-container {
  .filter-container {
    padding-top: 18px;
    padding-right: 10px;
    background-color: #f4f4f4;
    // .el-form-item {
    //   margin-bottom: 10px;
    // }
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .create-shipment-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin: 10px 0;
    & > {
      .result-title {
        height: 50px;
        padding: 0 10px 0 20px;
        line-height: 50px;
        font-size: 16px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
