<!--
 * @Author: luozhikai
 * @Date: 2024-12-27 14:19:57
 * @LastEditors: luozhikai
 * @LastEditTime: 2025-01-09 13:27:58
 * @Description: 新增指定供货渠道
-->
<template>
  <div class="app-container shipment-management--creation">
    <DividerHeader>客户信息</DividerHeader>
    <HeaderList
      ref="headerListRef"
      @dataList="handleHeaderList"
      @attachmentListOnchange="attachmentListOnchange"
      :defaultData="detailData.supplyChannelHeaderList"
      :canOperate="true"
      :type="type"
      :supplierCustomerStr="supplierCustomerStr"
      :pageSource="pageSource"
    ></HeaderList>
    <DividerHeader>商品信息</DividerHeader>
    <ItemList
      ref="itemListRef"
      @dataList="handleItemList"
      :defaultData="detailData.supplyChannelItemList"
      :canOperate="true"
      :type="type"
      :supplierCustomerStr="supplierCustomerStr"
      :pageSource="pageSource"
    ></ItemList>
    <el-row type="flex" justify="center" class="mv10">
      <el-button type="primary" :loading="saveLoading" @click="handleSave" v-if="hasPermission('保存')">保存</el-button>
      <el-button type="primary" @click="handleBack">返回</el-button>
    </el-row>
  </div>
</template>

<script>
import { fetchDetailData, saveData } from '@/api/shipmentManagement.js'

import DividerHeader from '@/components/DividerHeader'
import HeaderList from './components/supplyToCustomer/StcHeaderList'
import ItemList from './components/supplyToCustomer/StcItemList'
import { mapState } from 'vuex'
import { isEmpty } from 'lodash'

export default {
  name: 'ShipmentCreation',
  components: {
    DividerHeader,
    HeaderList,
    ItemList
  },
  data() {
    const { id } = this.$route.params
    return {
      id,
      detailData: {
        supplyChannelHeaderList: [],
        supplyChannelItemList: []
      },
      type: '',
      tempHeaderList: [],
      tempItemList: [],
      tempAttachmentList: [],
      pageSource: '',
      supplierCustomerStr: ['SUPPLY_TO_CUSTOMER', 'REPORT_SUPPLY_TO_CUSTOMER'],
      saveLoading: false
    }
  },
  created() {
    if (this.id !== '0') {
      this.pageSource = 'edit'
      this.fetchDetailData()
    } else {
      this.type = this.$route.query.type
      this.pageSource = 'create'
      this.tempHeaderList = []
      this.tempItemList = []
    }
  },
  mounted() {
  },
  methods: {
    async fetchDetailData() {
      try {
        const res = await fetchDetailData(this.id)
        if (!res.success) {
          this.$message.error(res.msg || '获取详情失败')
          return
        }
        this.detailData = res.data
        this.type = this.detailData.supplyChannelHeaderList[0].deliveryMode
        // console.log(this.type)
      } catch (error) {
      }
    },
    handleHeaderList(data) {
      this.tempHeaderList = data
    },
    attachmentListOnchange(val) {
      this.tempAttachmentList = val
    },
    handleItemList(data) {
      this.tempItemList = data
    },
    handleSave() {
      this.$refs.headerListRef.$refs.commonPostForm.validate(async (valid) => {
        if (valid) {
          if (isEmpty(this.tempAttachmentList)) {
            this.$message.error('请上传附件')
            return
          }
          if (this.tempItemList.filter(item => !item.id || item.id >= 0).some(item => item.materialGroup !== this.tempItemList[0].materialGroup)) {
            this.$message.error('同一配置单仅允许填写同一物料组数据，请检查！')
            return
          }
          try {
            // this.tempHeaderList[0].chooseDeliveryModeReason = this.$refs.headerListRef.$refs.commonPostForm.model.chooseDeliveryModeReason
            // this.tempHeaderList[0].channelType = this.$refs.headerListRef.$refs.commonPostForm.model.channelType
            this.tempHeaderList.forEach(item => {
              item.chooseDeliveryModeReason = this.$refs.headerListRef.$refs.commonPostForm.model.chooseDeliveryModeReason
              item.channelType = this.$refs.headerListRef.$refs.commonPostForm.model.channelType
              item.deliveryMode = this.$refs.headerListRef.$refs.commonPostForm.model.deliveryMode
              item.attachmentList = this.tempAttachmentList
              return item
            })
            this.tempItemList.forEach(item => {
              item.channelType = this.$refs.headerListRef.$refs.commonPostForm.model.channelType
              item.deliveryMode = this.$refs.headerListRef.$refs.commonPostForm.model.deliveryMode
            })

            const data = {
              batchSupplyChannelItemReq: {
                supplyChannelItemList: this.tempItemList
              },
              supplyChannelHeaderList: this.tempHeaderList
            }
            this.saveLoading = true;
            const res = await saveData(data)
            this.saveLoading = false;
            if (!res.success) {
              this.$message.error(res.msg || '保存失败')
            } else {
              this.$message.success(res.msg || '保存成功')
              if (res.data) {
                this.$closeTag(this.$route.path)
                this.$router.push({
                  path: `/shipmentManagement/detailStc/${res.data}?type=${this.type}`
                })
              }
            }
          } catch (error) {
          }
        } else {
          this.$message.error('必填项不能为空')
        }
      })
    },
    handleBack() {
      this.$router.back()
    },
    handleDownload(url) {
      if (url) {
        window.open(url)
      }
    },
    hasPermission(permissionName) {
      const ownerPermission = this.getPermissions.filter(permission => permission.name === permissionName).length > 0
      // 非审核流程按钮权限
      if (ownerPermission) {
        return true
      }
      return false
    }
  },
  computed: {
    ...mapState(['menu']),
    editOrCreate() {
      let a = null
      if (this.$route.params.id === '0') {
        a = 'create'
      } else {
        a = 'edit'
      }
      return a
    },
    // 获取操作权限
    getPermissions() {
      return this.menu.filter(menu => menu.name === '基础配置')[0].children[0].children
    }
  }
}
</script>
<style lang="scss" scoped>
  .shipment-management--creation {
    .mv10 {
      margin: 10px 0;
    }
  }
</style>
