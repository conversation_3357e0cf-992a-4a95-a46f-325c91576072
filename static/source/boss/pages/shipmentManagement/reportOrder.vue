<template>
  <div class="page page-home report-order">
    <iframe
      ref="iframe"
      :height="iframe.height"
      width="100%"
      scrolling="auto"
      :src="iframeSrc"
      frameborder="0"></iframe>
  </div>
</template>
<script>
export default {
  name: 'receivePayment',
  data () {
    return {
      container: '',
      iframe: {
        height: 0,
        width: 0
      },
      bridge: 'IframeBridge',
      iframePath: 'IframeBridge'
    }
  },
  created () {
    window.onresize = this.throttle(this.resize, 800)
    console.log(this.$route);
  },
  mounted () {
    this.container = this.$refs.iframe.parentNode
    this.resize()
  },
  beforeRouteLeave (to, from, next) {
    this.iframePath = this.bridge
    next()
  },
  computed: {
    iframeSrc () {
      let ret = 'https://boss.zkh360.com/so/sale'
      if (/boss-uat/.test(location.href)) {
        ret = 'https://boss-uat-4.zkh360.com/so/sale'
      }
      if (/fetest|localhost|local/.test(location.href)) {
        ret = 'http://local.zkh360.com:9004/so/sale'
      }
      return ret
    }
  },
  methods: {
    resize () {
      try {
        let rect = this.container && this.container.getBoundingClientRect()
        this.iframe.width = rect.width - 40
        this.iframe.height = window.innerHeight - 40 - 50
      } catch (err) {
        console.log(err)
      }
    },
    debounce (fn, delay = 800) {
      let timer
      return function () {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    },
    throttle (fn, delay = 800) {
      let timer
      return function () {
        if (timer) return
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    }
  }
}
</script>
<style>
.report-order{
  padding: 0px;
  overflow: hidden;
  position: relative;
}
</style>
