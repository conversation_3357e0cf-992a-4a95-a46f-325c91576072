<!--
 * @Author: l<PERSON>zhi<PERSON>
 * @Date: 2024-12-27 14:20:24
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-12-30 14:59:21
 * @Description: 指定供货渠道详情
-->
<template>
  <div class="app-container shipment-management--detail" v-loading="loading">
    <DividerHeader>
      <div class="flex justify-between">
        <span>客户信息</span>
        <span>
          <el-button type="primary" @click="handleEdit" v-if="operationList.includes('编辑')">编辑</el-button>
          <el-button type="primary" @click="handleSubmit" v-if="operationList.includes('提交')">提交</el-button>
          <!-- <el-button type="primary" @click="handleDelete" v-if="operationList.includes('删除')">删除</el-button> -->
          <el-button type="primary" @click="openConfirmDialog('SUBMIT')" v-if="operationList.includes('初审确认')">初审确认</el-button>
          <el-button type="primary" @click="openConfirmDialog('APPROVAL')" v-if="operationList.includes('审批')">审批</el-button>
          <el-button type="primary" @click="openConfirmDialog('REJECT')" v-if="operationList.includes('驳回')">驳回</el-button>
          <el-button type="primary" @click="openConfirmDialog('DRAFT')" v-if="operationList.includes('取消审核')">取消审核</el-button>
        </span>
      </div>
    </DividerHeader>
    <HeaderList
      :type="type"
      :approval="approval"
      :defaultData="detailData.supplyChannelHeaderList"
      :defaultItemData="detailData.supplyChannelItemList"
      :supplierCustomerStr="supplierCustomerStr"
      :pageSource="pageSource">
    </HeaderList>
    <DividerHeader>商品信息</DividerHeader>
    <ItemList
      :type="type"
      :defaultData="detailData.supplyChannelItemList"
      :supplierCustomerStr="supplierCustomerStr"
      :pageSource="pageSource"></ItemList>
    <DividerHeader>操作记录</DividerHeader>
    <OperationRecord />
    <ConfirmDialog :visible.sync='confirmDialogVisible' @confirm='handleConfirm' :confirmType='confirmType' />
  </div>
</template>

<script>
import { fetchDetailData, approveCustomerPosition, deleteCustomer } from '@/api/shipmentManagement.js'
import { searchContactListByGroup } from '@/api/orderSale'
import DividerHeader from '@/components/DividerHeader'
import HeaderList from './components/supplyToCustomer/StcHeaderList'
import ItemList from './components/supplyToCustomer/StcItemList'
import OperationRecord from './components/supplyToCustomer/OperationRecord'
import ConfirmDialog from './components/supplyToCustomer/ConfirmDialog'

export default {
  name: 'shipmentDetail',
  data() {
    const { id } = this.$route.params
    return {
      id,
      type: '',
      confirmDialogVisible: false,
      supplierCustomerStr: ['SUPPLY_TO_CUSTOMER', 'REPORT_SUPPLY_TO_CUSTOMER'],
      confirmType: '',
      detailData: {
        supplyChannelHeaderList: [],
        supplyChannelItemList: []
      },
      show: {
        hiddenFilter: false
      },
      searchForm: {
        customerNo: '',
        vkorg: '',
        selectedSalesRange: '',
        // 指定联系人
        orderContact: null,
        receiverContact: null,
        receiverAddress: null

      },
      selectedCustomer: null,
      saleOrgList: [],
      contactList: [],
      contactListWithAddress: [],
      loadingContact: false,
      isAddress: true,
      basisListLoading: false,
      pageSource: 'detail',
      approval: null,
      loading: false
    }
  },
  components: {
    DividerHeader,
    HeaderList,
    ItemList,
    OperationRecord,
    ConfirmDialog
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    this.type = this.$route.query.type
  },
  computed: {
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    operationList() {
      let operationList = this.detailData.supplyChannelHeaderList[0]?.buttonSet || []
      return operationList

      // 操作按钮权限控制迁移到后端
      // 草稿|审批驳回
      // if (approval === 'DRAFT' || approval === 'REJECT') {
      //   operationList = _intersection(['提交', '编辑', '删除'], ownerPermissions)
      // }
      // if (ownerPermission && ~['提交', '编辑', '删除'].indexOf(premissionName) && (approval === 'DRAFT' || approval === 'REJECT')) {
      //   return true
      // }
      // 审核中
      // if (approval === 'SUBMIT') {
      //   operationList = _intersection(['审批', '驳回'], ownerPermissions)
      // }
      // if (ownerPermission && ~['审批', '驳回'].indexOf(premissionName) && approval === 'SUBMIT') {
      //   return true
      // }
      // 审核通过
      // if (approval === 'APPROVAL') {
      //   operationList = _intersection(['取消审核'], ownerPermissions)
      // }
      // if (ownerPermission && premissionName === '取消审核' && approval === 'APPROVAL') {
      //   return true
      // }
      // return false
    }
  },
  mounted() {
    this.fetchDetailData()
  },
  watch: {
    'detailData.supplyChannelHeaderList': {
      handler(val) {
        if (val && val.length > 0) {
          this.approval = val[0].approval
        }
      }
    }
  },
  methods: {
    handleEdit() {
      this.$closeTag(this.$route.path)
      // this.$closeTag('/shipmentManagement/create');
      this.$router.push(`/shipmentManagement/createStc/${this.id}?type=${this.type}`)
    },
    handleDelete() {
      deleteCustomer(this.id).then((res) => {
        this.$message.success('删除成功')
        this.$closeTag(this.$route.path)
        this.$router.push({
          path: '/shipmentManagement/basisList'
        })
      })
    },
    openConfirmDialog(type) {
      this.confirmType = type
      this.confirmDialogVisible = true
    },
    handleConfirm(reason) {
      const data = {
        approval: this.confirmType,
        approvalReason: reason
      }
      this.approvePosition(data)
    },
    handleSubmit() {
      const data = {
        approval: 'PRODUCT_MANAGER_REVIEW',
        approvalReason: ''
      }
      this.approvePosition(data)
    },
    approvePosition(data) {
      approveCustomerPosition(this.id, data).then((res) => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          this.fetchDetailData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async fetchDetailData() {
      this.loading = true
      try {
        const res = await fetchDetailData(this.id)
        if (!res.success) {
          this.$message.error(res.msg || '获取详情失败')
          return
        }
        this.detailData = res.data
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
    toggle() {
      this.show.hiddenFilter = !this.show.hiddenFilter
    },
    handleSelectedCustomer(data) {
      this.selectedCustomer = data
    },
    handleSaleOrgList(data) {
      this.saleOrgList = (data || []).map((item, idx) => {
        const {
          salesOrganization,
          productGroup,
          distributionChannel,
          salesOrganizationName,
          distributionChannelName,
          productGroupName
        } = item
        return {
          data: {
            idx,
            ...item
          },
          key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
          value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`
        }
      })
    },
    handleQueryContactList(contactName, initFn, callbackFn) {
      const { customerNo } = this.searchForm
      if (this.searchForm.selectedSalesRange) {
        const {
          salesOrganization,
          productGroup,
          distributionChannel
        } = this.searchForm.selectedSalesRange
        initFn && initFn()
        searchContactListByGroup({
          customerCode: customerNo,
          contactName,
          distributionChannel,
          productGroup,
          salesOrganization
        }).then((res) => {
          callbackFn && callbackFn(res)
        })
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        })
      }
    },
    queryContactList(contactName) {
      this.handleQueryContactList(
        contactName,
        () => {
          this.loadingContact = true
        },
        (res) => {
          this.loadingContact = false
          if (res && res.code === 200 && res.data.records.length > 0) {
            const headerData = {
              contactName: '联系人',
              contactId: '联系人编号',
              contactPhone: '联系人电话',
              address: '联系人地址'
            }
            this.contactList = [
              headerData,
              ...res.data.records
            ]
            this.contactListWithAddress = [
              headerData,
              ...res.data.records.filter(
                (item) => item.addressId
              ).map(item => {
                item.address = item.provinceName + item.cityName + item.regionName + item.addressDetail
                return item
              })
            ]
          }
        }
      )
    },
    changeContact() {
    },
    handleFilter() {
      console.log(this.searchForm)
    },
    handleReset() {
      this.$refs.searchForm.resetFields()
    }
  }
}
</script>
<style lang="scss">
  .shipment-management--detail {
    .flex {
      display: flex;
    }

    .justify-between {
      justify-content: space-between;
    }

    .btn-toggle {
      width: 32px;
      height: 32px;
      line-height: 32px;
      color: #fff;
      border-radius: 32px;
      text-align: center;
      background-color: #597bee;
      margin: 10px 0 0;
      transform: rotate(-180deg);
      transition: transform .3s ease;

      &.on {
        transform: rotate(0deg);
      }
    }
  }
</style>
