<template>
  <div class="rateCfg-container">
    <template v-if="hasPremission('查看')">
      <el-form
          ref='searchForm'
          :model='searchForm'
          label-width='120px'
          label-position='right'
          class="search-container"
        >
        <el-row>
          <el-col :span=8>
            <el-form-item label="物料组" prop="productGroupId">
              <remoteSelect :type="'productGroupId'" :placeholder="'支持多选'" v-model="searchForm.productGroupId" :multiple="true" :optionLabel="'name'" :optionValue="'id'"></remoteSelect>
            </el-form-item>
          </el-col>
          <el-col :span=8>
            <el-form-item label="工厂" prop="factory">
              <remoteSelect :type="'factory'" :placeholder="'支持单选'" v-model="searchForm.factory" :optionLabel="'factoryName'" :optionValue="'factory'" :focusLoad="true"></remoteSelect>
            </el-form-item>
          </el-col>
          <el-col :span=8>
            <el-form-item label="品牌" prop="brandNo">
              <remoteSelect :type="'brandNo'" :placeholder="'支持多选'" v-model="searchForm.brandNo" :multiple="true" :optionLabel="'brandName'" :optionValue="'brandId'"></remoteSelect>
            </el-form-item>
          </el-col>
          <el-col :span=8>
            <el-form-item label="类目" prop="catalogs">
              <catalog-cascader v-model="searchForm.catalogs"></catalog-cascader>
            </el-form-item>
          </el-col>
          <el-col :span=8>
            <el-form-item label="SKU" prop="skuNos">
              <el-input v-model="searchForm.skuNos" placeholder="支持多个，空格分隔，最多支持100个"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span=8 class="button-col">
            <el-button type="primary" @click="handleClickBtn('search')" size="mini" v-if="hasPremission('查询')">查询</el-button>
            <el-button @click="handleClickBtn('reset')" size="mini" v-if="hasPremission('查询')">重置</el-button>
          </el-col>
        </el-row>
      </el-form>
      <div class="display-flex">
        <label>当前默认履约费率：{{ defaultFee }}</label>
        <div>
          <el-button type="primary" @click="handleClickBtn('new')" size="mini" v-if="hasPremission('编辑')">新增</el-button>
          <el-button type="primary" @click="handleClickBtn('upload')" size="mini" v-if="hasPremission('编辑')">批量导入</el-button>
          <el-button type="primary" @click="handleClickBtn('batchDelete')" size="mini" :loading="delLoading" v-if="hasPremission('编辑')">批量删除</el-button>
          <el-button type="primary" @click="handleClickBtn('rateCFG')" size="mini" v-if="hasPremission('编辑')">默认履约费率配置</el-button>
          <el-button @click="handleClickBtn('export')" :loading="exportLoading" v-if="hasPremission('导出')">导出</el-button>
        </div>
      </div>
      <el-table
        :data="tableData"
        v-loading='listLoading'
        border
        fit
        highlight-current-row
        @selection-change="handleSelectionChange"
        height='500'>
        <el-table-column type="selection" width="50" fixed="left" />
        <el-table-column label="物料组" align='center' prop='productGroupName'></el-table-column>
        <el-table-column label="工厂" align='center' prop='factoryName'></el-table-column>
        <el-table-column label="品牌名称" align='center' prop='brandName'></el-table-column>
        <el-table-column label="类目" align='center' prop='catalogTxt'>
          <template slot-scope='{ row }'>
            {{modelCatalogTxt(row)}}
          </template>
        </el-table-column>
        <el-table-column label="SKU" align='center' prop='sku'></el-table-column>
        <el-table-column label="履约费率" align='center' prop='fee'></el-table-column>
        <el-table-column label="创建人" align='center' prop='creator'></el-table-column>
        <el-table-column label="创建时间" align='center' prop='gmtCreate'>
          <template slot-scope='{ row }'>
            {{row.gmtCreate | formatGMTime}}
          </template>
        </el-table-column>
        <el-table-column label="操作" align='center' prop='handle'>
          <template slot-scope='{ row }' v-if="hasPremission('编辑')">
            <el-button type="text" @click="handleClickRowBtn('check', row)">查看</el-button>
            <el-button type="text" @click="handleClickRowBtn('update', row)">修改</el-button>
            <el-button type="text" @click="handleClickRowBtn('delete', row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show='total > 0'
        :total='total'
        align='right'
        :page.sync='pageInfo.pageNum'
        :pageSizes='[10, 20, 30, 50, 100]'
        :limit.sync='pageInfo.pageSize'
        layout='total, sizes, prev, pager, next, jumper'
        @pagination='qryRateList'
      />
      <config-dialog v-if="configDialogInfo.visible" :type="configDialogInfo.type" :visible.sync="configDialogInfo.visible" :value="configDialogInfo.formValue" :callback="qryRateList"></config-dialog>
      <rate-dialog :visible.sync="rateDialogInfo.visible" :value="rateDialogInfo.formValue" :callback="resetDefaultRate"></rate-dialog>
      <upload-dialog :visible.sync="uploadDialogVisible"></upload-dialog>
    </template>
    <template v-else>
      <div class="wrapper">
      <h1>对不起，你没有该业务的查看权限无法查看，请联系相关管理员开通</h1>
    </div>
    </template>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { clone } from 'lodash'
import { qryRateList, qryDeafaultRate, delRate, exportRate } from '@/api/performanceRateCFG'
import { formatTime } from '@/utility/helper'
export default {
  components: {
    Pagination: () => import('@/components/Pagination'),
    remoteSelect: () => import('./components/remoteSelect'),
    configDialog: () => import('./components/configDialog'),
    rateDialog: () => import('./components/rateDialog'),
    uploadDialog: () => import('./components/uploadDialog'),
    catalogCascader: () => import('./components/catalogCascader')
  },
  data() {
    return {
      searchForm: {
        productGroupId: [],
        factory: '',
        brandNo: [],
        catalogs: [],
        skuNos: ''
      },
      defaultFee: '',
      tableData: [],
      listLoading: false,
      configDialogInfo: {
        formValue: {},
        type: '',
        visible: false,
        callback: null
      },
      rateDialogInfo: {
        formValue: {},
        visible: false
      },
      pageInfo: {
        pageNum: 1,
        pageSize: 20
      },
      total: 0,
      multipleSections: [],
      uploadDialogVisible: false,
      delLoading: false,
      exportLoading: false
    }
  },
  filters: {
    formatGMTime(val) {
      return formatTime(new Date(val))
    }
  },
  computed: {
    ...mapState(['menu']),
    modelCatalogTxt() {
      return function(rowData = {}) {
        const { firstCatalogName = '', secondCatalogName = '', thirdCatalogName = '', fourthCatalogName = '' } = rowData
        return [ firstCatalogName, secondCatalogName, thirdCatalogName, fourthCatalogName ].filter(item => item).join(' / ')
      }
    },
    // 获取操作权限
    getPermissions () {
      const { children = [] } = this.menu.filter(menu => menu.name === '基础配置')?.[0] || {}
      return (children.filter(menu => menu.name === '仓库履约费率配置')?.[0]?.children || []).map(item => item.name)
    },
    hasPremission() {
      return function(permissionName) {
        return this.getPermissions.includes(permissionName)
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleClickBtn(type) {
      const fetch = {
        new: () => {
          this.handleConfigDialog(type, {
            productGroupId: '',
            factory: '',
            brandNo: '',
            catalogs: [],
            skuNos: '',
            fee: null
          })
        },
        rateCFG: () => {
          this.handleRateDialog()
        },
        batchDelete: () => {
          if (!this.multipleSections.length) {
            this.$message.warning('请选择数据')
            return
          }
          this.delLoading = true
          this.delRateCFG(this.multipleSections.map(item => item.id))
        },
        upload: () => {
          this.uploadDialogVisible = true
        },
        search: () => {
          this.qryRateList()
        },
        reset: () => {
          this.searchForm = {
            productGroup: '',
            factory: '',
            brand: '',
            catalogs: [],
            skuNos: ''
          }
        },
        export: () => {
          this.exportRateList()
        }
      }
      fetch[type] && fetch[type]()
    },
    handleConfigDialog(type, formValue = {}) {
      this.configDialogInfo = {
        formValue: {
          ...formValue
        },
        type,
        visible: true
      }
    },
    handleRateDialog() {
      this.rateDialogInfo = {
        formValue: {
          defaultFee: this.defaultFee || null
        },
        visible: true
      }
    },
    handleClickRowBtn(type, rowData = {}) {
      const { firstCatalogNo = '', secondCatalogNo = '', thirdCatalogNo = '', fourthCatalogNo = '' } = rowData
      const fetch = {
        check: () => {
          this.handleConfigDialog(type, {
            ...rowData,
            catalogs: [+firstCatalogNo, +secondCatalogNo, +thirdCatalogNo, +fourthCatalogNo].filter(item => item),
            skuNos: rowData?.sku || '',
            fee: +rowData.fee
          })
        },
        update: () => {
          this.handleConfigDialog(type, {
            ...rowData,
            catalogs: [+firstCatalogNo, +secondCatalogNo, +thirdCatalogNo, +fourthCatalogNo].filter(item => item),
            skuNos: rowData?.sku || '',
            fee: +rowData.fee
          })
        },
        delete: () => {
          this.delRateCFG([rowData.id])
        }
      }
      fetch[type] && fetch[type]()
    },
    qryParams() {
      const { skuNos = '' } = this.searchForm
      const [ firstCatalogNo = '', secondCatalogNo = '', thirdCatalogNo = '', fourthCatalogNo = '' ] = this.searchForm?.catalogs || []
      let skuNosArr = (!!skuNos.trim() && skuNos.trim().replace(/[\s,，]/g, ' ').replace(/[\s]+/g, ',').split(',')) || []
      return {
        ...this.searchForm,
        skuNos: skuNosArr.filter(item => item),
        firstCatalogNo,
        secondCatalogNo,
        thirdCatalogNo,
        fourthCatalogNo
      }
    },
    getList() {
      this.qryRateList()
      this.qryDefaultRate()
    },
    resetDefaultRate({ defaultFee = '' }) {
      this.defaultFee = defaultFee
    },
    async qryRateList() {
      this.listLoading = true
      const { code = '400', data = [], msg = '请求失败', total = 0 } = await qryRateList({
        ...this.qryParams(),
        pageNo: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize
      }).finally(() => {
        this.listLoading = false
      })
      let isError = (code + '') !== '200'
      if (isError) {
        this.$message.error(msg)
        return
      }
      this.tableData = data
      this.total = total
    },
    async qryDefaultRate() {
      const { code = '400', data = '', msg = '操作失败' } = await qryDeafaultRate()
      let isError = (code + '') !== '200'
      isError && this.$message.error(msg)
      if (isError) return
      this.defaultFee = data
    },
    handleSelectionChange(selection) {
      this.multipleSections = clone(selection)
    },
    delRateCFG(ids = []) {
      this.$confirm('删除后将无法恢复,确定删除吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { code = '400', msg = '操作失败' } = await delRate(ids).finally(() => {
          this.delLoading = false
        })
        if ((code + '') !== '200') {
          this.$message.error(msg)
          return
        }
        this.qryRateList()
      }).catch(() => {
        this.delLoading = false
      })
    },
    async exportRateList() {
      this.exportLoading = true
      const { code = '400', msg = '操作失败', data = '' } = await exportRate({
        ...this.qryParams()
      }).finally(() => {
        this.exportLoading = false
      })
      let isError = (code + '') !== '200'
      this.$message[isError ? 'error' : 'success'](isError ? msg : data || '操作成功')
    }
  }
}
</script>

<style lang='scss' scoped>
.rateCfg-container{
  padding:10px 20px 0 5px;
  .search-container{
    padding-top: 18px;
    padding-right: 10px;
    background-color: #f4f4f4;
  }
  .button-col{
    margin-left: auto;
    float: right;
    text-align: right;
    margin-bottom: 10px;
    margin-left: auto;
  }
  .display-flex{
    display: flex;
    justify-content: space-between;
    margin: 20px 0 5px 0;
  }
}
.wrapper{
  margin-top: 100px;
  text-align: center;
}
</style>
