<template>
   <el-dialog
      width="400px"
      title="导入"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
    >
      <div class="upload-container">
        <el-button type="primary" :loading="downloadLoading" @click="downloadTemplate" class="wd-80 mg-b-10"
          >下载模板</el-button
        >
        <el-upload drag accept=".xlsx,.xls" ref="upload" action="#" :on-remove="handleRemove" :show-file-list="true" :multiple="true" :http-request="httpRequestHandle" :before-upload="beforeUpload" :file-list="attachmentList"  :limit="limitNum" :on-exceed="onExceed" v-if="dialogVisible">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          </el-upload>
      </div>
      <template #footer>
        <el-button type="primary" size="mini" @click="onSave">确定</el-button>
        <el-button size="mini" @click="onCancel">取消</el-button>
      </template>
    </el-dialog>
</template>

<script>
import { importRate, downloadTemplate } from '@/api/performanceRateCFG'
import { remove } from 'lodash'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      downloadLoading: false,
      attachmentList: [],
      fileSizeLimit: 5,
      limitNum: 1
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }

      }
  },
  watch: {
    visible(val) {
      val && (this.attachmentList = [])
    }
  },
  methods: {
    async downloadTemplate() {
      this.downloadLoading = false
      const { code = '400', msg = '操作失败', data = '' } = await downloadTemplate().finally(() => {
        this.downloadLoading = false
      })
      if ((code + '') !== '200') this.$message.error(msg)
      data && window.open(data)
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false
      // 校验大小
      const isGtLimit = file.size / 1024 / 1024 < this.fileSizeLimit
      if (!isGtLimit) {
        this.$message.error('上传文件不能超过' + this.fileSizeLimit + 'MB!')
      }
      return isGtLimit;
    },
    onExceed() {
      this.$message({
        message: `目前只能上传${this.limitNum}个文件`,
        type: 'warning'
      })
    },
    // 删除文件
    handleRemove(file, fileList) {
      remove(fileList, function (item) {
        return item.uid === file.uid
      });
      this.attachmentList = fileList.map(item => {
        return {
          ...item,
          name: item.name,
          url: item.url
        }
      })
    },
    async httpRequestHandle(file) {
      this.attachmentList.push({
        file: file.file,
        name: file.file.name
      })
    },
    async onSave() {
      const loading = this.$loading({
        lock: true,
        text: '上传中......',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      let formData = new FormData()
      formData.append('excelFile', this.attachmentList?.[0].file)
      const { code = '400', msg = '上传失败' } = await importRate(formData).finally(() => {
        loading.close()
      })
      let isError = (code + '') !== '200'
      this.$message[isError ? 'error' : 'success'](isError ? msg : msg || '上传成功')
      !isError && this.onCancel()
    },
    onCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.upload-container{
  display: flex;
  flex-direction: column;
  align-items: center;
  .wd-80{
    width: 80px;
  }
  .mg-b-10{
    margin-bottom: 10px!important;
  }
}
</style>
