<template>
  <el-input
    ref="element"
    type="text"
    v-bind="$attrs"
    :value="currentValue"
    @input="handleChange"
    @focus="onFocus"
    @blur="onBlur"
    :maxlength="maxlength"
    :disabled="disabled"
    :placeholder="placeholder"
  >
    <slot name="suffix" slot="suffix"></slot>
    <slot name="append" slot="append"></slot>
  </el-input>
</template>

<script>

function isInputing(value, isInteger, isPositive) {
  let reg

  if (isPositive && isInteger) {
    return false
  } else if (isInteger) {
    reg = /(^$)/
  } else if (isPositive) {
    reg = /(^[0-9]+\.{1}$)/
  } else {
    reg = /(^-$)|(^-?[0-9]+\.$)/
  }
  return reg.test(value) && !/\s+/g.test(value)
}
function isValid(value, isInteger, isPositive, precision) {
  let reg
  if (isPositive && isInteger) {
    reg = /^[0-9]*$/
  } else if (isInteger) {
    reg = /(^-[0-9]+$)|(^[0-9]*$)/
  } else if (isPositive) {
    if (!isNaN(Number(value))) {
      const index = value.indexOf('.')
      if ((index === -1 || !precision || (precision >= value.length - index - 1)) && !/\s+/g.test(value)) {
        return true
      }
      return false
    } else {
      return false
    }
  } else {
    reg = /(^-?[0-9]+\.{1}\d+$)|(^-?[1-9][0-9]*$)|(^-?0{1}$)/
  }
  return reg.test(value) && !/\s+/g.test(value);
}

export default {

  // 定义来自父级的参数
  props: {
    max: {
      type: Number,
      default: Infinity
    },
    min: {
      type: Number,
      default: -Infinity
    },
    value: {
      type: [String, Number],
      default: null
    },
    step: {
      type: Number,
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    integer: {
      type: Boolean,
      default: false
    },
    positive: {
      type: Boolean,
      default: false
    },
    maxlength: {
      type: Number
    },
    precision: {
      type: Number
    },
    placeholder: {
      type: String,
      default: '请输入数字'
    }
  },
  data: function () {
    // Vue组件是单向数据流，无法直接修改prop中的值
    var currentValue = 0;
    if (this.value == null || this.value === '') {
      currentValue = this.value;
    } else if (this.value > this.max) {
      currentValue = this.max;
    } else if (this.value < this.min) {
      currentValue = this.min;
    } else {
      currentValue = this.value;
    }
    return {
      currentValue: currentValue
    }
  },
  watch: {
    // 增加监听
    currentValue: function (val) {
      // val === ''文本框可以输入空字符
      if (isInputing(val, this.integer, this.positive) && val !== '') {
        return
      }
      this.$emit('input', val);
      this.$emit('change', val);
      // this.$emit('update:value', val)
    },
    value: function (val) {
      this.updateVal(val);
    }
  },
  methods: {
    focus() {
      this.$refs.element.focus()
    },
    blur() {
      this.$refs.element.blur()
    },
    onFocus(e) {
      this.$emit('focus', e)
    },
    onBlur(e) {
      this.$emit('blur', e)
    },
    handleChange(val) {
      if (val === '' || isInputing(val, this.integer, this.positive)) {
        this.currentValue = val;
      }
      // console.log(val, 'vaasd')
      if (isValid(val, this.integer, this.positive, this.precision)) {
        var max = this.max;
        var min = this.min;
        this.currentValue = val;
        if (val > max) this.currentValue = max;
        if (val < min) this.currentValue = min;
      }
    },
    updateVal(val) {
      if (val > this.max) val = this.max;
      if (val < this.min) val = this.min;
      this.currentValue = val;
    }
  }

}
</script>
