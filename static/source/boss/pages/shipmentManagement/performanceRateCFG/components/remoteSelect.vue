<template>
  <el-select
    :ref="`${type}remoteSelect`"
    :value="modelValue"
    filterable
    clearable
    remote
    reserve-keyword
    :placeholder="placeholder"
    style="flex: 1; width: 100%"
    :remote-method="queryList"
    :loading="loading"
    @change="onChange"
    :multiple="multiple"
    :disabled="disabled"
    @focus="remoteFocus"
  >
    <el-option
      v-for="opt in modelOpts"
      :key="opt.value"
      :label="opt[optionLabel]"
      :value="opt[optionValue]">
    </el-option>
  </el-select>
</template>

<script>
import {
  getAllFactory,
  getEntityListApi,
  getBrandListApi
} from '@/api/performanceRateCFG'
export default {
  props: {
    type: {
      type: String,
      default: ''
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    optionLabel: {
      type: String,
      default: 'label'
    },
    optionValue: {
      type: String,
      default: 'value'
    },
    keyLabel: {
      type: String,
      default: ''
    },
    formData: {
      type: Object,
      default: () => { }
    },
    labelProp: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    focusLoad: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      options: [],
      apiFetch: {
        brandNo: getBrandListApi,
        productGroupId: getEntityListApi,
        factory: getAllFactory,
        fourthCatalogNo: getEntityListApi
      },
      selectedOpts: []
    }
  },
  computed: {
    modelValue: {
      get() {
        return this.value
      },
      set(val) {
        const { [this.optionLabel]: optLabel = '' } = this.options.filter(item => item[this.optionValue] === val)?.[0] || {}
        this.$emit('update:keyLabel', optLabel)
        this.$emit('input', val)
        if (this.multiple) {
          this.selectedOpts = this.selectedOpts.reduce((pre, cur) => {
            const { [this.optionLabel]: label = '', [this.optionValue]: value = '' } = cur
            if (val.includes(value)) {
            pre.push({
              [this.optionLabel]: label,
              [this.optionValue]: value
            })
}
            return pre
          }, [])
          let selectedVals = this.selectedOpts.map(item => item[this.optionValue])
          val.map(valItem => {
            if (!selectedVals.includes(valItem)) {
              this.selectedOpts.push(this.options.filter(item => item[this.optionValue] === valItem)?.[0])
            }
          })
        }
      }
    },
    defaultOpts: {
      get() {
        if (!this.labelProp) {
          return []
        }
        const { [this.labelProp]: label = '', [this.type]: value = '' } = this.formData
        if (label && value) {
          return [{
            [this.optionLabel]: label,
            [this.optionValue]: value
          }]
        }
        return []
      },
      set() { }
    },
    modelOpts: {
      get() {
        return this.options.length ? this.options : this.defaultOpts
      },
      set() { }
    }
  },
  methods: {
    getParams(query = '') {
      const fetch = {
        productGroupId: {
          name: query,
          entityType: 'entity.productgroup'
        },
        brandNo: {
          brandName: query
        }
      }
      return fetch[this.type] || {}
    },
    remoteFocus() {
      if (this.focusLoad && !this.options.length) {
        this.queryList('', true)
      }
    },
    async queryList(query = '', remote = false) {
      query = query.trim()
      if (!query && !remote) return
      this.defaultOpts = []
      this.loading = true
      const { data = [], result = [] } = await this.apiFetch[this.type]({
        ...this.getParams(query)
      }).finally(() => {
        this.loading = false
      })
      let list = data.length ? data : result
      this.options = this.selectedOpts.concat(this.filterOpts(query, list))
    },
    onChange(val) {
      this.modelValue = val
    },
    filterOpts(query, list) {
      const fetch = {
        factory: () => {
          return list.filter(item => !!~item[this.optionLabel].indexOf(query) || !!~item[this.optionValue].indexOf(query)).map(item => ({
            ...item,
            factoryName: `${item.factory} ${item.factoryName}`
          }))
        }
      }
      return (fetch[this.type] && fetch[this.type]()) || list.slice()
    }
  }
}
</script>

<style>

</style>
