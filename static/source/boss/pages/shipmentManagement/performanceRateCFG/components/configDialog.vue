<template>
  <el-dialog :title="modelTitle" :visible.sync="dialogVisible" :close-on-click-modal="false" @close="onCancel" :width="width">
    <el-form ref="form" :model="modelValue" :rules="rules" label-width="120px" v-if="dialogVisible">
      <el-row>
        <el-col :span=12>
          <el-form-item label="物料组" prop="productGroupId">
            <remoteSelect :type="'productGroupId'" :placeholder="'必填、单选'" v-model="modelValue.productGroupId" :labelProp="'productGroupName'" :keyLabel.sync="modelValue.productGroupName" :optionLabel="'name'" :optionValue="'id'" :formData="value" :disabled="isDisabled('productGroupId')"></remoteSelect>
          </el-form-item>
        </el-col>
        <el-col :span=12>
          <el-form-item label="工厂" prop="factory">
            <remoteSelect :type="'factory'" :placeholder="'必填、单选'" v-model="modelValue.factory" :optionLabel="'factoryName'" :optionValue="'factory'" :keyLabel.sync="modelValue.factoryName" :formData="value" :labelProp="'factoryName'" :disabled="isDisabled('factory')" :focusLoad="true"></remoteSelect>
          </el-form-item>
        </el-col>
        <el-col :span=12>
          <el-form-item label="品牌" prop="brandNo">
            <remoteSelect :type="'brandNo'" :placeholder="'下拉筛选、单选'" v-model="modelValue.brandNo" :keyLabel.sync="modelValue.brandName" :optionLabel="'brandName'" :optionValue="'brandId'" :formData="value" :labelProp="'brandName'" :disabled="isDisabled('brandNo')"></remoteSelect>
          </el-form-item>
        </el-col>
        <el-col :span=12>
          <el-form-item label="类目" prop="catalogs">
            <catalog-cascader v-model="modelValue.catalogs" :labels.sync="catalogLabels" :disabled="isDisabled('catalogs')"></catalog-cascader>
          </el-form-item>
        </el-col>
        <el-col :span=24>
          <el-form-item label="SKU" prop="skuNos">
           <el-input v-model="modelValue.skuNos" placeholder="支持批量配置、复制粘贴，每个SKU一行" type="textarea" rows="4" :disabled="isDisabled('skuNos')"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span=24>
          <el-form-item label="履约费率" prop="fee">
           <number-input :placeholder="'支持输入大于0的数字'" v-model="modelValue.fee" :disabled="isDisabled('fee')"></number-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" size="mini" @click="onSave" :loading="loading" v-if="type !=='check'">确认保存</el-button>
      <el-button size="mini" @click="onCancel">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { saveRate, updateRate } from '@/api/performanceRateCFG'
export default {
  components: {
    remoteSelect: () => import('./remoteSelect'),
    numberInput: () => import('./numberInput'),
    catalogCascader: () => import('./catalogCascader')
  },
    props: {
      type: {
        type: String,
        default: ''
      },
      visible: {
        type: Boolean,
        default: false
      },
      width: {
        type: String,
        default: '800'
      },
      value: {
        type: Object,
        default: () => {
          return {}
        }
      },
      callback: {
        type: Function,
        default: null
      }
    },
  data() {
    return {
      rules: {
        productGroupId: [
          { required: true, message: '请选择物料组', trigger: ['change', 'blur'] }
        ],
        factory: [
          { required: true, message: '请选择工厂', trigger: ['change', 'blur'] }
        ],
        fee: [
          {
            required: true,
            validator: (rule, val, callback) => {
              if (val <= 0 || !val) {
                callback(new Error('只能输入大于0的数字'))
                return
              }
              callback()
            },
            trigger: ['change', 'blur']
          }
        ]
      },
      loading: false,
      catalogLabels: []
    }
  },
  computed: {
    modelTitle() {
      const fetch = {
        new: '新增',
        update: '编辑',
        check: '查看'
      }
      return `${fetch[this.type]}配置`
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    modelValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('value', val)
      }
    },
    isDisabled() {
      return function(propKey = '') {
        return this.type === 'check' || (this.type === 'update' && propKey !== 'fee') || false
      }
    }
  },
  methods: {
    onCancel() {
      this.dialogVisible = false
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    },
    async onSave() {
      const res = await this.validate()
      if (!res) return
      this.loading = true
      const fetch = {
        new: saveRate,
        update: updateRate
      }
      if (!fetch[this.type]) return
      const [ firstCatalogNo = '', secondCatalogNo = '', thirdCatalogNo = '', fourthCatalogNo = '' ] = this.modelValue?.catalogs || []
      const [ firstCatalogName = '', secondCatalogName = '', thirdCatalogName = '', fourthCatalogName = '' ] = this.catalogLabels
      const { code = '400', msg = '操作失败' } = await fetch[this.type]({
        ...this.modelValue,
        skuNos: ((this.modelValue?.skuNos || '').trim().replace(/[\s,，]/g, ' ').replace(/[\s]+/g, ',').split(',') || []).filter(item => item),
        firstCatalogNo,
        secondCatalogNo,
        thirdCatalogNo,
        fourthCatalogNo,
        firstCatalogName,
        secondCatalogName,
        thirdCatalogName,
        fourthCatalogName
      }).finally(() => {
        this.loading = false
      })
      if ((code + '') !== '200') {
        this.$message.error(msg)
        return
      }
      this.callback && this.callback()
      this.dialogVisible = false
    }
  }
}
</script>

<style>

</style>
