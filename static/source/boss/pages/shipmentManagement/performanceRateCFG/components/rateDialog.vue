<template>
   <el-dialog title="履约费率配置" :visible.sync="dialogVisible" :close-on-click-modal="false" @close="onCancel" :width="'500'">
    <el-form ref="form" :model="modelValue" :rules="rules" label-width="120px" v-if="dialogVisible">
      <el-row>
        <el-col :span=24>
          <el-form-item label="履约费率" prop="defaultFee">
           <number-input :placeholder="'支持输入大于0的数字'" v-model="modelValue.defaultFee"></number-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button type="primary" size="mini" @click="onSave" :loading="loading">确认保存</el-button>
      <el-button size="mini" @click="onCancel">取消</el-button>
    </template>
   </el-dialog>
</template>

<script>
import { saveDefaultRate } from '@/api/performanceRateCFG'
export default {
  components: {
    numberInput: () => import('./numberInput')
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    visible: {
      type: Boolean,
      default: false
    },
    callback: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      rules: {
        defaultFee: [
          {
            required: true,
            validator: (rule, val, callback) => {
              if (val <= 0 || !val) {
                return callback(new Error('只能输入大于0的数字'))
              }
              return callback()
            },
            trigger: 'change'
          }
        ]
      },
      loading: false
    }
  },
  computed: {
    modelValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('value', val)
      }
    },
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    onCancel() {
      this.dialogVisible = false
    },
    validate() {
      return new Promise((resolve, reject) => {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            resolve(true)
          } else {
            resolve(false)
          }
        })
      })
    },
    async onSave() {
      const res = await this.validate()
      if (!res) return
      this.loading = true
      const { code = '400', msg = '操作失败' } = await saveDefaultRate({
        ...this.modelValue
      }).finally(() => {
        this.loading = false
      })
      if ((code + '') !== '200') {
        this.$message.error(msg)
        return
      }
      this.callback && this.callback({
        ...this.modelValue
      })
      this.dialogVisible = false
    }
  }
}
</script>

<style>

</style>
