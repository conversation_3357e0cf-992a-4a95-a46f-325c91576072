<template>
   <el-cascader
      collapse-tags
      style="width: 100%"
      v-model="modelValue"
      ref="catalogRef"
      :options="categoryList"
      clearable
      filterable
      :props="cascaderProps"
      :emitPath="false"
      placeholder="请选择目录"
      :disabled="disabled"
    />
</template>

<script>
import { getCatalogTree } from '@/api/performanceRateCFG'
export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    },
    labels: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      cascaderProps: {
        value: 'id',
        label: 'name',
        checkStrictly: true
      },
      categoryList: []
    }
  },
  created() {
    this.getCatalogTree()
  },
  computed: {
    modelValue: {
      get() {
        return this.value.slice()
      },
      set(val) {
        this.$emit('input', val)
        this.onChange()
      }
    }
  },
  methods: {
    async getCatalogTree() {
      const { success = false, data = [], errDesc = '获取类目数据异常' } = await getCatalogTree({ type: 0 })
      if (!success) {
        this.$message.error(errDesc)
        return
      }
      this.categoryList = this.getTreeData(data);
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].children.length < 1) {
          data[i].children = undefined;
        } else {
          this.getTreeData(data[i].children);
        }
      }
      return data;
    },
    onChange() {
      this.$nextTick(() => {
        let pathLabels = (this.$refs['catalogRef']?.presentText || '').split(' / ')
        this.$emit('update:labels', pathLabels)
      })
    }
  }
}
</script>

<style>

</style>
