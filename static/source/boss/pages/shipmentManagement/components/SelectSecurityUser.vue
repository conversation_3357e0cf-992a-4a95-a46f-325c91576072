<template>
  <el-select
    :value="value"
    filterable
    remote
    clearable
    multiple
    placeholder="请输入姓名"
    :remote-method="queryUser"
    :loading="loadingUser"
    value-key="id"
    @change="changePerson"
  >
    <el-option
      v-for="item in userList"
      :key="item[valueKey]"
      :label="item.nickname + '（' + item.username + '）'"
      :value="item[valueKey]"
    >
    </el-option>
  </el-select>
</template>

<script>
import { getAccountByName } from '@/api/shipmentManagement.js';

export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [Array]
    },
    valueKey: {
      type: String,
      default: 'id'
    }
  },
  data() {
    return {
      loadingUser: false,
      userList: []
    };
  },
  methods: {
    changePerson(val) {
      this.$emit('change', val);
    },
    queryUser(val) {
      getAccountByName(val).then((res) => {
        if (res && res.length) {
          this.userList = res;
        }
      });
    }
  }
};
</script>

<style>
</style>
