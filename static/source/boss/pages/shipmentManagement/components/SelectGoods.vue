<template>
  <el-select
    class="search-input"
    :value="value"
    filterable
    clearable
    remote
    reserve-keyword
    value-key="skuNo"
    placeholder="请输入商品编号/名称"
    :remote-method="searchSkuList"
    :loading="isLoading"
    @change="handleSelectGoods"
    :disabled="disabled"
  >
    <el-option
      v-for="(item, index) in skuList"
      :key="item.skuNo"
      :label="'【' + item.skuNo + '】' + item.materialDescribe"
      :value="item"
      :disabled="index === 0"
    >
      <div
        class="ba-row-start selectSkuItem"
        :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
      >
        <div>{{ item.skuNo }}</div>
        <div>{{ `${item.materialDescribe || ""}` }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { searchSkuList } from '@/api/orderSale';

export default {
  props: ['value', 'disabled'],
  data() {
    return {
      skuList: [],
      isLoading: false
    };
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  methods: {
    searchSkuList(val) {
      const params = val;
      searchSkuList(params).then((res) => {
        if (res.code === 200) {
          if (Array.isArray(res.data) && res.data.length > 0) {
            this.skuList = [
              {
                skuNo: '商品编号',
                materialDescribe: '商品描述'
              },
              ...res.data
            ];
          }
        }
      });
    },
    handleSelectGoods(val) {
      this.$emit('change', val);
    }
  }
};
</script>

<style lang="scss" scoped>
.search-input {
  width: 100%;
}

.ba-row-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.selectSkuItem {
  div:nth-child(1) {
    width: 90px;
  }
  div:nth-child(2) {
    width: 500px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
