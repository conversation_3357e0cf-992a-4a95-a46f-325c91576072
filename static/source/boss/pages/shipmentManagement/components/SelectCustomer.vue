<template>
  <el-select
    ref="customer"
    :value="value"
    filterable
    clearable
    remote
    reserve-keyword
    placeholder="请输入客户编号/名称"
    style="flex: 1; width: 100%"
    value-key="customerNumber"
    :remote-method="queryCustomerList"
    :loading="loadingCustomer"
    @change="changeCustomerNo"
  >
    <el-option
      v-for="(item, index) in customerList"
      :key="item.customerId"
      :label="item.customerName"
      :value="item.customerNumber"
      :disabled="index === 0"
    >
      <div
        class="ba-row-start selectClientItem"
        :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
      >
        <div>{{ item.customerNumber }}</div>
        <div>{{ item.cityName }}</div>
        <div>{{ item.customerName }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
import { get, find } from 'lodash';
import { searchClients } from '@/api/orderSale';
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String]
    }
  },
  data() {
    return {
      customerList: [],
      loadingCustomer: false
    };
  },
  methods: {
    changeCustomerNo(val) {
      this.$emit('change', val);
      const selectedCustomer = find(
        this.customerList,
        (custom) => custom.customerNumber === val
      );
      this.customerList = this.customerList.filter((custom) => custom.customerNumber === val)
      const saleOrgList = get(selectedCustomer, 'saleOrgList');
      this.$emit('selectedCustomer', selectedCustomer);
      this.$emit('saleOrgList', saleOrgList);
    },
    queryCustomerList(customer) {
      this.loadingCustomer = true;
      searchClients(customer)
        .then((res) => {
          this.loadingCustomer = false;
          if (res && res.code === 200) {
            this.customerList = [
              {
                customerNumber: '客户编码',
                customerName: '客户名称',
                cityName: '城市'
              },
              ...res.data
            ];
          }
        })
        .catch((res) => {
          this.loadingCustomer = true;
        });
    }

  }
};
</script>

<style lang="scss">
.ba-row-start {
  display: flex;
}
.selectClientItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
}
.selectClientItem {
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 120px;
  }
  div:nth-child(3) {
    width: 300px;
    overflow: auto;
  }
}
</style>
