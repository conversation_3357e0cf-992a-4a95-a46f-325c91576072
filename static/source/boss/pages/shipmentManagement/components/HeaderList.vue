<template>
  <div class="shipment-management--header-list">
    <el-form
      v-if="canOperate"
      ref="postForm"
      label-width="120px"
      :validate-on-rule-change="false"
      label-position="right"
      :rules="postFormRules"
      :model="postForm"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="选择客户：" prop="customerNo">
            <SelectCustomer
              ref="selectCustomer"
              v-model="postForm.customerNo"
              @saleOrgList="handleSaleOrgList"
              @selectedCustomer="handleSelectedCustomer"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="销售范围：" prop="selectedSalesRange">
            <el-select
              clearable
              value-key="idx"
              v-model="postForm.selectedSalesRange"
              style="width: 100%"
              placeholder="请选择销售范围"
            >
              <el-option
                v-for="item in saleOrgList"
                :key="item.key"
                :label="item.value"
                :value="item.data"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="指定客户维度：" prop="customerChooseType">
            <el-select
              v-model="postForm.customerChooseType"
              style="width: 100%"
              placeholder="请选择客户维度"
              @change="changeCustomerType"
            >
              <el-option
                v-for="item in CustomerChooseTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.customerChooseType==='CUSTOMER_ORDER_CONTACT'">
          <el-form-item label="订单联系人：" prop="orderContact">
            <SelectContact
              title="订单联系人"
              :data.sync="postForm.orderContact"
              :disabled="!canAddOrderContact(postForm.customerChooseType)"
              :contactList="contactList"
              :loading="loadingContact"
              :remoteMethod="queryContactList"
              @changeContact="changeContact"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.customerChooseType==='CUSTOMER_RECEIVER_CONTACT'">
          <el-form-item label="收货联系人：" prop="receiverContact">
            <SelectContact
              title="收货联系人"
              :data.sync="postForm.receiverContact"
              :disabled="!canAddReceiverContact(postForm.customerChooseType)"
              :contactList="contactList"
              :loading="loadingContact"
              :remoteMethod="queryContactList"
              @changeContact="changeContact"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.customerChooseType==='CUSTOMER_RECEIVER_ADDRESS'">
          <el-form-item label="收货地址：" prop="receiverAddress">
            <SelectContact
              title="收货地址"
              :data.sync="postForm.receiverAddress"
              :disabled="!canAddReceiverAddress(postForm.customerChooseType)"
              :contactList="contactListWithAddress"
              :loading="loadingContact"
              :isAddress="isAddress"
              :remoteMethod="queryContactList"
              @changeContact="changeContact"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.customerChooseType==='CUSTOMER_RECEIVER_ADDRESS_BRIEF'">
          <el-form-item label="收货地址：" prop="provinceArray">
            <ProvinceCascaderAsync :checkStrictly="true" @provinceChange="provinceChange" style="width:100%" v-model="postForm.provinceArray" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="" prop="containSubsidiary">
            <el-checkbox
              v-model="postForm.containSubsidiary"
              true-label="1"
              false-label="0">
              集团(含子客户)
            </el-checkbox>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-form
      v-if="canOperate"
      label-width="120px"
      label-position="right"
      ref="commonPostForm"
      :rules="commonPostFormRules"
      :model="commonPostForm"
    >
      <el-row>
        <el-col :span="8">
          <el-form-item label="说明：" prop="chooseDeliveryModeReason">
            <el-input v-model="commonPostForm.chooseDeliveryModeReason" placeholder="请输入客户指定供货渠道/发货仓原因" />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="supplierCustomerStr.includes(type)">
          <el-form-item label="渠道类型：" prop="channelType">
            <el-select
              v-model="commonPostForm.channelType"
              clearable
              style="width: 100%"
              placeholder="请选择渠道类型："
              @change="changeCustomerType"
            >
              <el-option
                v-for="item in channelTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="指定类型：">
            <el-select v-model="commonPostForm.deliveryMode" disabled>
              <el-option v-for="item in deliveryModeList"
                         :key="item.key"
                         :label="item.label"
                         :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row v-if="canOperate" type="flex" :justify="supplierCustomerStr.includes(type)?'space-between':'end'" class="mb10">
      <el-upload
        style="display:inline-block;margin-top:10px"
        accept=".xlsx"
        action="/api-vc/v1/upload"
        :before-upload="$validateFileType"
        :limit="6"
        :on-remove="handleUploadRemove"
        :on-success="handleUploadSuccess"
        :file-list="attachmentList"
        v-if="supplierCustomerStr.includes(type)"
      >
        <el-button type="primary" v-if="hasPremission('上传附件')">上传附件</el-button>
      </el-upload>
      <div class="right">
        <el-popover placement="bottom" width="160">
          <div class="align-center" style="text-align:center">
            <el-button type="primary" @click="handleDownload"
            >下载模板
            </el-button
            >
            <el-upload
              style="display:inline-block;margin-top:10px"
              accept=".xlsx"
              action="/api-oms-supply-channel/one_stop/supply_channel/headers/import"
              :on-change="handleChange"
              :on-success="handleSuccess"
              :on-error="handleError"
              :show-file-list="false"
              :before-upload="handleBeforeUpload"
            >

              <el-button v-if="hasPremission('导入客户')" type="primary">导入客户</el-button>
            </el-upload>
          </div>
          <el-button type="primary" slot="reference"
          >导入客户
          </el-button>
        </el-popover>
        <el-button type="primary" @click="handleAdd" style="margin-left:10px">添加</el-button>
      </div>
    </el-row>

    <div class="item-datail" v-if="!canOperate">
      <div v-for="item in resultItems" :key="item.id" class="item-show">
        <span class="item-label"> {{ item.label }}:</span>
        <span
          class="item-data"
          :class="[
            item.approval == 'approval'
              ? approvalClass
              : item.name == 'supplyChannelNo'
              ? 'channel-data'
              : 'normal-data',
          ]"
          v-if="dataList.length > 0"
        >
        <span class="item-data" v-if="item.name == 'approval'" :class="[approvalClass]">
         {{ approval | approvalFilter }}
        </span>
        <span class="item-data normal-data" v-else-if="item.name == 'createTime' || item.name == 'updateTime'">
         {{ dataList[0][item.name] | formatDate }}
        </span>
        <span class="item-data normal-data" v-else-if="item.name == 'chooseDeliveryModeReason'">
         {{ dataList[0][item.name] }}
        </span>
        <span class="item-data" v-else-if="item.name=='attachmentList'">
          <el-button type="text" v-for="(i,index) in dataList[0][item.name]" :key="index" @click="openPicUrl(i.path)">
           {{ i.name }}
          </el-button>
        </span>
           <span class="item-data normal-data" v-else-if="item.name == 'channelType'">
         {{ getLabelByValue('channelTypeList', dataList[0][item.name]) }}
        </span>
        <span class="item-data" :class="[item.name == 'supplyChannelNo'
              ? 'channel-data'
              : 'normal-data',]" v-else>
          {{ dataList[0][item.name] }}
        </span>
        </span>
      </div>
      <slot name="icon"></slot>
    </div>
    <div class="detail-page-search" v-if="pageSource==='detail'">
      <div class="btn-toggle" @click="toggle" :class="{on: show.hiddenFilter}" slot="icon">
        <i class="el-icon-caret-top"></i>
      </div>
      <el-form
        v-if="show.hiddenFilter"
        ref="postForm"
        label-width="120px"
        :validate-on-rule-change="false"
        label-position="right"
        :model="postForm"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item label="选择客户：" prop="customerNo">
              <SelectCustomer
                v-model="postForm.customerNo"
                @saleOrgList="handleSaleOrgList"
                @selectedCustomer="handleSelectedCustomer"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="销售范围：" prop="salesOrganizationName">
              <el-input v-model="postForm.salesOrganizationName" placeholder="请输入销售范围" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="指定客户维度：" prop="customerChooseType">
              <el-select
                v-model="postForm.customerChooseType"
                style="width: 100%"
                placeholder="请选择客户维度"
                @change="changeCustomerType"
              >
                <el-option
                  v-for="item in CustomerChooseTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="postForm.customerChooseType==='CUSTOMER_ORDER_CONTACT'">
            <el-form-item label="订单联系人：" prop="orderContactName">
              <el-input v-model="postForm.orderContactName" placeholder="请输入订单联系人" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="postForm.customerChooseType==='CUSTOMER_RECEIVER_CONTACT'">
            <el-form-item label="收货联系人：" prop="receiverContactName">
              <el-input v-model="postForm.receiverContactName" placeholder="请输入收货联系人" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="postForm.customerChooseType==='CUSTOMER_RECEIVER_ADDRESS'">
            <el-form-item label="收货地址：" prop="receiverCompleteAddressDetail">
              <el-input v-model="postForm.receiverCompleteAddressDetail" placeholder="请输入收货地址" clearable></el-input>
            </el-form-item>
          </el-col>
          <div style="float:right;min-width:150px">
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="basisListLoading"
              @click="handleFilter"
            >查询
            </el-button
            >
            <el-button type="primary" @click="handleReset">重置</el-button>
          </div>
        </el-row>
      </el-form>
    </div>
    <vxe-table
      border
      resizable
      keep-source
      show-overflow
      height="400"
      highlight-current-row
      :scroll-y="{gt: 20}"
      :data="filterDataList">
      <vxe-table-column
        align="center"
        title="序号"
        type="seq"
        width="50"
      ></vxe-table-column>
      <vxe-table-column
        title="客户编码"
        align="center"
        field="customerNo"
      ></vxe-table-column>
      <vxe-table-column
        title="客户名称"
        align="center"
        field="customerName"
      ></vxe-table-column>
      <vxe-table-column
        title="销售组织"
        align="center"
        field="salesOrganizationName"
      ></vxe-table-column>
      <vxe-table-column
        title="指定客户维度"
        align="center"
        field="customerChooseType"
      >
        <template v-slot="{ row }">
          {{
            getLabelByValue('CustomerChooseTypeOptions', row.customerChooseType)
          }}
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="订单联系人"
        align="center"
        field="orderContactName"
      ></vxe-table-column>
      <vxe-table-column
        title="收货联系人"
        align="center"
        field="receiverContactName"
      ></vxe-table-column>
      <vxe-table-column
        title="收货地址"
        align="center"
        field="receiverCompleteAddressDetail"
      ></vxe-table-column>
      <vxe-table-column
        title="是否集团客户"
        align="center"
        field="containSubsidiary"
      >
        <template v-slot="{ row }">
          {{
            row.containSubsidiary | boolMapStr
          }}
        </template>
      </vxe-table-column>
      <vxe-table-column v-if="canOperate" title="操作" align="center">
        <template v-slot="{ row, $index }">
          <el-button type="text" @click="deleteRow(row, $index)"
          >删除
          </el-button
          >
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>

<script>
import { searchContactListByGroup } from '@/api/orderSale'
import { CustomerChooseTypeOptions, getLabelByValue, approvalFilter, formatDate, boolMapStr, deliveryModeList, channelTypeList } from '../helper'
import SelectCustomer from './SelectCustomer'
import { mapState } from 'vuex'
import ProvinceCascaderAsync from '@/components/SearchFields/provinceCascaderAsync'
import { safeRun } from '@/utils/index'

import SelectContact from '@/pages/orderSale/components/common/SelectContact'
import _ from 'lodash'

export default {
  name: 'headerList',
  components: {
    SelectCustomer,
    SelectContact,
    ProvinceCascaderAsync
  },
  props: {
    approval: {
      type: String
    },
    defaultData: {
      type: Array,
      default() {
        return []
      }
    },
    defaultItemData: {
      type: Array,
      default() {
        return []
      }
    },
    canOperate: {
      type: Boolean
    },
    type: {
      type: String
    },
    supplierCustomerStr: {
      type: Array
    },
    pageSource: {
      type: String
    }

  },
  data() {
    return {
      CustomerChooseTypeOptions,
      deliveryModeList,
      channelTypeList,
      isAddress: true,
      postForm: {
        customerNo: '',
        selectedSalesRange: null,
        customerChooseType: '',
        orderContact: null,
        receiverContact: null,
        receiverAddress: null,
        containSubsidiary: '0',
        customerReceiveAdressBrief: {
          province: '',
          provinceName: '',
          city: '',
          cityName: '',
          region: '',
          regionName: ''
        },
        provinceArray: []
      },
      commonPostForm: {
        channelType: '',
        chooseDeliveryModeReason: ''
      },
      selectedCustomer: null,
      dataList: [],
      contactList: [],
      contactListWithAddress: [],
      loadingContact: false,
      approvalClass: 'approval-data',
      showItems: [
        { label: '指定渠道ID', name: 'supplyChannelNo', id: '1' },
        { label: '当前状态', name: 'approval', id: '2' },
        { label: '原因', name: 'approvalReason', id: '3' },
        { label: '创建人', name: 'createUserName', id: '4' },
        { label: '创建日期', name: 'createTime', id: '5' },
        { label: '修改日期', name: 'updateTime', id: '6' },
        { label: '说明', name: 'chooseDeliveryModeReason', id: '7' },
        { label: '相关证明附件', name: 'attachmentList', id: '8' },
        { label: '渠道类型', name: 'channelType', id: '9' }
      ],
      saleOrgList: [],
      show: {
        hiddenFilter: false
      },
      basisListLoading: false,
      uploadLoading: null,
      deleteIds: [],
      attachmentList: [],
      commonPostFormRules: {
        channelType: [
          { required: true, trigger: 'change', message: '请选择渠道类型！' }
        ],
        chooseDeliveryModeReason: [
          { required: true, trigger: 'change', message: '请输入说明！' }
        ]
      }

    }
  },
  created() {
  },
  filters: {
    approvalFilter,
    formatDate,
    boolMapStr
  },
  watch: {
    defaultData(val, oldVal) {
      this.dataList = [...this.defaultData]
      if (this.pageSource === 'edit') {
        console.log(this.dataList[0])
        this.attachmentList = this.dataList[0].attachmentList
        this.commonPostForm.chooseDeliveryModeReason = this.dataList[0].chooseDeliveryModeReason
        this.commonPostForm.channelType = this.dataList[0].channelType
        console.log(this.attachmentList)
      }
    },
    postForm: {
      handler(val, oldVal) {
        if (
          val.selectedSalesRange &&
          (this.canAddOrderContact(val.customerChooseType) ||
            this.canAddReceiverContact(val.customerChooseType) ||
            this.canAddReceiverAddress(val.customerChooseType))
        ) {
          if (!val.chooseDeliveryModeReason) {
            this.queryContactList()
          }
        }
      },
      deep: true
    },
    'dataList.length'() {
      if (this.dataList.length > 0) {
        switch (this.dataList.approval) {
          case 'APPROVAL':
            this.approvalClass = 'approval-data'
            break
          case 'REJECT':
            this.approvalClass = 'reject-data'
            break
          case 'DRAFT':
            this.approvalClass = 'draft-data'
            break
        }
      }
      this.$emit('dataList', this.dataList)
    },
    type: {
      immediate: true,
      handler(val) {
        console.log(val)
        if (val === 'ZKH_TO_CUSTOMER' || val === 'SPECIFY_WAREHOUSE') {
          this.postForm.supplyChannelScope = 'ALL'
        }
        this.commonPostForm.deliveryMode = this.type
      }
    },
    attachmentList: {
      deep: true,
      handler(val) {
        this.$emit('attachmentListOnchange', val)
        this.$emit('dataList', this.dataList)
        console.log(val)
      }
    }
  },
  computed: {
    ...mapState(['menu']),
    excelUrls () {
      return this.$store.state.orderCommon.excelUrls
    },
    postFormRules() {
      const rules = {
        customerNo: [
          { required: true, trigger: 'change', message: '请选择客户！' }
        ],
        selectedSalesRange: [
          { required: true, trigger: 'change', message: '请选择销售范围！' }
        ],
        customerChooseType: [
          {
            required: true,
            trigger: 'change',
            message: '请选择指定客户维度！'
          }
        ]
      }

      if (this.canAddOrderContact(this.postForm.customerChooseType)) {
        rules.orderContact = [
          {
            required: true,
            trigger: 'change',
            message: '请选择订单联系人！'
          }
        ]
      }

      if (this.canAddReceiverContact(this.postForm.customerChooseType)) {
        rules.receiverContact = [
          {
            required: true,
            trigger: 'change',
            message: '请选择收货联系人！'
          }
        ]
      }

      if (this.canAddReceiverAddress(this.postForm.customerChooseType)) {
        rules.receiverAddress = [
          {
            required: true,
            trigger: 'change',
            message: '请选择收货地址！'
          }
        ]
      }
      if (this.canAddReceiverAddressProviceCityRegion(this.postForm.customerChooseType)) {
        rules.provinceArray = [
          { required: true, trigger: 'change', message: '请选择收货地址！' }
        ]
      }

      return rules
    },
    filterParams() {
      var obj = this.pickPointedPropety(this.postForm, ['customerNo', 'salesOrganizationName', 'customerChooseType', 'orderContactName', 'receiverCompleteAddressDetail', 'receiverContactName'])
      return obj
    },
    filterDataList() {
      if (this.pageSource === 'edit') {
        return this.dataList.filter(item => (item.id >= 0 || item.id === undefined))
      } else {
        return this.dataList
      }
    },
    // 获取操作权限
    getPermissions() {
      return this.menu.filter(menu => menu.name === '基础配置')[0].children[0].children
    },
    resultItems() {
      let temp = this.showItems
      // if (this.type === 'REPORT_SUPPLY_TO_CUSTOMER') {
      //   temp = [].concat(this.showItems, [
      //     { label: '生效日期', name: 'validStartDate', id: '10' },
      //     { label: '失效日期', name: 'validEndDate', id: '11' }
      //   ])
      // } else {
      //   temp =
      // }
      return temp
    }
  },
  methods: {
    getLabelByValue,
    canAddOrderContact(customerChooseType) {
      return customerChooseType === 'CUSTOMER_ORDER_CONTACT'
    },
    canAddReceiverContact(customerChooseType) {
      return customerChooseType === 'CUSTOMER_RECEIVER_CONTACT'
    },
    canAddReceiverAddress(customerChooseType) {
      return customerChooseType === 'CUSTOMER_RECEIVER_ADDRESS'
    },
    canAddReceiverAddressProviceCityRegion(customerChooseType) {
      return customerChooseType === 'CUSTOMER_RECEIVER_ADDRESS_BRIEF'
    },
    checkProvinceArr(rule, value, callback) {
      if (!value) {
        return callback(new Error('客户收货地址(省市区)不能为空'))
      }
      if (value.length < 3) {
        callback(new Error('客户收货地址(省市区)请选到区级'))
      } else {
        callback()
      }
    },
    handleAdd() {
      this.$refs['postForm'].validate((valid) => {
        if (valid) {
          if (!this.selectedCustomer) {
            return
          }
          const {
            customerNo = '',
            selectedSalesRange = {},
            customerChooseType = '',
            orderContact = null,
            receiverContact = null,
            receiverAddress = null,
            customerReceiveAdressBrief,
            containSubsidiary
          } = this.postForm
          const params = {
            customerNo,
            customerName: this.selectedCustomer.customerName,
            customerChooseType,
            salesOrganization: selectedSalesRange.salesOrganization,
            salesOrganizationName: selectedSalesRange.salesOrganizationName,
            distributionChannel: selectedSalesRange.distributionChannel,
            productGroup: selectedSalesRange.productGroup,
            containSubsidiary
          }

          if (orderContact) {
            params.orderContactNo = orderContact.contactId
            params.orderContactName = orderContact.contactName
          }
          if (receiverContact) {
            params.receiverContactNo = receiverContact.contactId
            params.receiverContactName = receiverContact.contactName
          }
          if (receiverAddress) {
            params.receiverDistrictCode = receiverAddress.region
            params.receiverDistrictName = receiverAddress.regionName
            params.receiverCityCode = receiverAddress.city
            params.receiverCityName = receiverAddress.cityName
            params.receiverProvinceCode = receiverAddress.province
            params.receiverProvinceName = receiverAddress.provinceName
            params.receiverAddressDetail = receiverAddress.addressDetail
            params.receiverAddressId = receiverAddress.addressId
            params.receiverCompleteAddressDetail = receiverAddress.provinceName + receiverAddress.cityName + receiverAddress.regionName + receiverAddress.addressDetail
          }
          if (this.postForm.provinceArray.length) {
            params.receiverDistrictCode = customerReceiveAdressBrief.region
            params.receiverDistrictName = customerReceiveAdressBrief.regionName
            params.receiverCityCode = customerReceiveAdressBrief.city
            params.receiverCityName = customerReceiveAdressBrief.cityName
            params.receiverProvinceCode = customerReceiveAdressBrief.province
            params.receiverProvinceName = customerReceiveAdressBrief.provinceName
            params.receiverAddressDetail = customerReceiveAdressBrief.addressDetail
            params.receiverAddressId = customerReceiveAdressBrief.addressId
            params.receiverCompleteAddressDetail = customerReceiveAdressBrief.provinceName + (customerReceiveAdressBrief.cityName ? customerReceiveAdressBrief.cityName : '') + (customerReceiveAdressBrief.regionName ? customerReceiveAdressBrief.regionName : '')
          }
          this.dataList.push(params)
          this.$refs['postForm'].resetFields()
          this.$refs.selectCustomer.customerList = []
        }
      })
    },
    changeCustomerType(val) {
      if (this.pageSource === 'detail') {
        this.dataList = [...this.defaultData]
      }
      this.$set(this.postForm, 'orderContactName', '')
      this.$set(this.postForm, 'receiverContactName', '')
      this.$set(this.postForm, 'receiverCompleteAddressDetail', '')
      this.$refs['postForm'].clearValidate()
    },
    changeContact() {
    },
    handleQueryContactList(contactName, initFn, callbackFn) {
      const { customerNo } = this.postForm
      if (this.postForm.selectedSalesRange) {
        const {
          salesOrganization,
          productGroup,
          distributionChannel
        } = this.postForm.selectedSalesRange
        initFn && initFn()
        searchContactListByGroup({
          customerCode: customerNo,
          contactName,
          distributionChannel,
          productGroup,
          salesOrganization
        }).then((res) => {
          callbackFn && callbackFn(res)
        })
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        })
      }
    },
    queryContactList(contactName) {
      this.handleQueryContactList(
        contactName,
        () => {
          this.loadingContact = true
        },
        (res) => {
          this.loadingContact = false
          if (res && res.code === 200 && res.data.records.length >= 0) {
            const headerData = {
              contactName: '联系人',
              contactId: '联系人编号',
              contactPhone: '联系人电话',
              address: '联系人地址'
            }
            this.contactList = [
              headerData,
              ...res.data.records
            ]
            this.contactListWithAddress = [
              headerData,
              ...res.data.records.filter(
                (item) => item.addressId
              ).map(item => {
                item.address = item.provinceName + item.cityName + item.regionName + item.addressDetail
                return item
              })
            ]
          }
        }
      )
    },
    handleSelectedCustomer(data) {
      this.selectedCustomer = data
    },
    handleSaleOrgList(data) {
      this.saleOrgList = (data || []).map((item, idx) => {
        const {
          salesOrganization,
          productGroup,
          distributionChannel,
          salesOrganizationName,
          distributionChannelName,
          productGroupName
        } = item
        return {
          data: {
            idx,
            ...item
          },
          key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
          value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`
        }
      })
    },
    deleteRow(data, index) {
      // 前端删除的id 变成负的 大保存时让后端真正的删去
      if (data.id) {
        let obj = this.dataList.find(item => item.id === data.id)
        obj.id = -(obj.id)
      } else {
        this.dataList.splice(index, 1)
      }
    },
    toggle() {
      this.show.hiddenFilter = !this.show.hiddenFilter
    },
    pickPointedPropety(object, keyArr) {
      return _.pick(object, keyArr)
    },
    handleReset() {
      this.$refs.postForm.resetFields()
      this.dataList = [...this.defaultData]
    },
    handleFilter() {
      // 每次都从全量数据中查询
      this.dataList = [...this.defaultData]
      let filter = this.filterParams
      // 拿到有值的参数
      let tempFilter = {}
      for (var key in filter) {
        if (typeof (filter[key]) !== 'undefined' && (filter[key]) !== 'null' && filter[key] != null && filter[key] !== '') {
          tempFilter[key] = filter[key]
        }
      }
      // 若无查询条件 不搜索
      if (JSON.stringify(tempFilter) === '{}') {
        return
      }
      // 筛选
      let resultArr = this.dataList.filter(
        (item) => {
          let flag = false
          for (key in tempFilter) {
            if (item[key].toString().indexOf(tempFilter[key].toString()) >= 0) {
              flag = true
            } else {
              flag = false
              break
            }
          }
          if (flag) {
            return item
          }
        }
      )
      this.dataList = resultArr
    },
    handleDownload() {
      window.open(this.excelUrls?.supplyChannelCustomer)
    },
    handleError(error) {
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      )
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.uploadLoading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    handleSuccess(res, file) {
      if (this.uploadLoading) {
        this.uploadLoading.close()
      }
      if (res && res.data && res.data.length > 0) {
        this.dataList = [...this.dataList, ...res.data]
        if (res && res.code === 200) {
          this.$message.success(res.msg || '导入客户成功！')
        } else {
          this.$alert(res.msg.replace(/\r\n/g, '</br>') || '导入客户失败', '错误', {
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
      } else {
        this.$alert(res.msg.replace(/\r\n/g, '</br>') || '导入客户失败', '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    handleChange() {

    },
    handleUploadSuccess(response, file, fileList) {
      if (response && response.code === 0) {
        let { url } = response.data
        const index = url.lastIndexOf('.')
        if (index >= 0) {
          const post = url.substring(index)
          const reg = /\.(bmp|jpg|gif|jpeg|png)\b/i
          if (reg.test(post)) {
            url = `${url}?x-oss-process=style/common_style`
          }
        }
        const { name } = file
        this.attachmentList.push({
          name: name || 'file',
          path: url
        })
      }
    },
    handleUploadRemove(file, fileList) {
      this.attachmentList = fileList
    },
    openPicUrl(path) {
      let url = path
      try {
        if (url.indexOf('https') === -1) {
          url = 'https' + url.replace(/http(s)?/, '')
        }
      } catch (err) {
        url = path
      }
      window.open(url)
    },
    hasPremission(premissionName) {
      const ownerPermission = this.getPermissions.filter(premission => premission.name === premissionName).length > 0
      // 非审核流程按钮权限
      if (ownerPermission) {
        return true
      }
      return false
    },
    provinceChange(node) {
      safeRun(() => {
        this.postForm.customerReceiveAdressBrief.province = node?.[0]?.data?.code
        this.postForm.customerReceiveAdressBrief.provinceName = node?.[0]?.data?.label
        this.postForm.customerReceiveAdressBrief.city = node?.[1]?.data?.code
        this.postForm.customerReceiveAdressBrief.cityName = node[1]?.data?.label
        this.postForm.customerReceiveAdressBrief.region = node?.[2]?.data?.code
        this.postForm.customerReceiveAdressBrief.regionName = node?.[2]?.data?.label
      })
    }
  }
}
</script>

<style lang="scss">
  .shipment-management--header-list {
    .mb10 {
      margin-bottom: 10px;
    }

    .detail-page-search {
      margin-bottom: 5px;
    }

    .align-center {
      text-align: center;
    }

  }
</style>
<style scoped lang="scss">
  .item-datail {
    padding: 20px;
    padding-left: 50px !important;
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;

    .item-show {
      display: flex;
      width: 30%;
      line-height: 50px;

      .item-label {
        font-size: 14px;
        min-width: 96px;
      }

      .approval-data {
        font-size: 14px;
        color: #7c8f53;
      }

      .reject-data {
        font-size: 14px;
        color: red;
      }

      .draft-data {
        font-size: 14px;
        color: #c09e71;
      }

      .channel-data {
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
</style>
