<template>
  <el-select
    class="search-input"
    :value="value"
    filterable
    clearable
    remote
    reserve-keyword
    value-key="skuNo"
    placeholder="请输入产品组"
    :loading="isLoading"
    :disabled="disabled"
    @change="handleSelectSpart"
  >
    <el-option
      v-for="item in spartList"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    >
    </el-option>
  </el-select>
</template>

<script>
import { searchProductGroup } from '@/api/shipmentManagement';

export default {
  props: ['value', 'disabled'],
  data() {
    return {
      spartList: [],
      isLoading: false
    };
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  mounted() {
    this.searchProductGroup();
  },
  methods: {
    searchProductGroup() {
      searchProductGroup().then((res) => {
        // if (res.code === 200) {
        //   this.spartList = res.result.spart;
        // }
        this.spartList = res.data.spart;
      });
    },
    handleSelectSpart(val) {
      this.$emit('change', val);
    }
  }
};
</script>

<style lang="scss" scoped>
.search-input {
  width: 100%;
}
</style>
