<!--
 * @Author: l<PERSON>zhi<PERSON>
 * @Date: 2024-12-13 13:57:05
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-12-20 11:40:14
 * @Description: file content
-->
<template>
  <el-dialog
      title='提示'
      :visible.sync='dialogVisible'
      width='30%'
    >
    <el-form ref="confirmForm" :model="confirmForm" :rules="confirmRules" label-width="120px">
      <el-form-item :label="approvalMap[confirmType] + '理由：'" prop="confirmReason">
        <el-input
          type='textarea'
          :rows='4'
          placeholder='请输入理由'
          v-model='confirmForm.confirmReason'></el-input>
      </el-form-item>
    </el-form>
    <span slot='footer' class='dialog-footer'>
        <el-button @click='dialogVisible = false'>取 消</el-button>
        <el-button type='primary' @click='handleConfirm'
          >确 定</el-button
        >
      </span>
    </el-dialog>
</template>
<script>

const approvalMap = {
  'SUBMIT': '初审确认',
  'APPROVAL': '审核通过',
  'REJECT': '审核驳回',
  'DRAFT': '取消审核'
}

export default {
  name: 'ConfirmDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    confirmType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      approvalMap,
      confirmForm: {
        confirmReason: ''
      },
      confirmRules: {
        confirmReason: [
          { required: true, message: '请输入理由', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (val === false) {
        this.confirmForm.confirmReason = ''
      }
    }
  },
  methods: {
    handleConfirm() {
      this.$refs.confirmForm.validate((valid) => {
        if (valid) {
          this.$emit('confirm', this.confirmForm.confirmReason)
          this.dialogVisible = false
        } else {
          return false;
        }
      });
    }
  }
}
</script>
