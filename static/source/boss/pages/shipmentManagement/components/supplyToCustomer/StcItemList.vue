<!--
 * @Author: <PERSON><PERSON>zhi<PERSON>
 * @Date: 2024-12-27 14:22:55
 * @LastEditors: luozhikai
 * @LastEditTime: 2025-01-15 17:23:26
 * @Description: 指定供货渠道itemlist
-->
<template>
  <div class="shipment-management--item-list">
    <el-input
      v-model="searchText"
      placeholder="请输入商品编号或物料描述搜索"
      clearable
      class="search-abs"
      @input="handleSearch"
    >
    <div slot="prepend" v-if="searchIndex > -1">{{`${searchIndex + 1} / ${searchList.length}`}}</div>
    <div slot="append">
      <el-button icon="el-icon-arrow-up" @click="toPrev"></el-button>
      <el-button icon="el-icon-arrow-down" @click="toNext"></el-button>
    </div>
    </el-input>
    <el-form
      v-if="canOperate"
      ref="postForm"
      label-width="120px"
      :validate-on-rule-change="false"
      label-position="right"
      :rules="postFormRules"
      :model="postForm"
    >
      <el-row>
        <!--  :disabled="SupplyChannelScopeOptions.length===1" -->
        <el-col :span="8">
          <el-form-item label="指定商品维度：" prop="supplyChannelScope">
            <el-select
              v-model="postForm.supplyChannelScope"
              style="width: 100%"
              placeholder="请选择维度"
              @change="changeSupplyChannel"
            >
              <el-option
                v-for="item in SupplyChannelScopeOptions"
                :key="item.key"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.supplyChannelScope==='SKU'">
          <el-form-item label="选择商品：" prop="selectedGood">
            <SelectSku v-model="postForm.selectedGood" width="100%" needMaterialGroup @selectChange="handleSkuChange" />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.supplyChannelScope==='BRAND'">
          <el-form-item label="选择品牌：" prop="brandId">
            <RemoteBrand ref="remoteBrand" v-model="postForm.brandId"/>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="postForm.supplyChannelScope==='BRAND'">
          <el-form-item label="选择物料组：" prop="materialGroup">
            <MaterialGroup
              ref="materialGroup"
              v-model="postForm.materialGroup"
              @getlabel="setMaterialGroupName"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.supplyChannelScope==='SKU'">
          <el-form-item label="选择物料组：" prop="materialGroup">
            <el-input disabled v-model="postForm.materialGroupName" placeholder="请选择物料组" />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-show="false">
          <el-form-item label="选择发货方式：" prop="deliveryMode">
            <el-select
              :disabled="deliveryModeDisable"
              v-model="postForm.deliveryMode"
              style="width: 93%"
              placeholder="请选择发货方式"
              @change="changeDeliveryMode"
            >
              <el-option
                v-for="item in DeliveryModeOptions"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="选择发货方式：" prop="deliveryType">
            <el-select
              v-model="postForm.deliveryType"
              style="width: 100%"
              placeholder="请选择发货方式"
              :disabled="!canSelectSupplier(postForm.deliveryMode)"
              @change="changeDeliveryType"
            >
              <el-option label="直发" value="DIRECT"></el-option>
              <el-option label="系统挑仓" value="SYS_SELECT"></el-option>
              <el-option label="震坤行发货" value="ZKH" :disabled="canSelectSupplier(postForm.deliveryMode)"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="选择工厂：" prop="factory">
            <el-select
              :disabled="disabled"
              v-model="postForm.factory"
              value-key="name"
              style="width: 100%"
              placeholder="请选择"
              @change="chooseFac"
            >
              <el-option
                v-for="item in factoryList"
                :key="item.name"
                :label="item.value + ' ' + item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="supplierCustomerStr.includes(type)">
          <el-form-item label="选择仓位：" prop="position">
            <el-select
              :disabled="disabled"
              v-model="postForm.position"
              filterable
              clearable
              style="width: 100%"
              value-key="id"
              placeholder="请选择"
              @change="changePos"
            >
              <el-option
                v-for="(item, index) in storeWarehouseListFilterByFactory"
                :key="item.code + item.name + index"
                :label="(item.code !== -1 ? item.code : '') + item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8" v-if="(type==='ZKH_TO_CUSTOMER'||type==='SPECIFY_WAREHOUSE')&&postForm.supplyChannelScope==='ALL'">
          <el-form-item label="选择仓网类型：" prop="ruleCode">
            <el-select
              v-model="postForm.ruleCode"
              filterable
              clearable
              style="width: 100%"
              placeholder="请选择"
              @change="changeRuleCode"
            >
              <el-option
                v-for="(item, index) in ruleCodeList"
                :key="item.code + item.name + index"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="(type==='ZKH_TO_CUSTOMER'||type==='SPECIFY_WAREHOUSE')&&postForm.supplyChannelScope==='SKU'">
          <el-form-item label="选择仓网类型：" prop="ruleCode">
            <el-select
              v-model="postForm.ruleCode"
              filterable
              clearable
              style="width: 100%"
              placeholder="请选择"
              @change="changeSkuRuleCode"
            >
              <el-option
                v-for="(item, index) in ruleCodeList"
                :key="item.code + item.name + index"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="type==='ZKH_TO_CUSTOMER'||type==='SPECIFY_WAREHOUSE'">
          <el-form-item label="选择指定仓" prop="warehouseCode">
            <el-select
              v-model="postForm.warehouseCode"
              filterable
              clearable
              style="width: 100%"
              value-key="coverageAreaCode"
              placeholder="请选择"
              @change="changeWarehouseCode"
            >
              <el-option
                v-for="(item,index) in warehouseCodeList"
                :key="item.coverageAreaCode+index"
                :label="(item.warehouseCode !== -1 ? item.warehouseCode : '') + item.warehouseName"
                :value="item"

              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="type==='ZKH_TO_CUSTOMER'||type==='SPECIFY_WAREHOUSE'">
          <el-form-item label="选择指定仓位：" prop="position">
            <el-select
              v-model="postForm.position"
              filterable
              clearable
              style="width: 100%"
              value-key="code"
              placeholder="请选择"
              @change="changeDesignatedPos"
            >
              <el-option
                v-for="(item, index) in designatedPosition"
                :key="item.code + item.name + index"
                :label="(item.code !== -1 ? item.code : '') + ' '+ item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="选择供应商：" prop="supplierNo">
            <SelectSupplier
              placeholder="请输入供应商名称"
              style="width: 100%"
              @change="handleProviderChange"
              v-model="postForm.supplierNo"
              :disabled="!canSelectSupplier(postForm.deliveryMode)"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="生效日期：" prop="validDate">
            <el-date-picker
              v-model="postForm.validDate"
              type="datetimerange"
              unlink-panels
              placeholder="请选择生效日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width:100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="supplierCustomerStr.includes(type)">
          <el-form-item label="" prop="skipIfNoChannel">
            <el-checkbox v-model="postForm.skipIfNoChannel" style="width: 100%">
              <el-tooltip content="客户下单 sku，该指定供应商无可用采购价格时（删除/停用渠道），不会下单给该指定供应商。" placement="bottom">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
              商家不供货自动跳过
            </el-checkbox>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.supplyChannelScope && supplierCustomerStr.includes(type)">
          <el-form-item label="采购定价方式：" prop="purchasePricingType">
            <el-select
              v-model="postForm.purchasePricingType"
              filterable
              clearable
              style="width: 100%"
              placeholder="请选择"
              @change="purchasePricingTypeChange"
            >
              <el-option
                v-for="(item) in PurchasePricingTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="postForm.supplyChannelScope !== 'SKU' && item.value === 'SPECIAL_PRICE'"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.purchasePricingType === 'SPECIAL_PRICE' && supplierCustomerStr.includes(type)">
          <el-form-item label="采购价：" prop="purchasePrice">
            <el-input-number
              v-model="postForm.purchasePrice"
              placeholder="请输入采购价"
              clearable
              style="width: 100%"
              :controls="false"
              :min="0"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="8" v-if="postForm.purchasePricingType === 'COMMISSION_RATE' && supplierCustomerStr.includes(type)">
          <el-form-item label="佣金比率(%)：" prop="commissionRate">
            <el-input-number
              v-model="postForm.commissionRate"
              placeholder="请输入佣金比率"
              clearable
              style="width: 100%"
              :controls="false"
              :min="0"
              :max="100"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="end" class="mb10">
        <el-popover placement="bottom" width="160" v-if="supplierCustomerStr.includes(type)">
          <div class="align-center" style="text-align:center">
            <el-button type="primary" @click="handleDownload"
            >下载模板
            </el-button
            >
            <el-upload
              style="display:inline-block;margin-top:10px"
              accept=".xlsx"
              action="/api-oms-supply-channel/one_stop/supply_channel/items/import"
              :data="{'deliveryMode': type}"
              :on-change="handleChange"
              :on-success="handleSuccess"
              :on-error="handleError"
              :show-file-list="false"
              :before-upload="handleBeforeUpload"
            >
              <el-button v-if="hasPermission('导入客户')" type="primary">导入指定渠道</el-button>
            </el-upload>
          </div>
          <el-button type="primary" slot="reference"
          >导入指定渠道
          </el-button
          >
        </el-popover>
        <el-button type="primary" @click="handleAdd" style="margin-left:5px">添加</el-button>
      </el-row>
    </el-form>
    <vxe-table
      ref="xTable"
      border
      resizable
      keep-source
      show-overflow
      height="400"
      highlight-current-row
      :scroll-y="{gt: 20}"
      :data="filterDataList">
      <vxe-table-column
        align="center"
        title="序号"
        type="seq"
        width="50"
      ></vxe-table-column>
      <vxe-table-column
        title="指定商品维度"
        align="center"
        field="supplyChannelScope"
      >
        <template v-slot="{ row }">
          {{
            getLabelByValue('SupplyChannelScopeOptions', row.supplyChannelScope)
          }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="商品维度内容" align="center" field="customerName">
        <template v-slot="{ row }">
          {{ getContent(row) }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="物料组" align="center" field="materialGroup">
        <template v-slot="{ row }">
          {{ row.materialGroupName || '' }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="物料描述" align="center" field="materialDescribe">
        <template v-slot="{ row }">
          {{ row.supplyChannelScope === 'SKU' ? row.materialDescribe : '' }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="发货方式" align="center" field="deliveryType">
        <template v-slot="{ row }">
          {{ getLabelByValue('DeliveryTypeOptions', row.deliveryType) }}
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="工厂"
        align="center"
        field="factoryName"
      ></vxe-table-column>
      <vxe-table-column v-if="supplierCustomerStr.includes(type)" title="仓库地点" align="center" field="positionName">
        <template v-slot="{ row }">
          {{ `${row.position} ${row.positionName}` }}
        </template>
      </vxe-table-column>
      <vxe-table-column v-if="type==='ZKH_TO_CUSTOMER'||type==='SPECIFY_WAREHOUSE'||pageSource==='detail'" title="仓网类型" align="center" field="ruleCode">
        <template v-slot="{ row }">
          {{ (ruleCodeSourceList.find(item => item.code === row.ruleCode) || {}).name }}
        </template>
      </vxe-table-column>
      <vxe-table-column v-if="type==='ZKH_TO_CUSTOMER'||type==='SPECIFY_WAREHOUSE'||pageSource==='detail'" title="指定仓" align="center" field="warehouseCode">
        <template v-slot="{ row }">
          {{ `${row.warehouseCode} ${row.warehouseName}` }}
        </template>
      </vxe-table-column>
      <vxe-table-column v-if="type==='ZKH_TO_CUSTOMER'||type==='SPECIFY_WAREHOUSE'||pageSource==='detail'" title="指定仓位" align="center" field="position">
        <template v-slot="{ row }">
          {{ `${row.position} ${row.positionName}` }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="生效时间" align="center" field="validStartDate">
        <template v-slot="{ row }">
          {{ `${row.validStartDate || ''}` }}
        </template>
      </vxe-table-column>
      <vxe-table-column title="失效时间" align="center" field="validEndDate">
        <template v-slot="{ row }">
          {{ `${row.validEndDate || ''}` }}
        </template>
      </vxe-table-column>

      <vxe-table-column
        title="指定供应商"
        align="center"
        field="supplierName"
        v-if="pageSource==='detail'"
        width="100"
      >
        <template v-slot="{ row}">
          <el-link type="primary" @click="row.supplierUrl&&goToLink(row.supplierUrl)"> {{ row.supplierName }}</el-link>
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="指定供应商"
        align="center"
        field="supplierName"
        v-else
      >
      </vxe-table-column>
      <vxe-table-column
        title="商家不供货自动跳过"
        align="center"
        field="skipIfNoChannel"
      >
        <template v-slot="{ row}">
          {{ row.skipIfNoChannel ? '是' : '否'}}
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="采购定价方式"
        align="center"
        field="purchasePricingType"
      >
        <template v-slot="{ row}">
          {{ getLabelByValue('PurchasePricingTypeOptions', row.purchasePricingType) }}
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="采购价"
        align="right"
        field="purchasePrice"
      >
        <template v-slot="{ row}">
          <span :class="getPriceClass(row)">
            {{ row.purchasePrice || '--' }}
            <i class="el-icon-top" v-if="getPriceClass(row) === 'red-up-arrow'" />
            <i class="el-icon-bottom" v-else-if="getPriceClass(row) === 'green-down-arrow'" />
          </span>
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="历史采购价"
        align="right"
        field="historyAvgPurchasePrice"
      >
        <template v-slot="{ row }">
          {{ row.historyAvgPurchasePrice || '--' }}
        </template>
      </vxe-table-column>
      <vxe-table-column
        title="佣金比率"
        align="right"
        field="commissionRate"
      >
        <template v-slot="{ row}">
          {{ row.commissionRate ? row.commissionRate + '%' : '--'}}
        </template>
      </vxe-table-column>
      <vxe-table-column fixed="right" width="64" v-if="canOperate" title="操作" align="center">
        <template v-slot="{ row, rowIndex }">
          <el-link type="primary" @click="deleteRow(row, rowIndex)"
          >删除
          </el-link
          >
        </template>
      </vxe-table-column>
    </vxe-table>
  </div>
</template>

<script>
import RemoteBrand from '@/components/SearchFields/brand'
import MaterialGroup from '@/components/SearchFields/materialGroup'
import {
  getLabelByValue,
  SupplyChannelScopeOptions,
  PurchasePricingTypeOptions,
  DeliveryModeOptions,
  DeliveryTypeOptions,
  deliveryModeList
} from '../../helper'
import { getRuleCodeList, getwWarehouseCode, queryHistoryPrice } from '@/api/shipmentManagement'
import { mapState } from 'vuex'
import get from 'lodash/get'
import debounce from 'lodash/debounce'
import SelectSupplier from '@/components/SearchFields/consSupplier'
import SelectSku from '@/components/SearchFields/selectSku'
import moment from 'moment'

export default {
  name: 'StcItemList',
  components: {
    RemoteBrand,
    MaterialGroup,
    SelectSupplier,
    SelectSku
  },
  props: {
    defaultData: {
      type: Array,
      default() {
        return []
      }
    },
    canOperate: {
      type: Boolean
    },
    type: {
      type: String
    },
    supplierCustomerStr: {
      type: Array
    },
    pageSource: {
      type: String
    }
  },
  data() {
    return {
      SupplyChannelScopeOptions,
      DeliveryModeOptions,
      DeliveryTypeOptions,
      PurchasePricingTypeOptions,
      deliveryModeList,
      postForm: {
        supplyChannelScope: '',
        selectedGood: '',
        brandId: '',
        materialGroup: '',
        productGroup: '',
        deliveryMode: '',
        deliveryType: '',
        factory: '',
        position: '',
        supplierNo: '',
        ruleCode: '',
        warehouseCode: '',
        validDate: '',
        skipIfNoChannel: true,
        purchasePricingType: 'PURCHASE_PRICE'
      },
      dataList: [],
      disabled: false,
      uploadLoading: null,
      deliveryModeDisable: true,
      // 选择仓网类型
      ruleCodeList: [],
      // 选择指定仓：
      designatedPosition: [],
      // 选择指定仓位
      warehouseCodeList: [],
      ruleCodeSourceList: [],
      searchText: '',
      searchIndex: -1,
      searchList: []
    }
  },
  watch: {
    'postForm.validDate': {
      immediate: true,
      handler(val) {
        if (val && val.length > 0) {
          this.postForm.validStartDate = val[0]
          this.postForm.validEndDate = val[1]
        } else {
          this.postForm.validStartDate = ''
          this.postForm.validEndDate = ''
        }
      }
    },
    defaultData(val, oldVal) {
      this.dataList = [...this.defaultData]
    },
    'dataList.length'() {
      this.$emit('dataList', this.dataList)
    },
    factoryList(val) {
      if (val && this.supplierCustomerStr.includes(this.type)) {
        this.postForm.factory = this.findPointedFactory('1000')
      }
    },
    storeWarehouseListFilterByFactory(val) {
      if (val && this.supplierCustomerStr.includes(this.type)) {
        this.postForm.position = this.postForm.deliveryType === 'SYS_SELECT' ? {} : this.findPointedWareHose('1004')
      }
    },
    type: {
      immediate: true,
      handler(val) {
        if (this.supplierCustomerStr.includes(val)) {
          this.disabled = true
          if (this.factoryList) {
            this.postForm.factory = this.findPointedFactory('1000')
            this.postForm.position = this.postForm.deliveryType === 'SYS_SELECT' ? {} : this.findPointedWareHose('1004')
          }
        }
        if (val === 'ZKH_TO_CUSTOMER' || val === 'SPECIFY_WAREHOUSE') {
          this.SupplyChannelScopeOptions = this.SupplyChannelScopeOptions.filter(item => item.value === 'ALL' || item.value === 'SKU')
          this.postForm.supplyChannelScope = 'ALL'
        }
        this.postForm.deliveryMode = this.type
        let item = deliveryModeList.find((a) => a.value === this.type)
        this.postForm.deliveryType = item ? item.deliveryType : ''
        this.postForm.skipIfNoChannel = this.type !== 'REPORT_SUPPLY_TO_CUSTOMER'
      }
    },
    'postForm.factory': {
      deep: true,
      handler(val) {
        if (val) {
          this.postForm.ruleCode && this.changeRuleCode(this.postForm.ruleCode)
          this.postForm.warehouseCode = ''
          this.designatedPosition = []
        }
      }
    },
    getSkuRuleCodeListAndWareHouseListFlag(val) {
      if (val) {
        this.getSkuRuleCodeListAndWareHouseList()
      }
    }
  },
  created() {
    if (JSON.stringify(this.dictList) === '{}') {
      this.$store.dispatch('orderCommon/queryDictList')
    }
    if (!this.companyFactoryList || this.companyFactoryList.length === 0) {
      this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList')
    }
    if (!this.warehouseList || this.warehouseList.length === 0) {
      this.$store.dispatch('orderPurchase/queryFactoryWarehouseList')
    }
    getRuleCodeList({ level: 1 }).then((res) => {
      if (res && res.status === 200) {
        this.ruleCodeSourceList = res.result
        this.ruleCodeList = res.result
      } else {
        this.$message.error(res && res.msg)
      }
    })
  },
  computed: {
    ...mapState(['menu']),
    excelUrls () {
      return this.$store.state.orderCommon.excelUrls
    },
    // 获取操作权限
    getPermissions() {
      return this.menu.filter(menu => menu.name === '基础配置')[0].children[0].children
    },
    postFormRules() {
      const rules = {
        supplyChannelScope: [
          {
            required: true,
            trigger: 'change',
            message: '请选择指定商品维度！'
          }
        ],
        materialGroup: [
          {
            required: true,
            trigger: 'change',
            message: '请选择物料组！'
          }
        ],
        deliveryMode: [
          { required: true, trigger: 'change', message: '请选择发货方式！' }
        ],
        deliveryType: [
          { required: true, trigger: 'change', message: '请选择发货方式！' }
        ],
        factory: [
          {
            required: true,
            trigger: 'change',
            message: '请选择工厂！'
          }
        ],
        position: [
          {
            required: true,
            trigger: 'change',
            message: '请选择仓位！'
          }
        ],
        ruleCode: [
          {
            required: true,
            trigger: 'change',
            message: '请选择仓网类型！'
          }

        ],
        warehouseCode: [
          {
            required: true,
            trigger: 'change',
            message: '请选择指定仓位！'
          }
        ],
        validDate: [
          {
            required: true,
            trigger: 'change',
            message: '请设置生效时间！'
          },
          {
            validator: this.checkDateRange,
            trigger: 'change'
          }
        ],
        purchasePricingType: [
          { required: true, trigger: 'change', message: '请选择采购定价方式！' }
        ],
        purchasePrice: [
          { required: true, trigger: 'change', message: '请输入采购价！' }
        ],
        commissionRate: [
          { required: true, trigger: 'change', message: '请输入佣金比率！' }
        ]
      }

      if (this.canAdd(this.postForm.supplyChannelScope, 'SKU')) {
        rules.selectedGood = [
          {
            required: true,
            trigger: 'change',
            message: '请选择商品！'
          }
        ]
      }

      if (this.canAdd(this.postForm.supplyChannelScope, 'BRAND')) {
        rules.brandId = [
          {
            required: true,
            trigger: 'change',
            message: '请选择品牌！'
          }
        ]
      }

      if (this.canAdd(this.postForm.deliveryMode, 'SUPPLY_TO_CUSTOMER')) {
        rules.supplierNo = [
          {
            required: true,
            trigger: 'change',
            message: '请选择供应商！'
          }
        ]
      }
      return rules
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      factoryList: (state) => state.orderPurchase.factoryList,
      warehouseList: (state) => state.orderPurchase.warehouseList
    }),
    storeWarehouseListFilterByFactory() {
      const factoryCode = this.postForm.factory && this.postForm.factory.value
      let list = []
      if (factoryCode) {
        list = this.warehouseList.filter(adWareHouseItem => adWareHouseItem.factoryCode === factoryCode)
      } else {
        list = this.warehouseList
      }
      list.forEach(item => {
        item.name = item.warehouseLocationName
        item.code = item.warehouseLocationCode
      })
      return list
    },

    // 添加的数据 最终显示在页面的数据
    filterDataList() {
      if (this.pageSource === 'edit') {
        return this.dataList.filter(item => (item.id >= 0 || !item.id))
      } else {
        return this.dataList
      }
    },
    getSkuRuleCodeListAndWareHouseListFlag() {
      let flag = false
      if (this.postForm.selectedGood && this.postForm.selectedGood.skuNo && this.postForm.factory && this.postForm.factory.value) {
        flag = true
      }
      return flag
    }
  },
  methods: {
    setMaterialGroupName(val) {
      this.postForm.materialGroupName = val
    },
    getPriceClass(row) {
      const { purchasePrice, historyAvgPurchasePrice } = row
      if (!purchasePrice || !historyAvgPurchasePrice) {
        return ''
      }
      const rate = 0.5
      if (purchasePrice > historyAvgPurchasePrice * (1 + rate)) {
        return 'red-up-arrow'
      } else if (purchasePrice < historyAvgPurchasePrice * (1 - rate)) {
        return 'green-down-arrow'
      }
      return ''
    },
    handleSkuChange(val) {
      // 联动带出物料组
      this.postForm.materialGroup = val.materialGroupId
      this.postForm.materialGroupName = val.materialGroupName
      this.postForm.materialDescribe = val.materialDescribe
    },
    disabledDate(time) {
      return time.getTime() < moment(Date.now()).add(-1, 'days').valueOf()
    },
    checkDateRange(rule, value, callback) {
      const createTimeFrom = value[0]
      const createTimeTo = value[1]
      if (
        moment(createTimeTo)
          .diff(moment(createTimeFrom), 'day') < 1
      ) {
        callback(new Error('时间间隔至少一天'))
      } else {
        callback()
      }
    },
    hasPermission(permissionName) {
      const ownerPermission = this.getPermissions.filter(permission => permission.name === permissionName).length > 0
      // 非审核流程按钮权限
      if (ownerPermission) {
        return true
      }
      return false
    },
    getLabelByValue,
    // 对选择发货方式限制
    canSelectSupplier(deliveryMode) {
      return this.supplierCustomerStr.includes(deliveryMode)
    },
    // 切换制定商品纬度
    changeSupplyChannel(val) {
      this.postForm.selectedGood = null
      this.postForm.brandId = ''
      this.postForm.materialGroup = ''
      this.postForm.materialGroupName = ''
      this.postForm.productGroup = ''

      this.postForm.ruleCode = ''
      this.postForm.warehouseCode = ''
      this.postForm.position = this.postForm.deliveryType === 'SYS_SELECT' ? {} : ''
      this.ruleCodeList = []
      this.warehouseCodeList = []
      this.designatedPosition = []
      if (this.supplierCustomerStr.includes(this.type)) {
        this.postForm.position = this.postForm.deliveryType === 'SYS_SELECT' ? {} : this.findPointedWareHose('1004')
      }
      if (this.postForm.supplyChannelScope !== 'SKU' && this.postForm.purchasePricingType === 'SPECIAL_PRICE') {
        this.postForm.purchasePricingType = 'PURCHASE_PRICE'
      }

      this.$refs['postForm'].clearValidate()
    },
    // 切换选择发货方式
    changeDeliveryMode(val) {
      if (val === 'ZKH_TO_CUSTOMER' || val === 'SPECIFY_WAREHOUSE') {
        this.postForm.supplierNo = ''
        this.selectedSupplier = {}
        this.$refs['postForm'].clearValidate()
      }
    },
    changeDeliveryType(val) {
      if (val === 'SYS_SELECT') {
        this.postForm.position = {}
      }
    },
    canAdd(type, value) {
      return type === value
    },
    handleAdd() {
      this.$refs['postForm'].validate(async(valid) => {
        if (valid) {
          const {
            supplyChannelScope,
            selectedGood = '',
            brandId = '',
            deliveryMode,
            deliveryType,
            factory = {},
            position = {},
            warehouseCode = {},
            ruleCode,
            supplierNo = '',
            validDate = '',
            skipIfNoChannel,
            purchasePricingType,
            purchasePrice,
            commissionRate,
            materialGroupName,
            materialDescribe
          } = this.postForm

          // 把物料组id转换为数字
          let materialGroup = this.postForm.materialGroup
          try {
            materialGroup = Number(materialGroup)
          } catch (error) {
            console.log(error);
          }

          const params = {
            supplyChannelScope,
            deliveryMode,
            deliveryType,
            factory: factory.value,
            factoryName: factory.name,
            // position: position.warehouseLocationCode,
            // positionName: position.warehouseLocationName,
            // 选择指定仓位/库位
            position: position.code || '',
            positionName: position.name || '',
            // 选择指定仓
            warehouseCode: warehouseCode.warehouseCode,
            warehouseName: warehouseCode.warehouseName,
            ruleCode,
            supplierNo,
            validStartDate: validDate.length > 1 ? validDate[0] : '',
            validEndDate: validDate.length > 1 ? validDate[1] : '',
            supplierName: this.selectedSupplier === undefined ? null : this.selectedSupplier.providerName,
            supplierId: this.selectedSupplier === undefined ? null : this.selectedSupplier.providerId,
            skipIfNoChannel,
            purchasePricingType,
            materialGroup,
            materialGroupName
          }

          if (purchasePricingType === 'SPECIAL_PRICE') {
            if (purchasePrice === 0) {
              this.$message.error('采购价必填且不能为零！')
              return
            }
            if (!/^[+-]?\d+(?:\.\d{1,6})?$/.test(purchasePrice)) {
              this.$message.error('采购价最多为六位小数！')
              return
            }
            params.purchasePrice = purchasePrice

            const data = [
              {
                skuNo: selectedGood,
                factoryCode: factory.value,
                supplierNo: supplierNo
              }
            ]
            const res = await queryHistoryPrice(data)
            console.log(res);
            if (res.success) {
              params.historyAvgPurchasePrice = res.data[0]?.historyAvgPurchasePrice || ''
            }
          }

          if (purchasePricingType === 'COMMISSION_RATE') {
            if (commissionRate === 0) {
              this.$message.error('佣金比率必填且不能为零！')
              return
            }
            if (!/^[+-]?\d+(?:\.\d{1,2})?$/.test(commissionRate)) {
              this.$message.error('佣金比率最多为两位小数！')
              return
            }
            params.commissionRate = commissionRate
          }

          if (moment(params.validEndDate).isBefore(moment())) {
            this.$message.error('失效时间必须晚于当前时间')
            return
          }
          console.log(this.dataList, this.postForm, materialGroup)
          // id 大于0 表示是未删除行,没有id表示是新增的行
          if (this.dataList.some(item => (!item.id || item.id >= 0) && item.materialGroup !== materialGroup)) {
            this.$message.error('同一配置单仅允许填写同一物料组数据，请重新选择！')
            return
          }

          if (this.canAdd(this.postForm.supplyChannelScope, 'SKU')) {
            params.skuNo = selectedGood
            params.materialDescribe = materialDescribe
          }

          if (this.canAdd(this.postForm.supplyChannelScope, 'BRAND')) {
            params.brandId = brandId
            const brandName = get(
              this.$refs['remoteBrand'].brandIdOptions.find(
                (item) => item.value === brandId
              ),
              'label'
            )
            params.brandName = brandName
          }

          this.dataList.push(params)
          this.$refs['postForm'].resetFields()
          this.postForm.validDate = '';
          this.postForm.supplierNo = ''
          if (this.supplierCustomerStr.includes(this.type)) {
            this.postForm.factory = this.findPointedFactory('1000')
            this.postForm.position = this.postForm.deliveryType === 'SYS_SELECT' ? {} : this.findPointedWareHose('1004')
          }
          this.postForm.deliveryMode = this.type
          let item = deliveryModeList.find((a) => a.value === this.type)
          this.postForm.deliveryType = item ? item.deliveryType : ''
          this.postForm.skipIfNoChannel = this.type !== 'REPORT_SUPPLY_TO_CUSTOMER'
          console.log('this.dataList', this.dataList)
        }
      })
    },
    handleProviderChange(val, obj) {
      this.selectedSupplier = obj
    },
    deleteRow(data, index) {
      if (data.id) {
        let obj = this.dataList.find(item => item.id === data.id)
        obj.id = -(obj.id)
      } else {
        this.dataList.splice(index, 1)
      }
    },
    getContent(data) {
      // 1.SKU 2.品牌 BRAND 3.产品组 PRODUCT_GROUP 4 物料组 MATERIAL_GROUP 5.全部 ALL
      switch (data.supplyChannelScope) {
        case 'SKU':
          return data.skuNo
        case 'BRAND':
          return data.brandName
        case 'PRODUCT_GROUP':
          return data.productGroupName
        case 'MATERIAL_GROUP':
          return data.materialGroupName
        case 'ALL':
          return '-'
      }
    },
    findPointedWareHose(pointedWareHouse) {
      return this.storeWarehouseListFilterByFactory.find(item => item.code === pointedWareHouse)
    },
    findPointedFactory(pointedFactory) {
      return this.factoryList.find(item => item.value === pointedFactory)
    },
    handleDownload() {
      window.open(this.excelUrls?.stcSupplyChannelProduct)
    },
    handleChange() {

    },
    handleError(error) {
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      )
    },
    handleBeforeUpload(file) {
      if (!this.$validateFileType(file)) return false

      this.uploadLoading = this.$loading({
        lock: true,
        background: 'rgba(0, 0, 0, 0.5)'
      })
      return true
    },
    async handleSuccess(res, file) {
      console.log(res, file)
      if (this.uploadLoading) {
        this.uploadLoading.close()
      }
      if (res && res.data && res.data.length > 0) {
        const data = res.data
        // 筛选未删除的行
        const tempData = [...this.dataList, ...data].filter(item => (!item.id || item.id >= 0))
        // 检查物料组是否相同
        if (tempData.some(item => item.materialGroup !== tempData[0].materialGroup)) {
          this.$message.error('同一配置单仅允许填写同一物料组数据，请检查！')
          return
        }

        // 批量导入成功时，前端调接口获取历史平均采购价补充到行上
        let params = []
        res.data.forEach(async item => {
          const { skuNo, factory, supplierNo, supplyChannelScope, purchasePricingType } = item
          if (supplyChannelScope === 'SKU' && purchasePricingType === 'SPECIAL_PRICE') {
            params.push({
              skuNo,
              factoryCode: factory,
              supplierNo
            })
          }
        })
        const historyAvgPurchasePriceRes = await queryHistoryPrice(params)
        console.log(historyAvgPurchasePriceRes);
        if (historyAvgPurchasePriceRes.success) {
          data.forEach(item => {
            const { skuNo, factory, supplierNo } = item
            const historyAvgPurchasePrice = historyAvgPurchasePriceRes.data.find(item => item.skuNo === skuNo && item.factoryCode === factory && item.supplierNo === supplierNo)?.historyAvgPurchasePrice || ''
            item.historyAvgPurchasePrice = historyAvgPurchasePrice
          })
        }

        this.dataList = [...this.dataList, ...data]
        if (res && res.code === 200) {
          this.$message.success(res.msg || '导入客户成功！')
        } else {
          this.$alert(res.msg.replace(/\r\n/g, '</br>') || '导入客户失败', '错误', {
            type: 'error',
            dangerouslyUseHTMLString: true
          })
        }
      } else {
        this.$alert(res.msg.replace(/\r\n/g, '</br>') || '导入客户失败', '错误', {
          type: 'error',
          dangerouslyUseHTMLString: true
        })
      }
    },
    chooseFac(val) {
      this.postForm.position = this.postForm.deliveryType === 'SYS_SELECT' ? {} : ''
    },
    changePos(val) {

    },
    changeRuleCode(ruleCode) {
      // positionScope        库位范围：不传-不返回库位; 1-全量库位; 2-可售库位
      getwWarehouseCode({
        // ruleCodeLev: 1,depecrete
        skuSet: [],
        ruleCodeSet: [ruleCode],
        factorySet: this.postForm.factory ? [this.postForm.factory.value] : [],
        positionScope: 1
        // warehouseTypeSet: [ '100' ]
      }).then((res) => {
        if (res && res.status === 200) {
          this.warehouseCodeList = res.result
        } else {
          this.$message.error(res && res.msg)
        }
      })
    },
    changeSkuRuleCode() {

    },
    getSkuRuleCodeListAndWareHouseList() {
      const { skuNo } = this.postForm.selectedGood
      const { value: factoryCode } = this.postForm.factory
      // positionScope        库位范围：不传-不返回库位; 1-全量库位; 2-可售库位
      getwWarehouseCode({
        // ruleCodeLev: 1,depecrete
        skuSet: [skuNo],
        factorySet: [factoryCode],
        positionScope: 1
        // warehouseTypeSet: [ '100' ]
      }).then((res) => {
        if (res && res.status === 200) {
          this.warehouseCodeList = res.result
          this.ruleCodeList = this.pickSkuRuleList(res.result)
          console.log(this.ruleCodeList)
        } else {
          this.$message.error(res && res.msg)
        }
      })
    },
    pickSkuRuleList(data) {
      let ruleCodeList = []
      let ruleCodeObj = data && data[0]
      // const { parentRulCode: code, warehouseName: name } = ruleCodeObj
      const { parentRulCode: code } = ruleCodeObj
      let parentRulCodeName = (this.ruleCodeSourceList.find(item => item.code === code) || {}).name
      ruleCodeList.push({ code, name: parentRulCodeName })
      return ruleCodeList
    },
    changeWarehouseCode(selectedWareHouseCode) {
      this.designatedPosition = selectedWareHouseCode.allPosition
    },
    changeDesignatedPos(designatedPos) {
    },
    goToLink(url) {
      window.open(url)
    },
    handleSearch: debounce(function () {
      if (!this.searchText) {
        this.searchList = []
        this.searchIndex = -1
        return
      }
      let list = []
      this.filterDataList.forEach((a, i) => {
        let text = this.getContent(a)
        if (text.includes(this.searchText) || a.materialDescribe.includes(this.searchText)) {
          list.push(i)
        }
      })
      this.searchList = list
      this.searchIndex = 0
      this.tableChange()
    }, 1000),
    toPrev() {
      if (this.searchIndex === 0) {
        return
      }
      this.searchIndex -= 1
      this.tableChange()
    },
    toNext() {
      if (this.searchIndex === this.searchList.length - 1) {
        return
      }
      this.searchIndex += 1
      this.tableChange()
    },
    tableChange() {
      let index = this.searchList[this.searchIndex]
      this.$refs.xTable.scrollToRow(this.$refs.xTable.getData(index))
      this.$refs.xTable.setCurrentRow(this.$refs.xTable.getData(index))
    },
    purchasePricingTypeChange() {
      this.$refs['postForm'].clearValidate(['purchasePrice', 'commissionRate'])
    }

  }
}
</script>
<style lang="scss" scoped>
  .red-up-arrow {
    color: #F56C6C;
  }
  .green-down-arrow {
    color: #67C23A;
  }
</style>

<style lang="scss">
  .shipment-management--item-list {
    position: relative;
    .mb10 {
      margin-bottom: 10px;
    }

    .el-select {
      width: 100%;
    }
    .search-abs {
      position: absolute;
      right: 10px;
      top: -47px;
      width: 420px;
      height: 32px;
    }
    .el-row{
      display:flex;
      flex-wrap: wrap;
    }
  }
</style>
