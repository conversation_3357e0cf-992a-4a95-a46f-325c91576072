<!--
 * @Author: l<PERSON>zhi<PERSON>
 * @Date: 2024-12-11 11:23:00
 * @LastEditors: luozhikai
 * @LastEditTime: 2024-12-20 16:25:22
 * @Description: file content
-->
<template>
  <div>
    <vxe-table
      ref="xTable"
      border
      resizable
      keep-source
      show-overflow
      height="400"
      highlight-current-row
      :scroll-y="{gt: 20}"
      :data="dataList">
      <vxe-table-column field="gmtModified" align="center" title="操作时间"></vxe-table-column>
      <vxe-table-column field="operator" align="center" title="操作人"></vxe-table-column>
      <vxe-table-column field="operationType" align="center" title="操作类型"></vxe-table-column>
      <vxe-table-column field="statusChange" align="center" title="状态变化"></vxe-table-column>
      <vxe-table-column field="comment" align="center" title="备注"></vxe-table-column>
    </vxe-table>
  </div>
</template>

<script>
import { fetchDetailLog } from '@/api/shipmentManagement'

export default {
  name: 'OperationRecord',
  data() {
    return {
      dataList: []
    }
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    fetchData() {
      const { id } = this.$route.params
      fetchDetailLog(id).then((res) => {
        console.log('fetchDetailLog', res)
        if (res.success) {
          this.dataList = res.data
        }
      })
    }
  }
}
</script>
