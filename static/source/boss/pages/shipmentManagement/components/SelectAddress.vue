<template>
  <el-select
    ref="address"
    :value="value"
    filterable
    clearable
    remote
    reserve-keyword
    placeholder="请输入地址"
    style="flex: 1; width: 100%"
    value-key="customerNumber"
    :remote-method="queryCustomerList"
    :loading="loadingCustomer"
    @change="changeCustomerNo"
  >
      <el-option
      v-for="(item, index) in customerList"
      :key="item.customerId"
      :label="item.customerName"
      :value="item.customerNumber"
      :disabled="index === 0"
    >
      <div
        class="ba-row-start selectClientItem"
        :style="{ fontWeight: index === 0 ? 'bold' : 'normal' }"
      >
        <div>{{ item.customerNumber }}</div>
        <div>{{ item.cityName }}</div>
        <div>{{ item.customerName }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script>
export default {};
</script>

<style>
</style>
