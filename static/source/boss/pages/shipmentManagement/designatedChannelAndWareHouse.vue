<template>
    <div class="app-container" v-loading="pageLoading">
      <div class='filter-container'>
        <el-form  ref="postForm"
            label-width="120px"  label-position="right" :model="postForm">
          <el-row>
            <el-col :span=6>
              <el-form-item label="指定渠道ID" prop="supplyChannelNo">
                <el-input v-model="postForm.supplyChannelNo" placeholder="请输入指定渠道ID"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span=6>
              <el-form-item label="客户编码/名称" prop="customerNo">
                <SelectCustomer
                  v-model="postForm.customerNo"
                  @saleOrgList="handleSaleOrgList"
                  @selectedCustomer="handleSelectedCustomer"
                />
              </el-form-item>
            </el-col>
            <el-col :span=6>
              <el-form-item label="指定客户维度" prop="customerChooseType">
                <el-select
                v-model="postForm.customerChooseType"
                style="width: 100%"
                placeholder="请选择客户维度"
                @change="changeCustomerType"
              >
                <el-option
                  v-for="item in CustomerChooseTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="postForm.customerChooseType==='CUSTOMER_ORDER_CONTACT'">
              <el-form-item label="订单联系人" prop="orderContactName">
                <el-input v-model="postForm.orderContactName" placeholder="请输入订单联系人"></el-input>
              </el-form-item>
          </el-col>
           <el-col :span="6" v-if="postForm.customerChooseType==='CUSTOMER_RECEIVER_CONTACT'">
                <el-form-item label="收货联系人：" prop="receiverContactName">
                  <el-input v-model="postForm.receiverContactName" placeholder="请输入收货联系人"></el-input>
                </el-form-item>
              </el-col>
            <el-col :span="6"  v-if="postForm.customerChooseType==='CUSTOMER_RECEIVER_ADDRESS'">
                <el-form-item label="收货地址：" prop="receiverCompleteAddressDetail">
                  <el-input v-model="postForm.receiverCompleteAddressDetail"  placeholder="请输入收货地址"></el-input>
                </el-form-item>
            </el-col>

              <!-- <el-col :span="6">
                <el-form-item label="仓库地点：">
                  <el-select
                    v-model="postForm.position"
                    filterable
                    clearable
                    style="width: 100%"
                    placeholder="请选择"
                    >
                      <el-option
                        v-for="item in warehouseList"
                        :key="item.factoryCode + item.warehouseLocationCode"
                  :label="
                    item.warehouseLocationCode +
                      ' ' +
                      item.warehouseLocationName
                  "
                  :value="item.warehouseLocationCode"
                      />
                    </el-select>
                </el-form-item>
              </el-col> -->
              <el-col :span="6">
                <el-form-item label="指定商品维度：" prop="supplyChannelScope">
                    <el-select
                    v-model="postForm.supplyChannelScope"
                    style="width: 100%"
                    placeholder="请选择维度"
                    @change="changeSupplyChannel"
              >
                  <el-option
                    v-for="item in SupplyChannelScopeOptions"
                    :key="item.key"
                    :label="item.label"
                    :value="item.value"
                  />
              </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" v-if="postForm.supplyChannelScope==='SKU'">
              <el-form-item label="选择商品：" prop="selectedGood">
                <SelectGoods v-model="postForm.selectedGood" />
              </el-form-item>
             </el-col>
             <el-col :span="6" v-if="postForm.supplyChannelScope==='BRAND'">
              <el-form-item label="选择品牌：" prop="brandId">
                <RemoteBrand ref="remoteBrand" v-model="postForm.brandId"/>
              </el-form-item>
             </el-col>
            <el-col :span="6" v-if="postForm.supplyChannelScope==='MATERIAL_GROUP'">
            <el-form-item label="选择物料组：" prop="materialGroup">
              <MaterialGroup
                ref="materialGroup"
                v-model="postForm.materialGroup"
              />
            </el-form-item>
             </el-col>
            <el-col :span="6" v-if="postForm.supplyChannelScope==='PRODUCT_GROUP'">
              <el-form-item label="选择产品组：" prop="productGroup">
                <SelectProductGroup
                  ref="productGroup"
                  v-model="postForm.productGroup"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="选择供应商：" prop="supplierNo">
                    <SelectSupplier
                      placeholder="请输入供应商名称"
                      style="width: 100%"
                      @change="handleProviderChange"
                      v-model="postForm.supplierNo"
                    />
                </el-form-item>
            </el-col>
             <el-col :span="6">
             <el-form-item label='指定分类' prop='delivery_mode'>
                <el-select v-model="postForm.deliveryMode" style="width: 100%" clearable placeholder="请选择指定分类">
                  <el-option
                    v-for="item in deliveryModeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
            </el-form-item>
          </el-col>
          <el-col :span='6'>
            <el-form-item label='状态' prop='approval'>
              <el-select v-model="postForm.approval" style="width: 100%" clearable placeholder="请选择审核状态">
                <el-option
                  v-for="item in ApprovalStatusOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
             <el-col :span="6">
                <el-form-item label="选择工厂：" prop="factory">
                  <el-select
                    clearable
                    v-model="postForm.factory"
                    value-key="name"
                    style="width: 100%"
                    placeholder="请选择"
                    @change="chooseFac"
                  >
                    <el-option
                      v-for="item in factoryList"
                      :key="item.name"
                      :label="item.value + ' ' + item.name"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
            </el-col>
              <el-col :span="6">
                 <el-form-item label="选择仓网类型：" prop="ruleCode">
                      <el-select
                        v-model="postForm.ruleCode"
                        filterable
                        clearable
                        style="width: 100%"
                        placeholder="请选择"
                        @change="changeRuleCode"
                      >
                        <el-option
                          v-for="(item, index) in ruleCodeList"
                          :key="item.code + item.name + index"
                          :label="item.name"
                          :value="item.code"
                        />
                      </el-select>
                  </el-form-item>
              </el-col>
        <el-col :span="6" >
          <el-form-item label="选择指定仓" prop="warehouseCode">
            <el-select
              v-model="postForm.warehouseCode"
              filterable
              clearable
              style="width: 100%"
              value-key="coverageAreaCode"
              placeholder="请选择"
              @change="changeWarehouseCode"
            >
              <el-option
               :key="item.coverageAreaCode"
                v-for="item in warehouseCodeList"
                :label="(item.warehouseCode !== -1 ? item.warehouseCode : '') + item.warehouseName"
                :value="item"

              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6" >
          <el-form-item label="选择指定仓位：" prop="position">
            <el-select
              v-model="postForm.position"
              filterable
              clearable
              style="width: 100%"
              value-key="code"
              placeholder="请选择"
              @change="changeDesignatedPos"
            >
              <el-option
                v-for="(item, index) in designatedPosition"
                :key="item.code + item.name + index"
                :label="(item.code !== -1 ? item.code : '') + ' '+ item.name"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
            <el-col :span="6">
              <el-form-item>
                  <el-button @click="search" type="primary">查询</el-button>
                  <el-button @click="reset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="content-container">
        <el-row type="flex" justify="end">
          <el-button type="primary" @click="exportList" :loading="exportLoading"  v-if="hasPremission('导出')">导出</el-button>
        </el-row>
        <el-table border :data="tableData" v-loading="loading" height="500" style="margin-top:5px">
          <el-table-column
            show-overflow-tooltip
            align="center"
            v-for="item in columns"
            :key="item.field"
            :prop="item.field"
            :label="item.title"
            :width="item.width"
              >
            <template #default="scope">
                <div v-if="item.field === 'deliveryMode'">
                {{findInValue(deliveryModeList, scope.row.deliveryMode, 'value')}}
                </div>
              <div v-else-if="item.field === 'customerChooseType'">
                {{findInValue(CustomerChooseTypeOptions, scope.row.customerChooseType, 'value')}}
              </div>
              <div v-else-if="item.field === 'supplyChannelScope'">
                {{findInValue(SupplyChannelScopeOptions, scope.row.supplyChannelScope, 'value')}}
              </div>
                <div v-else-if="scope.row.supplyChannelScope === 'SKU'&&item.field==='supplyChannelContent'">
                {{scope.row.supplyChannelContent}}
              </div>
              <div v-else-if="scope.row.supplyChannelScope === 'BRAND'&&item.field==='supplyChannelContent'">
                {{scope.row.supplyChannelContent}}
              </div>
              <div v-else-if="scope.row.supplyChannelScope === 'PRODUCT_GROUP'&&item.field==='supplyChannelContent'">
                {{scope.row.supplyChannelContent}}
              </div>
              <div v-else-if="scope.row.supplyChannelScope === 'MATERIAL_GROUP'&&item.field==='supplyChannelContent'">
                {{scope.row.supplyChannelContent}}
              </div>
              <div v-else-if="item.field === 'deliveryModeForItem'">
                {{ getLabelByValue('DeliveryModeOptions',scope.row.deliveryModeForItem)}}
              </div>

              <div  v-else-if="item.field === 'approval'">
                  {{getLabelByValue('ApprovalStatusOptions',scope.row.approval)}}
              </div>
              <div v-else-if="item.field==='createTime'">
                {{formatDate(scope.row.createTime)}}
              </div>
              <div v-else-if="item.field==='updateTime'">
                {{formatDate(scope.row.updateTime)}}
              </div>
              <div v-else-if="item.field==='supplierName'">
                <el-button type="text" @click="scope.row.supplierName&&goToLink(scope.row.supplierUrl)">{{scope.row.supplierName}}</el-button>
              </div>
              <div v-else-if="item.field==='ruleCode'">
                 {{findInValue(ruleCodeList, scope.row.ruleCode, 'code') }}
              </div>
              <div v-else>{{ scope.row[item.field] }}</div>
            </template>
          </el-table-column>
        </el-table>
         <Pagination
            v-show="total > 0"
            :total="total"
            align="right"
            :page.sync="listQueryInfo.pageNo"
            :limit.sync="listQueryInfo.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            @pagination="getList('pagination')"
    />
      </div>

    </div>
</template>

<script>
import Pagination from '@/components/Pagination'

import RemoteBrand from '@/components/SearchFields/brand';
import MaterialGroup from '@/components/SearchFields/materialGroup';

import SelectCustomer from './components/SelectCustomer';
import SelectGoods from './components/SelectGoods';
import SelectProductGroup from './components/SelectProductGroup';

import SelectSupplier from '@/components/SearchFields/consSupplier';
import uniqBy from 'lodash/uniqBy';

import { getDesignatedChanelAndWareHouse, exportDesignatedChanelAndWareHouse, getRuleCodeList, getwWarehouseCode } from '@/api/shipmentManagement'
import { searchContactListByGroup } from '@/api/orderSale';

import { CustomerChooseTypeOptions, SupplyChannelScopeOptions, ApprovalStatusOptions, deliveryModeList, getLabelByValue } from './helper';
import { mapState } from 'vuex'
import moment from 'moment'
const columns = [
  {
    field: 'supplyChannelNo',
    title: '指定渠道ID',
    width: 150
  },
  {
    field: 'customerName',
    title: '客户名称',
    width: 200
  },
  {
    field: 'customerNo',
    title: '客户编码',
    width: 100
  },
  {
    field: 'deliveryMode',
    title: '指定分类',
    width: 100
  },
  {
    field: 'salesOrganizationName',
    title: '销售组织',
    width: 200
  },
  {
    field: 'customerChooseType',
    title: '指定客户维度',
    width: 150
  },
  // todo
  {
    field: 'orderContactName',
    title: '订单联系人',
    width: 100
  },
  {
    field: 'receiverContactName',
    title: '收货联系人',
    width: 100
  },
  //
  {
    field: 'receiverCompleteAddressDetail',
    title: '收货地址',
    width: 100
  },
  {
    field: 'supplyChannelScope',
    title: '指定商品维度',
    width: 100
  },
  {
    field: 'supplyChannelContent',
    title: '商品维度内容',
    width: 100
  },
  {
    field: 'materialDescribe',
    title: '物料描述',
    width: 100
  },
  {
    field: 'deliveryModeForItem',
    title: '发货方式',
    width: 100
  },
  {
    field: 'factoryName',
    title: '工厂',
    width: 200
  },
  {
    field: 'ruleCode',
    title: '仓网类型',
    width: 100
  },
  {
    // field: 'position',
    field: 'warehouseString',
    title: '选择指定仓',
    width: 200
  },
  {
    field: 'positionString',
    title: '选择指定仓位',
    width: 200
  },
  // {
  //   // field: 'positionName',
  //   field: 'positionString',
  //   title: '仓库地点',
  //   width: 100
  // },
  {
    field: 'supplierName',
    title: '指定供应商',
    width: 200
  },
  {
    field: 'createUserName',
    title: '创建人',
    width: 100
  },
  {
    field: 'createTime',
    title: '创建时间',
    width: 150
  },
  {
    field: 'updateTime',
    title: '修改时间',
    width: 150
  },
  {
    field: 'approval',
    title: '状态',
    width: 100
  }
]
const initPostForm = {
  supplyChannelNo: '',
  customerNo: '',
  customerChooseType: '',
  orderContactName: '',
  receiverContactName: '',
  receiverCompleteAddressDetail: '',
  supplyChannelScope: '',
  selectedGood: '',
  brandId: '',
  materialGroup: '',
  productGroup: '',
  supplierNo: ''
}
export default {
  props: {

  },
  data() {
    return {
      saleOrgList: [],
      CustomerChooseTypeOptions,
      deliveryModeList,
      SupplyChannelScopeOptions,
      ApprovalStatusOptions,
      contactList: [],
      postForm: {
        supplyChannelNo: '',
        customerNo: '',
        customerChooseType: '',
        orderContactName: '',
        receiverContactName: '',
        receiverCompleteAddressDetail: '',
        supplyChannelScope: '',
        selectedGood: '',
        brandId: '',
        materialGroup: '',
        productGroup: '',
        supplierNo: '',
        ruleCode: '',
        warehouseCode: '',
        factory: '',
        position: '',
        approval: ''
      },
      loadingContact: false,
      exportLoading: false,
      isAddress: true,
      contactListWithAddress: [],
      initPostForm,
      // 列表相关
      columns,
      tableData: [],
      loading: false,
      listQueryInfo: {
        pageNo: 1,
        pageSize: 50
      },
      total: 0,
      exportListData: [],
      // 选择仓网类型
      ruleCodeList: [],
      // 选择指定仓：
      designatedPosition: [],
      // 选择指定仓位
      warehouseCodeList: [],
      pageLoading: false

    };
  },
  components: {
    SelectCustomer,
    SelectSupplier,
    // SelectContact,
    SelectGoods,
    SelectProductGroup,
    RemoteBrand,
    MaterialGroup,
    Pagination
  },
  computed: {
    ...mapState({
      companyFactoryList: (state) => state.orderPurchase.companyFactoryList,
      factoryList: (state) => state.orderPurchase.factoryList,
      warehouseList: (state) => uniqBy(state.orderPurchase.warehouseList, 'warehouseLocationCode')
    }),
    ...mapState(['menu']),
    queryParams() {
      this.clean(this.postForm)
      let processdSearchForm = {}
      for (var k in this.postForm) {
        if (k === 'selectedGood') {
          processdSearchForm['supplyChannelContent'] = this.postForm[k].skuNo
        } else if (k === 'brandId' || k === 'materialGroup' || k === 'productGroup') {
          processdSearchForm['supplyChannelContent'] = this.postForm[k]
        } else if (k === 'orderContactName' || k === 'receiverContactName' || k === 'receiverCompleteAddressDetail') {
          processdSearchForm['customerChooseContent'] = this.postForm[k]
        } else if (k === 'warehouseCode') {
          processdSearchForm['warehouseCode'] = this.postForm[k].warehouseCode
        } else if (k === 'position') {
          processdSearchForm['position'] = this.postForm[k].code
        } else if (k === 'factory') {
          processdSearchForm['factory'] = this.postForm[k].value
        } else {
          processdSearchForm[k] = this.postForm[k]
        }
      }
      return processdSearchForm
    },
    // 获取操作权限
    getPermissions () {
      return this.menu.filter(menu => menu.name === '基础配置')[0].children[1].children
    }
  },
  async created() {
    this.pageLoading = true
    const plList = []
    if (!this.warehouseList || this.warehouseList.length === 0) {
      plList.push(this.$store.dispatch('orderPurchase/queryFactoryWarehouseList'))
    }
    plList.push(this.$store.dispatch('orderPurchase/queryCompanyAndFactoryList'))
    await Promise.all(plList)
    this.postForm.factory = this.findPointedFactory('1000')
    this.pageLoading = false
    // this.getList()
    this.getRuleCodeList()
  },
  watch: {
    postForm: {
      handler(val, oldVal) {
        if (
          val.selectedSalesRange &&
          (this.canAddOrderContact(val.customerChooseType) ||
            this.canAddReceiverContact(val.customerChooseType) ||
            this.canAddReceiverAddress(val.customerChooseType))
        ) {
          this.queryContactList();
        }
      },
      deep: true
    },
    'postForm.factory': {
      deep: true,
      handler(val, oldVal) {
        if (val) {
          console.log(val)
          this.postForm.warehouseCode = ''
          this.warehouseCodeList = []
          this.postForm.position = ''
          this.designatedPosition = []
        }
      }
    }

  },
  methods: {
    getRuleCodeList() {
      getRuleCodeList({ level: 1 }).then((res) => {
        if (res && res.status === 200) {
          this.ruleCodeList = res.result
        } else {
          this.$message.error(res && res.msg)
        }
      })
    },
    getwWarehouseCodeLlist() {
      // this.postForm.warehouseCode = ''
      // this.designatedPosition.position = ''
      // positionScope        库位范围：不传-不返回库位; 1-全量库位; 2-可售库位
      getwWarehouseCode({
        // ruleCodeLev: 1,
        ruleCodeSet: this.ruleCodeSet,
        factorySet: [this.postForm.factory.value],
        positionScope: 1
        // warehouseTypeSet: [ '100' ]
      }).then((res) => {
        if (res && res.status === 200) {
          this.warehouseCodeList = res.result
        } else {
          this.$message.error(res && res.msg)
        }
      })
    },
    handleSaleOrgList(data) {
      this.saleOrgList = (data || []).map((item, idx) => {
        const {
          salesOrganization,
          productGroup,
          distributionChannel,
          salesOrganizationName,
          distributionChannelName,
          productGroupName
        } = item;
        return {
          data: {
            idx,
            ...item
          },
          key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
          value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`
        };
      });
    },
    queryContactList(contactName) {
      this.handleQueryContactList(
        contactName,
        () => {
          this.loadingContact = true;
        },
        (res) => {
          this.loadingContact = false;
          if (res && res.code === 200 && res.data.records.length > 0) {
            const headerData = {
              contactName: '联系人',
              contactId: '联系人编号',
              contactPhone: '联系人电话',
              address: '联系人地址'
            }
            this.contactList = [
              headerData,
              ...res.data.records
            ]
            this.contactListWithAddress = [
              headerData,
              ...res.data.records.filter(
                (item) => item.addressId
              ).map(item => {
                item.address = item.provinceName + item.cityName + item.regionName + item.addressDetail
                return item
              })
            ];
          }
        }
      );
    },

    handleSelectedCustomer(data) {
      this.selectedCustomer = data;
    },
    handleQueryContactList(contactName, initFn, callbackFn) {
      const { customerNo } = this.postForm;
      if (this.postForm.selectedSalesRange) {
        const {
          salesOrganization,
          productGroup,
          distributionChannel
        } = this.postForm.selectedSalesRange;
        initFn && initFn();
        searchContactListByGroup({
          customerCode: customerNo,
          contactName,
          distributionChannel,
          productGroup,
          salesOrganization
        }).then((res) => {
          callbackFn && callbackFn(res);
        });
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        });
      }
    },
    changeContact() {},
    // 切换制定商品纬度
    changeSupplyChannel(val) {
      this.$set(this.postForm, 'selectedGood', '')
      this.$set(this.postForm, 'brandId', '')
      this.$set(this.postForm, 'materialGroup', '')
      this.$set(this.postForm, 'selectedGproductGroupood', '')
      this.$refs['postForm'].clearValidate()
    },
    changeCustomerType(val) {
      this.postForm.orderContact = ''
      this.postForm.receiverContact = ''
      this.postForm.receiverAddress = ''
      this.$refs['postForm'].clearValidate()
    },
    handleProviderChange() {

    },
    getList(type = 'list') {
      this.loading = true
      let query = {}
      if (type === 'pagination') {
        Object.assign(query, this.queryParams, this.listQueryInfo)
      } else {
        this.listQueryInfo.pageNo = 1
        this.listQueryInfo.pageSize = 50
        Object.assign(query, this.queryParams, this.listQueryInfo)
      }
      getDesignatedChanelAndWareHouse(query).then((res) => {
        this.loading = false
        if (res.code === 200) {
          this.tableData = res.data.data
          this.total = res.data.total
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async getExportList() {
      this.exportLoading = true
      delete this.queryParams.pageSize
      delete this.queryParams.pageNo
      exportDesignatedChanelAndWareHouse(this.queryParams).then(res => {
        console.log(res)
      })
        .catch(err => {
          this.$message.error(err.msg || err.message || '导出失败！')
        })
        .finally(() => {
          this.exportLoading = false
        })
    },
    search() {
      this.getList()
    },
    reset() {
      // this.$refs['postForm'].resetFields()
      for (var k in this.postForm) {
        if (k !== 'factory') {
          this.postForm[k] = ''
        } else {
          this.postForm[k] = this.findPointedFactory('1000')
        }
      }
      this.warehouseCodeList = []
      this.designatedPosition = []
      this.getList()
    },
    // 清除请求空参数
    clean(obj) {
      var propNames = Object.getOwnPropertyNames(obj)
      for (var i = 0; i < propNames.length; i++) {
        var propName = propNames[i]
        if (
          obj[propName] === null ||
          obj[propName] === undefined ||
          obj[propName] === ''
        ) {
          delete obj[propName]
        }
      }
    },
    getLabelByValue,
    formatDate(timestamp) {
      return moment(new Date(timestamp)).format('YYYY-MM-DD HH:mm:ss');
    },
    exportList() {
      this.getExportList()
    },
    goToLink(url) {
      window.open(url)
    },
    changeRuleCode(ruleCode) {
      if (ruleCode) {
        // positionScope        库位范围：不传-不返回库位; 1-全量库位; 2-可售库位
        getwWarehouseCode({
          ruleCodeSet: [ruleCode],
          factorySet: this.postForm.factory ? [this.postForm.factory.value] : [],
          positionScope: 1
        // warehouseTypeSet: [ '100' ]
        }).then((res) => {
          if (res && res.status === 200) {
            this.warehouseCodeList = res.result
          } else {
            this.$message.error(res && res.msg)
          }
        })
      }
    },
    changeWarehouseCode(selectedWareHouseCode) {
      this.$set(this.postForm, 'position', '')
      this.designatedPosition = selectedWareHouseCode.allPosition
    },
    changeDesignatedPos(designatedPos) {
      this.$forceUpdate()
    },
    chooseFac() {
      this.$forceUpdate()
    },
    // 审批流程权限相关
    hasPremission(premissionName, approval) {
      const ownerPermission = this.getPermissions.filter(premission => premission.name === premissionName).length > 0
      // 草稿|审批驳回 显示<提交??><编辑><删除>
      if (ownerPermission && ~['提交', '编辑', '删除'].indexOf(premissionName) && (approval === 'DRAFT' || approval === 'REJECT')) {
        return true
      }
      // 审核中 显示<审批><驳回>
      if (ownerPermission && ~['审批', '驳回'].indexOf(premissionName) && approval === 'SUBMIT') {
        return true
      }
      // 审核通过 显示<取消审核>
      if (ownerPermission && premissionName === '取消审核' && approval === 'APPROVAL') {
        return true
      }
      // 非审核流程按钮权限
      if (ownerPermission && !approval) {
        return true
      }
      return false
    },
    findPointedFactory(pointedFactory) {
      return this.factoryList.find(item => item.value === pointedFactory)
    },
    findInValue(list, value, keyStr) {
      const filter = list.find(item => item[keyStr] === value)
      return filter ? (filter.label || filter.name) : '';
    }
  }

};
</script>

<style scoped lang="scss">

</style>
