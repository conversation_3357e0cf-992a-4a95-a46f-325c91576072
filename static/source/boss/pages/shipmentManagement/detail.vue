<template>
  <div class="app-container shipment-management--detail" v-loading="loading">
    <DividerHeader>
      <div class="flex justify-between">
        <span>客户信息</span>
        <span>
          <el-button type="primary" @click="handleEdit" v-if="operationList.includes('编辑')">编辑</el-button>
          <el-button type="primary" @click="handleSubmit" v-if="operationList.includes('提交')">提交</el-button>
          <el-button type="primary" @click="handleDelete" v-if="operationList.includes('删除')">删除</el-button>
          <el-button type="primary" @click="handleApproval" v-if="operationList.includes('审批') && approvalPermission">审批</el-button>
          <el-button type="primary" @click="handleReject" v-if="operationList.includes('驳回')">驳回</el-button>
          <el-button type="primary" @click="handleDraft" v-if="operationList.includes('取消审核')">取消审核</el-button>
        </span>
      </div>
    </DividerHeader>
    <HeaderList
      :type="type"
      :approval="approval"
      :defaultData="detailData.supplyChannelHeaderList"
      :defaultItemData="detailData.supplyChannelItemList"
      :supplierCustomerStr="supplierCustomerStr"
      :pageSource="pageSource">
    </HeaderList>
    <DividerHeader>商品信息</DividerHeader>
    <ItemList
      :type="type"
      :defaultData="detailData.supplyChannelItemList"
      :supplierCustomerStr="supplierCustomerStr"
      :pageSource="pageSource"></ItemList>
    <el-dialog
      title="提示"
      :visible.sync="rejectDialog"
      width="30%"
    >
      <el-form ref="rejectForm" :model="rejectForm" :rules="rejectRules" label-width="100px">
        <el-form-item label="驳回理由：" prop="rejectReason">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入驳回理由"
            v-model="rejectForm.rejectReason"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rejectDialog = false">取 消</el-button>
        <el-button type="primary" @click="realReject"
        >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { fetchDetailData, approveCustomerPosition, deleteCustomer } from '@/api/shipmentManagement.js'
import { searchContactListByGroup } from '@/api/orderSale'
import DividerHeader from '@/components/DividerHeader'
import { mapState } from 'vuex'
import HeaderList from './components/HeaderList'
import ItemList from './components/ItemList'
import _intersection from 'lodash/intersection'

export default {
  name: 'shipmentDetail',
  data() {
    const { id } = this.$route.params
    return {
      id,
      type: '',
      rejectDialog: false,
      supplierCustomerStr: ['SUPPLY_TO_CUSTOMER', 'REPORT_SUPPLY_TO_CUSTOMER'],
      rejectForm: {
        rejectReason: ''
      },
      rejectRules: {
        rejectReason: [
          { required: true, message: '请输入驳回理由', trigger: 'blur' }
        ]
      },
      detailData: {
        supplyChannelHeaderList: [],
        supplyChannelItemList: []
      },
      show: {
        hiddenFilter: false
      },
      searchForm: {
        customerNo: '',
        vkorg: '',
        selectedSalesRange: '',
        // 指定联系人
        orderContact: null,
        receiverContact: null,
        receiverAddress: null

      },
      selectedCustomer: null,
      saleOrgList: [],
      contactList: [],
      contactListWithAddress: [],
      loadingContact: false,
      isAddress: true,
      basisListLoading: false,
      pageSource: 'detail',
      approval: null,
      loading: false
    }
  },
  components: {
    DividerHeader,
    HeaderList,
    ItemList
  },
  created() {
    const type = this.$route.query.type
    if (type === 'SUPPLY_TO_CUSTOMER') {
      this.$router.push({
        path: `/shipmentManagement/detailStc/${this.id}?type=${type}`
      });
    } else {
      if (JSON.stringify(this.dictList) === '{}') {
        this.$store.dispatch('orderCommon/queryDictList')
      }
      this.type = type
    }
  },
  computed: {
    ...mapState(['menu', 'userRole']),
    // 获取操作权限
    approvalPermission() {
      return this.userRole.includes('data-客服总监') && this.userRole.includes('boss-指定集货仓审核')
    },
    getPermissions() {
      return this.menu.filter(menu => menu.name === '基础配置')[0].children[0].children
    },
    dictList() {
      return this.$store.state.orderCommon.dictList || {}
    },
    operationList() {
      // let operationList = this.detailData.supplyChannelHeaderList[0]?.buttonSet || []
      // return operationList
      const ownerPermissions = this.getPermissions.map(premission => premission.name)
      const approval = this.approval
      let operationList = []
      // 草稿|审批驳回
      if (approval === 'DRAFT' || approval === 'REJECT') {
        operationList = _intersection(['提交', '编辑', '删除'], ownerPermissions)
      }
      // if (ownerPermission && ~['提交', '编辑', '删除'].indexOf(premissionName) && (approval === 'DRAFT' || approval === 'REJECT')) {
      //   return true
      // }
      // 审核中
      if (approval === 'SUBMIT') {
        operationList = _intersection(['审批', '驳回'], ownerPermissions)
      }
      // if (ownerPermission && ~['审批', '驳回'].indexOf(premissionName) && approval === 'SUBMIT') {
      //   return true
      // }
      // 审核通过
      if (approval === 'APPROVAL') {
        operationList = _intersection(['取消审核'], ownerPermissions)
      }
      // if (ownerPermission && premissionName === '取消审核' && approval === 'APPROVAL') {
      //   return true
      // }
      // return false
      return operationList
    }
  },
  mounted() {
    this.fetchDetailData()
  },
  watch: {
    'detailData.supplyChannelHeaderList': {
      handler(val) {
        if (val && val.length > 0) {
          this.approval = val[0].approval
        }
      }
    }
  },
  methods: {
    handleEdit() {
      this.$closeTag(this.$route.path)
      // this.$closeTag('/shipmentManagement/create');
      this.$router.push(`/shipmentManagement/create/${this.id}?type=${this.type}`)
    },
    handleDelete() {
      deleteCustomer(this.id).then((res) => {
        this.$message.success('删除成功')
        this.$closeTag(this.$route.path)
        this.$router.push({
          path: '/shipmentManagement/basisList'
        })
      })
    },
    handleApproval() {
      const data = {
        approval: 'APPROVAL',
        approvalReason: ''
      }
      this.approvePosition(data)
    },
    handleReject() {
      this.rejectDialog = true
      this.rejectForm.rejectReason = ''
    },
    realReject() {
      this.$refs.rejectForm.validate((valid) => {
        if (valid) {
          this.rejectDialog = false
          const data = {
            approval: 'REJECT',
            approvalReason: this.rejectForm.rejectReason
          }
          this.approvePosition(data)
        } else {
          return false
        }
      })
    },
    handleDraft() {
      const data = {
        approval: 'DRAFT',
        approvalReason: ''
      }
      this.approvePosition(data)
    },
    handleSubmit() {
      const data = {
        approval: 'SUBMIT',
        approvalReason: ''
      }
      this.approvePosition(data)
    },
    // 前端状态流转
    approveStatus(status) {
      let approval
      switch (status) {
        case 'SUBMIT':
          approval = 'SUBMIT'
          break
        case 'DRAFT':
          approval = 'DRAFT'
          break
        case 'REJECT':
          approval = 'REJECT'
          break
        case 'APPROVAL':
          approval = 'APPROVAL'
          break
        default:
          approval = null
      }
      this.approval = approval
    },
    approvePosition(data) {
      approveCustomerPosition(this.id, data).then((res) => {
        if (res.code === 200) {
          this.$message.success('操作成功')
          setTimeout(() => {
            this.approveStatus(data.approval)
          }, 1000)
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    async fetchDetailData() {
      this.loading = true
      try {
        const res = await fetchDetailData(this.id)
        if (!res.success) {
          this.$message.error(res.msg || '获取详情失败')
          return
        }
        this.detailData = res.data
        this.loading = false
      } catch (error) {
        this.loading = false
      }
    },
    toggle() {
      this.show.hiddenFilter = !this.show.hiddenFilter
    },
    handleSelectedCustomer(data) {
      this.selectedCustomer = data
    },
    handleSaleOrgList(data) {
      this.saleOrgList = (data || []).map((item, idx) => {
        const {
          salesOrganization,
          productGroup,
          distributionChannel,
          salesOrganizationName,
          distributionChannelName,
          productGroupName
        } = item
        return {
          data: {
            idx,
            ...item
          },
          key: `${salesOrganization}_${productGroup}_${distributionChannel}`,
          value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`
        }
      })
    },
    handleQueryContactList(contactName, initFn, callbackFn) {
      const { customerNo } = this.searchForm
      if (this.searchForm.selectedSalesRange) {
        const {
          salesOrganization,
          productGroup,
          distributionChannel
        } = this.searchForm.selectedSalesRange
        initFn && initFn()
        searchContactListByGroup({
          customerCode: customerNo,
          contactName,
          distributionChannel,
          productGroup,
          salesOrganization
        }).then((res) => {
          callbackFn && callbackFn(res)
        })
      } else {
        this.$message.warning({
          message: '请选择销售范围'
        })
      }
    },
    queryContactList(contactName) {
      this.handleQueryContactList(
        contactName,
        () => {
          this.loadingContact = true
        },
        (res) => {
          this.loadingContact = false
          if (res && res.code === 200 && res.data.records.length > 0) {
            const headerData = {
              contactName: '联系人',
              contactId: '联系人编号',
              contactPhone: '联系人电话',
              address: '联系人地址'
            }
            this.contactList = [
              headerData,
              ...res.data.records
            ]
            this.contactListWithAddress = [
              headerData,
              ...res.data.records.filter(
                (item) => item.addressId
              ).map(item => {
                item.address = item.provinceName + item.cityName + item.regionName + item.addressDetail
                return item
              })
            ]
          }
        }
      )
    },
    changeContact() {
    },
    handleFilter() {
      console.log(this.searchForm)
    },
    handleReset() {
      this.$refs.searchForm.resetFields()
    }
  }
}
</script>
<style lang="scss">
  .shipment-management--detail {
    .flex {
      display: flex;
    }

    .justify-between {
      justify-content: space-between;
    }

    .btn-toggle {
      width: 32px;
      height: 32px;
      line-height: 32px;
      color: #fff;
      border-radius: 32px;
      text-align: center;
      background-color: #597bee;
      margin: 10px 0 0;
      transform: rotate(-180deg);
      transition: transform .3s ease;

      &.on {
        transform: rotate(0deg);
      }
    }
  }
</style>
