import { get, find } from 'lodash'

export const ABNORMAL_DIVIDER = '$__$'

export const positionListColumns = [
  { type: 'default', visible: true, label: '客户编码', prop: 'customerNo', minWidth: '100px' },
  { type: 'default', visible: true, label: '客户名称', prop: 'customerName', minWidth: '300px' },
  {
    type: 'default',
visible: true,
label: '收货/订单联系人编号',
prop: 'contactId',
minWidth: '160px',
labelFormat: (_, row) => {
      return row.orderContactId || row.receiverContactId || '-'
    }
  },
  {
    type: 'default',
visible: true,
label: '收货/订单联系人姓名',
prop: 'contactName',
minWidth: '160px',
labelFormat: (_, row) => {
      return row.orderContactName || row.receiverContactName || '-'
    }
  },
  {
    type: 'default',
    visible: true,
    label: '指定落仓仓位',
    prop: 'positionName',
    minWidth: '160px',
    labelFormat: (_, row) => {
      return row.position + row.positionName
    }
  },
  { type: 'default', visible: true, label: '创建人', prop: 'createUserName', minWidth: '120px' },
  { type: 'default', visible: true, label: '维护人', prop: 'maintainUserName', minWidth: '120px' },
  { type: 'button', visible: true, label: '操作', prop: 'remove', minWidth: '80px', text: '删除' }
]

export const productListColumns = [
  { type: 'default', visible: true, label: '客户编码', prop: 'customerNo', minWidth: '100px' },
  { type: 'default', visible: true, label: '客户名称', prop: 'customerName', minWidth: '300px' },
  {
    type: 'default',
visible: true,
label: '商品编码',
prop: 'skuNo',
minWidth: '160px',
labelFormat: (val) => {
      if (!val) return '-'
    }
  },
  {
    type: 'default',
visible: true,
label: '物料描述',
prop: 'skuName',
minWidth: '160px',
labelFormat: (val) => {
      if (!val) return '-'
    }
  },
  {
    type: 'default',
    visible: true,
    label: '指定落仓仓位',
    prop: 'positionName',
    minWidth: '160px',
    labelFormat: (_, row) => {
      return row.position + row.positionName
    }
  },
  { type: 'button', visible: true, label: '操作', prop: 'remove', minWidth: '80px', text: '删除' }
]

export const getStorageData = (key) => {
  try {
    return JSON.parse(
      localStorage.getItem(key) || '[]'
    ) || []
  } catch {
    return []
  }
}

export const updateStorageData = (data, key) => {
  try {
    let newData =
      JSON.parse(
        localStorage.getItem(key) || '[]'
      ) || []
    newData = newData.concat(
      data
    )
    localStorage.setItem(
      key,
      JSON.stringify(newData)
    )
  } catch {
  }
}

export const CustomerChooseTypeOptions = [{
  label: '客户订单联系人',
  value: 'CUSTOMER_ORDER_CONTACT'
}, {
  label: '客户收货联系人',
  value: 'CUSTOMER_RECEIVER_CONTACT'
}, {
  label: '客户收货地址',
  value: 'CUSTOMER_RECEIVER_ADDRESS'
},
  {
    label: '客户收货地址(省市区)',
    value: 'CUSTOMER_RECEIVER_ADDRESS_BRIEF'
  },
  {
    label: '客户',
    value: 'CUSTOMER_ALL'
  }
]

export const ApprovalStatusOptions = [
  {
    label: '审核通过',
    value: 'APPROVAL'
  },
  // 审批未通过
  {
    label: '审核驳回',
    value: 'REJECT'
  },
  {
    label: '审核中',
    value: 'SUBMIT'
  },
  {
    label: '负责人初审中',
    value: 'PRODUCT_MANAGER_REVIEW'
  },
  {
    label: '草稿',
    value: 'DRAFT'
  }]

// 1.SKU 2.品牌 BRAND 3.产品组 PRODUCT_GROUP 4 物料组 MATERIAL_GROUP 5.全部 ALL
export const SupplyChannelScopeOptions = [{
  label: 'SKU',
  value: 'SKU'
},
  {
    label: '品牌',
    value: 'BRAND'
  },
  {
    label: '产品组',
    value: 'PRODUCT_GROUP',
    disabled: true
  },
  {
    label: '物料组',
    value: 'MATERIAL_GROUP',
    disabled: true
  },
  {
    label: '全部',
    value: 'ALL',
    disabled: true
  }]

// 1.直发 SUPPLY_TO_CUSTOMER 2.ZKH发货 ZKH_TO_CUSTOMER
export const DeliveryModeOptions = [{
  label: '直发',
  value: 'SUPPLY_TO_CUSTOMER'
},
  {
    label: 'ZKH发货',
    value: 'ZKH_TO_CUSTOMER'
  }, {
    label: '指定仓发货',
    value: 'SPECIFY_WAREHOUSE'
  }, {
    label: '直发',
    value: 'REPORT_SUPPLY_TO_CUSTOMER'
  }]
export const DeliveryTypeOptions = [
  {
    label: '直发',
    value: 'DIRECT'
  },
  {
    label: '震坤行发货',
    value: 'ZKH'
  }, {
    label: '系统挑仓',
    value: 'SYS_SELECT'
  }
]
export const deliveryModeList = [
  {
    value: 'SUPPLY_TO_CUSTOMER',
    label: '指定供货渠道',
    deliveryType: 'DIRECT'
  },
  {
    value: 'ZKH_TO_CUSTOMER',
    label: '指定集货仓',
    deliveryType: 'SYS_SELECT'
  },
  {
    value: 'SPECIFY_WAREHOUSE',
    label: '指定发货仓',
    deliveryType: 'ZKH'
  },
  {
    value: 'REPORT_SUPPLY_TO_CUSTOMER',
    label: '报备供货渠道',
    deliveryType: 'DIRECT'
  }
]
export const channelTypeList = [
  {
    value: 'CUSTOMER',
    label: '客户指定',
    disabled: true
  },
  {
    value: 'FACTORY',
    label: '原厂指定'
  },
  {
    value: 'PRODUCTION',
    label: '产线指定'
  }
]
export const PurchasePricingTypeOptions = [
  {
    value: 'PURCHASE_PRICE',
    label: '商品采购价'
  },
  {
    value: 'SPECIAL_PRICE',
    label: '客户专享采购价'
  },
  {
    value: 'COMMISSION_RATE',
    label: '佣金比率'
  }
]

export function formatDate(time) {
  if (time) {
    const date = new Date(time)
    const dateString = date.toISOString().replace(/T.+/, '')
    // const timeString = date.toTimeString().replace(/\s.+/, '')
    return `${dateString}`
  }
  return time
}

export function approvalFilter(data) {
  for (let item of ApprovalStatusOptions) {
    if (item.value === data) {
      return item.label
    }
  }
}

const optionsMapping = { CustomerChooseTypeOptions, ApprovalStatusOptions, SupplyChannelScopeOptions, DeliveryModeOptions, DeliveryTypeOptions, deliveryModeList, channelTypeList, PurchasePricingTypeOptions }

export function getLabelByValue(option, value) {
  return get(find(optionsMapping[option], item => item.value === value), 'label')
}

export function boolMapStr(val) {
  return val === '1' ? '是' : val === '0' ? '否' : ''
}
