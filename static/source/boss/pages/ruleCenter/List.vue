<template>
<div class="page page-rule-center">
  <div class="module-filter module-border">
    <div class="module-head">
      <div class="group-title">
        <span><i class="el-icon-search"></i>查询条件</span>
      </div>
      <div class="group-btns">
        <el-button type="success" size="mini" @click="search">搜索</el-button>
        <el-button type="warning" size="mini" @click="reset">重置</el-button>
        <el-button type="primary" size="mini" @click="exportDoc">导出</el-button>
      </div>
    </div>
    <div class="module-body">
      <el-row>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>规则名称:</label>
            </el-col>
            <el-col :span="16">
              <el-select v-model="filter.ruleName" filterable clearable size="small">
                <el-option v-for="(item, index) in option.rules" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>SKU编码:</label>
            </el-col>
            <el-col :span="16">
              <el-input v-model="filter.skuNos" placeholder="请输入SKU编码" clearable />
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>物料组:</label>
            </el-col>
            <el-col :span="16">
              <el-select v-model="filter.productGroupName" filterable clearable size="small">
                <el-option v-for="(item, index) in option.materialGroup" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>商品来源:</label>
            </el-col>
            <el-col :span="16">
              <el-select v-model="filter.commoditySourceName" filterable clearable size="small">
                <el-option v-for="(item, index) in option.source" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>一级分组:</label>
            </el-col>
            <el-col :span="16">
              <el-select v-model="filter.level1id" filterable remote clearable :remote-method="str => getOptionCategory(1, str)" placeholder="请输入一级分组">
                <el-option v-for="(item, index) in option.categoryLevel1" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>二级分组:</label>
            </el-col>
            <el-col :span="16">
              <el-select v-model="filter.level2id" filterable remote clearable :remote-method="str => getOptionCategory(2, str)" placeholder="请输入二级分组">
                <el-option v-for="(item, index) in option.categoryLevel2" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>三级分组:</label>
            </el-col>
            <el-col :span="16">
              <el-select v-model="filter.level3id" filterable remote clearable :remote-method="str => getOptionCategory(3, str)" placeholder="请输入三级分组">
                <el-option v-for="(item, index) in option.categoryLevel3" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="8">
          <el-row>
            <el-col :span="8" class="label">
              <label>四级分组:</label>
            </el-col>
            <el-col :span="16">
              <el-select v-model="filter.level4id" filterable remote clearable :remote-method="str => getOptionCategory(4, str)" placeholder="请输入四级分组">
                <el-option v-for="(item, index) in option.categoryLevel4" :key="index" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="16">
          <el-row>
            <el-col :span="4" class="label">
              <label>校验时间:</label>
            </el-col>
            <el-col :span="20">
              <el-date-picker v-model="filter.range" type="daterange" value-format="yyyy-MM-dd HH:mm:ss" clearable @change="onDatePick" />
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
  </div>
  <div class="module-list module-border">
    <div class="module-head">
      <div class="title">
        <span>
          <i class="el-icon-s-data"></i>商品列表
        </span>
      </div>
    </div>
    <div class="module-body" v-loading="loading.list">
      <el-table
        border fit highlight-current-row center
        width="100%"
        :data="list">
        <el-table-column prop="skuNo" label="SKU编码" fixed></el-table-column>
        <el-table-column prop="title" width="150" label="商品名称" show-overflow-tooltip />
        <el-table-column prop="commoditySourceName" label="商品来源" show-overflow-tooltip />
        <el-table-column prop="productGroupName" width="150" label="物料组" show-overflow-tooltip />
        <el-table-column prop="level1name" width="120" label="一级目录" show-overflow-tooltip/>
        <el-table-column prop="level2name" width="120" label="二级目录" show-overflow-tooltip/>
        <el-table-column prop="level3name" width="120" label="三级目录" show-overflow-tooltip/>
        <el-table-column prop="level4name" width="120" label="四级目录" show-overflow-tooltip/>
        <el-table-column prop="productStatus" width="120" label="商品状态" show-overflow-tooltip/>
        <el-table-column prop="createdTime" width="120" label="校验时间" show-overflow-tooltip/>
        <el-table-column prop="message" width="120" label="校验信息" show-overflow-tooltip/>
        <el-table-column prop="ruleName" width="120" label="校验规则" show-overflow-tooltip/>
      </el-table>

      <el-pagination
        @current-change="search"
        @size-change="search"
        :current-page.sync="filter.page"
        :page-size.sync="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>

  <el-dialog :visible.sync="show.export" title="文件导出">
    <div v-loading="loading.export">
      <p>{{ exportInfo.msg }}</p>
      <p v-show="exportInfo.data">导出文件:<a :href="exportInfo.data" target="_blank" :download="exportInfo.data">点击下载</a></p>
    </div>
  </el-dialog>
</div>
</template>

<script>

import { api } from '@/api/boss'

export default {
  data () {
    return {
      filter: {
        commoditySourceName: null,
        level1id: null,
        level2id: null,
        level3id: null,
        level4id: null,
        productGroupName: null,
        ruleName: null,
        skuNos: null,
        startTime: null,
        endTime: null,
        range: '',
        page: 0,
        pageSize: 10
      },
      show: {
        export: false
      },
      exportInfo: {
        msg: '',
        data: ''
      },
      list: [
        { skuNo: 1 }
      ],
      loading: {
        list: false,
        export: false
      },
      total: 0,
      option: {
        rules: [],
        source: [],
        materialGroup: [],
        categoryLevel1: [],
        categoryLevel2: [],
        categoryLevel3: [],
        categoryLevel4: []
      }
    }
  },
  components: {
  },
  methods: {
    onDatePick (date) {
      console.log(date)
      const [ min, max ] = date || []
      this.filter.startTime = min
      this.filter.endTime = max
    },
    getOptionCategory (level, name) {
      api({
        prefix: '/api-cc-gateway',
        url: '/api/scc/v1/catalog/page',
        method: 'POST',
        data: {
          stateCode: 1,
          name: name,
          level,
          pageNo: 1,
          rowCount: 20
        },
        complete: res => {
          const result = res.data.map(item => {
            return {
              label: item.name,
              value: item.id
            }
          })
          this.option[`categoryLevel${level}`] = result
          console.log(result)
        }
      })
    },
    getOptionSource () {
      const type = 'commoditysource'
      api({
        prefix: '/api-cc-gateway',
        url: '/api/scc/v1/dict/typeCodes',
        query: {
          typeCodes: type
          // pageNo: 1,
          // rowCount: 20
        },
        complete: res => {
          console.log(res)
          const result = res.data[type].map(item => {
            return {
              label: item.name,
              value: item.id
            }
          })
          console.log(result)
          this.option.source = result
        }
      })
    },
    getOptionMaterialGroup () {
      api({
        prefix: '/api-cc-gateway',
        url: '/api/scc/v1/materialGroup/page',
        query: {
          stateCode: 1
          // pageNo: 1,
          // rowCount: 20
        },
        complete: res => {
          const result = res.data.map(item => {
            return {
              label: item.materialGroupName,
              value: item.materialGroupName
            }
          })
          console.log(result)
          this.option.materialGroup = result
        }
      })
    },
    getAllRule () {
      api({
        prefix: '/api-rulecenter',
        url: '/rule/validate/result/getAllRule',
        query: {
        },
        complete: res => {
          if (res.code === 0) {
            this.option.rules = res.data.map(item => {
              return {
                label: item,
                value: item
              }
            })
          }
          console.log(res)
        }
      })
    },
    search () {
      this.loading.list = true
      api({
        prefix: '/api-rulecenter',
        url: '/rule/validate/result/queryResult',
        data: this.filter,
        method: 'POST',
        complete: res => {
          if (res.code === 0) {
            this.list = res.data.content
            this.total = res.data.totalElements
          } else {
            this.$message.error('API ERROR: ' + res.msg)
          }
          this.loading.list = false
        }
      })
    },
    reset () {
      for (let key in this.filter) {
        this.filter[key] = null
      }
      this.filter.page = 1
      this.filter.pageSize = 10
    },
    exportDoc () {
      this.show.export = true
      this.loading.export = true
      api({
        prefix: '/api-rulecenter',
        url: '/rule/validate/result/export',
        data: this.filter,
        method: 'POST',
        complete: res => {
          console.log(res)
          this.loading.export = false
          if (res.code === 0) {
            this.exportInfo.data = res.data
            this.exportInfo.msg = res.msg
          } else {
            this.show.export = false
            this.$message.error('导出失败：' + res.msg)
          }
        }
      })
    }
  },
  mounted () {
    this.getOptionCategory(1)
    this.getOptionCategory(2)
    this.getOptionCategory(3)
    this.getOptionCategory(4)
    this.getOptionSource()
    this.getOptionMaterialGroup()

    this.getAllRule()
    this.search()
  }
}
</script>

<style lang="less">
.page-rule-center {
  .module-head {
    padding: 10px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
  }
  .module-border {
    border: 1px solid #ddd;
    border-radius: 5px;
  }
  .module-filter {
    margin-bottom: 20px;
    .group-title {
      line-height: 32px;
    }
    .module-body {
      padding: 10px;
      > .el-row {
        > .el-col {
          padding: 10px;
        }
      }
      .label {
        text-align: center;
        line-height: 32px;
      }
      .el-select {
        width: 100%;
      }
    }
  }
  .el-pagination {
    text-align: center;
    padding: 10px;
  }
}
</style>
