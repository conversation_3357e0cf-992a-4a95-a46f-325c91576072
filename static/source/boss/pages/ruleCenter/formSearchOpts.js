// 查询条件配置

export default [
  // {
  //   label: '视图',
  //   type: 'select',
  //   key: 'productType',
  //   selects: [{
  //     "value" : "useful",
  //     "label" : "可用"
  //   }, {
  //     "value" : "stopped",
  //     "label" : "停用"
  //   }, {
  //     "value" : "webProduct",
  //     "label" : "官网产品"
  //   }, {
  //     "value" : "evm",
  //     "label" : "EVM产品"
  //   }, {
  //     "value" : "all",
  //     "label" : "所有产品"
  //   }, {
  //     "value" : "created",
  //     "label" : "我创建的产品"
  //   }, {
  //     "value" : "applyUpdate",
  //     "label" : "我修改的产品"
  //   }, {
  //     "value" : "applied",
  //     "label" : "我提交的产品"
  //   }, {
  //     "value" : "refusedProduct",
  //     "label" : "修改拒绝的产品"
  //   }, {
  //     "value" : "approved",
  //     "label" : "待我审批的产品"
  //   }, {
  //     "value" : "participant",
  //     "label" : "我处理过的产品"
  //   }, {
  //     "value" : "addProductByProvider",
  //     "label" : "供应商新增待完善"
  //   }],
  //   val: 'useful',
  //   placeholder: '请选择视图',
  // },
  {
    label: '商品名称',
    type: 'input',
    key: 'productName',
    val: '',
    placeholder: '请输入商品名称'
  },
  {
    label: 'SKU编码',
    type: 'input',
    key: 'skuNo',
    val: '',
    placeholder: '请输入SKU编码'
  },
  // {
  //   label: '在结果中排除',
  //   type: 'input',
  //   key: 'excludeProduct',
  //   val: '',
  //   placeholder: '请输入物料描述',
  // },
  {
    label: '品牌',
    type: 'remoteSelect',
    key: 'brand',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    action: 'entityEnums/fetchBrands'
  },
  {
    label: '物料号',
    type: 'input',
    key: 'productNumber',
    val: '',
    placeholder: '可按物料号、型号、订货号查询'
  },
  {
    label: '物料描述',
    type: 'input',
    key: 'materialDescribe',
    val: '',
    placeholder: '可按品牌、名称、箱规、规格查询'
  },
  {
    label: '启用状态',
    type: 'select',
    key: 'stopAllSwitch',
    val: 1,
    selects: [
      {
        label: '启用',
        value: 1
      },
      {
        label: '停用',
        value: 0
      }
    ],
    placeholder: '请选择启用状态'
  },
  {
    label: '一级分组',
    type: 'remoteSelect',
    key: 'firstDirectoryId',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    params: {
      level: 1
    },
    action: 'entityEnums/fetchManageCatalogues'
  },
  {
    label: '二级分组',
    type: 'remoteSelect',
    key: 'secondDirectoryId',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    params: {
      level: 2
    },
    action: 'entityEnums/fetchManageCatalogues'
  },
  {
    label: '三级分组',
    type: 'remoteSelect',
    key: 'thirdDirectoryId',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    params: {
      level: 3
    },
    action: 'entityEnums/fetchManageCatalogues'
  },
  {
    label: '四级分组',
    type: 'remoteSelect',
    key: 'fourthDirectoryId',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    params: {
      level: 4
    },
    action: 'entityEnums/fetchManageCatalogues'
  },
  {
    label: '商品定位',
    type: 'select',
    key: 'productPositioning',
    val: '',
    placeholder: '',
    action: 'enums/fetchEnumOptions',
    selects: [],
    enumsType: 'ProductPositioning'
  },
  {
    label: '物料组',
    type: 'remoteSelect',
    key: 'productGroup',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    action: 'entityEnums/fetchProductGroup'
  },
  {
    label: '行业专属',
    type: 'select',
    key: 'privateIndustry',
    val: '',
    placeholder: '',
    selects: [],
    action: 'enums/fetchEnumOptions',
    enumsType: 'privateIndustry'
  },
  {
    label: '推广关键字',
    type: 'input',
    key: 'generalizeKeyWords',
    val: '',
    placeholder: '请输入推广关键字'
  },
  {
    label: '采购员',
    type: 'remoteSelect',
    key: 'buy',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    action: 'entityEnums/fetchSysuser'
  },
  {
    label: '推荐行业',
    type: 'select',
    key: 'productTrade',
    val: '',
    placeholder: '',
    selects: [],
    action: 'enums/fetchEnumOptions',
    enumsType: 'ProductTrade'
  },
  {
    label: '备货类型',
    type: 'select',
    key: 'productType',
    val: '',
    placeholder: '',
    selects: [],
    action: 'enums/fetchEnumOptions',
    enumsType: 'ProductType'
  },
  {
    label: '商品来源',
    type: 'select',
    key: 'commoditySource',
    val: '',
    placeholder: '',
    selects: [],
    action: 'enums/fetchEnumOptions',
    enumsType: 'CommoditySource'
  },
  {
    label: '产品组',
    type: 'select',
    key: 'spart',
    val: '',
    placeholder: '',
    selects: [],
    action: 'enums/fetchEnumOptions',
    enumsType: 'spart'
  },
  {
    label: '商品经理',
    type: 'remoteSelect',
    key: 'ownerId',
    val: '',
    placeholder: '',
    filterable: true,
    remote: true,
    selects: [],
    action: 'entityEnums/fetchSysuser'
  },
  {
    label: '商品备注',
    type: 'input',
    key: 'description',
    val: '',
    placeholder: '请输入商品备注'
  },
  {
    label: '是否官网',
    type: 'select',
    key: 'isWebProduct',
    val: null,
    selects: [
      {
        label: '是',
        value: 1
      },
      {
        label: '否',
        value: 0
      }
    ],
    placeholder: '请选择官网状态'
  },
  {
    label: '审核状态',
    type: 'select',
    key: 'productStatus',
    val: 2,
    selects: [
      {
        label: '废弃',
        value: -1
      },
      {
        label: '新建',
        value: 0
      },
      {
        label: '审核中',
        value: 1
      },
      {
        label: '审核通过',
        value: 2
      },
      {
        label: '审核不通过',
        value: 3
      },
      {
        label: '财务审核不通过',
        value: 4
      },
      {
        label: '供应商新增待完善',
        value: 7
      }
    ],
    placeholder: '请选择审核状态'
  },
  {
    label: 'CAS编号',
    type: 'input',
    key: 'casNumber',
    val: '',
    placeholder: '请输入CAS编号'
  }
]
