<!--
* 参数传入： config(数据配置项)
* 触发方法： search(opt)
            opt: 返回数据
* 注意： 如果有标题，则标题配置项开始，到下一个标题配置项之间的内容为第一个标题配置下的属性
*使用规则:
     1、标题的配置项
        {
          label: 标题名
          type: 'text'
        }
     2、输入框配置项
        {
          key: 键名（一般是设定为接口的字段名）,
          label: 标签项,
          type: 'input',
          val: ''   //默认值,
          placeholder: ''
        }
      3、下拉框配置
        {
          key: 键名（一般是设定为接口的字段名）,
          label: 标签项,
          type: 'select',
          val: '',    //默认值
          selects: []   //模块-集合
          filterable: true //配置下拉选项是否可以搜索 (可全量获取列表) ： true => 可搜， false => 不可搜
          remote: 远程搜索
          selectsParams: {} // 远程查询参数
        }
      4.单选框配置
      {
        key: 键名（一般是设定为接口的字段名）,
        label: 标签项,
        type: 'radio',
        val: 0,  //默认值
        options: [{    //options: radio项
          label: '是',
          value: 1
        },{
          label: '不过滤',
          value: 0,
        }]
      }
      5. 时间配置
      {
        key: 键名（一般是设定为接口的字段名）,
        label: 标签项,
        type: 时间类型： date、 daterange、datetimerange,
        val: 0,  //默认值
      }
*
*示例：
*opts: [
        {
          label: '客户名称',
          type: 'remoteSelect',
          key: 'customerName',
          val: '',
          placeholder: '',
          filterable: true,
          remote: true,
          selects: [],
          action: 'entityEnums/fetchCustomerListForOpt'
        },
        {
          label: '客户编码',
          type: 'input',
          key: 'customerNumber',
          val: '这是一个默认值',
          placeholder: '请输入客户编码',
        },
        {
          label: '创建日期',
          type: 'date',
          key1: 'startTime',
          key2: 'endTime',
          placeholder1: '开始日期',
          placeholder2: '结束日期',
        },
        {
          label: '客户來源',
          type: 'select',
          key: 'customerSource',
          val: '',
          placeholder: '',
          filterable: true,
          selects: [],
          action: 'enums/fetchEnumOptions',
          enumsType: 'CustomerSource'
        },
        {
          label: '付款账户类型',
          type: 'select',
          key: 'paymentAccountType',
          val: '',
          placeholder: '',
          filterable: true,
          selects: [],
          action: 'enums/fetchEnumOptions',
          enumsType: 'PaymentAccountType'
        },
        {
          label: '开关',
          type: 'switch',
          key: 'customerStatus',
          val: '1',
        },
      ]
 -->
<template>
  <div class="drop-box g-box">
    <slot name="title">
      <div class="g-box-title form-list-title">
        <i class="el-icon-search"></i>
        查询条件
        <div class="g-box-title-btn">
          <el-button type="success" size="mini" @click="search()"
            >搜索</el-button
          >
          <el-button type="warning" size="mini" @click="reset()"
            >重置</el-button
          >
        </div>
      </div>
    </slot>
    <div class="form-search">
      <div class="content">
        <el-row :gutter="10">
          <template v-for="(item, index) in formOpt">
            <el-col
              v-if="isshow || (!isshow && index < 6)"
              :span="8"
              :key="item.key"
              class="list"
            >
              <el-row v-if="item.type === 'input'">
                <el-col :span="8"
                  ><label>{{ item.label }}:</label></el-col
                >
                <el-col :span="16">
                  <el-input
                    :placeholder="
                      item.placeholder
                        ? item.placeholder
                        : '请输入' + item.label
                    "
                    size="small"
                    v-model="searchForm[item.key]"
                    clearable
                  ></el-input>
                </el-col>
              </el-row>
              <el-row v-if="item.type === 'remoteSelect'">
                <el-col :span="8"
                  ><label>{{ item.label }}:</label></el-col
                >
                <el-col :span="16">
                  <el-select
                    v-model="searchForm[item.key]"
                    :filterable="!!item.filterable"
                    clearable
                    style="width: 100%"
                    :placeholder="
                      item.placeholder
                        ? item.placeholder
                        : '请输入' + item.label
                    "
                    size="small"
                    :remote="!!item.remote"
                    :remote-method="remoteMethod(item)"
                    @focus="handleRemoteSelectFocus(item)"
                    :loading="optionLoading"
                  >
                    <el-option
                      v-for="(el, index) in item.selects"
                      :key="index"
                      :label="el.label"
                      :value="el.value"
                    ></el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row v-if="item.type === 'select'">
                <el-col :span="8"
                  ><label>{{ item.label }}:</label></el-col
                >
                <el-col :span="16">
                  <el-select
                    v-model="searchForm[item.key]"
                    :filterable="!!item.filterable"
                    clearable
                    style="width: 100%"
                    :placeholder="
                      item.placeholder
                        ? item.placeholder
                        : '请输入' + item.label
                    "
                    size="small"
                    @focus="handleSelectFocus(item)"
                  >
                    <el-option
                      v-for="(el, index) in item.selects"
                      :key="index"
                      :label="el.label"
                      :value="el.value"
                    ></el-option>
                  </el-select>
                </el-col>
              </el-row>
              <el-row v-if="item.type === 'date'">
                <el-col :span="6"
                  ><label>{{ item.label }}:</label></el-col
                >
                <el-col :span="8">
                  <el-date-picker
                    v-model="searchForm[item.key1]"
                    type="date"
                    clearable
                    size="small"
                    value-format="yyyy-MM-dd"
                    :picker-options="pickerOptionsStart"
                    :placeholder="
                      item.placeholder1 ? item.placeholder1 : item.label
                    "
                  >
                  </el-date-picker>
                </el-col>
                <el-col :span="2"><label>至</label></el-col>
                <el-col :span="8">
                  <el-date-picker
                    v-model="searchForm[item.key2]"
                    type="date"
                    clearable
                    size="small"
                    value-format="yyyy-MM-dd"
                    :picker-options="pickerOptionsEnd"
                    :placeholder="
                      item.placeholder2 ? item.placeholder2 : item.label
                    "
                  >
                  </el-date-picker>
                </el-col>
              </el-row>
              <el-row v-if="item.type === 'switch'">
                <el-col :span="8"
                  ><label>{{ item.label }}:</label></el-col
                >
                <el-col :span="16">
                  <g-switch
                    v-model="searchForm[item.key]"
                    active-value="1"
                    inactive-value="0"
                    size="small"
                  ></g-switch>
                </el-col>
              </el-row>
              <!-- <el-row :gutter="20" v-if="item.type === 'radio'">
                <el-col :span="6" class="el_radia_title"><label>{{item.label}}:</label></el-col>
                <el-col :span="6" v-for="el in item.options"><el-radio v-model="item.val" :label="el.value">{{el.label}}</el-radio></el-col>
              </el-row>
              <el-row v-if="item.type.indexOf('date') > -1">
                <el-col :span="8" class="el_radia_title"><label>{{item.label}}:</label></el-col>
                <el-col :span="16" ><el-date-picker v-model="item.val" :type="item.type" placeholder="选择时间范围"></el-date-picker></el-col>
              </el-row> -->
            </el-col>
          </template>
        </el-row>
      </div>
    </div>
    <div v-if="formOpt.length > 6" class="drop-font" @click="dropDown()">
      <slot name="footer"></slot>
      <i v-if="isshow" class="el-icon-caret-top"></i>
      <i v-else class="el-icon-caret-bottom"></i>
      <span v-if="isshow">隐藏</span>
      <span v-else>更多</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    opts: {
      type: Array,
      default: []
    }
  },
  data() {
    return {
      isshow: false,
      searchForm: {},
      defaultForm: {},
      formOpt: [],
      optionLoading: false,
      pickerOptionsStart: {
        disabledDate: this.disabledDateStart
      },
      pickerOptionsEnd: {
        disabledDate: this.disabledDateEnd
      }
    }
  },
  mounted() {
    this.process()
  },
  methods: {
    // props 数据处理
    process() {
      let formOpt = []
      let form = {}
      this.opts.forEach((item) => {
        let opt = Object.assign({}, item)
        formOpt.push(opt)
        // 设置默认值
        if (item.val !== null && item.val !== undefined && item.val !== "") {
          form[item.key] = item.val
        }
      })
      this.formOpt = formOpt;
      this.searchForm = form;
      this.defaultForm = { ...form }
    },
    handleRemoteSelectFocus(item) {
      if (!item.selects || item.selects.length === 0) {
        this.getOptions(null, item)
      }
    },
    handleSelectFocus(item) {
      if (!item.selects || item.selects.length === 0) {
        this.getEnumOptions(item)
      }
    },
    // 远程查询
    remoteMethod(item) {
      return (val) => {
        // console.log(val, item)
        this.getOptions(val, item)
      }
    },
    async getOptions(val, item) {
      this.optionLoading = true
      let params = {
        value: val,
        ...item.params
      }
      const result = await this.$store.dispatch(item.action, params)
      this.optionLoading = false
      item.selects = result
    },
    async getEnumOptions(item) {
      const result = await this.$store.dispatch(item.action, item.enumsType)
      item.selects = result
    },
    // 搜索数据处理
    search() {
      this.$emit("search", this.searchForm)
    },
    reset() {
      this.searchForm = { ...this.defaultForm }
      this.$emit("search", this.searchForm)
    },
    dropDown() {
      this.isshow = !this.isshow
    },
    disabledDateStart(time) {
      if (this.searchForm.endTime) {
        return time.getTime() > new Date(this.searchForm.endTime).getTime()
      } else {
        return false
      }
    },
    disabledDateEnd(time) {
      if (this.searchForm.startTime) {
        return (
          time.getTime() <
          new Date(this.searchForm.startTime).getTime() - 3600000 * 8
        )
      } else {
        return false
      }
    }
  }
}
</script>

<style lang="less">
.drop-box {
  margin-bottom: 10px;
  &:hover {
    box-shadow: rgba(232, 237, 250, 0.6) 0px 0px 8px 0px,rgba(232, 237, 250, 0.498039) 0px 2px 4px 0px;
    .drop-font {
      span {
        display: inline-block;
      }
      i {
        transform: translateX(-40px);
      }
    }
  }

  .drop-title {
    transition: 0.2s;
    color: #13c2c2;
    font-size: 14px;
    line-height: 40px;
    border-bottom: 1px solid #e5e9f2;
    text-indent: 8px;
    border-radius: 0px;
  }
  .drop-title-btn {
    float: right;
    line-height: 40px;
    padding-right: 10px;
  }
  .el-select {
    width: 100%;
  }
  .drop-font {
    height: 36px;
    box-sizing: border-box;
    background-color: rgb(255, 255, 255);
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    text-align: center;
    margin-top: -1px;
    color: rgb(211, 220, 230);
    cursor: pointer;
    position: relative;
    border-top: 1px solid rgb(234, 238, 251);
    transition: 0.2s;
    i {
      font-size: 12px;
      line-height: 36px;
      transition: 0.3s;
    }
    span {
      position: absolute;
      transform: translateX(-30px);
      font-size: 12px;
      line-height: 36px;
      display: none;
      transition: 0.3s;
    }
    &:hover {
      color: #13c2c2;
      background-color: rgb(249, 250, 252);
    }
  }
}

.form-schema {
  .el-col {
    label {
      line-height: 18px;
      text-align: right;
      display: block;
      margin-right: 10px;
      color: #8fa1b2;
      font-size: 14px;
      width: 100%;
    }
    .attr-label-wrap {
      display: flex;
      align-items: center;
      height: 33px;
    }
    .attr-label:before {
      content: "*";
      color: #f56c6c;
      margin-right: 4px;
    }
    .el-select {
      width: 100%;
    }
    .el-textarea__inner {
      height: 100px;
    }
  }
}
</style>
