<template>
  <div class="page page-customer-relation-list">

    <div class="module-filter">
      <el-form :inline="true">
        <el-form-item label="模板名称">
          <el-input v-model="filter.modelName"></el-input>
        </el-form-item>
        <el-form-item label="客户编码">
          <el-input v-model="filter.customerCode"></el-input>
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="filter.customerName"></el-input>
        </el-form-item>
        <el-form-item label="模板类型">
          <el-select v-model="filter.modelType" placeholder="请选择">
            <el-option v-for="item in option.templateType" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="filter.modelStatus" placeholder="请选择">
            <el-option v-for="item in option.status" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-button type="primary" @click="search">查询</el-button>
      </el-form>
    </div>

    <div class="module-list">

      <div class="module-btns">
        <el-button type="primary" @click="create">新增</el-button>
      </div>

      <el-table :data="list" style="width: 100%" v-loading="loading.list">
        <el-table-column
          type="index"
          label="序号">
        </el-table-column>
        <el-table-column
          prop="modelName"
          label="模板名称">
          <template slot-scope="scope">
            <router-link :to="{path: `/insteadOrder/relation/template/detail/${scope.row.id}`, query: { type: 'update' }}" :title="scope.row.modelName">
              {{ scope.row.modelName }}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="customerName"
          label="客户名称">
        </el-table-column>
        <el-table-column
          prop="customerCode"
          label="客户编码">
        </el-table-column>
        <el-table-column
          prop="modelType"
          label="模板类型">
        </el-table-column>
        <el-table-column
          label="操作">
          <template slot-scope="scope">
            <el-button @click="takeEffect(scope.row)" type="text" size="small">
              {{ scope.row.modelStatus === 1 ? '失效' : '生效' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          width="80px"
          label="状态">
          <template slot-scope="scope">
            <i class="status-icon status-on" v-if="scope.row.modelStatus === 1"></i>
            <i class="status-icon status-off" v-else></i>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.page"
        :page-sizes="[10, 20]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import {
  getTemplateList,
  updateTemplateStatus
} from '@/api/maintainment.js'
import { templateType, status } from './option'
export default {
  data () {
    return {
      filter: {
        modelName: null,
        customerCode: null,
        customerName: null,
        modelType: null,
        modelStatus: null,
        page: 1,
        pageSize: 10
      },
      option: {
        templateType,
        status
      },
      loading: {
        list: false
      },
      list: [],
      total: 0
    }
  },
  methods: {
    create () {
      // console.log()
      this.$router.push({
        path: '/insteadOrder/relation/template/detail/0',
        query: {
          type: 'create'
        }
      })
    },
    edit (data) {

    },
    takeEffect (item) {
      console.log(item)
      const status = {
        id: item.id,
        toStatus: item.modelStatus ? 0 : 1
      }
      updateTemplateStatus(status).then(res => {
        console.log(res)
        item.modelStatus = status.toStatus
      })
    },
    search () {
      // Compatiable Code: 后端不支持 空字符串 查询
      this.filter.customerCode = this.filter.customerCode || null
      this.filter.customerName = this.filter.customerName || null
      this.filter.modelName = this.filter.modelName || null
      this.loading.list = true
      getTemplateList(this.filter).then(res => {
        if (res.code === 200 && res.data) {
          this.list = res.data
          this.total = res.totalCount
        }
        this.loading.list = false
      }).catch(err => {
        this.loading.list = false
        this.$message.error(err.message || '获取数据失败')
      })
    },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.page = res
      this.search()
    }
  },
  mounted () {
    console.log(this.$route.path)
    this.search()
  }
}
</script>

<style lang="less" src="./style.less"></style>
<style lang="less">
.page-customer-relation-list {
  .el-form-item__label{
    width: 80px;
  }
}
</style>
