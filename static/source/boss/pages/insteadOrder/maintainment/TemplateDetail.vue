<template>
  <div class="page page-customer-template-detail" v-loading="loading.page">
    <div class="module-map-table">
      <h3>
        模板基本信息
      </h3>
      <!-- <div class="group"> -->
        <el-form :inline="true" :model="form" :rules="rules" ref="form">
          <el-form-item label="模板名称" prop="modelName">
            <p class="info" v-if="isUpdate"><strong>{{ form.modelName }}</strong></p>
            <el-input v-else v-model="form.modelName"></el-input>
          </el-form-item>
          <el-form-item label="客户信息" prop="customerName">
            <p class="info" v-if="isUpdate"><strong>{{ form.customerName }}</strong></p>
            <customer-select v-else v-model="form.customerName" @select="select"></customer-select>
          </el-form-item>
          <el-form-item label="模板类型" prop="modelType">
            <el-select v-model="form.modelType" placeholder="请选择" :disabled="isUpdate">
              <el-option v-for="item in option.templateType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      <!-- </div> -->
    </div>

    <div class="module-map-table">
      <h3>
        模板结构维护
      </h3>
      <!-- <div class="group"> -->
        <el-form :inline="true">
          <el-form-item label="模板头开始行">
            <el-input v-model="form.headLineBegin"></el-input>
          </el-form-item>
          <el-form-item label="模板头结束行">
            <el-input v-model="form.headLineEnd"></el-input>
          </el-form-item>
          <el-form-item label="模板详情开始行">
            <el-input v-model="form.detailLineBegin"></el-input>
          </el-form-item>
        </el-form>
      <!-- </div> -->
    </div>

    <div class="module-map-table">
      <h3>
        订单草稿字段映射
      </h3>
      <div class="group">
        <div class="title">订单头信息</div>
        <el-form :inline="true">
          <el-form-item label="客户名称">
            <el-input v-model="form.customerNameIndex"></el-input>
          </el-form-item>
          <el-form-item label="客户订单号">
            <el-input v-model="form.customerOrderId"></el-input>
          </el-form-item>
          <el-form-item label="客户参考日期">
            <el-input v-model="form.customerReferenceDate"></el-input>
          </el-form-item>
          <el-form-item label="收货人">
            <el-input v-model="form.receiver"></el-input>
          </el-form-item>
          <el-form-item label="收货电话">
            <el-input v-model="form.receiverPhone"></el-input>
          </el-form-item>
          <el-form-item label="收货地址">
            <el-input v-model="form.address"></el-input>
          </el-form-item>
          <el-form-item label="采购员">
            <el-input v-model="form.purchaser"></el-input>
          </el-form-item>
          <el-form-item label="采购部门">
            <el-input v-model="form.purchaseDepartment"></el-input>
          </el-form-item>
          <el-form-item label="附加字段1">
            <el-input v-model="form.headAdditionalField1"></el-input>
          </el-form-item>
          <el-form-item label="附加字段2">
            <el-input v-model="form.headAdditionalField2"></el-input>
          </el-form-item>
          <el-form-item label="附加字段3">
            <el-input v-model="form.headAdditionalField3"></el-input>
          </el-form-item>
          <el-form-item label="附加字段4">
            <el-input v-model="form.headAdditionalField4"></el-input>
          </el-form-item>
          <el-form-item label="附加字段5">
            <el-input v-model="form.headAdditionalField5"></el-input>
          </el-form-item>
          <el-form-item label="附加字段6">
            <el-input v-model="form.headAdditionalField6"></el-input>
          </el-form-item>
          <el-form-item label="附加字段7">
            <el-input v-model="form.headAdditionalField7"></el-input>
          </el-form-item>
          <el-form-item label="附加字段8">
            <el-input v-model="form.headAdditionalField8"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <div class="group">
        <div class="title">订单明细信息</div>
        <el-form :inline="true">
          <el-form-item label="震坤行物料号">
            <el-input v-model="form.zkhMaterialId"></el-input>
          </el-form-item>
          <el-form-item label="客户物料编号">
            <el-input v-model="form.customerMaterialId"></el-input>
          </el-form-item>
          <el-form-item label="客户物料描述">
            <el-input v-model="form.customerMaterialDescription"></el-input>
          </el-form-item>
          <el-form-item label="客户物料单位">
            <el-input v-model="form.customerMaterialUnit"></el-input>
          </el-form-item>
          <el-form-item label="首个交期">
            <el-input v-model="form.firstDeliveryDate"></el-input>
          </el-form-item>
          <el-form-item label="附加字段1">
            <el-input v-model="form.detailAdditionalField1"></el-input>
          </el-form-item>
          <el-form-item label="附加字段2">
            <el-input v-model="form.detailAdditionalField2"></el-input>
          </el-form-item>
          <el-form-item label="附加字段3">
            <el-input v-model="form.detailAdditionalField3"></el-input>
          </el-form-item>
          <el-form-item label="附加字段4">
            <el-input v-model="form.detailAdditionalField4"></el-input>
          </el-form-item>
          <el-form-item label="附加字段5">
            <el-input v-model="form.detailAdditionalField5"></el-input>
          </el-form-item>
          <el-form-item label="附加字段6">
            <el-input v-model="form.detailAdditionalField6"></el-input>
          </el-form-item>
          <el-form-item label="附加字段7">
            <el-input v-model="form.detailAdditionalField7"></el-input>
          </el-form-item>
          <el-form-item label="附加字段8">
            <el-input v-model="form.detailAdditionalField8"></el-input>
          </el-form-item>
          <el-form-item label="附加字段9">
            <el-input v-model="form.detailAdditionalField9"></el-input>
          </el-form-item>
          <el-form-item label="附加字段10">
            <el-input v-model="form.detailAdditionalField10"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div class="module-btns">
      <el-button type="primary" @click="beforeSave">提交</el-button>
    </div>
  </div>
</template>

<script>
import {
  createTemplate,
  updateTemplate,
  getTemplateDetail
} from '@/api/maintainment.js'
import CustomerSelect from './CustomerSelect'
import { templateType } from './option'

export default {
  data () {
    return {
      filter: {
        status: 0
      },
      loading: {
        page: false
      },
      option: {
        templateType
      },
      form: {
        modelName: '',
        modelType: 0,
        customerCode: '',
        customerName: '',

        headLineBegin: 0,
        headLineEnd: 0,
        detailLineBegin: 0,
        customerNameIndex: '',
        customerOrderId: '',
        customerReferenceDate: '',
        receiver: '',
        receiverPhone: '',
        address: '',
        purchaser: '',
        purchaseDepartment: '',
        headAdditionalField1: '',
        headAdditionalField2: '',
        headAdditionalField3: '',
        headAdditionalField4: '',
        headAdditionalField5: '',
        headAdditionalField6: '',
        headAdditionalField7: '',
        headAdditionalField8: '',
        zkhMaterialId: '',
        customerMaterialId: '',
        customerMaterialDescription: '',
        customerMaterialUnit: '',
        firstDeliveryDate: '',
        detailAdditionalField1: '',
        detailAdditionalField2: '',
        detailAdditionalField3: '',
        detailAdditionalField4: '',
        detailAdditionalField5: '',
        detailAdditionalField6: '',
        detailAdditionalField7: '',
        detailAdditionalField8: '',
        detailAdditionalField9: '',
        detailAdditionalField10: ''
      },
      rules: {
        modelName: [
          { required: true, message: '请输入模板名称', trigger: 'change' }
        ],
        customerName: [
          { required: true, message: '请输入客户名称', trigger: 'change' }
        ],
        modelType: [
          // { required: true, message: '请输入模板类型', trigger: 'change' }
        ]
      },
      type: ''
    }
  },
  computed: {
    isUpdate () {
      return this.type === 'update'
    }
  },
  components: {
    CustomerSelect
  },
  methods: {
    select (item) {
      this.form.customerCode = item.customerNumber
    },
    handleResult (res) {
      if (res.code === 200) {
        this.$message.success('保存成功！')
        setTimeout(() => {
          this.$closeTag(this.$route.path)
        }, 1000)
      } else {
        this.$message.error(res.msg || res.message || '保存失败！')
      }
    },
    beforeSave () {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.save()
        } else {
          return false
        }
      })
    },
    save () {
      if (this.type === 'create') {
        createTemplate(this.form).then(res => {
          this.handleResult(res)
        })
      }
      if (this.type === 'update') {
        let clone = JSON.parse(JSON.stringify(this.form))
        clone.customerName = clone.customerNameIndex
        updateTemplate(clone).then(res => {
          this.handleResult(res)
        })
      }
    }
  },
  mounted () {
    const { id } = this.$route.params
    const { type } = this.$route.query
    this.type = type
    if (type === 'update') {
      this.loading.page = true
      getTemplateDetail({
        modelId: id
      }).then(res => {
        this.loading.page = false
        // Attention: errCode
        if (res.code === 200 && res.data) {
          this.form = res.data
        }
      }).catch(err => {
        this.loading.page = false
        this.$message.error(err.message || '获取数据失败')
      })
    }
  }
}
</script>

<style lang="less" src="./style.less"></style>
<style lang="less">
.page-customer-template-detail {
  .module-map-table {
    margin: 0 0 30px 0;
    h3{
      margin: 0 0 20px 0;
    }
    .group{
      .title{
        margin: 0 0 20px 0;
      }
      padding: 20px;
      margin: 0 0 20px 0;
      border-bottom: 1px solid #ddd;
    }
  }
  .el-form-item__label {
    width: 120px;
  }
}
</style>
