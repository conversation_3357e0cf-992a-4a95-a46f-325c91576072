<template>
  <div class="page page-customer-maintainment-detail">

    <div class="module-filter">
      <el-form :inline="true">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="客户名称">
              <p class="bold">{{ customer.name }}</p>
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="客户编码">
              <p class="bold">{{ customer.code }}</p>
            </el-form-item>
         </el-col>
           <el-col :span="8">
            <el-form-item label="客户物料名称">
              <el-input v-model="filter.customerObjectName" clearable></el-input>
            </el-form-item>
           </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="客户物料编号">
              <el-input v-model="filter.customerObjectId" clearable></el-input>
            </el-form-item>
         </el-col>
           <el-col :span="8">
            <el-form-item label="震坤行物料名称">
              <el-input v-model="filter.zkhObjectName" clearable></el-input>
            </el-form-item>
         </el-col>
           <el-col :span="8">
            <el-form-item label="震坤行物料描述">
              <el-input v-model="filter.zkhObjectDescription" clearable></el-input>
            </el-form-item>
           </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="震坤行物料编号">
              <el-input v-model="filter.zkhObjectId" clearable></el-input>
            </el-form-item>
         </el-col>
           <el-col :span="8">
            <el-form-item label="客户物料税编">
              <el-input v-model="filter.customerObjectTaxCode" maxlength="100" clearable></el-input>
            </el-form-item>
         </el-col>
           <el-col :span="8" >
            <el-button type="primary" @click="handleFilterSearch" style="margin-left:140px">查询</el-button>
           </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="module-list">

      <div class="module-btns">
        <div class="btn-group">
          <el-upload class="btn-upload" accept=".xls,.xlsx,.xlsm" :before-upload="$validateFileType" :action="`/api-boss-product/ocr/relation/import?customerId=${$route.params.id}`"
            :on-success="onSuccess"
            name="fileURL">
            <el-button size="small" type="primary">导入</el-button>
          </el-upload>
          <a class="el-button btn-export" :href="`/api-boss-product/ocr/relation/export?customerId=${$route.params.id}`" target="_blank">导出</a>
          <a class="el-button btn-export" href="https://static.zkh360.com/all/file/2020-09-09/%E5%AE%A2%E6%88%B7%E7%89%A9%E6%96%99%E5%85%B3%E7%B3%BB%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx" target="_blank">获取模板</a>
        </div>
        <div class="btn-group">
          <el-button type="primary" @click="opBatchRemove" :disabled="selected.length === 0">批量删除</el-button>
          <el-button type="primary" @click="create">新增</el-button>
        </div>
      </div>

      <el-table :data="list" style="width: 100%" @selection-change="opSelectChange">
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="序号"></el-table-column>
        <el-table-column prop="customerObjectName" label="客户物料名称"></el-table-column>
        <el-table-column prop="customerObjectId" label="客户物料编号"></el-table-column>
        <el-table-column prop="customerObjectUnit" label="客户物料单位"></el-table-column>
        <el-table-column prop="customerObjectSpecification" label="客户物料规格型号"></el-table-column>
        <el-table-column prop="zkhObjectDescription" label="震坤行物料描述"></el-table-column>
        <el-table-column prop="zkhObjectId" label="震坤行物料号"></el-table-column>
        <el-table-column prop="zkhObjectUnit" label="震坤行物料号单位"></el-table-column>
        <el-table-column
          label="客户标准单位数量 / 震坤行标准单位数量">
          <template slot-scope="scope">
            <!-- &lt;=&gt; -->
            {{ scope.row.customerObjectQuantity }} : {{ scope.row.zkhObjectQuantity }}
          </template>
        </el-table-column>
        <el-table-column prop="customerObjectTaxCode" label="客户物料税编"/>
        <el-table-column
          label="操作">
          <template slot-scope="scope">
            <el-button @click="edit(scope.row)" type="text" size="small">
              编辑
            </el-button>
            <el-button @click="takeEffect(scope.row)" type="text" size="small">
              {{ scope.row.status === 1 ? '失效' : '生效' }}
            </el-button>
            <el-button @click="opRemove(scope)" type="text" size="small">
              删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          width="60px"
          label="状态">
          <template slot-scope="scope">
            <i class="status-icon status-on" v-if="scope.row.status === 1"></i>
            <i class="status-icon status-off" v-else></i>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.page"
        :page-sizes="[10, 20, 50, 100, 200]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>

      <el-dialog title="添加关系" :visible.sync="show.create">
        <el-form :inline="true" :model="form" :rules="rules" ref="form">
          <el-form-item label="客户物料名称" prop="customerObjectName">
            <el-input v-model="form.customerObjectName"></el-input>
          </el-form-item>
          <el-form-item label="客户物料编号" prop="customerObjectId">
            <el-input v-model="form.customerObjectId"></el-input>
          </el-form-item>
          <el-form-item label="客户物料单位" prop="customerObjectUnit">
            <el-input v-model="form.customerObjectUnit"></el-input>
          </el-form-item>
          <el-form-item label="客户物料规格型号" prop="customerObjectSpecification">
            <el-input v-model="form.customerObjectSpecification"></el-input>
          </el-form-item>
          <el-form-item label="震坤行物料号" prop="zkhObjectId">
            <el-input v-model="form.zkhObjectId"></el-input>
          </el-form-item>
          <el-form-item label="客户标准单位数量" prop="customerObjectQuantity">
            <el-input v-model.number="form.customerObjectQuantity" min="1"></el-input>
          </el-form-item>
          <el-form-item label="震坤行标准单位数量" prop="zkhObjectQuantity">
            <el-input v-model.number="form.zkhObjectQuantity" min="1"></el-input>
          </el-form-item>
          <el-form-item label="客户物料税编" prop="customerObjectTaxCode">
            <el-input v-model="form.customerObjectTaxCode" min="1" maxlength="100" ></el-input>
          </el-form-item>
          <p class="tip">
            <i class="el-icon-warning-outline"></i>
            客户物料单位、客户标准单位数量、zkh标准单位数量 三个字段，同时填写才有意义。</p>
        </el-form>
        <p slot="footer" class="dialog-footer">
          <el-button type="primary" @click="saveRelation">{{ type === 'update' ? '保存': '添加' }}</el-button>
        </p>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import {
  getRelationLists,
  addRelation,
  updateRelation,
  updateRelationStatus
} from '@/api/maintainment.js'
import { cloneDeep } from 'lodash'
import { apiProduct } from '@/api/boss'

// const ruleNumber = (rule, value, callback) => {
//   let val = parseInt(value, 10)
//   if (val < 0 || val % 1 !== 0) {
//     callback(new Error('必须为正整数'))
//   } else {
//     callback()
//   }
// }

export default {
  data () {
    return {
      type: '',
      show: {
        create: false
      },
      customer: {
        customerId: 0,
        name: '',
        code: ''
      },
      filter: {
        zkhObjectId: null,
        zkhObjectName: null,
        customerObjectId: null,
        customerObjectName: null,
        zkhObjectDescription: '',
        page: 1,
        pageSize: 10
      },
      form: {
        zkhObjectId: '',
        zkhObjectUnit: '',
        zkhObjectQuantity: 0,
        customerObjectName: '',
        customerObjectId: '',
        customerObjectUnit: '',
        customerObjectSpecification: '',
        customerObjectQuantity: 0
      },
      rules: {
        zkhObjectId: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        // zkhObjectUnit: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        // zkhObjectQuantity: [
        //   { required: true, message: '请输入', trigger: 'blur' },
        //   { validator: ruleNumber, trigger: 'blur' }
        // ],
        // customerObjectName: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        customerObjectId: [
          { required: true, message: '请输入', trigger: 'blur' }
        ]
        // customerObjectUnit: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        // customerObjectSpecification: [
        //   { required: true, message: '请输入', trigger: 'blur' }
        // ],
        // customerObjectQuantity: [
        //   { required: true, message: '请输入', trigger: 'blur' },
        //   { validator: ruleNumber, trigger: 'blur' }
        // ]
      },
      list: [],
      total: 0,
      selected: []
    }
  },
  methods: {
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.page = res
      this.search()
    },
    handleFilterSearch () {
      this.filter.page = 1
      this.search()
    },
    onSuccess (res) {
      console.log(res);
      this.$message.success('上传完成，导入结果已发送邮件。')
      // Attention: 数据插入有延迟
      setTimeout(() => {
        this.search()
      }, 1500)
    },
    handleResult (res) {
      if (res.code === 200) {
        this.$message.success('保存成功！')
      } else {
        this.$message.error(res.msg || res.message || '保存失败！')
      }
    },
    saveRelation () {
      this.$refs.form.validate(valid => {
        if (valid) {
          let handler
          if (this.type === 'create') {
            handler = addRelation({
              customerId: this.$route.params.id,
              ...this.form
            })
          }
          if (this.type === 'update') {
            handler = updateRelation(this.form)
          }
          handler.then(res => {
            this.show.create = false
            setTimeout(() => {
              this.search()
            }, 1000)
            this.handleResult(res)
            console.log(res)
          }).catch(err => {
            this.show.create = false
            console.log(err)
          })
        } else {
          return false
        }
      })
    },
    importData () {
      //
    },
    exportData () {
      //
    },
    create () {
      this.show.create = true
      this.form = {}
      this.type = 'create'
    },
    edit (data) {
      this.show.create = true
      this.form = cloneDeep(data)
      this.type = 'update'
    },
    trimParams (data) {
      let ret = { ...data }
      for (let d in ret) {
        if (ret[d] === '' || ret[d] === undefined || ret[d] === null) {
          delete ret[d]
        }
      }
      return ret
    },
    search () {
      // Compatiable Code: 后端不支持 空字符串 查询
      this.filter.zkhObjectId = this.filter.zkhObjectId || null
      this.filter.customerObjectId = this.filter.customerObjectId || null
      const params = {
        customerId: this.$route.params.id,
        ...this.filter
      }
      const fParams = this.trimParams(params)
      getRelationLists(fParams).then(res => {
        if (res.code === 200 && res.data) {
          console.log(res.data)
          this.customer.name = res.data.customerName
          this.customer.code = res.data.customerCode
          this.list = res.data.ocrRelationVOList
          this.total = res.totalCount
        }
      }).catch(err => {
        console.error(err)
      })
    },
    takeEffect (item) {
      console.log(item)
      const status = {
        id: item.id,
        toStatus: item.status ? 0 : 1
      }
      updateRelationStatus(status).then(res => {
        console.log(res)
        item.status = status.toStatus
      })
    },
    opBatchRemove () {
      this.$confirm('请确认是否要删除?', '提示', { type: 'warning' })
        .then(() => {
          console.log(this.selected)
          const ids = this.selected.map(i => i.id)
          this.apiRemove(ids)
        }).catch(() => {

        })
    },
    opRemove (scope) {
      this.$confirm('请确认是否要删除?', '提示', { type: 'warning' })
        .then(() => {
          const ids = scope.row.id
          this.apiRemove([ids])
        }).catch(() => {})
    },
    apiRemove (ids) {
      apiProduct({
        url: '/ocr/list',
        method: 'PUT',
        data: {
          ids
        },
        complete: res => {
          if (res.code === 200) {
            this.search()
          } else {
            this.$message.error(`API ERROR @ ${res.msg}`)
          }
          console.log(res)
        }
      })
    },
    opSelectChange (val) {
      this.selected = val
      console.log(val)
    }
  },
  mounted () {
    console.log(this.$route)
    this.search()
  }
}
</script>
<style lang="less" src="./style.less"></style>
<style lang="less">
.page-customer-maintainment-detail{
  .btn-upload{
    display: inline-block;
    margin: 0 10px 0 0;
  }
  .el-upload-list{
    display: none;
  }
  .btn-export{
    padding: 9px 15px;
    font-size: 12px;
    border-radius: 3px;
    color: #FFFFFF;
    background-color: #597bee;
    border-color: #597bee;
    &:hover{
      background: #7a95f1;
      border-color: #7a95f1;
      color: #FFFFFF;
    }
  }
  .el-form-item__content{
    min-width: 200px;
  }
  .el-form-item__label{
    width: 140px;
  }
  .module-btns{
    flex-direction: row;
    justify-content: space-between;
  }
  .bold{
    font-weight: 800;
  }
  .tip {
    margin: 30px 0 0;
    color: #aaa;
  }
}
</style>
