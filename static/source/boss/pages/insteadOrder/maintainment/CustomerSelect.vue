<template>
  <el-select
    popper-class="module-select-customer"
    filterable clearable remote placeholder="请输入关键词"
    :value="name" :remote-method="getCustomerList" :loading="loading.customer"
    @change="afterChangeCustomer" @clear="afterClear">
    <el-option v-for="(item, index) in option.customer"
      :key="item.customerNumber" :label="item.customerName" :value="item.customerNumber"
      :disabled="index === 0">
      <p class="option" :class="{ bold: index === 0 }">
        <span >{{ item.customerNumber }}</span>
        <span >{{ item.cityName }}</span>
        <span >{{ item.customerName }}</span>
      </p>
    </el-option>
  </el-select>
</template>

<script>

import { searchClients } from '@/api/orderSale'

export default {
  data () {
    return {
      name: this.value,
      loading: {
        customer: false
      },
      option: {
        customer: []
      }
    }
  },
  props: {
    value: {
      type: String
    }
  },
  watch: {
    value (val) {
      this.name = val
      this.$emit('inpput', val)
    }
  },
  methods: {
    afterChangeCustomer (val) {
      const target = this.option.customer.find(item => item.customerNumber === val)
      this.$emit('input', target ? target.customerName : '')
      this.$emit('select', target)
    },
    afterClear () {
      this.$emit('input', '')
    },
    getCustomerList (query) {
      if (query !== '') {
        this.loading.customer = true
        searchClients(query).then(res => {
          console.log('customer list', res)
          if (res.code === 200 && res.data) {
            this.option.customer = [
              {
                customerNumber: '客户编码',
                cityName: '城市',
                customerName: '客户名称'
              },
              ...res.data
            ]
          }
          this.loading.customer = false
        }).catch(err => {
          console.log(err)
          this.loading.customer = false
        })
      } else {
        this.option.customer = []
      }
    }
  }
}
</script>

<style lang="scss">
.module-select-customer{
  width: 500px;
  .option{
    display: flex;
    span{
      width: 20%;
      &:last-child{
        width: auto;
      }
    }
  }
}
</style>
