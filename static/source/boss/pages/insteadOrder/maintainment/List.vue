<template>
  <div class="app-container page-customer-maintainment">

    <div class="module-filter">
      <el-form :inline="true">
        <el-form-item label="客户名称">
            <customer-select v-model="filter.customerName" @select="selectCustomer"></customer-select>
          </el-form-item>
          <!-- <el-form-item label="客户编码">
            <el-input v-model="filter.customerCode"></el-input>
          </el-form-item> -->
          <el-form-item label="状态">
            <el-select v-model="filter.customerStatus" placeholder="请选择">
              <el-option v-for="item in option.status" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
          </el-form-item>
      </el-form>
    </div>

    <div class="module-list">
      <div class="module-btns">
        <el-button type="primary" @click="add">新增</el-button>
      </div>

      <el-table :data="list" style="width: 100%">
        <el-table-column
          type="index"
          label="序号">
        </el-table-column>
        <el-table-column
          prop="customerName"
          label="客户名称">
          <template slot-scope="scope">
            <router-link :to="{path: `maintainment/${scope.row.id}`}" :title="scope.row.customerName">
              {{ scope.row.customerName }}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column
          prop="customerCode"
          label="客户编码">
        </el-table-column>
        <el-table-column
          label="操作">
          <template slot-scope="scope">
            <el-button @click="takeEffect(scope.row)" type="text" size="small">
              {{ scope.row.customerStatus === 1 ? '失效' : '生效' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          width="80px"
          label="状态">
          <template slot-scope="scope">
            <i class="status-icon status-on" v-if="scope.row.customerStatus === 1"></i>
            <i class="status-icon status-off" v-else></i>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.page"
        :page-sizes="[10, 20, 50, 100, 200]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>

    <el-dialog title="选择客户添加" :visible.sync="show.create">
      <el-form :inline="true" :rules="rules" :model="form">
        <el-form-item label="客户名称" prop="customerName">
          <customer-select v-model="form.customerName" @select="afterChangeCustomer"></customer-select>
        </el-form-item>
      </el-form>
      <p class="info" v-if="this.current.customer && this.current.customer.customerName">
        <span>
          {{ this.current.customer.customerNumber }}
        </span>
        <span>
          {{ this.current.customer.customerName }}
        </span>
      </p>
      <p slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createCustomer">添加</el-button>
      </p>
    </el-dialog>

  </div>
</template>

<script>
import {
  getLists,
  addCustomer,
  updateCustomer
} from '@/api/maintainment.js'
import CustomerSelect from './CustomerSelect'
import { status } from './option'
export default {
  data () {
    return {
      show: {
        create: false
      },
      current: {
        customer: {}
      },
      loading: {
        customer: false
      },
      rules: {
        customerName: [
          { required: true, message: '请输入客户名称', trigger: 'change' }
        ]
      },
      filter: {
        customerName: '',
        customerCode: null,
        customerStatus: null,
        page: 1,
        pageSize: 10
      },
      form: {
        customerName: ''
      },
      option: {
        status,
        customer: []
      },
      list: [],
      total: 0
    }
  },
  components: {
    CustomerSelect
  },
  computed: {
  },
  methods: {
    trimParams (data) {
      let ret = { ...data }
      for (let d in ret) {
        if (ret[d] === '' || ret[d] === undefined || ret[d] === null) {
          delete ret[d]
        }
      }
      return ret
    },
    selectCustomer (val) {
      this.filter.customerCode = val?.customerNumber
    },
    search () {
      this.filter.customerCode = this.filter.customerCode || null
      const params = this.trimParams(this.filter)
      delete params.customerName
      getLists(params).then(res => {
        if (res.code === 200 && res.data) {
          this.list = res.data
          this.total = res.totalCount
        }
      }).catch(err => {
        console.log(err)
      })
    },
    takeEffect (item) {
      const operation = item.customerStatus === 1 ? 'forbid' : 'active'
      updateCustomer(operation, { id: item.id }).then(res => {
        item.customerStatus = operation === 'active' ? 1 : 2
      })
    },
    afterChangeCustomer (target) {
      // let target = this.option.customer.find(item => item.customerNumber === val)
      this.current.customer = target
      console.log(target)
    },
    handleResult (res) {
      if (res.code === 200) {
        this.$message.success('保存成功！')
      } else {
        this.$message.error(res.msg || res.message || '保存失败！')
      }
    },
    add () {
      this.current.customer = null
      this.show.create = true
    },
    createCustomer () {
      if (!this.current.customer && this.current.customer.customerName) {
        this.$message.error('请选择客户')
        return
      }
      const { customerNumber: customerNo, customerName } = this.current.customer
      const customer = { customerNo, customerName }
      addCustomer(customer).then(res => {
        this.show.create = false
        this.handleResult(res)
        // this.list.push({
        //   ...customer,
        //   customerStatus: 1,
        //   customerCode: customerNo
        // })
        this.search()
      }).catch(err => {
        this.$message.error('API ERROR: ' + err.msg || err.message || '保存失败！')
      })
    },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.page = res
      this.search()
    }
  },
  mounted () {
    this.search()
  }
}
</script>

<style lang="less" src="./style.less"></style>
<style lang="scss">
.page-customer-maintainment {
  padding: 20px;
  .info{
    font-size: 16px;
    margin: 20px 0 0;
    padding: 10px;
    background-color: #eee;
    border-radius: 5px;
    span{
      margin-right: 10px;
    }
  }
}
</style>
