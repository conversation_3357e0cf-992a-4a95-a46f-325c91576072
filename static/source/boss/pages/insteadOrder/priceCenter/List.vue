<template>
  <div class="page page-price-center-list">
    <div class="pane">
      <div class="module module-filter">
        <el-form :inline="true">
          <el-form-item label="协议单号">
            <el-input v-model="filter.protocolNo" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="客户名称">
            <Client v-model="filter.customerNo" placeholder="请输入客户名称或编号" />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="filter.creatorName" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="创建日期" style="width:auto">
            <el-date-picker v-model="tempFilter.dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onDatePick"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="filterSearch" type="primary">查询</el-button>
            <el-button @click="filterReset" type="default">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="pane pane-list">
      <div class="module module-operation">
        <p class=title>客户商品价格列表</p>
        <div class="group">
          <el-button @click="opCreate" type="primary">新增</el-button>
          <!-- <el-button @click="opImport" type="default">批量导入</el-button>
          <el-button @click="opDownload" type="default">下载模板</el-button> -->
        </div>
      </div>
      <div class="module module-list">
        <el-table :data="list" class="boss-table" v-loading="loading.list" max-height="520">
          <el-table-column prop="protocolNo" label="协议单号" width="180px" fixed="left">
            <template slot-scope="scope">
              <router-link :to="`/insteadOrder/priceCenter/detail/${scope.row.id}?type=info`" class="btn-detail">
                {{ scope.row.protocolNo }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column prop="customerNo" label="客户编码" width="100px" fixed />
          <el-table-column show-overflow-tooltip prop="customerName" label="客户名称" width="160px" fixed />
          <el-table-column show-overflow-tooltip prop="salesOrganizationName" label="销售组织" width="200px" />
          <el-table-column prop="distributionChannelName" label="直分销渠道" width="160px" />
          <el-table-column prop="taxStatusName" label="含未税表示" width="100px" />
          <el-table-column label="协议有效期" width="160px">
            <template slot-scope="{ row }">
              {{ formatTime(row.validityBegin) }} ~ {{ formatTime(row.validityEnd) }}
            </template>
          </el-table-column>
          <el-table-column prop="creatorName" label="创建人" width="160px" />
          <el-table-column prop="creatorOn" label="创建日期" width="160px">
            <template slot-scope="{ row }">
              {{ formatTime(row.creatorOn) }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <router-link :to="`/insteadOrder/priceCenter/detail/${scope.row.id}?type=info`" class="btn-detail">查看详情</router-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="pane">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="filter.page"
        :page-sizes="[10, 20]"
        :page-size="filter.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { apiProduct } from '@/api/boss'
import Client from '@/components/SearchFields/client'

export default {
  data () {
    return {
      filter: {
        protocolNo: '',
        customerNo: '',
        creatorName: '',
        createdTime: '',
        createdTimeTo: '',
        page: 1,
        pageSize: 10
      },
      tempFilter: {
        dateRange: []
      },
      loading: {
        list: false
      },
      list: [],
      total: 0
    }
  },
  components: {
    Client
  },
  methods: {
    formatTime(time) {
      return time && time.replace(/T.+/, '')
    },
    filterSearch () {
      this.filter.page = 1
      this.search()
    },
    filterReset () {
      for (const key in this.filter) {
        this.filter[key] = null
      }
      this.tempFilter.dateRange = []
      this.filter.page = 1
      this.filter.pageSize = 10
    },
    opCreate () {
      this.$router.push({
        path: '/insteadOrder/priceCenter/detail/0',
        query: { type: 'create', tagName: '创建客户商品价格' }
      })
    },
    opImport () {},
    opDownload () {},
    search () {
      this.loading.list = true
      apiProduct({
        url: '/price/center',
        query: this.filter,
        complete: res => {
          if (res.code === 200) {
            this.list = res.data || []
            this.total = res.totalCount
          } else {
            //
          }
          this.loading.list = false
        }
      })
    },
    onDatePick (date) {
      const [ min, max ] = date
      this.filter.createdTime = min
      this.filter.createdTimeTo = max
    },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.page = res
      this.search()
    }
  },
  mounted () {
    this.search()
  }
}
</script>

<style lang="less">
.page-price-center-list {
  .pane {
    margin-bottom: 20px;
  }
  .pane-list {
    border: 1px solid #eee;
    border-radius: 5px;
  }
  .module-filter {
    background-color: #eee;
    padding: 20px;
    border-radius: 5px;
  }
  .module-operation {
    padding: 10px;
    display: flex;
    justify-content: space-between;
    .title {
      font-size: 18px;
      line-height: 32px;
    }
  }
  .el-pagination {
    text-align: right;
  }
  .btn-detail{
    color: #597bee;
  }
}
</style>
