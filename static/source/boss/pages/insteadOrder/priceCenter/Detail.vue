<template>
  <div class="page page-price-center-detail">

    <div class="pane pane-clients">
      <div class="pane-title">主客户信息</div>
      <div class="detail-info" v-if="isInfo || isUpdate">
        <ul>
          <li>
            <span class="title"><strong>{{ info.priceCustomerInfo.customerNo }}</strong></span>
            <span>{{ info.priceCustomerInfo.customerName }}</span>
          </li>
          <li>
            <span class="title">协议单号: </span>
            <span>{{ info.priceCustomerInfo.protocolNo }}</span>
          </li>
          <li style="text-align:right;">
            <el-button @click="opEditPage" type="primary" v-show="isInfo" size="mini">修改</el-button>
            <a class="el-button el-button--small btn-export" :href="`/api-boss-product/price/center/${id}/details/export`" target="_blank" v-show="isInfo">导出</a>
          </li>
          <li>
            <span class="title">分销渠道: </span>
            <span>{{ info.priceCustomerInfo.distributionChannelName }}</span>
          </li>
          <li>
            <span class="title">销售组织: </span>
            <span>{{ info.priceCustomerInfo.salesOrganizationName }}</span>
          </li>
          <li>
            <span class="title">含未税标识: </span>
            <span>{{ info.priceCustomerInfo.taxStatusName }}</span>
          </li>
          <li>
            <span class="title">协议有效期: </span>
            <el-date-picker v-if="isUpdate" v-model="form.dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onDatePick"></el-date-picker>
            <span v-if="isInfo">{{ formatTime(info.priceCustomerInfo.validityBegin) }} ~ {{ formatTime(info.priceCustomerInfo.validityEnd) }}</span>
          </li>
          <li>
            <span class="title">创建日期: </span>
            <span>{{ fmt(info.priceCustomerInfo.creatorOn) }}</span>
          </li>
          <li>
            <span class="title">修改日期: </span>
            <span>{{ fmt(info.priceCustomerInfo.modifiedOn) }}</span>
          </li>
          <li>
            <span class="title">显示子客户: </span>
            <span><el-switch v-model="form.hasSubClient" @change="afterSwitch(form)"></el-switch></span>
          </li>
        </ul>
      </div>
      <el-form v-if="isCreate" :inline="true" :model="info.priceCustomerInfo" :rules="rules" ref="form">
        <el-form-item label="选择客户" style="width: 480px" prop="customerNo">
          <ClientSelect v-model="info.priceCustomerInfo.customerNo" placeholder="请输入客户名称或编号" @change="changeCustomer" />
        </el-form-item>
        <el-form-item label="分销渠道" style="width: 300px" prop="distributionChannel">
          <el-select v-model="info.priceCustomerInfo.distributionChannel" @change="changeDistributionChannel">
            <el-option v-for="(item, index) in option.distributionChannel" :key="index" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="销售组织" style="width: 300px" prop="salesOrganization">
          <el-select v-model="info.priceCustomerInfo.salesOrganization" style="width:100%" value-key="idx" placeholder="请选择销售组织" @change="changeSaleOrg">
            <el-option v-for="(item, index) in CompuSalesOrg" :key="index" :label="item.value" :value="item.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="协议有效期" style="width: 480px" prop="validityBegin">
          <el-date-picker v-model="form.dateRange" value-format="yyyy-MM-dd" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="onDatePick"></el-date-picker>
        </el-form-item>
        <el-form-item label="含未税标识" style="width: 300px">
          {{ info.priceCustomerInfo.taxStatus ? '含税' : '未税' }}
        </el-form-item>
        <el-form-item label="显示子客户" style="width: 300px">
          <el-switch v-model="form.hasSubClient" @change="afterSwitch(form)"></el-switch>
        </el-form-item>
      </el-form>
    </div>

    <div class="pane pane-clients" v-show="form.hasSubClient">
      <div class="pane-title">子客户信息</div>
      <el-form :inline="true" v-if="isCreate || isUpdate">
        <el-form-item label="选择添加客户">
          <ClientSelect v-model="form.subClient" placeholder="请输入客户名称或编号" @change="changeSubCustomer" />
          <!-- <el-button type="primary" @click="addSubClient">添加</el-button> -->
        </el-form-item>
      </el-form>
      <el-table :data="info.subPriceCustomerInfos" class="boss-table" max-height="240">
        <el-table-column type="index" label="序号" />
        <el-table-column prop="customerNo" label="客户编号" />
        <el-table-column prop="customerName" label="客户名称" />
        <el-table-column label="操作" v-if="isCreate || isUpdate">
          <template slot-scope="scope">
            <a href="javascript:;" @click="removeSubClient(scope)">删除</a>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pane">
      <div class="pane-title">商品信息</div>
      <div class="module module-filter" v-if="isCreate || isUpdate">
        <div class="group">
          <el-form :inline="true">
            <el-form-item required label="选择商品">
              <el-select v-model="formProduct.type" @change="changeFormProductType" style="width:130px">
                <el-option :value="1" label="商品编号/名称"></el-option>
                <el-option :value="2" label="客户物料号"></el-option>
              </el-select>

              <el-select v-model="formProduct.sku" filterable clearable remote reserve-keyword value-key="skuNo" placeholder="请输入商品编号/名称"
                :remote-method="searchSkuList" :loading="option.skuList.loading">
                <el-option
                  v-for="(item, index) in option.skuList.options"
                  :label="isTypeSku ? `【${item.skuNo}】 ${item.materialDescribe}` : `${item.customerSkuNo} ${item.skuNo} ${item.skuName}`"
                  :key="index" :value="item" :disabled="index === 0">
                  <div class="opt-item" :style="{fontWeight:index===0?'bold':'normal'}">
                    <template  v-if="isTypeSku">
                      <span>{{ item.skuNo }}</span>
                      <span>{{ item.materialDescribe || ''}}</span>
                    </template>
                    <template v-else>
                      <span>{{ item.customerSkuNo }}</span>
                      <span>{{ item.skuNo }}</span>
                      <span>{{ item.skuName || '' }}</span>
                    </template>
                  </div>
                </el-option>
              </el-select>
              &nbsp;
              <el-button type="primary" plain @click="addSku">确认添加</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="group">
          <el-upload class="btn-upload" :action="`/api-boss-product/price/center/detail/import?salesOrganization=${info.priceCustomerInfo.salesOrganization}&customerNo=${info.priceCustomerInfo.customerNo}`"
            :on-success="opImport"
            :show-file-list="false"
            :accept="acceptFileType.commonType"
            :before-upload="$validateFileType"
            name="file">
            <el-button size="small" type="primary" :disabled="!info.priceCustomerInfo.salesOrganization">导入明细</el-button>
          </el-upload>
          <a class="el-button el-button--small btn-export" href="https://static.zkh360.com/file/all/%E5%AE%A2%E6%88%B7%E5%8D%8F%E8%AE%AE%E4%BB%B7%E6%A0%BC-%E6%98%8E%E7%BB%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E7%89%88.xlsx" target="_blank">下载模板</a>
        </div>
      </div>
      <div class="module module-list">
        <el-table :data="info.priceSkuDetailInfos" class="boss-table" v-loading="loading.list" max-height="500">
          <el-table-column type="index" label="序号" fixed="left" />
          <el-table-column prop="sku" label="SKU" fixed />
          <el-table-column prop="materialGroupName" label="物料组" width="160px" />
          <el-table-column show-overflow-tooltip prop="material" label="物料描述" width="160px" />
          <el-table-column prop="quantityUnit" label="单位" />
          <el-table-column prop="suggestedPrice" label="建议零售价" width="120px" />
          <el-table-column prop="zkhPrice" label="ZKH单价" width="160px">
            <template slot-scope="{ row }">
              <span v-if="isInfo">{{ row.zkhPrice }}</span>
              <el-input-number v-else v-model="row.zkhPrice" :controls="false" :precision="6" @change="changeSkuPrice(row, 'zkh')"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="customerMaterialId" label="客户物料号" />
          <el-table-column show-overflow-tooltip prop="customerMaterialName" label="客户物料描述" width="160px" />
          <el-table-column prop="customerMaterialUnit" label="客户物料单位" width="100px" />
          <el-table-column prop="customerMaterialPrice" label="客户物料单价" width="160px">
            <template slot-scope="{ row }">
              <el-input-number v-if="(isCreate || isUpdate) && row.customerMaterialCount && row.zkhMaterialCount" v-model="row.customerMaterialPrice" :controls="false" :precision="6" @change="changeSkuPrice(row, 'customer')"></el-input-number>
              <span v-else>{{ row.customerMaterialPrice || '' }}</span>
            </template>
          </el-table-column>
          <el-table-column width="120px">
            <template slot="header">
              <el-tooltip class="item" effect="dark" content="ZKH单价：客户物料单价=标准单位数量比（客户：ZKH）" placement="top">
                <span><i class="el-icon-warning"></i> 标准单位数量比（客户:ZKH）</span>
              </el-tooltip>
            </template>
            <template slot-scope="{ row }">
              <span v-show="row.customerMaterialCount && row.zkhMaterialCount">{{ row.customerMaterialCount }} : {{ row.zkhMaterialCount }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" v-if="isCreate || isUpdate">
            <template slot-scope="scope">
              <a href="javascript:;" @click="removeSku(scope)">删除</a>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="pane pane-op">
      <div class="module" v-if="isCreate">
        <el-button @click="opSave" type="primary">提交</el-button>
      </div>
      <div class="module" v-if="isUpdate">
        <el-button @click="opUpdate" type="primary">保存</el-button>
      </div>
    </div>

  </div>
</template>

<script>
import { apiProduct } from '@/api/boss'
import * as createOrder from '@/api/orderSale'
import ClientSelect from '@/components/SearchFields/client'
import * as helper from '@/utility/helper'
import { mapState } from 'vuex'

export default {
  data () {
    return {
      mode: '',
      form: {
        subClient: '',
        hasSubClient: true,
        dateRange: []
      },
      info: {
        priceCustomerInfo: {
          customerNo: '',
          customerName: '',
          distributionChannel: '',
          salesOrganization: '',
          validityBegin: '',
          validityEnd: '',
          taxStatus: 0
        },
        priceSkuDetailInfos: [],
        subPriceCustomerInfos: []
      },
      rules: {
        customerNo: [{ required: true, message: '请选择', trigger: 'change' }],
        distributionChannel: [{ required: true, message: '请选择', trigger: 'change' }],
        salesOrganization: [{ required: true, message: '请选择', trigger: 'change' }],
        validityBegin: [{ required: true, message: '请选择', trigger: 'blur' }]
      },
      formProduct: {
        type: 1,
        sku: ''
      },
      option: {
        saleOrg: [],
        distributionChannel: [],
        skuList: []
      },
      loading: {
        list: false
      },
      list: [],
      total: 0
    }
  },
  components: {
    ClientSelect
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType
    }),
    isTypeSku () {
      return this.formProduct.type === 1
    },
    isCreate () {
      return this.mode === 'create'
    },
    isInfo () {
      return this.mode === 'info'
    },
    isUpdate () {
      return this.mode === 'update'
    },
    CompuSalesOrg () {
      return this.option.saleOrg.filter(item => {
        return item.distributionChannel === this.info.priceCustomerInfo.distributionChannel
      })
    }
  },
  methods: {
    formatTime(time) {
      return time && time.replace(/T.+/, '')
    },
    fmt: helper.formatTime,
    onDatePick (date) {
      const [start, end] = date
      this.info.priceCustomerInfo.validityBegin = start
      this.info.priceCustomerInfo.validityEnd = end
    },
    opEditPage () {
      this.mode = 'update'
    },
    opImport (res) {
      if (res.code === 200) {
        const { priceSkuDetailFails, priceSkuDetailSuccesses } = res.data
        let fails = 0
        priceSkuDetailSuccesses && priceSkuDetailSuccesses.forEach(item => {
          const isDuplicated = this.info.priceSkuDetailInfos.find(it => it.sku === item.sku)
          if (isDuplicated) {
            fails++
          } else {
            this.info.priceSkuDetailInfos.push(item)
          }
        })
        fails += priceSkuDetailFails ? priceSkuDetailFails.length : 0
        if (fails) {
          this.$message.error(`有${fails}条未成功导入，请检查数据的正确性`)
        }
      } else {
        this.$message.error(`API ERROR @ ${res.msg}`)
      }
    },
    opDownload () {},
    afterSwitch () {},
    getInfo(id) {
      apiProduct({
        url: `/price/center/${id}`,
        complete: res => {
          if (res.code === 200) {
            const { priceCustomerInfo, priceSkuDetailInfos, subPriceCustomerInfos } = res.data
            this.info = {
              priceCustomerInfo,
              priceSkuDetailInfos: priceSkuDetailInfos || [],
              subPriceCustomerInfos: subPriceCustomerInfos || []
            }
            this.form.dateRange = [priceCustomerInfo.validityBegin, priceCustomerInfo.validityEnd]
            this.form.hasSubClient = this.info.subPriceCustomerInfos.length > 0
          } else {
            this.$message.error('API ERROR @' + res.msg)
            console.log(res)
          }
        }
      })
    },
    validate () {
      this.info.priceSkuDetailInfos.forEach((item, index) => {
        item.sequenceNo = index
      })
      let valid = true
      for (let i = 0; i < this.info.priceSkuDetailInfos.length; i++) {
        const sku = this.info.priceSkuDetailInfos[i]
        if (!sku.zkhPrice) {
          valid = false
          this.$message.error('请输入商品的ZKH单价')
          break
        }
      }
      return valid
    },
    opSave () {
      if (!this.validate()) return
      this.$refs.form.validate((valid) => {
        if (!valid) return
        apiProduct({
          url: '/price/center',
          method: 'POST',
          data: this.info,
          complete: res => {
            if (res.code === 200) {
              this.$message.success('请求成功')
              this.$closeTag(this.$route.path)
              this.$nextTick(() => {
                const id = res.data.priceCustomerInfo.id
                this.$router.push({
                  path: `/insteadOrder/priceCenter/detail/${id}`,
                  query: { type: 'info' }
                })
              })
            } else {
              this.$message.error('API ERROR @' + res.msg)
            }
          }
        })
      })
    },
    opUpdate () {
      if (!this.validate()) return
      apiProduct({
        url: `/price/center/${this.id}`,
        method: 'PUT',
        data: this.info,
        complete: res => {
          if (res.code === 200) {
            this.$message.success('请求成功')
            this.mode = 'info'
            this.getInfo(this.id)
          } else {
            this.$message.error('API ERROR @' + res.msg)
          }
        }
      })
    },

    addSku () {
      const { salesOrganization, customerNo } = this.info.priceCustomerInfo
      if (!salesOrganization) {
        this.$message.error('请选择销售组织')
        return
      }
      const query = {
        salesOrganization,
        customerNo
      }

      query.sku = this.formProduct.sku.skuNo
      if (!this.isTypeSku) {
        query.customerMaterialId = this.formProduct.sku.customerSkuNo
      }

      apiProduct({
        url: '/price/center/detail/sku/customer/info',
        query,
        complete: res => {
          if (res.code === 200) {
            console.log(res)
            const isDuplicateds = this.info.priceSkuDetailInfos.filter(item => item.sku === this.formProduct.sku.skuNo)
            const isDuplicatedIds = isDuplicateds.map(item => item.customerMaterialId)
            if (isDuplicatedIds.length) {
              let hasNew = false
              res.data.forEach(item => {
                if (!~isDuplicatedIds.indexOf(item.customerMaterialId)) {
                  // const price = isDuplicateds[0].zkhPrice
                  // item.zkhPrice = price
                  // item.customerMaterialPrice = price * item.zkhMaterialCount / item.customerMaterialCount
                  this.info.priceSkuDetailInfos.unshift(item)
                  hasNew = true
                }
              })
              if (!hasNew) return this.$message.error('该商品已存在')
            } else {
              this.info.priceSkuDetailInfos.unshift(...res.data)
            }
          } else {
            this.$message.error('API：获取失败@' + res.msg)
          }
        }
      })
    },

    // sub-clients control
    changeSubCustomer (val, data) {
      if (!data) return
      const isSelf = data.customerNumber === this.info.priceCustomerInfo.customerNo
      const isDuplicated = this.info.subPriceCustomerInfos.find(item => item.customerNo === data.customerNumber)
      if (isSelf || isDuplicated) {
        return this.$message.error('该客户已存在')
      }
      this.info.subPriceCustomerInfos.unshift({
        customerId: data.customerId,
        customerNo: data.customerNumber,
        customerName: data.customerName
      })
    },
    removeSubClient (scope) {
      this.info.subPriceCustomerInfos.splice(scope.$index, 1)
    },

    // filter
    changeCustomer (val, data) {
      console.log(val, data)
      this.info.priceCustomerInfo.customerName = data ? data.customerName : ''
      this.info.priceCustomerInfo.distributionChannel = ''
      this.info.priceCustomerInfo.salesOrganization = ''

      this.option.distributionChannel = data && data.saleMap && Object.keys(data.saleMap).map(key => {
        return {
          key,
          value: data.saleMap[key]
        }
      })
      this.option.distributionChannel = this.option.distributionChannel || []

      this.option.saleOrg = data && data.saleOrgList && data.saleOrgList.map(item => {
        const { salesOrganization, productGroup, distributionChannel, salesOrganizationName, distributionChannelName, productGroupName } = item
        return {
          distributionChannel,
          salesOrganizationName,
          key: salesOrganization,
          value: `${salesOrganization}/${distributionChannel}/${productGroup} ${salesOrganizationName} ${distributionChannelName} ${productGroupName}`
        }
      })
      this.option.saleOrg = this.option.saleOrg || []
    },
    changeDistributionChannel (val) {
      this.info.priceCustomerInfo.salesOrganization = ''
      const target = this.option.distributionChannel.find(item => item.key === val)
      this.info.priceCustomerInfo.distributionChannelName = target.value
    },
    changeSaleOrg (val) {
      this.getTaxStatus()
      const target = this.CompuSalesOrg.find(item => item.key === val)
      this.info.priceCustomerInfo.salesOrganizationName = target.salesOrganizationName
    },

    getTaxStatus () {
      const { customerNo, salesOrganization, distributionChannel } = this.info.priceCustomerInfo
      apiProduct({
        url: '/price/center/tax',
        query: {
          customerNo,
          salesOrganization,
          distributionChannel
        },
        complete: res => {
          if (res.code === 200) {
            this.info.priceCustomerInfo.taxStatus = res.data
            this.info.priceCustomerInfo.taxStatusName = res.data ? '含税' : '未税'
          } else {
            //
          }
          console.log(res)
        }
      })
    },

    changeFormProductType () {
      this.option.skuList.options = []
      this.formProduct.sku = ''
    },

    // 商品列表
    changeSkuPrice (row, type) {
      const { zkhMaterialCount: zkh, customerMaterialCount: cusomter } = row
      if (!zkh || !cusomter) return
      if (type === 'zkh') {
        row.customerMaterialPrice = row.zkhPrice * zkh / cusomter

        // sync all same sku
        // const maps = this.info.priceSkuDetailInfos.filter(item => item.sku === row.sku)
        // maps.forEach(item => {
        //   item.zkhPrice = row.zkhPrice
        //   item.customerMaterialPrice = row.zkhPrice * item.zkhMaterialCount / item.customerMaterialCount
        // })
      } else {
        const price = row.customerMaterialPrice * cusomter / zkh
        row.zkhPrice = price
        // sync all same sku
        // const maps = this.info.priceSkuDetailInfos.filter(item => item.sku === row.sku)
        // maps.forEach(item => {
        //   item.zkhPrice = price
        //   item.customerMaterialPrice = price * item.zkhMaterialCount / item.customerMaterialCount
        // })
      }
    },
    removeSku (scope) {
      console.log(scope)
      this.info.priceSkuDetailInfos.splice(scope.$index, 1)
    },
    searchSkuList (search) {
      this.option.skuList.loading = true
      const searchFn = this.isTypeSku ? 'searchSkuList' : 'searchMaterial'
      let params = search

      if (!this.isTypeSku) {
        params = {
          customerNo: this.info.priceCustomerInfo.customerNo,
          customerSkuNo: search,
          current: 1,
          size: 20
        }
      }
      createOrder[searchFn](params).then(res => {
        if (res.code === 200) {
          if (res.data && res.data.length > 0) {
            this.option.skuList = {
              options: [
                this.isTypeSku ? {
                  skuNo: '商品编号',
                  materialDescribe: '商品描述'
                } : {
                  customerSkuNo: '客户物料号',
                  skuNo: '商品编号',
                  skuName: '商品描述'
                },
                ...res.data
              ]
            }
          }
        } else {
          this.$message.error({
            message: res.msg
          })
        }
        // this.skuList.loading = false
      })
    },
    handleSizeChange (res) {
      this.filter.pageSize = res
      this.search()
    },
    handleCurrentChange (res) {
      this.filter.current = res
      this.search()
    }
  },
  mounted () {
    this.id = this.$route.params.id
    this.type = this.$route.query.type
    if (this.type === 'create') {
      this.mode = 'create'
      //
    }
    if (this.type === 'info') {
      this.mode = 'info'
      this.getInfo(this.id)
    }
  }
}
</script>

<style lang="less">
.page-price-center-detail {
  .pane-op {
    text-align: center;
  }
  .pane-title {
    padding: 10px;
    margin-bottom: 20px;
    background-color: #f4f7fc;
    color:#5098ff;
    font-weight: bold;
  }
  .pane {
    margin-bottom: 30px;
  }
  .pane-clients {
    .el-form {
      display: flex;
      flex-flow: wrap;
      justify-content: space-between;
    }
    .el-form-item {
      margin-right: 0;
    }
    .el-form-item__label {
      width: 100px;
    }
  }
  .detail-info {
    ul {
      display: flex;
      flex-wrap: wrap;
    }
    li {
      min-width: 33%;
      margin-bottom: 20px;
      height: 30px;
      line-height: 30px;
      span{
        &.title {
          display: inline-block;
          width: 90px;
          text-align: right;
          margin-right: 30px;
        }
        &:last-child {
          font-weight: bold;
        }
      }
    }
  }
  .module-filter {
    display: flex;
    justify-content: space-between;
    .btn-upload {
      display: inline-block;
      margin: 0 10px 0 0;
    }
  }
}
.opt-item {
  display: flex;
  span {
    padding-right: 10px;
  }
}
</style>
