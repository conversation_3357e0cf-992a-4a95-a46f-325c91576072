<!--
 * @Author: luozhikai
 * @Date: 2023-07-14 10:38:54
 * @LastEditors: luozhikai
 * @LastEditTime: 2023-07-26 19:01:16
 * @Description: file content
-->
<template>
  <div class="page page-home financial-management">
    <iframe ref="iframe" :height="iframe.height" width="100%" scrolling="auto" :src="iframeSrc" frameborder="0"></iframe>
  </div>
</template>
<script>
import { getCookie } from '@/utils/index'
export default {
  name: 'receivePayment',
  data() {
    return {
      container: '',
      iframe: {
        height: 0,
        width: 0
      },
      bridge: 'IframeBridge',
      iframePath: 'IframeBridge'
    }
  },
  created() {
    window.onresize = this.throttle(this.resize, 800)
    console.log(this.$route);
  },
  mounted() {
    this.container = this.$refs.iframe.parentNode
    this.resize()
    this.initPostIframeMessage()
    this.initMessageListener()
  },
  beforeRouteLeave(to, from, next) {
    this.iframePath = this.bridge
    next()
  },
  computed: {
    iframeSrc() {
      const index = location.href.indexOf('?')
      const paramsString = index > -1 ? `?${location.href.substring(index + 1)}` : ''
      let ret = `https://boss.zkh360.com/sr/maintainment${paramsString}`
      if (/boss-uat/.test(location.href)) {
        ret = `https://boss-uat.zkh360.com/sr/maintainment${paramsString}`
      }
      if (/fetest|localhost|local/.test(location.href)) {
        ret = `http://local.zkh360.com:5173/sr/maintainment${paramsString}`
      }
      return ret
    }
  },
  methods: {
    initMessageListener() {
      const that = this
      function receiveMsgFromChild(event) {
        try {
          const { id, source, type } = event.data
          if (source === 'sr') {
            if (id) that.$router.push(`/insteadOrder/maintainmentV3/detail/${id}`)
            else if (type === 'download') that.$router.push('/purchaseReport/downLoadList')
          }
          // console.log(event);
        } catch (err) { console.log(err) }
      }
      window.addEventListener('message', receiveMsgFromChild, false)
    },
    getAccessToken() {
      let proToken = 'zkh_access_token'
      let token
      try {
        token = document.cookie.match(/([a-z_]*?zkh_access_token)/g)
        const uatToken = token.filter(r => ~r.indexOf('uat'))[0]
        const localToken = token.filter(r => ~r.indexOf('local'))[0]
        if (/fetest|local|uat/.test(location.href)) {
          token = uatToken || localToken
        } else {
          token = proToken
        }
      } catch (err) { console.log(err) }
      return token
    },
    initPostIframeMessage() {
      try {
        const username = window.CUR_DATA.user.name
        const prefix = this.getAccessToken()
        const token = getCookie(prefix)
        console.log('%c username, prefix, token',
          'background-color:red;color:white;font-size:2em;', username, prefix, token)
        this.$refs.iframe.onload = () => {
          this.$refs.iframe.contentWindow.postMessage({
            username, token, source: 'boss'
          }, '*')
        }
      } catch (err) { console.log(err) }
    },
    resize() {
      try {
        let rect = this.container && this.container.getBoundingClientRect()
        this.iframe.width = rect.width - 40
        this.iframe.height = window.innerHeight - 40 - 50
      } catch (err) {
        console.log(err)
      }
    },
    debounce(fn, delay = 800) {
      let timer
      return function () {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    },
    throttle(fn, delay = 800) {
      let timer
      return function () {
        if (timer) return
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    }
  }
}
</script>
<style>
.financial-management {
  padding: 0px;
  overflow: hidden;
  position: relative;
}
</style>
