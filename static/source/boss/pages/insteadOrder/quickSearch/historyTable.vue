<template>
  <div>
    <el-tag>历史订单 {{ '(' + total + ')' }}</el-tag>
    <el-table
      v-loading="listLoading"
      style="border:1px solid #ebebeb"
      fit
      highlight-current-row
      height="250"
      @sort-change="sortHistory"
      :data="list"
      show-overflow-tooltip
      :header-cell-style="{
        padding: 0
      }"
    >
      <el-table-column
        label="序号"
        type="index"
        :index="getRealIndex"
        min-width="100px"
        align="center"
      />
      <el-table-column
        label="订单编号"
        sortable="custom"
        min-width="100px"
        align="center"
        prop="sap_order_no"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <zkh-link :key="row.sap_order_no" :targetUrl="row.sap_order_no | getSOdetailUrl">{{
            row.sap_order_no
          }}</zkh-link>
        </template>
      </el-table-column>

      <el-table-column
        label="订单日期"
        sortable="custom"
        min-width="140px"
        align="center"
        prop="sap_add_date"
        show-overflow-tooltip
      />
      <el-table-column
        label="最近交货日"
        sortable="custom"
        min-width="140px"
        align="center"
        prop="last_dn_time"
        show-overflow-tooltip
      />
      <el-table-column
        label="含税单价"
        sortable="custom"
        min-width="100px"
        align="center"
        prop="tax_price"
        show-overflow-tooltip
      />
      <el-table-column
          label="未税单价"
          sortable="custom"
          min-width="100px"
          align="center"
          prop="untaxed_unit_price"
          show-overflow-tooltip
      />
      <el-table-column
        label="数量"
        sortable="custom"
        min-width="80px"
        align="center"
        prop="order_quantity"
        show-overflow-tooltip
      />
      <el-table-column
        label="单位"
        sortable="custom"
        min-width="80px"
        align="center"
        prop="quantity_unit"
        show-overflow-tooltip
      />
      <el-table-column
        label="含税金额"
        sortable="custom"
        min-width="100px"
        align="center"
        prop="tax_total_price"
        show-overflow-tooltip
      />
      <el-table-column
        label="客户物料号"
        sortable="custom"
        min-width="120px"
        align="center"
        prop="customer_material_no"
        show-overflow-tooltip
      />
      <el-table-column
        label="客户物料名称"
        sortable="custom"
        min-width="120px"
        align="center"
        prop="customer_material_name"
        show-overflow-tooltip
      />
      <el-table-column
        label="客户物料规格"
        sortable="custom"
        min-width="120px"
        align="center"
        prop="customer_specification_model"
        show-overflow-tooltip
      />
    </el-table>

    <pagination
      style="margin:0;padding:10px 0"
      small
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="otherQueryParam.current"
      :limit.sync="otherQueryParam.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { goodsOrderListSearchBySku } from '@/api/insteadOrder.js'
import { buildSoDetailLink } from '@/utils/index.js'
export default {
  props: {
    searchParam: {
      type: Object,
      default: null
    },
    skuNo: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      total: 0,
      listLoading: false,
      list: [],
      otherQueryParam: {
        current: 1,
        pageSize: 5,
        sortFieldName: '', // 排序字段名称
        sortType: 1 // 排序方式 1:正序 -1:倒序
      }
    }
  },
  watch: {
    skuNo (newValue, oldValue) {
      if (newValue) {
        this.initData()
      }
    }
  },
  filters: {
    getSOdetailUrl (val) {
      return buildSoDetailLink({
        query: {
          sapOrderNo: val
        }
      })
    }
  },
  components: {
    Pagination
  },
  created () {
    this.initData()
  },
  methods: {
    getRealIndex (index) {
      return (
        index +
        1 +
        (this.otherQueryParam.current - 1) * this.otherQueryParam.pageSize
      )
    },
    initData () {
      this.otherQueryParam = {
        current: 1,
        pageSize: 5,
        sortFieldName: '', // 排序字段名称
        sortType: 1 // 排序方式 1:正序 -1:倒序
      }
      this.getList()
    },
    sortHistory ({ column, prop, order }) {
      this.otherQueryParam.current = 1
      this.otherQueryParam.pageSize = 5
      if (order) {
        this.otherQueryParam.sortFieldName = prop
        this.otherQueryParam.sortType = order === 'ascending' ? 1 : -1
      } else {
        this.otherQueryParam.sortFieldName = ''
        this.otherQueryParam.sortType = 1
      }
      this.getList()
    },
    getList () {
      let param = { ...this.searchParam }
      param.skuNo = this.skuNo
      param.current = this.otherQueryParam.current
      param.pageSize = this.otherQueryParam.pageSize
      param.sortFieldName = this.otherQueryParam.sortFieldName
      param.sortType = this.otherQueryParam.sortType
      goodsOrderListSearchBySku(param).then(res => {
        this.list = []
        if (res.code === 200) {
          this.total = res.totalCount
          const contents = res.data.contents || []
          this.list = contents.map(item => {
            const result = Object.assign(
              { ...item },
              {
                sap_order_no: item.orderNo,
                sap_add_date: item.orderDateTime,
                last_dn_time: item.lastDnTime,
                tax_price: item.taxPrice,
                untaxed_unit_price: item.untaxedUnitPrice,
                order_quantity: item.quantity,
                quantity_unit: item.unit,
                tax_total_price: item.totalTaxPrice,
                customer_material_no: item.customerMaterialNo,
                customer_material_name: item.customerMaterialName,
                customer_specification_model: item.customerMaterialType
              }
            )
            return result
          })
        }
      })
    }
  }
}
</script>

<style></style>
