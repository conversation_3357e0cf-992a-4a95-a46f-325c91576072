<template>
  <div>
    <el-tag>报价记录 {{ '(' + total + ')' }}</el-tag>
    <el-table
      v-loading="listLoading"
      style="border:1px solid #ebebeb"
      fit
      highlight-current-row
      height="250"
      @sort-change="sortQuote"
      :data="list"
      show-overflow-tooltip
      :header-cell-style="{
        padding: 0
      }"
    >
      <el-table-column
        label="序号"
        type="index"
        :index="getRealIndex"
        min-width="100px"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        label="报价单编号"
        sortable="custom"
        min-width="120px"
        align="center"
        prop="quotedetailid"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          <zkh-link :targetUrl="row.quoteDetailUrl">{{
            row.quotedetailid
          }}</zkh-link>
        </template>
      </el-table-column>
      <el-table-column
        label="报价日期"
        sortable="custom"
        min-width="100px"
        align="center"
        prop="modifiedon"
        show-overflow-tooltip
      />
      <el-table-column
        label="含税单价"
        sortable="custom"
        min-width="100px"
        align="center"
        prop="priceperunit"
        show-overflow-tooltip
      />
      <el-table-column
        label="数量"
        sortable="custom"
        min-width="80px"
        align="center"
        prop="quote_quantity"
        show-overflow-tooltip
      />
      <el-table-column
        label="单位"
        sortable="custom"
        min-width="80px"
        align="center"
        prop="uomname"
        show-overflow-tooltip
      />
      <el-table-column
        label="含税金额"
        sortable="custom"
        min-width="100px"
        align="center"
        prop="amount"
        show-overflow-tooltip
      />
      <el-table-column
        label="客户物料号"
        sortable="custom"
        min-width="120px"
        align="center"
        prop="customersku"
        show-overflow-tooltip
      />
      <el-table-column
        label="客户物料名称"
        sortable="custom"
        min-width="120px"
        align="center"
        prop="custskuname"
        show-overflow-tooltip
      />
      <el-table-column
        label="客户物料规格"
        sortable="custom"
        min-width="120px"
        align="center"
        prop="reqmpromodels"
        show-overflow-tooltip
      />
    </el-table>

    <pagination
      small
      style="margin:0;padding:10px 0"
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="otherQueryParam.current"
      :limit.sync="otherQueryParam.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import { goodsQuoteListSearchBySku } from '@/api/insteadOrder.js'
export default {
  props: {
    searchParam: {
      type: Object,
      default: null
    },
    skuNo: {
      type: [String, Number],
      default: ''
    }
  },
  data () {
    return {
      total: 0,
      listLoading: false,
      list: [],
      otherQueryParam: {
        current: 1,
        pageSize: 5,
        sortFieldName: '', // 排序字段名称
        sortType: 1 // 排序方式 1:正序 -1:倒序
      }
    }
  },
  watch: {
    skuNo (newValue, oldValue) {
      if (newValue) {
        this.initData()
      }
    }
  },
  created () {
    this.initData()
  },
  components: {
    Pagination
  },
  methods: {
    getRealIndex (index) {
      return (
        index +
        1 +
        (this.otherQueryParam.current - 1) * this.otherQueryParam.pageSize
      )
    },
    initData () {
      this.otherQueryParam = {
        current: 1,
        pageSize: 5,
        sortFieldName: '', // 排序字段名称
        sortType: 1 // 排序方式 1:正序 -1:倒序
      }
      this.getList()
    },
    sortQuote ({ column, prop, order }) {
      this.otherQueryParam.current = 1
      this.otherQueryParam.pageSize = 5
      if (order) {
        this.otherQueryParam.sortFieldName = prop
        this.otherQueryParam.sortType = order === 'ascending' ? 1 : -1
      } else {
        this.otherQueryParam.sortFieldName = ''
        this.otherQueryParam.sortType = 1
      }
      this.getList()
    },
    getList () {
      let param = { ...this.searchParam }
      param.skuNo = this.skuNo
      param.current = this.otherQueryParam.current
      param.pageSize = this.otherQueryParam.pageSize
      param.sortFieldName = this.otherQueryParam.sortFieldName
      param.sortType = this.otherQueryParam.sortType
      goodsQuoteListSearchBySku(param).then(res => {
        this.list = []
        if (res.code === 200) {
          this.total = res.totalCount
          const contents = res.data.contents || []
          this.list = contents.map(item => {
            const result = Object.assign(
              { ...item },
              {
                quotedetailid: item.quoteNo,
                priceperunit: item.taxPrice,
                modifiedon: item.quoteDate,
                quote_quantity: item.quantity,
                uomname: item.unit,
                customersku: item.customerMaterialNo,
                reqmpromodels: item.customerMaterialType,
                custskuname: item.customerMaterialName,
                amount: item.totalTaxPrice
              }
            )
            return result
          })
        }
      })
    }
  }
}
</script>

<style></style>
