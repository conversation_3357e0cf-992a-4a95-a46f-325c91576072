<template>
  <div class="app-container quick-search-container">
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
        label-position="right"
      >
        <el-row>
          <el-col :span="7">
            <el-form-item label="客户名称：" prop="customerNo">
              <el-select
                :value="customerValue"
                @change="changeCustomer"
                filterable
                clearable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteCustomerMethod"
                :loading="customerNoLoading"
              >
                <el-option
                  v-for="item in customerNoOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="客户物料号：" prop="customerMaterialNumber">
              <el-input
                v-model="searchForm.customerMaterialNumber"
                placeholder="多个可用空格隔开"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="SKU编号：" prop="skuNo">
              <el-input
                v-model="searchForm.skuNo"
                placeholder="多个可用空格隔开"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <!-- <el-form-item> -->
            <el-button
              type="primary"
              icon="el-icon-search"
              :loading="productLoading"
              @click="handleFilter"
              >查询</el-button
            >
            <!-- </el-form-item> -->
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="含税交易价格：" prop="taxStartPrice">
              <el-col :span="11">
                <el-form-item prop="taxStartPrice">
                  <el-input
                    v-model.trim="searchForm.taxStartPrice"
                    placeholder="起"
                    clearable
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="taxEndPrice">
                  <el-input
                    v-model.trim="searchForm.taxEndPrice"
                    placeholder="止"
                    clearable
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="交易数量：">
              <el-col :span="11">
                <el-form-item prop="startQuantity">
                  <el-input
                    v-model.trim.number="searchForm.startQuantity"
                    placeholder="起"
                    clearable
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="endQuantity">
                  <el-input
                    v-model.trim.number="searchForm.endQuantity"
                    placeholder="止"
                    clearable
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="品牌：" prop="brandId">
              <el-select
                clearable
                v-model="searchForm.brandId"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteBrandIdMethod"
                :loading="brandIdLoading"
              >
                <el-option
                  v-for="item in brandIdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;">
            <el-button icon="el-icon-search" @click="handleReset"
              >重置</el-button
            >
            <el-button
              type="primary"
              circle
              :icon="caretIcon"
              @click="searchSonditionOpen = !searchSonditionOpen"
            />
          </el-col>
        </el-row>

        <el-row v-show="searchSonditionOpen">
          <el-col :span="7">
            <el-form-item label="客户物料描述：" prop="customerDesc">
              <el-input
                v-model.trim="searchForm.customerDesc"
                placeholder="可输入客户物料名称或规格"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="物料描述：" prop="materialDesc">
              <el-input
                v-model.trim="searchForm.materialDesc"
                placeholder="可按名称、规格、箱规筛选"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item label="物料组：" prop="productGroupId">
              <el-select
                clearable
                v-model="searchForm.productGroupId"
                filterable
                remote
                placeholder="请输入关键词"
                :remote-method="remoteProductGroupIdMethod"
                :loading="productGroupIdLoading"
              >
                <el-option
                  v-for="item in productGroupIdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="3" style="padding-left:10px;" />
        </el-row>
        <el-row v-show="searchSonditionOpen">
          <el-col :span="11">
            <el-form-item label="订单日期：">
              <el-col :span="11">
                <el-form-item prop="orderStartDate">
                  <el-date-picker
                    v-model="searchForm.orderStartDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="orderEndDate">
                  <el-date-picker
                    v-model="searchForm.orderEndDate"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :offset="1" :span="11">
            <el-form-item label="报价日期：">
              <el-col :span="11">
                <el-form-item prop="quoteStartDate">
                  <el-date-picker
                    v-model="searchForm.quoteStartDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="起"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-form-item prop="quoteEndDate">
                  <el-date-picker
                    v-model="searchForm.quoteEndDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="止"
                    style="width: 100%;"
                  />
                </el-form-item>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col :span="7"></el-col>
          <el-col :span="3" style="padding-left:10px;" />
        </el-row>
      </el-form>
    </div>
    <div v-if="!productLoading" class="customer-tips">
      为您查询出
      <span>{{ total || 0 }}</span>
      笔满足条件的商品
    </div>
    <div v-if="productLoading" class="customer-tips">查询中</div>
    <div class="sku-container" v-for="skuItem in skuList" :key="skuItem.skuNo">
      <el-row class="sku-base-info" type="flex" align="middle">
        <el-col :span="2">
          <el-link type="primary" @click="copySku(skuItem.skuNo, $event)">{{
            skuItem.skuNo
          }}</el-link>
        </el-col>
        <el-col :span="1">
          <el-tooltip content="复制官网链接">
            <el-button
              v-if="
                isGanWang(skuItem.webSiteType) &&
                  isShangjia(skuItem.webSiteType) &&
                  skuItem.goodWebSiteUrl
              "
              style="margin-righ:5px;"
              icon="el-icon-share"
              circle
              @click="copyLink(skuItem.goodWebSiteUrl, $event)"
            />
          </el-tooltip>
          <el-tooltip v-if="skuItem.goodCrmProductUrl" content="SKU详情">
            <zkh-link :targetUrl="skuItem.goodCrmProductUrl">
              <el-button icon="el-icon-document" circle />
            </zkh-link>
          </el-tooltip>
        </el-col>
        <el-col :span="5">{{ skuItem.materialDesc }}</el-col>
        <el-col :span="5">
          <p>
            <span>{{ '销售建议价' }}</span>
            <span class="emphasis-money">
              {{ priceFormat(skuItem.salesSuggestPrice, skuItem.unit) }}
            </span>
          </p>
          <span>{{ '未税单价' }}</span>
          <span class="emphasis-money">
            {{ priceFormat(skuItem.untaxedPrice, skuItem.unit) }}
          </span>
        </el-col>
        <el-col :span="4">
          <p>
            <span>{{ '千客千价' }}</span>
            <span class="emphasis-money">
              {{ priceFormat(skuItem.batchPrice, skuItem.unit) }}
            </span>
          </p>
          <span>{{ '箱规：' + skuItem.bossPackage || '' }}</span>
        </el-col>
        <el-col :span="4">
          <div>{{ '品牌：' + skuItem.brandName || '' }}</div>
          <div>{{ '型号：' + skuItem.skuModel || '' }}</div>
        </el-col>
        <el-col :span="3">
          <div>{{ 'MOQ：' + skuItem.moq || '' }}</div>
          <div>{{ 'LT：' + skuItem.leadTime || '' }}</div>
        </el-col>
        <el-col :span="4">
          <div>{{ '制造商货号：' + skuItem.factoryArtNo || '' }}</div>
          <div>{{ '采购员：' + skuItem.byeName || '' }}</div>
        </el-col>
      </el-row>
      <div class="tag-container">
        <i
          v-if="skuItem.skuType"
          class="left-angle-tag"
          style="background-color:rgb(72,169,31)"
        >
          <span>启用</span>
        </i>
        <i
          v-if="!skuItem.skuType"
          class="left-angle-tag"
          style="background-color:rgb(209,91,71)"
        >
          <span>停用</span>
        </i>
        <i
          v-if="
            isGanWang(skuItem.webSiteType) && !isShangjia(skuItem.webSiteType)
          "
          class="left-angle-tag"
          style="background-color:rgb(214,72,126)"
        >
          <span>官网-下架中</span>
        </i>
        <i
          v-if="
            isGanWang(skuItem.webSiteType) && isShangjia(skuItem.webSiteType)
          "
          class="left-angle-tag"
          style="background-color:rgb(214,72,126)"
        >
          <span>官网</span>
        </i>
        <i
          v-if="!isGanWang(skuItem.webSiteType)"
          class="left-angle-tag"
          style="background-color:rgb(136,136,136)"
        >
          <span>非官网</span>
        </i>
        <i
          v-if="skuItem.regionSale"
          class="left-angle-tag"
          style="background-color:rgb(64,158,255)"
        >
          <span>区域售卖</span>
        </i>
        <i
          v-if="skuItem.soldOut"
          class="left-angle-tag"
          style="background-color:rgb(58,135,173)"
        >
          <span>售完即止</span>
        </i>
        <i v-if="skuItem.evm" class="left-angle-tag" style="background-color: rgb(241, 119, 15)">
          <span>EVM</span>
        </i>
        <i v-if="skuItem.vpi" class="left-angle-tag" style="background-color: rgb(241, 15, 15)">
          <span>VPI</span>
        </i>
      </div>
      <div class="table-container">
        <el-row>
          <el-col :span="12" style="padding:0 5px 0 10px">
            <historyTable :searchParam="currentParam" :skuNo="skuItem.skuNo" />
          </el-col>
          <el-col :span="12" style="padding:0 5px 0 10px">
            <quoteRecordTable
              :searchParam="currentParam"
              :skuNo="skuItem.skuNo"
            />
          </el-col>
        </el-row>
      </div>
    </div>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="skuPageInfo.current"
      :limit.sync="skuPageInfo.pageSize"
      layout="total, prev, pager, next, jumper"
      @pagination="getSkuList"
    />
  </div>
</template>

<script>
import historyTable from './historyTable.vue'
import quoteRecordTable from './quoteRecordTable.vue'
import Pagination from '@/components/Pagination'
import clip from '@/utils/clipboard' // use clipboard directly
// import { dateFormat } from '@/filters/index.js'
import {
  goodsBrandListSearch,
  goodsCustomerListSearch,
  goodsGroupListSearch,
  goodsProductListSearch
} from '@/api/insteadOrder.js'

// 页面搜索条件初始化
const getInitSearchParams = function () {
  // const nowTime = new Date()
  // const today = dateFormat('yyyy-MM-dd', nowTime)
  // const start = dateFormat(
  //   'yyyy-MM-dd',
  //   new Date(nowTime.setMonth(nowTime.getMonth() - 3))
  // )

  return {
    customerNo: '', // 客户编号,客户号编号不能为空
    customerId: '', // 客户id,客户号id不能为空
    customerMaterialNumber: '', // 客户物料号
    skuNo: '', // sku编号,sku编号不可为空
    taxStartPrice: '', // taxStartPrice
    taxEndPrice: '', // 含税终止交易价格
    startQuantity: '', // 起止交易数量
    endQuantity: '', // 终止交易数量
    brandId: '', // 品牌id
    customerDesc: '', // 客户物料描述
    materialDesc: '', // 物料描述
    productGroupId: '', // 物料组id
    orderStartDate: '', // 订单开始日期 默认最近三个月
    orderEndDate: '', // 订单结束日期
    quoteStartDate: '', // 报价开始日期 默认最近三个月
    quoteEndDate: '' // 报价结束日期
  }
}
const needNumberType = (rule, value, callback) => {
  setTimeout(() => {
    if (isNaN(value)) {
      callback(new Error('请输入数字值'))
    } else {
      callback()
    }
  }, 100)
}
export default {
  name: 'QuickSearch',
  data () {
    return {
      customerNoLoading: false,
      brandIdLoading: false,
      productGroupIdLoading: false,
      productLoading: false,
      customerNoOptions: [],
      brandIdOptions: [],
      productGroupIdOptions: [],
      searchForm: {}, // 绑定搜索参数
      currentParam: {}, // 搜索中的参数
      skuPageInfo: {
        current: 1,
        pageSize: 10
      },
      rules: {
        customerNo: [
          {
            required: true,
            message: '请输入客户名称',
            trigger: 'change'
          }
        ],
        taxStartPrice: [
          {
            validator: needNumberType,
            trigger: 'blur'
          }
        ],
        taxEndPrice: [
          {
            validator: needNumberType,
            trigger: 'blur'
          }
        ],
        startQuantity: [
          {
            validator: needNumberType,
            trigger: 'blur'
          }
        ],
        endQuantity: [
          {
            validator: needNumberType,
            trigger: 'blur'
          }
        ]
      },
      skuList: [],
      total: 0,
      searchSonditionOpen: false
    }
  },
  components: {
    Pagination,
    historyTable,
    quoteRecordTable
  },
  created () {
    this.searchForm = getInitSearchParams()
  },
  computed: {
    caretIcon () {
      if (this.searchSonditionOpen) {
        return 'el-icon-caret-top'
      } else {
        return 'el-icon-caret-bottom'
      }
    },
    customerValue () {
      if (this.searchForm.customerNo && this.searchForm.customerId) {
        return this.searchForm.customerNo + '-' + this.searchForm.customerId
      } else {
        return ''
      }
    }
  },
  methods: {
    isGanWang (webSiteType) {
      return (webSiteType || '').indexOf(',1') >= 0
    },
    isShangjia (webSiteType) {
      return (webSiteType || '').indexOf('1,') < 0
    },
    priceFormat (val, unit) {
      if (val === undefined || val === null) {
        return '----'
      } else {
        return '￥' + val + (unit ? '/' + unit : '')
      }
    },
    changeCustomer (value) {
      this.searchForm.customerNo = value.split('-')[0]
      this.searchForm.customerId = value.split('-')[1]
    },
    querySearchAsync (queryString, cb) {},
    getSkuList () {
      this.$refs['searchForm'].validate(valid => {
        if (valid) {
          this.productLoading = true

          let param = { ...this.searchForm }
          // 子表需要一个稳定的当前查询参数
          this.currentParam = { ...this.searchForm }
          this.currentParam.customerMaterialNumber = this.currentParam
            .customerMaterialNumber
            ? this.currentParam.customerMaterialNumber.trim()
            : ''
          this.currentParam.skuNo = this.currentParam.skuNo
            ? this.currentParam.skuNo.trim()
            : ''

          param.current = this.skuPageInfo.current
          param.pageSize = this.skuPageInfo.pageSize
          this.skuList = []
          goodsProductListSearch(param).then(res => {
            this.productLoading = false
            if (res.code === 200) {
              this.total = res.totalCount
              if (res.data && res.data.contents) {
                this.skuList = res.data.contents
              }
            } else {
              this.$message.error(res.msg)
            }
          })
        }
      })
    },
    // 查询按钮
    handleFilter () {
      this.skuPageInfo.current = 1
      this.getSkuList()
    },
    // 重置按钮
    handleReset () {
      this.$refs['searchForm'].resetFields()
    },
    // 远程查找客户
    remoteCustomerMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.customerNoLoading = true
        goodsCustomerListSearch({
          customer_name: key,
          pageSize: 100
        }).then(res => {
          this.customerNoLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.customerNoOptions = res.data.contents.map(item => {
                return {
                  value: item.customerNo + '-' + item.customerId,
                  label: item.customerName
                }
              })
            } else {
              this.customerNoOptions = []
            }
          } else {
            this.customerNoOptions = []
          }
        })
      } else {
        this.customerNoOptions = []
      }
    },
    // 远程查找品牌
    remoteBrandIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.brandIdLoading = true
        goodsBrandListSearch({
          brand_name: key
        }).then(res => {
          this.brandIdLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.brandIdOptions = res.data.contents.map(item => {
                return {
                  value: item.brandId,
                  label: item.brandName
                }
              })
            } else {
              this.brandIdOptions = []
            }
          } else {
            this.brandIdOptions = []
          }
        })
      } else {
        this.brandIdOptions = []
      }
    },
    // 远程查找物料组
    remoteProductGroupIdMethod (query) {
      const key = query.trim()
      if (key !== '') {
        this.productGroupIdLoading = true
        goodsGroupListSearch({
          group_name: key
        }).then(res => {
          this.productGroupIdLoading = false
          if (res.code === 200) {
            if (res.data.contents && res.data.contents.length > 0) {
              this.productGroupIdOptions = res.data.contents.map(item => {
                return {
                  value: item.productGroupId,
                  label: item.productGroupName
                }
              })
            } else {
              this.productGroupIdOptions = []
            }
          } else {
            this.productGroupIdOptions = []
          }
        })
      } else {
        this.productGroupIdOptions = []
      }
    },
    copySku (sku, event) {
      clip(sku, event, () => {
        const content = 'SKU编号复制成功'
        this.$message({
          message: content,
          type: 'success'
        })
      })
    },
    copyLink (link, event) {
      clip(link, event, () => {
        const content = '商品官网地址复制成功'
        this.$message({
          message: content,
          type: 'success'
        })
      })
    }
  }
}
</script>

<style lang="scss" scope></style>
<style lang="scss">
.left-angle-tag {
  display: inline-block;
  height: 20px;
  line-height: 21px;
  font-size: 13px;
  font-style: normal;
  background: #000000;
  margin-right: 5px;
  span {
    vertical-align: top;
    display: inline-block;
    margin: 0 5px 0 3px;
    color: #ffffff;
  }
  &::before {
    vertical-align: top;
    // width: 50px;
    display: inline-block;
    content: ' ';
    width: 0;
    height: 0;
    border-width: 10px;
    border-style: solid;
    border-left: 0;
    border-color: #ffffff transparent #ffffff #ffffff;
  }
}
.quick-search-container {
  .filter-container {
    padding-top: 18px;
    background-color: #f4f4f4;
    // .el-form-item {
    //   margin-bottom: 10px;
    // }
  }
  .tag-container {
    padding-left: 10px;
    padding-bottom: 5px;
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .customer-tips {
    background: #ffffff;
    margin-top: 15px;
    margin-bottom: 15px;
    padding-left: 0;
    font-size: 14px;
    span {
      text-align: center;
      color: red;
      font-size: 14px;
    }
  }
  .sku-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin-bottom: 10px;
    .sku-base-info {
      padding: 10px;
      color: #666666;
      .emphasis-money {
        color: rgb(236, 128, 141);
      }
      & > * {
        padding: 0 5px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}
</style>
