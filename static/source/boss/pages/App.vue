<template>
  <boss :class="{ 'menu-collapse': collapseSideMenu }">
    <template v-slot:logo>
      <span class="logo">
        <a href="/">
          <img src="@/assets/images/logo.png" />
          震坤行BOSS平台
        </a>
      </span>
    </template>
    <template v-slot:side-menu>
      <side-menu :menu="menu" :reg="reg" @toggle="toggle"></side-menu>
    </template>
    <template v-slot:extra-nav>
      <el-button
        style="left: 30px"
        type="text"
        size="large"
        class="collapse-icon color-grey-200"
        :icon="collapseIcon"
        @click="toggleSideMenu"
      ></el-button>
      <!-- <div style="margin-right: 40px;">
        <dropdown-task top="48" @link="handleLink" @goToWorkbench="routeToWorkbench" ></dropdown-task>
      </div> -->
      <el-dropdown @command="handleCreate">
        <span class="el-dropdown-link">
          创建工单
          <i class="el-icon-arrow-down el-icon--right"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">
            协同工单
          </el-dropdown-item>
          <el-dropdown-item command="0">
            <div>
              问题工单
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <span style="margin-left: 60px"></span>
      <zkh-link classNames="color-dark-300" :targetUrl="udeskUrl"
        >Udesk系统</zkh-link
      >
      <span style="margin-left: 60px"></span>
      <el-button
        class="color-dark-300"
        style="font-size: 14px"
        type="text"
        @click="showAnnouncement"
        >系统公告</el-button
      >
      <span style="margin-right: 32px"></span>
      <a :href="gongdanUrl"></a>
      <!-- <el-dropdown trigger="click" class="msg-dropdown">
        <el-badge :is-dot="msgTipsList.length > 0" class="item">  <i class="el-icon-info"></i></el-badge>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item class="clearfix" v-for="item in msgTipsList" :key="item.msgRuleNo">
            <div class="show-tip" @click="goToMsg(item.msgRuleNo)">
              <div class="tip-name">{{item.msgRuleTypeName}}</div>
              <div class="tip-count">{{item.count}}</div>
            </div>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown> -->
    </template>
  </boss>
</template>

<script>
import { mapState } from 'vuex'
import Boss from '@client/boss'
import SideMenu from '@/components/SideMenu'
import { gongdanUrl, udeskUrl } from '@/utils/config.js'
import {
  getAnnouncement,
  confirmAnnouncement,
  ifShowAnnouncement
} from '@/api/announcement.js'
import request from '@/utility/request'
// import * as msgApi from '@/api/messageCenter'
// import { createWebsocket } from '@/utils/ws.js'
import qs from 'qs'
import { toggleSideMenu as toggleSideMenuApi, isInWhiteList, openNewTab, safeRun, routeToWorkflow } from '@/utils/index'
import '@boss/web-components'
import Cookies from 'js-cookie';

export default {
  data() {
    return {
      reg: /https?:\/\/boss(-admin)?(-uat)?\.zkh360\.com/gi,
      collapse: false,
      gongdanUrl,
      udeskUrl,
      announcement: '',
      ifShow: false,
      collapseSideMenu: false,
      mainSide: null,
      isRouterAlive: true,
      msgTipsList: [],
      ws: ''
    }
  },
  provide() {
    return {
      reload: this.reload
    }
  },
  components: {
    boss: Boss.App,
    SideMenu
  },
  computed: {
    ...mapState(['menu']),
    collapseIcon() {
      return this.collapseSideMenu ? 'el-icon-s-unfold' : 'el-icon-s-fold'
    }
  },
  methods: {
    handleCreate(command) {
      routeToWorkflow('/wf/create/0', { essence: command })
    },
    reload() {
      this.isRouterAlive = false;
      this.$nextTick(() => {
        this.isRouterAlive = true;
      })
    },
    toggleSideMenu() {
      this.collapseSideMenu = !this.collapseSideMenu
    },
    async checkIfShow() {
      try {
        const response = await ifShowAnnouncement()
        if (response && response.code === 200 && response.data) {
          this.ifShow = response.data
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getLatestAnnouncement() {
      try {
        const response = await getAnnouncement()
        if (response && response.code === 200 && response.data) {
          this.announcement = response.data.publishContent
        }
        console.log(this.announcement)
      } catch (err) {
        console.log(err)
      }
    },
    async initAnnouncement() {
      await this.getLatestAnnouncement()
      await this.checkIfShow()
      if (this.ifShow && this.announcement) {
        this.showAnnouncement()
      }
    },
    async showAnnouncement() {
      await this.getLatestAnnouncement()
      if (!this.announcement) return this.$message.error('暂无公告！')
      this.$alert(`<pre>${this.announcement}</pre>`, '系统公告', {
        confirmButtonText: '确定',
        dangerouslyUseHTMLString: true,
        callback: (action) => {
          confirmAnnouncement().catch((err) => {
            this.$message.error(err.msg || err.message || '操作失败！')
          })
        }
      })
    },
    toggle(flag) {
      this.collapse = flag
    },
    leftMenuHideControl(type) {
      let dom = document.querySelector('.main-side')
      let main = document.querySelector('.main-frame')
      if (type === '1') {
        dom.style.display = 'none'
        main.classList.add('main-side-hidden')
      } else {
        dom.style.display = 'block'
        main.classList.remove('main-side-hidden')
      }
    },
    // 获取消息提示列表
    // getMsgTipsList () {
    //   msgApi.getMsgTipsListApi().then(res => {
    //     if (res.code === 200) {
    //       this.msgTipsList = res.data
    //     } else {
    //       this.msgTipsList = []
    //     }
    //   })
    // },
    // 跳转到消息列表页面
    goToMsg(ruleNo) {
      try {
        this.$closeTag('/messageCenter/centerList');
      } catch { }
      setTimeout(() => {
        this.$router.push({
          path: '/messageCenter/centerList',
          query: {
            ruleNo: ruleNo
          }
        })
      }, 10)
    },
    handlePostMessage(data) {
      const { cmd, params } = data
      switch (cmd) {
        case 'router':
          try {
            this.$closeTag(params.routerName);
          } catch { }
          setTimeout(() => {
            this.$router.push({
              path: params.routerName
            })
          }, 10)
          break;
      }
    },
    async redirectToGray() {
      const username = window.CUR_DATA.user && window.CUR_DATA.user.name;
      if (username) {
        const res = await request({
          url: '/api-opc/v1/so/template/whiteSwitch',
          method: 'get'
        })
        if (res && res.data && res.data === true) {
          Cookies.set('traffic-type', 'test')
        } else {
          Cookies.remove('traffic-type')
        }
      }
    },
    handleLink(data) {
      const link = data.detail.itemPersonLink
      if (link) {
        const _url = new URL(link)
        if (_url.host.indexOf('boss') > -1) {
          const firstRouteName = _url.pathname.split('/').filter((item) => !!item)[0];
          if (firstRouteName && firstRouteName.length > 2) {
            const url = _url.pathname + _url.search
            this.$router.push({
              path: url
            })
            return
          }
        }
        window.open(link, '_blank').focus();
      }
    },
    routeToWorkbench() {
      this.$router.push({
        path: '/'
      })
    }
  },
  destroyed() {
    // this.ws.close()
  },
  mounted() {
    window.boss = this
    if (!/fetest|localhost|local/.test(location.href)) {
      // this.redirectToGray()
    }
    // .main-side
    let hash = window.location.hash || ''
    hash = hash.replace('#', '')
    let hashObj = qs.parse(hash)
    let leftMenuHidden =
      hashObj.leftMenuHidden || sessionStorage.getItem('leftMenuHidden')
    if (leftMenuHidden + '' === '1') {
      sessionStorage.setItem('leftMenuHidden', '1')
    } else {
      sessionStorage.setItem('leftMenuHidden', '0')
    }
    this.leftMenuHideControl(leftMenuHidden)

    this.$store.dispatch('getUserRole').then((res) => {
      console.log(res)
    })
    this.initAnnouncement()
    this.mainSide = document.querySelector('.main-side')
    safeRun(() => {
      openNewTab() && isInWhiteList(location) && toggleSideMenuApi('hide')
    })
    this.$store.dispatch('orderPurchase/getMMAuthConfig')
    this.$store.dispatch('orderCommon/getCustomizeOrderConfig')
    this.$store.dispatch('orderCommon/getMorePositionDisabledConfig')
    this.$store.dispatch('orderCommon/getTemplateExcelUrls')
    this.$store.dispatch('orderCommon/getSapReturnOrderValidator')
    // createWebsocket({ vue: this })
    // let that = this
    // 监听子页面想父页面的传参
    window.addEventListener('message', event => {
      // 此处执行事件
      this.handlePostMessage(event.data)
    })
    // document.addEventListener('visibilitychange', () => {
    //   if (!document.hidden) { // 页面呼出
    //     if (that.ws === '' || that.ws.readyState === 2 || that.ws.readyState === 3) {
    //       createWebsocket({ vue: that })
    //     }
    //   }
    // })
  }
}
</script>

<style lang="scss">
@import "../style/colors.scss";
$spec60: 60px;
$spec48: 48px;

.mt10 {
  margin-top: 10px;
}

.el-dropdown-link {
  cursor: pointer;
}
.el-message-box {
  width: auto !important;
  min-width: 400px;
  max-width: 800px;
  pre {
    overflow: auto;
  }
}
.show-tip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 5px 0;
  .tip-name {
    line-height: 18px;
    color: #1a57e4;
    text-decoration: underline;
  }
  .tip-count {
    background: red;
    border-radius: 10px;
    color: #fff;
    display: inline-block;
    font-size: 12px;
    line-height: 18px;
    padding: 0 6px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #fff;
    margin-left: 30px;
  }
}
.msg-dropdown {
  font-size: 20px !important;
  .el-dropdown-selfdefine {
    line-height: 20px !important;
  }
}
.menu-collapse {
  .main-side {
    width: 0;
    .module-side-menu {
      width: 0;
    }
  }
  .main-frame {
    .slot-nav-bar {
      left: 0;
    }
  }
  .menu-filter {
    display: none;
  }
}
.main-frame {
  padding: $spec48 0 0;
  .slot-nav-bar {
    left: 220px;
    height: $spec48;
    .nav-bar nav {
      font-size: 16px;
      height: $spec48;
      box-shadow: 0px 0px 8px 0px rgba(1, 52, 117, 0.16);
    }
  }
}
.main-side {
  width: 220px;
  transition: width 0.2s ease;
  overflow: hidden;
  z-index: 99;
  .slot-side-menu {
    top: $spec48;
  }
  .logo {
    height: $spec48;
    display: flex;
    align-items: center;
    line-height: $spec48;
    width: 100%;
    padding: 0 20px 0 22px;
    font-size: 18px;
    font-weight: 500;
    background: $color-dark-400;
    white-space: nowrap;

    a {
      color: $color-grey-200;
    }
    img {
      width: 28px;
      padding-right: 12px;
    }
  }
}
.collapse-icon {
  position: absolute;
  font-size: 24px;
  transition: none;
}
.main-side-hidden {
  .slot-nav-bar {
    left: 0;
    .collapse-icon {
      display: none;
    }
  }
}
.custom-return-error{
  white-space: break-spaces;
}
</style>
