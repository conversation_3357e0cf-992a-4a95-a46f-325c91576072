<template>
  <div class="page page-customized-order">
    <div class="module-filter">
      <el-form :inline="true">
        <el-form-item label="客户名称">
          <Client v-model="filter.customerNo" placeholder="请输入客户名称或编号" />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="filter.orderNo" placeholder="请输入" clearable></el-input>
        </el-form-item>
         <el-form-item label="采购单号">
          <el-input v-model="filter.purchaseNo" placeholder="请输入" clearable></el-input>
        </el-form-item>
         <el-form-item label="交货单号">
          <el-input v-model="filter.deliveryNo" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="文件状态">
          <el-select v-model="filter.customizationDeliveryState" placeholder="请选择">
            <el-option v-for="item in option.orderStatus" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="客服">
          <el-input v-model="filter.customerServiceName" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="订单日期">
          <el-date-picker clearable v-model="date.order" type="daterange" value-format="yyyy-MM-dd" range-separator="至" @change="res => afterChangeDate(res, 'order')" />
        </el-form-item>
        <el-form-item label="上传日期">
          <el-date-picker clearable v-model="date.upload" type="daterange" value-format="yyyy-MM-dd" range-separator="至" @change="res => afterChangeDate(res, 'upload')" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button type="primary" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="module-list">
      <el-tabs v-model="tab.active" @tab-click="getList(tab.active)">
        <el-tab-pane label="直发" name="direct">
          <el-table :data="list.direct" class="boss-table" v-loading="loading.list.direct" max-height="500" border :span-method="objectSpanMethod">
            <el-table-column prop="sapOrderNo" width="120px" label="SAP订单号"/>
            <el-table-column prop="soNo" width="120px" label="OMS订单号"/>
            <el-table-column prop="customerReferenceNo" width="120px" label="客户订单号"/>
            <el-table-column prop="orderNo" width="120px" label="外围订单号"/>
            <el-table-column show-overflow-tooltip prop="customerName" width="200px" label="客户名称"/>
            <el-table-column prop="customerServiceName" label="客服"/>
            <el-table-column prop="gmtCreate" width="140px" label="订单日期">
              <template slot-scope="{ row }">
                <span>{{ formatTime(row.gmtCreate) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="lifnr" label="供应商编号"/>
            <el-table-column prop="ebeln" width="120px" label="采购单号"/>
            <el-table-column prop="poGmtCreate" width="140px" label="采购单日期">
              <template slot-scope="{ row }">
                <span>{{ formatTime(row.poGmtCreate) }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="buyer" label="采购员"/> -->
            <!-- <el-table-column prop="sku" label="SKU编号"/>
            <el-table-column show-overflow-tooltip prop="sapMaterialName" width="200px" label="物料描述"/>
            <el-table-column prop="menge" label="采购数量">
              <template slot-scope="{ row }">
                <span>{{ parseInt(row.menge || 0, 10) }}</span>
              </template>
            </el-table-column> -->
              <el-table-column show-overflow-tooltip prop="state" label="文件类型">
              <template slot-scope="{ row }">
                <span>{{ formatAttachmentLabel(row.attachementType) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="customizationDeliveryState" label="文件状态">
              <template slot-scope="{ row }">
                <span>{{row.customizationDeliveryState === '1' ? '已上传' : '未上传' }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="fileName" label="文件名称"/>
            <el-table-column show-overflow-tooltip prop="operatorTime" label="上传日期">
              <template slot-scope="{ row }">
                <span>{{ formatTime(row.operatorTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="operatorName" label="上传人"/>
            <el-table-column show-overflow-tooltip prop="customizationRemark" label="备注"/>
            <el-table-column label="操作" width="120px" fixed="right">
              <template slot-scope="{ row }">
                <el-link type="primary" @click="openUpload(row, 'po')" v-if="row.customizationDeliveryState !== '1'">上传</el-link>
                <template v-else>
                  <el-link type="primary" @click="openRemark(row, 'po')">{{ row.customizationRemark ? '编辑' : '添加'}}备注</el-link>
                  <el-link type="primary" :href="row.fileUrl" target="_blank">预览</el-link>
                  <el-link type="primary" @click="download(row)">下载</el-link>
                  <el-link type="primary" @click="removeOrder('po', row)">删除</el-link>
                  <el-link type="primary" @click="openUpload(row, 'po')">继续上传</el-link>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="direct.filter.current"
            :page-sizes="[10, 20]"
            :page-size="direct.filter.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total.direct">
          </el-pagination>
        </el-tab-pane>
        <el-tab-pane label="非直发" name="notDirect">
          <el-table :data="list.notDirect" class="boss-table" v-loading="loading.list.notDirect" max-height="500" border :span-method="objectSpanMethod" >
            <el-table-column prop="sapOrderNo" width="120px" label="SAP订单号"/>
            <el-table-column prop="soNo" width="120px" label="OMS订单号"/>
            <el-table-column prop="customerReferenceNo" width="120px" label="客户订单号"/>
            <el-table-column prop="orderNo" width="120px" label="外围订单号"/>
            <el-table-column show-overflow-tooltip prop="customerName" label="客户名称"/>
            <el-table-column prop="customerServiceName" label="客服"/>
            <el-table-column prop="gmtCreate" width="140px" label="订单日期">
              <template slot-scope="{ row }">
                <span>{{ formatTime(row.gmtCreate) }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="dnId" label="交货单号"/>
            <el-table-column prop="dnGmtCreate" width="140px" label="交货单日期">
              <template slot-scope="{ row }">
                <span>{{ formatTime(row.dnGmtCreate) }}</span>
              </template>
            </el-table-column>
             <el-table-column show-overflow-tooltip prop="state" label="文件类型">
              <template slot-scope="{ row }">
                <span>{{ formatAttachmentLabel(row.attachementType) }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="state" label="文件状态">
              <template slot-scope="{ row }">
                <span>{{row.customizationDeliveryState === '1' ? '已上传' : '未上传' }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="fileName" label="文件名称"/>
            <!-- <el-table-column prop="fileType" label="类型"/> -->
            <el-table-column prop="operatorTime" width="140px" label="上传日期">
              <template slot-scope="{ row }">
                <span>{{ formatTime(row.operatorTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column show-overflow-tooltip prop="operatorName" label="上传人"/>
            <el-table-column show-overflow-tooltip prop="customizationRemark" label="备注"/>
            <el-table-column label="操作" width="120px" fixed="right">
              <template slot-scope="{ row }">
                <el-link type="primary" @click="openUpload(row, 'dn')" v-if="row.customizationDeliveryState !== '1'">上传</el-link>
                <template v-else>
                  <el-link type="primary" @click="openRemark(row, 'dn')">{{ row.customizationRemark ? '编辑' : '添加'}}备注</el-link>
                  <el-link type="primary" :href="row.fileUrl" target="_blank">预览</el-link>
                  <el-link type="primary" @click="download(row)">下载</el-link>
                  <el-link type="primary" @click="removeOrder('dn', row)">删除</el-link>
                  <el-link type="primary" @click="openUpload(row, 'dn')">继续上传</el-link>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="notDirect.filter.current"
            :page-sizes="[10, 20]"
            :page-size="notDirect.filter.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total.notDirect">
          </el-pagination>
        </el-tab-pane>
      </el-tabs>
    </div>

    <el-dialog title="添加附件" class="module-attachment" :visible.sync="show.upload" width="600px" :destroy-on-close="true">
      <el-form ref="form" label-width="80px" size="mini">
        <el-form-item>
          <p>{{'可将文件直接拖拽到改区域、或者点击上传按钮，仅支持PDF' + (current.attachementType === 'label' ? '/EXCEL' : '') + '附件哟'}}</p>
          <el-upload
            ref="upload"
            action="/ali-upload"
            :accept="current.attachementType === 'label' ? '.xls, .xlsx, .xlsm, .csv, application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/pdf ,.pdf': 'application/pdf,.pdf'"
            :before-upload="$validateFileType"
            multiple
            drag
            :limit="5"
            :data="{ appName:'onestop' }"
            :with-credentials="true"
            :show-file-list="true"
            :on-success="afterUploadSucess"
            :on-error="afterUploadError"
            :on-remove="afterUploadRemove"
            :on-exceed="afterUploadExceed"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div slot="tip" class="el-upload__tip">{{ upload.notice }}</div>
          </el-upload>
        </el-form-item>
        <el-form-item >
          <div style="text-align: right">
          <el-button @click="show.upload = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitUpload">确认</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="添加备注" :visible.sync="show.remark" :destroy-on-close="true">
      <el-form>
        <el-form-item>
          <el-input type="textarea" :rows="7" placeholder="请输入内容" v-model="dialog.remark.customizationRemark"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="show.remark = false">关闭</el-button>
          <el-button type="primary" @click="handleSubmitRemark">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import downloadjs from 'downloadjs'
import Client from '@/components/SearchFields/client'
import { api } from '@/api/boss'
import moment from 'moment'

export default {
  components: {
    Client
  },
  data () {
    return {
      filter: {
        customerNo: null,
        orderNo: null,
        customizationDeliveryState: '0',
        customerServiceName: null,
        gmtCreateTime: null,
        gmtCreateTimeTo: null,
        operatorTime: null,
        operatorTimeTo: null
      },
      date: {
        order: [],
        upload: []
      },
      direct: {
        filter: {
          current: 1,
          pageSize: 10
        }
      },
      notDirect: {
        filter: {
          current: 1,
          pageSize: 10
        }
      },
      current: {},
      tab: {
        active: 'direct'
      },
      list: {
        direct: [],
        notDirect: []
      },
      total: {
        direct: 0,
        notDirect: 0
      },
      loading: {
        submitUpload: false,
        list: {
          direct: false,
          notDirect: false
        }
      },
      show: {
        upload: false,
        remark: false
      },
      dialog: {
        upload: {},
        remark: {
          customizationRemark: ''
        }
      },
      upload: {
        files: []
      },
      option: {
        orderStatus: [
          { label: '已上传', value: '1' },
          { label: '未上传', value: '0' }
        ]
      },
      map: {
        type: {
          direct: 'po',
          notDirect: 'dn'
        },
        ids: {
          po: {
            id: 'ebeln'
          },
          dn: {
            id: 'dnId'
          }
        }
      },
      spanArr: []
    }
  },
  computed: {
    /**
     * 列表类型
     * PO 直发
     * DN 非直发
     */
    bizType () {
      return this.tab.active === 'direct' ? 'po' : 'dn'
    }
  },
  methods: {
    formatTime (dateString) {
      if (!dateString) return
      const t = new Date(dateString)
      const date = dateString.split('T')[0]
      const time = t.toTimeString().split(' ')[0]
      return `${date} ${time}`
    },
    handleSizeChange (res) {
      const type = this.tab.active
      this[type].filter.pageSize = res
      this.getList(type)
    },
    handleCurrentChange (res) {
      const type = this.tab.active
      this[type].filter.current = res
      this.getList(type)
    },
    handleSearch () {
      const type = this.tab.active
      this[type].filter.current = 1
      this.getList(type)
    },
    openRemark (data, type) {
      this.current = data
      this.current.type = type
      this.show.remark = true
      this.dialog.remark.customizationRemark = data.customizationRemark || ''
    },
    openUpload (data, type) {
      this.current = data
      this.current.type = type
      this.show.upload = true
      this.upload.files = []
    },
    reset () {
      for (const key in this.filter) {
        this.filter[key] = null
      }
      this.filter.customizationDeliveryState = '0'
      this.date.order = []
      this.date.upload = []
    },

    download (data) {
      // window.open(data.fileUrl)
      downloadjs(data.fileUrl)
    },

    afterChangeDate(res, type) {
      const [ min, max ] = res || []

      const map = {
        upload: {
          start: 'operatorTime',
          end: 'operatorTimeTo'
        },
        order: {
          start: 'gmtCreateTime',
          end: 'gmtCreateTimeTo'
        }
      }

      this.filter[map[type].start] = min
      this.filter[map[type].end] = max
    },

    getList (type) {
      this.loading.list[type] = true
      api({
        url: `/customization/${this.map.type[type]}`,
        query: Object.assign(this.filter, this[type].filter),
        complete: res => {
          if (res.code === 200) {
            this.list[type] = res.data || []
            this.total[type] = res.totalCount
            this.getSpanArr(this.list[type])
          } else {
            this.list[type] = []
            this.total[type] = 0
            this.$message.error('API ERROR: ' + res.msg)
          }
          this.loading.list[type] = false
        }
      })
    },

    formatAttachmentLabel (type) {
      switch (type) {
        case 'other':
          return '其他随货资料';
        case 'label':
          return '标签';
        default:
          return '送货单';
      }
    },

    // 处理表格数据，得到需合并的规则
    getSpanArr (data) {
      // const idx = 0 // 此处定义第0列需要计算行合并
      const map = {
        po: 'ebeln',
        dn: 'dnId'
      }
      const anotherMap = {
        po: 'attachementType',
        dn: 'attachementType'
      }
      const prop = map[this.bizType] // 此处定义第0列的属性名，用于数据比较
      const anotherProp = anotherMap[this.bizType]
      this.spanArr = []
      this.position = 0
      data && data.forEach((element, index) => {
        if (index === 0) {
          this.spanArr.push(1)
          this.position = 0
        } else {
          if (data[index][prop] === data[index - 1][prop] && (anotherProp ? data[index][anotherProp] === data[index - 1][anotherProp] : true)) {
            // 有相同项
            this.spanArr[this.position] += 1
            this.spanArr.push(0) // 名称相同后往数组里加一项0
          } else {
            // 同列的前后两行单元格不相同
            this.spanArr.push(1)
            this.position = index
          }
        }
      })
    },

    objectSpanMethod ({ row, column, rowIndex, columnIndex }) {
      const mergeList = {
        po: [0, 1, 2, 3, 4, 5, 6, 7, 8],
        dn: [0, 1, 2, 3, 4, 5, 6, 7]
      }
      // 需要合并的列索引，0 -> 第一列
      if (~mergeList[this.bizType].indexOf(columnIndex)) {
        const _row = this.spanArr[rowIndex]
        const _col = _row > 0 ? 1 : 0
        return {
          rowspan: _row,
          colspan: _col
        }
      }
    },

    apiRemoveFile (type, item) {
      // DN("dn","交货单")
      // PO("po", "采购单")
      // tedId
      const map = this.map.ids

      api({
        prefix: '/documentApi',
        url: '/v1/ecorp/delEcorpDoc',
        method: 'POST',
        data: {
          id: item.tedId,
          dimension: type,
          vNo: item[map[type].id]
        },
        complete: res => {
          // if (res.code === 200 && type === 'po') {
          //   api({
          //     url: '/customization/po/delete',
          //     method: 'PUT',
          //     data: {
          //       ebeln: item.ebeln,
          //       ebelp: item.ebelp,
          //       poItemId: item.poItemId,
          //       sapOrderNo: item.sapOrderNo
          //     },
          //     complete: res => {
          //       console.log(res)
          //     }
          //   })
          // }
          this.getList(this.tab.active)
        }
      })
    },
    afterUploadSucess (res, file, fileList) {
      console.log('upload', res, file, fileList)
      // console.log(this.upload.files)
      // file = file.response && file.response[0] && file.response[0]

      this.upload.files = fileList
    },
    afterUploadRemove (file, fileList) {
      this.upload.files = []
    },
    afterUploadRequest () {

    },
    afterUploadError (error) {
      this.$message.error(error.error || error.message || '上传失败！')
    },
    afterUploadExceed (error) {
      this.$message.error(error.error || error.message || '文件单次最多上传5个！')
    },

    removeOrder (type, data) {
      this.$confirm('确定要删除附件吗', '提示', { type: 'warning' })
        .then(() => {
          this.apiRemoveFile(type, data)
        }).catch(() => {})
    },
    handleSubmitRemark () {
      const { tedId } = this.current
      // const map = this.map.ids
      // const id = this.current[map[type].id]

      api({
        prefix: '/documentApi',
        url: `/v1/ecorp/upEcorpRemark/${tedId}`,
        query: {
          remark: this.dialog.remark.customizationRemark
        },
        complete: res => {
          this.show.remark = false
          if (res.code === 200) {
            // console.log(res)
            this.$message.success('保存成功')
            this.getList(this.tab.active)
          } else {
            this.$message.error('保存失败')
          }
        }
      })
    },
    handleSubmitUpload () {
      if (this.loading.submitUpload) return
      this.loading.submitUpload = true
      const item = this.current
      const { type } = item
      const files = this.upload.files
      const map = this.map.ids
      if (files.filter(file => file.status !== 'success').length) {
        this.loading.submitUpload = false
        return this.$message.error('请等待文件上传完成！')
      }
      if (!files || !files.length) {
        this.loading.submitUpload = false
        return this.$message.error('请先上传文件')
      }
      const attachmentType = item.attachementType ? item.attachementType : 'deliveryNote'
      const filelist = files.map(file => {
        const fileRes = file.response && file.response.length && file.response[0]
        return {
          attachmentType,
          // secondLevelBusinessId: this.current[map[type].secId],
          upUserName: window.CUR_DATA.user && window.CUR_DATA.user.name,
          fileName: file.name,
          bucketName: fileRes.bucketName,
          url: fileRes.url,
          uploadTime: moment(new Date()).format('yyyy-MM-DD HH:mm:ss')
        }
      })
      api({
        prefix: '/documentApi',
        url: '/v2/ecorp/saveDocumentMetaData',
        method: 'POST',
        data: {
          businessId: this.current[map[type].id],
          dimension: type,
          docMetaDataList: filelist,
          source: 'BOSS'
        },
        complete: res => {
          if (res.code === 200) {
            this.$message.success('API: ' + res.msg)
            this.getList(this.tab.active)
            if (type === 'po') {
              let [ file ] = files
              file = filelist[0]
              api({
                url: '/customization/po/upload',
                method: 'POST',
                data: {
                  ebeln: item.ebeln,
                  // ebelp: item.ebelp,
                  fileName: file.fileName,
                  fileType: 'pdf',
                  fileUrl: file.url,
                  attachmentType: item.attachementType,
                  // poItemId: item.poItemId,
                  // printQuantity: item.printQuantity,
                  sapOrderNo: item.sapOrderNo
                },
                complete: res => {
                  this.getList(this.tab.active)
                  console.log(res)
                }
              })
            }
          } else {
            this.$message.error('API: ' + res.msg)
          }
          this.loading.submitUpload = false
          this.show.upload = false
        }
      })
    }
  },
  mounted () {
    const query = JSON.parse(localStorage.getItem('/customizedOrder/list')) || {}
    // 使用初始查询条件查询一次后需要清除该初始条件
    // setItem 参见 router/index.js > beforeEach
    localStorage.removeItem('/customizedOrder/list')

    // const query = { ...this.$route.query }
    if (query.deliveryType === 'ZKH') {
      this.tab.active = 'notDirect'
    }
    delete query.deliveryType
    try {
      if (query.current) {
        query.current = Number(query.current)
        this[this.tab.active].filter.current = query.current
      }
      if (query.pageSize) {
        query.pageSize = Number(query.pageSize)
        this[this.tab.active].filter.pageSize = query.pageSize
      }
    } catch (error) {
      console.log(error);
    }
    this.filter = { ...this.filter, ...query }
    this.getList(this.tab.active)
    // history.replaceState(null, '', window.location.href.split('?')[0])
  }
}
</script>

<style lang="less">
.page-customized-order {
  .el-form--inline .el-form-item__label {
    width: 120px;
  }
  .boss-table {
    .el-link {
      margin-right: 4px;
    }
  }
  .el-form-item--mini.el-form-item{
    // text-align: right;
  }
  .el-form-item--small.el-form-item {
    text-align: right;
  }
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}
.module-attachment {
  .el-upload-list__item {
    transition: none;
  }
}
</style>
