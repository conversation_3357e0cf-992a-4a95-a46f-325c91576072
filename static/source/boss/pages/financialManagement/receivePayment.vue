<template>
  <div class="page page-home financial-management">
    <iframe
      ref="iframe"
      :height="iframe.height"
      width="100%"
      scrolling="auto"
      :src="iframeSrc"
      frameborder="0"></iframe>
  </div>
</template>
<script>
import { getCookie } from '@/utils/index'
export default {
  name: 'receivePayment',
  data () {
    return {
      container: '',
      iframe: {
        height: 0,
        width: 0
      },
      bridge: 'IframeBridge',
      realPath: 'financialManagement/receivePayment',
      iframePath: 'IframeBridge'
    }
  },
  created () {
    window.onresize = this.throttle(this.resize, 800)
  },
  mounted () {
    this.container = this.$refs.iframe.parentNode
    this.resize()
    this.initPostIframeMessage()
    this.initMessageListener()
  },
  beforeRouteLeave (to, from, next) {
    this.iframePath = this.bridge
    next()
  },
  computed: {
    iframeSrc () {
      let ret = `https://admin.gongbangbang.com/${this.iframePath}?embed=1`
      if (/boss-uat/.test(location.href)) {
        ret = `https://zkh-gbb-admin-web-uat.gongbangbang.com/${this.iframePath}?embed=1`
      }
      if (/fetest|localhost/.test(location.href)) {
        ret = `http://localhost:8000/${this.iframePath}?embed=1`
      }
      return ret
    }
  },
  methods: {
    initMessageListener () {
      const that = this
      function receiveMsgFromChild (event) {
        try {
          const { success, source } = event.data
          if (success && source === 'gbb-admin') {
            that.iframePath = that.realPath
          }
        } catch (err) { console.log(err) }
      }
      window.addEventListener('message', receiveMsgFromChild, false)
    },
    getAccessToken () {
      let proToken = 'zkh_access_token'
      let token
      try {
        token = document.cookie.match(/([a-z_]*?zkh_access_token)/g)
        const uatToken = token.filter(r => ~r.indexOf('uat'))[0]
        const localToken = token.filter(r => ~r.indexOf('local'))[0]
        if (/fetest|localhost|uat/.test(location.href)) {
          token = uatToken || localToken
        } else {
          token = proToken
        }
      } catch (err) { console.log(err) }
      return token
    },
    initPostIframeMessage () {
      try {
        const username = window.CUR_DATA.user.name
        const prefix = this.getAccessToken()
        const token = getCookie(prefix)
        console.log('%c username, prefix, token',
          'background-color:red;color:white;font-size:2em;', username, prefix, token)
        this.$refs.iframe.onload = () => {
          this.$refs.iframe.contentWindow.postMessage({
            username, token, source: 'boss'
          }, '*')
        }
      } catch (err) { console.log(err) }
    },
    resize () {
      try {
        let rect = this.container && this.container.getBoundingClientRect()
        this.iframe.width = rect.width - 40
        this.iframe.height = window.innerHeight - 40 - 50
      } catch (err) {
        console.log(err)
      }
    },
    debounce (fn, delay = 800) {
      let timer
      return function () {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    },
    throttle (fn, delay = 800) {
      let timer
      return function () {
        if (timer) return
        timer = setTimeout(() => {
          fn()
          timer = null
        }, delay)
      }
    }
  }
}
</script>
<style>
.financial-management{
  padding: 0px;
  overflow: hidden;
  position: relative;
}
</style>
