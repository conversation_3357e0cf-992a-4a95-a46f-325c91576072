<template>
  <div class="dunning-letter-detail">
    <DividerHeader>客户信息</DividerHeader>
    <div class="statement-set-block">
      <div class="statement-set-block-title">
        <div>{{ detailData.customerName + " " + detailData.customerCode }}</div>
        <div>
          <el-button type="primary" plain @click="resend" v-if="showResend">重新发送</el-button>
        </div>
      </div>
      <div class="statement-set-block-item">
        <el-row>
          <el-col :span="8">总计欠款：{{ detailData.ownMoney }}</el-col>
          <el-col :span="8"
            >销售组织：{{ detailData.salesOrganizationName }}</el-col
          >
        </el-row>
        <el-row>
          <el-col v-if="detailData.letterType === 1" :span="8"
            >催款函发送日期：{{ detailData.sendDate }}</el-col
          >
          <el-col v-if="detailData.letterType === 2" :span="8"
            >付款提醒函发送日期：{{ detailData.sendDate }}</el-col
          >
        </el-row>
      </div>
    </div>
    <div v-if="showImport">
      <DividerHeader>上传附件</DividerHeader>
      <div class="statement-set-block">
        <div class="statement-set-block-upload-container">
          <div class="statement-set-block-upload-btn">
            <el-upload
              class="upload-demo"
              drag
              :data="{ id }"
              multiple
              :show-file-list="false"
              :limit="9"
              :on-error="onUploadError"
              :on-success="onUploadSuccess"
              :before-upload="$validateFileType"
              :accept="acceptFileType.commonType"
              action="/api-financial/callletter/upload"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div class="el-upload__tip" slot="tip">
                最多上传9个文件，且每个文件不超过10MB
              </div>
            </el-upload>
          </div>
          <div class="statement-set-block-file-list">
            <ul class="mt-10 w-60">
              <li
                :key="index"
                v-for="(item, index) in detailData.fileUploadLogList || []"
              >
                <span class="file-wrapper">
                  <span>{{ item.fileName }}</span>
                  <el-button
                    class="ml-10"
                    type="text"
                    icon="el-icon-download"
                    @click="downloadFile(item)"
                    >下载</el-button
                  >
                </span>
                <el-divider></el-divider>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    <ResendDialog :showDialog.sync="resendVisible" :data="detailData" />
  </div>
</template>

<script>
import moment from 'moment';
import {
  getDunningLetterDetail,
  downloadLetterFile
} from '@/api/accountReceivableManagement.js';
import DividerHeader from '@/components/DividerHeader';
import ResendDialog from './components/ResendDialog.vue';
import { downloadByLink, isValidLink } from '@/utility/request'
import { mapState } from 'vuex'
export default {
  name: 'DunningLetterDetail',
  components: {
    DividerHeader,
    ResendDialog
  },
  data() {
    return {
      id: '',
      detailData: {},
      menu: [],
      resendVisible: false
    };
  },
  created() {
    this.initData();
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType
    }),
    showImport () {
      let ret = false
      try {
        const thirdButton = this.menu.children.filter(menu => menu.name === '催款函上传附件')[0]
        if (thirdButton) {
          ret = true
        }
      } catch (err) {}
      return ret
    },
    showResend () {
      let ret = false
      try {
        const thirdButton = this.menu.children.filter(menu => menu.name === '催款函重新发送')[0]
        if (thirdButton) {
          ret = true
        }
      } catch (err) {}
      return ret
    }
  },
  methods: {
    resend() {
      this.resendVisible = true;
      // resendDunningLetter(this.id).then((res) => {
      //   if (res.status === 200) {
      //     if (res.datas) {
      //       this.$message.success(res.msg || '发送成功！');
      //     }
      //   } else {
      //     this.$message.error(res.msg || '发送失败！');
      //   }
      // });
    },
    formatDate(timestamp) {
      return moment(new Date(timestamp)).format('YYYY-MM-DD HH:mm:ss');
    },
    initData() {
      const firstMenu = this.$store.state.menu.filter(menu => menu.name === '应收款管理')[0]
      this.menu = firstMenu.children.filter(menu => menu.name === '催款函列表')[0]
      this.id = this.$route.params.id;
      getDunningLetterDetail(this.id).then((res) => {
        if (res.status === 200) {
          if (res.datas) {
            this.detailData = res.datas;
          }
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    downloadFile(item) {
      downloadLetterFile(item.id).then(res => {
        if (res && res.status === 200 && res.datas) {
          if (isValidLink(res.datas)) {
            downloadByLink(res.datas)
            this.$message.success('导出成功！')
          } else {
            this.$message.error('导出链接不合法！')
          }
        } else {
          this.$message.error(res.msg || res.message || '导出失败！')
        }
      })
    },
    onUploadSuccess(response) {
      if (response && response.status === 200) {
        this.$message.success(response.message || '上传成功！');
      } else {
        this.$message.error((response && response.message) || '上传失败！');
      }
      this.initData();
    },
    onUploadError(error) {
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.dunning-letter-detail {
  .statement-set-block {
    .statement-set-block-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 40px 0 30px;
      font-size: 18px;
    }
    .statement-set-block-item {
      padding: 0 30px;
      margin: 20px 0;
      font-size: 15px;
      & > div {
        margin-top: 10px;
      }
    }

    .statement-set-block-upload-container {
      padding: 0 30px;

      .statement-set-block-file-list {
        .file-wrapper {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
  }
  .mt-10 {
    margin-top: 10px;
  }
  .w-60 {
    width: 60%;
  }
  .ml-10 {
    margin-left: 10px;
  }
  ::v-deep .el-divider--horizontal {
    margin: 8px 0;
  }
}
</style>
