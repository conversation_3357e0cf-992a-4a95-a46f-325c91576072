<template>
  <div class="dunning-letter-list">
    <el-form
      ref="searchForm"
      :model="searchForm"
      label-width="120px"
      label-position="right"
    >
      <el-row>
        <el-col :span="7">
          <el-form-item label="催款编号：" prop="letterCode">
            <el-input
              v-model="searchForm.letterCode"
              placeholder="请输入内容"
              clearable
          /></el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="催款类型：" prop="letterType">
            <el-select
              v-model.trim="searchForm.letterType"
              placeholder="请选择"
              clearable
            >
              <el-option
                v-for="item in dunningLetterOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="客户：" prop="customerCode">
            <remote-customer-component v-model="searchForm.customerCode" />
          </el-form-item>
        </el-col>
        <el-col :span="3" style="padding-left: 10px">
          <el-button
            type="primary"
            icon="el-icon-search"
            :loading="basisListLoading"
            @click="handleFilter"
            >查询</el-button
          >
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7">
          <el-form-item label="客服：" prop="customerServiceName">
            <el-input
              v-model.trim="searchForm.customerServiceName"
              placeholder="请输入"
              clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="销售：" prop="saleName">
            <el-input
              v-model.trim="searchForm.saleName"
              placeholder="请输入"
              clearable/>
          </el-form-item>
        </el-col>
        <el-col :span="7"><el-form-item></el-form-item></el-col>
        <el-col :span="3">
          <el-button type="primary" @click="dunningLetterVisible = true">获取催款函</el-button>
        </el-col>
      </el-row>
      </el-form>
    <el-table
      v-loading="basisListLoading"
      :data="basisList"
      border
      fit
      height="500"
    >
      <el-table-column
        label="催款编号"
        min-width="100px"
        align="center"
        prop="id"
      />
      <el-table-column
        label="催款类型"
        min-width="100px"
        align="center"
        prop="letterType"
      >
        <template slot-scope="{ row }">
          {{ formatLabel(row.letterType) }}
        </template>
      </el-table-column>
      <el-table-column
        label="客户编码"
        min-width="100px"
        align="center"
        prop="customerCode"
      />
      <el-table-column
        label="客户名称"
        min-width="160px"
        align="center"
        prop="customerName"
      />
      <el-table-column
        label="催款函生成日期"
        min-width="120px"
        align="center"
        prop="sendDate"
      />
      <el-table-column
        label="最近一次邮件发送时间"
        min-width="160px"
        align="center"
        prop="lastSendDate"
      />
      <el-table-column
        label="发送次数"
        min-width="100px"
        align="center"
        prop="sendCount"
      />
      <el-table-column
        label="欠款总额"
        min-width="100px"
        align="center"
        prop="ownMoney"
      />
      <el-table-column
        label="公司主体"
        min-width="120px"
        align="center"
        prop="salesOrganizationName"
      />
      <el-table-column
        label="客服"
        min-width="120px"
        align="center"
        prop="customerServiceName"
      />
      <el-table-column
        label="销售"
        min-width="120px"
        align="center"
        prop="saleName"
      />
      <el-table-column
        fixed="right"
        label="操作"
        min-width="220px"
        align="center"
      >
        <template slot-scope="{ row }">
          <el-button type="text" @click="viewDetail(row)">查看</el-button>
          <el-upload
            class="btn-upload"
            :show-file-list="false"
            action="/api-financial/callletter/upload"
            :on-success="onUploadSuccess"
            :before-upload="beforeUpload"
            :data="{ id: row.id }"
            :on-error="onUploadError"
            :accept="acceptFileType.commonType"
            name="file"
          >
            <el-button
              :loading="importLoading"
              type="text"
              v-if="showImport"
              >上传附件</el-button
            >
          </el-upload>

          <el-button
            type="text"
            v-if="showResend"
            @click="resend(row)"
            >重新发送</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      align="right"
      :page.sync="listQueryInfo.current"
      :pageSizes="[10, 20, 30, 50, 100, 200, 500]"
      :limit.sync="listQueryInfo.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getBasisList"
    />
  <ResendDialog :showDialog.sync="resendVisible" :data="rowData" />
  <DunningLetterDialog :showDialog.sync="dunningLetterVisible" @submit="getBasisList" />
  </div>
</template>

<script>
import moment from 'moment';
import get from 'lodash/get';
import find from 'lodash/find';
import RemoteCustomerComponent from '@/components/SearchFields/customer.vue';
import Pagination from '@/components/Pagination';
import {
  getDunningLetterList
  // resendDunningLetter
} from '@/api/accountReceivableManagement.js';
import ResendDialog from './components/ResendDialog.vue';
import DunningLetterDialog from './components/DunningLetterDialog.vue';
import { mapState } from 'vuex'

export default {
  name: 'DunningLetterList',
  components: {
    Pagination,
    RemoteCustomerComponent,
    ResendDialog,
    DunningLetterDialog
  },
  data() {
    return {
      searchForm: {},
      importLoading: false,
      dunningLetterOptions: [
        {
          label: '催款函',
          value: 1
        },
        {
          label: '付款提醒函',
          value: 2
        }
      ],
      basisList: [],
      basisListLoading: false,
      uploadData: {},
      total: 0,
      listQueryInfo: {
        current: 1,
        pageSize: 20
      },
      menu: [],
      resendVisible: false,
      dunningLetterVisible: false,
      rowData: {}
    };
  },
  mounted() {
    this.handleFilter();
    this.initMemu()
  },
  computed: {
    ...mapState({
      acceptFileType: state => state.orderCommon.acceptFileType
    }),
    showImport () {
      let ret = false
      try {
        const thirdButton = this.menu.children.filter(menu => menu.name === '催款函上传附件')[0]
        if (thirdButton) {
          ret = true
        }
      } catch (err) {}
      return ret
    },
    showResend () {
      let ret = false
      try {
        const thirdButton = this.menu.children.filter(menu => menu.name === '催款函重新发送')[0]
        if (thirdButton) {
          ret = true
        }
      } catch (err) {}
      return ret
    }
  },
  methods: {
    initMemu () {
      const firstMenu = this.$store.state.menu.filter(menu => menu.name === '应收款管理')[0]
      this.menu = firstMenu.children.filter(menu => menu.name === '催款函列表')[0]
    },
    resend(row) {
      this.resendVisible = true;
      this.rowData = row;
      // resendDunningLetter(row.id).then((res) => {
      //   if (res.status === 200) {
      //     if (res.datas) {
      //       this.$message.success(res.msg || '发送成功！');
      //     }
      //   } else {
      //     this.$message.error(res.msg || '发送失败！');
      //   }
      // });
    },
    formatLabel(type) {
      if (type) {
        return get(
          find(this.dunningLetterOptions, (item) => item.value === type),
          'label'
        );
      }
    },
    formatDate(timestamp) {
      return moment(new Date(timestamp)).format('YYYY-MM-DD HH:mm:ss');
    },
    beforeUpload(file) {
      if (!this.$validateFileType(file)) return false
      this.importLoading = true;
    },
    onUploadSuccess(response) {
      this.importLoading = false;
      if (response && response.status === 200) {
        this.$message.success(response.message || '上传成功！');
      } else {
        this.$message.error((response && response.message) || '上传失败！');
      }
    },
    onUploadError(error) {
      this.importLoading = false;
      this.$message.error(
        (error && error.msg) || (error && error.message) || '上传失败'
      );
    },
    viewDetail(row) {
      this.$router.push(
        `/account-receivable-management/dunningLetterDetail/${row.id}`
      );
    },
    trimParams(obj) {
      for (let key in obj) {
        if (obj[key] && obj[key].trim) {
          obj[key] = obj[key].trim();
        }
      }
    },
    handleFilter() {
      this.listQueryInfo.current = 1;
      this.getBasisList();
    },
    getBasisList() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.trimParams(this.searchForm);
          let param = { ...this.searchForm };
          param.pageNum = this.listQueryInfo.current;
          param.pageSize = this.listQueryInfo.pageSize;
          this.basisList = [];
          this.basisListLoading = true;
          getDunningLetterList(param)
            .then((res) => {
              this.basisListLoading = false;
              if (res.status === 200) {
                if (res.datas) {
                  const data = res.datas;
                  this.total = data.total;
                  this.basisList = data.list;
                }
              } else {
                this.$message.error(res.message);
              }
            })
            .catch(() => {
              this.basisListLoading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dunning-letter-list {
  padding: 20px 10px;

  .btn-upload {
    display: inline-block;
    margin: 0 10px;
  }
}
</style>
