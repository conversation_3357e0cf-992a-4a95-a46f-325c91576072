<template>
  <el-dialog
    :visible.sync="showDlg"
    center
    title="确认需要给以下应收款联系人重新发送邮件吗？" width="500px"
    @open="getContactList">
    <el-form ref="form" :model="form" :rules="rules" v-loading="loading" label-width="120px">
        <el-form-item label="应收款联系人:" prop="contact">
            <el-select v-model="form.contact" multiple>
                <el-option v-for="(item, index) in contactList" :disabled="index===0" :key="item.contactId" :label="item.contactName" :value="item.contactId">
                    <div
                        class="ba-row-start selectClientItem"
                        :style="{fontWeight:index===0?'bold':'normal'}"
                        >
                            <div>{{ item.contactName }}</div>
                            <div>{{ item.email }}</div>
                        </div>
                </el-option>
            </el-select>
        </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          确认
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { getContactList, resend } from '@/api/accountReceivableManagement.js';
export default {
    data() {
      return {
        form: {
            contact: []
        },
        loading: false,
        submitLoading: false,
        contactList: [],
        rules: {
          contact: [
            { required: true, message: '请输入', trigger: 'blur' }
          ]
        }
      };
    },
    props: {
        showDialog: {
            type: Boolean,
            required: true
        },
        data: {
            type: Object,
            required: true
        }
    },
    computed: {
        showDlg: {
            get () {
                return this.showDialog
            },
            set (val) {
                this.$emit('update:showDialog', val)
            }
        }
    },
    methods: {
        handleClose () {
            this.$emit('update:showDialog', false)
        },
        getContactList () {
            this.loading = true;
            const data = {
                parentCode: this.data?.customerCode,
                stateCode: 1,
                parentIdTypeCode: 7,
                rowCount: 100,
                ifReceivables: 1
            }
            getContactList(data).then(res => {
                if (res?.code === '0000' && res?.result?.rows) {
                    this.contactList = res.result.rows.map(item => ({
                        contactName: item.fullName,
                        contactId: item.contactId,
                        email: item.emailAddress1 || item.emailAddress2
                    }))
                    this.contactList.unshift(
                        {
                            contactName: '联系人姓名',
                            email: '联系人邮箱'
                        }
                    )
                    this.form.contact = res.result.rows.map(item => item.contactId)
                    this.loading = false
                } else {
                    this.contactList = []
                    this.form.contact = []
                    this.loading = false
                }
            })
        },
        handleSubmit () {
          this.$refs.form.validate(valid => {
            if (valid) {
              this.submitLoading = true;
              const receiveAddressList = [];
              this.contactList.forEach(item => {
                if (~this.form.contact.indexOf(item.contactId)) {
                  receiveAddressList.push(item.email)
                }
              })
              const data = {
                id: this.data.id,
                receiveAddressList: receiveAddressList.join(',')
              }
              resend(data).then(res => {
                if (res?.status === 200) {
                  this.$message.success('发送成功');
                  this.handleClose();
                } else {
                  this.$message.error(res?.msg || res?.message || '发送失败');
                }
              }).finally(() => {
                this.submitLoading = false;
              })
            }
          })
        }
    }
  };
</script>
<style lang="scss" scoped>
.dialog-footer{
  margin: -10px 0px 0px;
  display: flex;
  justify-content: center;
}
.selectClientItem {
  div {
    margin-right: 5px;
    cursor: default;
  }
  div:nth-child(1) {
    width: 120px;
  }
  div:nth-child(2) {
    width: 150px;
  }
  div:nth-child(3) {
    width: 300px;
    overflow: auto;
  }
}
.ba-row-start{
  display: flex;
}
// ::v-deep {
//     .el-input, .el-input__inner{
//         width: 260px;
//         height: 140px;
//     }
// }
</style>
