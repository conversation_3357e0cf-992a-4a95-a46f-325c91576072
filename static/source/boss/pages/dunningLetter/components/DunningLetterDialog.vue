<template>
  <el-dialog
    :visible.sync="showDlg"
    center
    title="获取催款函" width="500px">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px" style="width: 400px">
        <el-form-item label="客户编码:" prop="customerCode">
            <Client v-model="form.customerCode" style="width: 100%" :getValue="true" @change="handleCustomerChange" />

        </el-form-item>
        <el-form-item label="客户名称:" prop="customerName">
            <el-input v-model="form.customerName" disabled></el-input>
        </el-form-item>
        <el-form-item label="公司主体:" prop="salesOrganization">
            <el-select v-model="form.salesOrganization" filterable style="width: 100%">
                <el-option
                    v-for="item in companyList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                />
            </el-select>
        </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
          获取
        </el-button>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script>
import { getDunningLetter } from '@/api/accountReceivableManagement.js';
import Client from '@/components/SearchFields/client.vue'
export default {
    data() {
      return {
        form: {
            customerCode: '',
            customerName: '',
            salesOrganization: ''
        },
        rules: {
          customerCode: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          customerName: [
            { required: true, message: '请输入', trigger: 'blur' }
          ],
          salesOrganization: [
            { required: true, message: '请输入', trigger: 'blur' }
          ]
        },
        submitLoading: false
      };
    },
    props: {
        showDialog: {
            type: Boolean,
            required: true
        }
    },
    components: { Client },
    created() {
      if (JSON.stringify(this.dictList) === '{}') {
        this.$store.dispatch('orderCommon/queryDictList')
      }
    },
    computed: {
        showDlg: {
            get () {
                return this.showDialog
            },
            set (val) {
                this.$emit('update:showDialog', val)
            }
        },
        dictList () {
            return this.$store.state.orderCommon.dictList || {}
        },
        companyList () {
            return this.dictList && this.dictList['companyScope']
        }
    },
    methods: {
        handleClose () {
            this.$emit('update:showDialog', false)
        },
        handleCustomerChange (val, customer) {
            console.log(val, customer)
            this.form.customerName = customer.customerName
        },
        handleSubmit () {
          this.$refs.form.validate(valid => {
            if (valid) {
              this.submitLoading = true;
              getDunningLetter({ ...this.form }).then(res => {
                if (res?.status === 200) {
                  this.$message.success('获取成功');
                  this.handleClose();
                  this.$emit('submit');
                } else {
                  this.$message.error(res?.msg || res?.message || '获取失败');
                }
              }).finally(() => {
                this.submitLoading = false;
              })
            }
          })
        }
    }
  };
</script>
<style lang="scss" scoped>
.dialog-footer{
  margin: -10px 0px 0px;
  display: flex;
  justify-content: center;
}
</style>
