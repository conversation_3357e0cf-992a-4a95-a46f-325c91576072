<template>
  <el-dialog
    title="消息订阅"
    :visible.sync="showDlg"
    :show-close="false"
    width="900px"
  >
    <div style="display:flex;padding-bottom:20px;align-items:center;">
      <div>消息类型：</div>
      <el-select
        v-model="msgRuleNo"
        filterable
        placeholder="请选择消息类型"
        clearable
        style="width:40%;"
      >
        <el-option
          v-for="item in msgRuleNoOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          ></el-option>
      </el-select>
      <el-button type="primary"  @click="getSubscriptions()" style="margin-left:10px;">
        查询
      </el-button>
    </div>
    <el-table border :data="list" width="100%" v-loading="listLoading" :header-cell-style="{'text-align':'center'}" >
      <el-table-column prop="msgRuleNo" label="消息类型" align="left" width="260">
        <template slot-scope="scope">
          <el-select
            v-model="scope.row.msgRuleNo"
            filterable
            placeholder="请选择消息类型"
            clearable
            :disabled="!scope.row.operatable"
            @change="handleMsgRuleChange"
          >
            <el-option
              v-for="item in msgRuleNoOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.subscription"
            ></el-option>
          </el-select>
        </template>
      </el-table-column>
      <el-table-column label="消息描述"  align="left" >
        <template slot-scope="{ row }">
          <div v-html="row.msgRuleConditionDesc"></div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="100" align="center">
        <template slot-scope="{row, $index}">
          <el-button v-if="row.operatable"
            type="text" size="mini" @click="handleSave($index)">
              保存
          </el-button>
          <el-button type="text" size="mini" @click="handleDel($index)">
              删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      layout="total, prev, pager, next, jumper"
      @pagination="getSubscriptions"
      style="text-align:ceter"
    />
    <div class="ba-row-center btnGroup">
      <el-button type="primary" @click="addline">新增计划行</el-button>
      <!-- <el-button type="primary" @click="saveline">确认保存</el-button> -->
      <el-button @click="$emit('update:showDialog', false)">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Pagination from '@/components/Pagination'
import * as msgApi from '@/api/messageCenter'
export default {
  props: ['showDialog', 'msgRuleNoOptions'],
  data () {
    return {
      list: [],
      msgRuleNo: '',
      total: 0,
      listLoading: false,
      listQuery: {
        page: 1,
        size: 20
      }
    }
  },
  components: {
    Pagination
  },
  computed: {
    showDlg: {
      get () {
        return this.showDialog
      },
      set (val) {
        this.$emit('update:showDialog', val)
      }
    }
  },
  watch: {
  },
  created() {
    this.getSubscriptions()
  },
  methods: {
    // 获取规则
    getSubscriptions() {
      this.listLoading = true
      const data = {
        msgRuleNo: this.msgRuleNo,
        pageNo: this.listQuery.page,
        pageSize: this.listQuery.size
      }
      msgApi.getSubscriptionsApi(data).then(res => {
        const { code, data, msg } = res
        if (code === 200 && data) {
          let showData = data.pcPageDTO.data
          for (let index in this.list) {
            if (this.list[index].operatable) {
              showData.push(this.list[index])
            }
          }
          this.list = showData
          this.total = data.pcPageDTO.total || 0
          this.$parent.getMsgRules()
        } else {
          this.$message.error(msg)
          // 报错时清空数据
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      })
    },
    handleDel (idx) {
      this.$confirm('是否删除该消息?', '提示', { type: 'warning' }).then(_ => {
        if (this.list[idx]) {
          if (this.list[idx].operatable) {
            this.list.splice(idx, 1)
          } else {
            const data = {
              msgSubscriptionUserNo: this.list[idx].msgSubscriptionUserNo,
              subscriptionUserType: 'BOSS_USER'
            }
            msgApi.delSubscriptionApi(data).then(res => {
              if (res.code === 200) {
                this.list.splice(idx, 1)
                this.total--
                this.$parent.getMsgRules()
              } else {
                this.$message.error(res.msg)
              }
            })
          }
        }
      }).catch(_ => {});
    },
    addline () {
      const firstItem = {
        msgRuleNo: '', // 规则类型编号
        msgRuleConditionDesc: '', // 消息描述
        operatable: true // 可操作
      }
      this.list.push(firstItem)
      this.$parent.getMsgRules()
    },
    handleMsgRuleChange(val) {
      const findItem = this.msgRuleNoOptions.find(item => {
        return item.value === val
      })
      this.list.map(item => {
        if (item.msgRuleNo === val && findItem) {
          item.msgRuleConditionDesc = findItem.msgRuleConditionDesc
        }
        return item
      })
    },
    handleSave (idx) {
      if (this.list[idx].msgRuleNo) {
        const data = {
          msgRuleNo: this.list[idx].msgRuleNo,
          noticeType: 'IN_MAIL',
          subscriptionUserType: 'BOSS_USER'
        }
        msgApi.postSubscriptionsApi(data).then(res => {
          if (res.code === 200) {
            this.$message.success('订阅成功')
            this.list[idx].operatable = false
            this.getSubscriptions()
          } else {
            this.$message.error(res.msg)
          }
        })
      } else {
        this.$message.error('消息类型必选')
      }
    },
    saveline () {
      //   this.$emit('update:showDialog', false)
    }
  }
}
</script>

<style scoped lang="scss">
.btnGroup {
  margin-top: 30px;
  text-align: center;
}
</style>
<style lang="scss">
.EditOrder-orderNum {
  input {
    text-align: center;
  }
}
</style>
