import moment from 'moment'
// 消息类型options
export const msgRuleNoOptions = []
// 消息状态options
export const msgRuleStatusOptions = [
  {
    label: '未处理',
    value: 0
  },
  {
    label: '已处理',
    value: 1
  }
]
// 页面搜索条件初始化
export function getInitSearchParams() {
  return {
    msgRuleNo: '', // 规则类型编号
    msgContent: '', // 消息描述
    msgStatus: '', // 消息状态
    dateRange: [moment().format('yyyy-MM-DD'), moment().format('yyyy-MM-DD')],
    createTimeBegin: moment().format('yyyy-MM-DD'),
    createTimeEnd: moment().format('yyyy-MM-DD')
  }
}
