// 消息中心-消息列表页面
<template>
  <div class="app-container backlog-search-container" >
    <div class="filter-container">
      <el-form
        ref="searchForm"
        :rules="rules"
        :model="searchForm"
        label-width="120px"
        label-position="right">
      <el-row>
        <el-col :span="7">
          <el-form-item label="消息类型：" prop="msgRuleNo">
            <el-select
              v-model="searchForm.msgRuleNo"
              filterable
              placeholder="请选择消息类型"
              clearable
            >
              <el-option
                v-for="item in msgRuleNoOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
             </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="消息描述：" prop="msgContent">
            <el-input
                v-model="searchForm.msgContent"
                placeholder="请输入消息描述"
                clearable
              />
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="消息状态：" prop="msgStatus">
            <el-select
              v-model="searchForm.msgStatus"
              filterable
              placeholder="请选择消息状态"
              clearable
            >
              <el-option
                v-for="item in msgRuleStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
             </el-select>
            </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item label="日期选择：" prop="dateRange">
            <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                align="right"
                unlink-panels
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="res => afterChangeDate(res, 'upload')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2">
          </el-col>
          <el-col :span="2">
            <el-button
              class="filterBtn"
              type="primary"
              icon="el-icon-search"
              @click="getList"
            >查询
            </el-button>
          </el-col>
          <el-col :span="2">
            <el-button
              icon="el-icon-refresh"
              @click="handleReset"
            >重置
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <el-row type="flex" justify="space-between" class="orderListTop">
      <el-col :span="2">
        <el-button type="primary"  @click="handleOperateList" :disabled="!multipleSections.length">
          消息处理
        </el-button>
      </el-col>
      <el-col :span="12" >
        <div style="display:flex;justify-content: flex-end;algin-items:center;">
          <div style="line-height:32px;margin-right:20px;">更新时间：{{update_time}}</div>
          <el-button
            type="primary"
            icon="el-icon-refresh-right"
            @click="getList"
            >刷新</el-button>
          <el-button
            type="primary"
            icon="el-icon-setting"
            @click="()=>{showSubscribeDialog = true}"
            >订阅</el-button>
          <el-button
            type="primary"
            icon="el-icon-download"
            @click="downloadFile"
          >导出</el-button>
        </div>
      </el-col>
    </el-row>
       <el-table ref="multipleTable" v-loading="listLoading"
      :data="list" border fit
      highlight-current-row height="500"
      :header-cell-style="{'text-align':'center'}"
      @selection-change="handleSelectionChange">
      <el-table-column
        type="selection"
        align="center"
        :selectable="selectable"
        width="55">
      </el-table-column>
      <el-table-column label="消息类型" min-width="100px" align="left" prop="msgRuleName"  width="200"/>
      <el-table-column label="消息描述"  align="left" >
        <template slot-scope="{ row }">
         <span v-html="row.msgContent"></span>
        </template>
      </el-table-column>
      <el-table-column label="消息时间"  align="left" prop="createTime" width="200"/>
      <el-table-column label="消息状态" align="center" width="150" fixed="right" class-name="small-padding" >
        <template slot-scope="{ row }">
          <el-button type="danger" size="mini" @click="toDtl(row)" v-if="row.msgRead === 0" >未处理</el-button>
          <el-button type="success" size="mini" @click="toDtl(row)" v-if="row.msgRead === 1">已处理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="listQuery.page"
      :limit.sync="listQuery.size"
      layout="total, prev, pager, next, jumper"
      @pagination="getList"
    />
     <SubscribeDialog
      :show-dialog.sync="showSubscribeDialog"
      :msgRuleNoOptions="msgRuleNoOptions"
    />
  </div>
</template>

<script>
import Pagination from '@/components/Pagination'
import SubscribeDialog from './components/SubscribeDialog'
import {
  getInitSearchParams,
  msgRuleStatusOptions
} from './constants.js'
import * as msgApi from '@/api/messageCenter'
export default {
  name: 'centerList',
  computed: {},
  data() {
    return {
      searchForm: {}, // 绑定搜索参数
      rules: {},
      msgRuleNoOptions: [],
      msgRuleStatusOptions,
      update_time: '',
      listLoading: false,
      showSubscribeDialog: false,
      list: [],
      multipleSections: [],
      responseList: [],
      total: 0,
      listQuery: {
        page: 1,
        size: 20
      }
    };
  },
  components: {
    Pagination,
    SubscribeDialog
  },
  mounted() {
    this.searchForm = getInitSearchParams()
    if (this.$route.query.ruleNo) {
      this.searchForm.msgRuleNo = this.$route.query.ruleNo
    }
    this.getList()
    this.getMsgRules()
  },
  methods: {
    selectable(row) {
      return row.msgRead === 0
    },
    // 重置
    handleReset() {
      this.searchForm = getInitSearchParams()
    },
    // 消息处理
    handleOperateList() {
      this.$confirm(`已勾选${this.multipleSections.length}行消息，是否确认进行处理`, '提示', { type: 'warning' }).then(_ => {
        const msgDetailNoList = this.multipleSections.map((item) => {
          return {
            msgDetailNo: item.msgDetailNo,
            msgSubscriptionUserNo: item.msgSubscriptionUserNo
          }
        })
        msgApi.operateListApi({ msgDetailNoList }).then(res => {
          if (res.code === 200) {
            this.$message.success(res.data)
            this.getList()
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(_ => {
      });
    },
    deleteEmptyData(data) {
      for (let item in data) {
        if (data[item] === '') {
          delete data[item]
        }
        if (item === 'dateRange') {
          delete data.dateRange
        }
      }
      return data
    },
    // 导出
    downloadFile() {
      this.$confirm('是否要导出相应数据', '提示', { type: 'warning' }).then(_ => {
        const msgDetailNoList = this.multipleSections.map((item) => {
          return item.msgDetailNo
        })
        const data = msgDetailNoList.length < 1 ? { ...this.searchForm } : { msgDetailNoList }
        msgApi.exportApi(this.deleteEmptyData(data)).then(res => {
          if (res.code === 200) {
            this.$message.success('导出成功')
          } else {
            this.$message.error(res.msg)
          }
        })
      }).catch(_ => {
      });
    },
    // 获取消息类型
    getMsgRules() {
      msgApi.getRulesApi().then(response => {
        const { code, data, msg } = response
        if (code === 200 && data.data) {
          this.msgRuleNoOptions = data.data.map((item) => {
            return {
              value: item.msgRuleNo,
              label: item.msgRuleTypeName,
              msgRuleConditionDesc: item.msgRuleConditionDesc,
              subscription: item.subscription
            }
          })
        } else {
          this.$message.error(msg)
        }
      })
    },
    afterChangeDate(res, type) {
      const [ min, max ] = res || []
      this.searchForm.createTimeBegin = min
      this.searchForm.createTimeEnd = max
    },
    // 获取消息列表
    getList () {
      this.listLoading = true
      const data = {
        ...this.searchForm,
        pageNo: this.listQuery.page,
        pageSize: this.listQuery.size
      }
      msgApi.getMsgListApi(this.deleteEmptyData(data)).then(res => {
        const { code, data, msg } = res
        if (code === 200 && data) {
          this.list = data.msgList.data
          this.total = data.msgList.total || 0
          this.update_time = data.refreshTime
        } else {
          this.$message.error(msg)
          // 报错时清空数据
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      })
    },
    handleSelectionChange (sections) {
      this.multipleSections = sections
    },
    // 消息处理
    toDtl (row) {
      const data = {
        msgDetailNo: row.msgDetailNo,
        operation: row.msgRead === 0 ? 1 : 0
      }
      msgApi.operateApi(data).then(res => {
        if (res.code === 200) {
          this.$message.success('操作成功！')
          const index = this.list.findIndex((item) => {
            return item.msgDetailNo === row.msgDetailNo
          })
          this.list[index].msgRead = data.operation
        } else {
          this.$message.error(res.msg)
        }
      })
    }
  }
}
</script>
<style lang="scss" scope>
.backlog-search-container .el-loading-mask{
  background-color: rgba(255, 255, 255, 1);
}
.filterBtn {
  margin-left: 30px;
}
.orderListTop {
  margin: 10px;

  .el-col:nth-child(1) {
    display: flex;
    align-items: center;

    span {
      margin: 0 10px;
      // color: #909399;
    }
  }

  .el-col:nth-child(2) {
    text-align: right;
  }
}
</style>
<style lang="scss">
.backlog-search-container {
  .pagination-container {
    text-align: center;
  }
  .filter-container {
    padding-top: 18px;
    background-color: #f4f4f4;
  }
  .tag-container {
    padding-left: 10px;
    padding-bottom: 5px;
  }
  .el-autocomplete,
  .el-select {
    width: 100%;
  }
  .el-range-editor.el-input__inner{
    width: 100%;
  }
  .line {
    text-align: center;
  }
  .backlog-search-result-container {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
    margin: 10px 0;
    & > {
      .result-title {
        height: 50px;
        padding: 0 10px 0 20px;
        line-height: 50px;
        font-size: 16px;
      }
    }
  }
  .el-form-item__content {
    .el-form-item--small.el-form-item {
      margin-bottom: 0;
    }
  }
}

</style>
