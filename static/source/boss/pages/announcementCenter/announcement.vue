<template>
  <div class="announcement">
    <div class="content">
      <h2>公告内容</h2>
      <el-input
        type="textarea"
        :autosize="{ minRows: 6, maxRows: 20}"
        placeholder="请输入内容"
        v-model="textarea">
      </el-input>
      <el-button class="save-button" @click="saveAnnouncement">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  saveAnnouncement,
  getAnnouncement
} from '@/api/announcement.js'
export default {
  name: 'announcement',
  data () {
    return {
      textarea: ''
    }
  },
  methods: {
    saveAnnouncement () {
      if (!this.textarea) {
        this.textarea = null
        this.$message.info('此操作将清空公告！')
      }
      const data = {
        publishContent: this.textarea
      }
      saveAnnouncement(data)
        .then(res => {
          if (res && res.code === 200) {
            this.$message.success('保存成功！')
          } else {
            this.$message.error('保存失败！')
          }
        })
        .catch(err => {
          this.$message.error(err.msg || err.message || '保存失败！')
        })
    }
  },
  mounted () {
    getAnnouncement()
      .then(res => {
        if (res && res.code === 200) {
          if (res.data) {
            this.textarea = res.data.publishContent
          }
        } else {
          this.$message.error('获取公告失败！')
        }
      })
      .catch(err => {
        this.$message.error(err.msg || err.message || '获取公告失败！')
      })
  }
}
</script>

<style lang="scss" scoped>
.announcement{
  max-width: 800px;
  margin: auto;
  margin-top: 20px;
  .content{
    display: flex;
    padding: 20px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    h2{
      margin-bottom: 20px;
    }
    .save-button{
      margin-top: 20px;
      align-self: flex-end;
    }
  }
}
</style>
