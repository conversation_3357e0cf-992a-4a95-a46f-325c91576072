
const { client, helper } = require('@tool/webpack-basic-common')
const env = require('@server/env')
const { merge, resolve } = helper

helper
  .set('baseDir', __dirname)
  .set('preset', 'vue')
  .set('patch', (rules) => {
    client.module.rules.push(rules.eslint)
  })
// 基于 @server/core 项目使用以下方法配置入口，并移除 merge 中的配置
helper.set('entryInKoa', ['app', 'callCenter', 'iframeBridge', 'dataCenterControl'])

// merge webpack config
module.exports = merge(client, {
  resolve: {
    alias: {
      '@': resolve(__dirname, './')
    }
  },
  devtool: env.RELEASE ? false : 'source-map'
})
