import { Vue, Vuex } from '@client/boss'

// global state
import state from './state'
import actions from './action'
import mutations from './mutation'
import getters from './getter'

import orderCommon from './module/orderSale/common'
import orderDetail from './module/orderSale/detail'
import orderGoods from './module/orderSale/goods'
import construction from './module/construction'
import inventoryManagement from './module/inventoryManagement'

import orderPurchase from './module/orderPurchase/common'

import mrp from './module/mrp'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    mrp,
    orderCommon,
    orderDetail,
    orderGoods,
    orderPurchase,
    inventoryManagement,
    construction
  },
  state,
  actions,
  mutations,
  getters
})

export default store
