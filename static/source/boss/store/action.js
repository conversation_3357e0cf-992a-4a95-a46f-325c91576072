import axios from 'axios'
import { appId } from '@/utils/config.js'
// const generateAction = (url, mutation) => {
//   return async ({commit}, data) => {
//     const res = await api.get(url, data)
//     console.log(url, res)
//     if(mutation) commit(mutation, res)
//     return res
//   }
// }
const sapStartHost = /^(http(s?):)?(\/\/)?sap/
const hasQuery = /\?[^]+/
const username = window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name
// const setOuterMenu = (menu) => {
//   const isLocal = /local|test|uat/.test(location.href)
//   if (!isLocal) return
//   const so = menu.find(item => item.name === '销售跟单')
//   const item = {
//     appClientId: null,
//     appId: null,
//     children: [],
//     icon: 'circle-plus',
//     id: 6998,
//     link: 'https://boss-uat-4.zkh360.com/so/csWorkbench?open=true',
//     name: '客服工作台',
//     parentId: null,
//     type: 'MENU',
//     urls: []
//   }
//   so.children.unshift(item)
// }
const handleSapMenu = (menu) => {
  menu.forEach(item => {
    if (item.type === 'MENU' && sapStartHost.test(item.link)) {
      const len = item.link.length
      const hash = item.link[len - 1] === '#'
      if (hash) item.link = item.link.slice(0, len - 1)
      if (hasQuery.test(item.link)) {
        item.link += `&userid=${encodeURIComponent(Buffer.from(username).toString('base64'))}`
      } else {
        item.link += `?userid=${encodeURIComponent(Buffer.from(username).toString('base64'))}`
      }
      console.log('set menu:', item.link)
    }
    if (Array.isArray(item.children) && item.children.length) {
      handleSapMenu(item.children)
    }
  })
}
export default {
  async getMenu ({ commit }, data) {
    let res
    let menu
    try {
      // https://security-service-uat.zkh360.com/resources?appId=277&level=3
      res = await axios.get(`/security-api/resources?appId=${appId}&level=3`)
    } catch (err) {
      console.error('ERROR: menu', err)
      alert('ERROR: Failed to get menu data')
    }

    if (res && res.status === 200) {
      menu = res.data && res.data.children
    }
    handleSapMenu(menu)
    // setOuterMenu(menu)
    // console.log(setOuterMenu)
    commit('setMenuLoaded', true)
    commit('setMenu', menu)
    console.log('set menu', menu)
    return menu
  },
  async getUserRole ({ commit }) {
    let res
    try {
      res = await axios.get('/internal-api/user')
    } catch (err) {
      console.error('ERROR: menu', err)
      alert('ERROR: Failed to get user role')
    }
    if (res && res.status === 200 && res.data && res.data.code === 200) {
      res = res.data
      const { roleInfoList, email, id } = res.data
      if (roleInfoList) {
        let roles = roleInfoList.map(item => item.name) || []
        console.log('roles:', roles)
        commit('setUserRole', roles)
      }
      if (email) {
        commit('setUserEmail', email)
      }
      if (id) {
        commit('setUserID', id)
      }
    }
    return res
  }
}
