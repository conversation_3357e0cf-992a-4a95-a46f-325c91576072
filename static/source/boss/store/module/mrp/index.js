import { setOrder<PERSON>rans<PERSON>, getOrderTransfer } from '@/storage/index'
import { productGroupList, getCompanyInfo, getMrpArea } from '@/api/mrp'

const key = 'summary-orderTransfer'

const state = {
  transferList: getOrder<PERSON>ran<PERSON><PERSON>(key),
  productList: [],
  companyInfoList: [],
  mrpAreaList: []
}

const mutations = {
  setTransferList (state, value) {
    state.transferList = value
    setOrderTransfer(key, value)
  },
  FETCH_PRODUCT_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.productList = data
    }
  },
  FETCH_COMPANY_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.companyInfoList = data
    }
  },
  FETCH_MRPAREA_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.mrpAreaList = data
    }
  }
}

const actions = {
  queryProductGroup ({ commit }) {
    return productGroupList().then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_PRODUCT_LIST', result)
        return result
      }
    })
  },
  queryCompanyInfoList ({ commit }) {
    return getCompanyInfo().then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_COMPANY_LIST', result)
        return result
      }
    })
  },
  queryMrpAreaList ({ commit }) {
    return getMrpArea({ mrpArea: '' }).then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_MRPAREA_LIST', result)
        return result
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
