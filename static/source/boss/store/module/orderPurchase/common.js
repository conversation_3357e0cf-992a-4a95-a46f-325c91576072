import {
  getDictionaryList, getCompanyAndFactory, getFactoryWarehouse,
  getAllPurchaseGroup, getPoFields, getInventoryTypes,
  searchMaterialGroup, getWarehouseConfig, getMMAuth, getTrackingOrder, getDisableCompanyListConfig, getDeleteReason,
  getProductPosition
} from '@/api/mm'
import { getFactoryList } from '@/utils/mm'

const baseState = {
  'Z001': [],
  'Z002': [],
  'Z003': [],
  'Z004': [],
  'Z005': [],
  'Z006': [],
  'Z007': [],
  'Z008': [],
  'Z009': [],
  'Z010': [],
  'Z011': [],
  'Z012': [],
  'Z013': [],
  'Z014': [],
  'Z015': []
}

const simpleClone = (source) => JSON.parse(JSON.stringify(source))

const state = {
  dictList: {},
  authConfig: {},
  trackingOrderConfig: {},
  companyFactoryList: [],
  disableCompanyCodeList: [],
  factoryList: [],
  warehouseList: [],
  purchaseList: [],
  inventoryTypes: [],
  searchMaterialGroupList: [],
  defaultWarehouseConfigList: [],
  storeWarehouseListFilterByFactory: [],
  create: { ...simpleClone(baseState) },
  edit: { ...simpleClone(baseState) },
  detail: { ...simpleClone(baseState) },
  costCenterList: [],
  deleteReasonOptions: [],
  productPositionOptions: []
}

const mutations = {
  SET_MM_AUTH: (state, { enable }) => {
    state.authConfig = { enable }
  },
  SET_TRACKING_ORDER: (state, data) => {
    state.trackingOrderConfig = data
  },
  SET_DELETE_REASON: (state, data) => {
    state.deleteReasonOptions = data
  },
  SET_PRODUCT_POSITION: (state, data) => {
    state.productPositionOptions = data && data.length > 0 ? data.map(item => {
      return {
        name: item.name,
        value: item.name
      }
    }) : data
  },
  SET_PO_FIELDS: (state, { orderType, routerType, res }) => {
    if (res.every(item => item.sequence != null)) {
      res = res.sort((a, b) => a.sequence - b.sequence)
    }
    res = res.map(item => ({
      ...item,
      required: Boolean(item.required),
      disabled: Boolean(item.disabled)
    }))
    const reg = new RegExp(routerType, 'i')
    res = res.filter(item => !item.status || reg.test(item.status))
    state[routerType][orderType] = res
  },
  FETCH_DICT_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      const dictList = {}
      data.forEach((item, idx) => {
        if (!dictList[item.type]) {
          dictList[item.type] = []
        }
        if (item.type === 'orderReason') {
          if (item.description === '申请下单原因') {
            dictList[item.type].push(item)
          }
        } else if (item.type === 'customsFeeType' || item.type === 'intlShippingType' || item.type === 'orderChannel') {
          item.value = Number(item.value)
          dictList[item.type].push(item)
        } else {
          dictList[item.type].push(item)
        }
      })
      dictList.customsFeeType = dictList.customsFeeType.sort((a, b) => a.value - b.value)
      dictList.intlShippingType = dictList.intlShippingType.sort((a, b) => a.value - b.value)
      state.dictList = dictList
    }
  },
  FETCH_COMPANY_FACTORY_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.companyFactoryList = data
      state.factoryList = getFactoryList(data)
    }
  },
  FETCH_DISABLE_COMPANY_CODE_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.disableCompanyCodeList = data
    }
  },
  FETCH_WAREHOUSE_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.warehouseList = data
    }
  },
  FETCH_PURCHASE_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.purchaseList = data
    }
  },
  FETCH_INVENTORYTYPE_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      state.inventoryTypes = data
    }
  },
  FETCH_SEARCH_MATERIAlGROUP: (state, data) => {
    if (data && Array.isArray(data)) {
      state.searchMaterialGroupList = data
    }
  },
  FETCH_DEFAULT_WAREHOUSE_CONFIG: (state, data) => {
    if (data && Array.isArray(data)) {
      state.defaultWarehouseConfigList = data
    }
  },
  SET_WAREHOUSE_FILTERBY_COMPANYCODE: (state, data) => {
    if (data.warehouseListFilterByFactory && Array.isArray(data.warehouseListFilterByFactory)) {
      state.storeWarehouseListFilterByFactory = data.warehouseListFilterByFactory
    }
  },
  SET_COST_CENTER: (state, data) => {
    if (data && data.costCenterList && Array.isArray(data.costCenterList)) {
      state.costCenterList = data.costCenterList
    }
  }
}

const actions = {
  // 获取PO字段定义 orderType -> Z001...Z015, routerType -> create/edit/detail
  getPoFields ({ commit }, { orderType, routerType }) {
    if (!orderType || !routerType) {
      throw Error('getPoFields orderType, routerType needed!')
    }
    return getPoFields(orderType, routerType)
      .then(res => {
        if (res) {
          commit('SET_PO_FIELDS', { orderType, routerType, res })
        }
      })
  },
  queryDictList ({ commit }, data) {
    return getDictionaryList(data).then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_DICT_LIST', result)
      }
    })
  },
  queryCompanyAndFactoryList ({ commit }) {
    return getCompanyAndFactory().then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_COMPANY_FACTORY_LIST', result)
        return result
      }
    })
  },
  queryDisableCompanyCodeList ({ commit }) {
    return getDisableCompanyListConfig().then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_DISABLE_COMPANY_CODE_LIST', result)
        return result
      }
    })
  },
  queryFactoryWarehouseList ({ commit }) {
    return getFactoryWarehouse().then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_WAREHOUSE_LIST', result)
      }
    })
  },
  queryPurchaseGroup ({ commit }) {
    return getAllPurchaseGroup().then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_PURCHASE_LIST', result)
        return result
      }
    })
  },
  getInventoryTypes({ commit }) {
    return getInventoryTypes().then(result => {
      if (result.result && Array.isArray(result.result.list)) {
        commit('FETCH_INVENTORYTYPE_LIST', result.result.list)
        return result
      }
    })
  },
  getSearchMaterialGroup({ commit }) {
    searchMaterialGroup().then((result) => {
      if (result && Array.isArray(result)) {
        commit('FETCH_SEARCH_MATERIAlGROUP', result)
        return result
      }
    })
  },
  getMMAuthConfig({ commit }) {
    getMMAuth().then((result) => {
      if (result) {
        commit('SET_MM_AUTH', result)
        return result
      }
    })
  },
  getTrackingOrderConfig({ commit }) {
    getTrackingOrder().then((result) => {
      if (result) {
        commit('SET_TRACKING_ORDER', result)
        return result
      }
    })
  },
  getDeleteReasonOptions({ commit }) {
    getDeleteReason().then((result) => {
      if (result) {
        commit('SET_DELETE_REASON', result.itemList)
        return result
      }
    })
  },
  getProductPositionOptions({ commit }) {
    getProductPosition().then((result) => {
      if (result) {
        commit('SET_PRODUCT_POSITION', result.data)
        return result
      }
    })
  },
  queryWarehouseConfigList({ commit }) {
    getWarehouseConfig().then(result => {
      if (result && Array.isArray(result)) {
        commit('FETCH_DEFAULT_WAREHOUSE_CONFIG', result)
        return result
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
