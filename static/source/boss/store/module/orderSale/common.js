import { getDictVersion, listDict, getCustomizeOrderConfig, getMorePositionDisabledConfig, getOrderServiceMap, getTemplateExcelUrls, getOpsAuthorization, getSearchSkuSwitch } from '@/api/orderSale'
import { deliverySwitch } from '@/api/orderDelivery'
import { patchOrderServiceOptions } from '@/utils/orderService';
import { getPoTemplateExcelUrls, getAcceptFileType } from '@/api/mm'
const state = {
  dictList: {},
  orderServiceDict: {},
  excelUrls: {},
  poExcelUrls: {},
  acceptFileType: {},
  shipping200UseOld: true,
  customizeOrder: true,
  morePositionDisabled: true,
  sapReturnOrderValidator: false, // 虚拟调账订单关联sap退货单，是否在白名单
  searchSkuSwitch: false // 切换查询sku接口的开关
}

const mutations = {
  FETCH_DICT_LIST: (state, data) => {
    if (data && Array.isArray(data)) {
      const dictList = {};
      data.forEach((item, idx) => {
        if (!dictList[item.listKey]) {
          dictList[item.listKey] = [];
        }
        dictList[item.listKey].push(item);
      });
      state.dictList = dictList;
    }
  },
  FETCH_ORDER_SERVICE_DICT: (state, data) => {
    state.orderServiceDict = data;
  },
  SET_SHIP_FLAG: (state, data) => {
    if (data != null) {
      state.shipping200UseOld = data
    }
  },
  SET_CUSTOMIZE_ORDER: (state, data) => {
    if (data != null) {
      state.customizeOrder = data
    }
  },
  SET_MORE_POSITION_DISABLED: (state, data) => {
    if (data != null) {
      state.morePositionDisabled = data
    }
  },
  SET_TEMPLATE_EXCEL_URLS: (state, data) => {
    if (data != null) {
      state.excelUrls = data
    }
  },
  SET_PO_TEMPLATE_EXCEL_URLS: (state, data) => {
    if (data != null) {
      state.poExcelUrls = data
    }
  },
  SET_ACCEPT_FILE_TYPE: (state, data) => {
    if (data != null) {
      state.acceptFileType = data
    }
  },
  SET_SAP_RETURN_ORDER_VALIDATOR: (state, data) => {
    if (data?.length) {
      state.sapReturnOrderValidator = true
    }
  },
  SET_SEARCH_SKU_SWITCH: (state, data) => {
    state.searchSkuSwitch = data;
  }
};

async function getDictList(commit, data) {
  const dictData = await listDict(data);
  if (dictData && dictData.code === 200) {
    localStorage.setItem('OMS_DICT_VALUES_DATA', JSON.stringify(dictData.data));
    commit('FETCH_DICT_LIST', dictData.data);
  }
}

const actions = {
  /**
   * 逻辑梳理
   * 1、请求接口：/v1/dict/values/values/version 获取当前字典时间戳；
   * 2、对比接口返回的时间戳与 localStorage 存储的时间戳是否一致；
   *    2.1、若一致，则判断字典数据是否存在 localStorage 中；
   *        2.1.1、如果存在，则 commit；
   *        2.1.2、如果不存在，则请求字典接口并存入 localStorage；
   *    2.2、若不一致，则请求请求字典接口并存入 localStorage，同时时间戳信息也存入 localStorage；
   *
   * @param {*} { commit, state }
   * @param {*} data
   * @return {*}
   */
  async queryDictList({ commit }, data) {
    const versionRes = await getDictVersion();
    if (versionRes.code === 200) {
      const dictVersion = versionRes.data && versionRes.data.toString();
      const localDictVersion = localStorage.getItem('OMS_DICT_VALUES_VERSION');
      const localDictData = localStorage.getItem('OMS_DICT_VALUES_DATA');
      if (dictVersion === localDictVersion) {
        if (localDictData) {
          let parsedDictData;
          try {
            parsedDictData = JSON.parse(localDictData);
            commit('FETCH_DICT_LIST', parsedDictData);
            return Promise.resolve(parsedDictData);
          } catch {
            getDictList(commit, data);
          }
        } else {
          return getDictList(commit, data);
        }
      } else {
        localStorage.setItem('OMS_DICT_VALUES_VERSION', dictVersion);
        return getDictList(commit, data);
      }
    } else {
      return getDictList(commit, data);
    }
  },
  async getOrderServiceDict({ commit }) {
    const res = await getOrderServiceMap();
    if (res.code === 200) {
      commit('FETCH_ORDER_SERVICE_DICT', patchOrderServiceOptions(res.data) || {});
    }
  },
  getShipFlag ({ commit }, data) {
    return deliverySwitch().then(result => {
      console.log(result)
      if (result && result.code === 200) {
        commit('SET_SHIP_FLAG', result.data)
        return result
      }
    })
  },
  getCustomizeOrderConfig ({ commit }, data) {
    return getCustomizeOrderConfig().then(result => {
      console.log(result)
      if (result) {
        commit('SET_CUSTOMIZE_ORDER', result.enable)
        return result
      }
    })
  },
  getMorePositionDisabledConfig ({ commit }, data) {
    return getMorePositionDisabledConfig().then(result => {
      console.log(result)
      if (result) {
        commit('SET_MORE_POSITION_DISABLED', result.enable)
        return result
      }
    })
  },
  getTemplateExcelUrls ({ commit }, data) {
    Promise.all([getTemplateExcelUrls(), getPoTemplateExcelUrls(), getAcceptFileType()]).then(([res1, res2, res3]) => {
      console.log('getTemplateExcelUrls', res1, res2, res3);
      if (res1) commit('SET_TEMPLATE_EXCEL_URLS', res1)
      if (res2) commit('SET_PO_TEMPLATE_EXCEL_URLS', res2)
      /**
       * res3: {
       *  commonType: '',
       *  poCommonType: '',
       *  soCommonType: ''
       * }
       */
      if (res3) commit('SET_ACCEPT_FILE_TYPE', res3)
    })
    // getTemplateExcelUrls().then(result => {
    //   console.log(result)
    //   if (result) {
    //     commit('SET_TEMPLATE_EXCEL_URLS', result)
    //     return result
    //   }
    // })
  },
  getSapReturnOrderValidator ({ commit }, data) {
    const params = {
      authKey: 'whiteOperateUser',
      authType: 'whitelist',
      authValue: window.CUR_DATA && window.CUR_DATA.user && window.CUR_DATA.user.name,
      businessType: 'SapReturnOrderValidator',
      domain: 'SO'
    }
    return getOpsAuthorization(params).then(result => {
      if (result) {
        commit('SET_SAP_RETURN_ORDER_VALIDATOR', result.data)
        return result
      }
    })
  },
  getSearchSkuSwitch({ commit }) {
    getSearchSkuSwitch().then((result) => {
      if (result.code === 200) {
        commit('SET_SEARCH_SKU_SWITCH', result.data)
        return result
      }
    })
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
