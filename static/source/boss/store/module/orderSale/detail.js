import * as orderDetail from '@/api/orderSale'

const state = {
  sku: null
}

const mutations = {
  FETCH_SKU_INFO: (state, data) => {
    state.sku = data || {}
  }
}

const actions = {
  getSkuInfo ({ commit }, skuNo) {
    return orderDetail.skuInfo(skuNo).then(result => {
      if (result && result.code === 200) {
        commit('FETCH_SKU_INFO', result.data)
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
