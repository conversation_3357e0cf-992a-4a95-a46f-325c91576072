import endsWith from 'lodash/endsWith'
import * as shortid from 'shortid'
import * as createOrder from '@/api/orderSale'
import { MessageBox } from 'element-ui'
import { formatPrice } from '@/utils'
import { isServiceOrder, isFreeOrder, isForecastOrder } from '@/utils/orderType'
import { isString, cloneDeep } from 'lodash'

const calSkuAmount = (skuList, isTax) => {
  const result = skuList.reduce(
    (acc, sku) => {
      const { taxRate, discountAmount } = sku
      const dp = parseFloat(discountAmount || 0)
      const newValue = {}
      const taxPriceRowTotal = sku.taxPrice * sku.quantity
      const freeTaxPriceRowTotal = sku.freeTaxPrice * sku.quantity
      newValue.taxedTotal = taxPriceRowTotal + acc.taxedTotal
      newValue.unTaxedTotal = freeTaxPriceRowTotal + acc.unTaxedTotal
      if (isTax === '0') {
        newValue.unTaxedDiscountTotal = acc.unTaxedDiscountTotal + freeTaxPriceRowTotal - dp
        newValue.taxedDiscountTotal = acc.taxedDiscountTotal + taxPriceRowTotal - dp * (1 + taxRate)
      }
      if (isTax === '1') {
        newValue.taxedDiscountTotal = acc.taxedDiscountTotal + taxPriceRowTotal - dp
        newValue.unTaxedDiscountTotal = acc.unTaxedDiscountTotal + freeTaxPriceRowTotal - (dp / (1 + taxRate))
      }
      return newValue
    },
    { taxedTotal: 0, taxedDiscountTotal: 0, unTaxedTotal: 0, unTaxedDiscountTotal: 0 })
  return result
}

const setTotal = (state, skus, key, isTax) => {
  const { taxedTotal, unTaxedTotal, taxedDiscountTotal, unTaxedDiscountTotal } = calSkuAmount(skus, isTax)
  state.taxedTotalAmount = {
    ...state.taxedTotalAmount,
    [key]: taxedTotal
  }
  state.untaxedTotalAmount = {
    ...state.untaxedTotalAmount,
    [key]: unTaxedTotal
  }
  state.taxedDiscountTotal = {
    ...state.taxedDiscountTotal,
    [key]: taxedDiscountTotal
  }
  state.unTaxedDiscountTotal = {
    ...state.unTaxedDiscountTotal,
    [key]: unTaxedDiscountTotal
  }
}

const setStore = (state, skus, key, isTax) => {
  state.skuList = {
    ...state.skuList,
    [key]: skus
  }
  setTotal(state, skus, key, isTax)
}

const findLastLineNo = (currentSkuList) => {
  let currentLineNo = 10
  const itemLen = (currentSkuList || []).length
  if (itemLen > 0) {
    const lastItem = currentSkuList[itemLen - 1]
    currentLineNo = (lastItem.idx + 10) || 10
  }
  return currentLineNo
}

const getDefaultDirectDeliverySupplier = (orderData, orderType, dictList) => {
  if (orderData) {
    const { selectedSalesRange } = orderData
    if (selectedSalesRange) {
      const salesOrganization = selectedSalesRange.salesOrganization
      if (orderType && salesOrganization) {
        const code = `${orderType}_${salesOrganization}`
        const dict = dictList['orderTypeOfDirectDeliverySupplierList'].find(item => {
          return item.parentCode === code
        })
        if (dict) {
          return '2'
        }
      }
    }
  }
  return '0'
}

function setAggreementPrice (sku, currency, exchangeRate) {
  const { customerPriceTax, customerPrice } = sku
  if (customerPriceTax != null) {
    if (customerPriceTax === '0' || customerPriceTax === 0) {
      sku.freeTaxPrice = formatPrice(customerPrice)
      sku.taxPrice = formatPrice(customerPrice / (1 + sku.taxRate))
    } else {
      sku.taxPrice = formatPrice(customerPrice)
      sku.freeTaxPrice = formatPrice(customerPrice * (1 + sku.taxRate))
    }
  }
  sku._taxPrice = sku.taxPrice
  sku._freeTaxPrice = sku.freeTaxPrice
  if (currency !== 'CNY') {
    // sku.taxPrice /= exchangeRate
    // sku.freeTaxPrice /= exchangeRate
    sku.taxPrice = formatPrice(sku.taxPrice / exchangeRate)
    sku.freeTaxPrice = formatPrice(sku.freeTaxPrice / exchangeRate)
    // sku.taxAmount /= exchangeRate
  }
}

const formatSku = async ({
  startIdx,
  idx,
  data,
  orderType,
  orderData,
  dictList,
  salesRange,
  company,
  factoryList
}) => {
  const { skuNo, quantity, deliveryDateStr,
    directDeliverySupplier, price,
    customerMaterialNo, recentDeliveryDate,
    deliveryFrequency, deliveryCycle
  } = data
  let { position } = data
  if (position === '-1') {
    position = parseInt(position, 10)
  }
  const { isTax, exchangeRate, currency, urgent, customerDateSensitive, taxType, autoBatching, customerReferenceDate, bidCustomer } = orderData
  const sku = data
  sku.idx = (startIdx + idx * 10) || 10
  sku.urgent = urgent
  sku.customerDateSensitive = customerDateSensitive
  sku.uuid = shortid.generate()
  sku.customerMaterialNo = customerMaterialNo || ''
  sku.customerMaterialQuantity = data && data.customerMaterialQuantity
    ? data.customerMaterialQuantity : 0
  sku.customerMaterialUnit = data && data.customerMaterialUnit
    ? data.customerMaterialUnit : ''
  sku.quantity = quantity || 0
  // 单位
  sku.packageUnit = ''
  // 备注
  sku.memo = ''
  // 客户行号
  sku.customerLine = ''
  // 领用人
  sku.recipient = ''
  // 需求部门
  sku.demandDepartment = sku.demandDepartment || ''
  // 直发备注
  sku.sendWarehouseMemo = ''
  // 选择直发
  sku.directDeliverySupplier = directDeliverySupplier ||
    getDefaultDirectDeliverySupplier(orderData, orderType, dictList)
  // 拉取货币列表
  sku.currency = orderData.currency
  // dictList['currency'] ? dictList['currency'][0] : null

  // 首个交期
  sku.customerDate = deliveryDateStr || orderData.orderDate
  // 可发货日期
  sku.deliveryDate = ''
  sku.skuArrivalDate = '' // 标准送达日期
  sku.sysDeliveryDate = '' // 标准发货日期
  // 是否接受标期
  sku.refuseSystemDeliveryDate = ''
  if (autoBatching !== 'X') {
    sku.customerDate = customerReferenceDate || sku.customerDate || ''
  }
  if (/z001/gim.test(orderType) && (bidCustomer === 'X' || bidCustomer === '8')) {
    sku.recentDeliveryDate = recentDeliveryDate
    sku.deliveryFrequency = deliveryFrequency
    sku.deliveryCycle = deliveryCycle
  }
  // 处理单位
  if (sku) {
    const { unitCode, unitName, skuNo } = sku
    if (unitCode && unitName) {
      sku.packageInfoList = [{
        skuNo,
        unitName,
        ruleDes: `1${unitName}/${unitName}`,
        conversion: 1,
        unit: unitCode
      }]
    }
  }
  const { unitCode, factoryProductPriceVOMap } = sku
  sku.quantityUnit = unitCode
  // 拉取工厂列表
  if (factoryList) {
    let factoryFlag = false
    if (sku.factory && isString(sku.factory)) {
      const factoryInt = parseInt(sku.factory, 10)
      if (factoryProductPriceVOMap && factoryProductPriceVOMap[factoryInt] && factoryProductPriceVOMap[factoryInt].taxRate != null) {
        sku.factory = factoryList.find(item => item.code === sku.factory)
        factoryFlag = true
      }
    }
    if (!factoryFlag) {
      let cmy = company
      if (company === '1300') {
        cmy = '1000'
      }
      factoryList.forEach(fa => {
        if (fa && fa.code) {
          const code = parseInt(fa.code, 10)
          if (orderData.selectedSalesRange.salesOrganization === '6001' && parseInt(sku.factoryPriorityList[0], 10) === code) {
            sku.factory = fa
          }
          if (orderData.selectedSalesRange.salesOrganization !== '6001' && factoryProductPriceVOMap[code] && factoryProductPriceVOMap[code].taxRate != null) {
            if (!(sku.factory && sku.factory.code)) {
              sku.factory = fa
            }
            if (sku.factory && sku.factory.code && fa.code === cmy) {
              sku.factory = fa
            }
          }
        }
      })
    }
    if (sku.factory && !sku.factory.code) {
      throw new Error(`该商品${skuNo}在指定工厂下未维护建议销售价或税率，或是无效商品编码，请重新调后再次上传`)
    }
  }

  // 拉取库位
  if (sku.factory) {
    let positionList = dictList['position'].filter(item => {
      if (isServiceOrder(orderType) || sku.mtart === 'Z002') {
        return item.parentCode === sku.factory.code && endsWith(String(item.code), '04')
      }
      return item.parentCode === sku.factory.code && item.code !== -1
    })
    const facCode = String(sku.factory.code)
    // 预测性销售订单不支持自动调仓
    if (sku.directDeliverySupplier === '2' &&
      !isForecastOrder(orderType) &&
      orderType !== 'Z014' &&
      orderType !== 'ZEV1' &&
      orderType !== 'ZEV2' &&
      orderType !== 'ZEV3' &&
      orderType !== 'ZEV4') {
      positionList.unshift({
        code: -1,
        name: '自动挑仓'
      })
      sku.position = position || -1
    } else {
      sku.position = position || ''
    }
    sku.positionList = positionList
    if (facCode) {
      const fcode = parseInt(facCode, 10)
      if (fcode && factoryProductPriceVOMap[fcode]) {
        const { taxRate, taxRateInPoint } = factoryProductPriceVOMap[fcode]
        let { suggestPrice } = factoryProductPriceVOMap[fcode]
        if (!suggestPrice) {
          suggestPrice = 0
        }
        sku.suggestPrice = currency === 'CNY' ? formatPrice(suggestPrice) : formatPrice(suggestPrice / exchangeRate)
        if (taxRate != null && taxRateInPoint != null) {
          sku.taxRate = taxType === '0' ? 0 : taxRateInPoint
          let initPrice = 0
          if (price) {
            if (isTax === '0') {
              initPrice = price * (1 + sku.taxRate)
            } else {
              initPrice = price
            }
            if (currency === 'CNY') {
              sku.taxPrice = formatPrice(initPrice)
              sku.freeTaxPrice = formatPrice((parseFloat(initPrice) / (1 + sku.taxRate)))
              //
              // sku.taxAmount
            } else {
              sku.taxPrice = formatPrice(initPrice / exchangeRate)
              sku.freeTaxPrice = formatPrice((parseFloat(initPrice) / (1 + sku.taxRate)) / exchangeRate)
            }
          } else {
            sku.taxPrice = 0
            sku.freeTaxPrice = 0
            //
            // sku.taxAmount = 0
            setAggreementPrice(sku, currency, exchangeRate)
          }
        } else {
          // 错误处理
          throw new Error('该商品在所选择的销售范围下未配置价格或税率，请选择其他商品或在crm维护该商品的工厂信息')
        }
      }
    }
  }
  try {
    if (sku.directDeliverySupplier === '0') {
      // 震坤行发货 sim 库位
      const asyncTask = async () => {
        let factory = sku.factory;
        if (typeof sku.factory === 'object') {
          factory = sku.factory.code
        }
        const data = {
          skuSet: [skuNo],
          factorySet: [factory],
          positionScope: 1
        }
        const res = await createOrder.getDeliveryWarehouse(data)
        console.log(res)
        if (res && res.status === 200 && Array.isArray(res.result)) {
          const posList = []
          const codeMap = {}
          res.result.reduce((prev, next) => {
            prev.push(...next.allPosition)
            return prev;
          }, []).forEach(item => {
            const key = `${item.factory}_${item.code}_${item.name}`
            if (!codeMap[key]) {
              codeMap[key] = true
              posList.push(item)
            }
          })
          const findImportPos = posList.find(pos => pos.code === sku.position);
          if (!findImportPos) {
            const findPos = (sku.positionList || []).find(pos => pos.code === sku.position);
            if (findPos) {
              posList.unshift(findPos)
            }
          }
          sku.simPositionList = posList
        }
      }
      await asyncTask()
    }
  } catch (err) {
    console.log(err)
  }
  // 整行税额
  sku.taxAmount = sku.freeTaxPrice
    ? formatPrice(sku.freeTaxPrice * sku.quantity * sku.taxRate, 6)
    : 0
  return sku
}

const state = {
  skuList: {},
  taxedTotalAmount: {},
  untaxedTotalAmount: {},
  taxedDiscountTotal: {},
  unTaxedDiscountTotal: {}
}

const mutations = {
  ADD_SKU_LIST: (state, data) => {
    const { key, sku } = data
    if (key && sku) {
      const skus = state.skuList[key] || []
      skus.push(sku)

      setStore(state, skus, key)
    }
  },
  CLEAR_SKU_LIST: (state, data) => {
    const { key } = data
    if (key) {
      state.skuList = {
        ...state.skuList,
        [key]: []
      }
    }
  },
  CHANGE_ITEM_AMOUNT: (state, data) => {
    const { orderData, page, size, index, value, key } = data
    if (index != null) {
      const skus = state.skuList[key] || []
      const foundItem = skus[(page - 1) * size + index]
      if (foundItem) {
        foundItem.quantity = value
        foundItem.taxAmount = foundItem.freeTaxPrice
          ? formatPrice(foundItem.freeTaxPrice * value * foundItem.taxRate)
          : 0
        const { skuUnitCount, customerSkuUnitCount } = foundItem;
        if (
          skuUnitCount && customerSkuUnitCount &&
          parseFloat(skuUnitCount) !== 0 &&
          parseFloat(customerSkuUnitCount) !== 0
          ) {
            const rate = formatPrice(parseFloat(customerSkuUnitCount) / parseFloat(skuUnitCount), 6);
            foundItem.customerMaterialQuantity = formatPrice(foundItem.quantity * rate, 6);
        }
      }
      const { isTax } = orderData || {}
      setStore(state, skus, key, isTax)
    }
  },
  REMOVE_SKU_LIST: (state, data) => {
    const { orderData, page, size, index, key } = data
    if (index != null) {
      const n = (page - 1) * size + index
      const skus = state.skuList[key] || []
      const foundItem = skus[n]
      if (foundItem) {
        skus.splice(n, 1)
      }
      const { isTax } = orderData || {}
      setStore(state, skus, key, isTax)
    }
  },
  REMOVE_MULTI_SKU_LIST: (state, data) => {
    const { page, size, value, key, orderData } = data
    if (value && Array.isArray(value)) {
      const skus = state.skuList[key] || []
      for (let i = size; i >= 0; i--) {
        const n = (page - 1) * size + i
        if (skus[n]) {
          const foundItem = value.find(item => item.uuid === skus[n].uuid)
          if (foundItem) {
            skus.splice(n, 1)
          }
        }
      }
      const { isTax } = orderData || {}
      setStore(state, skus, key, isTax)
    }
  },
  CHANGE_ITEM_TAXED_PRICE: (state, data) => {
    const { orderData, page, size, index, value, key } = data
    const skus = state.skuList[key] || []
    const foundItem = skus[(page - 1) * size + index]
    const {
      exchangeRate = 1
    } = orderData
    if (foundItem) {
      const taxRate = parseFloat(foundItem.taxRate)
      foundItem.taxPrice = value
      foundItem._taxPrice = value * exchangeRate
      if (!isNaN(taxRate)) {
        foundItem.freeTaxPrice = value ? value / (1 + taxRate) : 0
        foundItem._freeTaxPrice = (value ? value / (1 + taxRate) : 0) * exchangeRate
        foundItem.taxAmount = value
          ? formatPrice(foundItem.freeTaxPrice * foundItem.quantity * taxRate)
          : 0
      }
    }
    const { isTax } = orderData || {}
    setStore(state, skus, key, isTax)
  },
  CHANGE_ITEM_UNTAXED_PRICE: (state, data) => {
    const { orderData, page, size, index, value, key } = data
    let {
      exchangeRate
    } = orderData
    if (!exchangeRate) {
      exchangeRate = 1
    }
    const skus = state.skuList[key] || []
    const foundItem = skus[(page - 1) * size + index]
    if (foundItem) {
      const taxRate = parseFloat(foundItem.taxRate)
      foundItem.freeTaxPrice = value
      foundItem._freeTaxPrice = value * exchangeRate
      if (!isNaN(taxRate)) {
        foundItem.taxPrice = value ? value * (1 + taxRate) : 0
        foundItem._taxPrice = (value ? value * (1 + taxRate) : 0) * exchangeRate
        foundItem.taxAmount = value
          ? formatPrice(value * foundItem.quantity * taxRate)
          : 0
      }
    }
    const { isTax } = orderData || {}
    setStore(state, skus, key, isTax)
  },
  CHANGE_ROW_ITEM: (state, data) => {
    const { page, size, index, value, type, dictList, orderType, key, orderData } = data
    const skus = state.skuList[key] || []
    const foundItem = skus[(page - 1) * size + index]
    const { isTax } = orderData
    if (foundItem) {
      if (type === 'position') {
      } else if (type === 'factory') {
        const { factoryProductPriceVOMap } = foundItem
        const facCode = parseInt(value.code, 10)
        let position
        const positionList = dictList['position'].filter(item => {
          if (isServiceOrder(orderType)) {
            return item.parentCode === value.code && endsWith(String(item.code), '04')
          }
          return item.parentCode === value.code && item.code !== -1
        })
        foundItem['positionList'] = positionList
        foundItem['position'] = position
        if (
          foundItem['directDeliverySupplier'] === '2' &&
          !isForecastOrder(orderType) &&
          orderType !== 'Z014' &&
          orderType !== 'ZEV1' &&
          orderType !== 'ZEV2' &&
          orderType !== 'ZEV3' &&
          orderType !== 'ZEV4') {
          positionList.unshift({
            code: -1,
            name: '自动挑仓'
          })
          foundItem['position'] = -1
        }
        let hasSuggestPrice = true
        if (factoryProductPriceVOMap && facCode && factoryProductPriceVOMap[facCode]) {
          const { taxRate, suggestPrice } = factoryProductPriceVOMap[facCode]
          if (suggestPrice != null && taxRate != null) {
            // 税率
            foundItem.taxRate = (taxRate * 0.01).toFixed(2)
            if (isFreeOrder(orderType)) {
              foundItem.taxPrice = 0
              foundItem.freeTaxPrice = 0
            } else {
              if (isTax === '0') {
                foundItem.taxPrice = formatPrice(foundItem.freeTaxPrice * (1 + taxRate * 0.01), 6)
                // foundItem.freeTaxPrice = formatPrice(suggestPrice / (1 + taxRate * 0.01), 6)
              } else {
                // foundItem.taxPrice = formatPrice(suggestPrice, 6)
                foundItem.freeTaxPrice = formatPrice(foundItem.taxPrice / (1 + taxRate * 0.01), 6)
              }
              foundItem.suggestPrice = formatPrice(suggestPrice, 6)
            }
            foundItem.taxAmount = foundItem.freeTaxPrice ? formatPrice(foundItem.freeTaxPrice * foundItem.quantity * foundItem.taxRate) : 0
          } else {
            hasSuggestPrice = false
          }
        } else {
          hasSuggestPrice = false
        }
        if (!hasSuggestPrice) {
          foundItem.taxPrice = 0
          foundItem.freeTaxPrice = 0
          foundItem.taxRate = 0
          foundItem.taxAmount = 0
        }
        setStore(state, skus, key)
        return
      } else if (type === 'directDeliverySupplier') {
        let positionList = []
        let position = ''
        // const facCode = String(foundItem.factory.code)
        positionList = dictList['position'].filter(item => {
          if (isServiceOrder(orderType) || foundItem.mtart === 'Z002') {
            return item.parentCode === foundItem.factory.code && endsWith(String(item.code), '04')
          }
          return item.parentCode === foundItem.factory.code && item.code !== -1
        })
        if (value === '0') {
          // extract positionList
        } else if (value === '1') {
          positionList = positionList.filter(item =>
            endsWith(String(item.code), '04') && item.code !== -1
          )
        }
        if (value === '1') {
          if (orderType === 'Z008') {
            position = positionList[0]
          }
        } else if (
          value === '2' &&
          !isForecastOrder(orderType) &&
          orderType !== 'Z014' &&
          orderType !== 'ZEV1' &&
          orderType !== 'ZEV2' &&
          orderType !== 'ZEV3' &&
          orderType !== 'ZEV4') {
          positionList.unshift({
            code: -1,
            name: '自动挑仓'
          })
          position = -1
        } else {
          position = ''
        }
        foundItem['position'] = position
        foundItem['positionList'] = positionList
      } else {
        foundItem[type] = value
      }
    }
    state.skuList = {
      ...state.skuList,
      [key]: skus
    }
  },
  UPDATE_GOODS_DETAIL: (state, data) => {
    const { sku, orderData, index, key } = data
    if (index != null) {
      const n = index
      const skus = [ ...state.skuList[key] ] || []
      const foundItem = skus[n]
      skus[n] = {
        ...foundItem,
        ...sku
      }
      const skuList = {
        ...state.skuList,
        [key]: skus
      }
      state.skuList = skuList
      const { isTax } = orderData || {}
      setTotal(state, skus, key, isTax)
    }
  },
  IMPORT_GOODS: (state, data) => {
    const { key, skuList, orderData } = data
    const { isTax } = orderData || {}
    const skus = state.skuList[key] || []
    const sl = [
      ...skus,
      ...skuList
    ]
    state.skuList = {
      ...state.skuList,
      [key]: sl
    }
    setTotal(state, sl, key, isTax)
  },
  TOTAL_URGENT: (state, data) => {
    const { key, data: urgent } = data
    const skus = state.skuList[key] || []
    skus.forEach(element => {
      element.urgent = urgent
    })
    state.skuList = {
      ...state.skuList,
      [key]: [
        ...skus
      ]
    }
  },
  TOTAL_SENSITIVE: (state, data) => {
    const { key, data: customerDateSensitive } = data
    const skus = state.skuList[key] || []
    skus.forEach(element => {
      element.customerDateSensitive = customerDateSensitive
    })
    state.skuList = {
      ...state.skuList,
      [key]: [
        ...skus
      ]
    }
  },
  EXCHANGE_CURRENCY: (state, data) => {
    const { key, currency, orderData } = data
    const { isTax, exchangeRate } = orderData || {}
    const skus = state.skuList[key] || []
    if (currency === 'CNY') {
      skus.forEach(element => {
        element.currency = currency
        element.suggestPrice = element._suggestPrice
        element.taxPrice = formatPrice(element._taxPrice, 6)
        element.freeTaxPrice = formatPrice(element._freeTaxPrice, 6)
        element.taxAmount = element._freeTaxPrice ? formatPrice(element._freeTaxPrice * element.quantity * element.taxRate) : 0
      })
    } else {
      skus.forEach(element => {
        element.currency = currency
        element.suggestPrice = element._suggestPrice / exchangeRate
        element.taxPrice = formatPrice(element._taxPrice / exchangeRate, 6)
        element.freeTaxPrice = formatPrice(element._freeTaxPrice / exchangeRate, 6)
        element.taxAmount = element._freeTaxPrice ? formatPrice(element.freeTaxPrice * element.quantity * element.taxRate) : 0
      })
    }
    setStore(state, skus, key, isTax)
  },
  ASSIGN_DISCOUNT: (state, data) => {
    const { key, totalDiscount, orderData } = data
    const { isTax } = orderData || {}
    const skus = state.skuList[key] || []
    const untaxedTotalAmount = state.untaxedTotalAmount[key]
    const taxedTotalAmount = state.taxedTotalAmount[key]
    const sortSkus = [...skus].sort((a, b) => {
      const { quantity: q1, freeTaxPrice: f1, taxPrice: t1 } = a
      const { quantity: q2, freeTaxPrice: f2, taxPrice: t2 } = b
      let ret = 0
      if (isTax === '0') {
        ret = q1 * f1 > q2 * f2 ? 1 : -1
      }
      if (isTax === '1') {
        ret = q1 * t1 > q2 * t2 ? 1 : -1
      }
      return ret
    })
    let totalAmount = 0
    const len = sortSkus.length
    for (let i = 0; i < len - 1; i++) {
      const { quantity, freeTaxPrice, taxPrice } = sortSkus[i]
      let rowDiscount = 0
      if (isTax === '0') {
        rowDiscount = ((totalDiscount * quantity * freeTaxPrice) / untaxedTotalAmount)
      }
      if (isTax === '1') {
        rowDiscount = ((totalDiscount * quantity * taxPrice) / taxedTotalAmount)
      }
      const rd = parseFloat(rowDiscount.toFixed(2))
      sortSkus[i].discountAmount = rd
      totalAmount += rd
    }
    sortSkus[len - 1].discountAmount = totalDiscount - totalAmount
    setTotal(state, skus, key, isTax)
  }
}
function handleResultData (item) {
  console.log(item.customPropertyList)
  if (item && Array.isArray(item.customPropertyList)) {
    if (item?.fillDefaultCustomProperty !== 'Z') {
      item.customPropertyList = item.customPropertyList.map(property => ({
        ...property,
        placeholder: property.customPropertyRemark,
        customPropertyRemark: ''
      }))
    }
  }
}
const actions = {
  changeGoodsAmount ({ commit }, data) {
    commit('CHANGE_ITEM_AMOUNT', data)
  },
  changeGoodsTaxedPrice ({ commit }, data) {
    commit('CHANGE_ITEM_TAXED_PRICE', data)
  },
  changeGoodsUntaxedPrice ({ commit }, data) {
    commit('CHANGE_ITEM_UNTAXED_PRICE', data)
  },
  changeItem ({ commit, rootState }, data) {
    const {
      orderCommon: { dictList }
    } = rootState
    commit('CHANGE_ROW_ITEM', {
      ...data,
      dictList
    })
  },
  clearGoods ({ commit }, data) {
    commit('CLEAR_SKU_LIST', data)
  },
  addGoods ({ commit, rootState }, data) {
    console.log(data);
    if (data) {
      const { skus, goods, salesRange, orderData, orderType, key, company, factoryList, selectedSearchItem, index } = data
      if (goods && goods.skuNo && salesRange && orderData) {
        const { salesOrganization, distributionChannel, productGroup } = salesRange
        const { customerNumber } = orderData
        const customerSkuNo = selectedSearchItem === '2' ? 'customer/relation' : ''
        return createOrder.getSkuDetail(goods.skuNo, {
          customerNo: customerNumber,
          salesOrganization,
          distributionChannel,
          productGroup,
          orderType,
          customerSkuNo
        }).then(async result => {
          if (result && result.code === 200) {
            const {
              orderCommon: { dictList }
            } = rootState
            const {
              currency, urgent, customerDateSensitive, taxType, orderReason
            } = orderData
            const exchangeRate = orderData.exchangeRate || 1
            // 非订单原因：对账差异调整禁止添加客户限售商品
            if (result?.bizCode && result.bizCode === 200001 && orderReason !== '038') {
              MessageBox.alert(result?.msg, '提示', {
                type: 'error'
              })
              return
            }
            // 表格中添加一条列表记录
            const sku = result.data
            handleResultData(sku)
            const { customerSkuNo, customerSkuName, customerSkuUnitCount, customerSkuUnit, customerSkuSpecification, skuUnitCount } = goods
            sku.idx = findLastLineNo(skus)
            sku.uuid = shortid.generate()
            sku.urgent = urgent
            sku.customerDateSensitive = customerDateSensitive
            sku.customerMaterialNo = customerSkuNo || ''
            sku.customerMaterialName = customerSkuName;
            sku.customerMaterialUnit = customerSkuUnit;
            sku.customerSpecificationModel = customerSkuSpecification;
            sku.customerSkuUnitCount = customerSkuUnitCount || 0;
            sku.skuUnitCount = skuUnitCount || 0;
            // 数量
            if (goods.createdBySH) {
              sku.quantity = goods.quantity || 0
            } else {
              sku.quantity = 0
            }
            // 单位
            sku.packageUnit = ''
            // 备注
            sku.memo = ''
            // 客户行号
            sku.customerLine = ''
            // 领用人
            sku.recipient = ''
            // 需求部门
            sku.demandDepartment = ''
            // 直发备注
            sku.sendWarehouseMemo = ''
            // 选择直发
            sku.directDeliverySupplier = getDefaultDirectDeliverySupplier(orderData, orderType, dictList)

            // 拉取货币列表
            sku.currency = orderData.currency
            // dictList['currency'] ? dictList['currency'][0] : null
            // 首个交期
            sku.customerDate = orderData.orderDate
            // 整单客户期望日期对齐抬头
            if (orderData.autoBatching !== 'X') {
              sku.customerDate = orderData.customerReferenceDate || ''
            }
            // 处理单位
            // 暂时不处理多包规的情况
            if (sku) {
              const { unitCode, unitName, skuNo } = sku
              if (unitCode && unitName) {
                sku.packageInfoList = [{
                  skuNo,
                  unitName,
                  ruleDes: `1${unitName}/${unitName}`,
                  conversion: 1,
                  unit: unitCode
                }]
              }
            }
            const { unitCode, factoryProductPriceVOMap, factoryMtartMap } = sku
            sku.quantityUnit = unitCode
            // 拉取工厂列表
            if (factoryList) {
              sku.factory = {}
              let cmy = company
              if (company === '1300') {
                cmy = '1000'
              }
              let currentCompay
              factoryList.forEach(fa => {
                if (fa && fa.code) {
                  const code = parseInt(fa.code, 10)
                  if (orderData.selectedSalesRange.salesOrganization === '6001' && parseInt(sku.factoryPriorityList[0], 10) === code) {
                    sku.factory = fa
                  }
                  if (orderData.selectedSalesRange.salesOrganization !== '6001' && factoryProductPriceVOMap[code] && factoryProductPriceVOMap[code].taxRate != null) {
                    if (!(sku.factory && sku.factory.code)) {
                      sku.factory = fa
                    }
                    if (sku.factory && sku.factory.code && fa.code === cmy) {
                      sku.factory = fa
                    }
                  }
                  if (fa.code === cmy) {
                    currentCompay = fa
                  }
                }
              })
              if (sku.factory && !sku.factory.code) {
                MessageBox.alert('该商品在所选择的销售范围下未配置价格或税率，请选择其他商品或在crm维护该商品的工厂信息', '提示')
                return { result }
              } else if (sku.factory && sku.factory.code && sku.factory.code !== cmy && currentCompay) {
                try {
                  await MessageBox.confirm(`该商品在${currentCompay.name}工厂下未配置税率，选择其他工厂添加商品?`, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    center: true
                  })
                } catch (err) {
                  return { result }
                }
              }
            }
            // 判断物料类型
            if (sku.factory && factoryMtartMap) {
              const code = parseInt(sku.factory.code, 10)
              if (code) {
                const mtart = factoryMtartMap[code]
                // z002 z008 z009 不允许加服务物料
                // z005 z018必须加服务物料
                let err = false
                if (['Z002', 'Z008', 'Z009'].indexOf(orderType) >= 0 && mtart === 'Z002') {
                  MessageBox.alert('该单据类型暂不支持选择服务物料', '提示', {
                    type: 'error'
                  })
                  err = true
                } else if (['Z005', 'Z018'].indexOf(orderType) >= 0 && mtart !== 'Z002') {
                  MessageBox.alert('该单据类型必须选择服务物料', '提示', {
                    type: 'error'
                  })
                  err = true
                }
                if (err) {
                  return { result }
                }
              }
            }
            // 拉取库位
            if (sku.factory) {
              let positionList = dictList['position'].filter(item => {
                if (isServiceOrder(orderType) || sku.mtart === 'Z002' || orderType === 'Z018') {
                  return item.parentCode === sku.factory.code && endsWith(String(item.code), '04')
                }
                return item.parentCode === sku.factory.code && item.code !== -1
              })
              const facCode = String(sku.factory.code)
              // 预测性销售订单不支持自动调仓
              if (sku.directDeliverySupplier === '2' &&
                !isForecastOrder(orderType) &&
                orderType !== 'Z018' &&
                orderType !== 'Z009' &&
                orderType !== 'Z014' &&
                orderType !== 'ZEV1' &&
                orderType !== 'ZEV2' &&
                orderType !== 'ZEV3' &&
                orderType !== 'ZEV4') {
                positionList.unshift({
                  code: -1,
                  name: '自动挑仓'
                })
                sku.position = -1
              } else {
                sku.position = ''
              }
              sku.positionList = positionList
              // 含税/未税金额
              let hasTaxRate = true
              if (facCode) {
                const fcode = parseInt(facCode, 10)
                if (fcode && factoryProductPriceVOMap[fcode]) {
                  const { taxRate, taxRateInPoint } = factoryProductPriceVOMap[fcode]
                  let { suggestPrice } = factoryProductPriceVOMap[fcode]
                  if (!suggestPrice) {
                    suggestPrice = 0
                  }
                  if (taxRate != null && taxRateInPoint != null) {
                    // 税率
                    sku.taxRate = taxType === '0' ? 0 : taxRateInPoint
                    sku.suggestPrice = currency !== 'CNY' ? (suggestPrice / exchangeRate) : suggestPrice
                    sku._suggestPrice = suggestPrice
                    sku.taxPrice = 0
                    sku.freeTaxPrice = 0
                    setAggreementPrice(sku, currency, exchangeRate)
                  } else {
                    // 错误处理
                    hasTaxRate = false
                  }
                }
              }
              if (!hasTaxRate) {
                result.hasTaxRate = false
                return { result }
              }
              // 税额
              const taxAmount = sku.freeTaxPrice ? formatPrice(sku.freeTaxPrice * sku.quantity * sku.taxRate) : 0
              sku.taxAmount = taxAmount
            }
            if (goods.createdBySH) {
              sku.position = goods.position || sku.position
            }
            if (goods.createdBySH) {
              sku.directDeliverySupplier = goods.directDeliverySupplier || sku.directDeliverySupplier
            }
              // 可发货日期
            sku.deliveryDate = ''
            sku.skuArrivalDate = '' // 标准送达日期
            sku.sysDeliveryDate = '' // 标准发货日期
              // 是否接受标期
            sku.refuseSystemDeliveryDate = ''
            console.log(sku.directDeliverySupplier, sku)
            if (sku.directDeliverySupplier === '0') {
              // 震坤行发货 sim 库位
              const asyncTask = async () => {
                let factory = sku.factory
                if (typeof sku.factory === 'object') {
                  factory = sku.factory.code
                }
                const data = {
                  skuSet: [sku.skuNo],
                  factorySet: [factory],
                  positionScope: 1
                }
                const res = await createOrder.getDeliveryWarehouse(data)
                if (res && res.status === 200 && Array.isArray(res.result)) {
                  const posList = []
                  const codeMap = {}
                  res.result.reduce((prev, next) => {
                    prev.push(...next.allPosition)
                    return prev;
                  }, []).forEach(item => {
                    const key = `${item.factory}_${item.code}_${item.name}`
                    if (!codeMap[key]) {
                      codeMap[key] = true
                      posList.push(item)
                    }
                  })
                  console.log(posList)
                  sku.simPositionList = posList
                }
              }
              await asyncTask()
            }
            // 有index表示无SKU行，添加SKU后更新已有行信息；没有index表示直接添加SKU行（这里的index传的是分页处理后的）
            if (index !== undefined) {
              let foundItem = cloneDeep(skus[index]);
              console.log(foundItem)
              if (foundItem) {
                foundItem = {
                  ...foundItem,
                  ...sku,
                  idx: foundItem.idx,
                  uuid: foundItem.uuid,
                  freeTaxPrice: foundItem.freeTaxPrice || sku.freeTaxPrice,
                  taxPrice: foundItem.taxPrice || sku.taxPrice,
                  quantity: foundItem.quantity || sku.quantity
                }
              }
              const { isTax } = orderData || {}
              if (isTax === '0') {
                foundItem.taxPrice = formatPrice(foundItem.freeTaxPrice * (1 + foundItem.taxRate), 6)
              } else {
                foundItem.freeTaxPrice = formatPrice(foundItem.taxPrice / (1 + foundItem.taxRate), 6)
              }
              foundItem.taxAmount = foundItem.freeTaxPrice ? formatPrice(foundItem.freeTaxPrice * foundItem.quantity * foundItem.taxRate) : 0
              return { result, foundItem }
            } else {
              commit('ADD_SKU_LIST', {
                key,
                sku
              })
            }
          }
          return { result }
        })
      }
    }
  },
  addNullGoods ({ commit, rootState }, data) {
    const { skus, key, orderData, orderType } = data
    const {
      orderCommon: { dictList }
    } = rootState
    let sku = {};
    sku.idx = findLastLineNo(skus)
    sku.uuid = shortid.generate()
    // 数量
    sku.quantity = 0
    // 单位
    sku.packageUnit = ''
    // 备注
    sku.memo = ''
    // 客户行号
    sku.customerLine = ''
    // 领用人
    sku.recipient = ''
    // 需求部门
    sku.demandDepartment = ''
    // 直发备注
    sku.sendWarehouseMemo = ''
    // 选择直发
    sku.directDeliverySupplier = getDefaultDirectDeliverySupplier(orderData, orderType, dictList)
    sku.positionList = []
    if (sku.directDeliverySupplier === '2') {
      sku.positionList.unshift({
        code: -1,
        name: '自动挑仓'
      })
      sku.position = -1;
    } else {
      sku.position = '';
    }
    // 拉取货币列表
    sku.currency = orderData.currency
    // dictList['currency'] ? dictList['currency'][0] : null
    // 首个交期
    sku.customerDate = orderData.orderDate
    // 工厂
    sku.factory = {
      code: '1000'
    }
    // 价格
    sku.taxPrice = 0
    sku.freeTaxPrice = 0
    // 整单客户期望日期对齐抬头
    if (orderData.autoBatching !== 'X') {
      sku.customerDate = orderData.customerReferenceDate || ''
    }
    console.log(sku)
    commit('ADD_SKU_LIST', {
      key,
      sku
    })
  },
  removeGoods ({ commit }, data) {
    commit('REMOVE_SKU_LIST', data)
  },
  removeMultiGoods ({ commit }, data) {
    commit('REMOVE_MULTI_SKU_LIST', data)
  },
  updateGoodsDetail ({ commit }, data) {
    commit('UPDATE_GOODS_DETAIL', data)
  },
  assignDiscount ({ commit, state }, data) {
    const { key, orderData } = data
    const { isTax } = orderData || {}
    const taxedTotalAmount = state.taxedTotalAmount[key]
    const untaxedTotalAmount = state.untaxedTotalAmount[key]
    if ((isTax === '1' && taxedTotalAmount) || (isTax === '0' && untaxedTotalAmount)) {
      commit('ASSIGN_DISCOUNT', data)
      return true
    }
    return false
  },
  async importGoods ({ commit, rootState }, data) {
    const { skus: currentSkus, skuList, salesRange,
      orderData, orderType, key, company, factoryList } = data
    const {
      orderCommon: { dictList }
    } = rootState
    const skus = []
    if (skuList && skuList.length > 0) {
      for (let i = 0; i < skuList.length; i++) {
        try {
          const lineNo = findLastLineNo(currentSkus)
          const formatedSku = await formatSku({
            startIdx: lineNo,
            idx: i,
            data: skuList[i],
            dictList,
            company,
            orderType,
            orderData,
            salesRange,
            factoryList
          })
          handleResultData(formatedSku)
          if (orderData.autoBatching !== 'X') {
            formatedSku.customerDate = orderData.customerReferenceDate || formatedSku.customerDate || ''
          }
          if (formatedSku) {
            skus.push(formatedSku)
          }
        } catch (e) {
          skus.length = 0
          if (e && e.message) {
            MessageBox.alert(e.message, '提示', {
              type: 'error'
            })
          }
        }
      }
    }
    if (skus && skus.length > 0) {
      commit('IMPORT_GOODS', { key, skuList: skus, orderData })
    }
    return skus
  },
  urgentTotal ({ commit }, data) {
    commit('TOTAL_URGENT', data)
  },
  customerDateSensitiveTotal ({ commit }, data) {
    commit('TOTAL_SENSITIVE', data)
  },
  exchangeCurrency ({ commit }, data) {
    commit('EXCHANGE_CURRENCY', data)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
