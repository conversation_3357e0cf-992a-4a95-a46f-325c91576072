import { getBlocOptions } from '@/api/construction'
const state = {
  blocOptions: []
}

const mutations = {
  setBlocOptions (state, value) {
    console.log(state, value)
    state.blocOptions = value
  }
}

const actions = {
  getBlocOptions ({ commit, state }) {
    getBlocOptions()
      .then(res => {
        if (res && res.blocInfos) {
          commit('setBlocOptions', res.blocInfos.filter(e => e))
        }
      })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
