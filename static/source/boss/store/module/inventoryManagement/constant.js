/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2024-12-03 11:38:49
 * @LastEditors: luozhikai
 * @LastEditTime: 2025-05-29 10:52:56
 * @Description: file content
 */

// 库存管理除寄售盘亏单外的所有类型单据的表单字段
export const INVENTORY_MANAGEMENT_FIELDS = [
  {
      'id': 1,
      'name': '单据类型',
      'prop': 'orderType',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 2,
      'name': '申请单号',
      'prop': 'orderNo',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 3,
      'name': '单据状态',
      'prop': 'orderStatus',
      'width': 120,
      'type': 'select',
      'enums': '',
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 4,
      'name': '创建人员',
      'prop': 'createUser',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 5,
      'name': '凭证日期',
      'prop': 'certificateDate',
      'width': 120,
      'type': 'daterange',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 6,
      'name': '过账日期',
      'prop': 'postDate',
      'width': 120,
      'type': 'daterange',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 7,
      'name': '传输信息',
      'prop': 'transportMessage',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 8,
      'name': '运输方向',
      'prop': 'transportDirection',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': null,
      'field': 'contact',
      'span': 24,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 9,
      'name': '运输地址联系人',
      'prop': 'transportContactName',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': null,
      'field': 'contact',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 10,
      'name': '运输地址',
      'prop': 'transportAddress',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': null,
      'field': 'contact',
      'span': 24,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 11,
      'name': '震坤行联系人',
      'prop': 'zkhContactName',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': null,
      'field': 'contact',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 12,
      'name': '震坤行联系人电话',
      'prop': 'zkhContactNumber',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': null,
      'field': 'contact',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 13,
      'name': '震坤行地址',
      'prop': 'zkhAddress',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': null,
      'field': 'contact',
      'span': 24,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 14,
      'name': '项目行',
      'prop': 'itemNo',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 15,
      'name': '状态',
      'prop': 'isDeleted',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 16,
      'name': 'SKU编码',
      'prop': 'skuNo',
      'width': 200,
      'type': 'custom',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 17,
      'name': '物料描述',
      'prop': 'materialDescription',
      'width': 200,
      'type': 'text',
      'enums': null,
      'disabled': 1,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 18,
      'name': '工厂',
      'prop': 'factoryCode',
      'width': 200,
      'type': 'select',
      'enums': null,
      'disabled': 1,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 19,
      'name': '仓库地点',
      'prop': 'warehouseLocation',
      'width': 200,
      'type': 'select',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 20,
      'name': '批次',
      'prop': 'batchNo',
      'width': 200,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 21,
      'name': '交货数量',
      'prop': 'quantity',
      'width': 200,
      'type': 'input-number',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 22,
      'name': '单位',
      'prop': 'inventoryUnit',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 23,
      'name': '接收sku编码',
      'prop': 'receiveSkuNo',
      'width': 200,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 0,
      'isDetail': 0
  },
  {
      'id': 24,
      'name': '接受物料描述',
      'prop': 'receiveMaterialDescription',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 0,
      'isDetail': 0
  },
  {
      'id': 25,
      'name': '接收仓库地点',
      'prop': 'receiveWarehouseLocation',
      'width': 200,
      'type': 'select',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 26,
      'name': '需求数量',
      'prop': 'requiredQuantity',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 1,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 27,
      'name': '已领数量',
      'prop': 'shippedQuantity',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 1,
      'field': 'table',
      'span': 8,
      'isCreate': 0,
      'isDetail': 0
  },
  {
      'id': 28,
      'name': '成本中心',
      'prop': 'costCenter',
      'width': 200,
      'type': 'custom',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 30,
      'name': '供应商编码',
      'prop': 'supplierNo',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 31,
      'name': '供应商名称',
      'prop': 'supplierName',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 32,
      'name': '参考单号',
      'prop': 'referNo',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 1,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 33,
      'name': '参考行号',
      'prop': 'referItemNo',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 1,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 34,
      'name': '项目文本',
      'prop': 'projectText',
      'width': 200,
      'type': 'input',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 35,
      'name': '关联单据',
      'prop': 'relateOrderNo',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': null,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 0
  },
  {
      'id': 36,
      'name': '抬头备注',
      'prop': 'orderRemark',
      'width': 120,
      'type': 'textarea',
      'enums': null,
      'disabled': null,
      'field': 'baseInfo',
      'span': 16,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 37,
      'name': '运输联系人电话',
      'prop': 'transportContactNumber',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': null,
      'field': 'contact',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 38,
      'name': '坤合返回消息',
      'prop': 'sapsResult',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 0,
      'field': 'baseInfo',
      'span': 16,
      'isCreate': 0,
      'isDetail': 1
  },
  {
    'id': 39,
      'name': 'SAP返回消息',
      'prop': 'sapResult',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'baseInfo',
      'span': 16,
      'isCreate': 0,
      'isDetail': 1
  },
  {
    'id': 39.1,
    'name': 'sim返回消息',
    'prop': 'simResult',
    'width': 120,
    'type': 'text',
    'enums': null,
    'disabled': null,
    'field': 'baseInfo',
    'span': 16,
    'isCreate': 0,
    'isDetail': 1
  },
  {
      'id': 40,
      'name': '供应商',
      'prop': 'supplier',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 0
  },
  {
      'id': 41,
      'name': '序号',
      'prop': 'index',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'expressInfo',
      'span': 8,
      'isCreate': 0,
      'isDetail': 1
  },
  {
      'id': 42,
      'name': '运单号',
      'prop': 'taskNo',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'expressInfo',
      'span': 8,
      'isCreate': 0,
      'isDetail': 1
  },
  {
      'id': 43,
      'name': '物流公司',
      'prop': 'carrierName',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 0,
      'field': 'expressInfo',
      'span': 8,
      'isCreate': 0,
      'isDetail': 1
  },
  {
      'id': 44,
      'name': '当前状态',
      'prop': 'status',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'expressInfo',
      'span': 8,
      'isCreate': 0,
      'isDetail': 1
  },
  {
      'id': 45,
      'name': '司机',
      'prop': 'deliverName',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': null,
      'field': 'expressInfo',
      'span': 8,
      'isCreate': 0,
      'isDetail': 1
  },
  {
      'id': 46,
      'name': '司机电话',
      'prop': 'deliverTel',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 0,
      'field': 'expressInfo',
      'span': 8,
      'isCreate': 0,
      'isDetail': 1
  },
  {
      'id': 47,
      'name': '总账科目',
      'prop': 'generalLedgerAccount',
      'width': 160,
      'type': 'select',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 48,
      'name': '已过账数量',
      'prop': 'postQuantity',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 49,
      'name': '接收工厂',
      'prop': 'receiveFactoryCode',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': null,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 50,
      'name': '接收SKU编码',
      'prop': 'receiveSkuNo',
      'width': 200,
      'type': 'custom',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 51,
      'name': '责任归属',
      'prop': 'responsibilityOwner',
      'width': 120,
      'type': 'select',
      'enums': null,
      'disabled': 0,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 52,
      'name': 'OA流程编号',
      'prop': 'oaNo',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 53,
      'name': 'OA行号',
      'prop': 'oaItemNo',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 55,
      'name': '来源系统',
      'prop': 'source',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': 1,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 58,
      'name': '资产卡片号',
      'prop': 'assetsCardNo',
      'width': 120,
      'type': 'text',
      'enums': null,
      'disabled': 0,
      'field': 'table',
      'span': 9,
      'isCreate': 1,
      'isDetail': 1
  },
  {
      'id': 59,
      'name': '来源系统单号',
      'prop': 'sourceOrderNo',
      'width': 120,
      'type': 'input',
      'enums': null,
      'disabled': 0,
      'field': 'baseInfo',
      'span': 8,
      'isCreate': 1,
      'isDetail': 1
  },
  {
    'id': 60,
    'name': '客户编码',
    'prop': 'customerCode',
    'width': 120,
    'type': 'text',
    'enums': null,
    'disabled': 0,
    'field': 'table',
    'span': 9,
    'isCreate': 0,
    'isDetail': 1
  },
  {
    'id': 61,
    'name': '库存类型',
    'prop': 'inventoryId',
    'width': 120,
    'type': 'text',
    'enums': null,
    'disabled': 0,
    'field': 'table',
    'span': 9,
    'isCreate': 0,
    'isDetail': 1
  }
]

// 库存管理寄售盘亏单的表单字段
export const INVENTORY_MANAGEMENT_32_FIELDS = [
  {
    id: 1,
    name: '单据类型',
    prop: 'orderType',
    width: 120,
    type: 'select',
    enums: null,
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 2,
    name: '申请单号',
    prop: 'orderNo',
    width: 120,
    type: 'input',
    enums: null,
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 3,
    name: '单据状态',
    prop: 'orderStatus',
    width: 120,
    type: 'select',
    enums: '',
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 4,
    name: '创建人员',
    prop: 'createUser',
    width: 120,
    type: 'input',
    enums: null,
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 5,
    name: '凭证日期',
    prop: 'certificateDate',
    width: 120,
    type: 'daterange',
    enums: null,
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 6,
    name: '过账日期',
    prop: 'postDate',
    width: 120,
    type: 'daterange',
    enums: null,
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 7,
    name: '传输信息',
    prop: 'transportMessage',
    width: 120,
    type: 'select',
    enums: null,
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 8,
    name: '运输方向',
    prop: 'transportDirection',
    width: 120,
    type: 'select',
    enums: null,
    disabled: null,
    field: 'contact',
    span: 24,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 9,
    name: '运输地址联系人',
    prop: 'transportContactName',
    width: 120,
    type: 'select',
    enums: null,
    disabled: null,
    field: 'contact',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 10,
    name: '运输地址',
    prop: 'transportAddress',
    width: 120,
    type: 'select',
    enums: null,
    disabled: null,
    field: 'contact',
    span: 24,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 11,
    name: '震坤行联系人',
    prop: 'zkhContactName',
    width: 120,
    type: 'select',
    enums: null,
    disabled: null,
    field: 'contact',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 12,
    name: '震坤行联系人电话',
    prop: 'zkhContactNumber',
    width: 120,
    type: 'input',
    enums: null,
    disabled: null,
    field: 'contact',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 13,
    name: '震坤行地址',
    prop: 'zkhAddress',
    width: 120,
    type: 'select',
    enums: null,
    disabled: null,
    field: 'contact',
    span: 24,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 14,
    name: '交货总金额',
    prop: 'deliveryTotalAmount',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 15,
    name: '项目行',
    prop: 'itemNo',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 16,
    name: '状态',
    prop: 'isDeleted',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'table',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 17,
    name: 'SKU编码',
    prop: 'skuNo',
    width: 200,
    type: 'custom',
    enums: null,
    disabled: 0,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 18,
    name: '物料描述',
    prop: 'materialDescription',
    width: 200,
    type: 'text',
    enums: null,
    disabled: 1,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 19,
    name: '工厂',
    prop: 'factoryCode',
    width: 200,
    type: 'select',
    enums: null,
    disabled: 1,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 20,
    name: '寄售客户',
    prop: 'customerCode',
    width: 200,
    type: 'custom',
    enums: null,
    disabled: 0,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 21,
    name: '批次',
    prop: 'batchNo',
    width: 200,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 22,
    name: '批次数量',
    prop: 'batchQuantity',
    width: 200,
    type: 'text',
    enums: null,
    disabled: 1,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 23,
    name: '交货数量',
    prop: 'quantity',
    width: 200,
    type: 'input-number',
    enums: null,
    disabled: 0,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 24,
    name: '批次剩余数量',
    prop: 'remainQuantity',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 25,
    name: '交货金额',
    prop: 'deliveryAmount',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 25,
    name: '已过账数量',
    prop: 'postQuantity',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'table',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 34,
    name: '项目文本',
    prop: 'projectText',
    width: 200,
    type: 'input',
    enums: null,
    disabled: 0,
    field: 'table',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 35,
    name: '关联单据',
    prop: 'relateOrderNo',
    width: 120,
    type: 'input',
    enums: null,
    disabled: null,
    field: 'baseInfo',
    span: 8,
    isCreate: 1,
    isDetail: 0
  },
  {
    id: 36,
    name: '流程编号',
    prop: 'processInstanceId',
    width: 120,
    type: 'text',
    enums: null,
    disabled: 1,
    field: 'baseInfo',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 37,
    name: '抬头备注',
    prop: 'orderRemark',
    width: 120,
    type: 'textarea',
    enums: null,
    disabled: null,
    field: 'baseInfo',
    span: 16,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 38,
    name: '运输联系人电话',
    prop: 'transportContactNumber',
    width: 120,
    type: 'input',
    enums: null,
    disabled: null,
    field: 'contact',
    span: 8,
    isCreate: 1,
    isDetail: 1
  },
  {
    id: 39,
    name: '坤合返回消息',
    prop: 'sapsResult',
    width: 120,
    type: 'text',
    enums: null,
    disabled: 0,
    field: 'baseInfo',
    span: 16,
    isCreate: 0,
    isDetail: 1
  },
  {
    'id': 39.1,
    'name': 'sim返回消息',
    'prop': 'simResult',
    'width': 120,
    'type': 'text',
    'enums': null,
    'disabled': null,
    'field': 'baseInfo',
    'span': 16,
    'isCreate': 0,
    'isDetail': 1
  },
  {
    id: 40,
    name: 'SAP返回消息',
    prop: 'sapResult',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'baseInfo',
    span: 16,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 42,
    name: '序号',
    prop: 'index',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'expressInfo',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 43,
    name: '运单号',
    prop: 'taskNo',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'expressInfo',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 44,
    name: '物流公司',
    prop: 'carrierName',
    width: 120,
    type: 'text',
    enums: null,
    disabled: 0,
    field: 'expressInfo',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 45,
    name: '当前状态',
    prop: 'status',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'expressInfo',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 46,
    name: '司机',
    prop: 'deliverName',
    width: 120,
    type: 'text',
    enums: null,
    disabled: null,
    field: 'expressInfo',
    span: 8,
    isCreate: 0,
    isDetail: 1
  },
  {
    id: 47,
    name: '司机电话',
    prop: 'deliverTel',
    width: 120,
    type: 'text',
    enums: null,
    disabled: 0,
    field: 'expressInfo',
    span: 8,
    isCreate: 0,
    isDetail: 1
  }
];
