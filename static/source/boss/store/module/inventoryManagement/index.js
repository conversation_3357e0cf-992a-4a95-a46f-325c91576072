import { getInvFields } from '@/api/mm'
import { INVENTORY_MANAGEMENT_FIELDS, INVENTORY_MANAGEMENT_32_FIELDS } from './constant'
// OUTSOURCING_IN("20", "委外发料单"),
// OUTSOURCING_OUT("21", "委外退料单"),
// PRE_RECEIVE("22","预领用单"),
// COST_IN("18", "费用性领料单"),
// COST_OUT("19", "费用性退料单"),
// RETURN("28", "采购退货单"),
// CONSIGNMENT_TRANSFER("30", "寄售调拨单"),
// PRE_TRANSFER("31", "预调拨单"),
// WAREHOUSE_INNER_TRANSFER("13", "库内调拨单"),
// FACTORY_TRANSFER("29", "工厂间调拨单"),
// INVENTORY_ADJUST("27", "库存调整单"),
// INVENTORY_DUMP("23", "库存报废单");
// 32 寄售调拨单
// WAREHOUSE_TRANSFER_KH("14", "库内调拨-坤合发起"),
// BATCH_FREEZE_TRANSFER_KH("15", "批次冻结-坤合发起"),
// BATCH_THAWING_TRANSFER_KH("16", "批次解冻-坤合发起"),
// MANUAL_TRANSFER_RIGHTS("17", "手工货权转移单"),
// 这是类型的编码和名称
const baseState = {
  '13': [],
  '14': [],
  '15': [],
  '16': [],
  '17': [],
  '18': [],
  '19': [],
  '20': [],
  '21': [],
  '22': [],
  '23': [],
  '27': [],
  '28': [],
  '29': [],
  '30': [],
  '31': [],
  '32': [],
  '33': [],
  '34': [],
  '35': []
}
const simpleClone = (source) => JSON.parse(JSON.stringify(source))
const state = {
  create: { ...simpleClone(baseState) },
  edit: { ...simpleClone(baseState) },
  detail: { ...simpleClone(baseState) },
  list: []
}

const mutations = {
  SET_INV_FIELDS (state, { orderType, routerType, res }) {
    // console.log(state, orderType, routerType, res)
    state[routerType][orderType] = res
  },
  SET_LIST_FIELDS (state, { routerType, res }) {
    // console.log(state, routerType, res)
    state[routerType] = res
  }
}

const actions = {
  getInvFields ({ commit }, { orderType, routerType }) {
    if (!orderType || !routerType) {
      throw Error('getInvFields orderType, routerType needed!')
    }
    // 为什么去掉接口调用，因为接口返回的表单字段除了32，所有单据类型都是一样的，所以直接在常量中定义
    if (orderType === '32') {
      return commit('SET_INV_FIELDS', { orderType, routerType, res: INVENTORY_MANAGEMENT_32_FIELDS })
    } else {
      return commit('SET_INV_FIELDS', { orderType, routerType, res: INVENTORY_MANAGEMENT_FIELDS })
    }
    // return getInvFields(orderType)
    //   .then(res => {
    //     if (res) {
    //       commit('SET_INV_FIELDS', { orderType, routerType, res })
    //     }
    //   })
  },
  // routerType -> list
  getListFields ({ commit }, routerType) {
    if (!routerType) return new Error('getListFields routerType needed!')
    return getInvFields(routerType)
      .then(res => {
        if (res) {
          if (routerType === 'create') {
            res = res.filter(item => item.isCreate)
          }
          if (routerType === 'detail') {
            res = res.filter(item => item.isDetail)
          }
          commit('SET_LIST_FIELDS', { routerType, res })
        }
      })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
