module.exports = {
  root: true,
  env: {
    commonjs: true,
    es6: true,
    node: true
  },
  extends: [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  parserOptions: {
    parser: 'babel-eslint'
  },
  rules: {
    "quotes": [1, "single"],
    "space-before-function-paren": 0,
    'semi': 0,
    "comma-dangle": ["error", "never"],
    "template-curly-spacing" : "off",
    "indent": "off"
  }
};