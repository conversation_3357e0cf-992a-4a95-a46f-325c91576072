{"name": "vue-admin", "version": "1.0.0", "description": "basic vue admin", "main": "main.js", "scripts": {"dev": "cross-env NODE_ENV=local webpack-dev-server", "build": "webpack", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "*********************:fe-templates/vue-admin.git"}, "author": "zhou.zhou", "license": "ISC", "dependencies": {"axios": "^0.19.0", "element-ui": "^2.12.0", "vue": "^2.6.10", "vue-router": "^3.1.3", "vuex": "^3.1.1"}, "devDependencies": {"@tool/webpack-basic-ali": "^1.2.31", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.0.3", "cross-env": "^6.0.0", "eslint": "^6.5.1", "eslint-plugin-vue": "^5.2.3", "webpack": "^4.0.0", "webpack-cli": "^3.3.7", "webpack-dev-server": "^3.8.0"}}