apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${podName}
  namespace: ${nameSpace}
  labels:
    app: ${innerDomain}
    version: ${podName}
spec:
  replicas: ${replicas}
  minReadySeconds: 0
  progressDeadlineSeconds: 600
  selector:
    matchLabels:
      app: ${innerDomain}
  template:
    metadata:
      labels:
        app: ${innerDomain}
        version: ${podName}
    spec:
      containers:
      - name: ${podName}
        image: ${IMAGE}
        imagePullPolicy: Always
        lifecycle:
           preStop:
               exec:
                command: ["sh","stop.sh"]
        env:
        - name: ENV
          value: ${env}
        - name: spring.profiles.active
          value: ${envLower}
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        resources:
          requests:
            cpu: 10m
            memory: 128Mi
          limits:
            cpu: ${cpu}
            memory: ${memory}
        readinessProbe:
          httpGet:
            path: ${path}
            port: http
          initialDelaySeconds: ${delaySeconds}
          periodSeconds: ${periodSeconds}
          timeoutSeconds: ${timeoutSeconds}
          successThreshold: 1
          failureThreshold: ${failureThreshold}
        livenessProbe:
          httpGet:
            path: ${path}
            port: http
          initialDelaySeconds: ${delaySeconds}
          periodSeconds: ${periodSeconds}
          timeoutSeconds: ${timeoutSeconds}
          successThreshold: 1
          failureThreshold: ${failureThreshold}
        volumeMounts:
        - mountPath: /zkh/log
          name: log-volume
        - mountPath: /zkh/oplogs
          name: oplog-volume
        securityContext:
          privileged: true
      volumes:
      - name: log-volume
        emptyDir: {}
      - name: oplog-volume
        emptyDir: {}
      imagePullSecrets:
      - name: zkh-harbor