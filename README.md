# 一站式

### Migration

基于 @tool/z-cli 项目迁移, 模板 为 boss-admin

### 安装依赖说明
1.node >= 18.x
2.python使用3.9.x，推荐使用pyenv管理python版本
3.连公司内网，保证能访问xxx.zkh360.com
4.npm install
5.node-gyp、xprofiler可能安装失败，尝试全局安装
6.npm run serve


### 迁移准备

- 菜单 接口获取
- 工具 request 统一
- 检查代理库 对应的 api
- 用户逻辑相关功能去掉


### 前端页面迁移步骤

目标分支：feature/refactor
迁移方式：手动粘贴，不要 merge 代码到该分支

1. 将原目录 views/ 中的页面组件 迁移至 boss/pages/
2. 在 boss/router 中重新注册路由
3. 检查页面组件的依赖是否引入，工具方法，依赖组件，storage 等
4. 检查 api 接口代理是否正确，server/src/router.ts 中 proxy 部分
5. 访问路由，检查页面功能是否OK

注：boss -> static/source/boss/



### boss鉴权流程

![image](./auth.png)
