{"name": "boss-admin", "version": "2.0.143", "description": "aka boss-admin system", "scripts": {"start": "PORT=3000 node server/dist/app.js", "dev:server": "ENV=local ts-node-dev server/src/app.ts", "serve": "NODE_OPTIONS=--openssl-legacy-provider z serve -w boss", "build": "z build boss && tsc", "tsc": "tsc", "dev": "nodemon", "webpack": "z build boss", "release": "standard-version", "test": "jest -o", "test:coverage": "jest --coverage", "lint:fix": "eslint --fix --ext .js,.vue static/source/boss"}, "author": "zkh-fe", "license": "ISC", "dependencies": {"@boss-ui/smart-price-compare-float": "^0.0.10", "@boss-ui/ui": "^0.1.36", "@boss/dms": "^0.5.0", "@boss/email": "0.0.14", "@boss/excel": "^1.1.3", "@boss/id": "^1.0.0", "@boss/logger": "^1.0.5", "@boss/mse": "^0.1.0", "@boss/pdf": "^1.8.22", "@boss/redis": "^1.2.2", "@boss/translate": "^0.0.15", "@boss/web-components": "^0.2.4", "@boss/wecom": "^1.0.2", "@kun-ui/remote-customer": "0.0.16", "@kun-ui/remote-select": "0.0.5", "@kun-ui/remote-sku": "^0.0.9", "@kun-ui/remote-supplier": "0.0.12", "@poseidon/token": "^3.0.9", "@server/core-common": "^1.1.22", "@server/elk": "^0.2.1", "@server/env": "^0.0.4", "@server/proxy": "^1.1.1", "@server/token-service": "^1.0.0", "@types/moment": "^2.13.0", "ali-oss": "^6.17.1", "bignumber.js": "^9.1.2", "blob-polyfill": "^4.0.20200601", "file-saver": "^2.0.5", "form-data": "^4.0.1", "jschardet": "^3.0.0", "koa-body": "^4.1.1", "koa-bodyparser": "^4.3.0", "koa-cors": "0.0.16", "koa-proxies": "^0.12.1", "koa2-connect-history-api-fallback": "^0.1.2", "lightgallery.js": "^1.4.0", "lodash": "^4.17.15", "marked": "^4.3.0", "node-fetch": "^2.6.0", "nzh": "^1.0.4", "pdfmake": "^0.1.68", "qs": "^6.9.4", "sa-sdk-node": "^1.2.6", "sass": "^1.54.8", "shortid": "^2.2.15", "sortablejs": "^1.13.0", "sqlstring": "^2.3.1", "vuedraggable": "^2.23.2", "vxe-table": "^3.3.4", "xe-utils": "^3.2.1", "xlsx": "^0.16.9", "xprofiler": "^2.6.0", "xtransit": "^2.3.1"}, "remoteStaticCategory": "boss", "remoteStaticHost": "//files.zkh360.com/business/", "devDependencies": {"@babel/preset-env": "^7.12.11", "@client/boss": "^1.0.26", "@commitlint/cli": "^8.1.0", "@commitlint/config-conventional": "^8.1.0", "@tool/webpack-basic-common": "^1.2.31", "@tool/z-cli": "^0.2.14", "@types/koa": "^2.0.52", "@types/lodash": "^4.14.159", "@types/node": "^12.7.3", "@typescript-eslint/eslint-plugin": "^2.27.0", "@vue/eslint-config-standard": "^4.0.0", "@vue/runtime-dom": "^3.3.4", "@vue/test-utils": "^1.1.2", "axios": "^0.19.0", "babel-core": "^7.0.0-bridge.0", "babel-eslint": "^10.0.3", "babel-jest": "^26.6.3", "clipboard": "^2.0.6", "cross-env": "^6.0.0", "dotenv": "^8.2.0", "downloadjs": "^1.4.7", "echarts": "^4.4.0", "element-ui": "2.15.8", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.2.3", "flush-promises": "^1.0.2", "good-storage": "^1.1.1", "husky": "^3.0.5", "jest": "^26.6.3", "js-cookie": "^2.2.1", "moment": "^2.24.0", "nodemon": "^1.19.1", "standard-version": "^7.0.0", "ts-node": "^8.3.0", "ts-node-dev": "^1.1.6", "typescript": "^4.2.0", "vue": "^2.6.10", "vue-jest": "^3.0.7", "vue-router": "^3.1.3", "vuex": "^3.1.1", "webpack": "^4.0.0", "webpack-cli": "^3.3.7", "webpack-dev-server": "^3.8.0"}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"subject-case": [0], "scope-case": [0]}}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}