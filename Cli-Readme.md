# Boss-admin
> 企业统一后台管理系统

### 使用

#### 脚手架工具
- 安装
```
$ npm install -g @tool/z-cli --registry=http://npm.zkh360.com
```

- 初始化项目
```
$ z create ${ProjectName} -t boss-admin
```


#### 项目开发
- 安装依赖
```
$ npm install --registry=http://npm.zkh360.com
```

- 启动开发
```
$ z serve -w boss   // 热更新前端静态项目 static/source/boss
$ z serve -s        // 纯Node端开发模式
```
<!-- 注: 全局安装 @tool/z-cli 可使用 z 工作流 -->

- 本地配置
```
// 在 hosts 配置 以 zkh360.com 为根域的域名，并以此访问
127.0.0.1       local.zkh360.com
```

默认项目示例：
![image](http://static.zkh360.com/file/7b80830c3c734b2dbe9ff754a51edb0b.png)


#### 配置
1.登录。统一使用安全中心作为用户体系，项目中已集成 Node 中间件 @server/token-service 接入, 开发时需要配置各自业务的账号信息，无账号可向安全中心申请

```typescript
app.use(tokenService.middleware.connect({
  env: process.env.ENV || 'uat',
  clientId: config.clientId,
  clientSecret: config.clientSecret
}))
```

#### 安全中心账号配置
工程示例中，所含账号的 clientId: koa 仅在 UAT 环境生效，为测试用，**务必使用业务自己的账号，且保管好相关信息**
- 向安全中心申请一个业务公用的账号
- 在 security.zkh360.com 登录后，创建应用。（信息可保持各环境一致，fat/uat/pro）
- 生成 clientId, clientSecret 后，在中间件中配置


2.路由配置等Node开发，参考 koa.js 文档
``` typescript
import * as tokenService from '@server/service-token'
import * as proxy from '@server/proxy'
const { auth, authorize } = tokenService.middleware
/**
 * Router Pattern
 * router<method>(<path>, <controller>)
 * eg: 
 *  router.get('/health_check', async ctx => ctx.body = 'ok')
 */
export = (router, app) => {

  // security service auth, authorize
  router.get('/', auth, authorize, app.controllers.home.index)

  // Do not delete: health check
  router.get('/health_check', async ctx => ctx.body = 'ok')

  // Example: proxy api
  router.all('/api/*', proxy({
    host: 'https://api.github.com',
    rewrite: {
      '^/api': '',
    },
    extend: (option, ctx) => {
      // option: request option
      option.headers.Authorization = `bearer ${ctx.state.token}`
    }
  }))
}
```

3.前端 集成 @client/boss, 提供基础模板配置等，侧边菜单和顶部工具条有框架提供，让开发专注于业务开发。

![image](http://static.zkh360.com/file/def4118ed3654301873bfe0ad499bb00.png)


### 依赖
- @client/boss: zkh backend system layout
- @server/token-service: 安全中心Node中间件
- element-ui [doc link](https://element.eleme.cn/#/zh-CN/component/installation)


### 规范约束
- framework: vue
- 编码风格 standard
- Git 提交 angular commit guide